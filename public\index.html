<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title><%= htmlWebpackPlugin.options.title %></title>
  </head>
  <body>
    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="position: absolute; width: 0; height: 0"><%= svgIcon %></svg>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
  <script>
    window.onload = function () {
        function IEVersion() {
          var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
          var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
          var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
          var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
          if (isIE) {
            var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
            reIE.test(userAgent);
            var fIEVersion = parseFloat(RegExp["$1"]);
            if (fIEVersion == 7) {
              return 7;
            } else if (fIEVersion == 8) {
              return 8;
            } else if (fIEVersion == 9) {
              return 9;
            } else if (fIEVersion == 10) {
              return 10;
            } else {
              return 6;//IE版本<=7
            }
          } else if (isEdge) {
            return 'edge';//edge
          } else if (isIE11) {
            return 11; //IE11
          } else {
            return -1;//不是ie浏览器
          }
        }
        var a = IEVersion();

        if (a != -1) {
          alert("系统检测到您的浏览器版本过低，可能造成页面无法正常显示，请尝试使用其他浏览器打开！")
        }
      }
  </script>
</html>
