<template>
  <el-config-provider :locale="lang" :size="config.layout.size">
    <router-view></router-view>
  </el-config-provider>
</template>
<script setup lang="ts">
import { onMounted, nextTick, watch, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";
import { useConfig } from "@/stores/config";
import { useSiteConfig } from "@/stores/siteConfig";
import { useDark, useTitle, useToggle } from "@vueuse/core";
import { editDefaultLang } from "@/lang/index";
import Color from "color";

const config = useConfig();

// 初始化 element 的语言包
const { getLocaleMessage, t } = useI18n();
// const lang = getLocaleMessage(config.lang.defaultLang) as any;
const lang = computed(() => getLocaleMessage(config.lang.defaultLang) as any);

// 监听路由变化时更新浏览器标题
const route = useRoute();
const router = useRouter();
const siteConfig = useSiteConfig();
const title = useTitle();
watch(
  () => route.meta,
  (meta = {}, oldMeta = {}) => {
    nextTick(async () => {
      // // console.log();
      if (route.path === "/event/overview") {
        config.layout.menuCollapse = true;
      } else {
        if (meta.title !== oldMeta.title) config.layout.menuCollapse = false;
      }
      await router.isReady();
      if (typeof (meta || {}).title != "string") return;
      const RegGroup: Record<string, string> = (/^\{(?<name>.*)\}$/g.exec(((meta || {}).title as string) || "") || {}).groups || {};
      title.value = `${RegGroup.name ? t(RegGroup.name) : (meta || {}).title}${siteConfig.openName ? " - " + siteConfig.openName : ""}`;
    });
  },
  { immediate: true }
);

watch(
  (): [string, boolean] => [siteConfig.primary, config.layout.isDark],
  async ([$primary, $isDark]) => {
    await nextTick();
    const primary = new Color($primary);
    const mixin = new Color($isDark ? "#000000" : "#FFFFFF");
    const dark = new Color("#000000");
    document.documentElement.style.setProperty("--el-color-primary", primary.hex(), "important");
    document.documentElement.style.setProperty("--el-color-primary-light-2", primary.mix(mixin, 0.2).hex(), "important");
    document.documentElement.style.setProperty("--el-color-primary-dark-2", primary.mix(dark, 0.2).hex(), "important");
    document.documentElement.style.setProperty("--el-color-primary-light-3", primary.mix(mixin, 0.3).hex(), "important");
    document.documentElement.style.setProperty("--el-color-primary-light-4", primary.mix(mixin, 0.4).hex(), "important");
    document.documentElement.style.setProperty("--el-color-primary-light-5", primary.mix(mixin, 0.5).hex(), "important");
    document.documentElement.style.setProperty("--el-color-primary-light-6", primary.mix(mixin, 0.6).hex(), "important");
    document.documentElement.style.setProperty("--el-color-primary-light-7", primary.mix(mixin, 0.7).hex(), "important");
    document.documentElement.style.setProperty("--el-color-primary-light-8", primary.mix(mixin, 0.8).hex(), "important");
    document.documentElement.style.setProperty("--el-color-primary-light-9", primary.mix(mixin, 0.9).hex(), "important");
    config.layout.menuActiveBackground = [primary.mix(new Color("#FFFFFF"), 0.9).hex(), primary.mix(new Color("#000000"), 0.9).hex()];
    config.layout.menuActiveColor = [primary.hex(), "#FFFFFF"];
  },
  { immediate: true }
);
/**
 * 主题色修改
 */
// const isDark = useDark({
//   onChanged(dark: boolean) {
//     const htmlEl = document.getElementsByTagName("html")[0];
//     htmlEl.setAttribute("class", dark ? "dark tw-dark" : "");
//     config.setLayout("isDark", dark);
//     config.onSetLayoutColor();
//   },
// });

// const toggleDark = useToggle(isDark);
// watch(
//   () => config.layout.isDark,
//   async () => {
//     await nextTick();
//     if (isDark.value === config.layout.isDark) return;
//     toggleDark(config.layout.isDark);
//   }
// );

onMounted(() => {
  // 设置平台语言，优先平台记录的语言，浏览器语言，默认中文
  editDefaultLang(localStorage.getItem("systemLang") ? (localStorage.getItem("systemLang") as string) : config.lang.fallbackLang.includes(navigator.language.toLocaleLowerCase()) ? navigator.language.toLocaleLowerCase() : "zh-cn");
  // window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change", (event) => toggleDark(event.matches));
});
</script>
