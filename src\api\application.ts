/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */
import { SERVER, Method, type Response, type RequestBase, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";
import { i18n } from "@/lang/index";
import { auth } from "@/utils/common";
import { epPropKey } from "element-plus/es/utils";

/* TODO: 应用 */
export enum appTerminal {
  WEB = "WEB",
  WECHAT_APP = "WECHAT_APP",
}
export const appTerminalOption: { label: string; value: keyof typeof appTerminal }[] = [
  { label: "Web", value: appTerminal.WEB },
  { label: "微信小程序", value: appTerminal.WECHAT_APP },
];
export enum appType {
  DIR = "DIR",
  MENU = "MENU",
  ROUTE = "ROUTE",
  LINK = "LINK",
  MICRO = "MICRO",
  BUTTON = "BUTTON",
  GROUP = "GROUP",
}
export const appTypeOption: { label: string; value: keyof typeof appType }[] = [
  { label: "目录", value: appType.DIR },
  { label: "菜单项", value: appType.MENU },
  { label: "路由", value: appType.ROUTE },
  { label: "链接", value: appType.LINK },
  { label: "微应用", value: appType.MICRO },
  { label: "按钮", value: appType.BUTTON },
  // { label: "组", value: appType.GROUP },
];
export enum appTheme {
  BASE = "BASE",
  CLASSICS = "CLASSICS",
  SIMPLICITY = "SIMPLICITY",
  FOCUS = "FOCUS",
  DESKTOP = "DESKTOP",
}
export const appThemeOption: { label: string; value: keyof typeof appTheme }[] = [
  { label: "默认", value: appTheme.BASE },
  { label: "经典", value: appTheme.CLASSICS },
  { label: "简约", value: appTheme.SIMPLICITY },
  { label: "专注", value: appTheme.FOCUS },
  { label: "桌面", value: appTheme.DESKTOP },
];
/**
 *{
 *  "id": "501362398129553408",
 *  "rootPath": "/event_center",
 *  "terminal": "WEB",
 *  "name": "事件中心",
 *  "note": "事件中心",
 *  "config": "",
 *  "orderNum": 0,
 *  "version": "1",
 *  "createdTime": "1687009308471",
 *  "updatedTime": "1687009308471"
 *}
 */
export interface NavItem {
  id: string;
  rootId: string;
  parentId: string;
  title: string;
  enTitle: string;
  name: string;
  path: string;
  url: string;
  order: number;
  icon: string;
  type: keyof typeof appType;
  theme?: keyof typeof appTheme;
  enabled: boolean;
  permission: string[];
  component: string;
  keepalive: boolean;
  terminal: keyof typeof appTerminal;
  note: string;

  query: import("vue-router").LocationQueryRaw;
  params: import("vue-router").RouteParamsRaw;
  hash: string;
  pattern: RegExp;
  names: string[];

  version: string;
  config: string;
  children: NavItem[];
  createdTime?: string;
  updatedTime?: string;
  permissionGroups: string[][];
}
/* TODO: 应用 & 菜单 */
export function importApp(data: { file: File } & RequestBase) {
  const $data = new FormData();
  $data.set("file", data.file);
  return request<unknown, Response<null>>({
    url: `${SERVER.IAM}/ops/front/apps/import`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: $data,
  });
}
export function exportApp(data: { rootId: string } & RequestBase) {
  return request<unknown, Response<Blob>>({
    url: `${SERVER.IAM}/ops/front/apps/${data.rootId}/export`,
    method: Method.Get,
    responseType: "blob",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  }).then((res) => {
    if (!res.success) throw Object.assign(new Error(res.message), { data: res.data, success: res.success });
    if (res.data instanceof Blob) {
      const fileName = res.contentDisposition?.filename || `MENU_Data-${Date.now()}.json`;
      const file = new File([new Blob([res.data], { type: "application/json" })], fileName);
      const link = document.createElement("a");
      link.href = URL.createObjectURL(file);
      link.style.visibility = "hidden";
      link.download = file.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
    }
  });
}
/* TODO: 应用 */
export interface AppItem {
  id: NavItem["name"] /* 主键 */;
  rootPath: NavItem["path"] /* 跟路径，同终端下全局唯一 */;
  terminal: NavItem["terminal"] /* 终端类型 */;
  name: NavItem["title"] /* 名称 */;
  enName: NavItem["enTitle"] /* 名称 */;
  note: NavItem["note"] /* 备注信息 */;
  permissionIds: NavItem["permission"] /* 权限ID列表 */;
  orderNum: NavItem["order"] /* 排序 */;

  config: NavItem["config"] /* 配置信息 */;

  version: NavItem["version"] /* 乐观锁版本号 */;
  createdTime: NavItem["createdTime"] /* 创建时间 */;
  updatedTime: NavItem["updatedTime"] /* 更新时间 */;
  permissionGroups: NavItem["permissionGroups"];
}
export function getApp(data: Partial<NavItem> & RequestBase) {
  return request<unknown, Response<AppItem[]>>({
    url: `${SERVER.IAM}/front/apps`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  }).then<Response<NavItem[]>>((res) => {
    return {
      success: res.success,
      message: res.message,
      page: res.page,
      size: res.size,
      total: res.total,
      data: Array.from(
        res.data
          .map((menu): NavItem => {
            const config: Required<NavItem> = {
              id: menu.id,
              rootId: menu.id,
              parentId: "",
              path: menu.rootPath,
              terminal: menu.terminal,
              title: menu.name,
              enTitle: menu.enName,
              name: menu.id,
              order: Number(menu.orderNum),
              icon: "local-SystemApps-line",
              type: appType.ROUTE,
              theme: appTheme.BASE,
              url: "",
              component: "",
              keepalive: false,
              enabled: true,
              permission: menu.permissionIds instanceof Array ? menu.permissionIds : [],
              note: menu.note,
              version: menu.version,
              config: menu.config,
              hash: "",
              query: {},
              params: {},
              pattern: /(?:)/,
              names: [],
              children: [],
              createdTime: menu.createdTime as string,
              updatedTime: menu.updatedTime as string,
              permissionGroups: menu.permissionGroups instanceof Array ? menu.permissionGroups : [[], []],
            };
            try {
              const { type: type = config.type, theme: theme = config.theme, icon: icon = config.icon, url: url = config.url, component: component = config.component, keepalive: keepalive = config.keepalive } = JSON.parse(menu.config);
              return Object.assign(config, { type, theme, icon, url, component, keepalive });
            } catch (error) {
              return config;
            }
          })
          .sort((a, b) => Number(a.order) - Number(b.order))
      ),
    };
  });
}
export function addApp(data: Partial<NavItem> & RequestBase) {
  const req: Partial<AppItem> = {
    rootPath: data["path"] /* 跟路径，同终端下全局唯一 */,
    terminal: data["terminal"] /* 终端类型 */,
    name: data["title"] /* 名称 */,
    note: data["note"] /* 备注信息 */,
    permissionIds: data["permission"] /* 权限ID列表 */,
    orderNum: data["order"] /* 排序 */,
    config: JSON.stringify({ type: data.type, theme: data.theme, icon: data.icon, url: data.url, component: data.component, keepalive: data.keepalive }, null, 2),
  };
  return request<unknown, Response<AppItem[]>>({
    url: `${SERVER.IAM}/front/apps`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), req),
  });
}
export function modApp(data: Partial<NavItem> & RequestBase) {
  const req: Partial<AppItem> = {
    rootPath: data["path"] /* 跟路径，同终端下全局唯一 */,
    terminal: data["terminal"] /* 终端类型 */,
    name: data["title"] /* 名称 */,
    note: data["note"] /* 备注信息 */,
    orderNum: data["order"] /* 排序 */,
    config: JSON.stringify({ type: data.type, theme: data.theme, icon: data.icon, url: data.url, component: data.component, keepalive: data.keepalive }, null, 2),
  };
  return request<unknown, Response<AppItem[]>>({
    url: `${SERVER.IAM}/front/apps/${data.name}`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), req),
  });
}
export function delApp(data: Partial<NavItem> & RequestBase) {
  return request<unknown, Response<AppItem[]>>({
    url: `${SERVER.IAM}/front/apps/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function modAppByOrder(data: { orders: { appCode: string; order: number }[] } & RequestBase) {
  return request<unknown, Response<AppItem[]>>({
    url: `${SERVER.IAM}/front/apps/batch_update_order`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: data.orders.reduce((p, v) => Object.assign(p, { [v.appCode]: v.order }), {}),
  });
}
export function getModelAuthItemByPlatform(data: { platform: string } & RequestBase) {
  return request<unknown, Response<AppItem[]>>({
    url: `${SERVER.IAM}/front/platforms/${data.platform}/apps`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["platform"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  }).then<Response<NavItem[]>>((res) => {
    return {
      success: res.success,
      message: res.message,
      page: res.page,
      size: res.size,
      total: res.total,
      data: Array.from(
        res.data
          .map((menu): NavItem => {
            const config: Required<NavItem> = {
              id: menu.id,
              rootId: menu.id,
              parentId: "",
              path: menu.rootPath,
              terminal: menu.terminal,
              title: menu.name,
              enTitle: menu.enName,
              name: menu.id,
              order: Number(menu.orderNum),
              icon: "local-SystemApps-line",
              type: appType.ROUTE,
              theme: appTheme.BASE,
              url: "",
              component: "",
              keepalive: false,
              enabled: true,
              permission: menu.permissionIds instanceof Array ? menu.permissionIds : [],
              note: menu.note,
              version: menu.version,
              config: menu.config,
              hash: "",
              query: {},
              params: {},
              pattern: /(?:)/,
              names: [],
              children: [],
              createdTime: menu.createdTime as string,
              updatedTime: menu.updatedTime as string,
              permissionGroups: menu.permissionGroups instanceof Array ? menu.permissionGroups : [[], []],
            };
            try {
              const { type: type = config.type, theme: theme = config.theme, icon: icon = config.icon, url: url = config.url, component: component = config.component, keepalive: keepalive = config.keepalive } = JSON.parse(menu.config);
              return Object.assign(config, { type, theme, icon, url, component, keepalive });
            } catch (error) {
              return config;
            }
          })
          .sort((a, b) => Number(a.order) - Number(b.order))
      ),
    };
  });
}
export function setModelAuthItemByPlatform(data: { platform: string; apps: string[] } & RequestBase) {
  return request<unknown, Response<AppItem[]>>({
    url: `${SERVER.IAM}/front/app/save_platform_apps`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["platform"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: Array.from(new Set(data.apps)),
  });
}
/* TODO: 菜单 */
export interface MenuItem {
  id: NavItem["name"] /* 主键 */;
  parentId: NavItem["parentId"] /* 父ID */;
  appId: NavItem["rootId"] /* 所属应用ID */;
  name: NavItem["title"] /* 名称 */;
  enName: NavItem["enTitle"];
  permissionIds: NavItem["permission"] /* 权限ID列表 */;
  orderNum: NavItem["order"] /* 排序 */;
  enabled: NavItem["enabled"] /* 是否启用 */;
  note: string;

  config: NavItem["config"] /* 配置信息 */;

  version: NavItem["version"] /* 乐观锁版本号 */;
  createdTime: NavItem["createdTime"] /* 创建时间 */;
  updatedTime: NavItem["updatedTime"] /* 更新时间 */;
  permissionGroups: NavItem["permissionGroups"];
}
export function getMenu(data: Partial<MenuItem> & RequestBase) {
  return request<unknown, Response<MenuItem[]>>({
    url: `${SERVER.IAM}/front/menus`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["appId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  }).then<Response<NavItem[]>>((res) => {
    return {
      success: res.success,
      message: res.message,
      page: res.page,
      size: res.size,
      total: res.total,
      data: Array.from(
        res.data
          .map((menu): NavItem => {
            const config: Required<NavItem> = {
              id: menu.id,
              rootId: menu.appId,
              parentId: menu.parentId,
              path: "",
              terminal: appTerminal.WEB,
              title: menu.name,
              enTitle: menu.enName,
              name: menu.id,
              order: Number(menu.orderNum),
              icon: "local-SystemApps-line",
              type: appType.ROUTE,
              theme: appTheme.BASE,
              url: "",
              component: "",
              keepalive: false,
              enabled: menu.enabled,
              permission: menu.permissionIds instanceof Array ? menu.permissionIds : [],
              note: menu.note,
              version: menu.version,
              config: menu.config,
              hash: "",
              query: {},
              params: {},
              pattern: /(?:)/,
              names: [],
              children: [],
              createdTime: menu.createdTime as string,
              updatedTime: menu.updatedTime as string,
              permissionGroups: menu.permissionGroups instanceof Array ? menu.permissionGroups : [[], []],
            };

            try {
              const { type: type = config.type, path: path = config.path, icon: icon = config.icon, url: url = config.url, component: component = config.component, keepalive: keepalive = config.keepalive } = JSON.parse(menu.config);
              return Object.assign(config, { type, path, icon, url, component, keepalive });
            } catch (error) {
              return config;
            }
          })
          .map((value, _index, full) => {
            if (value.id === value.parentId) return value;
            else {
              return Object.assign(value, {
                children: full
                  .filter(({ parentId }) => parentId === value.id)
                  .map((v) => Object.assign(v, { consume: true }))
                  .sort((a, b) => Number(a.order) - Number(b.order)),
              });
            }
          })
          .filter((v: NavItem & { consume?: boolean }) => {
            const consume = v.consume;
            if (consume) delete v.consume;
            return !consume;
          })
          .sort((a, b) => Number(a.order) - Number(b.order))
      ),
    };
  });
}
export function addMenu(data: Partial<NavItem> & RequestBase) {
  const req: Partial<MenuItem> = {
    appId: data["rootId"],
    parentId: data["parentId"],
    name: data["title"],
    enName: data["enTitle"],
    note: data["note"] /* 备注信息 */,
    permissionIds: data["permission"] /* 权限ID列表 */,
    orderNum: data["order"] /* 排序 */,
    enabled: data["enabled"] /* 是否启用 */,
    config: JSON.stringify({ type: data.type, path: data.path, icon: data.icon, url: data.url, component: data.component, keepalive: data.keepalive }, null, 2),
    // permissionGroups: da,
  };

  return request<unknown, Response<MenuItem[]>>({
    url: `${SERVER.IAM}/front/menus`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["appCode", "permissionGroups"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), req),
  });
}
export function modMenu(data: Partial<NavItem> & RequestBase) {
  const req: Partial<MenuItem> = {
    ...(Object.prototype.hasOwnProperty.call(data, "parentId") ? { parentId: data["parentId"] || "-1" } : {}),
    ...(Object.prototype.hasOwnProperty.call(data, "title") ? { name: data["title"] } : {}),
    ...(Object.prototype.hasOwnProperty.call(data, "enTitle") ? { enName: data["enTitle"] } : {}),
    ...(Object.prototype.hasOwnProperty.call(data, "note") ? { note: data["note"] } : {}),
    ...(Object.prototype.hasOwnProperty.call(data, "permission") ? { permissionIds: data["permission"] } : {}),
    ...(Object.prototype.hasOwnProperty.call(data, "order") ? { orderNum: data["order"] } : {}),
    ...(Object.prototype.hasOwnProperty.call(data, "enabled") ? { enabled: data["enabled"] } : {}),
    ...(["type", "path", "icon", "url", "component", "keepalive"].every((key) => Object.prototype.hasOwnProperty.call(data, key)) ? { config: JSON.stringify({ type: data.type, path: data.path, icon: data.icon, url: data.url, component: data.component, keepalive: data.keepalive }, null, 2) } : {}),
  };

  return request<unknown, Response<MenuItem[]>>({
    url: `${SERVER.IAM}/front/menus/${data.name}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["permissionGroups"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), req),
  });
}
export function delMenu(data: Partial<NavItem> & RequestBase) {
  return request<unknown, Response<MenuItem[]>>({
    url: `${SERVER.IAM}/front/menus/${data.name}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function modMenuByOrder(data: { orders: { menuId: string; order: number }[] } & RequestBase) {
  return request<unknown, Response<MenuItem[]>>({
    url: `${SERVER.IAM}/front/menus/batch_update_order`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: data.orders.reduce((p, v) => Object.assign(p, { [v.menuId]: v.order }), {}),
  });
}
/* TODO: 权限组 */
export interface AuthCatalogItem {
  id: NavItem["name"] /* 主键 */;
  parentId: NavItem["parentId"] /* 父ID */;
  appId: NavItem["rootId"] /* 所属应用ID */;
  /*  */
  orderNum: NavItem["order"] /* 排序 */;
  name: NavItem["title"] /* 名称 */;
  note: NavItem["note"] /* 备注 */;
  /*  */
  /*  */
  /*  */
  type: NavItem["type"];
  config: NavItem["config"] /* 配置信息 */;
  enabled: NavItem["enabled"] /* 是否启用 */;
  version: NavItem["version"] /* 乐观锁版本号 */;
  children: AuthCatalogItem[];
  authorities: AuthorityItem[];
  createdTime: NavItem["createdTime"] /* 创建时间 */;
  updatedTime: NavItem["updatedTime"] /* 更新时间 */;
}
export function getAuthCatalog(data: Partial<AuthCatalogItem> & RequestBase) {
  return request<unknown, Response<AuthCatalogItem[]>>({
    url: `${SERVER.IAM}/front/permission_catalogs`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["appId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  }).then<Response<AuthCatalogItem[]>>((res) => {
    return {
      success: res.success,
      message: res.message,
      page: res.page,
      size: res.size,
      total: res.total,
      data: Array.from(res.data.map((auth): Required<AuthCatalogItem> => ({ id: auth.id /* 主键 */, type: appType.DIR, parentId: auth.parentId /* 父ID */, appId: auth.appId /* 所属应用ID */, orderNum: Number(auth.orderNum) /* 排序 */, name: auth.name /* 名称 */, note: auth.note /* 备注 */, config: auth.config /* 配置信息 */, enabled: Boolean(auth.enabled) /* 是否启用 */, children: [], authorities: [], version: auth.version /* 乐观锁版本号 */, createdTime: auth.createdTime /* 创建时间 */, updatedTime: auth.updatedTime /* 更新时间 */ }))),
    };
  });
}
export function addAuthCatalog(data: Partial<AuthCatalogItem> & RequestBase) {
  return request<unknown, Response<AuthCatalogItem[]>>({
    url: `${SERVER.IAM}/front/permission_catalogs`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["parentId", "name", "note", "orderNum", "enabled", "config"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { appId: data.appId }),
  });
}
export function modAuthCatalog(data: Partial<AuthCatalogItem> & RequestBase) {
  return request<unknown, Response<AuthCatalogItem[]>>({
    url: `${SERVER.IAM}/front/permission_catalogs/${data.id}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["parentId", "name", "note", "orderNum", "enabled", "config"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function delAuthCatalog(data: { id: string | string[] } & RequestBase) {
  return request<unknown, Response<AuthCatalogItem[]>>({
    url: `${SERVER.IAM}/front/permission_catalogs/${data.id instanceof Array ? data.id.join(",") : data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function setAuthCatalogByOrder(data: { order: { id: string; order: number }[] } & RequestBase) {
  return request<unknown, Response<AuthCatalogItem[]>>({
    url: `${SERVER.IAM}/front/permission_catalogs/batch_update_order`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: data.order.reduce((p, v) => Object.assign(p, { [v.id]: v.order }), {}),
  });
}

/* TODO: 权限 */
export interface AuthorityItem {
  id: NavItem["name"] /* 主键 */;
  /*  */
  appId: NavItem["rootId"] /* 所属应用ID */;
  catalogId: NavItem["name"] /* 所属目录ID */;
  orderNum: NavItem["order"] /* 排序 */;
  name: NavItem["title"] /* 名称 */;
  note: NavItem["note"] /* 备注 */;
  // apiPatterns?: string[] /* ApiPattern清单 */;
  // specificApis?: string[] /* 明确的api接口地址清单 */;
  apis: string[];
  authorities: string[] /* 权限标识列表 */;
  /*  */
  type: NavItem["type"];
  enabled: NavItem["enabled"] /* 是否启用 */;
  version: NavItem["version"] /* 乐观锁版本号 */;
  children: AuthorityItem[];
  createdTime: NavItem["createdTime"] /* 创建时间 */;
  updatedTime: NavItem["updatedTime"] /* 更新时间 */;

  itemId: NavItem["id"];
  itemName: NavItem["id"];
  code: NavItem["id"];
  datatypes: string[];
  allInItem: NavItem["enabled"];
  itemSecurity: NavItem["enabled"];
  groupSecurity: NavItem["enabled"];
  childIds: string[];

  groupId: string /** 配置组ID */;
}

/**
 * @description 获取前端权限列表
 * @url http://*************:3000/project/11/interface/api/2833
 */
export function getAppAuth(data: Partial<AuthorityItem> & RequestBase) {
  return request<unknown, Response<(AuthorityItem & Record<"apiPatterns" | "specificApis", string[]>)[]>>({
    url: `${SERVER.IAM}/front/permissions`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["appId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  }).then<Response<AuthorityItem[]>>((res) => {
    return {
      success: res.success,
      message: res.message,
      page: res.page,
      size: res.size,
      total: res.total,
      data: Array.from(
        res.data.map(
          (auth): Required<AuthorityItem> => ({
            id: auth.id /* 主键 */,
            appId: auth.appId /* 所属应用ID */,
            catalogId: auth.catalogId /* 所属目录ID */,
            groupId: auth.groupId /* 配置组ID */,
            orderNum: auth.orderNum /* 排序 */,
            name: auth.name /* 名称 */,
            note: auth.note /* 备注 */,
            apis: [...(auth.apiPatterns instanceof Array ? auth.apiPatterns : []), ...(auth.specificApis instanceof Array ? auth.specificApis : [])],
            authorities: auth.authorities instanceof Array ? auth.authorities : [] /* 权限标识列表 */,
            enabled: auth.enabled /* 是否启用 */,
            version: auth.version /* 乐观锁版本号 */,
            type: auth.type === appType.GROUP ? appType.GROUP : appType.BUTTON,
            children: [],
            createdTime: auth.createdTime /* 创建时间 */,
            updatedTime: auth.updatedTime /* 更新时间 */,
            itemId: auth.itemId /* 配置id */,
            itemName: auth.itemName /* 配置名称 */,
            code: auth.code,
            datatypes: auth.datatypes instanceof Array ? auth.datatypes : [],
            allInItem: auth.allInItem,
            itemSecurity: auth.itemSecurity,
            groupSecurity: auth.groupSecurity,
            childIds: auth.childIds instanceof Array ? auth.childIds : [],
          })
        )
      ),
    };
  });
}
export function addAppAuth(data: Partial<AuthorityItem> & RequestBase) {
  return request<unknown, Response<AuthorityItem[]>>({
    url: `${SERVER.IAM}/front/permissions`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["catalogId", "appId", "name", "note", "apis", "authorities", "orderNum", "enabled", "itemId", "datatypes", "allInItem", "itemSecurity", "groupSecurity", "childIds", "type"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { appId: data.appId }),
  });
}
export function modAppAuth(data: Partial<NavItem> & RequestBase) {
  return request<unknown, Response<AuthorityItem[]>>({
    url: `${SERVER.IAM}/front/permissions/${data.id}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["catalogId", "name", "note", "apis", "authorities", "orderNum", "enabled", "itemId", "datatypes", "allInItem", "itemSecurity", "groupSecurity", "childIds", "type"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function delAppAuth(data: { id: string | string[] } & RequestBase) {
  return request<unknown, Response<AuthorityItem[]>>({
    url: `${SERVER.IAM}/front/permissions/${data.id instanceof Array ? data.id.join(",") : data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function setAuthByOrder(data: { order: { id: string; order: number }[] } & RequestBase) {
  return request<unknown, Response<AuthCatalogItem[]>>({
    url: `${SERVER.IAM}/front/permissions/batch_update_order`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: data.order.reduce((p, v) => Object.assign(p, { [v.id]: v.order }), {}),
  });
}
interface PermissionGroupsItem {
  id: string;
  appId: string;
  name: string;
  orderNum: number;
  enabled: boolean;
}
//新增权限配置组
export function createPermissionGroups(data: RequestBase) {
  return request<unknown, Response<PermissionGroupsItem[]>>({
    url: `${SERVER.IAM}/front/permission_groups`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: data,
  });
}
/**
 * @description 获取应用下配置分组列表
 * @url http://*************:3000/project/11/interface/api/15506
 */
export function getPermissionGroups(data: { appId: string /* 应用ID */; controller?: AbortController }) {
  return request<unknown, Response<PermissionGroupsItem[]>>({
    url: `${SERVER.IAM}/front/permission_groups`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: { appId: data.appId },
    data: {},
  });
}
interface PermissionConfigItem {
  id: string;
  appId: string;
  groupId: string;
  name: string;
  orderNum: number;
  enabled: boolean;
}
//新增权限配置项
export function createPermissionItem(data: RequestBase) {
  return request<unknown, Response<PermissionConfigItem[]>>({
    url: `${SERVER.IAM}/front/permission_items`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: data,
  });
}
//编辑权限配置项
export function editPermissionItem(data: RequestBase) {
  return request<unknown, Response<PermissionConfigItem[]>>({
    url: `${SERVER.IAM}/front/permission_items/${data.id}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: ["name", "enabled"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

//获取权限配置项
export function getPermissionItems(data: RequestBase) {
  return request<unknown, Response<PermissionConfigItem[]>>({
    url: `${SERVER.IAM}/front/permission_items`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: { appId: data.appId },
    data: {},
  });
}
//更新权限配置项
export function setPermissionItems(data: RequestBase) {
  return request<unknown, Response<PermissionConfigItem[]>>({
    url: `${SERVER.IAM}/front/permission_items/${data.id}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: ["name", "orderNum", "enabled"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//新增权限配置模板
export function createPermissionModel(data: RequestBase) {
  return request<unknown, Response<AuthCatalogItem[]>>({
    url: `${SERVER.IAM}/front/permission_templates`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: data,
  });
}
// //获取权限配置模板
// export function getPermissionModel(data: RequestBase) {
//   return request<unknown, Response<AuthCatalogItem[]>>({
//     url: `${SERVER.IAM}/front/permission_templates`,
//     method: Method.Get,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     params: { appId: data.appId },
//     data: {},
//   });
// }

// //编辑权限配置模板
// export function editorPermissionModel(data: RequestBase) {
//   return request<unknown, Response<AuthCatalogItem[]>>({
//     url: `${SERVER.IAM}/front/permission_templates/${data.id}`,
//     method: Method.Patch,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     params: {},
//     data: data,
//   });
// }

/**
 * @description 配置模板
 */
export interface TemplatePermissionItem {
  id: string;
  appId: string;
  /** 权限配置组ID */
  groupId: string;
  /** 模板名称 */
  name: string;
  /** 排序 */
  orderNum: number;
  /** 权限ID列表 */
  permissionIds: string[];
}
/**
 * @description 获取配置模板列表
 * @url http://*************:3000/project/11/interface/api/15611
 */
export function getTemplatePermission(req: { appId: string /* 应用ID */; groupId: string /* 分组ID */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_templates`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ appId: req.appId, groupId: req.groupId }, $req.params);
        return $req;
      })
      .then(($req) => request<never, Response<TemplatePermissionItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 新建权限配置模板
 * @url http://*************:3000/project/11/interface/api/15583
 */
export function addTemplatePermission(req: { groupId: /** 权限配置组ID */ string; name: /** 模板名称 */ string; orderNum?: /** 排序 */ number; permissionIds: /** 权限ID列表 */ string[] }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_templates`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        $req.data = { groupId: req.groupId, name: req.name, orderNum: req.orderNum === void 0 ? null : req.orderNum, permissionIds: req.permissionIds instanceof Array ? req.permissionIds : [] };
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}
/**
 * @description 选择性更新权限配置模板
 * @url http://*************:3000/project/11/interface/api/15590
 */
export function modTemplatePermission(req: { name?: /** 模板名称 */ string; orderNum?: /** 排序 */ number; permissionIds?: /** 权限ID列表 */ string[] } & { id: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_templates/${req.id /* 模板ID */}`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = { name: req.name === void 0 ? null : req.name, orderNum: req.orderNum === void 0 ? null : req.orderNum, permissionIds: req.permissionIds === void 0 ? null : req.permissionIds };
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}
export function delTemplatePermission(req: { id: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_templates/${req.id /* 模板ID */}`, method: Method.Delete, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}
/**
 * @description 批量更新权限配置模板排序
 * @url http://*************:3000/project/11/interface/api/15597
 */
export function setTemplatePermissionBySlot(req: { slot: TemplatePermissionItem[] }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_templates/batch_update_order`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = req.slot.reduce((p, c) => Object.assign(p, { [c.id]: c.orderNum }), {});
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}
