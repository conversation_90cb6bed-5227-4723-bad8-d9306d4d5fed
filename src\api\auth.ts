/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */
import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";
import { useSiteConfig } from "@/stores/siteConfig";

export function getUser(data: Partial<Record<"keyword" | "schemas" | "scope", string>> & RequestBase) {
  return request<unknown, Response<Record<string, unknown>[]>>({
    url: useSiteConfig().multiTenant ? `${SERVER.IAM}/users/current_org/info` : `${SERVER.IAM}/users/current_platform/desensitized`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: ["keyword", "schemas", "scope"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { pageNumber: data.paging?.pageNumber, pageSize: data.paging?.pageSize, permissionId: ["515414964874248192"].join(",") }),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

export function /* { label: "设备权限", name: activeType.resource } */ getResourceList(req: RequestBase) {
  const params = new URLSearchParams({ pageNumber: String((req.paging || {}).pageNumber || 1), pageSize: String((req.paging || {}).pageSize || 30) });
  bindSearchParams(
    {
      modelIdent: req.modelIdent /* 模型标识 */,
      regionId: req.regionId /* 区域ID */,
      locationId: req.locationId /* 场所ID */,
      groupId: req.groupId /* 设备组ID */,
      vendorId: req.vendorId /* 供应商ID */,
      supportNoteId: req.supportNoteId /* 行动策略ID */,
      resourceTypeId: req.resourceTypeId /* 资源类型ID */,
      alertClassificationId: req.alertClassificationId /* 告警分类ID */,
      active: req.active /* 是否激活 */,
    },
    params
  );
  const data = new URLSearchParams();
  return request<never, Response<({ id: string; name: string } & Record<string, unknown>)[]>>({ url: `${SERVER.CMDB}/resources/tenant/current/desensitized`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, params, data });
}
export function /* { label: "区域权限", name: activeType.region } */ getRegionList(req: RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({ supportNoteId: req.supportNoteId /* 行动策略ID */ }, params);
  const data = new URLSearchParams({});
  return request<never, Response<({ id: string; name: string } & Record<string, unknown>)[]>>({ url: `${SERVER.CMDB}/regions/tenant/current`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, params, data });
}
export function /* { label: "场所权限", name: activeType.location } */ getLocationList(req: RequestBase) {
  const params = new URLSearchParams({ pageNumber: String((req.paging || {}).pageNumber || 1), pageSize: String((req.paging || {}).pageSize || 30) });
  bindSearchParams(
    {
      sort: req.sort,
      keyword: req.keyword /* 模糊查询关键字，名称，描述 */,
      regionId: req.regionId /* 区域ID */,
      externalId: req.externalId /* 外部ID */,
      supportNoteId: req.supportNoteId /* 行动策略ID */,
    },
    params
  );
  const data = new URLSearchParams({});
  return request<never, Response<({ id: string; name: string } & Record<string, unknown>)[]>>({ url: `${SERVER.CMDB}/locations/tenant/current`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, params, data });
}
export function /* { label: "联系人权限", name: activeType.contact } */ getContactList(req: RequestBase) {
  const params = new URLSearchParams({ pageNumber: String((req.paging || {}).pageNumber || 1), pageSize: String((req.paging || {}).pageSize || 30) });
  bindSearchParams({ sort: req.sort }, params);
  const data = new URLSearchParams({});
  return request<never, Response<({ id: string; name: string } & Record<string, unknown>)[]>>({ url: `${SERVER.CMDB}/contacts/tenant/current/desensitized`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, params, data });
}
export function /* { label: "供应商权限", name: activeType.vendor } */ getVendorList(req: RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({ vendorId: req.vendorId /* 供应商ID */ }, params);
  const data = new URLSearchParams({});
  return request<never, Response<({ id: string; name: string } & Record<string, unknown>)[]>>({ url: `${SERVER.CMDB}/vendors`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, params, data });
}
export function /* { label: "设备分组权限", name: activeType.resource_group } */ getResourceGroupList(req: RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({ alertClassificationId: req.alertClassificationId /* 告警分类ID */ }, params);
  const data = new URLSearchParams({});
  return request<never, Response<({ id: string; name: string } & Record<string, unknown>)[]>>({ url: `${SERVER.CMDB}/groups/tenant/current`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, params, data });
}
export function /* { label: "设备类型权限", name: activeType.resource_type } */ getResourceTypeList(req: RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({ alertClassificationId: req.alertClassificationId /* 告警分类ID */ }, params);
  const data = new URLSearchParams({});
  return request<never, Response<({ id: string; name: string } & Record<string, unknown>)[]>>({ url: `${SERVER.CMDB}/resource_types`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, params, data });
}
export function /* { label: "完结代码配置", name: activeType.completion } */ getCompletionList(req: { codeName?: string; codeType?: string } & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { codeName: req.codeName /* 完结代码名称 */, codeType: req.codeType /* 完结代码类型: 事件：EVENT | 服务请求：SERVICE | 问题：QUESTION | 变更：CHANGE | 发布：PUBLISH */ };
  return request<never, Response<({ id: string; name: string } & Record<string, unknown>)[]>>({ url: `${SERVER.EVENT_CENTER}/codeConfig/list`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
export function /* { label: "告警分类", name: activeType.alert_classification } */ getAlertClassificationList(req: RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = new URLSearchParams({});
  return request<never, Response<({ id: string; name: string } & Record<string, unknown>)[]>>({ url: `${SERVER.CMDB}/alert_classifications`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
export function /* { label: "行动策略", name: activeType.support_note } */ getSupportNoteList(req: Partial<Record<"containsGlobal", string>> & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({ containsGlobal: req.containsGlobal /* 是否包含全局行动策略 */ }, params);
  const data = new URLSearchParams({});
  return request<never, Response<({ id: string; name: string } & Record<string, unknown>)[]>>({ url: `${SERVER.CMDB}/support_notes/tenant/current`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
export function /* { label: "行动策略", name: activeType.support_note } */ getMainSupportNoteData(req: Partial<Record<"containsGlobal", string>> & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = new URLSearchParams({});
  return request<never, Response<{ id: string; name: string } & Record<string, unknown>>>({ url: `${SERVER.CMDB}/support_notes/findGlobalList`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
