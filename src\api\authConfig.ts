/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */
import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";
import { useSiteConfig } from "@/stores/siteConfig";
import { appType, appTheme } from "@/api/application";
import type { Zone } from "@/utils/zone";

import { 安全管理中心_权限管理_安全 } from "@/views/pages/permission";

//添加用户组到权限配置组
export function relationUserGroupsAuth(data: RequestBase) {
  return request<never, Response<{ assignId: string; containerId: string; userGroupId: string; userGroupName?: string; tenantId?: string; tenantName?: string; tenantAbbreviation?: string; extend: boolean }>>({
    url: `${SERVER.IAM}/security_container/assign_user_group`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//移除用户组
export function deleteUserGroupsAuth(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/security_container/unassign_user_group/${data.assignId}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//获取配置组下的模板权限
export function getUserGroupsAuth(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/front/permission_templates`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//通过模板id给用户组生成权限
export function setUserGroupAuth(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/security_container/assign_permission_by_template`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
interface PermissionGroupsToUserGroupsItem {
  /** 用户组分配ID */
  assignId: string;
  /** 安全容器ID */
  containerId: string;
  /* 权限配置组ID */
  permissionGroupId: string;
  /** 用户组ID */
  userGroupId: string;
  /** 用户组名称 */
  userGroupName?: string;
  /** 租户ID */
  tenantId?: string;
  /** 租户名称 */
  tenantName?: string;
  /** 租户缩写 */
  tenantAbbreviation?: string;
  /** 是不是继承过来的 */
  extend: boolean;
}

/**
 * @description 获取权限配置组下的用户组分配列表
 * @url http://*************:3000/project/11/interface/api/16087
 */
export function getAuthGroupRelationUserGroup(data: { containerId: string /* 安全容器ID */; permissionGroupId: string /* 权限配置组ID */; controller?: AbortController }) {
  return request<never, Response<PermissionGroupsToUserGroupsItem[]>>({
    url: `${SERVER.IAM}/security_container/assigned_user_groups`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { containerId: data.containerId, permissionGroupId: data.permissionGroupId, permissionId: ["512890964822458368"].join(",") },
    data: {},
  });
}
//获取权限配置组下的用户组的所拥有权限
export function getAssigned_permissions(data: RequestBase) {
  return request<never, Response<{ itemId: string; itemName: string; assign: boolean; permissions: { id: string; name: string; assign: boolean; extend: boolean; mfa: boolean }[] }[]>>({
    url: `${SERVER.IAM}/security_container/assigned_permissions/${data.groupAssignId}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      extendView: data.extendView,
    },
    data: {},
  });
}
//自定义分配权限
export function customSetPermissions(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/security_container/assign_permission`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//分配所有权限
export function setAllPermissions(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/security_container/assign_group_all_permission/${data.groupAssignId}`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      extend: false,
    },
    data: {},
  });
}

//编辑权限
export function editorPermissions(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/security_container/cover_item_permission/${data.groupAssignId}`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { permissionItemId: data.itemId },
    data: data.list,
  });
}

//清空所有权限
export function cancelPermission(data: { groupAssignId: string; permissionItemId: string[] } & RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/security_container/empty_group_permission/${data.groupAssignId}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params: {},
    data: {},
  });
}
//判断安全容器是否可以删除
export function deletable(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/security_containers/${data.id}/deletable`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//移动安全容器
export function moveContainer(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/security_containers/${data.id}/move`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      parentId: data.parentId,
    },
    data: {},
  });
}

//清空某一项权限
export function cancelItemPermission(data: { groupAssignId: string; permissionItemId: string } & RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/security_container/empty_item_permission/${data.groupAssignId}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    params: {
      permissionItemId: data.permissionItemId,
      deleteAssigned: data.deleteAssigned,
    },
    data: {},
  });
}
//获取所有的权限列表
export function getAllPermissions(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/front/permissions`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,

    params: {
      appId: data.appId,
    },
    data: {},
  });
}

//获取安全容器下所拥有的资源类型列表
export function getContainersResource(data: { id: string /* 安全容器ID */; controller?: AbortController }) {
  return request<never, Response<string[]>>({
    url: `${SERVER.IAM}/security_containers/${data.id}/exists_resource_types`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,

    params: {},
    data: {},
  });
}

//获取安全容器树列表
export function getContainersTree(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/current_org/security_containers/current_user/available_tree`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {
      containerId: data.containerId,
      permissionId: [安全管理中心_权限管理_安全].join(","),
    },
    data: {},
  });
}
