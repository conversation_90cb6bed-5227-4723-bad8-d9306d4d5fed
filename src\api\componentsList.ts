import {
  SERVER,
  Method,
  bindSearchParams,
  type Response,
  type RequestBase,
} from "@/api/service/common";
import request from "@/api/service/index";
export enum deviceImportance {
  High = "High",
  Medium = "Medium",
  Low = "Low",
  None = "None",
  Unknown = "Unknown",
}
export interface ModuleItem {
  [key: string]: unknown;
  id: string;
  name: string;
}
export interface RegionsTenant {
  id: string /* 主键 */;
  tenantId: string /* 租户ID */;
  parentId: string /* 父区域Id */;
  supportNoteIds: /* 行动策略列表 */ string[];
  label?: string;
  name: string /* 区域名称 */;
  description?: string /* 描述信息 */;
  externalId?: string /* 外部ID */;
  latitude?: string /* 纬度 */;
  longitude?: string /* 经度 */;
  version: string /* 版本号 */;
  createdTime: string /* 创建时间 */;
  updatedTime: string /* 最近更新时间 */;
  createdBy?: string /* 创建人 */;
  updatedBy?: string /* 最后更新人 */;
}
export interface Locations {
  id: string;
  tenantId: string;
  regionId: string;
  name: string;
  description: string;
  zoneId: string;
  externalId: string;
  country: string;
  postcode: string;
  address: string[];
  version: string;
  createdTime: string;
  updatedTime: string;
  createdBy: string;
  updatedBy: string;
}
export interface ContactsItem {
  id: string;
  name?: string;
  email?: string;
  landlinePhone?: string;
  mobilePhone?: string;
  smsPhone?: string;
  smsEnabled: boolean;
  vip: boolean;
  note?: string;
  zoneId?: string;
  externalId?: string;
  createdTime?: string;
  updatedTime?: string;
  createdBy?: string;
  updatedBy?: string;
}
export interface SlaConfigList {
  id: string;

  tenantId: string;
}
export interface DeviceTypeItem {
  id: string /* 主键 */;
  name: string /* 类型名称 */;
  description?: string /* 描述 */;
  alertClassificationIds: /* 告警分类ID列表 */ string[];
  version: string /* 版本号 */;
  createdTime: string /* 创建时间 */;
  updatedTime: string /* 最近更新时间 */;
  createdBy?: string /* 创建人 */;
  updatedBy?: string /* 最后更新人 */;
}
export interface deviceGroupItem {
  id: string /* 主键 */;
  tenantId: string /* 租户ID */;
  name: string /* 名称 */;
  description?: string /* 描述信息 */;
  report: boolean /* 是否为报告分组 */;
  alertClassificationIds: /* 告警分类ID列表 */ string[];
  version: string /* 版本号 */;
  createdTime: string /* 创建时间 */;
  updatedTime: string /* 最近更新时间 */;
  createdBy?: string /* 创建人 */;
  updatedBy?: string /* 最后更新人 */;
}

export interface DeviceList {
  id: string /* 主键 */;
  tenantId: string /* 租户ID */;
  modelIdent: string /* 模型标识 */;
  regionId: string /* 所在区域ID */;
  locationId: string /* 所在场所ID */;
  typeIds: /* 资源类型ID列表 */ string[];
  groupIds: /* 资源关联设备组ID列表 */ string[];
  vendorIds: /* 服务商ID列表 */ string[];
  supportNoteIds: /* 行动策略列表 */ string[];
  alertClassificationIds: /* 告警分类ID列表 */ string[];
  assetNumber?: string /* 资产编号 */;
  externalId?: string /* 外部ID */;
  name: string /* 资源名称 */;
  monitorSources: /* 监控源 */ string[];
  unit?: string /* 业务单位 */;
  description?: string /* 备注|描述信息 */;
  timeZone?: string /* 时区 */;
  importance: deviceImportance /* 资源重要性 枚举类型: High :至关重要的 | Medium :中 | Low :低 | None :无 | Unknown :未知的 */;
  tags: /* 标签列表 */ string[];
  config: /* 资源配置信息 */ { [key: string]: string };
  active: boolean /* 是否激活 */;
  onlineTime: string /* 最后上线时间 */;
  offlineTime: string /* 最后离线时间 */;
  version: string /* 变更版本号 */;
  createdTime: string /* 录入时间 */;
  updatedTime: string /* 最近变更时间 */;
  createdBy?: string /* 录入人 */;
  updatedBy?: string /* 最近变更人 */;
  regionDesc?: string /* 所在区域名称 */;
  locationDesc?: string /* 场所名称 */;
  resourceTypes: /* 资源类型 描述 */ {
    id: string /* 主键 */;
    name: string /* 类型名称 */;
    description?: string /* 描述 */;
    alertClassificationIds: /* 告警分类ID列表 */ string[];
    version: string /* 版本号 */;
    createdTime: string /* 创建时间 */;
    updatedTime: string /* 最近更新时间 */;
    createdBy?: string /* 创建人 */;
    updatedBy?: string /* 最后更新人 */;
  }[];
  vendors: /* 服务商名称列表 */ {
    id: string /* 主键 */;
    name: string /* 名称 */;
    description?: string /* 描述 */;
    landlinePhone?: string /* 固定电话 */;
    supportPhone?: string /* 支持电话 */;
    contactName?: string /* 联系人姓名 */;
    email?: string /* 电子邮箱 */;
    version: string /* 版本号 */;
    createdTime: string /* 创建时间 */;
    updatedTime: string /* 最近更新时间 */;
    createdBy?: string /* 创建人 */;
    updatedBy?: string /* 最后更新人 */;
  }[];
  alertClassifications: /* 告警分类列表 */ {
    id: string /* 主键 */;
    name: string /* 分类名称 */;
    description?: string /* 描述信息 */;
    version: string /* 版本号 */;
    createdTime: string /* 创建时间 */;
    updatedTime: string /* 最近更新时间 */;
    createdBy?: string /* 创建人 */;
    updatedBy?: string /* 最后更新人 */;
  }[];
  groups: /* 设备分组列表 */ deviceGroupItem[];
  serviceNumbers: /* 服务编号列表 */ {
    id: string /* 主键 */;
    resourceId: string /* 资源ID */;
    number: string /* 服务编号 */;
    vendorIds?: /* 供应商列表 */ string[];
    type?: string /* 类型 */;
    progress?: string /* 进度 */;
    product?: string /* 产品 */;
    description?: string /* 描述信息 */;
  }[];
  contacts: /* 联系人列表 */ {
    id: string /* 主键 */;
    tenantId: string /* 租户ID */;
    title?: string /* 头衔 */;
    name: string /* 姓名 */;
    language?: string /* 语言 */;
    email?: string /* 邮箱地址 */;
    landlinePhone?: string /* 固定电话 */;
    mobilePhone?: string /* 移动电话 */;
    afterWorkPhone?: string /* 下班后联系电话 */;
    smsPhone?: string /* 短信号码 */;
    fax?: string /* 传真 */;
    smsEnabled: boolean /* 是否启用短信 */;
    vip: boolean /* 是否VIP */;
    note?: string /* 备注 */;
    zoneId?: string /* 时区ID */;
    externalId?: string /* 外部ID */;
    version: string /* 版本号 */;
    createdTime: string /* 创建时间 */;
    updatedTime: string /* 最近更新时间 */;
    createdBy?: string /* 创建人 */;
    updatedBy?: string /* 最后更新人 */;
  }[];
}
//获取客户列表
export function getTenantList(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.IAM}/security_containers/${data.containerId}/tenants`,
    method: Method.Get,
    signal: undefined,
    headers: {},
    params: data,
    data: {},
  });
}
// 获取用户列表
export function getAllotUserList /* 用户获取模块 */(
  data: Partial<ModuleItem> & RequestBase
) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/security_containers/${data.containerId}/users`,
    method: Method.Get,
    responseType: "json",
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
export function getUserGroupsList /* 用户组列表 */(
  data: { pageNumber: Number; pageSize: Number } & RequestBase
) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/security_containers/${data.containerId}/user_groups`,
    method: Method.Get,
    responseType: "json",
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//行动策略列表
export function getSupport_notesList(
  data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase
) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/${data.containerId}/tenant/current`,
    method: Method.Get,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//设备供应商列表
export function getSupplierList(
  data: { pageNumber: number; pageSize: number } & RequestBase
) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/vendors/device/${data.containerId}/tenant/current`,
    method: Method.Get,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//线路供应商列表
export function getLineSupplierList(
  data: { pageNumber: number; pageSize: number } & RequestBase
) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/vendors/line/${data.containerId}/tenant/current`,
    method: Method.Get,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//告警分类列表
export function getAlarmClassificationList(
  data: { pageNumber: number; pageSize: number } & RequestBase
) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/alert_classifications/${data.containerId}/tenant/current`,
    method: Method.Get,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//设备类型列表
export function getdeviceTypeList(
  data: { pageNumber: number; pageSize: number } & RequestBase
) {
  return request<never, Response<DeviceTypeItem[]>>({
    url: `${SERVER.CMDB}/resource_types/${data.containerId}/tenant/current`,
    method: Method.Get,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//设备分组列表
export function getdeviceGroupList(
  data: { pageNumber: number; pageSize: number } & RequestBase
) {
  return request<never, Response<deviceGroupItem[]>>({
    url: `${SERVER.CMDB}/groups/${data.containerId}/tenant/current`,
    method: Method.Get,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//联系人分组列表

export function getContacts(
  data: { pageNumber: number; pageSize: number } & RequestBase
) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.CMDB}/contacts/${data.containerId}/tenant/current`,
    method: Method.Get,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//场所列表
export function getLocationsTenantCurrent(
  data: {
    pageNumber: number;
    pageSize: number;
    keyword?: string;
    active?: string;
  } & RequestBase
) {
  return request<never, Response<Locations[]>>({
    url: `${SERVER.CMDB}/locations/${data.containerId}/tenant/current`,
    method: Method.Get,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//区域列表
export function getRegionTree(
  data: {
    pageNumber: Number;
    pageSize: Number;
    parentId: string;
    active: Boolean;
  } & RequestBase
) {
  return request<unknown, Response<RegionsTenant>>({
    url: `${SERVER.CMDB}/regions/${data.containerId}/tenant/current`,
    method: Method.Get,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//设备列表
export function getDeviceList(
  data: { pageNumber: number; pageSize: number; name?: string } & RequestBase
) {
  return request<never, Response[]>({
    url: `${SERVER.CMDB}/resources/${data.containerId}/tenant/current/desensitized`,
    method: Method.Get,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//Sla配置列表
export function getSlaConfigByPage(
  data: { pageNumber: number; pageSize: number } & RequestBase
) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/${data.containerId}/tenant/current`,
    method: Method.Get,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//移动告警分类安全容器
export function moveAlarmClassificationList(data: RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/alert_classifications/${data.id}/move_container`,
    method: Method.Patch,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//移动联系人安全容器
export function moveContactsList(data: RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/contacts/${data.id}/move_container`,
    method: Method.Patch,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//移动设备分组安全容器
export function moveDeviceGroupsList(data: RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/groups/${data.id}/move_container`,
    method: Method.Patch,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//移动区域安全容器
export function moveRegionsList(data: RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/regions/${data.id}/move_container`,
    method: Method.Patch,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//移动设备类型安全容器
export function moveDeviceTypeList(data: RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/resource_types/${data.id}/move_container`,
    method: Method.Patch,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//移动行动策略安全容器
export function moveSupportNotesList(data: RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/${data.id}/move_container`,
    method: Method.Patch,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//移动供应商安全容器
export function moveVendorsList(data: RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/vendors/${data.id}/move_container`,
    method: Method.Patch,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//移动设备安全容器
export function moveDeviceList(data: RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/resources/${data.id}/move_container`,
    method: Method.Patch,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//移动场所安全容器
export function moveLocationList(data: RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/locations/${data.id}/move_container`,
    method: Method.Patch,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//移动SLA安全容器
export function moveSlaList(data: RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/${data.id}/move_container`,
    method: Method.Patch,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//移动用户安全容器
export function moveUserList(data: RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.IAM}/users/id/${data.id}/move_container`,
    method: Method.Patch,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//移动客户安全容器
export function moveTenantsList(data: RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.IAM}/tenants/${data.id}/move_container`,
    method: Method.Patch,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//移动用户组安全容器
export function moveUserGroupList(data: RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.IAM}/user_groups/${data.id}/move_container`,
    method: Method.Patch,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//移动政支安全容器
export function movetenantContainerList(data: RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.IAM}/tenantContainer/${data.id}/move_container`,
    method: Method.Patch,
    signal:
      data.controller instanceof AbortController
        ? data.controller.signal
        : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
