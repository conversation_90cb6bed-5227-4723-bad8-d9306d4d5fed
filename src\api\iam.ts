/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */
import { SERVER, Method, bindSearchParams, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";
// import { appTerminal, appTerminalOption, menuType, menuTypeOption, menuExtendType, menuExtendTypeOption } from "@/api/application";
// import type { AppMeta, AppItem, MenuMeta, MenuItem } from "@/api/application";
// export { appTerminal, appTerminalOption, menuType, menuTypeOption, menuExtendType, menuExtendTypeOption };
// export type { AppMeta, AppItem, MenuMeta, MenuItem };

/**
 * IAM-平台
 */
export interface ConfigSecurity {
  enableMfa: boolean /* 是否开启多因素登录, 默认否 */;
  repeatLogin: boolean /* 是否允许重复登录, 默认否 */;
  loginFailureLimit: number /* 登录失败次数限制, 大于0时生效, 连续多次登录失败则暂时冻结账号, 默认不限制 */;
  histPasswordLimit: number /* 最近几次使用过的密码不能重复使用, 大于0生效, 默认不限制 */;
  passwordExpireDays: number /* 密码过期天数, 大于0生效, 默认不过期 */;
}
export interface PlatformItem {
  code: string /* 平台编码 */;
  name: string /* 平台名称 */;
  openName: string /* 平台对外显示名称 */;
  note: string /* 平台备注 */;
  multiTenant: boolean /* 是否多租户平台, 默认否 */;
  securityConfig: ConfigSecurity /* 安全配置 */;
  enablePermissionCheck: boolean /* 是否启用权限校验, 默认是 */;
  registrable: boolean /* 是否允许用户注册 */;
  desensitize: boolean /* 关键信息是否脱敏 */;
  initialUserPassword: string /* 平台用户的初始密码, 默认无 */;
  loginPage: string /* 登录页地址 */;
  config: string /* 配置信息JSON字符串 */;
  ownerId: string /* 平台拥有人用户 */;
  owner?: UserBaseItem /* 平台拥有人信息, 可选 */;

  // code: string;
  // name: string;
  // note: string /* 备注 */;
  // multiTenant: boolean /* 是否多租户平台，默认否 */;
  // tenantHasAllPermission: boolean /* 租户超管是否拥有平台下所有的权限信息，多租户平台时生效 */;
  // enablePermissionCheck: boolean /* 是否开启api级别的接口鉴权，默认是 */;
  // enableMfa: boolean /* 是否开启多因素登录，默认否 */;
  // enableTwoStep: boolean /* 是否开启两步认证，默认否 */;
  // registrable: boolean /* 是否允许用户注册 */;
  // desensitize: boolean /* 关键信息是否脱敏 */;
  // loginFailureLimit: number /* 登录失败次数限制，大于0时生效，连续多次登录失败则暂时冻结账号，默认不限制 */;
  // histPasswordLimit: number /* 最近几次使用过的密码不能重复使用，大于0生效，默认不限制 */;
  // passwordExpireDays: number /* 密码过期天数，默认不过期 */;
  // initialUserPassword: string /* 平台用户的初始密码，默认无 */;
  // ownerId?: string;
  // owner?: Partial<UserBaseItem> /* 平台拥有人信息, 可选 */;
  // footer?: string;
  createdTime?: string;
  updatedTime?: string;
}
export function getPlatform(data: Partial<PlatformItem> & RequestBase) {
  return request<unknown, Response<PlatformItem[]>>({
    url: `${SERVER.IAM}/platforms`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function addPlatform(data: Partial<PlatformItem> & RequestBase) {
  return request<unknown, Response<PlatformItem[]>>({
    url: `${SERVER.IAM}/platforms`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [
      "code",
      "name",
      "openName",
      "note",
      /* 配置部分 */
      "multiTenant" /* 是否多租户平台 */,
      "securityConfig" /* 是否多租户平台 */,
      "enablePermissionCheck" /* 是否启用权限校验 */,
      "registrable" /* 是否允许用户注册 */,
      "desensitize" /* 关键信息是否脱敏 */,
      "initialUserPassword" /* 平台用户的初始密码 */,
      "loginPage" /* 平台登录地址 */,
      "config" /* 配置信息 */,
      "owner" /* 平台拥有人信息 */,
    ].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function modPlatform(data: Partial<PlatformItem> & RequestBase) {
  return request<unknown, Response<PlatformItem[]>>({
    url: `${SERVER.IAM}/platforms/${data.code}`,
    method: ["name", "openName", "note", "securityConfig", "enablePermissionCheck", "registrable", "desensitize", "enableTwoStep", "registrable", "desensitize", "initialUserPassword", "loginPage", "config"].every((key) => Object.prototype.hasOwnProperty.call(data, key) && data[key] !== undefined) ? Method.Put : Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "openName", "note", "securityConfig", "enablePermissionCheck", "registrable", "desensitize", "enableTwoStep", "registrable", "desensitize", "initialUserPassword", "loginPage", "config"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function delPlatform(data: Partial<PlatformItem> & RequestBase) {
  return request<unknown, Response<PlatformItem[]>>({
    url: `${SERVER.IAM}/platforms/${data.code}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function importPlatform(data: { file: File } & RequestBase) {
  const $data = new FormData();
  $data.set("file", data.file);
  return request<unknown, Response<null>>({
    url: `${SERVER.IAM}/ops/platforms/import`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: $data,
  });
}
export function exportPlatform(data: { code: string } & RequestBase) {
  // console.log(data);
  return request<unknown, Response<Blob>>({
    url: `${SERVER.IAM}/ops/platforms/${data.code}/export`,
    method: Method.Get,
    responseType: "blob",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  }).then((res) => {
    if (!res.success) throw Object.assign(new Error(res.message), { data: res.data, success: res.success });
    if (res.data instanceof Blob) {
      const fileName = res.contentDisposition?.filename || `MENU_Data-${Date.now()}.json`;
      const file = new File([new Blob([res.data], { type: "application/json" })], fileName);
      const link = document.createElement("a");
      link.href = URL.createObjectURL(file);
      link.style.visibility = "hidden";
      link.download = file.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
    }
  });
}
// 平台权限
export interface PlatformPower {
  modules: {
    id: string;
    name: string;
    note: string;
    groups: {
      id: string;
      name: string;
      note: string;
      permissions: {
        id: string;
        name: string;
        note: string;
        enabled: boolean;
        assigned: boolean;
      }[];
    }[];
  }[];
}
export interface PlatformPowerItem {
  id: string;
  value: string;
  label: string;
  children?: PlatformPowerItem[];
  disabled: boolean;
  select?: boolean;
  isLeaf: boolean;
}
export function getModelAuthItemByPlatform(data: { platform: string; controller?: AbortController }) {
  return request<unknown, Response<string[]>>({
    url: `${SERVER.IAM}/platforms/${data.platform}/app_codes`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function modModelAuthItemByPlatform(data: { platform: string; select: string[]; controller?: AbortController }) {
  return request<unknown, Response<unknown>>({
    url: `${SERVER.IAM}/platforms/${data.platform}/apps`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: { appCodes: data.select instanceof Array ? data.select : [] },
  });
}
// TODO: 授权端管理
export interface TenantItem {
  id: string /* 主键 */;
  platform: string /* 平台编码 */;

  baileeTenantId: string /* 受托租户ID */;
  name: string /* 租户名称 */;
  abbreviation: string /* 租户缩写 */;
  systemEdition: string /* 系统版本编码 */;
  zoneId: string /* 所在时区 */;
  language: keyof typeof language /* 语言 */;
  address: string /* 地址 */;
  note: string /* 备注信息 */;
  owner: Partial<UserBaseItem>;

  securityConfig: ConfigSecurity /* 安全配置 */;
  ownerId: string /* 租户拥有人用户ID */;
  ownerName: string /* 租户拥有人姓名 */;
  ownerNickname: string /* 租户拥有人昵称 */;
  ownerAccount: string /* 租户拥有人账号 */;
  ownerPhone: string /* 租户拥有人手机号 */;
  ownerEmail: string /* 租户拥有人邮箱 */;
  blocked: boolean /* 是否已被冻结 */;
  version: string /* 乐观锁版本号 */;
  createdTime: string /* 创建时间 */;
  updatedTime: string /* 更新时间 */;
  createdBy: string /* 创建人信息 */;
  updatedBy: string /* 更新人信息 */;

  // id: string /* 租户id */;
  // parentId: string /* 父租户id */;
  // platform: string /* 平台编码 */;
  // name: string /* 租户名称 */;
  // address: string /* 地址 */;
  // note: string /* 备注信息 */;
  // ownerId: string /* 租户拥有人用户ID */;
  // ownerName?: string /* 租户拥有人姓名 */;
  // ownerNickname?: string /* 租户拥有人昵称 */;
  // ownerAccount?: string /* 租户拥有人账号 */;
  // ownerPhone?: string /* 租户拥有人手机号 */;
  // ownerEmail?: string /* 租户拥有人邮箱 */;
  // frozen: boolean /* 是否已被冻结 */;
  // createdTime?: string /* 创建时间 */;
  // updatedTime?: string /* 更新时间 */;
  // zoneId?: string /* 时区ID */;
  // abbreviation?: string;
}
export function getTenant(data: Partial<TenantItem> & { platform: string; keyword?: string } & RequestBase) {
  return request<unknown, Response<TenantItem[]>>({
    url: `${SERVER.IAM}/tenants`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: ["platform", "keyword"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { pageNumber: data.paging?.pageNumber, pageSize: data.paging?.pageSize }),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function addTenant(data: Partial<TenantItem> & RequestBase) {
  return request<unknown, Response<TenantItem[]>>({
    url: `${SERVER.IAM}/tenants`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: ["platform"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["baileeTenantId", "name", "abbreviation", "systemEdition", "zoneId", "language", "address", "note", "owner"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function modTenant(data: Partial<TenantItem> & RequestBase) {
  return request<unknown, Response<TenantItem[]>>({
    url: `${SERVER.IAM}/tenants/${data.id}`,
    method: Method.Put || Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "abbreviation", "zoneId", "language", "address", "note"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function delTenant(data: Partial<TenantItem> & RequestBase) {
  return request<unknown, Response<TenantItem[]>>({
    url: `${SERVER.IAM}/tenants/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
//冻结租户
export function cutTenantByBlock(data: Partial<TenantItem> & RequestBase) {
  return request<unknown, Response<TenantItem[]>>({
    url: `${SERVER.IAM}/tenants/${data.id}/block`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["ownerId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//解冻租户
export function cutTenantByActive(data: Partial<TenantItem> & RequestBase) {
  return request<unknown, Response<TenantItem[]>>({
    url: `${SERVER.IAM}/tenants/${data.id}/active`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//变更租户拥有人
export function changeTenantByOwner(data: Partial<TenantItem> & RequestBase) {
  return request<unknown, Response<TenantItem[]>>({
    url: `${SERVER.IAM}/tenants/${data.id}/owner`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { userId: data.ownerId }),
  });
}
// export function cutTenantByBlock(data: { id: string } & RequestBase) {
//   return request<unknown, Response<TenantItem[]>>({
//     url: `${SERVER.IAM}/tenants/${data.id}/block`,
//     method: Method.Patch,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//   });
// }
// export function cutTenantByActive(data: { id: string } & RequestBase) {
//   return request<unknown, Response<TenantItem[]>>({
//     url: `${SERVER.IAM}/tenants/${data.id}/unfreeze`,
//     method: Method.Patch,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//   });
// }

// TODO: 授权端管理
export enum terminalType {
  WEB = "WEB",
  WECHAT_APP = "WECHAT_APP",
}
export const terminalTypeOption: { label: string; value: keyof typeof terminalType }[] = [
  { label: "Web", value: terminalType.WEB },
  { label: "微信小程序", value: terminalType.WECHAT_APP },
];
/*
platform: string // 平台编码
name: string // 客户端名称
note: string // 备注
token: string // token, 为空则自动生成
terminal: string // 终端类型
accessTokenValidity: integer // access token有效期, 单位秒
refreshTokenValidity: integer // refresh token有效期, 单位秒
*/
export interface AuthItem {
  id: string /* 授权端id */;
  platform: string /* 平台编码 */;
  multiTenant?: boolean /* 是否多租户平台，0否·1是 */;
  name: string /* 授权端名称 */;
  note: string /* 备注 */;
  token: string /* 用于登录请求的认证头 */;
  terminal: keyof typeof terminalType;
  accessTokenValidity: number /* AccessToken有效期，单位秒 */;
  refreshTokenValidity: number /* RefreshToken有效期，单位秒 */;
  version: string /* 乐观锁版本号 */;
  createdTime?: string /* 创建时间 */;
  updatedTime?: string /* 更新时间 */;
}
export function getAuth(data: Partial<AuthItem> & RequestBase) {
  return request<unknown, Response<AuthItem[]>>({
    url: `${SERVER.IAM}/auth_clients`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: ["platform"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function addAuth(data: Partial<AuthItem> & RequestBase) {
  return request<unknown, Response<AuthItem[]>>({
    url: `${SERVER.IAM}/auth_clients`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "note", "token", "terminal", "accessTokenValidity", "refreshTokenValidity"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { platform: data.platform }),
  });
}
export function modAuth(data: Partial<AuthItem> & RequestBase) {
  return request<unknown, Response<AuthItem[]>>({
    url: `${SERVER.IAM}/auth_clients/${data.id}`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "note", "terminal", "accessTokenValidity", "refreshTokenValidity"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function delAuth(data: Partial<AuthItem> & RequestBase) {
  return request<unknown, Response<AuthItem[]>>({
    url: `${SERVER.IAM}/auth_clients/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
// TODO: 用户管理
export enum gender {
  FEMALE = "FEMALE",
  MALE = "MALE",
  SECRET = "SECRET",
}
export const genderOption: { label: string; value: keyof typeof gender }[] = [
  { label: "女性", value: "FEMALE" },
  { label: "男性", value: "MALE" },
  { label: "保密", value: "SECRET" },
];
export enum language {
  ZH_CN = "ZH_CN",
  EN_US = "EN_US",
  none = "none",
}
export const languageOption: { label: string; value: keyof typeof language }[] = [
  { label: "中文-简体", value: "ZH_CN" },
  { label: "英文-美国", value: "EN_US" },
  { label: "无", value: "none" },
];
export interface UserBaseItem {
  platform?: string;
  keyword?: string;
  id?: string;

  name: string /* 姓名 */;
  account: string /* 账号 */;
  phone: string /* 手机号码 */;
  email: string /* 邮箱 */;
  language: string /* 语言 */;
  gender: keyof typeof gender /* 性别 */;
  password?: string /* 密码 */;
}
export interface UserItem extends UserBaseItem {
  owner?: boolean /* 是否平台拥有人，非多租户平台有效 */;
  superAdmin?: boolean /* 是否为超管角色，非多租户平台有效 */;
  roles?: object[] /* 角色信息列表，非多租户平台有效 */;

  id: string;
  // /* In: UserBaseItem */
  // passwordDate?: string /* 密码修改日期 */;
  // profilePicture?: string /* 头像 */;
  // frozen?: boolean /* 是否已被冻结 */;
  // frozenExpire?: string /* 账号冻结的过期时间 */;
  // frozenNote?: string /* 账号冻结备注 */;
  // createdTime?: string /* 用户创建时间 */;
  // updatedTime?: string /* 用户信息变更时间 */;
  // busy: string;
  // lastLoginTime?: string;

  // name?: string /* 姓名 */;
  // nickname?: string /* 昵称 */;
  // account?: string /* 账号 */;
  // phone?: string /* 手机号码 */;
  // email?: string /* 邮箱 */;
  // language?: string /* 语言 */;
  // gender?: "SECRET" | "MALE" | "FEMALE" /* 性别 枚举类型: SECRET :保密 | MALE :男性 | FEMALE :女性 */;

  /* In: Tenant */
  tenantId?: string /* 所属租户ID */;
  tenantName?: string /* 所属租户名称 */;
  tenantAbbreviation?: string /* 所属租户的缩写 */;

  /* In: UserBaseItem */

  profilePicture?: string /* 头像 */;
  securityConfig?: /* 安全配置 */ ConfigSecurity;
  passwordTime?: string /* 密码修改时间戳 */;
  busy: boolean /* 是否忙碌状态 */;
  busyTime?: string /* 进入忙碌状态的时间戳 */;
  improved: boolean /* 是否已完善个人信息 */;
  blocked: boolean /* 是否已被冻结 */;
  lastLoginTime?: string /* 最近登录时间 */;
  lastActivity?: string /* 最近访问时间 */;
  version: string /* 乐观锁版本号 */;
  createdTime: string /* 创建时间 */;
  updatedTime: string /* 更新时间 */;
  desensitized: boolean /* 是否已脱敏 */;
}
export function getCurrentPlatformUser(data: Partial<UserItem> & RequestBase) {
  return request<unknown, Response<UserItem[]>>({
    url: `${SERVER.IAM}/platform/current/users`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: ["keyword", "account", "phone", "email", "schemas"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { pageNumber: data.paging?.pageNumber, pageSize: data.paging?.pageSize }),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function getUser(data: Partial<UserItem> & { platform: string } & RequestBase) {
  return request<unknown, Response<UserItem[]>>({
    url: `${SERVER.IAM}/users/platform/${data.platform}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: ["userId", "keyword", "ident"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { pageNumber: data.paging?.pageNumber, pageSize: data.paging?.pageSize }),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
/**
 * @description 向指定租户添加用户
 * @url http://*************:3000/project/11/interface/api/2621
 */
export function /* 向指定租户添加用户 */ addUserByTenant(req: { tenantId: string; name?: string; nickname?: string; account?: string; phone?: string; email?: string; language?: string; gender?: "SECRET" | "MALE" | "FEMALE"; password?: string } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");

  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  const data = { name: req.name /* 姓名 */, nickname: req.nickname /* 昵称 */, account: req.account /* 账号 */, phone: req.phone /* 手机号 */, email: req.email /* 邮箱 */, language: req.language /* 语言 */, gender: req.gender /* 性别枚举：SECRET :保密、MALE :男性、FEMALE :女性 */, password: req.password /* 密码 */ };

  return request<never, Response<UserItem>>({ url: `${SERVER.IAM}/tenants/${req.tenantId /* 租户ID */}/users`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
export function addUser(data: Partial<UserItem> & { platform: string } & RequestBase) {
  return request<unknown, Response<UserItem[]>>({
    url: `${SERVER.IAM}/platforms/${data.platform}/users`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "language", "account", "phone", "email", "gender", "password"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
/**
 * @description 修改用户信息
 * @url http://*************:3000/project/11/interface/api/2627
 */
export function /* 修改用户信息 */ modUser(req: { id: string; name?: string; nickname?: string; language?: string; gender?: "SECRET" | "MALE" | "FEMALE" } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");

  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  const data = { name: req.name /* 姓名 */, nickname: req.nickname /* 昵称 */, language: req.language /* 语言 */, gender: req.gender /* 性别枚举：SECRET :保密、MALE :男性、FEMALE :女性 */ };

  return request<never, Response<null>>({ url: `${SERVER.IAM}/users/id/${req.id /* 用户ID */}`, method: Method.Patch, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
export function getUserById(data: { id: string } & RequestBase) {
  return request<unknown, Response<UserBaseItem>>({
    url: `${SERVER.IAM}/users/${data.id}/plain`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function cutUserByBlock(data: { id: string; frozenNote: string; frozenExpire?: string } & RequestBase) {
  return request<unknown, Response<UserItem[]>>({
    url: `${SERVER.IAM}/users/${data.id}/block`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["frozenNote", "frozenExpire"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function cutUserByActive(data: { id: string } & RequestBase) {
  return request<unknown, Response<UserItem[]>>({
    url: `${SERVER.IAM}/users/${data.id}/active`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function cutUserPasswordReset(data: { id: string } & RequestBase) {
  return request<unknown, Response<UserItem[]>>({
    url: `${SERVER.IAM}/users/${data.id}/password/reset`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["password", "ptype"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
/**
 * IAM-前端管理
 */
// export function getApp(data: Partial<AppItem> & RequestBase) {
//   return request<unknown, Response<AppMeta[]>>({
//     url: `${SERVER.IAM}/front/apps`,
//     method: Method.Get,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
//   }).then((res) => {
//     return {
//       success: res.success,
//       message: res.message,
//       page: res.page,
//       size: res.size,
//       total: res.total,
//       data: Array.from(
//         res.data
//           .map((menu): AppItem => {
//             const config: Required<AppItem> = {
//               id: menu.id,
//               code: menu.id,
//               order: Number(menu.orderNum),
//               name: menu.id,
//               title: menu.cnName,
//               terminal: menu.terminal || appTerminal.WEB,
//               type: menuType.dir,
//               path: "",
//               icon: "el-icon-Grid",
//               url: "",
//               component: "",
//               keepalive: false,
//               extend: menuExtendType.none,
//               note: "",
//               children: [],
//               createdTime: menu.createdTime as string,
//               updatedTime: menu.updatedTime as string,
//             };

//             try {
//               const {
//                 /*  */
//                 type: type = config.type,
//                 path: path = config.path,
//                 icon: icon = config.icon,
//                 url: url = config.url,
//                 component: component = config.component,
//                 keepalive: keepalive = config.keepalive,
//                 extend: extend = config.extend,
//                 note: note = config.note,
//               } = JSON.parse(menu.config);
//               return Object.assign(config, {
//                 /*  */
//                 type,
//                 path,
//                 icon,
//                 url,
//                 component,
//                 keepalive,
//                 extend,
//                 note,
//               });
//             } catch (error) {
//               return config;
//             }
//           })
//           .sort((a, b) => Number(a.order) - Number(b.order))
//       ),
//     };
//   });
// }
// export function addApp(data: Partial<AppItem> & RequestBase) {
//   const req: Partial<AppMeta> = {
//     id: data.name,
//     cnName: data.title,
//     terminal: data.terminal,
//     note: data.note,
//     orderNum: data.order,
//     config: JSON.stringify({ type: data.type, path: data.path, icon: data.icon, url: data.url, micro: data.micro, component: data.component, keepalive: data.keepalive, extend: data.extend, note: data.note }, null, 2),
//   };
//   return request<unknown, Response<AppItem[]>>({
//     url: `${SERVER.IAM}/front/apps`,
//     method: Method.Post,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), req),
//   });
// }
// export function modApp(data: Partial<AppItem> & RequestBase) {
//   const req: Partial<AppMeta> = {
//     id: data.name,
//     cnName: data.title,
//     terminal: data.terminal,
//     note: data.note,
//     orderNum: data.order,
//     config: JSON.stringify({ type: data.type, path: data.path, icon: data.icon, url: data.url, micro: data.micro, component: data.component, keepalive: data.keepalive, extend: data.extend, note: data.note }, null, 2),
//   };
//   return request<unknown, Response<AppItem[]>>({
//     url: `${SERVER.IAM}/front/apps/${data.id}`,
//     method: Method.Put,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), req),
//   });
// }
// export function delApp(data: Partial<AppItem> & RequestBase) {
//   return request<unknown, Response<AppItem[]>>({
//     url: `${SERVER.IAM}/front/apps/${data.id}`,
//     method: Method.Delete,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
//   });
// }
// export function modAppByOrder(data: { orders: { appCode: string; order: number }[] } & RequestBase) {
//   return request<unknown, Response<AppItem[]>>({
//     url: `${SERVER.IAM}/front_apps/order`,
//     method: Method.Patch,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: ["orders"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//   });
// }

// 菜单
// export function getMenu(data: Partial<MenuItem> & RequestBase) {
//   return request<unknown, Response<MenuMeta[]>>({
//     url: `${SERVER.IAM}/menus`,
//     method: Method.Get,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: ["appCode"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
//   }).then((res) => {
//     return {
//       success: res.success,
//       message: res.message,
//       page: res.page,
//       size: res.size,
//       total: res.total,
//       data: Array.from(
//         res.data
//           .map((menu): MenuItem => {
//             const config: Required<MenuItem> = {
//               id: menu.id,
//               parentId: menu.parentId,
//               order: Number(menu.order),
//               enabled: menu.enabled,
//               name: menu.ident,
//               title: menu.name,
//               permissions: menu.permissions,
//               type: menuType.dir,
//               path: "",
//               icon: "el-icon-Grid",
//               url: "",
//               component: "",
//               keepalive: false,
//               extend: menuExtendType.none,
//               note: "",
//               children: [],
//               createdTime: menu.createdTime as string,
//               updatedTime: menu.updatedTime as string,
//             };

//             try {
//               const {
//                 /*  */
//                 type: type = config.type,
//                 path: path = config.path,
//                 icon: icon = config.icon,
//                 url: url = config.url,
//                 component: component = config.component,
//                 keepalive: keepalive = config.keepalive,
//                 extend: extend = config.extend,
//                 note: note = config.note,
//               } = JSON.parse(menu.config);
//               return Object.assign(config, {
//                 /*  */
//                 type,
//                 path,
//                 icon,
//                 url,
//                 component,
//                 keepalive,
//                 extend,
//                 note,
//               });
//             } catch (error) {
//               return config;
//             }
//           })
//           .map((value, _index, full) => {
//             if (value.id === value.parentId) return value;
//             else {
//               return Object.assign(value, {
//                 children: full
//                   .filter(({ parentId }) => parentId === value.id)
//                   .map((v) => Object.assign(v, { consume: true }))
//                   .sort((a, b) => Number(a.order) - Number(b.order)),
//               });
//             }
//           })
//           .filter((v: MenuItem & { consume?: boolean }) => {
//             const consume = v.consume;
//             if (consume) delete v.consume;
//             return !consume;
//           })
//           .sort((a, b) => Number(a.order) - Number(b.order))
//       ),
//     };
//   });
// }
// export function addMenu(data: Partial<MenuItem> & RequestBase) {
//   const req: Partial<MenuMeta> = {
//     parentId: data.parentId,
//     ident: data.name,
//     name: data.title,
//     type: data.type === menuType.button ? "BUTTON" : "MENU",
//     note: data.note,
//     order: Number(data.order),
//     enabled: Boolean(data.enabled),
//     permissions: data.permissions instanceof Array ? data.permissions : [],
//     config: JSON.stringify({ type: data.type, path: data.path, icon: data.icon, url: data.url, micro: data.micro, component: data.component, keepalive: data.keepalive, extend: data.extend, note: data.note }, null, 2),
//   };
//   return request<unknown, Response<MenuMeta[]>>({
//     url: `${SERVER.IAM}/menus`,
//     method: Method.Post,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: ["appCode"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), req),
//   });
// }
// export function modMenu(data: Partial<MenuItem> & RequestBase) {
//   const req: Partial<MenuMeta> = {
//     ident: data.name,
//     name: data.title,
//     type: data.type === menuType.button ? "BUTTON" : "MENU",
//     note: data.note,
//     order: Number(data.order),
//     enabled: Boolean(data.enabled),
//     permissions: data.permissions instanceof Array ? data.permissions : [],
//     config: JSON.stringify({ type: data.type, path: data.path, icon: data.icon, url: data.url, micro: data.micro, component: data.component, keepalive: data.keepalive, extend: data.extend, note: data.note }, null, 2),
//   };
//   return request<unknown, Response<MenuMeta[]>>({
//     url: `${SERVER.IAM}/menus/${data.id}`,
//     method: Method.Put,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), req),
//   });
// }
// export function delMenu(data: Partial<MenuItem> & RequestBase) {
//   return request<unknown, Response<MenuMeta[]>>({
//     url: `${SERVER.IAM}/menus/${data.id}`,
//     method: Method.Delete,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
//   });
// }
// export function exportMenu(data: { appCode: string } & RequestBase) {
//   return request<unknown, Response<Blob>>({
//     url: `${SERVER.IAM}/front_apps/${data.appCode}/menus/export`,
//     method: Method.Get,
//     responseType: "blob",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
//   });
// }
// export function importMenu(data: { menu: MenuMeta[]; appCode: string } & RequestBase) {
//   return request<unknown, Response<MenuMeta[]>>({
//     url: `${SERVER.IAM}/front_apps/${data.appCode}/menus/import`,
//     method: Method.Post,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: data.menu,
//   });
// }
// export function modMenuByParent(data: Partial<MenuItem> & RequestBase) {
//   if (data.id === data.parentId) throw new Error("[ERROR]: 菜单父级变更发生错误！ -code: ApiServiceFor_IAM:574");
//   return request<unknown, Response<MenuMeta[]>>({
//     url: `${SERVER.IAM}/menus/${data.id}/parent`,
//     method: Method.Patch,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: ["parentId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
//   });
// }
// export function modMenuByOrder(data: { orders: { menuId: string; order: number }[] } & RequestBase) {
//   return request<unknown, Response<MenuMeta[]>>({
//     url: `${SERVER.IAM}/menus/batch/order`,
//     method: Method.Patch,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: ["orders"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { order: data.order }),
//   });
// }

/**
 * IAM-后端管理
 */
export interface ModelItem {
  id: string /* 应用编码 */;
  name: string /* 应用名称 */;
  note: string /* 备注 */;
  order: number /* 排序 */;
  createdTime?: string;
  updatedTime?: string;
}
export function getModel(data: Partial<ModelItem> & RequestBase) {
  return request<unknown, Response<ModelItem[]>>({
    url: `${SERVER.IAM}/permission_modules`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  }).then((res) => {
    if (res.data instanceof Array) res.data.sort((a, b) => Number(a.order) - Number(b.order));
    else res.data = [];
    return res;
  });
}
export function addModel(data: Partial<ModelItem> & RequestBase) {
  return request<unknown, Response<ModelItem[]>>({
    url: `${SERVER.IAM}/permission_modules`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "note"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function modModel(data: Partial<ModelItem> & RequestBase) {
  return request<unknown, Response<ModelItem[]>>({
    url: `${SERVER.IAM}/permission_modules/${data.id}`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "note"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function delModel(data: Partial<ModelItem> & RequestBase) {
  return request<unknown, Response<ModelItem[]>>({
    url: `${SERVER.IAM}/permission_modules/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function modModelByOrder(data: { id: string; order: number; controller?: AbortController }) {
  return request<unknown, Response<ModelItem[]>>({
    url: `${SERVER.IAM}/permission_modules/${data.id}/order`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: (["order"] as (keyof typeof data)[]).reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
// 权限组
export interface AuthGroupItem {
  id: string /* 主键 */;
  groupId: string;
  moduleId: string /* 分组所属模块 */;
  name: string /* 应用名称 */;
  note: string /* 备注 */;
  order: number /* 排序 */;
  createdTime?: string;
  updatedTime?: string;
}
export function getAuthGroup(data: Partial<AuthGroupItem> & RequestBase) {
  return request<unknown, Response<AuthGroupItem[]>>({
    url: `${SERVER.IAM}/permission_groups`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { moduleId: data.moduleId }),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  }).then((res) => {
    if (res.data instanceof Array) res.data.sort((a, b) => Number(a.order) - Number(b.order)).map((v) => Object.assign(v, { groupId: v.id }));
    else res.data = [];
    return res;
  });
}
export function addAuthGroup(data: Partial<AuthGroupItem> & RequestBase) {
  return request<unknown, Response<AuthGroupItem[]>>({
    url: `${SERVER.IAM}/permission_groups`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "note"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { moduleId: data.moduleId }),
  });
}
export function modAuthGroup(data: Partial<AuthGroupItem> & RequestBase) {
  return request<unknown, Response<AuthGroupItem[]>>({
    url: `${SERVER.IAM}/permission_groups/${data.id}`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "note"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function delAuthGroup(data: Partial<AuthGroupItem> & RequestBase) {
  return request<unknown, Response<AuthGroupItem[]>>({
    url: `${SERVER.IAM}/permission_groups/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function modAuthGroupByOrder(data: { id: string; order: number; controller?: AbortController }) {
  return request<unknown, Response<AuthGroupItem[]>>({
    url: `${SERVER.IAM}/permission_groups/${data.id}/order`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: (["order"] as (keyof typeof data)[]).reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
// 权限
export interface ModelAuthItem {
  apis: string[] /* 关联API接口列表 */;
  id: string /* 应用编码 */;
  code: string /* 权限编码, 全局唯一 */;
  moduleId: string /* 归属模块编码 */;
  groupId: string /* 归属权限分组 */;
  name: string /* 权限名称 */;
  note: string /* 权限备注 */;
  enabled: boolean /* 是否启用 */;
  order: number /* 排序 */;
  createdTime?: string;
  updatedTime?: string;
}
export function getModelAuthItem(data: Partial<ModelAuthItem> & RequestBase) {
  return request<unknown, Response<ModelAuthItem[]>>({
    url: `${SERVER.IAM}/permissions`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: ["groupId", "moduleId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  }).then((res) => {
    if (res.data instanceof Array) res.data.sort((a, b) => Number(a.order) - Number(b.order));
    else res.data = [];
    return res;
  });
}
export function addModelAuthItem(data: Partial<ModelAuthItem> & RequestBase) {
  return request<unknown, Response<ModelAuthItem[]>>({
    url: `${SERVER.IAM}/permissions`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["code", "name", "note", "apis", "enabled"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { groupId: data.groupId, moduleId: data.moduleId }),
  });
}
export function modModelAuthItem(data: Partial<ModelAuthItem> & RequestBase) {
  return request<unknown, Response<ModelAuthItem[]>>({
    url: `${SERVER.IAM}/permissions/${data.id}`,
    method: ["name", "note", "apis", "enabled"].every((key) => Object.prototype.hasOwnProperty.call(data, key) && data[key] !== undefined) ? Method.Put : Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "note", "apis", "enabled"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function delModelAuthItem(data: Partial<ModelAuthItem> & RequestBase) {
  return request<unknown, Response<ModelAuthItem[]>>({
    url: `${SERVER.IAM}/permissions/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function modModelAuthItemByOrder(data: { id: string; order: number; controller?: AbortController }) {
  return request<unknown, Response<ModelAuthItem[]>>({
    url: `${SERVER.IAM}/permissions/${data.id}/order`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: (["order"] as (keyof typeof data)[]).reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

export enum thirdType {
  github = "github",
  gitee = "gitee",
  wechat = "wechat",
}
export const thirdTypeOption: { label: string; value: keyof typeof thirdType }[] = [
  { label: "GitHub", value: thirdType.github },
  { label: "Gitee", value: thirdType.gitee },
  { label: "WeChat", value: thirdType.wechat },
];
export interface ThirdItem {
  type: keyof typeof thirdType;
  platform: string;
  clientId?: string /* 权限编码, 全局唯一 */;
  clientSecret?: string /* 归属模块编码 */;
  callbackUrl?: string /* 归属权限分组 */;
}
export function getThirdItem(data: Partial<ThirdItem> & RequestBase) {
  return request<unknown, Response<Record<string, ThirdItem>>>({
    url: `${SERVER.IAM}/platforms/${data.platform}/auth_configs`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { groupId: data.groupId, moduleId: data.moduleId }),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  }).then(
    (res): Response<ThirdItem[]> => ({
      ...res,
      data: Object.entries(res.data).reduce((p, [key, value]) => (!value ? p : p.concat({ ...(value as ThirdItem), type: key as keyof typeof thirdType, platform: data.platform as string })), [] as ThirdItem[]),
    })
  );
}
export function addThirdItem(data: Partial<ThirdItem> & RequestBase) {
  return request<unknown, Response<ThirdItem[]>>({
    url: `${SERVER.IAM}/${data.type}/config/platform/${data.platform}`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["clientId", "clientSecret", "callbackUrl"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { groupId: data.groupId, moduleId: data.moduleId }),
  });
}
export function modThirdItem(data: Partial<ThirdItem> & RequestBase) {
  return request<unknown, Response<ThirdItem[]>>({
    url: `${SERVER.IAM}/${data.type}/config/platform/${data.platform}`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["clientId", "clientSecret", "callbackUrl"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function delThirdItem(data: Partial<ThirdItem> & RequestBase) {
  return request<unknown, Response<ThirdItem[]>>({
    url: `${SERVER.IAM}/${data.type}/config/platform/${data.platform}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
// 导航
export interface NavigationGroupItem {
  id: string;
  name: string /* 分组名称 */;
  note: string /* 备注信息 */;
  order: number /* 排序 */;
  items: NavigationItem[];
}

export function getNavigationData(data: Record<"platform", string> & RequestBase) {
  return request<unknown, Response<Record<"groups", NavigationGroupItem[]>>>({
    url: `${SERVER.IAM}/navigation/details/platforms/${data.platform}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { groupId: data.groupId, moduleId: data.moduleId }),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  }).then((res) => {
    if (!(res.data.groups instanceof Array)) res.data.groups = [];
    res.data.groups.sort((a, b) => a.order - b.order);
    for (let index = 0; index < res.data.groups.length; index++) {
      if (!(res.data.groups[index].items instanceof Array)) res.data.groups[index].items = [];
      Object.assign(res.data.groups[index], { items: res.data.groups[index].items.map(formatNavigationItemData).sort((a, b) => a.order - b.order) });
    }
    return res as unknown as Response<Record<"groups", (Omit<NavigationGroupItem, "items"> & { items: NavigationDataItem[] })[]>>;
  });
}
export function getNavigationGroups(data: Record<"platform", string> & RequestBase) {
  return request<unknown, Response<NavigationGroupItem[]>>({
    url: `${SERVER.IAM}/navigation/groups/platform/${data.platform}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { groupId: data.groupId, moduleId: data.moduleId }),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function addNavigationGroups(data: Partial<NavigationGroupItem> & RequestBase) {
  return request<unknown, Response<NavigationGroupItem>>({
    url: `${SERVER.IAM}/navigation/groups`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "note", "order"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { platform: data.platform }),
  });
}
export function modNavigationGroups(data: Partial<ThirdItem> & RequestBase) {
  return request<unknown, Response<NavigationGroupItem>>({
    url: `${SERVER.IAM}/navigation/groups/${data.id}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "note", "enabled"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function setNavigationGroupsByOrders(data: { orders: { id: string; order: number }[] } & RequestBase) {
  return request<unknown, Response<NavigationGroupItem>>({
    url: `${SERVER.IAM}/navigation/groups/batch/order`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { orders: data.orders }),
  });
}
export function delNavigationGroups(data: Partial<ThirdItem> & RequestBase) {
  return request<unknown, Response<NavigationGroupItem>>({
    url: `${SERVER.IAM}/navigation/groups/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export interface NavigationItem {
  id: string /* 主键 */;
  groupId: string /* 所属分组 */;
  name: string /* 导航项名称 */;
  note: string /* 备注信息 */;
  type: string /* 导航类型: APP :应用 MENU :菜单 LINK :外部链接 */;
  targetId: string /* 导航类型关联ID，用于权限过滤 */;
  // 1. 如果是菜单, 则关联菜单ID
  // 2. 如果是应用, 则关联应用ID
  // 3. 如果是外部链接, 则为空
  order: number /* 排序 */;
  config: string /* 配置信息 */;
  targetConfig: string /* 目标配置信息 */;
  // - 如果是应用类型, 则为应用的配置字段
  // - 如果是菜单类型, 则为菜单的配置字段
  // - 如果是连接类型, 则为空
}
export function getNavigationItem(data: Record<"id", string> & RequestBase) {
  return request<unknown, Response<NavigationItem[]>>({
    url: `${SERVER.IAM}/navigation/items/group/${data.id}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { groupId: data.groupId, moduleId: data.moduleId }),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  }).then((res) => ({ ...res, data: (res.data instanceof Array ? res.data : []).map(formatNavigationItemData) }));
}
export function addNavigationItem(data: Partial<NavigationItem> & RequestBase) {
  return request<unknown, Response<NavigationItem>>({
    url: `${SERVER.IAM}/navigation/items`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["groupId", "name", "note", "type", "targetId", "order", "config"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { platform: data.platform }),
  }).then((res) => ({ ...res, data: formatNavigationItemData(res.data) }));
}
export function modNavigationItem(data: Partial<ThirdItem> & RequestBase) {
  return request<unknown, Response<NavigationItem>>({
    url: `${SERVER.IAM}/navigation/items/${data.id}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["groupId", "name", "note", "type", "targetId", "order", "enabled", "config"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  }).then((res) => ({ ...res, data: formatNavigationItemData(res.data) }));
}
export function setNavigationItemByOrders(data: { orders: { id: string; order: number }[] } & RequestBase) {
  return request<unknown, Response<NavigationItem>>({
    url: `${SERVER.IAM}/navigation/items/batch/order`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { orders: data.orders }),
  }).then((res) => ({ ...res, data: formatNavigationItemData(res.data) }));
}
export function delNavigationItem(data: Partial<ThirdItem> & RequestBase) {
  return request<unknown, Response<NavigationItem>>({
    url: `${SERVER.IAM}/navigation/items/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export interface NavigationDataItem {
  id: string /* 主键 */;
  groupId: string /* 所属分组 */;
  name: string /* 导航项名称 */;
  note: string /* 备注信息 */;
  type: string /* 导航类型: APP :应用 MENU :菜单 LINK :外部链接 */;
  targetId: string /* 导航类型关联ID，用于权限过滤 */;
  order: number /* 排序 */;
  path: string;
  url: string;
  icon: string;
}
function formatNavigationItemData(data: NavigationItem): NavigationDataItem {
  const base: NavigationDataItem = { id: data.id, groupId: data.groupId, name: data.name, note: data.note, type: data.type, targetId: data.targetId, order: data.order, path: "", url: "", icon: "" };
  try {
    switch (data.type) {
      case "APP": {
        const { icon, path, url } = JSON.parse(data.targetConfig || "{}");
        Object.assign(base, { icon, path, url });
        break;
      }
      case "MENU": {
        const { icon, path, url } = JSON.parse(data.targetConfig || "{}");
        Object.assign(base, { icon, path, url });
        break;
      }
      default: {
        const { icon, path, url } = JSON.parse(data.config || "{}");
        Object.assign(base, { icon, path, url });
        break;
      }
    }
  } catch (error) {
    try {
      const { icon, path, url } = JSON.parse(data.config || "{}");
      Object.assign(base, { icon, path, url });
    } catch (error) {
      /*  */
    }
  }
  return base;
}
