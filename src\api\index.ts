import { SERVER, Method, bindSearchParams, type Response, type RequestBase, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

export function getHomeCountAboutTotal(data: { [key: string]: unknown }) {
  return request<unknown, Response<Record<"code" | "name", string>[]>>({
    url: `${SERVER.EVENT_CENTER}/alert/homeCount`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

/* 告警首页统计(总数和告警中) */
export function getHomeCountTotal(data: { [key: string]: unknown }) {
  return request<unknown, Response<Record<"code" | "name", string>[]>>({
    url: `${SERVER.EVENT_CENTER}/alert/homeCountAboutTotal`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

// /* 告警首页统计(总数和告警中) */
// export function getHomeCountAboutTotal(data: { [key: string]: unknown }) {
//   return request<unknown, Response<Record<"code" | "name", string>[]>>({
//     url: `${SERVER.EVENT_CENTER}/alert/homeCountAboutTotal`,
//     method: Method.Get,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: {},
//   });
// }

/* 工单首页统计(响应和处理时间统计) */
export function getOrderHomestatistics(data: { [key: string]: unknown }) {
  return request<unknown, Response<Record<"code" | "name", string>[]>>({
    url: `${SERVER.EVENT_CENTER}/event/order/homeCountAboutAverageTime`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

/* 工单首页统计(不包括响应和处理时间统计) */
export function getOrderHomeCounts(data: { [key: string]: unknown }) {
  return request<unknown, Response<Record<"code" | "name", string>[]>>({
    url: `${SERVER.EVENT_CENTER}/event/order/homeCount`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

/**
 * @description 获取当前租户的所有区域
 * @url http://*************:3000/project/47/interface/api/1173
 */
export function getAllcurrent(req: { supportNoteId: string; active: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/regions/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ supportNoteId: req.supportNoteId, active: req.active }, $req.params);
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_区域_可读, 资产管理中心_区域_新增, 资产管理中心_区域_编辑, 资产管理中心_区域_删除 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [资产管理中心_区域_可读].join(","), verifyPermissionIds: [资产管理中心_区域_新增, 资产管理中心_区域_编辑, 资产管理中心_区域_删除].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<Record<"code" | "name", string>[]>>($req)),
    { controller }
  );
}
// // 获取当前租户的所有区域
// export function getAllcurrent(data: { [key: string]: unknown }) {
//   return request<unknown, Response<Record<"code" | "name", string>[]>>({
//     url: `${SERVER.CMDB}/regions/tenant/current`,
//     method: Method.Get,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: {},
//   });
// }

// // 获取资源类型列表
export function getResourceTypes(data: { [key: string]: unknown }) {
  return request<unknown, Response<Record<"code" | "name", string>[]>>({
    url: `${SERVER.EVENT_CENTER}/alert/countByResourceTypeId`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function getOrderListTop(data: { [key: string]: unknown }) {
  return request<unknown, Response<Record<"code" | "name", string>[]>>({
    url: `${SERVER.EVENT_CENTER}/event/order/homeList`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      pageNumber: 1,
      pageSize: 5,
    },
    data: {},
  });
}

export function getAlarmListTop(data: { [key: string]: unknown }) {
  return request<unknown, Response<Record<"code" | "name", string>[]>>({
    url: `${SERVER.EVENT_CENTER}/alert/top10`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      pageNumber: 1,
      pageSize: 5,
    },
    data: {},
  });
}

export interface OrderInfo {
  id: string /* 主键 */;
  version: string;
  createdTime?: string /* 创建时间 */;
  updatedTime?: string /* 更新时间 */;
  tenantId: string /* 租户id */;
  initAble: boolean /* 当前租户是否已经初始化 */;
  orderTotalCount?: string /* 工单总数 */;
  averageResponseTime?: string /* 平均响应时长 */;
  averageHandleTime?: string /* 平均处理时长 */;
  p1OrderTotalCount?: string /* P1优先级工单总数 */;
  p2OrderTotalCount?: string /* P2优先级工单总数 */;
  p3OrderTotalCount?: string /* P3优先级工单总数 */;
  p4OrderTotalCount?: string /* P4优先级工单总数 */;
  p5OrderTotalCount?: string /* P5优先级工单总数 */;
  p6OrderTotalCount?: string /* P6优先级工单总数 */;
  p7OrderTotalCount?: string /* P7优先级工单总数 */;
  p8OrderTotalCount?: string /* P8优先级工单总数 */;
  newOrderTotalCount?: string /* 新建状态工单总数 */;
  processingOrderTotalCount?: string /* 处理中状态工单总数 */;
  pendingOrderTotalCount?: string /* 挂起状态工单总数 */;
  approvalOrderTotalCount?: string /* 审批状态工单总数 */;
  completedOrderTotalCount?: string /* 已完成状态工单总数 */;
}

export function getOrderInfo(data: RequestBase) {
  return request<unknown, Response<OrderInfo>>({
    url: `${SERVER.EVENT_CENTER}/home_page/order/homeCountByPage`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
