import zh_CN_Icon from "@/assets/locale/1x1/cn.svg";
import en_US_Icon from "@/assets/locale/1x1/us.svg";
import ru_RU_Icon from "@/assets/locale/1x1/ru.svg";
import ar_AE_Icon from "@/assets/locale/1x1/ae.svg";
import es_ES_Icon from "@/assets/locale/1x1/es.svg";
import fr_FR_Icon from "@/assets/locale/1x1/fr.svg";
import zh_CN_BigIcon from "@/assets/locale/4x3/cn.svg";
import en_US_BigIcon from "@/assets/locale/4x3/us.svg";
import ru_RU_BigIcon from "@/assets/locale/4x3/ru.svg";
import ar_AE_BigIcon from "@/assets/locale/4x3/ae.svg";
import es_ES_BigIcon from "@/assets/locale/4x3/es.svg";
import fr_FR_BigIcon from "@/assets/locale/4x3/fr.svg";
const zh_CN_LABEL = { "zh-CN": "简体中文", "en-US": "Simplified Chinese", "ru-RU": "Упрощенный китайский", "ar-AE": "الصينية المبسطة ", "es-ES": "Chino simplificado", "fr-FR": "Chinois simplifié" };
const en_US_LABEL = { "zh-CN": "美国英语", "en-US": "American English", "ru-RU": "Американский английский", "ar-AE": "الإنجليزية الأمريكية ", "es-ES": "Inglés americano", "fr-FR": "Anglais américain" };
const ru_RU_LABEL = { "zh-CN": "俄语", "en-US": "Russian", "ru-RU": "Русский язык", "ar-AE": "روسي ", "es-ES": "Ruso", "fr-FR": "Russe" };
const ar_AE_LABEL = { "zh-CN": "阿拉伯语", "en-US": "Arabic", "ru-RU": "Арабский язык", "ar-AE": "بالعربية ", "es-ES": "árabe", "fr-FR": "Arabe" };
const es_ES_LABEL = { "zh-CN": "西班牙语", "en-US": "Spanish", "ru-RU": "Испанский язык", "ar-AE": "إسبانية ", "es-ES": "Español", "fr-FR": "Espagnol" };
const fr_FR_LABEL = { "zh-CN": "法语", "en-US": "French", "ru-RU": "Французский язык", "ar-AE": "الفرنسية ", "es-ES": "Francés", "fr-FR": "Français" };

export enum locales {
  "zh-CN" = "zh-CN",
  "en-US" = "en-US",
  "ru-RU" = "ru-RU",
  "ar-AE" = "ar-AE",
  "es-ES" = "es-ES",
  "fr-FR" = "fr-FR",
}

export const localesOption: { label: string; icon: string; big_icon: string; value: keyof typeof locales; [key: string]: string }[] = [
  { label: zh_CN_LABEL["zh-CN"], value: locales["zh-CN"], icon: zh_CN_Icon, big_icon: zh_CN_BigIcon, zh_label: zh_CN_LABEL["zh-CN"], en_label: zh_CN_LABEL["en-US"], ru_label: zh_CN_LABEL["ru-RU"], ar_label: zh_CN_LABEL["ar-AE"], es_label: zh_CN_LABEL["es-ES"], fr_label: zh_CN_LABEL["fr-FR"] },
  { label: en_US_LABEL["en-US"], value: locales["en-US"], icon: en_US_Icon, big_icon: en_US_BigIcon, zh_label: en_US_LABEL["zh-CN"], en_label: en_US_LABEL["en-US"], ru_label: en_US_LABEL["ru-RU"], ar_label: en_US_LABEL["ar-AE"], es_label: en_US_LABEL["es-ES"], fr_label: en_US_LABEL["fr-FR"] },
  { label: ru_RU_LABEL["ru-RU"], value: locales["ru-RU"], icon: ru_RU_Icon, big_icon: ru_RU_BigIcon, zh_label: ru_RU_LABEL["zh-CN"], en_label: ru_RU_LABEL["en-US"], ru_label: ru_RU_LABEL["ru-RU"], ar_label: ru_RU_LABEL["ar-AE"], es_label: ru_RU_LABEL["es-ES"], fr_label: ru_RU_LABEL["fr-FR"] },
  { label: ar_AE_LABEL["ar-AE"], value: locales["ar-AE"], icon: ar_AE_Icon, big_icon: ar_AE_BigIcon, zh_label: ar_AE_LABEL["zh-CN"], en_label: ar_AE_LABEL["en-US"], ru_label: ar_AE_LABEL["ru-RU"], ar_label: ar_AE_LABEL["ar-AE"], es_label: ar_AE_LABEL["es-ES"], fr_label: ar_AE_LABEL["fr-FR"] },
  { label: es_ES_LABEL["es-ES"], value: locales["es-ES"], icon: es_ES_Icon, big_icon: es_ES_BigIcon, zh_label: es_ES_LABEL["zh-CN"], en_label: es_ES_LABEL["en-US"], ru_label: es_ES_LABEL["ru-RU"], ar_label: es_ES_LABEL["ar-AE"], es_label: es_ES_LABEL["es-ES"], fr_label: es_ES_LABEL["fr-FR"] },
  { label: fr_FR_LABEL["fr-FR"], value: locales["fr-FR"], icon: fr_FR_Icon, big_icon: fr_FR_BigIcon, zh_label: fr_FR_LABEL["zh-CN"], en_label: fr_FR_LABEL["en-US"], ru_label: fr_FR_LABEL["ru-RU"], ar_label: fr_FR_LABEL["ar-AE"], es_label: fr_FR_LABEL["es-ES"], fr_label: fr_FR_LABEL["fr-FR"] },
];

export type Locales = keyof typeof locales;

export const Clientsigningplace: { label: string; value: string }[] = [
  { label: "其他", value: "0" },
  { label: "集团", value: "1" },
  { label: "国际公司", value: "2" },
  { label: "外地电信", value: "3" },
  { label: "上海", value: "4" },
];
export const industry: { label: string; value: string }[] = [
  { label: "其他", value: "0" },
  { label: "大企业", value: "1" },
  { label: "党政军", value: "2" },
  { label: "教育文化", value: "3" },
  { label: "金融", value: "4" },
  { label: "商贸连锁", value: "5" },
  { label: "物流", value: "6" },
  { label: "制造业", value: "7" },
];
export const OTCR: { label: string; value: string }[] = [
  { label: "上海", value: "0" },
  { label: "集团", value: "1" },
  { label: "外省市", value: "2" },
  { label: "香港", value: "3" },
  { label: "美洲", value: "4" },
  { label: "欧洲", value: "5" },
  { label: "合作方", value: "6" },
  { label: "其他", value: "7" },
];
export const platformVersion: { label: string; value: string }[] = [
  { label: "上海", value: "0" },
  { label: "集团", value: "1" },
  { label: "外省市", value: "2" },
];
