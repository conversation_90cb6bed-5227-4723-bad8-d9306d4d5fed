import contacts from "@/views/pages/alarm_convergence/details/changeDetail/models/contacts.vue";

export const operationLogger = [
  { origin: "system_config", operation: "事件处理配置", type: "更新", name: "更新事件处理配置", auditCode: "ec.systemConfig.createEvent.create" },
  { origin: "system_config", operation: "事件处理配置", type: "更新", name: "更新事件关闭配置", auditCode: "ec.systemConfig.closeEvent.create" },
  { origin: "monitor_source_mapping", operation: "监控源", type: "更新", name: "更新监控源映射(全量更新)", auditCode: "ec.monitorSourceMapping.allUpdate" },
  { origin: "monitor_source_mapping", operation: "监控源", type: "更新", name: "更新监控源映射(选择性更新)", auditCode: "ec.monitorSourceMapping.selectivityUpdate" },
  { origin: "priority_matrix", operation: "优先级矩阵", type: "更新", name: "更新优先级矩阵", auditCode: "ec.priorityMatrix.update" },
  { origin: "priority_matrix", operation: "优先级矩阵", type: "更新", name: "更新优先级矩阵(选择性更新)", auditCode: "ec.priorityMatrix.selectivityUpdate" },
  { origin: "", operation: "通知模板", type: "新建", name: "新建通知订阅", auditCode: "ec.notifySubscription.create" },
  { origin: "", operation: "通知模板", type: "更新", name: "更新通知订阅", auditCode: "ec.notifySubscription.update" },
  { origin: "", operation: "变更", type: "新增", name: "新增变更", auditCode: "ec.change.create" },
  { origin: "", operation: "变更", type: "选择", name: "选择性更新变更信息", auditCode: "ec.change.selectiveUpdate" },
  { origin: "", operation: "变更", type: "修改", name: "修改优先级", auditCode: "ec.change.updatePriority" },
  { origin: "", operation: "变更", type: "修改", name: "修改重要性", auditCode: "ec.change.updateImportance" },
  { origin: "", operation: "变更", type: "修改", name: "修改紧急性", auditCode: "ec.change.updateSeverity" },
  { origin: "", operation: "变更", type: "修改", name: "修改变更时间", auditCode: "ec.change.updateTime" },
  { origin: "contact", operation: "联系人", type: "添加", name: "添加联系人", auditCode: "ec.change.addContact" },
  { origin: "contact", operation: "联系人", type: "移除", name: "移除联系人", auditCode: "ec.change.removeContact" },
  { origin: "", operation: "变更", type: "变更", name: "变更添加设备", auditCode: "ec.change.addDevice" },
  { origin: "", operation: "变更", type: "变更", name: "变更移除设备", auditCode: "ec.change.removeDevice" },
  { origin: "finish_code", operation: "完结代码", type: "新增", name: "新增完结代码配置", auditCode: "ec.codeConfig.create" },
  { origin: "finish_code", operation: "完结代码", type: "更新", name: "更新完结代码配置", auditCode: "ec.codeConfig.update" },
  { origin: "finish_code", operation: "完结代码", type: "启用/禁用", name: "启用/禁用完结代码配置", auditCode: "ec.codeConfig.changeStatus" },
  { origin: "finish_code", operation: "完结代码", type: "删除", name: "删除完结代码配置", auditCode: "ec.codeConfig.delete" },
  { origin: "", operation: "客户报表", type: "新增", name: "新增客户报表", auditCode: "ec.customerStatement.create" },
  { origin: "", operation: "客户报表", type: "启用", name: "启用客户报表", auditCode: "ec.customerStatement.enable" },
  { origin: "", operation: "客户报表", type: "禁用", name: "禁用客户报表", auditCode: "ec.customerStatement.disable" },
  { origin: "", operation: "客户报表", type: "更新", name: "更新客户报表", auditCode: "ec.customerStatement.update" },
  { origin: "", operation: "客户报表", type: "删除", name: "删除客户报表", auditCode: "ec.customerStatement.delete" },
  { origin: "", operation: "客户报表", type: "新增", name: "向报表中添加客户", auditCode: "ec.customerStatement.addTenant" },
  { origin: "", operation: "客户报表", type: "移除", name: "从报表中移除客户", auditCode: "ec.customerStatement.removeTenant" },
  { origin: "", operation: "邮件模板", type: "创建", name: "创建邮件模板", auditCode: "ec.emailTemplate.create" },
  { origin: "", operation: "邮件模板", type: "修改", name: "修改邮件模板", auditCode: "ec.emailTemplate.update" },
  { origin: "", operation: "邮件模板", type: "启用", name: "启用邮件模板", auditCode: "ec.emailTemplate.enable" },
  { origin: "", operation: "邮件模板", type: "禁用", name: "禁用邮件模板", auditCode: "ec.emailTemplate.disable" },
  { origin: "", operation: "邮件模板", type: "删除", name: "删除邮件模板", auditCode: "ec.emailTemplate.delete" },
  { origin: "", operation: "事件", type: "新增", name: "手动新增事件", auditCode: "ec.event.manualCreate" },
  { origin: "", operation: "事件", type: "新增", name: "新增小记", auditCode: "ec.event.saveNote" },
  { origin: "", operation: "事件", type: "更新", name: "更新小记", auditCode: "ec.event.editNote" },
  { origin: "", operation: "事件", type: "删除", name: "删除小记", auditCode: "ec.event.deleteNote" },
  { origin: "", operation: "事件", type: "修改", name: "修改事件优先级", auditCode: "ec.event.updatePriority" },
  { origin: "", operation: "事件", type: "修改", name: "批量修改事件优先级", auditCode: "ec.event.batchChangePriority" },
  { origin: "", operation: "事件", type: "新增", name: "新增关联", auditCode: "ec.event.addAssociation" },
  { origin: "", operation: "事件", type: "移除", name: "移除关联", auditCode: "ec.event.removeAssociation" },
  { origin: "", operation: "事件", type: "关闭", name: "关闭收集告警", auditCode: "ec.event.disableCollectAlert" },
  { origin: "", operation: "事件", type: "添加", name: "添加联系人", auditCode: "ec.event.addContact" },
  { origin: "", operation: "事件", type: "移除", name: "移除联系人", auditCode: "ec.event.removeContact" },
  { origin: "", operation: "事件", type: "添加", name: "添加设备", auditCode: "ec.event.addDevice" },
  { origin: "", operation: "事件", type: "移除", name: "移除设备", auditCode: "ec.event.removeDevice" },
  { origin: "", operation: "事件", type: "修改", name: "修改重要性", auditCode: "ec.event.updateImportance" },
  { origin: "", operation: "事件", type: "修改", name: "修改紧急性", auditCode: "ec.event.updateUrgency" },
  { origin: "", operation: "事件", type: "更新", name: "全量更新事件", auditCode: "ec.event.updateEditable" },
  { origin: "", operation: "事件", type: "关闭", name: "批量关闭事件", auditCode: "ec.event.batchClose" },
  { origin: "", operation: "事件", type: "添加", name: "批量添加小记", auditCode: "ec.event.batchAddNote" },
  { origin: "", operation: "工单看板", type: "修改", name: "工单看板批量修改事件优先级", auditCode: "ec.event.boardBatchChangePriority" },
  { origin: "", operation: "工单看板", type: "关闭", name: "工单看板批量关闭事件", auditCode: "ec.event.boardBatchClose" },
  { origin: "", operation: "工单看板", type: "添加", name: "工单看板批量添加小记", auditCode: "ec.event.boardBatchAddNote" },
  { origin: "", operation: "短信模板", type: "新增", name: "新增短信模板", auditCode: "ec.messageTemplate.create" },
  { origin: "", operation: "短信模板", type: "更新", name: "更新短信模板", auditCode: "ec.messageTemplate.update" },
  { origin: "", operation: "短信模板", type: "删除", name: "删除短信模板", auditCode: "ec.messageTemplate.delete" },
  { origin: "", operation: "短信模板", type: "启用", name: "启用短信模板", auditCode: "ec.messageTemplate.enable" },
  { origin: "", operation: "短信模板", type: "禁用", name: "禁用短信模板", auditCode: "ec.messageTemplate.disable" },
  { origin: "", operation: "发布", type: "新建", name: "新建发布", auditCode: "ec.publish.create" },
  { origin: "", operation: "发布", type: "更新", name: "发布更新详述", auditCode: "ec.publish.updateDesc" },
  { origin: "", operation: "发布", type: "保存", name: "发布保存小记", auditCode: "ec.publish.saveNote" },
  { origin: "", operation: "发布", type: "更新", name: "发布更新小记", auditCode: "ec.publish.editNote" },
  { origin: "", operation: "发布", type: "删除", name: "发布删除小记", auditCode: "ec.publish.deleteNote" },
  { origin: "", operation: "发布", type: "添加", name: "发布添加设备", auditCode: "ec.publish.addDevice" },
  { origin: "", operation: "发布", type: "移除", name: "发布移除设备", auditCode: "ec.publish.removeDevice" },
  { origin: "", operation: "发布", type: "添加", name: "发布添加联系人", auditCode: "ec.publish.addContact" },
  { origin: "", operation: "发布", type: "移除", name: "发布移除联系人", auditCode: "ec.publish.removeContact" },
  { origin: "", operation: "发布", type: "更新", name: "发布详情页更新发布", auditCode: "ec.publish.update" },
  { origin: "", operation: "问题", type: "新增", name: "新增问题", auditCode: "ec.question.create" },
  { origin: "", operation: "问题", type: "更新", name: "更新问题详述", auditCode: "ec.question.updateDesc" },
  { origin: "", operation: "问题", type: "修改", name: "修改问题优先级", auditCode: "ec.question.updatePriority" },
  { origin: "", operation: "问题", type: "添加", name: "添加联系人", auditCode: "ec.question.addContact" },
  { origin: "", operation: "问题", type: "移除", name: "移除联系人", auditCode: "ec.question.removeContact" },
  { origin: "", operation: "问题", type: "添加", name: "添加设备", auditCode: "ec.question.addDevice" },
  { origin: "", operation: "问题", type: "移除", name: "移除设备", auditCode: "ec.question.removeDevice" },
  { origin: "", operation: "问题", type: "保存", name: "保存小记", auditCode: "ec.question.saveNote" },
  { origin: "", operation: "问题", type: "更新", name: "更新小记", auditCode: "ec.question.editNote" },
  { origin: "", operation: "问题", type: "删除", name: "删除小记", auditCode: "ec.question.deleteNote" },
  { origin: "", operation: "问题", type: "更新", name: "全量更新问题", auditCode: "ec.question.updateAll" },
  { origin: "", operation: "服务请求", type: "新增", name: "新增服务请求", auditCode: "ec.serviceRequest.create" },
  { origin: "", operation: "服务请求", type: "保存", name: "保存小记", auditCode: "ec.serviceRequest.createNote" },
  { origin: "", operation: "服务请求", type: "更新", name: "更新小记", auditCode: "ec.serviceRequest.updateNote" },
  { origin: "", operation: "服务请求", type: "删除", name: "删除小记", auditCode: "ec.serviceRequest.deleteNote" },
  { origin: "", operation: "服务请求", type: "添加", name: "服务请求添加设备", auditCode: "ec.serviceRequest.addDevice" },
  { origin: "", operation: "服务请求", type: "移除", name: "服务请求移除设备", auditCode: "ec.serviceRequest.removeDevice" },
  { origin: "", operation: "服务请求", type: "添加", name: "服务请求添加联系人", auditCode: "ec.serviceRequest.addContact" },
  { origin: "", operation: "服务请求", type: "移除", name: "服务请求移除联系人", auditCode: "ec.serviceRequest.removeContact" },
  { origin: "", operation: "服务请求", type: "更新", name: "全量更新服务请求", auditCode: "ec.serviceRequest.updateAll" },
  { origin: "degrade", operation: "告警降级", type: "新增", name: "创建降级策略", auditCode: "ec.degrade.create" },
  { origin: "degrade_global", operation: "全局告警降级", type: "新增", name: "创建全局降级策略", auditCode: "ec.degrade.createGlobal" },
  { origin: "degrade", operation: "告警降级", type: "更新", name: "更新降级策略", auditCode: "ec.degrade.update" },
  { origin: "degrade_global", operation: "全局告警降级", type: "更新", name: "更新全局降级策略", auditCode: "ec.degrade.updateGlobal" },
  { origin: "degrade", operation: "降级策略", type: "删除", name: "删除降级策略", auditCode: "ec.degrade.delete" },
  { origin: "degrade", operation: "降级策略", type: "启用", name: "启用/禁用降级策略", auditCode: "ec.degrade.enable" },
  { origin: "sla", operation: "SLA配置", type: "新增", name: "新增SLA规则", auditCode: "ec.sla.create" },
  { origin: "sla_global", operation: "全局SLA配置", type: "新增", name: "新增全局SLA规则", auditCode: "ec.sla.createGlobal" },
  { origin: "sla", operation: "SLA配置", type: "更新", name: "更新SLA规则基本信息", auditCode: "ec.sla.update" },
  { origin: "sla_global", operation: "全局SLA配置", type: "更新", name: "更新全局SLA", auditCode: "ec.sla.updateGlobal" },
  { origin: "sla", operation: "SLA配置", type: "删除", name: "删除SLA规则", auditCode: "ec.sla.delete" },
  { origin: "sla", operation: "SLA配置", type: "禁用/启用", name: "禁用/启用SLA规则", auditCode: "ec.sla.disable" },
  { origin: "user_group", operation: "用户组", type: "新增", name: "创建用户组", auditCode: "ec.team.create" },
  { origin: "user_group", operation: "用户组", type: "更新", name: "更新用户组", auditCode: "ec.team.update" },
  { origin: "user_group", operation: "用户组", type: "删除", name: "删除用户组", auditCode: "ec.team.delete" },
  { origin: "alert_classification", operation: "告警分类", type: "新增", name: "新增告警分类", auditCode: "cmdb.alert_classification.create" },
  { origin: "alert_classification", operation: "告警分类", type: "更新", name: "更新告警分类", auditCode: "cmdb.alert_classification.update" },
  { origin: "alert_classification", operation: "告警分类", type: "删除", name: "删除告警分类", auditCode: "cmdb.alert_classification.delete" },
  { origin: "contact", operation: "联系人", type: "新增", name: "新增联系人", auditCode: "cmdb.contact.create" },
  { origin: "contact", operation: "联系人", type: "更新", name: "更新联系人", auditCode: "cmdb.contact.selectiveUpdate" },
  { origin: "contact", operation: "联系人", type: "删除", name: "删除联系人", auditCode: "cmdb.contact.delete" },
  { origin: "contact", operation: "联系人", type: "查询", name: "查询联系人(非脱敏)", auditCode: "cmdb.contact.readNonDesensitized" },
  { origin: "contact", operation: "联系人", type: "查询", name: "查询联系人(非脱敏)", auditCode: "cmdb.contact.readByIdNonDesensitized" },
  { origin: "contact", operation: "联系人", type: "查询", name: "获取联系人信息(脱敏)", auditCode: "cmdb.contact.readDesensitized" },
  { origin: "device_group", operation: "设备分组", type: "新增", name: "创建设备组", auditCode: "cmdb.resourceGroup.create" },
  { origin: "device_group", operation: "设备分组", type: "更新", name: "更新设备组", auditCode: "cmdb.resourceGroup.update" },
  { origin: "device_group", operation: "设备分组", type: "删除", name: "删除设备组", auditCode: "cmdb.resourceGroup.delete" },
  { origin: "location", operation: "场所", type: "新增", name: "新增场所", auditCode: "cmdb.location.create" },
  { origin: "location", operation: "场所", type: "更新", name: "更新场所", auditCode: "cmdb.location.selectiveUpdate" },
  { origin: "location", operation: "场所", type: "删除", name: "删除场所", auditCode: "cmdb.location.delete" },
  { origin: "region", operation: "区域", type: "新增", name: "创建区域", auditCode: "cmdb.region.create" },
  { origin: "region", operation: "区域", type: "更新", name: "更新区域信息", auditCode: "cmdb.region.selectiveUpdate" },
  { origin: "region", operation: "区域", type: "变更", name: "变更父区域", auditCode: "cmdb.region.changeParent" },
  { origin: "region", operation: "区域", type: "删除", name: "删除区域", auditCode: "cmdb.region.delete" },
  { origin: "resource", operation: "设备", type: "新增", name: "创建资源", auditCode: "cmdb.resource.create" },
  { origin: "resource", operation: "设备", type: "更新", name: "选择性更新资源信息", auditCode: "cmdb.resource.selectivityUpdate" },
  { origin: "resource", operation: "设备", type: "删除", name: "删除资源", auditCode: "cmdb.resource.delete" },
  { origin: "resource_type", operation: "设备类型", type: "新增", name: "新增资源类型", auditCode: "cmdb.resourceType.create" },
  { origin: "resource_type", operation: "设备类型", type: "更新", name: "更新资源类型", auditCode: "cmdb.resourceType.update" },
  { origin: "resource_type", operation: "设备类型", type: "删除", name: "删除资源类型", auditCode: "cmdb.resourceType.delete" },
  { origin: "service_number", operation: "服务编号", type: "新增", name: "创建服务编号", auditCode: "cmdb.serviceNumber.create" },
  { origin: "service_number", operation: "服务编号", type: "更新", name: "更新服务编号", auditCode: "cmdb.serviceNumber.update" },
  { origin: "service_number", operation: "服务编号", type: "删除", name: "删除服务编号", auditCode: "cmdb.serviceNumber.delete" },
  { origin: "support_note_global", operation: "全局行动策略", type: "新增", name: "新增全局策略", auditCode: "cmdb.supportNote.globalCreate" },
  { origin: "support_note_global", operation: "全局行动策略", type: "更新", name: "更新全局行动策略", auditCode: "cmdb.supportNote.globalUpdate" },
  { origin: "support_note_global", operation: "全局行动策略", type: "删除", name: "删除全局行动策略", auditCode: "cmdb.supportNote.globalDelete" },
  { origin: "support_note", operation: "行动策略", type: "新增", name: "新增行动策略", auditCode: "cmdb.supportNote.create" },
  { origin: "support_note", operation: "行动策略", type: "更新", name: "更新行动策略", auditCode: "cmdb.supportNote.update" },
  { origin: "support_note", operation: "行动策略", type: "更新", name: "更新行动策略默认配置", auditCode: "cmdb.supportNote.defaultUpdate" },
  { origin: "support_note", operation: "行动策略", type: "更新", name: "更新行动策略激活配置", auditCode: "cmdb.supportNote.activeUpdate" },
  { origin: "support_note", operation: "行动策略", type: "删除", name: "删除行动策略", auditCode: "cmdb.supportNote.delete" },
  { origin: "vendor", operation: "供应商", type: "新增", name: "新增供应商", auditCode: "cmdb.vendor.create" },
  { origin: "vendor", operation: "供应商", type: "更新", name: "更新供应商", auditCode: "cmdb.vendor.update" },
  { origin: "vendor", operation: "供应商", type: "删除", name: "删除供应商", auditCode: "cmdb.vendor.delete" },
  { origin: "vendor", operation: "供应商", type: "添加", name: "添加关联资源", auditCode: "cmdb.vendor.addRelationResource" },
  { origin: "vendor", operation: "供应商", type: "移除", name: "移除关联资源", auditCode: "cmdb.vendor.removeRelationResource" },
  { origin: "role", operation: "角色", type: "新增", name: "新增角色", auditCode: "iam.role.create" },
  { origin: "role", operation: "角色", type: "新增", name: "新增全局角色", auditCode: "iam.global_role.create" },
  { origin: "role", operation: "角色", type: "更新", name: "修改角色", auditCode: "iam.role.update" },
  { origin: "role", operation: "角色", type: "修改", name: "修改全局角色", auditCode: "iam.global_role.update" },
  { origin: "role", operation: "角色", type: "更新", name: "设置基础角色", auditCode: "iam.role.set_as_basic" },
  { origin: "role", operation: "角色", type: "删除", name: "删除角色", auditCode: "iam.role.delete" },
  { origin: "role", operation: "角色", type: "删除", name: "删除全局角色", auditCode: "iam.global_role.delete" },
  { origin: "role", operation: "权限配置", type: "权限配置", name: "变更角色功能权限", auditCode: "iam.role_auth.save" },
  { origin: "role", operation: "权限配置", type: "权限配置", name: "变更全局角色功能权限", auditCode: "iam.global_role_auth.save" },
  { origin: "", operation: "", type: "新增", name: "新增OAuth2应用", auditCode: "iam.oauth_app.create" },
  { origin: "user_group", operation: "用户组", type: "新增", name: "新增用户组", auditCode: "iam.user_group.create" },
  { origin: "user_group", operation: "用户组", type: "更新", name: "更新用户组", auditCode: "iam.user_group.update" },
  { origin: "user_group", operation: "用户组", type: "删除", name: "删除用户组", auditCode: "iam.user_group.delete" },
  { origin: "user", operation: "用户", type: "新增", name: "新增用户", auditCode: "iam.user.create" },
  { origin: "user", operation: "用户", type: "新增", name: "为租户新增用户", auditCode: "iam.user.create" },
  { origin: "user", operation: "用户", type: "邀请", name: "邀请用户", auditCode: "iam.user.invite" },
  { origin: "user", operation: "用户", type: "移除", name: "移除用户", auditCode: "iam.user.remove" },
  { origin: "user", operation: "用户", type: "更新", name: "修改用户信息", auditCode: "iam.user.edit" },
  { origin: "user", operation: "用户", type: "冻结", name: "锁定用户", auditCode: "iam.user.block" },
  { origin: "user", operation: "用户", type: "删除", name: "删除用户", auditCode: "iam.user.delete" },
  { origin: "user", operation: "用户", type: "解冻", name: "解锁用户", auditCode: "iam.user.unblock" },
  { origin: "user", operation: "用户", type: "更新", name: "重置用户密码", auditCode: "iam.user.reset_pwd" },
  { origin: "user", operation: "用户", type: "查看", name: "查看用户", auditCode: "iam.user.read" },
  { origin: "tenant", operation: "客户", type: "新增", name: "新增客户", auditCode: "iam.tenant.create" },
  { origin: "tenant", operation: "客户", type: "更新", name: "更新客户", auditCode: "iam.tenant.update" },
  { origin: "tenant", operation: "客户", type: "锁定", name: "锁定客户", auditCode: "iam.tenant.block" },
  { origin: "tenant", operation: "客户", type: "解锁", name: "解锁客户", auditCode: "iam.tenant.unblock" },
  { origin: "tenant", operation: "客户", type: "激活", name: "激活客户", auditCode: "iam.tenant.activate" },
  { origin: "tenant", operation: "客户", type: "取消", name: "取消激活客户", auditCode: "iam.tenant.deactivate" },
  { origin: "tenant", operation: "客户", type: "变更", name: "变更客户拥有人", auditCode: "iam.tenant.change_owner" },
  // { origin: "", operation: "密码策略", type: "保存", name: "保存用户密码策略", auditCode: "iam.user_pwd_strategy.save" },
  { origin: "role_auth", operation: "权限配置", type: "权限配置", name: "告警分类", auditCode: "iam.role_data_auth.save.cmdb.alert.classification" },
  { origin: "role_auth", operation: "权限配置", type: "权限配置", name: "设备类型", auditCode: "iam.role_data_auth.save.cmdb.resource.type" },
  { origin: "role_auth", operation: "权限配置", type: "权限配置", name: "设备组", auditCode: "iam.role_data_auth.save.cmdb.resource.group" },
  { origin: "role_auth", operation: "权限配置", type: "权限配置", name: "设备", auditCode: "iam.role_data_auth.save.cmdb.resource" },
  { origin: "role_auth", operation: "权限配置", type: "权限配置", name: "区域", auditCode: "iam.role_data_auth.save.cmdb.region" },
  { origin: "role_auth", operation: "权限配置", type: "权限配置", name: "场所", auditCode: "iam.role_data_auth.save.cmdb.location" },
  { origin: "role_auth", operation: "权限配置", type: "权限配置", name: "联系人", auditCode: "iam.role_data_auth.save.cmdb.contact" },
  { origin: "role_auth", operation: "权限配置", type: "权限配置", name: "供应商", auditCode: "iam.role_data_auth.save.cmdb.vendor" },
  { origin: "tenant_pwd_strategy", operation: "客户", type: "更新", name: "客户", auditCode: "iam.tenant_pwd_strategy.save" },
  { origin: "tenant_pwd_strategy", operation: "用户", type: "更新", name: "用户", auditCode: "iam.user_pwd_strategy.save" },
  { origin: "resource", operation: "设备文件", type: "删除", name: "删除文件", auditCode: "cmdb.resource_file:delete" },
  { origin: "resource", operation: "设备文件", type: "上传", name: "上传文件", auditCode: "cmdb.resource_file:create" },
  { origin: "resource", operation: "设备文件", type: "上传", name: "上传文件", auditCode: "cmdb.file:upload" },
  { origin: "resource", operation: "设备文件", type: "上传", name: "多文件上传", auditCode: "cmdb.file:multi:upload" },
  { origin: "resource", operation: "日志文件", type: "新增", name: "上传资源日志", auditCode: "cmdb.resource_log:create" },
  { origin: "resource", operation: "日志文件", type: "更新", name: "更新资源日志", auditCode: "cmdb.resource_log:update" },
  { origin: "resource", operation: "日志文件", type: "删除", name: "删除资源日志", auditCode: "cmdb.resource_log:delete" },
  { origin: "resource", operation: "日志文件", type: "删除", name: "删除资源日志附件", auditCode: "cmdb.resource_log:deleteAttachment" },
  { origin: "resource", operation: "日志文件", type: "更新", name: "保存资源日志附件", auditCode: "cmdb.resource_log:saveAttachment" },
  { origin: "resource", operation: "SLA服务", type: "绑定", name: "设备关联sla", auditCode: "ec.sla.device.relation" },
  { origin: "resource", operation: "SLA服务", type: "解绑", name: "移除设备关联sla", auditCode: "ec.sla.device.remove" },
  { origin: "auto_event_config", operation: "告警归并配置", type: "更新", name: "保存", auditCode: "ec:auto_event_config:save" },
  { origin: "resource", operation: "设备", type: "移除", name: "取消资源联系人", auditCode: "cmdb.resource.unassignContact" },
  { origin: "location", operation: "场所", type: "移除", name: "取消场所联系人", auditCode: "cmdb.location.unassignContact" },
  { origin: "region", operation: "区域", type: "移除", name: "取消区域联系人", auditCode: "cmdb.region.unassignContact" },
  { origin: "location", operation: "场所", type: "分配", name: "为场所分配联系人", auditCode: "cmdb.location.assignContact" },
  { origin: "region", operation: "区域", type: "分配", name: "为区域分配联系人", auditCode: "cmdb.region.assignContact" },
  { origin: "resource", operation: "设备", type: "分配", name: "为资源分配联系人", auditCode: "cmdb.resource.assignContact" },
  { origin: "tenant", operation: "权限配置", type: "权限配置", name: "变更租户权限", auditCode: "iam:tenant:authChange" },
  { origin: "tenant", operation: "客户", type: "分配", name: "变更客户拥有人", auditCode: "iam.tenant.change_owner" },

  { origin: "device_group", operation: "设备分组", type: "分配", name: "设备分组分配设备", auditCode: "cmdb.resourceGroup.assignResource" },
  { origin: "device_group", operation: "设备分组", type: "移除", name: "设备分组移除设备", auditCode: "cmdb.resourceGroup.cancelResource" },

  { origin: "vendor", operation: "供应商", type: "分配", name: "供应商分配设备", auditCode: "cmdb.vendor.addRelationResource" },
  { origin: "vendor", operation: "供应商", type: "移除", name: "供应商移除分配设备", auditCode: "cmdb.vendor.removeRelationResource" },
  { origin: "alert_classification", operation: "告警分类", type: "分配", name: "告警分类分配设备", auditCode: "cmdb.alert_classification.assignDevice" },
  { origin: "alert_classification", operation: "告警分类", type: "移除", name: "告警分类移除分配设备", auditCode: "cmdb.alert_classification.cancelDevice" },
  { origin: "alert_classification", operation: "告警分类", type: "分配", name: "告警分类分配设备类型", auditCode: "cmdb.resourceType.assignType" },
  { origin: "alert_classification", operation: "告警分类", type: "移除", name: "告警分类移除分配设备类型", auditCode: "cmdb.resourceType.cancelType" },
  { origin: "alert_classification", operation: "告警分类", type: "分配", name: "告警分类分配设备分组", auditCode: "cmdb.alert_classification.assignGroup" },
  { origin: "alert_classification", operation: "告警分类", type: "移除", name: "告警分类移除分配设备分组", auditCode: "cmdb.alert_classification.cancelGroup" },
  { origin: "role", operation: "角色", type: "分配", name: "角色分配用户", auditCode: "iam.current_user.assignUser" },
  { origin: "role", operation: "角色", type: "移除", name: "角色移除用户", auditCode: "iam.current_user.cancelUser" },
  { origin: "role", operation: "角色", type: "分配", name: "角色分配用户组", auditCode: "iam.current_user.assignUserGroup" },
  { origin: "role", operation: "角色", type: "移除", name: "角色移除用户组", auditCode: "iam.current_user.cancelUserGroup" },
  { origin: "resource_type", operation: "设备类型", type: "分配", name: "设备类型分配设备", auditCode: "cmdb.resourceType.assignDevice" },
  { origin: "resource_type", operation: "设备类型", type: "移除", name: "设备类型分配设备", auditCode: "cmdb.resourceType.cancelDevice" },
  { origin: "degrade_roletion", operation: "告警降级", type: "移除", name: "告警降级移除设备", auditCode: "ec.degrade.removeDevice" },
  { origin: "degrade_roletion", operation: "告警降级", type: "分配", name: "告警降级分配设备", auditCode: "ec.degrade.assignDevice" },
  { origin: "degrade_roletion", operation: "告警降级", type: "移除", name: "告警降级移除场所", auditCode: "ec.degrade.removeLocation" },
  { origin: "degrade_roletion", operation: "告警降级", type: "分配", name: "告警降级分配场所", auditCode: "ec.degrade.assignLocation" },
  { origin: "degrade_roletion", operation: "告警降级", type: "移除", name: "告警降级移除区域", auditCode: "ec.degrade.removeRegion" },
  { origin: "degrade_roletion", operation: "告警降级", type: "分配", name: "告警降级分配区域", auditCode: "ec.degrade.assignRegion" },

  { origin: "support_roletion", operation: "行动策略", type: "移除", name: "行动策略移除设备", auditCode: "cmdb.support_note.device:edit" },
  { origin: "support_roletion", operation: "行动策略", type: "分配", name: "行动策略分配设备", auditCode: "cmdb.support_note.device:add" },

  { origin: "support_roletion", operation: "行动策略", type: "移除", name: "行动策略移除场所", auditCode: "cmdb.support_note.location:remove" },
  { origin: "support_roletion", operation: "行动策略", type: "分配", name: "行动策略分配场所", auditCode: "cmdb.support_note.location:add" },

  { origin: "support_roletion", operation: "行动策略", type: "移除", name: "行动策略移除区域", auditCode: "cmdb.support_note.region:remove" },
  { origin: "support_roletion", operation: "行动策略", type: "分配", name: "行动策略分配区域", auditCode: "cmdb.support_note.region:add" },
  { origin: "support_roletion", operation: "行动策略", type: "移除", name: "行动策略移除客户", auditCode: "cmdb.support_note.global:remove_tenants" },
  { origin: "support_roletion", operation: "行动策略", type: "分配", name: "行动策略分配客户", auditCode: "cmdb.support_note.global:assign_tenants" },

  { origin: "secure_container", operation: "安全容器", type: "创建", name: "创建安全容器", auditCode: "iam:security_container:create" },
  { origin: "secure_container", operation: "安全容器", type: "重命名", name: "重命名安全容器", auditCode: "iam:security_container:rename" },
  { origin: "secure_container", operation: "安全容器", type: "移动", name: "移动安全容器", auditCode: "iam:security_container:move" },
  { origin: "secure_container", operation: "安全容器", type: "删除", name: "删除安全容器", auditCode: "iam:security_container:delete" },

  { origin: "secure_container", operation: "安全容器", type: "移除", name: "移除用户组", auditCode: "iam:sc:unassign_user_group" },
  { origin: "secure_container", operation: "安全容器", type: "移动", name: "移动用户到指定安全容器", auditCode: "iam:user:move_container" },
  { origin: "secure_container", operation: "安全容器", type: "移动", name: "移动用户组安全容器", auditCode: "iam:user_group:move_container" },
  { origin: "secure_container", operation: "安全容器", type: "移动", name: "移动安全容器", auditCode: "iam:tenant:move_container" },

  { origin: "secure_container", operation: "安全容器", type: "移动", name: "移动设备", auditCode: "cmdb.resource:move_container" },
  { origin: "secure_container", operation: "安全容器", type: "移动", name: "移动区域", auditCode: "cmdb.region:move_container" },
  { origin: "secure_container", operation: "安全容器", type: "移动", name: "移动场所", auditCode: "cmdb.location:move_container" },
  { origin: "secure_container", operation: "安全容器", type: "移动", name: "移动联系人", auditCode: "cmdb.contact:move_container" },
  { origin: "secure_container", operation: "安全容器", type: "移动", name: "移动设备分组", auditCode: "cmdb.group:move_container" },
  { origin: "secure_container", operation: "安全容器", type: "移动", name: "移动设备类型", auditCode: "cmdb.resource_type:move_container" },
  { origin: "secure_container", operation: "安全容器", type: "移动", name: "移动行动策略", auditCode: "cmdb.support_note:move_container" },
  { origin: "secure_container", operation: "安全容器", type: "移动", name: "移动SLA", auditCode: "ec.sla.move_container" },
  { origin: "secure_container", operation: "安全容器", type: "移动", name: "移动供应商", auditCode: "cmdb.vendor:move_container" },
];

export const contactsType = {
  Notification: "通知联系人",
  Technical: "技术联系人",
  OnSite: "现场联系人",
};
// iam.role_data_auth.save.classification 告警分类
// iam.role_data_auth.save.resource.type 设备类型
// iam.role_data_auth.save.resource.group 设备组
// iam.role_data_auth.save.resource 设备
// iam.role_data_auth.save.region 区域
// iam.role_data_auth.save.location 场所
// iam.role_data_auth.save.contact 联系人
// iam.role_data_auth.save.vendor 供应商

//服务编号 number
//区域 label
//设备
// if (Object.keys(originalValue).length > 0) {
//   return h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: resourceTypeOption.find((v) => v.value === value)?.label || "" }, () => h("div", [h("p", {}, changedValue.name), h("p", { style: "color:#db3328" }, "(" + originalValue.name + ")"), h("p", `${(_row.tenant || {}).name}[${(_row.tenant || {}).abbreviation}]`)]))]);
// } else {
//   return h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: resourceTypeOption.find((v) => v.value === value)?.label || "" }, () => h("div", [h("p", {}, changedValue.name), h("p", `${(_row.tenant || {}).name}[${(_row.tenant || {}).abbreviation}]`)]))]);
// }

//sla ruleName
