import { SERVER, Method, type Response, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";
import type { UploadRequestOptions } from "element-plus";
import getUserInfo from "@/utils/getUserInfo";
import { useSiteConfig } from "@/stores/siteConfig";
import { isNil } from "lodash-es";

interface FileResult {
  /** 文件名 */
  name: string;
  /** 文件大小, 单位: kb */
  size: /* Integer */ string;
  /** 文件路径 */
  filePath: string;
  /** 访问url */
  url: string;
}
export function requestBatchUploadFile(option: UploadRequestOptions) {
  const xhr = new XMLHttpRequest();
  xhr.responseType = "json";
  xhr.withCredentials = option.withCredentials;
  const action = request.getUri({ url: option.action });

  xhr.upload.addEventListener("progress", (event) => option.onProgress(Object.assign(event, { percent: event.total > 0 ? (event.loaded / event.total) * 100 : 0 })));

  const formData = new FormData();
  if (option.data) {
    for (const [key, value] of Object.entries(option.data)) {
      if (value instanceof Array) {
        for (let i = 0; i < value.length; i++) formData.append(key, value[i]);
      } else {
        formData.append(key, value);
      }
    }
  }
  formData.append(option.filename, option.file, option.file.name);

  xhr.addEventListener("error", () => option.onError(Object.assign(new Error(xhr.response ? `${xhr.response.error || xhr.response}` : `fail to ${option.method} ${action} ${xhr.status}`), { status: xhr.status, method: option.method, url: action })));

  xhr.addEventListener("load", () => {
    if (xhr.status < 200 || xhr.status >= 300) option.onError(Object.assign(new Error(xhr.response ? `${xhr.response.error || xhr.response}` : `fail to ${option.method} ${action} ${xhr.status}`), { status: xhr.status, method: option.method, url: action }));
    else {
      const { success, message, data } = xhr.response || {};
      if (success) option.onSuccess(data as FileResult);
      else option.onError(Object.assign(new Error(message ? message : xhr.response ? `${xhr.response.error || xhr.response}` : `fail to ${option.method} ${action} ${xhr.status}`), { status: xhr.status, method: option.method, url: action }));
    }
  });

  xhr.open(option.method, action, true);

  const headers = option.headers || {};
  const info = getUserInfo();
  const siteConfig = useSiteConfig();
  const baseGuid = `${Date.now().toString(36).padStart(8, "x")}-xxxx-4xxx-yxxx-xxxxxxxxxxxx`;
  const guidCreate = (unit: string) => (unit === "x" ? (Math.random() * 16) | 0 : (((Math.random() * 16) | 0) & 0x3) | 0x8).toString(16);
  xhr.setRequestHeader("x-ideal-request-sequence", baseGuid.replace(/[xy]/g, guidCreate));
  xhr.setRequestHeader("x-ideal-request-timestamp", `${Date.now()}`);
  if (siteConfig.baseInfo) {
    xhr.setRequestHeader("x-auth-client-token", siteConfig.baseInfo.auth);
    xhr.setRequestHeader("x-ideal-app-id", siteConfig.baseInfo.app);
  }
  if (info) {
    xhr.setRequestHeader("x-tenant-id", info.currentTenantId);
    xhr.setRequestHeader("Authorization", info.token);
  }
  if (headers instanceof Headers) {
    headers.forEach((value, key) => xhr.setRequestHeader(key, value));
  } else {
    for (const [key, value] of Object.entries(headers)) {
      if (isNil(value)) continue;
      xhr.setRequestHeader(key, String(value));
    }
  }

  xhr.send(formData);
  return xhr;
}
