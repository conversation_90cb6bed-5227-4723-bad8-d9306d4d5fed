import { SERVER, Method, type Response, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

/**
 * @description 权限目录
 */
export interface PermissionCatalogItem {
  /** 主键 */
  id: string;
  /** 父ID */
  parentId?: string;
  /** 所属应用ID */
  appId: string;
  /** 名称 */
  name: string;
  /** 配置信息 */
  config: string;
  /** 排序 */
  orderNum: number;
  /** 是否启用 */
  enabled: boolean;
  /* 子集 */
  children: PermissionCatalogItem[];
  /** 乐观锁版本号 */
  version: string;
  /** 创建时间 */
  createdTime: string;
  /** 更新时间 */
  updatedTime: string;
}
/**
 * @description 获取权限目录列表
 * @url http://*************:3000/project/11/interface/api/2823
 */
export function getPermissionCatalog(req: { appId: string /* 应用ID, 必填 */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_catalogs`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams({ appId: req.appId /* 应用ID, 必填 */ });
        return $req;
      })
      .then(($req) => request<never, Response<PermissionCatalogItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 新增权限目录
 * @url http://*************:3000/project/11/interface/api/2815
 */
export function addPermissionCatalog(req: { parentId?: /** 父目录ID */ string; appId: /** 所属应用ID */ string; name: /** 名称 */ string; orderNum: /** 排序值 */ number; enabled: /** 是否启用, 默认是 */ boolean; config: /** 配置信息 */ string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_catalogs`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = { parentId: req.parentId, appId: req.appId, name: req.name, orderNum: req.orderNum, enabled: req.enabled, config: req.config };
        return $req;
      })
      .then(($req) => request<never, Response<PermissionCatalogItem>>($req)),
    { controller }
  );
}
/**
 * @description 更新权限目录
 * @url http://*************:3000/project/11/interface/api/2817
 */
export function modPermissionCatalog(req: Partial<{ parentId?: /** 父目录ID, 传-1代表没有父目录 */ string; name: /** 名称 */ string; orderNum: /** 排序值 */ number; enabled: /** 是否启用, 默认是 */ boolean; config: /** 配置信息 */ string }> & { id: /* 权限目录id */ string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_catalogs/${req.id /* 权限目录id */}`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = { parentId: req.parentId, name: req.name, orderNum: req.orderNum, enabled: req.enabled, config: req.config };
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}
/**
 * @description 删除权限目录
 * @url http://*************:3000/project/11/interface/api/2821
 */
export function delPermissionCatalog(req: { ids: string[] /* id列表 使用,分割 */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_catalogs/${req.ids.join(",")}`, method: Method.Delete, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}
// setTemplatePermissionBySlot
/**
 * @description 批量变更权限目录排序
 * @url http://*************:3000/project/11/interface/api/2819
 */
export function setPermissionCatalogBySlot(req: { slot: { id: PermissionCatalogItem["id"]; order: PermissionCatalogItem["orderNum"] }[] }) {
  if (!req.slot.length) return Promise.resolve<Response<null>>({ success: true, message: "success", data: null, page: 1, size: 20, total: NaN });
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_catalogs/batch_update_order`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = req.slot.reduce((p, c) => Object.assign(p, { [c.id]: c.order }), {});
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}

/**
 * @description 配置分组
 * @url http://*************:3000/project/11/interface/api/15506
 */
export interface PermissionGroupItem {
  id: string;
  appId: string;
  name: string;
  orderNum: number;
  enabled: boolean;
}
/**
 * @description 获取应用下配置分组列表
 * @url http://*************:3000/project/11/interface/api/15506
 */
export function getPermissionGroup(req: { appId: string /* 应用ID */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_groups`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams({ appId: req.appId /* 应用ID */ });
        return $req;
      })
      .then(($req) => request<never, Response<PermissionGroupItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 新增权限配置组
 * @url http://*************:3000/project/11/interface/api/15478
 */
export function addPermissionGroup(req: { appId: /** 所属应用ID */ string; name: /** 名称 */ string; orderNum: /** 排序值 */ number; enabled: /** 是否启用, 默认是 */ boolean }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_groups`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = { appId: req.appId, name: req.name, orderNum: req.orderNum, enabled: req.enabled };
        return $req;
      })
      .then(($req) => request<never, Response<PermissionGroupItem>>($req)),
    { controller }
  );
}
/**
 * @description 选择性更新权限配置组
 * @url http://*************:3000/project/11/interface/api/15485
 */
export function modPermissionGroup(req: Partial<{ name: /** 名称 */ string; orderNum: /** 排序值 */ number; enabled: /** 是否启用, 默认是 */ boolean }> & { id: /* 配置组ID */ string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_groups/${req.id /* 配置组ID */}`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = { name: req.name, orderNum: req.orderNum, enabled: req.enabled };
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}
/**
 * @description 删除配置分组
 * @url http://*************:3000/project/11/interface/api/15499
 */
export function delPermissionGroup(req: { id: string /* 分组ID */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_groups/${req.id}`, method: Method.Delete, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}
/**
 * @description 批量更新分组排序
 * @url http://*************:3000/project/11/interface/api/15492
 */
export function setPermissionGroupBySlot(req: { slot: { id: PermissionGroupItem["id"]; order: PermissionGroupItem["orderNum"] }[] }) {
  if (!req.slot.length) return Promise.resolve<Response<null>>({ success: true, message: "success", data: null, page: 1, size: 20, total: NaN });
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_groups/batch_update_order`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = req.slot.reduce((p, c) => Object.assign(p, { [c.id]: c.order }), {});
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}

/**
 * @description 配置项
 */
export interface PermissionOptionItem {
  id: string;
  appId: string;
  groupId: string;
  name: string;
  orderNum: number;
  enabled: boolean;
}
/**
 * @description 获取配置项列表
 * @url http://*************:3000/project/11/interface/api/15541
 */
export function getPermissionOption(req: { appId: string /* 应用ID */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_items`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams({ appId: req.appId /* 应用ID */ });
        return $req;
      })
      .then(($req) => request<never, Response<PermissionOptionItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 新增配置项
 * @url http://*************:3000/project/11/interface/api/15513
 */
export function addPermissionOption(req: { groupId: /** 所属应用ID */ string; name: /** 名称 */ string; orderNum: /** 排序值 */ number; enabled: /** 是否启用, 默认是 */ boolean }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_items`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = { groupId: req.groupId, name: req.name, orderNum: req.orderNum, enabled: req.enabled };
        return $req;
      })
      .then(($req) => request<never, Response<PermissionOptionItem>>($req)),
    { controller }
  );
}
/**
 * @description 选择性更新配置项
 * @url http://*************:3000/project/11/interface/api/15520
 */
export function modPermissionOption(req: Partial<{ name: /** 名称 */ string; orderNum: /** 排序值 */ /* Integer */ number; enabled: /** 是否启用, 默认是 */ boolean }> & { id: /* 配置项ID */ string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_items/${req.id /* 配置项ID */}`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = { name: req.name, orderNum: req.orderNum, enabled: req.enabled };
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}
/**
 * @description 删除配置项
 * @url http://*************:3000/project/11/interface/api/15534
 */
export function delPermissionOption(req: { id: string /* 配置项ID */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_items/${req.id}`, method: Method.Delete, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}
/**
 * @description 批量更新配置项排序
 * @url http://*************:3000/project/11/interface/api/15527
 */
export function setPermissionOptionBySlot(req: { slot: { id: PermissionOptionItem["id"]; order: PermissionOptionItem["orderNum"] }[] }) {
  if (!req.slot.length) return Promise.resolve<Response<null>>({ success: true, message: "success", data: null, page: 1, size: 20, total: NaN });
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_items/batch_update_order`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = req.slot.reduce((p, c) => Object.assign(p, { [c.id]: c.order }), {});
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}

/**
 * @description 权限
 */
export interface PermissionAuthItem {
  /** 主键 */
  id: string;
  /** 所属应用ID */
  appId: string;
  /** 所属目录ID */
  catalogId: string;
  /** 配置组ID */
  groupId: string;
  /** 配置项ID */
  itemId: string;
  /** 配置项名称 */
  itemName: string;
  /** 配置类型 */
  type: /* 枚举: GROUP :权限组 | ITEM :权限项 */ "GROUP" | "ITEM";
  /** 名称 */
  name: string;
  /** 标识 */
  ident: string;
  /** api pattern清单 */
  apiPatterns: string[];
  /** 明确的api接口地址清单 */
  specificApis: string[];
  /** 权限标识列表 */
  authorities: string[];
  /** 排序 */
  orderNum: number;
  /** 是否启用 */
  enabled: boolean;
  /** 是否拥有配置项内所有权限 */
  allInItem: boolean;
  /** 是否拥有所在配置项安全权限 */
  itemSecurity: boolean;
  /** 是否拥有所在配置组安全权限 */
  groupSecurity: boolean;
  /** 子权限列表 */
  childIds: string[];
  /** 创建时间 */
  createdTime: string;
  /** 更新时间 */
  updatedTime: string;
}
/**
 * @description 获取前端权限列表
 * @url http://*************:3000/project/11/interface/api/2833
 */
export function getPermissionAuth(req: { appId: string /* 应用ID, 必填 */; catalogId?: string; groupId?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permissions`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams({ appId: req.appId /* 应用ID, 必填 */, ...(req.catalogId ? { catalogId: req.catalogId } : {}), ...(req.groupId ? { groupId: req.groupId } : {}) });
        return $req;
      })
      .then(($req) => request<never, Response<PermissionAuthItem[]>>($req))
      .then(($res) => ({ ...$res, data: ($res.data instanceof Array ? $res.data : []).map((v) => ({ ...v, apiPatterns: v.apiPatterns instanceof Array ? v.apiPatterns : [], specificApis: v.specificApis instanceof Array ? v.specificApis : [], authorities: v.authorities instanceof Array ? v.authorities : [], childIds: v.childIds instanceof Array ? v.childIds : [] })) })),
    { controller }
  );
}
/**
 * @description 新增前端权限
 * @url http://*************:3000/project/11/interface/api/2825
 */
export function addPermissionAuth(req: { appId: /** 应用ID */ string; catalogId?: /** 权限目录ID */ string; itemId?: /** 配置项ID */ string; type: /** 配置类型 */ "GROUP" | "ITEM"; name: /** 名称 */ string; ident: /** 唯一标识符, 应用下唯一 */ string; apis: /** 访问的接口列表 */ string[]; authorities: /** 权限标识列表 */ string[]; orderNum: /** 排序值 */ number; enabled: /** 是否启用 */ boolean; allInItem: /** 是否拥有配置项内所有权限 */ boolean; itemSecurity: /** 是否拥有所在配置项安全权限 */ boolean; groupSecurity: /** 是否拥有所在配置组安全权限 */ boolean; childIds: /** 子权限id列表 */ string[]; tenantLevel: boolean }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permissions`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = { appId: req.appId, catalogId: req.catalogId, itemId: req.itemId, type: req.type, name: req.name, ident: req.ident, apis: req.apis, authorities: req.authorities, orderNum: req.orderNum, enabled: req.enabled, allInItem: req.allInItem, itemSecurity: req.itemSecurity, groupSecurity: req.groupSecurity, childIds: req.childIds, tenantLevel: req.tenantLevel };
        return $req;
      })
      .then(($req) => request<never, Response<PermissionAuthItem>>($req)),
    { controller }
  );
}
/**
 * @description 更新前端权限
 * @url http://*************:3000/project/11/interface/api/2827
 */
export function modPermissionAuth(req: Partial<{ catalogId?: /** 服务目录id */ string; itemId?: /** 权限配置项ID */ string; type: /** 配置类型 */ "GROUP" | "ITEM"; name: /** 名称 */ string; ident: /** 唯一标识符, 应用下唯一 */ string; apis: /** 接口地址列表 */ string[]; authorities: /** 权限标识列表 */ string[]; orderNum: /** 排序值 */ number; enabled: /** 是否启用 */ boolean; allInItem: /** 是否拥有配置项内所有权限 */ boolean; itemSecurity: /** 是否拥有所在配置项安全权限 */ boolean; groupSecurity: /** 是否拥有所在配置组安全权限 */ boolean; childIds: /** 子权限id列表 */ string[]; tenantLevel: boolean }> & { id: /* 权限id */ string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permissions/${req.id /* 权限id */}`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = { catalogId: req.catalogId, itemId: req.itemId, type: req.type, name: req.name, ident: req.ident, apis: req.apis, authorities: req.authorities, orderNum: req.orderNum, enabled: req.enabled, allInItem: req.allInItem, itemSecurity: req.itemSecurity, groupSecurity: req.groupSecurity, childIds: req.childIds, tenantLevel: req.tenantLevel };
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}
/**
 * @description 删除前端权限
 * @url http://*************:3000/project/11/interface/api/2831
 */
export function delPermissionAuth(req: { ids: string[] /* id列表 使用,分割 */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permissions/${req.ids.join(",")}`, method: Method.Delete, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}
/**
 * @description 批量变更权限排序
 * @url http://*************:3000/project/11/interface/api/2829
 */
export function setPermissionAuthBySlot(req: { slot: { id: PermissionAuthItem["id"]; order: PermissionAuthItem["orderNum"] }[] }) {
  if (!req.slot.length) return Promise.resolve<Response<null>>({ success: true, message: "success", data: null, page: 1, size: 20, total: NaN });
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permissions/batch_update_order`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = req.slot.reduce((p, c) => Object.assign(p, { [c.id]: c.order }), {});
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}

/**
 * @description 配置模板
 */
export interface PermissionTemplateItem {
  id: string;
  appId: string;
  /** 权限配置组ID */
  groupId: string;
  /** 模板名称 */
  name: string;
  /** 排序 */
  orderNum: number;
  /** 权限ID列表 */
  permissionIds: string[];
}
/**
 * @description 获取配置模板列表
 * @url http://*************:3000/project/11/interface/api/15611
 */
export function getPermissionTemplate(req: { appId: string /* 应用ID */; groupId?: string /* 分组ID */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_templates`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ appId: req.appId, groupId: req.groupId }, $req.params);
        return $req;
      })
      .then(($req) => request<never, Response<PermissionTemplateItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 新建权限配置模板
 * @url http://*************:3000/project/11/interface/api/15583
 */
export function addPermissionTemplate(req: { groupId: /** 权限配置组ID */ string; name: /** 模板名称 */ string; orderNum?: /** 排序 */ number; permissionIds: /** 权限ID列表 */ string[] }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_templates`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        $req.data = { groupId: req.groupId, name: req.name, orderNum: req.orderNum, permissionIds: req.permissionIds instanceof Array ? req.permissionIds : [] };
        return $req;
      })
      .then(($req) => request<never, Response<PermissionTemplateItem>>($req)),
    { controller }
  );
}
/**
 * @description 选择性更新权限配置模板
 * @url http://*************:3000/project/11/interface/api/15590
 */
export function modPermissionTemplate(req: Partial<{ name?: /** 模板名称 */ string; orderNum?: /** 排序 */ number; permissionIds?: /** 权限ID列表 */ string[] }> & { id: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_templates/${req.id /* 模板ID */}`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = { name: req.name, orderNum: req.orderNum, permissionIds: req.permissionIds };
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}
/**
 * @description 删除配置模板
 * @url http://*************:3000/project/11/interface/api/15604
 */
export function delPermissionTemplate(req: { id: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_templates/${req.id /* 模板ID */}`, method: Method.Delete, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}
/**
 * @description 批量更新权限配置模板排序
 * @url http://*************:3000/project/11/interface/api/15597
 */
export function setPermissionTemplateBySlot(req: { slot: { id: PermissionTemplateItem["id"]; order: PermissionTemplateItem["orderNum"] }[] }) {
  if (!req.slot.length) return Promise.resolve<Response<null>>({ success: true, message: "success", data: null, page: 1, size: 20, total: NaN });
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/front/permission_templates/batch_update_order`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = req.slot.reduce((p, c) => Object.assign(p, { [c.id]: c.order }), {});
        return $req;
      })
      .then(($req) => request<never, Response<null>>($req)),
    { controller }
  );
}
