/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */
import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";
import { useSiteConfig } from "@/stores/siteConfig";
import { appType, appTheme } from "@/api/application";
import type { Zone } from "@/utils/zone";

import { 安全管理中心_用户管理_编辑 } from "@/views/pages/permission";

export enum gender {
  FEMALE = "FEMALE",
  MALE = "MALE",
  SECRET = "SECRET",
}
export const genderOption: { label: string; value: keyof typeof gender }[] = [
  { label: "女性", value: "FEMALE" },
  { label: "男性", value: "MALE" },
  { label: "保密", value: "SECRET" },
];
export interface UserBaseItem {
  platform?: string;

  name: string /* 姓名 */;
  account: string /* 账号 */;
  phone: string /* 手机号码 */;
  email: string /* 邮箱 */;
  gender: keyof typeof gender /* 性别 */;
  language: string /* 语言 */;
  password?: string /* 密码 */;
}
export interface UserItem extends UserBaseItem {
  id: string /* 用户ID */;
  platform: string /* 所属平台 */;
  tenantId: string /* 所属租户 */;
  containerId: string /* 安全容器ID */;
  name: string /* 姓名 */;
  nickname: string /* 昵称 */;
  account: string /* 账号 */;
  phone: string /* 手机号 */;
  email: string /* 邮箱 */;
  language: string /* 语言 */;
  zoneId: keyof typeof Zone /* 时区 */;
  gender: /* 枚举: SECRET :保密 | MALE :男性 | FEMALE :女性 */ keyof typeof gender /* 性别 */;
  profilePicture?: string /* 头像 */;
  mfaState: /* 枚举: ENABLED :启用MFA认证 | DISABLED :禁用MFA认证 | DEFAULT :使用系统默认配置 */ "ENABLED" | "DISABLED" | "DEFAULT" /* 双因素认证启用状态 */;
  passwordTime?: string /* 密码修改时间戳 */;
  busy: boolean /* 是否忙碌 */;
  busyTime?: string /* 进入忙碌状态的时间戳 */;
  improved: boolean /* 是否已完善个人信息 */;
  blocked: boolean /* 是否被锁定 */;
  lastLoginTime?: string /* 最近登录时间 */;
  lastActivity?: string /* 最近访问时间 */;
  createdTime?: string /* 创建时间 */;
  tenantName?: string /* 所属租户名称 */;
  tenantAbbreviation?: string /* 所属租户的缩写 */;
  desensitized: boolean /* 是否已脱敏 */;
  joinTenantTime?: string /* 加入租户的事件, 多租户平台有效 */;
  blockedInTenant?: boolean /* 用户是否被租户冻结, 多租户平台有效 */;
  groups: { id: /** 用户组ID */ string; name: /** 用户组名称 */ string }[];
  accountOverdue: boolean;

  expirationDate: string | number;
}

export enum UserItemScope {
  "ALL" = "所有用户",
  "INTERNAL" = "仅租户内部用户",
  "EXTERNAL" = "仅租户外部用户",
}

export function getUser(data: Partial<Record<"keyword" | "schemas" | "scope", string>> & RequestBase) {
  const params = new URLSearchParams({ pageNumber: String((data.paging || {}).pageNumber || 1), pageSize: String((data.paging || {}).pageSize || 10), permissionId: ["515414964874248192"].join(",") });
  // const params = new URLSearchParams(, ));
  bindSearchParams(
    ["keyword", "schemas", "scope"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params
  );
  bindSearchParams(
    {
      blocked: data.blocked /* 锁定状态 */,
      mfaState: data.mfaState /* 双因素认证 */,

      ...([...(data.includeName instanceof Array ? data.includeName : []), ...(data.excludeName instanceof Array ? data.excludeName : []), ...(data.eqName instanceof Array ? data.eqName : []), ...(data.neName instanceof Array ? data.neName : [])].filter((v) => v).length ? { nameFilterRelation: data.nameFilterRelation === "OR" ? "OR" : "AND", includeName: data.includeName instanceof Array && data.includeName.length ? data.includeName.join(",") : void 0, excludeName: data.excludeName instanceof Array && data.excludeName.length ? data.excludeName.join(",") : void 0, eqName: data.eqName instanceof Array && data.eqName.length ? data.eqName.join(",") : void 0, neName: data.neName instanceof Array && data.neName.length ? data.neName.join(",") : void 0 } : {}),

      ...([...(data.includeAccount instanceof Array ? data.includeAccount : []), ...(data.excludeAccount instanceof Array ? data.excludeAccount : []), ...(data.eqAccount instanceof Array ? data.eqAccount : []), ...(data.neAccount instanceof Array ? data.neAccount : [])].filter((v) => v).length ? { accountFilterRelation: data.accountFilterRelation === "OR" ? "OR" : "AND", includeAccount: data.includeAccount instanceof Array && data.includeAccount.length ? data.includeAccount.join(",") : void 0, excludeAccount: data.excludeAccount instanceof Array && data.excludeAccount.length ? data.excludeAccount.join(",") : void 0, eqAccount: data.eqAccount instanceof Array && data.eqAccount.length ? data.eqAccount.join(",") : void 0, neAccount: data.neAccount instanceof Array && data.neAccount.length ? data.neAccount.join(",") : void 0 } : {}),

      ...([...(data.includePhone instanceof Array ? data.includePhone : []), ...(data.excludePhone instanceof Array ? data.excludePhone : []), ...(data.eqPhone instanceof Array ? data.eqPhone : []), ...(data.nePhone instanceof Array ? data.nePhone : [])].filter((v) => v).length ? { phoneFilterRelation: data.phoneFilterRelation === "OR" ? "OR" : "AND", includePhone: data.includePhone instanceof Array && data.includePhone.length ? data.includePhone.join(",") : void 0, excludePhone: data.excludePhone instanceof Array && data.excludePhone.length ? data.excludePhone.join(",") : void 0, eqPhone: data.eqPhone instanceof Array && data.eqPhone.length ? data.eqPhone.join(",") : void 0, nePhone: data.nePhone instanceof Array && data.nePhone.length ? data.nePhone.join(",") : void 0 } : {}),

      ...([...(data.includeEmail instanceof Array ? data.includeEmail : []), ...(data.excludeEmail instanceof Array ? data.excludeEmail : []), ...(data.eqEmail instanceof Array ? data.eqEmail : []), ...(data.neEmail instanceof Array ? data.neEmail : [])].filter((v) => v).length ? { emailFilterRelation: data.emailFilterRelation === "OR" ? "OR" : "AND", includeEmail: data.includeEmail instanceof Array && data.includeEmail.length ? data.includeEmail.join(",") : void 0, excludeEmail: data.excludeEmail instanceof Array && data.excludeEmail.length ? data.excludeEmail.join(",") : void 0, eqEmail: data.eqEmail instanceof Array && data.eqEmail.length ? data.eqEmail.join(",") : void 0, neEmail: data.neEmail instanceof Array && data.neEmail.length ? data.neEmail.join(",") : void 0 } : {}),
    },
    params
  );

  return request<unknown, Response<UserItem[]>>({
    url: useSiteConfig().multiTenant ? `${SERVER.IAM}/users/current_org/filter` : `${SERVER.IAM}/users/current_platform/desensitized`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params,
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function addUser(data: Partial<UserItem> & RequestBase) {
  return request<unknown, Response<UserItem>>({
    url: useSiteConfig().multiTenant ? `${SERVER.IAM}/users/current_org` : `${SERVER.IAM}/users/current_platform/desensitized`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      changePwdDuringLogin: data.changePwdDuringLogin,
      permissionId: ["515414964874248192"].join(","),
    },
    data: { name: data.name, account: data.account, phone: data.phone, email: data.email, password: data.password, gender: data.gender, birthday: data.birthday, profilePicture: data.profilePicture, zoneId: data.zoneId, userGroupIds: data.userGroupIds, expirationDate: data.expirationDate, blocked: data.blocked },
  });
}
/**
 * @description 查询当前平台下的用户列表(强制脱敏)
 * @url http://*************:3000/project/11/interface/api/2639
 */
export function /* 查询当前平台下的用户列表(强制脱敏) */ getUserByPlatform(req: Partial<Record<"tenantId" | "userId" | "userIds" | "keyword" | "ident" | "pageNumber" | "pageSize" | "sort", string | string[]>> & RequestBase) {
  // const header = new Headers();
  const params = new URLSearchParams({ pageNumber: String((req.paging || {}).pageNumber) /* 页码, 默认第一页 */, pageSize: String((req.paging || {}).pageSize) /* 页大小, 默认10 */ });
  bindSearchParams({ tenantId: req.tenantId /* undefined */, userId: req.userId /* 用户ID, 最高优先级 */, userIds: req.userIds /* 用户ID列表 */, keyword: req.keyword /* 查询关键字, 支持: 账号/邮箱/手机号/姓名/昵称 */, ident: req.ident /* 唯一标识, 如果传了keyword则该条件不生效. 支持: 账号/邮箱/手机号 */, sort: req.sort /* undefined */, permissionId: ["515414964874248192"].join(",") }, params);
  const data = new URLSearchParams({});
  return request<never, Response<UserItem[]>>({ url: `${SERVER.IAM}/users/current_platform/desensitized`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
// export function modUser(data: Partial<UserItem> & RequestBase) {
//   return request<unknown, Response<UserItem>>({
//     // url: useSiteConfig().multiTenant ? `${SERVER.IAM}/tenant/current/users/${data.id}` : `${SERVER.IAM}/platform/current/users/${data.id}`,
//     url: `${SERVER.IAM}/users/id/${data.id}`,
//     method: Method.Patch,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: ["name", "gender", "language"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//   });
// }
/**
 * @description 修改用户信息
 * @url http://*************:3000/project/11/interface/api/2627
 */
export function /* 修改用户信息 */ modUser(req: Required<Record<"id", string>> & { name?: string; nickname?: string; language?: string; gender?: gender; account?: string; phone?: string; email?: string } & Record<string, any> & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({ changePwdDuringLogin: req.changePwdDuringLogin }, params);
  const data = { enableMfaDefault: req.enableMfaDefault /*默认mfa*/, mfaState: req.mfaState /*mfa状态*/, name: req.name /* 姓名 */, nickname: req.nickname /* 昵称 */, language: req.language /* 语言 */, gender: req.gender, /* 性别枚举：SECRET :保密、MALE :男性、FEMALE :女性 */ account: req.account /* 账号 */, phone: req.phone /* 手机号 */, email: req.email /* 邮箱 */, zoneId: req.zoneId /*时区 */, userGroupIds: req.userGroupIds /*用户组 */, expirationDate: req.expirationDate, blocked: req.blocked, password: req.password, changePwdDuringLogin: req.changePwdDuringLogin };

  const containerId = req.containerId;
  return request<never, Response<null>>({
    url: `${SERVER.IAM}/users/id/${req.id /* 用户ID */}`,
    method: Method.Patch,
    responseType: "json",
    headers: {
      "x-permission-id": 安全管理中心_用户管理_编辑,
      "x-container-id": containerId,
    },
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
    params,
    data,
  });
}
/**
 * @description 邀请用户,多租户平台有效
 * @url http://*************:3000/project/11/interface/api/2623
 */
export function /* 邀请用户,多租户平台有效 */ addUserListByInvite(req: { userIds?: string[] } & RequestBase) {
  // const headers = new Headers();
  // headers.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { userIds: req.userIds /* 用户ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.IAM}/users/current_tenant/invite`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , headers */, params, data });
}
export function /* 邀请用户,多租户平台有效 */ delUserListByInvite(req: { userIds?: string[] } & RequestBase) {
  const params = new URLSearchParams({});
  const data = { userIds: req.userIds /* 用户ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.IAM}/users/current_tenant/remove`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , headers */, params, data });
}
/**
 * @description 移除用户,多租户平台有效
 * @url http://*************:3000/project/11/interface/api/2625
 */
export function /* 移除用户,多租户平台有效 */ addApiData(req: { userIds?: string[] } & RequestBase) {
  const params = new URLSearchParams({});
  const data = { userIds: req.userIds /* 用户ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.IAM}/users/current_tenant/remove`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , headers */, params, data });
}
export function getUserSensitiveById(data: { id: string } & RequestBase) {
  return request<unknown, Response<UserItem>>({
    url: `${SERVER.IAM}/users/id/${data.id}/sensitive`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { includeGroups: "true", dataMasking: data.dataMasking || false },
    data: {},
  });
}

export function getUserById(data: { id: string } & RequestBase) {
  return request<unknown, Response<UserItem>>({
    url: `${SERVER.IAM}/platform/current/users/${data.id}/plain`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: new URLSearchParams(),
  });
}
export function cutUserByBlock(data: { id: string; frozenNote?: string; frozenExpire?: string } & RequestBase) {
  return request<unknown, Response<UserItem[]>>({
    // url: useSiteConfig().multiTenant ? `${SERVER.IAM}/tenant/current/users/${data.id}/block` : `${SERVER.IAM}/platform/current/users/${data.id}/block`,
    url: `${SERVER.IAM}/users/id/${data.id}/block`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["frozenNote", "frozenExpire"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function cutUserByActive(data: { id: string } & RequestBase) {
  return request<unknown, Response<UserItem[]>>({
    // url: useSiteConfig().multiTenant ? `${SERVER.IAM}/tenant/current/users/${data.id}/active` : `${SERVER.IAM}/platform/current/users/${data.id}/active`,
    url: `${SERVER.IAM}/users/id/${data.id}/unblock`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function releaseLoginLock(data: { id: string } & RequestBase) {
  return request<unknown, Response<UserItem[]>>({
    // url: useSiteConfig().multiTenant ? `${SERVER.IAM}/tenant/current/users/${data.id}/active` : `${SERVER.IAM}/platform/current/users/${data.id}/active`,
    url: `${SERVER.IAM}/users/${data.id}/release_login_lock`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function cutUserPasswordReset(data: { id: string } & RequestBase) {
  return request<unknown, Response<UserItem[]>>({
    url: `${SERVER.IAM}/users/id/${data.id}/reset_password`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["password", "ptype", "changeDuringLogin"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function getUserByAccountOrEmailOrPhone(data: Partial<Pick<UserItem, "account" | "email" | "phone">> & { platform?: string; [key: string]: unknown }) {
  const response: Promise<Response<UserItem[]>>[] = [];
  if (data.account) {
    response.push(
      request<unknown, Response<UserItem[]>>({
        url: `${SERVER.IAM}/platforms/${data.platform}/users`,
        method: Method.Get,
        responseType: "json",
        signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
        headers: {},
        params: { pageNumber: 1, pageSize: 1, account: data.account },
        data: new URLSearchParams(),
      })
    );
  }
  if (data.email) {
    response.push(
      request<unknown, Response<UserItem[]>>({
        url: `${SERVER.IAM}/platforms/${data.platform}/users`,
        method: Method.Get,
        responseType: "json",
        signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
        headers: {},
        params: { pageNumber: 1, pageSize: 1, email: data.email },
        data: new URLSearchParams(),
      })
    );
  }
  if (data.phone) {
    response.push(
      request<unknown, Response<UserItem[]>>({
        url: `${SERVER.IAM}/platforms/${data.platform}/users`,
        method: Method.Get,
        responseType: "json",
        signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
        headers: {},
        params: { pageNumber: 1, pageSize: 1, phone: data.phone },
        data: new URLSearchParams(),
      })
    );
  }
  return Promise.all(response).then((res) => {
    return res.reduce<UserItem[]>((p, { success, data }) => {
      if (success) p.push(...(data instanceof Array ? data : []));
      return p;
    }, []);
  });
}

/* TODO: 角色 */
export interface RoleItem {
  id?: string /* 主键 */;
  appId?: string /* 应用ID */;
  name?: string /* 角色名称 */;
  note?: string /* 备注 */;
  global: boolean /* 是否全局角色 */;
  basic: boolean /* 是否基础角色 */;
  superAdmin: boolean /* 是否管理员角色 */;
  assignableRoleIds?: /* 可分配的角色ID列表 */ string[];
  version: string /* 版本号 */;
  createdTime: string /* 创建事件 */;
  updatedTime: string /* 更新时间 */;
  createdBy?: string /* 创建人信息 */;
  updatedBy?: string /* 更新人信息 */;
}
/**
 * @description 获取组织下角色列表
 * @url http://*************:3000/project/11/interface/api/2871
 */
export function /* 获取组织下角色列表 */ getRole(req: Partial<Record<"appId", string>> & RequestBase) {
  // const header = new Headers();
  const params = new URLSearchParams({});
  bindSearchParams({ appId: req.appId /* 应用ID, 指定应用下的角色列表, 不指定则返回组织下所有角色列表 */ }, params);
  const data = new URLSearchParams({});
  return request<never, Response<RoleItem[]>>({ url: `${SERVER.IAM}/roles/current_org`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

/**
 * @description 新增角色
 * @url http://*************:3000/project/11/interface/api/2853
 */
export function /* 新增角色 */ addRole(req: { appId: string; name: string; note?: string } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { appId: req.appId /* 前端应用ID */, name: req.name /* 名称 */, note: req.note /* 备注 */ };
  return request<never, Response<RoleItem>>({ url: `${SERVER.IAM}/roles/current_org`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function modRole(data: Partial<RoleItem> & RequestBase) {
  return request<unknown, Response<RoleItem>>({
    url: `${SERVER.IAM}/roles/id/${data.id}`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "note", "appId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function delRole(data: Partial<RoleItem> & RequestBase) {
  return request<unknown, Response<RoleItem>>({
    url: `${SERVER.IAM}/roles/id/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

/**
 * @description 获取当前用户可分配的角色列表
 * @url http://*************:3000/project/11/interface/api/2885
 */
export function /* 获取当前用户可分配的角色列表 */ getRoleByUsable(req: Partial<Record<"appId", string>> & RequestBase) {
  // const header = new Headers();
  const params = new URLSearchParams({});
  bindSearchParams({ appId: req.appId /* 应用ID, 为空代表全部 */ }, params);
  const data = new URLSearchParams({});
  return request<never, Response<RoleItem[]>>({ url: `${SERVER.IAM}/current_user/roles/assignable`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
export function getUserByRole /* 获取角色下的用户列表 */(data: { id: string } & RequestBase) {
  return request<unknown, Response<UserItem[]>>({
    url: `${SERVER.IAM}/current_org/roles/id/${data.id}/users/detail`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function getRoleByUser /* 获取用户角色列表 */(data: { id: string; appId: string } & RequestBase) {
  return request<unknown, Response<RoleItem[]>>({
    url: `${SERVER.IAM}/current_org/users/id/${data.id}/roles`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["appId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
/**
 * @description 批量为用户分配角色
 * @url http://*************:3000/project/11/interface/api/2887
 */
export function /* 批量为用户分配角色 */ setUserByRole(req: Partial<Record<"appId", string>> & { userIds: string[]; roleIds?: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({ appId: req.appId /* 应用ID,为空代表全部 */ }, params);
  const data = { userIds: req.userIds /* 用户ID列表 */, roleIds: req.roleIds /* 角色ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.IAM}/current_org/roles/assign_user_role`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function getGroupByRole /* 获取角色下的用户组列表 */(data: { id: string } & RequestBase) {
  return request<unknown, Response<GroupItem[]>>({
    url: `${SERVER.IAM}/current_org/roles/id/${data.id}/user_groups`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function getRoleByGroup /* 获取用户组角色列表 */(data: { id: string; appId: string } & RequestBase) {
  return request<unknown, Response<RoleItem[]>>({
    url: `${SERVER.IAM}/current_org/user_groups/id/${data.id /* 用户组ID */}/roles`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["appId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
/**
 * @description 为用户组分配角色
 * @url http://*************:3000/project/11/interface/api/2893
 */
export function /* 为用户组分配角色 */ setGroupByRole(req: Partial<Record<"appId", string>> & { userGroupIds: string[]; roleIds?: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({ appId: req.appId /* 应用ID, 为空代表全部 */ }, params);
  const data = { userGroupIds: req.userGroupIds /* 用户ID列表 */, roleIds: req.roleIds /* 角色ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.IAM}/current_org/roles/assign_user_group_role`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

// 角色权限

export interface Catalogs {
  id: string;
  parentId: string;
  appId: string;
  name: string;
  config: string;
  enabled: boolean;
  version: string;
  orderNum: number;
  createdTime: string;
  updatedTime: string;
  children: Catalogs[];
  permissions: Permissions[];
}
export interface Permissions {
  id: string;
  appId: string;
  catalogId: string;
  orderNum: number;
  name: string;
  enabled: boolean;
}
export interface DataAuthItem {
  roleId: string /* 角色id */;
  dataType: string /* 数据类型 */;
  authValue: string /* 权限信息 */;
  version: string /* 乐观锁版本号 */;
  createdTime: string /* 创建时间 */;
  updatedTime: string /* 更新时间 */;
  createdBy: string /* 创建人信息 */;
  updatedBy: string /* 最后更新人信息 */;
}
export interface PlatformPower {
  catalogs: Catalogs[];
  permissions: Permissions[];
  assignedPermissionIds: string[];
}
export interface PlatformPowerItem {
  id: string;
  parentId: string;
  appId: string;
  name: string;
  config: string;
  enabled: boolean;
  version: string;
  createdTime: string;
  updatedTime: string;
  children: PlatformPowerItem[] | Permissions[];
}

export interface Permissions {
  id: string;
  appId: string;
  catalogId: string;
  name: string;
  enabled: boolean;
}

export function cutRoleBasicAuthority(data: Partial<RoleItem> & RequestBase) {
  return request<never, Response<RoleItem>>({
    url: `${SERVER.IAM}/roles/id/${data.id}/${data.basic ? "set_as_basic" : "cancel_basic"}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

export function getRolePermission(data: { id: string } & RequestBase) {
  return request<never, Response<PlatformPower>>({
    url: `${SERVER.IAM}/roles/id/${data.id}/permission_assign_view`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function setRolePermission(data: { id: string; permissionIds: string[] } & RequestBase) {
  return request<never, Response<unknown>>({
    url: `${SERVER.IAM}/roles/id/${data.id}/permissions`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: { permissionIds: data.permissionIds instanceof Array ? data.permissionIds : [] },
  });
}
export function getRoleDataAuth(data: { id: string; dataTypes: string[] } & RequestBase) {
  const params = new URLSearchParams();
  params.append("dataTypes", data.dataTypes.join(","));
  return request<never, Response<DataAuthItem[]>>({
    url: `${SERVER.IAM}/current_org/roles/id/${data.id}/data_auths`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function setRoleDataAuth(data: { id: string; dataType: string; value: string } & RequestBase) {
  return request<unknown, Response<unknown>>({
    url: `${SERVER.IAM}/current_org/roles/id/${data.id}/data_auth`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: { dataType: data.dataType, value: data.value, hasAllAuth: data.hasAllAuth, auditInfo: data.auditInfo || undefined },
  });
}

export function setLoggerData(data: { type: string; id: string; beforeIds: []; beforeRegionIds: []; beforeLocationIds: []; afterIds: []; afterRegionIds: []; afterLocationIds: [] } & RequestBase) {
  return request<unknown, Response<unknown>>({
    url: `${SERVER.CMDB}/${data.type}/${data.id}/role`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: {
      beforeIds: data.beforeIds || [],
      beforeRegionIds: data.beforeRegionIds || [],
      beforeLocationIds: data.beforeLocationIds || [],
      afterIds: data.afterIds || [],
      afterRegionIds: data.afterRegionIds || [],
      afterLocationIds: data.afterLocationIds || [],
    },
  });
}

// /* TODO: 团队 */
// export interface GroupItem {
//   id: string;
//   tenantId: string;
//   name: string;
//   note: string;
//   version: string;
//   createdTime: string;
//   updatedTime: string;
//   createdBy: string;
//   updatedBy: string;
// }

// export function getGroup(data: { [key: string]: unknown }) {
//   return request<unknown, Response<GroupItem[]>>({
//     url: `${SERVER.IAM}/current_org/user_groups`,
//     method: Method.Get,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: new URLSearchParams({}),
//   });
// }
// export function addTeam(data: Partial<GroupItem> & { [key: string]: unknown }) {
//   return request<unknown, Response<GroupItem>>({
//     url: `${SERVER.IAM}/current_org/user_groups`,
//     method: Method.Post,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: { name: data.name, note: data.note },
//   });
// }
// export function modTeam(data: Partial<GroupItem> & { [key: string]: unknown }) {
//   return request<unknown, Response<GroupItem>>({
//     url: `${SERVER.IAM}/user_groups/${data.id}`,
//     method: Method.Put,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: { name: data.name, note: data.note },
//   });
// }
// export function delTeam(data: Partial<GroupItem> & { [key: string]: unknown }) {
//   return request<unknown, Response<GroupItem>>({
//     url: `${SERVER.IAM}/user_groups/${data.id}`,
//     method: Method.Delete,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: new URLSearchParams({}),
//   });
// }
// /* TODO: 业务组 */
// export interface BusinessItem {
//   id: string;
//   name: string;
//   note?: string;
//   createdTime?: string;
//   updatedTime?: string;
// }

// export function getBusiness(data: {} & RequestBase) {
//   return request<unknown, Response<BusinessItem[]>>({
//     url: `${SERVER.IAM}/biz_groups`,
//     method: Method.Get,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
//   });
// }

// export function addBusiness(data: Partial<BusinessItem> & RequestBase) {
//   return request<unknown, Response<BusinessItem[]>>({
//     url: `${SERVER.IAM}/biz_groups`,
//     method: Method.Post,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: ["name", "note"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//   });
// }
// export function modBusiness(data: Partial<BusinessItem> & RequestBase) {
//   return request<unknown, Response<BusinessItem[]>>({
//     url: `${SERVER.IAM}/biz_groups/${data.id}`,
//     method: Method.Put,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: ["name", "note"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//   });
// }
// export function delBusiness(data: Partial<BusinessItem> & RequestBase) {
//   return request<unknown, Response<BusinessItem[]>>({
//     url: `${SERVER.IAM}/biz_groups/${data.id}`,
//     method: Method.Delete,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
//   });
// }
/* 联系人 */
export interface ContactsItem {
  id: string;
  name?: string;
  email?: string;
  landlinePhone?: string;
  mobilePhone?: string;
  smsPhone?: string;
  smsEnabled: boolean;
  vip: boolean;
  note?: string;
  zoneId?: string;
  externalId?: string;
  createdTime?: string;
  updatedTime?: string;
  createdBy?: string;
  updatedBy?: string;
}

export function getContacts(data: { pageNumber: string | number; pageSize: string | number; name?: string } & RequestBase) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.IAM}/contacts/org/current`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

export function addContacts(data: Partial<ContactsItem> & RequestBase) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.IAM}/contacts/org/current`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: { name: data.name, email: data.email, landlinePhone: data.landlinePhone, mobilePhone: data.mobilePhone, smsPhone: data.smsPhone, smsEnabled: data.smsEnabled, vip: data.vip, note: data.note, zoneId: data.zoneId, externalId: data.externalId },
  });
}

export function modContacts(data: Partial<ContactsItem> & RequestBase) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.IAM}/contacts/${data.id}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: { name: data.name, email: data.email, landlinePhone: data.landlinePhone, mobilePhone: data.mobilePhone, smsPhone: data.smsPhone, smsEnabled: data.smsEnabled, vip: data.vip, note: data.note, zoneId: data.zoneId, externalId: data.externalId },
  });
}

export function delContacts(data: Partial<ContactsItem> & RequestBase) {
  return request<unknown, Response<RoleItem>>({
    url: `${SERVER.IAM}/contacts/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

/* TODO: 用户组 */
export interface GroupItem {
  id: string /* 主键 */;
  tenantId?: string /* 租户ID */;
  name?: string /* 用户组名称 */;
  note?: string /* 用户组备注 */;
  createdTime: string /* 创建时间 */;
  updatedTime: string /* 更新时间 */;
  createdBy?: string /* 创建人 */;
  updatedBy?: string /* 更新人 */;
  tenantName?: string /* 租户名称 */;
  tenantAbbreviation?: string /* 租户缩写 */;
}
const group = {
  getUserGroupByPermissionIds: function (req: { queryPermissionId: string; verifyPermissionIds: string[] } & RequestBase) {
    const params = new URLSearchParams({});
    // const data = new URLSearchParams({});
    // bindSearchParams({ queryPermissionId: req.queryPermissionId, verifyPermissionIds: req.verifyPermissionIds }, data);
    const siteConfig: any = useSiteConfig();

    return request<never, Response<GroupItem[]>>({
      url: `${SERVER.IAM}/current_org/queryUserGroupByPermissionIds`,
      method: Method.Post,
      // responseType: "json",
      signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
      params: {},
      data: Object.assign(req, { appId: siteConfig.baseInfo.app }),
    });
  },

  /**
   * @description 获取组织下所有用户组
   * @url http://*************:3000/project/11/interface/api/2683
   */
  getGroupList: function getGroupList(req: Partial<Record<"external" | "appId" | "ownToLoginUser", boolean | string>> & RequestBase) /* 获取组织下所有用户组 */ {
    // const header = new Headers();
    const params = new URLSearchParams({});
    bindSearchParams({ appId: req.appId /* 应用ID, 为空代表全部 */, external: req.external /* 是否包含外部用户组(默认不包含) */, ownToLoginUser: req.ownToLoginUser }, params);
    const data = new URLSearchParams({});
    return request<never, Response<GroupItem[]>>({
      url: `${SERVER.IAM}/current_org/user_groups`,
      method: Method.Get,
      responseType: "json",
      signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
      params: params,
      data,
    });
  },
  /**
   * @description 新增用户组
   * @url http://*************:3000/project/11/interface/api/2677
   */
  addGroupData: function addGroupData(req: { name: string; note?: string } & RequestBase) /* 新增用户组 */ {
    // const header = new Headers();
    // header.set("Content-Type", "application/json");
    const params = new URLSearchParams({});
    bindSearchParams({}, params);
    const data = { name: req.name /* 用户组名称 */, note: req.note /* 备注 */ };
    return request<never, Response<GroupItem>>({ url: `${SERVER.IAM}/current_org/user_groups`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
  },
  /**
   * @description 更新用户组
   * @url http://*************:3000/project/11/interface/api/2679
   */
  modGroupData: function modGroupData(req: Required<Record<"id", string>> & { name?: string; note?: string } & RequestBase) /* 更新用户组 */ {
    // const header = new Headers();
    // header.set("Content-Type", "application/json");
    const params = new URLSearchParams({});
    bindSearchParams({}, params);
    const data = { name: req.name /* 用户组名称 */, note: req.note /* 备注 */ };
    return request<never, Response<GroupItem>>({ url: `${SERVER.IAM}/user_groups/${req.id /* 用户组ID */}`, method: Method.Put, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
  },
  /**
   * @description 删除用户组
   * @url http://*************:3000/project/11/interface/api/2681
   */
  delGroupData: function delGroupData(req: Required<Record<"id", string>> & RequestBase) /* 删除用户组 */ {
    // const header = new Headers();
    // header.set("Content-Type", "application/x-www-form-urlencoded");
    const params = new URLSearchParams({});
    bindSearchParams({}, params);
    const data = new URLSearchParams({});
    return request<never, Response<null>>({ url: `${SERVER.IAM}/user_groups/${req.id /* 用户组ID */}`, method: Method.Delete, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
  },
  /**
   * @description 获取当前租户受托租户的用户组列表
   * @url http://*************:3000/project/11/interface/api/2685
   */
  getGroupByTenant: function getGroupByTenant(req: RequestBase) /* 获取当前租户受托租户的用户组列表 */ {
    // const header = new Headers();
    const params = new URLSearchParams({});
    bindSearchParams({}, params);
    const data = new URLSearchParams({});
    return request<never, Response<GroupItem[]>>({ url: `${SERVER.IAM}/current_org/entrustee/user_groups`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
  },
  /**
   * @description 获取用户组关联的租户列表
   * @url http://*************:3000/project/11/interface/api/2691
   */
  getTenantByGroup: function getTenantByGroup(req: Required<Record<"id", string>> & RequestBase) /* 获取用户组关联的租户列表 */ {
    // const header = new Headers();
    const params = new URLSearchParams({});
    bindSearchParams({}, params);
    const data = new URLSearchParams({});
    return request<never, Response<EntrustTenantItem[]>>({ url: `${SERVER.IAM}/user_groups/${req.id /* 用户组ID */}/assign_tenants`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
  },
  /**
   * @description 解除用户组和租户的分配关系
   * @url http://*************:3000/project/11/interface/api/2689
   */
  delGroupByTenant: function delGroupByTenant(req: { groupIds: string[]; tenantIds: string[] } & RequestBase) /* 解除用户组和租户的分配关系 */ {
    // const header = new Headers();
    // header.set("Content-Type", "application/json");
    const params = new URLSearchParams({});
    bindSearchParams({}, params);
    const data = { groupIds: req.groupIds /* 用户组ID列表 */, tenantIds: req.tenantIds /* 租户ID列表 */ };
    return request<never, Response<null>>({ url: `${SERVER.IAM}/user_group/unassign_tenant`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
  },
  /**
   * @description 为用户组分配租户
   * @url http://*************:3000/project/11/interface/api/2687
   */
  addGroupByTenant: function addGroupByTenant(req: { groupIds: string[]; tenantIds: string[] } & RequestBase) /* 添加用户组和租户的分配关系 */ {
    // const header = new Headers();
    // header.set("Content-Type", "application/json");
    const params = new URLSearchParams({});
    bindSearchParams({}, params);
    const data = { groupIds: req.groupIds /* 用户组ID列表 */, tenantIds: req.tenantIds /* 租户ID列表 */ };
    return request<never, Response<null>>({ url: `${SERVER.IAM}/user_group/assign_tenant`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
  },
  /**
   * @description 获取组下用户列表
   * @url http://*************:3000/project/11/interface/api/2697
   */
  getUserByGroup: function getUserByGroup(req: Required<Record<"id", string>> & RequestBase) /* 获取组下用户列表 */ {
    // const header = new Headers();
    const params = new URLSearchParams({});
    bindSearchParams({}, params);
    const data = new URLSearchParams({});
    return request<never, Response<EntrustUserItem[]>>({ url: `${SERVER.IAM}/user_groups/${req.id /* 用户组ID */}/user_infos`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
  },
  /**
   * @description 用户组添加用户
   * @url http://*************:3000/project/11/interface/api/2693
   */
  addUserByGroup: function addUserByGroup(req: Required<Record<"id", string>> & { userIds?: string[] } & RequestBase) /* 用户组添加用户 */ {
    // const header = new Headers();
    // header.set("Content-Type", "application/json");

    const params = new URLSearchParams({});
    bindSearchParams({}, params);

    const data = { userIds: req.userIds /* 用户ID列表 */ };

    return request<never, Response<null>>({ url: `${SERVER.IAM}/user_groups/${req.id /* 用户组ID */}/add_users`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
  },
  /**
   * @description 用户组移除用户
   * @url http://*************:3000/project/11/interface/api/2695
   */
  delUserByGroup: function delUserByGroup(req: Required<Record<"id", string>> & { userIds?: string[] } & RequestBase) /* 用户组移除用户 */ {
    // const header = new Headers();
    // header.set("Content-Type", "application/json");
    const params = new URLSearchParams({});
    bindSearchParams({}, params);
    const data = { userIds: req.userIds /* 用户ID列表 */ };
    return request<never, Response<null>>({ url: `${SERVER.IAM}/user_groups/${req.id /* 用户组ID */}/remove_users`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
  },

  getUsersByGourpIds: function (req: Required<Record<"ids", string>> & { ids?: string } & RequestBase) /* 用户组id批量获取用户 */ {
    // const header = new Headers();
    // header.set("Content-Type", "application/json");
    const params = new URLSearchParams({});
    bindSearchParams({}, params);
    const data = { userIds: req.userIds /* 用户ID列表 */ };
    return request<never, Response<null>>({ url: `${SERVER.IAM}/user_groups/${req.ids /* 用户组ID */}/find_group_users`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
  },
};
export const {
  getUserGroupByPermissionIds,
  /* 获取组织下所有用户组 */ getGroupList,
  /* 新增用户组 */ addGroupData,
  /* 更新用户组 */ modGroupData,
  /* 删除用户组 */ delGroupData,
  // 租户
  /* 获取当前租户受托租户的用户组列表 */ getGroupByTenant,
  /* 获取用户组关联的租户列表 */ getTenantByGroup,
  /* 解除用户组和租户的分配关系 */ delGroupByTenant,
  /* 为用户组分配租户 */ addGroupByTenant,
  // 用户
  /* 获取组下用户列表 */ getUserByGroup,
  /* 用户组添加用户 */ addUserByGroup,
  /* 用户组移除用户 */ delUserByGroup,
  /* 用户组id批量获取用户 */ getUsersByGourpIds,
} = group;
/* 租户 */
// export interface TenantItem {
//   id: string;
//   platform: string;
//   baileeTenantId: string;
//   name: string;
//   abbreviation: string;
//   systemEdition: string;
//   zoneId: keyof typeof Zone;
//   language: string;
//   address: string;
//   note: string;
//   fullPermission: boolean;
//   securityConfig: TenantSecurityConfig[];
//   ownerId: string;
//   ownerName: string;
//   ownerNickname: string;
//   ownerAccount: string;
//   ownerPhone: string;
//   ownerEmail: string;
//   blocked: boolean;
//   version: string;
//   createdTime: string;
//   updatedTime: string;
//   createdBy: string;
//   updatedBy: string;
//   desensitized: string;
//   children: TenantItem[];
// }
export interface TenantItem {
  /** 租户ID */
  id: /* Integer */ string;
  /** 安全容器ID */
  containerId: /* Integer */ string;
  /** 租户名称 */
  name: string;
  /** 租户缩写 */
  abbreviation: string;
  /** 系统版本 */
  systemEdition?: string;
  /** 时区ID */
  zoneId?: string;
  /** 语言 */
  language?: string;
  /** 地址 */
  address?: string;
  /** 备注 */
  note?: string;
  /** 是否拥有完整权限 */
  fullPermission: boolean;
  /** 双因素认证启用状态 */
  mfaState?: /* 枚举: ENABLED :启用MFA认证 | DISABLED :禁用MFA认证 | DEFAULT :使用系统默认配置 */ "ENABLED" | "DISABLED" | "DEFAULT";
  /** 拥有人用户ID */
  ownerId?: /* Integer */ string;
  /** 拥有人姓名 */
  ownerName?: string;
  /** 拥有人昵称 */
  ownerNickname?: string;
  /** 拥有人账号 */
  ownerAccount?: string;
  /** 拥有人手机号 */
  ownerPhone?: string;
  /** 拥有人邮箱 */
  ownerEmail?: string;
  /** 租户是否已被锁定 */
  blocked: boolean;
  /** 租户是否已激活 */
  activated: boolean;
  /** 乐观锁版本 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人信息 */
  createdBy?: string;
  /** 更新人信息 */
  updatedBy?: string;
  /** 租户邮编 */
  tenantPostcode?: string;
  /** 租户邮箱 */
  tenantEmail?: string;
  /** 租户传真 */
  tenantFax?: string;
  /** 租户电话 */
  tenantPhone?: string;
  /** 租户类型 */
  tenantType?: string;
  /** 租户签约地 */
  tenantSigningPlace?: string;
  /** 租户签约地2 */
  tenantSigningPlace2?: string;
  /** 租户行业 */
  tenantIndustry?: string;
  /** 租户性质 */
  tenantNature?: string;
  /** 租户渠道 */
  tenantChannel?: string;
  /** 租户是否运营商 */
  tenantOperator: boolean;
  /** 租户是否运营商 */
  tenantForeign: boolean;
  /** 是否默认启用了MFA */
  enableMfaDefault: boolean;

  synetcare: boolean;
}
// {
//   id?: string /* 主键 */;
//   baileeTenantId?: string /* 受托租户ID */;
//   name?: string /* 租户名称 */;
//   abbreviation?: string /* 租户缩写 */;
//   systemEdition?: string /* 系统版本 */;
//   zoneId?: string /* 时区ID */;
//   language?: string /* 语言 */;
//   address?: string /* 地址 */;
//   note?: string /* 备注 */;
//   ownerId?: string /* 拥有人用户ID */;
//   ownerName?: string /* 拥有人姓名 */;
//   ownerNickname?: string /* 拥有人昵称 */;
//   ownerAccount?: string /* 拥有人账号 */;
//   ownerPhone?: string /* 拥有人手机号 */;
//   ownerEmail?: string /* 拥有人邮箱 */;
//   fullPermission: boolean /* 拥有全部权限 */;
//   blocked: boolean /* 租户是否已被锁定 */;
//   version: string /* 乐观锁版本 */;
//   createdTime: string /* 创建时间 */;
//   updatedTime: string /* 更新时间 */;
//   createdBy?: string /* 创建人信息 */;
//   updatedBy?: string /* 更新人信息 */;
//   baileeTenantName?: string /* 受托租户名称 */;
//   baileeTenantAbbreviation?: string /* 受托租户缩写 */;
// }

export interface TenantSecurityConfig {
  enableMfa: boolean;
  repeatLogin: boolean;
  loginFailureLimit: string;
  histPasswordLimit: string;
  passwordExpireDays: string;
}
export function /* 查询当前平台下租户列表 */ getTenantList(req: Partial<{ id: string; keyword: string }> & RequestBase) {
  const params = new URLSearchParams({ pageNumber: String((req.paging || {}).pageNumber || 1), pageSize: String((req.paging || {}).pageSize || 10) });
  bindSearchParams({ id: req.id, keyword: req.keyword }, params);
  bindSearchParams({ permissionId: ["513160392680144896"].join(",") }, params);
  bindSearchParams({ sort: req.sort instanceof Array ? req.sort : [] }, params);
  bindSearchParams(
    {
      blocked: req.blocked,
      activated: req.activated,
      /* 客户名称 */
      ...([...(req.includeTenantName instanceof Array ? req.includeTenantName : []), ...(req.excludeTenantName instanceof Array ? req.excludeTenantName : []), ...(req.eqTenantName instanceof Array ? req.eqTenantName : []), ...(req.neTenantName instanceof Array ? req.neTenantName : [])].filter((v) => v).length ? { tenantNameFilterRelation: req.tenantNameFilterRelation === "OR" ? "OR" : "AND", includeTenantName: req.includeTenantName instanceof Array && req.includeTenantName.length ? req.includeTenantName.join(",") : void 0, excludeTenantName: req.excludeTenantName instanceof Array && req.excludeTenantName.length ? req.excludeTenantName.join(",") : void 0, eqTenantName: req.eqTenantName instanceof Array && req.eqTenantName.length ? req.eqTenantName.join(",") : void 0, neTenantName: req.neTenantName instanceof Array && req.neTenantName.length ? req.neTenantName.join(",") : void 0 } : {}),

      //  时区
      ...([...(req.includeTenantZoneId instanceof Array ? req.includeTenantZoneId : []), ...(req.excludeTenantZoneId instanceof Array ? req.excludeTenantZoneId : []), ...(req.eqTenantZoneId instanceof Array ? req.eqTenantZoneId : []), ...(req.neTenantZoneId instanceof Array ? req.neTenantZoneId : [])].filter((v) => v).length ? { tenantZoneIdFilterRelation: req.tenantZoneIdFilterRelation === "OR" ? "OR" : "AND", includeTenantZoneId: req.includeTenantZoneId instanceof Array && req.includeTenantZoneId.length ? req.includeTenantZoneId.join(",") : void 0, excludeTenantZoneId: req.excludeTenantZoneId instanceof Array && req.excludeTenantZoneId.length ? req.excludeTenantZoneId.join(",") : void 0, eqTenantZoneId: req.eqTenantZoneId instanceof Array && req.eqTenantZoneId.length ? req.eqTenantZoneId.join(",") : void 0, neTenantZoneId: req.neTenantZoneId instanceof Array && req.neTenantZoneId.length ? req.neTenantZoneId.join(",") : void 0 } : {}),

      /* 客户缩写 */
      ...([...(req.includeTenantAbbreviation instanceof Array ? req.includeTenantAbbreviation : []), ...(req.excludeTenantAbbreviation instanceof Array ? req.excludeTenantAbbreviation : []), ...(req.eqTenantAbbreviation instanceof Array ? req.eqTenantAbbreviation : []), ...(req.neTenantAbbreviation instanceof Array ? req.neTenantAbbreviation : [])].filter((v) => v).length ? { tenantAbbreviationFilterRelation: req.tenantAbbreviationFilterRelation === "OR" ? "OR" : "AND", includeTenantAbbreviation: req.includeTenantAbbreviation instanceof Array && req.includeTenantAbbreviation.length ? req.includeTenantAbbreviation.join(",") : void 0, excludeTenantAbbreviation: req.excludeTenantAbbreviation instanceof Array && req.excludeTenantAbbreviation.length ? req.excludeTenantAbbreviation.join(",") : void 0, eqTenantAbbreviation: req.eqTenantAbbreviation instanceof Array && req.eqTenantAbbreviation.length ? req.eqTenantAbbreviation.join(",") : void 0, neTenantAbbreviation: req.neTenantAbbreviation instanceof Array && req.neTenantAbbreviation.length ? req.neTenantAbbreviation.join(",") : void 0 } : {}),

      /* 地址 */
      ...([...(req.includeAddress instanceof Array ? req.includeAddress : []), ...(req.excludeAddress instanceof Array ? req.excludeAddress : []), ...(req.eqAddress instanceof Array ? req.eqAddress : []), ...(req.neAddress instanceof Array ? req.neAddress : [])].filter((v) => v).length ? { addressFilterRelation: req.addressFilterRelation === "OR" ? "OR" : "AND", includeAddress: req.includeAddress instanceof Array && req.includeAddress.length ? req.includeAddress.join(",") : void 0, excludeAddress: req.excludeAddress instanceof Array && req.excludeAddress.length ? req.excludeAddress.join(",") : void 0, eqAddress: req.eqAddress instanceof Array && req.eqAddress.length ? req.eqAddress.join(",") : void 0, neAddress: req.neAddress instanceof Array && req.neAddress.length ? req.neAddress.join(",") : void 0 } : {}),

      // mfaState: req.mfaState /* 双因素认证 */,

      tenantTypes: req.tenantTypes ? (req.tenantTypes as string).split(",") : void 0,
      languages: req.languages ? (req.languages as string).split(",") : void 0,
      systemEditions: req.systemEditions ? (req.systemEditions as string).split(",") : void 0,
      synetcare: req.synetcare,
    },
    params
  );

  const data = new URLSearchParams({});

  return request<unknown, Response<TenantItem[]>>({ url: `${SERVER.IAM}/tenants/platform/filter`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, params, data });
}
export function /* 当前平台创建租户 */ addTenantData(req: Partial<TenantItem> & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  const data = { sendMsg: req.sendMsg, sendEmail: req.sendEmail, containerId: req.containerId, tenantType: req.tenantType, activated: req.activated /* 是否激活 */, tenantPhone: req.tenantPhone /* 电话 */, baileeTenantId: req.baileeTenantId /* 受托租户ID */, name: req.name /* 租户名称 */, abbreviation: req.abbreviation /* 租户缩写 */, systemEdition: req.systemEdition /* 系统版本编码 */, zoneId: req.zoneId /* 所在时区 */, language: req.language /* 语言 */, address: req.address /* 地址 */, owner: req.owner /* 租户拥有人信息 */, joinUserGroupIds: req.joinUserGroupIds /*加入租户的用户组id */, fax: req.fax /*传真 */, postcode: req.postcode /*邮编 */, signAddress: req.signAddress /*客户签约地 */, signAddress2: req.signAddress2 /*客户签约地-2 */, nature: req.nature /*性质 */, note: req.note /*描述 */, blocked: req.blocked /*是否激活 */, isOperator: req.isOperator /*是否运营商 */, isForeign: req.isForeign /*是否国外客户 */, industry: req.industry /*行业 */, isJoin: req.isJoin /*是否加入 */, joinUserIds: req.joinUserIds /*加入租户的用户id */, channel: req.channel /*渠道 */, email: req.email /*邮箱 */, synetcare: req.synetcare };

  return request<unknown, Response<TenantItem>>({ url: `${SERVER.IAM}/tenants/platform/current`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, params, data });
}
export function /* 当前平台创建租户 */ activeTenant(req: Partial<TenantItem> & RequestBase) {
  // const params = new URLSearchParams({});
  // bindSearchParams({}, params);

  // const data = { baileeTenantId: req.baileeTenantId /* 受托租户ID */, name: req.name /* 租户名称 */, abbreviation: req.abbreviation /* 租户缩写 */, systemEdition: req.systemEdition /* 系统版本编码 */, zoneId: req.zoneId /* 所在时区 */, language: req.language /* 语言 */, address: req.address /* 地址 */, note: req.note /* 备注 */, owner: req.owner /* 租户拥有人信息 */, joinUserGroupIds: req.joinUserGroupIds, /*加入租户的用户组id */ joinUserIds: req.joinUserIds /*加入租户的用户id */ };

  return request<unknown, Response<TenantItem>>({ url: `${SERVER.IAM}/tenants/${req.id}/unblock/${req.abbreviation}`, method: Method.Patch, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, params: {}, data: {} });
}

export function /* 更新租户信息 */ setTenantData(req: { id: string } & Partial<TenantItem> & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  // const data = { name: req.name /* 租户名称 */, abbreviation: req.abbreviation /* 租户缩写 */, zoneId: req.zoneId /* 所在时区 */, language: req.language /* 语言 */, address: req.address /* 地址 */, note: req.note /* 备注 */, systemEdition: req.systemEdition /* 系统版本 */, joinUserGroupIds: req.joinUserGroupIds /*加入租户的用户组id */, joinUserIds: req.joinUserIds /*加入租户的用户id */ };

  const data = { sendMsg: req.sendMsg, sendEmail: req.sendEmail, tenantType: req.tenantType, mfaState: req.mfaState /*双因素认证*/, activated: req.activated /* 是否激活 */, tenantPhone: req.tenantPhone /* 电话 */, baileeTenantId: req.baileeTenantId /* 受托租户ID */, name: req.name /* 租户名称 */, abbreviation: req.abbreviation /* 租户缩写 */, systemEdition: req.systemEdition /* 系统版本编码 */, zoneId: req.zoneId /* 所在时区 */, language: req.language /* 语言 */, address: req.address /* 地址 */, owner: req.owner /* 租户拥有人信息 */, joinUserGroupIds: req.joinUserGroupIds /*加入租户的用户组id */, fax: req.fax /*传真 */, postcode: req.postcode /*邮编 */, signAddress: req.signAddress /*客户签约地 */, signAddress2: req.signAddress2 /*客户签约地-2 */, nature: req.nature /*性质 */, note: req.note /*描述 */, blocked: req.blocked /*是否激活 */, isOperator: req.isOperator /*是否运营商 */, isForeign: req.isForeign /*是否国外客户 */, industry: req.industry /*行业 */, isJoin: req.isJoin /*是否加入 */, joinUserIds: req.joinUserIds /*加入租户的用户id */, channel: req.channel /*渠道 */, email: req.email /*邮箱 */, synetcare: req.synetcare };
  return request<unknown, Response<TenantItem>>({ url: `${SERVER.IAM}/tenants/${req.id}`, method: Method.Put, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, params, data });
}
export function /* 选择性更新租户信息 */ modTenantData(req: { id: string } & Partial<TenantItem> & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  const data = { tenantType: req.tenantType, mfaState: req.mfaState /*双因素认证*/, activated: req.activated /* 是否激活 */, tenantPhone: req.tenantPhone /* 电话 */, baileeTenantId: req.baileeTenantId /* 受托租户ID */, name: req.name /* 租户名称 */, abbreviation: req.abbreviation /* 租户缩写 */, systemEdition: req.systemEdition /* 系统版本编码 */, zoneId: req.zoneId /* 所在时区 */, language: req.language /* 语言 */, address: req.address /* 地址 */, owner: req.owner /* 租户拥有人信息 */, joinUserGroupIds: req.joinUserGroupIds /*加入租户的用户组id */, fax: req.fax /*传真 */, postcode: req.postcode /*邮编 */, signAddress: req.signAddress /*客户签约地 */, signAddress2: req.signAddress2 /*客户签约地-2 */, nature: req.nature /*性质 */, note: req.note /*描述 */, blocked: req.blocked /*是否激活 */, isOperator: req.isOperator /*是否运营商 */, isForeign: req.isForeign /*是否国外客户 */, industry: req.industry /*行业 */, isJoin: req.isJoin /*是否加入 */, joinUserIds: req.joinUserIds /*加入租户的用户id */, channel: req.channel /*渠道 */, email: req.email /*邮箱 */ };

  return request<unknown, Response<TenantItem>>({ url: `${SERVER.IAM}/tenants/${req.id}`, method: Method.Patch, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, params, data });
}
export function /* 删除租户 */ delTenantData(req: { id: string } & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  const data = new URLSearchParams({});

  return request<unknown, Response<TenantItem>>({ url: `${SERVER.IAM}/tenants/${req.id}`, method: Method.Delete, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, params, data });
}
export function /* 取消，激活_租户 */ cutTenantActivate(req: { id: string; abbreviation: string; state: "block" | "unblock" } & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  const data = new URLSearchParams({});
  const url = req.state == "block" ? `${SERVER.IAM}/tenants/${req.id}/activate/${req.abbreviation}` : `${SERVER.IAM}/tenants/${req.id}/deactivate`;

  return request<unknown, Response<null>>({ url: url, method: Method.Patch, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, params, data });
}
export function /* 锁定，解锁_租户 */ cutTenantState(req: { id: string; state: "block" | "unblock" } & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  const data = new URLSearchParams({});

  return request<unknown, Response<null>>({ url: `${SERVER.IAM}/tenants/${req.id}/${req.state}`, method: Method.Patch, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, params, data });
}
export function /* 变更租户托管关系 */ setTenantDataByTrusteeship(req: { id: string; baileeTenantId: string } & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({ baileeTenantId: req.baileeTenantId || "" }, params);

  const data = new URLSearchParams({});

  return request<unknown, Response<null>>({ url: `${SERVER.IAM}/tenants/${req.id}/trusteeship`, method: Method.Patch, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, params, data });
}
interface TenantPermissionItem {
  app: import("@/api/application").NavItem /* 应用信息 */;
  catalogs: Catalogs[] /* 权限目录列表 */;
  permissions: Permissions[] /* 权限信息 */;
  assignedPermissionIds?: string[] /* 已分配的权限ID列表 */;
  appAssigned: boolean /* 是否已分配应用给角色 */;
}
/**
 * @description 获取租户权限分配视图
 * @url http://*************:3000/project/11/interface/api/2567
 */
export function /* 获取租户权限分配视图 */ getTenantByPermission(req: Partial<Record<"appIds", string>> & RequestBase) {
  // const header = new Headers();

  const params = new URLSearchParams({});
  bindSearchParams({ appIds: req.appIds /* 应用ID列表 多个之间,分割 */ }, params);

  const data = new URLSearchParams({});

  return request<never, Response<(Omit<TenantPermissionItem, "app"> & Record<"app", import("@/api/application").AppItem>)[]>>({ url: `${SERVER.IAM}/tenants/id/${req.tenantId /* 租户ID */}/permission_assign_view/batch`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data }).then(
    (res): Response<TenantPermissionItem[]> => ({
      success: res.success,
      message: res.message,
      page: res.page,
      size: res.size,
      total: res.total,
      data: res.data.map((data) => ({
        ...data,
        app: ((menu): import("@/api/application").NavItem => {
          const config: Required<import("@/api/application").NavItem> = { id: menu.id, rootId: menu.id, parentId: "", path: menu.rootPath, terminal: menu.terminal, title: menu.name, enTitle: menu.enName, name: menu.id, order: Number(menu.orderNum), icon: "local-SystemApps-line", type: appType.ROUTE, theme: appTheme.BASE, url: "", component: "", keepalive: false, enabled: true, permission: menu.permissionIds instanceof Array ? menu.permissionIds : [], permissionGroups: menu.permissionGroups instanceof Array ? menu.permissionGroups : [[], []], note: menu.note, version: menu.version, config: menu.config, hash: "", query: {}, params: {}, pattern: /(?:)/, names: [], children: [], createdTime: menu.createdTime as string, updatedTime: menu.updatedTime as string };
          try {
            const { type: type = config.type, theme: theme = config.theme, icon: icon = config.icon, url: url = config.url, component: component = config.component, keepalive: keepalive = config.keepalive } = JSON.parse(menu.config);
            return Object.assign(config, { type, theme, icon, url, component, keepalive });
          } catch (error) {
            return config;
          }
        })(data.app),
      })),
    })
  );
}
/**
 * @description 保存租户权限
 * @url http://*************:3000/project/11/interface/api/2569
 */
export function /* 保存租户权限 */ setTenantByPermission(req: { appId: string; appAssigned: boolean; permissionIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");

  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  const data = { appId: req.appId /* 应用ID */, appAssigned: req.appAssigned /* 是否分配此应用, 默认是 */, permissionIds: req.permissionIds /* 权限ID列表 */ };

  return request<never, Response<null>>({ url: `${SERVER.IAM}/tenants/id/${req.tenantId /* 租户ID */}/permissions`, method: Method.Put, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export interface EntrustTenantItem {
  id?: string /* 主键 */;
  name?: string /* 名称 */;
  abbreviation?: string /* 缩写 */;
  systemEdition?: string /* 系统版本 */;
  zoneId?: string /* 时区 */;
  language?: string /* 语言 */;
  note?: string /* 备注 */;
  blocked: boolean /* 是否已被冻结 */;
}
export interface EntrustUserItem {
  id: string /* 用户ID */;
  platform?: string /* 所属平台 */;
  tenantId?: string /* 所属租户 */;
  containerId?: string /* 安全容器ID */;
  name: string /* 姓名 */;
  nickname?: string /* 昵称 */;
  account?: string /* 账号 */;
  phone?: string /* 手机号 */;
  email?: string /* 邮箱 */;
  language?: string /* 语言 */;
  zoneId?: string /* 时区 */;
  gender?: "SECRET" | "MALE" | "FEMALE" /* 性别 枚举类型: SECRET :保密 | MALE :男性 | FEMALE :女性 */;
  profilePicture?: string /* 头像 */;
  mfaState?: "ENABLED" | "DISABLED" | "DEFAULT" /* 双因素认证启用状态 枚举类型: ENABLED :启用MFA认证 | DISABLED :禁用MFA认证 | DEFAULT :使用系统默认配置 */;
  passwordTime?: string /* 密码修改时间戳 */;
  busy: boolean /* 是否忙碌 */;
  busyTime?: string /* 进入忙碌状态的时间戳 */;
  improved: boolean /* 是否已完善个人信息 */;
  blocked: boolean /* 是否被锁定 */;
  lastLoginTime?: string /* 最近登录时间 */;
  lastActivity?: string /* 最近访问时间 */;
  createdTime?: string /* 创建时间 */;
  tenantName?: string /* 所属租户名称 */;
  tenantAbbreviation?: string /* 所属租户的缩写 */;
  enableMfaDefault: boolean /* 是否默认启用了MFA */;
  desensitized: boolean /* 是否已脱敏 */;
}
// {
//   id?: string /* 主键 */;
//   platform?: string /* 平台编码 */;
//   internal: boolean /* 是否内部用户, 用户组租户ID和用户租户ID相同则代表内部用户 */;
//   groupTenantId?: string /* 用户组所属租户ID */;
//   groupId: string /* 用户组id */;
//   userTenantId?: string /* 用户所属租户ID */;
//   userId: string /* 用户id */;
//   name?: string /* 姓名 */;
//   namePinYin?: string /* 姓名拼音 */;
//   nickname?: string /* 昵称 */;
//   nicknamePinYin?: string /* 昵称拼音 */;
//   account?: string /* 账号 */;
//   phone?: string /* 手机号 */;
//   email?: string /* 邮箱 */;
//   version: string /* 乐观锁版本号 */;
//   createdTime: string /* 创建时间 */;
//   createdBy?: string /* 创建人信息 */;
//   desensitized: boolean /* 是否已脱敏 */;
// }
/**
 * @description 获取托管租户列表
 * @url http://*************:3000/project/11/interface/api/2663
 */
export function /* 获取托管租户列表 */ getEntrustTenantList(req: RequestBase) {
  // const header = new Headers();

  const params = new URLSearchParams({});

  const data = new URLSearchParams({});

  return request<never, Response<EntrustTenantItem[]>>({ url: `${SERVER.IAM}/current_tenant/entrust`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export interface UserHaveItem {
  id?: string /* 租户id */;
  userId?: string /* 客户id */;
}

export function /* 获取托管租户列表 */ setUserHave(req: RequestBase) {
  // const header = new Headers();

  // const params = new URLSearchParams({});

  // const data = new URLSearchParams({});

  return request<never, Response<UserHaveItem[]>>({
    url: `${SERVER.IAM}/tenants/${req.id}/owner`,
    method: Method.Patch,
    responseType: "json",
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
    params: {
      userId: req.userId,
    },
    data: {},
  });
}

export interface PreviewReqBody {
  userGroupId?: number /** 用户组ID  预览接口不传值 */;

  scheduleType?: string /** 排班方式 */;
  /** 人员选择(快速排班) */
  quickShifts?: {
    /** 值班方案(快速排班) */
    schedulePlan?: number;
    /** 每天循环次数 */
    cycleTime?: number;
    /** 值班时间段 */
    times?: string[];
    /** 人员id */
    staffIds?: string[];
    /** 人员名称 */
    staffNames?: string[];
    /** 值班时间和人员关系 */
    shiftTimeAndStaff?: {
      key?: string;
    };
  };
  /** 自定义班次(精细排班) */
  fineShifts?: {
    /** 循环周期(精细排班) */
    cycle?: number;
    /** 班次详情 */
    fineSchedules?: {
      /** 班次名称 */
      name?: string;
      /** 开始时间 */
      startTime?: string;
      /** 结束时间 */
      endTime?: string;
      /** 排版人员id */
      staffIds?: string[];
      /** 人员名称 */
      staffNames?: string[];
    }[];
  };
}

export interface PreviewResBody {
  data?: {
    /** 快速排班 */
    quickShifts?: {
      key?: {
        key?: string;
      };
    };
    /** 精细排班 */
    fineShifts?: {
      key?: {
        /** 班次名称 */
        name?: string;
        /** 开始时间 */
        startTime?: string;
        /** 结束时间 */
        endTime?: string;
        /** 排版人员id */
        staffIds?: string[];
        /** 人员名称 */
        staffNames?: string[];
      }[];
    }[];
  };
}
import moment from "moment";
import { faRuler } from "@fortawesome/free-solid-svg-icons";
export function /* 排班预览 */ schedulingPreview(req: RequestBase) {
  // const header = new Headers();
  const date = moment().format("YYYY-MM"); //当前时间
  // console.log(date, req);
  return request<never, Response<EntrustTenantItem[]>>({
    url: `${SERVER.EVENT_CENTER}/scheduling/${date}/preview`,
    method: Method.Patch,
    responseType: "json",
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
    params: {},
    data: req,
  });
}

//

export function setRoleUserGroup(req: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/current_org/roles/add_user_groups`,
    method: Method.Post,
    responseType: "json",
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
    params: {},
    data: req,
  });
}

export function setRoleUser(req: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/current_org/roles/add_users`,
    method: Method.Post,
    responseType: "json",
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
    params: {},
    data: req,
  });
}
// 获取组织下所有用户组
export function getGroup(data: { [key: string]: unknown }) {
  return request<unknown, Response<Record<"code" | "name", string>[]>>({
    url: `${SERVER.IAM}/current_org/user_groups`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
// 获取组织下安全容器所拥有用户组
export function getContainerGroup(data: { [key: string]: unknown }) {
  return request<unknown, Response<Record<"code" | "name", string>[]>>({
    url: `${SERVER.IAM}/security_container/current_org/assignable_user_groups`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { permissionId: ["512890964822458368"].join(",") },
    data: {},
  });
}

// 获取当前租户密码策略配置视图
export function getTenantPasswordStrategy(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/current_tenant/password_strategy/config_view`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

// 保存当前租户密码策略配置
export function saveTenantPasswordStrategy(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/current_tenant/password_strategy/config`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

// 保存当前租户密码策略配置
export function saveUserPasswordStrategy(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/password_strategy/users/${data.userId}/config`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//获取用户密码策略配置视图
export function getUserPasswordStrategy(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/password_strategy/users/${data.userId}/config_view`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

// 获取当前租户密码策略配置
export function getTenantPasswordStrategyMsg(data: RequestBase) {
  return request<
    never,
    Response<{
      /** 最小密码长度, null代表被禁用 */
      minLength: string | null;
      /** 是否强化密码复杂性 */
      hardening: boolean;
      /** 是否允许用户修改密码 */
      allowUserChange: boolean;
      /** 历史密码限制, 用于修改密码时限制最近使用过的密码, null或小于1不限制 */
      histPasswordLimit: string | null;
      /** 密码是否自动过期 */
      passwordExpire: boolean;
      /** 密码过期天数, 大于0生效, 默认不过期 */
      passwordExpireDays: string | null;
    }>
  >({
    url: `${SERVER.IAM}/current_org/password_strategy`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//获取用户密码策略配置
export function getUserPasswordStrategyMsg(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/current_user/password_strategy`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//获取指定用户密码策略配置
export function getAppointUserPasswordStrategyMsg(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/password_strategy/users/${data.userId}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//创建安全容器树
export function createSafeContaineItem(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/security_containers`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {
      name: data.name,
      parentId: data.parentId,
    },
  });
}
export interface SafeContaineTree {
  id: string;
  parentId?: string;
  name: string;
  children: SafeContaineTree[];
}
//获取安全容器树
export function getSafeContaineList(data: RequestBase) {
  return request<never, Response<SafeContaineTree[]>>({
    url: `${SERVER.IAM}/current_org/security_containers/current_user/available_tree`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { permissionId: ["623053172327317504"].join(","), containerId: data.containerId },
    data: {},
  });
}
//重命名安全容器树
export function editSafeContaine(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/security_containers/${data.id}/rename`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      name: data.name,
    },
    data: {},
  });
}
//删除安全容器树

export function deleteSafeContaineItem(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/security_containers/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export enum CustomerType {
  NETCARE = "NETCARE",
  NETSTAR = "NETSTAR",
  IT外包 = "IT外包",
  EDNM = "EDNM",
  DICT = "DICT",
}

export const customerTypeOption: { label: string; value: keyof typeof CustomerType }[] = [
  { value: CustomerType.NETCARE, label: "NETCARE" },
  { value: CustomerType.NETSTAR, label: "NETSTAR" },
  { value: CustomerType.IT外包, label: "IT外包" },
  { value: CustomerType.EDNM, label: "企业数通网管" },
  { value: CustomerType.DICT, label: "DICT" },
];

export function setBatchChangeExpirationDate(data: {} & RequestBase) {
  return request<never, any>({
    url: `${SERVER.IAM}/users/active`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function setThawUser(data: {} & RequestBase) {
  return request<never, Response<Record<string, any>>>({
    url: `${SERVER.IAM}/remove/cached_data`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
