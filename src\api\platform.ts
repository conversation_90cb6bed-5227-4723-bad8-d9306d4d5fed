/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */
import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";
import { gender, genderOption } from "@/api/personnel";
export { gender, genderOption };

/* TODO: 平台 */
export interface PlatformItem {
  code: string;
  name: string;
  multiTenant: boolean;
  description?: string;
  safety: { registrable?: boolean; loginFailureLimit?: number; histPasswordLimit?: number; passwordExpireDays?: number; initialUserPassword?: string };
  thirdParty: { githubCallbackUrl?: string };
  auth: { tenantHasAllMenus: boolean; enableApiAuthenticate: boolean; enableMfa: boolean; enableTwoStep: boolean };
  createdTime?: string;
  updatedTime?: string;
}
export function getPlatform(data: { [key: string]: unknown }) {
  return request<unknown, Response<PlatformItem[]>>({
    url: `${SERVER.IAM}/platform/find_all`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: new URLSearchParams({}),
  });
}
export function addPlatform(data: Partial<PlatformItem> & { [key: string]: unknown }) {
  return request<unknown, Response<PlatformItem[]>>({
    url: `${SERVER.IAM}/platform/create_platform`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
export function modPlatform(data: Partial<PlatformItem> & { [key: string]: unknown }) {
  return request<unknown, Response<PlatformItem[]>>({
    url: `${SERVER.IAM}/platform/update_platform`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { code: data.code },
    data: {
      name: data.name,
      description: data.description,
      auth: data.auth,
      safety: data.safety,
      thirdParty: data.thirdParty,
    },
  });
}
export function delPlatform(data: Partial<PlatformItem> & { [key: string]: unknown }) {
  return request<unknown, Response<PlatformItem[]>>({
    url: `${SERVER.IAM}/platform/delete_platform`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { code: data.code },
    data: new URLSearchParams({}),
  });
}
// export function catPlatform(data: { [key: string]: unknown }) {
//   return request<unknown, Response<PlatformItem[]>>({
//     url: `${SERVER.IAM}/platform`,
//     method: Method.Post,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: new URLSearchParams({}),
//   });
// }

/* TODO: 客户端（授权端） */
export interface ClientItem {
  platform: string; // 平台编码
  clientId: string;
  name: string;
  note: string;
  tokenValue?: string;
  clientSecret?: string;
  accessTokenValidity: number; // access token有效期, 单位秒
  refreshTokenValidity: number; // refresh token有效期, 单位秒
  accessTokenAutoRenewal: boolean; // token是否自动续期
  createdTime?: string;
  updatedTime?: string;
}
export function getClient(data: { platform: string } & { [key: string]: unknown }) {
  return request<unknown, Response<ClientItem[]>>({
    url: `${SERVER.IAM}/auth_client/find_all_by_platform`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { platform: data.platform },
    data: new URLSearchParams({}),
  });
}
export function addClient(data: ClientItem & { [key: string]: unknown }) {
  return request<unknown, Response<ClientItem[]>>({
    url: `${SERVER.IAM}/auth_client/create_client`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { platform: data.platform, clientId: data.clientId, clientSecret: data.clientSecret, name: data.name, note: data.note, accessTokenValidity: data.accessTokenValidity, refreshTokenValidity: data.refreshTokenValidity, accessTokenAutoRenewal: data.accessTokenAutoRenewal },
  });
}
export function modClient(data: ClientItem & { [key: string]: unknown }) {
  return request<unknown, Response<ClientItem[]>>({
    url: `${SERVER.IAM}/auth_client/update_client`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { clientId: data.clientId },
    data: { name: data.name, note: data.note, accessTokenValidity: data.accessTokenValidity, refreshTokenValidity: data.refreshTokenValidity, accessTokenAutoRenewal: data.accessTokenAutoRenewal },
  });
}
export function delClient(data: ClientItem & { [key: string]: unknown }) {
  return request<unknown, Response<ClientItem[]>>({
    url: `${SERVER.IAM}/auth_client/delete_client`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { clientId: data.clientId },
    data: new URLSearchParams({}),
  });
}
// export function catClient(data: ClientItem & { [key: string]: unknown }) {
//   return request<unknown, Response<PlatformItem[]>>({
//     url: `${SERVER.IAM}/auth_client`,
//     method: Method.Post,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: new URLSearchParams({}),
//   });
// }

/* TODO: 租户 */
export interface TenantItem {
  id: string;
  platform: string; // 平台编码
  parentId: string; // 父租户id
  name: string; // 租户名称
  address: string; // 地址
  ownerId: string; // 租户拥有人用户id
  note: string; // 备注

  frozen?: boolean; // 是否已被冻结

  children?: TenantItem[];

  createdTime?: string;
  updatedTime?: string;
}
export function getTenantByRoot(data: { platform: string; name?: string; paging: { pageNumber: number; pageSize: number } } & { [key: string]: unknown }) {
  return request<unknown, Response<TenantItem[]>>({
    url: `${SERVER.IAM}/tenant/query_tenant`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { platform: data.platform },
    data: { paging: data.paging, ...(data.name ? { name: data.name } : {}) },
  });
}
export function getTenantByChild(data: { parentId: string } & { [key: string]: unknown }) {
  return request<unknown, Response<TenantItem[]>>({
    url: `${SERVER.IAM}/tenant/child_list`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { id: data.parentId },
    data: new URLSearchParams({}),
  });
}
export function addTenant(data: Partial<TenantItem> & { [key: string]: unknown }) {
  return request<unknown, Response<TenantItem>>({
    url: `${SERVER.IAM}/tenant/create_tenant`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { platform: data.platform },
    data: { parentId: data.parentId, name: data.name, address: data.address, note: data.note, ownerId: data.ownerId },
  });
}
export function modTenant(data: Partial<TenantItem> & { [key: string]: unknown }) {
  return request<unknown, Response<TenantItem>>({
    url: `${SERVER.IAM}/tenant/update_tenant`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { tenantId: data.id },
    data: { name: data.name, address: data.address, note: data.note },
  });
}
export function delTenant(data: Partial<TenantItem> & { [key: string]: unknown }) {
  return request<unknown, Response<TenantItem>>({
    url: `${SERVER.IAM}/tenant/delete`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { id: data.id },
    data: new URLSearchParams({}),
  });
}
export function cutTenantToFreeze(data: Partial<TenantItem> & { [key: string]: unknown }) {
  return request<unknown, Response<TenantItem>>({
    url: data.frozen ? `${SERVER.IAM}/tenant/unfreeze_tenant` : `${SERVER.IAM}/tenant/freeze_tenant`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { id: data.id },
    data: new URLSearchParams({}),
  });
}
export function modTenantByUser(data: Partial<TenantItem> & { [key: string]: unknown }) {
  return request<unknown, Response<TenantItem>>({
    url: `${SERVER.IAM}/tenant/change_owner`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { id: data.id },
    data: { tenantId: data.id, userId: data.ownerId },
  });
}
/* TODO: 用户 */
export interface UserItem {
  platform?: string;

  roles: RoleItem[];

  id: string;
  name: string; // 姓名
  account: string; // 账号
  phone: string; // 手机号码
  email: string; // 邮箱

  password: string; // 密码
  passwordDate?: string; // 密码修改日期

  gender: keyof typeof gender; // 性别
  birthday: string; // 生日
  profilePicture: string; // 头像

  frozen: boolean; // 是否已被冻结
  frozenExpire: string; // 账号冻结的过期时间
  frozenNote: string; // 账号冻结备注

  createdTime?: string;
  updatedTime?: string;
}

export function cutUserToFreeze(req: Partial<UserItem> & { [key: string]: unknown }) {
  return request<unknown, Response<UserItem>>({
    url: req.frozen ? `${SERVER.IAM}/users/id/${req.id}/unblock` : `${SERVER.IAM}/users/id/${req.id}/block`,
    method: Method.Patch,
    responseType: "json",
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    params: {},
    data: new URLSearchParams({}),
  });
}
/* TODO: 角色 */
export interface RoleItem {
  id: string;
  superAdmin: boolean;
  dataAdmin: boolean;
  basic: boolean;
  name: string;
  note?: string;
  assignableRoleIds?: string[];

  createdTime?: string;
  updatedTime?: string;
}
export function getRole(data: { platform: string } & { [key: string]: unknown }) {
  return request<unknown, Response<RoleItem[]>>({
    url: `${SERVER.IAM}/roles`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: new URLSearchParams({}),
  });
}
export function addRole(data: Partial<RoleItem> & { [key: string]: unknown }) {
  return request<unknown, Response<RoleItem>>({
    url: `${SERVER.IAM}/roles`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { name: data.name, note: data.note },
  });
}
export function modRole(data: Partial<RoleItem> & { [key: string]: unknown }) {
  return request<unknown, Response<RoleItem>>({
    url: `${SERVER.IAM}/roles/${data.id}`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { name: data.name, note: data.note },
  });
}
export function delRole(data: Partial<RoleItem> & { [key: string]: unknown }) {
  return request<unknown, Response<RoleItem>>({
    url: `${SERVER.IAM}/roles/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: new URLSearchParams({}),
  });
}
/* TODO: 终端 */
export interface TerminalItem {
  code: string;
  platform: string;
  name: string;
  note?: string;

  createdTime?: string;
  updatedTime?: string;
}
export function getTerminal(data: { platform: string } & { [key: string]: unknown }) {
  return request<unknown, Response<TerminalItem[]>>({
    url: `${SERVER.IAM}/terminal/find_all_by_platform`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { platform: data.platform },
    data: new URLSearchParams({}),
  });
}
export function addTerminal(data: Partial<TerminalItem> & { [key: string]: unknown }) {
  return request<unknown, Response<TerminalItem>>({
    url: `${SERVER.IAM}/terminal/create_terminal`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { platform: data.platform, code: data.code, name: data.name, note: data.note },
  });
}
export function modTerminal(data: Partial<TerminalItem> & { [key: string]: unknown }) {
  return request<unknown, Response<TerminalItem>>({
    url: `${SERVER.IAM}/terminal/update_terminal`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { code: data.code },
    data: { name: data.name, note: data.note },
  });
}
export function delTerminal(data: Partial<TerminalItem> & { [key: string]: unknown }) {
  return request<unknown, Response<TerminalItem>>({
    url: `${SERVER.IAM}/terminal/delete_terminal`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { code: data.code },
    data: new URLSearchParams({}),
  });
}
/* TODO: 菜单 */
export interface MenuMeta {
  id: string;
  platform: string;
  parentId: string;
  name: string;
  ident: string;
  order: number;
  apis: string[];
  note?: string;
  config: {
    id: string;
    parentId: string;
    order: number;
    apis: string[];
    type: MenuType;
    title: string;
    name: string;
    path: string;
    icon: string;
    menu_type: MenuViewType;
    url: string;
    component: string;
    layout: string;
    keepalive: string;
    extend: MenuExtendType;
    note?: string;
    children: MenuMeta["config"][] | null;
    createdTime?: string;
    updatedTime?: string;
  };

  createdTime?: string;
  updatedTime?: string;
}
export enum MenuType {
  menu_dir = "menu_dir",
  menu = "menu",
  button = "button",
}
export const menuType: { label: string; value: keyof typeof MenuType }[] = [
  { label: "菜单目录", value: "menu_dir" },
  { label: "菜单项", value: "menu" },
  { label: "页面按钮", value: "button" },
];
export enum MenuViewType {
  tab = "tab",
  link = "link",
  iframe = "iframe",
}
export const menuViewType: { label: string; value: keyof typeof MenuViewType }[] = [
  { label: "选项卡", value: "tab" },
  { label: "链接（站外）", value: "link" },
  { label: "嵌入", value: "iframe" },
];
export enum MenuExtendType {
  none = "none",
  add_rules_only = "add_rules_only",
  add_menu_only = "add_menu_only",
}
export const menuExtendType: { label: string; value: keyof typeof MenuExtendType }[] = [
  { label: "无", value: "none" },
  { label: "只添加为路由", value: "add_rules_only" },
  { label: "只添加为菜单", value: "add_menu_only" },
];
export type MenuConfigJSON = Omit<MenuItem, "id" | "parentId" | "order" | "apis" | "title" | "name" | "children" | "createdTime" | "updatedTime">;
export type MenuItem = MenuMeta["config"];

export function getMenu(data: { terminal: string } & { [key: string]: unknown }) {
  return request<unknown, Response<(Omit<MenuMeta, "config"> & { config: string })[]>>({
    url: `${SERVER.IAM}/menu/menus_by_terminal`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { terminal: data.terminal },
    data: new URLSearchParams({}),
  }).then((res) => {
    return {
      success: res.success,
      message: res.message,
      page: res.page,
      size: res.size,
      total: res.total,
      data: Array.from(
        res.data
          .map((menu): MenuItem => {
            const config: Omit<MenuItem, "children"> = {
              id: menu.id,
              parentId: menu.parentId,
              order: Number(menu.order),
              apis: menu.apis instanceof Array ? menu.apis : [],
              type: MenuType.menu,
              name: menu.ident,
              title: menu.name,
              path: "",
              icon: "el-icon-Grid",
              menu_type: MenuViewType.tab,
              url: "",
              component: "",
              layout: "",
              keepalive: "",
              extend: MenuExtendType.none,
              note: "",
              createdTime: menu.createdTime,
              updatedTime: menu.updatedTime,
            };

            try {
              const { type: type = config.type, path: path = config.path, icon: icon = config.icon, menu_type: menu_type = config.menu_type, url: url = config.url, component: component = config.component, layout: layout = config.layout, keepalive: keepalive = config.keepalive, extend: extend = config.extend, note: note = config.note } = JSON.parse(menu.config);
              return Object.assign(config, { type, path, icon, menu_type, url, component, layout, keepalive, extend, note, children: type === MenuType.menu_dir ? [] : [] }) as MenuItem;
            } catch (error) {
              return Object.assign(config, { children: [] }) as MenuItem;
            }
          })
          .map((value, _index, full) => {
            if (value.id === value.parentId) return value;
            else {
              return Object.assign(value, {
                children: full
                  .filter(({ parentId }) => parentId === value.id)
                  .map((v) => Object.assign(v, { consume: true }))
                  .sort((a, b) => Number(a.order) - Number(b.order)),
              });
            }
          })
          .filter((v: MenuItem & { consume?: boolean }) => {
            const consume = v.consume;
            if (consume) delete v.consume;
            return !consume;
          })
          .sort((a, b) => Number(a.order) - Number(b.order))
      ),
    };
  });
}
export function addMenu(data: Partial<MenuItem> & { terminal: string; parentId?: string; [key: string]: unknown }) {
  return request<unknown, Response<Omit<MenuMeta, "config"> & { config: string }>>({
    url: `${SERVER.IAM}/menu/create_menu`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {
      terminal: data.terminal,
      parentId: data.parentId || "",
      ident: data.name,
      name: data.title,
      order: data.order,
      apis: data.apis instanceof Array ? data.apis : [],
      config: JSON.stringify({
        type: data.type,
        path: data.path,
        icon: data.icon,
        menu_type: data.menu_type,
        url: data.url,
        component: data.component,
        layout: data.layout,
        keepalive: data.keepalive,
        extend: data.extend,
        note: data.note,
      }),
    },
  });
}
export function modMenu(data: Partial<MenuItem> & { [key: string]: unknown }) {
  return request<unknown, Response<Omit<MenuMeta, "config"> & { config: string }>>({
    url: `${SERVER.IAM}/menu/update_menu`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { id: data.id },
    data: {
      ident: data.name,
      name: data.title,
      order: data.order,
      apis: data.apis instanceof Array ? data.apis : [],
      config: JSON.stringify({
        type: data.type,
        path: data.path,
        icon: data.icon,
        menu_type: data.menu_type,
        url: data.url,
        component: data.component,
        layout: data.layout,
        keepalive: data.keepalive,
        extend: data.extend,
        note: data.note,
      }),
    },
  });
}
export function delMenu(data: Partial<MenuItem> & { [key: string]: unknown }) {
  return request<unknown, Response<Omit<MenuMeta, "config"> & { config: string }>>({
    url: `${SERVER.IAM}/menu/delete_menu`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { id: data.id },
    data: new URLSearchParams({}),
  });
}
export function modMenuByParent(data: { id: string; parentId: string } & { [key: string]: unknown }) {
  return request<unknown, Response<Omit<MenuMeta, "config"> & { config: string }>>({
    url: `${SERVER.IAM}/menu/change_parent`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { id: data.id, parentId: data.parentId },
    data: new URLSearchParams({}),
  });
}
export function modMenuByOrder(data: { orders: { menuId: string; order: number }[] } & { [key: string]: unknown }) {
  return request<unknown, Response<Omit<MenuMeta, "config"> & { config: string }>>({
    url: `${SERVER.IAM}/menu/change_order`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { orders: data.orders instanceof Array ? data.orders : [] },
  });
}
