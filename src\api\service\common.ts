import axios from "axios";
import { i18n } from "@/lang/index";
import moment from "moment";

export enum SERVER {
  IAM = "/iam",
  CS = "/cs",
  EVENT_CENTER = "/ops/event_center",
  CMDB = "/cmdb",
  REPORT_CMDB = "/cc_report/cmdb",
  CC_REPORT = "/cc_report",
  REPORT_IAM = "/cc_report/iam",
  REPORT_EVENT = "/cc_report/event",
}

export enum Method {
  Get = "GET",
  Delete = "DELETE",
  Head = "HEAD",
  Option = "OPTIONS",
  Post = "POST",
  Put = "PUT",
  Patch = "PATCH",
  Purge = "PURGE",
  Link = "LINK",
  Unlink = "UNLINK",
}

export function createUUID() {
  return String(`xxxxxxxx-xxxx-4xxx-yxxx-${Date.now().toString(16).padStart(12, "x")}`).replace(/[xy]/g, (c) => Number(c === "x" ? (Math.random() * 16) | 0 : (((Math.random() * 16) | 0) & 0x3) | 0x8).toString(16));
}

export interface Response<T = unknown> {
  data: T;
  success: boolean;
  date?: import("moment").Moment | null;
  message: string;
  contentType?: string;
  contentDisposition?: {
    [key: string]: string;
    // filename?: string;
  };
}

export interface Response {
  page: number;
  size: number;
  total: number;
}

export interface RequestBase {
  [key: string]: unknown;
  controller?: AbortController;
  keyword?: string;
  paging?: {
    pageNumber?: number;
    pageSize?: number;
  };
  sort?: string[];
}

/**
 * 处理异常
 * @param {*} error
 */
export function httpErrorStatusHandle(error: any): string {
  // 处理被取消的请求
  if (axios.isCancel(error)) {
    // eslint-disable-next-line no-console
    console.error(i18n.global.t("axios.Automatic cancellation due to duplicate request:") + error.message);
    return "";
  }
  let message = "";
  if (error && error.response) {
    switch (error.response.status) {
      case 302:
        message = i18n.global.t("axios.Interface redirected!");
        break;
      case 400:
        message = i18n.global.t("axios.Incorrect parameter!");
        break;
      case 401:
        message = i18n.global.t("axios.You do not have permission to operate!");
        break;
      case 403:
        message = i18n.global.t("axios.You do not have permission to operate!");
        break;
      case 404:
        message = i18n.global.t("axios.Error requesting address:") + "  " + error.response.config.url;
        break;
      case 408:
        message = i18n.global.t("axios.Request timed out!");
        break;
      case 409:
        message = i18n.global.t("axios.The same data already exists in the system!");
        break;
      case 500:
        message = i18n.global.t("axios.Server internal error!");
        break;
      case 501:
        message = i18n.global.t("axios.Service not implemented!");
        break;
      case 502:
        message = i18n.global.t("axios.Gateway error!");
        break;
      case 503:
        message = i18n.global.t("axios.Service unavailable!");
        break;
      case 504:
        message = i18n.global.t("axios.The service is temporarily unavailable Please try again later!");
        break;
      case 505:
        message = i18n.global.t("axios.HTTP version is not supported!");
        break;
      default:
        message = i18n.global.t("axios.Abnormal problem, please contact the website administrator!");
        break;
    }
  }
  if (error.message.includes("timeout")) {
    message = i18n.global.t("axios.Network request timeout!");
  }
  if (error.message.includes("Network")) {
    message = window.navigator.onLine ? i18n.global.t("axios.Server exception!") : i18n.global.t("axios.You are disconnected!");
  }
  return message;
}

export function bindSearchParams(data: Record<string, unknown>, searchParams: URLSearchParams | FormData | Headers = new URLSearchParams({}), prefix = "") {
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      bindSearchParam(data[key], key, searchParams, prefix);
    }
  }
}

export function bindSearchParam(data: unknown, key: string, param: URLSearchParams | FormData | Headers = new URLSearchParams({}), prefix = "") {
  switch (Object.prototype.toString.call(data)) {
    case "[object Undefined]":
      // param.append(`${prefix}${key}`, "");
      break;
    case "[object Null]":
      // param.append(`${prefix}${key}`, "");
      break;
    case "[object Boolean]":
      param.append(`${prefix}${key}`, data ? "true" : "false");
      break;
    case "[object Number]":
      if (!Number.isNaN(data)) param.append(`${prefix}${key}`, String(data));
      break;
    case "[object String]":
      if (data) param.append(`${prefix}${key}`, String(data));
      break;
    case "[object Object]":
      bindSearchParams(data as Record<string, unknown>, param, `${key}.`);
      break;
    case "[object Array]":
      if (param instanceof URLSearchParams) {
        // Array.from(data as unknown[]).forEach((item, index) => bindSearchParam(item, `${key}[${index}]`, param, prefix));
        param.append(`${prefix}${key}`, (data as unknown[]).map((v) => String(v)).join(","));
      } else if (param instanceof Headers) param.append(`${prefix}${key}`, (data as unknown[]).map((v) => String(v)).join(";"));
      else Array.from(data as unknown[]).forEach((item) => bindSearchParam(item, `${key}`, param, prefix));
      break;
    case "[object Date]":
      if (data instanceof Date) {
        const time = moment(data);
        if (time.isValid()) param.append(`${prefix}${key}`, time.format("x"));
      }
      break;
    case "[object File]":
      if (param instanceof FormData) param.append(`${prefix}${key}`, data as File, (data as File).name);
      break;
    case "[object Blob]":
      if (param instanceof FormData) param.append(`${prefix}${key}`, data as Blob);
      break;
    default:
      break;
  }
}
export function bindParamByObj(data: Record<string, unknown>, params: URLSearchParams | FormData | Headers = new URLSearchParams({}), prefix = "") {
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      bindParam(data[key], key, params, prefix);
    }
  }
  return params;
}
export function bindParamByMap(data: Map<any, unknown>, params: URLSearchParams | FormData | Headers = new URLSearchParams({}), prefix = "") {
  for (const [key, value] of data) {
    bindParam(value, String(key), params, prefix);
  }
  return params;
}

export function bindParam(data: unknown, key: string, params: URLSearchParams | FormData | Headers = new URLSearchParams({}), prefix = "") {
  if (data === void 0 || data === null) {
    return params;
  } else if (typeof data === "boolean") {
    params.append(`${prefix}${key}`, data ? "true" : "false");
  } else if (typeof data === "bigint" || typeof data === "number") {
    if (!Number.isNaN(data)) params.append(`${prefix}${key}`, String(data));
  } else if (typeof data === "string") {
    params.append(`${prefix}${key}`, data);
  } else if (data instanceof Object) {
    if (data instanceof Map) {
      bindParamByMap(data, params, `${prefix}${key}.`);
    } else if (data instanceof Array || typeof data[Symbol.iterator] === "function") {
      Array.from(data as Iterable<unknown>).forEach((item) => {
        if (params instanceof URLSearchParams) {
          /* [${index}] */
          bindParam(item, `${key}`, params, prefix);
        } else {
          bindParam(item, `${key}`, params, prefix);
        }
      });
    } else if (data instanceof Date) {
      if (!Number.isNaN(data.getDate())) {
        const year = data.getFullYear();
        const month = data.getMonth() + 1;
        const day = data.getDate();
        const hour = data.getHours();
        // const millihour = hour % 12 === 0 ? 12 : hour % 12;
        const minute = data.getMinutes();
        const second = data.getSeconds();
        // const quarter = Math.floor((month + 3) / 3);
        const milliseconds = data.getMilliseconds();
        // const ap = hour < 12 ? "AM" : "PM";
        /* ============================================ */
        const YYYY = year.toString().padStart(4, "0");
        const MM = month.toString().padStart(2, "0");
        const DD = day.toString().padStart(2, "0");
        const HH = hour.toString().padStart(2, "0");
        const mm = minute.toString().padStart(2, "0");
        const ss = second.toString().padStart(2, "0");
        const SSS = milliseconds.toString().padStart(3, "0");
        params.append(`${prefix}${key}`, `${YYYY}-${MM}-${DD} ${HH}:${mm}:${ss}.${SSS}`);
      }
    } else if (data instanceof Blob) {
      if (params instanceof FormData) params.append(`${prefix}${key}`, data);
    } else if (data.constructor === Object) {
      bindParamByObj(data as Record<string, unknown>, params, `${prefix}${key}.`);
    }
  }
  return params;
}
