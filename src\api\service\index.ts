/* eslint-disable no-console */
import axios, { HttpStatusCode, AxiosError, AxiosHeaders } from "axios";
import type { RawAxiosRequestHeaders, AxiosResponse, InternalAxiosRequestConfig } from "axios";
import { h, reactive } from "vue";
import userInfoConstructorMap from "@/stores/userInfoConstructor";
import { ElMessageBox, ElMessage, ElForm, ElFormItem, ElInput } from "element-plus";
import { SERVER, Method } from "@/api/service/common";
import { useSiteConfig } from "@/stores/siteConfig";
import { httpErrorStatusHandle } from "@/api/service/common";
import router from "@/router/index";
import { setCurrentUserInitialPassword, checkCipher } from "@/api/system";
import moment from "moment";

import { loginResultType } from "@/api/system";

const callbackRefresh: ((value: unknown) => void)[] = [];
function doneRefresh(success: boolean) {
  while (callbackRefresh.length) {
    const callback = callbackRefresh.shift();
    if (typeof callback === "function") callback(success);
  }
}

async function setUpPassword() {
  const info = await getUserInfo();
  const form = reactive<{ loading: boolean; data: Record<"newPassword" | "confirmationPassword", string>; message: string; ref: InstanceType<typeof ElForm> | null }>({
    loading: false,
    message: "",
    data: {
      newPassword: "",
      confirmationPassword: "",
    },
    ref: null,
  });
  const message = h({
    setup(_$props, _$ctx) {
      return () => {
        if (form.message) return h("div", form.message);
        else {
          return h(
            ElForm,
            { model: form.data, labelWidth: 80, labelPosition: "left", ref: ($form) => (form.ref = $form as InstanceType<typeof ElForm>) },
            {
              default() {
                return [
                  h(
                    ElFormItem,
                    {
                      prop: "newPassword",
                      rules: [
                        { required: true, message: "请输入新密码", trigger: "blur" },
                        { pattern: /^((?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*?])|(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])|(?=.*[a-z])(?=.*[0-9])(?=.*[~!@#$%^&*?])|(?=.*[A-Z])(?=.*[0-9])(?=.*[~!@#$%^&*?])).{8,25}$/, message: "密码为8-25位包含英文大小写、数字、特殊字符(~!@#$%^&*?)中的三种", trigger: "blur" },
                      ],
                      label: "密码",
                    },
                    () => h(ElInput, { "type": "password", "autocomplete": "new-password", "showPassword": true, "modelValue": form.data.newPassword, "onUpdate:modelValue": ($event) => (form.data.newPassword = $event) })
                  ),
                  h(
                    ElFormItem,
                    {
                      prop: "confirmationPassword",
                      rules: [
                        { required: true, message: "请输入确认密码", trigger: "blur" },
                        { validator: (rule, value, callback) => callback(value === form.data.newPassword ? undefined : new Error("Error")), message: "两次密码输入不一致", trigger: "blur" },
                      ],
                      label: "确认密码",
                    },
                    () => h(ElInput, { "type": "password", "autocomplete": "new-password", "showPassword": true, "modelValue": form.data.confirmationPassword, "onUpdate:modelValue": ($event) => (form.data.confirmationPassword = $event) })
                  ),
                ];
              },
            }
          );
        }
      };
    },
  });
  try {
    await ElMessageBox({
      title: "设置密码",
      type: "",
      message,
      showCancelButton: true,
      showConfirmButton: true,
      draggable: true,
      showClose: false,
      closeOnClickModal: false,
      closeOnHashChange: false,
      async beforeClose(action, instance, done) {
        if (action === "confirm") {
          instance.confirmButtonLoading = true;
          instance.confirmButtonText = "Loading...";
          const valid = await new Promise((resolve) => form.ref?.validate(resolve));
          if (!valid) {
            instance.confirmButtonLoading = false;
            instance.confirmButtonText = "确认";
            return;
          }
          try {
            const { success, message, data } = await setCurrentUserInitialPassword(form.data);
            if (success) {
              done();
              ElMessage.success("密码修改成功");
              instance.confirmButtonLoading = false;
              instance.confirmButtonText = "确认";
            } else throw Object.assign(new Error(message), { success, data });
          } catch (error) {
            if (error instanceof Error) {
              instance.showConfirmButton = false;
              instance.showInput = false;
              form.message = error.message;
              instance.type = "error";
            }
          } finally {
            instance.confirmButtonLoading = false;
          }
        } else {
          done();
        }
      },
    });
    await info?.updateInfo();
  } catch (error) {
    info?.handleLogout();
    throw Object.assign(new Error(form.message), { form: form.data });
  }
}

async function checkSensitiveOperation() {
  const form = reactive<{ loading: boolean; data: Record<"method" | "verifyCode", string>; message: string; ref: InstanceType<typeof ElForm> | null }>({
    loading: false,
    message: "",
    data: {
      method: "PASSWORD",
      verifyCode: "",
    },
    ref: null,
  });

  const message = h({
    setup(_$props, _$ctx) {
      return () => {
        if (form.message) return h("div", form.message);
        else {
          return h(
            ElForm,
            { model: form.data, labelPosition: "left", ref: ($form) => (form.ref = $form as InstanceType<typeof ElForm>) },
            {
              default() {
                return [
                  h(
                    ElFormItem,
                    {
                      prop: "verifyCode",
                      rules: [
                        { required: true, message: "请输入密码", trigger: "blur" },
                        // { pattern: /^((?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*?])|(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])|(?=.*[a-z])(?=.*[0-9])(?=.*[~!@#$%^&*?])|(?=.*[A-Z])(?=.*[0-9])(?=.*[~!@#$%^&*?])).{8,25}$/, message: "密码为8-25位包含英文大小写、数字、特殊字符(~!@#$%^&*?)中的三种", trigger: "blur" },
                      ],
                      label: "密码",
                    },
                    () => h(ElInput, { "type": "password", "autocomplete": "new-password", "showPassword": true, "modelValue": form.data.verifyCode, "onUpdate:modelValue": ($event) => (form.data.verifyCode = $event) })
                  ),
                ];
              },
            }
          );
        }
      };
    },
  });

  await ElMessageBox({
    title: "校验",
    type: "",
    message,
    showCancelButton: true,
    showConfirmButton: true,
    draggable: true,
    showClose: false,
    closeOnClickModal: false,
    closeOnHashChange: false,
    async beforeClose(action, instance, done) {
      if (action === "confirm") {
        instance.confirmButtonLoading = true;
        instance.confirmButtonText = "Loading...";
        const valid = await new Promise((resolve) => form.ref?.validate(resolve));
        if (!valid) {
          instance.confirmButtonLoading = false;
          instance.confirmButtonText = "确认";
          return;
        }
        try {
          const { success, message, data } = await checkCipher(form.data);
          if (success) {
            done();
            ElMessage.success("校验成功");
            instance.confirmButtonLoading = false;
            instance.confirmButtonText = "确认";
          } else throw Object.assign(new Error(message), { success, data });
        } catch (error) {
          if (error instanceof Error) {
            instance.showConfirmButton = false;
            instance.showInput = false;
            form.message = error.message;
            instance.type = "error";
          }
        } finally {
          instance.confirmButtonLoading = false;
        }
      } else {
        done();
      }
    },
  });
}

async function runApiGuide(ideal: string, config: InternalAxiosRequestConfig, response?: string): Promise<AxiosResponse | undefined> {
  switch (ideal) {
    case "iam.user.password.expired": {
      try {
        await setUpPassword();
        return await requestInstance(config);
      } catch (error) {
        await toBaseRouter();
        break;
      }
    }
    case "iam.user_frozen": {
      try {
        await ElMessageBox.alert(`账号已被冻结至${(JSON.parse(response || "{}").data || {}).frozenExpire}`);
      } catch (error) {
        /*  */
      }
      break;
    }
    case "iam.tenant_forbidden": {
      const userInfo = await getUserInfo();
      const siteConfig = useSiteConfig();
      if (userInfo) {
        userInfo.dataFill({ currentTenantId: "" });
        router.replace({ name: siteConfig.baseInfo?.name, query: { redirect: router.currentRoute.value.fullPath } });
      }
      break;
    }
    case "iam.two_step.unauthorized": {
      // 敏感操作
      await checkSensitiveOperation();
      return await requestInstance(config);
    }
  }
}

const requestInstance = axios.create({
  baseURL: process.env["APP_AXIOS_BASE_URL"] || undefined,
  timeout: 1000 * 120,
  withCredentials: false,
  xsrfCookieName: "XSRF-TOKEN",
  xsrfHeaderName: "X-XSRF-TOKEN",
  headers: {
    post: {},
    delete: {},
    put: {},
    get: {},
    common: {
      Accept: "application/json, application/octet-stream, text/plain, */*",
    },
  },
  // transformRequest: (data, headers) => data,
  // paramsSerializer: (params) => Qs.stringify(params, {arrayFormat: 'brackets'}),
  // transformResponse: (data) => data,
  responseType: "json",
  validateStatus: (status) => status >= 200 && status < 201,
});

// 添加请求拦截器
requestInstance.interceptors.request.use(
  async function (config) {
    const info = await getUserInfo();
    const siteConfig = useSiteConfig();
    const baseGuid = `${Date.now().toString(36).padStart(8, "x")}-xxxx-4xxx-yxxx-xxxxxxxxxxxx`;
    const guidCreate = (unit: string) => (unit === "x" ? (Math.random() * 16) | 0 : (((Math.random() * 16) | 0) & 0x3) | 0x8).toString(16);
    config.headers.set("x-ideal-request-sequence", baseGuid.replace(/[xy]/g, guidCreate));
    config.headers.set("x-ideal-request-timestamp", Date.now());
    config.headers.set("language", localStorage.getItem("systemLang") || "zh-cn");
    if (siteConfig.baseInfo) {
      if (!Object.prototype.hasOwnProperty.call(config.headers, "x-auth-client-token")) {
        config.headers.set("x-auth-client-token", siteConfig.baseInfo.auth);
      }
      config.headers.set("x-ideal-app-id", siteConfig.baseInfo.app);
    }
    if (info) {
      if (!config.url?.includes("user_center/profile") && siteConfig.multiTenant && info.currentTenantId) {
        if (!Object.prototype.hasOwnProperty.call(config.headers, "x-tenant-id")) {
          // console.log("tenant" in router.currentRoute.value.query ? "[QUERY]" : "[USERS]", "tenant" in router.currentRoute.value.query ? (router.currentRoute.value.query.tenant as string) : info.currentTenantId);
          config.headers.set("x-tenant-id", "tenant" in router.currentRoute.value.query ? (router.currentRoute.value.query.tenant as string) : info.currentTenantId);
        }
      }
      if (!Object.prototype.hasOwnProperty.call(config.headers, "Authorization")) {
        if (info.token) {
          Object.assign(config.headers as RawAxiosRequestHeaders, { Authorization: info.token });
        }
      }
    }
    return config;
  },
  async function (error) {
    console.group("%c [ error ] => -30", "font-size:13px; background:pink; color:#bf2c9f;");
    console.dir(error);
    console.groupEnd();
    throw error;
  }
);

// 添加响应拦截器
requestInstance.interceptors.response.use(
  async function ({ config, request, headers, status, statusText }: AxiosResponse) {
    // debuggerLog(request, config);
    if (headers["x-ideal-guide"]) {
      const result = await runApiGuide(headers["x-ideal-guide"] as string, config, request.response);
      if (result) return result;
    }
    const date = headers instanceof AxiosHeaders ? (headers.has("date") ? moment(headers.get("date") as string) : null) : null;
    switch (config.responseType || "text") {
      case "json":
        return ((json) => {
          try {
            return Object.assign(JSON.parse(json), { date });
          } catch (error) {
            return { success: false, date, data: json, message: `${statusText}\n\r服务器故障！请联系管理员！` };
          }
        })(request.response);
      case "blob":
        return {
          success: Number(status) === 200,
          message: statusText,
          contentType: (Object.entries(headers).find(([key]) => String(key).toLowerCase() === "content-type") || [])[1] as string,
          contentDisposition: (((Object.entries(headers).find(([key]) => String(key).toLowerCase() === "content-disposition") || [])[1] || "") as string).split(";").reduce((p, c) => {
            if (/([^=]+)=["]*([^,"]+)["]*[,]*/.test(c.trim())) {
              const [key, value] = c.trim().split("=");
              const filereg = Array.from(c.matchAll(new RegExp("filename[^;=\\n]*=((['\"]).*?\\2|[^;\\n]*)", "ig"))).map(([initial, target]) => decodeURIComponent(/^filename\*=/.test(initial) ? target.replace(/^(.*?)''/, "") : target.replace(/^["|'](.*)["|']$/g, "$1")));
              if (filereg.length) Object.assign(p, { filename: filereg.join(".") });
              else Object.assign(p, { [key]: value.replace(/^["|'](.*)["|']$/g, "$1") });
            } else {
              Object.assign(p, { disposition: c });
            }
            return p;
          }, {}),
          date,
          data: request.response,
        };
      default:
        return {
          success: Number(status) === 200,
          message: statusText,
          date,
          data: request.response,
        };
    }
  },
  async function (error: AxiosError) {
    const { config, response } = error as Required<AxiosError>;
    const message = httpErrorStatusHandle(error);
    if (axios.isCancel(error)) return Promise.resolve({ success: true, message });
    const { request: request = {}, headers: headers = {}, status: status = NaN } = response || {};

    const { useSiteConfig } = await import("@/stores/siteConfig");
    const siteConfig = useSiteConfig();
    if (!siteConfig.baseInfo) return Promise.resolve({ success: false, message });
    const info = userInfoConstructorMap.get(siteConfig.token)!();

    // if (Number(status) > 500) router.push({ name: "notServe", query: router.currentRoute.value.query, params: router.currentRoute.value.params });

    switch (Number(status)) {
      case 200:
        break;
      case 401: {
        const signalRefresh = new Promise((resolve) => callbackRefresh.push(resolve));
        const refreshUrl = `${SERVER.IAM}/login/refresh_token`;
        if (config.url === refreshUrl) throw error;
        else if (callbackRefresh.length > 1) {
          if (await signalRefresh) return await requestInstance(config);
        } else {
          try {
            if (info?.refreshToken) {
              type LoginData = import("@/api/system").LoginData;
              const res = await requestInstance<unknown, import("@/api/service/common").Response<LoginData>>({
                baseURL: process.env["APP_AXIOS_BASE_URL"] || undefined,
                url: refreshUrl,
                method: Method.Post,
                params: { refreshToken: info?.getToken("refresh") },
                data: new URLSearchParams({}),
              });
              if (res.success) {
                const { type } = res.data;
                /*
                  TOKEN :成功执行登录
                  NEED_MFA :登录成功, 但需要进行多因素认证
                  PASSWORD_EXPIRED :登录成功, 但是密码已过期, 需要修改密码
                 */
                switch (type) {
                  case loginResultType.TOKEN: {
                    const token = res.data.token;
                    info?.dataFill({ token: `${token.token_type} ${token.access_token}`, refreshToken: token.refresh_token || "" });
                    break;
                  }
                  // case loginResultType.PASSWORD_EXPIRED: {
                  //   const passwordTicket = res.data.passwordTicket;
                  //   break;
                  // }
                  // case loginResultType.NEED_MFA: {
                  //   const mfaTicket = res.data.mfaTicket;
                  //   break;
                  // }
                  default:
                    throw Object.assign(new Error(res.message), { message: res.message, success: res.success, data: res.data });
                }
              } else {
                throw Object.assign(new Error(res.message), res);
              }
              doneRefresh(true);
              return await requestInstance({
                url: config.url,
                method: config.method,
                responseType: config.responseType,
                headers: { Authorization: info?.getToken("auth") },
                params: config.params,
                data: config.data,
              });
            } else throw new Error("");
          } catch (error) {
            console.dir(error);
            doneRefresh(false);
            info?.handleLogout();
            await toBaseRouter();
          }
        }
        break;
      }
    }
    if (headers["x-ideal-guide"]) {
      const result = await runApiGuide(headers["x-ideal-guide"] as string, config, request.response);
      if (result) return result;
    }
    // debuggerLog(response.request, config);
    let data;
    const date = headers instanceof AxiosHeaders ? (headers.has("date") ? moment(headers.get("date") as string) : null) : null;
    switch (config.responseType || "text") {
      case "json": {
        data = ((json) => {
          try {
            const res = Object.assign(JSON.parse(json), { date });
            if (!res.message) Object.assign(res, { message });
            return res;
          } catch (error) {
            return { success: false, date, data: json, message };
          }
        })(request.response);
        break;
      }
      case "blob": {
        data = {
          success: Number(status) === 200,
          message,
          contentType: (Object.entries(headers).find(([key]) => String(key).toLowerCase() === "content-type") || [])[1] as string,
          contentDisposition: (((Object.entries(headers).find(([key]) => String(key).toLowerCase() === "content-disposition") || [])[1] || "") as string).split(";").reduce((p, c) => {
            if (/([^=]+)=["]*([^,"]+)["]*[,]*/.test(c.trim())) {
              const [key, value] = c.trim().split("=");
              const filereg = Array.from(c.matchAll(new RegExp("filename[^;=\\n]*=((['\"]).*?\\2|[^;\\n]*)", "ig"))).map(([initial, target]) => decodeURIComponent(/^filename\*=/.test(initial) ? target.replace(/^(.*?)''/, "") : target.replace(/^["|'](.*)["|']$/g, "$1")));
              if (filereg.length) Object.assign(p, { filename: filereg.join(".") });
              else Object.assign(p, { [key]: value.replace(/^["|'](.*)["|']$/g, "$1") });
            } else {
              Object.assign(p, { disposition: c });
            }
            return p;
          }, {}),
          date,
          data: request.response,
        };
        break;
      }
      default: {
        data = { date, data: request.response, code: status, message };
        break;
      }
    }
    throw Object.assign(new Error(data.message), { data: data.data, success: false, code: data.code });
  }
);
function tryParseJSON(input: unknown) {
  if (typeof input === "object") input = tryStringifyJSON(input);
  if (typeof input !== "string" || !input) return input;
  try {
    return JSON.parse(input);
  } catch (e) {
    return null;
  }
}
function tryStringifyJSON(input: unknown) {
  if (typeof input === "string") input = tryParseJSON(input);
  if (typeof input !== "object" || !input) return input;
  try {
    return JSON.stringify(input, null, 2);
  } catch (e) {
    return input;
  }
}
function debuggerLog(request?: XMLHttpRequest, config?: InternalAxiosRequestConfig) {
  if (!request) return;
  if (!config) return;
  if (process.env["NODE_ENV"] === "development") {
    const banner = `${config.method?.toUpperCase() || "GET"} ${config.url}`;
    const requestLine = `${banner}${Object.keys(config.params || {}).length ? `?${new URLSearchParams(config.params || {})}` : ""} HTTP/1.1`;
    const requestHeaders = `${config.headers}`;
    const requestData = `${tryStringifyJSON(config.data || "") || ""}`;
    const responseLine = `HTTP/1.1 ${request.status} ${request.statusText}`;
    const responseHeaders = request.getAllResponseHeaders();
    const responseData = `${tryStringifyJSON(request.response || "") || ""}`;
    if (request.status === HttpStatusCode.Ok) {
      console.groupCollapsed(`%c${banner} HTTP/1.1`, "font-size:12px; background:#41b883; color:#35495e; padding:0 6px; border-radius:4px; border:1px solid #35495e;");
      // console.log(`%c###\n\r${requestLine}\n\r${requestHeaders}\n\r\n\r${requestData}\n\r\n\r%c###\n\r${responseLine}\n\r${responseHeaders}\n\r\n\r${responseData}\n\r\n\r`, "font-size:12px; color:#294072;", "font-size:12px; color:#dda0dd;");
    } else {
      console.groupCollapsed(`%c${banner} HTTP/1.1`, "font-size:12px; background:#fff0f0; color:#bf2c9f; padding:0 6px; border-radius:4px; border:1px solid #bf2c9f;");
      // console.log(`%c###\n\r${requestLine}\n\r${requestHeaders}\n\r\n\r${requestData}\n\r\n\r%c###\n\r${responseLine}\n\r${responseHeaders}\n\r\n\r${responseData}\n\r\n\r`, "font-size:12px; color:#294072;", "font-size:12px; color:#fc5531;");
    }
    console.groupEnd();
  }
}

async function getUserInfo() {
  const { useSiteConfig } = await import("@/stores/siteConfig");
  const siteConfig = useSiteConfig();
  return userInfoConstructorMap.get(siteConfig.token)!();
}

async function toBaseRouter() {
  const { useSiteConfig } = await import("@/stores/siteConfig");
  const siteConfig = useSiteConfig();
  if (siteConfig.baseInfo) {
    await router.replace({ name: siteConfig.baseInfo.name, query: { redirect: router.currentRoute.value.fullPath, ...router.currentRoute.value.query }, params: {} });
  } else {
    await router.replace({ path: "/", query: { redirect: router.currentRoute.value.fullPath } });
  }
}

export default requestInstance;
