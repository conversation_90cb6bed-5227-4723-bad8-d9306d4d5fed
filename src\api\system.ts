import { SERVER, Method, bindSearchParams, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";
import { appType, appTypeOption, appTheme, appThemeOption, appTerminal, appTerminalOption, type AppItem, type MenuItem, type NavItem } from "@/api/application";
export { appType, appTypeOption, appTheme, appThemeOption, appTerminal, appTerminalOption, type AppItem, type MenuItem, type NavItem };
import { gender } from "@/api/personnel";
import { useSiteConfig } from "@/stores/siteConfig";
import { base64ToBuffer } from "@/utils/base64";
import { i18n } from "@/lang/index";

// import { useNavTabs } from "@/stores/navTabs";
// import { getFirstRoute } from "@/utils/router";

export enum MFAMethod {
  PASSWORD = "PASSWORD",
  SMS = "SMS",
  EMAIL = "EMAIL",
  TOTP = "TOTP",
}

export const MFAMethodOption: { label: string; value: keyof typeof MFAMethod }[] = [
  { label: i18n.global.t("personalInformation.Password"), value: "PASSWORD" },
  { label: "短信", value: "SMS" },
  { label: "邮箱", value: "EMAIL" },
  { label: "MFA动态码", value: "TOTP" },
];

export enum loginChannels {
  PASSWORD = "PASSWORD",
  REFRESH_TOKEN = "REFRESH_TOKEN",
  SMS_CODE = "SMS_CODE",
  EMAIL_CODE = "EMAIL_CODE",
  GIT_HUB = "GIT_HUB",
  WECHAT = "WECHAT",
  EMAIL = "EAMIL",
}
export const loginChannelsOption: { label: string; value: keyof typeof loginChannels }[] = [
  { label: "密码", value: "PASSWORD" },
  { label: "邮箱", value: "EAMIL" as any },
  { label: "Refresh Token", value: "REFRESH_TOKEN" },
  { label: "手机号", value: "SMS_CODE" },
  { label: "GitHub", value: "GIT_HUB" },
  { label: "微信", value: "WECHAT" },
];

export interface PlatformEnv {
  openName: string;
  multiTenant: boolean;
  registrable: boolean;
  ownerId: string;
  config: string;
  footer: string;
  loginChannels: (keyof typeof loginChannels)[];
  token: string;
  origin: string;
  primary: string;
  platformInformation: { showCarousel: boolean };
}
// export function getConfig(data: RequestBase) {
//   const siteConfig = useSiteConfig();
//   return request<unknown, Response<PlatformEnv>>({
//     url: `${SERVER.IAM}/platforms/current/env`,
//     method: Method.Get,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {
//       Authorization: siteConfig.baseInfo?.auth,
//     },
//     params: {},
//     data: {},
//   });
// }

export enum loginResultType {
  TOKEN = "TOKEN",
  NEED_MFA = "NEED_MFA",
  PASSWORD_EXPIRED = "PASSWORD_EXPIRED",
  SELECT_ACCOUNT = "SELECT_ACCOUNT",
  PASSWORD_ILLEGAL = "PASSWORD_ILLEGAL",
}
export const loginResultTypeOption: { label: string; value: keyof typeof loginResultType }[] = [
  { label: "成功登录", value: loginResultType.TOKEN },
  { label: "需要进行多因素认证", value: loginResultType.NEED_MFA },
  { label: "密码已过期,请重置", value: loginResultType.PASSWORD_EXPIRED },
  { label: "需要修改密码", value: loginResultType.PASSWORD_ILLEGAL },
  { label: "请选择一个账号", value: loginResultType.SELECT_ACCOUNT },
];
export type LoginData =
  /* Token */
  | { type: loginResultType.TOKEN; token: { token_type: string; access_token: string; refresh_token: string } }
  /* MFA */
  | { type: loginResultType.NEED_MFA; mfaTicket: { ticket: string; methods: (keyof typeof MFAMethod)[] } }
  /* 重置密码 */
  | { type: loginResultType.PASSWORD_EXPIRED; passwordTicket: { ticket: string } }
  /* 选择账号 */
  | { type: loginResultType.SELECT_ACCOUNT; selectAccountTicket: { ticket: string; accounts: Record<"uid" | "account" | "phone" | "email" | "registrationTime" | "lastLoginTime", string>[] } }
  | { type: loginResultType.PASSWORD_ILLEGAL; passwordTicket: { ticket: string } };

export interface TokenInfo {
  token_type: string;
  access_token: string;
  refresh_token: string;
  expires_in: string;
}

/*  PASSWORD :密码验证 CODE :邮箱/短信 验证码 TOTP :动态口令(Time-based One-Time Password)x */
export function loginByMFA(data: { ticket: string; method: "PASSWORD" | "SMS" | "EMAIL" | "TOTP"; code: string } & RequestBase) {
  const siteConfig = useSiteConfig();
  return request<unknown, Response<LoginData>>({
    url: `${SERVER.IAM}/authentication/sign_in/mfa`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: { Authorization: siteConfig.baseInfo?.auth },
    params: {},
    data: {
      ticket: data.ticket,
      method: data.method,
      code: data.code,
    },
  });
}

export function getExchangeCode(data: { [key: string]: unknown }) {
  return request<never, Response<string>>({
    url: `${SERVER.IAM}/authorization/exchange_code`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: { landingPage: data.service },
    data: new URLSearchParams({}),
  });
}
export function logout(data: { [key: string]: unknown }): Promise<Response<{}>> {
  return request({
    url: `${SERVER.IAM}/logout`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: new URLSearchParams({}),
  });
}

/**
 * @description 完善个人信息
 * @url http://*************:3000/project/11/interface/api/2701
 */
export function /* 完善个人信息 */ initUserinfo(req: Partial<Record<"smsCode" | "emailCode", string>> & { name?: string; nickName?: string; account?: string; phone?: string; email?: string; gender?: keyof typeof gender; language?: string } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({ smsCode: req.smsCode || null /* 短信验证码, 手机号为空或者短信功能不可用时可以为空 */, emailCode: req.emailCode || null /* 邮件验证码, 邮箱为空或者邮件功能不可用时可以为空 */ }, params);

  const data = { name: req.name /* 姓名, 未设置则必填 */, nickName: req.nickName /* 昵称 */, account: req.account /* 个人账号, 已设置则忽略 */, phone: req.phone /* 手机号码, 已设置则忽略 */, email: req.email /* 邮箱, 已设置则忽略 */, gender: req.gender /* 性别枚举：SECRET :保密、MALE :男性、FEMALE :女性 */, language: req.language /* 语言 */ };

  return request<never, Response<null>>({ url: `${SERVER.IAM}/current_user/information/improve`, method: Method.Put, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 发送通用短信验证码
 * @url http://*************:3000/project/11/interface/api/2743
 */
export function /* 发送通用短信验证码 */ sendSMSCode(req: Partial<Record<"phone", string>> & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/x-www-form-urlencoded");

  const params = new URLSearchParams({});
  bindSearchParams({ phone: req.phone /* 手机号 */ }, params);

  const data = new URLSearchParams({});

  return request<never, Response<null>>({ url: `${SERVER.IAM}/send_common_sms_code`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 发送通用邮箱验证码
 * @url http://*************:3000/project/11/interface/api/2745
 */
export function /* 发送通用邮箱验证码 */ sendEMAILCode(req: Partial<Record<"email", string>> & RequestBase) {
  // const header = new Headers();
  // header.set("x-auth-client-token", "undefined");
  // header.set("Content-Type", "application/x-www-form-urlencoded");

  const params = new URLSearchParams({});
  bindSearchParams({ email: req.email /* 邮箱地址 */ }, params);

  const data = new URLSearchParams({});

  return request<never, Response<null>>({ url: `${SERVER.IAM}/send_common_email_code`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
export async function requireCaptcha(data: { username: string } & RequestBase) {
  const siteConfig = useSiteConfig();
  try {
    const res = await request<unknown, Response<boolean>>({
      url: `${SERVER.IAM}/login/password/need_captcha`,
      method: Method.Get,
      responseType: "json",
      signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
      params: { username: data.username, clientToken: siteConfig.baseInfo?.auth },
      data: {},
    });
    if (!res.success) throw Object.assign(new Error(res.message), { success: res.success, data: res.data });
    return res.data;
  } catch (error) {
    return true;
  }
}
export function captchaForImage(data: { [key: string]: unknown; certificate: string }): Promise<Response<Blob>> {
  const siteConfig = useSiteConfig();
  return request({
    url: `${SERVER.IAM}/captcha/image`,
    method: Method.Get,
    responseType: "blob",
    // url: `${SERVER.IAM}/captcha/base64_image_code`,
    // method: Method.Get,
    // responseType: "text",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: { Authorization: siteConfig.baseInfo?.auth },
    params: { certificate: data.certificate },
    data: new URLSearchParams({}),
  });
}
export function getGUID(data: { [key: string]: unknown }): Promise<Response<string>> {
  const siteConfig = useSiteConfig();
  return request({
    url: `${SERVER.IAM}/captcha/certificate`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: { Authorization: siteConfig.baseInfo?.auth },
    params: {},
    data: new URLSearchParams({}),
  });
}
export function cutUserPasswordReset(data: { id: string; [key: string]: unknown }): Promise<Response<unknown>> {
  return request({
    url: `${SERVER.IAM}/users/${data.id}/password/reset`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { password: data.password, ptype: data.ptype },
  });
}
// export function setCurrentUserPassword(data: { mfaMethod: keyof typeof MFAMethod; mfaCode: string; newPassword: string; confirmationPassword: string; [key: string]: unknown }): Promise<Response<unknown>> {
//   return request({
//     url: `${SERVER.IAM}/current_user/password`,
//     method: Method.Patch,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: {
//       mfaMethod: data.mfaMethod,
//       mfaCode: data.mfaCode,
//       newPassword: data.newPassword,
//       confirmationPassword: data.confirmationPassword,
//     },
//   });
// }
export function setCurrentUserInitialPassword(data: { newPassword: string; confirmationPassword: string; [key: string]: unknown }): Promise<Response<unknown>> {
  return request({
    url: `${SERVER.IAM}/current_user/self/set_password`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {
      newPassword: data.newPassword,
      confirmationPassword: data.confirmationPassword,
    },
  });
}

export function checkCipher(data: { method: string; verifyCode: string } & RequestBase): Promise<Response<unknown>> {
  return request({
    url: `${SERVER.IAM}/sensitive_operation/verify`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {
      method: data.method,
      verifyCode: data.verifyCode,
    },
  });
}

export async function isSuper /* 是否为超管 */(data: { [key: string]: unknown }): Promise<boolean> {
  try {
    const res = await request<boolean, Response<boolean>>({
      url: `${SERVER.IAM}/current_user/is_super_admin`,
      method: Method.Get,
      responseType: "json",
      signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    });
    if (res.success) {
      return Boolean(res.data);
    } else throw Object.assign(new Error(res.message), res);
  } catch (error) {
    return false;
  }
}

export async function getAvatar /* 获取用户头像 */(data: { [key: string]: unknown; filePath?: string }): Promise<string> {
  try {
    if (!data.filePath) throw new Error("Not Avatar");
    const res = await request<boolean, Response<Blob>>({
      url: `${SERVER.IAM}/profile_picture/view/${data.filePath}`,
      method: Method.Get,
      responseType: "blob",
      signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    });
    if (res.success && res.data instanceof Blob) {
      return URL.createObjectURL(res.data);
    } else throw Object.assign(new Error(res.message), res);
  } catch (error) {
    const avatar = await import("@/assets/avatar.png");
    return avatar.default;
  }
}
export async function getAvatarUrlList /* 获取预置头像 */(data: { [key: string]: unknown }) {
  return request<boolean, Response<string[]>>({
    url: `${SERVER.IAM}/profile_picture/statics`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  });
}
export async function setAvatar /* 设置头像 */(data: { [key: string]: unknown; file?: File; filename?: string }) {
  if (data.file instanceof File) {
    return request<boolean, Response<string[]>>({
      url: `${SERVER.IAM}/profile_picture/user/current/custom`,
      method: Method.Put,
      responseType: "json",
      signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
      data: Object.entries({ file: data.file }).reduce((p, [k, v]) => (p.append(k, v), p), new FormData()),
    });
  } else {
    return request<boolean, Response<string[]>>({
      url: `${SERVER.IAM}/profile_picture/user/current/static`,
      method: Method.Put,
      responseType: "json",
      signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
      data: { filename: data.filename },
    });
  }
}

export interface GetUserInfo {
  id: string;
  userId: string /* 用户ID */;
  tenantId?: string /* 用户所属的租户ID */;
  name?: string /* 姓名 */;
  // nickname?: string /* 昵称 */;
  account?: string /* 账号 */;
  phone?: string /* 手机号码 */;
  email?: string /* 邮箱 */;
  language?: import("@/api/locale").Locales /* 语言 */;
  gender?: "SECRET" | "MALE" | "FEMALE" /* 性别 枚举类型: SECRET :保密 | MALE :男性 | FEMALE :女性 */;
  profilePicture?: string /* 头像 */;
  busy: boolean /* 是否忙碌状态 */;
  busyTime?: string /* 进入忙碌状态的时间戳 */;
  improved: boolean /* 是否已完善个人信息 */;
  improvedTime?: string /* 完善个人信息时间 */;
  registrationTime?: string /* 账号注册时间 */;
  switchedTenantId?: string /* 当前切换的租户ID */;
  tenants?: /* 用户所在的租户列表, 多租户平台有效 */ TenantItem[];
  zoneId: string;
  loginTime: string;
  tenantabbreviation: string;
  accountExpirationDate: string;
  containerId: string /* 安全容器ID */;
  // passwordDate?: string /* 密码修改日期 */;
  // owner?: boolean /* 是否平台拥有人, 非多租户平台有效 */;
  // superAdmin?: boolean /* 用户在平台下是否为超管角色, 非多租户平台有效 */;
  // roleIds?: string[] /* 角色列表, 非多租户平台有效 */;
  // frozen: boolean;
  // frozenExpire?: string;
  // frozenNote?: string;
  // createdTime?: string;
  // updatedTime?: string;
  // multiTenant?: boolean;
}

/**
 * @description 获取个人信息
 * @url http://*************:3000/project/11/interface/api/2699
 */
export function /* 获取个人信息 */ getUserInfo(req: { token: string; tenant?: string } & RequestBase) {
  const params = new URLSearchParams();
  bindSearchParams({ permissionId: ["513160392680144896"].join() }, params);
  return request<never, Response<GetUserInfo>>({
    url: `${SERVER.IAM}/current_user/profile`,
    method: Method.Get,
    responseType: "json",
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: { Authorization: req.token, ...(req.tenant ? { "x-tenant-id": req.tenant } : {}) },
    params,
    data: new URLSearchParams({}),
  });
}
/**
 * @description 获取个人信息
 * @url http://*************:3000/project/11/interface/api/2699
 */
export function /* 获取个人信息 */ getRawUserInfo(req: { token: string; tenant?: string } & RequestBase) {
  return request<never, Response<GetUserInfo>>({
    url: `${SERVER.IAM}/current_user/sensitive_info`,
    method: Method.Get,
    responseType: "json",
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: { Authorization: req.token, ...(req.tenant ? { "x-tenant-id": req.tenant } : {}) },
  });
}
/**
 * @description 获取当前用户可访问客户列表
 * @url http://*************:3000/project/11/interface/api/21141
 */
export function /* 获取当前用户可访问客户列表 */ getUserTenants(req: { token: string; tenant?: string } & RequestBase) {
  const params = new URLSearchParams();
  bindSearchParams({ permissionId: ["513160392680144896"].join() }, params);
  return request<never, Response<TenantItem>>({
    url: `${SERVER.IAM}/current_user/visible_tenants`,
    method: Method.Get,
    responseType: "json",
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: { Authorization: req.token, ...(req.tenant ? { "x-tenant-id": req.tenant } : {}) },
    params,
    data: new URLSearchParams({}),
  });
}
export function cutUserBusy /* 切换忙碌状态 */(data: { value: boolean; [key: string]: unknown }) {
  return request<unknown, Response<null>>({
    url: `${SERVER.IAM}/current_user/busy`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { value: data.value },
    data: new URLSearchParams({}),
  });
}
export interface GetUserLogs {
  id: string /* 主键 */;
  userId: string /* 用户ID */;
  platform: string /* 平台编码 */;
  clientId: string /* 授权客户端id */;
  clientName: string /* 授权客户端名称 */;
  ip: string /* 登录ip地址 */;
  location: string /* 登录场所 */;
  userAgent: string /* 浏览器UA */;
  loginTime: string /* 登录时间 */;
}
export function getUserLogs /* 获取登录日志 */(data: { paging: { page: number; size: number }; [key: string]: unknown }): Promise<Response<GetUserLogs[]>> {
  return request({
    url: `${SERVER.IAM}/log/current_user/login_logs`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { pageNumber: data.paging.page, pageSize: data.paging.size },
    data: new URLSearchParams({}),
  });
}
/**
 * @description 获取用户在指定应用下的权限ID列表
 * @url http://*************:3000/project/11/interface/api/2835
 */
export function /* 获取用户在指定应用下的权限ID列表 */ getUserPermission(req: RequestBase) {
  return request<never, Response<string[]>>({
    // url: `${SERVER.IAM}/front/current_user/apps/${req.appId /* 应用ID */}/available_permission_ids`,
    url: `${SERVER.IAM}/security_container/current_user/available_permission_ids?appId=${req.appId}`,
    method: Method.Get,
    responseType: "json",
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
    headers: { ...(req.tenant ? { "x-tenant-id": <string>req.tenant } : {}) },
    params: new URLSearchParams({}),
    data: new URLSearchParams({}),
  });
}
export function getStaticApp() {
  return {
    desktop: {
      id: "",
      rootId: "",
      parentId: "",
      path: "",
      terminal: appTerminal.WEB,
      title: "",
      enTitle: "",
      name: "",
      order: 0,
      icon: "local-SystemApps-line",
      type: appType.ROUTE,
      theme: appTheme.DESKTOP,
      url: "",
      component: "",
      keepalive: false,
      enabled: true,
      permission: [],
      note: "",
      version: "",
      config: "{}",
      hash: "",
      query: {},
      params: {},
      pattern: /(?:)/,
      names: [],
      children: [
        /*  */
        { id: "home", rootId: "home", parentId: "", path: "home", terminal: appTerminal.WEB, title: "桌面", name: "home", order: -1, icon: "local-DeviceComputer-line", type: appType.MENU, theme: appTheme.DESKTOP, url: "", component: "desktop/index.vue", keepalive: false, enabled: true, permission: [], note: "", version: "2", config: "{}", hash: "", query: {}, params: {}, pattern: /(?:)/, names: [], children: [], permissionGroups: [[], []] },
        {
          id: "permission",
          rootId: "permission",
          parentId: "",
          path: "permission",
          terminal: appTerminal.WEB,
          title: "用户权限",
          name: "permission",
          order: Infinity,
          icon: "local-SystemShield-keyhole-line",
          type: appType.DIR,
          theme: appTheme.DESKTOP,
          url: "",
          component: "routine/userInfo/index.vue",
          keepalive: false,
          enabled: true,
          permission: [],
          note: "",
          version: "2",
          config: "{}",
          hash: "",
          query: {},
          params: {},
          pattern: /(?:)/,
          names: [],
          children: [
            /*  */
            { id: "permission/user", rootId: "permission/user", parentId: "", path: "permission/user", terminal: appTerminal.WEB, title: "用户管理", name: "permission/user", order: Infinity, icon: "local-UserGroup-line", type: appType.MENU, theme: appTheme.DESKTOP, url: "", component: "permission/user/index.vue", keepalive: false, enabled: true, permission: [], note: "", version: "2", config: "{}", hash: "", query: {}, params: {}, pattern: /(?:)/, names: [], children: [], permissionGroups: [[], []] },
            { id: "permission/group", rootId: "permission/group", parentId: "", path: "permission/group", terminal: appTerminal.WEB, title: "用户组", name: "permission/group", order: Infinity, icon: "local-UserTeam-line", type: appType.MENU, theme: appTheme.DESKTOP, url: "", component: "permission/group/index.vue", keepalive: false, enabled: true, permission: [], note: "", version: "2", config: "{}", hash: "", query: {}, params: {}, pattern: /(?:)/, names: [], children: [], permissionGroups: [[], []] },
            { id: "permission/role", rootId: "permission/role", parentId: "", path: "permission/role", terminal: appTerminal.WEB, title: "角色管理", name: "permission/role", order: Infinity, icon: "local-UserAccount-box-line", type: appType.MENU, theme: appTheme.DESKTOP, url: "", component: "permission/role/index.vue", keepalive: false, enabled: true, permission: [], note: "", version: "2", config: "{}", hash: "", query: {}, params: {}, pattern: /(?:)/, names: [], children: [], permissionGroups: [[], []] },
          ],
          permissionGroups: [[], []],
        },
        {
          id: "tenant",
          rootId: "tenant",
          parentId: "",
          path: "tenant",
          terminal: appTerminal.WEB,
          title: "租户管理",
          name: "tenant",
          order: Infinity,
          icon: "local-UserTeam-line",
          type: appType.DIR,
          theme: appTheme.DESKTOP,
          url: "",
          component: "routine/userInfo/index.vue",
          keepalive: false,
          enabled: true,
          permission: [],
          note: "",
          version: "2",
          config: "{}",
          hash: "",
          query: {},
          params: {},
          pattern: /(?:)/,
          names: [],
          children: [
            /*  */
            { id: "tenant/govern", rootId: "tenant/govern", parentId: "", path: "tenant/govern", terminal: appTerminal.WEB, title: "租户", name: "tenant/govern", order: Infinity, icon: "local-DeviceComputer-line", type: appType.MENU, theme: appTheme.DESKTOP, url: "", component: "tenant/govern/index.vue", keepalive: false, enabled: true, permission: [], note: "", version: "2", config: "{}", hash: "", query: {}, params: {}, pattern: /(?:)/, names: [], children: [] },
          ],
          permissionGroups: [[], []],
        },
      ],
      permissionGroups: [[], []],
    },
  } as any as Record<string, NavItem>;
}
// 获取当前用户可访问的应用列表
export function getUserAvailableApps(data: { [key: string]: unknown }) {
  return request<unknown, Response<AppItem[]>>({
    url: `${SERVER.IAM}/front/apps/current_user/available`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  }).then<Response<NavItem[]>>((res) => {
    return {
      success: res.success,
      message: res.message,
      page: res.page,
      size: res.size,
      total: res.total,
      data: Array.from(
        res.data
          .map((menu): NavItem => {
            const config: Required<NavItem> = {
              id: menu.id,
              rootId: menu.id,
              parentId: "",
              path: menu.rootPath,
              terminal: menu.terminal,
              title: menu.name,
              enTitle: menu.enName,
              name: menu.id,
              order: Number(menu.orderNum),
              icon: "local-SystemApps-line",
              type: appType.ROUTE,
              theme: appTheme.BASE,
              url: "",
              component: "",
              keepalive: false,
              enabled: true,
              permission: menu.permissionIds instanceof Array ? menu.permissionIds : [],
              note: menu.note,
              version: menu.version,
              config: menu.config,
              hash: "",
              query: {},
              params: {},
              pattern: /(?:)/,
              names: [],
              children: [],
              createdTime: menu.createdTime as string,
              updatedTime: menu.updatedTime as string,
              permissionGroups: menu.permissionGroups instanceof Array ? menu.permissionGroups : [[], []],
            };
            try {
              const { type: type = config.type, theme: theme = config.theme, icon: icon = config.icon, url: url = config.url, component: component = config.component, keepalive: keepalive = config.keepalive } = JSON.parse(menu.config);
              return Object.assign(config, { type, theme, icon, url, component, keepalive });
            } catch (error) {
              return config;
            }
          })
          .sort((a, b) => Number(a.order) - Number(b.order))
      ),
    };
  });
}
export function getUserApps /* 获取应用信息 */(data: { [key: string]: unknown }) {
  return request<unknown, Response<AppItem[]>>({
    url: `${SERVER.IAM}/front/platform/current/apps`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: { ...(data.tenant ? { "x-tenant-id": <string>data.tenant } : {}) },
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  }).then<Response<NavItem[]>>((res) => {
    return {
      success: res.success,
      message: res.message,
      page: res.page,
      size: res.size,
      total: res.total,
      data: Array.from(
        res.data
          .map((menu): NavItem => {
            const config: Required<NavItem> = {
              id: menu.id,
              rootId: menu.id,
              parentId: "",
              path: menu.rootPath,
              terminal: menu.terminal,
              title: menu.name,
              enTitle: menu.enName,
              name: menu.id,
              order: Number(menu.orderNum),
              icon: "local-SystemApps-line",
              type: appType.ROUTE,
              theme: appTheme.BASE,
              url: "",
              component: "",
              keepalive: false,
              enabled: true,
              permission: menu.permissionIds instanceof Array ? menu.permissionIds : [],
              note: menu.note,
              version: menu.version,
              config: menu.config,
              hash: "",
              query: {},
              params: {},
              pattern: /(?:)/,
              names: [],
              children: [],
              createdTime: menu.createdTime as string,
              updatedTime: menu.updatedTime as string,
              permissionGroups: menu.permissionGroups instanceof Array ? menu.permissionGroups : [[], []],
            };
            try {
              const { type: type = config.type, theme: theme = config.theme, icon: icon = config.icon, url: url = config.url, component: component = config.component, keepalive: keepalive = config.keepalive } = JSON.parse(menu.config);
              return Object.assign(config, { type, theme, icon, url, component, keepalive });
            } catch (error) {
              return config;
            }
          })
          .sort((a, b) => Number(a.order) - Number(b.order))
      ),
    };
  });
}

enum OrderMenuId {
  event = "508096330019635200",
  service = "508099540994228224",
  question = "508192718296449024",
  change = "508192394424877056",
  publish = "519816202709630976",
  dictevent = "689688033594703872",
  dictservice = "690071929381453824",
}

export function getOrderMenuType(data: {} & RequestBase) {
  return request<unknown, Response<string[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_templates/menu`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: { ...(data.tenant ? { "x-tenant-id": <string>data.tenant } : {}) },
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function getOrderNewMenuType(data: {} & RequestBase) {
  return request<unknown, Response<string[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_templates/queryMenu`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: { ...(data.tenant ? { "x-tenant-id": <string>data.tenant } : {}) },
    params: data,
    data: {},
  });
}
export async function getUserMenu /* 获取菜单信息 */(data: { appId: string; [key: string]: unknown }) {
  const orderMenuId = [];
  const userInfo = (await import("@/utils/getUserInfo")).default();
  const params = {
    containerId: (userInfo.currentTenant || {}).containerId,
    queryPermissionId: "733532355297280000",
    verifyPermissionIds: "",
  };

  try {
    // const { data, message, success } = await getOrderMenuType({});
    const { data, message, success } = await getOrderNewMenuType(params);
    if (!success) throw new Error(message);
    data.forEach((v) => orderMenuId.push(OrderMenuId[v as never]));
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
  return request<unknown, Response<(Omit<MenuItem, "config"> & { config: string })[]>>({
    // url: `${SERVER.IAM}/front/apps/${data.appId}/current_user/available_menus`,
    url: `${SERVER.IAM}/front/menus`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? (data.controller as AbortController).signal : undefined,
    headers: { ...(data.tenant ? { "x-tenant-id": <string>data.tenant } : {}) },
    params: { appId: data.appId },
    data: new URLSearchParams({}),
  }).then<Response<NavItem[]>>((res) => {
    return {
      success: res.success,
      message: res.message,
      page: res.page,
      size: res.size,
      total: res.total,
      data: Array.from(
        res.data
          .filter((menu) => menu.enabled)
          .map((menu): NavItem => {
            const config: Required<NavItem> = {
              id: menu.id,
              rootId: menu.appId,
              parentId: menu.parentId,
              path: "",
              terminal: appTerminal.WEB,
              title: localStorage.getItem("systemLang") === "zh-cn" ? menu.name : menu.enName || menu.name,
              enTitle: menu.enName,
              name: menu.id,
              order: Number(menu.orderNum),
              icon: "local-SystemApps-line",
              type: appType.ROUTE,
              theme: appTheme.BASE,
              url: "",
              component: "",
              keepalive: false,
              enabled: menu.enabled,
              permission: menu.permissionIds instanceof Array ? menu.permissionIds : [],
              note: menu.note,
              version: menu.version,
              config: menu.config,
              hash: "",
              query: {},
              params: {},
              pattern: /(?:)/,
              names: [],
              children: [],
              createdTime: menu.createdTime as string,
              updatedTime: menu.updatedTime as string,
              permissionGroups: menu.permissionGroups instanceof Array ? menu.permissionGroups : [[], []],
            };

            try {
              const { type: type = config.type, path: path = config.path, icon: icon = config.icon, url: url = config.url, component: component = config.component, keepalive: keepalive = config.keepalive } = JSON.parse(menu.config);
              return Object.assign(config, { type, path, icon, url, component, keepalive });
            } catch (error) {
              return config;
            }
          })
          .map((value, _index, full) => {
            if (value.id === value.parentId) return value;
            else {
              return Object.assign(value, {
                children: full
                  .filter(({ parentId }) => parentId === value.id)
                  .map((v) => Object.assign(v, { consume: true }))
                  .sort((a, b) => Number(a.order) - Number(b.order)),
              });
            }
          })
          .filter((v: NavItem & { consume?: boolean }) => {
            const consume = v.consume;
            if (consume) delete v.consume;
            return !consume;
          })
          .sort((a, b) => Number(a.order) - Number(b.order))
      ).filter((v) => {
        /** 工单菜单ID */
        const orderMenu: string[] = [OrderMenuId.change, OrderMenuId.dictevent, OrderMenuId.dictservice, OrderMenuId.event, OrderMenuId.publish, OrderMenuId.question, OrderMenuId.service];
        /** 智能事件中心ID */
        const eventContentMenuId = "508095824115269632";
        if ([eventContentMenuId].includes(v.id)) {
          // /*  */
          v.children = (v.children || []).filter((child) => {
            if (orderMenu.includes(child.id)) {
              /*  */
              return orderMenuId.includes(child.id as never);
            } else return true;
          });
        }
        return true;
      }),
    };
  });
}
// export function getUserConf /* 获取配置信息 */(data: { keys?: string[]; [key: string]: unknown }): Promise<Response<{}>> {
//   return request({
//     url: `${SERVER.IAM}/user/self/load_configs`,
//     method: Method.Get,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: { keys: (data.keys instanceof Array ? data.keys : ["layout", "lang"]).join() },
//     data: new URLSearchParams({}),
//   });
// }
export function getMFAMethods /* 获取可用的MFA认证方式 */(data: { keys?: string[]; [key: string]: unknown }, token = "") {
  return request<unknown, Response<(keyof typeof MFAMethod)[]>>({
    url: `${SERVER.IAM}/current_user/mfa/available_methods`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: token ? { Authorization: token || undefined } : {},
    params: {},
    data: new URLSearchParams({}),
  });
}
export function getAuthPlats /* 获取已绑定第三方平台编码列表 */(data: { [key: string]: unknown }) {
  return request<unknown, Response<(keyof typeof loginChannels)[]>>({
    url: `${SERVER.IAM}/current_user/auth_plats`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: new URLSearchParams({}),
  });
}
export function delAuthPlats /* 获取已绑定第三方平台编码列表 */(data: { platCode: keyof typeof loginChannels; [key: string]: unknown }) {
  return request<unknown, Response<(keyof typeof loginChannels)[]>>({
    url: `${SERVER.IAM}/current_user/auth_plats/${data.platCode}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: new URLSearchParams({}),
  });
}

export function getUserTerm /* 获取终端信息 */(_data: { [key: string]: unknown }): Promise<Response<{ port: string; manager: string }>> {
  return new Promise((resolve) => setTimeout(resolve, 200, { sussess: true, data: { port: "", manager: "" }, message: "此Api暂无开发" }));
  // return request({
  //   url: `${SERVER.IAM}`,
  //   method: Method.Get,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   headers: {},
  //   params: {},
  //   data: new URLSearchParams({}),
  // })
}

export interface TenantItem {
  id: string /* 租户ID */;
  containerId: string /* 安全容器ID */;
  name: string /* 租户名称 */;
  abbreviation: string /* 租户缩写 */;
  systemEdition: string /* 系统版本 */;
  zoneId?: string /* 时区ID */;
  language?: string /* 语言 */;
  address?: string /* 地址 */;
  note?: string /* 备注 */;
  fullPermission: boolean /* 是否拥有完整权限 */;
  mfaState: "ENABLED" | "DISABLED" | "DEFAULT" /* 双因素认证启用状态 枚举类型: ENABLED :启用MFA认证 | DISABLED :禁用MFA认证 | DEFAULT :使用系统默认配置 */;
  ownerId?: string /* 拥有人用户ID */;
  ownerName?: string /* 拥有人姓名 */;
  ownerNickname?: string /* 拥有人昵称 */;
  ownerAccount?: string /* 拥有人账号 */;
  ownerPhone?: string /* 拥有人手机号 */;
  ownerEmail?: string /* 拥有人邮箱 */;
  blocked: boolean /* 租户是否已被锁定 */;
  activated: boolean /* 租户是否已激活 */;
  version: string /* 乐观锁版本 */;
  createdTime: string /* 创建时间 */;
  updatedTime: string /* 更新时间 */;
  createdBy?: string /* 创建人信息 */;
  updatedBy?: string /* 更新人信息 */;
  tenantPostcode?: string /* 租户邮编 */;
  tenantEmail?: string /* 租户邮箱 */;
  tenantFax?: string /* 租户传真 */;
  tenantPhone?: string /* 租户电话 */;
  tenantType?: string /* 租户类型 */;
  tenantSigningPlace?: string /* 租户签约地 */;
  tenantSigningPlace2?: string /* 租户签约地2 */;
  tenantIndustry?: string /* 租户行业 */;
  tenantNature?: string /* 租户性质 */;
  tenantChannel?: string /* 租户渠道 */;
  tenantOperator: boolean /* 租户是否运营商 */;
  tenantForeign: boolean /* 租户是否运营商 */;
  enableMfaDefault: boolean /* 是否默认启用了MFA */;
}
export function cutCurrentTenant /* 切换租户 */(data: { [key: string]: unknown }) {
  return request<unknown, Response<null>>({
    url: `${SERVER.IAM}/tenants/${data.tenantId}/switch`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: new URLSearchParams({}),
  });
}
export function getUserHire /* 获取租户信息 */(data: { [key: string]: unknown }) {
  return request<unknown, Response<TenantItem[]>>({
    url: `${SERVER.IAM}/tenants`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: new URLSearchParams({}),
  });
}
export function /* 生成动态密钥二维码 */ getTOTPGenerate(req: RequestBase, token = "") {
  // const header = new Headers();
  // header.set("Content-Type", "application/x-www-form-urlencoded");

  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  const data = new URLSearchParams({});

  return request<never, Response<string>>({ url: `${SERVER.IAM}/current_user/totp/generate`, method: Method.Post, responseType: "text", signal: req.controller instanceof AbortController ? req.controller.signal : undefined, headers: token ? { Authorization: token } : {}, params, data });
}
export function setUserInfo(data: { name?: string; gender?: string; birthday?: string } & RequestBase) {
  return request<unknown, Response<string>>({
    url: `${SERVER.IAM}/current_user/information`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
export function setPasswordGenerate(data: { mfaMethod: keyof typeof MFAMethod; mfaCode: string; newPassword: string; confirmationPassword: string; [key: string]: unknown }) {
  return request<unknown, Response<string>>({
    url: `${SERVER.IAM}/current_user/password`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { mfaMethod: data.mfaMethod, mfaCode: data.mfaCode, newPassword: data.newPassword, confirmationPassword: data.confirmationPassword, ptype: data.ptype },
  });
}
/**
 * @description 变更账号
 * @url http://*************:3000/project/11/interface/api/2709
 */
export function /* 变更账号 */ setAccountGenerate(req: { mfaMethod?: keyof typeof MFAMethod; mfaCode?: string; newAccount: string } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { mfaMethod: req.mfaMethod /* mfa验证方式枚举：PASSWORD :密码验证, 对应MfaType为PASSWORD、TOTP :MFA动态码, 对应MfaType为TOTP、EMAIL :邮箱验证, 对应MfaType为CODE、SMS :手机短信验证, 对应MfaType为CODE */, mfaCode: req.mfaCode /* mfa验证码 */, newAccount: req.newAccount /* 新账号 */, ptype: "RSA" };
  return request<never, Response<null>>({ url: `${SERVER.IAM}/current_user/account`, method: Method.Patch, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
export function setPhoneGenerate(data: { mfaMethod: keyof typeof MFAMethod; mfaCode: string; newPhone: string; [key: string]: unknown }) {
  return request<unknown, Response<string>>({
    url: `${SERVER.IAM}/current_user/phone`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { mfaMethod: data.mfaMethod, mfaCode: data.mfaCode, newPhone: data.newPhone, smsCode: data.smsCode, ptype: data.ptype },
  });
}
export function setEmailGenerate(data: { mfaMethod: keyof typeof MFAMethod; mfaCode: string; newEmail: string; [key: string]: unknown }) {
  return request<unknown, Response<string>>({
    url: `${SERVER.IAM}/current_user/email`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { mfaMethod: data.mfaMethod, mfaCode: data.mfaCode, newEmail: data.newEmail, emailCode: data.emailCode, ptype: data.ptype },
  });
}
export function setTOTPGenerate /* 确认动态密钥 */(data: { code: string; [key: string]: unknown }, token = "") {
  return request<unknown, Response<string>>({
    url: `${SERVER.IAM}/current_user/totp/confirmation`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: token ? { Authorization: token || undefined } : {},
    params: { code: data.code },
    data: new URLSearchParams({}),
  });
}
export function delTOTPGenerate /* 确认动态密钥 */(data: { mfaMethod: keyof typeof MFAMethod; mfaCode: string; [key: string]: unknown }) {
  return request<unknown, Response<string>>({
    url: `${SERVER.IAM}/current_user/totp`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { mfaMethod: data.mfaMethod, mfaCode: data.mfaCode, ptype: data.ptype },
    data: new URLSearchParams({}),
  });
}

export function registeredRegisteredUser /* 判断是否存在用户 */(data: { account: string; phone: string; email: string; [key: string]: unknown }) {
  const siteConfig = useSiteConfig();
  return request<unknown, Response<boolean>>({
    url: `${SERVER.IAM}authentication/registered`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: { Authorization: siteConfig.baseInfo?.auth },
    params: { account: data.account, phone: data.phone, email: data.email },
    data: new URLSearchParams({}),
  }).then((res) => {
    if (res.success) return res.data;
    else throw Object.assign(new Error(res.message), { success: res.success, data: res.data });
  });
}

export async function getBindingGithubInfo(): Promise<Response<string>> {
  const siteConfig = useSiteConfig();
  const { success, message, data } = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/github/authorize_url/bind`, method: Method.Get, responseType: "json", headers: { Authorization: siteConfig.baseInfo?.auth }, data: new URLSearchParams({}) });
  if (!success) throw Object.assign(new Error(message), { success, data });
  return await new Promise((resolve, reject) => {
    const windowProxy = window.open(data, "_blank", `location=no, menubar=no, status=no, titlebar=no, toolbar=no, top=0px, left=0px, width=${window.screen.availWidth * 0.45}px, height=${window.screen.availHeight * 0.45}px`);
    if (!windowProxy) return;
    const winLoop = setInterval(() => windowProxy.closed && (done(), reject(new Error("关闭了授权"))), 1000);
    window.addEventListener("message", binding);
    async function binding({ data }: { data: { idp: keyof typeof loginChannels; code: string } }) {
      if (data.idp === loginChannels.GIT_HUB) {
        done();
        try {
          const result = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/github/user/current/bind`, method: Method.Post, responseType: "json", data: { code: data.code } });
          resolve(result);
        } catch (error) {
          reject(error);
        }
      }
    }
    function done() {
      clearInterval(winLoop);
      window.removeEventListener("message", binding);
    }
  });
}

export interface ApiKeys {
  id: string;
  userId: string;
  label?: string;
  apiKey?: string;
  enabled: boolean;
  createdTime: string;
}

export function getApiKey(data: {} & RequestBase) {
  return request<unknown, Response<ApiKeys[]>>({
    url: `${SERVER.IAM}/current_user/api_keys`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function createApiKey(data: { label: string } & RequestBase) {
  return request<unknown, Response<string>>({
    url: `${SERVER.IAM}/current_user/api_keys`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function delApiKey(data: Partial<ApiKeys> & RequestBase) {
  return request<unknown, Response<string>>({
    url: `${SERVER.IAM}/current_user/api_keys/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function putApiKeyEnabled(data: Partial<ApiKeys> & RequestBase) {
  return request<unknown, Response<string>>({
    url: `${SERVER.IAM}/current_user/api_keys/${data.id}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data,
  });
}

// 导航
export interface NavigationGroupItem {
  id: string;
  name: string /* 分组名称 */;
  note: string /* 备注信息 */;
  order: number /* 排序 */;
  items: NavigationItem[];
}
export interface NavigationItem {
  id: string /* 主键 */;
  groupId: string /* 所属分组 */;
  name: string /* 导航项名称 */;
  note: string /* 备注信息 */;
  type: string /* 导航类型: APP :应用 MENU :菜单 LINK :外部链接 */;
  targetId: string /* 导航类型关联ID，用于权限过滤 */;
  // 1. 如果是菜单, 则关联菜单ID
  // 2. 如果是应用, 则关联应用ID
  // 3. 如果是外部链接, 则为空
  order: number /* 排序 */;
  config: string /* 配置信息 */;
  targetConfig: string /* 目标配置信息 */;
  // - 如果是应用类型, 则为应用的配置字段
  // - 如果是菜单类型, 则为菜单的配置字段
  // - 如果是连接类型, 则为空
}
export interface NavigationDataItem {
  id: string /* 主键 */;
  groupId: string /* 所属分组 */;
  name: string /* 导航项名称 */;
  note: string /* 备注信息 */;
  type: string /* 导航类型: APP :应用 MENU :菜单 LINK :外部链接 */;
  targetId: string /* 导航类型关联ID，用于权限过滤 */;
  order: number /* 排序 */;
  path: string;
  url: string;
  icon: string;
  route: import("vue-router").RouteLocationNamedRaw;
}
// export function getNavigationData(data: RequestBase) {
//   return request<unknown, Response<Record<"groups", NavigationGroupItem[]>>>({
//     url: `${SERVER.IAM}/navigation/details/user/current`,
//     method: Method.Get,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { groupId: data.groupId, moduleId: data.moduleId }),
//     data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
//   }).then(async (res) => {
//     if (!(res.data.groups instanceof Array)) res.data.groups = [];
//     res.data.groups.sort((a, b) => a.order - b.order);
//     const navs = useNavTabs();
//     for (let index = 0; index < res.data.groups.length; index++) {
//       if (!(res.data.groups[index].items instanceof Array)) res.data.groups[index].items = [];
//       Object.assign(res.data.groups[index], { items: res.data.groups[index].items.map(formatNavigationItemData.bind(navs.state.tabsViewRoutes)).sort((a, b) => a.order - b.order) });
//     }
//     return res as unknown as Response<Record<"groups", (Omit<NavigationGroupItem, "items"> & { items: NavigationDataItem[] })[]>>;
//   });
// }

// function formatNavigationItemData(this: import("@/utils/router").RouteLocation[], data: NavigationItem): NavigationDataItem {
//   const base: NavigationDataItem = { id: data.id, groupId: data.groupId, name: data.name, note: data.note, type: data.type, targetId: data.targetId, order: data.order, path: "", url: "", icon: "", route: { name: "notFound" } };
//   try {
//     switch (data.type) {
//       case "APP": {
//         if (base.type === "menu") {
//           const route = getFirstRoute(this.filter((v) => v.id === base.targetId)) as import("@/utils/router").RouteLocation;
//           const { icon, path, url, type } = JSON.parse(data.targetConfig || "{}");
//           Object.assign(base, { route, icon, path, url, type });
//         } else {
//           const { icon, path, url, type } = JSON.parse(data.targetConfig || "{}");
//           Object.assign(base, { route: base, icon, path, url, type });
//         }
//         break;
//       }
//       case "MENU": {
//         const findRoute = getRouteId(this, base.targetId);
//         if (findRoute) {
//           const route = getFirstRoute([findRoute]);
//           Object.assign(base, { route });
//         }
//         const { icon, path, url, type } = JSON.parse(data.targetConfig || "{}");
//         Object.assign(base, { icon, path, url, type });
//         break;
//       }
//       default: {
//         const { icon, path, url } = JSON.parse(data.config || "{}");
//         Object.assign(base, { icon, path, url, type: "link", route: { path: url }, query: {} });
//         break;
//       }
//     }
//   } catch (error) {
//     try {
//       const { icon, path, url, type } = JSON.parse(data.config || "{}");
//       Object.assign(base, { icon, path, url, type });
//     } catch (error) {
//       /*  */
//     }
//   }
//   return base;
// }

// function getRouteId(list: import("@/utils/router").RouteLocation[], findId: string): import("@/utils/router").RouteLocation | undefined {
//   for (let index = 0; index < list.length; index++) {
//     if ((list[index] || {}).id === findId) return list[index];
//     else if (list[index].children && list[index].children instanceof Array) {
//       const find = getRouteId(list[index].children as typeof list, findId);
//       if (find) return find;
//     }
//   }
// }

/* 配置信息 */
export interface userConfig {
  userId: string;
  key: string;
  value: Record<string, unknown>;
}
export function getUserNavigation(data: RequestBase) {
  return request<unknown, Response<userConfig>>({
    url: `${SERVER.IAM}/current_user/configs/${"navigation"}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function modUserNavigation(data: { value: userConfig["value"] } & RequestBase) {
  return request<unknown, Response<userConfig>>({
    url: `${SERVER.IAM}/current_user/configs`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["value"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { key: "navigation" }),
  });
}

export interface LoggerItem {
  id: string /* 主键 */;
  tenantId?: string /* 操作所在租户ID */;
  tenantName?: string /* 租户名称, 理想[ideal] */;
  userId?: string /* 请求用户ID */;
  userShowName?: string /* 请求用户的显示名称, 宋志宗(secure@ideal) */;
  name: string /* 操作名称 */;
  action: ActionType /* 审计操作 枚举类型: CREATE :新增 | UPDATE :更新 | DELETE :删除 | READ :查看 | ASSIGN :分配 | UNASSIGN :解除分配 | REMOVE :移除 | ENABLE :启用 | DISABLE :停用 | BLOCK :冻结 | UNBLOCK :解冻 | OPEN :开启 | CLOSE :关闭 | CONNECT :连接 | DISCONNECT :断开连接 | PERMISSION_CONFIG :权限配置 | NONE :权限配置 */;
  auditCode?: string /* 审计编码 */;
  classification?: string /* 审计分类, 即配置项编码 */;
  classificationName?: string /* 审计分类显示名称, 即配置项名称 */;
  resourceType?: string /* 资源类型 */;
  resourceId?: string /* 资源ID */;
  resourceName?: string /* 资源名称 */;
  originalResourceName?: string /* 修改前的资源名称 */;
  resourceTenantId?: string /* 资源所属的租户ID */;
  resourceTenantName?: string /* 资源所属的租户名称, 理想[ideal] */;
  path?: string /* 请求路径 */;
  originalIp?: string /* 原始请求IP */;
  userAgent?: string /* 浏览器UA */;
  success: boolean /* 是否成功 */;
  errorMessage?: string /* 错误信息 */;
  auditSchema?: string;
  auditInfo?: string /* 审计信息 */;
  originalValue?: string /* 变更前的值 */;
  changedValue?: string /* 变更后的值 */;
  request?: string /* 请求信息 */;
  response?: string /* 响应信息 */;
  consuming: string /* 耗时, 单位毫秒 */;
  operationTime: string /* 操作时间 */;
}
// {
//   id?: string /* 主键 */;
//   platform?: string /* 平台编码 */;
//   tenantId?: string /* 所属租户ID */;
//   userId: string /* 用户ID */;
//   operationType?: string /* 操作类型 */;
//   traceId?: string /* traceId */;
//   system?: string /* 系统名称 */;
//   name?: string /* 操作名称 */;
//   details?: string /* 操作详情 */;
//   resourceType?: string /* 资源类型 */;
//   resourceName?: string /* 资源名称 */;
//   path?: string /* 请求路径 */;
//   originalIp?: string /* 原始请求地址 */;
//   location?: string /* 所在位置 */;
//   userAgent?: string /* 浏览器UA */;
//   success: boolean /* 是否成功 */;
//   message?: string /* 执行信息, 可用于记录错误信息 */;
//   auditInfo?: string /* 审计信息 */;
//   originalValue?: string /* 变更前的值 */;
//   changedValue?: string /* 变更后的值 */;
//   request?: /* 请求信息 */ {};
//   response?: /* 响应信息 */ {};
//   consuming: string /* 耗时, 单位毫秒 */;
//   operationTime: string /* 操作时间 */;
//   user?: /* 操作用户信息 */ {
//     id?: string /* 主键 */;
//     tenantId?: string /* 所属租户ID */;
//     name?: string /* 姓名 */;
//     nickname?: string /* 昵称 */;
//     account?: string /* 账号 */;
//     phone?: string /* 手机号码 */;
//     email?: string /* 邮箱 */;
//     profilePicture?: string /* 头像 */;
//   };
//   tenant?: /* 租户信息 */ {
//     id?: string /* 租户ID */;
//     name?: string /* 名称 */;
//     abbreviation?: string /* 缩写 */;
//     systemEdition?: string /* 系统版本 */;
//     zoneId?: string /* 时区 */;
//     language?: string /* 语言 */;
//     note?: string /* 备注 */;
//     blocked: boolean /* 是否已被冻结 */;
//   };
// }

export enum OperationType {
  //新增
  create = "create",
  //删除
  delete = "delete",
  //更新
  update = "update",
  //查看
  read = "read",
  //分配
  assign = "assign",
  //移除
  remove = "remove",
  //权限配置
  config = "config",
  //移除
  unassign = "unassign",
  //移除
  remove_tenant = "remove_tenant",
}
import { operationLogger } from "./loggerType";
import { ElMessage } from "element-plus";

export const operationTypeOption: { label: string; value: string; type: string; operation: string; resourceType: string }[] = operationLogger.map((item) => {
  // { label: "权限配置", value: OperationType['iam.role_data_auth.save.cmdb.resource'] }
  return { label: item.name, value: item.auditCode, type: item.type, operation: item.operation, resourceType: item.origin };
});

export enum ResourceType {
  //设备
  resource = "resource",
  //联系人
  contact = "contact",
  //区域
  region = "region",
  //场所
  location = "location",
  //供应商
  vendor = "vendor",
  //响应策略
  support_note = "support_note",
  //服务编号
  service_number = "service_number",
  //设备分组
  device_group = "device_group",
  //设备类型
  resource_type = "resource_type",
  //告警类型
  alert_classification = "alert_classification",
  //租户
  tenant = "tenant",
  //用户
  user_group = "user_group", //角色
  current_org = "current_org",
  //租户组
  tenant_group = "tenant_group",
  //优先级矩阵
  priority_matrix = "priority_matrix",
  //SLA配置
  sla = "sla",
  //告警降级配置
  degrade = "degrade",
  //sla降级配置
  sla_degrade = "sla_degrade",
  //通知模板
  notification_template = "notification_template",
  //全局配置
  global_config = "global_config",
  //完结代码
  finish_code = "finish_code",
  //角色
  role = "role",
  //用户
  user = "user",
  user_center = "user_center",
  //租户系统配置
  system_config = "system_config",
  //全局行动策略
  support_note_global = "support_note_global",
  //全局告警降级
  degrade_global = "degrade_global",
  //全局sla
  sla_global = "sla_global",
}

export const resourceTypeOption: { label: string; value: keyof typeof ResourceType }[] = [
  //设备
  { label: "设备", value: ResourceType.resource },
  //联系人
  { label: "联系人", value: ResourceType.contact },
  //区域
  { label: "区域", value: ResourceType.region },
  //场所
  { label: "场所", value: ResourceType.location },
  //供应商
  { label: "供应商", value: ResourceType.vendor },
  //响应策略
  { label: "响应策略", value: ResourceType.support_note },
  //服务编号
  { label: "服务编号", value: ResourceType.service_number },
  //设备分组
  { label: "设备分组", value: ResourceType.device_group },
  //设备类型
  { label: "设备类型", value: ResourceType.resource_type },
  //告警类型
  { label: "告警类型", value: ResourceType.alert_classification },
  //租户
  { label: "客户", value: ResourceType.tenant },
  //用户
  { label: "用户组", value: ResourceType.user_group }, //角色
  { label: "角色", value: ResourceType.current_org },
  //租户组
  { label: "租户组", value: ResourceType.tenant_group },
  //优先级矩阵
  { label: "事件优先级矩阵", value: ResourceType.priority_matrix },
  //SLA配置
  { label: "SLA配置", value: ResourceType.sla },
  //告警降级配置
  { label: "告警降级配置", value: ResourceType.degrade },
  //sla降级配置
  { label: "sla降级配置", value: ResourceType.sla_degrade },
  //通知模板
  { label: "通知模板", value: ResourceType.notification_template },
  //全局配置
  { label: "全局配置", value: ResourceType.global_config },
  //完结代码
  { label: "完结代码", value: ResourceType.finish_code },
  //角色
  { label: "角色", value: ResourceType.role },
  //用户
  { label: "用户", value: ResourceType.user },
  //个人中心
  { label: "个人中心", value: ResourceType.user_center },
  // 事件处理配置模板
  { label: "租户系统配置", value: ResourceType.system_config },
  { label: "全局行动策略", value: ResourceType.support_note_global },
  { label: "全局SLA配置", value: ResourceType.sla_global },
  { label: "全局告警降级配置", value: ResourceType.degrade_global },
];

// export interface LoggerItem {
//   id?: string /* 主键 */;
//   platform?: string /* 平台编码 */;
//   tenantId?: string /* 所属租户ID */;
//   userId: string /* 用户ID */;
//   traceId?: string /* traceId */;
//   system?: string /* 系统名称 */;
//   name?: string /* 操作名称 */;
//   details?: string /* 操作详情 */;
//   path?: string /* 请求路径 */;
//   originalIp?: string /* 原始请求地址 */;
//   location?: string /* 所在位置 */;
//   userAgent?: string /* 浏览器UA */;
//   success: boolean /* 是否成功 */;
//   message?: string /* 执行信息, 可用于记录错误信息 */;
//   request?: /* 请求信息 */ string;
//   response?: /* 响应信息 */ string;
//   consuming: string /* 耗时, 单位毫秒 */;
//   operationTime: string /* 操作时间 */;
//   user?: /* 操作用户信息 */ {
//     id?: string /* 主键 */;
//     tenantId?: string /* 所属租户ID */;
//     name?: string /* 姓名 */;
//     nickname?: string /* 昵称 */;
//     account?: string /* 账号 */;
//     phone?: string /* 手机号码 */;
//     email?: string /* 邮箱 */;
//     profilePicture?: string /* 头像 */;
//   };
//   tenant?: /* 租户信息 */ {
//     id?: string /* 主键 */;
//     name?: string /* 名称 */;
//     abbreviation?: string /* 缩写 */;
//     systemEdition?: string /* 系统版本 */;
//     zoneId?: string /* 时区 */;
//     language?: string /* 语言 */;
//     note?: string /* 备注 */;
//     blocked: boolean /* 是否已被冻结 */;
//   };
// }

// export interface LoggerItem {
//   id: string;
//   location: string /* 所在位置 */;
//   user: UserItem /* 操作用户信息 */;
//   traceId: string;
//   system: string /* 统名称 */;
//   platform: string /* 台 */;
//   tenantId: string /* 户ID */;
//   name: string /* 作名称 */;
//   details: string /* 作详情 */;
//   path: string /* 求路径 */;
//   userId: string /* 户ID */;
//   originalIp: string /* 始请求地址 */;
//   userAgent: string /* 览器UA */;
//   success: boolean /* 是否成功 */;
//   message: string /* 行信息，可用于记录错误信息 */;
//   request: object /* 请求信息 */;
//   response: object /* 响应信息 */;
//   consuming: number /* 耗时，单位毫秒 */;
//   operationTime: string /* 操作时间 */;
// }

export enum ActionType {
  /**@type {string} - 新增 */
  CREATE = "CREATE",
  /**@type {string} - 更新 */
  UPDATE = "UPDATE",
  /**@type {string} - 删除 */
  DELETE = "DELETE",
  /**@type {string} - 查看 */
  READ = "READ",
  /**@type {string} - 分配 */
  ASSIGN = "ASSIGN",
  /**@type {string} - 解除分配 */
  UNASSIGN = "UNASSIGN",
  /**@type {string} - 移除 */
  REMOVE = "REMOVE",
  /**@type {string} - 启用 */
  ENABLE = "ENABLE",
  /**@type {string} - 停用 */
  DISABLE = "DISABLE",
  /**@type {string} - 冻结 */
  BLOCK = "BLOCK",
  /**@type {string} - 解冻 */
  UNBLOCK = "UNBLOCK",
  /**@type {string} - 开启 */
  OPEN = "OPEN",
  /**@type {string} - 关闭 */
  CLOSE = "CLOSE",
  /**@type {string} - 连接 */
  CONNECT = "CONNECT",
  /**@type {string} - 断开连接 */
  DISCONNECT = "DISCONNECT",
  /**@type {string} - 权限配置 */
  PERMISSION_CONFIG = "PERMISSION_CONFIG",
  /**@type {string} - 无(正常不应该会有) */
  NONE = "NONE",
}
export const actionTypeOptions: { label: string; value: ActionType }[] = [
  /*  */
  { label: "新增", value: ActionType.CREATE },
  { label: "更新", value: ActionType.UPDATE },
  { label: "删除", value: ActionType.DELETE },
  { label: "查看", value: ActionType.READ },
  { label: "分配", value: ActionType.ASSIGN },
  { label: "解除分配", value: ActionType.UNASSIGN },
  { label: "移除", value: ActionType.REMOVE },
  { label: "启用", value: ActionType.ENABLE },
  { label: "停用", value: ActionType.DISABLE },
  { label: "冻结", value: ActionType.BLOCK },
  { label: "解冻", value: ActionType.UNBLOCK },
  { label: "开启", value: ActionType.OPEN },
  { label: "关闭", value: ActionType.CLOSE },
  { label: "连接", value: ActionType.CONNECT },
  { label: "断开连接", value: ActionType.DISCONNECT },
  { label: "权限配置", value: ActionType.PERMISSION_CONFIG },
];

/**
 * 条件过滤器方法
 */
type ConditionFilter<N extends string, S extends string, R extends string, TE extends string> = { [P in N extends string ? `${S}${Capitalize<N>}` | `${Uncapitalize<N>}${Capitalize<R>}` : never]: P extends `${Uncapitalize<N>}${Capitalize<R>}` ? TE : string[] };

interface LoggerQuery {
  /** 用户ID */
  userIdIn?: /* Integer */ string[];
  /** 审计操作 <li>CREATE - 新增</li> <li>UPDATE - 更新</li> <li>DELETE - 删除</li> <li>READ - 查看</li> <li>ASSIGN - 分配</li> <li>UNASSIGN - 解除分配</li> <li>REMOVE - 移除</li> <li>ENABLE - 启用</li> <li>DISABLE -停用</li> <li>BLOCK - 锁定(冻结)</li> <li>UNBLOCK - 解锁(解冻)</li> <li>OPEN - 开启</li> <li>CLOSE -关闭</li> <li>CONNECT - 连接</li> <li>DISCONNECT - 断开连接</li> <li>PERMISSION_CONFIG - 权限配置</li> <li>NONE - 无(正常不应该会有)</li> */
  actionIn?: /* 枚举: CREATE :新增 | UPDATE :更新 | DELETE :删除 | READ :查看 | ASSIGN :分配 | UNASSIGN :解除分配 | ADD :添加 | REMOVE :移除 | ENABLE :启用 | DISABLE :停用 | BLOCK :冻结 | UNBLOCK :解冻 | OPEN :开启 | CLOSE :关闭 | CONNECT :连接 | DISCONNECT :断开连接 | UPLOAD :上传 | PERMISSION_CONFIG :权限配置 | MOVE :移动安全容器 | NONE :权限配置 */ ("CREATE" | "UPDATE" | "DELETE" | "READ" | "ASSIGN" | "UNASSIGN" | "ADD" | "REMOVE" | "ENABLE" | "DISABLE" | "BLOCK" | "UNBLOCK" | "OPEN" | "CLOSE" | "CONNECT" | "DISCONNECT" | "UPLOAD" | "PERMISSION_CONFIG" | "MOVE" | "NONE")[];
  /** 模糊查询条件 */
  keyword?: string;
  /** 是否执行成功 */
  success?: boolean;
  /** 资源类型 */
  resourceType?: string;
  /** 资源ID */
  resourceId?: string;
  /** 审计分类列表 */
  classificationIn?: string[];
  /** 操作起始时间 */
  operationTimeStart: number;
  /** 操作结束时间 */
  operationTimeEnd: number;

  queryType: string;

  shortcuts?: string;
  // /** 操作类型筛选 */
  // auditActionFilter: { includeAction: /* 包含的操作类型 */ string; excludeAction: /* 不包含的操作类型 */ string; eqAction: /* 等于的操作类型 */ string; neAction: /* 不等于的操作类型 */ string; actionRelation: /* 操作类型见的关系(AND,OR) */ "AND" | "OR" };
  // /** IP筛选 */
  // auditIPFilter: { includeAction: /* 包含的IP地址 */ string; excludeAction: /* 不包含的IP地址 */ string; actionRelation: /* 操作类型见的关系(AND,OR) */ "AND" | "OR" };
  // /** 客户筛选 */
  // auditTenantFilter: { includeTenantName: /* 包含的客户名称 */ string; excludeTenantName: /* 不包含的客户名称 */ string; eqTenantName: /* 等于的客户名称 */ string; neTenantName: /* 不等于的客户名称 */ string; tenantNameFilterRelation: /* 客户名称过滤关系(AND,OR) */ "AND" | "OR" };
  // /** 用户筛选 */
  // auditUserFilter: { includeUserName: /* 包含的用户名称 */ string; excludeUserName: /* 不包含的用户名称 */ string; userNameFilterRelation: /* 用户名称过滤关系(AND,OR) */ "AND" | "OR" };
}
/**
 * @description 查询操作日志
 * @url http://*************:3000/project/11/interface/api/2731
 */
export function /* 查询操作日志 */ getLogger(req: LoggerQuery & ConditionFilter<"action" | "tenant", "include" | "exclude" | "eq" | "ne", "FilterRelation", "AND" | "OR"> & ConditionFilter<"ip" | "user", "include" | "exclude", "FilterRelation", "AND" | "OR"> & { keyword?: string; sort?: string[]; paging: { pageNumber: number; pageSize: number }; queryType: string } & { controller?: AbortController }) {
  // const header = new Headers();
  const params = new URLSearchParams({ pageNumber: `${req.paging && req.paging.pageNumber ? req.paging.pageNumber : 1}`, pageSize: `${req.paging && req.paging.pageSize ? req.paging.pageSize : process.env["APP_PAGE_SIZE"]}`, queryType: req.queryType });
  const data = {
    userIdIn: req.userIdIn,
    actionIn: req.actionIn,
    ...(req.keyword ? { keyword: req.keyword } : {}),
    success: req.success,
    resourceType: req.resourceType,
    resourceId: req.resourceId,
    classificationIn: req.classificationIn,

    ...(req.operationTimeStart && req.operationTimeEnd ? { operationTimeStart: req.operationTimeStart, operationTimeEnd: req.operationTimeEnd } : {}),

    /** 操作类型筛选 */
    ...([...(req.includeAction instanceof Array ? req.includeAction : []), ...(req.excludeAction instanceof Array ? req.excludeAction : []), ...(req.eqAction instanceof Array ? req.eqAction : []), ...(req.neAction instanceof Array ? req.neAction : [])].length ? { auditActionFilter: { ...((req.includeAction instanceof Array ? req.includeAction : []).length ? { includeAction: /* 包含的操作类型 */ req.includeAction.join(",") } : {}), ...((req.excludeAction instanceof Array ? req.excludeAction : []).length ? { excludeAction: /* 不包含的操作类型 */ req.excludeAction.join(",") } : {}), ...((req.eqAction instanceof Array ? req.eqAction : []).length ? { eqAction: /* 等于的操作类型 */ req.eqAction.join(",") } : {}), ...((req.neAction instanceof Array ? req.neAction : []).length ? { neAction: /* 不等于的操作类型 */ req.neAction.join(",") } : {}), actionRelation: /* 操作类型见的关系(AND,OR) */ req.actionFilterRelation === "OR" ? "OR" : "AND" } } : {}),
    /** IP筛选 */
    ...([...(req.includeIp instanceof Array ? req.includeIp : []), ...(req.excludeIp instanceof Array ? req.excludeIp : [])].length ? { auditIPFilter: { ...((req.includeIp instanceof Array ? req.includeIp : []).length ? { includeAction: /* 包含的IP地址 */ req.includeIp.join(",") } : {}), ...((req.excludeIp instanceof Array ? req.excludeIp : []).length ? { excludeAction: /* 不包含的IP地址 */ req.excludeIp.join(",") } : {}), actionRelation: /* 操作类型见的关系(AND,OR) */ req.ipFilterRelation === "OR" ? "OR" : "AND" } } : {}),
    /** 客户筛选 */
    ...([...(req.includeTenant instanceof Array ? req.includeTenant : []), ...(req.excludeTenant instanceof Array ? req.excludeTenant : []), ...(req.eqTenant instanceof Array ? req.eqTenant : []), ...(req.neTenant instanceof Array ? req.neTenant : [])].length ? { auditTenantFilter: { ...((req.includeTenant instanceof Array ? req.includeTenant : []).length ? { includeTenantName: /* 包含的客户名称 */ req.includeTenant.join(",") } : {}), ...((req.excludeTenant instanceof Array ? req.excludeTenant : []).length ? { excludeTenantName: /* 不包含的客户名称 */ req.excludeTenant.join(",") } : {}), ...((req.eqTenant instanceof Array ? req.eqTenant : []).length ? { eqTenantName: /* 等于的客户名称 */ req.eqTenant.join(",") } : {}), ...((req.neTenant instanceof Array ? req.neTenant : []).length ? { neTenantName: /* 不等于的客户名称 */ req.neTenant.join(",") } : {}), tenantNameFilterRelation: /* 客户名称过滤关系(AND,OR) */ req.tenantFilterRelation === "OR" ? "OR" : "AND" } } : {}),
    /** 用户筛选 */
    ...([...(req.includeUser instanceof Array ? req.includeUser : []), ...(req.excludeUser instanceof Array ? req.excludeUser : [])].length ? { auditUserFilter: { ...((req.includeUser instanceof Array ? req.includeUser : []).length ? { includeUserName: /* 包含的用户名称 */ req.includeUser.join(",") } : {}), ...((req.excludeUser instanceof Array ? req.excludeUser : []).length ? { excludeUserName: /* 不包含的用户名称 */ req.excludeUser.join(",") } : {}), userNameFilterRelation: /* 用户名称过滤关系(AND,OR) */ req.userFilterRelation === "OR" ? "OR" : "AND" } } : {}),
  };

  return request<never, Response<LoggerItem[]>>({ url: `${SERVER.CS}/log/current_org/query_audit_logs`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
// export function getLogger(data: Partial<{ userId: string; startTime: string; endTime: string; schemas: string }> & RequestBase) {
//   return request<unknown, Response<LoggerItem>>({
//     url: `${SERVER.IAM}/log/current_org/operation_logs`,
//     method: Method.Get,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//     params: ["userId", "keyword", "startTime", "endTime", "schemas"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { pageNumber: data.paging?.pageNumber, pageSize: data.paging?.pageSize }),
//     data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
//   });
// }

/* 获取系统版本 */
export function getSystemVersion(data: { [key: string]: unknown }) {
  return request<unknown, Response<Record<"code" | "name", string>[]>>({
    url: `${SERVER.IAM}/system_editions/platform/current`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { orders: data.orders instanceof Array ? data.orders : [] },
  });
}

export function sendCommonSmsCode(data: { phone: string } & RequestBase) {
  return request<unknown, any>({
    url: `${SERVER.IAM}/send_common_sms_code`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { phone: data.phone, certificate: data.certificate, captcha: data.captcha },
    data: {},
  });
}

export function sendCommonEmailCode(data: { email: string } & RequestBase) {
  return request<unknown, any>({
    url: `${SERVER.IAM}/send_common_email_code`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { email: data.email, certificate: data.certificate, captcha: data.captcha },
    data: {},
  });
}
export async function getPublicKey() {
  const { success, message, data } = await request<unknown, any>({
    url: `${SERVER.IAM}/rsa/key/public`,
    method: Method.Get,
    responseType: "json",
    data: new URLSearchParams(),
  });
  if (!success) throw Object.assign(new Error(message), { success, data });
  return await window.crypto.subtle.importKey("spki", base64ToBuffer(data), { name: "RSA-OAEP", hash: { name: "SHA-512" } }, true, ["encrypt"]);
}

/**
 * @description 安全容器
 */
export interface SecurityContainer {
  id: string;
  parentId?: string;
  name: string;
  children: SecurityContainer[];
}

/**
 * @description 获取用户在指定安全容器之上的所有可见父容器
 * @url http://*************:3000/project/11/interface/api/19650
 */
export function getSecurityContainerById(req: { containerId: string /* 容器ID */ }) {
  const controller = new AbortController();
  const params = new URLSearchParams();
  bindSearchParams({ containerId: req.containerId, permissionId: "623053172327317504" }, params);
  // https://*************:20082/gw_proxy/iam/current_org/security_containers/current_user/available_tree?permissionId=623053172327317504
  return Object.assign(
    request<never, Response<SecurityContainer[]>>({
      // security_containers/current_user/visible_child_tree
      url: `${SERVER.IAM}/security_containers/current_user/visible_parent_tree`,
      // url: `${SERVER.IAM}/current_org/security_containers/current_user/available_tree`,
      method: Method.Get,
      responseType: "json",
      signal: controller.signal,
      params,
    }),
    { controller }
  );
}
