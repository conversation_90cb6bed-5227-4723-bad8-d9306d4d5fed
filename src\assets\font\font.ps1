[System.Reflection.Assembly]::LoadWithPartialName("System");
[System.Reflection.Assembly]::LoadWithPartialName("System.Net");

Function DownLoad {
  Param(
    [string]$url,
    [string]$path
  )

  $file = [System.IO.Path]::GetFileName($url);
  $dir = Join-Path $($path) $file;

  if ($file -eq "") {
    Clear-Host;
    Write-Warning -Message "DownLoad Error: $url File Name Get Error!";
  } else {
    if(Test-Path $dir) {
      Clear-Host;
      Write-Warning -Message "Save File Repeating: File $file Already Exist `nRemove File $dir !";
      Remove-Item $dir;
      Start-Sleep -Milliseconds 300
    }

    (new-object System.Net.WebClient).DownloadFile($(new-object System.Uri($url)), $dir);

    Write-Host "Save Seve Success`nFile: $file `nSave To: $dir `n`n";
  }
}

Get-ChildItem $(Get-Location) | ForEach-Object -Process{
  if($_ -is [System.IO.FileInfo]) {
    # Write-Host "File";
    # Write-Host $_.name;
  }
  if($_ -is [System.IO.DirectoryInfo]) {
    Write-Host "In Directory $($_.FullName) ";
    $dir = Join-Path $(Get-Location) $_;
    Get-ChildItem -Path $($dir) -Filter *.scss -Recurse | ForEach-Object {
      $index = $_
      $type = [System.IO.Path]::GetFileNameWithoutExtension($index.FullName);
      $path = Join-Path $($dir) $type;
      # [System.IO.Path]::GetFileNameWithoutExtension($_.FullName)
      # [System.IO.Path]::GetExtension($_.FullName)
      # [System.IO.Path]::GetFileName($_.FullName)
      if(Test-Path $path) {
        Clear-Host;
        Remove-Item $path -recurse;
        Write-Warning -Message "Directory Repeating: Directory $file Already Exist `nRemove Directory $path !";
        Start-Sleep -Milliseconds 300
      }

      $directory = New-Item -Path $path -ItemType Directory

      $content = Get-Content -Path $($index.FullName)
      Select-String -Path $($index.FullName) -Pattern 'url\((.*)\)\s' | ForEach-Object {
        $pattern = $_.Matches.Groups.Item(1)
        $fileName = [System.IO.Path]::GetFileName($pattern)
        Write-Host "File Name: `"$($fileName)`" `nDownLoad Save: `"$($index)`" File All Source Url`nDownLoad To: $($directory.FullName)`nDownLoad Loading...!";
        DownLoad -url $($pattern) -path $($directory.FullName)
        Get-Content -Path $($index.FullName) | % { $_ -Replace $($pattern), "`"./$($type)/$([System.IO.Path]::GetFileName($pattern))`"" }
        $content = $content -Replace $($pattern), "`"./$($type)/$([System.IO.Path]::GetFileName($pattern))`""
      }
      $content | Set-Content $($index.FullName)
    }
  }
}
