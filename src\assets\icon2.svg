<svg width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.30566" y="9.97119" width="28" height="31.2567" rx="4" transform="rotate(-20 1.30566 9.97119)" fill="url(#paint0_linear_5_140)"/>
<g filter="url(#filter0_f_5_140)">
<rect x="11.7798" y="12.7627" width="14.7368" height="14.7368" rx="4" transform="rotate(-18 11.7798 12.7627)" fill="#FF5318"/>
</g>
<g filter="url(#filter1_b_5_140)">
<path d="M34.1938 12.0801L43.0351 20.9187V44.8001H13.5928V12.0801H34.1938Z" fill="#FFC2AD" fill-opacity="0.24"/>
<path d="M34.5473 11.7265C34.4536 11.6327 34.3264 11.5801 34.1938 11.5801H13.5928C13.3166 11.5801 13.0928 11.8039 13.0928 12.0801V44.8001C13.0928 45.0762 13.3166 45.3001 13.5928 45.3001H43.0351C43.3112 45.3001 43.5351 45.0762 43.5351 44.8001V20.9187C43.5351 20.786 43.4824 20.6588 43.3886 20.5651L34.5473 11.7265Z" stroke="url(#paint1_linear_5_140)" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<g filter="url(#filter2_bd_5_140)">
<rect x="17.793" y="16" width="6" height="6" fill="url(#paint2_linear_5_140)"/>
<rect x="17.993" y="16.2" width="5.6" height="5.6" stroke="url(#paint3_linear_5_140)" stroke-width="0.4"/>
</g>
<g filter="url(#filter3_bd_5_140)">
<rect x="16.3931" y="31.2" width="21.6" height="7.2" fill="url(#paint4_linear_5_140)"/>
<rect x="16.5931" y="31.4" width="21.2" height="6.8" stroke="url(#paint5_linear_5_140)" stroke-width="0.4"/>
</g>
<defs>
<filter id="filter0_f_5_140" x="8.81885" y="5.24805" width="24.4912" height="24.491" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur_5_140"/>
</filter>
<filter id="filter1_b_5_140" x="2.59277" y="1.08008" width="51.4424" height="54.72" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_5_140"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_5_140" result="shape"/>
</filter>
<filter id="filter2_bd_5_140" x="2.79297" y="1" width="36" height="36" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="7.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_5_140"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.992157 0 0 0 0 0.482353 0 0 0 0 0.309804 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_5_140" result="effect2_dropShadow_5_140"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_5_140" result="shape"/>
</filter>
<filter id="filter3_bd_5_140" x="1.39307" y="16.2" width="51.6001" height="37.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="7.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_5_140"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.992157 0 0 0 0 0.482353 0 0 0 0 0.309804 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_5_140" result="effect2_dropShadow_5_140"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_5_140" result="shape"/>
</filter>
<linearGradient id="paint0_linear_5_140" x1="15.3057" y1="9.97119" x2="15.3057" y2="41.2278" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF9875"/>
<stop offset="1" stop-color="#FA541C"/>
</linearGradient>
<linearGradient id="paint1_linear_5_140" x1="24.2091" y1="16.6049" x2="34.7951" y2="40.2882" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.4"/>
<stop offset="1" stop-color="#FF7241" stop-opacity="0.14"/>
</linearGradient>
<linearGradient id="paint2_linear_5_140" x1="23.3676" y1="17.0835" x2="16.6841" y2="17.3014" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.2"/>
</linearGradient>
<linearGradient id="paint3_linear_5_140" x1="18.7481" y1="16.699" x2="22.5754" y2="21.4393" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.25"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_5_140" x1="36.4616" y1="32.5001" x2="12.604" y2="34.8337" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.2"/>
</linearGradient>
<linearGradient id="paint5_linear_5_140" x1="19.8314" y1="32.0388" x2="22.1896" y2="40.8007" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.25"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
