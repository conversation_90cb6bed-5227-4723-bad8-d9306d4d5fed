<template>
  <svg width="1em" height="1em" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
      <rect x="0.5" y="0.5" width="15" height="15" rx="2" stroke="currentColor"></rect>
      <path fill="currentColor" d="M8.75,4 L8.75,7.25 L12,7.25 L12,8.75 L8.749,8.75 L8.75,12 L7.25,12 L7.249,8.75 L4,8.75 L4,7.25 L7.25,7.25 L7.25,4 L8.75,4 Z"></path>
    </g>
  </svg>
</template>

<script setup lang="ts">
defineOptions({ name: "SvgIconPlus" });
</script>
