.fluid {
  width: 100%;
}
.fill-height {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 100%;
  flex: 1 1 100%;
}
.spacer {
  -webkit-box-flex: 1 !important;
  -ms-flex-positive: 1 !important;
  flex-grow: 1 !important;
}

.align-center {
  -webkit-box-align: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}

.flex {
  display: -webkit-box; /* OLD - iOS 6-, Safari 3.1-6 */
  display: -moz-box; /* OLD - Firefox 19- (buggy but mostly works) */
  display: -ms-flexbox; /* TWEENER - IE 10 */
  display: -webkit-flex; /* NEW - Chrome */
  display: flex;
}

.flex-start {
  -webkit-box-align: start;
  align-items: flex-start;
  justify-content: flex-start;
  -ms-flex-pack: start;
  -moz-box-align: start;
  -moz-box-pack: start;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
}

.flex-end {
  -webkit-box-align: end;
  align-items: flex-end;
  justify-content: flex-end;
  -ms-flex-pack: end;
  -moz-box-align: end;
  -moz-box-pack: end;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
}

.flex-direction-column {
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  flex-flow: column;
  -moz-flex-direction: column;
}

.flex-direction-column-reverse {
  -webkit-flex-direction: column-reverse;
  -ms-flex-direction: column-reverse;
  flex-direction: column-reverse;
  flex-flow: column-reverse;
  -moz-flex-direction: column-reverse;
}

.flex-direction-row {
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  flex-flow: row;
  -moz-flex-direction: row;
}

.justify-content-right, .justify-content-end {
  justify-content: flex-end;
  -ms-flex-pack: end;
  -moz-box-align: end;
  -moz-box-pack: end;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -webkit-box-align: end;
}

.justify-content-center {
  justify-content: center;
  -ms-flex-pack: center;
  -moz-box-align: center;
  -moz-box-pack: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -webkit-box-align: center;
}

.justify-content-between {
  justify-content: space-between;
  -ms-flex-pack: justify;
  -moz-box-align: stretch;
  -moz-box-pack: justify;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -webkit-box-align: stretch;
}

.justify-content-around {
  justify-content: space-around;
  -ms-flex-pack: justify;
  -moz-box-align: stretch;
  -moz-box-pack: justify;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -webkit-box-align: stretch;
}

.flex-fill {
  -webkit-flex: 0 1 auto;
  -moz-flex: 0 1 auto;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
}
.flex-fixed {
  -webkit-flex: 0 0 auto;
  -moz-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}
.flex-1 {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -moz-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-grow {
  flex-grow: 1;
}
