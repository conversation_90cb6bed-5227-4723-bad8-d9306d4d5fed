import { ComponentOptionsMixin } from "vue";

import moment from "moment";
/*
 * Vue mixin to inject the required methods, events to handle the date navigation
 * with the keyboard.
 * @module mixin - keyboardAccessibility
 */
export default {
  props: {
    noKeyboard: { type: Boolean, default: false },
  },
  emits: ["close"],
  data() {
    return {
      newValue: null as import("moment").Moment | null,
    };
  },
  computed: {
    currentValue() {
      return this.range ? this.newValue || this.modelValue.end || this.modelValue.start || moment() : this.newValue || this.modelValue || moment();
    },
  },
  methods: {
    keyPressed(e: KeyboardEvent) {
      /*
        13 : Enter
        27 : Escape
        32 : Space
        35 : PageDown
        36 : PageUp
        37 : ArrowLeft
        38 : ArrowUp
        39 : ArrowRight
        40 : ArrowDown
        40 : Right
      */
      e.preventDefault();

      // if (e.code === "ArrowUp" || e.code === "ArrowDown" || e.code === "PageDown" || e.code === "PageUp") {
      //   /* 防止滚动条意外滚动 */
      //   e.preventDefault();
      // }
      if (this.isKeyboardActive) {
        try {
          switch (e.code) {
            case "ArrowUp":
              this.previousWeek();
              break;
            case "ArrowLeft":
              this.previousDay();
              break;
            case "ArrowRight":
              this.nextDay();
              break;
            case "ArrowDown":
              this.nextWeek();
              break;
            case "Space":
            case "Enter":
              this.selectThisDay();
              break;
            case "PageUp":
              this.previousMonth();
              break;
            case "PageDown":
              this.nextMonth();
              break;
            case "Escape":
              this.$emit("close");
              break;
          }
          if ("activeElement" in document && document.activeElement) (document.activeElement as HTMLElement).blur();
        } catch (err) {
          window.console.error("An error occured while switch date", e);
        }
      }
    },
    previousWeek() {
      const newValue = moment(this.currentValue).subtract(1, "week");
      if (!this.isDisabled(newValue)) {
        this.newValue = newValue;
        this.checkMonth();
      }
    },
    previousDay() {
      const newValue = moment(this.currentValue).subtract(1, "days");
      if (!this.isDisabled(newValue)) {
        this.newValue = newValue;
        this.checkMonth();
      }
    },
    nextDay() {
      const newValue = moment(this.currentValue).add(1, "days");
      if (!this.isDisabled(newValue)) {
        this.newValue = newValue;
        this.checkMonth();
      }
    },
    nextWeek() {
      const newValue = moment(this.currentValue).add(1, "week");
      if (!this.isDisabled(newValue)) {
        this.newValue = newValue;
        this.checkMonth();
      }
    },
    previousMonth() {
      const newValue = moment(this.currentValue).subtract(1, "month");
      if (!this.isDisabled(newValue)) {
        this.newValue = newValue;
        this.checkMonth();
      }
    },
    nextMonth() {
      const newValue = moment(this.currentValue).add(1, "month");
      if (!this.isDisabled(newValue)) {
        this.newValue = newValue;
        this.checkMonth();
      }
    },
    selectThisDay() {
      this.selectDate(this.currentValue);
    },
    checkMonth() {
      this.$nextTick(() => {
        const newYear = parseInt(this.newValue.format("YYYY"));
        const currentYear = this.month.year;
        const isSameYear = newYear === currentYear;
        if (parseInt(String(this.newValue.format("MM") - 1)) !== this.month.month && isSameYear) {
          if (parseInt(String(this.newValue.format("MM") - 1)) > this.month.month) {
            this.changeMonth("next");
          } else {
            this.changeMonth("prev");
          }
        } else if (!isSameYear) {
          if (newYear > currentYear) {
            this.changeMonth("next");
          } else {
            this.changeMonth("prev");
          }
        }
      });
    },
  },
  mounted() {
    if (!this.noKeyboard && (this.inline || this.visible)) {
      window.addEventListener("keydown", this.keyPressed as (e: KeyboardEvent) => void);
    }
  },
  beforeUnmount() {
    window.removeEventListener("keydown", this.keyPressed as (e: KeyboardEvent) => void);
  },
  watch: {
    visible(value) {
      if (!this.noKeyboard && value) {
        window.addEventListener("keydown", this.keyPressed as (e: KeyboardEvent) => void);
      } else {
        window.removeEventListener("keydown", this.keyPressed as (e: KeyboardEvent) => void);
      }
    },
  },
} as ComponentOptionsMixin;
