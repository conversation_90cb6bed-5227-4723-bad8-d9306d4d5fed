<template>
  <el-dialog title="分配联系人" v-model="dialogFormVisible" width="580px" :before-close="beforeClose">
    <el-form ref="formRef" :model="form" label-width="120px" :rules="rules">
      <el-form-item label-width="0">
        <el-alert title="联系人需要提前录入联系人中，每次可分配多名联系人" type="warning" show-icon :closable="false" />
      </el-form-item>
      <el-form-item label="分配联系人" prop="contacts">
        <el-select v-model="form.contacts" placeholder="请分配联系人" filterable multiple :style="{ width: '100%' }">
          <el-option v-for="item in options" :disabled="item.disabled ? item.disabled : false" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="beforeClose">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { ref } from "vue";
import { getContacts } from "@/views/pages/apis/contacts";
import { ElForm } from "element-plus";

const formRef = ref<InstanceType<typeof ElForm>>();

interface Emits {
  (e: "submitForm", data: any): any;
}
const emits = defineEmits<Emits>();

const dialogFormVisible = ref(false);
const form = ref({ contacts: [] });
const options = ref([]);
const type = ref("");
const id = ref("");
const contacts = ref([]);

function beforeClose(done?: () => void) {
  formRef.value.clearValidate();
  formRef.value.resetFields();
  if (done instanceof Function) done();
  else dialogFormVisible.value = false;
}
function submitForm() {
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (!valid) return valid;
    emits("submitForm", {
      id: id.value,
      params: {
        contactType: type.value,
        contactIds: form.value.contacts,
      },
    });
  });
}
function open($id: string, $type: string, $contacts: Array = []) {
  dialogFormVisible.value = true;
  id.value = $id;
  type.value = $type;
  contacts.value = $contacts;
  options.value = options.value.map((v) => ({ ...v, disabled: false }));
  contacts.value.forEach((v) => {
    options.value.findIndex((item) => {
      if (v.id === item.id) {
        item.disabled = true;
      }
    });
  });
}
getContacts({ pageSize: 99999 }).then(({ success, data }) => {
  if (success) {
    options.value = data;
  }
});

defineExpose({ beforeClose, open });
</script>
