<template>
  <div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick" class="bind-contacts">
      <el-tab-pane v-for="item in tabs" :key="item.name" :label="item.label" :name="item.name">
        <el-row :gutter="20">
          <el-col :span="24" :style="{ textAlign: 'right' }" v-if="showButoon">
            <el-button type="primary" :icon="ElIconPlus" @click="handleAssignContacts">分配联系人</el-button>
          </el-col>
          <template v-if="contacts && contacts.length">
            <el-col :span="6" v-for="item in contacts" :key="item.id" :style="{ marginTop: '20px' }">
              <div class="contact-item">
                <div class="contact">
                  <span class="name">{{ item.name }}</span>
                  <div>
                    <el-button type="text" @click="handleViewContact(item)">查看</el-button>
                    <el-button
                      type="text"
                      textColor="danger"
                      @click="
                        $emit('handleDelContact', {
                          tab: tabs.find((v) => v.name === activeName),
                          contact: item,
                          id: row.id,
                        })
                      "
                      v-if="showButoon"
                      >移除</el-button
                    >
                  </div>
                </div>
                <el-form ref="form" :model="{}" label-width="80px" label-position="left">
                  <el-form-item label="固定电话">
                    <span class="form-content">{{ item.landlinePhone }}</span>
                  </el-form-item>
                  <el-form-item label="移动电话">
                    <span class="form-content">{{ item.mobilePhone }}</span>
                  </el-form-item>
                  <el-form-item label="邮箱">
                    <span class="form-content">{{ item.email }}</span>
                  </el-form-item>
                </el-form>
              </div>
            </el-col>
          </template>
          <el-col :span="24" v-else :style="{ marginTop: '20px' }">
            <div class="contact-item">
              <div class="contact">
                <span class="name">未分配联系人</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
    <contacts-edit ref="contactsEdit" />
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent } from "vue";
import { Plus as ElIconPlus } from "@element-plus/icons-vue";
import { getContactTypes } from "@/views/pages/apis/contacts";
import contactsEdit from "@/views/pages/alarm_convergence/PropertyManage/contactsManage/contactsEdit.vue";
export default defineComponent({
  components: { contactsEdit },
  props: {
    row: {
      type: Object,
      default: () => {
        return {};
      },
    },
    showButoon: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
    // data: {
    //   type: Array,
    //   default: () => {
    //     return [];
    //   }
    // }
  },
  emits: ["handleDelContact", "handleAssignContacts"],
  data() {
    return {
      activeName: "",
      tabs: [],
      contacts: [],
      all: [],
      ElIconPlus,
    };
  },
  methods: {
    handleViewContact(row) {
      if (!this.$refs.contactsEdit) return false;
      this.$refs.contactsEdit.open(row, true);
    },
    handleClick(tab) {
      // // console.log(tab.props.name, 111, this.all, this.contacts);
      this.contacts = this.all.filter((i) => i.contactType === tab.props.name).map((i) => i.contact);
    },
    setContacts(v) {
      this.all = v;
      this.$nextTick(() => {
        this.contacts = v.filter((i) => i.contactType === this.activeName).map((i) => i.contact);
      });
    },
    getTabs() {
      if (this.tabs.length) return false;
      getContactTypes({}).then(({ success, data }) => {
        if (success) {
          this.tabs = data.map((v) => {
            return { name: v.code, label: v.cnName };
          });
          if (data && data.length) this.activeName = data[0].code;
        }
      });
    },
    handleAssignContacts() {
      this.$emit("handleAssignContacts", {
        rowId: this.row.id,
        type: this.activeName,
        contacts: this.contacts,
      });
    },
  },
  expose: ["tabs", "contacts", "all", "getTabs", "setContacts"],
});
</script>

<style lang="scss" scoped>
.bind-contacts {
  padding: 0 15px;
  box-sizing: border-box;
}
.contact-item {
  padding: 24px !important;
  background: #f7f8fa;
  .contact {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .elstyle-button--text {
    font-family: "PingFang SC";
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
  }
  .elstyle-form-item {
    margin-bottom: 0;
  }
  :deep(.el-form-item__label) {
    font-family: "PingFang SC";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    color: #86909c;
  }
  .form-content {
    font-family: "PingFang SC";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    color: var(--color-text-primary);
  }
  .name {
    font-family: "PingFang SC";
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: var(--color-text-primary);
  }
}
</style>
