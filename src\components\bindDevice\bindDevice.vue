<template>
  <div class="bind-device">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane v-for="(item, index) in title" :key="index" :label="item" :name="item">
        <h3>
          {{ item.split("分配")[1] }}
          <el-button type="primary" @click="add(item, index)">
            {{ item }}
          </el-button>
        </h3>
        <el-row>
          <ul>
            <template v-if="contacts && contacts[index]?.length > 0">
              <el-col v-for="v in contacts[index]" :key="v.id" :span="6">
                <li>
                  <div>
                    <h4>
                      <el-tooltip class="box-item" effect="dark" :content="v.name" placement="top-start">
                        <b>{{ v.name }} </b>
                      </el-tooltip>

                      <span @click="remove(v, item)"> 移除</span>
                    </h4>
                    <!-- {{ deviceOptions[item] }} -->
                    <p>{{ v.detail ? v.detail : v.description }}</p>
                  </div>
                </li>
              </el-col>
            </template>
            <el-col :span="6" v-else>
              <li>
                <div class="not">未{{ item }}</div>
              </li>
            </el-col>
          </ul>
        </el-row>
      </el-tab-pane>
    </el-tabs>
    <dialogVue ref="deviceDialog" :title="dialogTitle" :list="contacts[number]" @confirm="confirm" />
  </div>
</template>

<script>
import { defineComponent } from "vue";
import { ElMessage, ElMenuItem } from "element-plus";
import dialogVue from "./dialog.vue";
import mixin from "./mixin";
export default defineComponent({
  components: {
    dialogVue,
  },
  mixins: [mixin],
  props: {
    title: {
      type: Array,
      default: () => {
        return [];
      },
    },
    list: {
      type: Array,
      default: () => {
        return [];
      },
    },
    id: {
      type: String,
      default: "",
    },
  },
  emits: ["confirm", "delete"],
  data() {
    return {
      contacts: [[], [], []],
      activeName: this.$props.title[0],
      dialogTitle: "",
      number: "",
    };
  },
  watch: {
    // list(val) {
    //   // this.contacts = [...val];
    // },
    // contacts(val) {
    //   // console.log(val);
    // },
  },
  mounted() {
    //
  },
  methods: {
    setContacts(index, list) {
      ////
      this.contacts[index] = [...list];
      // // console.log(list);
    },
    add(val, index) {
      this.$refs.deviceDialog.dialogVisible = true;
      this.$refs.deviceDialog.id = "";
      this.$refs.deviceDialog.areaList = [];
      this.dialogTitle = val;
      this.number = index;
    },
    confirm(val) {
      let ids = [];
      ids.push(val.id);

      this.$emit("confirm", { expandId: this.id, ids, type: val.type });
    },
    remove(val, type) {
      this.$emit("delete", { expandId: this.id, ids: [val.id], type });
    },
  },
  expose: ["contacts", "setContacts"],
});
</script>

<style lang="scss" scoped>
.bind-device {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0 15px;
  box-sizing: border-box;
  h3 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // font-weight: 700;
    font-size: 16px;
  }
  ul {
    width: 100%;
    height: auto;
    display: flex;
    flex-wrap: wrap;
    li {
      // flex: none;
      // width: 33%;
      padding: 20px;
      box-sizing: border-box;
      > div {
        // border: 1px solid #ddd;
        background: #f7f8fa;
        // border-radius: 8px;
        padding: 10px 10px;
        box-sizing: border-box;
        height: 100px;
        display: flex;
        flex-direction: column;
        // align-items: center;
        justify-content: space-between;
      }
      .not {
        // display: flex;
        // align-items: center;
        // justify-content: center;
        line-height: 80px;
        font-weight: 700;
        padding-left: 20px;
        box-sizing: border-box;
        font-size: 16px;
      }
      h4 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 900;
        font-size: 16px;
        // color: #0089ff;
        b {
          // display: flex;
          width: 75%;
          height: auto;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        span {
          color: red;
          cursor: pointer;
        }
      }
      p {
        color: #bbb;
      }
    }
  }
}
</style>
