<template>
  <div class="choseArea">
    <el-dialog :title="title" v-model="dialogVisible" width="580px" append-to-body>
      <el-form :model="form" ref="ruleForm">
        <el-form-item :label="title.split('分配')[1] + '名称:'" :label-width="formLabelWidth" prop="region">
          <el-select style="width: 220px" :popper-append-to-body="false" class="product-style" v-show="title != '分配区域' && title != '分配设备'" v-model="id" filterable multiple :placeholder="'请选择' + title.split('分配')[1]">
            <el-option v-for="item in options" :title="item.name" :disabled="item.disabled" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <el-select style="width: 220px" :popper-append-to-body="false" class="product-style" v-show="title == '分配设备'" multiple v-model="id" filterable :placeholder="'请选择' + title.split('分配')[1]">
            <el-option v-for="item in options" :title="item.name" :disabled="item.disabled" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <el-cascader
            popper-class="pc-sel-area-cascader"
            style="width: 220px"
            v-model="areaList"
            v-if="title == '分配区域'"
            filterable
            :options="options"
            :props="{
              checkStrictly: true,
              value: 'id',
              label: 'name',
              disabled: 'disabled',
            }"
            clearable
          >
            <template #default="{ data }">
              <el-tooltip :disabled="data.name.length < 11" class="item" effect="dark" :content="data.name" placement="top-start">
                <span>{{ data.name }}</span>
              </el-tooltip>
            </template>
          </el-cascader>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel"> 取 消 </el-button>
          <el-button type="primary" @click="submit"> 确 定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getdeviceGroupList, getdeviceTypeList, getDeviceList } from "@/views/pages/apis/deviceManage";
import { getLocationsTenantCurrent } from "@/views/pages/apis/locationManang";
import { getRegionsTenantCurrent, delRegionsById } from "@/views/pages/apis/regionManage";

export default defineComponent({
  name: "dialogDevice",
  props: {
    title: {
      type: String,
      default: "",
    },
    list: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  emits: ["confirm"],
  data() {
    return {
      dialogVisible: false,
      id: [],
      formLabelWidth: "120px",
      options: [],
      form: {},
      areaList: [],
      paging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      allRegion: [],
      allRegionByPage: [],
      allRegionSelect: [],
    };
  },
  watch: {
    title(val) {
      this.$nextTick(() => {
        switch (val) {
          case "分配设备":
            this.getDevice();
            break;
          case "分配设备分组":
            this.getDeviceGroup();
            break;
          case "分配设备类型":
            this.getDeviceType();
            break;
          case "分配区域":
            this.handleRefreshRegionTable();
            this.getRegions();

            break;
          case "分配场所":
            this.getLocation();
            break;
        }
      });
    },
    list(val) {
      this.$nextTick(() => {
        switch (this.title) {
          case "分配设备":
            this.getDevice();
            break;
          case "分配设备分组":
            this.getDeviceGroup();
            break;
          case "分配设备类型":
            this.getDeviceType();
            break;
          case "分配区域":
            this.handleRefreshRegionTable();
            this.getRegions();

            break;
          case "分配场所":
            this.getLocation();
            break;
        }
      });
    },
  },
  mounted() {
    this.handleRefreshRegionTable();
  },
  methods: {
    // findChild(array) {
    //   for (let i in array) {
    //     var data = array[i];

    //     this.regions.forEach((item) => {
    //       if (item === data.id) {
    //         data.disabled = true;
    //       }
    //     });

    //     if (data.children) {
    //       this.findChild(data.children);
    //     }
    //   }
    //   // // console.log(this.allRegionSelect);
    // },
    getDevice() {
      getDeviceList({ pageNumber: 1, pageSize: 10000 }).then((res) => {
        if (res.success) {
          this.options = [...res.data];
          for (let i = 0; i < this.list.length; i++) {
            this.options.findIndex((item) => {
              if (item.id === this.list[i].id) {
                item.disabled = true;
              }
            });
            // 前两个数据的key值相同，打印出对应的下标 ，后面找不相同的key  输出-1
          }
        } else {
          ElMessage.error(JSON.parse(res.data)?.message);
        }
      });
    },
    getDeviceType() {
      getdeviceTypeList({}).then((res) => {
        if (res.success) {
          this.options = [...res.data];
          for (let i = 0; i < this.list.length; i++) {
            this.options.findIndex((item) => {
              if (item.id === this.list[i].id) {
                item.disabled = true;
              }
            });
            // 前两个数据的key值相同，打印出对应的下标 ，后面找不相同的key  输出-1
          }
        } else {
          ElMessage.error(JSON.parse(res.data)?.message);
        }
      });
    },
    getDeviceGroup() {
      getdeviceGroupList({}).then((res) => {
        if (res.success) {
          this.options = [...res.data];
          for (let i = 0; i < this.list.length; i++) {
            this.options.findIndex((item) => {
              if (item.id === this.list[i].id) {
                item.disabled = true;
              }
            });
            // 前两个数据的key值相同，打印出对应的下标 ，后面找不相同的key  输出-1
          }
        } else {
          ElMessage.error(JSON.parse(res.data)?.message);
        }
      });
    },
    getLocation() {
      const params = {
        pageNumber: 1,
        pageSize: 9999,
      };
      getLocationsTenantCurrent(params).then((res) => {
        if (res.success) {
          res.data.forEach((v, i) => {
            this.options = [...res.data];
            for (let i = 0; i < this.list.length; i++) {
              this.options.findIndex((item) => {
                if (item.id === this.list[i].id) {
                  item.disabled = true;
                }
              });
              // 前两个数据的key值相同，打印出对应的下标 ，后面找不相同的key  输出-1
            }
          });
        }
      });
    },
    getRegions() {
      // this.findChild(this.allRegionSelect);

      this.options = [...this.allRegionSelect];
      for (let i = 0; i < this.list.length; i++) {
        this.options.findIndex((item) => {
          if (item.id === this.list[i].id) {
            item.disabled = true;
          }
        });
        // 前两个数据的key值相同，打印出对应的下标 ，后面找不相同的key  输出-1
      }
      // getRegionsTenantCurrent({}).then((res) => {
      //   // // console.log(res, 555);
      //   if (res.success) {
      //     this.options = [...res.data];
      //   }
      // });
    },

    submit() {
      this.dialogVisible = false;
      this.$refs["ruleForm"].resetFields();

      if (this.title != "分配区域") {
        this.$emit("confirm", { id: this.id, type: this.title });
      } else {
        this.$emit("confirm", {
          id: this.areaList[this.areaList.length - 1],
          type: this.title,
        });
      }
    },
    cancel() {
      this.dialogVisible = false;
      this.$refs["ruleForm"].resetFields();
    },
    handleRefreshRegionTable() {
      getRegionsTenantCurrent({ sort: "createdTime,desc" }).then(({ success, data }) => {
        if (success) {
          this.tableData = this.setTableData(data.sort((x, y) => y.createdTime - x.createdTime));
          this.allRegionSelect = JSON.parse(JSON.stringify(this.tableData));
          this.paging.total = this.tableData.length;
          this.tableData = this.setTableDataByPage(this.tableData);
          if (!this.tableData.length && this.paging.pageNumber !== 1) {
            this.paging.pageNumber = 1;
            this.handleRefreshRegionTable();
          }
        } else console.error(JSON.parse(data)?.message || "列表获取失败");
      });
    },
    setTableDataByPage(data) {
      let result = [];
      for (var i = 0, len = data.length; i < len; i += this.paging.pageSize) {
        result.push(data.slice(i, i + this.paging.pageSize));
      }
      this.allRegionByPage = result;
      return result[this.paging.pageNumber - 1] || [];
    },
    setTableData(data) {
      this.allRegion = JSON.parse(JSON.stringify(data));
      let _formatter = (list) => {
        for (let i = 0; i < list.length; i++) {
          list[i].children = [];
          list[i].isEdit = false;
          let _filter = this.allRegion.filter((v) => {
            return list[i].id === v.parentId;
          });
          if (_filter && _filter?.length) {
            list[i].children = _filter;
            _formatter(list[i].children);
          }
        }
      };
      let result = data.filter((v) => !v.parentId);
      _formatter(result);
      _formatter = null;

      return result;
    },
    handleDelRegion(row) {
      ElMessageBox.confirm(`确定删除区域"${row.name}"?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delRegionsById({ id: row.id }).then(({ success, data }) => {
            if (success) {
              ElMessage.success("操作成功");
              this.refresh ? this.refresh() : this.handleRefreshRegionTable();
            } else ElMessage.error(JSON.parse(data)?.message || "操作失败");
          });
        })
        .catch(() => {
          /* code */
        });
    },
    handleAddChild(row) {
      const editData = row.children.find(({ id }) => !id);
      const newRegion = Object.assign(
        {
          parentId: row.id,
          isEdit: true,
          label: "",
          name: "",
          description: "",
          externalId: "",
        },
        editData || {}
      );
      // row.children = row.children.filter((v) => v.id)
      if (row.isExpend) {
        row.children.unshift(newRegion);
      } else {
        this.$refs[`${row.parentId}-table`]?.toggleRowExpansion(row);
        row.children.unshift(newRegion);
      }
    },
    formatterTable(_row, _col, v) {
      switch (_col.property) {
        default:
          return v || "";
      }
    },
  },
  expose: ["dialogVisible", "id", "areaList"],
});
</script>
<style scoped lang="scss">
:deep(.product-style) {
  .el-select-dropdown__item {
    width: 300px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
:deep() {
  .el-cascader-menu__list {
    max-width: 300px;
  }
}
.el-cascader-menu__list {
  max-width: 300px !important;
}
</style>
