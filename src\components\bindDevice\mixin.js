import { getDeviceList } from "@/views/pages/apis/deviceManage";

export default {
  data() {
    return {
      deviceOptions: {},
      paging: {
        pageNumber: 1,
        pageSize: 9999,
        total: 0,
      },
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      getDeviceList(this.paging).then((res) => {
        if (res.success) {
          let options = {};
          res.data.forEach((item) => {
            options[item.id] = item.name;
          });
          this.deviceOptions = options;
        }
      });
    },
  },
};
