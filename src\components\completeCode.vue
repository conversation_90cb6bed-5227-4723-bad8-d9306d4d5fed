<template>
  <el-card>
    <template #header> {{ title + $t("generalDetails.code") }} </template>
    <div>
      <FormModel ref="formRef" :model="{}" label-position="left" :label-width="`${130}px`">
        <FormItem v-if="finishCodeName" :span="24" :label="`${title}${t('generalDetails.code name')}`" tooltip="" prop="" :rules="[]">{{ finishCodeName }}</FormItem>
        <!-- <FormItem :span="24" :label="`${title}代码描述`" tooltip="" prop="" :rules="[]">{{ finishCodeDesc }}</FormItem> -->
        <FormItem v-if="finishContent" :span="24" :label="`${title}${t('generalDetails.content')}`" tooltip="" prop="" :rules="[]"><div v-html="finishContent"></div></FormItem>
      </FormModel>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { toRefs } from "vue";
import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
interface Props {
  finishCodeName: string;
  finishCodeDesc: string;
  finishContent: string;
  title: string;
}

const props = withDefaults(defineProps<Props>(), {
  finishCodeName: "",
  finishCodeDesc: "",
  finishContent: "",
  title: "",
});
console.log(props, "props");
const { finishCodeName, finishCodeDesc, finishContent, title } = toRefs(props);
</script>
