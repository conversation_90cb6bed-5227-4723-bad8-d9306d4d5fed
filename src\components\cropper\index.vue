<template>
  <div class="vue-cropper" ref="cropper" @mouseenter.self="scaleImg" @mouseleave.self="cancelScale">
    <div class="cropper-box" v-if="imgs">
      <div
        class="cropper-box-canvas"
        v-show="!loading"
        :style="{
          width: `${trueWidth}px`,
          height: `${trueHeight}px`,
          transform: `scale(${scale}, ${scale}) translate3d(${x / scale}px, ${y / scale}px, 0) rotateZ(${rotate * 90}deg)`,
        }"
      >
        <img :src="imgs" alt="cropper-img" ref="cropperImg" />
      </div>
    </div>
    <div class="cropper-drag-box" :class="{ 'cropper-move': move && !crop, 'cropper-crop': crop, 'cropper-modal': cropping }" @mousedown.self="startMove" @touchstart.self="startMove"></div>
    <div
      v-show="cropping"
      class="cropper-crop-box"
      :style="{
        width: `${cropW}px`,
        height: `${cropH}px`,
        transform: `translate3d(${cropOffsertX}px, ${cropOffsertY}px, 0)`,
      }"
    >
      <span class="cropper-view-box">
        <img
          :style="{
            width: `${trueWidth}px`,
            height: `${trueHeight}px`,
            transform: `scale(${scale}, ${scale}) translate3d(${(x - cropOffsertX) / scale}px, ${(y - cropOffsertY) / scale}px, 0) rotateZ(${rotate * 90}deg)`,
          }"
          :src="imgs"
          alt="cropper-img"
        />
      </span>
      <span class="cropper-face cropper-move" @mousedown="cropMove" @touchstart="cropMove"></span>
      <span class="crop-info" v-if="props.info" :style="{ top: cropInfo.top }">{{ cropInfo.width }} × {{ cropInfo.height }}</span>
      <span v-if="!props.fixedBox">
        <span class="crop-line line-w" @mousedown.self.stop="changeCropSize($event, false, true, 0, 1)" @touchstart.self.stop="changeCropSize($event, false, true, 0, 1)"></span>
        <span class="crop-line line-a" @mousedown.self.stop="changeCropSize($event, true, false, 1, 0)" @touchstart.self.stop="changeCropSize($event, true, false, 1, 0)"></span>
        <span class="crop-line line-s" @mousedown.self.stop="changeCropSize($event, false, true, 0, 2)" @touchstart.self.stop="changeCropSize($event, false, true, 0, 2)"></span>
        <span class="crop-line line-d" @mousedown.self.stop="changeCropSize($event, true, false, 2, 0)" @touchstart.self.stop="changeCropSize($event, true, false, 2, 0)"></span>
        <span class="crop-point point1" @mousedown.self.stop="changeCropSize($event, true, true, 1, 1)" @touchstart.self.stop="changeCropSize($event, true, true, 1, 1)"></span>
        <span class="crop-point point2" @mousedown.self.stop="changeCropSize($event, false, true, 0, 1)" @touchstart.self.stop="changeCropSize($event, false, true, 0, 1)"></span>
        <span class="crop-point point3" @mousedown.self.stop="changeCropSize($event, true, true, 2, 1)" @touchstart.self.stop="changeCropSize($event, true, true, 2, 1)"></span>
        <span class="crop-point point4" @mousedown.self.stop="changeCropSize($event, true, false, 1, 0)" @touchstart.self.stop="changeCropSize($event, true, false, 1, 0)"></span>
        <span class="crop-point point5" @mousedown.self.stop="changeCropSize($event, true, false, 2, 0)" @touchstart.self.stop="changeCropSize($event, true, false, 2, 0)"></span>
        <span class="crop-point point6" @mousedown.self.stop="changeCropSize($event, true, true, 1, 2)" @touchstart.self.stop="changeCropSize($event, true, true, 1, 2)"></span>
        <span class="crop-point point7" @mousedown.self.stop="changeCropSize($event, false, true, 0, 2)" @touchstart.self.stop="changeCropSize($event, false, true, 0, 2)"></span>
        <span class="crop-point point8" @mousedown.self.stop="changeCropSize($event, true, true, 2, 2)" @touchstart.self.stop="changeCropSize($event, true, true, 2, 2)"></span>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts" name="Cropper">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from "vue";
import Exif from "./exif";
import { Buffer } from "buffer";

const cropper = ref<HTMLDivElement>();

interface Props {
  img: string | null /* Blob | File */;
  // 输出图片压缩比
  outputSize?: number;
  outputType?: string;
  info?: boolean;
  // 是否开启滚轮放大缩小
  canScale?: boolean;
  // 是否自成截图框
  autoCrop?: boolean;
  autoCropWidth?: number | string;
  autoCropHeight?: number | string;
  // 是否开启固定宽高比
  fixed?: boolean;
  // 宽高比 w/h
  fixedNumber?: [number, number];
  // 固定大小 禁止改变截图框大小
  fixedBox?: boolean;
  // 输出截图是否缩放
  full?: boolean;
  // 是否可以拖动图片
  canMove?: boolean;
  // 是否可以拖动截图框
  canMoveBox?: boolean;
  // 上传图片按照原始比例显示
  original?: boolean;
  // 截图框能否超过图片
  centerBox?: boolean;
  // 是否根据dpr输出高清图片
  high?: boolean;
  // 截图框展示宽高类型
  infoTrue?: boolean;
  // 可以压缩图片宽高  默认不超过200
  maxImgSize?: number | string;
  // 倍数  可渲染当前截图框的n倍 0 - 1000;
  enlarge?: number | string;

  // 自动预览的固定宽度
  preW?: number | string;
  /*
   * 图片布局方式 mode 实现和css背景一样的效果
   * contain  居中布局 默认不会缩放 保证图片在容器里面 mode?: 'contain'
   * cover    拉伸布局 填充整个容器  mode?: 'cover'
   * 如果仅有一个数值被给定，这个数值将作为宽度值大小，高度值将被设定为auto。 mode?: '50px'
   * 如果有两个数值被给定，第一个将作为宽度值大小，第二个作为高度值大小。 mode?: '50px 60px'
   */
  mode?: "contain" | "cover";
  // 限制最小区域,可传1以上的数字和字符串，限制长宽都是这么大
  // 也可以传数组[90,90]
  limitMinSize?: number | string | [number, number];
}
const props = withDefaults(defineProps<Props>(), {
  img: "",
  outputSize: 1,
  outputType: "jpeg",
  info: true,
  canScale: true,
  autoCrop: false,
  autoCropWidth: 0,
  autoCropHeight: 0,
  fixed: false,
  fixedNumber: () => [1, 1],
  fixedBox: false,
  full: false,
  canMove: true,
  canMoveBox: true,
  original: false,
  centerBox: false,
  high: true,
  infoTrue: true,
  maxImgSize: 2000,
  enlarge: 1,
  preW: 0,
  mode: "contain",
  limitMinSize: 10,
});

const emits = defineEmits<{
  (e: "imgLoad", status: "error" | "success"): void;
  (e: "imgMoving", status: { moving: boolean; axis: Record<"x1" | "x2" | "y1" | "y2", number> }): void;
  (e: "changeCropSize", size: Record<"width" | "height", number>): void;
  (e: "cropMoving", status: { moving: boolean; axis: Record<"x1" | "x2" | "y1" | "y2", number> }): void;
  (e: "realTime", preview: { div: { width: string; height: string }; w: number; h: number; url: string; img: { width: string; height: string; transform: string }; html: string }): void;
}>();

// 容器高宽
const w = ref(0);
const h = ref(0);
// 图片缩放比例
const scale = ref(1);
// 图片偏移x轴
const x = ref(0);
// 图片偏移y轴
const y = ref(0);
// 图片加载
const loading = ref(true);
// 图片真实宽度
const trueWidth = ref(0);
// 图片真实高度
const trueHeight = ref(0);
const move = ref(true);
// 移动的x
const moveX = ref(0);
// 移动的y
const moveY = ref(0);
// 开启截图
const crop = ref(false);
// 正在截图
const cropping = ref(false);
// 裁剪框大小
const cropW = ref(0);
const cropH = ref(0);
const cropOldW = ref(0);
const cropOldH = ref(0);
// 判断是否能够改变
const canChangeX = ref(false);
const canChangeY = ref(false);
// 改变的基准点
const changeCropTypeX = ref(1);
const changeCropTypeY = ref(1);
// 裁剪框的坐标轴
const cropX = ref(0);
const cropY = ref(0);
const cropChangeX = ref(0);
const cropChangeY = ref(0);
const cropOffsertX = ref(0);
const cropOffsertY = ref(0);
// 支持的滚动事件
const support = ref<"wheel" | "mousewheel" | "DOMMouseScroll">("onwheel" in document.createElement("div") ? "wheel" : "onmousewheel" in document ? "mousewheel" : "DOMMouseScroll");
// 移动端手指缩放
const touches = ref<Touch[]>([]);
const touchNow = ref(false);
// 图片旋转
const rotate = ref(0);
const isIos = ref<boolean>(false);
const orientation = ref(0);
const imgs = ref("");
// 图片缩放系数
const coe = ref(0.2);
// 是否正在多次缩放
const scaling = ref(false);
const scalingSet = ref<NodeJS.Timeout | undefined>();
const coeStatus = ref<"" | "add" | "reduce">("");
// 控制emit触发频率
const isCanShow = ref(true);

interface CropInfo {
  top: string;
  width: string;
  height: string;
}
const cropInfo = computed<CropInfo>(() => {
  const obj: Omit<CropInfo, "width" | "height"> & { width: number; height: number } = {
    top: cropOffsertY.value > 21 ? "-21px" : "0px",
    width: cropW.value > 0 ? cropW.value : 0,
    height: cropH.value > 0 ? cropH.value : 0,
  };
  if (props.infoTrue) {
    let dpr = 1;
    if (props.high && !props.full) {
      dpr = window.devicePixelRatio;
    }
    if (props.enlarge !== 1 && !props.full) {
      dpr = Math.abs(Number(props.enlarge));
    }
    obj.width = Number(obj.width) * dpr;
    obj.height = Number(obj.height) * dpr;
    if (props.full) {
      obj.width = obj.width / scale.value;
      obj.height = obj.height / scale.value;
    }
  }
  return { top: obj.top, width: obj.width.toFixed(0), height: obj.height.toFixed(0) };
});

const isIE = computed<boolean>(() => Object.prototype.hasOwnProperty.call(window, "ActiveXObject") || "ActiveXObject" in window);

const passive = computed<{ passive?: boolean }>(() => (isIE.value ? {} : { passive: false }));

watch(
  () => props.img,
  () => {
    // 如果图片改变， 重新布局
    checkedImg();
  }
);
watch(imgs, (val) => {
  if (val === "") return;
  reload();
});
watch(cropW, () => showPreview());
watch(cropH, () => showPreview());
watch(cropOffsertX, () => showPreview());
watch(cropOffsertY, () => showPreview());
watch(scale, () => showPreview());
watch(x, () => showPreview());
watch(y, () => showPreview());
watch(
  () => props.autoCrop,
  (val) => {
    if (val) goAutoCrop();
  }
);
// 修改了自动截图框
watch(
  () => props.autoCropWidth,
  () => {
    if (props.autoCrop) goAutoCrop();
  }
);
watch(
  () => props.autoCropHeight,
  () => {
    if (props.autoCrop) goAutoCrop();
  }
);
watch(
  () => props.mode,
  () => {
    checkedImg();
  }
);
watch(rotate, () => {
  showPreview();
  if (props.autoCrop) {
    goAutoCrop(cropW.value, cropH.value);
  } else {
    if (cropW.value > 0 || cropH.value > 0) {
      goAutoCrop(cropW.value, cropH.value);
    }
  }
});

onMounted(() => {
  const u = navigator.userAgent;
  isIos.value = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
  // 兼容blob
  if (!HTMLCanvasElement.prototype.toBlob) {
    Object.defineProperty(HTMLCanvasElement.prototype, "toBlob", {
      value: function toBlob(this: HTMLCanvasElement, callback: (blob: Blob) => void, type: string, quality: number) {
        const base64Url = this.toDataURL(type, quality);
        const base64 = /^data\:(?<type>[^\;]+)\;base64,(?<data>.*)$/gim.exec(base64Url);
        callback(new Blob([Buffer.from(base64?.groups?.data as string, "base64")], { type: type || base64?.groups?.type }));
      },
    });
  }
  showPreview();
  checkedImg();
});
onBeforeUnmount(() => {
  window.removeEventListener("mousemove", moveCrop);
  window.removeEventListener("mouseup", leaveCrop);
  window.removeEventListener("touchmove", moveCrop);
  window.removeEventListener("touchend", leaveCrop);
  cancelScale();
});

function getVersion(name: string): number[] {
  var arr = navigator.userAgent.split(" ");
  var chromeVersion = "";
  let result = [];
  const reg = new RegExp(name, "i");
  for (var i = 0; i < arr.length; i++) {
    if (reg.test(arr[i])) chromeVersion = arr[i];
  }
  if (chromeVersion) {
    result = chromeVersion.split("/")[1].split(".");
  } else {
    result = ["0", "0", "0"];
  }
  return result.map((v) => Number(v));
}
function checkOrientationImage(img: HTMLImageElement, orientation: number, width: number, height: number) {
  // 如果是 chrome内核版本在81 safari 在 605 以上不处理图片旋转
  // alert(navigator.userAgent)
  if (getVersion("chrome")[0] >= 81) {
    orientation = -1;
  } else {
    if (getVersion("safari")[0] >= 605) {
      const safariVersion = getVersion("version");
      if (safariVersion[0] > 13 && safariVersion[1] > 1) {
        orientation = -1;
      }
    } else {
      //  判断 ios 版本进行处理
      // 针对 ios 版本大于 13.4的系统不做图片旋转
      const isIos = navigator.userAgent.toLowerCase().match(/cpu iphone os (.*?) like mac os/);
      if (isIos) {
        let version = isIos[1].split("_").map((v) => Number(v));
        if (version[0] > 13 || (version[0] >= 13 && version[1] >= 4)) {
          orientation = -1;
        }
      }
    }
  }

  // alert(`当前处理的orientation${orientation}`)
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");
  if (!ctx) throw new Error("Error: Canvas上下文获取失败");
  ctx.save();

  switch (orientation) {
    case 2:
      canvas.width = width;
      canvas.height = height;
      // horizontal flip
      ctx.translate(width, 0);
      ctx.scale(-1, 1);
      break;
    case 3:
      canvas.width = width;
      canvas.height = height;
      //180 graus
      ctx.translate(width / 2, height / 2);
      ctx.rotate((180 * Math.PI) / 180);
      ctx.translate(-width / 2, -height / 2);
      break;
    case 4:
      canvas.width = width;
      canvas.height = height;
      // vertical flip
      ctx.translate(0, height);
      ctx.scale(1, -1);
      break;
    case 5:
      // vertical flip + 90 rotate right
      canvas.height = width;
      canvas.width = height;
      ctx.rotate(0.5 * Math.PI);
      ctx.scale(1, -1);
      break;
    case 6:
      canvas.width = height;
      canvas.height = width;
      //90 graus
      ctx.translate(height / 2, width / 2);
      ctx.rotate((90 * Math.PI) / 180);
      ctx.translate(-width / 2, -height / 2);
      break;
    case 7:
      // horizontal flip + 90 rotate right
      canvas.height = width;
      canvas.width = height;
      ctx.rotate(0.5 * Math.PI);
      ctx.translate(width, -height);
      ctx.scale(-1, 1);
      break;
    case 8:
      canvas.height = width;
      canvas.width = height;
      //-90 graus
      ctx.translate(height / 2, width / 2);
      ctx.rotate((-90 * Math.PI) / 180);
      ctx.translate(-width / 2, -height / 2);
      break;
    default:
      canvas.width = width;
      canvas.height = height;
  }

  ctx.drawImage(img, 0, 0, width, height);
  ctx.restore();
  canvas.toBlob(
    (blob) => {
      if (!blob) return;
      const data = URL.createObjectURL(blob);
      URL.revokeObjectURL(imgs.value);
      imgs.value = data;
    },
    `image/${props.outputType}`,
    1
  );
}

// checkout img
function checkedImg() {
  if (props.img === null || props.img === "") {
    imgs.value = "";
    clearCrop();
    return;
  }
  loading.value = true;
  scale.value = 1;
  rotate.value = 0;
  clearCrop();
  let img = new Image();
  img.onload = () => {
    if (props.img === "") {
      emits("imgLoad", "error");
      return false;
    }

    let width = img.width;
    let height = img.height;
    Exif.getData(img).then((data) => {
      orientation.value = data.orientation || 1;
      let max = Number(props.maxImgSize);
      if (!orientation.value && width < max && height < max) {
        imgs.value = props.img as string;
        return;
      }

      if (width > max) {
        height = (height / width) * max;
        width = max;
      }

      if (height > max) {
        width = (width / height) * max;
        height = max;
      }
      checkOrientationImage(img, orientation.value, width, height);
    });
  };

  img.onerror = () => {
    emits("imgLoad", "error");
  };

  // 判断如果不是base64图片 再添加crossOrigin属性，否则会导致iOS低版本(10.2)无法显示图片
  if (typeof props.img === "string" && props.img.slice(0, 4) !== "data") img.crossOrigin = "";

  if (isIE.value) {
    var xhr = new XMLHttpRequest();
    xhr.onload = function () {
      var url = URL.createObjectURL(xhr.response);
      img.src = url;
    };
    xhr.open("GET", props.img as string, true);
    xhr.responseType = "blob";
    xhr.send();
  } else {
    img.src = props.img as string;
  }
}
// 当按下鼠标键
function startMove(e: TouchEvent | MouseEvent) {
  e.preventDefault();
  // // console.log("当按下鼠标键");
  // 如果move 为true 表示当前可以拖动
  if (move.value && !crop.value) {
    if (!props.canMove) {
      return false;
    }
    // 开始移动
    moveX.value = ("clientX" in e ? e.clientX : e.touches[0].clientX) - x.value;
    moveY.value = ("clientY" in e ? e.clientY : e.touches[0].clientY) - y.value;
    if ((e as TouchEvent).touches) {
      // // console.log("手指按屏幕 [拖动图片]");
      window.addEventListener("touchmove", moveImg);
      window.addEventListener("touchend", leaveImg);
      if ((e as TouchEvent).touches.length == 2) {
        // 记录手指刚刚放上去
        // // console.log("手指按屏幕 [缩放图片]");
        touches.value = Array.from((e as TouchEvent).touches);
        window.addEventListener("touchmove", touchScale);
        window.addEventListener("touchend", cancelTouchScale);
      }
    } else {
      // // console.log("按下鼠标键 [拖动图片]");
      window.addEventListener("mousemove", moveImg);
      window.addEventListener("mouseup", leaveImg);
    }
    // 触发图片移动事件
    emits("imgMoving", { moving: true, axis: getImgAxis() });
  } else {
    // // console.log("截图");
    // 截图ing
    cropping.value = true;
    // 绑定截图事件
    window.addEventListener("mousemove", createCrop);
    window.addEventListener("mouseup", endCrop);
    window.addEventListener("touchmove", createCrop);
    window.addEventListener("touchend", endCrop);
    cropOffsertX.value = (e as MouseEvent).offsetX ? (e as MouseEvent).offsetX : (e as TouchEvent).touches[0].pageX - (cropper.value as HTMLDivElement).offsetLeft;
    cropOffsertY.value = (e as MouseEvent).offsetY ? (e as MouseEvent).offsetY : (e as TouchEvent).touches[0].pageY - (cropper.value as HTMLDivElement).offsetTop;
    cropX.value = "clientX" in e ? e.clientX : e.touches[0].clientX;
    cropY.value = "clientY" in e ? e.clientY : e.touches[0].clientY;
    cropChangeX.value = cropOffsertX.value;
    cropChangeY.value = cropOffsertY.value;
    cropW.value = 0;
    cropH.value = 0;
  }
}

// 移动端缩放
function touchScale(e: TouchEvent | MouseEvent) {
  e.preventDefault();
  let _scale = scale.value;
  // 记录变化量
  // 第一根手指
  var oldTouch1 = {
    x: touches.value[0].clientX,
    y: touches.value[0].clientY,
  };
  var newTouch1 = {
    x: (e as TouchEvent).touches[0].clientX,
    y: (e as TouchEvent).touches[0].clientY,
  };
  // 第二根手指
  var oldTouch2 = {
    x: touches.value[1].clientX,
    y: touches.value[1].clientY,
  };
  var newTouch2 = {
    x: (e as TouchEvent).touches[1].clientX,
    y: (e as TouchEvent).touches[1].clientY,
  };
  var oldL = Math.sqrt(Math.pow(oldTouch1.x - oldTouch2.x, 2) + Math.pow(oldTouch1.y - oldTouch2.y, 2));
  var newL = Math.sqrt(Math.pow(newTouch1.x - newTouch2.x, 2) + Math.pow(newTouch1.y - newTouch2.y, 2));
  var cha = newL - oldL;
  // 根据图片本身大小 决定每次改变大小的系数, 图片越大系数越小
  // 1px - 0.2
  var coe = 1;
  coe = coe / trueWidth.value > coe / trueHeight.value ? coe / trueHeight.value : coe / trueWidth.value;
  coe = coe > 0.1 ? 0.1 : coe;
  var num = coe * cha;
  if (!touchNow.value) {
    touchNow.value = true;
    if (cha > 0) {
      _scale += Math.abs(num);
    } else if (cha < 0) {
      _scale > Math.abs(num) ? (_scale -= Math.abs(num)) : _scale;
    }
    touches.value = Array.from((e as TouchEvent).touches);
    setTimeout(() => {
      touchNow.value = false;
    }, 8);
    if (!checkoutImgAxis(x.value, y.value, _scale)) {
      return false;
    }
    scale.value = _scale;
  }
}

function cancelTouchScale() {
  window.removeEventListener("touchmove", touchScale);
}

// 移动图片
function moveImg(e: TouchEvent | MouseEvent) {
  e.preventDefault();
  if ("touches" in e && (e as TouchEvent).touches.length === 2) {
    touches.value = Array.from(e.touches);
    window.addEventListener("touchmove", touchScale);
    window.addEventListener("touchend", cancelTouchScale);
    window.removeEventListener("touchmove", moveImg);
    return false;
  }
  let nowX = "clientX" in e ? e.clientX : e.touches[0].clientX;
  let nowY = "clientY" in e ? e.clientY : e.touches[0].clientY;

  let changeX: number, changeY: number;
  changeX = nowX - moveX.value;
  changeY = nowY - moveY.value;

  nextTick(() => {
    if (props.centerBox) {
      let axis = getImgAxis(changeX, changeY, scale.value);
      let cropAxis = getCropAxis();
      let imgW = trueHeight.value * scale.value;
      let imgH = trueWidth.value * scale.value;
      let maxLeft, maxTop, maxRight, maxBottom;
      switch (rotate.value) {
        case 1:
        case -1:
        case 3:
        case -3:
          maxLeft = cropOffsertX.value - (trueWidth.value * (1 - scale.value)) / 2 + (imgW - imgH) / 2;
          maxTop = cropOffsertY.value - (trueHeight.value * (1 - scale.value)) / 2 + (imgH - imgW) / 2;
          maxRight = maxLeft - imgW + cropW.value;
          maxBottom = maxTop - imgH + cropH.value;
          break;
        default:
          maxLeft = cropOffsertX.value - (trueWidth.value * (1 - scale.value)) / 2;
          maxTop = cropOffsertY.value - (trueHeight.value * (1 - scale.value)) / 2;
          maxRight = maxLeft - imgH + cropW.value;
          maxBottom = maxTop - imgW + cropH.value;
          break;
      }

      // 图片左边 图片不能超过截图框
      if (axis.x1 >= cropAxis.x1) {
        changeX = maxLeft;
      }

      // 图片上边 图片不能超过截图框
      if (axis.y1 >= cropAxis.y1) {
        changeY = maxTop;
      }

      // 图片右边
      if (axis.x2 <= cropAxis.x2) {
        changeX = maxRight;
      }

      // 图片下边
      if (axis.y2 <= cropAxis.y2) {
        changeY = maxBottom;
      }
    }
    x.value = changeX;
    y.value = changeY;
    // 触发图片移动事件
    emits("imgMoving", { moving: true, axis: getImgAxis() });
  });
}
// 移动图片结束
function leaveImg() {
  window.removeEventListener("mousemove", moveImg);
  window.removeEventListener("touchmove", moveImg);
  window.removeEventListener("mouseup", leaveImg);
  window.removeEventListener("touchend", leaveImg);
  // 触发图片移动事件
  emits("imgMoving", { moving: false, axis: getImgAxis() });
}
// 缩放图片
function scaleImg() {
  // // console.log("鼠标移入");
  if (props.canScale) window.addEventListener(support.value as "wheel", changeSize, passive.value);
}
// 移出框
function cancelScale() {
  // // console.log("鼠标移出");
  if (props.canScale) window.removeEventListener(support.value as "wheel", changeSize);
}
// 改变大小函数
function changeSize(e: WheelEvent) {
  e.preventDefault();
  let $scale = scale.value;
  let change = e.deltaY || (e as WheelEvent & { wheelDelta: number }).wheelDelta;
  // 根据图片本身大小 决定每次改变大小的系数, 图片越大系数越小
  var isFirefox = navigator.userAgent.indexOf("Firefox");
  change = isFirefox > 0 ? change * 30 : change;
  // 修复ie的滚动缩放
  if (isIE.value) {
    change = -change;
  }
  // 1px - 0.2
  let $coe = coe.value;
  $coe = $coe / trueWidth.value > $coe / trueHeight.value ? $coe / trueHeight.value : $coe / trueWidth.value;
  let num = $coe * change;
  num < 0 ? ($scale += Math.abs(num)) : $scale > Math.abs(num) ? ($scale -= Math.abs(num)) : $scale;
  // 延迟0.1s 每次放大大或者缩小的范围
  let status: "add" | "reduce" = num < 0 ? "add" : "reduce";
  if (status !== coeStatus.value) {
    coeStatus.value = status;
    coe.value = 0.2;
  }
  if (!scaling.value) {
    scalingSet.value = setTimeout(() => {
      scaling.value = false;
      coe.value = coe.value += 0.01;
    }, 50);
  }
  scaling.value = true;
  if (!checkoutImgAxis(x.value, y.value, $scale)) {
    return false;
  }
  scale.value = $scale;
  // // console.log("滚轮滚动", {
  //   width: `${trueWidth.value}px`,
  //   height: `${trueHeight.value}px`,
  //   transform: `scale(${scale.value}, ${scale.value}) translate3d(${x.value / scale.value}px, ${y.value / scale.value}, 0) rotateZ(${rotate.value * 90}deg)`,
  // });
}

// 修改图片大小函数
function changeScale(num: number) {
  let _scale = scale.value;
  num = num || 1;
  var coe = 20;
  coe = coe / trueWidth.value > coe / trueHeight.value ? coe / trueHeight.value : coe / trueWidth.value;
  num = num * coe;
  num > 0 ? (_scale += Math.abs(num)) : _scale > Math.abs(num) ? (_scale -= Math.abs(num)) : _scale;
  if (!checkoutImgAxis(x.value, y.value, _scale)) {
    return false;
  }
  scale.value = _scale;
}
// 创建截图框
function createCrop(e: TouchEvent | MouseEvent) {
  e.preventDefault();
  // 移动生成大小
  var nowX = "clientX" in e ? e.clientX : e.touches ? e.touches[0].clientX : 0;
  var nowY = "clientY" in e ? e.clientY : e.touches ? e.touches[0].clientY : 0;
  nextTick(() => {
    var fw = nowX - cropX.value;
    var fh = nowY - cropY.value;
    if (fw > 0) {
      cropW.value = fw + cropChangeX.value > w.value ? w.value - cropChangeX.value : fw;
      cropOffsertX.value = cropChangeX.value;
    } else {
      cropW.value = w.value - cropChangeX.value + Math.abs(fw) > w.value ? cropChangeX.value : Math.abs(fw);
      cropOffsertX.value = cropChangeX.value + fw > 0 ? cropChangeX.value + fw : 0;
    }

    if (!props.fixed) {
      if (fh > 0) {
        cropH.value = fh + cropChangeY.value > h.value ? h.value - cropChangeY.value : fh;
        cropOffsertY.value = cropChangeY.value;
      } else {
        cropH.value = h.value - cropChangeY.value + Math.abs(fh) > h.value ? cropChangeY.value : Math.abs(fh);
        cropOffsertY.value = cropChangeY.value + fh > 0 ? cropChangeY.value + fh : 0;
      }
    } else {
      var fixedHeight = (cropW.value / props.fixedNumber[0]) * props.fixedNumber[1];
      if (fixedHeight + cropOffsertY.value > h.value) {
        cropH.value = h.value - cropOffsertY.value;
        cropW.value = (cropH.value / props.fixedNumber[1]) * props.fixedNumber[0];
        if (fw > 0) {
          cropOffsertX.value = cropChangeX.value;
        } else {
          cropOffsertX.value = cropChangeX.value - cropW.value;
        }
      } else {
        cropH.value = fixedHeight;
      }
      // cropOffsertY.value = cropOffsertY.value;
    }
  });
}

// 改变截图框大小
function changeCropSize(e: TouchEvent | MouseEvent, w: boolean, h: boolean, typeW: number, typeH: number) {
  e.preventDefault();
  // // console.log("准备移动");
  window.addEventListener("mousemove", changeCropNow);
  window.addEventListener("mouseup", changeCropEnd);
  window.addEventListener("touchmove", changeCropNow);
  window.addEventListener("touchend", changeCropEnd);
  canChangeX.value = w;
  canChangeY.value = h;
  changeCropTypeX.value = typeW;
  changeCropTypeY.value = typeH;
  cropX.value = "clientX" in e ? e.clientX : e.touches[0].clientX;
  cropY.value = "clientY" in e ? e.clientY : e.touches[0].clientY;
  cropOldW.value = cropW.value;
  cropOldH.value = cropH.value;
  cropChangeX.value = cropOffsertX.value;
  cropChangeY.value = cropOffsertY.value;
  if (props.fixed) {
    if (canChangeX.value && canChangeY.value) {
      canChangeY.value = false;
    }
  }
  emits("changeCropSize", { width: cropW.value, height: cropH.value });
}

// 正在改变
function changeCropNow(e: TouchEvent | MouseEvent) {
  e.preventDefault();
  // // console.log("移动中");
  const nowX = "clientX" in e ? e.clientX : e.touches ? e.touches[0].clientX : 0;
  const nowY = "clientY" in e ? e.clientY : e.touches ? e.touches[0].clientY : 0;
  // 容器的宽高
  let wrapperW = w.value;
  let wrapperH = h.value;

  // 不能超过的坐标轴
  let minX = 0;
  let minY = 0;

  if (props.centerBox) {
    let axis = getImgAxis();
    let imgW = axis.x2;
    let imgH = axis.y2;
    minX = axis.x1 > 0 ? axis.x1 : 0;
    minY = axis.y1 > 0 ? axis.y1 : 0;
    if (wrapperW > imgW) {
      wrapperW = imgW;
    }

    if (wrapperH > imgH) {
      wrapperH = imgH;
    }
  }

  nextTick(() => {
    var fw = nowX - cropX.value;
    var fh = nowY - cropY.value;
    if (canChangeX.value) {
      if (changeCropTypeX.value === 1) {
        if (cropOldW.value - fw > 0) {
          cropW.value = wrapperW - cropChangeX.value - fw <= wrapperW - minX ? cropOldW.value - fw : cropOldW.value + cropChangeX.value - minX;
          cropOffsertX.value = wrapperW - cropChangeX.value - fw <= wrapperW - minX ? cropChangeX.value + fw : minX;
        } else {
          cropW.value = Math.abs(fw) + cropChangeX.value <= wrapperW ? Math.abs(fw) - cropOldW.value : wrapperW - cropOldW.value - cropChangeX.value;
          cropOffsertX.value = cropChangeX.value + cropOldW.value;
        }
      } else if (changeCropTypeX.value === 2) {
        if (cropOldW.value + fw > 0) {
          cropW.value = cropOldW.value + fw + cropOffsertX.value <= wrapperW ? cropOldW.value + fw : wrapperW - cropOffsertX.value;
          cropOffsertX.value = cropChangeX.value;
        } else {
          // 右侧坐标抽 超过左侧
          cropW.value = wrapperW - cropChangeX.value + Math.abs(fw + cropOldW.value) <= wrapperW - minX ? Math.abs(fw + cropOldW.value) : cropChangeX.value - minX;
          cropOffsertX.value = wrapperW - cropChangeX.value + Math.abs(fw + cropOldW.value) <= wrapperW - minX ? cropChangeX.value - Math.abs(fw + cropOldW.value) : minX;
        }
      }
    }

    if (canChangeY.value) {
      if (changeCropTypeY.value === 1) {
        if (cropOldH.value - fh > 0) {
          cropH.value = wrapperH - cropChangeY.value - fh <= wrapperH - minY ? cropOldH.value - fh : cropOldH.value + cropChangeY.value - minY;
          cropOffsertY.value = wrapperH - cropChangeY.value - fh <= wrapperH - minY ? cropChangeY.value + fh : minY;
        } else {
          cropH.value = Math.abs(fh) + cropChangeY.value <= wrapperH ? Math.abs(fh) - cropOldH.value : wrapperH - cropOldH.value - cropChangeY.value;
          cropOffsertY.value = cropChangeY.value + cropOldH.value;
        }
      } else if (changeCropTypeY.value === 2) {
        if (cropOldH.value + fh > 0) {
          cropH.value = cropOldH.value + fh + cropOffsertY.value <= wrapperH ? cropOldH.value + fh : wrapperH - cropOffsertY.value;
          cropOffsertY.value = cropChangeY.value;
        } else {
          cropH.value = wrapperH - cropChangeY.value + Math.abs(fh + cropOldH.value) <= wrapperH - minY ? Math.abs(fh + cropOldH.value) : cropChangeY.value - minY;
          cropOffsertY.value = wrapperH - cropChangeY.value + Math.abs(fh + cropOldH.value) <= wrapperH - minY ? cropChangeY.value - Math.abs(fh + cropOldH.value) : minY;
        }
      }
    }

    if (canChangeX.value && props.fixed) {
      var fixedHeight = (cropW.value / props.fixedNumber[0]) * props.fixedNumber[1];
      if (fixedHeight + cropOffsertY.value > wrapperH) {
        cropH.value = wrapperH - cropOffsertY.value;
        cropW.value = (cropH.value / props.fixedNumber[1]) * props.fixedNumber[0];
      } else {
        cropH.value = fixedHeight;
      }
    }

    if (canChangeY.value && props.fixed) {
      var fixedWidth = (cropH.value / props.fixedNumber[1]) * props.fixedNumber[0];
      if (fixedWidth + cropOffsertX.value > wrapperW) {
        cropW.value = wrapperW - cropOffsertX.value;
        cropH.value = (cropW.value / props.fixedNumber[0]) * props.fixedNumber[1];
      } else {
        cropW.value = fixedWidth;
      }
    }
  });
}

function checkCropLimitSize() {
  let limitMinNum: [number, number] = [10, 10];
  if (["string", "number"].includes(typeof props.limitMinSize)) {
    limitMinNum = [Number(props.limitMinSize), Number(props.limitMinSize)];
  } else if (Array.isArray(props.limitMinSize)) {
    limitMinNum = props.limitMinSize as [number, number];
  }

  //限制最小宽度和高度
  let $cropW = Number(parseFloat(String(limitMinNum[0])));
  let $cropH = Number(parseFloat(String(limitMinNum[1])));

  if ($cropW > cropW.value) cropW.value = $cropW;
  if ($cropH > cropH.value) cropH.value = $cropH;
  return [$cropW, $cropH];
}
// 结束改变
function changeCropEnd() {
  // // console.log("移动结束");
  window.removeEventListener("mousemove", changeCropNow);
  window.removeEventListener("mouseup", changeCropEnd);
  window.removeEventListener("touchmove", changeCropNow);
  window.removeEventListener("touchend", changeCropEnd);
}

// 创建完成
function endCrop() {
  if (cropW.value === 0 && cropH.value === 0) {
    cropping.value = false;
  }
  window.removeEventListener("mousemove", createCrop);
  window.removeEventListener("mouseup", endCrop);
  window.removeEventListener("touchmove", createCrop);
  window.removeEventListener("touchend", endCrop);
}
// 开始截图
function startCrop() {
  crop.value = true;
}
// 停止截图
function stopCrop() {
  crop.value = false;
}
// 清除截图
function clearCrop() {
  cropping.value = false;
  cropW.value = 0;
  cropH.value = 0;
}
// 截图移动
function cropMove(e: TouchEvent | MouseEvent) {
  e.preventDefault();
  if (!props.canMoveBox) {
    crop.value = false;
    startMove(e);
    return false;
  }

  if ("touches" in e && (e as TouchEvent).touches.length === 2) {
    crop.value = false;
    startMove(e);
    leaveCrop();
    return false;
  }
  window.addEventListener("mousemove", moveCrop);
  window.addEventListener("mouseup", leaveCrop);
  window.addEventListener("touchmove", moveCrop);
  window.addEventListener("touchend", leaveCrop);
  let x = ("clientX" in e ? e.clientX : e.touches[0].clientX) as number;
  let y = ("clientY" in e ? e.clientY : e.touches[0].clientY) as number;
  let newX: number, newY: number;
  newX = x - cropOffsertX.value;
  newY = y - cropOffsertY.value;
  cropX.value = newX;
  cropY.value = newY;
  // 触发截图框移动事件
  emits("cropMoving", { moving: true, axis: getCropAxis() });
}

function moveCrop(e?: TouchEvent | MouseEvent, isMove?: boolean) {
  let nowX = 0;
  let nowY = 0;
  if (e) {
    e.preventDefault();
    nowX = "clientX" in e ? e.clientX : e.touches[0].clientX;
    nowY = "clientY" in e ? e.clientY : e.touches[0].clientY;
  }
  nextTick(() => {
    let cx, cy;
    let fw = nowX - cropX.value;
    let fh = nowY - cropY.value;
    if (isMove) {
      fw = cropOffsertX.value;
      fh = cropOffsertY.value;
    }
    // 不能超过外层容器
    if (fw <= 0) {
      cx = 0;
    } else if (fw + cropW.value > w.value) {
      cx = w.value - cropW.value;
    } else {
      cx = fw;
    }

    if (fh <= 0) {
      cy = 0;
    } else if (fh + cropH.value > h.value) {
      cy = h.value - cropH.value;
    } else {
      cy = fh;
    }

    // 不能超过图片
    if (props.centerBox) {
      let axis = getImgAxis();
      // 横坐标判断
      if (cx <= axis.x1) {
        cx = axis.x1;
      }

      if (cx + cropW.value > axis.x2) {
        cx = axis.x2 - cropW.value;
      }

      // 纵坐标纵轴
      if (cy <= axis.y1) {
        cy = axis.y1;
      }

      if (cy + cropH.value > axis.y2) {
        cy = axis.y2 - cropH.value;
      }
    }

    cropOffsertX.value = cx;
    cropOffsertY.value = cy;

    // 触发截图框移动事件
    emits("cropMoving", { moving: true, axis: getCropAxis() });
  });
}

// 算出不同场景下面 图片相对于外层容器的坐标轴
function getImgAxis(_x?: number, _y?: number, _scale?: number) {
  _x = _x || x.value;
  _y = _y || y.value;
  _scale = _scale || scale.value;
  // 如果设置了截图框在图片内， 那么限制截图框不能超过图片的坐标
  // 图片的坐标
  const obj = { x1: 0, x2: 0, y1: 0, y2: 0 };
  const imgW = trueWidth.value * _scale;
  const imgH = trueHeight.value * _scale;
  switch (rotate.value) {
    case 0:
      obj.x1 = _x + (trueWidth.value * (1 - _scale)) / 2;
      obj.x2 = obj.x1 + trueWidth.value * _scale;
      obj.y1 = _y + (trueHeight.value * (1 - _scale)) / 2;
      obj.y2 = obj.y1 + trueHeight.value * _scale;
      break;
    case 1:
    case -1:
    case 3:
    case -3:
      obj.x1 = _x + (trueWidth.value * (1 - _scale)) / 2 + (imgW - imgH) / 2;
      obj.x2 = obj.x1 + trueHeight.value * _scale;
      obj.y1 = _y + (trueHeight.value * (1 - _scale)) / 2 + (imgH - imgW) / 2;
      obj.y2 = obj.y1 + trueWidth.value * _scale;
      break;
    default:
      obj.x1 = _x + (trueWidth.value * (1 - _scale)) / 2;
      obj.x2 = obj.x1 + trueWidth.value * _scale;
      obj.y1 = _y + (trueHeight.value * (1 - _scale)) / 2;
      obj.y2 = obj.y1 + trueHeight.value * _scale;
      break;
  }
  return obj;
}

// 获取截图框的坐标轴
function getCropAxis(): Record<"x1" | "x2" | "y1" | "y2", number> {
  let obj = {
    x1: 0,
    x2: 0,
    y1: 0,
    y2: 0,
  };
  obj.x1 = cropOffsertX.value;
  obj.x2 = obj.x1 + cropW.value;
  obj.y1 = cropOffsertY.value;
  obj.y2 = obj.y1 + cropH.value;
  return obj;
}

function leaveCrop() {
  window.removeEventListener("mousemove", moveCrop);
  window.removeEventListener("mouseup", leaveCrop);
  window.removeEventListener("touchmove", moveCrop);
  window.removeEventListener("touchend", leaveCrop);
  // 触发截图框移动事件
  emits("cropMoving", { moving: false, axis: getCropAxis() });
}

function getCropChecked(cb: (data: HTMLCanvasElement) => void) {
  const canvas = document.createElement("canvas");
  const img = new Image();
  // let rotate = rotate.value;
  // let trueWidth = trueWidth.value;
  // let trueHeight = trueHeight.value;
  // let cropOffsertX = cropOffsertX.value;
  // let cropOffsertY = cropOffsertY.value;
  img.onload = () => {
    if (cropW.value !== 0) {
      const ctx = canvas.getContext("2d");
      if (!ctx) throw new Error("Error: 未找到 Canvas 上下文");
      let dpr = 1;
      if (props.high && !props.full) {
        dpr = window.devicePixelRatio;
      }
      if (props.enlarge !== 1 && !props.full) {
        dpr = Math.abs(Number(props.enlarge));
      }
      let width = cropW.value * dpr;
      let height = cropH.value * dpr;
      let imgW = trueWidth.value * scale.value * dpr;
      let imgH = trueHeight.value * scale.value * dpr;
      // 图片x轴偏移
      let dx = (x.value - cropOffsertX.value + (trueWidth.value * (1 - scale.value)) / 2) * dpr;
      // 图片y轴偏移
      let dy = (y.value - cropOffsertY.value + (trueHeight.value * (1 - scale.value)) / 2) * dpr;
      //保存状态
      setCanvasSize(width, height);
      ctx.save();
      switch (rotate.value) {
        case 0:
          if (!props.full) {
            ctx.drawImage(img, dx, dy, imgW, imgH);
          } else {
            // 输出原图比例截图
            setCanvasSize(width / scale.value, height / scale.value);
            ctx.drawImage(img, dx / scale.value, dy / scale.value, imgW / scale.value, imgH / scale.value);
          }
          break;
        case 1:
        case -3:
          if (!props.full) {
            // 换算图片旋转后的坐标弥补
            dx = dx + (imgW - imgH) / 2;
            dy = dy + (imgH - imgW) / 2;
            ctx.rotate((rotate.value * 90 * Math.PI) / 180);
            ctx.drawImage(img, dy, -dx - imgH, imgW, imgH);
          } else {
            setCanvasSize(width / scale.value, height / scale.value);
            // 换算图片旋转后的坐标弥补
            dx = dx / scale.value + (imgW / scale.value - imgH / scale.value) / 2;
            dy = dy / scale.value + (imgH / scale.value - imgW / scale.value) / 2;
            ctx.rotate((rotate.value * 90 * Math.PI) / 180);
            ctx.drawImage(img, dy, -dx - imgH / scale.value, imgW / scale.value, imgH / scale.value);
          }
          break;
        case 2:
        case -2:
          if (!props.full) {
            ctx.rotate((rotate.value * 90 * Math.PI) / 180);
            ctx.drawImage(img, -dx - imgW, -dy - imgH, imgW, imgH);
          } else {
            setCanvasSize(width / scale.value, height / scale.value);
            ctx.rotate((rotate.value * 90 * Math.PI) / 180);
            dx = dx / scale.value;
            dy = dy / scale.value;
            ctx.drawImage(img, -dx - imgW / scale.value, -dy - imgH / scale.value, imgW / scale.value, imgH / scale.value);
          }
          break;
        case 3:
        case -1:
          if (!props.full) {
            // 换算图片旋转后的坐标弥补
            dx = dx + (imgW - imgH) / 2;
            dy = dy + (imgH - imgW) / 2;
            ctx.rotate((rotate.value * 90 * Math.PI) / 180);
            ctx.drawImage(img, -dy - imgW, dx, imgW, imgH);
          } else {
            setCanvasSize(width / scale.value, height / scale.value);
            // 换算图片旋转后的坐标弥补
            dx = dx / scale.value + (imgW / scale.value - imgH / scale.value) / 2;
            dy = dy / scale.value + (imgH / scale.value - imgW / scale.value) / 2;
            ctx.rotate((rotate.value * 90 * Math.PI) / 180);
            ctx.drawImage(img, -dy - imgW / scale.value, dx, imgW / scale.value, imgH / scale.value);
          }
          break;
        default:
          if (!props.full) {
            ctx.drawImage(img, dx, dy, imgW, imgH);
          } else {
            // 输出原图比例截图
            setCanvasSize(width / scale.value, height / scale.value);
            ctx.drawImage(img, dx / scale.value, dy / scale.value, imgW / scale.value, imgH / scale.value);
          }
      }
      ctx.restore();
    } else {
      let width = trueWidth.value * scale.value;
      let height = trueHeight.value * scale.value;
      let ctx = canvas.getContext("2d");
      if (!ctx) throw new Error("Error: 未找到 Canvas 上下文");
      ctx.save();
      switch (rotate.value) {
        case 0:
          setCanvasSize(width, height);
          ctx.drawImage(img, 0, 0, width, height);
          break;
        case 1:
        case -3:
          // 旋转90度 或者-270度 宽度和高度对调
          setCanvasSize(height, width);
          ctx.rotate((rotate.value * 90 * Math.PI) / 180);
          ctx.drawImage(img, 0, -height, width, height);
          break;
        case 2:
        case -2:
          setCanvasSize(width, height);
          ctx.rotate((rotate.value * 90 * Math.PI) / 180);
          ctx.drawImage(img, -width, -height, width, height);
          break;
        case 3:
        case -1:
          setCanvasSize(height, width);
          ctx.rotate((rotate.value * 90 * Math.PI) / 180);
          ctx.drawImage(img, -width, 0, width, height);
          break;
        default:
          setCanvasSize(width, height);
          ctx.drawImage(img, 0, 0, width, height);
      }
      ctx.restore();
    }
    cb(canvas);
  };
  // 判断图片是否是base64
  var s = (props.img as string).slice(0, 4);
  if (s !== "data") {
    img.crossOrigin = "Anonymous";
  }
  img.src = imgs.value;

  function setCanvasSize(width: number, height: number) {
    canvas.width = Math.round(width);
    canvas.height = Math.round(height);
  }
}

// 获取转换成base64 的图片信息
function getCropData(cb?: (url: string) => void): Promise<string> {
  return new Promise((resolve) => {
    getCropChecked((data: HTMLCanvasElement) => {
      const $data = data.toDataURL(`image/${props.outputType}`, props.outputSize);
      resolve($data);
      if (typeof cb === "function") cb($data);
    });
  });
}

//canvas获取为blob对象
function getCropBlob(cb?: (data: Blob | null) => void): Promise<Blob | null> {
  return new Promise((resolve) => {
    getCropChecked((data: HTMLCanvasElement) => {
      data.toBlob(
        (blob) => {
          if (typeof cb === "function") cb(blob);
          resolve(blob);
        },
        `image/${props.outputType}`,
        props.outputSize
      );
    });
  });
}

// 自动预览函数
function showPreview() {
  // 优化不要多次触发
  if (isCanShow.value) {
    isCanShow.value = false;
    setTimeout(() => (isCanShow.value = true), 16);
  } else {
    return false;
  }

  const $w = cropW.value;
  const $h = cropH.value;
  const $scale = scale.value;

  const $transformX = (x.value - cropOffsertX.value) / $scale;
  const $transformY = (y.value - cropOffsertY.value) / $scale;
  const $transformZ = 0;

  const obj = {
    div: {
      width: `${$w}px`,
      height: `${$h}px`,
    },
    w: $w,
    h: $h,
    url: imgs.value,
    img: {
      width: `${trueWidth.value}px`,
      height: `${trueHeight.value}px`,
      transform: `scale(${$scale}, ${$scale}) translate3d(${$transformX}px, ${$transformY}px, ${$transformZ}px) rotateZ(${rotate.value * 90}deg)`,
    },
    html: `
      <div class="show-preview" style="width: ${$w}px; height: ${$h}px,; overflow: hidden">
        <div style="width: ${$w}px; height: ${$h}px">
          <img src=${imgs.value} style="width: ${trueWidth.value}px; height: ${trueHeight.value}px; transform: scale(${$scale}, ${$scale}) translate3d(${$transformX}px, ${$transformY}px, ${$transformZ}px) rotateZ(${rotate.value * 90}deg)">
        </div>
      </div>`,
  };
  // // console.log("自动预览事件", obj);
  emits("realTime", obj);
}
// reload 图片布局函数
function reload() {
  let img = new Image();
  img.onload = () => {
    // 读取图片的信息原始信息， 解析是否需要旋转
    // 读取图片的旋转信息
    // 得到外层容器的宽度高度
    const $w = parseFloat(window.getComputedStyle(cropper.value as HTMLDivElement).width);
    const $h = parseFloat(window.getComputedStyle(cropper.value as HTMLDivElement).height);
    if (props.autoCropWidth && props.autoCropHeight) {
      w.value = parseFloat(String(props.autoCropWidth));
      h.value = parseFloat(String(props.autoCropHeight));
    } else {
      w.value = $w;
      h.value = $h;
    }

    // 存入图片真实高度
    // // console.log(`${img.width}px x ${img.height}px`);

    trueWidth.value = img.width;
    trueHeight.value = img.height;

    // 判断是否需要压缩大图
    if (!props.original) {
      // 判断布局方式 mode
      scale.value = checkedMode();
    } else {
      scale.value = 1;
    }

    nextTick(() => {
      x.value = -(trueWidth.value - trueWidth.value * scale.value) / 2 + ($w - trueWidth.value * scale.value) / 2;
      y.value = -(trueHeight.value - trueHeight.value * scale.value) / 2 + ($h - trueHeight.value * scale.value) / 2;
      // // console.log("背景布局", scale.value, x.value / scale.value, y.value / scale.value);
      loading.value = false;
      // // 获取是否开启了自动截图
      if (props.autoCrop) goAutoCrop();
      // 图片加载成功的回调
      emits("imgLoad", "success");
      setTimeout(() => showPreview(), 20);
    });
  };
  img.onerror = () => {
    emits("imgLoad", "error");
  };
  img.src = imgs.value;
}
// 背景布局的函数
function checkedMode() {
  let $scale = 1;
  // 通过字符串分割
  let imgW = trueWidth.value;
  let imgH = trueHeight.value;
  const arr = props.mode.split(" ");
  switch (arr[0]) {
    case "contain":
      if (trueWidth.value > w.value) {
        // 如果图片宽度大于容器宽度
        $scale = w.value / trueWidth.value;
      }

      if (trueHeight.value * $scale > h.value) {
        $scale = h.value / trueHeight.value;
      }
      break;
    case "cover":
      // 扩展布局 默认填充满整个容器
      // 图片宽度大于容器
      imgW = w.value;
      $scale = imgW / trueWidth.value;
      imgH = imgH * $scale;
      // 如果扩展之后高度小于容器的外层高度 继续扩展高度
      if (imgH < h.value) {
        imgH = h.value;
        $scale = imgH / trueHeight.value;
      }
      break;
    default:
      try {
        let str = arr[0];
        if (str.search("px") !== -1) {
          str = str.replace("px", "");
          imgW = parseFloat(str);
          const scaleX = imgW / trueWidth.value;
          let scaleY = 1;
          let strH = arr[1];
          if (strH.search("px") !== -1) {
            strH = strH.replace("px", "");
            imgH = parseFloat(strH);
            scaleY = imgH / trueHeight.value;
          }
          $scale = Math.min(scaleX, scaleY);
        }
        if (str.search("%") !== -1) {
          str = str.replace("%", "");
          imgW = (parseFloat(str) / 100) * w.value;
          $scale = imgW / trueWidth.value;
        }

        if (arr.length === 2 && str === "auto") {
          let str2 = arr[1];
          if (str2.search("px") !== -1) {
            str2 = str2.replace("px", "");
            imgH = parseFloat(str2);
            $scale = imgH / trueHeight.value;
          }
          if (str2.search("%") !== -1) {
            str2 = str2.replace("%", "");
            imgH = (parseFloat(str2) / 100) * h.value;
            $scale = imgH / trueHeight.value;
          }
        }
      } catch (error) {
        $scale = 1;
      }
  }
  return $scale;
}
// 自动截图函数
function goAutoCrop(cw?: number, ch?: number) {
  if (imgs.value === "" || imgs.value === null) return;
  clearCrop();
  nextTick(() => {
    cropping.value = true;
    let $maxWidth = w.value;
    let $maxHeight = h.value;
    if (props.centerBox) {
      const switchWH = Math.abs(rotate.value) % 2 > 0;
      let imgW = (switchWH ? trueHeight.value : trueWidth.value) * scale.value;
      let imgH = (switchWH ? trueWidth.value : trueHeight.value) * scale.value;
      $maxWidth = imgW < $maxWidth ? imgW : $maxWidth;
      $maxHeight = imgH < $maxHeight ? imgH : $maxHeight;
    }
    // 截图框默认大小
    // 如果为0 那么计算容器大小 默认为80%
    let $w = cw ? cw : parseFloat(String(props.autoCropWidth));
    let $h = ch ? ch : parseFloat(String(props.autoCropHeight));
    if ($w === 0 || $h === 0) {
      $w = $maxWidth * 0.8;
      $h = $maxHeight * 0.8;
    }
    $w = $w > $maxWidth ? $maxWidth : $w;
    $h = $h > $maxHeight ? $maxHeight : $h;
    if (props.fixed) {
      $h = ($w / props.fixedNumber[0]) * props.fixedNumber[1];
    }
    // 如果比例之后 高度大于h
    if ($h > h.value) {
      $h = h.value;
      $w = ($h / props.fixedNumber[1]) * props.fixedNumber[0];
    }
    changeCrop($w, $h);
  });
}
// 手动改变截图框大小函数
function changeCrop(cw: number, ch: number) {
  if (props.centerBox) {
    // 修复初始化时候在centerBox=true情况下
    const axis = getImgAxis();
    if (cw > axis.x2 - axis.x1) {
      // 宽度超标
      cw = axis.x2 - axis.x1;
      ch = (cw / props.fixedNumber[0]) * props.fixedNumber[1];
    }
    if (ch > axis.y2 - axis.y1) {
      // 高度超标
      ch = axis.y2 - axis.y1;
      cw = (ch / props.fixedNumber[1]) * props.fixedNumber[0];
    }
  }
  // 判断是否大于容器
  cropW.value = cw;
  cropH.value = ch;
  checkCropLimitSize();
  // // console.log("截图框大小", cropW.value, cropH.value);
  nextTick(() => {
    const $w = parseFloat(window.getComputedStyle(cropper.value as HTMLDivElement).width);
    const $h = parseFloat(window.getComputedStyle(cropper.value as HTMLDivElement).height);
    // 居中走一走
    cropOffsertX.value = ($w - cropW.value) / 2;
    cropOffsertY.value = ($h - cropH.value) / 2;
    if (props.centerBox) {
      moveCrop(undefined, true);
    }
  });
}

// 重置函数， 恢复组件置初始状态
function refresh() {
  imgs.value = "";
  scale.value = 1;
  crop.value = false;
  rotate.value = 0;
  w.value = 0;
  h.value = 0;
  trueWidth.value = 0;
  trueHeight.value = 0;
  clearCrop();
  nextTick(() => {
    checkedImg();
  });
}
// 向左边旋转
function rotateLeft() {
  rotate.value = rotate.value <= -3 ? 0 : rotate.value - 1;
}

// 向右边旋转
function rotateRight() {
  rotate.value = rotate.value >= 3 ? 0 : rotate.value + 1;
}

// 清除旋转
function rotateClear() {
  rotate.value = 0;
}

// 图片坐标点校验
function checkoutImgAxis(_x: number, _y: number, _scale: number) {
  _x = _x || x.value;
  _y = _y || y.value;
  _scale = _scale || scale.value;
  let canGo = true;
  // 开始校验 如果说缩放之后的坐标在截图框外 则阻止缩放
  if (props.centerBox) {
    let axis = getImgAxis(_x, _y, _scale);
    let cropAxis = getCropAxis();
    // 左边的横坐标 图片不能超过截图框
    if (axis.x1 >= cropAxis.x1) canGo = false;

    // 右边横坐标
    if (axis.x2 <= cropAxis.x2) canGo = false;

    // 纵坐标上面
    if (axis.y1 >= cropAxis.y1) canGo = false;

    // 纵坐标下面
    if (axis.y2 <= cropAxis.y2) canGo = false;
  }
  return canGo;
}

defineExpose({
  cropW,
  cropH,
  getCropData /* 获取转换成base64 的图片信息 */,
  getCropBlob /* canvas获取为blob对象 */,
  startCrop /* 开始截图 */,
  stopCrop /* 停止截图 */,
  clearCrop /* 清除截图 */,
  changeScale /* 修改图片大小函数 */,
  getImgAxis /* 算出不同场景下面 图片相对于外层容器的坐标轴 */,
  getCropAxis /* 获取截图框的坐标轴 */,
  goAutoCrop /* 自动截图函数 */,
  refresh /* 重置函数， 恢复组件置初始状态 */,
  rotateLeft /* 向左边旋转 */,
  rotateRight /* 向右边旋转 */,
  rotateClear /* 清除旋转 */,
});
</script>

<style lang="scss" scoped>
.vue-cropper {
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  direction: ltr;
  touch-action: none;
  text-align: left;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC");
}
.cropper-box-canvas,
.cropper-box,
.cropper-crop-box,
.cropper-drag-box,
.cropper-face {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  user-select: none;
}
.cropper-box-canvas img {
  position: relative;
  text-align: left;
  user-select: none;
  transform: none;
  max-width: none;
  max-height: none;
}
.cropper-box {
  overflow: hidden;
}
.cropper-move {
  cursor: move;
}
.cropper-crop {
  cursor: crosshair;
}
.cropper-modal {
  background: rgba(0, 0, 0, 0.5);
}
.cropper-view-box {
  display: block;
  overflow: hidden;
  width: 100%;
  height: 100%;
  outline: 1px solid #39f;
  outline-color: rgba(51, 153, 255, 0.75);
  user-select: none;
}
.cropper-view-box img {
  user-select: none;
  text-align: left;
  max-width: none;
  max-height: none;
}
.cropper-face {
  top: 0;
  left: 0;
  background-color: #fff;
  opacity: 0.1;
}
.crop-info {
  position: absolute;
  left: 0;
  min-width: 65px;
  text-align: center;
  color: #fff;
  line-height: 20px;
  background-color: rgba(0, 0, 0, 0.8);
  font-size: 12px;
}
.crop-line {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  opacity: 0.1;
}
.line-w {
  top: -3px;
  left: 0;
  height: 5px;
  cursor: n-resize;
}
.line-a {
  top: 0;
  left: -3px;
  width: 5px;
  cursor: w-resize;
}
.line-s {
  bottom: -3px;
  left: 0;
  height: 5px;
  cursor: s-resize;
}
.line-d {
  top: 0;
  right: -3px;
  width: 5px;
  cursor: e-resize;
}
.crop-point {
  position: absolute;
  width: 8px;
  height: 8px;
  opacity: 0.75;
  background-color: #39f;
  border-radius: 100%;
}
.point1 {
  top: -4px;
  left: -4px;
  cursor: nw-resize;
}
.point2 {
  top: -5px;
  left: 50%;
  margin-left: -3px;
  cursor: n-resize;
}
.point3 {
  top: -4px;
  right: -4px;
  cursor: ne-resize;
}
.point4 {
  top: 50%;
  left: -4px;
  margin-top: -3px;
  cursor: w-resize;
}
.point5 {
  top: 50%;
  right: -4px;
  margin-top: -3px;
  cursor: e-resize;
}
.point6 {
  bottom: -5px;
  left: -4px;
  cursor: sw-resize;
}
.point7 {
  bottom: -5px;
  left: 50%;
  margin-left: -3px;
  cursor: s-resize;
}
.point8 {
  bottom: -5px;
  right: -4px;
  cursor: se-resize;
}
@media screen and (max-width: 500px) {
  .crop-point {
    position: absolute;
    width: 20px;
    height: 20px;
    opacity: 0.45;
    background-color: #39f;
    border-radius: 100%;
  }
  .point1 {
    top: -10px;
    left: -10px;
  }
  .point2,
  .point4,
  .point5,
  .point7 {
    display: none;
  }
  .point3 {
    top: -10px;
    right: -10px;
  }
  .point4 {
    top: 0;
    left: 0;
  }
  .point6 {
    bottom: -10px;
    left: -10px;
  }
  .point8 {
    bottom: -10px;
    right: -10px;
  }
}
</style>
