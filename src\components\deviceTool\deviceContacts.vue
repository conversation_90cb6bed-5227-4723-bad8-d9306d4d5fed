<template>
  <div class="contact-dialog">
    <el-dialog :title="i18n.t('devicesContacts.contact information')" v-model="dialogFormVisible" :before-close="cancel" width="23%">
      <div style="height: 300px" class="device-dialog" v-if="isShow">
        <el-scrollbar height="300px">
          <div class="message">
            <div v-for="item in contanctTypes" :key="item.code">
              <h3 v-if="item.list.length > 0">{{ i18n.t(`generalDetails.${item.cnName}`) }}</h3>
              <div v-for="v in item.list" :key="v.id" class="contanct-list">
                <div class="contanct-list-name">
                  <div>
                    <div class="contanct-name">
                      <el-icon><Postcard /></el-icon>
                      <el-text type="primary" style="width: 150px" class="tw-mr-auto tw-text-[14px] tw-leading-[16px]" truncated :title="v.name">{{ v.name }}</el-text>
                    </div>
                    <el-text style="width: 150px" :title="name" truncated class="tw-mr-auto tw-text-[14px] tw-leading-[16px]">{{ name }}</el-text>
                  </div>
                  <div style="margin-right: 2px" v-for="itemA in localesOption" :key="itemA.value">
                    <div v-if="itemA.value == v.language" :style="{ background: `url(${itemA.icon}) no-repeat left / auto`, paddingLeft: '30px' }">{{ itemA.label }}</div>
                  </div>
                  <el-icon @click="previewContancts(v)" v-if="userInfo.hasPermission(资产管理中心_联系人_查看联系人)"><View /></el-icon>
                </div>

                <el-divider style="margin: 16px 0" />

                <div class="tw-flex tw-h-[24px] tw-items-center" :title="i18n.t('devicesContacts.Fixed telephone')">
                  <el-icon class="tw-mr-2"><Phone /></el-icon>
                  <el-text type="info" class="tw-text-[14px]">{{ v.landlinePhone || "--" }}</el-text>
                </div>
                <div class="tw-flex tw-h-[24px] tw-items-center" :title="i18n.t('devicesContacts.Phone')">
                  <el-icon class="tw-mr-2"><Iphone /></el-icon>
                  <el-text type="info" class="tw-text-[14px]">{{ v.mobilePhone || "--" }}</el-text>
                </div>
                <div class="tw-flex tw-h-[24px] tw-items-center" :title="i18n.t('devicesContacts.Email')">
                  <el-icon class="tw-mr-2"><Message /></el-icon>
                  <el-text type="info" class="tw-text-[14px]">{{ v.email || "--" }}</el-text>
                </div>
              </div>
              <!-- <el-col :xs="12" :sm="8" :md="8" :lg="6" :xl="4" class="tw-mb-[16px]" :span="6">
              <el-card shadow="never" :body-style="{ padding: '6px 20px' }" :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
                <template #default>
                  <div class="tw-flex tw-h-[24px] tw-items-center" title="固定电话">
                    <el-icon class="tw-mr-2"><Phone /></el-icon>
                    <el-text type="info" class="tw-text-[14px]">{{ item.landlinePhone || "--" }}</el-text>
                  </div>
                  <div class="tw-flex tw-h-[24px] tw-items-center" title="移动电话">
                    <el-icon class="tw-mr-2"><Iphone /></el-icon>
                    <el-text type="info" class="tw-text-[14px]">{{ item.mobilePhone || "--" }}</el-text>
                  </div>
                  <div class="tw-flex tw-h-[24px] tw-items-center" title="邮箱">
                    <el-icon class="tw-mr-2"><Message /></el-icon>
                    <el-text type="info" class="tw-text-[14px]">{{ item.email || "--" }}</el-text>
                  </div>
                </template>
              </el-card>
            </el-col> -->
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div style="height: 50px" class="device-dialog-none" v-else>
        <div>{{ `${i18n.t("devicesContacts.Unassigend Contact")}` }}</div>
      </div>
    </el-dialog>
  </div>
  <Preview ref="previewRef" :title="i18n.t('devicesContacts.Contacts')" display="dialog"></Preview>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { defineComponent } from "vue";
// import { QuestionFilled as ElIconQuestion } from "@element-plus/icons-vue";
import mixin from "./mixin";
import regionMixin from "./regionMixin";
import { getDeviceDetaile } from "@/views/pages/apis/deviceManage";
import { ElMessage } from "element-plus";
import { DocumentCopy, Phone, Message, Iphone, Postcard, View } from "@element-plus/icons-vue";

import { useClipboard } from "@vueuse/core";
import { getContactByResourceDetails, getContactGroup } from "@/views/pages/apis/device";
import Preview from "@/views/pages/modelExpand/Preview.vue";
import { useI18n } from "vue-i18n";

import { 资产管理中心_联系人_查看联系人 } from "@/views/pages/permission";

import { localesOption } from "@/api/locale.ts";
export default defineComponent({
  name: "EventCenterIntelNoiseReductCreate",
  components: {
    DocumentCopy,
    Phone,
    Message,
    Iphone,
    Postcard,
    View,
    Preview,
  },
  mixins: [mixin, regionMixin],

  emits: ["confirm"],
  data() {
    return {
      i18n: useI18n(),
      dialogFormVisible: false,
      contanctList: [],
      name: "",
      id: "",
      contanctTypes: [],
      isShow: false,
      资产管理中心_联系人_查看联系人,
    };
  },
  watch: {
    id(val) {
      // // console.log(val, 66666);
      // if (val) {
      //   this.getDetail();
      // }
    },
  },
  mounted() {
    // // console.log(this.allRegionSelect);
    // this.getAutoCloseEvent();
  },
  methods: {
    previewContancts(v) {
      this.$refs.previewRef.open(v);
    },
    open(id) {
      // console.log(id, 77777777);
      this.getDetail(id);
    },
    //设备详情
    async getDetail(id) {
      await getContactGroup({}).then((res) => {
        if (res.success) {
          this.contanctTypes = res.data.map((v) => {
            return { ...v, list: [] };
          });
        }
      });
      getContactByResourceDetails({ id }).then((res) => {
        if (res.success) {
          this.isShow = res.data.length > 0 ? true : false;
          res.data.forEach((v, i) => {
            // if(v.contactType==)
            this.contanctTypes.forEach((item) => {
              if (v.contactType == item.code) {
                item.list.push(v.contact);
              }
            });
          });
          // this.contanctTypes= this.contanctTypes.filter((item, index) => {
          //     return  this.contanctTypes.findIndex(obj =>  obj.id !== item.id) === -1;
          // });

          // this.contanctList = res.data;
        }
      });
      getDeviceDetaile({ id }).then((res) => {
        if (res.success) {
          this.name = res.data.name; //设备名称
        }
      });
    },

    cancel() {
      // this.$refs["ruleForm"].resetFields();
      this.dialogFormVisible = false;
      // this.$emit("confirm", { id: this.id });
    },
  },
  expose: ["type", "dialogFormVisible", "id", "open"],
});
</script>

<style lang="scss" scoped>
.contact-dialog {
  :deep(.el-dialog__body) {
    padding-top: 0 !important;
    // padding-left: 10px !important;
  }
}
.device-dialog-none {
  display: flex;
  padding: 0 50px;
  box-sizing: border-box;
  > div {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ccc;
    border-radius: 1px;
  }
}

.contanct-list-name {
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div > div {
    display: flex;
    align-items: center;
    > .el-icon {
      margin-right: 10px;
    }
  }
  .contanct-name {
    color: #409eff;
    font-size: 14px;
  }
}
.message {
  width: 100%;
  height: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  h3 {
    font-weight: 700;
    font-size: 16px;
    margin-top: 15px;
  }
  > div {
    flex: none;
    width: 100%;
    // border: 1px solid #ddd;
    // padding: 20px;
    // box-sizing: border-box;
  }
  .contanct-list {
    background: #f4f6f7;
    margin-top: 20px;
    padding: 15px;
  }
  .el-icon {
    cursor: pointer;
  }
}

.device-dialog {
  overflow: auto;
  > .el-scrollbar {
    overflow: auto;
  }
}
.select {
  .el-select-dropdown__item {
    // display: flex;
    min-height: 90px !important;
    padding-left: 5px;
    box-sizing: border-box;
    // background: red;
  }
}
.options-name {
  padding-right: 10px;
  float: left;
  margin-top: -6px;
}
.options-msg {
  float: left;
  // font-size: 13px;
  display: block;
  position: absolute;
  margin-top: 20px;
}

.association-select {
  height: 500px;
}
.summary {
  width: 270px;
}
:deep(.deviceForm) {
  .elstyle-dialog__body {
    padding: 20px 0;
    box-sizing: border-box;
  }
}
.deviceForm {
  :deep(.el-form-item) {
    width: 45% !important;
    .el-input {
      width: 202px;
    }
  }
}

.divider {
  width: 100%;
  height: 5px;
  background: #eee;
  margin-bottom: 22px;
  border-radius: 5px;
}
</style>
