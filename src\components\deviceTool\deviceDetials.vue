<template>
  <div class="message-dialog">
    <el-dialog :title="i18n.t('devicesAdd.Device Details')" v-model="dialogFormVisible" :before-close="cancel" width="50%">
      <div style="height: 450px" class="device-dialog">
        <el-scrollbar height="450px">
          <div class="message">
            <div>
              <ul>
                <li>
                  <span>{{ `${i18n.t("devicesList.Name")}` }}</span>
                  <span>{{ form.name || "--" }}</span>
                </li>
                <li>
                  <span>{{ `${i18n.t("devicesAdd.Description")}` }}</span>
                  <span>{{ form.description || "--" }}</span>
                </li>
                <li>
                  <span>{{ `${i18n.t("devicesAdd.IP address")}` }}</span>
                  <span>{{ form.config.ipAddress || "--" }}</span>
                  <el-icon @click="copyData(form.config.ipAddress)"><DocumentCopy /></el-icon>
                </li>
                <li>
                  <span>{{ `${i18n.t("devicesAdd.Importance")}` }}</span>
                  <span>{{ form.importance || "--" }}</span>
                </li>

                <li>
                  <span>{{ `${i18n.t("devicesAdd.Location")}` }}</span>
                  <span> {{ locationOption[form.locationId] || "--" }} </span>
                </li>
                <li>
                  <span>{{ regionsOption[form.regionId + regionsOption[form.regionId]] ? regionsOption[form.regionId + regionsOption[form.regionId]] : "区域" }}</span>

                  <span>{{ regionsOption[form.regionId] || "--" }}</span>
                </li>
                <li>
                  <span>{{ `${i18n.t("devicesAdd.Time Zone")}` }}</span>
                  <span>{{ timeZoneConvert[locationOption[form.locationId + locationOption[form.locationId]]] ? timeZoneConvert[locationOption[form.locationId + locationOption[form.locationId]]] : "--" }}</span>
                </li>
                <li>
                  <span>{{ `${i18n.t("devicesAdd.Serial numbers")}` }}</span>

                  <span>
                    <div style="display: flex; flex-wrap: wrap">
                      <span v-for="(item, index) in form.config.serialNumbers" v-show="form.config.serialNumbers.length > 0" :key="item.id"> {{ index != form.config.serialNumbers.length - 1 ? item + "," : item }}</span>
                    </div>
                    <span v-show="form.config.serialNumbers.length < 1"> -- </span>
                  </span>
                  <el-icon @click="copyData(form.config.serialNumbers)"><DocumentCopy /></el-icon>
                </li>
                <li>
                  <span>{{ `${i18n.t("devicesList.Device Model")}` }}</span>
                  <span>
                    <span v-for="(item, index) in form.config.modelNumbers" :key="item"> {{ index != form.config.modelNumbers.length - 1 ? item + "," : item }} </span>
                    <span v-show="form.config.modelNumbers.length < 1"> --</span>
                  </span>
                  <el-icon @click="copyData(form.config.modelNumbers)"><DocumentCopy /></el-icon>
                </li>
                <li>
                  <span>{{ `${i18n.t("devicesList.Service Number")}` }}</span>

                  <span>
                    <div v-for="(item, index) in form.serviceNumbers" v-show="form.serviceNumbers.length > 0" :key="item.id" style="display: flex; justify-content: space-between; width: 100%">
                      {{ item.number }}
                      <el-icon @click="copyData(item.number)"><DocumentCopy /></el-icon>
                    </div>

                    <span v-show="form.serviceNumbers.length < 1"> -- </span>
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { defineComponent } from "vue";
// import { QuestionFilled as ElIconQuestion } from "@element-plus/icons-vue";
import mixin from "./mixin";
import regionMixin from "./regionMixin";
import { getDeviceDetaile } from "@/views/pages/apis/deviceManage";
import { ElMessage } from "element-plus";
import { DocumentCopy } from "@element-plus/icons-vue";

import zone from "@/views/pages/common/zone.json";

import { Zone } from "@/utils/zone";
import { useClipboard } from "@vueuse/core";
import { useI18n } from "vue-i18n";
export default defineComponent({
  name: "EventCenterIntelNoiseReductCreate",
  components: {
    DocumentCopy,
  },
  mixins: [mixin, regionMixin],
  props: {},

  emits: ["confirm"],
  data() {
    return {
      i18n: useI18n(),
      timeZoneConvert: { ...Zone },
      dialogFormVisible: false,
      form: {
        modelIdent: null, //模型标识
        regionId: null, //所在区域ID
        locationId: null, //所在场所ID
        typeIds: [], //设备类型ID列表
        groupIds: [], //设备组ID列表
        vendorIds: [], //服务商ID列表
        alertClassificationIds: [], //告警分类ID列表
        externalId: null, //外部ID
        name: null, //设备名称
        description: null, //设备描述
        timeZone: null, //时区
        importance: null, //资源重要性
        tags: [], //标签
        active: true, //是否激活
        serviceNumbers: [],
        config: {
          ipAddress: "", //ip地址
          dynamicIp: "", //是否默认IP
          ackRequired: "", //确认告警

          nmsTicketing: "", //自动事件
          connectAuthType: "", //远程登录认证
          modelNumbers: [], //型号
          serialNumbers: [], //序列号
          assetNumbers: [], //资产编号
        },
      },
      id: "",
    };
  },
  watch: {
    id(val) {
      if (val) {
        // this.getDetail();
      }
    },
  },
  mounted() {
    this.handleRefreshRegionTable();

    this.getLocationList();
    this.getRegionList();
    this.getdeviceTypeList();
    this.getSupplierList();
    this.getAlarmList();
    this.getGroupDevice();
    // // console.log(this.allRegionSelect);
    // this.getAutoCloseEvent();
  },
  methods: {
    copyData(data) {
      const { copy, copied, isSupported } = useClipboard({ read: false, legacy: true });
      copy(data)
        .then(() => ElMessage.success(this.i18n.t("devicesList.Successfully")))
        .catch((error) => ElMessage.error(error instanceof Error ? error.message : "复制失败！"));
    },
    open(id) {
      //设备详情

      getDeviceDetaile({ id: id }).then((res) => {
        if (res.success) {
          let config = { ...res.data.config };
          // config.dynamicIp = JSON.parse(config.dynamicIp);
          // config.ackRequired = JSON.parse(config.ackRequired);
          // config.nmsTicketing = JSON.parse(config.nmsTicketing);
          config.modelNumbers = JSON.parse(config.modelNumbers);
          config.serialNumbers = JSON.parse(config.serialNumbers);
          config.assetNumbers = JSON.parse(config.assetNumbers);
          // this.form = { ...res.data };
          this.form.modelIdent = res.data.modelIdent; //模型标识
          this.form.locationId = res.data.locationId; //所在场所ID
          this.form.typeIds = res.data.typeIds; //设备类型ID列表
          this.form.groupIds = res.data.groupIds; //设备组ID列表
          this.form.vendorIds = res.data.vendorIds; //服务商ID列表
          this.form.alertClassificationIds = res.data.alertClassificationIds; //告警分类ID列表
          this.form.externalId = res.data.externalId; //外部ID
          this.form.name = res.data.name; //设备名称
          this.form.description = res.data.description; //设备描述
          this.form.timeZone = res.data.timeZone; //时区
          this.form.importance = res.data.importance; //资源重要性
          this.form.tags = res.data.tags; //标签
          this.form.active = res.data.active; //是否激活
          this.form.regionId = res.data.regionId; //所在区域ID
          this.form.config = config;
          this.form.serviceNumbers = res.data.serviceNumbers;

          zone.forEach((v) => {
            if (v.zoneId == this.locationOption[this.form.locationId + this.locationOption[this.form.locationId]]) {
              return (this.form.timeZone = v.displayName);
            }
          });
        }
      });
      this.$nextTick(() => {
        // setTimeout(() => {
        //   if (!this.$refs[`contactsRef`]) return false;
        //   this.$refs[`contactsRef`].getTabs();
        //   getRegionsContacts({ id: this.id }).then(({ success, data }) => {
        //     // console.log(success, data, this.$refs);
        //     if (success) {
        //       this.$refs[`contactsRef`]?.setContacts(data);
        //     }
        //   });
        // }, 1000);
      });
    },

    cancel() {
      // this.$refs["ruleForm"].resetFields();
      this.dialogFormVisible = false;
      // this.$emit("confirm", { id: this.id });
    },
  },
  expose: ["type", "dialogFormVisible", "id", "open"],
});
</script>

<style lang="scss" scoped>
.message-dialog {
  :deep(.el-dialog__body) {
    padding-top: 0 !important;
    // padding-left: 10px !important;
  }
}
.message {
  width: 100%;
  height: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  > div {
    flex: none;
    width: 100%;
    // border: 1px solid #ddd;
    // padding: 20px;
    // box-sizing: border-box;
  }
  ul > li {
    display: flex;
    // justify-content: space-between;
    min-height: 40px;
    align-items: center;
    padding: 10px 15px 0;
    box-sizing: border-box;
    > span:nth-child(even) {
      flex: 1;
      padding-left: 20px;
      box-sizing: border-box;
      display: flex;
      flex-wrap: wrap;
    }
    > span:first-child {
      display: flex;
      width: 80px;
    }
  }
  ul > li:nth-child(odd) {
    border-bottom: 1px solid #ddd;
    background: rgb(241, 241, 241);
  }
  .el-icon {
    cursor: pointer;
  }
}

.device-dialog {
  overflow: auto;
  > .el-scrollbar {
    overflow: auto;
  }
}
.select {
  .el-select-dropdown__item {
    // display: flex;
    min-height: 90px !important;
    padding-left: 5px;
    box-sizing: border-box;
    // background: red;
  }
}
.options-name {
  padding-right: 10px;
  float: left;
  margin-top: -6px;
}
.options-msg {
  float: left;
  // font-size: 13px;
  display: block;
  position: absolute;
  margin-top: 20px;
}

.association-select {
  height: 500px;
}
.summary {
  width: 270px;
}
:deep(.deviceForm) {
  .elstyle-dialog__body {
    padding: 20px 0;
    box-sizing: border-box;
  }
}
.deviceForm {
  :deep(.el-form-item) {
    width: 45% !important;
    .el-input {
      width: 202px;
    }
  }
}

.divider {
  width: 100%;
  height: 5px;
  background: #eee;
  margin-bottom: 22px;
  border-radius: 5px;
}
</style>
