<template>
  <div class="">
    <el-button link v-if="props.show != false" @click="oepnVnc(props.item)" :disabled="!props.active">
      <Icon class="tw-mx-[2px]" name="local-DeviceMac-line" v-preventReClick :color="props.active ? 'var(--el-color-primary)' : '#888'"></Icon>
    </el-button>
  </div>
  <!-- <deviceDetials ref="deviceDetialsRef"></deviceDetials>
  <deviceContacts ref="deviceContactsRef"></deviceContacts>
  <devicePing ref="devicePingRef"></devicePing>
  <deviceRoute ref="deviceRouteRef"></deviceRoute> -->
  <noVnc ref="noVncRef"></noVnc>
</template>
<script setup lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h } from "vue";
import { ElMessage, ElMessageBox, ElText } from "element-plus";

import { useI18n } from "vue-i18n";

// import deviceDetials from "./deviceDetials.vue";
// import deviceContacts from "./deviceContacts.vue";
// import devicePing from "./ping.vue";
// import deviceRoute from "./tracerRoute.vue";
import noVnc from "@/views/pages/alarm_convergence/PropertyManage/deviceManage/noVnc.vue";

import { LoginMode } from "@/views/pages/apis/device";

import validPwd from "@/views/pages/alarm_convergence/PropertyManage/passwordWallet/validPwd";
import { getMFAMethods, MFAMethod } from "@/api/system";

const i18n = useI18n({ useScope: "global" });
const props = defineProps({
  item: {
    type: Object,
  },
  list: {
    type: Number,
  },
  show: {
    type: Boolean,
  },
  active: {
    type: Boolean,
  },
});

// const deviceDetialsRef = ref<InstanceType<typeof deviceDetials>>();
// const deviceContactsRef = ref<InstanceType<typeof deviceContacts>>();
// const deviceRouteRef = ref<InstanceType<typeof deviceRoute>>();
// const devicePingRef = ref<InstanceType<typeof devicePing>>();
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}

// function oepnVnc(item) {
//   if (!props.active) return;
//   noVncRef.value.device = { ...item };
//   noVncRef.value.title = item.name ? "连接到 " + item.name : "连接到 " + item.deviceName;
//   noVncRef.value.dialogVisible = true;
//   // console.log(noVncRef.value.device)
// }

const noVncRef = ref<InstanceType<typeof noVnc>>();

async function oepnVnc(row) {
  try {
    if (row.config.connectAuthType === LoginMode.ON_CONNECT) {
      const { success: validSuccess } = (await validPwd(false)) as any;
      if (!validSuccess) return;
    } else if (row.config.connectAuthType === LoginMode.REQUIRE_TFA) {
      const { data, message, success } = await getMFAMethods({});
      if (!success) throw new Error(message);
      const isMfa = data.includes(MFAMethod.TOTP);
      if (!isMfa) throw new Error("请开启双因素认证");
      const { success: validSuccess } = (await validPwd(isMfa)) as any;
      if (!validSuccess) return;
    }
    noVncRef.value.device = { ...row };
    noVncRef.value.title = `${i18n.t("devicesList.connection to")} ` + row.name;
    noVncRef.value.dialogVisible = true;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
  // console.log(noVncRef.value.device)
}

beforeCreate();
nextTick(created);
// onMounted(mounted, ctx);
// onUpdated(updated, ctx);
// onUnmounted(destroyed, ctx);
// onBeforeMount(beforeMount, ctx);
// onBeforeUpdate(beforeUpdate, ctx);
// onBeforeUnmount(beforeDestroy, ctx);
// // onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, '🚀[onErrorCaptured]'), ctx);
// // onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, '🚀[onRenderTracked]'), ctx);
// // onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, '🚀[onRenderTriggered]'), ctx);
// onActivated(activated, ctx);
// onDeactivated(deactivated, ctx);
</script>
<style lang="scss" scoped></style>
