<template>
  <div class="ping-dialog">
    <el-dialog :title="title" v-model="dialogFormVisible" :before-close="cancel" width="40%" center>
      <div class="device-dialog">
        <el-scrollbar>
          <div>
            <el-progress :percentage="percentage" :format="format" />
          </div>
          <div class="ping-status">
            <div v-for="(item, i) in 4" :key="item">
              <span :class="pingTipArray[i]">
                {{ pingNumber[i] ? pingNumber[i] + "ms" : "" }}
              </span>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">{{ `${$t("glob.Close")}` }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
const format = (percentage) => (percentage === 100 ? "" : ``);
import { defineComponent } from "vue";
import { QuestionFilled as ElIconQuestion } from "@element-plus/icons-vue";
import mixin from "./mixin";
import regionMixin from "./regionMixin";

import { pingTo } from "@/views/pages/apis/deviceManage";
export default defineComponent({
  name: "EventCenterIntelNoiseReductCreate",
  components: {
    // inputTag,
    ElIconQuestion,
  },
  mixins: [mixin, regionMixin],
  props: {
    id: {
      type: String,
      default: "",
    },
  },
  emits: ["confirm"],
  data() {
    return {
      dialogFormVisible: false,
      name: "",
      ipAddress: "",
      title: "",
      percentage: 0,
      timer: null,
      format: format,
      pingData: [],
      className: "",
      pingTipArray: [],
      pingNumber: [],
    };
  },
  watch: {
    id(val) {
      if (val) {
        // this.getDetail();
      }
    },
  },
  mounted() {
    // // console.log(this.allRegionSelect);
    // this.getAutoCloseEvent();
  },
  methods: {
    async open(row) {
      // console.log(row, 666666);
      this.pingTipArray = [];
      this.pingData = [];
      this.pingNumber = [];
      this.dialogFormVisible = true;

      this.name = row.name;
      this.ipAddress = row.config?.ipAddress != undefined && row.config?.ipAddress !== "" ? `[${row.config?.ipAddress}]` : "";
      this.title = `Ping ${this.name || row.neName} ${this.ipAddress || row.address}`;
      await pingTo({ id: row.id || row.resourceId })
        .then((res) => {
          if (res.success) {
            // if (res.data.length > 0) {
            this.pingData = res.data;
            // }
          }
        })
        .catch((err) => {
          this.$message.error(err?.message.split(":")[1]);
        });
      let index = 0;
      do {
        await new Promise((resolve) => {
          setTimeout(() => {
            this.pingTipArray.push(this.pingData[index] != null ? "success" : "error");
            this.percentage = this.percentage + 25;
            this.pingNumber.push(this.pingData.length ? this.pingData[index] : "");
            index++;

            resolve();
          }, 1000);
        });
      } while (index <= 3);

      // this.timer = setInterval(() => {
      //   if (this.percentage >= 100) {
      //     // Stop the timer when we reach the end of the array
      //     this.stopTimer();
      //   } else {
      //     // Increment the progress value

      //   }
      // }, 1000);
      // getDeviceDetaile({ id: this.id }).then((res) => {});
    },
    stopTimer() {
      clearInterval(this.timer);
      // this.timerRunning = false;
    },

    cancel() {
      this.percentage = 0;
      this.dialogFormVisible = false;
      this.$emit("confirm", { id: this.id });
    },
  },
  expose: ["type", "dialogFormVisible", "open"],
});
</script>

<style lang="scss" scoped>
.ping-dialog {
  :deep(.el-progress) {
    .el-progress__text {
      display: none;
    }
  }
}
.ping-status {
  width: 100%;
  height: auto;
  display: flex;
  flex-wrap: nowrap;
  margin-top: 20px;
  > div {
    width: 100%;
    height: 30px;
    padding: 0 15px;
    > span {
      width: 100%;
      height: 100%;
      display: flex;
      background: #999999;
      border-radius: 15px;
      align-items: center;
      justify-content: center;
    }
    .success {
      // animation: successchangecolor 1s infinite;
      background: #83b56f;
      color: #fff;
    }
    .error {
      background: #bb6258;
    }
  }
}
.device-dialog {
  overflow: auto;
  > .el-scrollbar {
    overflow: auto;
  }
}
</style>
