// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { ElMessage, ElMessageBox } from "element-plus";
import { getRegionsTenantCurrent, delRegionsById } from "@/views/pages/apis/regionManage";
import { ElMessage, ElMessageBox } from "element-plus";
import getUserInfo from "@/utils/getUserInfo";

export default {
  inject: ["refresh"],
  data() {
    return {
      paging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      allRegion: [],
      allRegionByPage: [],
      allRegionSelect: [],
      userInfo: getUserInfo(),
    };
  },
  methods: {
    handleRefreshRegionTable() {
      getRegionsTenantCurrent({ sort: "createdTime,desc", containerId: this.userInfo.currentTenant.containerId, verifyPermissionIds: "612913010935070720,777393476089151488" }).then(({ success, data }) => {
        if (success) {
          this.tableData = this.setTableData(data.sort((x, y) => y.createdTime - x.createdTime));
          this.allRegionSelect = JSON.parse(JSON.stringify(this.tableData));
          this.paging.total = this.tableData.length;
          this.tableData = this.setTableDataByPage(this.tableData);
          if (!this.tableData.length && this.paging.pageNumber !== 1) {
            this.paging.pageNumber = 1;
            this.handleRefreshRegionTable();
          }
        } else console.error(JSON.parse(data)?.message || "列表获取失败");
      });
    },
    setTableDataByPage(data) {
      const result = [];
      for (let i = 0, len = data.length; i < len; i += this.paging.pageSize) {
        result.push(data.slice(i, i + this.paging.pageSize));
      }
      this.allRegionByPage = result;
      return result[this.paging.pageNumber - 1] || [];
    },
    setTableData(data) {
      this.allRegion = JSON.parse(JSON.stringify(data));
      let _formatter = (list) => {
        for (let i = 0; i < list.length; i++) {
          list[i].children = [];
          list[i].isEdit = false;
          const _filter = this.allRegion.filter((v) => {
            return list[i].id === v.parentId;
          });
          if (_filter && _filter?.length) {
            list[i].children = _filter;
            _formatter(list[i].children);
          }
        }
      };
      const result = data.filter((v) => !v.parentId);
      _formatter(result);
      _formatter = null;
      // console.log("result", result);
      return result;
    },
    handleDelRegion(row) {
      ElMessageBox.confirm(`确定删除区域"${row.name}"?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const { success, message } = await delRegionsById({ id: row.id });
            if (!success) throw new Error(message);
            ElMessage.success("操作成功");
            this.refresh ? this.refresh() : this.handleRefreshRegionTable();
          } catch (error) {
            error instanceof Error && ElMessage.error(error.message);
          }
        })
        .catch(() => {
          /* code */
        });
    },
    handleAddChild(row) {
      const editData = row.children.find(({ id }) => !id);
      const newRegion = Object.assign(
        {
          parentId: row.id,
          isEdit: true,
          label: "",
          name: "",
          description: "",
          externalId: "",
        },
        editData || {}
      );
      // row.children = row.children.filter((v) => v.id)
      if (row.isExpend) {
        row.children.unshift(newRegion);
      } else {
        this.$refs[`${row.parentId}-table`]?.toggleRowExpansion(row);
        row.children.unshift(newRegion);
      }
    },
    formatterTable(_row, _col, v) {
      switch (_col.property) {
        default:
          return v || "";
      }
    },
  },
};
