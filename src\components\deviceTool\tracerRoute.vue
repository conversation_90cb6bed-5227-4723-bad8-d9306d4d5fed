<template>
  <div class="ping-dialog">
    <el-dialog :title="title" v-model="dialogFormVisible" :before-close="cancel" width="40%" center>
      <div style="height: 200px" class="device-dialog" v-if="pingNumber.length > 0" key="data">
        <el-scrollbar height="200px">
          <div>
            <el-progress :percentage="percentage" :format="format" :status="!pingNumber.length && percentage === 100 ? `exception` : undefined" />
          </div>
          <div class="trace-route">
            <div v-for="(item, i) in pingNumber" :key="`trace-route-${i}`">
              <b>{{ i + 1 }}</b>
              <div>
                <span :class="item.status">
                  {{ item ? item.delay : "" }}
                </span>
                {{ item ? item.routeIp : "" }}
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div style="height: 50px" class="device-dialog-none" v-else>
        <div>
          <el-progress :percentage="percentage" :format="format" :status="!pingNumber.length && percentage === 100 ? `exception` : undefined" />
        </div>
        <div class="trace-route-none">{{ `${i18n.t("glob.No Data")}` }}</div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">{{ `${i18n.t("glob.Close")}` }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
const format = (percentage) => (percentage === 100 ? "" : ``);
import { defineComponent } from "vue";
import { QuestionFilled as ElIconQuestion } from "@element-plus/icons-vue";
import mixin from "./mixin";
import regionMixin from "./regionMixin";

import { routeSend, getDeviceRoute } from "@/views/pages/apis/deviceManage";
import { useI18n } from "vue-i18n";
export default defineComponent({
  name: "EventCenterIntelNoiseReductCreate",
  components: {
    // inputTag,
    ElIconQuestion,
  },
  mixins: [mixin, regionMixin],
  props: {
    id: {
      type: String,
      default: "",
    },
  },
  emits: ["confirm"],
  data() {
    return {
      i18n: useI18n(),
      dialogFormVisible: false,
      name: "",
      ipAddress: "",
      title: "",
      percentage: 0,
      timer: null,
      format: format,
      traceId: "",
      className: "",
      traceRouteList: [],
      pingNumber: [],
    };
  },
  watch: {
    id(val) {
      if (val) {
        this.getDetail();
      }
    },
  },
  mounted() {
    // // console.log(this.allRegionSelect);
    // this.getAutoCloseEvent();
  },
  methods: {
    /**
     * @desc 获取追踪数据
     * @param id 追踪id
     * @param deviceId 设备id
     * @param num 请求次数
     */
    async getRracerRoute(id, deviceId, num) {
      try {
        const { data: sourceData, message, success } = await getDeviceRoute({ id, deviceId });
        if (!success) throw new Error(message);
        num++; /* 请求次数增加,10次没有数据就结束 */
        if (num === 10 && !sourceData.length) {
          this.percentage = 100;
          throw new Error(this.i18n.t("axios.Request failed"));
        }
        /* OK NaN END LOOP */
        const successStatus = ["OK", "NaN"];
        const _continue = !sourceData.map((v) => successStatus.includes(JSON.parse(v).data)).some((v) => !v);
        /* 判断是否继续请求数据 */
        if (_continue) {
          const _timeout = setTimeout(() => {
            this.getRracerRoute(id, deviceId, num);
            clearTimeout(_timeout);
          }, 1000);
        } else {
          const data = [];
          for (let i = 0; i < sourceData.length; i++) {
            const item = JSON.parse(sourceData[i]);
            if (successStatus.includes(item.data)) {
              data.push({
                /*  */
                status: { OK: "warning", NaN: "error" }[item.data],
                delay: item.delay,
                routeIp: item.data === "NaN" ? "未响应" : item.routeIp,
              });
            } else if (item.data === "End") {
              data[i - 1].status = "success";
            }
          }
          let idx = 0;
          this.timer = setInterval(() => {
            this.percentage += 100 / data.length;
            this.pingNumber.push(data[idx]);
            this.$nextTick(() => (this.pingNumber = JSON.parse(JSON.stringify(this.pingNumber))));
            idx++;
            if (idx === data.length) {
              clearInterval(this.timer);
              this.timer = null;
            }
          }, 1000);
        }
      } catch (error) {
        error instanceof Error && this.$message.error(error.message);
      }
    },

    async open(row) {
      this.pingTipArray = [];
      this.pingData = [];
      this.pingNumber = [];
      this.dialogFormVisible = true;

      this.name = row.name;
      this.ipAddress = row.config?.ipAddress != undefined && row.config?.ipAddress !== "" ? `[${row.config?.ipAddress}]` : "";
      this.title = `${this.i18n.t("devicesList.Traceroute")} ${this.name || row.neName} ${this.ipAddress || row.address}`;
      try {
        const { data, message, success } = await routeSend({ id: row.id || row.resourceId });
        if (!success) throw new Error(message);
        this.getRracerRoute(data, row.id || row.resourceId, 0);
      } catch (error) {
        error instanceof Error && this.$message.error(error.message);
      }
    },
    stopTimer() {
      clearInterval(this.timer);
      // this.timerRunning = false;
    },

    cancel() {
      this.percentage = 0;
      this.dialogFormVisible = false;
      this.$emit("confirm", { id: this.id });
    },
  },
  expose: ["type", "dialogFormVisible", "open"],
});
</script>

<style lang="scss" scoped>
.ping-dialog {
  :deep(.el-progress) {
    .el-progress__text {
      display: none;
    }
  }
}
.trace-route-none {
  width: 100%;
  text-align: center;
  line-height: 50px;
}
.trace-route {
  display: flex;
  flex-wrap: wrap;
  > div {
    width: 100%;
    display: flex;
    margin-top: 10px;
    align-items: center;
    > b {
      color: #fff;
      background: #999999;
      display: block;
      padding: 1px 10px;
      box-sizing: border-box;
      border-radius: 13px;
    }
    > div {
      display: flex;
      align-items: center;
      > span {
        display: block;
        margin: 0 10px;
      }
      .warning {
        color: #fff;
        background: #deb26c;
        padding: 3px 10px;
        box-sizing: border-box;
        border-radius: 15px;
      }
      .error {
        color: #fff;

        background: #bb6258;
        padding: 3px 10px;
        box-sizing: border-box;
        border-radius: 15px;
      }
      .success {
        color: #fff;

        background: #83b56f;
        padding: 3px 10px;
        box-sizing: border-box;
        border-radius: 15px;
      }
    }
  }
}

.ping-status {
  width: 100%;
  height: auto;
  display: flex;
  flex-wrap: nowrap;
  margin-top: 20px;
  > div {
    width: 100%;
    height: 30px;
    padding: 0 15px;
    > span {
      width: 100%;
      height: 100%;
      display: flex;
      background: #999999;
      border-radius: 15px;
      align-items: center;
      justify-content: center;
    }
    .success {
      // animation: successchangecolor 1s infinite;
      background: #83b56f;
      color: #fff;
    }
    .error {
      background: #bb6258;
    }
  }
}
.device-dialog {
  overflow: auto;
  > .el-scrollbar {
    overflow: auto;
  }
}
</style>
