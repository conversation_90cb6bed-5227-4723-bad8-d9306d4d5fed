<template>
  <!-- 对话框表单 -->
  <el-dialog v-model="visible" :close-on-click-modal="false" append-to-body draggable :width="`${width}px`" :before-close="props.handleCancel">
    <template #header>
      <div class="title">
        <slot name="header" :width="width" :height="height"></slot>
      </div>
    </template>
    <template #default>
      <!-- :height="height || undefined" :style="{ maxHeight: `${$height / 1.5}px` }" -->
      <el-scrollbar ref="contentRef">
        <slot name="default" :width="width" :height="height"></slot>
      </el-scrollbar>
    </template>
    <template #footer>
      <div>
        <slot name="footer" :width="width" :height="height"></slot>
      </div>
      <div class="zoom-handle" @mousedown.self="handleZoom">
        <svg style="display: block; width: 60%; height: 60%; transform: translate(-25%, -25%); fill: currentColor; pointer-events: none" viewBox="0 0 1024 1024">
          <path d="M319.20128 974.56128L348.16 1003.52l655.36-655.36-28.95872-28.95872-655.36 655.36zM675.84 1003.52l327.68-327.68-28.95872-28.95872-327.68 327.68L675.84 1003.52z" fill="#000000"></path>
        </svg>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="EditorForm">
import { ElScrollbar } from "element-plus";
import { useModel, ref, inject, watch, nextTick } from "vue";

interface Props {
  visible: boolean;
  handleCancel: (done?: () => void) => unknown;
}
const props = withDefaults(defineProps<Props>(), { visible: false, handleCancel: (done?: () => void) => done && done() });

const visible = useModel(props, "visible");

const contentRef = ref<InstanceType<typeof ElScrollbar>>();

const $width = inject<import("vue").Ref<number>>("width", ref(document.body.clientWidth - 200));
const $height = inject<import("vue").Ref<number>>("height", ref(document.body.clientHeight - 260 - document.body.clientHeight * 0.15));
const width = ref($width.value / 1.75);
const height = ref(0);

watch(visible, async (visible) => {
  await nextTick();
  height.value = 0;
  const w = $width.value / 1.75;
  if (visible) {
    await nextTick();
    const h = contentRef.value ? contentRef.value.$el.clientHeight : $height.value / 2.5;
    if (h) height.value = contentRef.value ? contentRef.value.$el.clientHeight : $height.value / 2.5;
    width.value = w;
    height.value = h < document.body.clientHeight - 260 - document.body.clientHeight * 0.15 ? h : document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  }
});

function handleZoom($event: MouseEvent) {
  const w = width.value;
  const h = height.value;
  ($event.target as HTMLElement).ownerDocument.onmousemove = (e: MouseEvent) => {
    e.preventDefault();
    if (w + (e.clientX - $event.clientX) * 2 < document.body.clientWidth - 200) width.value = w + (e.clientX - $event.clientX) * 2 > 360 ? w + (e.clientX - $event.clientX) * 2 : 360;
    else width.value = document.body.clientWidth - 200;
    if (h + (e.clientY - $event.clientY) * 1 < document.body.clientHeight - 260 - document.body.clientHeight * 0.15) height.value = h + (e.clientY - $event.clientY) * 1 > 24 ? h + (e.clientY - $event.clientY) * 1 : 24;
    else height.value = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  };
  ($event.target as HTMLElement).ownerDocument.onmouseup = (e: MouseEvent) => {
    (e.target as HTMLElement).ownerDocument.onmousemove = null;
    (e.target as HTMLElement).ownerDocument.onmouseup = null;
  };
}

interface Slots {
  header(size: { width: number; height: number }): any;
  default(size: { width: number; height: number }): any;
  footer(size: { width: number; height: number }): any;
}
defineSlots<Slots>();
</script>

<style scoped lang="scss"></style>
