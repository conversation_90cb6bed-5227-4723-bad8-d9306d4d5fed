<template>
  <!-- 对话框表单 -->
  <el-drawer class="el-card tw-bottom-[20px] tw-right-[20px] tw-top-[124px]" :size="width" :style="{ height: `${height}px` }" v-model="visible" :close-on-click-modal="false" :show-close="false" :modal="false" :before-close="props.handleCancel">
    <template #header>
      <div class="tw-flex tw-h-[30px] tw-flex-nowrap tw-items-center">
        <el-page-header class="tw-mr-auto" @back="props.handleCancel()">
          <template #content>
            <slot name="header" :width="width" :height="height"></slot>
          </template>
        </el-page-header>
      </div>
    </template>
    <template #default>
      <el-scrollbar ref="contentRef" :height="height - 110 - 74" view-class="clearfix">
        <slot name="default" :width="width" :height="height"></slot>
      </el-scrollbar>
    </template>
    <template #footer>
      <slot name="footer" :width="width" :height="height"></slot>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="EditorForm">
import { useModel, ref, inject } from "vue";

interface Props {
  visible: boolean;
  handleCancel: (done?: () => void) => unknown;
}
const props = withDefaults(defineProps<Props>(), { visible: false, handleCancel: (done?: () => void) => done && done() });

const visible = useModel(props, "visible");

const width = inject<import("vue").Ref<number>>("width", ref(100));
const height = inject<import("vue").Ref<number>>("height", ref(100));

interface Slots {
  header(size: { width: number; height: number }): any;
  default(size: { width: number; height: number }): any;
  footer(size: { width: number; height: number }): any;
}
defineSlots<Slots>();
</script>

<style scoped lang="scss"></style>
