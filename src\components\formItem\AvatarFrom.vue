<template>
  <div class="flex-row avatar" :style="{ width: '100%' }">
    <div class="flex-row avatar-view">
      <el-avatar v-for="(src, index) in srcList" :key="`avatar_${index}`" :size="props.size" fit="fill" :src="src.src" @click.stop="setUserAvatar(src.url)"></el-avatar>
    </div>
  </div>
</template>

<script setup lang="ts" name="AvatarCropper">
import { nextTick, onMounted, onBeforeUnmount, ref, reactive } from "vue";
// import { useFileDialog } from "@vueuse/core";

// import defaultAvatar from "@/assets/avatar.png";
import { getAvatarUrlList, getAvatar, setAvatar } from "@/api/system";
import { ElMessage } from "element-plus";
import { useSuperInfo } from "@/stores/infoBySuper";

const superInfo = useSuperInfo();

interface Props {
  size?: number;
  modelValue: any;
}
const props = withDefaults(defineProps<Props>(), { size: 32 });

const srcList = ref<{ url: string; src: string }[]>([]);
const filesURL = reactive<{ src: string; file: File }[]>([]);
// const { files, open, reset } = useFileDialog({ multiple: false, accept: "image/gif, image/jpg, image/jpeg, image/bmp, image/png, image/webp" });
// watch(files, (files) => {
//   removeObjectURL();
//   if (files instanceof FileList) {
//     for (const key in files) if (Object.prototype.hasOwnProperty.call(files, key)) filesURL.push({ src: URL.createObjectURL(files[key]), file: files[key] });
//   }
// });
onBeforeUnmount(removeObjectURL);
function removeObjectURL() {
  while (filesURL.length) {
    URL.revokeObjectURL((filesURL.shift() as { src: string; file: File }).src);
  }
}

async function setUserAvatar(filename: string) {
  await nextTick();
  try {
    const { success, message, data } = await setAvatar({ filename });
    if (success) {
      ElMessage.success("头像修改成功！");
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    superInfo.updateInfo();
  }
}

onMounted(async () => {
  try {
    for (let index = 0; index < srcList.value.length; index++) URL.revokeObjectURL(srcList.value[index].url);
    const { success, message, data } = await getAvatarUrlList({});
    if (success) {
      srcList.value = await Promise.all((data instanceof Array ? data : []).map(async (v) => ({ url: v, src: await getAvatar({ filePath: v }) })));
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
});
</script>

<style scoped lang="scss">
.avatar {
  .avatar-view {
    width: 100%;
    flex-wrap: wrap;
    margin: 0;
    > :deep(.el-avatar) {
      cursor: pointer;
    }
  }
}
</style>
