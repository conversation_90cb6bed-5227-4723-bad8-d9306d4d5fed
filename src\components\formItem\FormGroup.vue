<template>
  <el-col :span="props.span" class="tw-mb-[18px]">
    <div class="tw-mx-0 tw-my-[1px] tw-rounded-md tw-px-[18px] tw-pb-[6px] tw-pt-0 tw-outline tw-outline-1 tw-outline-[var(--el-border-color)]">
      <el-divider content-position="left" class="tw-my-[18px] tw-translate-y-[-1px]">
        <span v-if="props.label">{{ props.label }}</span>
        <el-tooltip v-if="props.tooltip" :content="props.tooltip" placement="right-end">
          <el-icon :size="16" style="vertical-align: middle; margin-left: 0.5em"><QuestionFilled /></el-icon>
        </el-tooltip>
      </el-divider>
      <el-row :gutter="props.gutter" class="tw-pt-[9px]">
        <slot name="default" :label="props.label"></slot>
      </el-row>
    </div>
  </el-col>
</template>

<script setup lang="ts">
import { QuestionFilled } from "@element-plus/icons-vue";
import { type FormGroupProps, FormGroupDefaultProps } from "@/components/formItem/interface/index";

defineOptions({ name: "FormGroup" });

const props = withDefaults(defineProps<Partial<Omit<FormGroupProps, "span">> & Required<Pick<FormGroupProps, "span">>>(), FormGroupDefaultProps);

defineSlots<{
  default(props: { label: string }): any;
}>();

defineExpose({});
</script>

<style scoped lang="scss"></style>
