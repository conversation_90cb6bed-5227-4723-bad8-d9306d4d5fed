<template>
  <el-col :span="props.span">
    <ElFormItem :label-width="props.labelWidth" :prop="props.prop" :required="props.required" :rules="props.rules" :error="props.error" :validate-status="props.validateStatus" :for="props.for" :inline-message="props.inlineMessage" :show-message="props.showMessage" :size="props.size">
      <template v-if="props.label" #label>
        <span>
          <slot name="label">{{ props.label }}</slot>
          <el-tooltip v-if="props.tooltip">
            <template #content>
              {{ props.tooltip.split(";")[0] }}
              <div class="size">
                <p v-for="item in props.tooltip.split(';')[1].split(',')" :key="item">{{ item }}</p>
              </div>
            </template>
            <el-icon :size="16" style="vertical-align: middle; margin-left: 0.5em"><QuestionFilled /></el-icon>
          </el-tooltip>
        </span>
      </template>
      <template #default>
        <slot name="default" :formItem="formItem"></slot>
      </template>
    </ElFormItem>
  </el-col>
</template>

<script setup lang="ts">
import { ElFormItem } from "element-plus";
import { QuestionFilled } from "@element-plus/icons-vue";
import { type FormItemProps, formItemDefaultProps } from "@/components/formItem/interface/index";
import { ref } from "vue";

defineOptions({ name: "FormItem" });

const props = withDefaults(defineProps<Partial<Omit<FormItemProps, "span">> & Required<Pick<FormItemProps, "span">>>(), formItemDefaultProps);
const emits = defineEmits<{
  init: [prop: string | string[]];
}>();
emits("init", props.prop);

const formItem = ref<InstanceType<typeof ElFormItem>>();
defineSlots<{
  default(props: { formItem?: InstanceType<typeof ElFormItem> }): any;
  label(props: { formItem?: InstanceType<typeof HTMLElement> }): any;
}>();

defineExpose({});
</script>

<style lang="scss" scoped>
.size {
  // width: 300px;
  // display: flex;
  // flex-wrap: wrap;
  // > span {
  //   margin-right: 10px;
  //   margin-bottom: 10px;
  //   background: #fff;
  //   padding: 5px;
  //   box-sizing: border-box;
  //   color: #000;
  // }
}
</style>
