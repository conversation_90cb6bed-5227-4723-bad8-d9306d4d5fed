<template>
  <el-form v-loading="props.loading" ref="formRef" :size="props.size" :disabled="props.disabled" :model="props.model" :rules="props.rules" :label-position="props.labelPosition" :require-asterisk-position="props.requireAsteriskPosition" :label-width="props.labelWidth" :label-suffix="props.labelSuffix" :inline="props.inline" :inline-message="props.inlineMessage" :status-icon="props.statusIcon" :show-message="props.showMessage" :validate-on-rule-change="props.validateOnRuleChange" :hideRequired-asterisk="props.hideRequiredAsterisk" :scroll-to-error="props.scrollToError" :scroll-into-viewOptions="props.scrollIntoViewOptions" class="tw-overflow-hidden tw-p-[12px]" @submit.prevent @keyup.ctrl.enter="emits('submit', props.model)" @validate="(prop, isValid, message) => emits('validate', prop, isValid, message)">
    <el-row :gutter="props.gutter">
      <slot name="default"></slot>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { type FormModelProps, formModelDefaultProps } from "@/components/formItem/interface/index";
import { ElForm } from "element-plus";
import { ref } from "vue";

defineOptions({ name: "FormGroup" });
const emits = defineEmits<{
  submit: [form: any];
  validate: [prop: string | string[], isValid: boolean, message: string];
}>();

const props = withDefaults(defineProps<Partial<Omit<FormModelProps, "model">> & Required<Pick<FormModelProps, "model">>>(), formModelDefaultProps);

const formRef = ref<InstanceType<typeof ElForm>>();
defineSlots<{
  default(props: object): any;
}>();

defineExpose({
  validate(...args: Parameters<InstanceType<typeof ElForm>["validate"]>): ReturnType<InstanceType<typeof ElForm>["validate"]> {
    return formRef.value!.validate(...args);
  },
  validateField(...args: Parameters<InstanceType<typeof ElForm>["validateField"]>): ReturnType<InstanceType<typeof ElForm>["validateField"]> {
    return formRef.value!.validateField(...args);
  },
  resetFields(...args: Parameters<InstanceType<typeof ElForm>["resetFields"]>): ReturnType<InstanceType<typeof ElForm>["resetFields"]> {
    return formRef.value!.resetFields(...args);
  },
  scrollToField(...args: Parameters<InstanceType<typeof ElForm>["scrollToField"]>): ReturnType<InstanceType<typeof ElForm>["scrollToField"]> {
    return formRef.value!.scrollToField(...args);
  },
  clearValidate(...args: Parameters<InstanceType<typeof ElForm>["clearValidate"]>): ReturnType<InstanceType<typeof ElForm>["clearValidate"]> {
    return formRef.value!.clearValidate(...args);
  },
});
</script>

<style scoped></style>
