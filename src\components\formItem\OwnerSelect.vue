<template>
  <!-- <template v-if="!value.id"> -->
  <el-col :span="props.width > 580 ? 12 : 24">
    <el-form-item :label="`${props.label}名称`" :prop="`${props.prefix ? `${props.prefix}.` : ''}name`" :rules="[]">
      <el-input :model-value="value.name" @update:model-value="($event) => (value = { ...value, name: $event })" :placeholder="$t('glob.Please input field', { field: `${props.label}名称` })" type="text" :disabled="props.disabled"></el-input>
    </el-form-item>
  </el-col>
  <!-- <el-col :span="props.width > 580 ? 12 : 24">
    <el-form-item :label="`${props.label}昵称`" :prop="`${props.prefix ? `${props.prefix}.` : ''}nickname`" :rules="[]">
      <el-input :model-value="value.nickname" @update:model-value="($event) => (value = { ...value, nickname: $event })" :placeholder="$t('glob.Please input field', { field: `${props.label}昵称` })" type="text" :disabled="props.disabled"></el-input>
    </el-form-item>
  </el-col> -->
  <el-col :span="props.width > 580 ? 12 : 24">
    <el-form-item :label="`${props.label}账号`" :prop="`${props.prefix ? `${props.prefix}.` : ''}account`" :rules="[...(value.phone || value.email ? [] : [buildValidatorData({ name: 'required', title: '账号' })]), buildValidatorData({ name: 'account', title: '账号' })]">
      <el-input :model-value="value.account" @update:model-value="($event) => (value = { ...value, account: $event })" :placeholder="$t('glob.Please input field', { field: `${props.label}账号` })" type="text" :disabled="props.disabled || value.keyword === 'account'"></el-input>
    </el-form-item>
  </el-col>
  <el-col :span="props.width > 580 ? 12 : 24">
    <el-form-item :label="`${props.label}手机号`" :prop="`${props.prefix ? `${props.prefix}.` : ''}phone`" :rules="[...(value.account || value.email ? [] : [buildValidatorData({ name: 'required', title: '手机号' })]), buildValidatorData({ name: 'mobile', title: '手机号' })]">
      <el-input :model-value="value.phone" @update:model-value="($event) => (value = { ...value, phone: $event })" :placeholder="$t('glob.Please input field', { field: `${props.label}手机号` })" type="text" :disabled="props.disabled || value.keyword === 'phone'"></el-input>
    </el-form-item>
  </el-col>
  <el-col :span="props.width > 580 ? 12 : 24">
    <el-form-item :label="`${props.label}邮箱`" :prop="`${props.prefix ? `${props.prefix}.` : ''}email`" :rules="[...(value.account || value.phone ? [] : [buildValidatorData({ name: 'required', title: '邮箱' })]), buildValidatorData({ name: 'email', title: '邮箱' })]">
      <el-input :model-value="value.email" @update:model-value="($event) => (value = { ...value, email: $event })" :placeholder="$t('glob.Please input field', { field: `${props.label}邮箱` })" type="text" :disabled="props.disabled || value.keyword === 'email'"></el-input>
    </el-form-item>
  </el-col>
  <el-col :span="props.width > 580 ? 12 : 24">
    <el-form-item :label="`${props.label}语言`" :prop="`${props.prefix ? `${props.prefix}.` : ''}language`" :rules="[]">
      <el-select :model-value="value.language" :placeholder="$t('glob.Please select field', { field: `${props.label}语言` })" filterable @update:model-value="($event) => (value = { ...value, language: $event as keyof typeof language })" :disabled="props.disabled">
        <el-option v-for="item in languageOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </el-form-item>
  </el-col>
  <el-col :span="props.width > 580 ? 12 : 24">
    <el-form-item :label="`${props.label}性别`" :prop="`${props.prefix ? `${props.prefix}.` : ''}gender`" :rules="[]">
      <el-select :model-value="value.gender" :placeholder="$t('glob.Please select field', { field: `${props.label}性别` })" filterable @update:model-value="($event) => (value = { ...value, gender: $event as keyof typeof gender })" :disabled="props.disabled">
        <el-option v-for="item in genderOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </el-form-item>
  </el-col>
  <el-col :span="props.width > 580 ? 12 : 24">
    <el-form-item :label="`${props.label}密码`" :prop="`${props.prefix ? `${props.prefix}.` : ''}password`" :rules="[...(value.account ? [buildValidatorData({ name: 'required', title: '密码' })] : []), buildValidatorData({ name: 'password', title: '密码' })]">
      <el-input :model-value="value.password" @update:model-value="($event) => (value = { ...value, password: $event })" :placeholder="$t('glob.Please input field', { field: `${props.label}密码` })" type="password" show-password :disabled="props.disabled"></el-input>
    </el-form-item>
  </el-col>
  <!-- </template> -->
</template>

<script setup lang="ts">
import { useModel, watch, onMounted, getCurrentInstance } from "vue";
import { type UserBaseItem, gender, genderOption, language, languageOption } from "@/api/iam";
import { buildValidatorData } from "@/utils/validate";

const ctx = getCurrentInstance()!;

interface Props {
  modelValue?: UserBaseItem;
  prefix: string;
  label: string;
  required?: boolean;
  disabled?: boolean;
  platform: string;
  width: number;
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({ platform: "", keyword: "", id: "", name: "" /* 姓名 */, account: "" /* 账号 */, phone: "" /* 手机号码 */, email: "" /* 邮箱 */, language: "none" /* 语言 */, gender: "SECRET" /* 性别 */, password: "" /* 密码 */ }),
  required: false,
  disabled: false,
});

// interface Emits {
//   ($event: "update:modelValue", value: UserBaseItem): void;
// }
// const emits = defineEmits<Emits>();

let unWatch: (() => void) | undefined;

async function listenKey() {
  if (typeof unWatch === "function") {
    unWatch();
    unWatch = undefined;
  }
  if (props.platform) {
    /*  */
  }

  if (!ctx.isUnmounted) {
    unWatch = watch(() => [props.modelValue, props.platform], listenKey);
  }
}

const value = useModel(props, "modelValue");

onMounted(() => {
  listenKey();
});
</script>

<style scoped lang="scss"></style>
