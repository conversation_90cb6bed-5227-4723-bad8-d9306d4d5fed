<template>
  <el-form v-loading="loading" :model="form" ref="user_form" :label-width="60" @keyup.enter.stop @submit.prevent.stop>
    <el-alert v-show="formAlert.title" :title="formAlert.title" :type="formAlert.type" />
    <el-row v-if="userData.list.length" :gutter="16">
      <div v-for="user in userData.list" :key="user.id" class="el-card" :style="{ width: '260px', margin: '6px', cursor: 'pointer' }" @click="emits('update:modelValue', user.id)">
        <div class="flex-row" style="align-items: center; padding: 2px 8px">
          <div style="flex-shrink: 0">
            <el-avatar icon="el-icon-user-solid" shape="circle" :src="user.profilePicture" fit="fill"></el-avatar>
          </div>
          <div style="flex-shrink: 1; width: 100%">
            <!-- <div>昵称： {{ user.nickname }}</div> -->
            <div>账号： {{ user.account }}</div>
            <div>手机号： {{ user.phone }}</div>
            <div>邮箱： {{ user.email }}</div>
          </div>
          <el-radio :model-value="props.modelValue === user.id" :label="true"><span></span></el-radio>
        </div>
      </div>
      <!-- <div v-for="user in userData.list" :key="user.id" class="el-card" :style="{ width: '260px', margin: '6px', cursor: 'pointer' }" @click="emits('update:modelValue', user.id)">
        <div class="flex-row" style="align-items: center; padding: 2px 8px">
          <div style="flex-shrink: 0">
            <el-avatar icon="el-icon-user-solid" shape="circle" :src="user.profilePicture" fit="fill"></el-avatar>
          </div>
          <div style="flex-shrink: 1; width: 100%">
            <div>昵称： {{ user.nickname }}</div>
            <div>账号： {{ user.account }}</div>
            <div>手机号： {{ user.phone }}</div>
            <div>邮箱： {{ user.email }}</div>
          </div>
          <el-radio :model-value="props.modelValue === user.id" :label="true"><span></span></el-radio>
        </div>
      </div>
      <div v-for="user in userData.list" :key="user.id" class="el-card" :style="{ width: '260px', margin: '6px', cursor: 'pointer' }" @click="emits('update:modelValue', user.id)">
        <div class="flex-row" style="align-items: center; padding: 2px 8px">
          <div style="flex-shrink: 0">
            <el-avatar icon="el-icon-user-solid" shape="circle" :src="user.profilePicture" fit="fill"></el-avatar>
          </div>
          <div style="flex-shrink: 1; width: 100%">
            <div>昵称： {{ user.nickname }}</div>
            <div>账号： {{ user.account }}</div>
            <div>手机号： {{ user.phone }}</div>
            <div>邮箱： {{ user.email }}</div>
          </div>
          <el-radio :model-value="props.modelValue === user.id" :label="true"><span></span></el-radio>
        </div>
      </div> -->
    </el-row>
    <el-row v-else :gutter="16">
      <el-col v-if="extend" :span="props.width > 600 ? 12 : 24">
        <el-form-item label="姓名" prop="name" :rules="[buildValidatorData({ name: 'required', title: '姓名' })]">
          <el-input v-model="form.name" type="text" clearable :placeholder="$t('glob.Please input field', { field: '姓名' })"></el-input>
        </el-form-item>
      </el-col>
      <!-- <el-col v-if="extend" :span="props.width > 600 ? 12 : 24">
        <el-form-item label="昵称" prop="nickname" :rules="[buildValidatorData({ name: 'required', title: '昵称' })]">
          <el-input v-model="form.nickname" type="text" clearable :placeholder="$t('glob.Please input field', { field: '昵称' })"></el-input>
        </el-form-item>
      </el-col> -->
      <el-col :span="props.width > 600 ? 12 : 24">
        <el-form-item
          label="账号"
          prop="account"
          :rules="[
            { required: !form.phone && !form.email, message: $t('glob.Please input field', { field: '账号' }), trigger: 'blur' },
            { ...valid, message: '该账号未注册用户' },
          ]"
        >
          <el-input v-model="form.account" type="text" clearable :placeholder="$t('glob.Please input field', { field: '账号' })" @keyup.enter="user_form?.validate()"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="props.width > 600 ? 12 : 24">
        <el-form-item
          label="电话"
          prop="phone"
          :rules="[
            { required: !form.account && !form.email, message: $t('glob.Please input field', { field: '电话' }), trigger: 'blur' },
            { ...valid, message: '该电话未注册用户' },
          ]"
        >
          <el-input v-model="form.phone" type="text" clearable :placeholder="$t('glob.Please input field', { field: '电话' })" @keyup.enter="user_form?.validate()"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="props.width > 600 ? 12 : 24">
        <el-form-item
          label="邮箱"
          prop="email"
          :rules="[
            { required: !form.account && !form.phone, message: $t('glob.Please input field', { field: '邮箱' }), trigger: 'blur' },
            { ...valid, message: '该邮箱未注册用户' },
          ]"
        >
          <el-input v-model="form.email" type="text" clearable :placeholder="$t('glob.Please input field', { field: '邮箱' })" @keyup.enter="user_form?.validate()"></el-input>
        </el-form-item>
      </el-col>
      <el-col v-if="extend" :span="props.width > 600 ? 12 : 24">
        <el-form-item label="性别" prop="gender" :rules="[buildValidatorData({ name: 'required', title: '生日' })]">
          <el-radio-group v-model="form.gender">
            <el-radio label="FEMALE">女</el-radio>
            <el-radio label="MALE">男</el-radio>
            <el-radio label="SECRET">保密</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col v-if="extend" :span="props.width > 600 ? 12 : 24">
        <el-form-item label="生日" prop="birthday" :rules="[]">
          <!-- buildValidatorData({ name: 'required', title: '生日' }) -->
          <el-date-picker v-model="form.birthday" type="date" clearable value-format="YYYY-MM-DD" :placeholder="$t('glob.Please select field', { field: '生日' })"></el-date-picker>
        </el-form-item>
      </el-col>
      <el-col v-if="extend" :span="props.width > 600 ? 12 : 24">
        <el-form-item label="密码" prop="password" :rules="[buildValidatorData({ name: 'required', title: '密码' }), buildValidatorData({ name: 'password', title: '密码' })]">
          <el-input v-model="form.password" type="password" show-password clearable :placeholder="$t('glob.Please select field', { field: '密码' })"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script lang="ts" setup name="PersonnelSelect">
import { ref, reactive, nextTick, onBeforeUnmount, watch } from "vue";
import { ElMessage, FormInstance, FormItemRule } from "element-plus";
import { getUserByAccountOrEmailOrPhone, getUserById } from "@/api/personnel";
import type { UserItem } from "@/api/personnel";
import { addUser } from "@/api/personnel";
import { getAvatar } from "@/api/system";
import { buildValidatorData } from "@/utils/validate";
import { bus } from "./index";

interface Props {
  width: number;
  platform?: string;
  modelValue: any;
}
const props = withDefaults(defineProps<Props>(), {
  width: 0,
  platform: "",
});
interface Emits {
  (event: "update:modelValue", value: any): void;
}
const emits = defineEmits<Emits>();

const user_form = ref<FormInstance>();

interface FormModel {
  name: string; // 姓名
  nickname: string; // 昵称
  account: string; // 账号
  phone: string; // 手机号码
  email: string; // 邮箱
  gender: "FEMALE" | "MALE" | "SECRET"; // 性别
  birthday: string; // 生日
  password: string; // 密码
}
const form = reactive<FormModel>({
  name: "",
  nickname: "",
  account: "",
  phone: "",
  email: "",
  gender: "SECRET",
  birthday: "",
  password: "",
});

interface UserData {
  list: UserItem[];
}
const userData = reactive<UserData>({
  list: [],
});

watch(
  () => props.modelValue,
  async (value) => {
    if (value) {
      loading.value = true;
      try {
        const { success, message, data } = await getUserById({ id: value });
        if (success) userData.list = [data];
        else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        emits("update:modelValue", "");
      }
      loading.value = false;
    }
  }
);

onBeforeUnmount(
  bus.on((event: boolean, callback: (done?: string) => void) => {
    if (props.modelValue) {
      callback(props.modelValue);
    } else {
      user_form.value?.validate(async (valid) => {
        if (valid) {
          try {
            const { success, message, data } = await addUser({ platform: props.platform, name: form.name, nickname: form.nickname, account: form.account, phone: form.phone, email: form.email, gender: form.gender, birthday: form.birthday, password: form.password });
            if (success) {
              emits("update:modelValue", data.id);
              callback(data.id);
            } else throw new Error(message);
          } catch (error) {
            if (error instanceof Error) ElMessage.error(error.message);
            callback("");
          }
        } else {
          callback("");
        }
      });
    }
  })
);

const valid = reactive<FormItemRule>({
  validator(_rule: any, _value: any, callback: (error?: string | Error | undefined) => void) {
    loading.value = true;
    formAlert.title = "";
    nextTick(async () => {
      try {
        const data = await getUserByAccountOrEmailOrPhone({ platform: props.platform, account: form.account, phone: form.phone, email: form.email });
        if (!data.length) throw new Error("");
        userData.list = await Promise.all<UserItem>(data.map(async (v) => ({ ...v, profilePicture: await getAvatar({ filePath: v.profilePicture }) })));
      } catch (error) {
        if (error instanceof Error) {
          formAlert.type = "warning";
          formAlert.title = error.message;
        }
      } finally {
        extend.value = true;
        loading.value = false;
        callback();
      }
    });
  },
  trigger: "blur",
});

const formAlert = reactive<{ title: string; type: "success" | "warning" | "info" | "error" }>({
  title: "",
  type: "success",
});

const extend = ref<boolean>(false);

const loading = ref<boolean>(false);

defineExpose({});
</script>

<style lang="scss" scoped>
.user-item {
  background-color: var(--el-fill-color-blank);
}
</style>
