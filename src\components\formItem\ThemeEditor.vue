<template>
  <el-row class="layout-mode-box-style-row tw-w-full" :gutter="10">
    <el-col :span="12" class="tw-my-[5px]">
      <div class="layout-mode-style base" :class="[{ active: value === appTheme.BASE }]" @click="value = appTheme.BASE">
        <div class="layout-mode-style-box">
          <div class="layout-mode-style-aside"></div>
          <div class="layout-mode-style-container-box">
            <div class="layout-mode-style-header"></div>
            <div class="layout-mode-style-container"></div>
          </div>
        </div>
        <div class="layout-mode-style-name">{{ $t("layouts.default") }}</div>
      </div>
    </el-col>
    <el-col :span="12" class="tw-my-[5px]">
      <div class="layout-mode-style classic" :class="[{ active: value === appTheme.CLASSICS }]" @click="value = appTheme.CLASSICS">
        <div class="layout-mode-style-box">
          <div class="layout-mode-style-aside"></div>
          <div class="layout-mode-style-container-box">
            <div class="layout-mode-style-header"></div>
            <div class="layout-mode-style-container"></div>
          </div>
        </div>
        <div class="layout-mode-style-name">{{ $t("layouts.classic") }}</div>
      </div>
    </el-col>
    <el-col :span="12" class="tw-my-[5px]">
      <div class="layout-mode-style simplicity" :class="[{ active: value === appTheme.SIMPLICITY }]" @click="value = appTheme.SIMPLICITY">
        <div class="layout-mode-style-box">
          <div class="layout-mode-style-container-box">
            <div class="layout-mode-style-header"></div>
            <div class="layout-mode-style-container"></div>
          </div>
        </div>
        <div class="layout-mode-style-name">{{ $t("layouts.simplicity") }}</div>
      </div>
    </el-col>
    <el-col :span="12" class="tw-my-[5px]">
      <div class="layout-mode-style focus" :class="[{ active: value === appTheme.FOCUS }]" @click="value = appTheme.FOCUS">
        <div class="layout-mode-style-box">
          <div class="layout-mode-style-aside"></div>
          <div class="layout-mode-style-container-box">
            <div class="layout-mode-style-header"></div>
            <div class="layout-mode-style-container"></div>
          </div>
        </div>
        <div class="layout-mode-style-name">{{ $t("layouts.focus") }}</div>
      </div>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import { useModel } from "vue";
import { appTheme } from "@/api/application";

interface Props {
  modelValue?: keyof typeof appTheme;
}
const props = withDefaults(defineProps<Props>(), { modelValue: appTheme.BASE });

const value = useModel(props, "modelValue");
</script>

<style scoped lang="scss">
.layout-mode-style {
  position: relative;
  box-sizing: content-box;
  height: 100px;
  border: 1px solid var(--el-border-color-light);
  border-radius: var(--el-border-radius-small);
  &:hover,
  &.active {
    border: 1px solid var(--el-color-primary);
  }
  .layout-mode-style-name {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-color-primary-light-5);
    border-radius: 50%;
    height: 50px;
    width: 50px;
    border: 1px solid var(--el-color-primary-light-3);
  }
  .layout-mode-style-box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
  &.base {
    display: flex;
    align-items: center;
    justify-content: center;
    .layout-mode-style-aside {
      width: 18%;
      height: 90%;
      background-color: var(--el-border-color-lighter);
    }
    .layout-mode-style-container-box {
      width: 68%;
      height: 90%;
      margin-left: 5px;
      .layout-mode-style-header {
        width: 100%;
        height: 10%;
        background-color: var(--el-border-color-lighter);
      }
      .layout-mode-style-container {
        width: 100%;
        height: calc(90% - 5px);
        background-color: var(--el-border-color-extra-light);
        margin-top: 5px;
      }
    }
  }
  &.classic {
    display: flex;
    align-items: center;
    justify-content: center;
    .layout-mode-style-aside {
      width: 18%;
      height: 100%;
      background-color: var(--el-border-color-lighter);
    }
    .layout-mode-style-container-box {
      width: 82%;
      height: 100%;
      .layout-mode-style-header {
        width: 100%;
        height: 10%;
        background-color: var(--el-border-color);
      }
      .layout-mode-style-container {
        width: 100%;
        height: 90%;
        background-color: var(--el-border-color-extra-light);
      }
    }
  }
  &.simplicity {
    display: flex;
    align-items: center;
    justify-content: center;
    .layout-mode-style-container-box {
      width: 100%;
      height: 100%;
      .layout-mode-style-header {
        width: 100%;
        height: 10%;
        background-color: var(--el-border-color);
      }
      .layout-mode-style-container {
        width: 100%;
        height: 90%;
        background-color: var(--el-border-color-extra-light);
      }
    }
  }
  &.focus {
    display: flex;
    align-items: center;
    justify-content: center;
    .layout-mode-style-aside {
      width: 18%;
      height: 100%;
      background-color: var(--el-border-color);
    }
    .layout-mode-style-container-box {
      width: 82%;
      height: 100%;
      .layout-mode-style-header {
        width: 100%;
        height: 10%;
        background-color: var(--el-border-color);
      }
      .layout-mode-style-container {
        width: 100%;
        height: 90%;
        background-color: var(--el-border-color-extra-light);
      }
    }
  }
}
</style>
