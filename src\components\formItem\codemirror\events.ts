/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */
// import { EditorState } from "@codemirror/state";
// import { EditorView, ViewUpdate } from "@codemirror/view";

// export enum EventKey {
//   Change = "change",
//   Update = "update",
//   Focus = "focus",
//   Blur = "blur",
//   Ready = "ready",
//   ModelUpdate = "update:modelValue",
// }
// export interface EditorEvents {
//   // when content(doc) change only
//   ($event: EventKey.Change, value: string, viewUpdate: ViewUpdate): true;
//   // when codemirror state change
//   ($event: EventKey.Update, viewUpdate: ViewUpdate): true;
//   ($event: EventKey.Focus, viewUpdate: ViewUpdate): true;
//   ($event: EventKey.Blur, viewUpdate: ViewUpdate): true;
//   // when component mounted
//   ($event: EventKey.Ready, payload: { view: EditorView; state: EditorState; container: HTMLDivElement }): true;
// }
// // export interface ModelUpdateEvent {
// //   ($event: EventKey.ModelUpdate, value: string, viewUpdate: ViewUpdate): true;
// // }
// export type Events = EditorEvents;
