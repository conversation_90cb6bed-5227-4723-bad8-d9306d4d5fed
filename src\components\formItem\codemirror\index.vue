<template>
  <div class="codemirror el-input el-input--default el-input--suffix" @keyup.stop>
    <div ref="container" class="el-input__inner" :style="{ display: 'contents' }"></div>
  </div>
</template>

<script setup lang="ts" generic="T">
import { shallowRef, computed, watch, toRaw, onMounted, onBeforeUnmount } from "vue";
import type { CSSProperties } from "vue";

import { EditorView } from "@codemirror/view";
import { EditorState } from "@codemirror/state";

import type { EditorStateConfig, Extension } from "@codemirror/state";
import type { ViewUpdate } from "@codemirror/view";
import { createEditorState, createEditorView, destroyEditorView, getEditorTools } from "./codemirror";
import { useGlobalConfig, DEFAULT_CONFIG } from "./config";
import { ConfigProps } from "./props";
import { useConfig } from "@/stores/config";

// eslint-disable-next-line no-undef
defineOptions({ name: "Codemirror" });

const container = shallowRef<HTMLDivElement>();
const state = shallowRef<EditorState>();
const view = shallowRef<EditorView>();
const $config = useConfig();

const defaultConfig: ConfigProps = {
  ...DEFAULT_CONFIG,
  ...useGlobalConfig(),
};
interface Props {
  modelValue?: string;
  autofocus?: boolean;
  disabled?: boolean;
  indentWithTab?: boolean;
  tabSize?: number;
  placeholder?: string;
  style?: CSSProperties;
  autoDestroy?: boolean;
  phrases?: Record<string, string>;
  root?: ShadowRoot | Document;
  extensions?: Extension;
  selection?: EditorStateConfig["selection"];
}
const props = withDefaults(defineProps<Props>(), {
  autofocus: false,
  disabled: false,
  indentWithTab: true,
  tabSize: 2,
  placeholder: "",
  autoDestroy: true,
});

// interface Events {
//   "update:modelValue": [value: string, viewUpdate: ViewUpdate];
//   // when content(doc) change only
//   "change": [value: string, viewUpdate: ViewUpdate];
//   // when codemirror state change
//   "update": [viewUpdate: ViewUpdate];
//   "focus": [viewUpdate: ViewUpdate];
//   "blur": [viewUpdate: ViewUpdate];
//   // when component mounted
//   "ready": [payload: { view: EditorView; state: EditorState; container: HTMLDivElement }];
// }
interface Events {
  ($event: "update:modelValue", value: string, viewUpdate: ViewUpdate): true;
  // when content(doc) change only
  ($event: "change", value: string, viewUpdate: ViewUpdate): true;
  // when codemirror state change
  ($event: "update", viewUpdate: ViewUpdate): true;
  ($event: "focus", viewUpdate: ViewUpdate): true;
  ($event: "blur", viewUpdate: ViewUpdate): true;
  // when component mounted
  ($event: "ready", payload: { view: EditorView; state: EditorState; container: HTMLDivElement }): true;
}
const emits = defineEmits<Events>();

const config = computed<Omit<Props, "modelValue">>(() => Object.entries(toRaw(props)).reduce((p, [k, v]) => (k === "modelValue" ? p : { ...p, [k]: v }), { ...props }));

onMounted(() => {
  state.value = createEditorState({
    doc: props.modelValue,
    selection: config.value.selection,
    // The extensions are split into two parts, global and component prop.
    // Only the global part is initialized here.
    // The prop part is dynamically reconfigured after the component is mounted.
    extensions: defaultConfig.extensions ?? [],
    onFocus: (viewUpdate) => emits("focus", viewUpdate),
    onBlur: (viewUpdate) => emits("blur", viewUpdate),
    onUpdate: (viewUpdate) => emits("update", viewUpdate),
    onChange: (newDoc, viewUpdate) => {
      if (newDoc !== props.modelValue) {
        emits("change", newDoc, viewUpdate);
        emits("update:modelValue", newDoc, viewUpdate);
      }
    },
  });

  view.value = createEditorView({
    state: state.value,
    parent: container.value!,
    root: config.value.root,
  });

  const editorTools = getEditorTools(view.value);

  // watch prop.modelValue
  watch(
    () => props.modelValue,
    (newValue) => {
      if (newValue !== editorTools.getDoc()) {
        editorTools.setDoc(newValue);
      }
    }
  );

  // watch prop.extensions
  watch(
    () => props.extensions,
    (extensions) => editorTools.reExtensions(extensions || []),
    { immediate: true }
  );

  // watch prop.disabled
  watch(
    () => config.value.disabled,
    (disabled) => editorTools.toggleDisabled(disabled),
    { immediate: true }
  );

  // watch prop.indentWithTab
  watch(
    () => config.value.indentWithTab,
    (iwt) => editorTools.toggleIndentWithTab(iwt),
    { immediate: true }
  );

  // watch prop.tabSize
  watch(
    () => config.value.tabSize,
    (tabSize) => editorTools.setTabSize(tabSize!),
    { immediate: true }
  );

  // watch prop.phrases
  watch(
    () => config.value.phrases,
    (phrases) => editorTools.setPhrases(phrases || {}),
    { immediate: true }
  );

  // watch prop.placeholder
  watch(
    () => config.value.placeholder,
    (placeholder) => editorTools.setPlaceholder(placeholder!),
    { immediate: true }
  );

  // watch prop.style
  watch(
    () => [config.value.style, $config.layout.isDark],
    ([style]) => editorTools.setStyle(style as CSSProperties),
    { immediate: true }
  );

  // immediate autofocus
  if (config.value.autofocus) {
    editorTools.focus();
  }

  emits("ready", {
    state: state.value!,
    view: view.value!,
    container: container.value!,
  });
});

onBeforeUnmount(() => {
  if (config.value.autoDestroy && view.value) {
    destroyEditorView(view.value);
  }
});
</script>

<style lang="scss" scoped></style>
