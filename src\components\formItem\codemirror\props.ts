import type { CSSProperties } from "vue";
import { EditorStateConfig } from "@codemirror/state";

export interface ConfigProps {
  autofocus?: boolean;
  disabled?: boolean;
  indentWithTab?: boolean;
  tabSize?: number;
  placeholder?: string;
  style?: CSSProperties;
  autoDestroy?: boolean;
  phrases?: Record<string, string>;
  root?: ShadowRoot | Document;
  extensions: EditorStateConfig["extensions"];
  selection: EditorStateConfig["selection"];
}
export interface ModelValueProp {
  modelValue: string;
}
export type Props = ConfigProps & ModelValueProp;
// export const configProps = {
//   autofocus: NonDefaultBooleanType,
//   disabled: NonDefaultBooleanType,
//   indentWithTab: NonDefaultBooleanType,
//   tabSize: Number,
//   placeholder: String,
//   style: Object as PropType<CSSProperties>,
//   autoDestroy: NonDefaultBooleanType,
//   phrases: Object as PropType<Record<string, string>>,
//   // codemirror options
//   root: Object as PropType<ShadowRoot | Document>,
//   extensions: Array as PropType<EditorStateConfig["extensions"]>,
//   selection: Object as PropType<EditorStateConfig["selection"]>,
// };

// export const modelValueProp = {
//   modelValue: {
//     type: String,
//     default: "",
//   },
// };

// export const props = {
//   ...configProps,
//   ...modelValueProp,
// };

// export type ConfigProps = ExtractPropTypes<typeof configProps>;
// export type Props = ExtractPropTypes<typeof props>;
// export type PropKey = keyof Props;
