/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */
import { EditorView } from "@codemirror/view";
import { HighlightStyle, syntaxHighlighting } from "@codemirror/language";
import { tags as t } from "@lezer/highlight";

const light00 = /* "#657b83" */ "var(--el-text-color-primary)";
const light01 = /* "#586e75" */ "var(--el-fill-color)";
const light02 = "#073642";
const light03 = /* "#002b36" */ "var(--el-color-primary)";
const light04 = "#839496";
const light05 = "#93a1a1";
const light06 = "#eee8d5";
const light07 = /* "#f2f7ff" */ "var(--el-bg-color)";
const dark00 = "#1d1f21";
const dark01 = "#073642";
const dark02 = "#586e75";
const dark03 = "#657b83";
const dark04 = "#839496";
const dark05 = "#93a1a1";
const dark06 = "#eee8d5";
const dark07 = "#fdf6e3";
const base_red = "#dc322f";
const base_orange = "#cb4b16";
const base_yellow = "#b58900";
const base_green = "#859900";
const base_cyan = "#2aa198";
const base_blue = "#268bd2";
const base_violet = "#6c71c4";
const base_magenta = "#d33682";

const lightInvalid = "#d30102";
const lightHighlightBackground = "var(--el-color-primary-light-9)";
const lightBackground = light07;
const lightTooltipBackground = light01;
const lightSelection = "#3f6ad8";
const lightCursor = /* light01 */ light00;
const darkInvalid = "#d30102";
const darkStone = dark04;
const darkHighlightBackground = "#3a4d62";
const darkBackground = dark00;
const darkTooltipBackground = dark01;
const darkSelection = "#3a4d62";
const darkCursor = dark04;

/// The highlighting style for code in the Solarized Light theme.
export const solarizedLightHighlightStyle = HighlightStyle.define([
  { tag: t.keyword, color: base_green },
  { tag: [t.name, t.deleted, t.character, t.propertyName, t.macroName], color: base_cyan },
  { tag: [t.variableName], color: base_blue },
  { tag: [t.function(t.variableName)], color: base_blue },
  { tag: [t.labelName], color: base_magenta },
  { tag: [t.color, t.constant(t.name), t.standard(t.name)], color: base_yellow },
  { tag: [t.definition(t.name), t.separator], color: base_cyan },
  { tag: [t.brace], color: base_magenta },
  { tag: [t.annotation], color: lightInvalid },
  { tag: [t.number, t.changed, t.annotation, t.modifier, t.self, t.namespace], color: base_magenta },
  { tag: [t.typeName, t.className], color: base_orange },
  { tag: [t.operator, t.operatorKeyword], color: base_violet },
  { tag: [t.tagName], color: base_blue },
  { tag: [t.squareBracket], color: base_red },
  { tag: [t.angleBracket], color: light02 },
  { tag: [t.attributeName], color: light05 },
  { tag: [t.regexp], color: lightInvalid },
  { tag: [t.quote], color: base_green },
  { tag: [t.string], color: base_yellow },
  { tag: t.link, color: base_cyan, textDecoration: "underline", textUnderlinePosition: "under" },
  { tag: [t.url, t.escape, t.special(t.string)], color: base_yellow },
  { tag: [t.meta], color: base_red },
  { tag: [t.comment], color: light02, fontStyle: "italic" },
  { tag: t.strong, fontWeight: "bold", color: light01 },
  { tag: t.emphasis, fontStyle: "italic", color: base_green },
  { tag: t.strikethrough, textDecoration: "line-through" },
  { tag: t.heading, fontWeight: "bold", color: base_yellow },
  { tag: t.heading1, fontWeight: "bold", color: light03 },
  { tag: [t.heading2, t.heading3, t.heading4], fontWeight: "bold", color: light03 },
  { tag: [t.heading5, t.heading6], color: light03 },
  { tag: [t.atom, t.bool, t.special(t.variableName)], color: base_magenta },
  { tag: [t.processingInstruction, t.inserted, t.contentSeparator], color: base_red },
  { tag: [t.contentSeparator], color: base_yellow },
  { tag: t.invalid, color: light02, borderBottom: `1px dotted ${base_red}` },
]);

/// The highlighting style for code in the Solarized Dark theme.
export const solarizedDarkHighlightStyle = HighlightStyle.define([
  { tag: t.keyword, color: base_green },
  { tag: [t.name, t.deleted, t.character, t.propertyName, t.macroName], color: base_cyan },
  { tag: [t.variableName], color: dark05 },
  { tag: [t.function(t.variableName)], color: base_blue },
  { tag: [t.labelName], color: base_magenta },
  { tag: [t.color, t.constant(t.name), t.standard(t.name)], color: base_yellow },
  { tag: [t.definition(t.name), t.separator], color: base_cyan },
  { tag: [t.brace], color: base_magenta },
  { tag: [t.annotation], color: darkInvalid },
  { tag: [t.number, t.changed, t.annotation, t.modifier, t.self, t.namespace], color: base_magenta },
  { tag: [t.typeName, t.className], color: base_orange },
  { tag: [t.operator, t.operatorKeyword], color: base_violet },
  { tag: [t.tagName], color: base_blue },
  { tag: [t.squareBracket], color: base_red },
  { tag: [t.angleBracket], color: dark02 },
  { tag: [t.attributeName], color: dark05 },
  { tag: [t.regexp], color: darkInvalid },
  { tag: [t.quote], color: base_green },
  { tag: [t.string], color: base_yellow },
  { tag: t.link, color: base_cyan, textDecoration: "underline", textUnderlinePosition: "under" },
  { tag: [t.url, t.escape, t.special(t.string)], color: base_yellow },
  { tag: [t.meta], color: base_red },
  { tag: [t.comment], color: dark02, fontStyle: "italic" },
  { tag: t.strong, fontWeight: "bold", color: dark06 },
  { tag: t.emphasis, fontStyle: "italic", color: base_green },
  { tag: t.strikethrough, textDecoration: "line-through" },
  { tag: t.heading, fontWeight: "bold", color: base_yellow },
  { tag: t.heading1, fontWeight: "bold", color: dark07 },
  { tag: [t.heading2, t.heading3, t.heading4], fontWeight: "bold", color: dark06 },
  { tag: [t.heading5, t.heading6], color: dark06 },
  { tag: [t.atom, t.bool, t.special(t.variableName)], color: base_magenta },
  { tag: [t.processingInstruction, t.inserted, t.contentSeparator], color: base_red },
  { tag: [t.contentSeparator], color: base_yellow },
  { tag: t.invalid, color: dark02, borderBottom: `1px dotted ${base_red}` },
]);

export const lightTheme = EditorView.theme(
  {
    "&": {
      color: light00,
      backgroundColor: lightBackground,
      borderRadius: "var(--el-input-border-radius, var(--el-border-radius-base))",
      outline: "var(--el-border)",
      minWidth: "100%",
    },

    ".cm-content": { caretColor: lightCursor, fontFamily: "'Source Code Pro', monospace" },

    ".cm-cursor, .cm-dropCursor": { borderLeftColor: lightCursor },
    "&.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection": { backgroundColor: lightSelection },

    ".cm-panels": { backgroundColor: "#dfd9c8", color: light03 },
    ".cm-panels.cm-panels-top": { borderBottom: "2px solid black" },
    ".cm-panels.cm-panels-bottom": { borderTop: "2px solid black" },

    ".cm-searchMatch": { backgroundColor: "#72a1ff59", outline: "1px solid #457dff" },
    ".cm-searchMatch.cm-searchMatch-selected": { backgroundColor: "#6199ff2f" },

    ".cm-activeLine": { backgroundColor: lightHighlightBackground, opacity: "0.5" },
    ".cm-selectionMatch": { backgroundColor: "#aafe661a" },

    "&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket": { outline: `1px solid ${light05}` },

    ".cm-gutters": { backgroundColor: "#00000010", color: light00, border: "none" },

    ".cm-activeLineGutter": { backgroundColor: lightHighlightBackground },

    ".cm-foldPlaceholder": { backgroundColor: "transparent", border: "none", color: "#ddd" },

    ".cm-tooltip": { border: "none", backgroundColor: lightTooltipBackground },
    ".cm-tooltip .cm-tooltip-arrow:before": { borderTopColor: "transparent", borderBottomColor: "transparent" },
    ".cm-tooltip .cm-tooltip-arrow:after": { borderTopColor: lightTooltipBackground, borderBottomColor: lightTooltipBackground },
    ".cm-tooltip-autocomplete": { "& > ul > li[aria-selected]": { backgroundColor: lightHighlightBackground, color: light03 } },
  },
  { dark: false }
);
export const darkTheme = EditorView.theme(
  {
    "&": {
      color: dark05,
      backgroundColor: darkBackground,
      borderRadius: "var(--el-input-border-radius, var(--el-border-radius-base))",
      minWidth: "100%",
    },

    ".cm-content": { caretColor: darkCursor, fontFamily: "'Source Code Pro', monospace" },

    ".cm-cursor, .cm-dropCursor": { borderLeftColor: darkCursor },
    "&.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection": { backgroundColor: darkSelection },

    ".cm-panels": { backgroundColor: "#11243a", color: dark03 },
    ".cm-panels.cm-panels-top": { borderBottom: "2px solid black" },
    ".cm-panels.cm-panels-bottom": { borderTop: "2px solid black" },

    ".cm-searchMatch": { backgroundColor: "#72a1ff59", outline: "1px solid #457dff" },
    ".cm-searchMatch.cm-searchMatch-selected": { backgroundColor: "#6199ff2f" },

    ".cm-activeLine": { backgroundColor: darkHighlightBackground, opacity: "0.5" },
    ".cm-selectionMatch": { backgroundColor: "#aafe661a" },

    "&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket": { outline: `1px solid ${dark06}` },

    ".cm-gutters": { backgroundColor: "#11243a", color: darkStone, border: "none" },

    ".cm-activeLineGutter": { backgroundColor: darkHighlightBackground },

    ".cm-foldPlaceholder": { backgroundColor: "transparent", border: "none", color: "#ddd" },

    ".cm-tooltip": { border: "none", backgroundColor: darkTooltipBackground },
    ".cm-tooltip .cm-tooltip-arrow:before": { borderTopColor: "transparent", borderBottomColor: "transparent" },
    ".cm-tooltip .cm-tooltip-arrow:after": { borderTopColor: darkTooltipBackground, borderBottomColor: darkTooltipBackground },
    ".cm-tooltip-autocomplete": { "& > ul > li[aria-selected]": { backgroundColor: darkHighlightBackground, color: dark03 } },
  },
  { dark: true }
);
export const theme = [syntaxHighlighting(solarizedDarkHighlightStyle, { fallback: true }), syntaxHighlighting(solarizedLightHighlightStyle, { fallback: true })];
