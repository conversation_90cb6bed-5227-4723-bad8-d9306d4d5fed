<template>
  <el-popover :placement="placement" trigger="focus" :hide-after="0" :width="state.selectorWidth" :visible="state.popoverVisible">
    <div class="icon-selector" @mouseover.stop="state.iconSelectorMouseover = true" @mouseout.stop="state.iconSelectorMouseover = false">
      <transition name="el-zoom-in-center">
        <div class="icon-selector-box">
          <div class="selector-header">
            <div class="selector-title" style="flex-shrink: 0; margin-right: 6px">
              <el-switch v-model="state.iconTheme" :disabled="['Editor'].includes(state.iconType)" :active-icon="h('svg', h('use', { href: '#local-DeviceMac-line' }))" active-value="line" :inactive-icon="h('svg', h('use', { href: '#local-DeviceMac-fill' }))" inactive-value="fill"></el-switch>
            </div>
            <div class="selector-tab">
              <!-- <el-link :title="'Element Puls ' + $t('iconSelector.Icon')" :type="state.iconType == 'ele' ? 'primary' : 'default'" :icon="elementIcons.ElementPlus" @click="onChangeTab('ele')">UI</el-link>
              <el-link :title="'Awesome Solid' + $t('iconSelector.Icon')" :type="state.iconType == 'fas' ? 'primary' : 'default'" :icon="h(FontAwesomeIcon, { icon: fasFontAwesome, class: ['icon'] })" @click="onChangeTab('fas')">Solid</el-link>
              <el-link :title="'Awesome Regular' + $t('iconSelector.Icon')" :type="state.iconType == 'far' ? 'primary' : 'default'" :icon="h(FontAwesomeIcon, { icon: farFontAwesome, class: ['icon'] })" @click="onChangeTab('far')">Regular</el-link>
              <el-link :title="'Awesome Brands' + $t('iconSelector.Icon')" :type="state.iconType == 'fab' ? 'primary' : 'default'" :icon="h(FontAwesomeIcon, { icon: fabFontAwesome, class: ['icon'] })" @click="onChangeTab('fab')">Brands</el-link> -->
              <!--  -->
              <el-link :title="'Remix Buildings' + $t('iconSelector.Icon')" :type="state.iconType == 'Buildings' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-BuildingsBuilding-line' }))" @click="onChangeTab('Buildings')">楼栋</el-link>
              <el-link :title="'Remix Business' + $t('iconSelector.Icon')" :type="state.iconType == 'Business' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-BusinessBriefcase-2-line' }))" @click="onChangeTab('Business')">商业</el-link>
              <el-link :title="'Remix Communication' + $t('iconSelector.Icon')" :type="state.iconType == 'Communication' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-CommunicationChat-1-line' }))" @click="onChangeTab('Communication')">构思</el-link>
              <el-link :title="'Remix Design' + $t('iconSelector.Icon')" :type="state.iconType == 'Design' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-DesignBall-pen-line' }))" @click="onChangeTab('Design')">设计</el-link>
              <el-link :title="'Remix Development' + $t('iconSelector.Icon')" :type="state.iconType == 'Development' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-DevelopmentTerminal-box-line' }))" @click="onChangeTab('Development')">开发</el-link>
              <el-link :title="'Remix Device' + $t('iconSelector.Icon')" :type="state.iconType == 'Device' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-DeviceServer-line' }))" @click="onChangeTab('Device')">装置</el-link>
              <el-link :title="'Remix Document' + $t('iconSelector.Icon')" :type="state.iconType == 'Document' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-DocumentArticle-line' }))" @click="onChangeTab('Document')">文件</el-link>
              <el-link :title="'Remix Editor' + $t('iconSelector.Icon')" :type="state.iconType == 'Editor' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-EditorA-b' }))" @click="onChangeTab('Editor')">编辑</el-link>
              <el-link :title="'Remix Finance' + $t('iconSelector.Icon')" :type="state.iconType == 'Finance' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-FinanceCoins-line' }))" @click="onChangeTab('Finance')">资金</el-link>
              <el-link :title="'Remix Health' + $t('iconSelector.Icon')" :type="state.iconType == 'Health' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-HealthHearts-line' }))" @click="onChangeTab('Health')">健康</el-link>
              <el-link :title="'Remix Logos' + $t('iconSelector.Icon')" :type="state.iconType == 'Logos' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-LogosVuejs-line' }))" @click="onChangeTab('Logos')">Logo</el-link>
              <el-link :title="'Remix Map' + $t('iconSelector.Icon')" :type="state.iconType == 'Map' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-MapMap-pin-line' }))" @click="onChangeTab('Map')">地图</el-link>
              <el-link :title="'Remix Media' + $t('iconSelector.Icon')" :type="state.iconType == 'Media' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-MediaLive-line' }))" @click="onChangeTab('Media')">媒体</el-link>
              <el-link :title="'Remix Others' + $t('iconSelector.Icon')" :type="state.iconType == 'Others' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-OthersCharacter-recognition-line' }))" @click="onChangeTab('Others')">其他</el-link>
              <el-link :title="'Remix System' + $t('iconSelector.Icon')" :type="state.iconType == 'System' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-SystemSettings-3-line' }))" @click="onChangeTab('System')">系统</el-link>
              <el-link :title="'Remix User' + $t('iconSelector.Icon')" :type="state.iconType == 'User' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-UserAccount-box-line' }))" @click="onChangeTab('User')">用户</el-link>
              <el-link :title="'Remix Weather' + $t('iconSelector.Icon')" :type="state.iconType == 'Weather' ? 'primary' : 'default'" :icon="h('svg', h('use', { href: '#local-WeatherFoggy-line' }))" @click="onChangeTab('Weather')">天气</el-link>
              <!--  -->
              <!-- <el-link :title="$t('iconSelector.Ali iconcont Icon')" :type="state.iconType == 'ali' ? 'primary' : 'default'" :icon="elIcons.CollectionTag" @click="onChangeTab('ali')">Font</el-link> -->
              <!-- <el-link :title="$t('iconSelector.Local icon title')" :type="state.iconType == 'local' ? 'primary' : 'default'" :icon="elementIcons.Box" @click="onChangeTab('local')">App</el-link> -->
            </div>
          </div>
          <div class="selector-body">
            <el-scrollbar ref="selectorScrollbarRef">
              <div v-if="renderFontIconNames.length > 0">
                <div v-for="(item, key) in renderFontIconNames" :key="`${state.iconType}_${key}`" class="icon-selector-item" :title="item" @click="onIcon(item)">
                  <Icon :name="item" :key="`Icon-${state.iconType}_${key}`" />
                </div>
              </div>
              <div v-else>
                <el-empty :image-size="80" :description="$t('iconSelector.Not Load IconLibrary')" />
              </div>
            </el-scrollbar>
          </div>
        </div>
      </transition>
    </div>
    <template #reference>
      <el-input ref="selectorInput" v-model="state.inputValue" :size="size" :disabled="disabled" :placeholder="$t('glob.search') + $t('iconSelector.Icon')" :class="'size-' + size" @focus="onInputFocus" @blur="onInputBlur">
        <template #prepend>
          <div class="icon-prepend">
            <Icon :key="'Preview-Icon' + state.iconKey" :name="state.prependIcon ? state.prependIcon : state.defaultModelValue" />
            <!-- <div v-if="showIconName" class="name">{{ state.prependIcon ? state.prependIcon : state.defaultModelValue }}</div> -->
          </div>
        </template>
        <template #append>
          <Icon name="el-icon-RefreshRight" @click="onInputRefresh" />
        </template>
      </el-input>
    </template>
  </el-popover>
</template>

<script setup lang="ts">
/* eslint-disable prettier/prettier */
import Icon from "@/components/icon/index.vue";
import * as elementIcons from "@element-plus/icons-vue";
// import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
// import { fas, faFontAwesome as fasFontAwesome } from "@fortawesome/free-solid-svg-icons";
// import { far, faFontAwesome as farFontAwesome } from "@fortawesome/free-regular-svg-icons";
// import { fab, faSquareFontAwesome as fabFontAwesome } from "@fortawesome/free-brands-svg-icons";
import { readonly, reactive, ref, onMounted, nextTick, watch, computed, h } from "vue";
import { useEventListener } from "@vueuse/core";
import { Placement } from "element-plus";

// // console.log(elementIcons);local-BuildingsBuilding-line

interface IconPackage {
  [key: string]: string | IconPackage;
}
const iconPackage = readonly<IconPackage>(process.env["APP_ICON_PACKAGE"]);

type IconType = "ele" | "fas" | "far" | "fab" | "ali" | "remix" | "local" | "Buildings" | "Business" | "Communication" | "Design" | "Development" | "Device" | "Document" | "Editor" | "Finance" | "Health" | "Logos" | "Map" | "Media" | "Others" | "System" | "User" | "Weather";

interface Props {
  size?: "default" | "small" | "large";
  disabled?: boolean;
  title?: string;
  type?: IconType;
  placement?: Placement;
  modelValue?: string;
  showIconName?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  size: "default",
  disabled: false,
  title: "",
  type: "Buildings",
  placement: "bottom",
  modelValue: "",
  showIconName: false,
});

const emits = defineEmits<{
  (e: "update:modelValue", value: string): void;
  (e: "change", value: string): void;
}>();

const selectorInput = ref();
const selectorScrollbarRef = ref();
const state: {
  iconTheme: "line" | "fill";
  iconType: IconType;
  selectorWidth: number;
  popoverVisible: boolean;
  inputFocus: boolean;
  iconSelectorMouseover: boolean;
  fontIconNames: string[];
  inputValue: string;
  prependIcon: string;
  defaultModelValue: string;
  iconKey: number;
} = reactive({
  iconTheme: "line",
  iconType: props.type,
  selectorWidth: 0,
  popoverVisible: false,
  inputFocus: false,
  iconSelectorMouseover: false,
  fontIconNames: [],
  inputValue: "",
  prependIcon: props.modelValue,
  defaultModelValue: props.modelValue || "el-icon-Minus",
  iconKey: 0, // 给icon标签准备个key，以随时使用 h 函数重新生成元素
});

async function unbindIconTree(self: string[], $package: string | IconPackage): Promise<string[]> {
  if (typeof $package === "string") self.push($package);
  else {
    for (const key in $package) {
      if (Object.prototype.hasOwnProperty.call($package, key)) {
        await nextTick();
        if (typeof $package[key] === "string") self.push($package[key] as string);
        else unbindIconTree(self, $package[key] as IconPackage);
      }
    }
  }
  return self;
}

const onInputFocus = () => {
  state.inputFocus = state.popoverVisible = true;
};
const onInputBlur = () => {
  state.inputFocus = false;
  state.popoverVisible = state.iconSelectorMouseover;
};
const onInputRefresh = () => {
  state.iconKey++;
  state.prependIcon = state.defaultModelValue;
  state.inputValue = "";
  emits("update:modelValue", state.defaultModelValue);
  emits("change", state.defaultModelValue);
};
async function onChangeTab(name: IconType) {
  state.iconType = name;
  state.fontIconNames = [];
  await nextTick();
  switch (name) {
    case "ele":
      for (const i in elementIcons) {
        await nextTick();
        state.fontIconNames.push(`el-icon-${elementIcons[i as keyof typeof elementIcons].name}`);
      }
      break;
    // case "fas":
    //   state.fontIconNames = Object.keys(fas).map((v) => `fas-${v}`);
    //   break;
    // case "far":
    //   state.fontIconNames = Object.keys(far).map((v) => `far-${v}`);
    //   break;
    // case "fab":
    //   state.fontIconNames = Object.keys(fab).map((v) => `fab-${v}`);
    //   break;
    case "Buildings":
      await unbindIconTree(state.fontIconNames, iconPackage["Buildings"]);
      break;
    case "Business":
      await unbindIconTree(state.fontIconNames, iconPackage["Business"]);
      break;
    case "Communication":
      await unbindIconTree(state.fontIconNames, iconPackage["Communication"]);
      break;
    case "Design":
      await unbindIconTree(state.fontIconNames, iconPackage["Design"]);
      break;
    case "Development":
      await unbindIconTree(state.fontIconNames, iconPackage["Development"]);
      break;
    case "Device":
      await unbindIconTree(state.fontIconNames, iconPackage["Device"]);
      break;
    case "Document":
      await unbindIconTree(state.fontIconNames, iconPackage["Document"]);
      break;
    case "Editor":
      await unbindIconTree(state.fontIconNames, iconPackage["Editor"]);
      break;
    case "Finance":
      await unbindIconTree(state.fontIconNames, iconPackage["Finance"]);
      break;
    case "Health":
      await unbindIconTree(state.fontIconNames, iconPackage["Health"]);
      break;
    case "Logos":
      await unbindIconTree(state.fontIconNames, iconPackage["Logos"]);
      break;
    case "Map":
      await unbindIconTree(state.fontIconNames, iconPackage["Map"]);
      break;
    case "Media":
      await unbindIconTree(state.fontIconNames, iconPackage["Media"]);
      break;
    case "Others":
      await unbindIconTree(state.fontIconNames, iconPackage["Others"]);
      break;
    case "System":
      await unbindIconTree(state.fontIconNames, iconPackage["System"]);
      break;
    case "User":
      await unbindIconTree(state.fontIconNames, iconPackage["User"]);
      break;
    case "Weather":
      await unbindIconTree(state.fontIconNames, iconPackage["Weather"]);
      break;
    /*  */
    case "ali":
      break;
    case "local":
      await nextTick();
      const svgEl = document.getElementById("local-icon");
      if (svgEl?.dataset.iconName) {
        state.fontIconNames = (svgEl?.dataset.iconName as string).split(",");
      }
      break;
    default:
      state.fontIconNames = [];
      break;
  }
}
const onIcon = (icon: string) => {
  state.iconSelectorMouseover = state.popoverVisible = false;
  state.iconKey++;
  state.prependIcon = icon;
  state.inputValue = "";
  emits("update:modelValue", icon);
  emits("change", icon);
  nextTick(() => {
    selectorInput.value.blur();
  });
};

const renderFontIconNames = computed(() => {
  let inputValue = state.inputValue.trim().toLowerCase();
  return state.fontIconNames.filter((icon: string) => {
    if (icon.toLowerCase().indexOf(inputValue) === -1) return false;
    if (/(\-fill|\-line)$/.test(icon)) {
      switch (state.iconTheme) {
        case "fill":
          return /\-fill$/.test(icon);
        case "line":
          return /\-line$/.test(icon);
      }
    } else return true;
  });
});

// 获取 input 的宽度
const getInputWidth = () => {
  nextTick(() => {
    state.selectorWidth = selectorInput.value.$el.offsetWidth < 500 ? 500 : selectorInput.value.$el.offsetWidth;
  });
};

const popoverVisible = () => {
  state.popoverVisible = state.inputFocus || state.iconSelectorMouseover ? true : false;
};

watch(
  () => props.modelValue,
  () => {
    state.iconKey++;
    if (props.modelValue != state.prependIcon) state.defaultModelValue = props.modelValue;
    if (props.modelValue == "") state.defaultModelValue = "el-icon-Minus";
    state.prependIcon = props.modelValue;
  }
);
onMounted(() => {
  getInputWidth();
  useEventListener(document, "click", popoverVisible);
  nextTick(() => onChangeTab("Buildings"));
});
</script>

<style scoped lang="scss">
.size-small {
  height: 24px;
}
.size-large {
  height: 40px;
}
.size-default {
  height: 32px;
}
.icon-prepend {
  display: flex;
  align-items: center;
  justify-content: center;
  .name {
    padding-left: 5px;
  }
}
.selector-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.selector-tab {
  margin-left: auto;
  :deep(.el-link) {
    user-select: none;
    text-align: center;
    width: 50px;
    margin: 0 6px;
    // &:not(:first-child) {
    //   margin-left: 6px;
    // }
    // &:not(:last-child) {
    //   margin-right: 6px;
    // }
  }
}
.selector-body {
  height: 250px;
}
.icon-selector-item {
  display: inline-block;
  padding: 10px 10px 6px 10px;
  margin: 3px;
  border: 1px solid var(--ba-border-color);
  border-radius: var(--el-border-radius-base);
  cursor: pointer;
  font-size: 18px;
  .icon {
    height: 18px;
    width: 18px;
  }
  &:hover {
    border: 1px solid var(--el-color-primary);
  }
}
:deep(.el-input-group__prepend) {
  padding: 0 10px;
}
:deep(.el-input-group__append) {
  padding: 0 10px;
}
</style>
