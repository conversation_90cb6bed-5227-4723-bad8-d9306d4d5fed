/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */
import type { CSSProperties, DefineComponent } from "vue";
import type { FormItemRule } from "element-plus";
import type { buildValidatorParams } from "@/utils/validate";
import { useEventBus } from "@vueuse/core";

// import { ElInput, ElInputNumber, ElAutocomplete, ElOption, ElOptionGroup, ElSelect, ElSelectV2, ElCascader, ElCascaderPanel, ElColorPicker, ElDatePicker, ElTimePicker, ElTimeSelect, ElCheckbox, ElCheckboxButton, ElCheckboxGroup, ElRadio, ElRadioButton, ElRadioGroup, ElRate, ElSlider, ElSwitch, ElTransfer, ElUpload } from "element-plus";

export enum InputType {
  none = "none",
  text = "text",
  password = "password",
  number = "number",
  radio = "radio",
  checkbox = "checkbox",
  switch = "switch",
  textarea = "textarea",
  array = "array",
  datetime = "datetime",
  year = "year",
  date = "date",
  time = "time",
  select = "select",
  selects = "selects",
  remoteSelect = "remoteSelect",
  editor = "editor",
  city = "city",
  image = "image",
  images = "images",
  avatar = "avatar",
  file = "file",
  files = "files",
  icon = "icon",
  color = "color",
  user = "user",
  inside = "inside",
  html = "html",
  account = "account",
}

// export const itemComponent = {
//   Input: ElInput,
//   Number: ElInputNumber,
//   Autocomplete: ElAutocomplete,

//   Option: ElOption,
//   OptionGroup: ElOptionGroup,
//   Select: ElSelect,
//   SelectV2: ElSelectV2,
//   Cascader: ElCascader,
//   CascaderPanel: ElCascaderPanel,

//   ColorPicker: ElColorPicker,
//   DatePicker: ElDatePicker,
//   TimePicker: ElTimePicker,
//   TimeSelect: ElTimeSelect,
//   Checkbox: ElCheckbox,
//   CheckboxButton: ElCheckboxButton,
//   CheckboxGroup: ElCheckboxGroup,
//   Radio: ElRadio,
//   RadioButton: ElRadioButton,
//   RadioGroup: ElRadioGroup,

//   Rate: ElRate,
//   Slider: ElSlider,
//   Switch: ElSwitch,
//   Transfer: ElTransfer,
//   Upload: ElUpload,
// };

export const bus = useEventBus<boolean>("user");

export function getUserId() {
  return new Promise<string>((resolve) => {
    bus.emit(true, resolve);
  });
}

export interface FormItemProps {
  prop: string;
  modelValue: any;
  title: string;
  type: InputType;
  rules: FormItemRule | FormItemRule[];
  edit?: boolean;
  tooltip?: string;
  clearable?: boolean;
  span?: (width: number) => number;
  style?: CSSProperties;
  group?: string;
  showMessage?: boolean;
  inlineMessage?: boolean;
  disabled?: boolean;
  option?: { label: string; value: string | number; disabled?: boolean }[];
  disabledDate?: (date: Date) => boolean;
  disabledHours?: () => number[];
  disabledMinutes?: (hour: number) => number[];
  disabledSeconds?: (hour: number, minute: number) => number[];
  activeValue?: boolean | string | number;
  inactiveValue?: boolean | string | number;
  required?: buildValidatorParams["name"];
  error?: string;
}

export type RenderType = DefineComponent<FormItemProps>;
