<template>
  <el-col :span="24" v-if="props.type === InputType.user">
    <div class="form-group">
      <el-divider content-position="left">{{ currentLabel }}</el-divider>
      <el-row :gutter="20" class="tw-pt-[9px]">
        <el-col :span="24">
          <el-form-item :label="`${props.title} 手机号 或 邮箱`" prop="owner" :rules="[]">
            <!-- <el-input v-model="data.keyword" :placeholder="$t('glob.Please input field', { field: `${props.title} 手机号 或 邮箱` })" type="text" :clearable="true"></el-input> -->
            <el-select class="tw-w-full" :model-value="value.id ? value : null" @update:model-value="($event) => (value = $event)" value-key="id" :multiple="false" :disabled="disabled" :clearable="props.clearable" filterable remote reserve-keyword :remote-method="remoteUserMethod" :loading="usersLoading" :placeholder="$t('glob.Please input field', { field: `${props.title} 手机号 或 邮箱` })" @clear="() => (value = { name: '', account: '', phone: '', email: '', gender: 'SECRET', birthday: '', password: '' })">
              <el-option class="tw-h-fit" v-for="item in users" :key="item.id" :value="item" :label="item.id === 'new' ? `新用户${item.phone || item.email}` : `${item.name}（${item.account}）`">
                <div class="tw-flex tw-w-full tw-flex-row tw-items-center">
                  <el-avatar shape="circle" :icon="item.name ? undefined : Avatar" fit="fill">{{ item.name[0] }}</el-avatar>
                  <div class="tw-ml-4">
                    <div>
                      <span>{{ item.id === "new" ? "新用户" : "" }}{{ item.name }}{{ item.account ? `（${item.account}）` : "" }}</span>
                      <span class="tw-ml-2 tw-text-[var(--el-text-color-placeholder)]">手机号：{{ item.phone || "--" }}</span>
                    </div>
                    <p>
                      <span class="tw-text-[var(--el-text-color-placeholder)]">邮箱：{{ item.email || "--" }}</span>
                    </p>
                  </div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :prop="`${props.prop}.id`" :rules="[...(props.required ? [buildValidatorData({ name: props.required, title: props.title })] : []), ...(props.rules instanceof Array ? props.rules : [])]" :show-message="showMessage" :inline-message="inlineMessage" :error="props.error">
            <div v-if="value.id" class="el-card tw-flex tw-w-full tw-flex-row tw-items-center tw-border tw-p-4">
              <el-avatar class="tw-flex-shrink-0" shape="circle" :icon="value.name ? undefined : Avatar" fit="fill">{{ value.name[0] }}</el-avatar>
              <div class="tw-ml-4 tw-flex-shrink">
                <div>
                  <span>{{ value.id === "new" ? "新用户" : "" }}{{ value.name }}{{ value.account ? `（${value.account}）` : "" }}</span>
                  <span class="tw-ml-2 tw-text-[var(--el-text-color-placeholder)]">手机号：{{ value.phone || "--" }}</span>
                </div>
                <p>
                  <span class="tw-text-[var(--el-text-color-placeholder)]">邮箱：{{ value.email || "--" }}</span>
                </p>
              </div>
            </div>
          </el-form-item>
        </el-col>
        <template v-if="value.id === 'new'">
          <el-col class="clearfix" :span="width > 580 ? 12 : 24">
            <el-form-item :label="width > 580 ? '' : `${props.title}名称`" :prop="`${props.prop ? `${props.prop}.` : ''}name`" :rules="[]" :show-message="showMessage" :inline-message="inlineMessage" :error="props.error">
              <el-input :model-value="value.name" @update:model-value="($event) => (value = { ...value, name: $event })" :placeholder="$t('glob.Please input field', { field: `${props.title}名称` })" type="text" :disabled="disabled" :clearable="props.clearable"></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col class="clearfix" :span="width > 580 ? 12 : 24">
            <el-form-item :label="width > 580 ? '' : `${props.title}昵称`" :prop="`${props.prop ? `${props.prop}.` : ''}nickname`" :rules="[]" :show-message="showMessage" :inline-message="inlineMessage" :error="props.error">
              <el-input :model-value="value.nickname" @update:model-value="($event) => (value = { ...value, nickname: $event })" :placeholder="$t('glob.Please input field', { field: `${props.title}昵称` })" type="text" :disabled="disabled" :clearable="props.clearable"></el-input>
            </el-form-item>
          </el-col> -->
          <el-col class="clearfix" :span="width > 580 ? 12 : 24">
            <el-form-item :label="width > 580 ? '' : `${props.title}账号`" :prop="`${props.prop ? `${props.prop}.` : ''}account`" :rules="[buildValidatorData({ name: 'required', title: '账号' }), buildValidatorData({ name: 'account', title: '账号' })]" :show-message="showMessage" :inline-message="inlineMessage" :error="props.error">
              <el-input :model-value="value.account" @update:model-value="($event) => (value = { ...value, account: $event })" :placeholder="$t('glob.Please input field', { field: `${props.title}账号` })" type="text" :disabled="disabled || value.keyword === 'account'" :clearable="props.clearable"></el-input>
            </el-form-item>
          </el-col>
          <el-col class="clearfix" :span="width > 580 ? 12 : 24">
            <el-form-item :label="width > 580 ? '' : `${props.title}手机号`" :prop="`${props.prop ? `${props.prop}.` : ''}phone`" :rules="[buildValidatorData({ name: 'required', title: '手机号' }), buildValidatorData({ name: 'mobile', title: '手机号' })]" :show-message="showMessage" :inline-message="inlineMessage" :error="props.error">
              <el-input :model-value="value.phone" @update:model-value="($event) => (value = { ...value, phone: $event })" :placeholder="$t('glob.Please input field', { field: `${props.title}手机号` })" type="text" :disabled="disabled || value.keyword === 'phone'" :clearable="props.clearable"></el-input>
            </el-form-item>
          </el-col>
          <el-col class="clearfix" :span="width > 580 ? 12 : 24">
            <el-form-item :label="width > 580 ? '' : `${props.title}邮箱`" :prop="`${props.prop ? `${props.prop}.` : ''}email`" :rules="[buildValidatorData({ name: 'required', title: '邮箱' }), buildValidatorData({ name: 'email', title: '邮箱' })]" :show-message="showMessage" :inline-message="inlineMessage" :error="props.error">
              <el-input :model-value="value.email" @update:model-value="($event) => (value = { ...value, email: $event })" :placeholder="$t('glob.Please input field', { field: `${props.title}邮箱` })" type="text" :disabled="disabled || value.keyword === 'email'" :clearable="props.clearable"></el-input>
            </el-form-item>
          </el-col>
          <el-col class="clearfix" :span="width > 580 ? 12 : 24">
            <el-form-item :label="width > 580 ? '' : `${props.title}性别`" :prop="`${props.prop ? `${props.prop}.` : ''}gender`" :rules="[]" :show-message="showMessage" :inline-message="inlineMessage" :error="props.error">
              <el-radio-group :model-value="value.gender" @update:model-value="($event) => (value = { ...value, gender: $event })" :disabled="disabled">
                <el-radio v-for="gender in genderOption" :key="gender.value" :label="gender.value">{{ gender.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!-- <el-col class="clearfix" :span="width > 580 ? 12 : 24">
            <el-form-item :label="width > 580 ? '' : `${props.title}生日`" :prop="`${props.prop ? `${props.prop}.` : ''}birthday`" :rules="[]" :show-message="showMessage" :inline-message="inlineMessage" :error="props.error">
              <el-date-picker v-model="value.birthday" @update:model-value="($event) => (value = { ...value, birthday: $event })" type="date" value-format="YYYY-MM-DD" :disabled-date="props.disabledDate" :disabled="disabled" :clearable="props.clearable" :placeholder="$t('glob.Please select field', { field: `${props.title}生日` })"></el-date-picker>
            </el-form-item>
          </el-col> -->
          <el-col class="clearfix" :span="width > 580 ? 12 : 24">
            <el-form-item :label="width > 580 ? '' : `${props.title}语言`" :prop="`${props.prop ? `${props.prop}.` : ''}language`" :rules="[]" :show-message="showMessage" :inline-message="inlineMessage" :error="props.error">
              <el-input :model-value="value.language" @update:model-value="($event) => (value = { ...value, language: $event })" :placeholder="$t('glob.Please input field', { field: `${props.title}昵称` })" type="text" :disabled="disabled" :clearable="props.clearable"></el-input>
            </el-form-item>
          </el-col>
          <el-col class="clearfix" :span="width > 580 ? 12 : 24">
            <el-form-item :label="width > 580 ? '' : `${props.title}密码`" :prop="`${props.prop ? `${props.prop}.` : ''}password`" :rules="[buildValidatorData({ name: 'required', title: '密码' }), buildValidatorData({ name: 'password', title: '密码' })]" :show-message="showMessage" :inline-message="inlineMessage" :error="props.error">
              <el-input :model-value="value.password" @update:model-value="($event) => (value = { ...value, password: $event })" :placeholder="$t('glob.Please input field', { field: `${props.title}密码` })" type="password" :disabled="disabled" :clearable="props.clearable"></el-input>
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </div>
  </el-col>
  <el-col v-else :span="typeof props.span === 'function' ? props.span(width) : width > 600 ? 12 : 24">
    <el-form-item :prop="props.prop" :rules="[...(props.required ? [buildValidatorData({ name: props.required, title: props.title })] : []), ...(props.rules instanceof Array ? props.rules : [])]" :show-message="showMessage" :inline-message="inlineMessage" :error="props.error">
      <template #label>
        <span>{{ currentLabel }}</span>
        <el-tooltip v-if="props.tooltip" :content="(props.tooltip as string)">
          <el-icon :size="16" style="vertical-align: middle; margin-left: 0.5em"><QuestionFilled /></el-icon>
        </el-tooltip>
      </template>
      <template #default>
        <template v-if="params['#TYPE'] === EditorType.Cat || props.type === InputType.none">
          <slot>
            <div>{{ value }}</div>
          </slot>
        </template>
        <template v-else-if="props.type === InputType.text">
          <el-input v-model="value" :style="props.style" type="text" :disabled="disabled" :clearable="props.clearable" :placeholder="$t('glob.Please input field', { field: props.title })">
            <template #suffix><slot></slot></template>
          </el-input>
        </template>
        <template v-else-if="props.type === InputType.textarea">
          <el-input v-model="value" :style="props.style" type="textarea" :disabled="disabled" :clearable="props.clearable" :placeholder="$t('glob.Please input field', { field: props.title })" @keyup.enter.stop></el-input>
        </template>
        <template v-else-if="props.type === InputType.array">
          <el-input :model-value="arrayToString(value, '\n')" :style="props.style" type="textarea" :disabled="disabled" :clearable="props.clearable" :placeholder="$t('glob.Please input field', { field: props.title }) + '，' + $t('glob.Division Rule')" @update:model-value="value = $event.split('\n')" @keyup.enter.stop @change="value = (value instanceof Array ? value : []).filter((v) => v)"></el-input>
        </template>
        <template v-else-if="props.type === InputType.password">
          <el-input v-model="value" :style="props.style" type="password" show-password :disabled="disabled" :placeholder="$t('glob.Please input field', { field: props.title })"></el-input>
        </template>
        <template v-else-if="props.type === InputType.number">
          <el-input-number v-model="value" :min="0" :max="Number.MAX_SAFE_INTEGER" :step="1" :precision="0" :step-strictly="true" :controls="false" :style="props.style" :disabled="disabled" :placeholder="$t('glob.Please input field', { field: props.title })"></el-input-number>
        </template>
        <template v-else-if="props.type === InputType.select">
          <el-select v-model="value" :multiple="false" :disabled="disabled" :clearable="props.clearable" :filterable="props.filterable" :style="{ width: props.width }" :placeholder="$t('glob.Please input field', { field: props.title })">
            <el-option v-for="selectOption in options" :key="selectOption.value" :label="selectOption.label" :value="selectOption.value" :disabled="selectOption.disabled"></el-option>
          </el-select>
        </template>
        <template v-else-if="props.type === InputType.selects">
          <el-select v-model="value" :multiple="true" :disabled="disabled" :placeholder="$t('glob.Please select field', { field: props.title })" :style="{ width: props.width }">
            <el-option v-for="selectsOption in options" :key="selectsOption.value" :label="selectsOption.label" :value="selectsOption.value" :disabled="selectsOption.disabled"></el-option>
          </el-select>
        </template>
        <template v-else-if="props.type === InputType.radio">
          <el-radio-group v-model="value" :disabled="disabled">
            <el-radio v-for="radioOption in options" :key="radioOption.value" :label="radioOption.value" :disabled="radioOption.disabled">{{ radioOption.label }}</el-radio>
          </el-radio-group>
        </template>
        <template v-else-if="props.type === InputType.checkbox">
          <div>
            <el-checkbox :disabled="disabled" :model-value="(options instanceof Array ? options : []).every((v) => (value instanceof Array ? value : []).includes(v.value))" :indeterminate="(options instanceof Array ? options : []).every((v) => (value instanceof Array ? value : []).includes(v.value)) ? false : (options instanceof Array ? options : []).some((v) => (value instanceof Array ? value : []).includes(v.value))" @update:model-value="value = $event ? (options instanceof Array ? options : []).map((v) => v.value) : []">
              <el-icon style="vertical-align: bottom"><Finished /></el-icon>
              <span>{{ $t("glob.All Select") }}</span>
            </el-checkbox>
            <el-checkbox-group v-model="value" :disabled="disabled">
              <el-checkbox v-for="checkboxOption in options" :key="checkboxOption.value" :label="checkboxOption.value" :disabled="checkboxOption.disabled">{{ checkboxOption.label }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </template>
        <template v-else-if="props.type === InputType.icon">
          <icon-selector v-model="value" :disabled="disabled" :show-icon-name="true"></icon-selector>
        </template>
        <template v-else-if="props.type === InputType.date">
          <el-date-picker v-model="value" type="date" value-format="YYYY-MM-DD" :disabled-date="props.disabledDate" :disabled="disabled" :clearable="props.clearable" :placeholder="$t('glob.Please select field', { field: props.title })" :style="{ width: '100%' }"></el-date-picker>
        </template>
        <template v-else-if="props.type === InputType.time">
          <el-time-picker v-model="value" start="00:00" step="00:15" end="23:45" :disabled="disabled" :clearable="props.clearable" :disabled-hours="props.disabledHours" :disabled-minutes="props.disabledMinutes" :disabled-seconds="props.disabledSeconds" :placeholder="$t('glob.Please select field', { field: props.title })"></el-time-picker>
        </template>
        <template v-else-if="props.type === InputType.datetime">
          <el-date-picker v-model="value" type="datetime" :disabled="disabled" :clearable="props.clearable" :disabled-date="props.disabledDate" :placeholder="$t('glob.Please select field', { field: props.title })"></el-date-picker>
        </template>
        <template v-else-if="props.type === InputType.switch">
          <el-switch v-model="value" :disabled="disabled" :active-text="$t('glob.Enable')" :active-value="props.activeValue" :inactive-text="$t('glob.Disable')" :inactive-value="props.inactiveValue"></el-switch>
        </template>
        <template v-else-if="props.type === InputType.inside">
          <inside-path v-model="value" :disabled="disabled" :clearable="props.clearable" :placeholder="$t('glob.Please select field', { field: props.title })"></inside-path>
        </template>
        <template v-else-if="props.type === InputType.html">
          <VueCodemirror v-model="value" :extensions="[html({ matchClosingTags: true, autoCloseTags: true })]"></VueCodemirror>
        </template>
        <template v-else-if="props.type === InputType.avatar">
          <avatar-from v-model="value"></avatar-from>
        </template>
        <template v-else-if="props.type === InputType.account">
          <el-input v-model="value" :style="props.style" type="text" :disabled="disabled" :clearable="props.clearable" :placeholder="$t('glob.Please input field', { field: props.title })">
            <template #append>{{ `@${props.tenantAbbreviation}` }}</template>
          </el-input>
        </template>
      </template>
    </el-form-item>
  </el-col>
</template>

<script setup lang="ts" name="SuperFormItem">
/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */
import { computed, watch, inject, ref, nextTick, readonly, unref } from "vue";
import type { CSSProperties, Ref } from "vue";
import { useI18n } from "vue-i18n";
import { getUser, getCurrentPlatformUser } from "@/api/iam";
import type { UserItem } from "@/api/iam";
import { genderOption } from "@/api/personnel";
import { ElMessage, ElFormItem, ElInput, formContextKey } from "element-plus";
import type { FormItemRule } from "element-plus";
import { QuestionFilled, Finished, Avatar } from "@element-plus/icons-vue";
import { InputType } from "./index";
import { EditorType } from "@/views/common/interface";
import IconSelector from "./iconSelector.vue";
import InsidePath from "./insidePath.vue";
import AvatarFrom from "./AvatarFrom.vue";
import VueCodemirror from "./codemirror/index.vue";
import { buildValidatorData } from "@/utils/validate";
import type { buildValidatorParams } from "@/utils/validate";
import moment from "moment";

import { html } from "@codemirror/lang-html";

const { t } = useI18n();

interface Props {
  prop: string;
  modelValue: any;
  title: string;
  type: InputType;
  rules: FormItemRule | FormItemRule[];
  edit?: boolean;
  tooltip?: string;
  clearable?: boolean;
  span?: (width: number) => number;
  style?: CSSProperties;
  group?: string;
  showMessage?: boolean;
  inlineMessage?: boolean;
  disabled?: boolean;
  option?: { label: string; value: string | number; disabled?: boolean }[];
  disabledDate?: (date: Date) => boolean;
  disabledHours?: () => number[];
  disabledMinutes?: (hour: number) => number[];
  disabledSeconds?: (hour: number, minute: number) => number[];
  activeValue?: boolean | string | number;
  inactiveValue?: boolean | string | number;
  required?: buildValidatorParams["name"];
  error?: string;
  filterable?: boolean;
  width: string;
  tenantAbbreviation: string;
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: undefined,
  prop: "",
  group: "",
  clearable: true,
  span: (width: number) => (width > 600 ? 12 : 24),
  disabled: false,
  disabledDate: (date: Date) => moment().isBefore(date),
  disabledHours: () => [],
  disabledMinutes: (hour: number) => [],
  disabledSeconds: (hour: number, minute: number) => [],
  activeValue: true,
  inactiveValue: false,
  edit: true,
  showMessage: true,
  inlineMessage: false,
  filterable: false,
  width: "",
  tenantAbbreviation: "",
});

const params = inject<Ref<Record<string, string>>>("#PARAMS", () => ref({ "#TYPE": EditorType.Cat }), true);
// eslint-disable-next-line vue/no-dupe-keys
const width = inject<number>("#WIDTH", 0);

interface Emits {
  (event: "update:modelValue", value: any): void;
}
const emits = defineEmits<Emits>();

const value = computed<any>({
  get: () => props.modelValue,
  set: (value) => emits("update:modelValue", value),
});

const usersLoading = ref(false);
const users = ref<UserItem[]>([]);

if (props.type === InputType.user) {
  // watch(value, (culValue, oldValue) => {
  //   if (Object.prototype.hasOwnProperty.call(culValue, "id") && culValue.id) {
  //     if (Object.prototype.hasOwnProperty.call(oldValue, "id") && oldValue.id === culValue.id) return;
  //     users.value = [value];
  //   }
  // });
}

async function remoteUserMethod(keyword: string) {
  try {
    usersLoading.value = true;
    await nextTick();
    users.value = [];
    if (!keyword) return;
    const base: UserItem = {
      id: "new",
      keyword: "",
      name: "",
      // nickname: "",
      account: "",
      phone: "",
      email: "",
      gender: "SECRET",
      language: "none",
      password: "",
      busy: "",
    };
    if (/^(1[3-9])\d{9}$/.test(keyword)) {
      base.phone = keyword;
      base.keyword = "phone";
    } else if (/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/.test(keyword)) {
      base.email = keyword;
      base.keyword = "email";
    }
    if (!base.phone && !base.email) return;
    const req = { ...(base.phone ? { phone: base.phone } : {}), ...(base.email ? { email: base.email } : {}) };
    const { success, message, data: userList } = await (params.value.platform ? getUser({ platform: params.value.platform, ...req, paging: { pageNumber: 1, pageSize: 10 } }) : getCurrentPlatformUser({ ...req, paging: { pageNumber: 1, pageSize: 10 } }));
    if (!success) throw Object.assign(new Error(message), { success, data: userList });
    users.value = (userList instanceof Array ? userList : []).map((user) => ({ ...user, ...req, keyword: base.keyword }));
    if (users.value.length === 0) users.value.push(base);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    usersLoading.value = false;
  }
}

const disabled = computed<boolean>(() => {
  if (props.edit) return props.disabled;
  else if (unref(params)["#TYPE"] !== EditorType.Add) return true;
  else return props.disabled;
});
const formContext = inject(formContextKey, void 0);
const currentLabel = computed(() => `${props.title || ""}${(formContext == null || !props.title ? void 0 : formContext.labelSuffix) || ""}`);

const options = computed<Required<Pick<Props, "option">>["option"]>(() => (props.option instanceof Array ? props.option : []));

function arrayToString(arr: unknown, divisionString: string) {
  return (arr instanceof Array ? arr : []).map((v) => String(["string", "number"].includes(typeof v) ? v : "")).join(divisionString);
  // .filter((v) => v)
}
</script>

<style scoped lang="scss">
.ba-form-item-label {
  display: inline-block;
  .ba-form-item-label-tip {
    padding-left: 6px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}
.ba-form-item-not-support {
  line-height: 15px;
}
.ba-input-item-array :deep(.el-form-item__content) {
  display: block;
  padding-bottom: 32px;
}
</style>
