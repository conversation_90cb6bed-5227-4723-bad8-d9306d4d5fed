<template>
  <div></div>
  <!-- <el-cascader v-model="value" :options="options" :props="{ lazy: false, lazyLoad: lazyLoadPath }" :placeholder="props.placeholder" :disabled="props.disabled" :clearable="props.clearable"></el-cascader> -->
</template>

<script setup lang="ts" name="FormCascader">
// /* eslint-disable no-unused-vars, @typescript-eslint/no-unused-vars */
// import { nextTick, reactive } from "vue";
// import { ElCascader, Resolve } from "element-plus";
// import type { CascaderValue, CascaderNode, CascaderOption } from "element-plus";
// import { computed } from "vue";

// type OptionsItem = Map<string, OptionsItem>;
// const sourceViews = Object.entries(require.resolve("@/src/views"))
//   .map(([key]) => ["/src/views"].concat(key.replace(/^\/src\/views\//, "").split("/")))
//   .reduce((p, c) => {
//     let item: OptionsItem;
//     const root = c.shift() as string;
//     if (!p.get(root)) p.set(root, new Map());
//     item = p.get(root) as OptionsItem;
//     while (c.length) {
//       const cursor = c.shift() as string;
//       if (!item.get(cursor)) item.set(cursor, new Map());
//       item = item.get(cursor) as OptionsItem;
//     }
//     return p;
//   }, new Map<string, OptionsItem>());

// function bindTree(item: OptionsItem, path: string): CascaderOption {
//   return {
//     label: path,
//     value: path,
//     children: Array.from(item.entries()).map(([key, value]) => bindTree(value, key)),
//     leaf: item.size === 0,
//   };
// }

// const options = reactive<CascaderOption[]>(Array.from(sourceViews.entries()).map(([key, value]) => bindTree(value, key)));

// interface Props {
//   modelValue: undefined;
//   placeholder?: string;
//   disabled?: boolean;
//   clearable?: boolean;
// }
// const props = withDefaults(defineProps<Props>(), {
//   modelValue: undefined,
//   disabled: false,
//   clearable: true,
// });

// interface Emits {
//   (event: "update:modelValue", value: any): void;
// }
// const emits = defineEmits<Emits>();

// const value = computed<CascaderValue>({
//   get: () => (props.modelValue ? ["/src/views"].concat((typeof props.modelValue === "string" ? props.modelValue : "").replace(/^\/src\/views\//, "").split("/")) : []),
//   set: (value) => emits("update:modelValue", (value instanceof Array ? value : ["/src/views"]).join("/")),
// });
// async function lazyLoadPath(node: CascaderNode, resolve: (dataList?: CascaderOption[]) => void) {
//   const { pathNodes, level } = node;
//   await nextTick();
//   if (level) {
//     const path = pathNodes.map((v) => v.value);
//     let item = sourceViews.get(path.shift() as string) as OptionsItem;
//     while (path.length) {
//       const cursor = path.shift() as string;
//       item = item.get(cursor) as OptionsItem;
//     }
//     const nodes = Array.from(item.keys()).map((key) => ({ label: key, value: key, leaf: (item.get(key) as OptionsItem).size === 0 }));
//     resolve(nodes);
//   } else {
//     resolve([{ value: "/src/views", label: "SourceViews", leaf: false }]);
//   }
// }
</script>
