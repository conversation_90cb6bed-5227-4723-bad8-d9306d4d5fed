type NativeType = undefined | null | number | string | boolean | symbol | Function;
type InferDefaults<T> = {
  [K in keyof T]?: ((props: T) => T[K] & {}) | (T[K] extends NativeType ? T[K] : never);
};
type Primitive = null | undefined | string | number | boolean | symbol | bigint;
/**
 * Check whether it is tuple
 *
 * 检查是否为元组
 *
 * @example
 * IsTuple<[1, 2, 3]> => true
 * IsTuple<Array[number]> => false
 */
type IsTuple<T extends ReadonlyArray<any>> = number extends T["length"] ? false : true;
/**
 * Array method key
 *
 * 数组方法键
 */
type ArrayMethodKey = keyof any[];
/**
 * Tuple index key
 *
 * 元组下标键
 *
 * @example
 * TupleKey<[1, 2, 3]> => '0' | '1' | '2'
 */
type TupleKey<T extends ReadonlyArray<any>> = Exclude<keyof T, ArrayMethodKey>;
/**
 * Array index key
 *
 * 数组下标键
 */
type ArrayKey = number;
/**
 * Helper type for recursively constructing paths through a type
 *
 * 用于通过一个类型递归构建路径的辅助类型
 */
type PathImpl<K extends string | number, V> = V extends Primitive ? `${K}` : `${K}` | `${K}.${Path<V>}`;
/**
 * Type which collects all paths through a type
 *
 * 通过一个类型收集所有路径的类型
 *
 * @see {@link FieldPath}
 */
type Path<T> = T extends ReadonlyArray<infer V> ? (IsTuple<T> extends true ? { [K in TupleKey<T>]-?: PathImpl<Exclude<K, symbol>, T[K]> }[TupleKey<T>] /* tuple */ : PathImpl<ArrayKey, V> /* array */) : { [K in keyof T]-?: PathImpl<Exclude<K, symbol>, T[K]> }[keyof T]; /* object */
/**
 * Type which collects all paths through a type
 *
 * 通过一个类型收集所有路径的类型
 *
 * @example
 * FieldPath<{ 1: number; a: number; b: string; c: { d: number; e: string }; f: [{ value: string }]; g: { value: string }[] }> => '1' | 'a' | 'b' | 'c' | 'f' | 'g' | 'c.d' | 'c.e' | 'f.0' | 'f.0.value' | 'g.number' | 'g.number.value'
 */
type FieldPath<T> = T extends object ? Path<T> : never;

/**
 * FormModel
 */
export interface FormModelProps {
  gutter: number;
  loading: boolean;

  /**
   * @description Control the size of components in this form.
   */
  size: "" | "default" | "small" | "large";
  /**
   * @description Whether to disable all components in this form. If set to `true`, it will override the `disabled` prop of the inner component.
   */
  disabled: boolean;
  /*  */
  /**
   * @description Data of form component.
   */
  model: object;
  /**
   * @description Validation rules of form.
   */
  rules: Partial<Record<import("vue").UnwrapRef<import("vue").MaybeRef<Record<string, any> | string>> extends string ? import("vue").UnwrapRef<import("vue").MaybeRef<Record<string, any> | string>> : FieldPath<import("vue").UnwrapRef<import("vue").MaybeRef<Record<string, any> | string>>>, import("element-plus").FormItemRule | import("element-plus").FormItemRule[]>>;
  /**
   * @description Position of label. If set to `'left'` or `'right'`, `label-width` prop is also required.
   */
  labelPosition: "left" | "right" | "top";
  /**
   * @description Position of asterisk.
   */
  requireAsteriskPosition: "left" | "right";
  /**
   * @description Width of label, e.g. `'50px'`. All its direct child form items will inherit this value. `auto` is supported.
   */
  labelWidth: string | number;
  /**
   * @description Suffix of the label.
   */
  labelSuffix: string;
  /**
   * @description Whether the form is inline.
   */
  inline: boolean;
  /**
   * @description Whether to display the error message inline with the form item.
   */
  inlineMessage: boolean;
  /**
   * @description Whether to display an icon indicating the validation result.
   */
  statusIcon: boolean;
  /**
   * @description Whether to show the error message.
   */
  showMessage: boolean;
  /**
   * @description Whether to trigger validation when the `rules` prop is changed.
   */
  validateOnRuleChange: boolean;
  /**
   * @description Whether to hide required fields should have a red asterisk (star) beside their labels.
   */
  hideRequiredAsterisk: boolean;
  /**
   * @description When validation fails, scroll to the first error form entry.
   */
  scrollToError: boolean;
  /**
   * @description When validation fails, it scrolls to the first error item based on the scrollIntoView option.
   */
  scrollIntoViewOptions: ScrollIntoViewOptions | boolean;
}
export const formModelDefaultProps: InferDefaults<Partial<Omit<FormModelProps, "model">> & Required<Pick<FormModelProps, "model">>> = {
  gutter: 16,

  labelPosition: "right",
  requireAsteriskPosition: "left",
  labelWidth: "",
  labelSuffix: "",
  inline: false,
  inlineMessage: false,
  statusIcon: false,
  showMessage: true,
  validateOnRuleChange: true,
  hideRequiredAsterisk: false,
  scrollToError: false,
  scrollIntoViewOptions: () => true,
};

/**
 * FormItem
 */
export interface FormItemProps {
  span: number;
  tooltip: string;
  /**
   * @description Label text.
   */
  label: string;
  /**
   * @description Width of label, e.g. `'50px'`. `'auto'` is supported.
   */
  labelWidth: string | number;
  /**
   * @description  A key of `model`. It could be an array of property paths (e.g `['a', 'b', 0]`). In the use of `validate` and `resetFields` method, the attribute is required.
   */
  prop: string | string[];
  /**
   * @description Whether the field is required or not, will be determined by validation rules if omitted.
   */
  required: boolean;
  /**
   * @description Validation rules of form, see the [following table](#formitemrule), more advanced usage at [async-validator](https://github.com/yiminghe/async-validator).
   */
  rules: import("element-plus").FormItemRule | import("element-plus").FormItemRule[];
  /**
   * @description Field error message, set its value and the field will validate error and show this message immediately.
   */
  error: string;
  /**
   * @description Validation state of formItem.
   */
  validateStatus: "" | "error" | "validating" | "success";
  /**
   * @description Same as for in native label.
   */
  for: string;
  /**
   * @description Inline style validate message.
   */
  inlineMessage: string | boolean;
  /**
   * @description Whether to show the error message.
   */
  showMessage: boolean;
  /**
   * @description Control the size of components in this form-item.
   */
  size: "" | "default" | "small" | "large";
}
// tooltip
// labelWidth
// required
// error
// validateStatus
// for
// inlineMessage
// showMessage
// size
export const formItemDefaultProps: InferDefaults<Partial<Omit<FormItemProps, "span">> & Required<Pick<FormItemProps, "span">>> = {
  span: 24,

  labelWidth: "",
  required: undefined,
  inlineMessage: "",
  showMessage: true,
};
/**
 * FormGroup
 */
export interface FormGroupProps {
  span: number;
  gutter?: number;
  tooltip?: string;
  /**
   * @description Label text.
   */
  label?: string;
}

export const FormGroupDefaultProps: InferDefaults<Partial<Omit<FormGroupProps, "span">> & Required<Pick<FormGroupProps, "span">>> = {
  span: 24,
  gutter: 20,
  tooltip: "",
  label: "",
};
