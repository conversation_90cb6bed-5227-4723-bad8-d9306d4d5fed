<template>
  <!-- <div class="map" @keyup.stop>
    <div class="map-label">
      <span class="map-label-name">{{ name }}</span>
      <span class="map-label-time">{{ time }}</span>
    </div>
    <div class="map-wrap">
      <el-select v-model="name" :placeholder="$t('glob.Please select field', { field: '所在时区' })" filterable default-first-option class="tw-w-full tw-align-top">
        <el-option v-for="item in zone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"></el-option>
      </el-select>
      <div class="map-inset">
        <div class="map-tooltip">{{ hoverName }} {{ hoverTime }}</div>
        <div class="map-axis-x" :style="{ left: `${(hoverAxisX || axisX) * 100}%` }"></div>
        <div class="map-axis-y" :style="{ top: `${(hoverAxisY || axisY) * 100}%` }"></div>
        <span v-for="data in center" :key="data.name" :title="data.name" :class="[{ active: data.name === name }]" :style="{ left: `${((180 + (data.long || 0)) / 360) * 100}%`, top: `${((90 - (data.lat || 0)) / 180) * 100}%`, cursor: 'pointer' }" @click.stop="name = data.name" @mouseenter.stop="hoverMap(data)" @mouseleave="leaveMap(data)"></span>
      </div>
    </div>
  </div> -->
  <el-select v-model="name" :placeholder="$t('glob.Please select field', { field: '所在时区' })" filterable default-first-option class="tw-w-full tw-align-top" @keyup.stop>
    <el-option v-for="item in zone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"></el-option>
  </el-select>
</template>

<script setup lang="ts">
import { onMounted, useModel, watch, ref, nextTick } from "vue";
import moment from "moment-timezone";
import timezomeMeta from "./timezone-meta.json";
import zoneData from "./zone.json";

interface CenterData {
  name: string;
  lat: number;
  long: number;
  countries: string[];
  comments: string;
}

interface Props {
  modelValue?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
});
const center = ref<CenterData[]>([]);
const zone = ref<Record<"zoneId" | "displayName", string>[]>(zoneData as unknown as Record<"zoneId" | "displayName", string>[]);

const name = useModel(props, "modelValue");
const time = ref<string>("");
const axisX = ref<number>(0);
const axisY = ref<number>(0);
const hoverName = ref<string>("");
const hoverTime = ref<string>("");
const hoverAxisX = ref<number>(0);
const hoverAxisY = ref<number>(0);

watch(name, async (newName) => {
  if (!newName) {
    await nextTick();
    const getName = moment.tz.guess();
    // if (getName) return (name.value = getName);
  }
  const zones = timezomeMeta.zones as Record<string, CenterData>;
  if (Object.prototype.hasOwnProperty.call(zones, newName)) {
    const zone = zones[newName];
    const m = moment().tz(zone.name);
    time.value = `${m.format("HH:mm ")}${m.zoneAbbr()}`;
    axisX.value = (180 + (zone.long || 0)) / 360;
    axisY.value = (90 - (zone.lat || 0)) / 180;
  }
});

onMounted(() => {
  // if (!name.value) name.value = moment.tz.guess();
  const zones = timezomeMeta.zones as Record<string, CenterData>;
  for (const key in zones) {
    if (Object.prototype.hasOwnProperty.call(zones, key)) {
      center.value.push(zones[key]);
    }
  }
});

function hoverMap(data: CenterData) {
  const m = moment().tz(data.name);
  hoverName.value = data.name;
  hoverTime.value = `${m.format("HH:mm ")}${m.zoneAbbr()}`;
  hoverAxisX.value = (180 + (data.long || 0)) / 360;
  hoverAxisY.value = (90 - (data.lat || 0)) / 180;
}
function leaveMap(data: CenterData) {
  if (data.name === hoverName.value) {
    hoverName.value = "";
    hoverTime.value = "";
    hoverAxisX.value = 0;
    hoverAxisY.value = 0;
  }
}

// function rebuildGuess() {
//   // use Intl API when available and returning valid time zone
//   try {
//     var intlName = Intl.DateTimeFormat().resolvedOptions().timeZone;
//     if (intlName && intlName.length > 3) {
//       var name = names[(intlName || "").toLowerCase().replace(/\//g, "_")];
//       if (name) {
//         return name;
//       }
//       logError("Moment Timezone found " + intlName + " from the Intl api, but did not have that data loaded.");
//     }
//   } catch (e) {
//     // Intl unavailable, fall back to manual guessing.
//   }

//   var offsets = userOffsets(),
//     offsetsLength = offsets.length,
//     guesses = guessesForUserOffsets(offsets),
//     zoneScores = [],
//     zoneScore,
//     i,
//     j;

//   for (i = 0; i < guesses.length; i++) {
//     zoneScore = new ZoneScore(getZone(guesses[i]), offsetsLength);
//     for (j = 0; j < offsetsLength; j++) {
//       zoneScore.scoreOffsetAt(offsets[j]);
//     }
//     zoneScores.push(zoneScore);
//   }

//   zoneScores.sort(sortZoneScores);

//   return zoneScores.length > 0 ? zoneScores[0].zone.name : undefined;
// }
</script>

<style scoped lang="scss">
.map {
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  width: 100%;
  max-width: 1024px;

  &-wrap {
    background: var(--el-color-primary-light-7) url("~@/assets/worldMapBackground.png");
    padding: 1px;
    border-radius: 3px;
    position: relative;
  }
  &-label {
    font-size: 18px;
    margin: 0;
    background-color: var(--el-color-primary-light-5);
    color: var(--el-text-color-primary);
    font-family: monospace;
    white-space: nowrap;
    span {
      display: block;
    }
    .map-label-name {
      float: left;
      width: 50%;
      padding-right: 10px;
      text-align: right;
    }
    .map-label-time {
      margin-left: 50%;
      padding-left: 10px;
      text-align: left;
    }
  }

  &-inset {
    padding-bottom: 50%;
    background: url("~@/assets//worldMap.png") 50% 50%;
    background-size: cover;
    position: relative;
    border-radius: 2px;

    span {
      width: 6px;
      height: 6px;
      margin: -3px 0 0 -3px;
      background: #fff;
      position: absolute;
      border-radius: 3px;
      border: 1px solid var(--el-color-primary-light-3);

      &.active {
        background: var(--el-color-primary);
      }
    }

    .map-axis {
      &-x {
        position: absolute;
        top: 0;
        bottom: 0;
        border-left: 1px solid var(--el-color-primary);
        width: 0;
        transition: left 50ms ease-out;
      }
      &-y {
        position: absolute;
        left: 0;
        right: 0;
        border-top: 1px solid var(--el-color-primary);
        height: 0;
        transition: top 50ms ease-out;
      }
    }
  }
  &-tooltip {
    position: absolute;
    padding: 0 0.5em;
    font-size: 12px;
    line-height: 2em;
    background-color: var(--el-color-primary-light-5);
  }
}
</style>
