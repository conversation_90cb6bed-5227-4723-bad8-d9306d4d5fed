import { defineAsyncComponent, defineComponent, ref, toValue, nextTick, h, onMounted, onBeforeUnmount, type CSSProperties } from "vue";
import { ElResult, ElScrollbar, ElButton, ElIcon, ElText, ElMessageBox } from "element-plus";
import { useStyleTag } from "@vueuse/core";
import Loading from "./loading.vue";

export default function showSecurityTree(raw: { resourceId: string; resourceName: string }) {
  const $pre_error = { message: "" };
  const $message = defineAsyncComponent({
    delay: 0,
    onError: (error, retry, fail, attempts) => (($pre_error.message = error.message), (attempts <= 0 ? retry : fail)()),
    loadingComponent: defineComponent(() => () => h("div", { style: { "height": "280px", "--loading-size": "40px", "--loading-color": "var(--el-color-primary)" } satisfies CSSProperties }, h(Loading))),
    errorComponent: defineComponent(() => () => h(<PERSON><PERSON><PERSON><PERSON>, { icon: "error", title: "连接到资源" }, { "sub-title": () => h("div", { style: { display: "flex", flexDirection: "column" } satisfies CSSProperties }, [h(ElText, { type: "info" }, () => "加载失败"), h(ElText, { type: "danger" }, () => $pre_error.message || "未知错误")]) })),
    loader: async () => {
      // if (!raw.containerId) return await Promise.reject(Object.assign(new Error("没有资源ID"), { success: false, data: { resourceId: raw.resourceId } }));
      // const { success, message, data } = await api({ resourceId: raw.resourceId });
      // if (!success) return await Promise.reject(Object.assign(new Error(message), { success, data }));

      await nextTick();

      return defineComponent(() => {
        const { load, unload } = useStyleTag(``, { id: "security-container", immediate: false });
        onMounted(load);
        onBeforeUnmount(unload);

        return () => {
          return h(ElScrollbar, { height: 280 }, () => {
            return h("div");
          });
        };
      });
    },
  });
  return ElMessageBox.alert(h($message), `连接到${raw.resourceName ? `"${raw.resourceName}"` : "资源"}`, { confirmButtonText: "取消", draggable: true, closeOnClickModal: true, closeOnPressEscape: true, closeOnHashChange: true }).catch((error) => Promise.resolve(error));
}
