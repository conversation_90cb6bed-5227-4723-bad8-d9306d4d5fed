<script lang="ts">
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { createVNode, defineComponent, computed, CSSProperties, renderSlot, h } from "vue";
import * as Icons from "@element-plus/icons-vue";
import { ElIcon } from "element-plus";
import svg from "@/components/icon/svg/index.vue";
// import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
// import { fas } from "@fortawesome/free-solid-svg-icons";
// import { far } from "@fortawesome/free-regular-svg-icons";
// import { fab } from "@fortawesome/free-brands-svg-icons";

export default defineComponent({
  name: "Icon",
  props: {
    name: {
      type: String,
      required: false,
    },
    size: {
      type: String,
      default: "18px",
    },
    color: {
      type: String,
      default: "#000000",
    },
  },
  setup(props, ctx) {
    const iconStyle = computed((): CSSProperties => {
      const { size, color } = props;
      let s = `${size.replace("px", "")}px`;
      return {
        display: "inline-block",
        fontSize: s,
        color: color,
      };
    });

    return () => {
      if (typeof props.name !== "string") {
        return h("i", { name: props.name, size: props.size, color: props.color }, renderSlot(ctx.slots, "default", { name: props.name, size: props.size, color: props.color }));
      } else if (props.name.indexOf("el-icon-") === 0) {
        const name = props.name.split("-").pop() as string;
        return createVNode(ElIcon, { class: "icon el-icon", style: iconStyle.value }, { default: () => createVNode(Icons[name in Icons ? name : "QuestionFilled"]) });
      } else if (props.name.indexOf("local-") === 0 || /^(https?|ftp|mailto|tel):/.test(props.name)) {
        return createVNode(svg, { name: props.name, size: props.size, color: props.color });
      } /*  else if (props.name.indexOf("fas-") === 0) {
        const key = props.name.replace(/^fas-(.*)$/g, "$1");
        if (Object.prototype.hasOwnProperty.call(fas, key)) return createVNode(FontAwesomeIcon, { icon: fas[key], class: ["icon"], style: iconStyle.value });
      } else if (props.name.indexOf("far-") === 0) {
        const key = props.name.replace(/^far-(.*)$/g, "$1");
        if (Object.prototype.hasOwnProperty.call(far, key)) return createVNode(FontAwesomeIcon, { icon: far[key], class: ["icon"], style: iconStyle.value });
      } else if (props.name.indexOf("fab-") === 0) {
        const key = props.name.replace(/^fab-(.*)$/g, "$1");
        if (Object.prototype.hasOwnProperty.call(fab, key)) return createVNode(FontAwesomeIcon, { icon: fab[key], class: ["icon"], style: iconStyle.value });
      }  */ else return createVNode("i", { class: [props.name, "icon"], style: iconStyle.value });
    };
  },
});
</script>
