<template>
  <div class="tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center">
    <template v-if="type === TypeHelper.Plane">
      <div class="loading-plane"></div>
    </template>
    <template v-else-if="type === TypeHelper.Chase">
      <div class="loading-chase">
        <div v-for="i in 6" :key="`loading-chase-dot_${i}`" class="loading-chase-dot"></div>
      </div>
    </template>
    <template v-else-if="type === TypeHelper.Bounce">
      <div class="loading-bounce">
        <div v-for="i in 2" :key="`loading-bounce-dot_${i}`" class="loading-bounce-dot"></div>
      </div>
    </template>
    <template v-else-if="type === TypeHelper.Wave">
      <div class="loading-wave">
        <div v-for="i in 5" :key="`loading-wave-rect_${i}`" class="loading-wave-rect"></div>
      </div>
    </template>
    <template v-else-if="type === TypeHelper.Pulse">
      <div v-for="i in 1" :key="`loading-pulse_${i}`" class="loading-pulse"></div>
    </template>
    <template v-else-if="type === TypeHelper.Flow">
      <div class="loading-flow">
        <div v-for="i in 3" :key="`loading-flow-dot_${i}`" class="loading-flow-dot"></div>
      </div>
    </template>
    <template v-else-if="type === TypeHelper.Swing">
      <div class="loading-swing">
        <div v-for="i in 2" :key="`loading-swing-dot_${i}`" class="loading-swing-dot"></div>
      </div>
    </template>
    <template v-else-if="type === TypeHelper.Circle">
      <div class="loading-circle">
        <div v-for="i in 12" :key="`loading-circle-dot_${i}`" class="loading-circle-dot"></div>
      </div>
    </template>
    <template v-else-if="type === TypeHelper.CircleFade">
      <div class="loading-circle-fade">
        <div v-for="i in 12" :key="`loading-circle-fade-dot_${i}`" class="loading-circle-fade-dot"></div>
      </div>
    </template>
    <template v-else-if="type === TypeHelper.Grid">
      <div class="loading-grid">
        <div v-for="i in 9" :key="`loading-grid-cube_${i}`" class="loading-grid-cube"></div>
      </div>
    </template>
    <template v-else-if="type === TypeHelper.Fold">
      <div class="loading-fold">
        <div v-for="i in 4" :key="`loading-fold-cube_${i}`" class="loading-fold-cube"></div>
      </div>
    </template>
    <template v-else-if="type === TypeHelper.Wander">
      <div class="loading-wander">
        <div v-for="i in 4" :key="`loading-wander-cube_${i}`" class="loading-wander-cube"></div>
      </div>
    </template>
  </div>
  <!-- <div class="block-loading tw-absolute" :style="{ width: 'calc(100% - (var(--el-main-padding) * 2))', height: 'calc(100% - (var(--el-main-padding) * 2))' }">
    <div class="block-loading-box">
      <div class="block-loading-box-warp">
        <div v-for="i in 9" :key="`block-loading-box-item_${i}`" class="block-loading-box-item"></div>
      </div>
    </div>
  </div> -->
</template>

<script lang="ts" setup>
import { ref } from "vue";
defineOptions({ name: "LayoutLoading" });
enum TypeHelper {
  Plane,
  Chase,
  Bounce,
  Wave,
  Pulse,
  Flow,
  Swing,
  Circle,
  CircleFade,
  Grid,
  Fold,
  Wander,
}
const type = ref<TypeHelper>(TypeHelper.Chase);
</script>

<style lang="scss" scoped>
/* Config */
@at-root {
  :root {
    --loading-size: 40px;
    --loading-color: var(--el-color-primary);
  }
}

/* Utility class for centering */
.loading-center {
  margin: auto;
}
</style>
<style lang="scss" scoped>
/*  Plane

      <div class="loading-plane"></div>
 */
.loading-plane {
  width: var(--loading-size);
  height: var(--loading-size);
  background-color: var(--loading-color);
  animation: loading-plane 1.2s infinite ease-in-out;
}

@keyframes loading-plane {
  0% {
    transform: perspective(120px) rotateX(0deg) rotateY(0deg);
  }
  50% {
    transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);
  }
  100% {
    transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
  }
}

/*  Chase

      <div class="loading-chase">
        <div class="loading-chase-dot"></div>
        <div class="loading-chase-dot"></div>
        <div class="loading-chase-dot"></div>
        <div class="loading-chase-dot"></div>
        <div class="loading-chase-dot"></div>
        <div class="loading-chase-dot"></div>
      </div>
 */
.loading-chase {
  width: var(--loading-size);
  height: var(--loading-size);
  position: relative;
  animation: loading-chase 2.5s infinite linear both;

  &-dot {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    animation: loading-chase-dot 2s infinite ease-in-out both;
    &:before {
      content: "";
      display: block;
      width: 25%;
      height: 25%;
      background-color: var(--loading-color);
      border-radius: 100%;
      animation: loading-chase-dot-before 2s infinite ease-in-out both;
    }
    &:nth-child(1) {
      animation-delay: -1.1s;
      &:before {
        animation-delay: -1.1s;
      }
    }
    &:nth-child(2) {
      animation-delay: -1s;
      &:before {
        animation-delay: -1s;
      }
    }
    &:nth-child(3) {
      animation-delay: -0.9s;
      &:before {
        animation-delay: -0.9s;
      }
    }
    &:nth-child(4) {
      animation-delay: -0.8s;
      &:before {
        animation-delay: -0.8s;
      }
    }
    &:nth-child(5) {
      animation-delay: -0.7s;
      &:before {
        animation-delay: -0.7s;
      }
    }
    &:nth-child(6) {
      animation-delay: -0.6s;
      &:before {
        animation-delay: -0.6s;
      }
    }
  }
}

@keyframes loading-chase {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-chase-dot {
  80%,
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-chase-dot-before {
  50% {
    transform: scale(0.4);
  }
  100%,
  0% {
    transform: scale(1);
  }
}

/*  Bounce

      <div class="loading-bounce">
        <div class="loading-bounce-dot"></div>
        <div class="loading-bounce-dot"></div>
      </div>
  */
.loading-bounce {
  width: var(--loading-size);
  height: var(--loading-size);
  position: relative;
  &-dot {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: var(--loading-color);
    opacity: 0.6;
    position: absolute;
    top: 0;
    left: 0;
    animation: loading-bounce 2s infinite cubic-bezier(0.455, 0.03, 0.515, 0.955);
    &:nth-child(2) {
      animation-delay: -1s;
    }
  }
}

@keyframes loading-bounce {
  0%,
  100% {
    transform: scale(0);
  }
  45%,
  55% {
    transform: scale(1);
  }
}

/*  Wave

      <div class="loading-wave">
        <div class="loading-wave-rect"></div>
        <div class="loading-wave-rect"></div>
        <div class="loading-wave-rect"></div>
        <div class="loading-wave-rect"></div>
        <div class="loading-wave-rect"></div>
      </div>
 */
.loading-wave {
  width: var(--loading-size);
  height: var(--loading-size);
  display: flex;
  justify-content: space-between;
  &-rect {
    background-color: var(--loading-color);
    height: 100%;
    width: 15%;
    animation: loading-wave 1.2s infinite ease-in-out;
    &:nth-child(1) {
      animation-delay: -1.2s;
    }
    &:nth-child(2) {
      animation-delay: -1.1s;
    }
    &:nth-child(3) {
      animation-delay: -1s;
    }
    &:nth-child(4) {
      animation-delay: -0.9s;
    }
    &:nth-child(5) {
      animation-delay: -0.8s;
    }
  }
}

@keyframes loading-wave {
  0%,
  40%,
  100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

/*  Pulse

      <div class="loading-pulse"></div>
 */
.loading-pulse {
  width: var(--loading-size);
  height: var(--loading-size);
  background-color: var(--loading-color);
  border-radius: 100%;
  animation: loading-pulse 1.2s infinite cubic-bezier(0.455, 0.03, 0.515, 0.955);
}

@keyframes loading-pulse {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/*  Flow

      <div class="loading-flow">
        <div class="loading-flow-dot"></div>
        <div class="loading-flow-dot"></div>
        <div class="loading-flow-dot"></div>
      </div>
 */
.loading-flow {
  width: calc(var(--loading-size) * 1.3);
  height: calc(var(--loading-size) * 1.3);
  display: flex;
  justify-content: space-between;
  &-dot {
    width: 25%;
    height: 25%;
    background-color: var(--loading-color);
    border-radius: 50%;
    animation: loading-flow 1.4s cubic-bezier(0.455, 0.03, 0.515, 0.955) 0s infinite both;
  }
  &:nth-child(1) {
    animation-delay: -0.3s;
  }
  &:nth-child(2) {
    animation-delay: -0.15s;
  }
}

@keyframes loading-flow {
  0%,
  80%,
  100% {
    transform: scale(0.3);
  }
  40% {
    transform: scale(1);
  }
}

/*  Swing

      <div class="loading-swing">
        <div class="loading-swing-dot"></div>
        <div class="loading-swing-dot"></div>
      </div>
 */
.loading-swing {
  width: var(--loading-size);
  height: var(--loading-size);
  position: relative;
  animation: loading-swing 1.8s infinite linear;
  &-dot {
    width: 45%;
    height: 45%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    margin: auto;
    background-color: var(--loading-color);
    border-radius: 100%;
    animation: loading-swing-dot 2s infinite ease-in-out;
  }
  &:nth-child(2) {
    top: auto;
    bottom: 0;
    animation-delay: -1s;
  }
}

@keyframes loading-swing {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-swing-dot {
  0%,
  100% {
    transform: scale(0.2);
  }
  50% {
    transform: scale(1);
  }
}

/*  Circle

      <div class="loading-circle">
        <div class="loading-circle-dot"></div>
        <div class="loading-circle-dot"></div>
        <div class="loading-circle-dot"></div>
        <div class="loading-circle-dot"></div>
        <div class="loading-circle-dot"></div>
        <div class="loading-circle-dot"></div>
        <div class="loading-circle-dot"></div>
        <div class="loading-circle-dot"></div>
        <div class="loading-circle-dot"></div>
        <div class="loading-circle-dot"></div>
        <div class="loading-circle-dot"></div>
        <div class="loading-circle-dot"></div>
      </div>
 */
.loading-circle {
  width: var(--loading-size);
  height: var(--loading-size);
  position: relative;
  &-dot {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    &:before {
      content: "";
      display: block;
      width: 15%;
      height: 15%;
      background-color: var(--loading-color);
      border-radius: 100%;
      animation: loading-circle 1.2s infinite ease-in-out both;
    }
    &:nth-child(1) {
      transform: rotate(30deg);
      &:before {
        animation-delay: -1.1s;
      }
    }
    &:nth-child(2) {
      transform: rotate(60deg);
      &:before {
        animation-delay: -1s;
      }
    }
    &:nth-child(3) {
      transform: rotate(90deg);
      &:before {
        animation-delay: -0.9s;
      }
    }
    &:nth-child(4) {
      transform: rotate(120deg);
      &:before {
        animation-delay: -0.8s;
      }
    }
    &:nth-child(5) {
      transform: rotate(150deg);
      &:before {
        animation-delay: -0.7s;
      }
    }
    &:nth-child(6) {
      transform: rotate(180deg);
      &:before {
        animation-delay: -0.6s;
      }
    }
    &:nth-child(7) {
      transform: rotate(210deg);
      &:before {
        animation-delay: -0.5s;
      }
    }
    &:nth-child(8) {
      transform: rotate(240deg);
      &:before {
        animation-delay: -0.4s;
      }
    }
    &:nth-child(9) {
      transform: rotate(270deg);
      &:before {
        animation-delay: -0.3s;
      }
    }
    &:nth-child(10) {
      transform: rotate(300deg);
      &:before {
        animation-delay: -0.2s;
      }
    }
    &:nth-child(11) {
      transform: rotate(330deg);
      &:before {
        animation-delay: -0.1s;
      }
    }
  }
}

@keyframes loading-circle {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/*  Circle Fade

      <div class="loading-circle-fade">
        <div class="loading-circle-fade-dot"></div>
        <div class="loading-circle-fade-dot"></div>
        <div class="loading-circle-fade-dot"></div>
        <div class="loading-circle-fade-dot"></div>
        <div class="loading-circle-fade-dot"></div>
        <div class="loading-circle-fade-dot"></div>
        <div class="loading-circle-fade-dot"></div>
        <div class="loading-circle-fade-dot"></div>
        <div class="loading-circle-fade-dot"></div>
        <div class="loading-circle-fade-dot"></div>
        <div class="loading-circle-fade-dot"></div>
        <div class="loading-circle-fade-dot"></div>
      </div>
 */
.loading-circle {
  &-fade {
    width: var(--loading-size);
    height: var(--loading-size);
    position: relative;
    &-dot {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      &:before {
        content: "";
        display: block;
        width: 15%;
        height: 15%;
        background-color: var(--loading-color);
        border-radius: 100%;
        animation: loading-circle-fade 1.2s infinite ease-in-out both;
      }
      &:nth-child(1) {
        transform: rotate(30deg);
        &::before {
          animation-delay: -1.1s;
        }
      }
      &:nth-child(2) {
        transform: rotate(60deg);
        &::before {
          animation-delay: -1s;
        }
      }
      &:nth-child(3) {
        transform: rotate(90deg);
        &::before {
          animation-delay: -0.9s;
        }
      }
      &:nth-child(4) {
        transform: rotate(120deg);
        &::before {
          animation-delay: -0.8s;
        }
      }
      &:nth-child(5) {
        transform: rotate(150deg);
        &::before {
          animation-delay: -0.7s;
        }
      }
      &:nth-child(6) {
        transform: rotate(180deg);
        &::before {
          animation-delay: -0.6s;
        }
      }
      &:nth-child(7) {
        transform: rotate(210deg);
        &::before {
          animation-delay: -0.5s;
        }
      }
      &:nth-child(8) {
        transform: rotate(240deg);
        &::before {
          animation-delay: -0.4s;
        }
      }
      &:nth-child(9) {
        transform: rotate(270deg);
        &::before {
          animation-delay: -0.3s;
        }
      }
      &:nth-child(10) {
        transform: rotate(300deg);
        &::before {
          animation-delay: -0.2s;
        }
      }
      &:nth-child(11) {
        transform: rotate(330deg);
        &::before {
          animation-delay: -0.1s;
        }
      }
    }
  }
}

@keyframes loading-circle-fade {
  0%,
  39%,
  100% {
    opacity: 0;
    transform: scale(0.6);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
}

/*  Grid

    <div class="loading-grid">
      <div class="loading-grid-cube"></div>
      <div class="loading-grid-cube"></div>
      <div class="loading-grid-cube"></div>
      <div class="loading-grid-cube"></div>
      <div class="loading-grid-cube"></div>
      <div class="loading-grid-cube"></div>
      <div class="loading-grid-cube"></div>
      <div class="loading-grid-cube"></div>
      <div class="loading-grid-cube"></div>
    </div>
 */
.loading-grid {
  width: var(--loading-size);
  height: var(--loading-size);
  /* Cube positions
   * 1 2 3
   * 4 5 6
   * 7 8 9
   */
  &-cube {
    width: 33.33%;
    height: 33.33%;
    background-color: var(--loading-color);
    float: left;
    animation: loading-grid 1.3s infinite ease-in-out;
    &:nth-child(1) {
      animation-delay: 0.2s;
    }
    &:nth-child(2) {
      animation-delay: 0.3s;
    }
    &:nth-child(3) {
      animation-delay: 0.4s;
    }
    &:nth-child(4) {
      animation-delay: 0.1s;
    }
    &:nth-child(5) {
      animation-delay: 0.2s;
    }
    &:nth-child(6) {
      animation-delay: 0.3s;
    }
    &:nth-child(7) {
      animation-delay: 0s;
    }
    &:nth-child(8) {
      animation-delay: 0.1s;
    }
    &:nth-child(9) {
      animation-delay: 0.2s;
    }
  }
}

@keyframes loading-grid {
  0%,
  70%,
  100% {
    transform: scale3D(1, 1, 1);
  }
  35% {
    transform: scale3D(0, 0, 1);
  }
}

/*  Fold

      <div class="loading-fold">
        <div class="loading-fold-cube"></div>
        <div class="loading-fold-cube"></div>
        <div class="loading-fold-cube"></div>
        <div class="loading-fold-cube"></div>
      </div>
 */
.loading-fold {
  width: var(--loading-size);
  height: var(--loading-size);
  position: relative;
  transform: rotateZ(45deg);
  &-cube {
    float: left;
    width: 50%;
    height: 50%;
    position: relative;
    transform: scale(1.1);
    &:before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: var(--loading-color);
      animation: loading-fold 2.4s infinite linear both;
      transform-origin: 100% 100%;
    }
    &:nth-child(2) {
      transform: scale(1.1) rotateZ(90deg);
      &:before {
        animation-delay: 0.3s;
      }
    }
    &:nth-child(4) {
      transform: scale(1.1) rotateZ(180deg);
      &:before {
        animation-delay: 0.6s;
      }
    }
    &:nth-child(3) {
      transform: scale(1.1) rotateZ(270deg);
      &:before {
        animation-delay: 0.9s;
      }
    }
  }
}

@keyframes loading-fold {
  0%,
  10% {
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%,
  75% {
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%,
  100% {
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}

/*  Wander

      <div class="loading-wander">
        <div class="loading-wander-cube"></div>
        <div class="loading-wander-cube"></div>
        <div class="loading-wander-cube"></div>
        <div class="loading-wander-cube"></div>
      </div>
 */
.loading-wander {
  width: var(--loading-size);
  height: var(--loading-size);
  position: relative;
  &-cube {
    background-color: var(--loading-color);
    width: 20%;
    height: 20%;
    position: absolute;
    top: 0;
    left: 0;
    --loading-wander-distance: calc(var(--loading-size) * 0.75);
    animation: loading-wander 2s ease-in-out -2s infinite both;
    &:nth-child(2) {
      animation-delay: -0.5s;
    }
    &:nth-child(3) {
      animation-delay: -1s;
    }
  }
}

@keyframes loading-wander {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: translateX(var(--loading-wander-distance)) rotate(-90deg) scale(0.6);
  }
  50% {
    transform: translateX(var(--loading-wander-distance)) translateY(var(--loading-wander-distance)) rotate(-179deg);
  }
  50.1% {
    transform: translateX(var(--loading-wander-distance)) translateY(var(--loading-wander-distance)) rotate(-180deg);
  }
  75% {
    transform: translateX(0) translateY(var(--loading-wander-distance)) rotate(-270deg) scale(0.6);
  }
  100% {
    transform: rotate(-360deg);
  }
}
</style>
