<template>
  <!--  tw-outline-dashed tw-outline-1 -->
  <draggable class="source-tree tw-min-h-[24px]" tag="ul" :component-data="{ type: 'MENU' }" v-model="value"
    handle=".move-item" :sort="false" :group="{ name: 'group-item', pull: 'clone', put: true }" :move="handleMove"
    @start="() => (moveLiading = true)" @end.stop="() => (emits('end'), (moveLiading = false))" item-key="id">
    <template #item="{ element, index }">
      <li class="tree-item tw-pl-[18px]" :class="[{ first: index === 0, final: index === value.length - 1 }]">
        <div class="move-item tw-ml-[-8px] tw-flex tw-leading-[24px]" draggable="true">
          <div class="tw-mr-[6px] tw-flex tw-flex-shrink-0 tw-items-center">
            <Icon :name="element.icon" size="18px" color="inherit"></Icon>
          </div>
          <div class="tw-overflow-hidden tw-text-ellipsis tw-whitespace-nowrap">
            <span class="tw-mr-[6px] tw-overflow-hidden tw-text-ellipsis tw-whitespace-nowrap">{{ element.title }}</span>
            <span class="tw-overflow-hidden tw-text-ellipsis tw-whitespace-nowrap"
              style="color: var(--el-text-color-disabled)">{{ element.name }}</span>
          </div>
        </div>
        <div v-show="!moveLiading" v-if="element.children instanceof Array && element.children.length" class="subset">
          <NestedDraggable v-model="element.children"></NestedDraggable>
        </div>
      </li>
    </template>
  </draggable>
</template>
<script lang="ts" setup name="NestedDraggable">
import { computed, ref } from "vue";
import draggable from "vuedraggable";
import NestedDraggable from "./index.vue";

interface Props {
  modelValue: any[];
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
});

const moveLiading = ref(false);

const emits = defineEmits<{
  (e: "update:modelValue", item: Props["modelValue"]): void;
  (e: "update:model-value", item: Props["modelValue"]): void;
  (e: "end"): void;
}>();

const value = computed<any[]>({
  get() {
    return props.modelValue;
  },
  set(v) {
    emits("update:model-value", v);
    emits("update:modelValue", v);
  },
});
function handleMove({ to, relatedContext }: import("sortablejs").MoveEvent & { draggedContext: any; relatedContext: any }) {
  return relatedContext.element ? !Array.from(to.classList).includes("source-tree") : true;
}
</script>
<style lang="scss" scoped>
.move-item {
  cursor: move;
}

.tree-item {
  position: relative;

  &::before,
  &::after {
    content: "";
    display: block;
    position: absolute;
    top: 12px;
    left: 0px;
  }

  &::before {
    width: 12px;
    height: 100%;
  }

  &:not(.final, .first) {
    border-left: var(--el-border);

    &::before {
      left: 0px;
      border-top: var(--el-border);
    }
  }

  &.first {
    &:not(.final) {
      &::before {
        border-left: var(--el-border);
      }
    }

    &::before {
      top: 12px;
      border-top: var(--el-border);
    }

    &::after {
      top: 0;
      left: -5px;
      width: 6px;
      height: 100%;
      border-top: var(--el-border);
      border-right: var(--el-border);
    }
  }

  &.final {
    &::before {
      top: 12px;
      height: calc(100% - 12px);
      border-top: var(--el-border);
    }

    &::after {
      top: 0;
      height: calc(100% - 12px);
    }

    &:not(.first) {
      &::after {
        border-left: var(--el-border);
      }
    }
  }
}

.move-process {
  background-color: var(--el-color-primary-light-7);
}
</style>
