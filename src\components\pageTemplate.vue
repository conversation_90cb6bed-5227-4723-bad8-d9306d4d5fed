<template>
  <el-row>
    <el-col class="tw-flex" :span="24">
      <div ref="leftHeaderRef" class="header_left" :style="{ width: `calc(100% - ${centerHeaderRef.clientWidth + 20}px - ${rightHeaderRef.clientWidth + 20}px)` }">
        <el-scrollbar :view-style="{ whiteSpace: 'nowrap' }"><slot name="left"></slot></el-scrollbar>
      </div>
      <div ref="centerHeaderRef" class="header_center">
        <el-scrollbar :view-style="{ whiteSpace: 'nowrap' }"><slot name="center"></slot></el-scrollbar>
      </div>
      <div ref="rightHeaderRef" class="header_right">
        <slot name="right"></slot>
      </div>
    </el-col>
    <el-col :span="24">
      <div :style="{ height: `${height - (props.showPaging ? 100 : 50)}px`, marginBottom: props.showPaging ? '50px' : '0' }">
        <slot name="default" :height="height - (props.showPaging ? 100 : 50)"></slot>
      </div>
    </el-col>
    <el-col :span="24" v-if="props.showPaging" :style="{ marginTop: '-50px', height: '50px', paddingTop: '18px' }">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="props.total" :page-sizes="sizes" layout="->, total, sizes, prev, pager, next, jumper" @size-change="(size) => emits('size-change', size)" @current-change="(current) => emits('current-change', current)"> </el-pagination>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import { useModel, ref } from "vue";
import { sizes } from "@/utils/common";

interface Props {
  height: number;
  showPaging?: boolean;
  currentPage?: number;
  pageSize?: number;
  total?: number;
}
const props = withDefaults(defineProps<Props>(), { showPaging: true, currentPage: 1, pageSize: 10, total: 0 });

const currentPage = useModel(props, "currentPage");
const pageSize = useModel(props, "pageSize");

const leftHeaderRef = ref<HTMLDivElement>(document.createElement("div"));
const centerHeaderRef = ref<HTMLDivElement>(document.createElement("div"));
const rightHeaderRef = ref<HTMLDivElement>(document.createElement("div"));

interface Emits {
  ($event: "current-change", size: number): any;
  ($event: "size-change", current: number): any;
  // ($event: "handleCurrentPageChange", size: number): any;
  // ($event: "handleSizeChange", current: number): any;
  ($event: "update:currentPage", size: number): any;
  ($event: "update:pageSize", current: number): any;
}
const emits = defineEmits<Emits>();

interface Slots {
  left(props: {}): any;
  center(props: {}): any;
  right(props: {}): any;
  default(props: { height: number }): any;
}
defineSlots<Slots>();
</script>

<style lang="scss" scoped>
.header_left {
  height: 50px;
  flex-shrink: 1;
  margin-right: auto;
}
.header_center {
  height: 50px;
  width: fit-content;
  flex-shrink: 1;
  margin-left: auto;
  margin-right: auto;
}
.header_right {
  height: 50px;
  width: fit-content;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  flex-shrink: 0;
  margin-left: auto;
}
</style>
