import { defineAsyncComponent, defineComponent, ref, toValue, nextTick, h, onMounted, onBeforeUnmount, type CSSProperties } from "vue";
import { ElResult, ElScrollbar, ElTree, ElButton, ElIcon, ElText, ElMessageBox } from "element-plus";
import SvgIconMinus from "@/assets/minus.vue";
import SvgIconPlus from "@/assets/plus.vue";
import { getSecurityContainerById } from "@/api/system";
import { useStyleTag } from "@vueuse/core";
import Loading from "./loading.vue";
import { i18n } from "@/lang/index";
export default function showSecurityTree(raw: { containerId: string }) {
  const $pre_error = { message: "" };
  const $message = defineAsyncComponent({
    delay: 0,
    onError: (error, retry, fail, attempts) => (($pre_error.message = error.message), (attempts <= 0 ? retry : fail)()),
    loadingComponent: defineComponent(() => () => h("div", { style: { "height": "280px", "--loading-size": "40px", "--loading-color": "var(--el-color-primary)" } satisfies CSSProperties }, h(Loading))),
    errorComponent: defineComponent(() => () => h(ElResult, { icon: "error", title: "安全容器" }, { "sub-title": () => h("div", { style: { display: "flex", flexDirection: "column" } satisfies CSSProperties }, [h(ElText, { type: "info" }, () => "加载失败"), h(ElText, { type: "danger" }, () => $pre_error.message || "未知错误")]) })),
    loader: async () => {
      type Node = import("element-plus/es/components/tree/src/model/node").default;
      type TreeItem = ReturnType<typeof getSecurityContainerById> extends Promise<{ data: (infer T)[] }> ? T : never;

      if (!raw.containerId) return await Promise.reject(Object.assign(new Error("没有容器ID"), { success: false, data: { containerId: raw.containerId } }));
      const { success, message, data } = await getSecurityContainerById({ containerId: raw.containerId });
      if (!success) return await Promise.reject(Object.assign(new Error(message), { success, data }));

      // throw new Error("测试抛个异常---临时演示文案（正常情况下为接口返回的异常）");

      const expanded = (list: TreeItem[], key: string, path: string[] = []): string[] => {
        for (let i = 0; i < list.length; i++) {
          if (list[i].id === key) return path;
          const find = expanded(list[i].children instanceof Array ? list[i].children : [], key, path.concat(list[i].id));
          if (find.length) return find;
        }
        return [];
      };

      return defineComponent(() => {
        const { load, unload } = useStyleTag(
          `
          .size-full { width: 100%; height: 100% }
          .tree-autosize .el-tree-node__content { height: -moz-fit-content; height: fit-content; padding: 3px 0; margin: 6px 0 }
          .tree-custom >  .el-tree-node { width: fit-content }
          .tree-custom >  .el-tree-node > .el-tree-node__children::after { display: none !important }
          .tree-custom .el-tree-node { margin-left: 12px; overflow: hidden }
          .tree-custom .el-tree-node .skeleton-icon { overflow: hidden; cursor: pointer; position: absolute; z-index: 2; top: 0px; left: -24px; width: 24px; height: 100%; display: flex; justify-content: center; align-items: center }
          .tree-custom .el-tree-node .skeleton-icon > * { background-color: var(--el-fill-color-blank) }
          .tree-custom .el-tree-node .el-tree-node__expand-icon { display: none }
          .tree-custom .el-tree-node > .el-tree-node__content .skeleton-line { margin-left: 24px; position: relative }
          .tree-custom .el-tree-node > .el-tree-node__content .skeleton-line:not(.is-only)::before { content: ""; position: absolute; z-index: 1; top: -6px; left: -30px; display: block; width: 30px; pointer-events: none; height: calc(50% + 6px); border-bottom: 1px solid var(--el-text-color-placeholder) }
          .tree-custom .el-tree-node > .el-tree-node__content .skeleton-line.is-root:not(.is-only)::before { left: -12px; width: 12px }
          .tree-custom .el-tree-node.is-expanded_node > .el-tree-node__content { position: relative }
          .tree-custom .el-tree-node.is-expanded_node > .el-tree-node__content:not(:last-child)::before { content: ""; position: absolute; z-index: 1; left: 12px; bottom: -10px; display: block; width: 18px; pointer-events: none; height: calc(50% + 6px); border-left: 1px solid var(--el-text-color-placeholder) }
          .tree-custom .el-tree-node.is-expanded_node > .el-tree-node__children { position: relative; overflow: visible !important }
          .tree-custom .el-tree-node.is-expanded_node > .el-tree-node__children::before { content: ""; position: absolute; z-index: 1; top: 0px; left: 12px; display: block; width: 18px; pointer-events: none; height: calc(100% - 25px); border-left: 1px solid var(--el-text-color-placeholder) }
          .tree-custom .el-tree-node.is-expanded_node > .el-tree-node__children::after { content: ""; position: absolute; z-index: 1; top: -25px; left: 0px; display: block; width: 18px; pointer-events: none; height: 100%; border-left: 1px solid var(--el-fill-color-blank) }
          .tree-custom .el-tree-node:not(:last-child).is-expanded_node > .el-tree-node__children::after { border-left: 1px solid var(--el-text-color-placeholder) }`,
          { id: "security-container", immediate: false }
        );
        onMounted(load);
        onBeforeUnmount(unload);

        const expandedNodes = ref(new Set<string>());
        async function expandNodes(expand: boolean, node: Node) {
          if (expand) {
            expandedNodes.value.add(node.key as string);
            await nextTick();
            node.expand();
          } else {
            const unfoldChildren = async (children: Node[]) => {
              for (let i = 0; i < children.length; i++) {
                const item = children[i];
                const key = item.key as string;
                await unfoldChildren(item.childNodes);
                await nextTick();
                expandedNodes.value.delete(key);
                await nextTick();
                item.collapse();
              }
            };
            await unfoldChildren(node.childNodes);
            expandedNodes.value.delete(node.key as string);
            await nextTick();
            node.collapse();
          }
        }

        nextTick(() => {
          const $expanded = expanded(data, raw.containerId || "");
          for (let i = 0; i < $expanded.length; i++) expandedNodes.value.add($expanded[i]);
        });

        return () => {
          return h(ElScrollbar, { height: 280 }, () => {
            return h(
              ElTree,
              { data, "class": ["size-full", "tree-autosize", "tree-custom"], "props": { label: "name", children: "children", class: (data: { [key: string]: any }, node: Node) => (!node.isLeaf && toValue(expandedNodes).has((data as TreeItem).id) ? `is-expanded_node` : "") }, "nodeKey": "id", "expandOnClickNode": false, "indent": 0, "defaultExpandedKeys": [...toValue(expandedNodes)], "currentNodeKey": raw.containerId, "defaultCheckedKeys": [...toValue(expandedNodes)], "onMode-expand": ($event) => toValue(expandedNodes).add($event.id), "onNode-collapse": ($event) => toValue(expandedNodes).delete($event.id) },
              {
                default({ node, data }: { node: Node; data: TreeItem }) {
                  const icon = h(
                    "div",
                    { class: ["skeleton-icon"] },
                    h(ElIcon, { color: "var(--el-text-color-placeholder)", onClick: ($event: Event) => ($event.stopPropagation(), expandNodes(!node.expanded, node)) }, () => (node.expanded ? h(SvgIconMinus) : h(SvgIconPlus)))
                  );
                  return h("div", { class: ["skeleton-line", { "is-root": node.level === 1, "is-only": node.level === 1 && !(data.children instanceof Array ? data.children : []).length }], style: { width: "100%" } }, [node.isLeaf ? null : icon, h(ElButton, { type: raw.containerId === data.id ? "primary" : void 0 }, () => data.name)]);
                },
              }
            );
          });
        };
      });
    },
  });
  return ElMessageBox.alert(h($message), `${i18n.global.t("devicesInfo.Security Container")} `, { confirmButtonText: `${i18n.global.t("devicesInfo.OK")} `, draggable: true, closeOnClickModal: true, closeOnPressEscape: true, closeOnHashChange: true }).catch((error) => Promise.resolve(error));
}
