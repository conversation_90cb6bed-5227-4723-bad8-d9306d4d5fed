<template>
  <el-table-column v-if="props.type === 'default'" type="default" :class-name="props.className" :label-class-name="props.labelClassName" :property="props.property" :prop="props.prop" :width="props.width" :min-width="props.minWidth" :render-header="props.renderHeader" :sortable="props.sortable" :sort-method="props.sortMethod" :sort-by="props.sortBy" :resizable="props.resizable" :column-key="props.columnKey" :align="props.align" :header-align="props.headerAlign" :show-overflow-tooltip="props.showOverflowTooltip" :fixed="props.fixed" :formatter="props.formatter" :selectable="props.selectable" :reserve-selection="props.reserveSelection" :filter-method="props.filterMethod" :filtered-value="typeof props.filteredValue === 'string' ? props.filteredValue.split(',').filter((v) => v) : [props.filteredValue].filter((v) => v)" :filters="props.showFilter ? undefined : props.filters" :filter-placement="props.filterPlacement" :filter-multiple="props.filterMultiple" :index="props.index" :sort-orders="props.sortOrders">
    <template #header="{ column, $index }">
      <slot name="header" :column="column" :index="$index">
        {{ props.label }}
        <TableFilter v-if="props.showFilter" v-model:filtered-value="filteredValue" v-model:filter-opened="column.filterOpened" v-model:column-filtered-value="column.filteredValue" :filter-multiple="column.filterMultiple" :filter-placement="column.filterPlacement" :filters="props.filters" type="default" @filter-change="emits('filterChange')"></TableFilter>
      </slot>
    </template>
    <template #default="{ row, column, $index }">
      <slot name="default" :row="row" :column="column" :index="$index">
        <template v-if="typeof column.formatter === 'function'">
          <component :is="h({ setup: () => () => column.formatter(row, column, column.property ? row[column.property] : '', $index) })"></component>
        </template>
        <template v-else-if="row[column.property] === null || row[column.property] === undefined">
          <span :style="{ color: 'var(--el-text-color-disabled)', fontWeight: '600', userSelect: 'none' }">--</span>
        </template>
        <template v-else>
          {{ row[column.property] }}
        </template>
      </slot>
    </template>
  </el-table-column>

  <el-table-column v-if="props.type === 'tenantName'" type="tenantName" :class-name="props.className" :label="props.label" :label-class-name="props.labelClassName" :property="props.property" :prop="props.prop" :width="props.width" :min-width="props.minWidth" :render-header="props.renderHeader" :sortable="props.sortable" :sort-method="props.sortMethod" :sort-by="props.sortBy" :resizable="props.resizable" :column-key="props.columnKey" :align="props.align" :header-align="props.headerAlign" :show-overflow-tooltip="props.showOverflowTooltip" :fixed="props.fixed" :formatter="props.formatter" :selectable="props.selectable" :reserve-selection="props.reserveSelection" :filter-method="props.filterMethod" :filtered-value="typeof props.filteredValue === 'string' ? props.filteredValue.split(',').filter((v) => v) : [props.filteredValue].filter((v) => v)" :filters="props.showFilter ? undefined : props.filters" :filter-placement="props.filterPlacement" :filter-multiple="props.filterMultiple" :index="props.index" :sort-orders="props.sortOrders">
    <template #header="{ column, $index }">
      <slot name="header" :column="column" :index="$index">
        {{ props.label }}
        <TableFilter v-if="props.showFilter" v-model:filtered-many-value="manyFilteredValue" v-model:filter-opened="column.filterOpened" v-model:column-filtered-value="column.filteredValue" :filter-multiple="column.filterMultiple" :filter-placement="column.filterPlacement" :filters="props.filters" type="tenantName" @filter-change="emits('filterChange')"></TableFilter>
      </slot>
    </template>
    <template #default="{ row, column, $index }">
      <slot name="default" :row="row" :column="column" :index="$index">
        <template v-if="typeof column.formatter === 'function'">
          <component :is="h({ setup: () => () => column.formatter(row, column, column.property ? row[column.property] : '', $index) })"></component>
        </template>
        <template v-else-if="row[column.property] === null || row[column.property] === undefined">
          <span :style="{ color: 'var(--el-text-color-disabled)', fontWeight: '600', userSelect: 'none' }">--</span>
        </template>
        <template v-else>
          {{ row[column.property] }}
        </template>
      </slot>
    </template>
  </el-table-column>
  <el-table-column v-if="props.type === 'deviceName'" type="deviceName" :class-name="props.className" :label="props.label" :label-class-name="props.labelClassName" :property="props.property" :prop="props.prop" :width="props.width" :min-width="props.minWidth" :render-header="props.renderHeader" :sortable="props.sortable" :sort-method="props.sortMethod" :sort-by="props.sortBy" :resizable="props.resizable" :column-key="props.columnKey" :align="props.align" :header-align="props.headerAlign" :show-overflow-tooltip="props.showOverflowTooltip" :fixed="props.fixed" :formatter="props.formatter" :selectable="props.selectable" :reserve-selection="props.reserveSelection" :filter-method="props.filterMethod" :filtered-value="typeof props.filteredValue === 'string' ? props.filteredValue.split(',').filter((v) => v) : [props.filteredValue].filter((v) => v)" :filters="props.showFilter ? undefined : props.filters" :filter-placement="props.filterPlacement" :filter-multiple="props.filterMultiple" :index="props.index" :sort-orders="props.sortOrders">
    <template #header="{ column, $index }">
      <slot name="header" :column="column" :index="$index">
        {{ props.label }}
        <TableFilter v-if="props.showFilter" v-model:filtered-many-value="manyFilteredValue" v-model:filter-opened="column.filterOpened" v-model:column-filtered-value="column.filteredValue" :filter-multiple="column.filterMultiple" :filter-placement="column.filterPlacement" :filters="props.filters" type="deviceName" @filter-change="emits('filterChange')"></TableFilter>
      </slot>
    </template>
    <template #default="{ row, column, $index }">
      <slot name="default" :row="row" :column="column" :index="$index">
        <template v-if="typeof column.formatter === 'function'">
          <component :is="h({ setup: () => () => column.formatter(row, column, column.property ? row[column.property] : '', $index) })"></component>
        </template>
        <template v-else-if="row[column.property] === null || row[column.property] === undefined">
          <span :style="{ color: 'var(--el-text-color-disabled)', fontWeight: '600', userSelect: 'none' }">--</span>
        </template>
        <template v-else>
          {{ row[column.property] }}
        </template>
      </slot>
    </template>
  </el-table-column>
  <el-table-column v-if="props.type === 'deviceMessage'" type="deviceMessage" :class-name="props.className" :label="props.label" :label-class-name="props.labelClassName" :property="props.property" :prop="props.prop" :width="props.width" :min-width="props.minWidth" :render-header="props.renderHeader" :sortable="props.sortable" :sort-method="props.sortMethod" :sort-by="props.sortBy" :resizable="props.resizable" :column-key="props.columnKey" :align="props.align" :header-align="props.headerAlign" :show-overflow-tooltip="props.showOverflowTooltip" :fixed="props.fixed" :formatter="props.formatter" :selectable="props.selectable" :reserve-selection="props.reserveSelection" :filter-method="props.filterMethod" :filtered-value="typeof props.filteredValue === 'string' ? props.filteredValue.split(',').filter((v) => v) : [props.filteredValue].filter((v) => v)" :filters="props.showFilter ? undefined : props.filters" :filter-placement="props.filterPlacement" :filter-multiple="props.filterMultiple" :index="props.index" :sort-orders="props.sortOrders">
    <template #header="{ column, $index }">
      <slot name="header" :column="column" :index="$index">
        {{ props.label }}
        <TableFilter v-if="props.showFilter" v-model:filtered-many-value="manyFilteredValue" v-model:filter-opened="column.filterOpened" v-model:column-filtered-value="column.filteredValue" :filter-multiple="column.filterMultiple" :filter-placement="column.filterPlacement" :filters="props.filters" type="deviceMessage" @filter-change="emits('filterChange')"></TableFilter>
      </slot>
    </template>
    <template #default="{ row, column, $index }">
      <slot name="default" :row="row" :column="column" :index="$index">
        <template v-if="typeof column.formatter === 'function'">
          <component :is="h({ setup: () => () => column.formatter(row, column, column.property ? row[column.property] : '', $index) })"></component>
        </template>
        <template v-else-if="row[column.property] === null || row[column.property] === undefined">
          <span :style="{ color: 'var(--el-text-color-disabled)', fontWeight: '600', userSelect: 'none' }">--</span>
        </template>
        <template v-else>
          {{ row[column.property] }}
        </template>
      </slot>
    </template>
  </el-table-column>
  <el-table-column v-if="props.type === 'orderType'" type="orderType" :class-name="props.className" :label="props.label" :label-class-name="props.labelClassName" :property="props.property" :prop="props.prop" :width="props.width" :min-width="props.minWidth" :render-header="props.renderHeader" :sortable="props.sortable" :sort-method="props.sortMethod" :sort-by="props.sortBy" :resizable="props.resizable" :column-key="props.columnKey" :align="props.align" :header-align="props.headerAlign" :show-overflow-tooltip="props.showOverflowTooltip" :fixed="props.fixed" :formatter="props.formatter" :selectable="props.selectable" :reserve-selection="props.reserveSelection" :filter-method="props.filterMethod" :filtered-value="typeof props.filteredValue === 'string' ? props.filteredValue.split(',').filter((v) => v) : [props.filteredValue].filter((v) => v)" :filters="props.showFilter ? undefined : props.filters" :filter-placement="props.filterPlacement" :filter-multiple="props.filterMultiple" :index="props.index" :sort-orders="props.sortOrders">
    <template #header="{ column, $index }">
      <slot name="header" :column="column" :index="$index">
        {{ props.label }}
        <TableFilter v-if="props.showFilter" v-model:filtered-many-value="manyFilteredValue" v-model:filter-opened="column.filterOpened" v-model:column-filtered-value="column.filteredValue" :filter-multiple="column.filterMultiple" :filter-placement="column.filterPlacement" :filters="props.filters" type="orderType" @filter-change="emits('filterChange')"></TableFilter>
      </slot>
    </template>
    <template #default="{ row, column, $index }">
      <slot name="default" :row="row" :column="column" :index="$index">
        <template v-if="typeof column.formatter === 'function'">
          <component :is="h({ setup: () => () => column.formatter(row, column, column.property ? row[column.property] : '', $index) })"></component>
        </template>
        <template v-else-if="row[column.property] === null || row[column.property] === undefined">
          <span :style="{ color: 'var(--el-text-color-disabled)', fontWeight: '600', userSelect: 'none' }">--</span>
        </template>
        <template v-else>
          {{ row[column.property] }}
        </template>
      </slot>
    </template>
  </el-table-column>

  <el-table-column v-if="props.type === 'locationDesc'" type="locationDesc" :class-name="props.className" :label="props.label" :label-class-name="props.labelClassName" :property="props.property" :prop="props.prop" :width="props.width" :min-width="props.minWidth" :render-header="props.renderHeader" :sortable="props.sortable" :sort-method="props.sortMethod" :sort-by="props.sortBy" :resizable="props.resizable" :column-key="props.columnKey" :align="props.align" :header-align="props.headerAlign" :show-overflow-tooltip="props.showOverflowTooltip" :fixed="props.fixed" :formatter="props.formatter" :selectable="props.selectable" :reserve-selection="props.reserveSelection" :filter-method="props.filterMethod" :filtered-value="typeof props.filteredValue === 'string' ? props.filteredValue.split(',').filter((v) => v) : [props.filteredValue].filter((v) => v)" :filters="props.showFilter ? undefined : props.filters" :filter-placement="props.filterPlacement" :filter-multiple="props.filterMultiple" :index="props.index" :sort-orders="props.sortOrders">
    <template #header="{ column, $index }">
      <slot name="header" :column="column" :index="$index">
        {{ props.label }}
        <TableFilter v-if="props.showFilter" v-model:filtered-many-value="manyFilteredValue" v-model:filter-opened="column.filterOpened" v-model:column-filtered-value="column.filteredValue" :filter-multiple="column.filterMultiple" :filter-placement="column.filterPlacement" :filters="props.filters" type="locationDesc" @filter-change="emits('filterChange')"></TableFilter>
      </slot>
    </template>
    <template #default="{ row, column, $index }">
      <slot name="default" :row="row" :column="column" :index="$index">
        <template v-if="typeof column.formatter === 'function'">
          <component :is="h({ setup: () => () => column.formatter(row, column, column.property ? row[column.property] : '', $index) })"></component>
        </template>
        <template v-else-if="row[column.property] === null || row[column.property] === undefined">
          <span :style="{ color: 'var(--el-text-color-disabled)', fontWeight: '600', userSelect: 'none' }">--</span>
        </template>
        <template v-else>
          {{ row[column.property] }}
        </template>
      </slot>
    </template>
  </el-table-column>

  <el-table-column v-else-if="props.type === 'order'" type="order" :class-name="props.className" :label="props.label" :label-class-name="props.labelClassName" :property="props.property" :prop="props.prop" :width="props.width" :min-width="props.minWidth" :render-header="props.renderHeader" :sortable="props.sortable" :sort-method="props.sortMethod" :sort-by="props.sortBy" :resizable="props.resizable" :column-key="props.columnKey" :align="props.align" :header-align="props.headerAlign" :show-overflow-tooltip="props.showOverflowTooltip" :fixed="props.fixed" :formatter="props.formatter" :selectable="props.selectable" :reserve-selection="props.reserveSelection" :filter-method="props.filterMethod" :filtered-value="typeof props.filteredValue === 'string' ? props.filteredValue.split(',').filter((v) => v) : [props.filteredValue].filter((v) => v)" :filters="props.showFilter ? undefined : props.filters" :filter-placement="props.filterPlacement" :filter-multiple="props.filterMultiple" :index="props.index" :sort-orders="props.sortOrders">
    <template #header="{ column, $index }">
      <slot name="header" :column="column" :index="$index">
        {{ props.label }}
        <TableFilter v-if="props.showFilter" v-model:filtered-many-value="manyFilteredValue" v-model:filter-opened="column.filterOpened" v-model:column-filtered-value="column.filteredValue" :filter-multiple="column.filterMultiple" :filter-placement="column.filterPlacement" :filters="props.filters" type="order" @filter-change="emits('filterChange')"></TableFilter>
      </slot>
    </template>
    <template #default="{ row, column, $index }">
      <slot name="default" :row="row" :column="column" :index="$index">
        <template v-if="typeof column.formatter === 'function'">
          <component :is="h({ setup: () => () => column.formatter(row, column, column.property ? row[column.property] : '', $index) })"></component>
        </template>
        <template v-else-if="row[column.property] === null || row[column.property] === undefined">
          <span :style="{ color: 'var(--el-text-color-disabled)', fontWeight: '600', userSelect: 'none' }">--</span>
        </template>
        <template v-else>
          {{ row[column.property] }}
        </template>
      </slot>
    </template>
  </el-table-column>

  <el-table-column v-else-if="props.type === 'selection'" type="selection" :class-name="props.className" :label-class-name="props.labelClassName" :property="props.property" :prop="props.prop" :width="props.width" :min-width="props.minWidth" :render-header="props.renderHeader" :sortable="props.sortable" :sort-method="props.sortMethod" :sort-by="props.sortBy" :resizable="props.resizable" :column-key="props.columnKey" :align="props.align" :header-align="props.headerAlign" :show-overflow-tooltip="props.showOverflowTooltip" :fixed="props.fixed" :formatter="props.formatter" :selectable="props.selectable" :reserve-selection="props.reserveSelection" :filter-method="props.filterMethod" :filtered-value="typeof props.filteredValue === 'string' ? props.filteredValue.split(',').filter((v) => v) : [props.filteredValue].filter((v) => v)" :filters="props.showFilter ? undefined : props.filters" :filter-placement="props.filterPlacement" :filter-multiple="props.filterMultiple" :index="props.index" :sort-orders="props.sortOrders">
    <template #header="{ column, $index }">
      <slot name="header" :column="column" :index="$index"></slot>
    </template>
  </el-table-column>
  <el-table-column v-else-if="props.type === 'index'" type="index" :class-name="props.className" :label-class-name="props.labelClassName" :property="props.property" :prop="props.prop" :width="props.width" :min-width="props.minWidth" :render-header="props.renderHeader" :sortable="props.sortable" :sort-method="props.sortMethod" :sort-by="props.sortBy" :resizable="props.resizable" :column-key="props.columnKey" :align="props.align" :header-align="props.headerAlign" :show-overflow-tooltip="props.showOverflowTooltip" :fixed="props.fixed" :formatter="props.formatter" :selectable="props.selectable" :reserve-selection="props.reserveSelection" :filter-method="props.filterMethod" :filtered-value="typeof props.filteredValue === 'string' ? props.filteredValue.split(',').filter((v) => v) : [props.filteredValue].filter((v) => v)" :filters="props.showFilter ? undefined : props.filters" :filter-placement="props.filterPlacement" :filter-multiple="props.filterMultiple" :index="props.index" :sort-orders="props.sortOrders">
    <template #header="{ column, $index }">
      <slot name="header" :column="column" :index="$index">#</slot>
    </template>
    <template #default="{ row, column, $index }">
      <slot name="default" :row="row" :column="column" :index="$index">{{ $index }}</slot>
    </template>
  </el-table-column>
  <el-table-column v-else-if="props.type === 'expand'" type="expand" :class-name="props.className" :label-class-name="props.labelClassName" :property="props.property" :prop="props.prop" :width="props.width" :min-width="props.minWidth" :render-header="props.renderHeader" :sortable="props.sortable" :sort-method="props.sortMethod" :sort-by="props.sortBy" :resizable="props.resizable" :column-key="props.columnKey" :align="props.align" :header-align="props.headerAlign" :show-overflow-tooltip="props.showOverflowTooltip" :fixed="props.fixed" :formatter="props.formatter" :selectable="props.selectable" :reserve-selection="props.reserveSelection" :filter-method="props.filterMethod" :filtered-value="typeof props.filteredValue === 'string' ? props.filteredValue.split(',').filter((v) => v) : [props.filteredValue].filter((v) => v)" :filters="props.showFilter ? undefined : props.filters" :filter-placement="props.filterPlacement" :filter-multiple="props.filterMultiple" :index="props.index" :sort-orders="props.sortOrders">
    <template #header="{ column, $index }">
      <slot name="header" :column="column" :index="$index"></slot>
    </template>
    <template #default="{ row, column, $index, store, expanded }">
      <slot name="default" :row="row" :column="column" :store="store" :expanded="expanded" :index="$index"></slot>
    </template>
  </el-table-column>
  <!--  -->
  <!--  -->
  <!--  -->
  <el-table-column v-else-if="props.type === 'number'" type="default" :class-name="props.className" :label-class-name="props.labelClassName" :property="props.property" :prop="props.prop" :width="props.width" :min-width="props.minWidth" :render-header="props.renderHeader" :sortable="props.sortable" :sort-method="props.sortMethod" :sort-by="props.sortBy" :resizable="props.resizable" :column-key="props.columnKey" :align="props.align" :header-align="props.headerAlign" :show-overflow-tooltip="props.showOverflowTooltip" :fixed="props.fixed" :formatter="props.formatter" :selectable="props.selectable" :reserve-selection="props.reserveSelection" :filter-method="props.filterMethod" :filtered-value="typeof props.filteredValue === 'string' ? props.filteredValue.split(',').filter((v) => v) : [props.filteredValue].filter((v) => v)" :filters="props.showFilter ? undefined : props.filters" :filter-placement="props.filterPlacement" :filter-multiple="props.filterMultiple" :index="props.index" :sort-orders="props.sortOrders">
    <template #header="{ column, $index }">
      <slot name="header" :column="column" :index="$index">
        {{ props.label }}
        <TableFilter v-if="props.showFilter" v-model:filtered-value="filteredValue" :moment="props.moment" :format="props.format" v-model:data-filtered-value="dataFilteredValue" v-model:filter-opened="column.filterOpened" v-model:column-filtered-value="column.filteredValue" :filter-multiple="column.filterMultiple" :filter-placement="column.filterPlacement" :filters="props.filters" type="number" @filter-change="emits('filterChange')"></TableFilter>
      </slot>
    </template>
    <template #default="{ row, column, $index }">
      <slot name="default" :row="row" :column="column" :index="$index">
        <template v-if="typeof column.formatter === 'function'">
          <component :is="h({ setup: () => () => column.formatter(row, column, column.property ? row[column.property] : '', $index) })"></component>
        </template>
        <template v-else-if="isNaN(row[column.property])">
          <span :style="{ color: 'var(--el-text-color-disabled)', fontWeight: '600', userSelect: 'none' }">--</span>
        </template>
        <template v-else>
          <el-text type="primary">{{ row[column.property] || 0 }}</el-text>
        </template>
      </slot>
    </template>
  </el-table-column>
  <!--  -->
  <!--  -->
  <!--  -->
  <el-table-column v-else-if="props.type === 'date'" type="default" :class-name="props.className" :label-class-name="props.labelClassName" :property="props.property" :prop="props.prop" :width="props.width" :min-width="props.minWidth" :render-header="props.renderHeader" :sortable="props.sortable" :sort-method="props.sortMethod" :sort-by="props.sortBy" :resizable="props.resizable" :column-key="props.columnKey" :align="props.align" :header-align="props.headerAlign" :show-overflow-tooltip="props.showOverflowTooltip" :fixed="props.fixed" :formatter="props.formatter" :selectable="props.selectable" :reserve-selection="props.reserveSelection" :filter-method="props.filterMethod" :filtered-value="typeof props.filteredValue === 'string' ? props.filteredValue.split(',').filter((v) => v) : [props.filteredValue].filter((v) => v)" :filters="props.showFilter ? undefined : props.filters" :filter-placement="props.filterPlacement" :filter-multiple="props.filterMultiple" :index="props.index" :sort-orders="props.sortOrders">
    <template #header="{ column, $index }">
      <slot name="header" :column="column" :index="$index">
        {{ props.label }}
        <TableFilter v-if="props.showFilter" v-model:filtered-value="filteredValue" :moment="'x'" :format="props.format" v-model:data-filtered-value="dataFilteredValue" v-model:filter-opened="column.filterOpened" v-model:column-filtered-value="column.filteredValue" :filter-multiple="column.filterMultiple" :filter-placement="column.filterPlacement" :filters="props.filters" type="date" @filter-change="emits('filterChange')"></TableFilter>
      </slot>
    </template>
    <template #default="{ row, column, $index }">
      <slot name="default" :row="row" :column="column" :index="$index">
        <template v-if="typeof column.formatter === 'function'">
          <component :is="h({ setup: () => () => column.formatter(row, column, column.property ? row[column.property] : '', $index) })"></component>
        </template>
        <template v-else-if="!row[column.property]">
          <span :style="{ color: 'var(--el-text-color-disabled)', fontWeight: '600', userSelect: 'none' }">--</span>
        </template>
        <template v-else>
          {{ row[column.property] ? moment(row[column.property], props.moment).format(props.format) : "--" }}
        </template>
      </slot>
    </template>
  </el-table-column>
  <!--  -->
  <!--  -->
  <!--  -->
  <el-table-column v-else-if="props.type === 'enum'" type="default" :class-name="props.className" :label-class-name="props.labelClassName" :property="props.property" :prop="props.prop" :width="props.width" :min-width="props.minWidth" :render-header="props.renderHeader" :sortable="props.sortable" :sort-method="props.sortMethod" :sort-by="props.sortBy" :resizable="props.resizable" :column-key="props.columnKey" :align="props.align" :header-align="props.headerAlign" :show-overflow-tooltip="props.showOverflowTooltip" :fixed="props.fixed" :formatter="props.formatter" :selectable="props.selectable" :reserve-selection="props.reserveSelection" :filter-method="props.filterMethod" :filtered-value="typeof props.filteredValue === 'string' ? props.filteredValue.split(',').filter((v) => v) : [props.filteredValue].filter((v) => v)" :filters="props.showFilter ? undefined : props.filters" :filter-placement="props.filterPlacement" :filter-multiple="props.filterMultiple" :index="props.index" :sort-orders="props.sortOrders">
    <template #header="{ column, $index }">
      <slot name="header" :column="column" :index="$index">
        {{ props.label }}
        <TableFilter v-if="props.showFilter" v-model:filtered-value="filteredValue" v-model:filter-opened="column.filterOpened" v-model:column-filtered-value="column.filteredValue" :filter-multiple="column.filterMultiple" :filter-placement="column.filterPlacement" :filters="props.filters" type="enum" @filter-change="emits('filterChange')"></TableFilter>
      </slot>
    </template>
    <template #default="{ row, column, $index }">
      <slot name="default" :row="row" :column="column" :index="$index">
        <template v-if="typeof column.formatter === 'function'">
          {{ column.formatter(row, column, column.property ? row[column.property] : "", $index) }}
        </template>
        <template v-else-if="'type' in (find(props.filters, (v) => v.value === row[column.property]) || {})">
          <el-tag :type="(find(props.filters, (v) => v.value === row[column.property]) || {}).type" size="small">{{ (find(props.filters, (v) => v.value === row[column.property]) || {}).text }}</el-tag>
        </template>
        <template v-else-if="'color' in (find(props.filters, (v) => v.value === row[column.property]) || {})">
          <!-- <i class="tw-mr-2 tw-inline-block tw-h-[10px] tw-w-[10px] tw-rounded-[50%]" :style="{ backgroundColor: (find(props.filters, (v) => v.value === row[column.property]) || {}).color }"></i> -->
          <span class="tw-rounded-full tw-px-3 tw-py-1 tw-text-white" :style="{ backgroundColor: (find(props.filters, (v) => v.value === row[column.property]) || {}).color }">{{ (find(props.filters, (v) => v.value === row[column.property]) || {}).text }}</span>
          <!-- <el-tag :color="(find(props.filters, (v) => v.value === row[column.property]) || {}).color" size="small" effect="dark" round>{{ (find(props.filters, (v) => v.value === row[column.property]) || {}).text }}</el-tag> -->
        </template>
        <template v-else>
          <el-tag size="small" effect="dark" round>{{ (find(props.filters, (v) => v.value === row[column.property]) || {}).text || row[column.property] }}</el-tag>
        </template>
      </slot>
    </template>
  </el-table-column>
  <el-table-column v-else-if="props.type === 'condition'" type="default" :class-name="props.className" :label-class-name="props.labelClassName" :property="props.property" :prop="props.prop" :width="props.width" :min-width="props.minWidth" :render-header="props.renderHeader" :sortable="props.sortable" :sort-method="props.sortMethod" :sort-by="props.sortBy" :resizable="props.resizable" :column-key="props.columnKey" :align="props.align" :header-align="props.headerAlign" :show-overflow-tooltip="props.showOverflowTooltip" :fixed="props.fixed" :formatter="props.formatter" :selectable="props.selectable" :reserve-selection="props.reserveSelection" :filter-method="props.filterMethod" :filtered-value="[customFilteredValue.value0 || '', customFilteredValue.value1 || ''].filter((v) => v)" :filters="props.showFilter ? undefined : props.filters" :filter-placement="props.filterPlacement" :filter-multiple="props.filterMultiple" :index="props.index" :sort-orders="props.sortOrders">
    <template #header="{ column, $index }">
      <slot name="header" :column="column" :index="$index">
        {{ props.label }}
        <!-- <TableFilter v-if="props.showFilter" v-model:filtered-many-value="manyFilteredValue" v-model:filter-opened="column.filterOpened" v-model:column-filtered-value="column.filteredValue" :filter-multiple="column.filterMultiple" :filter-placement="column.filterPlacement" :filters="props.filters" type="tenantName" @filter-change="emits('filterChange')"></TableFilter> -->
      </slot>
      <TableFilter v-if="props.showFilter" v-model:filtered-value="column.filteredValue" v-model:filter-opened="column.filterOpened" v-model:column-filtered-value="customFilteredValue" :filter-multiple="column.filterMultiple" :filter-placement="column.filterPlacement" :filters="props.filters" type="condition" @filter-change="emits('filterChange')"></TableFilter>
    </template>
    <template #default="{ row, column, $index }">
      <slot name="default" :row="row" :column="column" :index="$index">
        <template v-if="typeof column.formatter === 'function'">
          <component :is="h({ setup: () => () => column.formatter(row, column, column.property ? row[column.property] : '', $index) })"></component>
        </template>
        <template v-else-if="row[column.property] === null || row[column.property] === undefined || row[column.property] === ''">
          <span :style="{ color: 'var(--el-text-color-disabled)', fontWeight: '600', userSelect: 'none' }">--</span>
        </template>
        <template v-else>
          {{ row[column.property] }}
        </template>
      </slot>
    </template>
  </el-table-column>
</template>

<script setup lang="ts" generic="Row extends Record<string, any>">
import { ref, useModel, computed, h, watch } from "vue";
import { useTransition, TransitionPresets } from "@vueuse/core";
import { find, isNaN } from "lodash-es";
import { useI18n } from "vue-i18n";
import { type VNode } from "vue";
import moment from "moment";
import { ElTooltip } from "element-plus";
import TableFilter from "./TableFilter.vue";

interface Props {
  type: "default" | "selection" | "index" | "expand" | "number" | "date" | "enum" | "condition" | "tenantName" | "order" | "deviceName" | "locationDesc" | "deviceMessage" | "orderType";
  list: Row[];

  moment: string;
  format: string;
  showFilter: boolean;

  label: string;
  className: string;
  labelClassName: string;
  property: string;
  prop: string;
  width: number | string;
  minWidth: number | string;
  renderHeader: TableColumnCtx<Row>["renderHeader"];
  sortable: boolean | string;
  sortMethod: TableColumnCtx<Row>["sortMethod"];
  sortBy: TableColumnCtx<Row>["sortBy"];
  resizable: boolean;
  columnKey: string;
  align: "left" | "center" | "right";
  headerAlign: "left" | "center" | "right";
  showOverflowTooltip: TableColumnCtx<Row>["showOverflowTooltip"];
  fixed: boolean | "left" | "right";
  formatter: TableColumnCtx<Row>["formatter"];
  selectable: TableColumnCtx<Row>["selectable"];
  reserveSelection: boolean;
  filterMethod: TableColumnCtx<Row>["filterMethod"];
  filteredValue: any;
  dataFilteredValue: { start: string; end: string };
  filters: TableColumnCtx<Row>["filters"];
  filterPlacement: "auto" | "auto-start" | "auto-end" | "top" | "bottom" | "right" | "left" | "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end";
  filterMultiple: boolean;
  index: TableColumnCtx<Row>["index"];
  sortOrders: TableColumnCtx<Row>["sortOrders"];
  manyFilteredValue: any;
  customFilteredValue: Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" };
}

const props = withDefaults(defineProps<Partial<Props>>(), {
  type: "default",
  list: () => [],

  moment: "x",
  format: "YYYY-MM-DD HH:mm:ss",
  options: () => [],

  width: "",
  minWidth: "",
  sortable: false,
  resizable: true,
  showOverflowTooltip: undefined,
  filterMultiple: false,
  sortOrders: () => ["ascending", "descending", null],
  manyFilteredValue: () => ({}),

  customFilteredValue: () => ({ relation: "AND", type0: "", input0: "", type1: "", value0: "", value1: "", input1: "" }),
});

const filteredValue = useModel(props, "filteredValue");
const manyFilteredValue = useModel(props, "manyFilteredValue");
const customFilteredValue = useModel(props, "customFilteredValue");

const dataFilteredValue = useModel(props, "dataFilteredValue");

interface Emits {
  ($event: "filterChange"): void;
  ($event: "update:filteredValue", value: any): void;
  ($event: "update:customFilteredValue", value: any): void;
  ($event: "update:status1", value: any): void;
  ($event: "update:mold", value: any): void;
  ($event: "update:status2", value: any): void;
}
const emits = defineEmits<Emits>();

type CI<T extends Record<string, any>> = { column: TableColumnCtx<T>; $index: number };

type Filters = {
  text: string;
  value: string | boolean;
  type?: "" | "success" | "warning" | "info" | "danger";
  color?: string;
}[];

type FilterMethods<T extends Record<string, any>> = (value: T[keyof T], row: T, column: TableColumnCtx<T>) => void;

type TableOverflowTooltipOptions = Partial<Pick<import("element-plus").ElTooltipProps, "effect" | "enterable" | "hideAfter" | "offset" | "placement" | "popperClass" | "popperOptions" | "showAfter" | "showArrow">>;

interface TableColumnCtx<T extends Record<string, any>> {
  id: string;
  realWidth: number;
  type: string;
  label: string;
  className: string;
  labelClassName: string;
  property: string;
  prop: string;
  width: string | number;
  minWidth: string | number;
  renderHeader: (data: CI<T>) => VNode;
  sortable: boolean | string;
  sortMethod: (a: T, b: T) => number;
  sortBy: string | ((row: T, index: number) => string) | string[];
  resizable: boolean;
  columnKey: string;
  rawColumnKey: string;
  align: string;
  headerAlign: string;
  showOverflowTooltip?: boolean | TableOverflowTooltipOptions;
  fixed: boolean | string;
  formatter: (row: T, column: TableColumnCtx<T>, cellValue: T[keyof T], index: number) => VNode | string;
  selectable: (row: T, index: number) => boolean;
  reserveSelection: boolean;
  filterMethod: FilterMethods<T>;
  filteredValue: string[];
  filters: Filters;
  filterPlacement: string;
  filterMultiple: boolean;
  index: number | ((index: number) => number);
  sortOrders: ("ascending" | "descending" | null)[];
  renderCell: (data: any) => void;
  colSpan: number;
  rowSpan: number;
  children: TableColumnCtx<T>[];
  level: number;
  filterable: boolean | FilterMethods<T> | Filters;
  order: string;
  isColumnGroup: boolean;
  isSubColumn: boolean;
  columns: TableColumnCtx<T>[];
  getColumnIndex: () => number;
  no: number;
  filterOpened?: boolean;
}

const tooltipRef = ref<InstanceType<typeof ElTooltip> | null>(null);

const { t } = useI18n();
const tooltipVisible = ref(false);

const popperPaneRef = computed(() => {
  return tooltipRef.value?.popperRef?.contentRef;
});

defineSlots<{
  header(props: { column: TableColumnCtx<Row>; index: number }): any;
  default(props: { row: Row; column: TableColumnCtx<Row>; store?: any; index: number; expanded?: boolean }): any;
}>();
</script>

<style lang="scss" scoped>
:deep(.el-tag) {
  border-color: none !important;
}
</style>
