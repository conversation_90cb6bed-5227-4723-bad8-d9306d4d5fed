<template>
  <el-tooltip ref="tooltipRef" :visible="filterOpened" :offset="0" :placement="props.filterPlacement || 'bottom-start'" :show-arrow="false" :stop-popper-mouse-event="false" teleported effect="light" pure popper-class="el-table-filter" persistent>
    <template #content>
      <div v-if="props.type === 'default' && !props.filters.length">
        <div class="el-table-filter__content tw-p-[12px]">
          <el-input :model-value="columnFilteredValue.join(',')" @update:model-value="($event) => (columnFilteredValue = [$event].filter((v) => v))" :prefix-icon="Search" clearable></el-input>
        </div>
        <div class="el-table-filter__bottom">
          <button :class="{ 'is-disabled': (columnFilteredValue || []).length === 0 }" :disabled="(columnFilteredValue || []).length === 0" type="button" @click="() => (emits('filterChange', (filteredValue = columnFilteredValue.join(','))), (filterOpened = false))">
            {{ t("el.table.confirmFilter") }}
          </button>
          <button type="button" @click="() => ((columnFilteredValue = []), emits('filterChange', (filteredValue = '')), (filterOpened = false))">
            {{ t("el.table.resetFilter") }}
          </button>
        </div>
      </div>
      <div v-else-if="props.type === 'condition'" style="padding: 8px; box-sizing: border-box; width: 240px">
        <!-- <pre>{{ columnFilteredValue }}</pre> -->
        <div class="el-table-filter__content">
          <div>
            <el-select :model-value="columnFilteredValue.type0" style="width: 100%" :teleported="false" @update:model-value="($event) => (columnFilteredValue = { input0: columnFilteredValue.input0, type0: $event, value0: columnFilteredValue.value0, relation: columnFilteredValue.relation, input1: columnFilteredValue.input1, type1: columnFilteredValue.type1, value1: columnFilteredValue.value1 })">
              <el-option v-for="filter in props.filters" :key="filter.value" :label="filter.text" :value="filter.value"></el-option>
            </el-select>
          </div>
          <div>
            <el-input v-if="columnFilteredValue.input0 === ''" :model-value="columnFilteredValue.value0" style="width: 100%" :prefix-icon="Search" clearable @update:model-value="($event) => ((columnFilteredValue = { input0: columnFilteredValue.input0, type0: columnFilteredValue.type0, value0: $event, relation: columnFilteredValue.relation, input1: columnFilteredValue.input1, type1: columnFilteredValue.type1, value1: columnFilteredValue.value1 }), nextTick(() => (filteredValue = [columnFilteredValue.value0 || '', columnFilteredValue.value1 || ''].filter((v) => v))))"></el-input>
            <el-input-number v-else-if="columnFilteredValue.input0 === 'number'" :model-value="columnFilteredValue.value0 === '' ? null : columnFilteredValue.value0" style="width: 100%" :min="0" :step="1" :step-strictly="true" :controls="true" controls-position="right" @update:model-value="($event) => ((columnFilteredValue = { input0: columnFilteredValue.input0, type0: columnFilteredValue.type0, value0: `${typeof $event === 'number' ? $event : ''}`, relation: columnFilteredValue.relation, input1: columnFilteredValue.input1, type1: columnFilteredValue.type1, value1: columnFilteredValue.value1 }), nextTick(() => (filteredValue = [columnFilteredValue.value0 || '', columnFilteredValue.value1 || ''].filter((v) => v))))"></el-input-number>
            <el-select v-else-if="typeof columnFilteredValue.input0 === 'string'" :model-value="columnFilteredValue.value0" style="width: 100%" :teleported="false" clearable filterable @update:model-value="($event) => ((columnFilteredValue = { input0: columnFilteredValue.input0, type0: columnFilteredValue.type0, value0: $event, relation: columnFilteredValue.relation, input1: columnFilteredValue.input1, type1: columnFilteredValue.type1, value1: columnFilteredValue.value1 }), nextTick(() => (filteredValue = [columnFilteredValue.value0 || '', columnFilteredValue.value1 || ''].filter((v) => v))))">
              <el-option v-for="[key, label] in toSearchParams(columnFilteredValue.input0)" :key="key" :label="label" :value="key"></el-option>
            </el-select>
          </div>
          <div style="padding: 10px 0; box-sizing: border-box">
            <el-select :model-value="columnFilteredValue.relation" style="width: 100%" :teleported="false" @update:model-value="($event) => (columnFilteredValue = { input0: columnFilteredValue.input0, type0: columnFilteredValue.type0, value0: columnFilteredValue.value0, relation: $event, input1: columnFilteredValue.input1, type1: columnFilteredValue.type1, value1: columnFilteredValue.value1 })">
              <el-option value="AND" :label="t('glob.And')"></el-option>
              <el-option value="OR" :label="t('glob.Or')"></el-option>
            </el-select>
          </div>
          <div>
            <el-select :model-value="columnFilteredValue.type1" style="width: 100%" :teleported="false" @update:model-value="($event) => (columnFilteredValue = { input0: columnFilteredValue.input0, type0: columnFilteredValue.type0, value0: columnFilteredValue.value0, relation: columnFilteredValue.relation, input1: columnFilteredValue.input1, type1: $event, value1: columnFilteredValue.value1 })">
              <el-option v-for="filter in props.filters" :key="filter.value" :label="filter.text" :value="filter.value"></el-option>
            </el-select>
          </div>
          <div>
            <el-input v-if="columnFilteredValue.input1 === ''" :model-value="columnFilteredValue.value1" style="width: 100%" :prefix-icon="Search" clearable @update:model-value="($event) => ((columnFilteredValue = { input0: columnFilteredValue.input0, type0: columnFilteredValue.type0, value0: columnFilteredValue.value0, relation: columnFilteredValue.relation, input1: columnFilteredValue.input1, type1: columnFilteredValue.type1, value1: $event }), nextTick(() => (filteredValue = [columnFilteredValue.value0 || '', columnFilteredValue.value1 || ''].filter((v) => v))))"></el-input>
            <el-input-number v-else-if="columnFilteredValue.input1 === 'number'" :model-value="columnFilteredValue.value1 === '' ? null : columnFilteredValue.value1" style="width: 100%" :min="0" :step="1" :step-strictly="true" :controls="true" controls-position="right" @update:model-value="($event) => ((columnFilteredValue = { input0: columnFilteredValue.input0, type0: columnFilteredValue.type0, value0: columnFilteredValue.value0, relation: columnFilteredValue.relation, input1: columnFilteredValue.input1, type1: columnFilteredValue.type1, value1: `${typeof $event === 'number' ? $event : ''}` }), nextTick(() => (filteredValue = [columnFilteredValue.value0 || '', columnFilteredValue.value1 || ''].filter((v) => v))))"></el-input-number>
            <el-select v-else-if="typeof columnFilteredValue.input1 === 'string'" :model-value="columnFilteredValue.value1" style="width: 100%" :teleported="false" clearable filterable @update:model-value="($event) => ((columnFilteredValue = { input0: columnFilteredValue.input0, type0: columnFilteredValue.type0, value0: columnFilteredValue.value0, relation: columnFilteredValue.relation, input1: columnFilteredValue.input1, type1: columnFilteredValue.type1, value1: `${$event || 0}` }), nextTick(() => (filteredValue = [columnFilteredValue.value0 || '', columnFilteredValue.value1 || ''].filter((v) => v))))">
              <el-option v-for="[key, label] in toSearchParams(columnFilteredValue.input1)" :key="key" :label="label" :value="key"></el-option>
            </el-select>
          </div>
        </div>
        <div class="el-table-filter__bottom">
          <button :class="{ 'is-disabled': (filteredValue instanceof Array ? filteredValue : []).filter((v) => v).length === 0 }" :disabled="(filteredValue instanceof Array ? filteredValue : []).filter((v) => v).length === 0" type="button" @click="() => nextTick(() => (emits('filterChange', (filteredValue = [columnFilteredValue.value0 || '', columnFilteredValue.value1 || ''].filter((v) => v)).join(',')), (filterOpened = false)))">
            {{ t("el.table.confirmFilter") }}
          </button>
          <button type="button" @click="() => ((columnFilteredValue = { input0: columnFilteredValue.input0, type0: columnFilteredValue.type0, value0: '', relation: columnFilteredValue.relation, input1: columnFilteredValue.input1, type1: columnFilteredValue.type1, value1: '' }), nextTick(() => (emits('filterChange', (filteredValue = [columnFilteredValue.value0 || '', columnFilteredValue.value1 || ''].filter((v) => v)).join(',')), (filterOpened = false))))">
            {{ t("el.table.resetFilter") }}
          </button>
        </div>
      </div>

      <div v-if="props.type === 'tenantName'" style="padding: 8px; box-sizing: border-box; width: 240px">
        <div>
          <el-select :model-value="filteredManyValue.status" @update:model-value="updateFilteredMany({ ...filteredManyValue, status: $event })" @change="filterOpened = true" style="width: 100%">
            <el-option value="include" :label="t('glob.Contains')">{{ t("glob.Contains") }}</el-option>
            <el-option value="exclude" :label="t('glob.Does not contain')">{{ t("glob.Does not contain") }}</el-option>
            <el-option value="be" label="等于">等于</el-option>
            <el-option value="notBe" label="不等于">不等于</el-option>
          </el-select>
        </div>
        <div>
          <el-input :model-value="filteredManyValue.firstName" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, firstName: $event })" :prefix-icon="Search" clearable></el-input>
        </div>
        <div style="padding: 10px 0; box-sizing: border-box">
          <el-select :model-value="filteredManyValue.relation" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, relation: $event })" @change="filterOpened = true">
            <el-option value="AND" :label="t('glob.And')">{{ t("glob.And") }}</el-option>
            <el-option value="OR" :label="t('glob.Or')">{{ t("glob.Or") }}</el-option>
          </el-select>
        </div>
        <div>
          <el-select :model-value="filteredManyValue.type" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, type: $event })" @change="filterOpened = true">
            <el-option value="include" :label="t('glob.Contains')">{{ t("glob.Contains") }}</el-option>
            <el-option value="exclude" :label="t('glob.Does not contain')">{{ t("glob.Does not contain") }}</el-option>
            <el-option value="be" label="等于">等于</el-option>
            <el-option value="notBe" label="不等于">不等于</el-option>
          </el-select>
        </div>
        <div>
          <el-input :model-value="filteredManyValue.lastName" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, lastName: $event })" :prefix-icon="Search" clearable></el-input>
        </div>

        <div class="el-table-filter__bottom">
          <button type="button" @click="exportFilterValue(), (filterOpened = false)">
            {{ t("el.table.confirmFilter") }}
          </button>
          <button type="button" @click="() => ((columnFilteredValue = []), emits('filterChange', (filteredManyValue = { status: 'include', type: 'include', relation: 'AND', firstName: '', lastName: '' })), (filterOpened = false))">
            {{ t("el.table.resetFilter") }}
          </button>
        </div>
      </div>

      <div v-if="props.type === 'order'" style="padding: 8px; box-sizing: border-box; width: 240px">
        <div>
          <el-select :model-value="filteredManyValue.status" @update:model-value="updateFilteredMany({ ...filteredManyValue, status: $event })" @change="filterOpened = true" style="width: 100%">
            <el-option value="include" :label="t('glob.Contains')">{{ t("glob.Contains") }}</el-option>
            <el-option value="exclude" :label="t('glob.Does not contain')">{{ t("glob.Does not contain") }}</el-option>
          </el-select>
        </div>
        <div>
          <el-input :model-value="filteredManyValue.firstName" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, firstName: $event })" :prefix-icon="Search" clearable></el-input>
        </div>
        <div style="padding: 10px 0; box-sizing: border-box">
          <el-select :model-value="filteredManyValue.relation" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, relation: $event })" @change="filterOpened = true">
            <el-option value="AND" :label="t('glob.And')">{{ t("glob.And") }}</el-option>
            <el-option value="OR" :label="t('glob.Or')">{{ t("glob.Or") }}</el-option>
          </el-select>
        </div>
        <div>
          <el-select :model-value="filteredManyValue.type" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, type: $event })" @change="filterOpened = true">
            <el-option value="include" :label="t('glob.Contains')">{{ t("glob.Contains") }}</el-option>
            <el-option value="exclude" :label="t('glob.Does not contain')">{{ t("glob.Does not contain") }}</el-option>
          </el-select>
        </div>
        <div>
          <el-input :model-value="filteredManyValue.lastName" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, lastName: $event })" :prefix-icon="Search" clearable></el-input>
        </div>

        <div class="el-table-filter__bottom">
          <button type="button" @click="exportFilterValue(), (filterOpened = false)">
            {{ t("el.table.confirmFilter") }}
          </button>
          <button type="button" @click="() => ((columnFilteredValue = []), emits('filterChange', (filteredManyValue = { status: 'include', type: 'include', relation: 'AND', firstName: '', lastName: '' })), (filterOpened = false))">
            {{ t("el.table.resetFilter") }}
          </button>
        </div>
      </div>

      <div v-if="props.type === 'deviceName'" style="padding: 8px; box-sizing: border-box; width: 240px">
        <div>
          <el-select :model-value="filteredManyValue.status" @update:model-value="updateFilteredMany({ ...filteredManyValue, status: $event })" @change="filterOpened = true" style="width: 100%">
            <el-option value="include" :label="t('glob.Contains')">{{ t("glob.Contains") }}</el-option>
            <el-option value="exclude" :label="t('glob.Does not contain')">{{ t("glob.Does not contain") }}</el-option>
          </el-select>
        </div>
        <div>
          <el-input :model-value="filteredManyValue.firstName" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, firstName: $event })" :prefix-icon="Search" clearable></el-input>
        </div>
        <div style="padding: 10px 0; box-sizing: border-box">
          <el-select :model-value="filteredManyValue.relation" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, relation: $event })" @change="filterOpened = true">
            <el-option value="AND" :label="t('glob.And')">{{ t("glob.And") }}</el-option>
            <el-option value="OR" :label="t('glob.Or')">{{ t("glob.Or") }}</el-option>
          </el-select>
        </div>
        <div>
          <el-select :model-value="filteredManyValue.type" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, type: $event })" @change="filterOpened = true">
            <el-option value="include" :label="t('glob.Contains')">{{ t("glob.Contains") }}</el-option>
            <el-option value="exclude" :label="t('glob.Does not contain')">{{ t("glob.Does not contain") }}</el-option>
          </el-select>
        </div>
        <div>
          <el-input :model-value="filteredManyValue.lastName" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, lastName: $event })" :prefix-icon="Search" clearable></el-input>
        </div>

        <div class="el-table-filter__bottom">
          <button type="button" @click="exportFilterValue(), (filterOpened = false)">
            {{ t("el.table.confirmFilter") }}
          </button>
          <button type="button" @click="() => ((columnFilteredValue = []), emits('filterChange', (filteredManyValue = { status: 'include', type: 'include', relation: 'AND', firstName: '', lastName: '' })), (filterOpened = false))">
            {{ t("el.table.resetFilter") }}
          </button>
        </div>
      </div>

      <div v-if="props.type === 'locationDesc'" style="padding: 8px; box-sizing: border-box; width: 240px">
        <div>
          <el-select :model-value="filteredManyValue.status" @update:model-value="updateFilteredMany({ ...filteredManyValue, status: $event })" @change="filterOpened = true" style="width: 100%">
            <el-option value="include" :label="t('glob.Contains')">{{ t("glob.Contains") }}</el-option>
            <el-option value="exclude" :label="t('glob.Does not contain')">{{ t("glob.Does not contain") }}</el-option>
          </el-select>
        </div>
        <div>
          <el-input :model-value="filteredManyValue.firstName" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, firstName: $event })" :prefix-icon="Search" clearable></el-input>
        </div>
        <div style="padding: 10px 0; box-sizing: border-box">
          <el-select :model-value="filteredManyValue.relation" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, relation: $event })" @change="filterOpened = true">
            <el-option value="AND" :label="t('glob.And')">{{ t("glob.And") }}</el-option>
            <el-option value="OR" :label="t('glob.Or')">{{ t("glob.Or") }}</el-option>
          </el-select>
        </div>
        <div>
          <el-select :model-value="filteredManyValue.type" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, type: $event })" @change="filterOpened = true">
            <el-option value="include" :label="t('glob.Contains')">{{ t("glob.Contains") }}</el-option>
            <el-option value="exclude" :label="t('glob.Does not contain')">{{ t("glob.Does not contain") }}</el-option>
          </el-select>
        </div>
        <div>
          <el-input :model-value="filteredManyValue.lastName" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, lastName: $event })" :prefix-icon="Search" clearable></el-input>
        </div>

        <div class="el-table-filter__bottom">
          <button type="button" @click="exportFilterValue(), (filterOpened = false)">
            {{ t("el.table.confirmFilter") }}
          </button>
          <button type="button" @click="() => ((columnFilteredValue = []), emits('filterChange', (filteredManyValue = { status: 'include', type: 'include', relation: 'AND', firstName: '', lastName: '' })), (filterOpened = false))">
            {{ t("el.table.resetFilter") }}
          </button>
        </div>
      </div>
      <div v-if="props.type === 'orderType'" style="padding: 8px; box-sizing: border-box; width: 240px">
        <div>
          <el-select :model-value="filteredManyValue.status" @update:model-value="updateFilteredMany({ ...filteredManyValue, status: $event })" @change="filterOpened = true" style="width: 100%">
            <el-option value="include" label="等于">等于</el-option>
            <el-option value="exclude" label="不等于">不等于</el-option>
          </el-select>
        </div>
        <div>
          <el-select :model-value="filteredManyValue.firstName" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, firstName: $event })" @change="filterOpened = true">
            <el-option value="EVENT_ORDER" label="事件">事件</el-option>
            <el-option value="SERVICE_REQUEST" label="服务请求">服务请求</el-option>
            <el-option value="CHANGE" label="变更">变更</el-option>
            <el-option value="QUESTION" label="问题">问题</el-option>
          </el-select>
          <!-- <el-input :model-value="filteredManyValue.firstName" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, firstName: $event })" :prefix-icon="Search" clearable></el-input> -->
        </div>
        <div style="padding: 10px 0; box-sizing: border-box">
          <el-select :model-value="filteredManyValue.relation" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, relation: $event })" @change="filterOpened = true">
            <el-option value="AND" :label="t('glob.And')">{{ t("glob.And") }}</el-option>
            <el-option value="OR" :label="t('glob.Or')">{{ t("glob.Or") }}</el-option>
          </el-select>
        </div>
        <div>
          <el-select :model-value="filteredManyValue.type" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, type: $event })" @change="filterOpened = true">
            <el-option value="include" label="等于">等于</el-option>
            <el-option value="exclude" label="不等于">不等于</el-option>
          </el-select>
        </div>
        <div>
          <el-select :model-value="filteredManyValue.lastName" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, lastName: $event })" @change="filterOpened = true">
            <el-option value="EVENT_ORDER" label="事件">事件</el-option>
            <el-option value="SERVICE_REQUEST" label="服务请求">服务请求</el-option>
            <el-option value="CHANGE" label="变更">变更</el-option>
            <el-option value="QUESTION" label="问题">问题</el-option>
          </el-select>
          <!-- <el-input :model-value="filteredManyValue.lastName" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, lastName: $event })" :prefix-icon="Search" clearable></el-input> -->
        </div>

        <div class="el-table-filter__bottom">
          <button type="button" @click="exportFilterValue(), (filterOpened = false)">
            {{ t("el.table.confirmFilter") }}
          </button>
          <button type="button" @click="() => ((columnFilteredValue = []), emits('filterChange', (filteredManyValue = { status: 'include', type: 'include', relation: 'AND', firstName: '', lastName: '' })), (filterOpened = false))">
            {{ t("el.table.resetFilter") }}
          </button>
        </div>
      </div>

      <div v-if="props.type === 'deviceMessage'" style="padding: 8px; box-sizing: border-box; width: 240px">
        <div>
          <el-select :model-value="filteredManyValue.status" @update:model-value="updateFilteredMany({ ...filteredManyValue, status: $event })" @change="filterOpened = true" style="width: 100%">
            <el-option value="include" :label="t('glob.Contains')">{{ t("glob.Contains") }}</el-option>
            <el-option value="exclude" :label="t('glob.Does not contain')">{{ t("glob.Does not contain") }}</el-option>
          </el-select>
        </div>
        <div>
          <el-input :model-value="filteredManyValue.firstName" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, firstName: $event })" :prefix-icon="Search" clearable></el-input>
        </div>
        <div style="padding: 10px 0; box-sizing: border-box">
          <el-select :model-value="filteredManyValue.relation" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, relation: $event })" @change="filterOpened = true">
            <el-option value="AND" :label="t('glob.And')">{{ t("glob.And") }}</el-option>
            <el-option value="OR" :label="t('glob.Or')">{{ t("glob.Or") }}</el-option>
          </el-select>
        </div>
        <div>
          <el-select :model-value="filteredManyValue.type" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, type: $event })" @change="filterOpened = true">
            <el-option value="include" :label="t('glob.Contains')">{{ t("glob.Contains") }}</el-option>
            <el-option value="exclude" :label="t('glob.Does not contain')">{{ t("glob.Does not contain") }}</el-option>
          </el-select>
        </div>
        <div>
          <el-input :model-value="filteredManyValue.lastName" style="width: 100%" @update:model-value="updateFilteredMany({ ...filteredManyValue, lastName: $event })" :prefix-icon="Search" clearable></el-input>
        </div>

        <div class="el-table-filter__bottom">
          <button type="button" @click="exportFilterValue(), (filterOpened = false)">
            {{ t("el.table.confirmFilter") }}
          </button>
          <button type="button" @click="() => ((columnFilteredValue = []), emits('filterChange', (filteredManyValue = { status: 'include', type: 'include', relation: 'AND', firstName: '', lastName: '' })), (filterOpened = false))">
            {{ t("el.table.resetFilter") }}
          </button>
        </div>
      </div>
      <div v-else-if="props.type === 'date' && !props.filters.length" :style="{}">
        <!-- width: '480px' -->
        <div class="el-table-filter__content tw-p-[12px]">
          <el-date-picker :model-value="[((columnFilteredValue || [])[0] || {}).start, ((columnFilteredValue || [])[0] || {}).end]" @update:model-value="(v) => (columnFilteredValue = [{ start: v ? v[0] : '', end: v ? v[1] : '' }])" v-if="props.filterMultiple" :format="props.format" :value-format="props.moment" type="datetimerange" range-separator="-" :teleported="false" :default-time="defaultTime"></el-date-picker>
          <el-date-picker :model-value="columnFilteredValue[0]" @update:model-value="(v) => (columnFilteredValue = [v])" v-else :format="props.format" :value-format="props.moment" type="datetime" range-separator="-" :teleported="false" :default-time="defaultTime"></el-date-picker>
          <!-- <VueCtkDateTimePicker v-if="props.filterMultiple" :model-value="columnFilteredValue[0]" @update:model-value="($event) => (columnFilteredValue = [$event])" :locale="locale" :dark="config.layout.isDark" inline :range="true" :custom-shortcuts="customShortcuts" no-keyboard :format="props.format" :output-format="props.moment" :max-date="moment().format(props.format)" color="var(--el-color-primary)"></VueCtkDateTimePicker>
          <VueCtkDateTimePicker v-else :model-value="columnFilteredValue[0]" @update:model-value="($event) => (columnFilteredValue = [$event])" :locale="locale" :dark="config.layout.isDark" inline :range="false" no-keyboard :format="props.format" :output-format="props.moment" :max-date="moment().format(props.format)" color="var(--el-color-primary)"></VueCtkDateTimePicker> -->
        </div>
        <div class="el-table-filter__bottom">
          <button :class="{ 'is-disabled': (columnFilteredValue || []).length === 0 }" :disabled="(columnFilteredValue || []).length === 0" type="button" @click="() => (emits('filterChange', (filteredValue = columnFilteredValue[0])), (filterOpened = false))">
            {{ t("el.table.confirmFilter") }}
          </button>
          <button type="button" @click="() => ((columnFilteredValue = []), emits('filterChange', (filteredValue = '')), (filterOpened = false))">
            {{ t("el.table.resetFilter") }}
          </button>
        </div>
      </div>
      <div v-else-if="props.type === 'number' && !props.filters.length">
        <div class="el-table-filter__content tw-p-[12px]">
          <el-input-number :model-value="columnFilteredValue[0] as number" @update:model-value="($event) => (columnFilteredValue = [$event].filter((v) => v))" :prefix-icon="Search" clearable> </el-input-number>
        </div>
        <div class="el-table-filter__bottom">
          <button :class="{ 'is-disabled': (columnFilteredValue || []).length === 0 }" :disabled="(columnFilteredValue || []).length === 0" type="button" @click="() => (emits('filterChange', (filteredValue = columnFilteredValue.join(','))), (filterOpened = false))">
            {{ t("el.table.confirmFilter") }}
          </button>
          <button type="button" @click="() => ((columnFilteredValue = []), emits('filterChange', (filteredValue = '')), (filterOpened = false))">
            {{ t("el.table.resetFilter") }}
          </button>
        </div>
      </div>
      <template v-else-if="props.type === 'enum'">
        <div v-if="props.filterMultiple">
          <div class="el-table-filter__content">
            <el-scrollbar wrap-class="el-table-filter__wrap">
              <div class="el-table-filter__checkbox-group">
                <el-checkbox :model-value="props.filters.every((v) => columnFilteredValue.includes(v.value))" :indeterminate="props.filters.every((v) => columnFilteredValue.includes(v.value)) ? false : props.filters.some((v) => columnFilteredValue.includes(v.value))" @update:model-value="($event) => (columnFilteredValue = $event ? props.filters.map((v) => v.value) : [])">{{ $t("event.SelectAll") }}</el-checkbox>
              </div>
              <el-divider style="width: calc(100% - 16px); margin: 0 auto"></el-divider>
              <el-checkbox-group v-model="columnFilteredValue" class="el-table-filter__checkbox-group">
                <el-checkbox v-for="filter in props.filters" :key="filter.value" :label="filter.value">{{ filter.text }}</el-checkbox>
              </el-checkbox-group>
            </el-scrollbar>
          </div>
          <div class="el-table-filter__bottom">
            <button :class="{ 'is-disabled': (columnFilteredValue || []).length === 0 }" :disabled="(columnFilteredValue || []).length === 0" type="button" @click="() => (emits('filterChange', (filteredValue = columnFilteredValue.join(','))), (filterOpened = false))">
              {{ t("el.table.confirmFilter") }}
            </button>
            <button type="button" @click="() => ((columnFilteredValue = []), emits('filterChange', (filteredValue = '')), (filterOpened = false))">
              {{ t("el.table.resetFilter") }}
            </button>
          </div>
        </div>
        <ul v-else class="el-table-filter__list">
          <li :class="['el-table-filter__list-item', { 'is-active': !(columnFilteredValue || []).length }]" @click="() => ((columnFilteredValue = []), emits('filterChange', (filteredValue = '')), (filterOpened = false))">
            {{ t("el.table.clearFilter") }}
          </li>
          <li v-for="filter in props.filters" :key="filter.value" :class="['el-table-filter__list-item', { 'is-active': filter.value === columnFilteredValue[0] }]" :label="filter.value" @click="(columnFilteredValue = [filter.value]), emits('filterChange', (filteredValue = filter.value)), (filterOpened = false)">
            {{ filter.text }}
          </li>
        </ul>
      </template>
    </template>
    <template #default>
      <span v-click-outside:[popperPaneRef]="() => ((filterOpened = false), props.type === 'condition' || (columnFilteredValue = typeof filteredValue === 'string' ? `${filteredValue || ''}`.split(',').filter((v) => v) : [filteredValue].filter((v) => v)))" :class="[`el-table__column-filter-trigger`, `el-none-outline`]" @click.stop="() => ((filterOpened = !filterOpened), props.type === 'condition' || (columnFilteredValue = typeof filteredValue === 'string' ? `${filteredValue || ''}`.split(',').filter((v) => v) : [filteredValue].filter((v) => v)))">
        <el-link class="tw-align-text-top" :type="filterOpened || filteredValue instanceof Array ? (filteredValue.length ? 'primary' : 'default') : filteredValue || filteredManyValue.firstName || filteredManyValue.lastName ? 'primary' : 'default'" :underline="false">
          <svg width="14" height="14" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.13159 1.24858C9.06253 1.07943 8.94375 0.935223 8.79097 0.835035C8.63818 0.734846 8.45858 0.683394 8.27592 0.687482H1.70171C1.52254 0.687811 1.34748 0.741191 1.19862 0.840888C1.04975 0.940585 0.933741 1.08213 0.865221 1.24768C0.796702 1.41323 0.778741 1.59536 0.813604 1.7711C0.848467 1.94684 0.934593 2.10833 1.06112 2.23518L4.06767 5.12016V7.99112C4.06741 8.09424 4.10772 8.19333 4.17989 8.26699L5.54991 9.66974C5.58672 9.7083 5.63098 9.73896 5.68002 9.75986C5.72905 9.78076 5.78183 9.79146 5.83514 9.79131C5.88631 9.79124 5.93704 9.78173 5.98476 9.76326C6.05782 9.73425 6.1205 9.68398 6.16468 9.61896C6.20886 9.55394 6.23251 9.47715 6.23258 9.39854V5.10146L8.95391 2.23518C9.07721 2.10639 9.15982 1.94413 9.19142 1.76865C9.22302 1.59318 9.20221 1.41229 9.13159 1.24858Z" fill="currentColor" />
          </svg>
        </el-link>
      </span>
    </template>
  </el-tooltip>
</template>

<script setup lang="ts" generic="Row extends Record<string, unknown>">
import { ref, useModel, computed, nextTick } from "vue";
import { useI18n } from "vue-i18n";
import { ElTooltip } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import moment from "moment";
import { useConfig } from "@/stores/config";
import VueCtkDateTimePicker from "@/components/VueCtkDateTimePicker/index.vue";

const toSearchParams = (input: string) => new URLSearchParams(input);

interface Props {
  type: "default" | "date" | "enum" | "number" | "condition" | "tenantName" | "order" | "deviceName" | "locationDesc" | "deviceMessage" | "orderType";

  moment: string;
  format: string;

  dataFilteredValue: { start: string; end: string };

  filters: { text: string; value: string }[];
  filteredValue: string | Record<string, any>;
  columnFilteredValue: any;
  filterOpened: boolean;

  filterPlacement: "auto" | "auto-start" | "auto-end" | "top" | "bottom" | "right" | "left" | "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end";
  filterMultiple: boolean;
  filteredManyValue: any;
}
const props = withDefaults(defineProps<Partial<Props>>(), {
  type: "default",
  filterPlacement: "bottom-start",
  filteredValue: () => "",
  filters: () => [],
  columnFilteredValue: () => [],
  filteredManyValue: () => ({}),
});
interface Emits {
  ($event: "filterChange", filters: string): void;
  ($event: "update:filterOpened", opened: boolean): void;
  ($event: "update:filteredValue", value: string): void;
  ($event: "update:filteredManyValue", value: any): void;

  ($event: "update:columnFilteredValue", value: string[]): void;
}
const emits = defineEmits<Emits>();
const tenantNameFilter = ref("include");

const config = useConfig();

const filteredValue = useModel(props, "filteredValue");

const columnFilteredValue = useModel(props, "columnFilteredValue");
const filteredManyValue = useModel(props, "filteredManyValue");
const filterOpened = useModel(props, "filterOpened");

const defaultTime: [Date, Date] = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]; // '12:00:00', '08:00:00'

const customShortcuts = ref<{ key: string; label: string; value: "day" | "-day" | "isoWeek" | "-isoWeek" | "quarter" | "month" | "-month" | "year" | "-year" | "week" | "-week" | number }[]>([
  { key: "thisWeek", label: "本周", value: "isoWeek" },
  { key: "lastWeek", label: "上周", value: "-isoWeek" },
  { key: "last7Days", label: "七天", value: 7 },
  { key: "last30Days", label: "三十天", value: 30 },
  { key: "thisMonth", label: "本月", value: "month" },
  { key: "lastMonth", label: "上个月", value: "-month" },
  { key: "thisYear", label: "今年", value: "year" },
  { key: "lastYear", label: "去年", value: "-year" },
]);

const tooltipRef = ref<InstanceType<typeof ElTooltip> | null>(null);

const { t, locale } = useI18n();

const popperPaneRef = computed(() => {
  return tooltipRef.value?.popperRef?.contentRef;
});

function exportFilterValue() {
  console.log(filteredValue);
  emits("filterChange", filteredManyValue);
  // filterOpened = false;
}
function updateFilteredMany(e) {
  // emits("filterChange", filteredManyValue);
  filteredManyValue.value = e;
  nextTick(() => {});
}
</script>

<style lang="scss" scoped></style>
