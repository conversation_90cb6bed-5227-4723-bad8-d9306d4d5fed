import { i18n } from "@/lang/index";

const { t } = i18n.global;

export const included = { text: t("glob.Contains"), value: "include" };
export const notIncluded = { text: t("glob.Does not contain"), value: "exclude" };
export const equalTo = { text: t("glob.Is equal to"), value: "eq" };
export const notEqualTo = { text: t("glob.Is not equal to"), value: "ne" };

export const greaterThanOrEqualTo = { text: t("glob.Greater than or equal to"), value: "ge" };
export const greaterThan = { text: t("glob.Greater than"), value: "gt" };
export const lessThanOrEqualTo = { text: t("glob.Less than or equal to"), value: "le" };
export const lessThan = { text: t("glob.Less than"), value: "lt" };
export const empty = { text: t("glob.empty"), value: "isNull" };
export const notEmpty = { text: t("glob.Not empty"), value: "isNotNull" };

export const exoprtMatch1 = [included, notIncluded, equalTo, notEqualTo];
export const exoprtMatch2 = [equalTo, notEqualTo, greaterThanOrEqualTo, greaterThan, lessThanOrEqualTo, lessThan, empty, notEmpty];
export const exoprtMatch3 = [equalTo, notEqualTo];
export const exoprtMatch4 = [included, notIncluded];

