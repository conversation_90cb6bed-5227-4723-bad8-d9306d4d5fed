<template>
  <el-scrollbar max-height="400px" class="tw-w-full">
    <div class="auth">
      <el-tree :disabled="treeDisabled" :index="0" class="pageTree tree-line" v-loading="loading" :data="data" :props="defaultProps" @node-click="handleNodeClick" :default-expanded-keys="[]" :default-checked-keys="[proptreeId]" node-key="id">
        <template #default="{ data, node }">
          <el-col :span="20">
            <div class="tree-main">
              <img v-if="data.children?.length > 0 && node.expanded == false ? true : false" class="tree-icon" src="@/assets/icons/cc-plus-square.png" />
              <img v-if="node.expanded" class="tree-icon" src="@/assets/icons/cc-minus-square.png" />
              <!-- <el-input v-if="data.id === treeItem.id && props.operationType === '重命名'" v-model="treeItem.name" @keyup.enter="updateName"></el-input> -->
              <span class="tree-name" :class="treeId == data.id || proptreeId == data.id ? 'on' : 'tree-name'" @click.self="chooseTree(data, node)">{{ node.label }}</span>
            </div>
          </el-col>
        </template>
      </el-tree>
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts" generic="T extends object">
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox, ElTag } from "element-plus";

/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */

// import { useSiteConfig } from "@/stores/siteConfig";
import getUserInfo from "@/utils/getUserInfo";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

// import EditorForm from "./EditForm.vue";
import { getSafeContaineList, editSafeContaine } from "@/api/personnel";
import { getContainersTree } from "@/api/authConfig";
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
// const i18n = useI18n({ useScope: "local" });
const { t } = useI18n();
interface Emits {
  (event: "clickItem", item: any): void;
}
const emits = defineEmits<Emits>();
const route = useRoute();
const router = useRouter();
const userInfo = getUserInfo();
defineOptions({ name: "TenantGovern" });
const loading = ref(true);
const props = defineProps({
  //容器ID
  operationType: {
    type: String,
    default: "",
  },
  belong: {
    type: String,
    default: "",
  },
  proptreeId: {
    type: Number,
    default: -1,
  },
  treeStyle: {
    type: Object,
    default: () => {
      return {
        maxWidth: "600px",
        pointerEvents: "auto",
      };
    },
  },
});

// const siteConfig = useSiteConfig();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));
const tabPosition = ref("");

interface Tree {
  label: string;
  children?: Tree[];
}
const treeId = ref("");
const treeDisabled = ref(true);

const handleNodeClick = (data: Tree, node) => {
  // treeId.value = node.id;
};
interface Props {
  operationType?: string;
  belong?: string;
  treeStyle?: any;
  proptreeId?: number;
}
// const props = withDefaults(defineProps<Props>(), {

// });
const data: Tree[] = ref([]);

const defaultProps = {
  children: "children",
  label: "name",
  disabled: () => {
    return true;
  },
};
const treeItem = ref({});
function chooseTree(data, node) {
  treeItem.value = data;
  treeId.value = data.id;
  emits("clickItem", data);
}
async function updateName(val) {
  await editSafeContaine({
    id: treeItem.value.id,
    name: treeItem.value.name,
  })
    .then((res) => {
      if (res.success) {
        ElMessage.success("操作成功");
      }
    })
    .catch((err) => {
      ElMessage.error(err?.data);
    });
}
async function getSafeContaine(val) {
  loading.value = true;
  await getSafeContaineList({ containerId: userInfo.currentTenant.containerId }).then((res) => {
    // console.log(val);
    if (res.success) {
      loading.value = false;
      if (val != "") {
        data.value = res.data.filter((item) => item.id != val);
      } else data.value = res.data;
    }
  });
}
async function getContainersTreeList(containerId) {
  loading.value = true;
  await getContainersTree({ containerId: containerId }).then((res) => {
    if (res.success) {
      loading.value = false;
      data.value = res.data;

      if (props.proptreeId) chooseTree({ id: props.proptreeId }, {});
    }
  });
}
async function setTreeDepth() {
  treeId.value = data.value[0].id;
  emits("clickItem", data.value[0]);
}
watch(
  () => props.proptreeId,
  (newValue, oldValue) => {
    if (props.proptreeId > -1) {
      getContainersTreeList(props.proptreeId);
    }
  }
);
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
// const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
async function mounted() {
  if (props.proptreeId > -1) {
    await getContainersTreeList(props.proptreeId);
  } else {
    await getSafeContaine("");
  }
  // console.log(props.belong);
  if (props?.belong === "manage") {
    await setTreeDepth();
  }
  //
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({ getSafeContaine, chooseTree, treeItem, treeId });
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.auth {
  :deep(.el-tree-node__expand-icon) {
    color: #fff;
  }
  :deep(.el-tree) {
    /* 修改选中节点的背景颜色 */
    .el-tree-node__content:hover {
      background-color: #fff; /* 你想要的任何颜色 */
    }

    /* 如果你想要修改当前选中节点的背景颜色，而不仅仅是鼠标悬停时 */
    .el-tree-node__content.is-current {
      background-color: none; /* 你想要的任何颜色 */
    }
    .el-tree-node__content {
      height: 50px;
      padding-left: 0 !important;
    }
  }
  // .pageTree {
  //   height: 300px; /* 设置树的高度 */
  //   overflow-y: scroll; /* 添加垂直滚动条 */
  //   overflow-x: scroll; /* 添加横向滚动条 */
  //   // width: 200px;
  //   display: flex; /* 使用 flex 布局 */
  // }
}
.tree-main {
  display: flex;
  align-items: center;
  > .tree-icon {
    width: 16px;
    height: 16px;
    margin-right: 5px;
  }
  > .tree-name {
    display: flex;
    border: 1px solid #ddd;
    padding: 5px;
    align-items: center;
    box-sizing: border-box;
    border-radius: 5px;
  }
  > .on {
    display: flex;
    border: 1px solid #ddd;
    padding: 5px;
    align-items: center;

    box-sizing: border-box;
    background: #409eff;
    color: #fff;
    border-radius: 5px;
  }
}
</style>
<style scoped lang="scss">
.auth {
  :deep(.el-tree) {
    .el-tree-node:after {
      border-top: none;
    }
    .el-tree-node {
      width: fit-content;
      position: relative;
      padding-left: 16px;
    }
    //节点有间隙，隐藏掉展开按钮就好了,如果觉得空隙没事可以删掉
    .el-tree-node__expand-icon {
      display: none;
    }
    .el-tree-node__children {
      padding-left: 16px;
    }

    .el-tree-node :last-child:before {
      height: 38px;
    }

    .el-tree-node:before {
      border-left: none;
    }

    .el-tree-node:after {
      border-top: none;
    }

    .el-tree-node:before {
      content: "";
      left: -4px;
      position: absolute;
      right: auto;
      border-width: 1px;
    }

    .el-tree-node:after {
      content: "";
      left: -4px;
      position: absolute;
      right: auto;
      border-width: 1px;
    }

    .el-tree-node:before {
      border-left: 1px solid #ccc;
      bottom: 0px;
      height: 100%;
      top: -7px;
      width: 1px;
    }

    .el-tree-node:after {
      // border-top: 1px solid #ccc;
      height: 20px;
      top: 12px;
      width: 24px;
      border-bottom: 1px solid #ccc;
      border-right: none;
      border-left: none;
    }
  }
}
</style>
