import { App, nextTick, type Directive } from "vue";
import { useEventListener } from "@vueuse/core";
import ClickOutside from "./click-outside";

/**
 * 横向滚动条
 */
export default class horizontalScroll {
  private el: HTMLElement;

  constructor(nativeElement: HTMLElement) {
    this.el = nativeElement;
    this.handleWheelEvent();
  }

  handleWheelEvent() {
    let wheel = "";

    if ("onmousewheel" in this.el) {
      wheel = "mousewheel";
    } else if ("onwheel" in this.el) {
      wheel = "wheel";
    } else if ("attachEvent" in window) {
      wheel = "onmousewheel";
    } else {
      wheel = "DOMMouseScroll";
    }
    this.el["addEventListener"](wheel, this.scroll, { passive: true });
  }

  scroll = (event: any) => {
    if (this.el.clientWidth >= this.el.scrollWidth) {
      return;
    }
    this.el.scrollLeft += event.deltaY ? event.deltaY : event.detail && event.detail !== 0 ? event.detail : -event.wheelDelta;
  };
}

export function directives(app: App) {
  // 鉴权指令
  // authDirective(app);
  // 拖动指令
  dragDirective(app);
  // 缩放指令
  zoomDirective(app);
  // 点击后自动失焦指令
  blurDirective(app);
  // 表格横向拖动指令
  tableLateralDragDirective(app);

  emoji(app);

  // ClickOutside
  app.directive("ClickOutside", ClickOutside);
}

// /**
//  * 页面按钮鉴权指令
//  * @description v-auth="'name'"，name可以为：index,add,edit,del,...
//  */
// function authDirective(app: App) {
//   app.directive("auth", {
//     mounted(el, binding) {
//       if (!binding.value) return false;
//       const navTabs = useNavTabs();
//       if (navTabs.state.authNode.has(router.currentRoute.value.path)) {
//         if (!navTabs.state.authNode.get(router.currentRoute.value.path)!.some((v: string) => v == router.currentRoute.value.path + "/" + binding.value)) {
//           el.parentNode.removeChild(el);
//         }
//       }
//     },
//   });
// }

/**
 * 表格横向滚动指令
 * @description v-table-lateral-drag
 */
function tableLateralDragDirective(app: App) {
  app.directive("tableLateralDrag", {
    created(el) {
      new horizontalScroll(el.querySelector(".el-table__body-wrapper .el-scrollbar .el-scrollbar__wrap"));
    },
  });
}

/**
 * 点击后自动失焦指令
 * @description v-blur
 */
function blurDirective(app: App) {
  app.directive("blur", {
    mounted(el) {
      useEventListener(el, "focus", () => el.blur());
    },
  });
}

/**
 * 缩放指令
 * @description v-zoom="[domEl]"
 * @description domEl=要开启缩放的元素
 */
function zoomDirective(app: App) {
  app.directive("zoom", {
    mounted(el, binding) {
      if (!binding.value) return false;

      nextTick(() => {
        const zoomDom = document.querySelector(binding.value) as HTMLElement;
        const zoomhandleEl = document.createElement("div");
        zoomhandleEl.className = "zoom-handle";
        zoomhandleEl.onmouseenter = () => {
          zoomhandleEl.onmousedown = (e: MouseEvent) => {
            const x = e.clientX;
            const y = e.clientY;
            const zoomDomWidth = zoomDom.offsetWidth;
            const zoomDomHeight = zoomDom.offsetHeight;
            document.onmousemove = (e: MouseEvent) => {
              e.preventDefault(); // 移动时禁用默认事件
              const w = zoomDomWidth + (e.clientX - x) * 2;
              const h = zoomDomHeight + (e.clientY - y);

              zoomDom.style.width = `${w}px`;
              zoomDom.style.height = `${h}px`;
            };

            document.onmouseup = function () {
              document.onmousemove = null;
              document.onmouseup = null;
            };
          };
        };

        zoomDom.appendChild(zoomhandleEl);
      });
    },
  });
}

/**
 * 拖动指令
 * @description v-drag="[domEl,handleEl]"
 * @description domEl=被拖动的元素，handleEl=在此元素内可以拖动`dom`
 */
interface downReturn {
  [key: string]: number;
}
function dragDirective(app: App) {
  app.directive("drag", {
    mounted(el, binding) {
      if (!binding.value) return false;

      const dragDom = document.querySelector(binding.value[0]) as HTMLElement;
      const dragHandle = document.querySelector(binding.value[1]) as HTMLElement;

      if (!dragHandle || !dragDom) {
        return false;
      }

      dragHandle.onmouseover = () => (dragHandle.style.cursor = "move");

      function down(e: MouseEvent | TouchEvent, type: string): downReturn {
        // 鼠标按下，计算当前元素距离可视区的距离
        const disX = type === "pc" ? (e as MouseEvent).clientX - dragHandle.offsetLeft : (e as TouchEvent).touches[0].clientX - dragHandle.offsetLeft;
        const disY = type === "pc" ? (e as MouseEvent).clientY - dragHandle.offsetTop : (e as TouchEvent).touches[0].clientY - dragHandle.offsetTop;

        // body宽度
        const screenWidth = document.body.clientWidth;
        const screenHeight = document.body.clientHeight || document.documentElement.clientHeight;

        // 被拖动元素宽度
        const dragDomWidth = dragDom.offsetWidth;
        // 被拖动元素高度
        const dragDomheight = dragDom.offsetHeight;

        // 拖动限位
        const minDragDomLeft = dragDom.offsetLeft;
        const maxDragDomLeft = screenWidth - dragDom.offsetLeft - dragDomWidth;
        const minDragDomTop = dragDom.offsetTop;
        const maxDragDomTop = screenHeight - dragDom.offsetTop - dragDomheight;

        // 获取到的值带px 正则匹配替换
        let styL: string | number = getComputedStyle(dragDom).left;
        let styT: string | number = getComputedStyle(dragDom).top;
        styL = +styL.replace(/\px/g, "");
        styT = +styT.replace(/\px/g, "");

        return {
          disX,
          disY,
          minDragDomLeft,
          maxDragDomLeft,
          minDragDomTop,
          maxDragDomTop,
          styL,
          styT,
        };
      }

      function move(e: MouseEvent | TouchEvent, type: string, obj: downReturn) {
        const { disX, disY, minDragDomLeft, maxDragDomLeft, minDragDomTop, maxDragDomTop, styL, styT } = obj;

        // 通过事件委托，计算移动的距离
        let left = type === "pc" ? (e as MouseEvent).clientX - disX : (e as TouchEvent).touches[0].clientX - disX;
        let top = type === "pc" ? (e as MouseEvent).clientY - disY : (e as TouchEvent).touches[0].clientY - disY;

        // 边界处理
        if (-left > minDragDomLeft) {
          left = -minDragDomLeft;
        } else if (left > maxDragDomLeft) {
          left = maxDragDomLeft;
        }

        if (-top > minDragDomTop) {
          top = -minDragDomTop;
        } else if (top > maxDragDomTop) {
          top = maxDragDomTop;
        }

        // 移动当前元素
        dragDom.style.cssText += `;left:${left + styL}px;top:${top + styT}px;`;
      }

      dragHandle.onmousedown = (e) => {
        const obj = down(e, "pc");
        document.onmousemove = (e) => {
          move(e, "pc", obj);
        };
        document.onmouseup = () => {
          document.onmousemove = null;
          document.onmouseup = null;
        };
      };
      dragHandle.ontouchstart = (e) => {
        const obj = down(e, "app");
        document.ontouchmove = (e) => {
          move(e, "app", obj);
        };
        document.ontouchend = () => {
          document.ontouchmove = null;
          document.ontouchend = null;
        };
      };
    },
  });
}
/**
 * 拖动指令
 * @description v-drag="[domEl,handleEl]"
 * @description domEl=被拖动的元素，handleEl=在此元素内可以拖动`dom`
 */
interface InputEmoji {
  [key: string]: number;
}
const findEle = (parent: any, type: any) => {
  return parent.tagName.toLowerCase() === type ? parent : parent.querySelector(type);
};
const trigger = (el: any, type: any) => {
  // 给元素绑定事件
  const e = document.createEvent("HTMLEvents");
  e.initEvent(type, true, true);
  el.dispatchEvent(e);
};
function emoji(app: App) {
  const directive: Directive = {
    // el：指令所绑定的元素，可以用来直接操作 DOM。
    // vnode：Vue 编译生成的虚拟节点
    // created(el: HTMLElement, binding, vnode, oldVnode) {
    //   const input = [...el.getElementsByTagName("input"), ...el.getElementsByTagName("textarea")];
    //   input[0].oninput = ($event) => {
    //     // console.log($event);
    //   };
    // },
    beforeMount(el, binding, vnode, oldVnode) {},
    // mounted(el, binding, vnode, oldVnode) {},
    beforeUpdate(el, binding, vnode, oldVnode) {},
    updated(el, binding, vnode, oldVnode) {},
    beforeUnmount(el, binding, vnode, oldVnode) {},
    unmounted(el, binding, vnode, oldVnode) {},
    mounted(el, binding) {
      function bind(el: any) {
        // 指令第一次绑定到元素时调用
        // 判断是否是emoji图标
        const isEmoji = (char: string) => {
          // 表情都是2个字符
          return char.length > 1;
        };

        const emoji2empty = (str: string) => {
          // emoji图标都替换成空字符串‘’
          return Array.from(str)
            .filter((c) => !isEmoji(c))
            .join("");
        };
        const $inp = findEle(el, "input") || findEle(el, "textarea"); // 判断绑定元素是否是input输入框或者富文本输入框
        el.$inp = $inp;
        $inp.handle = function () {
          const val = $inp.value;
          $inp.value = emoji2empty(val); // 监听输入框的emoji图标转换成空
          trigger($inp, "input");
        };
        $inp.addEventListener("keyup", $inp.handle); // el添加键盘监听事件keyup
        $inp.addEventListener("blur", $inp.handle); // el添加键盘失焦事件blur
      }
      function unbind(el: any) {
        // 只调用一次，指令与元素解绑时调用。
        el.$inp.removeEventListener("keyup", el.$inp.handle);
        el.$inp.removeEventListener("blur", el.$inp.handle);
      }
    },
  };

  app.directive("emoji", directive);
}
