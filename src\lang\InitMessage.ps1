$LANGUAGE= "zh-cn", "en", "zh-tw", "ja"
# $LANGUAGE= 'az', 'de', 'pt', 'es', 'da', 'fr', 'nb-no', 'zh-tw', 'it', 'ko', 'ja', 'nl', 'vi', 'ru', 'tr', 'pt-br', 'fa', 'th', 'id', 'bg', 'pa', 'pl', 'fi', 'sv', 'el', 'sk', 'ca', 'cs', 'uk', 'tk', 'ta', 'lv', 'af', 'et', 'sl', 'ar', 'he', 'lt', 'mn', 'kk', 'hu', 'ro', 'ku', 'km', 'sr', 'eu', 'ky', 'hy-am', 'hr', 'eo', 'bn', 'ug-cn'

$CONTENT= Get-Content -Path './template.json' -Encoding utf8

for($i=0; $i -lt $LANGUAGE.Length; $i++) {
  $FILE_NAME= "/adminInfo.json"

  New-Item -Force -Path ('./language/' + $LANGUAGE[$i] + $FILE_NAME) -ItemType File
  Set-Content -Path ('./language/' + $LANGUAGE[$i] + $FILE_NAME) -Value $CONTENT -Encoding utf8
}
# for($i=0; $i -lt $LANGUAGE.Length; $i++) {
#   # $INDEX= "{`n    `"locale`": `"" + $LANGUAGE[$i] + "`"`n}"
#   New-Item -Force -Path ('./language/' + $LANGUAGE[$i]) -ItemType Directory
#   New-Item -Force -Path ('./language/' + $LANGUAGE[$i] + '/index.json') -ItemType File
#   Set-Content -Path ('./language/' + $LANGUAGE[$i] + '/index.json') -Value $CONTENT -Encoding utf8
#   # New-Item -Force -Path ('./language/' + $LANGUAGE[$i] + '/401.json') -ItemType File
#   # Set-Content -Path ('./language/' + $LANGUAGE[$i] + '/401.json') -Value $CONTENT -Encoding utf8
#   # New-Item -Force -Path ('./language/' + $LANGUAGE[$i] + '/404.json') -ItemType File
#   # Set-Content -Path ('./language/' + $LANGUAGE[$i] + '/404.json') -Value $CONTENT -Encoding utf8
#   # New-Item -Force -Path ('./language/' + $LANGUAGE[$i] + '/layoujson.json') -ItemType File
#   # Set-Content -Path ('./language/' + $LANGUAGE[$i] + '/layoujson.json') -Value $CONTENT -Encoding utf8
#   # New-Item -Force -Path ('./language/' + $LANGUAGE[$i] + '/module.json') -ItemType File
#   # Set-Content -Path ('./language/' + $LANGUAGE[$i] + '/module.json') -Value $CONTENT -Encoding utf8
#   # New-Item -Force -Path ('./language/' + $LANGUAGE[$i] + '/terminal.json') -ItemType File
#   # Set-Content -Path ('./language/' + $LANGUAGE[$i] + '/terminal.json') -Value $CONTENT -Encoding utf8
#   # New-Item -Force -Path ('./language/' + $LANGUAGE[$i] + '/utils.json') -ItemType File
#   # Set-Content -Path ('./language/' + $LANGUAGE[$i] + '/utils.json') -Value $CONTENT -Encoding utf8
# }
