import type { App } from "vue";
import { createI18n } from "vue-i18n";
import { useConfig } from "@/stores/config";

import { useSiteConfig } from "@/stores/siteConfig";

/*
 * 默认只引入 element-plus 的中英文语言包
 * 其他语言包请自行在此 import,并添加到 assignLocale 内
 * 动态 import 只支持相对路径，所以无法按需 import element-plus 的语言包
 * 但i18n的 messages 内是按需载入的
 */
// import elementAfLocale from "element-plus/es/locale/lang/af";
// import elementArLocale from "element-plus/es/locale/lang/ar";
// import elementAzLocale from "element-plus/es/locale/lang/az";
// import elementBgLocale from "element-plus/es/locale/lang/bg";
// import elementBnLocale from "element-plus/es/locale/lang/bn";
// import elementCaLocale from "element-plus/es/locale/lang/ca";
// import elementCsLocale from "element-plus/es/locale/lang/cs";
// import elementDaLocale from "element-plus/es/locale/lang/da";
// import elementDeLocale from "element-plus/es/locale/lang/de";
// import elementElLocale from "element-plus/es/locale/lang/el";
import elementEnLocale from "element-plus/es/locale/lang/en";
// import elementEoLocale from "element-plus/es/locale/lang/eo";
// import elementEsLocale from "element-plus/es/locale/lang/es";
// import elementEtLocale from "element-plus/es/locale/lang/et";
// import elementEuLocale from "element-plus/es/locale/lang/eu";
// import elementFaLocale from "element-plus/es/locale/lang/fa";
// import elementFiLocale from "element-plus/es/locale/lang/fi";
// import elementFrLocale from "element-plus/es/locale/lang/fr";
// import elementHeLocale from "element-plus/es/locale/lang/he";
// import elementHrLocale from "element-plus/es/locale/lang/hr";
// import elementHuLocale from "element-plus/es/locale/lang/hu";
// import elementHyAMLocale from "element-plus/es/locale/lang/hy-am";
// import elementIdLocale from "element-plus/es/locale/lang/id";
// import elementItLocale from "element-plus/es/locale/lang/it";
// import elementJaLocale from "element-plus/es/locale/lang/ja";
// import elementKkLocale from "element-plus/es/locale/lang/kk";
// import elementKmLocale from "element-plus/es/locale/lang/km";
// import elementKoLocale from "element-plus/es/locale/lang/ko";
// import elementKuLocale from "element-plus/es/locale/lang/ku";
// import elementKyLocale from "element-plus/es/locale/lang/ky";
// import elementLtLocale from "element-plus/es/locale/lang/lt";
// import elementLvLocale from "element-plus/es/locale/lang/lv";
// import elementMgLocale from "element-plus/es/locale/lang/mg";
// import elementMnLocale from "element-plus/es/locale/lang/mn";
// import elementNbNoLocale from "element-plus/es/locale/lang/nb-no";
// import elementNlLocale from "element-plus/es/locale/lang/nl";
// import elementPaLocale from "element-plus/es/locale/lang/pa";
// import elementPlLocale from "element-plus/es/locale/lang/pl";
// import elementPtBrLocale from "element-plus/es/locale/lang/pt-br";
// import elementPtLocale from "element-plus/es/locale/lang/pt";
// import elementRoLocale from "element-plus/es/locale/lang/ro";
// import elementRuLocale from "element-plus/es/locale/lang/ru";
// import elementSkLocale from "element-plus/es/locale/lang/sk";
// import elementSlLocale from "element-plus/es/locale/lang/sl";
// import elementSrLocale from "element-plus/es/locale/lang/sr";
// import elementSvLocale from "element-plus/es/locale/lang/sv";
// import elementTaLocale from "element-plus/es/locale/lang/ta";
// import elementThLocale from "element-plus/es/locale/lang/th";
// import elementTkLocale from "element-plus/es/locale/lang/tk";
// import elementTrLocale from "element-plus/es/locale/lang/tr";
// import elementUgLocale from "element-plus/es/locale/lang/ug-cn";
// import elementUkLocale from "element-plus/es/locale/lang/uk";
// import elementUzLocale from "element-plus/es/locale/lang/uz-uz";
// import elementViLocale from "element-plus/es/locale/lang/vi";
import elementZhCnLocale from "element-plus/es/locale/lang/zh-cn";
// import elementZhTwLocale from "element-plus/es/locale/lang/zh-tw";

export type Locale = "zh-cn" | "en" /* | "zh-tw" | "ja" */;
export interface MessagesData {
  [key: string]: string | string[] | MessagesData;
}
export type assignLocaleMessage = Record<Locale, MessagesData>;
// type assignLocaleLocale = Record<Locale, MessagesData[]>;

// 准备要合并的语言包
// const assignLocale: assignLocaleMessage = {
//   "af": [elementAfLocale],
//   "ar": [elementArLocale],
//   "az": [elementAzLocale],
//   "bg": [elementBgLocale],
//   "bn": [elementBnLocale],
//   "ca": [elementCaLocale],
//   "cs": [elementCsLocale],
//   "da": [elementDaLocale],
//   "de": [elementDeLocale],
//   "el": [elementElLocale],
//   "en": [elementEnLocale],
//   "eo": [elementEoLocale],
//   "es": [elementEsLocale],
//   "et": [elementEtLocale],
//   "eu": [elementEuLocale],
//   "fa": [elementFaLocale],
//   "fi": [elementFiLocale],
//   "fr": [elementFrLocale],
//   "he": [elementHeLocale],
//   "hr": [elementHrLocale],
//   "hu": [elementHuLocale],
//   "hy-am": [elementHyAMLocale],
//   "id": [elementIdLocale],
//   "it": [elementItLocale],
//   "ja": [elementJaLocale],
//   "kk": [elementKkLocale],
//   "km": [elementKmLocale],
//   "ko": [elementKoLocale],
//   "ku": [elementKuLocale],
//   "ky": [elementKyLocale],
//   "lt": [elementLtLocale],
//   "lv": [elementLvLocale],
//   "mg": [elementMgLocale],
//   "mn": [elementMnLocale],
//   "nb-no": [elementNbNoLocale],
//   "nl": [elementNlLocale],
//   "pa": [elementPaLocale],
//   "pl": [elementPlLocale],
//   "pt-br": [elementPtBrLocale],
//   "pt": [elementPtLocale],
//   "ro": [elementRoLocale],
//   "ru": [elementRuLocale],
//   "sk": [elementSkLocale],
//   "sl": [elementSlLocale],
//   "sr": [elementSrLocale],
//   "sv": [elementSvLocale],
//   "ta": [elementTaLocale],
//   "th": [elementThLocale],
//   "tk": [elementTkLocale],
//   "tr": [elementTrLocale],
//   "ug-cn": [elementUgLocale],
//   "uk": [elementUkLocale],
//   "uz-uz": [elementUzLocale],
//   "vi": [elementViLocale],
//   "zh-cn": [elementZhCnLocale],
//   "zh-tw": [elementZhTwLocale],
// };
/*
 * 0、加载页面语言包 import.meta.globEager 的路径不能使用变量
 * 1、vue3 setup 内只能使用 useI18n({messages:{}}) 来动态载入当前页面单独的语言包，不方便使用
 * 2、直接载入所有 /@/lang/pages/语言/*.ts 文件，若某页面有特别大量的语言配置，可在其他位置单独建立语言包文件，并在对应页面加载语言包
 */
// eslint-disable-next-line no-console
// console.dir(import("./401.json"));

function loadRequireMessage() {
  function bindTreeObj(target: MessagesData, obj: MessagesData, paths: string[]) {
    const path = paths.shift();
    if (path) {
      if (!paths.length) Object.assign(target, { [path]: obj });
      else {
        if (!Object.prototype.hasOwnProperty.call(target, path)) Object.assign(target, { [path]: {} as MessagesData });
        else if (typeof (target[path] as MessagesData) === "string") Object.assign(target, { [path]: {} as MessagesData });
        bindTreeObj(target[path] as MessagesData, obj, paths);
      }
    }
  }

  const model = require.context(`./language`, true, /\.json$/, "sync");
  const staticMessage = model.keys().reduce((p, c) => {
    const exec = (/^\.\/(?<local>((?!\/).)*)\/(?<path>.*)\.json/.exec(c) || {}).groups || {};
    if (exec) {
      if (!Object.prototype.hasOwnProperty.call(p, exec.local)) Object.assign(p, { [exec.local as Locale]: {} as MessagesData });
      else if (typeof (p[exec.local as Locale] as MessagesData) === "string") Object.assign(p, { [exec.local as Locale]: {} as MessagesData });
      bindTreeObj(p[exec.local as Locale] as MessagesData, model(c), exec.path.split("/"));
    }
    return p;
  }, {} as assignLocaleMessage);
  return {
    "zh-cn": { ...staticMessage["zh-cn"], ...(elementZhCnLocale as MessagesData) },
    "en": { ...staticMessage["en"], ...(elementEnLocale as MessagesData) },
  };
}

// assignLocale["zh-cn"].push(getLangFileMessage(import.meta.glob("./language/zh-cn/(**)?.json", { eager: true }), "zh-cn"));
// assignLocale["en"].push(getLangFileMessage(import.meta.glob("./language/en/(**)?.json", { eager: true }), "en"));
// assignLocale["az"].push(getLangFileMessage(import.meta.glob("./language/az/(**)?.json", { eager: true }), "az"));
// assignLocale["de"].push(getLangFileMessage(import.meta.glob("./language/de/(**)?.json", { eager: true }), "de"));
// assignLocale["pt"].push(getLangFileMessage(import.meta.glob("./language/pt/(**)?.json", { eager: true }), "pt"));
// assignLocale["es"].push(getLangFileMessage(import.meta.glob("./language/es/(**)?.json", { eager: true }), "es"));
// assignLocale["da"].push(getLangFileMessage(import.meta.glob("./language/da/(**)?.json", { eager: true }), "da"));
// assignLocale["fr"].push(getLangFileMessage(import.meta.glob("./language/fr/(**)?.json", { eager: true }), "fr"));
// assignLocale["nb-no"].push(getLangFileMessage(import.meta.glob("./language/nb-no/(**)?.json", { eager: true }), "nb-no"));
// assignLocale["zh-tw"].push(getLangFileMessage(import.meta.glob("./language/zh-tw/(**)?.json", { eager: true }), "zh-tw"));
// assignLocale["it"].push(getLangFileMessage(import.meta.glob("./language/it/(**)?.json", { eager: true }), "it"));
// assignLocale["ko"].push(getLangFileMessage(import.meta.glob("./language/ko/(**)?.json", { eager: true }), "ko"));
// assignLocale["ja"].push(getLangFileMessage(import.meta.glob("./language/ja/(**)?.json", { eager: true }), "ja"));
// assignLocale["nl"].push(getLangFileMessage(import.meta.glob("./language/nl/(**)?.json", { eager: true }), "nl"));
// assignLocale["vi"].push(getLangFileMessage(import.meta.glob("./language/vi/(**)?.json", { eager: true }), "vi"));
// assignLocale["ru"].push(getLangFileMessage(import.meta.glob("./language/ru/(**)?.json", { eager: true }), "ru"));
// assignLocale["tr"].push(getLangFileMessage(import.meta.glob("./language/tr/(**)?.json", { eager: true }), "tr"));
// assignLocale["pt-br"].push(getLangFileMessage(import.meta.glob("./language/pt-br/(**)?.json", { eager: true }), "pt-br"));
// assignLocale["fa"].push(getLangFileMessage(import.meta.glob("./language/fa/(**)?.json", { eager: true }), "fa"));
// assignLocale["th"].push(getLangFileMessage(import.meta.glob("./language/th/(**)?.json", { eager: true }), "th"));
// assignLocale["id"].push(getLangFileMessage(import.meta.glob("./language/id/(**)?.json", { eager: true }), "id"));
// assignLocale["bg"].push(getLangFileMessage(import.meta.glob("./language/bg/(**)?.json", { eager: true }), "bg"));
// assignLocale["pa"].push(getLangFileMessage(import.meta.glob("./language/pa/(**)?.json", { eager: true }), "pa"));
// assignLocale["pl"].push(getLangFileMessage(import.meta.glob("./language/pl/(**)?.json", { eager: true }), "pl"));
// assignLocale["fi"].push(getLangFileMessage(import.meta.glob("./language/fi/(**)?.json", { eager: true }), "fi"));
// assignLocale["sv"].push(getLangFileMessage(import.meta.glob("./language/sv/(**)?.json", { eager: true }), "sv"));
// assignLocale["el"].push(getLangFileMessage(import.meta.glob("./language/el/(**)?.json", { eager: true }), "el"));
// assignLocale["sk"].push(getLangFileMessage(import.meta.glob("./language/sk/(**)?.json", { eager: true }), "sk"));
// assignLocale["ca"].push(getLangFileMessage(import.meta.glob("./language/ca/(**)?.json", { eager: true }), "ca"));
// assignLocale["cs"].push(getLangFileMessage(import.meta.glob("./language/cs/(**)?.json", { eager: true }), "cs"));
// assignLocale["uk"].push(getLangFileMessage(import.meta.glob("./language/uk/(**)?.json", { eager: true }), "uk"));
// assignLocale["tk"].push(getLangFileMessage(import.meta.glob("./language/tk/(**)?.json", { eager: true }), "tk"));
// assignLocale["ta"].push(getLangFileMessage(import.meta.glob("./language/ta/(**)?.json", { eager: true }), "ta"));
// assignLocale["lv"].push(getLangFileMessage(import.meta.glob("./language/lv/(**)?.json", { eager: true }), "lv"));
// assignLocale["af"].push(getLangFileMessage(import.meta.glob("./language/af/(**)?.json", { eager: true }), "af"));
// assignLocale["et"].push(getLangFileMessage(import.meta.glob("./language/et/(**)?.json", { eager: true }), "et"));
// assignLocale["sl"].push(getLangFileMessage(import.meta.glob("./language/sl/(**)?.json", { eager: true }), "sl"));
// assignLocale["ar"].push(getLangFileMessage(import.meta.glob("./language/ar/(**)?.json", { eager: true }), "ar"));
// assignLocale["he"].push(getLangFileMessage(import.meta.glob("./language/he/(**)?.json", { eager: true }), "he"));
// assignLocale["lt"].push(getLangFileMessage(import.meta.glob("./language/lt/(**)?.json", { eager: true }), "lt"));
// assignLocale["mn"].push(getLangFileMessage(import.meta.glob("./language/mn/(**)?.json", { eager: true }), "mn"));
// assignLocale["kk"].push(getLangFileMessage(import.meta.glob("./language/kk/(**)?.json", { eager: true }), "kk"));
// assignLocale["hu"].push(getLangFileMessage(import.meta.glob("./language/hu/(**)?.json", { eager: true }), "hu"));
// assignLocale["ro"].push(getLangFileMessage(import.meta.glob("./language/ro/(**)?.json", { eager: true }), "ro"));
// assignLocale["ku"].push(getLangFileMessage(import.meta.glob("./language/ku/(**)?.json", { eager: true }), "ku"));
// assignLocale["km"].push(getLangFileMessage(import.meta.glob("./language/km/(**)?.json", { eager: true }), "km"));
// assignLocale["sr"].push(getLangFileMessage(import.meta.glob("./language/sr/(**)?.json", { eager: true }), "sr"));
// assignLocale["eu"].push(getLangFileMessage(import.meta.glob("./language/eu/(**)?.json", { eager: true }), "eu"));
// assignLocale["ky"].push(getLangFileMessage(import.meta.glob("./language/ky/(**)?.json", { eager: true }), "ky"));
// assignLocale["hy-am"].push(getLangFileMessage(import.meta.glob("./language/hy-am/(**)?.json", { eager: true }), "hy-am"));
// assignLocale["hr"].push(getLangFileMessage(import.meta.glob("./language/hr/(**)?.json", { eager: true }), "hr"));
// assignLocale["eo"].push(getLangFileMessage(import.meta.glob("./language/eo/(**)?.json", { eager: true }), "eo"));
// assignLocale["bn"].push(getLangFileMessage(import.meta.glob("./language/bn/(**)?.json", { eager: true }), "bn"));
// assignLocale["ug-cn"].push(getLangFileMessage(import.meta.glob("./language/ug-cn/(**)?.json", { eager: true }), "ug-cn"));
// const messages = Object.keys(assignLocale).reduce<Partial<assignLocaleMessage>>((p, c) => ({ ...p, [c]: {} }), {}) as assignLocaleMessage;
// for (const locale in assignLocale) {
//   if (Object.prototype.hasOwnProperty.call(assignLocale, locale)) {
//     const message: MessagesData[] = assignLocale[locale as Locale] instanceof Array ? assignLocale[locale as Locale] : [];
//     Object.assign(messages[locale as Locale], ...message);
//     // i18n.global.setLocaleMessage(locale, Object.assign({}, ...message));
//   }
// }

export const i18n = createI18n({
  locale: localStorage.getItem("systemLang") || "zh-cn",
  fallbackLocale: ["zh-cn", "en", "zh-tw", "ja"],
  legacy: false,
  missing(locale, key, instance, type) {
    switch (type) {
      case "translate": {
        const keys = key.split(".");
        return keys[keys.length - 1];
      }
      default: {
        return;
      }
    }
  },
  fallbackRoot: false,
  silentTranslationWarn: true,
  silentFallbackWarn: true,
  // fallbackRootWithEmptyString: true,
  globalInjection: true, // 挂载$t,$d等到全局
  pluralizationRules: {} /* 多元化 */,
  datetimeFormats: {} /* 日期时间格式 */,
  numberFormats: {} /* 数字格式 */,
  messages: loadRequireMessage() /* 剧本 */,
});

export function loadLang(app: App) {
  const config = useConfig();

  // 合并语言包(含element-puls、页面语言包)
  i18n.global.locale.value = config.lang.defaultLang as Locale;
  app.use(i18n);
  return i18n;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
// function getLangFileMessage(messageList: Record<string, () => unknown>, locale: string) {
//   let msg: anyObj = {};
//   locale = "/" + locale;
//   for (const path in messageList) {
//     let message: unknown = messageList[path];
//     if ((message as { default?: Record<string, unknown>; [key: string]: unknown }).default) message = (message as Record<string, unknown>).default;
//     //  获取文件名
//     const pathName = path.slice(path.lastIndexOf(locale) + (locale.length + 1), path.lastIndexOf("."));
//     if (pathName.indexOf("/") > 0) {
//       msg = handleMsglist(msg, message as anyObj, pathName);
//     } else if (pathName) {
//       msg[pathName] = message as anyObj;
//     } else {
//       msg[pathName] = message as anyObj;
//       // Object.assign(msg, message)
//     }
//   }
//   return msg;
// }

// export function handleMsglist(msg: anyObj, mList: anyObj, pathName: string) {
//   const pathNameTmp = pathName.split("/");
//   let obj: anyObj = {};
//   for (let i = pathNameTmp.length - 1; i >= 0; i--) {
//     if (i == pathNameTmp.length - 1) {
//       obj = {
//         [pathNameTmp[i]]: mList,
//       };
//     } else {
//       obj = {
//         [pathNameTmp[i]]: obj,
//       };
//     }
//   }
//   return mergeMsg(msg, obj);
// }

// export function mergeMsg(msg: anyObj, obj: anyObj) {
//   for (const key in obj) {
//     if (typeof msg[key] == "undefined") {
//       msg[key] = obj[key];
//     } else if (typeof msg[key] == "object") {
//       msg[key] = mergeMsg(msg[key], obj[key]);
//     }
//   }
//   return msg;
// }

export async function editDefaultLang(lang: string): Promise<void> {
  const config = useConfig();
  config.setLang(lang);

  if (lang !== localStorage.getItem("systemLang")) {
    localStorage.setItem("systemLang", lang);
    /*
     * 语言包是按需加载的,比如默认语言为中文,则只在app实例内加载了中文语言包
     * 查阅文档无数遍,无耐接受当前的 i18n 版本并不支持动态添加语言包(或需要在 setup 内动态添加,无法满足全局替换的需求)
     * 故 reload;如果您有已经实现的无需一次性加载全部语言包且无需 reload 的方案,请一定@我
     */

    i18n.global.locale.value = lang as Locale;
    try {
      const siteConfig = useSiteConfig();
      const router = (await import("@/router/index")).default;
      router.replace({
        name: `${siteConfig.current}Loading`,
        query: { ...router.currentRoute.value.query },
        params: {
          path: router.currentRoute.value.path.split("/").filter((v) => `/${v}` !== siteConfig.baseInfo?.path),
        },
      });
    } catch (error) {
      /*  */
    } finally {
      location.reload();
    }
  }
}
