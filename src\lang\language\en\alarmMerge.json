{"Actions": "Actions", "AlarmCollection": "Alarm Collection", "AlarmMergingRules": "Alarm Merging Rules", "AlarmPushToAlarmBoard": "Alarm Push to Alarm Board", "AppliesToAlarmsAffectedByTopologicalRelationshipsIncludingDeviceOrInterfaceDownDeviceSymptoms": "Applies to alarms affected by topological relationships, including device or interface down, device symptoms.", "AssignDevice": "Assign <PERSON>", "AssignRegion": "Assign Region", "AssignToDevice": "Assign to <PERSON><PERSON>", "AssignToRegion": "Assign to Region", "AssignToVenue": "Assign to <PERSON><PERSON><PERSON>", "AssignVenue": "Assign <PERSON>", "AutomaticGenerationOfTicketsFromAlarms": "Automatic Generation of Tickets from Alarms", "AutomaticTicketConfiguration": "Automatic Ticket Configuration", "AutomaticTicketConfigurationIsCustomerSpecificOneClickConfigurationForAllDevicesUnderUser": "Automatic ticket configuration is customer-specific. The alarm configuration rules for devices under each customer can be customized.With one-click configuration, alarms generated by all devices under a user can automatically create tickets.", "BeforeTicketIsResolvedAndAlarmCollectionNotClosedAlarmsOfSameTypeFromSameDeviceWillBeMergedIntoTicket": "Before the ticket is resolved and alarm collection is not closed, alarms of the same type from the same device will be merged into that ticket.", "CheckedBoxScenario": "If the box is checked: Default rules are used. The automatic ticket rules will follow the default rules, and custom rules will not take effect even if configured.", "ConfigureTicketLockDuration": "Users can configure the [Ticket Lock Duration]. During the ticket lock period, the ticket cannot be updated or processed. Before the ticket is closed or alarm collection is stopped, alarms will be merged into the same ticket according to the configured rules.", "ConfirmToUseAutomaticTicketRulesOnceCheckedTheTicketAlarmMergingConfigurationRulesBelowWillNotTakeEffect": "Confirm to use automatic ticket rules? Once checked, the ticket alarm merging configuration rules below will not take effect.", "ConfirmUnassignCurrentData": "Are you sure you want to unassign the current data?", "CreateNewPolicy": "Create New Policy", "CustomerDefault": "Customer <PERSON><PERSON><PERSON>", "DefaultSystemRules": "The default system rules are:", "Description": "Description", "DeviceDefault": "<PERSON><PERSON>", "DeviceName": "Device Name", "DoNotUseDefaultRulesOptionInConfigurationPage": "On the automatic ticket configuration page, users can uncheck the [Use Default Automatic Ticket Rules of Ticket Group] option to not use default rules, in which case custom automatic ticket rules need to be configured.", "DowngradeConfiguration": "Downgrade Configuration：", "DuringTicketLockDurationAlarmsFromSameDeviceWillBeMergedIntoSameTicket": "During the ticket lock duration, alarms from the same device will be merged into the same ticket.", "DuringWorkingHours": "During Working Hours", "Friday": "Friday", "GeneratedAlarmsAreMergedIntoSameTicketAccordingToConfiguredRules": "Generated alarms are merged into the same ticket according to the configured rules.", "IsActive": "Is Active", "IsDefault": "<PERSON>", "MergeAlarmsIntoExistingGeneratedTickets": "Merge alarms into existing generated tickets", "MergeTopologyAlarmsIntoExistingGeneratedTickets": "Merge topology alarms into existing generated tickets", "MergeTopologyAlarmsIntoSameTicket": "Merge topology alarms into the same ticket", "MergingRules": "Merging Rules", "MinimumPriorityCanOnlyBeDowngradedToP7": "Minimum priority can only be downgraded to P7", "Monday": "Monday", "Name": "Name", "NewAlarmsMergedIntoExistingOpenTickets": "If a previously generated ticket is open, not closed, and the [Collect Alarms] button is not turned off, new alarms from the same device will be merged into this ticket. Otherwise, a new ticket will be created.", "OutsideWorkingHours": "Outside Working Hours", "Please select the order group": "Please select the order group", "PleaseSelect": "Please Select ", "PolicyName": "Policy Name", "PriorityAdjustmentPolicyConfiguration": "Priority Adjustment Policy Configuration", "ProhibitPriorityBelow": "Prohibit Priority Below：", "RegardlessOfMergingRulesSubDeviceTopologyAlarmsWillAlwaysBeMergedIntoParentDeviceUnresolvedAlarmTickets": "Regardless of merging rules, sub-device topology alarms will always be merged into the parent device's unresolved alarm tickets.", "RegionName": "Region Name", "SNMPTrapAlarmsRelatedToTopologyRelationshipsWillBeMergedIntoSameTicketAsAssociatedDeviceStatusAlarmsEgColdStart": "SNMP Trap alarms related to topology relationships will be merged into the same ticket as associated device status alarms (e.g., 'cold start').", "SNMPTrapTopologyAlarmMerging": "SNMP Trap Topology Alarm Merging", "SameDeviceAlarmsGenerateSameTicket": "Alarms from the same device will generate the same ticket.", "Saturday": "Saturday", "SaturdayDowngradeConfiguration": "Saturday Downgrade Configuration", "SelectPriorityDowngradeLevel": "Select Priority Downgrade Level：", "SelectTimeZone": "Select Time Zone：", "ShowAllDevice": "Show All Device", "ShowAllRegion": "Show All Region", "ShowAllVenue": "Show All Venue", "SubDeviceTopologyAlarmsAlwaysMerged": "Sub-device Topology Alarms Always Merged", "SubDeviceTopologyAlarmsMergedIntoParentDeviceUnresolvedTickets": "Topology alarms from sub-devices will always be merged into the unresolved topology alarm tickets of the parent device.", "Sunday": "Sunday", "SundayDowngradeConfiguration": "Sunday Downgrade Configuration", "TheAlarm": "The Alarm ", "TheAlarmsWillBeMergedIntoSameWorkOrder": "The alarms will be merged into the same work order", "Thursday": "Thursday", "TicketAlarmMergingConfiguration": "Ticket Alarm Merging Configuration", "TicketCreationRequiresAlarmCollectionAndAutomaticTicketGeneration": "Tickets will only be created if alarm collection and automatic ticket generation are configured for the customer.", "TicketLockDuration": "Ticket Lock Duration", "TicketLockDurationIs15Seconds": "The ticket lock duration is 15 seconds.", "TopologyAlarmMergingRules": "Topology Alarm Merging Rules", "TopologyAlarmsFromSameDeviceWillBeMergedIntoExistingTicketsGeneratedForDeviceTopologyAlarms": "Topology alarms from the same device will be merged into existing tickets generated for that device's topology alarms.", "Tuesday": "Tuesday", "Unassign": "Unassign", "UncheckedBoxScenario": "If the box is unchecked: If no custom automatic ticket rules are configured for the customer and default rules are not used, the automatic ticket feature will not work. When default rules are not used, custom configuration is required for alarms to automatically generate tickets.", "Use the priority strategy for order groups": "Use the priority strategy for order groups", "UseDefaultAutomaticTicketRulesOfTicketGroup": "Use Default Automatic Ticket Rules of Ticket Group", "UseDefaultRulesOptionInConfigurationPage": "On the automatic ticket configuration page, users can check the [Use Default Automatic Ticket Rules of Ticket Group] option to use default rules, in which case custom rules will not take effect.", "VenueName": "Venue Name", "Wednesday": "Wednesday", "WeekdayDowngradePolicyConfiguration": "Weekday Downgrade Policy Configuration", "WhenTheWorkOrderIsLocked": "When the work order is locked", "WhenTicketIsLockedAlarmsFromSameDeviceWillBeMergedIntoSameTicket": "When a ticket is locked, alarms from the same device will be merged into the same ticket.", "WillBeMergedIntoSameTicket": "Will be merged into the same ticket", "WithinTheDurationOfTheWorkOrderLock": "Within the duration of the work order lock", "WorkingHours": "Working Hours："}