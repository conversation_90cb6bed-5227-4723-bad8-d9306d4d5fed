﻿{
  "Add as menu only": "add as menu only",
  "Add as route only": "just add as route",
  "Component path": "component path",
  "English name, which does not need to start with `/admin`, such as auth/menu": "English name, no need to start with `/admin`, such as: auth/menu",
  "Extended properties": "extended attributes",
  "Icon": "icon",
  "It will be registered as the web side routing name and used as the server side API authentication": "It will be registered as the route name on the web side and used as the server-side API authentication",
  "Link address": "link address",
  "Menu type": "menu type",
  "Menu type link (offsite)": "link (outside)",
  "Menu type tab": "Tab",
  "Please enter the URL address of the link or iframe": "Please enter the URL address of the link or Iframe",
  "Please enter the correct URL": "Please enter the correct URL",
  "Please enter the weight of menu rule (sort by)": "Please enter menu rule weight (sort by)",
  "Routing path": "routing path",
  "Rule Icon": "rule icon",
  "Rule comments": "Rule Remarks",
  "Rule name": "rule name",
  "Rule title": "rule title",
  "Rule type": "rule type",
  "Rule weight": "rule weight",
  "Superior menu rule": "parent menu rules",
  "The superior menu rule cannot be the rule itself": "A parent menu rule cannot be a rule itself",
  "The web side routing path (path) does not need to start with `/admin`, such as auth/menu": "The web-side routing path (path), does not need to start with `/admin`, such as: auth/menu",
  "Use in controller `get_ route_ Remark()` function, which can obtain the value of this field for your own use, such as the banner file of the console": "Use the `get_route_remark()` function in the controller to obtain the value of this field for your own use, such as the banner copy of the console",
  "Web side component path, please start with /src, such as: /src/views/backend/dashboard": "Web-side component path, please start with /src, such as: /src/views/backend/dashboard.vue",
  "cache": "cache",
  "extend Title": "For example, if `auth/menu` is only added as a route, then `auth/menu`, `auth/menu/:a`, `auth/menu/:b/:c` can be added only as menus",
  "name": "name",
  "none": "none",
  "title": "title",
  "type": "Types of",
  "type button": "page button",
  "type menu": "Menu Item",
  "type menu_dir": "menu list"
}
