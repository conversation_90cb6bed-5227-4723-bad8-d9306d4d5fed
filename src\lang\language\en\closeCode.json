{"Close code configuration": "Close code configuration", "Newclosecode": "New close code", "editclosecode": "Edit close code", "Closecodename": "Close code name", "describe": "Description", "status": "Status", "subclass": "Subclass", "search": " Search", "Security Container": "Security Container", "IsDefault": "<PERSON>", "name": "Name", "Select a secure directory": "Select a secure directory", "Please enter name": "Please enter name", "Please enter a description": "Please enter a description", "Please enter a close code name": "Please enter a close code name", "The customer already has a default closure code, and modifying it will overwrite the original closure code. Do you want to modify it?": "The customer already has a default closure code, and modifying it will overwrite the original closure code. Do you want to modify it?", "reminder": "Reminder", "confirm": "Confirm", "cancel": "Cancel", "New success": "New success", "modify successfully": "Modify successfully", "Configuration": " Configuration", "Close code": "Close code", "Confirm to delete": "Confirm to delete", "Close the code?": "Close the code?", "Event closure code": "Event closure code", "Service request closure code": "Service request closure code", "Problem closure code": "Problem closure code", "Change closure code": "Change closure code", "Publish and close code": "Publish and close code", "Delete completed code": "Delete completed code", "Remove from report": "Remove from report"}