﻿{
  "Advanced Configuration": "advanced configuration",
  "Advanced Fields": "advanced field",
  "Allow NULL": "Allow NULL",
  "Are you sure to delete the generated CRUD code?": "Confirm to delete generated CRUD code?",
  "Associated Data Table": "Linked Data Table",
  "Auto increment": "auto increment",
  "Base Fields": "base field",
  "CRUD record": "CRUD records",
  "Common": "commonly used",
  "Common Fields": "common field",
  "Confirm CRUD code generation": "Confirm to generate CRUD code",
  "Continue building": "continue to generate",
  "Controller position": "controller location",
  "Data Model Location": "Data Model Location",
  "Data Table Notes": "Data Sheet Notes",
  "Delete Code": "delete code",
  "Drag the left element here to start designing CRUD": "Drag the left element here to start designing CRUD",
  "Drop down label field": "drop down label field",
  "Drop down value field": "drop down value field",
  "Fast experience": "Quick experience",
  "Field Defaults": "field default",
  "Field Form Properties": "Field form properties",
  "Field Name": "field name",
  "Field Properties": "field properties",
  "Field Table Properties": "Field Form Properties",
  "Field Type": "Field Type",
  "Field comments (CRUD dictionary)": "Field annotations (CRUD dictionary)",
  "Fields as Table Columns": "fields as table columns",
  "Fields as form items": "fields as form items",
  "Fields displayed in the table": "Fields displayed in the form",
  "For example: `user table` will be generated into `user management`": "For example: `Member Table` will be generated as `Member Management`",
  "Generate CRUD code": "Generate CRUD code",
  "Generated Controller Location": "Generated controller location",
  "Generated Data Model Location": "Generated data model location",
  "Generated Validator Location": "Generated validator location",
  "If it is left blank, the model of the associated table will be generated automatically If the table already has a model, it is recommended to select it to avoid repeated generation": "Leave it blank to automatically generate the model of the associated table. If the table already has a model, it is recommended to select it to avoid repeated generation",
  "It is irreversible to give up the design Are you sure you want to give up?": "Abandoning the design is irreversible, are you sure you want to give up?",
  "Name of the data table": "the name of the data table",
  "New background CRUD from zero": "New background CRUD from scratch",
  "Please design the primary key field!": "Please design the primary key field!",
  "Please enter SQL": "Please enter SQL",
  "Please enter the data table name!": "Please enter the data table name!",
  "Please enter the table creation SQL": "Please enter the SQL to create the table",
  "Please select a data table": "Please select a data sheet",
  "Please select a field from the left first": "Please select a field from the left first",
  "Please select the controller of the data table": "Please select the controller for the data table",
  "Please select the data model location of the data table": "Please select the data model location for the data table",
  "Please select the fields displayed in the table": "Please select the fields to display in the form",
  "Please select the label field of the select component": "Please select the label field of the select component",
  "Please select the value field of the select component": "Please select the value field of the select component",
  "Remote drop-down association information": "Remote drop-down associated information",
  "Select Data Table": "select data table",
  "Select a designed data table from the database": "Select a designed data table from the database",
  "Start CRUD design with this record?": "Starting CRUD design with this record?",
  "Start with previously generated CRUD code": "Start with previously generated CRUD code",
  "Table Default Sort Fields": "Table default sort field",
  "Table Quick Search Fields": "Form Quick Search Field",
  "The controller already exists Continuing to generate will automatically overwrite the existing code!": "The controller already exists, continuing to generate will automatically overwrite the existing code!",
  "The data table already exists Continuing to generate will automatically delete the original table and create a new one!": "The data table already exists, continuing to generate will automatically delete the original table and create a new data table!",
  "The field comment will be used as the CRUD dictionary, and will be identified as the field title before the colon, and as the data dictionary after the colon": "The field comment will be used as a CRUD dictionary, before the colon will be recognized as the field title, and after the colon will be recognized as a data dictionary",
  "The remote pull-down will request the corresponding controller to obtain data, so it is recommended that you create the CRUD of the associated table": "The remote pull-down will request the corresponding controller to obtain the data, so it is recommended to generate the CRUD of the associated table first",
  "There can only be one primary key field": "There can be only one primary key field.",
  "Unsigned": "unsigned",
  "WEB end view directory": "WEB end view directory",
  "You can directly enter null, 0, empty string": "You can directly input null, 0, empty string",
  "copy": "copy design",
  "create": "new build",
  "data sheet": "data sheet",
  "decimal point": "decimal point",
  "experience 1 1": "get ready",
  "experience 1 2": "development environment",
  "experience 2 1": "click on this page",
  "experience 2 2": "select data table",
  "experience 2 3": ", and select",
  "experience 3 1": "click",
  "experience 3 2": "Generate CRUD code",
  "experience 3 3": ", click",
  "experience 3 4": "continue to generate",
  "field comment": "field comment",
  "file-multi": "Multiple file upload",
  "generate": "generated as",
  "give up": "give up",
  "image-multi": "Multi-select upload of pictures",
  "length": "length",
  "operator": "common search operator",
  "relation-fields": "Association table display fields",
  "remote-controller": "Controller for relational tables",
  "remote-field": "Remote drop-down label field",
  "remote-model": "The model of the association table",
  "remote-pk": "Remote drop-down value field",
  "remote-table": "Linked Data Table",
  "remote-url": "Remote dropdown URL",
  "render": "rendering scheme",
  "rows": "Rows",
  "select-multi": "drop down box multiple choice",
  "show": "display in table column",
  "sort order": "sort by",
  "sort order asc": "asc-order",
  "sort order desc": "desc - reverse order",
  "sortable": "field sort",
  "start": "start",
  "step": "step value",
  "table create SQL": "Create table SQL",
  "timeFormat": "format method",
  "validator": "validation rules",
  "validatorMsg": "Verification error prompt",
  "width": "table column width"
}
