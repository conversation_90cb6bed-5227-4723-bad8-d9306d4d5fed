﻿{
  "Checkbox:opt0=Option1,opt1=Option2": "Checkbox: opt0=option one, opt1=option two",
  "Disable Search": "disable search",
  "If it is not input, it will be automatically analyzed by the controller": "If not entered, it will be automatically parsed by the controller",
  "If left blank, the verifier title attribute will be filled in automatically": "If left blank, the title attribute of the validator will be automatically filled in (if you don’t understand, please fill in the complete error message directly)",
  "Primary key": "primary key",
  "Radio:opt0=Option1,opt1=Option2": "Radio button: opt0=option one, opt1=option two",
  "Remote Select (association table)": "Remote dropdown (association table)",
  "Select:0=Option1,1=Option2": "Drop-down box: 0=option one, 1=option two",
  "Status:0=Disabled,1=Enabled": "Status: 0=disabled, 1=enabled",
  "Switch:0=off,1=on": "Switch: 0=off, 1=on",
  "Time date (timestamp storage)": "time date (time stamp storage)",
  "Weight (automatically generate drag sort button)": "Weight (automatically generate drag and drop sorting buttons)",
  "Weight (drag and drop sorting)": "Weight (drag sort)",
  "remarks": "Remark"
}
