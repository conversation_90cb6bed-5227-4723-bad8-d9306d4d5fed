﻿{
  "Acknowledgement required": "Acknowledgement required",
  "Active": "Active",
  "Alert classifications": "Alert classifications",
  "Asset numbers": "Asset numbers",
  "Business Unit": "Business Unit",
  "Configuration information": "Configuration information",
  "Conflicting IP": "Conflicting IP",
  "Conflicting customer": "Conflicting customer",
  "Conflicting device": "Conflicting device",
  "Deliver": "Deliver",
  "Description": "Description",
  "Device Details": "Device Details",
  "Device groups": "Device groups",
  "Device informations": "Device informations",
  "Device name cannot be empty": "Device name cannot be empty",
  "Device types": "Device types",
  "Device vendors": "Device vendors",
  "Dynamic IP": "Dynamic IP",
  "External Id": "External Id",
  "IP Address Conflict, please modify the conflicting device IP": "IP Address Conflict, please modify the conflicting device IP",
  "IP address": "IP address",
  "Importance": "Importance",
  "Location": "Location",
  "Login authentication": "Login authentication",
  "Model numbers": "Model numbers",
  "Monitoring source": "Monitoring source",
  "Name": "Name",
  "New Device": "New Device",
  "Nms ticketing": "Nms ticketing",
  "Offline Time": "Offline Time",
  "Offline time": "Offline time",
  "Online Time": "Online Time",
  "Please enter IP address": "Please enter IP address",
  "Please enter the Business Unit": "Please enter the Business Unit",
  "Please enter the alert classifications": "Please enter the alert classifications",
  "Please enter the asset numbers": "Please enter the asset numbers",
  "Please enter the correct IP address": "Please enter the correct IP address",
  "Please enter the description": "Please enter the description",
  "Please enter the device name": "Please enter the device name",
  "Please enter the external Id": "Please enter the external Id",
  "Please enter the model numbers": "Please enter the model numbers",
  "Please enter the serial numbers": "Please enter the serial numbers",
  "Please enter the tag": "Please enter the tag",
  "Region": "Region",
  "Resolve IPs from device names and set up DNS monitoring and groups for clients": "Resolve IPs from device names and set up DNS monitoring and groups for clients",
  "Select device groups": "Select device groups",
  "Select device types": "Select device types",
  "Select location": "Select location",
  "Select monitoring source": "Select monitoring source",
  "Select offline time": "Select offline time",
  "Select online time": "Select online time",
  "Select region": "Select region",
  "Select vendors": "Select vendors",
  "Serial numbers": "Serial numbers",
  "Tag": "Tag",
  "The location cannot be empty": "The location cannot be empty",
  "The region cannot be empty": "The region cannot be empty",
  "Time Zone": "Time Zone",
  "Tips": "Tips",
  "model numbers discovered": "model numbers discovered;",
  "serial numbers discovered": "serial numbers discovered;",
  "Default": "Default",
  "through Connector": "through Connector",
  "require TFA": "require TFA",
  "direct": "Current account does not require authentication, direct login",
  "Input": "Input the password of the platform to authenticate the user's identity",
  "Two-factor": "Two-factor authentication of identity is also required"
}
