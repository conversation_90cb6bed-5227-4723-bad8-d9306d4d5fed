﻿{
  "Device details": "Device details",
  "Details": "Details",
  "Basic information": "Basic information",
  "Poller": "Poller",
  "Monitoring": "Monitoring",
  "Name": "Name",
  "Duration": "Duration",
  "Status": "Status",
  "occurrence time": "occurrence time",
  "Overview of the situation": "Overview of the situation",
  "Lastest alert": "Lastest alert",
  "Snmp Protocol": "Snmp Protocol",
  "System Start Time": "System Start Time",
  "System Details": "System Details",
  "System Name": "System Name",
  "System OID": "System OID",
  "System Version": "System Version",
  "Manufacturer": "Manufacturer",
  "Contacts": "Contacts",
  "Priority": "Priority",
  "Summary": "Summary",
  "Created": "Created",
  "Modified": "Modified",
  "Support Notes": "Support Notes",
  "Customer Support Instructions": "Customer Support Instructions",
  "Attention: Use until": "Attention: Use until",
  "Attention: From": "Attention: From",
  "start using": "start using",
  "Service Numbers": "Service Numbers",
  "Service Number": "Service Number",
  "Please enter the service number": "Please enter the service number",
  "Add Service Number": "Add Service Number",
  "Edit Service Number": "Edit Service Number",
  "Line Vendor": "Line Vendor",
  "Select Line Vendor": "Select Line Vendor",
  "Type": "Type",
  "Bandwidth": "Bandwidth",
  "Numbers": "Numbers",
  "Product": "Product",
  "Port": "Port",
  "Service Lever": "Service Lever",
  "SLA": "SLA",
  "Bind SLA service": "Bind SLA service",
  "Select SLA service": "Select SLA service",
  "Please Select":"Please Select",
  "SLA name": "SLA name",
  "Description": "Description",
  "Operate": "Operate",
  "Unassign": "Unassign",
  "Alerts": "Alerts",
  "Urgency": "Urgency",
  "Alert": "Alert",
  "Ticket": "Ticket",
  "Timestamp": "Timestamp",
  "Acked By": "Acked By",
  "Journals": "Journals",
  "Add": "Add",
  "New Journal": "New Journal",
  "Edit Journal": "Edit Journal",
  "Operation successful": "Operation successful",
  "Files": "Files",
  "Upload Files": "Upload Files",
  "Drag the file here or click here to upload": "Drag the file here or click here to upload",
  "Security Container": "Security Container",
  "History Alarm":"History Alarm",
  "Alarm Confirmation Time":"Alarm Confirmation Time",
  "Files Name": "Files Name",
  "size": "size",
  "Redo Log File": "Redo Log File",
  "Builder": "Builder",
  "Please add the uploaded file":"Please add the uploaded file",
  "Drag the file here or":"Drag the file here or",
  "Click here to upload":"Click here to upload",
  "Work order priority matrix":"Work order priority matrix",
  "Priority matrix":"Priority matrix",
  "Equipment importance":"Equipment importance",
  "Alarm urgency":"Alarm urgency",
  "Work order urgency":"Work order urgency",
  "Work order influence":"Work order influence",
  "Customer engagement strategy":"Customer engagement strategy",
  "Equipment operation strategy":"Equipment operation strategy",
  "Workplace work strategy":"Workplace work strategy",
  "Regional work strategy":"Regional work strategy",
  "OK":"OK",
  "Working Time Action Strategy":"Working Time Action Strategy",
  "Non working time action strategy":"Non working time action strategy",
  "Service catalog":"Service catalog",
  "Allocation":"Allocation",
  "Service pack":"Service pack",
  "Delete":"Delete",
  "Close":"Close",
  "Please select a service directory":"Please select a service directory",
  "Please select":"Please select"
}
