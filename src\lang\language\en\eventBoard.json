{"Add": "Add", "Add Journal": "Add Journal", "Alert": "<PERSON><PERSON>", "All": "All", "All P8": "All P8", "All Priority": "All {priority}", "Are you sure of the following events in bulk batch P8?": "Are you sure of the following events in bulk batch P8?", "Attachments are being uploaded": "Attachments are being uploaded", "Brief Introduction to the Situation": "Brief Introduction to the Situation", "Cancel": "Cancel", "Close code": "Close code", "Changes": "Changes", "Click Upload": "Click Upload", "Compack": "<PERSON><PERSON><PERSON>", "Create by": "Create by", "Created": "Created", "Customer": "Customer", "Customer Ticket Number": "Customer Ticket Number", "DICT Iincidents": "DICT Iincidents", "DICT Requests": "DICT Requests", "Details": "Details", "Director": "Director", "Edit": "Edit", "End code": "End code", "Fault reported": "Fault reported", "Handler": "Handler", "History Tickets": "History Tickets", "Iincidents": "Iincidents", "Modified": "Modified", "My open": "My open", "New": "New", "Open": "Open", "Please select the completion code": "Please select the completion code", "Priority": "Priority", "Private Clients": "Private Clients", "Problems": "Problems", "Project name": "Project name", "QR code to report faults": "QR code to report faults", "Quick Close": "Quick Close", "ServiceRequest": "Service Request", "Requests": "Service Request", "Resolution": "Resolution", "Response": "Response", "Search Ticket": "Search Ticket", "Service Ticket Number": "Service Ticket Number", "Start": "Start", "State": "State", "Stop": "Stop", "Submitting": "Submitting", "Summary": "Summary", "Suspended": "Suspended", "The memo cannot be left blank": "The memo cannot be left blank", "The service ticket is in the process of being closed": "The service ticket is in the process of being closed", "Ticket": "Ticket", "Ticket description": "Ticket description", "Ticket title": "Ticket title", "Tickets": "Tickets", "Unified Service Code": "Unified Service Code", "Unopened": "Unopened", "Upload attachments": "Upload attachments", "no found id ticket": "no found {id} ticket", "operate note": "{operate} note"}