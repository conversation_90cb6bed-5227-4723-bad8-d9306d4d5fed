﻿{
  "Add Data": "Add {value}",
  "AddEffective Immediately": "Effective immediately",
  "All Select": "select all",
  "All submenus": "all submenus",
  "Allocate the": "Allocate the {value}",
  "And": "And",
  "Are you sure to delete the selected record?": "Are you sure to delete the selected records?",
  "Busy": "Busy",
  "Cancel": "Cancel",
  "Cannot be empty": "{field} cannot be empty",
  "Cat": "View",
  "Click Select": "click to select",
  "Close": "Close",
  "Confirm": "Confirm",
  "Contains": "Contains",
  "Copied": "copied!",
  "Copy": "copy",
  "Delete selected row": "delete selected row",
  "Disable": "disabled",
  "Display Column": "select display columns",
  "Division Rule": "Type Enter (line break) to split, and you can enter multiple lines",
  "Does not contain": "Does not contain",
  "Edit Data": "Edit {value}",
  "Edit selected row": "edit selected line",
  "Enable": "enable",
  "Equal": "Equal",
  "Exclude": "Exclude",
  "Expand all": "expand all",
  "Expand generic search": "expand general search",
  "Export": "Export",
  "Frozen": "freeze",
  "Fuzzy query": "fuzzy query",
  "Greater than": "Greater than",
  "Greater than or equal to": "Greater than or equal to",
  "Import": "import",
  "Include": "Include",
  "Is equal to": "Is equal to",
  "Is not equal to": "Is not equal to",
  "Keyword": "keywords",
  "Less than": "Less than",
  "Less than or equal to": "Less than or equal to",
  "Link address": "link address",
  "Model": "model",
  "More": "More",
  "Move Dw": "move down",
  "Move Up": "Move up",
  "New Data": "New {value}",
  "Next": "Next step",
  "No Data": "No Data",
  "No route found to jump~": "Could not find a route to jump~",
  "Normal": "normal",
  "Not": "Not",
  "Not empty": "Not empty",
  "Not equal": "Not equal",
  "Offline": "Offline",
  "Or": "Or",
  "Please enter the correct field": "Please enter the correct {field}",
  "Please input field": "Please enter {field}",
  "Please select field": "Please select {field}",
  "Prev": "Previous",
  "ReStart Router": "restart router",
  "Reminder": "Kind tips",
  "Reset": "reset",
  "Retrieve": "get back",
  "Retry": "Retry",
  "Role By Admin": "administrator",
  "Role By Normal": "normal role",
  "Role By Syper": "super administrator",
  "Role By Users": "basic role",
  "Save": "save",
  "Save and edit next item": "save and edit next item",
  "Shrink all": "shrink all",
  "This is a deliberate error thrown to prevent a hot update of Vite": "This is an error thrown intentionally to prevent hot update of Vite",
  "Upload": "upload",
  "Yes": "Yes",
  "add": "Add to",
  "allocation": "distribute",
  "back": "Back",
  "change": "change",
  "complete": "Finish",
  "createtime": "creation time",
  "delete": "Delete",
  "download": "download",
  "edit": "Edit",
  "empty": "Empty",
  "home": "front page",
  "id": "ID",
  "info": "check the details",
  "none": "none",
  "online": "online",
  "open": "expand",
  "operate": "Operate",
  "prompt": "prompt",
  "quick Search Placeholder": "Fuzzy search by {fields}",
  "refresh": "to refresh",
  "remove": "Remove",
  "search": "Search",
  "shrink": "shrink",
  "state": "state",
  "to": "to",
  "unknown": "unknown",
  "updatetime": "Change the time",
  "view": "View",
  "view Data": "View {value}",
  "weigh": "Weights",
  "weigh-sort": "drag to sort",
  "what": "what"
}
