﻿{
  "Are you sure you want to restore all configurations to the default values?": "Are you sure you want to restore all configurations to default values?",
  "Background color when hovering over the top bar": "Top bar background color on hover",
  "Background page switching animation": "Background page switching animation",
  "Dark mode": "dark mode",
  "Exit full screen": "Exit Full Screen",
  "Full screen is not supported": "Your browser does not support full screen, please change your browser and try again~",
  "Layout configuration": "layout configuration",
  "Layout mode": "Layout",
  "Main Size": "size",
  "Please select an Main Size": "Please select layout size",
  "Please select an animation name": "Please select an animation name",
  "Restore default": "reset",
  "Show side menu top bar (logo bar)": "Show side menu top bar (LOGO bar)",
  "Side menu accordion": "side menu accordion",
  "Side menu active item background color": "Side menu active item background color",
  "Side menu active item text color": "Side menu active item text color",
  "Side menu bar background color": "Side menu bar background color",
  "Side menu default icon": "Side menu default icon",
  "Side menu horizontal collapse": "Side menu collapsed horizontally",
  "Side menu text color": "Side menu text color",
  "Side menu top bar background color": "Side menu top bar background color",
  "Side menu width (when expanded)": "Side menu width (when expanded)",
  "Single column": "single column",
  "Top bar": "top bar",
  "Top bar background color": "Top bar background color",
  "Top bar menu active item background color": "Top bar menu active item background color",
  "Top bar menu active item text color": "Top bar menu active item text color",
  "Top bar text color": "top bar text color",
  "cancellation": "log out",
  "classic": "classic",
  "default": "default",
  "focus": "concentrate on",
  "overall situation": "global",
  "personal data": "personal information",
  "sidebar": "Sidebar",
  "simplicity": "simple",
  "Switch to busy status": "Switch to busy status",
  "Switch to normal status": "Switch to normal status",
  "opinion feedback": "opinion feedback"
}
