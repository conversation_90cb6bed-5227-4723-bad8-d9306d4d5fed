﻿{
  "After installation 1": "after installation",
  "After installation 2": "After installation",
  "Automatically execute reissue command?": "Automate the reissue command?",
  "Backup and overwrite existing files": "Backup and overwrite existing files",
  "Balance payment": "balance payment",
  "Buy now": "Buy now",
  "Click me to upload": "click me to upload",
  "Click to access": "click to visit",
  "Code scanning Preview": "scan code preview",
  "Confirm to disable the module": "Confirm disable module",
  "Conflict file": "conflict file",
  "Congratulations, module installation is complete": "Congratulations, the module installation is complete.",
  "Congratulations, the code of the module is ready": "Congratulations, the code for the module is ready.",
  "Contact developer": "Contact the developer",
  "Dependencies": "dependencies",
  "Dependency conflict": "dependency conflict",
  "Dependency installation completed~": "The dependency has been installed~",
  "Dependency installation fail 1": "Dependency installation failed, please click",
  "Dependency installation fail 2": "terminal",
  "Dependency installation fail 3": "retry button in , you can also check",
  "Dependency installation fail 4": "Complete pending tasks manually",
  "Dependency installation fail 5": "in your",
  "Dependency installation fail 6": "Make sure dependencies are ready",
  "Dependency installation fail 7": "Before, the module could not be used normally!",
  "Developer Homepage": "Developer Home",
  "Disable and update": "disable and update",
  "Discard new file": "discard new files",
  "Do not refresh the page!": "Please do not refresh the page!",
  "Do not use new dependencies": "No new dependencies are used",
  "Drag the module package file here": "Drag and drop the module package file here or",
  "End of installation": "end of installation",
  "Existing dependencies": "Already dependent",
  "Existing files": "Existing files",
  "File conflict": "file conflict",
  "Get points": "Earn points",
  "Install now": "install now",
  "Is the command that failed on the WEB terminal executed manually or in other ways successfully?": "Has the command that failed in the WEB terminal been successfully executed manually or otherwise?",
  "Last updated": "latest update",
  "Loading": "Loading...",
  "Local module": "local module",
  "Local upload warning": "Please be sure to confirm that the module package file comes from an official channel or an officially certified module author, otherwise the system may be damaged because:",
  "Log in to the buildadmin module marketplace": "Login to BuildAdmin Module Marketplace",
  "Manually clean up the system and browser cache, and refresh the page": "Manually clear the system and browser cache, and refresh the page.",
  "Member information": "member information",
  "Module classification": "Module classification",
  "Module installation warning": "Free download and update within one year after purchase, virtual products do not support 7 days no reason refund",
  "Module is disabled": "Module is disabled.",
  "Module purchase and use agreement": "Module Purchase and Use Agreement",
  "Module status": "module status",
  "My module": "my module",
  "New adjustment of dependency detected": "New adjustments detected for dependencies",
  "New dependency": "new dependency",
  "No detailed update log": "No detailed changelog",
  "No more": "no more...",
  "Order No": "order number",
  "Order price": "order price",
  "Order title": "order title",
  "Other works of developers": "TA's other works",
  "Overwrite existing dependencies": "override existing dependencies",
  "Please enter buildadmin account name or email": "Please enter BuildAdmin account name/email/mobile phone number",
  "Please enter the buildadmin account password": "Please enter your BuildAdmin account password",
  "Please enter the login verification code": "Please enter the login verification code",
  "Point payment": "points payment",
  "Price": "price",
  "Published on": "release time",
  "Publishing module": "release module",
  "Purchase user": "purchase user",
  "Purchased, can be installed directly": "Purchased and can be installed directly",
  "Register": "Don't have an account? \nto register",
  "Search is actually very simple": "Search is actually very simple",
  "Sign in": "Log in",
  "The built-in terminal of the system is automatically installing these dependencies, please wait~": "The built-in terminal of the system is automatically installing these dependencies, please wait~",
  "The module can execute sql commands and codes": "The module can execute sql commands and codes",
  "The module can install new front and rear dependencies": "Modules can install new front-end and back-end dependencies",
  "The module can modify and add system files": "Modules can modify and add system files",
  "The module declares the added dependencies": "Dependencies added by module declaration",
  "There are no more works": "no more entries",
  "There is no adjustment for system dependency": "System dependent no adjustments.",
  "This module adds new dependencies": "This module adds new dependencies",
  "This module does not add new dependencies": "This module adds no new dependencies.",
  "Treatment scheme": "Solution",
  "Understand and agree": "understand and agree",
  "Unknown state": "unknown status.",
  "Update Log": "update log",
  "Update warning": "It is detected that the following module files have been updated, and will be automatically overwritten when disabled, please pay attention to backup.",
  "Upload installation": "Upload and install",
  "Upload zip package for installation": "Upload ZIP package installation",
  "Uploaded / installed modules": "Uploaded/installed modules",
  "Uploaded, installation is about to start, please wait": "Uploaded, the installation will start soon, please wait",
  "View demo": "view demo",
  "View progress": "check progress",
  "You need to disable this module before updating Do you want to disable it now?": "This module needs to be disabled before updating, disable now?",
  "amount of downloads": "download times",
  "continue installation": "continue to install",
  "detailed information": "details",
  "env dependencies": "Front-end dependencies (NPM)",
  "env devDependencies": "Front-end development environment dependencies (NPM)",
  "env require": "Backend dependencies (composer)",
  "env require-dev": "Backend development environment dependency (composer)",
  "environment": "environment",
  "installed": "Installed",
  "new file": "new file",
  "no": "no",
  "payment": "to pay",
  "please": "Please",
  "retain": "reserve",
  "stateTitle download": "Downloading module...",
  "stateTitle init": "Module installer initialization...",
  "stateTitle install": "Installing module...",
  "to update": "renew",
  "uninstall": "uninstall",
  "yes": "yes"
}
