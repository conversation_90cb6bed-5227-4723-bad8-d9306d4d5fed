﻿{
  "Breakdown": "Details",
  "File MIME type": "file mime type",
  "File saving path Modifying records will not automatically transfer files": "File save path, modifying records will not automatically transfer files",
  "File size (bytes)": "file size (bytes)",
  "Files and records will be deleted at the same time Are you sure?": "Files and records will be deleted at the same time, confirm?",
  "Height of picture file": "image file height",
  "Last upload time": "last upload time",
  "Original file name": "original file name",
  "Original name": "original name",
  "Physical path": "physical path",
  "Picture height": "image height",
  "SHA1 code": "sha1",
  "SHA1 encoding of file": "The sha1 encoding of the file",
  "Select File": "Select the file",
  "Storage mode": "storage method",
  "The file is saved in the directory, and the file will not be automatically transferred if the record is modified": "The directory where the file is saved, the modification record will not automatically transfer the file",
  "Upload (Reference) times": "Upload (reference) times",
  "Upload (Reference) times of this file": "The number of times this file has been uploaded (referenced)",
  "Upload administrator": "upload manager",
  "Upload user": "upload member",
  "When the same file is uploaded multiple times, only one attachment record will be saved and added": "When the same file is uploaded multiple times, only one copy will be saved and an attachment record will be added",
  "Width of picture file": "image file width",
  "You can also select": "You can also choose",
  "choice": "choose",
  "file size": "File size",
  "image width": "image width",
  "items": "item",
  "mime type": "mime-type",
  "preview": "preview",
  "size": "size",
  "type": "Types of"
}
