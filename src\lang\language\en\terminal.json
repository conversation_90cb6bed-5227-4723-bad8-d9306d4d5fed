﻿{
  "Are you sure you want to republish?": "Are you sure you want to republish?",
  "Back to terminal": "back to terminal",
  "Clean up successful tasks when starting a new task": "Clean up successful tasks when starting a new task",
  "Clean up task list": "clean up task list",
  "Command run log": "command run log",
  "Connecting": "connecting...",
  "Do not refresh the browser": "Do not refresh your browser",
  "Executing": "executing...",
  "Execution failed": "Execution failed",
  "Failure to execute this command will block the execution of the queue": "Failure to execute this command will block queue execution",
  "I want to execute the command manually": "I want to execute the command manually",
  "Install dependent packages": "Install dependencies",
  "Install service port": "Install service port",
  "Installation service URL": "Install service URL",
  "Installation service startup command": "Install service start command",
  "Newly added tasks will never start because they are blocked by failed tasks": "The newly added task never starts because it is blocked by the failed task! \n(WEB terminal)",
  "No mission yet": "no tasks yet...",
  "Package manager": "package manager",
  "Please access the site through the installation service URL (except in debug mode)": "Please access the site through the installation service Url (except in debug mode)",
  "Please execute this command to start the service (add Su under Linux)": "Please execute this command to start the service (add su under Linux)",
  "Please select package manager": "Please select a package manager",
  "Republish": "Republish",
  "Site domain name": "domain name",
  "Successful execution": "execution succeed",
  "Switch package manager title": "Read-only WEB terminal, you can easily execute npm install, npm build and other commands after CRUD operations, please choose an installed or your favorite NPM package manager below",
  "Terminal": "terminal",
  "Terminal settings": "terminal settings",
  "Test command": "test command",
  "The current terminal is not running under the installation service, and some commands may not be executed": "The current terminal is not running under the installation service, and some commands may not be executed.",
  "The port number to start the installation service (this port needs to be opened for external network access)": "The port number for starting the installation service (the port needs to be opened for external network access)",
  "Unknown execution result": "The execution result is unknown",
  "Waiting for execution": "waiting to execute",
  "ectTenant": "Please select a tenant",
  "or": "or",
  "unknown": "unknown"
}
