﻿{
  "Account Expiration Date": "Account Expiration Date",
  "Active": "Active",
  "After activation, the system will force users to change their passwords after a period of time": "After activation, the system will force users to change their passwords after a period of time",
  "After setting the validity period in bulk, if there are users who have not enabled the account validity period, the system will automatically enable it, and the validity period will take effect according to the time configured on the current page": "After setting the validity period in bulk, if there are users who have not enabled the account validity period, the system will automatically enable it, and the validity period will take effect according to the time configured on the current page.",
  "Any of the customer default policy settings may be changed for the user": "Any of the customer default policy settings may be changed for the user。",
  "AreYouSureToFreezeUser": "Are you sure you want to freeze the user?",
  "AreYouSureToUnlockUser": "Are you sure you want to Unlock the user?",
  "At least 3 out of 4 categories including numbers, lowercase letters, uppercase letters, and special character symbols (.~! @ # $% ^&*?), with a minimum password length of 8 characters": "At least 3 out of 4 categories including numbers, lowercase letters, uppercase letters, and special character symbols (.~!@#$%^&*?), with a minimum password length of 8 characters",
  "At least one": "At least one",
  "Batch Operation": "Batch Operation",
  "Confirm Password": "Confirm password",
  "Confirm removal": "Confirm removal",
  "ConfirmPasswordCannotBeEmpty": "Confirm password cannot be empty",
  "Custom": "Custom",
  "CustomValue": "Custom value",
  "Customer affiliation": "Customer affiliation",
  "CustomerPolicy": "Customer Policy",
  "Default": "Default",
  "DefaultValue": "Default Value",
  "Disabled": "Disabled",
  "EditUser": "Edit user",
  "Email": "Email",
  "Enable": "Enable",
  "Enforce password complexity": "Enforce password complexity",
  "Enforce password complexity tip": "After activation, the system only allows passwords that comply with complexity rules. Passwords are strictly prohibited from using default, easily guessed strings such as admin, root, Huawei, ***********, ChinaTelecom, etc; Passwords should include at least 3 out of 4 categories: numbers, lowercase letters, uppercase letters, and special characters (.~! @ # S% ^&*?); The password should be unrelated to the account (username) and should not contain the complete string of the account, uppercase and lowercase variations, or strings that resemble transformations; Password settings should avoid keyboard sorting passwords",
  "Enter user account/email/phone number to query": "Enter user account/email/phone number to query",
  "Feature Configuration": "Feature Configuration",
  "FeatureConfiguration": "Feature Configuration",
  "FreezeUser": "Freeze user",
  "Full name": "Full name",
  "Invited users?": "Invited users?",
  "LatestLoginTime": "Latest login time",
  "Maximum password age days": "Maximum password age days",
  "Maximum password usage period": "Maximum password usage period",
  "MaximumPasswordAge": "Maximum Password Age",
  "Minimum password length": "Minimum password length",
  "MinimumPasswordLength": "Minimum password length",
  "Mobile": "Mobile",
  "More": "More",
  "NewUser": "New user",
  "NoData": "No Data",
  "NotActive": "Inactive",
  "NotificationLanguage": "Notification language",
  "Password": "Password",
  "Password Expiration": "Password Expiration",
  "Password Policy": "Customer Policy",
  "Password expire": "Password expire",
  "Password history": "Password history",
  "Password needs to be changed when logging in": "Password needs to be changed when logging in",
  "Please select the account expiration date": "Please select the account expiration date",
  "PleaseChoose": "PleaseChoose",
  "PleaseChooseNotificationLanguage": "PleaseChooseNotificationLanguage",
  "PleaseChooseUserGroup": "Please choose User group",
  "PleaseEnter": "PleaseEnter",
  "PleaseEnterEmail": "Please enter the email",
  "PleaseEnterMobilePhoneNumber": "Please enter mobile phone number",
  "PleaseEnterName": "Please enter the name",
  "PleaseEnterUserPassword": "Please enter the user password",
  "PleaseEnterUsername": "Please enter the Username login ID",
  "Registration time": "Registration time",
  "ResetPassword": "Reset password",
  "Search": "Search",
  "SearchUser": "Search user",
  "Select User": "Select User",
  "Selected": "Selected",
  "Set Password": "Set Password",
  "Settings": "Settings",
  "State": "State",
  "Strong password": "Strong password",
  "The User Account Policy applies only to a specific user": "The User Account Policy applies only to a specific user。",
  "The account starts with a letter and contains 1 to 64 uppercase and lowercase letters, numbers, and _#": "The account starts with a letter and contains 1 to 64 uppercase and lowercase letters, numbers, and _#",
  "The minimum allowed length of a password": "The minimum allowed length of a password",
  "The number of days after which the system will force users to change their password": "The number of days after which the system will force users to change their password",
  "The number of new passwords that users must use before reusing old passwords to prevent users from reusing old passwords when changing them。": "The number of new passwords that users must use before reusing old passwords to prevent users from reusing old passwords when changing them。",
  "TimeZone": "Time Zone",
  "Two passwords need to be consistent": "Two passwords need to be consistent",
  "TwoFactorAuthentication": "Two-factor authentication",
  "UnlockUser": "Unlock user",
  "User": "User",
  "User Password": "User Password",
  "User password cannot be empty": "User password cannot be empty",
  "UserAccountCannotBeEmpty": "User account cannot be empty",
  "UserCanChangePassword": "User can change password",
  "UserDefault": "Default",
  "UserGroup": "User group",
  "UserNameCannotBeEmpty": "User name cannot be empty",
  "User Password Policy": "User Password Policy",
  "Username": "Username login ID",
  "Username login ID start with uppercase and lowercase letters, including 1 to 64 digits of uppercase and lowercase letters, numbers, underscores, question marks, exclamation marks, and pound signs": "Username login ID start with uppercase and lowercase letters, including 1 to 64 digits of uppercase and lowercase letters, numbers, underscores, question marks, exclamation marks, and pound signs",
  "ViewUserInformation": "View user Information",
  "thaw": "Unlock",
  "AreYouSureToThawUser": "Are you sure you want to unlock the user?",
  "Unlock user": "Unlock user"
}
