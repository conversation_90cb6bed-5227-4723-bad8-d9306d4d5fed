﻿{
  "English name": "English name",
  "For example, if you add account/overview as a route only": "Web-side component path, please start with /src, such as: /src/views/frontend/index.vue",
  "Member center menu contents": "Member Center Menu Directory",
  "Member center menu items": "Member Center Menu Items",
  "Normal routing": "common routing",
  "Web side component path, please start with /src, such as: /src/views/frontend/index": "For example, add `account/overview` only as a route, then you can add `account/overview`, `account/overview/:a`, `account/overview/:b/:c` only as a menu",
  "Web side routing path": "web routing path (path)"
}
