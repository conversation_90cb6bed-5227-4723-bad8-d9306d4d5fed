﻿{
  "Add as menu only": "メニューとしてのみ追加",
  "Add as route only": "ルートとして追加するだけ",
  "Component path": "コンポーネント パス",
  "English name, which does not need to start with `/admin`, such as auth/menu": "英語名、「/admin」で始まる必要はありません。例: auth/menu",
  "Extended properties": "拡張属性",
  "Icon": "アイコン",
  "It will be registered as the web side routing name and used as the server side API authentication": "Web側ではルート名として登録され、サーバー側のAPI認証として利用されます",
  "Link address": "リンクアドレス",
  "Menu type": "メニュータイプ",
  "Menu type link (offsite)": "リンク（外部）",
  "Menu type tab": "タブ",
  "Please enter the URL address of the link or iframe": "リンクまたは iframe の URL アドレスを入力してください",
  "Please enter the correct URL": "正しい URL を入力してください",
  "Please enter the weight of menu rule (sort by)": "メニュー ルールの重みを入力してください (並べ替え順)",
  "Routing path": "ルーティング パス",
  "Rule Icon": "ルール アイコン",
  "Rule comments": "ルール 備考",
  "Rule name": "ルール名",
  "Rule title": "ルールのタイトル",
  "Rule type": "ルールの種類",
  "Rule weight": "ルールの重み",
  "Superior menu rule": "親メニューのルール",
  "The superior menu rule cannot be the rule itself": "親メニュー ルール自体をルールにすることはできません",
  "The web side routing path (path) does not need to start with `/admin`, such as auth/menu": "Web 側のルーティング パス (パス) は、次のように「/admin」で始まる必要はありません: auth/menu",
  "Use in controller `get_ route_ Remark()` function, which can obtain the value of this field for your own use, such as the banner file of the console": "コントローラーで `get_route_remark()` 関数を使用して、コンソールのバナー コピーなど、独自に使用するためにこのフィールドの値を取得します。",
  "Web side component path, please start with /src, such as: /src/views/backend/dashboard": "Web 側のコンポーネント パスは、/src で開始してください。次のようになります: /src/views/backend/dashboard.vue",
  "cache": "キャッシュ",
  "extend Title": "たとえば、「auth/menu」がルートとしてのみ追加される場合、「auth/menu」、「auth/menu/:a」、「auth/menu/:b/:c」はメニューとしてのみ追加できます。",
  "name": "名前",
  "none": "なし",
  "title": "題名",
  "type": "の種類",
  "type button": "ページボタン",
  "type menu": "メニュー項目",
  "type menu_dir": "メニュー一覧"
}
