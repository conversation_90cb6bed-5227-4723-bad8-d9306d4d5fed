﻿{
  "Advanced Configuration": "詳細設定",
  "Advanced Fields": "高度な分野",
  "Allow NULL": "ヌルを許可します",
  "Are you sure to delete the generated CRUD code?": "生成された CRUD コードの削除を確認しますか?",
  "Associated Data Table": "リンクされたデータ テーブル",
  "Auto increment": "自動増加",
  "Base Fields": "ベースフィールド",
  "CRUD record": "CRUD レコード",
  "Common": "一般的に使用される",
  "Common Fields": "共通フィールド",
  "Confirm CRUD code generation": "CRUD コードの生成を確認する",
  "Continue building": "生成し続ける",
  "Controller position": "コントローラーの場所",
  "Data Model Location": "データ モデルの場所",
  "Data Table Notes": "データシートの注記",
  "Delete Code": "コードを削除",
  "Drag the left element here to start designing CRUD": "左の要素をここにドラッグして、CRUD の設計を開始します",
  "Drop down label field": "ドロップダウン ラベル フィールド",
  "Drop down value field": "ドロップダウン値フィールド",
  "Fast experience": "クイック体験",
  "Field Defaults": "フィールドのデフォルト",
  "Field Form Properties": "フィールドフォームのプロパティ",
  "Field Name": "フィールド名",
  "Field Properties": "フィールド プロパティ",
  "Field Table Properties": "フィールドフォームのプロパティ",
  "Field Type": "フィールドタイプ",
  "Field comments (CRUD dictionary)": "フィールドの注釈 (CRUD 辞書)",
  "Fields as Table Columns": "テーブル列としてのフィールド",
  "Fields as form items": "フォーム項目としてのフィールド",
  "Fields displayed in the table": "フォームに表示されるフィールド",
  "For example: `user table` will be generated into `user management`": "例: `Member Table` は `Member Management` として生成されます。",
  "Generate CRUD code": "CRUD コードを生成する",
  "Generated Controller Location": "生成されたコントローラーの場所",
  "Generated Data Model Location": "生成されたデータ モデルの場所",
  "Generated Validator Location": "生成されたバリデータの場所",
  "If it is left blank, the model of the associated table will be generated automatically If the table already has a model, it is recommended to select it to avoid repeated generation": "関連付けられたテーブルのモデルを自動的に生成するには、空白のままにします。テーブルに既にモデルがある場合は、生成の繰り返しを避けるためにモデルを選択することをお勧めします",
  "It is irreversible to give up the design Are you sure you want to give up?": "デザインの放棄は元に戻せません。本当に放棄しますか?",
  "Name of the data table": "データ テーブルの名前",
  "New background CRUD from zero": "ゼロからの新しい背景 CRUD",
  "Please design the primary key field!": "主キー フィールドを設計してください。",
  "Please enter SQL": "SQLを入力してください",
  "Please enter the data table name!": "データテーブル名を入力してください!",
  "Please enter the table creation SQL": "テーブルを作成するための SQL を入力してください",
  "Please select a data table": "データシートを選択してください",
  "Please select a field from the left first": "最初に左からフィールドを選択してください",
  "Please select the controller of the data table": "データ テーブルのコントローラを選択してください",
  "Please select the data model location of the data table": "データ テーブルのデータ モデルの場所を選択してください",
  "Please select the fields displayed in the table": "テーブルに表示するフィールドを選択してください",
  "Please select the label field of the select component": "選択コンポーネントのラベル フィールドを選択してください",
  "Please select the value field of the select component": "選択コンポーネントの値フィールドを選択してください",
  "Remote drop-down association information": "リモートドロップダウン関連情報",
  "Select Data Table": "データ テーブルを選択",
  "Select a designed data table from the database": "データベースから設計されたデータ テーブルを選択します",
  "Start CRUD design with this record?": "このレコードで CRUD 設計を開始しますか?",
  "Start with previously generated CRUD code": "以前に生成された CRUD コードから開始する",
  "Table Default Sort Fields": "テーブルのデフォルトのソート フィールド",
  "Table Quick Search Fields": "フォームのクイック検索フィールド",
  "The controller already exists Continuing to generate will automatically overwrite the existing code!": "コントローラーは既に存在します。生成を続行すると、既存のコードが自動的に上書きされます。",
  "The data table already exists Continuing to generate will automatically delete the original table and create a new one!": "データ テーブルは既に存在します。生成を続行すると、元のテーブルが自動的に削除され、新しいデータ テーブルが作成されます。",
  "The field comment will be used as the CRUD dictionary, and will be identified as the field title before the colon, and as the data dictionary after the colon": "フィールド コメントは CRUD ディクショナリとして使用され、コロンの前はフィールド タイトルとして認識され、コロンの後はデータ ディクショナリとして認識されます。",
  "The remote pull-down will request the corresponding controller to obtain data, so it is recommended that you create the CRUD of the associated table": "リモート プルダウンは対応するコントローラーにデータを取得するように要求するため、最初に関連付けられたテーブルの CRUD を生成することをお勧めします。",
  "There can only be one primary key field": "主キー フィールドは 1 つだけです。",
  "Unsigned": "無署名",
  "WEB end view directory": "WEBエンドビューディレクトリ",
  "You can directly enter null, 0, empty string": "null、0、空の文字列を直接入力できます",
  "copy": "コピーデザイン",
  "create": "新築",
  "data sheet": "データシート",
  "decimal point": "小数点",
  "experience 1 1": "準備する",
  "experience 1 2": "開発環境",
  "experience 2 1": "このページをクリック",
  "experience 2 2": "データ テーブルを選択",
  "experience 2 3": "を選択",
  "experience 3 1": "クリック",
  "experience 3 2": "CRUD コードを生成する",
  "experience 3 3": "、 クリック",
  "experience 3 4": "生成し続ける",
  "field comment": "フィールドコメント",
  "file-multi": "複数ファイルのアップロード",
  "generate": "として生成",
  "give up": "あきらめる",
  "image-multi": "写真の複数選択アップロード",
  "length": "長さ",
  "operator": "一般的な検索演算子",
  "relation-fields": "関連テーブルの表示フィールド",
  "remote-controller": "リレーショナル テーブルのコントローラー",
  "remote-field": "リモート ドロップダウン ラベル フィールド",
  "remote-model": "関連テーブルのモデル",
  "remote-pk": "リモート ドロップダウン値フィールド",
  "remote-table": "リンクされたデータ テーブル",
  "remote-url": "リモート ドロップダウン URL",
  "render": "レンダリングスキーム",
  "rows": "行",
  "select-multi": "ドロップダウンボックスの複数選択",
  "show": "表の列に表示",
  "sort order": "並び替え",
  "sort order asc": "昇順",
  "sort order desc": "des - 逆順",
  "sortable": "フィールドソート",
  "start": "始める",
  "step": "ステップ値",
  "table create SQL": "テーブル SQL の作成",
  "timeFormat": "フォーマット方法",
  "validator": "検証規則",
  "validatorMsg": "確認エラー プロンプト",
  "width": "表の列幅"
}
