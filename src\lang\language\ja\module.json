﻿{
  "After installation 1": "インストール後",
  "After installation 2": "インストール後",
  "Automatically execute reissue command?": "再発行コマンドを自動化しますか?",
  "Backup and overwrite existing files": "既存のファイルのバックアップと上書き",
  "Balance payment": "残金のお支払い",
  "Buy now": "今買う",
  "Click me to upload": "私をクリックしてアップロード",
  "Click to access": "クリックして訪問",
  "Code scanning Preview": "スキャンコードのプレビュー",
  "Confirm to disable the module": "モジュールの無効化を確認",
  "Conflict file": "競合ファイル",
  "Congratulations, module installation is complete": "おめでとうございます。モジュールのインストールが完了しました。",
  "Congratulations, the code of the module is ready": "おめでとうございます。モジュールのコードの準備ができました。",
  "Contact developer": "開発者に連絡する",
  "Dependencies": "依存関係",
  "Dependency conflict": "依存関係の競合",
  "Dependency installation completed~": "依存関係がインストールされました〜",
  "Dependency installation fail 1": "依存関係のインストールに失敗しました。クリックしてください",
  "Dependency installation fail 2": "ターミナル",
  "Dependency installation fail 3": "の再試行ボタンで、確認することもできます",
  "Dependency installation fail 4": "保留中のタスクを手動で完了する",
  "Dependency installation fail 5": "あなたの",
  "Dependency installation fail 6": "依存関係の準備ができていることを確認する",
  "Dependency installation fail 7": "以前は、モジュールを正常に使用できませんでした。",
  "Developer Homepage": "デベロッパー ホーム",
  "Disable and update": "無効にして更新する",
  "Discard new file": "新しいファイルを破棄",
  "Do not refresh the page!": "ページを更新しないでください。",
  "Do not use new dependencies": "新しい依存関係は使用されません",
  "Drag the module package file here": "ここにモジュール パッケージ ファイルをドラッグ アンド ドロップするか、",
  "End of installation": "インストールの終了",
  "Existing dependencies": "すでに依存している",
  "Existing files": "既存のファイル",
  "File conflict": "ファイルの競合",
  "Get points": "ポイントを獲得",
  "Install now": "今すぐインストール",
  "Is the command that failed on the WEB terminal executed manually or in other ways successfully?": "WEB端末で失敗したコマンドは、手動などで正常に実行されましたか?",
  "Last updated": "最新のアップデート",
  "Loading": "読み込んでいます...",
  "Local module": "ローカルモジュール",
  "Local upload warning": "モジュール パッケージ ファイルが公式チャンネルまたは公式に認定されたモジュール作成者からのものであることを確認してください。",
  "Log in to the buildadmin module marketplace": "BuildAdmin モジュール マーケットプレイスにログイン",
  "Manually clean up the system and browser cache, and refresh the page": "システムとブラウザーのキャッシュを手動でクリアし、ページを更新します。",
  "Member information": "会員情報",
  "Module classification": "モジュール分類",
  "Module installation warning": "購入後 1 年以内の無料ダウンロードとアップデート、仮想製品は 7 日間の理由なしの払い戻しをサポートしていません",
  "Module is disabled": "モジュールが無効になっています。",
  "Module purchase and use agreement": "モジュール購入および使用契約",
  "Module status": "モジュールの状態",
  "My module": "私のモジュール",
  "New adjustment of dependency detected": "依存関係の新しい調整が検出されました",
  "New dependency": "新しい依存関係",
  "No detailed update log": "詳細な変更ログなし",
  "No more": "もういや...",
  "Order No": "注文番号",
  "Order price": "注文価格",
  "Order title": "注文タイトル",
  "Other works of developers": "TAの他の作品",
  "Overwrite existing dependencies": "既存の依存関係をオーバーライドする",
  "Please enter buildadmin account name or email": "BuildAdmin アカウント名/メールアドレス/携帯電話番号を入力してください",
  "Please enter the buildadmin account password": "BuildAdmin アカウントのパスワードを入力してください",
  "Please enter the login verification code": "ログイン認証コードを入力してください",
  "Point payment": "ポイント支払い",
  "Price": "価格",
  "Published on": "リリース時間",
  "Publishing module": "リリース モジュール",
  "Purchase user": "購入ユーザー",
  "Purchased, can be installed directly": "購入して直接インストール可能",
  "Register": "アカウントをお持ちでない場合\n登録するために",
  "Search is actually very simple": "検索は実際には非常に簡単です",
  "Sign in": "ログイン",
  "The built-in terminal of the system is automatically installing these dependencies, please wait~": "システムの組み込みターミナルがこれらの依存関係を自動的にインストールしています。お待ちください〜",
  "The module can execute sql commands and codes": "モジュールはSQLコマンドとコードを実行できます",
  "The module can install new front and rear dependencies": "モジュールは、新しいフロントエンドとバックエンドの依存関係をインストールできます",
  "The module can modify and add system files": "モジュールはシステムファイルを変更および追加できます",
  "The module declares the added dependencies": "モジュール宣言によって追加される依存関係",
  "There are no more works": "もうエントリはありません",
  "There is no adjustment for system dependency": "システム依存の調整なし。",
  "This module adds new dependencies": "このモジュールは新しい依存関係を追加します",
  "This module does not add new dependencies": "このモジュールは、新しい依存関係を追加しません。",
  "Treatment scheme": "解決",
  "Understand and agree": "理解して同意する",
  "Unknown state": "不明な状態。",
  "Update Log": "更新ログ",
  "Update warning": "以下のモジュールファイルが更新されていることが検出され、無効にすると自動的に上書きされます。バックアップには注意してください。",
  "Upload installation": "アップロードしてインストール",
  "Upload zip package for installation": "ZIP パッケージのインストールのアップロード",
  "Uploaded / installed modules": "アップロード/インストールされたモジュール",
  "Uploaded, installation is about to start, please wait": "アップロードしました。まもなくインストールが開始されます。しばらくお待ちください",
  "View demo": "デモを見る",
  "View progress": "進行状況を確認する",
  "You need to disable this module before updating Do you want to disable it now?": "このモジュールは更新前に無効にする必要があります。今すぐ無効にしますか?",
  "amount of downloads": "ダウンロード時間",
  "continue installation": "インストールを続ける",
  "detailed information": "詳細",
  "env dependencies": "フロントエンドの依存関係 (NPM)",
  "env devDependencies": "フロントエンド開発環境の依存関係 (NPM)",
  "env require": "バックエンドの依存関係 (作曲家)",
  "env require-dev": "バックエンド開発環境の依存関係 (コンポーザー)",
  "environment": "環境",
  "installed": "インストール済み",
  "new file": "新しいファイル",
  "no": "番号",
  "payment": "支払う",
  "please": "お願いします",
  "retain": "予約",
  "stateTitle download": "モジュールをダウンロードしています...",
  "stateTitle init": "モジュール インストーラの初期化...",
  "stateTitle install": "モジュールをインストールしています...",
  "to update": "更新する",
  "uninstall": "アンインストール",
  "yes": "はい"
}
