﻿{
  "Breakdown": "詳細",
  "File MIME type": "ファイルの MIME タイプ",
  "File saving path Modifying records will not automatically transfer files": "ファイル保存パス、レコードを変更してもファイルは自動的に転送されません",
  "File size (bytes)": "ファイルサイズ (バイト)",
  "Files and records will be deleted at the same time Are you sure?": "ファイルとレコードが同時に削除されます。確認しますか?",
  "Height of picture file": "画像ファイルの高さ",
  "Last upload time": "最終アップロード時間",
  "Original file name": "元のファイル名",
  "Original name": "元の名前",
  "Physical path": "物理パス",
  "Picture height": "画像の高さ",
  "SHA1 code": "しゃ1",
  "SHA1 encoding of file": "ファイルのsha1エンコーディング",
  "Select File": "ファイルを選択",
  "Storage mode": "保管方法",
  "The file is saved in the directory, and the file will not be automatically transferred if the record is modified": "ファイルが保存されるディレクトリ。修正記録はファイルを自動的に転送しません。",
  "Upload (Reference) times": "アップロード（参考）時間",
  "Upload (Reference) times of this file": "このファイルがアップロード（参照）された回数",
  "Upload administrator": "アップロードマネージャー",
  "Upload user": "メンバーをアップロード",
  "When the same file is uploaded multiple times, only one attachment record will be saved and added": "同じファイルが複数回アップロードされた場合、1 つのコピーのみが保存され、添付レコードが追加されます",
  "Width of picture file": "画像ファイル幅",
  "You can also select": "選択することもできます",
  "choice": "選ぶ",
  "file size": "ファイルサイズ",
  "image width": "画像幅",
  "items": "アイテム",
  "mime type": "MIME タイプ",
  "preview": "プレビュー",
  "size": "サイズ",
  "type": "の種類"
}
