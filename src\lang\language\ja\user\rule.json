﻿{
  "English name": "英語名",
  "For example, if you add account/overview as a route only": "Web 側のコンポーネント パスは、/src で開始してください。次のようになります: /src/views/frontend/index.vue",
  "Member center menu contents": "会員センターメニューディレクトリ",
  "Member center menu items": "会員センターのメニュー項目",
  "Normal routing": "共通ルーティング",
  "Web side component path, please start with /src, such as: /src/views/frontend/index": "たとえば、`account/overview` のみをルートとして追加すると、`account/overview`、`account/overview/:a`、`account/overview/:b/:c` のみをメニューとして追加できます。",
  "Web side routing path": "Web ルーティング パス (パス)"
}
