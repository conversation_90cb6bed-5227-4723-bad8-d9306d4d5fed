{"Actions": "操作", "AlarmCollection": "告警采集", "AlarmMergingRules": "告警归并规则", "AlarmPushToAlarmBoard": "告警推送至告警板", "AppliesToAlarmsAffectedByTopologicalRelationshipsIncludingDeviceOrInterfaceDownDeviceSymptoms": "适用于受拓扑关系影响的告警，包括设备或接口down、设备症状。", "AssignDevice": "分配设备", "AssignRegion": "分配区域", "AssignToDevice": "分配给设备", "AssignToRegion": "分配给区域", "AssignToVenue": "分配给场所", "AssignVenue": "分配场所", "AutomaticGenerationOfTicketsFromAlarms": "告警自动生成工单", "AutomaticTicketConfiguration": "自动工单配置", "AutomaticTicketConfigurationIsCustomerSpecificOneClickConfigurationForAllDevicesUnderUser": "自动工单配置是面向某个客户的，每个客户下的设备的告警配置规则可以自定义。一键可配置用户下的所有设备产生的告警都可以自动生成工单。", "BeforeTicketIsResolvedAndAlarmCollectionNotClosedAlarmsOfSameTypeFromSameDeviceWillBeMergedIntoTicket": "在工单未解决之前，且未关闭收集告警，同一个设备、相同类型的告警，被归并到该工单中。", "CheckedBoxScenario": "若勾选了此选框:使用默认规则。那自动工单规则就按照默认规则走~自定义规则即便配置，也不起作用。", "ConfigureTicketLockDuration": "用户可以配置【工单的锁定时间】，在工单锁定时间内，工单无法被更新、处理。在工单未关闭或者未关闭收集告警之前，告警都会按照配置的规则归并到同一个工单中。", "ConfirmToUseAutomaticTicketRulesOnceCheckedTheTicketAlarmMergingConfigurationRulesBelowWillNotTakeEffect": "确认使用自动工单规则？勾选后下方工单告警归并配置规则将不会生效。", "ConfirmUnassignCurrentData": "确认取消分配当前数据吗？", "CreateNewPolicy": "新建策略", "CustomerDefault": "客户默认", "DefaultSystemRules": "系统的默认规则为:", "Description": "描述", "DeviceDefault": "设备默认", "DeviceName": "设备名称", "DoNotUseDefaultRulesOptionInConfigurationPage": "用户可以在自动工单配置页面，不选中[使用默认的自动工单规则]选框，选择不使用默认规则，则需要自定义配置自动工单规则。", "DowngradeConfiguration": "降级配置：", "DuringWorkingHours": "工作时间内", "Friday": "周五", "GeneratedAlarmsAreMergedIntoSameTicketAccordingToConfiguredRules": "生成的告警按照配置的规则被归并到同一工单中。", "IsActive": "是否激活", "IsDefault": "是否默认", "MergeAlarmsIntoExistingGeneratedTickets": "告警归并到已生成的工单中", "MergeTopologyAlarmsIntoExistingGeneratedTickets": "拓扑告警归并到已生成的工单中", "MergeTopologyAlarmsIntoSameTicket": "拓扑告警归并到同一工单", "MergingRules": "归并规则", "MinimumPriorityCanOnlyBeDowngradedToP7": "优先级最小只能降到P7", "Monday": "周一", "Name": "名称", "NewAlarmsMergedIntoExistingOpenTickets": "如果之前生成的工单已开启，未关闭，并且未关闭【收集告警】按钮，新上报的同一设备的告警会收到这个工单中，否则，创建新的工单。", "OutsideWorkingHours": "非工作时间内", "Please select the order group": "请选择工单组", "PleaseSelect": "请选择 ", "PolicyName": "策略名称", "PriorityAdjustmentPolicyConfiguration": "优先级调整策略配置", "ProhibitPriorityBelow": "禁止优先级低于：", "RegardlessOfMergingRulesSubDeviceTopologyAlarmsWillAlwaysBeMergedIntoParentDeviceUnresolvedAlarmTickets": "无论归并规则如何，子设备的拓扑告警始终会被归并到父设备的未完成的告警工单中。", "RegionName": "区域名称", "SNMPTrapAlarmsRelatedToTopologyRelationshipsWillBeMergedIntoSameTicketAsAssociatedDeviceStatusAlarmsEgColdStart": "与拓扑关系有关的Snmp trap告警，告警与关联的设备状态告警会归并到同一工单中，例如:设备的'cold start'。", "SNMPTrapTopologyAlarmMerging": "Snmp trap拓扑告警归并", "SameDeviceAlarmsGenerateSameTicket": "同一个设备的告警，会生成同一个工单。", "Saturday": "周六", "SaturdayDowngradeConfiguration": "周六降级配置", "SelectPriorityDowngradeLevel": "选择优先级降级数：", "SelectTimeZone": "选择时区：", "ShowAllDevice": "展示所有设备", "ShowAllRegion": "展示所有区域", "ShowAllVenue": "展示所有场所", "SubDeviceTopologyAlarmsAlwaysMerged": "子设备拓扑告警始终归并", "SubDeviceTopologyAlarmsMergedIntoParentDeviceUnresolvedTickets": "子设备的拓扑告警始终会被归并到父设备的未完成的拓扑告警工单中。", "Sunday": "周日", "SundayDowngradeConfiguration": "周日降级配置", "TheAlarm": "的告警 ", "TheAlarmsWillBeMergedIntoSameWorkOrder": "的告警会被归并到同一工单中。", "Thursday": "周四", "TicketAlarmMergingConfiguration": "工单告警归并配置", "TicketCreationRequiresAlarmCollectionAndAutomaticTicketGeneration": "只有为客户配置了告警采集和自动工单生成功能，工单才会被创建。", "TicketLockDuration": "工单锁定持续时间", "TicketLockDurationIs15Seconds": "工单锁定时间为15秒。", "TopologyAlarmMergingRules": "拓扑告警归并规则", "TopologyAlarmsFromSameDeviceWillBeMergedIntoExistingTicketsGeneratedForDeviceTopologyAlarms": "同一个设备的拓扑告警，会被归并到该设备已经有拓扑告警生成的工单中。", "Tuesday": "周二", "Unassign": "取消分配", "UncheckedBoxScenario": "未勾选此选框:如果客户下没有配置自定义的自动工单规则，也不使用默认规则，那自动工单功能就不起作用。 在不使用默认规则的时候，需要自定义配置，告警才可以自动生成工单。", "Use the priority strategy for order groups": "使用工单组优先级策略", "UseDefaultAutomaticTicketRulesOfTicketGroup": "使用默认的自动工单规则", "UseDefaultRulesOptionInConfigurationPage": "用户可以在自动工单配置页面，选中[使用默认的自动工单规则]选框，选择使用默认规则，则自定义规则不起作用。", "VenueName": "场所名称", "Wednesday": "周三", "WeekdayDowngradePolicyConfiguration": "工作日降级策略配置", "WhenTheWorkOrderIsLocked": "当工单锁定时", "WillBeMergedIntoSameTicket": "都会被归并到同一工单中", "WithinTheDurationOfTheWorkOrderLock": "在工单锁定持续时间内", "WorkingHours": "工作时间："}