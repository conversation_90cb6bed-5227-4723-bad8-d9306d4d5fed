{"Close code configuration": "关闭代码配置", "Newclosecode": "新增关闭代码", "editclosecode": "编辑关闭代码", "Closecodename": "关闭代码名称", "describe": "描述", "status": "状态", "subclass": "子类", "search": "搜索", "Security Container": "查看安全目录", "IsDefault": "是否默认", "name": "名称", "Select a secure directory": "选择安全目录", "Please enter name": "请输入名称", "Please enter a description": "请输入描述", "Please enter a close code name": "请输入关闭代码名称", "The customer already has a default closure code, and modifying it will overwrite the original closure code. Do you want to modify it?": "该客户已有默认关闭代码,修改后,将会覆盖原有关闭代码,是否修改?", "reminder": "提示", "confirm": "确 定", "cancel": "取 消", "New success": "新增成功", "modify successfully": "修改成功", "Configuration": "配置", "Close code": "关闭代码", "Confirm to delete": "确定删除", "Close the code?": "关闭代码吗？", "Event closure code": "事件关闭代码", "Service request closure code": "服务请求关闭代码", "Problem closure code": "问题关闭代码", "Change closure code": "变更关闭代码", "Publish and close code": "发布关闭代码", "Delete completed code": "删除完成代码", "Remove from report": "从报表中移除"}