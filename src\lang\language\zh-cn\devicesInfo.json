﻿{
  "Acked By": "响应人",
  "Add": "添加",
  "Add Service Number": "新增服务编号",
  "Alarm Confirmation Time": "告警确认时间",
  "Alarm urgency": "告警紧急性",
  "Alert": "告警",
  "Alerts": "告警",
  "Allocation": "分配",
  "Attention: From": "注意:从",
  "Attention: Use until": "注意:使用直到",
  "Bandwidth": "线路带宽",
  "Basic information": "基本信息",
  "Bind SLA service": "绑定SLA服务",
  "Builder": "创建者",
  "Click here to upload": "点此处上传",
  "Close": "关闭",
  "Contacts": "联系人",
  "Created": "创建时间",
  "Customer Support Instructions": "客户支持说明",
  "Customer engagement strategy": "客户工作策略",
  "Delete": "删除",
  "Description": "描述",
  "Details": "详述",
  "Device details": "设备详情",
  "Drag the file here or": "将文件拖到此处或",
  "Drag the file here or click here to upload": "将文件拖到此处或点击此处上传",
  "Duration": "持续时间",
  "Edit Journal": "编辑日志",
  "Edit Service Number": "编辑服务编号",
  "Equipment importance": "设备重要性",
  "Equipment operation strategy": "设备工作策略",
  "Files": "文件",
  "Files Name": "文件名",
  "History Alarm": "历史告警",
  "Journals": "日志",
  "Lastest alert": "最新告警",
  "Line Vendor": "线路供应商",
  "Manufacturer": "制造商",
  "Modified": "更新时间",
  "Monitoring": "监控",
  "Name": "名称",
  "New Journal": "新建日志",
  "Non working time action strategy": "非工作时间行动策略",
  "Number": "编号",
  "OK": "确定",
  "Operate": "操作",
  "Operation successful": "操作成功",
  "Overview of the situation": "状况简介",
  "Please Select": "请选择",
  "Please add the uploaded file": "请添加上传的文件",
  "Please enter the service number": "请输入服务编号",
  "Please select": "请选择",
  "Please select a service directory": "请选择服务包",
  "Poller": "采集机",
  "Port": "端口名称",
  "Priority": "优先级",
  "Priority matrix": "优先级矩阵",
  "Product": "产品",
  "Redo Log File": "日志文件",
  "Regional work strategy": "区域工作策略",
  "SLA": "SLA服务",
  "SLA name": "sla名称",
  "Security Container": "查看安全目录",
  "Select Line Vendor": "请选择线路供应商",
  "Select SLA service": "选择SLA服务",
  "Service Lever": "服务等级",
  "Service Numbers": "服务编号",
  "Service catalog": "服务包",
  "Service pack": "服务项",
  "Snmp Protocol": "SNMP协议",
  "Status": "状态",
  "Summary": "摘要",
  "Support Notes": "行动策略",
  "System Details": "系统详细信息",
  "System Name": "系统名称",
  "System OID": "系统OID",
  "System Start Time": "系统启动时间",
  "System Version": "系统版本",
  "Ticket": "工单",
  "Timestamp": "时间",
  "Type": "类型",
  "Unassign": "移除",
  "Upload Files": "上传文件",
  "Urgency": "紧急性",
  "Work order influence": "工单影响性",
  "Work order priority matrix": "工单优先级矩阵",
  "Work order urgency": "工单紧急性",
  "Working Time Action Strategy": "工作时间行动策略",
  "Workplace work strategy": "场所工作策略",
  "occurrence time": "发生时间",
  "size": "大小",
  "start using": "开始使用"
}
