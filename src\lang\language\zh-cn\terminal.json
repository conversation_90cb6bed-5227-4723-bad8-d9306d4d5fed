﻿{
  "Are you sure you want to republish?": "确认要重新发布吗？",
  "Back to terminal": "回到终端",
  "Clean up successful tasks when starting a new task": "开始新任务时清理已成功任务",
  "Clean up task list": "清理任务列表",
  "Command run log": "命令运行日志",
  "Connecting": "连接中...",
  "Do not refresh the browser": "请勿刷新浏览器",
  "Executing": "执行中...",
  "Execution failed": "执行失败",
  "Failure to execute this command will block the execution of the queue": "本命令执行失败会阻断队列执行",
  "I want to execute the command manually": "我想手动执行命令",
  "Install dependent packages": "安装依赖包",
  "Install service port": "安装服务端口",
  "Installation service URL": "安装服务URL",
  "Installation service startup command": "安装服务启动命令",
  "Newly added tasks will never start because they are blocked by failed tasks": "新添加的任务永远不会开始，因为被失败的任务阻塞！（WEB终端）",
  "No mission yet": "还没有任务...",
  "Package manager": "包管理器",
  "Please access the site through the installation service URL (except in debug mode)": "请通过安装服务Url访问站点（调试模式下例外）",
  "Please execute this command to start the service (add Su under Linux)": "请执行本命令以启动服务（Linux下加su）",
  "Please select package manager": "请选择包管理器",
  "Republish": "重新发布",
  "Site domain name": "站点域名",
  "Successful execution": "执行成功",
  "Switch package manager title": "只读WEB终端，可以在CRUD等操作后方便的执行 npm install、npm build 等命令，请在下方选择一个已安装好或您喜欢的NPM包管理器",
  "Terminal": "终端",
  "Terminal settings": "终端设置",
  "Test command": "测试命令",
  "The current terminal is not running under the installation service, and some commands may not be executed": "当前终端未运行于安装服务下，部分命令可能无法执行。",
  "The port number to start the installation service (this port needs to be opened for external network access)": "启动安装服务的端口号(外网访问则需对外开放该端口)",
  "Unknown execution result": "执行结果未知",
  "Waiting for execution": "等待执行",
  "ectTenant": "请选择租户",
  "or": "或",
  "unknown": "未知"
}
