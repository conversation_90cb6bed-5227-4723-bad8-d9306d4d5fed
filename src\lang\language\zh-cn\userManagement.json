﻿{
  "Account Expiration Date": "账号有效期",
  "Active": "激活",
  "After activation, the system will force users to change their passwords after a period of time": "启用后，一段时间后系统会强制用户修改密码",
  "After setting the validity period in bulk, if there are users who have not enabled the account validity period, the system will automatically enable it, and the validity period will take effect according to the time configured on the current page": "批量设置有效期时间后，如有未开启账号有效期的用户，系统会自动开启，有效期时间按照当前页面配置的时间生效。",
  "Any of the customer default policy settings may be changed for the user": "任何客户密码策略设置都可以为每个用户单独更改。",
  "AreYouSureToFreezeUser": "确认冻结用户吗",
  "AreYouSureToUnlockUser": "确定解锁用户吗",
  "At least 3 out of 4 categories including numbers, lowercase letters, uppercase letters, and special character symbols (.~! @ # $% ^&*?), with a minimum password length of 8 characters": "包括数字、小写字母、大写字母、特殊字符号(.~!@#$%^&*?)4类中至少3类， 最少密码长度8位",
  "At least one": "至少一个",
  "Batch Operation": "批量操作",
  "Confirm Password": "确认密码",
  "Confirm removal": "确定移除",
  "ConfirmPasswordCannotBeEmpty": "确认密码不能为空",
  "Custom": "自定义",
  "CustomValue": "自定义",
  "Customer affiliation": "所属客户",
  "CustomerPolicy": "客户密码策略",
  "Default": "默认",
  "DefaultValue": "默认值",
  "Disabled": "禁用",
  "EditUser": "编辑用户",
  "Email": "邮箱",
  "Enable": "启用",
  "Enforce password complexity": "强化密码复杂度",
  "Enforce password complexity tip": "启用后，系统只允许符合复杂度规则的密码。密码严禁使用默认的、易被猜测的字符串，(如admin、root、huawei、************、chinatelecom)等;密码应包括数字、小写字母、大写字母、特殊字符(.~!@#S%^&*?)4类中至少3类;密码应与账号(用户名)无相关性，密码不得包含账号的完整字符串、大小写变位或形似变换的字符串;密码设置应避免键盘排序密码",
  "Enter user account/email/phone number to query": "输入用户账号/邮箱/手机号 查询",
  "Feature Configuration": "功能配置",
  "FeatureConfiguration": "功能配置",
  "FreezeUser": "冻结用户",
  "Full name": "姓名",
  "Invited users?": "受邀用户吗？",
  "LatestLoginTime": "最近访问时间",
  "Maximum password age days": "密码最长使用天数",
  "Maximum password usage period": "密码最长使用期限",
  "MaximumPasswordAge": "密码最长使用天数",
  "Minimum password length": "最小密码长度",
  "MinimumPasswordLength": "最小密码强度",
  "Mobile": "手机号",
  "More": "更多",
  "NewUser": "新建用户",
  "NoData": "暂无数据",
  "NotActive": "未激活",
  "NotificationLanguage": "用户语言",
  "Password": "密码",
  "Password Expiration": "密码过期",
  "Password Policy": "密码策略",
  "Password expire": "密码过期",
  "Password history": "密码历史",
  "Password needs to be changed when logging in": "登录时是否需要修改密码",
  "Please select the account expiration date": "请选择账号有效期",
  "PleaseChoose": "请选择",
  "PleaseChooseNotificationLanguage": "请选择用户语言",
  "PleaseChooseUserGroup": "请选择用户组",
  "PleaseEnter": "请输入",
  "PleaseEnterEmail": "请输入用户邮箱",
  "PleaseEnterMobilePhoneNumber": "请输入手机号",
  "PleaseEnterName": "请输入姓名",
  "PleaseEnterUserPassword": "请输入用户密码",
  "PleaseEnterUsername": "请输入用户账号",
  "Registration time": "注册时间",
  "ResetPassword": "重置密码",
  "Search": "搜索",
  "SearchUser": "请输入关键字进行搜索",
  "Select User": "选择用户",
  "Selected": "已选择",
  "Set Password": "设置密码",
  "Settings": "功能配置",
  "State": "状态",
  "Strong password": "强密码",
  "The User Account Policy applies only to a specific user": "客户账号密码策略适用于客户中的所有用户，除非为用户应用了特定配置。",
  "The account starts with a letter and contains 1 to 64 uppercase and lowercase letters, numbers, and _#": "账号以字母开头，包含1到64位大小写字母数字和_?!#",
  "The minimum allowed length of a password": "密码允许的最小长度",
  "The number of days after which the system will force users to change their password": "系统强制用户更改密码的天数",
  "The number of new passwords that users must use before reusing old passwords to prevent users from reusing old passwords when changing them。": "用户在重复使用旧密码之前必须使用的新密码数，防止用户在修改密码时重复使用旧密码",
  "TimeZone": "用户时区",
  "Two passwords need to be consistent": "两次密码需要一致",
  "TwoFactorAuthentication": "双因素认证",
  "UnlockUser": "解锁用户",
  "User": "用户",
  "User Password": "用户密码",
  "User Password Policy": "用户密码策略",
  "User password cannot be empty": "用户密码不能为空",
  "UserAccountCannotBeEmpty": "用户账号不能为空",
  "UserCanChangePassword": "用户可以更改密码",
  "UserDefault": "默认",
  "UserGroup": "用户组",
  "UserNameCannotBeEmpty": "用户姓名不能为空",
  "Username": "账号",
  "Username login ID start with uppercase and lowercase letters, including 1 to 64 digits of uppercase and lowercase letters, numbers, underscores, question marks, exclamation marks, and pound signs": "账号以大小写字母开头，包含1到64位大小写字母、数字、下划线、问号、感叹号、井号",
  "ViewUserInformation": "查看用户",
  "thaw": "解锁",
  "AreYouSureToThawUser":"是否确定解锁该用户",
  "Unlock user":"账户解锁"
}
