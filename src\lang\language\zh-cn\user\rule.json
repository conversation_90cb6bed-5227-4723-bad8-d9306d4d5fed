﻿{
  "Normal routing": "普通路由",
  "Member center menu contents": "会员中心菜单目录",
  "Member center menu items": "会员中心菜单项",
  "English name": "英文名称",
  "Web side routing path": "web端路由路径(path)",
  "For example, if you add account/overview as a route only": "web端组件路径，请以/src开头，如:/src/views/frontend/index.vue",
  "Web side component path, please start with /src, such as: /src/views/frontend/index": "比如将`account/overview`只添加为路由，那么可以另外将`account/overview`、`account/overview/:a`、`account/overview/:b/:c`只添加为菜单"
}
