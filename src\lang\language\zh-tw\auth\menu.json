﻿{
  "Add as menu only": "只添加為菜單",
  "Add as route only": "只添加為路由",
  "Component path": "組件路徑",
  "English name, which does not need to start with `/admin`, such as auth/menu": "英文名稱，無需以`/admin`開頭，如:auth/menu",
  "Extended properties": "擴展屬性",
  "Icon": "圖標",
  "It will be registered as the web side routing name and used as the server side API authentication": "將註冊為web端路由名稱，同時作為server端API驗權使用",
  "Link address": "鏈接地址",
  "Menu type": "菜單類型",
  "Menu type link (offsite)": "鏈接(站外)",
  "Menu type tab": "選項卡",
  "Please enter the URL address of the link or iframe": "請輸入鏈接或Iframe的URL地址",
  "Please enter the correct URL": "請輸入正確的Url",
  "Please enter the weight of menu rule (sort by)": "請輸入菜單規則權重(排序依據)",
  "Routing path": "路由路徑",
  "Rule Icon": "規則圖標",
  "Rule comments": "規則備註",
  "Rule name": "規則名稱",
  "Rule title": "規則標題",
  "Rule type": "規則類型",
  "Rule weight": "規則權重",
  "Superior menu rule": "上級菜單規則",
  "The superior menu rule cannot be the rule itself": "上級菜單規則不能是規則本身",
  "The web side routing path (path) does not need to start with `/admin`, such as auth/menu": "web端路由路徑(path)，無需以`/admin`開頭，如:auth/menu",
  "Use in controller `get_ route_ Remark()` function, which can obtain the value of this field for your own use, such as the banner file of the console": "在控制器中使用`get_route_remark()`函數，可以獲得此字段值自用，比如控制台的banner文案",
  "Web side component path, please start with /src, such as: /src/views/backend/dashboard": "web端組件路徑，請以/src開頭，如:/src/views/backend/dashboard.vue",
  "cache": "緩存",
  "extend Title": "比如將`auth/menu`只添加為路由，那麼可以另外將`auth/menu`、`auth/menu/:a`、`auth/menu/:b/:c`只添加為菜單",
  "name": "名稱",
  "none": "無",
  "title": "標題",
  "type": "類型",
  "type button": "頁面按鈕",
  "type menu": "菜單項",
  "type menu_dir": "菜單目錄"
}
