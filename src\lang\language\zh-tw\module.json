﻿{
  "After installation 1": "在安裝結束後",
  "After installation 2": "安裝結束後",
  "Automatically execute reissue command?": "自動執行重新發布命令？",
  "Backup and overwrite existing files": "備份並覆蓋已有文件",
  "Balance payment": "餘額支付",
  "Buy now": "立即購買",
  "Click me to upload": "點擊我上傳",
  "Click to access": "點擊訪問",
  "Code scanning Preview": "掃碼預覽",
  "Confirm to disable the module": "確認禁用模塊",
  "Conflict file": "衝突文件",
  "Congratulations, module installation is complete": "恭喜，模塊安裝已完成。",
  "Congratulations, the code of the module is ready": "恭喜，模塊的代碼已經準備好了。",
  "Contact developer": "聯繫開發者",
  "Dependencies": "依賴項",
  "Dependency conflict": "依賴衝突",
  "Dependency installation completed~": "依賴已安裝完成~",
  "Dependency installation fail 1": "依賴安裝失敗，請點擊",
  "Dependency installation fail 2": "終端",
  "Dependency installation fail 3": "中的重試按鈕，您也可以查看",
  "Dependency installation fail 4": "手動完成未盡事宜",
  "Dependency installation fail 5": "在您",
  "Dependency installation fail 6": "確定依賴已準備好",
  "Dependency installation fail 7": "之前，模塊還不能正常使用！",
  "Developer Homepage": "開發者主頁",
  "Disable and update": "禁用並更新",
  "Discard new file": "丟棄新文件",
  "Do not refresh the page!": "請勿刷新頁面！",
  "Do not use new dependencies": "不使用新依賴",
  "Drag the module package file here": "拖拽模塊包文件到此處或",
  "End of installation": "安裝結束",
  "Existing dependencies": "已有依賴",
  "Existing files": "已有文件",
  "File conflict": "文件衝突",
  "Get points": "獲得積分",
  "Install now": "立即安裝",
  "Is the command that failed on the WEB terminal executed manually or in other ways successfully?": "WEB終端失敗的命令已經手動或以其他方式執行成功？",
  "Last updated": "最後更新",
  "Loading": "加載中...",
  "Local module": "本地模塊",
  "Local upload warning": "請您務必確認模塊包文件來自官方渠道或經由官方認證的模塊作者，否則係統可能被破壞，因為：",
  "Log in to the buildadmin module marketplace": "登錄到 BuildAdmin 模塊市場",
  "Manually clean up the system and browser cache, and refresh the page": "手動的清理系統和瀏覽器緩存，並刷新頁面。",
  "Member information": "會員信息",
  "Module classification": "模塊分類",
  "Module installation warning": "購買後一年內可免費下載和更新，虛擬產品不支持7天無理由退款",
  "Module is disabled": "模塊已禁用。",
  "Module purchase and use agreement": "模塊購買和使用協議",
  "Module status": "模塊狀態",
  "My module": "我的模塊",
  "New adjustment of dependency detected": "檢測到依賴項有新的調整",
  "New dependency": "新依賴",
  "No detailed update log": "無詳細更新日誌",
  "No more": "沒有更多了...",
  "Order No": "訂單編號",
  "Order price": "訂單價格",
  "Order title": "訂單標題",
  "Other works of developers": "TA的其他作品",
  "Overwrite existing dependencies": "覆蓋已有依賴",
  "Please enter buildadmin account name or email": "請輸入 BuildAdmin 賬戶名/郵箱/手機號",
  "Please enter the buildadmin account password": "請輸入 BuildAdmin 賬戶密碼",
  "Please enter the login verification code": "請輸入登錄驗證碼",
  "Point payment": "積分支付",
  "Price": "價格",
  "Published on": "發佈時間",
  "Publishing module": "發布模塊",
  "Purchase user": "購買用戶",
  "Purchased, can be installed directly": "已購買，可直接安裝",
  "Register": "沒有賬戶？\n去註冊",
  "Search is actually very simple": "搜索其實很簡單",
  "Sign in": "登錄",
  "The built-in terminal of the system is automatically installing these dependencies, please wait~": "系統內置終端正在自動安裝這些依賴，請稍等~",
  "The module can execute sql commands and codes": "模塊可以執行sql命令和代碼",
  "The module can install new front and rear dependencies": "模塊可以安裝新的前後端依賴",
  "The module can modify and add system files": "模塊可以修改和新增系統文件",
  "The module declares the added dependencies": "模塊聲明添加的依賴",
  "There are no more works": "沒有更多作品了",
  "There is no adjustment for system dependency": "系統依賴無調整。",
  "This module adds new dependencies": "本模塊添加了新的依賴項",
  "This module does not add new dependencies": "本模塊沒有添加新的依賴項。",
  "Treatment scheme": "處理方案",
  "Understand and agree": "理解並同意",
  "Unknown state": "未知狀態。",
  "Update Log": "更新日誌",
  "Update warning": "檢測到以下的模塊文件有更新，禁用時將自動覆蓋，請注意備份。",
  "Upload installation": "上傳安裝",
  "Upload zip package for installation": "上傳ZIP包安裝",
  "Uploaded / installed modules": "已上傳/安裝的模塊",
  "Uploaded, installation is about to start, please wait": "已上傳，即將開始安裝，請稍等",
  "View demo": "查看演示",
  "View progress": "查看進度",
  "You need to disable this module before updating Do you want to disable it now?": "更新前需要先禁用該模塊，立即禁用？",
  "amount of downloads": "下載次數",
  "continue installation": "繼續安裝",
  "detailed information": "詳細信息",
  "env dependencies": "前端依賴（NPM）",
  "env devDependencies": "前端開發環境依賴（NPM）",
  "env require": "後端依賴（composer）",
  "env require-dev": "後端開發環境依賴（composer）",
  "environment": "環境",
  "installed": "已安裝",
  "new file": "新文件",
  "no": "否",
  "payment": "支付",
  "please": "請",
  "retain": "保留",
  "stateTitle download": "正在下載模塊...",
  "stateTitle init": "模塊安裝器初始化...",
  "stateTitle install": "正在安裝模塊...",
  "to update": "更新",
  "uninstall": "卸載",
  "yes": "是"
}
