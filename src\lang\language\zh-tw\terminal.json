﻿{
  "Are you sure you want to republish?": "確認要重新發布嗎？",
  "Back to terminal": "回到終端",
  "Clean up successful tasks when starting a new task": "開始新任務時清理已成功任務",
  "Clean up task list": "清理任務列表",
  "Command run log": "命令運行日誌",
  "Connecting": "連接中...",
  "Do not refresh the browser": "請勿刷新瀏覽器",
  "Executing": "執行中...",
  "Execution failed": "執行失敗",
  "Failure to execute this command will block the execution of the queue": "本命令執行失敗會阻斷隊列執行",
  "I want to execute the command manually": "我想手動執行命令",
  "Install dependent packages": "安裝依賴包",
  "Install service port": "安裝服務端口",
  "Installation service URL": "安裝服務URL",
  "Installation service startup command": "安裝服務啟動命令",
  "Newly added tasks will never start because they are blocked by failed tasks": "新添加的任務永遠不會開始，因為被失敗的任務阻塞！ \n（WEB終端）",
  "No mission yet": "還沒有任務...",
  "Package manager": "包管理器",
  "Please access the site through the installation service URL (except in debug mode)": "請通過安裝服務Url訪問站點（調試模式下例外）",
  "Please execute this command to start the service (add Su under Linux)": "請執行本命令以啟動服務（Linux下加su）",
  "Please select package manager": "請選擇包管理器",
  "Republish": "重新發布",
  "Site domain name": "站點域名",
  "Successful execution": "執行成功",
  "Switch package manager title": "只讀WEB終端，可以在CRUD等操作後方便的執行 npm install、npm build 等命令，請在下方選擇一個已安裝好或您喜歡的NPM包管理器",
  "Terminal": "終端",
  "Terminal settings": "終端設置",
  "Test command": "測試命令",
  "The current terminal is not running under the installation service, and some commands may not be executed": "當前終端未運行於安裝服務下，部分命令可能無法執行。",
  "The port number to start the installation service (this port needs to be opened for external network access)": "啟動安裝服務的端口號(外網訪問則需對外開放該端口)",
  "Unknown execution result": "執行結果未知",
  "Waiting for execution": "等待執行",
  "ectTenant": "請選擇租戶",
  "or": "或",
  "unknown": "未知"
}
