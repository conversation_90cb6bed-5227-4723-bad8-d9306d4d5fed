﻿{
  "English name": "英文名稱",
  "For example, if you add account/overview as a route only": "web端組件路徑，請以/src開頭，如:/src/views/frontend/index.vue",
  "Member center menu contents": "會員中心菜單目錄",
  "Member center menu items": "會員中心菜單項",
  "Normal routing": "普通路由",
  "Web side component path, please start with /src, such as: /src/views/frontend/index": "比如將`account/overview`只添加為路由，那麼可以另外將`account/overview`、`account/overview/:a`、`account/overview/:b/:c`只添加為菜單",
  "Web side routing path": "web端路由路徑(path)"
}
