<template>
  <el-main class="tw-h-full">
    <el-card class="tw-flex tw-h-full tw-items-center" :body-style="{ width: '100%' }">
      <div class="main-loading tw-mb-[30px]">
        <LoadingStyle></LoadingStyle>
        <p>{{ $t("utils.Loading") }}</p>
      </div>
      <div v-if="state.showReload" class="loading-footer">
        <el-button type="warning" @click="refresh">{{ $t("utils.Reload") }}</el-button>
      </div>
    </el-card>
  </el-main>
</template>

<script setup lang="ts">
import { onUnmounted, reactive } from "vue";
import router from "@/router/index";
import LoadingStyle from "@/layouts/common/components/loadingStyle.vue";
let timer: NodeJS.Timer;

const state = reactive({
  maximumWait: 1000 * 6,
  showReload: false,
});

const refresh = () => {
  router.go(0);
};

timer = setTimeout(() => {
  state.showReload = true;
}, state.maximumWait);

onUnmounted(() => {
  clearTimeout(timer);
});
</script>

<style scoped lang="scss">
.main-loading {
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.loading-footer {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
