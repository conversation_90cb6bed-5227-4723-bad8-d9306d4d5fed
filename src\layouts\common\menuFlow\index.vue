<template>
  <div class="inner">
    <div class="list_star">
      <h3>快捷访问</h3>
      <draggable v-model="navTabs.state.star" handle=".move-group" group="star" tag="ul" class="tw-flex tw-flex-wrap tw-items-start tw-justify-start" item-key="" @change="updateStar">
        <template #item="{ element: nav }">
          <li class="star_item tw-text-ellipsisl tw-m-[2px] tw-flex tw-h-[36px] tw-w-full tw-items-center tw-justify-start tw-overflow-hidden tw-whitespace-nowrap">
            <i class="move-group">
              <Icon class="tw-align-top" size="16px" color="inherit" :name="navTabs.allNavigation.find(({ id }) => id === nav)?.icon"></Icon>
            </i>
            <div style="width: calc(100% - 40px)">
              <el-link class="tw-max-w-ful tw-text-ellipsisl tw-ml-[6px] tw-overflow-hidden" @click="() => (emits('done'), routePush((allNav.find(({ id }) => id === nav) as NavigationDataItem)?.route))">
                <span>{{ navTabs.allNavigation.find(({ id }) => id === nav)?.name }}</span>
              </el-link>
            </div>
            <i class="close-btn" @click="delStar(nav)"></i>
          </li>
        </template>
      </draggable>
    </div>
    <div ref="listRef" class="list_inner">
      <div class="lists_more" :style="{ width: `calc(100% / ${waterfallList.length})` }" v-for="(row, i) of waterfallList" :key="`row_${i}`">
        <div class="list_item" v-for="(item, j) in row" :key="`col_${j}`">
          <!-- 自定义瀑布流内容 start -->
          <div class="tw-my-[6px] tw-h-[22px] tw-leading-[22px]">
            <h3 class="tw-font-semibold">{{ item.name }}</h3>
          </div>
          <div class="tw-mb-[6px] tw-whitespace-nowrap tw-px-[4px]">
            <div v-for="nav in item.items" :key="nav.id" class="main_item tw-flex tw-h-[32px] tw-items-center tw-py-6 tw-leading-[20px]">
              <el-link v-if="navTabs.state.star.includes(nav.id)" class="item_star active" :underline="false" :icon="StarFilled" @click="delStar(nav.id)"></el-link>
              <el-link v-else class="item_star" :underline="false" :icon="Star" @click="addStar(nav.id)"></el-link>

              <el-link class="tw-max-w-ful tw-text-ellipsisl tw-ml-[6px] tw-overflow-hidden" @click="() => (emits('done'), routePush(nav.route))">
                <Icon :name="nav.icon" color="inherit" class="tw-mr-1"></Icon>
                <span>{{ nav.name }}</span>
              </el-link>
            </div>
          </div>
          <!-- 自定义瀑布流内容 end -->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="MenuFlow">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { onMounted, computed, nextTick, ref, watch } from "vue";
import { useNavTabs } from "@/stores/navTabs";
import { StarFilled, Star } from "@element-plus/icons-vue";
import { routePush } from "@/utils/router";
import { modUserNavigation, type NavigationDataItem } from "@/api/system";
import { ElMessage } from "element-plus";
import draggable from "vuedraggable";
import { update } from "lodash-es";

const navTabs = useNavTabs();

const listRef = ref(document.createElement("div"));
const wrapWidth = ref(0); // 瀑布的wrap的宽度
onMounted(async () => {
  // navTabs.updateNavigation();
  await nextTick();
  wrapWidth.value = listRef.value.clientWidth;
});

async function addStar(star: string) {
  navTabs.state.star.push(star);
  await updateStar();
}
async function delStar(star: string) {
  navTabs.state.star.splice(navTabs.state.star.indexOf(star), 1);
  await updateStar();
}
async function updateStar() {
  try {
    const { success, message, data } = await modUserNavigation({ value: { star: navTabs.state.star } });
    if (!success) throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
}

const emits = defineEmits<{
  (e: "done"): void;
}>();

const waterfallList = computed(() => {
  const list: (typeof navTabs.state.navigation)[] = new Array(5).fill(null).map(() => []);
  const size: number[] = list.map(() => 0);

  for (let i = 0; i < navTabs.state.navigation.length; i++) {
    const index = size.findIndex((n) => n === Math.min(...size));
    const item = navTabs.state.navigation[i];
    size[index] += 40 + 32 * item.items.length;
    list[index].push(item);
  }

  return list;
});
const allNav = computed(() => {
  return navTabs.state.navigation.reduce((p, c) => p.concat(c.items instanceof Array ? c.items : ([] as NavigationDataItem[])), [] as NavigationDataItem[]);
});
</script>

<style lang="scss" scoped>
.main_item {
  &:hover {
    .item_star {
      opacity: 1;
    }
  }
  .item_star {
    opacity: 0;
    font-size: 18px;
    &.active {
      opacity: 1;
      transform: scale(1.4);
      color: var(--el-color-warning);
    }
  }
}
.inner {
  width: 850px;
  height: 100%;
  overflow: auto;
  padding: 12px;
  display: flex;
}
.list_star {
  width: 140px;
  margin: 0 20px;

  .star_item {
    outline: var(--el-border);
    padding: 0 4px;

    .move-group {
      visibility: hidden;
      width: 16px;
      height: 16px;
      display: inline-block;
      vertical-align: middle;
      background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTExIDEzdjIuMDAxSDlWMTNoMnptLTQgMHYyLjAwMUg1VjEzaDJ6bTQtNHYyLjAwMUg5VjloMnpNNyA5djIuMDAxSDVWOWgyem00LTR2Mi4wMDFIOVY1aDJ6TTcgNXYyLjAwMUg1VjVoMnptNC00djIuMDAxSDlWMWgyek03IDF2Mi4wMDFINVYxaDJ6IiBmaWxsPSIjODg4IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=);
      cursor: move;
      > * {
        visibility: visible;
      }
    }

    .close-btn {
      visibility: hidden;
      width: 16px;
      height: 16px;
      display: inline-block;
      vertical-align: middle;
      background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiI+PHBhdGggZmlsbD0iIzg4OCIgZD0iTTggMWE3IDcgMCAxMDAgMTRBNyA3IDAgMDA4IDF6bTMuNTM2IDkuMTIxbC0xLjQxNCAxLjQxNEw4IDkuNDE0bC0yLjEyMSAyLjEyMS0xLjQxNC0xLjQxNEw2LjU4NiA4IDQuNDY0IDUuODc5bDEuNDE0LTEuNDE0TDggNi41ODZsMi4xMjEtMi4xMjEgMS40MTQgMS40MTRMOS40MTQgOGwyLjEyMiAyLjEyMXoiLz48L3N2Zz4=);
      cursor: pointer;
    }
    &:hover {
      .move-group,
      .close-btn {
        visibility: visible;
        > * {
          visibility: hidden;
        }
      }
    }
  }
}
// 瀑布流样式
.list_inner {
  width: calc(100% - 180px);
  display: flex;
  height: fit-content;
  min-height: 50px;
  justify-content: space-between;
  flex-wrap: wrap;
  .lists_more {
    height: min-content;
    .list_item {
      margin-bottom: 6px;
      border-radius: 4px;
      overflow: hidden;
      img {
        width: 100%;
        min-height: 100px;
        max-height: 300px;
        display: block;
      }
      .list_detail {
        padding: 6px 12px;
        background: #fff;

        .list_name,
        .list_pos {
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          line-clamp: 1;
          -webkit-box-orient: vertical;
          word-break: break-all;
        }
        .list_name {
          font-size: 14px;
          font-weight: 600;
          color: #333333;
          line-height: 20px;
          margin-bottom: 4px;
        }
        .list_pos {
          font-size: 12px;
          color: #666666;
          line-height: 17px;
        }
      }
    }
  }
}
</style>
