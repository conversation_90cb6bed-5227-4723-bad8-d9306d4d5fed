<template>
  <micro-app keep-alive :name="name" :url="url" :baseroute="baseroute" iframe disable-memory-router class="micro-view" :data="baseData" :style="{ height: `${height}px`, width: `${width}px` }" @created="() => (loading = true)" @beforeshow="() => (loading = true)" @aftershow="() => (loading = false)" @mounted="() => updata()" @error="() => (loading = false)" @datachange="microEvent"></micro-app>
</template>

<script setup lang="ts" name="MicroApp">
/* eslint-disable @typescript-eslint/no-unused-vars, no-console */
import { ref, inject, computed, watch, onMounted, shallowRef, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";
import { parse } from "path-to-regexp";
// import { handleRoute, getMenuPaths, getFirstRoute } from "@/utils/router";
import type { UseInfo, Layout, SiteConfig } from "@/stores/interface";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
import { useSiteConfig } from "@/stores/siteConfig";
import { useNavTabs } from "@/stores/navTabs";
import { useConfig } from "@/stores/config";
import { find } from "lodash-es";

const superInfo = useSuperInfo();
const adminInfo = useAdminInfo();
const usersInfo = useUsersInfo();

const navTabs = useNavTabs();
const siteConfig = useSiteConfig();
const config = useConfig();
const loading = ref(false);

const route = useRoute();
const router = useRouter();

const baseData = ref(getBase());

const name = computed<string>(() => {
  return (route.name as string).split("/").filter((v) => v)[0];
});

const url = computed<string>((): string => {
  const url = String(route.meta.url || "");
  if (/^\:[0-9]/.test(url)) return `${location.protocol}//${location.hostname}${url}`;
  else return String(new URL(url, location.origin));
});

const baseroute = computed<string>((): string => {
  const url = new URL(route.fullPath, location.origin);
  const base = find(parse(route.matched[route.matched.length - 1].path), (v) => typeof v === "string") as string;
  return base ? base : url.pathname;
});

async function updata() {
  loading.value = false;
  // console.log("[ onUpdata ]");
  await nextTick();
  baseData.value = getBase();
}

onMounted(() => {
  updata();
});

type BaseOption = Record<"path" | "name" | "title" | "baseroute" | "fullPath", string> & { meta: Record<string, unknown>; userInfo: UseInfo; config: { layout: Layout; site: SiteConfig } };

const bindRegExp = (reg: string) =>
  new RegExp(
    `^${reg
      .split("")
      .filter((v) => v)
      .map((v) => `${["*", ".", "?", "+", "^", "$", "|", "\\", "/", "[", "]", "(", ")", "{", "}"].includes(v) ? "\\" : ""}${v}`)
      .join("")}.*$`,
    "g"
  );

const width = inject<import("vue").Ref<number>>("width", ref(0));
const height = inject<import("vue").Ref<number>>("height", ref(0));

interface BaseEventData {
  baseroute: string;
  fullPath: string;
  meta: import("vue-router").RouteMeta;
  userInfo: ReturnType<import("@/stores/interface").UserStoreData>;
  microEvent: (data: Partial<MicroEventData>) => Promise<void>;
  config: { layout: import("@/stores/interface").Layout; site: import("@/stores/interface").SiteConfig; nav: import("@/utils/router").RouteLocation | null };
  title: string;
  name: string;
  path: string;
}
function getBase(): BaseEventData {
  const url = new URL(route.fullPath, location.origin);
  const base = find(parse(route.matched[route.matched.length - 1].path), (v) => typeof v === "string") as string;
  const baseroute = base ? base : url.pathname;
  switch (route.matched[0].name) {
    case superBaseRoute.name:
      return { ...superBaseRoute, baseroute, fullPath: route.fullPath, meta: route.meta, userInfo: superInfo, microEvent, config: { layout: config.layout, site: siteConfig, nav: navTabs.state.activeRoute } };
    case adminBaseRoute.name:
      return { ...adminBaseRoute, baseroute, fullPath: route.fullPath, meta: route.meta, userInfo: adminInfo, microEvent, config: { layout: config.layout, site: siteConfig, nav: navTabs.state.activeRoute } };
    case usersBaseRoute.name:
      return { ...usersBaseRoute, baseroute, fullPath: route.fullPath, meta: route.meta, userInfo: usersInfo, microEvent, config: { layout: config.layout, site: siteConfig, nav: navTabs.state.activeRoute } };
    default:
      return { ...usersBaseRoute, baseroute, fullPath: route.fullPath, meta: route.meta, userInfo: usersInfo, microEvent, config: { layout: config.layout, site: siteConfig, nav: navTabs.state.activeRoute } };
  }
}

watch(
  () => route.fullPath,
  async () => {
    // console.log("onBeforeRouteUpdate");
    await nextTick();
    updata();
  }
);
watch(
  () => config.layout,
  () => updata(),
  { deep: true }
);
watch(
  () => superInfo,
  () => route.matched[0].name === superBaseRoute.name && updata(),
  { deep: true }
);
watch(
  () => adminInfo,
  () => route.matched[0].name === adminBaseRoute.name && updata(),
  { deep: true }
);
watch(
  () => usersInfo,
  () => route.matched[0].name === usersBaseRoute.name && updata(),
  { deep: true }
);

interface MicroEventData {
  fullPath?: string;
  layout?: Partial<typeof config.layout>;
  detail?: { data: MicroEventData };
}
async function microEvent(data: MicroEventData) {
  if (Object.prototype.hasOwnProperty.call(data, "detail")) data = data.detail?.data || data;
  if (data.fullPath) {
    const url = new URL(route.fullPath, location.origin);
    const base = find(parse(route.matched[route.matched.length - 1].path), (v) => typeof v === "string");
    const baseroute = base ? base : url.pathname;
    const path = `${baseroute}${data.fullPath || ""}`;
    if (path !== route.fullPath) await router.push(path);
  }
  if (data.layout) {
    if (Object.prototype.hasOwnProperty.call(data.layout, "menuCollapse")) {
      if (config.layout.menuCollapse === data.layout.menuCollapse) return;
      config.setLayout("menuCollapse", data.layout.menuCollapse);
      updata();
    }
  }
}
</script>

<style lang="scss" scoped>
.micro-view {
  &::after,
  &::before {
    content: "";
    display: block;
    height: 0px;
    clear: both;
  }
}
</style>
