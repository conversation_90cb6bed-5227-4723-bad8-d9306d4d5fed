<template>
  <template v-if="props.item.type === appType.BUTTON"></template>
  <template v-else-if="[appType.DIR].includes(props.item.type as appType)">
    <!--  "false" -->
    <el-sub-menu v-if="item.children.filter((v) => [appType.DIR, appType.MENU, appType.MICRO, appType.LINK].includes(v.type as appType)).length" :key="`${props.item.type}${props.item.name}`" :teleported="false" :index="props.item.name" popper-class="menu-container-variable">
      <template #title>
        <Icon color="" :name="props.item.icon ? props.item.icon : config.layout.menuDefaultIcon" />
        <span style="margin-left: 6px; font-weight: normal">{{ props.item.title ? props.item.title : $t("pagesTitle.noTitle") }}</span>
      </template>
      <template #default>
        <HorizontalMenuTree v-for="_item in item.children || []" :key="`${item.name}_${_item.name}`" :item="_item" :style="props.style"></HorizontalMenuTree>
      </template>
    </el-sub-menu>
  </template>
  <template v-else-if="[appType.MENU, appType.MICRO].includes(props.item.type as appType)">
    <el-menu-item :key="`${props.item.type}${props.item.name}`" :index="props.item.name" :route="{ name: props.item.name, query: props.item.query, params: props.item.params }">
      <template #default>
        <Icon color="" :name="props.item.icon ? props.item.icon : config.layout.menuDefaultIcon" />
      </template>
      <template #title>
        <span style="margin-left: 6px; font-weight: normal">{{ props.item.title ? props.item.title : $t("pagesTitle.noTitle") }}</span>
      </template>
    </el-menu-item>
  </template>
  <template v-else-if="[appType.LINK].includes(props.item.type as appType)">
    <el-menu-item :key="`${props.item.type}${props.item.name}`" :index="props.item.name" :route="{}" @click="onClickMenu(props.item)">
      <template #default>
        <Icon color="" :name="props.item.icon ? props.item.icon : config.layout.menuDefaultIcon" />
      </template>
      <template #title>
        <span style="margin-left: 6px; font-weight: normal">{{ props.item.title ? props.item.title : $t("pagesTitle.noTitle") }}</span>
      </template>
    </el-menu-item>
  </template>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";

import HorizontalMenuTree from "@/layouts/component/HorizontalMenuTree.vue";
import { appType, type NavItem } from "@/utils/router";
import { useConfig } from "@/stores/config";
import { onClickMenu } from "@/utils/router";
import { ElSubMenu } from "element-plus";

const config = useConfig();

function initStyle(vm: InstanceType<typeof ElSubMenu>) {
  // console.log(vm.$el);
}

defineOptions({ name: "HorizontalMenuTree" });
interface Props {
  item: NavItem;
  style: Record<string, string>;
}
const props = defineProps<Props>();
</script>
