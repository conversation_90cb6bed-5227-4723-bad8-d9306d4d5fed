<template>
  <div v-if="!config.layout.shrink" class="tw-mr-[16px] tw-flex tw-h-full tw-flex-shrink-0 tw-cursor-pointer tw-items-center tw-justify-center" :style="{ color: config.getColorVal('headerBarTabColor'), width: `${config.layout.menuCollapse ? 64 : 260}px` }" @click="$router.push('/')">
    <img src="@/assets/logo3.png" width="48" class="tw-mx-[] tw-flex-shrink-[8]" />
    <span v-show="!config.layout.menuCollapse" class="tw-ml-[12px] tw-w-[calc(100%-76px)] tw-flex-shrink tw-text-[24px] tw-font-semibold" :style="{ color: config.getColorVal('headerBarTabColor') }">{{ siteConfig.openName }}</span>
  </div>
  <el-link v-else :underline="false" :icon="config.layout.menuCollapse ? Expand : Fold" class="tw-cursor-pointer tw-text-[18px]" :style="{ color: config.getColorVal('headerBarTabColor'), margin: '0 auto 0 0' }" @click.stop.prevent="onMenuCollapse"></el-link>
</template>

<script setup lang="ts">
import { useSiteConfig } from "@/stores/siteConfig";
import { Expand, Fold } from "@element-plus/icons-vue";
import { useConfig } from "@/stores/config";
const config = useConfig();
const siteConfig = useSiteConfig();

const onMenuCollapse = function () {
  config.setLayout("menuCollapse", !config.layout.menuCollapse);
};
</script>

<style scoped lang="scss"></style>
