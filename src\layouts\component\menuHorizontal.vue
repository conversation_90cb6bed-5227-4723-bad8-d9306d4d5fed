<template>
  <div class="layouts-menu-horizontal">
    <div v-if="config.layout.menuShowTopBar" class="menu-horizontal-logo">
      <Logo />
    </div>
    <el-scrollbar ref="horizontalMenusRef" class="horizontal-menus-scrollbar">
      <el-menu :key="state.menuKey" class="menu-horizontal" mode="horizontal" :ellipsis="false" :default-active="state.defaultActive">
        <!-- 横向菜单直接使用 <MenuTree :menus="menus" /> 会报警告 -->
        <template v-for="menu in menus">
          <template v-if="menu.children && menu.children.length > 0">
            <el-sub-menu :key="menu.path" :index="menu.path">
              <template #title>
                <Icon :color="config.getColorVal('menuColor')" :name="menu.icon ? menu.icon : config.layout.menuDefaultIcon" />
                <span>{{ menu.title ? menu.title : $t("pagesTitle.noTitle") }}</span>
              </template>
              <menu-tree :menus="menu.children"></menu-tree>
            </el-sub-menu>
          </template>
          <template v-else>
            <el-menu-item :key="menu.path" :index="menu.path" @click="onClickMenu(menu)">
              <Icon :color="config.getColorVal('menuColor')" :name="menu.icon ? menu.icon : config.layout.menuDefaultIcon" />
              <span>{{ menu.title ? menu.title : $t("pagesTitle.noTitle") }}</span>
            </el-menu-item>
          </template>
        </template>
      </el-menu>
    </el-scrollbar>
    <NavMenus />
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref } from "vue";
import Logo from "./logo.vue";
import MenuTree from "./menuTree.vue";
import { useRoute, onBeforeRouteUpdate, RouteLocationNormalizedLoaded } from "vue-router";
import { useConfig } from "@/stores/config";
import { useNavTabs } from "@/stores/navTabs";
import type { ElScrollbar } from "element-plus";
import NavMenus from "@/layouts/component/navMenus.vue";
import { onClickMenu } from "@/utils/router";

const horizontalMenusRef = ref<InstanceType<typeof ElScrollbar>>();

const config = useConfig();
const navTabs = useNavTabs();
const route = useRoute();

const state = reactive({
  menuKey: uuid(),
  defaultActive: "",
});

const menus = computed(() => {
  state.menuKey = uuid(); // eslint-disable-line
  return navTabs.state.tabsViewRoutes;
});

// 激活当前路由的菜单
const currentRouteActive = (currentRoute: RouteLocationNormalizedLoaded) => {
  state.defaultActive = currentRoute.path;
};

// 滚动条滚动到激活菜单所在位置
const verticalMenusScroll = () => {
  nextTick(() => {
    let activeMenu: HTMLElement | null = document.querySelector(".el-menu.menu-horizontal li.is-active");
    if (!activeMenu) return false;
    horizontalMenusRef.value?.setScrollTop(activeMenu.offsetTop);
  });
};

onMounted(() => {
  currentRouteActive(route);
  verticalMenusScroll();
});

onBeforeRouteUpdate((to) => {
  currentRouteActive(to);
});
function uuid(unit = `${Date.now().toString(36).padStart(8, "x")}-xxxx-4xxx-yxxx-xxxxxxxxxxxx`): string {
  return (unit === "x" ? (Math.random() * 16) | 0 : (((Math.random() * 16) | 0) & 0x3) | 0x8).toString(16);
}
</script>

<style scoped lang="scss">
.layouts-menu-horizontal {
  display: flex;
  align-items: center;
  width: 100vw;
  height: 60px;
  background-color: var(--ba-bg-color-overlay);
  border-bottom: solid 1px var(--el-color-info-light-8);
}
.menu-horizontal-logo {
  width: 180px;
  &:hover {
    background-color: v-bind('config.getColorVal("headerBarHoverBackground")');
  }
}
.menu-horizontal {
  border: none;
  --el-menu-bg-color: v-bind('config.getColorVal("menuBackground")');
  --el-menu-text-color: v-bind('config.getColorVal("menuColor")');
  --el-menu-active-color: v-bind('config.getColorVal("menuActiveColor")');
}

.el-sub-menu .icon,
.el-menu-item .icon {
  vertical-align: middle;
  margin-right: 5px;
  width: 24px;
  text-align: center;
}
.is-active .icon {
  color: var(--el-menu-active-color) !important;
}
.el-menu-item.is-active {
  background-color: v-bind('config.getColorVal("menuActiveBackground")');
}
</style>
