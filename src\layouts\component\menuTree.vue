<template>
  <template v-for="menu in props.menus">
    <template v-if="menu.type === appType.ROUTE ? !props.menuNames.includes(menu.name) : menu.type === appType.DIR ? !haveChildren(menu) : false"></template>
    <template v-else-if="menu.type === appType.DIR">
      <el-sub-menu :key="menu.name" :index="menu.name as string">
        <template #title>
          <Icon :color="config.getColorVal('menuColor')" :name="menu.icon ? menu.icon : config.layout.menuDefaultIcon" />
          <span>{{ menu.title ? menu.title : $t("pagesTitle.noTitle") }}</span>
        </template>
        <menu-tree :menus="menu.children" :menu-names="props.menuNames"></menu-tree>
      </el-sub-menu>
    </template>
    <template v-else>
      <el-menu-item :key="menu.name" :index="menu.name as string" @click="onClickMenu(menu)" @contextmenu.prevent="($event: MouseEvent) => onContextmenu(menu, $event)">
        <Icon :color="config.getColorVal('menuColor')" :name="menu.icon ? menu.icon : config.layout.menuDefaultIcon" />
        <template #title>
          <span>{{ menu.title ? menu.title : $t("pagesTitle.noTitle") }}</span>
        </template>
      </el-menu-item>
    </template>
  </template>
  <Contextmenu ref="contextmenuRef" :items="state.contextmenuItems" @contextmenuItemClick="onContextmenuItem" />
</template>
<script setup lang="ts">
import { nextTick, reactive, computed, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useConfig } from "@/stores/config";
import { onClickMenu } from "@/utils/router";
import { useNavTabs } from "@/stores/navTabs";
import { type NavItem, appType } from "@/api/application";
import type { ContextMenuItem, ContextmenuItemClickEmitArg } from "@/components/contextmenu/interface";

import Contextmenu from "@/components/contextmenu/index.vue";

defineOptions({ name: "MenuTree" });

const config = useConfig();
const route = useRoute();
const navTabs = useNavTabs();

interface Props {
  menus: NavItem[];
  menuNames: string[];
}
const props = withDefaults(defineProps<Props>(), {
  menus: () => [],
  menuNames: () => [],
});

const state: {
  contextmenuItems: ContextMenuItem[];
} = reactive({
  contextmenuItems: [
    { name: "refresh", label: "新标签页打开", icon: "fa fa-refresh" },

    // { name: "closeOther", label: "关闭其他标签", icon: "fa fa-minus" },
    // { name: "closeAll", label: "关闭全部标签", icon: "fa fa-stop" },
  ],
});

function haveChildren(menu: NavItem): boolean {
  if ([appType.LINK, appType.MICRO, appType.MENU].includes(menu.type as appType)) return true;
  else if ([appType.ROUTE].includes(menu.type as appType)) {
    // // console.log(`[props.menuNames.includes("${menu.name}")]`, props.menuNames.includes(menu.name));
    const isfind = props.menuNames.includes(menu.name);
    return isfind;
  }
  if (menu.children instanceof Array && menu.children.length) {
    for (let index = 0; index < menu.children.length; index++) {
      const isFind = haveChildren(menu.children[index]);
      if (isFind) {
        // // console.log("[menu]", menu);
        return true;
      }
    }
  }
  return false;
}
const contextmenuRef = ref();
const router = useRouter();
const onContextmenu = (menu: NavItem, el: MouseEvent) => {
  // 禁用刷新
  // state.contextmenuItems[0].disabled = !(menu.names instanceof Array ? menu.names : [menu.name]).includes(navTabs.state.activeName);
  const { clientX, clientY } = el;
  contextmenuRef.value.onShowContextmenu(menu, { x: clientX, y: clientY });
};
const onContextmenuItem = async ({ name, data }: ContextmenuItemClickEmitArg) => {
  const config = JSON.parse(data.config || {});
  const routeData = router.resolve({ name: data.type === "LINK" ? config.url : data.name }, { target: "_blank" });
  const link = document.createElement("a");
  link.setAttribute("href", routeData.href);
  link.setAttribute("target", "_blank");
  document.body.appendChild(link);
  link.click();
  // if (!data) return;
  // switch (name) {
  //   case "refresh":
  //     proxy.eventBus.emit("onTabViewRefresh", data);
  //     break;
  //   // case "close":
  //   //   closeTab(menu);
  //   //   break;
  //   // case "closeOther":
  //   //   navTabs.closeTabs(menu);
  //   //   navTabs.setActiveRoute(menu);
  //   //   if (navTabs.state.activeRoute?.path !== route.path) {
  //   //     router.push(menu!.path);
  //   //   }
  //   //   break;
  //   // case "closeAll":
  //   //   closeAllTab();
  //   //   break;
  //   case "fullScreen":
  //     await onTab(data);
  //     navTabs.setFullScreen(true);
  //     break;
  // }
};
</script>

<style scoped lang="scss">
.el-sub-menu .icon,
.el-menu-item .icon {
  vertical-align: middle;
  margin-right: 5px;
  width: 24px;
  text-align: center;
}
.is-active > .icon {
  color: var(--el-menu-active-color) !important;
}
.el-menu-item.is-active {
  background-color: v-bind('config.getColorVal("menuActiveBackground")');
}
</style>
