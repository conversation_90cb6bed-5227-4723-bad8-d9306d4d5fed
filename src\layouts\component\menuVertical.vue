<template>
  <el-scrollbar ref="verticalMenusRef" :height="verticalMenusScrollbarHeight" class="vertical-menus-scrollbar">
    <el-menu v-if="config.layout.layoutMode === appTheme.DESKTOP ? (navTabs.state.activeRoute || {}).type === appType.DIR : true" class="layouts-menu-vertical" :collapse-transition="false" :unique-opened="config.layout.menuUniqueOpened" :default-active="active" :collapse="config.layout.menuCollapse" @select="verticalMenusScroll">
      <MenuTree v-if="viewRoutes.length" :menus="viewRoutes" :menu-names="menuNames" />
    </el-menu>
    <div v-else class="tw-w-full">
      <el-link class="tw-mx-[12px] tw-my-[8px] tw-block first:tw-mt-[20px]" v-for="menu in navTabs.state.tabsViewRoutes.filter((v) => v.type === appType.DIR && v.name !== route.name)" :key="menu.name" :underline="false" :href="formatURL(menu)" target="_blank">
        <div class="nav-menu-item tw-flex tw-cursor-pointer tw-flex-col tw-items-center tw-justify-center">
          <el-avatar :size="50" shape="square">
            <Icon :color="'inherit'" size="28" :name="menu.icon ? menu.icon : config.layout.menuDefaultIcon" />
          </el-avatar>
          <div class="tw-text-center">{{ menu.title }}</div>
        </div>
      </el-link>
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from "vue";
import MenuTree from "./menuTree.vue";
import { useRoute, useRouter } from "vue-router";
import type { ElScrollbar } from "element-plus";
import { useConfig } from "@/stores/config";
import { useNavTabs } from "@/stores/navTabs";
import { appTheme, appType, type NavItem } from "@/api/application";

const config = useConfig();
const navTabs = useNavTabs();
const route = useRoute();
const router = useRouter();

const loading = ref(false);
const active = ref(route.name as string);

const verticalMenusRef = ref<InstanceType<typeof ElScrollbar>>();

const menuNames = computed(() => hasTreeName(navTabs.state.tabsViewRoutes, route.name as string));

const viewRoutes = computed<NavItem[]>(() => {
  // console.log(config.layout.layoutMode,config.layout.shrink)
  switch (config.layout.layoutMode) {
    case appTheme.BASE:
      return config.layout.shrink ? navTabs.state.tabsViewRoutes : navTabs.state.activeRoute?.children instanceof Array ? navTabs.state.activeRoute?.children : [];
    case appTheme.CLASSICS:
      return navTabs.state.tabsViewRoutes.filter((v) => v.type !== appType.ROUTE);
    case appTheme.FOCUS:
      return navTabs.state.tabsViewRoutes;
    case appTheme.SIMPLICITY:
      return navTabs.state.tabsViewRoutes;
    case appTheme.DESKTOP:
      return config.layout.shrink ? navTabs.state.tabsViewRoutes : (navTabs.state.activeRoute || {}).children instanceof Array ? (navTabs.state.activeRoute || {}).children || [] : [];
    default:
      return navTabs.state.tabsViewRoutes;
  }
});

const verticalMenusScrollbarHeight = computed(() => {
  let menuTopBarHeight = " - 54px";
  switch (config.layout.layoutMode) {
    case appTheme.BASE:
      menuTopBarHeight += " - 32px";
      break;
    case appTheme.CLASSICS:
      break;
    case appTheme.FOCUS:
      break;
    case appTheme.SIMPLICITY:
      break;
    case appTheme.DESKTOP:
      if ((navTabs.state.activeRoute || {}).type !== appType.DIR) menuTopBarHeight = " - 50vh";
      break;
    default:
      break;
  }
  if (config.layout.menuShowTopBar) menuTopBarHeight += " - 50px";
  return `calc(100vh${menuTopBarHeight})`;
});

// 滚动条滚动到激活菜单所在位置
const verticalMenusScroll = async () => {
  if (loading.value) return;
  loading.value = true;
  active.value = "";
  await nextTick();
  if (verticalMenusRef.value && verticalMenusRef.value.wrapRef) {
    const activeElement = verticalMenusRef.value.wrapRef.getElementsByClassName("is-active");
    let activeMenu: HTMLElement | null = (activeElement[activeElement.length - 1] as HTMLElement) || null;
    if (activeMenu) {
      await new Promise((resolve) => setTimeout(resolve, 500));
      verticalMenusRef.value.scrollTo({ top: activeMenu.offsetTop, left: 0, behavior: "smooth" });
    }
  }
  active.value = route.name as string;
  loading.value = false;
};

onMounted(() => {
  watch(
    () => route.name,
    () => verticalMenusScroll(),
    { immediate: true }
  );
});

function hasTreeName(treeList: NavItem[], name: string): string[] {
  if (treeList instanceof Array) {
    for (let index = 0; index < treeList.length; index++) {
      if (treeList[index].name === name) return [treeList[index].name];
      const isFind = hasTreeName(treeList[index].children, name);
      if (isFind.length) {
        return [treeList[index].name, ...getNames(treeList[index].children)];
      }
    }
  }
  return [];
}

function getNames(treeList: NavItem[]): string[] {
  const list: string[] = [];
  if (treeList instanceof Array) {
    for (let index = 0; index < treeList.length; index++) {
      list.push(treeList[index].name, ...getNames(treeList[index].children));
    }
  }
  return list;
}

function formatURL(menu: NavItem): string {
  if (menu.type === appType.DIR) {
    for (let index = 0; index < menu.children.length; index++) {
      const url = formatURL(menu.children[index]);
      if (url) return url;
    }
    return "";
  } else if ([appType.ROUTE, appType.MENU].includes(menu.type as appType)) {
    const { href } = router.resolve(menu.path);
    const $url = new URL(router.options.history.base, location.origin);
    if (/^\#/.test(href)) {
      $url.hash = href;
    } else if (/^(http:|https:|\/\/)/.test(href)) {
      return href;
    } else {
      $url.pathname = href;
    }
    return $url.toString();
  } else {
    return "";
  }
}
</script>

<style scoped lang="scss">
.vertical-menus-scrollbar {
  height: v-bind(verticalMenusScrollbarHeight);
  background-color: v-bind('config.getColorVal("menuBackground")');
}
.layouts-menu-vertical {
  border: 0;
  --el-menu-bg-color: v-bind('config.getColorVal("menuBackground")');
  --el-menu-text-color: v-bind('config.getColorVal("menuColor")');
  --el-menu-active-color: v-bind('config.getColorVal("menuActiveColor")');
}
.nav-menu-item {
  &:hover {
    color: var(--el-color-primary);
    :deep(.el-avatar) {
      color: var(--el-color-primary);
    }
  }
  :deep(.el-avatar) {
    background-image: linear-gradient(75deg, var(--el-color-primary-light-2), var(--el-color-primary-light-6));
  }
}
</style>
