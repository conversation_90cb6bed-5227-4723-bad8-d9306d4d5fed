<template>
  <div class="nav-bar">
    <div v-if="!config.layout.shrink" class="logo website-name" :style="{ color: config.getColorVal('headerBarTabColor') }">
      <svg width="229" height="40" viewBox="0 0 229 40" fill="none" xmlns="http://www.w3.org/2000/svg" @click="$router.push('/')">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M24.5288 6C24.9502 6 25.2918 6.34158 25.2918 6.76294V33.2371C25.2918 33.6584 24.9502 34 24.5288 34H23.537C23.1156 34 22.7741 33.6584 22.7741 33.2371V6.76294C22.7741 6.34158 23.1156 6 23.537 6H24.5288ZM29.9458 10.654L37.172 17.8802C38.3638 19.072 38.3638 21.0043 37.172 22.1961L26.5125 32.8556V30.207C26.5125 29.8023 26.6733 29.4142 26.9594 29.128L34.9703 21.1171C35.5384 20.549 35.5649 19.6444 35.0498 19.045L34.9703 18.9592L30.3927 14.3815C30.1065 14.0954 29.9458 13.7073 29.9458 13.3026V10.654ZM18.9972 16.3569C20.3738 16.3569 21.4924 17.4617 21.5146 18.833L21.5149 32.7348L19.4441 30.664C19.158 30.3779 18.9972 29.9897 18.9972 29.5851L18.9972 20.4005C18.9972 19.5578 18.314 18.8747 17.4713 18.8747H16.1743V17.8828C16.1743 17.0401 16.8575 16.3569 17.7002 16.3569H18.9972ZM21.5533 7.22071V9.86932C21.5533 10.274 21.3926 10.6621 21.1064 10.9483L13.0955 18.9592C12.5056 19.5491 12.4997 20.5019 13.0778 21.0991L17.3298 25.3514C17.616 25.6376 17.7768 26.0257 17.7768 26.4304V29.079L10.8938 22.1961C9.70205 21.0043 9.70205 19.072 10.8938 17.8802L21.5533 7.22071ZM26.5122 7.25252L28.5776 9.2976C28.8671 9.58418 29.0299 9.97458 29.0299 10.3819V25.1981C29.0299 25.6028 28.8692 25.9909 28.583 26.2771L26.5122 28.3479V7.25252Z" fill="currentColor" />
        <path
          d="M53.2933 25.5579C54.3348 25.5579 55.176 25.5179 55.8169 25.4378C56.6581 25.4378 57.4792 25.3176 58.2804 25.0773V19.4893H53.774V21.3519H55.937V23.6953C55.8169 23.7353 55.7768 23.7353 55.8169 23.6953C55.0157 23.8555 54.3147 23.9356 53.7139 23.9356C51.6709 23.8956 50.6295 22.5536 50.5894 19.9099C50.6295 17.226 51.6709 15.8641 53.7139 15.824C55.196 15.824 55.937 16.4449 55.937 17.6867H58.4606C58.5007 15.2833 56.7782 14.0815 53.2933 14.0815C49.8884 14.3219 48.1459 16.2647 48.0658 19.9099C48.1459 23.475 49.8884 25.3577 53.2933 25.5579ZM62.7868 25.4378V21.1717H64.5293C67.0129 21.0515 68.2947 19.9099 68.3748 17.7468C68.4149 15.4235 66.9127 14.2818 63.8684 14.3219H60.4435V25.4378H62.7868ZM63.5079 19.309H62.7868V16.1845H63.5079C65.1101 16.0243 65.8913 16.5451 65.8512 17.7468C65.8913 18.9485 65.1101 19.4692 63.5079 19.309ZM74.804 25.5579C77.8884 25.4778 79.4907 24.0558 79.6109 21.2918V14.3219H77.2074V20.3906C77.2475 21.7124 77.0873 22.6137 76.7267 23.0944C76.3662 23.6552 75.7253 23.9356 74.804 23.9356C73.8827 23.9356 73.2217 23.6552 72.8212 23.0944C72.4606 22.6137 72.3004 21.7124 72.3405 20.3906V14.3219H69.937V21.2918C70.0172 24.0558 71.6395 25.4778 74.804 25.5579ZM83.937 25.1373V17.3863H92.8297V17.8069H94.7525L95.113 15.824H88.3233V15.2232H95.113V13.6009H88.3233V13.3004H86.2203V15.824H81.9542V24.7167C81.9943 25.0372 81.834 25.1774 81.4735 25.1373V26.8197H82.2546C83.3762 26.8598 83.937 26.299 83.937 25.1373ZM86.5808 22.073H94.8727V20.5107H87.5422C87.3018 20.5107 87.1817 20.4106 87.1817 20.2103V19.7897H95.0529V18.1674H87.1817V17.8069H85.0787V18.1674H84.3576V19.7897H85.0787V20.6309C85.0386 21.6323 85.5393 22.113 86.5808 22.073ZM95.0529 26.8197V25.2575H92.0486V24.6567H93.3705C94.412 24.7368 94.8927 24.2961 94.8126 23.3348V22.4335H93.0701C93.03 22.794 92.8898 22.9742 92.6495 22.9742H91.9285V22.4335H89.9456V25.2575H89.2847V22.4936H87.2418V22.9742H86.8812C86.7611 22.9742 86.701 22.9142 86.701 22.794V22.4335H84.5379V23.6352C84.4978 24.3562 84.9184 24.6967 85.7997 24.6567H87.2418V25.2575H84.3576V26.8197H95.0529ZM109.474 26.8197H110.555V25.1974H110.375C110.255 25.2375 110.175 25.1373 110.134 24.897L109.834 23.9356H109.353L110.495 21.1116V13.3004H108.572V20.9914L106.89 24.897C106.81 25.0572 106.71 25.1373 106.589 25.1373H106.169V23.515H104.607V24.8369C104.647 25.0773 104.567 25.1774 104.366 25.1373H103.946V13.3605H102.083V14.2618H101.542V13.2403H99.5594V14.2618H97.0358V16.1845H99.5594V18.4077L97.6366 21.7725C97.5164 21.9728 97.3762 22.073 97.216 22.073H96.8555V23.8155H98.1173C98.638 23.8555 98.9585 23.7353 99.0787 23.4549L99.5594 22.6137V24.7768C99.5994 25.0572 99.4993 25.1774 99.2589 25.1373H97.4564V26.7597H100.16C100.721 26.7597 101.102 26.6595 101.302 26.4592C101.462 26.299 101.542 25.9185 101.542 25.3176V19.4893H102.083V26.7597H105.027C105.668 26.7597 106.029 26.5393 106.109 26.0987V26.8197H107.431C107.951 26.8598 108.272 26.6795 108.392 26.279L108.692 25.618L108.933 26.3991C109.013 26.7196 109.193 26.8598 109.474 26.8197ZM106.229 22.3734H107.491V20.6309H107.371C107.25 20.6309 107.19 20.5708 107.19 20.4506L106.649 13.3605H104.667L105.147 20.9313C105.187 22.0129 105.548 22.4936 106.229 22.3734ZM102.083 17.5665H101.542V16.1845H102.083V17.5665ZM115.963 26.7597V16.3047L116.984 13.3605H114.641L113.74 15.5837C113.579 16.0243 113.359 16.2046 113.079 16.1245H112.598V17.8069H113.8V26.7597H115.963ZM126.237 26.5794V24.897H120.589C120.149 24.9371 119.948 24.7568 119.989 24.3562V21.2918H123.654C124.775 21.412 125.396 20.7711 125.516 19.3691L126.418 13.6609H124.134L123.474 18.4077C123.433 18.8884 123.353 19.1888 123.233 19.309C123.113 19.3891 122.833 19.4292 122.392 19.4292H119.989V13.4206H117.825V25.0773C117.825 26.0787 118.346 26.5794 119.388 26.5794H126.237ZM129.843 15.8841V15.2232H139.336V15.824H141.139L141.439 13.721H135.851V13.0601H133.448V13.6009H127.8V15.8841H129.843ZM138.074 18.1674H141.439V16.485H139.216C138.855 16.485 138.635 16.4049 138.555 16.2446L138.255 15.8841H135.791L136.933 17.6867C137.093 18.0472 137.474 18.2074 138.074 18.1674ZM132.486 17.6266L133.388 15.8841H131.104L130.864 16.2446C130.744 16.4449 130.544 16.525 130.263 16.485H127.8V18.1073H131.405C132.006 18.1474 132.366 17.9871 132.486 17.6266ZM139.096 21.0515H141.439V19.4292H140.237C139.957 19.4692 139.737 19.3491 139.577 19.0687L139.096 18.4678H130.263L129.843 19.0086C129.642 19.2489 129.422 19.3491 129.182 19.309H127.74V20.9914H130.383C130.704 21.0315 130.964 20.9113 131.165 20.6309L131.585 20.1502H137.774L138.074 20.5708C138.275 20.9313 138.615 21.0916 139.096 21.0515ZM139.697 26.6395C140.338 26.6395 140.738 26.5193 140.898 26.279C141.099 26.0787 141.199 25.578 141.199 24.7768V21.5923H128.1V26.6395H139.697ZM138.735 25.0172H130.203V23.3948H139.156V24.5966C139.196 24.917 139.056 25.0572 138.735 25.0172ZM155.499 17.206C156.461 17.206 156.941 16.7654 156.941 15.8841V13.1803H150.332V17.206H155.499ZM148.409 17.2661C149.371 17.2661 149.851 16.8054 149.851 15.8841V13.1803H143.302V17.2661H148.409ZM154.598 15.6438H152.375V14.8026H154.838V15.3433C154.878 15.5436 154.798 15.6438 154.598 15.6438ZM147.568 15.6438H145.285V14.8026H147.748V15.3433C147.788 15.5837 147.728 15.6838 147.568 15.6438ZM152.495 22.3133H156.941V20.5708H153.456C153.176 20.5708 152.976 20.5107 152.855 20.3906L152.074 19.7296H155.86C156.581 19.6896 156.961 19.4292 157.001 18.9485V17.5665H155.319V17.8069C155.319 17.927 155.219 17.9871 155.019 17.9871H151.474V17.5665H148.77V17.9871H143.242V19.7296H148.409L147.508 20.5107C147.388 20.5908 147.167 20.6309 146.847 20.6309H143.182V22.3133H148.049C148.649 22.3133 149.05 22.1931 149.25 21.9528L150.332 20.8712L151.714 22.133C151.874 22.2933 152.134 22.3534 152.495 22.3133ZM155.499 26.8798C156.461 26.8798 156.941 26.3991 156.941 25.4378V22.6738H150.332V26.8798H155.499ZM148.349 26.8197C149.31 26.8197 149.791 26.3591 149.791 25.4378V22.6738H143.122V26.8197H148.349ZM154.598 25.2575H152.375V24.3562H154.838V24.9571C154.878 25.1574 154.798 25.2575 154.598 25.2575ZM147.448 25.1974H145.165V24.3562H147.688V24.897C147.728 25.1373 147.648 25.2375 147.448 25.1974ZM172.323 16.5451V15.0429H171.783C171.622 15.0429 171.502 14.9428 171.422 14.7425L171.302 14.5021H172.263V13.0601H166.134L165.954 14.4421C165.834 14.8026 165.614 15.0029 165.293 15.0429H164.632C164.392 15.0429 164.212 14.9428 164.092 14.7425L164.031 14.5021H165.413V13H159.525L159.225 14.6223C159.185 14.8627 159.064 14.9828 158.864 14.9828H158.564V16.485H159.705C160.506 16.485 161.007 16.2046 161.207 15.6438L161.508 14.5021H161.748L161.928 15.0429C162.209 15.9642 162.73 16.4249 163.491 16.4249H166.615C167.376 16.4249 167.837 16.1044 167.997 15.4635L168.237 14.5021H168.718L169.199 15.7639C169.399 16.2446 169.82 16.505 170.461 16.5451H172.323ZM172.083 19.1288L172.263 17.1459H166.675V16.7854H164.031V17.1459H158.564V19.0687H160.907V18.5279H169.98L169.92 19.1288H172.083ZM170.34 26.9399C171.182 26.8999 171.642 26.3791 171.722 25.3777V23.515H161.448V22.9742H170.401C171.242 22.9342 171.682 22.4535 171.722 21.5322V19.4893H159.104V26.9399H170.34ZM169.259 21.5923H161.448V20.8712H169.499V21.1717C169.499 21.412 169.419 21.5522 169.259 21.5923ZM169.199 25.4979H161.448V24.8369H169.439V25.1373C169.439 25.3777 169.359 25.4979 169.199 25.4979ZM178.572 26.4592V24.897H177.25V20.7511H178.572V19.0086H177.25V14.8026H178.572V13.0601H173.886V14.8026H175.388V19.0086H173.825V20.691H175.388V24.8369H173.765V26.4592H178.572ZM187.765 26.8197V25.2575H184.701V24.0558H187.765V22.3734H184.701V21.2318H186.263C187.225 21.2718 187.685 20.8112 187.645 19.8498V13.1202H179.233V21.1717H182.358V22.3734H179.053V24.0558H182.358V25.2575H178.813V26.8197H187.765ZM185.783 16.3047H184.701V14.9828H185.783V16.3047ZM182.358 16.3047H181.096V14.9828H182.358V16.3047ZM182.358 19.5494H181.096V18.1674H182.358V19.5494ZM185.602 19.5494H184.701V18.1674H185.783V19.3691C185.783 19.4893 185.722 19.5494 185.602 19.5494ZM197.74 26.6996V25.0773H203.328V23.2146H197.74V15.0429H203.207V13.1803H189.748V15.0429H195.276V23.2146H189.628V25.0773H195.276V26.6996H197.74ZM202.186 21.2318L202.967 15.824H200.744L199.903 20.2704C199.903 20.5107 199.763 20.6309 199.482 20.6309H198.28V22.4936H200.864C201.665 22.5336 202.106 22.113 202.186 21.2318ZM192.152 22.4936H194.795V20.691H193.474C193.273 20.691 193.153 20.5708 193.113 20.3305L192.332 15.824H189.928L190.83 21.412C190.91 22.1731 191.35 22.5336 192.152 22.4936ZM216.727 18.8884C217.408 18.8884 217.828 18.7883 217.989 18.588C218.189 18.3877 218.289 17.9471 218.289 17.2661V14.8627H216.186V16.5451C216.226 16.9056 216.106 17.0658 215.825 17.0258H207.474L210.177 13.2403H207.594L204.89 16.9056V18.8884H216.727ZM216.186 27C216.987 27 217.528 26.8798 217.808 26.6395C218.089 26.3591 218.229 25.8584 218.229 25.1373V19.7897H204.95V27H216.186ZM215.585 25.1974H207.113V21.6524H216.126V24.5966C216.166 25.0372 215.986 25.2375 215.585 25.1974Z"
          fill="currentColor"
        />
      </svg>
    </div>
    <div class="nav-menus tw-w-[50px] tw-flex-shrink-0 tw-text-center">
      <el-popover v-model:visible="visible" placement="bottom-end" width="878px" trigger="click">
        <template #reference>
          <div class="nav-menu-item">
            <Icon :color="config.getColorVal('headerBarTabColor')" class="nav-menu-icon" name="local-SystemMenu-fill" size="18" />
          </div>
        </template>
        <template #default>
          <el-scrollbar max-height="calc(100vh - 120px)">
            <MenuFlow @done="() => nextTick(() => (visible = false))"></MenuFlow>
          </el-scrollbar>
        </template>
      </el-popover>
    </div>
    <NavTabs />
    <NavMenus />
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from "vue";
import { useConfig } from "@/stores/config";
// import { useSiteConfig } from "@/stores/siteConfig";
// import { useNavTabs } from "@/stores/navTabs";
import NavTabs from "@/layouts/component/navBar/tabs.vue";
import NavMenus from "../navMenus.vue";
// import { showShade } from "@/utils/pageShade";
import MenuFlow from "@/layouts/common/menuFlow/index.vue";

const config = useConfig();

const visible = ref(false);
// const siteConfig = useSiteConfig();
// const navTabs = useNavTabs();

// const onMenuCollapse = () => {
//   showShade("ba-aside-menu-shade", () => config.setLayout("menuCollapse", true));
//   config.setLayout("menuCollapse", false);
// };
</script>

<style scoped lang="scss">
.nav-menus {
  display: flex;
  align-items: center;
  height: 100%;
  flex-shrink: 0;
  margin-right: auto;
  background-color: v-bind('config.getColorVal("headerBarBackground")');
  overflow: hidden;
  .nav-menu-item {
    height: 100%;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    .nav-menu-icon {
      box-sizing: content-box;
      color: v-bind('config.getColorVal("headerBarTabColor")');
    }
    &:hover {
      background: v-bind('config.getColorVal("headerBarHoverBackground")');
      .icon {
        animation: twinkle 0.3s ease-in-out;
      }
    }
  }
}

.nav-bar {
  display: flex;
  height: 54px;
  width: 100vw;
  background-color: v-bind('config.getColorVal("headerBarBackground")');
  // background-image: linear-gradient(135deg, #6379c3 0%, #546ee5 60%);
  // :deep(.nav-tabs) {
  //   display: flex;
  //   height: 100%;
  //   position: relative;
  //   .ba-nav-tab {
  //     display: flex;
  //     align-items: center;
  //     justify-content: center;
  //     padding: 0 20px;
  //     cursor: pointer;
  //     z-index: 1;
  //     height: 100%;
  //     user-select: none;
  //     color: v-bind('config.getColorVal("headerBarTabColor")');
  //     transition: all 0.2s;
  //     -webkit-transition: all 0.2s;
  //     .close-icon {
  //       padding: 2px;
  //       margin: 2px 0 0 4px;
  //     }
  //     .close-icon:hover {
  //       background: var(--ba-color-primary-light);
  //       color: var(--el-border-color) !important;
  //       border-radius: 50%;
  //     }
  //     &.active {
  //       color: v-bind('config.getColorVal("headerBarTabActiveColor")');
  //     }
  //     &:hover {
  //       background-color: v-bind('config.getColorVal("headerBarHoverBackground")');
  //     }
  //   }
  //   .nav-tabs-active-box {
  //     position: absolute;
  //     top: 50%;
  //     height: 40px;
  //     border-radius: var(--el-border-radius-base);
  //     box-shadow: var(--el-box-shadow-light);
  //     transition: all 0.2s;
  //     -webkit-transition: all 0.2s;
  //   }
  // }
}
.unfold {
  flex-shrink: 0;
  align-self: center;
  padding-left: var(--ba-main-space);
  padding-right: var(--ba-main-space);
}
.logo {
  flex-shrink: 0;
  width: 178px;
  display: flex;
  margin: 0 16px;
  justify-content: flex-start;
  align-items: center;
}
.logo-img {
  width: 177px;
  margin-left: 30px;
}
@at-root .dark {
  .nav-bar {
    background-image: none;
  }
}
@keyframes twinkle {
  0% {
    transform: scale(0);
  }
  80% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
</style>
