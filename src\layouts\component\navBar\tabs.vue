<template>
  <el-menu class="nav-tabs" :default-active="effectsActive" :style="menuStyle" mode="horizontal" @select="($event: string) => onTab(navTabs.state.tabsViewRoutes.find((item) => item.name === $event) as NavItem, $event)">
    <template v-for="item in navTabs.state.tabsViewRoutes">
      <template v-if="item.type === appType.BUTTON"></template>
      <template v-else-if="item.type === appType.DIR">
        <el-sub-menu :key="`${item.type}${item.name}`" :index="item.name">
          <template #title>
            <Icon color="" :name="item.icon ? item.icon : config.layout.menuDefaultIcon" />
            <span style="margin-left: 6px; font-weight: normal">{{ item.title ? item.title : $t("pagesTitle.noTitle") }}</span>
          </template>
          <template #default>
            <el-menu-item index="2-4-1">item one</el-menu-item>
            <el-menu-item index="2-4-2">item two</el-menu-item>
            <el-menu-item index="2-4-3">item three</el-menu-item>
          </template>
        </el-sub-menu>
      </template>
      <template v-else>
        <el-menu-item :key="`${item.type}${item.name}`" :index="item.name" @contextmenu.prevent="($event: MouseEvent) => onContextmenu(item, $event)">
          <template #default>
            <Icon color="" :name="item.icon ? item.icon : config.layout.menuDefaultIcon" />
          </template>
          <template #title>
            <span style="margin-left: 6px; font-weight: normal">{{ item.title ? item.title : $t("pagesTitle.noTitle") }}</span>
          </template>
        </el-menu-item>
      </template>
    </template>
  </el-menu>
  <Contextmenu ref="contextmenuRef" :items="state.contextmenuItems" @contextmenuItemClick="onContextmenuItem" />
</template>

<script setup lang="ts">
import { nextTick, reactive, computed, ref } from "vue";
import { ElMenuItem } from "element-plus";
import { useConfig } from "@/stores/config";
import { useNavTabs } from "@/stores/navTabs";
// import { useTemplateRefsList } from "@vueuse/core";
import type { ContextMenuItem, ContextmenuItemClickEmitArg } from "@/components/contextmenu/interface";
import useCurrentInstance from "@/utils/useCurrentInstance";
import Contextmenu from "@/components/contextmenu/index.vue";
import { NavItem } from "@/api/application";
import { appType, getFirstRoute, routePush } from "@/utils/router";

// const route = useRoute();
// const router = useRouter();
const config = useConfig();
const navTabs = useNavTabs();

const { proxy } = useCurrentInstance();
// const tabScrollbarRef = ref<ScrollbarInstance>();
// const tabsRefs = useTemplateRefsList<InstanceType<typeof ElMenuItem>>();

const contextmenuRef = ref();

const effectsActive = ref<string>("");

const state: {
  contextmenuItems: ContextMenuItem[];
} = reactive({
  contextmenuItems: [
    { name: "refresh", label: "重新加载", icon: "fa fa-refresh" },
    // { name: "close", label: "关闭标签", icon: "fa fa-times" },
    { name: "fullScreen", label: "当前标签全屏", icon: "el-icon-FullScreen" },
    // { name: "closeOther", label: "关闭其他标签", icon: "fa fa-minus" },
    // { name: "closeAll", label: "关闭全部标签", icon: "fa fa-stop" },
  ],
});
const menuStyle = computed(() => ({
  "--el-menu-bg-color": config.getColorVal("headerBarBackground"),
  "--el-menu-text-color": config.getColorVal("headerBarTabColor"),
  "--el-bg-color-overlay": config.getColorVal("headerBarHoverBackground"),
  "--el-menu-hover-bg-color": config.getColorVal("headerBarHoverBackground"),
  "--el-menu-hover-text-color": config.getColorVal("headerBarTabColor"),
  "--el-menu-active-color": config.getColorVal("headerBarTabActiveColor"),
}));

// const activeBoxStyle = reactive({
//   width: "0",
//   transform: "translateX(0px)",
// });

const onTab = async (menu?: NavItem, active = "") => {
  effectsActive.value = active;
  try {
    await nextTick();
    if (!menu) return;
    let firstRoute = getFirstRoute([menu]);
    if (firstRoute) {
      await routePush(firstRoute);
    }
  } catch (error) {
    /*  */
  } finally {
    await new Promise((resolve) => setTimeout(resolve, 300));
  }
  await nextTick();
};

const onContextmenu = (menu: NavItem, el: MouseEvent) => {
  // 禁用刷新
  state.contextmenuItems[0].disabled = !(menu.names instanceof Array ? menu.names : [menu.name]).includes(navTabs.state.activeName);
  const { clientX, clientY } = el;
  contextmenuRef.value.onShowContextmenu(menu, { x: clientX, y: clientY });
};
const onContextmenuItem = async ({ name, data }: ContextmenuItemClickEmitArg) => {
  if (!data) return;
  switch (name) {
    case "refresh":
      proxy.eventBus.emit("onTabViewRefresh", data);
      break;
    // case "close":
    //   closeTab(menu);
    //   break;
    // case "closeOther":
    //   navTabs.closeTabs(menu);
    //   navTabs.setActiveRoute(menu);
    //   if (navTabs.state.activeRoute?.path !== route.path) {
    //     router.push(menu!.path);
    //   }
    //   break;
    // case "closeAll":
    //   closeAllTab();
    //   break;
    case "fullScreen":
      await onTab(data);
      navTabs.setFullScreen(true);
      break;
  }
};
</script>

<style scoped lang="scss">
.dark {
  .close-icon {
    color: v-bind('config.getColorVal("headerBarTabColor")') !important;
  }
  .ba-nav-tab.active {
    .close-icon {
      color: v-bind('config.getColorVal("headerBarTabActiveColor")') !important;
    }
  }
}
.nav-tabs {
  width: 100%;
  overflow: hidden;
  margin-right: 16px;
}
.ba-nav-tab {
  white-space: nowrap;
  height: 40px;
}
</style>
