<template>
  <div class="nav-menus" :class="configStore.layout.layoutMode">
    <!-- 首页 -->
    <router-link v-if="false" class="h100" :title="t('glob.home')" to="/">
      <div class="nav-menu-item">
        <Icon :color="configStore.getColorVal('headerBarTabColor')" class="nav-menu-icon" name="el-icon-Monitor" size="18" />
      </div>
    </router-link>
    <!-- 主题 -->
    <!-- <div class="nav-menu-item" :title="t('glob.Model')" @click="configStore.setLayout('isDark', !configStore.layout.isDark)">
      <Icon :color="configStore.getColorVal('headerBarTabColor')" class="nav-menu-icon" :name="configStore.layout.isDark ? 'local-light' : 'local-dark'" size="18" />
    </div> -->
    <!-- 语言 -->
    <el-dropdown class="h100" size="large" :hide-timeout="50" placement="bottom" trigger="click" :hide-on-click="true" @visible-change="onCurrentNavMenu($event, 'lang')">
      <div class="nav-menu-item pt2" :class="state.currentNavMenu == 'lang' ? 'hover' : ''">
        <Icon :color="configStore.getColorVal('headerBarTabColor')" class="nav-menu-icon" name="local-lang" size="18" />
      </div>
      <template #dropdown>
        <el-dropdown-menu class="dropdown-menu-box">
          <el-dropdown-item v-for="item in configStore.lang.langArray" :key="item.name" @click="editDefaultLang(item.name)">
            {{ item.value }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <!-- 全屏 -->
    <div class="nav-menu-item" :class="state.isFullScreen ? 'hover' : ''" @click="onFullScreen">
      <Icon v-if="state.isFullScreen" :color="configStore.getColorVal('headerBarTabColor')" class="nav-menu-icon" name="local-full-screen-cancel" size="18" />
      <Icon v-else :color="configStore.getColorVal('headerBarTabColor')" class="nav-menu-icon" name="el-icon-FullScreen" size="18" />
    </div>
    <!-- 租户 -->
    <el-dropdown v-if="siteConfig.multiTenant && !['520026380117737472', '519662651731607552'].includes((navTabs.state.tabsViewRoutes.find((v) => v.name === (route.name as string)) || { type: appType.MENU, id: '' }).id)" class="h100" size="large" :hide-timeout="50" placement="bottom" trigger="click" :hide-on-click="true" @visible-change="onCurrentNavMenu($event, 'tenant'), $event && $refs['tenantSearch'] && ($refs['tenantSearch'] as HTMLInputElement).focus()">
      <div class="nav-menu-item tw-w-fit tw-px-2" :class="state.currentNavMenu == 'tenant' ? 'hover' : ''">
        <Icon :color="configStore.getColorVal('headerBarTabColor')" class="nav-menu-icon" name="local-UserGroup-fill" size="18" />
        <span class="tw-ml-2" :style="{ color: configStore.getColorVal('headerBarTabColor') }"> {{ first(activeTenant.map((v) => v.name)) }} [{{ first(activeTenant.map((v) => v.abbreviation)) }}]</span>
      </div>
      <template #dropdown>
        <el-input ref="tenantSearch" v-model="state.search.tenant" class="tw-mx-[8px] tw-mt-[8px] tw-w-[224px]" placeholder="请输入租户名称" clearable></el-input>
        <el-scrollbar style="height: 400px">
          <el-dropdown-menu class="dropdown-menu-box tw-w-[240px]">
            <el-dropdown-item v-for="tenant in userInfo.tenants.filter((v) => (state.search.tenant ? (v.name || '').toLocaleLowerCase().indexOf(state.search.tenant.toLocaleLowerCase()) !== -1 || (v.abbreviation || '').toLocaleLowerCase().indexOf(state.search.tenant.toLocaleLowerCase()) !== -1 : true))" :key="tenant.id" :disabled="!!tenant.blocked" :style="{ color: first(activeTenant.map((v) => v.id)) === tenant.id ? 'var(--el-color-primary)' : !tenant.blocked ? 'var(--el-text-color-primary)' : 'var(--el-text-color-disabled)', justifyContent: 'flex-start', width: '100%', overflow: 'hidden', wordBreak: 'break-all' }" :title="`${tenant.name} [${tenant.abbreviation}]`" @click="handleTenantChange(tenant)">{{ tenant.name }} [{{ tenant.abbreviation }}]</el-dropdown-item>
          </el-dropdown-menu>
        </el-scrollbar>

        <el-empty class="tw-w-[240px]" description="没有找到符合要求的租户" :image-size="64" v-show="!userInfo.tenants.filter((v) => (state.search.tenant ? (v.name || '').indexOf(state.search.tenant) !== -1 || (v.abbreviation || '').indexOf(state.search.tenant) !== -1 : true)).length"></el-empty>
      </template>
    </el-dropdown>
    <!-- 缓存 -->
    <el-dropdown v-if="false" class="h100" size="large" :hide-timeout="50" placement="bottom" trigger="click" :hide-on-click="true" @visible-change="onCurrentNavMenu($event, 'clear')">
      <div class="nav-menu-item" :class="state.currentNavMenu == 'clear' ? 'hover' : ''">
        <Icon :color="configStore.getColorVal('headerBarTabColor')" class="nav-menu-icon" name="el-icon-Delete" size="18" />
      </div>
      <template #dropdown>
        <el-dropdown-menu class="dropdown-menu-box">
          <el-dropdown-item @click="onClearCache('tp')">{{ t("utils.Clean up system cache") }}</el-dropdown-item>
          <el-dropdown-item @click="onClearCache('storage')">{{ t("utils.Clean up browser cache") }}</el-dropdown-item>
          <el-dropdown-item divided @click="onClearCache('all')">{{ t("utils.Clean up all cache") }}</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <!-- 用户资料 -->
    <el-dropdown
      class="h100"
      size="large"
      :hide-timeout="50"
      placement="bottom"
      trigger="click"
      :hide-on-click="true"
      @command="
        ({ command }: Partial<Record<'command', () => void>>) =>
          typeof command === 'function' &&
          ((loading = true),
          nextTick()
            .then(() => command())
            .finally(() => (loading = false)))
      "
      @visible-change="onCurrentNavMenu($event, 'userinfo')"
    >
      <template #default>
        <div class="super-info header-user-image" :class="state.currentNavMenu == 'userinfo' ? 'hover' : ''">
          <el-avatar :size="36" fit="fill">{{ userInfo.username.slice(-2) }}</el-avatar>
        </div>
      </template>
      <template #dropdown>
        <el-dropdown-menu class="dropdown-menu-box">
          <li class="tw-flex tw-min-w-[260px] tw-flex-row tw-items-center tw-justify-center tw-px-[6px]">
            <el-avatar class="tw-flex-shrink-0" :size="60" fit="fill">{{ userInfo.username.slice(-2) }}</el-avatar>
            <div class="tw-ml-[12px] tw-flex tw-h-full tw-w-[176px] tw-flex-col tw-items-start tw-justify-center">
              <div class="tw-mb-[6px] tw-text-base tw-font-semibold">{{ userInfo.username }}</div>
              <div class="tw-my-[6px] tw-flex tw-items-center">
                <!-- <div class="state-checkbox-wrapper" >
                  <input class="tgl tgl-flip" id="user_state_checkbox" type="checkbox" :checked="userInfo.status === 'BUSY'" @change="() => userInfo.cutUserBusy()" />
                  <label class="tgl-btn" data-tg-off="正常" data-tg-on="忙碌" for="user_state_checkbox"></label>
                </div> -->
                <div class="state-checkbox-wrapper" :style="{ '--data-off-color': '#252B3B', '--data-on-color': '#F87A00' }">
                  <input
                    class="switch"
                    type="checkbox"
                    id="state-checkbox-wrapper"
                    :checked="userInfo.status === 'BUSY'"
                    :disabled="loading"
                    @change="
                      () => (
                        (loading = true),
                        nextTick()
                          .then(() => userInfo.cutUserBusy())
                          .finally(() => (loading = false))
                      )
                    "
                  />
                  <label for="state-checkbox-wrapper">
                    <span class="switch-x-text">当前状态：</span>
                    <span class="switch-x-toggletext">
                      <span class="switch-x-unchecked">
                        <span class="switch-x-hiddenlabel"></span>
                        正常
                      </span>
                      <span class="switch-x-checked">
                        <span class="switch-x-hiddenlabel"></span>
                        忙碌
                      </span>
                    </span>
                  </label>
                </div>
                <!-- <el-tag v-if="userInfo.status === 'DEFAULT'" size="small" type="">{{ t("glob.Normal") }}</el-tag>
                <el-tag v-else-if="userInfo.status === 'BUSY'" size="small" type="warning">{{ t("glob.Busy") }}</el-tag>
                <el-tag v-else-if="userInfo.status === 'FROZEN'" size="small" type="danger">{{ t("glob.Frozen") }}</el-tag> -->
              </div>
              <!-- <div class="tw-text-xs" :style="{ color: 'var(--el-text-color-placeholder)' }">账号ID: {{ userInfo.userId }}</div> -->
            </div>
          </li>
          <!-- <el-dropdown-item divided :command="{ command: onsuperInfo }">{{ t("layouts.personal data") }}</el-dropdown-item> -->
          <el-dropdown-item divided :command="{ command: onsuperInfo }">{{ t("layouts.personal data") }}</el-dropdown-item>

          <el-dropdown-item :disabled="loading" :command="{ command: userInfo.cutUserBusy }">{{ userInfo.status === "BUSY" ? t("layouts.Switch to normal status") : t("layouts.Switch to busy status") }}</el-dropdown-item>
          <el-dropdown-item @click="handleCreateOpinion"> {{ t("layouts.opinion feedback") }} </el-dropdown-item>
          <el-dropdown-item divided :command="{ command: onLogout }">
            <el-link type="danger" :underline="false">{{ t("layouts.cancellation") }}</el-link>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <!-- 设置 -->
    <!-- <div v-if="!configStore.layout.shrink" class="nav-menu-item" @click="configStore.setLayout('showDrawer', true)">
      <Icon :color="configStore.getColorVal('headerBarTabColor')" class="nav-menu-icon" name="local-cogs" size="18" />
    </div>
    <Config /> -->
    <opinion ref="opinionCreateRef" title="意见反馈" />
  </div>
</template>

<script lang="ts" setup>
import { readonly, reactive, ref, nextTick, computed, h, createVNode, renderSlot, watch, inject, getCurrentInstance } from "vue";

// import { reactive, nextTick, computed } from "vue";
import { editDefaultLang } from "@/lang";
import { first } from "lodash-es";
import screenfull from "screenfull";
import { ElMessage } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import Config from "./config.vue";
import { useSiteConfig } from "@/stores/siteConfig";
import { useNavTabs } from "@/stores/navTabs";
import { useConfig } from "@/stores/config";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";
import { routePush } from "@/utils/router";
import { appType, logout } from "@/api/system";
import opinion from "../dialog.vue";
import { opinionCreate } from "@/views/pages/apis/opinion";
import { faStackOverflow } from "@fortawesome/free-brands-svg-icons";

const { proxy } = getCurrentInstance()!;
const { t } = useI18n();

const route = useRoute();
const router = useRouter();
const loading = ref(false);

const superInfo = useSuperInfo();
const adminInfo = useAdminInfo();
const usersInfo = useUsersInfo();

const navTabs = useNavTabs();
const siteConfig = useSiteConfig();
const configStore = useConfig();
// const { proxy } = useCurrentInstance();

const userInfo = computed(() => {
  switch (siteConfig.current) {
    case superBaseRoute.name:
      return superInfo;
    case adminBaseRoute.name:
      return adminInfo;
    case usersBaseRoute.name:
      return usersInfo;
    default:
      return usersInfo;
  }
});

const activeTenant = computed(() => userInfo.value.tenants.filter((v) => v.id === ("tenant" in route.query ? (route.query.tenant as string) : userInfo.value.currentTenantId)));

const state = reactive({
  search: <Record<string, string>>{},
  isFullScreen: false,
  currentNavMenu: "",
  showLayoutDrawer: false,
});

const handleTenantChange = (tenant) => {
  /* 如果当前用户页面是route,重定向到第一个路由 */
  const isRoute = ["ROUTE"].includes(route.meta.type as string);
  userInfo.value.cutTenant(tenant.id).finally(async () => {
    try {
      sessionStorage.setItem("currentTenantId", tenant.id);

      await router.replace("tenant" in route.query ? { name: `${siteConfig.current}Loading`, query: { ...route.query, tenant: tenant.id }, params: isRoute ? {} : { path: route.path.split("/").filter((v) => `/${v}` !== siteConfig.baseInfo?.path) } } : { name: `${siteConfig.current}Loading`, query: { ...route.query }, params: isRoute ? {} : { path: route.path.split("/").filter((v) => `/${v}` !== siteConfig.baseInfo?.path) } });
    } catch (error) {
      /* */
    } finally {
      /* */
    }
  });
};

const onCurrentNavMenu = (status: boolean, name: string) => {
  if (status && name === state.currentNavMenu) state.currentNavMenu = "";
  else state.currentNavMenu = status ? name : "";
};

const onFullScreen = () => {
  if (!screenfull.isEnabled) {
    ElMessage.warning(t("layouts.Full screen is not supported"));
    return false;
  }
  screenfull.toggle();
  screenfull.onchange(() => {
    state.isFullScreen = screenfull.isFullscreen;
  });
};

const onsuperInfo = () => {
  state.currentNavMenu = "";
  nextTick(async () => {
    await routePush({ name: "userinfo/data" });
  });
};

const onLogout = () => {
  state.currentNavMenu = "";
  nextTick(async () => {
    try {
      const res = await logout({});
      if (res.success) {
        userInfo.value.handleLogout();
      } else throw Object.assign(new Error(res.message), res);

      sessionStorage.removeItem("currentTenantId");
    } catch (error) {
      userInfo.value.handleLogout();
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      await nextTick();
      await router.push({ name: `${siteConfig.baseInfo?.name}Loading` });
    }
  });
};

const onClearCache = (type: string) => {
  if (type == "storage" || type == "all") {
    configStore.$reset();
    // Session.clear();
    // Local.clear();
    if (type == "storage") return;
  }
};
const opinionCreateRef = ref<InstanceType<typeof opinion>>();
async function handleCreateOpinion() {
  if (!opinionCreateRef.value) return false;
  const params = {};
  await opinionCreateRef.value.open(params, async (returnForm: Record<string, unknown>) => {
    // alert(JSON.stringify(returnForm));
    const { success, data, message } = await opinionCreate(returnForm);
    if (!success) throw new Error(message);
    if (success) {
      ElMessage.success("操作成功");
    } else {
      ElMessage.error(message);
    }
    //  if (e instanceof Error) ElMessage.error(e.message);
    // form.value.id = data.id;
    // handleFinish();
  });
  proxy?.eventBus && proxy.eventBus.emit("opinionCreate");
}
</script>

<style scoped lang="scss">
// .nav-menus.Classic {
//   flex-shrink: 0;
//   border-radius: var(--el-border-radius-base);
//   box-shadow: var(--el-box-shadow-light);
// }
.header-user-image :deep(.el-avatar--circle) {
  background: none;
  --el-avatar-bg-color: none;
  border: 2px solid #fff;
}
.nav-menus {
  display: flex;
  align-items: center;
  height: 100%;
  flex-shrink: 0;
  margin-right: 0;
  margin-left: auto;
  background-color: v-bind('configStore.getColorVal("headerBarBackground")');
  overflow: hidden;
  .nav-menu-item {
    height: 100%;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    .nav-menu-icon {
      box-sizing: content-box;
      color: v-bind('configStore.getColorVal("headerBarTabColor")');
    }
    &:hover {
      .icon {
        animation: twinkle 0.3s ease-in-out;
      }
    }
  }

  .super-info {
    display: flex;
    height: 100%;
    padding: 0 10px;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    user-select: none;
    color: v-bind('configStore.getColorVal("headerBarTabColor")');
  }
  .super-name {
    padding-left: 6px;
    white-space: nowrap;
  }
  .nav-menu-item:hover,
  .super-info:hover,
  .nav-menu-item.hover,
  .super-info.hover {
    background: v-bind('configStore.getColorVal("headerBarHoverBackground")');
  }
}
.dropdown-menu-box :deep(.el-dropdown-menu__item) {
  justify-content: center;
  white-space: wrap !important;
}
.super-info-base {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  padding-top: 10px;
  .super-info-other {
    display: block;
    width: 100%;
    text-align: center;
    padding: 10px 0;
    .super-info-name {
      font-size: var(--el-font-size-large);
    }
  }
}
.super-info-footer {
  padding: 10px 0;
  margin: 0 -12px -12px -12px;
  display: flex;
  justify-content: space-around;
}
.pt2 {
  padding-top: 2px;
}

@keyframes twinkle {
  0% {
    transform: scale(0);
  }
  80% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.state-checkbox-wrapper {
  .tgl {
    display: none;
    + .tgl-btn {
      outline: 0;
      display: block;
      width: 4em;
      height: 2em;
      position: relative;
      cursor: pointer;
      user-select: none;
      &:after,
      &:before {
        position: relative;
        display: block;
        content: "";
        width: 50%;
        height: 100%;
      }
      &:after {
        left: 0;
      }
      &:before {
        display: none;
      }
    }
    &:checked {
      + .tgl-btn {
        &:after {
          left: 50%;
        }
      }
    }
  }
  .tgl,
  .tgl:after,
  .tgl:before,
  .tgl *,
  .tgl *:after,
  .tgl *:before,
  .tgl + .tgl-btn {
    box-sizing: border-box;
    &::selection {
      background: none;
    }
  }
  /* =========== */
  .tgl-flip {
    + .tgl-btn {
      padding: 2px;
      transition: all 0.2s ease;
      font-family: sans-serif;
      perspective: 100px;
      &:after,
      &:before {
        display: inline-block;
        transition: all 0.4s ease;
        width: 100%;
        text-align: center;
        position: absolute;
        line-height: 2em;
        font-weight: bold;
        color: #fff;
        position: absolute;
        top: 0;
        left: 0;
        backface-visibility: hidden;
        border-radius: 4px;
      }
      &::after {
        content: attr(data-tg-on);
        background: var(--data-on-color);
        transform: rotateY(-180deg);
      }
      &::before {
        content: attr(data-tg-off);
        background: var(--data-off-color);
      }
      &:active {
        &::before {
          transform: rotateY(-20deg);
        }
      }
    }
    &:checked {
      + .tgl-btn {
        &::before {
          transform: rotateY(180deg);
        }
        &::after {
          transform: rotateY(0);
          left: 0;
        }
        &:active {
          &::after {
            transform: rotateY(20deg);
          }
        }
      }
    }
  }
}

.state-checkbox-wrapper {
  .switch {
    display: none;
    + label {
      align-items: center;
      color: #78768d;
      cursor: pointer;
      display: flex;
      font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
      font-size: 12px;
      line-height: 15px;
      position: relative;
      user-select: none;
      &::before,
      &::after {
        content: "";
        display: block;
      }
      &::before {
        background-color: var(--data-off-color);
        border-radius: 500px;
        height: 15px;
        margin-right: 8px;
        transition: background-color 0.125s ease-out;
        width: 30px;
      }
      &::after {
        background-color: #fff;
        border-radius: 13px;
        box-shadow:
          0 3px 1px 0 rgba(37, 34, 71, 0.05),
          0 2px 2px 0 rgba(37, 34, 71, 0.1),
          0 3px 3px 0 rgba(37, 34, 71, 0.05);
        height: 13px;
        left: 1px;
        position: absolute;
        top: 1px;
        transition: transform 0.125s ease-out;
        width: 13px;
      }

      .switch-x-text {
        display: block;
        margin-right: 0.3em;
      }
      .switch-x-toggletext {
        display: block;
        font-weight: bold;
        height: 15px;
        overflow: hidden;
        position: relative;
        width: 25px;
      }
      .switch-x-unchecked,
      .switch-x-checked {
        left: 0;
        position: absolute;
        top: 0;
        transition:
          transform 0.125s ease-out,
          opacity 0.125s ease-out;
      }
      .switch-x-unchecked {
        color: var(--data-off-color);
        opacity: 1;
        transform: none;
      }
      .switch-x-checked {
        color: var(--data-on-color);
        opacity: 0;
        transform: translate3d(0, 100%, 0);
      }
      .switch-x-hiddenlabel {
        position: absolute;
        visibility: hidden;
      }
    }

    &:checked {
      + label {
        &::before {
          background-color: var(--data-on-color);
        }
        &::after {
          transform: translate3d(15px, 0, 0);
        }
        .switch-x-unchecked {
          opacity: 0;
          transform: translate3d(0, -100%, 0);
        }
        .switch-x-checked {
          opacity: 1;
          transform: none;
        }
      }
    }
  }
}
</style>
