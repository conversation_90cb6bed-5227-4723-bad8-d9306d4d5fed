<template>
  <!-- 对话框表单 -->
  <component :is="ComponentRender" v-model:visible="data.visible" :handle-cancel="handleCancel">
    <template #header>
      {{ `${props.title}` }}
    </template>
    <template #default >
      <FormModel ref="formRef" style="height:600px"  :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @submit="handleFinish">
        <FormItem :span="24" :label="`类型`" tooltip="" prop="path">
          <el-col :span="24">
            <el-radio-group v-model="form.type" :disabled="form.disabled" class="ml-4">
              <el-radio label="SYSTEM_BUG" size="large">Bug</el-radio>
              <el-radio label="OTHER" size="large">优化</el-radio>
              <el-radio label="SUGGESTION" size="large">建议</el-radio>
            </el-radio-group>
          </el-col>

          <p>请详细地描述您的意见，例如系统故障，请裁图故障所在页面，并描述故障出现的场景。衷心感谢您对我们系统的意见反馈。</p>
          <!-- <el-input v-model="form.name" type="text" clearable :placeholder="t('glob.Please input field', { field: `名称` })"></el-input> -->
        </FormItem>
        <FormItem :span="24" :label="`意见反馈`" tooltip="" prop="path">
          <QuillEditor ref="myQuillEditorRef" :class="[form.disabled ? 'is-disabled' : '']" v-if="data.visible" theme="snow" style="flex: 1; height: 400px" placeholder="请输入内容" v-model:content="form.content" contentType="html" :modules="modules" toolbar="full"></QuillEditor>
          <!-- <QuillEditor v-else  theme="snow" style="flex: 1; height: 400px" placeholder="请输入内容" v-model:content="form.content" contentType="html" :modules="modules" toolbar="full"></QuillEditor> -->

          <!-- <el-input v-model="form.name" type="text" clearable :placeholder="t('glob.Please input field', { field: `名称` })"></el-input> -->
        </FormItem>
      </FormModel>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </component>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, computed, h, createVNode, renderSlot, toRaw, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { templateRef } from "@vueuse/core";
// import { type Item } from "./helper";

import { buildTypeHelper } from "@/utils/type";
import { buildValidatorData } from "@/utils/validate";

import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import { QuillEditor } from "@vueup/vue-quill";
import BlotFormatter from "quill-blot-formatter";

import getUserInfo from "@/utils/getUserInfo";

const userInfo = getUserInfo();
const modules = {
  name: "blotFormatter",
  module: BlotFormatter,
};

const titleConfig = [
  { Choice: ".ql-insertMetric", title: "跳转配置" },
  { Choice: ".ql-bold", title: "加粗" },
  { Choice: ".ql-italic", title: "斜体" },
  { Choice: ".ql-underline", title: "下划线" },
  { Choice: ".ql-header", title: "段落格式" },
  { Choice: ".ql-strike", title: "删除线" },
  { Choice: ".ql-blockquote", title: "块引用" },
  { Choice: ".ql-code", title: "插入代码" },
  { Choice: ".ql-code-block", title: "插入代码段" },
  { Choice: ".ql-font", title: "字体" },
  { Choice: ".ql-size", title: "字体大小" },
  { Choice: '.ql-list[value="ordered"]', title: "编号列表" },
  { Choice: '.ql-list[value="bullet"]', title: "项目列表" },
  { Choice: ".ql-direction", title: "文本方向" },
  { Choice: '.ql-header[value="1"]', title: "h1" },
  { Choice: '.ql-header[value="2"]', title: "h2" },
  { Choice: ".ql-align", title: "对齐方式" },
  { Choice: ".ql-color", title: "字体颜色" },
  { Choice: ".ql-background", title: "背景颜色" },
  { Choice: ".ql-image", title: "图像" },
  { Choice: ".ql-video", title: "视频" },
  { Choice: ".ql-link", title: "添加链接" },
  { Choice: ".ql-formula", title: "插入公式" },
  { Choice: ".ql-clean", title: "清除字体格式" },
  { Choice: '.ql-script[value="sub"]', title: "下标" },
  { Choice: '.ql-script[value="super"]', title: "上标" },
  { Choice: '.ql-indent[value="-1"]', title: "向左缩进" },
  { Choice: '.ql-indent[value="+1"]', title: "向右缩进" },
  { Choice: ".ql-header .ql-picker-label", title: "标题大小" },
  { Choice: '.ql-header .ql-picker-item[data-value="1"]', title: "标题一" },
  { Choice: '.ql-header .ql-picker-item[data-value="2"]', title: "标题二" },
  { Choice: '.ql-header .ql-picker-item[data-value="3"]', title: "标题三" },
  { Choice: '.ql-header .ql-picker-item[data-value="4"]', title: "标题四" },
  { Choice: '.ql-header .ql-picker-item[data-value="5"]', title: "标题五" },
  { Choice: '.ql-header .ql-picker-item[data-value="6"]', title: "标题六" },
  { Choice: ".ql-header .ql-picker-item:last-child", title: "标准" },
  { Choice: '.ql-size .ql-picker-item[data-value="small"]', title: "小号" },
  { Choice: '.ql-size .ql-picker-item[data-value="large"]', title: "大号" },
  { Choice: '.ql-size .ql-picker-item[data-value="huge"]', title: "超大号" },
  { Choice: ".ql-size .ql-picker-item:nth-child(2)", title: "标准" },
  { Choice: ".ql-align .ql-picker-item:first-child", title: "居左对齐" },
  { Choice: '.ql-align .ql-picker-item[data-value="center"]', title: "居中对齐" },
  { Choice: '.ql-align .ql-picker-item[data-value="right"]', title: "居右对齐" },
  { Choice: '.ql-align .ql-picker-item[data-value="justify"]', title: "两端对齐" },
];

onMounted(() => {
  nextTick(() => {
    for (let item of titleConfig) {
      let tip = document.querySelector(".ql-toolbar " + item.Choice);
      if (!tip) continue;
      tip.setAttribute("title", item.title);
      if (item.Choice=='.ql-image' || item.Choice=='.ql-video' || item.Choice=='.ql-link') {
        tip.style.display = "none"; // 或者使用 visibility: "hidden"
      }
    }
  });
});

const formRef = templateRef<InstanceType<typeof FormModel>>("formRef");
/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Item {
  type: string;
  content: string;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  type: buildTypeHelper<Required<Item>["type"]>("SYSTEM_BUG"),
  content: buildTypeHelper<Required<Item>["content"]>(""),
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  return form;
}
/* ---------------------------------------------------------‖ ↑↑ 钩子 Start ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE ACHIEVE  END  ========================================================= */
/**
 * TODO: 本地方法
 */
/*  */
/**
 * TODO: 窗口方法
 */
interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 116,
});

const { t } = useI18n();

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<Item>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Partial<Item>) => void;
  reject?: (value: Partial<Item>) => void;
  callback?: (form: Record<string, unknown>) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }

  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], cloneDeep(toRaw(util.value)) as never)) });
    },
    form as Required<Item>
  );
}
async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => formRef.value.validate(resolve));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console

    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();

const myQuillEditorRef = ref<InstanceType<typeof QuillEditor>>();
defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, callback?: (form: Record<string, unknown>) => Promise<void>): Promise<unknown> {
    if (data.visible) handleCancel();
    const wait = new Promise<Record<string, unknown>>((resolve, reject) => ((data.resolve = resolve), (data.reject = reject)));
    $params.value = cloneDeep(params);
    setTimeout(() => {
      for (let item of titleConfig) {
        let tip = document.querySelector(".ql-toolbar " + item.Choice);
        if (!tip) continue;
        tip.setAttribute("title", item.title);
        if (item.Choice=='.ql-image' || item.Choice=='.ql-video' || item.Choice=='.ql-link') {
          tip.style.display = "none"; // 或者使用 visibility: "hidden"
        }
      }
      // myQuillEditorRef.value.readOnly = true;
      // // console.log((myQuillEditorRef.value.readOnly = true), 5555555);
    }, 1000);
    form.value.content = "";
    data.visible = true;
    data.loading = true;
    data.submitLoading = true;
    data.callback = callback;
    await nextTick();
    try {
      await runningInit($params.value);
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      handleCancel();
    }
    handleReset();
    data.loading = false;
    data.submitLoading = false;

    try {
      return await wait;
    } catch (error) {
      return error;
    }
  },
  async confirm<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.confirm(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async alert<T extends { $title: string; $type: "" | "success" | "warning" | "info" | "error"; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.alert(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
});
</script>

<style scoped lang="scss"></style>
