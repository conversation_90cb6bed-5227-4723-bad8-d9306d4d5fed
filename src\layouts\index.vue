<template>
  <el-container class="layout-container" direction="vertical">
    <Header />
    <el-container class="content-wrapper" direction="horizontal">
      <Aside />
      <Main />
    </el-container>
  </el-container>
  <CloseFullScreen v-if="navTabs.state.tabFullScreen" />
</template>

<script setup lang="ts">
import { reactive, ref, getCurrentInstance } from "vue";
import { onMounted, onBeforeMount, onUnmounted } from "vue";
import Aside from "@/layouts/model/aside.vue";
import Header from "@/layouts/model/header.vue";
import Main from "@/layouts/model/main.vue";
import CloseFullScreen from "@/layouts/component/closeFullScreen.vue";
import { useNavTabs } from "@/stores/navTabs";
import { useConfig } from "@/stores/config";
import { useEventListener } from "@vueuse/core";
import opinion from "./dialog.vue";
import { ElMessage } from "element-plus";
import { opinionCreate } from "@/views/pages/apis/opinion";
import { appTheme } from "@/api/system";

import getUserInfo from "@/utils/getUserInfo";
import { useWebSocketStore } from "@/stores/websocket";
const { proxy } = getCurrentInstance()!;
// if (!ctx) {
//   ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
//   throw new Error("Component context initialization failed!");
// }

const config = useConfig();
const navTabs = useNavTabs();

interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Record<string, unknown>) => void;
  reject?: (value: Error) => void;
  callback?: (form: Record<string, unknown>) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});

//
const wsStore = useWebSocketStore();
const userInfo = getUserInfo();

onMounted(async () => {
  // wsStore.connect(`/gw_proxy/websocket/${userInfo.userId}?authorization=${userInfo.token}`);
  // // // 添加消息处理器
  // // const removeHandler = wsStore.addMessageHandler("chat", (data) => {
  // //   console.log("Received chat message:", data);
  // //   // 处理聊天消息
  // // });
  // // 组件卸载时清理
  // onUnmounted(() => {
  //   // removeHandler();
  //   wsStore.disconnect();
  // });
});
onBeforeMount(() => {
  onAdaptiveLayout();
  useEventListener(window, "resize", onAdaptiveLayout);
});

const onAdaptiveLayout = () => {
  if (document.body.clientWidth < 1024) {
    if (config.layout.shrink === false) config.setLayout("shrink", true);
  } else {
    if (config.layout.shrink === true) config.setLayout("shrink", false);
  }
};

const opinionCreateRef = ref<InstanceType<typeof opinion>>();
async function handleCreateOpinion() {
  if (!opinionCreateRef.value) return false;
  const params = {};
  await opinionCreateRef.value.open(params, async (returnForm: Record<string, unknown>) => {
    // alert(JSON.stringify(returnForm));
    const { success, data, message } = await opinionCreate(returnForm);
    if (!success) throw new Error(message);
    if (success) {
      ElMessage.success("操作成功");
    } else {
      ElMessage.error(message);
    }
    //  if (e instanceof Error) ElMessage.error(e.message);
    // form.value.id = data.id;
    // handleFinish();
  });
  proxy?.eventBus && proxy.eventBus.emit("opinionCreate");
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100%;
  width: 100%;
}
.content-wrapper {
  /* flex-direction: column; */
  width: 100%;
  height: calc(100% - 120px);
}
// .opinion {
//   width: 100%;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   height: 60px;
//   padding-bottom: 20px;
//   box-sizing: border-box;
//   cursor: pointer;
//   // text-align: center;
//   // line-height: 40px;

//   color: dodgerblue;
// }
</style>
