<template>
  <el-container class="layout-container" direction="vertical">
    <el-header v-if="!navTabs.state.tabFullScreen" class="layout-header">
      <div class="nav-bar"></div>
    </el-header>
    <el-container class="content-wrapper" direction="horizontal">
      <el-main>
        <Loading></Loading>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import Loading from "@/components/loading.vue";
import { useNavTabs } from "@/stores/navTabs";
import { useConfig } from "@/stores/config";

const config = useConfig();
const navTabs = useNavTabs();
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100%;
  width: 100%;
}
.content-wrapper {
  /* flex-direction: column; */
  width: 100%;
  height: calc(100% - 120px);
}
.layout-header {
  height: auto;
  padding: 0;
}
.nav-bar {
  display: flex;
  height: 54px;
  width: 100vw;
  background-color: v-bind('config.getColorVal("headerBarBackground")');
}
</style>
