<template>
  <el-aside style="z-index: 1000" v-show="menuWidth !== '0px'" v-if="(!navTabs.state.tabFullScreen && navTabs.state.tabsViewRoutes.filter((v) => v.type !== appType.ROUTE).length > 1) || route.path.includes('/event/userinfo/')" :class="['layout-aside', `layout-aside-${config.layout.layoutMode}`, { shrink: !config.layout.menuCollapse && config.layout.shrink }, { desktop: config.layout.layoutMode === appTheme.DESKTOP && (navTabs.state.activeRoute || {}).type !== appType.DIR }]">
    <component :is="layoutMode"></component>
  </el-aside>
</template>

<script setup lang="ts" name="layout/aside">
import { computed } from "vue";
import { useRoute } from "vue-router";
import { useConfig } from "@/stores/config";
import { useNavTabs } from "@/stores/navTabs";
import { appTheme } from "@/utils/router";

import BaseAside from "@/layouts/model/aside/BaseAside.vue";
import ClassicsAside from "@/layouts/model/aside/ClassicsAside.vue";
import FocusAside from "@/layouts/model/aside/FocusAside.vue";
import SimplicityAside from "@/layouts/model/aside/SimplicityAside.vue";
import DesktopAside from "@/layouts/model/aside/DesktopAside.vue";
import { appType } from "@/api/system";
import getUserInfo from "@/utils/getUserInfo";

const userInfo = getUserInfo();

const route = useRoute();

const config = useConfig();
const navTabs = useNavTabs();

const menuWidth = computed(() => {
  // console.log(config.layout.layoutMode);
  switch (config.layout.layoutMode) {
    case appTheme.BASE:
      return ((navTabs.state.activeRoute || {}).children || []).length ? config.menuWidth() : "0px";
    case appTheme.CLASSICS:
      return (navTabs.state.activeRoute || {}).type === appType.ROUTE ? "0px" : config.menuWidth();
    case appTheme.FOCUS:
      return ((navTabs.state.activeRoute || {}).children || []).length ? config.menuWidth() : "0px";
    case appTheme.SIMPLICITY:
      return ((navTabs.state.activeRoute || {}).children || []).length ? config.menuWidth() : "0px";
    case appTheme.DESKTOP:
      return (navTabs.state.activeRoute || {}).type === appType.DIR && ((navTabs.state.activeRoute || {}).children || []).length ? config.menuWidth() : "80px";
    default:
      return config.menuWidth();
  }
});

const layoutMode = computed(() => {
  switch (config.layout.layoutMode) {
    case appTheme.BASE:
      return BaseAside;
    case appTheme.CLASSICS:
      return ClassicsAside;
    case appTheme.FOCUS:
      return FocusAside;
    case appTheme.SIMPLICITY:
      return SimplicityAside;
    case appTheme.DESKTOP:
      return DesktopAside;
    default:
      return BaseAside;
  }
});
</script>

<style scoped lang="scss">
.shrink {
  position: fixed;
  top: 54px;
  left: 0;
  z-index: 9999999;
}
.layout-aside {
  background: var(--ba-bg-color-overlay);
  transition: width 0.3s ease;
  width: v-bind(menuWidth);
  overflow: hidden;
  &-BASE {
    margin: 16px 0 16px 16px;
    height: calc(100vh - 152px);
    box-shadow: var(--el-box-shadow-light);
    border-radius: var(--el-border-radius-base);
  }
  &-CLASSICS {
    margin: 0;
    height: calc(100vh - 54px);
  }
  &-SIMPLICITY {
    margin: 0;
    height: calc(100vh - 54px);
  }
  &-FOCUS {
    margin: 0;
    height: calc(100vh - 54px);
  }
  &-DESKTOP {
    margin: 0;
    height: calc(100vh - 54px);
  }
}
.desktop {
  position: fixed;
  top: 50%;
  left: 0;
  z-index: 9999999;
  transform: translateY(-50%);
  height: calc(50vh + 100px);
}
</style>
