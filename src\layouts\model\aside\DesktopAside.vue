<template>
  <div v-if="config.layout.menuShowTopBar" class="layout-logo tw-flex tw-items-center tw-justify-center tw-text-[var(--el-color-primary)]">
    <svg class="tw-mx-auto" width="166" height="41" viewBox="0 0 166 41" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <rect x="0.5" y="0.5" width="164.651" height="40" fill="url(#pattern0)"></rect>
      <defs data-v-dd37268a="">
        <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_2082_6388" transform="scale(0.00282486 0.0116279)"></use></pattern>
        <image
          id="image0_2082_6388"
          width="354"
          height="86"
          xlink:href="data:image/png;base64,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"
        ></image>
      </defs>
    </svg>
  </div>

  <MenuVertical />

  <div v-if="(navTabs.state.activeRoute || {}).type !== appType.DIR" class="tw-flex tw-h-[100px] tw-items-end tw-py-[18px] tw-text-center">
    <el-link class="tw-w-full" type="danger" :underline="false" @click="onLogout">{{ $t("layouts.cancellation") }}</el-link>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { computed } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { Expand, Fold } from "@element-plus/icons-vue";
import Logo from "@/layouts/component/logo.vue";
import MenuVertical from "@/layouts/component/menuVertical.vue";
import { useConfig } from "@/stores/config";
import { useNavTabs } from "@/stores/navTabs";
import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";
import { appTheme, appType, type NavItem } from "@/utils/router";
import { logout } from "@/api/system";

const router = useRouter();
const config = useConfig();
const navTabs = useNavTabs();

const siteConfig = useSiteConfig();
const superInfo = useSuperInfo();
const adminInfo = useAdminInfo();
const usersInfo = useUsersInfo();
const userInfo = computed(() => {
  switch (siteConfig.current) {
    case superBaseRoute.name:
      return superInfo;
    case adminBaseRoute.name:
      return adminInfo;
    case usersBaseRoute.name:
      return usersInfo;
    default:
      return null;
  }
});

const active = computed<Partial<NavItem>>(() => navTabs.state.activeRoute || {});

const onMenuCollapse = function () {
  config.setLayout("menuCollapse", !config.layout.menuCollapse);
};

const onLogout = async () => {
  try {
    await ElMessageBox.confirm(`确定退出登录?`, "提示", { type: "warning" });
  } catch (error) {
    return;
  }
  try {
    const res = await logout({});
    if (res.success) {
      router.go(0);
    } else throw Object.assign(new Error(res.message), res);
  } catch (error) {
    router.replace({ path: "/" });
    if (error instanceof Error) ElMessage.error(error.message);
    return error;
  } finally {
    userInfo.value && userInfo.value.handleLogout();
  }
};
</script>

<style scoped lang="scss">
.layout-logo {
  width: 100%;
  height: 50px;
  box-sizing: border-box;
  padding: 10px;
  background: transparent;
}
.logo-img {
  width: 28px;
}
.website-name {
  display: block;
  // width: 100%;
  padding-left: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
