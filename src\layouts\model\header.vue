<template>
  <el-header style="z-index: 1000" v-if="!navTabs.state.tabFullScreen" class="layout-header">
    <component :is="layoutMode"></component>
  </el-header>
</template>
<script setup lang="ts" name="LayoutHeader">
import { computed } from "vue";
import { useConfig } from "@/stores/config";
import { useNavTabs } from "@/stores/navTabs";
import { appTheme, appType } from "@/api/application";
// import BaseNavBar from "@/layouts/component/navBar/base.vue";
// import ClassicNavBar from "@/layouts/component/navBar/classic.vue";
// import StreamlineNavBar from "@/layouts/component/menuHorizontal.vue";
import BaseHeader from "@/layouts/model/header/BaseHeader.vue";
import ClassicsHeader from "@/layouts/model/header/ClassicsHeader.vue";
import FocusHeader from "@/layouts/model/header/FocusHeader.vue";
import SimplicityHeader from "@/layouts/model/header/SimplicityHeader.vue";
import DesktopHeader from "@/layouts/model/header/DesktopHeader.vue";

const config = useConfig();
const navTabs = useNavTabs();

const layoutMode = computed(() => {
  switch (config.layout.layoutMode) {
    case appTheme.BASE:
      return BaseHeader;
    case appTheme.CLASSICS:
      return ClassicsHeader;
    case appTheme.FOCUS:
      return FocusHeader;
    case appTheme.SIMPLICITY:
      return SimplicityHeader;
    case appTheme.DESKTOP:
      return DesktopHeader;
    default:
      return BaseHeader;
  }
});
</script>

<style scoped lang="scss">
.layout-header {
  height: auto;
  padding: 0;
}
</style>
