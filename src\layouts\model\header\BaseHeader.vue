<template>
  <div class="nav-bar">
    <!-- Logo -->
    <Logo></Logo>
    <!-- 菜单 -->
    <el-menu v-if="!config.layout.shrink" ref="menuRef" class="tw-mr-[16px] tw-w-[calc(100%-500px)]" :default-active="($route.name as string)" :style="menuStyle" :default-openeds="openedsList" mode="horizontal" unique-opened menu-trigger="hover" router>
      <HorizontalMenuTree v-for="item in navTabs.state.tabsViewRoutes" :key="item.name" :item="item" :style="menuStyle"></HorizontalMenuTree>
    </el-menu>
    <!-- 设置 -->
    <NavMenus></NavMenus>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
// import { useEventListener } from "@vueuse/core";
import { useConfig } from "@/stores/config";
import { useNavTabs } from "@/stores/navTabs";
import NavMenus from "@/layouts/component/navMenus.vue";
import Logo from "@/layouts/component/logo.vue";
import HorizontalMenuTree from "@/layouts/component/HorizontalMenuTree.vue";
import { ElMenu } from "element-plus";

const config = useConfig();
const navTabs = useNavTabs();

const menuRef = ref<InstanceType<typeof ElMenu>>();
const openedsList = ref<string[]>([]);

const menuStyle = computed(() => ({
  "--el-menu-bg-color": config.getColorVal("headerBarBackground"),
  "--el-menu-text-color": config.getColorVal("headerBarTabColor"),
  "--el-bg-color-overlay": config.getColorVal("headerBarHoverBackground"),
  "--el-menu-hover-bg-color": config.getColorVal("headerBarHoverBackground"),
  "--el-menu-hover-text-color": config.getColorVal("headerBarTabColor"),
  "--el-menu-active-color": config.getColorVal("headerBarTabActiveColor"),
}));
</script>

<style scoped lang="scss">
// :global(.menu-container-variable) {
//   --el-menu-bg-color: v-bind(config.getColorVal("headerBarBackground"));
//   --el-menu-text-color: v-bind(config.getColorVal("headerBarTabColor"));
//   --el-bg-color-overlay: v-bind(config.getColorVal("headerBarHoverBackground"));
//   --el-menu-hover-bg-color: v-bind(config.getColorVal("headerBarHoverBackground"));
//   --el-menu-hover-text-color: v-bind(config.getColorVal("headerBarTabColor"));
//   --el-menu-active-color: v-bind(config.getColorVal("headerBarTabActiveColor"));
// }
.nav-menus {
  .nav-menu-item {
    height: 100%;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    .nav-menu-icon {
      box-sizing: content-box;
      color: v-bind('config.getColorVal("headerBarTabColor")');
    }
    &:hover {
      background: v-bind('config.getColorVal("headerBarHoverBackground")');
      .icon {
        animation: twinkle 0.3s ease-in-out;
      }
    }
  }
}

.nav-bar {
  display: flex;
  height: 54px;
  width: 100vw;
  padding: 0 16px;
  background-color: v-bind('config.getColorVal("headerBarBackground")');
}
@keyframes twinkle {
  0% {
    transform: scale(0);
  }
  80% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
</style>
