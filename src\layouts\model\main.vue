<template>
  <el-main class="tw-relative tw-overflow-hidden" :style="config.layout.layoutMode === appTheme.DESKTOP && (navTabs.state.activeRoute || {}).type !== appType.DIR ? { padding: '0' } : {}">
    <!-- 面包屑 -->
    <div v-show="breadcrumb.length > 1" class="tw-h-[50px]">
      <el-breadcrumb>
        <el-breadcrumb-item class="tw-text-[16px] tw-leading-[40px]" v-for="(title, i) in breadcrumb" :key="`breadcrumb${i}`">
          <span class="tw-font-semibold" :style="{ color: breadcrumb.length === i + 1 ? 'var(--el-text-color-primary)' : 'var(--el-text-color-secondary)' }">{{ title }}</span>
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <!-- 视口 -->
    <router-view name="default">
      <template #default="{ Component, route: { name, path } }">
        <transition :name="config.layout.mainAnimation" mode="out-in">
          <div v-show="!navTabs.state.loading" ref="viewRef" class="layout-main" :style="{ height: breadcrumb.length > 1 ? 'calc(100% - 50px)' : '100%' }">
            <keep-alive :include="['LayoutLoading']">
              <component :is="navTabs.state.loading ? LayoutLoading : Component" :key="!navTabs.state.loading ? name || path : '$main_$view_$loading'" />
            </keep-alive>
          </div>
        </transition>
      </template>
    </router-view>
    <Contextmenu
      ref="contextmenuRef"
      :items="[
        { name: 'refresh', label: '重新加载', icon: 'fa fa-refresh' },
        { name: 'fullScreen', label: navTabs.state.tabFullScreen ? '退出全屏' : '当前页全屏', icon: 'el-icon-FullScreen' },
      ]"
      @contextmenuItemClick="onContextmenuItem"
    />
  </el-main>
</template>

<script setup lang="ts" name="LayoutMain">
import { ref, reactive, onMounted, watch, onBeforeMount, onUnmounted, nextTick, computed, provide } from "vue";
import { useRoute, RouteLocationNormalized } from "vue-router";
// import { mainHeight as layoutMainScrollbarStyle } from "@/utils/layout";
import useCurrentInstance from "@/utils/useCurrentInstance";
import { useConfig } from "@/stores/config";
import { useNavTabs } from "@/stores/navTabs";
import { useElementSize } from "@vueuse/core";
import LayoutLoading from "@/components/loading.vue";
import Contextmenu from "@/components/contextmenu/index.vue";
import { appType, type NavItem } from "@/api/application";
import { appTheme } from "@/api/system";

import getUserInfo from "@/utils/getUserInfo";
import { ElMessageBox } from "element-plus";
import moment from "moment";

const userInfo = getUserInfo();

const contextmenuRef = ref<InstanceType<typeof Contextmenu>>();

const viewRef = ref<HTMLDivElement>();
const { width, height } = useElementSize(viewRef, { width: document.body.clientWidth - 300, height: document.body.clientHeight - 144 });

const { proxy } = useCurrentInstance();

const route = useRoute();
const config = useConfig();
const navTabs = useNavTabs();

provide("width", width);
provide("height", height);

/* 面包屑 */
const breadcrumb = computed(() => {
  const list: string[] = [];
  const getCurrentPath = (route: NavItem, currentName: string): (typeof route)[] => {
    const path: (typeof route)[] = [];
    if (route.children instanceof Array) {
      for (let index = 0; index < route.children.length; index++) {
        const item = route.children[index];
        if (item.name === currentName) {
          path.push(item);
          break;
        } else {
          const children = getCurrentPath(item, currentName);
          if (children.length) {
            path.push(item, ...children);
          }
        }
      }
    }
    return path;
  };
  if (navTabs.state.activeRoute) {
    switch (navTabs.state.activeRoute.type) {
      case appType.MICRO:
        break;
      default:
        list.push(navTabs.state.activeRoute.title as string, ...getCurrentPath(navTabs.state.activeRoute, route.name as string).map((v) => v.title as string));
        break;
    }
  }
  return list;
});

const state: { componentKey: string; keepAliveComponentNameList: string[] } = reactive({ componentKey: (route.name as string) || route.path, keepAliveComponentNameList: [] });

const addKeepAliveComponentName = function (keepAliveName: string | undefined) {
  if (keepAliveName) {
    if (state.keepAliveComponentNameList.includes(keepAliveName)) return;
    state.keepAliveComponentNameList.push(keepAliveName);
  }
};

const onContextmenu = ({ clientX, clientY }: MouseEvent) => {
  if (!contextmenuRef.value) return;
  contextmenuRef.value.onShowContextmenu(null, { x: clientX, y: clientY });
};

const onContextmenuItem = async ({ name }: { name: string }) => {
  switch (name) {
    case "refresh": {
      state.keepAliveComponentNameList = state.keepAliveComponentNameList.filter((name: string) => ((route.name as string) || route.path) !== name);
      state.componentKey = "";
      navTabs.state.loading = true;
      await nextTick();
      state.componentKey = (route.name as string) || route.path;
      navTabs.state.loading = false;
      break;
    }
    case "fullScreen": {
      navTabs.setFullScreen(!navTabs.state.tabFullScreen);
      break;
    }
  }
};
onBeforeMount(() => {
  proxy.eventBus.on("onTabViewRefresh", (menu) => {
    state.keepAliveComponentNameList = state.keepAliveComponentNameList.filter((name: string) => (menu as RouteLocationNormalized).meta.keepalive !== name);
    state.componentKey = "";
    navTabs.state.loading = true;
    nextTick(() => {
      state.componentKey = ((menu as RouteLocationNormalized).name as string) || (menu as RouteLocationNormalized).path;
      navTabs.state.loading = false;
      if ((menu as RouteLocationNormalized).meta.keepalive) addKeepAliveComponentName(((menu as RouteLocationNormalized).name as string) || (menu as RouteLocationNormalized).path);
    });
  });
  proxy.eventBus.on("onTabViewClose", (menu) => {
    state.keepAliveComponentNameList = state.keepAliveComponentNameList.filter((name: string) => (menu as RouteLocationNormalized).meta.keepalive !== name);
  });
});

onUnmounted(() => {
  proxy.eventBus.off("onTabViewRefresh");
  proxy.eventBus.off("onTabViewClose");
});

onMounted(() => {
  // 确保刷新页面时也能正确取得当前路由 keepalive 参数
  if (route.meta.keepalive) addKeepAliveComponentName((route.name as string) || route.path);

  handleAccountExpirationDateTip();
});

watch(
  () => navTabs.state.loading,
  (load) => {
    if (load) {
      state.componentKey = "";
    } else {
      nextTick(() => (state.componentKey = (route.name as string) || route.path));
    }
  }
);

watch(
  () => route.path,
  () => {
    state.componentKey = (route.name as string) || route.path;
    if (route.meta.keepalive) addKeepAliveComponentName((route.name as string) || route.path);
  }
);

function handleAccountExpirationDateTip() {
  const _key = `ACCOUNT_EXPIRATION_DATE_${userInfo.userId}`;
  const _value = `${moment().format("YYYYMMDD")}`;
  if (!userInfo.accountExpirationDate || localStorage.getItem(_key) === _value) return;
  const day = parseInt(((Number(userInfo.accountExpirationDate) - new Date().getTime()) / (1000 * 60 * 60 * 24)).toString());
  if (day > 30) return;
  ElMessageBox.alert(`您的账号有效期还剩${day}天，为不影响正常使用，请尽快找管理员申请延长有效期。`, "提醒", {
    confirmButtonText: "确定",
    beforeClose: (action, instance, done) => {
      localStorage.setItem(_key, _value);
      done();
    },
  });
}
</script>

<style scoped lang="scss">
.layout-container .layout-main {
  position: relative;
  width: 100%;
  overflow: visible;
  z-index: 1;
  &::before,
  &::after {
    content: "";
    display: table;
    overflow: hidden;
  }
  &::after {
    clear: both;
  }
}
</style>
