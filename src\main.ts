/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */
import { createApp, h } from "vue";
import pinia from "@/stores/index";
import App from "./App.vue";
import "@/styles/index.postcss";
import "@/styles/index.scss";
import "@vueup/vue-quill/dist/vue-quill.snow.css";
import "@vueup/vue-quill/dist/vue-quill.bubble.css";
import mitt from "@/utils/mitt";
import { loadLang } from "@/lang/index";
import Icon from "@/components/icon/index.vue";
import ElementPlus, { ElMessageBox } from "element-plus";
import { Search, Plus, Minus, Edit, EditPen, Delete, DeleteFilled, Tickets, Download, Upload } from "@element-plus/icons-vue";
import Security from "@/assets/dp.vue";
import { SERVER, Method } from "@/api/service/common";
import { useSiteConfig } from "@/stores/siteConfig";
import axios, { HttpStatusCode, AxiosError, isAxiosError } from "axios";
import { directives } from "@/directive";
// import microApp from "@/lib/microApp";
import microApp from "@micro-zoe/micro-app";
import createUUID from "@/utils/uuid";
// import { start } from "qiankun";
import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";
import JsonViewer from "vue-json-viewer";
import "vue-json-viewer/style.css";
import base from "../config/base";

const preventReClick = {
  mounted(el: any, binding: any) {
    el.addEventListener("click", () => {
      if (!el.disabled) {
        el.disabled = true;
        setTimeout(() => {
          el.disabled = false;
        }, binding.value || 1000); //2000ms间隔时间
      }
    });
  },
};

// import emoji from "./views/common/emoji";

// import { debounce } from "lodash-es";

// /* ResizeObserver节流补丁函数 */
// const GlobalResizeObserver = window.ResizeObserver;
// window.ResizeObserver = class extends GlobalResizeObserver {
//   constructor(callback: (entries: ResizeObserverEntry[], observer: ResizeObserver) => void) {
//     super(async (entries: ResizeObserverEntry[], observer: ResizeObserver) => {
//       await new Promise((resolve) => requestAnimationFrame(resolve));
//       if (!Array.isArray(entries) || !entries.length) return;
//       callback.apply(observer, [entries, observer]);
//     });
//   }
// };

microApp.start();

async function mount() {
  const app = createApp(App);
  app.use(pinia);
  loadLang(app);
  app.use(ElementPlus);
  app.use(JsonViewer);
  app.use(base);

  // 全局注册
  directives(app); // 指令
  app.directive("preventReClick", preventReClick);

  // directives("emoji", emoji);
  // directive(app);
  app.component("Icon", Icon);
  app.component("Search", Search);
  app.component("Plus", Plus);
  app.component("Minus", Minus);
  app.component("Edit", Edit);
  app.component("EditPen", EditPen);
  app.component("Delete", Delete);
  app.component("DeleteFilled", DeleteFilled);
  app.component("Download", Download);
  app.component("Upload", Upload);
  app.component("Tickets", Tickets);
  app.component("Security", Security);
  app.config.globalProperties.eventBus = mitt();
  app.config.globalProperties.PERMISSION = process.permission;
  Object.assign(app.config.globalProperties, { Search, Plus, Minus, Edit, EditPen, Delete, DeleteFilled, Download, Upload, Tickets });

  const siteConfig = useSiteConfig();

  // 全局语言包加载
  const render = async () => {
    try {
      const auths = await Promise.all(
        Array.from(new Set([superBaseRoute, adminBaseRoute, usersBaseRoute].map((v) => v.auth))).map(async (auth) => {
          if (!auth) return;
          const res = await axios({
            baseURL: process.env["APP_AXIOS_BASE_URL"] || undefined,
            url: `${SERVER.IAM}/env/platform`,
            method: Method.Get,
            responseType: "json",
            headers: { "Cache-Control": "no-cache", "x-auth-client-token": auth, "x-ideal-request-sequence": createUUID(), "x-ideal-request-timestamp": Date.now().toString() },
            params: { guid: createUUID() },
          });
          if (res.status !== HttpStatusCode.Ok || !res.data.success) throw new AxiosError((res.data || {}).message || "请求错误", AxiosError.ERR_BAD_RESPONSE, res.config, res.request, res.data);
          const loginChannels = ["PASSWORD"] as ("PASSWORD" | "REFRESH_TOKEN" | "SMS_CODE" | "EMAIL_CODE" | "GIT_HUB" | "WECHAT")[];
          if (res.data.data.smsAvailable) loginChannels.push("SMS_CODE");
          if (res.data.data.mailAvailable) loginChannels.push("EMAIL_CODE");
          const config: Record<string, string> = {};
          try {
            Object.assign(config, JSON.parse(res.data.data.config || "{}"));
          } catch (error) {
            /*  */
          }
          return {
            openName: res.data.data.openName,
            multiTenant: res.data.data.multiTenant,
            registrable: res.data.data.registrable,
            ownerId: res.data.data.ownerId,
            config: res.data.data.config,
            footer: config.footer || "",
            primary: config.primary || "#409EFF",
            token: auth,
            loginChannels,
          };
        })
      );
      const platformMap = auths.filter((v) => v).reduce((p, c) => (c && p.set(c.token, c), p), new Map<string, Partial<import("@/api/system").PlatformEnv>>());
      [superBaseRoute, adminBaseRoute, usersBaseRoute].forEach((base) => {
        siteConfig.dataFill(platformMap.get(base.auth) || {}, base.name);
      });

      const router = await import("./router").then((model) => model.default);
      app.use(router);
      app.mount(document.getElementById(process.env.APP_ID || "app") as HTMLElement);
    } catch (error) {
      if (isAxiosError(error)) {
        const message = h("div", null, [h("h3", { style: { color: "red", fontWeight: "500" } }, error.message), h("h3", { style: { color: "red", fontWeight: "500" } }, ((error.response || {}).data || {}).message || ""), h("p", null, "请稍后重试或联系管理员！"), h("p", { style: { color: "#909399" } }, `Error Code: ${error.code}`)]);
        await ElMessageBox.alert(message, "Systrm Error", { type: "error", confirmButtonText: "重试", customStyle: { color: "red" }, showClose: false });
        render();
      }
    }
  };

  await render();
}

mount();
