import router from "@/router/index";
import { cloneDeep } from "lodash-es";
import type { RouteRecordRaw, RouteLocationNormalized, NavigationGuardNext } from "vue-router";
import { getStaticApp, getUserMenu, getExchangeCode, getUserApps, getUserPermission, type NavItem, appTheme, appTerminal, appType } from "@/api/system";
import userInfoConstructorMap from "@/stores/userInfoConstructor";
import { ElMessage } from "element-plus";
import { handleRoute, getFirstRoute } from "@/utils/router";
import { type BaseOption } from "@/router/common";
import { useNavTabs } from "@/stores/navTabs";
import { useSiteConfig } from "@/stores/siteConfig";
import { useConfig } from "@/stores/config";
import { pathToRegexp, match } from "path-to-regexp";
import appMenus from "./menu.json";
import Color from "color";

export function getLoginRouterGuard(base: BaseOption) {
  return async function beforeEnter(route: RouteLocationNormalized, _from: RouteLocationNormalized, next: NavigationGuardNext) {
    const siteConfig = useSiteConfig();
    siteConfig.cutSite(base.name);
    const config = useConfig();

    const primary = new Color(siteConfig.primary);
    config.layout.headerBarBackground = [primary.hex(), "#1D1E1F"];
    config.layout.headerBarHoverBackground = [primary.mix(new Color("#FFFFFF"), 0.3).hex(), "#18222C"];

    const info = userInfoConstructorMap.get(base.auth)!();
    if (info.token) return next({ name: `${base.name}Loading`, query: route.query, params: route.params });
    else next();
  };
}

export function getRouterGuard(base: BaseOption) {
  const self = [`${base.name}Loading`, `${base.name}`];
  const baseReg = pathToRegexp(`${base.path}:path(.*)*`);
  return async function beforeEnter(route: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) {
    const config = useConfig();
    // // console.log("🚀 ~ file: auth.ts:26 ~ beforeEnter ~ route:", JSON.stringify({ params: route.params, query: route.query, path: route.path }, null, 2));
    const $location = new URLSearchParams({});
    const $query = Array.from(new URL(location.href).searchParams.entries()).reduce((p, [k, v]) => ({ ...p, [k]: v }), { ...route.query });
    for (const key in $query) {
      if (Object.prototype.hasOwnProperty.call($query, key)) {
        const value = $query[key];
        if (!value) {
          continue;
        } else if (value instanceof Array) {
          for (let i = 0; i < value.length; i++) {
            const item = value[i];
            if (item) $location.append(key, item);
          }
        } else {
          $location.append(key, value);
        }
      }
    }

    // if (window.opener && window.opener !== window) {
    //   window.opener.postMessage($query);
    //   window.close();
    //   return next();
    // }

    const siteConfig = useSiteConfig();
    siteConfig.cutSite(base.name);

    const navTabs = useNavTabs();
    /* 判断是否登录 */
    const info = userInfoConstructorMap.get(base.auth)!();

    const tenant = route.query.tenant as string | undefined;

    let redirect = "";
    if (typeof $query.redirect === "string") redirect = "";
    else if (route.params.path instanceof Array && route.params.path.filter((v) => v).length) redirect = `${route.path}?${$location}`;

    try {
      if (!info.token) throw undefined;
      switch (await info.updateInfo(tenant)) {
        case "FROZEN": {
          const _route: RouteRecordRaw = { path: base.path, name: Symbol("UserFrozen"), component: () => import("@/views/common/auth/frozen.vue"), meta: { base: base.name } };
          router.addRoute(_route);
          return next({ ..._route, query: route.query });
        }
        // case "INITIAL": {
        //   const _route: RouteRecordRaw = { path: base.path, name: Symbol("UserInitInfo"), component: () => import("@/views/common/auth/initInfo.vue"), meta: { base: base.name } };
        //   router.addRoute(_route);
        //   return next({ ..._route, query: route.query });
        // }
        case "NOT_TENANT": {
          const _route: RouteRecordRaw = { path: base.path, name: Symbol("SelectTenant"), component: () => import("@/views/common/auth/tenant.vue"), meta: { base: base.name } };
          router.addRoute(_route);
          return next({ ..._route, query: route.query });
        }
        default: /* "BUSY" | "DEFAULT" */
          break;
      }
    } catch (error) {
      if (error instanceof Error) await new Promise((resolve) => ElMessage.error({ onClose: () => resolve(undefined), message: (error as Error).message }));
      info.handleLogout();
      return next({ name: `${base.name}Login`, query: { redirect } });
    }

    next();

    if (info.userId) {
      /**
       * 跨应用静态路由跳出
       */
      if ($query.service) {
        const history = new URL($query.service as string, location.origin);
        const hash = new URL(/^#\//.test(history.hash) ? history.hash.replace(/^#/, "") : "", location.origin);
        try {
          const { success, message, data } = await getExchangeCode({ service: $query.service });
          if (!success) throw new Error(message);
          if (/^#\//.test(history.hash)) {
            hash.searchParams.set("authCode", data || "");
            location.href = `${history.origin}${history.pathname}${history.search}#${hash.pathname}${hash.search}${hash.hash}`;
          } else {
            history.searchParams.set("authCode", data || "");
            location.href = `${history.origin}${history.pathname}${history.search}`;
          }
        } catch (error) {
          if (error instanceof Error) {
            const message = error.message;
            await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(void 0) }));
          }
          location.reload();
        } finally {
          /*  */
        }
      }

      // const getRedirect = router.resolve(redirect);
      // if (redirect && typeof getRedirect.name === "string" && !self.includes(getRedirect.name)) {
      //   if (redirect && !baseReg.test(redirect)) return await router.replace(redirect);
      // }

      /**
       * 当前应用路由重载
       */
      try {
        info.clearPermission();
        if (route.params.path instanceof Array && route.params.path.includes("oauth")) {
          handleRoute(
            [...cloneDeep(appMenus as unknown as NavItem[])].map((v) => Object.assign(v, { order: v.order === null ? Infinity : v.order })).sort((a, b) => a.order - b.order),
            base.name
          );
          await new Promise((resolve) => setTimeout(resolve, 300));
        } else {
          const result: NavItem = await (async (req) => {
            const staticApp = getStaticApp();
            if (req.appId in staticApp) {
              return staticApp[req.appId as keyof typeof staticApp];
            } else if (req.appId) {
              const [{ success: appSuccess, message: appMessage, data: appData }, { success: menuSuccess, message: menuMessage, data: menuData }, { success: permissionSuccess, message: permissionMessage, data: permissionData }] = await Promise.all([getUserApps({ tenant: req.tenant }), getUserMenu(req), getUserPermission(req)]);
              if (!appSuccess) throw Object.assign(new Error(appMessage), { success: appSuccess, data: appData });
              if (!menuSuccess) throw Object.assign(new Error(menuMessage), { success: menuSuccess, data: menuData });
              if (!permissionSuccess) throw Object.assign(new Error(permissionMessage), { success: permissionSuccess, data: permissionData });
              const [app] = (appData instanceof Array ? appData : []).filter((app) => app.name === req.appId);
              info.setPermission(permissionData instanceof Array ? permissionData : []);
              return { ...(app || {}), children: (menuData instanceof Array ? menuData : ([] as NavItem[])).map((v) => Object.assign(v, { theme: (app || {}).theme })) };
            } else {
              return { id: "", rootId: "", parentId: "", path: "", terminal: appTerminal.WEB, title: "", enTitle: "", name: "", order: 0, icon: "local-SystemApps-line", type: appType.ROUTE, theme: appTheme.BASE, url: "", component: "", keepalive: false, enabled: true, permission: [], note: "", version: "", config: "{}", hash: "", query: {}, params: {}, pattern: /(?:)/, names: [], children: [], createdTime: "", updatedTime: "", permissionGroups: [[], []] };
            }
          })({ appId: base.app, tenant });

          (result || {}).theme && config.setLayoutMode((result || {}).theme || appTheme.BASE);

          handleRoute(
            [...cloneDeep(appMenus as unknown as NavItem[]), ...result.children].map((v) => Object.assign(v, { order: v.order === null ? Infinity : v.order })).sort((a, b) => a.order - b.order),
            base.name
          );
        }

        await new Promise((resolve) => setTimeout(resolve));
      } catch (error) {
        if (error instanceof Error) await new Promise((resolve) => ElMessage.error({ onClose: () => resolve(undefined), message: (error as Error).message }));
        handleRoute([], base.name);
        await new Promise((resolve) => setTimeout(resolve));
        return await router.replace({ name: `${base.name}NotFound`, query: route.query, params: route.params });
      }

      /**
       * 回到之前的页面
       */
      if (redirect) {
        const url = new URL(redirect, location.origin);
        let lastRouter = router.getRoutes().filter((v) => !self.includes(v.name as string) && baseReg.test(v.path));
        lastRouter = lastRouter.filter((v) => pathToRegexp(v.path).test(url.pathname));

        for (let index = 0; index < lastRouter.length; index++) {
          if (pathToRegexp(lastRouter[index].path).test(url.pathname)) {
            const params = (match<Record<string, string | string[]>>(lastRouter[index].path)(url.pathname) || {}).params || {};
            for (const key in params) {
              if (Object.prototype.hasOwnProperty.call(params, key)) {
                if (params[key] instanceof Array) params[key] = (params[key] as string[]).join("").split("/");
              }
            }
            /* path: redirect,  */
            return await router.replace({ name: lastRouter[index].name as string, query: Array.from(url.searchParams.entries()).reduce((p, [key, value]) => Object.assign(p, { [key]: value }), {}), params });
          }
        }
      }
      // 跳转到第一个菜单
      const firstRoute = getFirstRoute(navTabs.state.tabsViewRoutes);
      if (firstRoute) return await router.replace(firstRoute);
    } else {
      return await router.replace({ name: `${base.name}Login`, query: { redirect, ...route.query } });
    }
    return await router.replace({ name: `${base.name}NotFound`, query: route.query });
  };
}
