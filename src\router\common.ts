// import { useSuperInfo } from "@/stores/infoBySuper";
// import { useAdminInfo } from "@/stores/infoByAdmin";
// import { useUsersInfo } from "@/stores/infoByUsers";

export interface BaseOption {
  auth: string;
  path: string;
  name: string;
  title: string;
  app: string;
  // user: Promise<import("@/stores/interface").UserStoreData>;
}

/*
 * 超管基础静态路由
 */
export const superBaseRoute: BaseOption = {
  auth: <string>process.env["APP_SUPER_AUTHORIZATION"],
  path: `/${process.env["APP_SUPER_PLATFORM"]}`,
  name: <string>process.env["APP_SUPER_PLATFORM"],
  title: <string>process.env["APP_SUPER_PLATFORM"],
  app: <string>process.env["APP_SUPER_APP_ID"],
  // user: Promise.resolve(useSuperInfo),
  // user: import("@/stores/infoBySuper").then((m) => m.useSuperInfo),
};
/*
 * 后台基础静态路由
 */
export const adminBaseRoute: BaseOption = {
  auth: <string>process.env["APP_ADMIN_AUTHORIZATION"],
  path: `/${process.env["APP_ADMIN_PLATFORM"]}`,
  name: <string>process.env["APP_ADMIN_PLATFORM"],
  title: <string>process.env["APP_ADMIN_PLATFORM"],
  app: <string>process.env["APP_ADMIN_APP_ID"],
  // user: Promise.resolve(useAdminInfo),
  // user: import("@/stores/infoByAdmin").then((m) => m.useAdminInfo),
};
/*
 * 客户基础静态路由
 */
export const usersBaseRoute: BaseOption = {
  auth: <string>process.env["APP_USERS_AUTHORIZATION"],
  path: `/${process.env["APP_USERS_PLATFORM"]}`,
  name: <string>process.env["APP_USERS_PLATFORM"],
  title: <string>process.env["APP_USERS_PLATFORM"],
  app: <string>process.env["APP_USERS_APP_ID"],
  // user: Promise.resolve(useUsersInfo),
  // user: import("@/stores/infoByUsers").then((m) => m.useUsersInfo),
};
