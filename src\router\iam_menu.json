{
  "id": "iam_center",
  "rootId": "iam_center",
  "parentId": "",
  "path": "iam",
  "terminal": "WEB",
  "title": "iam管理",
  "name": "iam_center",
  "order": null,
  "icon": "local-SystemShield-flash-line",
  "type": "DIR",
  "theme": "BASE",
  "url": "",
  "component": "",
  "keepalive": false,
  "enabled": true,
  "permission": [],
  "note": "",
  "version": "3",
  "config": "",
  "children": [
    {
      "id": "482781369882116096",
      "parentId": null,
      "order": 0,
      "name": "iam/platform",
      "title": "平台管理",
      "type": "ROUTE",
      "path": "iam/platform",
      "icon": "local-DevelopmentTerminal-window-line",
      "url": "",
      "component": "iam/platform/index.vue",
      "keepalive": true,
      "extend": "none",
      "note": "",
      "children": []
    },
    {
      "id": "482781592368971776",
      "parentId": null,
      "order": 1,
      "name": "iam/app",
      "title": "工程应用",
      "type": "ROUTE",
      "path": "iam/app",
      "icon": "local-DevelopmentCommand-line",
      "url": "",
      "component": "iam/app/index.vue",
      "keepalive": true,
      "extend": "none",
      "note": "",
      "children": []
    }
  ],
  "createdTime": "",
  "updatedTime": ""
}


{
  "id": "userinfo/bindAccount",
  "rootId": "userinfo/bindAccount",
  "parentId": "",
  "path": "userinfo/bindAccount",
  "terminal": "WEB",
  "title": "账号绑定",
  "name": "userinfo/bindAccount",
  "order": 2,
  "icon": "local-UserTeam-line",
  "type": "ROUTE",
  "theme": "BASE",
  "url": "",
  "component": "routine/userBind/index.vue",
  "keepalive": false,
  "enabled": true,
  "permission": [],
  "note": "",
  "version": "3",
  "config": "",
  "children": [],
  "createdTime": "",
  "updatedTime": ""
},
{
  "id": "userinfo/api_key",
  "rootId": "userinfo/api_key",
  "parentId": "",
  "path": "userinfo/api_key",
  "terminal": "WEB",
  "title": "API KEY",
  "name": "userinfo/api_key",
  "order": 3,
  "icon": "local-SystemShield-keyhole-line",
  "type": "ROUTE",
  "theme": "BASE",
  "url": "",
  "component": "routine/apiKey/index.vue",
  "keepalive": false,
  "enabled": true,
  "permission": [],
  "note": "",
  "version": "3",
  "config": "",
  "children": [],
  "createdTime": "",
  "updatedTime": ""
},
