/* eslint-disable no-console, no-unused-vars, @typescript-eslint/no-unused-vars */
import { createRouter, createWebHashHistory, createWebHistory } from "vue-router";
import type { RouteLocationNormalized } from "vue-router";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";
import userInfoConstructorMap from "@/stores/userInfoConstructor";
import { getStaticRoutes } from "@/router/static";
import { useNavTabs } from "@/stores/navTabs";
import { loading } from "@/utils/loading";
import { appType } from "@/api/application";
import { useSiteConfig } from "@/stores/siteConfig";

const router = createRouter({
  // history: createWebHistory(process.env.BASE_URL),
  history: createWebHashHistory(process.env.BASE_URL),
  routes: getStaticRoutes(),
});

loading.show();

router.beforeEach((to: RouteLocationNormalized, form: RouteLocationNormalized) => {
  const siteConfig = useSiteConfig();
  const userInfoConstructor = userInfoConstructorMap.get(siteConfig.current);

  if (userInfoConstructor) {
    const info = userInfoConstructor();
    if (info.token) {
      if (form.query.tenant !== to.query.tenant && ![form.name, to.name].includes(`${siteConfig.current}Loading`)) {
        return { name: `${siteConfig.current}Loading`, query: { ...(to.query.tenant ? { tenant: to.query.tenant } : {}), redirect: to.fullPath } };
      }
    }
  }

  if (form.name === to.name) return;
  const navTabs = useNavTabs();
  if (to.meta.type !== appType.MICRO) navTabs.state.loading = true;
  // // console.log(to.meta);
  // // console.log("Route Reaction...");
  // console.time(`Router Change To: ${String(to.name) || to.fullPath}`);
  // console.timeLog(`Router Change To: ${String(to.name) || to.fullPath}`, "START");
  NProgress.start();
  NProgress.set(0.25);
  navTabs.setActiveRoute(to);
  router
    .isReady()
    // .then(() => {
    //   console.timeLog(`Router Change To: ${String(to.name) || to.fullPath}`, "SUCCESS!!!");
    // })
    // .catch(() => {
    //   console.timeLog(`Router Change To: ${String(to.name) || to.fullPath}`, "FAIL!!!");
    // })
    .finally(() => {
      NProgress.configure({ showSpinner: false, minimum: 0.5 });
      // console.timeLog(`Router Change To: ${String(to.name) || to.fullPath}`, "DONE");
      // console.timeEnd(`Router Change To: ${String(to.name) || to.fullPath}`);
    });
});

// 路由加载后
router.afterEach(async (to: RouteLocationNormalized) => {
  NProgress.set(0.75);
  const navTabs = useNavTabs();
  const siteConfig = useSiteConfig();
  await new Promise((resolve) => router.isReady().finally(() => resolve(void 0)));
  await new Promise((resolve) => setTimeout(resolve));
  loading.hide();
  navTabs.state.loading = false;
  NProgress.done();
  if (to.matched.filter((v) => [`${superBaseRoute.name}Login`, `${superBaseRoute.name}Loading`, `${superBaseRoute.name}`].includes((v.name as string) || "")).length) siteConfig.cutSite(superBaseRoute.name);
  if (to.matched.filter((v) => [`${adminBaseRoute.name}Login`, `${adminBaseRoute.name}Loading`, `${adminBaseRoute.name}`].includes((v.name as string) || "")).length) siteConfig.cutSite(adminBaseRoute.name);
  if (to.matched.filter((v) => [`${usersBaseRoute.name}Login`, `${usersBaseRoute.name}Loading`, `${usersBaseRoute.name}`].includes((v.name as string) || "")).length) siteConfig.cutSite(usersBaseRoute.name);
  navTabs.setActiveRoute(to);
});

router.onError(() => {
  const navTabs = useNavTabs();
  navTabs.state.loading = false;
  NProgress.remove();
});

export default router;
