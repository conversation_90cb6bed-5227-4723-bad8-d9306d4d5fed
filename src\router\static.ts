/* eslint-disable @typescript-eslint/no-unused-vars */
import type { RouteRecordRaw } from "vue-router";
import { i18n } from "@/lang/index";
import { getLoginRouterGuard, getRouterGuard } from "@/router/auth";
import Loading from "@/layouts/loading.vue";
import SuperLogin from "@/views/login/superLogin.vue";
import AdminLogin from "@/views/login/adminLogin.vue";
import UsersLogin from "@/views/login/usersLogin.vue";
import Layout from "@/layouts/index.vue";
import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";

export { getStaticRoutes };

function getStaticRoutes(): RouteRecordRaw[] {
  /*
   * 静态路由
   */
  return [
    {
      // 首页
      path: "/",
      // component: () => import("@/views/index.vue"),
      redirect: { name: `${usersBaseRoute.name}Loading` },
      meta: {
        title: pageTitle("home"),
      },
    },
    {
      // 单点登录
      path: "/oauth/authorize",
      name: "OauthAuthorize",
      component: import("@/views/common/auth/oauth.vue"),
      meta: {
        base: superBaseRoute.name,
        title: pageTitle("Loading"),
      },
    },

    {
      path: "/:path(.*)*",
      redirect: "/404",
    },

    ...[
      /* TODO: 超管 */
      {
        // 登录页
        path: `${superBaseRoute.path}/login`,
        name: `${superBaseRoute.name}Login`,
        component: SuperLogin,
        beforeEnter: getLoginRouterGuard(superBaseRoute),
        meta: {
          title: pageTitle("superLogin"),
        },
      },
      {
        path: superBaseRoute.path,
        name: superBaseRoute.name,
        component: Layout,
        redirect: { name: `${superBaseRoute.name}Loading` },
        meta: { title: superBaseRoute.title },
        children: [{ path: "/:path(.*)*", name: `${superBaseRoute.name}NotFound`, component: () => import("@/views/common/error/404.vue"), meta: { title: pageTitle("notFound") } }],
      },
      {
        // 加载页
        path: `${superBaseRoute.path}:path(.*)*`,
        name: `${superBaseRoute.name}Loading`,
        component: Loading,
        beforeEnter: getRouterGuard(superBaseRoute),
        meta: { base: superBaseRoute },
      },
    ],
    ...[
      /* TODO: 后台 */
      {
        // 登录页
        path: `${adminBaseRoute.path}/login`,
        name: `${adminBaseRoute.name}Login`,
        component: AdminLogin,
        beforeEnter: getLoginRouterGuard(adminBaseRoute),
        meta: {
          title: pageTitle("adminLogin"),
        },
      },
      {
        path: adminBaseRoute.path,
        name: adminBaseRoute.name,
        component: Layout,
        redirect: { name: `${adminBaseRoute.name}Loading` },
        meta: { title: adminBaseRoute.title },
        children: [{ path: "/:path(.*)*", name: `${adminBaseRoute.name}NotFound`, component: () => import("@/views/common/error/404.vue"), meta: { title: pageTitle("notFound") } }],
      },
      {
        // 加载页
        path: `${adminBaseRoute.path}:path(.*)*`,
        name: `${adminBaseRoute.name}Loading`,
        component: Loading,
        beforeEnter: getRouterGuard(adminBaseRoute),
        meta: { base: adminBaseRoute },
      },
    ],
    ...[
      /* TODO: 用户 */
      {
        // 登录页
        path: `${usersBaseRoute.path}/login`,
        name: `${usersBaseRoute.name}Login`,
        component: UsersLogin,
        beforeEnter: getLoginRouterGuard(usersBaseRoute),
        meta: {
          title: pageTitle("adminLogin"),
        },
      },
      {
        path: usersBaseRoute.path,
        name: usersBaseRoute.name,
        component: Layout,
        redirect: { name: `${usersBaseRoute.name}Loading` },
        meta: { title: usersBaseRoute.title },
        children: [{ path: "/:path(.*)*", name: `${usersBaseRoute.name}NotFound`, component: () => import("@/views/common/error/404.vue"), meta: { title: pageTitle("notFound") } }],
      },
      {
        // 加载页
        path: `${usersBaseRoute.path}:path(.*)*`,
        name: `${usersBaseRoute.name}Loading`,
        component: Loading,
        beforeEnter: getRouterGuard(usersBaseRoute),
        meta: { base: usersBaseRoute },
      },
    ],

    {
      // 500
      path: "/500",
      name: "notServe",
      component: () => import("@/views/common/error/500.vue"),
      meta: {
        title: pageTitle("notServe"), // 页面不存在
      },
    },
    {
      // 404
      path: "/404",
      name: "notFound",
      component: () => import("@/views/common/error/404.vue"),
      meta: {
        title: pageTitle("notFound"), // 页面不存在
      },
    },
    {
      // 403
      path: "/403",
      name: "notVerify",
      component: () => import("@/views/common/error/403.vue"),
      meta: {
        title: pageTitle("notVerify"), // 页面不存在
      },
    },
    {
      // 401
      path: "/401",
      name: "notPower",
      component: () => import("@/views/common/error/401.vue"),
      meta: {
        title: pageTitle("noPower"),
      },
    },
    {
      path: "/vnc",
      name: "vnc",
      component: () => import("@/views/pages/alarm_convergence/PropertyManage/deviceManage/deskTop.vue"),
      meta: {
        title: pageTitle("vnc"),
      },
    }
  ];
}

function pageTitle(name: string): string {
  return i18n.global.t(`pagesTitle.${name}`);
}
