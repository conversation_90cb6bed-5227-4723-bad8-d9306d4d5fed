/* eslint-disable */
// declare module "*.vue" {
//   import type { DefineComponent } from "vue";
//   const component: DefineComponent<{}, {}, { [key: string]: any; eventBus: Emitter; PERMISSION: PermissionAttr }>;
//   export default component;
// }

declare module "*.json" {
  const JSON: any;
  export default JSON;
}

declare module "*.svg";
declare module "*.png";
declare module "*.jpg";
declare module "*.jpeg";
declare module "*.gif";
declare module "*.bmp";
declare module "*.tiff";
