import { reactive, watch } from "vue";
import { defineStore } from "pinia";
import { LAYOUT_CONFIG } from "@/stores/constant/cacheKey";
import { Layout } from "@/stores/interface";
import { appTheme } from "@/api/application";

export const useConfig = defineStore(
  "config",
  () => {
    const layout: Layout = reactive({
      /* 全局 */
      showDrawer: false,
      // 是否收缩布局(小屏设备)
      shrink: false,
      // 后台布局方式，可选值<Default|Classic|Streamline>
      layoutMode: appTheme.BASE,
      // 全局大小，可选值<large|default|small>
      size: "default",
      // 后台主页面切换动画，可选值<slide-right|slide-left|el-fade-in-linear|el-fade-in|el-zoom-in-center|el-zoom-in-top|el-zoom-in-bottom>
      mainAnimation: "slide-right",
      // 是否暗黑模式
      isDark: false,

      /* 侧边菜单 */
      // 侧边菜单背景色
      menuBackground: ["#ffffff", "#1d1e1f"],
      // 侧边菜单文字颜色
      menuColor: ["#303133", "#CEDCE4"],
      // 侧边菜单激活项背景色
      menuActiveBackground: ["#FFF2EE", "#1d1e1f"],
      // 侧边菜单激活项文字色
      menuActiveColor: ["#FA541C", "#ffffff"],
      // 侧边菜单宽度(展开时)，单位px
      menuWidth: 260,
      // 侧边菜单项默认图标
      menuDefaultIcon: "local-DocumentPages-line",
      // 是否水平折叠收起菜单
      menuCollapse: false,
      // 是否只保持一个子菜单的展开(手风琴)
      menuUniqueOpened: true,
      // 显示菜单栏顶栏(LOGO)
      menuShowTopBar: false,

      /* 顶栏 */
      // 顶栏文字色
      headerBarTabColor: ["#FFFFFF", "#CEDCE4"],
      // 顶栏激活项背景色
      headerBarTabActiveBackground: ["#252B3B", "#1d1e1f"],
      // 顶栏激活项文字色
      headerBarTabActiveColor: ["#FFFFFF", "#FFFFFF"],
      // 顶栏背景色
      headerBarBackground: ["#2F54EB", "#1D1E1F"],
      // 顶栏悬停时背景色
      headerBarHoverBackground: ["#FA541C", "#18222c"],
    });

    const lang = reactive({
      // 默认语言，可选值<zh-cn|en>
      defaultLang: localStorage.getItem("systemLang") || "zh-cn",
      // 当在默认语言包找不到翻译时，继续在 fallbackLang 语言包内查找翻译
      fallbackLang: [
        "zh-cn",
        "en",
        // "az",
        // "de",
        // "pt",
        // "es",
        // "da",
        // "fr",
        // "nb-no",
        // "zh-tw",
        // "it",
        // "ko",
        // "ja",
        // "nl",
        // "vi",
        // "ru",
        // "tr",
        // "pt-br",
        // "fa",
        // "th",
        // "id",
        // "bg",
        // "pa",
        // "pl",
        // "fi",
        // "sv",
        // "el",
        // "sk",
        // "ca",
        // "cs",
        // "uk",
        // "tk",
        // "ta",
        // "lv",
        // "af",
        // "et",
        // "sl",
        // "ar",
        // "he",
        // "lt",
        // "mn",
        // "kk",
        // "hu",
        // "ro",
        // "ku",
        // "km",
        // "sr",
        // "eu",
        // "ky",
        // "hy-am",
        // "hr",
        // "eo",
        // "bn",
        // "ug-cn",
      ],
      // 支持的语言列表
      langArray: [
        { label: "简体中文", name: "zh-cn", value: "简体中文" },
        { label: "美式英语", name: "en", value: "English" },
        // { label: "阿塞拜疆语", name: "az", value: "Azerbayjanlı" },
        // { label: "德语", name: "de", value: "Deutsch" },
        // { label: "葡萄牙语", name: "pt", value: "Português" },
        // { label: "西班牙语", name: "es", value: "Español" },
        // { label: "丹麦语", name: "da", value: "Dansk" },
        // { label: "法语", name: "fr", value: "Français" },
        // { label: "挪威语", name: "nb-no", value: "norwegian" },
        // { label: "繁体中文", name: "zh-tw", value: "繁體中文" },
        // { label: "意大利语", name: "it", value: "Italiano" },
        // { label: "韩文", name: "ko", value: "한국어" },
        // { label: "日语", name: "ja", value: "日本語" },
        // { label: "荷兰语", name: "nl", value: "Nederlands" },
        // { label: "越南语", name: "vi", value: "Tiếng Việt" },
        // { label: "俄语", name: "ru", value: "русский язык" },
        // { label: "土耳其语", name: "tr", value: "Türkçe" },
        // { label: "巴西葡萄牙语", name: "pt-br", value: "Português brasileiro" },
        // { label: "波斯语", name: "fa", value: "فارسی" },
        // { label: "泰语", name: "th", value: "ภาษาไทย" },
        // { label: "印尼语", name: "id", value: "Indonesia" },
        // { label: "保加利亚语", name: "bg", value: "Български" },
        // { label: "普什图语", name: "pa", value: "پښتو" },
        // { label: "波兰语", name: "pl", value: "Paszto" },
        // { label: "芬兰语", name: "fi", value: "suomi" },
        // { label: "瑞典语", name: "sv", value: "Svenska" },
        // { label: "希腊语", name: "el", value: "Ελληνικά" },
        // { label: "斯洛伐克语", name: "sk", value: "Slovenská" },
        // { label: "加泰罗尼亚", name: "ca", value: "Catalònia" },
        // { label: "捷克语", name: "cs", value: "Čeština" },
        // { label: "乌克兰语", name: "uk", value: "Українська" },
        // { label: "土库曼语", name: "tk", value: "Türkmençe" },
        // { label: "泰米尔语", name: "ta", value: "தாமில்" },
        // { label: "拉脱维亚语", name: "lv", value: "Latvijas" },
        // { label: "南非荷兰语", name: "af", value: "Afrikaans" },
        // { label: "爱沙尼亚语", name: "et", value: "Eesti keel" },
        // { label: "斯洛文尼亚语", name: "sl", value: "slovenščina" },
        // { label: "阿拉伯语", name: "ar", value: "بالعربية" },
        // { label: "希伯来语", name: "he", value: "היברית" },
        // { label: "立陶宛语", name: "lt", value: "Lietuva" },
        // { label: "蒙古语", name: "mn", value: "Монгол" },
        // { label: "哈萨克语", name: "kk", value: "қазақ" },
        // { label: "匈牙利语", name: "hu", value: "Magyar" },
        // { label: "罗马尼亚语", name: "ro", value: "Română" },
        // { label: "库尔德语", name: "ku", value: "Kurdî" },
        // { label: "维吾尔语", name: "km", value: "ئۇيغۇر" },
        // { label: "高棉语", name: "sr", value: "កម្ពុជា។" },
        // { label: "塞尔维亚语", name: "eu", value: "Српски" },
        // { label: "巴斯克语", name: "ky", value: "euskara" },
        // { label: "吉尔吉斯语", name: "hy-am", value: "Кыргызча" },
        // { label: "亚美尼亚语", name: "hr", value: "հայերեն" },
        // { label: "克罗地亚语", name: "eo", value: "Hrvatski" },
        // { label: "世界语", name: "bn", value: "esperanto" },
        // { label: "孟加拉语", name: "ug-cn", value: "বাংলা" },
      ],
    });

    function menuWidth() {
      if (layout.shrink && layout.layoutMode !== appTheme.CLASSICS) {
        return layout.menuCollapse ? "0px" : layout.menuWidth + "px";
      }
      // 菜单是否折叠
      return layout.menuCollapse ? "64px" : layout.menuWidth + "px";
    }

    function setLang(val: string) {
      lang.defaultLang = val;
    }

    function onSetLayoutColor(data = layout.layoutMode) {
      // 切换布局时，如果是为默认配色方案，对菜单激活背景色重新赋值
      switch (data) {
        case appTheme.BASE:
          break;
        case appTheme.CLASSICS:
          break;
        case appTheme.FOCUS:
          break;
        case appTheme.SIMPLICITY:
          break;
        default:
          break;
      }
    }

    function setLayoutMode(data: keyof typeof appTheme) {
      layout.layoutMode = data;
      onSetLayoutColor(data);
    }

    const setLayout = (name: keyof Layout, value: any) => {
      layout[name] = value as never;
    };

    const getColorVal = function (name: keyof Layout): string {
      const colors = layout[name] as string[];
      if (layout.isDark) {
        return colors[1];
      } else {
        return colors[0];
      }
    };

    return { layout, lang, menuWidth, setLang, setLayoutMode, setLayout, getColorVal, onSetLayoutColor };
  },
  {
    persist: {
      key: LAYOUT_CONFIG,
      paths: ["lang"],
    },
  }
);
