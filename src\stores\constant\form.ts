// import { h, reactive } from "vue";
// import { ElMessageBox, ElMessage, ElForm, ElFormItem, ElInput, ElRadioGroup, ElRadio } from "element-plus";
// import { setCurrentUserPassword } from "@/api/system";

// export async function resetPassword(title: string): Promise<void> {
//   const form = reactive<{ loading: boolean; data: Record<"mfaCode" | "newPassword" | "confirmationPassword", string> & { mfaMethod: "PASSWORD" | "SMS" | "EMAIL" | "TOTP" }; message: string; ref: InstanceType<typeof ElForm> | null }>({
//     loading: false,
//     message: "",
//     data: {
//       mfaMethod: "PASSWORD",
//       mfaCode: "",
//       newPassword: "",
//       confirmationPassword: "",
//     },
//     ref: null,
//   });
//   const message = h({
//     setup(_$props, _$ctx) {
//       return () => {
//         if (form.message) return h("div", form.message);
//         else {
//           return h(
//             ElForm,
//             { model: form.data, labelWidth: 80, labelPosition: "left", ref: ($form) => (form.ref = $form as InstanceType<typeof ElForm>) },
//             {
//               default() {
//                 return [
//                   h(ElFormItem, { prop: "mfaMethod", rules: [{ required: true, message: "请选择验证方式", trigger: "blur" }], label: "验证方式" }, () => h(ElRadioGroup, { "modelValue": form.data.mfaMethod, "onUpdate:modelValue": ($event) => (form.data.mfaMethod = $event as "PASSWORD" | "SMS" | "EMAIL" | "TOTP") }, () => [h(ElRadio, { label: "PASSWORD" }, () => "密码"), h(ElRadio, { label: "SMS" }, () => "短信"), h(ElRadio, { label: "EMAIL" }, () => "邮箱"), h(ElRadio, { label: "TOTP" }, () => "动态码")])),
//                   h(ElFormItem, { prop: "mfaCode", rules: [{ required: true, message: "请输入验证码", trigger: "blur" }], label: "验证码" }, () => h(ElInput, { "type": "text", "modelValue": form.data.mfaCode, "onUpdate:modelValue": ($event) => (form.data.mfaCode = $event) })),
//                   h(
//                     ElFormItem,
//                     {
//                       prop: "newPassword",
//                       rules: [
//                         { required: true, message: "请输入新密码", trigger: "blur" },
//                         { pattern: /^((?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*?])|(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])|(?=.*[a-z])(?=.*[0-9])(?=.*[~!@#$%^&*?])|(?=.*[A-Z])(?=.*[0-9])(?=.*[~!@#$%^&*?])).{8,25}$/, message: "密码为8-25位包含英文大小写、数字、特殊字符(~!@#$%^&*?)中的三种", trigger: "blur" },
//                       ],
//                       label: "新密码",
//                     },
//                     () => h(ElInput, { "type": "password", "autocomplete": "new-password", "showPassword": true, "modelValue": form.data.newPassword, "onUpdate:modelValue": ($event) => (form.data.newPassword = $event) })
//                   ),
//                   h(
//                     ElFormItem,
//                     {
//                       prop: "confirmationPassword",
//                       rules: [
//                         { required: true, message: "请输入确认密码", trigger: "blur" },
//                         { validator: (rule, value, callback) => callback(value === form.data.newPassword ? undefined : new Error("Error")), message: "两次密码输入不一致", trigger: "blur" },
//                       ],
//                       label: "确认密码",
//                     },
//                     () => h(ElInput, { "type": "password", "autocomplete": "new-password", "showPassword": true, "modelValue": form.data.confirmationPassword, "onUpdate:modelValue": ($event) => (form.data.confirmationPassword = $event) })
//                   ),
//                 ];
//               },
//             }
//           );
//         }
//       };
//     },
//   });
//   try {
//     await ElMessageBox({
//       title,
//       type: "",
//       message,
//       showCancelButton: true,
//       showConfirmButton: true,
//       draggable: true,
//       showClose: false,
//       closeOnClickModal: false,
//       closeOnHashChange: false,
//       beforeClose: async (action, instance, done) => {
//         if (action === "confirm") {
//           instance.confirmButtonLoading = true;
//           instance.confirmButtonText = "Loading...";
//           const valid = await new Promise((resolve) => form.ref?.validate(resolve));
//           if (!valid) {
//             instance.confirmButtonLoading = false;
//             instance.confirmButtonText = "确认";
//             return;
//           }
//           try {
//             const { success, message, data } = await setCurrentUserPassword(form.data);
//             if (success) {
//               done();
//               ElMessage.success("密码修改成功");
//               instance.confirmButtonLoading = false;
//               instance.confirmButtonText = "确认";
//             } else throw Object.assign(new Error(message), { success, data });
//           } catch (error) {
//             if (error instanceof Error) {
//               instance.showConfirmButton = false;
//               instance.showInput = false;
//               form.message = error.message;
//               instance.type = "error";
//             }
//           } finally {
//             instance.confirmButtonLoading = false;
//           }
//         } else {
//           done();
//         }
//       },
//     });
//   } catch (error) {
//     throw Object.assign(new Error(form.message), { form: form.data });
//   }
// }
