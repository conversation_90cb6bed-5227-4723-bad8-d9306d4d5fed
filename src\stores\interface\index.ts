// 变量名对应含义请在 /stores/* 里边找
// import { RouteRecordRaw, RouteLocationNormalized } from "vue-router";
import { NavItem } from "@/api/application";
import { appTheme } from "@/api/application";
type permissionKey = typeof import("@/views/pages/permission") extends { [key: string]: infer T } ? T : never;

export interface Layout {
  showDrawer: boolean;
  shrink: boolean;
  layoutMode: keyof typeof appTheme;
  size: "large" | "default" | "small";
  mainAnimation: string;
  isDark: boolean;
  menuWidth: number;
  menuDefaultIcon: string;
  menuCollapse: boolean;
  menuUniqueOpened: boolean;
  menuShowTopBar: boolean;
  menuBackground: [string, string];
  menuColor: [string, string];
  menuActiveBackground: [string, string];
  menuActiveColor: [string, string];
  headerBarTabColor: [string, string];
  headerBarBackground: [string, string];
  headerBarHoverBackground: [string, string];
  headerBarTabActiveBackground: [string, string];
  headerBarTabActiveColor: [string, string];
}
export interface NavTabs {
  loading: boolean;
  activeName: string;
  activeRoute: NavItem | null;
  // tabsView: RouteLocationNormalized[];
  tabFullScreen: boolean;
  tabsViewRoutes: NavItem[];
  navigation: (Omit<import("@/api/system").NavigationGroupItem, "items"> & { items: import("@/api/system").NavigationDataItem[] })[];
  star: string[];
  authNode: Map<string, string[]>;
}

// export interface MemberCenter {
//   open: boolean;
//   layoutMode: string;
//   activeRoute: RouteRecordRaw | RouteLocationNormalized | null;
//   viewRoutes: RouteRecordRaw[];
//   showHeadline: boolean;
//   authNode: Map<string, string[]>;
//   shrink: boolean;
//   menuExpand: boolean;
// }

export interface UseInfo {
  userId: string /* 用户ID */;
  tenantId: string /* 用户所属的租户ID */;
  containerId: string /* 安全容器ID */;
  tenantName: string /* 用户所属租户名称 */;
  tenantabbreviation: string /* 用户所属租户缩写 */;
  name: string /* 姓名 */;
  username: string /* 姓名 */;
  // nickname?: string /* 昵称 */;
  account?: string /* 账号 */;
  phone?: string /* 手机号码 */;
  email?: string /* 邮箱 */;
  language?: import("@/api/locale").Locales /* 语言 */;
  zoneId?: string /* 时区 */;
  gender: "FEMALE" | "MALE" | "SECRET" /* 性别 */;
  // profilePicture?: string /* 头像 */;
  status: "FROZEN" | "INITIAL" | "NOT_TENANT" | "BUSY" | "DEFAULT" /* 用户状态: 冻结 未初始化 未选择租户 忙  默认 */;
  // busy: boolean /* 是否忙碌状态 */;
  // busyTime?: string /* 进入忙碌状态的时间戳 */;
  // improved: boolean /* 是否已完善个人信息 */;
  // improvedTime?: string /* 完善个人信息时间 */;
  registrationTime?: string /* 账号注册时间 */;
  switchedTenantId?: string /* 当前切换的租户ID */;
  loginTime: string /* 登录时间 */;
  /*  */
  avatar: string /* 头像 */;
  token: string;
  refreshToken: string;
  tenants: import("@/api/system").TenantItem[];
  currentTenantId: string;
  permission: string[];
  tenantAbbreviation: string;
  accountExpirationDate: string;
}
export interface UseGetter {
  currentTenant: (this: ReturnType<UserStoreData>, state: ReturnType<UserStoreData>) => import("@/api/system").TenantItem | undefined;
}
export interface UseActions {
  dataFill(this: ReturnType<UserStoreData>, state: Partial<UseInfo>): void;
  removeToken(this: ReturnType<UserStoreData>): void;
  handleLogout(this: ReturnType<UserStoreData>): void;
  setToken(this: ReturnType<UserStoreData>, token: string, type: "token" | "refreshToken"): void;
  getToken(this: ReturnType<UserStoreData>, type?: "auth" | "refresh"): string;
  logout(this: ReturnType<UserStoreData>): Promise<void>;
  updateInfo(this: ReturnType<UserStoreData>, tenant?: string): Promise<UseInfo["status"]>;
  cutUserBusy(this: ReturnType<UserStoreData>): Promise<void>;
  cutTenant(this: ReturnType<UserStoreData>, tenant: string): Promise<void>;
  clearPermission(this: ReturnType<UserStoreData>): void;
  setPermission(this: ReturnType<UserStoreData>, permission: string[]): void;
  hasPermission(this: ReturnType<UserStoreData>, ...permissions: permissionKey[]): boolean;
  // resetPassword(this: ReturnType<UserStoreData>, title: string): Promise<void>;
}

export type UserStoreData = import("pinia").StoreDefinition<string, UseInfo, UseGetter, UseActions>;

export interface TaskItem {
  // 任务唯一标识
  uuid: string;
  // 创建时间
  createtime: string;
  // 状态
  status: number;
  // 命令
  command: string;
  // 命令执行日志
  message: string[];
  // 显示命令执行日志
  showMessage: boolean;
  // 失败阻断后续命令执行
  blockOnFailure: boolean;
  // 扩展信息，自动发送到后台
  extend: string;
  // 执行结果回调
  callback: Function;
}

export interface Terminal {
  show: boolean;
  showDot: boolean;
  taskList: TaskItem[];
  packageManager: string;
  showPackageManagerDialog: boolean;
  showConfig: boolean;
  automaticCleanupTask: string;
  port: string;
}

export type SiteConfig = import("@/api/system").PlatformEnv;
// export interface SiteConfig {
//   multiTenant: boolean;
//   registrable: boolean;
//   desensitize: boolean;
//   loginChannel: ("PASSWORD" | "SMS_CODE" | "REFRESH_TOKEN" | "GIT_HUB")[];
//   frontConfig: { [key: string]: string };

//   site_name: string;
//   record_number?: string;
//   version: string;
//   cdn_url: string;
//   api_url: string;
//   upload: {
//     mode: string;
//     maxsize: number;
//     mimetype: string;
//     savename: string;
//     url?: string;
//     params?: anyObj;
//   };
// }
