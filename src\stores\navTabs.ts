import { isEmpty } from "lodash-es";
import { reactive, computed } from "vue";
import { defineStore } from "pinia";
import { STORE_TAB_VIEW_CONFIG } from "@/stores/constant/cacheKey";
import { NavTabs } from "@/stores/interface/index";
import { RouteLocationNormalized } from "vue-router";
import { /* getNavigationData, getUserNavigation, */ type NavigationDataItem } from "@/api/system";

export const useNavTabs = defineStore(
  "navTabs",
  () => {
    const state: NavTabs = reactive({
      loading: false,
      // 激活tab的index
      activeName: "",
      // 激活的tab
      activeRoute: null,
      // // tab列表
      // tabsView: [],
      // 当前tab是否全屏
      tabFullScreen: false,
      // 从后台加载到的菜单路由列表
      tabsViewRoutes: [],
      // 导航列表navigation
      navigation: [],
      // 快捷访问
      star: [],
      // 按钮权限节点
      authNode: new Map(),
    });

    const allNavigation = computed(() => state.navigation.reduce((p, c) => p.concat(c.items instanceof Array ? c.items : ([] as NavigationDataItem[])), [] as NavigationDataItem[]));
    const starNavigation = computed(() => state.star.map((id) => allNavigation.value.find((nav) => nav.id === id) as NavigationDataItem).filter((v) => v));

    // function addTab(route: RouteLocationNormalized) {
    //   if (!route.meta.addtab) return;
    //   for (const key in state.tabsView) {
    //     if (state.tabsView[key].path === route.path) {
    //       state.tabsView[key].params = !isEmpty(route.params) ? route.params : state.tabsView[key].params;
    //       state.tabsView[key].query = !isEmpty(route.query) ? route.query : state.tabsView[key].query;
    //       return;
    //     }
    //   }
    //   state.tabsView.push(route);
    // }

    // function closeTab(route: RouteLocationNormalized) {
    //   state.tabsView.map((v, k) => {
    //     if (v.path == route.path) {
    //       state.tabsView.splice(k, 1);
    //       return;
    //     }
    //   });
    // }

    // /**
    //  * 关闭多个标签
    //  * @param retainMenu 需要保留的标签，否则关闭全部标签
    //  */
    // const closeTabs = (retainMenu: RouteLocationNormalized | false = false) => {
    //   if (retainMenu) {
    //     state.tabsView = [retainMenu];
    //   } else {
    //     state.tabsView = [];
    //   }
    // };

    const setActiveRoute = (route: RouteLocationNormalized): void => {
      state.activeRoute = state.tabsViewRoutes.find((item) => (item.names instanceof Array ? (item.names as string[]) : [item.name]).includes(route.name as string)) || null;
      state.activeName = ((state.activeRoute || {}).name as string) || "";
    };

    const setTabsViewRoutes = (data: typeof state.tabsViewRoutes): void => {
      state.tabsViewRoutes = data;
    };

    const setAuthNode = (key: string, data: string[]) => {
      state.authNode.set(key, data);
    };

    const setFullScreen = (fullScreen: boolean): void => {
      state.tabFullScreen = fullScreen;
    };

    // const updateNavigation = async (): Promise<void> => {
    //   state.navigation = [];
    //   state.star = [];
    //   const [{ success, message, data }, { success: starSuccess, message: starMessage, data: starData }] = await Promise.all([getNavigationData({}), getUserNavigation({})]);
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   if (!starSuccess) throw Object.assign(new Error(starMessage), { success: starSuccess, data: starData });
    //   state.navigation = data.groups instanceof Array ? data.groups : [];
    //   state.star = ((starData.value || {}).star instanceof Array ? (starData.value.star as string[]) : []).filter((id) => allNavigation.value.filter((v) => v.id === id).length);
    // };

    return {
      state,
      allNavigation,
      starNavigation,
      // addTab,
      // closeTab,
      // closeTabs,
      setActiveRoute,
      setTabsViewRoutes,
      setAuthNode,
      setFullScreen,
      // updateNavigation,
    };
  },
  {
    persist: {
      key: STORE_TAB_VIEW_CONFIG,
      paths: ["tabFullScreen"],
    },
  }
);

/**
 * 对iframe的url进行编码
 */
// function encodeRoutesURI(data: RouteRecordRaw[]) {
//   data.forEach((item) => {
//     if (item.meta?.type == "iframe") {
//       item.path = "/admin/iframe/" + encodeURIComponent(item.path);
//     }

//     if (item.children && item.children.length) {
//       item.children = encodeRoutesURI(item.children);
//     }
//   });
//   return data;
// }
