import { defineStore } from "pinia";
import { SiteConfig } from "@/stores/interface";
import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";
import { SITE_INFO } from "@/stores/constant/cacheKey";
import { loginChannels, type PlatformEnv } from "@/api/system";

const baseSite = JSON.stringify({
  openName: "",
  multiTenant: false,
  registrable: false,
  ownerId: "",
  config: "{}",
  footer: "",
  loginChannels: [] as (keyof typeof loginChannels)[],
  token: "",
  origin: "",
  primary: "#409EFF",
  platformInformation: { showCarousel: true },
} as PlatformEnv);

export const useSiteConfig = defineStore("siteConfig", {
  state: () => ({
    current: usersBaseRoute.name,
    siteList: {
      [superBaseRoute.name]: JSON.parse(baseSite),
      [adminBaseRoute.name]: JSON.parse(baseSite),
      [usersBaseRoute.name]: JSON.parse(baseSite),
    } as Record<string, PlatformEnv>,
  }),
  getters: {
    platformInformation: () => ({
      showCarousel: ["unms.sst.net.cn"].includes(location.host),
      // navigationLogo: "",
    }),
    openName: (state) => state.siteList[state.current].openName,
    multiTenant: (state) => state.siteList[state.current].multiTenant,
    registrable: (state) => state.siteList[state.current].registrable,
    footer: (state) => state.siteList[state.current].footer,
    loginChannels: (state) => state.siteList[state.current].loginChannels,
    token: (state) => state.siteList[state.current].token,
    origin: (state) => state.siteList[state.current].origin,
    primary: (state) => state.siteList[state.current].primary,
    baseInfo: (state) => {
      switch (state.current) {
        case superBaseRoute.name:
          return { ...superBaseRoute };
        case adminBaseRoute.name:
          return { ...adminBaseRoute };
        case usersBaseRoute.name:
          return { ...usersBaseRoute };
      }
    },
  },
  actions: {
    dataFill(state: Partial<SiteConfig>, barrel?: string) {
      if (Object.prototype.hasOwnProperty.call(state, "openName")) this.siteList[barrel || this.current].openName = state.openName!;
      if (Object.prototype.hasOwnProperty.call(state, "multiTenant")) this.siteList[barrel || this.current].multiTenant = state.multiTenant!;
      if (Object.prototype.hasOwnProperty.call(state, "registrable")) this.siteList[barrel || this.current].registrable = state.registrable!;
      if (Object.prototype.hasOwnProperty.call(state, "ownerId")) this.siteList[barrel || this.current].ownerId = state.ownerId!;
      if (Object.prototype.hasOwnProperty.call(state, "config")) this.siteList[barrel || this.current].config = state.config!;
      if (Object.prototype.hasOwnProperty.call(state, "footer")) this.siteList[barrel || this.current].footer = state.footer!;
      if (Object.prototype.hasOwnProperty.call(state, "loginChannels")) this.siteList[barrel || this.current].loginChannels = state.loginChannels!;
      if (Object.prototype.hasOwnProperty.call(state, "token")) this.siteList[barrel || this.current].token = state.token!;
      if (Object.prototype.hasOwnProperty.call(state, "origin")) this.siteList[barrel || this.current].origin = state.origin!;
      if (Object.prototype.hasOwnProperty.call(state, "primary")) this.siteList[barrel || this.current].primary = state.primary!;
    },
    cutSite(key: string) {
      if (this.current === key) return;
      this.current = key;
    },
  },
  persist: {
    key: SITE_INFO,
    paths: ["current"],
    storage: sessionStorage,
  },
});
