import { find } from "lodash-es";
import { ElMessage } from "element-plus";
import { defineStore } from "pinia";
// import { ADMIN_INFO } from "@/stores/constant/cacheKey";
import { UseInfo, UseActions, UserStoreData } from "@/stores/interface";
import { useSiteConfig } from "@/stores/siteConfig";
import { useConfig } from "@/stores/config";
import Color from "color";
import { locales } from "@/api/locale";
import { logout, getUserInfo, cutUserBusy, cutCurrentTenant, getUserTenants, getAvatar } from "@/api/system";

const authInfo = Array.from(new Set([<string>process.env["APP_SUPER_AUTHORIZATION"], <string>process.env["APP_ADMIN_AUTHORIZATION"], <string>process.env["APP_USERS_AUTHORIZATION"]]));
export default authInfo.reduce((p, c) => {
  p.set(
    c,
    defineStore(c, {
      state: (): UseInfo => {
        return {
          userId: "",
          username: "",
          // nickname: "",
          account: "",
          phone: "",
          email: "",
          gender: "SECRET",
          language: locales["zh-CN"],
          avatar: "",
          token: "",
          refreshToken: "",
          status: "DEFAULT",
          currentTenantId: "",
          permission: [],
          zoneId: "",
          loginTime: "",
          tenants: [],
          tenantId: "",
          tenantAbbreviation: "",
          containerId: "",
          tenantName: "",
          tenantabbreviation: "",
          name: "",
          accountExpirationDate: "",
          // busy, improved
        };
      },
      getters: {
        currentTenant: (state: UseInfo) => find(state.tenants, (tenant) => tenant.id === state.currentTenantId),
      },
      actions: {
        dataFill(state: Partial<UseInfo>) {
          console.log(state);
          if (Object.prototype.hasOwnProperty.call(state, "userId") && state.userId !== undefined) this.userId = state.userId;
          // if (Object.prototype.hasOwnProperty.call(state, "nickname") && state.nickname !== undefined) this.nickname = state.nickname;
          if (Object.prototype.hasOwnProperty.call(state, "account") && state.account !== undefined) this.account = state.account;
          if (Object.prototype.hasOwnProperty.call(state, "phone") && state.phone !== undefined) this.phone = state.phone;
          if (Object.prototype.hasOwnProperty.call(state, "email") && state.email !== undefined) this.email = state.email;
          if (Object.prototype.hasOwnProperty.call(state, "gender") && state.gender !== undefined) this.gender = state.gender;
          if (Object.prototype.hasOwnProperty.call(state, "language") && state.language !== undefined) this.language = state.language;
          if (Object.prototype.hasOwnProperty.call(state, "avatar") && state.avatar !== undefined) this.avatar = state.avatar;
          if (Object.prototype.hasOwnProperty.call(state, "token") && state.token !== undefined) this.token = state.token;
          if (Object.prototype.hasOwnProperty.call(state, "refreshToken") && state.refreshToken !== undefined) this.refreshToken = state.refreshToken;
          if (Object.prototype.hasOwnProperty.call(state, "status") && state.status !== undefined) this.status = state.status;
          if (Object.prototype.hasOwnProperty.call(state, "tenants") && state.tenants !== undefined) this.tenants = state.tenants;
          if (Object.prototype.hasOwnProperty.call(state, "currentTenantId") && state.currentTenantId !== undefined) this.currentTenantId = state.currentTenantId;
          if (Object.prototype.hasOwnProperty.call(state, "zoneId") && state.zoneId !== undefined) this.zoneId = state.zoneId;

          if (Object.prototype.hasOwnProperty.call(state, "loginTime") && state.loginTime !== undefined) this.loginTime = state.loginTime;
          if (Object.prototype.hasOwnProperty.call(state, "tenantId") && state.tenantId !== undefined) this.tenantId = state.tenantId;
          if (Object.prototype.hasOwnProperty.call(state, "tenantAbbreviation") && state.tenantAbbreviation !== undefined) this.tenantAbbreviation = state.tenantAbbreviation;
          if (Object.prototype.hasOwnProperty.call(state, "accountExpirationDate") && state.accountExpirationDate !== undefined) this.accountExpirationDate = state.accountExpirationDate;
          if (Object.prototype.hasOwnProperty.call(state, "containerId") && state.containerId !== undefined) this.containerId = state.containerId;
        },
        removeToken() {
          this.token = "";
          this.refreshToken = "";
        },
        handleLogout() {
          this.token = "";
          this.refreshToken = "";
          this.userId = "";
          this.username = "";
          // this.nickname = "";
          this.account = "";
          this.phone = "";
          this.email = "";
          this.gender = "SECRET";
          this.language = locales["zh-CN"];
          this.avatar = "";
          this.status = "DEFAULT";
          this.tenants = [];
          this.accountExpirationDate = "";
          this.containerId = "";
        },
        clearPermission() {
          this.permission = [];
        },
        setPermission(permission: string[]) {
          this.permission.push(...permission);
        },
        hasPermission(...permissions: unknown[]) {
          return permissions.length ? permissions.some((permission) => this.permission.includes(<string>permission || "")) : true;
        },
        setToken(token: string, type: "token" | "refreshToken") {
          this[type] = token;
        },
        getToken(type: "auth" | "refresh" = "auth"): string {
          return type === "auth" ? this.token : this.refreshToken;
        },
        async logout(): Promise<void> {
          try {
            if (this.token) {
              const res = await logout({});
              if (!res.success) throw Object.assign(new Error(res.message), res);
            }
          } catch (error) {
            if (error instanceof Error) await new Promise((resolve) => ElMessage.error({ onClose: () => resolve(undefined), message: (error as Error).message }));
            return;
          } finally {
            this.handleLogout();
          }
        },
        async updateInfo(tenant): Promise<UseInfo["status"]> {
          try {
            const siteConfig = useSiteConfig();
            const config = useConfig();
            const [{ success, message, data }, { success: tenantSuccess, message: tenantMessage, data: tenantData }] = await Promise.all([
              /*  */
              getUserInfo({ token: this.getToken("auth"), ...(tenant ? { tenant } : {}) }),
              getUserTenants({ token: this.getToken("auth"), ...(tenant ? { tenant } : {}) }),
            ]);
            if (!success) throw Object.assign(new Error(message), { success, data });
            if (!tenantSuccess) throw Object.assign(new Error(tenantMessage), { success: tenantSuccess, data: tenantData });
            this.tenants = tenantData instanceof Array ? tenantData : [];
            this.userId = data.userId || "";
            this.username = this.name = data.name || "";
            // this.nickname = data.nickname || "";
            this.account = data.account || "";
            this.phone = data.phone || "";
            this.email = data.email || "";
            this.gender = data.gender || "SECRET";
            this.language = data.language || locales["zh-CN"];
            this.avatar = await getAvatar({ filePath: data.profilePicture });
            /* data.frozen ? "FROZEN" :  */
            // this.status = data.improved ? (data.busy ? "BUSY" : "DEFAULT") : "INITIAL";
            this.status = data.busy ? "BUSY" : "DEFAULT";
            this.zoneId = data.zoneId;
            this.loginTime = data.loginTime;
            this.tenantId = data.tenantId || "";
            this.tenantAbbreviation = data.tenantabbreviation || "";
            this.accountExpirationDate = data.accountExpirationDate || "";
            this.containerId = data.containerId || "";
            const primary = new Color(this.status === "BUSY" ? "#F87A00" : siteConfig.primary);
            config.layout.headerBarBackground = [primary.hex(), "#1D1E1F"];
            config.layout.headerBarHoverBackground = [primary.mix(new Color("#FFFFFF"), 0.3).hex(), "#18222C"];
            if (["FROZEN"].includes(this.status)) {
              /*  */
            } else if (siteConfig.multiTenant) {
              const tenants = this.tenants.filter((_tenant) => !_tenant.blocked);
              const $tenant = find(tenants, (_tenant) => _tenant.id === data.switchedTenantId) || find(tenants, (_tenant) => _tenant.id === data.tenantId);

              if (sessionStorage.getItem("currentTenantId") && this.tenants.find((v) => v.id === sessionStorage.getItem("currentTenantId"))) {
                this.currentTenantId = sessionStorage.getItem("currentTenantId") as string;
              } else {
                if (!$tenant) {
                  this.status = "NOT_TENANT";
                  this.currentTenantId = "";
                } else {
                  this.currentTenantId = $tenant.id;
                }

                sessionStorage.setItem("currentTenantId", this.currentTenantId);
              }
            }
          } catch (error) {
            await this.logout();
            throw error;
          }
          return this.status;
        },
        async cutUserBusy(): Promise<void> {
          try {
            const { success, message, data } = await cutUserBusy({ value: this.status !== "BUSY" });
            if (!success) throw Object.assign(new Error(message), { success, data });
          } catch (error) {
            // eslint-disable-next-line no-console
            console.error(error);
          } finally {
            this.updateInfo();
          }
        },
        async cutTenant(tenant: string): Promise<void> {
          try {
            const { success, message, data } = await cutCurrentTenant({ tenantId: tenant });
            if (!success) throw Object.assign(new Error(message), { success, data });
          } catch (error) {
            // eslint-disable-next-line no-console
            console.error(error);
          } finally {
            this.currentTenantId = tenant;
          }
          // /tenants/{tenantId}/switch
        },
      } as UseActions,
      persist: {
        key: c,
        paths: ["token", "refreshToken", "currentTenantId"],
      },
    })
  );
  return p;
}, new Map<string, UserStoreData>());
