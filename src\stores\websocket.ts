// stores/websocket.ts
import { defineStore } from "pinia";
import { ref, computed, h } from "vue";

import { ElNotification } from "element-plus";

type MessageHandler = (data: any) => void;

export const useWebSocketStore = defineStore("websocket", () => {
  // 状态
  const socket = ref<WebSocket | null>(null);
  const isConnected = ref(false);
  const reconnectAttempts = ref(0);
  const messageHandlers = ref<Record<string, MessageHandler[]>>({});

  // 配置
  const maxReconnectAttempts = 5;
  const reconnectInterval = 3000; // 3秒

  // 连接WebSocket
  function connect(url: string) {
    if (isConnected.value || socket.value) return;

    socket.value = new WebSocket(url);

    socket.value.onopen = () => {
      isConnected.value = true;
      reconnectAttempts.value = 0;
    };

    socket.value.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log("Received message:", data);

        if (data.type === "eventOrderCreated") {
          ElNotification({
            title: data.sender,
            // message: h("i", { style: "color: teal" }, data.content),
            message: data.content,
          });
        }

        const handlers = messageHandlers.value[data.type] || [];

        // console.log("handlers:", handlers);

        handlers.forEach((handler) => handler(data));
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    };

    socket.value.onclose = () => {
      isConnected.value = false;
      socket.value = null;
      console.log("WebSocket disconnected");

      // 自动重连
      if (reconnectAttempts.value < maxReconnectAttempts) {
        reconnectAttempts.value++;
        setTimeout(() => connect(url), reconnectInterval);
      }
    };

    socket.value.onerror = (error) => {
      console.error("WebSocket error:", error);
    };
  }

  // 断开连接
  function disconnect() {
    if (socket.value) {
      socket.value.close();
      socket.value = null;
    }
    isConnected.value = false;
    reconnectAttempts.value = 0;
  }

  // 发送消息
  function send(message: any) {
    if (socket.value && isConnected.value) {
      socket.value.send(JSON.stringify(message));
    } else {
      console.error("WebSocket is not connected");
    }
  }

  // 添加消息处理器
  function addMessageHandler(type: string, handler: MessageHandler) {
    if (!messageHandlers.value[type]) {
      messageHandlers.value[type] = [];
    }
    messageHandlers.value[type].push(handler);

    // 返回一个移除该处理器的函数
    return () => {
      messageHandlers.value[type] = messageHandlers.value[type].filter((h) => h !== handler);
    };
  }

  return {
    socket,
    isConnected,
    connect,
    disconnect,
    send,
    addMessageHandler,
  };
});
