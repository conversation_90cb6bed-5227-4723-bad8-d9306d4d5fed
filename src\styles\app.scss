/* 基本样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
html > body,
html > body > #app {
  margin: 0 !important;
  padding: 0 !important;
  width: 100% !important;
  height: 100% !important;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  // font-family: Noto Sans SC, Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -webkit-tap-highlight-color: transparent;
  background-color: var(--ba-bg-color) !important;
  font-size: 14px !important;
  overflow: hidden !important;
  position: relative !important;
}

.w100 {
  width: 100% !important;
}
.h100 {
  height: 100% !important;
}
.default-main {
  position: relative;
  margin: var(--ba-main-space) var(--ba-main-space) 60px var(--ba-main-space);
}
.default-super {
  position: relative;
  margin: var(--ba-main-space);
}
.ba-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.zoom-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: -10px;
  right: -10px;
  cursor: se-resize;
}
.block-help {
  display: block;
  width: 100%;
  color: #909399;
  font-size: 13px;
  line-height: 16px;
  padding-top: 5px;
}
.table-alert {
  border: none;
}

.table-header {
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  background-color: var(--ba-bg-color-overlay);
  border: 1px solid var(--el-border-color-lighter);
  border-bottom: none;
  padding: 13px 15px;
  font-size: 14px;
  .table-header-operate-text {
    margin-left: 0;
  }
}
.table-search {
  display: flex;
  align-items: flex-start;
  margin-left: auto;
  .quick-search {
    width: auto;
  }
}

/* 表格顶部菜单-s */
.table-header {
  .table-header-operate .icon {
    font-size: 14px !important;
    color: var(--el-color-white) !important;
  }
  .el-button.is-disabled .icon {
    color: var(--el-button-disabled-text-color) !important;
  }
}
/* 表格顶部菜单-e */

/* 鼠标置入浮动效果-s */
.suspension {
  transition: all 0.3s ease;
}
.suspension:hover {
  -webkit-transform: translateY(-4px) scale(1.02);
  -moz-transform: translateY(-4px) scale(1.02);
  -ms-transform: translateY(-4px) scale(1.02);
  -o-transform: translateY(-4px) scale(1.02);
  transform: translateY(-4px) scale(1.02);
  -webkit-box-shadow: 0 14px 24px rgba(0, 0, 0, 0.2);
  box-shadow: 0 14px 24px rgba(0, 0, 0, 0.2);
  z-index: 999;
  border-radius: 6px;
}

/* 表单组-s */
.form-group {
  padding: 0 24px 6px 24px;
  margin: 0 1px;
  border-radius: 6px;
  outline: 1px var(--el-border-style) var(--el-border-color);

  > .el-divider--horizontal {
    transform: translateY(-1px);
  }
}
/* 表单组-e */
/* 列表组-s */
ul.list-group {
  width: 100%;
  display: flex;
  flex-direction: column;

  > li {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 14px 20px;
    > * {
      flex-shrink: 0;
      &:not(:first-child) {
        margin-left: 12px;
      }
    }

    &:not(:first-child) {
      border-top: var(--el-border);
    }
  }
}
/* 列表组-e */
/* 验证码-s */
.captcha-img {
  width: 100px;
  height: 40px;
  vertical-align: bottom;
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
  transition: var(--el-transition-box-shadow);
  outline: 1px solid var(--el-border-color);
  cursor: pointer;
}
@at-root .dark {
  .captcha-img {
    filter: brightness(61%);
  }
}
/* 验证码-e */
/* 全局遮罩-s */
.layout-shade {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999990;
}
/* 全局遮罩-e */

/* 图片上传预览-s */
.img-preview-dialog .el-dialog__body {
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    max-width: 100%;
  }
}
/* 图片上传预览-e */

/* 页面切换动画-s */
.slide-right-enter-active,
.slide-right-leave-active,
.slide-left-enter-active,
.slide-left-leave-active {
  will-change: transform;
  transition: all 0.3s ease;
}
// slide-right
.slide-right-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}
.slide-right-leave-to {
  opacity: 0;
  transform: translateX(20px);
}
// slide-left
.slide-left-enter-from {
  @extend .slide-right-leave-to;
}
.slide-left-leave-to {
  @extend .slide-right-enter-from;
}
/* 页面切换动画-e */

/* 会员中心相关-s */
.frontend-footer-brother {
  min-height: calc(100vh - 120px);
}
.user-views {
  padding-left: 15px;
  .user-views-card {
    margin-bottom: 15px;
  }
}
/* 会员中心-e */

/* 暗黑模式公共样式-s */
.ba-icon-dark {
  color: var(--el-text-color-primary) !important;
}
/* 暗黑模式公共样式-e */

/* 自适应-s */
@media screen and (max-width: 768px) {
  .xs-hidden {
    display: none;
  }
}
@media screen and (max-width: 1000px) {
  .ba-operate-dialog .el-dialog__body {
    padding-left: 0;
    padding-right: 0;
  }
}
@media screen and (max-width: 991px) {
  .user-views {
    padding: 0;
  }
}
/* 自适应-e */

/* 浏览器自动填充背景-s */
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 32px var(--el-input-bg-color, var(--el-fill-color-blank)) inset; /**通过边框阴影遮挡住背景*/
  -webkit-text-fill-color: var(--el-input-text-color, var(--el-text-color-regular)); /*自动填充内容的文本颜色*/
}
/* 浏览器自动填充背景-e */

.menu-container-variable {
  .el-sub-menu.is-active {
    > .el-sub-menu__title {
      color: var(--el-menu-active-color) !important;
      border: none !important;
      > .el-sub-menu__icon-arrow {
        position: absolute;
        margin-top: -7px;
      }
    }
  }
}

.clearfix {
  &::before,
  &::after {
    content: "";
    display: table;
  }
  &::after {
    clear: both;
  }
}

.text-left_input__inner {
  .el-input__wrapper {
    .el-input__inner {
      text-align: left;
    }
  }
}
