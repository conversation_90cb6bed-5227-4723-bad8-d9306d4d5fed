/**
 * This injects Tailwind's base styles and any base styles registered by
 * plugins.
 */
@tailwind base;

/**
 * This injects Tailwind's component classes and any component classes
 * registered by plugins.
 */
@tailwind components;

/**
 * This injects Tailwind's utility classes and any utility classes registered
 * by plugins.
 */
@tailwind utilities;

/**
 * Use this directive to control where <PERSON><PERSON><PERSON> injects the hover, focus,
 * responsive, dark mode, and other variants of each class.
 *
 * If omitted, Tailwind will append these classes to the very end of
 * your stylesheet by default.
 */
@tailwind variants;

@layer base {
  input {
    @apply tw-font-sans;
  }
}

@layer components {
}

@layer utilities {
}

.content {
  width: calc(100vw - 32px);
  @apply tw-mx-auto sm:tw-w-[608px] md:tw-w-[736px] lg:tw-w-[992px] xl:tw-w-[1248px] 2xl:tw-w-[1504px];
}
.title-fonnt {
  @apply tw-text-xl sm:tw-text-xl md:tw-text-2xl lg:tw-text-2xl xl:tw-text-2xl 2xl:tw-text-3xl;
  @apply tw-mb-2 sm:tw-mb-2 md:tw-mb-3 lg:tw-mb-4 xl:tw-mb-5 2xl:tw-mb-6;
}
.text-fonnt {
  @apply tw-text-xs sm:tw-text-xs md:tw-text-sm lg:tw-text-base xl:tw-text-lg 2xl:tw-text-xl;
}

.flex-row,
.flex-col {
  @apply tw-relative tw-flex tw-overflow-hidden;
}
.flex-row > *:not(:last-child) {
  @apply tw-mr-[4px];
}
.flex-row > *:not(:first-child) {
  @apply tw-ml-[4px];
}

.flex-col > *:not(:last-child) {
  @apply tw-mb-[4px];
}
.flex-col > *:not(:first-child) {
  @apply tw-mt-[4px];
}
