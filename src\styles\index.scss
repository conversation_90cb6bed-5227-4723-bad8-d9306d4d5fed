@use "./theme/display.scss";
@use "./theme/index.scss";
@use "./app.scss";
@use "./var.scss";
@use "./dark.scss";

// BaseStyle
.flex {
  &-row {
    flex-direction: row;
    justify-content: center;
    align-items: flex-start;
    > * {
      &:first-child {
        flex-shrink: 0;
      }
      &:not(:last-child) {
        margin-right: 4px;
      }
      &:not(:first-child) {
        margin-left: 4px;
      }
    }
  }
  &-col {
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    > * {
      &:first-child {
        flex-shrink: 0;
      }
      &:not(:last-child) {
        margin-bottom: 4px;
      }
      &:not(:first-child) {
        margin-top: 4px;
      }
    }
  }
  &-row,
  &-col {
    display: flex;
    position: relative;
    overflow: hidden;
  }
}
/* 自定义菜单 */
.tab-menu {
  border: none;
  .el-menu-item-group__title {
    position: relative;
    .menu-add-btn {
      font-size: inherit;
      vertical-align: middle;
      line-height: 1;
      position: absolute;
      top: 50%;
      right: 7px;
      transform: translateY(-50%);
      .el-icon {
        font-size: inherit;
        vertical-align: bottom;
      }
    }
  }
  &.el-popper {
    .el-menu-item-group__title {
      .menu-add-btn {
        right: var(--el-menu-base-level-padding);
      }
    }
  }
  &:not(.el-menu--collapse, .el-popper) {
    width: 200px;
  }
}
.move_bar {
  cursor: move;
}
/* 自定义搜索栏 */
.flex-search {
  display: flex;
  height: 40px;
  width: fit-content;
  margin-bottom: 20px;
  padding: 0 20px;
  justify-content: center;
  align-items: flex-start;
  flex-wrap: nowrap;

  > .left,
  > .center,
  > .right {
    white-space: nowrap;
    height: fit-content;
    > * {
      width: fit-content;
      vertical-align: middle;
      &:not(:last-child) {
        margin-right: 6px;
      }
      &:not(:first-child) {
        margin-left: 6px;
      }
    }
  }
  > .left {
    margin-right: 6px;
    text-align: left;
    flex: 10;
  }
  > .center {
    margin-left: 6px;
    margin-right: 6px;
    text-align: center;
    flex: 4;
  }
  > .right {
    margin-left: 6px;
    text-align: right;
    flex: 10;
  }
}
