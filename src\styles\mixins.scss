@mixin set-css-var-value($name, $value) {
    #{joinVarName($name)}: #{$value};
}

@function joinVarName($list) {
    $name: '--ba';
    @each $item in $list {
        @if $item != '' {
            $name: $name + '-' + $item;
        }
    }
    @return $name;
}

@function getCssVarName($args...) {
    @return joinVarName($args);
}

/*
 * 通过映射设置所有的CSS变量
 */
@mixin set-component-css-var($name, $variables) {
    @each $attribute, $value in $variables {
        @if $attribute == 'default' {
            #{getCssVarName($name)}: #{$value};
        } @else {
            #{getCssVarName($name, $attribute)}: #{$value};
        }
    }
}
