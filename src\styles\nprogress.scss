$nprogress-color: #29d !default;
$nprogress-height: 2px !default;
$nprogress-spinner-opacity: 1 !default;
$nprogress-spinner-size: 18px !default;
$nprogress-spinner-stroke-width: 2px !default;

#nprogress {
  // Make clicks pass-through
  pointer-events: none;
}

#nprogress .bar {
  background: $nprogress-color;
  position: fixed;
  z-index: 1031;
  top: 0;
  left: 0;
  width: 100%;
  height: $nprogress-height;
}

// Glow effect
#nprogress .peg {
  display: block;
  position: absolute;
  right: 0px;
  width: 100px;
  height: 100%;
  box-shadow: 0 0 10px $nprogress-color, 0 0 5px $nprogress-color;
  opacity: 1;
  -webkit-transform: rotate(3deg) translate(0px, -4px);
  -ms-transform: rotate(3deg) translate(0px, -4px);
  transform: rotate(3deg) translate(0px, -4px);
}

// Spinner
#nprogress .spinner {
  display: block;
  position: fixed;
  z-index: 1031;
  top: 15px;
  right: 15px;
  opacity: $nprogress-spinner-opacity;
}

#nprogress .spinner-icon {
  width: $nprogress-spinner-size;
  height: $nprogress-spinner-size;
  box-sizing: border-box;
  border: solid $nprogress-spinner-stroke-width transparent;
  border-top-color: $nprogress-color;
  border-left-color: $nprogress-color;
  border-radius: 50%;
  -webkit-animation: nprogress-spinner 400ms linear infinite;
  animation: nprogress-spinner 400ms linear infinite;
}

.nprogress-custom-parent {
  overflow: hidden;
  position: relative;
}

.nprogress-custom-parent #nprogress .spinner,
.nprogress-custom-parent #nprogress .bar {
  position: absolute;
}

@-webkit-keyframes nprogress-spinner {
  0% {
    -webkit-transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes nprogress-spinner {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
