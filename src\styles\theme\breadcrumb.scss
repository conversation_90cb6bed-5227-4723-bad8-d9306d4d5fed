@use "mixins/mixins" as *;
@use "mixins/utils" as *;
@use "common/var" as *;

@include b(breadcrumb) {
  font-size: 14px;
  line-height: 1;
  @include utils-clearfix;

  @include e(separator) {
    margin: 0 9px;
    font-weight: bold;
    color: getCssVar("text-color", "placeholder");

    &.#{$namespace}-icon {
      margin: 0 6px;
      font-weight: normal;
      svg {
        vertical-align: middle;
      }
    }
  }

  @include e(item) {
    float: left;
    display: inline-flex;
    align-items: center;

    @include e(inner) {
      color: getCssVar("text-color", "regular");

      &.is-link,
      & a {
        font-weight: bold;
        text-decoration: none;
        transition: getCssVar("transition", "color");
        color: getCssVar("text-color", "primary");

        &:hover {
          color: getCssVar("color-primary");
          cursor: pointer;
        }
      }
    }

    &:last-child {
      .#{$namespace}-breadcrumb__inner,
      .#{$namespace}-breadcrumb__inner a {
        &,
        &:hover {
          font-weight: normal;
          color: getCssVar("text-color", "regular");
          cursor: text;
        }
      }

      .#{$namespace}-breadcrumb__separator {
        display: none;
      }
    }
  }
}
