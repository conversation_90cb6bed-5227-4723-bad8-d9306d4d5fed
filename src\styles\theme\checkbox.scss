@use "sass:map";

@use "common/var" as *;
@use "mixins/mixins" as *;
@use "mixins/var" as *;
@use "mixins/_button";
@use "mixins/utils" as *;

$checkbox-height: () !default;
$checkbox-height: map.merge($common-component-size, $checkbox-height);

$checkbox-bordered-input-height: () !default;
$checkbox-bordered-input-height: map.merge(
  (
    "large": 14px,
    "default": 12px,
    "small": 12px,
  ),
  $checkbox-bordered-input-height
);

$checkbox-font-size: () !default;
$checkbox-font-size: map.merge(
  (
    "large": 14px,
    "small": 12px,
  ),
  $checkbox-font-size
);

$checkbox-bordered-input-width: () !default;
$checkbox-bordered-input-width: map.merge($checkbox-bordered-input-height, $checkbox-bordered-input-width);

@include b(checkbox) {
  @include set-component-css-var("checkbox", $checkbox);
}

@include b(checkbox) {
  color: getCssVar("checkbox-text-color");
  font-weight: getCssVar("checkbox-font-weight");
  font-size: getCssVar("font-size", "base");
  position: relative;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  user-select: none;
  margin-right: 30px;
  height: getCssVarWithDefault("checkbox-height", map.get($checkbox-height, "default"));

  @include when(disabled) {
    cursor: not-allowed;
  }

  @include when(bordered) {
    padding: 0 map.get($checkbox-bordered-padding-right, "default")-$border-width 0 map.get($checkbox-bordered-padding-left, "default")-$border-width;
    border-radius: getCssVar("border-radius-base");
    border: getCssVar("border");
    box-sizing: border-box;

    &.is-checked {
      border-color: getCssVar("color-primary");
    }

    &.is-disabled {
      border-color: getCssVar("border-color-lighter");
    }

    @each $size in (large, small) {
      &.#{$namespace}-checkbox--#{$size} {
        padding: 0 map.get($checkbox-bordered-padding-right, $size)-$border-width 0 map.get($checkbox-bordered-padding-left, $size)-$border-width;

        border-radius: map.get($button-border-radius, $size);

        .#{$namespace}-checkbox__label {
          font-size: map.get($button-font-size, $size);
        }

        .#{$namespace}-checkbox__inner {
          height: map.get($checkbox-bordered-input-height, $size);
          width: map.get($checkbox-bordered-input-width, $size);
        }
      }
    }

    &.#{$namespace}-checkbox--small {
      .#{$namespace}-checkbox__inner {
        &::after {
          height: 6px;
          width: 2px;
        }
      }
    }
  }

  input:focus-visible {
    & + .#{$namespace}-checkbox__inner {
      outline: 2px solid getCssVar("checkbox-input-border-color-hover");
      outline-offset: 1px;
      border-radius: getCssVar("checkbox-border-radius");
    }
  }

  @include e(input) {
    white-space: nowrap;
    cursor: pointer;
    outline: none;
    display: inline-flex;
    position: relative;

    @include when(disabled) {
      .#{$namespace}-checkbox__inner {
        background-color: getCssVar("checkbox-disabled-input-fill");
        border-color: getCssVar("checkbox-disabled-border-color");
        cursor: not-allowed;

        &::after {
          cursor: not-allowed;
          border-color: getCssVar("checkbox-disabled-icon-color");
        }
      }

      &.is-checked {
        .#{$namespace}-checkbox__inner {
          background-color: getCssVar("checkbox-disabled-checked-input-fill");
          border-color: getCssVar("checkbox-disabled-checked-input-border-color");

          &::after {
            border-color: getCssVar("checkbox-disabled-checked-icon-color");
          }
        }
      }

      &.is-indeterminate {
        .#{$namespace}-checkbox__inner {
          background-color: getCssVar("checkbox-disabled-checked-input-fill");
          border-color: getCssVar("checkbox-disabled-checked-input-border-color");

          &::before {
            background-color: getCssVar("checkbox-disabled-checked-icon-color");
            border-color: getCssVar("checkbox-disabled-checked-icon-color");
          }
        }
      }

      & + span.#{$namespace}-checkbox__label {
        color: getCssVar("disabled-text-color");
        cursor: not-allowed;
      }
    }

    @include when(checked) {
      .#{$namespace}-checkbox__inner {
        background-color: getCssVar("checkbox-checked-bg-color");
        border-color: getCssVar("checkbox-checked-input-border-color");

        &::after {
          transform: rotate(45deg) scaleY(1);
          border-color: getCssVar("checkbox-checked-icon-color");
        }
      }

      & + .#{$namespace}-checkbox__label {
        color: getCssVar("checkbox-checked-text-color");
      }
    }
    @include when(focus) {
      // Visually distinguish when focus
      &:not(.is-checked) {
        .#{$namespace}-checkbox__original:not(:focus-visible) {
          border-color: getCssVar("checkbox-input-border-color-hover");
        }
      }
    }
    @include when(indeterminate) {
      .#{$namespace}-checkbox__inner {
        background-color: getCssVar("checkbox-checked-bg-color");
        border-color: getCssVar("checkbox-checked-input-border-color");

        &::before {
          content: "";
          position: absolute;
          display: block;
          background-color: getCssVar("checkbox-checked-icon-color");
          height: 2px;
          transform: scale(0.5);
          left: 0;
          right: 0;
          top: 5px;
        }

        &::after {
          display: none;
        }
      }
    }
  }
  @include e(inner) {
    display: inline-block;
    position: relative;
    border: getCssVar("checkbox-input-border");
    border-radius: getCssVar("checkbox-border-radius");
    box-sizing: border-box;
    width: getCssVar("checkbox-input-width");
    height: getCssVar("checkbox-input-height");
    background-color: getCssVar("checkbox-bg-color");
    z-index: getCssVar("index-normal");
    transition:
      border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46),
      background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46),
      outline 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);

    &:hover {
      border-color: getCssVar("checkbox-input-border-color-hover");
    }

    &::after {
      box-sizing: content-box;
      content: "";
      border: 1px solid transparent;
      border-left: 0;
      border-top: 0;
      height: 7px;
      left: 4px;
      position: absolute;
      top: 1px;
      transform: rotate(45deg) scaleY(0);
      width: 3px;
      transition: transform 0.15s ease-in 0.05s;
      transform-origin: center;
    }
  }

  @include e(original) {
    opacity: 0;
    outline: none;
    position: absolute;
    margin: 0;
    width: 0;
    height: 0;
    z-index: -1;
  }

  @include e(label) {
    display: inline-block;
    padding-left: 8px;
    line-height: 1;
    font-size: getCssVar("checkbox-font-size");
  }

  @each $size in (large, small) {
    &.#{$namespace}-checkbox--#{$size} {
      height: map.get($checkbox-height, $size);
      @include e(label) {
        font-size: map.get($checkbox-font-size, $size);
      }
      @include e(inner) {
        width: map.get($checkbox-font-size, $size);
        height: map.get($checkbox-font-size, $size);
      }
    }
  }

  &.#{$namespace}-checkbox--small {
    @include e(input) {
      @include when(indeterminate) {
        @include e(inner) {
          &::before {
            top: 4px;
          }
        }
      }
    }
    @include e(inner) {
      &::after {
        width: 2px;
        height: 6px;
      }
    }
  }

  &:last-of-type {
    margin-right: 0;
  }
}
