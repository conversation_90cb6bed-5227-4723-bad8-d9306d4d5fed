@use "./var" as *;
@use "../mixins/mixins" as *;
@use "../mixins/var" as *;

:root {
  @include set-component-css-var("popup", $popup);
}

.v-modal-enter {
  animation: v-modal-in getCssVar("transition-duration-fast") ease;
}

.v-modal-leave {
  animation: v-modal-out getCssVar("transition-duration-fast") ease forwards;
}

@keyframes v-modal-in {
  0% {
    opacity: 0;
  }
  100% {
  }
}

@keyframes v-modal-out {
  0% {
  }
  100% {
    opacity: 0;
  }
}

.v-modal {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: getCssVar("popup-modal-opacity");
  background: getCssVar("popup-modal-bg-color");
}

@include b(popup-parent) {
  @include m(hidden) {
    overflow: hidden;
  }
}
