@use "../mixins/mixins" as *;
@use "../common/var" as *;

@include b(picker-panel) {
  color: getCssVar("text-color", "regular");
  background: getCssVar("bg-color", "overlay");
  border-radius: getCssVar("border-radius-base");
  line-height: 30px;

  .#{$namespace}-time-panel {
    margin: 5px 0;
    border: solid 1px getCssVar("datepicker-border-color");
    background-color: getCssVar("bg-color", "overlay");
    box-shadow: getCssVar("box-shadow-light");
  }

  @include e((body, body-wrapper)) {
    &::after {
      content: "";
      display: table;
      clear: both;
    }
  }

  @include e(content) {
    position: relative;
    margin: 15px;
  }

  @include e(footer) {
    border-top: 1px solid getCssVar("datepicker-inner-border-color");
    padding: 4px 12px;
    text-align: right;
    background-color: getCssVar("bg-color", "overlay");
    position: relative;
    font-size: 0;
  }

  @include e(shortcut) {
    display: block;
    width: 100%;
    border: 0;
    background-color: transparent;
    line-height: 28px;
    font-size: 14px;
    color: getCssVar("datepicker-text-color");
    padding-left: 12px;
    text-align: left;
    outline: none;
    cursor: pointer;

    &:hover {
      color: getCssVar("datepicker-hover-text-color");
    }

    &.active {
      background-color: #e6f1fe;
      color: getCssVar("datepicker-active-color");
    }
  }

  @include e(btn) {
    border: 1px solid getCssVar("fill-color", "darker");
    color: getCssVar("text-color", "primary");
    line-height: 24px;
    border-radius: 2px;
    padding: 0 20px;
    cursor: pointer;
    background-color: transparent;
    outline: none;
    font-size: 12px;

    &[disabled] {
      color: getCssVar("text-color", "disabled");
      cursor: not-allowed;
    }
  }

  @include e(icon-btn) {
    font-size: 12px;
    color: getCssVar("datepicker-icon-color");
    border: 0;
    background: transparent;
    cursor: pointer;
    outline: none;
    margin-top: 8px;

    &:hover {
      color: getCssVar("datepicker-hover-text-color");
    }
    &:focus-visible {
      color: getCssVar("datepicker-hover-text-color");
    }

    @include when(disabled) {
      color: getCssVar("text-color-disabled");

      &:hover {
        cursor: not-allowed;
      }
    }
    & .#{$namespace}-icon {
      cursor: pointer;
      font-size: inherit;
    }
  }

  @include e(link-btn) {
    vertical-align: middle;
  }
}

.#{$namespace}-picker-panel *[slot="sidebar"],
.#{$namespace}-picker-panel__sidebar {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 110px;
  border-right: 1px solid getCssVar("datepicker-inner-border-color");
  box-sizing: border-box;
  padding-top: 6px;
  background-color: getCssVar("bg-color", "overlay");
  overflow: auto;
}

.#{$namespace}-picker-panel *[slot="sidebar"] + .#{$namespace}-picker-panel__body,
.#{$namespace}-picker-panel__sidebar + .#{$namespace}-picker-panel__body {
  margin-left: 110px;
}
