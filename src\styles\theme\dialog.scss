@use "sass:map";

@use "mixins/mixins" as *;
@use "mixins/utils" as *;
@use "mixins/var" as *;
@use "common/var" as *;
@use "common/popup" as *;

@include b(dialog) {
  @include set-component-css-var("dialog", $dialog);

  position: relative;
  margin: var(#{getCssVarName("dialog-margin-top")}, 15vh) auto 50px;
  background: getCssVar("dialog", "bg-color");
  border-radius: getCssVar("dialog", "border-radius");
  box-shadow: getCssVar("dialog", "box-shadow");
  box-sizing: border-box;
  padding: getCssVar("dialog", "padding-primary");
  width: var(#{getCssVarName("dialog-width")}, 50%);
  overflow-wrap: break-word;

  &:focus {
    outline: none !important;
  }

  @include when(align-center) {
    margin: auto;
  }

  @include when(fullscreen) {
    @include set-css-var-value("dialog-width", 100%);
    @include set-css-var-value("dialog-margin-top", 0);

    margin-bottom: 0;
    height: 100%;
    overflow: auto;
  }

  @include e(wrapper) {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
    margin: 0;
  }

  @include when(draggable) {
    @include e(header) {
      cursor: move;
      user-select: none;
    }
  }

  @include e(header) {
    padding-bottom: getCssVar("dialog", "padding-primary");

    &.show-close {
      padding-right: calc(getCssVar("dialog", "padding-primary") + var(#{getCssVarName("message-close-size")}, map.get($message, "close-size")));
    }
  }

  @include e(headerbtn) {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0;
    width: 48px;
    height: 48px;
    background: transparent;
    border: none;
    outline: none;
    cursor: pointer;
    font-size: var(#{getCssVarName("message-close-size")}, map.get($message, "close-size"));

    .#{$namespace}-dialog__close {
      color: getCssVar("color", "info");
      font-size: inherit;
    }

    &:focus,
    &:hover {
      .#{$namespace}-dialog__close {
        color: getCssVar("color", "primary");
      }
    }
  }

  @include e(title) {
    line-height: getCssVar("dialog-font-line-height");
    font-size: getCssVar("dialog-title-font-size");
    color: getCssVar("text-color", "primary");
  }

  @include e(body) {
    color: getCssVar("text-color", "regular");
    font-size: getCssVar("dialog-content-font-size");
  }

  @include e(footer) {
    padding-top: getCssVar("dialog", "padding-primary");
    text-align: right;
    box-sizing: border-box;
  }

  // 内容居中布局
  @include m(center) {
    text-align: center;

    @include e(body) {
      text-align: initial;
    }

    @include e(footer) {
      text-align: inherit;
    }
  }
}

.#{$namespace}-overlay-dialog {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
}

.dialog-fade-enter-active {
  animation: modal-fade-in getCssVar("transition-duration");
  .#{$namespace}-overlay-dialog {
    animation: dialog-fade-in getCssVar("transition-duration");
  }
}

.dialog-fade-leave-active {
  animation: modal-fade-out getCssVar("transition-duration");
  .#{$namespace}-overlay-dialog {
    animation: dialog-fade-out getCssVar("transition-duration");
  }
}

@keyframes dialog-fade-in {
  0% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes dialog-fade-out {
  0% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
  100% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
}

@keyframes modal-fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes modal-fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
