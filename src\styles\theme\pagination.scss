@use "mixins/mixins" as *;
@use "mixins/utils" as *;
@use "mixins/var" as *;
@use "common/var" as *;

@mixin pagination-button {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: getCssVar("pagination-font-size");
  min-width: getCssVar("pagination-button-width");
  height: getCssVar("pagination-button-height");
  line-height: getCssVar("pagination-button-height");
  color: getCssVar("pagination-button-color");
  background: getCssVar("pagination-bg-color");
  padding: 0 4px;
  border: none;
  border-radius: getCssVar("pagination-border-radius");
  cursor: pointer;
  text-align: center;
  box-sizing: border-box;

  * {
    pointer-events: none;
  }

  &:focus {
    outline: none;
  }

  &:hover {
    color: getCssVar("pagination-hover-color");
  }

  &.is-active {
    color: getCssVar("pagination-hover-color");
    cursor: default;
    font-weight: bold;

    &.is-disabled {
      font-weight: bold;
      color: getCssVar("text-color", "secondary");
    }
  }

  &:disabled,
  &.is-disabled {
    color: getCssVar("pagination-button-disabled-color");
    background-color: getCssVar("pagination-button-disabled-bg-color");
    cursor: not-allowed;
  }

  &:focus-visible {
    outline: 1px solid getCssVar("pagination-hover-color");
    outline-offset: -1px;
  }
}

@include b(pagination) {
  @include set-component-css-var("pagination", $pagination);

  white-space: nowrap;
  color: getCssVar("pagination-text-color");
  font-size: getCssVar("pagination-font-size");
  font-weight: normal;
  display: flex;
  align-items: center;

  .#{$namespace}-input__inner {
    text-align: center;
    -moz-appearance: textfield;
  }

  .#{$namespace}-select {
    width: 128px;
  }

  button {
    @include pagination-button;
  }

  .btn-prev,
  .btn-next {
    .#{$namespace}-icon {
      display: block;
      font-size: 12px;
      font-weight: bold;
      width: inherit;
    }
  }

  & > * {
    @include when(first) {
      margin-left: 0 !important;
    }
    @include when(last) {
      margin-right: 0 !important;
    }
  }

  .btn-prev {
    margin-left: getCssVar("pagination-item-gap");
  }

  @include e(sizes) {
    margin-left: getCssVar("pagination-item-gap");
    font-weight: normal;
    color: getCssVar("text-color", "regular");
  }

  @include e(total) {
    margin-left: getCssVar("pagination-item-gap");
    font-weight: normal;
    color: getCssVar("text-color", "regular");

    &[disabled="true"] {
      color: getCssVar("text-color", "placeholder");
    }
  }

  @include e(jump) {
    display: flex;
    align-items: center;
    margin-left: getCssVar("pagination-item-gap");
    font-weight: normal;
    color: getCssVar("text-color", "regular");

    &[disabled="true"] {
      color: getCssVar("text-color", "placeholder");
    }

    @include e(goto) {
      margin-right: 8px;
    }

    @include e(editor) {
      text-align: center;
      box-sizing: border-box;

      &.#{$namespace}-input {
        width: 56px;
      }

      .#{$namespace}-input__inner::-webkit-inner-spin-button,
      .#{$namespace}-input__inner::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
    }

    @include e(classifier) {
      margin-left: 8px;
    }
  }

  @include e(rightwrapper) {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  @include when(background) {
    .btn-prev,
    .btn-next,
    .#{$namespace}-pager li {
      margin: 0 4px;
      background-color: getCssVar("pagination-button-bg-color");

      &.is-active {
        background-color: getCssVar("color-primary");
        color: getCssVar("color-white");
      }

      &:disabled,
      &.is-disabled {
        color: getCssVar("text-color", "placeholder");
        background-color: getCssVar("disabled-bg-color");

        &.is-active {
          color: getCssVar("text-color", "secondary");
          background-color: getCssVar("fill-color", "dark");
        }
      }
    }

    .btn-prev {
      margin-left: getCssVar("pagination-item-gap");
    }
  }

  @include m(small) {
    .btn-prev,
    .btn-next,
    .#{$namespace}-pager li {
      height: getCssVar("pagination-button-height-small");
      line-height: getCssVar("pagination-button-height-small");
      font-size: getCssVar("pagination-font-size-small");
      min-width: getCssVar("pagination-button-width-small");
    }

    span:not([class*="suffix"]),
    button {
      font-size: getCssVar("pagination-font-size-small");
    }

    .#{$namespace}-select {
      width: 100px;
    }
  }
}

@include b(pager) {
  user-select: none;
  list-style: none;
  font-size: 0;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;

  li {
    @include pagination-button;
  }
}
