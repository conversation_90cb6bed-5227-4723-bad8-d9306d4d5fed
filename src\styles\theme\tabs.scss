@use "mixins/mixins" as *;
@use "common/var" as *;

@include b(tabs) {
  @include set-component-css-var("tabs", $tabs);

  @include e(header) {
    padding: 0;
    position: relative;
    margin: 0 0 15px;
  }
  @include e(active-bar) {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    background-color: getCssVar("color-primary");
    z-index: 1;
    transition:
      width getCssVar("transition-duration") getCssVar("transition-function-ease-in-out-bezier"),
      transform getCssVar("transition-duration") getCssVar("transition-function-ease-in-out-bezier");
    list-style: none;
  }
  @include e(new-tab) {
    display: flex;
    align-items: center;
    justify-content: center;
    float: right;
    border: 1px solid getCssVar("border-color");
    height: 20px;
    width: 20px;
    line-height: 20px;
    margin: 10px 0 10px 10px;
    border-radius: 3px;
    text-align: center;
    font-size: 12px;
    color: getCssVar("text-color", "primary");
    cursor: pointer;
    transition: all 0.15s;

    .is-icon-plus {
      height: inherit;
      width: inherit;
      transform: scale(0.8, 0.8);

      svg {
        vertical-align: middle;
      }
    }

    &:hover {
      color: getCssVar("color-primary");
    }
  }
  @include e(nav-wrap) {
    overflow: hidden;
    margin-bottom: -1px;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 2px;
      background-color: getCssVar("border-color-light");
      z-index: getCssVar("index-normal");
    }

    @include when(scrollable) {
      padding: 0 20px;
      box-sizing: border-box;
    }
  }
  @include e(nav-scroll) {
    overflow: hidden;
  }
  @include e((nav-next, nav-prev)) {
    position: absolute;
    cursor: pointer;
    line-height: getCssVar("tabs", "header-height");
    font-size: 12px;
    color: getCssVar("text-color", "secondary");
    width: 20px;
    text-align: center;
    > .#{$namespace}-icon {
      vertical-align: middle;
    }
    @include when(disabled) {
      color: getCssVar("disabled-text-color");
      cursor: not-allowed;
    }
  }
  @include e(nav-next) {
    right: 0;
  }
  @include e(nav-prev) {
    left: 0;
  }
  @include e(nav) {
    display: flex;
    white-space: nowrap;
    position: relative;
    transition: transform getCssVar("transition-duration");
    float: left;
    z-index: calc(#{getCssVar("index-normal")} + 1);

    @include when(stretch) {
      min-width: 100%;
      display: flex;
      & > * {
        flex: 1;
        text-align: center;
      }
    }
  }
  @include e(item) {
    padding: 0 20px;
    height: getCssVar("tabs", "header-height");
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    list-style: none;
    font-size: getCssVar("font-size-base");
    font-weight: 500;
    color: getCssVar("text-color", "primary");
    position: relative;

    &:focus,
    &:focus:active {
      outline: none;
    }

    &:focus-visible {
      box-shadow: 0 0 2px 2px getCssVar("color-primary") inset;
      border-radius: 3px;
    }

    & .is-icon-close {
      border-radius: 50%;
      text-align: center;
      transition: all getCssVar("transition-duration") getCssVar("transition-function-ease-in-out-bezier");
      margin-left: 5px;

      &:before {
        transform: scale(0.9);
        display: inline-block;
      }

      &:hover {
        background-color: getCssVar("text-color", "placeholder");
        color: $color-white;
      }
    }

    @include when(active) {
      color: getCssVar("color-primary");
    }

    &:hover {
      color: getCssVar("color-primary");
      cursor: pointer;
    }

    @include when(disabled) {
      color: getCssVar("disabled-text-color");
      cursor: not-allowed;
    }
  }
  @include e(content) {
    overflow: hidden;
    position: relative;
  }
  @include m(card) {
    > .#{$namespace}-tabs__header {
      border-bottom: 1px solid getCssVar("border-color-light");
      height: getCssVar("tabs", "header-height");
    }
    > .#{$namespace}-tabs__header .#{$namespace}-tabs__nav-wrap::after {
      content: none;
    }
    > .#{$namespace}-tabs__header .#{$namespace}-tabs__nav {
      border: 1px solid getCssVar("border-color-light");
      border-bottom: none;
      border-radius: 4px 4px 0 0;
      box-sizing: border-box;
    }
    > .#{$namespace}-tabs__header .#{$namespace}-tabs__active-bar {
      display: none;
    }
    > .#{$namespace}-tabs__header .#{$namespace}-tabs__item .is-icon-close {
      position: relative;
      font-size: 12px;
      width: 0;
      height: 14px;
      overflow: hidden;
      right: -2px;
      transform-origin: 100% 50%;
    }
    > .#{$namespace}-tabs__header .#{$namespace}-tabs__item {
      border-bottom: 1px solid transparent;
      border-left: 1px solid getCssVar("border-color-light");
      transition:
        color getCssVar("transition-duration") getCssVar("transition-function-ease-in-out-bezier"),
        padding getCssVar("transition-duration") getCssVar("transition-function-ease-in-out-bezier");

      &:first-child {
        border-left: none;
      }

      &.is-closable {
        &:hover {
          padding-left: 13px;
          padding-right: 13px;

          & .is-icon-close {
            width: 14px;
          }
        }
      }

      &.is-active {
        border-bottom-color: getCssVar("bg-color");

        &.is-closable {
          padding-left: 20px;
          padding-right: 20px;

          .is-icon-close {
            width: 14px;
          }
        }
      }
    }
  }
  @include m(border-card) {
    background: getCssVar("bg-color", "overlay");
    border: 1px solid getCssVar("border-color");

    > .#{$namespace}-tabs__content {
      padding: 15px;
    }
    > .#{$namespace}-tabs__header {
      background-color: getCssVar("fill-color", "light");
      border-bottom: 1px solid getCssVar("border-color-light");
      margin: 0;
    }
    > .#{$namespace}-tabs__header .#{$namespace}-tabs__nav-wrap::after {
      content: none;
    }
    > .#{$namespace}-tabs__header .#{$namespace}-tabs__item {
      transition: all getCssVar("transition-duration") getCssVar("transition-function-ease-in-out-bezier");
      border: 1px solid transparent;
      margin-top: -1px;
      color: getCssVar("text-color", "secondary");

      &:first-child {
        margin-left: -1px;
      }

      & + .#{$namespace}-tabs__item {
        margin-left: -1px;
      }

      &.is-active {
        color: getCssVar("color-primary");
        background-color: getCssVar("bg-color", "overlay");
        border-right-color: getCssVar("border-color");
        border-left-color: getCssVar("border-color");
      }

      &:not(.is-disabled):hover {
        color: getCssVar("color-primary");
      }

      &.is-disabled {
        color: getCssVar("disabled-text-color");
      }
    }

    > .#{$namespace}-tabs__header .is-scrollable .#{$namespace}-tabs__item:first-child {
      margin-left: 0;
    }
  }
  @include m((top, bottom)) {
    .#{$namespace}-tabs__item.is-top:nth-child(2),
    .#{$namespace}-tabs__item.is-bottom:nth-child(2) {
      padding-left: 0;
    }
    .#{$namespace}-tabs__item.is-top:last-child,
    .#{$namespace}-tabs__item.is-bottom:last-child {
      padding-right: 0;
    }

    &.#{$namespace}-tabs--border-card,
    &.#{$namespace}-tabs--card,
    .#{$namespace}-tabs--left,
    .#{$namespace}-tabs--right {
      > .#{$namespace}-tabs__header {
        .#{$namespace}-tabs__item:nth-child(2) {
          padding-left: 20px;

          &:not(.is-active).is-closable:hover {
            padding-left: 13px;
          }
        }

        .#{$namespace}-tabs__item:last-child {
          padding-right: 20px;

          &:not(.is-active).is-closable:hover {
            padding-right: 13px;
          }
        }
      }
    }
  }
  @include m(bottom) {
    .#{$namespace}-tabs__header.is-bottom {
      margin-bottom: 0;
      margin-top: 10px;
    }
    &.#{$namespace}-tabs--border-card {
      .#{$namespace}-tabs__header.is-bottom {
        border-bottom: 0;
        border-top: 1px solid getCssVar("border-color");
      }

      .#{$namespace}-tabs__nav-wrap.is-bottom {
        margin-top: -1px;
        margin-bottom: 0;
      }

      .#{$namespace}-tabs__item.is-bottom:not(.is-active) {
        border: 1px solid transparent;
      }

      .#{$namespace}-tabs__item.is-bottom {
        margin: 0 -1px -1px;
      }
    }
  }
  @include m((left, right)) {
    overflow: hidden;

    .#{$namespace}-tabs__header.is-left,
    .#{$namespace}-tabs__header.is-right,
    .#{$namespace}-tabs__nav-wrap.is-left,
    .#{$namespace}-tabs__nav-wrap.is-right,
    .#{$namespace}-tabs__nav-scroll {
      height: 100%;
    }

    .#{$namespace}-tabs__active-bar.is-left,
    .#{$namespace}-tabs__active-bar.is-right {
      top: 0;
      bottom: auto;
      width: 2px;
      height: auto;
    }

    .#{$namespace}-tabs__nav-wrap.is-left,
    .#{$namespace}-tabs__nav-wrap.is-right {
      margin-bottom: 0;

      > .#{$namespace}-tabs__nav-prev,
      > .#{$namespace}-tabs__nav-next {
        height: 30px;
        line-height: 30px;
        width: 100%;
        text-align: center;
        cursor: pointer;

        i {
          transform: rotateZ(90deg);
        }
      }

      > .#{$namespace}-tabs__nav-prev {
        left: auto;
        top: 0;
      }

      > .#{$namespace}-tabs__nav-next {
        right: auto;
        bottom: 0;
      }

      &.is-scrollable {
        padding: 30px 0;
      }

      &::after {
        height: 100%;
        width: 2px;
        bottom: auto;
        top: 0;
      }
    }

    .#{$namespace}-tabs__nav.is-left,
    .#{$namespace}-tabs__nav.is-right {
      flex-direction: column;
    }

    .#{$namespace}-tabs__item.is-left {
      justify-content: flex-end;
    }

    .#{$namespace}-tabs__item.is-right {
      justify-content: flex-start;
    }
  }
  @include m(left) {
    .#{$namespace}-tabs__header.is-left {
      float: left;
      margin-bottom: 0;
      margin-right: 10px;
    }
    .#{$namespace}-tabs__nav-wrap.is-left {
      margin-right: -1px;

      &::after {
        left: auto;
        right: 0;
      }
    }
    .#{$namespace}-tabs__active-bar.is-left {
      right: 0;
      left: auto;
    }
    .#{$namespace}-tabs__item.is-left {
      text-align: right;
    }

    &.#{$namespace}-tabs--card {
      .#{$namespace}-tabs__active-bar.is-left {
        display: none;
      }

      .#{$namespace}-tabs__item.is-left {
        border-left: none;
        border-right: 1px solid getCssVar("border-color-light");
        border-bottom: none;
        border-top: 1px solid getCssVar("border-color-light");
        text-align: left;
      }

      .#{$namespace}-tabs__item.is-left:first-child {
        border-right: 1px solid getCssVar("border-color-light");
        border-top: none;
      }

      .#{$namespace}-tabs__item.is-left.is-active {
        border: 1px solid getCssVar("border-color-light");
        border-right-color: #fff;
        border-left: none;
        border-bottom: none;

        &:first-child {
          border-top: none;
        }

        &:last-child {
          border-bottom: none;
        }
      }

      .#{$namespace}-tabs__nav {
        border-radius: 4px 0 0 4px;
        border-bottom: 1px solid getCssVar("border-color-light");
        border-right: none;
      }

      .#{$namespace}-tabs__new-tab {
        float: none;
      }
    }

    &.#{$namespace}-tabs--border-card {
      .#{$namespace}-tabs__header.is-left {
        border-right: 1px solid getCssVar("border-color");
      }

      .#{$namespace}-tabs__item.is-left {
        border: 1px solid transparent;
        margin: -1px 0 -1px -1px;

        &.is-active {
          border-color: transparent;
          border-top-color: rgb(209, 219, 229);
          border-bottom-color: rgb(209, 219, 229);
        }
      }
    }
  }
  @include m(right) {
    .#{$namespace}-tabs__header.is-right {
      float: right;
      margin-bottom: 0;
      margin-left: 10px;
    }

    .#{$namespace}-tabs__nav-wrap.is-right {
      margin-left: -1px;

      &::after {
        left: 0;
        right: auto;
      }
    }

    .#{$namespace}-tabs__active-bar.is-right {
      left: 0;
    }

    &.#{$namespace}-tabs--card {
      .#{$namespace}-tabs__active-bar.is-right {
        display: none;
      }

      .#{$namespace}-tabs__item.is-right {
        border-bottom: none;
        border-top: 1px solid getCssVar("border-color-light");
      }

      .#{$namespace}-tabs__item.is-right:first-child {
        border-left: 1px solid getCssVar("border-color-light");
        border-top: none;
      }

      .#{$namespace}-tabs__item.is-right.is-active {
        border: 1px solid getCssVar("border-color-light");
        border-left-color: #fff;
        border-right: none;
        border-bottom: none;

        &:first-child {
          border-top: none;
        }

        &:last-child {
          border-bottom: none;
        }
      }

      .#{$namespace}-tabs__nav {
        border-radius: 0 4px 4px 0;
        border-bottom: 1px solid getCssVar("border-color-light");
        border-left: none;
      }
    }
    &.#{$namespace}-tabs--border-card {
      .#{$namespace}-tabs__header.is-right {
        border-left: 1px solid getCssVar("border-color");
      }

      .#{$namespace}-tabs__item.is-right {
        border: 1px solid transparent;
        margin: -1px -1px -1px 0;

        &.is-active {
          border-color: transparent;
          border-top-color: rgb(209, 219, 229);
          border-bottom-color: rgb(209, 219, 229);
        }
      }
    }
  }
}

.slideInRight-transition,
.slideInLeft-transition {
  display: inline-block;
}

.slideInRight-enter {
  animation: slideInRight-enter getCssVar("transition-duration");
}

.slideInRight-leave {
  position: absolute;
  left: 0;
  right: 0;
  animation: slideInRight-leave getCssVar("transition-duration");
}

.slideInLeft-enter {
  animation: slideInLeft-enter getCssVar("transition-duration");
}

.slideInLeft-leave {
  position: absolute;
  left: 0;
  right: 0;
  animation: slideInLeft-leave getCssVar("transition-duration");
}

@keyframes slideInRight-enter {
  0% {
    opacity: 0;
    transform-origin: 0 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform-origin: 0 0;
    transform: translateX(0);
  }
}

@keyframes slideInRight-leave {
  0% {
    transform-origin: 0 0;
    transform: translateX(0);
    opacity: 1;
  }

  100% {
    transform-origin: 0 0;
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes slideInLeft-enter {
  0% {
    opacity: 0;
    transform-origin: 0 0;
    transform: translateX(-100%);
  }

  to {
    opacity: 1;
    transform-origin: 0 0;
    transform: translateX(0);
  }
}

@keyframes slideInLeft-leave {
  0% {
    transform-origin: 0 0;
    transform: translateX(0);
    opacity: 1;
  }

  100% {
    transform-origin: 0 0;
    transform: translateX(-100%);
    opacity: 0;
  }
}
