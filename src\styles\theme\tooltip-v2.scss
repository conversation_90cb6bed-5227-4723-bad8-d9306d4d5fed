@use "mixins/mixins" as *;
@use "mixins/var" as *;
@use "common/var" as *;

@include b(tooltip-v2) {
  @include e(content) {
    --#{$namespace}-tooltip-v2-padding: 5px 10px;
    --#{$namespace}-tooltip-v2-border-radius: 4px;

    @include css-var-from-global(("tooltip-v2", "border-color"), ("border-color"));

    border-radius: getCssVar("tooltip-v2-border-radius");
    color: getCssVar("color-black");
    background-color: getCssVar("color-white");
    padding: getCssVar("tooltip-v2-padding");
    border: 1px solid getCssVar("border-color");

    $content-selector: &;

    $sides: (
      "top": "bottom",
      "bottom": "top",
      "left": "right",
      "right": "left",
    );

    @include e(arrow) {
      position: absolute;
      color: getCssVar("color-white");
      width: var(--#{$namespace}-tooltip-v2-arrow-width);
      height: var(--#{$namespace}-tooltip-v2-arrow-height);
      pointer-events: none;
      left: var(--#{$namespace}-tooltip-v2-arrow-x);
      top: var(--#{$namespace}-tooltip-v2-arrow-y);

      &::before {
        content: "";
        width: 0;
        height: 0;
        border: var(--#{$namespace}-tooltip-v2-arrow-border-width) solid transparent;
        position: absolute;
      }

      &::after {
        content: "";
        width: 0;
        height: 0;
        border: var(--#{$namespace}-tooltip-v2-arrow-border-width) solid transparent;
        position: absolute;
      }

      @each $side, $opposite in $sides {
        #{$content-selector}[data-side^="#{$side}"] & {
          #{$opposite}: 0;
        }

        #{$content-selector}[data-side^="#{$side}"] &::before {
          border-#{$side}-color: var(--#{$namespace}-color-white);
          border-#{$side}-width: var(--#{$namespace}-tooltip-v2-arrow-border-width);
          border-#{$opposite}: 0;
          #{$side}: calc(100% - 1px);
        }

        #{$content-selector}[data-side^="#{$side}"] &::after {
          border-#{$side}-color: var(--#{$namespace}-border-color);
          border-#{$side}-width: var(--#{$namespace}-tooltip-v2-arrow-border-width);
          border-#{$opposite}: 0;
          #{$side}: 100%;
          z-index: -1;
        }
      }
    }

    &.is-dark {
      --#{$namespace}-tooltip-v2-border-color: transparent;
      background-color: getCssVar("color-black");
      color: getCssVar("color-white");
      border-color: transparent;

      @include e(arrow) {
        background-color: getCssVar("color-black");
        border-color: transparent;
      }
    }
  }
}
