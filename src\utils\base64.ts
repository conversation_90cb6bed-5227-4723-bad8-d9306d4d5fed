const base64Table = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
const paddingString = "=";
const $__empty_zero__ = 0;

export function base64ToBuffer(base64: string) {
  const $base64 = base64.replaceAll(/[^A-Za-z0-9+/=]/g, "");
  const paddingIdx = $base64.indexOf(paddingString);
  const b64NoPadding = paddingIdx !== -1 ? $base64.slice(0, paddingIdx) : $base64;
  const bufferLength = Math.floor((b64NoPadding.length * 6) / 8);
  const result = new ArrayBuffer(bufferLength);
  const byteView = new Uint8Array(result);

  /**
   * 每 3 个字节一组，共 8bit * 3 = 24bit ，划分成 4 组
   * 每 6bit 代表一个编码后的索引值
   *
   * 64 = 4 * 6
   * 循环使用base64字符串中的所有字符，以4个字符为增量，以3个字节为增量
   */
  for (let i = 0, j = 0; i < b64NoPadding.length; i += 4, j += 3) {
    // 获取接下来4个base64字符的索引
    const char1 = base64Table.indexOf(b64NoPadding[i + 0]);
    const char2 = base64Table.indexOf(b64NoPadding[i + 1]);
    const char3 = base64Table.indexOf(b64NoPadding[i + 2]);
    const char4 = base64Table.indexOf(b64NoPadding[i + 3]);

    /**
     * 超出字符集抛错
     */
    // if ([char1, char2, char3, char4].includes(-1)) throw new Error("[Error 超出字符集]: 超出 Base64 中64个字符");

    /**
     * 计算接下来的3个字节
     * 0. char1按位与63 11 11 11 按位左移 2 加 char2按位与48 11 00 00 按位右移 4
     * 1. char2按位与15 00 11 11 按位左移 4 加 char3按位与60 11 11 00 按位右移 2
     * 2. char3按位与03 00 00 11 按位左移 6 加 char4按位与63 11 11 11 按位右移 0
     */
    byteView[j + 0] = (((char1 === -1 ? $__empty_zero__ : char1) & 0b00111111) << 2) + (((char2 === -1 ? $__empty_zero__ : char2) & 0b00110000) >> 4);
    byteView[j + 1] = (((char2 === -1 ? $__empty_zero__ : char2) & 0b00001111) << 4) + (((char3 === -1 ? $__empty_zero__ : char3) & 0b00111100) >> 2);
    byteView[j + 2] = (((char3 === -1 ? $__empty_zero__ : char3) & 0b00000011) << 6) + (((char4 === -1 ? $__empty_zero__ : char4) & 0b00111111) >> 0);
  }

  return result;
}

export function bufferToBase64(buffer: ArrayBufferLike) {
  // 每3个字节转换为4个base64字符，如果少于3个字节，则必须附加“=”字符作为填充
  const padding = 3 - (buffer.byteLength % 3);
  const byteView = new Uint8Array(buffer);
  let result = "";

  // 循环通过缓冲区中的所有字节，增量为3字节
  for (let i = 0; i < byteView.byteLength; i += 3) {
    /**
     * 获取接下来4个 base64 字符的索引
     * char1. byte 按位与00 00 00 00 00 按位左移 6 加 byte0按位与252 11 11 11 00 按位右移 2
     * char2. byte0按位与03 00 00 00 11 按位左移 4 加 byte1按位与240 11 11 00 00 按位右移 4
     * char3. byte1按位与15 00 00 11 11 按位左移 2 加 byte2按位与192 11 00 00 00 按位右移 6
     * char4. byte2按位与63 00 11 11 11 按位左移 0 加 byte 按位与000 00 00 00 00 按位右移 8
     */
    const char1 = (($__empty_zero__ & 0b00000000) << 6) + ((byteView[i + 0] & 0b11111100) >> 2);
    const char2 = ((byteView[i + 0] & 0b00000011) << 4) + ((byteView[i + 1] & 0b11110000) >> 4);
    const char3 = ((byteView[i + 1] & 0b00001111) << 2) + ((byteView[i + 2] & 0b11000000) >> 6);
    const char4 = ((byteView[i + 2] & 0b00111111) << 0) + (($__empty_zero__ & 0b00000000) >> 8);

    /**
     * 超出字符集抛错
     */
    // if (Math.max(char1, char2, char3, char4) >= 64) throw new Error("[Error 超出字符集]: 超出 Base64 中64个字符");

    result += `${base64Table[char1] || paddingString}${base64Table[char2] || paddingString}` + (base64Table[char3] || paddingString) + (base64Table[char4] || paddingString);
  }

  // 添加填充字符
  if (padding !== 3) result = result.slice(0, result.length - padding) + paddingString.repeat(padding);

  return result;
}

export function stringToBuffer(text: string): ArrayBuffer {
  const encoder = new TextEncoder();
  return encoder.encode(text).buffer;
}
export function bufferToString(buffer: ArrayBufferLike): string {
  const decoder = new TextDecoder("utf-8");
  return decoder.decode(buffer);
}
