import { getCurrentInstance, ref, nextTick, provide, h } from "vue";
import type { VNode, RendererNode, RendererElement, ComponentPublicInstance, CSSProperties } from "vue";
import { ElMessageBox, ElForm } from "element-plus";
import type { Action } from "element-plus";
import { EditorType } from "@/views/common/interface";

// @keydown.prevent.enter="handleAction('confirm')"
type BoxInstance = ComponentPublicInstance<{ handleAction: (action: Action) => void }, {}, {}, {}, {}, {}, {}, {}, false>;

export function bindFormBox(formItemRender: VNode<RendererNode, RendererElement, { [key: string]: any }>[], form: { title: string; customStyle?: CSSProperties } & Record<string, any>, callback: () => Promise<{ success: boolean; message: string } & Record<string, any>>) {
  const formRef = ref<InstanceType<typeof ElForm>>();
  const errorMessage = ref<string>("");
  const formRender = h({
    name: "MessageBoxForm",
    setup: () => {
      const ctx = getCurrentInstance()!;
      provide("#PARAMS", { "#TYPE": EditorType.Add });
      return () => h(ElForm, { ref: (vm) => (formRef.value = vm as InstanceType<typeof ElForm>), model: form, labelPosition: "top", onSubmit: (e: Event) => e.preventDefault(), onKeydown: ({ code }: KeyboardEvent) => code === "Enter" && ((ctx.root.proxy as BoxInstance).handleAction ? (ctx.root.proxy as BoxInstance).handleAction : () => undefined)("confirm") }, () => (errorMessage.value ? h("h3", { style: { fontSize: "var(--el-messagebox-font-size)", lineHeight: "1", color: "var(--el-color-error)" } }, errorMessage.value) : formItemRender));
    },
  });
  return ElMessageBox({
    title: form.title,
    message: formRender,
    showClose: false,
    showCancelButton: true,
    showConfirmButton: true,
    closeOnClickModal: false,
    customStyle: form.customStyle,
    // closeOnPressEscape: false,
    draggable: true,
    async beforeClose(action, instance, done) {
      if (action === "confirm") {
        await nextTick();
        if (formRef.value) {
          const valid = await new Promise((resolve) => (formRef.value as InstanceType<typeof ElForm>).validate(resolve));
          if (!valid) return;
        }
        instance.confirmButtonLoading = true;
        instance.confirmButtonText = "Loading...";
        try {
          const { success, message } = await callback();
          if (success) done();
          else throw Object.assign(new Error(message), { success });
        } catch (error) {
          if (error instanceof Error) {
            instance.showConfirmButton = false;
            errorMessage.value = error.message;
            instance.type = "error";
          }
        } finally {
          instance.confirmButtonLoading = false;
        }
      } else done();
    },
  });
}
