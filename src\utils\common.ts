import router from "@/router/index";
import { useNavTabs } from "@/stores/navTabs";
import { i18n } from "../lang";

export const sizes = JSON.parse(process.env["APP_PAGE_SIZES"]);

/**
 * 防抖
 * @param fn 执行函数
 * @param ms 间隔毫秒数
 */
export const debounce = (fn: Function, ms: number) => {
  return (...args: any[]) => {
    if (window.lazy) {
      clearTimeout(window.lazy);
    }
    window.lazy = setTimeout(() => {
      fn(...args);
    }, ms);
  };
};

/**
 * 页面按钮鉴权
 * @param name
 */
export const auth = (name: string) => {
  const navTabs = useNavTabs();
  if (navTabs.state.authNode.has(router.currentRoute.value.path)) {
    if (navTabs.state.authNode.get(router.currentRoute.value.path)!.some((v: string) => v == router.currentRoute.value.path + "/" + name)) {
      return true;
    }
  }
  return false;
};

/**
 * 根据当前时间生成问候语
 */
export const getGreet = () => {
  const now = new Date();
  const hour = now.getHours();
  let greet = "";

  if (hour < 5) {
    greet = i18n.global.t("utils.Late at night, pay attention to your body!");
  } else if (hour < 9) {
    greet = i18n.global.t("utils.good morning!") + i18n.global.t("utils.welcome back");
  } else if (hour < 12) {
    greet = i18n.global.t("utils.Good morning!") + i18n.global.t("utils.welcome back");
  } else if (hour < 14) {
    greet = i18n.global.t("utils.Good noon!") + i18n.global.t("utils.welcome back");
  } else if (hour < 18) {
    greet = i18n.global.t("utils.good afternoon") + i18n.global.t("utils.welcome back");
  } else if (hour < 24) {
    greet = i18n.global.t("utils.Good evening") + i18n.global.t("utils.welcome back");
  } else {
    greet = i18n.global.t("utils.Hello!") + i18n.global.t("utils.welcome back");
  }
  return greet;
};
