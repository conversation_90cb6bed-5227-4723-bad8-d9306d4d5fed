import { i18n } from "@/lang/index";
import { h } from "vue";
import moment from "moment";
export const timeFormats = (dateTime: string | number | null = null, fmt = "yyyy-mm-dd hh:MM") => {
  if (dateTime == "none") return i18n.global.t("glob.none");
  if (!dateTime) dateTime = Number(new Date());
  if (dateTime.toString().length === 10) {
    dateTime = +dateTime * 1000;
  }

  const date = new Date(dateTime);
  let ret;
  const opt: anyObj = {
    "y+": date.getFullYear().toString(), // 年
    "m+": (date.getMonth() + 1).toString(), // 月
    "d+": date.getDate().toString(), // 日
    "h+": date.getHours().toString(), // 时
    "M+": date.getMinutes().toString(), // 分
    "s+": date.getSeconds().toString(), // 秒
  };
  for (const k in opt) {
    ret = new RegExp("(" + k + ")").exec(fmt);
    if (ret) {
      fmt = fmt.replace(ret[1], ret[1].length == 1 ? opt[k] : padStart(opt[k], ret[1].length, "0"));
    }
  }
  return fmt;
};
/*
 * 格式化时间戳
 */
export const timeFormat = (dateTime: string | number | null = null, fmt = "yyyy-mm-dd hh:MM:ss") => {
  if (dateTime == "none") return i18n.global.t("glob.none");
  if (!dateTime) dateTime = Number(new Date());
  if (dateTime.toString().length === 10) {
    dateTime = +dateTime * 1000;
  }

  const date = new Date(dateTime);
  let ret;
  const opt: anyObj = {
    "y+": date.getFullYear().toString(), // 年
    "m+": (date.getMonth() + 1).toString(), // 月
    "d+": date.getDate().toString(), // 日
    "h+": date.getHours().toString(), // 时
    "M+": date.getMinutes().toString(), // 分
    "s+": date.getSeconds().toString(), // 秒
  };
  for (const k in opt) {
    ret = new RegExp("(" + k + ")").exec(fmt);
    if (ret) {
      fmt = fmt.replace(ret[1], ret[1].length == 1 ? opt[k] : padStart(opt[k], ret[1].length, "0"));
    }
  }
  return fmt;
};

/*
 * 字符串补位
 */
const padStart = (str: string, maxLength: number, fillString = " ") => {
  if (str.length >= maxLength) return str;

  const fillLength = maxLength - str.length;
  let times = Math.ceil(fillLength / fillString.length);
  while ((times >>= 1)) {
    fillString += fillString;
    if (times === 1) {
      fillString += fillString;
    }
  }
  return fillString.slice(0, fillLength) + str;
};

export function formatterDate(time: string) {
  try {
    if (!time) throw new Error("Error");
    const date = moment(time, "YYYY-MM-DD HH:mm:ss.SSS");
    if (date.isValid()) {
      const formatDate = date.format("YYYY-MM-DD HH:mm:ss");
      return h("span", { title: formatDate, class: ["el-table-v2__cell-text"] }, formatDate);
    } else throw new Error("Error");
  } catch (error) {
    return h("span", { style: { color: "var(--el-text-color-disabled)" }, title: i18n.global.t("glob.none") }, "--");
  }
}
export function formatDateToString(time: string) {
  try {
    if (!time) throw new Error("Error");
    const date = moment(time, "YYYY-MM-DD HH:mm:ss.SSS");
    if (date.isValid()) {
      const formatDate = date.format("YYYY-MM-DD HH:mm:ss");
      return formatDate;
    } else throw new Error("Error");
  } catch (error) {
    return "--";
  }
}
