import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";

export default function getUserInfo() {
  const siteConfig = useSiteConfig();
  switch (siteConfig.current) {
    case process.env["APP_SUPER_PLATFORM"]:
      return useSuperInfo();
    case process.env["APP_ADMIN_PLATFORM"]:
      return useAdminInfo();
    case process.env["APP_USERS_PLATFORM"]:
      return useUsersInfo();
    default:
      throw new Error("[User Init Error] NotFind Site!");
  }
}
