export async function bindCanvasImage(ctx: CanvasRenderingContext2D, imgData: Blob | string | unknown) {
  ctx.canvas.width = parseInt(window.getComputedStyle(ctx.canvas).getPropertyValue("width"));
  ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
  ctx.font = "10px sans-serif";
  ctx.fillStyle = "red";
  try {
    const { image } = await bindImage(new Image(ctx.canvas.width, ctx.canvas.height), imgData);
    ctx.drawImage(image, 0, 0, (ctx.canvas.width = image.width), (ctx.canvas.height = image.height));
  } catch (error) {
    ctx.fillText(error instanceof Error ? error.message : "加载失败...", ctx.canvas.width / 2 - 40, ctx.canvas.height / 2 + 4);
  }
}

export function bindImage(image: HTMLImageElement, src: Blob | string | unknown): Promise<{ image: HTMLImageElement }> {
  return new Promise<{ image: HTMLImageElement }>((resolve, reject) => {
    try {
      if (src instanceof Blob) {
        const url = URL.createObjectURL(src);
        resultImage(image, url)
          .then((res) => resolve(res))
          .catch((err) => reject(err))
          .finally(() => URL.revokeObjectURL(url));
      } else if (typeof src === "string") {
        resultImage(image, src)
          .then((res) => resolve(res))
          .catch((err) => reject(err));
      } else {
        reject(Object.assign(new Error("未知数据源"), { image: image }));
      }
    } catch (error) {
      if (error instanceof Error) reject(Object.assign(error, { image: image }));
      else reject(Object.assign(new Error("解析错误"), { image: image }));
    }
  });
}

export function resultImage(image: HTMLImageElement, src: string): Promise<{ image: HTMLImageElement }> {
  return new Promise<{ image: HTMLImageElement }>((resolve, reject) => {
    if (image.src === src) resolve({ image });
    else {
      Object.assign(image, {
        src: src,
        onload: () => resolve({ image }),
        onerror: () => reject(Object.assign(new Error("解析错误"), { image: image })),
      });
    }
  });
}
