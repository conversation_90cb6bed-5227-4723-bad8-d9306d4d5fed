import "@/styles/loading.scss";

const element = Object.assign(document.createElement("div"), {
  className: "block-loading",
  innerHTML: `
  <div class="block-loading-box">
      <div class="block-loading-box-warp">
          <div class="block-loading-box-item"></div>
          <div class="block-loading-box-item"></div>
          <div class="block-loading-box-item"></div>
          <div class="block-loading-box-item"></div>
          <div class="block-loading-box-item"></div>
          <div class="block-loading-box-item"></div>
          <div class="block-loading-box-item"></div>
          <div class="block-loading-box-item"></div>
          <div class="block-loading-box-item"></div>
      </div>
  </div>
`,
});

export const loading = {
  show: () => {
    if (!document.body.contains(element)) document.body.insertBefore(element, document.body.childNodes[0]);
  },
  hide: () => {
    if (document.body.contains(element)) document.body.removeChild(element);
  },
};
