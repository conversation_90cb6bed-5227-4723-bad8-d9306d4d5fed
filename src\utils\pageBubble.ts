import type { ObjectDirective } from "vue";
import { useEventListener } from "@vueuse/core";
// 页面气泡效果
class Circle {
  pos: { x: number; y: number };
  alpha: number;
  scale: number;
  velocity: number;
  draw: () => void;
  constructor(ctx: CanvasRenderingContext2D) {
    this.pos = { x: Math.random() * window.innerWidth, y: window.innerHeight + Math.random() * 100 };
    this.alpha = 0.1 + Math.random() * 0.3;
    this.scale = 0.1 + Math.random() * 0.3;
    this.velocity = Math.random();
    this.draw = function () {
      this.pos.y -= this.velocity;
      this.alpha -= 0.0005;
      ctx.beginPath();
      ctx.arc(this.pos.x, this.pos.y, this.scale * 10, 0, 2 * Math.PI, false);
      ctx.fillStyle = "rgba(114,124,245," + this.alpha + ")";
      ctx.fill();
    };
  }
}

const circles: Circle[] = [];
let stop: (() => undefined) | undefined = undefined;

export const vBubble: ObjectDirective<HTMLDivElement> = {
  // // 在绑定元素的 attribute 前
  // // 或事件监听器应用前调用
  // created(el, binding, vnode, prevVnode) {},
  // // 在元素被插入到 DOM 前调用
  // beforeMount(el, binding, vnode, prevVnode) {},
  // // 在绑定元素的父组件
  // // 及他自己的所有子节点都挂载完成后调用
  mounted(el) {
    el.style.width = "100%";
    el.style.height = "100%";
    el.style.position = "fixed";
    el.style.overflow = "hidden";

    const element = el.getElementsByTagName("canvas").namedItem("bubble-canvas");
    const canvas = element || document.createElement("canvas");

    canvas.id = "bubble-canvas";
    canvas.className = "bubble-canvas";
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    if (!el.contains(canvas)) el.appendChild(canvas);

    const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
    for (let x = 0; x < window.innerWidth * 0.5; x++) {
      const c = new Circle(ctx);
      circles.push(c);
    }
    (function animate() {
      ctx.clearRect(0, 0, window.innerWidth, window.innerHeight);
      for (const i in circles) circles[i].draw();
      const requestId = requestAnimationFrame(animate);
      const removeEvent = useEventListener("resize", () => {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
      });
      stop = () => {
        cancelAnimationFrame(requestId);
        removeEvent();
        return undefined;
      };
    })();
  },
  beforeUnmount() {
    if (typeof stop === "function") stop = stop();
  },
};

export function removeListeners() {
  if (typeof stop === "function") stop = stop();
}
