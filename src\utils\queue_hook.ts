import { onBeforeUnmount, nextTick, watch, type Ref, type ComputedRef } from "vue";

export default function generateQueueHook<T>(data: Ref<T> | ComputedRef<T> | (() => T), collback: (source: T) => boolean) {
  const stack: AbortController[] = [];
  class QueuePromise {
    signal: AbortSignal;
    abort: (reason?: any) => void;
    constructor(clear?: boolean) {
      if (clear) QueuePromise.clean();
      const controller = new AbortController();
      this.signal = controller.signal;
      this.abort = controller.abort;
      stack.push(controller);
      controller.signal.addEventListener("abort", () => stack.splice(stack.indexOf(controller), 1), { once: true });
      const unWatch = watch(
        data,
        async (loading) => {
          await nextTick();
          if (!collback(loading)) return;
          unWatch();
          if (!controller.signal.aborted) controller.abort();
        },
        { immediate: true }
      );
    }
    then(callback: () => any) {
      if (this.signal.aborted) callback();
      else this.signal.addEventListener("abort", () => callback(), { once: true });
    }
    appendController(controller: AbortController) {
      return new Promise<void>((resolve) => {
        if (controller.signal.aborted) return resolve();
        stack.push(controller);
        controller.signal.addEventListener("abort", () => resolve(void stack.splice(stack.indexOf(controller), 1)), { once: true });
        const unWatch = watch(
          data,
          async (loading) => {
            await nextTick();
            if (!collback(loading)) return;
            unWatch();
            if (!controller.signal.aborted) controller.abort();
          },
          { immediate: true }
        );
      });
    }
    static clean() {
      for (let i = 0; i < stack.length; i++) {
        const controller = stack[i];
        if (!controller.signal.aborted) controller.abort();
      }
    }
  }
  onBeforeUnmount(QueuePromise.clean);
  return QueuePromise;
}
