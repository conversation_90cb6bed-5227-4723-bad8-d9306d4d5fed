import NotFind from "@/views/common/error/404.vue";
import NotPower from "@/views/common/error/403.vue";
import MicroView from "@/layouts/common/micro-view/iframe.vue";
import router from "@/router/index";
import { isNavigationFailure, NavigationFailureType, RouteRecordRaw, RouteLocationNamedRaw, RouteComponent, RouteParamsRaw } from "vue-router";
import { ElMessage, ElNotification } from "element-plus";
// import microApp from "@micro-zoe/micro-app";
import { useConfig } from "@/stores/config";
import { useNavTabs } from "@/stores/navTabs";
// import { useMemberCenter } from "@/stores/memberCenter";
import { closeShade } from "@/utils/pageShade";
import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";
import { pathToRegexp, match } from "path-to-regexp";
import { i18n } from "@/lang/index";
import getUserInfo from "@/utils/getUserInfo";
import { appType, appTheme, type NavItem } from "@/api/application";
export { appType, appTheme, type NavItem };
// import type { MenuItem } from "@/api/application";
// import getComponent from "@/getComponent";
type permissionKey = typeof import("@/views/pages/permission") extends { [key: string]: infer T } ? T : never;

declare type Model<T> = Record<"default" | string, T>;
declare type Lazy<T> = () => Promise<T>;

const routerList: (() => void)[] = [];

/**
 * 导航失败有错误消息的路由push
 * @param to — 导航位置，同 router.push
 */
export const routePush = async (to: Partial<NavItem>) => {
  try {
    switch (to.type || appType.ROUTE) {
      case appType.LINK: {
        const url = new URL(`${to.url}`, location.origin);
        const link = document.createElement("a");
        try {
          const tryName =
            url.pathname
              .split("/")
              .filter((v) => v)
              .shift() || "";
          if (/^[0-9]+$/g.test(tryName)) {
            const tryTo = router.resolve({ name: tryName });
            if (tryTo.name !== tryName) new Error("[ not find name ]");
            link.href = `${tryTo.href}`;
            // link.href = `${router.options.history}${tryTo.fullPath}`;
          } else throw new Error("[ is link ]");
        } catch (error) {
          link.href = url.toString();
        }
        link.target = to.keepalive ? "_blank" : "_self";
        document.body.append(link);
        link.click();
        document.body.removeChild(link);
        break;
      }
      case appType.MENU:
      case appType.ROUTE:
      case appType.MICRO: {
        const failure = await new Promise((resolve) =>
          router
            .push({ name: to.name, ...(to.path ? { path: to.path } : {}) })
            .then(resolve)
            .catch(resolve)
        );

        if (failure instanceof Error) {
          /* 导航失败 */
          if (isNavigationFailure(failure, NavigationFailureType.aborted)) ElNotification({ type: "error", message: i18n.global.t("utils.Navigation failed, navigation guard intercepted!") });

          /* 导航重复 */
          // if (isNavigationFailure(failure, NavigationFailureType.duplicated)) ElNotification({ type: "warning", message: i18n.global.t("utils.Navigation failed, it is at the navigation target position!") });

          /* 导航取消 */
          if (isNavigationFailure(failure, NavigationFailureType.cancelled)) ElNotification({ type: "error", message: i18n.global.t("utils.Navigation failed, invalid route!") });
        }
        break;
      }
      default: {
        break;
      }
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    // eslint-disable-next-line no-console
    console.dir(error);
  }
};
/**
 * 打开侧边菜单
 * @param menu 菜单数据
 */
export const onClickMenu = (menu: Partial<NavItem>) => {
  switch (menu.type) {
    case appType.DIR:
      if (menu.children instanceof Array) {
        const route = getFirstRoute(menu.children);
        route && routePush(route);
      }
      break;
    case appType.MICRO:
    case appType.MENU:
    case appType.ROUTE:
    case appType.LINK:
      routePush(menu);
      break;
    default:
      ElNotification({ message: i18n.global.t("utils.Navigation failed, the menu type is unrecognized!"), type: "error" });
      break;
  }

  const config = useConfig();
  if (config.layout.shrink) {
    closeShade(() => {
      config.setLayout("menuCollapse", true);
    });
  }
};
/**
 * 获取第一个菜单
 */
export const getFirstRoute = (routes: NavItem[]): NavItem | undefined => {
  const routers = router.getRoutes();
  const routerPaths = routers.reduce<string[]>((p, c) => (c.name ? [...p, c.name as string] : p), []);

  let find: undefined | NavItem = undefined;
  for (const key in routes) {
    switch (routes[key].type) {
      case appType.DIR:
        if (!(routes[key].children instanceof Array)) continue;
        find = getFirstRoute(routes[key].children as NavItem[]);
        if (find) return find;
        continue;
      case appType.MICRO:
      case appType.MENU:
        if (routerPaths.indexOf(routes[key].name as string) === -1) continue;
        return routes[key];
      case appType.LINK:
      case appType.ROUTE:
      case appType.BUTTON:
        continue;
      default:
        continue;
    }
  }
  return find;
};

/**
 * 处理路由
 */
export const handleRoute = (routes: NavItem[], type: string) => {
  while (routerList.length) {
    const removeRoute = routerList.shift();
    removeRoute && removeRoute();
  }
  // console.log(routes);
  const model = require.context(`@/views/pages`, true, /index\.vue$/, "lazy");
  const viewsComponent = model.keys().reduce((p, c) => Object.assign(p, { [c.replace(/^\.\//, "")]: (): Lazy<RouteComponent> => model(c) }), {} as Record<string, Model<RouteComponent> | Lazy<RouteComponent>>);
  // eslint-disable-next-line no-console
  if (process.env["NODE_ENV"] === "development") console.log(Object.keys(viewsComponent).join("\n"));

  const menuRule = ((type: string) => {
    switch (type) {
      case superBaseRoute.name: {
        addRouteAll(viewsComponent, routes, superBaseRoute.name as string);
        const menuSuperBaseRoute = `${superBaseRoute.path as string}/`;
        return handleMenuRule(routes, menuSuperBaseRoute, menuSuperBaseRoute, superBaseRoute.name);
      }
      case adminBaseRoute.name: {
        addRouteAll(viewsComponent, routes, adminBaseRoute.name as string);
        const menuadminBaseRoute = `${adminBaseRoute.path as string}/`;
        return handleMenuRule(routes, menuadminBaseRoute, menuadminBaseRoute, adminBaseRoute.name);
      }
      case usersBaseRoute.name: {
        addRouteAll(viewsComponent, routes, usersBaseRoute.name as string);
        const menuusersBaseRoute = `${usersBaseRoute.path as string}/`;
        return handleMenuRule(routes, menuusersBaseRoute, menuusersBaseRoute, usersBaseRoute.name);
      }
      default:
        throw new Error("Error: Not Router Type");
    }
  })(type);

  function getTreeAllNames(menu: NavItem): string[] {
    const names: string[] = [menu.name as string];
    if (menu.children instanceof Array) {
      for (let index = 0; index < menu.children.length; index++) {
        names.push(...getTreeAllNames(menu.children[index]));
      }
    }
    return names;
  }
  // console.log(menuRule);

  // 更新stores中的路由菜单数据
  const navTabs = useNavTabs();
  navTabs.setTabsViewRoutes(menuRule.map((menu) => ({ ...menu, names: getTreeAllNames(menu) })));
};

/**
 * 获取菜单的paths
 */
export const getMenuPaths = (menus: RouteRecordRaw[]): string[] => {
  let menuPaths: string[] = [];
  menus.forEach((item) => {
    menuPaths.push(item.path);
    if (item.children && item.children.length > 0) {
      menuPaths = menuPaths.concat(getMenuPaths(item.children));
    }
  });
  return menuPaths;
};

/**
 * 菜单处理
 */
// export interface RouteLocation extends RouteLocationNamedRaw {
//   route: RouteLocationNamedRaw;
//   path: string;
//   url: string;
//   pathReg: RegExp;
//   children: RouteLocation[];
//   id: string;
//   title: string;
//   icon: string;
//   type: string;
//   permissions: string[];
//   keepalive: boolean;
//   enabled: boolean;
//   names: string[];
// }
const callRouteLocation = (routes: NavItem[], base: NavItem, extPath = ""): NavItem[] => {
  const $match = match<Record<string, string | string[]>>(base.path);
  const result: NavItem[] = [];
  for (let index = 0; index < routes.length; index++) {
    const route: NavItem = routes[index];
    const $url = new URL(`${base.url}${/^\//.test(route.path) ? "" : extPath ? `${extPath}/` : extPath}${route.path}`.replaceAll("//", "/"), location.origin);

    const params = ($match($url.pathname) || {}).params || {};
    for (const key in params) {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        if (params[key] instanceof Array) params[key] = (params[key] as string[]).join("").split("/");
      }
    }

    result.push({
      ...route,
      path: $url.pathname,
      icon: route.icon || useConfig().layout.menuDefaultIcon,
      children: callRouteLocation(route.children, base, `${extPath ? `${extPath}/` : extPath}${route.path}`.replaceAll("//", "/")),
      // url: $url.pathname,
      // path: $url.pathname,
      // pathReg: base.pathReg,
      // ...$route,
      // route: $route,
      // id: route.id,
      // title: route.title,
      // icon: route.icon || useConfig().layout.menuDefaultIcon,
      // keepalive: route.keepalive,
      // type: route.type,
      // permissions: route.permission,
      // enabled: route.enabled,
      // names: [],
      // children: callRouteLocation(route.children, base, `${extPath ? `${extPath}/` : extPath}${route.path}`.replaceAll("//", "/")),
    });
  }
  return result;
};
function decode(text: unknown) {
  try {
    return decodeURIComponent(`${text}`);
  } catch (err) {
    // eslint-disable-next-line no-console
    process.env.NODE_ENV !== "production" && console.warn(`Error decoding "${text}". Using original value`);
  }
  return `${text}`;
}
function parseQuery(search: string) {
  const query: import("vue-router").LocationQueryRaw = {};
  // avoid creating an object with an empty key and empty value
  // because of split('&')
  if (search === "" || search === "?") return query;
  const searchParams = (search[0] === "?" ? search.slice(1) : search).split("&");
  for (let i = 0; i < searchParams.length; ++i) {
    // pre decode the + into space
    const searchParam = searchParams[i].replace(/\+/g, " ");
    // allow the = character
    const eqPos = searchParam.indexOf("=");
    const key = decode(eqPos < 0 ? searchParam : searchParam.slice(0, eqPos));
    const value = eqPos < 0 ? null : decode(searchParam.slice(eqPos + 1));
    if (key in query) {
      // an extra variable for ts types
      let currentValue = query[key];
      if (!Array.isArray(currentValue)) currentValue = query[key] = [currentValue];
      currentValue.push(value);
    } else {
      query[key] = value;
    }
  }
  return query;
}

function parseURL(path: string, $url: string): Pick<NavItem, "url" | "hash" | "params" | "query" | "pattern"> {
  const url = new URL($url, location.origin);
  const params: RouteParamsRaw = {};
  let pattern = new RegExp("");
  if (path) {
    pattern = pathToRegexp(path);
    try {
      const matchFunction = match<Record<string, string | string[]>>(path);
      if (url.pathname) {
        const matchData = matchFunction(url.pathname);
        if (matchData && matchData.params) {
          const { params } = matchData;
          for (const key in params) {
            if (Object.prototype.hasOwnProperty.call(params, key)) {
              if (params[key] instanceof Array) params[key] = (params[key] as string[]).join("").split("/");
            }
          }
        }
      }
    } catch (error) {
      /*  */
    }
  }
  return { url: `${url.origin}${url.pathname}${url.hash}${url.search}`, hash: url.hash, params, query: parseQuery(url.search), pattern };
}

const handleMenuRule = (routes: NavItem[], pathPrefix = "/", parent = "/", module = "admin") => {
  const menuRule: NavItem[] = [];
  const authNode: string[] = [];
  const info = getUserInfo();
  for (const key in routes) {
    const permissionLegacy = (routes[key].permission instanceof Array ? routes[key].permission : []).filter((v) => v && typeof v === "string");
    const permissionGroups = (routes[key].permissionGroups instanceof Array ? routes[key].permissionGroups : []).filter((v) => (v instanceof Array ? v.length : 0));
    if (permissionGroups.length ? !permissionGroups.some((permission) => permission.every((v) => info.hasPermission(v as permissionKey))) : permissionLegacy.length && !info.hasPermission(...(permissionLegacy as permissionKey[]))) continue;

    switch (routes[key].type) {
      case appType.DIR: {
        if (!(routes[key].children instanceof Array)) break;
        routes[key].children = handleMenuRule(routes[key].children, pathPrefix, routes[key].name, module);
        if (!routes[key].children.length) break;

        menuRule.push({
          ...routes[key],
          ...parseURL(routes[key].path, `${routes[key].path}/`.replace(/\/*$/, "/")),
          icon: routes[key].icon || useConfig().layout.menuDefaultIcon,
          names: [routes[key].name as string],
        });
        break;
      }
      case appType.LINK: {
        menuRule.push({
          ...routes[key],
          path: "",
          ...parseURL("", routes[key].url),
          icon: routes[key].icon || useConfig().layout.menuDefaultIcon,
          names: [routes[key].name as string],
        });
        break;
      }
      case appType.MICRO: {
        const path = `${pathPrefix}${routes[key].path}:page(.*)*`;

        const _micro = {
          ...routes[key],
          path,
          children: callRouteLocation(routes[key].children instanceof Array ? routes[key].children : [], routes[key]),
          ...parseURL(path, `${pathPrefix.replace(/\/*$/, "")}/${routes[key].path.replace(/^\/*(.*)\/*$/g, "$1/")}`),
          icon: routes[key].icon || useConfig().layout.menuDefaultIcon,
          names: [routes[key].name as string],
        };
        menuRule.push(_micro);
        break;
      }
      case appType.MENU:
      case appType.ROUTE: {
        const path = `${pathPrefix}${routes[key].path}`;

        menuRule.push({
          ...routes[key],
          path,
          children: callRouteLocation(routes[key].children instanceof Array ? routes[key].children : [], routes[key]),
          ...parseURL(path, `${pathPrefix.replace(/\/*$/, "")}/${routes[key].path.replace(/^\/*(.*)\/*$/g, "$1/")}`),
          icon: routes[key].icon || useConfig().layout.menuDefaultIcon,
          names: [routes[key].name as string],
        });
        break;
      }
      default: {
        authNode.push(`${pathPrefix}${routes[key].name}`);
      }
    }
  }
  if (authNode.length) {
    switch (module) {
      case superBaseRoute.name:
        useNavTabs().setAuthNode(parent, authNode);
        break;
      case adminBaseRoute.name:
        useNavTabs().setAuthNode(parent, authNode);
        break;
      case usersBaseRoute.name:
        useNavTabs().setAuthNode(parent, authNode);
        break;
    }
  }
  return menuRule;
};

/**
 * 动态添加路由-带子路由
 */
export const addRouteAll = (viewsComponent: Record<string, Model<RouteComponent> | Lazy<RouteComponent>>, routes: NavItem[], parentName: string) => {
  for (const idx in routes) {
    switch (routes[idx].type) {
      case appType.DIR: {
        addRouteAll(viewsComponent, routes[idx].children instanceof Array ? routes[idx].children : [], parentName);
        break;
      }
      case appType.LINK: {
        break;
      }
      case appType.BUTTON: {
        break;
      }
      case appType.MENU:
      case appType.MICRO:
      case appType.ROUTE: {
        addRouteItem(viewsComponent, routes[idx], parentName);
        if ((routes[idx].children instanceof Array ? routes[idx].children : []).length) addRouteAll(viewsComponent, routes[idx].children instanceof Array ? routes[idx].children : [], parentName);
        break;
      }
      default:
        break;
    }
  }
};

/**
 * 动态添加路由
 */
export const addRouteItem = (viewsComponent: Record<string, Model<RouteComponent> | Lazy<RouteComponent>>, route: NavItem, parentName: string) => {
  const path = parentName ? route.path : `/${route.path}`;
  const permissionLegacy = (route.permission instanceof Array ? route.permission : []).filter((v) => v && typeof v === "string");
  const permissionGroups = (route.permissionGroups instanceof Array ? route.permissionGroups : []).map((v) => (v instanceof Array ? v.filter((v) => v && typeof v === "string") : [])).filter((v) => v.length);

  // console.log(route);
  if (process.env["NODE_ENV"] === "development") {
    const info = getUserInfo();
    if (permissionGroups.length ? !permissionGroups.some((permission) => permission.every((v) => info.hasPermission(v as permissionKey))) : permissionLegacy.length && !info.hasPermission(...(permissionLegacy as permissionKey[]))) {
      // eslint-disable-next-line no-console
      console.log(`%c\n[×]${route.title}: [${route.permission.join(",")}]: ${route.component}`, "color: red;display: inline-block;max-width: 300px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;");
    } else {
      // eslint-disable-next-line no-console
      console.log(`%c\n[√]${route.title}: [${route.permission.join(",")}]: ${route.component}`, "color: green;display: inline-block;max-width: 300px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;");
    }
  }

  const getRouteRaw = (path: string): RouteRecordRaw => {
    switch (route.type) {
      case appType.MENU:
      case appType.ROUTE:
        return {
          path: path.replace(/^\//, ""),
          name: route.name,
          component: async (): Promise<RouteComponent> => {
            const info = getUserInfo();
            try {
              if (permissionGroups.length ? !permissionGroups.some((permission) => permission.every((v) => info.hasPermission(v as permissionKey))) : permissionLegacy.length && !info.hasPermission(...(permissionLegacy as permissionKey[]))) return NotPower;
              else if (typeof viewsComponent[route.component] === "function") return await (viewsComponent[route.component] as Lazy<RouteComponent>)();
              else if (viewsComponent[route.component]) return viewsComponent[route.component];
              else throw new Error("");
            } catch (e) {
              // eslint-disable-next-line no-console
              if (process.env["NODE_ENV"] === "development") console.log(`加载失败, \`${route.component}\`不存在此项目中`, Object.keys(viewsComponent).join("\n"));
              // eslint-disable-next-line no-console
              console.warn(e);
              return NotFind;
            }
          },
          meta: { title: route.title, icon: route.icon || useConfig().layout.menuDefaultIcon, keepalive: Boolean(route.keepalive), type: route.type, url: route.url, addtab: !/\/:/.test(path) },
        };
      case appType.MICRO:
        return {
          path: `${path.replace(/^\//, "")}:page(.*)*`,
          name: route.name,
          component: MicroView,
          meta: { title: route.title, icon: route.icon || useConfig().layout.menuDefaultIcon, keepalive: Boolean(route.keepalive), type: route.type, url: route.url, addtab: !/\/:/.test(path) },
        };
      default:
        return {
          path: path,
          name: route.name,
          component: NotFind,
          meta: { title: route.title, icon: route.icon || useConfig().layout.menuDefaultIcon, keepalive: Boolean(route.keepalive), type: route.type, url: route.url, addtab: !/\/:/.test(path) },
        };
    }
  };

  const routeBaseInfo: RouteRecordRaw = getRouteRaw(path);
  if (parentName) routerList.push(router.addRoute(parentName, routeBaseInfo));
  else routerList.push(router.addRoute(routeBaseInfo));
};
