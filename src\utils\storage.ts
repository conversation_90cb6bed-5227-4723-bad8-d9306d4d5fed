/**
 * window.localStorage
 * @method set 设置
 * @method get 获取
 * @method remove 移除
 * @method clear 移除全部
 */
export class Local {
  static set(key: string, val: any) {
    window.localStorage.setItem(key, JSON.stringify(val));
  }

  static get(key: string) {
    const json: any = window.localStorage.getItem(key);
    return JSON.parse(json);
  }

  static remove(key: string) {
    window.localStorage.removeItem(key);
  }

  static clear() {
    window.localStorage.clear();
  }
}

/**
 * window.sessionStorage
 * @method set 设置会话缓存
 * @method get 获取会话缓存
 * @method remove 移除会话缓存
 * @method clear 移除全部会话缓存
 */
export class Session {
  static set(key: string, val: any) {
    window.sessionStorage.setItem(key, JSON.stringify(val));
  }

  static get(key: string) {
    const json: any = window.sessionStorage.getItem(key);
    return JSON.parse(json);
  }

  static remove(key: string) {
    window.sessionStorage.removeItem(key);
  }

  static clear() {
    window.sessionStorage.clear();
  }
}
