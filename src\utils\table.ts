interface anyObj {
  [key: string]: any;
}

/* 表格行 */
export interface TableRow extends anyObj {
  children?: TableRow[];
}

export function findIndexRow(data: TableRow[], findIdx: number, keyIndex: number | TableRow = -1): number | TableRow {
  for (const key in data) {
    if (typeof keyIndex == "number") keyIndex++;

    if (keyIndex == findIdx) {
      return data[key];
    }

    if (data[key].children) {
      keyIndex = findIndexRow(data[key].children!, findIdx, keyIndex);
      if (typeof keyIndex != "number") {
        return keyIndex;
      }
    }
  }
  return keyIndex;
}

export function getTreeItemChildren<T extends Record<string, unknown>>(data: T[], dataId: unknown, option?: { key?: string; children?: string }): T[] | undefined {
  const key = option?.key || "id";
  const children = option?.children || "children";
  if (dataId) {
    for (let index = 0; index < data.length; index++) {
      if (data[index][key] === dataId) return data[index][children] instanceof Array ? (data[index][children] as T[]) : [];
      else {
        const findData = getTreeItemChildren(data[index][children] instanceof Array ? (data[index][children] as T[]) : [], dataId, { key, children });
        if (findData) return findData;
      }
    }
  } else return data;
}
export function spliceTreeItemById<T extends Record<string, unknown>>(data: T[], dataId: unknown, option?: { key?: string; children?: string; parentKey?: string }): { success: boolean; parentId: string; target: T | undefined; orders: { id: unknown; order: number }[] } {
  const key = option?.key || "id";
  const parentKey = option?.parentKey || "parentId";
  const children = option?.children || "children";
  if (dataId) {
    for (let index = 0; index < data.length; index++) {
      if (data[index][key] === dataId) {
        const parentId = (data[index][parentKey] as string) || "";
        const target = data.splice(index, 1)[0];
        return {
          success: true,
          parentId,
          orders: data.slice(index).map((v, i) => ({ id: v[key], order: index + i })),
          target,
        };
      } else {
        const result = spliceTreeItemById(data[index][children] instanceof Array ? (data[index][children] as T[]) : [], dataId, { key, children, parentKey });
        if (result.target) return result;
      }
    }
  }
  return { success: false, target: undefined, parentId: "", orders: [] };
}
export function appendTreeItemById<T extends Record<string, unknown>>(data: T[], dataId: unknown, inData: T, option?: { key?: string; children?: string; parentKey?: string; append?: boolean; toChildren?: boolean }): { success: boolean; parentId: string; orders: { id: unknown; order: number }[] } {
  const key = option?.key || "id";
  const append = option?.append || false;
  const toChildren = option?.toChildren || false;
  const parentKey = option?.parentKey || "parentId";
  const children = option?.children || "children";
  if (dataId) {
    for (let index = 0; index < data.length; index++) {
      if (data[index][key] === dataId) {
        if (toChildren && append) {
          const dataChildren = data[index][children] instanceof Array ? (data[index][children] as T[]) : [];
          if (dataChildren.length && (data[index][key] as string) !== inData[key]) {
            const parentId = (data[index][key] as string) || "";
            (data[index][children] as T[]).unshift(inData);
            return {
              success: true,
              parentId,
              orders: (data[index][children] as T[]).map((v, i) => ({ id: v[key], order: index + i })),
            };
          }
        }
        const parentId = (data[index][parentKey] as string) || "";
        data.splice(append ? index + 1 : index, 0, inData);
        return {
          success: true,
          parentId,
          orders: data.slice(index).map((v, i) => ({ id: v[key], order: index + i })),
        };
      } else {
        const dataChildren = data[index][children] instanceof Array ? (data[index][children] as T[]) : [];
        if (dataChildren.length) {
          if (!toChildren && append && dataChildren[dataChildren.length - 1][key] === dataId) {
            const parentId = (data[index][parentKey] as string) || "";
            data.splice(append ? index + 1 : index, 0, inData);
            return {
              success: true,
              parentId,
              orders: data.slice(index).map((v, i) => ({ id: v[key], order: index + i })),
            };
          } else {
            const orders = appendTreeItemById(dataChildren, dataId, inData, { key, children, parentKey, append });
            if (orders.success) return orders;
          }
        }
      }
    }
  }
  return { success: false, parentId: "", orders: [] };
}
export function getTreeItemById<T extends Record<string, unknown>>(data: T[], dataId: unknown, option: { key?: string; children?: string } = {}): T | undefined {
  const key = option.key || "id";
  const children = option.children || "children";
  if (dataId) {
    for (let index = 0; index < data.length; index++) {
      if (data[index][key] === dataId) return data[index];
      else {
        const result = getTreeItemById(data[index][children] instanceof Array ? (data[index][children] as T[]) : [], dataId, { key, children });
        if (result) return result;
      }
    }
  }
}
