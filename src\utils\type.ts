import { cloneDeep } from "lodash-es";

export const TypeHelper = {
  string: { test: (v: any): v is string => /^\[object\sString\]$/g.test(Object.prototype.toString.call(v)), transfer: (v: any, f: string): typeof f => (["string", "number"].includes(typeof v) ? String(v) : f) },
  number: { test: (v: any): v is number => /^\[object\sNumber\]$/g.test(Object.prototype.toString.call(v)), transfer: (v: any, f: number): typeof f => ((p) => (isNaN(p) ? f : p))(parseInt(String(v instanceof Date ? v.getTime() : v))) },
  boolean: { test: (v: any): v is boolean => /^\[object\sBoolean\]$/g.test(Object.prototype.toString.call(v)), transfer: (v: any, f: boolean): typeof f => (["string", "number", "boolean"].includes(typeof v) ? Boolean(v) : f) },
  object: { test: (v: any): v is Record<string, any> => /^\[object\sObject\]$/g.test(Object.prototype.toString.call(v)), transfer: (v: any, f: Record<string, any>): typeof f => (/^\[object\sObject\]$/g.test(Object.prototype.toString.call(v)) ? v : f) },
  array: { test: (v: any): v is any[] => /^\[object\sArray\]$/g.test(Object.prototype.toString.call(v)), transfer: (v: any, f: any[]): typeof f => (v instanceof Array ? v : f) },
  date: { test: (v: any): v is Date => /^\[object\sDate\]$/g.test(Object.prototype.toString.call(v)), transfer: (v: any, f: Date): typeof f => (["string", "number"].includes(typeof v) ? new Date(v as string | number | Date) : f) },
};

export interface ConstructorFunc<T> {
  new (value: unknown): T;
  (value: unknown): T;
  // toString(): string;
  // valueOf(): T;
  // readonly prototype: ConstructorFunc<T>;
}

export function buildTypeHelper<T>(value: T, pattern?: RegExp): { value: T; test: (v: unknown) => v is T; transfer: (fv: unknown, ov: T) => T; type: string; ConstructorFunction: ConstructorFunc<T> } {
  const objectConstructor: string = Object.prototype.toString.call(value);
  const test = (v: unknown): v is T => {
    if (Object.prototype.toString.call(v) === objectConstructor) return pattern instanceof RegExp ? pattern.test(String(v)) : true;
    else return false;
  };
  const ConstructorFunction = new Object(value).constructor as ConstructorFunc<T>;
  const type = (/^\[object\s(?<type>[a-zA-Z0-9]*)\]$/g.exec(objectConstructor)?.groups?.type as string).toLowerCase();

  switch (objectConstructor) {
    case "[object Undefined]":
    case "[object Null]": {
      const transfer = (fv: unknown, ov: T): T => (test(fv) ? fv : cloneDeep(ov));
      return { value, test, transfer, type, ConstructorFunction };
    }
    case "[object Boolean]":
    case "[object Number]":
    case "[object String]": {
      const transfer = (fv: unknown, ov: T): T => {
        if (test(fv)) return fv;
        if (pattern instanceof RegExp || fv === undefined || fv === null) return ov;
        try {
          return ConstructorFunction(fv);
        } catch (error) {
          return ov;
        }
      };
      return { value, test, transfer, type, ConstructorFunction };
    }
    case "[object Object]": {
      const transfer = (fv: unknown, ov: T): T => {
        if (test(fv)) return fv;
        else return ov;
        // try {
        //   return new ConstructorFunction(fv);
        // } catch (error) {
        //   return ov;
        // }
      };
      return { value, test, transfer, type, ConstructorFunction };
    }
    case "[object Array]": {
      const transfer = (fv: unknown, ov: T): T => {
        if (test(fv)) return fv;
        try {
          return Array.from(fv as Iterable<T> | ArrayLike<T>) as unknown as T;
        } catch (error) {
          return ov;
        }
      };
      return { value, test, transfer, type, ConstructorFunction };
    }
    default: {
      const transfer = (fv: unknown, ov: T): T => (test(fv) ? fv : cloneDeep(ov));
      return { value, test, transfer, type, ConstructorFunction };
    }
  }
}
