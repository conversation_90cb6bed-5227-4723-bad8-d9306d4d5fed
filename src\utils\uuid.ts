function createUUID(base = `${Date.now().toString(36).padStart(8, "x")}-xxxx-4xxx-yxxx-xxxxxxxxxxxx`) {
  return String(base).replace(/[xy]/g, replaceUnitGUID);
}

function replaceUnitGUID(unit: string) {
  const randomHEX = (Math.random() * 16) | 0;
  switch (String(unit).toLowerCase()) {
    case "x":
      return randomHEX.toString(16);
    case "y":
      return ((randomHEX & 0x3) | 0x8).toString(16);
    default:
      return ((randomHEX & 0x3) | 0x8).toString(16);
  }
}

export default createUUID;
