import type { RuleType } from "async-validator";
import { FormItemRule } from "element-plus";
import { i18n } from "@/lang/index";
import { computed } from "vue";

/**
 * 手机号码验证
 */
export function validatorMobile(rule: any, mobile: string | number, callback: Function) {
  // 允许空值，若需必填请添加多验证规则
  if (!mobile) {
    return callback();
  }
  if (!/^(1[3-9])\d{9}$/.test(mobile.toString())) {
    return callback(new Error(i18n.global.t("validate.Please enter the correct mobile number")));
  }
  return callback();
}

/**
 * 账户名验证
 */
export function validatorAccount(rule: any, val: string, callback: Function) {
  if (!val) {
    return callback();
  }
  if (!/^[a-zA-Z][A-Za-z0-9_?!#]{5,63}$/.test(val)) {
    return callback(new Error(i18n.global.t("validate.Please enter the correct account")));
  }
  return callback();
}

/**
 * 密码验证
 */
export function regularPassword(val: string) {
  return /^(?=.*?[a-z])(?=.*?[A-Z])(?=.*?\d)(?=.*?[~!#@$%^*&()_+{}\[\]|\\;:'",<.>\/?])[a-zA-Z\d~!#@$%^*&()_+{}\[\]|\\;:'",<.>\/?].+$/.test(val);
  // return /^(
  //     (?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*?])
  //     |
  //     (?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])
  //     |
  //     (?=.*[a-z])(?=.*[0-9])(?=.*[~!@#$%^&*?])
  //     |
  //     (?=.*[A-Z])(?=.*[0-9])(?=.*[~!@#$%^&*?])
  //   ).{8,128}$/.test(val);
  // return /^[\x21-\x7E]{8,128}$/g.test(val);
}
export function validatorPassword(rule: any, val: string, callback: Function) {
  if (!val) {
    return callback();
  }
  /* if (!/^.{8,128}$/g.test(val)) {
    return callback(new Error(i18n.global.t("validate.Requires {min} to {max} digits", { min: 8, max: 128 })));
  } else  */ if (!regularPassword(val)) {
    return callback(new Error(i18n.global.t("validate.Please enter the correct password") + " " + i18n.global.t("validate.Requires {min} to {max} digits", { min: 9, max: 16 })));
  }
  return callback();
}

/**
 * 变量名验证
 */
export function validatorVarName(rule: any, val: string, callback: Function) {
  if (!val) {
    return callback();
  }
  // eslint-disable-next-line no-control-regex
  if (!/^([^\x00-\xff]|[a-zA-Z_$])([^\x00-\xff]|[a-zA-Z0-9_$])*$/.test(val)) {
    return callback(new Error(i18n.global.t("validate.Please enter the correct name")));
  }
  return callback();
}

// export const validatorPattern: Partial<Record<buildValidatorParams["name"], { pattern: RegExp; message: string }>> = {
//   ident: { pattern: /^[^`~$^'< >]{0,63}$/, message: "支持1-64位字符,且不包含空格" },
//   mobile: { pattern: /^(1[3-9])\d{9}$/, message: i18n.global.t("validate.Please enter the correct mobile number") },
//   account: { pattern: /^[a-zA-Z][A-Za-z0-9_?!#]{0,63}$/, message: i18n.global.t("validate.Please enter the correct account") },
//   email: { pattern: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/, message: i18n.global.t("glob.Please enter the correct field", { field: "E-Mail" }) },
//   address: { pattern: /^((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))$/, message: "" },
//   password: { pattern: /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_!@#$%^&*`~?]+$)(?![a-z0-9]+$)(?![a-z\W_!@#$%^&*`~?]+$)(?![0-9\W_!@#$%^&*`~?]+$)[a-zA-Z0-9\W_!@#$%^&*`~?]+$/, message: i18n.global.t("login.Passwords check", { symbol: "(.~! @ # $% ^&*?)" }) },
//   // ${i18n.global.t("validate.Please enter the correct password")}
// };
export const validatorPatternC = computed<Partial<Record<buildValidatorParams["name"], { pattern: RegExp; message: string }>>>(() => ({
  ident: { pattern: /^[^`~$^'< >]{0,63}$/, message: "支持1-64位字符,且不包含空格" },
  mobile: { pattern: /^(1[3-9])\d{9}$/, message: i18n.global.t("validate.Please enter the correct mobile number") },
  account: { pattern: /^[a-zA-Z][A-Za-z0-9_?!#]{0,63}$/, message: i18n.global.t("validate.Please enter the correct account") },
  email: { pattern: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/, message: i18n.global.t("glob.Please enter the correct field", { field: "E-Mail" }) },
  address: { pattern: /^((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))$/, message: "" },
  password: { pattern: /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_!@#$%^&*`~?]+$)(?![a-z0-9]+$)(?![a-z\W_!@#$%^&*`~?]+$)(?![0-9\W_!@#$%^&*`~?]+$)[a-zA-Z0-9\W_!@#$%^&*`~?]+$/, message: i18n.global.t("login.Passwords check", { symbol: "(.~! @ # $% ^&*?)" }) },
  // ${i18n.global.t("validate.Please enter the correct password")}
}));

export const validatorPattern = validatorPatternC.value;

export function validatorEditorRequired(rule: any, val: string, callback: Function) {
  if (!val || val == "<p><br></p>") {
    return callback(new Error(i18n.global.t("validate.Content cannot be empty")));
  }
  return callback();
}

export const validatorType = {
  required: i18n.global.t("validate.required"),
  mobile: i18n.global.t("utils.mobile"),
  account: i18n.global.t("utils.account"),
  password: i18n.global.t("utils.password"),
  varName: i18n.global.t("utils.variable name"),
  url: "URL",
  email: i18n.global.t("utils.email"),
  date: i18n.global.t("utils.date"),
  number: i18n.global.t("utils.number"),
  integer: i18n.global.t("utils.integer"),
  float: i18n.global.t("utils.float"),
  reqPassword: i18n.global.t("validate.required"),
};

export interface buildValidatorParams {
  // 规则名:required=必填,mobile=手机号,account=账户,password=密码,varName=变量名,editorRequired=富文本必填,number、integer、float、date、url、email
  name: "required" | "mobile" | "account" | "password" | "varName" | "editorRequired" | "number" | "integer" | "float" | "date" | "url" | "email" | "ident" | "address" | "reqPassword";
  // 自定义验证错误消息
  message?: string;
  // 验证项的标题，这些验证方式不支持:mobile、account、password、varName、editorRequired
  title?: string;
  // 验证触发方式
  trigger?: "change" | "blur" | string[];

  min?: number;

  max?: number;
}

/**
 * 构建表单验证规则
 * @param {buildValidatorParams} paramsObj 参数对象
 */
export function buildValidatorData({ name, message, title, trigger = ["blur", "change"], min, max }: buildValidatorParams): FormItemRule {
  // 必填
  if (name == "required") {
    return {
      required: true,
      message: message ? message : i18n.global.t("glob.Cannot be empty", { field: title }),
      trigger: trigger,
    };
  }

  if (name == "reqPassword") {
    return {
      required: false,
      message: message ? message : i18n.global.t("glob.Cannot be empty", { field: title }),
      trigger: trigger,
    };
  }

  // 常见类型
  if (["number", "integer", "float", "date", "url", "email"].includes(name)) {
    return {
      type: name as RuleType,
      message: message ? message : i18n.global.t("glob.Please enter the correct field", { field: title }),
      trigger: trigger,
    };
  }
  const validatorPattern1 = validatorPatternC.value;
  // 正则
  if (name in validatorPattern1) {
    // i18n.global.t("validate.Requires {min} to {max} digits", { min: 9, max: 16 })
    const validator = validatorPattern1[name]!;
    if (typeof min == "number" && typeof max == "number") {
      return {
        type: "string",
        pattern: validator.pattern,
        message: `${validator.message || message || ""} ${i18n.global.t("validate.Requires {min} to {max} digits", { min, max })}`,
        min,
        max,
        trigger: trigger,
      };
    } else if (typeof min == "number") {
      // console.log(min, 66666);
      return {
        type: "string",
        pattern: validator.pattern,
        message: `${validator.message || message || ""} ${"最少密码长度" + min + "位"}`,
        min,
        trigger: trigger,
      };
    }
    return {
      type: "string",
      pattern: validatorPattern1[name]?.pattern,
      message: validatorPattern1[name]?.message ? `${validatorPattern1[name]?.message}` : message ? message : i18n.global.t("glob.Please enter the correct field", { field: title || "" }),
      trigger: trigger,
    };
  }

  // 自定义验证方法
  const validatorCustomFun: anyObj = {
    mobile: validatorMobile,
    account: validatorAccount,
    password: validatorPassword,
    varName: validatorVarName,
    editorRequired: validatorEditorRequired,
  };
  if (Object.prototype.hasOwnProperty.call(validatorCustomFun, name)) {
    return {
      required: name == "editorRequired" ? true : false,
      validator: validatorCustomFun[name],
      trigger: trigger,
      message: message,
    };
  }
  return {};
}
