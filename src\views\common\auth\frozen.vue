<template>
  <el-container class="tw-h-full tw-w-full">
    <el-main>
      <div class="main-container">
        <el-avatar class="tw-mb-[20px] tw-mt-[80px]" :size="80" :src="info.avatar" />
        <h1 class="tw-mb-[16px] tw-mt-[20px] tw-text-[22px] tw-font-semibold">{{ siteConfig.openName }}</h1>
        <div class="tw-w-full tw-overflow-hidden tw-text-center">
          <p class="tw-my-[6px] tw-text-[18px]" :style="{ color: 'var(--el-text-color-secondary)' }">{{ info.nickname || info.username || info.account || info.phone || info.email }} {{ $t("index.Select Tenant") }}</p>
          <div class="tw-w-full tw-py-[24px]">
            <el-scrollbar height="calc(100vh - 368px)" :view-style="{ padding: '0 8px' }">
              <el-form :model="form" ref="formRef" label-position="top">
                <el-row v-show="loading" :gutter="16">
                  <el-col :span="24" :offset="0">
                    <el-empty description="Loading...">
                      <template #image>
                        <LayoutLoading />
                      </template>
                    </el-empty>
                  </el-col>
                </el-row>
                <el-row v-show="!loading" :gutter="16">
                  <el-col :span="24">
                    <el-result icon="error" :title="$t('index.User Frozen')" sub-title="此用户已经被冻结，请联系管理员！"></el-result>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item>
                      <el-button type="danger" :loading="loading" class="tw-w-full" @click="logout">{{ $t("layouts.cancellation") }}</el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </el-main>
    <Footer></Footer>
  </el-container>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import Footer from "../components/footer.vue";
import LayoutLoading from "@/views/common/loading.vue";

import type { FormContext } from "element-plus";
import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";

import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";

const route = useRoute();
const router = useRouter();
const siteConfig = useSiteConfig();
const info = getInfo();
const loading = ref(false);

const formRef = ref<FormContext>();

const form = reactive({});

function getInfo() {
  switch (siteConfig.current) {
    case superBaseRoute.name:
      return useSuperInfo();
    case adminBaseRoute.name:
      return useAdminInfo();
    case usersBaseRoute.name:
      return useUsersInfo();
    default:
      return useUsersInfo();
  }
}

async function logout() {
  loading.value = true;
  await info.logout();
  await done();
  loading.value = false;
}

// async function submit() {
//   if (!formRef.value) return;
//   loading.value = true;
//   await done();
//   loading.value = false;
// }

async function done() {
  const routeName = route.name as import("vue-router").RouteRecordName;
  await router.replace({ name: siteConfig.baseInfo?.name, query: route.query });
  router.removeRoute(routeName);
}
</script>

<style scoped lang="scss">
.main-container {
  display: flex;
  flex-direction: column;
  width: 420px;
  height: 100%;
  margin: 0 auto;
  align-items: center;
}

@media screen and (max-width: 500px) {
  .main-container {
    width: 100%;
  }
}
.footer {
  color: var(--el-text-color-secondary);
  background-color: transparent !important;
  position: fixed;
  bottom: 0;
}
</style>
