<template>
  <el-container class="tw-h-full tw-w-full">
    <el-main>
      <div class="main-container">
        <el-avatar class="tw-mb-[20px] tw-mt-[80px]" :size="80" :src="info.avatar" />
        <h1 class="tw-mb-[16px] tw-mt-[20px] tw-text-[22px] tw-font-semibold">{{ siteConfig.openName }}</h1>
        <div class="tw-w-full tw-overflow-hidden tw-text-center">
          <p class="tw-my-[6px] tw-text-[18px]" :style="{ color: 'var(--el-text-color-secondary)' }">{{ info.username || info.account || info.phone || info.email }} {{ $t("index.Init Info") }}</p>
          <div class="tw-w-full tw-py-[24px]">
            <el-scrollbar height="calc(100vh - 368px)" :view-style="{ padding: '0 8px' }">
              <FormModel ref="formRef" :model="form" label-position="top" @submit="submit">
                <el-row v-show="loading" :gutter="16" class="tw-w-full">
                  <el-col :span="24" :offset="0">
                    <el-empty description="Loading...">
                      <template #image>
                        <LayoutLoading />
                      </template>
                    </el-empty>
                  </el-col>
                </el-row>
                <el-row v-show="!loading" :gutter="16">
                  <FormItem :span="24" :label="`姓名`" tooltip="" prop="name" :rules="[buildValidatorData({ name: 'required', title: `${'用户'}姓名` })]">
                    <el-input v-model="form.name" :disabled="!!info.username" :placeholder="`请输入${'用户'}姓名`" clearable></el-input>
                  </FormItem>
                  <!-- <FormItem :span="24" :label="`昵称`" tooltip="" prop="nickname" :rules="[{ required: false, message: t('glob.Please input field', { field: `${'用户'}昵称` }), trigger: 'blur' }]">
                    <el-input v-model="form.nickname" :placeholder="`请输入${'用户'}昵称`" />
                  </FormItem> -->
                  <FormItem :span="24" :label="`账号	`" tooltip="账号以大小写字母开头，包含6到64位大小写字母、数字、下划线、问号、感叹号、井号" prop="account" :rules="[...(!(form.phone || form.email) ? [buildValidatorData({ name: 'required', title: `${'用户'}账号` })] : []), ...(info.account ? [] : [buildValidatorData({ name: 'account', title: `${'用户'}账号` })])]">
                    <el-input v-model="form.account" :disabled="!!info.account" :placeholder="`请输入${'用户'}账号`" clearable>
                      <template #append>@{{ (info.currentTenant || {}).abbreviation }}</template>
                    </el-input>
                  </FormItem>

                  <FormItem :span="!info.phone && siteConfig.loginChannels.filter((v) => v === loginChannels.SMS_CODE).length ? 16 : 24" :label="`手机号	`" tooltip="" prop="phone" :rules="[...(!(info.account || info.email) ? [buildValidatorData({ name: 'required', title: `${'用户'}手机号` })] : []), ...(form.phone ? [] : [buildValidatorData({ name: 'mobile', title: `${'用户'}手机号` })])]">
                    <el-input v-model="form.phone" :disabled="!!info.phone" :placeholder="`请输入${'用户'}手机号`">
                      <template v-if="!info.phone && siteConfig.loginChannels.filter((v) => v === loginChannels.SMS_CODE).length" #suffix>
                        <el-link type="primary" :underline="false" :disabled="!form.phone || !!limitationSMS" @click="() => sendCode('phone')">发送短信验证码{{ limitationSMS ? `(${limitationSMS})` : "" }}</el-link>
                      </template>
                    </el-input>
                  </FormItem>
                  <FormItem v-if="!info.phone && siteConfig.loginChannels.filter((v) => v === loginChannels.SMS_CODE).length" :span="8" prop="smsCode" label="短信验证码" :rules="[{ required: !!form.phone, message: '请输入短信验证码', trigger: 'blur' }]">
                    <el-input v-model="form.smsCode" :placeholder="`短信验证码`"></el-input>
                  </FormItem>

                  <FormItem :span="!info.email && siteConfig.loginChannels.filter((v) => v === loginChannels.EMAIL_CODE).length ? 16 : 24" :label="`邮箱`" tooltip="" prop="email" :rules="[...(!(info.account || info.phone) ? [buildValidatorData({ name: 'required', title: `${'用户'}邮箱` })] : []), ...(info.email ? [] : [buildValidatorData({ name: 'email', title: `${'用户'}邮箱` })])]">
                    <el-input v-model="form.email" :disabled="!!info.email" :placeholder="`请输入${'用户'}邮箱`">
                      <template v-if="!info.email && siteConfig.loginChannels.filter((v) => v === loginChannels.EMAIL_CODE).length" #suffix>
                        <el-link type="primary" :underline="false" :disabled="!form.email || !!limitationEMAIL" @click="() => sendCode('email')">发送邮箱验证码{{ limitationEMAIL ? `(${limitationEMAIL})` : "" }}</el-link>
                      </template>
                    </el-input>
                  </FormItem>
                  <FormItem v-if="!info.email && siteConfig.loginChannels.filter((v) => v === loginChannels.EMAIL_CODE).length" :span="8" prop="emailCode" label="邮箱验证码" :rules="[{ required: !!form.email, message: '请输入邮箱验证码', trigger: 'blur' }]">
                    <el-input v-model="form.emailCode" :placeholder="`邮箱验证码`"></el-input>
                  </FormItem>

                  <FormItem :span="24" :label="`性别`" tooltip="" prop="gender" :rules="[]">
                    <el-radio-group v-model="form.gender">
                      <el-radio v-for="item in genderOption" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
                    </el-radio-group>
                  </FormItem>
                  <FormItem :span="24" :label="`${'用户'}语言`" tooltip="" prop="language" :rules="[]">
                    <el-select v-model="form.language" class="tw-w-full" :placeholder="$t('glob.Please select field', { field: `${'用户'}语言` })" filterable clearable>
                      <el-option v-for="item in localesOption" :key="`locale-${item.value}`" :label="item.label" :value="item.value">
                        <div :style="{ background: `url(${item.icon}) no-repeat left / auto calc(100% - 12px)`, paddingLeft: '30px' }">{{ item.label }}</div>
                      </el-option>
                    </el-select>
                  </FormItem>
                  <el-col :span="24">
                    <el-form-item>
                      <el-button type="primary" :loading="loading" class="tw-w-full" @click="submit">{{ $t("glob.Confirm") }}</el-button>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item>
                      <el-button type="danger" :loading="loading" class="tw-w-full" @click="logout">{{ $t("layouts.cancellation") }}</el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
              </FormModel>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </el-main>
    <Footer></Footer>
  </el-container>
</template>

<script setup lang="ts">
import { reactive, ref, provide, watch, nextTick, h } from "vue";
import { ElAlert, ElButton, ElForm, ElFormItem, ElInput, ElLink, ElMessageBox, ElRadio, ElRadioGroup } from "element-plus";
import { User, Lock, Phone, Message, ChatDotSquare } from "@element-plus/icons-vue";
import { useRouter, useRoute } from "vue-router";
import Footer from "../components/footer.vue";
import LayoutLoading from "@/views/common/loading.vue";
import { buildValidatorData } from "@/utils/validate";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import { bindCanvasImage } from "@/utils/image";
import { ElMessage, FormContext } from "element-plus";

// import FormItem from "@/components/formItem/index.vue";
// import { EditorType } from "@/views/common/interface";
// import { InputType } from "@/components/formItem";
import { gender, genderOption } from "@/api/personnel";
import { sendSMSCode, sendEMAILCode, initUserinfo, loginChannels } from "@/api/system";
import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";
import { sendCommonSmsCode, sendCommonEmailCode, captchaForImage } from "@/api/system";
import { bindFormBox } from "@/utils/bindFormBox";

import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
import { locales, localesOption } from "@/api/locale";

const route = useRoute();
const router = useRouter();
const siteConfig = useSiteConfig();
const info = getInfo();
const width = ref(420);
const loading = ref(false);

const limitationSMS = ref(0);
const limitationEMAIL = ref(0);

const formRef = ref<FormContext>();
const coolingCaptcha = ref(0);
watch(coolingCaptcha, (cooling) => cooling !== 0 && setTimeout(() => coolingCaptcha.value !== 0 && (coolingCaptcha.value = cooling - 1), 1000));
export interface ReqType {
  name?: string /* 姓名, 未设置则必填 */;
  // nickName?: string /* 昵称 */;
  account?: string /* 个人账号, 已设置则忽略 */;
  phone?: string /* 手机号码, 已设置则忽略 */;
  email?: string /* 邮箱, 已设置则忽略 */;
  gender?: keyof typeof gender /* 性别 枚举类型: SECRET :保密 | MALE :男性 | FEMALE :女性 */;
  language?: string;
}
const form = reactive<ReqType & Partial<Record<"smsCode" | "emailCode", string>>>({
  name: info.username /* 姓名 未设置则必填 */,
  // nickName: info.nickname /* 昵称 */,
  account: info.account /* 个人账号 已设置则忽略 */,
  phone: info.phone /* 手机号码 已设置则忽略 */,
  smsCode: "" /* 短信验证码, 如果需要设置手机号且平台当前支持短信功能, 则必填 */,
  email: info.email /* 邮箱 已设置则忽略 */,
  emailCode: "" /* 邮箱验证码, 如果需要设置邮箱且平台当前支持邮件功能, 则必填 */,
  gender: info.gender /* 性别 枚举: FEMALE,MALE,SECRET */,
  language: info.language,
});

watch(limitationSMS, async (timer) => {
  if (timer <= 0) return;
  await new Promise((resolve) => setTimeout(resolve, 1000));
  limitationSMS.value = timer - 1;
});
watch(limitationEMAIL, async (timer) => {
  if (timer <= 0) return;
  await new Promise((resolve) => setTimeout(resolve, 1000));
  limitationEMAIL.value = timer - 1;
});

async function sendCode(prop: keyof typeof form) {
  if (!formRef.value) return;
  const $formRef = formRef.value;
  $formRef.clearValidate();
  if (!(await new Promise((resolve) => $formRef.validateField([prop], resolve)))) return;
  try {
    loading.value = true;
    switch (prop) {
      case "phone": {
        // const { success, message, data } = await sendSMSCode({ phone: <string>form.phone });
        // if (!success) throw Object.assign(new Error(message), { success, data });
        // ElMessage.success("成功发送短信验证码");
        // limitationSMS.value = 60;
        await inputCertificate(($form) => sendCommonSmsCode({ phone: <string>form.phone, ...$form }));
        break;
      }
      case "email": {
        // const { success, message, data } = await sendEMAILCode({ email: <string>form.email });
        // if (!success) throw Object.assign(new Error(message), { success, data });
        // ElMessage.success("成功发送邮箱验证码");
        // limitationEMAIL.value = 60;
        await inputCertificate(($form) => sendCommonEmailCode({ email: <string>form.email, ...$form }));
        break;
      }
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}

async function inputCertificate(callback: (form: { certificate?: string; captcha?: string }) => Promise<{ success: boolean; message: string } & Record<string, unknown>>): Promise<void> {
  const $form = reactive({
    title: "输入验证码",
    certificate: "",
    captcha: "",
  });
  const $input = ref<import("element-plus").InputInstance>();
  const $canvasRef = ref<HTMLCanvasElement>();
  const unWatch = watch($canvasRef, ($canvas) => {
    if ($canvas instanceof HTMLCanvasElement) {
      unWatch();
      $form.captcha = "";
      $form.certificate = String(`xxxxxxxx-xxxx-4xxx-yxxx-${Date.now().toString(16).padStart(12, "x")}`).replace(/[xy]/g, (c) => Number(c === "x" ? (Math.random() * 16) | 0 : (((Math.random() * 16) | 0) & 0x3) | 0x8).toString(16));
      updateCertificate($canvas, $form.certificate).then(() => nextTick(() => $input.value?.focus()));
    }
  });
  await bindFormBox(
    [
      h(ElAlert, { type: "info", title: "验证码", description: "请输入下方验证码点击确定，发送验证码到其他设备", showIcon: true, closable: false, style: { marginBottom: "22px" } }),
      h(ElFormItem, { rules: [{ required: true, message: "请输入验证码", trigger: "blur" }], prop: "captcha", label: "", size: "large" }, () => [
        h(ElInput, {
          "ref": (vm) => ($input.value = vm as import("element-plus").InputInstance),
          "prefixIcon": ChatDotSquare,
          "placeholder": "请输入验证码",
          "clearable": true,
          "modelValue": $form.captcha,
          "onUpdate:modelValue": (v) => ($form.captcha = v),
          "style": { verticalAlign: "top", width: "calc(100% - 118px)", marginRight: "12px" },
        }),
        h("canvas", {
          ref: (vm) => ($canvasRef.value = vm as HTMLCanvasElement),
          class: ["captcha-img"],
          height: "40",
          width: "100",
          title: "看不清，换一张",
          onClick({ target }: PointerEvent) {
            if (target instanceof HTMLCanvasElement) {
              $form.captcha = "";
              $form.certificate = String(`xxxxxxxx-xxxx-4xxx-yxxx-${Date.now().toString(16).padStart(12, "x")}`).replace(/[xy]/g, (c) => Number(c === "x" ? (Math.random() * 16) | 0 : (((Math.random() * 16) | 0) & 0x3) | 0x8).toString(16));
              updateCertificate(target, $form.certificate).then(() => nextTick(() => $input.value?.focus()));
            }
          },
        }),
      ]),
    ],
    $form,
    async () => {
      const { success, message, data } = await callback({ certificate: $form.certificate, captcha: $form.captcha });
      if (!success) throw Object.assign(new Error(message), { success, data });
      coolingCaptcha.value = 60;
      return { success, message };
    }
  );
}

async function updateCertificate(canvasRef: HTMLCanvasElement, certificate: string) {
  try {
    const { success, data, message } = await captchaForImage({ certificate });
    if (success) {
      await bindCanvasImage(canvasRef.getContext("2d") as CanvasRenderingContext2D, data);
    } else throw Object.assign(new Error(message), { success, data, message });
  } catch (error) {
    return;
  }
}

function getInfo() {
  switch (siteConfig.current) {
    case superBaseRoute.name:
      return useSuperInfo();
    case adminBaseRoute.name:
      return useAdminInfo();
    case usersBaseRoute.name:
      return useUsersInfo();
    default:
      return useUsersInfo();
  }
}

async function submit() {
  if (!formRef.value) return;
  const $formRef = formRef.value;
  $formRef.clearValidate();
  const _props: (keyof typeof form)[] = [];
  if (!info.account) _props.push(...(["account"] as (keyof typeof form)[]));
  if (!info.phone && form.phone) _props.push(...((siteConfig.loginChannels.filter((v) => v === loginChannels.SMS_CODE).length ? ["phone", "smsCode"] : ["phone"]) as (keyof typeof form)[]));
  if (!info.email && form.email) _props.push(...((siteConfig.loginChannels.filter((v) => v === loginChannels.EMAIL_CODE).length ? ["email", "emailCode"] : ["phone"]) as (keyof typeof form)[]));
  if (!(await new Promise((resolve) => $formRef.validateField(_props, resolve)))) return;
  const $form = _props.reduce((p, k) => Object.assign(p, { [k]: form[k] }), <Partial<typeof form>>{ name: form.name, gender: form.gender, language: form.language });
  try {
    loading.value = true;
    await nextTick();
    const { success, message, data } = await initUserinfo($form);
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success("成功完善个人资料！");
    await done();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}

async function logout() {
  loading.value = true;
  await info.logout();
  await done();
  loading.value = false;
}

async function done() {
  const routeName = route.name as import("vue-router").RouteRecordName;
  await router.replace({ name: siteConfig.baseInfo?.name, query: route.query });
  router.removeRoute(routeName);
}

// provide("#PARAMS", { "#TYPE": EditorType.Add });
// provide("#WIDTH", width);
</script>

<style scoped lang="scss">
.main-container {
  display: flex;
  flex-direction: column;
  width: 420px;
  height: 100%;
  margin: 0 auto;
  align-items: center;
}

@media screen and (max-width: 500px) {
  .main-container {
    width: 100%;
  }
}
.footer {
  color: var(--el-text-color-secondary);
  background-color: transparent !important;
  position: fixed;
  bottom: 0;
}
.loading-chase {
  margin-left: auto;
  margin-right: auto;
  width: 40px;
  height: 40px;
  position: relative;
  animation: loading-chase 2.5s infinite linear both;

  &-dot {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    animation: loading-chase-dot 2s infinite ease-in-out both;
    &:before {
      content: "";
      display: block;
      width: 25%;
      height: 25%;
      background-color: var(--el-color-primary);
      border-radius: 100%;
      animation: loading-chase-dot-before 2s infinite ease-in-out both;
    }
    &:nth-child(1) {
      animation-delay: -1.1s;
      &:before {
        animation-delay: -1.1s;
      }
    }
    &:nth-child(2) {
      animation-delay: -1s;
      &:before {
        animation-delay: -1s;
      }
    }
    &:nth-child(3) {
      animation-delay: -0.9s;
      &:before {
        animation-delay: -0.9s;
      }
    }
    &:nth-child(4) {
      animation-delay: -0.8s;
      &:before {
        animation-delay: -0.8s;
      }
    }
    &:nth-child(5) {
      animation-delay: -0.7s;
      &:before {
        animation-delay: -0.7s;
      }
    }
    &:nth-child(6) {
      animation-delay: -0.6s;
      &:before {
        animation-delay: -0.6s;
      }
    }
  }
}

@keyframes loading-chase {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-chase-dot {
  80%,
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-chase-dot-before {
  50% {
    transform: scale(0.4);
  }
  100%,
  0% {
    transform: scale(1);
  }
}
</style>
