<template>
  <el-aside class="ba-user-layouts">
    <div class="userinfo">
      <el-button-group>
        <el-button class="userinfo-button-item" title="" size="default" plain @click="() => null">
          <span></span>
        </el-button>
      </el-button-group>
    </div>
  </el-aside>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { useRouter } from "vue-router";
import { useSiteConfig } from "@/stores/siteConfig";

const router = useRouter();
const siteConfig = useSiteConfig();
</script>

<style scoped lang="scss">
.ba-user-layouts {
  width: 240px;
  background-color: var(--ba-bg-color-overlay);
  box-shadow: var(--el-box-shadow-light);
}
.userinfo {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}
.username {
  display: block;
  text-align: center;
  width: 100%;
  padding: 10px 0;
  font-size: var(--el-font-size-large);
  font-weight: bold;
}
.user-avatar-box {
  position: relative;
  cursor: pointer;
}
.user-avatar {
  display: block;
  width: 100px;
  border-radius: 50%;
}
.user-avatar-gender {
  position: absolute;
  bottom: 0;
  right: 10px;
  height: 22px;
  width: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--ba-bg-color-overlay);
  border-radius: 50%;
  box-shadow: var(--el-box-shadow);
}
.userinfo-button-item {
  font-size: var(--el-font-size-small);
  height: 30px;
}
.user-menus {
  font-size: var(--el-font-size-base);
  color: var(--el-text-color-regular);
  padding-bottom: 20px;
}
.user-menu-max-title {
  font-size: 15px;
  color: var(--el-text-color-secondary);
  padding: 5px 30px;
}
.user-menu-item {
  padding: 10px 30px;
  cursor: pointer;
  .icon {
    width: 16px;
    height: 16px;
    text-align: center;
    margin-right: 8px;
  }
}
.user-menu-item:hover,
.user-menu-item.active {
  border-left: 2px solid var(--el-color-primary);
  padding-left: 28px;
  color: var(--el-color-primary);
  .icon {
    color: var(--el-color-primary) !important;
  }
  background-color: var(--el-color-info-light-8);
}
@media screen and (max-width: 991px) {
  .ba-user-layouts {
    width: 100%;
    background-color: var(--ba-bg-color-overlay);
    box-shadow: none;
  }
}
</style>
