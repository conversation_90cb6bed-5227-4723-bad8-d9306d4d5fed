<template>
  <el-footer class="footer">
    <div>Copyright @ {{ PackageDate }} {{ siteConfig.openName }} {{ $t("index.copyright") }}</div>
    <div v-html="siteConfig.footer"></div>
  </el-footer>
</template>

<script setup lang="ts">
import { useSiteConfig } from "@/stores/siteConfig";
import moment from "moment";

const PackageDate = `${moment(process.env.APP_PACKAGE_DATE).format("YYYY")}~${moment().format("YYYY")}`;
const siteConfig = useSiteConfig();
</script>

<style scoped lang="scss">
.footer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  :deep(a) {
    color: var(--el-text-color-secondary);
    font-size: 12px;
  }
}
</style>
