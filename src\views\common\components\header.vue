<template>
  <el-header class="header">
    <el-row justify="center">
      <el-col class="header-row" :span="16" :xs="24">
        <el-menu :default-active="state.activeMenu" class="frontend-header-menu" mode="horizontal" :ellipsis="false">
          <el-menu-item index="index" @click="router.push({ name: '/' })">{{ $t("index.index") }}</el-menu-item>
          <el-sub-menu index="switch-language">
            <template #title>{{ $t("index.language") }}</template>
            <el-scrollbar max-height="400px">
              <el-menu-item v-for="item in config.lang.langArray" :key="item.name" :index="'switch-language-' + item.value" @click="editDefaultLang(item.name)">{{ item.value }}</el-menu-item>
            </el-scrollbar>
          </el-sub-menu>
          <el-menu-item index="theme-switch" class="theme-switch">
            <DarkSwitch @click="() => config.setLayout('isDark', !config.layout.isDark)" />
          </el-menu-item>
        </el-menu>
      </el-col>
    </el-row>
  </el-header>
</template>

<script setup lang="ts">
import { reactive } from "vue";
import { useRoute } from "vue-router";
import { useRouter } from "vue-router";
import { useConfig } from "@/stores/config";
import { editDefaultLang } from "@/lang/index";
import Aside from "./aside.vue";
import DarkSwitch from "@/layouts/common/components/darkSwitch.vue";

const state = reactive({
  activeMenu: "",
});

const route = useRoute();
const router = useRouter();
const config = useConfig();

switch (route.name) {
  case "/":
    state.activeMenu = "";
    break;
  case "userLogin":
    state.activeMenu = "user";
    break;
}
</script>

<style scoped lang="scss">
.header {
  background-color: var(--ba-bg-color-overlay);
  box-shadow: 0 0 8px rgba(0 0 0 / 8%);
}
.el-header {
  padding: 0;
}
.header-row {
  display: flex;
}
.user-menus-expand {
  display: flex;
  height: 60px;
  align-items: center;
  justify-content: center;
}
.header-logo {
  display: flex;
  height: 60px;
  align-items: center;
  cursor: pointer;
  img {
    height: 34px;
    // width: 34px;
  }
  span {
    padding-left: 4px;
    font-size: var(--el-font-size-large);
  }
}
.switch-language {
  display: flex;
  align-items: center;
  span {
    padding-right: 4px;
  }
}
.el-menu--horizontal {
  margin-left: auto;
  border-bottom: none;
}
.header-user-box {
  display: flex;
  align-items: center;
  justify-content: center;
}
.header-user-avatar {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  border-radius: 50%;
}
.el-menu--horizontal > .el-menu-item,
.el-menu--horizontal > :deep(.el-sub-menu) .el-sub-menu__title,
.el-menu--horizontal > .el-menu-item.is-active {
  border-bottom: none;
}
:deep(.aside-drawer) {
  .el-drawer__body {
    padding: 0;
  }
}
@media only screen and (max-width: 768px) {
  .header-logo {
    padding-left: 10px;
  }
  .user-menus-expand {
    padding-left: 20px;
  }
}
@media screen and (max-width: 425px) {
  :deep(.aside-drawer) {
    width: 70% !important;
  }
}
.theme-switch {
  --el-menu-hover-bg-color: none;
}

@at-root .dark {
  .header-logo {
    .hidden-xs-only {
      color: var(--el-text-color-primary);
    }
  }
}
</style>
