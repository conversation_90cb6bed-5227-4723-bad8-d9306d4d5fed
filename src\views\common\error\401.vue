<template>
  <div>
    <el-header class="header">
      <el-row justify="center">
        <el-col class="header-row" :span="16" :xs="24">
          <div class="header-logo" @click="$router.push({ name: '/' })">
            <img src="@/assets/logo.png" />
            <span class="hidden-xs-only">{{ siteConfig.openName }}</span>
          </div>
          <el-menu class="frontend-header-menu" mode="horizontal" :ellipsis="false">
            <el-menu-item index="index" @click="$router.push({ name: '/' })">{{ $t("index.index") }}</el-menu-item>
            <el-sub-menu index="switch-language">
              <template #title>{{ $t("index.language") }}</template>
              <el-scrollbar max-height="400px">
                <el-menu-item v-for="item in config.lang.langArray" :key="item.name" :index="'switch-language-' + item.value" @click="editDefaultLang(item.name)">{{ item.value }}</el-menu-item>
              </el-scrollbar>
            </el-sub-menu>
          </el-menu>
        </el-col>
      </el-row>
    </el-header>
    <el-container class="container">
      <el-main class="main">
        <div class="main-container">
          <div class="main-left">
            <div class="main-title" :style="{ color: 'var(--el-color-error)' }">Error: 403</div>
            <div class="main-title">{{ siteConfig.openName }}</div>
            <div class="main-content">
              {{ $t("401.noPowerTip") }}
            </div>
            <el-button class="container-button" color="#ffffff" size="large" @click="$router.back()">
              {{ $t("404.Back to previous page") }}
            </el-button>
            <el-button class="container-button" color="#ffffff" size="large" @click="$router.push('/')">
              {{ $t("404.Return to home page") }}
            </el-button>
          </div>
          <div class="main-right">
            <img :src="indexLogo" alt="" />
          </div>
        </div>
      </el-main>
    </el-container>
    <Footer />
  </div>
</template>

<script setup lang="ts" name="MainHome">
import { useSiteConfig } from "@/stores/siteConfig";

import indexLogo from "@/assets/index/no-permission.svg";
import Footer from "../components/footer.vue";
import { editDefaultLang } from "@/lang/index";

import { useConfig } from "@/stores/config";

const config = useConfig();
const siteConfig = useSiteConfig();
</script>

<style scoped lang="scss">
.container-button {
  margin: 0 15px 15px 0;
}
.container {
  width: 100vw;
  height: 100vh;
  background: url(@/assets/bg.jpg) repeat;
  color: var(--el-color-white);
  .main {
    height: calc(100vh - 120px);
    padding: 0;
    .main-container {
      display: flex;
      height: 100%;
      width: 66%;
      margin: 0 auto;
      align-items: center;
      justify-content: space-between;
      .main-left {
        padding-right: 50px;
        .main-title {
          font-size: 45px;
        }
        .main-content {
          padding-top: 20px;
          padding-bottom: 40px;
          font-size: var(--el-font-size-large);
        }
      }
      .main-right {
        img {
          width: 380px;
        }
      }
    }
  }
}
.header {
  background-color: transparent !important;
  box-shadow: none !important;
  position: fixed;
  width: 100%;
  :deep(.header-logo) {
    span {
      padding-left: 4px;
      color: var(--el-color-white);
    }
  }
  :deep(.frontend-header-menu) {
    background: transparent;
    .el-menu-item,
    .el-sub-menu .el-sub-menu__title {
      color: var(--el-color-white);
      &.is-active {
        color: var(--el-color-white) !important;
      }
      &:hover {
        background-color: transparent;
        color: var(--el-menu-hover-text-color);
      }
    }
  }
}
.el-header {
  padding: 0;
}
.header-row {
  display: flex;
}
.user-menus-expand {
  display: flex;
  height: 60px;
  align-items: center;
  justify-content: center;
}
.header-logo {
  display: flex;
  height: 60px;
  align-items: center;
  cursor: pointer;
  img {
    height: 34px;
    // width: 34px;
  }
  span {
    padding-left: 4px;
    font-size: var(--el-font-size-large);
  }
}
.switch-language {
  display: flex;
  align-items: center;
  span {
    padding-right: 4px;
  }
}
.el-menu--horizontal {
  margin-left: auto;
  border-bottom: none;
}
.el-menu--horizontal > .el-menu-item,
.el-menu--horizontal > :deep(.el-sub-menu) .el-sub-menu__title,
.el-menu--horizontal > .el-menu-item.is-active {
  border-bottom: none;
}
.footer {
  color: var(--el-text-color-secondary);
  background-color: transparent !important;
  position: fixed;
  bottom: 0;
}

@media screen and (max-width: 1024px) {
  .container {
    .main {
      height: unset;
    }
  }
  .main-container {
    width: 90% !important;
    flex-wrap: wrap;
    align-content: center;
    justify-content: center !important;
    .main-right {
      padding-top: 50px;
    }
  }
}
@media only screen and (max-width: 768px) {
  .header-logo {
    padding-left: 10px;
  }
  .user-menus-expand {
    padding-left: 20px;
  }
}
@media screen and (max-width: 425px) {
  :deep(.aside-drawer) {
    width: 70% !important;
  }
}
@media screen and (max-width: 375px) {
  .main-right img {
    width: 300px !important;
  }
}

@at-root .dark {
  .header-logo {
    .hidden-xs-only {
      color: var(--el-text-color-primary);
    }
  }
}
</style>
