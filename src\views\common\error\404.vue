<template>
  <el-container class="container">
    <el-main class="main">
      <div class="main-container">
        <!-- <div class="main-right" style="color: var(--el-color-primary)">
          <Icon name="local-not-found" size="380" color="var(--el-color-primary)"></Icon>
        </div> -->
        <div class="main-left">
          <!-- <div class="main-title" :style="{ color: 'var(--el-color-error)' }">Error: 403</div> -->
          <!-- <div class="main-title">{{ siteConfig.openName }}</div> -->
          <!-- <div class="main-content">
            {{ $t("404.problems tip") }}
          </div> -->
          <!-- <el-button class="container-button" color="#ffffff" size="large" @click="$router.back()">
            {{ $t("404.Back to previous page") }}
          </el-button> -->
          <!-- <el-button class="container-button" color="#ffffff" size="large" @click="$router.push('/')">
            {{ $t("404.Return to home page") }}
          </el-button> -->
        </div>
      </div>
    </el-main>
  </el-container>
  <Footer />
</template>

<script setup lang="ts" name="MainHome">
import { useSiteConfig } from "@/stores/siteConfig";

import Footer from "../components/footer.vue";

const siteConfig = useSiteConfig();
</script>

<style scoped lang="scss">
.container-button {
  margin: 0 15px 15px 0;
}
.container {
  width: 100%;
  height: 100%;
  // background: url(@/assets/bg.jpg) repeat;
  // background: linear-gradient(217deg, rgba(255, 0, 0, 0.8), rgba(255, 0, 0, 0) 70.71%), linear-gradient(127deg, rgba(0, 255, 0, 0.8), rgba(0, 255, 0, 0) 70.71%), linear-gradient(336deg, rgba(0, 0, 255, 0.8), rgba(0, 0, 255, 0) 70.71%);
  // background: linear-gradient(45deg, rgba(255, 255, 255, 0) 10%, rgba(198, 185, 255, 0.2) 13%, rgba(255, 196, 242, 0.5) 25%, rgba(255, 177, 238, 0.5) 50%, rgba(180, 229, 255, 0.5) 60%, rgba(255, 255, 255, 0) 65%, rgba(168, 181, 252, 0.2) 75%, rgba(255, 255, 255, 0) 90%);
  // background: transparent radial-gradient(transparent 2px, rgba(255, 0, 0, 0.1) 1px) repeat center / 4px 4px;
  color: var(--el-text-color-secondary);
  border-radius: 6px;
  .main {
    height: calc(100% - 120px);
    padding: 0;
    .main-container {
      display: flex;
      height: 100%;
      width: 66%;
      margin: 0 auto;
      flex-direction: column;
      align-items: center;
      // justify-content: space-between;
      justify-content: center;
      .main-left {
        padding-right: 50px;
        .main-title {
          font-size: 45px;
        }
        .main-content {
          padding-top: 20px;
          padding-bottom: 40px;
          font-size: var(--el-font-size-large);
        }
      }
      .main-right {
        img {
          width: 380px;
        }
      }
    }
  }
}
.header {
  background-color: transparent !important;
  box-shadow: none !important;
  position: fixed;
  width: 100%;
  :deep(.header-logo) {
    span {
      padding-left: 4px;
      color: var(--el-text-color-secondary);
    }
  }
  :deep(.frontend-header-menu) {
    background: transparent;
    .el-menu-item,
    .el-sub-menu .el-sub-menu__title {
      color: var(--el-text-color-secondary);
      &.is-active {
        color: var(--el-text-color-secondary) !important;
      }
      &:hover {
        background-color: transparent;
        color: var(--el-menu-hover-text-color);
      }
    }
  }
}

.footer {
  color: var(--el-text-color-secondary);
  background: transparent radial-gradient(transparent 2px, var(--el-bg-color) 1px) repeat center / 4px 4px;
  backdrop-filter: saturate(50%) blur(4px);
}
.el-header {
  padding: 0;
}
.header-row {
  display: flex;
}
.user-menus-expand {
  display: flex;
  height: 60px;
  align-items: center;
  justify-content: center;
}
.header-logo {
  display: flex;
  height: 60px;
  align-items: center;
  cursor: pointer;
  img {
    height: 34px;
    // width: 34px;
  }
  span {
    padding-left: 4px;
    font-size: var(--el-font-size-large);
  }
}
.switch-language {
  display: flex;
  align-items: center;
  span {
    padding-right: 4px;
  }
}
.el-menu--horizontal {
  margin-left: auto;
  border-bottom: none;
}
.el-menu--horizontal > .el-menu-item,
.el-menu--horizontal > :deep(.el-sub-menu) .el-sub-menu__title,
.el-menu--horizontal > .el-menu-item.is-active {
  border-bottom: none;
}

@media screen and (max-width: 1024px) {
  .container {
    .main {
      height: unset;
    }
  }
  .main-container {
    width: 90% !important;
    flex-wrap: wrap;
    align-content: center;
    justify-content: center !important;
    .main-right {
      padding-top: 50px;
    }
  }
}
@media only screen and (max-width: 768px) {
  .header-logo {
    padding-left: 10px;
  }
  .user-menus-expand {
    padding-left: 20px;
  }
}
@media screen and (max-width: 425px) {
  :deep(.aside-drawer) {
    width: 70% !important;
  }
}
@media screen and (max-width: 375px) {
  .main-right img {
    width: 300px !important;
  }
}

@at-root .dark {
  .header-logo {
    .hidden-xs-only {
      color: var(--el-text-color-primary);
    }
  }
}
</style>
