<template>
  <div class="index-page">
    <div class="banner">
      <div class="slide slide1">
        <div class="bg-wrapper" id="J_bgWrapper1">
          <div class="bg" id="J_bg1"></div>
          <div class="mask"></div>
        </div>
        <div class="content-wrapper">
          <div class="content-inner">
            <div class="title">
              <img src="~@/assets/logo.png" width="354" height="86" class="tw-mx-auto" />
              {{ siteConfig.siteList[adminBaseRoute.name].openName }}
            </div>
            <div class="description">多租户端管理利器，打造专属运管。</div>
            <div class="products">
              <el-button type="default" size="large" @click="$router.push({ name: adminBaseRoute.name, query: $route.query })">进入后台</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="slide slide2">
        <div class="bg-wrapper" id="J_bgWrapper2">
          <div class="bg" id="J_bg2"></div>
          <div class="mask"></div>
        </div>
        <div class="content-wrapper">
          <div class="content-inner">
            <div class="title">
              <img src="~@/assets/logo.png" width="354" height="86" class="tw-mx-auto" />
              {{ siteConfig.siteList[superBaseRoute.name].openName }}
            </div>
            <div class="description">平台端管理利器，打造专属运管。</div>
            <div class="products">
              <el-button type="default" size="large" @click="$router.push({ name: superBaseRoute.name, query: $route.query })">进入后台</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="MainHome">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { useSiteConfig } from "@/stores/siteConfig";
import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";

const siteConfig = useSiteConfig();
</script>

<style scoped lang="scss">
.index-page {
  position: relative;
  background: black;
  height: 100%;
  min-height: 750px;
  overflow: hidden;
  .banner {
    overflow: hidden;
    white-space: nowrap;
    height: 100%;

    &:hover {
      .slide1 {
        transform: translate3d(-5%, 0, 0);
      }
      .slide2 {
        transform: translate3d(5%, 0, 0);
      }
      .content-wrapper {
        opacity: 0.4;
      }
      .slide .bg-wrapper .mask {
        opacity: 0.5;
      }
    }

    .slide {
      width: 80%;
      height: 100%;
      display: inline-block;
      zoom: 1;
      padding: 0;
      position: relative;
      transition: all 0.8s ease;
      text-align: center;
      .bg-wrapper {
        position: relative;
        height: 100%;
        overflow: hidden;
        transform: skew(-5deg) translateZ(0);
      }
      .bg-wrapper .bg {
        position: absolute;
        top: 0;
        background-repeat: no-repeat;
        background-size: cover;
        width: 100%;
        height: 100%;
        transition: all 0.8s ease;
        transform: skew(5deg) translateZ(0);
      }

      .bg-wrapper .mask {
        position: absolute;
        top: 0;
        width: 150%;
        height: 100%;
        background-color: black;
        opacity: 0;
        transition: opacity 0.8s;
      }

      &:hover {
        .title {
          transform: translate3d(0, -29px, 0);
        }
        .description {
          opacity: 1;
        }
        .bg-wrapper .mask {
          opacity: 0;
        }
        .bg-wrapper .bg {
          transform: skew(5deg) scale(1.1) translateZ(0);
        }
        .content-wrapper {
          opacity: 1;
        }
      }

      .content-wrapper {
        position: absolute;
        text-align: center;
        width: 62.5%;
        height: 100%;
        top: 0;
        z-index: 50;
        transition: all 0.8s ease;
      }
      .content-wrapper .content-inner {
        position: absolute;
        top: 50%;
        left: 50%;
        margin-top: -195px;
        margin-left: -235px;
      }

      .title {
        width: 490px;
        height: 186px;
        margin: 0 auto 0;
        transition: all 0.8s;
        font-size: 32px;
        line-height: 100px;
        color: #fff;
      }

      .description {
        color: white;
        font-size: 18px;
        margin-bottom: 65px;
        text-align: center;
        opacity: 0;
        transition: opacity 0.8s ease;
        text-shadow: 5px 5px 15px rgba(0, 0, 0, 0.5);
      }

      .products {
        white-space: nowrap;
        text-align: center;
        .product {
          display: inline-block;
          zoom: 1;
          width: 100px;
          color: white;
          margin: 0 30px;
          .icon {
            width: 82px;
            height: 82px;
            margin: 0 auto 18px;
            background-repeat: no-repeat;
            background-size: 79%;
            background-position: center;
            transition: background-image 0.25s;
          }

          .name {
            text-align: center;
            margin-bottom: 23px;
            font-size: 20px;
          }

          .more {
            font-size: 14px;
            text-decoration: underline !important;
            color: #ccc;
          }

          .caret {
            display: inline-block;
            width: 0;
            height: 0;
            border-width: 4px 0 4px 7px;
            border-color: transparent transparent transparent #ccc;
            border-style: dashed dashed dashed solid;
          }
        }
      }
    }

    .slide1 {
      margin-left: -30%;
      .bg {
        background-image: url("@/assets/red-1300-1200.jpg");
        margin-left: 20%;
      }

      .bg-wrapper {
        margin-right: -20px;
        .bg,
        .mask {
          right: -10%;
        }
      }

      .content-wrapper {
        right: 0;
        margin-left: 30%\9;
      }

      &:hover {
        transform: translate3d(5%, 0, 0);
        .content-wrapper {
          width: 75%;
        }
        .bg {
          transform: translate3d(-3%, 0, 0) skew(5deg);
        }
      }
    }

    .slide2 {
      .bg {
        margin-left: -20%;
        background-image: url("@/assets/blue-1300-1200.jpg");
      }

      .bg-wrapper {
        margin-left: -20px;
        .bg,
        .mask {
          left: 0;
        }
      }

      .content-wrapper {
        left: 0;
        margin-right: 30%\9;
      }

      &:hover {
        transform: translate3d(-5%, 0, 0);
        .content-wrapper {
          width: 75%;
        }

        .bg {
          transform: translate3d(3%, 0, 0) skew(5deg);
        }
      }
    }
  }
}
</style>
