<template>
  <el-container class="tw-h-full tw-bg-white">
    <el-header height="60px">
      <div class="tw-h-full">
        <div class="home-item content tw-flex tw-h-full tw-items-center">
          <svg class="tw-flex-shrink-0" width="197" height="50" viewBox="0 0 197 50" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_3_13)">
              <path d="M31.4336 13.0452L31.4339 13.0455C32.264 13.7887 32.9955 14.5962 33.6224 15.4586C34.009 15.4203 34.3973 15.402 34.7929 15.402L31.4336 13.0452ZM31.4336 13.0452C28.7851 10.6948 25.3592 9.40195 21.793 9.40195C16.7928 9.40195 12.1989 11.9214 9.5046 16.1401C9.02886 16.8847 9.24648 17.8746 9.99314 18.3503C10.7398 18.8261 11.7278 18.6042 12.2015 17.8618C14.3071 14.5665 17.8912 12.6 21.793 12.6C24.5767 12.6 27.247 13.6071 29.3045 15.4305C30.1797 16.2145 30.9153 17.0872 31.494 18.0206C31.8397 18.5837 32.4926 18.876 33.1494 18.7523C33.6952 18.6495 34.2331 18.6 34.793 18.6C39.4257 18.6 43.193 22.3672 43.193 27C43.193 31.6328 39.4257 35.4 34.793 35.4H26.793C25.9097 35.4 25.193 36.1168 25.193 37C25.193 37.8832 25.9097 38.6 26.793 38.6H34.793C41.1902 38.6 46.393 33.3972 46.393 27M31.4336 13.0452L46.393 27M46.393 27C46.393 20.6028 41.1902 15.4 34.793 15.402L46.393 27ZM18.127 29.928C20.4782 29.9279 22.393 28.0152 22.393 25.662C22.393 23.3108 20.4782 21.396 18.127 21.396C15.8085 21.396 13.9209 23.2533 13.864 25.5557L9.92061 27.5275C9.22419 27.0313 8.3799 26.73 7.45898 26.73C5.10776 26.73 3.19297 28.6447 3.19297 30.996C3.19297 33.3472 5.10776 35.262 7.45898 35.262C8.37989 35.262 9.22418 34.9608 9.92061 34.4646L13.864 36.4364C13.9209 38.7388 15.8085 40.596 18.127 40.596C20.4781 40.596 22.393 38.6832 22.393 36.33C22.393 33.9787 20.4782 32.0639 18.127 32.0639C16.8939 32.0639 15.7951 32.5976 15.0162 33.4348L11.6572 31.7563C11.7016 31.5102 11.727 31.2569 11.727 30.998C11.727 30.7392 11.7016 30.4858 11.6572 30.2397L15.0161 28.5612C15.7931 29.3984 16.8939 29.9339 18.127 29.928ZM18.127 29.928C18.1272 29.928 18.1273 29.928 18.1274 29.928L18.127 29.828V29.928C18.127 29.928 18.127 29.928 18.127 29.928ZM18.1266 35.268H18.127C18.7138 35.268 19.193 35.7453 19.193 36.334C19.193 36.9208 18.7137 37.4 18.127 37.4C17.538 37.4 17.059 36.9225 17.059 36.334C17.059 35.7451 17.5382 35.2661 18.1266 35.268ZM18.127 26.732C17.538 26.732 17.059 26.2546 17.059 25.666C17.059 25.0794 17.5381 24.6 18.127 24.6C18.7138 24.6 19.193 25.0774 19.193 25.666C19.193 26.2528 18.7137 26.732 18.127 26.732ZM8.52695 31C8.52695 31.5867 8.04786 32.066 7.45898 32.066C6.87221 32.066 6.39297 31.5868 6.39297 31C6.39297 30.4132 6.87221 29.934 7.45898 29.934C8.04786 29.934 8.52695 30.4133 8.52695 31Z" fill="#FA541C" stroke="#FA541C" stroke-width="0.2" />
            </g>
            <path d="M33.1865 18.9489L33.1864 18.9489C32.4482 19.0879 31.713 18.759 31.3239 18.1257C30.7559 17.2096 30.033 16.3516 29.1715 15.5798C27.1511 13.7895 24.5283 12.8 21.793 12.8C17.9598 12.8 14.4392 14.7314 12.3702 17.9693L33.1865 18.9489ZM33.1865 18.9489C33.7198 18.8484 34.2453 18.8 34.793 18.8C39.3153 18.8 42.993 22.4777 42.993 27C42.993 31.5223 39.3153 35.2 34.793 35.2H26.793C25.7993 35.2 24.993 36.0063 24.993 37C24.993 37.9937 25.7993 38.8 26.793 38.8H34.793C41.3007 38.8 46.593 33.5077 46.593 27M33.1865 18.9489L46.593 27M31.5664 12.8956L31.5674 12.8965C32.3781 13.6223 33.0964 14.4092 33.7167 15.2489C34.0725 15.2171 34.4299 15.202 34.7929 15.202L31.5664 12.8956ZM31.5664 12.8956C28.8809 10.5124 25.4077 9.20195 21.793 9.20195C16.7245 9.20195 12.0671 11.7563 9.33613 16.0324M31.5664 12.8956L9.58893 16.194L9.33613 16.0324M46.593 27C46.593 20.4924 41.3008 15.2 34.793 15.202L46.593 27ZM9.33613 16.0324C8.80092 16.8701 9.04574 17.9838 9.88574 18.519C10.7258 19.0542 11.8372 18.8046 12.37 17.9695L9.33613 16.0324Z" fill="#FA541C" stroke="#FA541C" stroke-width="0.6" />
            <path d="M71.157 20.744V23.152H79.641V20.744H71.157ZM71.157 15.76V18.112H79.641V15.76H71.157ZM68.133 12.904H82.833V25.98H68.133V12.904ZM67.937 28.388H83.141V31.412H67.937V28.388ZM65.977 34.1H84.065V37.124H65.977V34.1ZM57.773 13.324H67.069V16.46H57.773V13.324ZM58.053 21.388H66.593V24.496H58.053V21.388ZM57.465 32.028C59.873 31.412 63.457 30.292 66.817 29.2L67.405 32.42C64.269 33.484 60.909 34.576 58.221 35.444L57.465 32.028ZM60.881 14.556H64.073V31.776L60.881 32.28V14.556ZM74.041 14.108H76.869V24.664H77.177V35.528H73.733V24.664H74.041V14.108ZM92.101 29.732H95.405V33.54C95.405 34.38 95.685 34.464 97.225 34.464C97.925 34.464 100.781 34.464 101.705 34.464C103.021 34.464 103.217 34.072 103.357 31.244C104.085 31.776 105.485 32.252 106.381 32.448C105.989 36.452 105.065 37.46 101.957 37.46C101.145 37.46 97.645 37.46 96.889 37.46C93.053 37.46 92.101 36.536 92.101 33.596V29.732ZM96.133 29.368L98.429 27.52C99.689 28.64 101.425 30.236 102.293 31.244L99.857 33.316C99.073 32.28 97.393 30.572 96.133 29.368ZM105.597 30.152L108.537 28.78C109.685 30.516 111.309 32.924 112.065 34.38L108.929 35.976C108.257 34.492 106.717 32 105.597 30.152ZM88.069 29.172L91.065 30.32C90.533 32.336 89.693 34.856 88.685 36.48L85.633 34.968C86.669 33.456 87.593 31.16 88.069 29.172ZM86.109 15.732H97.897V18.56H86.109V15.732ZM90.757 11.756H93.949V28.612H90.757V11.756ZM90.645 17.608L92.941 18.336C91.793 21.724 89.749 25.224 87.593 27.184C87.089 26.456 86.109 25.364 85.437 24.832C87.537 23.292 89.581 20.352 90.645 17.608ZM93.753 19.484C94.705 20.016 97.645 22.116 98.401 22.676L96.637 25.364C95.545 24.216 93.193 22.06 92.017 21.136L93.753 19.484ZM102.041 19.876V21.528H107.221V19.876H102.041ZM102.041 23.964V25.644H107.221V23.964H102.041ZM102.041 15.788V17.384H107.221V15.788H102.041ZM98.989 13.128H110.441V28.276H98.989V13.128ZM114.137 30.796H139.505V33.456H114.137V30.796ZM120.773 23.152V24.16H133.233V23.152H120.773ZM120.773 26.064V27.1H133.233V26.064H120.773ZM120.773 20.268V21.276H133.233V20.268H120.773ZM117.385 18.252H136.789V29.116H117.385V18.252ZM130.097 28.584H133.569V37.964H130.097V28.584ZM117.553 13.548H126.373V16.208H117.553V13.548ZM128.669 13.548H139.393V16.208H128.669V13.548ZM117.665 11.504L120.801 12.372C119.653 14.836 117.777 17.356 116.069 18.924C115.425 18.392 114.109 17.524 113.353 17.104C115.117 15.76 116.769 13.604 117.665 11.504ZM129.061 11.504L132.253 12.288C131.301 14.724 129.593 17.104 127.997 18.588C127.297 18.084 125.841 17.356 125.029 17.02C126.793 15.704 128.277 13.604 129.061 11.504ZM118.673 15.592L121.445 14.668C122.145 15.536 122.929 16.768 123.265 17.608L120.353 18.644C120.045 17.832 119.345 16.516 118.673 15.592ZM130.993 15.564L133.681 14.444C134.577 15.312 135.669 16.544 136.173 17.44L133.345 18.672C132.897 17.832 131.889 16.488 130.993 15.564ZM120.801 28.556H124.189V31.244C124.189 33.876 122.761 36.452 116.601 38.02C116.153 37.32 115.229 36.2 114.501 35.556C119.877 34.52 120.801 32.644 120.801 31.188V28.556ZM142.893 17.552H164.341V20.996H142.893V17.552ZM162.969 17.552H166.469C166.469 17.552 166.441 18.7 166.385 19.176C165.825 30.376 165.265 34.632 164.005 36.116C163.193 37.18 162.381 37.488 161.149 37.656C160.085 37.824 158.349 37.796 156.641 37.712C156.585 36.732 156.109 35.22 155.465 34.24C157.257 34.38 158.909 34.408 159.665 34.408C160.225 34.408 160.561 34.296 160.953 33.932C161.933 32.98 162.493 28.556 162.969 18.224V17.552ZM151.489 11.784H155.045V17.552C155.045 23.908 154.149 32.784 144.629 38.16C144.097 37.348 142.837 36.032 141.997 35.444C150.705 30.712 151.489 22.956 151.489 17.552V11.784ZM173.301 13.52H192.565V16.992H173.301V13.52ZM170.137 21.668H195.477V25.14H170.137V21.668ZM178.369 23.74L182.513 24.776C181.057 28.388 179.209 32.392 177.669 34.968L174.449 33.932C175.905 31.16 177.529 26.96 178.369 23.74ZM170.781 33.204C176.269 33.064 184.837 32.756 192.621 32.448L192.481 35.668C184.837 36.116 176.549 36.536 171.033 36.76L170.781 33.204ZM186.013 28.5L189.289 26.96C191.837 29.76 194.413 33.512 195.645 36.172L192.173 38.048C191.109 35.416 188.449 31.384 186.013 28.5Z" fill="#FA541C" />
          </svg>

          <div class="tw-mx-[30px] tw-w-full tw-flex-shrink">
            <el-link class="tw-mx-[16px] max-sm:tw-hidden" :underline="false" @click.prevent>首页</el-link>
            <el-link class="tw-mx-[16px] max-sm:tw-hidden" :underline="false" @click.prevent>优势</el-link>
            <el-link class="tw-mx-[16px] max-sm:tw-hidden" :underline="false" @click.prevent>适用场景</el-link>
          </div>

          <div class="tw-flex-shrink-0">
            <el-button type="primary" size="default" @click="$router.push({ name: adminBaseRoute.name, query: $route.query })">立即开始</el-button>
          </div>
        </div>
      </div>
    </el-header>
    <el-main class="tw-m-0 tw-p-0">
      <el-scrollbar height="100%">
        <div class="tw-relative tw-w-full">
          <img src="@/assets/login-header.png" alt="" class="tw-min-h-[380px] tw-min-w-[620px]" style="width: 100%" />
          <div class="home-item content tw-absolute tw-left-[50%] tw-top-[50%] tw-translate-x-[-50%] tw-translate-y-[-50%]">
            <h1 class="tw-mb-10 tw-text-2xl tw-font-bold">理想算力云</h1>
            <h2 class="tw-mb-4 tw-text-3xl tw-font-bold">即开即用 弹性使用 方便用户一站式构建AI算法</h2>
            <p class="tw-mb-8 tw-w-1/2">基于容器GPU 虚拟化技术，通过混合云算力云，让您可以在任何云上轻松且经济高效地进行AI算法的开发</p>
            <el-button type="primary" size="default" @click="$router.push({ name: adminBaseRoute.name, query: $route.query })">立即开始</el-button>
          </div>
          <div class="home-item content tw-absolute tw-bottom-0 tw-left-[50%] tw-z-10 tw-translate-x-[-50%] tw-translate-y-[50%] max-[767px]:tw-translate-y-[calc(100%+24px)]">
            <el-card :body-style="{ backgroundImage: 'linear-gradient(#F1F5FB, #FEFEFF)' }">
              <el-row :gutter="20">
                <el-col :span="24" :offset="0" :sm="12">
                  <div class="tw-flex tw-h-[84px] tw-items-center">
                    <img src="@/assets/icon1.svg" alt="" />
                    <div class="tw-ml-4">
                      <p><span>¥</span><span class="tw-m-2 tw-text-2xl tw-font-bold">0.68</span><span>/小时</span></p>
                      <p><span class="tw-mr-2">GPU实例/低至</span><span class="tw-align-bottom tw-text-xs tw-line-through">0.98/小时</span></p>
                    </div>
                  </div>
                </el-col>
                <el-col :span="24" :offset="0" :sm="12">
                  <div class="tw-flex tw-h-[84px] tw-items-center">
                    <img src="@/assets/icon2.svg" alt="" />
                    <div class="tw-ml-4">
                      <p><span>¥</span><span class="tw-m-2 tw-text-2xl tw-font-bold">0.12</span><span>/GB/月</span></p>
                      <p><span class="tw-mr-2">持久化存储/仅需</span><span class="tw-align-bottom tw-text-xs tw-line-through">0.15/GB/月</span></p>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-card>
          </div>
        </div>
        <!--  -->
        <div class="tw-relative tw-w-full tw-bg-[#F9FBFF] tw-pt-[50px] max-[767px]:tw-pt-[236px]">
          <div class="home-item content tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-py-[50px] tw-text-center">
            <h3 class="tw-mb-8 tw-text-3xl tw-font-bold">我们的优势</h3>
            <div class="tw-w-full">
              <el-row :gutter="20">
                <el-col class="tw-mb-[10px]" :span="12" :sm="6">
                  <div class="tw-flex tw-h-full tw-flex-col tw-justify-center tw-bg-white tw-p-[20px]">
                    <img src="@/assets/home-icon1.png" alt="" class="tw-mx-auto tw-mb-4" />
                    <h4 class="title-fonnt tw-font-semibold">算力按需选取配置</h4>
                    <p class="text-fonnt">在控制台创建训练任务的同时即可以指定所需算力资源的规格和数量,支持动态增减可用算力资源</p>
                  </div>
                </el-col>
                <el-col class="tw-mb-[10px]" :span="12" :sm="6">
                  <div class="tw-flex tw-h-full tw-flex-col tw-justify-center tw-bg-white tw-p-[20px]">
                    <img src="@/assets/home-icon2.png" alt="" class="tw-mx-auto tw-mb-4" />
                    <h4 class="title-fonnt tw-font-semibold">灵活的购买方式</h4>
                    <p class="text-fonnt">支持分钟级按需的实时计费模式,训练任务完成之后即退订算力资源,只收取存储费用,有效控制成本</p>
                  </div>
                </el-col>
                <el-col class="tw-mb-[10px]" :span="12" :sm="6">
                  <div class="tw-flex tw-h-full tw-flex-col tw-justify-center tw-bg-white tw-p-[20px]">
                    <img src="@/assets/home-icon3.png" alt="" class="tw-mx-auto tw-mb-4" />
                    <h4 class="title-fonnt tw-font-semibold">一站式AI开放平台</h4>
                    <p class="text-fonnt">面向AI模型生产的生命周期,提供了数据处理、模型开发、模型训练和模型管理等功能,方便用户一站式构建AI算法</p>
                  </div>
                </el-col>
                <el-col class="tw-mb-[10px]" :span="12" :sm="6">
                  <div class="tw-flex tw-h-full tw-flex-col tw-justify-center tw-bg-white tw-p-[20px]">
                    <img src="@/assets/home-icon4.png" alt="" class="tw-mx-auto tw-mb-4" />
                    <h4 class="title-fonnt tw-font-semibold">稳定安全</h4>
                    <p class="text-fonnt">采用高可靠性设计,用户环境相互独立、环境隔离,业务互不干扰,充分保护客户隐私</p>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
        <!--  -->
        <div class="tw-relative tw-w-full">
          <div class="home-item content tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-py-[50px] tw-text-center">
            <h3 class="tw-mb-8 tw-text-3xl tw-font-bold">应用场景</h3>
            <el-row :gutter="20">
              <el-col :span="12" :offset="0">
                <div class="tw-relative tw-overflow-hidden">
                  <img src="@/assets/about1.png" alt="" class="tw-relative tw-left-[-1px] tw-min-h-[160px]" />
                  <div class="tw-absolute tw-left-[50%] tw-top-[50%] tw-w-full tw-translate-x-[-50%] tw-translate-y-[-50%] tw-px-[20px] tw-text-center">
                    <h4 class="title-fonnt tw-font-semibold">教育科研</h4>
                    <p class="text-fonnt tw-min-h-[9em]">为了方便人工智能相关专业的教研、教学、学生实践;学校基于混合云算力云实现了资源的流程化、自动化、精细化管理,提高了教学质量和教研效率的同时,降低建设成本和维护成本.</p>
                  </div>
                </div>
              </el-col>
              <el-col :span="12" :offset="0">
                <div class="tw-relative tw-overflow-hidden">
                  <img src="@/assets/about2.png" alt="" class="tw-relative tw-left-[-1px] tw-min-h-[160px]" />
                  <div class="tw-absolute tw-left-[50%] tw-top-[50%] tw-w-full tw-translate-x-[-50%] tw-translate-y-[-50%] tw-px-[20px] tw-text-center">
                    <h4 class="title-fonnt tw-font-semibold">AI创新企业</h4>
                    <p class="text-fonnt tw-min-h-[9em]">通过理想算力云为用户提供可靠算力,做到统一的调度、分配、管理和回收,进而提高算力的利用率.资源池化让用户灵活应对短时间内大量计算需求,可以在任何云上轻松且经济高效地进行AI算法的开发</p>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        <!--  -->
        <div class="tw-relative tw-w-full">
          <img src="@/assets/login-bottom.png" alt="" class="tw-min-h-[160px] tw-min-w-[620px]" style="width: 100%" />
          <div class="home-item content tw-absolute tw-left-[50%] tw-top-[50%] tw-translate-x-[-50%] tw-translate-y-[-50%] tw-text-center">
            <h3 class="tw-mb-8 tw-text-3xl tw-font-bold">理想算力云</h3>
            <el-button type="primary" size="default" @click="$router.push({ name: adminBaseRoute.name, query: $route.query })">立即开始</el-button>
          </div>
        </div>
        <!--  -->
        <el-footer class="tw-bg-[#2E354D] tw-pb-[80px] tw-text-white" height="auto">
          <div class="tw-flex tw-pt-[50px]">
            <div class="home-item content tw-flex tw-h-full tw-w-[calc(100%/5*4)] tw-items-start tw-justify-around max-md:tw-flex-col max-md:tw-items-center">
              <ul class="tw-mt-[30px] tw-w-[120px]">
                <li class="tw-mb-4 tw-text-lg">产品与服务</li>
                <li class="tw-text-[#AEB1B6]">了解产品</li>
                <li class="tw-text-[#AEB1B6]">解决方案</li>
                <li class="tw-text-[#AEB1B6]">企业指南</li>
              </ul>
              <ul class="tw-mt-[30px] tw-w-[120px]">
                <li class="tw-mb-4 tw-text-lg">关于我们</li>
                <li class="tw-text-[#AEB1B6]">公司简介</li>
              </ul>
              <ul class="tw-mt-[30px] tw-w-[120px]">
                <li class="tw-mb-4 tw-text-lg">联系我们</li>
                <li class="tw-text-[#AEB1B6]">商务合作</li>
                <li class="tw-text-[#AEB1B6]">意见反馈</li>
                <li class="tw-text-[#AEB1B6]">企业指南</li>
              </ul>
              <ul class="tw-mt-[30px] tw-w-[120px]">
                <li class="tw-mb-4 tw-text-lg">友情链接</li>
                <li class="tw-text-[#AEB1B6]">混合云</li>
              </ul>
            </div>
            <div class="home-item content tw-flex tw-h-full tw-w-[calc(100%/5*1)] tw-items-start tw-justify-around max-md:tw-flex-col max-md:tw-items-center">
              <ul class="tw-mt-[30px] tw-w-[120px]">
                <li class="tw-mb-4 tw-text-lg">关注我们</li>
                <li class="tw-text-[#AEB1B6]">
                  <img src="@/assets/about_qr.png" alt="" width="80" height="80" />
                </li>
              </ul>
            </div>
          </div>
        </el-footer>
      </el-scrollbar>
    </el-main>
  </el-container>
</template>

<script setup lang="ts" name="MainHome">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { useSiteConfig } from "@/stores/siteConfig";
import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";

const siteConfig = useSiteConfig();
</script>

<style scoped lang="scss">
// .home-item {
//   background-color: pink;
// }
</style>
