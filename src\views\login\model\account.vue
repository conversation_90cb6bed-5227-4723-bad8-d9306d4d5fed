<template>
  <div class="tw-mb-11 tw-text-center" @keyup.enter.stop="submit(active)">
    <h1 class="tw-mb-3 tw-text-2xl tw-font-black">{{ $t("adminLogin.Sign in") }}</h1>
    <p class="tw-text-[var(--el-text-color-placeholder)]">登录方式</p>
    <div class="tw-mt-4">
      <el-button v-if="methods.includes(loginChannels.PASSWORD)" :disabled="loading" :type="active === loginChannels.PASSWORD ? 'primary' : 'default'" circle plain :icon="Lock" @click="() => (active = loginChannels.PASSWORD)"></el-button>
      <!-- <el-button v-if="methods.includes(loginChannels.REFRESH_TOKEN)" :disabled="loading" :type="active === loginChannels.REFRESH_TOKEN ? 'primary' : 'default'" circle plain :icon="Loading" @click="active = loginChannels.REFRESH_TOKEN"></el-button> -->
      <el-button v-if="methods.includes(loginChannels.SMS_CODE)" :disabled="loading" :type="active === loginChannels.SMS_CODE ? 'primary' : 'default'" circle plain :icon="Iphone" @click="() => (active = loginChannels.SMS_CODE)"></el-button>
      <el-button v-if="methods.includes(loginChannels.GIT_HUB)" :disabled="loading" :type="active === loginChannels.GIT_HUB ? 'primary' : 'default'" circle plain :icon="h(FontAwesomeIcon, { icon: faGithub })" @click="() => submit(loginChannels.GIT_HUB)"></el-button>
      <el-button v-if="methods.includes(loginChannels.WECHAT)" :disabled="loading" :type="active === loginChannels.WECHAT ? 'primary' : 'default'" circle plain :icon="h(FontAwesomeIcon, { icon: faWeixin })" @click="() => submit(loginChannels.WECHAT)"></el-button>
    </div>
    <div style="padding: 10px 0; box-sizing: border-box">通过{{ loginMethods?.label }}登录</div>
    <template v-if="!active"></template>
    <template v-else-if="active === loginChannels.PASSWORD">
      <el-form-item prop="username" :rules="[{ required: true, message: $t('glob.Please input field', { field: '账号' }) }]">
        <el-input v-model="$form.username" :prefix-icon="User" type="text" clearable :placeholder="$t('adminLogin.Please enter an account')"></el-input>
      </el-form-item>

      <el-form-item prop="password" :rules="[{ required: true, message: $t('glob.Please input field', { field: '密码' }) }]">
        <el-input v-model="$form.password" :prefix-icon="Lock" type="password" :placeholder="$t('adminLogin.Please input a password')" show-password></el-input>
      </el-form-item>

      <!-- <el-form-item prop="captcha" :rules="[buildValidatorData({ name: 'required', title: '图形验证码' }), { type: 'string', min: 4, max: 8, message: '验证码需要在4-8位字符', trigger: 'blur' }]">
        <el-input v-model="$form.captcha" :prefix-icon="ChatDotSquare" type="text" :placeholder="$t('adminLogin.Please enter the verification code')" clearable autocomplete="off" :style="{ verticalAlign: 'top', width: 'calc(100% - 118px)', marginRight: '12px' }"></el-input>
        <canvas ref="captchaImg" class="captcha-img tw-flex-shrink-0" height="40" width="100" title="看不清，换一张" @click="onChangeCaptcha()"></canvas>
      </el-form-item> -->

      <!-- <el-form-item prop="lengthen">
        <el-checkbox v-model="$form.lengthen" :label="$t('adminLogin.Hold session')"></el-checkbox>
      </el-form-item> -->

      <!-- <el-form-item prop="captcha" :rules="[{ required: true, message: $t('glob.Please input field', { field: '验证码' }) }]">
        <el-input v-model="$form.captcha" :prefix-icon="ChatDotSquare" type="text" :placeholder="$t('adminLogin.Please enter the verification code')" clearable autocomplete="off" :style="{ verticalAlign: 'top', width: 'calc(100% - 118px)', marginRight: '12px' }"></el-input>
        <canvas ref="captchaImg" class="captcha-img tw-flex-shrink-0" height="40" width="100" title="看不清，换一张" @click="onChangeCaptcha()"></canvas>
      </el-form-item> -->
    </template>
    <template v-else-if="active === loginChannels.REFRESH_TOKEN"></template>
    <template v-else-if="active === loginChannels.SMS_CODE">
      <el-form-item prop="phone" :rules="[buildValidatorData({ name: 'required', title: '手机号' }), buildValidatorData({ name: 'mobile', title: '手机号' })]">
        <el-input v-model="$form.phone" :prefix-icon="Phone" type="text" clearable :placeholder="$t('adminLogin.Please enter an Phone')">
          <!--  -->
        </el-input>
      </el-form-item>
      <el-form-item prop="code" :rules="[buildValidatorData({ name: 'required', title: '短信验证码' }), { type: 'string', min: 4, max: 8, message: '验证码需要在4-8位字符', trigger: 'blur' }]">
        <el-input v-model="$form.code" :prefix-icon="ChatDotSquare" type="text" placeholder="短信验证码">
          <template #suffix>
            <el-button link :loading="loadingCaptcha" :disabled="coolingCaptcha !== 0 || !$form.phone" class="submit-button" type="primary" size="small" @click="sendCaptcha()">{{ $t("adminLogin.Send verification code") }}{{ coolingCaptcha ? `(${coolingCaptcha}秒)` : "" }}</el-button>
          </template>
        </el-input>
      </el-form-item>
      <!-- <el-form-item prop="lengthen">
        <el-checkbox v-model="form.lengthen" :label="$t('adminLogin.Hold session')"></el-checkbox>
      </el-form-item> -->
    </template>
    <template v-else-if="active === loginChannels.GIT_HUB">
      <div class="tw-p-[24px]">
        <div class="loading-chase">
          <div v-for="i in 6" :key="`loading-chase-dot_${i}`" class="loading-chase-dot"></div>
        </div>
      </div>
    </template>
    <template v-else-if="active === loginChannels.WECHAT">
      <div class="tw-p-[24px]">
        <div class="loading-chase">
          <div v-for="i in 6" :key="`loading-chase-dot_${i}`" class="loading-chase-dot"></div>
        </div>
      </div>
    </template>
    <el-button class="tw-w-full" :loading="loading" type="primary" @click="submit(active)">{{ loginMethods?.label }}{{ $t("adminLogin.Sign in") }}</el-button>
    <div class="tw-mt-4 tw-flex tw-items-center tw-justify-center">
      <el-link :disabled="loading" @click="() => emits('change', operateType.RETRIEVE)">{{ $t("user.user.Forgot your password") }}</el-link>
      <el-divider direction="vertical"></el-divider>
      <span class="userManage">联系管理员</span>
      <!-- <el-link :disabled="loading" @click="() => changeType(operateType.SIGNIN)">{{ $t("user.user.Register your user") }}</el-link> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, useModel, watch, inject, nextTick, h } from "vue";
import { useRoute } from "vue-router";
import { User, Lock, Phone, Iphone, ChatDotSquare } from "@element-plus/icons-vue";
import { operateType, formRefKey, inputCertificateKey, workResultKey } from "./common";
import { find } from "lodash-es";

import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";
import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";

import { buildValidatorData } from "@/utils/validate";
import { bufferToBase64, base64ToBuffer, stringToBuffer } from "@/utils/base64";

import { SERVER, Method, type Response } from "@/api/service/common";
import request from "@/api/service/index";
import CryptoJS from "crypto-js";
import { ElMessage } from "element-plus";

import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { faGithub, faWeixin } from "@fortawesome/free-brands-svg-icons";

import { type LoginData } from "@/api/system";

enum loginChannels {
  PASSWORD = "PASSWORD",
  REFRESH_TOKEN = "REFRESH_TOKEN",
  SMS_CODE = "SMS_CODE",
  EMAIL_CODE = "EMAIL_CODE",
  GIT_HUB = "GIT_HUB",
  WECHAT = "WECHAT",
}

const loginChannelsOption: { label: string; value: keyof typeof loginChannels }[] = [
  { label: "密码", value: "PASSWORD" },
  { label: "Refresh Token", value: "REFRESH_TOKEN" },
  { label: "手机号", value: "SMS_CODE" },
  { label: "GitHub", value: "GIT_HUB" },
  { label: "微信", value: "WECHAT" },
];

const siteConfig = useSiteConfig();
const methods = ref<(keyof typeof loginChannels)[]>(siteConfig.loginChannels);
const active = ref<keyof typeof loginChannels>(methods.value[0]);
const loginMethods = computed(() => find(loginChannelsOption, ({ value }) => value === active.value));

const loadingCaptcha = ref(false);
const coolingCaptcha = ref(0);
watch(coolingCaptcha, (cooling) => {
  if (cooling !== 0) setTimeout(() => coolingCaptcha.value !== 0 && (coolingCaptcha.value = cooling - 1), 1000);
});

interface Props {
  loading?: boolean;
  form: {
    [key: string]: unknown;
    certificate: string;
    captcha: string;
    username: string;
    password: string;
    lengthen: boolean;
    name: string;
    nickname: string;
    account: string;
    email: string;
    birthday: string;
    rePassword: string;
    phone: string;
    code: string;
    ticket: string;
  };
}
const props = withDefaults(defineProps<Props>(), { loading: false });

interface Emits {
  ($event: "change", type: operateType): void;
  ($event: "update:loading", data: Props["loading"]): void;
  ($event: "update:form", data: Props["form"]): void;
}
const emits = defineEmits<Emits>();
const loading = useModel(props, "loading");
const $form = useModel(props, "form");

const route = useRoute();

const formRef = inject(formRefKey, ref<InstanceType<typeof import("element-plus").ElForm>>());
const workResult = inject<(result?: Promise<Response<import("@/api/system").LoginData>>) => Promise<void>>(workResultKey);
const inputCertificate = inject<(code: string, callback: (form: { certificate?: string; captcha?: string }) => Promise<{ success: boolean; message: string } & Record<string, unknown>>) => Promise<void>>(inputCertificateKey);

async function submit($active: keyof typeof loginChannels) {
  if (!formRef.value) return;
  if (!loginMethods.value) return;
  if (!workResult) return;
  if (loading.value) return;

  let result: Promise<Response<LoginData>> | undefined = undefined;
  formRef.value.clearValidate();

  const info = ((type) => {
    switch (type) {
      case `${superBaseRoute.name}Login`:
        return useSuperInfo();
      case `${adminBaseRoute.name}Login`:
        return useAdminInfo();
      case `${usersBaseRoute.name}Login`:
        return useUsersInfo();
      default:
        return useSuperInfo();
    }
  })(route.name);

  active.value = $active;
  switch ($active) {
    case loginChannels.PASSWORD: {
      if (await new Promise((resolve) => formRef.value?.validateField(["username", "password"], resolve))) {
        loading.value = true;
        try {
          const { success, message, data } = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/rsa/key/public`, method: Method.Get, responseType: "json", params: {}, data: new URLSearchParams() });
          if (!success) throw Object.assign(new Error(message), { success, data });
          const importPublicKey = await window.crypto.subtle.importKey("spki", base64ToBuffer(data), { name: "RSA-OAEP", hash: { name: "SHA-512" } }, true, ["encrypt"]);
          const _password = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer($form.value.password));

          result = request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/login/password`, method: Method.Post, responseType: "json", params: await sendCaptcha(), data: { username: $form.value.username, password: bufferToBase64(_password), lengthen: $form.value.lengthen, ptype: "RSA" } });
        } catch (error) {
          ElMessage.error("密码读取错误，请确认密码是否符合要求，联系管理员排查问题");
        }
      }
      break;
    }
    case loginChannels.REFRESH_TOKEN: {
      loading.value = true;
      result = request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/authentication/sign_in/refresh_token`, method: Method.Post, responseType: "json", params: { refreshToken: info.getToken("refresh") }, data: { lengthen: $form.value.lengthen } });
      break;
    }
    case loginChannels.SMS_CODE: {
      if (await new Promise((resolve) => formRef.value?.validateField(["phone", "code"], resolve))) {
        loading.value = true;
        result = request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/authentication/sign_in/sms`, method: Method.Post, responseType: "json", params: {}, data: { phone: $form.value.phone, code: $form.value.code, lengthen: $form.value.lengthen } });
      }
      break;
    }
    case loginChannels.GIT_HUB: {
      loading.value = true;
      const { success, message, data } = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/github/authorize_url/login`, method: Method.Get, responseType: "json", params: {}, data: {} });
      if (!success) throw Object.assign(new Error(message), { success, data });
      result = new Promise((resolve, reject) => {
        const windowProxy = window.open(data, "_blank", `location=no, menubar=no, status=no, titlebar=no, toolbar=no, top=0px, left=0px, width=${window.screen.availWidth * 0.45}px, height=${window.screen.availHeight * 0.45}px`);
        if (!windowProxy) return;
        const winLoop = setInterval(() => windowProxy.closed && (done(), reject(new Error("关闭了授权"))), 1000);
        window.addEventListener("message", binding);
        async function binding({ data }: { data: { idp: keyof typeof loginChannels; code: string } }) {
          if (data.idp === loginChannels.GIT_HUB) {
            done();
            try {
              const result = await request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/github/login/code`, method: Method.Post, responseType: "json", params: {}, data: { code: data.code, lengthen: $form.value.lengthen } });
              resolve(result);
            } catch (error) {
              reject(error);
            }
          }
        }
        function done() {
          clearInterval(winLoop);
          window.removeEventListener("message", binding);
        }
      });
      break;
    }
    case loginChannels.WECHAT: {
      loading.value = true;
      break;
    }
  }
  if (result) await workResult(result);
  loading.value = false;
}

/* -------------------------------------------------------------------------------- */

async function requireCaptcha(data: { username: string }) {
  const base = ((type) => {
    switch (type) {
      case `${superBaseRoute.name}Login`:
        return superBaseRoute;
      case `${adminBaseRoute.name}Login`:
        return adminBaseRoute;
      case `${usersBaseRoute.name}Login`:
        return usersBaseRoute;
      default:
        return { auth: "", path: "", name: "", title: "", app: "" };
    }
  })(route.name);

  try {
    const res = await request<unknown, Response<boolean>>({ url: `${SERVER.IAM}/login/password/need_captcha`, method: Method.Get, responseType: "json", headers: { "x-auth-client-token": base.auth }, params: { username: data.username, clientToken: base.auth }, data: {} });
    if (!res.success) throw Object.assign(new Error(res.message), { success: res.success, data: res.data });
    return res.data;
  } catch (error) {
    return true;
  }
}

function sendCaptcha() {
  return new Promise<{ certificate?: string; captcha?: string }>((resolve) => {
    nextTick(async () => {
      if (!inputCertificate) return resolve({});
      if (!formRef.value) return resolve({});
      if (loadingCaptcha.value) return resolve({});
      loadingCaptcha.value = true;
      formRef.value.clearValidate();
      try {
        switch (active.value) {
          case loginChannels.PASSWORD:
            if (!(await new Promise((resolve) => formRef.value?.validateField(["username"], resolve)))) return ElMessage.error(`请填写正确的账号`);
            if (!(await requireCaptcha({ username: $form.value.username }))) {
              resolve({});
            } else {
              await inputCertificate($form.value.username, async (_form) => (resolve(_form), { success: true, message: "" }));
            }
            return;
          case loginChannels.REFRESH_TOKEN:
            resolve({});
            return;
          case loginChannels.SMS_CODE:
            if (!(await new Promise((resolve) => formRef.value?.validateField(["phone"], resolve)))) return ElMessage.error(`请填写正确的手机号`);
            await inputCertificate($form.value.phone, (_form) => (resolve(_form), request<unknown, { success: boolean; message: string }>({ url: `${SERVER.IAM}/login/send_sms_login_code`, method: Method.Get, responseType: "json", params: { ..._form, phone: $form.value.phone }, data: {} })));
            ElMessage.success("验证码发送成功");
            return;
          case loginChannels.GIT_HUB:
            resolve({});
            return;
          case loginChannels.WECHAT:
            resolve({});
            return;
        }
      } catch (error) {
        resolve({});
        return;
      } finally {
        loadingCaptcha.value = false;
      }
    });
  });
}
</script>

<style scoped lang="scss"></style>
