export const workResultKey = Symbol("workResult");
export const inputCertificateKey = Symbol("inputCertificate");
export const formRefKey = Symbol("formRef");
import { loginChannels, loginChannelsOption } from "@/api/system";
export { loginChannels, loginChannelsOption };

export enum operateType {
  LOGIN = "LOGIN",
  SIGNIN = "SIGNIN",
  VERIFY = "VERIFY",
  RETRIEVE = "RETRIEVE",
  PASSWORD_EXPIRED = "PASSWORD_EXPIRED",
  SELECT_ACCOUNT = "SELECT_ACCOUNT",
}
