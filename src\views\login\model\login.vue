<template>
  <el-form-item prop="username" :rules="[{ required: true, message: $t('glob.Please input field', { field: '账号' }) }]">
    <el-input v-model="$form.username" :prefix-icon="User" type="text" clearable :placeholder="$t('adminLogin.Please enter an account')"></el-input>
  </el-form-item>

  <el-form-item prop="password" :rules="[{ required: true, message: $t('glob.Please input field', { field: '密码' }) }]">
    <el-input v-model="$form.password" :prefix-icon="Lock" type="password" :placeholder="$t('adminLogin.Please input a password')" show-password></el-input>
  </el-form-item>

  <el-form-item
    prop="captcha"
    :rules="[
      { required: true, message: $t('glob.Please input field', { field: '图形验证码' }) },
      { type: 'string', min: 4, max: 8, message: '验证码需要在4-8位字符', trigger: 'blur' },
    ]"
  >
    <el-input v-model="$form.captcha" :prefix-icon="ChatDotSquare" type="text" :placeholder="$t('adminLogin.Please enter the verification code')" clearable autocomplete="off" :style="{ verticalAlign: 'top', width: 'calc(100% - 118px)', marginRight: '12px' }"></el-input>
    <canvas ref="captchaImg" class="captcha-img tw-flex-shrink-0" height="40" width="100" title="看不清，换一张" @click="onChangeCaptcha()"></canvas>
  </el-form-item>

  <!-- <el-form-item prop="lengthen">
    <el-checkbox v-model="$form.lengthen" :label="$t('adminLogin.Hold session')"></el-checkbox>
  </el-form-item> -->

  <el-button class="tw-w-full" :loading="loading" type="primary" @click="submit()">{{ methods.label }}{{ $t("adminLogin.Sign in") }}</el-button>
</template>

<script setup lang="ts">
import { ref, shallowReadonly, useModel, inject } from "vue";
import { useRoute } from "vue-router";
import { User, Lock, ChatDotSquare } from "@element-plus/icons-vue";
import { bufferToBase64, base64ToBuffer, stringToBuffer } from "@/utils/base64";
import { loginChannels, loginChannelsOption, formRefKey, workResultKey } from "./common";
import { find } from "lodash-es";

import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";

import { SERVER, Method, type Response } from "@/api/service/common";
import request from "@/api/service/index";
import CryptoJS from "crypto-js";
import { ElMessage } from "element-plus";

const methods = shallowReadonly<(typeof loginChannelsOption)[number]>(find(loginChannelsOption, (v) => v.value === loginChannels.PASSWORD)!);

interface Props {
  loading?: boolean;
  form: {
    [key: string]: unknown;
    certificate: string;
    captcha: string;
    username: string;
    password: string;
    lengthen: boolean;
    name: string;
    nickname: string;
    account: string;
    email: string;
    birthday: string;
    rePassword: string;
    phone: string;
    code: string;
    ticket: string;
  };
}
const props = withDefaults(defineProps<Props>(), { loading: false });

const route = useRoute();

const loading = useModel(props, "loading");
const $form = useModel(props, "form");

const formRef = inject(formRefKey, ref<InstanceType<typeof import("element-plus").ElForm>>());
const workResult = inject<(result?: Promise<Response<import("@/api/system").LoginData>>) => Promise<void>>(workResultKey);

async function submit() {
  if (!formRef.value) return;
  const _formRef = formRef.value;
  if (!workResult) return;

  let result: Promise<Response<import("@/api/system").LoginData>> | undefined = undefined;
  _formRef.clearValidate();

  const info = ((type) => {
    switch (type) {
      case `${superBaseRoute.name}Login`:
        return useSuperInfo();
      case `${adminBaseRoute.name}Login`:
        return useAdminInfo();
      case `${usersBaseRoute.name}Login`:
        return useUsersInfo();
      default:
        return useSuperInfo();
    }
  })(route.name);

  if (await new Promise((resolve) => _formRef.validateField(["username", "password", ...($form.value.certificate ? ["captcha"] : [])], resolve))) {
    loading.value = true;

    const params = new URLSearchParams();

    if (await requireCaptcha({ username: <string>$form.value.username })) {
      if (!$form.value.certificate) {
        $form.value.certificate = String(`xxxxxxxx-xxxx-4xxx-yxxx-${Date.now().toString(16).padStart(12, "x")}`).replace(/[xy]/g, (c) => Number(c === "x" ? (Math.random() * 16) | 0 : (((Math.random() * 16) | 0) & 0x3) | 0x8).toString(16));
        loading.value = false;
        return;
      } else {
        params.set("certificate", $form.value.certificate);
        params.set("captcha", $form.value.captcha);
      }
    }
    try {
      const { success, message, data } = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/rsa/key/public`, method: Method.Get, responseType: "json", params: {}, data: new URLSearchParams() });
      if (!success) throw Object.assign(new Error(message), { success, data });
      const importPublicKey = await window.crypto.subtle.importKey("spki", base64ToBuffer(data), { name: "RSA-OAEP", hash: { name: "SHA-512" } }, true, ["encrypt"]);
      const _password = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(<string>$form.value.password));

      result = request<unknown, Response<import("@/api/system").LoginData>>({ url: `${SERVER.IAM}/login/password`, method: Method.Post, responseType: "json", params, data: { username: <string>$form.value.username, password: bufferToBase64(_password), lengthen: <boolean>$form.value.lengthen, ptype: "RSA" } });
    } catch (error) {
      ElMessage.error("密码读取错误，请确认密码是否符合要求，联系管理员排查问题");
    }
  }

  if (result) await workResult(result);
  loading.value = false;
}

/* -------------------------------------------------------------------------------- */

async function requireCaptcha(data: { username: string }) {
  const base = ((type) => {
    switch (type) {
      case `${superBaseRoute.name}Login`:
        return superBaseRoute;
      case `${adminBaseRoute.name}Login`:
        return adminBaseRoute;
      case `${usersBaseRoute.name}Login`:
        return usersBaseRoute;
      default:
        return { auth: "", path: "", name: "", title: "", app: "" };
    }
  })(route.name);

  try {
    const res = await request<unknown, Response<boolean>>({ url: `${SERVER.IAM}/login/password/need_captcha`, method: Method.Get, responseType: "json", headers: { "x-auth-client-token": base.auth }, params: { username: data.username, clientToken: base.auth }, data: {} });
    if (!res.success) throw Object.assign(new Error(res.message), { success: res.success, data: res.data });
    return res.data;
  } catch (error) {
    return true;
  }
}
</script>

<style scoped lang="scss"></style>
