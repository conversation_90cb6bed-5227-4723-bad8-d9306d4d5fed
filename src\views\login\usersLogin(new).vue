<template>
  <div class="login-container">
    <!-- Header -->
    <div class="login-header-bar">
      <div class="logo-group" @click="handleLogoClick">
        <img :src="loginLeft" alt="logo" class="header-logo" />
      </div>
      <div class="header-right">
        <span class="support-phone">
          <img :src="loginDh" alt="phone icon" class="header-icon" />
          400 8888 112
        </span>
        <el-dropdown size="large" :hide-timeout="50" placement="bottom-end" :hide-on-click="true">
          <span class="language-switcher">
            <img :src="loginYuy" alt="language icon" class="header-icon" />
            {{ currentLanguage }}
            <el-icon class="el-icon--right">
              <ArrowDown />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu class="chang-lang">
              <el-dropdown-item v-for="item in config.lang.langArray" :key="item.name" @click="editDefaultLang(item.name)">
                {{ item.value }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <!-- main -->
    <div class="login-main">
      <div class="login-main-content">
        <!-- Left Panel -->
        <div class="left-panel">
          <div class="left-content">
            <h1 class="main-title">{{ swiper.list[activeSlideIndex].title }}</h1>
            <p class="sub-title" v-html="swiper.list[activeSlideIndex].subtitle"></p>
          </div>
          <el-carousel height="554px" motion-blur :interval="4000" arrow="never" @change="(index) => (activeSlideIndex = index)">
            <el-carousel-item v-for="item in swiper.list" :key="item.id">
              <img :src="item.url" alt="carousel image" class="carousel-image" />
            </el-carousel-item>
          </el-carousel>
        </div>

        <!-- Right Panel -->
        <div class="right-panel">
          <div class="form-container">
            <el-form v-focus :model="form" ref="formRef" size="large" label-position="top" @submit.prevent>
              <div v-if="loading">
                <el-empty description="Loading...">
                  <template #image>
                    <div class="tw-p-[24px]">
                      <div class="loading-chase">
                        <div v-for="i in 6" :key="`loading-chase-dot_${i}`" class="loading-chase-dot"></div>
                      </div>
                    </div>
                  </template>
                </el-empty>
              </div>
              <div v-else-if="type === operateType.LOGIN" class="tw-mb-11 tw-text-center" @keyup.enter.stop="loginSubmit(active)">
                <div class="tw-mt-4">
                  <el-tabs v-model="active" class="login-tabs">
                    <el-tab-pane :label="$t('login.Log In password')" :name="loginChannels.PASSWORD" v-if="methods.includes(loginChannels.PASSWORD)"></el-tab-pane>
                    <el-tab-pane :label="$t('login.Log In Email')" :name="loginChannels.EMAIL" v-if="methods.includes(loginChannels.PASSWORD)"></el-tab-pane>
                    <el-tab-pane :label="$t('login.Login In phone number')" :name="loginChannels.SMS_CODE" v-if="methods.includes(loginChannels.SMS_CODE)"></el-tab-pane>
                  </el-tabs>
                </div>
                <template v-if="!active"></template>
                <template v-else-if="active === loginChannels.PASSWORD">
                  <el-form-item prop="username" :rules="[{ required: true, message: $t('login.Please enter your', { value: $t('login.email or username') }) }]">
                    <el-input v-model="form.username" :prefix-icon="User" type="text" clearable :placeholder="$t('login.Please enter your', { value: $t('login.email or username') })"></el-input>
                  </el-form-item>

                  <el-form-item prop="password" :rules="[{ required: true, message: $t('login.Please enter your', { value: $t('login.password') }) }]">
                    <el-input v-model="form.password" :prefix-icon="Lock" type="password" :placeholder="$t('login.Please enter your', { value: $t('login.password') })" show-password></el-input>
                    <div class="forgot-password">
                      <el-link :disabled="loading" @click="() => changeType(operateType.RETRIEVE)">{{ $t("login.Forgot password") }}</el-link>
                    </div>
                  </el-form-item>

                  <!-- <el-form-item prop="captcha" :rules="[buildValidatorData({ name: 'required', title: '图形验证码' }), { type: 'string', min: 4, max: 8, message: '验证码需要在4-8位字符', trigger: 'blur' }]">
                    <el-input v-model="form.captcha" :prefix-icon="ChatDotSquare" type="text" :placeholder="$t('adminLogin.Please enter the verification code')" clearable autocomplete="off" :style="{ verticalAlign: 'top', width: 'calc(100% - 118px)', marginRight: '12px' }"></el-input>
                    <canvas ref="captchaImg" class="captcha-img tw-flex-shrink-0" height="40" width="100" title="看不清，换一张" @click="onChangeCaptcha()"></canvas>
                  </el-form-item> -->

                  <!-- <el-form-item prop="lengthen">
                    <el-checkbox v-model="form.lengthen" :label="$t('adminLogin.Hold session')"></el-checkbox>
                  </el-form-item> -->

                  <!-- <el-form-item prop="captcha" :rules="[{ required: true, message: $t('glob.Please input field', { field: '验证码' }) }]">
                    <el-input v-model="form.captcha" :prefix-icon="ChatDotSquare" type="text" :placeholder="$t('adminLogin.Please enter the verification code')" clearable autocomplete="off" :style="{ verticalAlign: 'top', width: 'calc(100% - 118px)', marginRight: '12px' }"></el-input>
                    <canvas ref="captchaImg" class="captcha-img tw-flex-shrink-0" height="40" width="100" title="看不清，换一张" @click="onChangeCaptcha()"></canvas>
                  </el-form-item> -->
                </template>

                <template v-else-if="active === loginChannels.EMAIL">
                  <el-form-item prop="username" :rules="[{ required: true, message: $t('glob.Please input field', { field: $t('adminLogin.email') }) }]">
                    <el-input v-model="form.username" :prefix-icon="Message" type="text" clearable :placeholder="$t('adminLogin.Please enter an Email')"></el-input>
                  </el-form-item>

                  <el-form-item prop="password" :rules="[{ required: true, message: $t('glob.Please input field', { field: $t('adminLogin.password') }) }]">
                    <el-input v-model="form.password" :prefix-icon="Lock" type="password" :placeholder="$t('adminLogin.Please input a password')" show-password></el-input>
                    <div class="forgot-password">
                      <el-link :disabled="loading" @click="() => changeType(operateType.RETRIEVE)">{{ $t("login.Forgot password") }}</el-link>
                    </div>
                  </el-form-item>

                  <!-- <el-form-item prop="captcha" :rules="[buildValidatorData({ name: 'required', title: '图形验证码' }), { type: 'string', min: 4, max: 8, message: '验证码需要在4-8位字符', trigger: 'blur' }]">
                    <el-input v-model="form.captcha" :prefix-icon="ChatDotSquare" type="text" :placeholder="$t('adminLogin.Please enter the verification code')" clearable autocomplete="off" :style="{ verticalAlign: 'top', width: 'calc(100% - 118px)', marginRight: '12px' }"></el-input>
                    <canvas ref="captchaImg" class="captcha-img tw-flex-shrink-0" height="40" width="100" title="看不清，换一张" @click="onChangeCaptcha()"></canvas>
                  </el-form-item> -->

                  <!-- <el-form-item prop="lengthen">
                    <el-checkbox v-model="form.lengthen" :label="$t('adminLogin.Hold session')"></el-checkbox>
                  </el-form-item> -->

                  <!-- <el-form-item prop="captcha" :rules="[{ required: true, message: $t('glob.Please input field', { field: '验证码' }) }]">
                    <el-input v-model="form.captcha" :prefix-icon="ChatDotSquare" type="text" :placeholder="$t('adminLogin.Please enter the verification code')" clearable autocomplete="off" :style="{ verticalAlign: 'top', width: 'calc(100% - 118px)', marginRight: '12px' }"></el-input>
                    <canvas ref="captchaImg" class="captcha-img tw-flex-shrink-0" height="40" width="100" title="看不清，换一张" @click="onChangeCaptcha()"></canvas>
                  </el-form-item> -->
                </template>

                <template v-else-if="active === loginChannels.REFRESH_TOKEN"></template>
                <template v-else-if="active === loginChannels.SMS_CODE">
                  <el-form-item prop="phone" :rules="[buildValidatorData({ name: 'required', title: $t('login.The phone number') }), buildValidatorData({ name: 'mobile', title: $t('login.The phone number') })]">
                    <el-input v-model="form.phone" :prefix-icon="Phone" type="text" clearable :placeholder="$t('adminLogin.Please enter an Phone')">
                      <!--  -->
                    </el-input>
                  </el-form-item>
                  <el-form-item prop="code" :rules="[buildValidatorData({ name: 'required', title: $t('login.SMS verification code') }), { type: 'string', min: 4, max: 8, message: $t('login.verification3'), trigger: 'blur' }]">
                    <el-input v-model="form.code" :prefix-icon="ChatDotSquare" type="text" :placeholder="$t('login.SMS verification code')">
                      <template #suffix>
                        <el-button link :loading="loadingCaptcha" :disabled="coolingCaptcha !== 0 || !form.phone" class="submit-button" type="primary" size="small" @click="sendCaptcha()">{{ $t("adminLogin.Send verification code") }}{{ coolingCaptcha ? `(${coolingCaptcha}${$t("login.s")})` : "" }}</el-button>
                      </template>
                    </el-input>
                    <div class="forgot-password">
                      <el-link :disabled="loading" @click="() => changeType(operateType.RETRIEVE)">{{ $t("login.Forgot password") }}</el-link>
                    </div>
                  </el-form-item>
                  <!-- <el-form-item prop="lengthen">
                    <el-checkbox v-model="form.lengthen" :label="$t('adminLogin.Hold session')"></el-checkbox>
                  </el-form-item> -->
                </template>
                <!-- <template v-else-if="active === loginChannels.GIT_HUB">
                  <div class="tw-p-[24px]">
                    <div class="loading-chase">
                      <div v-for="i in 6" :key="`loading-chase-dot_${i}`" class="loading-chase-dot"></div>
            </div>
                  </div>
                </template>
                <template v-else-if="active === loginChannels.WECHAT">
                  <div class="tw-p-[24px]">
                    <div class="loading-chase">
                      <div v-for="i in 6" :key="`loading-chase-dot_${i}`" class="loading-chase-dot"></div>
                    </div>
                  </div>
                </template> -->
                <!-- <el-button class="tw-w-full" :loading="loading" type="primary" @click="loginSubmit(active)">{{ loginMethods?.label }}{{ $t("adminLogin.Sign in") }}</el-button> -->
                <el-button class="tw-w-full" :loading="loading" type="primary" @click="loginSubmit(active)">{{ $t("login.Log In") }}</el-button>
                <div class="tw-mt-4 tw-flex tw-items-center tw-justify-center">
                  <p class="term">
                    <el-checkbox v-model="serviceTerm" style="margin-right: 3px"></el-checkbox>
                    {{ $t("login.Accept the above") }}
                    <b style="cursor: pointer; margin-left: 3px" @click="dialogVisible = true">{{ $t("login.terms") }}</b>
                  </p>
                </div>
              </div>

              <div v-else-if="type === operateType.SIGNIN" class="tw-mb-11 tw-text-center" @keyup.enter.stop="signinSubmit()">
                <div class="tw-text-left">
                  <el-link type="primary" :icon="Back" :underline="false" :disabled="loading" @click="() => changeType(operateType.LOGIN)"><span class="tw-ml-2">返回登录</span></el-link>
                </div>
                <h1 class="tw-mb-3 tw-text-2xl tw-font-black">{{ $t("user.user.register") }}</h1>
                <p class="tw-text-[var(--el-text-color-placeholder)]">填写注册信息</p>
                <div class="tw-mt-4"></div>
                <div style="padding: 10px 0; box-sizing: border-box">注册信息</div>
                <el-row :gutter="16">
                  <!-- <el-col :span="24">
                    <el-form-item prop="nickname" :rules="[]">
                      <el-input v-model="form.nickname" :prefix-icon="Avatar" autocomplete="nickname" placeholder="请输入昵称" clearable></el-input>
              </el-form-item>
                  </el-col> -->
                  <el-col :span="12">
                    <el-form-item prop="name" :rules="[buildValidatorData({ name: 'required', title: '姓名' })]">
                      <el-input v-model="form.name" :prefix-icon="User" autocomplete="given-name" placeholder="请输入姓名" clearable></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="account" :rules="[buildValidatorData({ name: 'required', title: '账号' })]">
                      <el-input v-model="form.account" :prefix-icon="Postcard" autocomplete="username" :placeholder="$t('adminLogin.Please enter an account')" clearable></el-input>
                    </el-form-item>
                  </el-col>
                  <template v-if="siteConfig.loginChannels.includes(loginChannels.SMS_CODE)">
                    <el-col :span="16">
                      <el-form-item prop="phone" :rules="[buildValidatorData({ name: 'required', title: '手机号' }), buildValidatorData({ name: 'mobile', title: '手机号' })]">
                        <el-input v-model="form.phone" :prefix-icon="Phone" type="text" autocomplete="tel" :placeholder="$t('adminLogin.Please enter an Phone')">
                          <template #suffix>
                            <el-button link :loading="loadingCaptcha" :disabled="coolingCaptcha !== 0 || !form.phone" class="submit-button" type="primary" size="small" @click="sendCaptcha()">{{ $t("adminLogin.Send verification code") }}{{ coolingCaptcha ? `(${coolingCaptcha}${$t("login.s")})` : "" }}</el-button>
                          </template>
                        </el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item prop="code" :rules="[buildValidatorData({ name: 'required', title: '短信验证码' }), { type: 'string', min: 4, max: 8, message: $t('login.verification3'), trigger: 'blur' }]">
                        <el-input v-model="form.code" :prefix-icon="ChatDotSquare" type="text" autocomplete="one-time-code" placeholder="短信验证码"></el-input>
                      </el-form-item>
                    </el-col>
                  </template>
                  <el-col :span="24">
                    <el-form-item prop="email" :rules="[buildValidatorData({ name: 'required', title: $t('adminLogin.email') }), buildValidatorData({ name: 'email', title: $t('adminLogin.email') })]">
                      <el-input v-model="form.email" :prefix-icon="Message" autocomplete="email" :placeholder="$t('adminLogin.Please enter an Email')" clearable></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="13">
                    <el-form-item prop="gender" :rules="[]">
                      <el-radio-group v-model="form.gender" autocomplete="sex">
                        <el-radio v-for="item in genderOption" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="11">
                    <el-form-item prop="birthday" :rules="[]">
                      <el-date-picker v-model="form.birthday" type="date" autocomplete="bday" placeholder="选择日期" value-format="YYYY-MM-DD"></el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item prop="password" :rules="[buildValidatorData({ name: 'required', title: '密码' }), buildValidatorData({ name: 'password', title: '密码' })]">
                      <el-input v-model="form.password" :prefix-icon="Lock" type="password" show-password autocomplete="new-password" placeholder="密码" clearable></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item prop="rePassword" :rules="[buildValidatorData({ name: 'required', title: '确认密码' }), { validator: (_rule, value, callback) => callback(value === form.password ? undefined : '两次密码需要一致'), trigger: 'blur' }]">
                      <el-input v-model="form.rePassword" :prefix-icon="Lock" type="password" show-password autocomplete="new-password" placeholder="确认密码" clearable></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-button class="tw-mt-4 tw-w-full" :loading="loading" type="primary" @click="signinSubmit()">{{ $t("user.user.register") }}</el-button>
              </div>

              <div v-else-if="type === operateType.VERIFY" class="tw-mb-11 tw-text-center" @keyup.enter.stop="verifySubmit()">
                <div class="tw-text-left">
                  <el-link type="primary" :icon="Back" :underline="false" :disabled="loading" @click="() => changeType(operateType.LOGIN)"
                    ><span class="tw-ml-2">{{ $t("glob.back") }}</span></el-link
                  >
                </div>
                <h1 class="tw-mb-3 tw-text-2xl tw-font-black">{{ $t("pagesTitle.notVerify") }}</h1>
                <p class="tw-text-[var(--el-text-color-placeholder)]">为了确保账户是您本人操作，请任意选择一种安全验证方式验证身份</p>
                <div class="tw-mt-4">
                  <el-button v-if="mfaMethods.includes(MFAMethod.PASSWORD)" :disabled="loading" :type="mfaActive === MFAMethod.PASSWORD ? 'primary' : 'default'" circle plain :icon="Lock" @click="() => ((mfaActive = MFAMethod.PASSWORD), (form.code = ''))"></el-button>
                  <el-button v-if="mfaMethods.includes(MFAMethod.SMS)" :disabled="loading" :type="mfaActive === MFAMethod.SMS ? 'primary' : 'default'" circle plain :icon="Iphone" @click="() => ((mfaActive = MFAMethod.SMS), (form.code = ''))"></el-button>
                  <el-button v-if="mfaMethods.includes(MFAMethod.EMAIL)" :disabled="loading" :type="mfaActive === MFAMethod.EMAIL ? 'primary' : 'default'" circle plain :icon="Message" @click="() => ((mfaActive = MFAMethod.EMAIL), (form.code = ''))"></el-button>
                  <el-button v-if="mfaMethods.includes(MFAMethod.TOTP)" :disabled="loading" :type="mfaActive === MFAMethod.TOTP ? 'primary' : 'default'" circle plain :icon="Timer" @click="() => ((mfaActive = MFAMethod.TOTP), (form.code = ''))"></el-button>
                </div>
                <div style="padding: 10px 0; box-sizing: border-box">通过{{ mfaLoginMethods?.label }}验证</div>
                <template v-if="!mfaActive"></template>
                <template v-else-if="mfaActive === MFAMethod.PASSWORD">
                  <el-form-item prop="code" :rules="[{ required: true, message: $t('glob.Please input field', { field: $t('login.password') }) }]">
                    <el-input v-model="form.code" :prefix-icon="Lock" type="password" :placeholder="$t('adminLogin.Please input a password')" show-password></el-input>
                  </el-form-item>
                </template>
                <template v-else-if="mfaActive === MFAMethod.SMS">
                  <el-form-item prop="code" :rules="[{ required: true, message: $t('glob.Please input field', { field: $t('login.SMS verification code') }) }]">
                    <el-input v-model="form.code" :prefix-icon="Lock" type="text" :placeholder="$t('adminLogin.Please enter the verification code')">
                      <template #suffix>
                        <el-button link :loading="loadingCaptcha" :disabled="coolingCaptcha !== 0" class="submit-button" type="primary" size="small" @click="sendMFACaptcha()">{{ $t("adminLogin.Send verification code") }}{{ coolingCaptcha ? `(${coolingCaptcha}${$t("login.s")})` : "" }}</el-button>
                      </template>
                    </el-input>
                  </el-form-item>
                </template>
                <template v-else-if="mfaActive === MFAMethod.EMAIL">
                  <el-form-item prop="code" :rules="[{ required: true, message: $t('glob.Please input field', { field: $t('login.Email verification code') }) }]">
                    <el-input v-model="form.code" :prefix-icon="Lock" type="text" :placeholder="$t('adminLogin.Please enter the verification code')">
                      <template #suffix>
                        <el-button link :loading="loadingCaptcha" :disabled="coolingCaptcha !== 0" class="submit-button" type="primary" size="small" @click="sendMFACaptcha()">{{ $t("adminLogin.Send verification code") }}{{ coolingCaptcha ? `(${coolingCaptcha}${$t("login.s")})` : "" }}</el-button>
                      </template>
                    </el-input>
                  </el-form-item>
                </template>
                <template v-else-if="mfaActive === MFAMethod.TOTP">
                  <el-form-item prop="code" :rules="[{ required: true, message: $t('glob.Please input field', { field: 'MFA动态码' }) }]">
                    <el-input v-model="form.code" :prefix-icon="Lock" type="text" placeholder="请输入MFA动态码"></el-input>
                  </el-form-item>
                </template>
                <el-button class="tw-mt-4 tw-w-full" :loading="loading" type="primary" @click="verifySubmit()">{{ mfaLoginMethods?.label }}{{ $t("adminLogin.Verify") }}</el-button>
              </div>

              <div v-else-if="type === operateType.RETRIEVE" class="tw-mb-11 tw-text-center" @keyup.enter.stop="resetSubmit()">
                <div class="tw-text-left">
                  <el-link type="primary" :icon="Back" :underline="false" :disabled="loading" @click="() => changeType(operateType.LOGIN)"
                    ><span class="tw-ml-2">{{ $t("glob.back") }}</span></el-link
                  >
                </div>
                <h1 class="tw-mb-3 tw-text-2xl tw-font-black">{{ $t("glob.Reset") }}</h1>
                <template v-if="!accountList.length">
                  <p class="tw-text-[var(--el-text-color-placeholder)]">{{ $t("login.Retrieve password verification method") }}</p>
                  <div class="tw-mt-4">
                    <el-button v-if="mfaMethods.includes(MFAMethod.SMS)" :disabled="loading" :type="mfaActive === MFAMethod.SMS ? 'primary' : 'default'" circle plain :icon="Iphone" @click="(mfaActive = MFAMethod.SMS), (form.code = '')"></el-button>
                    <el-button v-if="mfaMethods.includes(MFAMethod.EMAIL)" :disabled="loading" :type="mfaActive === MFAMethod.EMAIL ? 'primary' : 'default'" circle plain :icon="Message" @click="(mfaActive = MFAMethod.EMAIL), (form.code = '')"></el-button>
                  </div>
                </template>
                <div style="padding: 10px 0; box-sizing: border-box" v-if="accountList.length">此身份验证中包含多个用户，请选择其中一个用户进行找回</div>
                <div style="padding: 10px 0; box-sizing: border-box" v-else>
                  <!-- {{ mfaLoginMethods?.value }}通过{{ mfaLoginMethods?.label }}找回密码 -->
                  {{
                    {
                      SMS: $t("login.Retrieve password through", { value: $t("login.SMS") }),
                      EMAIL: $t("login.Retrieve password through", { value: $t("login.email") }),
                    }[mfaLoginMethods?.value] || undefined
                  }}
                </div>
                <template v-if="accountList.length">
                  <el-form-item prop="uid" :rules="[buildValidatorData({ name: 'required', title: '用户' })]">
                    <div class="tw-w-full">
                      <div class="tw-w-[calc(100%_-_14px)] tw-pl-[24px]" :style="{ borderBottom: 'var(--el-border)' }">
                        <el-row :gutter="16">
                          <el-col :span="12" class="tw-text-left tw-text-[var(--el-text-color-secondary)]">用户</el-col>
                          <el-col :span="6" class="tw-text-center tw-text-[var(--el-text-color-secondary)]">注册时间</el-col>
                          <el-col :span="6" class="tw-text-center tw-text-[var(--el-text-color-secondary)]">上次登录时间</el-col>
                        </el-row>
                      </div>
                      <el-radio-group v-model="form.uid" class="tw-w-full">
                        <el-row :gutter="16">
                          <el-col :span="24" v-for="item in accountList" :key="item.uid">
                            <el-radio :label="item.uid" class="line-radio tw-h-fit tw-w-full">
                              <el-row :gutter="12" class="tw-w-full">
                                <el-col :span="12">
                                  <div class="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-py-[6px] tw-text-left tw-text-[14px]">
                                    <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.account">
                                      <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.account || "--" }}</span>
                                    </div>
                                    <!-- <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.phone">
                                    <span class="tw-inline-block tw-w-[7em] tw-align-middle">手机号:</span>
                                    <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.phone || "--" }}</span>
                                  </div> -->
                                    <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.email">
                                      <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.email || "--" }}</span>
                                    </div>
                                  </div>
                                </el-col>
                                <el-col :span="6">
                                  <div class="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-py-[6px] tw-text-center tw-text-[14px]">
                                    <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.registrationTime">
                                      <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.registrationTime || "--" }}</span>
                                    </div>
                                  </div>
                                </el-col>
                                <el-col :span="6">
                                  <div class="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-py-[6px] tw-text-center tw-text-[14px]">
                                    <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.lastLoginTime">
                                      <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.lastLoginTime || "--" }}</span>
                                    </div>
                                  </div>
                                </el-col>
                              </el-row>
                            </el-radio>
                          </el-col>
                        </el-row>
                      </el-radio-group>
                    </div>
                  </el-form-item>
                  <el-button class="tw-mt-4 tw-w-full" :loading="loading" type="primary" @click="resetSubmit()">{{ $t("glob.Retrieve") }}密码</el-button>
                </template>
                <template v-else>
                  <el-row :gutter="16">
                    <template v-if="!mfaActive"></template>
                    <template v-else-if="mfaActive === MFAMethod.SMS">
                      <el-col :span="16">
                        <el-form-item prop="phone" :rules="[buildValidatorData({ name: 'required', title: $t('login.The phone number') }), buildValidatorData({ name: 'mobile', title: $t('login.The phone number') })]">
                          <el-input v-model="form.phone" :prefix-icon="Phone" type="text" autocomplete="tel" clearable :placeholder="$t('adminLogin.Please enter an Phone')">
                            <template #suffix>
                              <el-button link :loading="loadingCaptcha" :disabled="coolingCaptcha !== 0 || !form.phone" class="submit-button" type="primary" size="small" @click="sendCaptcha()">{{ $t("adminLogin.Send verification code") }}{{ coolingCaptcha ? `(${coolingCaptcha}${$t("login.s")})` : "" }}</el-button>
                            </template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item prop="code" :rules="[buildValidatorData({ name: 'required', title: $t('login.SMS verification code') }), { type: 'string', min: 4, max: 8, message: $t('login.verification3'), trigger: 'blur' }]">
                          <el-input v-model="form.code" :prefix-icon="ChatDotSquare" type="text" autocomplete="one-time-code" :placeholder="$t('login.SMS verification code')"></el-input>
                        </el-form-item>
                      </el-col>
                    </template>
                    <template v-else-if="mfaActive === MFAMethod.EMAIL">
                      <el-col :span="16">
                        <el-form-item prop="email" :rules="[buildValidatorData({ name: 'required', title: $t('login.Email') }), buildValidatorData({ name: 'email', title: $t('login.Email') })]">
                          <el-input v-model="form.email" :prefix-icon="Message" autocomplete="email" :placeholder="$t('adminLogin.Please enter an Email')" clearable>
                            <template #suffix>
                              <el-button link :loading="loadingCaptcha" :disabled="coolingCaptcha !== 0 || !form.email" class="submit-button" type="primary" size="small" @click="sendCaptcha()">{{ $t("adminLogin.Send verification code") }}{{ coolingCaptcha ? `(${coolingCaptcha}${$t("login.s")})` : "" }}</el-button>
                            </template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item prop="code" :rules="[buildValidatorData({ name: 'required', title: $t('login.Email verification code') }), { type: 'string', min: 4, max: 8, message: $t('login.verification3'), trigger: 'blur' }]">
                          <el-input v-model="form.code" :prefix-icon="ChatDotSquare" type="text" autocomplete="one-time-code" :placeholder="$t('login.Email verification code')"></el-input>
                        </el-form-item>
                      </el-col>
                    </template>
                  </el-row>
                  <el-form-item prop="password" :rules="[buildValidatorData({ name: 'required', title: $t('login.password') }), buildValidatorData({ name: 'password', title: $t('login.password') })]">
                    <el-input v-model="form.password" :prefix-icon="Lock" type="password" show-password autocomplete="new-password" :placeholder="$t('login.password')" clearable></el-input>
                  </el-form-item>
                  <el-form-item prop="rePassword" style="margin-top: 30px" :rules="[buildValidatorData({ name: 'required', title: $t('login.Confirm password') }), { validator: (_rule, value, callback) => callback(value === form.password ? undefined : $t('login.Two passwords need to be consistent')), trigger: 'blur' }]">
                    <el-input v-model="form.rePassword" :prefix-icon="Lock" type="password" show-password autocomplete="new-password" :placeholder="$t('login.Confirm password')" clearable></el-input>
                  </el-form-item>
                  <el-button class="tw-mt-4 tw-w-full" :loading="loading" type="primary" @click="resetSubmit()">
                    <!-- 使用{{ mfaLoginMethods?.label }}{{ $t("glob.Retrieve") }}密码 -->
                    {{ $t("login.use model Retrieve password", { model: { SMS: $t("login.SMS"), EMAIL: $t("login.email") }[mfaLoginMethods?.value] }) }}
                  </el-button>
                </template>
              </div>

              <div v-else-if="type === operateType.PASSWORD_EXPIRED" class="tw-mb-11 tw-text-center" @keyup.enter.stop="expiredSubmit()">
                <div class="tw-text-left">
                  <el-link type="primary" :icon="Back" :underline="false" :disabled="loading" @click="() => changeType(operateType.LOGIN)"
                    ><span class="tw-ml-2">{{ $t("glob.back") }}</span></el-link
                  >
                </div>
                <h1 class="tw-mb-3 tw-text-2xl tw-font-black">{{ $t("glob.Reset") }}</h1>
                <p class="tw-text-[var(--el-text-color-placeholder)]">{{ $t("login.reset your password") }}</p>
                <div class="tw-mt-4">
                  <el-button :disabled="loading" type="primary" circle plain :icon="Lock" @click="form.code = ''"></el-button>
                </div>
                <el-divider>{{ $t("login.Reset/Initial Password") }}</el-divider>
                <el-form-item prop="password" :rules="[buildValidatorData({ name: 'required', title: $t('login.Confirm password') }), buildValidatorData({ name: 'password', title: $t('login.password'), min: userInfo.passwordLength })]">
                  <el-input v-model="form.password" :prefix-icon="Lock" type="password" show-password autocomplete="new-password" :placeholder="$t('login.password')" clearable></el-input>
                </el-form-item>
                <el-form-item style="margin-top: 30px" prop="rePassword" :rules="[buildValidatorData({ name: 'required', title: $t('login.Confirm password') }), { validator: (_rule, value, callback) => callback(value === form.password ? undefined : '两次密码需要一致'), trigger: 'blur' }]">
                  <el-input v-model="form.rePassword" :prefix-icon="Lock" type="password" show-password autocomplete="new-password" :placeholder="$t('login.Confirm password')" clearable></el-input>
                </el-form-item>
                <el-button class="tw-mt-4 tw-w-full" :loading="loading" type="primary" @click="expiredSubmit()">{{ $t("login.Reset/Initial Password") }}</el-button>
              </div>

              <div v-else-if="type === operateType.SELECT_ACCOUNT" class="tw-mb-11 tw-text-center" @keyup.enter.stop="accountSubmit()">
                <div class="tw-text-left">
                  <el-link type="primary" :icon="Back" :underline="false" :disabled="loading" @click="() => changeType(operateType.LOGIN)"><span class="tw-ml-2">返回登录</span></el-link>
                </div>
                <h1 class="tw-mb-3 tw-text-2xl tw-font-black">{{ $t("glob.Please select field", { field: "用户" }) }}</h1>
                <p class="tw-text-[var(--el-text-color-placeholder)]">此身份验证中包含多个用户，请选择其中一个用户进行登录</p>
                <div class="tw-mt-4">
                  <el-button :disabled="loading" type="primary" circle plain :icon="Lock" @click="form.code = ''"></el-button>
                </div>
                <el-divider>选择用户</el-divider>
                <el-form-item prop="uid" :rules="[buildValidatorData({ name: 'required', title: '用户' })]">
                  <div class="tw-w-full">
                    <div class="tw-w-[calc(100%_-_14px)] tw-pl-[24px]" :style="{ borderBottom: 'var(--el-border)' }">
                      <el-row :gutter="16">
                        <el-col :span="12" class="tw-text-left tw-text-[var(--el-text-color-secondary)]">用户</el-col>
                        <el-col :span="6" class="tw-text-center tw-text-[var(--el-text-color-secondary)]">注册时间</el-col>
                        <el-col :span="6" class="tw-text-center tw-text-[var(--el-text-color-secondary)]">上次登录时间</el-col>
                      </el-row>
                    </div>
                    <el-radio-group v-model="form.uid" class="tw-w-full">
                      <el-row :gutter="16">
                        <el-col :span="24" v-for="item in accountList" :key="item.uid">
                          <el-radio :label="item.uid" class="line-radio tw-h-fit tw-w-full">
                            <el-row :gutter="12" class="tw-w-full">
                              <el-col :span="12">
                                <div class="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-py-[6px] tw-text-left tw-text-[14px]">
                                  <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.account">
                                    <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.account || "--" }}</span>
                                  </div>
                                  <!-- <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.phone">
                                    <span class="tw-inline-block tw-w-[7em] tw-align-middle">手机号:</span>
                                    <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.phone || "--" }}</span>
                                  </div> -->
                                  <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.email">
                                    <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.email || "--" }}</span>
                                  </div>
                                </div>
                              </el-col>
                              <el-col :span="6">
                                <div class="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-py-[6px] tw-text-center tw-text-[14px]">
                                  <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.registrationTime">
                                    <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.registrationTime || "--" }}</span>
                                  </div>
                                </div>
                              </el-col>
                              <el-col :span="6">
                                <div class="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-py-[6px] tw-text-center tw-text-[14px]">
                                  <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.lastLoginTime">
                                    <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.lastLoginTime || "--" }}</span>
                                  </div>
                                </div>
                              </el-col>
                            </el-row>
                          </el-radio>
                        </el-col>
                      </el-row>
                    </el-radio-group>
                  </div>
                </el-form-item>
                <el-button class="tw-mt-4 tw-w-full" :loading="loading" type="primary" @click="accountSubmit()">{{ $t("adminLogin.Sign in") }}</el-button>
              </div>
            </el-form>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="login-footer-bar">© 上海理想信息产业(集团)有限公司 沪ICP备********号-46</div>
    <el-dialog v-model="dialogVisible" :title="$t('login.Terms & Conditions')" width="35%" :before-close="handleClose">
      <div>
        <p>{{ $t("login.constraint1") }}:</p>

        <p class="term-text">{{ $t("login.constraint2") }}</p>

        <p class="term-text">{{ $t("login.constraint3") }}</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="dialogVisible = false">{{ $t("glob.Confirm") }} </el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisibleManage" :title="$t('login.Contact the Administrator')" width="30%" :before-close="handleCloseManage">
      <div>
        <p>{{ $t("login.Administrator Hotline") }}: 400-8888-112</p>
        <p>{{ $t("login.Administrator Email") }}: <EMAIL></p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="dialogVisibleManage = false"> {{ $t("glob.Cancel") }} </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, onMounted, onUnmounted, computed, watch, provide, h } from "vue";
import { formRefKey, workResultKey } from "./model/common";
import { useRouter } from "vue-router";
import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";
import DarkSwitch from "@/layouts/common/components/darkSwitch.vue";
import { vBubble } from "@/utils/pageBubble";
import { editDefaultLang } from "@/lang/index";
import { bindCanvasImage } from "@/utils/image";
import { bufferToBase64, base64ToBuffer, stringToBuffer } from "@/utils/base64";
import { bindFormBox } from "@/utils/bindFormBox";
import moment from "@/lang/moment/zh-cn";
import { useI18n } from "vue-i18n";

import { useConfig } from "@/stores/config";
import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
import { useStorage } from "@vueuse/core";

// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { User, ChatDotSquare, Phone, Back, Loading, Lock, Iphone, Message, Timer, Postcard, Avatar, ArrowDown } from "@element-plus/icons-vue";
import { ElMessage, ElForm, ElFormItem, ElInput, ElAlert } from "element-plus";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { faGithub, faWeixin } from "@fortawesome/free-brands-svg-icons";

import { requireCaptcha, captchaForImage, MFAMethod, MFAMethodOption, loginChannels, loginChannelsOption, loginResultType, loginResultTypeOption, type LoginData, getMFAMethods } from "@/api/system";
import { gender, genderOption } from "@/api/personnel";
import { SERVER, Method, type Response } from "@/api/service/common";
import request from "@/api/service/index";
// import CryptoJS from "crypto-js";
import { buildValidatorData } from "@/utils/validate";
// import backgroundImage from "@/assets/bg.png";
import swiper1 from "@/views/login/assets/img/1.png";
import swiper2 from "@/views/login/assets/img/2.png";
import swiper3 from "@/views/login/assets/img/3.png";
import loginBg from "@/views/login/assets/img/login_bg.png";
import loginLeft from "@/views/login/assets/img/login_left.png";
import loginDh from "@/views/login/assets/img/login_dh.png";
import loginYuy from "@/views/login/assets/img/login_yuy.png";
import { getAppointUserPasswordStrategyMsg, getUser } from "@/api/personnel";

import BindMFAWizard from "@/views/pages/routine/bindMFAWizard.vue";

const vFocus: import("vue").Directive = {
  mounted(el) {
    const inputs = el.getElementsByTagName("input");
    nextTick(() => {
      for (let index = 0; index < inputs.length; index++) {
        if (inputs[index].value) continue;
        else return inputs[index].focus();
      }
    });
  },
  // 移除 updated 钩子，避免在组件更新时重复聚焦
  // updated(el) {
  //   const inputs = el.getElementsByTagName("input");
  //   nextTick(() => {
  //     for (let index = 0; index < inputs.length; index++) {
  //       if (inputs[index].value) continue;
  //       else return inputs[index].focus();
  //     }
  //   });
  // },
};

const loading = ref(false);

const formRef = ref<InstanceType<typeof ElForm>>();
const { t } = useI18n();
provide(formRefKey, formRef);
provide(workResultKey, workResult);
// const captchaImg = ref<HTMLCanvasElement>(document.createElement("canvas"));
const dialogVisible = ref(false);
const dialogVisibleManage = ref(false);
const serviceTerm = useStorage("privacy", false);
enum operateType {
  LOGIN = "LOGIN",
  SIGNIN = "SIGNIN",
  VERIFY = "VERIFY",
  RETRIEVE = "RETRIEVE",
  PASSWORD_EXPIRED = "PASSWORD_EXPIRED",
  SELECT_ACCOUNT = "SELECT_ACCOUNT",
}
const type = ref<keyof typeof operateType>(operateType.LOGIN);

const router = useRouter();
const siteConfig = useSiteConfig();
console.log(siteConfig, 620);

const config = useConfig();

const currentLanguage = computed(() => {
  const lang = config.lang.langArray.find((item) => item.name === config.lang.defaultLang);
  return lang ? lang.value : "简体中文";
});

const loadingCaptcha = ref(false);
const coolingCaptcha = ref(0);
watch(coolingCaptcha, (cooling) => {
  if (cooling !== 0) setTimeout(() => coolingCaptcha.value !== 0 && (coolingCaptcha.value = cooling - 1), 1000);
});

const methods = ref<(keyof typeof loginChannels)[]>(siteConfig.loginChannels);
const active = ref<keyof typeof loginChannels>(methods.value[0]);

const mfaMethods = ref<(keyof typeof MFAMethod)[]>(Object.keys(MFAMethod) as (keyof typeof MFAMethod)[]);
const mfaActive = ref<keyof typeof MFAMethod>(mfaMethods.value[0]);

const accountList = ref<Record<"uid" | "account" | "phone" | "email" | "registrationTime" | "lastLoginTime", string>[]>([]);

// 抑制 ResizeObserver 错误的处理函数
const resizeObserverErrorHandler = (e: ErrorEvent) => {
  if (e.message && e.message.includes("ResizeObserver loop completed with undelivered notifications")) {
    e.stopImmediatePropagation();
    return false;
  }
};

onMounted(() => {
  // 添加全局错误监听器来抑制 ResizeObserver 错误
  window.addEventListener("error", resizeObserverErrorHandler);
});

onUnmounted(() => {
  // 组件卸载时移除监听器
  window.removeEventListener("error", resizeObserverErrorHandler);
});

watch(methods, () => (active.value = methods.value[0]));
watch(mfaMethods, () => (mfaActive.value = mfaMethods.value[0]));

const bindMFAWizardRef = ref<InstanceType<typeof BindMFAWizard>>();

const loginMethods = computed(() => loginChannelsOption.find(({ value }) => value === active.value));
const mfaLoginMethods = computed(() => MFAMethodOption.find(({ value }) => value === mfaActive.value));

// const showLeft = computed(() => ["unms.sst.net.cn"].includes(location.host));

const activeSlideIndex = ref(0);

const swiper = reactive({
  list: [
    {
      id: 1,
      url: swiper3,
      title: "强化客户感知",
      subtitle: "实时网络监控功能，了解设备运行情况，<br />及时发现并自动处理网络问题，确保客户的业务持续稳定运行",
    },
    {
      id: 2,
      url: swiper1,
      title: "降低人力成本",
      subtitle: "可减少上门巡检和故障排查，无需现场技术员，<br />节省维修时间和成本，同时保障设备安全，降低丢失率",
    },
    {
      id: 3,
      url: swiper2,
      title: "综合提升管理能力",
      subtitle: "集中管理资源，简化流程，减少手动操作，提升效率，<br />统一平台管理，确保信息透明，消除信息孤岛",
    },
  ],
});

const form = reactive({
  uid: "",
  certificate: "",
  captcha: "",

  username: "",
  password: "",
  lengthen: false,

  name: "",
  nickname: "",
  account: "",
  email: "",
  gender: gender.SECRET,
  birthday: "",
  rePassword: "",

  phone: "",
  code: "",

  ticket: "",
});
const userInfo = ref({
  userId: "",
  passwordLength: 0,
});

function handleClose() {
  dialogVisible.value = false;
}

function handleCloseManage() {
  dialogVisibleManage.value = false;
}

async function workResult(result?: Promise<Response<LoginData>>) {
  if (!result) return;
  try {
    const { success, message, data } = await result;
    /* 强制校验绑定mfa */
    // if (["TOKEN"].includes(data.type)) {
    //   const { success: mfaSuccess, message: mfaMessage, data: mfaData } = await getMFAMethods({}, `${data.token.token_type} ${data.token.access_token}`);
    //   if (!mfaSuccess) throw new Error(mfaMessage);
    //   const isBindMfa = mfaData.includes(MFAMethod.TOTP) ? true : bindMFAWizardRef.value ? await bindMFAWizardRef.value.open(`${data.token.token_type} ${data.token.access_token}`) : false;
    //   if (!isBindMfa) return;
    // }
    /* --------------- */
    if (success) {
      const info = ((type) => {
        switch (type) {
          case `${superBaseRoute.name}Login`:
            return useSuperInfo();
          case `${adminBaseRoute.name}Login`:
            return useAdminInfo();
          case `${usersBaseRoute.name}Login`:
            return useUsersInfo();
          default:
            return useSuperInfo();
        }
      })(router.currentRoute.value.name);

      switch (data.type) {
        case loginResultType.TOKEN:
          changeType(type.value);
          if (!data.token) throw Object.assign(new Error(`未知Token`), { $message: message, success, data });
          info.dataFill({ token: `${data.token.token_type} ${data.token.access_token}`, refreshToken: form.lengthen ? data.token.refresh_token : "" });
          await router.push({ name: siteConfig.current, query: router.currentRoute.value.query, params: router.currentRoute.value.params });
          break;
        case loginResultType.NEED_MFA:
          changeType(operateType.VERIFY);
          if (!data.mfaTicket.ticket) throw Object.assign(new Error(`二次验证状态无效`), { $message: message, success, data });
          form.ticket = data.mfaTicket.ticket;
          mfaMethods.value = data.mfaTicket.methods instanceof Array ? data.mfaTicket.methods : [];
          break;
        case loginResultType.PASSWORD_EXPIRED:
          changeType(operateType.PASSWORD_EXPIRED);
          ElMessage.info(loginResultTypeOption.find(({ value }) => value === data.type)?.label);
          form.ticket = data.passwordTicket.ticket;
          // userInfo.value = data.passwordTicket.userId;
          getUserPasswordStrategy(data.passwordTicket.userId);
          form.password = "";
          form.rePassword = "";
          break;
        case loginResultType.SELECT_ACCOUNT:
          changeType(operateType.SELECT_ACCOUNT);
          if (!data.selectAccountTicket.ticket) throw Object.assign(new Error(`选择账号状态无效`), { $message: message, success, data });
          form.ticket = data.selectAccountTicket.ticket;
          accountList.value = (data.selectAccountTicket.accounts instanceof Array ? data.selectAccountTicket.accounts : []).map((v) => ({ ...v, registrationTime: moment(v.registrationTime, "x").format("YYYY.MM.DD"), lastLoginTime: moment(v.lastLoginTime, "x").format("YYYY.MM.DD") }));
          form.uid = "";
          break;
        case "PASSWORD_ILLEGAL":
          changeType(operateType.PASSWORD_EXPIRED);
          ElMessage.info(loginResultTypeOption.find(({ value }) => value === data.type)?.label);
          form.ticket = data.passwordTicket.ticket;
          // userInfo.value = data.passwordTicket.userId;
          getUserPasswordStrategy(data.passwordTicket.userId);
          form.password = "";
          form.rePassword = "";
          break;
        default:
          throw Object.assign(new Error(`未知类型-${data.type}`), { $message: message, success, data });
      }
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    active.value = loginChannels.PASSWORD;
    if (error instanceof Error) {
      const res: Record<string, unknown> = { ...error };
      if (res.data && res.data instanceof Object) {
        const duration = Number((res.data as { blockExpire: string }).blockExpire);
        const failureCount = Number((res.data as { failureCount: string }).failureCount);
        const failureLimit = Number((res.data as { failureLimit: string }).failureLimit);
        // const passwordTicket = res.data.passwordTicket;

        // if (!isNaN(duration)) return void ElMessage.error(`${error.message}`);

        if (!isNaN(failureCount)) return void ElMessage.error(`当前${error.message}${failureCount}次,剩余${failureLimit - failureCount}次机会`);

        if (duration && !isNaN(duration)) {
          const remainingMinutes = Math.floor((Number(duration) - new Date().getTime()) / (1000 * 60));
          return void ElMessage.error(`${error.message}，请${remainingMinutes}分钟后重试`);
        } else {
          return void ElMessage.error(`${error.message}`);
        }
      }
      ElMessage.error(error.message);
    }
  }
}
async function getUserPasswordStrategy(id) {
  await getAppointUserPasswordStrategyMsg({ userId: id }).then((res) => {
    if (res.success) {
      // console.log(res.data)
      userInfo.value.passwordLength = res.data.minLength;
    }
  });
}

async function loginSubmit($active: keyof typeof loginChannels) {
  if (!formRef.value) return;
  if (!loginMethods.value) return;
  if (loading.value) return;
  if (!serviceTerm.value) {
    return ElMessage.error("请同意用户隐私条款");
  }
  useStorage("privacy", true);

  let result: Promise<Response<LoginData>> | undefined = undefined;
  formRef.value.clearValidate();

  const info = ((type) => {
    switch (type) {
      case `${superBaseRoute.name}Login`:
        return useSuperInfo();
      case `${adminBaseRoute.name}Login`:
        return useAdminInfo();
      case `${usersBaseRoute.name}Login`:
        return useUsersInfo();
      default:
        return useSuperInfo();
    }
  })(router.currentRoute.value.name);

  active.value = $active;
  switch ($active) {
    case loginChannels.EMAIL as any:
    case loginChannels.PASSWORD: {
      if (await new Promise((resolve) => formRef.value?.validateField(["username", "password"], resolve))) {
        loading.value = true;
        try {
          const { success, message, data } = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/rsa/key/public`, method: Method.Get, responseType: "json", params: {}, data: new URLSearchParams() });
          if (!success) throw Object.assign(new Error(message), { success, data });
          if (typeof data !== "string") throw new Error("公钥获取失败！");
          const keyData = base64ToBuffer(data);
          let importPublicKey: CryptoKey;
          try {
            importPublicKey = await window.crypto.subtle.importKey("spki", keyData, { name: "RSA-OAEP", hash: { name: "SHA-512" } }, true, ["encrypt"]);
          } catch (error) {
            if (error instanceof Error) throw new Error(`公钥导入失败 ${error.message} \n${bufferToBase64(keyData) === data.replaceAll(/[^A-Za-z0-9+/=]/g, "") ? "" : bufferToBase64(keyData)}`);
            else throw new Error("公钥不合法");
          }
          if (!importPublicKey) throw new Error("没有找到公钥");
          let _password: ArrayBufferLike;
          try {
            _password = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.password));
          } catch (error) {
            throw new Error("密码加密失败");
          }
          if (!_password) throw new Error("密码读取错误，请确认密码是否符合要求，联系管理员排查问题");
          result = request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/login/password`, method: Method.Post, responseType: "json", params: await sendCaptcha(), data: { username: form.username, password: bufferToBase64(_password), lengthen: form.lengthen, ptype: "RSA" } });
        } catch (error) {
          if (error instanceof Error) ElMessage.error(error.message + 1);
        }
      }
      break;
    }
    case loginChannels.REFRESH_TOKEN: {
      loading.value = true;
      result = request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/authentication/sign_in/refresh_token`, method: Method.Post, responseType: "json", params: { refreshToken: info.getToken("refresh") }, data: { lengthen: form.lengthen } });
      break;
    }
    case loginChannels.SMS_CODE: {
      if (await new Promise((resolve) => formRef.value?.validateField(["phone", "code"], resolve))) {
        loading.value = true;
        result = request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/login/sms_code`, method: Method.Post, responseType: "json", params: {}, data: { phone: form.phone, code: form.code, lengthen: form.lengthen } });
      }
      break;
    }
    case loginChannels.GIT_HUB: {
      loading.value = true;
      const { success, message, data } = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/github/authorize_url/login`, method: Method.Get, responseType: "json", params: {}, data: {} });
      if (!success) throw Object.assign(new Error(message), { success, data });
      result = new Promise((resolve, reject) => {
        const windowProxy = window.open(data, "_blank", `location=no, menubar=no, status=no, titlebar=no, toolbar=no, top=0px, left=0px, width=${window.screen.availWidth * 0.45}px, height=${window.screen.availHeight * 0.45}px`);
        if (!windowProxy) return;
        const winLoop = setInterval(() => windowProxy.closed && (done(), reject(new Error("关闭了授权"))), 1000);
        window.addEventListener("message", binding);
        async function binding({ data }: { data: { idp: keyof typeof loginChannels; code: string } }) {
          if (data.idp === loginChannels.GIT_HUB) {
            done();
            try {
              const result = await request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/github/login/code`, method: Method.Post, responseType: "json", params: {}, data: { code: data.code, lengthen: form.lengthen } });
              resolve(result);
            } catch (error) {
              reject(error);
            }
          }
        }
        function done() {
          clearInterval(winLoop);
          window.removeEventListener("message", binding);
        }
      });
      break;
    }
    case loginChannels.WECHAT: {
      loading.value = true;
      break;
    }
  }

  if (result) await workResult(result);
  loading.value = false;
}
async function signinSubmit() {
  if (!formRef.value) return;
  if (loading.value) return;
  formRef.value.clearValidate();

  try {
    if (!(await new Promise((resolve) => formRef.value?.validateField(["code", "name", "nickname", "account", "phone", "email", "gender", "birthday", "password", "rePassword"], resolve)))) return;
    loading.value = true;
    const { success, message, data } = await request<unknown, Response<LoginData>>({
      url: `${SERVER.IAM}/authentication/sign_up`,
      method: Method.Post,
      responseType: "json",
      params: { smsCode: form.code },
      data: { name: form.name, nickname: form.nickname, account: form.account, phone: form.phone, email: form.email, gender: form.gender, birthday: form.birthday, password: form.password },
    });
    if (success) {
      ElMessage.success("用户成功注册，请重新登录！");
      form.username = form.account;
      form.password = form.rePassword;
      form.code = "";
      form.name = "";
      form.nickname = "";
      form.account = "";
      form.phone = "";
      form.email = "";
      form.gender = gender.SECRET;
      form.birthday = "";
      form.rePassword = "";
      changeType(operateType.LOGIN);
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}
async function verifySubmit() {
  if (!formRef.value) return;
  if (!mfaLoginMethods.value) return;
  if (loading.value) return;
  let result: Promise<Response<LoginData>> | undefined = undefined;
  formRef.value.clearValidate();

  if (await new Promise((resolve) => formRef.value?.validateField(["code"], resolve))) {
    loading.value = true;

    try {
      const { success, message, data } = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/rsa/key/public`, method: Method.Get, responseType: "json", params: {}, data: new URLSearchParams() });
      if (!success) throw Object.assign(new Error(message), { success, data });
      const importPublicKey = await window.crypto.subtle.importKey("spki", base64ToBuffer(data), { name: "RSA-OAEP", hash: { name: "SHA-512" } }, true, ["encrypt"]);
      const _ticket = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.ticket));
      const _code = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.code));

      result = request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/login/mfa`, method: Method.Post, responseType: "json", params: {}, data: { ticket: bufferToBase64(_ticket), method: mfaActive.value, code: bufferToBase64(_code), ptype: "RSA" } });
    } catch (error) {
      console.error(error);
      ElMessage.error("密码读取错误，请确认密码是否符合要求，联系管理员排查问题");
    }
  }

  if (result) await workResult(result);
  loading.value = false;
}
async function resetSubmit() {
  if (!formRef.value) return;
  if (!mfaLoginMethods.value) return;
  if (loading.value) return;
  let result: Promise<Response<{ type: "SUCCESS" | "SELECT_ACCOUNT"; accounts: Record<"uid" | "account" | "phone" | "email" | "registrationTime" | "lastLoginTime", string>[] }>> | undefined = undefined;
  formRef.value.clearValidate();

  switch (mfaActive.value) {
    case MFAMethod.PASSWORD:
      loading.value = true;
      break;
    case MFAMethod.SMS:
      if (!(await new Promise((resolve) => formRef.value?.validateField(accountList.value.length ? ["uid"] : ["code", "phone", "password", "rePassword"], resolve)))) return (loading.value = false), undefined;
      loading.value = true;
      try {
        const { success, message, data } = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/rsa/key/public`, method: Method.Get, responseType: "json", params: {}, data: new URLSearchParams() });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const importPublicKey = await window.crypto.subtle.importKey("spki", base64ToBuffer(data), { name: "RSA-OAEP", hash: { name: "SHA-512" } }, true, ["encrypt"]);
        const _password = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.password));
        const _rePassword = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.rePassword));

        result = request<unknown, Response<{ type: "SUCCESS" | "SELECT_ACCOUNT"; accounts: Record<"uid" | "account" | "phone" | "email" | "registrationTime" | "lastLoginTime", string>[] }>>({ url: `${SERVER.IAM}/retrieve_password/sms`, method: Method.Post, responseType: "json", params: {}, data: { ident: form.phone, code: form.code, password: bufferToBase64(_password), confirmationPassword: bufferToBase64(_rePassword), ...(accountList.value.length ? { userId: form.uid } : {}), ptype: "RSA" } });
      } catch (error) {
        console.error(error);
        ElMessage.error("密码读取错误，请确认密码是否符合要求，联系管理员排查问题");
      }
      break;
    case MFAMethod.EMAIL:
      if (!(await new Promise((resolve) => formRef.value?.validateField(["code", "email", "password", "rePassword"], resolve)))) return (loading.value = false), undefined;
      loading.value = true;
      try {
        const { success, message, data } = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/rsa/key/public`, method: Method.Get, responseType: "json", params: {}, data: new URLSearchParams() });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const importPublicKey = await window.crypto.subtle.importKey("spki", base64ToBuffer(data), { name: "RSA-OAEP", hash: { name: "SHA-512" } }, true, ["encrypt"]);
        const _password = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.password));
        const _rePassword = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.rePassword));

        result = request<unknown, Response<{ type: "SUCCESS" | "SELECT_ACCOUNT"; accounts: Record<"uid" | "account" | "phone" | "email" | "registrationTime" | "lastLoginTime", string>[] }>>({ url: `${SERVER.IAM}/retrieve_password/email`, method: Method.Post, responseType: "json", params: {}, data: { ident: form.email, code: form.code, password: bufferToBase64(_password), confirmationPassword: bufferToBase64(_rePassword), ptype: "RSA" } });
      } catch (error) {
        console.error(error);
        ElMessage.error("密码读取错误，请确认密码是否符合要求，联系管理员排查问题");
      }
      break;
    case MFAMethod.TOTP:
      break;
  }
  if (result) {
    try {
      const { success, message, data } = await result;
      if (success) {
        if (data) {
          switch (data.type) {
            case loginResultType.SELECT_ACCOUNT:
              accountList.value = (data.accounts instanceof Array ? data.accounts : []).map((v) => ({ ...v, registrationTime: moment(v.registrationTime, "x").format("YYYY.MM.DD"), lastLoginTime: moment(v.lastLoginTime, "x").format("YYYY.MM.DD") }));
              loading.value = false;
              return;
            default:
              break;
          }
        }
        ElMessage.success("成功重置密码！请重新登录！");
        form.password = form.rePassword;
        form.code = "";
        form.account = "";
        form.phone = "";
        form.email = "";
        form.rePassword = "";
        changeType(operateType.LOGIN);
      } else throw Object.assign(new Error(message), { success, data });
    } catch (error) {
      if ([loginChannels.GIT_HUB, loginChannels.WECHAT].includes(active.value as loginChannels)) active.value = loginChannels.PASSWORD;
      if (error instanceof Error) ElMessage.error(error.message);
    }
  }
  loading.value = false;
}
async function expiredSubmit() {
  if (!formRef.value) return;
  if (!mfaLoginMethods.value) return;
  if (loading.value) return;
  let result: Promise<Response<LoginData>> | undefined = undefined;
  formRef.value.clearValidate();

  await nextTick();

  if (await new Promise((resolve) => formRef.value && formRef.value.validateField(["password", "rePassword"], resolve))) {
    loading.value = true;

    try {
      const { success, message, data } = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/rsa/key/public`, method: Method.Get, responseType: "json", params: {}, data: new URLSearchParams() });
      if (!success) throw Object.assign(new Error(message), { success, data });
      const importPublicKey = await window.crypto.subtle.importKey("spki", base64ToBuffer(data), { name: "RSA-OAEP", hash: { name: "SHA-512" } }, true, ["encrypt"]);
      const _ticket = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.ticket));
      const _password = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.password));
      const _rePassword = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.rePassword));

      result = request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/login/password_expired`, method: Method.Post, responseType: "json", params: {}, data: { ticket: bufferToBase64(_ticket), password: bufferToBase64(_password), confirmationPassword: bufferToBase64(_rePassword), ptype: "RSA" } });
    } catch (error) {
      console.error(error);
      ElMessage.error("密码读取错误，请确认密码是否符合要求，联系管理员排查问题");
    }
  }

  if (result) await workResult(result);
  loading.value = false;
}
async function accountSubmit() {
  if (!formRef.value) return;
  if (!mfaLoginMethods.value) return;
  if (loading.value) return;
  let result: Promise<Response<LoginData>> | undefined = undefined;
  formRef.value.clearValidate();

  await nextTick();

  if (await new Promise((resolve) => formRef.value && formRef.value.validateField(["uid"], resolve))) {
    loading.value = true;
    result = request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/login/select_account`, method: Method.Post, responseType: "json", params: {}, data: { ticket: form.ticket, uid: form.uid } });
  }

  if (result) await workResult(result);
  loading.value = false;
}

/**
 **************************************************************
 **************************************************************
 **************************************************************
 **************************************************************
 **************************************************************
 */

function changeType($type: keyof typeof operateType) {
  type.value = $type;
  coolingCaptcha.value = 0;
  switch ($type) {
    case operateType.RETRIEVE:
      mfaActive.value = MFAMethod.SMS;
      // mfaActive.value = MFAMethod.EMAIL;
      accountList.value = [];
      form.uid = "";
      form.username = "";
      form.password = "";
      form.name = "";
      form.nickname = "";
      form.account = "";
      form.email = "";
      form.gender = gender.SECRET;
      form.birthday = "";
      form.rePassword = "";
      form.phone = "";
      form.code = "";
      break;
  }
}

function sendCaptcha() {
  return new Promise<{ certificate?: string; captcha?: string }>((resolve) => {
    nextTick(async () => {
      if (!formRef.value) return resolve({});
      if (loadingCaptcha.value) return resolve({});
      loadingCaptcha.value = true;
      formRef.value.clearValidate();
      try {
        if (!type.value) {
          resolve({});
          return ElMessage.error("未知操作");
        } else if (type.value === operateType.SIGNIN) {
          if (!(await new Promise((resolve) => formRef.value?.validateField(["phone"], resolve)))) return ElMessage.error(`请填写正确的手机号`);
          await inputCertificate(form.phone, ($form) => (resolve($form), request<unknown, { success: boolean; message: string }>({ url: `${SERVER.IAM}/authentication/sign_up/sms_code`, method: Method.Get, responseType: "json", params: { ...$form, phone: form.phone }, data: {} })));
          ElMessage.success("验证码发送成功");
          coolingCaptcha.value = 60;
          return;
        } else if (type.value === operateType.LOGIN) {
          switch (active.value) {
            case loginChannels.EMAIL:
            case loginChannels.PASSWORD:
              if (!(await new Promise((resolve) => formRef.value?.validateField(["username"], resolve)))) return ElMessage.error(`请填写正确的账号`);
              if (!(await requireCaptcha({ username: form.username }))) {
                resolve({});
              } else {
                await inputCertificate(form.username, async ($form) => (resolve($form), { success: true, message: "" }));
              }
              return;
            case loginChannels.REFRESH_TOKEN:
              resolve({});
              return;
            case loginChannels.SMS_CODE:
              if (!(await new Promise((resolve) => formRef.value?.validateField(["phone"], resolve)))) return ElMessage.error(`请填写正确的手机号`);
              await inputCertificate(form.phone, ($form) => (resolve($form), request<unknown, { success: boolean; message: string }>({ url: `${SERVER.IAM}/login/send_sms_login_code`, method: Method.Post, responseType: "json", params: { ...$form, phone: form.phone }, data: {} })));
              ElMessage.success("验证码发送成功");
              coolingCaptcha.value = 60;
              return;
            case loginChannels.GIT_HUB:
              resolve({});
              return;
            case loginChannels.WECHAT:
              resolve({});
              return;
          }
        } else if (type.value === operateType.RETRIEVE) {
          switch (mfaActive.value) {
            case MFAMethod.SMS:
              if (!(await new Promise((resolve) => formRef.value?.validateField(["phone"], resolve)))) return ElMessage.error(`请填写正确的手机号`);
              await inputCertificate(form.phone, ($form) => (resolve($form), request<unknown, { success: boolean; message: string }>({ url: `${SERVER.IAM}/retrieve_password/send_sms_code`, method: Method.Post, responseType: "json", params: { ...$form, phone: form.phone }, data: {} })));
              ElMessage.success("验证码发送成功");
              // getUser(form.phone);
              coolingCaptcha.value = 60;
              return;
            case MFAMethod.EMAIL:
              if (!(await new Promise((resolve) => formRef.value?.validateField(["email"], resolve)))) return ElMessage.error(`请填写正确的邮箱地址`);
              await inputCertificate(form.email, ($form) => (resolve($form), request<unknown, { success: boolean; message: string }>({ url: `${SERVER.IAM}/retrieve_password/send_email_code`, method: Method.Post, responseType: "json", params: { ...$form, email: form.email }, data: {} })));
              ElMessage.success("验证码发送成功");
              coolingCaptcha.value = 60;
              // getUser(form.email);

              return;
          }
        }
      } catch (error) {
        resolve({});
        return;
      } finally {
        loadingCaptcha.value = false;
      }
    });
  });
}
//  getUserPasswordStrategy
function getUserInfo(data) {
  getUser({ pageNumber: 1, pageSize: 20, keyword: data, schemas: "ROLE", scope: "ALL" }).then((res) => {
    if (res.success) {
      getUserPasswordStrategy(res.data[0].id);
    }
  });
}

async function sendMFACaptcha() {
  if (!formRef.value) return;
  if (loadingCaptcha.value) return;
  loadingCaptcha.value = true;
  formRef.value.clearValidate();
  try {
    let result: Promise<Response<unknown>> | undefined = undefined;
    switch (mfaActive.value) {
      case MFAMethod.PASSWORD:
        break;
      case MFAMethod.SMS:
        result = request<unknown, Response<unknown>>({ url: `${SERVER.IAM}/login/mfa/code/sms`, method: Method.Post, responseType: "json", params: { ticket: form.ticket }, data: {} });
        break;
      case MFAMethod.EMAIL:
        result = request<unknown, Response<unknown>>({ url: `${SERVER.IAM}/login/mfa/code/email`, method: Method.Post, responseType: "json", params: { ticket: form.ticket }, data: {} });
        break;
      case MFAMethod.TOTP:
        break;
    }
    if (!result) throw new Error("未知验证码");
    const { success, message, data } = await result;
    if (success) {
      coolingCaptcha.value = 60;
      ElMessage.success("验证码发送成功");
    } else throw Object.assign(new Error(message), { success, data });
    return { success, message };
  } catch (error) {
    return;
  } finally {
    loadingCaptcha.value = false;
  }
}

async function inputCertificate(code: string, callback: (form: { certificate?: string; captcha?: string }) => Promise<{ success: boolean; message: string } & Record<string, unknown>>): Promise<void> {
  const $form = reactive({
    title: `${t("login.Enter the verification code")}`,
    certificate: "",
    captcha: "",
  });
  2;
  const $input = ref<import("element-plus").InputInstance>();
  const $canvasRef = ref<HTMLCanvasElement>();
  const unWatch = watch($canvasRef, ($canvas) => {
    if ($canvas instanceof HTMLCanvasElement) {
      unWatch();
      $form.captcha = "";
      $form.certificate = String(`xxxxxxxx-xxxx-4xxx-yxxx-${Date.now().toString(16).padStart(12, "x")}`).replace(/[xy]/g, (c) => Number(c === "x" ? (Math.random() * 16) | 0 : (((Math.random() * 16) | 0) & 0x3) | 0x8).toString(16));
      updateCertificate($canvas, $form.certificate).then(() => nextTick(() => $input.value?.focus()));
    }
  });
  await bindFormBox(
    [
      h(ElAlert, { type: "info", title: `${t("login.verification code")}`, description: `${t("login.verification1")}`, showIcon: true, closable: false, style: { marginBottom: "22px" } }),
      h(ElFormItem, { rules: [{ required: true, message: `${t("login.verification2")}`, trigger: "blur" }], prop: "captcha", label: "", size: "large" }, () => [
        h(ElInput, { "ref": (vm) => ($input.value = vm as import("element-plus").InputInstance), "prefixIcon": ChatDotSquare, "placeholder": `${t("login.verification2")}`, "clearable": true, "modelValue": $form.captcha, "onUpdate:modelValue": (v) => ($form.captcha = v), "style": { verticalAlign: "top", width: "calc(100% - 118px)", marginRight: "12px" } }),
        h("canvas", {
          ref: (vm) => ($canvasRef.value = vm as HTMLCanvasElement),
          class: ["captcha-img"],
          height: "40",
          width: "100",
          title: "看不清，换一张",
          onClick({ target }: PointerEvent) {
            if (target instanceof HTMLCanvasElement) {
              $form.captcha = "";
              $form.certificate = String(`xxxxxxxx-xxxx-4xxx-yxxx-${Date.now().toString(16).padStart(12, "x")}`).replace(/[xy]/g, (c) => Number(c === "x" ? (Math.random() * 16) | 0 : (((Math.random() * 16) | 0) & 0x3) | 0x8).toString(16));
              updateCertificate(target, $form.certificate).then(() => nextTick(() => $input.value?.focus()));
            }
          },
        }),
      ]),
    ],
    $form,
    async () => {
      const { success, message, data } = await callback({ certificate: $form.certificate, captcha: $form.captcha });
      if (!success) throw Object.assign(new Error(message), { success, data });
      return { success, message };
    }
  );
}
async function updateCertificate(canvasRef: HTMLCanvasElement, certificate: string) {
  try {
    const { success, data, message } = await captchaForImage({ certificate });
    if (success) {
      await bindCanvasImage(canvasRef.getContext("2d") as CanvasRenderingContext2D, data);
    } else throw Object.assign(new Error(message), { success, data, message });
  } catch (error) {
    return;
  }
}

function handleLogoClick() {
  const host = location.host;
  let targetUrl = "/";

  switch (host) {
    case "localhost:8080":
    case "*************:20082": //B4环境
    case "*************:20082": //203环境
      targetUrl = "https://*************:20082/#/";
      break;
    case "*************": // 验收环境
      targetUrl = "http://*************/#/";
      break;
    case "www.ainoc.cn": // 生产环境
      targetUrl = "https://www.ainoc.cn/#/";
      break;
    default:
      // 默认情况下使用路由跳转
      router.push("/");
      return;
  }

  // 跳转到门户
  window.open(targetUrl, "_blank");
}
</script>

<style lang="scss" scoped>
// -----------------------------------------新UI样式start-------------------------------------------------------
.login-container {
  height: 100vh;
  width: 100vw;
  background-image: url("@/views/login/assets/img/login_bg.png");
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  color: #fff;
  overflow: hidden;
  // header start
  .login-header-bar {
    padding: 20px 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;

    .logo-group {
      display: flex;
      align-items: center;
      cursor: pointer;
      .header-logo {
        height: 33px;
      }
    }

    .logo-separator {
      margin: 0 10px;
      font-weight: normal;
    }

    .logo-text-secondary {
      font-size: 20px;
      font-weight: normal;
      margin-left: 5px;
    }

    .header-right {
      display: flex;
      align-items: center;
      .support-phone,
      .language-switcher {
        display: flex;
        align-items: center;
        margin-left: 25px;
        cursor: pointer;
        font-size: 14px;
        color: #fff;
        .header-icon {
          height: 20px;
          margin-right: 8px;
        }
      }

      .support-phone {
        padding: 5px 15px;
        border-radius: 30px;
        border: 1px solid #0444c8;
        color: #0343c6;
      }

      .language-switcher {
        color: #868d9f;
        &:focus,
        &:focus-visible {
          outline: none;
        }
        .el-icon {
          color: #868d9f;
          margin-left: 5px;
        }
      }
    }
  }
}
.login-main {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 60px; /* Header Height */
  padding-bottom: 40px; /* Footer Height */

  .login-main-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // width: 100%;
    max-width: 1500px;
    // margin-right: 10px;
    // padding-left: 151px;
    // padding-right: 151px;
    // background-color: #daa520;

    .left-panel {
      flex-basis: 65%;
      // background-color: #0343c6;
      width: 902px;
      // padding-right: 129px;
      margin-left: 100px;

      .left-content {
        text-align: center;
        .main-title {
          font-size: 34px;
          color: #333333;
          margin-bottom: 10px;
          font-weight: 300;
        }
        .sub-title {
          font-size: 16px;
          line-height: 1.8;
          color: #666666;
        }
      }

      .el-carousel {
        width: 902px;
        height: 554px;
        margin: 0 auto;
      }

      .carousel-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    .right-panel {
      flex-basis: 35%;
      width: 400px;
      max-width: 450px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 50px;
      // padding-right: 151px;
      // background-color: #fff;

      .form-container {
        width: 100%;
        padding: 35px 40px;
        border-radius: 10px;
        backdrop-filter: blur(10px);
        box-shadow:
          0 15px 40px rgba(29, 89, 255, 0.15),
          0 3px 10px rgba(31, 99, 255, 0.08);
        color: #333;

        .login-tabs {
          margin-bottom: 20px;
        }
      }
    }
  }
}

.login-footer-bar {
  padding: 15px 0;
  text-align: center;
  font-size: 13px;
  color: #616161;
  flex-shrink: 0;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

// 移动端适配
@media (max-width: 768px) {
  .login-container {
    .login-header-bar {
      padding: 15px 20px;
      .header-right {
        display: none; // 移动端隐藏右侧头部内容
      }
    }

    .login-main {
      padding-top: 80px;
      padding-bottom: 20px;

      .login-main-content {
        flex-direction: column;
        padding: 0 20px;

        .left-panel {
          display: none; // 移动端隐藏左侧轮播区域
        }

        .right-panel {
          flex-basis: 100%;
          max-width: 100%;
          width: 100%;

          .form-container {
            padding: 25px 20px;
            margin: 0 auto;
            max-width: 400px;
          }
        }
      }
    }

    .login-footer-bar {
      font-size: 11px;
      padding: 10px 0;
    }
  }
}

// 平板端适配
@media (max-width: 1024px) and (min-width: 769px) {
  .login-container {
    .login-main {
      .login-main-content {
        padding: 0 30px;

        .left-panel {
          flex-basis: 50%;
          padding-right: 40px;

          .left-content {
            .main-title {
              font-size: 28px;
            }
            .sub-title {
              font-size: 14px;
            }
          }
        }

        .right-panel {
          flex-basis: 45%;

          .form-container {
            padding: 30px 30px;
          }
        }
      }
    }
  }
}
:deep(.el-form-item__error) {
  text-align: left;
}

// 针对包含forgot-password的表单项，调整错误提示位置
.forgot-password + :deep(.el-form-item__error) {
  margin-top: -40px;
}
// -----------------------------------------新UI样式end-------------------------------------------------------
.user-login {
  :deep(.el-dialog) {
    .el-dialog__header {
      // background: #409eff;
      width: 100%;
      padding-bottom: 15px;
      padding-top: 15px;
      .el-dialog__title {
        // color: #fff;
      }
      .el-dialog__close {
        // color: #fff;
      }
    }
  }
  :deep(.el-form-item__error) {
    text-align: left;
  }
}
.term-text {
  text-indent: 30px;
}
.term {
  display: flex;
  align-items: center;
  color: #606266;
  > b {
    color: #409eff;
    font-weight: 400;
    text-decoration: underline;
  }
}
.userManage {
  color: #606266;
  cursor: pointer;
}
#login-message {
  background: url("@/assets/login/bg.png") no-repeat;
  background-size: 100% 100%;
}
.block {
  width: 100%;
  height: 100%;
}
:deep(.block) {
  .el-carousel {
    height: 100%;
    .el-carousel__container {
      height: 100%;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .el-carousel__button {
      height: 5px;
      border-radius: 3px;
    }
  }
}

.switch-language {
  position: fixed;
  top: 20px;
  right: 90px;
  z-index: 1;
}
.switch-theme {
  position: fixed;
  top: 22px;
  right: 20px;
  z-index: 1;
}
.loading-chase {
  margin-left: auto;
  margin-right: auto;
  width: 40px;
  height: 40px;
  position: relative;
  animation: loading-chase 2.5s infinite linear both;

  &-dot {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    animation: loading-chase-dot 2s infinite ease-in-out both;
    &:before {
      content: "";
      display: block;
      width: 25%;
      height: 25%;
      background-color: var(--el-color-primary);
      border-radius: 100%;
      animation: loading-chase-dot-before 2s infinite ease-in-out both;
    }
    &:nth-child(1) {
      animation-delay: -1.1s;
      &:before {
        animation-delay: -1.1s;
      }
    }
    &:nth-child(2) {
      animation-delay: -1s;
      &:before {
        animation-delay: -1s;
      }
    }
    &:nth-child(3) {
      animation-delay: -0.9s;
      &:before {
        animation-delay: -0.9s;
      }
    }
    &:nth-child(4) {
      animation-delay: -0.8s;
      &:before {
        animation-delay: -0.8s;
      }
    }
    &:nth-child(5) {
      animation-delay: -0.7s;
      &:before {
        animation-delay: -0.7s;
      }
    }
    &:nth-child(6) {
      animation-delay: -0.6s;
      &:before {
        animation-delay: -0.6s;
      }
    }
  }
}

@keyframes loading-chase {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-chase-dot {
  80%,
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-chase-dot-before {
  50% {
    transform: scale(0.4);
  }
  100%,
  0% {
    transform: scale(1);
  }
}

.line-radio {
  :deep(.el-radio__label) {
    flex: 1;
  }
}

:deep(.el-carousel__indicator--horizontal) {
  .el-carousel__button {
    width: 20px;
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
  }

  &.is-active .el-carousel__button {
    background: #3a84ff;
  }
}

.form-title {
  font-size: 24px;
}

.forgot-password {
  display: flex;
  justify-content: flex-end;
  margin-top: 5px;
  margin-bottom: -5px; // 减少底部间距，避免挤压错误提示
  width: 100%;
}

// 增加一个额外的选择器，确保样式能覆盖Element Plus的默认样式
.form-container :deep(.forgot-password .el-link) {
  margin-left: auto;
}
</style>
