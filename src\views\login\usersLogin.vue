<template>
  <div class="switch-language">
    <el-dropdown size="large" :hide-timeout="50" placement="bottom-end" :hide-on-click="true">
      <i style="font-size: 28px; color: var(--el-text-color-secondary)">
        <svg style="width: 1em; height: 1em; vertical-align: bottom; fill: currentColor; overflow: hidden" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <path d="M714.55 251.05c-4.418-7.972-18.996-23.674-27.954-26.65-11.958-1.842-4.796 8.826-4.796 13.4-3.086 2.314-7.882 4.908-10.204 8-1.398 1.2 3.408 12.206 12.674 8.67s3.928 3.598 10.328 17.73 19.214 14.414 24.98 10.134C726.888 276.91 718.572 258.31 714.55 251.05zM751.6 341.2c-0.002 0.012-0.008 0.022-0.01 0.034C751.59 341.258 751.594 341.258 751.6 341.2zM869.2 394.2C870 394.2 865.6 393.4 869.2 394.2L869.2 394.2zM512 64C264.6 64 64 264.6 64 512c0 247.402 200.6 448 448 448 247.402 0 448-200.598 448-448C960 264.6 759.402 64 512 64zM346.798 858.848c-11.03-6.266-18.748-15.12-21.922-31.858-7.894-41.616-0.606-64.134 35.924-88.49 17.208-11.472 20.044-25.344 33.8-39.1 3.37-4.28 12.422-20.748 18.4-21.6 7.274-1.558 27.418-5.054 31.4-13 7.58-10.292 24.254-54.796 31.6-59.204 11.994-7.85 28.838-31.92 16.6-46.2-14.042-17.286-33.598-17.532-51.8-26.602-16.03-8.016-23.148-44.562-35.4-57.202-23.824-24.272-59.998-38.398-90-53.4-16.232-6.5-14.688-8.512-27.4 4.2-16.324 16.324-40.186-4.134-42.6-21-0.18-5.604-6.592-36.432-2.8-38.8 35.912-22.45-16.656-17.28-21-27.6-10.71-29.986 26.016-53.72 50.8-56.2 27.77-3.476 33.568 43.79 44.2 39.8 5.112-2.552 5.132-24.788 5.8-29.6 2.534-16.058 7.176-18.546 22.8-24.35 18.196-6.758 32.126-15.234 51.4-18.45 22.012-6.39 40.634-3.066 57.6-17.2 7.776-5.83 13.408 3.16 20.8 4.8 15.998 3.198 19.4-22.2 19.4-31.8-0.068-9.33 2.53-17.26-9.4-27.2-15.612-12.138-38.058-1.738-50.6 10.8-15.148 14.07-32.714 13.1-27.6-11.2 1.268-8.866 20.418-19.174 28-24 7.4-4.444 11.808 5.044 20.8 2.8 13.158-2.926 18.068 9.47 33.6 9.6 6.466-1.462 29.592-13.762 17.228-38.732C508.286 128.042 510.14 128 512 128c3.71 0 7.414 0.068 11.11 0.172 5.61 17.762-11.93 32.886-3.11 55.228 17.58 42.95 31.984 6.028 49.4-14 5.698-5.698 9.266-4.422 18.6-6.2 5.792-0.966 14.11-18.54 16.786-23.964 40.116 9.912 78.242 26.254 113.134 48.51-14.894 1.584-18.696-2.792-18.468 14.754 0.084 6.634 0.594 26.638 10.548 28.3 16.554 1.95 13.562 13.664 29.102 15.7 14.886 1.952 5.632 15.106 11.702 28.1 6.786 20.454-39.612 26.604-48.604 29.6-27.28 9.108 12.68 48.82 30.4 44.6 5.634-1.408 24.43-3.804 25.734-10.134-0.022-0.646-3.672-22.024-1.734-24.466 3.066-3.868 10.246-5.892 21.552 0.51 26.748 15.146 40.292 51.4 71.794 58.59 4.292 0.978 11.72-0.3 16.988 4.066 4.462 6.03 15.84 16.832 2.262 16.832-18.034-4.006-27.56 1.718-42.396-9.748-15.466-11.954-25.086-21.782-45.5-22.152-17.58-0.318-33.36-7.638-51.476-4.7-10.1 1.638-20.076 5.622-29.824 8.6-9.234 3.076-11.02 19.53-20.4 21.4-38.934 9.162-30.522 46.338-37.546 75.826-2.702 11.324-12.05 42.006-1.852 52.174 18.24 17.708 39.566 42.07 66.352 45.926 14.412 2.074 45.908-9.152 55.646 5.474 4.124 8.208 14.894-6.06 17.8 1.202 7.326 21.976-8.526 36.372-8.674 57.102-0.22 30.406 18.356 41.14-6.526 68.296-27.284 27.214-8.666 61.576-23.8 94-8.084 16.674-9.616 39.788-21.744 47.554-26.642 17.064-55.4 30.798-85.428 40.768-53.254 17.676-110.31 23.344-166.034 16.912-29.238-3.376-58.082-10.114-85.76-20.13-7.538-2.726-14.56-5.01-20.944-7.408C356.286 863.238 351.518 861.1 346.798 858.848zM263.398 804.658c-0.936-0.794-1.87-1.592-2.798-2.396-6.888-5.984-13.604-12.226-20.12-18.742-6.776-6.776-13.268-13.762-19.468-20.942-0.07-0.082-0.14-0.164-0.21-0.246-1.274-1.476-2.514-2.972-3.764-4.468C113.668 632.942 98.518 450.008 185.2 310.2c12.156 12.156-8.202 36.048 15 52 10.248 7.212 20.042 4.318 18 17.6-1.986 12.898 10.71 12.67 12.6 24 5.684 24.858 38.414 37.284 19.8 65.2-18.236 25.25-33.984 53.458-20.2 85.6 4.508 11.72 15.838 23.716 24.444 32.488 15.626 15.932 14.754 6.376 14.754 26.512-0.312 8.746 10.522 17.794 9.4 23.4-2.002 16.022-5.808 31.86-7.8 47.8-3.584 57.276-0.258 113.324 42.6 156.2-15.938-9.612-31.272-20.498-45.936-32.58C266.366 807.182 264.88 805.922 263.398 804.658z" />
        </svg>
      </i>
      <template #dropdown>
        <el-dropdown-menu class="chang-lang">
          <el-dropdown-item v-for="item in config.lang.langArray" :key="item.name" @click="editDefaultLang(item.name)">
            {{ item.value }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
  <!-- <div class="switch-theme"><DarkSwitch @click="config.setLayout('isDark', !config.layout.isDark)" /></div> -->

  <div class="tw-bg-[var(--el-bg-color)]" v-bubble @contextmenu.stop=""></div>

  <div class="user-login tw-relative tw-flex tw-h-full tw-w-full tw-flex-col lg:tw-flex-row-reverse">
    <div v-if="!siteConfig.platformInformation.showCarousel" class="tw-order-1 tw-flex tw-min-w-0 tw-flex-auto tw-bg-cover tw-bg-center lg:tw-order-2 lg:tw-w-[75%]">
      <div class="block lg:tw-w-[100%]">
        <el-carousel arrow="never">
          <el-carousel-item v-for="item in swiper.list" :key="item.id" class="lg:tw-order-2">
            <img :src="item.url" />
          </el-carousel-item>
        </el-carousel>
      </div>
      <!-- <div class="md:tw-px-15 tw-flex tw-w-full tw-flex-col tw-items-center tw-justify-center tw-px-16 tw-py-7 lg:tw-py-16">
        <div class="tw-hidden tw-text-center tw-text-base tw-text-white lg:tw-block">
          In this kind of post, <a href="#" class="opacity-75-hover text-warning fw-bold me-1">the blogger</a>

          introduces a person they’ve interviewed <br />
          and provides some background information about

          <a href="#" class="opacity-75-hover text-warning fw-bold me-1">the interviewee</a>
          and their <br />
          work following this is a transcript of the interview.
        </div>
      </div> -->
    </div>
    <div class="tw-order-2 tw-flex tw-h-full tw-min-w-0 tw-flex-col tw-p-10 lg:tw-order-1 lg:tw-w-[50%] lg:tw-flex-auto" id="login-message">
      <div class="tw-flex tw-flex-auto tw-flex-col tw-items-center tw-justify-center">
        <el-scrollbar height="calc(100vh - 5rem - 21px)" :view-style="{ minHeight: 'calc(100vh - 5rem - 21px)', display: 'flex', flexDirection: 'column', justifyContent: 'center' }">
          <div class="tw-w-[500px] tw-max-w-[100%] tw-p-10 lg:tw-w-[500px]">
            <el-form v-focus :model="form" ref="formRef" size="large" label-position="top" @submit.prevent>
              <div v-if="loading">
                <el-empty description="Loading...">
                  <template #image>
                    <div class="tw-p-[24px]">
                      <div class="loading-chase">
                        <div v-for="i in 6" :key="`loading-chase-dot_${i}`" class="loading-chase-dot"></div>
                      </div>
                    </div>
                  </template>
                </el-empty>
              </div>
              <!--  -->
              <!--  -->
              <!--  -->
              <div v-else-if="type === operateType.LOGIN" class="tw-mb-11 tw-text-center" @keyup.enter.stop="loginSubmit(active)">
                <h1 class="tw-mb-3 tw-text-2xl tw-font-black">{{ $t("login.Log In") }}</h1>
                <p class="tw-text-[var(--el-text-color-placeholder)]">{{ $t("login.Login method") }}</p>
                <div class="tw-mt-4">
                  <el-button v-if="methods.includes(loginChannels.PASSWORD)" :disabled="loading" :type="active === loginChannels.PASSWORD ? 'primary' : 'default'" circle plain :icon="Lock" @click="() => (active = loginChannels.PASSWORD)"></el-button>
                  <el-button
                    v-if="methods.includes(loginChannels.PASSWORD)"
                    :disabled="loading"
                    :type="active === (loginChannels.EMAIL as loginChannels) ? 'primary' : 'default'"
                    circle
                    plain
                    :icon="Message"
                    @click="
                      () => {
                        active = loginChannels.EMAIL;
                        formRef?.resetFields();
                      }
                    "
                  ></el-button>
                  <!-- <el-button v-if="methods.includes(loginChannels.REFRESH_TOKEN)" :disabled="loading" :type="active === loginChannels.REFRESH_TOKEN ? 'primary' : 'default'" circle plain :icon="Loading" @click="active = loginChannels.REFRESH_TOKEN"></el-button> -->
                  <el-button v-if="methods.includes(loginChannels.SMS_CODE)" :disabled="loading" :type="active === loginChannels.SMS_CODE ? 'primary' : 'default'" circle plain :icon="Iphone" @click="() => (active = loginChannels.SMS_CODE)"></el-button>
                  <el-button v-if="methods.includes(loginChannels.GIT_HUB)" :disabled="loading" :type="active === loginChannels.GIT_HUB ? 'primary' : 'default'" circle plain :icon="h(FontAwesomeIcon, { icon: faGithub })" @click="() => loginSubmit(loginChannels.GIT_HUB)"></el-button>
                  <el-button v-if="methods.includes(loginChannels.WECHAT)" :disabled="loading" :type="active === loginChannels.WECHAT ? 'primary' : 'default'" circle plain :icon="h(FontAwesomeIcon, { icon: faWeixin })" @click="() => loginSubmit(loginChannels.WECHAT)"></el-button>
                </div>
                <div style="padding: 10px 0; box-sizing: border-box">
                  {{
                    {
                      PASSWORD: $t("login.Login by password"),
                      SMS_CODE: $t("login.Login by phone number"),
                    }[loginMethods?.value]
                  }}
                  <!-- 通过{{ loginMethods?.label }}登录 -->
                </div>
                <template v-if="!active"></template>
                <template v-else-if="active === loginChannels.PASSWORD">
                  <el-form-item prop="username" :rules="[{ required: true, message: $t('login.Please enter your', { value: $t('login.email or username') }) }]">
                    <el-input v-model="form.username" :prefix-icon="User" type="text" clearable :placeholder="$t('login.Please enter your', { value: $t('login.email or username') })"></el-input>
                  </el-form-item>

                  <el-form-item prop="password" :rules="[{ required: true, message: $t('login.Please enter your', { value: $t('login.password') }) }]">
                    <el-input v-model="form.password" :prefix-icon="Lock" type="password" :placeholder="$t('login.Please enter your', { value: $t('login.password') })" show-password></el-input>
                  </el-form-item>

                  <!-- <el-form-item prop="captcha" :rules="[buildValidatorData({ name: 'required', title: '图形验证码' }), { type: 'string', min: 4, max: 8, message: '验证码需要在4-8位字符', trigger: 'blur' }]">
                    <el-input v-model="form.captcha" :prefix-icon="ChatDotSquare" type="text" :placeholder="$t('adminLogin.Please enter the verification code')" clearable autocomplete="off" :style="{ verticalAlign: 'top', width: 'calc(100% - 118px)', marginRight: '12px' }"></el-input>
                    <canvas ref="captchaImg" class="captcha-img tw-flex-shrink-0" height="40" width="100" title="看不清，换一张" @click="onChangeCaptcha()"></canvas>
                  </el-form-item> -->

                  <!-- <el-form-item prop="lengthen">
                    <el-checkbox v-model="form.lengthen" :label="$t('adminLogin.Hold session')"></el-checkbox>
                  </el-form-item> -->

                  <!-- <el-form-item prop="captcha" :rules="[{ required: true, message: $t('glob.Please input field', { field: '验证码' }) }]">
                    <el-input v-model="form.captcha" :prefix-icon="ChatDotSquare" type="text" :placeholder="$t('adminLogin.Please enter the verification code')" clearable autocomplete="off" :style="{ verticalAlign: 'top', width: 'calc(100% - 118px)', marginRight: '12px' }"></el-input>
                    <canvas ref="captchaImg" class="captcha-img tw-flex-shrink-0" height="40" width="100" title="看不清，换一张" @click="onChangeCaptcha()"></canvas>
                  </el-form-item> -->
                </template>

                <template v-else-if="active === loginChannels.EMAIL">
                  <el-form-item prop="username" :rules="[{ required: true, message: $t('glob.Please input field', { field: $t('adminLogin.email') }) }]">
                    <el-input v-model="form.username" :prefix-icon="Message" type="text" clearable :placeholder="$t('adminLogin.Please enter an Email')"></el-input>
                  </el-form-item>

                  <el-form-item prop="password" :rules="[{ required: true, message: $t('glob.Please input field', { field: $t('adminLogin.password') }) }]">
                    <el-input v-model="form.password" :prefix-icon="Lock" type="password" :placeholder="$t('adminLogin.Please input a password')" show-password></el-input>
                  </el-form-item>

                  <!-- <el-form-item prop="captcha" :rules="[buildValidatorData({ name: 'required', title: '图形验证码' }), { type: 'string', min: 4, max: 8, message: '验证码需要在4-8位字符', trigger: 'blur' }]">
                    <el-input v-model="form.captcha" :prefix-icon="ChatDotSquare" type="text" :placeholder="$t('adminLogin.Please enter the verification code')" clearable autocomplete="off" :style="{ verticalAlign: 'top', width: 'calc(100% - 118px)', marginRight: '12px' }"></el-input>
                    <canvas ref="captchaImg" class="captcha-img tw-flex-shrink-0" height="40" width="100" title="看不清，换一张" @click="onChangeCaptcha()"></canvas>
                  </el-form-item> -->

                  <!-- <el-form-item prop="lengthen">
                    <el-checkbox v-model="form.lengthen" :label="$t('adminLogin.Hold session')"></el-checkbox>
                  </el-form-item> -->

                  <!-- <el-form-item prop="captcha" :rules="[{ required: true, message: $t('glob.Please input field', { field: '验证码' }) }]">
                    <el-input v-model="form.captcha" :prefix-icon="ChatDotSquare" type="text" :placeholder="$t('adminLogin.Please enter the verification code')" clearable autocomplete="off" :style="{ verticalAlign: 'top', width: 'calc(100% - 118px)', marginRight: '12px' }"></el-input>
                    <canvas ref="captchaImg" class="captcha-img tw-flex-shrink-0" height="40" width="100" title="看不清，换一张" @click="onChangeCaptcha()"></canvas>
                  </el-form-item> -->
                </template>

                <template v-else-if="active === loginChannels.REFRESH_TOKEN"></template>
                <template v-else-if="active === loginChannels.SMS_CODE">
                  <el-form-item prop="phone" :rules="[buildValidatorData({ name: 'required', title: $t('login.The phone number') }), buildValidatorData({ name: 'mobile', title: $t('login.The phone number') })]">
                    <el-input v-model="form.phone" :prefix-icon="Phone" type="text" clearable :placeholder="$t('adminLogin.Please enter an Phone')">
                      <!--  -->
                    </el-input>
                  </el-form-item>
                  <el-form-item prop="code" :rules="[buildValidatorData({ name: 'required', title: $t('login.SMS verification code') }), { type: 'string', min: 4, max: 8, message: $t('login.verification3'), trigger: 'blur' }]">
                    <el-input v-model="form.code" :prefix-icon="ChatDotSquare" type="text" :placeholder="$t('login.SMS verification code')">
                      <template #suffix>
                        <el-button link :loading="loadingCaptcha" :disabled="coolingCaptcha !== 0 || !form.phone" class="submit-button" type="primary" size="small" @click="sendCaptcha()">{{ $t("adminLogin.Send verification code") }}{{ coolingCaptcha ? `(${coolingCaptcha}${$t("login.s")})` : "" }}</el-button>
                      </template>
                    </el-input>
                  </el-form-item>
                  <!-- <el-form-item prop="lengthen">
                    <el-checkbox v-model="form.lengthen" :label="$t('adminLogin.Hold session')"></el-checkbox>
                  </el-form-item> -->
                </template>
                <!-- <template v-else-if="active === loginChannels.GIT_HUB">
                  <div class="tw-p-[24px]">
                    <div class="loading-chase">
                      <div v-for="i in 6" :key="`loading-chase-dot_${i}`" class="loading-chase-dot"></div>
                    </div>
                  </div>
                </template>
                <template v-else-if="active === loginChannels.WECHAT">
                  <div class="tw-p-[24px]">
                    <div class="loading-chase">
                      <div v-for="i in 6" :key="`loading-chase-dot_${i}`" class="loading-chase-dot"></div>
                    </div>
                  </div>
                </template> -->
                <!-- <el-button class="tw-w-full" :loading="loading" type="primary" @click="loginSubmit(active)">{{ loginMethods?.label }}{{ $t("adminLogin.Sign in") }}</el-button> -->
                <el-button class="tw-w-full" :loading="loading" type="primary" @click="loginSubmit(active)">{{ $t("login.Log In password") }}</el-button>
                <div class="tw-mt-4 tw-flex tw-items-center tw-justify-center">
                  <p class="term">
                    <el-checkbox v-model="serviceTerm"></el-checkbox>
                    {{ $t("login.Accept the above") }}
                    <b style="cursor: pointer" @click="dialogVisible = true">{{ $t("login.terms") }}</b>
                  </p>
                  <el-divider direction="vertical"></el-divider>
                  <el-link :disabled="loading" @click="() => changeType(operateType.RETRIEVE)">{{ $t("login.Forgot password") }}</el-link>
                  <!-- <el-divider direction="vertical"></el-divider>
                  <el-link class="userManage" @click="dialogVisibleManage = true">{{ $t("login.Contact the Administrator") }} </el-link> -->

                  <!-- <el-link class="userManage" >用户须知 </el-link> -->

                  <!-- <span class="userManage">123</span> -->
                  <!-- <el-link :disabled="loading" @click="() => changeType(operateType.SIGNIN)">{{ $t("user.user.Register your user") }}</el-link> -->
                </div>
              </div>
              <!--  -->
              <!--  -->
              <!--  -->
              <div v-else-if="type === operateType.SIGNIN" class="tw-mb-11 tw-text-center" @keyup.enter.stop="signinSubmit()">
                <div class="tw-text-left">
                  <el-link type="primary" :icon="Back" :underline="false" :disabled="loading" @click="() => changeType(operateType.LOGIN)"><span class="tw-ml-2">返回登录</span></el-link>
                </div>
                <h1 class="tw-mb-3 tw-text-2xl tw-font-black">{{ $t("user.user.register") }}</h1>
                <p class="tw-text-[var(--el-text-color-placeholder)]">填写注册信息</p>
                <div class="tw-mt-4"></div>
                <div style="padding: 10px 0; box-sizing: border-box">注册信息</div>
                <el-row :gutter="16">
                  <!-- <el-col :span="24">
                    <el-form-item prop="nickname" :rules="[]">
                      <el-input v-model="form.nickname" :prefix-icon="Avatar" autocomplete="nickname" placeholder="请输入昵称" clearable></el-input>
                    </el-form-item>
                  </el-col> -->
                  <el-col :span="12">
                    <el-form-item prop="name" :rules="[buildValidatorData({ name: 'required', title: '姓名' })]">
                      <el-input v-model="form.name" :prefix-icon="User" autocomplete="given-name" placeholder="请输入姓名" clearable></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="account" :rules="[buildValidatorData({ name: 'required', title: '账号' })]">
                      <el-input v-model="form.account" :prefix-icon="Postcard" autocomplete="username" :placeholder="$t('adminLogin.Please enter an account')" clearable></el-input>
                    </el-form-item>
                  </el-col>
                  <template v-if="siteConfig.loginChannels.includes(loginChannels.SMS_CODE)">
                    <el-col :span="16">
                      <el-form-item prop="phone" :rules="[buildValidatorData({ name: 'required', title: '手机号' }), buildValidatorData({ name: 'mobile', title: '手机号' })]">
                        <el-input v-model="form.phone" :prefix-icon="Phone" type="text" autocomplete="tel" :placeholder="$t('adminLogin.Please enter an Phone')">
                          <template #suffix>
                            <el-button link :loading="loadingCaptcha" :disabled="coolingCaptcha !== 0 || !form.phone" class="submit-button" type="primary" size="small" @click="sendCaptcha()">{{ $t("adminLogin.Send verification code") }}{{ coolingCaptcha ? `(${coolingCaptcha}${$t("login.s")})` : "" }}</el-button>
                          </template>
                        </el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item prop="code" :rules="[buildValidatorData({ name: 'required', title: '短信验证码' }), { type: 'string', min: 4, max: 8, message: $t('login.verification3'), trigger: 'blur' }]">
                        <el-input v-model="form.code" :prefix-icon="ChatDotSquare" type="text" autocomplete="one-time-code" placeholder="短信验证码"></el-input>
                      </el-form-item>
                    </el-col>
                  </template>
                  <el-col :span="24">
                    <el-form-item prop="email" :rules="[buildValidatorData({ name: 'required', title: $t('adminLogin.email') }), buildValidatorData({ name: 'email', title: $t('adminLogin.email') })]">
                      <el-input v-model="form.email" :prefix-icon="Message" autocomplete="email" :placeholder="$t('adminLogin.Please enter an Email')" clearable></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="13">
                    <el-form-item prop="gender" :rules="[]">
                      <el-radio-group v-model="form.gender" autocomplete="sex">
                        <el-radio v-for="item in genderOption" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="11">
                    <el-form-item prop="birthday" :rules="[]">
                      <el-date-picker v-model="form.birthday" type="date" autocomplete="bday" placeholder="选择日期" value-format="YYYY-MM-DD"></el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item prop="password" :rules="[buildValidatorData({ name: 'required', title: '密码' }), buildValidatorData({ name: 'password', title: '密码' })]">
                      <el-input v-model="form.password" :prefix-icon="Lock" type="password" show-password autocomplete="new-password" placeholder="密码" clearable></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item prop="rePassword" :rules="[buildValidatorData({ name: 'required', title: '确认密码' }), { validator: (_rule, value, callback) => callback(value === form.password ? undefined : '两次密码需要一致'), trigger: 'blur' }]">
                      <el-input v-model="form.rePassword" :prefix-icon="Lock" type="password" show-password autocomplete="new-password" placeholder="确认密码" clearable></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-button class="tw-mt-4 tw-w-full" :loading="loading" type="primary" @click="signinSubmit()">{{ $t("user.user.register") }}</el-button>
              </div>
              <!--  -->
              <!--  -->
              <!--  -->
              <div v-else-if="type === operateType.VERIFY" class="tw-mb-11 tw-text-center" @keyup.enter.stop="verifySubmit()">
                <div class="tw-text-left">
                  <el-link type="primary" :icon="Back" :underline="false" :disabled="loading" @click="() => changeType(operateType.LOGIN)"
                    ><span class="tw-ml-2">{{ $t("glob.back") }}</span></el-link
                  >
                </div>
                <h1 class="tw-mb-3 tw-text-2xl tw-font-black">{{ $t("pagesTitle.notVerify") }}</h1>
                <p class="tw-text-[var(--el-text-color-placeholder)]">为了确保账户是您本人操作，请任意选择一种安全验证方式验证身份</p>
                <div class="tw-mt-4">
                  <el-button v-if="mfaMethods.includes(MFAMethod.PASSWORD)" :disabled="loading" :type="mfaActive === MFAMethod.PASSWORD ? 'primary' : 'default'" circle plain :icon="Lock" @click="() => ((mfaActive = MFAMethod.PASSWORD), (form.code = ''))"></el-button>
                  <el-button v-if="mfaMethods.includes(MFAMethod.SMS)" :disabled="loading" :type="mfaActive === MFAMethod.SMS ? 'primary' : 'default'" circle plain :icon="Iphone" @click="() => ((mfaActive = MFAMethod.SMS), (form.code = ''))"></el-button>
                  <el-button v-if="mfaMethods.includes(MFAMethod.EMAIL)" :disabled="loading" :type="mfaActive === MFAMethod.EMAIL ? 'primary' : 'default'" circle plain :icon="Message" @click="() => ((mfaActive = MFAMethod.EMAIL), (form.code = ''))"></el-button>
                  <el-button v-if="mfaMethods.includes(MFAMethod.TOTP)" :disabled="loading" :type="mfaActive === MFAMethod.TOTP ? 'primary' : 'default'" circle plain :icon="Timer" @click="() => ((mfaActive = MFAMethod.TOTP), (form.code = ''))"></el-button>
                </div>
                <div style="padding: 10px 0; box-sizing: border-box">通过{{ mfaLoginMethods?.label }}验证</div>
                <template v-if="!mfaActive"></template>
                <template v-else-if="mfaActive === MFAMethod.PASSWORD">
                  <el-form-item prop="code" :rules="[{ required: true, message: $t('glob.Please input field', { field: $t('login.password') }) }]">
                    <el-input v-model="form.code" :prefix-icon="Lock" type="password" :placeholder="$t('adminLogin.Please input a password')" show-password></el-input>
                  </el-form-item>
                </template>
                <template v-else-if="mfaActive === MFAMethod.SMS">
                  <el-form-item prop="code" :rules="[{ required: true, message: $t('glob.Please input field', { field: $t('login.SMS verification code') }) }]">
                    <el-input v-model="form.code" :prefix-icon="Lock" type="text" :placeholder="$t('adminLogin.Please enter the verification code')">
                      <template #suffix>
                        <el-button link :loading="loadingCaptcha" :disabled="coolingCaptcha !== 0" class="submit-button" type="primary" size="small" @click="sendMFACaptcha()">{{ $t("adminLogin.Send verification code") }}{{ coolingCaptcha ? `(${coolingCaptcha}${$t("login.s")})` : "" }}</el-button>
                      </template>
                    </el-input>
                  </el-form-item>
                </template>
                <template v-else-if="mfaActive === MFAMethod.EMAIL">
                  <el-form-item prop="code" :rules="[{ required: true, message: $t('glob.Please input field', { field: $t('login.Email verification code') }) }]">
                    <el-input v-model="form.code" :prefix-icon="Lock" type="text" :placeholder="$t('adminLogin.Please enter the verification code')">
                      <template #suffix>
                        <el-button link :loading="loadingCaptcha" :disabled="coolingCaptcha !== 0" class="submit-button" type="primary" size="small" @click="sendMFACaptcha()">{{ $t("adminLogin.Send verification code") }}{{ coolingCaptcha ? `(${coolingCaptcha}${$t("login.s")})` : "" }}</el-button>
                      </template>
                    </el-input>
                  </el-form-item>
                </template>
                <template v-else-if="mfaActive === MFAMethod.TOTP">
                  <el-form-item prop="code" :rules="[{ required: true, message: $t('glob.Please input field', { field: 'MFA动态码' }) }]">
                    <el-input v-model="form.code" :prefix-icon="Lock" type="text" placeholder="请输入MFA动态码"></el-input>
                  </el-form-item>
                </template>
                <el-button class="tw-mt-4 tw-w-full" :loading="loading" type="primary" @click="verifySubmit()">{{ mfaLoginMethods?.label }}{{ $t("adminLogin.Verify") }}</el-button>
              </div>
              <!--  -->
              <!--  -->
              <!--  -->
              <div v-else-if="type === operateType.RETRIEVE" class="tw-mb-11 tw-text-center" @keyup.enter.stop="resetSubmit()">
                <div class="tw-text-left">
                  <el-link type="primary" :icon="Back" :underline="false" :disabled="loading" @click="() => changeType(operateType.LOGIN)"
                    ><span class="tw-ml-2">{{ $t("glob.back") }}</span></el-link
                  >
                </div>
                <h1 class="tw-mb-3 tw-text-2xl tw-font-black">{{ $t("glob.Reset") }}</h1>
                <template v-if="!accountList.length">
                  <p class="tw-text-[var(--el-text-color-placeholder)]">{{ $t("login.Retrieve password verification method") }}</p>
                  <div class="tw-mt-4">
                    <el-button v-if="mfaMethods.includes(MFAMethod.SMS)" :disabled="loading" :type="mfaActive === MFAMethod.SMS ? 'primary' : 'default'" circle plain :icon="Iphone" @click="(mfaActive = MFAMethod.SMS), (form.code = '')"></el-button>
                    <el-button v-if="mfaMethods.includes(MFAMethod.EMAIL)" :disabled="loading" :type="mfaActive === MFAMethod.EMAIL ? 'primary' : 'default'" circle plain :icon="Message" @click="(mfaActive = MFAMethod.EMAIL), (form.code = '')"></el-button>
                  </div>
                </template>
                <div style="padding: 10px 0; box-sizing: border-box" v-if="accountList.length">此身份验证中包含多个用户，请选择其中一个用户进行找回</div>
                <div style="padding: 10px 0; box-sizing: border-box" v-else>
                  <!-- {{ mfaLoginMethods?.value }}通过{{ mfaLoginMethods?.label }}找回密码 -->
                  {{
                    {
                      SMS: $t("login.Retrieve password through", { value: $t("login.SMS") }),
                      EMAIL: $t("login.Retrieve password through", { value: $t("login.email") }),
                    }[mfaLoginMethods?.value] || undefined
                  }}
                </div>
                <template v-if="accountList.length">
                  <el-form-item prop="uid" :rules="[buildValidatorData({ name: 'required', title: '用户' })]">
                    <div class="tw-w-full">
                      <div class="tw-w-[calc(100%_-_14px)] tw-pl-[24px]" :style="{ borderBottom: 'var(--el-border)' }">
                        <el-row :gutter="16">
                          <el-col :span="12" class="tw-text-left tw-text-[var(--el-text-color-secondary)]">用户</el-col>
                          <el-col :span="6" class="tw-text-center tw-text-[var(--el-text-color-secondary)]">注册时间</el-col>
                          <el-col :span="6" class="tw-text-center tw-text-[var(--el-text-color-secondary)]">上次登录时间</el-col>
                        </el-row>
                      </div>
                      <el-radio-group v-model="form.uid" class="tw-w-full">
                        <el-row :gutter="16">
                          <el-col :span="24" v-for="item in accountList" :key="item.uid">
                            <el-radio :label="item.uid" class="line-radio tw-h-fit tw-w-full">
                              <el-row :gutter="12" class="tw-w-full">
                                <el-col :span="12">
                                  <div class="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-py-[6px] tw-text-left tw-text-[14px]">
                                    <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.account">
                                      <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.account || "--" }}</span>
                                    </div>
                                    <!-- <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.phone">
                                    <span class="tw-inline-block tw-w-[7em] tw-align-middle">手机号:</span>
                                    <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.phone || "--" }}</span>
                                  </div> -->
                                    <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.email">
                                      <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.email || "--" }}</span>
                                    </div>
                                  </div>
                                </el-col>
                                <el-col :span="6">
                                  <div class="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-py-[6px] tw-text-center tw-text-[14px]">
                                    <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.registrationTime">
                                      <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.registrationTime || "--" }}</span>
                                    </div>
                                  </div>
                                </el-col>
                                <el-col :span="6">
                                  <div class="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-py-[6px] tw-text-center tw-text-[14px]">
                                    <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.lastLoginTime">
                                      <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.lastLoginTime || "--" }}</span>
                                    </div>
                                  </div>
                                </el-col>
                              </el-row>
                            </el-radio>
                          </el-col>
                        </el-row>
                      </el-radio-group>
                    </div>
                  </el-form-item>
                  <el-button class="tw-mt-4 tw-w-full" :loading="loading" type="primary" @click="resetSubmit()">{{ $t("glob.Retrieve") }}密码</el-button>
                </template>
                <template v-else>
                  <el-row :gutter="16">
                    <template v-if="!mfaActive"></template>
                    <template v-else-if="mfaActive === MFAMethod.SMS">
                      <el-col :span="16">
                        <el-form-item prop="phone" :rules="[buildValidatorData({ name: 'required', title: $t('login.The phone number') }), buildValidatorData({ name: 'mobile', title: $t('login.The phone number') })]">
                          <el-input v-model="form.phone" :prefix-icon="Phone" type="text" autocomplete="tel" clearable :placeholder="$t('adminLogin.Please enter an Phone')">
                            <template #suffix>
                              <el-button link :loading="loadingCaptcha" :disabled="coolingCaptcha !== 0 || !form.phone" class="submit-button" type="primary" size="small" @click="sendCaptcha()">{{ $t("adminLogin.Send verification code") }}{{ coolingCaptcha ? `(${coolingCaptcha}${$t("login.s")})` : "" }}</el-button>
                            </template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item prop="code" :rules="[buildValidatorData({ name: 'required', title: $t('login.SMS verification code') }), { type: 'string', min: 4, max: 8, message: $t('login.verification3'), trigger: 'blur' }]">
                          <el-input v-model="form.code" :prefix-icon="ChatDotSquare" type="text" autocomplete="one-time-code" :placeholder="$t('login.SMS verification code')"></el-input>
                        </el-form-item>
                      </el-col>
                    </template>
                    <template v-else-if="mfaActive === MFAMethod.EMAIL">
                      <el-col :span="16">
                        <el-form-item prop="email" :rules="[buildValidatorData({ name: 'required', title: $t('login.Email') }), buildValidatorData({ name: 'email', title: $t('login.Email') })]">
                          <el-input v-model="form.email" :prefix-icon="Message" autocomplete="email" :placeholder="$t('adminLogin.Please enter an Email')" clearable>
                            <template #suffix>
                              <el-button link :loading="loadingCaptcha" :disabled="coolingCaptcha !== 0 || !form.email" class="submit-button" type="primary" size="small" @click="sendCaptcha()">{{ $t("adminLogin.Send verification code") }}{{ coolingCaptcha ? `(${coolingCaptcha}${$t("login.s")})` : "" }}</el-button>
                            </template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item prop="code" :rules="[buildValidatorData({ name: 'required', title: $t('login.Email verification code') }), { type: 'string', min: 4, max: 8, message: $t('login.verification3'), trigger: 'blur' }]">
                          <el-input v-model="form.code" :prefix-icon="ChatDotSquare" type="text" autocomplete="one-time-code" :placeholder="$t('login.Email verification code')"></el-input>
                        </el-form-item>
                      </el-col>
                    </template>
                  </el-row>
                  <el-form-item prop="password" :rules="[buildValidatorData({ name: 'required', title: $t('login.password') }), buildValidatorData({ name: 'password', title: $t('login.password') })]">
                    <el-input v-model="form.password" :prefix-icon="Lock" type="password" show-password autocomplete="new-password" :placeholder="$t('login.password')" clearable></el-input>
                  </el-form-item>
                  <el-form-item prop="rePassword" style="margin-top: 30px" :rules="[buildValidatorData({ name: 'required', title: $t('login.Confirm password') }), { validator: (_rule, value, callback) => callback(value === form.password ? undefined : $t('login.Two passwords need to be consistent')), trigger: 'blur' }]">
                    <el-input v-model="form.rePassword" :prefix-icon="Lock" type="password" show-password autocomplete="new-password" :placeholder="$t('login.Confirm password')" clearable></el-input>
                  </el-form-item>
                  <el-button class="tw-mt-4 tw-w-full" :loading="loading" type="primary" @click="resetSubmit()">
                    <!-- 使用{{ mfaLoginMethods?.label }}{{ $t("glob.Retrieve") }}密码 -->
                    {{ $t("login.use model Retrieve password", { model: { SMS: $t("login.SMS"), EMAIL: $t("login.email") }[mfaLoginMethods?.value] }) }}
                  </el-button>
                </template>
              </div>
              <!--  -->
              <!--  -->
              <!--  -->
              <div v-else-if="type === operateType.PASSWORD_EXPIRED" class="tw-mb-11 tw-text-center" @keyup.enter.stop="expiredSubmit()">
                <div class="tw-text-left">
                  <el-link type="primary" :icon="Back" :underline="false" :disabled="loading" @click="() => changeType(operateType.LOGIN)"
                    ><span class="tw-ml-2">{{ $t("glob.back") }}</span></el-link
                  >
                </div>
                <h1 class="tw-mb-3 tw-text-2xl tw-font-black">{{ $t("glob.Reset") }}</h1>
                <p class="tw-text-[var(--el-text-color-placeholder)]">{{ $t("login.reset your password") }}</p>
                <div class="tw-mt-4">
                  <el-button :disabled="loading" type="primary" circle plain :icon="Lock" @click="form.code = ''"></el-button>
                </div>
                <el-divider>{{ $t("login.Reset/Initial Password") }}</el-divider>
                <el-form-item prop="password" :rules="[buildValidatorData({ name: 'required', title: $t('login.Confirm password') }), buildValidatorData({ name: 'password', title: $t('login.password'), min: userInfo.passwordLength })]">
                  <el-input v-model="form.password" :prefix-icon="Lock" type="password" show-password autocomplete="new-password" :placeholder="$t('login.password')" clearable></el-input>
                </el-form-item>
                <el-form-item style="margin-top: 30px" prop="rePassword" :rules="[buildValidatorData({ name: 'required', title: $t('login.Confirm password') }), { validator: (_rule, value, callback) => callback(value === form.password ? undefined : '两次密码需要一致'), trigger: 'blur' }]">
                  <el-input v-model="form.rePassword" :prefix-icon="Lock" type="password" show-password autocomplete="new-password" :placeholder="$t('login.Confirm password')" clearable></el-input>
                </el-form-item>
                <el-button class="tw-mt-4 tw-w-full" :loading="loading" type="primary" @click="expiredSubmit()">{{ $t("login.Reset/Initial Password") }}</el-button>
              </div>
              <!--  -->
              <!--  -->
              <!--  -->
              <div v-else-if="type === operateType.SELECT_ACCOUNT" class="tw-mb-11 tw-text-center" @keyup.enter.stop="accountSubmit()">
                <div class="tw-text-left">
                  <el-link type="primary" :icon="Back" :underline="false" :disabled="loading" @click="() => changeType(operateType.LOGIN)"><span class="tw-ml-2">返回登录</span></el-link>
                </div>
                <h1 class="tw-mb-3 tw-text-2xl tw-font-black">{{ $t("glob.Please select field", { field: "用户" }) }}</h1>
                <p class="tw-text-[var(--el-text-color-placeholder)]">此身份验证中包含多个用户，请选择其中一个用户进行登录</p>
                <div class="tw-mt-4">
                  <el-button :disabled="loading" type="primary" circle plain :icon="Lock" @click="form.code = ''"></el-button>
                </div>
                <el-divider>选择用户</el-divider>
                <el-form-item prop="uid" :rules="[buildValidatorData({ name: 'required', title: '用户' })]">
                  <div class="tw-w-full">
                    <div class="tw-w-[calc(100%_-_14px)] tw-pl-[24px]" :style="{ borderBottom: 'var(--el-border)' }">
                      <el-row :gutter="16">
                        <el-col :span="12" class="tw-text-left tw-text-[var(--el-text-color-secondary)]">用户</el-col>
                        <el-col :span="6" class="tw-text-center tw-text-[var(--el-text-color-secondary)]">注册时间</el-col>
                        <el-col :span="6" class="tw-text-center tw-text-[var(--el-text-color-secondary)]">上次登录时间</el-col>
                      </el-row>
                    </div>
                    <el-radio-group v-model="form.uid" class="tw-w-full">
                      <el-row :gutter="16">
                        <el-col :span="24" v-for="item in accountList" :key="item.uid">
                          <el-radio :label="item.uid" class="line-radio tw-h-fit tw-w-full">
                            <el-row :gutter="12" class="tw-w-full">
                              <el-col :span="12">
                                <div class="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-py-[6px] tw-text-left tw-text-[14px]">
                                  <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.account">
                                    <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.account || "--" }}</span>
                                  </div>
                                  <!-- <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.phone">
                                    <span class="tw-inline-block tw-w-[7em] tw-align-middle">手机号:</span>
                                    <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.phone || "--" }}</span>
                                  </div> -->
                                  <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.email">
                                    <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.email || "--" }}</span>
                                  </div>
                                </div>
                              </el-col>
                              <el-col :span="6">
                                <div class="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-py-[6px] tw-text-center tw-text-[14px]">
                                  <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.registrationTime">
                                    <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.registrationTime || "--" }}</span>
                                  </div>
                                </div>
                              </el-col>
                              <el-col :span="6">
                                <div class="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-py-[6px] tw-text-center tw-text-[14px]">
                                  <div class="tw-h-[24px] tw-w-full tw-leading-[24px]" :title="item.lastLoginTime">
                                    <span class="tw-inline-block tw-w-[calc(100%_-_1em)] tw-overflow-hidden tw-overflow-ellipsis tw-align-middle">{{ item.lastLoginTime || "--" }}</span>
                                  </div>
                                </div>
                              </el-col>
                            </el-row>
                          </el-radio>
                        </el-col>
                      </el-row>
                    </el-radio-group>
                  </div>
                </el-form-item>
                <el-button class="tw-mt-4 tw-w-full" :loading="loading" type="primary" @click="accountSubmit()">{{ $t("adminLogin.Sign in") }}</el-button>
              </div>
            </el-form>
          </div>
        </el-scrollbar>
      </div>
      <div class="tw-mx-auto tw-flex tw-w-[500px] tw-max-w-[100%] tw-items-center tw-justify-center tw-px-10 lg:tw-w-[500px]">
        <div class="tw-flex tw-gap-5 tw-text-base tw-font-medium" v-html="siteConfig.footer" />
      </div>
    </div>
    <el-dialog v-model="dialogVisible" :title="$t('login.Terms & Conditions')" width="35%" :before-close="handleClose">
      <div>
        <p>{{ $t("login.constraint1") }}:</p>

        <p class="term-text">{{ $t("login.constraint2") }}</p>

        <p class="term-text">{{ $t("login.constraint3") }}</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="dialogVisible = false">{{ $t("glob.Confirm") }} </el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisibleManage" :title="$t('login.Contact the Administrator')" width="30%" :before-close="handleCloseManage">
      <div>
        <p>{{ $t("login.Administrator Hotline") }}: 400-8888-112</p>
        <p>{{ $t("login.Administrator Email") }}: <EMAIL></p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="dialogVisibleManage = false"> {{ $t("glob.Cancel") }} </el-button>
        </span>
      </template>
    </el-dialog>

    <BindMFAWizard ref="bindMFAWizardRef" :mfaMethods="[]" title="动态密钥" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, onMounted, computed, watch, provide, h } from "vue";
import { formRefKey, workResultKey } from "./model/common";
import { useRouter } from "vue-router";
import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";
import DarkSwitch from "@/layouts/common/components/darkSwitch.vue";
import { vBubble } from "@/utils/pageBubble";
import { editDefaultLang } from "@/lang/index";
import { bindCanvasImage } from "@/utils/image";
import { bufferToBase64, base64ToBuffer, stringToBuffer } from "@/utils/base64";
import { bindFormBox } from "@/utils/bindFormBox";
import moment from "@/lang/moment/zh-cn";
import { useI18n } from "vue-i18n";

import { useConfig } from "@/stores/config";
import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
import { useStorage } from "@vueuse/core";

// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { User, ChatDotSquare, Phone, Back, Loading, Lock, Iphone, Message, Timer, Postcard, Avatar } from "@element-plus/icons-vue";
import { ElMessage, ElForm, ElFormItem, ElInput, ElAlert } from "element-plus";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { faGithub, faWeixin } from "@fortawesome/free-brands-svg-icons";

import { requireCaptcha, captchaForImage, MFAMethod, MFAMethodOption, loginChannels, loginChannelsOption, loginResultType, loginResultTypeOption, type LoginData, getMFAMethods } from "@/api/system";
import { gender, genderOption } from "@/api/personnel";
import { SERVER, Method, type Response } from "@/api/service/common";
import request from "@/api/service/index";
// import CryptoJS from "crypto-js";
import { buildValidatorData } from "@/utils/validate";
// import backgroundImage from "@/assets/bg.png";
import swiper1 from "@/assets/login/swiper1.png";
import swiper2 from "@/assets/login/swiper2.png";
import swiper3 from "@/assets/login/swiper3.png";
import { getAppointUserPasswordStrategyMsg, getUser } from "@/api/personnel";

import BindMFAWizard from "@/views/pages/routine/bindMFAWizard.vue";

const vFocus: import("vue").Directive = {
  mounted(el) {
    const inputs = el.getElementsByTagName("input");
    nextTick(() => {
      for (let index = 0; index < inputs.length; index++) {
        if (inputs[index].value) continue;
        else return inputs[index].focus();
      }
    });
  },
  updated(el) {
    const inputs = el.getElementsByTagName("input");
    nextTick(() => {
      for (let index = 0; index < inputs.length; index++) {
        if (inputs[index].value) continue;
        else return inputs[index].focus();
      }
    });
  },
};

const loading = ref(false);

const formRef = ref<InstanceType<typeof ElForm>>();
const { t } = useI18n();
provide(formRefKey, formRef);
provide(workResultKey, workResult);
// const captchaImg = ref<HTMLCanvasElement>(document.createElement("canvas"));
const dialogVisible = ref(false);
const dialogVisibleManage = ref(false);
const serviceTerm = useStorage("privacy", false);
enum operateType {
  LOGIN = "LOGIN",
  SIGNIN = "SIGNIN",
  VERIFY = "VERIFY",
  RETRIEVE = "RETRIEVE",
  PASSWORD_EXPIRED = "PASSWORD_EXPIRED",
  SELECT_ACCOUNT = "SELECT_ACCOUNT",
}
const type = ref<keyof typeof operateType>(operateType.LOGIN);

const router = useRouter();
const siteConfig = useSiteConfig();
console.log(siteConfig, 620);

const config = useConfig();

const loadingCaptcha = ref(false);
const coolingCaptcha = ref(0);
watch(coolingCaptcha, (cooling) => {
  if (cooling !== 0) setTimeout(() => coolingCaptcha.value !== 0 && (coolingCaptcha.value = cooling - 1), 1000);
});

const methods = ref<(keyof typeof loginChannels)[]>(siteConfig.loginChannels);
const active = ref<keyof typeof loginChannels>(methods.value[0]);

const mfaMethods = ref<(keyof typeof MFAMethod)[]>(Object.keys(MFAMethod) as (keyof typeof MFAMethod)[]);
const mfaActive = ref<keyof typeof MFAMethod>(mfaMethods.value[0]);

const accountList = ref<Record<"uid" | "account" | "phone" | "email" | "registrationTime" | "lastLoginTime", string>[]>([]);

onMounted(() => {
  /*  */
});

watch(methods, () => (active.value = methods.value[0]));
watch(mfaMethods, () => (mfaActive.value = mfaMethods.value[0]));

const bindMFAWizardRef = ref<InstanceType<typeof BindMFAWizard>>();

const loginMethods = computed(() => loginChannelsOption.find(({ value }) => value === active.value));
const mfaLoginMethods = computed(() => MFAMethodOption.find(({ value }) => value === mfaActive.value));

// const showLeft = computed(() => ["unms.sst.net.cn"].includes(location.host));

const swiper = reactive({
  list: [
    {
      url: swiper1,
      id: 0,
    },
    {
      url: swiper2,
      id: 1,
    },
    {
      url: swiper3,
      id: 2,
    },
  ],
});

const form = reactive({
  uid: "",
  certificate: "",
  captcha: "",

  username: "",
  password: "",
  lengthen: false,

  name: "",
  nickname: "",
  account: "",
  email: "",
  gender: gender.SECRET,
  birthday: "",
  rePassword: "",

  phone: "",
  code: "",

  ticket: "",
});
const userInfo = ref({
  userId: "",
  passwordLength: 0,
});

function handleClose() {
  dialogVisible.value = false;
}

function handleCloseManage() {
  dialogVisibleManage.value = false;
}

async function workResult(result?: Promise<Response<LoginData>>) {
  if (!result) return;
  try {
    const { success, message, data } = await result;
    /* 强制校验绑定mfa */
    // if (["TOKEN"].includes(data.type)) {
    //   const { success: mfaSuccess, message: mfaMessage, data: mfaData } = await getMFAMethods({}, `${data.token.token_type} ${data.token.access_token}`);
    //   if (!mfaSuccess) throw new Error(mfaMessage);
    //   const isBindMfa = mfaData.includes(MFAMethod.TOTP) ? true : bindMFAWizardRef.value ? await bindMFAWizardRef.value.open(`${data.token.token_type} ${data.token.access_token}`) : false;
    //   if (!isBindMfa) return;
    // }
    /* --------------- */
    if (success) {
      const info = ((type) => {
        switch (type) {
          case `${superBaseRoute.name}Login`:
            return useSuperInfo();
          case `${adminBaseRoute.name}Login`:
            return useAdminInfo();
          case `${usersBaseRoute.name}Login`:
            return useUsersInfo();
          default:
            return useSuperInfo();
        }
      })(router.currentRoute.value.name);

      switch (data.type) {
        case loginResultType.TOKEN:
          changeType(type.value);
          if (!data.token) throw Object.assign(new Error(`未知Token`), { $message: message, success, data });
          info.dataFill({ token: `${data.token.token_type} ${data.token.access_token}`, refreshToken: form.lengthen ? data.token.refresh_token : "" });
          await router.push({ name: siteConfig.current, query: router.currentRoute.value.query, params: router.currentRoute.value.params });
          break;
        case loginResultType.NEED_MFA:
          changeType(operateType.VERIFY);
          if (!data.mfaTicket.ticket) throw Object.assign(new Error(`二次验证状态无效`), { $message: message, success, data });
          form.ticket = data.mfaTicket.ticket;
          mfaMethods.value = data.mfaTicket.methods instanceof Array ? data.mfaTicket.methods : [];
          break;
        case loginResultType.PASSWORD_EXPIRED:
          changeType(operateType.PASSWORD_EXPIRED);
          ElMessage.info(loginResultTypeOption.find(({ value }) => value === data.type)?.label);
          form.ticket = data.passwordTicket.ticket;
          // userInfo.value = data.passwordTicket.userId;
          getUserPasswordStrategy(data.passwordTicket.userId);
          form.password = "";
          form.rePassword = "";
          break;
        case loginResultType.SELECT_ACCOUNT:
          changeType(operateType.SELECT_ACCOUNT);
          if (!data.selectAccountTicket.ticket) throw Object.assign(new Error(`选择账号状态无效`), { $message: message, success, data });
          form.ticket = data.selectAccountTicket.ticket;
          accountList.value = (data.selectAccountTicket.accounts instanceof Array ? data.selectAccountTicket.accounts : []).map((v) => ({ ...v, registrationTime: moment(v.registrationTime, "x").format("YYYY.MM.DD"), lastLoginTime: moment(v.lastLoginTime, "x").format("YYYY.MM.DD") }));
          form.uid = "";
          break;
        case "PASSWORD_ILLEGAL":
          changeType(operateType.PASSWORD_EXPIRED);
          ElMessage.info(loginResultTypeOption.find(({ value }) => value === data.type)?.label);
          form.ticket = data.passwordTicket.ticket;
          // userInfo.value = data.passwordTicket.userId;
          getUserPasswordStrategy(data.passwordTicket.userId);
          form.password = "";
          form.rePassword = "";
          break;
        default:
          throw Object.assign(new Error(`未知类型-${data.type}`), { $message: message, success, data });
      }
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    active.value = loginChannels.PASSWORD;
    if (error instanceof Error) {
      const res: Record<string, unknown> = { ...error };
      if (res.data && res.data instanceof Object) {
        const duration = Number((res.data as { blockExpire: string }).blockExpire);
        const failureCount = Number((res.data as { failureCount: string }).failureCount);
        const failureLimit = Number((res.data as { failureLimit: string }).failureLimit);
        // const passwordTicket = res.data.passwordTicket;

        // if (!isNaN(duration)) return void ElMessage.error(`${error.message}`);

        if (!isNaN(failureCount)) return void ElMessage.error(`当前${error.message}${failureCount}次,剩余${failureLimit - failureCount}次机会`);

        if (duration && !isNaN(duration)) {
          const remainingMinutes = Math.floor((Number(duration) - new Date().getTime()) / (1000 * 60));
          return void ElMessage.error(`${error.message}，请${remainingMinutes}分钟后重试`);
        } else {
          return void ElMessage.error(`${error.message}`);
        }
      }
      ElMessage.error(error.message);
    }
  }
}
async function getUserPasswordStrategy(id) {
  await getAppointUserPasswordStrategyMsg({ userId: id }).then((res) => {
    if (res.success) {
      // console.log(res.data)
      userInfo.value.passwordLength = res.data.minLength;
    }
  });
}

async function loginSubmit($active: keyof typeof loginChannels) {
  if (!formRef.value) return;
  if (!loginMethods.value) return;
  if (loading.value) return;
  if (!serviceTerm.value) {
    return ElMessage.error("请同意用户隐私条款");
  }
  useStorage("privacy", true);

  let result: Promise<Response<LoginData>> | undefined = undefined;
  formRef.value.clearValidate();

  const info = ((type) => {
    switch (type) {
      case `${superBaseRoute.name}Login`:
        return useSuperInfo();
      case `${adminBaseRoute.name}Login`:
        return useAdminInfo();
      case `${usersBaseRoute.name}Login`:
        return useUsersInfo();
      default:
        return useSuperInfo();
    }
  })(router.currentRoute.value.name);

  active.value = $active;
  switch ($active) {
    case loginChannels.EMAIL as any:
    case loginChannels.PASSWORD: {
      if (await new Promise((resolve) => formRef.value?.validateField(["username", "password"], resolve))) {
        loading.value = true;
        try {
          const { success, message, data } = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/rsa/key/public`, method: Method.Get, responseType: "json", params: {}, data: new URLSearchParams() });
          if (!success) throw Object.assign(new Error(message), { success, data });
          if (typeof data !== "string") throw new Error("公钥获取失败！");
          const keyData = base64ToBuffer(data);
          let importPublicKey: CryptoKey;
          try {
            importPublicKey = await window.crypto.subtle.importKey("spki", keyData, { name: "RSA-OAEP", hash: { name: "SHA-512" } }, true, ["encrypt"]);
          } catch (error) {
            if (error instanceof Error) throw new Error(`公钥导入失败 ${error.message} \n${bufferToBase64(keyData) === data.replaceAll(/[^A-Za-z0-9+/=]/g, "") ? "" : bufferToBase64(keyData)}`);
            else throw new Error("公钥不合法");
          }
          if (!importPublicKey) throw new Error("没有找到公钥");
          let _password: ArrayBufferLike;
          try {
            _password = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.password));
          } catch (error) {
            throw new Error("密码加密失败");
          }
          if (!_password) throw new Error("密码读取错误，请确认密码是否符合要求，联系管理员排查问题");
          result = request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/login/password`, method: Method.Post, responseType: "json", params: await sendCaptcha(), data: { username: form.username, password: bufferToBase64(_password), lengthen: form.lengthen, ptype: "RSA" } });
        } catch (error) {
          if (error instanceof Error) ElMessage.error(error.message + 1);
        }
      }
      break;
    }
    case loginChannels.REFRESH_TOKEN: {
      loading.value = true;
      result = request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/authentication/sign_in/refresh_token`, method: Method.Post, responseType: "json", params: { refreshToken: info.getToken("refresh") }, data: { lengthen: form.lengthen } });
      break;
    }
    case loginChannels.SMS_CODE: {
      if (await new Promise((resolve) => formRef.value?.validateField(["phone", "code"], resolve))) {
        loading.value = true;
        result = request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/login/sms_code`, method: Method.Post, responseType: "json", params: {}, data: { phone: form.phone, code: form.code, lengthen: form.lengthen } });
      }
      break;
    }
    case loginChannels.GIT_HUB: {
      loading.value = true;
      const { success, message, data } = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/github/authorize_url/login`, method: Method.Get, responseType: "json", params: {}, data: {} });
      if (!success) throw Object.assign(new Error(message), { success, data });
      result = new Promise((resolve, reject) => {
        const windowProxy = window.open(data, "_blank", `location=no, menubar=no, status=no, titlebar=no, toolbar=no, top=0px, left=0px, width=${window.screen.availWidth * 0.45}px, height=${window.screen.availHeight * 0.45}px`);
        if (!windowProxy) return;
        const winLoop = setInterval(() => windowProxy.closed && (done(), reject(new Error("关闭了授权"))), 1000);
        window.addEventListener("message", binding);
        async function binding({ data }: { data: { idp: keyof typeof loginChannels; code: string } }) {
          if (data.idp === loginChannels.GIT_HUB) {
            done();
            try {
              const result = await request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/github/login/code`, method: Method.Post, responseType: "json", params: {}, data: { code: data.code, lengthen: form.lengthen } });
              resolve(result);
            } catch (error) {
              reject(error);
            }
          }
        }
        function done() {
          clearInterval(winLoop);
          window.removeEventListener("message", binding);
        }
      });
      break;
    }
    case loginChannels.WECHAT: {
      loading.value = true;
      break;
    }
  }

  if (result) await workResult(result);
  loading.value = false;
}
async function signinSubmit() {
  if (!formRef.value) return;
  if (loading.value) return;
  formRef.value.clearValidate();

  try {
    if (!(await new Promise((resolve) => formRef.value?.validateField(["code", "name", "nickname", "account", "phone", "email", "gender", "birthday", "password", "rePassword"], resolve)))) return;
    loading.value = true;
    const { success, message, data } = await request<unknown, Response<LoginData>>({
      url: `${SERVER.IAM}/authentication/sign_up`,
      method: Method.Post,
      responseType: "json",
      params: { smsCode: form.code },
      data: { name: form.name, nickname: form.nickname, account: form.account, phone: form.phone, email: form.email, gender: form.gender, birthday: form.birthday, password: form.password },
    });
    if (success) {
      ElMessage.success("用户成功注册，请重新登录！");
      form.username = form.account;
      form.password = form.rePassword;
      form.code = "";
      form.name = "";
      form.nickname = "";
      form.account = "";
      form.phone = "";
      form.email = "";
      form.gender = gender.SECRET;
      form.birthday = "";
      form.rePassword = "";
      changeType(operateType.LOGIN);
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}
async function verifySubmit() {
  if (!formRef.value) return;
  if (!mfaLoginMethods.value) return;
  if (loading.value) return;
  let result: Promise<Response<LoginData>> | undefined = undefined;
  formRef.value.clearValidate();

  if (await new Promise((resolve) => formRef.value?.validateField(["code"], resolve))) {
    loading.value = true;

    try {
      const { success, message, data } = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/rsa/key/public`, method: Method.Get, responseType: "json", params: {}, data: new URLSearchParams() });
      if (!success) throw Object.assign(new Error(message), { success, data });
      const importPublicKey = await window.crypto.subtle.importKey("spki", base64ToBuffer(data), { name: "RSA-OAEP", hash: { name: "SHA-512" } }, true, ["encrypt"]);
      const _ticket = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.ticket));
      const _code = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.code));

      result = request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/login/mfa`, method: Method.Post, responseType: "json", params: {}, data: { ticket: bufferToBase64(_ticket), method: mfaActive.value, code: bufferToBase64(_code), ptype: "RSA" } });
    } catch (error) {
      console.error(error);
      ElMessage.error("密码读取错误，请确认密码是否符合要求，联系管理员排查问题");
    }
  }

  if (result) await workResult(result);
  loading.value = false;
}
async function resetSubmit() {
  if (!formRef.value) return;
  if (!mfaLoginMethods.value) return;
  if (loading.value) return;
  let result: Promise<Response<{ type: "SUCCESS" | "SELECT_ACCOUNT"; accounts: Record<"uid" | "account" | "phone" | "email" | "registrationTime" | "lastLoginTime", string>[] }>> | undefined = undefined;
  formRef.value.clearValidate();

  switch (mfaActive.value) {
    case MFAMethod.PASSWORD:
      loading.value = true;
      break;
    case MFAMethod.SMS:
      if (!(await new Promise((resolve) => formRef.value?.validateField(accountList.value.length ? ["uid"] : ["code", "phone", "password", "rePassword"], resolve)))) return (loading.value = false), undefined;
      loading.value = true;
      try {
        const { success, message, data } = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/rsa/key/public`, method: Method.Get, responseType: "json", params: {}, data: new URLSearchParams() });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const importPublicKey = await window.crypto.subtle.importKey("spki", base64ToBuffer(data), { name: "RSA-OAEP", hash: { name: "SHA-512" } }, true, ["encrypt"]);
        const _password = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.password));
        const _rePassword = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.rePassword));

        result = request<unknown, Response<{ type: "SUCCESS" | "SELECT_ACCOUNT"; accounts: Record<"uid" | "account" | "phone" | "email" | "registrationTime" | "lastLoginTime", string>[] }>>({ url: `${SERVER.IAM}/retrieve_password/sms`, method: Method.Post, responseType: "json", params: {}, data: { ident: form.phone, code: form.code, password: bufferToBase64(_password), confirmationPassword: bufferToBase64(_rePassword), ...(accountList.value.length ? { userId: form.uid } : {}), ptype: "RSA" } });
      } catch (error) {
        console.error(error);
        ElMessage.error("密码读取错误，请确认密码是否符合要求，联系管理员排查问题");
      }
      break;
    case MFAMethod.EMAIL:
      if (!(await new Promise((resolve) => formRef.value?.validateField(["code", "email", "password", "rePassword"], resolve)))) return (loading.value = false), undefined;
      loading.value = true;
      try {
        const { success, message, data } = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/rsa/key/public`, method: Method.Get, responseType: "json", params: {}, data: new URLSearchParams() });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const importPublicKey = await window.crypto.subtle.importKey("spki", base64ToBuffer(data), { name: "RSA-OAEP", hash: { name: "SHA-512" } }, true, ["encrypt"]);
        const _password = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.password));
        const _rePassword = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.rePassword));

        result = request<unknown, Response<{ type: "SUCCESS" | "SELECT_ACCOUNT"; accounts: Record<"uid" | "account" | "phone" | "email" | "registrationTime" | "lastLoginTime", string>[] }>>({ url: `${SERVER.IAM}/retrieve_password/email`, method: Method.Post, responseType: "json", params: {}, data: { ident: form.email, code: form.code, password: bufferToBase64(_password), confirmationPassword: bufferToBase64(_rePassword), ptype: "RSA" } });
      } catch (error) {
        console.error(error);
        ElMessage.error("密码读取错误，请确认密码是否符合要求，联系管理员排查问题");
      }
      break;
    case MFAMethod.TOTP:
      break;
  }
  if (result) {
    try {
      const { success, message, data } = await result;
      if (success) {
        if (data) {
          switch (data.type) {
            case loginResultType.SELECT_ACCOUNT:
              accountList.value = (data.accounts instanceof Array ? data.accounts : []).map((v) => ({ ...v, registrationTime: moment(v.registrationTime, "x").format("YYYY.MM.DD"), lastLoginTime: moment(v.lastLoginTime, "x").format("YYYY.MM.DD") }));
              loading.value = false;
              return;
            default:
              break;
          }
        }
        ElMessage.success("成功重置密码！请重新登录！");
        form.password = form.rePassword;
        form.code = "";
        form.account = "";
        form.phone = "";
        form.email = "";
        form.rePassword = "";
        changeType(operateType.LOGIN);
      } else throw Object.assign(new Error(message), { success, data });
    } catch (error) {
      if ([loginChannels.GIT_HUB, loginChannels.WECHAT].includes(active.value as loginChannels)) active.value = loginChannels.PASSWORD;
      if (error instanceof Error) ElMessage.error(error.message);
    }
  }
  loading.value = false;
}
async function expiredSubmit() {
  if (!formRef.value) return;
  if (!mfaLoginMethods.value) return;
  if (loading.value) return;
  let result: Promise<Response<LoginData>> | undefined = undefined;
  formRef.value.clearValidate();

  await nextTick();

  if (await new Promise((resolve) => formRef.value && formRef.value.validateField(["password", "rePassword"], resolve))) {
    loading.value = true;

    try {
      const { success, message, data } = await request<unknown, Response<string>>({ url: `${SERVER.IAM}/rsa/key/public`, method: Method.Get, responseType: "json", params: {}, data: new URLSearchParams() });
      if (!success) throw Object.assign(new Error(message), { success, data });
      const importPublicKey = await window.crypto.subtle.importKey("spki", base64ToBuffer(data), { name: "RSA-OAEP", hash: { name: "SHA-512" } }, true, ["encrypt"]);
      const _ticket = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.ticket));
      const _password = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.password));
      const _rePassword = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.rePassword));

      result = request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/login/password_expired`, method: Method.Post, responseType: "json", params: {}, data: { ticket: bufferToBase64(_ticket), password: bufferToBase64(_password), confirmationPassword: bufferToBase64(_rePassword), ptype: "RSA" } });
    } catch (error) {
      console.error(error);
      ElMessage.error("密码读取错误，请确认密码是否符合要求，联系管理员排查问题");
    }
  }

  if (result) await workResult(result);
  loading.value = false;
}
async function accountSubmit() {
  if (!formRef.value) return;
  if (!mfaLoginMethods.value) return;
  if (loading.value) return;
  let result: Promise<Response<LoginData>> | undefined = undefined;
  formRef.value.clearValidate();

  await nextTick();

  if (await new Promise((resolve) => formRef.value && formRef.value.validateField(["uid"], resolve))) {
    loading.value = true;
    result = request<unknown, Response<LoginData>>({ url: `${SERVER.IAM}/login/select_account`, method: Method.Post, responseType: "json", params: {}, data: { ticket: form.ticket, uid: form.uid } });
  }

  if (result) await workResult(result);
  loading.value = false;
}

/**
 **************************************************************
 **************************************************************
 **************************************************************
 **************************************************************
 **************************************************************
 */

function changeType($type: keyof typeof operateType) {
  type.value = $type;
  coolingCaptcha.value = 0;
  switch ($type) {
    case operateType.RETRIEVE:
      mfaActive.value = MFAMethod.SMS;
      // mfaActive.value = MFAMethod.EMAIL;
      accountList.value = [];
      form.uid = "";
      form.username = "";
      form.password = "";
      form.name = "";
      form.nickname = "";
      form.account = "";
      form.email = "";
      form.gender = gender.SECRET;
      form.birthday = "";
      form.rePassword = "";
      form.phone = "";
      form.code = "";
      break;
  }
}

function sendCaptcha() {
  return new Promise<{ certificate?: string; captcha?: string }>((resolve) => {
    nextTick(async () => {
      if (!formRef.value) return resolve({});
      if (loadingCaptcha.value) return resolve({});
      loadingCaptcha.value = true;
      formRef.value.clearValidate();
      try {
        if (!type.value) {
          resolve({});
          return ElMessage.error("未知操作");
        } else if (type.value === operateType.SIGNIN) {
          if (!(await new Promise((resolve) => formRef.value?.validateField(["phone"], resolve)))) return ElMessage.error(`请填写正确的手机号`);
          await inputCertificate(form.phone, ($form) => (resolve($form), request<unknown, { success: boolean; message: string }>({ url: `${SERVER.IAM}/authentication/sign_up/sms_code`, method: Method.Get, responseType: "json", params: { ...$form, phone: form.phone }, data: {} })));
          ElMessage.success("验证码发送成功");
          coolingCaptcha.value = 60;
          return;
        } else if (type.value === operateType.LOGIN) {
          switch (active.value) {
            case loginChannels.EMAIL:
            case loginChannels.PASSWORD:
              if (!(await new Promise((resolve) => formRef.value?.validateField(["username"], resolve)))) return ElMessage.error(`请填写正确的账号`);
              if (!(await requireCaptcha({ username: form.username }))) {
                resolve({});
              } else {
                await inputCertificate(form.username, async ($form) => (resolve($form), { success: true, message: "" }));
              }
              return;
            case loginChannels.REFRESH_TOKEN:
              resolve({});
              return;
            case loginChannels.SMS_CODE:
              if (!(await new Promise((resolve) => formRef.value?.validateField(["phone"], resolve)))) return ElMessage.error(`请填写正确的手机号`);
              await inputCertificate(form.phone, ($form) => (resolve($form), request<unknown, { success: boolean; message: string }>({ url: `${SERVER.IAM}/login/send_sms_login_code`, method: Method.Post, responseType: "json", params: { ...$form, phone: form.phone }, data: {} })));
              ElMessage.success("验证码发送成功");
              coolingCaptcha.value = 60;
              return;
            case loginChannels.GIT_HUB:
              resolve({});
              return;
            case loginChannels.WECHAT:
              resolve({});
              return;
          }
        } else if (type.value === operateType.RETRIEVE) {
          switch (mfaActive.value) {
            case MFAMethod.SMS:
              if (!(await new Promise((resolve) => formRef.value?.validateField(["phone"], resolve)))) return ElMessage.error(`请填写正确的手机号`);
              await inputCertificate(form.phone, ($form) => (resolve($form), request<unknown, { success: boolean; message: string }>({ url: `${SERVER.IAM}/retrieve_password/send_sms_code`, method: Method.Post, responseType: "json", params: { ...$form, phone: form.phone }, data: {} })));
              ElMessage.success("验证码发送成功");
              // getUser(form.phone);
              coolingCaptcha.value = 60;
              return;
            case MFAMethod.EMAIL:
              if (!(await new Promise((resolve) => formRef.value?.validateField(["email"], resolve)))) return ElMessage.error(`请填写正确的邮箱地址`);
              await inputCertificate(form.email, ($form) => (resolve($form), request<unknown, { success: boolean; message: string }>({ url: `${SERVER.IAM}/retrieve_password/send_email_code`, method: Method.Post, responseType: "json", params: { ...$form, email: form.email }, data: {} })));
              ElMessage.success("验证码发送成功");
              coolingCaptcha.value = 60;
              // getUser(form.email);

              return;
          }
        }
      } catch (error) {
        resolve({});
        return;
      } finally {
        loadingCaptcha.value = false;
      }
    });
  });
}
//  getUserPasswordStrategy
function getUserInfo(data) {
  getUser({ pageNumber: 1, pageSize: 20, keyword: data, schemas: "ROLE", scope: "ALL" }).then((res) => {
    if (res.success) {
      getUserPasswordStrategy(res.data[0].id);
    }
  });
}

async function sendMFACaptcha() {
  if (!formRef.value) return;
  if (loadingCaptcha.value) return;
  loadingCaptcha.value = true;
  formRef.value.clearValidate();
  try {
    let result: Promise<Response<unknown>> | undefined = undefined;
    switch (mfaActive.value) {
      case MFAMethod.PASSWORD:
        break;
      case MFAMethod.SMS:
        result = request<unknown, Response<unknown>>({ url: `${SERVER.IAM}/login/mfa/code/sms`, method: Method.Post, responseType: "json", params: { ticket: form.ticket }, data: {} });
        break;
      case MFAMethod.EMAIL:
        result = request<unknown, Response<unknown>>({ url: `${SERVER.IAM}/login/mfa/code/email`, method: Method.Post, responseType: "json", params: { ticket: form.ticket }, data: {} });
        break;
      case MFAMethod.TOTP:
        break;
    }
    if (!result) throw new Error("未知验证码");
    const { success, message, data } = await result;
    if (success) {
      coolingCaptcha.value = 60;
      ElMessage.success("验证码发送成功");
    } else throw Object.assign(new Error(message), { success, data });
    return { success, message };
  } catch (error) {
    return;
  } finally {
    loadingCaptcha.value = false;
  }
}

async function inputCertificate(code: string, callback: (form: { certificate?: string; captcha?: string }) => Promise<{ success: boolean; message: string } & Record<string, unknown>>): Promise<void> {
  const $form = reactive({
    title: `${t("login.Enter the verification code")}`,
    certificate: "",
    captcha: "",
  });
  2;
  const $input = ref<import("element-plus").InputInstance>();
  const $canvasRef = ref<HTMLCanvasElement>();
  const unWatch = watch($canvasRef, ($canvas) => {
    if ($canvas instanceof HTMLCanvasElement) {
      unWatch();
      $form.captcha = "";
      $form.certificate = String(`xxxxxxxx-xxxx-4xxx-yxxx-${Date.now().toString(16).padStart(12, "x")}`).replace(/[xy]/g, (c) => Number(c === "x" ? (Math.random() * 16) | 0 : (((Math.random() * 16) | 0) & 0x3) | 0x8).toString(16));
      updateCertificate($canvas, $form.certificate).then(() => nextTick(() => $input.value?.focus()));
    }
  });
  await bindFormBox(
    [
      h(ElAlert, { type: "info", title: `${t("login.verification code")}`, description: `${t("login.verification1")}`, showIcon: true, closable: false, style: { marginBottom: "22px" } }),
      h(ElFormItem, { rules: [{ required: true, message: `${t("login.verification2")}`, trigger: "blur" }], prop: "captcha", label: "", size: "large" }, () => [
        h(ElInput, { "ref": (vm) => ($input.value = vm as import("element-plus").InputInstance), "prefixIcon": ChatDotSquare, "placeholder": `${t("login.verification2")}`, "clearable": true, "modelValue": $form.captcha, "onUpdate:modelValue": (v) => ($form.captcha = v), "style": { verticalAlign: "top", width: "calc(100% - 118px)", marginRight: "12px" } }),
        h("canvas", {
          ref: (vm) => ($canvasRef.value = vm as HTMLCanvasElement),
          class: ["captcha-img"],
          height: "40",
          width: "100",
          title: "看不清，换一张",
          onClick({ target }: PointerEvent) {
            if (target instanceof HTMLCanvasElement) {
              $form.captcha = "";
              $form.certificate = String(`xxxxxxxx-xxxx-4xxx-yxxx-${Date.now().toString(16).padStart(12, "x")}`).replace(/[xy]/g, (c) => Number(c === "x" ? (Math.random() * 16) | 0 : (((Math.random() * 16) | 0) & 0x3) | 0x8).toString(16));
              updateCertificate(target, $form.certificate).then(() => nextTick(() => $input.value?.focus()));
            }
          },
        }),
      ]),
    ],
    $form,
    async () => {
      const { success, message, data } = await callback({ certificate: $form.certificate, captcha: $form.captcha });
      if (!success) throw Object.assign(new Error(message), { success, data });
      return { success, message };
    }
  );
}
async function updateCertificate(canvasRef: HTMLCanvasElement, certificate: string) {
  try {
    const { success, data, message } = await captchaForImage({ certificate });
    if (success) {
      await bindCanvasImage(canvasRef.getContext("2d") as CanvasRenderingContext2D, data);
    } else throw Object.assign(new Error(message), { success, data, message });
  } catch (error) {
    return;
  }
}
</script>

<style lang="scss" scoped>
.user-login {
  :deep(.el-dialog) {
    .el-dialog__header {
      // background: #409eff;
      width: 100%;
      padding-bottom: 15px;
      padding-top: 15px;
      .el-dialog__title {
        // color: #fff;
      }
      .el-dialog__close {
        // color: #fff;
      }
    }
  }
  :deep(.el-form-item__error) {
    text-align: left;
  }
}
.term-text {
  text-indent: 30px;
}
.term {
  display: flex;
  align-items: center;
  color: #606266;
  > b {
    color: #409eff;
    font-weight: 400;
    text-decoration: underline;
  }
}
.userManage {
  color: #606266;
  cursor: pointer;
}
#login-message {
  background: url("@/assets/login/bg.png") no-repeat;
  background-size: 100% 100%;
}
.block {
  width: 100%;
  height: 100%;
}
:deep(.block) {
  .el-carousel {
    height: 100%;
    .el-carousel__container {
      height: 100%;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .el-carousel__button {
      height: 5px;
      border-radius: 3px;
    }
  }
}

.switch-language {
  position: fixed;
  top: 20px;
  right: 90px;
  z-index: 1;
}
.switch-theme {
  position: fixed;
  top: 22px;
  right: 20px;
  z-index: 1;
}
.loading-chase {
  margin-left: auto;
  margin-right: auto;
  width: 40px;
  height: 40px;
  position: relative;
  animation: loading-chase 2.5s infinite linear both;

  &-dot {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    animation: loading-chase-dot 2s infinite ease-in-out both;
    &:before {
      content: "";
      display: block;
      width: 25%;
      height: 25%;
      background-color: var(--el-color-primary);
      border-radius: 100%;
      animation: loading-chase-dot-before 2s infinite ease-in-out both;
    }
    &:nth-child(1) {
      animation-delay: -1.1s;
      &:before {
        animation-delay: -1.1s;
      }
    }
    &:nth-child(2) {
      animation-delay: -1s;
      &:before {
        animation-delay: -1s;
      }
    }
    &:nth-child(3) {
      animation-delay: -0.9s;
      &:before {
        animation-delay: -0.9s;
      }
    }
    &:nth-child(4) {
      animation-delay: -0.8s;
      &:before {
        animation-delay: -0.8s;
      }
    }
    &:nth-child(5) {
      animation-delay: -0.7s;
      &:before {
        animation-delay: -0.7s;
      }
    }
    &:nth-child(6) {
      animation-delay: -0.6s;
      &:before {
        animation-delay: -0.6s;
      }
    }
  }
}

@keyframes loading-chase {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-chase-dot {
  80%,
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-chase-dot-before {
  50% {
    transform: scale(0.4);
  }
  100%,
  0% {
    transform: scale(1);
  }
}

.line-radio {
  :deep(.el-radio__label) {
    flex: 1;
  }
}
</style>
