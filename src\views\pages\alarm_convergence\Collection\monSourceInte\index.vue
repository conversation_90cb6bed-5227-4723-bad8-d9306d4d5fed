<template>
  <div>
    <el-card class="el-card-mt" style="height: 41.5vh; overflow-y: auto">
      <div class="modules-item">
        <span class="modules-title">已集成</span>
      </div>
      <el-row :gutter="24">
        <el-col :span="8" v-for="item in IntegratedList" :key="item.id">
          <el-card style="padding-bottom: 10px; box-sizing: border-box">
            <el-col class="integrated">
              <div class="integrated-icon">
                <img src="../../../assets/<EMAIL>" alt="" />
              </div>
              <div class="integrated-msg">
                <p>
                  {{ options[item.sourceType] }}
                  <span :class="item.enabled ? 'enable' : 'disable'">
                    {{ item.enabled ? "已启用" : "已禁用" }}
                  </span>
                </p>
                <p></p>
              </div>
              <div class="integrated-status">
                <el-select v-model="item.enabled" placeholder="请选择" @change="statusChange(item)">
                  <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </div>
            </el-col>
            <!-- IntegratedList -->
            <!--  -->
            <el-col :style="{ textAlign: 'center', marginTop: '10px' }">
              <el-button v-if="userInfo.hasPermission(PERMISSION.group527766513894031360.auth527766678474326016)" style="width: 100%" type="primary" @click="handleDetail(item.id)"> 查看详情 </el-button>
            </el-col>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="el-card-mt" style="height: 41.5vh; overflow-y: auto">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="我的设备" name="first">
          <el-row :gutter="24">
            <el-col :span="8" v-for="v in notIntegratedlist" :key="v.type" v-show="v.type === 'NET_CARE' && v.status">
              <el-card style="padding-bottom: 10px; box-sizing: border-box">
                <el-col class="monName"> {{ v.name }} </el-col>
                <el-col class="monType"> 未集成 </el-col>
                <el-col :style="{ textAlign: 'center', marginTop: '10px' }">
                  <el-button v-if="userInfo.hasPermission(PERMISSION.group527766513894031360.auth527766647138680832)" class="access" style="width: 100%" @click="accessCurrent(v)">接入集成</el-button>
                </el-col>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="其它监控源" name="second">
          <el-row :gutter="20">
            <el-col :span="8" v-for="v in notIntegratedlist" :key="v.name" v-show="v.type !== 'NET_CARE' && v.status">
              <el-card style="padding-bottom: 10px; box-sizing: border-box">
                <el-col class="monName"> {{ v.name }} </el-col>
                <el-col class="monType"> 未集成 </el-col>
                <el-col :style="{ textAlign: 'center', marginTop: '10px' }">
                  <el-button v-if="userInfo.hasPermission(PERMISSION.group527766513894031360.auth527766647138680832)" class="access" style="width: 100%" @click="accessCurrent(v)">接入集成</el-button>
                  <!-- {{ PERMISSION }} -->
                </el-col>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { getMointerSourceList, addtMointerSource, updateMointerSource } from "@/views/pages/apis/monSourceInte";
import getUserInfo from "@/utils/getUserInfo";

export default {
  data() {
    return {
      userInfo: getUserInfo(),
      activeName: "first",
      ServiceSearch: "", //搜索关键字
      notIntegratedlist: [
        {
          type: "ZABBIX",
          name: "Zabbix",
          status: true,
        },
        {
          type: "STANDARD",
          name: "Standard",
          status: true,
        },
        {
          type: "NET_CARE",
          name: "Netcare v6",
          status: true,
        },

        {
          type: "PROMETHEUS",
          name: "Prometheus",
          status: true,
        },
        {
          type: "N9E",
          name: "N9E 夜莺",
          status: true,
        },
        {
          type: "IDEAL_METRIC",
          name: "Open-Falcon",
          status: true,
        },
        {
          type: "UNKNOWN",
          name: "标准集成",
          status: true,
        },
      ],
      IntegratedList: [],
      options: {
        ZABBIX: "Zabbix",
        STANDARD: "Standard",
        NET_CARE: "Netcare v6",
        PROMETHEUS: "Prometheus",
        N9E: "N9E",
        IDEAL_METRIC: "Open-Falcon",
        UNKNOWN: "Unknown",
      },
      value: "",
      statusList: [
        {
          label: "启用",
          value: true,
        },
        {
          label: "禁用",
          value: false,
        },
      ],
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      getMointerSourceList().then((res) => {
        if (res.code === 200 && res.success == true) {
          this.IntegratedList = [...res.data];

          res.data.forEach((item) => {
            this.notIntegratedlist.filter((v) => {
              if (v.type === item.sourceType) {
                v.status = false;
              }
            });
            // resultArr.push( arr1.filter(_item => _item.value === item.value)[0] );
          });
        }
      });
    },
    accessCurrent(item) {
      addtMointerSource({ sourceType: item.type }).then((res) => {
        if (res.code === 200 && res.success == true) {
          this.$message.success("接入集成成功");
          this.getList();
        }
      });
    },
    statusChange(val) {
      updateMointerSource({
        id: val.id,
        enabled: val.enabled,
      }).then(({ success }) => {
        // // console.log(success);
        if (success) {
          this.$message.success("状态变更成功");
          this.getList();
        }
      });
    },
    handleDetail(id) {
      this.$router.push({
        name: "531001143573086208",
        params: { id },
      });
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    handleSizeChange(v) {
      this.page.pageSize = v;
    },
    handleCurrentPageChange(v) {
      this.page.pageNumber = v;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .elstyle-card {
  margin-top: 20px;
  &::-webkit-scrollbar {
    width: 0;
  }
}
::v-deep .el-card-mt {
}

.modules-item {
  .modules-title {
    color: #1d2129;
    font-weight: 500;
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}
.monName {
  color: #17233d;
  font-weight: 500;
  font-size: 20px;
  text-align: left;
  font-family: SourceHanSansSC-medium;
}

.monType {
  color: #c9cdd4;
  font-weight: 500;
  font-size: 14px;
  text-align: left;
  font-family: SourceHanSansSC-medium;
}
::v-deep .integrated {
  display: flex;
  align-items: center;
  justify-content: space-between;
  > .integrated-icon {
    flex: none;
    width: 50px;
    > img {
      width: 100%;
      height: auto;
    }
  }
  > .integrated-msg {
    flex: 1;
    padding-left: 5px;
    box-sizing: border-box;
    font-size: 20px;
    font-family:
      PingFang SC-Medium,
      PingFang SC;
    font-weight: 500;
    color: #17233d;
    .enable {
      font-size: 12px;
      font-family:
        PingFang SC-Medium,
        PingFang SC;
      font-weight: 500;
      color: #0fc6c2;
      background: #e8fffb;
      padding: 2px 4px;
      box-sizing: border-box;
    }
    .disable {
      font-size: 12px;
      font-family:
        PingFang SC-Medium,
        PingFang SC;
      font-weight: 500;
      color: #f53f3f;
      background: #ffece8;
      padding: 2px 4px;
      box-sizing: border-box;
    }
  }
  > .integrated-status {
    flex: none;
    width: 120px;
  }
}
.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}
// ::v-deep .elstyle-col {
//   padding: 0 !important;
// }
::v-deep .access {
  // border-color: #2f54eb;
  // color: #2f54eb;
}
::v-deep .elstyle-form-item__content .el-form-item-content {
  display: flex;
  flex-direction: column;
}
</style>
