<template>
  <div>
    <el-dialog :title="type == 'add'?$t('eventPriority.AddPriorityMatrix'):$t('eventPriority.EditPriorityMatrix')" v-model="dialogFormVisible" :before-close="handleClose" width="50vw">
      <el-form :model="form" ref="ruleForm">
        <el-form-item :label="$t('eventPriority.Name')" :label-width="formLabelWidth" prop="name" :rules="[{ required: true, message: $t('eventPriority.PleaseEnterPriorityMatrixName'), trigger: 'blur' }]">
          <el-input v-model="form.name" autocomplete="off" :placeholder="$t('eventPriority.PleaseEnterName')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('eventPriority.DescriptionField')" :label-width="formLabelWidth" prop="desc">
          <el-input type="textarea" v-model="form.desc" autocomplete="off" :rows="2" :placeholder="$t('eventPriority.PleaseEnterDescription')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('eventPriority.IsDefaultField')" :label-width="formLabelWidth" prop="defaultMatrix">
          <el-checkbox v-model="form.defaultMatrix"></el-checkbox>
        </el-form-item>
        <el-form-item v-if="type == 'add'" :label="$t('eventPriority.SelectSecurityDirectory')" :label-width="formLabelWidth" prop="containerId" :rules="[{ required: true, message: $t('eventPriority.PleaseSelectSecurityDirectory'), trigger: 'blur' }]">
          <treeAuth ref="treeAuthRef" :treeStyle="treeStyle"></treeAuth>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel()">{{$t('glob.NO')}}</el-button>
          <el-button type="primary" @click="confirm('ruleForm')">{{$t('glob.OK')}}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent } from "vue";
import { ElMessage, ElMenuItem, ElMessageBox } from "element-plus";
import getUserInfo from "@/utils/getUserInfo";

import { addNewPriorityMatrix, EditNewSlaConfig, updateBasicInfo, hasPriorityMatrixcheckDefault } from "@/views/pages/apis/eventPriority";
import treeAuth from "@/components/treeAuth/index.vue";
import { useI18n } from "vue-i18n";

export default defineComponent({
  name: "eventPriorityCreate",
  components: {
    treeAuth,
  },
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      form: {
        containerId: "",
        name: "",
        desc: "",
        defaultMatrix: false,
      },
      i18n: useI18n(),
      containerIdS: null,
      title: "",
      checked: false,
      dialogFormVisible: false,
      formLabelWidth: "175px",
      type: "",
      value: "",
      disabled: "",
      treeStyle: {
        width: "300px",
        height: "150px",
      }
    };
  },
  watch: {
    dialog(val) {
      this.dialogFormVisible = val;
    },
    type(val) {
      if (val === "add") {
        // this.form.containerId = "";
        // this.form.name = "";
        // this.form.desc = "";
        // this.form.defaultMatrix = false;
        for (var key in this.form) {
          this.form[key] = null;
          if (key == "defaultMatrix") {
            this.form[key] = false;
          }
        }
      }
    },
  },
  created() {},
  methods: {
    confirm(formName) {
      if (this.type == "add") {
        this.form.containerId = this.$refs.treeAuthRef.treeItem.id;
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.type === "add") {
            if (!this.form.containerId) {
              ElMessage.error(this.i18n.t("eventPriority.PleaseSelectSecurityDirectory"));
              return;
            }

            if (this.form.defaultMatrix == true) {
              hasPriorityMatrixcheckDefault({ tenantId: getUserInfo().currentTenant.id }).then((res) => {
                if (res.success == false) {
                  ElMessageBox.confirm(this.i18n.t("eventPriority.CustomerHasDefaultPriorityMatrix"),this.i18n.t("eventPriority.Prompt"), {
                    confirmButtonText: this.i18n.t("eventPriority.Confirm"),
                    cancelButtonText: this.i18n.t("eventPriority.Cancel"),
                    type: "warning",
                  })
                    .then(() => {
                      addNewPriorityMatrix(this.form)
                        .then((res) => {
                          if (res.success) {
                            ElMessage.success("新增成功");
                            this.$emit("dialogClose", false);
                            this.$refs[formName].resetFields();
                            this.form.defaultMatrix = false;
                            this.$refs.treeAuthRef.getSafeContaine();
                            this.$refs.treeAuthRef.treeId = -1;
                          } else {
                            ElMessage.error(JSON.parse(res.data)?.message);
                            this.$emit("dialogClose", false);
                            this.$refs[formName].resetFields();
                            this.form.defaultMatrix = false;
                            this.$refs.treeAuthRef.getSafeContaine();
                            this.$refs.treeAuthRef.treeId = -1;
                          }
                        })
                        .catch((e) => {
                          if (e instanceof Error) ElMessage.error(e.message);
                          this.$refs.treeAuthRef.getSafeContaine();
                          this.form.defaultMatrix = false;
                          this.$refs.treeAuthRef.treeId = -1;
                          this.$refs[formName].resetFields();
                          // this.$emit("dialogClose", false);
                        });
                    })
                    .catch(() => {});
                } else {
                  addNewPriorityMatrix(this.form)
                    .then((res) => {
                      if (res.success) {
                        ElMessage.success("新增成功");
                        this.$emit("dialogClose", false);
                        this.$refs[formName].resetFields();
                        this.form.defaultMatrix = false;
                        this.$refs.treeAuthRef.getSafeContaine();
                        this.$refs.treeAuthRef.treeId = -1;
                      } else {
                        ElMessage.error(JSON.parse(res.data)?.message);
                        this.$emit("dialogClose", false);
                        this.$refs[formName].resetFields();
                        this.form.defaultMatrix = false;
                        this.$refs.treeAuthRef.getSafeContaine();
                        this.$refs.treeAuthRef.treeId = -1;
                      }
                    })
                    .catch((e) => {
                      if (e instanceof Error) ElMessage.error(e.message);
                      this.$refs.treeAuthRef.getSafeContaine();
                      this.form.defaultMatrix = false;
                      this.$refs.treeAuthRef.treeId = -1;
                      this.$refs[formName].resetFields();
                      // this.$emit("dialogClose", false);
                    });
                }
              });
            } else {
              addNewPriorityMatrix(this.form)
                .then((res) => {
                  if (res.success) {
                    ElMessage.success("新增成功");
                    this.$emit("dialogClose", false);
                    this.$refs[formName].resetFields();
                    this.form.defaultMatrix = false;
                    this.$refs.treeAuthRef.getSafeContaine();
                    this.$refs.treeAuthRef.treeId = -1;
                  } else {
                    ElMessage.error(JSON.parse(res.data)?.message);
                    this.$emit("dialogClose", false);
                    this.$refs[formName].resetFields();
                    this.form.defaultMatrix = false;
                    this.$refs.treeAuthRef.getSafeContaine();
                    this.$refs.treeAuthRef.treeId = -1;
                  }
                })
                .catch((e) => {
                  if (e instanceof Error) ElMessage.error(e.message);
                  this.$refs.treeAuthRef.getSafeContaine();
                  this.form.defaultMatrix = false;
                  this.$refs.treeAuthRef.treeId = -1;
                  this.$refs[formName].resetFields();
                  // this.$emit("dialogClose", false);
                });
            }
          } else {
            if (this.form.defaultMatrix == true) {
              hasPriorityMatrixcheckDefault({ tenantId: getUserInfo().currentTenant.id }).then((res) => {
                if (res.success == false) {
                    ElMessageBox.confirm(this.i18n.t("eventPriority.CustomerHasDefaultPriorityMatrix"),this.i18n.t("eventPriority.Prompt"), {
                      confirmButtonText: this.i18n.t("eventPriority.Confirm"),
                      cancelButtonText: this.i18n.t("eventPriority.Cancel"),
                      type: "warning",
                    })
                    .then(() => {
                      updateBasicInfo(this.form)
                        .then((res) => {
                          if (res.success) {
                            ElMessage.success("修改成功");
                            this.$emit("dialogClose", false);
                            this.form.defaultMatrix = false;
                            this.$refs[formName].resetFields();
                          } else {
                            ElMessage.error(JSON.parse(res.data)?.message);
                            this.$emit("dialogClose", false);
                          }
                        })
                        .catch((e) => {
                          if (e instanceof Error) ElMessage.error(e.message);
                          // this.$emit("dialogClose", false);
                        });
                    })
                    .catch(() => {});
                } else {
                  updateBasicInfo(this.form)
                    .then((res) => {
                      if (res.success) {
                        ElMessage.success("修改成功");
                        this.$emit("dialogClose", false);
                        this.form.defaultMatrix = false;
                        this.$refs[formName].resetFields();
                      } else {
                        ElMessage.error(JSON.parse(res.data)?.message);
                        this.$emit("dialogClose", false);
                      }
                    })
                    .catch((e) => {
                      if (e instanceof Error) ElMessage.error(e.message);
                      // this.$emit("dialogClose", false);
                    });
                }
              });
            } else {
              updateBasicInfo(this.form)
                .then((res) => {
                  if (res.success) {
                    ElMessage.success("修改成功");
                    this.$emit("dialogClose", false);
                    this.form.defaultMatrix = false;
                    this.$refs[formName].resetFields();
                  } else {
                    ElMessage.error(JSON.parse(res.data)?.message);
                    this.$emit("dialogClose", false);
                  }
                })
                .catch((e) => {
                  if (e instanceof Error) ElMessage.error(e.message);
                  // this.$emit("dialogClose", false);
                });
            }
          }
          // this.dialogFormVisible = false;
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      this.form.defaultMatrix = false;
      if (this.type == "add") {
        this.$refs.treeAuthRef.getSafeContaine();
        this.$refs.treeAuthRef.treeId = -1;
      }
    },
    cancel() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      this.form.defaultMatrix = false;
      if (this.type == "add") {
        this.$refs.treeAuthRef.getSafeContaine();
        this.$refs.treeAuthRef.treeId = -1;
      }
    },
    blurChange(data) {
      this.tags.push(data);
    },
    tagClose(index) {
      this.tags.splice(index, 1);
    },
  },
  expose: ["type", "disabled", "title", "form", "value"],
});
</script>
