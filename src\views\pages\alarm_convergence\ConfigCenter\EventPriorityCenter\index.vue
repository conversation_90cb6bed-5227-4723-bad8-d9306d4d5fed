<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane :label="$t('eventPriority.MappingRelationshipSettings')" name="first">
        <el-row :gutter="24" class="event_priority_center">
          <el-col :span="24">
            <h3>{{ $t("eventPriority.MappingRelationshipSettings") }}</h3>
            <el-scrollbar :height="height - 150">
              <div>
                <div v-for="(val, i) in mappingList" :key="val.id">
                  <el-divider content-position="left">{{ val.sourceName }}</el-divider>
                  <ul class="alarm-status">
                    <li>
                      {{ val.sourceName }}
                    </li>
                    <li v-for="item in val.mappings" :key="item.id" :class="item.eventSeverity">
                      <!-- {{ v. }} -->

                      <span>{{ item.alarmSeverityName }}</span>
                    </li>
                  </ul>
                  <ol>
                    <li>CloudCare</li>
                    <li v-for="(item, index) in val.mappings" :key="index">
                      <el-dropdown @command="handleCommand" :disabled="!userInfo.hasPermission(服务管理中心_告警映射关系_编辑)">
                        <span class="el-dropdown-link">
                          {{ valueList[item.eventSeverity] }}<el-icon class="el-icon--right"><el-icon-arrow-down /></el-icon>
                        </span>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item v-for="item in statusOptions" :key="item.label" :command="beforeHandleCommand(item, index, i, '1')">{{ item.label }}</el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </li>
                  </ol>
                  <ol v-if="val.sourceName == 'Netcare V6'">
                    <li>{{ $t("eventPriority.AutomaticTicketGeneration") }}</li>
                    <li v-for="(item, index) in val.mappings" :key="index">
                      <el-dropdown @command="handleCommand" :disabled="!userInfo.hasPermission(服务管理中心_告警映射关系_编辑)">
                        <span class="el-dropdown-link">
                          {{ valueList1[item.autoEvent] }}<el-icon class="el-icon--right"><el-icon-arrow-down /></el-icon>
                        </span>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item v-for="item in typeSource" :key="item.label" :command="beforeHandleCommand(item, index, i, '2')">{{ item.label }}</el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </li>
                  </ol>
                </div>
              </div>
            </el-scrollbar>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane :label="$t('eventPriority.PriorityMatrix')" name="second">
        <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="getList()" @current-change="getList()">
          <template #right>
            <span class="">
              <el-button v-if="userInfo.hasPermission(服务管理中心_优先级矩阵模板_新增)" type="primary" :icon="Plus" @click="PriorityMatrixDialog('add')">{{ $t("eventPriority.AddPriorityMatrix") }}</el-button>
            </span>
          </template>
          <template #default="{ height: tableHeight }">
            <el-table v-loading="tableloading" stripe :data="priorityListData" :height="tableHeight" style="width: 100%" :row-key="getRowKeys" :expand-row-keys="expands" @expand-change="handleExpandChange">
              <el-table-column type="expand">
                <template #default="{ row, expanded }">
                  <eventConfig v-if="expanded" :detail="row" :width="width - 40"></eventConfig>
                </template>
              </el-table-column>

              <!-- <el-table-column prop="name" :formatter="formatterTable" label="优先级矩阵名称"></el-table-column> -->
              <TableColumn type="condition" :prop="`name`" :label="$t('eventPriority.PriorityMatrixName')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="getPriorityList()" :formatter="formatterTable"></TableColumn>

              <!-- <el-table-column prop="desc" :formatter="formatterTable" label="描述"></el-table-column> -->
              <TableColumn type="condition" :prop="`desc`" :label="$t('eventPriority.Description')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByDescription" :filters="$filter0" @filter-change="getPriorityList()" :formatter="formatterTable"></TableColumn>

              <TableColumn
                type="enum"
                :prop="`active`"
                :label="$t('eventPriority.IsDefault')"
                :min-width="100"
                :showOverflowTooltip="true"
                show-filter
                v-model:filtered-value="prioritySearchForm.defaultMatrix"
                :filters="[
                  { value: 'true', text: '√' },
                  { value: 'false', text: '×' },
                ]"
                @filter-change="getPriorityList()"
              >
                <template #default="scope">
                  <span> {{ scope.row.defaultMatrix == true ? "√" : "×" }}</span>
                </template>
              </TableColumn>

              <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="180">
                <template #default="{ row }">
                  <span>
                    <el-link v-if="row.verifyPermissionIds.includes(服务管理中心_优先级矩阵模板_编辑)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="PriorityMatrixDialog('edit', row)">{{ $t("glob.edit") }}</el-link>
                  </span>
                  <el-popconfirm :title="delTitle" @confirm="delConfirm(row)" v-if="row.verifyPermissionIds.includes(服务管理中心_优先级矩阵模板_删除)">
                    <template #reference>
                      <el-button v-if="row.verifyPermissionIds.includes(服务管理中心_优先级矩阵模板_删除)" type="text" textColor="danger" @click="delLevel()">{{ $t("glob.delete") }}</el-button>
                    </template>
                  </el-popconfirm>
                  <span v-if="row.verifyPermissionIds.includes(服务管理中心_优先级矩阵模板_安全)">
                    <el-link :type="row.verifyPermissionIds.includes(服务管理中心_优先级矩阵模板_安全) ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="row.verifyPermissionIds.includes(服务管理中心_优先级矩阵模板_安全) ? false : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </pageTemplate>
      </el-tab-pane>
    </el-tabs>
  </el-card>
  <eventPriorityCreate :dialog="eventPriorityialog" ref="eventPriorityRef" @dialogClose="dialogClose"></eventPriorityCreate>
</template>

<script setup lang="ts" generic="T extends object">
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete, ArrowDown as ElIconArrowDown, Bottom, Right } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox } from "element-plus";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";
import { 服务管理中心_告警映射关系_编辑, 服务管理中心_优先级矩阵模板_新增, 服务管理中心_优先级矩阵模板_编辑, 服务管理中心_优先级矩阵模板_删除, 服务管理中心_优先级矩阵模板_安全 } from "@/views/pages/permission";

import { getMointerSourceMappingList, deletePriorityMatrix, editMointerSourceMapping, getPriorityMatrixList, getNewPriorityMatrixList, getNewPriorityMatrixList2, editPriorityMatrix } from "@/views/pages/apis/eventPriority";
import getUserInfo from "@/utils/getUserInfo";
import type { TabsPaneContext } from "element-plus";
import pageTemplate from "@/components/pageTemplate.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import eventPriorityCreate from "./eventPriorityCreate";
import eventConfig from "./eventConfig.vue";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";
import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";

/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "EventPriorityCenter" });
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));
const eventPriorityialog = ref(false);

const userInfo = getUserInfo();
const paging = reactive({
  pageNumber: 1,
  pageSize: 10,
  total: 0,
});

// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
// const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);

const activeName = ref("first");
const tableloading = ref(false);
// const priorityListData = ref([]);
const priorityListData = ref<any[]>([]);

// const $filter0 = ref([
//   { text: "包含", value: "include" },
//   { text: "不包含", value: "exclude" },
//   { text: "等于", value: "eq" },
//   { text: "不等于", value: "ne" },
// ]);
const $filter0 = ref(exoprtMatch1);

const typeSource = ref([
  { label: "是", value1: true },
  { label: "否", value1: false },
]);

const prioritySearchForm = ref<Record<string, any>>({
  eqName: [] /* 等于的优先级矩阵名称 */,
  includeName: [] /* 包含的优先级矩阵名称 */,
  nameFilterRelation: "AND" /* 优先级矩阵名称过滤关系(AND,OR) */,
  neName: [] /* 不等于的优先级矩阵名称 */,
  excludeName: [] /* 不包含的优先级矩阵名称 */,

  eqDescription: [] /* 等于的优先级矩阵描述 */,
  includeDescription: [] /* 包含的优先级矩阵描述 */,
  descriptionFilterRelation: "AND" /* 优先级矩阵描述过滤关系(AND,OR) */,
  neDescription: [] /* 不等于的优先级矩阵描述 */,
  excludeDescription: [] /* 不包含的优先级矩阵描述 */,

  defaultMatrix: "" /* 是否默认 */,
});

const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByName) === "include") value0 = prioritySearchForm.value.includeName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = prioritySearchForm.value.excludeName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = prioritySearchForm.value.eqName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = prioritySearchForm.value.neName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = prioritySearchForm.value.includeName[prioritySearchForm.value.includeName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = prioritySearchForm.value.excludeName[prioritySearchForm.value.excludeName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = prioritySearchForm.value.eqName[prioritySearchForm.value.eqName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = prioritySearchForm.value.neName[prioritySearchForm.value.neName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: prioritySearchForm.value.nameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    prioritySearchForm.value.nameFilterRelation = v.relation;
    prioritySearchForm.value.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    prioritySearchForm.value.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    prioritySearchForm.value.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    prioritySearchForm.value.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByDescription = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByDescription) === "include") value0 = prioritySearchForm.value.includeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "exclude") value0 = prioritySearchForm.value.excludeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "eq") value0 = prioritySearchForm.value.eqDescription[0] || "";
    if (toValue(searchType0ByDescription) === "ne") value0 = prioritySearchForm.value.neDescription[0] || "";
    let value1 = "";
    if (toValue(searchType1ByDescription) === "include") value1 = prioritySearchForm.value.includeDescription[prioritySearchForm.value.includeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "exclude") value1 = prioritySearchForm.value.excludeDescription[prioritySearchForm.value.excludeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "eq") value1 = prioritySearchForm.value.eqDescription[prioritySearchForm.value.eqDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "ne") value1 = prioritySearchForm.value.neDescription[prioritySearchForm.value.neDescription.length - 1] || "";
    return {
      type0: toValue(searchType0ByDescription),
      type1: toValue(searchType1ByDescription),
      relation: prioritySearchForm.value.descriptionFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByDescription.value = v.type0 as typeof searchType0ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchType1ByDescription.value = v.type1 as typeof searchType1ByDescription extends import("vue").Ref<infer T> ? T : string;
    prioritySearchForm.value.descriptionFilterRelation = v.relation;
    prioritySearchForm.value.includeDescription = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    prioritySearchForm.value.excludeDescription = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    prioritySearchForm.value.eqDescription = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    prioritySearchForm.value.neDescription = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const handleClick = (tab: TabsPaneContext, event: Event) => {
  // console.log(tab, event);
};
const tableTitle = ref(["High", "Medium", "Low", "Normal", "Unknown"]);
const tableData = ref<[Record<string, string>, Record<string, string>, Record<string, string>, Record<string, string>, Record<string, string>]>([
  { High: "", Medium: "", Low: "", None: "", Unknown: "" },
  { High: "", Medium: "", Low: "", None: "", Unknown: "" },
  { High: "", Medium: "", Low: "", None: "", Unknown: "" },
  { High: "", Medium: "", Low: "", None: "", Unknown: "" },
  { High: "", Medium: "", Low: "", None: "", Unknown: "" },
]);

const alarmStatus = ref([{ Critical: "Critical", Major: "Major", Minor: "Minor", Warning: "Warning", Calculating: "Calculating", Normal: "" }]);

const alertOrderMappingItems = ref([]);
const deviceOrderMappingItems = ref([]);
const levelOptions = ref([
  { label: "P1", value: "P1" },
  { label: "P2", value: "P2" },
  { label: "P3", value: "P3" },
  { label: "P4", value: "P4" },
  { label: "P5", value: "P5" },
  { label: "P6", value: "P6" },
  { label: "P7", value: "P7" },
]);
const statusOptions = ref([
  { label: "Critical", value: "Critical" },
  { label: "Major", value: "Major" },
  { label: "Minor", value: "Minor" },
  { label: "Normal", value: "Normal" },
  { label: "Unknown", value: "Unknown" },
  { label: "Warning", value: "Warning" },
  { label: "Calculating", value: "Calculating" },

  { label: "Informational", value: "Informational" },
  { label: "Symptom", value: "Symptom" },
  { label: "Monitoring", value: "Monitoring" },
]);
const valueList = ref<Record<string, string>>({
  Critical: "Critical",
  Major: "Major",
  Minor: "Minor",
  Normal: "Normal",
  Unknown: "Unknown",
  Warning: "Warning",
  Informational: "Informational",
  Calculating: "Calculating",
  Symptom: "Symptom",
  Monitoring: "Monitoring",
});
const valueList1 = ref<Record<string, string>>({
  true: "是",
  false: "否",
});
const deviceTypeList = ref<Record<string, string>>({
  High: "High",
  Medium: "Medium",
  Low: "Low",
  Normal: "None",
  Unknown: "Unknown",
});
const mappingList = ref<Record<string, any>[]>([]);
const expands = ref<string[]>([]);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  getList();
  getPriorityList();
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
//新增编辑
function PriorityMatrixDialog(type, row?) {
  try {
    ctx.refs.eventPriorityRef.type = type;
    if (type === "add") {
      ctx.refs.eventPriorityRef.title = "新增优先级矩阵";
    } else {
      ctx.refs.eventPriorityRef.form = {
        name: row.name,
        desc: row.desc,
        id: row.id,
        defaultMatrix: row.defaultMatrix,
        containerId: row.containerId,
      };
      ctx.refs.eventPriorityRef.title = "编辑优先级矩阵";
    }
    eventPriorityialog.value = true;
  } catch (error) {
    /*  */
  } finally {
    /*  */
  }
}
function formatterTable(_row, _col, v) {
  switch (_col.property) {
    default:
      return v || "--";
  }
}
function getRowKeys(row) {
  return row.id;
}
async function handleExpandChange(row, expandedRows) {
  if (expandedRows.length) {
    //展开
    expands.value = [];
    if (row) {
      expands.value.push(row.id);
    }
  } else {
    expands.value = [];
  }
}
//关闭弹框
function dialogClose(bool) {
  eventPriorityialog.value = bool;
  getPriorityList();
}
const delTitle = ref("");
//删除优先级矩阵
function delLevel() {
  delTitle.value = `${i18n.t("eventPriority.ConfirmDeleteCurrentContent")}`;
}
function delConfirm(row) {
  deletePriorityMatrix({ id: row.id })
    .then((res) => {
      if (res.success) {
        ElMessage.success("删除成功");
        getPriorityList();
      }
    })
    .catch((err) => {
      ElMessage.error(err.message);
      getPriorityList();
    });
}

function priorityChange(row: { [x: string]: any }, thing: string | number, device: any) {
  let obj = {
    influence: device,
    urgency: thing,
    priority: row[thing],
  };

  editPriorityMatrix({ priorityMatrixItems: [obj] })
    .then((res) => {
      if (!res.success) throw new Error(res.message);
      ElMessage.success("操作成功");
      getPriorityList();
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
  // // console.log(obj);
}
//查询列表数据
function getPriorityList /* 获取事件优先级矩阵列表 */() {
  tableloading.value = true;
  getNewPriorityMatrixList2({
    ...prioritySearchForm.value,
    containerId: (userInfo as any).currentTenant.containerId,
    queryPermissionId: "515413099910529024",
  })
    .then((res) => {
      if (!res.success) throw new Error(res.message);
      tableloading.value = false;
      priorityListData.value = [...res.data];
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
}

function getList /* 获取映射关系列表 */() {
  getMointerSourceMappingList({})
    .then((res) => {
      if (!res.success) throw new Error(res.message);
      mappingList.value = [...res.data];
      console.log(mappingList.value, "9999");
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
}
function updateMointerSourceMapping(id: any, mappings: any) {
  let obj = {
    sourceId: id,
    mappings: [mappings],
  };
  // console.log(obj);
  editMointerSourceMapping(obj)
    .then((res) => {
      if (!res.success) throw new Error(res.message);

      ElMessage.success("状态修改成功");
      getList();
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
}
function handleCommand(val: ReturnType<typeof beforeHandleCommand>) {
  if (val.i != "alarm" && val.i != "device") {
    if (val.type == "2") {
      mappingList.value[val.i].mappings[val.index].autoEvent = val.command.value1;
    } else {
      mappingList.value[val.i].mappings[val.index].eventSeverity = val.command.value;
    }

    updateMointerSourceMapping(mappingList.value[val.i].sourceId, mappingList.value[val.i].mappings[val.index]);
  } else {
    if (val.i === "alarm") {
      console.log(val);
      editPriorityMatrix({
        alertOrderMappingItems: [
          {
            severity: alertOrderMappingItems.value[val.index].severity,
            urgency: val.command,
          },
        ],
      })
        .then((res) => {
          if (!res.success) throw new Error(res.message);
          ElMessage.success("操作成功");
          getPriorityList();
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        });
    } else {
      editPriorityMatrix({
        deviceOrderMappingItems: [
          {
            importance: deviceOrderMappingItems.value[val.index].importance,
            influence: val.command,
          },
        ],
      })
        .then((res) => {
          if (!res.success) throw new Error(res.message);
          ElMessage.success("操作成功");
          getPriorityList();
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        });
    }
  }
}
function beforeHandleCommand(command: { label: string; value: string; value1: string }, index: number, i: number, type: string) {
  return {
    command: command,
    index: index,
    i: i,
    type: type,
  };
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.event_priority_center {
  > div {
    flex: 1;
    flex-direction: column;
    h3 {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 4px 12px;
      box-sizing: border-box;

      height: 40px;

      /* primary/cloudcare/fill/fill5 */

      background: #f7f8fa;
      border-radius: 2px;
      margin-bottom: 10px;
      /* Inside auto layout */

      align-self: stretch;
    }
    h2 {
      color: #409eff;
    }
    :deep(.deviceImportant) {
      .cell {
        height: 32px;
      }
    }
    .alarm-urgent-list {
      height: auto;
      border-left: 1px solid #ebeef5;
      .alarm-urgent-title {
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .alarm-urgent-title:first-child {
        border-top: 1px solid #ebeef5;
        border-right: 1px solid #ebeef5;
      }
      :deep(.el-table) {
        .cell {
          height: 35px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .corresponding {
        border-top: 0;
        span {
          color: #000;
        }
        :deep(.el-table__inner-wrapper::before) {
          background-color: none;
        }
      }
      span {
        color: #fff;
        padding: 2px 15px;
        box-sizing: border-box;
        border-radius: 20px;
      }
      span.Critical {
        background: #db3328;
      }
      span.Major {
        background: #f0ad4e;
      }
      span.Minor {
        background: #e9d310;
      }
      span.Warning {
        background: #31b0d5;
      }
      span.Normal {
        background: #5cb85c;
      }
      span.Calculating {
        background: #bd9fd9;
      }
    }
  }
  :deep(.el-table) {
    .el-table__border-left-patch {
      display: none;
    }
  }
  .matrix {
    ::deep(.el-table--border::before) {
      width: 0;
    }
  }
  .matrix::before {
    width: 0 !important;
  }
}
ol {
  display: flex;
  flex-wrap: wrap;
  background: #f7f8fa;
  > li {
    flex: 1;
    // width: 25%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    > span {
      padding: 2px 15px;
      box-sizing: border-box;
      border-radius: 20px;
      color: #fff;
    }
    border: 1px solid #e5e6eb;
  }
}
.alarm-status {
  display: flex;
  flex-wrap: wrap;
  > li {
    flex: 1;
    // width: 25%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    > span {
      padding: 2px 15px;
      box-sizing: border-box;
      border-radius: 20px;
      color: #fff;
    }
    border: 1px solid #e5e6eb;
  }
  > li {
    span {
      background: #d3d3d3;
    }
  }
  // > li:nth-child(-n + 5) {
  //   // border: 1px solid #E5E6EB;
  //   border-left: 1px solid #e5e6eb;
  //   border-top: 1px solid #e5e6eb;
  // }
  // > li:nth-child(n + 6) {
  //   border-left: 1px solid #e5e6eb;
  //   border-top: 1px solid #e5e6eb;
  //   border-bottom: 1px solid #e5e6eb;
  //   background: #f7f8fa;
  // }
  // > li:nth-child(5) {
  //   border-right: 1px solid #e5e6eb;
  // }
  // > li:nth-child(10) {
  //   border-right: 1px solid #e5e6eb;
  // }

  li.Critical {
    span {
      background: #db3328;
    }
  }
  li.Major {
    span {
      background: #f0ad4e;
    }
  }
  li.Minor {
    span {
      background: #e9d310;
    }
  }
  li.Warning {
    span {
      background: #31b0d5;
      // background: #bd9fd9;
    }
  }
  li.Normal {
    span {
      background: #5cb85c;
    }
  }
  li.Calculating {
    span {
      background: #bd9fd9;
    }
  }
  li {
    span {
      background: #d3d3d3;
    }
  }
}
</style>
