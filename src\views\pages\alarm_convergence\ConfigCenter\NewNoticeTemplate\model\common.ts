export enum subjectKeys {
  "ORDER_TYPE" = "工单类型",
  "ORDER_NUMBER" = "工单编号",
  "ORDER_SUMMARY" = "工单摘要",
  "ORDER_STATE" = "工单状态",
  "TRIGGERING_TIME" = "触发时间",
}
export enum contentKeys {
  // "CUSTOMER_NAME" = "客户名称",
  // "ORDER_TYPE" = "工单类型",
  // "ORDER_NUMBER" = "工单编号",
  // "ORDER_SUMMARY" = "工单摘要",
  // "ORDER_DESCRIPTION" = "工单描述",
  // "ORDER_STATE" = "工单状态",
  // "ORDER_PRIORITY" = "工单优先级",
  // "LINE_NUMBER" = "线路编号",
  // "ALARM_SUMMARY" = "告警摘要",
  // "TRIGGERING_TIME" = "触发时间",
  "客户名称:{{CUSTOMER_NAME}}" = "客户名称",
  "工单类型:{{ORDER_TYPE}}" = "工单类型",
  "工单编号:{{ORDER_NUMBER}}" = "工单编号",
  "工单摘要:{{ORDER_SUMMARY}}" = "工单摘要",
  "工单描述:{{ORDER_DESCRIPTION}}" = "工单描述",
  "工单状态:{{ORDER_STATE}}" = "工单状态",
  "工单优先级:{{ORDER_PRIORITY}}" = "工单优先级",
  "线路编号:{{LINE_NUMBER}}" = "线路编号",
  "告警摘要:{{ALARM_SUMMARY}}" = "告警摘要",
  "触发时间:{{TRIGGERING_TIME}}" = "触发时间",
}
export enum messageFields {
  "ORDER_TYPE" = "工单类型",
  "ORDER_NUMBER" = "工单编号",
  "ORDER_DIGEST" = "工单摘要",
  "ORDER_STATE" = "工单状态",
  "ORDER_PRIORITY" = "工单等级",
  "LINE_NUMBER" = "线路编号",
  "TRIGGER_TIME" = "触发时间",
}
