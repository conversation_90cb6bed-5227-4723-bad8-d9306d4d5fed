<template>
  <div style="border: 1px solid #ccc; margin-top: 10px; z-index: 999">
    <!-- 工具栏 -->
    <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig" />
    <!-- 编辑器 -->
    <Editor style="min-height: 180px; overflow-y: hidden" :defaultConfig="editorConfig" v-model="internalValue" @onCreated="onCreated" />
    <!-- @onChange="onChange" -->
  </div>
</template>

<script>
import { ElMessage } from "element-plus";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
export default {
  components: { Editor, Toolbar },
  props: {
    passSon: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      editor: null,
      internalValue: this.passSon,
      toolbarConfig: {
        /* 显示哪些菜单，如何排序、分组 */
        toolbarKeys: ["blockquote", "header1", "header2", "header3", "|", "bold", "underline", "italic", "through", "color", "bgColor", "clearStyle", "|", "bulletedList", "numberedList", "todo", "justifyLeft", "justifyRight", "justifyCenter", "|", "insertLink", "insertTable", "codeBlock", "|", "undo", "redo", "|"],

        /* 隐藏哪些菜单 */
        excludeKeys: [],
      },
      editorConfig: {
        placeholder: "点击全屏介绍产品吧...",
        // autoFocus: false,
        // 所有的菜单配置，都要在 MENU_CONF 属性下
        MENU_CONF: {
          // 配置字号
          // fontSize: [... ],
        },
      },
    };
  },
  watch: {
    // internalValue(newValue) {
    //   // 监听子组件内部数据的变化，并通过$emit传递给父组件
    //   this.$emit("Editor", newValue);
    // },
    passSon(val) {
      this.internalValue = val;
      this.$emit("update:content", val);
      this.$emit("Editor", val);
    },
    internalValue(newValue) {
      this.$emit("update:content", newValue);
      this.$emit("Editor", newValue);
      // 监听子组件内部数据的变化，并通过$emit传递给父组件
    },
  },
  mounted() {},

  beforeUnmount() {
    const editor = this.editor;
    if (editor == null) return;
    editor.destroy(); // 组件销毁时，及时销毁 editor ，重要！！！
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor); // 【注意】一定要用 Object.seal() 否则会报错
    },

    // onChange(editor) {
    //   const text = editor.getHtml();
    //   this.$emit("update:content", text);
    //   console.log("text :>> ", text);
    // },
  },
};
</script>

<style src="@wangeditor/editor/dist/css/style.css"></style>
