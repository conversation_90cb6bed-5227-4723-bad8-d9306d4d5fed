<template>
  <el-scrollbar class="device-list">
    <el-card :body-style="{ padding: '20px' }">
      <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40 - 50 - 20" @size-change="handleRefreshTable()" @current-change="handleRefreshTable()">
        <template #right>
          <span class="tw-ml-[12px] tw-h-fit">
            <el-button v-if="userInfo.hasPermission('612175394593832960')" type="primary" :icon="Plus" @click="handleCommand(command.Create, {})">{{ $t("glob.add") }}{{ props.title }}</el-button>
          </span>
        </template>
        <template #default="{ height: tableHeight }">
          <el-table :data="tableData" :height="tableHeight" :expand-row-keys="expands" :row-key="getRowKeys" :default-expand-all="false" stripe style="width: 100%" @expand-change="handleExpandChange">
            <el-table-column type="expand">
              <template #default="{ row, column, $index }">
                <el-form v-if="row.permissions.includes('612175428227956736')" ref="form" :model="form" label-width="120px" :rules="rules">
                  <el-form-item label="短信内容" prop="messageFields">
                    <div class="tw-w-full">
                      <el-checkbox v-model="item.state" v-for="item in messageFields" :key="`messageFields-${item.value}`" :true-label="item.value" :false-label="`cancel:${item.value}`" :label="item.value" @change="handleAddField">{{ item.label }}</el-checkbox>
                      <br />
                      <el-input type="textarea" ref="inputRef" :autosize="{ minRows: 4, maxRows: 8 }" placeholder="请选择短信内容" v-model="form.messageText" @blur="inputBlur"> </el-input>
                    </div>
                  </el-form-item>
                  <el-row>
                    <el-col :style="{ textAlign: 'right', marginTop: '10px' }">
                      <el-button type="primary" @click="submitForm">保 存</el-button>
                    </el-col>
                  </el-row>
                </el-form>
                <el-empty v-else :description="$t('glob.noPower')" class="tw-h-full" />
              </template>
            </el-table-column>
            <TableColumn type="condition" :prop="`name`" :label="`模版名称`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="handleQuery()"></TableColumn>

            <TableColumn type="condition" :prop="`desc`" :label="`描述`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByDescription" :filters="$filter0" @filter-change="handleQuery()"></TableColumn>

            <TableColumn type="enum" :prop="`active`" :label="`激活`" :min-width="100" :showOverflowTooltip="true" show-filter v-model:filtered-value="search.active" :filters="['true', 'false'].map((v) => ({ value: v, text: v === 'true' ? '√' : '×' }))" @filter-change="handleQuery()">
              <template #default="{ row }">
                <div style="font-size: 18">{{ row.active ? "√" : "×" }}</div>
              </template>
            </TableColumn>

            <el-table-column label="操作" prop="">
              <template #default="{ row }">
                <span>
                  <el-link v-if="row.permissions.includes('612175428227956736')" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleCommand(command.Update, row)">{{ $t("glob.edit") }}</el-link>
                </span>
                <span>
                  <el-link v-if="row.permissions.includes('612175443923042304')" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleCommand(command.Delete, row)">{{ $t("glob.delete") }}</el-link>
                </span>
                <span v-if="row.permissions.includes('612175462654803968')">
                  <!-- 短信模板('612175151722659840') -->
                  <el-link :type="row.permissions.includes('612175462654803968') ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="row.permissions.includes('612175462654803968') ? false : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
                </span>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </pageTemplate>
    </el-card>
    <smsEditor ref="addGroup" @custom-event="handleRefreshTable" />
    <el-dialog v-model="dialogVisibleshow" title="查看安全目录" width="500" :before-close="handleClose">
      <treeAuth :proptreeId="containerId" :treeStyle="treeStyle" ref="treeAuthRef"></treeAuth>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisibleshow = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </el-scrollbar>
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { ref, reactive, readonly, computed, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch } from "vue";
import getUserInfo from "@/utils/getUserInfo";
import smsEditor from "./smsEditor.vue";
import pageTemplate from "@/components/pageTemplate.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getGroup } from "@/api/personnel";
import treeAuth from "@/components/treeAuth/index.vue";
import { recipientsEnum, noteOperation, messageFields, operation, makeacopyfor } from "./common";
import { getSmsList, delSmsTemplates, getSmsDetails, customSmsContent } from "@/views/pages/apis/NewNoticeTemplate";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";
import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";

export default {
  components: {
    smsEditor,
    pageTemplate,
    treeAuth,
    TableColumn,
  },
  inject: ["height"],
  data() {
    return {
      // 安全容器
      dialogVisibleshow: false,
      treeStyle: {
        pointerEvents: "none",
      },
      containerId: "",
      paging: {
        pageNumber: 1,
        pageSize: 50,
        total: 0,
      },
      tableData: [],
      userInfo: getUserInfo(),
      props: { title: "短信模版" },
      command: {
        Create: "Create",
        Update: "Update",
        Delete: "Delete",
        Safety: "Safety",
      },
      expands: [],
      //短信模版内容
      textCursor: 0,
      messageFields,
      form: {
        messageFields: [],
        messageText: "",
      },
      smsId: "",
      Security,

      // $filter0: [
      //   { text: "包含", value: "include" },
      //   { text: "不包含", value: "exclude" },
      //   { text: "等于", value: "eq" },
      //   { text: "不等于", value: "ne" },
      // ],
      $filter0: exoprtMatch1,
      searchType0ByName: "include",
      searchType1ByName: "include",

      searchType0ByDescription: "include",
      searchType1ByDescription: "include",

      search: {
        eqName: [] /* 等于的短信模板名称 */,
        includeName: [] /* 包含的短信模板名称 */,
        nameFilterRelation: "AND" /* 短信模板名称过滤关系(AND,OR) */,
        neName: [] /* 不等于的短信模板名称 */,
        excludeName: [] /* 不包含的短信模板名称 */,

        eqDescription: [] /* 等于的短信模板描述 */,
        includeDescription: [] /* 包含的短信模板描述 */,
        descriptionFilterRelation: "AND" /* 短信模板描述过滤关系(AND,OR) */,
        neDescription: [] /* 不等于的短信模板描述 */,
        excludeDescription: [] /* 不包含的短信模板描述 */,

        active: "",
      },
    };
  },
  computed: {
    searchByName: {
      get: function () {
        let value0 = "";
        if (this.searchType0ByName === "include") value0 = this.search.includeName[0] || "";
        if (this.searchType0ByName === "exclude") value0 = this.search.excludeName[0] || "";
        if (this.searchType0ByName === "eq") value0 = this.search.eqName[0] || "";
        if (this.searchType0ByName === "ne") value0 = this.search.neName[0] || "";
        let value1 = "";
        if (this.searchType1ByName === "include") value1 = this.search.includeName[this.search.includeName.length - 1] || "";
        if (this.searchType1ByName === "exclude") value1 = this.search.excludeName[this.search.excludeName.length - 1] || "";
        if (this.searchType1ByName === "eq") value1 = this.search.eqName[this.search.eqName.length - 1] || "";
        if (this.searchType1ByName === "ne") value1 = this.search.neName[this.search.neName.length - 1] || "";
        return {
          type0: this.searchType0ByName,
          type1: this.searchType1ByName,
          relation: this.search.nameFilterRelation,
          value0,
          value1,
          input0: "",
          // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
          input1: "",
          // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
        };
      },
      set: function (v) {
        this.searchType0ByName = v.type0;
        this.searchType1ByName = v.type1;
        this.search.nameFilterRelation = v.relation;
        this.search.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
        this.search.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
        this.search.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
        this.search.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
      },
    },

    searchByDescription: {
      get: function () {
        let value0 = "";
        if (this.searchType0ByDescription === "include") value0 = this.search.includeDescription[0] || "";
        if (this.searchType0ByDescription === "exclude") value0 = this.search.excludeDescription[0] || "";
        if (this.searchType0ByDescription === "eq") value0 = this.search.eqDescription[0] || "";
        if (this.searchType0ByDescription === "ne") value0 = this.search.neDescription[0] || "";
        let value1 = "";
        if (this.searchType1ByDescription === "include") value1 = this.search.includeDescription[this.search.includeDescription.length - 1] || "";
        if (this.searchType1ByDescription === "exclude") value1 = this.search.excludeDescription[this.search.excludeDescription.length - 1] || "";
        if (this.searchType1ByDescription === "eq") value1 = this.search.eqDescription[this.search.eqDescription.length - 1] || "";
        if (this.searchType1ByDescription === "ne") value1 = this.search.neDescription[this.search.neDescription.length - 1] || "";
        return {
          type0: this.searchType0ByDescription,
          type1: this.searchType1ByDescription,
          relation: this.search.descriptionFilterRelation,
          value0,
          value1,
          input0: "",
          // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
          input1: "",
          // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
        };
      },
      set: function (v) {
        this.searchType0ByDescription = v.type0;
        this.searchType1ByDescription = v.type1;
        this.search.descriptionFilterRelation = v.relation;
        this.search.includeDescription = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
        this.search.excludeDescription = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
        this.search.eqDescription = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
        this.search.neDescription = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
      },
    },

    rules() {
      return {
        messageFields: [
          {
            required: true,
            validator: (rule, value, callback) => {
              const flag = [];
              this.messageFields.forEach((v) => {
                if (v.state && !v.state.includes("cancel:")) {
                  flag.push(v.value);
                }
              });
              if (!flag || !flag.length) callback(new Error("短信主题必选"));
              else if (!this.form.messageText) callback(new Error("请输入模版内容"));
              else callback();
            },
            trigger: "change",
          },
        ],
      };
    },
  },
  created() {},
  mounted() {
    this.handleRefreshTable();
  },
  methods: {
    handleCommand(type, row) {
      switch (type) {
        case this.command.Create:
          this.$refs.addGroup.open(row);
          break;
        case this.command.Update:
          this.$refs.addGroup.open(row);
          break;
        case this.command.Delete:
          ElMessageBox.confirm(`确定删除短信模版："${row.name}"?`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(async () => {
              try {
                const { success, data, message } = await delSmsTemplates({ id: row.id });
                if (!success) throw new Error(message);
                ElMessage.success("操作成功");
                this.handleRefreshTable();
              } catch (error) {
                error instanceof Error && ElMessage.error(error.message);
              }
            })
            .catch((err) => {});
          break;
        case this.command.Safety:
          this.containerId = row.containerId;
          this.dialogVisibleshow = true;
          break;
      }
    },
    // eslint-disable-next-line no-undef
    getRowKeys(row) {
      return row.id;
    },
    async handleExpandChange(row, expandedRows) {
      if (expandedRows.length) {
        //展开
        this.expands = []; //先干掉之前展开的行
        if (row) {
          this.expands.push(row.id); //push新的行 (原理有点类似防抖)
        }
      } else {
        this.expands = []; //折叠 就清空expand-row-keys对应的数组
      }
      this.smsId = row.id;
      this.form.messageText = "";
      this.messageFields = this.setSubjectKeysNot();
      await this.getDetails(this.smsId);
    },

    //获取短信详情
    async getDetails(smsId) {
      getSmsDetails({ id: smsId }).then(({ success, data, total }) => {
        console.log("data :>> ", data);
        if (success) {
          let form = {
            id: data.id,
            messageText: data.messageText,
          };
          this.form.messageText = form.messageText;
          this.messageFields = this.setSubjectKeys();
          data.messageFields.forEach((v) => {
            const enumItem = this.messageFields.find((item) => item.value === v);
            enumItem.state = enumItem.state.substring(enumItem.state.indexOf(":") + 1);
          });
        }
      });
    },

    async handleQuery() {
      this.paging.pageNumber = 1;
      await nextTick();
      this.handleRefreshTable();
    },

    // 获取短信模版列表
    handleRefreshTable() {
      const params = {
        ...this.search,
        pageNumber: this.paging.pageNumber,
        pageSize: this.paging.pageSize,
        containerId: this.userInfo.currentTenant.containerId,
        queryPermissionId: "612175414051209216",
        verifyPermissionIds: "612175394593832960,612175428227956736,612175443923042304,612175462654803968",
      };
      getSmsList(params).then(({ success, data, total }) => {
        if (success) {
          this.paging.total = Number(total);
          this.tableData = data;
          if (!this.tableData.length && this.paging.pageNumber !== 1) {
            this.paging.pageNumber = 1;
            this.handleRefreshTable();
          }
        } else console.error(JSON.parse(data)?.message || data);
      });
    },
    // 安全容器
    showSecurityTree,
    // 短信模版内容处理方法
    getParams() {
      const params = {
        id: this.smsId,
        messageFields: [],
        messageText: this.form.messageText,
      };
      this.messageFields.forEach((v) => {
        if (v.state && !v.state.includes("cancel:")) {
          params.messageFields.push(v.value);
        }
      });

      if (this.isEdit) params.id = this.form.id;
      return params;
    },
    submitForm() {
      this.$refs["form"].validate(async (valid) => {
        if (!valid) return false;
        try {
          const { success, message } = await customSmsContent(this.getParams());
          if (!success) throw new Error(message);
          this.$message.success(`操作成功!`);
          this.$refs.form.resetFields();
          this.$refs.form.clearValidate();
          this.handleClose();
        } catch (error) {
          error instanceof Error && this.$message.success(error.message);
        }
      });
    },
    inputBlur() {
      this.textCursor = this.$refs.inputRef.$el.children[0].selectionStart;
    },
    handleAddField(v) {
      if (!v.includes("cancel:")) {
        this.form.messageText = this.form.messageText.slice(0, this.textCursor) + `{{${v}}}` + this.form.messageText.slice(this.textCursor);
      } else {
        String.prototype.replaceAll = function (f, e) {
          var reg = new RegExp(f, "g");
          return this.replace(reg, e);
        };
        this.form.messageText = this.form.messageText.replaceAll(`{{${v.substring(v.indexOf(":") + 1)}}}`, ``);
        String.prototype.replaceAll = null;
      }
    },
    setSubjectKeysNot() {
      let result = [];
      for (let key in messageFields) {
        result.push({
          label: messageFields[key],
          value: key,
          state: `cancel:${key}`,
        });
      }
      return result;
    },
    setSubjectKeys() {
      if (this.messageFields instanceof Array) return this.messageFields;
      let result = [];
      for (let key in this.messageFields) {
        result.push({
          label: messageFields[key],
          value: key,
          state: this.isEdit ? (this.form.messageText.includes(`{{${key}}}`) ? key : `cancel:${key}`) : `cancel:${key}`,
        });
      }
      return result;
    },
    /* 返回上一页 */
    handleClose(done) {
      this.$emit("confirm");
      if (done instanceof Function) done();
      else this.drawer = false;
    },
  },
};
</script>

<style scoped lang="scss">
.box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.box-table {
  padding: 10px 20px;
}

.select_item {
  min-height: 30px !important;
  height: 100px !important;
  font-size: 12px;
}
.select_item_once {
  height: 60px !important;
}
</style>
