// 联系人
export enum recipientsEnum {
  "NOTIFY_CONTACT" = "通知联系人",
  "FIELD_CONTACT" = "现场联系人",
  "TECHNICAL_CONTACT" = "技术联系人",
}

export enum noteOperation {
  "CREATE" = "新增小记",
  "UPDATE" = "编辑小记",
  "DELETE" = "删除小记",
}

export enum subjectKeys {
  "CUSTOMER_NAME" = "客户名称",
  "TRIGGERING_TIME" = "触发时间",
  "EVENT_NUMBER" = "事件编号",
  "EVENT_SUMMARY" = "事件摘要",
  "ALARM_NUMBER" = "告警数量",
}

export enum operation {
  "TAKE_OVER" = "接手",
  "DELIVER_TO" = "转交",
  "FINISHED" = "完成",
  "UPGRADE" = "升级",
  "HOLD_ON" = "挂起",
}
