<template>
  <div>
    <template #header>
      <el-page-header @back="handleBackRouter" :content="`${isEdit ? '编辑' : '新增'}邮件模板`"></el-page-header>
    </template>
    <el-form ref="form" :model="form" label-width="120px" :rules="rules">
      <el-card class="el-card-mt">
        <template #header>
          <div class="modules-item">
            <span class="modules-title">基础信息</span>
          </div>
        </template>
        <el-row>
          <el-col :span="24">
            <el-form-item label="模板名称" prop="name">
              <el-input v-model="form.name" :style="basicClassInput" placeholder="请输入邮件模板名称"></el-input>
            </el-form-item>
            <el-form-item label="模板描述" prop="desc">
              <el-input v-model="form.desc" type="textarea" :autosize="{ minRows: 4, maxRows: 8 }" placeholder="请输入邮件模板描述" :style="basicClassInput"> </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="el-card-mt">
        <template #header>
          <div class="modules-item">
            <span class="modules-title">内容配置</span>
          </div>
        </template>
        <el-row>
          <el-col :span="24">
            <el-form-item label="收件人" prop="recipients">
              <el-checkbox-group v-model="form.recipients">
                <el-checkbox v-for="(value, key) in recipientsEnum" :key="`recipient-${key}`" :label="key">{{ value }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="抄送人" prop="carbonCopies">
              <el-checkbox-group v-model="form.carbonCopies">
                <el-checkbox v-for="(value, key) in recipientsEnum" :key="`recipient-${key}`" :label="key">{{ value }}</el-checkbox>
              </el-checkbox-group>
              <el-input v-model="form.cc" placeholder="本邮箱将抄送每一封邮件，用于验证邮件是否发送成功，多个邮箱用;分隔" :style="basicClassInput" />
            </el-form-item>
            <el-form-item label="触发条件">
              <el-checkbox v-model="form.generateEvent">生成事件</el-checkbox>
              <el-checkbox v-model="form.handleEvent">处理事件</el-checkbox>
              <el-row :gutter="20" v-if="form.handleEvent">
                <el-col :span="6">
                  <el-cascader v-model="form.eventOperations" :options="priorityEnum" :props="{ multiple: true }" :collapse-tags="false" :style="{ width: '100%' }"></el-cascader>
                </el-col>
                <el-col :span="6">
                  <el-cascader v-model="form.noteOperations" :options="noteOperation" :props="{ multiple: true }" :collapse-tags="false" :style="{ width: '100%' }"></el-cascader>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="邮件主题" prop="subjectKeys">
              <el-checkbox v-model="item.state" v-for="item in subjectKeys" :key="`subjectKeys-${item.value}`" :true-label="item.value" :false-label="`cancel:${item.value}`" :label="item.value" @change="handleAddField">{{ item.label }}</el-checkbox>
              <br />
              <el-input type="textarea" ref="inputRef" :autosize="{ minRows: 4, maxRows: 8 }" placeholder="请输入内容" v-model="form.subjectText" :style="basicClassInput" @blur="inputBlur"> </el-input>
            </el-form-item>
            <el-form-item label="邮件模板" prop="templateUrl">
              <el-upload class="upload-demo" drag action :auto-upload="false" multiple :limit="1" :on-change="handleFileChange" :file-list="fileList">
                <el-icon><el-icon-upload /></el-icon>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :style="{ textAlign: 'center', marginTop: '10px' }">
            <el-button type="primary" @click="submitForm">提 交</el-button>
            <el-button @click="handleBackRouter">取 消</el-button>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
  </div>
</template>

<script>
import { Upload as ElIconUpload } from "@element-plus/icons";
import { recipientsEnum, noteOperation, subjectKeys, operation } from "./js/common";
import priorityEnum from "@/views/pages/common/priority";
import { createEmailTemplate, getEmailTemplateInfo, editEmailTemplate, uploadFile } from "@/views/pages/apis/NoticeTemplate";
export default {
  components: {
    ElIconUpload,
  },
  data() {
    return {
      recipientsEnum,
      subjectKeys,
      priorityEnum: [],
      noteOperation: [],
      basicClassInput: { width: "35.8vw" } /* 输入框选择器基本样式 */,
      form: {
        recipients: [],
        eventOperations: [],
        noteOperations: [],
        subjectKeys: [],
        carbonCopies: [],
        subjectText: "",
        generateEvent: false,
      },
      options: [],
      eventOperationsProp: {},
      textCursor: 0,
      currentFile: [],
      fileList: [],
    };
  },
  computed: {
    isEdit() {
      return route.path.includes("edit");
    },
    rules() {
      return {
        name: [
          {
            required: true,
            message: "请输入邮件模板名称",
            trigger: ["blur", "change"],
          },
        ],
        recipients: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (this.form.recipients && this.form.recipients.length) callback();
              else callback(new Error("收件人必选"));
            },
            trigger: ["blur", "change"],
          },
        ],
        // carbonCopies: [
        //   {
        //     required: true,
        //     validator: (rule, value, callback) => {
        //       if (this.form.carbonCopies && this.form.carbonCopies.length) callback();
        //       else callback(new Error("抄送人必选"));
        //     },
        //     trigger: ["blur", "change"],
        //   },
        // ],
        subjectKeys: [
          {
            required: true,
            validator: (rule, value, callback) => {
              const flag = [];
              this.subjectKeys.forEach((v) => {
                if (v.state && !v.state.includes("cancel:")) {
                  flag.push(v.value);
                }
              });
              if (!flag || !flag.length) callback(new Error("邮件主题必选"));
              else if (!this.form.subjectText) callback(new Error("请输入模板内容"));
              else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        templateUrl: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (this.currentFile.keyName) callback();
              else callback(new Error("请上传邮件模板"));
            },
            trigger: ["blur", "change"],
          },
        ],
      };
    },
  },
  created() {
    this.priorityEnum = this.setEventOperations();
    this.noteOperation = this.setNoteOperation();
    this.subjectKeys = this.setSubjectKeys();
    if (this.isEdit) {
      this.$nextTick(() => {
        this.getDetail();
      });
    }
  },
  methods: {
    getDetail() {
      getEmailTemplateInfo({ id: route.params.mailTemplateId }).then(({ success, data }) => {
        if (success) {
          let form = {
            id: data.id,
            name: data.name,
            desc: data.desc,
            recipients: data.recipients, // 收件人
            carbonCopies: data.carbonCopies, // 抄送人
            generateEvent: data.generateEvent, // 生成事件
            subjectText: data.subjectText,
            cc: data.cc,
          };
          this.subjectKeys = this.setSubjectKeys();
          if ((data.eventOperations && data.eventOperations.length) || (data.noteOperations && data.noteOperations.length)) form.handleEvent = true; // 处理事件
          form.eventOperations = data.eventOperations.map((v) => [v.priority, v.operation]);
          form.noteOperations = data.noteOperations.map((v) => [v.priority, v.operation]);
          data.subjectKeys.forEach((v) => {
            const enumItem = this.subjectKeys.find((item) => item.value === v);
            enumItem.state = enumItem.state.substring(enumItem.state.indexOf(":") + 1);
          });
          this.fileList = [
            {
              name: data.templateUrl.keyName,
              url: `/${data.templateUrl.bucketName}/${data.templateUrl.keyName}`,
            },
          ];
          this.currentFile = data.templateUrl;
          this.form = form;
        } else ElMessage.error(JSON.parse(data)?.message || "邮件模板详情获取失败");
      });
    },
    getParams() {
      const params = {
        name: this.form.name,
        desc: this.form.desc,
        recipients: this.form.recipients,
        carbonCopies: this.form.carbonCopies,
        generateEvent: this.form.generateEvent,
        subjectKeys: [],
        subjectText: this.form.subjectText,
        templateUrl: this.currentFile,
        cc: this.form.cc,
      };
      if (this.form.handleEvent) {
        params.eventOperations = this.form.eventOperations.map((v) => {
          return { priority: v[0], operation: v[1] };
        });
        params.noteOperations = this.form.noteOperations.map((v) => {
          return { priority: v[0], operation: v[1] };
        });
      }
      this.subjectKeys.forEach((v) => {
        if (v.state && !v.state.includes("cancel:")) {
          params.subjectKeys.push(v.value);
        }
      });

      if (this.isEdit) params.id = this.form.id;
      return params;
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) return false;
        const api = { createEmailTemplate, editEmailTemplate };
        api[this.isEdit ? "editEmailTemplate" : "createEmailTemplate"](this.getParams()).then(({ success, data }) => {
          if (success) {
            ElMessage.success(`邮件模板${this.isEdit ? "编辑" : "创建"}成功!`);
            this.handleBackRouter();
          } else ElMessage.error(JSON.parse(data)?.message || `邮件模板${this.isEdit ? "编辑" : "创建"}失败`);
        });
      });
    },
    handleFileChange(file) {
      let formData = new FormData();
      formData.append("file", file.raw);
      uploadFile(formData).then(({ success, data }) => {
        // console.log(data);
        if (success) {
          ElMessage.success("模板上传成功");
          this.currentFile = data;
        } else {
          ElMessage.error(JSON.parse(data)?.message || "模板上传失败");
          this.currentFile = {};
          this.fileList = [];
        }
      });
    },
    inputBlur() {
      this.textCursor = this.$refs.inputRef.$el.children[0].selectionStart;
    },
    handleAddField(v) {
      if (!v.includes("cancel:")) {
        this.form.subjectText = this.form.subjectText.slice(0, this.textCursor) + `{{${v}}}` + this.form.subjectText.slice(this.textCursor);
      } else {
        String.prototype.replaceAll = function (f, e) {
          var reg = new RegExp(f, "g");
          return this.replace(reg, e);
        };
        this.form.subjectText = this.form.subjectText.replaceAll(`{{${v.substring(v.indexOf(":") + 1)}}}`, ``);
        String.prototype.replaceAll = null;
      }
    },
    setSubjectKeys() {
      if (this.subjectKeys instanceof Array) return this.subjectKeys;
      let result = [];
      for (let key in this.subjectKeys) {
        result.push({
          label: subjectKeys[key],
          value: key,
          state: this.form.subjectText.includes(`{{${key}}}`) ? key : `cancel:${key}`,
        });
      }
      // console.log(result);
      return result;
    },
    setNoteOperation() {
      let result = [];
      for (let key in priorityEnum) {
        let obj = {
          value: key,
          label: key,
          children: [],
        };
        for (let key2 in noteOperation) {
          obj.children.push({
            label: noteOperation[key2],
            value: key2,
          });
        }
        result.push(obj);
      }
      return result;
    },
    setEventOperations() {
      let result = [];
      for (let key in priorityEnum) {
        result.push({
          value: key,
          label: key,
          children: Object.keys(operation).map((v) => {
            return { value: v, label: operation[v] };
          }),
        });
      }
      return result;
    },
    handleBackRouter /* 返回上一页 */() {
      router.replace({ path: "/noticeTemplate" });
    },
  },
};
</script>

<style lang="scss" scoped>
:deep() .elstyle-card {
  margin-top: 20px;
}
.modules-item {
  .modules-title {
    color: rgba(32, 128, 255, 1);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}
</style>
