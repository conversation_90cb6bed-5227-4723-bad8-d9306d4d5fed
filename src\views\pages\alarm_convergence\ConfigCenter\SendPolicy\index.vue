<template>
  <el-container :style="{ margin: '0 -16px' }">
    <el-header height="50px">
      <el-card :body-style="{ padding: '0 20px' }">
        <el-menu class="tw-h-[50px]" mode="horizontal" :default-active="active" @select="active = $event">
          <el-menu-item v-if="userInfo.hasPermission('612172715167580160')" index="sms">短信发送策略</el-menu-item>
          <el-menu-item v-if="userInfo.hasPermission('612173058102263808')" index="email">邮件发送策略</el-menu-item>
        </el-menu>
      </el-card>
    </el-header>
    <el-main>
      <el-card :body-style="{ padding: '0px', height: `${height - 66}px`, width: `${width}px` }">
        <template v-if="active === 'sms'">
          <SmsTemplate v-if="userInfo.hasPermission('612172715167580160')" :height="height - 66" :width="width"></SmsTemplate>
          <el-empty v-else :description="$t('glob.noPower')" class="tw-h-full" />
        </template>
        <template v-else-if="active === 'email'">
          <EmailTemplate v-if="userInfo.hasPermission('612173058102263808')" :height="height - 66" :width="width"></EmailTemplate>
          <el-empty v-else :description="$t('glob.noPower')" class="tw-h-full" />
        </template>
        <template v-else>
          <el-empty description="无效发送策略类型" />
        </template>
      </el-card>
    </el-main>
  </el-container>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { ref, reactive, readonly, computed, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch } from "vue";
import getUserInfo from "@/utils/getUserInfo";
import EmailTemplate from "./model/emailTemplate.vue";
import SmsTemplate from "./model/smsTemplate.vue";
export default {
  components: {
    EmailTemplate,
    SmsTemplate,
  },
  data() {
    const userInfo = getUserInfo();
    return {
      userInfo,
      active: userInfo.hasPermission("612172715167580160") ? "sms" : userInfo.hasPermission("612173058102263808") ? "email" : "",
      width: inject("width", ref(0)),
      height: inject("height", ref(0)),
    };
  },
  mounted() {},
  methods: {},
};
</script>
