<template>
  <div class="SubruleAdd">
    <el-dialog :title="addType == 'area' ? '分配区域' : addType == 'device' ? '分配设备' : addType == 'userGroup' ? '分配用户组' : addType == 'user' ? '分配用户' : addType == 'contacts' ? '分配联系人' : '分配场所'" v-model="dialogVisible" append-to-body draggable width="35%" :before-close="handleClose">
      <el-form>
        <el-form-item style="width: 100%">
          <el-row v-if="addType == 'userGroup' || addType == 'user' || addType == 'contacts'" style="width: 100%; margin-bottom: 10px">
            <el-col style="text-align: left" class="bold" :span="4">
              <span style="color: red">*</span>
              客户：
            </el-col>
            <el-col :span="14">
              <el-select @change="changeTenants" v-model="valueT" filterable :placeholder="'请选择客户'">
                <el-option v-for="item in tenantsOptions" :key="item.id" :label="item.name + '[' + item.abbreviation + ']'" :value="item.id">
                  <span>{{ item.name }} [{{ item.abbreviation }}]</span>
                </el-option>
              </el-select>
            </el-col>
          </el-row>
          <el-row style="width: 100%">
            <el-col style="text-align: left" class="bold" :span="4">
              <span style="color: red">*</span>
              {{ title + "：" }}
            </el-col>
            <el-col :span="14">
              <el-select v-if="addType != 'area'" v-model="value" filterable multiple :placeholder="'请选择' + title">
                <el-option class="select_item" v-for="item in checked ? allOptions : options" :key="item.id" :label="item.name" :value="item.id">
                  <template v-if="addType == 'userGroup' || addType == 'user' || addType == 'contacts'">
                    <div style="height: 50px">
                      <p style="color: #8492a6; font-weight: 800; font-size: 12px; height: 35px; display: block">
                        {{ item.name }}
                      </p>
                      <p style="color: #8492a6; font-size: 13px; display: block; margin-top: -10px; height: 20px">{{ item.email }}</p>
                      <p style="color: #8492a6; font-size: 13px; display: block; height: 20px">{{ item.mobilePhone || item.phone }}</p>
                    </div>
                  </template>
                </el-option>
              </el-select>
              <el-cascader style="width: 370px" v-model="areaList" v-if="addType == 'area'" filterable :options="checked ? allOptions : options" :props="{ checkStrictly: true, value: 'id', label: 'name', disabled: 'disabled' }" clearable></el-cascader>
            </el-col>
          </el-row>
          <el-row style="width: 100%" v-if="addType !== 'userGroup' && addType !== 'user' && addType !== 'contacts'">
            <el-col :offset="4">
              <el-checkbox @change="changeCheck" v-model="checked">展示所有{{ addType == "area" ? "区域" : addType == "device" ? "设备" : "场所" }}</el-checkbox>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import getUserInfo from "@/utils/getUserInfo";
export default {
  name: "SlaDownDialog",
  props: {
    addType: {
      type: String,
      default: "",
    },
    options: {
      type: Array,
    },
    allOptions: {
      type: Array,
    },
    tenantsOptions: {
      type: Array,
    },
  },
  emits: ["clearAddType", "confirmMsg", "confirmTenant"],
  data() {
    const userInfo = getUserInfo();
    return {
      title: "",
      dialogVisible: false,
      value: [],
      valueT: userInfo.currentTenant.id,
      checked: false,
      areaList: [],
      userInfo,
    };
  },
  watch: {
    async addType(val) {
      if (!val) return;
      this.valueT = this.userInfo.currentTenant.id;
      this.changeTenants(this.valueT);
      if (val === "contacts") this.changeTenants(this.valueT);
      await this.$nextTick();
      val == "area" ? (this.title = "区域名称") : val == "device" ? (this.title = "设备名称") : val == "userGroup" ? (this.title = "用户组") : val == "user" ? (this.title = "用户") : val == "contacts" ? (this.title = "联系人") : (this.title = "场所名称");
      this.checked = false;
    },
    dialogVisible(val) {
      if (val) this.valueT = this.userInfo.currentTenant.id;
      else this.$emit("clearAddType");
    },
    options(val) {
      // // console.log(val);
    },
  },
  mounted() {},
  methods: {
    changeCheck(val) {
      this.checked = val;
    },
    cancel() {
      this.dialogVisible = false;
    },
    confirm() {
      // // console.log(instanceof this.value);
      if (this.addType === "area") {
        if (this.areaList.length > 0) {
          // console.log(this.areaList[this.areaList.length - 1]);
          this.$emit("confirmMsg", { value: [this.areaList[this.areaList.length - 1]], type: this.addType });
        } else {
          this.$message.error("请选择" + this.title);
        }
      } else {
        if (this.value) {
          this.$emit("confirmMsg", { value: this.value, type: this.addType });
        } else {
          this.$message.error("请选择" + this.title);
        }
      }
    },
    changeTenants(val) {
      this.$emit("confirmTenant", val);
    },
    handleClose(done) {
      done();
      this.dialogVisible = false;
    },
  },
  expose: ["confirm", "cancel", "dialogVisible", "addType", "options", "title", "value", "valueT", "areaList"],
};
</script>
<style lang="scss" scoped>
.select_item {
  height: 80px !important;
  font-size: 12px;
}
</style>
