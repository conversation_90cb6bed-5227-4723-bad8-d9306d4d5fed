// 收件人类型
export enum recipientTypes {
  "NOTIFY_CONTACT" = "通知联系人",
  "FIELD_CONTACT" = "现场联系人",
  "TECHNICAL_CONTACT" = "技术联系人",
}

export enum makeacopyfor {
  "NOTIFY_CONTACT" = "通知联系人",
  "FIELD_CONTACT" = "现场联系人",
  "TECHNICAL_CONTACT" = "技术联系人",
  "APPOINT_CONTACT" = "指定联系人",
}

export enum noteOperation {
  "CREATE" = "新增小记",
  "UPDATE" = "编辑小记",
  "DELETE" = "删除小记",
}

export enum subjectKeys {
  "CUSTOMER_NAME" = "客户名称",
  "TRIGGERING_TIME" = "触发时间",
  "EVENT_NUMBER" = "事件编号",
  "EVENT_SUMMARY" = "事件摘要",
  "ALARM_NUMBER" = "告警数量",
}

export enum messageFields {
  "CUSTOMER_NAME" = "客户名称",
  "TRIGGER_TIME" = "触发时间",
  "ORDER_NUMBER" = "工单编号",
  "ORDER_DIGEST" = "工单摘要",
  "ALERT_NUMBER" = "告警数量",
  "ORDER_STATE" = "工单状态",
  "DEVICE_NAME" = "设备名称",
  "CONTACT_NAME" = "联系人名称",
}

export enum operation {
  "TAKE_OVER" = "新建",
  "DELIVER_TO" = "转交",
  "FINISHED" = "完成",
  "UPGRADE" = "升级",
  "CUSTOMER_SUSPENDED" = "客户挂起",
  "SERVICE_PROVIDER_SUSPENDED" = "供应商挂起",
  "CLOSE" = "关闭",
}
