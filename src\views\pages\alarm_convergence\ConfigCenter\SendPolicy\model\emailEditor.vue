<template>
  <el-dialog :title="`${isEdit ? '编辑' : '新建'}邮件发送策略`" v-model="dialogFormVisible" :before-close="beforeClose">
    <el-form ref="form" :model="form" label-width="80px" :rules="rules">
      <el-row :gutter="24">
        <el-col :span="23">
          <el-form-item label="名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="23">
          <el-form-item label="描述" prop="desc">
            <el-input type="textarea" v-model="form.desc" placeholder="请输入描述"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="默认" prop="defaultable">
            <el-tooltip class="item" effect="dark" content="若已经有默认的发送策略,在创建新的时,会覆盖原有的发送策略。" placement="top-start">
              <el-checkbox v-model="form.defaultable"></el-checkbox>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="激活" prop="active">
            <el-checkbox v-model="form.active"></el-checkbox>
          </el-form-item>
        </el-col>
        <el-col v-show="!isEdit" :span="23">
          <el-form-item label="选择安全目录" prop="containerId" label-width="135px">
            <treeAuth ref="treeAuthRef" :treeStyle="treeStyle"></treeAuth>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="beforeClose">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { ref, reactive, readonly, computed, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch } from "vue";
import { createRegionsTenantCurrent, editRegionsById } from "@/views/pages/apis/regionManage";
import { ElMessage } from "element-plus";
import treeAuth from "@/components/treeAuth/index.vue";
import { addEmailStrategy, editEmailStrategy } from "@/views/pages/apis/SendPolicy";
import { getTenantInfo } from "@/views/pages/apis/tenant";
export default {
  components: { treeAuth },
  emits: ["refresh"],
  // components: { aMap },
  data() {
    return {
      dialogFormVisible: false,
      form: {
        id: "",
        name: "",
        desc: "",
        active: true,
        defaultable: false,
        containerId: "",
      },
      isEdit: false,
      treeStyle: {
        width: "300px",
        height: "150px",
      },
    };
  },
  computed: {
    rules() {
      return {
        name: [
          {
            required: true,
            message: "请输入名称",
            trigger: ["bulr", "change"],
          },
        ],
      };
    },
  },
  mounted() {},
  methods: {
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) return valid;
        const params = {
          id: this.form.id,
          name: this.form.name,
          desc: this.form.desc,
          active: this.form.active,
          defaultable: this.form.defaultable,
          containerId: this.form.id ? this.form.containerId : this.$refs.treeAuthRef.treeItem.id,
        };
        const treeItemId = this.form.containerId || this.$refs.treeAuthRef.treeItem.id;
        if (!treeItemId) {
          ElMessage.error("请选择安全目录");
          return;
        }
        if (!this.form.id) Object.assign(params, { parentId: this.form.parentId });
        (this.form.id ? editEmailStrategy : addEmailStrategy)(params, this.form?.id || null)
          .then(({ success, data }) => {
            if (success) {
              ElMessage.success("操作成功");
              this.$emit("custom-event");
              this.beforeClose();
            } else ElMessage.error(JSON.parse(data)?.message || "操作失败");
          })
          .catch((e) => {
            if (e instanceof Error) ElMessage.error(e.message);
          });
      });
    },
    open(row = {}) {
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.form = {
          id: row.id,
          name: row.name,
          desc: row.desc,
          active: row.active === undefined ? true : row.active,
          defaultable: row.defaultable === undefined ? false : row.defaultable,
          containerId: row.id ? row.containerId : this.$refs.treeAuthRef.treeItem.id,
        };
      });
      this.isEdit = row.id ? true : false;
    },
    setPosition(v) {
      this.form.latitude = v.lat;
      this.form.longitude = v.lng;
    },
    beforeClose(done) {
      this.$refs.form.clearValidate();
      this.$refs.form.resetFields();
      this.$refs.treeAuthRef.getSafeContaine();
      this.$refs.treeAuthRef.treeId = -1;
      this.$refs.treeAuthRef.treeItem.id = false;
      if (done instanceof Function) done();
      else this.dialogFormVisible = false;
    },
  },
  expose: ["open", "beforeClose"],
};
</script>
<style lang="scss" scoped>
.res-dialog {
  :deep(.el-dialog__body) {
    overflow: auto !important;
    height: 400px !important;
  }
}
</style>
