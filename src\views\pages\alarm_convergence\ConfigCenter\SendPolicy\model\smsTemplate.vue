<template>
  <el-scrollbar class="device-list">
    <el-card :body-style="{ padding: '20px' }">
      <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40 - 50" @size-change="handleRefreshTable()" @current-change="handleRefreshTable()">
        <template #right>
          <span class="tw-ml-[12px] tw-h-fit">
            <el-button v-if="userInfo.hasPermission('612172695882170368')" type="primary" :icon="Plus" @click="handleCommand(command.Create, {})">{{ $t("glob.add") }}{{ props.title }}</el-button>
          </span>
        </template>
        <template #default="{ height: tableHeight }">
          <el-table :data="tableData" :height="tableHeight" :expand-row-keys="expands" :row-key="getRowKeys" :default-expand-all="false" stripe style="width: 100%" @expand-change="handleExpandChange">
            <el-table-column type="expand">
              <template #default="{ row }">
                <div v-if="row.permissions.includes('612172734373298176')">
                  <el-form ref="form" :model="form" label-width="120px" :rules="rules" v-loading="defaulloading">
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="收件人类型" prop="recipientTypes">
                          <el-checkbox-group v-model="form.recipientTypes" @change="cascaderChangeG">
                            <el-checkbox v-for="item in receiverTypes" :label="item.value" :key="item.value">{{ item.label }}</el-checkbox>
                          </el-checkbox-group>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="关联短信模版" prop="smsTemplateId">
                          <el-select style="width: 300px" v-model="form.smsTemplateId" placeholder="请选择短信模版">
                            <el-option v-for="item in SMSOption" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="指定联系人" prop="recipients">
                          <el-input v-model="form.recipients" style="width: 300px" type="text" ref="" placeholder="可输入多个手机号，通过分号；分隔" :style="basicClassInput"> </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="关联触发条件" prop="triggerTemplateId">
                          <el-select style="width: 300px" v-model="form.triggerTemplateId" placeholder="请选择触发条件">
                            <el-option v-for="item in TriggersOption" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <!-- <el-col :span="12">
                        <el-form-item label="发送用户组" prop="userGroupIds">
                          <el-select v-if="userInfo.hasPermission('638977805605928960')" style="width: 300px" v-model="form.userGroupIds" multiple placeholder="请选择">
                            <el-option v-for="item in userGroupOption" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                          </el-select>
                          <el-select style="width: 300px" v-else></el-select>
                        </el-form-item>
                      </el-col> -->
                      <!-- <el-col :span="12">
                        <el-form-item label="指定联系人" prop="recipients">
                          <el-input v-model="form.recipients" style="width: 300px" type="text" ref=""
                            placeholder="可输入多个手机号，通过分号；分隔" :style="basicClassInput"> </el-input>
                        </el-form-item>
                      </el-col> -->
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <!-- <el-form-item label="发送用户" prop="userIds">
                          <el-select v-if="userInfo.hasPermission('638977858743566336')" style="width: 300px" v-model="form.userIds" multiple placeholder="请选择">
                            <el-option class="select_item" v-for="item in userOption" :key="item.id" :label="item.name" :value="item.id">
                              <div style="height: 50px">
                                <p style="color: #8492a6; font-weight: 800; font-size: 16px; height: 35px; display: block">
                                  {{ item.name }}
                                </p>
                                <p style="color: #8492a6; font-size: 13px; display: block; margin-top: -10px; height: 20px">账号:{{ item.account }}</p>
                                <p style="color: #8492a6; font-size: 13px; display: block; height: 20px">邮箱:{{ item.email }}</p>
                                <p style="color: #8492a6; font-size: 13px; display: block; height: 20px">
                                  {{ item.name }}
                                </p>
                              </div>
                            </el-option>
                          </el-select>
                          <el-select style="width: 300px" v-else></el-select>
                        </el-form-item> -->
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :style="{ textAlign: 'center', marginTop: '10px' }">
                        <el-button type="primary" @click="submitForm">保 存</el-button>
                      </el-col>
                    </el-row>
                    <strategySms :detail="row" :width="width - 40" @confirm="update"> </strategySms>
                  </el-form>
                </div>
                <el-empty v-else :description="$t('glob.noPower')" class="tw-h-full" />
              </template>
            </el-table-column>
            <TableColumn type="condition" :prop="`name`" :label="`策略名称`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="handleQuery()"></TableColumn>

            <TableColumn type="condition" :prop="`desc`" :label="`描述`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByDescription" :filters="$filter0" @filter-change="handleQuery()"></TableColumn>

            <TableColumn type="condition" :prop="`templateName`" :label="`短信模版`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByTemplate" :filters="$filter0" @filter-change="handleQuery()"></TableColumn>

            <TableColumn type="condition" :prop="`triggerTemplateName`" :label="`触发条件`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByTrigger" :filters="$filter0" @filter-change="handleQuery()"></TableColumn>

            <TableColumn type="enum" :prop="`defaultable`" :label="`默认`" :min-width="100" :showOverflowTooltip="true" show-filter v-model:filtered-value="search.defaultable" :filters="['true', 'false'].map((v) => ({ value: v, text: v === 'true' ? '√' : '×' }))" @filter-change="handleQuery()">
              <template #default="{ row }">
                <div style="font-size: 18">{{ row.defaultable ? "√" : "×" }}</div>
              </template>
            </TableColumn>

            <TableColumn type="enum" :prop="`active`" :label="`激活`" :min-width="100" :showOverflowTooltip="true" show-filter v-model:filtered-value="search.active" :filters="['true', 'false'].map((v) => ({ value: v, text: v === 'true' ? '√' : '×' }))" @filter-change="handleQuery()">
              <template #default="{ row }">
                <div style="font-size: 18">{{ row.active ? "√" : "×" }}</div>
              </template>
            </TableColumn>

            <el-table-column label="操作" prop="">
              <template #default="{ row }">
                <span>
                  <el-link v-if="row.permissions.includes('612172734373298176')" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleCommand(command.Update, row)">{{ $t("glob.edit") }}</el-link>
                </span>
                <span>
                  <el-link v-if="row.permissions.includes('612172749170802688')" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleCommand(command.Delete, row)">{{ $t("glob.delete") }}</el-link>
                </span>
                <span v-if="row.permissions.includes('612172762626129920')">
                  <!-- 短信发送策略('612172653188349952') -->
                  <el-link :type="row.permissions.includes('612172762626129920') ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="row.permissions.includes('612172762626129920') ? false : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
                </span>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </pageTemplate>
    </el-card>
    <smsEditor ref="addGroup" @custom-event="handleRefreshTable" />
    <el-dialog v-model="dialogVisibleshow" title="查看安全目录" width="500" :before-close="handleClose">
      <treeAuth :proptreeId="containerId" :treeStyle="treeStyle" ref="treeAuthRef"></treeAuth>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisibleshow = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </el-scrollbar>
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { ref, reactive, readonly, computed, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch } from "vue";
import getUserInfo from "@/utils/getUserInfo";
import smsEditor from "./smsEditor.vue";
import pageTemplate from "@/components/pageTemplate.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import treeAuth from "@/components/treeAuth/index.vue";
import strategySms from "./strategySms.vue";
import { getMsgStrategyList, delSmsStrategy, getSmsStrategyDetails, customSmsContent } from "@/views/pages/apis/SendPolicy";

import { getUserGroupsList } from "@/views/pages/apis/groupManage";
import { getUserList } from "@/views/pages/apis/model";
import { getSmsList } from "@/views/pages/apis/NewNoticeTemplate";
import { getTriggerList } from "@/views/pages/apis/Triggercondition";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";

import { getAvailableTreeLine } from "@/views/pages/apis/supportNotes";

import { 安全管理中心_权限管理_安全 } from "@/views/pages/permission";
import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";

export default {
  components: {
    smsEditor,
    pageTemplate,
    treeAuth,
    strategySms,
    TableColumn,
  },
  inject: ["height"],
  data() {
    return {
      userGroupOption: [],
      userOption: [],
      SMSOption: [],
      TriggersOption: [],
      // 安全容器
      dialogVisibleshow: false,
      treeStyle: {
        pointerEvents: "none",
      },
      containerId: "",
      paging: {
        pageNumber: 1,
        pageSize: 50,
        total: 0,
      },
      tableData: [],
      userInfo: getUserInfo(),
      props: { title: "短信发送策略" },
      command: {
        Create: "Create",
        Update: "Update",
        Delete: "Delete",
      },
      expands: [],
      //短信模版内容
      textCursor: 0,
      receiverTypes: [
        { label: "通知联系人", value: "Notification" },
        { label: "现场联系人", value: "OnSite" },
        { label: "技术联系人", value: "Technical" },
      ],
      form: {
        recipientTypes: [], //收件人类型
        recipients: "", //指定联系人(输入手机号，多个用分号隔开)
        smsTemplateId: "", //	关联的短信模版ID
        triggerTemplateId: "", //关联的触发条件模版ID
        userGroupIds: [], //发送给的用户组ID列表
        userIds: [], //	发送给的用户ID列表
      },
      smsId: "",
      defaultable: false,
      defaulloading: false,
      Security,

      // $filter0: [
      //   { text: "包含", value: "include" },
      //   { text: "不包含", value: "exclude" },
      //   { text: "等于", value: "eq" },
      //   { text: "不等于", value: "ne" },
      // ],
      $filter0: exoprtMatch1,

      searchType0ByName: "include",
      searchType1ByName: "include",

      searchType0ByDescription: "include",
      searchType1ByDescription: "include",

      searchType0ByTemplate: "include",
      searchType1ByTemplate: "include",

      searchType0ByTrigger: "include",
      searchType1ByTrigger: "include",

      search: {
        eqName: [],
        includeName: [],
        nameFilterRelation: "AND",
        neName: [],
        excludeName: [],

        eqDescription: [],
        includeDescription: [],
        descriptionFilterRelation: "AND",
        neDescription: [],
        excludeDescription: [],

        eqTemplate: [] /* 等于的短信发送策略模板 */,
        includeTemplate: [] /* 包含的短信发送策略模板 */,
        templateFilterRelation: "AND" /* 短信发送策略模板过滤关系(AND,OR) */,
        neTemplate: [] /* 不等于的短信发送策略模板 */,
        excludeTemplate: [] /* 不包含的短信发送策略模板 */,

        eqTrigger: [] /* 等于的短信发送策略触发条件 */,
        includeTrigger: [] /* 包含的短信发送策略触发条件 */,
        triggerFilterRelation: "AND" /* 短信发送策略触发条件过滤关系(AND,OR) */,
        neTrigger: [] /* 不等于的短信发送策略触发条件 */,
        excludeTrigger: [] /* 不包含的短信发送策略触发条件 */,

        active: "" /* 是否激活 */,
        defaultable: "" /* 是否默认 */,
      },
    };
  },
  computed: {
    searchByName: {
      get: function () {
        let value0 = "";
        if (this.searchType0ByName === "include") value0 = this.search.includeName[0] || "";
        if (this.searchType0ByName === "exclude") value0 = this.search.excludeName[0] || "";
        if (this.searchType0ByName === "eq") value0 = this.search.eqName[0] || "";
        if (this.searchType0ByName === "ne") value0 = this.search.neName[0] || "";
        let value1 = "";
        if (this.searchType1ByName === "include") value1 = this.search.includeName[this.search.includeName.length - 1] || "";
        if (this.searchType1ByName === "exclude") value1 = this.search.excludeName[this.search.excludeName.length - 1] || "";
        if (this.searchType1ByName === "eq") value1 = this.search.eqName[this.search.eqName.length - 1] || "";
        if (this.searchType1ByName === "ne") value1 = this.search.neName[this.search.neName.length - 1] || "";
        return {
          type0: this.searchType0ByName,
          type1: this.searchType1ByName,
          relation: this.search.nameFilterRelation,
          value0,
          value1,
          input0: "",
          // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
          input1: "",
          // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
        };
      },
      set: function (v) {
        this.searchType0ByName = v.type0;
        this.searchType1ByName = v.type1;
        this.search.nameFilterRelation = v.relation;
        this.search.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
        this.search.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
        this.search.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
        this.search.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
      },
    },

    searchByDescription: {
      get: function () {
        let value0 = "";
        if (this.searchType0ByDescription === "include") value0 = this.search.includeDescription[0] || "";
        if (this.searchType0ByDescription === "exclude") value0 = this.search.excludeDescription[0] || "";
        if (this.searchType0ByDescription === "eq") value0 = this.search.eqDescription[0] || "";
        if (this.searchType0ByDescription === "ne") value0 = this.search.neDescription[0] || "";
        let value1 = "";
        if (this.searchType1ByDescription === "include") value1 = this.search.includeDescription[this.search.includeDescription.length - 1] || "";
        if (this.searchType1ByDescription === "exclude") value1 = this.search.excludeDescription[this.search.excludeDescription.length - 1] || "";
        if (this.searchType1ByDescription === "eq") value1 = this.search.eqDescription[this.search.eqDescription.length - 1] || "";
        if (this.searchType1ByDescription === "ne") value1 = this.search.neDescription[this.search.neDescription.length - 1] || "";
        return {
          type0: this.searchType0ByDescription,
          type1: this.searchType1ByDescription,
          relation: this.search.descriptionFilterRelation,
          value0,
          value1,
          input0: "",
          // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
          input1: "",
          // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
        };
      },
      set: function (v) {
        this.searchType0ByDescription = v.type0;
        this.searchType1ByDescription = v.type1;
        this.search.descriptionFilterRelation = v.relation;
        this.search.includeDescription = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
        this.search.excludeDescription = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
        this.search.eqDescription = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
        this.search.neDescription = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
      },
    },

    searchByTemplate: {
      get: function () {
        let value0 = "";
        if (this.searchType0ByTemplate === "include") value0 = this.search.includeTemplate[0] || "";
        if (this.searchType0ByTemplate === "exclude") value0 = this.search.excludeTemplate[0] || "";
        if (this.searchType0ByTemplate === "eq") value0 = this.search.eqTemplate[0] || "";
        if (this.searchType0ByTemplate === "ne") value0 = this.search.neTemplate[0] || "";
        let value1 = "";
        if (this.searchType1ByTemplate === "include") value1 = this.search.includeTemplate[this.search.includeTemplate.length - 1] || "";
        if (this.searchType1ByTemplate === "exclude") value1 = this.search.excludeTemplate[this.search.excludeTemplate.length - 1] || "";
        if (this.searchType1ByTemplate === "eq") value1 = this.search.eqTemplate[this.search.eqTemplate.length - 1] || "";
        if (this.searchType1ByTemplate === "ne") value1 = this.search.neTemplate[this.search.neTemplate.length - 1] || "";
        return {
          type0: this.searchType0ByTemplate,
          type1: this.searchType1ByTemplate,
          relation: this.search.templateFilterRelation,
          value0,
          value1,
          input0: "",
          // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
          input1: "",
          // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
        };
      },
      set: function (v) {
        this.searchType0ByTemplate = v.type0;
        this.searchType1ByTemplate = v.type1;
        this.search.templateFilterRelation = v.relation;
        this.search.includeTemplate = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
        this.search.excludeTemplate = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
        this.search.eqTemplate = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
        this.search.neTemplate = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
      },
    },

    searchByTrigger: {
      get: function () {
        let value0 = "";
        if (this.searchType0ByTrigger === "include") value0 = this.search.includeTrigger[0] || "";
        if (this.searchType0ByTrigger === "exclude") value0 = this.search.excludeTrigger[0] || "";
        if (this.searchType0ByTrigger === "eq") value0 = this.search.eqTrigger[0] || "";
        if (this.searchType0ByTrigger === "ne") value0 = this.search.neTrigger[0] || "";
        let value1 = "";
        if (this.searchType1ByTrigger === "include") value1 = this.search.includeTrigger[this.search.includeTrigger.length - 1] || "";
        if (this.searchType1ByTrigger === "exclude") value1 = this.search.excludeTrigger[this.search.excludeTrigger.length - 1] || "";
        if (this.searchType1ByTrigger === "eq") value1 = this.search.eqTrigger[this.search.eqTrigger.length - 1] || "";
        if (this.searchType1ByTrigger === "ne") value1 = this.search.neTrigger[this.search.neTrigger.length - 1] || "";
        return {
          type0: this.searchType0ByTrigger,
          type1: this.searchType1ByTrigger,
          relation: this.search.triggerFilterRelation,
          value0,
          value1,
          input0: "",
          // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
          input1: "",
          // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
        };
      },
      set: function (v) {
        this.searchType0ByTrigger = v.type0;
        this.searchType1ByTrigger = v.type1;
        this.search.triggerFilterRelation = v.relation;
        this.search.includeTrigger = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
        this.search.excludeTrigger = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
        this.search.eqTrigger = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
        this.search.neTrigger = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
      },
    },
    rules() {
      return {
        // userGroupIds: [{ type: "array", required: true, message: "请选择用户组", trigger: "change" }],
        // userIds: [{ type: "array", required: true, message: "请选择用户", trigger: "change" }],
        smsTemplateId: [{ required: true, message: "请选择短信模版", trigger: "change" }],
        triggerTemplateId: [{ required: true, message: "请选择触发条件", trigger: "change" }],
        // recipients: [{ required: true, message: "请输入活动名称", trigger: "blur" }],
        recipientTypes: [{ type: "array", required: true, message: "请选择收件人类型", trigger: "change" }],
      };
    },
  },
  created() {},
  mounted() {
    this.handleRefreshTable();
  },
  methods: {
    //修改表格展开行数据
    update(val) {
      let obj = { ...val };
      getSmsStrategyDetails({ ...obj })
        .then((res) => {
          if (res.success) {
            ElMessage.success("操作成功");
            // getList();
          } else {
            ElMessage.error(JSON.parse(res.data)?.message);
          }
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        });
    },
    handleCommand(type, row) {
      switch (type) {
        case this.command.Create:
          this.$refs.addGroup.open(row);
          break;
        case this.command.Update:
          this.$refs.addGroup.open(row);
          break;
        case this.command.Delete:
          ElMessageBox.confirm(`确定删除短信模版："${row.name}"?`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(async () => {
              try {
                const { success, data, message } = await delSmsStrategy({ id: row.id });
                if (!success) throw new Error(message);
                ElMessage.success("操作成功");
                this.handleRefreshTable();
              } catch (error) {
                error instanceof Error && ElMessage.error(error.message);
              }
            })
            .catch((err) => {});
          break;
      }
    },
    // eslint-disable-next-line no-undef
    getRowKeys(row) {
      return row.id;
    },
    async handleExpandChange(row, expandedRows) {
      if (expandedRows.length) {
        //展开
        this.expands = []; //先干掉之前展开的行
        if (row) {
          this.expands.push(row.id); //push新的行 (原理有点类似防抖)
        }
        this.smsId = row.id;
        await this.getDetails(this.smsId);
      } else {
        this.expands = []; //折叠 就清空expand-row-keys对应的数组
      }
    },

    //获取短信详情
    async getDetails(smsId) {
      this.defaulloading = true;
      const params = {
        pageNumber: 1,
        pageSize: 9999,
      };
      const paramsSms = {
        pageNumber: 1,
        pageSize: 9999,
        active: true,
        containerId: this.userInfo.currentTenant.containerId,
        queryPermissionId: "612175414051209216",
        verifyPermissionIds: "612175394593832960,612175428227956736,612175443923042304",
      };
      const paramsTrig = {
        pageNumber: 1,
        pageSize: 9999,
        active: true,
        containerId: this.userInfo.currentTenant.containerId,
        queryPermissionId: "612172435491389440",
        verifyPermissionIds: "612172412355608576,612172450129510400,612172465858150400",
      };
      // 获取用户组
      await (async () => {
        const { success, message, data } = await getUserGroupsList({ ...params, active: true });
        if (!success) throw Object.assign(new Error(message), { success, data });
        this.userGroupOption = data;
      })();
      // 获取用户
      await (async () => {
        const { success, message, data } = await getUserList({ ...params, blocked: false });
        if (!success) throw Object.assign(new Error(message), { success, data });
        this.userOption = data;
      })();
      // 获取短信模版
      await (async () => {
        const { success, message, data } = await getSmsList({ ...paramsSms, active: true });
        if (!success) throw Object.assign(new Error(message), { success, data });
        this.SMSOption = data;
      })();
      // 获取触发条件模版
      await (async () => {
        const { success, message, data } = await getTriggerList({ ...paramsTrig, active: true });
        if (!success) throw Object.assign(new Error(message), { success, data });
        this.TriggersOption = data;
      })();
      getSmsStrategyDetails({ id: smsId }).then(({ success, data, total }) => {
        if (success) {
          this.form.recipientTypes = data.recipientTypes; //收件人类型
          this.form.recipients = data.recipients; //指定联系人(输入手机号，多个用分号隔开)
          this.form.smsTemplateId = data.templateId; //	关联的短信模版ID
          this.form.triggerTemplateId = data.triggerId; //关联的触发条件模版ID
          this.form.userGroupIds = data.userGroupIds; //发送给的用户组ID列表
          this.form.userIds = data.userIds; //	发送给的用户ID列表
          this.defaultable = data.defaultable; //	发送给的用户ID列表
          this.defaulloading = false;
          setTimeout(() => {
            this.clearValidate();
          }, 100);
        }
      });
    },
    clearValidate() {
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    async handleQuery() {
      this.paging.pageNumber = 1;
      await nextTick();
      this.handleRefreshTable();
    },
    // 获取短信发送策略列表
    async handleRefreshTable() {
      try {
        const { data, message, success } = await getAvailableTreeLine({ permissionId: 安全管理中心_权限管理_安全, containerId: this.userInfo.currentTenant.containerId });

        if (!success) throw new Error(message);

        const params = {
          ...this.search,
          pageNumber: this.paging.pageNumber,
          pageSize: this.paging.pageSize,
          containerId: this.userInfo.currentTenant.containerId,
          queryPermissionId: "612172715167580160",
          verifyPermissionIds: "612172695882170368,612172734373298176,612172749170802688,612172762626129920",
          containerList: (data || []).join(","),
        };
        getMsgStrategyList(params).then(({ success, data, total }) => {
          if (success) {
            this.paging.total = Number(total);
            this.tableData = data;
            if (!this.tableData.length && this.paging.pageNumber !== 1) {
              this.paging.pageNumber = 1;
              this.handleRefreshTable();
            }
          } else console.error(JSON.parse(data)?.message || data);
        });
      } catch (error) {
        error instanceof Error && this.$message.error(error.message);
      }
    },
    // 安全容器
    showSecurityTree,
    // 短信模版内容处理方法
    getParams() {
      const params = {
        id: this.smsId,
        recipientTypes: this.form.recipientTypes, //收件人类型
        recipients: this.form.recipients, //指定联系人(输入手机号，多个用分号隔开)
        smsTemplateId: this.form.smsTemplateId, //	关联的短信模版ID
        triggerTemplateId: this.form.triggerTemplateId, //关联的触发条件模版ID
        userGroupIds: this.form.userGroupIds, //发送给的用户组ID列表
        userIds: this.form.userIds, //	发送给的用户ID列表
      };
      if (this.isEdit) params.id = this.form.id;
      return params;
    },
    submitForm() {
      this.$refs["form"].validate(async (valid) => {
        if (!valid) return false;
        try {
          const { success, message } = await customSmsContent(this.getParams());
          if (!success) throw new Error(message);
          this.$message.success(`操作成功!`);
          this.handleRefreshTable();
        } catch (error) {
          error instanceof Error && this.$message.success(error.message);
        }
      });
    },
    /* 返回上一页 */
    handleClose(done) {
      this.$emit("confirm");
      if (done instanceof Function) done();
      else this.drawer = false;
    },
    //
    cascaderChangeG(val) {
      this.form.recipientTypes = [];
      val.forEach((v, i) => {
        if (v.length > 1) {
          this.form.recipientTypes.push(v);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.box-table {
  padding: 10px 20px;
}

.select_item {
  min-height: 30px !important;
  height: 100px !important;
  font-size: 12px;
}

.select_item_once {
  height: 60px !important;
}
</style>
