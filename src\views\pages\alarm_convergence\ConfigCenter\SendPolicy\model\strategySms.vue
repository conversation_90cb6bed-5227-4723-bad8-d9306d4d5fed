<template>
  <el-form :model="form" :style="{ marginTop: '10px', padding: '0 10px' }" label-position="left" label-width="160px" ref="roleFormRef" label-suffix=":">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-row style="width: 100%">
          <el-col :span="24" style="text-align: right; margin: 30px 0">
            <el-button v-if="userInfo.hasPermission(资产管理中心_联系人_可读) && userInfo.hasPermission(系统管理中心_短信发送策略_分配联系人)" type="primary" @click="addModule('contacts')">
              <el-icon class="el-icon--left"> <Plus /> </el-icon>分配联系人
            </el-button>
          </el-col>
          <div style="height: 40px; background: #f2f4f5; width: 100%; margin-bottom: 20px"></div>
          <el-row :gutter="24" style="width: 100%" class="tw-mx-[2px]">
            <template v-for="item in contactsList" :key="item.id">
              <el-col :xs="12" :sm="12" :md="12" :lg="8" :xl="6" class="tw-mb-[16px]" :span="8">
                <el-card shadow="never" :body-style="{ padding: '6px 20px' }" :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
                  <template #header>
                    <div style="display: flex !important" class="tw-flex tw-items-center">
                      <el-icon color="var(--el-color-primary)" :size="16" class="tw-mr-1">
                        <Postcard />
                      </el-icon>
                      <el-text type="primary" class="tw-mr-auto tw-w-[calc(100%_-_265px)] tw-text-[14px] tw-leading-[16px]" truncated :title="item.name">{{ item.name }}</el-text>
                      <div style="display: flex; align-items: center">
                        <div style="margin-right: 2px" v-for="itemA in localesOption" :key="itemA.value">
                          <div v-if="itemA.value == item.language" :style="{ background: `url(${itemA.icon}) no-repeat left / auto`, paddingLeft: '30px' }">
                            {{ itemA.label }}
                          </div>
                        </div>
                        <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(资产管理中心_联系人_查看联系人)">
                          <span class="tw-ml-auto tw-h-fit">
                            <el-link type="danger" :underline="false" :disabled="!userInfo.hasPermission(资产管理中心_联系人_查看联系人)" @click="viewContactDetail(item as ContactsItem)">{{ $t("glob.Cat") }}</el-link>
                          </span>
                        </el-tooltip>
                        <el-popconfirm :width="200" :disabled="!userInfo.hasPermission(智能事件中心_变更工单_分配联系人)" :title="`确定${$t('glob.remove')} ${item.name} ?`" :confirm-button-text="$t('glob.remove')" confirm-button-type="danger" @confirm="delConfirm('contacts', '', item)">
                          <template #reference>
                            <span>
                              <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(智能事件中心_变更工单_分配联系人)">
                                <span class="tw-ml-auto tw-h-fit">
                                  <el-link type="danger" :underline="false" :disabled="!userInfo.hasPermission(智能事件中心_变更工单_分配联系人)" class="tw-ml-[8px]">{{ $t("glob.remove") }}</el-link>
                                </span>
                              </el-tooltip>
                            </span>
                          </template>
                        </el-popconfirm>
                      </div>
                    </div>
                  </template>
                  <template #default>
                    <div style="display: flex !important" class="tw-flex tw-h-[24px] tw-items-center" title="固定电话">
                      <el-icon class="tw-mr-2">
                        <Phone />
                      </el-icon>
                      <el-text type="info" class="tw-text-[14px]">{{ item.landlinePhone || "--" }}</el-text>
                    </div>
                    <div style="display: flex !important" class="tw-flex tw-h-[24px] tw-items-center" title="移动电话">
                      <el-icon class="tw-mr-2">
                        <Iphone />
                      </el-icon>
                      <el-text type="info" class="tw-text-[14px]">{{ item.mobilePhone || "--" }}</el-text>
                    </div>
                    <div style="display: flex !important" class="tw-flex tw-h-[24px] tw-items-center" title="邮箱">
                      <el-icon class="tw-mr-2">
                        <Message />
                      </el-icon>
                      <el-text type="info" class="tw-text-[14px]">{{ item.email || "--" }}</el-text>
                    </div>
                  </template>
                  <template #footer> {{ item.tenantName + `[${item.tenantAbbreviation}]` }}</template>
                </el-card>
              </el-col>
            </template>
            <div v-if="contactsList.length == 0" style="margin: 0 auto; color: #909399">暂无数据</div>
          </el-row>
          <el-col :span="24" style="text-align: right; margin-top: 30px">
            <el-button v-if="userInfo.hasPermission(系统管理中心_用户组权限_可读) && userInfo.hasPermission(系统管理中心_短信发送策略_分配用户组)" type="primary" @click="addModule('userGroup')">
              <el-icon class="el-icon--left"> <Plus /> </el-icon>分配用户组
            </el-button>
          </el-col>
          <el-col>
            <el-table stripe :data="userGroupsList" style="width: 100%; margin-top: 30px">
              <el-table-column align="left" label="名称" prop="name">
                <template #default="{ row, column, $index }">
                  <span>{{ row.name }} [{{ row.tenantAbbreviation }}]</span>
                </template>
              </el-table-column>
              <el-table-column align="left" label="操作" width="500">
                <template #default="scope">
                  <el-popconfirm :title="delTitle" @confirm="delConfirm('userGroup', scope.$index, scope.row)">
                    <template #reference>
                      <el-button type="text" textColor="danger" @click="delLevel('userGroup', scope.$index, scope.row)">取消分配</el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col :span="24" style="text-align: right; margin-top: 30px">
            <el-button v-if="userInfo.hasPermission(系统管理中心_用户权限_可读) && userInfo.hasPermission(系统管理中心_短信发送策略_分配用户)" type="primary" @click="addModule('user')">
              <el-icon class="el-icon--left"> <Plus /> </el-icon>分配用户
            </el-button>
          </el-col>
          <el-col>
            <el-table stripe :data="usersList" style="width: 100%; margin-top: 30px">
              <el-table-column align="left" label="名称" prop="name">
                <template #default="{ row, column, $index }">
                  <span>{{ row.name }} [{{ row.tenantAbbreviation }}]</span>
                </template>
              </el-table-column>
              <el-table-column align="left" label="操作" width="500">
                <template #default="scope">
                  <el-popconfirm :title="delTitle" @confirm="delConfirm('user', scope.$index, scope.row)">
                    <template #reference>
                      <el-button type="text" textColor="danger" @click="delLevel('user', scope.$index, scope.row)">取消分配</el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col :span="24" style="text-align: right; margin-top: 30px">
            <el-button v-if="userInfo.hasPermission('612172864119898112')" type="primary" @click="handleAssignCustomer">
              <el-icon class="el-icon--left"> <Plus /> </el-icon>分配客户
            </el-button>
          </el-col>
          <el-col>
            <el-table stripe :data="customsList" style="width: 100%; margin-top: 30px">
              <el-table-column align="left" label="名称" prop="name">
                <template #default="{ row, column, $index }">
                  <span>{{ row.name }} [{{ row.abbreviation }}]</span>
                </template>
              </el-table-column>
              <el-table-column align="left" label="描述" prop="detail"> </el-table-column>
              <el-table-column align="left" label="是否激活" prop="active">
                <template #default="scope">
                  <el-text type="primary" v-if="scope.row.active">是</el-text>
                  <el-text type="danger" v-else>否</el-text>
                </template>
              </el-table-column>

              <el-table-column align="left" label="操作" width="500">
                <template #default="scope">
                  <el-popconfirm :title="delTitle" @confirm="delCostom('area', scope.$index, scope.row)">
                    <template #reference>
                      <el-button type="text" textColor="danger" @click="delCostomLevel('area', scope.$index, scope.row)">取消分配</el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-row style="width: 100%">
            <el-col :span="24" style="text-align: right; margin-top: 30px">
              <el-button v-if="userInfo.hasPermission('612172885808644096')" type="primary" @click="addModule('area')">
                <el-icon class="el-icon--left"> <Plus /> </el-icon>分配区域
              </el-button>
            </el-col>
            <el-col>
              <el-table stripe :data="areaList" style="width: 100%; margin-top: 30px">
                <el-table-column align="left" label="名称" prop="name"> </el-table-column>
                <el-table-column align="left" label="描述" prop="detail"> </el-table-column>
                <el-table-column align="left" label="是否激活" prop="active">
                  <template #default="scope">
                    <el-text type="primary" v-if="scope.row.active">是</el-text>
                    <el-text type="danger" v-else>否</el-text>
                  </template>
                </el-table-column>

                <el-table-column align="left" label="操作" width="500">
                  <template #default="scope">
                    <el-popconfirm :title="delTitle" @confirm="delConfirm('area', scope.$index, scope.row)">
                      <template #reference>
                        <el-button type="text" textColor="danger" @click="delLevel('area', scope.$index, scope.row)">取消分配</el-button>
                      </template>
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="24" style="margin-top: 20px; text-align: right">
              <el-button v-if="userInfo.hasPermission('612172905534455808')" type="primary" @click="addModule('location')">
                <el-icon class="el-icon--left"> <Plus /> </el-icon>分配场所
              </el-button>
            </el-col>
            <el-col>
              <el-table stripe :data="locationList" style="width: 100%; margin-top: 30px">
                <el-table-column align="left" label="名称" prop="name"> </el-table-column>
                <el-table-column align="left" label="描述" prop="detail"> </el-table-column>
                <el-table-column align="left" label="是否激活" prop="active">
                  <template #default="scope">
                    <el-text type="primary" v-if="scope.row.active">是</el-text>
                    <el-text type="danger" v-else>否</el-text>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="操作" width="500">
                  <template #default="scope">
                    <!-- <el-button type="text" v-show="!scope.row.state" @click="confirmLevel('response', scope.$index, scope.row)">保存</el-button> -->

                    <el-popconfirm :title="delTitle" @confirm="delConfirm('location', scope.$index, scope.row)">
                      <template #reference>
                        <el-button type="text" textColor="danger" @click="delLevel('location', scope.$index, scope.row)">取消分配</el-button>
                      </template>
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="24" style="margin-top: 20px; text-align: right">
              <el-button v-if="userInfo.hasPermission('612172931853713408')" type="primary" @click="addModule('device')">
                <el-icon class="el-icon--left"> <Plus /> </el-icon>分配设备
              </el-button>
            </el-col>
            <el-col>
              <el-table stripe :data="devicesList" style="width: 100%; margin-top: 30px">
                <el-table-column align="left" label="名称" prop="name">
                  <template #default="{ row }">
                    <div>
                      <div style="color: #409eff; cursor: pointer" @click="openDevice(row)">
                        {{ row.name }}
                      </div>
                      <div style="font-size: 12px">
                        {{ row.detail }}
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="描述" prop="description"> </el-table-column>
                <el-table-column align="left" label="是否激活" prop="active">
                  <template #default="{ row }">
                    <el-text type="primary" v-if="row.active">是</el-text>
                    <el-text type="danger" v-else>否</el-text>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="操作" width="500">
                  <template #default="scope">
                    <el-popconfirm :title="delTitle" @confirm="delConfirm('device', scope.$index, scope.row)">
                      <template #reference>
                        <el-button type="text" textColor="danger" @click="delLevel('device', scope.$index, scope.row)">取消分配</el-button>
                      </template>
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-row>
      </el-col>
      <contactView ref="contactViewRef"></contactView>
      <alarmDownDialogSms ref="DownSlaConfig" :addType="dialogType" :options="options" :allOptions="allOptions" :tenantsOptions="tenantsOptions" @confirmMsg="confirmMsg" @confirmTenant="confirmTenant" @clearAddType="() => (dialogType = '')"></alarmDownDialogSms>
      <customDownDialog ref="customConfig" :customvalueA="customvalueA" :allOptions="allOptions" :options="options" @customMsg="customMsg"></customDownDialog>
    </el-row>
  </el-form>
</template>

<script lang="ts" setup generic="T extends { [key: string]: unknown; id: string }">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";
import { ElMessage, ElMenuItem, ElForm, ElMessageBox } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { QuillEditor } from "@vueup/vue-quill";
import BlotFormatter from "quill-blot-formatter";
import "@vueup/vue-quill/dist/vue-quill.snow.css";
// 注册
import { Plus, PhoneFilled, Iphone, Message, Phone, Postcard } from "@element-plus/icons-vue";
import bindDevice from "@/components/bindDevice/bindDevice.vue";
import { getRegionsTenantCurrent } from "@/views/pages/apis/regionManage";
import { getLocationsTenantCurrent } from "@/views/pages/apis/locationManang";
import contactView from "@/views/pages/alarm_convergence/PropertyManage/contactsManage/contactsEdit.vue";
import { getUserGroupsList } from "@/views/pages/apis/groupManage";
import { getQueryContactList, getVisibleTenants, getUserbyGroupsList, getUserList } from "@/views/pages/apis/NewNoticeTemplate";

import getUserInfo from "@/utils/getUserInfo";

import { getSmsStrategyDetails } from "@/views/pages/apis/SendPolicy";
import { querySMSassignedList, queryLocationList, queryResourceList, queryRegionsList } from "@/views/pages/apis/index";
import alarmDownDialogSms from "./alarmDownDialogSms.vue";
import customDownDialog from "./customDownDialog.vue";
import { useResizeObserver } from "@vueuse/core";
import { sms_assign_user, sms_assign_contacts, sms_assign_userGroups, getAllUsersList, getAllUsersGroupsList, getAllContactsList, sms_assign_customers, sms_assign_regions, sms_assign_locations, sms_assign_devices, getDeviceList, getAllLocationsList, getAllRegionsList, getAllDeviceList } from "@/views/pages/apis/SendPolicy";
import { getReportList, addTenantReport as addData, editReport as setData, type Reports as DataItem, reportStateOption, reportModelOption, enableReport, delTenantReport, getTenantReportList as getData } from "@/views/pages/apis/reports";

import { 系统管理中心_场所权限_分配短信发送策略, 系统管理中心_区域权限_分配短信发送策略, 系统管理中心_设备权限_分配短信发送策略, 系统管理中心_客户管理_分配短信发送策略 } from "@/views/pages/permission";
import { localesOption } from "@/api/locale.ts";
import { 智能事件中心_变更工单_分配联系人, 资产管理中心_联系人_查看联系人, 资产管理中心_联系人_可读, 系统管理中心_短信发送策略_分配联系人, 系统管理中心_用户组权限_可读, 系统管理中心_短信发送策略_分配用户组, 系统管理中心_用户权限_可读, 系统管理中心_短信发送策略_分配用户 } from "@/views/pages/permission";
// import ModelExpand from "@/views/pages/modelExpand/Model.vue";
const userInfo = getUserInfo();
const route = useRoute();
const router = useRouter();
const modules = {
  name: "blotFormatter",
  module: BlotFormatter,
};
interface Emits {
  ($event: "confirm", data: Record<string, any>): any;
}
const emits = defineEmits<Emits>();
interface Props {
  detail: T;
  width: number;
}
const props = withDefaults(defineProps<Props>(), { width: 0, detail: () => ({}) as T });

const roleFormRef = ref<InstanceType<typeof ElForm>>();
const DownSlaConfig = ref<InstanceType<typeof alarmDownDialogSms>>();
const customConfig = ref<InstanceType<typeof customDownDialog>>();
// const bindDeviceRef = ref<InstanceType<typeof bindDevice>>();

const id = ref("");
const type = ref("");
//工作时间降级
const areaList = ref<Record<"id" | "name" | "detail", string>[]>([]); //区域表格数组
const locationList = ref<Record<"id" | "name" | "detail", string>[]>([]); //场所表格数据
const devicesList = ref<Record<"id" | "name" | "detail", string>[]>([]); //设备列表
const customsList = ref<Record<"id" | "name" | "detail", string>[]>([]); //客户列表
const usersList = ref([]); //用户表格数据
const userGroupsList = ref([]); //用户组表格数据
const contactsList = ref([]); //联系人表格数据
const form = reactive<Record<string, any>>({ ...props.detail });
const ctx = getCurrentInstance()!;

const allBool = ref([false, false, false, false, false, false, false]);
const tableContentRef = ref();
const tableWidth = ref(0);
useResizeObserver(tableContentRef, (entries) => {
  const entry = entries[0];
  const { width, height } = entry.contentRect;
  tableWidth.value = ((width - 80) / 24).toFixed(0);
});
const options = ref([]);
const customvalueA = ref([]);
const dialogType = ref("");
const tenantId = ref("");
const defaultable = ref(false);
const allOptions = ref([]);
const tenantsOptions = ref([]);
//新增区域/场所/设备
function addModule(type, tenantIds) {
  options.value = [];
  allOptions.value = [];
  dialogType.value = type;
  tenantsOptions.value = userInfo.tenants;
  const params = {
    pageNumber: 1,
    pageSize: 9999,
    active: true,
    tenantId: tenantIds,
  };

  const permissionMapping = {
    location: 系统管理中心_场所权限_分配短信发送策略,
    area: 系统管理中心_区域权限_分配短信发送策略,
    userGroup: "638977805605928960",
    user: "638977858743566336",
    contacts: "513148893207199744",
    default: 系统管理中心_设备权限_分配短信发送策略,
  };

  const queryMapping = {
    location: queryLocationList,
    area: queryRegionsList,
    default: queryResourceList,
  };
  const queryMappingA = {
    userGroup: getUserbyGroupsList,
    user: getUserList,
    contacts: getQueryContactList,
  };
  const assignedIdsMapping = {
    location: "locationIds",
    area: "regionIds",
    userGroup: "userGroupIds",
    user: "userIds",
    contacts: "contactIds",
    default: "deviceIds",
  };

  const userPermission = permissionMapping[type] || permissionMapping.default;
  let queryFunction = "";
  if (tenantIds) {
    queryFunction = queryMappingA[type] || queryMappingA.default;
  } else {
    queryFunction = queryMapping[type] || queryMapping.default;
  }
  const assignedIdType = assignedIdsMapping[type] || assignedIdsMapping.default;

  const fetchData = (queryFunc, type) => {
    return new Promise((resolve, reject) => {
      queryFunc(type ? {} : params).then((res) => {
        if (res.success) {
          resolve(res.data);
        } else {
          reject(res.error);
        }
      });
    });
  };

  if (!userInfo.hasPermission(userPermission)) {
    options.value = [];
    return;
  }

  Promise.all([fetchData(queryFunction, false), fetchData(querySMSassignedList, true)])
    .then((result) => {
      const [allItems, assignedData] = result;
      const assignedIds = assignedData[assignedIdType];
      const newArray = allItems.filter((item) => !assignedIds.includes(item.id));
      console.log("tenantId.value :>> ", tenantId.value);
      if (dialogType.value == "userGroup" || dialogType.value == "user" || dialogType.value == "contacts") {
        if (tenantIds) {
          options.value = newArray;
          allOptions.value = newArray;
        } else {
          options.value = [];
          allOptions.value = [];
        }
      } else {
        options.value = newArray;
        allOptions.value = allItems;
      }
    })
    .catch((error) => {
      console.error("Error fetching data:", error);
    });

  DownSlaConfig.value.dialogVisible = true;
  DownSlaConfig.value.value = "";
  DownSlaConfig.value.areaList = [];
}

//分配客户
function handleAssignCustomer() {
  const tenant = userInfo.currentTenant || {};
  const tenantName = `${tenant.name}[${tenant.abbreviation}]`;
  ElMessageBox.confirm(`确定分配给客户${tenantName}?`, "分配客户", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      bindRelationCoustom({ value: [tenant.id] });
    })
    .catch(() => {});
}

// function addCostom() {
//   const params = {
//     pageNumber: 1,
//     pageSize: 9999,
//     // active: true
//   };
//   const activeTenants = userInfo.tenants.filter((tenant) => tenant.activated);
//   customConfig.value.dialogVisible = true;
//   customConfig.value.customvalue = [];
//   options.value = [];
//   allOptions.value = [];

//   if (!userInfo.hasPermission(系统管理中心_客户管理_分配短信发送策略)) return;

//   const fetchAssignedCustomerIds = () => {
//     return new Promise((resolve, reject) => {
//       querySMSassignedList(params).then((res) => {
//         if (res.success) {
//           resolve(res.data.customerIds);
//         } else {
//           reject(res.error);
//         }
//       });
//     });
//   };

//   fetchAssignedCustomerIds()
//     .then((customerIds) => {
//       const newArray = activeTenants.filter((item) => !customerIds.includes(item.id));
//       options.value = newArray;
//       allOptions.value = activeTenants;
//     })
//     .catch((error) => {
//       console.error("Error fetching assigned customer IDs:", error);
//     });
// }

//确认客户
async function customMsg(val) {
  bindRelationCoustom(val);
}
//确认当前保存的数据
function confirmMsg(val) {
  bindRelation(val);
}

function confirmTenant(val) {
  // tenantId.value = val
  addModule(dialogType.value, val);
}
const contactViewRef = ref<InstanceType<typeof contactView>>();
async function viewContactDetail(row: ContactsItem) {
  if (!contactViewRef.value) return false;
  contactViewRef.value.open(row, true);
}
// //删除
const delTitle = ref("");
function delLevel(type, index, data) {
  delTitle.value = "确认取消分配当前数据吗？";
}
function delCostomLevel(type, index, data) {
  delTitle.value = "确认取消分配当前数据吗？";
}
//客户删除
function delCostom(type, index, data) {
  sms_assign_customers({ id: form.id, customerIds: [data.id], assignType: "delete" })
    .then(({ success, message, data }) => {
      if (!success) {
        throw Object.assign(new Error(message));
      } else {
        // 初始化加载策略配置表格
        getSmsStrategyDetails({ id: form.id }).then(({ success, data, total }) => {
          if (success) {
            getCostom(data);
          }
        });
        ElMessage.success("操作成功");
        customConfig.value.dialogVisible = false;
      }
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
}
async function delConfirm(type, index, data) {
  delRelation({ type, id: data.id });
}
function openDevice(props) {
  const { href } = router.resolve({
    name: "509596457372745728",
    params: { id: props.id },
    query: {
      fallback: route.name,
      tenant: props.tenantId,
    },
  });
  window.open(href, props.id);
}
// 初始化加载策略配置表格
getSmsStrategyDetails({ id: form.id }).then(({ success, data, total }) => {
  if (success) {
    defaultable.value = data.defaultable;
    getCostom(data);
    getDetail({ id: data.id }, data);
  }
});
function getCostom(rowall) {
  let tenantIds = rowall.customerIds || [];
  customsList.value = []; //客户列表
  if (tenantIds.length > 0) {
    getData({ tenantIds })
      .then(({ success, message, data }) => {
        // customsList.value = data;
        type Item = typeof customsList.value;
        customsList.value = data.reduce((p, v) => p.concat({ abbreviation: v.abbreviation, id: v.id, name: v.name, detail: "", active: v.activated }), [] as Item);
      })
      .catch((error) => {
        if (error instanceof Error) ElMessage.error(error.message);
      });
  } else {
    // throw Object.assign(new Error(message));
  }
}
function getDetail(row: Partial<{ id: string }>, rowall) {
  getDeviceList({
    pageNumber: 1,
    pageSize: 10000,
    ids: rowall.deviceIds.join(","),
  })
    .then(({ success, message, data }) => {
      if (!success) throw new Error(message);
      type Item = typeof devicesList.value;
      devicesList.value = data.reduce((p, v) => p.concat({ description: v.description, id: v.id, name: v.name, detail: v.config.ipAddress, active: v.active }), [] as Item);
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });

  getAllRegionsList({ ids: rowall.regionIds })
    .then(({ success, message, data }) => {
      if (!success) throw new Error(message);
      type Item = typeof areaList.value;
      areaList.value = data.reduce((p, v) => p.concat({ id: v.id, name: v.name, detail: v.description || "", active: v.active }), [] as Item);
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });

  getAllLocationsList({ ids: rowall.locationIds })
    .then(({ success, message, data }) => {
      if (!success) throw new Error(message);
      type Item = typeof locationList.value;
      locationList.value = data.reduce((p, v) => p.concat({ id: v.id, name: v.name, detail: v.description || "", active: v.active }), [] as Item);
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
  getAllUsersList({ ids: rowall.userIds })
    .then(({ success, message, data }) => {
      if (!success) throw new Error(message);
      type Item = typeof usersList.value;
      usersList.value = data;
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
  getAllUsersGroupsList({ ids: rowall.userGroupIds })
    .then(({ success, message, data }) => {
      if (!success) throw new Error(message);
      type Item = typeof userGroupsList.value;
      userGroupsList.value = data;
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
  getAllContactsList({ ids: rowall.contactIds })
    .then(({ success, message, data }) => {
      if (!success) throw new Error(message);
      contactsList.value = data;
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
}
//绑定客户
async function bindRelationCoustom(item: Record<string, any>) {
  try {
    let res: Promise<import("@/api/service/common").Response<any>> | null = null;
    res = sms_assign_customers({ id: form.id, customerIds: item.value, assignType: "insert" });
    if (res) {
      const { success, message } = await res;
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
      customConfig.value.dialogVisible = false;
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    getSmsStrategyDetails({ id: form.id }).then(({ success, data, total }) => {
      if (success) {
        getCostom(data);
      }
    });
  }
}
//绑定关联关系
async function bindRelation(item: Record<string, any>) {
  try {
    let res: Promise<import("@/api/service/common").Response<any>> | null = null;
    switch (item.type) {
      case "area":
        res = sms_assign_regions({ regionIds: item.value, id: form.id, assignType: "insert" });
        break;
      case "location":
        res = sms_assign_locations({ locationIds: item.value, id: form.id, assignType: "insert" });
        break;
      default:
        res = sms_assign_devices({ deviceIds: item.value, id: form.id, assignType: "insert" });
        break;
      case "userGroup":
        res = sms_assign_userGroups({ ids: item.value, id: form.id, assignType: "insert" });
        break;
      case "user":
        res = sms_assign_user({ ids: item.value, id: form.id, assignType: "insert" });
        break;
      case "contacts":
        res = sms_assign_contacts({ ids: item.value, id: form.id, assignType: "insert" });
        break;
    }
    if (res) {
      const { success, message } = await res;

      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
      DownSlaConfig.value.dialogVisible = false;
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    getSmsStrategyDetails({ id: form.id }).then(({ success, data, total }) => {
      if (success) {
        getDetail({ id: data.id }, data);
      }
    });
  }
}
//删除关联关系
async function delRelation(item: Record<string, any>) {
  try {
    let res: Promise<import("@/api/service/common").Response<any>> | null = null;
    switch (item.type) {
      case "area":
        res = sms_assign_regions({ regionIds: [item.id.toString()], id: form.id, assignType: "delete" });
        break;
      case "location":
        res = sms_assign_locations({ locationIds: [item.id.toString()], id: form.id, assignType: "delete" });
        break;
      default:
        res = sms_assign_devices({ deviceIds: [item.id.toString()], id: form.id, assignType: "delete" });
        break;
      case "userGroup":
        res = sms_assign_userGroups({ ids: [item.id.toString()], id: form.id, assignType: "delete" });
        break;
      case "user":
        res = sms_assign_user({ ids: [item.id.toString()], id: form.id, assignType: "delete" });
        break;
      case "contacts":
        res = sms_assign_contacts({ ids: [item.id.toString()], id: form.id, assignType: "delete" });
        break;
    }
    if (res) {
      const { success, message } = await res;
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    getSmsStrategyDetails({ id: form.id }).then(({ success, data, total }) => {
      if (success) {
        getDetail({ id: data.id }, data);
      }
    });
  }
}
</script>

<style lang="scss" scoped>
:deep(.ql-blank) {
  height: 184px;
}

.work-editor {
  padding: 0 20px;
  box-sizing: border-box;
}

.modules-item {
  .modules-title {
    color: rgba(32, 128, 255, 1);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}

.sun {
  background: rgb(26, 190, 107);

  :deep() .elstyle-button--text {
    color: #fff;
  }
}

.moon {
  background: #fff;

  :deep() .elstyle-button--text {
    color: rgb(153, 153, 153);
  }
}

.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;

  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}
</style>
