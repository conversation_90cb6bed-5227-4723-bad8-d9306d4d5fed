<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="getServiceManageList()" @current-change="getServiceManageList()">
      <template #right>
        <span class="tw-mx-[12px] tw-h-fit">
          <el-input v-model="ServiceSearch" @keyup.enter="searchList()" placeholder="搜索服务包" class="input-with-select">
            <template #append>
              <el-button :icon="Search" @click="searchList()" />
            </template>
          </el-input>
        </span>
        <span class="tw-h-fit">
          <el-button type="primary" v-if="userInfo.hasPermission('659635513484902400')" :icon="Plus" @click="handleCreate('add')">新增服务包</el-button>
        </span>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table v-loading="tableLoading" stripe :data="tableData" :height="tableHeight" style="width: 100%" :row-key="getRowKeys" :expand-row-keys="expands" @expand-change="handleExpandChange">
          <el-table-column type="expand">
            <template #default="{ row, expanded }">
              <el-card class="el-card-mt">
                <h2 style="font-weight: 700">服务项</h2>
                <el-row style="width: 100%">
                  <el-col :span="24" style="text-align: right; margin-bottom: 10px">
                    <el-button type="primary" v-if="userInfo.hasPermission('659635555297918976')" v-preventReClick @click="addServicePack()">
                      <el-icon class="el-icon--left"><Plus /></el-icon>添加服务项
                    </el-button>
                  </el-col>
                  <el-col>
                    <el-table stripe :show-header="false" border :data="ServicePackList" style="width: 100%">
                      <el-table-column align="left" label="名称" prop="servicePackage"> </el-table-column>
                      <el-table-column align="left" label="操作">
                        <template #default="scope">
                          <el-popconfirm :title="delTitlePack" @confirm="delLevelPack(scope.row)">
                            <template #reference>
                              <el-button type="text" v-if="userInfo.hasPermission('659635555297918976')" textColor="danger" v-preventReClick @click="delConfirmPack()">删除</el-button>
                            </template>
                          </el-popconfirm>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-col>
                  <el-col :span="24" style="margin-top: 20px; text-align: left">
                    <h2 style="font-weight: 700">服务等级</h2>
                  </el-col>
                  <el-col :span="24" style="margin-top: 10px; text-align: right">
                    <el-button v-if="userInfo.hasPermission('659635555297918976')" type="primary" v-preventReClick @click="addServiceSLa()">
                      <el-icon class="el-icon--left"><Plus /></el-icon>添加SLA
                    </el-button>
                  </el-col>
                  <el-col>
                    <el-table :show-header="false" border stripe :data="SlaDataList" style="width: 100%; margin-top: 10px">
                      <el-table-column align="left" label="名称" prop="slaName"> </el-table-column>
                      <el-table-column align="left" label="操作">
                        <template #default="scope">
                          <el-popconfirm :title="delTitleSLA" @confirm="delLevelSLA(scope.row)">
                            <template #reference>
                              <el-button type="text" v-if="userInfo.hasPermission('659635555297918976')" textColor="danger" v-preventReClick @click="delConfirmSLA()">删除</el-button>
                            </template>
                          </el-popconfirm>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-col>
                </el-row>
              </el-card>
            </template>
          </el-table-column>

          <TableColumn type="condition" :prop="`name`" :label="`名称`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="getServiceManageFilterList()" :formatter="formatterTable">
            <template #default="{ row }">
              <div style="font-size: 18; white-space: pre">{{ row.name }}</div>
            </template>
          </TableColumn>

          <TableColumn type="condition" :prop="`description`" :label="`描述`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByDescription" :filters="$filter0" @filter-change="getServiceManageFilterList()" :formatter="formatterTable">
            <template #default="{ row }">
              <div style="font-size: 18; white-space: pre">{{ row.description }}</div>
            </template>
          </TableColumn>
          <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="126">
            <template #default="{ row }">
              <span class="tw-h-fit tw-align-middle">
                <el-link v-if="(row.verifyPermissionIds || []).includes('659635555297918976')" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleCreate('edit', row as DataItem)">{{ $t("glob.edit") }}</el-link>
              </span>

              <span class="tw-h-fit tw-align-middle">
                <el-link v-if="(row.verifyPermissionIds || []).includes('659635670263791616')" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="DeleteServiceMan(row as DataItem)">{{ $t("glob.delete") }}</el-link>
              </span>
              <span v-if="(row.verifyPermissionIds || []).includes(服务管理中心_服务目录_安全)">
                <el-link :type="row.verifyPermissionIds.includes(服务管理中心_服务目录_安全) ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="row.verifyPermissionIds.includes(服务管理中心_服务目录_安全) ? false : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
  <serviceCreate :dialog="serviceManagementdialog" ref="serviceManagementRef" @dialogClose="dialogClose"></serviceCreate>

  <el-dialog title="选择服务项" v-model="dialogservicePack" width="30%">
    <el-form :model="servicePackform1" ref="servicePackform1">
      <el-form-item label="服务项" prop="servicePackageIds">
        <el-select filterable style="width: 300px" v-model="servicePackageIds" multiple placeholder="请选择" @change="ChangePackage()">
          <el-option v-for="item in servicePackageList" :key="item.servicePackage" :label="item.servicePackage" :value="item.servicePackage"> </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelService()">取 消</el-button>
        <el-button type="primary" @click="confirmService()" v-loading="btnloading">确 定</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog title="选择SLA" v-model="dialogSLA" width="30%">
    <el-form :model="servicePackform" ref="servicePackform">
      <!-- {{ serviceSLaIds }} -->
      <el-form-item label="SLA" prop="serviceSlaIds">
        <el-select filterable style="width: 300px" v-model="serviceSLaIds" multiple placeholder="请选择" @change="ChangeSLa()">
          <el-option v-for="item in serviceSlaList" :key="item.ruleId" :label="item.ruleName" :value="item.ruleId"> </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelServiceLv()">取 消</el-button>
        <el-button type="primary" @click="confirmServiceLv()" v-loading="btnloading">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete } from "@element-plus/icons-vue";

/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox, ElText } from "element-plus";
import pageTemplate from "@/components/pageTemplate.vue";

import { QuestionFilled } from "@element-plus/icons-vue";
import { getSlaDefault } from "@/views/pages/apis/SlaConfig";
import treeAuth from "@/components/treeAuth/index.vue";
import getUserInfo from "@/utils/getUserInfo";
// import { state } from "./helper";
import { slaStatus } from "./common";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";
import serviceCreate from "./serviceCreate.vue";
import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";

/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";
import { 服务管理中心_SLA配置_新增, 服务管理中心_SLA配置_编辑, 服务管理中心_SLA配置_删除 } from "@/views/pages/permission";

import { 服务管理中心_服务目录_安全, 服务管理中心_服务目录_可读, 服务管理中心_服务目录_新增, 服务管理中心_服务目录_编辑, 服务管理中心_服务目录_删除 } from "@/views/pages/permission";

import { getServiceManagementPage, DelServiceManagement, getServiceManagementPageFilter, getServicePack, getServiceLv, getServiceSLA, getServiceRelation, getServiceRemove, getSPackList, getServiceRemovePack, getServicePackRelation } from "@/views/pages/apis/ServiceManagement";

/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "ServiceManagement" });

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const siteConfig = useSiteConfig();
const userInfoS = getUserInfo();
const userInfo = ((key) => {
  switch (key) {
    case process.env["APP_SUPER_PLATFORM"]:
      return useSuperInfo();
    case process.env["APP_ADMIN_PLATFORM"]:
      return useAdminInfo();
    case process.env["APP_USERS_PLATFORM"]:
      return useUsersInfo();
    default:
      return null;
  }
})(siteConfig.current);

// const $filter0 = ref([
//   { text: "包含", value: "include" },
//   { text: "不包含", value: "exclude" },
//   { text: "等于", value: "eq" },
//   { text: "不等于", value: "ne" },
// ]);
const $filter0 = ref(exoprtMatch1);

const searchForm = ref<Record<string, any>>({
  eqName: [] /* 等于的服务包名称 */,
  includeName: [] /* 包含的服务包名称 */,
  nameFilterRelation: "AND" /* 服务包名称过滤关系(AND,OR) */,
  neName: [] /* 不等于的服务包名称 */,
  excludeName: [] /* 不包含的服务包名称 */,

  eqDescription: [] /* 等于的服务包描述 */,
  includeDescription: [] /* 包含的服务包描述 */,
  descriptionFilterRelation: "AND" /* 服务包描述过滤关系(AND,OR) */,
  neDescription: [] /* 不等于的服务包描述 */,
  excludeDescription: [] /* 不包含的服务包描述 */,
});

const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByName) === "include") value0 = searchForm.value.includeName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = searchForm.value.excludeName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = searchForm.value.eqName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = searchForm.value.neName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = searchForm.value.includeName[searchForm.value.includeName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = searchForm.value.excludeName[searchForm.value.excludeName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = searchForm.value.eqName[searchForm.value.eqName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = searchForm.value.neName[searchForm.value.neName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: searchForm.value.nameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.nameFilterRelation = v.relation;
    searchForm.value.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByDescription = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByDescription) === "include") value0 = searchForm.value.includeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "exclude") value0 = searchForm.value.excludeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "eq") value0 = searchForm.value.eqDescription[0] || "";
    if (toValue(searchType0ByDescription) === "ne") value0 = searchForm.value.neDescription[0] || "";
    let value1 = "";
    if (toValue(searchType1ByDescription) === "include") value1 = searchForm.value.includeDescription[searchForm.value.includeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "exclude") value1 = searchForm.value.excludeDescription[searchForm.value.excludeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "eq") value1 = searchForm.value.eqDescription[searchForm.value.eqDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "ne") value1 = searchForm.value.neDescription[searchForm.value.neDescription.length - 1] || "";
    return {
      type0: toValue(searchType0ByDescription),
      type1: toValue(searchType1ByDescription),
      relation: searchForm.value.descriptionFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByDescription.value = v.type0 as typeof searchType0ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchType1ByDescription.value = v.type1 as typeof searchType1ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.descriptionFilterRelation = v.relation;
    searchForm.value.includeDescription = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeDescription = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqDescription = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neDescription = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const tableLoading = ref(false);
const serviceManagementdialog = ref(false);
const serviceManagementRef = ref("");
const slaHelpdialog = ref(false);
const ServiceSearch = ref("");

//状态值
const slaEnable = ref(null);
// 群通知状态
const GroupState = ref(false);
const tableData = ref<DataItem[]>([]);
const expands = ref<string[]>([]);
const containerId = ref("");
const dialogVisibleshow = ref(false);
const dialogservicePack = ref(false);
const dialogSLA = ref(false);
const serviceSlaList = ref<string[]>([]);
const serviceSLaIds = ref([]);
const servicePackageList = ref<string[]>([]);
const servicePackageIds = ref([]);
const expandedserviceCatalogId = ref("");
const expandedserviceArrSLa = ref([]);
const expandedserviceArrPackage = ref([]);
const SlaDataList = ref([]);
const ServicePackList = ref([]);
const delTitleSLA = ref("");
const delTitlePack = ref("");
// const serviceSLaIds = ref<string[]>([]);
// const servicePackform = ref<Record<string, any>>({
//   serviceSLaIds: [],
// });

const treeAuthRef = ref<InstanceType<typeof treeAuth>>();

const paging = reactive({
  pageNumber: 1,
  pageSize: 50,
  total: 0,
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  getServiceManageList();
}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

function formatterTable(_row, _col, v) {
  console.log(v, "000000");

  switch (_col.property) {
    default:
      return v || "--";
  }
}
//搜索
function searchList() {
  paging.pageNumber = 1;
  getServiceManageList();
}
function getRowKeys(row) {
  return row.id;
}
async function handleExpandChange(row, expandedRows) {
  expandedserviceCatalogId.value = row.id;
  if (expandedRows.length) {
    //展开
    expands.value = [];
    if (row) {
      expands.value.push(row.id);
    }
  } else {
    expands.value = [];
  }
  await getServicePackList(row.id);
  await getSlaList(row.id);
}

function getServiceManageList() {
  let data = {
    ...paging,
    containerId: userInfoS.currentTenant.containerId,
    fullQuery: ServiceSearch.value,
    queryPermissionId: 服务管理中心_服务目录_可读,
    verifyPermissionIds: [服务管理中心_服务目录_安全, 服务管理中心_服务目录_新增, 服务管理中心_服务目录_编辑, 服务管理中心_服务目录_删除].join(),
  };
  tableLoading.value = true;
  getServiceManagementPage(data)
    .then(({ success, data, page, size, total }) => {
      if (success) {
        tableData.value = [...data];

        paging.total = Number(total);
        paging.pageNumber = page;
        paging.pageSize = size;
      }
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {
      tableLoading.value = false;
    });
}
// 过滤
function getServiceManageFilterList() {
  let data = {
    ...paging,
    containerId: userInfoS.currentTenant.containerId,
    queryPermissionId: "515413313438351360",
    verifyPermissionIds: "515413363665141760,515413387727863808,6121692631673077716",
    ...searchForm.value,
  };
  tableLoading.value = true;
  getServiceManagementPageFilter(data)
    .then(({ success, data, page, size, total }) => {
      if (success) {
        tableData.value = [...data];
        paging.total = Number(total);
        paging.pageNumber = page;
        paging.pageSize = size;
      }
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {
      tableLoading.value = false;
    });
}
// 新增编辑SLA
async function handleCreate(type, row) {
  try {
    ctx.refs.serviceManagementRef.type = type;
    if (type === "add") {
      ctx.refs.serviceManagementRef.title = "新增服务包";
    } else {
      ctx.refs.serviceManagementRef.form = {
        name: row.name,
        description: row.description,
        id: row.id,
      };
      ctx.refs.serviceManagementRef.title = "编辑服务包";
    }
    serviceManagementdialog.value = true;
  } catch (error) {
    /*  */
  } finally {
    /*  */
  }
}
//关闭弹框
function dialogClose(bool) {
  serviceManagementdialog.value = bool;
  getServiceManageList();
}

//删除
function DeleteServiceMan(row: Partial<DataItem>) {
  ElMessageBox({
    title: "删除服务包",
    message: h("p", null, [h("span", null, "是否删除服务包 "), h("span", { style: "color: #3E97FF;" }, row.name)]),
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
    showCancelButton: true,
  })
    .then(() => {
      DelServiceManagement({ id: row.id })
        .then(({ success, data, message }) => {
          if (!success) throw new Error(message);
          paging.pageNumber = 1;
          ElMessage.success("操作成功");
          getServiceManageList();
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        })
        .finally(() => {
          tableLoading.value = false;
        });
    })
    .catch(() => {});
}
// 服务包----------------------Start------------

// 列表
function getServicePackList(serviceCatalogId) {
  getSPackList({ id: serviceCatalogId })
    .then(({ success, data, page, size, total }) => {
      ServicePackList.value = data;
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {});
}
//查询可添加的服务包
function getAddServicePack(serviceCatalogId) {
  getServicePack({ id: serviceCatalogId })
    .then((res) => {
      servicePackageList.value = res;
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {
      tableLoading.value = false;
    });
}
function addServicePack() {
  dialogservicePack.value = true;
  servicePackageIds.value = [];
  getAddServicePack(expandedserviceCatalogId.value);
}
function cancelService() {
  dialogservicePack.value = false;
  servicePackageIds.value = [];
}
function ChangePackage() {
  const selectedItems = servicePackageIds.value.map((id: number) => {
    return servicePackageList.value.find((item) => item.servicePackage === id);
  });

  // 提取需要的字段
  const extractedData = selectedItems.map((item) => {
    return {
      servicePackage: item.servicePackage,
      description: item.description,
      businessID: item.businessID,
      serviceCatalogId: expandedserviceCatalogId.value,
    };
  });

  expandedserviceArrPackage.value = extractedData;
}
//提交服务包
function confirmService() {
  getServicePackRelation(expandedserviceArrPackage.value)
    .then((res) => {
      ElMessage.success("操作成功");
      dialogservicePack.value = false;
      getServicePackList(expandedserviceCatalogId.value);
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {});
}

function delConfirmPack() {
  delTitlePack.value = "确认删除当前数据吗？";
}
function delLevelPack(row) {
  getServiceRemovePack({
    serviceCatalogId: expandedserviceCatalogId.value,
    servicePackage: row.servicePackage,
  })
    .then((res) => {
      ElMessage.success("操作成功");
      getServicePackList(expandedserviceCatalogId.value);
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {});
}

// 服务包----------------------End------------

// 服务等级----------------------------------
//查询可添加的SLA
function getAddSla(serviceCatalogId) {
  let slaPrarms = {
    id: serviceCatalogId,
    containerId: userInfoS.currentTenant.containerId,
    queryPermissionId: "515413313438351360",
    verifyPermissionIds: "515413363665141760,515413387727863808,612169263167307776",
  };
  getServiceSLA(slaPrarms)
    .then((res) => {
      serviceSlaList.value = res;
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {});
}
//列表
function getSlaList(serviceCatalogId) {
  getServiceLv({ id: serviceCatalogId })
    .then(({ success, data, page, size, total }) => {
      SlaDataList.value = data;
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {
      // tableLoading.value = false;
    });
}
function ChangeSLa(val) {
  const selectedItems = serviceSLaIds.value.map((id: number) => {
    return serviceSlaList.value.find((item) => item.ruleId === id);
  });

  // 提取需要的字段
  const extractedData = selectedItems.map((item) => {
    return {
      slaId: item.ruleId,
      slaName: item.ruleName,
      slaDesc: item.ruleDesc,
      serviceCatalogId: expandedserviceCatalogId.value,
    };
  });

  expandedserviceArrSLa.value = extractedData;
}

function addServiceSLa() {
  dialogSLA.value = true;
  serviceSLaIds.value = [];
  getAddSla(expandedserviceCatalogId.value);
}
function cancelServiceLv() {
  dialogSLA.value = false;
  serviceSLaIds.value = [];
}
//提交SLa
function confirmServiceLv() {
  getServiceRelation(expandedserviceArrSLa.value)
    .then((res) => {
      ElMessage.success("操作成功");
      dialogSLA.value = false;
      getSlaList(expandedserviceCatalogId.value);
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {});
}

function delConfirmSLA() {
  delTitleSLA.value = "确认删除当前数据吗？";
}
function delLevelSLA(row) {
  getServiceRemove({
    serviceCatalogId: expandedserviceCatalogId.value,
    slaId: row.slaId,
  })
    .then((res) => {
      ElMessage.success("操作成功");
      getSlaList(expandedserviceCatalogId.value);
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {});
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.sla-config {
  display: flex;
  align-items: center;
  h2 {
    display: flex;
    align-items: center;
    height: 100%;
    font-size: 14px;
    padding-left: 10px;
    box-sizing: border-box;
    font-weight: 700;
    margin-top: 15px;
  }
}
</style>
