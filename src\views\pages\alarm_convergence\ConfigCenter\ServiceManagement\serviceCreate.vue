<template>
  <div>
    <el-dialog :title="title" v-model="dialogFormVisible" :before-close="handleClose">
      <el-form :model="form" :rules="rules" ref="ruleForm">
        <el-form-item label="名称:" :label-width="formLabelWidth" prop="name">
          <el-input v-model="form.name" autocomplete="off" placeholder="请输入名称"></el-input>
        </el-form-item>
        <el-form-item label="描述:" :label-width="formLabelWidth" prop="description">
          <el-input type="textarea" v-model="form.description" autocomplete="off" :rows="2" placeholder="请输入描述"></el-input>
        </el-form-item>
        <el-form-item v-if="type == 'add'" label="选择安全目录:" :label-width="formLabelWidth">
          <treeAuth ref="treeAuthRef" :treeStyle="treeStyle"></treeAuth>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel()">取 消</el-button>
          <el-button type="primary" @click="confirm('ruleForm')" v-loading="btnloading">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

import { AddServiceManagement, EditServiceManagement } from "@/views/pages/apis/ServiceManagement";

import treeAuth from "@/components/treeAuth/index.vue";
import getUserInfo from "@/utils/getUserInfo";

export default defineComponent({
  name: "serviceCreate",
  components: {
    treeAuth,
  },
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      form: {
        containerId: "",
        name: "",
        description: "",
        id: "",
      },
      containerIdS: null,
      btnloading: false,
      rules: {
        name: [{ required: true, message: "请输入服务目录名称", trigger: "blur" }],
      },
      title: "",
      checked: false,
      dialogFormVisible: false,
      formLabelWidth: "130px",
      type: "",
      value: "",
      disabled: "",
      treeStyle: {
        width: "300px",
        height: "150px",
      },
    };
  },
  watch: {
    dialog(val) {
      this.dialogFormVisible = val;
    },
    type(val) {
      if (val === "add") {
        for (var key in this.form) {
          this.form[key] = null;
        }
      }
    },
  },
  created() {},
  methods: {
    confirm(formName) {
      var treeItemId = "";
      if (this.type == "add") {
        this.form.containerId = this.$refs.treeAuthRef.treeItem.id;
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        treeItemId = this.form.containerId || this.$refs.treeAuthRef.treeItem.id;

        if (!treeItemId) {
          ElMessage.error("请选择安全目录");
          return;
        }
      } else {
        treeItemId = "";
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.type === "add") {
            // if (!this.form.containerId) {
            //   ElMessage.error("请选择安全目录");
            //   return;
            // }
            this.btnloading = true;
            AddServiceManagement(this.form)
              .then((res) => {
                if (res.success) {
                  ElMessage.success("新增成功");
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                  this.$refs.treeAuthRef.getSafeContaine();
                  this.$refs.treeAuthRef.treeId = -1;
                  this.$refs.treeAuthRef.treeItem.id = "";
                  this.btnloading = false;
                } else {
                  this.btnloading = false;
                  ElMessage.error(JSON.parse(res.data)?.message);
                  this.$emit("dialogClose", false);
                  this.$refs.treeAuthRef.treeItem.id = "";
                  this.$refs[formName].resetFields();
                  this.$refs.treeAuthRef.getSafeContaine();
                  this.$refs.treeAuthRef.treeId = -1;
                }
              })
              .catch((e) => {
                this.btnloading = false;
                if (e instanceof Error) ElMessage.error(e.message);
                this.$refs.treeAuthRef.getSafeContaine();
                this.$refs.treeAuthRef.treeId = -1;
                this.$refs[formName].resetFields();
                this.$refs.treeAuthRef.treeItem.id = "";
              });
          } else {
            this.btnloading = true;
            EditServiceManagement(this.form)
              .then((res) => {
                if (res.success) {
                  this.btnloading = false;
                  ElMessage.success("修改成功");
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                } else {
                  this.btnloading = false;
                  ElMessage.error(JSON.parse(res.data)?.message);
                  this.$emit("dialogClose", false);
                }
              })
              .catch((e) => {
                this.btnloading = false;
                if (e instanceof Error) ElMessage.error(e.message);
              });
          }
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      if (this.type == "add") {
        this.$refs.treeAuthRef.getSafeContaine();
        this.$refs.treeAuthRef.treeId = -1;
      }
    },
    cancel() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      if (this.type == "add") {
        this.$refs.treeAuthRef.getSafeContaine();
        this.$refs.treeAuthRef.treeId = -1;
      }
    },
    blurChange(data) {
      this.tags.push(data);
    },
    tagClose(index) {
      this.tags.splice(index, 1);
    },
  },
  expose: ["type", "disabled", "title", "form", "value"],
});
</script>
