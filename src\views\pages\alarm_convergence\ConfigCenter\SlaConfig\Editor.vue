<template>
  <el-form :model="form" :style="{ marginTop: '10px' }" label-position="left" class="edit_sla_config">
    <el-card class="el-card-mt">
      <el-row>
        <el-form-item style="width: 100%">
          <el-row style="width: 100%">
            <!-- <el-col class="bold" :span="3" style="text-align: left">覆盖时间：</el-col>
            <el-col class="bold" :span="12" style="text-align: right">选择时区：</el-col>
            <el-col :span="6">
              <el-select v-model="coverTimeCfg.timeZone" filterable placeholder="请选择" :style="basicClassInputDown" style="margin-left: 30px">
                <el-option v-for="item in timeZone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"> </el-option>
              </el-select>
            </el-col> -->

            <el-col :span="12">
              <el-form-item label="覆盖时间：">
                <!--  -->
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="选择时区：">
                <el-select v-model="coverTimeCfg.timeZone" filterable placeholder="请选择" class="tw-w-full" :empty-values="[null]" :value-on-clear="''">
                  <el-option :label="`自动时区`" :value="'自动时区'"> </el-option>
                  <el-option v-for="item in timeZone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"> </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <div style="width: 100%" class="support-table-content" ref="tableContentRef">
                <el-table stripe border :data="coverTimeCfg.coverWorkTime" style="width: 100%; margin-top: 30px" @header-click="(column, $event) => handleClick({ column, $event })">
                  <el-table-column align="left" prop="week" width="70" fixed="left">
                    <template #default="scope">
                      <div @click="handleSelectTime('all', scope.$index, scope.row)">
                        {{ scope.row.weekDay == 1 ? "周一" : scope.row.weekDay == 2 ? "周二" : scope.row.weekDay == 3 ? "周三" : scope.row.weekDay == 4 ? "周四" : scope.row.weekDay == 5 ? "周五" : scope.row.weekDay == 6 ? "周六" : "周日" }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" :width="tableWidth" v-for="(item, key) in 24" :key="`h-${key}`" :label="String(key)">
                    <template #default="scope">
                      <div @click="handleSelectTime(key, scope.$index, scope.row)" style="width: 100%; height: 100%">
                        <el-button type="text" style="font-size: 30px; color: rgb(26, 190, 107)">
                          <el-icon v-if="scope.row.workTime && scope.row.workTime.length && scope.row.workTime.includes(key)">
                            <Check></Check>
                          </el-icon>
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>

            <el-col class="bold tw-my-[10px]" :span="24" style="text-align: right" v-if="userInfo.hasPermission(服务管理中心_SLA配置_编辑)">
              <el-button @click="baocunLevel" type="primary">{{ t("glob.Save") }}</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item class="things" style="width: 100%">
          <el-row style="width: 100%">
            <el-col class="bold" :span="3" style="text-align: left"> 事件响应时间等级：</el-col>
            <el-col :span="21" style="text-align: right">
              <el-button :disabled="!userInfo.hasPermission(PERMISSION.group515413286225707008.editor)" type="primary" :icon="ElIconPlus" @click="addLevel('response')">新增等级</el-button>
              <!-- <el-button type="primary" :icon="ElIconPlus" @click="addLevel('response')">Help</el-button> -->
            </el-col>
            <el-col>
              <el-table stripe :data="responseTimeList" style="width: 100%; margin-top: 30px">
                <el-table-column align="left" label="优先级">
                  <template #default="{ row }">
                    <span v-if="row.state" class="state" :style="{ color: '#fff', backgroundColor: eventPriorityColor[row.priority as keyof typeof eventPriorityColor].color }">{{ row.priority }}</span>
                    <el-select v-else v-model="row.priority">
                      <el-option v-for="item in responseData" :key="item.value" :label="item.value" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="持续时间" width="300">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].day ? scope.row.resolves[0].day + "day" : "" }}
                      {{ scope.row.resolves[0].hour ? scope.row.resolves[0].hour + "h" : "" }}
                      {{ scope.row.resolves[0].minute ? scope.row.resolves[0].minute + "min" : "" }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input-number :min="0" v-model="scope.row.resolves[0].day" :max="10000000000"> </el-input-number>
                      day
                      <el-input-number :min="0" v-model="scope.row.resolves[0].hour" :max="23"> </el-input-number>
                      h
                      <el-input-number :min="0" v-model="scope.row.resolves[0].minute" :max="59"> </el-input-number>
                      min
                    </div>
                  </template>
                </el-table-column>

                <el-table-column align="left" label="提醒" width="300">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].warnDay ? scope.row.resolves[0].warnDay + "day" : "" }}
                      {{ scope.row.resolves[0].warnHour ? scope.row.resolves[0].warnHour + "h" : "" }}
                      {{ scope.row.resolves[0].warnMinute ? scope.row.resolves[0].warnMinute + "min" : "" }}
                    </div>
                    <div v-show="!scope.row.state">
                      <div>
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnDay" :max="10000000000"> </el-input-number>
                        day
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnHour" :max="23"> </el-input-number>
                        h
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnMinute" :max="59"> </el-input-number>
                        min
                      </div>
                      <div>
                        <el-checkbox v-model="scope.row.resolves[0].repeatWarn" label="重复提醒" />
                      </div>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column align="left" label="SLA状态" prop="urgencyType">
                  <template #default="scope">
                    <span
                      class="state"
                      v-show="scope.row.state"
                      :style="{
                        color: '#fff',
                        backgroundColor: slaStateColor[scope.row.resolves[0].urgencyType?.toLowerCase()],
                      }"
                    >
                      <span v-show="scope.row.resolves[0].urgencyType">
                        {{ scope.row.resolves[0].urgencyType?.slice(0, 1).toUpperCase() + scope.row.resolves[0].urgencyType?.slice(1).toLowerCase() }}
                      </span>
                      <span v-show="!scope.row.resolves[0].urgencyType"> </span>
                    </span>
                    <el-select v-show="!scope.row.state" v-model="scope.row.resolves[0].urgencyType">
                      <el-option v-for="item in statusData" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="名称">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].name }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].name"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="描述">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].description }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].description"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="定义">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].definition }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].definition"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="操作">
                  <template #default="scope">
                    <template v-if="scope.row.state">
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" @click="editLevel('response', scope.$index, scope.row)" style="margin-right: 10px">编辑</el-button>
                      <el-popconfirm :title="delTitle" @confirm="delConfirm('response', scope.$index, scope.row)">
                        <template #reference>
                          <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" textColor="danger" @click="delLevel('response', scope.$index, scope.row)">删除</el-button>
                        </template>
                      </el-popconfirm>
                    </template>

                    <template v-else>
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" @click="confirmLevel('response', scope.$index, scope.row)">保存</el-button>
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" @click="getSlaDetail(props.detail.ruleId)">取消</el-button>
                    </template>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>

          <el-row style="margin-top: 20px; width: 100%">
            <el-col class="bold" :span="3" style="text-align: left"> 事件处理时间等级：</el-col>
            <el-col :span="21" style="text-align: right">
              <el-button :disabled="!userInfo.hasPermission(PERMISSION.group515413286225707008.editor)" type="primary" :icon="ElIconPlus" @click="addLevel('resolve')">新增等级</el-button>
            </el-col>
            <el-col>
              <el-table stripe :data="handelTimeList" style="width: 100%; margin-top: 30px">
                <el-table-column align="left" label="优先级">
                  <template #default="{ row }">
                    <span
                      v-if="row.state"
                      class="state"
                      :style="{
                        color: '#fff',
                        backgroundColor: (eventPriorityColor[row.priority as keyof typeof eventPriorityColor] || {}).color,
                      }"
                      >{{ row.priority }}</span
                    >
                    <el-select v-else v-model="row.priority">
                      <el-option v-for="item in responseData" :key="item.value" :label="item.value" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="持续时间" width="300">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].day ? scope.row.resolves[0].day + "day" : "" }}
                      {{ scope.row.resolves[0].hour ? scope.row.resolves[0].hour + "h" : "" }}
                      {{ scope.row.resolves[0].minute ? scope.row.resolves[0].minute + "min" : "" }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input-number :min="0" v-model="scope.row.resolves[0].day" :max="10000000000"> </el-input-number>
                      day
                      <el-input-number :min="0" v-model="scope.row.resolves[0].hour" :max="23"> </el-input-number>
                      h
                      <el-input-number :min="0" v-model="scope.row.resolves[0].minute" :max="59"> </el-input-number>
                      min
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="提醒" width="300">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].warnDay ? scope.row.resolves[0].warnDay + "day" : "" }}
                      {{ scope.row.resolves[0].warnHour ? scope.row.resolves[0].warnHour + "h" : "" }}
                      {{ scope.row.resolves[0].warnMinute ? scope.row.resolves[0].warnMinute + "min" : "" }}
                    </div>
                    <div v-show="!scope.row.state">
                      <div>
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnDay" :max="10000000000"> </el-input-number>
                        day
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnHour" :max="23"> </el-input-number>
                        h
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnMinute" :max="59"> </el-input-number>
                        min
                      </div>
                      <div>
                        <el-checkbox v-model="scope.row.resolves[0].repeatWarn" label="重复提醒" />
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="SLA状态" prop="urgencyType">
                  <template #default="scope">
                    <span
                      class="state"
                      v-show="scope.row.state"
                      :style="{
                        color: '#fff',
                        backgroundColor: slaStateColor[scope.row.resolves[0].urgencyType?.toLowerCase()],
                      }"
                    >
                      <span v-show="scope.row.resolves[0].urgencyType">{{ scope.row.resolves[0].urgencyType?.slice(0, 1).toUpperCase() + scope.row.resolves[0].urgencyType?.slice(1).toLowerCase() }} </span>
                      <span v-show="!scope.row.resolves[0].urgencyType"></span>
                    </span>
                    <el-select v-show="!scope.row.state" v-model="scope.row.resolves[0].urgencyType">
                      <el-option v-for="item in statusData" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="名称">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].name }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].name"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="描述">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].description }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].description"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="定义">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].definition }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].definition"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="操作">
                  <template #default="scope">
                    <template v-if="scope.row.state">
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="scope.row.state" @click="editLevel('resolve', scope.$index, scope.row)" style="margin-right: 10px">编辑</el-button>
                      <el-popconfirm :title="delTitle" @confirm="delConfirm('resolve', scope.$index, scope.row)">
                        <template #reference>
                          <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" textColor="danger" @click="delLevel('resolve', scope.$index, scope.row)">删除</el-button>
                        </template>
                      </el-popconfirm>
                    </template>
                    <template v-else>
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="!scope.row.state" @click="confirmLevel('resolve', scope.$index, scope.row)">保存</el-button>
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="!scope.row.state" @click="getSlaDetail(props.detail.ruleId)">取消</el-button>
                    </template>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>

            <el-col class="bold" :span="3" style="text-align: left"> 事件处理更新时间等级：</el-col>
            <el-col :span="21" style="text-align: right">
              <el-button :disabled="!userInfo.hasPermission(PERMISSION.group515413286225707008.editor)" type="primary" :icon="ElIconPlus" @click="addLevel('eventRespUpdateTime')">新增等级</el-button>
            </el-col>
            <el-col>
              <el-table stripe :data="eventRespUpdateTimeList" style="width: 100%; margin-top: 30px">
                <el-table-column align="left" label="优先级">
                  <template #default="{ row }">
                    <span
                      v-if="row.state"
                      class="state"
                      :style="{
                        color: '#fff',
                        backgroundColor: (eventPriorityColor[row.priority as keyof typeof eventPriorityColor] || {}).color,
                      }"
                      >{{ row.priority }}</span
                    >
                    <el-select v-else v-model="row.priority">
                      <el-option v-for="item in responseData" :key="item.value" :label="item.value" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="持续时间" width="300">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].day ? scope.row.resolves[0].day + "day" : "" }}
                      {{ scope.row.resolves[0].hour ? scope.row.resolves[0].hour + "h" : "" }}
                      {{ scope.row.resolves[0].minute ? scope.row.resolves[0].minute + "min" : "" }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input-number :min="0" v-model="scope.row.resolves[0].day" :max="10000000000"> </el-input-number>
                      day
                      <el-input-number :min="0" v-model="scope.row.resolves[0].hour" :max="23"> </el-input-number>
                      h
                      <el-input-number :min="0" v-model="scope.row.resolves[0].minute" :max="59"> </el-input-number>
                      min
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="提醒" width="300">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].warnDay ? scope.row.resolves[0].warnDay + "day" : "" }}
                      {{ scope.row.resolves[0].warnHour ? scope.row.resolves[0].warnHour + "h" : "" }}
                      {{ scope.row.resolves[0].warnMinute ? scope.row.resolves[0].warnMinute + "min" : "" }}
                    </div>
                    <div v-show="!scope.row.state">
                      <div>
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnDay" :max="10000000000"> </el-input-number>
                        day
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnHour" :max="23"> </el-input-number>
                        h
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnMinute" :max="59"> </el-input-number>
                        min
                      </div>
                      <div>
                        <el-checkbox v-model="scope.row.resolves[0].repeatWarn" label="重复提醒" />
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <!-- <el-table-column align="left" label="提醒" width="300">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].day ? scope.row.resolves[0].day + "day" : "" }}
                      {{ scope.row.resolves[0].hour ? scope.row.resolves[0].hour + "h" : "" }}
                      {{ scope.row.resolves[0].minute ? scope.row.resolves[0].minute + "min" : "" }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input-number :min="0" v-model="scope.row.resolves[0].day" :max="10000000000"> </el-input-number>
                      day
                      <el-input-number :min="0" v-model="scope.row.resolves[0].hour" :max="23"> </el-input-number>
                      h
                      <el-input-number :min="0" v-model="scope.row.resolves[0].minute" :max="59"> </el-input-number>
                      min
                    </div>
                  </template>
                </el-table-column> -->
                <el-table-column align="left" label="SLA状态" prop="urgencyType">
                  <template #default="scope">
                    <span
                      class="state"
                      v-show="scope.row.state"
                      :style="{
                        color: '#fff',
                        backgroundColor: slaStateColor[scope.row.resolves[0].urgencyType?.toLowerCase()],
                      }"
                    >
                      <span v-show="scope.row.resolves[0].urgencyType">{{ scope.row.resolves[0].urgencyType?.slice(0, 1).toUpperCase() + scope.row.resolves[0].urgencyType?.slice(1).toLowerCase() }} </span>
                      <span v-show="!scope.row.resolves[0].urgencyType"></span>
                    </span>
                    <el-select v-show="!scope.row.state" v-model="scope.row.resolves[0].urgencyType">
                      <el-option v-for="item in statusData" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="名称">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].name }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].name"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="描述">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].description }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].description"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="定义">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].definition }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].definition"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="操作">
                  <template #default="scope">
                    <template v-if="scope.row.state">
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="scope.row.state" @click="editLevel('eventRespUpdateTime', scope.$index, scope.row)" style="margin-right: 10px">编辑</el-button>
                      <el-popconfirm :title="delTitle" @confirm="delConfirm('eventRespUpdateTime', scope.$index, scope.row)">
                        <template #reference>
                          <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" textColor="danger" @click="delLevel('eventRespUpdateTime', scope.$index, scope.row)">删除</el-button>
                        </template>
                      </el-popconfirm>
                    </template>
                    <template v-else>
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="!scope.row.state" @click="confirmLevel('eventRespUpdateTime', scope.$index, scope.row)">保存</el-button>
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="!scope.row.state" @click="getSlaDetail(props.detail.ruleId)">取消</el-button>
                    </template>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>

            <el-col class="bold" :span="3" style="text-align: left"> 事件挂起更新时间等级：</el-col>
            <el-col :span="21" style="text-align: right">
              <el-button :disabled="!userInfo.hasPermission(PERMISSION.group515413286225707008.editor)" type="primary" :icon="ElIconPlus" @click="addLevel('eventSuspendUpdateTime')">新增等级</el-button>
            </el-col>
            <el-col>
              <el-table stripe :data="eventSuspendUpdateTimeList" style="width: 100%; margin-top: 30px">
                <el-table-column align="left" label="优先级">
                  <template #default="{ row }">
                    <span
                      v-if="row.state"
                      class="state"
                      :style="{
                        color: '#fff',
                        backgroundColor: (eventPriorityColor[row.priority as keyof typeof eventPriorityColor] || {}).color,
                      }"
                      >{{ row.priority }}</span
                    >
                    <el-select v-else v-model="row.priority">
                      <el-option v-for="item in responseData" :key="item.value" :label="item.value" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="持续时间" width="300">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].day ? scope.row.resolves[0].day + "day" : "" }}
                      {{ scope.row.resolves[0].hour ? scope.row.resolves[0].hour + "h" : "" }}
                      {{ scope.row.resolves[0].minute ? scope.row.resolves[0].minute + "min" : "" }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input-number :min="0" v-model="scope.row.resolves[0].day" :max="10000000000"> </el-input-number>
                      day
                      <el-input-number :min="0" v-model="scope.row.resolves[0].hour" :max="23"> </el-input-number>
                      h
                      <el-input-number :min="0" v-model="scope.row.resolves[0].minute" :max="59"> </el-input-number>
                      min
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="提醒" width="300">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].warnDay ? scope.row.resolves[0].warnDay + "day" : "" }}
                      {{ scope.row.resolves[0].warnHour ? scope.row.resolves[0].warnHour + "h" : "" }}
                      {{ scope.row.resolves[0].warnMinute ? scope.row.resolves[0].warnMinute + "min" : "" }}
                    </div>
                    <div v-show="!scope.row.state">
                      <div>
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnDay" :max="10000000000"> </el-input-number>
                        day
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnHour" :max="23"> </el-input-number>
                        h
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnMinute" :max="59"> </el-input-number>
                        min
                      </div>
                      <div>
                        <el-checkbox v-model="scope.row.resolves[0].repeatWarn" label="重复提醒" />
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <!-- <el-table-column align="left" label="提醒" width="300">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].day ? scope.row.resolves[0].day + "day" : "" }}
                      {{ scope.row.resolves[0].hour ? scope.row.resolves[0].hour + "h" : "" }}
                      {{ scope.row.resolves[0].minute ? scope.row.resolves[0].minute + "min" : "" }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input-number :min="0" v-model="scope.row.resolves[0].day" :max="10000000000"> </el-input-number>
                      day
                      <el-input-number :min="0" v-model="scope.row.resolves[0].hour" :max="23"> </el-input-number>
                      h
                      <el-input-number :min="0" v-model="scope.row.resolves[0].minute" :max="59"> </el-input-number>
                      min
                    </div>
                  </template>
                </el-table-column> -->
                <el-table-column align="left" label="SLA状态" prop="urgencyType">
                  <template #default="scope">
                    <span
                      class="state"
                      v-show="scope.row.state"
                      :style="{
                        color: '#fff',
                        backgroundColor: slaStateColor[scope.row.resolves[0].urgencyType?.toLowerCase()],
                      }"
                    >
                      <span v-show="scope.row.resolves[0].urgencyType">{{ scope.row.resolves[0].urgencyType?.slice(0, 1).toUpperCase() + scope.row.resolves[0].urgencyType?.slice(1).toLowerCase() }} </span>
                      <span v-show="!scope.row.resolves[0].urgencyType"></span>
                    </span>
                    <el-select v-show="!scope.row.state" v-model="scope.row.resolves[0].urgencyType">
                      <el-option v-for="item in statusData" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="名称">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].name }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].name"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="描述">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].description }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].description"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="定义">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].definition }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].definition"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="操作">
                  <template #default="scope">
                    <template v-if="scope.row.state">
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="scope.row.state" @click="editLevel('eventSuspendUpdateTime', scope.$index, scope.row)" style="margin-right: 10px">编辑</el-button>
                      <el-popconfirm :title="delTitle" @confirm="delConfirm('eventSuspendUpdateTime', scope.$index, scope.row)">
                        <template #reference>
                          <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" textColor="danger" @click="delLevel('eventSuspendUpdateTime', scope.$index, scope.row)">删除</el-button>
                        </template>
                      </el-popconfirm>
                    </template>
                    <template v-else>
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="!scope.row.state" @click="confirmLevel('eventSuspendUpdateTime', scope.$index, scope.row)">保存</el-button>
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="!scope.row.state" @click="getSlaDetail(props.detail.ruleId)">取消</el-button>
                    </template>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>

            <!-- 服务请求 -->

            <el-col class="bold" :span="3" style="text-align: left"> 服务请求响应时间等级：</el-col>
            <el-col :span="21" style="text-align: right">
              <el-button :disabled="!userInfo.hasPermission(PERMISSION.group515413286225707008.editor)" type="primary" :icon="ElIconPlus" @click="addServiceLevel('response')">新增等级</el-button>
              <!-- <el-button type="primary" :icon="ElIconPlus" @click="addServiceLevel('response')">Help</el-button> -->
            </el-col>
            <el-col>
              <el-table stripe :data="serviceResponseTimeList" style="width: 100%; margin-top: 30px">
                <el-table-column align="left" label="优先级">
                  <template #default="{ row }">
                    <span v-if="row.state" class="state" :style="{ color: '#fff', backgroundColor: eventPriorityColor[row.priority as keyof typeof eventPriorityColor].color }">{{ row.priority }}</span>
                    <el-select v-else v-model="row.priority">
                      <el-option v-for="item in responseData" :key="item.value" :label="item.value" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="持续时间" width="300">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].day ? scope.row.resolves[0].day + "day" : "" }}
                      {{ scope.row.resolves[0].hour ? scope.row.resolves[0].hour + "h" : "" }}
                      {{ scope.row.resolves[0].minute ? scope.row.resolves[0].minute + "min" : "" }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input-number :min="0" v-model="scope.row.resolves[0].day" :max="10000000000"> </el-input-number>
                      day
                      <el-input-number :min="0" v-model="scope.row.resolves[0].hour" :max="23"> </el-input-number>
                      h
                      <el-input-number :min="0" v-model="scope.row.resolves[0].minute" :max="59"> </el-input-number>
                      min
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="提醒" width="300">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].warnDay ? scope.row.resolves[0].warnDay + "day" : "" }}
                      {{ scope.row.resolves[0].warnHour ? scope.row.resolves[0].warnHour + "h" : "" }}
                      {{ scope.row.resolves[0].warnMinute ? scope.row.resolves[0].warnMinute + "min" : "" }}
                    </div>
                    <div v-show="!scope.row.state">
                      <div>
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnDay" :max="10000000000"> </el-input-number>
                        day
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnHour" :max="23"> </el-input-number>
                        h
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnMinute" :max="59"> </el-input-number>
                        min
                      </div>
                      <div>
                        <el-checkbox v-model="scope.row.resolves[0].repeatWarn" label="重复提醒" />
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="SLA状态" prop="urgencyType">
                  <template #default="scope">
                    <span
                      class="state"
                      v-show="scope.row.state"
                      :style="{
                        color: '#fff',
                        backgroundColor: slaStateColor[scope.row.resolves[0].urgencyType?.toLowerCase()],
                      }"
                    >
                      <span v-show="scope.row.resolves[0].urgencyType">
                        {{ scope.row.resolves[0].urgencyType?.slice(0, 1).toUpperCase() + scope.row.resolves[0].urgencyType?.slice(1).toLowerCase() }}
                      </span>
                      <span v-show="!scope.row.resolves[0].urgencyType"> </span>
                    </span>
                    <el-select v-show="!scope.row.state" v-model="scope.row.resolves[0].urgencyType">
                      <el-option v-for="item in statusData" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="名称">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].name }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].name"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="描述">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].description }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].description"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="定义">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].definition }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].definition"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="操作">
                  <template #default="scope">
                    <template v-if="scope.row.state">
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="scope.row.state" @click="editLevel('response', scope.$index, scope.row)" style="margin-right: 10px">编辑</el-button>
                      <el-popconfirm :title="delTitle" @confirm="serviceDelConfirm('response', scope.$index, scope.row)">
                        <template #reference>
                          <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" textColor="danger" @click="delserviceLevel('response', scope.$index, scope.row)">删除</el-button>
                        </template>
                      </el-popconfirm>
                    </template>
                    <template v-else>
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="!scope.row.state" @click="serviceConfirmLevel('response', scope.$index, scope.row)">保存</el-button>
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="!scope.row.state" @click="getSlaDetail(props.detail.ruleId)">取消</el-button>
                    </template>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>

            <el-col class="bold" :span="3" style="text-align: left">服务请求处理时间等级：</el-col>
            <el-col :span="21" style="text-align: right">
              <el-button :disabled="!userInfo.hasPermission(PERMISSION.group515413286225707008.editor)" type="primary" :icon="ElIconPlus" @click="addServiceLevel('resolve')">新增等级</el-button>
            </el-col>
            <el-col>
              <el-table stripe :data="serviceHandelTimeList" style="width: 100%; margin-top: 30px">
                <el-table-column align="left" label="优先级">
                  <template #default="{ row }">
                    <span
                      v-if="row.state"
                      class="state"
                      :style="{
                        color: '#fff',
                        backgroundColor: (eventPriorityColor[row.priority as keyof typeof eventPriorityColor] || {}).color,
                      }"
                      >{{ row.priority }}</span
                    >
                    <el-select v-else v-model="row.priority">
                      <el-option v-for="item in responseData" :key="item.value" :label="item.value" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="持续时间" width="300">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].day ? scope.row.resolves[0].day + "day" : "" }}
                      {{ scope.row.resolves[0].hour ? scope.row.resolves[0].hour + "h" : "" }}
                      {{ scope.row.resolves[0].minute ? scope.row.resolves[0].minute + "min" : "" }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input-number :min="0" v-model="scope.row.resolves[0].day" :max="10000000000"> </el-input-number>
                      day
                      <el-input-number :min="0" v-model="scope.row.resolves[0].hour" :max="23"> </el-input-number>
                      h
                      <el-input-number :min="0" v-model="scope.row.resolves[0].minute" :max="59"> </el-input-number>
                      min
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="提醒" width="300">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].warnDay ? scope.row.resolves[0].warnDay + "day" : "" }}
                      {{ scope.row.resolves[0].warnHour ? scope.row.resolves[0].warnHour + "h" : "" }}
                      {{ scope.row.resolves[0].warnMinute ? scope.row.resolves[0].warnMinute + "min" : "" }}
                    </div>
                    <div v-show="!scope.row.state">
                      <div>
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnDay" :max="10000000000"> </el-input-number>
                        day
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnHour" :max="23"> </el-input-number>
                        h
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnMinute" :max="59"> </el-input-number>
                        min
                      </div>
                      <div>
                        <el-checkbox v-model="scope.row.resolves[0].repeatWarn" label="重复提醒" />
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="SLA状态" prop="urgencyType">
                  <template #default="scope">
                    <span
                      class="state"
                      v-show="scope.row.state"
                      :style="{
                        color: '#fff',
                        backgroundColor: slaStateColor[scope.row.resolves[0].urgencyType?.toLowerCase()],
                      }"
                    >
                      <span v-show="scope.row.resolves[0].urgencyType">{{ scope.row.resolves[0].urgencyType?.slice(0, 1).toUpperCase() + scope.row.resolves[0].urgencyType?.slice(1).toLowerCase() }} </span>
                      <span v-show="!scope.row.resolves[0].urgencyType"></span>
                    </span>
                    <el-select v-show="!scope.row.state" v-model="scope.row.resolves[0].urgencyType">
                      <el-option v-for="item in statusData" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="名称">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].name }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].name"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="描述">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].description }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].description"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="定义">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].definition }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].definition"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="操作">
                  <template #default="scope">
                    <template v-if="scope.row.state">
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="scope.row.state" @click="editLevel('resolve', scope.$index, scope.row)" style="margin-right: 10px">编辑</el-button>
                      <el-popconfirm :title="delTitle" @confirm="serviceDelConfirm('resolve', scope.$index, scope.row)">
                        <template #reference>
                          <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" textColor="danger" @click="delserviceLevel('resolve', scope.$index, scope.row)">删除</el-button>
                        </template>
                      </el-popconfirm>
                    </template>
                    <template v-else>
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="!scope.row.state" @click="serviceConfirmLevel('resolve', scope.$index, scope.row)">保存</el-button>
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="!scope.row.state" @click="getSlaDetail(props.detail.ruleId)">取消</el-button>
                    </template>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>

            <!-- 服务请求处理更新时间 -->
            <el-col class="bold" :span="3" style="text-align: left">服务请求处理更新时间：</el-col>
            <el-col :span="21" style="text-align: right">
              <el-button :disabled="!userInfo.hasPermission(PERMISSION.group515413286225707008.editor)" type="primary" :icon="ElIconPlus" @click="addServiceLevel('serviceResolveUpdateTime')">新增等级</el-button>
            </el-col>
            <el-col>
              <el-table stripe :data="serviceResolveUpdateTimeList" style="width: 100%; margin-top: 30px">
                <el-table-column align="left" label="优先级">
                  <template #default="{ row }">
                    <span
                      v-if="row.state"
                      class="state"
                      :style="{
                        color: '#fff',
                        backgroundColor: (eventPriorityColor[row.priority as keyof typeof eventPriorityColor] || {}).color,
                      }"
                      >{{ row.priority }}</span
                    >
                    <el-select v-else v-model="row.priority">
                      <el-option v-for="item in responseData" :key="item.value" :label="item.value" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="持续时间" width="300">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].day ? scope.row.resolves[0].day + "day" : "" }}
                      {{ scope.row.resolves[0].hour ? scope.row.resolves[0].hour + "h" : "" }}
                      {{ scope.row.resolves[0].minute ? scope.row.resolves[0].minute + "min" : "" }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input-number :min="0" v-model="scope.row.resolves[0].day" :max="10000000000"> </el-input-number>
                      day
                      <el-input-number :min="0" v-model="scope.row.resolves[0].hour" :max="23"> </el-input-number>
                      h
                      <el-input-number :min="0" v-model="scope.row.resolves[0].minute" :max="59"> </el-input-number>
                      min
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="提醒" width="300">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].warnDay ? scope.row.resolves[0].warnDay + "day" : "" }}
                      {{ scope.row.resolves[0].warnHour ? scope.row.resolves[0].warnHour + "h" : "" }}
                      {{ scope.row.resolves[0].warnMinute ? scope.row.resolves[0].warnMinute + "min" : "" }}
                    </div>
                    <div v-show="!scope.row.state">
                      <div>
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnDay" :max="10000000000"> </el-input-number>
                        day
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnHour" :max="23"> </el-input-number>
                        h
                        <el-input-number :min="0" v-model="scope.row.resolves[0].warnMinute" :max="59"> </el-input-number>
                        min
                      </div>
                      <div>
                        <el-checkbox v-model="scope.row.resolves[0].repeatWarn" label="重复提醒" />
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="SLA状态" prop="urgencyType">
                  <template #default="scope">
                    <span
                      class="state"
                      v-show="scope.row.state"
                      :style="{
                        color: '#fff',
                        backgroundColor: slaStateColor[scope.row.resolves[0].urgencyType?.toLowerCase()],
                      }"
                    >
                      <span v-show="scope.row.resolves[0].urgencyType">{{ scope.row.resolves[0].urgencyType?.slice(0, 1).toUpperCase() + scope.row.resolves[0].urgencyType?.slice(1).toLowerCase() }} </span>
                      <span v-show="!scope.row.resolves[0].urgencyType"></span>
                    </span>
                    <el-select v-show="!scope.row.state" v-model="scope.row.resolves[0].urgencyType">
                      <el-option v-for="item in statusData" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="名称">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].name }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].name"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="描述">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].description }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].description"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="定义">
                  <template #default="scope">
                    <div v-show="scope.row.state">
                      {{ scope.row.resolves[0].definition }}
                    </div>
                    <div v-show="!scope.row.state">
                      <el-input v-model="scope.row.resolves[0].definition"> </el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="操作">
                  <template #default="scope">
                    <template v-if="scope.row.state">
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="scope.row.state" @click="editLevel('serviceResolveUpdateTime', scope.$index, scope.row)" style="margin-right: 10px">编辑</el-button>
                      <el-popconfirm :title="delTitle" @confirm="serviceDelConfirm('serviceResolveUpdateTime', scope.$index, scope.row)">
                        <template #reference>
                          <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" textColor="danger" @click="delserviceLevel('serviceResolveUpdateTime', scope.$index, scope.row)">删除</el-button>
                        </template>
                      </el-popconfirm>
                    </template>
                    <template v-else>
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="!scope.row.state" @click="serviceConfirmLevel('serviceResolveUpdateTime', scope.$index, scope.row)">保存</el-button>
                      <el-button :disabled="!userInfo.hasPermission(服务管理中心_SLA配置_编辑)" type="text" v-show="!scope.row.state" @click="getSlaDetail(props.detail.ruleId)">取消</el-button>
                    </template>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form-item>
      </el-row>
    </el-card>
  </el-form>
</template>

<!--  generic="Item extends Record<'id', string> & Record<string, unknown>" -->
<script lang="ts" setup name="EditorForm" generic="T extends { [key: string]: unknown; id: string }">
/* eslint-disable no-unused-vars, @typescript-eslint/no-unused-vars */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { getCurrentInstance, onMounted } from "vue";
import { 服务管理中心_SLA配置_编辑 } from "@/views/pages/permission";

import { readonly, reactive, ref, nextTick, inject, h } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { TypeHelper } from "@/utils/type";
import { InfoFilled as ElIconInfo, Plus as ElIconPlus } from "@element-plus/icons-vue";

import getUserInfo from "@/utils/getUserInfo";
import { Check } from "@element-plus/icons-vue";
import { type SlaConfigList as DataItem } from "@/views/pages/apis/SlaConfig";
import * as Api from "@/views/pages/apis/SlaConfig";

import { responseData as _responseData, resolveData as _resolveData, priority, statusData as _statusData } from "./common";

import { slaState } from "@/views/pages/common/slaState";
import _timeZone from "@/views/pages/common/zone.json";

import { useResizeObserver } from "@vueuse/core";

const emit = defineEmits(["confrim"]);
const userInfo = getUserInfo();

const customerItemId = ref("");
const packagePreview = ref(false);
const isShow = ref(false);
const content = ref("新增SLA");
const eventPriorityColor = ref(priority);
const customerName = ref("");
const subruleName = ref("");
const subruleDesc = ref("");
// sla名称
const slaName = ref("");
// sla类型
const slaType = ref("");
// sla描述
const slaDesc = ref("");
// 选择时区
const DefaultTime = ref("");
// 优先级等级
const PriorityLevel = ref("");
const SlaDialogVisible = ref(false);
// const form = ref({});
const customerNameList = ref([]);
const tableData = ref([]);
const responseData = ref(_responseData);
const resolveData = ref(_resolveData);
const statusData = ref(_statusData);
const timeZone = ref(_timeZone as { zoneId: string; displayName: string }[]);
const slaStateColor = ref(slaState);
const TimeAddress = ref([]);
const expands = ref([]);
const index = ref(1);
interface Emits {
  ($event: "confirm", data: Record<string, any>): any;
}
interface Props {
  detail: T;
  width: number;
}
const props = withDefaults(defineProps<Props>(), { width: 0, detail: () => ({}) as T });
const coverTimeCfg = ref({
  timeZone: "",
  useCustomerTimeZone: false,
  useDeviceTimeZone: false,
  useAutoTimeZone: true,
  coverWorkTime: [
    {
      week: "周一",
      workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      weekDay: 1,
    },
    {
      week: "周二",
      workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      weekDay: 2,
    },
    {
      week: "周三",
      workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      weekDay: 3,
    },
    {
      week: "周四",
      workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      weekDay: 4,
    },
    {
      week: "周五",
      workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      weekDay: 5,
    },
    {
      week: "周六",
      workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      weekDay: 6,
    },
    {
      week: "周日",
      workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      weekDay: 7,
    },
  ],
});
onMounted(() => {
  nextTick(() => {
    getSlaDetail(props.detail.ruleId);
  });
});
const allBool = ref([false, false, false, false, false, false, false]);
const basicClassInput = ref({ width: "35.8vw" }); /* 输入框选择器基本样式 */
const basicClassInputDown = ref({ width: "15.8vw" }); /* 输入框选择器基本样式 */
//事件时间等级弹框
const TimeDialogVisible = ref(false);
//事件响应时间等级
const responseTimeList = ref<Record<string, any>[]>([]);
// 服务请求响应时间等级
const serviceResponseTimeList = ref<Record<string, any>[]>([]);
const resList = ref<Record<string, any>[]>([]);
//事件处理时间等级
const handelTimeList = ref<Record<string, any>[]>([]);
const serviceHandelTimeList = ref<Record<string, any>[]>([]);
const timePriority = ref("");
const eventType = ref("");
const eventTypeList = ref<Record<string, any>[]>([]);
const ruleId = ref("");
const chooseLevelType = ref("");
const delTitle = ref("");

// 事件处理更新时间
const eventRespUpdateTimeList = ref<Record<string, any>[]>([]);
// 事件挂起更新时间
const eventSuspendUpdateTimeList = ref<Record<string, any>[]>([]);

// 服务请求处理更新时间
const serviceResolveUpdateTimeList = ref<Record<string, any>[]>([]);

const tableContentRef = ref();
const tableWidth = ref(0);
useResizeObserver(tableContentRef, (entries) => {
  const entry = entries[0];
  const { width, height } = entry.contentRect;
  tableWidth.value = ((width - 80) / 24).toFixed(0);
});

/**
 * TODO: 本地方法
 */
//编辑事件操作
function editLevel(type: string, index: number, data: any) {
  data.state = false;
}
//保存事件操作
function confirmLevel(type: string, index: number, data: any) {
  //type 类型
  //data 输入框中的数据
  //index当前操作的下标

  if (data.resolves[0].minute == 0 && data.resolves[0].hour == 0 && data.resolves[0].day == 0) {
    data.state = false;
    return ElMessage.warning("持续时间不能为0");
  } else if (data.resolves[0].warnMinute == 0 && data.resolves[0].warnHour == 0 && data.resolves[0].warnDay == 0) {
    data.state = false;
    return ElMessage.warning("提醒不能为0");
  }
  if (data.priority) {
    if (type === "response") {
      const hasObj: Record<string, unknown> = {};
      let allConditionsMet = true; // 新增标志变量
      type DataType = (typeof responseTimeList.value)[number];
      responseTimeList.value = responseTimeList.value.reduce((total: DataType[], next: DataType) => {
        const filterKey = next.priority + next.resolves[0].urgencyType;
        if (hasObj[filterKey] != undefined) {
          data.state = false;
          next.state = false;
          total.push(next);
          ElMessage.warning("SLA状态不可重复");
          allConditionsMet = false; // 标记条件未满足
        } else {
          // console.log("8888888888888888");
          // // baocunLevel();
          // data.state = true;

          if (filterKey && filterKey !== "null") {
            hasObj[filterKey] ? "" : (hasObj[filterKey] = true && total.push(next));
          } else {
            total.push(next);
          }
        }
        return total;
      }, []);
      // 所有元素处理完毕后判断
      if (allConditionsMet) {
        data.state = true;
        baocunLevel(); // 所有条件满足时调用
      } else {
        data.state = false;
      }
    } else if (type === "resolve") {
      const hasObj: Record<string, unknown> = {};
      let allConditionsMet = true; // 新增标志变量
      type DataType = (typeof responseTimeList.value)[number];

      handelTimeList.value = handelTimeList.value.reduce((total: DataType[], next: DataType) => {
        const filterKey = next.priority + next.resolves[0].urgencyType;

        if (hasObj[filterKey] != undefined) {
          next.state = false;
          total.push(next);
          ElMessage.warning("SLA状态不可重复");
          allConditionsMet = false; // 标记条件未满足
        } else {
          if (filterKey) {
            hasObj[filterKey] ? "" : (hasObj[filterKey] = true && total.push(next));
            next.state = true;
          } else {
            total.push(next);
          }
        }

        return total;
      }, []);
      // 所有元素处理完毕后判断
      if (allConditionsMet) {
        data.state = true;
        baocunLevel(); // 所有条件满足时调用
      } else {
        data.state = false;
      }
    } else if (type === "eventRespUpdateTime") {
      /*  */
      const hasObj: Record<string, unknown> = {};
      let allConditionsMet = true; // 新增标志变量
      type DataType = (typeof responseTimeList.value)[number];

      eventRespUpdateTimeList.value = eventRespUpdateTimeList.value.reduce((total: DataType[], next: DataType) => {
        const filterKey = next.priority + next.resolves[0].urgencyType;

        if (hasObj[filterKey] != undefined) {
          next.state = false;
          total.push(next);
          ElMessage.warning("SLA状态不可重复");
          allConditionsMet = false; // 标记条件未满足
        } else {
          if (filterKey) {
            hasObj[filterKey] ? "" : (hasObj[filterKey] = true && total.push(next));
            next.state = true;
          } else {
            total.push(next);
          }
        }

        return total;
      }, []);
      // 所有元素处理完毕后判断
      if (allConditionsMet) {
        data.state = true;
        baocunLevel(); // 所有条件满足时调用
      } else {
        data.state = false;
      }
    } else if (type === "eventSuspendUpdateTime") {
      /*  */
      const hasObj: Record<string, unknown> = {};
      let allConditionsMet = true; // 新增标志变量
      type DataType = (typeof responseTimeList.value)[number];

      eventSuspendUpdateTimeList.value = eventSuspendUpdateTimeList.value.reduce((total: DataType[], next: DataType) => {
        const filterKey = next.priority + next.resolves[0].urgencyType;

        if (hasObj[filterKey] != undefined) {
          next.state = false;
          total.push(next);
          ElMessage.warning("SLA状态不可重复");
          allConditionsMet = false; // 标记条件未满足
        } else {
          if (filterKey) {
            hasObj[filterKey] ? "" : (hasObj[filterKey] = true && total.push(next));
            next.state = true;
          } else {
            total.push(next);
          }
        }

        return total;
      }, []);
      // 所有元素处理完毕后判断
      if (allConditionsMet) {
        data.state = true;
        baocunLevel(); // 所有条件满足时调用
      } else {
        data.state = false;
      }
    }
    // data.state = true;
  } else {
    return ElMessage.warning("请先选择事件优先级");
  }
  //
  //
}

function serviceConfirmLevel(type: string, index: number, data: any) {
  //type 类型
  //data 输入框中的数据
  //index当前操作的下标

  if (data.resolves[0].minute == 0 && data.resolves[0].hour == 0 && data.resolves[0].day == 0) {
    data.state = false;
    return ElMessage.warning("持续时间不能为0");
  } else if (data.resolves[0].warnMinute == 0 && data.resolves[0].warnHour == 0 && data.resolves[0].warnDay == 0) {
    data.state = false;
    return ElMessage.warning("提醒不能为0");
  }
  if (data.priority) {
    if (type === "response") {
      const hasObj: Record<string, unknown> = {};
      let allConditionsMet = true; // 新增标志变量
      type DataType = (typeof serviceResponseTimeList.value)[number];

      serviceResponseTimeList.value = serviceResponseTimeList.value.reduce((total: DataType[], next: DataType) => {
        const filterKey = next.priority + next.resolves[0].urgencyType;
        if (hasObj[filterKey] != undefined) {
          data.state = false;
          next.state = false;
          total.push(next);
          ElMessage.warning("SLA状态不可重复");
          allConditionsMet = false; // 标记条件未满足
        } else {
          if (filterKey && filterKey !== "null") {
            hasObj[filterKey] ? "" : (hasObj[filterKey] = true && total.push(next));
          } else {
            total.push(next);
          }
        }

        return total;
      }, []);
      // 所有元素处理完毕后判断
      if (allConditionsMet) {
        data.state = true;
        baocunLevel(); // 所有条件满足时调用
      } else {
        data.state = false;
      }
    } else if (type === "resolve") {
      const hasObj: Record<string, unknown> = {};
      let allConditionsMet = true; // 新增标志变量
      type DataType = (typeof serviceResponseTimeList.value)[number];

      serviceHandelTimeList.value = serviceHandelTimeList.value.reduce((total: DataType[], next: DataType) => {
        const filterKey = next.priority + next.resolves[0].urgencyType;

        if (hasObj[filterKey] != undefined) {
          next.state = false;
          total.push(next);
          ElMessage.warning("SLA状态不可重复");
          allConditionsMet = false; // 标记条件未满足
        } else {
          if (filterKey) {
            hasObj[filterKey] ? "" : (hasObj[filterKey] = true && total.push(next));
            next.state = true;
          } else {
            total.push(next);
          }
        }

        return total;
      }, []);
      // 所有元素处理完毕后判断
      if (allConditionsMet) {
        data.state = true;
        baocunLevel(); // 所有条件满足时调用
      } else {
        data.state = false;
      }
    } else if (type === "serviceResolveUpdateTime") {
      const hasObj: Record<string, unknown> = {};
      let allConditionsMet = true; // 新增标志变量
      type DataType = (typeof serviceResponseTimeList.value)[number];

      serviceResolveUpdateTimeList.value = serviceResolveUpdateTimeList.value.reduce((total: DataType[], next: DataType) => {
        const filterKey = next.priority + next.resolves[0].urgencyType;

        if (hasObj[filterKey] != undefined) {
          next.state = false;
          total.push(next);
          ElMessage.warning("SLA状态不可重复");
          allConditionsMet = false; // 标记条件未满足
        } else {
          if (filterKey) {
            hasObj[filterKey] ? "" : (hasObj[filterKey] = true && total.push(next));
            next.state = true;
          } else {
            total.push(next);
          }
        }

        return total;
      }, []);
      // 所有元素处理完毕后判断
      if (allConditionsMet) {
        data.state = true;
        baocunLevel(); // 所有条件满足时调用
      } else {
        data.state = false;
      }
    }
    // data.state = true;
  } else {
    return ElMessage.warning("请先选择事件优先级");
  }
  //
  //
}
//删除事件操作
function delLevel(type: string, index: number, data: any) {
  delTitle.value = "确认删除当前等级为" + data.priority + "的所有内容吗？";
}
function delConfirm(type: string, index: number, data: any) {
  // console.log(data);
  if (type == "response") {
    if (data.id) {
      responseTimeList.value.splice(index, 1);
      // ElMessage.success("删除成功");
      baocunLevel();
    } else {
      responseTimeList.value.splice(index, 1);
      // ElMessage.success("删除成功");
      baocunLevel();
    }
  } else if (type === "resolve") {
    if (data.id) {
      handelTimeList.value.splice(index, 1);
      // ElMessage.success("删除成功");
      baocunLevel();
    } else {
      handelTimeList.value.splice(index, 1);
      baocunLevel();
    }
  } else if (type === "eventRespUpdateTime") {
    if (data.id) {
      eventRespUpdateTimeList.value.splice(index, 1);
      // ElMessage.success("删除成功");
      baocunLevel();
    } else {
      eventRespUpdateTimeList.value.splice(index, 1);
      baocunLevel();
    }
  } else if (type === "eventSuspendUpdateTime") {
    if (data.id) {
      eventRespUpdateTimeList.value.splice(index, 1);
      // ElMessage.success("删除成功");
      baocunLevel();
    } else {
      eventRespUpdateTimeList.value.splice(index, 1);
      baocunLevel();
    }
  }
}

function delserviceLevel(type: string, index: number, data: any) {
  delTitle.value = "确认删除当前等级为" + data.priority + "的所有内容吗？";
}
//删除服务请求操作
function serviceDelConfirm(type: string, index: number, data: any) {
  // console.log(data);
  if (type == "response") {
    if (data.id) {
      serviceResponseTimeList.value.splice(index, 1);
      // ElMessage.success("删除成功");
      baocunLevel();
    } else {
      serviceResponseTimeList.value.splice(index, 1);
      // ElMessage.success("删除成功");
      baocunLevel();
    }
  } else if (type === "resolve") {
    if (data.id) {
      serviceHandelTimeList.value.splice(index, 1);
      // ElMessage.success("删除成功");
      baocunLevel();
    } else {
      serviceHandelTimeList.value.splice(index, 1);
      baocunLevel();
    }
  } else if (type === "serviceResolveUpdateTime") {
    if (data.id) {
      serviceResolveUpdateTimeList.value.splice(index, 1);
      // ElMessage.success("删除成功");
      baocunLevel();
    } else {
      serviceResolveUpdateTimeList.value.splice(index, 1);
      baocunLevel();
    }
  }
}

//新增等级
function addLevel(type: string) {
  eventType.value = "";
  chooseLevelType.value = type;
  TimeDialogVisible.value = true;

  if (type === "response") {
    if (responseTimeList.value.filter((v) => !v.state).length) return ElMessage.warning("请先保存正在编辑的信息");

    responseTimeList.value.push({
      priority: "",
      resolves: [
        {
          day: "",
          hour: "",
          minute: "",
          warnDay: "",
          warnHour: "",
          warnMinute: "",
          repeatWarn: true,
          urgencyType: null,
          name: "",
          description: "",
          definition: "",
        },
      ],
      state: false,
    });
  } else if (type === "resolve") {
    if (handelTimeList.value.filter((v) => !v.state).length) return ElMessage.warning("请先保存正在编辑的信息");

    handelTimeList.value.push({
      priority: "",
      resolves: [
        {
          day: "",
          hour: "",
          minute: "",
          warnDay: "",
          warnHour: "",
          warnMinute: "",
          repeatWarn: true,
          urgencyType: null,
          name: "",
          description: "",
          definition: "",
        },
      ],
      state: false,
    });
  } else if (type === "eventRespUpdateTime") {
    if (eventRespUpdateTimeList.value.filter((v) => !v.state).length) return ElMessage.warning("请先保存正在编辑的信息");

    eventRespUpdateTimeList.value.push({
      priority: "",
      resolves: [
        {
          day: "",
          hour: "",
          minute: "",
          warnDay: "",
          warnHour: "",
          warnMinute: "",
          repeatWarn: true,
          urgencyType: null,
          name: "",
          description: "",
          definition: "",
        },
      ],
      state: false,
    });
  } else if (type === "eventSuspendUpdateTime") {
    if (eventSuspendUpdateTimeList.value.filter((v) => !v.state).length) return ElMessage.warning("请先保存正在编辑的信息");

    eventSuspendUpdateTimeList.value.push({
      priority: "",
      resolves: [
        {
          day: "",
          hour: "",
          minute: "",
          warnDay: "",
          warnHour: "",
          warnMinute: "",
          repeatWarn: true,
          urgencyType: null,
          name: "",
          description: "",
          definition: "",
        },
      ],
      state: false,
    });
  }
}

function addServiceLevel(type: string) {
  // eventType.value = "";
  // chooseLevelType.value = type;
  // TimeDialogVisible.value = true;

  // resolve
  // serviceResolveUpdateTime

  if (type === "response") {
    if (serviceResponseTimeList.value.filter((v) => !v.state).length) return ElMessage.warning("请先保存正在编辑的信息");

    serviceResponseTimeList.value.push({
      priority: "",
      resolves: [
        {
          day: "",
          hour: "",
          minute: "",
          warnDay: "",
          warnHour: "",
          warnMinute: "",
          repeatWarn: true,
          urgencyType: null,
          name: "",
          description: "",
          definition: "",
        },
      ],
      state: false,
    });
  } else if (type === "resolve") {
    if (serviceHandelTimeList.value.filter((v) => !v.state).length) return ElMessage.warning("请先保存正在编辑的信息");

    serviceHandelTimeList.value.push({
      priority: "",
      resolves: [
        {
          day: "",
          hour: "",
          minute: "",
          warnDay: "",
          warnHour: "",
          warnMinute: "",
          repeatWarn: true,
          urgencyType: null,
          name: "",
          description: "",
          definition: "",
        },
      ],
      state: false,
    });
  } else if (type === "serviceResolveUpdateTime") {
    serviceResolveUpdateTimeList.value.push({
      priority: "",
      resolves: [
        {
          day: "",
          hour: "",
          minute: "",
          warnDay: "",
          warnHour: "",
          warnMinute: "",
          repeatWarn: true,
          urgencyType: null,
          name: "",
          description: "",
          definition: "",
        },
      ],
      state: false,
    });
  }
}

//事件时间等级保存
function baocunLevel() {
  if (!coverTimeCfg.value.timeZone || coverTimeCfg.value.timeZone === "自动时区") {
    coverTimeCfg.value.useAutoTimeZone = true;
    coverTimeCfg.value.useCustomerTimeZone = false;
    coverTimeCfg.value.useDeviceTimeZone = false;
  } else if (coverTimeCfg.value.timeZone === "客户默认") {
    coverTimeCfg.value.useCustomerTimeZone = true;
    coverTimeCfg.value.useDeviceTimeZone = false;
    coverTimeCfg.value.useAutoTimeZone = false;
  } else if (coverTimeCfg.value.timeZone === "设备默认") {
    coverTimeCfg.value.useDeviceTimeZone = true;
    coverTimeCfg.value.useCustomerTimeZone = false;
    coverTimeCfg.value.useAutoTimeZone = false;
  } else {
    coverTimeCfg.value.useCustomerTimeZone = false;
    coverTimeCfg.value.useDeviceTimeZone = false;
    coverTimeCfg.value.useAutoTimeZone = false;
  }
  let eventRespTimeLevels: any[] = [];
  let eventRespDefaultResolves: any[] = [];
  let eventResolveTimeLevels: any[] = [];
  let eventResolveDefaultResolves: any[] = [];

  let eventResponseUpdateDefaultResolves: any[] = [];
  let eventResponseUpdateTimeLevels: any[] = [];
  let eventSuspendUpdateDefaultResolves: any[] = [];
  let eventSuspendUpdateTimeLevels: any[] = [];

  responseTimeList.value.forEach((v, i) => {
    if (v.priority !== "Default" && v.state) {
      eventRespTimeLevels.push(v);
    } else if (v.priority === "Default" && v.state) {
      eventRespDefaultResolves.push(v.resolves[0]);
    }
  });
  handelTimeList.value.forEach((v, i) => {
    if (v.priority !== "Default" && v.state) {
      eventResolveTimeLevels.push(v);
    } else if (v.priority === "Default" && v.state) {
      eventResolveDefaultResolves.push(v.resolves[0]);
    }
  });

  eventRespUpdateTimeList.value.forEach((v, i) => {
    if (v.priority !== "Default" && v.state) {
      eventResponseUpdateTimeLevels.push(v);
    } else if (v.priority === "Default" && v.state) {
      eventResponseUpdateDefaultResolves.push(v.resolves[0]);
    }
  });

  eventSuspendUpdateTimeList.value.forEach((v, i) => {
    if (v.priority !== "Default" && v.state) {
      eventSuspendUpdateTimeLevels.push(v);
    } else if (v.priority === "Default" && v.state) {
      eventSuspendUpdateDefaultResolves.push(v.resolves[0]);
    }
  });

  let serviceRespTimeLevels: any[] = [];
  let serviceRespDefaultResolves: any[] = [];
  let serviceResolveTimeLevels: any[] = [];
  let serviceResolveDefaultResolves: any[] = [];

  serviceResponseTimeList.value.forEach((v, i) => {
    if (v.priority !== "Default" && v.state) {
      serviceRespTimeLevels.push(v);
    } else if (v.priority === "Default" && v.state) {
      serviceRespDefaultResolves.push(v.resolves[0]);
    }
  });

  serviceHandelTimeList.value.forEach((v, i) => {
    if (v.priority !== "Default" && v.state) {
      serviceResolveTimeLevels.push(v);
    } else if (v.priority === "Default" && v.state) {
      serviceResolveDefaultResolves.push(v.resolves[0]);
    }
  });

  let serviceResponseUpdateDefaultResolves: any[] = [];
  let serviceResponseUpdateTimeLevels: any[] = [];

  serviceResolveUpdateTimeList.value.forEach((v, i) => {
    if (v.priority !== "Default" && v.state) {
      serviceResponseUpdateTimeLevels.push(v);
    } else if (v.priority === "Default" && v.state) {
      serviceResponseUpdateDefaultResolves.push(v.resolves[0]);
    }
  });

  let data = {
    tenantId: getUserInfo()?.currentTenantId,
    coverTimeCfg: coverTimeCfg.value,
    eventRespTimeLevels,
    eventRespDefaultResolves,
    eventResolveTimeLevels,
    eventResolveDefaultResolves,

    eventResponseUpdateTimeLevels,
    eventResponseUpdateDefaultResolves,
    eventSuspendUpdateTimeLevels,
    eventSuspendUpdateDefaultResolves,

    serviceRespTimeLevels,
    serviceRespDefaultResolves,
    serviceResolveTimeLevels,
    serviceResolveDefaultResolves,

    serviceResponseUpdateDefaultResolves,
    serviceResponseUpdateTimeLevels,
    defaultRule: false,
  };
  if (!props.detail.ruleId) {
    AddSla(data);
  } else {
    EditSlaConfig(data);
  }

  // addResponseLevel({
  //   itemId: this.customerItemId,
  //   list: responseTimeList.value,
  // }).then((res) => {
  //   // console.log(res);
  // });
}

const apiLoading = ref<boolean>(false);

//获取基本信息详情
function getSlaDetail(ruleId) {
  apiLoading.value = true;
  try {
    Api.DetailSlaConfig({ ruleId, tenantId: getUserInfo()?.currentTenantId })
      .then(({ success, data }) => {
        if (success) {
          if (data.coverTimeCfg.coverWorkTime.length > 0) {
            coverTimeCfg.value = (data as any).coverTimeCfg;
            coverTimeCfg.value.coverWorkTime.forEach((item: any) => {
              if (item.workTime.length > 23) {
                allBool.value[item.weekDay - 1] = true;
              } else {
                allBool.value[item.weekDay - 1] = false;
              }
            });
          }
          coverTimeCfg.value.timeZone = data.coverTimeCfg.timeZone ? data.coverTimeCfg.timeZone : "自动时区";

          let responseData: any[] = [];
          let resolveData: any[] = [];

          (data as any).eventRespDefaultResolves.forEach((v: any, i: number) => {
            responseData.push({
              priority: "Default",
              resolves: [v],
              state: true,
            });
          });
          (data as any).eventRespTimeLevels.forEach((v: any, i: number) => {
            if (v.resolves.length > 0) {
              v.resolves.forEach((item: Record<string, unknown>) => {
                responseData.push({
                  priority: v.priority,
                  resolves: [item],
                  state: true,
                });
              });
            }
          });
          (data as any).eventResolveDefaultResolves.forEach((v: any, i: number) => {
            resolveData.push({
              priority: "Default",
              resolves: [v],
              state: true,
            });
          });
          (data as any).eventResolveTimeLevels.forEach((v: any, i: number) => {
            if (v.resolves.length > 0) {
              v.resolves.forEach((item: Record<string, unknown>) => {
                resolveData.push({
                  priority: v.priority,
                  resolves: [item],
                  state: true,
                });
              });
            }
          });

          let eventRespUpdateTimeData: any[] = [];

          (data as any).eventResponseUpdateDefaultResolves.forEach((v: any, i: number) => {
            eventRespUpdateTimeData.push({
              priority: "Default",
              resolves: [v],
              state: true,
            });
          });
          (data as any).eventResponseUpdateTimeLevels.forEach((v: any, i: number) => {
            if (v.resolves.length > 0) {
              v.resolves.forEach((item: Record<string, unknown>) => {
                eventRespUpdateTimeData.push({
                  priority: v.priority,
                  resolves: [item],
                  state: true,
                });
              });
            }
          });

          let eventSuspendUpdateTimeData: any[] = [];

          (data as any).eventSuspendUpdateDefaultResolves.forEach((v: any, i: number) => {
            eventSuspendUpdateTimeData.push({
              priority: "Default",
              resolves: [v],
              state: true,
            });
          });
          (data as any).eventSuspendUpdateTimeLevels.forEach((v: any, i: number) => {
            if (v.resolves.length > 0) {
              v.resolves.forEach((item: Record<string, unknown>) => {
                eventSuspendUpdateTimeData.push({
                  priority: v.priority,
                  resolves: [item],
                  state: true,
                });
              });
            }
          });

          let serviceResolveUpdateTimeData: any[] = [];

          (data as any).serviceResponseUpdateDefaultResolves.forEach((v: any, i: number) => {
            serviceResolveUpdateTimeData.push({
              priority: "Default",
              resolves: [v],
              state: true,
            });
          });
          (data as any).serviceResponseUpdateTimeLevels.forEach((v: any, i: number) => {
            if (v.resolves.length > 0) {
              v.resolves.forEach((item: Record<string, unknown>) => {
                serviceResolveUpdateTimeData.push({
                  priority: v.priority,
                  resolves: [item],
                  state: true,
                });
              });
            }
          });

          let serviceResponseData: any[] = [];
          let serviceResolveData: any[] = [];

          (data as any).serviceRespDefaultResolves.forEach((v: any, i: number) => {
            serviceResponseData.push({
              priority: "Default",
              resolves: [v],
              state: true,
            });
          });
          (data as any).serviceRespTimeLevels.forEach((v: any, i: number) => {
            if (v.resolves.length > 0) {
              v.resolves.forEach((item: Record<string, unknown>) => {
                serviceResponseData.push({
                  priority: v.priority,
                  resolves: [item],
                  state: true,
                });
              });
            }
          });
          (data as any).serviceResolveDefaultResolves.forEach((v: any, i: number) => {
            serviceResolveData.push({
              priority: "Default",
              resolves: [v],
              state: true,
            });
          });
          (data as any).serviceResolveTimeLevels.forEach((v: any, i: number) => {
            if (v.resolves.length > 0) {
              v.resolves.forEach((item: Record<string, unknown>) => {
                serviceResolveData.push({
                  priority: v.priority,
                  resolves: [item],
                  state: true,
                });
              });
            }
          });

          responseTimeList.value = [...responseData];
          handelTimeList.value = [...resolveData];

          eventRespUpdateTimeList.value = [...eventRespUpdateTimeData];
          eventSuspendUpdateTimeList.value = [...eventSuspendUpdateTimeData];

          serviceResponseTimeList.value = [...serviceResponseData];
          serviceHandelTimeList.value = [...serviceResolveData];

          serviceResolveUpdateTimeList.value = [...serviceResolveUpdateTimeData];
        }
      })
      .catch((e) => {
        ElMessage.error(e.message);
      });
  } catch (error) {
    /* error */
  } finally {
    apiLoading.value = false;
  }
}
//覆盖时间列
function handleClick({ column, evene }: Record<string, any>) {
  let index = Number(column.label);

  const { coverWorkTime } = coverTimeCfg.value;

  let workTime = [];
  coverWorkTime.forEach((v) => {
    workTime = workTime.concat(v.workTime);
  });

  const isActive = workTime.includes(index) && workTime.filter((v) => v === index).length === 7; // 是否激活
  // console.log(isActive);

  coverWorkTime.forEach((v: Record<string, any>, i: number) => {
    let delIndex = v.workTime.indexOf(index);
    if (isActive) {
      v.workTime.splice(delIndex, 1);
    } else {
      v.workTime.push(index);
    }

    v.workTime = [...new Set(v.workTime.sort((a, b) => a - b))];
  });
}
//覆盖时间
function handleSelectTime(key: string | number, weekIndex: number, row: any) {
  if (key === "all") {
    allBool.value[weekIndex] = !allBool.value[weekIndex];
    let data = [];
    for (let i = 0; i < 24; i++) {
      data.push(i);
    }
    row.workTime = [...new Set(data)];
    if (!allBool.value[weekIndex]) {
      row.workTime = [];
    }
  } else {
    const index = row.workTime.indexOf(key);

    if (index == -1) {
      row.workTime.push(key);
    } else row.workTime.splice(index, 1);
  }
}
//
function AddSla(obj: any) {
  if (slaName.value == "") {
    return ElMessage.error("请输入SLA名称");
  }
  build(obj);
}
//新增SLA
function build(obj: any) {
  Api.AddSlaConfig(obj)
    .then(({ success, data, message }) => {
      if (!success) throw new Error(message);
      ruleId.value = (data as unknown as Record<string, string>).ruleId as string;
      ElMessage.success("操作成功");
      emit("confrim");
      handleCancel();
    })
    .catch((e: any) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {});
}
//编辑服务
function EditSlaConfig(obj: any) {
  edit(obj);
}
function edit(obj: any) {
  Api.EditSlaConfig({
    ruleId: props.detail.ruleId,
    ...obj,
  })
    .then(({ success, data, message }) => {
      if (success) {
        ElMessage.success("操作成功");
        // handleCancel();
        getSlaDetail(props.detail.ruleId);
      } else {
        ElMessage.error(JSON.parse(data)?.message);
      }
    })
    .catch((e) => {
      ElMessage.error(e.message);
    });
}
// //查询客户名称列表
// function getCustomerList() {
//   let params = {
//     ...this.page,
//   };
//   this.tableLoading = true;
//   getCustomerList(params)
//     .then(({ success, data, page, size, total }) => {
//       // console.log(success, data);
//       if (success) {
//         this.customerNameList = data.tenants;
//         this.tableLoading = false;
//         this.page.total = Number(total);
//         this.page.pageNumber = page;
//         this.page.pageSize = size;
//       }
//     })
//     .catch(() => {
//       this.tableLoading = false;
//     });
// }
/**
 * TODO: 窗口方法
 */
type Item = Omit<DataItem, "createdTime" | "updatedTime" | "id" | "status"> & { id: string };

// interface Props {
//   title: string;
//   labelWidth?: number;
// }
// const props = withDefaults(defineProps<Props>(), {
//   title: "",
//   labelWidth: 116,
// });

const { t } = useI18n();

// const width = inject<import("vue").Ref<number>>("width", ref(100));
// const height = inject<import("vue").Ref<number>>("height", ref(100));

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<DataItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Partial<Item>) => void;
  reject?: (value: Partial<Item>) => void;
  callback?: (form: Item & { [key: string]: unknown }) => Promise<boolean>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item>>({});

type DefaultForm<T> = { [P in keyof T]: { value: T[P]; test: (v: any) => v is T[P]; transfer: (fv: any, ov: T[P]) => T[P] } };
const defaultForm = readonly<DefaultForm<Required<Item>>>({
  id: { value: "", ...TypeHelper.string },
  itemId: { value: "", ...TypeHelper.string },
  level: { value: "", ...TypeHelper.string },
  effectiveBegin: { value: [], ...TypeHelper.array },
  effectiveEnd: { value: [], ...TypeHelper.array },
  response: { value: [], ...TypeHelper.array },
  resolve: { value: [], ...TypeHelper.array },
  ruleId: { value: "", ...TypeHelper.string },
  ruleName: { value: "", ...TypeHelper.string },
  ruleDesc: { value: "", ...TypeHelper.string },
  ruleType: { value: "", ...TypeHelper.string },
  slaRuleId: { value: "", ...TypeHelper.string },
  degradeId: { value: "", ...TypeHelper.string },
  slaRuleName: { value: "", ...TypeHelper.string },
  tenantId: { value: "", ...TypeHelper.string },
});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  form = cloneDeep(form);

  try {
    // getCustomerList();
    timeZone.value.unshift({ zoneId: "设备默认", displayName: "设备默认" });
    let map = new Map();
    for (let item of timeZone.value) {
      map.set(item.zoneId, item);
    }
    timeZone.value = [...map.values()];
  } catch (error) {
    /*  */
  }

  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], util.value as never)) });
    },
    form as Required<Item>
  );
}
async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => resolve(true));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") {
      const valid = await data.callback($form);
      if (!valid) throw new Error("Error");
    }

    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});
  slaName.value = "";
  slaDesc.value = "";
  coverTimeCfg.value.timeZone = "";
  coverTimeCfg.value.coverWorkTime.forEach((item) => {
    item.workTime = [];
  });
  //  coverTimeCfg = ref({
  // timeZone: "客户默认",
  // useCustomerTimeZone: false,
  // useDeviceTimeZone: false,
  // coverWorkTime: [
  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    // formRef.value && formRef.value.clearValidate();
    // await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

defineExpose({
  close: handleCancel,
  async open(params: Partial<Item>, callback?: (form: Item) => Promise<boolean>) {
    if (JSON.stringify(params) === "{}") {
      responseTimeList.value = [];
      coverTimeCfg.value = {
        timeZone: "自动时区",
        useCustomerTimeZone: false,
        useDeviceTimeZone: false,
        coverWorkTime: [
          {
            week: "周一",
            workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
            weekDay: 1,
          },
          {
            week: "周二",
            workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
            weekDay: 2,
          },
          {
            week: "周三",
            workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
            weekDay: 3,
          },
          {
            week: "周四",
            workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
            weekDay: 4,
          },
          {
            week: "周五",
            workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
            weekDay: 5,
          },
          {
            week: "周六",
            workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
            weekDay: 6,
          },
          {
            week: "周日",
            workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
            weekDay: 7,
          },
        ],
      };
      //事件处理时间等级
      handelTimeList.value = [];
    }
    if (data.visible) {
      return await new Promise((resolve) => {
        ElMessage.warning("先关闭其他弹窗再重试！");
        resolve(params);
      });
    } else {
      $params.value = cloneDeep(params);
      data.visible = true;
      data.loading = true;
      data.submitLoading = true;
      data.callback = callback;
      try {
        return await new Promise((resolve, reject) => {
          data.resolve = resolve;
          data.reject = reject;
          if ($params.value.ruleId) {
            getSlaDetail();
          }

          nextTick(async () => {
            await nextTick();
            handleReset();
            data.loading = false;
            data.submitLoading = false;
          });
        });
      } catch (error) {
        return error;
      }
    }
  },
});
</script>

<style scoped lang="scss">
.edit_sla_config {
  :deep(.things) {
    .el-input-number {
      width: 50px !important;

      .elstyle-input {
        width: 50px;

        input {
          padding: 0;
        }
      }
    }
    .el-input-number {
      .el-input__wrapper {
        padding-left: 0px;
        padding-right: 0px;
      }
    }
    .el-input-number__increase {
      display: none;
    }

    .el-input-number__decrease {
      display: none;
    }

    .el-card {
      margin-top: 20px;
    }

    .el-form-item__content .el-form-item-content {
      display: flex;
      flex-direction: column;
    }

    .el-table .cell {
      padding: 0 !important;
    }

    .el-table .el-table__cell {
      padding: 0 !important;
      height: 50px;
    }
  }
}
.priority-icon {
  width: 10px;
  height: 10px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 10px;
}
.state {
  padding: 2px 10px;
  box-sizing: border-box;
  border-radius: 20px;
  width: 100px;
  display: block;
  text-align: center;
}
.modules-item {
  .modules-title {
    color: rgba(32, 128, 255, 1);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}
.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}
</style>
