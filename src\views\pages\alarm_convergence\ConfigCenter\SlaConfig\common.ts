/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { reactive, readonly, computed, nextTick } from "vue";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { filter, find } from "lodash-es";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
import { type EventItem as DataItem } from "@/views/pages/apis/event";

export enum rangeEnum {
  "false " = "未排班",
  "true" = "已排班",
}

export const slaStatus: { label: string; value: any; type?: "" | "success" | "warning" | "info" | "danger" }[] = [
  // { label: "未分配", value: slaSatte.UNASSIGNED, color: "#ED4013", type: "danger" },
  { label: "禁用", value: false, type: "danger" },
  // { label: "禁用", value: null, type: "danger" },
  // { label: "待处理", value: slaSatte.WAITING_FOR_RECEIVE, color: "#FF7D00", type: "danger" },
  { label: "启用", value: true, type: "success" },
];
export const responseData = [
  {
    label: "P1",
    value: "P1",
    disabled: false,
  },
  {
    label: "P2",
    value: "P2",
    disabled: false,
  },
  {
    label: "P3",
    value: "P3",
    disabled: false,
  },
  {
    label: "P4",
    value: "P4",
    disabled: false,
  },
  {
    label: "P5",
    value: "P5",
    disabled: false,
  },
  {
    label: "P6",
    value: "P6",
    disabled: false,
  },
  {
    label: "P7",
    value: "P7",
    disabled: false,
  },
  {
    label: "Default",
    value: "Default",
    disabled: false,
  },
];

export const resolveData = [
  {
    label: "P1",
    value: "P1",
    disabled: false,
  },
  {
    label: "P2",
    value: "P2",
    disabled: false,
  },
  {
    label: "P3",
    value: "P3",
    disabled: false,
  },
  {
    label: "P4",
    value: "P4",
    disabled: false,
  },

  {
    label: "Default",
    value: "Default",
    disabled: false,
  },
];
export const statusData = [
  {
    label: "Warning",
    value: "WARNING",
    disabled: false,
  },
  {
    label: "Imminent",
    value: "IMMINENT",
    disabled: false,
  },
  {
    label: "Breach",
    value: "BREACH",
    disabled: false,
  },
];

export const priority = {
  P1: { color: "#ED4013", value: "P1" },
  P2: { color: "#FF7D00", value: "P2" },
  P3: { color: "#2CB6F4", value: "P3" },
  P4: { color: "#3EBE6B", value: "P4" },
  P5: { color: "#3EBE6B", value: "P5" },
  P6: { color: "#3EBE6B", value: "P6" },
  P7: { color: "#3EBE6B", value: "P7" },
  P8: { color: "#3EBE6B", value: "P8" },
  Default: { color: "#3EBE6B", value: "Default" },
};

interface State<T> {
  search: Record<string, any>;
  sort?: { prop: string; order: "ascending" | "descending" };
  list: T[];
  page: number;
  size: number;
  total: number;
}

export const state = reactive<State<DataItem>>({
  search: {},
  sort: undefined,
  list: [],
  page: 1,
  size: 20,
  total: 0,
});
