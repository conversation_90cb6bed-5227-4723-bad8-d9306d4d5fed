<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="getSlaList()" @current-change="getSlaList()">
      <template #left>
        <!-- <el-input v-model="ServiceSearch" clearable placeholder="请输入SLA名称" @keyup.enter="searchSlaList()" style="width: 220px">
          <template #append>
            <el-button :icon="Search" @click="searchSlaList()" />
          </template>
        </el-input> -->
      </template>
      <template #right>
        <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group515413286225707008.create)">
          <span class="tw-h-fit">
            <el-button :disabled="!userInfo.hasPermission(PERMISSION.group515413286225707008.create)" type="primary" :icon="Plus" @click="handleCreate()">{{ $t("glob.add") }}SLA</el-button>
          </span>
        </el-tooltip>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table stripe :data="tableData" :height="tableHeight" style="width: 100%">
          <el-table-column type="index" align="left" prop="date" label="序号" :width="56"> </el-table-column>
          <!-- <el-table-column  align="left" prop="ruleName" label="SLA名称" :formatter="formatterTable" width="180"> </el-table-column> -->
          <TableColumn type="default" show-filter v-model:filtered-value="ServiceSearch" @filter-change="getSlaList()" prop="ruleName" label="SLA名称" :width="120"> </TableColumn>
          <el-table-column align="left" prop="ruleDesc" label="SLA描述" :formatter="formatterTable"> </el-table-column>
          <TableColumn type="enum" show-filter v-model:filtered-value="slaEnable" @filter-change="getSlaList()" prop="status" label="使用状态" :width="100" :filters="slaStatus.map((v) => ({ ...v, text: v.label }))">
            <template #default="{ row }">
              <el-tag class="ml-2" :type="row.status ? 'success' : 'danger'">
                {{ row.status ? "启用" : "禁用" }}
              </el-tag>
            </template>
          </TableColumn>

          <!-- <el-table-column align="left" prop="status" label="状态" :width="60" :formatter="(_row, _col, v) => h(ElText, { type: v ? 'success' : 'danger' }, () => (v ? $t('glob.Enable') : $t('glob.Disable')))"></el-table-column> -->
          <el-table-column align="left" prop="createTime" label="创建时间" :formatter="(_row, _col, v) => (v ? moment(v, 'x').format('yyyy-MM-DD HH:mm:ss') : '--')"></el-table-column>
          <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="126">
            <template #default="{ row }">
              <el-tooltip :content="$t('glob.noPower')" v-if="!row.globalEnable" :disabled="userInfo.hasPermission(PERMISSION.group515413286225707008.editor)">
                <span class="tw-h-fit tw-align-middle" v-show="!row.defaultRule">
                  <el-link :disabled="!userInfo.hasPermission(PERMISSION.group515413286225707008.editor)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleSlaEdit(row as DataItem)">{{ $t("glob.edit") }}</el-link>
                </span>
              </el-tooltip>

              <span class="tw-h-fit tw-align-middle" v-show="row.globalEnable">
                <el-link type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleSlaDefaultMessage(row as DataItem)">详情</el-link>
              </span>

              <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group515413286225707008.editor)">
                <span class="tw-h-fit tw-align-middle">
                  <el-link :disabled="!userInfo.hasPermission(PERMISSION.group515413286225707008.editor)" :type="row.status ? 'danger' : 'success'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="SlaConfigDisable(row as DataItem)">{{ row.status ? $t("glob.Disable") : $t("glob.Enable") }}</el-link>
                </span>
              </el-tooltip>
              <el-tooltip :content="$t('glob.noPower')" v-if="!row.globalEnable" :disabled="userInfo.hasPermission(PERMISSION.group515413286225707008.remove)">
                <span class="tw-h-fit tw-align-middle" v-show="!row.defaultRule">
                  <el-link :disabled="!userInfo.hasPermission(PERMISSION.group515413286225707008.remove)" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="SlaConfigDelete(row as DataItem)">{{ $t("glob.delete") }}</el-link>
                </span>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
  <el-dialog title="新增用户组" v-model="centerDialogVisible" :modal-append-to-body="false" width="40%" height="30%" left>
    <el-row>
      <el-col class="bold" :xs="24" :sm="6" :md="6">用户组名称：</el-col>
      <el-col :xs="24" :sm="14" :md="14">
        <el-input placeholder="请输入用户组名称" v-model="UserGroupName" style="width: 80%; margin-right: 30px"></el-input>
      </el-col>
      <el-col class="bold" :xs="24" :sm="6" :md="6">用户组成员：</el-col>
      <el-col :xs="24" :sm="14" :md="14">
        <el-input placeholder="请输入用户组成员" v-model="UserGroupMember" style="width: 80%; margin-right: 30px,margin-top:20px"></el-input>
      </el-col>
      <el-col class="bold" :xs="24" :sm="6" :md="6">群通知：</el-col>
      <el-col :xs="24" :sm="14" :md="14">
        <el-select v-model="value" placeholder="请选择群类型">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-input placeholder="请输入群通知地址" v-model="GroupAddress" style="width: 80%"></el-input>
      </el-col>
      <el-col class="bold" :xs="24" :sm="6" :md="6">用户组描述：</el-col>
      <el-col :xs="24" :sm="14" :md="14">
        <el-input type="textarea" placeholder="请输入用户组描述" v-model="GroupDesc" style="width: 80%; margin-right: 30px"></el-input>
      </el-col>
    </el-row>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="centerDialogVisible = false">确 定</el-button>
      </span>
    </template>
  </el-dialog>
  <Editor ref="editorRef" title="SLA" @confrim="getSlaList"></Editor>
  <messgaeView ref="messgaeViewRef" title="SLA" @confrim="getSlaList"></messgaeView>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox, ElText } from "element-plus";
import pageTemplate from "@/components/pageTemplate.vue";
import Editor from "./Editor.vue";
import messgaeView from "./messgaeView.vue";
import { getSlaDefault } from "@/views/pages/apis/SlaConfig";

import { state } from "./helper";
import { slaStatus } from "./common";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";

import { getSlaConfigByPage, DelSlaConfig, EnableSlaConfig, SlaConfigStatus, type SlaConfigList as DataItem } from "@/views/pages/apis/SlaConfig";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "SlaConfig" });
const editorRef = ref<InstanceType<typeof Editor>>();
const messgaeViewRef = ref<InstanceType<typeof messgaeView>>();

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const siteConfig = useSiteConfig();
const userInfo = ((key) => {
  switch (key) {
    case process.env["APP_SUPER_PLATFORM"]:
      return useSuperInfo();
    case process.env["APP_ADMIN_PLATFORM"]:
      return useAdminInfo();
    case process.env["APP_USERS_PLATFORM"]:
      return useUsersInfo();
    default:
      return null;
  }
})(siteConfig.current);

// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
// const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);

const tableLoading = ref(false);

const centerDialogVisible = ref(false);
// 群类型
const value = ref("");
// 搜索关键字
const ServiceSearch = ref("");
//状态值
const slaEnable = ref(null);
// 用户组名称
const UserGroupName = ref("");
// 用户组成员
const UserGroupMember = ref("");
// 群通知地址
const GroupAddress = ref("");
// 群通知描述
const GroupDesc = ref("");
// 群通知状态
const GroupState = ref(false);
const tableData = ref<DataItem[]>([]);
const options = ref([
  {
    value: "选项1",
    label: "黄金糕",
  },
]);
const paging = reactive({
  pageNumber: 1,
  pageSize: 50,
  total: 0,
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  getSlaList();
}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

function formatterTable(_row, _col, v) {
  switch (_col.property) {
    default:
      return v || "--";
  }
}
//搜索

function searchSlaList() {
  paging.pageNumber = 1;
  getSlaList();
}
//开启是否默认全局配置
function statusChange(row: Partial<Record<string, any>>) {
  // console.log(row);
  // tableData.value.forEach((v, i) => {
  //   if (v.id === row.id) {
  //     row.defaultRule = true;
  //   } else {
  //     row.defaultRule = false;
  //   }
  // });
  SlaConfigStatus({
    id: row.ruleId,
    defaultable: row.defaultRule,
  })
    .then((res) => {
      // console.log(res);
      if (res.success) {
        ElMessage.success("默认状态修改成功");
        getSlaList();
      }
    })
    .catch((e) => {
      ElMessage.error(e.message);
    });
}
//启禁用服务
function SlaConfigDisable(row: Partial<DataItem>) {
  let currState = row.status;
  const apiName = row.status ? "DisableSlaConfig" : "EnableSlaConfig";
  let params = {
    ruleId: row.ruleId,
    ruleStatus: !currState,
  };
  if (apiName === "DisableSlaConfig") {
    ElMessageBox.confirm(`确定禁用${row.ruleName}吗？禁用后，该服务级别协议将不再生效，请谨慎操作`, {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        EnableSlaConfig(params)
          .then(({ success, data, message }) => {
            if (!success) throw new Error(message);
            ElMessage.success("操作成功");
            getSlaList();
          })
          .catch((e) => {
            if (e instanceof Error) ElMessage.error(e.message);
          })
          .finally(() => {
            tableLoading.value = false;
          });
      })
      .catch(() => {
        // ElMessage.info("已取消禁用");
      });
  } else
    EnableSlaConfig(params)
      .then(({ success, data, message }) => {
        if (!success) throw new Error(message);
        ElMessage.success("操作成功");
        getSlaList();
      })
      .catch((e) => {
        if (e instanceof Error) ElMessage.error(e.message);
      })
      .finally(() => {
        tableLoading.value = false;
      });
}

function getSlaList() {
  let data = {
    ...paging,
    ruleName: ServiceSearch.value,
    slaEnable: slaEnable.value,
    tenantId: userInfo?.currentTenantId,
    // global: false,
  };

  tableLoading.value = true;
  getSlaConfigByPage(data)
    .then(({ success, data, page, size, total }) => {
      if (success) {
        let arr = [...data];
        let newArr = [];
        // // console.log()
        if (slaEnable.value === "" || slaEnable.value == null) {
          tableData.value = data;
        } else {
          if (slaEnable.value) {
            arr.forEach((v, i) => {
              if (v.status) {
                newArr.push(v);
              }
            });
            tableData.value = newArr;
          } else {
            arr.forEach((v, i) => {
              if (!v.status) {
                newArr.push(v);
              }
            });
            tableData.value = newArr;
          }
        }

        paging.total = Number(total);
        paging.pageNumber = page;
        paging.pageSize = size;
      }
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {
      tableLoading.value = false;
    });
}
async function handleCreate() {
  try {
    if (!editorRef.value) return;
    await editorRef.value.open({}, async (form) => {
      return true;
    });
  } catch (error) {
    /*  */
  } finally {
    /*  */
  }
  // router.push("/SlaConfig/create");
}
async function handleSlaEdit(row: Partial<DataItem>) {
  try {
    if (!editorRef.value) return;
    await editorRef.value.open(row, async (form) => {
      // editorRef.value
      return true;
    });
  } catch (error) {
    /*  */
  } finally {
    /*  */
  }
  // router.push(`/SlaConfig/edit/${row.ruleId}`);
}
const slaForm = ref({});
//  //获取默认sla规则

async function handleSlaDefaultMessage(row: Partial<DataItem>) {
  try {
    if (!messgaeViewRef.value) return;
    await messgaeViewRef.value.open(row, async (form) => {
      // console.log(res, 555);
      return true;
    });
  } catch (error) {
    /*  */
  } finally {
    /*  */
  }
  // router.push(`/SlaConfig/edit/${row.ruleId}`);
}

//删除用户组
function SlaConfigDelete(row: Partial<DataItem>) {
  if (row.status) {
    ElMessage.warning("启用状态下无法删除，请更改数据状态");
  } else
    ElMessageBox.confirm("此操作将永久删除该服务, 是否继续?", "删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        if (tableData.value.length == 2) {
          tableData.value.length = tableData.value.length - 1;
        }
        // tableData.value.find((i) => i.id === v.id))
        if (!row.status) {
          let params = {
            ruleId: row.ruleId,
          };
          DelSlaConfig(params)
            .then(({ success, data, message }) => {
              if (!success) throw new Error(message);
              paging.pageNumber = 1;
              ElMessage.success("操作成功");
              getSlaList();
            })
            .catch((e) => {
              if (e instanceof Error) ElMessage.error(e.message);
            })
            .finally(() => {
              tableLoading.value = false;
            });
        } else {
          ElMessage.warning("启用状态下无法删除，请更改数据状态");
        }
      })
      .catch(() => {
        // ElMessage.info("已取消删除");
      });
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
