<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="getSlaList()" @current-change="getSlaList()">
      <template #left>
        <div class="sla-config">
          <h2>
            SLA配置
            <el-icon @click="slaHelp" style="color: #2a8bf5; cursor: pointer; margin-left: 10px"><QuestionFilled /></el-icon>
          </h2>
        </div>
      </template>
      <template #right>
        <span class="tw-h-fit">
          <el-button v-if="userInfo.hasPermission(服务管理中心_SLA配置_新增)" type="primary" :icon="Plus" @click="handleCreate('add')">新增SLA</el-button>
        </span>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table stripe :data="tableData" :height="tableHeight" style="width: 100%" :row-key="getRowKeys" :expand-row-keys="expands" @expand-change="handleExpandChange">
          <el-table-column type="expand">
            <template #default="{ row, expanded }">
              <Editor v-if="row.hasPermissionIds.includes(服务管理中心_SLA配置_编辑)" :detail="row" :width="width - 40" @confirm="update"></Editor>
            </template>
          </el-table-column>
          <!-- <TableColumn type="default" show-filter v-model:filtered-value="ServiceSearch" @filter-change="handleQuery()" prop="ruleName" label="SLA名称" :width="320"> </TableColumn> -->

          <TableColumn type="condition" :prop="`ruleName`" :label="`SLA名称`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="handleQuery()" :formatter="formatterTable">
            <template #default="{ row }">
              <div style="font-size: 18; white-space: pre">{{ row.ruleName }}</div>
            </template>
          </TableColumn>

          <!-- <el-table-column align="left" prop="ruleDesc" label="SLA描述" :formatter="formatterTable"> </el-table-column> -->
          <TableColumn type="condition" :prop="`ruleDesc`" :label="`SLA描述`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByDescription" :filters="$filter0" @filter-change="handleQuery()" :formatter="formatterTable">
            <template #default="{ row }">
              <div style="font-size: 18; white-space: pre">{{ row.ruleDesc }}</div>
            </template>
          </TableColumn>

          <!-- <TableColumn type="enum" :prop="`active`" :label="`是否默认`" :min-width="100" :showOverflowTooltip="true" show-filter v-model:filtered-value="searchForm.defaultSla" :filters="[{ value: 'true', text: '√' }]" @filter-change="handleQuery()">
            <template #default="scope">
              <span> {{ scope.row.defaultSla == true ? "√" : "" }}</span>
            </template>
          </TableColumn> -->

          <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="126">
            <template #default="{ row }">
              <span class="tw-h-fit tw-align-middle">
                <el-link v-if="row.hasPermissionIds.includes(服务管理中心_SLA配置_编辑)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleCreate('edit', row as DataItem)">{{ $t("glob.edit") }}</el-link>
              </span>

              <span class="tw-h-fit tw-align-middle">
                <el-link v-if="row.hasPermissionIds.includes(服务管理中心_SLA配置_删除)" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="SlaConfigDelete(row as DataItem)">{{ $t("glob.delete") }}</el-link>
              </span>
              <span v-if="row.hasPermissionIds.includes(服务管理中心_SLA配置_安全)">
                <!-- SLA配置('604219214026244096') -->
                <el-link :type="row.hasPermissionIds.includes(服务管理中心_SLA配置_安全) ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="row.hasPermissionIds.includes(服务管理中心_SLA配置_安全) ? false : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
  <slaConfigCreate :dialog="slaConfigdialog" ref="slaConfigRef" @dialogClose="dialogClose"></slaConfigCreate>

  <el-dialog v-model="slaHelpdialog" :show-close="false" width="30%">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <h4 :id="titleId" :class="titleClass">SLA(服务级别协议)</h4>
        <el-icon style="cursor: pointer" @click="close" class="el-icon--left"><Close /></el-icon>
      </div>
    </template>
    <div class="text">
      <p>服务级别协议(SLA),是服务提供者对客户/用户问题工单的处理时效承诺,并不是一成不变的,可根据客户、客户的问题工单分类、甚至客户/用户购买的服务级别而设置不同的SLA。</p>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="slaHelpdialog = false">取消</el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog v-model="dialogVisibleshow" title="查看安全目录" width="500" :before-close="handleClose">
    <treeAuth :proptreeId="containerId" :treeStyle="treeStyle" ref="treeAuthRef"></treeAuth>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisibleshow = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox, ElText } from "element-plus";
import pageTemplate from "@/components/pageTemplate.vue";
import Editor from "./Editor.vue";
import messgaeView from "./messgaeView.vue";
import slaConfigCreate from "./slaConfigCreate.vue";
import { QuestionFilled } from "@element-plus/icons-vue";
import { getSlaDefault } from "@/views/pages/apis/SlaConfig";
import treeAuth from "@/components/treeAuth/index.vue";
import getUserInfo from "@/utils/getUserInfo";
// import { state } from "./helper";
import { slaStatus } from "./common";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";
import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";
import { 服务管理中心_SLA配置_新增, 服务管理中心_SLA配置_编辑, 服务管理中心_SLA配置_删除, 服务管理中心_SLA配置_安全 } from "@/views/pages/permission";

import { getSlaConfigByPage, getnewSlaConfigByPage, DelSlaConfig, EnableSlaConfig, SlaConfigStatus, type SlaConfigList as DataItem } from "@/views/pages/apis/SlaConfig";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "SlaConfig" });
const editorRef = ref<InstanceType<typeof Editor>>();
const messgaeViewRef = ref<InstanceType<typeof messgaeView>>();

const slaConfigdialog = ref(false);
const slaHelpdialog = ref(false);

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const siteConfig = useSiteConfig();
const userInfoS = getUserInfo();
const userInfo = ((key) => {
  switch (key) {
    case process.env["APP_SUPER_PLATFORM"]:
      return useSuperInfo();
    case process.env["APP_ADMIN_PLATFORM"]:
      return useAdminInfo();
    case process.env["APP_USERS_PLATFORM"]:
      return useUsersInfo();
    default:
      return null;
  }
})(siteConfig.current);

// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
// const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);

// const $filter0 = ref([
//   { text: "包含", value: "include" },
//   { text: "不包含", value: "exclude" },
//   { text: "等于", value: "eq" },
//   { text: "不等于", value: "ne" },
// ]);
const $filter0 = ref(exoprtMatch1);

const searchForm = ref<Record<string, any>>({
  eqName: [] /* 等于的SLA名称 */,
  includeName: [] /* 包含的SLA名称 */,
  nameFilterRelation: "AND" /* SLA名称过滤关系(AND,OR) */,
  neName: [] /* 不等于的SLA名称 */,
  excludeName: [] /* 不包含的SLA名称 */,

  eqDescription: [] /* 等于的SLA描述 */,
  includeDescription: [] /* 包含的SLA描述 */,
  descriptionFilterRelation: "AND" /* SLA描述过滤关系(AND,OR) */,
  neDescription: [] /* 不等于的SLA描述 */,
  excludeDescription: [] /* 不包含的SLA描述 */,

  defaultSla: "" /* 是否默认 */,
});

const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByName) === "include") value0 = searchForm.value.includeName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = searchForm.value.excludeName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = searchForm.value.eqName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = searchForm.value.neName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = searchForm.value.includeName[searchForm.value.includeName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = searchForm.value.excludeName[searchForm.value.excludeName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = searchForm.value.eqName[searchForm.value.eqName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = searchForm.value.neName[searchForm.value.neName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: searchForm.value.nameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.nameFilterRelation = v.relation;
    searchForm.value.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByDescription = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByDescription) === "include") value0 = searchForm.value.includeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "exclude") value0 = searchForm.value.excludeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "eq") value0 = searchForm.value.eqDescription[0] || "";
    if (toValue(searchType0ByDescription) === "ne") value0 = searchForm.value.neDescription[0] || "";
    let value1 = "";
    if (toValue(searchType1ByDescription) === "include") value1 = searchForm.value.includeDescription[searchForm.value.includeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "exclude") value1 = searchForm.value.excludeDescription[searchForm.value.excludeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "eq") value1 = searchForm.value.eqDescription[searchForm.value.eqDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "ne") value1 = searchForm.value.neDescription[searchForm.value.neDescription.length - 1] || "";
    return {
      type0: toValue(searchType0ByDescription),
      type1: toValue(searchType1ByDescription),
      relation: searchForm.value.descriptionFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByDescription.value = v.type0 as typeof searchType0ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchType1ByDescription.value = v.type1 as typeof searchType1ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.descriptionFilterRelation = v.relation;
    searchForm.value.includeDescription = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeDescription = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqDescription = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neDescription = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const tableLoading = ref(false);

const centerDialogVisible = ref(false);
// 群类型
const value = ref("");
// 搜索关键字
const ServiceSearch = ref("");
//状态值
const slaEnable = ref(null);
// 用户组名称
const UserGroupName = ref("");
// 用户组成员
const UserGroupMember = ref("");
// 群通知地址
const GroupAddress = ref("");
// 群通知描述
const GroupDesc = ref("");
// 群通知状态
const GroupState = ref(false);
const tableData = ref<DataItem[]>([]);
const expands = ref<string[]>([]);
const containerId = ref("");
const dialogVisibleshow = ref(false);
const treeAuthRef = ref<InstanceType<typeof treeAuth>>();
const options = ref([
  {
    value: "选项1",
    label: "黄金糕",
  },
]);
const paging = reactive({
  pageNumber: 1,
  pageSize: 50,
  total: 0,
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  getSlaList();
}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

function formatterTable(_row, _col, v) {
  switch (_col.property) {
    default:
      return v || "--";
  }
}
//搜索

function searchSlaList() {
  paging.pageNumber = 1;
  getSlaList();
}
function getRowKeys(row) {
  return row.ruleId;
}
async function handleExpandChange(row, expandedRows) {
  if (expandedRows.length) {
    //展开
    expands.value = [];
    if (row) {
      expands.value.push(row.ruleId);
    }
  } else {
    expands.value = [];
  }
}
//开启是否默认全局配置
function statusChange(row: Partial<Record<string, any>>) {
  // console.log(row);
  // tableData.value.forEach((v, i) => {
  //   if (v.id === row.id) {
  //     row.defaultRule = true;
  //   } else {
  //     row.defaultRule = false;
  //   }
  // });
  SlaConfigStatus({
    id: row.ruleId,
    defaultable: row.defaultRule,
  })
    .then((res) => {
      // console.log(res);
      if (res.success) {
        ElMessage.success("默认状态修改成功");
        getSlaList();
      }
    })
    .catch((e) => {
      ElMessage.error(e.message);
    });
}
//启禁用服务
function SlaConfigDisable(row: Partial<DataItem>) {
  let currState = row.status;
  const apiName = row.status ? "DisableSlaConfig" : "EnableSlaConfig";
  let params = {
    ruleId: row.ruleId,
    ruleStatus: !currState,
  };
  if (apiName === "DisableSlaConfig") {
    ElMessageBox.confirm(`确定禁用${row.ruleName}吗？禁用后，该服务级别协议将不再生效，请谨慎操作`, {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        EnableSlaConfig(params)
          .then(({ success, data, message }) => {
            if (!success) throw new Error(message);
            ElMessage.success("操作成功");
            getSlaList();
          })
          .catch((e) => {
            if (e instanceof Error) ElMessage.error(e.message);
          })
          .finally(() => {
            tableLoading.value = false;
          });
      })
      .catch(() => {
        // ElMessage.info("已取消禁用");
      });
  } else
    EnableSlaConfig(params)
      .then(({ success, data, message }) => {
        if (!success) throw new Error(message);
        ElMessage.success("操作成功");
        getSlaList();
      })
      .catch((e) => {
        if (e instanceof Error) ElMessage.error(e.message);
      })
      .finally(() => {
        tableLoading.value = false;
      });
}

async function handleQuery() {
  paging.pageNumber = 1;
  await nextTick();
  await getSlaList();
}

function getSlaList() {
  let data = {
    ...paging,
    ruleName: ServiceSearch.value,
    slaEnable: slaEnable.value,
    tenantId: userInfo?.currentTenantId,
    containerId: userInfoS.currentTenant.containerId,
    queryPermissionId: "515413313438351360",
    verifyPermissionIds: "515413363665141760,515413387727863808,612169263167307776",
    // global: false,
    ...searchForm.value,
  };

  tableLoading.value = true;
  getnewSlaConfigByPage(data)
    .then(({ success, data, page, size, total }) => {
      if (success) {
        let arr = [...data];
        let newArr = [];
        // // console.log()
        if (slaEnable.value === "" || slaEnable.value == null) {
          tableData.value = data;
        } else {
          if (slaEnable.value) {
            arr.forEach((v, i) => {
              if (v.status) {
                newArr.push(v);
              }
            });
            tableData.value = newArr;
          } else {
            arr.forEach((v, i) => {
              if (!v.status) {
                newArr.push(v);
              }
            });
            tableData.value = newArr;
          }
        }

        paging.total = Number(total);
        paging.pageNumber = page;
        paging.pageSize = size;
      }
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {
      tableLoading.value = false;
    });
}
// 新增编辑SLA
async function handleCreate(type, row) {
  try {
    ctx.refs.slaConfigRef.type = type;
    if (type === "add") {
      ctx.refs.slaConfigRef.title = "新增SLA";
    } else {
      ctx.refs.slaConfigRef.form = {
        ruleName: row.ruleName,
        ruleDesc: row.ruleDesc,
        ruleId: row.ruleId,
        defaultSla: row.defaultSla,
      };
      ctx.refs.slaConfigRef.changeSlav = null;
      ctx.refs.slaConfigRef.title = "编辑SLA";
    }
    slaConfigdialog.value = true;
  } catch (error) {
    /*  */
  } finally {
    /*  */
  }
}
//关闭弹框
function dialogClose(bool) {
  slaConfigdialog.value = bool;
  getSlaList();
}
//sla帮助文件
function slaHelp() {
  slaHelpdialog.value = true;
}
async function handleSlaEdit(row: Partial<DataItem>) {
  try {
    if (!editorRef.value) return;
    await editorRef.value.open(row, async (form) => {
      // editorRef.value
      return true;
    });
  } catch (error) {
    /*  */
  } finally {
    /*  */
  }
  // router.push(`/SlaConfig/edit/${row.ruleId}`);
}
const slaForm = ref({});
//  //获取默认sla规则

async function handleSlaDefaultMessage(row: Partial<DataItem>) {
  try {
    if (!messgaeViewRef.value) return;
    await messgaeViewRef.value.open(row, async (form) => {
      // console.log(res, 555);
      return true;
    });
  } catch (error) {
    /*  */
  } finally {
    /*  */
  }
  // router.push(`/SlaConfig/edit/${row.ruleId}`);
}

//删除
function SlaConfigDelete(row: Partial<DataItem>) {
  if (row.status) {
    ElMessage.warning("启用状态下无法删除，请更改数据状态");
  } else
    ElMessageBox.confirm("此操作将永久删除该服务, 是否继续?", "删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        if (tableData.value.length == 2) {
          tableData.value.length = tableData.value.length - 1;
        }
        // tableData.value.find((i) => i.id === v.id))
        if (!row.status) {
          let params = {
            ruleId: row.ruleId,
          };
          DelSlaConfig(params)
            .then(({ success, data, message }) => {
              if (!success) throw new Error(message);
              paging.pageNumber = 1;
              ElMessage.success("操作成功");
              getSlaList();
            })
            .catch((e) => {
              if (e instanceof Error) ElMessage.error(e.message);
              getSlaList();
            })
            .finally(() => {
              tableLoading.value = false;
              getSlaList();
            });
        } else {
          ElMessage.warning("启用状态下无法删除，请更改数据状态");
          getSlaList();
        }
      })
      .catch(() => {
        // ElMessage.info("已取消删除");
      });
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.sla-config {
  display: flex;
  align-items: center;
  h2 {
    display: flex;
    align-items: center;
    height: 100%;
    font-size: 14px;
    padding-left: 10px;
    box-sizing: border-box;
    font-weight: 700;
    margin-top: 15px;
  }
}
</style>
