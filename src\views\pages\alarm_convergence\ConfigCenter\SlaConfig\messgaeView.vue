<template>
  <!-- 对话框表单 -->
  <el-drawer class="el-card tw-bottom-[16px] tw-right-[16px] tw-top-[120px]" :size="width" :style="{ height: `${height}px` }" v-model="data.visible" :close-on-click-modal="false" :show-close="false" :modal="false" :before-close="handleCancel">
    <template #header>
      <div class="tw-flex tw-h-[30px] tw-flex-nowrap tw-items-center">
        <el-page-header class="tw-mr-auto" :content="`${props.title}详情`" @back="handleCancel()"></el-page-header>
      </div>
    </template>
    <template #default>
      <el-scrollbar v-loading="state.loading">
        <el-form :model="form" :style="{ marginTop: '10px' }" label-position="left" class="edit_sla_config">
          <el-card class="el-card-mt">
            <template #header>
              <div class="modules-item">
                <span class="modules-title">SLA基本信息</span>
                <span class="modules-tips">
                  <el-icon><el-icon-info /></el-icon>
                  服务级别协议(SLA),是服务提供者对客户/用户问题工单的处理时效承诺,并不是一成不变的,可根据客户、客户的问题工单分类、甚至客户/用户购买的服务级别而设置不同的SLA</span
                >
              </div>
            </template>
            <div>
              <el-form-item>
                <el-row>
                  <el-col style="text-align: left" class="bold" :span="4">
                    <span style="color: red">*</span>
                    SLA名称：
                  </el-col>
                  <el-col :span="14">
                    <el-input v-model="slaName" placeholder="请输入SLA名称" :style="basicClassInput" style="margin-left: 30px" disabled />
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item>
                <el-row>
                  <el-col style="text-align: left" class="bold" :span="4"> SLA描述： </el-col>
                  <el-col :span="14">
                    <el-input v-model="slaDesc" type="textarea" placeholder="请输入SLA描述" :style="basicClassInput" style="margin-left: 30px" disabled />
                  </el-col>
                </el-row>
              </el-form-item>
              <!-- <el-form-item>
                <el-row>
                  <el-col :style="{ textAlign: 'center', marginTop: '10px' }">
                    <el-button type="primary" @click="CreateSlaConfig">保存</el-button>
                  </el-col>
                </el-row>
              </el-form-item> -->
            </div>
          </el-card>
          <el-card class="el-card-mt">
            <template #header>
              <div class="modules-item">
                <span class="modules-title">规则设置</span>
              </div>
            </template>
            <el-row>
              <!-- <div class="modules-item" style="margin-left: 30px">
                <el-button type="primary" icon="el-icon-plus" :disabled="AddSubruleDisabled" @click="CreateSubrule">新增子规则</el-button>
              </div> -->

              <el-form-item style="width: 100%">
                <el-row style="width: 100%">
                  <el-col class="bold" :span="3" style="text-align: left">覆盖时间：</el-col>
                  <el-col class="bold" :span="15" style="text-align: right">选择时区：</el-col>
                  <el-col :span="6" style="text-align: right">
                    <el-select v-model="coverTimeCfg.timeZone" filterable placeholder="请选择" :style="basicClassInputDown" style="margin-left: 30px" disabled>
                      <el-option v-for="item in timeZone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"> </el-option>
                    </el-select>
                  </el-col>

                  <el-col>
                    <div style="width: 100%" class="support-table-content" ref="tableContentRef">
                      <el-table stripe border :data="coverTimeCfg.coverWorkTime" style="width: 100%; margin-top: 30px" @header-click="(column, $event) => handleClick({ column, $event })">
                        <el-table-column align="left" prop="week" width="80">
                          <template #default="scope">
                            <div @click="handleSelectTime('all', scope.$index, scope.row)" disabled>
                              {{ scope.row.weekDay == 1 ? "周一" : scope.row.weekDay == 2 ? "周二" : scope.row.weekDay == 3 ? "周三" : scope.row.weekDay == 4 ? "周四" : scope.row.weekDay == 5 ? "周五" : scope.row.weekDay == 6 ? "周六" : "周日" }}
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column align="left" :width="tableWidth" v-for="(item, key) in 24" :key="`h-${key}`" :label="String(key)">
                          <template #default="scope">
                            <div @click="handleSelectTime(key, scope.$index, scope.row)" style="width: 100%; height: 100%">
                              <el-button type="text" style="font-size: 30px; color: rgb(26, 190, 107)" disabled>
                                <el-icon v-if="scope.row.workTime && scope.row.workTime.length && scope.row.workTime.includes(key)">
                                  <Check></Check>
                                </el-icon>
                              </el-button>
                            </div>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item class="things" style="width: 100%">
                <el-row style="width: 100%">
                  <el-col class="bold" :span="3" style="text-align: left"> 事件响应时间等级：</el-col>
                  <el-col :span="21" style="text-align: right">
                    <el-button type="primary" :icon="ElIconPlus" @click="addLevel('response')" disabled> 新增等级</el-button>
                  </el-col>
                  <el-col>
                    <el-table stripe :data="responseTimeList" style="width: 100%; margin-top: 30px">
                      <el-table-column align="left" label="事件优先级">
                        <template #default="{ row }">
                          <span
                            v-if="row.state"
                            class="state"
                            :style="{
                              color: '#fff',
                              backgroundColor: eventPriorityColor[row.priority as keyof typeof eventPriorityColor].color,
                            }"
                            >{{ row.priority }}</span
                          >
                          <el-select v-else v-model="row.priority">
                            <el-option v-for="item in responseData" :key="item.value" :label="item.value" :value="item.value"> </el-option>
                          </el-select>
                        </template>
                      </el-table-column>
                      <el-table-column align="left" label="容忍时间" width="300">
                        <template #default="scope">
                          <div v-show="scope.row.state">
                            {{ scope.row.resolves[0].day ? scope.row.resolves[0].day + "day" : "" }}
                            {{ scope.row.resolves[0].hour ? scope.row.resolves[0].hour + "h" : "" }}
                            {{ scope.row.resolves[0].minute ? scope.row.resolves[0].minute + "min" : "" }}
                          </div>
                          <div v-show="!scope.row.state">
                            <el-input-number :min="0" v-model="scope.row.resolves[0].day" :max="10000000000"> </el-input-number>
                            day
                            <el-input-number :min="0" v-model="scope.row.resolves[0].hour" :max="23"> </el-input-number>
                            h
                            <el-input-number :min="0" v-model="scope.row.resolves[0].minute" :max="59"> </el-input-number>
                            min
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column align="left" label="SLA状态" prop="urgencyType">
                        <template #default="scope">
                          <span
                            class="state"
                            v-show="scope.row.state"
                            :style="{
                              color: '#fff',
                              backgroundColor: slaStateColor[scope.row.resolves[0].urgencyType?.toLowerCase()],
                            }"
                          >
                            <span v-show="scope.row.resolves[0].urgencyType">
                              {{ scope.row.resolves[0].urgencyType?.slice(0, 1).toUpperCase() + scope.row.resolves[0].urgencyType?.slice(1).toLowerCase() }}
                            </span>
                            <span v-show="!scope.row.resolves[0].urgencyType"> </span>
                          </span>
                          <el-select v-show="!scope.row.state" v-model="scope.row.resolves[0].urgencyType">
                            <el-option v-for="item in statusData" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                          </el-select>
                        </template>
                      </el-table-column>
                      <el-table-column align="left" label="名称">
                        <template #default="scope">
                          <div v-show="scope.row.state">
                            {{ scope.row.resolves[0].name }}
                          </div>
                          <div v-show="!scope.row.state">
                            <el-input v-model="scope.row.resolves[0].name"> </el-input>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column align="left" label="描述">
                        <template #default="scope">
                          <div v-show="scope.row.state">
                            {{ scope.row.resolves[0].description }}
                          </div>
                          <div v-show="!scope.row.state">
                            <el-input v-model="scope.row.resolves[0].description"> </el-input>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column align="left" label="定义">
                        <template #default="scope">
                          <div v-show="scope.row.state">
                            {{ scope.row.resolves[0].definition }}
                          </div>
                          <div v-show="!scope.row.state">
                            <el-input v-model="scope.row.resolves[0].definition"> </el-input>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column align="left" label="操作">
                        <template #default="scope">
                          <el-button type="text" v-show="scope.row.state" @click="editLevel('response', scope.$index, scope.row)" style="margin-right: 10px" disabled> 编辑</el-button>
                          <el-button type="text" v-show="!scope.row.state" @click="confirmLevel('response', scope.$index, scope.row)" disabled>保存</el-button>

                          <el-popconfirm :title="delTitle" @confirm="delConfirm('response', scope.$index, scope.row)">
                            <template #reference>
                              <el-button type="text" textColor="danger" @click="delLevel('response', scope.$index, scope.row)" disabled>删除</el-button>
                            </template>
                          </el-popconfirm>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-col>
                </el-row>

                <el-row style="margin-top: 20px; width: 100%">
                  <el-col class="bold" :span="3" style="text-align: left"> 事件处理时间等级：</el-col>
                  <el-col :span="21" style="text-align: right">
                    <el-button type="primary" :icon="ElIconPlus" @click="addLevel('resolve')" disabled>新增等级</el-button>
                  </el-col>
                  <el-col>
                    <el-table stripe :data="handelTimeList" style="width: 100%; margin-top: 30px">
                      <el-table-column align="left" label="事件优先级">
                        <template #default="{ row }">
                          <span
                            v-if="row.state"
                            class="state"
                            :style="{
                              color: '#fff',
                              backgroundColor: eventPriorityColor[row.priority as keyof typeof eventPriorityColor].color,
                            }"
                            >{{ row.priority }}</span
                          >
                          <el-select v-else v-model="row.priority">
                            <el-option v-for="item in responseData" :key="item.value" :label="item.value" :value="item.value"> </el-option>
                          </el-select>
                        </template>
                      </el-table-column>
                      <el-table-column align="left" label="容忍时间" width="300">
                        <template #default="scope">
                          <div v-show="scope.row.state">
                            {{ scope.row.resolves[0].day ? scope.row.resolves[0].day + "day" : "" }}
                            {{ scope.row.resolves[0].hour ? scope.row.resolves[0].hour + "h" : "" }}
                            {{ scope.row.resolves[0].minute ? scope.row.resolves[0].minute + "min" : "" }}
                          </div>
                          <div v-show="!scope.row.state">
                            <el-input-number :min="0" v-model="scope.row.resolves[0].day" :max="10000000000"> </el-input-number>
                            day
                            <el-input-number :min="0" v-model="scope.row.resolves[0].hour" :max="23"> </el-input-number>
                            h
                            <el-input-number :min="0" v-model="scope.row.resolves[0].minute" :max="59"> </el-input-number>
                            min
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column align="left" label="SLA状态" prop="urgencyType">
                        <template #default="scope">
                          <span
                            class="state"
                            v-show="scope.row.state"
                            :style="{
                              color: '#fff',
                              backgroundColor: slaStateColor[scope.row.resolves[0].urgencyType?.toLowerCase()],
                            }"
                          >
                            <span v-show="scope.row.resolves[0].urgencyType">
                              {{ scope.row.resolves[0].urgencyType?.slice(0, 1).toUpperCase() + scope.row.resolves[0].urgencyType?.slice(1).toLowerCase() }}
                            </span>
                            <span v-show="!scope.row.resolves[0].urgencyType"> </span>
                          </span>
                          <el-select v-show="!scope.row.state" v-model="scope.row.resolves[0].urgencyType">
                            <el-option v-for="item in statusData" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                          </el-select>
                        </template>
                      </el-table-column>
                      <el-table-column align="left" label="名称">
                        <template #default="scope">
                          <div v-show="scope.row.state">
                            {{ scope.row.resolves[0].name }}
                          </div>
                          <div v-show="!scope.row.state">
                            <el-input v-model="scope.row.resolves[0].name"> </el-input>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column align="left" label="描述">
                        <template #default="scope">
                          <div v-show="scope.row.state">
                            {{ scope.row.resolves[0].description }}
                          </div>
                          <div v-show="!scope.row.state">
                            <el-input v-model="scope.row.resolves[0].description"> </el-input>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column align="left" label="定义">
                        <template #default="scope">
                          <div v-show="scope.row.state">
                            {{ scope.row.resolves[0].definition }}
                          </div>
                          <div v-show="!scope.row.state">
                            <el-input v-model="scope.row.resolves[0].definition"> </el-input>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column align="left" label="操作">
                        <template #default="scope">
                          <el-button type="text" v-show="scope.row.state" @click="editLevel('resolve', scope.$index, scope.row)" style="margin-right: 10px" disabled>编辑</el-button>
                          <el-button type="text" v-show="!scope.row.state" @click="confirmLevel('resolve', scope.$index, scope.row)" disabled>保存</el-button>

                          <el-popconfirm :title="delTitle" @confirm="delConfirm('resolve', scope.$index, scope.row)">
                            <template #reference>
                              <el-button type="text" textColor="danger" @click="delLevel('resolve', scope.$index, scope.row)" disabled>删除</el-button>
                            </template>
                          </el-popconfirm>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-col>
                  <el-col class="bold" :span="24" style="text-align: center; margin-top: 30px">
                    <!-- <el-button @click="baocunLevel" type="primary">返回</el-button> -->
                  </el-col>
                </el-row>
              </el-form-item>

              <!-- <el-button type="text" v-show="row.createTime" @click="EditSubruleExpand()">编辑</el-button>
              <el-button type="text" textColor="danger" @click="DelSubrule()">删除</el-button> -->
            </el-row>
          </el-card>
          <!-- 选择事件时间等级 -->

          <!-- <el-col :style="{ textAlign: 'center', marginTop: '50px' }">
            <el-button v-if="!isEdit" type="primary" @click="CreateSlaConfig">确 定</el-button>
            <el-button v-if="isEdit" type="primary" @click="EditSlaConfig">确 定</el-button>
            <el-button>取 消</el-button>
          </el-col> -->
        </el-form>
      </el-scrollbar>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <!-- <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button> -->
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </el-drawer>
</template>

<!--  generic="Item extends Record<'id', string> & Record<string, unknown>" -->
<script setup lang="ts" name="EditorForm">
/* eslint-disable no-unused-vars, @typescript-eslint/no-unused-vars */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { getCurrentInstance } from "vue";

import { readonly, reactive, ref, nextTick, inject, h } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { TypeHelper } from "@/utils/type";
import { InfoFilled as ElIconInfo, Plus as ElIconPlus } from "@element-plus/icons-vue";

import getUserInfo from "@/utils/getUserInfo";
import { Check } from "@element-plus/icons-vue";
import { type SlaConfigList as DataItem } from "@/views/pages/apis/SlaConfig";
import * as Api from "@/views/pages/apis/SlaConfig";

import { responseData as _responseData, resolveData as _resolveData, priority, statusData as _statusData } from "./common";
import { getSlaDefault } from "@/views/pages/apis/SlaConfig";

import { slaState } from "@/views/pages/common/slaState";
import _timeZone from "@/views/pages/common/zone.json";
import { useResizeObserver } from "@vueuse/core";
const emit = defineEmits(["confrim"]);

const customerItemId = ref("");
const packagePreview = ref(false);
const isShow = ref(false);
const content = ref("新增SLA");
const eventPriorityColor = ref(priority);
const customerName = ref("");
const subruleName = ref("");
const subruleDesc = ref("");
// sla名称
const slaName = ref("");
// sla类型
const slaType = ref("");
// sla描述
const slaDesc = ref("");
// 选择时区
const DefaultTime = ref("");
// 优先级等级
const PriorityLevel = ref("");
const SlaDialogVisible = ref(false);
// const form = ref({});
const customerNameList = ref([]);
const tableData = ref([]);
const responseData = ref(_responseData);
const resolveData = ref(_resolveData);
const statusData = ref(_statusData);
const timeZone = ref(_timeZone as { zoneId: string; displayName: string }[]);
const slaStateColor = ref(slaState);
const TimeAddress = ref([]);
const expands = ref([]);
const index = ref(1);

const coverTimeCfg = ref({
  timeZone: "客户默认",
  useCustomerTimeZone: false,
  useDeviceTimeZone: false,
  coverWorkTime: [
    {
      week: "周一",
      workTime: [],
      weekDay: 1,
    },
    {
      week: "周二",
      workTime: [],
      weekDay: 2,
    },
    {
      week: "周三",
      workTime: [],
      weekDay: 3,
    },
    {
      week: "周四",
      workTime: [],
      weekDay: 4,
    },
    {
      week: "周五",
      workTime: [],
      weekDay: 5,
    },
    {
      week: "周六",
      workTime: [],
      weekDay: 6,
    },
    {
      week: "周日",
      workTime: [],
      weekDay: 7,
    },
  ],
});

const allBool = ref([false, false, false, false, false, false, false]);
const basicClassInput = ref({ width: "35.8vw" }); /* 输入框选择器基本样式 */
const basicClassInputDown = ref({ width: "15.8vw" }); /* 输入框选择器基本样式 */
//事件时间等级弹框
const TimeDialogVisible = ref(false);
//事件响应时间等级
const responseTimeList = ref<Record<string, any>[]>([]);
const resList = ref<Record<string, any>[]>([]);
//事件处理时间等级
const handelTimeList = ref<Record<string, any>[]>([]);
const timePriority = ref("");
const eventType = ref("");
const eventTypeList = ref<Record<string, any>[]>([]);
const ruleId = ref("");
const chooseLevelType = ref("");
const delTitle = ref("");

const tableContentRef = ref();
const tableWidth = ref(0);
useResizeObserver(tableContentRef, (entries) => {
  const entry = entries[0];
  const { width, height } = entry.contentRect;
  tableWidth.value = ((width - 80) / 24).toFixed(0);
});
/**
 * TODO: 本地方法
 */
//编辑事件操作
function editLevel(type: string, index: number, data: any) {
  data.state = false;
}
//保存事件操作
function confirmLevel(type: string, index: number, data: any) {
  //type 类型
  //data 输入框中的数据
  //index当前操作的下标

  if (data.resolves[0].minute == 0 && data.resolves[0].hour == 0 && data.resolves[0].day == 0) {
    data.state = false;
    return ElMessage.warning("容忍时间不能为0");
  }
  if (data.priority) {
    if (type === "response") {
      const hasObj: Record<string, unknown> = {};

      type DataType = (typeof responseTimeList.value)[number];

      responseTimeList.value = responseTimeList.value.reduce((total: DataType[], next: DataType) => {
        const filterKey = next.priority + next.resolves[0].urgencyType;

        if (hasObj[filterKey] != undefined) {
          data.state = false;
          next.state = false;
          total.push(next);
          ElMessage.warning("SLA状态不可重复");
        } else data.state = true;
        if (filterKey) {
          hasObj[filterKey] ? "" : (hasObj[filterKey] = true && total.push(next));
        } else {
          total.push(next);
        }

        return total;
      }, []);
    } else {
      const hasObj: Record<string, unknown> = {};

      type DataType = (typeof responseTimeList.value)[number];

      handelTimeList.value = handelTimeList.value.reduce((total: DataType[], next: DataType) => {
        const filterKey = next.priority + next.resolves[0].urgencyType;

        if (hasObj[filterKey] != undefined) {
          next.state = false;
          total.push(next);
          ElMessage.warning("SLA状态不可重复");
        } else data.state = true;
        if (filterKey) {
          hasObj[filterKey] ? "" : (hasObj[filterKey] = true && total.push(next));
          next.state = true;
        } else {
          total.push(next);
        }

        return total;
      }, []);
    }
    // data.state = true;
  } else {
    return ElMessage.warning("请先选择事件优先级");
  }
  //
  //
}
//删除事件操作
function delLevel(type: string, index: number, data: any) {
  delTitle.value = "确认删除当前等级为" + data.priority + "的所有内容吗？";
}
function delConfirm(type: string, index: number, data: any) {
  // console.log(data);
  if (type == "response") {
    if (data.id) {
      responseTimeList.value.splice(index, 1);
      ElMessage.success("删除成功");
    } else {
      responseTimeList.value.splice(index, 1);
      ElMessage.success("删除成功");
    }
  } else {
    if (data.id) {
      handelTimeList.value.splice(index, 1);
      ElMessage.success("删除成功");
    } else {
      handelTimeList.value.splice(index, 1);
    }
  }
}
//新增等级
function addLevel(type: string) {
  eventType.value = "";
  chooseLevelType.value = type;
  TimeDialogVisible.value = true;

  if (type === "response") {
    responseTimeList.value.push({
      priority: "",
      resolves: [
        {
          day: "",
          hour: "",
          minute: "",
          urgencyType: null,
          name: "",
          description: "",
          definition: "",
        },
      ],
      state: false,
    });
  } else {
    handelTimeList.value.push({
      priority: "",
      resolves: [
        {
          day: "",
          hour: "",
          minute: "",
          urgencyType: null,
          name: "",
          description: "",
          definition: "",
        },
      ],
      state: false,
    });
  }
}
//事件时间等级保存
function baocunLevel() {
  if (coverTimeCfg.value.timeZone === "客户默认") {
    coverTimeCfg.value.useCustomerTimeZone = true;
    coverTimeCfg.value.useDeviceTimeZone = false;
  } else if (coverTimeCfg.value.timeZone === "设备默认") {
    coverTimeCfg.value.useDeviceTimeZone = true;
    coverTimeCfg.value.useCustomerTimeZone = false;
  } else {
    coverTimeCfg.value.useCustomerTimeZone = false;
    coverTimeCfg.value.useDeviceTimeZone = false;
  }
  let eventRespTimeLevels: any[] = [];
  let eventRespDefaultResolves: any[] = [];
  let eventResolveTimeLevels: any[] = [];
  let eventResolveDefaultResolves: any[] = [];
  responseTimeList.value.forEach((v, i) => {
    if (v.priority !== "Default" && v.state) {
      eventRespTimeLevels.push(v);
    } else if (v.priority === "Default" && v.state) {
      eventRespDefaultResolves.push(v.resolves[0]);
    }
  });
  handelTimeList.value.forEach((v, i) => {
    if (v.priority !== "Default" && v.state) {
      eventResolveTimeLevels.push(v);
    } else if (v.priority === "Default" && v.state) {
      eventResolveDefaultResolves.push(v.resolves[0]);
    }
  });
  let data = {
    tenantId: getUserInfo()?.currentTenantId,
    ruleName: slaName.value,
    ruleDesc: slaDesc.value,
    coverTimeCfg: coverTimeCfg.value,
    eventRespTimeLevels,
    eventRespDefaultResolves,
    eventResolveTimeLevels,
    eventResolveDefaultResolves,
    defaultRule: false,
  };
  if (!$params.value.ruleId) {
    AddSla(data);
  } else {
    EditSlaConfig(data);
  }

  // addResponseLevel({
  //   itemId: this.customerItemId,
  //   list: responseTimeList.value,
  // }).then((res) => {
  //   // console.log(res);
  // });
}

//获取基本信息详情
function getSlaDetail() {
  // console.log($params.value, form);
  getSlaDefault({ global: true })
    .then(({ success, data }) => {
      if (success) {
        coverTimeCfg.value = (data as any).coverTimeCfg;

        coverTimeCfg.value.coverWorkTime.forEach((item: any) => {
          // console.log(item);
          if (item.workTime.length > 23) {
            allBool.value[item.weekDay - 1] = true;
          } else {
            allBool.value[item.weekDay - 1] = false;
          }
        });
        slaName.value = (data as any).ruleName;
        slaDesc.value = (data as any).ruleDesc;
        let responseData: any[] = [];
        let resolveData: any[] = [];
        (data as any).eventRespDefaultResolves.forEach((v: any, i: number) => {
          responseData.push({
            priority: "Default",
            resolves: [v],
            state: true,
          });
        });
        (data as any).eventRespTimeLevels.forEach((v: any, i: number) => {
          if (v.resolves.length > 0) {
            v.resolves.forEach((item: Record<string, unknown>) => {
              responseData.push({
                priority: v.priority,
                resolves: [item],
                state: true,
              });
            });
          }
        });
        (data as any).eventResolveDefaultResolves.forEach((v: any, i: number) => {
          resolveData.push({
            priority: "Default",
            resolves: [v],
            state: true,
          });
        });
        (data as any).eventResolveTimeLevels.forEach((v: any, i: number) => {
          if (v.resolves.length > 0) {
            v.resolves.forEach((item: Record<string, unknown>) => {
              resolveData.push({
                priority: v.priority,
                resolves: [item],
                state: true,
              });
            });
          }
        });
        responseTimeList.value = [...responseData];
        handelTimeList.value = [...resolveData];

        // this.tableData = data.slaItems;
      }
    })
    .catch((e) => {
      ElMessage.error(e.message);
    });
}
//覆盖时间列
function handleClick({ column, evene }: Record<string, any>) {
  let index = Number(column.label);
  // // console.log(index, column);
  coverTimeCfg.value.coverWorkTime.forEach((v: any, i) => {
    if (v.workTime[v.workTime.length - 1] === index) {
      v.workTime.pop();
      v.workTime = [...new Set(v.workTime)];
    } else {
      v.workTime.push(index as any);
      v.workTime = [...new Set(v.workTime)];
    }
  });
}
//覆盖时间
function handleSelectTime(key: string | number, weekIndex: number, row: any) {
  if (key === "all") {
    allBool.value[weekIndex] = !allBool.value[weekIndex];
    let data = [];
    for (let i = 0; i < 24; i++) {
      data.push(i);
    }
    row.workTime = [...new Set(data)];
    if (!allBool.value[weekIndex]) {
      row.workTime = [];
    }
  } else {
    const index = row.workTime.indexOf(key);

    if (index == -1) {
      row.workTime.push(key);
    } else row.workTime.splice(index, 1);
  }
}
//
function AddSla(obj: any) {
  if (slaName.value == "") {
    return ElMessage.error("请输入SLA名称");
  }
  build(obj);
}
//新增SLA
function build(obj: any) {
  Api.AddSlaConfig(obj)
    .then(({ success, data, message }) => {
      if (!success) throw new Error(message);
      ruleId.value = (data as unknown as Record<string, string>).ruleId as string;
      ElMessage.success("操作成功");
      emit("confrim");
      handleCancel();
    })
    .catch((e: any) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {});
}
//编辑服务
function EditSlaConfig(obj: any) {
  if (slaName.value == "") {
    return ElMessage.error("请输入SLA名称");
  }
  edit(obj);
}
function edit(obj: any) {
  Api.EditSlaConfig({
    ruleId: $params.value.ruleId,
    ...obj,
  })
    .then(({ success, data, message }) => {
      if (success) {
        ElMessage.success("操作成功");
        emit("confrim");
        handleCancel();
      } else {
        ElMessage.error(JSON.parse(data)?.message);
      }
    })
    .catch((e) => {
      ElMessage.error(e.message);
    });
}
// //查询客户名称列表
// function getCustomerList() {
//   let params = {
//     ...this.page,
//   };
//   this.tableLoading = true;
//   getCustomerList(params)
//     .then(({ success, data, page, size, total }) => {
//       // console.log(success, data);
//       if (success) {
//         this.customerNameList = data.tenants;
//         this.tableLoading = false;
//         this.page.total = Number(total);
//         this.page.pageNumber = page;
//         this.page.pageSize = size;
//       }
//     })
//     .catch(() => {
//       this.tableLoading = false;
//     });
// }
/**
 * TODO: 窗口方法
 */
type Item = Omit<DataItem, "createdTime" | "updatedTime" | "id" | "status"> & { id: string };

interface Props {
  title: string;
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  labelWidth: 116,
});

const { t } = useI18n();

const width = inject<import("vue").Ref<number>>("width", ref(100));
const height = inject<import("vue").Ref<number>>("height", ref(100));

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<DataItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Partial<Item>) => void;
  reject?: (value: Partial<Item>) => void;
  callback?: (form: Item & { [key: string]: unknown }) => Promise<boolean>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item>>({});

type DefaultForm<T> = { [P in keyof T]: { value: T[P]; test: (v: any) => v is T[P]; transfer: (fv: any, ov: T[P]) => T[P] } };
const defaultForm = readonly<DefaultForm<Required<Item>>>({
  id: { value: "", ...TypeHelper.string },
  itemId: { value: "", ...TypeHelper.string },
  level: { value: "", ...TypeHelper.string },
  effectiveBegin: { value: [], ...TypeHelper.array },
  effectiveEnd: { value: [], ...TypeHelper.array },
  response: { value: [], ...TypeHelper.array },
  resolve: { value: [], ...TypeHelper.array },
  ruleId: { value: "", ...TypeHelper.string },
  ruleName: { value: "", ...TypeHelper.string },
  ruleDesc: { value: "", ...TypeHelper.string },
  ruleType: { value: "", ...TypeHelper.string },
  slaRuleId: { value: "", ...TypeHelper.string },
  degradeId: { value: "", ...TypeHelper.string },
  slaRuleName: { value: "", ...TypeHelper.string },
  tenantId: { value: "", ...TypeHelper.string },
});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  form = cloneDeep(form);

  try {
    // getCustomerList();
    timeZone.value.unshift({ zoneId: "客户默认", displayName: "客户默认" }, { zoneId: "设备默认", displayName: "设备默认" });
    let map = new Map();
    for (let item of timeZone.value) {
      map.set(item.zoneId, item);
    }
    timeZone.value = [...map.values()];
  } catch (error) {
    /*  */
  }

  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], util.value as never)) });
    },
    form as Required<Item>
  );
}
async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => resolve(true));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") {
      const valid = await data.callback($form);
      if (!valid) throw new Error("Error");
    }

    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});
  slaName.value = "";
  slaDesc.value = "";
  coverTimeCfg.value.timeZone = "客户默认";
  coverTimeCfg.value.coverWorkTime.forEach((item) => {
    item.workTime = [];
  });
  //  coverTimeCfg = ref({
  // timeZone: "客户默认",
  // useCustomerTimeZone: false,
  // useDeviceTimeZone: false,
  // coverWorkTime: [
  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    // formRef.value && formRef.value.clearValidate();
    // await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

defineExpose({
  close: handleCancel,
  async open(params: Partial<Item>, callback?: (form: Item) => Promise<boolean>) {
    if (JSON.stringify(params) === "{}") {
      responseTimeList.value = [];

      //事件处理时间等级
      handelTimeList.value = [];
    }
    if (data.visible) {
      return await new Promise((resolve) => {
        ElMessage.warning("先关闭其他弹窗再重试！");
        resolve(params);
      });
    } else {
      $params.value = cloneDeep(params);
      data.visible = true;
      data.loading = true;
      data.submitLoading = true;
      data.callback = callback;
      try {
        return await new Promise((resolve, reject) => {
          data.resolve = resolve;
          data.reject = reject;
          getSlaDetail();
          // if ($params.value.ruleId) {

          // }

          nextTick(async () => {
            await nextTick();
            handleReset();
            data.loading = false;
            data.submitLoading = false;
          });
        });
      } catch (error) {
        return error;
      }
    }
  },
});
</script>

<style scoped lang="scss">
.edit_sla_config {
  :deep(.things) {
    .el-input-number {
      width: 50px !important;

      .elstyle-input {
        width: 50px;

        input {
          padding: 0;
        }
      }
    }
    .el-input-number {
      .el-input__wrapper {
        padding-left: 0px;
        padding-right: 0px;
      }
    }
    .el-input-number__increase {
      display: none;
    }

    .el-input-number__decrease {
      display: none;
    }

    .el-card {
      margin-top: 20px;
    }

    .el-form-item__content .el-form-item-content {
      display: flex;
      flex-direction: column;
    }

    .el-table .cell {
      padding: 0 !important;
    }

    .el-table .el-table__cell {
      padding: 0 !important;
      height: 50px;
    }
  }
}
.priority-icon {
  width: 10px;
  height: 10px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 10px;
}
.state {
  padding: 2px 10px;
  box-sizing: border-box;
  border-radius: 20px;
}
.modules-item {
  .modules-title {
    color: rgba(32, 128, 255, 1);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}
.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}
</style>
