<template>
  <div>
    <el-dialog :title="title" v-model="dialogFormVisible" :before-close="handleClose" width="50vw">
      <el-form :model="form" :rules="rules" ref="ruleForm">
        <el-form-item label="名称:" :label-width="formLabelWidth" prop="ruleName">
          <el-input v-model="form.ruleName" autocomplete="off" placeholder="请输入名称"></el-input>
        </el-form-item>
        <el-form-item label="描述:" :label-width="formLabelWidth" prop="ruleDesc">
          <el-input type="textarea" v-model="form.ruleDesc" autocomplete="off" :rows="2" placeholder="请输入描述"></el-input>
        </el-form-item>
        <!-- <el-form-item label="是否默认:" :label-width="formLabelWidth" prop="defaultSla">
          <el-checkbox v-model="form.defaultSla" @change="changeSla"></el-checkbox>
        </el-form-item> -->
        <el-form-item v-if="type == 'add'" label="选择安全目录:" :label-width="formLabelWidth">
          <treeAuth ref="treeAuthRef" :treeStyle="treeStyle"></treeAuth>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel()">取 消</el-button>
          <el-button type="primary" @click="confirm('ruleForm')" v-loading="btnloading">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent } from "vue";
import { ElMessage, ElMenuItem, ElMessageBox } from "element-plus";

import { AddNewSlaConfig, EditNewSlaConfig, hasSLAcheckDefault } from "@/views/pages/apis/SlaConfig";
import { getAlarmClassificationList } from "@/views/pages/apis/alarmClassification";
import treeAuth from "@/components/treeAuth/index.vue";
import getUserInfo from "@/utils/getUserInfo";

export default defineComponent({
  name: "slaConfigCreate",
  components: {
    treeAuth,
  },
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      form: {
        containerId: "",
        ruleName: "",
        ruleDesc: "",
        ruleId: "",
        // defaultSla: false,
      },
      containerIdS: null,
      btnloading: false,
      rules: {
        ruleName: [{ required: true, message: "请输入SLA名称", trigger: "blur" }],
      },
      title: "",
      checked: false,
      dialogFormVisible: false,
      formLabelWidth: "130px",
      type: "",
      value: "",
      disabled: "",
      treeStyle: {
        width: "300px",
        height: "150px",
      },
      changeSlav: null,
    };
  },
  watch: {
    dialog(val) {
      this.dialogFormVisible = val;
    },
    type(val) {
      if (val === "add") {
        for (var key in this.form) {
          this.form[key] = null;
          // if (key == "defaultSla") {
          //   this.form[key] = false;
          // }
        }
      }
    },
  },
  created() {},
  methods: {
    confirm(formName) {
      if (this.type == "add") {
        this.form.containerId = this.$refs.treeAuthRef.treeItem.id;
      } else {
        this.form.containerId = "";
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.type === "add") {
            if (!this.form.containerId) {
              ElMessage.error("请选择安全目录");
              return;
            }
            // if (this.form.defaultSla == true) {
            //   hasSLAcheckDefault({ tenantId: getUserInfo().currentTenant.id }).then((res) => {
            //     if (res.success == false) {
            //       ElMessageBox.confirm("已有默认SLA,是否修改?修改后,当前SLA将会变成默认SLA,适用于所有客户", "提示", {
            //         confirmButtonText: "确定",
            //         cancelButtonText: "取消",
            //         type: "warning",
            //       })
            //         .then(() => {
            //           this.btnloading = true;
            //           AddNewSlaConfig(this.form)
            //             .then((res) => {
            //               if (res.success) {
            //                 ElMessage.success("新增成功");
            //                 this.$emit("dialogClose", false);
            //                 this.btnloading = false;
            //                 this.$refs[formName].resetFields();
            //                 this.$refs.treeAuthRef.getSafeContaine();
            //                 this.$refs.treeAuthRef.treeId = -1;
            //               } else {
            //                 ElMessage.error(JSON.parse(res.data)?.message);
            //                 this.$emit("dialogClose", false);
            //                 this.btnloading = false;
            //                 this.$refs[formName].resetFields();
            //                 this.$refs.treeAuthRef.getSafeContaine();
            //                 this.$refs.treeAuthRef.treeId = -1;
            //               }
            //             })
            //             .catch((e) => {
            //               this.btnloading = false;
            //               if (e instanceof Error) ElMessage.error(e.message);
            //               this.$refs.treeAuthRef.getSafeContaine();
            //               this.$refs.treeAuthRef.treeId = -1;
            //               this.$refs[formName].resetFields();
            //               // this.$emit("dialogClose", false);
            //             });
            //         })
            //         .catch((e) => {
            //           if (e instanceof Error) ElMessage.error(e.message);
            //         });
            //     } else {
            //       this.btnloading = true;
            //       AddNewSlaConfig(this.form)
            //         .then((res) => {
            //           if (res.success) {
            //             this.btnloading = false;
            //             ElMessage.success("新增成功");
            //             this.$emit("dialogClose", false);
            //             this.$refs[formName].resetFields();
            //             this.$refs.treeAuthRef.getSafeContaine();
            //             this.$refs.treeAuthRef.treeId = -1;
            //           } else {
            //             this.btnloading = false;
            //             ElMessage.error(JSON.parse(res.data)?.message);
            //             this.$emit("dialogClose", false);
            //             this.$refs[formName].resetFields();
            //             this.$refs.treeAuthRef.getSafeContaine();
            //             this.$refs.treeAuthRef.treeId = -1;
            //           }
            //         })
            //         .catch((e) => {
            //           this.btnloading = false;
            //           if (e instanceof Error) ElMessage.error(e.message);
            //           this.$refs.treeAuthRef.getSafeContaine();
            //           this.$refs.treeAuthRef.treeId = -1;
            //           this.$refs[formName].resetFields();
            //           // this.$emit("dialogClose", false);
            //         });
            //     }
            //   });
            // } else {
            this.btnloading = true;
            AddNewSlaConfig(this.form)
              .then((res) => {
                if (res.success) {
                  ElMessage.success("新增成功");
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                  this.$refs.treeAuthRef.getSafeContaine();
                  this.$refs.treeAuthRef.treeId = -1;
                  this.btnloading = false;
                } else {
                  this.btnloading = false;
                  ElMessage.error(JSON.parse(res.data)?.message);
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                  this.$refs.treeAuthRef.getSafeContaine();
                  this.$refs.treeAuthRef.treeId = -1;
                }
              })
              .catch((e) => {
                this.btnloading = false;
                if (e instanceof Error) ElMessage.error(e.message);
                this.$refs.treeAuthRef.getSafeContaine();
                this.$refs.treeAuthRef.treeId = -1;
                this.$refs[formName].resetFields();
                // this.$emit("dialogClose", false);
              });
            // }
          } else {
            // if (this.form.defaultSla == true && this.changeSlav != null) {
            //   hasSLAcheckDefault({ tenantId: getUserInfo().currentTenant.id }).then((res) => {
            //     if (res.success == false) {
            //       ElMessageBox.confirm("已有默认SLA,是否修改?修改后,当前SLA将会变成默认SLA,适用于所有客户", "提示", {
            //         confirmButtonText: "确定",
            //         cancelButtonText: "取消",
            //         type: "warning",
            //       })
            //         .then(() => {
            //           this.btnloading = true;
            //           EditNewSlaConfig(this.form)
            //             .then((res) => {
            //               if (res.success) {
            //                 this.btnloading = false;
            //                 ElMessage.success("修改成功");
            //                 this.$emit("dialogClose", false);
            //                 this.$refs[formName].resetFields();
            //               } else {
            //                 this.btnloading = false;
            //                 ElMessage.error(JSON.parse(res.data)?.message);
            //                 this.$emit("dialogClose", false);
            //               }
            //             })
            //             .catch((e) => {
            //               if (e instanceof Error) ElMessage.error(e.message);
            //               // this.$emit("dialogClose", false);
            //             });
            //         })
            //         .catch((e) => {
            //           if (e instanceof Error) ElMessage.error(e.message);
            //         });
            //     } else {
            //       this.btnloading = true;
            //       EditNewSlaConfig(this.form)
            //         .then((res) => {
            //           if (res.success) {
            //             this.btnloading = false;
            //             ElMessage.success("修改成功");
            //             this.$emit("dialogClose", false);
            //             this.$refs[formName].resetFields();
            //           } else {
            //             this.btnloading = false;
            //             ElMessage.error(JSON.parse(res.data)?.message);
            //             this.$emit("dialogClose", false);
            //           }
            //         })
            //         .catch((e) => {
            //           this.btnloading = false;
            //           if (e instanceof Error) ElMessage.error(e.message);
            //           // this.$emit("dialogClose", false);
            //         });
            //     }
            //   });
            // } else {
            this.btnloading = true;
            EditNewSlaConfig(this.form)
              .then((res) => {
                if (res.success) {
                  this.btnloading = false;
                  ElMessage.success("修改成功");
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                } else {
                  this.btnloading = false;
                  ElMessage.error(JSON.parse(res.data)?.message);
                  this.$emit("dialogClose", false);
                }
              })
              .catch((e) => {
                this.btnloading = false;
                if (e instanceof Error) ElMessage.error(e.message);
                // this.$emit("dialogClose", false);
              });
            // }
          }
          // this.dialogFormVisible = false;
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      if (this.type == "add") {
        this.$refs.treeAuthRef.getSafeContaine();
        this.$refs.treeAuthRef.treeId = -1;
      }
    },
    cancel() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      if (this.type == "add") {
        this.$refs.treeAuthRef.getSafeContaine();
        this.$refs.treeAuthRef.treeId = -1;
      }
    },
    blurChange(data) {
      this.tags.push(data);
    },
    tagClose(index) {
      this.tags.splice(index, 1);
    },
    changeSla(event) {
      this.changeSlav = event;
    },
  },
  expose: ["type", "disabled", "title", "form", "value"],
});
</script>
