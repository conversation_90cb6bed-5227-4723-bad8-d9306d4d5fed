<template>
  <div class="SubruleAdd">
    <el-dialog :title="addType == 'area' ? $t('alarmMerge.AssignRegion') : addType == 'device' ? $t('alarmMerge.AssignDevice') : $t('alarmMerge.AssignVenue')" v-model="dialogVisible" append-to-body draggable width="35%" :before-close="handleClose">
      <el-form :model="{}" label-width="140" label-suffix="：">
        <el-form-item style="width: 100%" :label="title" class="is-required">
          <el-select class="tw-w-full" v-if="addType != 'area'" v-model="value" multiple filterable :placeholder="$t('alarmMerge.PleaseSelect') + title">
            <el-option v-for="item in checked?allOptions:options" :key="item.id" :label="item.name" :value="item.id" :disabled="item.disabled"> </el-option>
          </el-select>
          <el-cascader class="tw-w-full" v-model="areaList" v-if="addType == 'area'" filterable :options="checked?allOptions:options" :props="{ checkStrictly: true, value: 'id', label: 'name', disabled: 'disabled' }" clearable></el-cascader>
          <el-row style="width: 100%" >
            <el-col :offset="0">
              <el-checkbox @change="changeCheck" v-model="checked">{{ addType == 'area' ? $t('alarmMerge.ShowAllRegion') : addType == 'device' ? $t('alarmMerge.ShowAllDevice') : $t('alarmMerge.ShowAllVenue') }}</el-checkbox>
            </el-col>
          </el-row>  
        </el-form-item> 
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancel">{{ $t("glob.Cancel") }}</el-button>
          <el-button type="primary" @click="confirm">{{ $t("glob.Confirm") }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { useI18n } from "vue-i18n";
export default {
  name: "SlaDownDialog",
  props: {
    addType: {
      type: String,
      default: "",
    },
    options: {
      type: Array,
    },
    allOptions: {
      type: Array,
    },
  },
  data() {
    return {
      title: "",
      dialogVisible: false,
      checked: false,
      value: [],
      areaList: [],
      i18n: useI18n()
    };
  },
  watch: {
    addType(val) {
      val == "area" ? (this.title = this.i18n.t("alarmMerge.RegionName")) : val == "device" ? (this.title = this.i18n.t("alarmMerge.DeviceName")) : (this.title = this.i18n.t("alarmMerge.VenueName"));
      this.checked=false
    },
    options(val) {
      // // console.log(val);
    },
  },
  mounted() {
    // // console.log(this.$props.addType);
  },
  methods: {
    changeCheck(val){
      this.checked=val
    },
    cancel() {
      this.dialogVisible = false;
    },
    confirm() {
      // // console.log(instanceof this.value);
      if (this.addType === "area") {
        if (this.areaList.length > 0) {
          // console.log(this.areaList[this.areaList.length - 1]);
          this.$emit("confirmMsg", { value: [this.areaList[this.areaList.length - 1]], type: this.addType });
        } else {
          this.$message.error(this.i18n.t("alarmMerge.PleaseSelect") + this.title);
        }
      } else {
        if (this.value) {
          this.$emit("confirmMsg", { value: this.value, type: this.addType });
        } else {
          this.$message.error(this.i18n.t("alarmMerge.PleaseSelect") + this.title);
        }
      }
    },
    handleClose(done) {
      done();
      this.dialogVisible = false;
    },
  },
  expose: ["confirm", "cancel", "dialogVisible", "addType", "options", "title", "value", "areaList"],
};
</script>
