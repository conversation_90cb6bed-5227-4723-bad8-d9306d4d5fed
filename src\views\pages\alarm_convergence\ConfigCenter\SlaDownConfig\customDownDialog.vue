<template>
  <div class="SubruleAdd">
    <el-dialog title="分配客户" v-model="dialogVisible" append-to-body draggable width="35%" :before-close="handleClose">
      <el-form>
        <el-form-item style="width: 100%">
          <el-row style="width: 100%">
            <el-col style="text-align: right" class="bold" :span="4">
              <span style="color: red">*</span>
              {{ "类型" + "：" }}
            </el-col>
            <el-col :span="14">
              <el-select disabled v-model="customtypevalue" :placeholder="'请选择' + title">
                <el-option v-for="item in customtype" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled"> </el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item style="width: 100%">
          <el-row style="width: 100%">
            <el-col style="text-align: right" class="bold" :span="4">
              <span style="color: red">*</span>
              {{ "客户" + "：" }}
            </el-col>
            <el-col :span="14">
              <el-select v-model="customvalue" multiple :placeholder="'请选择' + title" filterable default-first-option :filter-method="filterOptions">
                <el-option v-for="item in filteredOptions" :key="item.id" :label="item.name" :value="item.id">
                  <span>{{ item.name }} [{{ item.abbreviation }}]</span>
                </el-option>
              </el-select>
            </el-col>
          </el-row>
          <el-row style="width: 100%">
            <el-col :offset="4">
              <el-checkbox @change="changeCheck" v-model="checked">展示所有客户</el-checkbox>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import getUserInfo from "@/utils/getUserInfo";

export default {
  name: "SlaDownDialog",
  props: {
    addType: {
      type: String,
      default: "",
    },
    options: {
      type: Array,
    },
    allOptions: {
      type: Array,
    },
  },
  data() {
    return {
      title: "",
      dialogVisible: false,
      value: [],
      customvalue: [],
      customtypevalue: "1",
      customtype: [
        {
          label: "客户",
          value: "1",
          disabled: true,
        },
      ],
      userInfo: getUserInfo(),
      checked: false,
      searchQuery: "", // 用于存储查询条件
    };
  },
  computed: {
    filteredOptions() {
      const sourceOptions = this.checked ? this.allOptions : this.options;
      const lowerQuery = this.searchQuery.toLocaleLowerCase();

      // 根据搜索条件过滤 `options`
      return sourceOptions.filter((item) => item.name.toLocaleLowerCase().includes(lowerQuery) || item.abbreviation.toLocaleLowerCase().includes(lowerQuery));
    },
  },
  watch: {},
  mounted() {
    // // console.log(this.$props.addType);
  },
  methods: {
    changeCheck(val) {
      this.checked = val;
    },
    cancel() {
      this.dialogVisible = false;
    },
    confirm() {
      if (this.customvalue) {
        this.$emit("customMsg", { value: this.customvalue });
      } else {
        this.$message.error("请选择");
      }
    },
    handleClose(done) {
      done();
      this.dialogVisible = false;
    },
    filterOptions(query) {
      // 更新搜索条件
      this.searchQuery = query;
    },
  },
  expose: ["confirm", "cancel", "dialogVisible", "title", "value", "customvalue"],
};
</script>
