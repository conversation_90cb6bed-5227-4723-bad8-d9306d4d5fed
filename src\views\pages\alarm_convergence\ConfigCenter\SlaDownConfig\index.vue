<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="getSlaDownList()" @current-change="getSlaDownList()">
      <!-- <template #left>
        <el-input v-model="ServiceSearch" clearable style="width: 220px" placeholder="请输入告警降级配置名称" @keyup.enter="getSlaList()">
          <template #append>
            <el-button :icon="Search" @click="getSlaList()" />
          </template>
        </el-input>
      </template> -->
      <template #right>
        <span class="tw-h-fit">
          <el-button v-if="userInfo.hasPermission(PERMISSION.group515413922812002304.create)" type="primary" :icon="Plus" @click="handleCreate()">{{ $t("glob.add") }}告警降级配置</el-button>
        </span>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table stripe :data="tableData" :height="tableHeight" style="width: 100%">
          <el-table-column type="index" align="left" prop="date" label="序号" :width="56"> </el-table-column>
          <!-- <el-table-column  align="left" prop="degradeName" label="告警降级配置名称" :formatter="formatterTable" width="180"> </el-table-column> -->
          <TableColumn type="default" show-filter v-model:filtered-value="ServiceSearch" @filter-change="getSlaDownList()" prop="degradeName" label="告警降级配置名称" :width="160"> </TableColumn>
          <!-- <el-table-column  align="left" prop="ruleDesc" label="SLA描述" :formatter="formatterTable"> </el-table-column> -->
          <TableColumn type="enum" show-filter v-model:filtered-value="degradeStatus" @filter-change="getSlaDownList()" prop="degradeStatus" label="使用状态" :width="100" :filters="slaDownStatus.map((v) => ({ ...v, text: v.label }))">
            <template #default="{ row }">
              <el-tag class="ml-2" :type="row.degradeStatus ? 'success' : 'danger'">
                {{ row.degradeStatus ? "启用" : "禁用" }}
              </el-tag>
            </template>
          </TableColumn>
          <el-table-column align="left" prop="degradeDesc" label="告警降级配置描述" :formatter="formatterTable"> </el-table-column>
          <el-table-column align="left" label="是否默认" :width="90">
            <template #default="{ row }">
              <span class="tw-h-fit tw-align-middle">
                <el-switch v-model="row.defaultRule" :disabled="!userInfo.hasPermission(PERMISSION.group515413922812002304.editor)" @change="statusChange(row)" active-color="#13ce66" inactive-color="#D3D3D3"></el-switch>
              </span>
            </template>
          </el-table-column>
          <!-- <el-table-column align="left" prop="degradeStatus" label="使用状态" :width="80" :formatter="(_row, _col, v) => h(ElText, { type: v ? 'success' : 'danger' }, () => (v ? $t('glob.Enable') : $t('glob.Disable')))"></el-table-column> -->
          <el-table-column align="left" prop="degradeCreateTime" label="创建时间" :formatter="(_row, _col, v) => (v ? moment(v, 'x').format('yyyy-MM-DD HH:mm:ss') : '--')"></el-table-column>
          <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="126">
            <template #default="{ row }">
              <span class="tw-h-fit tw-align-middle" v-show="!row.globalEnable">
                <el-link v-if="userInfo.hasPermission(PERMISSION.group515413922812002304.editor)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleSlaDownEdit(row as DataItem)">{{ $t("glob.edit") }}</el-link>
              </span>
              <span class="tw-h-fit tw-align-middle">
                <el-link v-if="userInfo.hasPermission(PERMISSION.group515413922812002304.editor)" :type="row.degradeStatus ? 'danger' : 'primary'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="SlaConfigDisable(row as DataItem)">{{ row.degradeStatus ? $t("glob.Disable") : $t("glob.Enable") }}</el-link>
              </span>
              <span class="tw-h-fit tw-align-middle" v-show="row.globalEnable">
                <el-link type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleSlaDownDefaultView(row as DataItem)">详情</el-link>
              </span>
              <span v-if="!row.degradeStatus" v-show="!row.globalEnable" class="tw-h-fit tw-align-middle">
                <el-link v-if="userInfo.hasPermission(PERMISSION.group515413922812002304.remove)" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="SlaDownConfigDelete(row as DataItem)">{{ $t("glob.delete") }}</el-link>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
  <Editor ref="editorRef" title="SLA降级服务" @confirm="confirm"></Editor>
  <messgaeView ref="messgaeViewRef" title="SLA" @confrim="getSlaList"></messgaeView>
</template>

<script setup lang="ts" generic="T extends object">
/* eslint-disable @typescript-eslint/no-unused-vars */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMessageBox, ElText } from "element-plus";
import pageTemplate from "@/components/pageTemplate.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import Editor from "./Editor.vue";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";
import { slaDownStatus } from "./common";
import { AddSlaDownConfig, SlaDownConfigEdit } from "@/views/pages/apis/SlaConfig";

import { getSlaDownConfigByPage, DelSlaDownConfig, EnableSlaDownConfig, DisableSlaDownConfig, SlaDownConfigStatus, type SlaConfigList as DataItem } from "@/views/pages/apis/SlaConfig";
import messgaeView from "./messageView.vue";
import { faBullseye } from "@fortawesome/free-solid-svg-icons";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "SlaDownConfig" });
const editorRef = ref<InstanceType<typeof Editor>>();
const messgaeViewRef = ref<InstanceType<typeof messgaeView>>();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const siteConfig = useSiteConfig();
const userInfo = getUserInfo();

// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
// const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);

const tableLoading = ref(false);

// 搜索关键字
const ServiceSearch = ref("");
const degradeStatus = ref(null);
const tableData = ref<DataItem[]>([]);
const paging = reactive({
  pageNumber: 1,
  pageSize: 50,
  total: 0,
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  getSlaList();
}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
//搜索
function getSlaList() {
  paging.pageNumber = 1;

  getSlaDownList();
}
function formatterTable(_row, _col, v) {
  switch (_col.property) {
    default:
      return v || "--";
  }
}
//默认值更改
function statusChange(row: Partial<Record<string, any>>) {
  // console.log(row);
  // tableData.value.forEach((v, i) => {
  //   if (v.id === row.id) {
  //     row.defaultRule = true;
  //   } else {
  //     row.defaultRule = false;
  //   }
  // });
  SlaDownConfigStatus({
    id: row.id,
    defaultable: row.defaultRule,
  })
    .then((res) => {
      // console.log(res);
      if (res.success) {
        ElMessage.success("默认状态修改成功");
        getSlaDownList();
      }
    })
    .catch((e) => {
      ElMessage.error(e.message);
    });
}
//启禁用服务

function SlaConfigDisable(row: Partial<Record<string, any>>) {
  let currState = row.degradeStatus;
  const apiName = row.degradeStatus ? "DisableServiceCenter" : "EnableServiceCenter";
  let params = {
    id: row.id,
    enable: !currState,
  };
  if (apiName === "DisableServiceCenter")
    ElMessageBox.confirm(`确定禁用${row.degradeName}吗？禁用后，该服务级别协议将不再生效，请谨慎操作`, {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        DisableSlaDownConfig(params)
          .then(({ success, data, message }) => {
            if (!success) throw new Error(message);
            ElMessage.success("操作成功");
            tableLoading.value = false;
            getSlaList();
          })
          .catch((e) => {
            if (e instanceof Error) ElMessage.error(e.message);
          })
          .finally(() => {
            tableLoading.value = false;
          });
      })
      .catch(() => {
        // ElMessage({
        //   type: "info",
        //   message: "已取消禁用",
        // });
      });
  else
    EnableSlaDownConfig(params)
      .then(({ success, data, message }) => {
        // console.log(success, data);
        if (!success) throw new Error(message);
        ElMessage.success("操作成功");
        tableLoading.value = false;
        getSlaList();
      })
      .catch((e) => {
        ElMessage.error(e.message);
        tableLoading.value = false;
      });
  // (currState ? DisableSlaDownConfig : EnableSlaDownConfig)(params)
  //   .then(({ success, data, message }) => {
  //     // console.log(success, data);
  //     if (success) {
  //       ElMessage.success(`${apiName === "DisableServiceCenter" ? "禁用" : "启用"}成功`);
  //       tableLoading.value = false;
  //       getSlaDownList();
  //     } else ElMessage(message || ``);
  //   })
  //   .catch((e) => {
  //     ElMessage.error(e.message);
  //     tableLoading.value = false;
  //   });
}
function getSlaDownList() {
  let params = {
    ...paging,
    degradeName: ServiceSearch.value,
    degradeStatus: degradeStatus.value,
  };
  tableLoading.value = true;
  getSlaDownConfigByPage(params)
    .then(({ success, data, page, size, total }) => {
      if (success) {
        let arr = [];
        data.forEach((v, i) => {
          if (v.defaultRule == 1 || v.defaultRule == null) {
            arr.push({ ...v, defaultRule: true });
          } else {
            arr.push({ ...v, defaultRule: false });
          }
        });
        //  let arr = [...data];
        let newArr = [];
        // // console.log()

        if (degradeStatus.value == null || degradeStatus.value === "") {
          tableData.value = arr;
        } else {
          // console.log(degradeStatus.value);
          if (degradeStatus.value) {
            arr.forEach((v, i) => {
              if (v.degradeStatus) {
                newArr.push(v);
              }
            });
            tableData.value = newArr;
          }
          if (degradeStatus.value == false) {
            arr.forEach((v, i) => {
              // console.log(v);
              if (v.degradeStatus == degradeStatus.value) {
                newArr.push(v);
              }
            });
            tableData.value = newArr;
          }
        }
        // // console.log(arr, 55555);

        tableLoading.value = false;
        paging.total = Number(total);
        paging.pageNumber = page;
        paging.pageSize = size;
      }
    })
    .catch(() => {
      tableLoading.value = false;
    });
}
async function handleCreate() {
  try {
    if (!editorRef.value) return;
    await editorRef.value.open({}, async (form) => {
      return true;
    });
  } catch (error) {
    /*  */
  } finally {
    /*  */
  }
  // router.push("/SlaDownConfig/create");
}
async function handleSlaDownEdit(row: Partial<Record<string, any>>) {
  try {
    if (!editorRef.value) return;
    await editorRef.value.open(row, async (form) => {
      return true;
    });
  } catch (error) {
    /*  */
  } finally {
    /*  */
  }
  // router.push(`/SlaDownConfig/edit/${row.id}`);
}
async function handleSlaDownDefaultView(row: Partial<Record<string, any>>) {
  try {
    if (!messgaeViewRef.value) return;
    await messgaeViewRef.value.open(row, async (form) => {
      return true;
    });
  } catch (error) {
    /*  */
  } finally {
    /*  */
  }
  // router.push(`/SlaDownConfig/edit/${row.id}`);
}

function confirm(val: any) {
  // console.log(val);
  if (val.type == "add") {
    AddSlaDownConfig({ ...val.data, defaultRule: false }).then((res: any) => {
      if (res.success) {
        ElMessage.success("操作成功");
        getSlaDownList();
      } else {
        ElMessage.error(JSON.parse(res.data)?.message);
      }
    });
  } else {
    SlaDownConfigEdit({ ...val.data, defaultRule: false }).then((res: any) => {
      if (res.success) {
        ElMessage.success("操作成功");
        getSlaDownList();
      } else {
        ElMessage.error(JSON.parse(res.data)?.message);
      }
    });
  }
}
//删除降级策略
function SlaDownConfigDelete(row: Partial<Record<string, any>>) {
  if (row.status) {
    ElMessage.warning("启用状态下无法删除，请更改数据状态");
  } else
    ElMessageBox.confirm("此操作将永久删除该策略, 是否继续?", "删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        // // tableData.value.length = tableData.value.length - 1;
        let params = {
          id: row.id,
        };
        DelSlaDownConfig(params)
          .then(({ success, data, message }) => {
            if (!success) throw new Error(message);
            ElMessage.success("操作成功");

            if (tableData.value.slice((paging.pageNumber - 1) * paging.pageSize, paging.pageNumber * paging.pageSize).length == 0) {
              paging.pageNumber = 1;
            }
            getSlaDownList();
          })
          .catch((e) => {
            ElMessage.error(e.message);
            tableLoading.value = false;
          });
      })
      .catch(() => {
        // ElMessage({
        //   type: "info",
        //   message: "已取消删除",
        // });
      });
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
