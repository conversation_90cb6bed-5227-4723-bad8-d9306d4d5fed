<template>
  <!-- 对话框表单 -->
  <el-drawer class="el-card tw-bottom-[16px] tw-right-[16px] tw-top-[120px]" :size="'calc(100% - 308px)'" :style="{ height: `calc(100% - 135px)` }" v-model="visible" :close-on-click-modal="false" :show-close="false" :modal="false" :before-close="handleCancel">
    <template #header>
      <div class="tw-flex tw-h-[30px] tw-flex-nowrap tw-items-center">
        <el-page-header class="tw-mr-auto" :content="`${title}详情`" @back="handleCancel()"></el-page-header>
      </div>
    </template>
    <template #default>
      <el-scrollbar>
        <div class="alarm-down-config">
          <el-form :model="form" :style="{ marginTop: '10px' }" label-position="left" :rules="rules" ref="roleForm" class="alarm-table" disabled>
            <el-card class="el-card-mt">
              <!-- <div slot="header" class="modules-item">
          <span class="modules-title">规则设置</span>
        </div> -->
              <el-row style="width: 100%">
                <el-col :span="12">
                  <el-form-item label="告警降级服务名称：" prop="degradeName">
                    <el-input style="width: 400px" v-model="form.degradeName" placeholder="请输入告警降级服务名称" :style="basicClassInput" disabled />
                  </el-form-item>
                </el-col>
                <el-col :span="12" style="text-align: right">
                  <el-form-item label="告警降级服务描述：">
                    <div class="el-form-item-content">
                      <el-input style="width: 400px" type="textarea" v-model="form.degradeDesc" placeholder="请输入告警降级服务描述" :style="basicClassInput" disabled />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <!-- <template slot-scope="props"> -->

                <el-form-item style="width: 100%">
                  <el-row style="width: 100%">
                    <el-col class="bold" :span="12" style="text-align: left">
                      <el-form-item label="工作时间："> </el-form-item>
                    </el-col>
                    <el-col class="bold" :span="12" style="text-align: right">
                      <el-form-item label="选择时区：">
                        <el-select v-model="effectTimeCfg.timeZone" filterable placeholder="请选择" :style="basicClassInputDown" style="width: 400px" disabled>
                          <el-option v-for="item in timeZone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"> </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <!-- <el-col :span="10" style="text-align: right">

              </el-col> -->
                  </el-row>
                </el-form-item>

                <el-form-item style="width: 100%">
                  <el-row style="width: 100%">
                    <el-col>
                      <div style="width: 100%" class="support-table-content" ref="tableContentRef">
                        <el-table stripe border :data="effectTimeCfg.coverWorkTime" style="width: 100%; margin-top: 0px">
                          <el-table-column align="left" prop="week" disabled width="80">
                            <template #default="scope">
                              <div class="week" style="width: 100%" disabled>
                                {{ scope.row.weekDay == 1 ? "周一" : scope.row.weekDay == 2 ? "周二" : scope.row.weekDay == 3 ? "周三" : scope.row.weekDay == 4 ? "周四" : scope.row.weekDay == 5 ? "周五" : scope.row.weekDay == 6 ? "周六" : "周日" }}
                              </div>
                            </template>
                          </el-table-column>
                          <el-table-column align="left" :width="tableWidth" v-for="(item, key) in 24" :key="`h-${key}`" :label="String(key)" disabled>
                            <template #default="scope">
                              <div style="width: 100%; height: 100%" :class="scope.row.workTime && scope.row.workTime.length && scope.row.workTime.includes(key) ? 'sun' : 'moon'" disabled>
                                <!-- <span>{{ (scope.row.workTime[key], scope.row.workTime.includes(key)) }}</span> -->
                                <!-- <el-button type="text" style="font-size: 30px"> -->
                                <el-icon v-if="scope.row.workTime && scope.row.workTime.length && scope.row.workTime.includes(key)" class="tw-text-white"><Sunny></Sunny></el-icon>
                                <el-icon v-else class="tw-text-black"><Moon></Moon></el-icon>
                                <!-- </el-button> -->
                              </div>
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </el-col>
                  </el-row>
                </el-form-item>
                <el-form-item style="width: 100%">
                  <el-row style="width: 100%">
                    <el-col class="bold" :span="2" style="text-align: right">降级配置：</el-col>
                  </el-row>
                  <el-row :gutter="20" style="width: 100%">
                    <el-col :span="8">
                      <el-card>
                        <div><span>工作日降级策略配置</span></div>
                        <el-row>
                          <el-col>工作时间内<el-button type="text" icon="el-icon-sunny"></el-button></el-col>
                        </el-row>

                        <el-row style="display: flex">
                          <el-col :span="10"> 选择优先级降级数：</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="workingHours.degradeInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC" disabled> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="workingHours.degradeInWorkTime" style="width: 100px">
                              <el-option v-for="item in downNumberOptions" :disabled="!workingHours.degradeInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="10"> 禁止优先级低于：</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="workingHours.forbidInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="workingHours.forbidInWorkTime" style="width: 100px">
                              <el-option v-for="item in downLevelOptions" :disabled="!workingHours.forbidInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col>非工作时间内<el-button type="text" icon="el-icon-moon"></el-button></el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="10"> 选择优先级降级数：</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="workingHours.degradeOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="workingHours.degradeOutWorkTime" style="width: 100px">
                              <el-option v-for="item in downNumberOptions" :disabled="!workingHours.degradeOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="10"> 禁止优先级低于：</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="workingHours.forbidOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="workingHours.forbidOutWorkTime" style="width: 100px">
                              <el-option v-for="item in downLevelOptions" :disabled="!workingHours.forbidOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row>
                          <span>*优先级最小只能降到P7</span>
                        </el-row>
                      </el-card>
                    </el-col>
                    <el-col :span="8">
                      <el-card>
                        <div><span>周六降级配置</span></div>
                        <el-row>
                          <el-col>工作时间内<el-button type="text" icon="el-icon-sunny"></el-button></el-col>
                        </el-row>

                        <el-row style="display: flex">
                          <el-col :span="10"> 选择优先级降级数：</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="saturdayHours.degradeInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="saturdayHours.degradeInWorkTime" style="width: 100px">
                              <el-option v-for="item in downNumberOptions" :disabled="!saturdayHours.degradeInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="10"> 禁止优先级低于：</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="saturdayHours.forbidInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="saturdayHours.forbidInWorkTime" style="width: 100px">
                              <el-option v-for="item in downLevelOptions" :disabled="!saturdayHours.forbidInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col>非工作时间内<el-button type="text" icon="el-icon-moon"></el-button></el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="10"> 选择优先级降级数：</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="saturdayHours.degradeOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="saturdayHours.degradeOutWorkTime" style="width: 100px">
                              <el-option v-for="item in downNumberOptions" :disabled="!saturdayHours.degradeOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="10"> 禁止优先级低于：</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="saturdayHours.forbidOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="saturdayHours.forbidOutWorkTime" style="width: 100px">
                              <el-option v-for="item in downLevelOptions" :disabled="!saturdayHours.forbidOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row>
                          <span>*优先级最小只能降到P7</span>
                        </el-row>
                      </el-card>
                    </el-col>
                    <el-col :span="8">
                      <el-card>
                        <div><span>周日降级配置</span></div>
                        <el-row>
                          <el-col>工作时间内<el-button type="text" icon="el-icon-sunny"></el-button></el-col>
                        </el-row>

                        <el-row style="display: flex">
                          <el-col :span="10"> 选择优先级降级数：</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="sundayHours.degradeInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="sundayHours.degradeInWorkTime" style="width: 100px">
                              <el-option v-for="item in downNumberOptions" :disabled="!sundayHours.degradeInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="10"> 禁止优先级低于：</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="sundayHours.forbidInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="sundayHours.forbidInWorkTime" style="width: 100px">
                              <el-option v-for="item in downLevelOptions" :disabled="!sundayHours.forbidInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col>非工作时间内<el-button type="text" icon="el-icon-moon"></el-button></el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="10"> 选择优先级降级数：</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="sundayHours.degradeOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="sundayHours.degradeOutWorkTime" style="width: 100px">
                              <el-option v-for="item in downNumberOptions" :disabled="!sundayHours.degradeOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="10"> 禁止优先级低于：</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="sundayHours.forbidOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="sundayHours.forbidOutWorkTime" style="width: 100px">
                              <el-option v-for="item in downLevelOptions" :disabled="!sundayHours.forbidOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row>
                          <span>*优先级最小只能降到P7</span>
                        </el-row>
                      </el-card>
                    </el-col>
                    <!-- <el-col :style="{ textAlign: 'center', marginTop: '10px' }">
                <el-button type="primary" @click="CreateSlaDownConfig">保存</el-button>
              </el-col> -->
                  </el-row>
                </el-form-item>
              </el-row>
            </el-card>
            <el-card class="el-card-mt">
              <el-row style="width: 100%">
                <el-col :span="24" style="text-align: right">
                  <el-button type="primary" @click="addModule('area')">
                    <el-icon class="el-icon--left"><Plus /></el-icon>新增区域</el-button
                  >
                </el-col>
                <el-col>
                  <el-table stripe :data="areaList" style="width: 100%; margin-top: 30px">
                    <el-table-column align="left" label="名称" prop="name"> </el-table-column>
                    <el-table-column align="left" label="描述" prop="description"> </el-table-column>

                    <el-table-column align="left" label="操作">
                      <template #default="scope">
                        <!-- <el-button type="text" v-show="!scope.row.state" @click="confirmLevel('response', scope.$index, scope.row)">保存</el-button> -->

                        <el-popconfirm :title="delTitle" @confirm="delConfirm('area', scope.$index, scope.row)">
                          <template #reference>
                            <el-button type="text" textColor="danger" @click="delLevel('area', scope.$index, scope.row)">删除</el-button>
                          </template>
                        </el-popconfirm>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
                <el-col :span="24" style="margin-top: 20px; text-align: right">
                  <el-button type="primary" @click="addModule('location')">
                    <el-icon class="el-icon--left"><Plus /></el-icon>新增场所</el-button
                  >
                </el-col>
                <el-col>
                  <el-table stripe :data="locationList" style="width: 100%; margin-top: 30px">
                    <el-table-column align="left" label="名称" prop="name"> </el-table-column>
                    <el-table-column align="left" label="描述" prop="description"> </el-table-column>
                    <el-table-column align="left" label="操作">
                      <template #default="scope">
                        <!-- <el-button type="text" v-show="!scope.row.state" @click="confirmLevel('response', scope.$index, scope.row)">保存</el-button> -->

                        <el-popconfirm :title="delTitle" @confirm="delConfirm('location', scope.$index, scope.row)">
                          <template #reference>
                            <el-button type="text" textColor="danger" @click="delLevel('location', scope.$index, scope.row)">删除</el-button>
                          </template>
                        </el-popconfirm>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
                <el-col :span="24" style="margin-top: 20px; text-align: right">
                  <el-button type="primary" @click="addModule('device')">
                    <el-icon class="el-icon--left"><Plus /></el-icon>
                    新增设备</el-button
                  >
                </el-col>
                <el-col>
                  <el-table stripe :data="deviceList" style="width: 100%; margin-top: 30px">
                    <el-table-column align="left" label="名称" prop="name"> </el-table-column>
                    <el-table-column align="left" label="描述" prop="description"> </el-table-column>
                    <el-table-column align="left" label="操作">
                      <template #default="scope">
                        <!-- <el-button type="text" v-show="!scope.row.state" @click="confirmLevel('response', scope.$index, scope.row)">保存</el-button> -->

                        <el-popconfirm :title="delTitle" @confirm="delConfirm('device', scope.$index, scope.row)">
                          <template #reference>
                            <el-button type="text" textColor="danger" @click="delLevel('device', scope.$index, scope.row)">删除</el-button>
                          </template>
                        </el-popconfirm>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
              <el-col :style="{ textAlign: 'center', marginTop: '10px', paddingBottom: '10px' }">
                <!-- <el-button type="primary" @click="CreateSlaDownConfig">保 存</el-button> -->
              </el-col>
            </el-card>
            <alarmDownDialog ref="DownSlaConfig" :addType="type" :options="options" @confirmMsg="confirmMsg"></alarmDownDialog>
          </el-form>
        </div>
      </el-scrollbar>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <!-- <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button> -->
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </el-drawer>
  <!-- <SlaDownDialog ref="DownSlaConfig" :addType="type" :options="options" @confirmMsg="confirmMsg"></SlaDownDialog> -->
</template>

<!--  generic="Item extends Record<'id', string> & Record<string, unknown>" -->
<script lang="ts" name="EditorForm">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, inject, h } from "vue";
const width = inject<import("vue").Ref<number>>("width", ref(200));
const height = inject<import("vue").Ref<number>>("height", ref(100));
import { getLocationsTenantCurrent } from "@/views/pages/apis/locationManang";
import { getRegionsTenantCurrent } from "@/views/pages/apis/regionManage";
import { getDeviceList } from "@/views/pages/apis/deviceManage";
import alarmDownDialog from "./alarmDownDialog.vue";
import timeZone from "@/views/pages/common/zone.json";
import mixin from "@/views/pages/alarm_convergence/PropertyManage/regionManage/js/mixin";
import { Sunny, Moon, Plus } from "@element-plus/icons-vue";
import { SlaDownConfigDetaile, getDegradeDefault } from "@/views/pages/apis/SlaConfig";

export default {
  name: "EventCenterIntelNoiseReductCreate",
  components: {
    Plus,
    alarmDownDialog,
    Sunny,
    Moon,
  },
  mixins: [mixin],
  props: {
    downForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      tableWidth: 0,
      width: width,
      height: height,
      title: "告警降级配置",
      activeNames: [""],
      priorityDown: true,
      DefaultTime: "", //选择时区
      degradeName: "", //降级策略名称
      degradeDesc: "", //降级策略类型
      SlaDialogVisible: false,
      visible: false,
      form: {
        degradeName: "",
        degradeDesc: "",
      },
      id: "",
      rules: {
        degradeName: [{ required: true, message: "请输入SLA降级服务名称", trigger: "blur" }],
      },
      timeZone: [...timeZone],
      effectTimeCfg: {
        timeZone: "客户默认",
        useCustomerTimeZone: false,
        useDeviceTimeZone: false,
        coverWorkTime: [
          {
            week: "周一",
            workTime: [],
            weekDay: 1,
          },
          {
            week: "周二",
            workTime: [],
            weekDay: 2,
          },
          {
            week: "周三",
            workTime: [],
            weekDay: 3,
          },
          {
            week: "周四",
            workTime: [],
            weekDay: 4,
          },
          {
            week: "周五",
            workTime: [],
            weekDay: 5,
          },
          {
            week: "周六",
            workTime: [],
            weekDay: 6,
          },
          {
            week: "周日",
            workTime: [],
            weekDay: 7,
          },
        ],
        degradeConfigs: [],
      },
      TimeAddress: [],
      basicClassInput: { width: "25.8vw" } /* 输入框选择器基本样式 */,
      basicClassInputDown: { width: "20.8vw" } /* 输入框选择器基本样式 */,
      describeList: [],

      type: "",
      //工作时间降级
      downNumberOptions: [
        {
          label: 1,
          value: 1,
        },
        {
          label: 2,
          value: 2,
        },
        {
          label: 3,
          value: 3,
        },
        {
          label: 4,
          value: 4,
        },
        {
          label: 5,
          value: 5,
        },
        {
          label: 6,
          value: 6,
        },
      ],
      downLevelOptions: [
        {
          label: "P1",
          value: "1",
        },
        {
          label: "P2",
          value: "2",
        },
        {
          label: "P3",
          value: "3",
        },
        {
          label: "P4",
          value: "4",
        },
        {
          label: "P5",
          value: "5",
        },
        {
          label: "P6",
          value: "6",
        },
      ],
      workingHours: {
        degradeInWorkTime: "", //工作时间降级数
        degradeInWorkTimeEnable: false, //工作时间降级数是否生效
        forbidInWorkTime: "", //工作时间禁止下降的优先级
        forbidInWorkTimeEnable: false, //工作时间优先级是否生效
        degradeOutWorkTime: "", //非工作时间降级数
        degradeOutWorkTimeEnable: false, //非工作时间降级数是否生效
        forbidOutWorkTime: "", //非工作时间禁止下降的优先级
        forbidOutWorkTimeEnable: false, //非工作时间优先级是否生效
      },
      saturdayHours: {
        degradeInWorkTime: "", //工作时间降级数
        degradeInWorkTimeEnable: false, //工作时间降级数是否生效
        forbidInWorkTime: "", //工作时间禁止下降的优先级
        forbidInWorkTimeEnable: false, //工作时间优先级是否生效
        degradeOutWorkTime: "", //非工作时间降级数
        degradeOutWorkTimeEnable: false, //非工作时间降级数是否生效
        forbidOutWorkTime: "", //非工作时间禁止下降的优先级
        forbidOutWorkTimeEnable: false, //非工作时间优先级是否生效
      },
      sundayHours: {
        degradeInWorkTime: "", //工作时间降级数
        degradeInWorkTimeEnable: false, //工作时间降级数是否生效
        forbidInWorkTime: "", //工作时间禁止下降的优先级
        forbidInWorkTimeEnable: false, //工作时间优先级是否生效
        degradeOutWorkTime: "", //非工作时间降级数
        degradeOutWorkTimeEnable: false, //非工作时间降级数是否生效
        forbidOutWorkTime: "", //非工作时间禁止下降的优先级
        forbidOutWorkTimeEnable: false, //非工作时间优先级是否生效
      },
      regions: [], //区域id
      areaList: [], //区域表格数组
      locationList: [], //场所表格数据
      locations: [], //场所id
      devices: [], //设备id
      deviceList: [], //设备表格数据
      options: [],
      delTitle: "",
      areaOptions: [],
      allBool: [false, false, false, false, false, false, false],
    };
  },
  watch: {
    downForm(val) {
      if (Object.keys(val).length > 0) {
        this.id = val.id;
        this.form.degradeDesc = val.degradeDesc;
        this.form.degradeName = val.degradeName;
        this.effectTimeCfg = { ...val.effectTimeCfg };
        this.regions = [...val.regions];
        this.locations = [...val.locations];
        this.devices = [...val.devices];
        this.getLocation();
        val.effectTimeCfg.degradeConfigs.forEach((v, i) => {
          if (v.weekType === "WORKDAY") {
            this.workingHours = { ...v };
          } else if (v.weekType === "SATURDAY") {
            this.saturdayHours = { ...v };
          } else {
            this.sundayHours = { ...v };
          }
        });
      }
    },
  },

  created() {
    this.timeZone.unshift(
      {
        zoneId: "客户默认",
        displayName: "客户默认",
      },
      {
        zoneId: "设备默认",
        displayName: "设备默认",
      }
    );

    let map = new Map();
    for (let item of this.timeZone) {
      map.set(item.zoneId, item);
    }
    this.timeZone = [...map.values()];
  },
  mounted() {
    this.handleRefreshRegionTable();
  },
  methods: {
    handleCancel() {
      //
      this.visible = false;
    },
    getDownList(id) {
      getDegradeDefault({}).then((res: any) => {
        if (res.success) {
          this.id = res.data.id;
          this.form.degradeDesc = res.data.degradeDesc;
          this.form.degradeName = res.data.degradeName;
          this.effectTimeCfg = { ...res.data.effectTimeCfg };

          this.effectTimeCfg.coverWorkTime.forEach((item) => {
            if (item.workTime.length > 23) {
              this.allBool[item.weekDay - 1] = true;
            } else {
              this.allBool[item.weekDay - 1] = false;
            }
          });

          this.regions = [...res.data.regions];
          this.locations = [...res.data.locations];
          this.devices = [...res.data.devices];
          this.getLocation();
          res.data.effectTimeCfg.degradeConfigs.forEach((v, i) => {
            if (v.weekType === "WORKDAY") {
              this.workingHours = { ...v };
            } else if (v.weekType === "SATURDAY") {
              this.saturdayHours = { ...v };
            } else {
              this.sundayHours = { ...v };
            }
          });
        }
        // if (res.success && res.data instanceof Object) {
        //   downForm.value = res.data;
        // } else {
        //   downForm.value = {};
        // }
        // // console.log(downForm.value);
      });
    },
    open(params) {
      this.visible = true;
      // // console.log(params);
      if (JSON.stringify(params) == "{}") {
        this.id = "";
        // this.title='新增'
      } else {
        this.id = params.id;
        this.getDownList(params.id);
        // this.title='编辑'
      }
      setTimeout(() => {
        let width = this.$refs.tableContentRef.offsetWidth;
        this.tableWidth = ((width - 80) / 24).toFixed(0);
        //根据屏幕缩放自动获取页面宽高
        window.onresize = () => {
          return (() => {
            let width = this.$refs.tableContentRef.offsetWidth;
            this.tableWidth = ((width - 80) / 24).toFixed(0);
          })();
        };
      }, 500);
    },
    getLocation() {
      const params = {
        pageNumber: 1,
        pageSize: 9999,
      };
      getLocationsTenantCurrent(params).then((res) => {
        if (res.success) {
          res.data.forEach((v, i) => {
            this.locations.forEach((item) => {
              if (v.id === item) {
                this.locationList.push(v);
              }
            });
          });

          let map = new Map();
          for (let item of this.locationList) {
            map.set(item.id, item);
          }
          this.locationList = [...map.values()];
        }
      });
      getRegionsTenantCurrent({}).then((res) => {
        // // console.log(res, 555);
        if (res.success) {
          res.data.forEach((v, i) => {
            this.regions.forEach((item) => {
              if (v.id === item) {
                this.areaList.push(v);
              }
            });
          });
          let map = new Map();
          for (let item of this.areaList) {
            map.set(item.id, item);
          }
          this.areaList = [...map.values()];
        }
      });
      getDeviceList(params).then((res) => {
        if (res.success) {
          res.data.forEach((v, i) => {
            this.devices.forEach((item) => {
              // // console.log(v, item);

              if (v.id === item) {
                this.deviceList.push(v);
              }
            });
          });

          let map = new Map();
          for (let item of this.deviceList) {
            map.set(item.id, item);
          }
          this.deviceList = [...map.values()];
        }
      });
    },
    //确认当前保存的数据
    confirmMsg(val) {
      // // console.log(val.value);
      if (val.type === "location") {
        Object.getOwnPropertyNames(val.value).forEach((key) => {
          // // console.log(key,);
          this.locations.push(val.value[key]);
        });
        // for (let key in val.value) {

        // }
      } else if (val.type === "area") {
        // console.log(val.value);
        this.regions = val.value;
      } else {
        Object.getOwnPropertyNames(val.value).forEach((key) => {
          // // console.log(key,);
          this.devices.push(val.value[key]);
        });
      }
      this.getLocation();
      this.$refs.DownSlaConfig.dialogVisible = false;
    },
    //新增区域/场所/设备
    addModule(type) {
      this.type = type;
      const params = {
        pageNumber: 1,
        pageSize: 9999,
      };
      if (type === "location") {
        getLocationsTenantCurrent(params).then((res) => {
          if (res.success) {
            let data = [];
            res.data.forEach((v, i) => {
              data.push({
                label: v.name,
                value: v.id,
                disabled: false,
              });
              this.locations.forEach((item) => {
                if (item === v.id) {
                  data[i].disabled = true;
                }
              });
            });

            this.options = data;
          }
        });
      } else if (type == "area") {
        // let
        this.findChild(this.allRegionSelect);

        this.options = [...this.allRegionSelect];
      } else {
        getDeviceList(params).then((res) => {
          if (res.success) {
            let data = [];
            res.data.forEach((v, i) => {
              data.push({
                label: v.name,
                value: v.id,
                disabled: false,
              });
              this.devices.forEach((item) => {
                if (item === v.id) {
                  data[i].disabled = true;
                }
              });
            });
            this.options = data;
          }
        });
      }
      this.$refs.DownSlaConfig.areaList = [];
      this.$refs.DownSlaConfig.value = "";
      this.$refs.DownSlaConfig.dialogVisible = true;
      // this.$refs.DownSlaConfig.addType = type;
    },
    findChild(array) {
      for (let i in array) {
        var data = array[i];

        this.regions.forEach((item) => {
          if (item === data.id) {
            data.disabled = true;
          }
        });

        if (data.children) {
          this.findChild(data.children);
        }
      }
      // // console.log(this.allRegionSelect);
    },
    //覆盖时间列
    handleClick({ column, evene }) {
      let index = Number(column.label);

      this.effectTimeCfg.coverWorkTime.forEach((v, i) => {
        // console.log(v.workTime);
        if (v.workTime[v.workTime.length - 1] === index) {
          v.workTime.pop();
          v.workTime = [...new Set(v.workTime)];
        } else {
          v.workTime.push(index as any);
          v.workTime = [...new Set(v.workTime)];
        }
      });
    },
    //覆盖时间
    handleSelectTime(key, weekIndex, row) {
      if (key === "all") {
        this.allBool[weekIndex] = !this.allBool[weekIndex];
        let data = [];
        for (let i = 0; i < 24; i++) {
          data.push(i);
        }
        row.workTime = [...new Set(data)];
        if (!this.allBool[weekIndex]) {
          row.workTime = [];
        }
      } else {
        const index = row.workTime.indexOf(key);
        if (index == -1) {
          row.workTime.push(key);
        } else row.workTime.splice(index, 1);
      }
    },
    // //删除
    delLevel(type, index, data) {
      this.delTitle = "确认删除当前数据吗？";
    },
    delConfirm(type, index, data) {
      if (type == "area") {
        this.regions.splice(index, 1);
        this.areaList.splice(index, 1);

        // setTimeout(() => {
        //   this.findChild(this.allRegionSelect);
        // }, 500);
      } else if (type === "location") {
        this.locations.splice(index, 1);
        this.locationList.splice(index, 1);
      } else {
        this.devices.splice(index, 1);
        this.deviceList.splice(index, 1);
      }
    },

    //新增服务
    CreateSlaDownConfig() {
      // this.build();
      let data = [];
      if (this.effectTimeCfg.timeZone === "客户默认") {
        this.effectTimeCfg.useCustomerTimeZone = true;
        this.effectTimeCfg.useDeviceTimeZone = false;
      } else if (this.effectTimeCfg.timeZone === "设备默认") {
        this.effectTimeCfg.useDeviceTimeZone = true;
        this.effectTimeCfg.useCustomerTimeZone = false;
      } else {
        this.effectTimeCfg.useCustomerTimeZone = false;
        this.effectTimeCfg.useDeviceTimeZone = false;
      }

      data[0] = { ...this.workingHours, weekType: "WORKDAY" };
      data[1] = { ...this.saturdayHours, weekType: "SATURDAY" };
      data[2] = { ...this.sundayHours, weekType: "SUNDAY" };
      this.effectTimeCfg.degradeConfigs = data;
      let params = {
        degradeName: this.form.degradeName,
        degradeDesc: this.form.degradeDesc,
        defaultRule: true,
        effectTimeCfg: this.effectTimeCfg,
        regions: this.regions,
        locations: this.locations,
        devices: this.devices,
        id: this.id ? this.id : "",
      };

      this.$refs["roleForm"].validate((valid) => {
        if (valid) {
          if (this.id) {
            this.$emit("confirm", { data: params, type: "edit" });
            this.visible = false;
          } else {
            this.$emit("confirm", { data: params, type: "add" });
            this.visible = false;
          }
        } else {
          this.$message.error("请输入SLA降级服务名称");
        }
      });
    },

    // backRouter /* 返回上一页 */() {
    //   this.$router.replace({ path: "/SlaDownConfig" });
    // },
  },
};
</script>

<style scoped lang="scss">
.alarm-down-config {
  :deep(.alarm-table) {
    .cell {
      padding: 0 !important;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      // line-height: 50px;
    }
    .el-table__cell {
      padding: 0 !important;
      height: 50px;
    }
    .week {
      width: 100%;
    }
  }
  .elstyle-card {
    margin-top: 20px;
  }
  .sun {
    font-size: 30px;
    background: rgb(26, 190, 107);
    display: flex;
    align-items: center;
    justify-content: center;

    :deep() .elstyle-button--text {
      color: #fff;
    }
  }
  .moon {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;

    background: #fff;

    :deep() .elstyle-button--text {
      color: rgb(153, 153, 153);
    }
  }
}

.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}
// ::v-deep .elstyle-form-item__content .el-form-item-content {
//   display: flex;
//   flex-direction: column;
// }
</style>
