<template>
  <!-- 对话框表单 -->
  <el-card class="el-card-mt">
    <template #default>
      <el-scrollbar>
        <div class="alarm-down-config">
          <el-form :model="form" label-position="left" :rules="rules" ref="roleForm" class="alarm-table">
            <el-card class="el-card-mt">
              <el-row>
                <el-form-item style="width: 100%">
                  <!-- <el-row style="width: 100%">
                    <el-col class="bold" :span="12" style="text-align: left">
                      <el-form-item label="工作时间："> </el-form-item>
                    </el-col>
                    <el-col class="bold" :span="12" style="text-align: right">
                      <el-form-item label="选择时区：">
                        <el-select v-model="effectTimeCfg.timeZone" filterable placeholder="请选择" :style="basicClassInputDown" style="width: 400px">
                          <el-option v-for="item in timeZone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"> </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row> -->

                  <el-col :span="12">
                    <el-form-item :label="$t('alarmMerge.WorkingHours')">
                      <!--  -->
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('alarmMerge.SelectTimeZone')">
                      <el-select v-model="effectTimeCfg.timeZone" filterable :placeholder="$t('alarmMerge.PleaseSelect')" class="tw-w-full">
                        <el-option v-for="item in timeZone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"> </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-form-item>
                <el-form-item style="width: 100%">
                  <el-row style="width: 100%">
                    <el-col>
                      <div style="width: 100%" class="support-table-content" ref="tableContentRef">
                        <el-table stripe border :data="effectTimeCfg.coverWorkTime" style="width: 100%; margin-top: 0px" @header-click="(column, $event) => handleClick({ column, $event })">
                          <el-table-column align="center" prop="week" width="80">
                            <template #default="scope">
                              <div class="week" v-preventReClick @click="handleSelectTime('all', scope.$index, scope.row)" style="width: 100%">
                                {{ scope.row.weekDay == 1 ? $t("alarmMerge.Monday") : scope.row.weekDay == 2 ? $t("alarmMerge.Tuesday") : scope.row.weekDay == 3 ? $t("alarmMerge.Wednesday") : scope.row.weekDay == 4 ? $t("alarmMerge.Thursday") : scope.row.weekDay == 5 ? $t("alarmMerge.Friday") : scope.row.weekDay == 6 ? $t("alarmMerge.Saturday") : $t("alarmMerge.Sunday") }}
                              </div>
                            </template>
                          </el-table-column>
                          <el-table-column align="left" :width="tableWidth" v-for="(item, key) in 24" :key="`h-${key}`" :label="String(key)">
                            <template #default="scope">
                              <div v-preventReClick @click="handleSelectTime(key, scope.$index, scope.row)" style="width: 100%; height: 100%" :class="scope.row.workTime && scope.row.workTime.length && scope.row.workTime.includes(key) ? 'sun' : 'moon'">
                                <!-- <span>{{ (scope.row.workTime[key], scope.row.workTime.includes(key)) }}</span> -->
                                <!-- <el-button type="text" style="font-size: 30px"> -->
                                <el-icon v-if="scope.row.workTime && scope.row.workTime.length && scope.row.workTime.includes(key)" class="tw-text-white"><Sunny></Sunny></el-icon>
                                <el-icon v-else class="tw-text-black"><Moon></Moon></el-icon>
                                <!-- </el-button> -->
                              </div>
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </el-col>
                  </el-row>
                </el-form-item>
                <el-form-item style="width: 100%">
                  <el-row style="width: 100%">
                    <el-col class="bold" :span="2" style="text-align: right">{{ $t("alarmMerge.DowngradeConfiguration") }}</el-col>
                  </el-row>
                  <el-row :gutter="20" style="width: 100%">
                    <el-col :span="8">
                      <el-card>
                        <div>
                          <span>{{ $t("alarmMerge.WeekdayDowngradePolicyConfiguration") }}</span>
                        </div>
                        <el-row>
                          <el-col>{{ $t("alarmMerge.DuringWorkingHours") }}<el-button type="text" icon="el-icon-sunny"></el-button></el-col>
                        </el-row>

                        <el-row style="display: flex">
                          <el-col :span="12"> {{ $t("alarmMerge.SelectPriorityDowngradeLevel") }}</el-col>
                          <el-col :span="3" style="text-align: right">
                            <el-switch v-model="workingHours.degradeInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="workingHours.degradeInWorkTime" style="width: 100px">
                              <el-option v-for="item in downNumberOptions" :disabled="!workingHours.degradeInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="10"> {{ $t("alarmMerge.ProhibitPriorityBelow") }}</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="workingHours.forbidInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="workingHours.forbidInWorkTime" style="width: 100px">
                              <el-option v-for="item in downLevelOptions" :disabled="!workingHours.forbidInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col>{{ $t("alarmMerge.OutsideWorkingHours") }}<el-button type="text" icon="el-icon-moon"></el-button></el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="12"> {{ $t("alarmMerge.SelectPriorityDowngradeLevel") }}</el-col>
                          <el-col :span="3" style="text-align: right">
                            <el-switch v-model="workingHours.degradeOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="workingHours.degradeOutWorkTime" style="width: 100px">
                              <el-option v-for="item in downNumberOptions" :disabled="!workingHours.degradeOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="10"> {{ $t("alarmMerge.ProhibitPriorityBelow") }}</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="workingHours.forbidOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="workingHours.forbidOutWorkTime" style="width: 100px">
                              <el-option v-for="item in downLevelOptions" :disabled="!workingHours.forbidOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row>
                          <span>*{{ $t("alarmMerge.MinimumPriorityCanOnlyBeDowngradedToP7") }}</span>
                        </el-row>
                      </el-card>
                    </el-col>
                    <el-col :span="8">
                      <el-card>
                        <div>
                          <span>{{ $t("alarmMerge.SaturdayDowngradeConfiguration") }}</span>
                        </div>
                        <el-row>
                          <el-col>{{ $t("alarmMerge.DuringWorkingHours") }}<el-button type="text" icon="el-icon-sunny"></el-button></el-col>
                        </el-row>

                        <el-row style="display: flex">
                          <el-col :span="12"> {{ $t("alarmMerge.SelectPriorityDowngradeLevel") }}</el-col>
                          <el-col :span="3" style="text-align: right">
                            <el-switch v-model="saturdayHours.degradeInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="saturdayHours.degradeInWorkTime" style="width: 100px">
                              <el-option v-for="item in downNumberOptions" :disabled="!saturdayHours.degradeInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="10"> {{ $t("alarmMerge.ProhibitPriorityBelow") }}</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="saturdayHours.forbidInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="saturdayHours.forbidInWorkTime" style="width: 100px">
                              <el-option v-for="item in downLevelOptions" :disabled="!saturdayHours.forbidInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col>{{ $t("alarmMerge.OutsideWorkingHours") }}<el-button type="text" icon="el-icon-moon"></el-button></el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="12"> {{ $t("alarmMerge.SelectPriorityDowngradeLevel") }}</el-col>
                          <el-col :span="3" style="text-align: right">
                            <el-switch v-model="saturdayHours.degradeOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="saturdayHours.degradeOutWorkTime" style="width: 100px">
                              <el-option v-for="item in downNumberOptions" :disabled="!saturdayHours.degradeOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="10"> {{ $t("alarmMerge.ProhibitPriorityBelow") }}</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="saturdayHours.forbidOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="saturdayHours.forbidOutWorkTime" style="width: 100px">
                              <el-option v-for="item in downLevelOptions" :disabled="!saturdayHours.forbidOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row>
                          <span>*{{ $t("alarmMerge.MinimumPriorityCanOnlyBeDowngradedToP7") }}</span>
                        </el-row>
                      </el-card>
                    </el-col>
                    <el-col :span="8">
                      <el-card>
                        <div>
                          <span>{{ $t("alarmMerge.SundayDowngradeConfiguration") }}</span>
                        </div>
                        <el-row>
                          <el-col>{{ $t("alarmMerge.DuringWorkingHours") }}<el-button type="text" icon="el-icon-sunny"></el-button></el-col>
                        </el-row>

                        <el-row style="display: flex">
                          <el-col :span="12"> {{ $t("alarmMerge.SelectPriorityDowngradeLevel") }}</el-col>
                          <el-col :span="3" style="text-align: right">
                            <el-switch v-model="sundayHours.degradeInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="sundayHours.degradeInWorkTime" style="width: 100px">
                              <el-option v-for="item in downNumberOptions" :disabled="!sundayHours.degradeInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="10"> {{ $t("alarmMerge.ProhibitPriorityBelow") }}</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="sundayHours.forbidInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="sundayHours.forbidInWorkTime" style="width: 100px">
                              <el-option v-for="item in downLevelOptions" :disabled="!sundayHours.forbidInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col>{{ $t("alarmMerge.OutsideWorkingHours") }}<el-button type="text" icon="el-icon-moon"></el-button></el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="12"> {{ $t("alarmMerge.SelectPriorityDowngradeLevel") }}</el-col>
                          <el-col :span="3" style="text-align: right">
                            <el-switch v-model="sundayHours.degradeOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="sundayHours.degradeOutWorkTime" style="width: 100px">
                              <el-option v-for="item in downNumberOptions" :disabled="!sundayHours.degradeOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row style="display: flex">
                          <el-col :span="10"> {{ $t("alarmMerge.ProhibitPriorityBelow") }}</el-col>
                          <el-col :span="5" style="text-align: right">
                            <el-switch v-model="sundayHours.forbidOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                          </el-col>
                          <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                            <el-select v-model="sundayHours.forbidOutWorkTime" style="width: 100px">
                              <el-option v-for="item in downLevelOptions" :disabled="!sundayHours.forbidOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                            </el-select>
                          </el-col>
                        </el-row>
                        <el-row>
                          <span>*{{ $t("alarmMerge.MinimumPriorityCanOnlyBeDowngradedToP7") }}</span>
                        </el-row>
                      </el-card>
                    </el-col>
                    <!-- <el-col :style="{ textAlign: 'center', marginTop: '10px' }">
                <el-button type="primary" v-preventReClick @click="CreateSlaDownConfig">保存</el-button>
              </el-col> -->
                  </el-row>
                </el-form-item>
              </el-row>
            </el-card>
            <el-col :style="{ textAlign: 'center', marginTop: '10px', paddingBottom: '10px' }">
              <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission('638972170063577088')">
                <el-button :disabled="!userInfo.hasPermission('638972170063577088')" type="primary" v-preventReClick @click="CreateSlaDownConfig">{{ $t("glob.Save") }}</el-button>
              </el-tooltip>
            </el-col>
            <el-card class="el-card-mt el-card-table">
              <h2>{{ $t("alarmMerge.AssignToRegion") }}</h2>
              <el-row style="width: 100%">
                <el-col :span="24" style="text-align: right">
                  <!-- <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(服务管理中心_自动工单_分配区域)"> -->
                  <el-button :disabled="!userInfo.hasPermission('638972263592361984')" type="primary" v-preventReClick @click="addModule('area')">
                    <el-icon class="el-icon--left"><Plus /></el-icon>{{ $t("alarmMerge.AssignRegion") }}
                  </el-button>
                  <!-- </el-tooltip> -->
                </el-col>
                <el-col>
                  <el-table stripe :data="areaList" style="width: 100%; margin-top: 30px">
                    <el-table-column align="left" :label="$t('alarmMerge.Name')" prop="name"> </el-table-column>
                    <el-table-column align="left" :label="$t('alarmMerge.IsActive')" prop="active">
                      <template #default="scope">
                        <el-text type="primary" v-if="scope.row.active">{{ $t("glob.Yes") }}</el-text>
                        <el-text type="danger" v-else>{{ $t("glob.Not") }}</el-text>
                      </template>
                    </el-table-column>

                    <el-table-column align="left" :label="$t('alarmMerge.Actions')">
                      <template #default="scope">
                        <!-- <el-button type="text" v-show="!scope.row.state" v-preventReClick @click="confirmLevel('response', scope.$index, scope.row)">保存</el-button> -->

                        <el-popconfirm :title="delTitle" @confirm="delConfirm('area', scope.$index, scope.row)">
                          <template #reference>
                            <el-button :disabled="!userInfo.hasPermission('638972170063577088')" type="text" textColor="danger" v-preventReClick @click="delLevel('area', scope.$index, scope.row)">{{ $t("alarmMerge.Unassign") }}</el-button>
                          </template>
                        </el-popconfirm>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
                <el-col :span="24" style="margin-top: 20px; text-align: left">
                  <h2>{{ $t("alarmMerge.AssignToVenue") }}</h2>
                </el-col>
                <el-col :span="24" style="margin-top: 20px; text-align: right">
                  <!-- <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(服务管理中心_自动工单_分配场所)"> -->
                  <el-button :disabled="!userInfo.hasPermission('638972304847536128')" v-if="id" type="primary" v-preventReClick @click="addModule('location')">
                    <el-icon class="el-icon--left"><Plus /></el-icon>{{ $t("alarmMerge.AssignVenue") }}
                  </el-button>
                  <!-- </el-tooltip> -->
                </el-col>
                <el-col>
                  <el-table stripe :data="locationList" style="width: 100%; margin-top: 30px">
                    <el-table-column align="left" :label="$t('alarmMerge.Name')" prop="name"> </el-table-column>
                    <el-table-column align="left" :label="$t('alarmMerge.IsActive')" prop="active">
                      <template #default="scope">
                        <el-text type="primary" v-if="scope.row.active">{{ $t("glob.Yes") }}</el-text>
                        <el-text type="danger" v-else>{{ $t("glob.Not") }}</el-text>
                      </template>
                    </el-table-column>
                    <el-table-column align="left" :label="$t('alarmMerge.Actions')">
                      <template #default="scope">
                        <!-- <el-button type="text" v-show="!scope.row.state" v-preventReClick @click="confirmLevel('response', scope.$index, scope.row)">保存</el-button> -->

                        <el-popconfirm :title="delTitle" @confirm="delConfirm('location', scope.$index, scope.row)">
                          <template #reference>
                            <el-button :disabled="!userInfo.hasPermission('638972170063577088')" type="text" textColor="danger" v-preventReClick @click="delLevel('location', scope.$index, scope.row)">{{ $t("alarmMerge.Unassign") }}</el-button>
                          </template>
                        </el-popconfirm>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
                <el-col :span="24" style="margin-top: 20px; text-align: left">{{ $t("alarmMerge.AssignToDevice") }}</el-col>
                <el-col :span="24" style="margin-top: 20px; text-align: right">
                  <!-- <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission('638972329140944896')"> -->
                  <el-button :disabled="!userInfo.hasPermission('638972329140944896')" v-if="id" type="primary" v-preventReClick @click="addModule('device')">
                    <el-icon class="el-icon--left"><Plus /></el-icon>{{ $t("alarmMerge.AssignDevice") }}
                  </el-button>
                  <!-- </el-tooltip> -->
                </el-col>
                <el-col>
                  <el-table stripe :data="deviceList" style="width: 100%; margin-top: 30px">
                    <el-table-column align="left" :label="$t('alarmMerge.Name')" prop="name">
                      <template #default="{ row }">
                        <div>
                          <div style="color: #409eff; cursor: pointer" @click="openDevice(row)">
                            {{ row.name }}
                          </div>
                          <div style="font-size: 12px">
                            {{ row.config.ipAddress }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column align="left" :label="$t('alarmMerge.IsActive')" prop="active">
                      <template #default="scope">
                        <el-text type="primary" v-if="scope.row.active">{{ $t("glob.Yes") }}</el-text>
                        <el-text type="danger" v-else>{{ $t("glob.Not") }}</el-text>
                      </template>
                    </el-table-column>
                    <el-table-column align="left" :label="$t('alarmMerge.Actions')">
                      <template #default="scope">
                        <!-- <el-button type="text" v-show="!scope.row.state" v-preventReClick @click="confirmLevel('response', scope.$index, scope.row)">保存</el-button> -->

                        <el-popconfirm :title="delTitle" @confirm="delConfirm('device', scope.$index, scope.row)">
                          <template #reference>
                            <el-button :disabled="!userInfo.hasPermission('638972170063577088')" type="text" textColor="danger" v-preventReClick @click="delLevel('device', scope.$index, scope.row)">{{ $t("alarmMerge.Unassign") }}</el-button>
                          </template>
                        </el-popconfirm>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </el-card>
            <alarmDownDialog ref="DownSlaConfig" :addType="type" :options="options" :allOptions="allOptions" @confirmMsg="confirmMsg"></alarmDownDialog>
          </el-form>
        </div>
      </el-scrollbar>
    </template>
    <template #footer>
      <!-- <el-button type="warning" v-preventReClick @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <!-- <el-button type="default" v-preventReClick @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button> -->
      <!-- <el-button type="primary" v-preventReClick @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </el-card>
  <!-- <SlaDownDialog ref="DownSlaConfig" :addType="type" :options="options" @confirmMsg="confirmMsg"></SlaDownDialog> -->
</template>

<!--  generic="Item extends Record<'id', string> & Record<string, unknown>" -->
<script lang="ts" name="EditorForm">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, inject, h } from "vue";
import { useRoute, useRouter } from "vue-router";
const width = inject<import("vue").Ref<number>>("width", ref(200));
const height = inject<import("vue").Ref<number>>("height", ref(100));
import { getLocationsTenantCurrent } from "@/views/pages/apis/locationManang";
import { getRegionsTenantCurrent } from "@/views/pages/apis/regionManage";
import { getDeviceQuery } from "@/views/pages/apis/deviceManage";
import { getQueryLocation } from "@/views/pages/apis/device";
import alarmDownDialog from "./alarmDownDialog.vue";
import { queryDegradeAssignedList, queryLocationList, queryResourceList, queryRegionsList } from "@/views/pages/apis/index";
import timeZone from "@/views/pages/common/zone.json";
import mixin from "@/views/pages/alarm_convergence/PropertyManage/regionManage/js/mixin";
import { Sunny, Moon, Plus } from "@element-plus/icons-vue";
import { SlaDownConfigDetaile, DetailNewSlaDownConfig } from "@/views/pages/apis/SlaConfig";
import { useResizeObserver } from "@vueuse/core";
import { slaDownConfigRelation, slaUnDownConfigRelation } from "@/views/pages/apis/SlaConfig";
import { ElMessage, ElMessageBox, ElText } from "element-plus";
import { 服务管理中心_自动工单_分配区域, 服务管理中心_自动工单_分配场所, 服务管理中心_自动工单_分配设备, 服务管理中心_自动工单_编辑 } from "@/views/pages/permission";

import getUserInfo from "@/utils/getUserInfo";
import { useI18n } from "vue-i18n";
export default {
  name: "EventCenterIntelNoiseReductCreate",
  components: {
    Plus,
    alarmDownDialog,
    Sunny,
    Moon,
  },
  mixins: [mixin],
  props: {
    downForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
    detail: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      tableWidth: 0,
      width: width,
      height: height,
      route: useRoute(),
      router: useRouter(),
      title: "告警降级配置",
      activeNames: [""],
      priorityDown: true,
      DefaultTime: "", //选择时区
      degradeName: "", //降级策略名称
      degradeDesc: "", //降级策略类型
      SlaDialogVisible: false,
      visible: false,
      userInfo: getUserInfo(),
      form: {
        degradeName: "",
        degradeDesc: "",
      },
      id: "",
      rules: {
        degradeName: [{ required: true, message: "请输入SLA降级服务名称", trigger: "blur" }],
      },
      timeZone: [...timeZone],
      effectTimeCfg: {
        timeZone: "设备默认",
        useCustomerTimeZone: false,
        useDeviceTimeZone: false,
        coverWorkTime: [
          {
            week: "周一",
            workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
            weekDay: 1,
          },
          {
            week: "周二",
            workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
            weekDay: 2,
          },
          {
            week: "周三",
            workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
            weekDay: 3,
          },
          {
            week: "周四",
            workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
            weekDay: 4,
          },
          {
            week: "周五",
            workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
            weekDay: 5,
          },
          {
            week: "周六",
            workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
            weekDay: 6,
          },
          {
            week: "周日",
            workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
            weekDay: 7,
          },
        ],
        degradeConfigs: [],
      },
      TimeAddress: [],
      basicClassInput: { width: "25.8vw" } /* 输入框选择器基本样式 */,
      basicClassInputDown: { width: "20.8vw" } /* 输入框选择器基本样式 */,
      describeList: [],

      type: "",
      //工作时间降级
      downNumberOptions: [
        {
          label: 1,
          value: 1,
        },
        {
          label: 2,
          value: 2,
        },
        {
          label: 3,
          value: 3,
        },
        {
          label: 4,
          value: 4,
        },
        {
          label: 5,
          value: 5,
        },
        {
          label: 6,
          value: 6,
        },
      ],
      downLevelOptions: [
        {
          label: "P1",
          value: "1",
        },
        {
          label: "P2",
          value: "2",
        },
        {
          label: "P3",
          value: "3",
        },
        {
          label: "P4",
          value: "4",
        },
        {
          label: "P5",
          value: "5",
        },
        {
          label: "P6",
          value: "6",
        },
      ],
      workingHours: {
        degradeInWorkTime: "", //工作时间降级数
        degradeInWorkTimeEnable: false, //工作时间降级数是否生效
        forbidInWorkTime: "", //工作时间禁止下降的优先级
        forbidInWorkTimeEnable: false, //工作时间优先级是否生效
        degradeOutWorkTime: "", //非工作时间降级数
        degradeOutWorkTimeEnable: false, //非工作时间降级数是否生效
        forbidOutWorkTime: "", //非工作时间禁止下降的优先级
        forbidOutWorkTimeEnable: false, //非工作时间优先级是否生效
      },
      saturdayHours: {
        degradeInWorkTime: "", //工作时间降级数
        degradeInWorkTimeEnable: false, //工作时间降级数是否生效
        forbidInWorkTime: "", //工作时间禁止下降的优先级
        forbidInWorkTimeEnable: false, //工作时间优先级是否生效
        degradeOutWorkTime: "", //非工作时间降级数
        degradeOutWorkTimeEnable: false, //非工作时间降级数是否生效
        forbidOutWorkTime: "", //非工作时间禁止下降的优先级
        forbidOutWorkTimeEnable: false, //非工作时间优先级是否生效
      },
      sundayHours: {
        degradeInWorkTime: "", //工作时间降级数
        degradeInWorkTimeEnable: false, //工作时间降级数是否生效
        forbidInWorkTime: "", //工作时间禁止下降的优先级
        forbidInWorkTimeEnable: false, //工作时间优先级是否生效
        degradeOutWorkTime: "", //非工作时间降级数
        degradeOutWorkTimeEnable: false, //非工作时间降级数是否生效
        forbidOutWorkTime: "", //非工作时间禁止下降的优先级
        forbidOutWorkTimeEnable: false, //非工作时间优先级是否生效
      },
      regions: [], //区域id
      areaList: [], //区域表格数组
      locationList: [], //场所表格数据
      locations: [], //场所id
      devices: [], //设备id
      deviceList: [], //设备表格数据
      options: [],
      allOptions: [],
      delTitle: "",
      areaOptions: [],
      allBool: [false, false, false, false, false, false, false],
      i18n: useI18n(),
    };
  },
  watch: {
    downForm(val) {
      if (Object.keys(val).length > 0) {
        this.id = val.id;
        this.form.degradeDesc = val.degradeDesc;
        this.form.degradeName = val.degradeName;
        this.effectTimeCfg = { ...val.effectTimeCfg };
        this.regions = [...val.regions];
        this.locations = [...val.locations];
        this.devices = [...val.devices];
        this.getLocation();
        val.effectTimeCfg.degradeConfigs.forEach((v, i) => {
          if (v.weekType === "WORKDAY") {
            this.workingHours = { ...v };
          } else if (v.weekType === "SATURDAY") {
            this.saturdayHours = { ...v };
          } else {
            this.sundayHours = { ...v };
          }
        });
      }
    },
  },

  created() {
    this.timeZone.unshift({
      zoneId: "设备默认",
      displayName: "设备默认",
    });

    let map = new Map();
    for (let item of this.timeZone) {
      map.set(item.zoneId, item);
    }
    this.timeZone = [...map.values()];
  },
  mounted() {
    this.open();
  },
  methods: {
    openDevice(props) {
      const { href } = this.router.resolve({
        name: "509596457372745728",
        params: { id: props.id },
        query: {
          fallback: this.route.name,
          tenant: props.tenantId,
        },
      });
      window.open(href, props.id);
    },
    handleCancel() {
      this.visible = false;
    },
    getDownList(id) {
      DetailNewSlaDownConfig({ id })
        .then((res: any) => {
          if (res.success) {
            this.id = res.data.id;
            this.form.degradeDesc = res.data.degradeDesc;
            this.form.degradeName = res.data.degradeName;
            if (res.data.effectTimeCfg.coverWorkTime.length > 0) {
              this.effectTimeCfg = { ...res.data.effectTimeCfg };

              this.effectTimeCfg.coverWorkTime.forEach((item) => {
                if (item.workTime.length > 23) {
                  this.allBool[item.weekDay - 1] = true;
                } else {
                  this.allBool[item.weekDay - 1] = false;
                }
              });
            }

            this.regions = [...res.data.regions];
            this.locations = [...res.data.locations];
            this.devices = [...res.data.devices];
            this.getLocation();
            res.data.effectTimeCfg.degradeConfigs.forEach((v, i) => {
              if (v.weekType === "WORKDAY") {
                this.workingHours = { ...v };
              } else if (v.weekType === "SATURDAY") {
                this.saturdayHours = { ...v };
              } else {
                this.sundayHours = { ...v };
              }
            });
          }
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        });
    },
    open() {
      if (this.detail.id) {
        this.getDownList(this.detail.id);
        let width = this.$refs.tableContentRef && this.$refs.tableContentRef.offsetWidth;
        this.tableWidth = ((width - 80) / 24).toFixed(0);
        //根据屏幕缩放自动获取页面宽高
        window.onresize = () => {
          return (() => {
            let width = this.$refs.tableContentRef && this.$refs.tableContentRef.offsetWidth;
            this.tableWidth = ((width - 80) / 24).toFixed(0);
          })();
        };
      }
    },
    getLocation() {
      const params = {
        pageNumber: 1,
        pageSize: 9999,
        active: true,
      };
      getQueryLocation({ active: true }).then((res) => {
        if (res.success) {
          res.data.forEach((v, i) => {
            this.locations.forEach((item) => {
              if (v.id === item) {
                this.locationList.push(v);
              }
            });
          });

          let map = new Map();
          for (let item of this.locationList) {
            map.set(item.id, item);
          }
          this.locationList = [...map.values()];
        }
      });
      getRegionsTenantCurrent(params).then((res) => {
        if (res.success) {
          res.data.forEach((v, i) => {
            this.regions.forEach((item) => {
              if (v.id === item) {
                this.areaList.push(v);
              }
            });
          });
          let map = new Map();
          for (let item of this.areaList) {
            map.set(item.id, item);
          }
          this.areaList = [...map.values()];
        }
      });
      getDeviceQuery(params).then((res) => {
        if (res.success) {
          res.data.forEach((v, i) => {
            this.devices.forEach((item) => {
              if (v.id === item) {
                this.deviceList.push(v);
              }
            });
          });

          let map = new Map();
          for (let item of this.deviceList) {
            map.set(item.id, item);
          }
          this.deviceList = [...map.values()];
        }
      });
    },
    //确认当前保存的数据
    confirmMsg(val) {
      // // console.log(val.value);
      if (val.type === "location") {
        Object.getOwnPropertyNames(val.value).forEach((key) => {
          // // console.log(key,);
          this.locations.push(val.value[key]);
        });
        // console.log(val);
        slaDownConfigRelation({ id: this.detail.id, type: "assignLocation", locations: val.value })
          .then((res) => {
            this.$message.success(this.i18n.t("axios.Operation successful"));
          })
          .catch((err) => {
            this.$message.error(err.message);
          });
      } else if (val.type === "area") {
        // console.log(val.value);
        this.regions = val.value;
        slaDownConfigRelation({ id: this.detail.id, type: "assignRegion", regions: val.value })
          .then((res) => {
            this.$message.success(this.i18n.t("axios.Operation successful"));
          })
          .catch((err) => {
            this.$message.error(err.message);
          });
      } else {
        Object.getOwnPropertyNames(val.value).forEach((key) => {
          // // console.log(key,);
          this.devices.push(val.value[key]);
        });
        slaDownConfigRelation({ id: this.detail.id, type: "assignDevice", devices: val.value })
          .then((res) => {
            this.$message.success(this.i18n.t("axios.Operation successful"));
          })
          .catch((err) => {
            this.$message.error(err.message);
          });
      }
      this.getLocation();
      this.$refs.DownSlaConfig.dialogVisible = false;
    },
    //新增区域/场所/设备
    addModule(type) {
      this.options = [];
      this.allOptions = [];
      this.type = type;

      const params = {
        pageNumber: 1,
        pageSize: 9999,
        active: true,
        containerId: this.userInfo.currentTenant.containerId,
        queryPermissionId: "",
        verifyPermissionIds: "",
      };

      const permissionMapping = {
        location: "638986075099889664",
        area: "638985913375916032",
        default: "638985596299116544",
      };

      const queryMapping = {
        location: queryLocationList,
        area: queryRegionsList,
        default: queryResourceList,
      };

      const assignedIdsMapping = {
        location: "locations",
        area: "regions",
        default: "devices",
      };

      const userPermission = permissionMapping[type] || permissionMapping.default;
      const queryFunction = queryMapping[type] || queryMapping.default;
      const assignedIdType = assignedIdsMapping[type] || assignedIdsMapping.default;

      if (!this.userInfo.hasPermission(userPermission)) {
        this.options = [];
        return;
      }

      const fetchData = (queryFunc) => {
        return new Promise((resolve, reject) => {
          queryFunc(params)
            .then((res) => {
              if (res.success) {
                resolve(res.data);
              } else {
                reject(res.error);
              }
            })
            .catch((error) => {
              reject(error);
            });
        });
      };

      Promise.all([fetchData(queryFunction), fetchData(queryDegradeAssignedList)])
        .then((result) => {
          const [allItems, assignedData] = result;
          const assignedIds = assignedData[assignedIdType];
          const newArray = allItems.filter((item) => !assignedIds.includes(item.id));
          this.options = newArray;
          this.allOptions = allItems;
        })
        .catch((error) => {
          console.error("Error fetching data:", error);
        });

      this.$refs.DownSlaConfig.areaList = [];
      this.$refs.DownSlaConfig.value = "";
      this.$refs.DownSlaConfig.dialogVisible = true;
    },
    //覆盖时间列
    handleClick({ column, evene }) {
      let index = Number(column.label);
      const { coverWorkTime } = this.effectTimeCfg;

      let workTime = [];
      coverWorkTime.forEach((v) => {
        workTime = workTime.concat(v.workTime);
      });

      const isActive = workTime.includes(index) && workTime.filter((v) => v === index).length === 7; // 是否激活
      // console.log(isActive);

      coverWorkTime.forEach((v: Record<string, any>, i: number) => {
        let delIndex = v.workTime.indexOf(index);
        if (isActive) {
          v.workTime.splice(delIndex, 1);
        } else {
          v.workTime.push(index);
        }

        v.workTime = [...new Set(v.workTime.sort((a, b) => a - b))];
      });
    },
    //覆盖时间
    handleSelectTime(key, weekIndex, row) {
      if (key === "all") {
        this.allBool[weekIndex] = !this.allBool[weekIndex];
        let data = [];
        for (let i = 0; i < 24; i++) {
          data.push(i);
        }
        row.workTime = [...new Set(data)];
        if (!this.allBool[weekIndex]) {
          row.workTime = [];
        }
      } else {
        const index = row.workTime.indexOf(key);
        if (index == -1) {
          row.workTime.push(key);
        } else row.workTime.splice(index, 1);
      }
    },
    // //取消分配
    delLevel(type, index, data) {
      this.delTitle = this.i18n.t("alarmMerge.ConfirmUnassignCurrentData");
    },
    delConfirm(type, index, data) {
      if (type == "area") {
        slaUnDownConfigRelation({ id: this.detail.id, type: "unassignRegion", regions: [data.id] })
          .then((res) => {
            this.$message.success(this.i18n.t("axios.Operation successful"));
            this.regions.splice(index, 1);
            this.areaList.splice(index, 1);
          })
          .catch((err) => {
            this.$message.error(err.message);
          });
      } else if (type === "location") {
        slaUnDownConfigRelation({ id: this.detail.id, type: "unassignLocation", locations: [data.id] })
          .then((res) => {
            this.$message.success(this.i18n.t("axios.Operation successful"));
            this.locations.splice(index, 1);
            this.locationList.splice(index, 1);
          })
          .catch((err) => {
            this.$message.error(err.message);
          });
      } else {
        slaUnDownConfigRelation({ id: this.detail.id, type: "unassignDevice", devices: [data.id] })
          .then((res) => {
            this.$message.success(this.i18n.t("axios.Operation successful"));
            this.devices.splice(index, 1);
            this.deviceList.splice(index, 1);
          })
          .catch((err) => {
            this.$message.error(err.message);
          });
      }
    },

    //新增服务
    CreateSlaDownConfig() {
      let data = [];
      if (this.effectTimeCfg.timeZone === "客户默认") {
        this.effectTimeCfg.useCustomerTimeZone = true;
        this.effectTimeCfg.useDeviceTimeZone = false;
      } else if (this.effectTimeCfg.timeZone === "设备默认") {
        this.effectTimeCfg.useDeviceTimeZone = true;
        this.effectTimeCfg.useCustomerTimeZone = false;
      } else {
        this.effectTimeCfg.useCustomerTimeZone = false;
        this.effectTimeCfg.useDeviceTimeZone = false;
      }

      data[0] = { ...this.workingHours, weekType: "WORKDAY" };
      data[1] = { ...this.saturdayHours, weekType: "SATURDAY" };
      data[2] = { ...this.sundayHours, weekType: "SUNDAY" };
      this.effectTimeCfg.degradeConfigs = data;
      let params = {
        degradeName: this.form.degradeName,
        degradeDesc: this.form.degradeDesc,
        defaultRule: true,
        effectTimeCfg: this.effectTimeCfg,
        regions: this.regions,
        locations: this.locations,
        devices: this.devices,
        id: this.detail.id ? this.detail.id : "",
      };
      this.$refs["roleForm"].validate((valid) => {
        if (valid) {
          if (this.detail.id) {
            this.$emit("confirm", { data: params, type: "add" });
            this.visible = false;
          } else {
            this.$emit("confirm", { data: params, type: "edit" });
            this.visible = false;
          }
        } else {
          this.$message.error("请输入SLA降级服务名称");
        }
      });
    },

    // backRouter /* 返回上一页 */() {
    //   this.$router.replace({ path: "/SlaDownConfig" });
    // },
  },
};
</script>

<style scoped lang="scss">
.alarm-down-config {
  :deep(.alarm-table) {
    .cell {
      padding: 0 !important;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      // line-height: 50px;
    }
    .el-table__cell {
      padding: 0 !important;
      height: 50px;
    }
    .week {
      width: 100%;
    }
  }
  .elstyle-card {
    margin-top: 20px;
  }
  .sun {
    font-size: 30px;
    background: rgb(26, 190, 107);
    display: flex;
    align-items: center;
    justify-content: center;

    :deep() .elstyle-button--text {
      color: #fff;
    }
  }
  .moon {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;

    background: #fff;

    :deep() .elstyle-button--text {
      color: rgb(153, 153, 153);
    }
  }

  :deep(.el-card-table) {
    .cell {
      padding: 0 12px !important;
      height: 50px;
      display: flex;
      align-items: left;
      justify-content: left;
    }
  }
}
.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}
// ::v-deep .elstyle-form-item__content .el-form-item-content {
//   display: flex;
//   flex-direction: column;
// }
</style>
