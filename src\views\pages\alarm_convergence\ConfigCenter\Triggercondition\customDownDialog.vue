<template>
  <div class="SubruleAdd">
    <el-dialog title="分配客户" v-model="dialogVisible" append-to-body draggable width="35%" :before-close="handleClose">
      <el-form>
        <el-form-item style="width: 100%">
          <el-row style="width: 100%">
            <el-col style="text-align: right" class="bold" :span="4">
              <span style="color: red">*</span>
              事件等级
            </el-col>
            <el-col :span="14">
              <el-select v-model="levelValue" multiple :placeholder="'请选择' + title" style="margin-left: 30px">
                <el-option v-for="item in eventLevelOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item style="width: 100%">
          <el-row style="width: 100%">
            <el-col style="text-align: right" class="bold" :span="4">
              <span style="color: red">*</span>
              触发条件
            </el-col>
            <el-col :span="14">
              <el-select v-model="triggersValue" multiple :placeholder="'请选择' + title" style="margin-left: 30px">
                <el-option v-for="item in riggersOptions" :key="item.tenantId" :label="item.tenantName" :value="item.tenantId"> </el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import getUserInfo from "@/utils/getUserInfo";

export default {
  name: "SlaDownDialog",
  props: {
    triggersOptions: {
      type: Array,
    },
    eventLevelOptions: {
      type: Array,
    },
  },
  data() {
    return {
      title: "",
      dialogVisible: false,
      levelValue: [],
      triggersValue: [],
      customtypevalue: "1",
      customtype: [
        {
          label: "客户",
          value: "1",
          disabled: true,
        },
      ],
      userInfo: getUserInfo(),
    };
  },
  watch: {},
  mounted() {
    // // console.log(this.$props.addType);
  },
  methods: {
    cancel() {
      this.dialogVisible = false;
    },
    confirm() {
      if (this.triggersValue && this.levelValue) {
        this.$emit("customMsg", { value1: this.triggersValue, value2: this.levelValue });
      } else {
        this.$message.error("请选择");
      }
    },
    handleClose(done) {
      done();
      this.dialogVisible = false;
    },
  },
  expose: ["confirm", "cancel", "dialogVisible", "title", "levelValue", "triggersValue"],
};
</script>
