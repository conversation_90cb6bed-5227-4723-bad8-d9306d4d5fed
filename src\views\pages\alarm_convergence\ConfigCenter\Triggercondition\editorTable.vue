<template>
  <div class="default">
    <el-form :model="form" :style="{ marginTop: '10px', padding: '0 10px' }" label-position="left" label-width="160px" ref="roleFormRef" label-suffix=":">
      <el-row :gutter="20">
        <el-col style="margin-bottom: 20px">触发条件</el-col>
        <el-col :span="24">
          <el-row style="width: 100%">
            <el-col :span="24" style="text-align: left">
              <el-button type="primary" @click="addTriggerCondition('EVENT')">
                <el-icon class="el-icon--left"><Plus /></el-icon>事件管理触发条件</el-button
              >
            </el-col>
            <el-col>
              <el-table :show-header="false" :data="detailInfo.eventOperation" style="width: 100%; margin: 20px 0">
                <el-table-column align="left" label="" prop="priority" width="385">
                  <template #default="scope">
                    <el-select :disabled="scope.row.disabled" v-model="scope.row.priority" placeholder="请选择工单等级！" multiple style="width: 100%">
                      <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="" prop="operation" width="">
                  <template #default="scope">
                    <el-select :disabled="scope.row.disabled" v-model="scope.row.operation" placeholder="请选择触发条件！" multiple style="width: 100%">
                      <el-option v-for="item in eventOptions" :key="item.value" :label="item.label" :value="item.value" @click="handleSelectionChange(item.value, 'EVENT', scope.row)"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="" width="180">
                  <template #default="scope">
                    <el-button v-show="!scope.row.disabled" type="text" textColor="danger" @click="saveCostomLevel('EVENT', scope.$index, scope.row)">保存</el-button>
                    <el-button v-show="!scope.row.disabled" type="text" textColor="danger" @click="cancelCostomLevel('EVENT', scope.$index, scope.row, (scope.row.disabled = true))">取消</el-button>
                    <el-button v-show="scope.row.disabled" type="text" textColor="danger" @click="editCostomLevel('EVENT', scope.$index, scope.row, (scope.row.disabled = false))">编辑</el-button>
                    <el-button v-show="scope.row.disabled" type="text" textColor="danger" @click="delCostomLevel('EVENT', scope.$index, scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="24">
          <el-row style="width: 100%">
            <el-col :span="24" style="text-align: left">
              <el-button type="primary" @click="addTriggerCondition('SERVICE')">
                <el-icon class="el-icon--left"><Plus /></el-icon>服务请求触发条件</el-button
              >
            </el-col>
            <el-col>
              <el-table :show-header="false" :data="detailInfo.serviceOperation" style="width: 100%; margin: 20px 0">
                <el-table-column align="left" label="" prop="priority" width="385">
                  <template #default="scope">
                    <el-select :disabled="scope.row.disabled" v-model="scope.row.priority" placeholder="请选择工单等级！" multiple style="width: 100%">
                      <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="" prop="operation">
                  <template #default="scope">
                    <el-select :disabled="scope.row.disabled" v-model="scope.row.operation" placeholder="请选择触发条件！" multiple style="width: 100%">
                      <el-option v-for="item in serviceOptions" :key="item.value" :label="item.label" :value="item.value" @click="handleSelectionChange(item.value, 'SERVICE', scope.row)"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="" width="180">
                  <template #default="scope">
                    <el-button v-show="!scope.row.disabled" type="text" textColor="danger" @click="saveCostomLevel('SERVICE', scope.$index, scope.row)">保存</el-button>
                    <el-button v-show="!scope.row.disabled" type="text" textColor="danger" @click="cancelCostomLevel('SERVICE', scope.$index, scope.row, (scope.row.disabled = true))">取消</el-button>
                    <el-button v-show="scope.row.disabled" type="text" textColor="danger" @click="editCostomLevel('SERVICE', scope.$index, scope.row, (scope.row.disabled = false))">编辑</el-button>
                    <el-button v-show="scope.row.disabled" type="text" textColor="danger" @click="delCostomLevel('SERVICE', scope.$index, scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="24">
          <el-row style="width: 100%">
            <el-col :span="24" style="text-align: left">
              <el-button type="primary" @click="addTriggerCondition('QUESTION')">
                <el-icon class="el-icon--left"><Plus /></el-icon>问题管理触发条件</el-button
              >
            </el-col>
            <el-col>
              <el-table :show-header="false" :data="detailInfo.questionOperation" style="width: 100%; margin: 20px 0">
                <el-table-column align="left" label="" prop="priority" width="385">
                  <template #default="scope">
                    <el-select :disabled="scope.row.disabled" v-model="scope.row.priority" placeholder="请选择工单等级！" multiple style="width: 100%">
                      <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="" prop="operation">
                  <template #default="scope">
                    <el-select :disabled="scope.row.disabled" v-model="scope.row.operation" placeholder="请选择触发条件！" multiple style="width: 100%">
                      <el-option v-for="item in questionOptions" :key="item.value" :label="item.label" :value="item.value" @click="handleSelectionChange(item.value, 'QUESTION', scope.row)"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="" width="180">
                  <template #default="scope">
                    <el-button v-show="!scope.row.disabled" type="text" textColor="danger" @click="saveCostomLevel('QUESTION', scope.$index, scope.row)">保存</el-button>
                    <el-button v-show="!scope.row.disabled" type="text" textColor="danger" @click="cancelCostomLevel('QUESTION', scope.$index, scope.row, (scope.row.disabled = true))">取消</el-button>
                    <el-button v-show="scope.row.disabled" type="text" textColor="danger" @click="editCostomLevel('QUESTION', scope.$index, scope.row, (scope.row.disabled = false))">编辑</el-button>
                    <el-button v-show="scope.row.disabled" type="text" textColor="danger" @click="delCostomLevel('QUESTION', scope.$index, scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="24">
          <el-row style="width: 100%">
            <el-col :span="24" style="text-align: left">
              <el-button type="primary" @click="addTriggerCondition('CHANGE')">
                <el-icon class="el-icon--left"><Plus /></el-icon>变更管理触发条件</el-button
              >
            </el-col>
            <el-col>
              <el-table :show-header="false" :data="detailInfo.changeOperation" style="width: 100%; margin: 20px 0">
                <el-table-column align="left" label="" prop="priority" width="385">
                  <template #default="scope">
                    <el-select :disabled="scope.row.disabled" v-model="scope.row.priority" placeholder="请选择工单等级！" multiple style="width: 100%">
                      <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="" prop="operation">
                  <template #default="scope">
                    <el-select :disabled="scope.row.disabled" v-model="scope.row.operation" placeholder="请选择触发条件！" multiple style="width: 100%">
                      <el-option v-for="item in changeOptions" :key="item.value" :label="item.label" :value="item.value" @click="handleSelectionChange(item.value, 'CHANGE', scope.row)"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="" width="180">
                  <template #default="scope">
                    <el-button v-show="!scope.row.disabled" type="text" textColor="danger" @click="saveCostomLevel('CHANGE', scope.$index, scope.row)">保存</el-button>
                    <el-button v-show="!scope.row.disabled" type="text" textColor="danger" @click="cancelCostomLevel('CHANGE', scope.$index, scope.row, (scope.row.disabled = true))">取消</el-button>
                    <el-button v-show="scope.row.disabled" type="text" textColor="danger" @click="editCostomLevel('CHANGE', scope.$index, scope.row, (scope.row.disabled = false))">编辑</el-button>
                    <el-button v-show="scope.row.disabled" type="text" textColor="danger" @click="delCostomLevel('CHANGE', scope.$index, scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="24">
          <el-row style="width: 100%">
            <el-col :span="24" style="text-align: left">
              <el-button type="primary" @click="addTriggerCondition('PUBLISH')">
                <el-icon class="el-icon--left"><Plus /></el-icon>发布管理触发条件</el-button
              >
            </el-col>
            <el-col>
              <el-table :show-header="false" :data="detailInfo.publishOperation" style="width: 100%; margin: 20px 0">
                <el-table-column align="left" label="" prop="priority" width="385">
                  <template #default="scope">
                    <el-select :disabled="scope.row.disabled" v-model="scope.row.priority" placeholder="请选择工单等级！" multiple style="width: 100%">
                      <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="" prop="operation">
                  <template #default="scope">
                    <el-select :disabled="scope.row.disabled" v-model="scope.row.operation" placeholder="请选择触发条件！" multiple style="width: 100%">
                      <el-option v-for="item in publishOptions" :key="item.value" :label="item.label" :value="item.value" @click="handleSelectionChange(item.value, 'PUBLISH', scope.row)"> </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="left" label="" width="180">
                  <template #default="scope">
                    <el-button v-show="!scope.row.disabled" type="text" textColor="danger" @click="saveCostomLevel('PUBLISH', scope.$index, scope.row)">保存</el-button>
                    <el-button v-show="!scope.row.disabled" type="text" textColor="danger" @click="cancelCostomLevel('PUBLISH', scope.$index, scope.row, (scope.row.disabled = true))">取消</el-button>
                    <el-button v-show="scope.row.disabled" type="text" textColor="danger" @click="editCostomLevel('PUBLISH', scope.$index, scope.row, (scope.row.disabled = false))">编辑</el-button>
                    <el-button v-show="scope.row.disabled" type="text" textColor="danger" @click="delCostomLevel('PUBLISH', scope.$index, scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { triggerConditionAdd, triggerConditionEdit, triggerConditionDel, getTriggerDetails } from "@/views/pages/apis/Triggercondition";
import { ElMessage, ElMessageBox } from "element-plus";
import { ReleaseManagementEnum, ChangeManagementEnum, ProblemManagementEnum, ServiceManagementEnum, EventManagementEnum, eventLevelEnum } from "./common";
import test from "node:test";
import { el } from "element-plus/es/locale";
export default {
  components: {},
  props: {
    detail: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      ReleaseManagementEnum,
      ChangeManagementEnum,
      ProblemManagementEnum,
      ServiceManagementEnum,
      EventManagementEnum,
      eventLevelEnum,
      detailInfo: {},
      form: {},
      customsList: [],
      disabled: true,
      // 子组件数据
      eventOptions: [],
      serviceOptions: [],
      questionOptions: [],
      changeOptions: [],
      publishOptions: [],
      levelOptions: [],
      manageType: "",
    };
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.initialize();
    });
  },
  methods: {
    handleSelectionChange(event, type, data) {
      let newOptions;
      switch (type) {
        case "EVENT":
          newOptions = this.setSubjectKeys(EventManagementEnum);
          break;
        case "SERVICE":
          newOptions = this.setSubjectKeys(ServiceManagementEnum);
          break;
        case "QUESTION":
          newOptions = this.setSubjectKeys(ProblemManagementEnum);
          break;
        case "CHANGE":
          newOptions = this.setSubjectKeys(ChangeManagementEnum);
          break;
        case "PUBLISH":
          newOptions = this.setSubjectKeys(ReleaseManagementEnum);
          break;
        default:
          break;
      }

      if (event !== "ALL") {
        const idx = data.operation.indexOf("ALL");
        /* 删除全选 */ if (idx !== -1) data.operation.splice(idx, 1);
        /* 补充全选 */ else if (data.operation.length === newOptions.filter((v) => v.value !== "ALL").length) data.operation.push("ALL");
        return;
      }

      if (data.operation.length === newOptions.filter((v) => v.value !== "ALL").length) {
        data.operation = [];
      } else {
        data.operation = newOptions.map((v) => v.value);
      }

      // if (event[event.length - 1] !== "ALL" && data.operation.includes("ALL")) {
      //   let valueToDelete = "ALL";
      //   data.operation = data.operation.filter((item) => item !== valueToDelete);
      // } else {
      //   if (data.operation.includes("ALL")) {
      //     this.$nextTick(() => {
      //       newOptions.forEach((item) => {
      //         if (item.value !== "ALL" && !data.operation.includes(item.value)) {
      //           data.operation.push(item.value);
      //         }
      //       });
      //     });
      //   }
      // }
    },
    cancelCostomLevel(type, index, data) {
      const operations = {
        EVENT: this.detailInfo.eventOperation,
        SERVICE: this.detailInfo.serviceOperation,
        QUESTION: this.detailInfo.questionOperation,
        CHANGE: this.detailInfo.changeOperation,
        PUBLISH: this.detailInfo.publishOperation,
      };
      if (operations.hasOwnProperty(type)) {
        if (data.operationId == undefined) {
          operations[type].splice(index, 1);
          this.initialize();
        }
      }
    },
    // 编辑
    editCostomLevel(type, index, data) {
      this.manageType = type;
    },
    // 保存
    saveCostomLevel(type, index, data) {
      if (data.priority.length === 0) {
        ElMessage.error("请选择工单等级！");
        return;
      } else if (data.operation.length === 0) {
        ElMessage.error("请选择触发条件！");
        return;
      } else {
        const params = {
          id: this.detailInfo.id,
          conditionType: this.manageType,
          eventOperation: {},
          serviceOperation: {},
          questionOperation: {},
          changeOperation: {},
          publishOperation: {},
        };
        const operations = {
          EVENT: params.eventOperation,
          SERVICE: params.serviceOperation,
          QUESTION: params.questionOperation,
          CHANGE: params.changeOperation,
          PUBLISH: params.publishOperation,
        };
        if (operations.hasOwnProperty(type)) {
          operations[type].priority = data.priority;
          operations[type].operation = data.operation;
          operations[type].disabled = true;
          operations[type].operationId = data.operationId;
        }
        console.log("params :>> ", params);
        (data.operationId ? triggerConditionEdit : triggerConditionAdd)(params, data?.operationId || null)
          .then(({ success, data }) => {
            if (success) {
              ElMessage.success("操作成功");
              this.initialize();
            } else ElMessage.error(JSON.parse(data)?.message || "操作失败");
          })
          .catch((e) => {
            if (e instanceof Error) ElMessage.error(e.message);
          });
      }
    },
    //删除
    delCostomLevel(type, index, data) {
      const params = {
        id: this.detailInfo.id,
        conditionType: type,
        operationId: data.operationId,
      };
      ElMessageBox.confirm(`确定删除触发条件?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const { success, data, message } = await triggerConditionDel(params);
            if (!success) throw new Error(message);
            ElMessage.success("操作成功");
            this.initialize();
          } catch (error) {
            error instanceof Error && ElMessage.error(error.message);
          }
          this.initialize();
        })
        .catch((err) => {});
    },
    addTriggerCondition(type) {
      this.manageType = type;
      const params = {
        priority: [],
        operation: [],
        disabled: false,
      };
      const operations = {
        EVENT: this.detailInfo.eventOperation,
        SERVICE: this.detailInfo.serviceOperation,
        QUESTION: this.detailInfo.questionOperation,
        CHANGE: this.detailInfo.changeOperation,
        PUBLISH: this.detailInfo.publishOperation,
      };
      if (operations.hasOwnProperty(type)) {
        operations[type].push(params);
      } else {
        console.log("未知类型数据...");
      }
    },

    initialize() {
      this.levelOptions = this.setSubjectKeys(eventLevelEnum);
      this.eventOptions = this.setSubjectKeys(EventManagementEnum);
      this.serviceOptions = this.setSubjectKeys(ServiceManagementEnum);
      this.questionOptions = this.setSubjectKeys(ProblemManagementEnum);
      this.changeOptions = this.setSubjectKeys(ChangeManagementEnum);
      this.publishOptions = this.setSubjectKeys(ReleaseManagementEnum);
      getTriggerDetails({ id: this.detail.id }).then(({ success, data, total }) => {
        if (success) {
          this.detailInfo = data;
        }
      });
    },
    setSubjectKeys(type) {
      if (type instanceof Array) return type;
      let result = [];
      for (let key in type) {
        result.push({
          label: type[key],
          value: key,
        });
      }
      return result;
    },
  },
};
</script>

<style scoped lang="scss"></style>
