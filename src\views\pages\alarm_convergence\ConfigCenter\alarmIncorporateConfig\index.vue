<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="getSlaList()" @current-change="getSlaList()">
      <template #left>
        <el-input v-model="ServiceSearch" clearable placeholder="请输入SLA名称" @keyup.enter="searchSlaList()" style="width: 220px">
          <template #append>
            <el-button :icon="Search" @click="searchSlaList()" />
          </template>
        </el-input>
      </template>
      <template #right>
        <el-button type="primary" :icon="Plus" @click="handleCreate">{{ $t("glob.add") }}SLA</el-button>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table stripe :data="tableData" :height="tableHeight" style="width: 100%">
          <el-table-column type="index" align="left" prop="date" label="序号" width="100"> </el-table-column>
          <el-table-column align="left" prop="ruleName" label="SLA名称" :formatter="formatterTable" width="180"> </el-table-column>
          <el-table-column align="left" prop="ruleDesc" label="SLA描述" :formatter="formatterTable"> </el-table-column>
          <el-table-column align="left" prop="defaultRule" label="是否默认">
            <template #default="scope">
              <el-switch style="display: block" v-model="scope.row.defaultRule" @change="statusChange(scope.row)" active-color="#13ce66" inactive-color="#D3D3D3"> </el-switch>
            </template>
          </el-table-column>

          <el-table-column align="left" prop="State" label="状态">
            <template #default="{ row }">
              <el-button v-if="!row.status" type="text" textColor="danger">禁用</el-button>
              <el-button v-else type="text" textColor="success">启用</el-button>
            </template>
          </el-table-column>
          <el-table-column align="left" prop="createTime" label="创建时间">
            <template #default="scope">
              <div>
                {{ moment(scope.row.createTime, "x").format("yyyy-MM-DD HH:mm:ss") }}
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" prop="Operation" label="操作">
            <template #default="{ row }">
              <div>
                <el-button type="text" v-show="!row.defaultRule" @click="handleSlaEdit(row as DataItem)">编辑</el-button>
                <el-button v-if="row.status" type="text" textColor="danger" @click="SlaConfigDisable(row as DataItem)">禁用</el-button>
                <el-button v-else type="text" textColor="success" @click="SlaConfigDisable(row as DataItem)">启用</el-button>
                <el-button type="text" textColor="danger" v-show="!row.defaultRule" @click="SlaConfigDelete(row as DataItem)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
  <el-dialog title="新增用户组" v-model="centerDialogVisible" :modal-append-to-body="false" width="40%" height="30%" left>
    <el-row>
      <el-col class="bold" :xs="24" :sm="6" :md="6">用户组名称：</el-col>
      <el-col :xs="24" :sm="14" :md="14">
        <el-input placeholder="请输入用户组名称" v-model="UserGroupName" style="width: 80%; margin-right: 30px"></el-input>
      </el-col>
      <el-col class="bold" :xs="24" :sm="6" :md="6">用户组成员：</el-col>
      <el-col :xs="24" :sm="14" :md="14">
        <el-input placeholder="请输入用户组成员" v-model="UserGroupMember" style="width: 80%; margin-right: 30px,margin-top:20px"></el-input>
      </el-col>
      <el-col class="bold" :xs="24" :sm="6" :md="6">群通知：</el-col>
      <el-col :xs="24" :sm="14" :md="14">
        <el-select v-model="value" placeholder="请选择群类型">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-input placeholder="请输入群通知地址" v-model="GroupAddress" style="width: 80%"></el-input>
      </el-col>
      <el-col class="bold" :xs="24" :sm="6" :md="6">用户组描述：</el-col>
      <el-col :xs="24" :sm="14" :md="14">
        <el-input type="textarea" placeholder="请输入用户组描述" v-model="GroupDesc" style="width: 80%; margin-right: 30px"></el-input>
      </el-col>
    </el-row>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="centerDialogVisible = false">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox } from "element-plus";
import pageTemplate from "@/components/pageTemplate.vue";

/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";

import { getSlaConfigByPage, DelSlaConfig, EnableSlaConfig, SlaConfigStatus, type SlaConfigList as DataItem } from "@/views/pages/apis/SlaConfig";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const siteConfig = useSiteConfig();
const userInfo = ((key) => {
  switch (key) {
    case process.env["APP_SUPER_PLATFORM"]:
      return useSuperInfo();
    case process.env["APP_ADMIN_PLATFORM"]:
      return useAdminInfo();
    case process.env["APP_USERS_PLATFORM"]:
      return useUsersInfo();
    default:
      return null;
  }
})(siteConfig.current);

// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
