<template>
  <div>
    <el-dialog :title="title" v-model="dialogFormVisible" :before-close="handleClose" width="30%">
      <el-form :model="form" :rules="rules" ref="ruleForm">
        <el-form-item label="名称:" :label-width="formLabelWidth" prop="degradeName">
          <el-input v-model="form.degradeName" style="width: 220px" autocomplete="off" placeholder="请输入策略名称"></el-input>
        </el-form-item>
        <el-form-item label="描述:" :label-width="formLabelWidth" prop="degradeDesc">
          <el-input type="textarea" style="width: 220px" v-model="form.degradeDesc" autocomplete="off" :rows="2" placeholder="请输入描述"></el-input>
        </el-form-item>
        <el-form-item label="是否默认:" :label-width="formLabelWidth" prop="defaultable">
          <el-checkbox v-model="form.defaultable"></el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel()">取 消</el-button>
          <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent } from "vue";
import { ElMessage, ElMenuItem, ElMessageBox } from "element-plus";

import { AddNewSlaDownConfig, EditNewSlaDownConfig, hasSlaDownDefaultCloseCode } from "@/views/pages/apis/SlaConfig";
import { getAlarmClassificationList } from "@/views/pages/apis/alarmClassification";

export default defineComponent({
  name: "SlaDownConfigCreate",
  components: {},
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      form: {
        degradeName: "",
        degradeDesc: "",
        defaultable: false,
      },
      rules: {
        degradeName: [{ required: true, message: "请输入策略名称", trigger: "blur" }],
      },
      title: "",
      checked: false,
      dialogFormVisible: false,
      formLabelWidth: "130px",
      type: "",
      options: [],
      value: "",

      disabled: "",
      treeStyle: {
        width: "300px",
        height: "150px",
      },
    };
  },
  watch: {
    dialog(val) {
      // this.$refs["ruleForm"].clearValidate();
      this.dialogFormVisible = val;
    },
    // type(val) {
    //   if (val === "add") {
    //     for (var key in this.form) {
    //       this.form[key] = null;
    //     }
    //   }
    // },
  },
  created() {
    this.getAlarmList();
  },
  methods: {
    getAlarmList() {
      getAlarmClassificationList({}).then((res) => {
        if (res.success) {
          this.options = [...res.data];
        } else {
          ElMessage.error(JSON.parse(res.data)?.message + "操作失败");
        }
      });
    },
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.type === "add") {
            if (this.form.defaultable == true) {
              hasSlaDownDefaultCloseCode({}).then((res) => {
                if (res.data == true) {
                  ElMessageBox.confirm("该客户已有默认策略,修改后,将会覆盖原有策略,是否修改?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                  })
                    .then(() => {
                      AddNewSlaDownConfig(this.form)
                        .then((res) => {
                          if (res.success) {
                            ElMessage.success("新增成功");
                            this.$emit("dialogClose", false);
                            this.$refs[formName].resetFields();
                            this.form.defaultable = false;
                          } else {
                            ElMessage.error(JSON.parse(res.data)?.message);
                            // this.$emit("dialogClose", false);
                          }
                        })
                        .catch((e) => {
                          if (e instanceof Error) ElMessage.error(e.message);
                          // this.$emit("dialogClose", false);
                        });
                    })
                    .catch(() => {});
                } else {
                  AddNewSlaDownConfig(this.form)
                    .then((res) => {
                      if (res.success) {
                        ElMessage.success("新增成功");
                        this.$emit("dialogClose", false);
                        this.$refs[formName].resetFields();
                        this.form.defaultable = false;
                      } else {
                        ElMessage.error(JSON.parse(res.data)?.message);
                        // this.$emit("dialogClose", false);
                      }
                    })
                    .catch((e) => {
                      if (e instanceof Error) ElMessage.error(e.message);
                      // this.$emit("dialogClose", false);
                    });
                }
              });
            } else {
              console.log(this.form, "this.form");
              AddNewSlaDownConfig(this.form)
                .then((res) => {
                  if (res.success) {
                    ElMessage.success("新增成功");
                    this.$emit("dialogClose", false);
                    this.$refs[formName].resetFields();
                    this.form.defaultable = false;
                  } else {
                    ElMessage.error(JSON.parse(res.data)?.message);
                    // this.$emit("dialogClose", false);
                  }
                })
                .catch((e) => {
                  if (e instanceof Error) ElMessage.error(e.message);
                  // this.$emit("dialogClose", false);
                });
            }
          } else {
            if (this.form.defaultable == true) {
              hasSlaDownDefaultCloseCode({}).then((res) => {
                if (res.data == true) {
                  ElMessageBox.confirm("该客户已有默认策略,修改后,将会覆盖原有策略,是否修改?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                  })
                    .then(() => {
                      EditNewSlaDownConfig(this.form)
                        .then((res) => {
                          if (res.success) {
                            ElMessage.success("修改成功");
                            this.$emit("dialogClose", false);
                            this.$refs[formName].resetFields();
                            this.form.defaultable = false;
                          } else {
                            ElMessage.error(JSON.parse(res.data)?.message);
                            // this.$emit("dialogClose", false);
                          }
                        })
                        .catch((e) => {
                          if (e instanceof Error) ElMessage.error(e.message);
                          // this.$emit("dialogClose", false);
                        });
                    })
                    .catch(() => {});
                } else {
                  EditNewSlaDownConfig(this.form)
                    .then((res) => {
                      if (res.success) {
                        ElMessage.success("修改成功");
                        this.$emit("dialogClose", false);
                        this.$refs[formName].resetFields();
                        this.form.defaultable = false;
                      } else {
                        ElMessage.error(JSON.parse(res.data)?.message);
                        // this.$emit("dialogClose", false);
                      }
                    })
                    .catch((e) => {
                      if (e instanceof Error) ElMessage.error(e.message);
                      // this.$emit("dialogClose", false);
                    });
                }
              });
            } else {
              EditNewSlaDownConfig(this.form)
                .then((res) => {
                  if (res.success) {
                    ElMessage.success("修改成功");
                    this.$emit("dialogClose", false);
                    this.$refs[formName].resetFields();
                    this.form.defaultable = false;
                  } else {
                    ElMessage.error(JSON.parse(res.data)?.message);
                    // this.$emit("dialogClose", false);
                  }
                })
                .catch((e) => {
                  if (e instanceof Error) ElMessage.error(e.message);
                  // this.$emit("dialogClose", false);
                });
            }
          }
          // this.dialogFormVisible = false;
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      this.form.defaultable = false;
    },
    cancel() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      this.form.defaultable = false;
    },
    blurChange(data) {
      this.tags.push(data);
    },
    tagClose(index) {
      this.tags.splice(index, 1);
    },
  },
  expose: ["type", "disabled", "title", "form", "value"],
});
</script>
