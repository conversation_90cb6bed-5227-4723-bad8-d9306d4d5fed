<template>
  <el-card :body-style="{ padding: '0px', height: `${height}px`, width: `${width}px` }">
    <div class="alarm-collapse">
      <h2>
        自动工单配置
        <el-icon @click="help(1)" style="color: #2a8bf5; cursor: pointer; margin-left: 10px"><QuestionFilled /></el-icon>
      </h2>
    </div>
    <div class="confirm">
      <el-row :gutter="24">
        <el-col :span="4"> 告警采集 </el-col>
        <el-col :span="10">
          <el-checkbox :disabled="!userInfo.hasPermission(PERMISSION.group520509358275035136.editor)" v-model="form.alertCollect"></el-checkbox>
        </el-col>
        <el-col :span="10">
          <el-button type="primary" @click="submit" :disabled="!userInfo.hasPermission(PERMISSION.group520509358275035136.editor)">保存</el-button>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="4"> 告警自动生成事件 </el-col>
        <el-col :span="10">
          <el-checkbox :disabled="!userInfo.hasPermission(PERMISSION.group520509358275035136.editor)" v-model="form.autoEvent" @change="autoEventChange"></el-checkbox>
        </el-col>
        <!-- <el-col :span="10">
          <el-button type="primary" @click="submit" :disabled="!userInfo.hasPermission(PERMISSION.group520509358275035136.editor)">保存</el-button>
        </el-col> -->
      </el-row>
    </div>
    <div :class="['alarm-merge-message', form.autoEvent ? 'alarm-merge-message-expand' : 'alarm-merge-message-default']">
      <div>
        <!-- <el-row :gutter="24">
          <el-col :span="4"> 事件分配到组 </el-col>
          <el-col :span="10">

            <el-select v-model="form.teamId">
              <el-option v-for="item in eventGroup" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-col>
        </el-row> -->
        <el-row :gutter="24">
          <el-col :span="10">
            使用事件组默认的自动事件规则：<el-icon @click="help(2)" style="color: #2a8bf5; cursor: pointer; margin-left: 10px"><QuestionFilled /></el-icon>
            <el-checkbox v-model="form.defaultRule"></el-checkbox>
          </el-col>
        </el-row>
        <div class="alram-merge-config">
          <h2>自动工单配置</h2>
          <div>
            <el-row :gutter="24">
              <el-col :span="4"> 事件锁定持续时间 </el-col>
              <el-col :span="5">
                <el-select v-model="form.sustainTime">
                  <el-option label="不设置" :value="0"></el-option>
                  <el-option label="15seconds" :value="15"></el-option>
                  <el-option label="30seconds" :value="30"></el-option>
                  <el-option label="45seconds" :value="45"></el-option>
                  <el-option label="60seconds" :value="60"></el-option>
                </el-select>
              </el-col>
              <el-col :span="10" v-show="form.sustainTime != 0"> *事件锁定时间内，生成的告警按照配置的规则被归并到同一事件中。 </el-col>
            </el-row>
            <div class="dobule">
              <el-row :gutter="24">
                <el-col :span="5"> 事件锁定时间内，告警归并 </el-col>
                <el-col :span="4">
                  <el-select v-model="form.alertMerge">
                    <el-option v-for="item in alarmTypeOption" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    <!-- <el-option label="不设置" value="NOT_SET"></el-option>
                    <el-option label="任何告警" value="ANY_ALERT"></el-option>
                    <el-option label="同一区域" value="SAME_REGION"></el-option>
                    <el-option label="同一场所" value="SAME_LOCATION"></el-option>
                    <el-option label="同一设备" value="SAME_DEVICE"></el-option>
                    <el-option label="同种类型告警" value="SAME_ALERT"></el-option> -->
                  </el-select>
                </el-col>
                <el-col :span="15" v-show="form.alertMerge != 'NOT_SET'">
                  *在事件锁定持续时间内，{{ alarmTypeLabel[form.alertMerge] }}
                  {{ form.alertMerge != "ANY_ALERT" && form.alertMerge != "SAME_ALERT" ? "的告警" : "" }}
                  都会被归并到同一事件中。
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="5"> 告警归并到已生成的事件中 </el-col>
                <el-col :span="4">
                  <el-checkbox v-model="form.mergeEvent"></el-checkbox>
                </el-col>
                <el-col :span="15" v-show="form.mergeEvent"> *在事件未解决之前，且未关闭收集告警，同一个设备、相同类型的告警，被归并到该事件中。 </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-card>
  <el-dialog v-model="visible" :show-close="false" width="45%">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <h4 :id="titleId" :class="titleClass">Help</h4>
        <el-icon style="cursor: pointer" @click="close" class="el-icon--left"><Close /></el-icon>
      </div>
    </template>
    <div class="text" v-show="helpType == 1">
      <p>1、自动工单配置是面向某个客户的，每个客户下的设备的告警配置规则可以自定义。一键可配置用户下的所有设备产生的告警都可以自动生成事件。</p>
      <p>2、只有为客户配置了告警采集和自动事件生成功能，事件才会被创建。</p>
      <p>3、用户可以在告警归井配置页面，选中[使用事件组默认的自动事件规则]选框，选择使用默认规则，则自定义规则不起作用。</p>
      <p>4、用户可以在自动工单配置页面，不选中[使用事件组默认的自动事件规则]选框，选择不使用默认规则，则需要自定义配置告警归并规则。</p>
      <p>5、用户可以配置[事件的锁定时间] ，且在锁定时间内，配置告警按照[任何告警] 或同一区域]或[同一场所]或[同一设备] 或[同种类型告警] 的规则归并。</p>
    </div>
    <div class="text" v-show="helpType == 2">
      <p>1、未勾选此选框:如果客户下没有配置自定义的告警归并规则，也不使用默认规则，那自动事件功能就不起作用。 在不使用默认规则的时候，需要自定义配置，告警才可以自动生成事件。</p>
      <p>2、若勾选了此选框:使用默认规则。那自动事件规则就按照默认规则走~自定义规则即便配置，也不起作用。</p>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { QuestionFilled, Close } from "@element-plus/icons-vue";
import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox, ElDialog } from "element-plus";

import pageTemplate from "@/components/pageTemplate.vue";
import { alarmTypeOption, alarmTypeLabel } from "./helper";

/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";

import { getSlaConfigByPage, DelSlaConfig, EnableSlaConfig, SlaConfigStatus, type SlaConfigList as DataItem } from "@/views/pages/apis/SlaConfig";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
import { getGroupList as getData } from "@/api/personnel";
import { getAlarmMerge, setAlarmMerge } from "@/views/pages/apis/alarmMerge";

import getUserInfo from "@/utils/getUserInfo";
import { getSystemVersion } from "@/api/system";

const userInfo = getUserInfo();
const systemEditionOption = ref(new Map<string, string>());
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));
// const autoEvent = ref(false);
const visible = ref(false);
const form = reactive({
  autoEvent: false, //告警自动生成事件
  // teamId: "-1", //用户组id,事件分配到组,-1为不设置
  defaultRule: false, //使用事件组默认的自动事件规则
  sustainTime: 0, //事件锁定持续事件,0为不设置,单位是秒
  alertMerge: "NOT_SET", //事件锁定时间内，告警归并
  mergeEvent: false, //告警归并到已生成的事件中
  alertCollect: false, //告警采集
});
const helpType = ref("");
const eventGroup = reactive([
  {
    label: "不设置",
    value: -1,
  },
]);
// const checked1 = ref(false);
// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {
  // runningInit();
}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */
// runningInit();
// async function runningInit(): Promise<void> {
//   const { success, message, data } = await getSystemVersion({});
//   if (!success) throw Object.assign(new Error(message), { success, data });
//   for (let i = 0; i < data.length; i++) {
//     systemEditionOption.value.set(data[i].code, data[i].name);
//   }
//   // console.log(systemEditionLabel, 666666);
//   if (systemEditionLabel.value === "标准版") {
//     form.alertCollect = true;
//     form.autoEvent = true;
//   }
// }
// const systemEditionLabel = computed(() => {
//   return systemEditionOption.value.get((userInfo.currentTenant || ({ systemEdition: "" } as Partial<import("@/api/system").TenantItem>)).systemEdition || "");
// });
function help(type: number) {
  // // console.log(type);
  // eslint-disable-next-line no-const-assign
  helpType.value = type;
  visible.value = true;
}
async function getItem() {
  getAlarmMerge({})
    .then((res) => {
      // // console.log(res);
      if (res.success) {
        // const form = reactive({
        // form.teamId = res.data.teamId == "-1" ? res.data.teamId * 1 : res.data.teamId; //用户组id事件分配到组-1为不设置
        form.sustainTime = res.data.sustainTime * 1; //事件锁定持续事件0为不设置单位是秒
        form.alertMerge = res.data.alertMerge; //事件锁定时间内，告警归并
        form.autoEvent = res.data.autoEvent; //告警自动生成事件
        form.mergeEvent = res.data.mergeExists; //告警归并到已生成的事件中
        form.defaultRule = res.data.defaultRule; //使用事件组默认的自动事件规则
        form.alertCollect = res.data.alertCollect || false;
        // });
      }
    })
    .catch((err) => {
      ElMessage.error(err.message);
    });
}
async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  getData({ external: false }).then((res) => {
    // // console.log(res);
    if (res.success) {
      res.data.forEach((item) => {
        eventGroup.push({
          label: item.name,
          value: item.id,
        });
      });
    }
  });
}

function submit() {
  // // console.log(form);
  setAlarmMerge({ ...form })
    .then((res) => {
      // // console.log(res);
      if (res.success) {
        ElMessage.success("操作成功");
        getItem();
      }
    })
    .catch((err) => {
      ElMessage.error(err.message);
    });
}

function autoEventChange(val: any) {
  // // console.log(val);
  if (!val) {
    // autoEvent: false, //告警自动生成事件
    // form.teamId = -1; //用户组id,事件分配到组,-1为不设置
    form.defaultRule = false; //使用事件组默认的自动事件规则
    form.sustainTime = 0; //事件锁定持续事件,0为不设置,单位是秒
    form.alertMerge = "NOT_SET"; //事件锁定时间内，告警归并
    form.mergeEvent = false; //告警归并到已生成的事件中
  }
}
/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate(querysItem(), getItem());
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.text {
  > p {
    padding: 5px;
    box-sizing: border-box;
  }
}
.el-row {
  .el-col {
    display: flex;
    align-items: center;
  }
}
.alarm-collapse {
  display: flex;
  align-items: center;
  height: 60px;
  background: rgb(232, 232, 232);
  h2 {
    display: flex;
    align-items: center;
    height: 100%;
    font-size: 16px;
    padding-left: 20px;
    box-sizing: border-box;
  }
}
.my-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.confirm {
  padding: 20px 30px;
  box-sizing: border-box;
}
.alarm-merge-message {
  height: 0px;
  overflow: hidden;
  > div {
    padding: 8px 30px;
    box-sizing: border-box;
  }
}
.alarm-merge-message-expand {
  transition: all 1s;
  // background: red;
  overflow: hidden;
  height: 500px;
  // display: none;
}
.alarm-merge-message-default {
  // height: 500px;
  transition: all 0.5s;

  // transition: max-height 2s ease-out;
}
.alram-merge-config {
  padding: 20px;
  box-sizing: border-box;
  h2 {
    display: flex;
    align-items: center;
    height: 60px;
    // font-size: 16px;
    padding-left: 20px;
    box-sizing: border-box;
    background: rgb(232, 232, 232);
    border: 1px solid #ccc;
    border-bottom: none;
  }
  > div {
    border: 1px solid #ccc;
    border-top: none;
    min-height: 300px;
    padding: 10px;
    box-sizing: border-box;
    .dobule {
      border: 1px solid #ccc;
      // border-top: none;
      // min-height: 300px;
      padding: 10px;
      box-sizing: border-box;
      margin-block: 30px;
      .el-row {
        margin: 10px 0;
      }
    }
  }
}
</style>
