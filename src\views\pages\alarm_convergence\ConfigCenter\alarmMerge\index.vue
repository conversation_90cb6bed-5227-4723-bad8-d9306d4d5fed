<template>
  <el-scrollbar>
    <el-card
      :body-style="{
        padding: '20px',
        height: `${height}px`,
        width: `${width}px`,
      }"
    >
      <template #header v-if="userInfo.hasPermission(服务管理中心_自动工单_可读)">
        <h2 style="font-size: 14px; margin-left: 16px; margin-bottom: 10px; font-weight: 700">{{ $t("alarmMerge.AutomaticTicketConfiguration") }}———{{ userInfo.currentTenant.name }} [{{ userInfo.currentTenant.abbreviation }}]</h2>
        <div class="alarm-collapse">
          <h2>
            {{ $t("alarmMerge.AutomaticTicketConfiguration") }}
            <el-icon @click="help(1)" style="color: #2a8bf5; cursor: pointer; margin-left: 10px"><QuestionFilled /></el-icon>
          </h2>
        </div>
        <div class="confirm">
          <div class="tw-flex tw-items-center tw-justify-between">
            <div class="tw-w-full">
              <el-row :gutter="24">
                <el-col :span="4"> {{ $t("alarmMerge.AlarmCollection") }} </el-col>
                <el-col :span="10">
                  <el-checkbox :disabled="!userInfo.hasPermission(服务管理中心_自动工单_编辑)" v-model="form.alertCollect"></el-checkbox>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="4"> {{ $t("alarmMerge.AutomaticGenerationOfTicketsFromAlarms") }} </el-col>
                <el-col :span="10">
                  <el-checkbox :disabled="!userInfo.hasPermission(服务管理中心_自动工单_编辑)" v-model="form.autoEvent" @change="autoEventChange"></el-checkbox>
                </el-col>
              </el-row>
            </div>

            <div>
              <el-button type="primary" @click="submit">{{ $t("glob.Save") }}</el-button>
            </div>
          </div>

          <!-- <el-row :gutter="24">
            <el-col :span="4"> {{ $t("alarmMerge.AlarmPushToAlarmBoard") }} </el-col>
            <el-col :span="10">
              <el-checkbox :disabled="!userInfo.hasPermission(服务管理中心_自动工单_编辑)" v-model="form.alertBoardPush" @change="autoEventChange"></el-checkbox>
            </el-col>
          </el-row> -->
        </div>
        <div :class="['alarm-merge-message', form.autoEvent ? 'alarm-merge-message-expand' : 'alarm-merge-message-default']">
          <div>
            <el-row :gutter="24">
              <el-col :span="4">
                {{ $t("alarmMerge.UseDefaultAutomaticTicketRulesOfTicketGroup") }}<el-icon @click="help(2)" style="color: #2a8bf5; cursor: pointer; margin-left: 10px"><QuestionFilled /></el-icon>
              </el-col>
              <el-col :span="10">
                <el-checkbox @change="changeCheck" :disabled="!userInfo.hasPermission(服务管理中心_自动工单_编辑)" v-model="form.defaultRule"></el-checkbox>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="4"> {{ $t("orderGroup.Order group") }} </el-col>
              <el-col :span="10">
                <el-select v-model="form.ticketGroupId" :placeholder="$t('alarmMerge.Please select the order group')" @change="(v) => (form.ticketGroupName = (orderGroupOption.find((f) => f.id === v) || {}).ticketGroupName)" :disabled="!userInfo.hasPermission(服务管理中心_自动工单_编辑)" clearable>
                  <el-option v-for="item in orderGroupOption" :key="`orderGroup-${item.id}`" :value="item.id" :label="item.ticketGroupName"></el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="4"> {{ $t("alarmMerge.Use the priority strategy for order groups") }} </el-col>
              <el-col :span="10">
                <el-checkbox :disabled="!userInfo.hasPermission(服务管理中心_自动工单_编辑)" v-model="form.ticketDefalu"></el-checkbox>
              </el-col>
            </el-row>

            <div class="alram-merge-config">
              <h2>{{ $t("alarmMerge.TicketAlarmMergingConfiguration") }}</h2>
              <div style="min-height: 60px">
                <el-row :gutter="24">
                  <el-col :span="4"> {{ $t("alarmMerge.TicketLockDuration") }} </el-col>
                  <el-col :span="5">
                    <el-select :disabled="!userInfo.hasPermission(服务管理中心_自动工单_编辑)" v-model="form.sustainTime">
                      <el-option label="不设置" :value="0"></el-option>
                      <el-option label="15seconds" :value="15"></el-option>
                      <el-option label="30seconds" :value="30"></el-option>
                      <el-option label="45seconds" :value="45"></el-option>
                      <el-option label="60seconds" :value="60"></el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="10" v-show="form.sustainTime != 0"> *{{ $t("alarmMerge.GeneratedAlarmsAreMergedIntoSameTicketAccordingToConfiguredRules") }} </el-col>
                </el-row>
              </div>
              <div class="alram-merge-config">
                <h2>{{ $t("alarmMerge.MergingRules") }}</h2>
                <div style="min-height: 205px">
                  <div class="dobule">
                    <el-row :gutter="24">
                      <el-col :span="5"> {{ $t("alarmMerge.AlarmMergingRules") }} </el-col>
                      <el-col :span="4">
                        <el-select :disabled="!userInfo.hasPermission(服务管理中心_自动工单_编辑)" v-model="form.alertMerge">
                          <el-option v-for="item in alarmTypeOption" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                        </el-select>
                      </el-col>
                      <el-col :span="15" v-show="form.alertMerge != 'NOT_SET'"> *{{ $t("alarmMerge.WithinTheDurationOfTheWorkOrderLock") }}，{{ alarmTypeLabel[form.alertMerge] }} {{ form.alertMerge != "ANY_ALERT" ? $t("alarmMerge.TheAlarm") : "" }}{{ $t("alarmMerge.WillBeMergedIntoSameTicket") }}。 </el-col>
                    </el-row>
                    <el-row :gutter="24">
                      <el-col :span="5"> {{ $t("alarmMerge.MergeAlarmsIntoExistingGeneratedTickets") }} </el-col>
                      <el-col :span="4">
                        <el-checkbox :disabled="!userInfo.hasPermission(服务管理中心_自动工单_编辑)" v-model="form.mergeEvent"></el-checkbox>
                      </el-col>
                      <el-col :span="15"> *{{ $t("alarmMerge.BeforeTicketIsResolvedAndAlarmCollectionNotClosedAlarmsOfSameTypeFromSameDeviceWillBeMergedIntoTicket") }} </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
              <div class="alram-merge-config">
                <h2>
                  {{ $t("alarmMerge.TopologyAlarmMergingRules") }} <span style="margin-left: 29.8%">*{{ $t("alarmMerge.AppliesToAlarmsAffectedByTopologicalRelationshipsIncludingDeviceOrInterfaceDownDeviceSymptoms") }}</span>
                </h2>
                <div>
                  <div class="dobule">
                    <el-row :gutter="24">
                      <el-col :span="5"> {{ $t("alarmMerge.MergeTopologyAlarmsIntoSameTicket") }} </el-col>
                      <el-col :span="4">
                        <el-select :disabled="!userInfo.hasPermission(服务管理中心_自动工单_编辑)" v-model="form.topologyAlertMerge">
                          <el-option v-for="item in anyAlarmTypeOption" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                        </el-select>
                      </el-col>
                      <el-col :span="15" v-show="form.topologyAlertMerge != 'NOT_SET'">
                        *{{ $t("alarmMerge.WhenTheWorkOrderIsLocked") }}，{{ alarmTypeLabelA[form.topologyAlertMerge] }}{{ $t("alarmMerge.TheAlarmsWillBeMergedIntoSameWorkOrder") }}
                        <!-- {{ form.topologyAlertMerge != "ANY_ALERT" && form.topologyAlertMerge != "SAME_ALERT" ? "的告警" : "的告警" }} -->
                      </el-col>
                    </el-row>
                    <el-row :gutter="24">
                      <el-col :span="5"> {{ $t("alarmMerge.MergeTopologyAlarmsIntoExistingGeneratedTickets") }} </el-col>
                      <el-col :span="4">
                        <el-checkbox :disabled="!userInfo.hasPermission(服务管理中心_自动工单_编辑)" v-model="form.topologyMergeEvent"></el-checkbox>
                      </el-col>
                      <el-col :span="15"> *{{ $t("alarmMerge.TopologyAlarmsFromSameDeviceWillBeMergedIntoExistingTicketsGeneratedForDeviceTopologyAlarms") }} </el-col>
                    </el-row>
                    <el-row :gutter="24">
                      <el-col :span="5"> {{ $t("alarmMerge.SNMPTrapTopologyAlarmMerging") }} </el-col>
                      <el-col :span="4">
                        <el-checkbox :disabled="!userInfo.hasPermission(服务管理中心_自动工单_编辑)" v-model="form.trapMergeEvent"></el-checkbox>
                      </el-col>
                      <el-col :span="15"> *{{ $t("alarmMerge.SNMPTrapAlarmsRelatedToTopologyRelationshipsWillBeMergedIntoSameTicketAsAssociatedDeviceStatusAlarmsEgColdStart") }}</el-col>
                    </el-row>
                    <el-row :gutter="24">
                      <el-col :span="5"> {{ $t("alarmMerge.SubDeviceTopologyAlarmsAlwaysMerged") }} </el-col>
                      <el-col :span="4">
                        <el-checkbox :disabled="!userInfo.hasPermission(服务管理中心_自动工单_编辑)" v-model="form.subDeviceMergeEvent"></el-checkbox>
                      </el-col>
                      <el-col :span="15"> *{{ $t("alarmMerge.RegardlessOfMergingRulesSubDeviceTopologyAlarmsWillAlwaysBeMergedIntoParentDeviceUnresolvedAlarmTickets") }} </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <div>
        <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="getList()" @current-change="getList()">
          <template #left>
            <h1 style="font-size: 14px; margin-left: 16px; margin-bottom: 10px; font-weight: 700">{{ $t("alarmMerge.PriorityAdjustmentPolicyConfiguration") }}</h1>
          </template>
          <template #right>
            <span class="">
              <el-button v-if="userInfo.hasPermission(服务管理中心_自动工单_新增)" type="primary" :icon="Plus" @click="deviceDialog('add')">{{ $t("alarmMerge.CreateNewPolicy") }}</el-button>
            </span>
          </template>
          <template #default="{ height: tableHeight }">
            <el-table v-loading="loading" stripe :data="tableData.slice((paging.pageNumber - 1) * paging.pageSize, paging.pageNumber * paging.pageSize)" :height="tableHeight" :row-key="getRowKeys" :expand-row-keys="expands" @expand-change="handleExpandChange" style="width: 100%">
              <el-table-column type="expand">
                <template #default="{ row, expanded }">
                  <newEditor v-if="expanded" :detail="row" :width="width - 40" @confirm="confirm"></newEditor>
                </template>
              </el-table-column>
              <TableColumn type="condition" :prop="`degradeName`" :label="$t('alarmMerge.PolicyName')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="getSlaDownList()" :formatter="formatterTable"></TableColumn>

              <TableColumn type="condition" :prop="`degradeDesc`" :label="$t('alarmMerge.Description')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByDescription" :filters="$filter0" @filter-change="getSlaDownList()" :formatter="formatterTable"></TableColumn>

              <TableColumn
                type="enum"
                :prop="`active`"
                :label="$t('alarmMerge.IsDefault')"
                :min-width="100"
                :showOverflowTooltip="true"
                show-filter
                v-model:filtered-value="searchForm.defaultable"
                :filters="[
                  { value: 'true', text: '√' },
                  { value: 'false', text: '×' },
                ]"
                @filter-change="getSlaDownList()"
              >
                <template #default="scope">
                  <span> {{ scope.row.defaultable == true ? "√" : "×" }}</span>
                </template>
              </TableColumn>
              <!-- <el-table-column prop="active" label="是否默认">
                <template #default="scope">
                  <span> {{ scope.row.defaultable == true ? "√" : "" }}</span>
                </template>
              </el-table-column> -->
              <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="120">
                <template #default="{ row }">
                  <span>
                    <el-link v-if="userInfo.hasPermission(服务管理中心_自动工单_编辑)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="deviceDialog('edit', row)">{{ $t("glob.edit") }}</el-link>
                  </span>
                  <span>
                    <el-link v-if="userInfo.hasPermission(服务管理中心_自动工单_删除)" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="SlaDownConfigDelete(row as DataItem)">{{ $t("glob.delete") }}</el-link>
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </pageTemplate>
      </div>
    </el-card>
  </el-scrollbar>
  <alarmNotesCreate :dialog="alarmdialog" ref="alarmnotes" @dialogClose="dialogClose"></alarmNotesCreate>
  <el-dialog v-model="visible" :show-close="false" width="45%">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <h4 :id="titleId" :class="titleClass">Help</h4>
        <el-icon style="cursor: pointer" @click="close" class="el-icon--left"><Close /></el-icon>
      </div>
    </template>
    <div class="text" v-show="helpType == 1">
      <p>1、{{ $t("alarmMerge.AutomaticTicketConfigurationIsCustomerSpecificOneClickConfigurationForAllDevicesUnderUser") }}</p>
      <p>2、{{ $t("alarmMerge.TicketCreationRequiresAlarmCollectionAndAutomaticTicketGeneration") }}</p>
      <p>3、{{ $t("alarmMerge.UseDefaultRulesOptionInConfigurationPage") }}</p>
      <p>4、{{ $t("alarmMerge.DoNotUseDefaultRulesOptionInConfigurationPage") }}</p>
      <p>5、{{ $t("alarmMerge.ConfigureTicketLockDuration") }}</p>
    </div>
    <div class="text" v-show="helpType == 2">
      <p>1、{{ $t("alarmMerge.UncheckedBoxScenario") }}</p>
      <p>2、{{ $t("alarmMerge.CheckedBoxScenario") }}</p>
      <p>3、{{ $t("alarmMerge.DefaultSystemRules") }}</p>
      <p style="text-indent: 3em">1）{{ $t("alarmMerge.SameDeviceAlarmsGenerateSameTicket") }}</p>
      <p style="text-indent: 3em">2）{{ $t("alarmMerge.SubDeviceTopologyAlarmsMergedIntoParentDeviceUnresolvedTickets") }}</p>
      <p style="text-indent: 3em">3）{{ $t("alarmMerge.NewAlarmsMergedIntoExistingOpenTickets") }}</p>
      <p style="text-indent: 3em">4）{{ $t("alarmMerge.TicketLockDurationIs15Seconds") }}</p>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">{{ $t("glob.Close") }}</el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog v-model="checkVisible" :show-close="false" width="35%">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <h4 :id="titleId" :class="titleClass"></h4>
        <el-icon style="cursor: pointer" @click="close" class="el-icon--left"><Close /></el-icon>
      </div>
    </template>
    <div class="text">
      <h3>{{ $t("alarmMerge.ConfirmToUseAutomaticTicketRulesOnceCheckedTheTicketAlarmMergingConfigurationRulesBelowWillNotTakeEffect") }}</h3>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <!-- <el-button type="primary" @click="checkVisible = false">确 定</el-button>
        <el-button @click="checkVisible = false">取 消</el-button> -->
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { QuestionFilled, Close } from "@element-plus/icons-vue";
import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox, ElDialog } from "element-plus";
import { 服务管理中心_工单组_可读, 服务管理中心_自动工单_可读, 服务管理中心_自动工单_新增, 服务管理中心_自动工单_编辑, 服务管理中心_自动工单_删除, 服务管理中心_自动工单_分配区域, 服务管理中心_自动工单_分配场所, 服务管理中心_自动工单_分配设备 } from "@/views/pages/permission";

import pageTemplate from "@/components/pageTemplate.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { alarmTypeOption, anyAlarmTypeOption, alarmTypeLabel, alarmTypeLabelA } from "./helper";
import alarmNotesCreate from "./alarmNotesCreate.vue";
import newEditor from "../SlaDownConfig/newEditor.vue";

/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";

import { getSlaDownConfigByPage, getNewSlaDownConfigByPage, DelNewSlaDownConfig, DelSlaDownConfig, DelSlaConfig, EnableSlaConfig, SlaConfigStatus, type SlaConfigList as DataItem } from "@/views/pages/apis/SlaConfig";
import { getMaxListeners } from "process";

import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", {
    type: "error",
  });
  throw new Error("Component context initialization failed!");
}
import { getGroupList as getData } from "@/api/personnel";
import { getAlarmMerge, setAlarmMerge } from "@/views/pages/apis/alarmMerge";
import { getOrderGroup } from "@/views/pages/apis/orderGroup";
import { AddSlaDownConfig, SlaDownConfigEdit, AddAllNewSlaDownConfig } from "@/views/pages/apis/SlaConfig";

import getUserInfo from "@/utils/getUserInfo";
import { getSystemVersion } from "@/api/system";
import { el } from "element-plus/es/locale";

const userInfo = getUserInfo();
const systemEditionOption = ref(new Map<string, string>());
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));
const expands = ref<string[]>([]);
// const autoEvent = ref(false);
const visible = ref(false);
const checkVisible = ref(false);
const alarmdialog = ref(false);
const form = reactive({
  autoEvent: false, //告警自动生成工单
  // teamId: "-1", //用户组id,工单分配到组,-1为不设置
  defaultRule: false, //使用工单组默认的自动工单规则
  sustainTime: 0, //工单锁定持续工单,0为不设置,单位是秒
  alertMerge: "NOT_SET", //工单锁定时间内，自动工单
  mergeEvent: false, //自动工单到已生成的工单中
  alertCollect: false, //告警采集
  alertBoardPush: false, // 告警推送至告警板
  topologyAlertMerge: "SAME_DEVICE", // 拓扑告警归并到同一工单
  topologyMergeEvent: false, // 拓扑告警归并到已生成的工单中
  trapMergeEvent: false, // Snmp trap拓扑告警归并
  subDeviceMergeEvent: false, // 子设备拓扑告警始终归并

  ticketGroupId: "", // 工单组ID
  ticketGroupName: "", // 工单组名称

  ticketDefalu: false, // 使用工单组优先级策略
});
const helpType = ref("");
const eventGroup = reactive([
  {
    label: "不设置",
    value: -1,
  },
]);
const paging = reactive({
  pageNumber: 1,
  pageSize: 50,
  total: 0,
});
const tableLoading = ref(false);

// const $filter0 = ref([
//   { text: "包含", value: "include" },
//   { text: "不包含", value: "exclude" },
//   { text: "等于", value: "eq" },
//   { text: "不等于", value: "ne" },
// ]);
const $filter0 = ref(exoprtMatch1);

const searchForm = ref<Record<string, any>>({
  eqName: [],
  includeName: [],
  nameFilterRelation: "AND",
  neName: [],
  excludeName: [],

  eqDescription: [],
  includeDescription: [],
  descriptionFilterRelation: "AND",
  neDescription: [],
  excludeDescription: [],

  defaultable: "",
});

const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByName) === "include") value0 = searchForm.value.includeName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = searchForm.value.excludeName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = searchForm.value.eqName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = searchForm.value.neName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = searchForm.value.includeName[searchForm.value.includeName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = searchForm.value.excludeName[searchForm.value.excludeName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = searchForm.value.eqName[searchForm.value.eqName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = searchForm.value.neName[searchForm.value.neName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: searchForm.value.nameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.nameFilterRelation = v.relation;
    searchForm.value.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByDescription = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByDescription) === "include") value0 = searchForm.value.includeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "exclude") value0 = searchForm.value.excludeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "eq") value0 = searchForm.value.eqDescription[0] || "";
    if (toValue(searchType0ByDescription) === "ne") value0 = searchForm.value.neDescription[0] || "";
    let value1 = "";
    if (toValue(searchType1ByDescription) === "include") value1 = searchForm.value.includeDescription[searchForm.value.includeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "exclude") value1 = searchForm.value.excludeDescription[searchForm.value.excludeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "eq") value1 = searchForm.value.eqDescription[searchForm.value.eqDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "ne") value1 = searchForm.value.neDescription[searchForm.value.neDescription.length - 1] || "";
    return {
      type0: toValue(searchType0ByDescription),
      type1: toValue(searchType1ByDescription),
      relation: searchForm.value.descriptionFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByDescription.value = v.type0 as typeof searchType0ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchType1ByDescription.value = v.type1 as typeof searchType1ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.descriptionFilterRelation = v.relation;
    searchForm.value.includeDescription = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeDescription = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqDescription = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neDescription = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// 搜索关键字
const ServiceSearch = ref("");
const degradeStatus = ref(null);
const tableData = ref<DataItem[]>([]);
// const checked1 = ref(false);
// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  getSlaDownList();
}
function beforeMount() {
  // runningInit();
}
function mounted() {
  // handleGetOrderGroup();
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */
// runningInit();
// async function runningInit(): Promise<void> {
//   const { success, message, data } = await getSystemVersion({});
//   if (!success) throw Object.assign(new Error(message), { success, data });
//   for (let i = 0; i < data.length; i++) {
//     systemEditionOption.value.set(data[i].code, data[i].name);
//   }
//   // console.log(systemEditionLabel, 666666);
//   if (systemEditionLabel.value === "标准版") {
//     form.alertCollect = true;
//     form.autoEvent = true;
//   }
// }
// const systemEditionLabel = computed(() => {
//   return systemEditionOption.value.get((userInfo.currentTenant || ({ systemEdition: "" } as Partial<import("@/api/system").TenantItem>)).systemEdition || "");
// });
//获取列表
// function getList() {
//   getSupport_notesList({ active: true }).then((res) => {
//     if (res.success) {
//       tableData.value = [...res.data];
//       paging.total = res.data.length;
//     } else {
//       ElMessage.error(JSON.parse(res.data)?.message);
//     }
//   });
// }
function getRowKeys(row) {
  return row.id;
}
async function handleExpandChange(row, expandedRows) {
  if (expandedRows.length) {
    //展开
    expands.value = [];
    if (row) {
      expands.value.push(row.id);
    }
  } else {
    expands.value = [];
  }
}
function getSlaDownList() {
  let params = {
    ...searchForm.value,
    ...paging,
    degradeName: ServiceSearch.value,
    degradeStatus: degradeStatus.value,
  };
  tableLoading.value = true;
  getNewSlaDownConfigByPage(params)
    .then(({ data }) => {
      if (data.success) {
        let arr = [];
        data.data.forEach((v, i) => {
          if (v.defaultRule == 1 || v.defaultRule == null) {
            arr.push({ ...v, defaultRule: true });
          } else {
            arr.push({ ...v, defaultRule: false });
          }
        });
        //  let arr = [...data];
        let newArr = [];
        // // console.log()

        if (degradeStatus.value == null || degradeStatus.value === "") {
          tableData.value = arr;
        } else {
          // console.log(degradeStatus.value);
          if (degradeStatus.value) {
            arr.forEach((v, i) => {
              if (v.degradeStatus) {
                newArr.push(v);
              }
            });
            tableData.value = newArr;
          }
          if (degradeStatus.value == false) {
            arr.forEach((v, i) => {
              // console.log(v);
              if (v.degradeStatus == degradeStatus.value) {
                newArr.push(v);
              }
            });
            tableData.value = newArr;
          }
        }
        // // console.log(arr, 55555);

        tableLoading.value = false;
        paging.total = Number(data.total);
        paging.pageNumber = data.page;
        paging.pageSize = data.size;
      }
    })
    .catch(() => {
      tableLoading.value = false;
    });
}
//新增
function deviceDialog(type, row) {
  ctx.refs.alarmnotes.type = type;
  if (type === "add") {
    ctx.refs.alarmnotes.title = "新增策略";
  } else {
    ctx.refs.alarmnotes.form = {
      degradeName: row.degradeName,
      degradeDesc: row.degradeDesc,
      defaultable: row.defaultable,
      id: row.id,
    };
    ctx.refs.alarmnotes.title = "编辑策略";
  }
  alarmdialog.value = true;
}
//取消
function dialogClose(bool) {
  alarmdialog.value = bool;
  getSlaDownList();
}
//删除降级策略
function SlaDownConfigDelete(row: Partial<Record<string, any>>) {
  if (row.status) {
    ElMessage.warning("启用状态下无法删除，请更改数据状态");
  } else
    ElMessageBox.confirm("此操作将永久删除该策略, 是否继续?", "删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        // // tableData.value.length = tableData.value.length - 1;
        let params = {
          id: row.id,
        };
        DelNewSlaDownConfig(params)
          .then(({ success, data, message }) => {
            if (!success) throw new Error(message);
            ElMessage.success("操作成功");

            if (tableData.value.slice((paging.pageNumber - 1) * paging.pageSize, paging.pageNumber * paging.pageSize).length == 0) {
              paging.pageNumber = 1;
            }
            getSlaDownList();
          })
          .catch((e) => {
            ElMessage.error(e.message);
            tableLoading.value = false;
          });
      })
      .catch(() => {
        // ElMessage({
        //   type: "info",
        //   message: "已取消删除",
        // });
      });
}
function changeCheck(val) {
  if (val) {
    checkVisible.value = true;
    form.mergeEvent = true; //使用工单组默认的自动工单规则
    form.sustainTime = 15; //工单锁定持续工单,0为不设置,单位是秒
    form.alertMerge = "SAME_DEVICE"; //工单锁定时间内，自动工单
    form.topologyAlertMerge = "SAME_DEVICE"; //工单锁定时间内，自动工单
    form.topologyMergeEvent = true; //自动工单到已生成的工单中
    form.subDeviceMergeEvent = true; //自动工单到已生成的工单中
  } else {
    form.mergeEvent = false; //使用工单组默认的自动工单规则
    form.sustainTime = 0; //工单锁定持续工单,0为不设置,单位是秒
    form.alertMerge = "NOT_SET"; //工单锁定时间内，自动工单
    form.topologyAlertMerge = "NOT_SET"; //工单锁定时间内，自动工单
    form.topologyMergeEvent = false; //自动工单到已生成的工单中
    form.subDeviceMergeEvent = false; //自动工单到已生成的工单中
  }
}
function help(type: number) {
  // // console.log(type);
  // eslint-disable-next-line no-const-assign
  helpType.value = type;
  visible.value = true;
}
async function getItem() {
  getAlarmMerge({})
    // 111111111111111111111
    .then(async (res) => {
      await handleGetOrderGroup();
      // // console.log(res);
      if (res.success) {
        // const form = reactive({
        // form.teamId = res.data.teamId == "-1" ? res.data.teamId * 1 : res.data.teamId; //用户组id工单分配到组-1为不设置
        form.sustainTime = res.data.sustainTime * 1; //工单锁定持续工单0为不设置单位是秒
        form.alertMerge = res.data.alertMerge; //工单锁定时间内，自动工单
        form.autoEvent = res.data.autoEvent; //告警自动生成工单
        form.mergeEvent = res.data.mergeExists; //自动工单到已生成的工单中
        form.defaultRule = res.data.defaultRule; //使用工单组默认的自动工单规则
        form.alertCollect = res.data.alertCollect || false;
        form.alertBoardPush = res.data.alertBoardPush; //告警推送到告警板
        form.topologyAlertMerge = res.data.topologyAlertMerge; // 拓扑告警归并到同一工单
        form.topologyMergeEvent = res.data.topologyMergeEvent; // 拓扑告警归并到已生成的工单中
        form.trapMergeEvent = res.data.trapMergeEvent; // Snmp trap拓扑告警归并
        form.subDeviceMergeEvent = res.data.subDeviceMergeEvent; // 子设备拓扑告警始终归并
        form.ticketGroupId = res.data.ticketGroupId || "";
        form.ticketGroupId = userInfo.hasPermission(服务管理中心_工单组_可读) ? (orderGroupOption.value.some((item) => item.id === form.ticketGroupId) ? form.ticketGroupId : "") : "";
        form.ticketGroupName = res.data.ticketGroupName;
        form.ticketDefalu = res.data.ticketDefalu;
        // });
      }
    })
    .catch((err) => {
      ElMessage.error(err.message);
    });
}
async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  getData({ external: false }).then((res) => {
    // // console.log(res);
    if (res.success) {
      res.data.forEach((item) => {
        eventGroup.push({
          label: item.name,
          value: item.id,
        });
      });
    }
  });
}

const orderGroupOption = ref([]);
async function handleGetOrderGroup() {
  const { data, message, success } = await getOrderGroup({ pageNumber: 1, pageSize: 99999 });
  if (!success) throw new Error(message);
  orderGroupOption.value = data;
}

function submit() {
  // // console.log(form);

  if (form.autoEvent && !form.ticketGroupId) return ElMessage.warning("请选择工单组");

  setAlarmMerge({ ...form })
    .then((res) => {
      // // console.log(res);
      if (res.success) {
        ElMessage.success("操作成功");
        getItem();
      }
    })
    .catch((err) => {
      ElMessage.error(err.message);
    });
}

function autoEventChange(val: any) {
  // // console.log(val);
  if (!val) {
    // autoEvent: false, //告警自动生成工单
    // form.teamId = -1; //用户组id,工单分配到组,-1为不设置
    form.defaultRule = false; //使用工单组默认的自动工单规则
    form.sustainTime = 0; //工单锁定持续工单,0为不设置,单位是秒
    form.alertMerge = "NOT_SET"; //工单锁定时间内，自动工单
    form.mergeEvent = false; //自动工单到已生成的工单中
    form.ticketGroupId = ""; // 工单组
  }
}
function confirm(val: any) {
  // console.log(val);
  if (val.type == "add") {
    AddAllNewSlaDownConfig({ ...val.data, defaultRule: false })
      .then((res: any) => {
        if (res.success) {
          ElMessage.success("操作成功");
          getSlaDownList();
        } else {
          ElMessage.error(JSON.parse(res.data)?.message);
        }
      })
      .catch((e) => {
        if (e instanceof Error) ElMessage.error(e);
      })
      .finally(() => {});
  } else {
    SlaDownConfigEdit({ ...val.data, defaultRule: false })
      .then((res: any) => {
        if (res.success) {
          ElMessage.success("操作成功");
          getSlaDownList();
        } else {
          ElMessage.error(JSON.parse(res.data)?.message);
        }
      })
      .catch((e) => {
        if (e instanceof Error) ElMessage.error(e);
      });
  }
}
/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate(querysItem(), getItem());
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.text {
  > p {
    padding: 5px;
    box-sizing: border-box;
  }
}
.el-row {
  .el-col {
    display: flex;
    align-items: center;
  }
}
.alarm-collapse {
  display: flex;
  align-items: center;
  height: 60px;
  background: rgb(232, 232, 232);
  h2 {
    display: flex;
    align-items: center;
    height: 100%;
    font-size: 16px;
    padding-left: 20px;
    box-sizing: border-box;
  }
}
.my-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.confirm {
  padding: 20px 30px;
  box-sizing: border-box;
}
.alarm-merge-message {
  height: 0px;
  overflow: hidden;
  > div {
    padding: 8px 30px;
    box-sizing: border-box;
  }
}
.alarm-merge-message-expand {
  transition: all 1s;
  // background: red;
  overflow: hidden;
  height: 880px;
  // display: none;
}
.alarm-merge-message-default {
  // height: 500px;
  transition: all 0.5s;

  // transition: max-height 2s ease-out;
}
.alram-merge-config {
  padding: 20px;
  box-sizing: border-box;
  h2 {
    display: flex;
    align-items: center;
    height: 60px;
    font-size: 14px;
    padding-left: 20px;
    box-sizing: border-box;
    background: rgb(232, 232, 232);
    border: 1px solid #ccc;
    border-bottom: none;
  }
  > div {
    border: 1px solid #ccc;
    border-top: none;
    min-height: 300px;
    padding: 10px;
    box-sizing: border-box;
    .dobule {
      border: 1px solid #ccc;
      // border-top: none;
      // min-height: 300px;
      padding: 10px;
      box-sizing: border-box;
      margin-block: 30px;
      .el-row {
        margin: 10px 0;
      }
    }
  }
}
</style>
