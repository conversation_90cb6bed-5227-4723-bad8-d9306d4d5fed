<template>
  <el-card :body-style="{ padding: '20px', height: `${props.height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="props.height - 40" @size-change="getRefreshTable()" @current-change="getRefreshTable()">
      <template #left>
        <el-input class="tw-w-[300px]" v-model="searchForm.name" placeholder="请输入邮件模板名称" @keyup.enter="getSearchData()" clearable>
          <template #append>
            <el-button :icon="Search" @click="getSearchData()" />
          </template>
        </el-input>
      </template>
      <template #right>
        <span class="tw-h-fit">
          <el-button v-if="userInfo.hasPermission(PERMISSION.group515414099828408320.group518667475873693696.create)" type="primary" :icon="Plus" @click="handleCreateTemplate()">{{ $t("glob.add") }}模板</el-button>
        </span>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table v-loading="loading" stripe :data="tableData" :height="tableHeight" style="width: 100%">
          <el-table-column prop="name" label="模板名称" align="left" />
          <!-- <el-table-column  prop="desc" label="邮件模板描述" align="left" /> -->
          <el-table-column align="left" prop="enable" label="使用状态" :width="80" :formatter="(_row, _col, v) => h(ElText, { type: v ? 'success' : 'danger' }, () => (v ? $t('glob.Enable') : $t('glob.Disable')))"></el-table-column>
          <!-- <el-table-column  align="left" prop="createdBy" label="创建人" :formatter="(_row, _col, v) => JSON.parse(v)?.username || '--'" /> -->

          <el-table-column prop="name" label="触发条件" align="left">
            <template #default="{ row }">
              <div>
                {{ row.generateEvent ? "生成事件" : "" }}
                {{ row.generateEvent && row.eventOperations.length > 0 ? "、" : "" }}
                {{ row.eventOperations.length > 0 ? "处理事件" : "" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" prop="createdTime" label="创建时间" :formatter="(_row, _col, v) => (v ? moment(v, 'x').format('yyyy-MM-DD HH:mm:ss') : '--')"></el-table-column>
          <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="126">
            <template #default="{ row }">
              <span class="tw-h-fit tw-align-middle">
                <el-link v-if="userInfo.hasPermission(PERMISSION.group515414099828408320.group518667475873693696.editor)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleEditTemplate(row as DataItem)">{{ $t("glob.edit") }}</el-link>
              </span>
              <span class="tw-h-fit tw-align-middle">
                <el-link v-if="userInfo.hasPermission(PERMISSION.group515414099828408320.group518667475873693696.editor)" :type="row.enable ? 'danger' : 'primary'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleEnableChange(row as DataItem)">{{ row.enable ? $t("glob.Disable") : $t("glob.Enable") }}</el-link>
              </span>
              <span class="tw-h-fit tw-align-middle" v-show="!row.enable">
                <el-link v-if="userInfo.hasPermission(PERMISSION.group515414099828408320.group518667475873693696.remove)" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleDelMailTemplate(row as DataItem)">{{ $t("glob.delete") }}</el-link>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
  <Editor ref="editorRef" title="模板" @confirm="getRefreshTable()"></Editor>
</template>

<script setup lang="ts" generic="T extends object">
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

import { useSiteConfig } from "@/stores/siteConfig";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMessageBox, type TableColumnCtx, ElText } from "element-plus";
import pageTemplate from "@/components/pageTemplate.vue";
import Editor from "./mailTemplateCreate.vue";

/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";

import { getEmailTemplate, enableAndDisableEmailTemplate, delEmailTemplate, type EmailTemplate as DataItem } from "@/views/pages/apis/NoticeTemplate";
import { column } from "element-plus/es/components/table-v2/src/common";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "SlaDownConfig" });
const editorRef = ref<InstanceType<typeof Editor>>();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */

const siteConfig = useSiteConfig();
const userInfo = getUserInfo();

interface Props {
  height: number;
  width: number;
}
const props = withDefaults(defineProps<Props>(), { height: 0, width: 0 });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);

const tableLoading = ref(false);

// 搜索关键字
const searchForm = ref<Record<string, string>>({ name: "" });
const tableData = ref<DataItem[]>([]);
const paging = reactive({
  pageNumber: 1,
  pageSize: 10,
  total: 0,
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  getSearchData();
}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
//搜索
function getSearchData() {
  paging.pageNumber = 1;

  getRefreshTable();
}
function handleDelMailTemplate({ id }: DataItem) {
  ElMessageBox.confirm(`确定删除该邮件模板?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      const params = { id };
      delEmailTemplate(params)
        .then(({ success, message, data }) => {
          if (!success) throw new Error(message);
          ElMessage.success("操作成功");
          getRefreshTable();
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        });
    })
    .catch(() => {
      /*...code*/
    });
}
function handleEnableChange({ id, enable }: DataItem) {
  ElMessageBox.confirm(`确定${enable ? "禁用" : "启用"}该邮件模板?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      const params = { id, type: enable ? "disable" : "enable" };
      enableAndDisableEmailTemplate(params)
        .then(({ success, message, data }) => {
          if (!success) throw new Error(message);
          ElMessage.success("操作成功");
          getRefreshTable();
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        });
    })
    .catch(() => {
      /*...code*/
    });
}
async function handleEditTemplate(v: Partial<DataItem>) {
  // console.log(ctx?.refs.editorRef);
  // console.log(editorRef.value);

  editorRef.value?.open(v.id);
  // try {
  //   if (!editorRef.value) return;
  //   await editorRef.value.open(v, async (form) => {
  //     return true;
  //   });
  // } catch (error) {
  //   /*  */
  // } finally {
  //   /*  */
  // }
  // router.push(`/mailTemplate/edit/${v.id}`);
}
async function handleCreateTemplate() {
  editorRef.value?.open();
  // console.log(editorRef.value?.$refs);
  editorRef.value?.$refs.form && editorRef.value?.$refs.form.resetFields();

  // try {
  //   if (!editorRef.value) return;
  //   await editorRef.value.open({}, async (form) => {
  //     return true;
  //   });
  // } catch (error) {
  //   /*  */
  // } finally {
  //   /*  */
  // }
  // router.push(`/mailTemplate/create`);
}
function getRefreshTable() {
  loading.value = true;
  const params = {
    pageNumber: paging.pageNumber,
    pageSize: paging.pageSize,
    name: searchForm.value.name,
  };
  getEmailTemplate(params)
    .then(({ success, message, data, total }) => {
      // console.log(success, data);
      if (!success) throw new Error(message);
      tableData.value = data;
      paging.total = Number(total);
      if (!tableData.value.length && paging.pageNumber !== 1) {
        paging.pageNumber = 1;
        getRefreshTable();
      }
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {
      loading.value = false;
    });
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
