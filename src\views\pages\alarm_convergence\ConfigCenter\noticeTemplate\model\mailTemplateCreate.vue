<template>
  <el-drawer class="el-card tw-bottom-[16px] tw-right-[16px] tw-top-[120px] tw-h-auto" v-model="drawer" title="I am the title" :direction="'rtl'" :before-close="handleClose" :size="width" :close-on-click-modal="false" :show-close="false" :modal="false">
    <template #header>
      <el-page-header @back="handleClose" :content="`${isEdit ? '编辑' : '新增'}邮件模板`"></el-page-header>
    </template>
    <el-scrollbar :height="height - 115">
      <el-form ref="form" :model="form" label-width="120px" :rules="rules">
        <el-card class="el-card-mt">
          <template #header>
            <div class="modules-item">
              <span class="modules-title">基础信息</span>
            </div>
          </template>
          <el-row>
            <el-col :span="24">
              <el-form-item label="模板名称" prop="name">
                <el-input v-model="form.name" :style="basicClassInput" placeholder="请输入邮件模板名称"></el-input>
              </el-form-item>
              <el-form-item label="模板描述" prop="desc">
                <el-input v-model="form.desc" type="textarea" :autosize="{ minRows: 4, maxRows: 8 }" placeholder="请输入邮件模板描述" :style="basicClassInput"> </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        <el-card class="el-card-mt">
          <template #header>
            <div class="modules-item">
              <span class="modules-title">内容配置</span>
            </div>
          </template>
          <el-row>
            <el-col :span="24">
              <el-form-item label="收件人" prop="recipients">
                <el-checkbox-group v-model="form.recipients">
                  <el-checkbox v-for="(value, key) in recipientsEnum" :key="`recipient-${key}`" :label="key">{{ value }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="抄送人" prop="carbonCopies">
                <div class="tw-w-full">
                  <el-checkbox-group v-model="form.carbonCopies" @change="ccChange">
                    <el-checkbox v-for="(value, key) in makeacopyfor" :key="`recipient-${key}`" :label="key">{{ value }}</el-checkbox>
                  </el-checkbox-group>
                  <el-input v-show="showInput" v-model="form.cc" placeholder="本邮箱将抄送每一封邮件，用于验证邮件是否发送成功，多个邮箱用;分隔" :style="basicClassInput" />
                </div>
              </el-form-item>
              <el-form-item label="触发条件">
                <el-checkbox v-model="form.generateEvent">新建事件</el-checkbox>
                <el-checkbox v-model="form.handleEvent">处理事件</el-checkbox>
                <el-row class="tw-ml-[18px]" :gutter="20" v-if="form.handleEvent">
                  <el-col :span="12">
                    <el-cascader v-model="form.eventOperations" :options="priorityEnum" :props="{ multiple: true }" :collapse-tags="false" class="tw-w-full"></el-cascader>
                  </el-col>
                  <el-col :span="12">
                    <el-cascader v-model="form.noteOperations" :options="noteOperation" :props="{ multiple: true }" :collapse-tags="false" class="tw-w-full"></el-cascader>
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item label="邮件主题" prop="subjectKeys">
                <div class="tw-w-full">
                  <el-checkbox v-model="item.state" v-for="item in subjectKeys" :key="`subjectKeys-${item.value}`" :true-label="item.value" :false-label="`cancel:${item.value}`" :label="item.value" @change="handleAddField">{{ item.label }}</el-checkbox>
                  <br />
                  <el-input type="textarea" ref="inputRef" :autosize="{ minRows: 4, maxRows: 8 }" placeholder="请输入内容" v-model="form.subjectText" :style="basicClassInput" @blur="inputBlur"> </el-input>
                </div>
              </el-form-item>
              <el-form-item label="邮件模板" prop="templateUrl">
                <el-upload :style="{ width: basicClassInput.width }" drag action :auto-upload="false" multiple :limit="1" :on-change="handleFileChange" :file-list="fileList">
                  <el-icon><el-icon-upload /></el-icon>
                  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :style="{ textAlign: 'center', marginTop: '10px' }">
              <el-button type="primary" @click="submitForm">提 交</el-button>
              <el-button @click="handleClose">取 消</el-button>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
    </el-scrollbar>
  </el-drawer>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { Upload as ElIconUpload } from "@element-plus/icons-vue";
import { recipientsEnum, noteOperation, subjectKeys, operation, makeacopyfor } from "./common";
import priorityEnum from "@/views/pages/common/priority";
import { createEmailTemplate, getEmailTemplateInfo, editEmailTemplate, uploadFile } from "@/views/pages/apis/NoticeTemplate";
export default {
  components: {
    ElIconUpload,
  },
  inject: ["width", "height"],
  emits: ["confirm"],
  data() {
    return {
      recipientsEnum,
      makeacopyfor,
      subjectKeys,
      priorityEnum: [],
      noteOperation: [],
      basicClassInput: { width: "35.8vw" } /* 输入框选择器基本样式 */,
      form: {
        recipients: [],
        eventOperations: [],
        noteOperations: [],
        subjectKeys: [],
        carbonCopies: [],
        subjectText: "",
        generateEvent: false,
        handleEvent: false,
        cc: "",
      },
      options: [],
      eventOperationsProp: {},
      textCursor: 0,
      currentFile: [],
      fileList: [],
      drawer: false,
      showInput: false,
    };
  },
  computed: {
    // isEdit() {
    //   return !!this.form.id;
    // },
    rules() {
      return {
        name: [
          {
            required: true,
            message: "请输入邮件模板名称",
            trigger: ["blur"],
          },
        ],
        recipients: [
          {
            type: "array",
            required: true,
            message: "收件人必选",
            trigger: "change",
          },
        ],
        // carbonCopies: [
        //   {
        //     required: true,
        //     validator: (rule, value, callback) => {
        //       if (this.form.carbonCopies && this.form.carbonCopies.length) callback();
        //       else callback(new Error("抄送人必选"));
        //     },
        //     trigger: ["blur", "change"],
        //   },
        // ],
        subjectKeys: [
          {
            required: true,
            validator: (rule, value, callback) => {
              const flag = [];
              this.subjectKeys.forEach((v) => {
                if (v.state && !v.state.includes("cancel:")) {
                  flag.push(v.value);
                }
              });
              // // console.log(flag);
              if (!flag) callback(new Error("邮件主题必选"));
              else if (!this.form.subjectText) callback(new Error("请输入模板内容"));
              else callback();
            },
            trigger: "change",
          },
        ],
        templateUrl: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (this.currentFile.keyName) callback();
              else callback(new Error("请上传邮件模板"));
            },
            trigger: "change",
          },
        ],
      };
    },
  },

  created() {},
  methods: {
    open(id?: string) {
      // // console.log(id, 55555);
      // this.$nextTick(() => {
      //   this.$refs.form.resetFields();
      // });
      this.drawer = true;
      this.priorityEnum = this.setEventOperations();
      this.noteOperation = this.setNoteOperation();

      this.fileList = [];
      this.form.cc = "";

      if (id) {
        this.isEdit = true;
        this.form.id = id;
        // this.$nextTick(() => {
        this.getDetail(id);
        this.subjectKeys = this.setSubjectKeys();
        // });
      } else {
        this.isEdit = false;
        this.$nextTick(() => {
          this.form.noteOperations = [];
          this.form.generateEvent = false;
          this.form.handleEvent = false;
          this.$refs.form.resetFields();
          this.$refs.form.clearValidate();
          this.form.subjectText = "";
          this.subjectKeys = this.setSubjectKeysNot();
        });
      }
      this.$nextTick(() => {
        this.$refs.form.resetFields();
        this.$refs.form.clearValidate();
        this.$refs.form.validateField("subjectKeys", (valid) => {
          return this.$refs["form"].clearValidate("subjectKeys");
        });
        this.$refs.form.validateField("recipients", (valid) => {
          return this.$refs["form"].clearValidate("recipients");
        });
      });
    },
    ccChange(val, list) {
      const bool = this.form.carbonCopies.indexOf("APPOINT_CONTACT");

      if (bool != -1) {
        this.showInput = true;
      } else {
        this.showInput = false;
      }
    },
    getDetail(id) {
      getEmailTemplateInfo({ id }).then(({ success, data }) => {
        if (success) {
          let form = {
            id: data.id,
            name: data.name,
            desc: data.desc,
            recipients: data.recipients, // 收件人
            carbonCopies: data.carbonCopies, // 抄送人
            generateEvent: data.generateEvent, // 生成事件
            subjectText: data.subjectText,
            cc: data.cc,
          };
          this.subjectKeys = this.setSubjectKeys();
          if ((data.eventOperations && data.eventOperations.length) || (data.noteOperations && data.noteOperations.length)) form.handleEvent = true; // 处理事件
          form.eventOperations = data.eventOperations.map((v) => [v.priority, v.operation]);
          // console.log(form.eventOperations);
          form.noteOperations = data.noteOperations.map((v) => [v.priority, v.operation]);
          data.subjectKeys.forEach((v) => {
            const enumItem = this.subjectKeys.find((item) => item.value === v);
            enumItem.state = enumItem.state.substring(enumItem.state.indexOf(":") + 1);
          });
          this.fileList = [
            {
              name: data.templateUrl.keyName,
              url: `/${data.templateUrl.bucketName}/${data.templateUrl.keyName}`,
            },
          ];
          this.currentFile = data.templateUrl;
          this.form = form;
        } else this.$message.error(JSON.parse(data)?.message || "邮件模板详情获取失败");
      });
    },
    getParams() {
      let index = this.form.carbonCopies.indexOf("APPOINT_CONTACT");
      let carbonCopies = [...this.form.carbonCopies];
      if (index > -1) {
        carbonCopies.splice(index, 1);
      }
      const params = {
        name: this.form.name,
        desc: this.form.desc,
        recipients: this.form.recipients,
        carbonCopies,
        generateEvent: this.form.generateEvent,
        subjectKeys: [],
        subjectText: this.form.subjectText,
        templateUrl: this.currentFile,
        cc: this.form.cc,
      };
      if (this.form.handleEvent) {
        params.eventOperations = this.form.eventOperations.map((v) => {
          return { priority: v[0], operation: v[1] };
        });
        params.noteOperations = this.form.noteOperations.map((v) => {
          return { priority: v[0], operation: v[1] };
        });
      }
      this.subjectKeys.forEach((v) => {
        if (v.state && !v.state.includes("cancel:")) {
          params.subjectKeys.push(v.value);
        }
      });

      if (this.isEdit) params.id = this.form.id;
      return params;
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) return false;
        const api = { createEmailTemplate, editEmailTemplate };

        api[this.isEdit ? "editEmailTemplate" : "createEmailTemplate"](this.getParams())
          .then(({ success, data }) => {
            if (success) {
              this.$message.success(`邮件模板${this.isEdit ? "编辑" : "创建"}成功!`);
              this.$refs.form.resetFields();
              this.$refs.form.clearValidate();
              this.handleClose();
            } else this.$message.error(JSON.parse(data)?.message || `邮件模板${this.isEdit ? "编辑" : "创建"}失败`);
          })
          .catch((err) => {
            this.$message.error(err?.message);
          });
      });
    },
    handleFileChange(file) {
      let formData = new FormData();
      formData.append("file", file.raw);
      uploadFile(formData).then(({ success, data }) => {
        if (success) {
          this.$message.success("模板上传成功");
          this.currentFile = data;
        } else {
          this.$message.error(JSON.parse(data)?.message || "模板上传失败");
          this.currentFile = {};
          this.fileList = [];
        }
      });
    },
    inputBlur() {
      this.textCursor = this.$refs.inputRef.$el.children[0].selectionStart;
    },
    handleAddField(v) {
      if (!v.includes("cancel:")) {
        this.form.subjectText = this.form.subjectText.slice(0, this.textCursor) + `{{${v}}}` + this.form.subjectText.slice(this.textCursor);
      } else {
        String.prototype.replaceAll = function (f, e) {
          var reg = new RegExp(f, "g");
          return this.replace(reg, e);
        };
        this.form.subjectText = this.form.subjectText.replaceAll(`{{${v.substring(v.indexOf(":") + 1)}}}`, ``);
        String.prototype.replaceAll = null;
      }
    },
    setSubjectKeys() {
      if (this.subjectKeys instanceof Array) return this.subjectKeys;
      let result = [];

      for (let key in this.subjectKeys) {
        result.push({
          label: subjectKeys[key],
          value: key,
          state: this.isEdit ? (this.form.subjectText.includes(`{{${key}}}`) ? key : `cancel:${key}`) : `cancel:${key}`,
        });
      }

      return result;
    },
    setSubjectKeysNot() {
      // let data = this.setSubjectKeys();
      // // console.log(data, subjectKeys);
      // if (this.subjectKeys instanceof Array) return this.subjectKeys;
      let result = [];

      for (let key in subjectKeys) {
        result.push({
          label: subjectKeys[key],
          value: key,
          state: `cancel:${key}`,
        });
      }

      return result;
    },
    setNoteOperation() {
      let result = [];
      for (let key in priorityEnum) {
        let obj = {
          value: key,
          label: key,
          children: [],
        };
        for (let key2 in noteOperation) {
          obj.children.push({
            label: noteOperation[key2],
            value: key2,
          });
        }
        result.push(obj);
      }
      return result;
    },
    setEventOperations() {
      let result = [];
      for (let key in priorityEnum) {
        result.push({
          value: key,
          label: key,
          children: Object.keys(operation).map((v) => {
            return { value: v, label: operation[v] };
          }),
        });
      }
      return result;
    },
    handleClose /* 返回上一页 */(done) {
      this.$refs.form.resetFields();
      this.$refs.form.clearValidate();
      this.$emit("confirm");
      if (done instanceof Function) done();
      else this.drawer = false;
      // this.$router.replace({ path: "/noticeTemplate" });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/theme/common/var.scss";
// deep() .elstyle-card {
//   margin-top: 20px;
// }
.modules-item {
  .modules-title {
    color: $color-primary;
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}
</style>
