<template>
  <el-drawer class="el-card tw-bottom-[16px] tw-right-[16px] tw-top-[120px] tw-h-auto" v-model="drawer" :title="`${isEdit ? '编辑' : '新增'}短信模板`" :direction="'rtl'" :before-close="handleClose" :size="width" :close-on-click-modal="false" :show-close="false" :modal="false">
    <template #header>
      <el-page-header @back="handleClose" :content="`${isEdit ? '编辑' : '新增'}短信模板`"></el-page-header>
    </template>
    <el-scrollbar :height="height - 115">
      <el-form ref="form" :model="form" label-width="120px" :rules="rules">
        <el-card class="el-card-mt">
          <template #header>
            <div class="modules-item">
              <span class="modules-title">基础信息</span>
            </div>
          </template>
          <el-row>
            <el-col :span="24">
              <el-form-item label="模板名称" prop="name">
                <el-input v-model="form.name" :style="basicClassInput" placeholder="请输入短信模板名称"></el-input>
              </el-form-item>
              <el-form-item label="模板描述" prop="desc">
                <el-input v-model="form.desc" type="textarea" :autosize="{ minRows: 4, maxRows: 8 }" placeholder="请输入短信模板描述" :style="basicClassInput"> </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        <el-card class="el-card-mt">
          <template #header>
            <div class="modules-item">
              <span class="modules-title">内容配置</span>
            </div>
          </template>
          <el-row>
            <el-col :span="24">
              <el-form-item label="收件人" prop="recipients">
                <div>
                  <div class="tw-my-2.5 tw-flex">
                    <el-select v-model="form.deviceId" class="m-2 tw-mr-2.5" placeholder="请选择设备"> <el-option v-for="item in [{ value: '-1', label: '全部设备' }, ...devices]" :key="item.value" :label="item.label" :value="item.value" /> </el-select>
                    <el-checkbox-group v-model="form.deviceContacts">
                      <el-checkbox v-for="(value, key) in recipientsEnum" :key="`recipient-${key}`" :label="key">{{ value }}</el-checkbox>
                    </el-checkbox-group>
                  </div>
                  <div class="tw-my-2.5 tw-flex">
                    <el-cascader class="m-2 tw-mr-2.5" v-model="form.regionId" :options="[{ children: [], id: '-1', name: '全部区域' }, ...regions]" :props="{ checkStrictly: true, emitPath: true, children: 'children', value: 'id', label: 'name' }" clearable />
                    <el-checkbox-group v-model="form.regionContacts">
                      <el-checkbox v-for="(value, key) in recipientsEnum" :key="`recipient-${key}`" :label="key">{{ value }}</el-checkbox>
                    </el-checkbox-group>
                  </div>
                  <div class="tw-my-2.5 tw-flex">
                    <el-select v-model="form.locationId" class="m-2 tw-mr-2.5" placeholder="请选择场所">
                      <el-option v-for="item in [{ value: '-1', label: '全部场所' }, ...locations]" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                    <el-checkbox-group v-model="form.locationContacts">
                      <el-checkbox v-for="(value, key) in recipientsEnum" :key="`recipient-${key}`" :label="key">{{ value }}</el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="触发条件">
                <el-checkbox v-model="form.createEvent">新建事件</el-checkbox>
                <el-checkbox v-model="form.handleEvent">处理事件</el-checkbox>
                <el-row class="tw-ml-[18px]" :gutter="20" v-if="form.handleEvent">
                  <el-col :span="12">
                    <el-cascader v-model="form.eventOperations" :options="priorityEnum" :props="{ multiple: true }" :collapse-tags="false" class="tw-w-full"></el-cascader>
                  </el-col>
                  <el-col :span="12">
                    <el-cascader v-model="form.noteOperations" :options="noteOperation" :props="{ multiple: true }" :collapse-tags="false" class="tw-w-full"></el-cascader>
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item label="短信内容" prop="messageFields">
                <div class="tw-w-full">
                  <el-checkbox v-model="item.state" v-for="item in messageFields" :key="`messageFields-${item.value}`" :true-label="item.value" :false-label="`cancel:${item.value}`" :label="item.value" @change="handleAddField">{{ item.label }}</el-checkbox>
                  <br />
                  <el-input type="textarea" ref="inputRef" :autosize="{ minRows: 4, maxRows: 8 }" placeholder="请输入内容" v-model="form.messageText" :style="basicClassInput" @blur="inputBlur"> </el-input>
                </div>
              </el-form-item>
              <!-- <el-form-item label="短信模板" prop="templateUrl">
                <el-upload :style="{ width: basicClassInput.width }" drag action :auto-upload="false" multiple :limit="1" :on-change="handleFileChange" :file-list="fileList">
                  <el-icon><el-icon-upload /></el-icon>
                  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                </el-upload>
              </el-form-item> -->
            </el-col>
            <el-col :style="{ textAlign: 'center', marginTop: '10px' }">
              <el-button type="primary" @click="submitForm">提 交</el-button>
              <el-button @click="handleClose">取 消</el-button>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
    </el-scrollbar>
  </el-drawer>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { Upload as ElIconUpload } from "@element-plus/icons-vue";
import { recipientsEnum, noteOperation, messageFields, operation, makeacopyfor } from "./common";
import priorityEnum from "@/views/pages/common/priority";
import { addSmsTemplate as addData, getSmsTemplateDetail, setSmsTemplate as editData, uploadFile } from "@/views/pages/apis/NoticeTemplate";

import { getDeviceList } from "@/views/pages/apis/deviceManage";
import { getRegionTree } from "@/views/pages/apis/regionManage";
import { getLocationsTenantCurrent } from "@/views/pages/apis/locationManang";
export default {
  components: {
    // ElIconUpload,
  },
  inject: ["width", "height"],
  emits: ["confirm"],
  data() {
    return {
      recipientsEnum,
      makeacopyfor,
      messageFields,
      priorityEnum: [],
      noteOperation: [],
      basicClassInput: { width: "35.8vw" } /* 输入框选择器基本样式 */,
      form: {
        recipients: [],
        eventOperations: [],
        noteOperations: [],
        messageFields: [],
        carbonCopies: [],
        messageText: "",
        createEvent: false,
        deviceId: "-1",
        regionId: ["-1"],
        locationId: "-1",
      },
      options: [],
      eventOperationsProp: {},
      textCursor: 0,
      currentFile: [],
      fileList: [],
      drawer: false,
      showInput: false,

      devices: [],
      regions: [],
      locations: [],
    };
  },
  computed: {
    // isEdit() {
    //   return !!this.form.id;
    // },
    rules() {
      return {
        name: [
          {
            required: true,
            message: "请输入短信模板名称",
            trigger: ["blur"],
          },
        ],
        // recipients: [
        //   {
        //     required: true,
        //     validator: (rule, value, callback) => {
        //       if (this.form.recipients && this.form.recipients.length) callback();
        //       else callback(new Error("收件人必选"));
        //     },
        //     trigger: "change",
        //   },
        // ],
        // carbonCopies: [
        //   {
        //     required: true,
        //     validator: (rule, value, callback) => {
        //       if (this.form.carbonCopies && this.form.carbonCopies.length) callback();
        //       else callback(new Error("抄送人必选"));
        //     },
        //     trigger: ["blur", "change"],
        //   },
        // ],
        messageFields: [
          {
            required: true,
            validator: (rule, value, callback) => {
              const flag = [];
              this.messageFields.forEach((v) => {
                if (v.state && !v.state.includes("cancel:")) {
                  flag.push(v.value);
                }
              });
              if (!flag || !flag.length) callback(new Error("短信主题必选"));
              else if (!this.form.messageText) callback(new Error("请输入模板内容"));
              else callback();
            },
            trigger: "change",
          },
        ],
        templateUrl: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (this.currentFile.keyName) callback();
              else callback(new Error("请上传短信模板"));
            },
            trigger: "change",
          },
        ],
      };
    },
  },

  created() {
    this.getDevices();
    this.getRegions();
    this.getLocations();
  },
  methods: {
    async getDevices() {
      const { success, data, message } = await getDeviceList({ pageNumber: 1, pageSize: 99999 });
      if (!success) throw new Error(message);
      this.devices = data.map((v) => ({ value: v.id, label: v.name }));
    },
    async getRegions() {
      const { success, data, message } = await getRegionTree({ pageNumber: 1, pageSize: 99999, parentId: "-1" });
      if (!success) throw new Error(message);
      this.regions = data;
    },
    async getLocations() {
      const { success, data, message } = await getLocationsTenantCurrent({ pageNumber: 1, pageSize: 99999 });
      if (!success) throw new Error(message);
      this.locations = data.map((v) => ({ value: v.id, label: v.name }));
    },
    open(id?: string) {
      this.drawer = true;
      this.priorityEnum = this.setEventOperations();
      this.noteOperation = this.setNoteOperation();
      this.recipients = [];
      this.fileList = [];
      this.form.cc = "";

      if (id) {
        this.isEdit = true;
        this.form.id = id;
        // this.$nextTick(() => {
        this.getDetail(id);
        this.messageFields = this.setSubjectKeys();
        // });
      } else {
        this.isEdit = false;
        this.$nextTick(() => {
          this.$refs.form.resetFields();
          this.$refs.form.clearValidate();
          this.form.messageText = "";
          this.messageFields = this.setSubjectKeysNot();
        });
      }
    },
    ccChange(val, list) {
      const bool = this.form.carbonCopies.indexOf("APPOINT_CONTACT");

      if (bool != -1) {
        this.showInput = true;
      } else {
        this.showInput = false;
      }
    },
    getDetail(id) {
      getSmsTemplateDetail({ id }).then(({ success, data }) => {
        if (success) {
          let form = {
            id: data.id,
            name: data.name,
            desc: data.desc,
            deviceId: data.deviceId,
            regionId: data.regionId,
            locationId: data.locationId,
            deviceContacts: data.deviceContacts,
            regionContacts: data.regionContacts,
            locationContacts: data.locationContacts,
            // recipients: data.recipients, // 收件人
            // carbonCopies: data.carbonCopies, // 抄送人
            createEvent: data.createEvent, // 生成事件
            messageText: data.messageText,
          };
          this.messageFields = this.setSubjectKeys();
          if ((data.eventOperations && data.eventOperations.length) || (data.noteOperations && data.noteOperations.length)) form.handleEvent = true; // 处理事件
          form.eventOperations = data.eventOperations.map((v) => [v.priority, v.operation]);
          console.log(form.eventOperations);
          form.noteOperations = data.noteOperations.map((v) => [v.priority, v.operation]);
          data.messageFields.forEach((v) => {
            const enumItem = this.messageFields.find((item) => item.value === v);
            enumItem.state = enumItem.state.substring(enumItem.state.indexOf(":") + 1);
          });
          // this.fileList = [
          //   {
          //     name: data.templateUrl.keyName,
          //     url: `/${data.templateUrl.bucketName}/${data.templateUrl.keyName}`,
          //   },
          // ];
          this.currentFile = data.templateUrl;
          this.form = form;
          console.log(this.form);
        } else this.$message.error(JSON.parse(data)?.message || "短信模板详情获取失败");
      });
    },
    getParams() {
      // let index = this.form.carbonCopies.indexOf("APPOINT_CONTACT");
      // let carbonCopies = [...this.form.carbonCopies];
      // if (index > -1) {
      //   carbonCopies.splice(index, 1);
      // }
      const params = {
        name: this.form.name,
        desc: this.form.desc,
        deviceId: this.form.deviceId,
        deviceContacts: this.form.deviceContacts,
        regionId: this.form.regionId instanceof Array ? this.form.regionId[this.form.regionId.length - 1] : this.form.regionId || "",
        regionContacts: this.form.regionContacts,
        locationId: this.form.locationId,
        locationContacts: this.form.locationContacts,
        createEvent: this.form.createEvent,
        messageFields: [],
        messageText: this.form.messageText,
      };
      if (this.form.handleEvent) {
        params.eventOperations = this.form.eventOperations.map((v) => {
          return { priority: v[0], operation: v[1] };
        });
        params.noteOperations = this.form.noteOperations.map((v) => {
          return { priority: v[0], operation: v[1] };
        });
      }
      this.messageFields.forEach((v) => {
        if (v.state && !v.state.includes("cancel:")) {
          params.messageFields.push(v.value);
        }
      });

      if (this.isEdit) params.id = this.form.id;
      return params;
    },
    submitForm() {
      this.$refs["form"].validate(async (valid) => {
        if (!valid) return false;
        try {
          const { success, message } = await (this.isEdit ? editData : addData)(this.getParams());
          if (!success) throw new Error(message);
          this.$message.success(`操作成功!`);
          this.$refs.form.resetFields();
          this.$refs.form.clearValidate();
          this.handleClose();
        } catch (error) {
          error instanceof Error && this.$message.success(error.message);
        }
      });
    },
    handleFileChange(file) {
      let formData = new FormData();
      formData.append("file", file.raw);
      uploadFile(formData).then(({ success, data }) => {
        if (success) {
          this.$message.success("模板上传成功");
          this.currentFile = data;
        } else {
          this.$message.error(JSON.parse(data)?.message || "模板上传失败");
          this.currentFile = {};
          this.fileList = [];
        }
      });
    },
    inputBlur() {
      this.textCursor = this.$refs.inputRef.$el.children[0].selectionStart;
    },
    handleAddField(v) {
      if (!v.includes("cancel:")) {
        this.form.messageText = this.form.messageText.slice(0, this.textCursor) + `{{${v}}}` + this.form.messageText.slice(this.textCursor);
      } else {
        String.prototype.replaceAll = function (f, e) {
          var reg = new RegExp(f, "g");
          return this.replace(reg, e);
        };
        this.form.messageText = this.form.messageText.replaceAll(`{{${v.substring(v.indexOf(":") + 1)}}}`, ``);
        String.prototype.replaceAll = null;
      }
    },
    setSubjectKeys() {
      if (this.messageFields instanceof Array) return this.messageFields;
      let result = [];

      for (let key in this.messageFields) {
        result.push({
          label: messageFields[key],
          value: key,
          state: this.isEdit ? (this.form.messageText.includes(`{{${key}}}`) ? key : `cancel:${key}`) : `cancel:${key}`,
        });
      }

      return result;
    },
    setSubjectKeysNot() {
      // let data = this.setSubjectKeys();
      // // console.log(data, messageFields);
      // if (this.messageFields instanceof Array) return this.messageFields;
      let result = [];

      for (let key in messageFields) {
        result.push({
          label: messageFields[key],
          value: key,
          state: `cancel:${key}`,
        });
      }

      return result;
    },
    setNoteOperation() {
      let result = [];
      for (let key in priorityEnum) {
        let obj = {
          value: key,
          label: key,
          children: [],
        };
        for (let key2 in noteOperation) {
          obj.children.push({
            label: noteOperation[key2],
            value: key2,
          });
        }
        result.push(obj);
      }
      return result;
    },
    setEventOperations() {
      let result = [];
      for (let key in priorityEnum) {
        result.push({
          value: key,
          label: key,
          children: Object.keys(operation).map((v) => {
            return { value: v, label: operation[v] };
          }),
        });
      }
      return result;
    },
    handleClose /* 返回上一页 */(done) {
      this.$refs.form.resetFields();
      this.$refs.form.clearValidate();
      this.$emit("confirm");
      if (done instanceof Function) done();
      else this.drawer = false;
      // this.$router.replace({ path: "/noticeTemplate" });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/theme/common/var.scss";
// deep() .elstyle-card {
//   margin-top: 20px;
// }
.modules-item {
  .modules-title {
    color: $color-primary;
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}
</style>
