<template>
  <el-dialog v-model="dialogVisible" :title="t('glob.New Data', { value: t('orderGroup.Order group') })" :before-close="handleClose">
    <el-form ref="formRef" :model="form" label-width="130px" :rules="rules">
      <el-form-item :label="t('orderGroup.Name')" prop="ticketGroupName">
        <el-input v-model="form.ticketGroupName" :placeholder="t('orderGroup.Please enter the name')" />
      </el-form-item>
      <el-form-item :label="t('orderGroup.Description')" prop="description">
        <el-input v-model="form.description" :placeholder="t('orderGroup.Please enter the description')" type="textarea" :autosize="{ minRows: 2 }" />
      </el-form-item>
      <el-form-item :label="t('orderGroup.Send email')" prop="sendEmail">
        <el-input v-model="form.sendEmail" :placeholder="t('orderGroup.Please enter the sending email address')" />
      </el-form-item>
      <el-form-item :label="t('orderGroup.Close directly')" prop="sendEmail">
        <el-switch v-model="form.close" />
      </el-form-item>
      <el-form-item :label="t('orderGroup.Automatic closing time of the work order')" prop="autoCloseTime">
        <el-select v-model="form.autoCloseTime" :placeholder="t('orderGroup.Please select the automatic closing time of the work order')">
          <el-option v-for="item in autoCloseTimeOption" :key="`autoCloseTime-${item.value}`" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('orderGroup.Security Container')" prop="containerId">
        <treeAuth v-if="dialogVisible" ref="treeAuthRef"></treeAuth>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ t("glob.Cancel") }}</el-button>
        <el-button type="primary" @click="handleSubmit"> {{ t("glob.Confirm") }} </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { nextTick, reactive, ref } from "vue";

import { useI18n } from "vue-i18n";

import treeAuth from "@/components/treeAuth/index.vue";

import { addOrderGroup, autoCloseTimeOption } from "@/views/pages/apis/orderGroup";

import { ElMessage, FormInstance } from "element-plus";

const { t } = useI18n();

const emits = defineEmits(["refresh"]);

const dialogVisible = ref<boolean>(false);

const treeAuthRef = ref();

// interface FormItem {}

const form = ref({
  ticketGroupName: "",
  description: "",
  sendEmail: "",
  close: true,
  autoCloseTime: (autoCloseTimeOption.value.find((v) => v.default) || {}).value || "",
  containerId: "",
});

const formRef = ref<FormInstance>();

const rules = reactive({
  ticketGroupName: [{ required: true, message: t("orderGroup.Please enter the name"), trigger: ["blur", "change"] }],
  containerId: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        form.value.containerId = treeAuthRef.value && treeAuthRef.value.treeId ? treeAuthRef.value.treeId : "";
        nextTick(() => (form.value.containerId ? callback() : callback(new Error(t("orderGroup.Please select the security Container")))));
      },
      trigger: ["blur", "change"],
    },
  ],
});

function handleSubmit() {
  formRef.value &&
    formRef.value.validate(async (valid) => {
      try {
        if (!valid) return;
        const params = { ...form.value };
        const { message, success } = await addOrderGroup(params);
        if (!success) throw new Error(message);
        ElMessage.success(t("axios.Operation successful"));
        nextTick(() => handleClose(void 0));
        emits("refresh");
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    });
}

function handleClose(done) {
  formRef.value && formRef.value.resetFields();
  nextTick(() => (done && done instanceof Function ? done() : (dialogVisible.value = false)));
}

function handleOpen() {
  nextTick(() => (dialogVisible.value = true));
}

defineExpose({ open: handleOpen });
</script>
