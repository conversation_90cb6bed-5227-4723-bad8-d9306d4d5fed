import { defineComponent, h, toRefs } from "vue";
import { ElButton, ElDialog } from "element-plus";

export default defineComponent({
  name: "HelpDialog",
  props: {
    modelValue: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      default: "Help",
    },
    content: {
      type: Array,
      required: true,
    },
  },
  emits: ["update:modelValue"],
  setup(props, { emit }) {
    const { modelValue, title, content } = toRefs(props);

    const closeDialog = () => {
      emit("update:modelValue", false);
    };

    return () =>
      h(
        ElDialog,
        {
          "modelValue": modelValue.value,
          "onUpdate:modelValue": (value: boolean) => emit("update:modelValue", value),
          "title": title.value,
          "width": "600px",
          "alignCenter": true,
          "class": "help-dialog",
          "appendToBody": true,
        },
        {
          default: () => [...content.value.map((v) => h("p", { class: "tw-w-full tw-leading-8" }, v as string))],
          footer: () => [h(El<PERSON>utt<PERSON>, { type: "primary", onClick: closeDialog }, "确 定")],
        }
      );
  },
});
