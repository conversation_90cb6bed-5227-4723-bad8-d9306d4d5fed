<template>
  <el-card>
    <pageTemplate v-model:currentPage="state.pageNumber" v-model:pageSize="state.pageSize" :total="state.total" :height="height - 40" @size-change="handleRefresh()" @current-change="handleRefresh()">
      <template #left>
        <div class="tw-flex tw-items-center">
          <span class="tw-text-[14px] tw-font-bold">{{ t("orderGroup.Order group") }}</span>
          <!-- <el-button type="primary" link :icon="QuestionFilled"></el-button> -->
        </div>
      </template>
      <template #right>
        <el-button v-if="userInfo.hasPermission(服务管理中心_工单组_新建)" type="primary" :icon="Plus" @click="handleEditor('add')">{{ t("glob.New Data", { value: t("orderGroup.Order group") }) }}</el-button>
      </template>
      <template #default="{ height: tableHeight }">
        <el-scrollbar :height="tableHeight">
          <el-table :data="state.data" border v-loading="state.loading">
            <el-table-column type="expand">
              <template #default="{ row }">
                <div class="tw-m-[20px]">
                  <userGroupConfig :detail="row" />
                  <orderType :detail="row" />
                  <sendStrategy :detail="row" />
                  <orderCloseReason :detail="row" />
                  <priorityStrategy :detail="row" />
                </div>
              </template>
            </el-table-column>

            <TableColumn type="condition" prop="ticketGroupName" :label="t('orderGroup.Name')" filter-multiple show-filter v-model:custom-filtered-value="searchProjectName" :filters="$filter0" @filter-change="handleRefresh()">
              <template #default="{ row }">
                <div v-if="!row.isEdit">{{ row.ticketGroupName }}</div>
                <el-input v-else v-model="row.ticketGroupName"></el-input>
              </template>
            </TableColumn>
            <TableColumn type="condition" prop="description" :label="t('orderGroup.Description')" filter-multiple show-filter v-model:custom-filtered-value="searchProjectDesc" :filters="$filter0" @filter-change="handleRefresh()">
              <template #default="{ row }">
                <div v-if="!row.isEdit">{{ row.description }}</div>
                <el-input v-else v-model="row.description"></el-input>
              </template>
            </TableColumn>
            <TableColumn type="condition" prop="sendEmail" :label="t('orderGroup.Send email')" filter-multiple show-filter v-model:custom-filtered-value="searchProjectSendEmail" :filters="$filter0" @filter-change="handleRefresh()">
              <template #default="{ row }">
                <div v-if="!row.isEdit">{{ row.sendEmail }}</div>
                <el-input v-else v-model="row.sendEmail"></el-input>
              </template>
            </TableColumn>

            <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="search.autoCloseTime" @filter-change="handleRefresh()" prop="autoCloseTime" :label="$t('orderGroup.Automatic closing time of the work order')" :width="120" :filters="autoCloseTimeOption.map((v) => ({ ...v, text: v.label }))">
              <template #default="{ row }">
                <div v-if="!row.isEdit">{{ (autoCloseTimeOption.find((v) => v.value == row.autoCloseTime) || {}).label }}</div>
                <el-select v-else :modelValue="row.autoCloseTime" :onUpdate:modelValue="(v) => (row.autoCloseTime = v)" :placeholder="t('orderGroup.Please select the automatic closing time of the work order')" :empty-values="[null, undefined]" :value-on-clear="null">
                  <el-option v-for="item in autoCloseTimeOption" :key="`autoCloseTime-${item.value}`" :label="item.label" :value="item.value" />
                </el-select>
              </template>
            </TableColumn>
            <TableColumn type="default" width="120">
              <template #header>
                <div class="tw-flex tw-items-center">
                  {{ t("orderGroup.Close directly") }}
                  <el-button type="info" link :icon="QuestionFilled" @click="handleOpenHelp('close')"></el-button>
                </div>
              </template>
              <template #default="{ row }">
                <template v-if="row.isEdit">
                  <el-checkbox v-model="row.close" />
                </template>
                <span v-else>{{ row.close ? "✔" : "" }}</span>
              </template>
            </TableColumn>
            <TableColumn type="default" prop="alarmClassification">
              <template #header>
                <div class="tw-flex tw-items-center">
                  {{ t("orderGroup.The Processing Sequence of Alarm Classification") }}
                  <el-button type="info" link :icon="QuestionFilled" @click="handleOpenHelp('alarmClassification')"></el-button>
                </div>
              </template>
            </TableColumn>
            <TableColumn type="default" :label="t('glob.operate')">
              <template #default="{ row }">
                <template v-if="!row.isEdit">
                  <el-button v-if="row.verifyPermissionIds.includes(服务管理中心_工单组_更新)" type="primary" link @click="() => (state.data.find((v: any) => v.isEdit) ? ElMessage.warning(t('orderGroup.Please save the information that you are currently editing first')) : (row.isEdit = true))">{{ t("glob.edit") }}</el-button>
                  <el-button v-if="row.verifyPermissionIds.includes(服务管理中心_工单组_删除)" type="danger" link @click="handleDelItem(row as Item)">{{ t("glob.delete") }}</el-button>

                  <el-link v-if="row.verifyPermissionIds.includes(服务管理中心_工单组_安全)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" style="font-size: 22px" :icon="Security" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
                </template>

                <template v-else>
                  <el-button type="primary" link @click="handleEditor('edit', row as Item)">{{ t("glob.Confirm") }}</el-button>
                  <el-button type="primary" link @click="handleRefresh">{{ t("glob.Cancel") }}</el-button>
                </template>
              </template>
            </TableColumn>
          </el-table>
        </el-scrollbar>
      </template>
    </pageTemplate>

    <Editor ref="editRef" @refresh="handleRefresh" />
    <HelpDialog v-model="showHelp" :title="helpTitle" :content="helpContent" />
  </el-card>
</template>

<script setup lang="ts">
import { ref, inject, reactive, onMounted, nextTick, computed, toValue } from "vue";
import { useI18n } from "vue-i18n";

/* component  */
import pageTemplate from "@/components/pageTemplate.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import Editor from "./Editor.vue";
import showSecurityTree from "@/components/security-container";
import Security from "@/assets/dp.vue";

/* element */
import { Plus, QuestionFilled } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, Action, MessageBoxState } from "element-plus";

/* model component */
import userGroupConfig from "./model/userGroupConfig.vue";
import orderType from "./model/orderType.vue";
import sendStrategy from "./model/sendStrategy.vue";
import orderCloseReason from "./model/orderCloseReason.vue";
import priorityStrategy from "./model/priorityStrategy.vue";

/* api */
import { getOrderGroup as getData, autoCloseTimeOption, setOrderGroup as setData, OrderGroup, delOrderGroup as delData } from "@/views/pages/apis/orderGroup";
import getUserInfo from "@/utils/getUserInfo";

/* help */
import HelpDialog from "./help";

/* permission */
import { 服务管理中心_工单组_新建, 服务管理中心_工单组_更新, 服务管理中心_工单组_删除, 服务管理中心_工单组_可读, 服务管理中心_工单组_安全 } from "@/views/pages/permission";

const { t } = useI18n();

const height = inject("height") as number;

const userInfo = getUserInfo();

type Item = OrderGroup;

interface State<T> {
  data: T[];
  pageNumber: number;
  pageSize: number;
  total: number;
  loading: boolean;
}

const state = reactive<State<Item>>({
  data: [],
  pageNumber: 1,
  pageSize: 50,
  total: 0,
  loading: false,
});

const editRef = ref();

const showHelp = ref(false);
const helpTitle = ref<string>("");
const helpContent = ref<string[]>([]);
const helpOption = {
  alarmClassification: {
    title: "告警分类处理说明",
    content: [
      /*  */
      "1、告警分类处理顺序：用来定义当工单里有不同的告警分类时，可以按照告警分类来分给一个工单组下对应的用户处理。例如：服务器类型的告警，可以分给服务器用户组。",
      "2、当一个工单内具有多个告警分类时，需要定义好，应该按照配置的顺序去分派给对应的用户组。举例说明：假设告警分类的处理顺序为网络、安全、服务器，一个工单中涵盖网络、安全两种类型的告警，此时，工单应该默认分配给该工单组下的网络组。",
    ],
  },
  close: {
    title: "直接关闭说明",
    content: [
      /*  */
      "1、若勾选了“直接关闭”选项，工单未被完成时，可以执行关闭操作。",
      "2、若未勾选“直接关闭”选项，工单未被完成时，无法执行关闭操作。",
    ],
  },
};

async function handleOpenHelp(type: "alarmClassification" | "close") {
  helpTitle.value = helpOption[type].title;
  helpContent.value = helpOption[type].content;
  await nextTick();
  showHelp.value = true;
}
const search = ref({
  // 工单组名称
  eqTicketGroupName: [],
  includeTicketGroupName: [],
  ticketGroupNameFilterRelation: "AND",
  neTicketGroupName: [],
  excludeTicketGroupName: [],
  // 描述
  excludeDesc: [],
  includeDesc: [],
  descFilterRelation: "AND",
  eqDesc: [],
  neDesc: [],
  // 发送邮件
  excludeSendEmail: [],
  includeSendEmail: [],
  sendEmailFilterRelation: "AND",
  eqSendEmail: [],
  neSendEmail: [],
  // 自动关闭时间
  // autoCloseTime: [],
});
import { exoprtMatch1, exoprtMatch2, exoprtMatch3 } from "@/components/tableColumn/common";
const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);
const $filter2 = ref(exoprtMatch3);
// 工单组名称
const searchProject0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchProject1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchProjectName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchProject0ByName) === "include") value0 = search.value.includeTicketGroupName[0] || "";
    if (toValue(searchProject0ByName) === "exclude") value0 = search.value.excludeTicketGroupName[0] || "";
    if (toValue(searchProject0ByName) === "eq") value0 = search.value.eqTicketGroupName[0] || "";
    if (toValue(searchProject0ByName) === "ne") value0 = search.value.neTicketGroupName[0] || "";
    let value1 = "";
    if (toValue(searchProject1ByName) === "include") value1 = search.value.includeTicketGroupName[search.value.includeTicketGroupName.length - 1] || "";
    if (toValue(searchProject1ByName) === "exclude") value1 = search.value.excludeTicketGroupName[search.value.excludeTicketGroupName.length - 1] || "";
    if (toValue(searchProject1ByName) === "eq") value1 = search.value.eqTicketGroupName[search.value.eqTicketGroupName.length - 1] || "";
    if (toValue(searchProject1ByName) === "ne") value1 = search.value.neTicketGroupName[search.value.neTicketGroupName.length - 1] || "";
    return {
      type0: toValue(searchProject0ByName),
      type1: toValue(searchProject1ByName),
      relation: search.value.ticketGroupNameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchProject0ByName.value = v.type0 as typeof searchProject0ByName extends import("vue").Ref<infer T> ? T : string;
    searchProject1ByName.value = v.type1 as typeof searchProject1ByName extends import("vue").Ref<infer T> ? T : string;
    search.value.ticketGroupNameFilterRelation = v.relation;
    search.value.includeTicketGroupName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeTicketGroupName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqTicketGroupName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neTicketGroupName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
// 描述
const searchProject0ByDesc = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchProject1ByDesc = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchProjectDesc = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchProject0ByDesc) === "include") value0 = search.value.includeDesc[0] || "";
    if (toValue(searchProject0ByDesc) === "exclude") value0 = search.value.excludeDesc[0] || "";
    if (toValue(searchProject0ByDesc) === "eq") value0 = search.value.eqDesc[0] || "";
    if (toValue(searchProject0ByDesc) === "ne") value0 = search.value.neDesc[0] || "";
    let value1 = "";
    if (toValue(searchProject1ByDesc) === "include") value1 = search.value.includeDesc[search.value.includeDesc.length - 1] || "";
    if (toValue(searchProject1ByDesc) === "exclude") value1 = search.value.excludeDesc[search.value.excludeDesc.length - 1] || "";
    if (toValue(searchProject1ByDesc) === "eq") value1 = search.value.eqDesc[search.value.eqDesc.length - 1] || "";
    if (toValue(searchProject1ByDesc) === "ne") value1 = search.value.neDesc[search.value.neDesc.length - 1] || "";
    return {
      type0: toValue(searchProject0ByDesc),
      type1: toValue(searchProject1ByDesc),
      relation: search.value.descFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchProject0ByDesc.value = v.type0 as typeof searchProject0ByDesc extends import("vue").Ref<infer T> ? T : string;
    searchProject1ByDesc.value = v.type1 as typeof searchProject1ByDesc extends import("vue").Ref<infer T> ? T : string;
    search.value.descFilterRelation = v.relation;
    search.value.includeDesc = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeDesc = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqDesc = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neDesc = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
// 发送邮件
const searchProject0BySendEmail = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchProject1BySendEmail = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchProjectSendEmail = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchProject0BySendEmail) === "include") value0 = search.value.includeSendEmail[0] || "";
    if (toValue(searchProject0BySendEmail) === "exclude") value0 = search.value.excludeSendEmail[0] || "";
    if (toValue(searchProject0BySendEmail) === "eq") value0 = search.value.eqSendEmail[0] || "";
    if (toValue(searchProject0BySendEmail) === "ne") value0 = search.value.neSendEmail[0] || "";
    let value1 = "";
    if (toValue(searchProject1BySendEmail) === "include") value1 = search.value.includeSendEmail[search.value.includeSendEmail.length - 1] || "";
    if (toValue(searchProject1BySendEmail) === "exclude") value1 = search.value.excludeSendEmail[search.value.excludeSendEmail.length - 1] || "";
    if (toValue(searchProject1BySendEmail) === "eq") value1 = search.value.eqSendEmail[search.value.eqSendEmail.length - 1] || "";
    if (toValue(searchProject1BySendEmail) === "ne") value1 = search.value.neSendEmail[search.value.neSendEmail.length - 1] || "";
    return {
      type0: toValue(searchProject0BySendEmail),
      type1: toValue(searchProject1BySendEmail),
      relation: search.value.sendEmailFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchProject0BySendEmail.value = v.type0 as typeof searchProject0BySendEmail extends import("vue").Ref<infer T> ? T : string;
    searchProject1BySendEmail.value = v.type1 as typeof searchProject1BySendEmail extends import("vue").Ref<infer T> ? T : string;
    search.value.sendEmailFilterRelation = v.relation;
    search.value.includeSendEmail = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeSendEmail = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqSendEmail = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neSendEmail = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
function handleDelItem(row: Item) {
  ElMessageBox.confirm(t("orderGroup.Are you sure you want to delete the work order group", { name: row.ticketGroupName }), t("glob.delete"), {
    confirmButtonText: t("glob.Confirm"),
    cancelButtonText: t("glob.Cancel"),
    type: "warning",
    beforeClose: async (action: Action, instance: MessageBoxState, done: () => void) => {
      if (action === "confirm") {
        try {
          const { success, message } = await delData({ id: row.id });
          if (!success) throw new Error(message);
          ElMessage.success(t("axios.Operation successful"));
          handleRefresh();
          done();
        } catch (error) {
          error instanceof Error && ElMessage.error(error.message);
        }
      } else done();
    },
  })
    .then(() => {})
    .catch(() => {});
}

/**
 * @desc 新增编辑
 */
async function handleEditor(operation: "add" | "edit", row?: Item) {
  switch (operation) {
    case "add":
      editRef.value && editRef.value.open();
      break;
    case "edit":
      try {
        if (!row) return;
        const params = {
          id: row.id,
          tenantId: row.tenantId,
          ticketGroupName: row.ticketGroupName,
          description: row.description,
          sendEmail: row.sendEmail,
          close: row.close,
          autoCloseTime: row.autoCloseTime,
        };
        const { message, success } = await setData({ ...params });
        if (!success) throw new Error(message);
        ElMessage.success(t("axios.Operation successful"));
        handleRefresh();
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
      break;
    default:
      break;
  }
}

/**
 * @desc 刷新
 */
async function handleRefresh() {
  try {
    state.loading = true;
    const params = {
      pageNumber: state.pageNumber,
      pageSize: state.pageSize,
      ...search.value,
    };
    const { data, message, success, total } = await getData({ ...params });
    if (!success) throw new Error(message);
    state.data = data.map((v) => Object.assign(v, { isEdit: false }));
    state.total = Number(total || 0);
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    state.loading = false;
  }
}

onMounted(() => {
  handleRefresh();
});
</script>
