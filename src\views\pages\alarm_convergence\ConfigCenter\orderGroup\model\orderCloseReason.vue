<template>
  <div class="tw-mt-[8px]">
    <div class="tw-mb-[8px] tw-flex tw-items-center tw-justify-between">
      <span class="tw-text-[14px] tw-font-bold">{{ t("orderGroup.Close reason") }}</span>
      <el-button v-if="userInfo.hasPermission(服务管理中心_工单组_分配关闭原因)" type="primary" :icon="Plus" :loading="state.loading" @click="handleEditor('add')">{{ t("glob.Add Data", { value: t("orderGroup.Close reason") }) }}</el-button>
    </div>
    <el-table :data="state.data" border :show-header="false" v-loading="state.loading">
      <el-table-column prop="closeCodeName"></el-table-column>
      <el-table-column width="120" v-if="userInfo.hasPermission(服务管理中心_工单组_分配关闭原因)">
        <template #default="{ row }">
          <el-button type="danger" link @click="handleRemoveItem(row)">{{ t("glob.remove") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, h, defineComponent, reactive, onMounted } from "vue";
import { useI18n } from "vue-i18n";

import { FormInstance, ElMessageBox, ElForm, ElFormItem, ElSelect, ElOption, Action, MessageBoxState, ElMessage } from "element-plus";
import { Plus } from "@element-plus/icons-vue";

import {
  /*  */
  OrderGroup,
  CloseCodeItem,
  addCloseCode as addData,
  getCloseCode as getData,
  delCloseCode as delData,
} from "@/views/pages/apis/orderGroup";
import { getnewCodeConfigList } from "@/views/pages/apis/completeCodeConfig";

import getUserInfo from "@/utils/getUserInfo";

import { 服务管理中心_工单组_分配关闭原因 } from "@/views/pages/permission";

interface Props {
  detail: OrderGroup;
}

const props = withDefaults(defineProps<Props>(), { detail: () => ({}) as OrderGroup });

const userInfo = getUserInfo();

const { t } = useI18n();

type Item = CloseCodeItem;

interface State<T> {
  data: T[];
  loading: boolean;
}

const state = reactive<State<Item>>({
  data: [],
  loading: false,
});

function handleRemoveItem(row) {
  ElMessageBox.confirm(t("orderGroup.Are you sure you want to remove the reason for closure", { name: row.closeCodeName }), t("glob.delete"), {
    confirmButtonText: t("glob.Confirm"),
    cancelButtonText: t("glob.Cancel"),
    type: "warning",
    beforeClose: async (action: Action, instance: MessageBoxState, done: () => void) => {
      if (action === "confirm") {
        try {
          const { success, message } = await delData({ id: row.id });
          if (!success) throw new Error(message);
          ElMessage.success(t("axios.Operation successful"));
          handleRefresh();
          done();
        } catch (error) {
          error instanceof Error && ElMessage.error(error.message);
        }
      } else done();
    },
  })
    .then(() => {})
    .catch(() => {});
}

const form = ref({ ticketGroupId: props.detail.id, closeCodeId: "", closeCodeName: "" });
const formRef = ref<FormInstance>();
const codeConfigOption = ref([]);
async function handleEditor(operate: "add" | "edit", _row?) {
  switch (operate) {
    case "add":
      state.loading = true;
      const { data, message, success } = await getnewCodeConfigList({
        containerId: (userInfo.currentTenant || {}).containerId,
        queryPermissionId: "517242654942035968",
        verifyPermissionIds: ["517242693848399872", "517242712731156480", "612169437818126336"],
      });

      if (!success) throw new Error(message);
      const ids = state.data.map((v) => v.closeCodeId);
      codeConfigOption.value = data.filter((v) => !ids.includes(v.id)) as any;
      await nextTick();
      ElMessageBox({
        title: t("glob.Add Data", { value: t("orderGroup.Close reason") }),
        message: h(
          defineComponent({
            setup() {
              return () =>
                h(ElForm, { model: form.value, ref: (el) => (formRef.value = el as FormInstance), rules: { closeCodeId: [{ required: true, message: "", trigger: ["blur", "change"] }] } }, [
                  h(
                    ElFormItem,
                    { prop: "closeCodeId" },
                    h(
                      ElSelect,
                      {
                        "modelValue": form.value.closeCodeId,
                        "onUpdate:modelValue": (v) => {
                          form.value.closeCodeId = v;
                          form.value.closeCodeName = (codeConfigOption.value.find((f: any) => f.id === v) || ({} as any)).codeName;
                        },
                        "filterable": true,
                      },
                      codeConfigOption.value.map((v: any) => h(ElOption, { label: v.codeName, value: v.id }))
                    )
                  ),
                ]);
            },
          })
        ),
        showCancelButton: true,
        beforeClose(action: Action, instance: MessageBoxState, done: () => void) {
          if (action === "confirm") {
            formRef.value &&
              formRef.value.validate(async (valid) => {
                try {
                  if (!valid) return;
                  const { message, success } = await addData({ ...form.value });
                  if (!success) throw new Error(message);
                  ElMessage.success(t("axios.Operation successful"));
                  formRef.value && formRef.value.resetFields();
                  await nextTick();
                  done();
                  handleRefresh();
                } catch (error) {
                  error instanceof Error && ElMessage.error(error.message);
                }
              });
          } else {
            formRef.value && formRef.value.resetFields();
            nextTick(() => done());
          }
        },
      })
        .then(() => {})
        .catch(() => {})
        .finally(() => {
          state.loading = false;
        });
      break;
    case "edit":
      break;
    default:
      break;
  }
}

async function handleRefresh() {
  try {
    state.loading = true;
    const { data, message, success } = await getData({ ticketGroupId: props.detail.id });
    if (!success) throw new Error(message);
    state.data = data;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    state.loading = false;
  }
}
onMounted(() => {
  handleRefresh();
});
</script>
