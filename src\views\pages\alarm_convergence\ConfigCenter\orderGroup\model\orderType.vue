<template>
  <div class="tw-mt-[10px]" v-for="(flag, flagIdx) in flags" :key="`${flag.title}-${flagIdx}`">
    <div class="tw-mb-[8px] tw-flex tw-items-center tw-justify-between">
      <span class="tw-text-[14px] tw-font-bold">{{ flag.title }}</span>
      <el-button type="primary" :icon="Plus" @click="handleAddItem(flag)">{{ t("glob.Add Data", { value: flag.title }) }}</el-button>
    </div>
    <el-table :data="state[flag.type].data" border row-key="id" :loading="state[flag.type].loading" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column :label="t('orderGroup.Classification name')" prop="classificationName">
        <template #default="{ row, column }">
          <template v-if="!row.isEdit">{{ row[column.property] }}</template>
          <el-input v-else v-model="row[column.property]" placeholder="请输入名称"></el-input>
        </template>
      </el-table-column>
      <el-table-column :label="t('orderGroup.Description')" prop="description">
        <template #default="{ row, column }">
          <template v-if="!row.isEdit">{{ row[column.property] }}</template>
          <el-input v-else v-model="row[column.property]" placeholder="请输入描述"></el-input>
        </template>
      </el-table-column>
      <el-table-column :label="`激活`">
        <template #default="{ row }">
          <template v-if="!row.isEdit">{{ row.active ? "✔" : "" }}</template>
          <el-checkbox v-else v-model="row.active" />
        </template>
      </el-table-column>
      <el-table-column :label="`已有工单`">
        <template #default="{ row }">
          <template v-if="!row.isEdit">{{ row.orderByuse ? "✔" : "" }}</template>
          <!-- <el-checkbox v-model="row.orderByuse" /> -->
        </template>
      </el-table-column>
      <el-table-column :label="t('glob.operate')">
        <template #default="{ row }">
          <template v-if="!row.isEdit">
            <el-button type="primary" link @click="() => (row.isEdit = true)">{{ t("glob.edit") }}</el-button>
            <el-button type="primary" link @click="handleAddItem(flag, row)">{{ t("glob.Add Data", { value: t("orderGroup.subclassification") }) }}</el-button>
            <el-button type="danger" link @click="handleDelItem(row)">{{ t("glob.delete") }}</el-button>
          </template>
          <template v-else>
            <el-button type="primary" link @click="handleEditItem(row)">{{ t("glob.Save") }}</el-button>
            <el-button type="primary" link @click="handleRefresh(flag.type)">{{ t("glob.Cancel") }}</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h, defineComponent, nextTick, onMounted, reactive } from "vue";
import { useI18n } from "vue-i18n";

import { ElCheckbox, ElForm, ElFormItem, ElInput, ElMessageBox, FormInstance, Action, MessageBoxState, ElMessage } from "element-plus";
import { Plus } from "@element-plus/icons-vue";

import {
  /*  */
  OrderGroup,
  TicketClassificationItem,
  AssignableTicketType as OrderType,
  addTicketClassification as addData,
  getTicketClassification as getData,
  setTicketClassification as setData,
  delTicketClassification as delData,
} from "@/views/pages/apis/orderGroup";

import getUserInfo from "@/utils/getUserInfo";

const { t } = useI18n();

interface Props {
  detail: OrderGroup;
}

const props = withDefaults(defineProps<Props>(), { detail: () => ({}) as OrderGroup });

const userInfo = getUserInfo();

type ItemData = TicketClassificationItem & Record<"children", TicketClassificationItem[]>;
type TypeItem = { title: string; type: keyof typeof OrderType };

const flags = computed<TypeItem[]>(() => [
  /*  */
  { title: t("orderGroup.Event classification", { classification: t("orderGroup.classification") }), type: OrderType.event },
  { title: t("orderGroup.Change classification", { classification: t("orderGroup.classification") }), type: OrderType.change },
  { title: t("orderGroup.Problem classification", { classification: t("orderGroup.classification") }), type: OrderType.question },
  { title: t("orderGroup.Service request classification", { classification: t("orderGroup.classification") }), type: OrderType.service },
  { title: t("orderGroup.Release classification", { classification: t("orderGroup.classification") }), type: OrderType.publish },
  // { title: t("orderGroup.DICT event classification", { classification: t("orderGroup.classification") }), type: OrderType.dictevent },
  // { title: t("orderGroup.DICT service request classification", { classification: t("orderGroup.classification") }), type: OrderType.dictservice },
]);

const state = reactive<Record<keyof typeof OrderType, { data: ItemData[]; loading: boolean }>>({
  [OrderType.event]: { data: [], loading: false },
  [OrderType.change]: { data: [], loading: false },
  [OrderType.question]: { data: [], loading: false },
  [OrderType.service]: { data: [], loading: false },
  [OrderType.publish]: { data: [], loading: false },
  [OrderType.dictevent]: { data: [], loading: false },
  [OrderType.dictservice]: { data: [], loading: false },
});

const addItemForm = ref({
  classificationName: "",
  description: "",
  quality: "",
  active: true,
});
const addItemFormRef = ref<FormInstance>();

async function handleEditItem(row) {
  try {
    if (!row.classificationName) throw new Error("请输入名称");
    const params = {
      id: row.id,
      classificationName: row.classificationName,
      description: row.description,
      quality: row.quality,
      active: row.active,
    };
    const { success, message } = await setData({ ...params });
    if (!success) throw new Error(message);
    ElMessage.success(t("axios.Operation successful"));
    handleRefresh(row.type);
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

function handleDelItem(row) {
  ElMessageBox.confirm(`确定删除工单分类 "${row.classificationName}" ?`, "提示", {
    confirmButtonText: t("glob.Confirm"),
    cancelButtonText: t("glob.Cancel"),
    type: "warning",
    beforeClose: async (action, instance, done) => {
      if (action === "confirm") {
        try {
          const { success, message } = await delData({ id: row.id });
          if (!success) throw new Error(message);
          ElMessage.success(t("axios.Operation successful"));
          done();
          handleRefresh(row.type);
        } catch (error) {
          error instanceof Error && ElMessage.error(error.message);
        }
      } else done();
    },
  })
    .then(() => {})
    .catch(() => {});
}

function handleAddItem(addType: TypeItem, row?: ItemData) {
  ElMessageBox({
    title: t("glob.Add Data", {
      value: {
        /*  */
        [OrderType.event]: t("orderGroup.Event classification", { classification: !(row || {}).id ? t("orderGroup.classification") : t("orderGroup.subclassification") }),
        [OrderType.change]: t("orderGroup.Change classification", { classification: !(row || {}).id ? t("orderGroup.classification") : t("orderGroup.subclassification") }),
        [OrderType.question]: t("orderGroup.Problem classification", { classification: !(row || {}).id ? t("orderGroup.classification") : t("orderGroup.subclassification") }),
        [OrderType.service]: t("orderGroup.Service request classification", { classification: !(row || {}).id ? t("orderGroup.classification") : t("orderGroup.subclassification") }),
        [OrderType.publish]: t("orderGroup.Release classification", { classification: !(row || {}).id ? t("orderGroup.classification") : t("orderGroup.subclassification") }),
        [OrderType.dictevent]: t("orderGroup.DICT event classification", { classification: !(row || {}).id ? t("orderGroup.classification") : t("orderGroup.subclassification") }),
        [OrderType.dictservice]: t("orderGroup.DICT service request classification", { classification: !(row || {}).id ? t("orderGroup.classification") : t("orderGroup.subclassification") }),
      }[addType.type],
    }),
    message: h(
      defineComponent({
        setup() {
          return () =>
            h(
              ElForm,
              {
                model: addItemForm.value,
                ref: ($el) => (addItemFormRef.value = $el as FormInstance),
                rules: {
                  classificationName: [{ required: true, message: `请输入名称`, trigger: ["blur", "change"] }],
                },
                // labelPosition: "left",
                labelWidth: "50px",
              },
              [
                /*  */
                h(ElFormItem, { label: "名称", prop: "classificationName" }, h(ElInput, { "modelValue": addItemForm.value.classificationName, "onUpdate:modelValue": (v) => (addItemForm.value.classificationName = v), "placeholder": `请输入名称` })),
                h(ElFormItem, { label: "描述", prop: "description" }, h(ElInput, { "modelValue": addItemForm.value.description, "onUpdate:modelValue": (v) => (addItemForm.value.description = v), "placeholder": `请输入描述` })),
                // h(ElFormItem, { label: "性质", prop: "quality" }, h(ElInput, { "modelValue": addItemForm.value.quality, "onUpdate:modelValue": (v) => (addItemForm.value.quality = v), "placeholder": `请输入性质` })),
                h(ElFormItem, { prop: "active" }, h(ElCheckbox, { "label": "激活", "modelValue": addItemForm.value.active, "onUpdate:modelValue": (v) => (addItemForm.value.active = v as boolean) })),
              ]
            );
        },
      })
    ),
    showCancelButton: true,
    beforeClose(action: Action, instance: MessageBoxState, done: () => void) {
      if (action === "confirm") {
        addItemFormRef.value &&
          addItemFormRef.value.validate(async (valid) => {
            try {
              if (!valid) return;
              const { message, success } = await addData(Object.assign(addItemForm.value, { ticketGroupId: props.detail.id, parentId: (row || {}).id || void 0, type: addType.type }));
              if (!success) throw new Error(message);
              addItemFormRef.value && addItemFormRef.value.resetFields();
              nextTick(() => done());
              handleRefresh(addType.type);
              ElMessage.success(t("axios.Operation successful"));
            } catch (error) {
              error instanceof Error && ElMessage.error(error.message);
            }
          });
      } else {
        addItemFormRef.value && addItemFormRef.value.resetFields();
        nextTick(() => done());
      }
    },
  })
    .then(() => {})
    .catch(() => {});
}

/**
 * @description 获取分类列表
 * @param type 工单类型
 */
async function handleRefresh(type) {
  try {
    state[type].loading = true;
    const { data, message, success } = await getData({ ticketGroupId: props.detail.id, type });
    if (!success) throw new Error(message);
    state[type].data = _resetData(
      data.filter((v) => !v.parentId || v.parentId === "-1"),
      data
    );
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    state[type].loading = false;
  }
}

function _resetData(list, allData) {
  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    item.children = allData.filter((v) => v.parentId === item.id) || [];
    if (item.children instanceof Array && item.children.length) _resetData(item.children, allData);
  }
  return list;
}

onMounted(() => {
  Promise.all(flags.value.map((v) => handleRefresh(v.type)));
});
</script>
