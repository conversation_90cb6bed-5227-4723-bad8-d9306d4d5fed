<template>
  <div class="tw-my-[8px] tw-text-[14px] tw-font-bold">{{ t("orderGroup.Default Priority Adjustment Strategy") }}</div>
  <el-card class="el-card-mt alarm-down-config tw-mt-[8px]">
    <el-form :model="{}" class="alarm-table">
      <el-row>
        <el-form-item style="width: 100%">
          <!-- <el-row style="width: 100%">
                    <el-col class="bold" :span="12" style="text-align: left">
                      <el-form-item label="工作时间："> </el-form-item>
                    </el-col>
                    <el-col class="bold" :span="12" style="text-align: right">
                      <el-form-item label="选择时区：">
                        <el-select v-model="effectTimeCfg.timeZone" filterable placeholder="请选择" :style="basicClassInputDown" style="width: 400px">
                          <el-option v-for="item in timeZone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"> </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row> -->

          <el-col :span="12">
            <el-form-item :label="$t('alarmMerge.WorkingHours')">
              <!--  -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('alarmMerge.SelectTimeZone')">
              <el-select v-model="effectTimeCfg.timeZone" filterable :placeholder="$t('alarmMerge.PleaseSelect')" class="tw-w-full">
                <el-option v-for="item in timeZone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item style="width: 100%">
          <el-row style="width: 100%">
            <el-col>
              <div style="width: 100%" class="support-table-content" ref="tableContentRef">
                <el-table stripe border :data="effectTimeCfg.coverWorkTime" style="width: 100%; margin-top: 0px" @header-click="(column, $event) => handleClick({ column })">
                  <el-table-column align="center" prop="week" width="80">
                    <template #default="scope">
                      <el-button link @click="handleSelectTime('all', scope.$index, scope.row)" style="width: 100%">
                        {{ scope.row.weekDay == 1 ? $t("alarmMerge.Monday") : scope.row.weekDay == 2 ? $t("alarmMerge.Tuesday") : scope.row.weekDay == 3 ? $t("alarmMerge.Wednesday") : scope.row.weekDay == 4 ? $t("alarmMerge.Thursday") : scope.row.weekDay == 5 ? $t("alarmMerge.Friday") : scope.row.weekDay == 6 ? $t("alarmMerge.Saturday") : $t("alarmMerge.Sunday") }}
                      </el-button>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" :width="tableWidth" v-for="(item, key) in 24" :key="`h-${key}`" :label="String(key)">
                    <template #default="scope">
                      <div v-preventReClick @click="handleSelectTime(key, scope.$index, scope.row)" style="width: 100%; height: 100%" :class="scope.row.workTime && scope.row.workTime.length && scope.row.workTime.includes(key) ? 'sun' : 'moon'">
                        <!-- <span>{{ (scope.row.workTime[key], scope.row.workTime.includes(key)) }}</span> -->
                        <!-- <el-button type="text" style="font-size: 30px"> -->
                        <el-icon v-if="scope.row.workTime && scope.row.workTime.length && scope.row.workTime.includes(key)" class="tw-text-white"><Sunny></Sunny></el-icon>
                        <el-icon v-else class="tw-text-black"><Moon></Moon></el-icon>
                        <!-- </el-button> -->
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item style="width: 100%">
          <el-row style="width: 100%">
            <el-col class="bold" :span="2" style="text-align: right">{{ $t("alarmMerge.DowngradeConfiguration") }}</el-col>
          </el-row>
          <el-row :gutter="20" style="width: 100%">
            <el-col :span="8">
              <el-card>
                <div>
                  <span>{{ $t("alarmMerge.WeekdayDowngradePolicyConfiguration") }}</span>
                </div>
                <el-row>
                  <el-col>{{ $t("alarmMerge.DuringWorkingHours") }}<el-button type="text" icon="el-icon-sunny"></el-button></el-col>
                </el-row>

                <el-row style="display: flex">
                  <el-col :span="12"> {{ $t("alarmMerge.SelectPriorityDowngradeLevel") }}</el-col>
                  <el-col :span="3" style="text-align: right">
                    <el-switch v-model="workingHours.degradeInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                  </el-col>
                  <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                    <el-select v-model="workingHours.degradeInWorkTime" style="width: 100px">
                      <el-option v-for="item in downNumberOptions" :disabled="!workingHours.degradeInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row style="display: flex">
                  <el-col :span="10"> {{ $t("alarmMerge.ProhibitPriorityBelow") }}</el-col>
                  <el-col :span="5" style="text-align: right">
                    <el-switch v-model="workingHours.forbidInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                  </el-col>
                  <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                    <el-select v-model="workingHours.forbidInWorkTime" style="width: 100px">
                      <el-option v-for="item in downLevelOptions" :disabled="!workingHours.forbidInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col>{{ $t("alarmMerge.OutsideWorkingHours") }}<el-button type="text" icon="el-icon-moon"></el-button></el-col>
                </el-row>
                <el-row style="display: flex">
                  <el-col :span="12"> {{ $t("alarmMerge.SelectPriorityDowngradeLevel") }}</el-col>
                  <el-col :span="3" style="text-align: right">
                    <el-switch v-model="workingHours.degradeOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                  </el-col>
                  <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                    <el-select v-model="workingHours.degradeOutWorkTime" style="width: 100px">
                      <el-option v-for="item in downNumberOptions" :disabled="!workingHours.degradeOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row style="display: flex">
                  <el-col :span="10"> {{ $t("alarmMerge.ProhibitPriorityBelow") }}</el-col>
                  <el-col :span="5" style="text-align: right">
                    <el-switch v-model="workingHours.forbidOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                  </el-col>
                  <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                    <el-select v-model="workingHours.forbidOutWorkTime" style="width: 100px">
                      <el-option v-for="item in downLevelOptions" :disabled="!workingHours.forbidOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <span>*{{ $t("alarmMerge.MinimumPriorityCanOnlyBeDowngradedToP7") }}</span>
                </el-row>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card>
                <div>
                  <span>{{ $t("alarmMerge.SaturdayDowngradeConfiguration") }}</span>
                </div>
                <el-row>
                  <el-col>{{ $t("alarmMerge.DuringWorkingHours") }}<el-button type="text" icon="el-icon-sunny"></el-button></el-col>
                </el-row>

                <el-row style="display: flex">
                  <el-col :span="12"> {{ $t("alarmMerge.SelectPriorityDowngradeLevel") }}</el-col>
                  <el-col :span="3" style="text-align: right">
                    <el-switch v-model="saturdayHours.degradeInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                  </el-col>
                  <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                    <el-select v-model="saturdayHours.degradeInWorkTime" style="width: 100px">
                      <el-option v-for="item in downNumberOptions" :disabled="!saturdayHours.degradeInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row style="display: flex">
                  <el-col :span="10"> {{ $t("alarmMerge.ProhibitPriorityBelow") }}</el-col>
                  <el-col :span="5" style="text-align: right">
                    <el-switch v-model="saturdayHours.forbidInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                  </el-col>
                  <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                    <el-select v-model="saturdayHours.forbidInWorkTime" style="width: 100px">
                      <el-option v-for="item in downLevelOptions" :disabled="!saturdayHours.forbidInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col>{{ $t("alarmMerge.OutsideWorkingHours") }}<el-button type="text" icon="el-icon-moon"></el-button></el-col>
                </el-row>
                <el-row style="display: flex">
                  <el-col :span="12"> {{ $t("alarmMerge.SelectPriorityDowngradeLevel") }}</el-col>
                  <el-col :span="3" style="text-align: right">
                    <el-switch v-model="saturdayHours.degradeOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                  </el-col>
                  <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                    <el-select v-model="saturdayHours.degradeOutWorkTime" style="width: 100px">
                      <el-option v-for="item in downNumberOptions" :disabled="!saturdayHours.degradeOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row style="display: flex">
                  <el-col :span="10"> {{ $t("alarmMerge.ProhibitPriorityBelow") }}</el-col>
                  <el-col :span="5" style="text-align: right">
                    <el-switch v-model="saturdayHours.forbidOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                  </el-col>
                  <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                    <el-select v-model="saturdayHours.forbidOutWorkTime" style="width: 100px">
                      <el-option v-for="item in downLevelOptions" :disabled="!saturdayHours.forbidOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <span>*{{ $t("alarmMerge.MinimumPriorityCanOnlyBeDowngradedToP7") }}</span>
                </el-row>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card>
                <div>
                  <span>{{ $t("alarmMerge.SundayDowngradeConfiguration") }}</span>
                </div>
                <el-row>
                  <el-col>{{ $t("alarmMerge.DuringWorkingHours") }}<el-button type="text" icon="el-icon-sunny"></el-button></el-col>
                </el-row>

                <el-row style="display: flex">
                  <el-col :span="12"> {{ $t("alarmMerge.SelectPriorityDowngradeLevel") }}</el-col>
                  <el-col :span="3" style="text-align: right">
                    <el-switch v-model="sundayHours.degradeInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                  </el-col>
                  <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                    <el-select v-model="sundayHours.degradeInWorkTime" style="width: 100px">
                      <el-option v-for="item in downNumberOptions" :disabled="!sundayHours.degradeInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row style="display: flex">
                  <el-col :span="10"> {{ $t("alarmMerge.ProhibitPriorityBelow") }}</el-col>
                  <el-col :span="5" style="text-align: right">
                    <el-switch v-model="sundayHours.forbidInWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                  </el-col>
                  <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                    <el-select v-model="sundayHours.forbidInWorkTime" style="width: 100px">
                      <el-option v-for="item in downLevelOptions" :disabled="!sundayHours.forbidInWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col>{{ $t("alarmMerge.OutsideWorkingHours") }}<el-button type="text" icon="el-icon-moon"></el-button></el-col>
                </el-row>
                <el-row style="display: flex">
                  <el-col :span="12"> {{ $t("alarmMerge.SelectPriorityDowngradeLevel") }}</el-col>
                  <el-col :span="3" style="text-align: right">
                    <el-switch v-model="sundayHours.degradeOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                  </el-col>
                  <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                    <el-select v-model="sundayHours.degradeOutWorkTime" style="width: 100px">
                      <el-option v-for="item in downNumberOptions" :disabled="!sundayHours.degradeOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row style="display: flex">
                  <el-col :span="10"> {{ $t("alarmMerge.ProhibitPriorityBelow") }}</el-col>
                  <el-col :span="5" style="text-align: right">
                    <el-switch v-model="sundayHours.forbidOutWorkTimeEnable" active-color="#13ce66" inactive-color="#DCDCDC"> </el-switch>
                  </el-col>
                  <el-col :span="9" style="padding-left: 10px; box-sizing: border-box">
                    <el-select v-model="sundayHours.forbidOutWorkTime" style="width: 100px">
                      <el-option v-for="item in downLevelOptions" :disabled="!sundayHours.forbidOutWorkTimeEnable" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <span>*{{ $t("alarmMerge.MinimumPriorityCanOnlyBeDowngradedToP7") }}</span>
                </el-row>
              </el-card>
            </el-col>
            <el-col :style="{ textAlign: 'center', marginTop: '10px' }">
              <el-button type="primary" v-preventReClick @click="handleSubmit">{{ t("glob.Save") }}</el-button>
            </el-col>
          </el-row>
        </el-form-item>
      </el-row>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";

import { useI18n } from "vue-i18n";

import timeZone from "@/views/pages/common/zone.json";

import { Sunny, Moon } from "@element-plus/icons-vue";

import { getTicketDegrade as getData, setTicketDegrade as setData, OrderGroup } from "@/views/pages/apis/orderGroup";
import { ElMessage } from "element-plus";

interface Props {
  detail: OrderGroup;
}

const props = withDefaults(defineProps<Props>(), { detail: () => ({}) as OrderGroup });

const { t } = useI18n();

const effectTimeCfg = ref({
  timeZone: "设备默认",
  useCustomerTimeZone: false,
  useDeviceTimeZone: false,
  coverWorkTime: [
    {
      week: "周一",
      workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      weekDay: 1,
    },
    {
      week: "周二",
      workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      weekDay: 2,
    },
    {
      week: "周三",
      workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      weekDay: 3,
    },
    {
      week: "周四",
      workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      weekDay: 4,
    },
    {
      week: "周五",
      workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      weekDay: 5,
    },
    {
      week: "周六",
      workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      weekDay: 6,
    },
    {
      week: "周日",
      workTime: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      weekDay: 7,
    },
  ],
  degradeConfigs: [],
});

const downNumberOptions = ref([
  {
    label: 1,
    value: 1,
  },
  {
    label: 2,
    value: 2,
  },
  {
    label: 3,
    value: 3,
  },
  {
    label: 4,
    value: 4,
  },
  {
    label: 5,
    value: 5,
  },
  {
    label: 6,
    value: 6,
  },
]);
const downLevelOptions = ref([
  {
    label: "P1",
    value: "1",
  },
  {
    label: "P2",
    value: "2",
  },
  {
    label: "P3",
    value: "3",
  },
  {
    label: "P4",
    value: "4",
  },
  {
    label: "P5",
    value: "5",
  },
  {
    label: "P6",
    value: "6",
  },
]);

const workingHours = ref({
  degradeInWorkTime: "", //工作时间降级数
  degradeInWorkTimeEnable: false, //工作时间降级数是否生效
  forbidInWorkTime: "", //工作时间禁止下降的优先级
  forbidInWorkTimeEnable: false, //工作时间优先级是否生效
  degradeOutWorkTime: "", //非工作时间降级数
  degradeOutWorkTimeEnable: false, //非工作时间降级数是否生效
  forbidOutWorkTime: "", //非工作时间禁止下降的优先级
  forbidOutWorkTimeEnable: false, //非工作时间优先级是否生效
});
const saturdayHours = ref({
  degradeInWorkTime: "", //工作时间降级数
  degradeInWorkTimeEnable: false, //工作时间降级数是否生效
  forbidInWorkTime: "", //工作时间禁止下降的优先级
  forbidInWorkTimeEnable: false, //工作时间优先级是否生效
  degradeOutWorkTime: "", //非工作时间降级数
  degradeOutWorkTimeEnable: false, //非工作时间降级数是否生效
  forbidOutWorkTime: "", //非工作时间禁止下降的优先级
  forbidOutWorkTimeEnable: false, //非工作时间优先级是否生效
});
const sundayHours = ref({
  degradeInWorkTime: "", //工作时间降级数
  degradeInWorkTimeEnable: false, //工作时间降级数是否生效
  forbidInWorkTime: "", //工作时间禁止下降的优先级
  forbidInWorkTimeEnable: false, //工作时间优先级是否生效
  degradeOutWorkTime: "", //非工作时间降级数
  degradeOutWorkTimeEnable: false, //非工作时间降级数是否生效
  forbidOutWorkTime: "", //非工作时间禁止下降的优先级
  forbidOutWorkTimeEnable: false, //非工作时间优先级是否生效
});

const allBool = ref([true, true, true, true, true, true, true]);

function handleSelectTime(key, weekIndex, row) {
  if (key === "all") {
    allBool.value[weekIndex] = !allBool.value[weekIndex];
    let data: number[] = [];
    for (let i = 0; i < 24; i++) {
      data.push(i);
    }
    row.workTime = [...new Set(data)];
    if (!allBool.value[weekIndex]) {
      row.workTime = [];
    }
  } else {
    const index = row.workTime.indexOf(key);
    if (index == -1) {
      row.workTime.push(key);
    } else row.workTime.splice(index, 1);
  }
}

function handleClick({ column }) {
  let index = Number(column.label);
  const { coverWorkTime } = effectTimeCfg.value;

  let workTime = [];
  coverWorkTime.forEach((v) => {
    workTime = workTime.concat(v.workTime as any);
  });

  const isActive = workTime.includes(index as never) && workTime.filter((v) => v === index).length === 7; // 是否激活
  // console.log(isActive);

  coverWorkTime.forEach((v: Record<string, any>) => {
    let delIndex = v.workTime.indexOf(index);
    if (isActive) {
      v.workTime.splice(delIndex, 1);
    } else {
      v.workTime.push(index);
    }

    v.workTime = [...new Set(v.workTime.sort((a, b) => a - b))];
  });
}

const tableWidth = ref(0);

const tableContentRef = ref();

function resizeTable() {
  let width = tableContentRef.value && tableContentRef.value.offsetWidth;
  tableWidth.value = Number(((width - 80) / 24).toFixed(0));
}

async function handleSubmit() {
  try {
    if (effectTimeCfg.value.timeZone === "客户默认") {
      effectTimeCfg.value.useCustomerTimeZone = true;
      effectTimeCfg.value.useDeviceTimeZone = false;
    } else if (effectTimeCfg.value.timeZone === "设备默认") {
      effectTimeCfg.value.useDeviceTimeZone = true;
      effectTimeCfg.value.useCustomerTimeZone = false;
    } else {
      effectTimeCfg.value.useCustomerTimeZone = false;
      effectTimeCfg.value.useDeviceTimeZone = false;
    }

    const params = {
      ticketGroupId: props.detail.id,
      effectTimeCfg: {
        timeZone: effectTimeCfg.value.timeZone,
        useCustomerTimeZone: effectTimeCfg.value.useCustomerTimeZone,
        useDeviceTimeZone: effectTimeCfg.value.useDeviceTimeZone,
        coverWorkTime: effectTimeCfg.value.coverWorkTime.map((v) => ({ weekDay: v.weekDay, workTime: v.workTime })),
        degradeConfigs: [
          /*  */
          Object.assign(workingHours.value, { weekType: "WORKDAY" }),
          Object.assign(saturdayHours.value, { weekType: "SATURDAY" }),
          Object.assign(sundayHours.value, { weekType: "SUNDAY" }),
        ],
      },
    };
    const { message, success } = await setData({ ...params });
    if (!success) throw new Error(message);
    ElMessage.success(t("axios.Operation successful"));
    handleRefresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

async function handleRefresh() {
  try {
    const { data, message, success } = await getData({ ticketGroupId: props.detail.id });
    if (!success) throw new Error(message);
    effectTimeCfg.value.timeZone = data.effectTimeCfg.timeZone;
    effectTimeCfg.value.useCustomerTimeZone = data.effectTimeCfg.useCustomerTimeZone;
    effectTimeCfg.value.useDeviceTimeZone = data.effectTimeCfg.useDeviceTimeZone;
    effectTimeCfg.value.coverWorkTime = data.effectTimeCfg.coverWorkTime.map((v) => Object.assign(effectTimeCfg.value.coverWorkTime.find((f) => Number(f.weekDay) === Number(v.weekDay)) || {}, { workTime: v.workTime })) as any;
    for (let i = 0; i < data.effectTimeCfg.degradeConfigs.length; i++) {
      const _item = data.effectTimeCfg.degradeConfigs[i];
      Object.assign({ WORKDAY: workingHours.value, SATURDAY: saturdayHours.value, SUNDAY: sundayHours.value }[_item.weekType], {
        degradeInWorkTime: _item.degradeInWorkTime, //工作时间降级数
        degradeInWorkTimeEnable: _item.degradeInWorkTimeEnable, //工作时间降级数是否生效
        forbidInWorkTime: (_item.forbidInWorkTime || "").toString(), //工作时间禁止下降的优先级
        forbidInWorkTimeEnable: _item.forbidInWorkTimeEnable, //工作时间优先级是否生效
        degradeOutWorkTime: _item.degradeOutWorkTime, //非工作时间降级数
        degradeOutWorkTimeEnable: _item.degradeOutWorkTimeEnable, //非工作时间降级数是否生效
        forbidOutWorkTime: (_item.forbidOutWorkTime || "").toString(), //非工作时间禁止下降的优先级
        forbidOutWorkTimeEnable: _item.forbidOutWorkTimeEnable, //非工作时间优先级是否生效
      });
    }
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}
onMounted(() => {
  resizeTable();
  //根据屏幕缩放自动获取页面宽高
  window.onresize = () => resizeTable();

  handleRefresh();
});
</script>

<style scoped lang="scss">
.alarm-down-config {
  :deep(.alarm-table) {
    .cell {
      padding: 0 !important;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      // line-height: 50px;
    }
    .el-table__cell {
      padding: 0 !important;
      height: 50px;
      cursor: pointer;
    }
  }
  .elstyle-card {
    margin-top: 20px;
  }
  .sun {
    font-size: 30px;
    background: rgb(26, 190, 107);
    display: flex;
    align-items: center;
    justify-content: center;

    :deep() .elstyle-button--text {
      color: #fff;
    }
  }
  .moon {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;

    background: #fff;

    :deep() .elstyle-button--text {
      color: rgb(153, 153, 153);
    }
  }

  :deep(.el-card-table) {
    .cell {
      padding: 0 12px !important;
      height: 50px;
      display: flex;
      align-items: left;
      justify-content: left;
    }
  }
}
.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}
// ::v-deep .elstyle-form-item__content .el-form-item-content {
//   display: flex;
//   flex-direction: column;
// }
</style>

<style scoped>
.custom-message-box {
  width: 600px;
  /* maxwidth: 90%; */
  border-radius: 10px;
}
</style>
