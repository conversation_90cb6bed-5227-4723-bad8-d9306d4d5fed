<template>
  <div class="tw-mt-[8px]" v-for="(flag, flagIdx) in flags" :key="`${flagIdx}-${flag.flag}`">
    <div class="tw-mb-[8px] tw-flex tw-items-center tw-justify-between">
      <span class="tw-text-[14px] tw-font-bold">{{ flag.title }}</span>
      <el-button v-if="userInfo.hasPermission(服务管理中心_工单组_分配发送策略)" type="primary" :icon="Plus" :loading="state[flag.flag].loading" @click="handleEditor('add', flag.flag)">{{ t("glob.Add Data", { value: flag.title }) }}</el-button>
    </div>
    <el-table :data="state[flag.flag].data" border v-loading="state[flag.flag].loading">
      <el-table-column :label="t('orderGroup.sendStrategyName')" prop="sendPolicyName"></el-table-column>
      <el-table-column :label="t('orderGroup.Sequence')" prop="index">
        <template #default="{ row }">
          <div v-if="!row.isEdit">{{ row.index }}</div>
          <el-input-number v-else v-model="row.index" :min="1" />
        </template>
      </el-table-column>

      <el-table-column :label="t('glob.operate')" width="120" v-if="userInfo.hasPermission(服务管理中心_工单组_分配发送策略)">
        <template #default="{ row }">
          <template v-if="row.isEdit">
            <el-button type="primary" link @click="handleEditor('edit', flag.flag, row)">{{ t("glob.Confirm") }}</el-button>
            <el-button type="primary" link @click="handleRefresh(flag.flag)">{{ t("glob.Cancel") }}</el-button>
          </template>
          <template v-else>
            <el-button type="primary" link @click="() => (state[flag.flag].data.find((v: any) => v.isEdit) ? ElMessage.warning(t('orderGroup.Please save the information that you are currently editing first')) : (row.isEdit = true))">{{ t("glob.edit") }}</el-button>
            <el-button type="danger" link @click="handleRemoveItem(flag.flag, row)">{{ t("glob.remove") }}</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineComponent, h, nextTick, onMounted, reactive } from "vue";
import { useI18n } from "vue-i18n";
import { Plus } from "@element-plus/icons-vue";
import { ElForm, ElFormItem, ElMessage, ElMessageBox, ElOption, ElSelect, FormInstance, Action, MessageBoxState } from "element-plus";
import { getEmailStrategyList, getMsgStrategyList } from "@/views/pages/apis/SendPolicy";
import {
  /*  */
  OrderGroup,
  SendPolicyItem,
  addSendPolicy as addData,
  getSendPolicy as getData,
  setSendPolicy as setData,
  delSendPolicy as delData,
} from "@/views/pages/apis/orderGroup";
import getUserInfo from "@/utils/getUserInfo";

import { 服务管理中心_工单组_分配发送策略 } from "@/views/pages/permission";

interface Props {
  detail: OrderGroup;
}

const props = withDefaults(defineProps<Props>(), { detail: () => ({}) as OrderGroup });

const userInfo = getUserInfo();

const { t } = useI18n();

enum Types {
  sms = "sms",
  mail = "mail",
}

type Item = SendPolicyItem;

type State = Record<keyof typeof Types, { data: Item[]; loading: boolean }>;

const state = reactive<State>({
  [Types.sms]: { data: [], loading: false },
  [Types.mail]: { data: [], loading: false },
});

const flags = computed(() => [
  /*  */
  {
    flag: Types.sms,
    title: t("orderGroup.Send strategy", { type: t("orderGroup.Sms") }),
  },
  {
    flag: Types.mail,
    title: t("orderGroup.Send strategy", { type: t("orderGroup.Email") }),
  },
]);

async function handleRemoveItem(type: keyof typeof Types, row: Item) {
  ElMessageBox.confirm(t("orderGroup.Are you sure you want to remove the", { name: row.sendPolicyName, type: (flags.value.find((v) => v.flag === type) || {}).title }), t("glob.remove"), {
    confirmButtonText: t("glob.Confirm"),
    cancelButtonText: t("glob.Cancel"),
    type: "warning",
    beforeClose: async (action: Action, instance: MessageBoxState, done: () => void) => {
      if (action === "confirm") {
        try {
          const { success, message } = await delData({ id: row.id });
          if (!success) throw new Error(message);
          ElMessage.success(t("axios.Operation successful"));
          handleRefresh(type);
          done();
        } catch (error) {
          error instanceof Error && ElMessage.error(error.message);
        }
      } else done();
    },
  })
    .then(() => {})
    .catch(() => {});
}

const form = ref({ ticketGroupId: props.detail.id, sendPolicyId: "", sendPolicyName: "", sendPolicyType: "" });
const formRef = ref<FormInstance>();
const strategyOption = ref([]);
async function handleEditor(operate: "add" | "edit", type: keyof typeof Types, row?: Item) {
  switch (operate) {
    case "add":
      state[type].loading = true;
      form.value.sendPolicyType = type;
      const currentType = flags.value.find((v) => v.flag === type) || ({} as any);
      const title = t("glob.Add Data", { value: currentType.title });
      const { data: strategyData, message: strategyMessage, success: strategySuccess } = await (type === Types.sms ? getMsgStrategyList : getEmailStrategyList)(Object.assign({ pageNumber: 1, pageSize: 99999, containerId: (userInfo.currentTenant || {}).containerId }, type === Types.sms ? { queryPermissionId: "612172715167580160", verifyPermissionIds: "612172695882170368,612172734373298176,612172749170802688" } : { queryPermissionId: "612173058102263808", verifyPermissionIds: "612173033284567040,612173103207809024,612173126817546240" }));

      if (!strategySuccess) throw new Error(strategyMessage);
      strategyOption.value = strategyData as any;
      await nextTick();
      ElMessageBox({
        title,
        message: h(
          defineComponent({
            setup() {
              return () =>
                h(ElForm, { model: form.value, ref: (el) => (formRef.value = el as FormInstance), rules: { sendPolicyId: [{ required: true, message: t("orderGroup.Please select the", { title: currentType.title }), trigger: ["blur", "change"] }] } }, [
                  h(
                    ElFormItem,
                    { prop: "sendPolicyId" },
                    h(
                      ElSelect,
                      {
                        "modelValue": form.value.sendPolicyId,
                        "onUpdate:modelValue": (v) => {
                          form.value.sendPolicyId = v;
                          form.value.sendPolicyName = (strategyOption.value.find((f: any) => f.id === v) || ({} as any)).name;
                        },
                      },
                      strategyOption.value.map((v: any) => h(ElOption, { label: v.name, value: v.id }))
                    )
                  ),
                ]);
            },
          })
        ),
        showCancelButton: true,
        beforeClose(action: Action, instance: MessageBoxState, done: () => void) {
          if (action === "confirm") {
            formRef.value &&
              formRef.value.validate(async (valid) => {
                try {
                  if (!valid) return;
                  const { message, success } = await addData({ ...form.value, index: state[type].data.length + 1 });
                  if (!success) throw new Error(message);
                  ElMessage.success(t("axios.Operation successful"));
                  formRef.value && formRef.value.resetFields();
                  await nextTick();
                  done();
                  handleRefresh(type);
                } catch (error) {
                  error instanceof Error && ElMessage.error(error.message);
                }
              });
          } else {
            formRef.value && formRef.value.resetFields();
            nextTick(() => done());
          }
        },
      })
        .then(() => {})
        .catch(() => {
          state[type].loading = false;
        });
      break;
    case "edit":
      try {
        if (!row) return;
        if (!row.index) return ElMessage.error("顺序不能为空");
        const params = {
          id: row.id,
          index: row.index,
          sendPolicyType: type,
          ticketGroupId: props.detail.id,
        };
        const { message, success } = await setData({ ...params });
        if (!success) throw new Error(message);
        ElMessage.success(t("axios.Operation successful"));
        handleRefresh(type);
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
      break;
    default:
      break;
  }
}

async function handleRefresh(sendPolicyType: keyof typeof Types) {
  try {
    state[sendPolicyType].loading = true;
    const { data, message, success } = await getData({ ticketGroupId: props.detail.id, sendPolicyType });
    if (!success) throw new Error(message);
    state[sendPolicyType].data = (data as any).map((v) => Object.assign(v, { isEdit: false }));
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    state[sendPolicyType].loading = false;
  }
}

onMounted(() => {
  Promise.all(flags.value.map((v) => handleRefresh(v.flag)));
});
</script>
