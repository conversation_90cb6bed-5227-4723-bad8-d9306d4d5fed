<template>
  <div>
    <div class="tw-mb-[8px] tw-flex tw-items-center tw-justify-between">
      <span class="tw-text-[14px] tw-font-bold">{{ t("orderGroup.User groups Config") }}</span>
      <el-button v-if="userInfo.hasPermission(服务管理中心_工单组_分配用户组)" type="primary" :icon="Plus" :loading="state.loading" @click="handleAddUserGroup">{{ t("glob.Add Data", { value: t("userGroup.User group") }) }}</el-button>
    </div>
    <el-table :data="state.data" border v-loading="state.loading">
      <el-table-column :label="t('userGroup.User group')" prop="userGroupName">
        <template #default="{ row }">
          <div>{{ row.userGroupName }}[{{ row.abbreviation }}]</div>
        </template>
      </el-table-column>
      <el-table-column :label="t('orderGroup.Assignable work order types')" prop="assignableTicketType">
        <template #default="{ row }">
          <div v-if="!row.isEdit">
            <el-tag class="tw-mb-2 tw-mr-2" v-for="label in row.assignableTicketType instanceof Array && row.assignableTicketType.length ? assignableTicketTypeOption.filter((v) => row.assignableTicketType.includes(v.value)).map((v) => v.label) : []" :key="`${row.id}-${label}`">{{ label }}</el-tag>
          </div>
          <el-select v-else v-model="row.assignableTicketType" multiple>
            <el-option v-for="item in assignableTicketTypeOption" :key="`assignableTicketType-${item.value}`" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column :label="t('orderGroup.Change approval types')" prop="changeApprovalType">
        <template #default="{ row }">
          <div v-if="!row.isEdit">
            <!-- {{
              row.changeApprovalType instanceof Array && row.changeApprovalType.length
                ? changeApprovalTypeOption
                    .filter((v) => row.changeApprovalType.includes(v.value))
                    .map((v) => v.label)
                    .join(",")
                : ""
            }} -->

            <el-tag class="tw-mb-2 tw-mr-2" v-for="item in row.changeApprovalType instanceof Array && row.changeApprovalType.length ? changeApprovalTypeOption.filter((v) => row.changeApprovalType.includes(v.value)) : []" :key="`${row.id}-changeApprovalType-${item.value}`" :type="item.type">{{ item.label }}</el-tag>
          </div>
          <el-select v-else v-model="row.changeApprovalType" multiple>
            <el-option v-for="item in changeApprovalTypeOption" :key="`changeApprovalType-${item.value}`" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column :label="t('orderGroup.Release approval types')" prop="releaseApprovalType">
        <template #default="{ row }">
          <div v-if="!row.isEdit">
            <!-- {{
              row.releaseApprovalType instanceof Array && row.releaseApprovalType.length
                ? releaseApprovalTypeOption
                    .filter((v) => row.releaseApprovalType.includes(v.value))
                    .map((v) => v.label)
                    .join(",")
                : "--"
            }} -->

            <el-tag class="tw-mb-2 tw-mr-2" v-for="item in row.releaseApprovalType instanceof Array && row.releaseApprovalType.length ? releaseApprovalTypeOption.filter((v) => row.releaseApprovalType.includes(v.value)) : []" :key="`${row.id}-releaseApprovalType-${item.value}`" :type="item.type">{{ item.label }}</el-tag>
          </div>
          <el-select v-else v-model="row.releaseApprovalType" multiple>
            <el-option v-for="item in releaseApprovalTypeOption" :key="`releaseApprovalType-${item.value}`" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column :label="t('orderGroup.Types of work orders that can be closed')" prop="closeTicket">
        <template #header>
          <div class="tw-flex tw-items-center">
            {{ t("orderGroup.Types of work orders that can be closed") }}
            <el-button type="info" link :icon="QuestionFilled" @click="handleOpenHelp('closeTicket')"></el-button>
          </div>
        </template>
        <template #default="{ row }">
          <div v-if="!row.isEdit">
            <el-tag class="tw-mb-2 tw-mr-2" v-for="label in row.closeTicketType instanceof Array && row.closeTicketType.length ? assignableTicketTypeOption.filter((v) => row.closeTicketType.includes(v.value)).map((v) => v.label) : []" :key="`${row.id}-${label}`">{{ label }}</el-tag>
          </div>
          <el-select v-else v-model="row.closeTicketType" multiple>
            <el-option v-for="item in assignableTicketTypeOption" :key="`closeTicketType-${item.value}`" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <!-- <template v-if="row.isEdit">
            <el-checkbox v-model="row.closeTicket" />
          </template>
          <span v-else>{{ row.closeTicket ? "✔" : "" }}</span> -->
        </template>
      </el-table-column>
      <el-table-column :label="t('orderGroup.Maximum suspension time')" prop="maxSuspensionTime">
        <template #header>
          <div class="tw-flex tw-items-center">
            {{ t("orderGroup.Maximum suspension time") }}
            <el-button type="info" link :icon="QuestionFilled" @click="handleOpenHelp('maxSuspensionTime')"></el-button>
          </div>
        </template>
        <template #default="{ row }">
          <div v-if="!row.isEdit">{{ (maxSuspensionTimeOption.find((v) => v.value === row.maxSuspensionTime) || {}).label || row.maxSuspensionTime }}</div>
          <!-- <el-input-number v-else v-model="row.maxSuspensionTime" :min="0" :max="56" /> -->
          <el-select v-else v-model="row.maxSuspensionTime">
            <el-option v-for="item in maxSuspensionTimeOption" :key="`maxSuspensionTime-${item.value}`" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column :label="t('orderGroup.Default work order assignment group')" prop="defaultTicketGroup">
        <template #header>
          <div class="tw-flex tw-items-center">
            {{ t("orderGroup.Default work order assignment group") }}
            <el-button type="info" link :icon="QuestionFilled" @click="handleOpenHelp('defaultTicketGroup')"></el-button>
          </div>
        </template>
        <template #default="{ row }">
          <template v-if="row.isEdit">
            <el-checkbox v-model="row.defaultTicketGroup" />
          </template>
          <span v-else>{{ row.defaultTicketGroup ? "✔" : "" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('orderGroup.Handled alarm types')" prop="handleAlarmType">
        <template #header>
          <div class="tw-flex tw-items-center">
            {{ t("orderGroup.Handled alarm types") }}
            <el-button type="info" link :icon="QuestionFilled" @click="handleOpenHelp('handleAlarmType')"></el-button>
          </div>
        </template>
        <template #default="{ row }">
          <div v-if="!row.isEdit">{{ row.handleAlarmType }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="t('glob.operate')" width="120" v-if="userInfo.hasPermission(服务管理中心_工单组_分配用户组)">
        <template #default="{ row }">
          <template v-if="row.isEdit">
            <el-button type="primary" link @click="handleEditor(row)">{{ t("glob.Confirm") }}</el-button>
            <el-button type="primary" link @click="handleRefresh">{{ t("glob.Cancel") }}</el-button>
          </template>
          <template v-else>
            <el-button type="primary" link @click="() => (state.data.find((v: any) => v.isEdit) ? ElMessage.warning(t('orderGroup.Please save the information that you are currently editing first')) : (row.isEdit = true))">{{ t("glob.edit") }}</el-button>
            <el-button type="danger" link @click="handleRemoveItem(row)">{{ t("glob.remove") }}</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>

    <HelpDialog v-model="showHelp" :title="helpTitle" :content="helpContent" />
  </div>
</template>

<script setup lang="ts">
import { ref, h, defineComponent, computed, nextTick, reactive, onMounted } from "vue";
import { useI18n } from "vue-i18n";

import { ElForm, ElFormItem, ElMessage, ElMessageBox, ElOption, ElSelect, Action, MessageBoxState, FormInstance } from "element-plus";
import { Plus, QuestionFilled } from "@element-plus/icons-vue";

import getUserInfo from "@/utils/getUserInfo";

/* help */
import HelpDialog from "../help";

import {
  /* api */
  getUserGroups,
  OrderGroup,
  UserGroupConfigurationItem,
  getUserGroupConfiguration as getData,
  addUserGroupConfiguration as addData,
  setUserGroupConfiguration as setData,
  delUserGroupConfiguration as delData,
  AssignableTicketType,
  ChangeApprovalType,
  ReleaseApprovalType,
  assignableTicketTypeOption,
  changeApprovalTypeOption,
  releaseApprovalTypeOption,
  maxSuspensionTimeOption,
} from "@/views/pages/apis/orderGroup";

import { 服务管理中心_工单组_分配用户组 } from "@/views/pages/permission";

interface Props {
  detail: OrderGroup;
}

const props = withDefaults(defineProps<Props>(), { detail: () => ({}) as OrderGroup });

const userInfo = getUserInfo();

const { t } = useI18n();

type Item = UserGroupConfigurationItem;

interface State<T> {
  data: T[];
  loading: boolean;
}

const state = reactive<State<Item>>({
  data: [],
  loading: false,
});

const form = ref({
  tenantId: (userInfo.currentTenant || {}).id,
  tenantName: (userInfo.currentTenant || {}).name,
  userGrouptId: "",
  userGroupName: "",
  ticketGroupId: props.detail.id,
  ticketGroupName: props.detail.ticketGroupName,
  abbreviation: "",
});

const formRef = ref<FormInstance>();
const userGroup = ref<any>([]);

const showHelp = ref(false);
const helpTitle = ref<string>("");
const helpContent = ref<string[]>([]);
const helpOption = {
  closeTicket: {
    title: "可关闭工单类型",
    content: [
      /*  */
      "可定义当前用户组能否执行关闭工单操作。",
      "配置了对应的工单类型，就可关闭该类型的工单。",
      "否则，无法关闭。",
    ],
  },
  maxSuspensionTime: {
    title: "最长挂起时间",
    content: [
      /*  */
      "1、最长挂起时间：不同的用户组，其工单可直接挂起的时间支持自定义。",
      "例如：用户组A工单直接挂起的时间为24小时，如果该用户组下的用户工单挂起时间，选择时超过24小时，就需要审批。如果业务只要有挂起，就要审批，其最长挂起时间需设置为0。",
    ],
  },
  defaultTicketGroup: {
    title: "默认工单分派组",
    content: [
      /*  */
      "勾选了此选项，代表告警自动生成的事件单，可分配给该客户组。一个工单组下，只能定义一个默认的工单自动分配组。如若不设置，告警自动生成的事件单下，不会展示分配的用户组信息。",
    ],
  },
  handleAlarmType: {
    title: "处理的告警类型",
    content: [
      /*  */
      "可按照告警分类的类型将工单分配到对应的用户组。",
      "一个用户组，可以处理多个告警类型的工单，但是一种告警类型的工单，只能分配给一个组，不能分配给多个用户组。",
    ],
  },
};

async function handleOpenHelp(type: "closeTicket" | "maxSuspensionTime" | "defaultTicketGroup" | "handleAlarmType") {
  helpTitle.value = helpOption[type].title;
  helpContent.value = helpOption[type].content;
  await nextTick();
  showHelp.value = true;
}

async function handleEditor(row: Item) {
  try {
    const params = {
      id: row.id,
      assignableTicketType: row.assignableTicketType || [],
      changeApprovalType: row.changeApprovalType || [],
      releaseApprovalType: row.releaseApprovalType || [],
      closeTicket: row.closeTicket,
      maxSuspensionTime: row.maxSuspensionTime,
      defaultTicketGroup: row.defaultTicketGroup,
      handleAlarmType: row.handleAlarmType,
      closeTicketType: row.closeTicketType,
    };

    const { message, success } = await setData({ ...params });
    if (!success) throw new Error(message);
    ElMessage.success(t("axios.Operation successful"));
    handleRefresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

function handleRemoveItem(row: Item) {
  ElMessageBox.confirm(t("orderGroup.Are you sure you want to remove the user group", { name: row.userGroupName }), t("glob.delete"), {
    confirmButtonText: t("glob.Confirm"),
    cancelButtonText: t("glob.Cancel"),
    type: "warning",
    beforeClose: async (action: Action, instance: MessageBoxState, done: () => void) => {
      if (action === "confirm") {
        try {
          const { success, message } = await delData({ id: row.id });
          if (!success) throw new Error(message);
          ElMessage.success(t("axios.Operation successful"));
          handleRefresh();
          done();
        } catch (error) {
          error instanceof Error && ElMessage.error(error.message);
        }
      } else done();
    },
  })
    .then(() => {})
    .catch(() => {});
}

async function handleAddUserGroup() {
  state.loading = true;
  userGroup.value = await handleGetUserGroups(form.value.tenantId as string);
  ElMessageBox({
    title: t("glob.Add Data", { value: t("userGroup.User group") }),
    message: h(
      defineComponent({
        setup() {
          return () =>
            computed(() =>
              h(
                ElForm,
                {
                  ref: (el) => (formRef.value = el as FormInstance),
                  model: form.value,
                  labelWidth: "65px",
                  labelPosition: "left",
                  rules: {
                    tenantId: [{ required: true, message: t("orderGroup.Please select the customer"), trigger: ["blur", "change"] }],
                    userGrouptId: [{ required: true, message: t("orderGroup.Please select the user group"), trigger: ["blur", "change"] }],
                  },
                },
                () => [
                  h(
                    ElFormItem,
                    { label: "客户", prop: "tenantId" },
                    h(
                      ElSelect,
                      {
                        "modelValue": form.value.tenantId,
                        "onUpdate:modelValue": async (v) => {
                          form.value.tenantId = v;
                          form.value.tenantName = (userInfo.tenants.find((f) => f.id === v) || {}).name;
                          form.value.userGrouptId = "";
                          userGroup.value = await handleGetUserGroups(v);
                        },
                        "filterable": true,
                      },
                      userInfo.tenants.map((v) => h(ElOption, { value: v.id, label: `${v.name}[${v.abbreviation}]` }))
                    )
                  ),
                  h(
                    ElFormItem,
                    { label: "用户组", prop: "userGrouptId" },
                    h(
                      ElSelect,
                      {
                        "modelValue": form.value.userGrouptId,
                        "onUpdate:modelValue": (v) => {
                          form.value.userGrouptId = v;
                          const currentUserGroup = userGroup.value.find((f) => f.id === v) || {};
                          form.value.userGroupName = currentUserGroup.name;
                          form.value.abbreviation = currentUserGroup.tenantAbbreviation;
                        },
                        "filterable": true,
                      },
                      userGroup.value.map((v) =>
                        h(ElOption, { class: "tw-h-auto", value: v.id, label: `${v.name}` }, [
                          /*  */
                          h("div", { class: "tw-h-full" }, [
                            /*  */
                            h("div", { class: "tw-font-bold" }, [h("span", {}, v.name)]),
                            h("div", { class: "tw-italic" }, [h("span", {}, v.note)]),
                          ]),
                          // h(ElFormItem, { label: "描述" }, v.name),
                        ])
                      )
                    )
                  ),
                ]
              )
            ).value;
        },
      })
    ),
    showCancelButton: true,
    confirmButtonText: t("glob.Confirm"),
    cancelButtonText: t("glob.Cancel"),
    beforeClose: (action: Action, instance: MessageBoxState, done: () => void) => {
      if (action === "confirm") {
        formRef.value &&
          formRef.value.validate(async (valid: boolean) => {
            try {
              if (!valid) return;
              const { success, message } = await addData({ ...form.value });
              if (!success) throw new Error(message);
              ElMessage.success(t("axios.Operation successful"));
              formRef.value && formRef.value.resetFields();
              await nextTick();
              done();
              handleRefresh();
            } catch (error) {
              error instanceof Error && ElMessage.error(error.message);
            }
          });
      } else {
        formRef.value && formRef.value.resetFields();
        nextTick(() => done());
        state.loading = false;
      }
    },
  })
    .then(() => {})
    .catch(() => {})
    .finally(() => {});
}

async function handleGetUserGroups(tenantId: string) {
  try {
    if (!tenantId) return [];
    const { data, message, success } = await getUserGroups({ tenantId });
    if (!success) throw new Error(message);
    const existingUserGroupId = state.data.map((v) => v.userGrouptId);
    return data.filter((v) => !existingUserGroupId.includes(v.id));
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
    return [];
  }
}

async function handleRefresh() {
  try {
    if (!props.detail.id) return;
    state.loading = true;
    const { data, message, success } = await getData({ ticketGroupId: props.detail.id });
    if (!success) throw new Error(message);
    state.data = data.map((v) => Object.assign(v, { isEdit: false }));
    state.loading = false;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
    state.loading = false;
  }
}

onMounted(() => {
  handleRefresh();
});
</script>
