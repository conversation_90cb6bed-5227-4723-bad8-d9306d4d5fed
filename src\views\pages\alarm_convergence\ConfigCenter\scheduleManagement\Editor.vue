<template>
  <el-dialog v-model="dialogVisible" :title="`${isEdit ? '编辑' : '新建'}班次`" width="650" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="auto">
      <el-form-item label="班次名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入班次名称"></el-input>
      </el-form-item>
      <el-form-item label="值班开始时间" prop="dutyStartTime">
        <el-time-picker class="tw-w-full" v-model="form.dutyStartTime" placeholder="请选择值班开始时间" format="HH:mm" value-format="HH:mm" />
      </el-form-item>
      <el-form-item label="值班结束时间" prop="dutyEndTime">
        <el-time-picker class="tw-w-full" v-model="form.dutyEndTime" placeholder="请选择值班结束时间" format="HH:mm" value-format="HH:mm" />
      </el-form-item>
      <el-form-item label="自动排班" prop="autoScheduling">
        <el-switch v-model="form.autoScheduling"></el-switch>
      </el-form-item>
      <el-form-item label="主题色" prop="accentColor">
        <el-color-picker v-model="form.accentColor" show-alpha :predefine="predefineColors" />
      </el-form-item>
      <template v-if="form.autoScheduling">
        <el-form-item label="班次生效日期" prop="takeEffectStartTime">
          <el-date-picker class="tw-w-full" v-model="form.takeEffectStartTime" type="date" placeholder="请选择班次生效日期" format="YYYY-MM-DD" value-format="x" />
        </el-form-item>
        <el-form-item label="循环周期" prop="cycleTime">
          <el-select v-model="form.cycleTime" placeholder="请选择">
            <el-option v-for="item in cycleTimeOptions" :key="`cycleTime-${item.value}`" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="自定义周期" prop="frequency" v-if="form.cycleTime === CycleTime.customize">
          <div class="tw-flex tw-w-full tw-items-center tw-justify-between">
            <span>每</span>
            <el-input-number v-model="form.frequency" controls-position="right" :min="1" />
            <el-select class="tw-w-[auto]" v-model="form.frequencyType" placeholder="请选择">
              <el-option v-for="item in frequencyTypeOptions" :key="`frequencyType-${item.value}`" :label="item.label" :value="item.value" />
            </el-select>
            <el-select class="tw-w-[auto]" v-model="form.frequencyRange" placeholder="请选择">
              <el-option v-for="item in frequencyRangeOptions" :key="`frequencyRange-${item.value}`" :label="item.label" :value="item.value" />
            </el-select>
            <span class="tw-whitespace-nowrap">循环一次</span>
          </div>
        </el-form-item>
        <el-form-item label="班次结束日期" prop="takeEffectyEndTime">
          <el-date-picker class="tw-w-full" v-model="form.takeEffectyEndTime" type="date" placeholder="请选择班次结束日期" format="YYYY-MM-DD" value-format="x" />
        </el-form-item>
      </template>

      <el-form-item label="安全容器" prop="containerId" v-if="dialogVisible && !isEdit">
        <treeAuth ref="treeAuthRef" :treeStyle="treeStyle" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ t("glob.Cancel") }}</el-button>
        <el-button type="primary" @click="handleSubmit"> {{ t("glob.Confirm") }} </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, nextTick, ref } from "vue";

import { useI18n } from "vue-i18n";

import { ScheduleItem, addSchedule as addItem, setSchedule as setItem, cycleTimeOptions, CycleTime, frequencyTypeOptions, FrequencyType, frequencyRangeOptions, FrequencyRange } from "@/views/pages/apis/shiftManagement";

import { ElMessage, FormInstance, FormRules } from "element-plus";

import treeAuth from "@/components/treeAuth/index.vue";

const { t } = useI18n();

const dialogVisible = ref<boolean>(false);

const emits = defineEmits(["refresh"]);

const treeStyle = {
  width: "300px",
  height: "150px",
};

const systemPrimaryColor = getComputedStyle(document.documentElement).getPropertyValue("--el-color-primary").trim();

const form = ref<Partial<ScheduleItem>>({
  id: "",
  name: "", // 班次名称
  dutyStartTime: "", // 值班开始时间
  dutyEndTime: "", // 值班结束时间
  autoScheduling: false, // 自动排班状态
  accentColor: systemPrimaryColor, // 主题色
  takeEffectStartTime: "", // 班次生效日期
  takeEffectyEndTime: "", // 班次结束日期
  cycleTime: CycleTime.customize, // 循环周期
  frequency: 1, // 自定义周期
  frequencyType: FrequencyType.day, // 自定义周期类型
  frequencyRange: FrequencyRange.workDay, // 自定义周期范围
  containerId: "", // 安全容器
});

const rules = ref<FormRules<typeof form.value>>({
  name: [{ required: true, message: "请输入班次名称", trigger: ["blur", "change"] }],
  dutyStartTime: [{ required: true, message: "请选择值班开始时间", trigger: ["blur", "change"] }],
  dutyEndTime: [{ required: true, message: "请选择值班结束时间", trigger: ["blur", "change"] }],
  autoScheduling: [{ required: true, message: "请选择自动排班状态", trigger: ["blur", "change"] }],
  takeEffectStartTime: [{ required: true, message: "请选择班次生效日期", trigger: ["blur", "change"] }],
  takeEffectyEndTime: [{ required: true, message: "请选择班次结束日期", trigger: ["blur", "change"] }],
  cycleTime: [{ required: true, message: "请选择循环周期", trigger: ["blur", "change"] }],
  frequency: [{ required: true, message: "请输入自定义周期", trigger: ["blur", "change"] }],
  accentColor: [{ required: true, message: "请选择主题色", trigger: ["blur", "change"] }],
  containerId: [{ required: true, message: "请选择安全容器", trigger: ["blur", "change"] }],
});

const isEdit = computed(() => !!form.value.id);

/* 预设值颜色 */
const predefineColors = ref(["#ff4500", "#ff8c00", "#ffd700", "#90ee90", "#00ced1", "#1e90ff", "#c71585", "rgba(255, 69, 0, 0.68)", "rgb(255, 120, 0)", "hsv(51, 100, 98)", "hsva(120, 40, 94, 0.5)", "hsl(181, 100%, 37%)", "hsla(209, 100%, 56%, 0.73)", "#c7158577"]);

function handleClose(done?) {
  formRef.value && formRef.value.resetFields();

  if (done instanceof Function) done();
  else dialogVisible.value = false;
}

const formRef = ref<FormInstance>();
const treeAuthRef = ref<any>();

async function handleSubmit() {
  // 安全容器
  if (!isEdit.value) form.value.containerId = treeAuthRef.value ? treeAuthRef.value.treeItem.id : "";

  await nextTick();
  formRef.value &&
    formRef.value.validate(async (valid) => {
      if (!valid) return;

      try {
        const { message, success } = await (isEdit.value ? setItem : addItem)({
          id: form.value.id,
          containerId: form.value.containerId,
          name: form.value.name,
          dutyStartTime: form.value.dutyStartTime,
          dutyEndTime: form.value.dutyEndTime,
          autoScheduling: form.value.autoScheduling,
          accentColor: form.value.accentColor,
          takeEffectStartTime: form.value.takeEffectStartTime,
          takeEffectyEndTime: form.value.takeEffectyEndTime,
          cycleTime: form.value.cycleTime,
          frequency: form.value.frequency,
          frequencyType: form.value.frequencyType,
          frequencyRange: form.value.frequencyRange,
        });

        if (!success) throw new Error(message);

        ElMessage.success(t("axios.Operation successful"));

        handleClose();

        emits("refresh");
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    });
}

defineExpose({
  open: (row?: ScheduleItem) => {
    dialogVisible.value = true;

    if (row && row.id) {
      /*  */
      nextTick(() => {
        form.value = JSON.parse(JSON.stringify(row));
        form.value.takeEffectStartTime = form.value.takeEffectStartTime ? Number(form.value.takeEffectStartTime) : "";
        form.value.takeEffectyEndTime = form.value.takeEffectyEndTime ? Number(form.value.takeEffectyEndTime) : "";
      });
    } else form.value.id = "";
  },
});
</script>
