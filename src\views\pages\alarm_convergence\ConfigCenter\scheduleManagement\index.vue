<template>
  <el-card>
    <pageTemplate v-model:currentPage="state.page" v-model:pageSize="state.size" :total="state.total" :height="height - 40" @size-change="handleRefresh()" @current-change="handleRefresh()">
      <template #left>
        <!-- <div class="tw-text-[14px] tw-font-bold">班次管理 - {{ (userInfo.currentTenant || {}).name }}[{{ (userInfo.currentTenant || {}).abbreviation }}]</div> -->
        <el-input class="tw-w-[300px]" v-model="state.search.name" placeholder="请输入班次名称" @change="handleRefresh" @keyup.enter="handleRefresh">
          <template #append>
            <el-button :icon="Search" @click="handleRefresh" />
          </template>
        </el-input>
      </template>

      <template #right>
        <el-button v-if="userInfo.hasPermission(服务管理中心_班次管理_新建)" type="primary" :icon="Plus" @click="() => editorRef && editorRef.open()">新增班次</el-button>
      </template>

      <template #default="{ height: tableHeight }">
        <el-table :border="true" v-loading="state.loading" :data="state.data" :height="tableHeight" style="width: 100%">
          <TableColumn :showOverflowTooltip="true" type="condition" label="班次名称" prop="name"> </TableColumn>
          <TableColumn :showOverflowTooltip="true" type="condition" label="值班开始时间" prop="dutyStartTime" :width="120"></TableColumn>
          <TableColumn :showOverflowTooltip="true" type="condition" label="值班结束时间" prop="dutyEndTime" :width="120"></TableColumn>
          <TableColumn :showOverflowTooltip="true" type="condition" label="自动排班状态" prop="autoScheduling" :width="120">
            <template #default="{ row }">
              <span>{{ row.autoScheduling ? "✔" : "" }}</span>
            </template>
          </TableColumn>
          <TableColumn :showOverflowTooltip="true" type="date" label="班次生效日期" prop="takeEffectStartTime" format="YYYY-MM-DD" :width="180"></TableColumn>
          <TableColumn :showOverflowTooltip="true" type="date" label="班次结束日期" prop="takeEffectyEndTime" format="YYYY-MM-DD" :width="180"></TableColumn>
          <TableColumn :showOverflowTooltip="true" type="condition" label="循环周期">
            <template #default="{ row }">
              <div v-if="row.autoScheduling">
                <template v-if="row.cycleTime !== CycleTime.customize">
                  {{ (cycleTimeOptions.find((v) => v.value === row.cycleTime) || {}).label }}
                </template>
                <template v-else> 每{{ row.frequency }}{{ (frequencyTypeOptions.find((v) => v.value === row.frequencyType) || {}).label }}{{ (frequencyRangeOptions.find((v) => v.value === row.frequencyRange) || {}).label }}循环一次 </template>
              </div>
            </template>
          </TableColumn>
          <TableColumn type="default" label="操作" :width="180">
            <template #default="{ row }">
              <el-button v-if="userInfo.hasPermission(服务管理中心_班次管理_编辑)" link type="primary" @click="() => editorRef && editorRef.open(row as ScheduleItem)">编辑</el-button>
              <el-button v-if="userInfo.hasPermission(服务管理中心_班次管理_删除)" link type="primary" @click="handleDelItem(row as ScheduleItem)">删除</el-button>
              <el-button v-if="userInfo.hasPermission(服务管理中心_班次管理_安全)" link :type="row.verifyPermissionIds.includes(服务管理中心_班次管理_安全) ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="row.verifyPermissionIds.includes(服务管理中心_班次管理_安全) ? false : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-button>
            </template>
          </TableColumn>
        </el-table>
      </template>
    </pageTemplate>

    <Editor ref="editorRef" @refresh="handleRefresh" />
  </el-card>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive, inject } from "vue";

import { useI18n } from "vue-i18n";

import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Search } from "@element-plus/icons-vue";

import { ScheduleItem, getSchedule as getData, delSchedule as delItem, CycleTime, cycleTimeOptions, frequencyTypeOptions, frequencyRangeOptions } from "@/views/pages/apis/shiftManagement";

import { /* 服务管理中心_班次管理_可读, */ 服务管理中心_班次管理_新建, 服务管理中心_班次管理_编辑, 服务管理中心_班次管理_删除, 服务管理中心_班次管理_安全 } from "@/views/pages/permission";

import pageTemplate from "@/components/pageTemplate.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import Editor from "@/views/pages/alarm_convergence/ConfigCenter/scheduleManagement/Editor.vue";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";

import getUserInfo from "@/utils/getUserInfo";

const userInfo = getUserInfo();

const width = inject("width", ref(0));
const height = inject("height", ref(0));

const { t } = useI18n();

const editorRef = ref<InstanceType<typeof Editor>>();

interface State<T> {
  data: T[];
  page: number;
  size: number;
  total: number;
  loading: boolean;
  search: Record<string, any>;
}

const state = reactive<State<ScheduleItem>>({
  data: [],
  page: 1,
  size: 50,
  total: 0,
  loading: false,
  search: {
    name: "",
  },
});

function handleDelItem(row: ScheduleItem) {
  ElMessageBox.confirm(`确定删除班次"${row.name}"？`, "删除", {
    confirmButtonText: t("glob.Confirm"),
    cancelButtonText: t("glob.Cancel"),
    type: "warning",
    beforeClose: async (action, instance, done) => {
      if (action === "confirm") {
        try {
          const { success, message } = await delItem({ id: row.id });
          if (!success) throw new Error(message);
          ElMessage.success(t("axios.Operation successful"));
          handleRefresh();
          done();
        } catch (error) {
          error instanceof Error && ElMessage.error(error.message);
        }
      } else done();
    },
  })
    .then(() => {})
    .catch(() => {});
}

async function handleRefresh() {
  try {
    state.loading = true;
    const { data, message, success, total } = await getData({ pageNumber: state.page, pageSize: state.size, ...state.search });
    if (!success) throw new Error(message);
    state.data = data;

    state.total = Number(total);
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    state.loading = false;
  }
}

onMounted(() => {
  handleRefresh();
});
</script>
