<template>
  <el-dialog v-model="dialogVisible" :title="`${isEdit ? '编辑' : '新建'}排班`" width="500" :before-close="handleClose">
    <el-form :model="form" ref="formRef" :rules="rules" :label-width="'auto'" v-if="dialogVisible">
      <div v-for="(item, idx) in form.schedule" :key="`scheduling-${idx}`">
        <template v-if="!isEdit">
          <el-form-item label="排班班次" :prop="`schedule.${idx}.scheduleId`" :rules="rules.scheduleId">
            <el-select v-model="item.scheduleId" placeholder="请选择排班班次">
              <el-option v-for="schedule in scheduleOption" :key="`schedule-${schedule.id}`" :label="schedule.name" :value="schedule.id" />
            </el-select>
          </el-form-item>

          <el-form-item v-if="!(scheduleOption.find((v) => v.id === item.scheduleId) || {}).autoScheduling" label="排班日期" :prop="`schedule.${idx}.schedulingDate`" :rules="rules.schedulingDate">
            <el-date-picker class="tw-w-full" v-model="item.schedulingDate" type="dates" placeholder="请选择排班日期" :value-format="'x'" />
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item label="值班开始时间" :prop="`schedule.${idx}.dutyStartTime`" :rules="rules.dutyStartTime">
            <el-time-picker class="tw-w-full" v-model="item.dutyStartTime" placeholder="请选择值班开始时间" format="HH:mm" value-format="HH:mm" />
          </el-form-item>
          <el-form-item label="值班结束时间" :prop="`schedule.${idx}.dutyEndTime`" :rules="rules.dutyStartTime">
            <el-time-picker class="tw-w-full" v-model="item.dutyEndTime" placeholder="请选择值班结束时间" format="HH:mm" value-format="HH:mm" />
          </el-form-item>
        </template>

        <el-form-item label="排班人员" :prop="`schedule.${idx}.schedulingStaff`" :rules="rules.schedulingStaff">
          <el-cascader v-model="item.schedulingStaff" class="tw-w-full" collapse-tags collapse-tags-tooltip filterable :options="usersOption" :props="{ multiple: true }" clearable />
        </el-form-item>

        <template v-if="!isEdit">
          <el-button type="danger" class="tw-w-full" :icon="Delete" @click="form.schedule.splice(idx, 1)">移除</el-button>
          <!-- 分割线 -->
          <el-divider />
        </template>
      </div>
    </el-form>
    <el-button v-if="!isEdit" type="primary" class="tw-w-full" :icon="Plus" @click="form.schedule.push(JSON.parse(JSON.stringify(newSchedule)))">新增一条</el-button>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ t("glob.Cancel") }}</el-button>
        <el-button type="primary" @click="handleSubmit"> {{ t("glob.Confirm") }} </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from "vue";

import { useI18n } from "vue-i18n";

import { Plus, Delete } from "@element-plus/icons-vue";

import { getSchedule, ScheduleItem, addDuty as addItem, setDuty as setItem, DutyItem, getUserByUserGroupIds } from "@/views/pages/apis/shiftManagement";

import { getUserGroupByPermissionIds, getUsersByGourpIds, getUserByGroup } from "@/api/personnel";
import { 安全管理中心_用户组管理_可读, 智能事件中心_用户组_分配工单 } from "@/views/pages/permission";
import { 服务管理中心_排班日历_分配用户 } from "@/views/pages/permission";

import { FormInstance, ElMessage } from "element-plus";
import getUserInfo from "@/utils/getUserInfo";
// import { template } from "lodash-es";

const emits = defineEmits(["refresh"]);

const { t } = useI18n();

const userInfo = getUserInfo();

const newSchedule = {
  schedulingDate: [],
  scheduleId: "",
  schedulingStaff: "",
};

const formRef = ref<FormInstance>();

const form: any = reactive({ schedule: [JSON.parse(JSON.stringify(newSchedule))] });

const rules = reactive({
  scheduleId: [{ required: true, message: "请选择排班班次", trigger: ["blur", "change"] }],
  dutyStartTime: [{ required: true, message: "请选择值班开始时间", trigger: ["blur", "change"] }],
  dutyEndTime: [{ required: true, message: "请选择值班结束时间", trigger: ["blur", "change"] }],
  schedulingDate: [{ required: true, message: "请选择排班日期", trigger: ["blur", "change"] }],
  schedulingStaff: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        const result = findDuplicateIndex1(value);
        if (!value || !value.length) {
          return callback(new Error("请选择排班人员"));
        }
        if (result.length) {
          const resultName: string[] = [];
          for (let i = 0; i < result.length; i++) {
            const arr = result[i].indices.map((v) => value[v]);
            for (let j = 0; j < arr.length; j++) {
              resultName.push(getUserGroupAndUserNane(arr[j][0], arr[j][1]));
            }
          }
          return callback(new Error(`排班人员不能重复(${resultName.join(",")})`));
        } else {
          callback();
        }
      },
      trigger: ["blur", "change"],
    },
  ],
});

function getUserGroupAndUserNane(groupId: string, userId: string) {
  const userGroup = usersOption.value.find((v) => v.value === groupId);
  if (userGroup) {
    const user = userGroup.children.find((v) => v.value === userId);
    return `${userGroup.label}/${user.label}`;
  }
  return "";
}

function findDuplicateIndex1(arr) {
  const valueMap = new Map();
  const duplicates: any = [];

  arr.forEach((subArray, index) => {
    if (Array.isArray(subArray)) {
      const value = subArray[1];
      if (value !== undefined) {
        if (valueMap.has(value)) {
          const existing = valueMap.get(value);
          if (existing.count === 1) {
            duplicates.push({
              value: value,
              indices: [existing.index, index],
            });
          }

          valueMap.set(value, { count: existing.count + 1, index: existing.index });
        } else {
          valueMap.set(value, { count: 1, index });
        }
      }
    }
  });

  return duplicates;
}

const dialogVisible = ref(false);

const scheduleOption = ref<ScheduleItem[]>([]);

async function handleGetSchedule() {
  try {
    const { data, message, success } = await getSchedule({ pageNumber: 1, pageSize: 99999 });
    if (!success) throw new Error(message);
    scheduleOption.value = data;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

const usersOption = ref<any>([]);

async function handleGetUserGroup() {
  try {
    if (!userInfo.hasPermission(服务管理中心_排班日历_分配用户)) return;
    const { data, message, success } = await getUserGroupByPermissionIds({ queryPermissionId: 安全管理中心_用户组管理_可读, verifyPermissionIds: [智能事件中心_用户组_分配工单] });
    if (!success) throw new Error(message);

    const { data: userData, message: userMessage, success: userSuccess } = await getUserByUserGroupIds(data.map((v) => v.id));

    if (!userSuccess) throw new Error(userMessage);

    // const usersResult = await Promise.all(data.map((v) => getUserByGroup({ id: v.id })));

    // const usersObject = {};

    // data.forEach((v, i) => {
    //   Object.assign(usersObject, { [v.id]: usersResult[i].data });
    // });

    usersOption.value = data.map((v) => {
      const children = userData.filter((u) => u.groupId === v.id).map((u) => ({ value: u.id, label: u.name }));
      return {
        value: v.id,
        label: v.name,
        children,
        disabled: !children || !children.length,
      };
    });
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

function handleSubmit() {
  formRef.value &&
    formRef.value.validate(async (valid) => {
      if (!valid) return;
      try {
        let params = {};

        if (!isEdit.value) {
          params = form.schedule.map((v) => {
            const userNameList: string[] = [];

            const userObj = v.schedulingStaff.reduce((pre, cur) => {
              const userGroup = usersOption.value.find((v) => v.value === cur[0]);
              const userName = userGroup.children.find((v) => v.value === cur[1]).label;
              userNameList.push(userName);
              if (cur[0] in pre) {
                pre[cur[0]][cur[1]] = userName;
                return pre;
              } else {
                return Object.assign(pre, { [cur[0]]: { [cur[1]]: userName } });
              }
            }, {});

            return {
              scheduleId: v.scheduleId,
              containerId: (userInfo.currentTenant || {}).containerId,
              scheduleDateList: v.schedulingDate,
              userNameList: Object.keys(userObj).map((m) => ({ [m]: userObj[m] })),
            };
          });
        } else {
          params = form.schedule.map((v) => {
            const userNameList: string[] = [];

            const userObj = v.schedulingStaff.reduce((pre, cur) => {
              const userGroup = usersOption.value.find((v) => v.value === cur[0]);
              const userName = userGroup.children.find((v) => v.value === cur[1]).label;
              userNameList.push(userName);
              if (cur[0] in pre) {
                pre[cur[0]][cur[1]] = userName;
                return pre;
              } else {
                return Object.assign(pre, { [cur[0]]: { [cur[1]]: userName } });
              }
            }, {});

            return {
              id: v.id,
              scheduleId: v.scheduleId,
              dutyStartTime: v.dutyStartTime,
              dutyEndTime: v.dutyEndTime,
              userNameList: Object.keys(userObj).map((m) => ({ [m]: userObj[m] })),
            };
          });
        }

        const { message, success } = await (isEdit.value ? setItem : addItem)(isEdit.value ? params[0] : params);
        if (!success) throw new Error(message);
        ElMessage.success(t("axios.Operation successful"));
        handleClose();
        emits("refresh");
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    });
}

function handleClose(done?) {
  formRef.value && formRef.value.resetFields();

  if (done instanceof Function) done();
  else dialogVisible.value = false;
}

const isEdit = computed(() => !!form.schedule[0].id);

onMounted(() => {
  Promise.all([handleGetSchedule(), handleGetUserGroup()]);
});

defineExpose({
  open: (row?: DutyItem) => {
    dialogVisible.value = true;
    if (row && row.id) {
      nextTick(() => {
        form.schedule = [
          {
            id: row.id,
            scheduleId: row.scheduleId,
            dutyStartTime: row.dutyStartTime,
            dutyEndTime: row.dutyEndTime,
            schedulingStaff: (row.userNameList || []).reduce((pre, cur) => {
              for (const groupId in cur) {
                for (const userId in cur[groupId]) {
                  pre.push([groupId, userId]);
                }
              }
              return pre;
            }, []),
          },
        ];
      });
    } else {
      form.schedule = [JSON.parse(JSON.stringify(newSchedule))];
    }
  },
});
</script>
