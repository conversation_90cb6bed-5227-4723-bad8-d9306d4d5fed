<template>
  <div>
    <el-button-group class="ml-4" size="small">
      <el-button :type="type == View.timeGridDay ? 'primary' : ''" @click="changeType(View.timeGridDay)">日</el-button>
      <el-button :type="type == View.dayGridWeek ? 'primary' : ''" @click="changeType(View.dayGridWeek)">周</el-button>
      <el-button :type="type == View.dayGridMonth ? 'primary' : ''" @click="changeType(View.dayGridMonth)">月</el-button>
    </el-button-group>
    <!-- <el-button class="ml2" type="primary" size="small" :icon="Plus" plain>新增日程</el-button> -->
    <div class="tw-mb-[10px] tw-flex tw-justify-between">
      <el-date-picker v-model="timeWeek" type="week" format="YYYY[年][第]ww[周] " placeholder="Pick a week" @change="(v) => FullCalendarApi.gotoDate(v)" :clearable="false" />

      <el-button-group>
        <el-button :type="'primary'" @click="FullCalendarApi.prev()">上一周</el-button>
        <el-button :type="'primary'" @click="FullCalendarApi.today()">今天</el-button>
        <el-button :type="'primary'" @click="FullCalendarApi.next()">下一周</el-button>
      </el-button-group>
    </div>
    <FullCalendar :options="calendarCustomnOptions" ref="Tcalendar" @datesRender="(arg) => console.log(arg)">
      <!-- 用slot展示内容-->
      <template #eventContent="{ event }">
        <div class="ellipsis tw-w-full" :style="{ backgroundColor: event.backgroundColor, borderColor: event.borderColor }" @click="handleEventClick(event)">
          <!-- 在这里进行自己需要的字段展示处理 ,通过arg.event拿到自己的需要的字段等-->
          <div>{{ event.title }}</div>
          <div>{{ formatTimeToHHMM(event.start) }}-{{ formatTimeToHHMM(event.end) }}</div>
          <!-- <div v-for="staff in 10" :key="staff">{{ "人员" }}-{{ staff }}</div> -->
        </div>
      </template>
    </FullCalendar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from "vue";

import FullCalendar from "@fullcalendar/vue3";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";

import type { Options as PopperOptions } from "@popperjs/core";
// import dayjs from "/@/utils/dayjs";
import { ArrowLeftBold, ArrowRightBold, Plus } from "@element-plus/icons-vue";

const timeWeek = ref<Date>(new Date());

const Tcalendar = ref();

enum View {
  dayGridMonth = "dayGridMonth",
  dayGridWeek = "dayGridWeek",
  // timeGridWeek = "timeGridWeek",
  timeGridDay = "timeGridDay",
}

const type = ref(View.dayGridWeek); // 默认周视图
// 根据 initialView 动态设置 dayMaxEvents
const dayMaxValEvents = computed(() => {
  switch (type.value) {
    case View.dayGridMonth:
      return 3;
    case View.dayGridWeek:
      // case View.timeGridWeek:
      return 7;
    default:
      return 3; // 周日作为一周的开始
  }
});

//我需要动态设置 firstDay，所以用了computed
const calendarCustomnOptions = ref({
  header: {
    left: "title",
    center: "",
    right: "today,prev,next",
  },
  plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],
  locale: "zh-cn",
  headerToolbar: false, // 关闭默认日历头部，采取自定义的方式切换日历视图
  editable: false, // 允许编辑表格 是否允许拖拽和调整事件大小
  droppable: false, //允许从外部拖拽进入日历
  eventDurationEditable: false, //控制时间段是否可以拖动
  eventResizableFromStart: false, //控制事件是否可以拖动
  selectable: false, // 允许用户通过单击和拖动来突出显示多个日期或时间段
  firstDay: 0, // 设置一周中显示的第一天是哪天，周日是0，周一是1，类推。
  unselectAuto: true, // 当点击页面日历以外的位置时，是否自动取消当前的选中状态
  dayMaxEvents: dayMaxValEvents.value, //在dayGrid视图中，给定日期内的最大事件数，最多能展示几个事件
  allDaySlot: true, // 关闭全天选项
  nowIndicator: true, // 当前的时间线显示,为true时当前小时那一格有个红线，并且有红三角
  weekends: true, //周末显示
  // slotDuration: '00:30:00', // 一格时间槽代表多长时间，默认00:30:00（30分钟）
  // slotLabelInterval: '00:30:00', // 设置时间段标签间隔为整天

  events: [
    { title: "event 1", start: "2025-07-22 12:00:00", end: "2025-07-22 14:00:00", color: "pink" },
    { title: "event 2", start: "2025-07-22 14:00:00", end: "2025-07-22 16:00:00", color: "red" },
    { title: "event 3", start: "2025-07-22 16:00:00", end: "2025-07-22 18:00:00", color: "orange" },
    // { title: "event 2", date: "2025-07-22 13:00:00" },
  ],

  datesSet: (dateInfo) => {
    // 你可以在这里存储这些日期或触发其他操作
    timeWeek.value = dateInfo.start;
  },
  // slotMinTime: "00:00:00",
  // slotLabelFormat: {
  //   hour: "2-digit", // 确保小时数为两位数
  //   minute: "2-digit", // 确保分钟数为两位数
  //   meridiem: false, // 不显示上午/下午标记
  //   hour12: false, // 使用24小时制
  //   // 自定义表格的class
  //   dayHeaderClassNames: "header-x",
  //   dayCellClassNames: "cell-x",
  //   datesSet: (ev: any) => {
  //     // 当视图的日期范围被设置时触发。这可以用于处理日期变化后的逻辑。
  //     // 我是在这里进行了接口请求，日历的数据是通过请求后再填充到日历中的
  //     console.log("datesSet1", ev);
  //   },
  //   moreLinkClick: (ev: any) => {
  //     // 在这里处理“更多”链接的点击事件，自带的more弹窗有点丑，我是自己重新写的弹窗，就是需要注意隐藏自带的弹窗，我没有找到关闭弹窗的参数，是通过css处理的隐藏
  //   },
  //   eventClick: (ev: any) => {
  //     //事件点击，我这里是点击后进行弹窗展示，根据自己的业务逻辑
  //   },
  //   viewDidMount: (ev: any) => {
  //     //当视图完全渲染并且 DOM 元素已经被添加到页面上时触发。 适用于需要在视图完全渲染后执行的操作，比如添加事件监听器或者对视图进行额外的 DOM 操作。
  //     console.log("viewDidMount", ev);
  //   },
  // },
});

const FullCalendarApi: any = ref();

const changeType = (val: View) => {
  type.value = val;
  //调用 FullCalendarApi 的 changeView 方法，传入新的类型值
  FullCalendarApi.value?.changeView(type.value);
};

function formatTimeToHHMM(date) {
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  return `${hours}:${minutes}`;
}

function handleEventClick(ev) {
  console.log("handleEventClick", ev);
}

onMounted(() => {
  let calendarApi = Tcalendar.value.getApi();
  FullCalendarApi.value = calendarApi;
  FullCalendarApi.value.render(); //刷新日历

  nextTick(() => {
    changeType(View.dayGridWeek);
  });
});
</script>

<style lang="scss">
.fc-view-harness {
  height: 600px !important;
}
</style>
