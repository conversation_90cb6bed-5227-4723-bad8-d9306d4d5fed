<template>
  <el-card class="scheduling-calendar">
    <pageTemplate :showPaging="false" :height="height - 40">
      <template #left>
        <el-input v-model="searchData.scheduleName" placeholder="请输入班次名称进行检索" @change="handleRefresh" @keyup.enter="handleRefresh" class="tw-mr-[10px] tw-w-[300px]">
          <template #append>
            <el-button :icon="Search" @click="handleRefresh" />
          </template>
        </el-input>
        <el-input v-model="searchData.userName" placeholder="请输入人员名称进行检索" @change="handleRefresh" @keyup.enter="handleRefresh" class="tw-w-[300px]">
          <template #append>
            <el-button :icon="Search" @click="handleRefresh" />
          </template>
        </el-input>
      </template>
      <template #right>
        <el-button type="primary" :icon="Download" @click="handleExportDuty">导出</el-button>
        <el-button type="primary" :icon="Upload" @click="handleUpload" v-if="userInfo.hasPermission(服务管理中心_排班日历_新建)">导入</el-button>
        <el-button type="primary" :icon="Plus" @click="handleOpenEditor" v-if="userInfo.hasPermission(服务管理中心_排班日历_新建)">新增排班</el-button>
      </template>
      <template #default>
        <!-- <el-button class="ml2" type="primary" size="small" :icon="Plus" plain>新增日程</el-button> -->
        <div class="tw-mb-[10px] tw-flex tw-justify-between">
          <el-button-group>
            <el-button :type="'primary'" @click="FullCalendarApi.prev()" :icon="ArrowLeft"></el-button>
            <el-button :type="'primary'" @click="FullCalendarApi.today()">今天</el-button>
            <el-button :type="'primary'" @click="FullCalendarApi.next()" :icon="ArrowRight"></el-button>
          </el-button-group>

          <el-date-picker v-model="currentTime" :type="type === View.dayGridWeek ? 'week' : type === View.dayGridMonth ? 'month' : 'date'" :format="type === View.dayGridWeek ? 'YYYY[年][第]ww[周]' : type === View.dayGridMonth ? 'YYYY-MM' : 'YYYY-MM-DD'" placeholder="Pick a week" class="tw-mr-[10px]" @change="(v) => FullCalendarApi.gotoDate(v)" :clearable="false" value-format="x" />

          <el-button-group>
            <el-button :type="type == View.timeGridDay ? 'primary' : ''" @click="changeType(View.timeGridDay)">日</el-button>
            <el-button :type="type == View.dayGridWeek ? 'primary' : ''" @click="changeType(View.dayGridWeek)">周</el-button>
            <el-button :type="type == View.dayGridMonth ? 'primary' : ''" @click="changeType(View.dayGridMonth)">月</el-button>
          </el-button-group>
        </div>
        <FullCalendar :options="calendarCustomnOptions" ref="Tcalendar" @datesRender="(arg) => console.log(arg)" v-loading="loading">
          <!-- 用slot展示内容-->
          <template #eventContent="{ event }">
            <el-tooltip popper-class="event-tip" effect="light" trigger="click" :placement="View.timeGridDay === type ? 'top-start' : 'left'" :append-to="'.scheduling-calendar'">
              <template #content>
                <el-card>
                  <div class="tw-ellipsis tw-w-[200px]">
                    <!-- 在这里进行自己需要的字段展示处理 ,通过arg.event拿到自己的需要的字段等-->
                    <div class="tw-flex tw-justify-between tw-justify-items-center">
                      <div class="tw-mr-[8px]">{{ event.title }}</div>
                      <div>{{ formatTimeToHHMM(event.start) }}-{{ formatTimeToHHMM(event.end) }}</div>
                    </div>

                    <div class="tw-full tw-whitespace-normal tw-text-[14px]">{{ event.extendedProps.staffName }}</div>
                    <!-- <div v-for="staff in 10" :key="staff">{{ "人员" }}-{{ staff }}</div> -->
                  </div>
                  <template #footer>
                    <div class="tw-flex tw-justify-between tw-justify-items-center">
                      <el-button link type="primary" @click="handleOpenEditor(allDutyItem.find((v) => v.id === event.id))" v-if="userInfo.hasPermission(服务管理中心_排班日历_编辑)">编辑</el-button>
                      <el-button link type="danger" :icon="Delete" @click="handleDelItem({ id: event.id })" v-if="userInfo.hasPermission(服务管理中心_排班日历_删除)"></el-button>
                    </div>
                  </template>
                </el-card>
              </template>
              <div class="target tw-ellipsis tw-w-full tw-p-[10px]" :style="{ backgroundColor: event.backgroundColor, borderColor: event.borderColor, color: event.textColor }" @click="handleEventClick(event)">
                <!-- 在这里进行自己需要的字段展示处理 ,通过arg.event拿到自己的需要的字段等-->
                <div class="tw-flex tw-justify-between tw-justify-items-center">
                  <div class="tw-mr-[8px]">{{ event.title }}</div>
                  <div>{{ formatTimeToHHMM(event.start) }}-{{ formatTimeToHHMM(event.end) }}</div>
                </div>

                <div class="tw-full tw-whitespace-normal tw-text-[14px]">{{ event.extendedProps.staffName }}</div>
                <!-- <div v-for="staff in 10" :key="staff">{{ "人员" }}-{{ staff }}</div> -->
              </div>
            </el-tooltip>
          </template>
        </FullCalendar>
      </template>
    </pageTemplate>

    <Editor ref="editorRef" @refresh="handleRefresh" />

    <UploadFile ref="uploadFileRef" @refresh="handleRefresh" />
  </el-card>
</template>

<script setup lang="ts">
import { ref, inject, reactive, computed, onMounted, nextTick, watch } from "vue";

import { useI18n } from "vue-i18n";

import { ElMessage, ElMessageBox } from "element-plus";

import { Search, Download, Upload, Plus, Delete, ArrowLeft, ArrowRight } from "@element-plus/icons-vue";

import pageTemplate from "@/components/pageTemplate.vue";
import Editor from "@/views/pages/alarm_convergence/ConfigCenter/schedulingCalendar/Editor.vue";
import UploadFile from "@/views/pages/alarm_convergence/ConfigCenter/schedulingCalendar/upload.vue";

import FullCalendar from "@fullcalendar/vue3";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";

import { getDuty as getItem, delDuty as delItem, exportDuty, DutyItem } from "@/views/pages/apis/shiftManagement";

import { 服务管理中心_排班日历_可读, 服务管理中心_排班日历_新建, 服务管理中心_排班日历_编辑, 服务管理中心_排班日历_删除, 服务管理中心_排班日历_分配用户 } from "@/views/pages/permission";

import getUserInfo from "@/utils/getUserInfo";

import moment from "moment";

const userInfo = getUserInfo();

const { t } = useI18n();

const height = inject("height", ref(0));

const calendarheight = computed(() => height.value - 82 - 40);

watch(
  () => calendarheight.value,
  () => FullCalendarApi.value && FullCalendarApi.value.render()
);

// https://172.31.26.203:20082/gw_proxy/cmdb/templates/schedule-template.xlsx

// const userInfo = getUserInfo();

const editorRef = ref<InstanceType<typeof Editor>>();

const uploadFileRef = ref<InstanceType<typeof UploadFile>>();

const searchData = reactive({
  scheduleName: "",
  userName: "",
});

function handleOpenEditor(row?: DutyItem) {
  editorRef.value && editorRef.value.open(row);
}

const currentTime = ref(new Date().getTime());

const calendarStart = ref<Date>(new Date());
const calendarEnd = ref<Date>(new Date());

const Tcalendar = ref();

enum View {
  dayGridMonth = "dayGridMonth",
  dayGridWeek = "dayGridWeek",
  // timeGridWeek = "timeGridWeek",
  timeGridDay = "timeGridDay",
}

const type = ref(View.dayGridWeek); // 默认周视图
// 根据 initialView 动态设置 dayMaxEvents
const dayMaxValEvents = computed(() => {
  switch (type.value) {
    case View.dayGridMonth:
      return 3;
    case View.dayGridWeek:
      // case View.timeGridWeek:
      return 7;
    default:
      return void 0; // 周日作为一周的开始
  }

  // return void 0;
});

const events = ref<any>([]);

//我需要动态设置 firstDay，所以用了computed
const calendarCustomnOptions = computed(() => ({
  header: {
    left: "title",
    center: "",
    right: "today,prev,next",
  },
  plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],
  locale: "zh-cn",
  headerToolbar: false, // 关闭默认日历头部，采取自定义的方式切换日历视图
  editable: false, // 允许编辑表格 是否允许拖拽和调整事件大小
  droppable: false, //允许从外部拖拽进入日历
  eventDurationEditable: false, //控制时间段是否可以拖动
  eventResizableFromStart: false, //控制事件是否可以拖动
  selectable: false, // 允许用户通过单击和拖动来突出显示多个日期或时间段
  firstDay: 0, // 设置一周中显示的第一天是哪天，周日是0，周一是1，类推。
  unselectAuto: true, // 当点击页面日历以外的位置时，是否自动取消当前的选中状态
  dayMaxEvents: dayMaxValEvents.value, //在dayGrid视图中，给定日期内的最大事件数，最多能展示几个事件
  allDaySlot: true, // 关闭全天选项
  nowIndicator: true, // 当前的时间线显示,为true时当前小时那一格有个红线，并且有红三角
  weekends: true, //周末显示
  slotDuration: "00:30:00", // 一格时间槽代表多长时间，默认00:30:00（30分钟）
  // slotLabelInterval: '00:30:00', // 设置时间段标签间隔为整天

  events: events.value,

  datesSet: (dateInfo) => {
    // 你可以在这里存储这些日期或触发其他操作
    calendarStart.value = dateInfo.start;
    calendarEnd.value = dateInfo.end;

    nextTick(handleRefresh);
    // timeWeek.value = dateInfo.end;
  },
  // slotMinTime: "00:00:00",
  // slotLabelFormat: {
  //   hour: "2-digit", // 确保小时数为两位数
  //   minute: "2-digit", // 确保分钟数为两位数
  //   meridiem: false, // 不显示上午/下午标记
  //   hour12: false, // 使用24小时制
  //   // 自定义表格的class
  //   dayHeaderClassNames: "header-x",
  //   dayCellClassNames: "cell-x",
  //   datesSet: (ev: any) => {
  //     // 当视图的日期范围被设置时触发。这可以用于处理日期变化后的逻辑。
  //     // 我是在这里进行了接口请求，日历的数据是通过请求后再填充到日历中的
  //     console.log("datesSet1", ev);
  //   },
  //   moreLinkClick: (ev: any) => {
  //     // 在这里处理“更多”链接的点击事件，自带的more弹窗有点丑，我是自己重新写的弹窗，就是需要注意隐藏自带的弹窗，我没有找到关闭弹窗的参数，是通过css处理的隐藏
  //   },
  //   eventClick: (ev: any) => {
  //     //事件点击，我这里是点击后进行弹窗展示，根据自己的业务逻辑
  //   },
  //   viewDidMount: (ev: any) => {
  //     //当视图完全渲染并且 DOM 元素已经被添加到页面上时触发。 适用于需要在视图完全渲染后执行的操作，比如添加事件监听器或者对视图进行额外的 DOM 操作。
  //     console.log("viewDidMount", ev);
  //   },
  // },
}));

watch(
  () => [calendarStart.value, type.value],
  () => {
    const now = new Date(calendarStart.value);
    if (type.value === View.dayGridMonth) {
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const day = now.getDate();
      if (day !== 1) month + 1;
      currentTime.value = new Date(year, month, 1).getTime();
    } else {
      currentTime.value = now.getTime();
    }
  }
);

const FullCalendarApi: any = ref();

const changeType = (val: View) => {
  type.value = val;
  //调用 FullCalendarApi 的 changeView 方法，传入新的类型值
  FullCalendarApi.value?.changeView(type.value);
};

function formatTimeToHHMM(date) {
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  return `${hours}:${minutes}`;
}

const allDutyItem = ref<DutyItem[]>([]);

function handleEventClick(ev) {
  console.log("handleEventClick", ev);
}

async function handleExportDuty() {
  ElMessageBox.confirm(`导出当前视图中排班或者导出全部排班`, "导出", {
    confirmButtonText: "当前视图",
    cancelButtonText: "全部",
    distinguishCancelAndClose: true,
    type: "warning",
    beforeClose: async (action, instance, done) => {
      if (action === "confirm" || action === "cancel") {
        try {
          const { data, message, success } = await exportDuty(
            Object.assign(
              {
                scheduleName: searchData.scheduleName,
                userName: searchData.userName,
              },
              action === "confirm" ? { dutyStartDate: new Date(calendarStart.value).getTime().toString(), dutyEndDate: new Date(calendarEnd.value).getTime().toString() } : {}
            )
          );

          if (!success) throw new Error(message);
          const link = document.createElement("a");
          let blob = new Blob([data], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
          });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.setAttribute("download", "schedule.xlsx");
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        } catch (error) {
          error instanceof Error && ElMessage.error(error.message);
        }
      } else done();
    },
  })
    .then(() => {})
    .catch(() => {});
}

const loading = ref(false);

async function handleRefresh() {
  try {
    loading.value = true;
    const { data, success, message } = await getItem({
      dutyStartDate: new Date(calendarStart.value).getTime().toString(),
      dutyEndDate: new Date(calendarEnd.value).getTime().toString(),
      scheduleName: searchData.scheduleName,
      userName: searchData.userName,
    });

    if (!success) throw new Error(message);

    allDutyItem.value = data;

    events.value = data.map((v) => {
      const dutyDate = moment(Number(v.dutyDate)).format("YYYY-MM-DD");

      const allUserNames = (v.userNameList || []).flatMap((obj) => {
        const innerObj = Object.values(obj)[0]; // 获取内部对象
        return Object.values(innerObj); // 返回所有值(即用户名)
      });

      return {
        /*  */
        id: v.id,
        title: v.scheduleName,
        start: `${dutyDate} ${v.dutyStartTime}:00`,
        end: `${dutyDate} ${v.dutyEndTime}:00`,
        color: v.accentColor,
        staffName: allUserNames.join(","),
        textColor: getTextColor(v.accentColor),
      };
    });

    FullCalendarApi.value.render(); //刷新日历
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}

function handleUpload() {
  uploadFileRef.value && uploadFileRef.value.open();
}

function handleDelItem(row) {
  ElMessageBox.confirm(`确定删除排班吗？`, "删除", {
    confirmButtonText: t("glob.Confirm"),
    cancelButtonText: t("glob.Cancel"),
    type: "warning",
    beforeClose: async (action, instance, done) => {
      if (action === "confirm") {
        try {
          const { success, message } = await delItem({ id: row.id });
          if (!success) throw new Error(message);
          ElMessage.success(t("axios.Operation successful"));
          handleRefresh();
          done();
        } catch (error) {
          error instanceof Error && ElMessage.error(error.message);
        }
      } else done();
    },
  })
    .then(() => {})
    .catch(() => {});
}

/**
 * 根据背景色返回最佳文本颜色（黑色或白色）
 * @param {string} bgColor - 背景色，支持 hex、rgb、rgba 或颜色名称
 * @returns {string} 'black' 或 'white'
 */
function getTextColor(bgColor) {
  // 1. 创建一个临时元素来解析颜色
  const tempElem = document.createElement("div");
  tempElem.style.color = bgColor;
  document.body.appendChild(tempElem);

  // 2. 获取计算后的 RGB 值
  const computedColor = window.getComputedStyle(tempElem).color;
  document.body.removeChild(tempElem);

  // 3. 解析 RGB 值（处理 rgb(r, g, b) 或 rgba(r, g, b, a)）
  const rgbMatch = computedColor.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*\d+\.?\d*)?\)$/i);
  if (!rgbMatch) return "black"; // 默认黑色

  const r = parseInt(rgbMatch[1]);
  const g = parseInt(rgbMatch[2]);
  const b = parseInt(rgbMatch[3]);

  // 4. 计算亮度（使用 W3C 推荐公式）
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

  // 5. 根据亮度决定文本颜色
  return luminance > 0.5 ? "black" : "white";
}

onMounted(() => {
  let calendarApi = Tcalendar.value.getApi();
  FullCalendarApi.value = calendarApi;
  FullCalendarApi.value.render(); //刷新日历

  nextTick(() => {
    changeType(View.dayGridWeek);
  });
});
</script>

<style lang="scss">
.fc-view-harness {
  height: v-bind("calendarheight + 'px'") !important;
}

.fc .fc-more-popover .fc-popover-body {
  max-height: v-bind("calendarheight - 27 + 'px'") !important;
  overflow: auto !important;
}

.event-tip {
  padding: 0;

  .el-card__footer {
    padding: 10px;
  }

  .el-card__body {
    padding: 10px;
  }
}
</style>
