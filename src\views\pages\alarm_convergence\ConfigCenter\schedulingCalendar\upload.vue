<template>
  <el-dialog title="导入排班" v-model="visible" width="500" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="auto">
      <el-form-item prop="name">
        <div class="tw-flex tw-w-full tw-justify-end">
          <el-button :size="'small'" @click="handleDownloadTemplate">下载模板</el-button>
        </div>
      </el-form-item>
      <el-form-item prop="file">
        <el-upload class="upload-demo tw-w-full" drag action :auto-upload="false" :limit="1" :on-change="handleChangeFile">
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">拖拽到此处或者<em>点击上传</em></div>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ t("glob.Cancel") }}</el-button>
        <el-button type="primary" @click="handleSubmit"> {{ t("glob.Confirm") }} </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { nextTick, ref } from "vue";

import { useI18n } from "vue-i18n";

import { ElMessage, FormInstance, UploadFile } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";

import { downloadDutyUploadTemplate, importDuty } from "@/views/pages/apis/shiftManagement";

const { t } = useI18n();

const emits = defineEmits(["refresh"]);

const visible = ref(false);

const formRef = ref<FormInstance>();
const form = ref<any>({
  file: null,
});

const rules = ref<any>({
  file: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        if (!value) {
          callback(new Error("请上传文件"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
});

async function handleDownloadTemplate() {
  try {
    const { data, success, message } = await downloadDutyUploadTemplate();
    if (!success) throw new Error(message);
    const link = document.createElement("a");
    let blob = new Blob([data], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
    });
    link.style.display = "none";
    link.href = URL.createObjectURL(blob);
    link.setAttribute("download", "schedule-template.xlsx");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

async function handleChangeFile(file: UploadFile) {
  form.value.file = file.raw;
}

function handleSubmit() {
  formRef.value &&
    formRef.value.validate(async (valid) => {
      if (!valid) return;
      try {
        const formData = new FormData();
        formData.append("file", form.value.file);
        const { message, success } = await importDuty(formData as any);
        if (!success) throw new Error(message);
        ElMessage.success("导入成功");
        handleClose();
        emits("refresh");
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    });
}

function handleClose(done?) {
  nextTick(() => formRef.value && formRef.value.resetFields());

  if (done instanceof Function) done();
  else visible.value = false;
}

defineExpose({
  open() {
    visible.value = true;
  },
});
</script>
