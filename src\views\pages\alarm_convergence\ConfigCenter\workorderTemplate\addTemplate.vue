<template>
  <el-dialog v-model="dialogVisible" :title="`${isEdit ? '编辑' : '新增'}工单模版`" width="45%" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="formRules" :label-width="'100px'">
      <el-form-item label="名称" prop="ticketTemplatesName">
        <el-input v-model="form.ticketTemplatesName" placeholder="请输入名称"></el-input>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="form.description" placeholder="请输入描述" />
      </el-form-item>
      <el-form-item label="工单类型" prop="ticketType">
        <el-select v-model="form.ticketType" clearable filterable placeholder="请选择">
          <el-option v-for="item in ticketTypesList" :key="item.ticketType" :label="item.ticketName" :value="item.ticketType"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="工单子类型" prop="orderSubclass">
        <el-input v-model="form.orderSubclass" placeholder="请输入工单子类型" />
      </el-form-item>
      <el-form-item label="关联表单" prop="relevancy">
        <!-- <el-input v-model="form.relevancy" placeholder="请输入工单子类型" /> -->
        <el-select v-model="form.relevancy" placeholder="请选择关联表单">
          <el-option v-for="item in relevancyOption" :key="`relevancy-${item.value}`" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="工单组" prop="ticketGroupId">
        <el-select v-model="form.ticketGroupId" clearable filterable placeholder="请选择">
          <el-option v-for="item in orderGrouplist" :key="item.id" :label="item.ticketGroupName" :value="item.id"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择安全目录" prop="containerId" v-if="!isEdit" label-width="120px">
        <treeAuth
          v-if="dialogVisible"
          ref="treeAuthRef"
          :treeStyle="{
            width: '300px',
            height: '150px',
          }"
        ></treeAuth>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, nextTick, computed } from "vue";

import treeAuth from "@/components/treeAuth/index.vue";

import { ElMessage, type FormInstance } from "element-plus";

import { addAllticketTemplates, setAllticketTemplates, type AddReqBody, type SetReqBody, ticketTypes, relevancyOptions } from "@/views/pages/apis/ticketTemplate";
import { getOrderGroup, type OrderGroup } from "@/views/pages/apis/orderGroup";

import getUserInfo from "@/utils/getUserInfo";

const dialogVisible = ref<boolean>(false);

const userInfo = getUserInfo();

const emits = defineEmits(["refresh"]);
defineOptions({ name: "AddTemplate" });

const form = ref<AddReqBody & Partial<SetReqBody>>({
  ticketTemplatesName: "",
  description: "",
  ticketGroupId: "",
  ticketType: "event",
  ticketGroupName: "",
  containerId: "",
  orderSubclass: "",
  relevancy: "",
});

const isEdit = computed(() => !!form.value.id);

const formRules = ref({
  ticketTemplatesName: [{ required: true, message: "名称不能为空", trigger: ["blur", "change"] }],
  containerId: [{ required: true, message: "请选择安全容器", trigger: ["blur", "change"] }],
  orderSubclass: [{ required: true, message: "子类型不能为空", trigger: ["blur", "change"] }],
  ticketGroupId: [{ required: true, message: "工单组不能为空", trigger: ["blur", "change"] }],
  relevancy: [{ required: true, message: "关联表单不能为空", trigger: ["blur", "change"] }],
});

const relevancyOption = computed(() => {
  if (form.value.ticketType) {
    const currentTicketTypeRelevancy = relevancyOptions[form.value.ticketType];
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    form.value.relevancy = (currentTicketTypeRelevancy.find((v) => v) || {}).value || "";
    return currentTicketTypeRelevancy;
  } else return [];
});

const formRef = ref<FormInstance>();
const orderGrouplist = ref<OrderGroup[]>([]);

const treeAuthRef = ref<InstanceType<typeof treeAuth>>();
const ticketTypesList = ref<any>(ticketTypes);

async function handleClose(done) {
  formRef.value && formRef.value.resetFields();

  await nextTick();

  if (done instanceof Function) done();
  else dialogVisible.value = false;
}

async function handleOpen(row) {
  dialogVisible.value = true;

  await nextTick();
  await queryOrderGroup();

  if (row.id) form.value = JSON.parse(JSON.stringify(row));
  // treeAuthRef.value = null;
}

async function queryOrderGroup() {
  // 查询工单组数据
  try {
    const { data, message, success } = await getOrderGroup({ pageNumber: 1, pageSize: 99999 });
    if (!success) throw new Error(message);
    orderGrouplist.value = data;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

async function handleSubmit() {
  if (!isEdit.value) form.value.containerId = treeAuthRef.value && treeAuthRef.value.treeId ? treeAuthRef.value.treeId : "";

  console.log(treeAuthRef.value);

  await nextTick();

  if (!formRef.value) return;

  formRef.value.validate(async (valid: any) => {
    if (!valid) return;
    const { ticketTemplatesName, description, ticketType, ticketGroupId, containerId, orderSubclass, relevancy } = form.value;

    const addArguments = {
      ticketTemplatesName,
      description,
      ticketType,
      ticketGroupId: ticketGroupId === "" ? null : ticketGroupId,
      ticketGroupName: orderGrouplist.value.find((item) => item.id === ticketGroupId)?.ticketGroupName || null,
      containerId,
      orderSubclass,
      relevancy,
    };
    try {
      const { success, message } = await (isEdit.value ? setAllticketTemplates(addArguments) : addAllticketTemplates(addArguments as any));
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
      await nextTick();

      handleClose(null);

      emits("refresh");
    } catch (error) {
      error instanceof Error && ElMessage.error(error.message);
    }
  });
}

defineExpose({ open: handleOpen });
</script>
