<template>
  <!-- 联系人 -->
  <div class="tw-my-4">
    <div class="tw-mb-4 tw-flex tw-items-center tw-justify-between">
      <span class="tw-font-bold">联系人</span>
      <el-button type="primary" :icon="Plus" @click="opencreateContact" :loading="contactLoading">分配联系人</el-button>
    </div>
    <el-table :data="contactstableData" border stripe v-loading="contactsloading">
      <el-table-column type="default" prop="name" label="联系人类型">
        <template #default="{ row }">
          {{ contactTypelist.find((item) => item.code === row.contactType)?.cnName || "--" }}
        </template>
      </el-table-column>
      <el-table-column type="default" prop="contactName" label="名称"></el-table-column>
      <!-- <el-table-column type="default" prop="active" label="主要联系人">
        <template #default="scope">
          <el-text type="primary" v-if="scope.row.active">是</el-text>
          <el-text type="danger" v-else>否</el-text>
        </template>
      </el-table-column> -->
      <el-table-column type="default" label="操作" width="200">
        <template #default="{ row }">
          <el-popconfirm :title="`确认删除当前数据吗?`" @confirm="handledelContacts(row)">
            <template #reference>
              <el-button type="danger" link>删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <!-- 设备 -->
  <div class="tw-my-4">
    <div class="tw-mb-4 tw-flex tw-items-center tw-justify-between">
      <span class="tw-font-bold">设备</span>
      <el-button type="primary" :icon="Plus" @click="handleAddresource" :loading="deviceLoading">分配设备</el-button>
    </div>
    <el-table :data="resourcetableData" border stripe v-loading="resourceloading">
      <el-table-column type="default" prop="name" label="设备名称">
        <template #default="{ row }">
          <div>
            <div style="color: #409eff; cursor: pointer">
              {{ row.name || "--" }}
            </div>
            <div style="font-size: 12px">
              {{ row.config?.ipAddress || "--" }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column type="default" prop="description" label="IP"></el-table-column>
      <el-table-column type="default" prop="description" label="描述"></el-table-column>
      <el-table-column type="default" label="操作" width="200">
        <template #default="{ row }">
          <el-popconfirm :title="`确认删除当前数据吗?`" @confirm="delRefreshresource(row)">
            <template #reference>
              <el-button type="danger" link v-if="userInfo.hasPermission('689703964815392768')">删除设备</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <!-- 设备自动分配规则 -->
  <div class="tw-my-4" v-if="props.workOrderType === 'change'">
    <div class="tw-mb-4 tw-flex tw-items-center tw-justify-between">
      <span class="tw-font-bold">设备自动分配规则</span>
      <el-button type="primary" :icon="Plus" @click="opencreateRule" :loading="automaticLoading">新增规则</el-button>
    </div>
    <el-table :data="ruletableData" border stripe v-loading="ruleloading">
      <el-table-column type="default" prop="regionName" label="区域">
        <template #default="{ row }">
          {{ row.regionName || "--" }}
        </template>
      </el-table-column>
      <el-table-column type="default" prop="locationName" label="场所"></el-table-column>
      <el-table-column type="default" prop="orderGroupName" label="设备分组"></el-table-column>
      <el-table-column type="default" prop="autoContacts" label="分配设备联系人">
        <template #default="{ row }">
          {{ row.autoContacts ? "√" : "" }}
        </template>
      </el-table-column>
      <el-table-column type="default" label="操作" width="200">
        <template #default="{ row }">
          <el-popconfirm :title="`确认删除当前数据吗?`" @confirm="delRefreshrule(row)">
            <template #reference>
              <el-button type="danger" link>删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </div>

  <createContact ref="createContactRef" :contactTypelist="contactTypelist" />
  <createRule ref="createRuleRef" />
</template>

<script setup lang="ts">
import { onMounted, ref, h, defineComponent, nextTick, computed, inject } from "vue";

import { Plus } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, ElForm, ElFormItem, ElSelect, ElOption, ElCheckbox } from "element-plus";

import { ProjectaddProdDevice, ProjectdelProdDevice } from "@/views/pages/apis/projectPlan";

import createContact from "./createContact.vue";
import createRule from "./createRule.vue";

import { eventBatchContact as getData, eventBatchdesensitized, eventAddContact as addData, eventDelContact as delData, getChangeContacts } from "@/views/pages/apis/eventManage";
import { deviceAllocateRule as getRule, addDeviceAllocateRule as addRule, delDeviceAllocateRule as delRule, changeState } from "@/views/pages/apis/change";
import { addticketcreate, getticketcontacts, delticketcontacts, addticketresource, getticketresource, delticketresource, addticketrule, getticketrule, delticketrule } from "@/views/pages/apis/ticketTemplate";

import getUserInfo from "@/utils/getUserInfo";
import { getDeviceById } from "@/views/pages/apis/alarmBoard";
import { queryResourceList } from "@/views/pages/apis/index";
import { useRoute, useRouter } from "vue-router";
interface Emits {
  (event: "refresh", data: unknown): void;
}
const emits = defineEmits<Emits>();
const route = useRoute();
const router = useRouter();

import {} from "@/views/pages/permission";

defineOptions({ name: "assignPage" });

const userInfo: any = getUserInfo();

interface Props {
  parentId: string;
  containerId: string;
  tenantId: string;
  deviceIds: string[]; // 显式声明类型
  workOrderType: "event" | "server" | "change" | "released" | "problem" | string;
}

const props = withDefaults(defineProps<Props>(), {
  parentId: "",
  containerId: "",
  tenantId: "",
  deviceIds: undefined,
  workOrderType: "",
});

const deviceId = ref<string[]>([]);
const autoMatch = ref(false);
const contactLoading = ref<boolean>(false);
const deviceLoading = ref<boolean>(false);
const automaticLoading = ref<boolean>(false);

const contactsloading = ref<boolean>(false);
const resourceloading = ref<boolean>(false);
const ruleloading = ref<boolean>(false);

const createContactRef = ref<InstanceType<typeof createContact>>();
const createRuleRef = ref<InstanceType<typeof createRule>>();
const contactstableData = ref<Record<string, any>>([]);
const resourcetableData = ref<Record<string, any>>([]);
const ruletableData = ref<Record<string, any>>([]);

// 联系人类型
const contactTypelist = ref([
  { cnName: "通知联系人", code: "Notification", enName: "Notification Contacts" },
  { cnName: "技术联系人", code: "Technical", enName: "Technical Contacts" },
  { cnName: "现场联系人", code: "OnSite", enName: "On Site Contacts" },
]);

async function handleRefreshContacts() {
  contactsloading.value = true;
  try {
    const { data, message, success } = await getticketcontacts({ ticketTemplatesId: props.parentId });
    if (!success) throw new Error(message);
    contactstableData.value = data;
    contactsloading.value = false;
  } catch (error) {
    contactsloading.value = false;
    error instanceof Error && ElMessage.error(error.message);
  }
}
async function opencreateContact() {
  if (!createContactRef.value) return false;
  await createContactRef.value.open({}, async (form) => {
    const { success, message, data } = await addticketcreate({ ticketTemplatesId: props.parentId, contactType: form.contactType, contactId: form.contactId, contactName: form.contactName });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success("操作成功");
    handleRefreshContacts();
  });
}

async function handledelContacts(row) {
  try {
    const { message, success } = await delticketcontacts({ id: row.id });
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    handleRefreshContacts();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}
// ----------------------------------联系人结束---------------------------------------

async function handleRefreshresource() {
  resourceloading.value = true;
  try {
    const { data, message, success } = await getticketresource({ ticketTemplatesId: props.parentId });
    if (!success) throw new Error(message);
    resourcetableData.value = data;
    resourceloading.value = false;
  } catch (error) {
    resourceloading.value = false;
    error instanceof Error && ElMessage.error(error.message);
  }
}

//设备新增
async function handleAddresource() {
  try {
    deviceLoading.value = true;
    // 获取激活设备
    const activeResult = await queryResourceList({ active: true });

    if (!activeResult.success) {
      throw new Error(activeResult.message || "获取数据失败");
    }
    const activeDevices = !userInfo.hasPermission("701290146212872192") ? [] : activeResult.data || []; // 激活设备
    deviceId.value = [];
    await nextTick();
    deviceLoading.value = false;
    ElMessageBox({
      title: "分配设备",
      message: h(
        defineComponent({
          setup() {
            return () =>
              h(ElForm, { ref: "createFormRef", style: { width: "396px" }, model: {}, labelPosition: "left" }, [
                h(
                  ElFormItem,
                  {
                    label: "设备名称",
                    prop: "versionId",
                    rules: [
                      {
                        required: true,
                        validator: (rule, value, callback) => (!deviceId.value ? callback(new Error("设备不能为空")) : callback()),
                        trigger: "blur",
                      },
                    ],
                  },
                  [
                    h(
                      ElSelect,
                      {
                        "class": "tw-w-full",
                        "modelValue": deviceId.value,
                        "onUpdate:modelValue": ($event) => (deviceId.value = $event),
                        "filterable": true,
                        "multiple": true,
                      },
                      () => activeDevices.map((v) => h(ElOption, { key: `option-${v.id}`, label: v.name, value: v.id }))
                    ),
                  ]
                ),
                h(
                  ElFormItem,
                  {},
                  h(
                    ElCheckbox,
                    {
                      "modelValue": autoMatch.value,
                      "onUpdate:modelValue": (value) => (autoMatch.value = value as boolean),
                    },
                    "自动匹配联系人"
                  )
                ),
              ]);
          },
        })
      ),
      beforeClose: async (action, instance, done) => {
        if (action === "confirm") {
          try {
            if (!(deviceId.value instanceof Array) || !deviceId.value.length) throw new Error("请选择设备名称");
            const { message, success } = await addticketresource({
              ticketTemplatesId: props.parentId,
              resourceIds: deviceId.value,
              autoContacts: autoMatch.value,
            });
            if (!success) throw new Error(message);
            ElMessage.success("操作成功");
            await handleRefreshresource();
            done();
          } catch (error) {
            error instanceof Error && ElMessage.error(error.message);
          }
        } else done();
      },
    }).catch(() => Promise.resolve("cancel"));
  } catch (error) {
    deviceLoading.value = false;
    error instanceof Error && ElMessage.error(error.message);
  }
}

async function delRefreshresource(row) {
  try {
    const { success, message } = await delticketresource({ id: row.id });
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    handleRefreshresource();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

// ----------------------------------设备结束---------------------------------------

async function opencreateRule() {
  if (!createRuleRef.value) return false;
  await createRuleRef.value.open({}, async (form: any) => {
    const rulePrams = {
      ...form,
      regionId: form.regionId[form.regionId.length - 1],
      regionName: form.regionName[form.regionName.length - 1],
      ticketTemplatesId: props.parentId,
    };
    const { success, message } = await addticketrule(rulePrams);
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    handleRefreshRule();
  });
}
async function handleRefreshRule() {
  ruleloading.value = true;
  try {
    const { data, message, success } = await getticketrule({ ticketTemplatesId: props.parentId });
    if (!success) throw new Error(message);
    ruletableData.value = data;
    ruleloading.value = false;
  } catch (error) {
    ruleloading.value = false;
    error instanceof Error && ElMessage.error(error.message);
  }
}
async function delRefreshrule(row) {
  try {
    const { success, message } = await delticketrule({ id: row.id });
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    handleRefreshRule();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

onMounted(() => {
  handleRefreshContacts();
  handleRefreshresource();
  handleRefreshRule();
});
</script>
