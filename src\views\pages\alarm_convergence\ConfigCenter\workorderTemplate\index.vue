<template>
  <el-card>
    <pageTemplate v-model:currentPage="state.page" v-model:pageSize="state.size" :total="state.total" :height="height - 40" @size-change="handleRefresh" @current-change="handleRefresh">
      <template #left>
        <div class="worktitle">
          <h2>
            工单模版
            <!-- <el-icon @click="workorderHelp" style="color: #2a8bf5; cursor: pointer; margin-left: 10px"><QuestionFilled /></el-icon> -->
          </h2>
        </div>
      </template>
      <template #right>
        <div>
          <el-button type="primary" :icon="Plus" @click="hendleCreate" v-if="userInfo.hasPermission(服务管理中心_工单模版_新增)">新增工单模版</el-button>
        </div>
      </template>
      <template #default="{ height: tableHeight }">
        <el-form :model="state" ref="formRef">
          <el-table :data="state.data" border stripe style="width: 100%" :height="tableHeight" :row-key="(row) => row.id" :expand-row-keys="expands" @expand-change="expandColumn" v-loading="state.loading">
            <el-table-column type="expand">
              <template #default="{ row }">
                <div class="tw-mt-2 tw-px-4" v-if="row.verifyPermissionIds.includes(服务管理中心_工单模版_编辑)">
                  <workOrderConfig ref="workOrderConfigRef" :parentId="row.id" :ticketTemplatesName="row.ticketTemplatesName" :workOrderType="row.ticketType" :ticketGroupId="row.ticketGroupId" :isEdit="row.isEdit" @refresh="handleRefresh" />
                  <!-- <assignPage :parentId="row.id" :workOrderType="row.ticketType" @refresh="handleRefresh" /> -->
                </div>
              </template>
            </el-table-column>
            <TableColumn type="condition" prop="ticketTemplatesName" label="名称" filter-multiple show-filter v-model:custom-filtered-value="searchName" :filters="$filter0" @filter-change="handleRefresh()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].ticketTemplatesName`" :rules="[{ required: row.isEdit, message: '模版名称不能为空', trigger: 'blur' }]">
                  <el-input v-model="row.ticketTemplatesName"></el-input>
                </el-form-item>
                <template v-else>{{ row.ticketTemplatesName }}</template>
              </template>
            </TableColumn>
            <TableColumn type="condition" prop="description" label="描述" filter-multiple show-filter v-model:custom-filtered-value="searchDesc" :filters="$filter0" @filter-change="handleRefresh()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].description`">
                  <el-input v-model="row.description"></el-input>
                </el-form-item>
                <template v-else>{{ row.description }}</template>
              </template>
            </TableColumn>
            <TableColumn type="condition" prop="category" label="类别" filter-multiple show-filter v-model:custom-filtered-value="searchCategory" :filters="$filter0" @filter-change="handleRefresh()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].category`">
                  <el-input v-model="row.category"></el-input>
                </el-form-item>
                <template v-else>{{ row.category }}</template>
              </template>
            </TableColumn>
            <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="search.ticketTypeList" @filter-change="handleRefresh()" prop="ticketType" label="工单类型" :width="170" :filters="ticketTypesList.map((v) => ({ ...v, text: v.label }))">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].ticketType`" :rules="[{ required: row.isEdit, message: '工单类型不能为空', trigger: 'blur' }]">
                  <el-select v-model="row.ticketType" filterable placeholder="请选择" @change="() => changeticketType(row)">
                    <el-option v-for="item in ticketTypesList" :key="item.ticketType" :label="item.ticketName" :value="item.ticketType"> </el-option>
                  </el-select>
                </el-form-item>
                <template v-else> {{ getCombinedTypeDisplay(row.ticketType, row.type) }}</template>
              </template>
            </TableColumn>
            <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="search.relevancyList" @filter-change="handleRefresh()" prop="ticketType" label="关联表单" :width="170" :filters="relevancyTypes.map((v) => ({ ...v, text: v.label }))">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].relevancy`" :rules="[{ required: row.isEdit, message: '关联表单不能为空', trigger: 'blur' }]">
                  <el-select class="tw-w-full" v-model="row.relevancy" filterable placeholder="请选择">
                    <el-option v-for="item in relevancyOptions[row.ticketType]" :key="`relevancy-${item.value}`" :label="item.label" :value="item.value"> </el-option>
                  </el-select>
                </el-form-item>
                <template v-else>
                  {{ relevancyOptions[row.ticketType] ? (relevancyOptions[row.ticketType].find((v) => v.value === row.relevancy) || {}).label : "" }}
                </template>
              </template>
            </TableColumn>
            <TableColumn type="condition" prop="orderSubclass" label="工单子类型" filter-multiple show-filter v-model:custom-filtered-value="searchOrderSubclass" :filters="$filter0" @filter-change="handleRefresh()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].orderSubclass`" :rules="[{ required: row.isEdit, message: '工单子类型不能为空', trigger: 'blur' }]">
                  <el-input v-model="row.orderSubclass"></el-input>
                </el-form-item>
                <template v-else>{{ row.orderSubclass }}</template>
              </template>
            </TableColumn>
            <TableColumn type="condition" prop="ticketGroupName" label="工单组" filter-multiple show-filter v-model:custom-filtered-value="searchTicketGroupName" :filters="$filter0" @filter-change="handleRefresh()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].ticketGroupName`">
                  <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(服务管理中心_工单模版_分配工单组)">
                    <el-select v-if="orderGrouplist.length > 0" :disabled="!userInfo.hasPermission(服务管理中心_工单模版_分配工单组)" v-model="row.ticketGroupId" filterable placeholder="请选择" @change="(newGroupId) => handleGroupChange(newGroupId, row)">
                      <el-option v-for="item in checkGroupList(orderGrouplist, row)" :key="item.id" :label="item.ticketGroupName" :value="item.id" />
                    </el-select>
                    <el-select v-else :disabled="!userInfo.hasPermission(服务管理中心_工单模版_分配工单组)" filterable placeholder="请选择" @change="(newGroupId) => handleGroupChange(newGroupId, row)">
                      <el-option v-for="item in orderGrouplist" :key="item.id" :label="item.ticketGroupName" :value="item.id" />
                    </el-select>
                  </el-tooltip>
                </el-form-item>
                <template v-else>{{ isValidGroup(row.ticketGroupId) ? row.ticketGroupName : "" }}</template>
              </template>
            </TableColumn>
            <TableColumn type="condition" prop="assignUserGroups" label="分配用户组" filter-multiple show-filter v-model:custom-filtered-value="searchAssignUserGroupName" :filters="$filter0" @filter-change="handleRefresh()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].assignUserGroups`">
                  <!-- <el-select v-model="row.assignUserGroups" class="tw-w-full" placeholder="请选择用户组">
                    <el-option v-for="item in userGroupOption" :key="item.userGrouptId" :label="item.userGroupName" :value="item.userGrouptId"></el-option>
                  </el-select> -->
                  <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(服务管理中心_工单模版_分配用户组)">
                    <el-select v-if="userGroupOption.length > 0" :disabled="!userInfo.hasPermission(服务管理中心_工单模版_分配用户组)" v-model="row.assignUserGroups" filterable placeholder="请选择">
                      <el-option v-for="item in checkUserGroupList(userGroupOption, row)" :key="item.userGrouptId" :label="item.userGroupName" :value="item.userGrouptId" />
                    </el-select>
                    <el-select v-else :disabled="!userInfo.hasPermission(服务管理中心_工单模版_分配用户组)" filterable placeholder="请选择">
                      <el-option v-for="item in userGroupOption" :key="item.userGrouptId" :label="item.userGroupName" :value="item.userGrouptId" />
                    </el-select>
                  </el-tooltip>
                </el-form-item>
                <template v-else>
                  <!-- 显示时从缓存获取 -->
                  {{ userGroupCache[row.ticketGroupId]?.find((item) => item.userGroupId === row.assignUserGroups)?.userGroupName }}
                </template>
                <!-- <template v-else> {{ userGroupOption.find((item) => item.userGrouptId === row.assignUserGroups)?.userGroupName }}</template> -->
              </template>
            </TableColumn>
            <TableColumn type="enum" :prop="`createSubmit`" label="提交时创建" :width="80" :showOverflowTooltip="true" show-filter v-model:filtered-value="search.createSubmit" :filters="[false, true].map((v) => ({ value: v, text: v === false ? '✕' : '✓' }))" @filter-change="handleRefresh()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].createSubmit`">
                  <el-checkbox v-model="row.createSubmit"></el-checkbox>
                </el-form-item>
                <template v-else>{{ row.createSubmit ? "√" : "" }}</template>
              </template>
            </TableColumn>
            <TableColumn type="condition" prop="levelService" label="服务等级" filter-multiple show-filter v-model:custom-filtered-value="searchLevelServiceName" :filters="$filter0" @filter-change="handleRefresh()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].levelService`">
                  <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(服务管理中心_工单模版_分配SLA)">
                    <el-select v-if="slaOptions.length > 0" :disabled="!userInfo.hasPermission(服务管理中心_工单模版_分配SLA)" v-model="row.levelService" filterable placeholder="请选择服务等级" :empty-values="['', undefined]" :value-on-clear="''">
                      <el-option label="不设置" :value="null" key="no_setting"></el-option>
                      <el-option v-for="slaItem in checkSlaList(slaOptions, row)" :key="slaItem.ruleId" :label="slaItem.ruleName" :value="slaItem.ruleId"> </el-option>
                    </el-select>
                    <el-select v-else :disabled="!userInfo.hasPermission(服务管理中心_工单模版_分配SLA)" filterable placeholder="请选择" @change="(newGroupId) => handleGroupChange(newGroupId, row)">
                      <el-option v-for="item in slaOptions" :key="item.ruleId" :label="item.ruleName" :value="item.ruleId" />
                    </el-select>
                  </el-tooltip>
                </el-form-item>
                <template v-else> {{ getRuleName(row.levelService) }} </template>
              </template>
            </TableColumn>
            <TableColumn type="condition" :prop="`zoneId`" label="时区" width="150" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByZoneId" :filters="$filter0" @filter-change="handleRefresh()" :formatter="formatterTable">
              <template #header>
                <span style="display: inline-flex; align-items: center; gap: 4px">
                  时区
                  <el-icon @click="workorderHelp" style="color: #2a8bf5; cursor: pointer; margin-left: 10px"><QuestionFilled /></el-icon>
                </span>
              </template>
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].zoneId`">
                  <el-select v-model="row.zoneId" filterable placeholder="请选择时区" class="tw-w-full">
                    <el-option v-for="item in timeZone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"> </el-option>
                  </el-select>
                </el-form-item>
                <template v-else> {{ timeZone.find((t) => t.zoneId === row.zoneId)?.displayName || row.zoneId }}</template>
              </template>
            </TableColumn>
            <TableColumn type="enum" :prop="`active`" label="激活" :width="80" :showOverflowTooltip="true" show-filter v-model:filtered-value="search.active" :filters="[false, true].map((v) => ({ value: v, text: v === false ? '✕' : '✓' }))" @filter-change="handleRefresh()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].active`">
                  <el-checkbox v-model="row.active"></el-checkbox>
                </el-form-item>
                <template v-else>{{ row.active ? "√" : "" }}</template>
              </template>
            </TableColumn>
            <TableColumn type="default" label="操作">
              <template #default="{ row }">
                <template v-if="row.isEdit">
                  <el-button type="primary" v-if="userInfo.hasPermission(服务管理中心_工单模版_编辑)" link @click="handleSetSubmit(row)">保存</el-button>
                  <el-button type="primary" v-if="userInfo.hasPermission(服务管理中心_工单模版_编辑)" link @click="handleRefresh">取消</el-button>
                </template>
                <template v-else>
                  <el-button type="primary" link v-if="row.verifyPermissionIds.includes(服务管理中心_工单模版_编辑)" @click="hendleSetItem(row)">编辑</el-button>
                  <el-popconfirm :title="`确定删除'${row.ticketTemplatesName}'吗?`" @confirm="handleDelItem(row)">
                    <template #reference>
                      <el-button type="danger" v-if="row.verifyPermissionIds.includes(服务管理中心_工单模版_删除)" link>删除</el-button>
                    </template>
                  </el-popconfirm>
                  <el-button type="primary" link :icon="Security" style="font-size: 22px" v-if="row.verifyPermissionIds.includes(服务管理中心_工单模版_安全)" @click="showSecurityTree({ containerId: row.containerId })"></el-button>
                </template>
              </template>
            </TableColumn>
          </el-table>
        </el-form>
        <el-dialog v-model="workorderdialog" :show-close="false" width="30%">
          <template #header="{ close, titleId, titleClass }">
            <div class="my-header">
              <h4 :id="titleId" :class="titleClass">时区规则</h4>
              <el-icon style="cursor: pointer" @click="close" class="el-icon--left"><Close /></el-icon>
            </div>
          </template>
          <div class="text">
            <p>SLA时区为自动时区情况:如果工单模版时区为一个具体的时区,则采用该具体时区,如果工单模版定义的为客户时区,则采用客户时区。</p>
            <p>SLA时区为一个具体的时区的情况,无论工单模版的时区定义为哪一个,都将采用SLA的时区。</p>
          </div>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="workorderdialog = false">关闭</el-button>
            </span>
          </template>
        </el-dialog>
        <addTemplate ref="addTemplateRef" @refresh="handleRefresh" />
      </template>
    </pageTemplate>
  </el-card>
</template>

<script setup lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { ref, reactive, inject, onMounted, nextTick, toValue, computed } from "vue";

import { ElMessage, type TableColumnCtx, FormInstance } from "element-plus";
import { Plus, WarningFilled, Check } from "@element-plus/icons-vue";
import Security from "@/assets/dp.vue";

import { sizes } from "@/utils/common";

import getUserInfo from "@/utils/getUserInfo";
import { QuestionFilled } from "@element-plus/icons-vue";

import pageTemplate from "@/components/pageTemplate.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import addTemplate from "./addTemplate.vue";
import assignPage from "./assignPage.vue";
import workOrderConfig from "./workOrderConfig.vue";

import zone from "@/views/pages/common/zone.json";
import { getAllticketTemplates, getconfigurationGroup, setAllticketTemplates, delAllticketTemplates, ticketTypes, type TicketType, relevancyOptions, relevancyTypes } from "@/views/pages/apis/ticketTemplate";

import showSecurityTree from "@/components/security-container";
import _timeZone from "@/views/pages/common/contactsZone.json";
import moment from "moment";
import { getGroup } from "@/api/personnel";
import { getOrderGroup, type OrderGroup } from "@/views/pages/apis/orderGroup";
import { getnewSlaquerySlaList } from "@/views/pages/apis/SlaConfig";
import { changeType, publishType } from "@/views/pages/apis/ticketTemplate";

const workOrderConfigRef = ref(null);

import { 服务管理中心_工单模版_可读, 服务管理中心_工单模版_新增, 服务管理中心_工单模版_编辑, 服务管理中心_工单模版_删除, 服务管理中心_工单模版_安全, 服务管理中心_工单模版_分配用户组, 服务管理中心_工单模版_分配SLA, 服务管理中心_工单模版_分配工单组, 服务管理中心_SLA配置_分配工单模版 } from "@/views/pages/permission";

defineOptions({ name: "workorderTemplate" });

const width = inject("width", ref(0));
const height = inject("height", ref(0));
const slaOptions = ref([]);
const workorderdialog = ref(false);
const timeZone = ref([{ zoneId: "客户时区", displayName: "客户时区", offsetMinutes: 0 }].concat(_timeZone));
const userGroupOption = ref<Record<"name" | "code", string>[]>([]);
const ticketTypesList = ref<TicketType[]>(ticketTypes);
const orderGrouplist = ref<OrderGroup[]>([]);
// 在组件中定义缓存对象
const userGroupCache = ref<Record<string, Array<{ userGroupId: string; userGroupName: string }>>>({});

const userInfo: any = getUserInfo();

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: any };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: TableColumnCtx<Record<string, any>>, value: T[keyof T]) => string | import("vue").VNode; showOverflowTooltip?: boolean }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}

const state = reactive<StateData<[]>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  column: [],
  data: [],
  page: 1,
  size: 50,
  sizes,
  total: 0,
});

const addTemplateRef = ref<InstanceType<typeof addTemplate>>();

const formRef = ref<FormInstance>();

const getCombinedTypeDisplay = (ticketType: string, changeTypeVal: string) => {
  const ticket = ticketTypesList.value.find((t) => t.ticketType === ticketType);
  const ticketName = ticket?.ticketName || ticketType;

  // 根据工单类型名称匹配对应的配置
  const typeConfigMap = {
    变更管理: changeType,
    发布管理: publishType,
  } as Record<string, Array<{ value: string; label: string }>>;

  const matchedLabel = typeConfigMap[ticketName]?.find((item) => item.value === changeTypeVal)?.label;

  return matchedLabel ? `${ticketName}（${matchedLabel}）` : ticketName;
};
// 检查工单组ID是否有效
function isValidGroup(groupId) {
  return orderGrouplist.value.some((item) => item.id === groupId);
}
// 修改后的工具函数
function checkGroupList(alarmList, row) {
  if (!alarmList || alarmList.length === 0) {
    row.ticketGroupId = "";
    return [];
  }

  const isValid = alarmList.some((item) => String(item.id) === String(row.ticketGroupId));
  if (row.ticketGroupId && !isValid) {
    row.ticketGroupId = "";
  }

  return alarmList;
}
function checkUserGroupList(alarmList, row) {
  if (!alarmList || alarmList.length === 0) {
    row.assignUserGroups = "";
    return [];
  }

  const isValid = alarmList.some((item) => String(item.userGrouptId) === String(row.assignUserGroups));
  if (row.assignUserGroups && !isValid) {
    row.assignUserGroups = "";
  }

  return alarmList;
}

// 修改后的工具函数
function checkSlaList(alarmList, row) {
  if (!alarmList || alarmList.length === 0) {
    row.levelService = "";
    return [];
  }

  const isValid = alarmList.some((item) => String(item.ruleId) === String(row.levelService));
  if (row.levelService && !isValid) {
    row.levelService = "";
  }

  return alarmList;
}
const changeticketType = (row) => {
  if (workOrderConfigRef.value) {
    workOrderConfigRef.value.form.workorderStatus = "";
    // 判断工单类型并设置状态
    if (row.ticketType === "event" || row.ticketType === "service" || row.ticketType === "question") {
      workOrderConfigRef.value.form.workorderStatus = "NEW";
    } else if (row.ticketType === "dictevent" || row.ticketType === "dictservice") {
      workOrderConfigRef.value.form.workorderStatus = "NEW";
    } else {
      workOrderConfigRef.value.form.workorderStatus = "";
    }
  }

  /* 关联表单 */
  row.relevancy = (relevancyOptions[row.ticketType].find((v) => v) || {}).value;
};

async function getGroupList(id) {
  userGroupOption.value = [];
  await (async () => {
    const { success, message, data } = await getconfigurationGroup({ ticketGroupId: id });
    if (!success) throw Object.assign(new Error(message), { success, data });
    userGroupOption.value = data || [];
  })();
}
async function getSlaList() {
  const parameters = {
    containerId: userInfo.currentTenant.containerId,
    queryPermissionId: 服务管理中心_SLA配置_分配工单模版,
    verifyPermissionIds: "",
  };
  try {
    const res = await getnewSlaquerySlaList(parameters);
    if (res) slaOptions.value = res;
  } catch (error) {
    error instanceof Error && ElMessage.error(error);
  }
}
async function queryOrderGroup() {
  // 查询工单组数据
  try {
    const { data, message, success } = await getOrderGroup({ pageNumber: 1, pageSize: 999999, queryPermissionId: 服务管理中心_工单模版_分配工单组 });
    if (!success) throw new Error(message);
    orderGrouplist.value = data;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}
function workorderHelp() {
  workorderdialog.value = true;
}
function hendleCreate(row) {
  if (!addTemplateRef.value) return false;
  addTemplateRef.value.open(row);
}

function hendleSetItem(row) {
  if (state.data.filter((v: any) => v.isEdit).length) return ElMessage.warning("请先保存正在编辑的信息");
  row.isEdit = true;
  getGroupList(row.ticketGroupId);
}

function handleSetSubmit(row) {
  const ticketGroupNamelist = orderGrouplist.value.find((item) => item.id === row.ticketGroupId) || {};
  formRef.value &&
    formRef.value.validate(async (valid) => {
      if (!valid) return;
      try {
        const { id, ticketTemplatesName, description, category, ticketType, ticketGroupId, assignUserGroups, levelService, zoneId, active, createSubmit, orderSubclass, relevancy } = row;
        const params = {
          id,
          ticketTemplatesName,
          description,
          category,
          ticketType,
          ticketGroupId,
          ticketGroupName: ticketGroupNamelist.ticketGroupName || null,
          assignUserGroups,
          levelService: levelService == "null" ? "" : levelService,
          zoneId,
          active,
          type: row.type,
          createSubmit,
          orderSubclass,
          relevancy,
        };
        const { message, success } = await setAllticketTemplates(params);
        if (!success) throw new Error(message);
        ElMessage.success("操作成功");
        await nextTick();
        handleRefresh();
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    });
}

async function handleDelItem(row) {
  try {
    const { message, success } = await delAllticketTemplates({ prodId: row.id });
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    handleRefresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

const expands = ref<string[]>([]);
function expandColumn(row, expandedRows) {
  // 每次只展开一行
  if (expandedRows.length) {
    expands.value = [];
    if (row) {
      expands.value.push(row.id);
    }
  } else {
    expands.value = [];
  }
}

function getRuleName(levelService) {
  if (levelService === null || levelService === "null") {
    return "不设置"; // 直接返回“不设置”文本
  }
  // 在 slaOptions 中查找匹配的 ruleId
  const found = slaOptions.value.find((item) => item.ruleId === levelService);
  return found ? found.ruleName : ""; // 找不到则返回空字符串
}
function formatterTable(_row, _col, _v) {
  // // console.log(v);
  switch (_col.property) {
    case "zoneId":
      return timeZone.value.find((i) => i.zoneId === _v)?.displayName || "--";
    default:
      return _v || "--";
  }
}
async function handleRefresh() {
  const params = {
    pageNumber: state.page,
    pageSize: state.size,
    containerId: userInfo.currentTenant.containerId,
    queryPermissionId: 服务管理中心_工单模版_可读,
    verifyPermissionIds: [服务管理中心_工单模版_新增, 服务管理中心_工单模版_编辑, 服务管理中心_工单模版_删除, 服务管理中心_工单模版_安全].join(","),
    ...search.value,
  };
  state.loading = true;
  try {
    const { data, message, success, total } = await getAllticketTemplates({ ...params });
    if (!success) throw new Error(message);
    state.data = data.map((v) => Object.assign(v, { isEdit: false, zoneId: v.zoneId === null ? "China Standard Time" : v.zoneId, levelService: v.levelService === "" ? "null" : v.levelService }));
    state.total = Number(total);
    const groupIds = [...new Set(data.map((item) => item.ticketGroupId))];

    await Promise.all(
      groupIds.map(async (groupId) => {
        if (!userGroupCache.value[groupId]) {
          // 避免重复加载
          const { data } = await getconfigurationGroup({ ticketGroupId: groupId });
          userGroupCache.value[groupId] = data.map((item) => ({
            userGroupId: item.userGrouptId,
            userGroupName: item.userGroupName,
          }));
        }
      })
    );
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    state.loading = false;
  }
}
async function handleGroupChange(newGroupId: string, row: any) {
  // 清空原有的用户组数据
  userGroupOption.value = [];
  row.assignUserGroups = null;
  await getGroupList(newGroupId);
}
const search = ref({
  // 名称
  excludeName: [],
  includeName: [],
  nameFilterRelation: "AND",
  eqName: [],
  neName: [],
  // 描述
  includeDesc: [],
  excludeDesc: [],
  descFilterRelation: "AND",
  eqDesc: [],
  neDesc: [],
  // 类别
  includeCategory: [],
  excludeCategory: [],
  categoryFilterRelation: "AND",
  eqCategory: [],
  neCategory: [],
  // 工单子类
  excludeOrderSubclass: [],
  eqOrderSubclass: [],
  neOrderSubclass: [],
  includeOrderSubclass: [],
  orderSubclassFilterRelation: "AND",
  // 工单组
  includeTicketGroupName: [],
  eqTicketGroupName: [],
  neTicketGroupName: [],
  excludeTicketGroupName: [],
  ticketGroupNameFilterRelation: "AND",
  //服务等级
  includeLevelServiceName: [],
  excludeLevelServiceName: [],
  levelServiceNameFilterRelation: "AND",
  eqLevelServiceName: [],
  neLevelServiceName: [],
  //用户组
  includeAssignUserGroupName: [],
  excludeAssignUserGroupName: [],
  assignUserGroupNameFilterRelation: "AND",
  eqAssignUserGroupName: [],
  neAssignUserGroupName: [],
  // 时区
  eqZoneId: [] /* 等于的场所时区ID */,
  includeZoneId: [] /* 包含的场所时区ID */,
  zoneIdFilterRelation: "AND" /* 场所时区ID过滤关系(AND,OR) */,
  neZoneId: [] /* 不等于的场所时区ID */,
  excludeZoneId: [] /* 不包含的场所时区ID */,
});
import { exoprtMatch1, exoprtMatch2, exoprtMatch3 } from "@/components/tableColumn/common";
const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);
const $filter2 = ref(exoprtMatch3);
const searchForm = ref<Record<string, any>>({});
// 名称
const search0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const search1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(search0ByName) === "include") value0 = search.value.includeName[0] || "";
    if (toValue(search0ByName) === "exclude") value0 = search.value.excludeName[0] || "";
    if (toValue(search0ByName) === "eq") value0 = search.value.eqName[0] || "";
    if (toValue(search0ByName) === "ne") value0 = search.value.neName[0] || "";
    let value1 = "";
    if (toValue(search1ByName) === "include") value1 = search.value.includeName[search.value.includeName.length - 1] || "";
    if (toValue(search1ByName) === "exclude") value1 = search.value.excludeName[search.value.excludeName.length - 1] || "";
    if (toValue(search1ByName) === "eq") value1 = search.value.eqName[search.value.eqName.length - 1] || "";
    if (toValue(search1ByName) === "ne") value1 = search.value.neName[search.value.neName.length - 1] || "";
    return {
      type0: toValue(search0ByName),
      type1: toValue(search1ByName),
      relation: search.value.nameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    search0ByName.value = v.type0 as typeof search0ByName extends import("vue").Ref<infer T> ? T : string;
    search1ByName.value = v.type1 as typeof search1ByName extends import("vue").Ref<infer T> ? T : string;
    search.value.nameFilterRelation = v.relation;
    search.value.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
// 描述
const search0ByDesc = ref<"include" | "exclude" | "eq" | "ne">("include");
const search1ByDesc = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchDesc = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(search0ByDesc) === "include") value0 = search.value.includeDesc[0] || "";
    if (toValue(search0ByDesc) === "exclude") value0 = search.value.excludeDesc[0] || "";
    if (toValue(search0ByDesc) === "eq") value0 = search.value.eqDesc[0] || "";
    if (toValue(search0ByDesc) === "ne") value0 = search.value.neDesc[0] || "";
    let value1 = "";
    if (toValue(search1ByDesc) === "include") value1 = search.value.includeDesc[search.value.includeDesc.length - 1] || "";
    if (toValue(search1ByDesc) === "exclude") value1 = search.value.excludeDesc[search.value.excludeDesc.length - 1] || "";
    if (toValue(search1ByDesc) === "eq") value1 = search.value.eqDesc[search.value.eqDesc.length - 1] || "";
    if (toValue(search1ByDesc) === "ne") value1 = search.value.neDesc[search.value.neDesc.length - 1] || "";
    return {
      type0: toValue(search0ByDesc),
      type1: toValue(search1ByDesc),
      relation: search.value.descFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    search0ByDesc.value = v.type0 as typeof search0ByDesc extends import("vue").Ref<infer T> ? T : string;
    search1ByDesc.value = v.type1 as typeof search1ByDesc extends import("vue").Ref<infer T> ? T : string;
    search.value.descFilterRelation = v.relation;
    search.value.includeDesc = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeDesc = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqDesc = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neDesc = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
// 类别
const search0ByCategory = ref<"include" | "exclude" | "eq" | "ne">("include");
const search1ByCategory = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchCategory = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(search0ByCategory) === "include") value0 = search.value.includeCategory[0] || "";
    if (toValue(search0ByCategory) === "exclude") value0 = search.value.excludeCategory[0] || "";
    if (toValue(search0ByCategory) === "eq") value0 = search.value.eqCategory[0] || "";
    if (toValue(search0ByCategory) === "ne") value0 = search.value.neCategory[0] || "";
    let value1 = "";
    if (toValue(search1ByCategory) === "include") value1 = search.value.includeCategory[search.value.includeCategory.length - 1] || "";
    if (toValue(search1ByCategory) === "exclude") value1 = search.value.excludeCategory[search.value.excludeCategory.length - 1] || "";
    if (toValue(search1ByCategory) === "eq") value1 = search.value.eqCategory[search.value.eqCategory.length - 1] || "";
    if (toValue(search1ByCategory) === "ne") value1 = search.value.neCategory[search.value.neCategory.length - 1] || "";
    return {
      type0: toValue(search0ByCategory),
      type1: toValue(search1ByCategory),
      relation: search.value.categoryFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    search0ByCategory.value = v.type0 as typeof search0ByCategory extends import("vue").Ref<infer T> ? T : string;
    search1ByCategory.value = v.type1 as typeof search1ByCategory extends import("vue").Ref<infer T> ? T : string;
    search.value.categoryFilterRelation = v.relation;
    search.value.includeCategory = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeCategory = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqCategory = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neCategory = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
// 工单组
const search0ByTicketGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const search1ByTicketGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchTicketGroupName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(search0ByTicketGroupName) === "include") value0 = search.value.includeTicketGroupName[0] || "";
    if (toValue(search0ByTicketGroupName) === "exclude") value0 = search.value.excludeTicketGroupName[0] || "";
    if (toValue(search0ByTicketGroupName) === "eq") value0 = search.value.eqTicketGroupName[0] || "";
    if (toValue(search0ByTicketGroupName) === "ne") value0 = search.value.neTicketGroupName[0] || "";
    let value1 = "";
    if (toValue(search1ByTicketGroupName) === "include") value1 = search.value.includeTicketGroupName[search.value.includeTicketGroupName.length - 1] || "";
    if (toValue(search1ByTicketGroupName) === "exclude") value1 = search.value.excludeTicketGroupName[search.value.excludeTicketGroupName.length - 1] || "";
    if (toValue(search1ByTicketGroupName) === "eq") value1 = search.value.eqTicketGroupName[search.value.eqTicketGroupName.length - 1] || "";
    if (toValue(search1ByTicketGroupName) === "ne") value1 = search.value.neTicketGroupName[search.value.neTicketGroupName.length - 1] || "";
    return {
      type0: toValue(search0ByTicketGroupName),
      type1: toValue(search1ByTicketGroupName),
      relation: search.value.ticketGroupNameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    search0ByTicketGroupName.value = v.type0 as typeof search0ByTicketGroupName extends import("vue").Ref<infer T> ? T : string;
    search1ByTicketGroupName.value = v.type1 as typeof search1ByTicketGroupName extends import("vue").Ref<infer T> ? T : string;
    search.value.ticketGroupNameFilterRelation = v.relation;
    search.value.includeTicketGroupName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeTicketGroupName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqTicketGroupName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neTicketGroupName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
// 用户组
const search0ByAssignUserGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const search1ByAssignUserGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchAssignUserGroupName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(search0ByAssignUserGroupName) === "include") value0 = search.value.includeAssignUserGroupName[0] || "";
    if (toValue(search0ByAssignUserGroupName) === "exclude") value0 = search.value.excludeAssignUserGroupName[0] || "";
    if (toValue(search0ByAssignUserGroupName) === "eq") value0 = search.value.eqAssignUserGroupName[0] || "";
    if (toValue(search0ByAssignUserGroupName) === "ne") value0 = search.value.neAssignUserGroupName[0] || "";
    let value1 = "";
    if (toValue(search1ByAssignUserGroupName) === "include") value1 = search.value.includeAssignUserGroupName[search.value.includeAssignUserGroupName.length - 1] || "";
    if (toValue(search1ByAssignUserGroupName) === "exclude") value1 = search.value.excludeAssignUserGroupName[search.value.excludeAssignUserGroupName.length - 1] || "";
    if (toValue(search1ByAssignUserGroupName) === "eq") value1 = search.value.eqAssignUserGroupName[search.value.eqAssignUserGroupName.length - 1] || "";
    if (toValue(search1ByAssignUserGroupName) === "ne") value1 = search.value.neAssignUserGroupName[search.value.neAssignUserGroupName.length - 1] || "";
    return {
      type0: toValue(search0ByAssignUserGroupName),
      type1: toValue(search1ByAssignUserGroupName),
      relation: search.value.assignUserGroupNameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    search0ByAssignUserGroupName.value = v.type0 as typeof search0ByAssignUserGroupName extends import("vue").Ref<infer T> ? T : string;
    search1ByAssignUserGroupName.value = v.type1 as typeof search1ByAssignUserGroupName extends import("vue").Ref<infer T> ? T : string;
    search.value.assignUserGroupNameFilterRelation = v.relation;
    search.value.includeAssignUserGroupName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeAssignUserGroupName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqAssignUserGroupName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neAssignUserGroupName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
// 服务等级
const search0ByLevelServiceName = ref<"include" | "exclude" | "eq" | "ne">("include");
const search1ByLevelServiceName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchLevelServiceName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(search0ByLevelServiceName) === "include") value0 = search.value.includeLevelServiceName[0] || "";
    if (toValue(search0ByLevelServiceName) === "exclude") value0 = search.value.excludeLevelServiceName[0] || "";
    if (toValue(search0ByLevelServiceName) === "eq") value0 = search.value.eqLevelServiceName[0] || "";
    if (toValue(search0ByLevelServiceName) === "ne") value0 = search.value.neLevelServiceName[0] || "";
    let value1 = "";
    if (toValue(search1ByLevelServiceName) === "include") value1 = search.value.includeLevelServiceName[search.value.includeLevelServiceName.length - 1] || "";
    if (toValue(search1ByLevelServiceName) === "exclude") value1 = search.value.excludeLevelServiceName[search.value.excludeLevelServiceName.length - 1] || "";
    if (toValue(search1ByLevelServiceName) === "eq") value1 = search.value.eqLevelServiceName[search.value.eqLevelServiceName.length - 1] || "";
    if (toValue(search1ByLevelServiceName) === "ne") value1 = search.value.neLevelServiceName[search.value.neLevelServiceName.length - 1] || "";
    return {
      type0: toValue(search0ByLevelServiceName),
      type1: toValue(search1ByLevelServiceName),
      relation: search.value.levelServiceNameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    search0ByLevelServiceName.value = v.type0 as typeof search0ByLevelServiceName extends import("vue").Ref<infer T> ? T : string;
    search1ByLevelServiceName.value = v.type1 as typeof search1ByLevelServiceName extends import("vue").Ref<infer T> ? T : string;
    search.value.levelServiceNameFilterRelation = v.relation;
    search.value.includeLevelServiceName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeLevelServiceName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqLevelServiceName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neLevelServiceName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
// 工单子类
const search0ByOrderSubclass = ref<"include" | "exclude" | "eq" | "ne">("include");
const search1ByOrderSubclass = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchOrderSubclass = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(search0ByOrderSubclass) === "include") value0 = search.value.includeOrderSubclass[0] || "";
    if (toValue(search0ByOrderSubclass) === "exclude") value0 = search.value.excludeOrderSubclass[0] || "";
    if (toValue(search0ByOrderSubclass) === "eq") value0 = search.value.eqOrderSubclass[0] || "";
    if (toValue(search0ByOrderSubclass) === "ne") value0 = search.value.neOrderSubclass[0] || "";
    let value1 = "";
    if (toValue(search1ByOrderSubclass) === "include") value1 = search.value.includeOrderSubclass[search.value.includeOrderSubclass.length - 1] || "";
    if (toValue(search1ByOrderSubclass) === "exclude") value1 = search.value.excludeOrderSubclass[search.value.excludeOrderSubclass.length - 1] || "";
    if (toValue(search1ByOrderSubclass) === "eq") value1 = search.value.eqOrderSubclass[search.value.eqOrderSubclass.length - 1] || "";
    if (toValue(search1ByOrderSubclass) === "ne") value1 = search.value.neOrderSubclass[search.value.neOrderSubclass.length - 1] || "";
    return {
      type0: toValue(search0ByOrderSubclass),
      type1: toValue(search1ByOrderSubclass),
      relation: search.value.orderSubclassFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    search0ByOrderSubclass.value = v.type0 as typeof search0ByOrderSubclass extends import("vue").Ref<infer T> ? T : string;
    search1ByOrderSubclass.value = v.type1 as typeof search1ByOrderSubclass extends import("vue").Ref<infer T> ? T : string;
    search.value.orderSubclassFilterRelation = v.relation;
    search.value.includeOrderSubclass = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeOrderSubclass = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqOrderSubclass = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neOrderSubclass = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByZoneId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByZoneId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByZoneId = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByZoneId) === "include") value0 = search.value.includeZoneId[0] || "";
    if (toValue(searchType0ByZoneId) === "exclude") value0 = search.value.excludeZoneId[0] || "";
    if (toValue(searchType0ByZoneId) === "eq") value0 = search.value.eqZoneId[0] || "";
    if (toValue(searchType0ByZoneId) === "ne") value0 = search.value.neZoneId[0] || "";
    let value1 = "";
    if (toValue(searchType1ByZoneId) === "include") value1 = search.value.includeZoneId[search.value.includeZoneId.length - 1] || "";
    if (toValue(searchType1ByZoneId) === "exclude") value1 = search.value.excludeZoneId[search.value.excludeZoneId.length - 1] || "";
    if (toValue(searchType1ByZoneId) === "eq") value1 = search.value.eqZoneId[search.value.eqZoneId.length - 1] || "";
    if (toValue(searchType1ByZoneId) === "ne") value1 = search.value.neZoneId[search.value.neZoneId.length - 1] || "";
    return {
      type0: toValue(searchType0ByZoneId),
      type1: toValue(searchType1ByZoneId),
      relation: search.value.zoneIdFilterRelation,
      value0,
      value1,
      // input0: "",
      input0: timeZone.value.reduce((p, c) => (p.append(c.zoneId, c.displayName), p), new URLSearchParams()).toString(),
      // input1: "",
      input1: timeZone.value.reduce((p, c) => (p.append(c.zoneId, c.displayName), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByZoneId.value = v.type0 as typeof searchType0ByZoneId extends import("vue").Ref<infer T> ? T : string;
    searchType1ByZoneId.value = v.type1 as typeof searchType1ByZoneId extends import("vue").Ref<infer T> ? T : string;
    search.value.zoneIdFilterRelation = v.relation;
    search.value.includeZoneId = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeZoneId = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqZoneId = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neZoneId = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
onMounted(() => {
  handleRefresh();
  // getGroupList();
  queryOrderGroup();
  getSlaList();
});
</script>

<style scoped lang="scss">
.worktitle {
  display: flex;
  align-items: center;
  h2 {
    display: flex;
    align-items: center;
    height: 100%;
    font-size: 14px;
    padding-left: 10px;
    box-sizing: border-box;
    font-weight: 700;
    margin-top: 15px;
  }
}

.text p {
  text-indent: 2em;
  margin-bottom: 8px;
}
</style>
