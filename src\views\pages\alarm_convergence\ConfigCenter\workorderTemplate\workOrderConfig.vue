<template>
  <el-row :gutter="20" class="card-row" v-loading="configloading">
    <el-col :span="8">
      <el-card style="max-width: 480px" shadow="never">
        <template #header>
          <div class="card-header">
            <span> {{ templateTitle }}</span>
          </div>
        </template>
        <el-form :model="form" ref="formRef" label-width="auto">
          <el-form-item v-if="props.workOrderType === 'change'" label="变更类型" prop="workOrderType">
            <el-select v-model="form.workorderStatus" filterable placeholder="请选择">
              <el-option v-for="item in changeType" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-else-if="props.workOrderType === 'publish'" label="发布类型" prop="workOrderType">
            <el-select v-model="form.workorderStatus" filterable placeholder="请选择">
              <el-option v-for="item in publishType" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-else-if="props.workOrderType === 'dictevent' || props.workOrderType === 'dictservice'" label="初始化状态" prop="projectCode">
            <el-select v-model="form.workorderStatus" filterable placeholder="请选择">
              <el-option v-for="item in dictStateOption" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-else label="初始化状态" prop="projectCode">
            <el-select v-model="form.workorderStatus" filterable placeholder="请选择">
              <el-option v-for="item in StateOption" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="isApproval" v-if="props.workOrderType === 'change' || props.workOrderType === 'publish'">
            <span class="checkbox-label">审批</span>
            <el-checkbox v-model="form.isApproval"></el-checkbox>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="tw-flex tw-justify-end">
            <el-button type="primary" @click="handleAddItem" :loading="configLoading" v-if="userInfo.hasPermission(服务管理中心_工单模版_编辑)">保存配置</el-button>
          </div>
        </template>
      </el-card>
    </el-col>
    <el-col :span="8" v-if="(props.workOrderType === 'change' || props.workOrderType === 'publish') && !!form?.isApproval">
      <el-card style="max-width: 480px" shadow="never">
        <template #header>
          <div class="card-header" style="display: flex; justify-content: space-between; align-items: center">
            <span> 审批组</span>
            <el-button type="primary" :icon="Plus" @click="openApproval" v-if="userInfo.hasPermission(服务管理中心_工单模版_分配用户组)">分配审批组</el-button>
          </div>
        </template>
        <el-table :data="userGroupslist" border stripe v-loading="false">
          <el-table-column type="default" prop="name" label="用户组">
            <template #default="{ row }">
              {{ row.name || "--" }}
            </template>
          </el-table-column>
          <el-table-column type="default" label="操作" width="200">
            <template #default="{ row }">
              <el-popconfirm :title="`删除当前数据吗?`" @confirm="delRefresh(row)">
                <template #reference>
                  <el-button type="danger" link>删除 </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-col>
  </el-row>
  <createApproval ref="createApprovalRef" :ticketGroupId="props.ticketGroupId" :type="form.workorderStatus" :tickettype="props.workOrderType" :isEdit="props.isEdit" />
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from "vue";

import { ElMessage, formItemValidateStates } from "element-plus";

import createApproval from "./createApproval.vue";
import {} from "@/views/pages/apis/ticketTemplate";

import getUserInfo from "@/utils/getUserInfo";
import { Plus } from "@element-plus/icons-vue";
import { useRoute, useRouter } from "vue-router";
import { StateOption, dictStateOption, changeType, publishType, getticketConfigurations, setticketConfigurations, getticketfindTenant, delticketConfigurations, delticketfindUserGroup } from "@/views/pages/apis/ticketTemplate";
import { 服务管理中心_工单模版_编辑, 服务管理中心_工单模版_分配用户组 } from "@/views/pages/permission";

interface Emits {
  (event: "refresh"): void;
}
const emits = defineEmits<Emits>();

const form = ref({
  workorderStatus: "", // 工单状态
  isApproval: false,
  type: "",
});

defineOptions({ name: "workOrderConfig" });
const createApprovalRef = ref<InstanceType<typeof createApproval>>();
const configloading = ref<boolean>(false);

const userGroupslist = ref<Record<string, any>>([]);
const formlist = ref<Record<string, any>>({
  userGroupsId: [] as string[],
  userGroupsName: [] as string[],
});
const userInfo: any = getUserInfo();

interface Props {
  workOrderType: "event" | "server" | "change" | "publish" | "question" | "dictevent" | "dictservice" | string;
  parentId: string;
  ticketTemplatesName: string;
  ticketGroupId: string;
  isEdit: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  workOrderType: "",
  parentId: "",
  ticketTemplatesName: "",
  ticketGroupId: "",
  type: "",
  isEdit: false,
});

const configLoading = ref<boolean>(false);

const templateTitle = computed(() => {
  const titleMapping: Record<string, string> = {
    event: "事件工单配置",
    service: "服务请求工单配置",
    question: "问题工单配置",
    publish: "发布工单配置",
    change: "变更工单配置",
    dictevent: "DICT事件管理工单配置",
    dictservice: "DICT服务请求工单配置",
  };

  return titleMapping[props.workOrderType] || "未知模板类型";
});

async function handleRefresh() {
  configloading.value = true;
  try {
    const { data = {} as any, message, success } = await getticketConfigurations({ ticketTemplatesId: props.parentId });
    if (!success) throw new Error(message);
    const { type } = data as any;
    formlist.value = data;
    const userGroupsId = data.userGroupsId ?? []; // 如果为 null，则赋值为空数组
    const userGroupsName = data.userGroupsName ?? [];
    form.value.workorderStatus = type;
    if (props.workOrderType === "change" || props.workOrderType === "publish") {
      form.value.isApproval = data.approval;
      userGroupslist.value = userGroupsId.map((id, index) => ({
        id: id,
        name: userGroupsName[index],
      }));
    }
    configloading.value = false;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
    configloading.value = false;
  }
}
async function openApproval(row) {
  if (!createApprovalRef.value) return false;
  await createApprovalRef.value.open({}, async (form) => {
    await handleAddItem(form);
  });
}
async function handleAddItem(item) {
  if (props.isEdit) return ElMessage.warning("请先保存正在编辑的信息");
  const mergedData: Record<string, any> = {};

  // 处理 tenantId
  const tenantId = item?.tenantId ?? formlist.value?.tenantId;
  if (tenantId != null) {
    // 检查是否为 null/undefined
    mergedData.tenantId = tenantId;
  }

  // 处理 tenantName
  const tenantName = item?.tenantName ?? formlist.value?.tenantName;
  if (tenantName != null) {
    mergedData.tenantName = tenantName;
  }
  mergedData.userGroupsId = item?.teamId ?? formlist.value?.userGroupsId ?? [];
  mergedData.userGroupsName = item?.teamName ?? formlist.value?.userGroupsName ?? [];

  // 提交后清空 formlist 中的 userGroupsId 和 userGroupsName
  const resetFormlist = () => {
    if (formlist.value) {
      formlist.value.userGroupsId = [];
      formlist.value.userGroupsName = [];
    }
    if (item) {
      item.teamId = [];
      item.teamName = [];
    }
  };

  configLoading.value = true;
  const { parentId, ticketTemplatesName } = props;
  const params = {
    ticketTemplatesId: parentId,
    ticketTemplatesName,
    type: form.value.workorderStatus,
    approval: form.value.isApproval || false,
    ticketGroupId: props.ticketGroupId || null,
    ticketType: props.workOrderType,
    ...mergedData,
  };
  try {
    const { data, message, success } = await setticketConfigurations(params);
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    handleRefresh();
    emits("refresh");
    resetFormlist();
    configLoading.value = false;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
    configLoading.value = false;
  }
}

async function delRefresh(row: any) {
  const params = {
    id: formlist.value?.id,
    userGroupsId: row.id,
    userGroupsName: row.name,
  };
  try {
    const { success, message } = await delticketfindUserGroup(params);
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    handleRefresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}
// async function handleRefreshfTenant() {
//   try {
//     const { data, message, success } = await getticketfindTenant({ ticketGroupId: props.ticketGroupId });
//     if (!success) throw new Error(message);
//     form.value.workorderStatus = data.type;
//   } catch (error) {
//     error instanceof Error && ElMessage.error(error.message);
//   }
// }

onMounted(() => {
  handleRefresh();
  // handleRefreshfTenant();
});

defineExpose({
  form,
});
</script>
<style scoped>
/* 文字居左 */
.checkbox-label {
  order: -1; /* 将文字移动到复选框前面 */
  margin-right: 50px;
}
</style>
