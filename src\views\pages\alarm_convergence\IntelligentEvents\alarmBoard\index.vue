<template>
  <el-scrollbar :height="height">
    <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
      <pageTemplate v-model:currentPage="state.page" v-model:pageSize="state.size" :total="state.total" :height="height - 40" :show-paging="true" @size-change="handleCommand(command.Request)" @current-change="handleCommand(command.Request)">
        <!-- <template #left>
          <el-input v-model="state.search.alert" :disabled="state.loading" :placeholder="$t('glob.Please input field', { field: $t('glob.Keyword') })" @keyup.enter="handleQuery()">
            <template #append>
              <el-button :icon="Search" :disabled="state.loading" @click.stop="handleQuery()" />
            </template>
          </el-input>
        </template> -->
        <template #right>
          <span class="">
            <el-button v-if="userInfo.hasPermission(资产管理中心_设备_确认告警)" type="primary" v-preventReClick @click="handleCommand(command.Create, { ids: state.select })" :disabled="!state.select.length">{{ t("alarmBoard.ACK") }}</el-button>
            <!-- v-if="userInfo.hasPermission(资产管理中心_设备_确认告警)" -->
          </span>
          <span class="tw-ml-[16px]">
            <el-button :icon="Tools" @click="handleSetCustomConfig" :title="t('alarmBoard.Dashboard Settings')"></el-button>
          </span>
          <span class="tw-ml-[16px]">
            <el-button :icon="Grid" @click="() => customFieldsRef && customFieldsRef.open()" :title="'自定义展示列'"></el-button>
          </span>
          <span class="tw-ml-[16px]">
            <el-button v-if="userInfo.hasPermission(监控管理中心_告警板_可读) || state.loading" type="default" :icon="Refresh" v-preventReClick @click="handleCommand(command.Refresh)" :title="$t('glob.refresh')"></el-button>
          </span>
        </template>
        <template #default="{ height: tableHeight }">
          <el-table v-loading="state.loading" ref="tableRef" :data="dataList" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).filter((v) => !v.alarmBoardConfirmed).map((v) => v.id))" @expand-change="handleExpand">
            <TableColumn type="selection" :width="55" :selectable="(row: DataItem) => !row.alarmBoardConfirmed && row.confirm && row.eventSeverity != 'Normal'"></TableColumn>
            <TableColumn v-if="state.customFields.includes('eventSeverity')" type="enum" filter-multiple show-filter v-model:filtered-value="state.search.eventSeverity" @filter-change="handleResetPageQuery()" prop="eventSeverity" :label="t('alarmBoard.Severity')" :minWidth="130" :filters="eventSeverityOption.map((v) => ({ ...v, text: v.label }))"></TableColumn>
            <TableColumn v-if="state.customFields.includes('tenantName')" type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByTenantName" @filter-change="handleResetPageQuery()" prop="tenantName" :label="t('alarmBoard.Customer')" :minWidth="130" :filters="$filter0">
              <template #default="{ row }">
                <div>{{ `${row.tenantName}${row.tenantAbbreviation ? "(" + row.tenantAbbreviation + ")" : ""}` }}</div>
              </template>
            </TableColumn>
            <TableColumn v-if="state.customFields.includes('deviceName')" type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByDeviceOrIp" @filter-change="handleResetPageQuery()" prop="deviceId" :label="t('alarmBoard.Device')" :minWidth="180" :filters="$filter2">
              <template #default="{ row }">
                <!-- <pre>{{ row }}</pre> -->
                <!-- <router-link v-if="row.id" :to="{ name: '509596457372745728', params: { id: row.deviceId }, query: { fallback: route.name as string, tenant: row.tenantId as string } }" target="_blank" tag="a"> -->
                <!-- <template #default="{ href }"> -->
                <p v-preventReClick @click="routerDevice(row)" v-if="row.deviceId" style="color: #409eff; cursor: pointer">
                  {{ row.deviceName || "--" }}
                </p>
                <!-- {{ row || "--" }} -->
                <p v-preventReClick @click="routerDevice(row)" v-if="row.deviceId" style="color: #409eff; cursor: pointer">
                  {{ row.deviceIp || "--" }}
                </p>
                <!-- </template> -->
                <!-- </router-link> -->
                <div style="cursor: pointer; display: flex">
                  <template v-if="userInfo.hasPermission(资产管理中心_设备_工具权限)">
                    <span class="tw-h-fit">
                      <el-button type="primary" link>
                        <deviceTools :item="row" :list="deskTopObj.length" :show="row.showDesktop" :active="row.active"></deviceTools>
                      </el-button>
                    </span>
                    <span class="tw-h-fit">
                      <el-button type="primary" link @click="ping(row)">
                        <Icon class="tw-mx-[2px]" name="local-DeviceWifi-line" :color="row.active ? 'var(--el-color-primary)' : '#888'"></Icon>
                      </el-button>
                    </span>
                    <span class="tw-h-fit">
                      <el-button type="primary" link @click="routeDevice(row)">
                        <Icon class="tw-mx-[2px]" name="local-SystemShare-line" :color="row.active ? 'var(--el-color-primary)' : '#888'"></Icon>
                      </el-button>
                    </span>
                  </template>
                  <span class="tw-h-fit">
                    <el-button type="primary" link v-preventReClick @click="routerV6Busines(row.deviceName, row.tenantId)">
                      <Icon class="tw-mx-[2px]" name="local-DocumentNumbers-line" color="var(--el-color-primary)"></Icon>
                    </el-button>
                  </span>
                  <span class="tw-h-fit">
                    <el-button type="primary" link v-preventReClick @click="preview(row.deviceId)">
                      <Icon class="tw-mx-[2px]" name="local-SystemError-warning-line" color="var(--el-color-primary)"></Icon>
                    </el-button>
                  </span>

                  <span class="tw-h-fit">
                    <el-button type="primary" link v-preventReClick @click="contancts(row.deviceId)">
                      <Icon :disabled="true" class="tw-mx-[2px]" name="local-UserUser-3-line" color="var(--el-color-primary)"></Icon>
                    </el-button>
                  </span>
                </div>
              </template>
            </TableColumn>
            <TableColumn v-if="state.customFields.includes('desc')" type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByAlert" @filter-change="handleResetPageQuery()" prop="title" :label="t('alarmBoard.Alert')" :filters="$filter2" :minWidth="320">
              <template #default="{ row }">
                <el-link type="default" :underline="false" @click.stop>{{ row.title }}</el-link>
                <div>{{ row.desc }}</div>
              </template>
            </TableColumn>

            <TableColumn v-if="state.customFields.includes('order')" type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderId" @filter-change="handleResetPageQuery()" prop="order" :label="t('alarmBoard.Ticket')" sortable="custom" :minWidth="180" :filters="$filter2">
              <template #default="{ row }">
                <template v-if="userInfo.hasPermission(智能事件中心_客户_工单可读) || (userInfo.hasPermission('756062918394511360' as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))">
                  <div style="display: flex; flex-direction: column">
                    <div v-if="!(row as DataItem).orders.length">--</div>
                    <div v-for="order in (row as DataItem).orders" :key="order.orderId">
                      <el-tag class="orderPriority" :color="priorityColor[order.priority].color" style="color: #fff; border: 0">{{ order.priority }}</el-tag>
                      <span v-for="val in orderState" :key="val.value">
                        <el-tag v-show="val.value === order.state">{{ val.label }}</el-tag>
                      </span>
                      <!-- <router-link custom :to="{ name: routerPath[order.orderType], params: { id: order.orderId }, query: { fallback: route.name as string } }">
                      <template #default="{ href }"> -->
                      <el-button type="primary" link class="clipboard tw-ml-[6px]" @click="handleToOrder(order.orderType, order.orderId, order.tenantId)">{{ order.orderId }}</el-button>
                      <!-- </template>
                    </router-link> -->
                    </div>
                  </div>
                </template>
                <template v-else>--</template>
              </template>
            </TableColumn>

            <TableColumn v-if="state.customFields.includes('alertCreateTime')" type="date" show-filter v-model:filtered-value="state.search.alertCreateTimeRange" filter-multiple @filter-change="handleResetPageQuery()" prop="alertCreateTime" :label="t('alarmBoard.Timestamp')" sortable="custom" :minWidth="160"></TableColumn>
            <TableColumn v-if="state.customFields.includes('eventConfirmedTime')" type="date" show-filter v-model:filtered-value="state.search.alarmBoardConfirmedTime" filter-multiple @filter-change="handleResetPageQuery()" prop="alarmBoardConfirmedTime" :label="t('alarmBoard.Acked Timestamp')" sortable="custom" :minWidth="160"></TableColumn>
            <TableColumn v-if="state.customFields.includes('eventConfirmedPerson')" type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByResponseUser" @filter-change="handleResetPageQuery()" :label="t('alarmBoard.Acked By')" :minWidth="100" :filters="$filter2">
              <template #default="{ row }">
                <div v-if="userInfo.hasPermission(安全管理中心_用户管理_可读)">{{ row.alarmBoardConfirmedPerson.account }}</div>
              </template>
            </TableColumn>
          </el-table>
        </template>
      </pageTemplate>
    </el-card>
    <Editor ref="editorRef" :title="t('alarmBoard.ACK')" display="dialog"></Editor>

    <deviceDetials ref="deviceDetialsRef"></deviceDetials>
    <deviceContacts ref="deviceContactsRef"></deviceContacts>
    <devicePing ref="devicePingRef"></devicePing>
    <deviceRoute ref="deviceRouteRef"></deviceRoute>

    <setCustomConfig ref="setCustomConfigRef" :title="t('alarmBoard.Dashboard Settings')" display="dialog"></setCustomConfig>

    <CustomFields ref="customFieldsRef" :customFieldsType="'alarm'" :submitApi="setCustomFields" :selectedCustomFields="state.customFields" />
  </el-scrollbar>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch, computed, readonly, reactive, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Refresh, Tools, Grid } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";
import { ElTable } from "element-plus";
import Editor from "./Editor.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox } from "element-plus";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { filter, find } from "lodash-es";
import moment from "moment";

// import { type BaseItem, DataItem, type Item } from "./helper";
// import { state, dataList, routerPath } from "./helper";
// import { resetData, handleExpand, handleSort, command } from "./helper";
import getUserInfo from "@/utils/getUserInfo";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
import { eventSeverity, eventSeverityOption } from "@/views/pages/apis/event";
import { type AlertItem as $DataItem, getAlertBoardList as getItemList, hasAlertOrderByOrderPermission } from "@/views/pages/apis/event";
import { addAlertData as addItemData, setAlertData as setItemData, modAlertData as modItemData, delAlertData as delItemData } from "@/views/pages/apis/event";

import { getUserList } from "@/views/pages/apis/model";
import { setAlertStat, getAlertBoardConfig, setAlertBoardConfig } from "@/views/pages/apis/event";
import { getResourcesList } from "@/views/pages/apis/cmdb";

import { useSiteConfig } from "@/stores/siteConfig";

import priorityColor from "@/views/pages/common/priority";
import { routerV6, routerV6Busines } from "@/views/pages/common/routeV6";
import { orderType, orderState } from "@/views/pages/apis/association";

import deviceDetials from "@/components/deviceTool/deviceDetials.vue";
import deviceContacts from "@/components/deviceTool/deviceContacts.vue";
import devicePing from "@/components/deviceTool/ping.vue";
import deviceRoute from "@/components/deviceTool/tracerRoute.vue";
import deviceTools from "@/components/deviceTool/index.vue";
import setCustomConfig from "./setCustomConfig.vue";
import { desktop, desktopbatch, getDeviceDiscovery } from "@/views/pages/apis/device";
import _timeZoneHours from "@/views/pages/common/offsetHours.json";

import handleToOrder from "@/views/pages/alarm_convergence/IntelligentEvents/eventBoard/toOrder";

import CustomFields from "@/views/pages/alarm_convergence/IntelligentEvents/eventBoard/customFields.vue";

const timeZoneHours = ref(_timeZoneHours);
import {
  /*  */
  监控管理中心_告警板_可读,
  资产管理中心_设备_工具权限,
  监控管理中心_告警板_确认告警,
  监控管理中心_告警板_安全,
  监控管理中心_告警板_所有权限,
  监控管理中心_设备_可读,
  安全管理中心_用户管理_可读,
  资产管理中心_设备_确认告警,
  系统管理中心_客户管理_可读,
  智能事件中心_客户_工单可读,
  智能事件中心_设备_工单可读,
} from "@/views/pages/permission";

import { getCustomOrderFields, setCustomOrderFields } from "@/views/pages/apis/eventBoard";
import type { ICustomFindOrderFieldsResult } from "@/views/pages/apis/eventBoard";

import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";

type DataItem = Omit<$DataItem, "alarmBoardConfirmedPerson" | "eventConfirmedPerson"> & Record<"alarmBoardConfirmedPerson" | "eventConfirmedPerson", { userid: string; username: string }>;
/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const { t } = useI18n({ useScope: "global" });
const route = useRoute();
const router = useRouter();
const userInfo = getUserInfo();
const siteConfig = useSiteConfig();
defineOptions({ name: "alarmBoard" });
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);
const $filter2 = ref(exoprtMatch1);
interface Props {
  width: number;
  height: number;
  title?: string;
}
const props = withDefaults(defineProps<Props>(), { title: "告警" });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

const _width = inject("width", ref(0));
const _height = inject("height", ref(0));

const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);

const tableRef = ref<InstanceType<typeof ElTable>>();
const editorRef = ref<InstanceType<typeof Editor>>();

const deviceDetialsRef = ref<InstanceType<typeof deviceDetials>>();
const deviceContactsRef = ref<InstanceType<typeof deviceContacts>>();
const deviceRouteRef = ref<InstanceType<typeof deviceRoute>>();
const devicePingRef = ref<InstanceType<typeof devicePing> & any>();
const setCustomConfigRef = ref<InstanceType<typeof setCustomConfig>>();
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

interface State<T, P> {
  loading: boolean;
  expand: string[];
  select: string[];
  current: string | undefined;
  search: P & any;
  sort?: { prop: string; order: "ascending" | "descending" };
  list: T[];
  page: number;
  size: number;
  total: number;
  customFields: ICustomFindOrderFieldsResult["fields"];
}
enum command {
  Refresh = "Refresh",
  Request = "Request",
  Preview = "Preview",
  Create = "Create",
  Update = "Update",
  Modify = "Modify",
  Delete = "Delete",
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const final = readonly({
  pagination: false,
});

const routerPath = {
  EVENT_ORDER: "510685950393712640",
  SERVICE_REQUEST: "514703398516293632",
  CHANGE: "515123784953364480",
  QUESTION: "515035822471249920",
  PUBLISH: "519831507997556736",
};

type DataSrch = Omit<typeof getItemList extends (req: infer P) => any ? P : never, "paging" | "sort">;
const state = reactive<State<DataItem, DataSrch>>({
  loading: false,
  expand: [],
  select: [],
  current: undefined,
  search: {
    tenantId: "",
    loginTime: "",

    // alertCreateTimeRange: { start: "", end: "" },
    // alarmBoardConfirmedTime: { start: "", end: "" },

    includeTenantName: [],
    excludeTenantName: [],
    eqTenantName: [],
    neTenantName: [],
    tenantNameFilterRelation: "AND",
    includeOrderId: [],
    excludeOrderId: [],
    includeAlert: [],
    excludeAlert: [],
    includeDeviceOrIp: [],
    excludeDeviceOrIp: [],
    includeResponseUser: [],
    excludeResponseUser: [],
    orderIdFilterRelation: "AND",
    alertFilterRelation: "AND",
    deviceOrIpFilterRelation: "AND",
    responseUserFilterRelation: "AND",
  },
  sort: undefined,
  list: [],
  page: 1,
  size: 50,
  total: 0,

  customFields: [],
});
const dataList = computed(() => (final.pagination ? state.list.slice((state.page - 1) * state.size, state.page * state.size) : state.list));
const expand = computed(() => filter<DataItem[]>(state.list, (row: DataItem) => state.expand.includes(row.id)));
const select = computed(() => filter<DataItem[]>(state.list, (row: DataItem) => state.select.includes(row.id)));
const current = computed(() => find<DataItem[]>(state.list, (row: DataItem) => row.id === state.current));
// const name = computed(() => state.name);

const searchType0ByTenantName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByTenantName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByTenantName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByTenantName) === "include") value0 = state.search.includeTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "exclude") value0 = state.search.excludeTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "eq") value0 = state.search.eqTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "ne") value0 = state.search.neTenantName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByTenantName) === "include") value1 = state.search.includeTenantName[state.search.includeTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "exclude") value1 = state.search.excludeTenantName[state.search.excludeTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "eq") value1 = state.search.eqTenantName[state.search.eqTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "ne") value1 = state.search.neTenantName[state.search.neTenantName.length - 1] || "";
    return {
      type0: toValue(searchType0ByTenantName),
      type1: toValue(searchType1ByTenantName),
      relation: state.search.tenantNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByTenantName.value = v.type0 as typeof searchType0ByTenantName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByTenantName.value = v.type1 as typeof searchType1ByTenantName extends import("vue").Ref<infer T> ? T : string;
    state.search.tenantNameFilterRelation = v.relation;
    state.search.includeTenantName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeTenantName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqTenantName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neTenantName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByOrderId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByOrderId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByOrderId = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByOrderId) === "include") value0 = state.search.includeOrderId[0] || "";
    if (toValue(searchType0ByOrderId) === "exclude") value0 = state.search.excludeOrderId[0] || "";
    let value1 = "";
    if (toValue(searchType1ByOrderId) === "include") value1 = state.search.includeOrderId[state.search.includeOrderId.length - 1] || "";
    if (toValue(searchType1ByOrderId) === "exclude") value1 = state.search.excludeOrderId[state.search.excludeOrderId.length - 1] || "";
    return {
      type0: toValue(searchType0ByOrderId),
      type1: toValue(searchType1ByOrderId),
      relation: state.search.orderIdFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByOrderId.value = v.type0 as typeof searchType0ByOrderId extends import("vue").Ref<infer T> ? T : string;
    searchType1ByOrderId.value = v.type1 as typeof searchType1ByOrderId extends import("vue").Ref<infer T> ? T : string;
    state.search.orderIdFilterRelation = v.relation;
    state.search.includeOrderId = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeOrderId = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
  },
});
const searchType0ByAlert = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByAlert = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByAlert = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByAlert) === "include") value0 = state.search.includeAlert[0] || "";
    if (toValue(searchType0ByAlert) === "exclude") value0 = state.search.excludeAlert[0] || "";
    let value1 = "";
    if (toValue(searchType1ByAlert) === "include") value1 = state.search.includeAlert[state.search.includeAlert.length - 1] || "";
    if (toValue(searchType1ByAlert) === "exclude") value1 = state.search.excludeAlert[state.search.excludeAlert.length - 1] || "";
    return {
      type0: toValue(searchType0ByAlert),
      type1: toValue(searchType1ByAlert),
      relation: state.search.alertFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByAlert.value = v.type0 as typeof searchType0ByAlert extends import("vue").Ref<infer T> ? T : string;
    searchType1ByAlert.value = v.type1 as typeof searchType1ByAlert extends import("vue").Ref<infer T> ? T : string;
    state.search.alertFilterRelation = v.relation;
    state.search.includeAlert = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeAlert = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
  },
});
const searchType0ByDeviceOrIp = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByDeviceOrIp = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByDeviceOrIp = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByDeviceOrIp) === "include") value0 = state.search.includeDeviceOrIp[0] || "";
    if (toValue(searchType0ByDeviceOrIp) === "exclude") value0 = state.search.excludeDeviceOrIp[0] || "";
    let value1 = "";
    if (toValue(searchType1ByDeviceOrIp) === "include") value1 = state.search.includeDeviceOrIp[state.search.includeDeviceOrIp.length - 1] || "";
    if (toValue(searchType1ByDeviceOrIp) === "exclude") value1 = state.search.excludeDeviceOrIp[state.search.excludeDeviceOrIp.length - 1] || "";
    return {
      type0: toValue(searchType0ByDeviceOrIp),
      type1: toValue(searchType1ByDeviceOrIp),
      relation: state.search.deviceOrIpFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByDeviceOrIp.value = v.type0 as typeof searchType0ByDeviceOrIp extends import("vue").Ref<infer T> ? T : string;
    searchType1ByDeviceOrIp.value = v.type1 as typeof searchType1ByDeviceOrIp extends import("vue").Ref<infer T> ? T : string;
    state.search.deviceOrIpFilterRelation = v.relation;
    state.search.includeDeviceOrIp = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeDeviceOrIp = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
  },
});
const searchType0ByResponseUser = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByResponseUser = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByResponseUser = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByResponseUser) === "include") value0 = state.search.includeResponseUser[0] || "";
    if (toValue(searchType0ByResponseUser) === "exclude") value0 = state.search.excludeResponseUser[0] || "";
    let value1 = "";
    if (toValue(searchType1ByResponseUser) === "include") value1 = state.search.includeResponseUser[state.search.includeResponseUser.length - 1] || "";
    if (toValue(searchType1ByResponseUser) === "exclude") value1 = state.search.excludeResponseUser[state.search.excludeResponseUser.length - 1] || "";
    return {
      type0: toValue(searchType0ByResponseUser),
      type1: toValue(searchType1ByResponseUser),
      relation: state.search.responseUserFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByResponseUser.value = v.type0 as typeof searchType0ByResponseUser extends import("vue").Ref<infer T> ? T : string;
    searchType1ByResponseUser.value = v.type1 as typeof searchType1ByResponseUser extends import("vue").Ref<infer T> ? T : string;
    state.search.responseUserFilterRelation = v.relation;
    state.search.includeResponseUser = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeResponseUser = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
  },
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

// 设置告警板自定义配置
async function handleSetCustomConfig() {
  if (!setCustomConfigRef.value) return false;
  try {
    const { data: alertBoardConfigData, success: alertBoardConfigSuccess, message: alertBoardConfigMessage } = await getAlertBoardConfig({});
    if (!alertBoardConfigSuccess) throw new Error(alertBoardConfigMessage);
    await setCustomConfigRef.value.open(Object.keys(alertBoardConfigData || {}).length ? { queryHours: (alertBoardConfigData || {}).queryHours || "", eventSeveritys: (alertBoardConfigData || {}).eventSeveritys || [] } : {}, async (form) => {
      const params = {
        ...form,
        tenantId: userInfo.tenantId,
        userId: userInfo.userId,
      };
      const { data, message, success } = await setAlertBoardConfig({ ...params });
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
      queryData();
    });
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

function handleExpand(row: DataItem, expandedRows: DataItem[]) {
  state.expand = expandedRows.filter((v) => !v.alarmBoardConfirmed).map(({ id }) => id);
  if (find(expandedRows, ({ id }) => row.id === id)) {
    /*  */
  } else {
    /*  */
  }
}
function handleSort(sort: { prop: string; order: "ascending" | "descending" }) {
  state.sort = sort.prop ? sort : undefined;
}
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
async function resetData() {
  state.list = [];
  state.page = 1;
  state.size = 50;
  state.total = 0;
  // for (const key in state.search) {
  //   if (Object.prototype.hasOwnProperty.call(state.search, key)) {
  //     delete state.search[key];
  //   }
  // }
  await nextTick();
}
/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */

/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const timer = ref<null | number>(null);
const autoRefreshTime = ref(0);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}

const tenantNameObj = ref({
  status: "include",
  type: "include",
  relation: "AND",
  firstName: "",
  lastName: "",
});

const orderObj = ref({
  status: "include",
  type: "include",
  relation: "AND",
  firstName: "",
  lastName: "",
});

const alarmObj = ref({
  status: "include",
  type: "include",
  relation: "AND",
  firstName: "",
  lastName: "",
});
function beforeMount() {}
function mounted() {
  handleRefresh().then(() => (autoRefreshTime.value = 60));
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {
  if (timer.value !== null) {
    window.clearInterval(timer.value);
    timer.value = null;
  }
}
function beforeDestroy() {
  if (timer.value !== null) {
    window.clearInterval(timer.value);
    timer.value = null;
  }
}
function destroyed() {}

function routeDevice(props) {
  // // console.log(props,777)
  let obj = {
    id: props.deviceId,
    config: {
      ipAddress: props.deviceIp,
    },
    name: props.deviceName,
  };
  deviceRouteRef.value.open(obj);
}

function routerDevice(v) {
  const fallback: string = route.name as string;
  userInfo.cutTenant(v.tenantId).finally(async () => {
    // try {
    //   // await router.replace({
    //   //   name: `${siteConfig.current}Loading`,
    //   //   query: { ...route.query },
    //   //   params: {
    //   //     path: route.path.split("/").filter((v) => `/${v}` !== siteConfig.baseInfo?.path),
    //   //   },
    //   // });
    // } catch (error) {
    //   /* */
    // } finally {
    const routeData = router.resolve({
      name: "509596457372745728",
      params: { id: v.deviceId },
      query: {
        fallback,
      },
    });
    const link = document.createElement("a");
    link.href = routeData.href;
    link.target = "_blank"; // 强制新窗口
    link.rel = "noopener noreferrer"; // 安全防护
    link.style.opacity = "0";
    link.style.position = "absolute";
    link.style.pointerEvents = "none";
    document.body.appendChild(link);
    link.click();
    setTimeout(() => {
      document.body.removeChild(link);
    }, 100);
    // }
  });

  // const previousWindow = this.$store.state.previousWindow
}
function timeZoneSwitching(): number {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == userInfo.zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
// function handleToOrder(routerName, routerParams, tenantId) {
// const fallback: string = route.name as string;
// userInfo.cutTenant(tenantId).finally(async () => {
//   try {
//     await router.replace({
//       name: `${siteConfig.current}Loading`,
//       query: { ...route.query },
//       params: {
//         path: route.path.split("/").filter((v) => `/${v}` !== siteConfig.baseInfo?.path),
//       },
//     });
//   } catch (error) {
//     /* */
//   } finally {
//     const routeData = router.resolve({
//       path: {
//         [routerPath.EVENT_ORDER]: `/event/intelligent_events/details/${routerParams.id}`,
//         [routerPath.SERVICE_REQUEST]: `/event/intelligent_request/details/${routerParams.id}`,
//         [routerPath.CHANGE]: `/event/intelligent_change/details/${routerParams.id}`,
//         [routerPath.PUBLISH]: `/event/intelligent_publish/details/${routerParams.id}`,
//         [routerPath.QUESTION]: `/event/intelligent_question/details/${routerParams.id}`,
//         // [routerPath.DICT_EVENT_ORDER]: `/event/intelligent_events/dict_event/details/${routerParams.id}`,
//         // [routerPath.DICT_SERVICE_REQUEST]: `/event/intelligent_events/dict_serviceRequest/details/${routerParams.id}`,
//       }[routerName],
//       // params: routerParams,
//       query: {
//         fallback,
//       },
//     });
//     window.open(routeData.href);
//   }
// });
// }

function routerOrder(item) {
  // <!-- <router-link :to="{ name: routerPath[item.orderType], params: { id: item?.orderId }, query: { fallback: route.name as string, tenant: item.tenantId as string } }" target="_blank" tag="a"> -->
  const routeData = router.resolve({
    name: routerPath[item.orderType],
    params: { id: item.orderId },
    query: {
      fallback: route.name as string,
      tenant: item.tenantId,
    },
  });
  window.open(routeData.href, item.orderId);
}
async function ping(props: any) {
  // // console.log(props,777)
  let obj = {
    id: props.deviceId,
    config: { ipAddress: props.deviceIp },
    name: props.deviceName,
  };
  await devicePingRef.value.open(obj);
  // pingTo({ id: "523721061548687360" }).then((res) => {
  //   // console.log(res);
  // });
  // deviceId.value = row.id;
  // deviceContactsRef.value.dialogFormVisible = true;
}

function contancts(id) {
  // deviceId.value = id;
  deviceContactsRef.value.dialogFormVisible = true;
  // deviceContactsRef.value.id = id;
  deviceContactsRef.value.open(id);
}
function preview(id) {
  // deviceId.value = id;
  deviceDetialsRef.value.open(id);
  deviceDetialsRef.value.dialogFormVisible = true;
}

watch(autoRefreshTime, (autoRefreshTime) => {
  if (timer.value !== null) timer.value = (window.clearInterval(timer.value), null);
  if (autoRefreshTime) timer.value = window.setInterval(queryData, autoRefreshTime * 1000);
});
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

const tenants = userInfo.tenants;
async function handleCommand(type: command, data?: Record<string, unknown>) {
  const time = autoRefreshTime.value;
  autoRefreshTime.value = 0;
  try {
    state.loading = true;
    await nextTick();

    switch (type) {
      case command.Refresh:
        await resetData();
        await queryData();
        break;
      case command.Request:
        await queryData();
        break;
      case command.Preview:
        await previewItem(data as Record<string, unknown>);
        break;
      case command.Create:
        await createItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
      case command.Update:
        await rewriteItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
      case command.Modify:
        await modifyItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
      case command.Delete:
        await deleteItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
    }
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await resetData();
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
      await queryData();
    }
  } finally {
    autoRefreshTime.value = time;
    state.loading = false;
  }
}
async function handleRefresh() {
  try {
    state.loading = true;
    await nextTick();
    await resetData();

    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}

async function handleResetPageQuery() {
  // 查询重置分页
  try {
    state.loading = true;
    state.page = 1;
    await nextTick();
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}

async function handleQuery() {
  try {
    state.loading = true;
    // let includeTenantIds: any = [];
    // let excludeTenantIds: any = [];
    // let includeOrderIds: any = [];
    // let excludeOrderIds: any = [];
    // let includeAlerts: any = [];
    // let excludeAlerts: any = [];
    // let eqTenantName: any = [];
    // let neTenantName: any = [];

    // if (tenantNameObj.value) {
    //   state.search.tenantIdFilterRelation = tenantNameObj.value.relation;

    //   // includeTenantIds = [];
    //   // excludeTenantIds = [];
    //   // console.log(tenantNameObj.value.firstName, tenantNameObj.value.lastName);
    //   switch (tenantNameObj.value.status) {
    //     case "include":
    //       if (tenantNameObj.value.firstName != "") {
    //         includeTenantIds.push(tenantNameObj.value.firstName);
    //       } else {
    //         includeTenantIds.push(null);
    //       }

    //       break;
    //     case "exclude":
    //       if (tenantNameObj.value.firstName != "") {
    //         excludeTenantIds.push(tenantNameObj.value.firstName);
    //       } else {
    //         excludeTenantIds.push(null);
    //       }
    //       break;
    //     case "be":
    //       if (tenantNameObj.value.firstName != "") {
    //         eqTenantName.push(tenantNameObj.value.firstName);
    //       } else {
    //         eqTenantName.push(null);
    //       }
    //       break;
    //     case "notBe":
    //       if (tenantNameObj.value.firstName != "") {
    //         neTenantName.push(tenantNameObj.value.firstName);
    //       } else {
    //         neTenantName.push(null);
    //       }
    //       break;
    //   }

    //   switch (tenantNameObj.value.type) {
    //     // console.log(v.tenantName.includes(tenantNameObj.value.lastName))
    //     case "include":
    //       if (tenantNameObj.value.lastName != "") {
    //         includeTenantIds.push(tenantNameObj.value.lastName);
    //       } else {
    //         includeTenantIds.push(null);
    //       }

    //       break;
    //     case "exclude":
    //       if (tenantNameObj.value.lastName != "") {
    //         excludeTenantIds.push(tenantNameObj.value.lastName);
    //       } else {
    //         excludeTenantIds.push(null);
    //       }
    //       break;
    //     case "be":
    //       if (tenantNameObj.value.lastName != "") {
    //         eqTenantName.push(tenantNameObj.value.lastName);
    //       } else {
    //         eqTenantName.push(null);
    //       }
    //       break;
    //     case "notBe":
    //       if (tenantNameObj.value.lastName != "") {
    //         neTenantName.push(tenantNameObj.value.lastName);
    //       } else {
    //         neTenantName.push(null);
    //       }
    //       break;
    //   }

    //   state.search.includeTenantIds = [...new Set(includeTenantIds)];
    //   state.search.excludeTenantIds = [...new Set(excludeTenantIds)];
    //   state.search.eqTenantName = [...new Set(eqTenantName)];
    //   state.search.neTenantName = [...new Set(neTenantName)];
    // }

    // if (orderObj.value) {
    //   state.search.orderIdFilterRelation = orderObj.value.relation;

    //   switch (orderObj.value.status) {
    //     case "include":
    //       includeOrderIds.push(orderObj.value.firstName);
    //       break;
    //     case "exclude":
    //       excludeOrderIds.push(orderObj.value.firstName);
    //       break;
    //   }
    //   switch (orderObj.value.type) {
    //     case "include":
    //       includeOrderIds.push(orderObj.value.lastName);
    //       break;
    //     case "exclude":
    //       excludeOrderIds.push(orderObj.value.lastName);
    //       break;
    //   }

    //   state.search.includeOrderIds = includeOrderIds;
    //   state.search.excludeOrderIds = excludeOrderIds;
    // }
    // if (alarmObj.value) {
    //   state.search.alertFilterRelation = alarmObj.value.relation;

    //   switch (alarmObj.value.status) {
    //     case "include":
    //       includeAlerts.push(alarmObj.value.firstName);
    //       break;
    //     case "exclude":
    //       excludeAlerts.push(alarmObj.value.firstName);
    //       break;
    //   }
    //   switch (alarmObj.value.type) {
    //     case "include":
    //       includeAlerts.push(alarmObj.value.lastName);
    //       break;
    //     case "exclude":
    //       excludeAlerts.push(alarmObj.value.lastName);
    //       break;
    //   }

    //   state.search.includeAlerts = includeAlerts;
    //   state.search.excludeAlerts = excludeAlerts;
    // }

    await nextTick();
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}

const deskTopObj = ref<ReturnType<typeof desktop> extends Promise<{ data: infer T }> ? T : never>([]);

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
async function queryData() {
  let sort: string[] = [];
  switch ((state.sort || {}).order) {
    case "ascending":
      sort.push(`${String(state.sort?.prop)},asc`);
      break;
    case "descending":
      sort.push(`${String(state.sort?.prop)},desc`);
      break;
  }

  const [{ success, message, data, page, size, total }] = await Promise.all([getItemList({ ...state.search, loginTime: userInfo.loginTime, isAlertBoard: true, sort, paging: { pageNumber: state.page, pageSize: state.size }, permissionId: 监控管理中心_设备_可读 }), getCustomFields()]);
  if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });

  const $data = (data instanceof Array ? data : []).map((v): DataItem => {
    const alarmBoardConfirmedPerson = { userid: "", username: "--" };
    const eventConfirmedPerson = { userid: "", username: "--" };
    try {
      const $alarmBoardConfirmedPerson = JSON.parse(v.alarmBoardConfirmedPerson || "");
      alarmBoardConfirmedPerson.userid = $alarmBoardConfirmedPerson.userId || "";
      alarmBoardConfirmedPerson.username = $alarmBoardConfirmedPerson.username || "--";
      alarmBoardConfirmedPerson.account = $alarmBoardConfirmedPerson.account || "--";
    } catch (error) {
      /*  */
    }
    try {
      const $eventConfirmedPerson = JSON.parse(v.eventConfirmedPerson || "");
      eventConfirmedPerson.userid = $eventConfirmedPerson.userId || "";
      eventConfirmedPerson.username = $eventConfirmedPerson.username || "--";
    } catch (error) {
      /*  */
    }
    return Object.assign(v, { alarmBoardConfirmedPerson, eventConfirmedPerson });
  });
  const select = new Set($data.filter((v) => state.select.includes(v.id)).map((v) => v.id));

  const [hasOrderSet /* hasUserSet */] = await Promise.all([
    (async (req) => {
      if (!req.orderIds.length) return new Set<string>();
      const { success, message, data } = await hasAlertOrderByOrderPermission({ orderIds: req.orderIds, permissionId: 智能事件中心_客户_工单可读 });
      if (!success) throw Object.assign(new Error(message), { success, data, page });
      return data.reduce((p, c) => p.add(c.orderId), new Set<string>());
    })({ orderIds: Array.from(new Set($data.reduce<string[]>((p, c) => p.concat((c.orders instanceof Array ? c.orders : []).map((v) => v.orderId)), [])).values()).filter((v) => v) }),
    // (async (req) => {
    //   if (!req.userIds.length) return new Set<string>();
    //   const { success, message, data } = await getUserList({ pageNumber: 1, pageSize: req.userIds.length, userIds: req.userIds, permissionId: 安全管理中心_用户管理_可读 });
    //   if (!success) throw Object.assign(new Error(message), { success, data, page });
    //   return data.reduce((p, c) => p.add(c.id), new Set<string>());
    // })({ userIds: Array.from(new Set($data.reduce<string[]>((p, c) => p.concat(c.alarmBoardConfirmedPerson.userid, c.eventConfirmedPerson.userid), [])).values()).filter((v) => v) }),
  ]);
  if (userInfo.hasPermission(系统管理中心_客户管理_可读) && userInfo.hasPermission(监控管理中心_设备_可读)) {
    state.list.splice(
      0,
      state.list.length,
      ...$data.map((v) => {
        return {
          ...v,
          alertCreateTime: Number(v.alertCreateTime) + timeZoneSwitching(),
          alarmBoardConfirmedTime: v.alarmBoardConfirmedTime ? Number(v.alarmBoardConfirmedTime) + timeZoneSwitching() : v.alarmBoardConfirmedTime,
          orders: (v.orders instanceof Array ? v.orders : []).filter((order) => hasOrderSet.has(order.orderId)),
          alarmBoardConfirmedPerson: {
            ...v.alarmBoardConfirmedPerson,
            username: v.alarmBoardConfirmedPerson.userid ? v.alarmBoardConfirmedPerson.username : "--",
          },
          eventConfirmedPerson: {
            ...v.eventConfirmedPerson,
            username: v.eventConfirmedPerson.userid ? v.eventConfirmedPerson.username : "--",
          },
        };
      })
    );

    // state.list.splice(0, state.list.length, ...$data.map((v) => Object.assign(v, { orders: (v.orders instanceof Array ? v.orders : []).filter((v) => hasOrderSet.has(v.orderId)), alarmBoardConfirmedPerson: { ...v.alarmBoardConfirmedPerson, ...(v.alarmBoardConfirmedPerson.userid ? { username: v.alarmBoardConfirmedPerson.username } : { username: "--" }) }, eventConfirmedPerson: { ...v.eventConfirmedPerson, ...(v.eventConfirmedPerson.userid ? { username: v.eventConfirmedPerson.username } : { username: "--" }) } })));
    // if (!userInfo.hasPermission(智能事件中心_客户_工单可读)) {
    //   state.list = state.list.map((item) => {
    //     return {
    //       ...item,
    //       orders: [],
    //     };
    //   });
    // }

    state.page = Number(page) || 1;
    state.size = Number(size) || 20;
    state.total = Number(total) || 0;
  } else {
    state.list = [];
  }

  deskTopObj.value = [];
  if (state.list.length) {
    const { success: desktopSuccess, message: desktopMessage, data: desktopData } = await desktopbatch({ deviceIds: state.list.map((v) => v.deviceId).join(), ids: [] });
    if (!desktopSuccess) throw Object.assign(new Error(desktopMessage), { success: desktopSuccess, data: desktopData });
    deskTopObj.value = desktopData instanceof Array ? desktopData : [];
    for (let i = 0; i < state.list.length; i++) {
      const $desktop = find(desktopData, (v) => state.list[i].deviceId === v.resourceId);
      Object.assign(state.list[i], { showDesktop: $desktop ? $desktop.icoShow : false, allowTypes: $desktop && $desktop.allowTypes instanceof Array ? $desktop.allowTypes : [] });
    }
  }
  // await (async (ids: string[]) => {
  //   try {
  //     if (!ids.length) return;
  //     const { success, message, data: ResourcesList } = await getResourcesList({ ids });
  //     if (!success) throw Object.assign(new Error(message), { success, data: ResourcesList });
  //     for (let i = 0; i < (data instanceof Array ? data : []).length; i++) {
  //       const device = find(ResourcesList, ({ id }) => id === (data instanceof Array ? data : [])[i].deviceId);
  //       (data instanceof Array ? data : [])[i].deviceName = device ? device.name : "";
  //       (data instanceof Array ? data : [])[i].deviceIp = device ? device.config.ipAddress : "";
  //       try {
  //         (data instanceof Array ? data : [])[i].alarmBoardConfirmedPerson = JSON.parse((data instanceof Array ? data : [])[i].alarmBoardConfirmedPerson).username;
  //       } catch (error) {
  //         /*  */
  //       }
  //     }
  //   } catch (error) {
  //     if (error instanceof Error) ElMessage.error(message);
  //   }
  // })(Array.from((data instanceof Array ? data : []).reduce((p, c) => (p.add(c.deviceId), p), new Set<string>())));

  await nextTick();
  if (tableRef.value) {
    tableRef.value.clearSelection();
    for (let i = 0; i < state.list.length; i++) {
      if (state.list[i].alarmBoardConfirmed) {
        tableRef.value.toggleRowSelection(state.list[i], true);
        select.delete(state.list[i].id);
      } else {
        tableRef.value.toggleRowSelection(state.list[i], select.has(state.list[i].id));
      }
    }
  }
  state.select = Array.from(select);
}

async function createItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await setAlertStat({ ids: form.ids as string[], ...form });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`成功确认告警`);
  });
}
async function previewItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await setAlertStat({ ids: form.ids as string[], ...form });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${t("axios.Operation successful")}`);
  });
}
async function rewriteItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await setAlertStat({ ids: form.ids as string[], ...form });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${t("axios.Operation successful")}`);
  });
}
async function modifyItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await setAlertStat({ ids: form.ids as string[], ...form });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${t("axios.Operation successful")}`);
  });
}
async function deleteItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await delItemData(form);
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${t("axios.Operation successful")}`);
  });
}

const customFieldsRef = ref();

async function getCustomFields() {
  try {
    const { success, data, message } = await getCustomOrderFields({ userId: userInfo.userId, type: "alarm" });
    if (!success) throw new Error(message);
    state.customFields = data.fields;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

function setCustomFields(fields: string) {
  return new Promise((resolve, reject) => {
    setCustomOrderFields({ fields, userId: userInfo.userId, type: "alarm" })
      .then(({ data, message, success }) => {
        if (!success) throw new Error(message);
        ElMessage.success(t("axios.Operation successful"));
        getCustomFields();
        resolve(void 0);
      })
      .catch((error) => {
        reject(error);
      });
  });
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: DataItem): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.orderPriority {
  // display: flex;
  border-radius: 20px;
  width: 40px !important;
}
</style>
