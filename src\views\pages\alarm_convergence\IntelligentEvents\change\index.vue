<template>
  <el-scrollbar :height="height">
    <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
      <pageTemplate v-model:currentPage="state.page" v-model:pageSize="state.size" :total="state.total" :height="height - 40" :show-paging="true" @size-change="handleCommand(command.Request)" @current-change="handleCommand(command.Request)">
        <template #left>
          <el-radio-group v-model="state.search.changeState" :style="{ whiteSpace: 'nowrap', flexWrap: 'nowrap' }" @change="handleQuery()">
            <el-radio-button label="">
              <el-badge type="primary" :value="changeStat.reduce((p, c) => p + c.count, 0)" :hidden="!changeStat.reduce((p, c) => p + c.count, 0)">
                <div class="tw-px-[1em]">{{ $t("el.table.clearFilter") }}</div>
              </el-badge>
            </el-radio-button>
            <el-radio-button v-for="item in changeStat" :key="item.value" :label="item.value">
              <el-badge type="primary" :value="item.count" :hidden="!item.count">
                <div class="tw-px-[1em]">{{ $t(`event.${item.label}`) }}</div>
              </el-badge>
            </el-radio-button>
          </el-radio-group>
        </template>
        <template #right>
          <span class="tw-h-fit">
            <el-button v-if="userInfo.hasPermission(智能事件中心_变更工单_新增) /* && userInfo.hasPermission(服务管理中心_工单模版_可读) && userInfo.hasPermission(服务管理中心_工单组_可读) */" :loading="state.loading" type="primary" :icon="Plus" @click="handleCommand(command.Create, {})">{{ $t("generalDetails.New Change") }}</el-button>
          </span>
          <span class="tw-ml-[16px] tw-h-fit">
            <el-button :loading="state.loading" type="default" :icon="Refresh" @click="handleCommand(command.Refresh)" :title="$t('glob.refresh')"></el-button>
          </span>
        </template>
        <template #default="{ height: tableHeight }">
          <el-table v-loading="state.loading" ref="tableRef" :data="dataList" row-key="id" :height="tableHeight" @cell-contextmenu="copyClick" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).map((v) => v.id))" @expand-change="handleExpand">
            <TableColumn type="selection" :width="55"></TableColumn>

            <!--
              searchByTenantName
              searchByOrderIds

              searchByAlarmCount
              searchByActorName
              searchByResponsibleName
              searchByOrderSummary
            -->

            <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.priority" @filter-change="handleQuery()" prop="priority" :label="$t('event.Priority')" :width="120" :filters="priorityOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>
            <!-- <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByStates" @filter-change="handleQuery()" prop="changeState" label="状态" :width="120" :filters="$filter0">
              <template #default="{ row, column }">
                <el-tag :type="(find(changeStateOptionA, (v) => v.value === row[column.property]) || {}).type" size="small">{{ (find(changeStateOptionA, (v) => v.value === row[column.property]) || {}).label }}</el-tag>
              </template>
            </TableColumn> -->

            <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.state" @filter-change="handleQuery()" prop="changeState" :label="$t('event.State')" :width="120" :filters="changeStateOption.map((v) => ({ value: v.value, text: v.label }))" sortable="custom">
              <template #default="{ row, column }">
                <el-tag :type="(find(changeStateOptionA, (v) => v.value === row[column.property]) || {}).type" size="small">{{ (find(changeStateOption, (v) => v.value === row[column.property]) || {}).label }}</el-tag>
              </template>
            </TableColumn>

            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderIds" @filter-change="handleQuery()" prop="identifier" :label="$t('event.Ticket')" sortable="custom" :width="180" :filters="$filter0">
              <template #default="{ row, column }">
                <!-- {{ row.tenantId }} -->
                <el-link type="primary" :underline="false" class="clipboard tw-ml-[6px]" @click.stop="handleToOrder(row.orderType, row.id, row.tenantId)">{{ row[column.property] }}</el-link>
              </template>
            </TableColumn>
            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByAlarmCount" @filter-change="handleQuery()" prop="alertNumber" :label="$t('event.Alerts')" sortable="custom" :width="100" :filters="$filter1">
              <template #default="{ row }">
                <el-link type="danger" :underline="false">{{ row.alertNumber || 0 }}</el-link>
              </template>
            </TableColumn>
            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderSummary" @filter-change="handleQuery()" prop="digest" :label="$t('event.Summary')" :min-width="120" :filters="$filter0"></TableColumn>
            <TableColumn type="condition" :label="t('userGroup.User group')" prop="teamName" :formatter="(_row, _col, _v) => (_v ? _v + (_row.userGroupTenantAbbreviation ? `[${_row.userGroupTenantAbbreviation}]` : '') : '--')" show-filter v-model:custom-filtered-value="searchByUserGroupName" :filters="$filter0" @filter-change="handleQuery()"> </TableColumn>
            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByExternalId" @filter-change="handleQuery()" prop="externalId" :label="$t('generalDetails.External ID')" :filters="$filter0" :min-width="120"></TableColumn>
            <TableColumn type="condition" :label="t('orderGroup.Order group')" prop="ticketGroupName" show-filter v-model:custom-filtered-value="searchByTicketGroupName" :filters="$filter0" @filter-change="handleQuery()"> </TableColumn>
            <TableColumn type="date" show-filter v-model:filtered-value="timeByCreate" filter-multiple @filter-change="handleQuery()" prop="createdTime" :label="$t('event.Created')" sortable="custom" :width="140"></TableColumn>
            <TableColumn type="date" show-filter v-model:filtered-value="timeByUpdate" filter-multiple @filter-change="handleQuery()" prop="updatedTime" :label="$t('event.Modified')" sortable="custom" :width="140"></TableColumn>
            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByResponsibleName" @filter-change="handleQuery()" prop="createdBy" :label="$t('event.Handler')" :min-width="120" :formatter="(_row, _col, v) => (userInfo.hasPermission(安全管理中心_用户管理_可读) ? JSON.parse(v)?.username + (_row.responsibleTenantAbbreviation ? `[${_row.responsibleTenantAbbreviation}]` : '') : '--')" :filters="$filter0"></TableColumn>
            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByActorName" @filter-change="handleQuery()" prop="actorName" :label="$t('event.Director')" :min-width="120" :formatter="(_row, _col, v) => (userInfo.hasPermission(安全管理中心_用户管理_可读) ? (v ? v + (_row.actorTenantAbbreviation ? `[${_row.actorTenantAbbreviation}]` : '') : '--') : '--')" :filters="$filter0"></TableColumn>
          </el-table>
        </template>
      </pageTemplate>
    </el-card>
    <Editor ref="editorRef" title="变更" display="dialog"></Editor>
  </el-scrollbar>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch, computed, h, readonly, reactive, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Refresh, InfoFilled } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";
import { ElTable, ElIcon } from "element-plus";
import Editor from "./Editor.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox } from "element-plus";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { useSiteConfig } from "@/stores/siteConfig";
import getUserInfo from "@/utils/getUserInfo";
import { filter, find } from "lodash-es";
import moment from "moment";

// import { type BaseItem, DataItem, type Item } from "./helper";
// import { state, dataList, expand, select, current } from "./helper";
// import { resetData, handleExpand, handleSort, command } from "./helper";

/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
import { getChangeList as getItemList, changeState, changeStateOption, priority, priorityOption, ChangeType, changeCreate as addItemData, getChangeStateCount /* getChangeList */ } from "@/views/pages/apis/change";
import { /*addServiceData as addItemData,*/ setServiceData as setItemData, modServiceData as modItemData, delServiceData as delItemData } from "@/views/pages/apis/event";
import { getChangeCount } from "@/views/pages/apis/eventBoard";
// import { getchangeState } from "@/views/pages/apis/event";

import handleToOrder from "@/views/pages/alarm_convergence/IntelligentEvents/eventBoard/toOrder";
import {
  /*  */
  智能事件中心_变更工单_可读,
  智能事件中心_变更工单_新增,
  智能事件中心_变更工单_更新,
  智能事件中心_变更工单_创建审批,
  智能事件中心_变更工单_编辑小记,
  智能事件中心_变更工单_分配设备,
  智能事件中心_变更工单_分配联系人,
  智能事件中心_变更工单_关联工单,
  智能事件中心_变更工单_分配用户,
  智能事件中心_变更工单_分配用户组,
  智能事件中心_变更工单_变更处理,
  智能事件中心_变更工单_变更审批,
  智能事件中心_变更工单_安全,
  智能事件中心_变更工单_所有权限,
  智能事件中心_客户_工单可读,
  安全管理中心_用户管理_可读,
  智能事件中心_工单看板_全部工单,
  智能事件中心_工单看板_我的工单,
  服务管理中心_工单模版_可读,
  服务管理中心_工单模版_新增,
  服务管理中心_工单组_可读,
} from "@/views/pages/permission";

import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";

import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);

/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const { t } = useI18n({ useScope: "global" });
const changeStateOptionA = computed(() =>
  changeStateOption.map((item) => ({
    ...item,
    labelS: t(`event.${item.label}`),
  }))
);
const route = useRoute();
const router = useRouter();
defineOptions({ name: "serviceRequest" });
const siteConfig = useSiteConfig();
const userInfo = getUserInfo();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);

interface Props {
  width?: number;
  height?: number;
  title?: string;
}
const props = withDefaults(defineProps<Props>(), { title: "告警" });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

const _width = inject("width", ref(0));
const _height = inject("height", ref(0));

const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);

const tableRef = ref<InstanceType<typeof ElTable>>();
const editorRef = ref<InstanceType<typeof Editor>>();
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */

interface State<T, P> {
  loading: boolean;
  expand: string[];
  select: string[];
  current: string | undefined;
  search: P;
  sort?: { prop: string; order: "ascending" | "descending" };
  list: T[];
  page: number;
  size: number;
  total: number;
}
enum command {
  Refresh = "Refresh",
  Request = "Request",
  Preview = "Preview",
  Create = "Create",
  Update = "Update",
  Modify = "Modify",
  Delete = "Delete",
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const final = readonly({ pagination: false });

type DataItem = ReturnType<typeof getItemList> extends Promise<{ data: (infer P)[] }> ? P : never;
type ParamsData = Omit<typeof getItemList extends (req: infer P) => Promise<{ data: DataItem[] }> ? P : never, "type" | "paging" | "sort">;

const state = reactive<State<DataItem, ParamsData>>({
  loading: false,
  expand: [],
  select: [],
  current: undefined,
  search: {
    includeState: [],
    excludeState: [],
    eqState: [],
    neState: [],
    includeTenantName: [],
    excludeTenantName: [],
    eqTenantName: [],
    neTenantName: [],
    includeOrderId: [],
    excludeOrderId: [],
    eqOrderId: [],
    neOrderId: [],
    includeOrderSummary: [],
    excludeOrderSummary: [],
    eqOrderSummary: [],
    neOrderSummary: [],
    includeActorName: [],
    excludeActorName: [],
    eqActorName: [],
    neActorName: [],
    includeResponsibleName: [],
    excludeResponsibleName: [],
    eqResponsibleName: [],
    neResponsibleName: [],
    stateFilterRelation: "AND",
    tenantNameFilterRelation: "AND",
    orderIdFilterRelation: "AND",
    orderSummaryFilterRelation: "AND",
    actorNameFilterRelation: "AND",
    responsibleNameFilterRelation: "AND",
    eqAlarmCount: [],
    neAlarmCount: [],
    geAlarmCount: [],
    gtAlarmCount: [],
    leAlarmCount: [],
    ltAlarmCount: [],
    isNullAlarmCount: [],
    isNotNullAlarmCount: [],
    alarmCountFilterRelation: "AND",
    inTicketGroupName: [],
    excludeTicketGroupName: [],
    eqTicketGroupName: [],
    neTicketGroupName: [],
    ticketGroupNameFilterRelation: "AND",

    includeExternalId: [],
    excludeExternalId: [],
    eqExternalId: [],
    neExternalId: [],
    externalIdFilterRelation: "AND",
    inUserGroupName: [],
    excludeUserGroupName: [],
    eqUserGroupName: [],
    neUserGroupName: [],
    userGroupNameFilterRelation: "AND",
    createdTimeRange: { start: "", end: "" },
    updatedTimeRange: { start: "", end: "" },
    createdBy: { userId: "", username: "" },
    updatedBy: { userId: "", username: "" },
    priority: "",
    state: "",
  },
  sort: undefined,
  list: [],
  page: 1,
  size: 50,
  total: 0,
});
const dataList = computed(() => (final.pagination ? state.list.slice((state.page - 1) * state.size, state.page * state.size) : state.list));
const expand = computed(() => filter<DataItem>(state.list, (row: DataItem) => state.expand.includes(row.id)));
const select = computed(() => filter<DataItem>(state.list, (row: DataItem) => state.select.includes(row.id)));
const current = computed(() => find<DataItem>(state.list, (row: DataItem) => row.id === state.current));
// const name = computed(() => state.name);

const timeByCreate = computed({
  get: () => (state.search.createdTimeRange.start && state.search.createdTimeRange.end ? { start: state.search.createdTimeRange.start, end: state.search.createdTimeRange.end } : ""),
  set: (v) => {
    state.search.createdTimeRange.start = (v || {}).start || "";
    state.search.createdTimeRange.end = (v || {}).end || "";
  },
});
const timeByUpdate = computed({
  get: () => (state.search.updatedTimeRange.start && state.search.updatedTimeRange.end ? { start: state.search.updatedTimeRange.start, end: state.search.updatedTimeRange.end } : ""),
  set: (v) => {
    state.search.updatedTimeRange.start = (v || {}).start || "";
    state.search.updatedTimeRange.end = (v || {}).end || "";
  },
});

const searchType0ByTenantName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByTenantName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByTenantName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByTenantName) === "include") value0 = state.search.includeTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "exclude") value0 = state.search.excludeTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "eq") value0 = state.search.eqTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "ne") value0 = state.search.neTenantName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByTenantName) === "include") value1 = state.search.includeTenantName[state.search.includeTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "exclude") value1 = state.search.excludeTenantName[state.search.excludeTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "eq") value1 = state.search.eqTenantName[state.search.eqTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "ne") value1 = state.search.neTenantName[state.search.neTenantName.length - 1] || "";
    return {
      type0: toValue(searchType0ByTenantName),
      type1: toValue(searchType1ByTenantName),
      relation: state.search.tenantNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByTenantName.value = v.type0 as typeof searchType0ByTenantName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByTenantName.value = v.type1 as typeof searchType1ByTenantName extends import("vue").Ref<infer T> ? T : string;
    state.search.tenantNameFilterRelation = v.relation;
    state.search.includeTenantName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeTenantName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqTenantName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neTenantName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByOrderIds = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByOrderIds = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByOrderIds = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByOrderIds) === "include") value0 = state.search.includeOrderId[0] || "";
    if (toValue(searchType0ByOrderIds) === "exclude") value0 = state.search.excludeOrderId[0] || "";
    if (toValue(searchType0ByOrderIds) === "eq") value0 = state.search.eqOrderId[0] || "";
    if (toValue(searchType0ByOrderIds) === "ne") value0 = state.search.neOrderId[0] || "";
    let value1 = "";
    if (toValue(searchType1ByOrderIds) === "include") value1 = state.search.includeOrderId[state.search.includeOrderId.length - 1] || "";
    if (toValue(searchType1ByOrderIds) === "exclude") value1 = state.search.excludeOrderId[state.search.excludeOrderId.length - 1] || "";
    if (toValue(searchType1ByOrderIds) === "eq") value1 = state.search.eqOrderId[state.search.eqOrderId.length - 1] || "";
    if (toValue(searchType1ByOrderIds) === "ne") value1 = state.search.neOrderId[state.search.neOrderId.length - 1] || "";
    return {
      type0: toValue(searchType0ByOrderIds),
      type1: toValue(searchType1ByOrderIds),
      relation: state.search.orderIdFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByOrderIds.value = v.type0 as typeof searchType0ByOrderIds extends import("vue").Ref<infer T> ? T : string;
    searchType1ByOrderIds.value = v.type1 as typeof searchType1ByOrderIds extends import("vue").Ref<infer T> ? T : string;
    state.search.orderIdFilterRelation = v.relation;
    state.search.includeOrderId = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeOrderId = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqOrderId = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neOrderId = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByStates = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByStates = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByStates = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByStates) === "include") value0 = state.search.includeState[0] || "";
    if (toValue(searchType0ByStates) === "exclude") value0 = state.search.excludeState[0] || "";
    if (toValue(searchType0ByStates) === "eq") value0 = state.search.eqState[0] || "";
    if (toValue(searchType0ByStates) === "ne") value0 = state.search.neState[0] || "";
    let value1 = "";
    if (toValue(searchType1ByStates) === "include") value1 = state.search.includeState[state.search.includeState.length - 1] || "";
    if (toValue(searchType1ByStates) === "exclude") value1 = state.search.excludeState[state.search.excludeState.length - 1] || "";
    if (toValue(searchType1ByStates) === "eq") value1 = state.search.eqState[state.search.eqState.length - 1] || "";
    if (toValue(searchType1ByStates) === "ne") value1 = state.search.neState[state.search.neState.length - 1] || "";
    return {
      type0: toValue(searchType0ByStates),
      type1: toValue(searchType1ByStates),
      relation: state.search.stateFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByStates.value = v.type0 as typeof searchType0ByStates extends import("vue").Ref<infer T> ? T : string;
    searchType1ByStates.value = v.type1 as typeof searchType1ByStates extends import("vue").Ref<infer T> ? T : string;
    state.search.stateFilterRelation = v.relation;
    state.search.includeState = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeState = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqState = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neState = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByAlarmCount = ref<"eq" | "ne" | "ge" | "gt" | "le" | "lt" | "isNull" | "isNotNull">("eq");
const searchType1ByAlarmCount = ref<"eq" | "ne" | "ge" | "gt" | "le" | "lt" | "isNull" | "isNotNull">("eq");
const searchByAlarmCount = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByAlarmCount) === "eq") value0 = state.search.eqAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "ne") value0 = state.search.neAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "ge") value0 = state.search.geAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "gt") value0 = state.search.gtAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "le") value0 = state.search.leAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "lt") value0 = state.search.ltAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "isNull") value0 = state.search.isNullAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "isNotNull") value0 = state.search.isNotNullAlarmCount[0] || "";
    let value1 = "";
    if (toValue(searchType1ByAlarmCount) === "eq") value1 = state.search.eqAlarmCount[state.search.eqAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "ne") value1 = state.search.neAlarmCount[state.search.neAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "ge") value1 = state.search.geAlarmCount[state.search.geAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "gt") value1 = state.search.gtAlarmCount[state.search.gtAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "le") value1 = state.search.leAlarmCount[state.search.leAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "lt") value1 = state.search.ltAlarmCount[state.search.ltAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "isNull") value1 = state.search.isNullAlarmCount[state.search.isNullAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "isNotNull") value1 = state.search.isNotNullAlarmCount[state.search.isNotNullAlarmCount.length - 1] || "";
    return {
      type0: toValue(searchType0ByAlarmCount),
      type1: toValue(searchType1ByAlarmCount),
      relation: state.search.alarmCountFilterRelation,
      value0,
      value1,
      input0: "number",
      input1: "number",
    };
  },
  set: (v) => {
    searchType0ByAlarmCount.value = v.type0 as typeof searchType0ByAlarmCount extends import("vue").Ref<infer T> ? T : string;
    searchType1ByAlarmCount.value = v.type1 as typeof searchType1ByAlarmCount extends import("vue").Ref<infer T> ? T : string;
    state.search.alarmCountFilterRelation = v.relation;
    state.search.eqAlarmCount = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neAlarmCount = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
    state.search.geAlarmCount = [...(v.type0 === "ge" ? [v.value0] : []), ...(v.type1 === "ge" ? [v.value1] : [])];
    state.search.gtAlarmCount = [...(v.type0 === "gt" ? [v.value0] : []), ...(v.type1 === "gt" ? [v.value1] : [])];
    state.search.leAlarmCount = [...(v.type0 === "le" ? [v.value0] : []), ...(v.type1 === "le" ? [v.value1] : [])];
    state.search.ltAlarmCount = [...(v.type0 === "lt" ? [v.value0] : []), ...(v.type1 === "lt" ? [v.value1] : [])];
    state.search.isNullAlarmCount = [...(v.type0 === "isNull" ? [v.value0] : []), ...(v.type1 === "isNull" ? [v.value1] : [])];
    state.search.isNotNullAlarmCount = [...(v.type0 === "isNotNull" ? [v.value0] : []), ...(v.type1 === "isNotNull" ? [v.value1] : [])];
  },
});
const searchType0ByActorName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByActorName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByActorName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByActorName) === "include") value0 = state.search.includeActorName[0] || "";
    if (toValue(searchType0ByActorName) === "exclude") value0 = state.search.excludeActorName[0] || "";
    if (toValue(searchType0ByActorName) === "eq") value0 = state.search.eqActorName[0] || "";
    if (toValue(searchType0ByActorName) === "ne") value0 = state.search.neActorName[0] || "";
    let value1 = "";
    if (toValue(searchType0ByActorName) === "include") value1 = state.search.includeActorName[state.search.includeActorName.length - 1] || "";
    if (toValue(searchType0ByActorName) === "exclude") value1 = state.search.excludeActorName[state.search.excludeActorName.length - 1] || "";
    if (toValue(searchType0ByActorName) === "eq") value1 = state.search.eqActorName[state.search.eqActorName.length - 1] || "";
    if (toValue(searchType0ByActorName) === "ne") value1 = state.search.neActorName[state.search.neActorName.length - 1] || "";
    return {
      type0: toValue(searchType0ByActorName),
      type1: toValue(searchType1ByActorName),
      relation: state.search.actorNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByActorName.value = v.type0 as typeof searchType0ByActorName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByActorName.value = v.type1 as typeof searchType1ByActorName extends import("vue").Ref<infer T> ? T : string;
    state.search.actorNameFilterRelation = v.relation;
    state.search.includeActorName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeActorName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqActorName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neActorName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByResponsibleName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByResponsibleName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByResponsibleName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByResponsibleName) === "include") value0 = state.search.includeResponsibleName[0] || "";
    if (toValue(searchType0ByResponsibleName) === "exclude") value0 = state.search.excludeResponsibleName[0] || "";
    if (toValue(searchType0ByResponsibleName) === "eq") value0 = state.search.eqResponsibleName[0] || "";
    if (toValue(searchType0ByResponsibleName) === "ne") value0 = state.search.neResponsibleName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByResponsibleName) === "include") value1 = state.search.includeResponsibleName[state.search.includeResponsibleName.length - 1] || "";
    if (toValue(searchType1ByResponsibleName) === "exclude") value1 = state.search.excludeResponsibleName[state.search.excludeResponsibleName.length - 1] || "";
    if (toValue(searchType1ByResponsibleName) === "eq") value1 = state.search.eqResponsibleName[state.search.eqResponsibleName.length - 1] || "";
    if (toValue(searchType1ByResponsibleName) === "ne") value1 = state.search.neResponsibleName[state.search.neResponsibleName.length - 1] || "";
    return {
      type0: toValue(searchType0ByResponsibleName),
      type1: toValue(searchType1ByResponsibleName),
      relation: state.search.responsibleNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByResponsibleName.value = v.type0 as typeof searchType0ByResponsibleName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByResponsibleName.value = v.type1 as typeof searchType1ByResponsibleName extends import("vue").Ref<infer T> ? T : string;
    state.search.responsibleNameFilterRelation = v.relation;
    state.search.includeResponsibleName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeResponsibleName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqResponsibleName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neResponsibleName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByOrderSummary = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByOrderSummary = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByOrderSummary = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByOrderSummary) === "include") value0 = state.search.includeOrderSummary[0] || "";
    if (toValue(searchType0ByOrderSummary) === "exclude") value0 = state.search.excludeOrderSummary[0] || "";
    if (toValue(searchType0ByOrderSummary) === "eq") value0 = state.search.eqOrderSummary[0] || "";
    if (toValue(searchType0ByOrderSummary) === "ne") value0 = state.search.neOrderSummary[0] || "";
    let value1 = "";
    if (toValue(searchType1ByOrderSummary) === "include") value1 = state.search.includeOrderSummary[state.search.includeOrderSummary.length - 1] || "";
    if (toValue(searchType1ByOrderSummary) === "exclude") value1 = state.search.excludeOrderSummary[state.search.excludeOrderSummary.length - 1] || "";
    if (toValue(searchType1ByOrderSummary) === "eq") value1 = state.search.eqOrderSummary[state.search.eqOrderSummary.length - 1] || "";
    if (toValue(searchType1ByOrderSummary) === "ne") value1 = state.search.neOrderSummary[state.search.neOrderSummary.length - 1] || "";
    return {
      type0: toValue(searchType0ByOrderSummary),
      type1: toValue(searchType1ByOrderSummary),
      relation: state.search.orderSummaryFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByOrderSummary.value = v.type0 as typeof searchType0ByOrderSummary extends import("vue").Ref<infer T> ? T : string;
    searchType1ByOrderSummary.value = v.type1 as typeof searchType1ByOrderSummary extends import("vue").Ref<infer T> ? T : string;
    state.search.orderSummaryFilterRelation = v.relation;
    state.search.includeOrderSummary = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeOrderSummary = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqOrderSummary = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neOrderSummary = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByUserGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByUserGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByUserGroupName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByUserGroupName) === "include") value0 = state.search.inUserGroupName[0] || "";
    if (toValue(searchType0ByUserGroupName) === "exclude") value0 = state.search.excludeUserGroupName[0] || "";
    if (toValue(searchType0ByUserGroupName) === "eq") value0 = state.search.eqUserGroupName[0] || "";
    if (toValue(searchType0ByUserGroupName) === "ne") value0 = state.search.neUserGroupName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByUserGroupName) === "include") value1 = state.search.inUserGroupName[state.search.inUserGroupName.length - 1] || "";
    if (toValue(searchType1ByUserGroupName) === "exclude") value1 = state.search.excludeUserGroupName[state.search.excludeUserGroupName.length - 1] || "";
    if (toValue(searchType1ByUserGroupName) === "eq") value1 = state.search.eqUserGroupName[state.search.eqUserGroupName.length - 1] || "";
    if (toValue(searchType1ByUserGroupName) === "ne") value1 = state.search.neUserGroupName[state.search.neUserGroupName.length - 1] || "";
    return {
      type0: toValue(searchType0ByUserGroupName),
      type1: toValue(searchType1ByUserGroupName),
      relation: state.search.userGroupNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByUserGroupName.value = v.type0 as typeof searchType0ByUserGroupName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByUserGroupName.value = v.type1 as typeof searchType1ByUserGroupName extends import("vue").Ref<infer T> ? T : string;
    state.search.userGroupNameFilterRelation = v.relation;
    state.search.inUserGroupName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeUserGroupName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqUserGroupName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neUserGroupName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
//外部ID  开始
const searchType0ByExternalId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByExternalId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByExternalId = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByExternalId) === "include") value0 = state.search.includeExternalId[0] || "";
    if (toValue(searchType0ByExternalId) === "exclude") value0 = state.search.excludeExternalId[0] || "";
    if (toValue(searchType0ByExternalId) === "eq") value0 = state.search.eqExternalId[0] || "";
    if (toValue(searchType0ByExternalId) === "ne") value0 = state.search.neExternalId[0] || "";
    let value1 = "";
    if (toValue(searchType1ByExternalId) === "include") value1 = state.search.includeExternalId[state.search.includeExternalId.length - 1] || "";
    if (toValue(searchType1ByExternalId) === "exclude") value1 = state.search.excludeExternalId[state.search.excludeExternalId.length - 1] || "";
    if (toValue(searchType1ByExternalId) === "eq") value1 = state.search.eqExternalId[state.search.eqExternalId.length - 1] || "";
    if (toValue(searchType1ByExternalId) === "ne") value1 = state.search.neExternalId[state.search.neExternalId.length - 1] || "";
    return {
      type0: toValue(searchType0ByExternalId),
      type1: toValue(searchType1ByExternalId),
      relation: state.search.externalIdFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByExternalId.value = v.type0 as typeof searchType0ByExternalId extends import("vue").Ref<infer T> ? T : string;
    searchType1ByExternalId.value = v.type1 as typeof searchType1ByExternalId extends import("vue").Ref<infer T> ? T : string;
    state.search.externalIdFilterRelation = v.relation;
    state.search.includeExternalId = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeExternalId = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqExternalId = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neExternalId = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
//外部ID  结束
const searchType0ByTicketGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByTicketGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByTicketGroupName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByTicketGroupName) === "include") value0 = state.search.inTicketGroupName[0] || "";
    if (toValue(searchType0ByTicketGroupName) === "exclude") value0 = state.search.excludeTicketGroupName[0] || "";
    if (toValue(searchType0ByTicketGroupName) === "eq") value0 = state.search.eqTicketGroupName[0] || "";
    if (toValue(searchType0ByTicketGroupName) === "ne") value0 = state.search.neTicketGroupName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByTicketGroupName) === "include") value1 = state.search.inTicketGroupName[state.search.inTicketGroupName.length - 1] || "";
    if (toValue(searchType1ByTicketGroupName) === "exclude") value1 = state.search.excludeTicketGroupName[state.search.excludeTicketGroupName.length - 1] || "";
    if (toValue(searchType1ByTicketGroupName) === "eq") value1 = state.search.eqTicketGroupName[state.search.eqTicketGroupName.length - 1] || "";
    if (toValue(searchType1ByTicketGroupName) === "ne") value1 = state.search.neTicketGroupName[state.search.neTicketGroupName.length - 1] || "";
    return {
      type0: toValue(searchType0ByTicketGroupName),
      type1: toValue(searchType1ByTicketGroupName),
      relation: state.search.ticketGroupNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByTicketGroupName.value = v.type0 as typeof searchType0ByTicketGroupName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByTicketGroupName.value = v.type1 as typeof searchType1ByTicketGroupName extends import("vue").Ref<infer T> ? T : string;
    state.search.ticketGroupNameFilterRelation = v.relation;
    state.search.inTicketGroupName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeTicketGroupName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqTicketGroupName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neTicketGroupName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
function handleExpand(row: DataItem, expandedRows: DataItem[]) {
  state.expand = expandedRows.filter((v) => v).map(({ id }) => id);
  if (find(expandedRows, ({ id }) => row.id === id)) {
    /*  */
  } else {
    /*  */
  }
}
function handleSort(sort: { prop: string; order: "ascending" | "descending" }) {
  state.sort = sort.prop ? sort : undefined;
}

/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const timer = ref<null | number>(null);
const autoRefreshTime = ref(0);

const changeStat = ref<{ label: string; value: changeState; color?: string; count: number }[]>([]);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  handleRefresh().then(() => (autoRefreshTime.value = 60));
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {
  if (timer.value !== null) {
    window.clearInterval(timer.value);
    timer.value = null;
  }
}
function beforeDestroy() {
  if (timer.value !== null) {
    window.clearInterval(timer.value);
    timer.value = null;
  }
}
function ruoterOrder(val) {
  const routeData = router.resolve({ name: "515123784953364480", params: { id: val }, query: { fallback: route.name as string } }, { target: "_blank" });
  window.open(routeData.href, val);
}
function destroyed() {}

watch(autoRefreshTime, (autoRefreshTime) => {
  if (timer.value !== null) timer.value = (window.clearInterval(timer.value), null);
  if (autoRefreshTime) timer.value = window.setInterval(queryData, autoRefreshTime * 1000);
});
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
async function handleCommand(type: command, data?: Record<string, unknown>) {
  const time = autoRefreshTime.value;
  autoRefreshTime.value = 0;
  try {
    state.loading = true;
    await nextTick();
    switch (type) {
      case command.Refresh:
        await resetData();
        await queryData();
        break;
      case command.Request:
        await queryData();
        break;
      case command.Preview:
        // await previewItem(data as Record<string, unknown>);
        break;
      case command.Create:
        await createItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
      case command.Update:
        // await rewriteItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
      case command.Modify:
        // await modifyItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
      case command.Delete:
        // await deleteItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
    }
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await resetData();
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
      await queryData();
    }
  } finally {
    autoRefreshTime.value = time;
    state.loading = false;
  }
}
async function handleRefresh() {
  try {
    state.loading = true;
    await nextTick();
    await resetData();
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
async function handleQuery() {
  try {
    state.loading = true;
    await nextTick();
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
async function resetData() {
  state.list = [];
  state.page = 1;
  state.size = 50;
  state.total = 0;
  // for (const key in state.search) {
  //   if (Object.prototype.hasOwnProperty.call(state.search, key)) {
  //     delete state.search[key];
  //   }
  // }
  await nextTick();
}
function timeZoneSwitching(): number {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == userInfo.zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
async function queryData() {
  try {
    let sort: string[] = [];
    switch ((state.sort || {}).order) {
      case "ascending":
        sort.push(`${String(state.sort?.prop)},asc`);
        if (state.sort?.prop === "createTime") {
          sort.shift();

          sort.push(`createdTime,asc`);
          // delete sort.createTime;
        } else if (state.sort?.prop === "updateTime") {
          sort.shift();

          sort.push(`updatedTime,asc`);
          // delete sort.updateTime;
        }
        break;
      case "descending":
        sort.push(`${String(state.sort?.prop)},desc`);
        if (state.sort?.prop === "createTime") {
          sort.shift();

          sort.push(`createdTime,desc`);
          // delete sort.createTime;
        } else if (state.sort?.prop === "updateTime") {
          sort.shift();

          sort.push(`updatedTime,desc`);
          // delete sort.updateTime;
        }
        break;
    }

    // const type = userInfo.hasPermission(智能事件中心_工单看板_全部工单) ? "all" : userInfo.hasPermission(智能事件中心_工单看板_我的工单) ? "my" : "";

    // if (!type) return;

    const [{ success, message, data, page, size, total }, { success: statSuccess, message: statMessage, data: statData }] = await Promise.all([getItemList({ ...state.search, type: "", boardOrNot: false, sort, paging: { pageNumber: state.page, pageSize: state.size } }), getChangeCount({ type: "", boardOrNot: false })]);
    if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
    if (!statSuccess) throw Object.assign(new Error(statMessage), { success: statSuccess, data: statData });

    changeStat.value = statData.map((v): { label: string; value: changeState; color?: string; count: number } => {
      const typeItem = find(changeStateOption, ({ value }) => v.state === value) || { label: v.state as string, value: v.state };
      return { count: Number(v.count) || 0, label: typeItem.label, value: typeItem.value as changeState };
    });

    const select = new Set((data instanceof Array ? data : []).filter((v) => state.select.includes(v.id)).map((v) => v.id));

    // state.list.splice(0, Infinity, ...(data instanceof Array ? data : []));
    state.list.splice(0, state.list.length, ...(data instanceof Array ? data : []).map((v) => Object.assign(v, { updatedTime: Number(v.updatedTime) + timeZoneSwitching() }, { createdTime: Number(v.createdTime) + timeZoneSwitching() })));

    state.page = Number(page) || 1;
    state.size = Number(size) || 20;
    state.total = Number(total) || 0;

    await nextTick();
    if (tableRef.value) {
      tableRef.value.clearSelection();
      for (let i = 0; i < state.list.length; i++) {
        tableRef.value.toggleRowSelection(state.list[i], select.has(state.list[i].id));
      }
    }
    state.select = Array.from(select);
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  }
}
async function createItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    if (form.ticketClassificationId) {
      const ticketClassificationArrId = (form.ticketClassificationId as string).split(",");
      form.ticketClassificationId = ticketClassificationArrId[ticketClassificationArrId.length - 1];
    }
    const { success, message, data } = await addItemData({ changeType: form.changeType as ChangeType, digest: form.digest as string, priority: form.priority as priority, desc: form.desc as string, ticketTemplateId: form.ticketTemplateId as string, ticketSubtype: form.ticketSubtype as string });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`成功创建变更`);
    router.push({ name: "515123784953364480", params: { id: data.id }, query: { fallback: route.name as string, tenant: data.tenantId } });
  });
}

function copyClick(row, column, cell, event) {
  event.stopPropagation();
}

// async function previewItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setItemData({ ids: form.ids as string[], ...form });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
// async function rewriteItem(row: Record<string, unknown>) {
//   const title = (find(priorityOption, ({ value }) => value === row.priority) || {}).label || row.priority;
//   if (!editorRef.value) return row;
//   const params = { select: <Item[]>row.select /* .filter((v) => row.priority !== v.priority) */ };
//   await editorRef.value.confirm({ ...row, ...params, $type: "warning", $title: `批量${title}`, $slot: "batchConfirm" }, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setEventDataByPriority({ id: (<Item[]>form.select).map((v) => v.id), priority: form.priority as priority });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`成功${form.$title}`);
//   });
// }
// async function modifyItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   const params = { select: (<Item[]>row.select).filter((v) => v.eventState === eventState.UNASSIGNED) };
//   const form = { type: "userGroupId", id: "" };
//   await editorRef.value.confirm({ ...row, ...form, ...params, $type: "warning", $title: `批量指派`, $slot: "batchAssign" }, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setEventDataByAssign({ id: (<Item[]>form.select).map((v) => v.id), userGroupId: form.userGroupId as string, userId: form.userId as string });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`成功${form.$title}`);
//   });
// }
// async function deleteItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await delItemData(form);
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: DataItem): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
