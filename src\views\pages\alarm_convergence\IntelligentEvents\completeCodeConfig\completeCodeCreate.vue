<template>
  <div>
    <el-dialog :title="`${type == 'add'?i18n.t('closeCode.Newclosecode'):i18n.t('closeCode.editclosecode')}`" v-model="dialogFormVisible" :before-close="handleClose" width="50vw">
      <el-form :model="form"  ref="ruleForm">
        <el-form-item :rules="[{ required: true, message: `${this.i18n.t('closeCode.Please enter a close code name')}`, trigger: 'blur' }]" :label=" `${i18n.t('closeCode.name')}`" :label-width="formLabelWidth" prop="codeName">
          <el-input v-model="form.codeName" autocomplete="off" :placeholder="`${i18n.t('closeCode.Please enter name')}`"></el-input>
        </el-form-item>
        <el-form-item :label=" `${i18n.t('closeCode.describe')}`" :label-width="formLabelWidth" prop="codeDesc">
          <el-input type="textarea" v-model="form.codeDesc" autocomplete="off" :rows="2" :placeholder="`${i18n.t('closeCode.Please enter a description')}`"></el-input>
        </el-form-item>
        <el-form-item :label=" `${i18n.t('closeCode.IsDefault')}`" :label-width="formLabelWidth" prop="defaultable">
          <el-checkbox v-model="form.defaultable"></el-checkbox>
        </el-form-item>
        <el-form-item v-if="type == 'add'" :label="`${i18n.t('closeCode.Select a secure directory')}`" :label-width="formLabelWidth">
          <treeAuth ref="treeAuthRef" :treeStyle="treeStyle"></treeAuth>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel()">{{`${i18n.t('closeCode.cancel')}`}}</el-button>
          <el-button type="primary" @click="confirm('ruleForm')">{{`${i18n.t('closeCode.confirm')}`}}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent } from "vue";
import { ElMessage, ElMenuItem } from "element-plus";

import { addNewContacts, editNewContacts, hasDefaultCloseCode } from "@/views/pages/apis/completeCodeConfig";
import treeAuth from "@/components/treeAuth/index.vue";
import { ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";

export default defineComponent({
  name: "completeCodeCreate",
  components: {
    treeAuth,
  },
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      i18n:useI18n(),
      form: {
        containerId: "",
        codeName: "",
        codeDesc: "",
        codeId: "",
        defaultable: true,
      },
      containerIdS: null,
      // rules: {
      //   codeName: [{ required: true, message: `${this.i18n.t('closeCode.Please enter a close code name')}`, trigger: "blur" }],
      // },
      title: "",
      checked: false,
      dialogFormVisible: false,
      formLabelWidth: "167px",
      type: "",
      value: "",
      disabled: "",
      treeStyle: {
        width: "300px",
        height: "150px",
      },
    };
  },
  watch: {
    dialog(val) {
      this.dialogFormVisible = val;
    },
    type(val) {
      if (val === "add") {
        for (var key in this.form) {
          this.form[key] = null;
        }
      }
    },
  },
  created() {
    console.log(this.i18n,'this.i18n.t :>> ', this.i18n.t);
  },
  methods: {
    confirm(formName) {
      if (this.type == "add") {
        this.form.containerId = this.$refs.treeAuthRef.treeItem.id;
      } else {
        this.form.containerId = "";
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.type === "add") {
            if (!this.form.containerId) {
              ElMessage.error(`${this.i18n.t('closeCode.Select a secure directory')}`);
              return;
            }
            if (this.form.defaultable == true) {
              hasDefaultCloseCode({}).then((res) => {
                if (res.data == true) {
                  ElMessageBox.confirm(`${this.i18n.t('closeCode.The customer already has a default closure code, and modifying it will overwrite the original closure code. Do you want to modify it?')}`, `${this.i18n.t('closeCode.reminder')}`, {
                    confirmButtonText: `${this.i18n.t('closeCode.confirm')}`,
                    cancelButtonText: `${this.i18n.t('closeCode.cancel')}`,
                    type: "warning",
                  })
                    .then(() => {
                      addNewContacts(this.form)
                        .then((res) => {
                          if (res.success) {
                            ElMessage.success(`${this.i18n.t('closeCode.New success')}`);
                            this.$emit("dialogClose", false);
                            this.$refs[formName].resetFields();
                            this.form.defaultable = false;
                            this.$refs.treeAuthRef.getSafeContaine();
                            this.$refs.treeAuthRef.treeId = -1;
                          } else {
                            ElMessage.error(JSON.parse(res.data)?.message);
                            // this.$emit("dialogClose", false);
                          }
                        })
                        .catch((e) => {
                          if (e instanceof Error) ElMessage.error(e.message);
                          // this.$emit("dialogClose", false);
                        });
                    })
                    .catch(() => {});
                } else {
                  addNewContacts(this.form)
                    .then((res) => {
                      if (res.success) {
                        ElMessage.success(`${this.i18n.t('closeCode.New success')}`);
                        this.$emit("dialogClose", false);
                        this.$refs[formName].resetFields();
                        this.form.defaultable = false;
                        this.$refs.treeAuthRef.getSafeContaine();
                        this.$refs.treeAuthRef.treeId = -1;
                      } else {
                        ElMessage.error(JSON.parse(res.data)?.message);
                        // this.$emit("dialogClose", false);
                      }
                    })
                    .catch((e) => {
                      if (e instanceof Error) ElMessage.error(e.message);
                      // this.$emit("dialogClose", false);
                    });
                }
              });
            } else {
              addNewContacts(this.form)
                .then((res) => {
                  if (res.success) {
                    ElMessage.success(`${this.i18n.t('closeCode.New success')}`);
                    this.$emit("dialogClose", false);
                    this.$refs[formName].resetFields();
                    this.form.defaultable = false;
                    this.$refs.treeAuthRef.getSafeContaine();
                    this.$refs.treeAuthRef.treeId = -1;
                  } else {
                    ElMessage.error(JSON.parse(res.data)?.message);
                    // this.$emit("dialogClose", false);
                  }
                })
                .catch((e) => {
                  if (e instanceof Error) ElMessage.error(e.message);
                  // this.$emit("dialogClose", false);
                });
            }
          } else {
            editNewContacts(this.form)
              .then((res) => {
                if (res.success) {
                  ElMessage.success(`${this.i18n.t('closeCode.modify successfully')}`);
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                  this.form.defaultable = false;
                } else {
                  ElMessage.error(JSON.parse(res.data)?.message);
                  // this.$emit("dialogClose", false);
                }
              })
              .catch((e) => {
                if (e instanceof Error) ElMessage.error(e.message);
                // this.$emit("dialogClose", false);
              });
          }
          // this.dialogFormVisible = false;
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      this.form.defaultable = false;
      if (this.type == "add") {
        this.$refs.treeAuthRef.getSafeContaine();
        this.$refs.treeAuthRef.treeId = -1;
      }
    },
    cancel() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      this.form.defaultable = false;
      if (this.type == "add") {
        this.$refs.treeAuthRef.getSafeContaine();
        this.$refs.treeAuthRef.treeId = -1;
      }
    },
    blurChange(data) {
      this.tags.push(data);
    },
    tagClose(index) {
      this.tags.splice(index, 1);
    },
  },
  expose: ["type", "disabled", "title", "form", "value"],
});
</script>
