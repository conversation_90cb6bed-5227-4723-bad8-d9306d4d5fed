<template>
  <el-card
    :body-style="{
      padding: '20px',
      height: `${height}px`,
      width: `${width}px`,
    }"
  >
    <pageTemplate :showPaging="false" v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="getcodeList()" @current-change="getcodeList()">
      <template #left>
        <div class="sla-config">
          <h2>{{ $t("closeCode.Close code configuration") }}</h2>
        </div>
      </template>
      <template #right>
        <span class="tw-h-fit">
          <el-button v-if="userInfo.hasPermission(服务管理中心_关闭代码配置_新增)" type="primary" :icon="Plus" @click="handleCreate('add')">{{ $t("closeCode.Newclosecode") }}</el-button>
        </span>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table stripe :data="tableData" :height="tableHeight" style="width: 100%" :row-key="getRowKeys" :expand-row-keys="expands" @expand-change="handleExpandChange">
          <el-table-column type="expand">
            <template #default="{ row, expanded }">
              <treeEditor v-if="expanded" :detail="row" :width="width - 40" @confirm="update"></treeEditor>
            </template>
          </el-table-column>
          <!-- <TableColumn type="default" show-filter v-model:filtered-value="ServiceSearch" @filter-change="getcodeList()" prop="codeName" label="关闭代码名称" :width="320"> </TableColumn> -->

          <TableColumn type="condition" :prop="`codeName`" :label="$t('closeCode.Closecodename')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="getcodeList()" :formatter="formatterTable"></TableColumn>

          <!-- <el-table-column align="left" prop="codeDesc" label="描述" :formatter="formatterTable"> </el-table-column> -->

          <TableColumn type="condition" :prop="`codeDesc`" :label="$t('closeCode.describe')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByDescription" :filters="$filter0" @filter-change="getcodeList()" :formatter="formatterTable"></TableColumn>

          <el-table-column align="left" :label="$t('closeCode.IsDefault')">
            <template #default="scope">
              <span> {{ scope.row.defaultable == true ? "√" : "" }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="126">
            <template #default="{ row }">
              <span class="tw-h-fit tw-align-middle">
                <el-link v-if="row.hasPermissionIds.includes(服务管理中心_关闭代码配置_编辑)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleCreate('edit', row as DataItem)">{{ $t("glob.edit") }}</el-link>
              </span>
              <span class="tw-h-fit tw-align-middle">
                <el-link v-if="row.hasPermissionIds.includes(服务管理中心_关闭代码配置_删除)" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="SlaConfigDelete(row as DataItem)">{{ $t("glob.delete") }}</el-link>
              </span>
              <span>
                <!-- 关闭代码配置('604219285753036800') -->
                <el-link :type="row.hasPermissionIds.includes('612169437818126336') ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="row.hasPermissionIds.includes('612169437818126336') ? false : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>

  <completeCodeCreate :dialog="completeCodedialog" ref="completeCodeRef" @dialogClose="dialogClose"></completeCodeCreate>

  <el-dialog v-model="dialogVisibleshow" :title="$t('closeCode.Security Container')" width="500" :before-close="handleClose">
    <treeAuth :proptreeId="containerId" :treeStyle="treeStyle" ref="treeAuthRef"></treeAuth>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisibleshow = false">{{ $t("glob.NO") }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { 服务管理中心_关闭代码配置_新增, 服务管理中心_关闭代码配置_编辑, 服务管理中心_关闭代码配置_删除 } from "@/views/pages/permission";

/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox, ElText } from "element-plus";
import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
import pageTemplate from "@/components/pageTemplate.vue";
import treeAuth from "@/components/treeAuth/index.vue";
import completeCodeCreate from "./completeCodeCreate.vue";
import treeEditor from "./treeEditor";
import getUserInfo from "@/utils/getUserInfo";
import { state } from "./helper";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";
import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";

/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */

import { getnewCodeConfigList, DelCodeConfig, EnableSlaConfig, SlaConfigStatus, type SlaConfigList as DataItem } from "@/views/pages/apis/completeCodeConfig";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", {
    type: "error",
  });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "completeCodeConfig" });

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));
const userInfoS = getUserInfo();

// const $filter0 = ref([
//   { text: "包含", value: "include" },
//   { text: "不包含", value: "exclude" },
//   { text: "等于", value: "eq" },
//   { text: "不等于", value: "ne" },
// ]);
const $filter0 = ref(exoprtMatch1);

const searchForm = ref<Record<string, any>>({
  eqName: [],
  includeName: [],
  nameFilterRelation: "AND",
  neName: [],
  excludeName: [],

  eqDescription: [],
  includeDescription: [],
  descriptionFilterRelation: "AND",
  neDescription: [],
  excludeDescription: [],
});

const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByName) === "include") value0 = searchForm.value.includeName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = searchForm.value.excludeName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = searchForm.value.eqName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = searchForm.value.neName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = searchForm.value.includeName[searchForm.value.includeName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = searchForm.value.excludeName[searchForm.value.excludeName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = searchForm.value.eqName[searchForm.value.eqName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = searchForm.value.neName[searchForm.value.neName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: searchForm.value.nameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.nameFilterRelation = v.relation;
    searchForm.value.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByDescription = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByDescription) === "include") value0 = searchForm.value.includeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "exclude") value0 = searchForm.value.excludeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "eq") value0 = searchForm.value.eqDescription[0] || "";
    if (toValue(searchType0ByDescription) === "ne") value0 = searchForm.value.neDescription[0] || "";
    let value1 = "";
    if (toValue(searchType1ByDescription) === "include") value1 = searchForm.value.includeDescription[searchForm.value.includeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "exclude") value1 = searchForm.value.excludeDescription[searchForm.value.excludeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "eq") value1 = searchForm.value.eqDescription[searchForm.value.eqDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "ne") value1 = searchForm.value.neDescription[searchForm.value.neDescription.length - 1] || "";
    return {
      type0: toValue(searchType0ByDescription),
      type1: toValue(searchType1ByDescription),
      relation: searchForm.value.descriptionFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByDescription.value = v.type0 as typeof searchType0ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchType1ByDescription.value = v.type1 as typeof searchType1ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.descriptionFilterRelation = v.relation;
    searchForm.value.includeDescription = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeDescription = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqDescription = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neDescription = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const tableLoading = ref(false);
const tableData = ref<DataItem[]>([]);
const expands = ref<string[]>([]);
const containerId = ref("");
const dialogVisibleshow = ref(false);
const treeAuthRef = ref<InstanceType<typeof treeAuth>>();

const completeCodedialog = ref(false);
const paging = reactive({
  pageNumber: 1,
  pageSize: 50,
  total: 0,
});
const siteConfig = useSiteConfig();
const userInfo = ((key) => {
  switch (key) {
    case process.env["APP_SUPER_PLATFORM"]:
      return useSuperInfo();
    case process.env["APP_ADMIN_PLATFORM"]:
      return useAdminInfo();
    case process.env["APP_USERS_PLATFORM"]:
      return useUsersInfo();
    default:
      return null;
  }
})(siteConfig.current);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  getcodeList();
}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

function formatterTable(_row, _col, v) {
  switch (_col.property) {
    default:
      return v || "--";
  }
}

function searchSlaList() {
  paging.pageNumber = 1;
  getcodeList();
}
function getRowKeys(row) {
  return row.id;
}
async function handleExpandChange(row, expandedRows) {
  if (expandedRows.length) {
    //展开
    expands.value = [];
    if (row) {
      expands.value.push(row.id);
    }
  } else {
    expands.value = [];
  }
}
function getcodeList() {
  let data = {
    ...paging,
  };

  tableLoading.value = true;
  getnewCodeConfigList({
    ...searchForm.value,
    containerId: userInfoS.currentTenant.containerId,
    queryPermissionId: "517242654942035968",
    verifyPermissionIds: ["517242693848399872", "517242712731156480", "612169437818126336"],
  })
    .then(({ success, data, page, size, total }) => {
      if (success) {
        let arr = [...data];
        tableData.value = arr;
        paging.total = Number(total);
        paging.pageNumber = page;
        paging.pageSize = size;
      }
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {
      tableLoading.value = false;
    });
}
// 新增编辑
async function handleCreate(type, row) {
  try {
    ctx.refs.completeCodeRef.type = type;
    if (type === "add") {
      // ctx.refs.completeCodeRef.title = "新增关闭代码";
    } else {
      ctx.refs.completeCodeRef.form = {
        codeName: row.codeName,
        codeDesc: row.codeDesc,
        id: row.id,
        defaultable: row.defaultable,
      };
      // ctx.refs.completeCodeRef.title = "编辑关闭代码";
    }
    completeCodedialog.value = true;
  } catch (error) {
    /*  */
  } finally {
    /*  */
  }
}
//关闭弹框
function dialogClose(bool) {
  completeCodedialog.value = bool;
  getcodeList();
}

//删除
function SlaConfigDelete(row: Partial<DataItem>) {
  if (row.status) {
    ElMessage.warning("启用状态下无法删除，请更改数据状态");
  } else
    ElMessageBox.confirm("此操作将永久删除该项, 是否继续?", "删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        if (tableData.value.length == 2) {
          tableData.value.length = tableData.value.length - 1;
        }
        // tableData.value.find((i) => i.id === v.id))
        if (!row.status) {
          let params = {
            id: row.id,
          };
          DelCodeConfig(params)
            .then(({ success, data, message }) => {
              if (!success) throw new Error(message);
              paging.pageNumber = 1;
              ElMessage.success("操作成功");
              getcodeList();
            })
            .catch((e) => {
              if (e instanceof Error) ElMessage.error(e.message);
            })
            .finally(() => {
              tableLoading.value = false;
            });
        } else {
          ElMessage.warning("启用状态下无法删除，请更改数据状态");
        }
      })
      .catch(() => {
        // ElMessage.info("已取消删除");
      });
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.sla-config {
  display: flex;
  align-items: center;
  h2 {
    display: flex;
    align-items: center;
    height: 100%;
    font-size: 14px;
    padding-left: 10px;
    box-sizing: border-box;
    font-weight: 700;
    margin-top: 15px;
  }
}
</style>
