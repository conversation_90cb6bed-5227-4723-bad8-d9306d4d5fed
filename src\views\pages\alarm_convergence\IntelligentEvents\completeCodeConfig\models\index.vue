<template>
  <pageTemplate v-model:currentPage="page" v-model:pageSize="size" :total="props.total" :height="height" :width="width" :show-paging="false" @size-change="handleCommand(command.Request)" @current-change="handleCommand(command.Request)">
    <template #left>
      <el-input v-model="search.keyword" :disabled="props.loading" :placeholder="`${$t('glob.Please input field', { field: $t('closeCode.name') })}${$t('closeCode.search')}`" @keyup.enter="handleCommand(command.Request)" class="tw-w-fit">
        <template #append>
          <el-button :icon="Search" :disabled="props.loading" @click.stop="handleCommand(command.Request)" />
        </template>
      </el-input>
    </template>
    <template #center> </template>
    <template #right>
      <span class="tw-h-fit">
        <el-button v-if="userInfo.hasPermission(PERMISSION.group517242632200519680.create)" :disabled="props.loading" type="primary" :icon="Plus" @click="handleCommand(command.Create, { parentId: null, codeType: props.type })">{{ $t("glob.add") }}{{ props.title }}</el-button>
      </span>
      <span class="tw-ml-[16px] tw-h-fit">
        <el-button v-if="userInfo.hasPermission(PERMISSION.group517242632200519680.preview)" :disabled="props.loading" type="default" :icon="Refresh" @click="handleCommand(command.Refresh)"></el-button>
      </span>
    </template>
    <template #default="{ height: tableHeight }">
      <el-table v-loading="props.loading" ref="tableRef" :data="props.list" row-key="id" :height="tableHeight" :expand-row-keys="expand" @expand-change="(row, expanded) => (expanded ? _expand.add(row.id) : _expand.delete(row.id))">
        <el-table-column prop="codeName" :label="$t('closeCode.name')"></el-table-column>
        <el-table-column prop="codeDesc" :label="$t('closeCode.describe')"></el-table-column>

        <el-table-column align="left" :label="$t('closeCode.Remove from report')">
          <template #default="scope">
            <span> {{ scope.row.removeFromReport == true ? "√" : "" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" :label="$t('closeCode.status')" :width="140" :formatter="(_row, _col, v) => (v ? $t('glob.Enable') : $t('glob.Disable'))">
          <!-- <template #default="{ row }">
            <el-switch :model-value="row.status" :active-text="$t('glob.Enable')" :active-value="true" :inactive-text="$t('glob.Disable')" :inactive-value="false" @change="handleCommand(command.Update, { id: row.id, ruleStatus: !row.status })"></el-switch>
          </template> -->
        </el-table-column>
        <el-table-column :label="$t('glob.operate')" header-align="left" align="left" :width="280">
          <template #default="{ row }">
            <span class="tw-h-fit">
              <el-link v-if="userInfo.hasPermission(PERMISSION.group517242632200519680.editor)" :disabled="props.loading" class="tw-mx-[3px]" type="primary" :underline="false" @click.prevent="handleCommand(command.Modify, row)">{{ $t("glob.edit") }}</el-link>
            </span>
            <span class="tw-h-fit">
              <el-link v-if="userInfo.hasPermission(PERMISSION.group517242632200519680.editor)" :disabled="props.loading" class="tw-mx-[3px]" :type="row.status ? 'danger' : 'primary'" :underline="false" @click.prevent="handleCommand(command.Update, { id: row.id, ruleStatus: !row.status })">{{ row.status ? $t("glob.Disable") : $t("glob.Enable") }}</el-link>
            </span>
            <span class="tw-h-fit">
              <el-link v-if="userInfo.hasPermission(PERMISSION.group517242632200519680.create)" :disabled="props.loading" class="tw-mx-[3px]" type="primary" :underline="false" @click.prevent="handleCommand(command.Create, { parentId: row.id, codeType: props.type })">{{ $t("glob.add") + $t("closeCode.subclass") }}</el-link>
            </span>
            <span class="tw-h-fit">
              <el-link v-if="userInfo.hasPermission(PERMISSION.group517242632200519680.remove)" :disabled="props.loading" class="tw-mx-[3px]" type="danger" :underline="false" @click.prevent="handleCommand(command.Delete, row)">{{ $t("glob.delete") }}</el-link>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </template>
  </pageTemplate>
</template>

<script setup lang="ts" generic="T extends object">
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, defineAsyncComponent, watch, useModel, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete, Refresh } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox } from "element-plus";
import getUserInfo from "@/utils/getUserInfo";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
import { codeConfigType as activeType } from "@/views/pages/apis/completeCodeConfig";
import { handleCommandKey, commandKey, command as _command } from "../helper";
/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const userInfo = getUserInfo();
defineOptions({ name: "completeCodeConfigModel" });
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */

const width = inject("width", ref(0));
const height = inject("height", ref(0));

interface Props {
  type: activeType;
  title?: string;
  page: number;
  size: number;
  total: number;
  search: Record<string, string>;
  loading: boolean;
  list: import("../helper").Item[];
}
const props = withDefaults(defineProps<Props>(), { title: "完成代码配置" });

const search = useModel(props, "search");
const page = useModel(props, "page");
const size = useModel(props, "size");

const _expand = ref(new Set<string>());
const expand = computed(() => Array.from(_expand.value));

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");
const handleCommand = inject(handleCommandKey, (_type: _command, _data?: Record<string, unknown>) => Promise.resolve(void 0));
const command = inject(commandKey, _command);
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */

/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */

/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
