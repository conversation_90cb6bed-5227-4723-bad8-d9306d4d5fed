<template>
  <el-card>
    <el-tabs v-model="active">
      <el-tab-pane :label="`${item.label + i18n.t('closeCode.Configuration')}`" :name="item.name" v-for="item in tabs" :key="`config-${item.name}`">
        <component :type="active" :loading="state.loading" v-model:search="state.search" v-model:page="state.page" v-model:size="state.size" :total="state.total" :is="item.Component" :title="item.label" :list="searchTree"></component>
      </el-tab-pane>
    </el-tabs>
    <Editor ref="editorRef" :title="$t('closeCode.Close code')" display="dialog" :id="props.detail.id">
      <template #deleteItem="{ params }">
        <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
          <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
          <p class="title">
            {{$t("closeCode.Confirm to delete")}}
            <span :style="{ color: 'var(--el-color-danger)' }">{{ params.codeName }}</span>
            {{$t("closeCode.Close the code?")}}
          </p>
        </div>
      </template>
    </Editor>
  </el-card>
</template>

<script lang="ts" setup generic="T extends { [key: string]: unknown; id: string }">
import { computed, ref, shallowRef, provide, inject, onMounted, watch, nextTick, getCurrentInstance, onUpdated, onUnmounted, onBeforeMount, onBeforeUpdate, onBeforeUnmount, onActivated, onDeactivated } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

import { InfoFilled } from "@element-plus/icons-vue";

import { state, command, resetData, handleCommandKey, commandKey, type Item } from "./helper";

import { getCodeConfigList as getItemList, codeConfigType as activeType, getexpandConfig } from "@/views/pages/apis/completeCodeConfig";
import { addNewCodeconfig, editNewCodeconfig, deleteNewCodeconfig, addCodeConfigData as addItemData, modCodeConfigData as setItemData, modCodeConfigData as modItemData, delCodeConfigData as delItemData, setCodeConfigDataByStatus } from "@/views/pages/apis/completeCodeConfig";

import Editor from "./Editor.vue";
import ModelComponent from "./models/index.vue";
import { ElMessage, ElMessageBox } from "element-plus";

const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}

const route = useRoute();
const router = useRouter();
const i18n = useI18n({ useScope: "global" });

const width = inject("width", ref(0));
const height = inject("height", ref(0));
provide(
  "width",
  computed(() => width.value - 55)
);
provide(
  "height",
  computed(() => height.value - 95)
);
provide(handleCommandKey, handleCommand);
provide(commandKey, command);

const editorRef = shallowRef<InstanceType<typeof Editor>>();

interface Props {
  detail: T;
  width: number;
}
const props = withDefaults(defineProps<Props>(), { width: 0, detail: () => ({}) as T });

const active = computed({
  get: () => (route.query.type as activeType) || activeType.EVENT,
  set: (v) => ((route.query.type as activeType) || activeType.EVENT) !== v && router.push({ query: { ...route.query, type: v } }),
});
const tabs = shallowRef([
  { label: `${i18n.t("closeCode.Event closure code")}`, name: activeType.EVENT, Component: ModelComponent },
  { label: `${i18n.t("closeCode.Service request closure code")}`, name: activeType.SERVICE, Component: ModelComponent },
  { label: `${i18n.t("closeCode.Problem closure code")}`, name: activeType.QUESTION, Component: ModelComponent },
  { label: `${i18n.t("closeCode.Change closure code")}`, name: activeType.CHANGE, Component: ModelComponent },
  { label: `${i18n.t("closeCode.Publish and close code")}`, name: activeType.PUBLISH, Component: ModelComponent },
]);

const searchTree = computed((): Item[] => {
  const subHasTreeItem = (parent: Item[], item: Item) => {
    if (item.codeName.includes(state.search.keyword || "")) parent.push({ ...item });
    else {
      const childrenItem: Item = { ...item, children: [] };
      for (let i = 0; i < item.children.length; i++) {
        subHasTreeItem(childrenItem.children, item.children[i]);
      }
      if (childrenItem.children.length) parent.push(childrenItem);
    }
    return parent;
  };
  return state.list.reduce(subHasTreeItem, [] as Item[]);
});

async function handleCommand(type: command, data?: Record<string, unknown>) {
  const time = autoRefreshTime.value;
  autoRefreshTime.value = 0;
  try {
    await nextTick();
    switch (type) {
      case command.Refresh:
        await resetData();
        break;
      case command.Request:
        break;
      case command.Preview:
        await previewItem(data as Record<string, unknown>);
        break;
      case command.Create:
        await createItem(data as Record<string, unknown>);
        await resetData();
        break;
      case command.Update:
        await updateItem(data as Record<string, unknown>);
        await resetData();
        break;
      case command.Modify:
        await modifyItem(data as Record<string, unknown>);
        await resetData();
        break;
      case command.Delete:
        await deleteItem(data as Record<string, unknown>);
        await resetData();
        break;
    }
    state.loading = true;
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await resetData();
      ElMessage.error(message);
      // await new Promise((resolve) => ElMessage.error({ message, showClose: true, onClose: () => resolve(null) }));
      await queryData();
    }
  } finally {
    autoRefreshTime.value = time;
    state.loading = false;
  }
}
function array2Tree(arr) {
  if (!Array.isArray(arr) || !arr.length) return;
  let map = {};
  arr.forEach((item) => (map[item.id] = item));

  let roots = [];
  arr.forEach((item) => {
    const parent = map[item.parentId];
    if (parent) {
      (parent.children || (parent.children = [])).push(item);
    } else {
      roots.push(item);
    }
  });

  return roots || [];
}

async function queryData() {
  // console.log(state, "999999");

  const { success, message, data, page, size, total } = await getexpandConfig({ id: props.detail.id, codeType: active.value, paging: { pageNumber: state.page, pageSize: state.size } });
  if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });

  const result = array2Tree(data);

  state.list.splice(0, Infinity, ...(result || []));
}
async function createItem(row: Record<string, unknown>) {
  console.log(row);
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Partial<Item>) => {
    // console.log(form);
    // delete form.id
    const { success, message, data } = await addNewCodeconfig(form);
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${i18n.t("axios.Operation successful")}`);
  });
}
async function previewItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Partial<Item>) => {
    const { success, message, data } = await setItemData(form);
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${i18n.t("axios.Operation successful")}`);
  });
}
async function updateItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;

  const { success, message, data } = await setCodeConfigDataByStatus(row);
  if (!success) throw Object.assign(new Error(message), { success, data });
  ElMessage.success(`${i18n.t("axios.Operation successful")}`);
  // await editorRef.value.open(row, async (form: Partial<Item>) => {
  //   const { success, message, data } = await setCodeConfigDataByStatus(form);
  //   if (!success) throw Object.assign(new Error(message), { success, data });
  //   ElMessage.success(`${i18n.t("axios.Operation successful")}`);
  // });
}
async function modifyItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  // console.log(row);
  await editorRef.value.open(row, async (form: Partial<Item>) => {
    // console.log(form, row);
    const { success, message, data } = await editNewCodeconfig(form);
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${i18n.t("axios.Operation successful")}`);
  });
}
async function deleteItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.confirm({ ...row, $title: `${i18n.t("closeCode.Delete completed code")}`, $slot: "deleteItem" }, async (form: Partial<Item>) => {
    const { success, message, data } = await deleteNewCodeconfig(form);
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${i18n.t("axios.Operation successful")}`);
  });
}

const timer = ref<null | NodeJS.Timer>(null);
const autoRefreshTime = ref(0);
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  watch(active, async () => {
    await handleCommand(command.Refresh);
  });
  handleCommand(command.Refresh).then(() => (autoRefreshTime.value = 10));
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {
  if (timer.value !== null) {
    clearInterval(timer.value);
    timer.value = null;
  }
}
function beforeDestroy() {
  if (timer.value !== null) {
    clearInterval(timer.value);
    timer.value = null;
  }
}
function destroyed() {}

watch(autoRefreshTime, (autoRefreshTime) => {
  if (timer.value !== null) {
    clearInterval(timer.value);
    timer.value = null;
  }
  if (autoRefreshTime) {
    timer.value = setInterval(async () => {
      try {
        await nextTick();
        await queryData();
      } catch (error) {
        if (error instanceof Error) {
          const message = error.message;
          await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
        }
      }
    }, autoRefreshTime * 1000);
  }
});
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
</script>
