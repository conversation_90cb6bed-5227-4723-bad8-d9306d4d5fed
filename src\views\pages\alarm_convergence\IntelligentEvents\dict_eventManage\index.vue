<template>
  <el-scrollbar :height="height">
    <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
      <pageTemplate v-model:currentPage="state.page" v-model:pageSize="state.size" :total="state.total" :height="height - 40" :show-paging="true" @size-change="handleCommand(command.Request)" @current-change="handleCommand(command.Request)">
        <template #left>
          <el-radio-group v-model="state.search.eventState" :style="{ whiteSpace: 'nowrap', flexWrap: 'nowrap' }" @change="handleQuery()">
            <el-radio-button label="">
              <el-badge type="primary" :value="eventStat.reduce((p, c) => p + c.count, 0)" :hidden="!eventStat.reduce((p, c) => p + c.count, 0)">
                <div class="tw-px-[1em]">{{ $t("el.table.clearFilter") }}</div>
              </el-badge>
            </el-radio-button>
            <el-radio-button v-for="item in eventStat" :key="item.value" :label="item.value" v-show="item.label != '完成'">
              <el-badge type="primary" :value="item.count" :hidden="!item.count">
                <div class="tw-px-[1em]">{{ item.label }}</div>
              </el-badge>
            </el-radio-button>
          </el-radio-group>
          <!-- <el-input v-model="state.search.alert" :disabled="state.loading" :placeholder="$t('glob.Please input field', { field: $t('glob.Keyword') })" @keyup.enter="handleQuery()">
            <template #append>
              <el-button :icon="Search" :disabled="state.loading" @click.stop="handleQuery()" />
            </template>
          </el-input> -->
        </template>
        <template #right>
          <span class="tw-h-fit">
            <el-button v-if="userInfo.hasPermission(智能事件中心_DICT事件管理_新增) /* && userInfo.hasPermission(服务管理中心_工单模版_可读) && userInfo.hasPermission(服务管理中心_工单组_可读) */" :disabled="state.loading" type="primary" :icon="Plus" @click="handleCommand(command.Create, {})">{{ $t("glob.add") }}事件</el-button>
          </span>
          <!-- <span class="tw-ml-[16px] tw-h-fit">
            <el-button v-if="userInfo.hasPermission(智能事件中心_DICT事件管理_审批)" :disabled="state.loading || !select.filter((v) => v.eventState === eventState.PENDING_APPROVAL).length" type="primary" @click="handleCommand(command.Approve, { select })">批量审批</el-button>
          </span> -->
          <span class="tw-ml-[16px] tw-h-fit">
            <el-button v-if="userInfo.hasPermission(智能事件中心_DICT事件管理_更新)" :disabled="state.loading || !chooseIdList.length" type="primary" @click="rewriteItem(chooseIdList)">批量P8</el-button>
          </span>
          <!-- <span class="tw-ml-[16px] tw-h-fit">
            <el-button v-if="userInfo.hasPermission(智能事件中心_事件工单_查看历史工单)" :disabled="state.loading" type="primary" :icon="Platform" @click="routerV6eventHistoryAll()">历史工单</el-button>
          </span> -->
          <span class="tw-ml-[16px] tw-h-fit">
            <el-button v-if="userInfo.hasPermission(智能事件中心_DICT事件管理_可读)" :disabled="state.loading" type="default" :icon="Refresh" @click="handleCommand(command.Refresh)" :title="$t('glob.refresh')"></el-button>
          </span>
          <!-- <el-button type="primary" :disabled="state.loading || !select.filter((v) => v.eventState === eventState.UNASSIGNED).length" @click="handleCommand(command.Modify, { select })">批量指派</el-button> -->
        </template>
        <template #default="{ height: tableHeight }">
          <el-table v-loading="state.loading" ref="tableRef" @cell-contextmenu="copyClick" :data="dataList" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).map((v) => v.id))" @expand-change="handleExpand">
            <!-- <TableColumn type="selection" :width="55" :selectable="checkSelectSet"></TableColumn> -->
            <el-table-column :width="55">
              <template #header>
                <!-- {{ row }} -->
                <!-- <div>123456789</div> -->
                <div style="display: flex; align-items: center">
                  <el-checkbox :model-value="state.list.every((v) => v.checkbox)" :indeterminate="state.list.every((v) => v.checkbox) ? false : state.list.some((v) => v.checkbox)" size="large" @change="handleCheckAllChange" />
                  <el-dropdown style="margin-left: 8px">
                    <span class="el-dropdown-link" style="font-size: 20px">
                      <el-icon><CaretBottom /></el-icon>
                    </span>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <!-- <el-dropdown-item v-if="userInfo.hasPermission(智能事件中心_DICT事件管理_更新)" @click="handleEnd" :disabled="chooseIdList.length < 1">快速关闭</el-dropdown-item> -->
                        <el-dropdown-item v-if="userInfo.hasPermission(智能事件中心_DICT事件管理_编辑小记)" @click="handelAddNotes" :disabled="chooseIdList.length < 1"> {{ $t("eventBoard.Add Journal") }}</el-dropdown-item>
                        <!-- <el-dropdown-item v-if="userInfo.hasPermission(智能事件中心_工单看板_批量处理)" @click="rewriteItem(chooseIdList)" :disabled="chooseIdList.length > 0"> 批量P8</el-dropdown-item> -->
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
              <template #default="{ row }">
                <!-- {{ row }} -->
                <el-checkbox v-model="row.checkbox" size="large" @change="chooseTable" :disabled="row.eventState == 'CLOSED' || row.eventState == 'AUTO_CLOSED'" />
              </template>
            </el-table-column>
            <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.priority" @filter-change="handleQuery()" prop="priority" label="优先级" :width="120" :filters="priorityOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>
            <!-- <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByStates" @filter-change="handleQuery()" prop="eventState" label="状态" :width="120" :filters="$filter0">
              <template #default="{ row, column }">
                <el-tag :type="(find(eventStateOption, (v) => v.value === row[column.property]) || {}).type" size="small">{{ (find(eventStateOption, (v) => v.value === row[column.property]) || {}).label }}</el-tag>
              </template>
            </TableColumn> -->

            <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.state" @filter-change="handleQuery()" prop="eventState" label="状态" :width="110" :filters="eventStateOption.map((v) => ({ value: v.value, text: v.label }))" sortable="custom">
              <template #default="{ row, column }">
                <el-tag :type="(find(eventStateOption, (v) => v.value === row[column.property]) || {}).type" size="small">{{ (find(eventStateOption, (v) => v.value === row[column.property]) || {}).label }}</el-tag>
              </template>
            </TableColumn>

            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderIds" @filter-change="handleQuery()" prop="identifier" column-key="identifier" label="工单" sortable="custom" :filters="$filter0" :width="180">
              <template #default="{ row, column }">
                <!-- <router-link v-if="row.id" :to="{ name: '689693383307821056', params: { id: row.id }, query: { fallback: route.name as string } }" custom> -->
                <!-- <template #default="{ href }"> -->
                <el-link type="primary" @click.stop="handleToOrder(row.orderType, row.id, row.tenantId)" target="_blank" :underline="false" class="tw-ml-[6px]">{{ row[column.property] }}</el-link>
                <!-- </template> -->
                <!-- </router-link> -->
              </template>
            </TableColumn>
            <!-- <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByAlarmCount" @filter-change="handleQuery()" prop="alarmNumber" label="告警" sortable="custom" :filters="$filter1" :width="100">
              <template #default="{ row }">
                <el-link type="danger" :underline="false">{{ row.alarmNumber || 0 }}</el-link>
              </template>
            </TableColumn> -->
            <!-- <el-table-column prop="alarmNumber" label="告警" sortable="custom">
              <template #default="{ row }">
                <el-link type="danger" :underline="false">{{ row.alarmNumber || 0 }}</el-link>
              </template>
            </el-table-column> -->
            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderSummary" @filter-change="handleQuery()" prop="summary" label="摘要" :filters="$filter0" :min-width="120"></TableColumn>
            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByKbServiceCodes" @filter-change="handleQuery()" prop="kbServiceCode" label="外部工单号" :filters="$filter0" :min-width="120"></TableColumn>
            <TableColumn type="condition" :label="t('userGroup.User group')" :formatter="(_row, _col, _v) => (_v ? _v + (_row.userGroupTenantAbbreviation ? `[${_row.userGroupTenantAbbreviation}]` : '') : '--')" prop="displayUserGroupName" show-filter v-model:custom-filtered-value="searchByUserGroupName" :filters="$filter0" @filter-change="handleQuery()"> </TableColumn>
            <TableColumn type="condition" :label="t('orderGroup.Order group')" prop="ticketGroupName" show-filter v-model:custom-filtered-value="searchByTicketGroupName" :filters="$filter0" @filter-change="handleQuery()"> </TableColumn>
            <TableColumn type="date" show-filter v-model:filtered-value="timeByCreate" filter-multiple @filter-change="handleQuery()" prop="createTime" label="创建时间" sortable="custom" :width="140"></TableColumn>
            <TableColumn type="date" show-filter v-model:filtered-value="timeByUpdate" filter-multiple @filter-change="handleQuery()" prop="updateTime" label="修改时间" sortable="custom" :width="140"></TableColumn>
            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByResponsibleName" @filter-change="handleQuery()" prop="responsibleName" label="负责人" :filters="$filter0" :min-width="120" :formatter="(_row, _col, _v) => (userInfo.hasPermission(安全管理中心_用户管理_可读) || userInfo.userId === _row.responsibleId ? (_v || '') + (_row.responsibleTenantAbbreviation ? `[${_row.responsibleTenantAbbreviation}]` : '') : '--')"></TableColumn>
            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByActorName" @filter-change="handleQuery()" prop="actorName" label="处理人" :filters="$filter0" :min-width="120" :formatter="(_row, _col, _v) => (userInfo.hasPermission(安全管理中心_用户管理_可读) || userInfo.userId === _row.actorId ? (_row.actorName ? _row.actorName + (_row.actorTenantAbbreviation ? `[${_row.actorTenantAbbreviation}]` : '') : (_row.userGroupName || '') + (_row.userGroupTenantAbbreviation ? `[${_row.userGroupTenantAbbreviation}]` : '')) : '--')"></TableColumn>

            <!-- <TableColumn type="default" label="操作" :width="132" header-align="left" align="left" fixed="right">
              <template #default="{ row }">
                <el-link type="danger" :underline="false" class="tw-mr-[6px]" @click.stop>审批</el-link>
                <el-link type="danger" :underline="false" class="tw-mx-[6px]" @click.stop>指派</el-link>
                <el-link type="primary" :underline="false" class="tw-ml-[6px]" @click.stop="router.push({ name: '689693383307821056', params: { id: row.id }, query: { fallback: route.name as string } })">详情</el-link>
              </template>
            </TableColumn> -->
          </el-table>
        </template>
      </pageTemplate>
    </el-card>
    <Editor ref="editorRef" title="事件" display="dialog">
      <template #batchConfirm="{ params }">
        <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
          <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
          <p class="title">确定批量P8以下事件吗？</p>
        </div>
        <ol class="tw-pl-4">
          <li v-for="(item, index) in <DataItem[]>params.select" :key="item.id">{{ `${index + 1}. ${item.summary}` }}</li>
        </ol>
      </template>
      <template #batchAssign="{ params, form }">
        <el-alert title="选择需要指派对象，被指派到的对象需要立即处理事件" type="info" show-icon class="tw-mb-4"></el-alert>
        <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
          <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
          <p class="title">确定批量指派以下事件吗？</p>
        </div>
        <ol class="tw-pl-4">
          <li v-for="(item, index) in <DataItem[]>params.select" :key="item.id">{{ `${index + 1}. ${item.summary}` }}</li>
        </ol>
        <FormModel :model="form">
          <FormItem label="指派对象类型" props="type">
            <el-radio-group v-model="form.type" @change="form.id = ''">
              <el-radio label="userGroupId">用户组</el-radio>
              <el-radio label="userId">用户</el-radio>
            </el-radio-group>
          </FormItem>
          <FormItem label="指派对象" props="id">
            <el-select v-model="form.id" value-key="" placeholder="" clearable filterable>
              <el-option v-for="item in (params[form.type] || []) as Record<'label' | 'value', string>[]" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </FormItem>
        </FormModel>
        <div></div>
      </template>
    </Editor>
    <approve ref="approveRef" title="挂起审批" />

    <EventEnd ref="endRef" :refresh="handleRefresh" @end="closeEvent" :height="height - 317"></EventEnd>
    <el-dialog v-model="innerVisible" title="正在关闭工单" append-to-body :before-close="beforeCloseInnerDialog">
      <div class="close-event">
        <el-scrollbar height="380px" class="close-event-scroll">
          <div class="close-event-list">
            <h3>工单</h3>
            <ul>
              <li v-for="item in chooseIdList" :key="item">
                {{ item }}
              </li>
            </ul>
          </div>
          <div class="close-event-list">
            <h3>状况简介</h3>

            <ul>
              <li v-for="item in closeMessage" :key="item">
                {{ item }}
              </li>
            </ul>
          </div>
        </el-scrollbar>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="startPromise" v-if="showStartBtn && showCancel">
            <el-icon><CaretRight /></el-icon>开始</el-button
          >
          <el-button @click="cancelPromise" v-if="showCancel"> 取消</el-button>
          <el-button type="primary" @click="beforeCloseInnerDialog">
            <el-icon><Close /></el-icon> 关闭</el-button
          >
        </div>
      </template>
    </el-dialog>
    <EventCreateNote ref="eventCreateNoteRef" :batchIds="chooseIdList" orderType="DICT_EVENT_ORDER" />
  </el-scrollbar>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch, computed, h, toValue, readonly, reactive } from "vue";
import { useRoute, useRouter, useLink } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Refresh, InfoFilled, CaretBottom, CaretRight, Close } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";
import { ElTable, ElIcon } from "element-plus";
import Editor from "./Editor.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import approve from "./approve.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox, ElForm, ElFormItem } from "element-plus";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { useSiteConfig } from "@/stores/siteConfig";
import getUserInfo from "@/utils/getUserInfo";
import { filter, find, first } from "lodash-es";
import moment from "moment";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
import { eventState, eventStateOption, priority, priorityOption } from "@/views/pages/apis/dictEvent";
import { getEventList as getList } from "@/views/pages/apis/dictEvent";
import { addEventData as addItemData, setEventData as setItemData, modEventData as modItemData, delEventData as delItemData } from "@/views/pages/apis/dictEvent";
import { setEventDataByPriority, setEventDataByAssign, getEventStat, type SuspendRecord } from "@/views/pages/apis/dictEvent";
import { getDictEventCount } from "@/views/pages/apis/eventBoard";
import { dictEventApproveHangUp } from "@/views/pages/apis/eventManage";
import EventEnd from "./end.vue";
import EventCreateNote from "@/views/pages/alarm_convergence/details/eventDetail/models/createNote.vue";
import axios from "axios";
import { batchCloseDictEvent, axiosCancel } from "@/views/pages/apis/eventBoard";

import { routerV6eventHistoryAll } from "@/views/pages/common/routeV6";
import { 智能事件中心_DICT事件管理_可读, 智能事件中心_DICT事件管理_新增, 服务管理中心_工单模版_可读, 服务管理中心_工单模版_新增, 服务管理中心_工单组_可读, 智能事件中心_DICT事件管理_批量审批, 智能事件中心_事件工单_查看历史工单, 智能事件中心_事件工单_审批, 智能事件中心_事件工单_更新, 智能事件中心_事件工单_编辑小记, 智能事件中心_事件工单_分配设备, 智能事件中心_事件工单_分配联系人, 智能事件中心_事件工单_关联工单, 智能事件中心_事件工单_安全, 智能事件中心_事件工单_所有权限, 智能事件中心_事件工单_分配用户, 智能事件中心_事件工单_分配用户组, 智能事件中心_工单看板_批量处理, 智能事件中心_客户_工单可读, 安全管理中心_用户管理_可读, 智能事件中心_工单看板_我的工单, 智能事件中心_工单看板_全部工单, 智能事件中心_DICT事件管理_更新, 智能事件中心_DICT事件管理_编辑小记, 智能事件中心_DICT事件管理_审批 } from "@/views/pages/permission";
import _timeZoneHours from "@/views/pages/common/offsetHours.json";

import handleToOrder from "@/views/pages/alarm_convergence/IntelligentEvents/eventBoard/toOrder";

import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";

const timeZoneHours = ref(_timeZoneHours);

/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
defineOptions({ name: "alarmBoard" });
const siteConfig = useSiteConfig();
const userInfo = getUserInfo();

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);
interface Props {
  width?: number;
  height?: number;
  title?: string;
}
const props = withDefaults(defineProps<Props>(), { title: "告警" });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

const _width = inject("width", ref(0));
const _height = inject("height", ref(0));

const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);
const $width = inject<import("vue").Ref<number>>("width", ref(document.body.clientWidth - 200));
const dialogWidth = ref($width.value / 1.75);

const tableRef = ref<InstanceType<typeof ElTable>>();
const editorRef = ref<InstanceType<typeof Editor>>();
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface State<T, P> {
  loading: boolean;
  expand: string[];
  select: string[];
  current: string | undefined;
  search: P;
  sort?: { prop: string; order: "ascending" | "descending" };
  list: T[];
  page: number;
  size: number;
  total: number;
}
enum command {
  Refresh = "Refresh",
  Request = "Request",
  Preview = "Preview",
  Create = "Create",
  Update = "Update",
  Modify = "Modify",
  Approve = "approve",
  Delete = "Delete",
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const final = readonly({
  pagination: false,
});
type DataItem = typeof getList extends (req: any) => Promise<{ data: Array<infer T> }> ? T : never;
type ParamsData = Omit<typeof getList extends (req: infer P) => Promise<{ data: DataItem[] }> ? P : never, "type" | "paging" | "sort">;
const state = reactive<State<DataItem, ParamsData>>({
  loading: false,
  expand: [],
  select: [],
  current: undefined,
  search: {
    createTimeStart: "",
    createTimeEnd: "",
    updateTimeStart: "",
    updateTimeEnd: "",
    compactTimeStart: "",
    compactTimeEnd: "",
    includeTenantName: [],
    excludeTenantName: [],
    eqTenantName: [],
    neTenantName: [],
    tenantNameFilterRelation: "AND",
    includeOrderId: [],
    excludeOrderId: [],
    eqOrderId: [],
    neOrderId: [],
    orderIdFilterRelation: "AND",
    includeState: [],
    excludeState: [],
    eqState: [],
    neState: [],
    stateFilterRelation: "AND",
    eqAlarmCount: [],
    neAlarmCount: [],
    geAlarmCount: [],
    gtAlarmCount: [],
    leAlarmCount: [],
    ltAlarmCount: [],
    isNullAlarmCount: [],
    isNotNullAlarmCount: [],
    alarmCountFilterRelation: "AND",
    includeActorName: [],
    excludeActorName: [],
    eqActorName: [],
    neActorName: [],
    actorNameFilterRelation: "AND",
    includeResponsibleName: [],
    excludeResponsibleName: [],
    eqResponsibleName: [],
    neResponsibleName: [],
    responsibleNameFilterRelation: "AND",
    includeOrderSummary: [],
    excludeOrderSummary: [],
    eqOrderSummary: [],
    neOrderSummary: [],
    orderSummaryFilterRelation: "AND",
    includeCompactActorName: [],
    excludeCompactActorName: [],
    eqCompactActorName: [],
    neCompactActorName: [],
    compactActorNameFilterRelation: "AND",
    includeKbServiceCodes: [],
    excludeKbServiceCodes: [],
    eqKbServiceCodes: [],
    neKbServiceCodes: [],
    kbServiceCodesFilterRelation: "AND",

    inTicketGroupName: [],
    excludeTicketGroupName: [],
    eqTicketGroupName: [],
    neTicketGroupName: [],
    ticketGroupNameFilterRelation: "AND",

    inUserGroupName: [],
    excludeUserGroupName: [],
    eqUserGroupName: [],
    neUserGroupName: [],
    userGroupNameFilterRelation: "AND",
  },
  sort: undefined,
  list: [],
  page: 1,
  size: 50,
  total: 0,
});
const dataList = computed(() => (final.pagination ? state.list.slice((state.page - 1) * state.size, state.page * state.size) : state.list));
const expand = computed(() => filter<DataItem>(state.list, (row: DataItem) => state.expand.includes(row.id)));
const select = computed(() => filter<DataItem>(state.list, (row: DataItem) => state.select.includes(row.id)));
const current = computed(() => find<DataItem>(state.list, (row: DataItem) => row.id === state.current));
// const name = computed(() => state.name);

const timeByCreate = computed({
  get: () => (state.search.createTimeStart && state.search.createTimeEnd ? { start: state.search.createTimeStart, end: state.search.createTimeEnd } : ""),
  set: (v) => {
    state.search.createTimeStart = (v || {}).start || "";
    state.search.createTimeEnd = (v || {}).end || "";
  },
});
const timeByUpdate = computed({
  get: () => (state.search.updateTimeStart && state.search.updateTimeEnd ? { start: state.search.updateTimeStart, end: state.search.updateTimeEnd } : ""),
  set: (v) => {
    state.search.updateTimeStart = (v || {}).start || "";
    state.search.updateTimeEnd = (v || {}).end || "";
  },
});
const searchType0ByKbServiceCodes = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByKbServiceCodes = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByKbServiceCodes = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByKbServiceCodes) === "include") value0 = state.search.includeKbServiceCodes[0] || "";
    if (toValue(searchType0ByKbServiceCodes) === "exclude") value0 = state.search.excludeKbServiceCodes[0] || "";
    if (toValue(searchType0ByKbServiceCodes) === "eq") value0 = state.search.eqKbServiceCodes[0] || "";
    if (toValue(searchType0ByKbServiceCodes) === "ne") value0 = state.search.neKbServiceCodes[0] || "";
    let value1 = "";
    if (toValue(searchType1ByKbServiceCodes) === "include") value1 = state.search.includeKbServiceCodes[state.search.includeKbServiceCodes.length - 1] || "";
    if (toValue(searchType1ByKbServiceCodes) === "exclude") value1 = state.search.excludeKbServiceCodes[state.search.excludeKbServiceCodes.length - 1] || "";
    if (toValue(searchType1ByKbServiceCodes) === "eq") value1 = state.search.eqKbServiceCodes[state.search.eqKbServiceCodes.length - 1] || "";
    if (toValue(searchType1ByKbServiceCodes) === "ne") value1 = state.search.neKbServiceCodes[state.search.neKbServiceCodes.length - 1] || "";
    return {
      type0: toValue(searchType0ByKbServiceCodes),
      type1: toValue(searchType1ByKbServiceCodes),
      relation: state.search.kbServiceCodesFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByKbServiceCodes.value = v.type0 as typeof searchType0ByKbServiceCodes extends import("vue").Ref<infer T> ? T : string;
    searchType1ByKbServiceCodes.value = v.type1 as typeof searchType1ByKbServiceCodes extends import("vue").Ref<infer T> ? T : string;
    state.search.kbServiceCodesFilterRelation = v.relation;
    state.search.includeKbServiceCodes = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeKbServiceCodes = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqKbServiceCodes = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neKbServiceCodes = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByTenantName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByTenantName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByTenantName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByTenantName) === "include") value0 = state.search.includeTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "exclude") value0 = state.search.excludeTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "eq") value0 = state.search.eqTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "ne") value0 = state.search.neTenantName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByTenantName) === "include") value1 = state.search.includeTenantName[state.search.includeTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "exclude") value1 = state.search.excludeTenantName[state.search.excludeTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "eq") value1 = state.search.eqTenantName[state.search.eqTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "ne") value1 = state.search.neTenantName[state.search.neTenantName.length - 1] || "";
    return {
      type0: toValue(searchType0ByTenantName),
      type1: toValue(searchType1ByTenantName),
      relation: state.search.tenantNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByTenantName.value = v.type0 as typeof searchType0ByTenantName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByTenantName.value = v.type1 as typeof searchType1ByTenantName extends import("vue").Ref<infer T> ? T : string;
    state.search.tenantNameFilterRelation = v.relation;
    state.search.includeTenantName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeTenantName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqTenantName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neTenantName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByOrderIds = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByOrderIds = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByOrderIds = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByOrderIds) === "include") value0 = state.search.includeOrderId[0] || "";
    if (toValue(searchType0ByOrderIds) === "exclude") value0 = state.search.excludeOrderId[0] || "";
    if (toValue(searchType0ByOrderIds) === "eq") value0 = state.search.eqOrderId[0] || "";
    if (toValue(searchType0ByOrderIds) === "ne") value0 = state.search.neOrderId[0] || "";
    let value1 = "";
    if (toValue(searchType1ByOrderIds) === "include") value1 = state.search.includeOrderId[state.search.includeOrderId.length - 1] || "";
    if (toValue(searchType1ByOrderIds) === "exclude") value1 = state.search.excludeOrderId[state.search.excludeOrderId.length - 1] || "";
    if (toValue(searchType1ByOrderIds) === "eq") value1 = state.search.eqOrderId[state.search.eqOrderId.length - 1] || "";
    if (toValue(searchType1ByOrderIds) === "ne") value1 = state.search.neOrderId[state.search.neOrderId.length - 1] || "";
    return {
      type0: toValue(searchType0ByOrderIds),
      type1: toValue(searchType1ByOrderIds),
      relation: state.search.orderIdFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByOrderIds.value = v.type0 as typeof searchType0ByOrderIds extends import("vue").Ref<infer T> ? T : string;
    searchType1ByOrderIds.value = v.type1 as typeof searchType1ByOrderIds extends import("vue").Ref<infer T> ? T : string;
    state.search.orderIdFilterRelation = v.relation;
    state.search.includeOrderId = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeOrderId = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqOrderId = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neOrderId = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByStates = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByStates = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByStates = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByStates) === "include") value0 = state.search.includeState[0] || "";
    if (toValue(searchType0ByStates) === "exclude") value0 = state.search.excludeState[0] || "";
    if (toValue(searchType0ByStates) === "eq") value0 = state.search.eqState[0] || "";
    if (toValue(searchType0ByStates) === "ne") value0 = state.search.neState[0] || "";
    let value1 = "";
    if (toValue(searchType1ByStates) === "include") value1 = state.search.includeState[state.search.includeState.length - 1] || "";
    if (toValue(searchType1ByStates) === "exclude") value1 = state.search.excludeState[state.search.excludeState.length - 1] || "";
    if (toValue(searchType1ByStates) === "eq") value1 = state.search.eqState[state.search.eqState.length - 1] || "";
    if (toValue(searchType1ByStates) === "ne") value1 = state.search.neState[state.search.neState.length - 1] || "";
    return {
      type0: toValue(searchType0ByStates),
      type1: toValue(searchType1ByStates),
      relation: state.search.stateFilterRelation,
      value0,
      value1,
      // input0: "",
      input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      // input1: "",
      input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByStates.value = v.type0 as typeof searchType0ByStates extends import("vue").Ref<infer T> ? T : string;
    searchType1ByStates.value = v.type1 as typeof searchType1ByStates extends import("vue").Ref<infer T> ? T : string;
    state.search.stateFilterRelation = v.relation;
    state.search.includeState = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeState = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqState = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neState = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByAlarmCount = ref<"eq" | "ne" | "ge" | "gt" | "le" | "lt" | "isNull" | "isNotNull">("eq");
const searchType1ByAlarmCount = ref<"eq" | "ne" | "ge" | "gt" | "le" | "lt" | "isNull" | "isNotNull">("eq");
const searchByAlarmCount = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByAlarmCount) === "eq") value0 = state.search.eqAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "ne") value0 = state.search.neAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "ge") value0 = state.search.geAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "gt") value0 = state.search.gtAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "le") value0 = state.search.leAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "lt") value0 = state.search.ltAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "isNull") value0 = state.search.isNullAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "isNotNull") value0 = state.search.isNotNullAlarmCount[0] || "";
    let value1 = "";
    if (toValue(searchType1ByAlarmCount) === "eq") value1 = state.search.eqAlarmCount[state.search.eqAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "ne") value1 = state.search.neAlarmCount[state.search.neAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "ge") value1 = state.search.geAlarmCount[state.search.geAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "gt") value1 = state.search.gtAlarmCount[state.search.gtAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "le") value1 = state.search.leAlarmCount[state.search.leAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "lt") value1 = state.search.ltAlarmCount[state.search.ltAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "isNull") value1 = state.search.isNullAlarmCount[state.search.isNullAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "isNotNull") value1 = state.search.isNotNullAlarmCount[state.search.isNotNullAlarmCount.length - 1] || "";
    return {
      type0: toValue(searchType0ByAlarmCount),
      type1: toValue(searchType1ByAlarmCount),
      relation: state.search.alarmCountFilterRelation,
      value0,
      value1,
      input0: "number",
      input1: "number",
    };
  },
  set: (v) => {
    searchType0ByAlarmCount.value = v.type0 as typeof searchType0ByAlarmCount extends import("vue").Ref<infer T> ? T : string;
    searchType1ByAlarmCount.value = v.type1 as typeof searchType1ByAlarmCount extends import("vue").Ref<infer T> ? T : string;
    state.search.alarmCountFilterRelation = v.relation;
    state.search.eqAlarmCount = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neAlarmCount = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
    state.search.geAlarmCount = [...(v.type0 === "ge" ? [v.value0] : []), ...(v.type1 === "ge" ? [v.value1] : [])];
    state.search.gtAlarmCount = [...(v.type0 === "gt" ? [v.value0] : []), ...(v.type1 === "gt" ? [v.value1] : [])];
    state.search.leAlarmCount = [...(v.type0 === "le" ? [v.value0] : []), ...(v.type1 === "le" ? [v.value1] : [])];
    state.search.ltAlarmCount = [...(v.type0 === "lt" ? [v.value0] : []), ...(v.type1 === "lt" ? [v.value1] : [])];
    state.search.isNullAlarmCount = [...(v.type0 === "isNull" ? [v.value0] : []), ...(v.type1 === "isNull" ? [v.value1] : [])];
    state.search.isNotNullAlarmCount = [...(v.type0 === "isNotNull" ? [v.value0] : []), ...(v.type1 === "isNotNull" ? [v.value1] : [])];
  },
});
const searchType0ByActorName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByActorName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByActorName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByActorName) === "include") value0 = state.search.includeActorName[0] || "";
    if (toValue(searchType0ByActorName) === "exclude") value0 = state.search.excludeActorName[0] || "";
    if (toValue(searchType0ByActorName) === "eq") value0 = state.search.eqActorName[0] || "";
    if (toValue(searchType0ByActorName) === "ne") value0 = state.search.neActorName[0] || "";
    let value1 = "";
    if (toValue(searchType0ByActorName) === "include") value1 = state.search.includeActorName[state.search.includeActorName.length - 1] || "";
    if (toValue(searchType0ByActorName) === "exclude") value1 = state.search.excludeActorName[state.search.excludeActorName.length - 1] || "";
    if (toValue(searchType0ByActorName) === "eq") value1 = state.search.eqActorName[state.search.eqActorName.length - 1] || "";
    if (toValue(searchType0ByActorName) === "ne") value1 = state.search.neActorName[state.search.neActorName.length - 1] || "";
    return {
      type0: toValue(searchType0ByActorName),
      type1: toValue(searchType1ByActorName),
      relation: state.search.actorNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByActorName.value = v.type0 as typeof searchType0ByActorName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByActorName.value = v.type1 as typeof searchType1ByActorName extends import("vue").Ref<infer T> ? T : string;
    state.search.actorNameFilterRelation = v.relation;
    state.search.includeActorName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeActorName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqActorName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neActorName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByResponsibleName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByResponsibleName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByResponsibleName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByResponsibleName) === "include") value0 = state.search.includeResponsibleName[0] || "";
    if (toValue(searchType0ByResponsibleName) === "exclude") value0 = state.search.excludeResponsibleName[0] || "";
    if (toValue(searchType0ByResponsibleName) === "eq") value0 = state.search.eqResponsibleName[0] || "";
    if (toValue(searchType0ByResponsibleName) === "ne") value0 = state.search.neResponsibleName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByResponsibleName) === "include") value1 = state.search.includeResponsibleName[state.search.includeResponsibleName.length - 1] || "";
    if (toValue(searchType1ByResponsibleName) === "exclude") value1 = state.search.excludeResponsibleName[state.search.excludeResponsibleName.length - 1] || "";
    if (toValue(searchType1ByResponsibleName) === "eq") value1 = state.search.eqResponsibleName[state.search.eqResponsibleName.length - 1] || "";
    if (toValue(searchType1ByResponsibleName) === "ne") value1 = state.search.neResponsibleName[state.search.neResponsibleName.length - 1] || "";
    return {
      type0: toValue(searchType0ByResponsibleName),
      type1: toValue(searchType1ByResponsibleName),
      relation: state.search.responsibleNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByResponsibleName.value = v.type0 as typeof searchType0ByResponsibleName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByResponsibleName.value = v.type1 as typeof searchType1ByResponsibleName extends import("vue").Ref<infer T> ? T : string;
    state.search.responsibleNameFilterRelation = v.relation;
    state.search.includeResponsibleName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeResponsibleName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqResponsibleName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neResponsibleName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByOrderSummary = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByOrderSummary = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByOrderSummary = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByOrderSummary) === "include") value0 = state.search.includeOrderSummary[0] || "";
    if (toValue(searchType0ByOrderSummary) === "exclude") value0 = state.search.excludeOrderSummary[0] || "";
    if (toValue(searchType0ByOrderSummary) === "eq") value0 = state.search.eqOrderSummary[0] || "";
    if (toValue(searchType0ByOrderSummary) === "ne") value0 = state.search.neOrderSummary[0] || "";
    let value1 = "";
    if (toValue(searchType1ByOrderSummary) === "include") value1 = state.search.includeOrderSummary[state.search.includeOrderSummary.length - 1] || "";
    if (toValue(searchType1ByOrderSummary) === "exclude") value1 = state.search.excludeOrderSummary[state.search.excludeOrderSummary.length - 1] || "";
    if (toValue(searchType1ByOrderSummary) === "eq") value1 = state.search.eqOrderSummary[state.search.eqOrderSummary.length - 1] || "";
    if (toValue(searchType1ByOrderSummary) === "ne") value1 = state.search.neOrderSummary[state.search.neOrderSummary.length - 1] || "";
    return {
      type0: toValue(searchType0ByOrderSummary),
      type1: toValue(searchType1ByOrderSummary),
      relation: state.search.orderSummaryFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByOrderSummary.value = v.type0 as typeof searchType0ByOrderSummary extends import("vue").Ref<infer T> ? T : string;
    searchType1ByOrderSummary.value = v.type1 as typeof searchType1ByOrderSummary extends import("vue").Ref<infer T> ? T : string;
    state.search.orderSummaryFilterRelation = v.relation;
    state.search.includeOrderSummary = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeOrderSummary = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqOrderSummary = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neOrderSummary = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByUserGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByUserGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByUserGroupName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByUserGroupName) === "include") value0 = state.search.inUserGroupName[0] || "";
    if (toValue(searchType0ByUserGroupName) === "exclude") value0 = state.search.excludeUserGroupName[0] || "";
    if (toValue(searchType0ByUserGroupName) === "eq") value0 = state.search.eqUserGroupName[0] || "";
    if (toValue(searchType0ByUserGroupName) === "ne") value0 = state.search.neUserGroupName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByUserGroupName) === "include") value1 = state.search.inUserGroupName[state.search.inUserGroupName.length - 1] || "";
    if (toValue(searchType1ByUserGroupName) === "exclude") value1 = state.search.excludeUserGroupName[state.search.excludeUserGroupName.length - 1] || "";
    if (toValue(searchType1ByUserGroupName) === "eq") value1 = state.search.eqUserGroupName[state.search.eqUserGroupName.length - 1] || "";
    if (toValue(searchType1ByUserGroupName) === "ne") value1 = state.search.neUserGroupName[state.search.neUserGroupName.length - 1] || "";
    return {
      type0: toValue(searchType0ByUserGroupName),
      type1: toValue(searchType1ByUserGroupName),
      relation: state.search.userGroupNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByUserGroupName.value = v.type0 as typeof searchType0ByUserGroupName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByUserGroupName.value = v.type1 as typeof searchType1ByUserGroupName extends import("vue").Ref<infer T> ? T : string;
    state.search.userGroupNameFilterRelation = v.relation;
    state.search.inUserGroupName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeUserGroupName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqUserGroupName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neUserGroupName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByTicketGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByTicketGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByTicketGroupName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByTicketGroupName) === "include") value0 = state.search.inTicketGroupName[0] || "";
    if (toValue(searchType0ByTicketGroupName) === "exclude") value0 = state.search.excludeTicketGroupName[0] || "";
    if (toValue(searchType0ByTicketGroupName) === "eq") value0 = state.search.eqTicketGroupName[0] || "";
    if (toValue(searchType0ByTicketGroupName) === "ne") value0 = state.search.neTicketGroupName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByTicketGroupName) === "include") value1 = state.search.inTicketGroupName[state.search.inTicketGroupName.length - 1] || "";
    if (toValue(searchType1ByTicketGroupName) === "exclude") value1 = state.search.excludeTicketGroupName[state.search.excludeTicketGroupName.length - 1] || "";
    if (toValue(searchType1ByTicketGroupName) === "eq") value1 = state.search.eqTicketGroupName[state.search.eqTicketGroupName.length - 1] || "";
    if (toValue(searchType1ByTicketGroupName) === "ne") value1 = state.search.neTicketGroupName[state.search.neTicketGroupName.length - 1] || "";
    return {
      type0: toValue(searchType0ByTicketGroupName),
      type1: toValue(searchType1ByTicketGroupName),
      relation: state.search.ticketGroupNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByTicketGroupName.value = v.type0 as typeof searchType0ByTicketGroupName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByTicketGroupName.value = v.type1 as typeof searchType1ByTicketGroupName extends import("vue").Ref<infer T> ? T : string;
    state.search.ticketGroupNameFilterRelation = v.relation;
    state.search.inTicketGroupName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeTicketGroupName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqTicketGroupName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neTicketGroupName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
function handleExpand(row: DataItem, expandedRows: DataItem[]) {
  state.expand = expandedRows.filter((v) => v).map(({ id }) => id);
  if (find(expandedRows, ({ id }) => row.id === id)) {
    /*  */
  } else {
    /*  */
  }
}
function handleSort(sort: { prop: string; order: "ascending" | "descending" }) {
  state.sort = sort.prop ? sort : undefined;
}
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
async function resetData() {
  state.list = [];
  state.page = 1;
  state.size = 50;
  state.total = 0;
  await nextTick();
}
function timeZoneSwitching(): number {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == userInfo.zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}

const timer = ref<number | null>(null);
const autoRefreshTime = ref(0);

const eventStat = ref<{ label: string; value: eventState; color?: string; count: number }[]>([]);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  handleRefresh().then(() => (autoRefreshTime.value = 60));
}
function beforeUpdate() {}
function updated() {}
function activated() {}

const checkAll = ref(false);

const isIndeterminate = ref(false);
const chooseIdList = ref([]);
const chooseEventList = ref([]);
const handleCheckAllChange = (val: boolean) => {
  // checkedCities.value = val ? cities : []
  // console.log(val);
  let list = [];
  let ids = [];
  if (val) {
    state.select = state.list.filter((v) => v.eventState !== eventState.CLOSED && v.eventState !== eventState.AUTO_CLOSED).map((v) => v.id);
    state.list.forEach((v, i) => {
      if (v.eventState !== eventState.CLOSED && v.eventState !== eventState.AUTO_CLOSED) {
        v.checkbox = true;
        ids.push(v.id);
        list.push(v);
      }
    });
  } else {
    state.select = [];
    state.list.forEach((v, i) => {
      v.checkbox = false;
    });
    ids = [];
    list = [];
  }
  chooseEventList.value = [...list];

  chooseIdList.value = Array.from(new Set(ids));
  isIndeterminate.value = false;
};

function chooseTable() {
  let ids = [];
  let list = [];
  // console.log(state.list);
  state.select = state.list.filter((v) => v.eventState !== eventState.CLOSED && v.eventState !== eventState.AUTO_CLOSED && v.checkbox).map((v) => v.id);
  state.list.forEach((v, i) => {
    if (v.checkbox) {
      ids.push(v.id);
      list.push(v);
    }
  });
  chooseEventList.value = [...list];

  chooseIdList.value = Array.from(new Set(ids));
  const allcheck = state.list.every((obj) => obj.checkbox === true);

  isIndeterminate.value = allcheck ? false : true;
}

async function beforeCloseInnerDialog(done) {
  showCancel.value = true;
  showStartBtn.value = true;
  closeMessage.value = [];
  await nextTick();
  handleCommand(command.Refresh);
  if (done instanceof Function) done();
  else innerVisible.value = false;
}

const endRef = ref<InstanceType<typeof EventEnd>>();

const innerVisible = ref(false);
async function handleEnd(data: any, type: string) {
  if (!endRef.value) return false;
  // console.log(type, data, 5555);
  // if (type === "ConfirmAndFinish") {
  //   operation.value = "FINISHED";
  // }
  endRef.value.open(data, type);
}
const completeInfo = ref({});
const eventCreateNoteRef = ref<InstanceType<typeof EventCreateNote>>();
async function closeEvent(data) {
  // console.log(data);
  completeInfo.value = { ...data.params.completeInfo };
  innerVisible.value = true;
}
const showCancel = ref(true);
let source: any = null;
const closeMessage = ref([]);
async function startPromise() {
  closeMessage.value = [];
  let index = 0;
  source = axios.CancelToken.source();

  for (let i = 0; i < chooseIdList.value.length; i++) {
    closeMessage.value.push();
    await batchCloseDictEvent({
      orderIds: [chooseIdList.value[i]],
      completeInfo: completeInfo.value,
    })
      .then((res) => {
        if (res.success && res.message != "") {
          ///
          closeMessage.value[i] = "已关闭";
          index = index + 1;
        }
      })
      .catch((err) => {
        if (axios.isCancel(err)) {
          ///
        } else {
          closeMessage.value[i] = err.message;
          index = index + 1;
        }
      });
  }
  if (index === chooseIdList.value.length) {
    showCancel.value = false;
  }
}

function ruoterOrder(val) {
  const routeData = router.resolve({ name: "689693383307821056", params: { id: val }, query: { fallback: route.name as string } }, { target: "_blank" });

  window.open(routeData.href, val);
}
const showStartBtn = ref(true);
function cancelPromise() {
  source && source.cancel();
  axiosCancel();
  showStartBtn.value = false;
}

function handelAddNotes() {
  eventCreateNoteRef.value.orderIds = chooseIdList.value;
  eventCreateNoteRef.value.open();
}

function deactivated() {
  if (timer.value !== null) {
    window.clearInterval(timer.value);
    timer.value = null;
  }
}

function beforeDestroy() {
  if (timer.value !== null) {
    window.clearInterval(timer.value);
    timer.value = null;
  }
}
function destroyed() {}

watch(autoRefreshTime, (autoRefreshTime) => {
  if (timer.value !== null) timer.value = (window.clearInterval(timer.value), null);
  if (autoRefreshTime) timer.value = window.setInterval(queryData, autoRefreshTime * 1000);
});
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
async function handleCommand(type: command, data?: Record<string, unknown>) {
  const time = autoRefreshTime.value;
  autoRefreshTime.value = 0;
  try {
    state.loading = true;
    await nextTick();
    switch (type) {
      case command.Refresh:
        await resetData();
        await queryData();
        break;
      case command.Request:
        await queryData();
        break;
      case command.Preview:
        await previewItem(data as Record<string, unknown>);
        break;
      case command.Create:
        await createItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
      case command.Update:
        await rewriteItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
      case command.Modify:
        await modifyItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
      case command.Delete:
        await deleteItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
      case command.Approve:
        await approveItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
    }
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await resetData();
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
      await queryData();
    }
  } finally {
    autoRefreshTime.value = time;
    state.loading = false;
  }
}
async function handleRefresh() {
  try {
    state.loading = true;
    await nextTick();
    await resetData();
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
async function handleQuery() {
  try {
    state.loading = true;
    await nextTick();
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
async function queryData() {
  try {
    let sort: string[] = [];

    switch ((state.sort || {}).order) {
      case "ascending":
        sort.push(`${String(state.sort?.prop)},asc`);
        if (state.sort?.prop === "createTime") {
          sort.shift();

          sort.push(`createdTime,asc`);
          // delete sort.createTime;
        } else if (state.sort?.prop === "updateTime") {
          sort.shift();

          sort.push(`updatedTime,asc`);
          // delete sort.updateTime;
        }
        break;
      case "descending":
        sort.push(`${String(state.sort?.prop)},desc`);
        if (state.sort?.prop === "createTime") {
          sort.shift();

          sort.push(`createdTime,desc`);
          // delete sort.createTime;
        } else if (state.sort?.prop === "updateTime") {
          sort.shift();

          sort.push(`updatedTime,desc`);
          // delete sort.updateTime;
        }
        break;
    }

    const [{ success, message, data, page, size, total }, { success: statSuccess, message: statMessage, data: statData }] = await Promise.all([getList({ ...state.search, type: "list", sort, tenantId: userInfo.currentTenantId, boardOrNot: false, paging: { pageNumber: state.page, pageSize: state.size } }), getDictEventCount({ type: "list", boardOrNot: false })]);
    if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
    if (!statSuccess) throw Object.assign(new Error(statMessage), { success: statSuccess, data: statData });

    eventStat.value = statData.map((v): { label: string; value: eventState; color?: string; count: number } => {
      const typeItem = find(eventStateOption, ({ value }) => v.eventState === value) || { label: v.eventState as string, value: v.eventState };

      return { count: Number(v.eventCount) || 0, label: typeItem.label, value: typeItem.value as eventState };
    });

    const select = new Set((data instanceof Array ? data : []).filter((v) => state.select.includes(v.id)).map((v) => v.id));

    // state.list.splice(0, state.list.length, ...(data instanceof Array ? data : []).map((v) => Object.assign(v, { checkbox: select.has(v.id) })));
    state.list.splice(0, state.list.length, ...(data instanceof Array ? data : []).map((v) => Object.assign(v, { updateTime: Number(v.updateTime) + timeZoneSwitching() }, { createTime: Number(v.createTime) + timeZoneSwitching() }, { checkbox: select.has(v.id) })));

    state.page = Number(page) || 1;
    state.size = Number(size) || 20;
    state.total = Number(total) || 0;

    await nextTick();
    if (tableRef.value) {
      tableRef.value.clearSelection();
      for (let i = 0; i < state.list.length; i++) {
        tableRef.value.toggleRowSelection(state.list[i], select.has(state.list[i].id));
      }
    }
    state.select = Array.from(select);
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  }
}
async function createItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    if (form.ticketClassificationId) {
      const ticketClassificationArrId = (form.ticketClassificationId as string).split(",");
      form.ticketClassificationId = ticketClassificationArrId[ticketClassificationArrId.length - 1];
    }
    const { success, message, data } = await addItemData({ ...form, orderType: "EVENT_ORDER" });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`成功${t("glob.add")}事件`);
    router.push({ name: "689693383307821056", params: { id: data.id }, query: { fallback: route.name as string, tenant: data.tenantId } });
  });
}
async function previewItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await setItemData({ ids: form.ids as string[], ...form });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${t("axios.Operation successful")}`);
  });
}
async function rewriteItem(row: Record<string, unknown>) {
  const title = "P8";
  if (!editorRef.value) return row;
  const params = chooseEventList.value;

  await editorRef.value.confirm({ ...params, select: params, $type: "warning", $title: `批量${title}`, $slot: "batchConfirm" }, async (form: Record<string, unknown>) => {
    const { success, message, data } = await setEventDataByPriority({ id: (<DataItem[]>form.select).map((v) => v.id), priority: "P8" });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`成功${form.$title}`);
    await queryData();
    checkAll.value = false;
  });
}
async function modifyItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  const params = { select: (<DataItem[]>row.select).filter((v) => v.eventState === eventState.UNASSIGNED) };
  const form = { type: "userGroupId", id: "" };
  await editorRef.value.confirm({ ...row, ...form, ...params, $type: "warning", $title: `批量指派`, $slot: "batchAssign" }, async (form: Record<string, unknown>) => {
    const { success, message, data } = await setEventDataByAssign({ id: (<DataItem[]>form.select).map((v) => v.id), userGroupId: form.userGroupId as string, userId: form.userId as string });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`成功${form.$title}`);
  });
}
async function deleteItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await delItemData(form);
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${t("axios.Operation successful")}`);
  });
}

const approveRef = ref<InstanceType<typeof approve>>();
async function approveItem(row: Record<string, unknown>) {
  if (!approveRef.value) return false;
  const select: Record<string, unknown>[] = state.list.map((v) => ({ ...v, suspendRecord: first((v.suspendRecords instanceof Array ? v.suspendRecords : []).sort((a, b) => Number(b.serial) - Number(a.serial))) })).filter((v) => v.suspendRecords && state.select.includes(v.id) && v.eventState === eventState.PENDING_APPROVAL);
  for (let i = 0; i < select.length; i++) {
    const params: Record<string, unknown> = { next: i === select.length - 1, ...(<Record<string, unknown>>select[i].suspendRecord), eventName: select[i].summary, eventId: select[i].id };
    try {
      await approveRef.value.open(params, async (form) => {
        const { success, data, message } = await dictEventApproveHangUp({ id: <string>select[i].id, approve: <boolean>form.approve });
        if (!success) throw new Error(message);
        ElMessage.success("操作成功");
      });
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve));
    } catch (error) {
      await nextTick();
      await new Promise((resolve) => setTimeout(resolve));
      break;
    }
  }
}

function checkSelectSet(row, index) {
  //row 就是每行的数据
  // console.log(row);
  if ([eventState.CLOSED, eventState.AUTO_CLOSED].includes(row.eventState || ("" as eventState))) {
    return false;
  } else {
    return true;
  }
}

function copyClick(row, column, cell, event) {
  event.stopPropagation();
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: DataItem): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.close-event {
  width: 100%;
  height: 400px;
  border: 1px solid #ccc;
  padding: 10px;
  box-sizing: border-box;
  display: flex;
  :deep(.close-event-scroll) {
    display: flex;
    width: 100%;
    .el-scrollbar__wrap {
      width: 100%;
    }
    .el-scrollbar__view {
      display: flex;
      justify-content: space-between;
      > .close-event-list {
        flex: 1;
        h3 {
          font-weight: 700;
          font-size: 18px;
        }
        ul > li {
          height: 35px;
          line-height: 35px;
        }
      }
      > .close-event-list:last-child {
        text-align: right;
      }
    }
  }
}
</style>
