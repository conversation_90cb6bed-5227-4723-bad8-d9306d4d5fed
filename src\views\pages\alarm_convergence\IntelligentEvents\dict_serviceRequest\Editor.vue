<template>
  <!-- 对话框表单 -->
  <component :is="ComponentRender" v-model:visible="data.visible" :handle-cancel="handleCancel">
    <template #header>
      {{ `${$params.id ? t("glob.edit") : t("glob.add")}${props.title}` }}
    </template>
    <template #default="{}">
      <!-- width -->
      <FormModel ref="formRef" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @submit="handleFinish">
        <FormItem v-if="orderSubclasslist instanceof Array && orderSubclasslist.length" :span="width > 675 ? 24 : 24" :label="`${props.title}子类型`" tooltip="" prop="ticketTemplateId" :rules="[{ required: true, message: '请选择子类型', trigger: 'bulr' }]">
          <el-select v-model="form.ticketTemplateId" placeholder="请选择子类型" clearable filterable class="tw-w-full" @change="handleSubtypeChange">
            <el-option v-for="item in orderSubclasslist" :key="item.id" :label="item.orderSubclass" :value="item.id"> </el-option>
          </el-select>
        </FormItem>
        <FormItem :span="24" :label="`项目名称`" tooltip="" prop="projectId" :rules="[{ required: true, message: '请选择项目名称', trigger: ['change', 'blur'] }]">
          <el-select v-model="form.projectId" placeholder="请选择项目名称" filterable class="tw-w-full">
            <!-- @change="(v) => handleGetSlaByProject((projects.find((f) => f.id === v) || {}).slaId)" -->
            <el-option class="tw-h-[auto]" v-for="item in projects" :key="item.value" :label="item.projectName" :value="item.id">
              <div>
                <p class="tw-text-base tw-font-semibold tw-leading-8">{{ item.projectName }}</p>
                <p class="tw-text-sm tw-leading-7">统一服务编号：{{ item.uniformServiceCode }}</p>
                <p class="tw-text-sm tw-leading-7">项目编号：{{ item.projectCode }}</p>
              </div>
            </el-option>
          </el-select>
        </FormItem>
        <FormItem :span="24" :label="`${props.title}名称`" tooltip="" prop="title" :rules="[buildValidatorData({ name: 'required', title: `${props.title}名称` })]">
          <el-input v-model="form.title" type="text" :placeholder="`请输入${props.title}名称`" clearable></el-input>
        </FormItem>
        <FormItem :span="24" :label="`${props.title}描述`" tooltip="" prop="description" :rules="[]">
          <!-- <div class="tw-flex tw-min-h-[250px] tw-flex-col" @keyup.enter.stop>
            <QuillEditor theme="snow" style="flex: 1" v-model:content="form.description" contentType="html" toolbar="full"></QuillEditor>
          </div> -->
          <el-input v-model="form.description" :autosize="{ minRows: 4, maxRows: 6 }" type="textarea" :placeholder="`请输入${props.title}描述`" />
        </FormItem>
        <!-- <FormItem :span="width > 675 ? 24 : 24" :label="`${props.title}设备`" tooltip="" prop="deviceId" :rules="[]">
          <el-input v-model="form.deviceId" type="text" placeholder="" clearable></el-input>
        </FormItem> -->

        <FormItem :span="24" :label="`外部工单号`" tooltip="" prop="serviceCode" :rules="[{ required: true, message: '请输入外部工单号', trigger: ['change', 'blur'] }]">
          <el-input v-model="form.serviceCode" placeholder="请输入外部工单号"></el-input>
        </FormItem>

        <FormItem :span="24" :label="`优先级`" tooltip="" prop="priority" :rules="[buildValidatorData({ name: 'required', title: `优先级` })]">
          <el-select v-model="form.priority" placeholder="" clearable filterable class="tw-w-full">
            <!-- <el-option v-for="item in priorityOption" :key="item.value" :label="`${item.label}${item.desc ? '(' + item.desc + ')' : ''}`" :value="item.value"> </el-option> -->
            <el-option v-for="item in priorityList" :key="item.value" :label="`${item.label}`" :value="item.value"> </el-option>
          </el-select>
        </FormItem>

        <FormItem
          :span="24"
          :label="`期望响应时间`"
          tooltip=""
          prop="expectStartTime"
          :rules="[
            {
              required: true,
              validator: (rule: any, value: any, callback: any) => {
                if (!form.expectStartTime) return callback(new Error(`请选择期望响应时间`));
                if (Number(form.expectStartTime) < new Date().getTime() + 1000 * 60) return callback(new Error(`请选择${moment(new Date().getTime() + 1000 * 60).format('YYYY-MM-DD HH:mm:ss')}之后的时间`));
                callback();
              },
              trigger: ['change', 'blur'],
            },
          ]"
        >
          <el-date-picker v-model="form.expectStartTime" type="datetime" value-format="x" placeholder="请选择期望响应时间" :disabledDate="(time) => time.getTime() < Date.now() - 8.64e7" />
        </FormItem>

        <FormItem
          :span="24"
          :label="`期望完成时间`"
          tooltip=""
          prop="expectEndTime"
          :rules="[
            {
              required: true,
              validator: (rule: any, value: any, callback: any) => {
                if (!form.expectEndTime) return callback(new Error(`请选择期望完成时间`));
                if (Number(form.expectEndTime) < new Date().getTime() + 1000 * 60) return callback(new Error(`请选择${moment(new Date().getTime() + 1000 * 60).format('YYYY-MM-DD HH:mm:ss')}之后的时间`));
                callback();
              },
              trigger: ['change', 'blur'],
            },
          ]"
        >
          <el-date-picker v-model="form.expectEndTime" type="datetime" value-format="x" placeholder="请选择期望完成时间" :disabledDate="(time) => time.getTime() < Date.now() - 8.64e7" />
        </FormItem>

        <FormItem v-show="isShowTicketClassification" :span="24" :label="`分类`" tooltip="" prop="ticketClassificationId" :rules="[]">
          <orderType v-if="data.visible" ref="orderTypeRef" v-model:ticketClassificationId="form.ticketClassificationId" type="dictservice" :ticketTemplateId="form.ticketTemplateId"></orderType>
        </FormItem>
        <!-- <FormItem :span="24" :label="`请求起止时间`" tooltip="" prop="time" :rules="[]">
          <VueCtkDateTimePicker v-model="form.time" :locale="locale" :dark="config.layout.isDark" inline :range="true" :custom-shortcuts="customShortcuts" no-keyboard format="YYYY-MM-DD HH:mm" output-format="x" :min-date="moment().format(props.format)" color="var(--el-color-primary)"></VueCtkDateTimePicker>
        </FormItem> -->
      </FormModel>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </component>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, computed, h, renderSlot, getCurrentInstance, createVNode, shallowRef } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { buildTypeHelper } from "@/utils/type";
import { useConfig } from "@/stores/config";
import moment from "moment";

import { buildValidatorData } from "@/utils/validate";

import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import VueCtkDateTimePicker from "@/components/VueCtkDateTimePicker/index.vue";

import { QuillEditor } from "@vueup/vue-quill";

import getUserInfo from "@/utils/getUserInfo";

import { priority, priorityOption as priorityList } from "@/views/pages/apis/event";

import { getProjectsByEventId /* getAllUniformServiceCodes */ } from "@/views/pages/apis/projectManage";

import { 配置管理中心_项目管理_可读 } from "@/views/pages/permission";

import { DetailSlaConfig } from "@/views/pages/apis/SlaConfig";

import { getAllticketTemplates } from "@/views/pages/apis/ticketTemplate";

import orderType from "@/views/pages/alarm_convergence/IntelligentEvents/eventManage/orderType.vue";

// import { getAllProjectPlans } from "@/views/pages/apis/projectPlan";

const userInfo = getUserInfo();
const config = useConfig();
const { t, locale } = useI18n({ useScope: "global" });
const formRef = ref<InstanceType<typeof FormModel>>();
const ctx = getCurrentInstance();
if (!ctx) throw new Error("Component context initialization failed!");
const { appContext } = ctx;
const orderSubclasslist = ref<any>([]);

const orderTypeRef = ref<InstanceType<typeof orderType>>();
const isShowTicketClassification = computed(() => orderTypeRef.value && shallowRef(orderTypeRef.value.options).value instanceof Array && shallowRef(orderTypeRef.value.options).value.length);

interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 120,
});

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Item {
  title: string /* 标题 */;
  description: string /* 服务请求描述 */;
  // deviceId: string[] /* 设备id */;
  priority: string /* 优先级 */;

  projectId: string;

  // projectName: string;
  // projectCode: string;
  // uniformServiceCode: string;
  // projectLevel: string;
  // range: { start: string; end: string };
  serviceCode: string;

  expectStartTime: string;
  expectEndTime: string;

  ticketSubtype: string /* 子类型 */;
  ticketTemplateId: string /* 模板id */;

  ticketClassificationId: string;
  // time: { start: string; end: string } /* 请求起止时间 */;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  title: buildTypeHelper<Required<Item>["title"]>(""),
  description: buildTypeHelper<Required<Item>["description"]>(""),
  // deviceId: buildTypeHelper<Required<Item>["deviceId"]>(""),
  priority: buildTypeHelper<Required<Item>["priority"]>(priority.P7, new RegExp(`^${Object.keys(priority).join("|")}$`, "g")),
  // time: buildTypeHelper<Required<Item>["time"]>({ start: "", end: "" }),
  projectId: buildTypeHelper<Required<Item>["projectId"]>(""),

  // projectName: buildTypeHelper<Required<Item>["projectName"]>(""),
  // projectCode: buildTypeHelper<Required<Item>["projectCode"]>(""),
  // uniformServiceCode: buildTypeHelper<Required<Item>["uniformServiceCode"]>(""),
  // projectLevel: buildTypeHelper<Required<Item>["projectLevel"]>(""),
  // range: buildTypeHelper<Required<Item>["range"]>({} as Item["range"]),
  serviceCode: buildTypeHelper<Required<Item>["serviceCode"]>(""),
  expectStartTime: buildTypeHelper<Required<Item>["expectStartTime"]>(""),
  expectEndTime: buildTypeHelper<Required<Item>["expectEndTime"]>(""),

  ticketSubtype: buildTypeHelper<Required<Item>["ticketSubtype"]>(""),
  ticketTemplateId: buildTypeHelper<Required<Item>["ticketTemplateId"]>(""),

  ticketClassificationId: buildTypeHelper<Required<Item>["ticketClassificationId"]>(""),
});

const customShortcuts = ref<{ key: string; label: string; value: "day" | "-day" | "isoWeek" | "-isoWeek" | "quarter" | "month" | "-month" | "year" | "-year" | "week" | "-week" | number }[]>([
  { key: "thisWeek", label: "本周", value: "isoWeek" },
  { key: "lastWeek", label: "上周", value: "-isoWeek" },
  { key: "last7Days", label: "七天", value: 7 },
  { key: "last30Days", label: "三十天", value: 30 },
  { key: "thisMonth", label: "本月", value: "month" },
  { key: "lastMonth", label: "上个月", value: "-month" },
  { key: "thisYear", label: "今年", value: "year" },
  { key: "lastYear", label: "去年", value: "-year" },
]);
async function getorderSub() {
  const params = {
    pageNumber: 1,
    pageSize: 9999,
    containerId: userInfo?.currentTenant?.containerId,
    queryPermissionId: "733532355297280000",
    verifyPermissionIds: "733532411236712448,733532436834549760,733532469994717184",
  };
  try {
    const { data, message, success, total } = await getAllticketTemplates({ ...params });
    if (!success) throw new Error(message);
    orderSubclasslist.value = data.filter((item) => {
      return item.ticketType === "dictservice";
    });

    if (orderSubclasslist.value.length === 1) {
      form.value.ticketTemplateId = orderSubclasslist.value[0].id;
    }
    console.log(orderSubclasslist.value, "0000000000000000000");
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

const handleSubtypeChange = (val: string) => {
  const selected = orderSubclasslist.value.find((item) => item.id === val);
  if (selected) {
    // form.value.ticketTemplateId = selected.id;
    form.value.ticketSubtype = selected.orderSubclass;
  }
};

const projects = ref<Record<string, any>[]>([]);

async function handleGetProject() {
  try {
    const { data, success, message } = await getProjectsByEventId({ id: 0 });
    if (!success) throw new Error(message);
    projects.value = data;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

const priorityOption = ref<Record<string, any>>([]);

async function handleGetSlaByProject(slaId) {
  try {
    priorityOption.value = priorityList.map((v) => ({ ...v, desc: "" }));
    if (!slaId) return;
    const { data, message, success } = await DetailSlaConfig({ ruleId: slaId, tenantId: userInfo.currentTenantId });
    if (!success) throw new Error(message);
    priorityOption.value = priorityList.map((v) => {
      const desc = {
        resp: "",
        resolve: "",
      };

      // if (v.value !== priority.P8) {
      const currentResp = (data.serviceRespTimeLevels.find((f) => f.priority === v.value) || {}).resolves;
      const respDHS = (currentResp || []).map((m) => Number((m.day || 0) * 24 * 60) + Number((m.hour || 0) * 60) + Number(m.minute || 0)) || []; // 时分秒拼接算最大值

      if (respDHS.length) {
        const res = currentResp[respDHS.indexOf(Math.max(...respDHS))];
        desc.resp = `${res.day ? res.day + "day" : ""}${res.hour ? res.hour + "h" : ""}${res.minute ? res.minute + "min" : ""}`;
      }

      const currentResolve = (data.serviceResolveTimeLevels.find((f) => f.priority === v.value) || {}).resolves;
      const resolvepDHS = (currentResolve || []).map((m) => Number((m.day || 0) * 24 * 60) + Number((m.hour || 0) * 60) + Number(m.minute || 0)) || []; // 时分秒拼接算最大值

      if (resolvepDHS.length) {
        const res = currentResolve[resolvepDHS.indexOf(Math.max(...resolvepDHS))];
        desc.resolve = `${res.day ? res.day + "day" : ""}${res.hour ? res.hour + "h" : ""}${res.minute ? res.minute + "min" : ""}`;
      }
      // } else {
      //   const respDHS = (data.serviceRespDefaultResolves || []).map((m) => Number((m.day || 0) * 24 * 60) + Number((m.hour || 0) * 60) + Number(m.minute || 0)) || []; // 时分秒拼接算最大值
      //   if (respDHS.length) {
      //     const res = data.serviceRespDefaultResolves[respDHS.indexOf(Math.max(...respDHS))];
      //     desc.resp = `${res.day ? res.day + "day" : ""}${res.hour ? res.hour + "h" : ""}${res.minute ? res.minute + "min" : ""}`;
      //   }

      //   const resolvepDHS = (data.serviceResolveDefaultResolves || []).map((m) => Number((m.day || 0) * 24 * 60) + Number((m.hour || 0) * 60) + Number(m.minute || 0)) || []; // 时分秒拼接算最大值

      //   if (resolvepDHS.length) {
      //     const res = data.serviceResolveDefaultResolves[resolvepDHS.indexOf(Math.max(...resolvepDHS))];
      //     desc.resolve = `${res.day ? res.day + "day" : ""}${res.hour ? res.hour + "h" : ""}${res.minute ? res.minute + "min" : ""}`;
      //   }
      // }

      return { ...v, desc: `${desc.resp ? "响应时限:" + desc.resp + ";" : ""}${desc.resolve ? "处理时限:" + desc.resolve + ";" : ""}` };
    });
  } catch (error) {
    /* ...error */
  }
}
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  if (userInfo.hasPermission(配置管理中心_项目管理_可读)) handleGetProject();
  await handleGetSlaByProject(undefined);
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  return form;
}

// function handleProjectChange(key: string, v: string) {
//   const currentProject: Record<string, any> = projects.value.find((f) => f[key] === v) || {};
//   form.value.projectId = currentProject.id;
//   form.value.projectName = currentProject.projectName;
//   form.value.projectCode = currentProject.projectCode;
//   form.value.uniformServiceCode = currentProject.uniformServiceCode;
//   form.value.projectLevel = currentProject.projectLevel;
//   form.value.range = currentProject.range;
// }

/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 窗口方法
 */
interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<Item>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Record<string, unknown>) => void;
  reject?: (value: Error) => void;
  callback?: (form: Record<string, unknown>) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Record<string, unknown>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(function (formResult, [key, util]) {
    if (!util) return formResult;
    else if (util.test(formResult[key])) return formResult;
    else return Object.assign(formResult, { [key]: util.transfer(formResult[key], util.value as never) });
  }, form as Required<Item>);
}

async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => (formRef.value ? formRef.value.validate(resolve) : resolve(false)));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();

defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, callback?: (form: Record<string, unknown>) => Promise<void>): Promise<unknown> {
    await getorderSub();
    if (form.value.ticketTemplateId) {
      params.ticketTemplateId = form.value.ticketTemplateId;
    }
    if (data.visible) handleCancel();
    const wait = new Promise<Record<string, unknown>>((resolve, reject) => ((data.resolve = resolve), (data.reject = reject)));
    $params.value = cloneDeep(params);
    data.visible = true;
    data.loading = true;
    data.submitLoading = true;
    data.callback = callback;
    await nextTick();
    try {
      await runningInit($params.value);
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      handleCancel();
    }
    handleReset();
    data.loading = false;
    data.submitLoading = false;

    try {
      return await wait;
    } catch (error) {
      return error;
    }
  },
  async confirm<T extends { $title: string; $type: "" | "success" | "warning" | "info" | "error"; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      await ElMessageBox.confirm(
        createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: ElMessageBox.close }) }),
        params.$title,
        {
          // type: params.$type,
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        },
        appContext
      );
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async alert<T extends { $title: string; $type: "" | "success" | "warning" | "info" | "error"; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      await ElMessageBox.alert(
        createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: ElMessageBox.close }) }),
        // params.$title,
        {
          type: params.$type,
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        },
        appContext
      );
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
});
</script>

<style scoped lang="scss">
.edit_sla_config {
  :deep() {
    .el-input-number {
      width: 50px !important;

      .elstyle-input {
        width: 50px;

        input {
          padding: 0;
        }
      }
    }

    .el-input-number__increase {
      display: none;
    }

    .el-input-number__decrease {
      display: none;
    }

    .el-card {
      margin-top: 20px;
    }

    .el-form-item__content .el-form-item-content {
      display: flex;
      flex-direction: column;
    }

    .el-table .cell {
      padding: 0 !important;
    }

    .el-table .el-table__cell {
      padding: 0 !important;
      height: 50px;
    }
  }
}
.priority-icon {
  width: 10px;
  height: 10px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 10px;
}
.state {
  padding: 2px 10px;
  box-sizing: border-box;
  border-radius: 20px;
}
.modules-item {
  .modules-title {
    color: rgba(32, 128, 255, 1);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}
.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}
</style>
