/**
 * 工单自定义字段
 * 声明变量工单类型开头 + CustomFieldsOption
 * 在下面default导出，组件内直接通过动态调用
 * 组件路径：src\views\pages\alarm_convergence\IntelligentEvents\eventBoard\customFields.vue
 */

export const eventCustomFieldsOption = [
  // { key: "tenantAbbreviation", label: "" },
  { key: "tenantName", label: "客户名称" },
  { key: "responseLimit", label: "响应时限" },
  { key: "resolveLimit", label: "解决时限" },
  { key: "responseUpdateLimit", label: "处理更新时限" },
  { key: "suspendUpdateLimit", label: "挂起更新时限" },
  { key: "priority", label: "优先级" },
  { key: "orderStatus", label: "状态" },
  { key: "id", label: "工单" },
  { key: "alarmNumber", label: "告警" },
  { key: "summary", label: "摘要" },
  { key: "userGroupName", label: "用户组" },
  { key: "externalId", label: "外部ID" },
  { key: "ticketGroupName", label: "工单组" },
  { key: "createTime", label: "创建时间" },
  { key: "updateTime", label: "更新时间" },
  { key: "responsibleName", label: "负责人" },
  { key: "actorName", label: "处理人" },
];

export const serviceCustomFieldsOption = [
  { key: "tenantName", label: "客户名称" },
  { key: "responseLimit", label: "响应时限" },
  { key: "resolveLimit", label: "解决时限" },
  { key: "priority", label: "优先级" },
  { key: "orderStatus", label: "状态" },
  { key: "id", label: "工单" },
  { key: "alarmNumber", label: "告警" },
  { key: "summary", label: "摘要" },
  { key: "userGroupName", label: "用户组" },
  { key: "externalId", label: "外部ID" },
  { key: "ticketGroupName", label: "工单组" },
  { key: "createTime", label: "创建时间" },
  { key: "updateTime", label: "更新时间" },
  { key: "responsibleName", label: "负责人" },
  { key: "actorName", label: "处理人" },
];

export const questionCustomFieldsOption = [
  { key: "tenantName", label: "客户名称" },
  { key: "priority", label: "优先级" },
  { key: "orderStatus", label: "状态" },
  { key: "id", label: "工单" },
  { key: "alarmNumber", label: "告警" },
  { key: "summary", label: "摘要" },
  { key: "userGroupName", label: "用户组" },
  { key: "externalId", label: "外部ID" },
  { key: "ticketGroupName", label: "工单组" },
  { key: "createTime", label: "创建时间" },
  { key: "updateTime", label: "更新时间" },
  { key: "responsibleName", label: "负责人" },
  { key: "actorName", label: "处理人" },
];

export const changeCustomFieldsOption = [
  { key: "tenantName", label: "客户名称" },
  { key: "priority", label: "优先级" },
  { key: "orderStatus", label: "状态" },
  { key: "id", label: "工单" },
  { key: "alarmNumber", label: "告警" },
  { key: "summary", label: "摘要" },
  { key: "userGroupName", label: "用户组" },
  { key: "externalId", label: "外部ID" },
  { key: "ticketGroupName", label: "工单组" },
  { key: "createTime", label: "创建时间" },
  { key: "updateTime", label: "更新时间" },
  { key: "responsibleName", label: "负责人" },
  { key: "actorName", label: "处理人" },
];

export const qrcodeCustomFieldsOption = [
  { key: "tenantName", label: "客户名称" },
  { key: "projectName", label: "项目名称" },
  { key: "unificationCode", label: "统一服务编码" },
  { key: "urgency", label: "优先级" },
  { key: "status", label: "状态" },
  { key: "kebaoCode", label: "客保工单号" },
  { key: "failureTitle", label: "工作标题" },
  { key: "failureDescription", label: "工单描述" },
  { key: "chargePerson", label: "负责人" },
  { key: "processors", label: "处理人" },
  { key: "createTime", label: "创建时间" },
];

export const alarmCustomFieldsOption = [
  { key: "eventSeverity", label: "紧急性" },
  { key: "tenantName", label: "客户名称" },
  { key: "deviceName", label: "设备" },
  { key: "desc", label: "告警" },
  { key: "order", label: "工单" },
  { key: "alertCreateTime", label: "时间" },
  { key: "eventConfirmedTime", label: "告警确认时间" },
  { key: "eventConfirmedPerson", label: "响应人" },
];

export default {
  eventCustomFieldsOption,
  serviceCustomFieldsOption,
  questionCustomFieldsOption,
  changeCustomFieldsOption,
  qrcodeCustomFieldsOption,
  alarmCustomFieldsOption,
};
