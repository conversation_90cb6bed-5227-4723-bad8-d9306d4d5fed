<template>
  <el-dialog title="自定义展示列" v-model="visible" :width="'fit-content'" :before-close="handleClose">
    <el-transfer v-model="customFields" :data="data" :titles="['未添加', '已添加']" />
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from "vue";

import { ElMessage } from "element-plus";

import customFieldsKey from "./customFields";

interface Props {
  customFieldsType: string;
  submitApi: Function;
  selectedCustomFields: string[];
}

const props = withDefaults(defineProps<Props>(), {
  customFieldsType: "",
  submitApi: () => {
    return Promise.reject(new Error("submitApi is not defined"));
  },
});

const data = computed(() => {
  return customFieldsKey[`${props.customFieldsType}CustomFieldsOption`];
});

const visible = ref(false);

const customFields = ref<string[]>([]);

function handleClose(done?) {
  if (done instanceof Function) done();
  else visible.value = false;
}

async function handleSubmit() {
  try {
    const result = await props.submitApi(customFields.value.join(","));
    if (result instanceof Error) throw new Error(result.message);
    handleClose();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

defineExpose({
  open: () => {
    visible.value = true;

    nextTick(() => (customFields.value = props.selectedCustomFields));
  },
});
</script>
