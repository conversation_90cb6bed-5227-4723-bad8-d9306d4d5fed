/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { reactive, readonly, computed, nextTick } from "vue";
import { templateRef } from "@vueuse/core";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import Editor from "./Editor.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage } from "element-plus";
import { filter, find } from "lodash-es";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
import { ElTable } from "element-plus";
import { type ModuleItem as DataItem } from "./api";
import { getModuleList as getItemList } from "./api";
/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */

export const tableRef = templateRef<typeof ElTable>("tableRef");
export const editorRef = templateRef<typeof Editor>("editorRef");

/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
export interface BaseItem {
  id: string;
}

type ItemKeys = "id" | "name";
export type Item = Pick<DataItem & BaseItem, ItemKeys>;

interface State<T> {
  loading: boolean;
  expand: string[];
  select: string[];
  current: string | undefined;
  search: Record<string, any>;
  list: T[];
  page: number;
  size: number;
  total: number;
  sort?: { prop: string; order: "ascending" | "descending" };
}
export enum command {
  Refresh = "Refresh",
  Request = "Request",
  Preview = "Preview",
  Create = "Create",
  Update = "Update",
  Modify = "Modify",
  Delete = "Delete",
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const final = readonly({
  pagination: true,
});

export const state = reactive<State<Item>>({
  loading: false,
  expand: [],
  select: [],
  current: undefined,
  search: {
    tenantName: {
      firstName: "",
      status: "include",
      type: "include",
      lastName: "",
      relation: "AND",
    },
  },
  list: [],
  sort: undefined,
  page: 1,
  size: 20,
  total: 0,
});
export const dataList = computed(() => (final.pagination ? state.list.slice((state.page - 1) * state.size, state.page * state.size) : state.list));
export const expand = computed(() => filter<Item>(state.list, (row) => state.expand.includes(row.id)));
export const select = computed(() => filter<Item>(state.list, (row) => state.select.includes(row.id)));
export const current = computed(() => find<Item>(state.list, (row) => row.id === state.current));
// const name = computed(() => state.name);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
export function handleExpand(row: Item, expandedRows: Item[]) {
  state.expand = expandedRows.map(({ id }) => id);
  if (find(expandedRows, ({ id }) => row.id === id)) {
    /*  */
  } else {
    /*  */
  }
}
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

export async function handleRefresh() {
  try {
    state.loading = true;
    await nextTick();
    await resetData();
    await queryData();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    state.loading = false;
  }
}
export async function handleQuery() {
  try {
    state.loading = true;
    await nextTick();
    await queryData();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    state.loading = false;
  }
}
export async function resetData() {
  state.list = [];
  state.page = 1;
  state.size = 20;
  state.total = 0;
  // for (const key in state.search) {
  //   if (key === "resource") continue;
  //   if (key === "type") continue;
  //   if (Object.prototype.hasOwnProperty.call(state.search, key)) {
  //     delete state.search[key];
  //   }
  // }
  await nextTick();
}
async function queryData() {
  const { success, message, data, page, size, total } = await getItemList({ ...state.search, paging: { pageNumber: state.page, pageSize: state.size } });
  if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
  state.list.splice(0, Infinity, ...(data instanceof Array ? data : []));
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */
