<template>
  <!-- 对话框表单 -->
  <component :is="ComponentRender" v-model:visible="data.visible" :handle-cancel="handleCancel">
    <template #header>
      {{ `${$params.id ? t("glob.edit") : t("event.Create")} ${$t(`event.${props.title}`)}` }}
    </template>
    <template #default="{ width }">
      <FormModel ref="formRef" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @submit="handleFinish">
        <FormItem :span="width > 675 ? 24 : 24" :label="`${$t(`event.${props.title}`)} ${t('event.suntype')}`" tooltip="" prop="ticketTemplateId" :rules="[{ required: true, message: t('event.Please select a subtype'), trigger: 'blur' }]">
          <el-select v-model="form.ticketTemplateId" :placeholder="t('event.Please select a subtype')" clearable filterable class="tw-w-full" @change="handleSubtypeChange">
            <el-option v-for="item in orderSubclasslist" :key="item.id" :label="item.orderSubclass" :value="item.id"> </el-option>
          </el-select>
        </FormItem>

        <FormItem v-if="isDict" :span="width > 675 ? 24 : 24" :label="`项目名称`" tooltip="" prop="projectId" :rules="[{ required: true, message: '请选择项目名称', trigger: ['change', 'blur'] }]">
          <el-select v-model="form.projectId" placeholder="请选择项目名称" filterable class="tw-w-full" @change="(v) => handleGetSlaByProject((projects.find((f) => f.id === v) || {}).slaId)">
            <el-option class="tw-h-[auto]" v-for="item in projects" :key="item.value" :label="item.projectName" :value="item.id">
              <div>
                <p class="tw-text-base tw-font-semibold tw-leading-8">{{ item.projectName }}</p>
                <p class="tw-text-sm tw-leading-7">统一服务编号：{{ item.uniformServiceCode }}</p>
                <p class="tw-text-sm tw-leading-7">项目编号：{{ item.projectCode }}</p>
              </div>
            </el-option>
          </el-select>
        </FormItem>

        <FormItem :span="width > 675 ? 24 : 24" :label="t(`event.${props.title}名称`)" tooltip="" prop="alertCount" :rules="[]">
          <el-input v-model="form.eventName" type="text" :placeholder="`${t('event.Enter')} ${t(`event.${props.title}名称`)}`" clearable></el-input>
        </FormItem>
        <FormItem :span="width > 675 ? 24 : 24" :label="t(`event.${props.title}描述`)" tooltip="" prop="deviceCount" :rules="[]">
          <el-input v-model="form.eventDesc" :autosize="{ minRows: 4, maxRows: 6 }" type="textarea" :placeholder="`${t('event.Enter')} ${t(`event.${props.title}描述`)}`" />
        </FormItem>

        <FormItem v-if="isDict" :span="width > 675 ? 24 : 24" :label="`外部工单号`" tooltip="" prop="kbServiceCode" :rules="[{ validator: handleValidServiceCode }, { required: true, message: '请输入外部工单号', trigger: ['change', 'blur'] }]">
          <el-input v-model="form.kbServiceCode" placeholder="请输入外部工单号"></el-input>
        </FormItem>

        <FormItem :span="width > 675 ? 24 : 24" :label="t('event.Priority')" tooltip="" prop="tenantCount" :rules="[]">
          <el-select v-model="form.priority" placeholder="" clearable filterable class="tw-w-full">
            <el-option v-for="item in isDict && form.projectId ? priorityOption : priorityList" :key="item.value" :label="`${item.label}${item.desc ? '(' + item.desc + ')' : ''}`" :value="item.value"> </el-option>
          </el-select>
        </FormItem>

        <FormItem v-show="isShowTicketClassification" :span="width > 675 ? 24 : 24" :label="`分类`" tooltip="" prop="ticketClassificationId" :rules="[]">
          <orderType v-if="data.visible" ref="orderTypeRef" v-model:ticketClassificationId="form.ticketClassificationId" type="event" :ticketTemplateId="form.ticketTemplateId"></orderType>
        </FormItem>
      </FormModel>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </component>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, computed, h, renderSlot, getCurrentInstance, createVNode, shallowRef, watch } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { buildTypeHelper } from "@/utils/type";

import { buildValidatorData } from "@/utils/validate";

import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import { QuillEditor } from "@vueup/vue-quill";

import getUserInfo from "@/utils/getUserInfo";

import { priority, priorityOption as priorityList } from "@/views/pages/apis/event";

import { /* getAllProjects */ getProjectsByEventId /* getAllUniformServiceCodes */ } from "@/views/pages/apis/projectManage";

import { getAllticketTemplates } from "@/views/pages/apis/ticketTemplate";

import orderType from "./orderType.vue";

import { DetailSlaConfig } from "@/views/pages/apis/SlaConfig";

// import { 智能事件中心_事件工单_填写客保工单 } from "@/views/pages/permission";

const userInfo = getUserInfo();
const { t } = useI18n();
const formRef = ref<InstanceType<typeof FormModel>>();
const ctx = getCurrentInstance();
if (!ctx) throw new Error("Component context initialization failed!");
const { appContext } = ctx;
const orderSubclasslist = ref<any>([]);

const orderTypeRef = ref<InstanceType<typeof orderType>>();
const isShowTicketClassification = computed(() => orderTypeRef.value && shallowRef(orderTypeRef.value.options).value instanceof Array && shallowRef(orderTypeRef.value.options).value.length);

interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 120,
});

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Item {
  eventName: string /* 事件名称 */;
  eventDesc: string /* 事件描述 */;
  priority: priority /* 事件优先级 */;

  projectName: string;
  projectCode: string;
  kbServiceCode: string;
  serviceCode: string;
  uniformServiceCode: string;

  ticketSubtype: string /* 子类型 */;
  ticketTemplateId: string /* 模板id */;

  ticketClassificationId: string;

  projectId: string;

  orderType: string;
}

/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  eventName: buildTypeHelper<Required<Item>["eventName"]>(""),
  eventDesc: buildTypeHelper<Required<Item>["eventDesc"]>(""),
  priority: buildTypeHelper<Required<Item>["priority"]>(priority.P7, new RegExp(`^${Object.keys(priority).join("|")}$`, "g")),
  projectName: buildTypeHelper<Required<Item>["projectName"]>(""),
  projectCode: buildTypeHelper<Required<Item>["projectCode"]>(""),
  kbServiceCode: buildTypeHelper<Required<Item>["kbServiceCode"]>(""),
  serviceCode: buildTypeHelper<Required<Item>["serviceCode"]>(""),
  uniformServiceCode: buildTypeHelper<Required<Item>["uniformServiceCode"]>(""),
  ticketSubtype: buildTypeHelper<Required<Item>["ticketSubtype"]>(""),
  ticketTemplateId: buildTypeHelper<Required<Item>["ticketTemplateId"]>(""),
  ticketClassificationId: buildTypeHelper<Required<Item>["ticketClassificationId"]>(""),
  projectId: buildTypeHelper<Required<Item>["projectId"]>(""),
  orderType: buildTypeHelper<Required<Item>["orderType"]>(""),
});

const isDict = computed(() => {
  let result = false;
  if (!form.value.ticketTemplateId) return result;
  result = (orderSubclasslist.value.find((v) => v.id === form.value.ticketTemplateId) || {}).relevancy === "dictevent";
  // eslint-disable-next-line vue/no-side-effects-in-computed-properties
  form.value.orderType = result ? "DICT_EVENT_ORDER" : "EVENT_ORDER";
  if (result) handleGetProject();
  return result;
});

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */

const priorityOption = ref<Record<string, any>>([]);

async function handleGetSlaByProject(slaId) {
  try {
    priorityOption.value = priorityList.map((v) => ({ ...v, desc: "" }));
    if (!slaId) return;
    const { data, message, success } = await DetailSlaConfig({ ruleId: slaId, tenantId: userInfo.currentTenantId });
    if (!success) throw new Error(message);
    priorityOption.value = priorityList.map((v) => {
      const desc = {
        resp: "",
        resolve: "",
      };

      // if (v.value !== priority.P8) {
      const currentResp = (data.eventRespTimeLevels.find((f) => f.priority === v.value) || {}).resolves || data.eventRespDefaultResolves;
      const respDHS = (currentResp || []).map((m) => Number((m.day || 0) * 24 * 60) + Number((m.hour || 0) * 60) + Number(m.minute || 0)) || []; // 时分秒拼接到一起取最大值

      if (respDHS.length) {
        const res = currentResp[respDHS.indexOf(Math.max(...respDHS))];
        desc.resp = `${res.day ? res.day + "day" : ""}${res.hour ? res.hour + "h" : ""}${res.minute ? res.minute + "min" : ""}`;
      }

      const currentResolve = (data.eventResolveTimeLevels.find((f) => f.priority === v.value) || {}).resolves || data.eventResolveDefaultResolves;
      const resolvepDHS = (currentResolve || []).map((m) => Number((m.day || 0) * 24 * 60) + Number((m.hour || 0) * 60) + Number(m.minute || 0)) || []; // 时分秒拼接到一起取最大值

      if (resolvepDHS.length) {
        const res = currentResolve[resolvepDHS.indexOf(Math.max(...resolvepDHS))];
        desc.resolve = `${res.day ? res.day + "day" : ""}${res.hour ? res.hour + "h" : ""}${res.minute ? res.minute + "min" : ""}`;
      }

      return { ...v, desc: `${desc.resp ? "响应时限:" + desc.resp + ";" : ""}${desc.resolve ? "处理时限:" + desc.resolve + ";" : ""}` };
    });
  } catch (error) {
    /* ...error */
  }
}

async function getorderSub() {
  const params = {
    pageNumber: 1,
    pageSize: 9999,
    containerId: userInfo?.currentTenant?.containerId,
    queryPermissionId: "733532355297280000",
    verifyPermissionIds: "733532411236712448,733532436834549760,733532469994717184",
  };
  try {
    const { data, message, success, total } = await getAllticketTemplates({ ...params });
    if (!success) throw new Error(message);
    orderSubclasslist.value = data.filter((item) => item.ticketType === "event");
    if (orderSubclasslist.value.length === 1) form.value.ticketTemplateId = orderSubclasslist.value[0].id;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

const handleSubtypeChange = (val: string) => {
  const selected = orderSubclasslist.value.find((item) => item.id === val);
  if (selected) {
    // form.value.ticketTemplateId = selected.id;
    form.value.ticketSubtype = selected.orderSubclass;
  }
};

const projects = ref<Record<string, any>[]>([]);

async function handleGetProject() {
  try {
    const { data, success, message } = await getProjectsByEventId({ id: 0 });
    if (!success) throw new Error(message);
    projects.value = data;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

// const uniformServiceCodes = ref<Record<string, any>[]>();
// async function getUnformeServiceCode() {
//   try {
//     const { data, success, message } = await getAllUniformServiceCodes({});
//     if (!success) throw new Error(message);
//     uniformServiceCodes.value = data.filter((v) => v.uniformServiceCode);
//   } catch (error) {
//     error instanceof Error && ElMessage.error(error.message);
//   }
// }
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  // getUnformeServiceCode();
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  return form;
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

function handleProjectChange(key: string, v: string) {
  const currentProject: Record<string, any> = projects.value.find((f) => f[key] === v) || {};
  form.value.projectName = currentProject.projectName;
  form.value.projectCode = currentProject.projectCode;
  form.value.uniformServiceCode = currentProject.uniformServiceCode;
}

function handleValidServiceCode(rule: any, value: any, callback: any) {
  if (form.value.kbServiceCode && form.value.serviceCode) return callback(new Error("客保工单号和服务工单号只能填写一个"));
  else callback();
}

/**
 * TODO: 窗口方法
 */
interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<Item>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Record<string, unknown>) => void;
  reject?: (value: Error) => void;
  callback?: (form: Record<string, unknown>) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Record<string, unknown>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(function (formResult, [key, util]) {
    if (!util) return formResult;
    else if (util.test(formResult[key])) return formResult;
    else return Object.assign(formResult, { [key]: util.transfer(formResult[key], util.value as never) });
  }, form as Required<Item>);
}

async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => (formRef.value ? formRef.value.validate(resolve) : resolve(false)));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    // close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();

defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, callback?: (form: Record<string, unknown>) => Promise<void>): Promise<unknown> {
    await getorderSub();
    if (form.value.ticketTemplateId) {
      params.ticketTemplateId = form.value.ticketTemplateId;
    }

    if (data.visible) handleCancel();
    const wait = new Promise<Record<string, unknown>>((resolve, reject) => ((data.resolve = resolve), (data.reject = reject)));
    $params.value = cloneDeep(params);
    data.visible = true;
    data.loading = true;
    data.submitLoading = true;
    data.callback = callback;
    await nextTick();
    try {
      await runningInit($params.value);
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      handleCancel();
    }
    handleReset();
    data.loading = false;
    data.submitLoading = false;

    try {
      return await wait;
    } catch (error) {
      return error;
    }
  },
  async confirm<T extends { $title: string; $type: "" | "success" | "warning" | "info" | "error"; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      await ElMessageBox.confirm(
        createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: ElMessageBox.close }) }),
        params.$title,
        {
          // type: params.$type,
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        },
        appContext
      );
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async alert<T extends { $title: string; $type: "" | "success" | "warning" | "info" | "error"; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      await ElMessageBox.alert(
        createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: ElMessageBox.close }) }),
        // params.$title,
        {
          type: params.$type,
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        },
        appContext
      );
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
});
</script>

<style scoped lang="scss">
.edit_sla_config {
  :deep() {
    .el-input-number {
      width: 50px !important;

      .elstyle-input {
        width: 50px;

        input {
          padding: 0;
        }
      }
    }

    .el-input-number__increase {
      display: none;
    }

    .el-input-number__decrease {
      display: none;
    }

    .el-card {
      margin-top: 20px;
    }

    .el-form-item__content .el-form-item-content {
      display: flex;
      flex-direction: column;
    }

    .el-table .cell {
      padding: 0 !important;
    }

    .el-table .el-table__cell {
      padding: 0 !important;
      height: 50px;
    }
  }
}
.priority-icon {
  width: 10px;
  height: 10px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 10px;
}
.state {
  padding: 2px 10px;
  box-sizing: border-box;
  border-radius: 20px;
}
.modules-item {
  .modules-title {
    color: rgba(32, 128, 255, 1);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}
.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}
</style>
