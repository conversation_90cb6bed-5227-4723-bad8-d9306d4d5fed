<template>
  <el-cascader class="tw-w-full" v-model="ticketClassificationId" :options="options" :props="{ checkStrictly: true, value: 'id', label: 'classificationName', children: 'children' }" clearable />
</template>

<script setup lang="ts">
import { ref, useModel, watch } from "vue";

import { getTicketClassificationByTenant as getOrderType, AssignableTicketType } from "@/views/pages/apis/orderGroup";

interface Props {
  ticketTemplateId: string;
  ticketClassificationId: string;
  type: keyof typeof AssignableTicketType;
}

const props = withDefaults(defineProps<Props>(), {
  ticketTemplateId: "",
  ticketClassificationId: "",
  type: AssignableTicketType.event,
});

watch(
  () => props.ticketTemplateId,
  (v) => {
    handleRefresh(v);
  },
  {
    deep: true,
    immediate: true,
  }
);

const ticketClassificationId = useModel(props, "ticketClassificationId");

const options = ref([]);

async function handleRefresh(ticketTemplateId) {
  if (!ticketTemplateId) return;
  const { data, message, success } = await getOrderType({ type: props.type, ticketTemplateId });
  if (!success) throw new Error(message);
  function _resetData(list, allData) {
    for (let i = 0; i < list.length; i++) {
      const item = list[i];
      item.children = allData.filter((v) => v.parentId === item.id) || [];
      if (item.children instanceof Array && item.children.length) _resetData(item.children, allData);
    }
    return list;
  }
  options.value = _resetData(
    data.filter((v) => !v.parentId || v.parentId === "-1"),
    data
  );
}

defineExpose({
  options: options,
});

// onMounted(() => {
//   handleRefresh();
// });
</script>
