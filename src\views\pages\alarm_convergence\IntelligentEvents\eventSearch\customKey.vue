<template>
  <el-dialog v-model="dialogVisible" :title="$t('eventSearch.CustomDisplayColumns')" width="600" :before-close="handleClose">
    <el-form :model="form" ref="formRef" :rules="{ value: [{ required: true, message: $t('eventSearch.PleaseAddDisplayColumns'), trigger: 'blur' }] }">
      <el-form-item prop="value">
        <el-transfer v-model="form.value" :data="translatedOptions" :props="{ key: 'key', label: 'label' }" :titles="[$t('eventSearch.NotAdded'), $t('eventSearch.Added')]" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ $t("eventSearch.Cancel") }}</el-button>
        <el-button type="primary" @click="handleSubmit"> {{ $t("eventSearch.Confirm") }} </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";

import { customKeyOption } from "@/views/pages/apis/eventSearch";
import { ElMessage, FormInstance } from "element-plus";

import { useI18n } from "vue-i18n";
const { t } = useI18n({ useScope: "global" });
const translatedOptions = computed(() =>
  customKeyOption.map((item) => ({
    ...item,
    label: t(`eventSearch.${item.label}`),
  }))
);

const handleLanguageChange = (event) => {
  if (event.key === "systemLang") {
    window.location.reload();
  }
};

onMounted(() => {
  window.addEventListener("storage", handleLanguageChange);
});

onUnmounted(() => {
  window.removeEventListener("storage", handleLanguageChange);
});
interface Props {
  submitApi: Function;
  customKeys: string[];
}

const props = withDefaults(defineProps<Props>(), { submitApi: () => {}, customKeys: () => [] });

const dialogVisible = ref<boolean>(false);

function handleClose(done) {
  if (done instanceof Function) done();
  else dialogVisible.value = false;
}

const formRef = ref<FormInstance>();
const form = ref<{ value: string[] }>({
  value: [],
});

async function handleSubmit() {
  formRef.value &&
    formRef.value.validate(async (valid) => {
      if (!valid) return;
      const result = await props.submitApi(form.value.value.join());
      if (result) {
        console.log(result);
        ElMessage.success(t(`eventSearch.OperationSuccess`));
        handleClose(null);
      }
    });
}

defineExpose({
  open: function () {
    form.value.value = props.customKeys;
    dialogVisible.value = true;
  },
});
</script>
