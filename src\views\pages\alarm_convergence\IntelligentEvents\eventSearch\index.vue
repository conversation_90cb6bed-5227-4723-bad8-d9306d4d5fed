<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="state.page" v-model:pageSize="state.size" :total="state.total" :height="height - 40" :show-paging="!showSearch" @size-change="handleRefresh" @current-change="handleRefresh">
      <template #left>
        <el-button type="primary" :icon="Search" @click="() => (showSearch = !showSearch)">{{ $t("eventSearch.search") }}</el-button>
      </template>
      <template #right v-if="!showSearch">
        <el-button type="primary" :icon="Tools" @click="handleCustomKey"></el-button>
        <el-button type="primary" :icon="Download" @click="handleExportRetrievalResult" :disabled="!state.list.length">{{ $t("eventSearch.Export") }}</el-button>
      </template>
      <template #default="{ height: tableHeight }">
        <ElCollapseTransition name="el-zoom-in-top">
          <div class="tw-rounded-md tw-border tw-p-[20px]" v-show="showSearch">
            <el-form ref="formRef" :model="{}" :label-width="formLabelWidth" label-position="left" :rules="rules">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item :label="$t('eventSearch.WorkOrderID')" prop="id">
                    <div class="tw-flex tw-w-full">
                      <el-input v-model="form.id" :placeholder="$t('eventSearch.PleaseentertheworkorderID')"></el-input>
                      <el-button class="tw-ml-2" type="primary" @click="handleGetOrderDetail">{{ $t("eventSearch.Viewdetails") }}</el-button>
                    </div>
                  </el-form-item>
                </el-col>

                <el-col :span="24"><el-divider /></el-col>

                <el-col :span="12">
                  <el-form-item :label="$t('eventSearch.Workordertype')">
                    <el-select v-model="form.orderType" :placeholder="$t('eventSearch.Pleaseselectthetypeofworkorder')" multiple clearable>
                      <template #header>
                        <el-checkbox v-model="orderTypeAll" :indeterminate="orderTypeIndeterminate" @change="handleOrderTypeAll"> {{ $t("eventSearch.SelectAll") }} </el-checkbox>
                      </template>
                      <el-option v-for="item in orderTypeOption" :key="item.value" :label="$t('eventSearch.' + item.label)" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item :label="$t('eventSearch.Workorderstatus')">
                    <el-select v-model="form.state" :placeholder="$t('eventSearch.Pleaseselectthestatusoftheworkorder')" multiple clearable>
                      <template #header>
                        <el-checkbox v-model="orderStateAll" :indeterminate="orderStateIndeterminate" @change="handleOrderStateAll"> {{ $t("eventSearch.SelectAll") }} </el-checkbox>
                      </template>
                      <el-option v-for="item in stateOption" :key="item.value" :label="$t('eventSearch.' + item.label)" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item :label="$t('eventSearch.Priority')">
                    <el-select v-model="form.priority" placeholder="$t('eventSearch.Pleasechoosepriority')" multiple clearable>
                      <template #header>
                        <el-checkbox v-model="priorityAll" :indeterminate="priorityIndeterminate" @change="handlePriorityAll"> {{ $t("eventSearch.SelectAll") }} </el-checkbox>
                      </template>
                      <el-option v-for="item in priorityOption" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24"> </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('eventSearch.createTime')">
                    <el-date-picker :model-value="[form.createTimeStart, form.createTimeEnd]" @update:model-value="(v) => ((form.createTimeStart = (v || []).length ? v[0] : ''), (form.createTimeEnd = (v || []).length ? v[1] : ''))" type="datetimerange" :range-separator="$t('eventSearch.Arrive')" :start-placeholder="$t('eventSearch.StartTime')" :end-placeholder="$t('eventSearch.EndTime')" value-format="x" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('eventSearch.completionTime')">
                    <el-date-picker :model-value="[form.finishTimeStart, form.finishTimeEnd]" @update:model-value="(v) => ((form.finishTimeStart = (v || []).length ? v[0] : ''), (form.finishTimeEnd = (v || []).length ? v[1] : ''))" type="datetimerange" :range-separator="$t('eventSearch.Arrive')" :start-placeholder="$t('eventSearch.StartTime')" :end-placeholder="$t('eventSearch.EndTime')" value-format="x" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('eventSearch.closeTime')">
                    <el-date-picker :model-value="[form.closeTimeStart, form.closeTimeEnd]" @update:model-value="(v) => ((form.closeTimeStart = (v || []).length ? v[0] : ''), (form.closeTimeEnd = (v || []).length ? v[1] : ''))" type="datetimerange" :range-separator="$t('eventSearch.Arrive')" :start-placeholder="$t('eventSearch.StartTime')" :end-placeholder="$t('eventSearch.EndTime')" value-format="x" />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item :label="$t('eventSearch.RetrieveField')" prop="field">
                    <el-select v-model="form.field" :placeholder="$t('eventSearch.Pleaseselectthesearchfield')" multiple clearable>
                      <el-option v-for="item in fieldOption" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item :label="$t('eventSearch.RetrieveText')" prop="text">
                    <el-input v-model="form.text" :placeholder="$t('eventSearch.Pleaseenterthesearchfext')"></el-input>
                  </el-form-item>
                </el-col>

                <el-col :span="24" class="tw-flex tw-justify-end">
                  <div class="tw-flex tw-justify-between">
                    <el-button type="primary" @click="handleQuery" :loading="state.loading">{{ $t("eventSearch.Search") }}</el-button>
                    <el-button @click="() => (showSearch = !showSearch)">{{ $t("eventSearch.Cancel") }}</el-button>
                  </div>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </ElCollapseTransition>
        <ElCollapseTransition name="el-zoom-in-bottom">
          <el-table v-loading="state.loading" ref="tableRef" :data="state.list" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleRefresh())" @selection-change="(select) => (state.select = (select as Record<string, any>[]).map((v) => v.id))" v-show="!showSearch">
            <TableColumn v-if="customKeys.includes(CustomKey.priority)" type="default" prop="priority" :label="$t('eventSearch.Priority')" sortable="custom">
              <template #default="{ row, column }">
                <span class="tw-rounded-full tw-px-3 tw-py-1 tw-text-white" :style="{ backgroundColor: (find(priorityOption, (v) => v.value === row[column.property]) || {}).color }">{{ (find(priorityOption, (v) => v.value === row[column.property]) || {}).label }}</span>
              </template>
            </TableColumn>
            <TableColumn v-if="customKeys.includes(CustomKey.id)" type="default" prop="id" :label="$t('eventSearch.WorkOrderID')" sortable="custom" width="180">
              <template #default="{ row }">
                <el-button type="primary" link @click="handleRouterOrder(row)">{{ row.id }}</el-button>
              </template>
            </TableColumn>
            <TableColumn v-if="customKeys.includes(CustomKey.order)" type="default" prop="id" :label="$t('eventSearch.WorkOrder')" width="180">
              <template #default="{ row }">
                <div>
                  <p>{{ OrderTypes[row.orderType] }}</p>
                  <el-button type="primary" link @click="handleRouterOrder(row)">{{ row.id }}</el-button>
                  <div>
                    <el-tag :type="(find(stateOption, (v) => v.value === row.orderStatus) || {}).type" size="small">{{ (find(stateOption, (v) => v.value === row.orderStatus) || {}).label }}</el-tag>
                  </div>
                </div>
              </template>
            </TableColumn>
            <TableColumn v-if="customKeys.includes(CustomKey.alarmNumber)" type="default" prop="alarmNumber" :label="$t('eventSearch.giveanalarm')" sortable="custom">
              <template #default="{ row }">
                <el-link type="danger" :underline="false">{{ row.alarmNumber || 0 }}</el-link>
              </template>
            </TableColumn>
            <TableColumn v-if="customKeys.includes(CustomKey.summary)" type="default" prop="summary" :label="$t('eventSearch.summary')"></TableColumn>
            <TableColumn v-if="customKeys.includes(CustomKey.desc)" type="default" prop="desc" :label="$t('eventSearch.description')"></TableColumn>
            <TableColumn v-if="customKeys.includes(CustomKey.summaryAndDesc)" type="default" :label="$t('eventSearch.title')">
              <template #default="{ row }">
                <p class="tw-text-base tw-font-semibold">{{ row.summary }}</p>
                <p class="tw-text-sm">{{ row.desc }}</p>
              </template>
            </TableColumn>
            <TableColumn v-if="customKeys.includes(CustomKey.responsibleName)" type="default" prop="responsibleName" :label="$t('eventSearch.assignee')" sortable="custom">
              <template #default="{ row }">
                {{ handleFormatTableUserinfo(row.responsibleName) }}
              </template>
            </TableColumn>
            <TableColumn v-if="customKeys.includes(CustomKey.actorName)" type="default" prop="actorName" :label="$t('eventSearch.handler')" sortable="custom">
              <template #default="{ row }">
                {{ handleFormatTableUserinfo(row.actorName) }}
              </template>
            </TableColumn>
            <TableColumn v-if="customKeys.includes(CustomKey.finishTime)" type="date" prop="finishTime" :label="$t('eventSearch.completionTime')" sortable="custom"></TableColumn>
            <TableColumn v-if="customKeys.includes(CustomKey.closeTime)" type="date" prop="closeTime" :label="$t('eventSearch.closeTime')" sortable="custom"></TableColumn>
            <TableColumn v-if="customKeys.includes(CustomKey.createUser)" type="default" prop="createUser" :label="$t('eventSearch.creator')" sortable="custom">
              <template #default="{ row }">
                {{ handleFormatTableUserinfo(row.createUser) }}
              </template>
            </TableColumn>
            <TableColumn v-if="customKeys.includes(CustomKey.createTime)" type="date" prop="createTime" :label="$t('eventSearch.createTime')" sortable="custom"></TableColumn>
            <TableColumn v-if="customKeys.includes(CustomKey.updateUser)" type="default" prop="updateUser" :label="$t('eventSearch.modifier')" sortable="custom">
              <template #default="{ row }">
                {{ handleFormatTableUserinfo(row.updateUser) }}
              </template>
            </TableColumn>
            <TableColumn v-if="customKeys.includes(CustomKey.updateTime)" type="date" prop="updateTime" :label="$t('eventSearch.modifiedTime')" sortable="custom"></TableColumn>
          </el-table>
        </ElCollapseTransition>

        <custom-key ref="customKeyRef" :submitApi="handleSetCustomKey" :customKeys="customKeys"></custom-key>
      </template>
    </pageTemplate>
  </el-card>
</template>

<script setup lang="ts">
import { inject, ref, computed, reactive, onMounted, watch, nextTick } from "vue";
import { useRouter, useRoute, RouteLocation } from "vue-router";

import { find } from "lodash-es";

import { Tools, Download, Search } from "@element-plus/icons-vue";

import { ElCollapseTransition, ElMessage, FormInstance, CheckboxValueType } from "element-plus";

import pageTemplate from "@/components/pageTemplate.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";

import { /* OrderTypes, */ DataType } from "@/views/pages/apis/deviceManage";

import { priorityOption } from "@/views/pages/apis/eventPriority";

import { eventStateOption, serviceStateOption } from "@/views/pages/apis/event";
import { publishStateOption } from "@/views/pages/apis/publish";
import { changeStateOption } from "@/views/pages/apis/change";
import { questionStateOption } from "@/views/pages/apis/question";

import { getRetrievalResult as getData, getRetrievalResultByOrderId as getDataById, exportRetrievalResult as exportData, getCustomKey, setCustomKey, CustomKey } from "@/views/pages/apis/eventSearch";

import getUserInfo from "@/utils/getUserInfo";

import customKey from "./customKey.vue";

import handleToOrder from "@/views/pages/alarm_convergence/IntelligentEvents/eventBoard/toOrder";
import { useI18n } from "vue-i18n";
const router = useRouter();
const route = useRoute();

const { t, locale } = useI18n();
const userInfo = getUserInfo();

interface Props {
  width?: number;
  height?: number;
  title?: string;
}

const props = withDefaults(defineProps<Props>(), { title: "工单检索" });

const _width = inject("width", ref(0));
const _height = inject("height", ref(0));

const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);

const formLabelWidth = computed(() => (locale.value === "en" ? void 0 : "80px"));

enum OrderTypes {
  "EVENT_ORDER,DICT_EVENT_ORDER" = "事件",
  "SERVICE_REQUEST,DICT_SERVICE_REQUEST" = "服务请求",
  CHANGE = "变更",
  QUESTION = "问题",
  PUBLISH = "发布",
  // DICT_EVENT_ORDER = "DICT事件",
  // DICT_SERVICE_REQUEST = "DICT服务请求",
}

const orderTypeOption = computed(() => {
  const arrayObjects: any = [];
  for (const [propertyKey, propertyValue] of Object.entries(OrderTypes)) {
    if (!Number.isNaN(Number(propertyKey))) {
      continue;
    }
    arrayObjects.push({ value: propertyKey, label: propertyValue });
  }

  return arrayObjects;
});

const stateOption = computed(() => {
  const res = new Map();
  return [...eventStateOption, ...serviceStateOption, ...publishStateOption, ...changeStateOption, ...questionStateOption].filter((item) => !res.has(item.value) && res.set(item.value, 1));
});

const fieldOption = ref<{ value: string; label: string }[]>([
  { label: t("eventSearch.WorkOrderID"), value: "id" },
  { label: t("eventSearch.Alerts"), value: "alarmNumber" },
  { label: t("eventSearch.Summary"), value: "summary" },
  { label: t("eventSearch.Description"), value: "description" },
  // { label: "摘要描述", value: "summaryAndDesc" },
  { label: t("eventSearch.Created"), value: "createUser" },
  { label: t("eventSearch.Modified"), value: "updateUser" },
  { label: t("eventSearch.Director"), value: "responsibleName" },
  { label: t("eventSearch.Handler"), value: "actorName" },
]);

// 工单ID:Ticket ID;  2)告警数量：Alerts  3）摘要：Summary  4）描述：Description  5）创建人：Created   6）修改人：Modified    7）负责人：Director      8）处理人：Handler

const showSearch = ref<boolean>(true);

const state = reactive<Record<string, any>>({
  page: 1,
  size: 50,
  total: 0,
  loading: false,
  list: [],
  sort: undefined,
  expand: [],
  select: [],
  current: undefined,
});

const form = ref<Record<string, any>>({
  id: "",
  orderType: orderTypeOption.value.map((v) => v.value),
  state: stateOption.value.map((v) => v.value),
  priority: priorityOption.map((v) => v.value),
  createTimeStart: "",
  createTimeEnd: "",
  finishTimeStart: "",
  finishTimeEnd: "",
  closeTimeStart: "",
  closeTimeEnd: "",
  field: [],
  text: "",
});
const formRef = ref<FormInstance>();

const rules = reactive({
  id: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (!form.value.id) {
          return callback(new Error(t(`eventSearch.PleaseentertheworkorderID`)));
        }
        if (!new RegExp("^\.{1,19}$").test(form.value.id)) {
          return callback(new Error(t(`eventSearch.PleaseenterthecorrectworkorderID`)));
        }
        return callback();
      },
      trigger: ["blur", "change"],
    },
  ],
  field: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (form.value.text && (!form.value.field || !form.value.field.length)) {
          return callback(new Error(t(`eventSearch.Pleaseselectthesearchfield`)));
        }
        return callback();
      },
      trigger: ["blur", "change"],
    },
  ],
});

const orderTypeAll = ref(false);
const orderTypeIndeterminate = ref(false);
function handleOrderTypeAll(val: CheckboxValueType) {
  orderTypeIndeterminate.value = false;
  if (val) {
    form.value.orderType = orderTypeOption.value.map((_) => _.value);
  } else {
    form.value.orderType = [];
  }
}
watch(
  () => form.value.orderType,
  (val) => {
    if (val.length === 0) {
      orderTypeAll.value = false;
      orderTypeIndeterminate.value = false;
    } else if (val.length === orderTypeOption.value.length) {
      orderTypeAll.value = true;
      orderTypeIndeterminate.value = false;
    } else {
      orderTypeIndeterminate.value = true;
    }
  },
  { immediate: true }
);

const orderStateAll = ref(false);
const orderStateIndeterminate = ref(false);
function handleOrderStateAll(val: CheckboxValueType) {
  orderStateIndeterminate.value = false;
  if (val) {
    form.value.state = stateOption.value.map((_) => _.value);
  } else {
    form.value.state = [];
  }
}
watch(
  () => form.value.state,
  (val) => {
    if (val.length === 0) {
      orderStateAll.value = false;
      orderStateIndeterminate.value = false;
    } else if (val.length === stateOption.value.length) {
      orderStateAll.value = true;
      orderStateIndeterminate.value = false;
    } else {
      orderStateIndeterminate.value = true;
    }
  },
  { immediate: true }
);

const priorityAll = ref(false);
const priorityIndeterminate = ref(false);
function handlePriorityAll(val: CheckboxValueType) {
  priorityIndeterminate.value = false;
  if (val) {
    form.value.priority = priorityOption.map((_) => _.value);
  } else {
    form.value.priority = [];
  }
}
watch(
  () => form.value.priority,
  (val) => {
    if (val.length === 0) {
      priorityAll.value = false;
      priorityIndeterminate.value = false;
    } else if (val.length === priorityOption.length) {
      priorityAll.value = true;
      priorityIndeterminate.value = false;
    } else {
      priorityIndeterminate.value = true;
    }
  },
  { immediate: true }
);

async function handleQuery() {
  state.page = 1;
  await nextTick();
  await handleRefresh();
}

/**
 * @desc 刷新获取列表
 */
async function handleRefresh() {
  /* 查询清除工单ID验证信息 */
  formRef.value && formRef.value.clearValidate(["id"]);

  formRef.value &&
    formRef.value.validateField(["field"], async (valid) => {
      try {
        if (!valid) return;
        state.loading = true;

        let sort: string[] = [];

        switch ((state.sort || {}).order) {
          case "ascending":
            sort.push(`${String(state.sort?.prop)},asc`);
            if (state.sort?.prop === "createTime") {
              sort.shift();

              sort.push(`createdTime,asc`);
              // delete sort.createTime;
            } else if (state.sort?.prop === "updateTime") {
              sort.shift();

              sort.push(`updatedTime,asc`);
              // delete sort.updateTime;
            }
            break;
          case "descending":
            sort.push(`${String(state.sort?.prop)},desc`);
            if (state.sort?.prop === "createTime") {
              sort.shift();

              sort.push(`createdTime,desc`);
              // delete sort.createTime;
            } else if (state.sort?.prop === "updateTime") {
              sort.shift();

              sort.push(`updatedTime,desc`);
              // delete sort.updateTime;
            }
            break;
        }

        await handleGetCustomKey();

        const { data, message, success, total } = await getData({ ...Object.keys(form.value).reduce((p, t) => Object.assign(p, form.value[t] ? { [t]: form.value[t] instanceof Array ? form.value[t].join() : form.value[t] } : {}), {}), pageNumber: state.page, pageSize: state.size, limit: sort.join() as any });
        if (!success) throw new Error(message);
        showSearch.value = false;
        state.list = data;
        state.total = Number(total);
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      } finally {
        state.loading = false;
      }
    });
}

/**
 * @desc 查看工单-跳转工单详情
 */
async function handleGetOrderDetail() {
  formRef.value &&
    formRef.value.validateField(["id"], async (valid) => {
      try {
        if (!valid) return;
        const { data, message, success } = await getDataById({ ordreId: form.value.id });
        if (!success) throw new Error(message);
        if (!data) throw new Error(`工单不存在：${form.value.id}`);
        handleRouterOrder(data);
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    });
}

/**
 * @desc 跳转工单详情
 * @param v 工单信息
 */
function handleRouterOrder(v) {
  handleToOrder(v.orderType, v.id, v.tenantId);
}

/**
 * @desc 格式化用户
 */
function handleFormatTableUserinfo(userinfo) {
  try {
    return JSON.parse(userinfo).account || "--";
  } catch (error) {
    return userinfo || "--";
  }
}

/**
 * @desc 导出
 */
async function handleExportRetrievalResult() {
  try {
    const { data, message, success } = await exportData({ ...Object.keys(form.value).reduce((p, t) => Object.assign(p, form.value[t] ? { [t]: form.value[t] instanceof Array ? form.value[t].join() : form.value[t] } : {}), {}), userId: userInfo.userId, pageNumber: state.page, pageSize: state.size });
    if (!success) throw new Error(message);
    const href = URL.createObjectURL(data);
    const link = document.createElement("a");
    link.download = `${`工单检索`}.xlsx`;
    link.href = href;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(href); // 释放内存
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

/**
 * @desc 自定义字段
 */
const customKeyRef = ref();
async function handleCustomKey() {
  customKeyRef.value && customKeyRef.value.open();
}

const customKeys = ref<string[]>([]);

async function handleGetCustomKey() {
  try {
    const { data, message, success } = await getCustomKey({ userId: userInfo.userId });
    if (!success) throw new Error(message);
    customKeys.value = data.fields;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

async function handleSetCustomKey(fields) {
  try {
    const { message, success } = await setCustomKey({ userId: userInfo.userId, fields });
    if (!success) throw new Error(message);
    handleGetCustomKey();
    return true;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
    return false;
  }
}

onMounted(async () => {
  await handleGetCustomKey();
});
</script>
