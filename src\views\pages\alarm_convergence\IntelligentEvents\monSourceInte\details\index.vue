<template>
  <div>
    <el-page-header slot="header" @back="backRouter" :content="content"></el-page-header>
    <el-form :style="{ marginTop: '10px' }" label-position="left">
      <el-scrollbar :height="height - 50">
        <el-card class="el-card-mt">
          <div class="modules-item">集成秘钥</div>
          <div>
            <div class="modules-tips"><i class="el-icon-info" /> 集成秘钥是集成监控源的关键秘钥，已集成的监控源支持一键更新替换原有秘钥，请谨慎使用！</div>
          </div>
          <div>
            <el-form-item>
              <el-row>
                <!-- <el-col style="text-align: left" class="bold" :span="2"> </el-col> -->
                <el-col :span="24" style="margin-top: 10px">
                  集成秘钥
                  <el-input :disabled="true" v-model="detailObj.integrationKey" :style="basicClassInput" style="margin-left: 10px" />
                  <!-- <el-button  type="text" icon="el-icon-document-copy"></el-button> -->
                  <el-icon style="margin-left: 20px; cursor: pointer" @click="copy(detailObj.integrationKey)"><CopyDocument /></el-icon>
                </el-col>
              </el-row>
            </el-form-item>
          </div>
        </el-card>
        <el-card class="el-card-mt">
          <div class="modules-item">
            集成接入步骤
            <div class="modules-tips"><i class="el-icon-info" /> 请按照一下集成接入步骤完成标准集成监控集成，需要在标准集成侧完成集成后点击地步确认集成按钮确认集成；更多详情，查看集成文档</div>
          </div>
          <div>
            <el-form-item>
              <el-row class="step-list">
                <!-- <el-steps direction="vertical" space="100px" :active="4">
                <el-step title="步骤 1：配置报警推送URL地址" description="http://localhost:8081/admin/alarm_convergence/monSourceInteDetail"></el-step>
                <el-step title="步骤 2：确认集成" description="完成以上配置后点击集成秘钥下的确认集成按钮完成集成配置"></el-step>
                <el-step title="步骤 3：验证集成是否成功" description="在标准集成监控侧配置告警、订阅规则并触发告警后，前往[集成配置]页面查看到标准集成监控源状态为“已接收”报警则说明已集成成功"></el-step>
                <el-step title="步骤 4：标准集成数据格式" description="标准集成目前仅支持POST方式集成，后续将逐步开放GET，PUSH等集成接入方式"></el-step>
              </el-steps> -->
                <el-col :span="24">
                  <div class="step">步骤一</div>
                  <div>
                    <h3>配置报警推送URL地址</h3>
                    <span>http://localhost:8080</span>
                  </div>
                </el-col>
                <el-col :span="24">
                  <div class="step">步骤二</div>
                  <div>
                    <h3>确认集成</h3>
                    <span>完成以上配置后点击集成秘钥下的确认集成按钮完成集成配置</span>
                  </div>
                </el-col>
                <el-col :span="24">
                  <div class="step">步骤三</div>
                  <div>
                    <h3>验证集成是否成功</h3>
                    <span>在标准集成监控侧配置告警、订阅规则并触发告警后，前往[集成配置]页面查看到标准集成监控源状态为“已接收”报警则说明已集成成功。</span>
                  </div>
                </el-col>
                <el-col :span="24">
                  <div class="step">步骤四</div>
                  <div>
                    <h3>标准集成数据格式</h3>
                    <span>标准集成目前仅支持POST方式集成，后续将逐步开放GET，PUSH等集成接入方式</span>
                    <json-viewer :value="jsonData"></json-viewer>
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <span>字段说明如下：</span>
              </el-row>
              <!-- <template> -->
              <el-table stripe :data="tableData" border style="width: 100%">
                <el-table-column align="center" prop="field" label="字段" width="180"> </el-table-column>
                <el-table-column align="center" prop="logField" label="日志字段" width="180"> </el-table-column>
                <el-table-column align="center" prop="desc" label="描述" width="180"> </el-table-column>
                <el-table-column align="center" prop="isNecessary" label="是否必要" width="180"> </el-table-column>
                <el-table-column align="center" prop="sample" label="样例"> </el-table-column>
              </el-table>
              <!-- </template> -->
            </el-form-item>
          </div>
        </el-card>
        <el-col :style="{ textAlign: 'center', marginTop: '10px' }">
          <el-button v-if="userInfo.hasPermission(PERMISSION.group527766513894031360.auth527766849463517184)" type="primary" @click="secure">解除集成</el-button>
          <el-button v-if="userInfo.hasPermission(PERMISSION.group527766513894031360.auth527766809655377920)" @click="prohibit">{{ detailObj.enabled ? "禁用" : "启用" }}</el-button>
        </el-col>
      </el-scrollbar>
    </el-form>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { MointerSourceDetails, DeleteMointerSource, updateMointerSource } from "@/views/pages/apis/monSourceInte";
// import { resolveData } from "../ConfigCenter/SlaConfig/common";
import getUserInfo from "@/utils/getUserInfo";
import { CopyDocument } from "@element-plus/icons-vue";
export default {
  components: {
    //
    CopyDocument,
  },
  inject: ["height"],
  data() {
    return {
      userInfo: getUserInfo(),

      tableData: [
        {
          field: "告警摘要",
          logField: "summary",
          desc: "Event的报警记录摘要",
          isNecessary: "必要",
          sample: "如： “Cpu_util at 85.16% on machine112.11.123.11”格式：{指标}at{指标值}on{告警对象}",
        },
        {
          field: "优先级",
          logField: "priority",
          desc: "Event 优先等级",
          isNecessary: "必要",
          sample: "如:(P1/P2/P3/P4)",
        },
        {
          field: "告警名称",
          logField: "name",
          desc: "Event名称(监控项的名称)",
          isNecessary: "非必要",
          sample: "如: Cpu_usage ;使用超额预警",
        },
        {
          field: "告警时间",
          logField: "timestamp",
          desc: "时间戳",
          isNecessary: "必要",
          sample: "如:2018-04-08T00:00:00Z",
        },
        {
          field: "告警地区",
          logField: "region",
          desc: "不同地域的产品字段",
          isNecessary: "非必要",
          sample: "如: shang hai /zhe jiang ..",
        },
        {
          field: "告警对象",
          logField: "source",
          desc: "Event告警触发对象，如主机ID或主机名",
          isNecessary: "必要",
          sample: "如: machine ************ (hostlD) or{hostname...}",
        },
        {
          field: "所属分组",
          logField: "group",
          desc: "告警对象的分组或集群",
          isNecessary: "非必要",
          sample: "如:日常/预发/线上 等分组",
        },
        {
          field: "所属应用",
          logField: "application",
          desc: "受影响的应用系统",
          isNecessary: "非必要",
          sample: "如: Zheda/Mozi",
        },
        {
          field: "告警分类",
          logField: "class",
          desc: "Event对应的指标类型",
          isNecessary: "非必要",
          sample: "如:CPU /Load / Diskl0 ...",
        },
        {
          field: "告警内容",
          logField: "details",
          desc: "Event 的内容详情 (key、value)",
          isNecessary: "必要",
          sample: "如:{“free space”: “1%”, “ping time”:“1500ms”, “load avg”: 0.75 )",
        },
        {
          field: "扩展字段",
          logField: "tags",
          desc: "扩展字段",
          isNecessary: "非必要",
          sample: "支持用户自定义扩展字段",
        },
      ],
      content: "标准集成",
      basicClassInput: { width: "35.8vw" } /* 输入框选择器基本样式 */,
      basicClassInputDown: { width: "20.8vw" } /* 输入框选择器基本样式 */,
      id: this.$route.params.id,
      detailObj: {},
      options: {
        STANDARD: "Zabbix",
        NET_CARE: "Netcare v6",
        PROMETHEUS: "Prometheus",
        N9E: "N9E",
        IDEAL_METRIC: "Open-Falcon",
        UNKNOWN: "Unknown",
      },
      jsonData: {
        summary: "Cpu_util at 85.16% on machine 112.11.123.11",
        priority: "p1",
        name: "Cpu 使用超额预警",
        timestamp: "2021-07-17 T08:42:58.315+0000",
        region: "shanghai",
        source: "112.11.123.11",
        application: "zheda/mozi",
        group: "线上",
        class: "Load",
        details: {
          freeSpace: "1%",
          pingTime: "1500ms",
          loadAvg: "0.75",
        },

        tags: [],
      },
    };
  },
  computed: {
    //
  },
  created() {
    //
    // // console.log(this.id);
  },
  mounted() {
    this.getDetails();
  },
  methods: {
    prohibit() {
      // console.log(this.detailObj);
      updateMointerSource({
        id: this.detailObj.id,
        enabled: !this.detailObj.enabled,
      }).then(({ success }) => {
        // // console.log(success);
        if (success) {
          this.$message.success("状态变更成功");
          this.getDetails();
        }
      });
    },
    secure() {
      // console.log(this.detailObj);
      if (this.detailObj.enabled) {
        this.$message(`已接入的集成监控工具禁用后才能被解除;解除该集成后原集成密钥将会失效将不再接收任何数据，
        请谨慎操作!
        `);
      } else {
        this.$confirm(
          `确认解除 "${this.options[this.detailObj.sourceType]}"吗？
        解除后，系统将不再接受其推送的任何数据，请谨慎操作!
        `,
          "解除集成",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            DeleteMointerSource({ id: this.detailObj.id }).then((res) => {
              if (res.code === 200 && res.success) {
                this.$message.success("解除集成成功");
                this.backRouter();
              }
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消解除",
            });
          });
      }
    },
    backRouter /* 返回上一页 */() {
      this.$router.go(-1);
    },
    getDetails() {
      MointerSourceDetails({ id: this.id }).then((res) => {
        if (res.code === 200 && res.success) {
          this.detailObj = { ...res.data };
        }
      });
    },
    copy(key) {
      if (navigator.clipboard && window.isSecureContext) {
        // navigator clipboard 向剪贴板写文本
        this.$message.success("复制成功");
        return navigator.clipboard.writeText(key);
      } else {
        // 创建text area
        const textArea = document.createElement("textarea");
        textArea.value = key;
        // 使text area不在viewport，同时设置不可见
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        this.$message.success("复制成功");
        return new Promise((res, rej) => {
          // 执行复制命令并移除文本框
          document.execCommand("copy") ? res() : rej();
          textArea.remove();
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .tabbar {
  position: fixed;
  width: 100%;
  bottom: 0;
  background: #fff;
  height: 72px;
}
::v-deep .elstyle-card {
  margin-top: 20px;
}
.priority-icon {
  width: 10px;
  height: 10px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 10px;
}

.modules-item {
  .modules-title {
    color: rgba(32, 128, 255, 1);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}
.step-list {
  .step {
    padding: 4px 12px;
    box-sizing: border-box;

    height: 40px;
    line-height: 40px;
    /* primary/cloudcare/fill/fill5 */

    background: #f7f8fa;
    border-radius: 2px;
    margin-top: 10px;
  }
  h3 {
    font-weight: 700;
    color: #1d2129;
  }
  color: #4e5969;
  font-family: "PingFang SC";
}
.modules-tips {
  // margin-left: 20px;
  padding: 9px 16px;
  margin-top: 8px;
  box-sizing: border-box;

  width: 1640px;
  height: 42px;

  /* c/fill/p3-0.1 */

  background: rgba(211, 187, 57, 0.1);
  border-radius: 2px;
  font-family: SourceHanSansSC-regular;

  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}

::v-deep .elstyle-form-item__content .el-form-item-content {
  display: flex;
  flex-direction: column;
}
</style>
