<template>
  <el-scrollbar :height="height">
    <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
      <pageTemplate v-model:currentPage="state.page" v-model:pageSize="state.size" :total="state.total" :height="height - 40" :show-paging="true" @size-change="handleCommand(command.Request)" @current-change="handleCommand(command.Request)">
        <template #left>
          <el-radio-group v-model="state.search.publishState" :style="{ whiteSpace: 'nowrap', flexWrap: 'nowrap' }" @change="handleQuery()">
            <el-radio-button label="">
              <el-badge type="primary" :value="publishStat.reduce((p, c) => p + c.count, 0)" :hidden="!publishStat.reduce((p, c) => p + c.count, 0)">
                <div class="tw-px-[1em]">{{ $t("el.table.clearFilter") }}</div>
              </el-badge>
            </el-radio-button>
            <el-radio-button v-for="item in publishStat" :key="item.value" :label="item.value">
              <el-badge type="primary" :value="item.count" :hidden="!item.count">
                <div class="tw-px-[1em]">{{ item.label }}</div>
              </el-badge>
            </el-radio-button>
          </el-radio-group>
        </template>
        <template #right>
          <span class="tw-h-fit">
            <!-- <el-button v-if="userInfo.hasPermission(智能事件中心_发布管理_新增)" :disabled="state.loading" type="primary" :icon="Plus" @click="handleCommand(command.Create, {})">{{ $t("glob.add") }}变更</el-button> -->
          </span>
          <span class="tw-ml-[16px] tw-h-fit">
            <el-button :disabled="state.loading" type="default" :icon="Refresh" @click="handleCommand(command.Refresh)" :title="$t('glob.refresh')"></el-button>
          </span>
        </template>
        <template #default="{ height: tableHeight }">
          <el-table v-loading="state.loading" ref="tableRef" @cell-contextmenu="copyClick" :data="dataList" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).map((v) => v.id))" @expand-change="handleExpand">
            <TableColumn type="selection" :width="55"></TableColumn>

            <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.priority" @filter-change="handleQuery()" prop="priority" label="优先级" :width="110" :filters="priorityOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>
            <!-- <TableColumn type="enum" prop="id" label="发布编号" :width="120"></TableColumn> -->
            <TableColumn type="condition" :list="<DataItem[]>[]" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderIds" @filter-change="handleQuery()" prop="identifier" column-key="identifier" label="发布编号" sortable="custom" :width="180" :filters="$filter0">
              <template #default="{ row, column }">
                <!-- <router-link v-if="row.id" :to="{ name: '519831507997556736', params: { id: row.id }, query: { fallback: route.name as string } }" custom> -->
                <!-- <template #default="{ href }"> -->
                <el-link type="primary" @click.stop="handleToOrder(row.orderType, row.id, row.tenantId)" target="_blank" :underline="false">{{ row[column.property] }}</el-link>
                <!-- </template> -->
                <!-- </router-link> -->
              </template>
            </TableColumn>

            <!-- <TableColumn type="condition" :list="<DataItem[]>[]" filter-multiple show-filter v-model:custom-filtered-value="searchByStates" @filter-change="handleQuery()" prop="publishState" label="状态" :width="120" :filters="$filter0">
              <template #default="{ row, column }">
                <el-tag :type="(find(publishStateOption, (v) => v.value === row[column.property]) || {}).type" size="small">{{ (find(publishStateOption, (v) => v.value === row[column.property]) || {}).label }}</el-tag>
              </template>
            </TableColumn> -->

            <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.state" @filter-change="handleQuery()" prop="publishState" label="状态" :width="120" :filters="publishStateOption.map((v) => ({ value: v.value, text: v.label }))" sortable="custom">
              <template #default="{ row, column }">
                <el-tag :type="(find(publishStateOption, (v) => v.value === row[column.property]) || {}).type" size="small">{{ (find(publishStateOption, (v) => v.value === row[column.property]) || {}).label }}</el-tag>
              </template>
            </TableColumn>

            <TableColumn type="condition" :list="<DataItem[]>[]" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderSummary" @filter-change="handleQuery()" prop="digest" column-key="digest" label="摘要" show-overflow-tooltip :min-width="120" :filters="$filter0"></TableColumn>

            <TableColumn type="condition" :label="t('userGroup.User group')" prop="userGroupName" :formatter="(_row, _col, _v) => (_v ? _v + (_row.userGroupTenantAbbreviation ? `[${_row.userGroupTenantAbbreviation}]` : '') : '--')" show-filter v-model:custom-filtered-value="searchByUserGroupName" :filters="$filter0" @filter-change="handleQuery()"> </TableColumn>
            <!-- <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByExternalId" @filter-change="handleQuery()" prop="externalId" :label="$t('generalDetails.External ID')" :filters="$filter0" :min-width="120"></TableColumn> -->
            <TableColumn type="condition" :label="t('orderGroup.Order group')" prop="ticketGroupName" show-filter v-model:custom-filtered-value="searchByTicketGroupName" :filters="$filter0" @filter-change="handleQuery()"> </TableColumn>

            <TableColumn type="condition" :list="<DataItem[]>[]" filter-multiple show-filter v-model:custom-filtered-value="searchByResponsibleName" @filter-change="handleQuery()" prop="responsibleName" label="负责人" :min-width="120" :filters="$filter0" :formatter="(_row, _col, _v) => (userInfo.hasPermission(安全管理中心_用户管理_可读) || userInfo.userId === _row.responsibleId ? (_v || '') + ((_row as any).responsibleTenantAbbreviation ? `[${(_row as any).responsibleTenantAbbreviation}]` : '') || '--' : '--')"></TableColumn>
            <TableColumn type="condition" :list="<DataItem[]>[]" filter-multiple show-filter v-model:custom-filtered-value="searchByActorName" @filter-change="handleQuery()" prop="actorName" label="处理人" :min-width="120" :filters="$filter0" :formatter="(_row, _col, _v) => (userInfo.hasPermission(安全管理中心_用户管理_可读) || userInfo.userId === _row.actorId ? (_v || '') + ((_row as any).actorTenantAbbreviation ? `[${(_row as any).actorTenantAbbreviation}]` : '') || '--' : '--')"></TableColumn>
            <TableColumn type="date" :list="<DataItem[]>[]" filter-multiple show-filter v-model:filtered-value="timeByCreate" @filter-change="handleQuery()" prop="createdTime" label="创建时间" sortable="custom" :width="140"></TableColumn>
            <TableColumn type="date" :list="<DataItem[]>[]" filter-multiple show-filter v-model:filtered-value="timeByUpdate" @filter-change="handleQuery()" prop="updatedTime" label="修改时间" sortable="custom" :width="140"></TableColumn>
          </el-table>
        </template>
      </pageTemplate>
    </el-card>
  </el-scrollbar>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch, computed, h, readonly, reactive, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Refresh, InfoFilled } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";
import { ElTable, ElIcon } from "element-plus";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox } from "element-plus";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { useSiteConfig } from "@/stores/siteConfig";
import getUserInfo from "@/utils/getUserInfo";
import { filter, find } from "lodash-es";
import moment from "moment";

// import { type BaseItem, DataItem, type Item } from "./helper";
// import { state, dataList, expand, select, current } from "./helper";
// import { resetData, handleExpand, handleSort, command } from "./helper";

/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
import { getChangeItems, priority, priorityOption, ChangeType, changeCreate as addItemData } from "@/views/pages/apis/change";

import { type PublishItem as DataItem, getPublishList as getItemList, publishState, publishStateOption, getPublishStateCount } from "@/views/pages/apis/publish";
import { /*addServiceData as addItemData,*/ setServiceData as setItemData, modServiceData as modItemData, delServiceData as delItemData } from "@/views/pages/apis/event";
import { 智能事件中心_发布管理_可读, 智能事件中心_发布管理_新增, 智能事件中心_发布管理_更新, 智能事件中心_发布管理_分配处理人, 智能事件中心_发布管理_创建审批, 智能事件中心_发布管理_审批, 智能事件中心_发布管理_编辑小记, 智能事件中心_发布管理_分配设备, 智能事件中心_发布管理_分配联系人, 智能事件中心_发布管理_关联工单, 智能事件中心_发布管理_安全, 智能事件中心_发布管理_所有权限, 智能事件中心_客户_工单可读, 安全管理中心_用户管理_可读 } from "@/views/pages/permission";
import _timeZoneHours from "@/views/pages/common/offsetHours.json";

import handleToOrder from "@/views/pages/alarm_convergence/IntelligentEvents/eventBoard/toOrder";

import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";

const timeZoneHours = ref(_timeZoneHours);
/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const { t } = useI18n({ useScope: "global" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "serviceRequest" });
const siteConfig = useSiteConfig();
const userInfo = getUserInfo();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);
interface Props {
  width?: number;
  height?: number;
  title?: string;
}
const props = withDefaults(defineProps<Props>(), { title: "告警" });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

const _width = inject("width", ref(0));
const _height = inject("height", ref(0));

const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);

const tableRef = ref<InstanceType<typeof ElTable>>();

/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

interface State<T, P> {
  loading: boolean;
  expand: string[];
  select: string[];
  current: string | undefined;
  search: P;
  sort?: { prop: string; order: "ascending" | "descending" };
  list: T[];
  page: number;
  size: number;
  total: number;
}
enum command {
  Refresh = "Refresh",
  Request = "Request",
  Preview = "Preview",
  Create = "Create",
  Update = "Update",
  Modify = "Modify",
  Delete = "Delete",
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const final = readonly({ pagination: false });

type ParamsData = Omit<typeof getItemList extends (req: infer P) => Promise<{ data: DataItem[] }> ? P : never, "type" | "paging" | "sort">;

const state = reactive<State<DataItem, ParamsData>>({
  loading: false,
  expand: [],
  select: [],
  current: undefined,
  search: {
    includeState: [],
    excludeState: [],
    eqState: [],
    neState: [],
    includeTenantName: [],
    excludeTenantName: [],
    eqTenantName: [],
    neTenantName: [],
    includeOrderId: [],
    excludeOrderId: [],
    eqOrderId: [],
    neOrderId: [],
    includeOrderSummary: [],
    excludeOrderSummary: [],
    eqOrderSummary: [],
    neOrderSummary: [],
    includeActorName: [],
    excludeActorName: [],
    eqActorName: [],
    neActorName: [],
    includeResponsibleName: [],
    excludeResponsibleName: [],
    eqResponsibleName: [],
    neResponsibleName: [],
    // includeExternalId: [],
    // excludeExternalId: [],
    // eqExternalId: [],
    // neExternalId: [],
    // externalIdFilterRelation: "AND",
    stateFilterRelation: "AND",
    tenantNameFilterRelation: "AND",
    orderIdFilterRelation: "AND",
    orderSummaryFilterRelation: "AND",
    actorNameFilterRelation: "AND",
    responsibleNameFilterRelation: "AND",
    eqAlarmCount: [],
    neAlarmCount: [],
    geAlarmCount: [],
    gtAlarmCount: [],
    leAlarmCount: [],
    ltAlarmCount: [],
    isNullAlarmCount: [],
    isNotNullAlarmCount: [],
    alarmCountFilterRelation: "AND",
    createTimeStart: "",
    createTimeEnd: "",
    updateTimeStart: "",
    updateTimeEnd: "",
    includeCompactActorName: [],
    excludeCompactActorName: [],
    eqCompactActorName: [],
    neCompactActorName: [],
    compactActorNameFilterRelation: "AND",
    compactTimeStart: "",
    compactTimeEnd: "",

    inTicketGroupName: [],
    excludeTicketGroupName: [],
    eqTicketGroupName: [],
    neTicketGroupName: [],
    ticketGroupNameFilterRelation: "AND",

    inUserGroupName: [],
    excludeUserGroupName: [],
    eqUserGroupName: [],
    neUserGroupName: [],
    userGroupNameFilterRelation: "AND",
  },
  sort: undefined,
  list: [],
  page: 1,
  size: 50,
  total: 0,
});
const dataList = computed(() => (final.pagination ? state.list.slice((state.page - 1) * state.size, state.page * state.size) : state.list));
const expand = computed(() => filter<DataItem>(state.list, (row: DataItem) => state.expand.includes(row.id)));
const select = computed(() => filter<DataItem>(state.list, (row: DataItem) => state.select.includes(row.id)));
const current = computed(() => find<DataItem>(state.list, (row: DataItem) => row.id === state.current));
// const name = computed(() => state.name);

const timeByCreate = computed({
  get: () => (state.search.createTimeStart && state.search.createTimeEnd ? { start: state.search.createTimeStart, end: state.search.createTimeEnd } : ""),
  set: (v) => {
    state.search.createTimeStart = (v || {}).start || "";
    state.search.createTimeEnd = (v || {}).end || "";
  },
});
const timeByUpdate = computed({
  get: () => (state.search.updateTimeStart && state.search.updateTimeEnd ? { start: state.search.updateTimeStart, end: state.search.updateTimeEnd } : ""),
  set: (v) => {
    state.search.updateTimeStart = (v || {}).start || "";
    state.search.updateTimeEnd = (v || {}).end || "";
  },
});

const searchType0ByTenantName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByTenantName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByTenantName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByTenantName) === "include") value0 = state.search.includeTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "exclude") value0 = state.search.excludeTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "eq") value0 = state.search.eqTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "ne") value0 = state.search.neTenantName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByTenantName) === "include") value1 = state.search.includeTenantName[state.search.includeTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "exclude") value1 = state.search.excludeTenantName[state.search.excludeTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "eq") value1 = state.search.eqTenantName[state.search.eqTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "ne") value1 = state.search.neTenantName[state.search.neTenantName.length - 1] || "";
    return {
      type0: toValue(searchType0ByTenantName),
      type1: toValue(searchType1ByTenantName),
      relation: state.search.tenantNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByTenantName.value = v.type0 as typeof searchType0ByTenantName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByTenantName.value = v.type1 as typeof searchType1ByTenantName extends import("vue").Ref<infer T> ? T : string;
    state.search.tenantNameFilterRelation = v.relation;
    state.search.includeTenantName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeTenantName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqTenantName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neTenantName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByOrderIds = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByOrderIds = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByOrderIds = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByOrderIds) === "include") value0 = state.search.includeOrderId[0] || "";
    if (toValue(searchType0ByOrderIds) === "exclude") value0 = state.search.excludeOrderId[0] || "";
    if (toValue(searchType0ByOrderIds) === "eq") value0 = state.search.eqOrderId[0] || "";
    if (toValue(searchType0ByOrderIds) === "ne") value0 = state.search.neOrderId[0] || "";
    let value1 = "";
    if (toValue(searchType1ByOrderIds) === "include") value1 = state.search.includeOrderId[state.search.includeOrderId.length - 1] || "";
    if (toValue(searchType1ByOrderIds) === "exclude") value1 = state.search.excludeOrderId[state.search.excludeOrderId.length - 1] || "";
    if (toValue(searchType1ByOrderIds) === "eq") value1 = state.search.eqOrderId[state.search.eqOrderId.length - 1] || "";
    if (toValue(searchType1ByOrderIds) === "ne") value1 = state.search.neOrderId[state.search.neOrderId.length - 1] || "";
    return {
      type0: toValue(searchType0ByOrderIds),
      type1: toValue(searchType1ByOrderIds),
      relation: state.search.orderIdFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByOrderIds.value = v.type0 as typeof searchType0ByOrderIds extends import("vue").Ref<infer T> ? T : string;
    searchType1ByOrderIds.value = v.type1 as typeof searchType1ByOrderIds extends import("vue").Ref<infer T> ? T : string;
    state.search.orderIdFilterRelation = v.relation;
    state.search.includeOrderId = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeOrderId = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqOrderId = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neOrderId = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByStates = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByStates = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByStates = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByStates) === "include") value0 = state.search.includeState[0] || "";
    if (toValue(searchType0ByStates) === "exclude") value0 = state.search.excludeState[0] || "";
    if (toValue(searchType0ByStates) === "eq") value0 = state.search.eqState[0] || "";
    if (toValue(searchType0ByStates) === "ne") value0 = state.search.neState[0] || "";
    let value1 = "";
    if (toValue(searchType1ByStates) === "include") value1 = state.search.includeState[state.search.includeState.length - 1] || "";
    if (toValue(searchType1ByStates) === "exclude") value1 = state.search.excludeState[state.search.excludeState.length - 1] || "";
    if (toValue(searchType1ByStates) === "eq") value1 = state.search.eqState[state.search.eqState.length - 1] || "";
    if (toValue(searchType1ByStates) === "ne") value1 = state.search.neState[state.search.neState.length - 1] || "";
    return {
      type0: toValue(searchType0ByStates),
      type1: toValue(searchType1ByStates),
      relation: state.search.stateFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByStates.value = v.type0 as typeof searchType0ByStates extends import("vue").Ref<infer T> ? T : string;
    searchType1ByStates.value = v.type1 as typeof searchType1ByStates extends import("vue").Ref<infer T> ? T : string;
    state.search.stateFilterRelation = v.relation;
    state.search.includeState = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeState = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqState = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neState = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByAlarmCount = ref<"eq" | "ne" | "ge" | "gt" | "le" | "lt" | "isNull" | "isNotNull">("eq");
const searchType1ByAlarmCount = ref<"eq" | "ne" | "ge" | "gt" | "le" | "lt" | "isNull" | "isNotNull">("eq");
const searchByAlarmCount = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByAlarmCount) === "eq") value0 = state.search.eqAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "ne") value0 = state.search.neAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "ge") value0 = state.search.geAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "gt") value0 = state.search.gtAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "le") value0 = state.search.leAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "lt") value0 = state.search.ltAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "isNull") value0 = state.search.isNullAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "isNotNull") value0 = state.search.isNotNullAlarmCount[0] || "";
    let value1 = "";
    if (toValue(searchType1ByAlarmCount) === "eq") value1 = state.search.eqAlarmCount[state.search.eqAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "ne") value1 = state.search.neAlarmCount[state.search.neAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "ge") value1 = state.search.geAlarmCount[state.search.geAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "gt") value1 = state.search.gtAlarmCount[state.search.gtAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "le") value1 = state.search.leAlarmCount[state.search.leAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "lt") value1 = state.search.ltAlarmCount[state.search.ltAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "isNull") value1 = state.search.isNullAlarmCount[state.search.isNullAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "isNotNull") value1 = state.search.isNotNullAlarmCount[state.search.isNotNullAlarmCount.length - 1] || "";
    return {
      type0: toValue(searchType0ByAlarmCount),
      type1: toValue(searchType1ByAlarmCount),
      relation: state.search.alarmCountFilterRelation,
      value0,
      value1,
      input0: "number",
      input1: "number",
    };
  },
  set: (v) => {
    searchType0ByAlarmCount.value = v.type0 as typeof searchType0ByAlarmCount extends import("vue").Ref<infer T> ? T : string;
    searchType1ByAlarmCount.value = v.type1 as typeof searchType1ByAlarmCount extends import("vue").Ref<infer T> ? T : string;
    state.search.alarmCountFilterRelation = v.relation;
    state.search.eqAlarmCount = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neAlarmCount = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
    state.search.geAlarmCount = [...(v.type0 === "ge" ? [v.value0] : []), ...(v.type1 === "ge" ? [v.value1] : [])];
    state.search.gtAlarmCount = [...(v.type0 === "gt" ? [v.value0] : []), ...(v.type1 === "gt" ? [v.value1] : [])];
    state.search.leAlarmCount = [...(v.type0 === "le" ? [v.value0] : []), ...(v.type1 === "le" ? [v.value1] : [])];
    state.search.ltAlarmCount = [...(v.type0 === "lt" ? [v.value0] : []), ...(v.type1 === "lt" ? [v.value1] : [])];
    state.search.isNullAlarmCount = [...(v.type0 === "isNull" ? [v.value0] : []), ...(v.type1 === "isNull" ? [v.value1] : [])];
    state.search.isNotNullAlarmCount = [...(v.type0 === "isNotNull" ? [v.value0] : []), ...(v.type1 === "isNotNull" ? [v.value1] : [])];
  },
});
const searchType0ByActorName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByActorName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByActorName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByActorName) === "include") value0 = state.search.includeActorName[0] || "";
    if (toValue(searchType0ByActorName) === "exclude") value0 = state.search.excludeActorName[0] || "";
    if (toValue(searchType0ByActorName) === "eq") value0 = state.search.eqActorName[0] || "";
    if (toValue(searchType0ByActorName) === "ne") value0 = state.search.neActorName[0] || "";
    let value1 = "";
    if (toValue(searchType0ByActorName) === "include") value1 = state.search.includeActorName[state.search.includeActorName.length - 1] || "";
    if (toValue(searchType0ByActorName) === "exclude") value1 = state.search.excludeActorName[state.search.excludeActorName.length - 1] || "";
    if (toValue(searchType0ByActorName) === "eq") value1 = state.search.eqActorName[state.search.eqActorName.length - 1] || "";
    if (toValue(searchType0ByActorName) === "ne") value1 = state.search.neActorName[state.search.neActorName.length - 1] || "";
    return {
      type0: toValue(searchType0ByActorName),
      type1: toValue(searchType1ByActorName),
      relation: state.search.actorNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByActorName.value = v.type0 as typeof searchType0ByActorName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByActorName.value = v.type1 as typeof searchType1ByActorName extends import("vue").Ref<infer T> ? T : string;
    state.search.actorNameFilterRelation = v.relation;
    state.search.includeActorName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeActorName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqActorName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neActorName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByResponsibleName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByResponsibleName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByResponsibleName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByResponsibleName) === "include") value0 = state.search.includeResponsibleName[0] || "";
    if (toValue(searchType0ByResponsibleName) === "exclude") value0 = state.search.excludeResponsibleName[0] || "";
    if (toValue(searchType0ByResponsibleName) === "eq") value0 = state.search.eqResponsibleName[0] || "";
    if (toValue(searchType0ByResponsibleName) === "ne") value0 = state.search.neResponsibleName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByResponsibleName) === "include") value1 = state.search.includeResponsibleName[state.search.includeResponsibleName.length - 1] || "";
    if (toValue(searchType1ByResponsibleName) === "exclude") value1 = state.search.excludeResponsibleName[state.search.excludeResponsibleName.length - 1] || "";
    if (toValue(searchType1ByResponsibleName) === "eq") value1 = state.search.eqResponsibleName[state.search.eqResponsibleName.length - 1] || "";
    if (toValue(searchType1ByResponsibleName) === "ne") value1 = state.search.neResponsibleName[state.search.neResponsibleName.length - 1] || "";
    return {
      type0: toValue(searchType0ByResponsibleName),
      type1: toValue(searchType1ByResponsibleName),
      relation: state.search.responsibleNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByResponsibleName.value = v.type0 as typeof searchType0ByResponsibleName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByResponsibleName.value = v.type1 as typeof searchType1ByResponsibleName extends import("vue").Ref<infer T> ? T : string;
    state.search.responsibleNameFilterRelation = v.relation;
    state.search.includeResponsibleName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeResponsibleName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqResponsibleName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neResponsibleName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByOrderSummary = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByOrderSummary = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByOrderSummary = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByOrderSummary) === "include") value0 = state.search.includeOrderSummary[0] || "";
    if (toValue(searchType0ByOrderSummary) === "exclude") value0 = state.search.excludeOrderSummary[0] || "";
    if (toValue(searchType0ByOrderSummary) === "eq") value0 = state.search.eqOrderSummary[0] || "";
    if (toValue(searchType0ByOrderSummary) === "ne") value0 = state.search.neOrderSummary[0] || "";
    let value1 = "";
    if (toValue(searchType1ByOrderSummary) === "include") value1 = state.search.includeOrderSummary[state.search.includeOrderSummary.length - 1] || "";
    if (toValue(searchType1ByOrderSummary) === "exclude") value1 = state.search.excludeOrderSummary[state.search.excludeOrderSummary.length - 1] || "";
    if (toValue(searchType1ByOrderSummary) === "eq") value1 = state.search.eqOrderSummary[state.search.eqOrderSummary.length - 1] || "";
    if (toValue(searchType1ByOrderSummary) === "ne") value1 = state.search.neOrderSummary[state.search.neOrderSummary.length - 1] || "";
    return {
      type0: toValue(searchType0ByOrderSummary),
      type1: toValue(searchType1ByOrderSummary),
      relation: state.search.orderSummaryFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByOrderSummary.value = v.type0 as typeof searchType0ByOrderSummary extends import("vue").Ref<infer T> ? T : string;
    searchType1ByOrderSummary.value = v.type1 as typeof searchType1ByOrderSummary extends import("vue").Ref<infer T> ? T : string;
    state.search.orderSummaryFilterRelation = v.relation;
    state.search.includeOrderSummary = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeOrderSummary = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqOrderSummary = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neOrderSummary = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
//外部ID  开始
const searchType0ByExternalId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByExternalId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByExternalId = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByExternalId) === "include") value0 = state.search.includeExternalId[0] || "";
    if (toValue(searchType0ByExternalId) === "exclude") value0 = state.search.excludeExternalId[0] || "";
    if (toValue(searchType0ByExternalId) === "eq") value0 = state.search.eqExternalId[0] || "";
    if (toValue(searchType0ByExternalId) === "ne") value0 = state.search.neExternalId[0] || "";
    let value1 = "";
    if (toValue(searchType1ByExternalId) === "include") value1 = state.search.includeExternalId[state.search.includeExternalId.length - 1] || "";
    if (toValue(searchType1ByExternalId) === "exclude") value1 = state.search.excludeExternalId[state.search.excludeExternalId.length - 1] || "";
    if (toValue(searchType1ByExternalId) === "eq") value1 = state.search.eqExternalId[state.search.eqExternalId.length - 1] || "";
    if (toValue(searchType1ByExternalId) === "ne") value1 = state.search.neExternalId[state.search.neExternalId.length - 1] || "";
    return {
      type0: toValue(searchType0ByExternalId),
      type1: toValue(searchType1ByExternalId),
      relation: state.search.externalIdFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByExternalId.value = v.type0 as typeof searchType0ByExternalId extends import("vue").Ref<infer T> ? T : string;
    searchType1ByExternalId.value = v.type1 as typeof searchType1ByExternalId extends import("vue").Ref<infer T> ? T : string;
    state.search.externalIdFilterRelation = v.relation;
    state.search.includeExternalId = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeExternalId = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqExternalId = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neExternalId = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
//外部ID  结束
const searchType0ByUserGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByUserGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByUserGroupName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByUserGroupName) === "include") value0 = state.search.inUserGroupName[0] || "";
    if (toValue(searchType0ByUserGroupName) === "exclude") value0 = state.search.excludeUserGroupName[0] || "";
    if (toValue(searchType0ByUserGroupName) === "eq") value0 = state.search.eqUserGroupName[0] || "";
    if (toValue(searchType0ByUserGroupName) === "ne") value0 = state.search.neUserGroupName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByUserGroupName) === "include") value1 = state.search.inUserGroupName[state.search.inUserGroupName.length - 1] || "";
    if (toValue(searchType1ByUserGroupName) === "exclude") value1 = state.search.excludeUserGroupName[state.search.excludeUserGroupName.length - 1] || "";
    if (toValue(searchType1ByUserGroupName) === "eq") value1 = state.search.eqUserGroupName[state.search.eqUserGroupName.length - 1] || "";
    if (toValue(searchType1ByUserGroupName) === "ne") value1 = state.search.neUserGroupName[state.search.neUserGroupName.length - 1] || "";
    return {
      type0: toValue(searchType0ByUserGroupName),
      type1: toValue(searchType1ByUserGroupName),
      relation: state.search.userGroupNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByUserGroupName.value = v.type0 as typeof searchType0ByUserGroupName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByUserGroupName.value = v.type1 as typeof searchType1ByUserGroupName extends import("vue").Ref<infer T> ? T : string;
    state.search.userGroupNameFilterRelation = v.relation;
    state.search.inUserGroupName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeUserGroupName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqUserGroupName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neUserGroupName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByTicketGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByTicketGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByTicketGroupName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByTicketGroupName) === "include") value0 = state.search.inTicketGroupName[0] || "";
    if (toValue(searchType0ByTicketGroupName) === "exclude") value0 = state.search.excludeTicketGroupName[0] || "";
    if (toValue(searchType0ByTicketGroupName) === "eq") value0 = state.search.eqTicketGroupName[0] || "";
    if (toValue(searchType0ByTicketGroupName) === "ne") value0 = state.search.neTicketGroupName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByTicketGroupName) === "include") value1 = state.search.inTicketGroupName[state.search.inTicketGroupName.length - 1] || "";
    if (toValue(searchType1ByTicketGroupName) === "exclude") value1 = state.search.excludeTicketGroupName[state.search.excludeTicketGroupName.length - 1] || "";
    if (toValue(searchType1ByTicketGroupName) === "eq") value1 = state.search.eqTicketGroupName[state.search.eqTicketGroupName.length - 1] || "";
    if (toValue(searchType1ByTicketGroupName) === "ne") value1 = state.search.neTicketGroupName[state.search.neTicketGroupName.length - 1] || "";
    return {
      type0: toValue(searchType0ByTicketGroupName),
      type1: toValue(searchType1ByTicketGroupName),
      relation: state.search.ticketGroupNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByTicketGroupName.value = v.type0 as typeof searchType0ByTicketGroupName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByTicketGroupName.value = v.type1 as typeof searchType1ByTicketGroupName extends import("vue").Ref<infer T> ? T : string;
    state.search.ticketGroupNameFilterRelation = v.relation;
    state.search.inTicketGroupName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeTicketGroupName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqTicketGroupName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neTicketGroupName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
function handleExpand(row: DataItem, expandedRows: DataItem[]) {
  state.expand = expandedRows.filter((v) => v).map(({ id }) => id);
  if (find(expandedRows, ({ id }) => row.id === id)) {
    /*  */
  } else {
    /*  */
  }
}
function handleSort(sort: { prop: string; order: "ascending" | "descending" }) {
  state.sort = sort.prop ? sort : undefined;
}
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
async function resetData() {
  state.list = [];
  state.page = 1;
  state.size = 50;
  state.total = 0;

  state.search.includeTenantName = [];
  state.search.excludeTenantName = [];
  state.search.eqTenantName = [];
  state.search.neTenantName = [];
  state.search.includeOrderId = [];
  state.search.excludeOrderId = [];
  state.search.eqOrderId = [];
  state.search.neOrderId = [];
  state.search.includeState = [];
  state.search.excludeState = [];
  state.search.eqState = [];
  state.search.neState = [];
  state.search.includeActorName = [];
  state.search.excludeActorName = [];
  state.search.eqActorName = [];
  state.search.neActorName = [];
  state.search.includeResponsibleName = [];
  state.search.excludeResponsibleName = [];
  state.search.eqResponsibleName = [];
  state.search.neResponsibleName = [];
  state.search.includeOrderSummary = [];
  state.search.excludeOrderSummary = [];
  state.search.eqOrderSummary = [];
  state.search.neOrderSummary = [];
  state.search.tenantNameFilterRelation = "AND";
  // state.search.includeExternalId = [];
  // state.search.excludeExternalId = [];
  // state.search.eqExternalId = [];
  // state.search.neExternalId = [];
  // state.search.externalIdFilterRelation = "AND";
  state.search.orderIdFilterRelation = "AND";
  state.search.stateFilterRelation = "AND";
  state.search.actorNameFilterRelation = "AND";
  state.search.responsibleNameFilterRelation = "AND";
  state.search.orderSummaryFilterRelation = "AND";
  state.search.eqAlarmCount = [];
  state.search.neAlarmCount = [];
  state.search.geAlarmCount = [];
  state.search.gtAlarmCount = [];
  state.search.leAlarmCount = [];
  state.search.ltAlarmCount = [];
  state.search.isNullAlarmCount = [];
  state.search.isNotNullAlarmCount = [];
  state.search.alarmCountFilterRelation = "AND";
  state.search.createTimeStart = "";
  state.search.createTimeEnd = "";
  state.search.updateTimeStart = "";
  state.search.updateTimeEnd = "";

  // for (const key in state.search) {
  //   if (Object.prototype.hasOwnProperty.call(state.search, key)) {
  //     delete state.search[key];
  //   }
  // }
  await nextTick();
}
/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */

/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const timer = ref<null | number>(null);
const autoRefreshTime = ref(0);

const publishStat = ref<{ label: string; value: publishState; color?: string; count: number }[]>([]);

// const dataList = ref([]);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  handleRefresh().then(() => (autoRefreshTime.value = 60));
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {
  if (timer.value !== null) {
    window.clearInterval(timer.value);
    timer.value = null;
  }
}
function beforeDestroy() {
  if (timer.value !== null) {
    window.clearInterval(timer.value);
    timer.value = null;
  }
}

function ruoterOrder(val) {
  const routeData = router.resolve({ name: "519831507997556736", params: { id: val }, query: { fallback: route.name as string } });

  window.open(routeData.href, val);
}
function destroyed() {}

watch(autoRefreshTime, (autoRefreshTime) => {
  if (timer.value !== null) timer.value = (window.clearInterval(timer.value), null);
  if (autoRefreshTime) timer.value = window.setInterval(queryData, autoRefreshTime * 1000);
});
function timeZoneSwitching(): number {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == userInfo.zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
async function handleCommand(type: command, data?: Record<string, unknown>) {
  const time = autoRefreshTime.value;
  autoRefreshTime.value = 0;
  try {
    state.loading = true;
    await nextTick();
    switch (type) {
      case command.Refresh:
        await resetData();
        await queryData();
        break;
      case command.Request:
        await queryData();
        break;
      case command.Preview:
        // await previewItem(data as Record<string, unknown>);
        break;
      case command.Create:
        // await createItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
      case command.Update:
        // await rewriteItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
      case command.Modify:
        // await modifyItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
      case command.Delete:
        // await deleteItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
    }
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await resetData();
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
      await queryData();
    }
  } finally {
    autoRefreshTime.value = time;
    state.loading = false;
  }
}
async function handleRefresh() {
  try {
    state.loading = true;
    await nextTick();
    await resetData();
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
async function handleQuery() {
  try {
    state.loading = true;
    state.page = 1;
    await nextTick();
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
async function queryData() {
  try {
    let sort: string[] = [];
    switch ((state.sort || {}).order) {
      case "ascending":
        sort.push(`${String(state.sort?.prop)},asc`);
        if (state.sort?.prop === "createTime") {
          sort.shift();

          sort.push(`createdTime,asc`);
          // delete sort.createTime;
        } else if (state.sort?.prop === "updateTime") {
          sort.shift();

          sort.push(`updatedTime,asc`);
          // delete sort.updateTime;
        }
        break;
      case "descending":
        sort.push(`${String(state.sort?.prop)},desc`);
        if (state.sort?.prop === "createTime") {
          sort.shift();

          sort.push(`createdTime,desc`);
          // delete sort.createTime;
        } else if (state.sort?.prop === "updateTime") {
          sort.shift();

          sort.push(`updatedTime,desc`);
          // delete sort.updateTime;
        }
        break;
    }
    const [{ success, message, data, page, size, total }, { success: statSuccess, message: statMessage, data: statData }] = await Promise.all([getItemList({ ...state.search, sort, paging: { pageNumber: state.page, pageSize: state.size } }), getPublishStateCount({})]);
    // if (success) {
    //   dataList.value = [...data];
    // }

    if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
    if (!statSuccess) throw Object.assign(new Error(statMessage), { success: statSuccess, data: statData });

    publishStat.value = statData.map((v): { label: string; value: publishState; color?: string; count: number } => {
      const typeItem = find(publishStateOption, ({ value }) => v.publishState === value) || { label: v.publishState as string, value: v.publishState };
      return { count: Number(v.count) || 0, label: typeItem.label, value: typeItem.value as publishState };
    });

    const select = new Set((data instanceof Array ? data : []).filter((v) => state.select.includes(v.id)).map((v) => v.id));

    // state.list.splice(0, Infinity, ...(data instanceof Array ? data : []));
    state.list.splice(0, state.list.length, ...(data instanceof Array ? data : []).map((v) => Object.assign(v, { updatedTime: Number(v.updatedTime) + timeZoneSwitching() }, { createdTime: Number(v.createdTime) + timeZoneSwitching() })));

    state.page = Number(page) || 1;
    state.size = Number(size) || 20;
    state.total = Number(total) || 0;

    await nextTick();
    if (tableRef.value) {
      tableRef.value.clearSelection();
      for (let i = 0; i < state.list.length; i++) {
        tableRef.value.toggleRowSelection(state.list[i], select.has(state.list[i].id));
      }
    }
    state.select = Array.from(select);
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  }
}

function copyClick(row, column, cell, event) {
  event.stopPropagation();
}

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: DataItem): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
