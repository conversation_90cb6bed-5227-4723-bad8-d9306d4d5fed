<template>
  <el-dialog v-model="dialogVisible" title="报障详情" width="500" :before-close="handleClose">
    <el-form :model="detail" v-loading="loading">
      <el-form-item label="工单标题">{{ detail.failureTitle }}</el-form-item>
      <el-form-item label="工单描述">{{ detail.failureDescription }}</el-form-item>
      <el-form-item label="紧急程度">
        <el-text
          :type="
            {
              VERY_URGENT: 'danger',
              URGENT: 'warning',
              NORMAL: 'primary',
              NOT_URGENT: 'info',
            }[detail.urgency]
          "
        >
          <div :class="`order-state ${'state-' + detail.urgency}`">
            {{ (urgencyOption.find((v) => v.value === detail.urgency) || {}).label || detail.urgency }}
          </div>
        </el-text>
      </el-form-item>
      <el-form-item label="联系人姓名">{{ detail.contactName }}</el-form-item>
      <el-form-item label="联系人电话"
        ><el-text type="primary">{{ detail.contactPhone }}</el-text>
      </el-form-item>
      <el-form-item label="联系人姓名" v-if="detail.spareContactName">{{ detail.spareContactName }}</el-form-item>
      <el-form-item label="联系人电话" v-if="detail.spareContactPhone"
        ><el-text type="primary">{{ detail.spareContactPhone }}</el-text>
      </el-form-item>
      <el-form-item label="报障人电话"
        ><el-text type="primary">{{ detail.reportFaultPhone }}</el-text></el-form-item
      >
      <el-form-item label="工单状态">
        <el-tag :type="(statusOption.find((v) => v.value === detail.status) || {}).type || 'primary'">{{ (statusOption.find((v) => v.value === detail.status) || {}).label || detail.status }}</el-tag>
      </el-form-item>
      <el-form-item label="无效原因" v-if="detail.reason">
        {{ detail.reason }}
      </el-form-item>
      <el-form-item label="修复情况" v-if="detail.dispose">
        {{ detail.dispose }}
      </el-form-item>
      <el-divider />
      <div class="tw-mb-2">
        <span class="el-dialog__title"> 客户项目信息 </span>
      </div>
      <el-form-item label="项目名称">{{ detail.projectName }}</el-form-item>
      <el-form-item label="统一服务编码">{{ detail.unificationCode }}</el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <template v-if="![Status.DEPRECATED].includes(detail.status) && userInfo.hasPermission(智能事件中心_客户_二维码报障)">
          <el-button type="danger" v-if="!detail.kebaoCode" @click="handleUpdate(props.handleQrcodeAbandon)">废 弃</el-button>
          <el-button v-if="[Status.NEW].includes(detail.status)" type="primary" @click="handleUpdate(props.handleQrcodeUpdate)">已报障</el-button>
          <el-button v-if="[Status.REPORTED].includes(detail.status) && !detail.kebaoCode" type="primary" @click="handleUpdate(props.handleQrcodeUpdate)">客保工单号</el-button>
          <el-button type="primary" v-if="[Status.REPORTED].includes(detail.status)" @click="handleUpdate(props.handleQrcodeRepair)">已修复</el-button>
        </template>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";

import { urgencyOption, Status, statusOption } from "@/views/pages/apis/qrcode";

import { getQrcodeItem } from "@/views/pages/apis/qrcode";
import { ElMessage } from "element-plus";

import getUserInfo from "@/utils/getUserInfo";

import { 智能事件中心_客户_二维码报障 } from "@/views/pages/permission";

const userInfo = getUserInfo();

interface Props {
  handleQrcodeAbandon: Function;
  handleQrcodeUpdate: Function;
  handleQrcodeRepair: Function;
}

const props = withDefaults(defineProps<Props>(), {
  handleQrcodeAbandon: () => {},
  handleQrcodeUpdate: () => {},
  handleQrcodeRepair: () => {},
});

const loading = ref(false);

async function handleUpdate(fun) {
  const result = await fun(detail.value);
  if (result) handleRefresh(detail.value.id);
}

const dialogVisible = ref<boolean>(false);

const detail = ref<Record<string, any>>({});

function handleOpen({ id }) {
  dialogVisible.value = true;
  handleRefresh(id);
}

function handleClose() {
  dialogVisible.value = false;
}

async function handleRefresh(id) {
  try {
    loading.value = true;
    const { success, data, message } = await getQrcodeItem({ id });
    if (!success) throw new Error(message);
    detail.value = data;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}

defineExpose({
  open: handleOpen,
});
</script>

<style lang="scss" scoped>
.order-state {
  border-radius: 2px;
  opacity: 1;
  // background: rgba(30, 205, 132, 0.1);
  display: inline-block;
  padding: 2px 6px;
  font-family: 思源黑体;
  font-size: 12px;
  font-weight: normal;
  line-height: 14px;
  letter-spacing: 0em;
  // color: #1ECD84;
  margin-right: 6px;
}

.state-VERY_URGENT {
  background: rgba(213, 73, 65, 0.1);
  color: #d54941;
}

.state-URGENT {
  background: rgba(243, 173, 60, 0.1);
  color: #f3ad3c;
}

.state-NORMAL {
  background: rgba(30, 205, 132, 0.1);
  color: #1ecd84;
}

.state-NOT_URGENT {
  background: rgba(139, 139, 139, 0.1);
  color: #8b8b8b;
}
</style>
