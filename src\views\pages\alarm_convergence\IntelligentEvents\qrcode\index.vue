<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="state.page" v-model:pageSize="state.size" :total="state.total" :height="height - 40" :show-paging="true" @size-change="handleRefresh" @current-change="handleRefresh">
      <template #left>
        <div>
          <el-switch v-model="state.search.isAbandon" inline-prompt active-value="ALL" active-text="所有报障单" inactive-value="DELETED" inactive-text="废弃报障单" @change="handleIsAbandonChange" />
        </div>
      </template>
      <template #right>
        <div>
          <el-input v-model="state.search.code" style="width: 300px" placeholder="请输入统一服务编号、工单号查询" class="input-with-select">
            <template #append>
              <el-button :icon="Search" @click="handleRefresh" />
            </template>
          </el-input>
          <el-button class="tw-ml-2" :icon="Refresh" @click="handleRefresh"></el-button>
        </div>
      </template>

      <template #default="{ height: tableHeight }">
        <el-table v-loading="state.loading" ref="tableRef" :data="state.list" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleRefresh)" @selection-change="(select) => (state.select = (select as DataItem[]).map((v) => v.id))">
          <!-- <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByAbbreviation" @filter-change="handleQuery()" prop="tenantName" label="客户" show-overflow-tooltip :filters="$filter0" :min-width="100"></TableColumn> -->

          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByName" @filter-change="handleQuery()" prop="projectName" label="项目名称" show-overflow-tooltip :filters="$filter0" :min-width="100"></TableColumn>

          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByUnificationCode" @filter-change="handleQuery()" prop="unificationCode" label="统一服务编码" show-overflow-tooltip :filters="$filter0" width="150"></TableColumn>

          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.urgency" @filter-change="handleQuery()" prop="urgency" label="优先级" :width="110" :filters="urgencyOption.map((v) => ({ ...v, text: v.label }))">
            <template #default="{ row }">
              <div :class="`order-state ${'state-' + row.urgency}`">
                {{ (urgencyOption.find((v) => v.value === row.urgency) || {}).label || row.urgency }}
              </div>
            </template>
          </TableColumn>

          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.status" @filter-change="handleQuery()" prop="urgency" label="状态" :width="110" :filters="statusOption.map((v) => ({ ...v, text: v.label }))">
            <template #default="{ row }">
              <el-tag :type="(statusOption.find((v) => v.value === row.status) || {}).type || 'primary'">{{ (statusOption.find((v) => v.value === row.status) || {}).label || row.status }}</el-tag>
            </template>
          </TableColumn>

          <!-- <TableColumn type="default" prop="status" label="状态">
              <template #default="{ row }">
                <el-tag :type="(statusOption.find((v) => v.value === row.status) || {}).type || 'primary'">{{ (statusOption.find((v) => v.value === row.status) || {}).label || row.status }}</el-tag>
              </template>
            </TableColumn> -->
          <!-- searchByKebaoCode -->
          <!-- <TableColumn type="default" prop="kebaoCode" label="客保工单号"></TableColumn> -->
          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByKebaoCode" @filter-change="handleQuery()" prop="kebaoCode" label="客保工单号" show-overflow-tooltip :filters="$filter0" :min-width="120"></TableColumn>

          <!-- <TableColumn type="default" prop="failureTitle" label="工单标题"></TableColumn>
            <TableColumn type="default" prop="failureDescription" label="工单描述"></TableColumn> -->
          <!-- searchByFailureTitle
searchByFailureDescription -->

          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByFailureTitle" @filter-change="handleQuery()" prop="failureTitle" label="工单标题" show-overflow-tooltip :filters="$filter0" :min-width="120"></TableColumn>
          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByFailureDescription" @filter-change="handleQuery()" prop="failureDescription" label="工单描述" show-overflow-tooltip :filters="$filter0" :min-width="120"></TableColumn>

          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByChargePerson" @filter-change="handleQuery()" prop="chargePerson" label="负责人" :filters="$filter0" :min-width="120" :formatter="(_row, _col, _v) => (userInfo.hasPermission(安全管理中心_用户管理_可读) || userInfo.userId === _row.chargePersonId ? _v : '--')"></TableColumn>
          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByProcessors" @filter-change="handleQuery()" prop="processors" label="处理人" :filters="$filter0" :min-width="120" :formatter="(_row, _col, _v) => (userInfo.hasPermission(安全管理中心_用户管理_可读) || userInfo.userId === _row.processorsId ? _v : '--')"></TableColumn>
          <TableColumn type="date" prop="createTime" label="时间" width="200"></TableColumn>
          <TableColumn label="查看" width="100" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link @click="() => dateilRef.open(row)">详情</el-button>
            </template>
          </TableColumn>
          <TableColumn type="default" label="操作" width="200" fixed="right" v-if="userInfo.hasPermission(智能事件中心_二维码报障_更新)">
            <template #default="{ row }">
              <div>
                <template v-if="![Status.DEPRECATED].includes(row.status)">
                  <el-button type="danger" link :icon="CloseBold" v-if="!row.kebaoCode" @click="handleQrcodeAbandon(row as any)">废弃</el-button>
                  <el-button type="primary" link :icon="CircleCheck" v-if="[Status.NEW].includes(row.status)" @click="handleQrcodeUpdate(row as any)">已报障</el-button>
                  <el-button type="primary" link :icon="EditPen" v-if="[Status.REPORTED].includes(row.status) && !row.kebaoCode" @click="handleQrcodeUpdate(row as any)">客保工单号</el-button>
                  <el-button type="primary" link :icon="CircleCheck" v-if="row.kebaoCode && [Status.REPORTED].includes(row.status)" @click="handleQrcodeRepair(row as any)">已修复</el-button>
                </template>
              </div>
            </template>
          </TableColumn>
        </el-table>
      </template>
    </pageTemplate>

    <detail ref="dateilRef" :handleQrcodeAbandon="handleQrcodeAbandon" :handleQrcodeUpdate="handleQrcodeUpdate" :handleQrcodeRepair="handleQrcodeRepair"></detail>
  </el-card>
</template>

<script setup lang="ts">
import { ref, inject, computed, reactive, onMounted, defineComponent, h, toValue, nextTick } from "vue";

import { Search, Refresh, CloseBold, CircleCheck, EditPen, WarningFilled } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, ElForm, ElFormItem, ElInput, ElIcon, FormInstance } from "element-plus";

import { type QrcodeItem as DataItem, getQrcode as getData, getQrcodeList as getDataByFilter, setQrcode as setData, urgencyOption, Status, statusOption } from "@/views/pages/apis/qrcode";

import pageTemplate from "@/components/pageTemplate.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";

import detail from "./detail.vue";

import getUserInfo from "@/utils/getUserInfo";

import { 智能事件中心_二维码报障_可读, 智能事件中心_二维码报障_更新, 智能事件中心_工单看板_我的工单, 智能事件中心_工单看板_全部工单, 安全管理中心_用户管理_可读 } from "@/views/pages/permission";

import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";

const userInfo = getUserInfo();

interface Props {
  width?: number;
  height?: number;
  title?: string;
}
const props = withDefaults(defineProps<Props>(), { title: "二维码报障" });

const _width = inject("width", ref(0));
const _height = inject("height", ref(0));

const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);

const dateilRef = ref();

const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);
const $filter2 = ref([
  { text: "包含", value: "include" },
  { text: "不包含", value: "exclude" },
]);

interface State<T, P> {
  loading: boolean;
  expand: string[];
  select: string[];
  current: string | undefined;
  search: P;
  sort?: { prop: string; order: "ascending" | "descending" };
  list: T[];
  page: number;
  size: number;
  total: number;
}

const state = reactive<State<DataItem, Record<string, any>>>({
  loading: false,
  expand: [],
  select: [],
  current: undefined,
  search: {
    isAbandon: "ALL",
    code: "",
    type: "all",
    status: "",
    urgency: "",
    eqName: [],
    includeName: [],
    nameFilterRelation: "AND",
    neName: [],
    excludeName: [],
    eqFailureTitle: [],
    includeFailureTitle: [],
    failureTitleFilterRelation: "AND",
    neFailureTitle: [],
    excludeFailureTitle: [],
    eqFailureDescription: [],
    includeFailureDescription: [],
    failureDescriptionFilterRelation: "AND",
    neFailureDescription: [],
    excludeFailureDescription: [],
    eqUnificationCode: [],
    includeUnificationCode: [],
    unificationCodeFilterRelation: "AND",
    neUnificationCode: [],
    excludeUnificationCode: [],
    eqKebaoCode: [],
    includeKebaoCode: [],
    kebaoCodeFilterRelation: "AND",
    neKebaoCode: [],
    excludeKebaoCode: [],
    chargePerson: "",
    processors: "",

    eqAbbreviation: [],
    includeAbbreviation: [],
    neAbbreviation: [],
    excludeAbbreviation: [],
    abbreviationFilterRelation: "AND",

    eqChargePerson: [],
    includeChargePerson: [],
    chargePersonFilterRelation: "AND",
    neChargePerson: [],
    excludeChargePerson: [],

    eqProcessors: [],
    includeProcessors: [],
    processorsFilterRelation: "AND",
    neProcessors: [],
    excludeProcessors: [],
  },
  sort: undefined,
  list: [],
  page: 1,
  size: 50,
  total: 0,
});

const searchType0ByAbbreviation = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByAbbreviation = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByAbbreviation = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByAbbreviation) === "include") value0 = state.search.includeAbbreviation[0] || "";
    if (toValue(searchType0ByAbbreviation) === "exclude") value0 = state.search.excludeAbbreviation[0] || "";
    if (toValue(searchType0ByAbbreviation) === "eq") value0 = state.search.eqAbbreviation[0] || "";
    if (toValue(searchType0ByAbbreviation) === "ne") value0 = state.search.neAbbreviation[0] || "";
    let value1 = "";
    if (toValue(searchType1ByAbbreviation) === "include") value1 = state.search.includeAbbreviation[state.search.includeAbbreviation.length - 1] || "";
    if (toValue(searchType1ByAbbreviation) === "exclude") value1 = state.search.excludeAbbreviation[state.search.excludeAbbreviation.length - 1] || "";
    if (toValue(searchType1ByAbbreviation) === "eq") value1 = state.search.eqAbbreviation[state.search.eqAbbreviation.length - 1] || "";
    if (toValue(searchType1ByAbbreviation) === "ne") value1 = state.search.neAbbreviation[state.search.neAbbreviation.length - 1] || "";
    return {
      type0: toValue(searchType0ByAbbreviation),
      type1: toValue(searchType1ByAbbreviation),
      relation: state.search.abbreviationFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByAbbreviation.value = v.type0 as typeof searchType0ByAbbreviation extends import("vue").Ref<infer T> ? T : string;
    searchType1ByAbbreviation.value = v.type1 as typeof searchType1ByAbbreviation extends import("vue").Ref<infer T> ? T : string;
    state.search.abbreviationFilterRelation = v.relation;
    state.search.includeAbbreviation = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeAbbreviation = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqAbbreviation = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neAbbreviation = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByName) === "include") value0 = state.search.includeName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = state.search.excludeName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = state.search.eqName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = state.search.neName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = state.search.includeName[state.search.includeName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = state.search.excludeName[state.search.excludeName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = state.search.eqName[state.search.eqName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = state.search.neName[state.search.neName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: state.search.nameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    state.search.nameFilterRelation = v.relation;
    state.search.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// eqUnificationCode: [],
//     includeUnificationCode: [],
//     unificationCodeFilterRelation: "AND",
//     neUnificationCode: [],
//     excludeUnificationCode: [],
const searchType0ByUnificationCode = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByUnificationCode = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByUnificationCode = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByUnificationCode) === "include") value0 = state.search.includeUnificationCode[0] || "";
    if (toValue(searchType0ByUnificationCode) === "exclude") value0 = state.search.excludeUnificationCode[0] || "";
    if (toValue(searchType0ByUnificationCode) === "eq") value0 = state.search.eqUnificationCode[0] || "";
    if (toValue(searchType0ByUnificationCode) === "ne") value0 = state.search.neUnificationCode[0] || "";
    let value1 = "";
    if (toValue(searchType1ByUnificationCode) === "include") value1 = state.search.includeUnificationCode[state.search.includeUnificationCode.length - 1] || "";
    if (toValue(searchType1ByUnificationCode) === "exclude") value1 = state.search.excludeUnificationCode[state.search.excludeUnificationCode.length - 1] || "";
    if (toValue(searchType1ByUnificationCode) === "eq") value1 = state.search.eqUnificationCode[state.search.eqUnificationCode.length - 1] || "";
    if (toValue(searchType1ByUnificationCode) === "ne") value1 = state.search.neUnificationCode[state.search.neUnificationCode.length - 1] || "";
    return {
      type0: toValue(searchType0ByUnificationCode),
      type1: toValue(searchType1ByUnificationCode),
      relation: state.search.unificationCodeFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByUnificationCode.value = v.type0 as typeof searchType0ByUnificationCode extends import("vue").Ref<infer T> ? T : string;
    searchType1ByUnificationCode.value = v.type1 as typeof searchType1ByUnificationCode extends import("vue").Ref<infer T> ? T : string;
    state.search.unificationCodeFilterRelation = v.relation;
    state.search.includeUnificationCode = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeUnificationCode = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqUnificationCode = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neUnificationCode = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByKebaoCode = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByKebaoCode = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByKebaoCode = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByKebaoCode) === "include") value0 = state.search.includeKebaoCode[0] || "";
    if (toValue(searchType0ByKebaoCode) === "exclude") value0 = state.search.excludeKebaoCode[0] || "";
    if (toValue(searchType0ByKebaoCode) === "eq") value0 = state.search.eqKebaoCode[0] || "";
    if (toValue(searchType0ByKebaoCode) === "ne") value0 = state.search.neKebaoCode[0] || "";
    let value1 = "";
    if (toValue(searchType1ByKebaoCode) === "include") value1 = state.search.includeKebaoCode[state.search.includeKebaoCode.length - 1] || "";
    if (toValue(searchType1ByKebaoCode) === "exclude") value1 = state.search.excludeKebaoCode[state.search.excludeKebaoCode.length - 1] || "";
    if (toValue(searchType1ByKebaoCode) === "eq") value1 = state.search.eqKebaoCode[state.search.eqKebaoCode.length - 1] || "";
    if (toValue(searchType1ByKebaoCode) === "ne") value1 = state.search.neKebaoCode[state.search.neKebaoCode.length - 1] || "";
    return {
      type0: toValue(searchType0ByKebaoCode),
      type1: toValue(searchType1ByKebaoCode),
      relation: state.search.kebaoCodeFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByKebaoCode.value = v.type0 as typeof searchType0ByKebaoCode extends import("vue").Ref<infer T> ? T : string;
    searchType1ByKebaoCode.value = v.type1 as typeof searchType1ByKebaoCode extends import("vue").Ref<infer T> ? T : string;
    state.search.kebaoCodeFilterRelation = v.relation;
    state.search.includeKebaoCode = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeKebaoCode = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqKebaoCode = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neKebaoCode = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// eqFailureTitle: [],
//     includeFailureTitle: [],
//     failureTitleFilterRelation: "AND",
//     neFailureTitle: [],
//     excludeFailureTitle: [],

const searchType0ByFailureTitle = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByFailureTitle = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByFailureTitle = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByFailureTitle) === "include") value0 = state.search.includeFailureTitle[0] || "";
    if (toValue(searchType0ByFailureTitle) === "exclude") value0 = state.search.excludeFailureTitle[0] || "";
    if (toValue(searchType0ByFailureTitle) === "eq") value0 = state.search.eqFailureTitle[0] || "";
    if (toValue(searchType0ByFailureTitle) === "ne") value0 = state.search.neFailureTitle[0] || "";
    let value1 = "";
    if (toValue(searchType1ByFailureTitle) === "include") value1 = state.search.includeFailureTitle[state.search.includeFailureTitle.length - 1] || "";
    if (toValue(searchType1ByFailureTitle) === "exclude") value1 = state.search.excludeFailureTitle[state.search.excludeFailureTitle.length - 1] || "";
    if (toValue(searchType1ByFailureTitle) === "eq") value1 = state.search.eqFailureTitle[state.search.eqFailureTitle.length - 1] || "";
    if (toValue(searchType1ByFailureTitle) === "ne") value1 = state.search.neFailureTitle[state.search.neFailureTitle.length - 1] || "";
    return {
      type0: toValue(searchType0ByFailureTitle),
      type1: toValue(searchType1ByFailureTitle),
      relation: state.search.failureTitleFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByFailureTitle.value = v.type0 as typeof searchType0ByFailureTitle extends import("vue").Ref<infer T> ? T : string;
    searchType1ByFailureTitle.value = v.type1 as typeof searchType1ByFailureTitle extends import("vue").Ref<infer T> ? T : string;
    state.search.failureTitleFilterRelation = v.relation;
    state.search.includeFailureTitle = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeFailureTitle = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqFailureTitle = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neFailureTitle = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// eqFailureDescription: [],
//     includeFailureDescription: [],
//     failureDescriptionFilterRelation: "AND",
//     neFailureDescription: [],
//     excludeFailureDescription: [],

const searchType0ByFailureDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByFailureDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByFailureDescription = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByFailureDescription) === "include") value0 = state.search.includeFailureDescription[0] || "";
    if (toValue(searchType0ByFailureDescription) === "exclude") value0 = state.search.excludeFailureDescription[0] || "";
    if (toValue(searchType0ByFailureDescription) === "eq") value0 = state.search.eqFailureDescription[0] || "";
    if (toValue(searchType0ByFailureDescription) === "ne") value0 = state.search.neFailureDescription[0] || "";
    let value1 = "";
    if (toValue(searchType1ByFailureDescription) === "include") value1 = state.search.includeFailureDescription[state.search.includeFailureDescription.length - 1] || "";
    if (toValue(searchType1ByFailureDescription) === "exclude") value1 = state.search.excludeFailureDescription[state.search.excludeFailureDescription.length - 1] || "";
    if (toValue(searchType1ByFailureDescription) === "eq") value1 = state.search.eqFailureDescription[state.search.eqFailureDescription.length - 1] || "";
    if (toValue(searchType1ByFailureDescription) === "ne") value1 = state.search.neFailureDescription[state.search.neFailureDescription.length - 1] || "";
    return {
      type0: toValue(searchType0ByFailureDescription),
      type1: toValue(searchType1ByFailureDescription),
      relation: state.search.failureDescriptionFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByFailureDescription.value = v.type0 as typeof searchType0ByFailureDescription extends import("vue").Ref<infer T> ? T : string;
    searchType1ByFailureDescription.value = v.type1 as typeof searchType1ByFailureDescription extends import("vue").Ref<infer T> ? T : string;
    state.search.failureDescriptionFilterRelation = v.relation;
    state.search.includeFailureDescription = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeFailureDescription = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqFailureDescription = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neFailureDescription = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// eqChargePerson: [],
//     includeChargePerson: [],
//     chargePersonFilterRelation: "AND",
//     neChargePerson: [],
//     excludeChargePerson: [],

const searchType0ByChargePerson = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByChargePerson = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByChargePerson = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByChargePerson) === "include") value0 = state.search.includeChargePerson[0] || "";
    if (toValue(searchType0ByChargePerson) === "exclude") value0 = state.search.excludeChargePerson[0] || "";
    if (toValue(searchType0ByChargePerson) === "eq") value0 = state.search.eqChargePerson[0] || "";
    if (toValue(searchType0ByChargePerson) === "ne") value0 = state.search.neChargePerson[0] || "";
    let value1 = "";
    if (toValue(searchType1ByChargePerson) === "include") value1 = state.search.includeChargePerson[state.search.includeChargePerson.length - 1] || "";
    if (toValue(searchType1ByChargePerson) === "exclude") value1 = state.search.excludeChargePerson[state.search.excludeChargePerson.length - 1] || "";
    if (toValue(searchType1ByChargePerson) === "eq") value1 = state.search.eqChargePerson[state.search.eqChargePerson.length - 1] || "";
    if (toValue(searchType1ByChargePerson) === "ne") value1 = state.search.neChargePerson[state.search.neChargePerson.length - 1] || "";
    return {
      type0: toValue(searchType0ByChargePerson),
      type1: toValue(searchType1ByChargePerson),
      relation: state.search.chargePersonFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByChargePerson.value = v.type0 as typeof searchType0ByChargePerson extends import("vue").Ref<infer T> ? T : string;
    searchType1ByChargePerson.value = v.type1 as typeof searchType1ByChargePerson extends import("vue").Ref<infer T> ? T : string;
    state.search.chargePersonFilterRelation = v.relation;
    state.search.includeChargePerson = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeChargePerson = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqChargePerson = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neChargePerson = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// eqProcessors: [],
//     includeProcessors: [],
//     processorsFilterRelation: "AND",
//     neProcessors: [],
//     excludeProcessors: [],

const searchType0ByProcessors = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByProcessors = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByProcessors = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByProcessors) === "include") value0 = state.search.includeProcessors[0] || "";
    if (toValue(searchType0ByProcessors) === "exclude") value0 = state.search.excludeProcessors[0] || "";
    if (toValue(searchType0ByProcessors) === "eq") value0 = state.search.eqProcessors[0] || "";
    if (toValue(searchType0ByProcessors) === "ne") value0 = state.search.neProcessors[0] || "";
    let value1 = "";
    if (toValue(searchType1ByProcessors) === "include") value1 = state.search.includeProcessors[state.search.includeProcessors.length - 1] || "";
    if (toValue(searchType1ByProcessors) === "exclude") value1 = state.search.excludeProcessors[state.search.excludeProcessors.length - 1] || "";
    if (toValue(searchType1ByProcessors) === "eq") value1 = state.search.eqProcessors[state.search.eqProcessors.length - 1] || "";
    if (toValue(searchType1ByProcessors) === "ne") value1 = state.search.neProcessors[state.search.neProcessors.length - 1] || "";
    return {
      type0: toValue(searchType0ByProcessors),
      type1: toValue(searchType1ByProcessors),
      relation: state.search.processorsFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByProcessors.value = v.type0 as typeof searchType0ByProcessors extends import("vue").Ref<infer T> ? T : string;
    searchType1ByProcessors.value = v.type1 as typeof searchType1ByProcessors extends import("vue").Ref<infer T> ? T : string;
    state.search.processorsFilterRelation = v.relation;
    state.search.includeProcessors = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeProcessors = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqProcessors = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neProcessors = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const abandonFormRef = ref();
const abandonForm = ref<{ reason: string; status: keyof typeof Status }>({
  status: Status.DEPRECATED,
  reason: "",
});

function handleQrcodeAbandon({ id }) {
  return new Promise((resolve) => {
    abandonForm.value.reason = "";

    ElMessageBox({
      title: `废弃`,
      message: h(
        defineComponent({
          setup() {
            return () => [h("div", { class: "tw-flex tw-items-center tw-mb-2" }, [h("div", { class: "tw-px-4" }, [h(ElIcon, { color: "#f0ad4e", size: "36" }, [h(WarningFilled)])]), h("div", "报障单废弃后，不可继续报障。")]), h(ElForm, { model: abandonForm, ref: (v) => (abandonFormRef.value = v), rules: { reason: [{ required: true, message: "请输入废弃原因", trigger: ["change", "blur"] }] }, labelPosition: "top" }, [h(ElFormItem, { label: "废弃原因", prop: "reason" }, [h(ElInput, { "modelValue": abandonForm.value.reason, "onUpdate:modelValue": (v) => (abandonForm.value.reason = v) })])])];
          },
        })
      ),
      beforeClose: async (action, instance, done) => {
        if (action === "confirm") {
          abandonFormRef.value.validate(async (valid) => {
            if (!valid) return;
            try {
              const { message, success } = await setData({ ...abandonForm.value, id });
              if (!success) throw new Error(message);
              ElMessage.success("操作成功");
              handleRefresh();
              done();
              resolve(true);
            } catch (error) {
              error instanceof Error && ElMessage.error(error.message);
            }
          });
        } else {
          done();
          resolve(false);
        }
      },
    })
      .then(() => {})
      .catch(() => {});
  });
}

function handleIsAbandonChange(v) {
  if (v === "DELETED") state.search.status = Status.DEPRECATED;
  else state.search.status = "";
  handleRefresh();
}

const updateFormRef = ref<FormInstance>();
const updateForm = ref<{ kebaoCode: string; status: keyof typeof Status }>({
  status: Status.REPORTED,
  kebaoCode: "",
});

function handleQrcodeUpdate({ status, id }) {
  return new Promise((resolve) => {
    updateForm.value.kebaoCode = "";

    const isReported = status === Status.REPORTED; // 客保工单号必填
    ElMessageBox({
      title: !isReported ? "已报障" : "客保工单号",
      message: h(
        defineComponent({
          setup() {
            return () => h(ElForm, { model: updateForm, ref: (v) => (updateFormRef.value = v as FormInstance), rules: { kebaoCode: [{ required: isReported, message: "请输入客保工单号", trigger: ["change", "blur"] }] } }, [h(ElFormItem, { label: "客保工单号", prop: "kebaoCode" }, [h(ElInput, { "modelValue": updateForm.value.kebaoCode, "onUpdate:modelValue": (v) => (updateForm.value.kebaoCode = v) })])]);
          },
        })
      ),
      beforeClose: async (action, instance, done) => {
        if (action === "confirm") {
          updateFormRef.value &&
            updateFormRef.value.validate(async (valid) => {
              if (!valid) return;
              try {
                const { message, success } = await setData({ ...updateForm.value, id });
                if (!success) throw new Error(message);
                ElMessage.success("操作成功");
                handleRefresh();
                done();
                resolve(true);

                updateFormRef.value && updateFormRef.value.resetFields();
              } catch (error) {
                error instanceof Error && ElMessage.error(error.message);
              }
            });
        } else {
          done();
          resolve(false);
        }
      },
    })
      .then(() => {})
      .catch(() => {});
  });
}

const repairFormRef = ref<FormInstance>();
const repairForm = ref<{ dispose: string; status: keyof typeof Status }>({
  status: Status.FIXED,
  dispose: "",
});
function handleQrcodeRepair({ id }) {
  return new Promise((resolve) => {
    ElMessageBox({
      title: "已修复",
      message: h(
        defineComponent({
          setup() {
            return () => [h("div", { class: "tw-flex tw-items-center tw-mb-2" }, [h("div", { class: "tw-px-4" }, [h(ElIcon, { color: "#f0ad4e", size: "36" }, [h(CircleCheck)])]), h("div", "需填写故障处理情况，同步给用户。")]), h(ElForm, { model: repairForm, ref: (v) => (repairFormRef.value = v as FormInstance), rules: { dispose: [{ required: true, message: "请输入修复情况", trigger: ["change", "blur"] }] }, labelPosition: "top" }, [h(ElFormItem, { label: "修复情况", prop: "dispose" }, [h(ElInput, { "modelValue": repairForm.value.dispose, "onUpdate:modelValue": (v) => (repairForm.value.dispose = v) })])])];
          },
        })
      ),
      beforeClose: async (action, instance, done) => {
        if (action === "confirm") {
          repairFormRef.value &&
            repairFormRef.value.validate(async (valid) => {
              if (!valid) return;
              try {
                const { message, success } = await setData({ ...repairForm.value, id });
                if (!success) throw new Error(message);
                ElMessage.success("操作成功");
                repairFormRef.value && repairFormRef.value.resetFields();
                handleRefresh();
                done();
                resolve(true);
              } catch (error) {
                error instanceof Error && ElMessage.error(error.message);
              }
            });
        } else {
          done();
          resolve(false);
        }
      },
    })
      .then(() => {})
      .catch(() => {});
  });
}

async function handleQuery() {
  state.page = 1;
  await nextTick();
  await handleRefresh();
}

async function handleRefresh() {
  try {
    // const type = userInfo.hasPermission(智能事件中心_工单看板_全部工单) ? "all" : userInfo.hasPermission(智能事件中心_工单看板_我的工单) ? "mine" : "";

    // if (!type) return;

    state.loading = true;
    const { success, message, data, total } = await /* state.search.code
      ? getData({
          pageNumber: state.page,
          pageSize: state.size,
          status: state.search.isAbandon,
          code: state.search.code,
        })
      : */ getDataByFilter(
      Object.assign(
        {
          type: "all",
          pageNumber: state.page,
          pageSize: state.size,
        },
        { ...state.search }
      ) as any
    );

    if (!success) throw new Error(message);
    state.list = data;
    state.total = Number(total);
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    state.loading = false;
  }
}

onMounted(() => {
  handleRefresh();
});
</script>

<style lang="scss" scoped>
.order-state {
  border-radius: 2px;
  opacity: 1;
  // background: rgba(30, 205, 132, 0.1);
  display: inline-block;
  padding: 2px 6px;
  font-family: 思源黑体;
  font-size: 12px;
  font-weight: normal;
  line-height: 14px;
  letter-spacing: 0em;
  // color: #1ECD84;
  margin-right: 6px;
}

.state-VERY_URGENT {
  background: rgba(213, 73, 65, 0.1);
  color: #d54941;
}

.state-URGENT {
  background: rgba(243, 173, 60, 0.1);
  color: #f3ad3c;
}

.state-NORMAL {
  background: rgba(30, 205, 132, 0.1);
  color: #1ecd84;
}

.state-NOT_URGENT {
  background: rgba(139, 139, 139, 0.1);
  color: #8b8b8b;
}
</style>
