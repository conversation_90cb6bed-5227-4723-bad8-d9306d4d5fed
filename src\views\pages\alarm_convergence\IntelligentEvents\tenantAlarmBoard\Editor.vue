<template>
  <!-- 对话框表单 -->
  <component :is="ComponentRender" v-model:visible="data.visible" :handle-cancel="handleCancel">
    <template #header>
      <!-- {{ `${$params.id ? t("glob.edit") : t("glob.add")}${props.title}` }} -->
      {{ `${props.title}` }}
    </template>
    <template #default="{ width }">
      <FormModel ref="formRef" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @submit="handleFinish">
        <FormGroup :span="24" :label="'告警统计'" tooltip="">
          <template #default="{}">
            <FormItem :span="width > 600 ? 12 : 24" :label="`告警`" tooltip="" prop="alertCount" :rules="[]">
              <div class="tw-flex tw-w-full tw-items-center tw-justify-end">
                <el-tag type="" effect="dark" round>{{ $params.alertCount }}</el-tag>
              </div>
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" :label="`设备数`" tooltip="" prop="deviceCount" :rules="[]">
              <div class="tw-flex tw-w-full tw-items-center tw-justify-end">
                <el-tag type="" effect="dark" round>{{ $params.deviceCount }}</el-tag>
              </div>
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" :label="`客户数`" tooltip="" prop="tenantCount" :rules="[]">
              <div class="tw-flex tw-w-full tw-items-center tw-justify-end">
                <el-tag type="" effect="dark" round>{{ $params.tenantCount }}</el-tag>
              </div>
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" :label="`已分配告警的处理中事件数`" tooltip="" prop="processEventCount" :rules="[]">
              <div class="tw-flex tw-w-full tw-items-center tw-justify-end">
                <el-tag type="" effect="dark" round>{{ $params.processEventCount }}</el-tag>
              </div>
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" :label="`已分配告警的未接手的事件数`" tooltip="" prop="waitingForReceiveCount" :rules="[]">
              <div class="tw-flex tw-w-full tw-items-center tw-justify-end">
                <el-tag type="danger" effect="dark" round>{{ $params.waitingForReceiveCount }}</el-tag>
              </div>
            </FormItem>
          </template>
        </FormGroup>
        <FormGroup :span="24" :label="'告警的处理规则'" tooltip="">
          <template #default="{}">
            <FormItem :span="24" label-width="0px" tooltip="" prop="processEventConfirm" :rules="[]">
              <el-checkbox v-model="form.processEventConfirm" :disabled="!$params.processEventConfirm" label="同步确认处理中事件下的告警" :indeterminate="false"></el-checkbox>
            </FormItem>
            <FormItem :span="24" label-width="0px" tooltip="" prop="waitingForReceiveConfirm" :rules="[]">
              <el-checkbox v-model="form.waitingForReceiveConfirm" :disabled="!$params.waitingForReceiveConfirm" label="同步确认未接手事件下的告警并将事件状态置为处理中" :indeterminate="false"></el-checkbox>
            </FormItem>
            <FormItem :span="24" label-width="0px" tooltip="" :rules="[]">
              <el-checkbox :model-value="[...($params.assignedToEvent ? [form.assignedToEvent] : []), ...($params.createEvent ? [form.createEvent] : [])].every((v) => v)" :indeterminate="[...($params.assignedToEvent ? [form.assignedToEvent] : []), ...($params.createEvent ? [form.createEvent] : [])].every((v) => v) ? false : [...($params.assignedToEvent ? [form.assignedToEvent] : []), ...($params.createEvent ? [form.createEvent] : [])].some((v) => v)" :disabled="!$params.assignedToEvent && !$params.createEvent" @update:model-value="($event) => ($params.assignedToEvent && (form.assignedToEvent = !!$event), $params.createEvent && (form.createEvent = !!$event))" label="未分配事件的告警"></el-checkbox>
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" label-width="0px" tooltip="" prop="assignedToEvent" :rules="[]">
              <div class="tw-w-full tw-pl-[48px]">
                <el-checkbox v-model="form.assignedToEvent" :disabled="!$params.assignedToEvent" label="分配告警到事件" :indeterminate="false"></el-checkbox>
              </div>
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" label-width="0px" tooltip="" prop="createEvent" :rules="[]">
              <div class="tw-w-full tw-pl-[48px]">
                <el-checkbox v-model="form.createEvent" :disabled="!$params.createEvent" label="创建事件" :indeterminate="false"></el-checkbox>
              </div>
            </FormItem>
          </template>
        </FormGroup>
      </FormModel>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </component>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, computed, h } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { ElMessage } from "element-plus";
import { buildTypeHelper } from "@/utils/type";

import { buildValidatorData } from "@/utils/validate";

import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import getUserInfo from "@/utils/getUserInfo";

import { getAlertStat } from "@/views/pages/apis/event";

const userInfo = getUserInfo();
const { t } = useI18n();
const formRef = ref<InstanceType<typeof FormModel>>();

interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 120,
});

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Item {
  ids: string[];

  alertCount: number /* 告警数 */;
  deviceCount: number /* 设备数 */;
  tenantCount: number /* 租户数（客户数） */;
  processEventCount: number /* 处理中的事件数 */;
  waitingForReceiveCount: number /* 未接手的事件数 */;

  processEventConfirm: boolean /* 同步确认处理中事件下的告警 */;
  waitingForReceiveConfirm: boolean /* 同步确认未接手事件下的告警并将事件状态置为处理中 */;
  assignedToEvent: boolean /* 分配告警到事件 */;
  createEvent: boolean /* 创建事件 */;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  ids: buildTypeHelper<Required<Item>["ids"]>([]),
  alertCount: buildTypeHelper<Required<Item>["alertCount"]>(0),
  deviceCount: buildTypeHelper<Required<Item>["deviceCount"]>(0),
  tenantCount: buildTypeHelper<Required<Item>["tenantCount"]>(0),
  processEventCount: buildTypeHelper<Required<Item>["processEventCount"]>(0),
  waitingForReceiveCount: buildTypeHelper<Required<Item>["waitingForReceiveCount"]>(0),
  processEventConfirm: buildTypeHelper<Required<Item>["processEventConfirm"]>(false),
  waitingForReceiveConfirm: buildTypeHelper<Required<Item>["waitingForReceiveConfirm"]>(false),
  assignedToEvent: buildTypeHelper<Required<Item>["assignedToEvent"]>(false),
  createEvent: buildTypeHelper<Required<Item>["createEvent"]>(false),
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (params) {
    return;
  }
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  if ($params.value.ids) {
    const { success, message, data } = await getAlertStat({ ids: params.ids as string[] });
    if (!success) throw new Error(message);
    $params.value = {
      ids: $params.value.ids,
      ...data,
    };
    await getForm(data);
  }
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  return form;
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 窗口方法
 */
interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<Item>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Record<string, unknown>) => void;
  reject?: (value: Error) => void;
  callback?: (form: Record<string, unknown>) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Record<string, unknown>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(function (formResult, [key, util]) {
    if (!util) return formResult;
    else if (util.test(formResult[key])) return formResult;
    else return Object.assign(formResult, { [key]: util.transfer(formResult[key], util.value as never) });
  }, form as Required<Item>);
}

async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => (formRef.value ? formRef.value.validate(resolve) : resolve(false)));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
    // console.log(form, 5555);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, callback?: (form: Record<string, unknown>) => Promise<void>) {
    if (data.visible) {
      return await new Promise((resolve) => {
        ElMessage.warning("先关闭其他弹窗再重试！");
        resolve(params);
      });
    } else {
      $params.value = cloneDeep(params);
      await resetFormInit($params.value);
      // // console.log(params, 55555);
      data.visible = true;
      data.loading = true;
      data.submitLoading = true;
      data.callback = callback;
      try {
        return await new Promise((resolve, reject) => {
          data.resolve = resolve;
          data.reject = reject;
          nextTick(async () => {
            await nextTick();
            try {
              await runningInit($params.value);
            } catch (error) {
              if (error instanceof Error) ElMessage.error(error.message);
              handleCancel();
            }
            handleReset();
            data.loading = false;
            data.submitLoading = false;
          });
        });
      } catch (error) {
        return error;
      }
    }
  },
});
</script>

<style scoped lang="scss">
.edit_sla_config {
  :deep() {
    .el-input-number {
      width: 50px !important;

      .elstyle-input {
        width: 50px;

        input {
          padding: 0;
        }
      }
    }

    .el-input-number__increase {
      display: none;
    }

    .el-input-number__decrease {
      display: none;
    }

    .el-card {
      margin-top: 20px;
    }

    .el-form-item__content .el-form-item-content {
      display: flex;
      flex-direction: column;
    }

    .el-table .cell {
      padding: 0 !important;
    }

    .el-table .el-table__cell {
      padding: 0 !important;
      height: 50px;
    }
  }
}
.priority-icon {
  width: 10px;
  height: 10px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 10px;
}
.state {
  padding: 2px 10px;
  box-sizing: border-box;
  border-radius: 20px;
}
.modules-item {
  .modules-title {
    color: rgba(32, 128, 255, 1);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}
.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}
</style>
