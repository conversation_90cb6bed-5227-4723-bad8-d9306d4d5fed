<template>
  <el-scrollbar :height="height">
    <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
      <pageTemplate v-model:currentPage="state.page" v-model:pageSize="state.size" :total="state.total" :height="height - 40" :show-paging="true" @size-change="handleCommand(command.Request)" @current-change="handleCommand(command.Request)">
        <!-- <template #left>
          <el-input v-model="state.search.alert" :disabled="state.loading" :placeholder="$t('glob.Please input field', { field: $t('glob.Keyword') })" @keyup.enter="handleQuery()">
            <template #append>
              <el-button :icon="Search" :disabled="state.loading" @click.stop="handleQuery()" />
            </template>
          </el-input>
        </template> -->
        <template #right>
          <el-button v-if="userInfo.hasPermission(监控管理中心_告警_可读)" :disabled="state.loading" type="primary" @click="routerV6alarmHistoryAll(userInfo.currentTenantId)">{{ $t("alarm.History Alert") }}</el-button>
        </template>
        <template #default="{ height: tableHeight }">
          <el-table v-loading="state.loading" ref="tableRef" :data="dataList" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).filter((v) => !v.alarmBoardConfirmed).map((v) => v.id))" @expand-change="handleExpand">
            <!-- <TableColumn type="selection" :width="55" :selectable="(row: DataItem) => !row.alarmBoardConfirmed && row.confirm && row.eventSeverity!='Normal'"></TableColumn> -->
            <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.eventSeverity" @filter-change="handleQuery()" prop="eventSeverity" :label="t('alarm.Severity')" :width="130" :filters="eventSeverityOption.map((v) => ({ ...v, text: v.label }))"></TableColumn>
            <!-- <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByTenantName" @filter-change="handleQuery()" prop="tenantName" label="客户名称" :width="130" :filters="$filter0"></TableColumn> -->
            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByDeviceOrIp" @filter-change="handleQuery()" prop="deviceId" :label="t('alarm.Device')" :width="180" :filters="$filter2">
              <template #default="{ row }">
                <!-- <pre>{{ row }}</pre> -->
                <router-link v-if="row.id && row.deviceId" custom :to="{ name: '509596457372745728', params: { id: row.deviceId }, query: { fallback: route.name as string, tenant: row.tenantId as string } }">
                  <template #default="{ href }">
                    <p>
                      <el-link type="primary" :underline="false" :href target="_blank">{{ row.deviceName || "--" }}</el-link>
                    </p>
                    <p>
                      <el-link type="primary" :underline="false" :href target="_blank">{{ row.deviceIp || "--" }}</el-link>
                    </p>
                  </template>
                </router-link>
                <div style="cursor: pointer; display: flex">
                  <template v-if="userInfo.hasPermission(资产管理中心_设备_工具权限)">
                    <span class="tw-h-fit">
                      <el-button type="primary" link>
                        <deviceTools :item="row" :list="deskTopObj.length" :show="row.showDesktop" :active="row.active"></deviceTools>
                      </el-button>
                    </span>
                    <span class="tw-h-fit">
                      <el-button type="primary" link @click="ping(row)">
                        <Icon class="tw-mx-[2px]" name="local-DeviceWifi-line" :color="row.active ? 'var(--el-color-primary)' : '#888'"></Icon>
                      </el-button>
                    </span>
                    <span class="tw-h-fit">
                      <el-button type="primary" link @click="routeDevice(row)">
                        <Icon class="tw-mx-[2px]" name="local-SystemShare-line" :color="row.active ? 'var(--el-color-primary)' : '#888'"></Icon>
                      </el-button>
                    </span>
                  </template>
                  <span class="tw-h-fit">
                    <el-button type="primary" link @click="routerV6Busines(row.deviceName)">
                      <Icon class="tw-mx-[2px]" name="local-DocumentNumbers-line" color="var(--el-color-primary)"></Icon>
                    </el-button>
                  </span>
                  <span class="tw-h-fit">
                    <el-button type="primary" link @click="preview(row.deviceId)">
                      <Icon class="tw-mx-[2px]" name="local-SystemError-warning-line" color="var(--el-color-primary)"></Icon>
                    </el-button>
                  </span>

                  <span class="tw-h-fit">
                    <el-button type="primary" link @click="contancts(row.deviceId)">
                      <Icon :disabled="true" class="tw-mx-[2px]" name="local-UserUser-3-line" color="var(--el-color-primary)"></Icon>
                    </el-button>
                  </span>

                  <!-- <deviceTools :item="row"></deviceTools> -->
                </div>
              </template>
            </TableColumn>
            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByAlert" @filter-change="handleQuery()" prop="title" :label="t('alarm.Alert')" :filters="$filter2">
              <template #default="{ row }">
                <el-link type="default" :underline="false" @click.stop>{{ row.title }}</el-link>
                <div>{{ row.desc }}</div>
              </template>
            </TableColumn>
            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderId" @filter-change="handleQuery()" prop="order" :label="t('alarm.Ticket')" sortable="custom" :width="180" :filters="$filter2">
              <template #default="{ row }">
                <template v-if="userInfo.hasPermission(智能事件中心_客户_工单可读) || (userInfo.hasPermission('756062918394511360' as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))">
                  <div style="display: flex; flex-direction: column">
                    <div v-if="!(row as DataItem).orders.length">--</div>
                    <div v-for="order in (row as DataItem).orders" :key="order.orderId">
                      <el-tag class="orderPriority" :color="priorityColor[order.priority].color" style="color: #fff; border: 0">{{ order.priority }}</el-tag>
                      <span v-for="val in orderState" :key="val.value">
                        <el-tag v-show="val.value === order.state">{{ val.label }}</el-tag>
                      </span>
                      <el-button type="primary" link @click="handleToOrder(routerPath[order.orderType], { id: order.orderId }, row.tenantId)">{{ order.orderId }}</el-button>
                      <!-- <router-link custom :to="{ name: routerPath[order.orderType], params: { id: order.orderId }, query: { fallback: route.name as string, tenant: order.tenantId } }">
                      <template #default="{ href }">
                        <el-link type="primary" :underline="false" :href="href" target="_blank" class="clipboard tw-ml-[6px]">{{ order.orderId }}</el-link>
                      </template>
                    </router-link> -->
                    </div>
                  </div>
                </template>
                <template v-else>--</template>
              </template>
            </TableColumn>
            <TableColumn type="date" show-filter v-model:filtered-value="state.search.alertCreateTimeRange" filter-multiple @filter-change="handleQuery()" prop="alertCreateTime" :label="t('alarm.Timestamp')" sortable="custom" :width="160"></TableColumn>
            <!-- <TableColumn type="date" show-filter v-model:filtered-value="state.search.alarmBoardConfirmedTime" filter-multiple @filter-change="handleQuery()" prop="alarmBoardConfirmedTime" label="告警确认时间" sortable="custom" :width="160"></TableColumn> -->
            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByResponseUser" @filter-change="handleQuery()" :label="t('alarm.Acked By')" :width="100" :filters="$filter2">
              <template #default="{ row }">
                <div v-if="userInfo.hasPermission(安全管理中心_用户管理_可读)">{{ row.alarmBoardConfirmedPerson.account }}</div>
              </template>
            </TableColumn>
            <!-- <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByResponseUser" @filter-change="handleQuery()" prop="alarmBoardConfirmedPerson" label="响应人" :width="100" :filters="$filter2"></TableColumn> -->
          </el-table>
        </template>
      </pageTemplate>
    </el-card>
    <Editor ref="editorRef" title="告警确认" display="dialog"></Editor>

    <deviceDetials ref="deviceDetialsRef"></deviceDetials>
    <deviceContacts ref="deviceContactsRef"></deviceContacts>
    <devicePing ref="devicePingRef"></devicePing>
    <deviceRoute ref="deviceRouteRef"></deviceRoute>
  </el-scrollbar>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch, computed, readonly, reactive, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Refresh } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";
import { ElTable } from "element-plus";
import Editor from "./Editor.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox } from "element-plus";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { filter, find } from "lodash-es";
import moment from "moment";
import getUserInfo from "@/utils/getUserInfo";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
import { eventSeverity, eventSeverityOption } from "@/views/pages/apis/event";
import { type AlertItem as $DataItem, getAlertList as getItemList, hasAlertOrderByOrderPermission } from "@/views/pages/apis/event";
import { addAlertData as addItemData, setAlertData as setItemData, modAlertData as modItemData, delAlertData as delItemData } from "@/views/pages/apis/event";

import { getUserList } from "@/views/pages/apis/model";
import { setAlertStat } from "@/views/pages/apis/event";
import { getResourcesList } from "@/views/pages/apis/cmdb";

import priorityColor from "@/views/pages/common/priority";
import { routerV6, routerV6Busines, routerV6alarmHistoryAll } from "@/views/pages/common/routeV6";
import { orderType, orderState } from "@/views/pages/apis/association";

import deviceDetials from "@/components/deviceTool/deviceDetials.vue";
import deviceContacts from "@/components/deviceTool/deviceContacts.vue";
import devicePing from "@/components/deviceTool/ping.vue";
import deviceRoute from "@/components/deviceTool/tracerRoute.vue";
import deviceTools from "@/components/deviceTool/index.vue";
import { desktop, getDeviceDiscovery } from "@/views/pages/apis/device";

import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);
import {
  /*  */
  资产管理中心_设备_工具权限,
  监控管理中心_告警_安全,
  监控管理中心_告警_所有权限,
  监控管理中心_设备_可读,
  监控管理中心_告警_可读,
  安全管理中心_用户管理_可读,
  系统管理中心_客户管理_可读,
  智能事件中心_客户_工单可读,
  智能事件中心_设备_工单可读,
} from "@/views/pages/permission";

import { exoprtMatch1, exoprtMatch2, exoprtMatch4 } from "@/components/tableColumn/common";

type DataItem = Omit<$DataItem, "alarmBoardConfirmedPerson" | "eventConfirmedPerson"> & Record<"alarmBoardConfirmedPerson" | "eventConfirmedPerson", { userid: string; username: string }>;

/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const userInfo = getUserInfo();
defineOptions({ name: "alarmBoard" });
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
function handleExpand(row: DataItem, expandedRows: DataItem[]) {
  state.expand = expandedRows.filter((v) => !v.alarmBoardConfirmed).map(({ id }) => id);
  if (find(expandedRows, ({ id }) => row.id === id)) {
    /*  */
  } else {
    /*  */
  }
}
function handleSort(sort: { prop: string; order: "ascending" | "descending" }) {
  state.sort = sort.prop ? sort : undefined;
}
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
async function resetData() {
  state.list = [];
  state.page = 1;
  state.size = 50;
  state.total = 0;
  // for (const key in state.search) {
  //   if (Object.prototype.hasOwnProperty.call(state.search, key)) {
  //     delete state.search[key];
  //   }
  // }
  await nextTick();
}

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);
const $filter2 = ref(exoprtMatch4);

interface Props {
  width: number;
  height: number;
  title?: string;
}

const props = withDefaults(defineProps<Props>(), { title: "告警" });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

const _width = inject("width", ref(0));
const _height = inject("height", ref(0));

const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);

const tableRef = ref<InstanceType<typeof ElTable>>();
const editorRef = ref<InstanceType<typeof Editor>>();

const deviceDetialsRef = ref<InstanceType<typeof deviceDetials>>();
const deviceContactsRef = ref<InstanceType<typeof deviceContacts>>();
const deviceRouteRef = ref<InstanceType<typeof deviceRoute>>();
const devicePingRef = ref<InstanceType<typeof devicePing>>();
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

interface State<T, P> {
  loading: boolean;
  expand: string[];
  select: string[];
  current: string | undefined;
  search: P;
  sort?: { prop: string; order: "ascending" | "descending" };
  list: T[];
  page: number;
  size: number;
  total: number;
}
enum command {
  Refresh = "Refresh",
  Request = "Request",
  Preview = "Preview",
  Create = "Create",
  Update = "Update",
  Modify = "Modify",
  Delete = "Delete",
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const final = readonly({
  pagination: false,
});

const routerPath = {
  EVENT_ORDER: "510685950393712640",
  SERVICE_REQUEST: "514703398516293632",
  CHANGE: "515123784953364480",
  QUESTION: "515035822471249920",
  PUBLISH: "519831507997556736",
};

type DataSrch = Omit<typeof getItemList extends (req: infer P) => any ? P : never, "paging" | "sort">;
const state = reactive<State<DataItem, DataSrch>>({
  loading: false,
  expand: [],
  select: [],
  current: undefined,
  search: {
    tenantId: "",
    loginTime: "",

    // alertCreateTimeRange: { start: "", end: "" },
    // alarmBoardConfirmedTime: { start: "", end: "" },

    includeTenantName: [],
    excludeTenantName: [],
    eqTenantName: [],
    neTenantName: [],
    tenantNameFilterRelation: "AND",
    includeOrderId: [],
    excludeOrderId: [],
    includeAlert: [],
    excludeAlert: [],
    includeDeviceOrIp: [],
    excludeDeviceOrIp: [],
    includeResponseUser: [],
    excludeResponseUser: [],
    orderIdFilterRelation: "AND",
    alertFilterRelation: "AND",
    deviceOrIpFilterRelation: "AND",
    responseUserFilterRelation: "AND",
  },
  sort: undefined,
  list: [],
  page: 1,
  size: 50,
  total: 0,
});
const dataList = computed(() => (final.pagination ? state.list.slice((state.page - 1) * state.size, state.page * state.size) : state.list));
const expand = computed(() => filter<DataItem[]>(state.list, (row: DataItem) => state.expand.includes(row.id)));
const select = computed(() => filter<DataItem[]>(state.list, (row: DataItem) => state.select.includes(row.id)));
const current = computed(() => find<DataItem[]>(state.list, (row: DataItem) => row.id === state.current));
// const name = computed(() => state.name);

const searchType0ByTenantName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByTenantName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByTenantName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByTenantName) === "include") value0 = state.search.includeTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "exclude") value0 = state.search.excludeTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "eq") value0 = state.search.eqTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "ne") value0 = state.search.neTenantName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByTenantName) === "include") value1 = state.search.includeTenantName[state.search.includeTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "exclude") value1 = state.search.excludeTenantName[state.search.excludeTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "eq") value1 = state.search.eqTenantName[state.search.eqTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "ne") value1 = state.search.neTenantName[state.search.neTenantName.length - 1] || "";
    return {
      type0: toValue(searchType0ByTenantName),
      type1: toValue(searchType1ByTenantName),
      relation: state.search.tenantNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByTenantName.value = v.type0 as typeof searchType0ByTenantName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByTenantName.value = v.type1 as typeof searchType1ByTenantName extends import("vue").Ref<infer T> ? T : string;
    state.search.tenantNameFilterRelation = v.relation;
    state.search.includeTenantName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeTenantName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqTenantName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neTenantName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByOrderId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByOrderId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByOrderId = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByOrderId) === "include") value0 = state.search.includeOrderId[0] || "";
    if (toValue(searchType0ByOrderId) === "exclude") value0 = state.search.excludeOrderId[0] || "";
    let value1 = "";
    if (toValue(searchType1ByOrderId) === "include") value1 = state.search.includeOrderId[state.search.includeOrderId.length - 1] || "";
    if (toValue(searchType1ByOrderId) === "exclude") value1 = state.search.excludeOrderId[state.search.excludeOrderId.length - 1] || "";
    return {
      type0: toValue(searchType0ByOrderId),
      type1: toValue(searchType1ByOrderId),
      relation: state.search.orderIdFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByOrderId.value = v.type0 as typeof searchType0ByOrderId extends import("vue").Ref<infer T> ? T : string;
    searchType1ByOrderId.value = v.type1 as typeof searchType1ByOrderId extends import("vue").Ref<infer T> ? T : string;
    state.search.orderIdFilterRelation = v.relation;
    state.search.includeOrderId = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeOrderId = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
  },
});
const searchType0ByAlert = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByAlert = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByAlert = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByAlert) === "include") value0 = state.search.includeAlert[0] || "";
    if (toValue(searchType0ByAlert) === "exclude") value0 = state.search.excludeAlert[0] || "";
    let value1 = "";
    if (toValue(searchType1ByAlert) === "include") value1 = state.search.includeAlert[state.search.includeAlert.length - 1] || "";
    if (toValue(searchType1ByAlert) === "exclude") value1 = state.search.excludeAlert[state.search.excludeAlert.length - 1] || "";
    return {
      type0: toValue(searchType0ByAlert),
      type1: toValue(searchType1ByAlert),
      relation: state.search.alertFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByAlert.value = v.type0 as typeof searchType0ByAlert extends import("vue").Ref<infer T> ? T : string;
    searchType1ByAlert.value = v.type1 as typeof searchType1ByAlert extends import("vue").Ref<infer T> ? T : string;
    state.search.alertFilterRelation = v.relation;
    state.search.includeAlert = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeAlert = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
  },
});
const searchType0ByDeviceOrIp = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByDeviceOrIp = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByDeviceOrIp = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByDeviceOrIp) === "include") value0 = state.search.includeDeviceOrIp[0] || "";
    if (toValue(searchType0ByDeviceOrIp) === "exclude") value0 = state.search.excludeDeviceOrIp[0] || "";
    let value1 = "";
    if (toValue(searchType1ByDeviceOrIp) === "include") value1 = state.search.includeDeviceOrIp[state.search.includeDeviceOrIp.length - 1] || "";
    if (toValue(searchType1ByDeviceOrIp) === "exclude") value1 = state.search.excludeDeviceOrIp[state.search.excludeDeviceOrIp.length - 1] || "";
    return {
      type0: toValue(searchType0ByDeviceOrIp),
      type1: toValue(searchType1ByDeviceOrIp),
      relation: state.search.deviceOrIpFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByDeviceOrIp.value = v.type0 as typeof searchType0ByDeviceOrIp extends import("vue").Ref<infer T> ? T : string;
    searchType1ByDeviceOrIp.value = v.type1 as typeof searchType1ByDeviceOrIp extends import("vue").Ref<infer T> ? T : string;
    state.search.deviceOrIpFilterRelation = v.relation;
    state.search.includeDeviceOrIp = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeDeviceOrIp = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
  },
});
const searchType0ByResponseUser = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByResponseUser = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByResponseUser = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByResponseUser) === "include") value0 = state.search.includeResponseUser[0] || "";
    if (toValue(searchType0ByResponseUser) === "exclude") value0 = state.search.excludeResponseUser[0] || "";
    let value1 = "";
    if (toValue(searchType1ByResponseUser) === "include") value1 = state.search.includeResponseUser[state.search.includeResponseUser.length - 1] || "";
    if (toValue(searchType1ByResponseUser) === "exclude") value1 = state.search.excludeResponseUser[state.search.excludeResponseUser.length - 1] || "";
    return {
      type0: toValue(searchType0ByResponseUser),
      type1: toValue(searchType1ByResponseUser),
      relation: state.search.responseUserFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByResponseUser.value = v.type0 as typeof searchType0ByResponseUser extends import("vue").Ref<infer T> ? T : string;
    searchType1ByResponseUser.value = v.type1 as typeof searchType1ByResponseUser extends import("vue").Ref<infer T> ? T : string;
    state.search.responseUserFilterRelation = v.relation;
    state.search.includeResponseUser = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeResponseUser = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
  },
});
/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */

/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const timer = ref<null | number>(null);
const autoRefreshTime = ref(0);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}

const tenantNameObj = ref({
  status: "include",
  type: "include",
  relation: "AND",
  firstName: "",
  lastName: "",
});

const orderObj = ref({
  status: "include",
  type: "include",
  relation: "AND",
  firstName: "",
  lastName: "",
});

const alarmObj = ref({
  status: "include",
  type: "include",
  relation: "AND",
  firstName: "",
  lastName: "",
});
function beforeMount() {}
function mounted() {
  handleRefresh().then(() => (autoRefreshTime.value = 60));
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {
  if (timer.value !== null) {
    window.clearInterval(timer.value);
    timer.value = null;
  }
}
function beforeDestroy() {
  if (timer.value !== null) {
    window.clearInterval(timer.value);
    timer.value = null;
  }
}
function destroyed() {}

function historyAlarm() {}

function handleToOrder(routerName, routerParams, tenantId) {
  const fallback: string = route.name as string;
  // userInfo.cutTenant(tenantId).finally(async () => {
  const routeData = router.resolve({
    // name: routerName,
    path: {
      [routerPath.EVENT_ORDER]: `/event/intelligent_events/details/${routerParams.id}`,
      [routerPath.SERVICE_REQUEST]: `/event/intelligent_request/details/${routerParams.id}`,
      [routerPath.CHANGE]: `/event/intelligent_change/details/${routerParams.id}`,
      [routerPath.PUBLISH]: `/event/intelligent_publish/details/${routerParams.id}`,
      [routerPath.QUESTION]: `/event/intelligent_question/details/${routerParams.id}`,
      // [routerPath.DICT_EVENT_ORDER]: `/event/intelligent_events/dict_event/details/${routerParams.id}`,
      // [routerPath.DICT_SERVICE_REQUEST]: `/event/intelligent_events/dict_serviceRequest/details/${routerParams.id}`,
    }[routerName],
    // params: routerParams,
    query: {
      fallback,
    },
  });
  window.open(routeData.href);
  // });
}

function routeDevice(props) {
  // // console.log(props,777)
  let obj = {
    id: props.deviceId,
    config: {
      ipAddress: props.deviceIp,
    },
    name: props.deviceName,
  };
  deviceRouteRef.value.open(obj);
}

function timeZoneSwitching(): number {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == userInfo.zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}

function routerDevice(v) {
  const routeData = router.resolve({
    // name: "509596457372745728",
    params: { id: v.deviceId },
    query: { fallback: route.name as string, tenant: v.tenantId },
  });
  window.open(routeData.href, v.deviceId);
  // const previousWindow = this.$store.state.previousWindow
}

function routerOrder(item) {
  // <!-- <router-link :to="{ name: routerPath[item.orderType], params: { id: item?.orderId }, query: { fallback: route.name as string, tenant: item.tenantId as string } }" target="_blank" tag="a"> -->
  const routeData = router.resolve({
    name: routerPath[item.orderType],
    params: { id: item.orderId },
    query: { fallback: route.name as string, tenant: item.tenantId },
  });
  window.open(routeData.href, item.orderId);
}
async function ping(props: any) {
  // // console.log(props,777)
  let obj = {
    id: props.deviceId,
    config: {
      ipAddress: props.deviceIp,
    },
    name: props.deviceName,
  };
  await devicePingRef.value.open(obj);
  // pingTo({ id: "523721061548687360" }).then((res) => {
  //   // console.log(res);
  // });
  // deviceId.value = row.id;
  // deviceContactsRef.value.dialogFormVisible = true;
}

function contancts(id) {
  // deviceId.value = id;
  deviceContactsRef.value.dialogFormVisible = true;
  // deviceContactsRef.value.id = id;
  deviceContactsRef.value.open(id);
}
function preview(id) {
  // deviceId.value = id;
  deviceDetialsRef.value.open(id);
  deviceDetialsRef.value.dialogFormVisible = true;
}

watch(autoRefreshTime, (autoRefreshTime) => {
  if (timer.value !== null) timer.value = (window.clearInterval(timer.value), null);
  if (autoRefreshTime) timer.value = window.setInterval(queryData, autoRefreshTime * 1000);
});
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

const tenants = userInfo.tenants;
async function handleCommand(type: command, data?: Record<string, unknown>) {
  const time = autoRefreshTime.value;
  autoRefreshTime.value = 0;
  try {
    state.loading = true;
    await nextTick();

    switch (type) {
      case command.Refresh:
        await resetData();
        await queryData();
        break;
      case command.Request:
        await queryData();
        break;
      case command.Preview:
        await previewItem(data as Record<string, unknown>);
        break;
      case command.Create:
        await createItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
      case command.Update:
        await rewriteItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
      case command.Modify:
        await modifyItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
      case command.Delete:
        await deleteItem(data as Record<string, unknown>);
        await resetData();
        await queryData();
        break;
    }
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await resetData();
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
      await queryData();
    }
  } finally {
    autoRefreshTime.value = time;
    state.loading = false;
  }
}
async function handleRefresh() {
  try {
    state.loading = true;
    await nextTick();
    await resetData();

    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
async function handleQuery() {
  try {
    state.loading = true;
    // let includeTenantIds: any = [];
    // let excludeTenantIds: any = [];
    // let includeOrderIds: any = [];
    // let excludeOrderIds: any = [];
    // let includeAlerts: any = [];
    // let excludeAlerts: any = [];
    // let eqTenantName: any = [];
    // let neTenantName: any = [];
    // includeTenantIds.push(tenantNameObj.value.firstName);

    // if (tenantNameObj.value) {
    // state.search.tenantIdFilterRelation = tenantNameObj.value.relation;

    //   // includeTenantIds = [];
    //   // excludeTenantIds = [];
    //   // console.log(tenantNameObj.value.firstName, tenantNameObj.value.lastName);
    //   switch (tenantNameObj.value.status) {
    //     case "include":
    //       if (tenantNameObj.value.firstName != "") {
    //         includeTenantIds.push(tenantNameObj.value.firstName);
    //       } else {
    //         includeTenantIds.push(null);
    //       }

    //       break;
    //     case "exclude":
    //       if (tenantNameObj.value.firstName != "") {
    //         excludeTenantIds.push(tenantNameObj.value.firstName);
    //       } else {
    //         excludeTenantIds.push(null);
    //       }
    //       break;
    //     case "be":
    //       if (tenantNameObj.value.firstName != "") {
    //         eqTenantName.push(tenantNameObj.value.firstName);
    //       } else {
    //         eqTenantName.push(null);
    //       }
    //       break;
    //     case "notBe":
    //       if (tenantNameObj.value.firstName != "") {
    //         neTenantName.push(tenantNameObj.value.firstName);
    //       } else {
    //         neTenantName.push(null);
    //       }
    //       break;
    //   }

    //   switch (tenantNameObj.value.type) {
    //     // console.log(v.tenantName.includes(tenantNameObj.value.lastName))
    //     case "include":
    //       if (tenantNameObj.value.lastName != "") {
    //         includeTenantIds.push(tenantNameObj.value.lastName);
    //       } else {
    //         includeTenantIds.push(null);
    //       }

    //       break;
    //     case "exclude":
    //       if (tenantNameObj.value.lastName != "") {
    //         excludeTenantIds.push(tenantNameObj.value.lastName);
    //       } else {
    //         excludeTenantIds.push(null);
    //       }
    //       break;
    //     case "be":
    //       if (tenantNameObj.value.lastName != "") {
    //         eqTenantName.push(tenantNameObj.value.lastName);
    //       } else {
    //         eqTenantName.push(null);
    //       }
    //       break;
    //     case "notBe":
    //       if (tenantNameObj.value.lastName != "") {
    //         neTenantName.push(tenantNameObj.value.lastName);
    //       } else {
    //         neTenantName.push(null);
    //       }
    //       break;
    //   }

    //   state.search.includeTenantIds = [...new Set(includeTenantIds)];
    //   state.search.excludeTenantIds = [...new Set(excludeTenantIds)];
    //   state.search.eqTenantName = [...new Set(eqTenantName)];
    //   state.search.neTenantName = [...new Set(neTenantName)];
    // }

    // if (orderObj.value) {
    //   state.search.orderIdFilterRelation = orderObj.value.relation;

    //   switch (orderObj.value.status) {
    //     case "include":
    //       includeOrderIds.push(orderObj.value.firstName);
    //       break;
    //     case "exclude":
    //       excludeOrderIds.push(orderObj.value.firstName);
    //       break;
    //   }
    //   switch (orderObj.value.type) {
    //     case "include":
    //       includeOrderIds.push(orderObj.value.lastName);
    //       break;
    //     case "exclude":
    //       excludeOrderIds.push(orderObj.value.lastName);
    //       break;
    //   }

    //   state.search.includeOrderIds = includeOrderIds;
    //   state.search.excludeOrderIds = excludeOrderIds;
    // }
    // if (alarmObj.value) {
    //   state.search.alertFilterRelation = alarmObj.value.relation;

    //   switch (alarmObj.value.status) {
    //     case "include":
    //       includeAlerts.push(alarmObj.value.firstName);
    //       break;
    //     case "exclude":
    //       excludeAlerts.push(alarmObj.value.firstName);
    //       break;
    //   }
    //   switch (alarmObj.value.type) {
    //     case "include":
    //       includeAlerts.push(alarmObj.value.lastName);
    //       break;
    //     case "exclude":
    //       excludeAlerts.push(alarmObj.value.lastName);
    //       break;
    //   }

    //   state.search.includeAlerts = includeAlerts;
    //   state.search.excludeAlerts = excludeAlerts;
    // }

    await nextTick();
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
const deskTopObj = ref<ReturnType<typeof desktop> extends Promise<{ data: infer T }> ? T : never>([]);

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
async function queryData() {
  let sort: string[] = [];
  switch ((state.sort || {}).order) {
    case "ascending":
      sort.push(`${String(state.sort?.prop)},asc`);
      break;
    case "descending":
      sort.push(`${String(state.sort?.prop)},desc`);
      break;
  }

  const { success, message, data, page, size, total } = await getItemList({ ...state.search, loginTime: userInfo.loginTime, tenantId: userInfo.currentTenantId, /* containerId: userInfo.currentTenant.containerId, */ sort, paging: { pageNumber: state.page, pageSize: state.size }, permissionId: 监控管理中心_设备_可读 });
  if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });

  const $data = (data instanceof Array ? data : []).map((v): DataItem => {
    const alarmBoardConfirmedPerson = { userid: "", username: "--", account: "--" };
    const eventConfirmedPerson = { userid: "", username: "--", account: "--" };
    try {
      const $alarmBoardConfirmedPerson = JSON.parse(v.alarmBoardConfirmedPerson || "");
      alarmBoardConfirmedPerson.userid = $alarmBoardConfirmedPerson.userId || "";
      alarmBoardConfirmedPerson.username = $alarmBoardConfirmedPerson.username || "--";
      alarmBoardConfirmedPerson.account = $alarmBoardConfirmedPerson.account || "--";
    } catch (error) {
      /*  */
    }
    try {
      const $eventConfirmedPerson = JSON.parse(v.eventConfirmedPerson || "");
      eventConfirmedPerson.userid = $eventConfirmedPerson.userId || "";
      eventConfirmedPerson.username = $eventConfirmedPerson.username || "--";
      eventConfirmedPerson.account = $eventConfirmedPerson.account || "--";
    } catch (error) {
      /*  */
    }
    return Object.assign(v, { alarmBoardConfirmedPerson, eventConfirmedPerson });
  });
  const select = new Set($data.filter((v) => state.select.includes(v.id)).map((v) => v.id));

  const [hasOrderSet, hasUserSet] = await Promise.all([
    (async (req) => {
      if (!req.orderIds.length) return new Set<string>();
      const { success, message, data } = await hasAlertOrderByOrderPermission({ orderIds: req.orderIds, permissionId: 智能事件中心_客户_工单可读 });
      if (!success) throw Object.assign(new Error(message), { success, data, page });
      return data.reduce((p, c) => p.add(c.orderId), new Set<string>());
    })({ orderIds: Array.from(new Set($data.reduce<string[]>((p, c) => p.concat((c.orders instanceof Array ? c.orders : []).map((v) => v.orderId)), [])).values()).filter((v) => v) }),
    (async (req) => {
      if (!req.userIds.length) return new Set<string>();
      const { success, message, data } = await getUserList({ pageNumber: 1, pageSize: req.userIds.length, userIds: req.userIds, permissionId: 安全管理中心_用户管理_可读 });
      if (!success) throw Object.assign(new Error(message), { success, data, page });
      return data.reduce((p, c) => p.add(c.id), new Set<string>());
    })({ userIds: Array.from(new Set($data.reduce<string[]>((p, c) => p.concat(c.alarmBoardConfirmedPerson.userid, c.eventConfirmedPerson.userid), [])).values()).filter((v) => v) }),
  ]);
  if (userInfo.hasPermission(监控管理中心_设备_可读) && userInfo.hasPermission(系统管理中心_客户管理_可读)) {
    state.list.splice(
      0,
      state.list.length,
      ...$data.map((v) => {
        const newAlertCreateTime = Number(v.alertCreateTime) + timeZoneSwitching();

        return Object.assign(v, {
          alertCreateTime: newAlertCreateTime,
          orders: (v.orders instanceof Array ? v.orders : []).filter((order) => hasOrderSet.has(order.orderId)),
          alarmBoardConfirmedPerson: {
            ...v.alarmBoardConfirmedPerson,
            ...(hasUserSet.has(v.alarmBoardConfirmedPerson.userid) ? { username: v.alarmBoardConfirmedPerson.username } : { username: "--" }),
          },
          eventConfirmedPerson: {
            ...v.eventConfirmedPerson,
            ...(hasUserSet.has(v.eventConfirmedPerson.userid) ? { username: v.eventConfirmedPerson.username } : { username: "--" }),
          },
        });
      })
    );
    // state.list.splice(0, state.list.length, ...$data.map((v) => Object.assign(v, { alertCreateTime: newAlertCreateTime }, { orders: (v.orders instanceof Array ? v.orders : []).filter((v) => hasOrderSet.has(v.orderId)), alarmBoardConfirmedPerson: { ...v.alarmBoardConfirmedPerson, ...(hasUserSet.has(v.alarmBoardConfirmedPerson.userid) ? { username: v.alarmBoardConfirmedPerson.username } : { username: "--" }) }, eventConfirmedPerson: { ...v.eventConfirmedPerson, ...(hasUserSet.has(v.eventConfirmedPerson.userid) ? { username: v.eventConfirmedPerson.username } : { username: "--" }) } })));
    // if (!userInfo.hasPermission(智能事件中心_客户_工单可读) && !userInfo.hasPermission("756062918394511360" as any) && !userInfo.hasPermission(智能事件中心_设备_工单可读)) {
    //   state.list = state.list.map((item) => {
    //     return {
    //       ...item,
    //       orders: [],
    //     };
    //   });
    // }
    state.page = Number(page) || 1;
    state.size = Number(size) || 20;
    state.total = Number(total) || 0;
  } else {
    state.list = [];
  }

  deskTopObj.value = [];
  if (state.list.length) {
    const { success: desktopSuccess, message: desktopMessage, data: desktopData } = await desktop({ deviceIds: state.list.map((v) => v.deviceId).join() });
    if (!desktopSuccess) throw Object.assign(new Error(desktopMessage), { success: desktopSuccess, data: desktopData });
    deskTopObj.value = desktopData instanceof Array ? desktopData : [];
    for (let i = 0; i < state.list.length; i++) {
      const $desktop = find(desktopData, (v) => state.list[i].deviceId === v.resourceId);
      Object.assign(state.list[i], { showDesktop: $desktop ? $desktop.icoShow : false, allowTypes: $desktop && $desktop.allowTypes instanceof Array ? $desktop.allowTypes : [] });
    }
  }
  // await (async (ids: string[]) => {
  //   try {
  //     if (!ids.length) return;
  //     const { success, message, data: ResourcesList } = await getResourcesList({ ids });
  //     if (!success) throw Object.assign(new Error(message), { success, data: ResourcesList });
  //     for (let i = 0; i < (data instanceof Array ? data : []).length; i++) {
  //       const device = find(ResourcesList, ({ id }) => id === (data instanceof Array ? data : [])[i].deviceId);
  //       (data instanceof Array ? data : [])[i].deviceName = device ? device.name : "";
  //       (data instanceof Array ? data : [])[i].deviceIp = device ? device.config.ipAddress : "";
  //       try {
  //         (data instanceof Array ? data : [])[i].alarmBoardConfirmedPerson = JSON.parse((data instanceof Array ? data : [])[i].alarmBoardConfirmedPerson).username;
  //       } catch (error) {
  //         /*  */
  //       }
  //     }
  //   } catch (error) {
  //     if (error instanceof Error) ElMessage.error(message);
  //   }
  // })(Array.from((data instanceof Array ? data : []).reduce((p, c) => (p.add(c.deviceId), p), new Set<string>())));

  await nextTick();
  if (tableRef.value) {
    tableRef.value.clearSelection();
    for (let i = 0; i < state.list.length; i++) {
      if (state.list[i].alarmBoardConfirmed) {
        tableRef.value.toggleRowSelection(state.list[i], true);
        select.delete(state.list[i].id);
      } else {
        tableRef.value.toggleRowSelection(state.list[i], select.has(state.list[i].id));
      }
    }
  }
  state.select = Array.from(select);
}

function tableFormatter(row: any, col: any, v: any) {
  // // console.log()
  switch (col.property) {
    case "alarmBoardConfirmedPerson":
      if (JSON.parse(v)?.username != undefined) {
        return JSON.parse(v)?.username + "@" + row.tenantAbbreviation || "--";
      } else {
        return "--";
      }
  }
}
async function createItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await setAlertStat({ ids: form.ids as string[], ...form });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`成功确认告警`);
  });
}
async function previewItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await setAlertStat({ ids: form.ids as string[], ...form });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${t("axios.Operation successful")}`);
  });
}
async function rewriteItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await setAlertStat({ ids: form.ids as string[], ...form });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${t("axios.Operation successful")}`);
  });
}
async function modifyItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await setAlertStat({ ids: form.ids as string[], ...form });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${t("axios.Operation successful")}`);
  });
}
async function deleteItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await delItemData(form);
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${t("axios.Operation successful")}`);
  });
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: DataItem): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.orderPriority {
  // display: flex;
  border-radius: 20px;
  width: 40px !important;
}
</style>
