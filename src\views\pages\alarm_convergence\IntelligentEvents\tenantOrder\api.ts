/* eslint-disable @typescript-eslint/no-unused-vars */
import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";

/**
 * 模块Api示例
 */
export interface ModuleItem {
  [key: string]: unknown;
  id: string;
  name: string;
}
export function getModuleList /* 获取模块 */(data: Record<string, unknown> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<ModuleItem[]>);
  // return request<unknown, Response<ModuleItem[]>>({
  //   url: `${SERVER.IAM}/module`,
  //   method: Method.Get,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  // });
}
export function addModuleData /* 添加模块 */(data: Partial<ModuleItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<ModuleItem[]>);
  // return request<unknown, Response<ModuleItem[]>>({
  //   url: `${SERVER.IAM}/module`,
  //   method: Method.Post,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  // });
}
export function setModuleData /* 更新模块 */(data: Partial<ModuleItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<ModuleItem[]>);
  // return request<unknown, Response<ModuleItem[]>>({
  //   url: `${SERVER.IAM}/module/${data.id}`,
  //   method: Method.Put,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  // });
}
export function modModuleData /* 修改模块 */(data: Partial<ModuleItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<ModuleItem[]>);
  // return request<unknown, Response<ModuleItem[]>>({
  //   url: `${SERVER.IAM}/module/${data.id}`,
  //   method: Method.Patch,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  // });
}
export function delModuleData /* 删除模块 */(data: Partial<ModuleItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<ModuleItem[]>);
  // return request<unknown, Response<ModuleItem[]>>({
  //   url: `${SERVER.IAM}/module/${data.id}`,
  //   method: Method.Delete,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  // });
}
