<template>
  <el-dialog :title="`${i18n.t('eventBoard.operate note', { operate: isEdit ? i18n.t('eventBoard.Edit') : i18n.t('eventBoard.Add') })}`" v-model="drawer" draggable :before-close="handleClose" :size="width" ref="drawer">
    <el-form :model="{}" :rules="rules" ref="form" label-width="0" v-loading="fileLoading" :element-loading-text="i18n.t('eventBoard.Attachments are being uploaded')">
      <el-scrollbar height="600px">
        <el-form-item prop="content" v-if="drawer">
          <div class="tw-flex tw-min-h-[350px] tw-w-full tw-flex-col">
            <QuillEditor theme="snow" class="tw-h-[350px] tw-w-full" v-model:content="content" @update:content="content = $event" contentType="html" toolbar="full" :enable="true" :modules="modules"></QuillEditor>
            <!-- <QuillEditor ref="myQuillEditorRef" :class="[form.disabled ? 'is-disabled' : '']" v-if="data.visible" theme="snow" style="flex: 1; height: 400px" placeholder="请输入内容" v-model:content="form.content" contentType="html" :modules="modules" toolbar="full"></QuillEditor> -->
          </div>
        </el-form-item>

        <el-form-item :label="i18n.t('eventBoard.Upload attachments')" label-width="120">
          <div class="tw-w-full">
            <el-button type="primary" @click="openFileDialog()">{{ i18n.t("eventBoard.Click Upload") }}</el-button>
            <div>
              <el-checkbox v-model="privateAble">{{ i18n.t("eventBoard.Private Clients") }}[{{ tenantAbbreviation }}]</el-checkbox>
            </div>
            <transition-group tag="ul" :class="['el-upload-list', 'el-upload-list--text']" name="el-upload-list">
              <li v-for="file in allFileList" :key="file.uid || file.name" :class="['el-upload-list__item', `is-${file.status}`, { focusing }]" tabindex="0" @keydown.delete.stop @focus="focusing = true" @blur="focusing = false" @click="focusing = false">
                <div :class="['el-upload-list__item-info']">
                  <a :class="['el-upload-list__item-name']" @click.prevent.stop>
                    <el-icon :class="['el-icon--document']"><Document /></el-icon>
                    <span :class="['el-upload-list__item-file-name']" :title="file.name"> {{ file.fileName }} </span>
                  </a>
                  <el-progress v-if="file.status === 'uploading'" type="line" :stroke-width="2" :percentage="Number(file.percentage)" style="margin-top: 0.5rem" />
                </div>

                <label :class="['el-upload-list__item-status-label']">
                  <el-icon :class="['el-icon--upload-success', 'el-icon--circle-check']"><CircleCheck /></el-icon>
                </label>

                <!-- 删除按钮 -->
                <el-icon :class="['el-icon--close']" @click.stop="handleRemove(file)"><Close /></el-icon>

                <i :class="['el-icon--close-tip']">{{ $t("el.upload.deleteTip") }}</i>
              </li>
            </transition-group>
          </div>
        </el-form-item>
      </el-scrollbar>
      <el-form-item class="demo-drawer__footer">
        <el-button type="primary" @click="submitNote">{{ loading ? `${i18n.t("eventBoard.Submitting")}...` : isEdit ? i18n.t("eventBoard.Edit") : i18n.t("eventBoard.New") }}</el-button>
        <el-button @click="handleClose">{{ i18n.t("glob.Cancel") }}</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
// import editor from "@/components/Editor/indexEditor.vue";
import { setEventNotes } from "@/views/pages/apis/eventBoard";
import { uploadFile, FileType } from "@/views/pages/apis/file";
import { Document, CircleCheck, Close } from "@element-plus/icons-vue";
import { useFileDialog } from "@vueuse/core";
import { QuillEditor } from "@vueup/vue-quill";
import getUserInfo from "@/utils/getUserInfo";
import BlotFormatter from "quill-blot-formatter";
import { useI18n } from "vue-i18n";

const titleConfig = [
  { Choice: ".ql-insertMetric", title: "跳转配置" },
  { Choice: ".ql-bold", title: "加粗" },
  { Choice: ".ql-italic", title: "斜体" },
  { Choice: ".ql-underline", title: "下划线" },
  { Choice: ".ql-header", title: "段落格式" },
  { Choice: ".ql-strike", title: "删除线" },
  { Choice: ".ql-blockquote", title: "块引用" },
  { Choice: ".ql-code", title: "插入代码" },
  { Choice: ".ql-code-block", title: "插入代码段" },
  { Choice: ".ql-font", title: "字体" },
  { Choice: ".ql-size", title: "字体大小" },
  { Choice: '.ql-list[value="ordered"]', title: "编号列表" },
  { Choice: '.ql-list[value="bullet"]', title: "项目列表" },
  { Choice: ".ql-direction", title: "文本方向" },
  { Choice: '.ql-header[value="1"]', title: "h1" },
  { Choice: '.ql-header[value="2"]', title: "h2" },
  { Choice: ".ql-align", title: "对齐方式" },
  { Choice: ".ql-color", title: "字体颜色" },
  { Choice: ".ql-background", title: "背景颜色" },
  { Choice: ".ql-image", title: "图像" },
  { Choice: ".ql-video", title: "视频" },
  { Choice: ".ql-link", title: "添加链接" },
  { Choice: ".ql-formula", title: "插入公式" },
  { Choice: ".ql-clean", title: "清除字体格式" },
  { Choice: '.ql-script[value="sub"]', title: "下标" },
  { Choice: '.ql-script[value="super"]', title: "上标" },
  { Choice: '.ql-indent[value="-1"]', title: "向左缩进" },
  { Choice: '.ql-indent[value="+1"]', title: "向右缩进" },
  { Choice: ".ql-header .ql-picker-label", title: "标题大小" },
  { Choice: '.ql-header .ql-picker-item[data-value="1"]', title: "标题一" },
  { Choice: '.ql-header .ql-picker-item[data-value="2"]', title: "标题二" },
  { Choice: '.ql-header .ql-picker-item[data-value="3"]', title: "标题三" },
  { Choice: '.ql-header .ql-picker-item[data-value="4"]', title: "标题四" },
  { Choice: '.ql-header .ql-picker-item[data-value="5"]', title: "标题五" },
  { Choice: '.ql-header .ql-picker-item[data-value="6"]', title: "标题六" },
  { Choice: ".ql-header .ql-picker-item:last-child", title: "标准" },
  { Choice: '.ql-size .ql-picker-item[data-value="small"]', title: "小号" },
  { Choice: '.ql-size .ql-picker-item[data-value="large"]', title: "大号" },
  { Choice: '.ql-size .ql-picker-item[data-value="huge"]', title: "超大号" },
  { Choice: ".ql-size .ql-picker-item:nth-child(2)", title: "标准" },
  { Choice: ".ql-align .ql-picker-item:first-child", title: "居左对齐" },
  { Choice: '.ql-align .ql-picker-item[data-value="center"]', title: "居中对齐" },
  { Choice: '.ql-align .ql-picker-item[data-value="right"]', title: "居右对齐" },
  { Choice: '.ql-align .ql-picker-item[data-value="justify"]', title: "两端对齐" },
];

export default {
  components: { Document, CircleCheck, Close, QuillEditor },
  inject: ["width"],
  emits: ["refresh"],
  data() {
    return {
      focusing: false,
      drawer: false,
      loading: false,
      content: "",
      noteItem: {},
      fileList: [],
      allFileList: [],
      fileLoading: false,
      userInfo: getUserInfo(),
      // .../* { files, open, reset, onChange } = */ useFileDialog({ multiple: true, reset: true }),
      openFileDialog: () => {},
      modules: {
        name: "blotFormatter",
        module: BlotFormatter,
      },
      privateAble: false,
      privateCustomerId: "",
      orderIds: [] as string[],
      i18n: useI18n(),
    };
  },
  computed: {
    isEdit() {
      return !!this.noteItem?.noteId;
    },
    rules() {
      return {
        content: [
          {
            validator: (rule, value, callback) => {
              if (this.content) return callback();
              else callback(new Error(this.i18n.t("eventBoard.The memo cannot be left blank")));
            },
            trigger: ["blur", "change"],
          },
        ],
      };
    },
    tenantAbbreviation() {
      for (let i = 0; i < this.userInfo.tenants.length; i++) {
        if (this.userInfo.tenants[i].id === this.userInfo.tenantId) return this.userInfo.tenants[i].abbreviation;
      }
      return "";
    },
  },
  created() {
    // this.tenantAbbreviation = item.tenantAbbreviation;
    // this.userInfo.tenants.forEach((item) => {
    //   if (item.id === this.userInfo.tenantId) {
    //     this.tenantAbbreviation = item.abbreviation;
    //   }
    // });

    const { open, onChange } = useFileDialog({ multiple: true, reset: true });
    this.openFileDialog = open;
    onChange(async (files) => {
      const $files = files instanceof FileList ? Array.from(files) : [];
      if ($files.length === 0) {
        return;
      }
      this.fileLoading = true;
      try {
        const { success, data, message } = await uploadFile({ files: $files }, { fileType: FileType.EVENT, noteCreated: true, id: "1", ids: this.orderIds.join() });
        if (!success) throw new Error(message);
        this.allFileList.push(...data);
      } catch (error) {
        error instanceof Error && this.$message.error(error.message);
        this.fileLoading = false;
      } finally {
        this.fileLoading = false;
      }
    });
  },
  methods: {
    handleRemove(v) {
      this.allFileList.splice(this.allFileList.indexOf(v), 1);
    },
    handleChange(file, fileList) {
      // console.log(fileList);
      this.fileLoading = true;
      // let formData = new FormData();
      // formData.append("files", file.raw);
      // const params = {
      //   fileType: FileType.EVENT,
      //   noteCreated: true,
      //   id: this.$route.params.id,
      // };
      // uploadFile(formData, params).then(({ success, data }) => {
      //   if (success) {
      //     this.fileList = fileList;
      //     this.$message.success("操作成功");
      //     this.allFileList.push(...data);
      //     this.$nextTick(() => {
      //       document.getElementsByClassName("elstyle-upload-list__item").forEach((e) => {
      //         const classVal = e.getAttribute("class").concat(" is-success");
      //         e.setAttribute("class", classVal);
      //       });
      //     });
      //   } else this.$message.error(JSON.parse(data)?.message || "操作失败");
      //   this.fileLoading = false;
      // });
    },
    submitNote() {
      this.$refs["form"].validate((valid) => {
        if (!valid) return false;
        let attachmentList = this.allFileList.map((v) => {
          return {
            attachmentId: v.fileId,
            attachmentName: v.fileName,
            attachmentUrl: `/${v.bucketName}/${v.keyName}`,
            attachmentKey: v.keyName,
          };
        });

        // this.fileList.forEach((v) => {
        //   let flag = this.allFileList.find((f) => f.fileName === v.name);
        //   if (flag) {
        //     attachmentList.push({
        // attachmentId: flag.fileId,
        // attachmentName: flag.fileName,
        // attachmentUrl: `/${flag.bucketName}/${flag.keyName}`,
        // attachmentKey: flag.keyName,
        //     });
        //   }
        // });

        let params = {
          content: this.content,
          attachmentList,
          privateAble: this.privateAble,
          privateCustomerId: this.privateAble ? this.userInfo.currentTenantId : "",
          orderIds: this.orderIds,
        };
        this.loading = true;

        // params[this.isEdit ? "noteId" : "eventId"] = this.isEdit ? this.noteItem.noteId : this.$route.params.id;
        const api = {
          setEventNotes,
        };
        api["setEventNotes"](params)
          .then(({ success, data }) => {
            if (success) {
              this.$message.success("操作成功");
              this.handleClose();
              this.$emit("refresh");
            } else this.$message.error(JSON.parse(data)?.message);
            this.loading = false;
          })
          .catch((err) => {
            this.$message.error(err?.message);
            this.loading = false;
          });
      });
    },
    getEditorValue(v) {
      this.content = v;
    },
    open(row) {
      this.drawer = true;
      this.privateAble = false;
      setTimeout(() => {
        for (let item of titleConfig) {
          let tip = document.querySelector(".ql-toolbar " + item.Choice);
          if (!tip) continue;
          tip.setAttribute("title", item.title);
          if (item.Choice == ".ql-image" || item.Choice == ".ql-video" || item.Choice == ".ql-link") {
            tip.style.display = "none"; // 或者使用 visibility: "hidden"
          }
        }
      }, 1000);
      if (row?.noteId) {
        this.noteItem = row;
        this.fileList = row.attachmentList.map((v) => {
          return {
            name: v.attachmentName,
            url: v.attachmentUrl,
          };
        });
        if (row.attachmentList) {
          this.allFileList = row.attachmentList.map((v) => {
            return {
              keyName: v.attachmentKey,
              fileName: v.attachmentName,
              bucketName: v.attachmentUrl.substring(v.attachmentUrl.indexOf("/") + 1, v.attachmentUrl.lastIndexOf("/")),
            };
          });
        }
        this.$nextTick(() => {
          // this.$refs.editor.setValue(row.noteContent);
          this.content = row.noteContent;
        });
      }
    },
    handleClose(done) {
      // this.$refs.editor.cleanContent();
      this.noteItem = {};
      this.fileList = [];
      this.allFileList = [];
      this.content = "";
      this.$refs.form.resetFields();
      this.$refs.form.clearValidate();
      if (done instanceof Function) return done();
      else this.drawer = false;
    },
  },
};
</script>

<style lang="scss" scoped>
// ::v-deep .elstyle-drawer__body {
//   padding: 0 20px;
// }

// ::v-deep #elstyle-drawer__title span {
//   font-size: 16px;
//   font-family: PingFang SC-Medium, PingFang SC;
//   font-weight: 500;
//   color: #1d2129;
//   line-height: 24px;
// }

// ::v-deep .demo-drawer__footer {
//   display: flex;
//   margin-top: 20px;
//   // justify-content: flex-end;
// }

// ::v-deep .elstyle-form-item {
//   margin-bottom: 22px;
// }
</style>
