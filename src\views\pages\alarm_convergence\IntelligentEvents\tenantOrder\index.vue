<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <div>
      <el-scrollbar :height="45">
        <div class="tab" style="height: 35px">
          <div class="tw-text-[16px] tw-leading-[18px]" style="padding: 8px 30px" :class="`tw-w-[${tabWidth}px] ${active === ActiveType.event ? 'on' : ''}`" @click="() => active === ActiveType.event || (active = ActiveType.event)">
            <el-badge :value="eventTotal" class="mark" type="primary">
              <div class="tw-mb-[6px] tw-flex tw-items-center tw-text-left">
                {{ t("eventBoard.Iincidents") }}
              </div>
            </el-badge>
          </div>
          <div class="tw-text-[16px] tw-leading-[18px]" style="padding: 8px 30px" :class="`tw-w-[${tabWidth}px] ${active === ActiveType.service ? 'on' : ''}`" @click="() => active === ActiveType.service || (active = ActiveType.service)">
            <el-badge :value="serviceTotal" class="mark" type="primary">
              <div class="tw-mb-[6px] tw-flex tw-items-center tw-text-left">
                {{ t("eventBoard.Requests") }}
              </div>
            </el-badge>
          </div>
          <div class="tw-text-[16px] tw-leading-[18px]" style="padding: 8px 30px" :class="`tw-w-[${tabWidth}px] ${active === ActiveType.question ? 'on' : ''}`" @click="() => active === ActiveType.question || (active = ActiveType.question)">
            <el-badge :value="questionTotal" class="mark" type="primary">
              <div class="tw-mb-[6px] tw-flex tw-items-center tw-text-left">
                {{ t("eventBoard.Problems") }}
              </div>
            </el-badge>
          </div>
          <div class="tw-text-[16px] tw-leading-[18px]" style="padding: 8px 30px" :class="`tw-w-[${tabWidth}px] ${active === ActiveType.change ? 'on' : ''}`" @click="() => active === ActiveType.change || (active = ActiveType.change)">
            <el-badge :value="changeTotal" class="mark" type="primary">
              <div class="tw-mb-[6px] tw-flex tw-items-center tw-text-left">
                {{ t("eventBoard.Changes") }}
              </div>
            </el-badge>
          </div>

          <div class="tw-text-[16px] tw-leading-[18px]" style="padding: 8px 30px" :class="`tw-w-[${tabWidth}px] ${active === ActiveType.publish ? 'on' : ''}`" @click="() => active === ActiveType.publish || (active = ActiveType.publish)">
            <el-badge :value="publishTotal" class="mark" type="primary">
              <div class="tw-mb-[6px] tw-flex tw-items-center tw-text-left">发布</div>
            </el-badge>
          </div>
          <!-- 二维码报障 -->
          <div class="tw-text-[16px] tw-leading-[18px]" style="padding: 8px 30px" :class="`tw-w-[${tabWidth}px] ${active === ActiveType.qrcode ? 'on' : ''}`" @click="() => active === ActiveType.qrcode || (active = ActiveType.qrcode)">
            <el-badge :value="qrcodeTotal" class="mark" type="primary">
              <div class="tw-mb-[6px] tw-flex tw-items-center tw-text-left">
                {{ t("eventBoard.QR code to report faults") }}
              </div>
            </el-badge>
          </div>

          <!-- DICT事件 -->
          <!-- <div class="tw-overflow-hidden tw-text-[16px] tw-leading-[18px]" style="padding: 8px 15px;" :class="`tw-w-[${tabWidth}px] ${active === ActiveType.dictEvent ? 'on' : ''}`" @click="() => active === ActiveType.dictEvent || (active = ActiveType.dictEvent)">
            <div class="tw-mb-[6px] tw-flex tw-items-center tw-text-left">
               {{ t("eventBoard.DICT Iincidents") }}
            </div>
            <div>
              <el-radio-group v-model="type" :disabled="state.loading" class="tw-w-full tw-flex-col">
                <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
                  <el-radio v-show="active === ActiveType.dictEvent" :value="TypeEnum.user" class="tw-w-[auto]" @click.stop>{{ t("eventBoard.My open") }}</el-radio>
                  <div class="tw-w-[103px] tw-whitespace-nowrap tw-text-[var(--el-text-color-placeholder)]" v-for="item in dictScopeEvnetCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
                </div>
                <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
                  <el-radio v-show="active === ActiveType.dictEvent" :value="TypeEnum.all" class="tw-w-[auto]" @click.stop>{{ t("eventBoard.All") }}</el-radio>
                  <div class="tw-w-[103px] tw-whitespace-nowrap tw-text-[var(--el-text-color-placeholder)]" v-for="item in dictGlobalEvnetCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
                </div>
              </el-radio-group>
            </div>
          </div> -->

          <!-- DICT请求 -->
          <!-- <div class="tw-overflow-hidden tw-text-[16px] tw-leading-[18px]" style="padding: 8px 15px;" :class="`tw-w-[${tabWidth}px] ${active === ActiveType.dictService ? 'on' : ''}`" @click="() => active === ActiveType.dictService || (active = ActiveType.dictService)">
            <div class="tw-mb-[6px] tw-flex tw-items-center tw-text-left">
               {{ t("eventBoard.DICT Requests") }}
            </div>
            <div>
              <el-radio-group v-model="type" :disabled="state.loading" class="tw-w-full tw-flex-col">
                <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
                  <el-radio v-show="active === ActiveType.dictService" :value="TypeEnum.user" class="tw-w-[auto]" @click.stop>{{ t("eventBoard.My open") }}</el-radio>
                  <div class="tw-w-[103px] tw-whitespace-nowrap tw-text-[var(--el-text-color-placeholder)]" v-for="item in dictScopeServiceCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
                </div>
                <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
                  <el-radio v-show="active === ActiveType.dictService" :value="TypeEnum.all" class="tw-w-[auto]" @click.stop>{{ t("eventBoard.All") }}</el-radio>
                  <div class="tw-w-[103px] tw-whitespace-nowrap tw-text-[var(--el-text-color-placeholder)]" v-for="item in dictGlobalServiceCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
                </div>
              </el-radio-group>
            </div>
          </div> -->
        </div>
      </el-scrollbar>
    </div>
    <pageTemplate v-model:currentPage="state.page" v-model:pageSize="state.size" :total="state.total" :height="height - 145" :show-paging="true" @size-change="handleCommand(command.Request)" @current-change="handleCommand(command.Request)">
      <template #center> </template>
      <template #right>
        <div style="padding: 8px 0; box-sizing: border-box; display: flex">
          <el-button type="default" :disabled="state.loading" :icon="Refresh" @click="handleCommand(command.Request)" style="margin-right: 10px"></el-button>
          <el-switch v-if="false" v-model="tableState" class="ml-2" inline-prompt :active-text="t('eventBoard.Compack')" :inactive-text="t('eventBoard.Details')" @change="handleCommand(command.Refresh)"></el-switch>
        </div>
      </template>
      <template #default="{ height: tableHeight }">
        <!-- 事件表格 -->
        <el-table v-if="active === ActiveType.event" @cell-contextmenu="copyClick" v-loading="state.loading" ref="tableRef" :data="state.list" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).map((v) => v.id))" @expand-change="handleExpand">
          <!-- <el-table-column v-if="userInfo.hasPermission(智能事件中心_事件工单_更新) || userInfo.hasPermission(智能事件中心_事件工单_编辑小记)" :width="55">
            <template #header>
              <div style="display: flex; align-items: center">
                <el-checkbox :model-value="state.list.every((v) => state.select.includes(v.id))" :indeterminate="state.list.every((v) => state.select.includes(v.id)) ? false : state.list.some((v) => state.select.includes(v.id))" :disabled="state.loading" @update:model-value="($event) => (state.select = $event ? state.list.map((v) => v.id) : [])"></el-checkbox>
                <el-dropdown style="margin-left: 8px" @command="($event) => $event.command()">
                  <span class="el-dropdown-link">
                    <el-icon :size="20"><CaretBottom></CaretBottom></el-icon>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item v-if="userInfo.hasPermission(智能事件中心_事件工单_编辑小记)" :disabled="!select.length" :command="{ command: () => handelBatchAddNotes(select) }"> {{ t("eventBoard.Add Journal") }}</el-dropdown-item>
                      <el-dropdown-item v-if="userInfo.hasPermission(智能事件中心_事件工单_更新)" :disabled="!select.length" :command="{ command: () => handelBatchSetPriority(select, priority.P8) }"> {{ t("eventBoard.All P8") }}</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
            <template #default="{ row }">
              <el-checkbox :model-value="state.select.includes(row.id)" :disabled="state.loading" @update:model-value="($event) => void ($event ? state.select.includes(row.id) || state.select.push(row.id) : state.select.includes(row.id) && state.select.splice(state.select.indexOf(row.id), 1))"></el-checkbox>
            </template>
          </el-table-column> -->
          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.priority" @filter-change="handleQuery()" prop="priority" :label="t('eventBoard.Priority')" :width="110" :filters="priorityOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>
          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.state" @filter-change="handleQuery()" prop="eventState" :label="t('eventBoard.State')" :width="110" :filters="eventStateOption.map((v) => ({ value: v.value, text: v.label }))" sortable="custom">
            <template #default="{ row, column }">
              <el-tag :type="(find(eventStateOption, (v) => v.value === row[column.property]) || {}).type" size="small">{{ (find(eventStateOption, (v) => v.value === row[column.property]) || {}).label }}</el-tag>
            </template>
          </TableColumn>

          <TableColumn type="condition" :list="<EventItem[]>[]" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderIds" @filter-change="handleQuery()" prop="id" column-key="id" :label="t('eventBoard.Tickets')" sortable="custom" :width="180" :filters="$filter0">
            <template #default="{ row, column }">
              <el-button type="primary" link @click="handleToOrder(row.orderType, row.id, row.tenantId)">{{ row[column.property] }}</el-button>
            </template>
          </TableColumn>
          <TableColumn type="condition" :list="<EventItem[]>[]" filter-multiple show-filter v-model:custom-filtered-value="searchByAlarmCount" @filter-change="handleQuery()" prop="alarmNumber" column-key="alarmCount" :label="t('eventBoard.Alert')" sortable="custom" :width="100" :filters="$filter1">
            <template #default="{ row, column }">
              <el-text type="danger">{{ row[column.property] || 0 }}</el-text>
            </template>
          </TableColumn>
          <TableColumn type="condition" :list="<EventItem[]>[]" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderSummary" @filter-change="handleQuery()" prop="summary" column-key="summary" :label="t('eventBoard.Summary')" show-overflow-tooltip :min-width="120" :filters="$filter0"></TableColumn>

          <template v-if="tableState">
            <TableColumn
              type="condition"
              :label="t('eventBoard.Close code')"
              prop="completeInfo"
              :formatter="
                (row) => {
                  if (!row.completeInfo) return '--';
                  return row.completeInfo.finishCodeName || row.completeInfo.finishContent || '--';
                }
              "
              show-filter
              v-model:custom-filtered-value="searchByCode"
              :filters="$filter0"
              @filter-change="handleQuery()"
            >
              <template #default="{ row }">
                <template v-if="row.completeInfo">
                  <div v-if="row.completeInfo.finishCodeName">
                    {{ row.completeInfo.finishCodeName }}
                  </div>
                  <div v-else-if="row.completeInfo.finishContent">{{ row.completeInfo.finishContent }}</div>
                  <div v-else>--</div>
                </template>
                <div v-else>--</div>
              </template>
            </TableColumn>
            <!-- <TableColumn type="condition" :label="t('eventBoard.Close code')" prop="completeInfo" :formatter="(_row, _col, _v) => (_v ? _v + (_row.completeInfo ? `[${_row.completeInfo}]` : '') : '--')" show-filter v-model:custom-filtered-value="searchByUserGroupName" :filters="$filter0" @filter-change="handleQuery()"> </TableColumn> -->
            <TableColumn type="date" :list="<EventItem[]>[]" filter-multiple show-filter v-model:filtered-value="timeByUpdate" @filter-change="handleQuery()" prop="updateTime" :label="t('eventBoard.Modified')" sortable="custom" :width="140">
              <template #default="{ row }">
                <div style="width: 100%">{{ moment(Number(row.updateTime), "x").format("YYYY-MM-DD HH:mm:ss") }}</div>
              </template>
            </TableColumn>
          </template>
          <template v-else>
            <TableColumn type="condition" :label="`${t('eventBoard.Create by')}(${t('orderGroup.Order group')})`" prop="userGroupName" show-filter v-model:custom-filtered-value="searchByCompactTicketName" :filters="$filter0" @filter-change="handleQuery()">
              <template #default="{ row }">
                <div>{{ JSON.parse(row.createdBy || "{}").username || "--" }}</div>
                <div>{{ row.ticketGroupName || "--" }}</div>
              </template>
            </TableColumn>
            <TableColumn type="date" :list="<EventItem[]>[]" filter-multiple show-filter v-model:filtered-value="timeByCompact" @filter-change="handleQuery()" prop="createTime" :label="`${t('eventBoard.Created')}(${t('eventBoard.Modified')})`" sortable="custom" :width="180">
              <template #default="{ row }">
                <div style="width: 100%">{{ moment(Number(row.createTime), "x").format("YYYY-MM-DD HH:mm:ss") }}</div>
                <div style="width: 100%">{{ moment(Number(row.updateTime), "x").format("YYYY-MM-DD HH:mm:ss") }}</div>
              </template>
            </TableColumn>
            <TableColumn type="condition" :list="<EventItem[]>[]" filter-multiple show-filter v-model:custom-filtered-value="searchByCompactActorName" @filter-change="handleQuery()" prop="actorName" :label="`${t('eventBoard.Handler')}(${t('eventBoard.Director')}`" :min-width="160" :filters="$filter0">
              <template #default="{ row }">
                <div style="width: 100%">{{ row.actorName ? (row.actorName || "") + ((row as any).actorTenantAbbreviation ? `[${(row as any).actorTenantAbbreviation}]` : "") : (row.userGroupName || "") + ((row as any).userGroupTenantAbbreviation ? `[${(row as any).userGroupTenantAbbreviation}]` : "") }}</div>
                <div style="width: 100%">{{ (row.responsibleName || "") + ((row as any).responsibleTenantAbbreviation ? `[${(row as any).responsibleTenantAbbreviation}]` : "") }}</div>
              </template>
            </TableColumn>
          </template>
        </el-table>
        <!-- 服务请求表格 -->
        <el-table v-if="active === ActiveType.service" @cell-contextmenu="copyClick" v-loading="state.loading" ref="tableRef" :data="state.list" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).map((v) => v.id))" @expand-change="handleExpand">
          <!-- <el-table-column v-if="userInfo.hasPermission(智能事件中心_服务请求工单_更新) || userInfo.hasPermission(智能事件中心_服务请求工单_编辑小记)" :width="55">
            <template #header>
              <div style="display: flex; align-items: center">
                <el-checkbox :model-value="state.list.every((v) => state.select.includes(v.id))" :indeterminate="state.list.every((v) => state.select.includes(v.id)) ? false : state.list.some((v) => state.select.includes(v.id))" :disabled="state.loading" @update:model-value="($event) => (state.select = $event ? state.list.map((v) => v.id) : [])"></el-checkbox>
                <el-dropdown style="margin-left: 8px" @command="($event) => $event.command()">
                  <span class="el-dropdown-link">
                    <el-icon :size="20"><CaretBottom></CaretBottom></el-icon>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item v-if="userInfo.hasPermission(智能事件中心_服务请求工单_编辑小记)" :disabled="!select.length" :command="{ command: () => handelBatchAddNotes(select) }"> {{ t("eventBoard.Add Journal") }}</el-dropdown-item>
                      <el-dropdown-item v-if="userInfo.hasPermission(智能事件中心_服务请求工单_更新)" :disabled="!select.length" :command="{ command: () => handelBatchSetPriority(select, priority.P8) }"> {{ t("eventBoard.All P8") }}</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
            <template #default="{ row }">
              <el-checkbox :model-value="state.select.includes(row.id)" :disabled="state.loading" @update:model-value="($event) => void ($event ? state.select.includes(row.id) || state.select.push(row.id) : state.select.includes(row.id) && state.select.splice(state.select.indexOf(row.id), 1))"></el-checkbox>
            </template>
          </el-table-column> -->
          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.priority" @filter-change="handleQuery()" prop="priority" :label="t('eventBoard.Priority')" :width="120" :filters="priorityOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>
          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.state" @filter-change="handleQuery()" prop="serviceState" :label="t('eventBoard.State')" :width="110" :filters="serviceStateOption.map((v) => ({ value: v.value, text: v.label }))" sortable="custom">
            <template #default="{ row, column }">
              <el-tag :type="(find(serviceStateOption, (v) => v.value === row[column.property]) || {}).type" size="small">{{ (find(serviceStateOption, (v) => v.value === row[column.property]) || {}).label }}</el-tag>
            </template>
          </TableColumn>
          <TableColumn type="condition" :list="state.list" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderIds" @filter-change="handleQuery()" prop="id" column-key="id" :label="t('eventBoard.Tickets')" sortable="custom" :width="180" :filters="$filter0">
            <template #default="{ row, column }">
              <el-button type="primary" link @click="handleToOrder(row.orderType, row.id, row.tenantId)">{{ row[column.property] }}</el-button>
            </template>
          </TableColumn>
          <TableColumn type="condition" :list="state.list" filter-multiple show-filter v-model:custom-filtered-value="searchByAlarmCount" @filter-change="handleQuery()" prop="alarmCount" column-key="alarmCount" :label="t('eventBoard.Alert')" sortable="custom" :width="100" :filters="$filter1">
            <template #default="{ row, column }">
              <el-text type="danger">{{ row[column.property] || 0 }}</el-text>
            </template>
          </TableColumn>
          <TableColumn type="condition" :list="state.list" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderSummary" @filter-change="handleQuery()" prop="title" column-key="title" :label="t('eventBoard.Summary')" show-overflow-tooltip :min-width="120" :filters="$filter0"></TableColumn>

          <template v-if="tableState">
            <TableColumn type="condition" :label="t('eventBoard.Close code')" prop="completeInfo" :formatter="(_row, _col, _v) => (_v ? _v + (_row.completeInfo ? `[${_row.completeInfo}]` : '') : '--')" show-filter v-model:custom-filtered-value="searchByUserGroupName" :filters="$filter0" @filter-change="handleQuery()"> </TableColumn>
            <TableColumn type="date" :list="<ServiceItem[]>[]" filter-multiple show-filter v-model:filtered-value="timeByUpdate" @filter-change="handleQuery()" prop="updateTime" :label="t('eventBoard.Modified')" sortable="custom" :width="140"></TableColumn>
          </template>
          <template v-else>
            <TableColumn type="condition" :label="`${t('eventBoard.Create by')}(${t('orderGroup.Order group')})`" prop="userGroupName" show-filter v-model:custom-filtered-value="searchByCompactTicketName" :filters="$filter0" @filter-change="handleQuery()">
              <template #default="{ row }">
                <div>{{ JSON.parse(row.createdBy || "{}").username || "--" }}</div>
                <div>{{ row.ticketGroupName || "--" }}</div>
              </template>
            </TableColumn>
            <TableColumn type="date" :list="<ServiceItem[]>[]" filter-multiple show-filter v-model:filtered-value="timeByCompact" @filter-change="handleQuery()" prop="createTime" :label="`${t('eventBoard.Created')}(${t('eventBoard.Modified')})`" sortable="custom" :width="180">
              <template #default="{ row }">
                <div style="width: 100%">{{ moment(Number(row.createTime), "x").format("YYYY-MM-DD HH:mm:ss") }}</div>
                <div style="width: 100%">{{ moment(Number(row.updateTime), "x").format("YYYY-MM-DD HH:mm:ss") }}</div>
              </template>
            </TableColumn>
            <TableColumn type="condition" :list="<ServiceItem[]>[]" filter-multiple show-filter v-model:custom-filtered-value="searchByCompactActorName" @filter-change="handleQuery()" prop="actorName" :label="`${t('eventBoard.Handler')}(${t('eventBoard.Director')}`" :min-width="160" :filters="$filter0">
              <template #default="{ row }">
                <div style="width: 100%">{{ (row.currentOwnerName || "") + ((row as any).actorTenantAbbreviation ? `[${(row as any).actorTenantAbbreviation}]` : "") }}</div>
                <div style="width: 100%">{{ (row.responsibleName || "") + ((row as any).responsibleTenantAbbreviation ? `[${(row as any).responsibleTenantAbbreviation}]` : "") }}</div>
              </template>
            </TableColumn>
          </template>
        </el-table>
        <!-- 问题表格 -->
        <el-table v-if="active === ActiveType.question" @cell-contextmenu="copyClick" v-loading="state.loading" ref="tableRef" :data="state.list" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).map((v) => v.id))" @expand-change="handleExpand">
          <!-- <el-table-column v-if="userInfo.hasPermission(智能事件中心_工单看板_批量处理)" :width="55">
            <template #header>
              <div style="display: flex; align-items: center">
                <el-checkbox :model-value="state.list.every((v) => state.select.includes(v.id))" :indeterminate="state.list.every((v) => state.select.includes(v.id)) ? false : state.list.some((v) => state.select.includes(v.id))" :disabled="state.loading" @update:model-value="($event) => (state.select = $event ? state.list.map((v) => v.id) : [])"></el-checkbox>
              </div>
            </template>
            <template #default="{ row }">
              <el-checkbox :model-value="state.select.includes(row.id)" :disabled="state.loading" @update:model-value="($event) => void ($event ? state.select.includes(row.id) || state.select.push(row.id) : state.select.includes(row.id) && state.select.splice(state.select.indexOf(row.id), 1))"></el-checkbox>
            </template>
          </el-table-column> -->
          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.priority" @filter-change="handleQuery()" prop="priority" :label="t('eventBoard.Priority')" :width="120" :filters="priorityOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>
          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.state" @filter-change="handleQuery()" prop="questionState" :label="t('eventBoard.State')" :width="110" :filters="questionStateOption.map((v) => ({ value: v.value, text: v.label }))" sortable="custom">
            <template #default="{ row, column }">
              <el-tag :type="(find(questionStateOption, (v) => v.value === row[column.property]) || {}).type" size="small">{{ (find(questionStateOption, (v) => v.value === row[column.property]) || {}).label }}</el-tag>
            </template>
          </TableColumn>
          <TableColumn type="condition" :list="state.list" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderIds" @filter-change="handleQuery()" prop="id" column-key="id" :label="t('eventBoard.Tickets')" sortable="custom" :width="180" :filters="$filter0">
            <template #default="{ row, column }">
              <el-button type="primary" link @click="handleToOrder(row.orderType, row.id, row.tenantId)">{{ row[column.property] }}</el-button>
            </template>
          </TableColumn>
          <TableColumn type="condition" :list="state.list" filter-multiple show-filter v-model:custom-filtered-value="searchByAlarmCount" @filter-change="handleQuery()" prop="alertNumber" column-key="alertNumber" :label="t('eventBoard.Alert')" sortable="custom" :width="100" :filters="$filter1">
            <template #default="{ row, column }">
              <el-text type="danger">{{ row[column.property] || 0 }}</el-text>
            </template>
          </TableColumn>
          <TableColumn type="condition" :list="state.list" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderSummary" @filter-change="handleQuery()" prop="digest" column-key="digest" :label="t('eventBoard.Summary')" show-overflow-tooltip :min-width="120" :filters="$filter0"></TableColumn>

          <template v-if="tableState">
            <TableColumn
              type="condition"
              :label="t('eventBoard.Close code')"
              prop="completeInfo"
              :formatter="
                (row) => {
                  if (!row.completeInfo) return '--';
                  return row.completeInfo.finishCodeName || row.completeInfo.finishContent || '--';
                }
              "
              show-filter
              v-model:custom-filtered-value="searchByUserGroupName"
              :filters="$filter0"
              @filter-change="handleQuery()"
            >
              <template #default="{ row }">
                <template v-if="row.completeInfo">
                  <div v-if="row.completeInfo.finishCodeName">
                    {{ row.completeInfo.finishCodeName }}
                  </div>
                  <div v-else-if="row.completeInfo.finishContent">{{ row.completeInfo.finishContent }}</div>
                  <div v-else>--</div>
                </template>
                <div v-else>--</div>
              </template>
            </TableColumn>
            <!-- <TableColumn type="condition" :label="t('eventBoard.Close code')" prop="completeInfo" :formatter="(_row, _col, _v) => (_v ? _v + (_row.completeInfo ? `[${_row.completeInfo}]` : '') : '--')" show-filter v-model:custom-filtered-value="searchByUserGroupName" :filters="$filter0" @filter-change="handleQuery()"> </TableColumn> -->
            <TableColumn type="date" :list="<QuestionItem[]>[]" filter-multiple show-filter v-model:filtered-value="timeByUpdate" @filter-change="handleQuery()" prop="updateTime" :label="t('eventBoard.Modified')" sortable="custom" :width="140"></TableColumn>
          </template>
          <template v-else>
            <TableColumn type="condition" :label="`${t('eventBoard.Create by')}(${t('orderGroup.Order group')})`" prop="userGroupName" show-filter v-model:custom-filtered-value="searchByCompactTicketName" :filters="$filter0" @filter-change="handleQuery()">
              <template #default="{ row }">
                <div>{{ JSON.parse(row.creatorName || "{}").username || "--" }}</div>
                <div>{{ row.ticketGroupName || "--" }}</div>
              </template>
            </TableColumn>
            <TableColumn type="date" :list="<QuestionItem[]>[]" filter-multiple show-filter v-model:filtered-value="timeByCompact" @filter-change="handleQuery()" prop="createTime" :label="`${t('eventBoard.Created')}(${t('eventBoard.Modified')})`" sortable="custom" :width="180">
              <template #default="{ row }">
                <div style="width: 100%">{{ moment(Number(row.createTime), "x").format("YYYY-MM-DD HH:mm:ss") }}</div>
                <div style="width: 100%">{{ moment(Number(row.updateTime), "x").format("YYYY-MM-DD HH:mm:ss") }}</div>
              </template>
            </TableColumn>
            <TableColumn type="condition" :list="<QuestionItem[]>[]" filter-multiple show-filter v-model:custom-filtered-value="searchByCompactActorName" @filter-change="handleQuery()" prop="actorName" :label="`${t('eventBoard.Handler')}(${t('eventBoard.Director')}`" :min-width="160" :filters="$filter0">
              <template #default="{ row }">
                <div style="width: 100%">{{ (row.actorName || "") + ((row as any).actorTenantAbbreviation ? `[${(row as any).actorTenantAbbreviation}]` : "") }}</div>
                <div style="width: 100%">{{ (row.responsibleName || "") + ((row as any).responsibleTenantAbbreviation ? `[${(row as any).responsibleTenantAbbreviation}]` : "") }}</div>
              </template>
            </TableColumn>
          </template>
        </el-table>

        <!-- 发布表格 -->
        <el-table v-if="active === ActiveType.publish" @cell-contextmenu="copyClick" v-loading="state.loading" ref="tableRef" :data="state.list" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).map((v) => v.id))" @expand-change="handleExpand">
          <!-- <el-table-column v-if="userInfo.hasPermission(智能事件中心_发布管理_更新)" :width="55">
            <template #header>
              <div style="display: flex; align-items: center">
                <el-checkbox :model-value="state.list.every((v) => state.select.includes(v.id))" :indeterminate="state.list.every((v) => state.select.includes(v.id)) ? false : state.list.some((v) => state.select.includes(v.id))" :disabled="state.loading" @update:model-value="($event) => (state.select = $event ? state.list.map((v) => v.id) : [])"></el-checkbox>
              </div>
            </template>
            <template #default="{ row }">
              <el-checkbox :model-value="state.select.includes(row.id)" :disabled="state.loading" @update:model-value="($event) => void ($event ? state.select.includes(row.id) || state.select.push(row.id) : state.select.includes(row.id) && state.select.splice(state.select.indexOf(row.id), 1))"></el-checkbox>
            </template>
          </el-table-column> -->
          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.priority" @filter-change="handleQuery()" prop="priority" :label="t('eventBoard.Priority')" :width="120" :filters="priorityOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>

          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.state" @filter-change="handleQuery()" prop="publishState" :label="t('eventBoard.State')" :width="110" :filters="publishStateOption.map((v) => ({ value: v.value, text: v.label }))" sortable="custom">
            <template #default="{ row, column }">
              <el-tag :type="(find(publishStateOption, (v) => v.value === row[column.property]) || {}).type" size="small">{{ (find(publishStateOption, (v) => v.value === row[column.property]) || {}).label }}</el-tag>
            </template>
          </TableColumn>
          <TableColumn type="condition" :list="state.list" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderIds" @filter-change="handleQuery()" prop="id" column-key="id" :label="t('eventBoard.Tickets')" sortable="custom" :width="180" :filters="$filter0">
            <template #default="{ row, column }">
              <el-button type="primary" link @click="handleToOrder(row.orderType, row.id, row.tenantId)">{{ row[column.property] }}</el-button>
            </template>
          </TableColumn>
          <TableColumn type="condition" :list="state.list" filter-multiple show-filter v-model:custom-filtered-value="searchByAlarmCount" @filter-change="handleQuery()" prop="alertNumber" column-key="alertNumber" :label="t('eventBoard.Alert')" sortable="custom" :width="100" :filters="$filter1">
            <template #default="{ row, column }">
              <el-text type="danger">{{ row[column.property] || 0 }}</el-text>
            </template>
          </TableColumn>
          <TableColumn type="condition" :list="state.list" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderSummary" @filter-change="handleQuery()" prop="digest" column-key="digest" :label="t('eventBoard.Summary')" show-overflow-tooltip :min-width="120" :filters="$filter0"></TableColumn>
          <template v-if="tableState">
            <TableColumn
              type="condition"
              :label="t('eventBoard.Close code')"
              prop="completeInfo"
              :formatter="
                (row) => {
                  if (!row.completeInfo) return '--';
                  return row.completeInfo.finishCodeName || row.completeInfo.finishContent || '--';
                }
              "
              show-filter
              v-model:custom-filtered-value="searchByUserGroupName"
              :filters="$filter0"
              @filter-change="handleQuery()"
            >
              <template #default="{ row }">
                <template v-if="row.completeInfo">
                  <div v-if="row.completeInfo.finishCodeName">
                    {{ row.completeInfo.finishCodeName }}
                  </div>
                  <div v-else-if="row.completeInfo.finishContent">{{ row.completeInfo.finishContent }}</div>
                  <div v-else>--</div>
                </template>
                <div v-else>--</div>
              </template>
            </TableColumn>
            <TableColumn type="date" :list="<QuestionItem[]>[]" filter-multiple show-filter v-model:filtered-value="timeByUpdate" @filter-change="handleQuery()" prop="updateTime" :label="t('eventBoard.Modified')" sortable="custom" :width="140"></TableColumn>
          </template>
          <template v-else>
            <TableColumn type="condition" :label="`${t('eventBoard.Create by')}(${t('orderGroup.Order group')})`" prop="userGroupName" show-filter v-model:custom-filtered-value="searchByCompactTicketName" :filters="$filter0" @filter-change="handleQuery()">
              <template #default="{ row }">
                <div>{{ JSON.parse(row.creatorName || "{}").username || "--" }}</div>
                <div>{{ row.ticketGroupName || "--" }}</div>
              </template>
            </TableColumn>
            <TableColumn type="date" :list="<QuestionItem[]>[]" filter-multiple show-filter v-model:filtered-value="timeByCompact" @filter-change="handleQuery()" prop="createTime" :label="`${t('eventBoard.Created')}(${t('eventBoard.Modified')})`" sortable="custom" :width="180">
              <template #default="{ row }">
                <div style="width: 100%">{{ moment(Number(row.createTime), "x").format("YYYY-MM-DD HH:mm:ss") }}</div>
                <div style="width: 100%">{{ moment(Number(row.updateTime), "x").format("YYYY-MM-DD HH:mm:ss") }}</div>
              </template>
            </TableColumn>
            <TableColumn type="condition" :list="<QuestionItem[]>[]" filter-multiple show-filter v-model:custom-filtered-value="searchByCompactActorName" @filter-change="handleQuery()" prop="actorName" :label="`${t('eventBoard.Handler')}(${t('eventBoard.Director')}`" :min-width="160" :filters="$filter0">
              <template #default="{ row }">
                <div style="width: 100%">{{ (row.actorName || "") + ((row as any).actorTenantAbbreviation ? `[${(row as any).actorTenantAbbreviation}]` : "") }}</div>
                <div style="width: 100%">{{ (row.responsibleName || "") + ((row as any).responsibleTenantAbbreviation ? `[${(row as any).responsibleTenantAbbreviation}]` : "") }}</div>
              </template>
            </TableColumn>
          </template>
        </el-table>
        <!-- 变更表格 -->
        <el-table v-if="active === ActiveType.change" @cell-contextmenu="copyClick" v-loading="state.loading" ref="tableRef" :data="state.list" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).map((v) => v.id))" @expand-change="handleExpand">
          <!-- <el-table-column v-if="userInfo.hasPermission(智能事件中心_工单看板_批量处理)" :width="55">
            <template #header>
              <div style="display: flex; align-items: center">
                <el-checkbox :model-value="state.list.every((v) => state.select.includes(v.id))" :indeterminate="state.list.every((v) => state.select.includes(v.id)) ? false : state.list.some((v) => state.select.includes(v.id))" :disabled="state.loading" @update:model-value="($event) => (state.select = $event ? state.list.map((v) => v.id) : [])"></el-checkbox>
              </div>
            </template>
            <template #default="{ row }">
              <el-checkbox :model-value="state.select.includes(row.id)" :disabled="state.loading" @update:model-value="($event) => void ($event ? state.select.includes(row.id) || state.select.push(row.id) : state.select.includes(row.id) && state.select.splice(state.select.indexOf(row.id), 1))"></el-checkbox>
            </template>
          </el-table-column> -->
          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.priority" @filter-change="handleQuery()" prop="priority" :label="t('eventBoard.Priority')" :width="120" :filters="priorityOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>

          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.state" @filter-change="handleQuery()" prop="changeState" :label="t('eventBoard.State')" :width="110" :filters="changeStateOption.map((v) => ({ value: v.value, text: v.label }))" sortable="custom">
            <template #default="{ row, column }">
              <el-tag :type="(find(changeStateOption, (v) => v.value === row[column.property]) || {}).type" size="small">{{ (find(changeStateOption, (v) => v.value === row[column.property]) || {}).label }}</el-tag>
            </template>
          </TableColumn>
          <TableColumn type="condition" :list="state.list" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderIds" @filter-change="handleQuery()" prop="id" column-key="id" :label="t('eventBoard.Tickets')" sortable="custom" :width="180" :filters="$filter0">
            <template #default="{ row, column }">
              <el-button type="primary" link @click="handleToOrder(row.orderType, row.id, row.tenantId)">{{ row[column.property] }}</el-button>
            </template>
          </TableColumn>
          <TableColumn type="condition" :list="state.list" filter-multiple show-filter v-model:custom-filtered-value="searchByAlarmCount" @filter-change="handleQuery()" prop="alertNumber" column-key="alertNumber" :label="t('eventBoard.Alert')" sortable="custom" :width="100" :filters="$filter1">
            <template #default="{ row, column }">
              <el-text type="danger">{{ row[column.property] || 0 }}</el-text>
            </template>
          </TableColumn>
          <TableColumn type="condition" :list="state.list" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderSummary" @filter-change="handleQuery()" prop="digest" column-key="digest" :label="t('eventBoard.Summary')" show-overflow-tooltip :min-width="120" :filters="$filter0"></TableColumn>

          <template v-if="tableState">
            <TableColumn type="condition" :label="t('eventBoard.Close code')" prop="completeInfo" :formatter="(_row, _col, _v) => (_v ? _v + (_row.completeInfo ? `[${_row.completeInfo}]` : '') : '--')" show-filter v-model:custom-filtered-value="searchByUserGroupName" :filters="$filter0" @filter-change="handleQuery()"> </TableColumn>
            <TableColumn type="date" :list="<ChangeItem[]>[]" filter-multiple show-filter v-model:filtered-value="timeByUpdate" @filter-change="handleQuery()" prop="updatedTime" :label="t('eventBoard.Modified')" sortable="custom" :width="140"></TableColumn>
          </template>
          <template v-else>
            <TableColumn type="condition" :label="`${t('eventBoard.Create by')}(${t('orderGroup.Order group')})`" prop="teamName" show-filter v-model:custom-filtered-value="searchByCompactTicketName" :filters="$filter0" @filter-change="handleQuery()">
              <template #default="{ row }">
                <div>{{ JSON.parse(row.createdBy || "{}").username || "--" }}</div>
                <div>{{ row.ticketGroupName || "--" }}</div>
              </template>
            </TableColumn>
            <TableColumn type="date" :list="<ChangeItem[]>[]" filter-multiple show-filter v-model:filtered-value="timeByCompact" @filter-change="handleQuery()" prop="createdTime" :label="`${t('eventBoard.Created')}(${t('eventBoard.Modified')})`" sortable="custom" :width="180">
              <template #default="{ row }">
                <div style="width: 100%">{{ moment(Number(row.createdTime), "x").format("YYYY-MM-DD HH:mm:ss") }}</div>
                <div style="width: 100%">{{ moment(Number(row.updatedTime), "x").format("YYYY-MM-DD HH:mm:ss") }}</div>
              </template>
            </TableColumn>
            <TableColumn type="condition" :list="<ChangeItem[]>[]" filter-multiple show-filter v-model:custom-filtered-value="searchByCompactActorName" @filter-change="handleQuery()" prop="actorName" :label="`${t('eventBoard.Handler')}(${t('eventBoard.Director')}`" :min-width="160" :filters="$filter0">
              <template #default="{ row }">
                <div style="width: 100%">{{ (row.actorName || "") + ((row as any).actorTenantAbbreviation ? `[${(row as any).actorTenantAbbreviation}]` : "") }}</div>
                <div style="width: 100%">{{ (jsonToObj(row.createdBy || "").username || "") + ((row as any).responsibleTenantAbbreviation ? `[${(row as any).responsibleTenantAbbreviation}]` : "") }}</div>
              </template>
            </TableColumn>
          </template>
        </el-table>
        <!-- 二维码保障 -->
        <el-table v-if="active === ActiveType.qrcode" @cell-contextmenu="copyClick" v-loading="state.loading" ref="tableRef" :data="state.list" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).map((v) => v.id))" @expand-change="handleExpand">
          <!-- <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByAbbreviation" @filter-change="handleQuery()" prop="tenantName" :label="t('eventBoard.Customer')" :filters="$filter0" :min-width="100"></TableColumn>

          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByName" @filter-change="handleQuery()" prop="projectName" :label="t('eventBoard.Project name')" show-overflow-tooltip :filters="$filter0"></TableColumn>

          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByUnificationCode" @filter-change="handleQuery()" prop="unificationCode" :label="t('eventBoard.Unified Service Code')" show-overflow-tooltip :filters="$filter0"></TableColumn> -->

          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.urgency" @filter-change="handleQuery()" prop="urgency" :label="t('eventBoard.Priority')" :width="110" :filters="urgencyOption.map((v) => ({ ...v, text: v.label }))">
            <template #default="{ row }">
              <div :class="`order-state ${'state-' + row.urgency}`">
                {{ (urgencyOption.find((v) => v.value === row.urgency) || {}).label || row.urgency }}
              </div>
            </template>
          </TableColumn>

          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.status" @filter-change="handleQuery()" prop="urgency" :label="t('eventBoard.State')" :width="110" :filters="statusOption.map((v) => ({ ...v, text: v.label }))">
            <template #default="{ row }">
              <el-tag :type="(statusOption.find((v) => v.value === row.status) || {}).type || 'primary'">{{ (statusOption.find((v) => v.value === row.status) || {}).label || row.status }}</el-tag>
            </template>
          </TableColumn>
          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByKebaoCode" @filter-change="handleQuery()" prop="kebaoCode" :label="t('eventBoard.Customer Ticket Number')" show-overflow-tooltip :filters="$filter0"></TableColumn>
          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByFailureTitle" @filter-change="handleQuery()" prop="failureTitle" :label="t('eventBoard.Ticket title')" show-overflow-tooltip :filters="$filter0"></TableColumn>
          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByFailureDescription" @filter-change="handleQuery()" prop="failureDescription" :label="t('eventBoard.Ticket description')" show-overflow-tooltip :filters="$filter0"></TableColumn>
          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByChargePerson" @filter-change="handleQuery()" prop="chargePerson" :label="t('eventBoard.Director')" :formatter="(_row, _col, _v) => (userInfo.hasPermission(安全管理中心_用户管理_可读) || userInfo.userId === _row.chargePersonId ? _v : '--')" :filters="$filter0"></TableColumn>
          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByProcessors" @filter-change="handleQuery()" prop="processors" :label="t('eventBoard.Handler')" :formatter="(_row, _col, _v) => (userInfo.hasPermission(安全管理中心_用户管理_可读) || userInfo.userId === _row.processorsId ? _v : '--')" :filters="$filter0"></TableColumn>
          <TableColumn type="date" prop="createTime" :label="t('eventBoard.Created')" width="200"></TableColumn>

          <TableColumn :label="t('glob.Cat')" width="100" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link @click="() => qrcodeDateilRef.open(row)">{{ t("glob.info") }}</el-button>
            </template>
          </TableColumn>
          <!-- <TableColumn type="default" :label="t('glob.operate')" width="200" fixed="right" v-if="userInfo.hasPermission(智能事件中心_二维码报障_更新)">
            <template #default="{ row }">
              <div>
                <template v-if="![Status.DEPRECATED].includes(row.status)">
                  <el-button type="danger" link :icon="CloseBold" v-if="!row.kebaoCode" @click="handleQrcodeAbandon(row as any)">{{ t("qrcode.abandon") }}</el-button>
                  <el-button type="primary" link :icon="CircleCheck" v-if="[Status.NEW].includes(row.status)" @click="handleQrcodeUpdate(row as any)">{{ t("qrcode.fixed") }}</el-button>
                  <el-button type="primary" link :icon="EditPen" v-if="[Status.REPORTED].includes(row.status) && !row.kebaoCode" @click="handleQrcodeUpdate(row as any)">{{ t("qrcode.Customer Ticket Number") }}</el-button>
                  <el-button type="primary" link :icon="CircleCheck" v-if="[Status.REPORTED].includes(row.status)" @click="handleQrcodeRepair(row as any)">{{ t("qrcode.Fault reported") }}</el-button>
                </template>
              </div>
            </template>
          </TableColumn> -->
        </el-table>

        <!-- DICT事件列表 -->
        <el-table v-if="active === ActiveType.dictEvent" v-loading="state.loading" ref="tableRef" @cell-contextmenu="copyClick" :data="state.list" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).map((v) => v.id))" @expand-change="handleExpand">
          <el-table-column v-if="userInfo.hasPermission(智能事件中心_DICT事件管理_更新) || userInfo.hasPermission(智能事件中心_DICT事件管理_编辑小记)" :width="55">
            <template #header>
              <div style="display: flex; align-items: center">
                <el-checkbox :model-value="state.list.every((v) => state.select.includes(v.id))" :indeterminate="state.list.every((v) => state.select.includes(v.id)) ? false : state.list.some((v) => state.select.includes(v.id))" :disabled="state.loading" @update:model-value="($event) => (state.select = $event ? state.list.map((v) => v.id) : [])"></el-checkbox>
                <el-dropdown style="margin-left: 8px" @command="($event) => $event.command()">
                  <span class="el-dropdown-link">
                    <el-icon :size="20"><CaretBottom></CaretBottom></el-icon>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item v-if="userInfo.hasPermission(智能事件中心_DICT事件管理_编辑小记)" :disabled="!select.length" :command="{ command: () => handelBatchAddNotes(select) }"> {{ t("eventBoard.Add Journal") }}</el-dropdown-item>
                      <el-dropdown-item v-if="userInfo.hasPermission(智能事件中心_DICT事件管理_更新)" :disabled="!select.length" :command="{ command: () => handelBatchSetPriority(select, priority.P8) }"> {{ t("eventBoard.All P8") }}</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
            <template #default="{ row }">
              <el-checkbox :model-value="state.select.includes(row.id)" :disabled="state.loading" @update:model-value="($event) => void ($event ? state.select.includes(row.id) || state.select.push(row.id) : state.select.includes(row.id) && state.select.splice(state.select.indexOf(row.id), 1))"></el-checkbox>
            </template>
          </el-table-column>
          <TableColumn type="condition" :list="<EventItem[]>[]" filter-multiple show-filter v-model:custom-filtered-value="searchByTenantName" @filter-change="handleQuery()" prop="tenantName" :label="t('eventBoard.Customer')" :width="130" :filters="$filter0">
            <template #default="{ row }">
              <div>{{ row.tenantName }}</div>
              <div v-if="row.tenantAbbreviation">[{{ row.tenantAbbreviation }}]</div>
            </template>
          </TableColumn>
          <TableColumn type="default" :list="<EventItem[]>[]" prop="responseTime" :label="t('eventBoard.Response')" :width="100" :formatter="handleTableResponseTime"></TableColumn>
          <TableColumn type="default" :list="<EventItem[]>[]" prop="resolveTime" :label="t('eventBoard.Resolution')" :width="100" :formatter="handleTableResolveTime"></TableColumn>
          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.priority" @filter-change="handleQuery()" prop="priority" :label="t('eventBoard.Priority')" :width="120" :filters="priorityOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>
          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.state" @filter-change="handleQuery()" prop="eventState" :label="t('eventBoard.State')" :width="110" :filters="eventStateOption.map((v) => ({ value: v.value, text: v.label }))" sortable="custom">
            <template #default="{ row, column }">
              <el-tag :type="(find(eventStateOption, (v) => v.value === row[column.property]) || {}).type" size="small">{{ (find(eventStateOption, (v) => v.value === row[column.property]) || {}).label }}</el-tag>
            </template>
          </TableColumn>
          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderIds" @filter-change="handleQuery()" prop="id" column-key="id" :label="t('eventBoard.Tickets')" sortable="custom" :filters="$filter0" :width="180">
            <template #default="{ row, column }">
              <el-button type="primary" link @click="handleToOrder(row.orderType, row.id, row.tenantId)">{{ row[column.property] }}</el-button>
            </template>
          </TableColumn>

          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderSummary" @filter-change="handleQuery()" prop="summary" :label="t('eventBoard.Summary')" :filters="$filter0" :min-width="120"></TableColumn>
          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByKbServiceCodes" @filter-change="handleQuery()" prop="kbServiceCode" label="客保工单号" :filters="$filter0" :min-width="120"></TableColumn>

          <template v-if="tableState">
            <TableColumn type="condition" :label="t('eventBoard.Close code')" prop="completeInfo" :formatter="(_row, _col, _v) => (_v ? _v + (_row.completeInfo ? `[${_row.completeInfo}]` : '') : '--')" show-filter v-model:custom-filtered-value="searchByUserGroupName" :filters="$filter0" @filter-change="handleQuery()"> </TableColumn>

            <TableColumn type="date" show-filter v-model:filtered-value="timeByUpdate" filter-multiple @filter-change="handleQuery()" prop="updateTime" :label="t('eventBoard.Modified')" sortable="custom" :width="140"></TableColumn>
          </template>
          <template v-else>
            <TableColumn type="condition" :label="`${t('eventBoard.Create by')}(${t('orderGroup.Order group')})`" prop="displayUserGroupName" show-filter v-model:custom-filtered-value="searchByCompactTicketName" :filters="$filter0" @filter-change="handleQuery()">
              <template #default="{ row }">
                <div>{{ JSON.parse(row.createdBy || "{}").username || "--" }}</div>
                <div>{{ row.ticketGroupName || "--" }}</div>
              </template>
            </TableColumn>

            <TableColumn type="date" filter-multiple show-filter v-model:filtered-value="timeByCompact" @filter-change="handleQuery()" prop="createdTime" :label="`${t('eventBoard.Created')}(${t('eventBoard.Modified')})`" sortable="custom" :width="180">
              <template #default="{ row }">
                <div style="width: 100%">{{ moment(Number(row.createTime), "x").format("YYYY-MM-DD HH:mm:ss") }}</div>
                <div style="width: 100%">{{ moment(Number(row.updateTime), "x").format("YYYY-MM-DD HH:mm:ss") }}</div>
              </template>
            </TableColumn>
            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByCompactActorName" @filter-change="handleQuery()" prop="actorName" :label="`${t('eventBoard.Handler')}(${t('eventBoard.Director')}`" :min-width="160" :filters="$filter0">
              <template #default="{ row }">
                <div style="width: 100%">{{ row.actorName ? (row.actorName || "") + (row.actorTenantAbbreviation ? `[${row.actorTenantAbbreviation}]` : "") : (row.userGroupName || "") + (row.userGroupTenantAbbreviation ? `[${row.userGroupTenantAbbreviation}]` : "") }}</div>
                <div style="width: 100%">{{ (row.responsibleName || "") + (row.responsibleTenantAbbreviation ? `[${row.responsibleTenantAbbreviation}]` : "") }}</div>
              </template>
            </TableColumn>
          </template>
        </el-table>
        <!-- DICT服务请求列表 -->
        <el-table v-if="active === ActiveType.dictService" v-loading="state.loading" ref="tableRef" @cell-contextmenu="copyClick" :data="dataList" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).map((v) => v.id))" @expand-change="handleExpand">
          <el-table-column v-if="userInfo.hasPermission(智能事件中心_DICT服务请求_更新) || userInfo.hasPermission(智能事件中心_DICT服务请求_编辑小记)" :width="55">
            <template #header>
              <div style="display: flex; align-items: center">
                <el-checkbox :model-value="state.list.every((v) => state.select.includes(v.id))" :indeterminate="state.list.every((v) => state.select.includes(v.id)) ? false : state.list.some((v) => state.select.includes(v.id))" :disabled="state.loading" @update:model-value="($event) => (state.select = $event ? state.list.map((v) => v.id) : [])"></el-checkbox>
                <el-dropdown style="margin-left: 8px" @command="($event) => $event.command()">
                  <span class="el-dropdown-link">
                    <el-icon :size="20"><CaretBottom></CaretBottom></el-icon>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item v-if="userInfo.hasPermission(智能事件中心_DICT服务请求_编辑小记)" :disabled="!select.length" :command="{ command: () => handelBatchAddNotes(select) }"> {{ t("eventBoard.Add Journal") }}</el-dropdown-item>
                      <el-dropdown-item v-if="userInfo.hasPermission(智能事件中心_DICT服务请求_更新)" :disabled="!select.length" :command="{ command: () => handelBatchSetPriority(select, priority.P8) }"> {{ t("eventBoard.All P8") }}</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
            <template #default="{ row }">
              <el-checkbox :model-value="state.select.includes(row.id)" :disabled="state.loading" @update:model-value="($event) => void ($event ? state.select.includes(row.id) || state.select.push(row.id) : state.select.includes(row.id) && state.select.splice(state.select.indexOf(row.id), 1))"></el-checkbox>
            </template>
          </el-table-column>
          <TableColumn type="condition" :list="<EventItem[]>[]" filter-multiple show-filter v-model:custom-filtered-value="searchByTenantName" @filter-change="handleQuery()" prop="tenantName" :label="t('eventBoard.Customer')" :width="130" :filters="$filter0">
            <template #default="{ row }">
              <div>{{ row.tenantName }}</div>
              <div v-if="row.tenantAbbreviation">[{{ row.tenantAbbreviation }}]</div>
            </template>
          </TableColumn>
          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.priority" @filter-change="handleQuery()" prop="priority" :label="t('eventBoard.Priority')" :width="120" :filters="priorityOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>

          <TableColumn type="enum" filter-multiple show-filter v-model:filtered-value="state.search.state" @filter-change="handleQuery()" prop="serviceState" :label="t('eventBoard.State')" :width="120" :filters="serviceStateOption.map((v) => ({ value: v.value, text: v.label }))" sortable="custom">
            <template #default="{ row, column }">
              <el-tag :type="(find(serviceStateOption, (v) => v.value === row[column.property]) || {}).type" size="small">{{ (find(serviceStateOption, (v) => v.value === row[column.property]) || {}).label }}</el-tag>
            </template>
          </TableColumn>

          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderIds" @filter-change="handleQuery()" prop="id" :label="t('eventBoard.Tickets')" sortable="custom" :filters="$filter0" :width="180">
            <template #default="{ row, column }">
              <router-link v-if="row.id" :to="{ name: '690082193480876032', params: { id: row.id }, query: { fallback: route.name as string } }" custom>
                <template #default="{ href }">
                  <el-link type="primary" :href="href" target="_blank" :underline="false" class="tw-ml-[6px]">{{ row[column.property] }}</el-link>
                </template>
              </router-link>
            </template>
          </TableColumn>
          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByOrderSummary" @filter-change="handleQuery()" prop="title" :label="t('eventBoard.Summary')" :filters="$filter0" :min-width="120"></TableColumn>
          <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByServiceCode" @filter-change="handleQuery()" prop="serviceCode" :label="t('eventBoard.Service Ticket Number')" :filters="$filter0" :min-width="120"></TableColumn>

          <template v-if="tableState">
            <TableColumn type="condition" :label="t('eventBoard.Close code')" prop="completeInfo" :formatter="(_row, _col, _v) => (_v ? _v + (_row.completeInfo ? `[${_row.completeInfo}]` : '') : '--')" show-filter v-model:custom-filtered-value="searchByUserGroupName" :filters="$filter0" @filter-change="handleQuery()"> </TableColumn>

            <TableColumn type="date" show-filter v-model:filtered-value="timeByUpdate" filter-multiple @filter-change="handleQuery()" prop="updateTime" :label="t('eventBoard.Modified')" sortable="custom" :width="140"></TableColumn>
          </template>
          <template v-else>
            <TableColumn type="condition" :label="`${t('eventBoard.Create by')}(${t('orderGroup.Order group')})`" prop="userGroupName" show-filter v-model:custom-filtered-value="searchByCompactTicketName" :filters="$filter0" @filter-change="handleQuery()">
              <template #default="{ row }">
                <div>{{ JSON.parse(row.createdBy || "{}").username || "--" }}</div>
                <div>{{ row.ticketGroupName || "--" }}</div>
              </template>
            </TableColumn>

            <TableColumn type="date" filter-multiple show-filter v-model:filtered-value="timeByCompact" @filter-change="handleQuery()" prop="createdTime" :label="`${t('eventBoard.Created')}(${t('eventBoard.Modified')})`" sortable="custom" :width="180">
              <template #default="{ row }">
                <div style="width: 100%">{{ moment(Number(row.createTime), "x").format("YYYY-MM-DD HH:mm:ss") }}</div>
                <div style="width: 100%">{{ moment(Number(row.updateTime), "x").format("YYYY-MM-DD HH:mm:ss") }}</div>
              </template>
            </TableColumn>
            <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByCompactActorName" @filter-change="handleQuery()" prop="currentOwnerName" :label="`${t('eventBoard.Handler')}(${t('eventBoard.Director')}`" :min-width="160" :filters="$filter0">
              <template #default="{ row }">
                <div style="width: 100%">{{ (row.currentOwnerName || "") + (row.actorTenantAbbreviation ? `[${row.actorTenantAbbreviation}]` : "") }}</div>
                <div style="width: 100%">{{ (row.responsibleName || "") + (row.responsibleTenantAbbreviation ? `[${row.responsibleTenantAbbreviation}]` : "") }}</div>
              </template>
            </TableColumn>
          </template>
        </el-table>
      </template>
    </pageTemplate>
    <!-- </template> -->
  </el-card>
  <EventEnd ref="endRef" :refresh="handleRefresh" @end="closeEvent" :height="height - 317"></EventEnd>
  <EventDictEnd ref="dictEndRef" :refresh="handleRefresh" @end="closeEvent" :height="height - 317"></EventDictEnd>

  <ServiceEnd ref="serviceEndRef" :refresh="handleRefresh" @end="closeEvent" :height="height - 317"></ServiceEnd>
  <ServiceDcitEnd ref="serviceDcitEndRef" :refresh="handleRefresh" @end="closeEvent" :height="height - 317"></ServiceDcitEnd>

  <el-dialog v-model="innerVisible" :title="t('eventBoard.The service ticket is in the process of being closed')" append-to-body :before-close="beforeCloseInnerDialog">
    <div class="close-event">
      <el-scrollbar height="380px" class="close-event-scroll">
        <div class="close-event-list">
          <h3>{{ t("eventBoard.Tickets") }}</h3>
          <ul>
            <li v-for="item in select" :key="item.id">{{ item.id }}</li>
          </ul>
        </div>
        <div class="close-event-list">
          <h3>{{ t("eventBoard.Brief Introduction to the Situation") }}</h3>
          <ul>
            <li v-for="item in closeMessage" :key="item">{{ item }}</li>
          </ul>
        </div>
      </el-scrollbar>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="startPromise" v-if="showStartBtn && showCancel">
          <el-icon><CaretRight /></el-icon> {{ t("eventBoard.Start") }}
        </el-button>
        <el-button @click="cancelPromise" v-if="showCancel">{{ t("eventBoard.Stop") }}</el-button>
        <el-button type="primary" @click="beforeCloseInnerDialog(undefined)">
          <el-icon><Close /></el-icon> {{ t("eventBoard.Cancel") }}
        </el-button>
      </div>
    </template>
  </el-dialog>
  <EventCreateNote ref="eventCreateNoteRef" @refresh="() => handleCommand(command.Request)" :orderType="nodeOrderType" :batchIds="nodeBatchId" />

  <Editor ref="editorRef" title="类型" display="dialog">
    <template #batchConfirm="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">{{ t("eventBoard.Are you sure of the following events in bulk batch P8?") }}</p>
      </div>
      <ol class="tw-pl-4">
        <li v-for="(item, index) in <DataItem[]>params.select" :key="item.id">{{ `${Number(index) + 1}. ${[OrderType.EVENT_ORDER, OrderType.DICT_EVENT_ORDER].includes(item.orderType) ? item.summary : [OrderType.SERVICE_REQUEST, OrderType.DICT_SERVICE_REQUEST].includes(item.orderType) ? item.title : item.orderType === OrderType.CHANGE ? item.digest : item.orderType === OrderType.QUESTION ? item.digest : item.orderType === OrderType.PUBLISH ? item.digest : ""}` }}</li>
      </ol>
    </template>
  </Editor>

  <qrcodeDetail ref="qrcodeDateilRef" :handleQrcodeAbandon="handleQrcodeAbandon" :handleQrcodeUpdate="handleQrcodeUpdate" :handleQrcodeRepair="handleQrcodeRepair"></qrcodeDetail>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { h, ref, readonly, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch, reactive, computed, toValue, defineComponent } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import moment from "moment";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Refresh, Warning, CirclePlus, CaretBottom, CaretRight, Close, Platform, InfoFilled, WarningFilled, CircleCheck, CloseBold, EditPen } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";
import { ElTable, ElIcon, ElForm, ElFormItem, ElInput, FormInstance } from "element-plus";
import Editor from "./Editor.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox, ElProgress } from "element-plus";

import TableColumn from "@/components/tableColumn/TableColumn.vue";
import getUserInfo from "@/utils/getUserInfo";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
import { DataType } from "@/views/pages/apis/deviceManage";
import { questionStateOption } from "@/views/pages/apis/question";
import { publishStateOption } from "@/views/pages/apis/publish";
import { serviceStateOption, eventState, eventStateOption, priority, priorityOption, TimeLimit, urgencyType, urgencyTypeOption } from "@/views/pages/apis/event";
import { changeStateOption, getOrderType } from "@/views/pages/apis/eventBoard";
import { eventCountSate, getEventCount, getDictEventCount, getRequestCount, getDictRequestCount, getQuestionCount, getChangeCount, batchCloseEvent, batchCloseDictEvent, batchCloseService, batchCloseDictService, axiosCancel } from "@/views/pages/apis/eventBoard";
import { QrcodeItem, QrcodeCountData, getQrcodeByFilter, urgencyOption, statusOption, getQrcodeCount, Status, setQrcode } from "@/views/pages/apis/qrcode";
import EventEnd from "./end.vue";
import { filter, find } from "lodash-es";
import axios from "axios";
import EventCreateNote from "@/views/pages/alarm_convergence/details/eventDetail/models/createNote.vue";
import { setEventDataByPriority, setServiceDataByPriority, setDictServiceDataByPriority } from "@/views/pages/apis/event";
import { setEventDataByPriority as setDictEventDataByPriority } from "@/views/pages/apis/dictEvent";
import { routerV6orderHistoryAll } from "@/views/pages/common/routeV6";
import { 智能事件中心_工单看板_可读, 智能事件中心_工单看板_查看历史工单, 智能事件中心_工单看板_批量处理, 智能事件中心_发布管理_更新, 智能事件中心_工单看板_安全, 智能事件中心_工单看板_所有权限, 安全管理中心_用户管理_可读, /* 智能事件中心_工单看板_我的工单, 智能事件中心_工单看板_全部工单, */ 智能事件中心_客户_工单可读, 智能事件中心_问题工单_可读, 智能事件中心_二维码报障_可读, 智能事件中心_变更工单_可读, 智能事件中心_服务请求工单_可读, 智能事件中心_事件工单_可读, 智能事件中心_DICT事件管理_可读, 智能事件中心_DICT服务请求_可读, 智能事件中心_事件工单_更新, 智能事件中心_事件工单_编辑小记, 智能事件中心_服务请求工单_更新, 智能事件中心_服务请求工单_编辑小记, 智能事件中心_DICT事件管理_更新, 智能事件中心_DICT事件管理_编辑小记, 智能事件中心_DICT服务请求_编辑小记, 智能事件中心_DICT服务请求_更新, 智能事件中心_二维码报障_更新 } from "@/views/pages/permission";

import { useSiteConfig } from "@/stores/siteConfig";
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);

import { OrderType } from "@/views/pages/apis/association";
/**
 * 事件管理
 * /event_center/event/list
 * /event_center/event/myList
 **/
import { type EventItem, getEventList } from "@/views/pages/apis/event";
/**
 * 服务请求
 * /event_center/serviceRequest/list
 * /event_center/serviceRequest/myList
 **/
import { type ServiceItem, getServiceList } from "@/views/pages/apis/event";
/**
 * 变更管理
 * /event_center/change
 * /event_center/change/owner
 **/
import { type ChangeItem, getChangeList } from "@/views/pages/apis/change";
/**
 * 问题管理
 * /event_center/question/allList
 **/
import { type QuestionItem, getQuestionList } from "@/views/pages/apis/question";
/**
 * 发布管理
 * /event_center/publish/query
 **/
import { type PublishItem, getPublishList } from "@/views/pages/apis/publish";

import { getEventList as getDictEventList, getServiceList as getDictServiceList } from "@/views/pages/apis/dictEvent";

/**
 *
 * dict
 */
import EventCreateDictNote from "@/views/pages/alarm_convergence/IntelligentEvents/dict_eventManage/createNote.vue";
import EventDictEnd from "@/views/pages/alarm_convergence/IntelligentEvents/dict_eventManage/end.vue";

import ServiceEnd from "@/views/pages/alarm_convergence/IntelligentEvents/serviceRequest/end.vue";
import ServiceCreatetNote from "@/views/pages/alarm_convergence/IntelligentEvents/serviceRequest/createNote.vue";

import ServiceDcitEnd from "@/views/pages/alarm_convergence/IntelligentEvents/dict_serviceRequest/end.vue";
import ServiceDcitCreateNote from "@/views/pages/alarm_convergence/IntelligentEvents/dict_serviceRequest/createNote.vue";

import qrcodeDetail from "@/views/pages/alarm_convergence/IntelligentEvents/qrcode/detail.vue";

import handleToOrder from "@/views/pages/alarm_convergence/IntelligentEvents/eventBoard/toOrder";

import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";
import { start } from "nprogress";

const { t } = useI18n();

const jsonToObj = (json: string): Record<string, string> => {
  try {
    return JSON.parse(json) || {};
  } catch (error) {
    return {};
  }
};

type DataItem = EventItem | ServiceItem | ChangeItem | QuestionItem | PublishItem | QrcodeItem;
/* "alarmNumber" | "alarmCount" | "alertNumber" |  */
type EventParams = Omit<typeof getEventList extends (req: infer P) => any ? P : never, "type" | "paging" | "sort">;
type ServiceParams = Omit<typeof getServiceList extends (req: infer P) => any ? P : never, "type" | "paging" | "sort">;
type ChangeParams = Omit<typeof getChangeList extends (req: infer P) => any ? P : never, "type" | "paging" | "sort">;
type QuestionParams = Omit<typeof getQuestionList extends (req: infer P) => any ? P : never, "type" | "paging" | "sort">;
type PublishParams = Omit<typeof getPublishList extends (req: infer P) => any ? P : never, "type" | "paging" | "sort">;

const orderRoute = readonly<Record<keyof typeof OrderType, string>>({
  /** 事件管理 */
  [OrderType.EVENT_ORDER]: "510685950393712640",
  /** 服务请求 */
  [OrderType.SERVICE_REQUEST]: "514703398516293632",
  /** 变更管理 */
  [OrderType.CHANGE]: "515123784953364480",
  /** 问题管理 */
  [OrderType.QUESTION]: "515035822471249920",
  /** 发布管理 */
  [OrderType.PUBLISH]: "519831507997556736",
  /** dict事件 */
  [OrderType.DICT_EVENT_ORDER]: "689693383307821056",
  /** dict服务请求 */
  [OrderType.DICT_SERVICE_REQUEST]: "690082193480876032",
});

/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const route = useRoute();
const router = useRouter();
defineOptions({ name: "alarmBoard" });
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);
const $filter2 = ref([
  { text: "包含", value: "include" },
  { text: "不包含", value: "exclude" },
]);

const width = inject("width", ref(0));

// const tabWidth = computed(() => Math.trunc(width.value / 5) - Math.trunc(50 / 5));
const tabWidth = ref(115);

const $width = inject<import("vue").Ref<number>>("width", ref(document.body.clientWidth - 200));
const dialogWidth = ref($width.value / 1.75);
const height = inject("height", ref(0));
const orderLink = ref("");
const qrcodeSearchValue = ref("");
const qrcodeSearchStatus = ref("ALL");
interface Props {
  title?: string;
}
const props = withDefaults(defineProps<Props>(), { title: "事件看板" });
const userInfo = getUserInfo();
const siteConfig = useSiteConfig();
const tableState = ref(true);

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

const tableRef = ref<InstanceType<typeof ElTable>>();
const editorRef = ref<InstanceType<typeof Editor>>();
const eventCreateNoteRef = ref<InstanceType<typeof EventCreateNote>>();
// const eventCreateDictNoteRef = ref<InstanceType<typeof EventCreateDictNote>>();
// const serviceCreatetNoteRef = ref<InstanceType<typeof ServiceCreatetNote>>();
// const serviceDcitCreateNoteRef = ref<InstanceType<typeof ServiceDcitCreateNote>>();
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

interface State<T, P> {
  loading: boolean;
  expand: string[];
  select: string[];
  current: string | undefined;
  search: P;
  sort?: { prop: string; order: "ascending" | "descending" };
  list: T[];
  page: number;
  size: number;
  total: number;
}
enum command {
  Refresh = "Refresh",
  Request = "Request",
  Preview = "Preview",
  Create = "Create",
  Update = "Update",
  Modify = "Modify",
  Delete = "Delete",
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const final = readonly({
  pagination: true,
});

type Merge<T1 extends object, T2 extends object, T3 extends object, T4 extends object, T5 extends object, MT = T1 & T2 & T3 & T4 & T5> = { [MK in keyof MT]: MT[MK] };
type DataSrch = Merge<EventParams, ServiceParams, ChangeParams, QuestionParams, PublishParams, Record<string, any>>;
enum ActiveType {
  event = "event",
  service = "service",
  change = "change",
  question = "question",
  qrcode = "qrcode",
  publish = "publish",
  dictEvent = "dictEvent",
  dictService = "dictService",
}

let list = [
  {
    key: ActiveType.event,
    hide: userInfo.hasPermission(智能事件中心_事件工单_可读),
  },
  {
    key: ActiveType.service,
    hide: userInfo.hasPermission(智能事件中心_服务请求工单_可读),
  },
  {
    key: ActiveType.question,
    hide: userInfo.hasPermission(智能事件中心_问题工单_可读),
  },
  {
    key: ActiveType.change,
    hide: userInfo.hasPermission(智能事件中心_变更工单_可读),
  },
  {
    key: ActiveType.qrcode,
    hide: userInfo.hasPermission(智能事件中心_二维码报障_可读),
  },
  {
    key: ActiveType.dictEvent,
    hide: userInfo.hasPermission(智能事件中心_二维码报障_可读),
  },
  {
    key: ActiveType.dictService,
    hide: userInfo.hasPermission(智能事件中心_二维码报障_可读),
  },
];

let k = list.find((item) => item.hide == true);

const active = computed({
  get: () => {
    state.loading = true;
    if (route.query.type === undefined) {
      router.replace({ query: { ...route.query, type: TypeEnum.all, active: "event" } }).finally(async () => {
        await resetData();
        state.loading = false;
        nextTick(() => {
          handleCommand(command.Refresh);
        });
      });
    } else {
      return `${route.query.active}` in ActiveType ? (route.query.active as ActiveType) : (list.find((v) => v.hide) || {}).key;
    }
  },
  set: (v) => {
    state.loading = true;
    router.replace({ query: { ...route.query, type: TypeEnum.all, active: v } }).finally(async () => {
      await resetData();
      state.loading = false;
      nextTick(() => {
        handleCommand(command.Refresh);
      });
    });
  },
});

/**
 * 添加小记类型
 */
enum NodeOrderType {
  event = OrderType.EVENT_ORDER,
  service = OrderType.SERVICE_REQUEST,
  dictEvent = OrderType.DICT_EVENT_ORDER,
  dictService = OrderType.DICT_SERVICE_REQUEST,
  change = OrderType.CHANGE,
  question = OrderType.QUESTION,
  PUBLISH = OrderType.PUBLISH,
}

const nodeOrderType = computed(() => (active.value ? NodeOrderType[active.value] : ""));

const nodeBatchId = ref<string[]>([]);

const activeName = ref("first");
enum TypeEnum {
  user = "user",
  all = "all",
}
const type = computed({
  get: () => {
    // if (!userInfo.hasPermission(智能事件中心_工单看板_我的工单) && !userInfo.hasPermission(智能事件中心_工单看板_全部工单)) return "";
    return `${route.query.type}` in TypeEnum && route.query.type === TypeEnum.all ? TypeEnum.all : TypeEnum.all;
  },
  set: (v) => {
    state.loading = true;
    router.replace({ query: { ...route.query, type: v } }).finally(() => {
      state.loading = false;
      nextTick(() => {
        handleCommand(command.Refresh);
      });
    });
  },
});
const state = reactive<State<DataItem, DataSrch>>({
  loading: true,
  expand: [],
  select: [],
  current: undefined,
  search: {
    includeTenantName: [],
    excludeTenantName: [],
    eqTenantName: [],
    neTenantName: [],
    includeOrderId: [],
    excludeOrderId: [],
    eqOrderId: [],
    neOrderId: [],
    includeState: [],
    excludeState: [],
    eqState: [],
    neState: [],
    includeActorName: [],
    excludeActorName: [],
    eqActorName: [],
    neActorName: [],
    includeResponsibleName: [],
    excludeResponsibleName: [],
    eqResponsibleName: [],
    neResponsibleName: [],
    includeOrderSummary: [],
    excludeOrderSummary: [],
    eqOrderSummary: [],
    neOrderSummary: [],
    tenantNameFilterRelation: "AND",
    orderIdFilterRelation: "AND",
    stateFilterRelation: "AND",
    actorNameFilterRelation: "AND",
    responsibleNameFilterRelation: "AND",
    orderSummaryFilterRelation: "AND",
    eqAlarmCount: [],
    neAlarmCount: [],
    geAlarmCount: [],
    gtAlarmCount: [],
    leAlarmCount: [],
    ltAlarmCount: [],
    isNullAlarmCount: [],
    isNotNullAlarmCount: [],
    alarmCountFilterRelation: "AND",
    includeCompactActorName: [],
    excludeCompactActorName: [],
    eqCompactActorName: [],
    neCompactActorName: [],
    compactActorNameFilterRelation: "AND",

    priority: "",

    state: "",

    createTimeStart: "",
    createTimeEnd: "",
    updateTimeStart: "",
    updateTimeEnd: "",
    createdBy: { userId: "", username: "" },
    updatedBy: { userId: "", username: "" },
    compactTimeStart: "",
    compactTimeEnd: "",

    status: "",
    urgency: "",
    eqName: [],
    includeName: [],
    nameFilterRelation: "AND",
    neName: [],
    excludeName: [],
    eqFailureTitle: [],
    includeFailureTitle: [],
    failureTitleFilterRelation: "AND",
    neFailureTitle: [],
    excludeFailureTitle: [],
    eqFailureDescription: [],
    includeFailureDescription: [],
    failureDescriptionFilterRelation: "AND",
    neFailureDescription: [],
    excludeFailureDescription: [],
    eqUnificationCode: [],
    includeUnificationCode: [],
    unificationCodeFilterRelation: "AND",
    neUnificationCode: [],
    excludeUnificationCode: [],
    eqKebaoCode: [],
    includeKebaoCode: [],
    kebaoCodeFilterRelation: "AND",
    neKebaoCode: [],
    excludeKebaoCode: [],
    chargePerson: "",
    processors: "",

    eqAbbreviation: [],
    includeAbbreviation: [],
    neAbbreviation: [],
    excludeAbbreviation: [],
    abbreviationFilterRelation: "AND",

    includeServiceCodes: [],
    excludeServiceCodes: [],
    eqServiceCodes: [],
    neServiceCodes: [],
    serviceCodesFilterRelation: "AND",

    includeKbServiceCodes: [],
    excludeKbServiceCodes: [],
    eqKbServiceCodes: [],
    neKbServiceCodes: [],
    kbServiceCodesFilterRelation: "AND",

    inTicketGroupName: [],
    excludeTicketGroupName: [],
    eqTicketGroupName: [],
    neTicketGroupName: [],
    ticketGroupNameFilterRelation: "AND",

    inUserGroupName: [],
    excludeUserGroupName: [],
    eqUserGroupName: [],
    neUserGroupName: [],
    userGroupNameFilterRelation: "AND",

    includeFinishCode: [],
    excludeFinishCode: [],
    eqFinishCode: [],
    neFinishCode: [],
    finishCodeFilterRelation: "AND",

    compactEqTicketName: [],
    compactNeTicketName: [],
    compactIncludeTicketName: [],
    compactExcludeTicketName: [],
    compactTicketNameFilterRelation: "AND",

    eqChargePerson: [],
    includeChargePerson: [],
    chargePersonFilterRelation: "AND",
    neChargePerson: [],
    excludeChargePerson: [],

    eqProcessors: [],
    includeProcessors: [],
    processorsFilterRelation: "AND",
    neProcessors: [],
    excludeProcessors: [],
  },
  list: [],
  sort: undefined,
  page: 1,
  size: 50,
  total: 0,
});
const dataList = computed(() => (final.pagination ? state.list.slice((state.page - 1) * state.size, state.page * state.size) : state.list));
const expand = computed(() => filter<DataItem>(state.list, (row) => state.expand.includes(row.id)));
const select = computed(() => filter<DataItem>(state.list, (row) => state.select.includes(row.id)));
const current = computed(() => find<DataItem>(state.list, (row) => row.id === state.current));
// const name = computed(() => state.name);

const timeByCreate = computed({
  get: () => (state.search.createTimeStart && state.search.createTimeEnd ? { start: state.search.createTimeStart, end: state.search.createTimeEnd } : ""),
  set: (v) => {
    state.search.createTimeStart = (v || {}).start || "";
    state.search.createTimeEnd = (v || {}).end || "";
  },
});
const timeByUpdate = computed({
  get: () => (state.search.updateTimeStart && state.search.updateTimeEnd ? { start: state.search.updateTimeStart, end: state.search.updateTimeEnd } : ""),
  set: (v) => {
    state.search.updateTimeStart = (v || {}).start || "";
    state.search.updateTimeEnd = (v || {}).end || "";
  },
});
const timeByCompact = computed({
  get: () => (state.search.compactTimeStart && state.search.compactTimeEnd ? { start: state.search.compactTimeStart, end: state.search.compactTimeEnd } : ""),
  set: (v) => {
    state.search.compactTimeStart = (v || {}).start || "";
    state.search.compactTimeEnd = (v || {}).end || "";
  },
});

const searchType0ByTenantName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByTenantName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByTenantName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByTenantName) === "include") value0 = state.search.includeTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "exclude") value0 = state.search.excludeTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "eq") value0 = state.search.eqTenantName[0] || "";
    if (toValue(searchType0ByTenantName) === "ne") value0 = state.search.neTenantName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByTenantName) === "include") value1 = state.search.includeTenantName[state.search.includeTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "exclude") value1 = state.search.excludeTenantName[state.search.excludeTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "eq") value1 = state.search.eqTenantName[state.search.eqTenantName.length - 1] || "";
    if (toValue(searchType1ByTenantName) === "ne") value1 = state.search.neTenantName[state.search.neTenantName.length - 1] || "";
    return {
      type0: toValue(searchType0ByTenantName),
      type1: toValue(searchType1ByTenantName),
      relation: state.search.tenantNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByTenantName.value = v.type0 as typeof searchType0ByTenantName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByTenantName.value = v.type1 as typeof searchType1ByTenantName extends import("vue").Ref<infer T> ? T : string;
    state.search.tenantNameFilterRelation = v.relation;
    state.search.includeTenantName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeTenantName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqTenantName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neTenantName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByOrderIds = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByOrderIds = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByOrderIds = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByOrderIds) === "include") value0 = state.search.includeOrderId[0] || "";
    if (toValue(searchType0ByOrderIds) === "exclude") value0 = state.search.excludeOrderId[0] || "";
    if (toValue(searchType0ByOrderIds) === "eq") value0 = state.search.eqOrderId[0] || "";
    if (toValue(searchType0ByOrderIds) === "ne") value0 = state.search.neOrderId[0] || "";
    let value1 = "";
    if (toValue(searchType1ByOrderIds) === "include") value1 = state.search.includeOrderId[state.search.includeOrderId.length - 1] || "";
    if (toValue(searchType1ByOrderIds) === "exclude") value1 = state.search.excludeOrderId[state.search.excludeOrderId.length - 1] || "";
    if (toValue(searchType1ByOrderIds) === "eq") value1 = state.search.eqOrderId[state.search.eqOrderId.length - 1] || "";
    if (toValue(searchType1ByOrderIds) === "ne") value1 = state.search.neOrderId[state.search.neOrderId.length - 1] || "";
    return {
      type0: toValue(searchType0ByOrderIds),
      type1: toValue(searchType1ByOrderIds),
      relation: state.search.orderIdFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByOrderIds.value = v.type0 as typeof searchType0ByOrderIds extends import("vue").Ref<infer T> ? T : string;
    searchType1ByOrderIds.value = v.type1 as typeof searchType1ByOrderIds extends import("vue").Ref<infer T> ? T : string;
    state.search.orderIdFilterRelation = v.relation;
    state.search.includeOrderId = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeOrderId = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqOrderId = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neOrderId = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByStates = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByStates = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByStates = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByStates) === "include") value0 = state.search.includeState[0] || "";
    if (toValue(searchType0ByStates) === "exclude") value0 = state.search.excludeState[0] || "";
    if (toValue(searchType0ByStates) === "eq") value0 = state.search.eqState[0] || "";
    if (toValue(searchType0ByStates) === "ne") value0 = state.search.neState[0] || "";
    let value1 = "";
    if (toValue(searchType1ByStates) === "include") value1 = state.search.includeState[state.search.includeState.length - 1] || "";
    if (toValue(searchType1ByStates) === "exclude") value1 = state.search.excludeState[state.search.excludeState.length - 1] || "";
    if (toValue(searchType1ByStates) === "eq") value1 = state.search.eqState[state.search.eqState.length - 1] || "";
    if (toValue(searchType1ByStates) === "ne") value1 = state.search.neState[state.search.neState.length - 1] || "";
    return {
      type0: toValue(searchType0ByStates),
      type1: toValue(searchType1ByStates),
      relation: state.search.stateFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByStates.value = v.type0 as typeof searchType0ByStates extends import("vue").Ref<infer T> ? T : string;
    searchType1ByStates.value = v.type1 as typeof searchType1ByStates extends import("vue").Ref<infer T> ? T : string;
    state.search.stateFilterRelation = v.relation;
    state.search.includeState = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeState = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqState = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neState = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByAlarmCount = ref<"eq" | "ne" | "ge" | "gt" | "le" | "lt" | "isNull" | "isNotNull">("eq");
const searchType1ByAlarmCount = ref<"eq" | "ne" | "ge" | "gt" | "le" | "lt" | "isNull" | "isNotNull">("eq");
const searchByAlarmCount = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByAlarmCount) === "eq") value0 = state.search.eqAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "ne") value0 = state.search.neAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "ge") value0 = state.search.geAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "gt") value0 = state.search.gtAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "le") value0 = state.search.leAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "lt") value0 = state.search.ltAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "isNull") value0 = state.search.isNullAlarmCount[0] || "";
    if (toValue(searchType0ByAlarmCount) === "isNotNull") value0 = state.search.isNotNullAlarmCount[0] || "";
    let value1 = "";
    if (toValue(searchType1ByAlarmCount) === "eq") value1 = state.search.eqAlarmCount[state.search.eqAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "ne") value1 = state.search.neAlarmCount[state.search.neAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "ge") value1 = state.search.geAlarmCount[state.search.geAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "gt") value1 = state.search.gtAlarmCount[state.search.gtAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "le") value1 = state.search.leAlarmCount[state.search.leAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "lt") value1 = state.search.ltAlarmCount[state.search.ltAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "isNull") value1 = state.search.isNullAlarmCount[state.search.isNullAlarmCount.length - 1] || "";
    if (toValue(searchType1ByAlarmCount) === "isNotNull") value1 = state.search.isNotNullAlarmCount[state.search.isNotNullAlarmCount.length - 1] || "";
    return {
      type0: toValue(searchType0ByAlarmCount),
      type1: toValue(searchType1ByAlarmCount),
      relation: state.search.alarmCountFilterRelation,
      value0,
      value1,
      input0: "number",
      input1: "number",
    };
  },
  set: (v) => {
    searchType0ByAlarmCount.value = v.type0 as typeof searchType0ByAlarmCount extends import("vue").Ref<infer T> ? T : string;
    searchType1ByAlarmCount.value = v.type1 as typeof searchType1ByAlarmCount extends import("vue").Ref<infer T> ? T : string;
    state.search.alarmCountFilterRelation = v.relation;
    state.search.eqAlarmCount = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neAlarmCount = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
    state.search.geAlarmCount = [...(v.type0 === "ge" ? [v.value0] : []), ...(v.type1 === "ge" ? [v.value1] : [])];
    state.search.gtAlarmCount = [...(v.type0 === "gt" ? [v.value0] : []), ...(v.type1 === "gt" ? [v.value1] : [])];
    state.search.leAlarmCount = [...(v.type0 === "le" ? [v.value0] : []), ...(v.type1 === "le" ? [v.value1] : [])];
    state.search.ltAlarmCount = [...(v.type0 === "lt" ? [v.value0] : []), ...(v.type1 === "lt" ? [v.value1] : [])];
    state.search.isNullAlarmCount = [...(v.type0 === "isNull" ? [v.value0] : []), ...(v.type1 === "isNull" ? [v.value1] : [])];
    state.search.isNotNullAlarmCount = [...(v.type0 === "isNotNull" ? [v.value0] : []), ...(v.type1 === "isNotNull" ? [v.value1] : [])];
  },
});
const searchType0ByActorName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByActorName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByActorName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByActorName) === "include") value0 = state.search.includeActorName[0] || "";
    if (toValue(searchType0ByActorName) === "exclude") value0 = state.search.excludeActorName[0] || "";
    if (toValue(searchType0ByActorName) === "eq") value0 = state.search.eqActorName[0] || "";
    if (toValue(searchType0ByActorName) === "ne") value0 = state.search.neActorName[0] || "";
    let value1 = "";
    if (toValue(searchType0ByActorName) === "include") value1 = state.search.includeActorName[state.search.includeActorName.length - 1] || "";
    if (toValue(searchType0ByActorName) === "exclude") value1 = state.search.excludeActorName[state.search.excludeActorName.length - 1] || "";
    if (toValue(searchType0ByActorName) === "eq") value1 = state.search.eqActorName[state.search.eqActorName.length - 1] || "";
    if (toValue(searchType0ByActorName) === "ne") value1 = state.search.neActorName[state.search.neActorName.length - 1] || "";
    return {
      type0: toValue(searchType0ByActorName),
      type1: toValue(searchType1ByActorName),
      relation: state.search.actorNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByActorName.value = v.type0 as typeof searchType0ByActorName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByActorName.value = v.type1 as typeof searchType1ByActorName extends import("vue").Ref<infer T> ? T : string;
    state.search.actorNameFilterRelation = v.relation;
    state.search.includeActorName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeActorName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqActorName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neActorName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByResponsibleName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByResponsibleName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByResponsibleName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByResponsibleName) === "include") value0 = state.search.includeResponsibleName[0] || "";
    if (toValue(searchType0ByResponsibleName) === "exclude") value0 = state.search.excludeResponsibleName[0] || "";
    if (toValue(searchType0ByResponsibleName) === "eq") value0 = state.search.eqResponsibleName[0] || "";
    if (toValue(searchType0ByResponsibleName) === "ne") value0 = state.search.neResponsibleName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByResponsibleName) === "include") value1 = state.search.includeResponsibleName[state.search.includeResponsibleName.length - 1] || "";
    if (toValue(searchType1ByResponsibleName) === "exclude") value1 = state.search.excludeResponsibleName[state.search.excludeResponsibleName.length - 1] || "";
    if (toValue(searchType1ByResponsibleName) === "eq") value1 = state.search.eqResponsibleName[state.search.eqResponsibleName.length - 1] || "";
    if (toValue(searchType1ByResponsibleName) === "ne") value1 = state.search.neResponsibleName[state.search.neResponsibleName.length - 1] || "";
    return {
      type0: toValue(searchType0ByResponsibleName),
      type1: toValue(searchType1ByResponsibleName),
      relation: state.search.responsibleNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByResponsibleName.value = v.type0 as typeof searchType0ByResponsibleName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByResponsibleName.value = v.type1 as typeof searchType1ByResponsibleName extends import("vue").Ref<infer T> ? T : string;
    state.search.responsibleNameFilterRelation = v.relation;
    state.search.includeResponsibleName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeResponsibleName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqResponsibleName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neResponsibleName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByOrderSummary = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByOrderSummary = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByOrderSummary = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByOrderSummary) === "include") value0 = state.search.includeOrderSummary[0] || "";
    if (toValue(searchType0ByOrderSummary) === "exclude") value0 = state.search.excludeOrderSummary[0] || "";
    if (toValue(searchType0ByOrderSummary) === "eq") value0 = state.search.eqOrderSummary[0] || "";
    if (toValue(searchType0ByOrderSummary) === "ne") value0 = state.search.neOrderSummary[0] || "";
    let value1 = "";
    if (toValue(searchType1ByOrderSummary) === "include") value1 = state.search.includeOrderSummary[state.search.includeOrderSummary.length - 1] || "";
    if (toValue(searchType1ByOrderSummary) === "exclude") value1 = state.search.excludeOrderSummary[state.search.excludeOrderSummary.length - 1] || "";
    if (toValue(searchType1ByOrderSummary) === "eq") value1 = state.search.eqOrderSummary[state.search.eqOrderSummary.length - 1] || "";
    if (toValue(searchType1ByOrderSummary) === "ne") value1 = state.search.neOrderSummary[state.search.neOrderSummary.length - 1] || "";
    return {
      type0: toValue(searchType0ByOrderSummary),
      type1: toValue(searchType1ByOrderSummary),
      relation: state.search.orderSummaryFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByOrderSummary.value = v.type0 as typeof searchType0ByOrderSummary extends import("vue").Ref<infer T> ? T : string;
    searchType1ByOrderSummary.value = v.type1 as typeof searchType1ByOrderSummary extends import("vue").Ref<infer T> ? T : string;
    state.search.orderSummaryFilterRelation = v.relation;
    state.search.includeOrderSummary = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeOrderSummary = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqOrderSummary = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neOrderSummary = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
const searchType0ByCompactActorName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByCompactActorName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByCompactActorName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByCompactActorName) === "include") value0 = state.search.includeCompactActorName[0] || "";
    if (toValue(searchType0ByCompactActorName) === "exclude") value0 = state.search.excludeCompactActorName[0] || "";
    if (toValue(searchType0ByCompactActorName) === "eq") value0 = state.search.eqCompactActorName[0] || "";
    if (toValue(searchType0ByCompactActorName) === "ne") value0 = state.search.neCompactActorName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByCompactActorName) === "include") value1 = state.search.includeCompactActorName[state.search.includeCompactActorName.length - 1] || "";
    if (toValue(searchType1ByCompactActorName) === "exclude") value1 = state.search.excludeCompactActorName[state.search.excludeCompactActorName.length - 1] || "";
    if (toValue(searchType1ByCompactActorName) === "eq") value1 = state.search.eqCompactActorName[state.search.eqCompactActorName.length - 1] || "";
    if (toValue(searchType1ByCompactActorName) === "ne") value1 = state.search.neCompactActorName[state.search.neCompactActorName.length - 1] || "";
    return {
      type0: toValue(searchType0ByCompactActorName),
      type1: toValue(searchType1ByCompactActorName),
      relation: state.search.compactActorNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByCompactActorName.value = v.type0 as typeof searchType0ByCompactActorName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByCompactActorName.value = v.type1 as typeof searchType1ByCompactActorName extends import("vue").Ref<infer T> ? T : string;
    state.search.compactActorNameFilterRelation = v.relation;
    state.search.includeCompactActorName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeCompactActorName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqCompactActorName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neCompactActorName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByName) === "include") value0 = state.search.includeName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = state.search.excludeName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = state.search.eqName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = state.search.neName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = state.search.includeName[state.search.includeName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = state.search.excludeName[state.search.excludeName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = state.search.eqName[state.search.eqName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = state.search.neName[state.search.neName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: state.search.nameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    state.search.nameFilterRelation = v.relation;
    state.search.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// eqUnificationCode: [],
//     includeUnificationCode: [],
//     unificationCodeFilterRelation: "AND",
//     neUnificationCode: [],
//     excludeUnificationCode: [],
const searchType0ByUnificationCode = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByUnificationCode = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByUnificationCode = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByUnificationCode) === "include") value0 = state.search.includeUnificationCode[0] || "";
    if (toValue(searchType0ByUnificationCode) === "exclude") value0 = state.search.excludeUnificationCode[0] || "";
    if (toValue(searchType0ByUnificationCode) === "eq") value0 = state.search.eqUnificationCode[0] || "";
    if (toValue(searchType0ByUnificationCode) === "ne") value0 = state.search.neUnificationCode[0] || "";
    let value1 = "";
    if (toValue(searchType1ByUnificationCode) === "include") value1 = state.search.includeUnificationCode[state.search.includeUnificationCode.length - 1] || "";
    if (toValue(searchType1ByUnificationCode) === "exclude") value1 = state.search.excludeUnificationCode[state.search.excludeUnificationCode.length - 1] || "";
    if (toValue(searchType1ByUnificationCode) === "eq") value1 = state.search.eqUnificationCode[state.search.eqUnificationCode.length - 1] || "";
    if (toValue(searchType1ByUnificationCode) === "ne") value1 = state.search.neUnificationCode[state.search.neUnificationCode.length - 1] || "";
    return {
      type0: toValue(searchType0ByUnificationCode),
      type1: toValue(searchType1ByUnificationCode),
      relation: state.search.unificationCodeFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByUnificationCode.value = v.type0 as typeof searchType0ByUnificationCode extends import("vue").Ref<infer T> ? T : string;
    searchType1ByUnificationCode.value = v.type1 as typeof searchType1ByUnificationCode extends import("vue").Ref<infer T> ? T : string;
    state.search.unificationCodeFilterRelation = v.relation;
    state.search.includeUnificationCode = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeUnificationCode = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqUnificationCode = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neUnificationCode = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByKebaoCode = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByKebaoCode = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByKebaoCode = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByKebaoCode) === "include") value0 = state.search.includeKebaoCode[0] || "";
    if (toValue(searchType0ByKebaoCode) === "exclude") value0 = state.search.excludeKebaoCode[0] || "";
    if (toValue(searchType0ByKebaoCode) === "eq") value0 = state.search.eqKebaoCode[0] || "";
    if (toValue(searchType0ByKebaoCode) === "ne") value0 = state.search.neKebaoCode[0] || "";
    let value1 = "";
    if (toValue(searchType1ByKebaoCode) === "include") value1 = state.search.includeKebaoCode[state.search.includeKebaoCode.length - 1] || "";
    if (toValue(searchType1ByKebaoCode) === "exclude") value1 = state.search.excludeKebaoCode[state.search.excludeKebaoCode.length - 1] || "";
    if (toValue(searchType1ByKebaoCode) === "eq") value1 = state.search.eqKebaoCode[state.search.eqKebaoCode.length - 1] || "";
    if (toValue(searchType1ByKebaoCode) === "ne") value1 = state.search.neKebaoCode[state.search.neKebaoCode.length - 1] || "";
    return {
      type0: toValue(searchType0ByKebaoCode),
      type1: toValue(searchType1ByKebaoCode),
      relation: state.search.kebaoCodeFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByKebaoCode.value = v.type0 as typeof searchType0ByKebaoCode extends import("vue").Ref<infer T> ? T : string;
    searchType1ByKebaoCode.value = v.type1 as typeof searchType1ByKebaoCode extends import("vue").Ref<infer T> ? T : string;
    state.search.kebaoCodeFilterRelation = v.relation;
    state.search.includeKebaoCode = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeKebaoCode = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqKebaoCode = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neKebaoCode = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// eqFailureTitle: [],
//     includeFailureTitle: [],
//     failureTitleFilterRelation: "AND",
//     neFailureTitle: [],
//     excludeFailureTitle: [],

const searchType0ByFailureTitle = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByFailureTitle = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByFailureTitle = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByFailureTitle) === "include") value0 = state.search.includeFailureTitle[0] || "";
    if (toValue(searchType0ByFailureTitle) === "exclude") value0 = state.search.excludeFailureTitle[0] || "";
    if (toValue(searchType0ByFailureTitle) === "eq") value0 = state.search.eqFailureTitle[0] || "";
    if (toValue(searchType0ByFailureTitle) === "ne") value0 = state.search.neFailureTitle[0] || "";
    let value1 = "";
    if (toValue(searchType1ByFailureTitle) === "include") value1 = state.search.includeFailureTitle[state.search.includeFailureTitle.length - 1] || "";
    if (toValue(searchType1ByFailureTitle) === "exclude") value1 = state.search.excludeFailureTitle[state.search.excludeFailureTitle.length - 1] || "";
    if (toValue(searchType1ByFailureTitle) === "eq") value1 = state.search.eqFailureTitle[state.search.eqFailureTitle.length - 1] || "";
    if (toValue(searchType1ByFailureTitle) === "ne") value1 = state.search.neFailureTitle[state.search.neFailureTitle.length - 1] || "";
    return {
      type0: toValue(searchType0ByFailureTitle),
      type1: toValue(searchType1ByFailureTitle),
      relation: state.search.failureTitleFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByFailureTitle.value = v.type0 as typeof searchType0ByFailureTitle extends import("vue").Ref<infer T> ? T : string;
    searchType1ByFailureTitle.value = v.type1 as typeof searchType1ByFailureTitle extends import("vue").Ref<infer T> ? T : string;
    state.search.failureTitleFilterRelation = v.relation;
    state.search.includeFailureTitle = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeFailureTitle = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqFailureTitle = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neFailureTitle = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// eqFailureDescription: [],
//     includeFailureDescription: [],
//     failureDescriptionFilterRelation: "AND",
//     neFailureDescription: [],
//     excludeFailureDescription: [],

const searchType0ByFailureDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByFailureDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByFailureDescription = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByFailureDescription) === "include") value0 = state.search.includeFailureDescription[0] || "";
    if (toValue(searchType0ByFailureDescription) === "exclude") value0 = state.search.excludeFailureDescription[0] || "";
    if (toValue(searchType0ByFailureDescription) === "eq") value0 = state.search.eqFailureDescription[0] || "";
    if (toValue(searchType0ByFailureDescription) === "ne") value0 = state.search.neFailureDescription[0] || "";
    let value1 = "";
    if (toValue(searchType1ByFailureDescription) === "include") value1 = state.search.includeFailureDescription[state.search.includeFailureDescription.length - 1] || "";
    if (toValue(searchType1ByFailureDescription) === "exclude") value1 = state.search.excludeFailureDescription[state.search.excludeFailureDescription.length - 1] || "";
    if (toValue(searchType1ByFailureDescription) === "eq") value1 = state.search.eqFailureDescription[state.search.eqFailureDescription.length - 1] || "";
    if (toValue(searchType1ByFailureDescription) === "ne") value1 = state.search.neFailureDescription[state.search.neFailureDescription.length - 1] || "";
    return {
      type0: toValue(searchType0ByFailureDescription),
      type1: toValue(searchType1ByFailureDescription),
      relation: state.search.failureDescriptionFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByFailureDescription.value = v.type0 as typeof searchType0ByFailureDescription extends import("vue").Ref<infer T> ? T : string;
    searchType1ByFailureDescription.value = v.type1 as typeof searchType1ByFailureDescription extends import("vue").Ref<infer T> ? T : string;
    state.search.failureDescriptionFilterRelation = v.relation;
    state.search.includeFailureDescription = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeFailureDescription = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqFailureDescription = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neFailureDescription = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByAbbreviation = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByAbbreviation = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByAbbreviation = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByAbbreviation) === "include") value0 = state.search.includeAbbreviation[0] || "";
    if (toValue(searchType0ByAbbreviation) === "exclude") value0 = state.search.excludeAbbreviation[0] || "";
    if (toValue(searchType0ByAbbreviation) === "eq") value0 = state.search.eqAbbreviation[0] || "";
    if (toValue(searchType0ByAbbreviation) === "ne") value0 = state.search.neAbbreviation[0] || "";
    let value1 = "";
    if (toValue(searchType1ByAbbreviation) === "include") value1 = state.search.includeAbbreviation[state.search.includeAbbreviation.length - 1] || "";
    if (toValue(searchType1ByAbbreviation) === "exclude") value1 = state.search.excludeAbbreviation[state.search.excludeAbbreviation.length - 1] || "";
    if (toValue(searchType1ByAbbreviation) === "eq") value1 = state.search.eqAbbreviation[state.search.eqAbbreviation.length - 1] || "";
    if (toValue(searchType1ByAbbreviation) === "ne") value1 = state.search.neAbbreviation[state.search.neAbbreviation.length - 1] || "";
    return {
      type0: toValue(searchType0ByAbbreviation),
      type1: toValue(searchType1ByAbbreviation),
      relation: state.search.abbreviationFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByAbbreviation.value = v.type0 as typeof searchType0ByAbbreviation extends import("vue").Ref<infer T> ? T : string;
    searchType1ByAbbreviation.value = v.type1 as typeof searchType1ByAbbreviation extends import("vue").Ref<infer T> ? T : string;
    state.search.abbreviationFilterRelation = v.relation;
    state.search.includeAbbreviation = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeAbbreviation = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqAbbreviation = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neAbbreviation = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByServiceCode = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByServiceCode = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByServiceCode = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByServiceCode) === "include") value0 = state.search.includeServiceCodes[0] || "";
    if (toValue(searchType0ByServiceCode) === "exclude") value0 = state.search.excludeServiceCodes[0] || "";
    if (toValue(searchType0ByServiceCode) === "eq") value0 = state.search.eqServiceCodes[0] || "";
    if (toValue(searchType0ByServiceCode) === "ne") value0 = state.search.neServiceCodes[0] || "";
    let value1 = "";
    if (toValue(searchType1ByServiceCode) === "include") value1 = state.search.includeServiceCodes[state.search.includeServiceCodes.length - 1] || "";
    if (toValue(searchType1ByServiceCode) === "exclude") value1 = state.search.excludeServiceCodes[state.search.excludeServiceCodes.length - 1] || "";
    if (toValue(searchType1ByServiceCode) === "eq") value1 = state.search.eqServiceCodes[state.search.eqServiceCodes.length - 1] || "";
    if (toValue(searchType1ByServiceCode) === "ne") value1 = state.search.neServiceCodes[state.search.neServiceCodes.length - 1] || "";
    return {
      type0: toValue(searchType0ByServiceCode),
      type1: toValue(searchType1ByServiceCode),
      relation: state.search.serviceCodesFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByServiceCode.value = v.type0 as typeof searchType0ByServiceCode extends import("vue").Ref<infer T> ? T : string;
    searchType1ByServiceCode.value = v.type1 as typeof searchType1ByServiceCode extends import("vue").Ref<infer T> ? T : string;
    state.search.serviceCodesFilterRelation = v.relation;
    state.search.includeServiceCodes = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeServiceCodes = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqServiceCodes = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neServiceCodes = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByKbServiceCodes = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByKbServiceCodes = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByKbServiceCodes = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByKbServiceCodes) === "include") value0 = state.search.includeKbServiceCodes[0] || "";
    if (toValue(searchType0ByKbServiceCodes) === "exclude") value0 = state.search.excludeKbServiceCodes[0] || "";
    if (toValue(searchType0ByKbServiceCodes) === "eq") value0 = state.search.eqKbServiceCodes[0] || "";
    if (toValue(searchType0ByKbServiceCodes) === "ne") value0 = state.search.neKbServiceCodes[0] || "";
    let value1 = "";
    if (toValue(searchType1ByKbServiceCodes) === "include") value1 = state.search.includeKbServiceCodes[state.search.includeKbServiceCodes.length - 1] || "";
    if (toValue(searchType1ByKbServiceCodes) === "exclude") value1 = state.search.excludeKbServiceCodes[state.search.excludeKbServiceCodes.length - 1] || "";
    if (toValue(searchType1ByKbServiceCodes) === "eq") value1 = state.search.eqKbServiceCodes[state.search.eqKbServiceCodes.length - 1] || "";
    if (toValue(searchType1ByKbServiceCodes) === "ne") value1 = state.search.neKbServiceCodes[state.search.neKbServiceCodes.length - 1] || "";
    return {
      type0: toValue(searchType0ByKbServiceCodes),
      type1: toValue(searchType1ByKbServiceCodes),
      relation: state.search.kbServiceCodesFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByKbServiceCodes.value = v.type0 as typeof searchType0ByKbServiceCodes extends import("vue").Ref<infer T> ? T : string;
    searchType1ByKbServiceCodes.value = v.type1 as typeof searchType1ByKbServiceCodes extends import("vue").Ref<infer T> ? T : string;
    state.search.kbServiceCodesFilterRelation = v.relation;
    state.search.includeKbServiceCodes = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeKbServiceCodes = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqKbServiceCodes = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neKbServiceCodes = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// inTicketGroupName: [],
//     excludeTicketGroupName: [],
//     eqTicketGroupName: [],
//     neTicketGroupName: [],
//     ticketGroupNameFilterRelation: "AND",

const searchType0ByTicketGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByTicketGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByTicketGroupName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByTicketGroupName) === "include") value0 = state.search.inTicketGroupName[0] || "";
    if (toValue(searchType0ByTicketGroupName) === "exclude") value0 = state.search.excludeTicketGroupName[0] || "";
    if (toValue(searchType0ByTicketGroupName) === "eq") value0 = state.search.eqTicketGroupName[0] || "";
    if (toValue(searchType0ByTicketGroupName) === "ne") value0 = state.search.neTicketGroupName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByTicketGroupName) === "include") value1 = state.search.inTicketGroupName[state.search.inTicketGroupName.length - 1] || "";
    if (toValue(searchType1ByTicketGroupName) === "exclude") value1 = state.search.excludeTicketGroupName[state.search.excludeTicketGroupName.length - 1] || "";
    if (toValue(searchType1ByTicketGroupName) === "eq") value1 = state.search.eqTicketGroupName[state.search.eqTicketGroupName.length - 1] || "";
    if (toValue(searchType1ByTicketGroupName) === "ne") value1 = state.search.neTicketGroupName[state.search.neTicketGroupName.length - 1] || "";
    return {
      type0: toValue(searchType0ByTicketGroupName),
      type1: toValue(searchType1ByTicketGroupName),
      relation: state.search.ticketGroupNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByTicketGroupName.value = v.type0 as typeof searchType0ByTicketGroupName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByTicketGroupName.value = v.type1 as typeof searchType1ByTicketGroupName extends import("vue").Ref<infer T> ? T : string;
    state.search.ticketGroupNameFilterRelation = v.relation;
    state.search.inTicketGroupName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeTicketGroupName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqTicketGroupName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neTicketGroupName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// inUserGroupName: [],
//     excludeUserGroupName: [],
//     eqUserGroupName: [],
//     neUserGroupName: [],
//     userGroupNameFilterRelation: "AND",

const searchType0ByUserGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByUserGroupName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByUserGroupName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByUserGroupName) === "include") value0 = state.search.inUserGroupName[0] || "";
    if (toValue(searchType0ByUserGroupName) === "exclude") value0 = state.search.excludeUserGroupName[0] || "";
    if (toValue(searchType0ByUserGroupName) === "eq") value0 = state.search.eqUserGroupName[0] || "";
    if (toValue(searchType0ByUserGroupName) === "ne") value0 = state.search.neUserGroupName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByUserGroupName) === "include") value1 = state.search.inUserGroupName[state.search.inUserGroupName.length - 1] || "";
    if (toValue(searchType1ByUserGroupName) === "exclude") value1 = state.search.excludeUserGroupName[state.search.excludeUserGroupName.length - 1] || "";
    if (toValue(searchType1ByUserGroupName) === "eq") value1 = state.search.eqUserGroupName[state.search.eqUserGroupName.length - 1] || "";
    if (toValue(searchType1ByUserGroupName) === "ne") value1 = state.search.neUserGroupName[state.search.neUserGroupName.length - 1] || "";
    return {
      type0: toValue(searchType0ByUserGroupName),
      type1: toValue(searchType1ByUserGroupName),
      relation: state.search.userGroupNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByUserGroupName.value = v.type0 as typeof searchType0ByUserGroupName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByUserGroupName.value = v.type1 as typeof searchType1ByUserGroupName extends import("vue").Ref<infer T> ? T : string;
    state.search.userGroupNameFilterRelation = v.relation;
    state.search.inUserGroupName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeUserGroupName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqUserGroupName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neUserGroupName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByTicketCode = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByTicketCode = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByCode = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByTicketCode) === "include") value0 = state.search.includeFinishCode[0] || "";
    if (toValue(searchType0ByTicketCode) === "exclude") value0 = state.search.excludeFinishCode[0] || "";
    if (toValue(searchType0ByTicketCode) === "eq") value0 = state.search.eqFinishCode[0] || "";
    if (toValue(searchType0ByTicketCode) === "ne") value0 = state.search.neFinishCode[0] || "";
    let value1 = "";
    if (toValue(searchType1ByTicketCode) === "include") value1 = state.search.includeFinishCode[state.search.includeFinishCode.length - 1] || "";
    if (toValue(searchType1ByTicketCode) === "exclude") value1 = state.search.excludeFinishCode[state.search.excludeFinishCode.length - 1] || "";
    if (toValue(searchType1ByTicketCode) === "eq") value1 = state.search.eqFinishCode[state.search.eqFinishCode.length - 1] || "";
    if (toValue(searchType1ByTicketCode) === "ne") value1 = state.search.neFinishCode[state.search.neFinishCode.length - 1] || "";
    return {
      type0: toValue(searchType0ByTicketCode),
      type1: toValue(searchType1ByTicketCode),
      relation: state.search.finishCodeFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByTicketCode.value = v.type0 as typeof searchType0ByTicketCode extends import("vue").Ref<infer T> ? T : string;
    searchType1ByTicketCode.value = v.type1 as typeof searchType1ByTicketCode extends import("vue").Ref<infer T> ? T : string;
    state.search.finishCodeFilterRelation = v.relation;
    state.search.includeFinishCode = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeFinishCode = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqFinishCode = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neFinishCode = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// compactEqTicketName
// compactNeTicketName
// compactIncludeTicketName
// compactExcludeTicketName
// compactTicketNameFilterRelation

const searchType0ByCompactTicketName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByCompactTicketName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByCompactTicketName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByCompactTicketName) === "include") value0 = state.search.compactIncludeTicketName[0] || "";
    if (toValue(searchType0ByCompactTicketName) === "exclude") value0 = state.search.compactExcludeTicketName[0] || "";
    if (toValue(searchType0ByCompactTicketName) === "eq") value0 = state.search.compactEqTicketName[0] || "";
    if (toValue(searchType0ByCompactTicketName) === "ne") value0 = state.search.compactNeTicketName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByCompactTicketName) === "include") value1 = state.search.compactIncludeTicketName[state.search.compactIncludeTicketName.length - 1] || "";
    if (toValue(searchType1ByCompactTicketName) === "exclude") value1 = state.search.compactExcludeTicketName[state.search.compactExcludeTicketName.length - 1] || "";
    if (toValue(searchType1ByCompactTicketName) === "eq") value1 = state.search.compactEqTicketName[state.search.compactEqTicketName.length - 1] || "";
    if (toValue(searchType1ByCompactTicketName) === "ne") value1 = state.search.compactNeTicketName[state.search.compactNeTicketName.length - 1] || "";
    return {
      type0: toValue(searchType0ByCompactTicketName),
      type1: toValue(searchType1ByCompactTicketName),
      relation: state.search.compactTicketNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByCompactTicketName.value = v.type0 as typeof searchType0ByCompactTicketName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByCompactTicketName.value = v.type1 as typeof searchType1ByCompactTicketName extends import("vue").Ref<infer T> ? T : string;
    state.search.compactTicketNameFilterRelation = v.relation;
    state.search.compactIncludeTicketName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.compactExcludeTicketName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.compactEqTicketName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.compactNeTicketName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByChargePerson = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByChargePerson = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByChargePerson = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByChargePerson) === "include") value0 = state.search.includeChargePerson[0] || "";
    if (toValue(searchType0ByChargePerson) === "exclude") value0 = state.search.excludeChargePerson[0] || "";
    if (toValue(searchType0ByChargePerson) === "eq") value0 = state.search.eqChargePerson[0] || "";
    if (toValue(searchType0ByChargePerson) === "ne") value0 = state.search.neChargePerson[0] || "";
    let value1 = "";
    if (toValue(searchType1ByChargePerson) === "include") value1 = state.search.includeChargePerson[state.search.includeChargePerson.length - 1] || "";
    if (toValue(searchType1ByChargePerson) === "exclude") value1 = state.search.excludeChargePerson[state.search.excludeChargePerson.length - 1] || "";
    if (toValue(searchType1ByChargePerson) === "eq") value1 = state.search.eqChargePerson[state.search.eqChargePerson.length - 1] || "";
    if (toValue(searchType1ByChargePerson) === "ne") value1 = state.search.neChargePerson[state.search.neChargePerson.length - 1] || "";
    return {
      type0: toValue(searchType0ByChargePerson),
      type1: toValue(searchType1ByChargePerson),
      relation: state.search.chargePersonFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByChargePerson.value = v.type0 as typeof searchType0ByChargePerson extends import("vue").Ref<infer T> ? T : string;
    searchType1ByChargePerson.value = v.type1 as typeof searchType1ByChargePerson extends import("vue").Ref<infer T> ? T : string;
    state.search.chargePersonFilterRelation = v.relation;
    state.search.includeChargePerson = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeChargePerson = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqChargePerson = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neChargePerson = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// eqProcessors: [],
//     includeProcessors: [],
//     processorsFilterRelation: "AND",
//     neProcessors: [],
//     excludeProcessors: [],

const searchType0ByProcessors = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByProcessors = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByProcessors = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByProcessors) === "include") value0 = state.search.includeProcessors[0] || "";
    if (toValue(searchType0ByProcessors) === "exclude") value0 = state.search.excludeProcessors[0] || "";
    if (toValue(searchType0ByProcessors) === "eq") value0 = state.search.eqProcessors[0] || "";
    if (toValue(searchType0ByProcessors) === "ne") value0 = state.search.neProcessors[0] || "";
    let value1 = "";
    if (toValue(searchType1ByProcessors) === "include") value1 = state.search.includeProcessors[state.search.includeProcessors.length - 1] || "";
    if (toValue(searchType1ByProcessors) === "exclude") value1 = state.search.excludeProcessors[state.search.excludeProcessors.length - 1] || "";
    if (toValue(searchType1ByProcessors) === "eq") value1 = state.search.eqProcessors[state.search.eqProcessors.length - 1] || "";
    if (toValue(searchType1ByProcessors) === "ne") value1 = state.search.neProcessors[state.search.neProcessors.length - 1] || "";
    return {
      type0: toValue(searchType0ByProcessors),
      type1: toValue(searchType1ByProcessors),
      relation: state.search.processorsFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByProcessors.value = v.type0 as typeof searchType0ByProcessors extends import("vue").Ref<infer T> ? T : string;
    searchType1ByProcessors.value = v.type1 as typeof searchType1ByProcessors extends import("vue").Ref<infer T> ? T : string;
    state.search.processorsFilterRelation = v.relation;
    state.search.includeProcessors = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeProcessors = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqProcessors = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neProcessors = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
function handleExpand(row: DataItem, expandedRows: DataItem[]) {
  state.expand = expandedRows.map(({ id }) => id);
  if (find(expandedRows, ({ id }) => row.id === id)) {
    /*  */
  } else {
    /*  */
  }
}
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
async function resetData() {
  state.list = [];
  state.page = 1;
  state.size = 50;
  state.total = 0;

  state.search.includeTenantName = [];
  state.search.excludeTenantName = [];
  state.search.eqTenantName = [];
  state.search.neTenantName = [];
  state.search.includeOrderId = [];
  state.search.excludeOrderId = [];
  state.search.eqOrderId = [];
  state.search.neOrderId = [];
  state.search.includeState = [];
  state.search.excludeState = [];
  state.search.eqState = [];
  state.search.neState = [];
  state.search.includeActorName = [];
  state.search.excludeActorName = [];
  state.search.eqActorName = [];
  state.search.neActorName = [];
  state.search.includeResponsibleName = [];
  state.search.excludeResponsibleName = [];
  state.search.eqResponsibleName = [];
  state.search.neResponsibleName = [];
  state.search.includeOrderSummary = [];
  state.search.excludeOrderSummary = [];
  state.search.eqOrderSummary = [];
  state.search.neOrderSummary = [];
  state.search.tenantNameFilterRelation = "AND";
  state.search.orderIdFilterRelation = "AND";
  state.search.stateFilterRelation = "AND";
  state.search.actorNameFilterRelation = "AND";
  state.search.responsibleNameFilterRelation = "AND";
  state.search.orderSummaryFilterRelation = "AND";
  state.search.eqAlarmCount = [];
  state.search.neAlarmCount = [];
  state.search.geAlarmCount = [];
  state.search.gtAlarmCount = [];
  state.search.leAlarmCount = [];
  state.search.ltAlarmCount = [];
  state.search.isNullAlarmCount = [];
  state.search.isNotNullAlarmCount = [];
  state.search.alarmCountFilterRelation = "AND";
  state.search.includeCompactActorName = [];
  state.search.excludeCompactActorName = [];
  state.search.eqCompactActorName = [];
  state.search.neCompactActorName = [];
  state.search.compactActorNameFilterRelation = "AND";
  state.search.createTimeStart = "";
  state.search.createTimeEnd = "";
  state.search.updateTimeStart = "";
  state.search.updateTimeEnd = "";
  state.search.createdBy = { userId: "", username: "" };
  state.search.updatedBy = { userId: "", username: "" };
  state.search.compactTimeStart = "";
  state.search.compactTimeEnd = "";
  state.search.state = "";

  state.search.eqAbbreviation = [];
  state.search.includeAbbreviation = [];
  state.search.neAbbreviation = [];
  state.search.excludeAbbreviation = [];
  state.search.abbreviationFilterRelation = "AND";

  state.search.includeTenantName = [];
  state.search.excludeTenantName = [];
  state.search.eqTenantName = [];
  state.search.neTenantName = [];
  state.search.tenantNameFilterRelation = "AND";

  state.search.priority = "";

  state.search.includeUnificationCode = [];
  state.search.excludeUnificationCode = [];
  state.search.eqUnificationCode = [];
  state.search.neUnificationCode = [];
  state.search.unificationCodeFilterRelation = "AND";

  state.search.includeName = [];
  state.search.excludeName = [];
  state.search.eqName = [];
  state.search.neName = [];
  state.search.nameFilterRelation = "AND";

  state.search.urgency = "";

  state.search.includeKebaoCode = [];
  state.search.excludeKebaoCode = [];
  state.search.eqKebaoCode = [];
  state.search.neKebaoCode = [];
  state.search.kebaoCodeFilterRelation = "AND";

  state.search.includeFailureTitle = [];
  state.search.excludeFailureTitle = [];
  state.search.eqFailureTitle = [];
  state.search.neFailureTitle = [];
  state.search.failureTitleFilterRelation = "AND";

  state.search.includeFailureDescription = [];
  state.search.excludeFailureDescription = [];
  state.search.eqFailureDescription = [];
  state.search.neFailureDescription = [];
  state.search.failureDescriptionFilterRelation = "AND";

  state.search.includeServiceCodes = [];
  state.search.excludeServiceCodes = [];
  state.search.eqServiceCodes = [];
  state.search.neServiceCodes = [];
  state.search.serviceCodesFilterRelation = "AND";

  state.search.includeKbServiceCodes = [];
  state.search.excludeKbServiceCodes = [];
  state.search.eqKbServiceCodes = [];
  state.search.neKbServiceCodes = [];
  state.search.kbServiceCodesFilterRelation = "AND";

  state.search.inTicketGroupName = [];
  state.search.excludeTicketGroupName = [];
  state.search.eqTicketGroupName = [];
  state.search.neTicketGroupName = [];
  state.search.ticketGroupNameFilterRelation = "AND";

  state.search.inUserGroupName = [];
  state.search.excludeUserGroupName = [];
  state.search.eqUserGroupName = [];
  state.search.neUserGroupName = [];
  state.search.userGroupNameFilterRelation = "AND";

  state.search.includeFinishCode = [];
  state.search.excludeFinishCode = [];
  state.search.eqFinishCode = [];
  state.search.neFinishCode = [];
  state.search.finishCodeFilterRelation = "AND";

  state.search.eqChargePerson = [];
  state.search.includeChargePerson = [];
  state.search.chargePersonFilterRelation = "AND";
  state.search.neChargePerson = [];
  state.search.excludeChargePerson = [];

  state.search.eqProcessors = [];
  state.search.includeProcessors = [];
  state.search.processorsFilterRelation = "AND";
  state.search.neProcessors = [];
  state.search.excludeProcessors = [];
  await nextTick();
}
/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */

/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const timer = ref<null | number>(null);
const autoRefreshTime = ref(0);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  // if (!userInfo.hasPermission(智能事件中心_工单看板_我的工单) && !userInfo.hasPermission(智能事件中心_工单看板_全部工单)) return;
  handleRefresh().then(() => (autoRefreshTime.value = 60));
  state.loading = true;
  getAllEvent();
  getAllService();
  getAllQuestion();
  getAllChange();
  getAllPublish();
  getAllQrcode();
}

// const tenantNameObj = ref({
//   firstName: "",
//   status: "include",
//   type: "include",
//   lastName: "",
//   relation: "AND",
// });
// const orderObj = ref({
//   firstName: "",
//   status: "include",
//   type: "include",
//   lastName: "",
//   relation: "AND",
// });
// const checkAll = ref(false);

// const isIndeterminate = ref(false);
// const chooseIdList = ref([]);
// const chooseEventList = ref([]);
// const handleCheckAllChange = (val: string | number | boolean) => {
//   state.select = val ? state.list.map((v) => v.id) : [];
// };
// function chooseTable() {
//   let ids = [];
//   let list = [];
//   state.list.forEach((v, i) => {
//     if (v.checkbox) {
//       ids.push(v.id);
//       list.push(v);
//     }
//   });
//   chooseEventList.value = [...list];

//   chooseIdList.value = Array.from(new Set(ids));
//   const allcheck = state.list.every((obj) => obj.checkbox === true);

//   isIndeterminate.value = allcheck ? false : true;
// }

const endRef = ref<InstanceType<typeof EventEnd>>();
const dictEndRef = ref<InstanceType<typeof EventDictEnd>>();
const serviceEndRef = ref<InstanceType<typeof ServiceEnd>>();
const serviceDcitEndRef = ref<InstanceType<typeof ServiceDcitEnd>>();

const innerVisible = ref(false);
const completeInfo = ref({});

const showCancel = ref(true);
let source: any = null;
const closeMessage = ref<string[]>([]);
const showStartBtn = ref(true);
const globalType = ref("all");

function getLabel(key) {
  const labels = {
    createCount: "新增",
    deleteCount: "废弃",
    makeCount: "已保障",
    disposeCount: "已修复",
  };
  return labels[key] || key; // 如果没有匹配标签，返回原始键名
}
function cancelPromise() {
  source && source.cancel();
  axiosCancel();
  showStartBtn.value = false;
}

function beforeUpdate() {}
function updated() {}
function activated() {}

function timeZoneSwitching(): number {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == userInfo.zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}

function deactivated() {
  if (timer.value !== null) {
    window.clearInterval(timer.value);
    timer.value = null;
  }
}
function beforeDestroy() {
  if (timer.value !== null) {
    window.clearInterval(timer.value);
    timer.value = null;
  }
}
function destroyed() {}

async function ruoterOrder(id) {
  if (!id) return;
  if (!/^[0-9]*$/g.test(id)) return;
  try {
    state.loading = true;
    const { success, message, data } = await getOrderType({ id });
    if (!success) throw Object.assign(new Error(message), { success, data });
    const link = document.createElement("a");
    link.target = "_blank";
    switch (data) {
      case DataType.EVENT_ORDER: {
        link.href = router.resolve({ name: "510685950393712640", params: { id }, query: { fallback: route.name as string } }).href;
        break;
      }
      case DataType.SERVICE_REQUEST: {
        link.href = router.resolve({ name: "514703398516293632", params: { id }, query: { fallback: route.name as string } }).href;
        break;
      }
      case DataType.CHANGE: {
        link.href = router.resolve({ name: "515123784953364480", params: { id }, query: { fallback: route.name as string } }).href;
        break;
      }
      case DataType.QUESTION: {
        link.href = router.resolve({ name: "515035822471249920", params: { id }, query: { fallback: route.name as string } }).href;
        break;
      }
      case DataType.DICT_EVENT_ORDER: {
        link.href = router.resolve({ name: orderRoute.DICT_EVENT_ORDER, params: { id }, query: { fallback: route.name as string } }).href;
        break;
      }
      case DataType.DICT_SERVICE_REQUEST: {
        link.href = router.resolve({ name: orderRoute.DICT_SERVICE_REQUEST, params: { id }, query: { fallback: route.name as string } }).href;
        break;
      }
      // case DataType.PUBLISH: {
      //   break;
      // }
      default: {
        throw new Error(`没有找到"${id}"工单`);
      }
    }
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : `${error}`);
  } finally {
    state.loading = false;
  }
}
function handleClick(tab, event) {}
// function handleToOrder(routerName, routerParams, tenantId) {
//   const fallback: string = route.name as string;
//   userInfo.cutTenant(tenantId).finally(async () => {
//     const routeData = router.resolve({
//       // name: routerName,
//       path: {
//         [orderRoute.EVENT_ORDER]: `/event/intelligent_events/details/${routerParams.id}`,
//         [orderRoute.SERVICE_REQUEST]: `/event/intelligent_request/details/${routerParams.id}`,
//         [orderRoute.CHANGE]: `/event/intelligent_change/details/${routerParams.id}`,
//         [orderRoute.PUBLISH]: `/event/intelligent_publish/details/${routerParams.id}`,
//         [orderRoute.QUESTION]: `/event/intelligent_question/details/${routerParams.id}`,
//         [orderRoute.DICT_EVENT_ORDER]: `/event/intelligent_events/dict_event/details/${routerParams.id}`,
//         [orderRoute.DICT_SERVICE_REQUEST]: `/event/intelligent_events/dict_serviceRequest/details/${routerParams.id}`,
//       }[routerName],
//       // params: routerParams,
//       query: {
//         fallback,
//       },
//     });
//     // window.open(routeData.href);
//     const link = document.createElement("a");
//     link.href = routeData.href;
//     link.target = "_blank"; // 强制新窗口
//     link.rel = "noopener noreferrer"; // 安全防护
//     link.style.opacity = "0";
//     link.style.position = "absolute";
//     link.style.pointerEvents = "none";
//     document.body.appendChild(link);
//     link.click();
//     setTimeout(() => {
//       document.body.removeChild(link);
//     }, 100);
//   });
// }

function handleTableResponseTime(row: EventItem) {
  if (!Number(row.responseLimit)) return "--";
  const percentage: number = (((Number(row.responseTime) || 0) / (Number(row.responseLimit) || 0)) * 100 > 100 ? 100 : (Number(row.responseTime) / Number(row.responseLimit)) * 100) || 0;
  const color = row.eventState != "NEW" ? "#CCC" : setSlaState((row.slaTimeLimit || {}).responseTimeLimits || [], Number(row.responseTime) || 0).color || "";
  return h("div", [h("div", { class: "tw-text-center" }, `${convertMinutes(row.responseTime)} / ${convertMinutes(row.responseLimit)}`), h(ElProgress, { percentage, color, showText: false, class: "tw-w-full" })]);

  // return h("div", [h("div", { class: "tw-text-center" }, `${row.responseTime}分钟 / ${row.responseLimit}分钟`), h(ElProgress, { percentage, color, showText: false, class: "tw-w-full" })]);
}

function handleTableResolveTime(row: EventItem) {
  if (!Number(row.resolveLimit)) return "--";
  const percentage: number = (((Number(row.resolveTime) || 0) / (Number(row.resolveLimit) || 0)) * 100 > 100 ? 100 : (Number(row.resolveTime) / Number(row.resolveLimit)) * 100) || 0;
  const color = [eventState.COMPLETED, eventState.CLOSED, eventState.AUTO_CLOSED].includes(row.eventState || ("" as eventState)) ? "#ccc" : setSlaState((row.slaTimeLimit || {}).completedTimeLimits || [], Number(row.resolveTime) || 0).color;
  return h("div", [h("div", { class: "tw-text-center" }, `${convertMinutes(row.resolveTime)} / ${convertMinutes(row.resolveLimit)}`), h(ElProgress, { percentage, color, showText: false, class: "tw-w-full" })]);

  // return h("div", [h("div", { class: "tw-text-center" }, `${row.resolveTime}分钟 / ${row.resolveLimit}分钟`), h(ElProgress, { percentage, color, showText: false, class: "tw-w-full" })]);
}

function setSlaState(list: TimeLimit[], val: number) {
  let result = urgencyType.BREACH;
  // list.sort((a, b) => (Number(b.tolerateMinutes) || 0) - (Number(a.tolerateMinutes) || 0)).forEach((el) => ((Number(el.tolerateMinutes) || 0) >= val || list.length === 1) && (result = el.urgencyType));
  // return find(urgencyTypeOption, ({ value }) => value === result) || { label: "", value: "", color: "#BBBCBE" };
  // let result = urgencyType.BREACH;

  // if(list.length>0){
  //   list.unshift({
  //     urgencyType: "NORMAL",
  //     tolerateMinutes: 0,
  //   });
  // }

  list.sort((a, b) => Number(a.tolerateMinutes) - Number(b.tolerateMinutes));

  let index = list.findIndex((v, i) => {
    if (Number(v.tolerateMinutes) > val) return i;
  });
  if (index != -1) result = list[index - 1].urgencyType;
  else result = list[list.length - 1].urgencyType;
  if (val < Number(list[0].tolerateMinutes)) result = urgencyType.NORMAL;

  // list.sort((a, b) => (Number(b.tolerateMinutes) || 0) - (Number(a.tolerateMinutes) || 0)).forEach((el) => ((Number(el.tolerateMinutes) || 0) >= val || list.length === 1) && (result = el.urgencyType));
  return find(urgencyTypeOption, ({ value }) => value === result) || { label: "", value: "", color: "#eee" };
}

function convertMinutes(minutes: any) {
  var seconds = minutes * 60; // 将分钟转化为秒数

  var years = Math.floor(seconds / 31536000); // 每年有31536000秒（平均）
  seconds -= years * 31536000;

  var months = Math.floor(seconds / 2592000); // 每月有2592000秒（平均）
  seconds -= months * 2592000;

  var weeks = Math.floor(seconds / 604800); // 每周有604800秒（平均）
  seconds -= weeks * 604800;

  var days = Math.floor(seconds / 86400); // 每天有86400秒（平均）
  seconds -= days * 86400;

  var hours = Math.floor(seconds / 3600); // 每小时有3600秒
  seconds -= hours * 3600;

  const mins = minutes % 60;

  return `${years > 0 ? years + "Y" : ""}${months > 0 ? months + "M" : ""}${weeks > 0 ? weeks + "W" : ""}${days > 0 ? days + "D" : ""}${hours > 0 ? hours + "H" : ""}${mins > 0 ? mins + "m" : ""}`;
}

watch(autoRefreshTime, (autoRefreshTime) => {
  if (timer.value !== null) timer.value = (window.clearInterval(timer.value), null);
  if (autoRefreshTime) timer.value = window.setInterval(queryData, autoRefreshTime * 1000);
});

/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
async function handleCommand(type: command) {
  if (toValue(state.loading)) return;
  const time = autoRefreshTime.value;
  autoRefreshTime.value = 0;
  try {
    state.loading = true;
    await nextTick();
    await router.isReady();
    switch (type) {
      case command.Refresh:
        await resetData();
        await queryData();
        break;
      case command.Request:
        await queryData();
        break;
    }
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await resetData();
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
      await queryData();
    }
  } finally {
    autoRefreshTime.value = time;
    state.loading = false;
  }
}
function copyClick(row, column, cell, event) {
  event.stopPropagation();
}

async function handleRefresh() {
  try {
    state.loading = true;
    await nextTick();
    await resetData();
    // state.loading = false;
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // state.loading = false;
  }
}
async function handleQuery() {
  try {
    state.loading = true;
    await nextTick();
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
/* 事件 */
const scopeEvnetCount = ref<{ label: string; count: number }[]>([]);
const globalEvnetCount = ref<{ label: string; count: number }[]>([]);
async function initEventCountInfo() {
  if (!userInfo.hasPermission(智能事件中心_事件工单_可读)) return;
  await Promise.all([
    (async () => {
      const { success, message, data } = await getEventCount({ boardOrNot: true, type: "my", id: userInfo.userId });
      if (!success) throw Object.assign(new Error(message), { success, data });
      scopeEvnetCount.value = (data instanceof Array ? data : []).map((v) => ({ label: eventCountSate[v.eventState], count: Number(v.eventCount) || 0 })).filter((v) => v.label);
    })(),
    (async () => {
      const { success, message, data } = await getEventCount({ boardOrNot: true, type: "all" });
      if (!success) throw Object.assign(new Error(message), { success, data });
      globalEvnetCount.value = (data instanceof Array ? data : []).map((v) => ({ label: eventCountSate[v.eventState], count: Number(v.eventCount) || 0 })).filter((v) => v.label);
    })(),
  ]).catch((error) => Promise.resolve(void ElMessage.error(error instanceof Error ? error.message : `${error}`)));
}
/* 服务 */
const scopeServiceCount = ref<{ label: string; count: number }[]>([]);
const globalServiceCount = ref<{ label: string; count: number }[]>([]);
async function initServiceCountInfo() {
  if (!userInfo.hasPermission(智能事件中心_服务请求工单_可读)) return;

  await Promise.all([
    (async () => {
      const { success, message, data } = await getRequestCount({ type: "my", tenantId: userInfo.currentTenantId });
      if (!success) throw Object.assign(new Error(message), { success, data });
      scopeServiceCount.value = (data instanceof Array ? data : []).map((v) => ({ label: eventCountSate[v.serviceState], count: Number(v.serviceCount) || 0 })).filter((v) => v.label);
    })(),
    (async () => {
      const { success, message, data } = await getRequestCount({ type: "all", id: userInfo.currentTenantId });
      if (!success) throw Object.assign(new Error(message), { success, data });
      globalServiceCount.value = (data instanceof Array ? data : []).map((v) => ({ label: eventCountSate[v.serviceState], count: Number(v.serviceCount) || 0 })).filter((v) => v.label);
    })(),
  ]).catch((error) => Promise.resolve(void ElMessage.error(error instanceof Error ? error.message : `${error}`)));
}

/* DICT事件 */
const dictScopeEvnetCount = ref<{ label: string; count: number }[]>([]);
const dictGlobalEvnetCount = ref<{ label: string; count: number }[]>([]);
async function initDictEventCountInfo() {
  if (!userInfo.hasPermission(智能事件中心_DICT事件管理_可读)) return;
  await Promise.all([
    (async () => {
      const { success, message, data } = await getDictEventCount({ boardOrNot: true, type: "my", id: userInfo.userId });
      if (!success) throw Object.assign(new Error(message), { success, data });
      dictScopeEvnetCount.value = (data instanceof Array ? data : []).map((v) => ({ label: eventCountSate[v.eventState], count: Number(v.eventCount) || 0 })).filter((v) => v.label);
    })(),
    (async () => {
      const { success, message, data } = await getDictEventCount({ boardOrNot: true, type: "all" });
      if (!success) throw Object.assign(new Error(message), { success, data });
      dictGlobalEvnetCount.value = (data instanceof Array ? data : []).map((v) => ({ label: eventCountSate[v.eventState], count: Number(v.eventCount) || 0 })).filter((v) => v.label);
    })(),
  ]).catch((error) => Promise.resolve(void ElMessage.error(error instanceof Error ? error.message : `${error}`)));
}
/* DICT服务服务请求 */
const dictScopeServiceCount = ref<{ label: string; count: number }[]>([]);
const dictGlobalServiceCount = ref<{ label: string; count: number }[]>([]);
async function initDictServiceCountInfo() {
  if (!userInfo.hasPermission(智能事件中心_DICT服务请求_可读)) return;

  await Promise.all([
    (async () => {
      const { success, message, data } = await getDictRequestCount({ type: "my", tenantId: userInfo.currentTenantId });
      if (!success) throw Object.assign(new Error(message), { success, data });
      dictScopeServiceCount.value = (data instanceof Array ? data : []).map((v) => ({ label: eventCountSate[v.serviceState], count: Number(v.serviceCount) || 0 })).filter((v) => v.label);
    })(),
    (async () => {
      const { success, message, data } = await getDictRequestCount({ type: "all", id: userInfo.currentTenantId });
      if (!success) throw Object.assign(new Error(message), { success, data });
      dictGlobalServiceCount.value = (data instanceof Array ? data : []).map((v) => ({ label: eventCountSate[v.serviceState], count: Number(v.serviceCount) || 0 })).filter((v) => v.label);
    })(),
  ]).catch((error) => Promise.resolve(void ElMessage.error(error instanceof Error ? error.message : `${error}`)));
}

/* 问题 */
const scopeQuestionCount = ref<{ label: string; count: number }[]>([]);
const globalQuestionCount = ref<{ label: string; count: number }[]>([]);
async function initQuestionCountInfo() {
  if (!userInfo.hasPermission(智能事件中心_问题工单_可读)) return;

  await Promise.all([
    (async () => {
      const { success, message, data } = await getQuestionCount({ type: "my", id: userInfo.userId });
      if (!success) throw Object.assign(new Error(message), { success, data });
      scopeQuestionCount.value = (data instanceof Array ? data : []).map((v) => ({ label: eventCountSate[v.questionState], count: Number(v.count) || 0 })).filter((v) => v.label);
    })(),
    (async () => {
      const { success, message, data } = await getQuestionCount({ type: "all" });
      if (!success) throw Object.assign(new Error(message), { success, data });
      globalQuestionCount.value = (data instanceof Array ? data : []).map((v) => ({ label: eventCountSate[v.questionState], count: Number(v.count) || 0 })).filter((v) => v.label);
    })(),
  ]).catch((error) => Promise.resolve(void ElMessage.error(error instanceof Error ? error.message : `${error}`)));
}
/* 变更 */
const scopeChangeCount = ref<{ label: string; count: number }[]>([]);
const globalChangeCount = ref<{ label: string; count: number }[]>([]);
async function initChangeCountInfo() {
  if (!userInfo.hasPermission(智能事件中心_变更工单_可读)) return;

  await Promise.all([
    (async () => {
      const { success, message, data } = await getChangeCount({ type: "owner" });
      if (!success) throw Object.assign(new Error(message), { success, data });
      scopeChangeCount.value = (data instanceof Array ? data : []).map((v) => ({ label: eventCountSate[v.state], count: Number(v.count) || 0 })).filter((v) => v.label);
    })(),
    (async () => {
      const { success, message, data } = await getChangeCount({ type: "" });
      if (!success) throw Object.assign(new Error(message), { success, data });
      globalChangeCount.value = (data instanceof Array ? data : []).map((v) => ({ label: eventCountSate[v.state], count: Number(v.count) || 0 })).filter((v) => v.label);
    })(),
  ]).catch((error) => Promise.resolve(void ElMessage.error(error instanceof Error ? error.message : `${error}`)));
}

/* 二维码报障 */

const mineQrcodeCount = ref<QrcodeCountData>({});
const allQrcodeCount = ref<QrcodeCountData>({});
async function initQrcodeCountInfo() {
  if (!userInfo.hasPermission(智能事件中心_二维码报障_可读)) return;
  await Promise.all([
    (async () => {
      const { success, message, data } = await getQrcodeCount({ type: "mine" });
      if (!success) throw Object.assign(new Error(message), { success, data });
      mineQrcodeCount.value = data;
    })(),
    (async () => {
      const { success, message, data } = await getQrcodeCount({ type: "all" });
      if (!success) throw Object.assign(new Error(message), { success, data });
      allQrcodeCount.value = data;
    })(),
  ]).catch((error) => Promise.resolve(void ElMessage.error(error instanceof Error ? error.message : `${error}`)));
}

// async function clickQrcode(type) {
//   globalType.value = type;
//   const { success, message, data } = await getQrcodeCount({ type });
//   if (!success) throw Object.assign(new Error(message), { success, data });

//   scopeQrcodeCount.value = data;
// }

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
async function queryData() {
  const sort: string[] = [];
  const getPropKey = (prop: string) => {
    switch (toValue(active)) {
      case ActiveType.event:
        return prop === "createTime" ? "createdTime" : prop === "eventState" ? "state" : prop === "updateTime" ? "updatedTime" : prop;
      case ActiveType.service:
        return prop;
      case ActiveType.change:
        return prop;
      case ActiveType.question:
        return prop === "createTime" ? "createdTime" : prop === "updateTime" ? "updatedTime" : prop;
      default:
        return prop;
    }
  };
  switch ((state.sort || {}).order) {
    case "ascending": {
      sort.push(`${getPropKey(state.sort!.prop)},asc`);
      if (!toValue(tableState)) sort.includes("createdTime,asc") ? sort.push("updatedTime,asc") : sort.includes("createTime,asc") ? sort.push("updateTime,asc") : void 0;
      break;
    }
    case "descending": {
      sort.push(`${getPropKey(state.sort!.prop)},desc`);
      if (!toValue(tableState)) sort.includes("createdTime,desc") || sort.includes("createTime,desc") ? sort.push("updatedTime,desc") : sort.includes("createTime,desc") ? sort.push("updateTime,desc") : void 0;
      break;
    }
  }
  await router.isReady();
  await Promise.all([
    initEventCountInfo(),
    initServiceCountInfo(),
    initQuestionCountInfo(),
    initChangeCountInfo(),
    initQrcodeCountInfo(),
    initDictEventCountInfo(),
    initDictServiceCountInfo(),
    (async () => {
      switch (toValue(active)) {
        case ActiveType.event: {
          // if (!userInfo.hasPermission(智能事件中心_事件工单_可读)) return;
          const { success, message, data, page, size, total } = await getEventList({
            /*  */
            priority: state.search.priority,
            queryType: "CustOrder",
            state: state.search.state,
            compactTimeStart: state.search.compactTimeStart,
            compactTimeEnd: state.search.compactTimeEnd,
            createTimeStart: state.search.createTimeStart,
            createTimeEnd: state.search.createTimeEnd,
            updateTimeStart: state.search.updateTimeStart,
            updateTimeEnd: state.search.updateTimeEnd,
            includeTenantName: state.search.includeTenantName,
            excludeTenantName: state.search.excludeTenantName,
            eqTenantName: state.search.eqTenantName,
            neTenantName: state.search.neTenantName,
            includeOrderId: state.search.includeOrderId,
            excludeOrderId: state.search.excludeOrderId,
            eqOrderId: state.search.eqOrderId,
            neOrderId: state.search.neOrderId,
            includeState: state.search.includeState,
            excludeState: state.search.excludeState,
            eqState: state.search.eqState,
            neState: state.search.neState,
            includeActorName: state.search.includeActorName,
            excludeActorName: state.search.excludeActorName,
            eqActorName: state.search.eqActorName,
            neActorName: state.search.neActorName,
            includeResponsibleName: state.search.includeResponsibleName,
            excludeResponsibleName: state.search.excludeResponsibleName,
            eqResponsibleName: state.search.eqResponsibleName,
            neResponsibleName: state.search.neResponsibleName,
            includeOrderSummary: state.search.includeOrderSummary,
            excludeOrderSummary: state.search.excludeOrderSummary,
            eqOrderSummary: state.search.eqOrderSummary,
            neOrderSummary: state.search.neOrderSummary,
            tenantNameFilterRelation: state.search.tenantNameFilterRelation,
            orderIdFilterRelation: state.search.orderIdFilterRelation,
            stateFilterRelation: state.search.stateFilterRelation,
            actorNameFilterRelation: state.search.actorNameFilterRelation,
            responsibleNameFilterRelation: state.search.responsibleNameFilterRelation,
            orderSummaryFilterRelation: state.search.orderSummaryFilterRelation,
            eqAlarmCount: state.search.eqAlarmCount,
            neAlarmCount: state.search.neAlarmCount,
            geAlarmCount: state.search.geAlarmCount,
            gtAlarmCount: state.search.gtAlarmCount,
            leAlarmCount: state.search.leAlarmCount,
            ltAlarmCount: state.search.ltAlarmCount,
            isNullAlarmCount: state.search.isNullAlarmCount,
            isNotNullAlarmCount: state.search.isNotNullAlarmCount,
            alarmCountFilterRelation: state.search.alarmCountFilterRelation,
            includeCompactActorName: state.search.includeCompactActorName,
            excludeCompactActorName: state.search.excludeCompactActorName,
            eqCompactActorName: state.search.eqCompactActorName,
            neCompactActorName: state.search.neCompactActorName,
            compactActorNameFilterRelation: state.search.compactActorNameFilterRelation,

            inTicketGroupName: state.search.inTicketGroupName,
            excludeTicketGroupName: state.search.excludeTicketGroupName,
            eqTicketGroupName: state.search.eqTicketGroupName,
            neTicketGroupName: state.search.neTicketGroupName,
            ticketGroupNameFilterRelation: state.search.ticketGroupNameFilterRelation,
            inUserGroupName: state.search.inUserGroupName,
            excludeUserGroupName: state.search.excludeUserGroupName,
            eqUserGroupName: state.search.eqUserGroupName,
            neUserGroupName: state.search.neUserGroupName,
            userGroupNameFilterRelation: state.search.userGroupNameFilterRelation,

            includeFinishCode: state.search.includeFinishCode,
            excludeFinishCode: state.search.excludeFinishCode,
            eqFinishCode: state.search.eqFinishCode,
            neFinishCode: state.search.neFinishCode,
            finishCodeFilterRelation: state.search.finishCodeFilterRelation,
            compactEqTicketName: state.search.compactEqTicketName,
            compactNeTicketName: state.search.compactNeTicketName,
            compactIncludeTicketName: state.search.compactIncludeTicketName,
            compactExcludeTicketName: state.search.compactExcludeTicketName,
            compactTicketNameFilterRelation: state.search.compactTicketNameFilterRelation,

            type: toValue(type) === TypeEnum.user ? "myList" : "list",
            tenantId: userInfo.currentTenantId,
            sort,
            paging: { pageNumber: state.page, pageSize: state.size },
            boardOrNot: "true",
          });
          if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
          state.list =
            data instanceof Array
              ? data.map((item) => {
                  return {
                    ...item,
                    updateTime: Number(item.updateTime) + timeZoneSwitching(),
                    createTime: Number(item.createTime) + timeZoneSwitching(),
                  };
                })
              : [];
          state.page = Number(page) || 1;
          state.size = Number(size) || 20;
          state.total = Number(total) || 0;
          break;
        }
        case ActiveType.service: {
          // if (!userInfo.hasPermission(智能事件中心_服务请求工单_可读)) return;
          const { success, message, data, page, size, total } = await getServiceList({
            /*  */
            priority: state.search.priority,
            state: state.search.state,
            queryType: "CustOrder",
            compactTimeStart: state.search.compactTimeStart,
            compactTimeEnd: state.search.compactTimeEnd,
            createTimeStart: state.search.createTimeStart,
            createTimeEnd: state.search.createTimeEnd,
            updateTimeStart: state.search.updateTimeStart,
            updateTimeEnd: state.search.updateTimeEnd,
            includeTenantName: state.search.includeTenantName,
            excludeTenantName: state.search.excludeTenantName,
            eqTenantName: state.search.eqTenantName,
            neTenantName: state.search.neTenantName,
            includeState: state.search.includeState,
            excludeState: state.search.excludeState,
            eqState: state.search.eqState,
            neState: state.search.neState,
            includeOrderId: state.search.includeOrderId,
            excludeOrderId: state.search.excludeOrderId,
            eqOrderId: state.search.eqOrderId,
            neOrderId: state.search.neOrderId,
            includeActorName: state.search.includeActorName,
            excludeActorName: state.search.excludeActorName,
            eqActorName: state.search.eqActorName,
            neActorName: state.search.neActorName,
            includeResponsibleName: state.search.includeResponsibleName,
            excludeResponsibleName: state.search.excludeResponsibleName,
            eqResponsibleName: state.search.eqResponsibleName,
            neResponsibleName: state.search.neResponsibleName,
            includeOrderSummary: state.search.includeOrderSummary,
            excludeOrderSummary: state.search.excludeOrderSummary,
            eqOrderSummary: state.search.eqOrderSummary,
            neOrderSummary: state.search.neOrderSummary,
            tenantNameFilterRelation: state.search.tenantNameFilterRelation,
            stateFilterRelation: state.search.stateFilterRelation,
            orderIdFilterRelation: state.search.orderIdFilterRelation,
            actorNameFilterRelation: state.search.actorNameFilterRelation,
            responsibleNameFilterRelation: state.search.responsibleNameFilterRelation,
            orderSummaryFilterRelation: state.search.orderSummaryFilterRelation,
            eqAlarmCount: state.search.eqAlarmCount,
            neAlarmCount: state.search.neAlarmCount,
            geAlarmCount: state.search.geAlarmCount,
            gtAlarmCount: state.search.gtAlarmCount,
            leAlarmCount: state.search.leAlarmCount,
            ltAlarmCount: state.search.ltAlarmCount,
            isNullAlarmCount: state.search.isNullAlarmCount,
            isNotNullAlarmCount: state.search.isNotNullAlarmCount,
            alarmCountFilterRelation: state.search.alarmCountFilterRelation,
            includeCompactActorName: state.search.includeCompactActorName,
            excludeCompactActorName: state.search.excludeCompactActorName,
            eqCompactActorName: state.search.eqCompactActorName,
            neCompactActorName: state.search.neCompactActorName,
            compactActorNameFilterRelation: state.search.compactActorNameFilterRelation,

            inTicketGroupName: state.search.inTicketGroupName,
            excludeTicketGroupName: state.search.excludeTicketGroupName,
            eqTicketGroupName: state.search.eqTicketGroupName,
            neTicketGroupName: state.search.neTicketGroupName,
            ticketGroupNameFilterRelation: state.search.ticketGroupNameFilterRelation,
            inUserGroupName: state.search.inUserGroupName,
            excludeUserGroupName: state.search.excludeUserGroupName,
            eqUserGroupName: state.search.eqUserGroupName,
            neUserGroupName: state.search.neUserGroupName,
            userGroupNameFilterRelation: state.search.userGroupNameFilterRelation,

            includeFinishCode: state.search.includeFinishCode,
            excludeFinishCode: state.search.excludeFinishCode,
            eqFinishCode: state.search.eqFinishCode,
            neFinishCode: state.search.neFinishCode,
            finishCodeFilterRelation: state.search.finishCodeFilterRelation,
            compactEqTicketName: state.search.compactEqTicketName,
            compactNeTicketName: state.search.compactNeTicketName,
            compactIncludeTicketName: state.search.compactIncludeTicketName,
            compactExcludeTicketName: state.search.compactExcludeTicketName,
            compactTicketNameFilterRelation: state.search.compactTicketNameFilterRelation,

            type: toValue(type) === TypeEnum.user ? "myList" : "list",
            tenantId: userInfo.currentTenantId,
            sort,
            paging: { pageNumber: state.page, pageSize: state.size },
            boardOrNot: "true",
          });
          if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
          state.list =
            data instanceof Array
              ? data.map((item) => {
                  return {
                    ...item,
                    updateTime: Number(item.updateTime) + timeZoneSwitching(),
                    createTime: Number(item.createTime) + timeZoneSwitching(),
                  };
                })
              : [];
          state.page = Number(page) || 1;
          state.size = Number(size) || 20;
          state.total = Number(total) || 0;
          break;
        }
        case ActiveType.change: {
          // if (!userInfo.hasPermission(智能事件中心_变更工单_可读)) return;
          const { success, message, data, page, size, total } = await getChangeList({
            /*  */
            priority: state.search.priority,
            state: state.search.state,
            queryType: "CustOrder",
            compactTimeStart: state.search.compactTimeStart,
            compactTimeEnd: state.search.compactTimeEnd,
            createTimeStart: state.search.createTimeStart,
            createTimeEnd: state.search.createTimeEnd,
            updateTimeStart: state.search.updateTimeStart,
            updateTimeEnd: state.search.updateTimeEnd,
            includeTenantName: state.search.includeTenantName,
            excludeTenantName: state.search.excludeTenantName,
            eqTenantName: state.search.eqTenantName,
            neTenantName: state.search.neTenantName,
            includeOrderId: state.search.includeOrderId,
            excludeOrderId: state.search.excludeOrderId,
            eqOrderId: state.search.eqOrderId,
            neOrderId: state.search.neOrderId,
            includeOrderSummary: state.search.includeOrderSummary,
            excludeOrderSummary: state.search.excludeOrderSummary,
            eqOrderSummary: state.search.eqOrderSummary,
            neOrderSummary: state.search.neOrderSummary,
            includeState: state.search.includeState,
            excludeState: state.search.excludeState,
            eqState: state.search.eqState,
            neState: state.search.neState,
            includeActorName: state.search.includeActorName,
            excludeActorName: state.search.excludeActorName,
            eqActorName: state.search.eqActorName,
            neActorName: state.search.neActorName,
            includeResponsibleName: state.search.includeResponsibleName,
            excludeResponsibleName: state.search.excludeResponsibleName,
            eqResponsibleName: state.search.eqResponsibleName,
            neResponsibleName: state.search.neResponsibleName,
            tenantNameFilterRelation: state.search.tenantNameFilterRelation,
            orderIdFilterRelation: state.search.orderIdFilterRelation,
            orderSummaryFilterRelation: state.search.orderSummaryFilterRelation,
            stateFilterRelation: state.search.stateFilterRelation,
            actorNameFilterRelation: state.search.actorNameFilterRelation,
            responsibleNameFilterRelation: state.search.responsibleNameFilterRelation,
            eqAlarmCount: state.search.eqAlarmCount,
            neAlarmCount: state.search.neAlarmCount,
            geAlarmCount: state.search.geAlarmCount,
            gtAlarmCount: state.search.gtAlarmCount,
            leAlarmCount: state.search.leAlarmCount,
            ltAlarmCount: state.search.ltAlarmCount,
            isNullAlarmCount: state.search.isNullAlarmCount,
            isNotNullAlarmCount: state.search.isNotNullAlarmCount,
            alarmCountFilterRelation: state.search.alarmCountFilterRelation,
            includeCompactActorName: state.search.includeCompactActorName,
            excludeCompactActorName: state.search.excludeCompactActorName,
            eqCompactActorName: state.search.eqCompactActorName,
            neCompactActorName: state.search.neCompactActorName,
            compactActorNameFilterRelation: state.search.compactActorNameFilterRelation,
            createdBy: state.search.createdBy,
            updatedBy: state.search.updatedBy,
            inTicketGroupName: state.search.inTicketGroupName,
            excludeTicketGroupName: state.search.excludeTicketGroupName,
            eqTicketGroupName: state.search.eqTicketGroupName,
            neTicketGroupName: state.search.neTicketGroupName,
            ticketGroupNameFilterRelation: state.search.ticketGroupNameFilterRelation,
            inUserGroupName: state.search.inUserGroupName,
            excludeUserGroupName: state.search.excludeUserGroupName,
            eqUserGroupName: state.search.eqUserGroupName,
            neUserGroupName: state.search.neUserGroupName,
            userGroupNameFilterRelation: state.search.userGroupNameFilterRelation,
            includeFinishCode: state.search.includeFinishCode,
            excludeFinishCode: state.search.excludeFinishCode,
            eqFinishCode: state.search.eqFinishCode,
            neFinishCode: state.search.neFinishCode,
            finishCodeFilterRelation: state.search.finishCodeFilterRelation,
            compactEqTicketName: state.search.compactEqTicketName,
            compactNeTicketName: state.search.compactNeTicketName,
            compactIncludeTicketName: state.search.compactIncludeTicketName,
            compactExcludeTicketName: state.search.compactExcludeTicketName,
            compactTicketNameFilterRelation: state.search.compactTicketNameFilterRelation,
            type: toValue(type) === TypeEnum.user ? "owner" : "",
            sort,
            paging: { pageNumber: state.page, pageSize: state.size },
          });
          if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
          state.list =
            data instanceof Array
              ? data.map((item) => {
                  return {
                    ...item,
                    updateTime: Number(item.updateTime) + timeZoneSwitching(),
                    createTime: Number(item.createTime) + timeZoneSwitching(),
                  };
                })
              : [];
          state.page = Number(page) || 1;
          state.size = Number(size) || 20;
          state.total = Number(total) || 0;
          break;
        }
        case ActiveType.question: {
          // if (!userInfo.hasPermission(智能事件中心_问题工单_可读)) return;
          const { success, message, data, page, size, total } = await getQuestionList({
            /*  */
            priority: state.search.priority,
            state: state.search.state,
            queryType: "CustOrder",
            compactTimeStart: state.search.compactTimeStart,
            compactTimeEnd: state.search.compactTimeEnd,
            createTimeStart: state.search.createTimeStart,
            createTimeEnd: state.search.createTimeEnd,
            updateTimeStart: state.search.updateTimeStart,
            updateTimeEnd: state.search.updateTimeEnd,
            includeTenantName: state.search.includeTenantName,
            excludeTenantName: state.search.excludeTenantName,
            eqTenantName: state.search.eqTenantName,
            neTenantName: state.search.neTenantName,
            includeOrderId: state.search.includeOrderId,
            excludeOrderId: state.search.excludeOrderId,
            eqOrderId: state.search.eqOrderId,
            neOrderId: state.search.neOrderId,
            includeOrderSummary: state.search.includeOrderSummary,
            excludeOrderSummary: state.search.excludeOrderSummary,
            eqOrderSummary: state.search.eqOrderSummary,
            neOrderSummary: state.search.neOrderSummary,
            includeState: state.search.includeState,
            excludeState: state.search.excludeState,
            eqState: state.search.eqState,
            neState: state.search.neState,
            includeActorName: state.search.includeActorName,
            excludeActorName: state.search.excludeActorName,
            eqActorName: state.search.eqActorName,
            neActorName: state.search.neActorName,
            includeResponsibleName: state.search.includeResponsibleName,
            excludeResponsibleName: state.search.excludeResponsibleName,
            eqResponsibleName: state.search.eqResponsibleName,
            neResponsibleName: state.search.neResponsibleName,
            tenantNameFilterRelation: state.search.tenantNameFilterRelation,
            orderIdFilterRelation: state.search.orderIdFilterRelation,
            orderSummaryFilterRelation: state.search.orderSummaryFilterRelation,
            stateFilterRelation: state.search.stateFilterRelation,
            actorNameFilterRelation: state.search.actorNameFilterRelation,
            responsibleNameFilterRelation: state.search.responsibleNameFilterRelation,
            eqAlarmCount: state.search.eqAlarmCount,
            neAlarmCount: state.search.neAlarmCount,
            geAlarmCount: state.search.geAlarmCount,
            gtAlarmCount: state.search.gtAlarmCount,
            leAlarmCount: state.search.leAlarmCount,
            ltAlarmCount: state.search.ltAlarmCount,
            isNullAlarmCount: state.search.isNullAlarmCount,
            isNotNullAlarmCount: state.search.isNotNullAlarmCount,
            alarmCountFilterRelation: state.search.alarmCountFilterRelation,
            includeCompactActorName: state.search.includeCompactActorName,
            excludeCompactActorName: state.search.excludeCompactActorName,
            eqCompactActorName: state.search.eqCompactActorName,
            neCompactActorName: state.search.neCompactActorName,
            compactActorNameFilterRelation: state.search.compactActorNameFilterRelation,

            inTicketGroupName: state.search.inTicketGroupName,
            excludeTicketGroupName: state.search.excludeTicketGroupName,
            eqTicketGroupName: state.search.eqTicketGroupName,
            neTicketGroupName: state.search.neTicketGroupName,
            ticketGroupNameFilterRelation: state.search.ticketGroupNameFilterRelation,
            inUserGroupName: state.search.inUserGroupName,
            excludeUserGroupName: state.search.excludeUserGroupName,
            eqUserGroupName: state.search.eqUserGroupName,
            neUserGroupName: state.search.neUserGroupName,
            userGroupNameFilterRelation: state.search.userGroupNameFilterRelation,
            includeFinishCode: state.search.includeFinishCode,
            excludeFinishCode: state.search.excludeFinishCode,
            eqFinishCode: state.search.eqFinishCode,
            neFinishCode: state.search.neFinishCode,
            finishCodeFilterRelation: state.search.finishCodeFilterRelation,
            compactEqTicketName: state.search.compactEqTicketName,
            compactNeTicketName: state.search.compactNeTicketName,
            compactIncludeTicketName: state.search.compactIncludeTicketName,
            compactExcludeTicketName: state.search.compactExcludeTicketName,
            compactTicketNameFilterRelation: state.search.compactTicketNameFilterRelation,
            ...(toValue(type) === TypeEnum.user ? { type: "myList", userId: userInfo.userId, tenantId: userInfo.currentTenantId } : { tenantId: userInfo.currentTenantId, type: "allList" }),
            sort,
            paging: { pageNumber: state.page, pageSize: state.size },
            boardOrNot: true,
          });
          if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
          state.list =
            data instanceof Array
              ? data.map((item) => {
                  return {
                    ...item,
                    updateTime: Number(item.updateTime) + timeZoneSwitching(),
                    createTime: Number(item.createTime) + timeZoneSwitching(),
                  };
                })
              : [];
          state.page = Number(page) || 1;
          state.size = Number(size) || 20;
          state.total = Number(total) || 0;
          break;
        }

        case ActiveType.publish: {
          // if (!userInfo.hasPermission(智能事件中心_问题工单_可读)) return;
          const { success, message, data, page, size, total } = await getPublishList({
            /*  */
            priority: state.search.priority,
            state: state.search.state,
            queryType: "CustOrder",
            compactTimeStart: state.search.compactTimeStart,
            compactTimeEnd: state.search.compactTimeEnd,
            createTimeStart: state.search.createTimeStart,
            createTimeEnd: state.search.createTimeEnd,
            updateTimeStart: state.search.updateTimeStart,
            updateTimeEnd: state.search.updateTimeEnd,
            includeTenantName: state.search.includeTenantName,
            excludeTenantName: state.search.excludeTenantName,
            eqTenantName: state.search.eqTenantName,
            neTenantName: state.search.neTenantName,
            includeOrderId: state.search.includeOrderId,
            excludeOrderId: state.search.excludeOrderId,
            eqOrderId: state.search.eqOrderId,
            neOrderId: state.search.neOrderId,
            includeOrderSummary: state.search.includeOrderSummary,
            excludeOrderSummary: state.search.excludeOrderSummary,
            eqOrderSummary: state.search.eqOrderSummary,
            neOrderSummary: state.search.neOrderSummary,
            includeState: state.search.includeState,
            excludeState: state.search.excludeState,
            eqState: state.search.eqState,
            neState: state.search.neState,
            includeActorName: state.search.includeActorName,
            excludeActorName: state.search.excludeActorName,
            eqActorName: state.search.eqActorName,
            neActorName: state.search.neActorName,
            includeResponsibleName: state.search.includeResponsibleName,
            excludeResponsibleName: state.search.excludeResponsibleName,
            eqResponsibleName: state.search.eqResponsibleName,
            neResponsibleName: state.search.neResponsibleName,
            tenantNameFilterRelation: state.search.tenantNameFilterRelation,
            orderIdFilterRelation: state.search.orderIdFilterRelation,
            orderSummaryFilterRelation: state.search.orderSummaryFilterRelation,
            stateFilterRelation: state.search.stateFilterRelation,
            actorNameFilterRelation: state.search.actorNameFilterRelation,
            responsibleNameFilterRelation: state.search.responsibleNameFilterRelation,
            eqAlarmCount: state.search.eqAlarmCount,
            neAlarmCount: state.search.neAlarmCount,
            geAlarmCount: state.search.geAlarmCount,
            gtAlarmCount: state.search.gtAlarmCount,
            leAlarmCount: state.search.leAlarmCount,
            ltAlarmCount: state.search.ltAlarmCount,
            isNullAlarmCount: state.search.isNullAlarmCount,
            isNotNullAlarmCount: state.search.isNotNullAlarmCount,
            alarmCountFilterRelation: state.search.alarmCountFilterRelation,
            includeCompactActorName: state.search.includeCompactActorName,
            excludeCompactActorName: state.search.excludeCompactActorName,
            eqCompactActorName: state.search.eqCompactActorName,
            neCompactActorName: state.search.neCompactActorName,
            compactActorNameFilterRelation: state.search.compactActorNameFilterRelation,

            inTicketGroupName: state.search.inTicketGroupName,
            excludeTicketGroupName: state.search.excludeTicketGroupName,
            eqTicketGroupName: state.search.eqTicketGroupName,
            neTicketGroupName: state.search.neTicketGroupName,
            ticketGroupNameFilterRelation: state.search.ticketGroupNameFilterRelation,
            inUserGroupName: state.search.inUserGroupName,
            excludeUserGroupName: state.search.excludeUserGroupName,
            eqUserGroupName: state.search.eqUserGroupName,
            neUserGroupName: state.search.neUserGroupName,
            userGroupNameFilterRelation: state.search.userGroupNameFilterRelation,
            includeFinishCode: state.search.includeFinishCode,
            excludeFinishCode: state.search.excludeFinishCode,
            eqFinishCode: state.search.eqFinishCode,
            neFinishCode: state.search.neFinishCode,
            finishCodeFilterRelation: state.search.finishCodeFilterRelation,
            compactEqTicketName: state.search.compactEqTicketName,
            compactNeTicketName: state.search.compactNeTicketName,
            compactIncludeTicketName: state.search.compactIncludeTicketName,
            compactExcludeTicketName: state.search.compactExcludeTicketName,
            compactTicketNameFilterRelation: state.search.compactTicketNameFilterRelation,
            ...(toValue(type) === TypeEnum.user ? { type: "myList", userId: userInfo.userId, tenantId: userInfo.currentTenantId } : { tenantId: userInfo.currentTenantId, type: "allList" }),
            sort,
            paging: { pageNumber: state.page, pageSize: state.size },
            boardOrNot: true,
          });
          if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
          state.list =
            data instanceof Array
              ? data.map((item) => {
                  return {
                    ...item,
                    updateTime: Number(item.updateTime) + timeZoneSwitching(),
                    createTime: Number(item.createTime) + timeZoneSwitching(),
                  };
                })
              : [];
          state.page = Number(page) || 1;
          state.size = Number(size) || 20;
          state.total = Number(total) || 0;
          break;
        }
        case ActiveType.qrcode: {
          /* ...code */
          // if (!userInfo.hasPermission(智能事件中心_二维码报障_可读)) return;
          const { success, message, data, page, size, total } = await getQrcodeByFilter(
            Object.assign(
              {
                pageNumber: state.page,
                pageSize: state.size,
                queryType: "CustOrder",
                startTime: moment().subtract(1, "months").startOf("day").valueOf(),
                endTime: moment().endOf("day").valueOf(),
                // status: qrcodeSearchStatus.value,
                // code: qrcodeSearchValue.value,
              },
              {
                type: toValue(type) === TypeEnum.user ? "mine" : "all",
                status: state.search.status,
                urgency: state.search.urgency,
                eqName: state.search.eqName,
                includeName: state.search.includeName,
                nameFilterRelation: state.search.nameFilterRelation,
                neName: state.search.neName,
                excludeName: state.search.excludeName,
                eqFailureTitle: state.search.eqFailureTitle,
                includeFailureTitle: state.search.includeFailureTitle,
                failureTitleFilterRelation: state.search.failureTitleFilterRelation,
                neFailureTitle: state.search.neFailureTitle,
                excludeFailureTitle: state.search.excludeFailureTitle,
                eqFailureDescription: state.search.eqFailureDescription,
                includeFailureDescription: state.search.includeFailureDescription,
                failureDescriptionFilterRelation: state.search.failureDescriptionFilterRelation,
                neFailureDescription: state.search.neFailureDescription,
                excludeFailureDescription: state.search.excludeFailureDescription,
                eqUnificationCode: state.search.eqUnificationCode,
                includeUnificationCode: state.search.includeUnificationCode,
                unificationCodeFilterRelation: state.search.unificationCodeFilterRelation,
                neUnificationCode: state.search.neUnificationCode,
                excludeUnificationCode: state.search.excludeUnificationCode,
                eqKebaoCode: state.search.eqKebaoCode,
                includeKebaoCode: state.search.includeKebaoCode,
                kebaoCodeFilterRelation: state.search.kebaoCodeFilterRelation,
                neKebaoCode: state.search.neKebaoCode,
                excludeKebaoCode: state.search.excludeKebaoCode,
                chargePerson: state.search.chargePerson,
                processors: state.search.processors,

                eqAbbreviation: state.search.eqAbbreviation,
                includeAbbreviation: state.search.includeAbbreviation,
                neAbbreviation: state.search.neAbbreviation,
                excludeAbbreviation: state.search.excludeAbbreviation,
                abbreviationFilterRelation: state.search.abbreviationFilterRelation,

                eqChargePerson: state.search.eqChargePerson,
                includeChargePerson: state.search.includeChargePerson,
                chargePersonFilterRelation: state.search.chargePersonFilterRelation,
                neChargePerson: state.search.neChargePerson,
                excludeChargePerson: state.search.excludeChargePerson,

                eqProcessors: state.search.eqProcessors,
                includeProcessors: state.search.includeProcessors,
                processorsFilterRelation: state.search.processorsFilterRelation,
                neProcessors: state.search.neProcessors,
                excludeProcessors: state.search.excludeProcessors,
              }
            ) as any
          );
          if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
          state.list =
            data instanceof Array
              ? data.map((item) => {
                  return {
                    ...item,
                    // updateTime: Number(item.updateTime) + timeZoneSwitching(),
                    createTime: Number(item.createTime) + timeZoneSwitching(),
                  };
                })
              : [];
          state.page = Number(page) || 1;
          state.size = Number(size) || 20;
          state.total = Number(total) || 0;
          break;
        }
        case ActiveType.dictEvent: {
          // if (!userInfo.hasPermission(智能事件中心_DICT事件管理_可读)) return;
          const { success, message, data, page, size, total } = await getDictEventList({
            priority: state.search.priority,
            state: state.search.state,
            queryType: "CustOrder",
            compactTimeStart: state.search.compactTimeStart,
            compactTimeEnd: state.search.compactTimeEnd,
            createTimeStart: state.search.createTimeStart,
            createTimeEnd: state.search.createTimeEnd,
            updateTimeStart: state.search.updateTimeStart,
            updateTimeEnd: state.search.updateTimeEnd,
            includeTenantName: state.search.includeTenantName,
            excludeTenantName: state.search.excludeTenantName,
            eqTenantName: state.search.eqTenantName,
            neTenantName: state.search.neTenantName,
            includeOrderId: state.search.includeOrderId,
            excludeOrderId: state.search.excludeOrderId,
            eqOrderId: state.search.eqOrderId,
            neOrderId: state.search.neOrderId,
            includeState: state.search.includeState,
            excludeState: state.search.excludeState,
            eqState: state.search.eqState,
            neState: state.search.neState,
            includeActorName: state.search.includeActorName,
            excludeActorName: state.search.excludeActorName,
            eqActorName: state.search.eqActorName,
            neActorName: state.search.neActorName,
            includeResponsibleName: state.search.includeResponsibleName,
            excludeResponsibleName: state.search.excludeResponsibleName,
            eqResponsibleName: state.search.eqResponsibleName,
            neResponsibleName: state.search.neResponsibleName,
            includeOrderSummary: state.search.includeOrderSummary,
            excludeOrderSummary: state.search.excludeOrderSummary,
            eqOrderSummary: state.search.eqOrderSummary,
            neOrderSummary: state.search.neOrderSummary,
            tenantNameFilterRelation: state.search.tenantNameFilterRelation,
            orderIdFilterRelation: state.search.orderIdFilterRelation,
            stateFilterRelation: state.search.stateFilterRelation,
            actorNameFilterRelation: state.search.actorNameFilterRelation,
            responsibleNameFilterRelation: state.search.responsibleNameFilterRelation,
            orderSummaryFilterRelation: state.search.orderSummaryFilterRelation,
            eqAlarmCount: state.search.eqAlarmCount,
            neAlarmCount: state.search.neAlarmCount,
            geAlarmCount: state.search.geAlarmCount,
            gtAlarmCount: state.search.gtAlarmCount,
            leAlarmCount: state.search.leAlarmCount,
            ltAlarmCount: state.search.ltAlarmCount,
            isNullAlarmCount: state.search.isNullAlarmCount,
            isNotNullAlarmCount: state.search.isNotNullAlarmCount,
            alarmCountFilterRelation: state.search.alarmCountFilterRelation,
            includeCompactActorName: state.search.includeCompactActorName,
            excludeCompactActorName: state.search.excludeCompactActorName,
            eqCompactActorName: state.search.eqCompactActorName,
            neCompactActorName: state.search.neCompactActorName,
            compactActorNameFilterRelation: state.search.compactActorNameFilterRelation,
            includeKbServiceCodes: state.search.includeKbServiceCodes,
            excludeKbServiceCodes: state.search.excludeKbServiceCodes,
            eqKbServiceCodes: state.search.eqKbServiceCodes,
            neKbServiceCodes: state.search.neKbServiceCodes,
            kbServiceCodesFilterRelation: state.search.kbServiceCodesFilterRelation,
            inTicketGroupName: state.search.inTicketGroupName,
            excludeTicketGroupName: state.search.excludeTicketGroupName,
            eqTicketGroupName: state.search.eqTicketGroupName,
            neTicketGroupName: state.search.neTicketGroupName,
            ticketGroupNameFilterRelation: state.search.ticketGroupNameFilterRelation,
            inUserGroupName: state.search.inUserGroupName,
            excludeUserGroupName: state.search.excludeUserGroupName,
            eqUserGroupName: state.search.eqUserGroupName,
            neUserGroupName: state.search.neUserGroupName,
            userGroupNameFilterRelation: state.search.userGroupNameFilterRelation,
            includeFinishCode: state.search.includeFinishCode,
            excludeFinishCode: state.search.excludeFinishCode,
            eqFinishCode: state.search.eqFinishCode,
            neFinishCode: state.search.neFinishCode,
            finishCodeFilterRelation: state.search.finishCodeFilterRelation,
            compactEqTicketName: state.search.compactEqTicketName,
            compactNeTicketName: state.search.compactNeTicketName,
            compactIncludeTicketName: state.search.compactIncludeTicketName,
            compactExcludeTicketName: state.search.compactExcludeTicketName,
            compactTicketNameFilterRelation: state.search.compactTicketNameFilterRelation,
            type: toValue(type) === TypeEnum.user ? "myList" : "list",
            tenantId: userInfo.currentTenantId,
            sort,
            paging: { pageNumber: state.page, pageSize: state.size },
            boardOrNot: "true",
          });
          if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
          state.list =
            data instanceof Array
              ? data.map((item) => {
                  return {
                    ...item,
                    updateTime: Number(item.updateTime) + timeZoneSwitching(),
                    createTime: Number(item.createTime) + timeZoneSwitching(),
                  };
                })
              : [];
          state.page = Number(page) || 1;
          state.size = Number(size) || 20;
          state.total = Number(total) || 0;
          break;
        }
        case ActiveType.dictService: {
          // if (!userInfo.hasPermission(智能事件中心_DICT服务请求_可读)) return;
          const { success, message, data, page, size, total } = await getDictServiceList({
            priority: state.search.priority,
            state: state.search.state,
            queryType: "CustOrder",
            compactTimeStart: state.search.compactTimeStart,
            compactTimeEnd: state.search.compactTimeEnd,
            createTimeStart: state.search.createTimeStart,
            createTimeEnd: state.search.createTimeEnd,
            updateTimeStart: state.search.updateTimeStart,
            updateTimeEnd: state.search.updateTimeEnd,
            includeTenantName: state.search.includeTenantName,
            excludeTenantName: state.search.excludeTenantName,
            eqTenantName: state.search.eqTenantName,
            neTenantName: state.search.neTenantName,
            includeState: state.search.includeState,
            excludeState: state.search.excludeState,
            eqState: state.search.eqState,
            neState: state.search.neState,
            includeOrderId: state.search.includeOrderId,
            excludeOrderId: state.search.excludeOrderId,
            eqOrderId: state.search.eqOrderId,
            neOrderId: state.search.neOrderId,
            includeActorName: state.search.includeActorName,
            excludeActorName: state.search.excludeActorName,
            eqActorName: state.search.eqActorName,
            neActorName: state.search.neActorName,
            includeResponsibleName: state.search.includeResponsibleName,
            excludeResponsibleName: state.search.excludeResponsibleName,
            eqResponsibleName: state.search.eqResponsibleName,
            neResponsibleName: state.search.neResponsibleName,
            includeOrderSummary: state.search.includeOrderSummary,
            excludeOrderSummary: state.search.excludeOrderSummary,
            eqOrderSummary: state.search.eqOrderSummary,
            neOrderSummary: state.search.neOrderSummary,
            tenantNameFilterRelation: state.search.tenantNameFilterRelation,
            stateFilterRelation: state.search.stateFilterRelation,
            orderIdFilterRelation: state.search.orderIdFilterRelation,
            actorNameFilterRelation: state.search.actorNameFilterRelation,
            responsibleNameFilterRelation: state.search.responsibleNameFilterRelation,
            orderSummaryFilterRelation: state.search.orderSummaryFilterRelation,
            eqAlarmCount: state.search.eqAlarmCount,
            neAlarmCount: state.search.neAlarmCount,
            geAlarmCount: state.search.geAlarmCount,
            gtAlarmCount: state.search.gtAlarmCount,
            leAlarmCount: state.search.leAlarmCount,
            ltAlarmCount: state.search.ltAlarmCount,
            isNullAlarmCount: state.search.isNullAlarmCount,
            isNotNullAlarmCount: state.search.isNotNullAlarmCount,
            alarmCountFilterRelation: state.search.alarmCountFilterRelation,
            includeCompactActorName: state.search.includeCompactActorName,
            excludeCompactActorName: state.search.excludeCompactActorName,
            eqCompactActorName: state.search.eqCompactActorName,
            neCompactActorName: state.search.neCompactActorName,
            compactActorNameFilterRelation: state.search.compactActorNameFilterRelation,
            includeServiceCodes: state.search.includeServiceCodes,
            excludeServiceCodes: state.search.excludeServiceCodes,
            eqServiceCodes: state.search.eqServiceCodes,
            neServiceCodes: state.search.neServiceCodes,
            serviceCodesFilterRelation: state.search.serviceCodesFilterRelation,
            inTicketGroupName: state.search.inTicketGroupName,
            excludeTicketGroupName: state.search.excludeTicketGroupName,
            eqTicketGroupName: state.search.eqTicketGroupName,
            neTicketGroupName: state.search.neTicketGroupName,
            ticketGroupNameFilterRelation: state.search.ticketGroupNameFilterRelation,
            inUserGroupName: state.search.inUserGroupName,
            excludeUserGroupName: state.search.excludeUserGroupName,
            eqUserGroupName: state.search.eqUserGroupName,
            neUserGroupName: state.search.neUserGroupName,
            userGroupNameFilterRelation: state.search.userGroupNameFilterRelation,
            includeFinishCode: state.search.includeFinishCode,
            excludeFinishCode: state.search.excludeFinishCode,
            eqFinishCode: state.search.eqFinishCode,
            neFinishCode: state.search.neFinishCode,
            finishCodeFilterRelation: state.search.finishCodeFilterRelation,
            compactEqTicketName: state.search.compactEqTicketName,
            compactNeTicketName: state.search.compactNeTicketName,
            compactIncludeTicketName: state.search.compactIncludeTicketName,
            compactExcludeTicketName: state.search.compactExcludeTicketName,
            compactTicketNameFilterRelation: state.search.compactTicketNameFilterRelation,
            type: toValue(type) === TypeEnum.user ? "myList" : "list",
            tenantId: userInfo.currentTenantId,
            sort,
            paging: { pageNumber: state.page, pageSize: state.size },
            boardOrNot: "true",
          });
          if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
          state.list =
            data instanceof Array
              ? data.map((item) => {
                  return {
                    ...item,
                    updateTime: Number(item.updateTime) + timeZoneSwitching(),
                    createTime: Number(item.createTime) + timeZoneSwitching(),
                  };
                })
              : [];
          state.page = Number(page) || 1;
          state.size = Number(size) || 20;
          state.total = Number(total) || 0;
          break;
        }
      }
      state.loading = false;
    })(),
  ]);
}

async function handleBatchEnd(data) {
  // const $endRef = toValue(dictEndRef) : toValue(endRef);
  const $endRef = toValue(
    {
      [ActiveType.event]: endRef,
      [ActiveType.dictEvent]: dictEndRef,
      [ActiveType.service]: serviceEndRef,
      [ActiveType.dictService]: serviceDcitEndRef,
    }[active.value]
  );
  if (!$endRef) return;
  $endRef.open(void 0, void 0);
}
async function closeEvent(data) {
  completeInfo.value = { ...data.params.completeInfo };
  innerVisible.value = true;
}
async function startPromise() {
  closeMessage.value = [];
  source = axios.CancelToken.source();

  for (let i = 0; i < select.value.length; i++) {
    closeMessage.value[i] = "loading...";
    try {
      const { success, message, data } = await {
        [ActiveType.event]: batchCloseEvent,
        [ActiveType.dictEvent]: batchCloseDictEvent,
        [ActiveType.service]: batchCloseService,
        [ActiveType.dictService]: batchCloseDictService,
      }[active.value]({ orderIds: [select.value[i].id], completeInfo: completeInfo.value });
      if (!success) await Promise.reject(Object.assign(new Error(message), { success, data }));
      closeMessage.value[i] = "已关闭";
      handleCommand(command.Request);
    } catch (error) {
      closeMessage.value[i] = error instanceof Error ? error.message : `${error}`;
    }
  }
  showCancel.value = false;
}

async function beforeCloseInnerDialog(done) {
  showCancel.value = true;
  showStartBtn.value = true;
  closeMessage.value = [];
  await nextTick();
  handleCommand(command.Request);
  if (done instanceof Function) done();
  else innerVisible.value = false;
}

async function handelBatchAddNotes(select: DataItem[]) {
  // const $eventCreateNoteRef = [ActiveType.dictEvent, ActiveType.dictEvent].includes(active.value as any) ? toValue(eventCreateDictNoteRef) : toValue(eventCreateNoteRef);
  const $eventCreateNoteRef = toValue(eventCreateNoteRef);

  if (!$eventCreateNoteRef) return;
  // $eventCreateNoteRef.orderIds = select.map((v) => v.id);

  nodeBatchId.value = select.map((v) => v.id as string);
  await nextTick(() => $eventCreateNoteRef.open(void 0));
}
async function handelBatchSetPriority(select: DataItem[], priority: priority) {
  const $editorRef = toValue(editorRef);
  if (!$editorRef) return;
  await $editorRef
    .confirm({ select: select, $type: "warning", $title: t("eventBoard.All Priority", { priority }), $slot: "batchConfirm" }, async (form: Record<string, unknown>) => {
      // const { success, message, data } = await ([ActiveType.dictEvent, ActiveType.dictService].includes(active.value as any) ? setDictEventDataByPriority : setEventDataByPriority)({ id: (<DataItem[]>form.select).map((v) => v.id), priority });
      const { success, message, data } = await {
        [ActiveType.event]: setEventDataByPriority,
        [ActiveType.dictEvent]: setDictEventDataByPriority,
        [ActiveType.service]: setServiceDataByPriority,
        [ActiveType.dictService]: setDictServiceDataByPriority,
      }[active.value]({ id: (<DataItem[]>form.select).map((v) => v.id), priority });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`成功${form.$title}`);
      await queryData();
    })
    .catch((form) => Promise.resolve(form));
}

const qrcodeDateilRef = ref();

const abandonFormRef = ref();
const abandonForm = ref<{ reason: string; status: keyof typeof Status }>({
  status: Status.DEPRECATED,
  reason: "",
});

function handleQrcodeAbandon({ id }) {
  return new Promise((resolve) => {
    abandonForm.value.reason = "";

    ElMessageBox({
      title: t("qrcode.abandon"),
      message: h(
        defineComponent({
          setup() {
            return () => [h("div", { class: "tw-flex tw-items-center tw-mb-2" }, [h("div", { class: "tw-px-4" }, [h(ElIcon, { color: "#f0ad4e", size: "36" }, [h(WarningFilled)])]), h("div", "报障单废弃后，不可继续报障。")]), h(ElForm, { model: abandonForm, ref: (v) => (abandonFormRef.value = v), rules: { reason: [{ required: true, message: "请输入废弃原因", trigger: ["change", "blur"] }] }, labelPosition: "top" }, [h(ElFormItem, { label: "废弃原因", prop: "reason" }, [h(ElInput, { "modelValue": abandonForm.value.reason, "onUpdate:modelValue": (v) => (abandonForm.value.reason = v) })])])];
          },
        })
      ),
      beforeClose: async (action, instance, done) => {
        if (action === "confirm") {
          abandonFormRef.value.validate(async (valid) => {
            if (!valid) return;
            try {
              const { message, success } = await setQrcode({ ...abandonForm.value, id });
              if (!success) throw new Error(message);
              ElMessage.success(t("axios.Operation successful"));
              handleRefresh();
              done();
              resolve(true);
            } catch (error) {
              error instanceof Error && ElMessage.error(error.message);
            }
          });
        } else {
          done();
          resolve(false);
        }
      },
    })
      .then(() => {})
      .catch(() => {});
  });
}

const updateFormRef = ref<FormInstance>();
const updateForm = ref<{ kebaoCode: string; status: keyof typeof Status }>({
  status: Status.REPORTED,
  kebaoCode: "",
});

function handleQrcodeUpdate({ status, id }) {
  return new Promise((resolve) => {
    updateForm.value.kebaoCode = "";

    const isReported = status === Status.REPORTED; // 客保工单号必填
    ElMessageBox({
      title: !isReported ? t("qrcode.Fault reported") : t("qrcode.Customer Ticket Number"),
      message: h(
        defineComponent({
          setup() {
            return () => h(ElForm, { model: updateForm, ref: (v) => (updateFormRef.value = v as FormInstance), rules: { kebaoCode: [{ required: isReported, message: "请输入客保工单号", trigger: ["change", "blur"] }] } }, [h(ElFormItem, { label: "客保工单号", prop: "kebaoCode" }, [h(ElInput, { "modelValue": updateForm.value.kebaoCode, "onUpdate:modelValue": (v) => (updateForm.value.kebaoCode = v) })])]);
          },
        })
      ),
      beforeClose: async (action, instance, done) => {
        if (action === "confirm") {
          updateFormRef.value &&
            updateFormRef.value.validate(async (valid) => {
              if (!valid) return;
              try {
                const { message, success } = await setQrcode({ ...updateForm.value, id });
                if (!success) throw new Error(message);
                ElMessage.success(t("axios.Operation successful"));
                handleRefresh();
                done();
                resolve(true);

                updateFormRef.value && updateFormRef.value.resetFields();
              } catch (error) {
                error instanceof Error && ElMessage.error(error.message);
              }
            });
        } else {
          done();
          resolve(false);
        }
      },
    })
      .then(() => {})
      .catch(() => {});
  });
}

const repairFormRef = ref<FormInstance>();
const repairForm = ref<{ dispose: string; status: keyof typeof Status }>({
  status: Status.FIXED,
  dispose: "",
});
function handleQrcodeRepair({ id }) {
  return new Promise((resolve) => {
    ElMessageBox({
      title: t("qrcode.fixed"),
      message: h(
        defineComponent({
          setup() {
            return () => [h("div", { class: "tw-flex tw-items-center tw-mb-2" }, [h("div", { class: "tw-px-4" }, [h(ElIcon, { color: "#f0ad4e", size: "36" }, [h(CircleCheck)])]), h("div", "需填写故障处理情况，同步给用户。")]), h(ElForm, { model: repairForm, ref: (v) => (repairFormRef.value = v as FormInstance), rules: { dispose: [{ required: true, message: "请输入修复情况", trigger: ["change", "blur"] }] }, labelPosition: "top" }, [h(ElFormItem, { label: "修复情况", prop: "dispose" }, [h(ElInput, { "modelValue": repairForm.value.dispose, "onUpdate:modelValue": (v) => (repairForm.value.dispose = v) })])])];
          },
        })
      ),
      beforeClose: async (action, instance, done) => {
        if (action === "confirm") {
          repairFormRef.value &&
            repairFormRef.value.validate(async (valid) => {
              if (!valid) return;
              try {
                const { message, success } = await setQrcode({ ...repairForm.value, id });
                if (!success) throw new Error(message);
                ElMessage.success(t("axios.Operation successful"));
                repairFormRef.value && repairFormRef.value.resetFields();
                handleRefresh();
                done();
                resolve(true);
              } catch (error) {
                error instanceof Error && ElMessage.error(error.message);
              }
            });
        } else {
          done();
          resolve(false);
        }
      },
    })
      .then(() => {})
      .catch(() => {});
  });
}
const eventTotal = ref(0);
const serviceTotal = ref(0);
const questionTotal = ref(0);
const changeTotal = ref(0);
const publishTotal = ref(0);
const qrcodeTotal = ref(0);
async function getAllEvent() {
  const sort: string[] = [];
  const getPropKey = (prop: string) => {
    switch (toValue(active)) {
      case ActiveType.event:
        return prop === "createTime" ? "createdTime" : prop === "eventState" ? "state" : prop === "updateTime" ? "updatedTime" : prop;
      case ActiveType.service:
        return prop;
      case ActiveType.change:
        return prop;
      case ActiveType.question:
        return prop === "createTime" ? "createdTime" : prop === "updateTime" ? "updatedTime" : prop;
      default:
        return prop;
    }
  };
  switch ((state.sort || {}).order) {
    case "ascending": {
      sort.push(`${getPropKey(state.sort!.prop)},asc`);
      if (!toValue(tableState)) sort.includes("createdTime,asc") ? sort.push("updatedTime,asc") : sort.includes("createTime,asc") ? sort.push("updateTime,asc") : void 0;
      break;
    }
    case "descending": {
      sort.push(`${getPropKey(state.sort!.prop)},desc`);
      if (!toValue(tableState)) sort.includes("createdTime,desc") || sort.includes("createTime,desc") ? sort.push("updatedTime,desc") : sort.includes("createTime,desc") ? sort.push("updateTime,desc") : void 0;
      break;
    }
  }
  await router.isReady();
  // 事件
  const { success, message, data, page, size, total } = await getEventList({
    priority: state.search.priority,
    queryType: "CustOrder",
    state: state.search.state,
    type: toValue(type) === TypeEnum.user ? "myList" : "list",
    tenantId: userInfo.currentTenantId,
    sort,
    paging: { pageNumber: state.page, pageSize: state.size },
    boardOrNot: "true",
  });
  if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
  eventTotal.value = Number(total) || 0;
}
// 服务
async function getAllService() {
  const sort: string[] = [];
  const getPropKey = (prop: string) => {
    switch (toValue(active)) {
      case ActiveType.event:
        return prop === "createTime" ? "createdTime" : prop === "eventState" ? "state" : prop === "updateTime" ? "updatedTime" : prop;
      case ActiveType.service:
        return prop;
      case ActiveType.change:
        return prop;
      case ActiveType.question:
        return prop === "createTime" ? "createdTime" : prop === "updateTime" ? "updatedTime" : prop;
      default:
        return prop;
    }
  };
  switch ((state.sort || {}).order) {
    case "ascending": {
      sort.push(`${getPropKey(state.sort!.prop)},asc`);
      if (!toValue(tableState)) sort.includes("createdTime,asc") ? sort.push("updatedTime,asc") : sort.includes("createTime,asc") ? sort.push("updateTime,asc") : void 0;
      break;
    }
    case "descending": {
      sort.push(`${getPropKey(state.sort!.prop)},desc`);
      if (!toValue(tableState)) sort.includes("createdTime,desc") || sort.includes("createTime,desc") ? sort.push("updatedTime,desc") : sort.includes("createTime,desc") ? sort.push("updateTime,desc") : void 0;
      break;
    }
  }
  await router.isReady();
  const { success, message, data, page, size, total } = await getServiceList({
    priority: state.search.priority,
    state: state.search.state,
    queryType: "CustOrder",

    type: toValue(type) === TypeEnum.user ? "myList" : "list",
    tenantId: userInfo.currentTenantId,
    sort,
    paging: { pageNumber: state.page, pageSize: state.size },
    boardOrNot: "true",
  });
  if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
  serviceTotal.value = Number(total) || 0;
}
// 问题
async function getAllQuestion() {
  const sort: string[] = [];
  const getPropKey = (prop: string) => {
    switch (toValue(active)) {
      case ActiveType.event:
        return prop === "createTime" ? "createdTime" : prop === "eventState" ? "state" : prop === "updateTime" ? "updatedTime" : prop;
      case ActiveType.service:
        return prop;
      case ActiveType.change:
        return prop;
      case ActiveType.question:
        return prop === "createTime" ? "createdTime" : prop === "updateTime" ? "updatedTime" : prop;
      default:
        return prop;
    }
  };
  switch ((state.sort || {}).order) {
    case "ascending": {
      sort.push(`${getPropKey(state.sort!.prop)},asc`);
      if (!toValue(tableState)) sort.includes("createdTime,asc") ? sort.push("updatedTime,asc") : sort.includes("createTime,asc") ? sort.push("updateTime,asc") : void 0;
      break;
    }
    case "descending": {
      sort.push(`${getPropKey(state.sort!.prop)},desc`);
      if (!toValue(tableState)) sort.includes("createdTime,desc") || sort.includes("createTime,desc") ? sort.push("updatedTime,desc") : sort.includes("createTime,desc") ? sort.push("updateTime,desc") : void 0;
      break;
    }
  }
  await router.isReady();
  const { success, message, data, page, size, total } = await getQuestionList({
    ...(toValue(type) === TypeEnum.user ? { type: "myList", userId: userInfo.userId, tenantId: userInfo.currentTenantId } : { tenantId: userInfo.currentTenantId, type: "allList" }),
    sort,
    paging: { pageNumber: state.page, pageSize: state.size },
    boardOrNot: true,
  });
  if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
  questionTotal.value = Number(total) || 0;
}
// 变更
async function getAllChange() {
  const sort: string[] = [];
  const getPropKey = (prop: string) => {
    switch (toValue(active)) {
      case ActiveType.event:
        return prop === "createTime" ? "createdTime" : prop === "eventState" ? "state" : prop === "updateTime" ? "updatedTime" : prop;
      case ActiveType.service:
        return prop;
      case ActiveType.change:
        return prop;
      case ActiveType.question:
        return prop === "createTime" ? "createdTime" : prop === "updateTime" ? "updatedTime" : prop;
      default:
        return prop;
    }
  };
  switch ((state.sort || {}).order) {
    case "ascending": {
      sort.push(`${getPropKey(state.sort!.prop)},asc`);
      if (!toValue(tableState)) sort.includes("createdTime,asc") ? sort.push("updatedTime,asc") : sort.includes("createTime,asc") ? sort.push("updateTime,asc") : void 0;
      break;
    }
    case "descending": {
      sort.push(`${getPropKey(state.sort!.prop)},desc`);
      if (!toValue(tableState)) sort.includes("createdTime,desc") || sort.includes("createTime,desc") ? sort.push("updatedTime,desc") : sort.includes("createTime,desc") ? sort.push("updateTime,desc") : void 0;
      break;
    }
  }
  await router.isReady();
  // if (!userInfo.hasPermission(智能事件中心_变更工单_可读)) return;
  const { success, message, data, page, size, total } = await getChangeList({
    /*  */
    priority: state.search.priority,
    state: state.search.state,
    queryType: "CustOrder",
    type: toValue(type) === TypeEnum.user ? "owner" : "",
    sort,
    paging: { pageNumber: state.page, pageSize: state.size },
  });
  if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
  changeTotal.value = Number(total) || 0;
}
// 发布
async function getAllPublish() {
  const sort: string[] = [];
  const getPropKey = (prop: string) => {
    switch (toValue(active)) {
      case ActiveType.event:
        return prop === "createTime" ? "createdTime" : prop === "eventState" ? "state" : prop === "updateTime" ? "updatedTime" : prop;
      case ActiveType.service:
        return prop;
      case ActiveType.change:
        return prop;
      case ActiveType.question:
        return prop === "createTime" ? "createdTime" : prop === "updateTime" ? "updatedTime" : prop;
      default:
        return prop;
    }
  };
  switch ((state.sort || {}).order) {
    case "ascending": {
      sort.push(`${getPropKey(state.sort!.prop)},asc`);
      if (!toValue(tableState)) sort.includes("createdTime,asc") ? sort.push("updatedTime,asc") : sort.includes("createTime,asc") ? sort.push("updateTime,asc") : void 0;
      break;
    }
    case "descending": {
      sort.push(`${getPropKey(state.sort!.prop)},desc`);
      if (!toValue(tableState)) sort.includes("createdTime,desc") || sort.includes("createTime,desc") ? sort.push("updatedTime,desc") : sort.includes("createTime,desc") ? sort.push("updateTime,desc") : void 0;
      break;
    }
  }
  await router.isReady();
  const { success, message, data, page, size, total } = await getPublishList({
    /*  */
    priority: state.search.priority,
    state: state.search.state,
    queryType: "CustOrder",
    ...(toValue(type) === TypeEnum.user ? { type: "myList", userId: userInfo.userId, tenantId: userInfo.currentTenantId } : { tenantId: userInfo.currentTenantId, type: "allList" }),
    sort,
    paging: { pageNumber: state.page, pageSize: state.size },
    boardOrNot: true,
  });
  if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
  publishTotal.value = Number(total) || 0;
}
// 二维码保障
async function getAllQrcode() {
  const sort: string[] = [];
  const getPropKey = (prop: string) => {
    switch (toValue(active)) {
      case ActiveType.event:
        return prop === "createTime" ? "createdTime" : prop === "eventState" ? "state" : prop === "updateTime" ? "updatedTime" : prop;
      case ActiveType.service:
        return prop;
      case ActiveType.change:
        return prop;
      case ActiveType.question:
        return prop === "createTime" ? "createdTime" : prop === "updateTime" ? "updatedTime" : prop;
      default:
        return prop;
    }
  };
  switch ((state.sort || {}).order) {
    case "ascending": {
      sort.push(`${getPropKey(state.sort!.prop)},asc`);
      if (!toValue(tableState)) sort.includes("createdTime,asc") ? sort.push("updatedTime,asc") : sort.includes("createTime,asc") ? sort.push("updateTime,asc") : void 0;
      break;
    }
    case "descending": {
      sort.push(`${getPropKey(state.sort!.prop)},desc`);
      if (!toValue(tableState)) sort.includes("createdTime,desc") || sort.includes("createTime,desc") ? sort.push("updatedTime,desc") : sort.includes("createTime,desc") ? sort.push("updateTime,desc") : void 0;
      break;
    }
  }
  await router.isReady();

  const { success, message, data, page, size, total } = await getQrcodeByFilter(
    Object.assign(
      {
        pageNumber: state.page,
        pageSize: state.size,
        queryType: "CustOrder",
        startTime: moment().subtract(1, "months").startOf("day").valueOf(),
        endTime: moment().endOf("day").valueOf(),
        // status: qrcodeSearchStatus.value,
        // code: qrcodeSearchValue.value,
      },
      {
        type: toValue(type) === TypeEnum.user ? "mine" : "all",
        status: state.search.status,
        urgency: state.search.urgency,
      }
    ) as any
  );
  if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
  qrcodeTotal.value = Number(total) || 0;
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: DataItem): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.close-event {
  width: 100%;
  height: 400px;
  border: 1px solid #ccc;
  padding: 10px;
  box-sizing: border-box;
  display: flex;
  :deep(.close-event-scroll) {
    display: flex;
    width: 100%;
    .el-scrollbar__wrap {
      width: 100%;
    }
    .el-scrollbar__view {
      display: flex;
      justify-content: space-between;
      > .close-event-list {
        flex: 1;
        h3 {
          font-weight: 700;
          font-size: 18px;
        }
        ul > li {
          height: 35px;
          line-height: 35px;
        }
      }
      > .close-event-list:last-child {
        text-align: right;
      }
    }
  }
}
.tab {
  display: flex;
  > div {
    flex: none;
    border-top: 1px solid #cccc;
    border-left: 1px solid #cccc;
    border-bottom: 1px solid #cccc;
    padding: 10px;
    box-sizing: border;
  }
  > div:last-child {
    border-right: 1px solid #cccc;
  }
  > div {
    transition: all 0.3s ease;
  }
  > div:hover {
    // background: #eee;
    cursor: pointer;
    color: #3e97ff;
  }

  .on {
    border-bottom: none;
    background: #3e97ff;
    color: #fff;
  }
  .on:hover {
    border-bottom: none;
    background: #3e97ff;
    color: #fff;
  }
}

.order-state {
  border-radius: 2px;
  opacity: 1;
  // background: rgba(30, 205, 132, 0.1);
  display: inline-block;
  padding: 2px 6px;
  font-family: 思源黑体;
  font-size: 12px;
  font-weight: normal;
  line-height: 14px;
  letter-spacing: 0em;
  // color: #1ECD84;
  margin-right: 6px;
}

.state-VERY_URGENT {
  background: rgba(213, 73, 65, 0.1);
  color: #d54941;
}

.state-URGENT {
  background: rgba(243, 173, 60, 0.1);
  color: #f3ad3c;
}

.state-NORMAL {
  background: rgba(30, 205, 132, 0.1);
  color: #1ecd84;
}

.state-NOT_URGENT {
  background: rgba(139, 139, 139, 0.1);
  color: #8b8b8b;
}
.tw-text-left {
  font-size: 14px;
}
:deep(.el-badge__content.is-fixed) {
  right: -5px !important;
}
</style>
