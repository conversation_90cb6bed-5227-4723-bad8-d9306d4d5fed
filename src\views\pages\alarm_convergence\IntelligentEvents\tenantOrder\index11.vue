<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <!-- <el-tabs v-model="state.search.type" class="event-board-tabs" @tab-click="nextTick(() => handleCommand(command.Request))" :style="{ '--el-tabs-header-height': '88px', 'height': '105px' }">
      <el-tab-pane name="event" :disabled="state.loading">
        <template #label>
          <div class="tw-w-[320px] tw-text-[16px] tw-leading-[18px]">
            <div class="tw-mb-[6px] tw-flex tw-items-center tw-text-left">
              <el-icon class="tw-mr-2"><Warning></Warning></el-icon> 事件
            </div>
            <div>
              <el-radio-group v-model="state.search.resource" :disabled="state.loading" class="tw-w-full tw-flex-col" @change="nextTick(() => handleCommand(command.Request))">
                <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
                  <el-radio v-show="state.search.type === 'event'" label="use" class="tw-w-[71px]">我的</el-radio>

                  <div class="tw-w-[103px] tw-text-[var(--el-text-color-placeholder)]" v-for="item in myStatisticsEvnetCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>

                </div>
                <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
                  <el-radio v-show="state.search.type === 'event'" label="all" class="tw-w-[71px]">全部</el-radio>
                  <div class="tw-w-[103px] tw-text-[var(--el-text-color-placeholder)]" v-for="item in allStatisticsEvnetCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
                </div>
              </el-radio-group>
            </div>
          </div>
        </template>
      </el-tab-pane>
      <el-tab-pane name="request" :disabled="state.loading">
        <template #label>
          <div class="tw-w-[320px] tw-text-[16px] tw-leading-[18px]">
            <div class="tw-mb-[6px] tw-flex tw-items-center tw-text-left">
              <el-icon class="tw-mr-2"><CirclePlus></CirclePlus></el-icon> 请求
            </div>
            <div>
              <el-radio-group v-model="state.search.resource" :disabled="state.loading" class="tw-w-full tw-flex-col" @change="nextTick(() => handleCommand(command.Request))">
                <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
                  <el-radio v-show="state.search.type === 'request'" label="use" class="tw-w-[71px]">我的</el-radio>
                  <div class="tw-w-[103px] tw-text-[var(--el-text-color-placeholder)]" v-for="item in myStatisticsRequestCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
                </div>
                <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
                  <el-radio v-show="state.search.type === 'request'" label="all" class="tw-w-[71px]">全部</el-radio>
                  <div class="tw-w-[103px] tw-text-[var(--el-text-color-placeholder)]" v-for="item in allStatisticsRequestCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
                </div>
              </el-radio-group>
            </div>
          </div>
        </template>
      </el-tab-pane>
      <el-tab-pane name="problem" :disabled="state.loading">
        <template #label>
          <div class="tw-w-[320px] tw-text-[16px] tw-leading-[18px]">
            <div class="tw-mb-[6px] tw-flex tw-items-center tw-text-left">
              <el-icon class="tw-mr-2"><Warning></Warning></el-icon> 问题
            </div>
            <div>
              <el-radio-group v-model="state.search.resource" :disabled="state.loading" class="tw-w-full tw-flex-col" @change="nextTick(() => handleCommand(command.Request))">
                <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
                  <el-radio v-show="state.search.type === 'problem'" label="use" class="tw-w-[71px]">我的</el-radio>

                  <div class="tw-w-[103px] tw-text-[var(--el-text-color-placeholder)]" v-for="item in myStatisticsQuestionCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>


                </div>
                <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
                  <el-radio v-show="state.search.type === 'problem'" label="all" class="tw-w-[71px]">全部</el-radio>

                  <div class="tw-w-[103px] tw-text-[var(--el-text-color-placeholder)]" v-for="item in allStatisticsQuestionCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
                </div>
              </el-radio-group>
            </div>
          </div>
        </template>
      </el-tab-pane>
      <el-tab-pane name="change" :disabled="state.loading">
        <template #label>
          <div class="tw-w-[320px] tw-text-[16px] tw-leading-[18px]">
            <div class="tw-mb-[6px] tw-flex tw-items-center tw-text-left">
              <el-icon class="tw-mr-2"><Warning></Warning></el-icon> 变更
            </div>
            <div>
              <el-radio-group v-model="state.search.resource" :disabled="state.loading" class="tw-w-full tw-flex-col" @change.stop="nextTick(() => handleCommand(command.Request))">
                <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
                  <el-radio v-show="state.search.type === 'change'" label="use" class="tw-w-[71px]">我的</el-radio>
                  <div class="tw-w-[103px] tw-text-[var(--el-text-color-placeholder)]" v-for="item in myStatisticsChangeCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>


                </div>
                <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
                  <el-radio v-show="state.search.type === 'change'" label="all" class="tw-w-[71px]">全部</el-radio>


                  <div class="tw-w-[103px] tw-text-[var(--el-text-color-placeholder)]" v-for="item in allStatisticsChangeCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
                </div>
              </el-radio-group>
            </div>
          </div>
        </template>
      </el-tab-pane>
    </el-tabs> -->
    <div class="tab">
      <div class="tw-w-[320px] tw-text-[16px] tw-leading-[18px]" :class="state.search.type === 'event' ? 'on' : ''" @click="nextTick(() => handleCommand(command.Request), (state.search.type = 'event'))">
        <div class="tw-mb-[6px] tw-flex tw-items-center tw-text-left">
          <el-icon class="tw-mr-2"><Warning></Warning></el-icon> 事件
        </div>
        <div>
          <el-radio-group v-model="state.search.resource" :disabled="state.loading" class="tw-w-full tw-flex-col" @change.self="nextTick(() => handleCommand(command.Request))">
            <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
              <el-radio v-show="state.search.type === 'event'" label="use" class="tw-w-[71px]" @click.stop="radioChange('use')">我的</el-radio>

              <div class="tw-w-[103px] tw-text-[var(--el-text-color-placeholder)]" v-for="item in myStatisticsEvnetCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
            </div>
            <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
              <el-radio v-show="state.search.type === 'event'" label="all" class="tw-w-[71px]" @click.stop="radioChange('all')">全部</el-radio>
              <div class="tw-w-[103px] tw-text-[var(--el-text-color-placeholder)]" v-for="item in allStatisticsEvnetCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
            </div>
          </el-radio-group>
        </div>
      </div>
      <div class="tw-w-[320px] tw-text-[16px] tw-leading-[18px]" :class="state.search.type === 'request' ? 'on' : ''" @click="nextTick(() => handleCommand(command.Request), (state.search.type = 'request'))">
        <div class="tw-mb-[6px] tw-flex tw-items-center tw-text-left">
          <el-icon class="tw-mr-2"><CirclePlus></CirclePlus></el-icon> 请求
        </div>
        <div>
          <el-radio-group v-model="state.search.resource" :disabled="state.loading" class="tw-w-full tw-flex-col">
            <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
              <el-radio v-show="state.search.type === 'request'" label="use" class="tw-w-[71px]" @click.stop="radioChange('use')">我的</el-radio>
              <div class="tw-w-[103px] tw-text-[var(--el-text-color-placeholder)]" v-for="item in myStatisticsRequestCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
            </div>
            <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
              <el-radio v-show="state.search.type === 'request'" label="all" class="tw-w-[71px]" @click.stop="radioChange('all')">全部</el-radio>
              <div class="tw-w-[103px] tw-text-[var(--el-text-color-placeholder)]" v-for="item in allStatisticsRequestCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
            </div>
          </el-radio-group>
        </div>
      </div>
      <div class="tw-w-[320px] tw-text-[16px] tw-leading-[18px]" :class="state.search.type === 'problem' ? 'on' : ''" @click="nextTick(() => handleCommand(command.Request), (state.search.type = 'problem'))">
        <div class="tw-mb-[6px] tw-flex tw-items-center tw-text-left">
          <el-icon class="tw-mr-2"><Warning></Warning></el-icon> 问题
        </div>
        <div>
          <el-radio-group v-model="state.search.resource" :disabled="state.loading" class="tw-w-full tw-flex-col" @change.self="nextTick(() => handleCommand(command.Request))">
            <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
              <el-radio v-show="state.search.type === 'problem'" label="use" class="tw-w-[71px]" @click.stop="radioChange('use')">我的</el-radio>

              <div class="tw-w-[103px] tw-text-[var(--el-text-color-placeholder)]" v-for="item in myStatisticsQuestionCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
            </div>
            <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
              <el-radio v-show="state.search.type === 'problem'" label="all" class="tw-w-[71px]" @click.stop="radioChange('all')">全部</el-radio>

              <div class="tw-w-[103px] tw-text-[var(--el-text-color-placeholder)]" v-for="item in allStatisticsQuestionCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
            </div>
          </el-radio-group>
        </div>
      </div>
      <div class="tw-w-[320px] tw-text-[16px] tw-leading-[18px]" :class="state.search.type === 'change' ? 'on' : ''" @click="nextTick(() => handleCommand(command.Request), (state.search.type = 'change'))">
        <div class="tw-mb-[6px] tw-flex tw-items-center tw-text-left">
          <el-icon class="tw-mr-2"><Warning></Warning></el-icon> 变更
        </div>
        <div>
          <el-radio-group v-model="state.search.resource" :disabled="state.loading" class="tw-w-full tw-flex-col" @change.self="nextTick(() => handleCommand(command.Request))">
            <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
              <el-radio v-show="state.search.type === 'change'" label="use" class="tw-w-[71px]" @click.stop="radioChange('use')">我的</el-radio>
              <div class="tw-w-[103px] tw-text-[var(--el-text-color-placeholder)]" v-for="item in myStatisticsChangeCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
            </div>
            <div class="tw-flex tw-w-full tw-items-center tw-text-[14px]" :style="{ lineHeight: '32px' }">
              <el-radio v-show="state.search.type === 'change'" label="all" class="tw-w-[71px]" @click.stop="radioChange('all')">全部</el-radio>

              <div class="tw-w-[103px] tw-text-[var(--el-text-color-placeholder)]" v-for="item in allStatisticsChangeCount" :key="item.label">{{ item.label }}:{{ item.count }}</div>
            </div>
          </el-radio-group>
        </div>
      </div>
    </div>
    <pageTemplate v-model:currentPage="state.page" v-model:pageSize="state.size" :total="state.total" :height="height - 145" :show-paging="true" @size-change="handleCommand(command.Request)" @current-change="handleCommand(command.Request)">
      <!-- <template #left>
        <el-input v-model="state.search.keyword" :disabled="state.loading" :placeholder="$t('glob.Please input field', { field: $t('glob.Keyword') })" @keyup.enter="handleCommand(command.Request)">
          <template #append>
            <el-button :icon="Search" :disabled="state.loading" @click.stop="handleCommand(command.Request)" />
          </template>
        </el-input>
      </template> -->
      <template #center>
        {{ state.search.title }}
        <!-- <div>src\views\pages\alarm_convergence\IntelligentEvents\eventBoard\index.vue</div> -->
      </template>
      <template #right>
        <div style="padding: 8px 0; box-sizing: border-box; display: flex">
          <el-input v-model="orderLink" placeholder="搜索工单" class="input-with-select" @keyup.enter="ruoterOrder(orderLink)">
            <template #append>
              <el-button :icon="Search" @click="ruoterOrder(orderLink)" />
            </template>
          </el-input>
          <el-button type="default" :disabled="state.loading" :icon="Refresh" @click="handleCommand(command.Refresh)"></el-button>
        </div>
        <!-- <el-button type="primary" :disabled="state.loading" :icon="Plus" @click="handleCommand(command.Create, {})">{{ $t("glob.add") }}{{ props.title }}</el-button> -->
      </template>
      <template #default="{ height: tableHeight }">
        <!-- {{ state.search }} -->
        <!-- 事件表格 -->
        <el-table v-if="state.search.type === 'event'" @cell-contextmenu="copyClick" v-loading="state.loading" ref="tableRef" :data="state.list" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).map((v) => v.id))" @expand-change="handleExpand">
          <TableColumn type="selection" :width="55"></TableColumn>
          <TableColumn type="tenantName" show-filter v-model:many-filtered-value="tenantNameObj" @filter-change="handleQuery()" prop="tenantName" label="客户名称" sortable="custom" :width="130"></TableColumn>
          <TableColumn type="default" prop="responseTime" label="响应时限" :width="100" :formatter="handleTableResponseTime"></TableColumn>
          <TableColumn type="default" prop="resolveTime" label="解决时限" :width="100" :formatter="handleTableResolveTime"></TableColumn>
          <TableColumn type="enum" show-filter v-model:filtered-value="state.search.priority" @filter-change="handleQuery()" prop="priority" label="优先级" :width="110" :filters="priorityOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>
          <TableColumn type="enum" show-filter v-model:filtered-value="state.search.eventState" @filter-change="handleQuery()" prop="eventState" label="状态" :width="100" :filters="eventStateOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>
          <TableColumn type="order" show-filter v-model:many-filtered-value="orderObj" @filter-change="handleQuery()" prop="identifier" label="工单" sortable="custom" :width="180">
            <template #default="{ row, column }">
              <router-link v-if="row.id" :to="{ name: '510685950393712640', params: { id: row.id }, query: { fallback: route.name as string, tenant: row.tenantId as string } }">
                <template #default="{ href }">
                  <el-link type="primary" :href="href" target="_blank" :underline="false" class="tw-ml-[6px]" @click.stop>{{ row[column.property] }}</el-link>
                </template>
              </router-link>
            </template>
            <!-- <template #default="{ row, column }">
              <el-link type="primary" :underline="false" class="tw-ml-[6px]" @click.stop="router.push({ name: '510685950393712640', params: { id: row.id }, query: { fallback: route.name as string } })">{{ row[column.property] }}</el-link>
            </template> -->
          </TableColumn>
          <TableColumn type="number" v-model:filtered-value="state.search.alarmNumber" @filter-change="handleQuery()" prop="alarmNumber" label="告警" sortable="custom" :width="80">
            <template #default="{ row }">
              <el-link type="danger" :underline="false">{{ row.alarmNumber || 0 }}</el-link>
            </template>
          </TableColumn>
          <TableColumn type="default" show-filter v-model:filtered-value="state.search.summary" @filter-change="handleQuery()" prop="summary" label="摘要" sortable="custom" :min-width="300"></TableColumn>
          <TableColumn type="date" show-filter v-model:filtered-value="state.search.createTime" filter-multiple @filter-change="handleQuery()" prop="createTime" label="创建时间" sortable="custom" :width="140"></TableColumn>
          <TableColumn type="date" show-filter v-model:filtered-value="state.search.updateTime" filter-multiple @filter-change="handleQuery()" prop="updateTime" label="修改时间" sortable="custom" :width="140"></TableColumn>
          <TableColumn type="default" show-filter v-model:filtered-value="state.search.responsibleName" @filter-change="handleQuery()" prop="responsibleName" label="负责人" sortable="custom" :min-width="120"></TableColumn>
          <TableColumn type="default" show-filter v-model:filtered-value="state.search.actorName" @filter-change="handleQuery()" prop="actorName" label="处理人" sortable="custom" :min-width="120"></TableColumn>
        </el-table>
        <!-- 服务请求表格 -->
        <el-table v-if="state.search.type === 'request'" @cell-contextmenu="copyClick" v-loading="state.loading" ref="tableRef" :data="state.list" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).map((v) => v.id))" @expand-change="handleExpand">
          <TableColumn type="selection" :width="55"></TableColumn>
          <TableColumn type="tenantName" show-filter v-model:filtered-value="tenantNameObj" @filter-change="handleQuery()" prop="tenantName" label="客户名称" sortable="custom" :min-width="130"></TableColumn>
          <TableColumn type="enum" show-filter v-model:filtered-value="state.search.priority" @filter-change="handleQuery()" prop="priority" label="优先级" :width="120" :filters="priorityOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>
          <TableColumn type="enum" show-filter v-model:filtered-value="state.search.serviceState" @filter-change="handleQuery()" prop="serviceState" label="状态" :width="120" :filters="serviceStateOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>
          <TableColumn type="order" show-filter v-model:many-filtered-value="orderObj" @filter-change="handleQuery()" prop="identifier" label="工单" sortable="custom" :width="180">
            <template #default="{ row, column }">
              <router-link v-if="row.id" :to="{ name: '514703398516293632', params: { id: row.id }, query: { fallback: route.name as string, tenant: row.tenantId as string } }">
                <template #default="{ href }">
                  <el-link type="primary" :href="href" target="_blank" :underline="false" class="tw-ml-[6px]" @click.stop>{{ row[column.property] }}</el-link>
                </template>
              </router-link>
            </template>
            <!-- <template #default="{ row, column }">
              <el-link type="primary" :underline="false" class="tw-ml-[6px]" @click.stop="router.push({ name: '514703398516293632', params: { id: row.id }, query: { fallback: route.name as string } })">{{ row[column.property] }}</el-link>
            </template> -->
          </TableColumn>
          <TableColumn type="number" v-model:filtered-value="state.search.alarmNumber" @filter-change="handleQuery()" prop="alarmNumber" label="告警" sortable="custom" :width="80">
            <template #default="{ row }">
              <el-link type="danger" :underline="false">{{ row.alarmNumber || 0 }}</el-link>
            </template>
          </TableColumn>
          <TableColumn type="default" show-filter v-model:filtered-value="state.search.title" @filter-change="handleQuery()" prop="title" label="摘要" sortable="custom" :min-width="120"></TableColumn>
          <TableColumn type="date" show-filter v-model:filtered-value="state.search.createTime" filter-multiple @filter-change="handleQuery()" prop="createTime" label="创建时间" sortable="custom" :width="140"></TableColumn>
          <TableColumn type="date" show-filter v-model:filtered-value="state.search.updateTime" filter-multiple @filter-change="handleQuery()" prop="updateTime" label="修改时间" sortable="custom" :width="140"></TableColumn>
          <TableColumn type="default" show-filter v-model:filtered-value="state.search.responsibleName" @filter-change="handleQuery()" prop="responsibleName" label="负责人" sortable="custom" :min-width="120"></TableColumn>
          <TableColumn type="default" show-filter v-model:filtered-value="state.search.actorName" @filter-change="handleQuery()" prop="currentOwnerName" label="处理人" sortable="custom" :min-width="120"></TableColumn>
        </el-table>
        <!-- 问题表格 -->
        <el-table v-if="state.search.type === 'problem'" @cell-contextmenu="copyClick" v-loading="state.loading" ref="tableRef" :data="state.list" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).map((v) => v.id))" @expand-change="handleExpand">
          <TableColumn type="selection" :width="55"></TableColumn>
          <TableColumn type="tenantName" show-filter v-model:filtered-value="tenantNameObj" @filter-change="handleQuery()" prop="tenantName" label="客户名称" sortable="custom" :min-width="130"></TableColumn>
          <TableColumn type="enum" show-filter v-model:filtered-value="state.search.priority" @filter-change="handleQuery()" prop="priority" label="优先级" :width="120" :filters="priorityOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>
          <TableColumn type="enum" show-filter v-model:filtered-value="state.search.questionState" @filter-change="handleQuery()" prop="questionState" label="状态" :width="120" :filters="questionStateOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>
          <TableColumn type="order" show-filter v-model:many-filtered-value="orderObj" @filter-change="handleQuery()" prop="identifier" label="工单" sortable="custom" :width="180">
            <template #default="{ row, column }">
              <router-link v-if="row.id" :to="{ name: '515035822471249920', params: { id: row.id }, query: { fallback: route.name as string, tenant: row.tenantId as string } }">
                <template #default="{ href }">
                  <el-link type="primary" :href="href" target="_blank" :underline="false" class="tw-ml-[6px]" @click.stop>{{ row[column.property] }}</el-link>
                </template>
              </router-link>
            </template>
            <!-- <template #default="{ row, column }">
              <el-link type="primary" :underline="false" class="tw-ml-[6px]" @click.stop="router.push({ name: '515035822471249920', params: { id: row.id }, query: { fallback: route.name as string } })">{{ row[column.property] }}</el-link>
            </template> -->
          </TableColumn>
          <!-- <TableColumn type="number" v-model:filtered-value="state.search.alarmNumber" @filter-change="handleQuery()"  prop="alarmNumber" label="告警" sortable="custom" :width="80"></TableColumn> -->
          <TableColumn type="number" v-model:filtered-value="state.search.alertNumber" @filter-change="handleQuery()" prop="alertNumber" label="告警" sortable="custom" :width="80">
            <template #default="{ row }">
              <el-link type="danger" :underline="false">{{ row.alertNumber || 0 }}</el-link>
            </template>
          </TableColumn>
          <TableColumn type="default" show-filter v-model:filtered-value="state.search.digest" @filter-change="handleQuery()" prop="digest" label="摘要" sortable="custom" :min-width="120"></TableColumn>
          <TableColumn type="date" show-filter v-model:filtered-value="state.search.createTime" filter-multiple @filter-change="handleQuery()" prop="createTime" label="创建时间" sortable="custom" :width="140"></TableColumn>
          <TableColumn type="date" show-filter v-model:filtered-value="state.search.updateTime" filter-multiple @filter-change="handleQuery()" prop="updateTime" label="修改时间" sortable="custom" :width="140"></TableColumn>
          <TableColumn type="default" show-filter v-model:filtered-value="state.search.responsibleName" @filter-change="handleQuery()" prop="responsibleName" label="负责人" sortable="custom" :min-width="120"></TableColumn>
          <TableColumn type="default" show-filter v-model:filtered-value="state.search.actorName" @filter-change="handleQuery()" prop="actorName" label="处理人" sortable="custom" :min-width="120">
            <template #default="{ row }">
              <div>
                {{ row.actorName ? row.actorName : row.userGroupName }}
              </div>
            </template>
          </TableColumn>
        </el-table>
        <!-- 变更表格 -->
        <el-table v-if="state.search.type === 'change'" @cell-contextmenu="copyClick" v-loading="state.loading" ref="tableRef" :data="state.list" row-key="id" :height="tableHeight" :default-sort="state.sort" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" @selection-change="(select) => (state.select = (select as DataItem[]).map((v) => v.id))" @expand-change="handleExpand">
          <TableColumn type="selection" :width="55"></TableColumn>
          <TableColumn type="tenantName" show-filter v-model:filtered-value="tenantNameObj" @filter-change="handleQuery()" prop="tenantName" label="客户名称" sortable="custom" :min-width="130"></TableColumn>
          <TableColumn type="enum" show-filter v-model:filtered-value="state.search.priority" @filter-change="handleQuery()" prop="priority" label="优先级" :width="120" :filters="priorityOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>
          <TableColumn type="enum" show-filter v-model:filtered-value="state.search.changeState" @filter-change="handleQuery()" prop="changeState" label="状态" :width="120" :filters="changeStateOption.map((v) => ({ ...v, text: v.label }))" sortable="custom"></TableColumn>

          <TableColumn type="order" show-filter v-model:many-filtered-value="orderObj" @filter-change="handleQuery()" prop="identifier" label="工单" sortable="custom" :width="180">
            <template #default="{ row, column }">
              <router-link v-if="row.id" :to="{ name: '515123784953364480', params: { id: row.id }, query: { fallback: route.name as string, tenant: row.tenantId as string } }">
                <template #default="{ href }">
                  <el-link type="primary" :href="href" target="_blank" :underline="false" class="tw-ml-[6px]" @click.stop>{{ row[column.property] }}</el-link>
                </template>
              </router-link>
            </template>
            <!-- <template #default="{ row, column }">
              <el-link type="primary" :underline="false" class="tw-ml-[6px]" @click.stop="router.push({ name: '515123784953364480', params: { id: row.id }, query: { fallback: route.name as string } })">{{ row[column.property] }}</el-link>
            </template> -->
          </TableColumn>
          <TableColumn type="number" v-model:filtered-value="state.search.alertNumber" @filter-change="handleQuery()" prop="alertNumber" label="告警" sortable="custom" :width="80">
            <template #default="{ row }">
              <el-link type="danger" :underline="false">{{ row.alertNumber || 0 }}</el-link>
            </template>
          </TableColumn>
          <TableColumn type="default" show-filter v-model:filtered-value="state.search.digest" @filter-change="handleQuery()" prop="digest" label="摘要" sortable="custom" :min-width="120"></TableColumn>
          <TableColumn type="date" show-filter v-model:filtered-value="state.search.startTime" filter-multiple @filter-change="handleQuery()" prop="createdTime" label="创建时间" sortable="custom" :width="140"></TableColumn>
          <TableColumn type="date" show-filter v-model:filtered-value="state.search.updatedTime" filter-multiple @filter-change="handleQuery()" prop="updatedTime" label="修改时间" sortable="custom" :width="140"></TableColumn>
          <TableColumn type="default" show-filter v-model:filtered-value="state.search.responsibleName" @filter-change="handleQuery()" prop="createdBy" label="负责人" sortable="custom" :min-width="120" :formatter="(_row, _col, v) => (v ? JSON.parse(v).username : '--')"></TableColumn>
          <TableColumn type="default" show-filter v-model:filtered-value="state.search.actorName" @filter-change="handleQuery()" prop="actorName" label="处理人" sortable="custom" :min-width="120" :formatter="(_row, _col, v) => (v ? v : '--')"></TableColumn>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
  <Editor ref="editorRef" title="类型" display="dialog"></Editor>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { h, ref, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import {} from "@/views/pages/apis/event";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Refresh, Warning, CirclePlus } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";
import { ElTable } from "element-plus";
import Editor from "./Editor.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox, ElProgress } from "element-plus";

import { type BaseItem, type Item } from "./helper";
import { state, dataList } from "./helper";
import { resetData, handleExpand, command } from "./helper";

import TableColumn from "@/components/tableColumn/TableColumn.vue";
import getUserInfo from "@/utils/getUserInfo";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
import { questionState, questionStateOption } from "@/views/pages/apis/question";
import { serviceState, serviceStateOption, eventState, eventStateOption, priority, priorityOption, TimeLimit, urgencyType, urgencyTypeOption } from "@/views/pages/apis/event";
import { changeStateOption, getOrderType } from "@/views/pages/apis/eventBoard";
import { getEventList as getItemList, getServiceList as getServiceItem, getQuestionList as getQuestionItem, getChangeItems as getChangeItem, getChangeItemsOwner } from "@/views/pages/apis/eventBoard";
import { addModuleData as addItemData, setModuleData as setItemData, modModuleData as modItemData, delModuleData as delItemData } from "./api";
import { eventCount, eventCountSate, getEventCount, getRequestCount, getQuestionCount, getChangeCount } from "@/views/pages/apis/eventBoard";

import { find } from "lodash-es";

/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "alarmBoard" });
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */

const width = inject("width", ref(0));
const height = inject("height", ref(0));
const orderLink = ref("");
interface Props {
  title?: string;
}
const props = withDefaults(defineProps<Props>(), { title: "事件看板" });
const userInfo = getUserInfo();

// console.log(userInfo, 666666);
//
const myStatisticsEvnetCount = ref([]);
const allStatisticsEvnetCount = ref([]);

const myStatisticsRequestCount = ref([]);
const allStatisticsRequestCount = ref([]);

const myStatisticsQuestionCount = ref([]);
const allStatisticsQuestionCount = ref([]);

const myStatisticsChangeCount = ref([]);
const allStatisticsChangeCount = ref([]);

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

const tableRef = ref<InstanceType<typeof ElTable>>();
const editorRef = ref<InstanceType<typeof Editor>>();
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */

/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const timer = ref<null | number>(null);
const autoRefreshTime = ref(0);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  state.search.resource = "all";
  state.search.type = "event";
  handleRefresh().then(() => (autoRefreshTime.value = 60));
}
const tenantNameObj = ref({
  firstName: "",
  status: "include",
  type: "include",
  lastName: "",
  relation: "AND",
});
const orderObj = ref({
  firstName: "",
  status: "include",
  type: "include",
  lastName: "",
  relation: "AND",
});

function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {
  if (timer.value !== null) {
    clearInterval(timer.value);
    timer.value = null;
  }
}
function beforeDestroy() {
  if (timer.value !== null) {
    clearInterval(timer.value);
    timer.value = null;
  }
}
function destroyed() {}

function ruoterOrder(val) {
  // console.log(val);
  getOrderType({ id: val }).then((res) => {
    // console.log(res, 66666);
    if (res.success) {
      let routeData = null;
      switch (res.data) {
        case "EVENT_ORDER":
          routeData = router.resolve({ name: "510685950393712640", params: { id: val }, query: { fallback: route.name as string } }, { target: "_blank" });
          break;
        case "SERVICE_REQUEST":
          routeData = router.resolve({ name: "514703398516293632", params: { id: val }, query: { fallback: route.name as string } }, { target: "_blank" });

          break;
        case "CHANGE":
          routeData = router.resolve({ name: "515123784953364480", params: { id: val }, query: { fallback: route.name as string } });

          break;
        case "QUESTION":
          routeData = router.resolve({ name: "515035822471249920", params: { id: val }, query: { fallback: route.name as string } });
          break;
      }
      const link = document.createElement("a");
      link.setAttribute("href", routeData.href);
      link.setAttribute("target", "_blank");
      document.body.appendChild(link);
      link.click();
    }
  });
  //  let routeData=router.resolve({
  //  })
}

function handleTableResponseTime(_row, _col, v) {
  if (!Number(_row.responseLimit)) return "--";

  const percentage = (((Number(_row.responseTime) || 0) / (Number(_row.responseLimit) || 0)) * 100 > 100 ? 100 : (Number(_row.responseTime) / Number(_row.responseLimit)) * 100) || 0;
  const color = setSlaState((_row.slaTimeLimit || {}).responseTimeLimits || [], Number(_row.responseTime) || 0).color;
  return h("div", [h("div", { class: "tw-text-center" }, `${_row.responseTime}分钟 / ${_row.responseLimit}分钟`), h(ElProgress, { percentage, color, showText: false, class: "tw-w-full" })]);

  // return h("div", [h("div", { class: "tw-text-center" }, `${convertMinutes(_row.responseTime)} / ${convertMinutes(_row.responseLimit)}`), h(ElProgress, { percentage, color, showText: false, class: "tw-w-full" })]);
}

function handleTableResolveTime(_row, _col, v) {
  if (!Number(_row.resolveLimit)) return "--";
  const percentage = (((Number(_row.resolveTime) || 0) / (Number(_row.resolveLimit) || 0)) * 100 > 100 ? 100 : (Number(_row.resolveTime) / Number(_row.resolveLimit)) * 100) || 0;
  const color = setSlaState((_row.slaTimeLimit || {}).completedTimeLimits || [], Number(_row.resolveTime) || 0).color;
  // return h("div", [h("div", { class: "tw-text-center" }, `${convertMinutes(_row.resolveTime)} / ${convertMinutes(_row.resolveLimit)}`), h(ElProgress, { percentage, color, showText: false, class: "tw-w-full" })]);
  return h("div", [h("div", { class: "tw-text-center" }, `${_row.resolveTime}分钟 / ${_row.resolveLimit}分钟`), h(ElProgress, { percentage, color, showText: false, class: "tw-w-full" })]);

}

function convertMinutes(minutes: any) {
  var seconds = minutes * 60; // 将分钟转化为秒数

  var years = Math.floor(seconds / 31536000); // 每年有31536000秒（平均）
  seconds -= years * 31536000;

  var months = Math.floor(seconds / 2592000); // 每月有2592000秒（平均）
  seconds -= months * 2592000;

  var weeks = Math.floor(seconds / 604800); // 每周有604800秒（平均）
  seconds -= weeks * 604800;

  var days = Math.floor(seconds / 86400); // 每天有86400秒（平均）
  seconds -= days * 86400;

  var hours = Math.floor(seconds / 3600); // 每小时有3600秒
  seconds -= hours * 3600;

  const mins = minutes % 60;

  return `${years > 0 ? years + "Y" : ""}${months > 0 ? months + "M" : ""}${weeks > 0 ? weeks + "W" : ""}${days > 0 ? days + "D" : ""}${hours > 0 ? hours + "H" : ""}${mins > 0 ? mins + "m" : ""}`;
}

function setSlaState(list: TimeLimit[], val: number) {
  let result = urgencyType.NORMAL;
  list.unshift({
    urgencyType: "NORMAL",
    tolerateMinutes: 0,
  });
  list.sort((a: any, b: any) => {
    return Number(a.tolerateMinutes) - Number(b.tolerateMinutes);
  });

  let index = list.findIndex((v, i) => {
    if (v.tolerateMinutes > val) {
      // console.log(i);
      return i;
    }
  });
  if (index != -1) {
    result = list[index - 1].urgencyType;
  } else {
    result = list[list.length - 1].urgencyType;
  }
  // list.sort((a, b) => (Number(b.tolerateMinutes) || 0) - (Number(a.tolerateMinutes) || 0)).forEach((el) => ((Number(el.tolerateMinutes) || 0) >= val || list.length === 1) && (result = el.urgencyType));
  return find(urgencyTypeOption, ({ value }) => value === result) || { label: "", value: "", color: "#eee" };
}

watch(autoRefreshTime, (autoRefreshTime) => {
  if (timer.value !== null) timer.value = (clearInterval(timer.value), null);
  if (autoRefreshTime) timer.value = setInterval(queryData, autoRefreshTime * 1000);
});

function radioChange(val: string) {
  state.search.resource = val;
  handleCommand(command.Request);
}
const tenants = userInfo.tenants;

/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
async function handleCommand(type: command, data?: Partial<Item> | (BaseItem & Partial<Item>)) {
  const time = autoRefreshTime.value;
  autoRefreshTime.value = 0;
  tenantNameObj.value = {
    firstName: "",
    status: "include",
    type: "include",
    lastName: "",
    relation: "AND",
  };
  orderObj.value = {
    firstName: "",
    status: "include",
    type: "include",
    lastName: "",
    relation: "AND",
  };
  state.search.priority = "";
  state.search.changeState = "";
  state.search.eventState = "";
  state.search.servicestate = "";
  state.search.identifier = "";
  state.search.alertNumber = "";
  state.search.title = "";
  state.search.digest = "";
  state.search.summary = "";
  state.search.createTime = "";
  state.search.startTime = "";
  state.search.updatedTime = "";
  state.search.updateTime = "";
  state.search.responsibleName = "";
  state.search.actorName = "";
  state.search.tenantIdFilterRelation = "AND";
  state.search.includeTenantIds = [];
  state.search.excludeTenantIds = [];
  state.search.includeOrderIds = [];
  state.search.excludeOrderIds = [];
  state.search.orderIdFilterRelation = "AND";
  // console.log(type, 6666666666, data);
  try {
    state.loading = true;
    await nextTick();
    await queryData();
    // switch (type) {
    //   case command.Refresh:
    //     await resetData();
    //     await queryData();
    //     break;
    //   case command.Request:
    //     await queryData();
    //     break;
    //   case command.Preview:
    //     await previewItem(data as Partial<Item>);
    //     break;
    //   case command.Create:
    //     await createItem(data as Partial<Item>);
    //     await resetData();
    //     await queryData();
    //     break;
    //   case command.Update:
    //     await rewriteItem(data as BaseItem & Partial<Item>);
    //     await resetData();
    //     await queryData();
    //     break;
    //   case command.Modify:
    //     await modifyItem(data as BaseItem & Partial<Item>);
    //     await resetData();
    //     await queryData();
    //     break;
    //   case command.Delete:
    //     await deleteItem(data as BaseItem & Partial<Item>);
    //     await resetData();
    //     await queryData();
    //     break;
    // }
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await resetData();
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
      await queryData();
    }
  } finally {
    autoRefreshTime.value = time;
    state.loading = false;
  }
}
function copyClick(row, column, cell, event) {
  event.stopPropagation();
}

async function handleRefresh() {
  try {
    state.loading = true;
    await nextTick();
    await resetData();
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
async function handleQuery() {
  try {
    state.loading = true;
    await nextTick();

    let includeTenantIds: any = [];
    let excludeTenantIds: any = [];
    let includeOrderIds: any = [];
    let excludeOrderIds: any = [];
    if (tenantNameObj.value) {
      state.search.tenantIdFilterRelation = tenantNameObj.value.relation;

      tenants.forEach((v: any, i: number) => {
        // includeTenantIds = [];
        // excludeTenantIds = [];
        switch (tenantNameObj.value.status) {
          case "include":
            if (tenantNameObj.value.firstName != "" && v.tenantName.includes(tenantNameObj.value.firstName)) {
              includeTenantIds.push(v.tenantId);
            } else {
              includeTenantIds.push();
            }
            break;
          case "exclude":
            if (tenantNameObj.value.firstName != "" && v.tenantName.includes(tenantNameObj.value.firstName)) {
              excludeTenantIds.push(v.tenantId);
            } else excludeTenantIds.push();
            break;
          case "be":
            if (v.tenantName == tenantNameObj.value.firstName) {
              includeTenantIds.push(v.tenantId);
            }
            break;
          case "notBe":
            if (v.tenantName == tenantNameObj.value.firstName) {
              excludeTenantIds.push(v.tenantId);
            }
            break;
        }

        switch (tenantNameObj.value.type) {
          // console.log(v.tenantName.includes(tenantNameObj.value.lastName))
          case "include":
            if (tenantNameObj.value.lastName != "" && v.tenantName.includes(tenantNameObj.value.lastName)) {
              includeTenantIds.push(v.tenantId);
            } else includeTenantIds.push();
            break;
          case "exclude":
            if (tenantNameObj.value.lastName != "" && v.tenantName.includes(tenantNameObj.value.lastName)) {
              excludeTenantIds.push(v.tenantId);
            } else excludeTenantIds.push();
            break;
          case "be":
            if (v.tenantName == tenantNameObj.value.lastName) {
              includeTenantIds.push(v.tenantId);
            }
            break;
          case "notBe":
            if (v.tenantName == tenantNameObj.value.lastName) {
              excludeTenantIds.push(v.tenantId);
            }
            break;
        }
      });
      state.search.includeTenantIds = includeTenantIds;
      state.search.excludeTenantIds = excludeTenantIds;
    }
    if (orderObj.value) {
      state.search.orderIdFilterRelation = orderObj.value.relation;

      switch (orderObj.value.status) {
        case "include":
          includeOrderIds.push(orderObj.value.firstName);
          break;
        case "exclude":
          excludeOrderIds.push(orderObj.value.firstName);
          break;
      }
      switch (orderObj.value.type) {
        case "include":
          includeOrderIds.push(orderObj.value.lastName);
          break;
        case "exclude":
          excludeOrderIds.push(orderObj.value.lastName);
          break;
      }

      state.search.includeOrderIds = includeOrderIds;
      state.search.excludeOrderIds = excludeOrderIds;
    }

    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}

async function getEventCountList() {
  await getEventCount({ type: "my", id: userInfo.userId }).then((res: any) => {
    if (res.success) {
      myStatisticsEvnetCount.value = [];
      res.data.forEach((item: any) => {
        if (eventCount[item.eventState]) {
          myStatisticsEvnetCount.value.push({
            label: eventCountSate[item.eventState],
            count: item.eventCount,
          });
        }
      });
    }
  });
  await getEventCount({ type: "all" }).then((res: any) => {
    if (res.success) {
      allStatisticsEvnetCount.value = [];
      res.data.forEach((item: any) => {
        if (eventCount[item.eventState]) {
          allStatisticsEvnetCount.value.push({
            label: eventCountSate[item.eventState],
            count: item.eventCount,
          });
        }
      });
    }
  });
}

async function getRequestCountList() {
  await getRequestCount({ type: "my", tenantId: userInfo.currentTenantId }).then((res: any) => {
    if (res.success) {
      myStatisticsRequestCount.value = [];
      res.data.forEach((item: any) => {
        if (eventCount[item.serviceState]) {
          myStatisticsRequestCount.value.push({
            label: eventCountSate[item.serviceState],
            count: item.serviceCount,
          });
        }
      });
    }
  });
  await getRequestCount({ type: "all", id: userInfo.currentTenantId }).then((res: any) => {
    if (res.success) {
      allStatisticsRequestCount.value = [];
      res.data.forEach((item: any) => {
        if (eventCount[item.serviceState]) {
          allStatisticsRequestCount.value.push({
            label: eventCountSate[item.serviceState],
            count: item.serviceCount,
          });
        }
      });
    }
  });
}
async function getQuestionCountList() {
  //我的问题统计
  await getQuestionCount({ type: "my", id: userInfo.userId }).then((res: any) => {
    if (res.success) {
      myStatisticsQuestionCount.value = [];
      res.data.forEach((item: any) => {
        if (eventCount[item.questionState]) {
          myStatisticsQuestionCount.value.push({
            label: eventCountSate[item.questionState],
            count: item.count,
          });
        }
      });
    }
  });
  await getQuestionCount({ type: "all" }).then((res: any) => {
    if (res.success) {
      allStatisticsQuestionCount.value = [];
      res.data.forEach((item: any) => {
        if (eventCount[item.questionState]) {
          allStatisticsQuestionCount.value.push({
            label: eventCountSate[item.questionState],
            count: item.count,
          });
        }
      });
    }
  });
}

async function getChangeCountList() {
  await getChangeCount({ type: "owner" }).then((res: any) => {
    if (res.success) {
      myStatisticsChangeCount.value = [];
      res.data.forEach((item: any) => {
        if (eventCount[item.state]) {
          myStatisticsChangeCount.value.push({
            label: eventCountSate[item.state],
            count: item.count,
          });
        }
      });
    }
  });
  await getChangeCount({ type: "" }).then((res: any) => {
    if (res.success) {
      allStatisticsChangeCount.value = [];
      res.data.forEach((item: any) => {
        if (eventCount[item.state]) {
          allStatisticsChangeCount.value.push({
            label: eventCountSate[item.state],
            count: item.count,
          });
        }
      });
    }
  });
}

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
async function queryData() {
  let sort: string[] = [];
  switch ((state.sort || {}).order) {
    case "ascending":
      sort.push(`${String(state.sort?.prop)},asc`);
      break;
    case "descending":
      sort.push(`${String(state.sort?.prop)},desc`);
      break;
  }
  getEventCountList();
  getRequestCountList();
  getQuestionCountList();
  getChangeCountList();
  // console.log(state.search.type, 666666666);
  switch (state.search.type) {
    case "event":
      // delete state.search.tenantName;
      if (state.search.resource === "use") {
        const { success, message, data, page, size, total } = await getItemList({ ...state.search, ...tenantNameObj.value, type: "myList", sort, tenantId: userInfo.currentTenantId, paging: { pageNumber: state.page, pageSize: state.size } });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        state.list.splice(0, Infinity, ...(data instanceof Array ? data : []));
        state.total = Number(total) || 0;
      } else {
        const { success, message, data, page, size, total } = await getItemList({ ...state.search, ...tenantNameObj.value, type: "list", sort, boardOrNot: true, paging: { pageNumber: state.page, pageSize: state.size } });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        state.list.splice(0, Infinity, ...(data instanceof Array ? data : []));
        state.total = Number(total) || 0;
      }
      // getEventCountList();
      break;

    case "request":
      if (state.search.resource === "use") {
        const { success, message, data, page, size, total } = await getServiceItem({ ...state.search, type: "myList", sort, tenantId: userInfo.currentTenantId, paging: { pageNumber: state.page, pageSize: state.size } });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        state.list.splice(0, Infinity, ...(data instanceof Array ? data : []));
        state.total = Number(total) || 0;
      } else {
        const { success, message, data, page, size, total } = await getServiceItem({ ...state.search, type: "list", sort, boardOrNot: true, tenantId: userInfo.currentTenantId, paging: { pageNumber: state.page, pageSize: state.size } });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        state.list.splice(0, Infinity, ...(data instanceof Array ? data : []));
        state.total = Number(total) || 0;
      }
      // getRequestCountList();
      break;

    case "problem":
      if (state.search.resource === "use") {
        const { success, message, data, page, size, total } = await getQuestionItem({ ...state.search, userId: userInfo.userId, sort, tenantId: userInfo.currentTenantId, pageNumber: state.page, pageSize: state.size });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        state.list.splice(0, Infinity, ...(data instanceof Array ? data : []));
        state.total = Number(total) || 0;
      } else {
        const { success, message, data, page, size, total } = await getQuestionItem({ ...state.search, sort, boardOrNot: true, tenantId: userInfo.currentTenantId, pageNumber: state.page, pageSize: state.size });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        state.list.splice(0, Infinity, ...(data instanceof Array ? data : []));
        state.total = Number(total) || 0;
      }
      break;

    case "change":
      if (state.search.resource === "use") {
        const { success, message, data, page, size, total } = await getChangeItemsOwner({ ...state.search, sort, type: "owner", tenantId: userInfo.currentTenantId, pageNumber: state.page, pageSize: state.size });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        state.list.splice(0, Infinity, ...(data instanceof Array ? data : []));
        state.total = Number(total) || 0;
      } else {
        const { success, message, data, page, size, total } = await getChangeItem({ ...state.search, sort, tenantId: userInfo.currentTenantId, pageNumber: state.page, pageSize: state.size });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        state.list.splice(0, Infinity, ...(data instanceof Array ? data : []));
        state.total = Number(total) || 0;
      }
      break;
  }

  // if (state.search.type === 'event') {

  // }
}
async function createItem(row: Partial<Item>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Partial<Item>) => {
    const { success, message, data } = await addItemData(form);
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${i18n.t("axios.Operation successful")}`);
  });
}
async function previewItem(row: Partial<Item>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Partial<Item>) => {
    const { success, message, data } = await setItemData(form);
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${i18n.t("axios.Operation successful")}`);
  });
}
async function rewriteItem(row: BaseItem & Partial<Item>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Partial<Item>) => {
    const { success, message, data } = await setItemData(form);
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${i18n.t("axios.Operation successful")}`);
  });
}
async function modifyItem(row: BaseItem & Partial<Item>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Partial<Item>) => {
    const { success, message, data } = await modItemData(form);
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${i18n.t("axios.Operation successful")}`);
  });
}
async function deleteItem(row: BaseItem & Partial<Item>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Partial<Item>) => {
    const { success, message, data } = await delItemData(form);
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${i18n.t("axios.Operation successful")}`);
  });
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.tab {
  display: flex;
  > div {
    flex: none;
    border-top: 1px solid #cccc;
    border-left: 1px solid #cccc;
    border-bottom: 1px solid #cccc;
    padding: 10px;
    box-sizing: border;
  }
  > div:last-child {
    border-right: 1px solid #cccc;
  }
  > div:hover {
    background: #eee;
  }

  .on {
    border-bottom: none;
  }
}
</style>
