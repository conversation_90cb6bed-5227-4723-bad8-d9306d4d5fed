import getUserInfo from "@/utils/getUserInfo";
import { OrderType } from "@/views/pages/apis/association";
import router from "@/router/index";
const userInfo = getUserInfo();

export default function (type: keyof typeof OrderType, orderId: string, tenantId: string, routerQuery?: Record<string, any>) {
  // 缺少参数不执行
  if (!type || !orderId || !tenantId) return;

  const fallback: string = router.currentRoute.value.name as string;
  userInfo.cutTenant(tenantId).finally(async () => {
    const routeData = router.resolve({
      // name: routerName,
      path: {
        [OrderType.EVENT_ORDER]: `/event/intelligent_events/details/${orderId}`,
        [OrderType.SERVICE_REQUEST]: `/event/intelligent_request/details/${orderId}`,
        [OrderType.CHANGE]: `/event/intelligent_change/details/${orderId}`,
        [OrderType.PUBLISH]: `/event/intelligent_publish/details/${orderId}`,
        [OrderType.QUESTION]: `/event/intelligent_question/details/${orderId}`,
        [OrderType.DICT_EVENT_ORDER]: `/event/intelligent_events/dict_event/details/${orderId}`,
        [OrderType.DICT_SERVICE_REQUEST]: `/event/intelligent_events/dict_serviceRequest/details/${orderId}`,
      }[type],
      query: Object.assign(
        {
          fallback,
        },
        routerQuery || {}
      ),
    });
    // window.open(routeData.href);
    const link = document.createElement("a");
    link.href = routeData.href;
    link.target = "_blank"; // 强制新窗口
    link.rel = "noopener noreferrer"; // 安全防护
    link.style.opacity = "0";
    link.style.position = "absolute";
    link.style.pointerEvents = "none";
    document.body.appendChild(link);
    link.click();
    setTimeout(() => {
      document.body.removeChild(link);
    }, 100);
  });
}
