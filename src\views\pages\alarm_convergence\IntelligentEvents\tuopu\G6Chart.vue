<template>
  <!-- 添加边框便于调试 -->
  <div ref="container" style="width: 100%; height: 600px; border: 1px solid #ccc"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import G6 from "@antv/g6";

const container = ref(null);
let graph = null;

// 简化数据（使用布局代替手动坐标）
const data = {
  nodes: [
    { id: "node0", size: 30 },
    { id: "node1", size: 30 },
    { id: "node2", size: 30 },
    { id: "node3", size: 30 },
    { id: "node4", size: 30, isLeaf: true },
    { id: "node5", size: 30, isLeaf: true },
    { id: "node6", size: 30, isLeaf: true },
    { id: "node7", size: 30, isLeaf: true },
    { id: "node8", size: 30, isLeaf: true },
    { id: "node9", size: 30, isLeaf: true },
    { id: "node10", size: 30, isLeaf: true },
    { id: "node11", size: 30, isLeaf: true },
    { id: "node12", size: 30, isLeaf: true },
    { id: "node13", size: 30, isLeaf: true },
    { id: "node14", size: 30, isLeaf: true },
    { id: "node15", size: 30, isLeaf: true },
    { id: "node16", size: 30, isLeaf: true },
  ],
  edges: [
    { source: "node0", target: "node1" },
    { source: "node0", target: "node2" },
    { source: "node0", target: "node3" },
    { source: "node0", target: "node4" },
    { source: "node0", target: "node5" },
    { source: "node1", target: "node6" },
    { source: "node1", target: "node7" },
    { source: "node2", target: "node8" },
    { source: "node2", target: "node9" },
    { source: "node2", target: "node10" },
    { source: "node2", target: "node11" },
    { source: "node2", target: "node12" },
    { source: "node2", target: "node13" },
    { source: "node3", target: "node14" },
    { source: "node3", target: "node15" },
    { source: "node3", target: "node16" },
  ],
};

const tooltip = new G6.Tooltip({
  offsetX: 10,
  offsetY: 10,
  // v4.2.1 起支持配置 trigger，click 代表点击后出现 tooltip。默认为 mouseenter
  trigger: "click",
  // the types of items that allow the tooltip show up
  // 允许出现 tooltip 的 item 类型
  itemTypes: ["node", "edge"],
  // custom the tooltip's content
  // 自定义 tooltip 内容
  getContent: (e) => {
    const outDiv = document.createElement("div");
    outDiv.style.width = "fit-content";
    //outDiv.style.padding = '0px 0px 20px 0px';
    outDiv.innerHTML = `
      <h4>Custom Content</h4>
      <ul>
        <li>Type: ${e.item.getType()}</li>
      </ul>
      <ul>
        <li>Label: ${e.item.getModel().label || e.item.getModel().id}</li>
      </ul>`;
    return outDiv;
  },
});
onMounted(() => {
  // 确保容器存在
  if (!container.value) return;

  // 延迟初始化确保DOM加载完成
  setTimeout(() => {
    graph = new G6.Graph({
      container: container.value,
      width: container.value.clientWidth,
      height: container.value.clientHeight,
      plugins: [tooltip],
      layout: {
        type: "force",
        preventOverlap: true,
        linkDistance: (d) => {
          if (d.source.id === "node0" || d.target.id === "node0") {
            return 100;
          }
          return 80;
        },
        // nodeStrength: (d) => {
        // if (d.isLeaf) {
        //   return -50;
        // }
        // return -5;
        // },
        // edgeStrength: (d) => {
        // if (d.source.id === "node1" || d.source.id === "node2" || d.source.id === "node3") {
        //   return 0.7;
        // }
        // return 0.1;
        // },
      },
      defaultNode: {
        color: "#5B8FF9",
      },
      modes: {
        default: ["drag-canvas", "drag-canvas", "zoom-canvas"],
      },
    });
    const nodes = data.nodes;
    graph.data({
      nodes,
      edges: data.edges.map(function (edge, i) {
        edge.id = "edge" + i;
        return Object.assign({}, edge);
      }),
    });
    graph.render();

    graph.on("node:dragstart", function (e) {
      graph.layout();
      refreshDragedNodePosition(e);
    });
    graph.on("node:drag", function (e) {
      refreshDragedNodePosition(e);
    });
    graph.on("node:dragend", function (e) {
      e.item.get("model").fx = null;
      e.item.get("model").fy = null;
    });

    if (typeof window !== "undefined")
      window.onresize = () => {
        if (!graph || graph.get("destroyed")) return;
        if (!container || !container.scrollWidth || !container.scrollHeight) return;
        graph.changeSize(container.scrollWidth, container.scrollHeight);
      };

    function refreshDragedNodePosition(e) {
      const model = e.item.get("model");
      model.fx = e.x;
      model.fy = e.y;
    }
    // graph.data(data);
    // graph.render();
    // window.addEventListener("resize", handleResize);
  }, 100);
});

const handleResize = () => {
  if (graph) {
    graph.changeSize(container.value.clientWidth, container.value.clientHeight);
    graph.fitView(); // 重新适配视图
  }
};

onBeforeUnmount(() => {
  if (graph) {
    graph.destroy();
    graph = null;
  }
  window.removeEventListener("resize", handleResize);
});
</script>
<style>
.g6-component-tooltip {
  background-color: rgba(255, 255, 255, 0.8);
  padding: 0px 10px 24px 10px;
  box-shadow: rgb(174, 174, 174) 0px 0px 10px;
}
</style>
