<template>
  <el-scrollbar :height="height">
    <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
      <div style="display: flex; justify-content: space-between; align-items: center">
        <div style="font-weight: bold">拓扑-{{ userInfo.currentTenant.name }}【{{ userInfo.currentTenant.abbreviation }}】</div>
        <div style="width: 80px">
          <!-- <el-slider v-model="value1" :min="100" :max="200" size="small" @input="handleSliderInput" @change="handleSliderChange"></el-slider> -->
        </div>
      </div>
      <div class="content" v-loading="loading">
        <div id="container" ref="graphChartRef"></div>
        <div v-if="contextMenuVisible" :style="{ top: `${menuPosition.y}px`, left: `${menuPosition.x}px`, width: `180px` }" class="context-menu" @click.stop="contextMenuVisible = false" data-close-contextmenu>
          <p style="color: #4c7db4; cursor: pointer" @click.stop="openDevice(devInfo)">{{ devInfo.neName }}</p>
          <p v-for="item in eventSeverityOption" :key="item.label">
            <span class="alarmUrgency" :style="{ color: item.label == devInfo.severity ? item.color : 'red' }">
              {{ item.value == devInfo.severity ? devInfo.severity : "" }}
            </span>
          </p>
          <p>{{ devInfo.address }}</p>
          <p>{{ devInfo.locationName }}</p>
          <p>{{ devInfo.description }}</p>
          <div style="cursor: pointer; display: flex">
            <template v-if="userInfo.hasPermission(资产管理中心_设备_工具权限)">
              <span class="tw-h-fit">
                <el-button type="primary" link v-if="devInfo.icoShow">
                  <Icon class="tw-mx-[2px]" name="local-DeviceMac-line" v-preventReClick @click.stop="oepnVnc(devInfo)" color="var(--el-color-primary)"></Icon>
                </el-button>
              </span>
              <span class="tw-h-fit">
                <el-button type="primary" link @click.stop="ping(devInfo)">
                  <Icon class="tw-mx-[2px]" name="local-DeviceWifi-line" color="var(--el-color-primary)"></Icon>
                </el-button>
              </span>
              <span class="tw-h-fit">
                <el-button type="primary" link @click.stop="routeDevice(devInfo)">
                  <Icon class="tw-mx-[2px]" name="local-SystemShare-line" color="var(--el-color-primary)"></Icon>
                </el-button>
              </span>
            </template>
            <span class="tw-h-fit">
              <el-button type="primary" link @click.stop="preview(devInfo.resourceId)">
                <Icon class="tw-mx-[2px]" name="local-SystemError-warning-line" color="var(--el-color-primary)"></Icon>
              </el-button>
            </span>

            <span class="tw-h-fit">
              <el-button type="primary" link @click.stop="contancts(devInfo.resourceId)">
                <Icon :disabled="true" class="tw-mx-[2px]" name="local-UserUser-3-line" color="var(--el-color-primary)"></Icon>
              </el-button>
            </span>
          </div>
        </div>
      </div>
      <modelAlarm ref="modelAlarmRef"></modelAlarm>
      <deviceDetials ref="deviceDetialsRef"></deviceDetials>
      <deviceContacts ref="deviceContactsRef"></deviceContacts>
      <devicePing ref="devicePingRef"></devicePing>
      <deviceRoute ref="deviceRouteRef"></deviceRoute>
      <noVnc ref="noVncRef" :deviceId="devInfo.resourceId"></noVnc>
    </el-card>
  </el-scrollbar>
</template>

<script setup lang="ts" generic="T extends object">
import { ref, inject, shallowRef, reactive, readonly, computed, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, watch } from "vue";
import * as echarts from "echarts";
import { eventSeverityOption } from "@/views/pages/apis/event";
import { ElTable } from "element-plus";
import Editor from "../../PropertyManage/deviceManage/Editor.vue";
import modelAlarm from "../../PropertyManage/deviceManage/modelAlarm.vue";
import deviceDetials from "@/components/deviceTool/deviceDetials.vue";
import deviceContacts from "@/components/deviceTool/deviceContacts.vue";
import devicePing from "@/components/deviceTool/ping.vue";
import deviceRoute from "@/components/deviceTool/tracerRoute.vue";
import noVnc from "../../PropertyManage/deviceManage/noVnc.vue";
import { getDeviceList as getItemList, LoginMode, getTuopu, getRemoteDesktopByResources } from "@/views/pages/apis/device";
import validPwd from "@/views/pages/alarm_convergence/PropertyManage/passwordWallet/validPwd";
import { getMFAMethods, MFAMethod } from "@/api/system";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import getUserInfo from "@/utils/getUserInfo";
import { 资产管理中心_设备_工具权限 } from "@/views/pages/permission";
import { debounce } from "lodash-es";
defineOptions({});

const ctx = getCurrentInstance();
if (!ctx) throw new Error("Component context initialization failed!");

interface Props {
  width: number;
  height: number;
  title?: string;
}
const router = useRouter();
const userInfo = getUserInfo();

const myGraphData = ref([]);
const contextMenuVisible = ref(false);
const loading = ref(false);
const menuPosition = reactive({ x: 0, y: 0 });
const props = withDefaults(defineProps<Props>(), { title: "" });
const graphChartRef = ref<HTMLElement>();
const myChart = shallowRef<any>();
const _width = inject("width", ref(0));
const _height = inject("height", ref(0));
const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);
// 获取屏幕中心坐标
const devInfo = ref({
  tid: "",
  collectorId: "",
  sort: "",
  neId: "",
  neName: "",
  description: "",
  severity: "",
  severityName: "",
  locationName: "",
  happenTime: "",
  duration: "",
  resourceId: "",
  address: "",
  delay: "",
  children: [],
  icoShow: false,
  allowTypes: [],
});
/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */

/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}

function mounted() {
  getList();
  const handleWindowClick = (event) => {
    if (contextMenuVisible.value && myChart.value) {
      contextMenuVisible.value = true;
    }
  };

  window.addEventListener("click", handleWindowClick);

  onBeforeUnmount(() => {
    window.removeEventListener("click", handleWindowClick);
  });
}

function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */
// 工具方法-------------开始
const tableRef = ref<InstanceType<typeof ElTable>>();
const editorRef = ref<InstanceType<typeof Editor>>();
const modelAlarmRef = ref<InstanceType<typeof modelAlarm>>();
const deviceDetialsRef = ref<InstanceType<typeof deviceDetials>>();
const deviceContactsRef = ref<InstanceType<typeof deviceContacts>>();
const deviceRouteRef = ref<InstanceType<typeof deviceRoute>>();
const devicePingRef = ref<InstanceType<typeof devicePing>>();
const noVncRef = ref<InstanceType<typeof noVnc>>();
function routeDevice(props) {
  deviceRouteRef.value.open(props);
}

async function oepnVnc(row) {
  console.log(row.resourceId,'00000')
  try {
    if (row.config.connectAuthType === LoginMode.ON_CONNECT) {
      const { success: validSuccess } = (await validPwd(false)) as any;
      if (!validSuccess) return;
    } else if (row.config.connectAuthType === LoginMode.REQUIRE_TFA) {
      const { data, message, success } = await getMFAMethods({});
      if (!success) throw new Error(message);
      const isMfa = data.includes(MFAMethod.TOTP);
      if (!isMfa) throw new Error("请开启双因素认证");
      const { success: validSuccess } = (await validPwd(isMfa)) as any;
      if (!validSuccess) return;
    }
    noVncRef.value.device = { ...row };
    noVncRef.value.title = "连接到 " + row.neName;
    noVncRef.value.dialogVisible = true;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}
function openDevice(props) {
  const { href } = router.resolve({
    name: "509596457372745728",
    params: { id: props.resourceId },
    query: {
      // fallback: route.name,
      tenant: sessionStorage.getItem("currentTenantId"),
    },
  });

  window.open(href, props.resourceId);
}
async function ping(props: any) {
  const wasMenuVisible = contextMenuVisible.value;
  try {
    await devicePingRef.value.open(props);
  } finally {
    contextMenuVisible.value = wasMenuVisible;
  }
}

const showProperty = ref(false);
const showService = ref(false);
function setNodeStyle(node) {
  console.log("node :>> ", node);
  // 在option数组中查找与node.severity匹配的项
  const matchedOption = eventSeverityOption.find((item) => item.value === node.severity);

  // 如果找到匹配项，则使用该项的颜色，否则使用默认颜色
  let nodeColor = "";
  if (node.permissions === null || node.permissions === false) {
    nodeColor = "#9e9e9e";
  } else {
    if (node.isCollector) {
      nodeColor = "#555555";
    } else {
      nodeColor = matchedOption ? matchedOption.color : "#d3d3d3"; // 默认颜色是灰色
    }
  }
  return {
    color: nodeColor, // 设置节点的颜色
  };
}

function contancts(id) {
  // deviceId.value = id;
  deviceContactsRef.value.dialogFormVisible = true;
  deviceContactsRef.value.open(id);
}
function preview(id) {
  deviceDetialsRef.value.open(id);
  deviceDetialsRef.value.dialogFormVisible = true;
}
function getList() {
  loading.value = true;
  const screenCenterX = Number(width.value == 0 ? 1612 : width.value) / 2;
  const screenCenterY = Number(height.value == 0 ? 752 : height.value) / 2;
  const paramsSms = {
    containerId: userInfo.currentTenant.containerId,
    queryPermissionId: "509623239069138944",
  };
  getTuopu(paramsSms).then((res) => {
    if (res.success) {
      let tuopuArr = res.data;
      getRemoteDesktopByResources({ deviceIds: collectResourceIds(res.data) }).then((res) => {
        if (res.success) {
          let desktopArr = res.data;
          myGraphData.value = mergeArraysWithChildren(tuopuArr, desktopArr);
          var listdata = [];
          var linksdata = [];
          var currentIndex = 0; // 当前节点索引
          function addNode(node, level, parentIndex = null) {
            listdata.push({
              id: currentIndex,
              name: node.neName || node.collectorName,
              severity: node.severity,
              permissions: node.permissions,
              isCollector: node.isCollector,
              level: level,
              category: "roundRect", // 根据层级设置类别
              draggable: "true",
              label: {
                normal: {
                  show: true, // 显示标签
                  position: "top", // 标签位置设置为上方
                  fontSize: 22, // 设置字体大小
                  fontWeight: "bold", // 可选：加粗字体
                  // fontStyle: 'italic',      // 可选：斜体字体
                },
              },
            });
            // 如果有父节点，添加到链接
            if (parentIndex !== null) {
              linksdata.push({
                source: parentIndex,
                target: currentIndex,
              });
            }
            return currentIndex++;
          }
          function processChildren(node, level, parentIndex) {
            if (node.children && node.children.length > 0) {
              node.children.forEach((child) => {
                const childIndex = addNode(child, level, parentIndex);
                processChildren(child, level + 1, childIndex);
              });
            }
          }

          // 处理根节点
          myGraphData.value.forEach((node) => {
            const rootIndex = addNode(node, 0); // 根节点
            processChildren(node, 1, rootIndex); // 递归处理子节点
          });

          myChart.value = echarts.init(graphChartRef.value!);
          myChart.value.setOption({
            series: [
              {
                name: "知识图谱",
                type: "graph",
                layout: "force",
                force: {
                  repulsion: 400,
                  gravity: 0.1,
                  edgeLength: 15,
                  layoutAnimation: true,
                },
                data: listdata.map((node) => ({
                  ...node,
                  // symbolSize: node.level === 0 ? 50 : 20, // 根节点大，子节点小
                  symbolSize: 30, // 根节点大，子节点小
                  // symbol: node.level === 0 ? "roundRect" : "circle", // 根节点圆角矩形，子节点圆形
                  symbol: "circle", // 根节点圆角矩形，子节点圆形
                  itemStyle: {
                    color: setNodeStyle(node).color,
                  },
                  // 如果该节点是中心节点，可以指定固定位置
                  x: node.isCollector ? screenCenterX : undefined, // 如果是中心节点，设置其固定位置在 (0, 0)
                  y: node.isCollector ? screenCenterY : undefined, // 同理，y 坐标也设置为 0
                  fixed: node.isCollector ? false : false, // 设置该节点固定位置
                })),
                links: linksdata,
                categories: [
                  {
                    name: "父节点",
                    symbol: "rect",
                  },
                  {
                    name: "层级二",
                    symbol: "circle",
                  },
                  {
                    name: "层级三",
                    symbol: "triangle",
                  },
                ],
                roam: true,
                label: {
                  normal: {
                    show: true,
                    position: "bottom",
                    formatter: "{b}",
                    fontSize: 50,
                    fontStyle: "bold",
                  },
                },
                lineStyle: {
                  normal: {
                    opacity: 0.9,
                    width: 1.5,
                    curveness: 0,
                  },
                },
              },
            ],
          });

          const box = document.querySelector(".content");
          const resizeObserver = new ResizeObserver(() => {
            myChart.value.resize();
          });
          resizeObserver.observe(box);

          window.onresize = function () {
            myChart.value.resize();
          };
          myChart.value.on("click", (param) => {
            if (param.dataType === "node" && param.data.isCollector == undefined) {
              // 获取点击的节点名称
              const nodeName = param.data.name;
              // 使用递归查找节点
              const result = findNodeRecursively(myGraphData.value, nodeName);
              if (result) {
                console.log("result :>> ", result.permissions);
                if (result.permissions) {
                  contextMenuVisible.value = true;
                } else {
                  ElMessage.warning("您没有权限!");
                }
                menuPosition.x = param.event.offsetX + 50;
                menuPosition.y = param.event.offsetY;
                devInfo.value = result;
              }
            } else {
              contextMenuVisible.value = false;
            }
          });
          window.addEventListener("click", (event) => {
            if (contextMenuVisible.value) {
              const chartElement = myChart.value.getDom(); // 获取图表 DOM 元素
              // 判断点击是否发生在图表之外
              if (!chartElement.contains(event.target)) {
                contextMenuVisible.value = true; // 关闭右键菜单
              }
            }
          });
          loading.value = false;
        } else {
          ElMessage.error(JSON.parse(res.data)?.message);
        }
      });
    }
  });
}
// 添加 slider 的 change 事件处理函数
// const value1 = ref(100); // 初始缩放值 (100%)
const baseFontSize = 12; // 定义基础字体大小
// const graphChartRef = ref<HTMLElement>();
// const myChart = shallowRef<echarts.ECharts>();
const value1 = ref(100); // 初始缩放值 (100%)

// 实时响应滑块变化
function handleSliderInput(value: number) {
  updateChartSize(value);
}

// 防抖处理最终调整
const handleSliderChange = debounce((value: number) => {
  updateChartSize(value);
}, 300);

function updateChartSize(value: number) {
  if (!myChart.value) return;

  // 计算缩放后的字体大小
  const scaledFontSize = Math.max(8, baseFontSize * (value / 100)); // 最小8px

  const option = {
    series: [
      {
        type: "graph",
        symbolSize: value / 3,
        roam: true, // 确保可拖动
        draggable: true, // 确保节点可拖动
        data: myChart.value.getOption().series[0].data.map((node) => ({
          ...node,
          symbolSize: value / 3,
          label: {
            ...(node.label || {}), // 保留原有标签配置
            fontSize: scaledFontSize,
          },
        })),
        links: myChart.value.getOption().series[0].links,
        layout: "force",
        force: {
          repulsion: 400 * (value / 100), // 力导向参数也按比例调整
          gravity: 0.1 * (value / 100),
          edgeLength: 15 * (value / 100),
        },
        label: {
          show: true,
          position: "bottom",
          fontSize: scaledFontSize,
          fontWeight: "bold",
        },
        lineStyle: {
          width: 1.5 * (value / 100), // 线条也按比例调整
        },
      },
    ],
  };

  myChart.value.setOption(option, {
    replaceMerge: ["series"],
  });
}
function findNodeRecursively(data, targetAddress) {
  for (let node of data) {
    // 检查当前节点是否匹配
    if (node.neName === targetAddress) {
      return node;
    }
    // 如果有子节点，递归检查
    if (node.children && node.children.length > 0) {
      const result = findNodeRecursively(node.children, targetAddress);
      if (result) {
        return result;
      }
    }
  }
  return null;
}
// 获取所有设备id
function collectResourceIds(data) {
  let resourceIds = [];
  data.forEach((node) => {
    if (node.resourceId) {
      resourceIds.push(node.resourceId);
    }
    if (node.children && node.children.length > 0) {
      resourceIds = resourceIds.concat(collectResourceIds(node.children));
    }
  });
  return resourceIds;
}
function mergeArraysWithChildren(array1, array2) {
  return array1.map((item1) => {
    if (!item1.resourceId) {
      if (item1.children && item1.children.length > 0) {
        item1.children = mergeArraysWithChildren(item1.children, array2);
      }
      return item1;
    }
    const matchingItem = array2.find((item2) => item2.resourceId === item1.resourceId);
    if (matchingItem) {
      const mergedItem = {
        ...item1,
        icoShow: matchingItem.icoShow,
        success: matchingItem.success,
        allowTypes: matchingItem.allowTypes,
      };
      if (item1.children && item1.children.length > 0) {
        mergedItem.children = mergeArraysWithChildren(item1.children, array2);
      }
      return mergedItem;
    }
    return item1;
  });
}

// 工具方法------------------------结束
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
defineExpose({});

beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ beforeMount, ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ beforeMount, ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ beforeMount, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
</script>

<style scoped lang="scss">
.content {
  width: 100%;
  min-width: calc(100vw - 340px);
  height: 100%;
  overflow-x: auto;
}
#container {
  width: 100%;
  height: 100%;
}
.context-menu {
  font-weight: bold;
  position: absolute;
  background-color: white;
  border: 1px solid #ddd;
  list-style: none;
  padding: 5px;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
}

.context-menu li {
  padding: 5px 10px;
  cursor: pointer;
}

.context-menu li:hover {
  background-color: #f0f0f0;
}
</style>
