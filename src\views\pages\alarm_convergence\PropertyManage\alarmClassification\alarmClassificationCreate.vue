<template>
  <div>
    <el-dialog :title="title" v-model="dialogFormVisible" :before-close="handleClose" width="50vw">
      <el-form :model="form" :rules="rules" ref="ruleForm">
        <el-form-item label="名称:" :label-width="formLabelWidth" prop="name">
          <el-input v-model="form.name" autocomplete="off" placeholder="请输入告警分类名称"></el-input>
        </el-form-item>
        <el-form-item label="描述:" :label-width="formLabelWidth" prop="description">
          <el-input type="textarea" v-model="form.description" autocomplete="off" :rows="2" placeholder="请输入描述"></el-input>
        </el-form-item>
        <el-form-item v-show="!form.id" :label="`选择安全目录`" tooltip="" prop="" :rules="[buildValidatorData({ name: 'required', title: `请选择安全目录` })]" style="margin-top: 10px">
          <treeAuth ref="treeAuthRef" :treeStyle="treeStyle"></treeAuth>
        </el-form-item>
        <!-- <el-form-item label="告警分类:" :label-width="formLabelWidth" prop="description">

            </el-form-item> -->
        <!-- <el-form-item label="是否为一个报告组:" :label-width="formLabelWidth" prop="report">
              <el-checkbox v-model="form.report"></el-checkbox>
            </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel()">取 消</el-button>
          <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { defineComponent } from "vue";
import { ElMessage, ElMenuItem } from "element-plus";
import { addAlarmClassification, editAlarmClassification } from "@/views/pages/apis/alarmClassification";
import treeAuth from "@/components/treeAuth/index.vue";
import { buildValidatorData, validatorPattern } from "@/utils/validate";
export default defineComponent({
  name: "supplierCreate",
  components: {
    treeAuth,
  },
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      form: {
        name: "",
        description: "",
      },
      rules: {
        name: [{ required: true, message: "请输入告警名称", trigger: "blur" }],
      },
      title: "",
      checked: false,
      dialogFormVisible: false,
      formLabelWidth: "130px",
      type: "",
      buildValidatorData: buildValidatorData,
      treeStyle: {
        width: "300px",
        height: "150px",
      },
    };
  },
  watch: {
    dialog(val) {
      // this.$refs["ruleForm"].clearValidate();
      this.dialogFormVisible = val;
      this.$nextTick(() => {
        // console.log(this.$refs.treeAuthRef);
        this.$refs.treeAuthRef.getSafeContaine();
      });
    },
    type(val) {
      if (val === "add") {
        for (var key in this.form) {
          this.form[key] = null;
        }
      }
      // console.log(this.form);
    },
  },
  created() {
    // console.log(this.$props, 5555);
  },
  methods: {
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.type === "add") {
            if (!this.$refs.treeAuthRef?.treeItem?.id) {
              ElMessage.error("请选择安全目录");
              return;
            }
            addAlarmClassification({ ...this.form, containerId: this.$refs.treeAuthRef?.treeItem?.id }).then((res) => {
              if (res.success) {
                ElMessage.success("新增成功");
                this.$emit("dialogClose", false);
                this.$refs[formName].resetFields();
              } else {
                this.$emit("dialogClose", false);
                this.$refs[formName].resetFields();
                ElMessage.error(JSON.parse(res.data)?.message + "操作失败");
              }
            });
          } else {
            editAlarmClassification({ ...this.form, containerId: this.$refs.treeAuthRef?.treeItem?.id }).then((res) => {
              if (res.success) {
                ElMessage.success("修改成功");
                this.$emit("dialogClose", false);
                this.$refs[formName].resetFields();
              } else {
                this.$emit("dialogClose", false);
                this.$refs[formName].resetFields();
                ElMessage.error(JSON.parse(res.data)?.message + "操作失败");
              }
            });
          }
          this.dialogFormVisible = false;
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      // this.dialogFormVisible = false;
    },
    cancel() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
    },
    blurChange(data) {
      this.tags.push(data);
    },
    tagClose(index) {
      this.tags.splice(index, 1);
    },
  },
  expose: ["form", "title", "checked", "dialogFormVisible", "type"],
});
</script>
