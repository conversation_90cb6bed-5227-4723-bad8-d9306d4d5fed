<template>
  <div style="height: 100%">
    <el-row :gutter="20" style="height: 100%">
      <el-col :span="5" style="height: 100%">
        <el-card style="height: 100%">
          <template #header>
            <div>
              <span>模型（{{ curruntModel }}）</span>
            </div>
          </template>
          <template #default>
            <div>
              <el-input v-model="modelName" @clear="getServiceNumberList()" clearable placeholder="请输入关键字" @keyup.enter="getServiceNumberList()">
                <template #append>
                  <el-button :icon="Search" @click="getServiceNumberList()" />
                </template>
              </el-input>
            </div>
            <el-scrollbar v-loading="loading" style="height: calc(100vh - 280px); overflow: auto">
              <div v-for="item in modelList" :key="item.indet">
                <el-button type="text" @click="selModel(item)">{{ item.name }}</el-button>
              </div>
            </el-scrollbar>
          </template>
        </el-card>
      </el-col>
      <el-col :span="19" style="height: 100%">
        <!-- <SmsTemplate></SmsTemplate> -->
        <el-card style="height: 100%" class="tablecell">
          <page-template :search-span="24" :showPaging="true" v-model:current-page="paging.pageNumber" v-model:page-size="paging.pageSize" :total="paging.total" @current-change="getServiceNumberList()" @size-change="getServiceNumberList()">
            <template #left>
              <div>
                <el-switch v-model="active" @change="handleCommand()" inline-prompt active-text="上线设备" inactive-text="非上线设备" />
              </div>
            </template>
            <template #right>
              <span class="tw-h-fit">
                <el-button v-if="userInfo.hasPermission(PERMISSION.group542927661840728064.create)" type="primary" :icon="Plus" @click="handleAdd('add')">{{ $t("glob.add") }}设备</el-button>
              </span>
              <span class="tw-ml-[12px] tw-h-fit">
                <el-button v-if="userInfo.hasPermission(PERMISSION.group542927661840728064.export)" type="primary" :icon="Download" @click="handleExport()">{{ $t("glob.Export") }}</el-button>
              </span>
              <span class="tw-ml-[12px] tw-h-fit">
                <el-button v-if="userInfo.hasPermission(PERMISSION.group542927661840728064.preview)" type="default" :icon="Refresh" @click="handleRefresh()"></el-button>
              </span>
            </template>
            <template #default="{}">
              <el-table :data="resourceList" stripe style="width: 100%; height: calc(100vh - 290px)" border>
                <!-- <el-table-column width="180" prop="name" label="设备名称"> </el-table-column> -->
                <el-table-column v-for="val in headerDetail.fields" :key="val.ident" :prop="val.ident" :label="val.name">
                  <template #default="{ row }">
                    {{ row[val.ident] || "--" }}
                  </template>
                </el-table-column>
                <el-table-column prop="address" label="操作" width="180">
                  <template #header>
                    操作
                    <el-icon @click="setHeader('add')" style="cursor: pointer"><HelpFilled /></el-icon>
                  </template>
                  <template #default="{ row }">
                    <el-button v-if="userInfo.hasPermission(PERMISSION.group542927661840728064.editor)" type="text" @click="handleAdd('edit', row)">编辑</el-button>
                    <el-button v-if="userInfo.hasPermission(PERMISSION.group542927661840728064.editor)" type="text" @click="handleStatus(row)">{{ row.active ? "下线" : "上线" }}</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </page-template>
        </el-card>
      </el-col>
    </el-row>
    <sortDialog :isAdd="sortType" :curruntModelId="curruntModelId" :modelDetail="modelDetail" @confirm="confirmSort" ref="sortRef"></sortDialog>
    <serviceDialog :isAdd="serviceType" :curruntModelId="curruntModelId" :modelDetail="modelDetail" ref="serviceRef" @confirm="confirmService"></serviceDialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import ModelExpand from "@/views/pages/modelExpand/Model.vue";

import { Download, DeleteFilled, Plus, Refresh, HelpFilled } from "@element-plus/icons-vue";
import serviceDialog from "./serviceDialog.vue";
import sortDialog from "./sortDialog.vue";
import moment from "moment";
import { state, dataList, expand, select, current } from "./helper";
// import SmsTemplate from "../deviceManage/SmsTemplate";
import { h } from "vue";
import pageTemplate from "@/components/pageTemplate.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { getUserList, getAllModel, modelManageDetail, getResourcesList, getResourcesHeader, getResourcesExport, editResource } from "@/views/pages/apis/model";
import getUserInfo from "@/utils/getUserInfo";
// import {} from "@/permission";
const userInfo = getUserInfo();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */

// const refs = ref({});
export default {
  name: "deviceManageDetails",
  components: {
    // SmsTemplate,
    pageTemplate,
    serviceDialog,
    sortDialog,
    HelpFilled,
  },
  data() {
    return {
      userInfo,
      modelName: "",
      sortType: "",
      serviceType: "",
      curruntModel: "",
      modelDetail: "",
      headerDetail: "",
      curruntModelId: "",
      active: true,
      loading: false,
      modelList: [],
      dataBox: [],
      options2: [],
      routerId: this.$route.query.id,
      routerName: this.$route.query.name,
      ids: [],
      paging: { pageNumber: 1, pageSize: 10, total: 0 },
      resourceList: [],
      resourceListOld: [],
      errFilePath: "",
      Plus,
      Refresh,
      Download,
    };
  },
  computed: {},

  mounted() {
    this.getServiceNumberList();
    this.getHeaderList();
  },
  methods: {
    //模型关联 新增/编辑事件操作
    confirmSort(bool) {
      this.sortType = "";
      if (bool) {
        this.getHeader();
        this.getResource();
      }
    },
    setHeader(type) {
      this.sortType = type;
      (this.$refs.sortRef as any).dialogVisible = true;
    },
    confirmService(bool) {
      this.serviceType = "";
      if (bool) {
        this.getResource();
      }
    },
    handleCommand() {
      this.getResource();
    },
    //上线，下线
    handleStatus(row) {
      editResource({ modelIdent: row.id, active: row.active ? false : true })
        .then((res) => {
          if (res.success) {
            this.$message.success("操作成功");
            this.getResource();
          } else this.$message.error(JSON.parse(res.data)?.message);
        })
        .catch((err) => {
          this.$message.error(err?.message);
        });
    },
    //新增
    handleAdd(type, row) {
      this.serviceType = type;
      (this.$refs.serviceRef as any).dialogVisible = true;
      if (row) {
        this.resourceListOld.forEach((element) => {
          if (element.id === row.id) {
            (this.$refs.serviceRef as any).form = element;
            (this.$refs.serviceRef as any).form.name = row.name;
            (this.$refs.serviceRef as any).form.monitorSources = row.monitorSources;
            (this.$refs.serviceRef as any).form.unit = row.unit;
            (this.$refs.serviceRef as any).form.description = row.description;
            (this.$refs.serviceRef as any).form.timeZone = row.timeZone;
            (this.$refs.serviceRef as any).form.importance = row.importance;
            (this.$refs.serviceRef as any).form.active = row.active;
            (this.$refs.serviceRef as any).form.onlineTime = row.onlineTime;
          }
        });
      }
    },
    //导出
    handleExport() {
      getResourcesExport({ pageNumber: 1, pageSize: 1000, modelIdent: this.curruntModelId }).then((res) => {
        const link = document.createElement("a");
        let blob = new Blob([res.data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
        });
        link.style.display = "none";
        link.href = URL.createObjectURL(blob);
        link.setAttribute("download", this.errFilePath);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      });
    },
    handleRefresh() {
      this.getResource();
    },
    selModel(val) {
      this.curruntModel = val.name;
      this.curruntModelId = val.ident;
      this.getDetail();
      this.getHeader();
    },
    getHeaderList() {
      getUserList({
        pageNumber: 1,
        pageSize: 1000,
      }).then((res) => {
        if (res.success) {
          this.options2 = res.data;
        }
      });
    },
    //模型列表
    getServiceNumberList() {
      let params = {
        key: this.modelName || "",
        // enabled: '',
      };
      getAllModel(params)
        .then(({ success, message, data, total }) => {
          if (!success) throw new Error(message);
          this.modelList = data;
          if (data.length > 0) {
            this.curruntModel = this.routerName || data[0].name || "";
            this.curruntModelId = this.routerId || data[0].ident || "";
            this.getDetail();
            this.getHeader();
          }
        })
        .catch((e) => {
          this.$message.error(e?.message);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //模型详情
    getDetail() {
      modelManageDetail({ ident: this.curruntModelId }).then((res) => {
        if (res.success) {
          this.modelDetail = res.data;
          this.modelDetail.fields.forEach((element) => {
            if (element.type === "BOOL") {
              element.value = false;
            } else if (element.type === "LIST" || element.type === "DATE_TIME" || element.type === "TIME_ZONE" || element.type === "ENUM") {
              element.value = null;
            } else {
              element.value = "";
            }
          });
        }
      });
    },
    //资源表头
    getHeader() {
      getResourcesHeader({ modelIdent: this.curruntModelId }).then((res) => {
        if (res.success) {
          this.headerDetail = res.data;
          this.getResource();
        }
      });
    },
    //设备列表
    getResource() {
      let params = {
        pageNumber: this.paging.pageNumber,
        pageSize: this.paging.pageSize,
        modelIdent: this.curruntModelId,
        active: this.active,
      };
      getResourcesList(params).then((res) => {
        if (res.success) {
          let arr = res.data;
          this.resourceListOld = JSON.parse(JSON.stringify(res.data));
          arr.forEach((item) => {
            this.ids.push(item.id);
            this.modelDetail.fields.forEach((value) => {
              for (let index in item.config) {
                if (index === value.ident) {
                  switch (value.type) {
                    case "SHORT_CHARACTER":
                      break;
                    case "LONG_CHARACTER":
                      break;
                    case "DIGITAL":
                      break;
                    case "FLOAT":
                      break;
                    case "ENUM":
                      break;
                    case "DATE_TIME":
                      if (item.config[index]) {
                        let str = item.config[index];
                        item.config[index] = str ? moment(str, "x").format("yyyy-MM-DD HH:mm:ss") : "";
                      }
                      break;
                    case "TIME_ZONE":
                      break;
                    case "USER":
                      let arr = JSON.parse(item.config[index]);
                      item.config[index] = [];
                      this.options2.forEach((element) => {
                        if (arr.includes(element.id)) {
                          item.config[index].push(element.name);
                        }
                      });
                      break;
                    case "BOOL":
                      break;
                    case "LIST":
                      break;
                    default:
                      break;
                  }
                }
              }
            });
            item = Object.assign(item, item.config);
          });
          this.resourceList = arr;
          this.paging.total = Number(res.total);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.tablecell v-deep .el-table .el-table__body td .cell:empty::after {
  content: "-";
}
</style>
