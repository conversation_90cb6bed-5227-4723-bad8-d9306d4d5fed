<!--  -->
<template>
  <div>
    <el-dialog :title="title" v-model="dialogVisible" width="45%" :before-close="cancel">
      <el-form :model="form" :inline="true" label-position="left" :rules="rules" ref="serviceFormRef" style="max-height: calc(70vh - 116px); overflow: auto">
        <el-form-item label="名称" style="width: 42%" :label-width="formLabelWidth" prop="name">
          <el-input style="width: 202px" placeholder="请输入内容" v-model="form.name"> </el-input>
        </el-form-item>
        <el-form-item label="序列号" style="width: 42%" :label-width="formLabelWidth" prop="config.serialNumbers">
          <el-input style="width: 202px" placeholder="请输入内容" v-model="form.config.serialNumbers"> </el-input>
        </el-form-item>
        <el-form-item label="监控源" style="width: 42%" :label-width="formLabelWidth" prop="monitorSources">
          <el-select v-model="form.monitorSources" style="width: 202px" clearable placeholder="请选择">
            <el-option v-for="item in monitorSourcesList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单位" style="width: 42%" :label-width="formLabelWidth" prop="unit">
          <el-input style="width: 202px" placeholder="请输入内容" v-model="form.unit"> </el-input>
        </el-form-item>
        <el-form-item label="描述" style="width: 42%" :label-width="formLabelWidth" prop="description">
          <el-input type="textarea" :rows="2" style="width: 202px" placeholder="请输入内容" v-model="form.description"> </el-input>
        </el-form-item>
        <el-form-item label="时区" style="width: 42%" :label-width="formLabelWidth" prop="timeZone">
          <el-select v-model="form.timeZone" style="width: 202px" clearable placeholder="请选择">
            <el-option v-for="item in timeZoneList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="重要性" style="width: 42%" :label-width="formLabelWidth" prop="importance">
          <el-select v-model="form.importance" style="width: 202px" clearable placeholder="请选择">
            <el-option v-for="item in importanceList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否激活" style="width: 42%" :label-width="formLabelWidth" prop="active">
          <el-checkbox v-model="form.active">激活</el-checkbox>
        </el-form-item>
        <el-form-item label="上线时间" style="width: 42%" :label-width="formLabelWidth" prop="onlineTime">
          <el-date-picker style="width: 202px" v-model="form.onlineTime" type="date" value-format="x" placeholder="选择日期"> </el-date-picker>
        </el-form-item>
        <el-form-item :label="val.name" v-for="val in modelDetail.fields" :key="val.ident" :rules="[{ required: val.required, message: val.name + '内容不能为空', trigger: ['change', 'blur'] }]" style="width: 42%" :label-width="formLabelWidth" :prop="val.ident + '__fields'">
          <template #label>
            <div v-if="val.type == 'DIGITAL' || val.type == 'FLOAT'">
              <span>{{ val.name }}</span>
              <el-tooltip class="box-item" effect="dark" v-if="val.helpNote" :content="val.helpNote" placement="top-start">
                <el-icon style="float: right; top: 9px"><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-input v-if="val.type == 'SHORT_CHARACTER'" placeholder="请输入内容" :disabled="title == '新建设备' ? false : val.readonly" style="width: 202px" v-model="val.value" @change="changeFromData($event, val.ident + '__fields')"> </el-input>
          <el-input v-if="val.type == 'LONG_CHARACTER'" type="textarea" :rows="2" placeholder="请输入内容" :disabled="title == '新建设备' ? false : val.readonly" style="width: 202px" @change="changeFromData($event, val.ident + '__fields')" v-model="val.value"> </el-input>
          <div v-if="val.type == 'DIGITAL'">
            <el-input-number @change="changeFromData($event, val.ident + '__fields')" :disabled="title == '新建设备' ? false : val.readonly" :min="min" :max="max" v-model="val.value" label="描述文字"></el-input-number>
            <span style="margin-left: 10px">{{ val.unit }}</span>
          </div>
          <div v-if="val.type == 'FLOAT'">
            <el-input-number @change="changeFromData($event, val.ident + '__fields')" :disabled="title == '新建设备' ? false : val.readonly" :min="minF" :max="maxF" v-model="val.value" :step="0.1" :precision="2" label="描述文字"></el-input-number>
            <span style="margin-left: 10px">{{ val.unit }}</span>
          </div>
          <el-select style="width: 202px" @change="changeFromData($event, val.ident + '__fields')" v-if="val.type == 'ENUM'" :disabled="title == '新建设备' ? false : val.readonly" v-model="val.value" clearable placeholder="请选择">
            <el-option v-for="item in meijuList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
          <el-date-picker style="width: 202px" v-if="val.type == 'DATE_TIME'" v-model="val.value" type="date" :disabled="title == '新建设备' ? false : val.readonly" @change="changeFromData($event, val.ident + '__fields')" value-format="x" placeholder="选择日期"> </el-date-picker>
          <el-select style="width: 202px" @change="changeFromData($event, val.ident + '__fields')" v-if="val.type == 'TIME_ZONE'" :disabled="title == '新建设备' ? false : val.readonly" v-model="val.value" filterable clearable placeholder="请选择">
            <el-option v-for="item in timeZoneList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
          <el-select style="width: 202px" @change="changeFromData($event, val.ident + '__fields')" v-if="val.type == 'USER'" :disabled="title == '新建设备' ? false : val.readonly" v-model="val.value" multiple clearable placeholder="请选择">
            <el-option v-for="item in options2" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
          <el-switch @change="changeFromData($event, val.ident + '__fields')" v-if="val.type == 'BOOL'" v-model="val.value" :disabled="title == '新建设备' ? false : val.readonly" active-color="#13ce66" inactive-color="#ff4949"> </el-switch>
          <el-select style="width: 202px" @change="changeFromData($event, val.ident + '__fields')" v-if="val.type == 'LIST'" :disabled="title == '新建设备' ? false : val.readonly" v-model="val.value" clearable placeholder="请选择">
            <el-option v-for="item in tableList" :key="item" :label="item" :value="item"> </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import mixin from "./mixin";
import { Zone } from "@/utils/zone";
import { addResource, editResource, getUserList } from "@/views/pages/apis/model";
import { QuestionFilled } from "@element-plus/icons-vue";
export default {
  mixins: [mixin],
  props: {
    isAdd: {
      type: String,
      default: "",
    },
    curruntModelId: {
      type: String,
      default: "",
    },
    dataBox: {
      type: Array,
      default: [],
    },
    modelDetail: {
      type: Object,
      default: {},
    },
  },
  emits: ["confirm"],
  components: {
    QuestionFilled,
  },
  data() {
    return {
      form: {
        name: "",
        monitorSources: "",
        unit: "",
        description: "",
        timeZone: "",
        importance: "",
        active: "",
        onlineTime: "",
        modelIdent: "",
        id: "",
        config: {
          serialNumbers: "",
        },
      },
      monitorSourcesList: [
        { value: "NIGHTINGALE_V6", label: "夜莺V6" },
        { value: "NETCARE_V6", label: "NetCareV6" },
        { value: "OTHER", label: "未知" },
      ],
      importanceList: [
        { value: "High", label: "至关重要的" },
        { value: "Medium", label: "中" },
        { value: "Low", label: "低" },
        { value: "None", label: "无" },
        { value: "Unknown", label: "未知" },
      ],
      formLabelWidth: "auto",
      dialogVisible: false,
      rules: {
        name: [{ required: true, message: "设备名称不能为空", trigger: "blur" }],
        // targetModel: [{ required: true, message: "请选择", trigger: "change" }],
      },
      numMax: "",
      meijuList: [],
      timeZoneList: [],
      options2: [],
      tableList: [],
      min: "",
      minF: "",
      max: "",
      maxF: "",
      title: "",
    };
  },
  watch: {
    isAdd(val) {
      this.title = val == "add" ? "新建设备" : "修改设备";
      this.meijuList = [];
      this.tableList = [];
      this.timeZoneList = [];
      for (let key in Zone) {
        this.timeZoneList.push({ id: key, name: Zone[key] });
      }
      if (val === "edit") {
        this.form.modelIdent = this.form.id;
        this.modelDetail.fields.forEach((element) => {
          for (let index in this.form.config) {
            if (index === element.ident) {
              this.form[[index] + "__fields"] = this.form.config[index];
              switch (element.type) {
                case "SHORT_CHARACTER":
                  element.value = this.form.config[index];
                  break;
                case "LONG_CHARACTER":
                  element.value = this.form.config[index];
                  break;
                case "DIGITAL":
                  this.max = JSON.parse(element.option).max;
                  this.min = JSON.parse(element.option).min;
                  element.value = this.form.config[index];
                  break;
                case "FLOAT":
                  this.maxF = JSON.parse(element.option).max;
                  this.minF = JSON.parse(element.option).min;
                  element.value = this.form.config[index];
                  break;
                case "ENUM":
                  this.meijuList = JSON.parse(element.option).nodes;
                  element.value = this.form.config[index];
                  break;
                case "DATE_TIME":
                  // let date = this.form.config[index]
                  // element.value = new Date(date).getTime()
                  element.value = Number(this.form.config[index]);
                  break;
                case "TIME_ZONE":
                  element.value = this.form.config[index];
                  break;
                case "USER":
                  // let arr = this.form.config[index]
                  // let arr2 = []
                  // this.options2.forEach(val => {
                  //   if(arr.includes(val.name)){
                  //     arr2.push(val.id)
                  //   }
                  // });
                  // element.value = arr2
                  element.value = JSON.parse(this.form.config[index]);
                  break;
                case "BOOL":
                  element.value = JSON.parse(this.form.config[index]);
                  break;
                case "LIST":
                  this.tableList = JSON.parse(element.option).values;
                  element.value = this.form.config[index];
                  break;
                default:
                  break;
              }
            }
          }
        });
      } else {
        this.form.modelIdent = this.curruntModelId;
        this.form.name = "";
        this.modelDetail.fieldGroups.forEach((item) => {
          item.fields = [];
          this.modelDetail.fields.forEach((element) => {
            if (element.type === "BOOL") {
              this.form[element.ident + "__fields"] = false;
              element.value = false;
            } else if (element.type === "LIST" || element.type === "DATE_TIME" || element.type === "TIME_ZONE" || element.type === "ENUM") {
              element.value = null;
            } else {
              element.value = "";
            }
            if (item.ident === element.groupIdent) {
              item.fields.push(element);
            }
            switch (element.type) {
              case "SHORT_CHARACTER":
                break;
              case "LONG_CHARACTER":
                break;
              case "DIGITAL":
                this.max = JSON.parse(element.option).max;
                this.min = JSON.parse(element.option).min;
                this.form[element.ident + "__fields"] = this.min;
                element.value = this.min;
                break;
              case "FLOAT":
                this.maxF = JSON.parse(element.option).max;
                this.minF = JSON.parse(element.option).min;
                this.form[element.ident + "__fields"] = this.minF;
                element.value = this.minF;
                break;
              case "ENUM":
                this.meijuList = JSON.parse(element.option).nodes;
                break;
              case "DATE_TIME":
                break;
              case "TIME_ZONE":
                break;
              case "USER":
                break;
              case "BOOL":
                break;
              case "LIST":
                this.tableList = JSON.parse(element.option).values;
                break;
              default:
                break;
            }
          });
        });
      }
    },
  },
  created() {
    getUserList({
      pageNumber: 1,
      pageSize: 1000,
    }).then((res) => {
      if (res.success) {
        this.options2 = res.data;
      }
    });
  },

  methods: {
    cancel() {
      this.dialogVisible = false;
      this.$emit("confirm", false);
      this.$refs["serviceFormRef"].resetFields();
      this.$refs["serviceFormRef"].clearValidate();
    },
    changeFromData(val, key) {
      this.form[key] = val;
    },
    submit() {
      this.$refs["serviceFormRef"].validate((valid) => {
        if (valid) {
          this.modelDetail.fields.forEach((element) => {
            if (element.type === "USER") {
              this.form.config[element.ident] = JSON.stringify(element.value);
            } else {
              this.form.config[element.ident] = element.value;
            }
          });
          if (this.isAdd === "add") {
            addResource({ ...this.form })
              .then((res) => {
                // console.log(res);
                if (res.success) {
                  this.$message.success("操作成功");
                  this.$refs["serviceFormRef"].resetFields();
                  this.dialogVisible = false;
                  this.$emit("confirm", true);
                } else this.$message.error(JSON.parse(res.data)?.message);
              })
              .catch((err) => {
                this.$message.error(err?.message);
              });
          } else {
            editResource({ ...this.form })
              .then((res) => {
                if (res.success) {
                  this.$message.success("操作成功");
                  this.$refs["serviceFormRef"].resetFields();
                  this.dialogVisible = false;
                  this.$emit("confirm", true);
                } else this.$message.error(JSON.parse(res.data)?.message);
              })
              .catch((err) => {
                this.$message.error(err?.message);
              });
          }
        }
      });
    },
  },
  expose: ["dialogVisible", "title", "form"],
};
</script>
<style scoped lang="scss"></style>
