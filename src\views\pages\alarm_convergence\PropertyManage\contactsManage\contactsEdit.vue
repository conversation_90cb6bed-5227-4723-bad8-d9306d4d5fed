<template>
  <el-dialog :title="isView ? i18n.t('contact.View Contact') : isEdit ? i18n.t('contact.Edit Contact') : i18n.t('contact.New Contact')" v-model="dialogFormVisible" :before-close="beforeClose" append-to-body>
    <el-scrollbar :height="400">
      <el-form ref="form" :model="form" label-width="145px" :rules="rules" :disabled="isView" :class="isView ? 'form-item' : ''">
        <el-row>
          <el-col :sm="24" :lg="24" style="margin-bottom: 20px">
            <h3 class="form-title">
              {{ $t("contact.Basic information") }}
              <p></p>
            </h3>
          </el-col>
          <el-col :sm="24" :lg="12">
            <el-form-item :label="i18n.t('contact.Name')" prop="name" :style="isView ? { margin: '0' } : {}">
              <template v-if="isView">
                <el-text truncated :title="form.name">{{ form.name }}</el-text>
              </template>
              <el-input v-else v-model="form.name" :placeholder="i18n.t('contact.Please enter the name')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :sm="24" :lg="12">
            <el-form-item :label="i18n.t('contact.Is Active')" prop="active" :style="isView ? { margin: '0' } : {}">
              <template v-if="isView">
                <el-tag v-if="form.active" type="success">{{ $t("contact.Yes") }}</el-tag>
                <el-tag v-else type="info">{{ $t("contact.Not") }}</el-tag>
              </template>
              <el-radio-group v-else v-model="form.active" class="ml-4">
                <el-radio :value="true"> {{ $t("contact.Yes") }}</el-radio>
                <el-radio :value="false"> {{ $t("contact.Not") }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :sm="24" :lg="12">
            <el-form-item :label="i18n.t('contact.Mobile')" prop="smsPhone">
              <template v-if="isView">
                {{ form.smsPhone }}
              </template>
              <el-input v-else v-model="form.smsPhone" :placeholder="i18n.t('contact.Please enter your mobile phone number')"></el-input>
            </el-form-item>
          </el-col>

          <el-col :sm="24" :lg="12">
            <el-form-item :label="i18n.t('contact.Fixed phone')" prop="landlinePhone" :style="isView ? { margin: '0' } : {}">
              <template v-if="isView">
                <el-text truncated :title="form.landlinePhone">{{ form.landlinePhone }}</el-text>
              </template>
              <el-input v-else v-model="form.landlinePhone" :placeholder="i18n.t('contact.Please enter the fixed phone number')"></el-input>
            </el-form-item>
          </el-col>

          <el-col :sm="24" :lg="12">
            <el-form-item :label="i18n.t('contact.Email')" prop="email" :style="isView ? { margin: '0' } : {}">
              <template v-if="isView">
                <el-text truncated :title="form.email">{{ form.email }}</el-text>
              </template>
              <el-input v-else v-model="form.email" :placeholder="i18n.t('contact.Please enter the email')"></el-input>
            </el-form-item>
          </el-col>

          <el-col :sm="24" :lg="12">
            <el-form-item :label="i18n.t('contact.Is it VIP')" prop="vip" :style="isView ? { margin: '0' } : {}">
              <template v-if="isView">
                <el-tag v-if="form.vip" type="success">{{ $t("contact.Yes") }}</el-tag>
                <el-tag v-else type="info">{{ $t("contact.Not") }}</el-tag>
              </template>
              <el-switch v-else v-model="form.vip" :active-text="i18n.t('contact.Yes')" :inactive-text="i18n.t('contact.Not')" />
            </el-form-item>
          </el-col>

          <!-- <el-col :sm="24" :lg="12"> </el-col> -->
          <el-col :sm="24" :lg="24" style="margin-bottom: 20px">
            <h3 class="form-title">
              {{ $t("contact.High") }}
              <p></p>
            </h3>
          </el-col>
          <el-row class="many-message">
            <el-col :sm="24" :lg="12">
              <el-form-item :label="i18n.t('contact.Notification language')" prop="language" :style="isView ? { margin: '0' } : {}">
                <template v-if="isView">
                  <el-text
                    truncated
                    :title="
                      localesOption
                        .filter((v) => v.value === form.language)
                        .map((v) => v.label)
                        .join(',')
                    "
                  >
                    {{
                      localesOption
                        .filter((v) => v.value === form.language)
                        .map((v) => v.label)
                        .join(",")
                    }}
                  </el-text>
                </template>
                <el-select v-else v-model="form.language" :placeholder="i18n.t('contact.Please select language')" :style="{ width: '100%' }" filterable ref="select">
                  <el-option v-for="item in localesOption" :key="item.value" :label="item.label" :value="item.value">
                    <!-- <i class="flag-icon" :class="item.icon" /> -->
                    <!-- <img :src="item.icon" :alt="item.label" class="tw-w-[25px]" />
                <span>{{ item.label }}</span> -->
                    <div :style="{ background: `url(${item.icon}) no-repeat left / auto calc(100% - 12px)`, paddingLeft: '30px' }">{{ item.label }}</div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="24" :lg="12">
              <el-form-item :label="i18n.t('contact.Time Zone')" prop="zoneId" :style="isView ? { margin: '0' } : {}">
                <template v-if="isView">
                  <el-text
                    truncated
                    :title="
                      zone
                        .filter((v) => v.zoneId === form.zoneId)
                        .map((v) => v.displayName)
                        .join(',')
                    "
                  >
                    {{
                      zone
                        .filter((v) => v.zoneId === form.zoneId)
                        .map((v) => v.displayName)
                        .join(",")
                    }}
                  </el-text>
                </template>
                <el-select v-else v-model="form.zoneId" :placeholder="i18n.t('contact.Please choose a time zone')" :style="{ width: '100%' }" filterable>
                  <el-option v-for="item in zone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :sm="24" :lg="12">
              <el-form-item :label="i18n.t('contact.SMScall')" prop="mobilePhone" :style="isView ? { margin: '0' } : {}">
                <template v-if="isView">
                  <el-text truncated :title="form.mobilePhone">{{ form.mobilePhone }}</el-text>
                </template>
                <el-input v-else v-model="form.mobilePhone" :placeholder="i18n.t('contact.PleaseenterSMSphonenumber')" @change="handleSetInternationalPhone"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="24"></el-col>

            <el-col :sm="24" :lg="12">
              <el-form-item :label="i18n.t('contact.Internationalphonenumber')" prop="internationalPhone" :style="isView ? { margin: '0' } : {}">
                <template v-if="isView">
                  <el-tag v-if="form.internationalPhone" type="success">{{ $t("contact.Yes") }}</el-tag>
                  <el-tag v-else type="info">{{ $t("contact.Not") }}</el-tag>
                </template>
                <el-radio-group v-else v-model="form.internationalPhone" class="ml-4">
                  <el-radio :value="true"> {{ $t("contact.Yes") }}</el-radio>
                  <el-radio :value="false"> {{ $t("contact.Not") }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :sm="24" :lg="12">
              <el-form-item :label="i18n.t('contact.Sms enabled')" prop="smsEnabled" :style="isView ? { margin: '0' } : {}">
                <template v-if="isView">
                  <el-tag v-if="form.smsEnabled" type="success">{{ $t("contact.Yes") }}</el-tag>
                  <el-tag v-else type="info">{{ $t("contact.Not") }}</el-tag>
                </template>
                <el-radio-group v-else v-model="form.smsEnabled" class="ml-4">
                  <el-radio :value="true"> {{ $t("contact.Yes") }}</el-radio>
                  <el-radio :value="false"> {{ $t("contact.Not") }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item :label="i18n.t('contact.Title')" prop="position" :style="isView ? { margin: '0' } : {}">
                <template v-if="isView">
                  <el-text truncated :title="form.position">{{ form.position }}</el-text>
                </template>
                <el-input v-else v-model="form.position" :placeholder="i18n.t('contact.Please enter the title')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="i18n.t('contact.Description')" prop="note" :style="isView ? { margin: '0' } : {}">
                <template v-if="isView">
                  <el-text :truncated="false" :title="form.note">{{ form.note }}</el-text>
                </template>
                <el-input v-else type="textarea" v-model="form.note" :placeholder="i18n.t('contact.Please enter the Description')"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item :label="i18n.t('contact.External Id')" prop="externalId" :style="isView ? { margin: '0' } : {}">
                <template v-if="isView">
                  <el-text truncated :title="form.externalId">{{ form.externalId }}</el-text>
                </template>
                <el-input v-else v-model="form.externalId" :placeholder="i18n.t('contact.Please enter the External Id')"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="dialog-footer" v-if="!isView">
        <el-button @click="beforeClose">{{ $t("glob.NO") }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t("glob.OK") }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent } from "vue";
import { ElMessage, ElMenuItem } from "element-plus";

import zone from "@/views/pages/common/contactsZone.json";
import { addContacts, modContacts, getContactInfoById } from "@/views/pages/apis/contacts";
import { localesOption } from "@/api/locale.ts";
import { checkEmail } from "@/views/pages/common/validate";
// import treeAuth from "@/components/treeAuth/index.vue";
import { getTenantInfo } from "@/views/pages/apis/tenant";
import { useI18n } from "vue-i18n";
export default defineComponent({
  components: {},
  emits: ["refresh"],
  data() {
    return {
      i18n: useI18n(),
      zone,
      localesOption,
      dialogFormVisible: false,
      form: {
        language: "",
        active: true,
        zoneId: "",
      },
      isEdit: false,
      currentId: "",
      isView: false,
      containerId: "",
    };
  },
  computed: {
    rules() {
      return {
        name: [
          {
            required: true,
            message: "请输入姓名",
            trigger: ["blur", "change"],
          },
        ],
        email: [{ required: false, message: "请输入邮箱", trigger: ["blur", "change"] }],
        mobilePhone: [
          {
            validator: (rule, value, callback) => {
              // if (this.form.mobilePhone) {
              //   if (this.handleValidateChinesePhone(this.form.mobilePhone) || this.handleValidateInternationalPhone(this.form.mobilePhone)) {
              //     callback();
              //   } else callback(new Error("无效电话"));
              // } else callback();
              callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        smsPhone: [
          // {
          //   validator: (rule, value, callback) => {
          //     if (this.form.smsPhone) {
          //       if (this.handleValidateChinesePhone(this.form.smsPhone) || this.handleValidateInternationalPhone(this.form.smsPhone)) {
          //         callback();
          //       } else callback(new Error("无效电话"));
          //     } else callback();
          //   },
          //   trigger: ["blur", "change"],
          // },
        ],
      };
    },
  },
  created() {
    this.runningInit();
    this.getInfo();
  },
  methods: {
    // 获取当前客户信息
    async getInfo() {
      await getTenantInfo({})
        .then(({ success, data }) => {
          if (success) {
            this.form.language = data.language;
          } else ElMessage.error(JSON.parse(data)?.message || "操作失败");
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        });
    },
    /**
     * @desc 校验国内电话
     * @param phone
     */
    handleValidateChinesePhone(phone) {
      // 手机号码正则表达式
      const mobileRegex = /(^(?:\+86|0086)\s?\d)|(^\d+$)/;

      // // 固定电话正则表达式（带区号）
      // const landlineRegex = /^\d{3,4}-\d{7,8}$/;

      return mobileRegex.test(phone) /* || landlineRegex.test(phone) */;
    },

    /**
     * @desc 校验国际电话
     * @param phone
     */
    handleValidateInternationalPhone(phone) {
      // 国际电话号码正则表达式
      const internationalRegex = /^(?:\+|00)\s?\d/;

      return internationalRegex.test(phone);
    },

    /**
     * @desc 判断是否国际电话
     * @param v
     */
    handleSetInternationalPhone(v) {
      /* 国内 */ if (/^(\s?)+(?:\+86|0086)\s?.+$/.test(v)) this.form.internationalPhone = false;
      /* 国际 */ else if (/^(\s?)+(?:\+|00)\s?.+$/.test(v)) this.form.internationalPhone = true;
      /* 国内 */ else if (/(^\d+$)/.test(v)) this.form.internationalPhone = false;
      /* 无效电话 */ else this.form.internationalPhone = "";
    },

    async runningInit() {
      (async (req) => {
        const { success, message, data } = await getTenantInfo(req);
        if (!success) throw Object.assign(new Error(message), { success, data });

        this.containerId = data.containerId;
        this.form.zoneId = data.zoneId || "China Standard Time";
      })({});
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) return false;
        const params = {
          ...this.form,
          containerId: this.containerId,
        };
        (this.isEdit ? modContacts : addContacts)(params, this.currentId).then(({ success, data }) => {
          if (success) {
            this.$emit("refresh");
            ElMessage.success("操作成功");
            this.beforeClose();
          } else ElMessage.error(JSON.parse(data)?.message || "操作失败");
        });
      });
    },
    open(row = {}, isView = false) {
      this.isEdit = !!row.id;
      this.isView = isView;
      if (this.isEdit) {
        this.currentId = row.id;
        this.getDetail(this.currentId);
      }
      this.dialogFormVisible = true;
    },
    getDetail(id) {
      getContactInfoById({ id }).then(({ success, data }) => {
        if (success) {
          this.$nextTick(() => {
            this.form = {
              name: data.name,
              email: data.email,
              landlinePhone: data.landlinePhone,
              mobilePhone: data.mobilePhone,
              smsPhone: data.smsPhone,
              zoneId: data.zoneId,
              vip: data.vip,
              smsEnabled: data.smsEnabled,
              note: data.note,
              externalId: data.externalId,
              language: data.language,
              active: data.active,
              position: data.position,
              internationalPhone: data.internationalPhone,
              acceptEmail: data.acceptEmail,
            };
          });
        } else ElMessage.error(JSON.parse(data)?.message || "联系人信息获取失败");
      });
    },
    beforeClose(done) {
      this.$refs.form.clearValidate();
      this.$refs.form.resetFields();
      if (done instanceof Function) done();
      else this.dialogFormVisible = false;
    },
  },
  expose: ["open", "beforeClose"],
});
</script>

<style lang="scss" scoped>
@import "@/views/pages/assets/style/flag-icons.css";
.form-title {
  font-size: 16px;
  font-weight: 600;
  // margin-bottom: 20px;
  display: flex;
  align-items: center;
  color: #000;

  > p {
    margin-left: 10px;
    flex: 1;
    height: 1px;
    border-bottom: 1px dashed rgba(201, 205, 212, 1);
  }
}
.many-message {
  width: 100%;
  background-color: rgb(247, 248, 250);
  padding: 20px 20px 0px;
  box-sizing: border-box;
}
.form-item {
  :deep(.el-input.is-disabled .el-input__inner) {
    color: #000;
    -webkit-text-fill-color: #000;
  }
  :deep(.el-input__inner)::placeholder {
    color: #000;
    -webkit-text-fill-color: #000;
  }
  :deep(.el-textarea.is-disabled .el-textarea__inner) {
    color: #000;
    -webkit-text-fill-color: #000;
  }
  :deep(.el-textarea__inner)::placeholder {
    color: #000;
    -webkit-text-fill-color: #000;
  }
}
</style>
