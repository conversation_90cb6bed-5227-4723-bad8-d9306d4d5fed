<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="handleRefreshTable()" @current-change="handleRefreshTable()">
      <template #left>
        <el-input style="width: 200px" v-model="searchForm.filter" :placeholder="i18n.t('contact.Search Contacts')" @keyup.enter="handleRefreshTablequery()">
          <template #append>
            <el-button :icon="Search" @click="handleRefreshTablequery()" />
          </template>
        </el-input>
      </template>
      <template #right>
        <span class="tw-h-fit">
          <el-button v-if="userInfo.hasPermission(资产管理中心_联系人_新增)" type="primary" :icon="Plus" @click="handleOpenEdit()">{{ i18n.t("contact.New Contact") }}</el-button>
        </span>
        <!-- <span class="tw-ml-[16px] tw-h-fit">
          <el-button v-if="userInfo.hasPermission(资产管理中心_联系人_可读)" type="primary" :icon="Download" @click="handleExport()">{{ $t("glob.Export") }}联系人</el-button>
        </span> -->
      </template>
      <template #default="{ height: tableHeight }">
        <el-table v-loading="loading" stripe :data="tableData" row-key="id" :height="tableHeight" style="width: 100%">
          <TableColumn type="condition" :prop="`name`" :label="i18n.t('contact.Name')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="handleQuery" :formatter="formatterTable"> </TableColumn>

          <!-- <el-table-column prop="name" label="姓名" :formatter="formatterTable" align="left"> </el-table-column> -->
          <TableColumn type="condition" :prop="`email`" :label="i18n.t('contact.Contact Email')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByEmail" :filters="$filter2" @filter-change="handleQuery" :formatter="formatterTable"> </TableColumn>

          <!-- <el-table-column prop="email" label="联系邮箱" :formatter="formatterTable" align="left"> </el-table-column> -->
          <TableColumn type="condition" :prop="`landlinePhone`" :label="i18n.t('contact.Fixed phone')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByLandlinePhone" :filters="$filter2" @filter-change="handleQuery" :formatter="formatterTable"> </TableColumn>
          <!-- <el-table-column prop="landlinePhone" label="固定电话" :formatter="formatterTable" align="left"> </el-table-column> -->
          <TableColumn type="condition" :prop="`smsPhone`" :label="i18n.t('contact.Mobile')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchBySmsPhone" :filters="$filter2" @filter-change="handleQuery" :formatter="formatterTable"> </TableColumn>
          <TableColumn type="condition" :prop="`mobilePhone`" :label="i18n.t('contact.SMScall')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByMobilePhone" :filters="$filter2" @filter-change="handleQuery" :formatter="formatterTable"> </TableColumn>
          <!-- <el-table-column prop="mobilePhone" label="移动电话" :formatter="formatterTable" align="left"> </el-table-column> -->
          <!-- <el-table-column prop="smsPhone" label="短信号码" :formatter="formatterTable" align="left"> </el-table-column> -->
          <TableColumn type="enum" :prop="`smsEnabledValue`" :label="i18n.t('contact.SMS enabled')" :min-width="110" :showOverflowTooltip="true" show-filter v-model:filtered-value="searchForm.smsEnabled" :filters="['true', 'false'].map((v) => ({ value: v, text: v === 'true' ? i18n.t('contact.Yes') : i18n.t('contact.Not') }))" @filter-change="handleQuery">
            <template #default="{ row }">
              <span>{{ row.smsEnabled ? $t("contact.Yes") : $t("contact.Not") }}</span>
            </template>
          </TableColumn>
          <!-- <el-table-column prop="smsEnabled" label="已启用短信" min-width="100" :formatter="formatterTable" align="left"> </el-table-column> -->
          <TableColumn type="enum" :prop="`vipValue`" :label="`VIP`" :min-width="100" :showOverflowTooltip="true" show-filter v-model:filtered-value="searchForm.vip" :filters="['true', 'false'].map((v) => ({ value: v, text: v === 'true' ? $t('contact.Yes') : $t('contact.Not') }))" @filter-change="handleRefreshTable()">
            <template #default="{ row }">
              <span>{{ row.vip ? $t("contact.Yes") : $t("contact.Not") }}</span>
            </template>
          </TableColumn>
          <!-- <el-table-column prop="vip" label="VIP" :formatter="formatterTable" align="left"> </el-table-column> -->

          <TableColumn type="condition" :prop="`note`" :label="i18n.t('contact.Description')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByNote" :filters="$filter0" @filter-change="handleQuery" :formatter="formatterTable"> </TableColumn>
          <!-- <el-table-column prop="note" label="描述" :formatter="formatterTable" align="left"> </el-table-column> -->
          <TableColumn type="condition" :prop="`zoneId`" :label="i18n.t('contact.Time Zone')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByZoneId" :filters="$filter0" @filter-change="handleQuery" :formatter="formatterTable"></TableColumn>
          <!-- <el-table-column prop="zoneId" label="时区" :formatter="formatterTable" align="left"> </el-table-column> -->
          <TableColumn type="condition" :prop="`id`" :label="`ID`" :min-width="180" :formatter="formatterTable"> </TableColumn>
          <!-- :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByExternalId" :filters="$filter0" @filter-change="handleQuery"  -->
          <!-- <el-table-column prop="label" label="是否激活" :width="100">
            <template #default="{ row }">
              <el-text :type="row.active == true ? 'success' : 'info'">{{ row.active == true ? $t("contact.Activation") : $t("contact.Not Active") }}</el-text>
            </template>
          </el-table-column> -->
          <TableColumn type="enum" :prop="`activeValue`" :label="i18n.t('contact.Is Active')" :min-width="100" :showOverflowTooltip="true" show-filter v-model:filtered-value="searchForm.active" :filters="['true', 'false'].map((v) => ({ value: v, text: v === 'true' ? $t('contact.Activation') : $t('contact.Not Active') }))" @filter-change="handleQuery">
            <template #default="{ row }">
              <el-text :type="row.active == true ? 'success' : 'info'">{{ row.active == true ? $t("contact.Activation") : $t("contact.Not Active") }}</el-text>
            </template>
          </TableColumn>
          <!-- <el-table-column prop="externalId" label="外部ID" :formatter="formatterTable" align="left"> </el-table-column> -->
          <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="175">
            <template #default="{ row }">
              <span class="tw-h-fit">
                <el-link v-if="row.hasPermissionIds.includes(资产管理中心_联系人_查看联系人)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleOpenEdit(row, true)">{{ $t("glob.Cat") }}</el-link>
              </span>
              <span class="tw-h-fit">
                <el-link v-if="row.hasPermissionIds.includes(资产管理中心_联系人_编辑)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleOpenEdit(row, false)">{{ $t("glob.edit") }}</el-link>
              </span>
              <span class="tw-h-fit">
                <el-link v-if="row.hasPermissionIds.includes(资产管理中心_联系人_删除)" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleDelConstant(row)">{{ $t("glob.delete") }}</el-link>
              </span>
              <span class="tw-h-fit">
                <!-- 联系人('603900019854016512') -->
                <el-link :type="row.hasPermissionIds.includes(资产管理中心_联系人_安全) ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="row.hasPermissionIds.includes(资产管理中心_联系人_安全) ? loading : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
  <Editor ref="editorRef" :title="i18n.t('contact.Supplier')"></Editor>
  <contacts-edit ref="contactsEditRef" @refresh="handleRefreshTable" />
  <el-dialog v-model="dialogVisibleshow" :title="i18n.t('contact.Security Container')" width="500" :before-close="handleClose">
    <treeAuth :proptreeId="containerId" :treeStyle="treeStyle" ref="treeAuthRef"></treeAuth>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisibleshow = false">{{ $t("glob.NO") }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete, Download } from "@element-plus/icons-vue";

import pageTemplate from "@/components/pageTemplate.vue";
import Editor from "./Editor.vue";
import treeAuth from "@/components/treeAuth/index.vue";

import bindDevice from "@/components/bindDevice/bindDevice.vue";
import contactsEdit from "./contactsEdit.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { useTemplateRefsList } from "@vueuse/core";
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMessageBox } from "element-plus";

import TableColumn from "@/components/tableColumn/TableColumn.vue";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";

import { getContacts, getContactsquery, delContacts, contactsExport, type ContactsItem as DataItem } from "@/views/pages/apis/contacts";

import zone from "@/views/pages/common/zone.json";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";
import { exoprtMatch1, exoprtMatch2, exoprtMatch3 } from "@/components/tableColumn/common";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
import { 资产管理中心_联系人_可读, 资产管理中心_联系人_新增, 资产管理中心_联系人_编辑, 资产管理中心_联系人_删除, 资产管理中心_联系人_分配区域, 资产管理中心_联系人_分配场所, 资产管理中心_联系人_分配设备, 资产管理中心_联系人_安全, 资产管理中心_联系人_所有权限, 资产管理中心_联系人_管理, 资产管理中心_联系人_查看联系人 } from "@/views/pages/permission";
const ctx = getCurrentInstance()!;
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "global" });
const route = useRoute();
const router = useRouter();
const treeStyle = ref({
  pointerEvents: "none",
});
defineOptions({ name: "contactsManage" });
const editorRef = ref<InstanceType<typeof Editor>>();
const contactsEditRef = ref<InstanceType<typeof contactsEdit>>();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */

const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);
const $filter2 = ref(exoprtMatch3);
const width = inject("width", ref(0));
const height = inject("height", ref(0));
const containerId = ref("");
const dialogVisibleshow = ref(false);
const siteConfig = useSiteConfig();
const userInfo = getUserInfo();

const refs = useTemplateRefsList();
// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

// const assignContacts = ref<InstanceType<typeof AssignContacts>>();
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);

const searchForm = ref<Record<string, string>>({
  /* 联系人姓名 */
  eqName: [] /* 等于的联系人姓名 */,
  includeName: [] /* 包含的联系人姓名 */,
  nameFilterRelation: "AND" /* 联系人姓名过滤关系(AND,OR) */,
  neName: [] /* 不等于的联系人姓名 */,
  excludeName: [] /* 不包含的联系人姓名 */,
  /* 联系人邮箱 */
  eqEmail: [] /* 等于的联系人邮箱 */,
  includeEmail: [] /* 包含的联系人邮箱 */,
  emailFilterRelation: "AND" /* 联系人邮箱过滤关系(AND,OR) */,
  neEmail: [] /* 不等于的联系人邮箱 */,
  excludeEmail: [] /* 不包含的联系人邮箱 */,
  /* 联系人固定电话 */
  eqLandlinePhone: [] /* 等于的联系人固定电话 */,
  includeLandlinePhone: [] /* 包含的联系人固定电话 */,
  landlinePhoneFilterRelation: "AND" /* 联系人固定电话过滤关系(AND,OR) */,
  neLandlinePhone: [] /* 不等于的联系人固定电话 */,
  excludeLandlinePhone: [] /* 不包含的联系人固定电话 */,
  /* 联系人移动电话 */
  eqMobilePhone: [] /* 等于的联系人移动电话 */,
  includeMobilePhone: [] /* 包含的联系人移动电话 */,
  mobilePhoneFilterRelation: "AND" /* 联系人移动电话过滤关系(AND,OR) */,
  neMobilePhone: [] /* 不等于的联系人移动电话 */,
  excludeMobilePhone: [] /* 不包含的联系人移动电话 */,
  /* 联系人描述 */
  eqNote: [] /* 等于的联系人描述 */,
  includeNote: [] /* 包含的联系人描述 */,
  noteFilterRelation: "AND" /* 联系人描述过滤关系(AND,OR) */,
  neNote: [] /* 不等于的联系人描述 */,
  excludeNote: [] /* 不包含的联系人描述 */,
  /* 联系人时区 */
  eqZoneId: [] /* 等于的联系人时区 */,
  includeZoneId: [] /* 包含的联系人时区 */,
  zoneIdFilterRelation: "AND" /* 联系人时区过滤关系(AND,OR) */,
  neZoneId: [] /* 不等于的联系人时区 */,
  excludeZoneId: [] /* 不包含的联系人时区 */,
  /* 联系人外部ID */
  eqExternalId: [] /* 等于的联系人外部ID */,
  includeExternalId: [] /* 包含的联系人外部ID */,
  externalIdFilterRelation: "AND" /* 联系人外部ID过滤关系(AND,OR) */,
  neExternalId: [] /* 不等于的联系人外部ID */,
  excludeExternalId: [] /* 不包含的联系人外部ID */,

  eqSmsPhone: [],
  includeSmsPhone: [],
  smsPhoneFilterRelation: "AND",
  neSmsPhone: [],
  excludeSmsPhone: [],

  smsEnabled: "",
  vip: "",
  active: "",
});

const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByName) === "include") value0 = searchForm.value.includeName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = searchForm.value.excludeName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = searchForm.value.eqName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = searchForm.value.neName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = searchForm.value.includeName[searchForm.value.includeName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = searchForm.value.excludeName[searchForm.value.excludeName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = searchForm.value.eqName[searchForm.value.eqName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = searchForm.value.neName[searchForm.value.neName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: searchForm.value.nameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.nameFilterRelation = v.relation;
    searchForm.value.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByEmail = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchType1ByEmail = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchByEmail = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByEmail) === "include") value0 = searchForm.value.includeEmail[0] || "";
    if (toValue(searchType0ByEmail) === "exclude") value0 = searchForm.value.excludeEmail[0] || "";
    if (toValue(searchType0ByEmail) === "eq") value0 = searchForm.value.eqEmail[0] || "";
    if (toValue(searchType0ByEmail) === "ne") value0 = searchForm.value.neEmail[0] || "";
    let value1 = "";
    if (toValue(searchType1ByEmail) === "include") value1 = searchForm.value.includeEmail[searchForm.value.includeEmail.length - 1] || "";
    if (toValue(searchType1ByEmail) === "exclude") value1 = searchForm.value.excludeEmail[searchForm.value.excludeEmail.length - 1] || "";
    if (toValue(searchType1ByEmail) === "eq") value1 = searchForm.value.eqEmail[searchForm.value.eqEmail.length - 1] || "";
    if (toValue(searchType1ByEmail) === "ne") value1 = searchForm.value.neEmail[searchForm.value.neEmail.length - 1] || "";
    return {
      type0: toValue(searchType0ByEmail),
      type1: toValue(searchType1ByEmail),
      relation: searchForm.value.emailFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByEmail.value = v.type0 as typeof searchType0ByEmail extends import("vue").Ref<infer T> ? T : string;
    searchType1ByEmail.value = v.type1 as typeof searchType1ByEmail extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.emailFilterRelation = v.relation;
    searchForm.value.includeEmail = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeEmail = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqEmail = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neEmail = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByLandlinePhone = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchType1ByLandlinePhone = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchByLandlinePhone = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByLandlinePhone) === "include") value0 = searchForm.value.includeLandlinePhone[0] || "";
    if (toValue(searchType0ByLandlinePhone) === "exclude") value0 = searchForm.value.excludeLandlinePhone[0] || "";
    if (toValue(searchType0ByLandlinePhone) === "eq") value0 = searchForm.value.eqLandlinePhone[0] || "";
    if (toValue(searchType0ByLandlinePhone) === "ne") value0 = searchForm.value.neLandlinePhone[0] || "";
    let value1 = "";
    if (toValue(searchType1ByLandlinePhone) === "include") value1 = searchForm.value.includeLandlinePhone[searchForm.value.includeLandlinePhone.length - 1] || "";
    if (toValue(searchType1ByLandlinePhone) === "exclude") value1 = searchForm.value.excludeLandlinePhone[searchForm.value.excludeLandlinePhone.length - 1] || "";
    if (toValue(searchType1ByLandlinePhone) === "eq") value1 = searchForm.value.eqLandlinePhone[searchForm.value.eqLandlinePhone.length - 1] || "";
    if (toValue(searchType1ByLandlinePhone) === "ne") value1 = searchForm.value.neLandlinePhone[searchForm.value.neLandlinePhone.length - 1] || "";
    return {
      type0: toValue(searchType0ByLandlinePhone),
      type1: toValue(searchType1ByLandlinePhone),
      relation: searchForm.value.landlinePhoneFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByLandlinePhone.value = v.type0 as typeof searchType0ByLandlinePhone extends import("vue").Ref<infer T> ? T : string;
    searchType1ByLandlinePhone.value = v.type1 as typeof searchType1ByLandlinePhone extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.landlinePhoneFilterRelation = v.relation;
    searchForm.value.includeLandlinePhone = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeLandlinePhone = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqLandlinePhone = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neLandlinePhone = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByMobilePhone = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchType1ByMobilePhone = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchByMobilePhone = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    /* 联系人移动电话 */
    // eqMobilePhone: [] /* 等于的联系人移动电话 */,
    // includeMobilePhone: [] /* 包含的联系人移动电话 */,
    // mobilePhoneFilterRelation: "AND" /* 联系人移动电话过滤关系(AND,OR) */,
    // neMobilePhone: [] /* 不等于的联系人移动电话 */,
    // excludeMobilePhone: [] /* 不包含的联系人移动电话 */,
    let value0 = "";
    if (toValue(searchType0ByMobilePhone) === "include") value0 = searchForm.value.includeMobilePhone[0] || "";
    if (toValue(searchType0ByMobilePhone) === "exclude") value0 = searchForm.value.excludeMobilePhone[0] || "";
    if (toValue(searchType0ByMobilePhone) === "eq") value0 = searchForm.value.eqMobilePhone[0] || "";
    if (toValue(searchType0ByMobilePhone) === "ne") value0 = searchForm.value.neMobilePhone[0] || "";
    let value1 = "";
    if (toValue(searchType1ByMobilePhone) === "include") value1 = searchForm.value.includeMobilePhone[searchForm.value.includeMobilePhone.length - 1] || "";
    if (toValue(searchType1ByMobilePhone) === "exclude") value1 = searchForm.value.excludeMobilePhone[searchForm.value.excludeMobilePhone.length - 1] || "";
    if (toValue(searchType1ByMobilePhone) === "eq") value1 = searchForm.value.eqMobilePhone[searchForm.value.eqMobilePhone.length - 1] || "";
    if (toValue(searchType1ByMobilePhone) === "ne") value1 = searchForm.value.neMobilePhone[searchForm.value.neMobilePhone.length - 1] || "";
    return {
      type0: toValue(searchType0ByMobilePhone),
      type1: toValue(searchType1ByMobilePhone),
      relation: searchForm.value.mobilePhoneFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByMobilePhone.value = v.type0 as typeof searchType0ByMobilePhone extends import("vue").Ref<infer T> ? T : string;
    searchType1ByMobilePhone.value = v.type1 as typeof searchType1ByMobilePhone extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.mobilePhoneFilterRelation = v.relation;
    searchForm.value.includeMobilePhone = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeMobilePhone = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqMobilePhone = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neMobilePhone = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0BySmsPhone = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchType1BySmsPhone = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchBySmsPhone = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0BySmsPhone) === "include") value0 = searchForm.value.includeSmsPhone[0] || "";
    if (toValue(searchType0BySmsPhone) === "exclude") value0 = searchForm.value.excludeSmsPhone[0] || "";
    if (toValue(searchType0BySmsPhone) === "eq") value0 = searchForm.value.eqSmsPhone[0] || "";
    if (toValue(searchType0BySmsPhone) === "ne") value0 = searchForm.value.neSmsPhone[0] || "";
    let value1 = "";
    if (toValue(searchType1BySmsPhone) === "include") value1 = searchForm.value.includeSmsPhone[searchForm.value.includeSmsPhone.length - 1] || "";
    if (toValue(searchType1BySmsPhone) === "exclude") value1 = searchForm.value.excludeSmsPhone[searchForm.value.excludeSmsPhone.length - 1] || "";
    if (toValue(searchType1BySmsPhone) === "eq") value1 = searchForm.value.eqSmsPhone[searchForm.value.eqSmsPhone.length - 1] || "";
    if (toValue(searchType1BySmsPhone) === "ne") value1 = searchForm.value.neSmsPhone[searchForm.value.neSmsPhone.length - 1] || "";
    return {
      type0: toValue(searchType0BySmsPhone),
      type1: toValue(searchType1BySmsPhone),
      relation: searchForm.value.smsPhoneFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0BySmsPhone.value = v.type0 as typeof searchType0BySmsPhone extends import("vue").Ref<infer T> ? T : string;
    searchType1BySmsPhone.value = v.type1 as typeof searchType1BySmsPhone extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.smsPhoneFilterRelation = v.relation;
    searchForm.value.includeSmsPhone = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeSmsPhone = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqSmsPhone = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neSmsPhone = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByNote = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByNote = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByNote = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByNote) === "include") value0 = searchForm.value.includeNote[0] || "";
    if (toValue(searchType0ByNote) === "exclude") value0 = searchForm.value.excludeNote[0] || "";
    if (toValue(searchType0ByNote) === "eq") value0 = searchForm.value.eqNote[0] || "";
    if (toValue(searchType0ByNote) === "ne") value0 = searchForm.value.neNote[0] || "";
    let value1 = "";
    if (toValue(searchType1ByNote) === "include") value1 = searchForm.value.includeNote[searchForm.value.includeNote.length - 1] || "";
    if (toValue(searchType1ByNote) === "exclude") value1 = searchForm.value.excludeNote[searchForm.value.excludeNote.length - 1] || "";
    if (toValue(searchType1ByNote) === "eq") value1 = searchForm.value.eqNote[searchForm.value.eqNote.length - 1] || "";
    if (toValue(searchType1ByNote) === "ne") value1 = searchForm.value.neNote[searchForm.value.neNote.length - 1] || "";
    return {
      type0: toValue(searchType0ByNote),
      type1: toValue(searchType1ByNote),
      relation: searchForm.value.noteFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByNote.value = v.type0 as typeof searchType0ByNote extends import("vue").Ref<infer T> ? T : string;
    searchType1ByNote.value = v.type1 as typeof searchType1ByNote extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.noteFilterRelation = v.relation;
    searchForm.value.includeNote = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeNote = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqNote = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neNote = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByZoneId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByZoneId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByZoneId = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByZoneId) === "include") value0 = searchForm.value.includeZoneId[0] || "";
    if (toValue(searchType0ByZoneId) === "exclude") value0 = searchForm.value.excludeZoneId[0] || "";
    if (toValue(searchType0ByZoneId) === "eq") value0 = searchForm.value.eqZoneId[0] || "";
    if (toValue(searchType0ByZoneId) === "ne") value0 = searchForm.value.neZoneId[0] || "";
    let value1 = "";
    if (toValue(searchType1ByZoneId) === "include") value1 = searchForm.value.includeZoneId[searchForm.value.includeZoneId.length - 1] || "";
    if (toValue(searchType1ByZoneId) === "exclude") value1 = searchForm.value.excludeZoneId[searchForm.value.excludeZoneId.length - 1] || "";
    if (toValue(searchType1ByZoneId) === "eq") value1 = searchForm.value.eqZoneId[searchForm.value.eqZoneId.length - 1] || "";
    if (toValue(searchType1ByZoneId) === "ne") value1 = searchForm.value.neZoneId[searchForm.value.neZoneId.length - 1] || "";
    return {
      type0: toValue(searchType0ByZoneId),
      type1: toValue(searchType1ByZoneId),
      relation: searchForm.value.zoneIdFilterRelation,
      value0,
      value1,
      // input0: "",
      input0: zone.reduce((p, c) => (p.append(c.zoneId, c.displayName), p), new URLSearchParams()).toString(),
      // input1: "",
      input1: zone.reduce((p, c) => (p.append(c.zoneId, c.displayName), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByZoneId.value = v.type0 as typeof searchType0ByZoneId extends import("vue").Ref<infer T> ? T : string;
    searchType1ByZoneId.value = v.type1 as typeof searchType1ByZoneId extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.zoneIdFilterRelation = v.relation;
    searchForm.value.includeZoneId = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeZoneId = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqZoneId = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neZoneId = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByExternalId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByExternalId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByExternalId = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByExternalId) === "include") value0 = searchForm.value.includeExternalId[0] || "";
    if (toValue(searchType0ByExternalId) === "exclude") value0 = searchForm.value.excludeExternalId[0] || "";
    if (toValue(searchType0ByExternalId) === "eq") value0 = searchForm.value.eqExternalId[0] || "";
    if (toValue(searchType0ByExternalId) === "ne") value0 = searchForm.value.neExternalId[0] || "";
    let value1 = "";
    if (toValue(searchType1ByExternalId) === "include") value1 = searchForm.value.includeExternalId[searchForm.value.includeExternalId.length - 1] || "";
    if (toValue(searchType1ByExternalId) === "exclude") value1 = searchForm.value.excludeExternalId[searchForm.value.excludeExternalId.length - 1] || "";
    if (toValue(searchType1ByExternalId) === "eq") value1 = searchForm.value.eqExternalId[searchForm.value.eqExternalId.length - 1] || "";
    if (toValue(searchType1ByExternalId) === "ne") value1 = searchForm.value.neExternalId[searchForm.value.neExternalId.length - 1] || "";
    return {
      type0: toValue(searchType0ByExternalId),
      type1: toValue(searchType1ByExternalId),
      relation: searchForm.value.externalIdFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: zone.reduce((p, c) => (p.append(c.zoneId, c.displayName), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: zone.reduce((p, c) => (p.append(c.zoneId, c.displayName), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByExternalId.value = v.type0 as typeof searchType0ByExternalId extends import("vue").Ref<infer T> ? T : string;
    searchType1ByExternalId.value = v.type1 as typeof searchType1ByExternalId extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.externalIdFilterRelation = v.relation;
    searchForm.value.includeExternalId = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeExternalId = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqExternalId = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neExternalId = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
// 搜索关键字
const tableLocationData = ref([]);
const tableData = ref<DataItem[]>([]);
const paging = reactive({
  pageNumber: 1,
  pageSize: 50,
  total: 0,
});

const allRegion = ref([]);
const allRegionByPage = ref([]);
const allRegionSelect = ref([]);

const refresh = inject("refresh");

const dialog = ref(false);
const expandList = ref([]);

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  handleRefreshTable();
}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

function handleExport() {
  contactsExport({}).then(({ success, data }) => {
    if (success) {
      download(`联系人${Date.now()}.xlsx`, data);
    }
  });
}
function download(fileName, blob) {
  const link = document.createElement("a");
  link.href = window.URL.createObjectURL(blob);
  link.style.display = "none";
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  window.URL.revokeObjectURL(link.href);
  document.body.removeChild(link);
}
function handleOpenEdit(row, isView = false) {
  contactsEditRef.value.open(row, isView);
}
function handleDelConstant(row) {
  ElMessageBox.confirm(`${i18n.t("contact.Are you sure you want to delete this contact", { contactName: row.name })}`, `${i18n.t("glob.prompt")}`, {
    confirmButtonText: `${i18n.t("glob.OK")}`,
    cancelButtonText: `${i18n.t("glob.NO")}`,
    type: "warning",
  }).then(async () => {
    try {
      const params = {
        id: row.id,
      };
      const { success, message } = await delContacts(params);
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
      handleRefreshTable();
    } catch (error) {
      error instanceof Error && ElMessage.error(error.message);
    }
  });
}
async function handleQuery() {
  paging.pageNumber = 1;
  await nextTick();
  await handleRefreshTable();
}

function handleRefreshTable() {
  loading.value = true;
  const params = {
    pageNumber: paging.pageNumber,
    pageSize: paging.pageSize,
    // keyword: searchForm.value.keyword,
    ...searchForm.value,
    // filter: searchForm.value.filter || "",
  };
  getContacts(params).then(({ success, data, total }) => {
    if (success) {
      paging.total = Number(total);
      tableData.value = data.map((v) => Object.assign({ ...v }, { smsEnabledValue: String(v.smsEnabled), vipValue: String(v.vip), activeValue: String(v.active) }));
      if (!tableData.value.length && paging.pageNumber !== 1) {
        paging.pageNumber = 1;
        handleRefreshTable();
      }
    } else console.error(JSON.parse(data)?.message || data);
    loading.value = false;
  });
}
function handleRefreshTablequery() {
  loading.value = true;
  const params = {
    pageNumber: paging.pageNumber,
    pageSize: paging.pageSize,
    // keyword: searchForm.value.keyword,
    filter: searchForm.value.filter || "",
  };
  getContactsquery(params).then(({ success, data, total }) => {
    if (success) {
      paging.total = Number(total);
      tableData.value = data.map((v) => Object.assign({ ...v }, { smsEnabledValue: String(v.smsEnabled), vipValue: String(v.vip), activeValue: String(v.active) }));
      if (!tableData.value.length && paging.pageNumber !== 1) {
        paging.pageNumber = 1;
        handleRefreshTablequery();
      }
    } else console.error(JSON.parse(data)?.message || data);
    loading.value = false;
  });
}
function formatterTable(_row, _col, v) {
  switch (_col.property) {
    case "smsEnabled":
      return h("el-tag", { props: { type: v ? "success" : "danger" } }, v ? i18n.t("contact.Yes") : i18n.t("contact.Not"));
    case "vip":
      return h("el-tag", { props: { type: v ? "success" : "danger" } }, v ? i18n.t("contact.Yes") : i18n.t("contact.Not"));
    case "active":
      return h("el-tag", { props: { type: v ? "success" : "danger" } }, v ? i18n.t("contact.Yes") : i18n.t("contact.Not"));
    case "zoneId":
      return zone.find((i) => v === i.zoneId)?.displayName || "--";
    default:
      return v || "--";
  }
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
