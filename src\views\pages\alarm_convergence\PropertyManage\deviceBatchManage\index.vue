<template>
  <el-card class="device-batch-manage" :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="getDevBatchList()" @current-change="getDevBatchList()">
      <template #left>
        <el-date-picker v-model="time" type="datetimerange" start-placeholder="" end-placeholder="" @change="timeChange" />
        <!-- <el-switch v-model="active" @change="getDevList()" inline-prompt active-text="上线设备" inactive-text="非上线设备" /> -->
      </template>
      <template #right>
        <el-input v-model="searchValue" clearable style="width: 220px; height: 32px; margin-right: 10px" placeholder="请输入搜索内容" @keyup.enter="handleQuery">
          <template #append>
            <el-button :icon="Search" @click="handleQuery()" />
          </template>
        </el-input>
        <span class="">
          <el-button v-if="userInfo.hasPermission(资产管理中心_设备批量导入_模版下载)" type="primary" :icon="Download" @click="downloadFile">模板下载 </el-button>
        </span>
        <!--
          ① 客户        【可读】
          ② 设备        【新增】、【可读】、【编辑】、【分配设备类型】、【分配设备供应商】、【分配区域】、【分配场所】、【分配联系人】
          ③ 服务编号    【新增】、【可读】、【编辑】、【删除】、【分配设备供应商】
          ④ 区域        【新增】、【编辑】、【删除】、【可读】、【分配联系人】
          ⑤ 场所        【新增】、【编辑】、【删除】、【可读】、【分配联系人】
          ⑥ 联系人      【新增】、【编辑】、【删除】、【可读】、【分配区域】、【分配场所】、【分配设备】
          ⑦ 行动策略    【新增】、【编辑】、【删除】、【可读】、【分配客户】、【分配区域】、【分配场所】、【分配设备】
          ⑧ 设备类型    【新增】、【编辑】、【删除】、【可读】、【分配设备】
          ⑨ 设备供应商  【新增】、【编辑】、【删除】、【可读】、【分配设备】
          ⑩ 线路供应商  【新增】、【编辑】、【删除】、【可读】、【分配服务编号】
         -->
        <!--
          ①客户       【可读】
          ②设备       【新增】、【可读】、【编辑】、【分配联系人】
          ③服务编号   【新增】、【可读】、【编辑】、【删除】
          ④区域       【新增】、【编辑】、【删除】、【可读】、【分配联系人】
          ⑤场所       【新增】、【编辑】、【删除】、【可读】、【分配联系人】
          ⑥联系人     【新增】、【编辑】、【删除】、【可读】、【分配区域】、【分配场所】、【分配设备】
          ⑦行动策略   【新增】、【编辑】、【删除】、【可读】、【分配客户】、【分配区域】、【分配场所】、【分配设备】
          ⑧设备类型   【新增】、【编辑】、【删除】、【可读】、【分配设备】
          ⑨设备供应商 【新增】、【编辑】、【删除】、【可读】、【分配设备】
          ⑩线路供应商 【新增】、【编辑】、【删除】、【可读】
         -->
        <span class="tw-ml-[12px]" v-if="userInfo.hasPermission(资产管理中心_设备批量导入_客户信息单导入) && hasPermission">
          <el-button type="primary" :icon="Upload" @click="openUploadDialog">客户信息单导入 </el-button>
        </span>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table v-loading="loading" stripe :data="tableData" :height="tableHeight" row-key="id" style="width: 100%">
          <el-table-column prop="name" label="客户"> </el-table-column>
          <el-table-column prop="fileName" label="文件名"> </el-table-column>
          <el-table-column prop="successTotal" label="导入成功条数"> </el-table-column>
          <el-table-column prop="updatedTime" label="导入时间">
            <template #default="{ row }">{{ moment(row.updatedTime, "x").format("yyyy-MM-DD HH:mm:ss") }}</template>
          </el-table-column>
          <el-table-column prop="ownerTenantAbbreviation" label="用户">
            <template #default="{ row }">
              <span>
                {{ row.createdBy ? JSON.parse(row.createdBy)?.account : "--" }}
              </span>
              <!-- <span v-if="row.createdBy != null">
                {{ " @" + row.ownerTenantAbbreviation }}
              </span> -->
            </template>
          </el-table-column>
          <el-table-column prop="status" label="导入结果">
            <template #default="{ row }">{{ row.status ? "导入成功" : "导入失败" }}</template>
          </el-table-column>
          <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="120">
            <template #default="{ row }">
              <!-- <span v-show="!row.status">
                <el-link v-if="userInfo.hasPermission(资产管理中心_设备批量导入_删除)" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="delItem(row)">{{ $t("glob.delete") }}</el-link>
              </span> -->
              <span v-show="!row.status">
                <el-link v-if="userInfo.hasPermission(资产管理中心_设备批量导入_客户信息单导入)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="exportFailFile(row)">{{ $t("glob.Export") }}</el-link>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- <uploadFile ></uploadFile> -->
    </pageTemplate>
    <uploadFileVue ref="uploadFileRef" @submit="uploadSuccess"></uploadFileVue>
  </el-card>
</template>

<script setup lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import uploadFileVue from "./uploadFile.vue";
import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";

/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete, Download, Upload, Close } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";

/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMessageBox } from "element-plus";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";

import { downloadDeviceBatchModelFile, deviceBatchList, deleteBatchItem, downloadFailFileMessage } from "@/views/pages/apis/deviceBatchManage";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
import { 资产管理中心_设备批量导入_模版下载, 资产管理中心_设备批量导入_客户信息单导入, 系统管理中心_客户管理_可读, 资产管理中心_设备_新增, 资产管理中心_设备_可读, 资产管理中心_设备_编辑, 资产管理中心_设备_分配联系人, 资产管理中心_服务编号_可读, 资产管理中心_服务编号_新增, 资产管理中心_服务编号_编辑, 资产管理中心_服务编号_删除, 资产管理中心_区域_新增, 资产管理中心_区域_可读, 资产管理中心_区域_编辑, 资产管理中心_区域_删除, 资产管理中心_区域_分配联系人, 资产管理中心_场所_可读, 资产管理中心_场所_新增, 资产管理中心_场所_编辑, 资产管理中心_场所_删除, 资产管理中心_场所_分配联系人, 资产管理中心_联系人_可读, 资产管理中心_联系人_新增, 资产管理中心_联系人_编辑, 资产管理中心_联系人_删除, 资产管理中心_联系人_分配区域, 资产管理中心_联系人_分配场所, 资产管理中心_联系人_分配设备, 资产管理中心_行动策略_可读, 资产管理中心_行动策略_新增, 资产管理中心_行动策略_编辑, 资产管理中心_行动策略_删除, 资产管理中心_行动策略_分配客户, 资产管理中心_行动策略_分配区域, 资产管理中心_行动策略_分配场所, 资产管理中心_行动策略_分配设备, 资产管理中心_设备类型_可读, 资产管理中心_设备类型_新增, 资产管理中心_设备类型_编辑, 资产管理中心_设备类型_删除, 资产管理中心_设备类型_分配设备, 资产管理中心_设备供应商_可读, 资产管理中心_设备供应商_新增, 资产管理中心_设备供应商_编辑, 资产管理中心_设备供应商_删除, 资产管理中心_设备供应商_分配设备, 资产管理中心_线路供应商_可读, 资产管理中心_线路供应商_新增, 资产管理中心_线路供应商_编辑, 资产管理中心_线路供应商_删除 } from "@/views/pages/permission";
const hasPermission = computed(() => {
  return [
    // ①客户
    系统管理中心_客户管理_可读,
    // ②设备
    资产管理中心_设备_可读,
    资产管理中心_设备_新增,
    资产管理中心_设备_编辑,
    资产管理中心_设备_分配联系人,
    // ③服务编号
    资产管理中心_服务编号_可读,
    资产管理中心_服务编号_新增,
    资产管理中心_服务编号_编辑,
    资产管理中心_服务编号_删除,
    // ④区域
    资产管理中心_区域_可读,
    资产管理中心_区域_新增,
    资产管理中心_区域_编辑,
    资产管理中心_区域_删除,
    资产管理中心_区域_分配联系人,
    // ⑤场所
    资产管理中心_场所_可读,
    资产管理中心_场所_新增,
    资产管理中心_场所_编辑,
    资产管理中心_场所_删除,
    资产管理中心_场所_分配联系人,
    // ⑥联系人
    资产管理中心_联系人_可读,
    资产管理中心_联系人_新增,
    资产管理中心_联系人_编辑,
    资产管理中心_联系人_删除,
    资产管理中心_联系人_分配区域,
    资产管理中心_联系人_分配场所,
    资产管理中心_联系人_分配设备,
    // ⑦行动策略
    资产管理中心_行动策略_可读,
    资产管理中心_行动策略_新增,
    资产管理中心_行动策略_编辑,
    资产管理中心_行动策略_删除,
    资产管理中心_行动策略_分配客户,
    资产管理中心_行动策略_分配区域,
    资产管理中心_行动策略_分配场所,
    资产管理中心_行动策略_分配设备,
    // ⑧设备类型
    资产管理中心_设备类型_可读,
    资产管理中心_设备类型_新增,
    资产管理中心_设备类型_编辑,
    资产管理中心_设备类型_删除,
    资产管理中心_设备类型_分配设备,
    // ⑨设备供应商
    资产管理中心_设备供应商_可读,
    资产管理中心_设备供应商_新增,
    资产管理中心_设备供应商_编辑,
    资产管理中心_设备供应商_删除,
    资产管理中心_设备供应商_分配设备,
    // ⑩线路供应商
    资产管理中心_线路供应商_可读,
    资产管理中心_线路供应商_新增,
    资产管理中心_线路供应商_编辑,
    资产管理中心_线路供应商_删除,
  ].every((v) => userInfo.hasPermission(v));
});
const ctx = getCurrentInstance()!;
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "deviceBatchManage" });

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const siteConfig = useSiteConfig();

const refs = ref({});
const searchValue = ref("");
const userInfo = getUserInfo();
/*-----------------------------          页面数据               ----------------------*/
const tableData = ref([]);
const loading = ref(<boolean>true);
const time = ref("");
const paging = ref({
  pageSize: 50,
  pageNumber: 1,
  total: 0,
});

function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  getDevBatchList();
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
// const dotEnv = require("dotenv");
// const path = require("node:path");

// const baseEnv = Object.assign(dotEnv.config().parsed, dotEnv.config({ path: path.resolve(__dirname, `.env.${process.env.NODE_ENV}`) }).parsed);

/*-----------------------------       页面方法          ----------------------------------- */

function downloadFile() {
  downloadDeviceBatchModelFile().then((res) => {
    const link = document.createElement("a");
    let blob = new Blob([res.data], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
    });
    link.style.display = "none";
    link.href = URL.createObjectURL(blob);
    link.setAttribute("download", "customer-template.xlsx");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  });
}

function openUploadDialog() {
  ctx.refs.uploadFileRef.open();
}

function uploadSuccess(val) {
  getDevBatchList();
  setTimeout(() => {
    ctx.refs.uploadFileRef.uploadDialog = false;
  }, 1000);
}
//时间更新
function timeChange(val) {
  // if (val) {
  // // console.log(val[0].getTime());
  getDevBatchList();
  // }
}

async function handleQuery() {
  paging.value.pageNumber = 1;
  await nextTick();
  await getDevBatchList();
}

//获取列表
async function getDevBatchList() {
  loading.value = true;
  await deviceBatchList({
    ...paging.value,
    sort: "updatedTime,desc",
    startTime: time.value ? time.value[0].getTime() : "",
    endTime: time.value ? time.value[1].getTime() : "",
    keyword: searchValue.value,
  }).then((res) => {
    if (res.success) {
      loading.value = false;
      tableData.value = [...res.data];
      paging.value.total = res.total * 1;
    }
  });
}

async function delItem(item) {
  ElMessageBox.confirm(`确定删除${item.fileName}?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      deleteBatchItem({ id: item.id })
        .then((res) => {
          ElMessage.success("操作成功");
          // // tableData.value.length = tableData.value.length - 1;
          if (res.success) {
            if (tableData.value.slice((paging.value.pageNumber - 1) * paging.value.pageSize, paging.value.pageNumber * paging.value.pageSize).length == 0) {
              paging.value.pageNumber = 1;
            }
            getDevBatchList();
          } else ElMessage.error(JSON.parse(res.data)?.message);
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        });
    })
    .catch(() => {
      //
    });
}

async function exportFailFile(item) {
  try {
    await downloadFailFileMessage({ filePath: item.errFilePath }).then((res) => {
      const link = document.createElement("a");
      let blob = new Blob([res.data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
      });
      link.style.display = "none";
      link.href = URL.createObjectURL(blob);
      link.setAttribute("download", item.errFilePath);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
// interface Slots {
//   default(props: T): any;
// }
// defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>
<style scoped lang="scss"></style>
