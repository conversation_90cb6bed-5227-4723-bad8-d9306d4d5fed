<!--  -->
<template>
  <el-dialog title="客户信息单导入" v-model="uploadDialog" width="35%" :before-close="() => (controller && controller.abort(), $emit('submit'))">
    <el-form>
      <el-upload class="mk" :show-file-list="false" :auto-upload="false" :on-progress="handleProgress" :on-change="handleFileChange">
        <template #tip>
          <div class="btn-upload">
            <!-- <el-button type="primary" @click="chooseFile">选择文件</el-button> -->
            <p>
              文件支持格式：<span style="color: #67c23a">xls、xlsx</span> ；文件大小： <span style="color: #67c23a">不超过500M</span> 每次导入最多
              <span style="color: #67c23a">不能超过500条 </span>
            </p>
          </div>
        </template>
      </el-upload>

      <el-form-item style="width: 100%">
        <el-upload class="upload-demo" ref="uploadRef" drag :show-file-list="false" :auto-upload="false" :on-progress="handleProgress" :on-change="handleFileChange">
          <template #default>
            <div>
              <el-icon style="font-size: 40px"><UploadFilled style="width: 1em; height: 1em" /></el-icon>
              <div class="el-upload__text">拖拽文件至此处</div>
            </div>

            <!-- zidingyimubanneirong -->
            <!-- <div class="el-upload__tip">jpg/png files with a size less than 500kb</div> -->

            <div v-show="showPercentage && !showUploadFial" style="text-align: left">
              <span>
                {{ uploadTitle }}
              </span>
              <el-progress :percentage="percentage" />
            </div>
            <div class="upload-fail" v-show="showUploadFial">
              <el-icon style="font-size: 14px; color: red">
                <Warning style="width: 1em; height: 1em" />
              </el-icon>
              {{ uploadTitle }}

              <el-button type="text" v-show="!exportFileResult" @click.stop="referUplaod">重新上传</el-button>
              <el-button type="text" v-show="exportFileResult" @click.stop="exportFailFile">导出校验结果</el-button>
            </div>
          </template>
          <template #tip>
            <ul class="imgList">
              <li v-for="item in fileList" :key="item">
                <div class="file-img" :class="item.className"></div>
                <div>{{ item.name }} {{ (Number(item.size) / 1024 / 1024).toFixed(1) }} M</div>
                <!-- <el-image :src="item" :preview-src-list="fileList"> </el-image> -->
              </li>
            </ul>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="选择用户组：" style="width: 100%" v-show="showUserGroups">
        <el-select v-model="userGroupIds" multiple placeholder="请选择" @change="cascaderChange">
          <el-option v-for="item in userGroups" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
        <!-- <el-select style="width: 100%" placeholder="请选择用户组" multiple v-model="userGroupIds"> -->
        <!-- <el-cascader v-show="isTrue" ref="cascader" style="width: 500px" v-model="userGroupIds" @change="cascaderChange" filterable :options="userGroups" :props="props" clearable popper-class="dissableFirstLevel"></el-cascader> -->

        <!-- </el-select> -->
      </el-form-item>
      <el-form-item style="width: 100%" v-show="showUserGroups">
        <el-checkbox label="是否加入该客户" v-model="isJoin"> </el-checkbox>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button v-show="showCancel && percentage < 100" @click="cancelUpload">取消</el-button>
      <el-button @click="cancelUpload" v-show="showUserGroups && percentage >= 100">取消</el-button>

      <el-button @click="confirm" type="primary" v-show="showUserGroups">提交</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { UploadFilled, Warning } from "@element-plus/icons-vue";
import { uploadResourceByFile } from "@/views/pages/apis/deviceBatchManage";
import getUserInfo from "@/utils/getUserInfo";
import { downloadFailFileMessage, getUserGroups, jionUserGroups, getTenantList } from "@/views/pages/apis/deviceBatchManage";
import { getGroup } from "@/api/personnel";

export default {
  name: "upload-fail",
  components: {
    UploadFilled,
    Warning,
  },
  emits: ["uploadDialog", "submit"],
  data() {
    return {
      showCancel: false, //是否展示取消按钮
      showPercentage: false, //是否展示上传进度
      percentage: 0, //上传进度

      uploadTitle: "",
      showUploadFial: false,
      fileList: [],
      uploadDialog: false,
      operateType: "",
      controller: null,

      userInfo: getUserInfo(),
      exportFileResult: false,
      errFilePath: "",
      userGroups: [],
      showUserGroups: false,
      isJoin: false,
      userGroupIds: [],
      props: {
        checkStrictly: true,
        children: "children",
        label: "name",
        value: "id",
        lazy: true,
        multiple: true,
        lazyLoad(node, resolve) {
          const { level } = node;
          //下一层节点
          let nodes = [];
          //如果有子节点 或者 为根节点（即首次进入level为0）
          //也有人写成 node.level == 0 作用是一样的

          if (node.value) {
            // 0 代表第一次请求
            let nodeId = level == 0 ? null : node.value;
            //这里setTimeout的目的是 显示加载动画
            setTimeout(() => {
              //调用后端接口 获得返回数据
              getUserGroups({ tenantId: node.value }).then((res) => {
                if (res.success) {
                  nodes = Array.from(res.data).map((item) => ({
                    id: item.id,
                    name: item.name,
                    leaf: level >= 1,
                  }));
                  // console.log(nodes, 66666);
                  resolve(nodes);
                } else {
                  resolve([]);
                }
              });
            }, 1);
          } else {
            //如果没有子节点就不发起请求，直接渲染，也避免了点击叶子节点仍然有加载动画的问题
            resolve(nodes);
          }
        },
      },
      tenantId: "",
      groupIdList: [],
    };
  },
  unmounted() {
    clearInterval(this.timer);
  },
  methods: {
    cascaderChange(val) {
      this.groupIdList = [];

      val.forEach((v, i) => {
        if (v.length > 1) {
          this.groupIdList.push(v);
        }
      });
    },
    confirm() {
      // if (this.isJoin) {
      jionUserGroups(
        {
          joinUserGroupIds: this.groupIdList,
          joinUserIds: this.isJoin ? [this.userInfo.userId] : undefined,
        },
        this.tenantId
      )
        .then((res) => {
          if (res.success) {
            this.$message.success("操作成功");
            this.uploadDialog = false;
            if (this.controller instanceof AbortController) this.controller.abort();
            this.$emit("submit");
          }
        })
        .catch((err) => {
          this.$message.error(err?.message);
        });
      // }
    },
    chooseFile() {
      this.$refs.uploadRef.clearFiles();
      clearInterval(this.timer);
    },
    exportFailFile() {
      downloadFailFileMessage({ filePath: this.errFilePath }).then((res) => {
        const link = document.createElement("a");
        let blob = new Blob([res.data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
        });
        link.style.display = "none";
        link.href = URL.createObjectURL(blob);
        link.setAttribute("download", this.errFilePath);
        this.$refs.uploadRef.clearFiles();
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      });
    },
    open() {
      this.showUserGroups = false;
      this.uploadDialog = true;
      this.showUploadFial = false;
      this.percentage = 0;
      this.fileList = [];
      this.uploadTitle = "";
      this.showPercentage = false;
      this.exportFileResult = false;
      this.showCancel = false;
      this.userGroupIds = [];
      this.isJoin = false;
      clearInterval(this.timer);

      this.getTenantUserGroups();
    },

    async getTenantUserGroups() {
      try {
        const { success: groupSuccess, data: groups, message: groupMessage } = await getGroup({});
        if (!groupSuccess) throw new Error(groupMessage);
        this.userGroups = groups;

        this.userGroups.forEach(async (v) => {
          v.children = [];

          // const { success: userSuccess, data: users, message: userMessage } = await getUserGroups({ tenantId: v.id });
          // if (!userSuccess) throw new Error(userMessage);

          // let data = [...users];
          // let newData = [];
          // data.forEach((item) => {
          //   newData.push({
          //     checkStrictly: false,
          //     children: [],
          //     name: item.name,
          //     id: item.id,
          //   });
          // });
          // v.children = newData;
        });
      } catch (error) {
        this.$message.error(error.message);
      }
      // getUserGroups({
      //   tenantId: this.userInfo.currentTenant.tenantId,
      //   external: false,
      // }).then((res) => {
      //   // // console.log(12346789, res);
      //   if (res.success) {
      //     this.userGroups = res.data;
      //   }
      // });
    },
    //取消上传
    cancelUpload() {
      this.$refs.uploadRef.abort();
      this.showUploadFial = false;
      this.uploadTitle = "";
      this.$refs.uploadRef.clearFiles();
      this.showUserGroups = false;
      const abortController = new AbortController();
      abortController.abort();
      this.uploadDialog = false;
      if (this.controller instanceof AbortController) this.controller.abort();
      this.$emit("submit");
      // this.cancel("请求已取消");
    },
    referUplaod() {
      this.showUserGroups = false;
      document.querySelector(".el-upload__input").click();
    },
    //文件上传
    async handleFileChange(file, fileList) {
      this.showUserGroups = false;
      this.$refs.uploadRef.clearFiles();
      this.exportFileResult = false;
      let index = file.name.lastIndexOf(".");
      let type = file.name.substr(index + 1, file.name.length);
      this.showUploadFial = false;
      this.uploadTitle = "";
      this.showCancel = true;
      if (["xlsx", "xls", "XLSX", "XLS"].indexOf(type.toLowerCase()) == -1) {
        this.showUploadFial = true;
        this.exportFileResult = false;
        this.uploadTitle = "文件上传失败，格式错误";
        this.$refs.uploadRef.clearFiles();
      } else {
        this.showUploadFial = false;
        this.showPercentage = true;

        let formData = new FormData();

        formData.append("file", file.raw);

        try {
          this.percentage = 0;
          const result = uploadResourceByFile({ file: [file.raw], abbreviation: this.userInfo.currentTenant.abbreviation, onUploadProgress: (progress) => (this.percentage = Math.round(Number(progress.progress) * 100)) });
          const { controller } = result;
          this.controller = controller;
          const { success, message, data } = await result;
          this.$refs.uploadRef.clearFiles();
          if (!success) return await Promise.reject(Object.assign(new Error(message), { success, data }));
          if (data.errors.length > 0) {
            this.showUploadFial = true;
            this.exportFileResult = true;
            this.uploadTitle = "文件上传失败，文件内容格式错误";
            this.$refs.uploadRef.clearFiles();
            this.errFilePath = data.errFilePath;
          } else {
            this.$refs.uploadRef.clearFiles();
            this.tenantId = data.tenant.id;
            this.showUserGroups = data.newTenant;
            this.$emit("submit");
          }
        } catch (error) {
          this.exportFileResult = false;
          clearInterval(this.timer);
          this.showUploadFial = true;
          this.uploadTitle = error instanceof Error ? error.message : `${error}`;
          this.$refs.uploadRef.clearFiles();
        } finally {
          this.controller = null;
        }
      }
    },

    handleProgress(event, file, fileList) {
      this.showUserGroups = false;
      this.showCancel = true;
      let index = file.name.lastIndexOf(".");
      let Type = file.name.substr(index + 1, file.name.length);
      const percent = Math.floor(file.percentage);
      if (["xlsx", "xls", "XLSX", "XLS"].indexOf(Type.toLowerCase()) === -1) {
        this.showUploadFial = true;
        this.uploadTitle = "文件上传失败，格式错误";
      } else {
        this.showPercentage = true;
        this.showUploadFial = false;
        this.percentage = percent;
        this.uploadTitle = "正在上传";
      }
      // setInterval(() => {

      // }, 500);
    },
  },
  expose: ["uploadDialog", "operateType", "open"],
};
</script>
<style scoped lang="scss">
.dissableFirstLevel {
  .el-scrollbar:first-child {
    .el-checkbox {
      display: none;
    }
  }
}
.upload-demo {
  width: 100%;
  margin-top: 10px;
}
.btn-upload {
  display: flex;
  justify-content: space-between;
  > p {
    padding-left: 10px;
    box-sizing: border-box;
  }
}
</style>
