<template>
  <div>
    <el-dialog :title="title" v-model="dialogFormVisible" :before-close="handleClose" width="45%">
      <el-form :model="form" :rules="rules" ref="ruleForm">
        <el-form-item label="名称:" :label-width="formLabelWidth" prop="name">
          <el-input v-model="form.name" autocomplete="off" placeholder="请输入设备分组名称"></el-input>
        </el-form-item>
        <el-form-item label="描述:" :label-width="formLabelWidth" prop="description">
          <el-input type="textarea" v-model="form.description" autocomplete="off" :rows="2" placeholder="请输入描述"></el-input>
        </el-form-item>

        <!-- <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(服务管理中心_设备分组_分配告警分类)"> -->
        <el-form-item label="告警分类:" :label-width="formLabelWidth" prop="alertClassificationIds">
          <!-- :disabled="!userInfo.hasPermission(服务管理中心_设备分组_分配告警分类)" -->
          <el-select v-model="form.alertClassificationIds" placeholder="请选择告警分类" :style="{ width: '100%' }" multiple>
            <el-option v-for="item in options" :key="item.name" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <!-- </el-tooltip> -->
        <el-form-item label="" :label-width="formLabelWidth">
          <el-checkbox v-model="form.report" label="是否为一个报告组"></el-checkbox>
        </el-form-item>
        <el-form-item v-show="type === 'add'" :label="`选择安全目录`" tooltip="" prop="containerId" style="margin-top: 10px">
          <treeAuth v-if="dialogFormVisible" ref="treeAuthRef" :treeStyle="treeStyle"></treeAuth>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel()">取 消</el-button>
          <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { defineComponent } from "vue";
import { ElMessage } from "element-plus";
import { adddeviceGroup, editdeviceGroup } from "@/views/pages/apis/deviceManage";
import { getAlarmClassificationList } from "@/views/pages/apis/alarmClassification";
import { getTenantInfo } from "@/views/pages/apis/tenant";
import getUserInfo from "@/utils/getUserInfo";
import { buildValidatorData, validatorPattern } from "@/utils/validate";

import treeAuth from "@/components/treeAuth/index.vue";

import { 服务管理中心_设备分组_分配告警分类, 服务管理中心_告警分类_可读 } from "@/views/pages/permission";
import { debug } from "util";
export default defineComponent({
  name: "supplierCreate",
  components: {
    treeAuth,
  },
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      userInfo: getUserInfo(),
      form: {
        name: "",
        description: "",
        report: false,
        alertClassificationIds: [],
        containerId: "",
      },
      rules: {
        name: [{ required: true, message: "请输入设备分组名称", trigger: "blur" }],
        containerId: [buildValidatorData({ name: "required", title: `请选择安全目录` })],
      },
      disabled: false,
      title: "",
      checked: false,
      dialogFormVisible: false,
      formLabelWidth: "130px",
      type: "",
      options: [],
      value: "",
      buildValidatorData: buildValidatorData,
      treeStyle: {
        width: "300px",
        height: "150px",
      },
      服务管理中心_告警分类_可读,
    };
  },
  watch: {
    // "dialog"(val) {
    //   this.dialogFormVisible = val;
    // },
    "form.report"(val) {
      this.form.report = val;
    },
  },
  created() {
    this.getAlarmList();
  },
  methods: {
    open(type, row) {
      this.dialogFormVisible = true;
      // // console.log(type, row);
      this.type = type;
      this.form.report = false;
      if (type === "add") {
        this.form = {
          name: "",
          description: "",
          report: false,
          alertClassificationIds: [],
        };
        this.title = "新增设备分组";
        // // console.log(this.form.report);
      } else {
        this.form = { ...row };
        this.form.alertClassificationIds = this.userInfo.hasPermission(服务管理中心_告警分类_可读) ? this.form.alertClassificationIds : [];
        const vendorAll = new Set(this.options.map((item) => String(item.id)));
        this.form.alertClassificationIds = this.form.alertClassificationIds.filter((id) => vendorAll.has(id));
        this.title = "编辑设备分组";
      }
    },
    getAlarmList() {
      getAlarmClassificationList({}).then((res) => {
        if (res.success) {
          this.options = [...res.data];
        } else {
          ElMessage.error(JSON.parse(res.data)?.message);
        }
      });
    },
    async confirm(formName) {
      if (this.type === "add") this.form.containerId = this.$refs.treeAuthRef.treeId;
      await this.$nextTick();
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          if (this.type === "add") {
            // getTenantInfo({})
            //   .then(({ success, message, data }) => {
            //     if (!success) throw Object.assign(new Error(message), { success, data });
            //     return ;
            //   })
            adddeviceGroup({ ...this.form })
              .then(({ success, message, data }) => {
                if (!success) throw Object.assign(new Error(message), { success, data });
                ElMessage.success("新增成功");
                this.dialogFormVisible = false;
                this.form.report = false;

                this.$emit("dialogClose", false);

                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
              })
              .catch((error) => {
                this.dialogFormVisible = false;
                this.form.report = false;

                this.$emit("dialogClose", false);
                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
                if (error instanceof Error) ElMessage.error(error.message);
              });
          } else {
            editdeviceGroup({ ...this.form })
              .then((res) => {
                if (res.success) {
                  this.dialogFormVisible = false;
                  this.form.report = false;

                  ElMessage.success("修改成功");
                  this.$emit("dialogClose", false);

                  this.$refs[formName].resetFields();
                  this.$refs[formName].clearValidate();
                } else {
                  this.dialogFormVisible = false;
                  this.form.report = false;

                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                  this.$refs[formName].clearValidate();
                  ElMessage.error(JSON.parse(res.data)?.message);
                }
              })
              .catch((error) => {
                this.dialogFormVisible = false;
                this.form.report = false;

                this.$emit("dialogClose", false);
                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
                if (error instanceof Error) ElMessage.error(error.message);
              });
          }
          // this.dialogFormVisible = false;
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.dialogFormVisible = false;
      this.form.report = false;
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      this.$refs["ruleForm"].clearValidate();

      // this.dialogFormVisible = false;
    },
    cancel() {
      this.dialogFormVisible = false;
      this.form.report = false;
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      this.$refs["ruleForm"].clearValidate();
    },
    blurChange(data) {
      this.tags.push(data);
    },
    tagClose(index) {
      this.tags.splice(index, 1);
    },
  },
  expose: ["type", "disabled", "title", "form", "open"],
});
</script>
