<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="getList()" @current-change="getList()">
      <!-- <template #left>
        <el-input v-model="ServiceSearch" placeholder="请输入告警降级配置名称" @keyup.enter="getSlaList()">
          <template #append>
            <el-button :icon="Search" @click="getSlaList()" />
          </template>
        </el-input>
      </template> -->
      <template #right>
        <span class="">
          <el-button v-if="userInfo.hasPermission(资产管理中心_设备分组_新增)" type="primary" :icon="Plus" @click="deviceDialog('add')">{{ $t("glob.add") }}分组</el-button>
        </span>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table v-loading="loading" stripe :data="tableData.slice((paging.pageNumber - 1) * paging.pageSize, paging.pageNumber * paging.pageSize)" row-key="id" :height="tableHeight" :expand-row-keys="expandList" style="width: 100%">
          <el-table-column type="expand">
            <template #default="{ row, expanded }">
              <ModelExpand
                v-if="row.id && expanded"
                :key="row.id"
                :id="row.id"
                type="group"
                :create="{
                  resource: !userInfo.hasPermission(资产管理中心_设备分组_分配设备),
                }"
                :viewer="{}"
                :remove="{
                  resource: !userInfo.hasPermission(资产管理中心_设备分组_分配设备),
                }"
              ></ModelExpand>
              <!-- <bindDevice :ref="'bindDeviceRef' + row.id" :key="row.id" @delete="delRelation" :id="row.id" @confirm="bindDeviceList" :list="[]" :title="['分配设备']"></bindDevice> -->
            </template>
          </el-table-column>
          <TableColumn type="condition" :prop="`name`" :label="`分组名称`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="getList()" :formatter="formatterTable"></TableColumn>
          <!-- <el-table-column prop="name" label="分组名称" :formatter="formatterTable"> </el-table-column> -->

          <TableColumn type="condition" :prop="`description`" :label="`描述`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByDescription" :filters="$filter0" @filter-change="getList()" :formatter="formatterTable"></TableColumn>
          <!-- <el-table-column prop="description" label="描述" :formatter="formatterTable"></el-table-column> -->

          <TableColumn type="condition" :prop="`alertClassificationIds`" :label="`告警分类`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByAlertClassificationId" :filters="$filter0" @filter-change="getList()" :formatter="formatterTable">
            <!-- <template #default="scope">
              <div>
                <span v-for="(item, index) in scope.row.alertClassificationIds" :key="item">
                  {{ index != scope.row.alertClassificationIds.length - 1 ? options[item] + "," : options[item] }}
                </span>
                <span>
                  {{ scope.row.alertClassificationIds.length < 1 ? "--" : "" }}
                </span>
              </div>
            </template> -->
            <template #default="scope">
              <div>
                <span v-for="(item, index) in scope.row.alertClassificationIds" :key="item">
                  {{ options[item] ? `${options[item]}${index !== scope.row.alertClassificationIds.length - 1 ? "," : ""}` : "" }}
                </span>
                <span>
                  {{ scope.row.alertClassificationIds.length < 1 ? "--" : "" }}
                </span>
              </div>
            </template>
          </TableColumn>

          <!-- <el-table-column prop="alertClassificationIds" label="告警分类" :formatter="formatterTable">
            <template #default="scope">
              <div>
                <span v-for="(item, index) in scope.row.alertClassificationIds" :key="item">
                  {{ index != scope.row.alertClassificationIds.length - 1 ? options[item] + "," : options[item] }}
                </span>
                <span>
                  {{ scope.row.alertClassificationIds.length < 1 ? "--" : "" }}
                </span>
              </div>
            </template>
          </el-table-column> -->

          <TableColumn type="enum" :prop="`report`" :label="`是否为一个报告组`" :min-width="100" :showOverflowTooltip="true" show-filter v-model:filtered-value="searchForm.report" :filters="['true', 'false'].map((v) => ({ value: v, text: v === 'true' ? '是' : '否' }))" @filter-change="getList()">
            <template #default="{ row }">
              <div>
                {{ row.report ? "是" : "否" }}
              </div>
            </template>
          </TableColumn>

          <!-- <el-table-column prop="report" label="是否为一个报告组" :formatter="formatterTable">
            <template #default="{ row }">
              <div>
                {{ row.report ? "是" : "否" }}
              </div>
            </template>
          </el-table-column> -->

          <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="120">
            <template #default="{ row }">
              <span>
                <el-link v-if="row.verifyPermissionIds.includes(资产管理中心_设备分组_编辑)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="deviceDialog('edit', row)">{{ $t("glob.edit") }}</el-link>
              </span>
              <span>
                <el-link v-if="row.verifyPermissionIds.includes(资产管理中心_设备分组_删除)" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="delItem(row)">{{ $t("glob.delete") }}</el-link>
              </span>
              <span>
                <!-- 设备分组('603899963763589120') -->
                <el-link :type="row.verifyPermissionIds.includes(资产管理中心_设备分组_安全) ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="row.verifyPermissionIds.includes(资产管理中心_设备分组_安全) ? loading : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
  <!-- <Editor ref="editorRef" title="时区"></Editor> -->
  <deviceGroupCreate :dialog="dialog" ref="supplier" @dialogClose="dialogClose"></deviceGroupCreate>
  <el-dialog v-model="containerVisible" title="查看安全目录" width="500">
    <template #default>
      <treeAuth :proptreeId="containerId" :treeStyle="{ pointerEvents: 'none' }"></treeAuth>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="containerVisible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete, Download } from "@element-plus/icons-vue";

import Editor from "./Editor.vue";

import assignContacts from "@/components/bindContacts/assignContacts";
import bindDevice from "@/components/bindDevice/bindDevice.vue";
import deviceGroupCreate from "./deviceGroupCreate.vue";
import ModelExpand from "@/views/pages/modelExpand/Model.vue";

import TableColumn from "@/components/tableColumn/TableColumn.vue";

/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { useTemplateRefsList } from "@vueuse/core";
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMessageBox } from "element-plus";
import pageTemplate from "@/components/pageTemplate.vue";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";

import { getdeviceGroupList, deletedeviceGroup, deviceGroupRelationDevice, getDeviceList, deviceGroupDelRelationDevice, type SlaConfigList as DataItem } from "@/views/pages/apis/deviceManage";
import { getAlarmClassificationList } from "@/views/pages/apis/alarmClassification";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
import { 资产管理中心_设备分组_可读, 资产管理中心_设备分组_新增, 资产管理中心_设备分组_编辑, 资产管理中心_设备分组_删除, 资产管理中心_设备分组_分配设备, 资产管理中心_设备分组_安全 } from "@/views/pages/permission";
import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";
const containerId = ref("");
const containerVisible = ref(false);
const ctx = getCurrentInstance()!;
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "deviceGroupManage" });
// const editorRef = ref<InstanceType<typeof Editor>>();
const assignContactsRef = ref<InstanceType<typeof assignContacts>>();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const siteConfig = useSiteConfig();
const userInfo = getUserInfo();

const refs = useTemplateRefsList();

const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);

// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

// const assignContacts = ref<InstanceType<typeof AssignContacts>>();
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);

// 搜索关键字
const searchForm = ref<Record<string, any>>({
  eqName: [] /* 等于的设备分组名称 */,
  includeName: [] /* 包含的设备分组名称 */,
  nameFilterRelation: "AND" /* 设备分组名称过滤关系(AND,OR) */,
  neName: [] /* 不等于的设备分组名称 */,
  excludeName: [] /* 不包含的设备分组名称 */,

  eqDescription: [] /* 等于的设备分组描述 */,
  includeDescription: [] /* 包含的设备分组描述 */,
  descriptionFilterRelation: "AND" /* 设备分组描述过滤关系(AND,OR) */,
  neDescription: [] /* 不等于的设备分组描述 */,
  excludeDescription: [] /* 不包含的设备分组描述 */,

  eqAlertClassificationName: [] /* 等于的设备分组告警分类 */,
  includeAlertClassificationName: [] /* 包含的设备分组告警分类 */,
  alertClassificationNameFilterRelation: "AND" /* 设备分组告警分类过滤关系(AND,OR) */,
  neAlertClassificationName: [] /* 不等于的设备分组告警分类 */,
  excludeAlertClassificationName: [] /* 不包含的设备分组告警分类 */,

  report: "" /* 是否为一个报告组 */,
});

const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByName) === "include") value0 = searchForm.value.includeName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = searchForm.value.excludeName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = searchForm.value.eqName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = searchForm.value.neName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = searchForm.value.includeName[searchForm.value.includeName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = searchForm.value.excludeName[searchForm.value.excludeName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = searchForm.value.eqName[searchForm.value.eqName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = searchForm.value.neName[searchForm.value.neName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: searchForm.value.nameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.nameFilterRelation = v.relation;
    searchForm.value.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByDescription = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    //   eqDescription: [] /* 等于的设备分组描述 */,
    // includeDescription: [] /* 包含的设备分组描述 */,
    // descriptionFilterRelation: "AND" /* 设备分组描述过滤关系(AND,OR) */,
    // neDescription: [] /* 不等于的设备分组描述 */,
    // excludeDescription: [] /* 不包含的设备分组描述 */
    let value0 = "";
    if (toValue(searchType0ByDescription) === "include") value0 = searchForm.value.includeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "exclude") value0 = searchForm.value.excludeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "eq") value0 = searchForm.value.eqDescription[0] || "";
    if (toValue(searchType0ByDescription) === "ne") value0 = searchForm.value.neDescription[0] || "";
    let value1 = "";
    if (toValue(searchType1ByDescription) === "include") value1 = searchForm.value.includeDescription[searchForm.value.includeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "exclude") value1 = searchForm.value.excludeDescription[searchForm.value.excludeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "eq") value1 = searchForm.value.eqDescription[searchForm.value.eqDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "ne") value1 = searchForm.value.neDescription[searchForm.value.neDescription.length - 1] || "";
    return {
      type0: toValue(searchType0ByDescription),
      type1: toValue(searchType1ByDescription),
      relation: searchForm.value.descriptionFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByDescription.value = v.type0 as typeof searchType0ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchType1ByDescription.value = v.type1 as typeof searchType1ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.descriptionFilterRelation = v.relation;
    searchForm.value.includeDescription = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeDescription = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqDescription = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neDescription = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByAlertClassificationId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByAlertClassificationId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByAlertClassificationId = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    //   eqAlertClassificationName: [] /* 等于的设备分组告警分类 */,
    // includeAlertClassificationName: [] /* 包含的设备分组告警分类 */,
    // alertClassificationNameFilterRelation: "AND" /* 设备分组告警分类过滤关系(AND,OR) */,
    // neAlertClassificationName: [] /* 不等于的设备分组告警分类 */,
    // excludeAlertClassificationName: [] /* 不包含的设备分组告警分类 */,
    let value0 = "";
    if (toValue(searchType0ByAlertClassificationId) === "include") value0 = searchForm.value.includeAlertClassificationName[0] || "";
    if (toValue(searchType0ByAlertClassificationId) === "exclude") value0 = searchForm.value.excludeAlertClassificationName[0] || "";
    if (toValue(searchType0ByAlertClassificationId) === "eq") value0 = searchForm.value.eqAlertClassificationName[0] || "";
    if (toValue(searchType0ByAlertClassificationId) === "ne") value0 = searchForm.value.neAlertClassificationName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByAlertClassificationId) === "include") value1 = searchForm.value.includeAlertClassificationName[searchForm.value.includeAlertClassificationName.length - 1] || "";
    if (toValue(searchType1ByAlertClassificationId) === "exclude") value1 = searchForm.value.excludeAlertClassificationName[searchForm.value.excludeAlertClassificationName.length - 1] || "";
    if (toValue(searchType1ByAlertClassificationId) === "eq") value1 = searchForm.value.eqAlertClassificationName[searchForm.value.eqAlertClassificationName.length - 1] || "";
    if (toValue(searchType1ByAlertClassificationId) === "ne") value1 = searchForm.value.neAlertClassificationName[searchForm.value.neAlertClassificationName.length - 1] || "";
    return {
      type0: toValue(searchType0ByAlertClassificationId),
      type1: toValue(searchType1ByAlertClassificationId),
      relation: searchForm.value.alertClassificationNameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByAlertClassificationId.value = v.type0 as typeof searchType0ByAlertClassificationId extends import("vue").Ref<infer T> ? T : string;
    searchType1ByAlertClassificationId.value = v.type1 as typeof searchType1ByAlertClassificationId extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.alertClassificationNameFilterRelation = v.relation;
    searchForm.value.includeAlertClassificationName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeAlertClassificationName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqAlertClassificationName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neAlertClassificationName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const tableData = ref<DataItem[]>([]);
const expandList = ref<string[]>([]);
const paging = reactive({
  pageNumber: 1,
  pageSize: 50,
  total: 0,
});

const allRegion = ref([]);
const allRegionByPage = ref([]);
const allRegionSelect = ref([]);

const dialog = ref(false);
const options = ref({});

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  getList();
}
function beforeMount() {}
function mounted() {
  loading.value = true;
  getAlarmClassificationList({})
    .then(({ success, message, data }) => {
      if (!success) throw new Error(message);
      options.value = data.reduce((p, c) => ({ ...p, [c.id]: c.name }), {});
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {
      loading.value = false;
    });
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

function formatterTable(_row, _col, v) {
  switch (_col.property) {
    default:
      return v || "--";
  }
}
function getDetails(row) {
  getDeviceList({ pageNumber: 1, pageSize: 10000, groupId: row.id }).then((res) => {
    if (res.success) {
      let data = [];
      res.data.forEach((item) => {
        data.push({
          name: item.name,
          detail: item.config.ipAddress,
          id: item.id,
        });
      });
      // setContacts
      ctx.refs["bindDeviceRef" + row.id].setContacts(0, data);
      // ctx.refs.bindDevice.contacts = [data];
    } else {
      ElMessage.error(JSON.parse(res.data)?.message);
    }
  });
}
//取消关联设备
function delRelation(val) {
  let deviceId = val.ids;

  deviceGroupDelRelationDevice({
    resourceIds: deviceId,
    id: val.expandId,
  }).then((res) => {
    if (res.success) {
      ElMessage.success("操作成功");
      getDetails({ id: val.expandId });
      // getList();
      // expandChange({
      //   id: val.expandId,
      // });
    }
  });
}
// //绑定设备关联关系
// function bindDeviceList(val) {
//   // let deviceId = val.ids[0];

//   deviceGroupRelationDevice({
//     resourceIds: val.ids[0],
//     id: val.expandId,
//   }).then((res) => {
//     if (res.success) {
//       ElMessage.success("操作成功");
//       getDetails({ id: val.expandId });
//       // getList();
//       // expandChange({
//       //   id: val.expandId,
//       // });
//     }
//   });
// }
// function expandChange(row, colum, event) {
//   expandList.value = colum.map((v) => v.id);
//   if (colum.length > 0) {
//     getDetails(row);
//   } else {
//     // refs.value.forEach((vm) => (vm.contacts = [[]]));
//     ctx.refs["bindDeviceRef" + row.id].contacts = [[]];
//   }
// }
function getList() {
  getdeviceGroupList({ ...searchForm.value }).then((res) => {
    if (res.success) {
      let data = [];
      res.data.forEach((item) => {
        data.push(item);
        // data.push({
        //   deviceList: [],
        // });
      });
      tableData.value = [...data];
      paging.total = res.data.length;
    } else {
      ElMessage.error(JSON.parse(res.data)?.message + "操作失败");
    }
  });
}
function deviceDialog(type, row) {
  ctx.refs.supplier.form.report = false;
  // ctx.refs.supplier.type = type;

  // if (type === "add") {
  //   ctx.refs.supplier.title = "新增设备组";
  //   ctx.refs.supplier.form = {
  //     name: "",
  //     description: "",
  //     report: false,

  //     alertClassificationIds: [],
  //   };
  // } else {
  //   ctx.refs.supplier.form = {
  //     name: row.name,
  //     description: row.description,
  //     report: row.report,
  //     id: row.id,
  //     alertClassificationIds: row.alertClassificationIds,
  //   };
  //   ctx.refs.supplier.title = "编辑设备组";
  // }
  setTimeout(() => {
    ctx.refs.supplier.open(type, row);
  }, 200);
  dialog.value = true;
}
function dialogClose(bool) {
  dialog.value = bool;
  getList();
  ctx.refs.supplier.form.report = false;
}
function delItem(row, index) {
  ElMessageBox.confirm(`确定删除${row.name}?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // tableData.value.length = tableData.value.length - 1;
      deletedeviceGroup({
        id: row.id,
      })
        .then((res) => {
          if (res.success) {
            ElMessage.success("删除成功");
            if (tableData.value.slice((paging.pageNumber - 1) * paging.pageSize, paging.pageNumber * paging.pageSize).length == 0) {
              paging.pageNumber = 1;
            }
            getList();
          } else {
            ElMessage.error(JSON.parse(res.data)?.message + "操作失败");
          }
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        });
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
