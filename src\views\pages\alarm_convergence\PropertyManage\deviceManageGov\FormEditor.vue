<template>
  <!-- 对话框表单 -->
  <el-dialog v-model="data.visible" :close-on-click-modal="false" append-to-body draggable :width="data.width" :before-close="(done) => handleCancel().then(() => done())">
    <template #header>
      <div class="title">
        {{ `${$params.id ? t("glob.edit") : t("glob.add")}${props.title}` }}
      </div>
    </template>
    <template #default>
      <el-scrollbar ref="contentRef" :max-height="data.height" :view-style="{ padding: '0 12px' }">
        <el-form :model="form" ref="formRef" :label-width="props.labelWidth" label-position="left" @submit.stop="handleFinish()">
          <el-row :gutter="24">
            <el-col :span="data.width > 380 ? 12 : 24">
              <el-form-item prop="deviceName" :rules="[{ required: true, type: 'string', message: '设备名称不能为空' }]">
                <template #label>
                  <el-text style="color: inherit">设备名称</el-text>
                  <!-- <el-tooltip content="" placement="top" effect="dark">
                    <el-icon color="inherit" style="height: 100%; margin-left: auto"><QuestionFilled></QuestionFilled></el-icon>
                  </el-tooltip> -->
                </template>
                <template #default>
                  <el-input v-model="form.deviceName" placeholder="请输入名称" clearable></el-input>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="data.width > 380 ? 12 : 24">
              <el-form-item prop="vendorId" :rules="[{ required: true, type: 'string', message: '设备厂商不能为空' }]">
                <template #label>
                  <el-text style="color: inherit">设备厂商</el-text>
                  <!-- <el-tooltip content="" placement="top" effect="dark">
                    <el-icon color="inherit" style="height: 100%; margin-left: auto"><QuestionFilled></QuestionFilled></el-icon>
                  </el-tooltip> -->
                </template>
                <template #default>
                  <el-select v-model="form.vendorId" placeholder="请输入或选择厂商" clearable filterable style="min-width: 100%">
                    <el-option v-for="item in vendorsList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                  </el-select>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="data.width > 380 ? 12 : 24">
              <el-form-item prop="sn" :rules="[{ required: true, type: 'string', message: 'SN序列号不能为空' }]">
                <template #label>
                  <el-text style="color: inherit">SN序列号</el-text>
                  <!-- <el-tooltip content="" placement="top" effect="dark">
                    <el-icon color="inherit" style="height: 100%; margin-left: auto"><QuestionFilled></QuestionFilled></el-icon>
                  </el-tooltip> -->
                </template>
                <template #default>
                  <el-input v-model="form.sn" placeholder="请输入SN序列号" clearable></el-input>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="data.width > 380 ? 12 : 24">
              <el-form-item prop="deviceModel" :rules="[{ required: true, type: 'string', message: '设备型号不能为空' }]">
                <template #label>
                  <el-text style="color: inherit">设备型号</el-text>
                  <!-- <el-tooltip content="" placement="top" effect="dark">
                    <el-icon color="inherit" style="height: 100%; margin-left: auto"><QuestionFilled></QuestionFilled></el-icon>
                  </el-tooltip> -->
                </template>
                <template #default>
                  <el-input v-model="form.deviceModel" placeholder="请输入设备型号" clearable></el-input>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="data.width > 380 ? 12 : 24">
              <el-form-item prop="linkNumber" :rules="[{ required: true, type: 'string', message: '链路编号不能为空' }]">
                <template #label>
                  <el-text style="color: inherit">链路编号</el-text>
                  <!-- <el-tooltip content="" placement="top" effect="dark">
                    <el-icon color="inherit" style="height: 100%; margin-left: auto"><QuestionFilled></QuestionFilled></el-icon>
                  </el-tooltip> -->
                </template>
                <template #default>
                  <el-input v-model="form.linkNumber" placeholder="请输入名称" clearable></el-input>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="data.width > 380 ? 12 : 24">
              <el-form-item label="是否激活" prop="active" :rules="[]">
                <el-checkbox v-model="form.active"></el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="data.width > 380 ? 12 : 24">
              <el-form-item label="mac地址" prop="mac" :rules="[]">
                <!-- <el-checkbox v-model="form.active"></el-checkbox> -->
                <el-input v-model="form.mac" placeholder="请输入mac地址" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item required>
                <template #label>
                  <el-text style="color: inherit">安装地址</el-text>
                  <!-- <el-tooltip content="" placement="top" effect="dark">
                    <el-icon color="inherit" style="height: 100%; margin-left: auto"><QuestionFilled></QuestionFilled></el-icon>
                  </el-tooltip> -->
                </template>
                <template #default>
                  <el-row style="width: 100%">
                    <el-col :span="data.width > 380 ? 8 : 24" :style="data.width > 380 ? { paddingLeft: '0px', paddingRight: '6px' } : { paddingTop: '0px', paddingBottom: '16px' }">
                      <el-form-item prop="province" label-width="auto" :rules="[{ required: true, type: 'string', message: '省份不能为空' }]">
                        <el-select v-model="form.province" placeholder="选择省" clearable style="min-width: 100%" autocapitalize="off" @change="() => (form.city = form.district = form.installAddress = '')">
                          <el-option v-for="item in provinceList" :key="`province_${item.code}`" :label="item.name" :value="item.name"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="data.width > 380 ? 8 : 24" :style="data.width > 380 ? { paddingLeft: '6px', paddingRight: '6px' } : { paddingTop: '2px', paddingBottom: '16px' }">
                      <el-form-item prop="city" label-width="auto" :rules="[{ required: true, type: 'string', message: '市不能为空' }]">
                        <el-select v-model="form.city" :disabled="!form.province || data.loading" placeholder="选择市" clearable style="min-width: 100%" autocapitalize="off" @change="() => (form.district = form.installAddress = '')">
                          <el-option v-for="item in cityList" :key="`area_${item.code}`" :label="item.name" :value="item.name"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="data.width > 380 ? 8 : 24" :style="data.width > 380 ? { paddingLeft: '6px', paddingRight: '0px' } : { paddingTop: '2px', paddingBottom: '0px' }">
                      <el-form-item prop="district" label-width="auto" :rules="[{ required: true, type: 'string', message: '区/县不能为空' }]">
                        <el-select v-model="form.district" :disabled="!form.city || data.loading" placeholder="选择区/县" clearable style="min-width: 100%" autocapitalize="off" @change="() => (form.installAddress = '')">
                          <el-option v-for="item in areaList" :key="`town_${item.code}`" :label="item.name" :value="item.name"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="installAddress" :rules="[{ required: true, type: 'string', message: '详细地址不能为空' }]">
                <template #default>
                  <!-- townList -->
                  <el-autocomplete v-model="form.installAddress" :disabled="!form.district || data.loading" :fetch-suggestions="($input, $cb) => $cb(townList.filter((v) => v.name !== $input && v.name.includes($input)).map((v) => ({ value: v.name })))" :trigger-on-focus="true" placeholder="请输入详细地址" clearable autocapitalize="off"></el-autocomplete>
                  <!-- <el-input v-model="form.installAddress" placeholder="请输入详细地址" clearable></el-input> -->
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label-width="auto">
                <el-divider direction="horizontal" style="margin: 0"></el-divider>
              </el-form-item>
            </el-col>
            <el-col :span="data.width > 380 ? 12 : 24">
              <el-form-item prop="installerName" :rules="[{ required: false, type: 'string', message: '安装人姓名不能为空' }]">
                <template #label>
                  <el-text style="color: inherit">安装人姓名</el-text>
                  <!-- <el-tooltip content="" placement="top" effect="dark">
                    <el-icon color="inherit" style="height: 100%; margin-left: auto"><QuestionFilled></QuestionFilled></el-icon>
                  </el-tooltip> -->
                </template>
                <template #default>
                  <el-input v-model="form.installerName" placeholder="请输入安装人姓名" clearable></el-input>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="data.width > 380 ? 12 : 24">
              <el-form-item prop="installerPhone" :rules="[{ required: false, type: 'string', message: '安装人电话不能为空' }]">
                <template #label>
                  <el-text style="color: inherit">安装人电话</el-text>
                  <!-- <el-tooltip content="" placement="top" effect="dark">
                    <el-icon color="inherit" style="height: 100%; margin-left: auto"><QuestionFilled></QuestionFilled></el-icon>
                  </el-tooltip> -->
                </template>
                <template #default>
                  <el-input v-model="form.installerPhone" placeholder="请输入安装人电话" clearable></el-input>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="data.width > 380 ? 12 : 24">
              <el-form-item prop="contactPerson" :rules="[{ required: false, type: 'string', message: '设备联系人不能为空' }]">
                <template #label>
                  <el-text style="color: inherit">设备联系人</el-text>
                  <!-- <el-tooltip content="" placement="top" effect="dark">
                    <el-icon color="inherit" style="height: 100%; margin-left: auto"><QuestionFilled></QuestionFilled></el-icon>
                  </el-tooltip> -->
                </template>
                <template #default>
                  <el-input v-model="form.contactPerson" placeholder="请输入设备联系人" clearable></el-input>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="data.width > 380 ? 12 : 24">
              <el-form-item prop="contactPhone" :rules="[{ required: false, type: 'string', message: '联系人电话不能为空' }]">
                <template #label>
                  <el-text style="color: inherit">联系人电话</el-text>
                  <!-- <el-tooltip content="" placement="top" effect="dark">
                    <el-icon color="inherit" style="height: 100%; margin-left: auto"><QuestionFilled></QuestionFilled></el-icon>
                  </el-tooltip> -->
                </template>
                <template #default>
                  <el-input v-model="form.contactPhone" placeholder="请输入联系人电话" clearable></el-input>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="desc" :rules="[{ required: false, type: 'string', message: '描述不能为空' }]">
                <template #label>
                  <el-text style="color: inherit">描述</el-text>
                  <!-- <el-tooltip content="" placement="top" effect="dark">
                    <el-icon color="inherit" style="height: 100%; margin-left: auto"><QuestionFilled></QuestionFilled></el-icon>
                  </el-tooltip> -->
                </template>
                <template #default>
                  <el-input v-model="form.desc" placeholder="请输入描述" clearable></el-input>
                </template>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-scrollbar>
    </template>
    <template #footer>
      <div>
        <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
        <el-button type="default" @click="handleCancel()" :disabled="data.loading" :loading="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
        <el-button type="primary" @click="handleFinish()" :disabled="data.loading" :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
        <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
      </div>
      <div class="zoom-handle" @mousedown.self="handleZoom">
        <svg style="display: block; width: 60%; height: 60%; transform: translate(-25%, -25%); fill: currentColor; pointer-events: none" viewBox="0 0 1024 1024">
          <path d="M319.20128 974.56128L348.16 1003.52l655.36-655.36-28.95872-28.95872-655.36 655.36zM675.84 1003.52l327.68-327.68-28.95872-28.95872-327.68 327.68L675.84 1003.52z" fill="#000000"></path>
        </svg>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, computed, h, createVNode, renderSlot, toRaw, toValue, inject } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { QuestionFilled } from "@element-plus/icons-vue";
import { templateRef } from "@vueuse/core";
import { buildTypeHelper } from "@/utils/type";
import { buildValidatorData } from "@/utils/validate";

import getUserInfo from "@/utils/getUserInfo";
import { catGovResourceData as catData, getInstallAddress } from "../../../apis/clientDeviceManage";
import { getVendorsList } from "@/views/pages/apis/supplier";
import provinceAddress from "@/assets/json/address/province.json";
import cityAddress from "@/assets/json/address/city.json";
import areaAddress from "@/assets/json/address/area.json";
import townAddress from "@/assets/json/address/town.json";

const provinceList = computed(() => provinceAddress as { code: string; name: string; province: string }[]);
const province = computed(() => toValue(provinceList).find((v) => v.name === toValue(form).province));
const cityList = computed(() => {
  // 四个直辖市 北京:11, 上海:31，天津:12，重庆市:50
  const $province = toValue(province);
  return $province ? (cityAddress as { code: string; name: string; province: string; city: string }[]).filter((v) => v.province === $province.province) : [];
});
const city = computed(() => toValue(cityList).find((v) => v.name === toValue(form).city));
const areaList = computed(() => {
  const $city = toValue(city);
  return $city ? (areaAddress as { code: string; name: string; province: string; city: string; area: string }[]).filter((v) => v.province === $city.province && v.city === $city.city) : [];
});
const area = computed(() => toValue(areaList).find((v) => v.name === toValue(form).district));
const townList = computed(() => {
  const $area = toValue(area);
  return $area ? (townAddress as { code: string; name: string; province: string; city: string; area: string; town: string }[]).filter((v) => v.province === $area.province && v.city === $area.city && v.area === $area.area) : [];
});
const town = computed(() => toValue(townList).find((v) => v.name === toValue(form).installAddress));

const userInfo = getUserInfo();

const formRef = templateRef<InstanceType<typeof import("element-plus").ElForm>>("formRef");
/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
type DeepUnReadonly<T> = T extends string | number | boolean | bigint | symbol | undefined | null | Function | Date | Error | RegExp ? T : T extends Map<infer K, infer V> ? ReadonlyMap<DeepUnReadonly<K>, DeepUnReadonly<V>> : T extends ReadonlyMap<infer K, infer V> ? ReadonlyMap<DeepUnReadonly<K>, DeepUnReadonly<V>> : T extends WeakMap<infer K, infer V> ? WeakMap<DeepUnReadonly<K>, DeepUnReadonly<V>> : T extends Set<infer U> ? ReadonlySet<DeepUnReadonly<U>> : T extends ReadonlySet<infer U> ? ReadonlySet<DeepUnReadonly<U>> : T extends WeakSet<infer U> ? WeakSet<DeepUnReadonly<U>> : T extends Promise<infer U> ? Promise<DeepUnReadonly<U>> : T extends {} ? { -readonly [K in keyof T]: DeepUnReadonly<T[K]> } : { -readonly [K in keyof T]: T[K] };
type Item<TT = DeepUnReadonly<{ [K in keyof typeof defaultForm]: (typeof defaultForm)[K] extends { readonly value: infer V } ? V : never }>> = { [K in keyof TT]: TT[K] };
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly({
  id: /** 设备ID */ buildTypeHelper(""),
  userId: /** 当前用户ID */ buildTypeHelper(""),
  deviceName: /** 设备名称 */ buildTypeHelper(""),
  vendorId: /** 设备厂商ID */ buildTypeHelper(""),
  sn: /** SN序列号 */ buildTypeHelper(""),
  deviceModel: /** 设备型号 */ buildTypeHelper(""),
  linkNumber: /** 链路编号 */ buildTypeHelper(""),
  province: /** 安装地址（省） */ buildTypeHelper(""),
  city: /** 安装地址（市） */ buildTypeHelper(""),
  district: /** 安装地址（区域） */ buildTypeHelper(""),
  installAddress: /** 安装地址（详细地址） */ buildTypeHelper(""),
  installerName: /** 安装人姓名 */ buildTypeHelper(""),
  installerPhone: /** 安装人电话 */ buildTypeHelper(""),
  contactPerson: /** 设备联系人 */ buildTypeHelper(""),
  contactPhone: /** 联系人电话 */ buildTypeHelper(""),

  desc: /** 描述 */ buildTypeHelper(""),

  active: buildTypeHelper(true),

  mac: buildTypeHelper(""),
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
const vendorsList = ref<ReturnType<typeof getVendorsList> extends Promise<{ data: infer T }> ? T : never>([]);
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  await Promise.all([
    (async (req) => {
      if (params.id) return;
      const { success, message, data } = await getInstallAddress(req);
      if (!success) throw Object.assign(new Error(message), { success, data });
      Object.assign($params.value, {
        province: data.province || "",
        city: data.city || "",
        district: data.district || "",
        installAddress: data.installAddress || "",
      });
    })({}),
    (async (req) => {
      const { success, message, data } = await getVendorsList(req);
      if (!success) throw Object.assign(new Error(message), { success, data });
      vendorsList.value = data instanceof Array ? data : [];
    })({ vendorType: "DEVICE" as const }),
    (async (req) => {
      if (!req) return;
      const { success, message, data } = await catData({ deviceId: req });
      if (!success) throw Object.assign(new Error(message), { success, data });
      Object.assign($params.value, {
        id: data.id,
        deviceName: data.name,
        vendorId: (data.vendorIds instanceof Array ? data.vendorIds : []).reduce((p, c) => (p ? p : c), ""),
        sn: data.sn,
        deviceModel: data.deviceModel,
        linkNumber: data.linkNumber,
        province: data.province,
        city: data.city,
        district: data.district,
        installAddress: data.installAddress,
        installerName: data.installerName,
        installerPhone: data.installerPhone,
        contactPerson: data.contactPerson,
        contactPhone: data.contactPhone,
        desc: data.description,
      });
    })(params.id as string | undefined),
    // (async (req) => {
    //   const { success, message, data } = await getVendorsList(req);
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   vendorList.value = data instanceof Array ? data : [];
    // })({}),
    // (async (req) => {
    //   const { success, message, data } = await getResourceTypeList(req);
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   deviceTypeList.value = data instanceof Array ? data : [];
    // })({}),
    // (async (req) => {
    //   const { success, message, data } = await getDeviceGroupList(req);
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   deviceGroupList.value = data instanceof Array ? data : [];
    // })({}),
    // //查询客户信息
    // (async (req) => {
    //   const { success, message, data } = await getTenantInfo(req);
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   containerId.value = data.containerId;
    // })({}),
  ]);
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  return form;
}
/* ---------------------------------------------------------‖ ↑↑ 钩子 Start ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE ACHIEVE  END  ========================================================= */
/**
 * TODO: 本地方法
 */
/*  */
/**
 * TODO: 窗口方法
 */
interface Props {
  title?: string;
  labelWidth?: number;
  width?: number;
  height?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  labelWidth: 86,
  width: 0,
  height: 0,
});

const { t } = useI18n();

interface EditorData {
  width: number;
  height: number;
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Partial<Item>) => void;
  reject?: (value: Partial<Item>) => void;
  callback?: (form: Item) => Promise<void>;
}
const data = reactive<EditorData>({
  width: document.body.clientWidth * 0.5 - 200,
  height: document.body.clientHeight - 260 - document.body.clientHeight * 0.3,
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }

  await nextTick();
  return (Object.entries(defaultForm) as [keyof Item, { readonly value: Item[keyof Item]; readonly test: (v: unknown) => v is Item[keyof Item]; readonly transfer: (fv: unknown, ov: Item[keyof Item]) => Item[keyof Item] }][]).reduce<Required<Item>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], cloneDeep(toRaw(util.value)))) });
    },
    form as Required<Item> satisfies Item
  );
}

async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => formRef.value.validate(resolve));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish() {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(toValue(form));

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick(() => close());
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : `${error}`);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel() {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick(() => close());
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : `${error}`);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  form.value = await getForm($params.value);

  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  $params.value = {};
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {}) as Item;
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();
const contentRef = ref<InstanceType<typeof import("element-plus").ElScrollbar>>();

defineExpose({
  close: handleCancel,
  async opener(params: Record<string, unknown>, callback?: (form: Item) => Promise<void>): Promise<unknown> {
    if (data.visible) await handleCancel();
    const wait = new Promise<Record<string, unknown>>((resolve, reject) => ((data.resolve = resolve), (data.reject = reject)));
    $params.value = cloneDeep(params);
    // form.value = { ...params };
    data.visible = true;
    data.loading = true;
    data.submitLoading = true;
    data.callback = callback;

    await initSize();
    await nextTick();
    try {
      await runningInit($params.value);
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      await handleCancel();
    }
    await handleReset();
    data.loading = false;
    data.submitLoading = false;

    try {
      return await wait;
    } catch (error) {
      return error;
    }
  },
});

const $width = inject<import("vue").Ref<number>>("width", ref(document.body.clientWidth - 200));
const $height = inject<import("vue").Ref<number>>("height", ref(document.body.clientHeight - 260 - document.body.clientHeight * 0.15));

function handleZoom($event: MouseEvent) {
  const $content = toValue(contentRef);
  if (!$content) return;
  const $view = toValue($content.wrapRef);
  if (!$view) return;

  const w = data.width;
  const h = Number(getComputedStyle($view).getPropertyValue("height").replace("px", ""));
  ($event.target as HTMLElement).ownerDocument.onmousemove = (e: MouseEvent) => {
    e.preventDefault();
    if (w + (e.clientX - $event.clientX) * 2 < document.body.clientWidth - 200) data.width = w + (e.clientX - $event.clientX) * 2 > 360 ? w + (e.clientX - $event.clientX) * 2 : 360;
    else data.width = document.body.clientWidth - 200;
    if (h + (e.clientY - $event.clientY) * 1 < document.body.clientHeight - 260 - document.body.clientHeight * 0.15) data.height = h + (e.clientY - $event.clientY) * 1 > 24 ? h + (e.clientY - $event.clientY) * 1 : 24;
    else data.height = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  };
  ($event.target as HTMLElement).ownerDocument.onmouseup = (e: MouseEvent) => {
    (e.target as HTMLElement).ownerDocument.onmousemove = null;
    (e.target as HTMLElement).ownerDocument.onmouseup = null;
  };
}
async function initSize() {
  await nextTick();
  const $content = toValue(contentRef);
  if (!$content) return;
  const $view = toValue($content.wrapRef);
  if (!$view) return;

  const w = document.body.clientWidth * 0.5 - 200;
  const h = Number(getComputedStyle($view).getPropertyValue("height").replace("px", ""));

  if (w < document.body.clientWidth - 200) data.width = w > 360 ? w : 360;
  else data.width = document.body.clientWidth - 200;
  if (h < document.body.clientHeight - 260 - document.body.clientHeight * 0.15) data.height = h > 24 ? h : 24;
  else data.height = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
}
</script>

<style scoped lang="scss"></style>
