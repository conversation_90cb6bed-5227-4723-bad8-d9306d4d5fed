<template>
  <!-- 对话框表单 -->
  <el-dialog v-model="data.visible" :close-on-click-modal="false" append-to-body draggable :width="data.width" :before-close="(done) => handleCancel().then(() => done())">
    <template #header>
      <div class="title">
        {{ `${$params.id ? t("glob.edit") : t("glob.add")}${props.title}` }}
      </div>
    </template>
    <template #default>
      <el-scrollbar ref="contentRef" :max-height="data.height" :view-style="{ padding: '0 12px' }">
        <el-form :model="form" ref="formRef" :label-width="props.labelWidth" label-position="left" @submit.stop="handleFinish()">
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item prop="tenantName" :rules="[{ required: true, type: 'string', message: '客户名称不能为空' }]">
                <template #label>
                  <el-text style="color: inherit">客户名称</el-text>
                  <!-- <el-tooltip content="" placement="top" effect="dark">
                    <el-icon color="inherit" style="height: 100%; margin-left: auto"><QuestionFilled></QuestionFilled></el-icon>
                  </el-tooltip> -->
                </template>
                <template #default>
                  <el-input v-model="form.tenantName" placeholder="请输入客户名称" clearable></el-input>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                prop="tenantIdent"
                :rules="[
                  { required: true, type: 'string', message: '客户标识不能为空' },
                  { required: true, type: 'string', pattern: /^[0-9]+$/g, message: '客户标识为数字组成' },
                ]"
              >
                <template #label>
                  <el-text style="color: inherit">客户标识</el-text>
                </template>
                <template #default>
                  <el-input v-model="form.tenantIdent" placeholder="请输入客户标识" clearable></el-input>
                </template>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-scrollbar>
    </template>
    <template #footer>
      <div>
        <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
        <el-button type="default" @click="handleCancel()" :disabled="data.loading" :loading="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
        <el-button type="primary" @click="handleFinish()" :disabled="data.loading" :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
        <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
      </div>
      <div class="zoom-handle" @mousedown.self="handleZoom">
        <svg style="display: block; width: 60%; height: 60%; transform: translate(-25%, -25%); fill: currentColor; pointer-events: none" viewBox="0 0 1024 1024">
          <path d="M319.20128 974.56128L348.16 1003.52l655.36-655.36-28.95872-28.95872-655.36 655.36zM675.84 1003.52l327.68-327.68-28.95872-28.95872-327.68 327.68L675.84 1003.52z" fill="#000000"></path>
        </svg>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, computed, h, createVNode, renderSlot, toRaw, toValue, inject } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { QuestionFilled } from "@element-plus/icons-vue";
import { templateRef } from "@vueuse/core";
import { buildTypeHelper } from "@/utils/type";
import { buildValidatorData } from "@/utils/validate";

import { type GovTenantItem, getGovTenantList, addGovTenantData as addData, modGovTenantData } from "../../../apis/clientDeviceManage";
import getUserInfo from "@/utils/getUserInfo";

const userInfo = getUserInfo();

const formRef = templateRef<InstanceType<typeof import("element-plus").ElForm>>("formRef");
/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
type DeepUnReadonly<T> = T extends string | number | boolean | bigint | symbol | undefined | null | Function | Date | Error | RegExp ? T : T extends Map<infer K, infer V> ? ReadonlyMap<DeepUnReadonly<K>, DeepUnReadonly<V>> : T extends ReadonlyMap<infer K, infer V> ? ReadonlyMap<DeepUnReadonly<K>, DeepUnReadonly<V>> : T extends WeakMap<infer K, infer V> ? WeakMap<DeepUnReadonly<K>, DeepUnReadonly<V>> : T extends Set<infer U> ? ReadonlySet<DeepUnReadonly<U>> : T extends ReadonlySet<infer U> ? ReadonlySet<DeepUnReadonly<U>> : T extends WeakSet<infer U> ? WeakSet<DeepUnReadonly<U>> : T extends Promise<infer U> ? Promise<DeepUnReadonly<U>> : T extends {} ? { -readonly [K in keyof T]: DeepUnReadonly<T[K]> } : { -readonly [K in keyof T]: T[K] };
type Item<TT = DeepUnReadonly<{ [K in keyof typeof defaultForm]: (typeof defaultForm)[K] extends { readonly value: infer V } ? V : never }>> = { [K in keyof TT]: TT[K] };
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly({
  id: buildTypeHelper(""),
  tenantName: buildTypeHelper(""),
  tenantIdent: buildTypeHelper(""),
  containerId: buildTypeHelper(""),
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  await Promise.all([
    // (async (req) => {
    //   const { success, message, data } = await getRegionsList(req);
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   regionTree.value = buildTree<import("@/views/pages/apis/device").RegionsItem>(data instanceof Array ? data : []);
    // })({}),
    // (async (req) => {
    //   const { success, message, data } = await getLocationList(req);
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   locationList.value = data instanceof Array ? data : [];
    // })({}),
    // (async (req) => {
    //   const { success, message, data } = await getAlertClassificationsList(req);
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   alarmList.value = data instanceof Array ? data : [];
    // })({}),
    // (async (req) => {
    //   const { success, message, data } = await getVendorsList(req);
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   vendorList.value = data instanceof Array ? data : [];
    // })({}),
    // (async (req) => {
    //   const { success, message, data } = await getResourceTypeList(req);
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   deviceTypeList.value = data instanceof Array ? data : [];
    // })({}),
    // (async (req) => {
    //   const { success, message, data } = await getDeviceGroupList(req);
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   deviceGroupList.value = data instanceof Array ? data : [];
    // })({}),
    // //查询客户信息
    // (async (req) => {
    //   const { success, message, data } = await getTenantInfo(req);
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   containerId.value = data.containerId;
    // })({}),
  ]);
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  return form;
}
/* ---------------------------------------------------------‖ ↑↑ 钩子 Start ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE ACHIEVE  END  ========================================================= */
/**
 * TODO: 本地方法
 */
/*  */
/**
 * TODO: 窗口方法
 */
interface Props {
  title?: string;
  labelWidth?: number;
  width?: number;
  height?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  labelWidth: 86,
  width: 0,
  height: 0,
});

const { t } = useI18n();

interface EditorData {
  width: number;
  height: number;
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Partial<Item>) => void;
  reject?: (value: Partial<Item>) => void;
  callback?: (form: Item) => Promise<void>;
}
const data = reactive<EditorData>({
  width: document.body.clientWidth * 0.5 - 200,
  height: document.body.clientHeight - 260 - document.body.clientHeight * 0.3,
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }

  await nextTick();
  return (Object.entries(defaultForm) as [keyof Item, { readonly value: Item[keyof Item]; readonly test: (v: unknown) => v is Item[keyof Item]; readonly transfer: (fv: unknown, ov: Item[keyof Item]) => Item[keyof Item] }][]).reduce<Required<Item>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], cloneDeep(toRaw(util.value)))) });
    },
    form as Required<Item> satisfies Item
  );
}

async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => formRef.value.validate(resolve));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish() {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(toValue(form));

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick(() => close());
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : `${error}`);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel() {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick(() => close());
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : `${error}`);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  form.value = await getForm($params.value);

  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  $params.value = {};
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {}) as Item;
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();
const contentRef = ref<InstanceType<typeof import("element-plus").ElScrollbar>>();

defineExpose({
  close: handleCancel,
  async opener(params: Record<string, unknown>, callback?: (form: Item) => Promise<void>): Promise<unknown> {
    if (data.visible) await handleCancel();
    const wait = new Promise<Record<string, unknown>>((resolve, reject) => ((data.resolve = resolve), (data.reject = reject)));
    $params.value = cloneDeep(params);
    // form.value = { ...params };
    data.visible = true;
    data.loading = true;
    data.submitLoading = true;
    data.callback = callback;

    await initSize();
    await nextTick();
    try {
      await runningInit($params.value);
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      await handleCancel();
    }
    await handleReset();
    data.loading = false;
    data.submitLoading = false;

    try {
      return await wait;
    } catch (error) {
      return error;
    }
  },
});
function handleZoom($event: MouseEvent) {
  const $content = toValue(contentRef);
  if (!$content) return;
  const $view = toValue($content.wrapRef);
  if (!$view) return;

  const w = data.width;
  const h = Number(getComputedStyle($view).getPropertyValue("height").replace("px", ""));
  ($event.target as HTMLElement).ownerDocument.onmousemove = (e: MouseEvent) => {
    e.preventDefault();
    if (w + (e.clientX - $event.clientX) * 2 < document.body.clientWidth - 200) data.width = w + (e.clientX - $event.clientX) * 2 > 360 ? w + (e.clientX - $event.clientX) * 2 : 360;
    else data.width = document.body.clientWidth - 200;
    if (h + (e.clientY - $event.clientY) * 1 < document.body.clientHeight - 260 - document.body.clientHeight * 0.15) data.height = h + (e.clientY - $event.clientY) * 1 > 24 ? h + (e.clientY - $event.clientY) * 1 : 24;
    else data.height = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  };
  ($event.target as HTMLElement).ownerDocument.onmouseup = (e: MouseEvent) => {
    (e.target as HTMLElement).ownerDocument.onmousemove = null;
    (e.target as HTMLElement).ownerDocument.onmouseup = null;
  };
}
async function initSize() {
  await nextTick();
  const $content = toValue(contentRef);
  if (!$content) return;
  const $view = toValue($content.wrapRef);
  if (!$view) return;

  const w = document.body.clientWidth * 0.5 - 200;
  const h = Number(getComputedStyle($view).getPropertyValue("height").replace("px", ""));

  if (w < document.body.clientWidth - 200) data.width = w > 360 ? w : 360;
  else data.width = document.body.clientWidth - 200;
  if (h < document.body.clientHeight - 260 - document.body.clientHeight * 0.15) data.height = h > 24 ? h : 24;
  else data.height = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
}
</script>

<style scoped lang="scss"></style>
