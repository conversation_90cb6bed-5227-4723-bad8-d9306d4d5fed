<template>
  <div :style="{ height: 'calc(100% - 28px)' }" class="device-detail">
    <el-page-header @back="backRouter">
      <template #content>
        <el-row class="title" :gutter="24">
          <el-col :span="12">
            <p>{{ form.name }}</p>
          </el-col>
          <el-col :span="12" class="tw-text-right" v-if="(form.vendorName || '') === xnSION">
            <el-dropdown @command="handleRemoteLogin" class="tw-mr-2">
              <el-button type="primary">
                远程登录
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="ssh">SSH</el-dropdown-item>
                  <el-dropdown-item command="http">HTTP</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-dropdown @command="handleExportFile" class="tw-mr-2">
              <el-button type="primary">
                配置导入/导出
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="export">导出配置到本地</el-dropdown-item>
                  <el-dropdown-item command="import">导入本地配置文件</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-button type="primary" @click="handleRestart">{{ `重启` }}</el-button>
          </el-col>
        </el-row>
      </template>
    </el-page-header>
    <div class="details" :style="{ height: 'calc(100% - 60px)' }">
      <div class="device-detail-tab-list">
        <el-tabs v-model="activeName">
          <el-tab-pane label="详情" name="详情">
            <div class="message">
              <div>
                <ul>
                  <li>
                    <span>设备名称</span>
                    <span>{{ form.name || "--" }}</span>
                  </li>
                  <li>
                    <span>所属客户</span>
                    <span>{{ form.customerName || "--" }}</span>
                  </li>
                  <li>
                    <span>是否激活</span>
                    <span>
                      <el-text :type="form.active ? 'primary' : 'danger'">{{ form.active ? "激活" : "未激活" }}</el-text>
                    </span>
                  </li>
                  <li>
                    <span>是否在线</span>
                    <span>
                      <el-text :type="deviceOnline[$route.params.id] ? 'success' : 'danger'">{{ deviceOnline[$route.params.id] ? "在线" : "不在线" }}</el-text>
                    </span>
                  </li>
                  <li>
                    <span>安装地址</span>
                    <span>{{ form.city }}{{ form.district }}{{ form.installAddress }}</span>
                  </li>
                  <li>
                    <span>设备厂商</span>
                    <span>{{ form.vendorName || "--" }}</span>
                  </li>
                  <li>
                    <span>SN序列号</span>
                    <span style="-webkit-line-clamp: 10">
                      {{ form.sn }}
                    </span>
                  </li>
                  <li>
                    <span>设备型号</span>
                    <el-tooltip class="box-item" effect="light" placement="top">
                      <template #content>
                        <p style="max-width: 500px">
                          <span>{{ form.deviceModel }}</span>
                        </p>
                      </template>
                      <span>{{ form.deviceModel }}</span>
                    </el-tooltip>
                  </li>
                  <li>
                    <span>链路编号</span>
                    <span>{{ form.linkNumber || "--" }}</span>
                  </li>
                  <li>
                    <span>mac地址</span>
                    <span>{{ form.mac || "--" }}</span>
                  </li>
                  <li>
                    <span>安装人姓名</span>
                    <span> {{ form.installerName || "--" }}</span>
                  </li>
                  <li>
                    <span>安装人电话</span>
                    <span>{{ form.installerPhone || "--" }}</span>
                  </li>
                  <li>
                    <span>设备联系人</span>
                    <span>{{ form.contactPerson || "--" }}</span>
                  </li>
                  <li>
                    <span>联系人电话</span>
                    <span>{{ form.contactPhone || "--" }}</span>
                  </li>
                  <li>
                    <span>描述</span>
                    <span>{{ form.description || "--" }}</span>
                  </li>
                </ul>
              </div>
            </div>
          </el-tab-pane>
          <!-- 和产品确认中文判断厂商是否为欣诺 -->
          <template v-if="(form.vendorName || '') === xnSION">
            <el-tab-pane label="端口信息" name="端口信息">
              <port v-if="activeName === '端口信息'"></port>
            </el-tab-pane>
            <el-tab-pane label="VLAN配置" name="VLAN配置">
              <vlan v-if="activeName === 'VLAN配置'"></vlan>
            </el-tab-pane>
          </template>

          <el-tab-pane v-if="userInfo.hasPermission(监控管理中心_告警_可读)" label="告警" name="告警">
            <page-template :search-span="24" height="650" :showPaging="true" v-model:current-page="alarmPaging.pageNumber" v-model:page-size="alarmPaging.pageSize" :total="alarmPaging.total" @current-change="getAlarmList()" @size-change="getAlarmList()">
              <template #default="{}">
                <el-table :data="alarmList" stripe style="width: 100%" border :height="tableHeight" class="device-alarm-table">
                  <TableColumn type="enum" show-filter v-model:filtered-value="alarmObj.eventSeverity" @filter-change="handleQuery('alarm')" prop="eventSeverity" label="紧急性" :width="130" :filters="eventSeverityOption.map((v) => ({ ...v, text: v.label }))"></TableColumn>

                  <TableColumn type="order" prop="title" label="告警" show-filter v-model:many-filtered-value="search.alarm.alarmObj" @filter-change="handleQuery('alarm')">
                    <template #default="{ row }">
                      <div>
                        <p>{{ row.title }}</p>
                        <p>{{ row.desc }}</p>
                      </div>
                    </template>
                  </TableColumn>

                  <TableColumn type="order" prop="title" label="工单" show-filter v-model:many-filtered-value="search.alarm.alarmOrderObj" @filter-change="handleQuery('alarm')">
                    <template #default="{ row }">
                      <p v-for="item in row.orders" :key="item.orderId">
                        <span class="priority tw-rounded-full tw-px-3 tw-py-1 tw-text-white" :style="{ background: priority[item.priority].color }">
                          {{ item ? item.priority : "" }}
                        </span>
                        &nbsp;&nbsp;&nbsp;

                        <span style="color: #409eff; cursor: pointer" @click="routerOrder(item, '告警')">{{ item.orderId ? item.orderId : "" }}</span>
                        &nbsp;&nbsp;&nbsp;
                        <span>{{ item.state ? eventStatus[item.state]?.value : "" }}</span>
                      </p>
                      {{ row.orders.length < 1 ? "--" : "" }}
                    </template>
                  </TableColumn>
                  <TableColumn type="date" show-filter v-model:filtered-value="alarmObj.alertCreateTimeRange" filter-multiple @filter-change="handleQuery('alarm')" prop="alertCreateTime" label="时间" :width="160"></TableColumn>
                  <TableColumn type="date" show-filter v-model:filtered-value="alarmObj.alarmBoardConfirmedTime" filter-multiple @filter-change="handleQuery('alarm')" prop="alarmBoardConfirmedTime" label="告警确认时间" :width="160"></TableColumn>

                  <el-table-column prop="alarmBoardConfirmedPerson.username" label="响应人">
                    <template #default="{ row }">
                      <div>
                        {{ JSON.parse(row.alarmBoardConfirmedPerson) ? JSON.parse(row.alarmBoardConfirmedPerson).username : "--" }}
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </page-template>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <el-dialog v-model="importVisible" title="导入配置" :before-close="importBeforeClose" width="500">
      <el-upload ref="importRef" class="upload-demo" drag action :auto-upload="false" :on-change="(v) => (importFileList = v)">
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将 bpf 文件拖到此处，或 <em>点击上传</em></div>
        <!-- <template #tip>
          <div class="el-upload__tip">jpg/png files with a size less than 500kb</div>
        </template> -->
      </el-upload>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importBeforeClose">取 消</el-button>
          <el-button type="primary" @click="handleImportSubmit"> 确 定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import pageTemplate from "@/components/pageTemplate";
import moment from "moment";
import { eventSeverityOption } from "@/views/pages/apis/event";
import { OrderType, DataType } from "@/views/pages/apis/deviceManage";
import { eventStatus } from "../../deviceManage/common";
import { getGovResourceData, restartDevice, remoteSshAc, backupDevices, uploadSwitchConfig } from "@/views/pages/apis/clientDeviceManage";
import { getRemoteOnlineByResources } from "@/views/pages/apis/device";
import { getAlarmBoardDetail } from "@/views/pages/apis/alarmBoard";
import priority from "../../../../common/priority";
import eventColor from "../../../../common/eventColor";
import getUserInfo from "@/utils/getUserInfo";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { 监控管理中心_告警_可读 } from "@/views/pages/permission";

import { getDevicePort } from "@/views/pages/apis/clientDeviceManage";
import { ElMessage, ElMessageBox, ElIcon } from "element-plus";
import { ArrowDown, UploadFilled } from "@element-plus/icons-vue";

import port from "./module/port.vue";
import vlan from "./module/vlan.vue";

const userInfo = getUserInfo();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */

export default {
  name: "DeviceManageGovDetails",
  components: {
    pageTemplate,
    TableColumn,
    port,
    vlan,
    ElIcon,
    ArrowDown,
    UploadFilled,
  },
  provide() {
    return {
      detail: this.form,
      sinoDevice: this.sinoDevice,
    };
  },
  data() {
    return {
      eventSeverityOption,
      userInfo,
      paging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      priority: priority,
      eventColor: eventColor,
      eventStatus: eventStatus,
      activeName: "详情",
      content: "设备详情",
      id: this.$route.params.id,
      tableHeight: "550",
      form: {
        name: null,
        installAddress: null,
        customerName: null,
        vendorName: null,
        sn: null,
        deviceModel: null,
        linkNumber: null,
        installerName: null,
        installerPhone: null,
        contactPerson: null,
        contactPhone: null,
        description: null,
        city: null,
        district: null,
      },

      alarmList: [],
      alarmPaging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      OrderType: OrderType,
      DataType: DataType,
      search: {
        orderType: {
          status: "include",
          type: "include",
          relation: "AND",
          firstName: "",
          lastName: "",
        },
        orderId: {
          status: "include",
          type: "include",
          relation: "AND",
          firstName: "",
          lastName: "",
        },
        orderSummary: {
          status: "include",
          type: "include",
          relation: "AND",
          firstName: "",
          lastName: "",
        },
        alarm: {
          eventSeverity: "",
          alarmObj: {
            status: "include",
            type: "include",
            relation: "AND",
            firstName: "",
            lastName: "",
          },
          alarmOrderObj: {
            status: "include",
            type: "include",
            relation: "AND",
            firstName: "",
            lastName: "",
          },
        },
      },
      state: {
        includeOrderType: [],
        excludeOrderType: [],
        includeOrderSummary: [],
        excludeOrderSummary: [],
        includeOrderIds: [],
        excludeOrderIds: [],
        orderTypeRelation: "AND",
        inOrderId: [],
        neInOrderId: [],
        inOrderSummary: [],
        neInOrderSummary: [],

        orderIdFilterRelation: "AND",

        orderSummaryFilterRelation: "AND",
      },
      alarmObj: {
        eventSeverity: "",
        includeAlerts: [],
        alertFilterRelation: "AND",
        excludeAlerts: [],
        includeOrderIds: [],
        orderIdFilterRelation: "AND",
        excludeOrderIds: [],
        alertCreateTimeRange: "",
        alarmBoardConfirmedTime: "",
      },
      监控管理中心_告警_可读,

      deviceOnline: {},

      sinoDevice: {},

      importVisible: false,
      importFileList: [],

      xnSION: "欣诺(SION)",
    };
  },
  watch: {},
  mounted() {
    this.id = this.$route.params.id;
    this.getDetail(); //设备详情
    this.getAlarmList();
  },
  beforeUnmount() {
    // 清理工作或者取消订阅等操作
    sessionStorage.setItem("deviceTab", "");
  },
  methods: {
    async handleImportSubmit() {
      try {
        const formData = new FormData();
        formData.append("file", this.importFileList.raw);
        const { message, success } = await uploadSwitchConfig({ resourceId: this.form.id }, formData);
        if (!success) throw new Error(message);
        this.$message.success("操作成功");
        this.$nextTick(this.importBeforeClose);
      } catch (error) {
        error instanceof Error && this.$message.error(error.message);
      }
    },
    importBeforeClose() {
      this.$refs.importRef && this.$refs.importRef.clearFiles();
      this.$nextTick(() => (this.importVisible = false));
    },
    async handleExportFile(_type) {
      try {
        switch (_type) {
          case "export":
            const { data, message, success } = await backupDevices({ resourceIds: this.form.id });
            if (!success) throw new Error(message);
            window.open(data.downLoadUrl);
            break;
          case "import":
            this.importVisible = true;
            break;
          default:
            break;
        }
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    },
    async handleRemoteLogin(tunnelType) {
      try {
        const { data, message, success } = await remoteSshAc({ tunnelType, resourceId: this.form.id });
        if (!success) throw new Error(message);
        let _href;
        switch (tunnelType) {
          case "ssh":
            _href = `http://${data.tunnelHost}:8888?hostname=${data.tunnelHost}&port=${data.port}&encode=gbk&onopen=sshOpen?${this.sinoDevice.id},${this.sinoDevice.serialNum},${tunnelType}&onclose=sshClose?${this.sinoDevice.id},${this.sinoDevice.serialNum},${tunnelType}`;
            break;
          case "http":
            _href = `http://${data.tunnelHost}:${data.port}/login.asp`;
            break;
          default:
            break;
        }
        _href && window.open(_href, "_blank");
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    },
    async handleGetPort() {
      try {
        if ((this.form.vendorName || "") !== this.xnSION) return;
        const { data, success, message } = await getDevicePort({ mac: this.form.mac });
        if (!success) throw new Error(message);
        this.sinoDevice = data.deviceInfo;
      } catch (error) {
        error instanceof Error && this.$message.error(error.message);
      }
    },
    handleRestart() {
      ElMessageBox.confirm("是否确认重启?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          /* code */
          try {
            const { message, success } = await restartDevice({ ccDeviceIds: this.form.id });
            if (!success) throw new Error(message);
            ElMessage.success("操作成功");
            this.getDetail();
          } catch (error) {
            error instanceof Error && this.$message.error(error.message);
          }
        })
        .catch(() => {
          /* code */
        });
    },
    moment,
    handleQuery(type) {
      this.state.includeOrderType = [];
      this.state.excludeOrderType = [];
      this.state.includeOrderSummary = [];
      this.state.excludeOrderSummary = [];
      this.state.includeOrderIds = [];
      this.state.excludeOrderIds = [];
      this.state.inOrderId = [];
      this.state.neInOrderId = [];
      this.state.inOrderSummary = [];
      this.state.neInOrderSummary = [];
      this.state.orderTypeRelation = this.search.orderType.relation;
      this.state.orderIdFilterRelation = this.search.orderId.relation;
      this.state.orderSummaryFilterRelation = this.search.orderSummary.relation;

      this.alarmObj.includeAlerts = [];
      this.alarmObj.excludeAlerts = [];
      this.alarmObj.includeOrderIds = [];
      this.alarmObj.excludeOrderIds = [];
      this.alarmObj.alertFilterRelation = this.search.alarm.alarmObj.relation;
      this.alarmObj.orderIdFilterRelation = this.search.alarm.alarmOrderObj.relation;

      //类型
      this.setFilterMessage(this.state.includeOrderType, this.state.excludeOrderType, [], [], this.search.orderType);
      //工单号
      this.setFilterMessage(this.state.includeOrderIds, this.state.excludeOrderIds, this.state.inOrderId, this.state.neInOrderId, this.search.orderId);
      //摘要
      this.setFilterMessage(this.state.includeOrderSummary, this.state.excludeOrderSummary, this.state.inOrderSummary, this.state.neInOrderSummary, this.search.orderSummary);

      //告警工单
      this.setFilterMessage(this.alarmObj.includeOrderIds, this.alarmObj.excludeOrderIds, [], [], this.search.alarm.alarmOrderObj);

      //告警摘要
      this.setFilterMessage(this.alarmObj.includeAlerts, this.alarmObj.excludeAlerts, [], [], this.search.alarm.alarmObj);

      if (typeof type === "string") {
        setTimeout(() => {
          this.getAlarmList();
        });
      } else {
        setTimeout(() => {
          this.getWorkOrderList();
        });
      }
    },
    setFilterMessage(includeData: any, excludeData: any, beData: any, notBeData: any, type: any) {
      switch (type.status) {
        case "include":
          includeData.push(type.firstName);

          break;
        case "exclude":
          excludeData.push(type.firstName);
          break;
        case "be":
          beData.push(type.firstName);
          break;
        case "notBe":
          notBeData.push(type.firstName);
          break;
      }

      switch (type.type) {
        case "include":
          includeData.push(type.lastName);
          break;
        case "exclude":
          excludeData.push(type.lastName);
          break;
        case "be":
          beData.push(type.firstName);
          break;
        case "notBe":
          notBeData.push(type.firstName);
          break;
      }
    },
    getAlarmList() {
      getAlarmBoardDetail({
        deviceId: this.id,
        ...this.alarmPaging,
        sort: "alertCreateTime,desc",
        ...this.alarmObj,
      }).then((res) => {
        if (res.success) {
          this.alarmList = [...res.data];
          this.alarmPaging.total = res.total * 1;
        }
      });
    },
    backRouter() {
      if (this.$route.query.fallback && typeof this.$route.query.fallback === "string") {
        this.$router.push({
          name: this.$route.query.fallback,
          params: { id: this.$route.query.eventId },
        });
      } else {
        this.$router.back();
      }
    },
    //设备详情
    async getDetail() {
      const res = await getGovResourceData({ deviceId: this.id });
      if (res.success) {
        this.getDeviceOnline();
        const { name, customerName, installAddress, vendorName, sn, deviceModel, linkNumber, installerName, installerPhone, contactPerson, contactPhone, description, city, district, active, mac, id } = res.data;

        Object.assign(this.form, {
          id: id,
          active: active ?? "--",
          name: name ?? "--",
          customerName: customerName ?? "--",
          installAddress: installAddress ?? "--",
          vendorName: vendorName ?? "--",
          sn: sn ?? "--",
          deviceModel: deviceModel ?? "--",
          linkNumber: linkNumber ?? "--",
          installerName: installerName ?? "--",
          installerPhone: installerPhone ?? "--",
          contactPerson: contactPerson ?? "--",
          contactPhone: contactPhone ?? "--",
          description: description ?? "--",
          city: city,
          district: district,
          mac: mac ?? "--",
        });
        this.$nextTick(this.handleGetPort);
      }
    },

    async getDeviceOnline() {
      const { data, message, success } = await getRemoteOnlineByResources({ ids: [this.$route.params.id] });
      if (!success) throw new Error(message);

      this.deviceOnline = data.reduce((t, c) => Object.assign(t, { [c.resourceId]: c.online }), {});
    },
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-page-header__header) {
  display: flex;
  background: #fff;
  padding: 10px 20px 0 20px;
  // padding-top: 10px;

  .el-page-header__left {
    flex: 1;
  }
  .el-page-header__content {
    flex: 1;
  }
}
:deep(.el-button--text:not(.is-disabled):focus) {
  color: #409eff;
}

.device-alarm-table {
  :deep(.cell) {
    line-height: 30px;
  }
  margin-top: -50px;
}

.device-detail {
  overflow-y: auto;
}
.device-detail-tab-list {
  padding: 0 20px;
  box-sizing: border-box;
  background: #fff;
  :deep(.el-tabs__content) {
    overflow-y: hidden;
  }
}

.details {
  background: #fff;
}
.title {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  p {
    line-height: 30px;
    font-size: 18px;
  }
}
.device-img {
  display: flex;
  > img {
    margin-right: 8px;
    cursor: pointer;
    width: 25px;
    height: auto;
  }
}

.message {
  width: 100%;
  height: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  > div {
    flex: none;
    width: 48%;
    border: 1px solid #ddd;
    padding: 15px;
    box-sizing: border-box;
    > ul {
      width: 100%;
    }
  }

  ul > li {
    width: 100%;
    display: flex;
    justify-content: space-between;
    min-height: 40px;
    align-items: center;
    padding: 10px 15px 0;
    box-sizing: border-box;

    > span:first-child {
      display: flex;
      min-width: 120px;
      flex: none;
      justify-content: flex-start;
    }
    > span {
      flex: 1;
      display: flex;
      //
      flex-wrap: wrap;
      justify-content: flex-end;
      word-wrap: break-word;
      word-break: break-all;
    }
    > span:nth-child(even) {
      // flex: none;
      width: calc(100% - 120px);
      text-align: right;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
  ul > li:nth-child(odd) {
    border-bottom: 1px solid #ddd;
    background: rgb(241, 241, 241);
  }
  .monitor {
    display: flex;
    flex-direction: column;
    border: 0 !important;
    padding: 0;
    > div {
      flex: 1;
      border: 1px solid #ddd;
      padding: 15px;
      box-sizing: border-box;
    }
    > div:last-child {
      flex: none;
      height: 49%;
      margin-top: 1%;
    }
  }
}
</style>
