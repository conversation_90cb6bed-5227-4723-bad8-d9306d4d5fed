<template>
  <el-card shadow="never" style="margin: auto" :body-style="{ padding: '16px', height: `${$height}px`, width: `${$width}px`, display: 'flex', flexDirection: 'column', lineHeight: '18px' }">
    <el-page-header @back="router.back()">
      <template #content>
        <el-text style="font-size: 16px; line-height: inherit; font-weight: bold">{{ route.meta.title }}</el-text>
      </template>
      <template #extra></template>
      <template #default>
        <el-tabs v-model="active" tab-position="top">
          <el-tab-pane v-for="tab in tabs" :key="tab.value" :label="tab.label" :name="tab.value">
            <component v-if="active === tab.value" :is="tab.Component" :height="$height - 127" :width="$width - 32"></component>
          </el-tab-pane>
        </el-tabs>
      </template>
    </el-page-header>
  </el-card>
</template>

<script lang="ts" setup>
import { ref, inject, computed, shallowRef } from "vue";
import { useRoute, useRouter } from "vue-router";
import TabsDetail from "./module/Detail.vue";
import TabsAlarms from "./module/Alarms.vue";

defineOptions({ name: "DeviceManageGovDetails" });

const $width = inject<import("vue").Ref<number>>("width", ref(document.body.clientWidth - 200));
const $height = inject<import("vue").Ref<number>>("height", ref(document.body.clientHeight - 260 - document.body.clientHeight * 0.15));

const route = useRoute();
const router = useRouter();

enum ActiveType {
  DETAIL = "DETAIL",
  ALARMS = "ALARMS",
}

const active = computed({
  get: () => (`${route.query.active}` in ActiveType ? (route.query.active as ActiveType) : ActiveType.DETAIL),
  set: (v) => router.replace({ query: { ...route.query, active: v } }),
});

const tabs = ref([
  { label: "详情", value: ActiveType.DETAIL, Component: shallowRef(TabsDetail) },
  { label: "告警", value: ActiveType.ALARMS, Component: shallowRef(TabsAlarms) },
]);
</script>

<style lang="scss" scoped></style>
