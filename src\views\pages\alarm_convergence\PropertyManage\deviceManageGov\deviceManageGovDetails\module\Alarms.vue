<template>
  <el-table :class="style['view']" :data="[]" :height="props.height" row-key="id" stripe>
    <el-table-column show-overflow-tooltip prop="" label="紧急性" :width="80"></el-table-column>
    <el-table-column show-overflow-tooltip prop="" label="告警" :width="80"></el-table-column>
    <el-table-column show-overflow-tooltip prop="" label="工单" :width="180"></el-table-column>
    <el-table-column show-overflow-tooltip prop="" label="时间"></el-table-column>
    <el-table-column show-overflow-tooltip prop="" label="告警确认时间"></el-table-column>
    <el-table-column show-overflow-tooltip prop="" label="响应人" :width="80"></el-table-column>
  </el-table>
</template>

<script lang="ts" setup>
import { computed, toValue, useCssModule } from "vue";
import { type Props, defaultProps } from "./props";
import { type Emits, publishEmits } from "./emits";
const props = withDefaults(defineProps<Props>(), defaultProps);
const emits = defineEmits<Emits>();
const style = useCssModule();
</script>

<style lang="scss" module scoped>
.view {
  width: v-bind("`${props.width}px`");
  height: v-bind("`${props.height}px`");
}
</style>
