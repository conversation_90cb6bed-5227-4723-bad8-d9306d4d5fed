<template>
  <el-scrollbar :class="style['view']" :height="props.height" :view-style="{ padding: '0 12px' }">
    <el-row :gutter="24">
      <el-col :span="24" :xs="14" :sm="15" :md="16" :lg="17" :xl="18">
        <ul :class="style['info']">
          <li><el-text title="设备名称">设备名称</el-text><el-text truncated type="info">0</el-text></li>
          <li><el-text title="所属客户">所属客户</el-text><el-text truncated type="info">0</el-text></li>
          <li><el-text title="安装地址">安装地址</el-text><el-text truncated type="info">0</el-text></li>
          <li><el-text title="设备厂商">设备厂商</el-text><el-text truncated type="info">0</el-text></li>
          <li><el-text title="SN序列号">SN序列号</el-text><el-text truncated type="info">0</el-text></li>
          <li><el-text title="设备型号">设备型号</el-text><el-text truncated type="info">0</el-text></li>
          <li><el-text title="链路编号">链路编号</el-text><el-text truncated type="info">0</el-text></li>
          <li><el-text title="安装人姓名">安装人姓名</el-text><el-text truncated type="info">0</el-text></li>
          <li><el-text title="安装人电话">安装人电话</el-text><el-text truncated type="info">0</el-text></li>
          <li><el-text title="设备联系人">设备联系人</el-text><el-text truncated type="info">0</el-text></li>
          <li><el-text title="联系人电话">联系人电话</el-text><el-text truncated type="info">0</el-text></li>
          <li><el-text title="描述人电话">描述人电话</el-text><el-text truncated type="info">0</el-text></li>
        </ul>
      </el-col>
      <el-col :span="24" :xs="10" :sm="9" :md="8" :lg="7" :xl="6"></el-col>
    </el-row>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { computed, toValue, useCssModule } from "vue";
import { type Props, defaultProps } from "./props";
import { type Emits, publishEmits } from "./emits";
const props = withDefaults(defineProps<Props>(), defaultProps);
const emits = defineEmits<Emits>();
const style = useCssModule();
console.log(style);
</script>

<style lang="scss" module scoped>
.view {
  width: v-bind("`${props.width}px`");
  height: v-bind("`${props.height}px`");
  ul.info {
    border-radius: var(--el-border-radius-base);
    border: var(--el-border);
    overflow: hidden;
    li {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 12px;
      font-size: 12px;
      line-height: 16px;
      > * {
        width: calc(50% - 12px);
        &:first-child {
          text-align: left;
          font-weight: bold;
          margin-right: auto;
        }
        &:last-child {
          text-align: right;
          margin-left: auto;
        }
      }
      &:not(:last-child) {
        border-bottom: 1px solid #ddd;
      }
      &:nth-child(odd) {
        background-color: var(--el-fill-color-light);
      }
      &:nth-child(even) {
        background-color: var(--el-bg-color);
      }
    }
  }
}
</style>
