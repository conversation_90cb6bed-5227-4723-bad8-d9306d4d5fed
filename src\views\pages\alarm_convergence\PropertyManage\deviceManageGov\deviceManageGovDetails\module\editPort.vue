<template>
  <el-dialog v-model="dialogVisible" title="端口VLAN配置" width="600" :before-close="handleClose">
    <el-form ref="formRef" :model="form" label-width="120px" :rules="rules">
      <el-form-item label="已选端口">
        <div class="tw-w-full">
          {{ form.selectedPortTitle.join() }}
        </div>
      </el-form-item>
      <el-form-item label="端口类型">
        <el-radio-group v-model="form.vlanMode">
          <el-radio v-for="item in vlanModeOption" :key="`vlanMode-${item.value}`" :value="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="VLAN" v-if="Number(form.vlanMode) === 0">
        <el-input v-model="form.pvid"></el-input>
      </el-form-item>
      <template v-else>
        <el-form-item label="Native ID">
          <el-input v-model="form.pvid"></el-input>
        </el-form-item>
        <el-form-item label="Allowed Vlan">
          <el-input v-model="form.allowedVlan"></el-input>
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit"> 确 定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, ref, inject, nextTick } from "vue";

import { FormInstance, ElMessage } from "element-plus";

import { addSionVlan, vlanModeOption, setSionVlanPort } from "@/views/pages/apis/clientDeviceManage";

interface Props {
  sionDeviceId: string;
}

const props = withDefaults(defineProps<Props>(), {
  sionDeviceId: "",
});

const emits = defineEmits(["refresh"]);
const dialogVisible = ref<boolean>(false);

const detail: any = inject("detail");

const form = ref<Record<string, any>>({
  selectedPortTitle: "",
});

const formRef = ref<FormInstance>();

const rules = ref<Record<string, any>>({
  vlanId: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        if (!form.value.vlanId) return callback(new Error("请输入VLAN ID"));
        if (isNaN(Number(form.value.vlanId))) return callback(new Error("请输入数字"));
        if (Number(form.value.vlanId) > 4094) return callback(new Error("该输入项的最大值是4094"));
        if (Number(form.value.vlanId) < 2) return callback(new Error("该输入项的最小值是2"));
        return callback();
      },
      trigger: ["blur", "change"],
    },
  ],
});

async function handleClose() {
  formRef.value && formRef.value.resetFields();
  await nextTick();
  dialogVisible.value = false;
}

function handleOpen(rows, id) {
  dialogVisible.value = true;

  form.value.selectedPortTitle = rows.map((v) => v.portDesc);
  form.value.ifIndexArrStr = rows.map((v) => v.ifIndex);

  const current = rows.find((v) => v);

  form.value.vlanMode = current.vlanMode + "";
  form.value.pvid = current.pvid;
  form.value.allowedVlan = current.allowedVlan;
  form.value.vlanId = id;
}

function handleSubmit() {
  if (!formRef.value) return false;
  formRef.value.validate(async (valid: boolean) => {
    try {
      if (!valid) return;
      const params = {
        sionDeviceId: props.sionDeviceId,
        mac: detail.mac,
        mode: vlanModeOption.find((v) => v.value === form.value.vlanMode)?.label,
        pvId: form.value.pvid,
        allowedVlan: form.value.allowedVlan,
        ifIndexArrStr: form.value.ifIndexArrStr,
        vlanId: form.value.vlanId,
      };
      const { message, success } = await setSionVlanPort(params);
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    } catch (error) {
      error instanceof Error && ElMessage.error(error.message);
    } finally {
      emits("refresh");
      handleClose();
    }
  });
}

defineExpose({
  open: handleOpen,
});
</script>
