<template>
  <el-dialog v-model="dialogVisible" :title="`${isEdit ? '编辑' : '添加'}VLAN`" width="600" :before-close="handleClose">
    <el-form ref="formRef" :model="form" label-width="120px" :rules="rules">
      <el-row>
        <el-col :span="12">
          <el-form-item label="VLAN ID" prop="vlanId">
            <el-input v-model="form.vlanId" placeholder="请输入VLAN ID" :disabled="isEdit"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="VLAN IP" props="isVlanIp">
            <el-switch v-model="form.isVlanIp" />
          </el-form-item>
        </el-col>

        <template v-if="form.isVlanIp">
          <el-col :span="24">
            <el-form-item label="VLAN IP(主)" prop="vlanIpMain">
              <el-row>
                <el-col :span="11">
                  <el-input v-model="form.vlanIpMain" placeholder="IP地址" @blur="(v) => (form.maskMain = v ? '*************' : '')"></el-input>
                </el-col>
                <el-col :span="2" class="tw-text-center"> / </el-col>
                <el-col :span="11">
                  <el-input v-model="form.maskMain" placeholder="子网掩码"></el-input>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="VLAN IP(备)" prop="vlanIpStandby">
              <el-row>
                <el-col :span="11">
                  <el-input v-model="form.vlanIpStandby" placeholder="IP地址" @blur="(v) => (form.maskStandby = v ? '*************' : '')"></el-input>
                </el-col>
                <el-col :span="2" class="tw-text-center"> / </el-col>
                <el-col :span="11">
                  <el-input v-model="form.maskStandby" placeholder="子网掩码"></el-input>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit"> 确 定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, ref, inject, computed, nextTick } from "vue";

import { FormInstance, ElMessage } from "element-plus";

import { addSionVlan } from "@/views/pages/apis/clientDeviceManage";

interface Props {
  sionDeviceId: string;
}

const props = withDefaults(defineProps<Props>(), {
  sionDeviceId: "",
});

const emits = defineEmits(["refresh"]);
const dialogVisible = ref<boolean>(false);

const detail: any = inject("detail");

const form = ref<Record<string, any>>({
  isVlanIp: false,
});

const formRef = ref<FormInstance>();

const rules = ref<Record<string, any>>({
  vlanId: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        if (!form.value.vlanId) return callback(new Error("请输入VLAN ID"));
        if (isNaN(Number(form.value.vlanId))) return callback(new Error("请输入数字"));
        if (Number(form.value.vlanId) > 4094) return callback(new Error("该输入项的最大值是4094"));
        if (Number(form.value.vlanId) < 2) return callback(new Error("该输入项的最小值是2"));
        return callback();
      },
      trigger: ["blur", "change"],
    },
  ],
});

const _row = ref({});
const isEdit = computed(() => !!Object.keys(_row.value).length);

async function handleClose() {
  formRef.value && formRef.value.resetFields();
  await nextTick();
  dialogVisible.value = false;
}

async function handleOpen(row: any = {}) {
  dialogVisible.value = true;
  _row.value = row;
  await nextTick();
  if (Object.keys(row).length) {
    form.value.vlanId = row.vlanId;
    form.value.isVlanIp = !!row.vlanIp;
    form.value.ifIndex = row.ifIndex;
    form.value.vlanIpMain = (row.vlanIp || {}).ip || "";
    form.value.maskMain = (row.vlanIp || {}).nm || "";
    form.value.vlanIpStandby = row.manageIp.length > 1 ? row.manageIp[1].ip : "";
    form.value.maskStandby = row.manageIp.length > 1 ? row.manageIp[1].nm : "";
  }
}

function handleSubmit() {
  if (!formRef.value) return false;
  formRef.value.validate(async (valid: boolean) => {
    try {
      if (!valid) return;
      const params = {
        sionDeviceId: props.sionDeviceId,
        mac: detail.mac,
        vlanId: form.value.vlanId,
        ifIndex: form.value.ifIndex,
        operArrStr: form.value.isVlanIp
          ? [
              { mode: 2, ip: form.value.vlanIpMain || "", mask: form.value.maskMain || "", primary: true },
              { mode: 2, ip: form.value.vlanIpStandby || "", mask: form.value.maskStandby || "", primary: false },
            ]
          : [],
      };
      const { message, success } = await addSionVlan(params);
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    } catch (error) {
      error instanceof Error && ElMessage.error(error.message);
    } finally {
      emits("refresh");
      handleClose();
    }
  });
}

defineExpose({
  open: handleOpen,
});
</script>
