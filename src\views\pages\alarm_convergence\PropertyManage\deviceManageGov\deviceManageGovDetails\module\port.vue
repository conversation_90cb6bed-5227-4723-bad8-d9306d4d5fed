<template>
  <div>
    <div class="tw-mb-2 tw-border tw-p-2">
      <!-- 端口图 -->

      <!-- 上排 -->
      <div class="tw-flex">
        <div v-for="item in tableData.filter((v) => !(Number(v.ifIndex) % 2) && Number(v.phyIfType))" :key="`${item.portDesc}-${item.ifIndex}`" class="port-item tw-text-center">
          <el-tooltip class="box-item" effect="dark" placement="right">
            <template #content>
              <el-form :model="item" label-width="120px" label-position="left">
                <el-form-item :label-style="{ color: '#fff' }" :label="item.portDesc">{{ Number(item.status) !== 1 ? "已启用" : "已关闭" }}</el-form-item>
                <el-form-item :label-style="{ color: '#fff' }" :label="`状态`">{{ Number(item.status) === 0 ? "已连接" : "未连接" }}</el-form-item>
                <el-form-item :label-style="{ color: '#fff' }" :label="`类型`">{{ (speedTypeOption.find((v) => Number(v.value) === Number(item.speedType)) || {}).label || "" }}</el-form-item>
                <el-form-item :label-style="{ color: '#fff' }" :label="`速率`">
                  <span class="tw-mr-1">{{ (speedOption.find((v) => Number(v.value) === Number(item.speed)) || {}).label || "" }}</span>
                  <span>{{ (duplexOption.find((v) => Number(v.value) === Number(item.duplex)) || {}).label || "" }}</span>
                </el-form-item>
              </el-form>
            </template>
            <div>
              <p>{{ item.ifIndex }}</p>
              <div class="tw-mx-1 tw-h-[20px] tw-w-[20px]" :style="{ background: `${Number(item.status) === 1 ? '#000' : Number(item.status) === 0 ? '#00dbb0' : '#909399'}` }">
                <img v-if="Number(item.phyIfType) === 1" src="../../../../../assets/device/electricity_down.png" alt="" class="tw-h-full tw-w-full" />
                <img v-else-if="Number(item.phyIfType) === 2" src="../../../../../assets/device/OpticalPort.png" alt="" class="tw-h-full tw-w-full" />
              </div>
            </div>
          </el-tooltip>
        </div>
      </div>
      <!-- 下排 -->
      <div class="tw-mt-1 tw-flex">
        <div v-for="item in tableData.filter((v) => Number(v.ifIndex) % 2 && Number(v.phyIfType))" :key="`${item.portDesc}-${item.ifIndex}`" class="port-item tw-text-center">
          <el-tooltip class="box-item" effect="dark" placement="right">
            <template #content>
              <el-form :model="item" label-width="120px" label-position="left">
                <el-form-item :label-style="{ color: '#fff' }" :label="item.portDesc">{{ Number(item.status) !== 1 ? "已启用" : "已关闭" }}</el-form-item>
                <el-form-item :label-style="{ color: '#fff' }" :label="`状态`">{{ Number(item.status) === 0 ? "已连接" : "未连接" }}</el-form-item>
                <el-form-item :label-style="{ color: '#fff' }" :label="`类型`">{{ (speedTypeOption.find((v) => Number(v.value) === Number(item.speedType)) || {}).label || "" }}</el-form-item>
                <el-form-item :label-style="{ color: '#fff' }" :label="`速率`">
                  <span class="tw-mr-1">{{ (speedOption.find((v) => Number(v.value) === Number(item.speed)) || {}).label || "" }}</span>
                  <span>{{ (duplexOption.find((v) => Number(v.value) === Number(item.duplex)) || {}).label || "" }}</span>
                </el-form-item>
              </el-form>
            </template>
            <div>
              <div class="tw-mx-1 tw-h-[20px] tw-w-[20px]" :style="{ background: `${Number(item.status) === 1 ? '#000' : Number(item.status) === 0 ? '#00dbb0' : '#909399'}` }">
                <img v-if="Number(item.phyIfType) === 1" src="../../../../../assets/device/electricity_down.png" alt="" class="tw-h-full tw-w-full" />
                <img v-else-if="Number(item.phyIfType) === 2" src="../../../../../assets/device/OpticalPort.png" alt="" class="tw-h-full tw-w-full" />
              </div>
              <p>{{ item.ifIndex }}</p>
            </div>
          </el-tooltip>
        </div>
      </div>

      <div class="tw-flex tw-justify-end">
        <ul class="tw-flex">
          <li class="tw-mr-2 tw-flex tw-items-center">
            <div class="tw-mr-1 tw-h-[20px] tw-w-[20px] tw-bg-[#000]"><img src="../../../../../assets/device/electricity_down.png" alt="" class="tw-h-full tw-w-full" /></div>
            <span>电口</span>
          </li>
          <li class="tw-flex tw-items-center">
            <div class="tw-mr-1 tw-h-[20px] tw-w-[20px] tw-bg-[#000]"><img src="../../../../../assets/device/OpticalPort.png" alt="" class="tw-h-full tw-w-full" /></div>
            <span>光口</span>
          </li>
        </ul>
      </div>
    </div>
    <el-table :data="tableData" v-loading="tableLoading" stripe>
      <el-table-column :label="`端口描述`" prop="name">
        <template #default="{ row }">
          <div>
            <el-button type="primary" link @click="handleSetPortDesc(row)">{{ row.name }}</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="`状态`" prop="status">
        <template #default="{ row }">
          <div>
            <el-switch :model-value="[0, 2].includes(Number(row.status))" @update:model-value="handleBrforeChangeStatus(row)" />
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="`速率类型`" prop="speedType" :formatter="(_row, _col, _v) => (speedTypeOption.find((v) => Number(v.value) === Number(_v)) || {}).label || ''"></el-table-column>
      <el-table-column :label="`速率`" prop="speed" :formatter="(_row, _col, _v) => (speedOption.find((v) => Number(v.value) === Number(_v)) || {}).label || ''"></el-table-column>
      <el-table-column :label="`双工`" prop="duplex" :formatter="(_row, _col, _v) => (duplexOption.find((v) => Number(v.value) === Number(_v)) || {}).label || ''"></el-table-column>
      <el-table-column :label="`Keepalive`" prop="keepaliveStatus" :formatter="(_row, _col, _v) => (keepaliveStatusOption.find((v) => Number(v.value) === Number(_v)) || {}).label || ''"></el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, onMounted, nextTick, h } from "vue";

import { getDevicePort, setSionPortStatus, SionPortInfo as DataItem, speedTypeOption, speedOption, duplexOption, keepaliveStatusOption, setSionPortDesc } from "@/views/pages/apis/clientDeviceManage";
import { ElMessage, ElMessageBox } from "element-plus";

const detail: any = inject("detail");

const tableData = ref<DataItem[]>([]);

const tableLoading = ref<boolean>(false);

function handleBrforeChangeStatus(row) {
  ElMessageBox({
    title: "提示",
    message: () => h("div", {}, [h("p", { class: "el-message-box__message" }, "确定改变端口状态?"), h("p", {}, "注：若关闭所有通电上联端口，会影响配置下发功能")]),
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    showCancelButton: true,
    type: "warning",
    beforeClose: async (action, instance, done) => {
      if (action === "confirm") {
        try {
          instance.confirmButtonLoading = true;
          await nextTick();
          const { message, success } = await setSionPortStatus({ mac: detail.mac, index: row.ifIndex, status: [1].includes(Number(row.status)) ? 1 : 2 });
          if (!success) throw new Error(message);
          ElMessage.success("操作成功");
          handleRefresh();
          done();
        } catch (error) {
          error instanceof Error && ElMessage.error(error.message);
        } finally {
          instance.confirmButtonLoading = false;
        }
      } else done();
    },
  })
    .then(async () => {})
    .catch(() => {});
}

async function handleSetPortDesc(row) {
  ElMessageBox.prompt("请输入端口描述", "编辑端口描述", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputValue: row.name,
    inputValidator: (value) => !!value,
    inputErrorMessage: "请输入端口描述",
  })
    .then(async ({ value }) => {
      try {
        const { message, success } = await setSionPortDesc({ portId: row.id, portName: value });
        if (!success) throw new Error(message);
        ElMessage.success("操作成功");
        handleRefresh();
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    })
    .catch(() => {
      /* */
    });
}

async function handleRefresh() {
  try {
    if (!detail.mac) return;
    tableLoading.value = true;
    const { data, success, message } = await getDevicePort({ mac: detail.mac });
    if (!success) throw new Error(message);
    tableData.value = data.sionPortInfo.filter((v) => !(v.portDesc || "").includes("VLAN"));
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    tableLoading.value = false;
  }
}

onMounted(() => {
  handleRefresh();
});
</script>

<style scoped lang="scss">
.el-form {
  :deep(.el-form-item__label) {
    color: #fff;
  }
}
</style>
