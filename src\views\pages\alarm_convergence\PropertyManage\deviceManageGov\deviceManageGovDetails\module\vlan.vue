<template>
  <div>
    <div class="tw-mb-2">
      <div class="tw-mb-2">
        <span class="tw-mr-2">VLAN列表</span>
        <el-button-group size="small">
          <el-button type="primary" @click="handleAddVlan({})">添加VLAN</el-button>
          <el-button type="primary" @click="handleDelVlan" :disabled="!multipleSelection.length">删除VLAN</el-button>
        </el-button-group>
      </div>

      <el-table v-loading="tableLoading" :data="vlanTabelData" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" :selectable="(row) => Number(row.vlanId) !== 1" />
        <el-table-column :label="`VLAN ID`" prop="vlanId"></el-table-column>
        <el-table-column :label="`VLAN IP`" prop="vlanIp.ip"></el-table-column>
        <el-table-column :label="`操作`" width="200">
          <template #default="{ row }">
            <el-button type="primary" v-if="Number(row.vlanId) !== 1" link @click="handleAddVlan(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div>
      <div class="tw-mb-2">
        <span class="tw-mr-2">端口列表</span>
        <el-button-group size="small">
          <el-button type="primary" @click="() => editPortRef && editPortRef.open(multipleSelectionPort, multipleSelectionPort.map((v) => v.id).join())" :disabled="!multipleSelectionPort.length">编辑</el-button>
        </el-button-group>
      </div>

      <el-table v-loading="tableLoading" :data="portTabelData" @selection-change="handlePortTabelDataChange">
        <el-table-column type="selection" width="55" />
        <el-table-column :label="`端口描述`" prop="name"></el-table-column>
        <el-table-column :label="`端口类型`" prop="vlanMode" :formatter="(_row, _col, _v) => (vlanModeOption.find((v) => Number(v.value) === Number(_v)) || {}).label"></el-table-column>
        <el-table-column :label="`VLAN`" prop="pvid">
          <template #default="{ row }">
            <div v-if="Number(row.vlanMode) === 1">
              <p>Native ID: {{ row.pvid }}</p>
              <p>Allowed Vlan: {{ row.allowedVlan }}</p>
            </div>
            <div v-else>
              {{ row.pvid }}
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="`操作`" width="200">
          <template #default="{ row }">
            <el-button type="primary" link @click="() => editPortRef && editPortRef.open([row], row.id)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <editVlan ref="editVlanRef" :mac="detail.mac" :sionDeviceId="sionDevice.id" @refresh="handleRefresh"></editVlan>
    <editPort ref="editPortRef" :mac="detail.mac" :sionDeviceId="sionDevice.id" @refresh="handleRefresh"></editPort>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, onMounted } from "vue";

import { getDevicePort, delSionVlan, SionPortInfo as DataItem, vlanModeOption } from "@/views/pages/apis/clientDeviceManage";
import { ElMessage, ElMessageBox } from "element-plus";

import editVlan from "./editVlan.vue";
import editPort from "./editPort.vue";

const detail: any = inject("detail");

const tableLoading = ref<boolean>(false);

const vlanTabelData = ref<Record<string, any>[]>([]);

const portTabelData = ref<Record<string, any>[]>([]);

const sionDevice = ref<Record<string, any>>({});

const editVlanRef = ref<InstanceType<typeof editVlan>>();
const editPortRef = ref<InstanceType<typeof editPort>>();

const multipleSelection = ref<Record<string, any>[]>([]);
const multipleSelectionPort = ref<Record<string, any>[]>([]);
const handlePortTabelDataChange = (val) => {
  multipleSelectionPort.value = val;
};

const handleSelectionChange = (val) => {
  multipleSelection.value = val;
};

async function handleRefresh() {
  try {
    if (!detail.mac) return;
    tableLoading.value = true;
    const { data, success, message } = await getDevicePort({ mac: detail.mac });
    if (!success) throw new Error(message);
    sionDevice.value = data.deviceInfo;

    vlanTabelData.value = (data.deviceInfo.vlanIdList || [])
      .map((v) => {
        const current: any = data.sionPortInfo.find((f) => f.portDesc === "VLAN" + v) || {};
        return Object.assign({ vlanId: v }, { vlanIp: JSON.parse(current.manageIp || JSON.stringify([])).find((f) => f), manageIp: JSON.parse(current.manageIp || JSON.stringify([])), ifIndex: current.ifIndex });
      })
      .sort((a, b) => Number(a.vlanId) - Number(b.vlanId));
    portTabelData.value = data.sionPortInfo.filter((v) => !(v.portDesc || "").includes("VLAN"));
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    tableLoading.value = false;
  }
}

async function handleAddVlan(row) {
  if (!editVlanRef.value) return false;
  editVlanRef.value.open(row);
}

function handleDelVlan() {
  ElMessageBox.confirm("确定删除VLAN?注：删除管理VLAN会导致设备脱管", "删除VLAN", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    beforeClose: async (action, instance, done) => {
      if (action === "confirm") {
        try {
          const { message, success } = await delSionVlan({ mac: detail.mac, vlanIds: multipleSelection.value.map((v) => v.vlanId).join() });
          if (!success) throw new Error(message);
          ElMessage.success("操作成功");
          handleRefresh();
          done();
        } catch (error) {
          error instanceof Error && ElMessage.error(error.message);
        }
      } else done();
    },
  })
    .then(() => {})
    .catch(() => {});
}

onMounted(() => {
  handleRefresh();
});
</script>
