<template>
  <el-card shadow="never" style="margin: auto" :body-style="{ padding: '20px', height: `${$height}px`, width: `${$width}px`, display: 'flex', flexDirection: 'column', lineHeight: '18px' }">
    <el-scrollbar style="margin-bottom: -18px; height: fit-content" :height="56" :view-style="{ padding: '6px' }">
      <el-form :model="form" ref="formGovTenantRef" style="display: flex; align-items: flex-start; margin-bottom: -6px" :inline="true" @submit.prevent>
        <el-form-item
          label="所属客户"
          prop="tenantName"
          style="flex-shrink: 0"
          :rules="[
            /*  */
            { required: form.id === state.search.customerId, type: 'string', message: '客户名称不能为空', trigger: 'blur' },
          ]"
        >
          <el-select v-if="form.id !== state.search.customerId" :model-value="state.search.customerId === null ? Command.ALL : state.search.customerId" :disabled="state.loading" placeholder="请选择" filterable style="width: 180px" @update:model-value="executeCommand">
            <template #header>
              <div class="el-select-dropdown__wrap" style="margin: -10px">
                <ul class="el-select-dropdown__list">
                  <el-option label="所有客户" :value="Command.ALL">
                    <el-text style="color: inherit">
                      <el-icon><MoreFilled /></el-icon>
                      所有客户
                    </el-text>
                  </el-option>
                </ul>
              </div>
            </template>
            <template #default>
              <el-option v-for="item in govTenantList" :key="`name-${item.id}`" :label="item.tenantName" :value="item.id"></el-option>
              <el-option v-if="!govTenantList.length" disabled value="">无可选客户</el-option>
            </template>
            <template v-if="user.hasPermission(资产管理中心_客户设备By政支_新建客户)" #footer>
              <div class="el-select-dropdown__wrap" style="margin: -10px">
                <ul class="el-select-dropdown__list">
                  <el-option label="新建客户" :value="Command.NEW">
                    <el-text type="primary">
                      <el-icon><CirclePlus /></el-icon>
                      新建客户
                    </el-text>
                  </el-option>
                </ul>
              </div>
            </template>
          </el-select>
          <el-input v-else v-model="form.tenantName" :disabled="state.loading" placeholder="请输入客户名称"></el-input>
        </el-form-item>
        <el-form-item style="flex-shrink: 0; width: 140px">
          <template v-if="state.search.customerId && user.hasPermission(资产管理中心_客户设备By政支_编辑客户)">
            <el-button v-if="form.id !== state.search.customerId" type="primary" :disabled="state.loading" @click="executeCommand(Command.MOD)">修改</el-button>
            <el-button v-if="form.id === state.search.customerId" type="primary" :disabled="state.loading" @click="executeCommand(Command.EVE)">保存</el-button>
            <el-button v-if="form.id === state.search.customerId" type="primary" :disabled="state.loading" @click="executeCommand(Command.CLS)">取消</el-button>
          </template>
        </el-form-item>
        <el-form-item
          label="客户标识"
          prop="tenantIdent"
          style="flex-shrink: 0"
          :rules="[
            { required: form.id === state.search.customerId, type: 'string', message: '客户标识不能为空', trigger: 'blur' },
            { required: form.id === state.search.customerId, type: 'string', pattern: /^[0-9]+$/g, message: '客户标识为数字组成', trigger: 'blur' },
          ]"
        >
          <el-select v-if="form.id !== state.search.customerId" :model-value="state.search.customerId === null ? Command.ALL : state.search.customerId" :disabled="state.loading" placeholder="请选择" filterable style="width: 180px" @update:model-value="executeCommand">
            <template #header>
              <div class="el-select-dropdown__wrap" style="margin: -10px">
                <ul class="el-select-dropdown__list">
                  <el-option label="所有客户" :value="Command.ALL">
                    <el-text style="color: inherit">
                      <el-icon><MoreFilled /></el-icon>
                      所有客户
                    </el-text>
                  </el-option>
                </ul>
              </div>
            </template>
            <template #default>
              <el-option v-for="item in govTenantList" :key="`name-${item.id}`" :label="item.tenantIdent" :value="item.id"></el-option>
              <el-option v-if="!govTenantList.length" disabled value="">无可选客户</el-option>
            </template>
            <template v-if="user.hasPermission(资产管理中心_客户设备By政支_新建客户)" #footer>
              <div class="el-select-dropdown__wrap" style="margin: -10px">
                <ul class="el-select-dropdown__list">
                  <el-option label="新建客户" :value="Command.NEW">
                    <el-text style="color: inherit">
                      <el-icon><CirclePlus /></el-icon>
                      新建客户
                    </el-text>
                  </el-option>
                </ul>
              </div>
            </template>
          </el-select>
          <el-input v-else v-model="form.tenantIdent" :disabled="state.loading" placeholder="请输入客户标识"></el-input>
        </el-form-item>
        <el-form-item style="flex-shrink: 0; width: 140px">
          <template v-if="state.search.customerId && user.hasPermission(资产管理中心_客户设备By政支_编辑客户)">
            <el-button v-if="form.id !== state.search.customerId" type="primary" :disabled="state.loading" @click="executeCommand(Command.MOD)">修改</el-button>
            <el-button v-if="form.id === state.search.customerId" type="primary" :disabled="state.loading" @click="executeCommand(Command.EVE)">保存</el-button>
            <el-button v-if="form.id === state.search.customerId" type="primary" :disabled="state.loading" @click="executeCommand(Command.CLS)">取消</el-button>
          </template>
        </el-form-item>

        <el-form-item label="搜索设备">
          <el-input v-model="state.search.fullQuery" placeholder="请输入关键字" @change="handleReaderData(state.search)"></el-input>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <el-divider direction="horizontal" style="margin: 18px 0 10px 0"></el-divider>
    <el-scrollbar style="height: fit-content" :height="56" :view-style="{ padding: '6px' }">
      <el-form :model="state.search" style="display: flex; align-items: flex-start; margin-bottom: -6px" :inline="true" @submit.prevent>
        <el-form-item style="flex-shrink: 0; margin-right: auto">
          <el-text style="font-size: 16px; font-weight: bold">客户设备管理</el-text>
          <el-switch
            class="tw-ml-2"
            :model-value="state.search.active"
            @update:model-value="
              ($event) => {
                state.search.active = $event;
                nextTick(() => handleReaderData(state.search));
              }
            "
            inline-prompt
            active-text="激活设备"
            inactive-text="所有设备"
          />
        </el-form-item>
        <!-- <el-form-item label="" prop="name" style="width: 300px; flex-shrink: 0">
          <el-input v-model="state.search.name" :disabled="state.loading" placeholder="输入搜索关键字" clearable @change="handleReaderData(state.search)">
            <template #append>
              <el-button :icon="Search" @click="handleReaderData(state.search)"></el-button>
            </template>
          </el-input>
        </el-form-item> -->
        <el-form-item style="flex-shrink: 0; width: 260px">
          <div class="tw-w-full tw-text-right">
            <span class="tw-mr-2" v-if="form.id !== state.search.customerId && state.search.customerId && user.hasPermission(资产管理中心_客户设备By政支_新建设备)">
              <el-button type="primary" :disabled="state.loading" :icon="Plus" @click="handleCreateData(state.search)">新建设备</el-button>
            </span>

            <span class="tw-mr-2">
              <el-button type="primary" :icon="Download" @click="handleExportData(state.search)">导出</el-button>
            </span>

            <!-- <span class="tw-mr-2">
              <el-button type="primary" :icon="Refresh" :disabled="!(multipleSelection instanceof Array) || !multipleSelection.length" @click="handleRestart">批量重启</el-button>
            </span> -->

            <el-button :disabled="state.loading" :icon="Refresh" @click="handleReaderData(state.search)"></el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <el-table v-loading="state.loading" ref="tableRef" :data="dataList" row-key="id" :height="$height - 210" :expand-row-keys="state.expand" :current-row-key="state.current" style="width: 100%" @expand-change="handleExpand" @selection-change="(v) => (multipleSelection = v)">
      <!-- <el-table-column prop="name" label="设备" :resizable="false" :width="140">
        <template #default="{ row }: { row: DataItem }">
          <router-link v-if="row.id" :to="{ name: '626587648747634688', params: { id: row.id } }" custom>
            <template #default="{ href }">
              <div style="width: 100%; display: flex; flex-wrap: wrap">
                <a class="el-link el-link--primary" :href="href" style="display: block; font-weight: bold; white-space: nowrap; text-overflow: ellipsis; overflow: hidden; max-width: 100%" :title="row.name">
                  {{ row.name }}
                </a>
                <el-text :type="(remoteOnline.get(row.id) || { resourceId: row.id, online: false }).online ? 'success' : 'danger'" style="width: 100%">{{ (remoteOnline.get(row.id) || { resourceId: row.id, online: false }).online ? "在线" : "不在线" }}</el-text>
              </div>
            </template>
          </router-link>
        </template>
      </el-table-column> -->

      <!-- <el-table-column type="selection" width="55" /> -->

      <TableColumn type="condition" :prop="`name`" :label="`设备`" :min-width="200" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="handleReaderData(state.search)">
        <template #default="{ row }: { row: DataItem }">
          <router-link v-if="row.id" :to="{ name: '626587648747634688', params: { id: row.id } }" custom>
            <template #default="{ href }">
              <div style="width: 100%; display: flex; flex-wrap: wrap">
                <a class="el-link" :class="route.query.itemId && route.query.itemId === row.id ? 'el-link--danger' : 'el-link--primary'" :href="href" style="display: block; font-weight: bold; white-space: nowrap; text-overflow: ellipsis; overflow: hidden; max-width: 100%" :title="row.name" @click.prevent="handleAddRouterId(href, row.id)">
                  {{ row.name }}
                </a>
                <el-text :type="(remoteOnline.get(row.id) || { resourceId: row.id, online: false }).online ? 'success' : 'danger'" style="width: 100%">{{ (remoteOnline.get(row.id) || { resourceId: row.id, online: false }).online ? "在线" : "不在线" }}</el-text>
              </div>
            </template>
          </router-link>
        </template>
      </TableColumn>

      <el-table-column v-if="!state.search.customerId" prop="customerName" label="所属客户" show-overflow-tooltip :resizable="false" :width="160" :formatter="(_row: DataItem, _col, v: string) => `${v || '--'}${_row.customerIdent ? `[${_row.customerIdent}]` : ''}`"></el-table-column>

      <!-- <el-table-column prop="province" label="安装地址" :resizable="false" :width="180" :formatter="(_row: DataItem, _col, v: string) => `${_row.province || ''}${_row.city || ''}${_row.district || ''}${_row.installAddress || ''}`"></el-table-column> -->
      <TableColumn type="condition" :prop="`province`" :label="`安装地址`" :min-width="120" :showOverflowTooltip="false" filter-multiple show-filter v-model:custom-filtered-value="searchByAddress" :filters="$filter0" @filter-change="handleReaderData(state.search)" :formatter="(_row: DataItem, _col, v: string) => `${_row.province || ''}${_row.city || ''}${_row.district || ''}${_row.installAddress || ''}`"></TableColumn>

      <!-- <el-table-column prop="name" label="设备基本信息" :resizable="false" :min-width="240">
      </el-table-column>
      <el-table-column v-if="!state.search.customerId" prop="customerName" label="所属客户" show-overflow-tooltip :resizable="false" :width="160" :formatter="(_row: DataItem, _col, v: string) => `${v || '--'}${_row.customerIdent ? `[${_row.customerIdent}]` : ''}`"></el-table-column>
      <el-table-column prop="province" label="安装地址" :resizable="false" :width="180" :formatter="(_row: DataItem, _col, v: string) => `${_row.province || ''}${_row.city || ''}${_row.district || ''}${_row.installAddress || ''}`"></el-table-column>
      <el-table-column prop="name" label="设备基本信息" :resizable="false" :min-width="240">
        <template #default="{ row }: { row: DataItem }">
          <el-form :model="row" size="small">
            <el-form-item v-if="row.vendorName" label="设备厂商" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.vendorName" placement="top">
                <el-text line-clamp="1"> {{ row.vendorName }}</el-text>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="row.sn" label="SN序列号" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.sn" placement="top">
                <el-text line-clamp="1"> {{ row.sn }}</el-text>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="row.deviceModel" label="设备型号" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.deviceModel" placement="top">
                <el-text line-clamp="1"> {{ row.deviceModel }}</el-text>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="row.linkNumber" label="链路编号" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.linkNumber" placement="top">
                <el-text line-clamp="1"> {{ row.linkNumber }}</el-text>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="row.description" label="描述" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.description" placement="top">
                <el-text line-clamp="1"> {{ row.description }}</el-text>
              </el-tooltip>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column> -->

      <TableColumn type="condition" :prop="`name`" :label="`设备基本信息`" :min-width="240" :showOverflowTooltip="false" filter-multiple show-filter v-model:custom-filtered-value="searchByBasicInfo" :filters="$filter0" @filter-change="handleReaderData(state.search)">
        <template #default="{ row }: { row: DataItem }">
          <el-form :model="row" size="small">
            <el-form-item v-if="row.vendorName" label="设备厂商" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.vendorName" placement="top">
                <el-text line-clamp="1"> {{ row.vendorName }}</el-text>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="row.sn" label="SN序列号" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.sn" placement="top">
                <el-text line-clamp="1"> {{ row.sn }}</el-text>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="row.deviceModel" label="设备型号" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.deviceModel" placement="top">
                <el-text line-clamp="1"> {{ row.deviceModel }}</el-text>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="row.linkNumber" label="链路编号" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.linkNumber" placement="top">
                <el-text line-clamp="1"> {{ row.linkNumber }}</el-text>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="row.description" label="描述" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.description" placement="top">
                <el-text line-clamp="1"> {{ row.description }}</el-text>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="row.mac" label="mac地址" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.description" placement="top">
                <el-text line-clamp="1"> {{ row.mac }}</el-text>
              </el-tooltip>
            </el-form-item>
          </el-form>
        </template>
      </TableColumn>

      <!-- <el-table-column prop="name" label="联系方式" :resizable="false" :width="220">
        <template #default="{ row }: { row: DataItem }">
          <el-form :model="row" size="small">
            <el-form-item v-if="row.installerName" label="安装人姓名" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.installerName" placement="top">
                <el-text line-clamp="1"> {{ row.installerName }}</el-text>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="row.installerPhone" label="安装人电话" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.installerPhone" placement="top">
                <el-text line-clamp="1"> {{ row.installerPhone }}</el-text>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="row.contactPerson" label="设备联系人" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.contactPerson" placement="top">
                <el-text line-clamp="1"> {{ row.contactPerson }}</el-text>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="row.contactPhone" label="联系人电话" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.contactPhone" placement="top">
                <el-text line-clamp="1"> {{ row.contactPhone }}</el-text>
              </el-tooltip>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column> -->

      <!-- show-filter v-model:filtered-value="state.search.active" :filters="['true', 'false'].map((v) => ({ value: v, text: v == 'true' ? '激活' : '未激活' }))" @filter-change="handleReaderData(state.search)" -->
      <!-- 7.29 产品说去掉是否激活 -->
      <TableColumn type="enum" :label="`是否激活`" :min-width="100" :showOverflowTooltip="true">
        <template #default="{ row }">
          <el-text :type="row.active == true ? 'success' : 'info'">{{ row.active == true ? "激活" : "未激活" }}</el-text>
        </template>
      </TableColumn>

      <TableColumn type="condition" :prop="`name`" :label="`联系方式`" :min-width="220" :showOverflowTooltip="false" filter-multiple show-filter v-model:custom-filtered-value="searchByContact" :filters="$filter0" @filter-change="handleReaderData(state.search)">
        <template #default="{ row }: { row: DataItem }">
          <el-form :model="row" size="small">
            <el-form-item v-if="row.installerName" label="安装人姓名" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.installerName" placement="top">
                <el-text line-clamp="1"> {{ row.installerName }}</el-text>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="row.installerPhone" label="安装人电话" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.installerPhone" placement="top">
                <el-text line-clamp="1"> {{ row.installerPhone }}</el-text>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="row.contactPerson" label="设备联系人" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.contactPerson" placement="top">
                <el-text line-clamp="1"> {{ row.contactPerson }}</el-text>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="row.contactPhone" label="联系人电话" style="margin: 0">
              <el-tooltip class="box-item" effect="dark" :content="row.contactPhone" placement="top">
                <el-text line-clamp="1"> {{ row.contactPhone }}</el-text>
              </el-tooltip>
            </el-form-item>
          </el-form>
        </template>
      </TableColumn>

      <el-table-column label="操作" :resizable="false" :width="86">
        <template #default="{ row }: { row: DataItem }">
          <span v-if="row.verifyPermissionIds.includes(资产管理中心_客户设备By政支_编辑设备)">
            <el-link type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :disabled="state.loading" @click="handleUpdateData(row)">{{ $t("glob.edit") }}</el-link>
          </span>
          <span>
            <!-- 客户设备管理('627069869040336896') -->
            <el-link :type="row.verifyPermissionIds.includes(资产管理中心_客户设备By政支_安全) ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="row.verifyPermissionIds.includes(资产管理中心_客户设备By政支_安全) ? state.loading : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="state.page" v-model:page-size="state.size" :total="state.total" :page-sizes="sizes" layout="->, total, sizes, prev, pager, next, jumper" @size-change="handleReaderData(state.search)" @current-change="handleReaderData(state.search)" style="margin-top: auto"> </el-pagination>
    <FormEditor ref="editorRef" title="设备"></FormEditor>
    <TenantEditor ref="tenantEditorRef" title="客户"></TenantEditor>
  </el-card>
</template>

<script lang="ts" setup>
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, computed, inject, nextTick, toValue, watch, shallowRef, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { Refresh, Plus, Search, CirclePlus, MoreFilled, Download, SwitchButton } from "@element-plus/icons-vue";
import getUserInfo from "@/utils/getUserInfo";
import { sizes } from "@/utils/common";
import generateQueueHook from "@/utils/queue_hook";
import { filter, find } from "lodash-es";
import FormEditor from "./FormEditor.vue";
import TenantEditor from "./TenantEditor.vue";
import { getRawUserInfo } from "@/api/system";
import { type GovTenantItem, getGovTenantList, addGovTenantData, modGovTenantData, restartDevice } from "../../../apis/clientDeviceManage";
import { type GovResourceItem as DataItem, getGovResourceList as getList, addGovResourceData as addData, modGovResourceData as modData, getGovResourceExport as exportData } from "../../../apis/clientDeviceManage";
import { getRemoteOnlineByResources } from "@/views/pages/apis/device";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { 资产管理中心_客户设备By政支_可读, 资产管理中心_客户设备By政支_新建设备, 资产管理中心_客户设备By政支_编辑设备, 资产管理中心_客户设备By政支_新建客户, 资产管理中心_客户设备By政支_编辑客户, 资产管理中心_客户设备By政支_安全 } from "@/views/pages/permission";

import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";
defineOptions({ name: "DeviceManageGov" });

const $width = inject<import("vue").Ref<number>>("width", ref(document.body.clientWidth - 200));
const $height = inject<import("vue").Ref<number>>("height", ref(document.body.clientHeight - 260 - document.body.clientHeight * 0.15));

const route = useRoute();
const router = useRouter();
const user = getUserInfo();

interface State<T, P> {
  loading: boolean;
  expand: string[];
  select: string[];
  current: string | undefined;
  search: P;
  list: T[];
  page: number;
  size: number;
  total: number;
}

type DataSrch = Omit<typeof getList extends (req: infer P) => any ? P : never, "paging" | "containerId" | "queryPermissionId" | "verifyPermissionIds" | "controller" | "slot">;
const state = reactive<State<DataItem, DataSrch & Record<string, any>>>({
  loading: false,
  expand: [],
  select: [],
  current: undefined,
  search: {
    active: true,

    fullQuery: "" /* 全局筛选 */,

    customerId: route.query.customerId || null,

    eqName: [] /* 等于的设备名称 */,
    includeName: [] /* 包含的设备名称 */,
    nameFilterRelation: "AND" /* 设备名称过滤关系(AND,OR) */,
    neName: [] /* 不等于的设备名称 */,
    excludeName: [] /* 不包含的设备名称 */,

    eqAddress: [] /* 等于的设备安装地址 */,
    includeAddress: [] /* 包含的设备安装地址 */,
    addressFilterRelation: "AND" /* 设备安装地址过滤关系(AND,OR) */,
    neAddress: [] /* 不等于的设备安装地址 */,
    excludeAddress: [] /* 不包含的设备安装地址 */,

    eqBasicInfo: [] /* 等于的设备基本信息 */,
    includeBasicInfo: [] /* 包含的设备基本信息 */,
    basicInfoFilterRelation: "AND" /* 设备基本信息过滤关系(AND,OR) */,
    neBasicInfo: [] /* 不等于的设备基本信息 */,
    excludeBasicInfo: [] /* 不包含的设备基本信息 */,

    eqContact: [] /* 等于的设备联系方式 */,
    includeContact: [] /* 包含的设备联系方式 */,
    contactFilterRelation: "AND" /* 设备联系方式过滤关系(AND,OR) */,
    neContact: [] /* 不等于的设备联系方式 */,
    excludeContact: [] /* 不包含的设备联系方式 */,
  },
  list: [],
  page: 1,
  size: 50,
  total: 0,
});
const QueuePromise = generateQueueHook(
  () => state.loading,
  ($loading) => !$loading
);

const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);

const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByName) === "include") value0 = state.search.includeName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = state.search.excludeName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = state.search.eqName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = state.search.neName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = state.search.includeName[state.search.includeName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = state.search.excludeName[state.search.excludeName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = state.search.eqName[state.search.eqName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = state.search.neName[state.search.neName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: state.search.nameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    state.search.nameFilterRelation = v.relation;
    state.search.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByAddress = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByAddress = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByAddress = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    // eqAddress: [] /* 等于的设备安装地址 */,
    // includeAddress: [] /* 包含的设备安装地址 */,
    // addressFilterRelation: "AND" /* 设备安装地址过滤关系(AND,OR) */,
    // neAddress: [] /* 不等于的设备安装地址 */,
    // excludeAddress: [] /* 不包含的设备安装地址 */,
    let value0 = "";
    if (toValue(searchType0ByAddress) === "include") value0 = state.search.includeAddress[0] || "";
    if (toValue(searchType0ByAddress) === "exclude") value0 = state.search.excludeAddress[0] || "";
    if (toValue(searchType0ByAddress) === "eq") value0 = state.search.eqAddress[0] || "";
    if (toValue(searchType0ByAddress) === "ne") value0 = state.search.neAddress[0] || "";
    let value1 = "";
    if (toValue(searchType1ByAddress) === "include") value1 = state.search.includeAddress[state.search.includeAddress.length - 1] || "";
    if (toValue(searchType1ByAddress) === "exclude") value1 = state.search.excludeAddress[state.search.excludeAddress.length - 1] || "";
    if (toValue(searchType1ByAddress) === "eq") value1 = state.search.eqAddress[state.search.eqAddress.length - 1] || "";
    if (toValue(searchType1ByAddress) === "ne") value1 = state.search.neAddress[state.search.neAddress.length - 1] || "";
    return {
      type0: toValue(searchType0ByAddress),
      type1: toValue(searchType1ByAddress),
      relation: state.search.addressFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByAddress.value = v.type0 as typeof searchType0ByAddress extends import("vue").Ref<infer T> ? T : string;
    searchType1ByAddress.value = v.type1 as typeof searchType1ByAddress extends import("vue").Ref<infer T> ? T : string;
    state.search.addressFilterRelation = v.relation;
    state.search.includeAddress = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeAddress = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqAddress = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neAddress = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByBasicInfo = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByBasicInfo = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByBasicInfo = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    // eqBasicInfo: [] /* 等于的设备基本信息 */,
    // includeBasicInfo: [] /* 包含的设备基本信息 */,
    // basicInfoFilterRelation: "AND" /* 设备基本信息过滤关系(AND,OR) */,
    // neBasicInfo: [] /* 不等于的设备基本信息 */,
    // excludeBasicInfo: [] /* 不包含的设备基本信息 */,
    let value0 = "";
    if (toValue(searchType0ByBasicInfo) === "include") value0 = state.search.includeBasicInfo[0] || "";
    if (toValue(searchType0ByBasicInfo) === "exclude") value0 = state.search.excludeBasicInfo[0] || "";
    if (toValue(searchType0ByBasicInfo) === "eq") value0 = state.search.eqBasicInfo[0] || "";
    if (toValue(searchType0ByBasicInfo) === "ne") value0 = state.search.neBasicInfo[0] || "";
    let value1 = "";
    if (toValue(searchType1ByBasicInfo) === "include") value1 = state.search.includeBasicInfo[state.search.includeBasicInfo.length - 1] || "";
    if (toValue(searchType1ByBasicInfo) === "exclude") value1 = state.search.excludeBasicInfo[state.search.excludeBasicInfo.length - 1] || "";
    if (toValue(searchType1ByBasicInfo) === "eq") value1 = state.search.eqBasicInfo[state.search.eqBasicInfo.length - 1] || "";
    if (toValue(searchType1ByBasicInfo) === "ne") value1 = state.search.neBasicInfo[state.search.neBasicInfo.length - 1] || "";
    return {
      type0: toValue(searchType0ByBasicInfo),
      type1: toValue(searchType1ByBasicInfo),
      relation: state.search.basicInfoFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByBasicInfo.value = v.type0 as typeof searchType0ByBasicInfo extends import("vue").Ref<infer T> ? T : string;
    searchType1ByBasicInfo.value = v.type1 as typeof searchType1ByBasicInfo extends import("vue").Ref<infer T> ? T : string;
    state.search.basicInfoFilterRelation = v.relation;
    state.search.includeBasicInfo = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeBasicInfo = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqBasicInfo = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neBasicInfo = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByContact = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByContact = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByContact = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    // eqContact: [] /* 等于的设备联系方式 */,
    // includeContact: [] /* 包含的设备联系方式 */,
    // contactFilterRelation: "AND" /* 设备联系方式过滤关系(AND,OR) */,
    // neContact: [] /* 不等于的设备联系方式 */,
    // excludeContact: [] /* 不包含的设备联系方式 */,
    let value0 = "";
    if (toValue(searchType0ByContact) === "include") value0 = state.search.includeContact[0] || "";
    if (toValue(searchType0ByContact) === "exclude") value0 = state.search.excludeContact[0] || "";
    if (toValue(searchType0ByContact) === "eq") value0 = state.search.eqContact[0] || "";
    if (toValue(searchType0ByContact) === "ne") value0 = state.search.neContact[0] || "";
    let value1 = "";
    if (toValue(searchType1ByContact) === "include") value1 = state.search.includeContact[state.search.includeContact.length - 1] || "";
    if (toValue(searchType1ByContact) === "exclude") value1 = state.search.excludeContact[state.search.excludeContact.length - 1] || "";
    if (toValue(searchType1ByContact) === "eq") value1 = state.search.eqContact[state.search.eqContact.length - 1] || "";
    if (toValue(searchType1ByContact) === "ne") value1 = state.search.neContact[state.search.neContact.length - 1] || "";
    return {
      type0: toValue(searchType0ByContact),
      type1: toValue(searchType1ByContact),
      relation: state.search.contactFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByContact.value = v.type0 as typeof searchType0ByContact extends import("vue").Ref<infer T> ? T : string;
    searchType1ByContact.value = v.type1 as typeof searchType1ByContact extends import("vue").Ref<infer T> ? T : string;
    state.search.contactFilterRelation = v.relation;
    state.search.includeContact = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeContact = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqContact = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neContact = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const dataList = computed(() => state.list);
const expand = computed(() => filter<DataItem>(state.list, (row) => state.expand.includes(row.id)));
const select = computed(() => filter<DataItem>(state.list, (row) => state.select.includes(row.id)));
const current = computed(() => find<DataItem>(state.list, (row) => row.id === state.current));

const remoteOnline = ref(new Map<string, (ReturnType<typeof getRemoteOnlineByResources> extends Promise<{ data: infer P }> ? P : never)[number]>());

const editorRef = ref<InstanceType<typeof FormEditor>>();
const tenantEditorRef = ref<InstanceType<typeof TenantEditor>>();

const multipleSelection = ref<DataItem[]>([]);

onMounted(async () => {
  await handleReaderData(state.search);
});

const formGovTenantRef = ref<InstanceType<typeof import("element-plus").ElForm>>();
const govTenantList = ref<GovTenantItem[]>([]);
const form = reactive({
  id: "",
  tenantName: "",
  tenantIdent: "",
});
enum Command {
  NEW = "NEW",
  ALL = "ALL",
  MOD = "MOD",
  CLS = "CLS",
  EVE = "EVE",
}

function handleRestart() {
  ElMessageBox.confirm("是否确认重启?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      /* code */
      try {
        const { message, success } = await restartDevice({ ccDeviceIds: multipleSelection.value.map((v) => v.id).join(",") });
        if (!success) throw new Error(message);
        ElMessage.success("操作成功");
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    })
    .catch(() => {
      /* code */
    });
}

async function executeCommand(cmd: Command | string) {
  switch (cmd) {
    case Command.ALL: {
      state.search.customerId = null;
      await nextTick();
      await handleReaderData(state.search);
      break;
    }
    case Command.NEW: {
      const $dialog = toValue(tenantEditorRef);
      if (!$dialog) return;
      await $dialog.opener({ containerId: (user.currentTenant || {}).containerId }, async (form) => {
        try {
          state.loading = true;
          await nextTick();
          const { success, message, data } = await addGovTenantData({ ...form, containerId: (user.currentTenant || {}).containerId || "" });
          if (!success) throw Object.assign(new Error(message), { success, data });
          state.search.customerId = data.id;
          ElMessage.success("操作成功");
          state.loading = false;
          await nextTick();
          await handleReaderData(state.search);
        } catch (error) {
          state.loading = false;
          await nextTick();
          await handleReaderData(state.search);
          throw error;
        }
      });
      break;
    }
    case Command.MOD: {
      const $formRef = toValue(formGovTenantRef);
      if (!$formRef) return;
      const $list = toValue(govTenantList);
      for (let i = 0; i < $list.length; i++) {
        if ($list[i].id === state.search.customerId) {
          form.id = $list[i].id;
          form.tenantName = $list[i].tenantName;
          form.tenantIdent = $list[i].tenantIdent;
          await nextTick();
          $formRef.clearValidate();
          break;
        }
      }
      break;
    }
    case Command.EVE: {
      const $formRef = toValue(formGovTenantRef);
      if (!$formRef) return;
      state.loading = true;
      try {
        if (await new Promise<boolean>((resolve) => $formRef.validate(resolve))) {
          const { success, message, data } = await modGovTenantData({ ...form, containerId: (user.currentTenant || {}).containerId || "" });
          if (!success) throw Object.assign(new Error(message), { success, data });
          form.id = "";
          form.tenantName = "";
          ElMessage.success("操作成功");
          await nextTick();
          $formRef.clearValidate();
        }
      } catch (error) {
        ElMessage.error(error instanceof Error ? error.message : `${error}`);
      }
      state.loading = false;
      await nextTick();
      await handleReaderData(state.search);
      break;
    }
    case Command.CLS: {
      const $formRef = toValue(formGovTenantRef);
      if (!$formRef) return;
      form.id = "";
      form.tenantName = "";
      await nextTick();
      $formRef.clearValidate();
      break;
    }
    default: {
      state.search.customerId = cmd;

      router.replace({ query: Object.assign(JSON.parse(JSON.stringify(route.query)) /* { customerId: cmd } */) }).finally(async () => {
        await nextTick();
        await handleReaderData(state.search);
      });
      break;
    }
  }
}

// const vendorList = ref<ReturnType<typeof getVendorsList> extends Promise<{ data: infer T }> ? T : never>([]);

function handleExpand(row: DataItem, expandedRows: DataItem[]) {
  state.expand = expandedRows.map(({ id }) => id);
  if (find(expandedRows, ({ id }) => row.id === id)) {
    /*  */
  } else {
    /*  */
  }
}

async function handleAddRouterId(href, id) {
  // console.log(href, id);
  // return false;
  router.replace({ query: Object.assign(JSON.parse(JSON.stringify(route.query)), { itemId: id }) });

  await nextTick();
  let timeout = setTimeout(() => {
    router.push({
      name: "626587648747634688",
      params: { id },
    });
    clearTimeout(timeout);
  }, 500);
}

async function handleCreateData(raw: DataSrch) {
  const $dialog = toValue(editorRef);
  if (!$dialog) return;
  const ext: Record<string, string> = { installerName: user.name, installerPhone: user.phone || "" };
  try {
    const { success, message, data } = await getRawUserInfo({ token: user.getToken("auth"), tenant: user.currentTenantId });
    if (!success) throw Object.assign(new Error(message), { success, data });
    Object.assign(ext, { installerName: data.name, installerPhone: data.phone || "" });
  } catch (error) {
    /*  */
  }
  await $dialog.opener({ ...raw, userId: user.userId, ...ext, customerId: state.search.customerId as string, containerId: (user.currentTenant || {}).containerId as string }, async (form) => {
    try {
      state.loading = true;
      await nextTick();
      const { success, message, data } = await addData({ ...form, modelIdent: "netcare_device", customerId: state.search.customerId as string, containerId: (user.currentTenant || {}).containerId as string });
      if (!success) throw Object.assign(new Error(message), { success, data });
      state.loading = false;
      ElMessage.success("操作成功");
      await handleReaderData(state.search);
    } catch (error) {
      state.loading = false;
      await handleReaderData(state.search);
      throw error;
    }
  });
}

async function handleExportData(raw: DataSrch) {
  const { data, message, success } = await exportData({ ...raw });
  if (!success) throw new Error(message);
  const href = URL.createObjectURL(data);
  const link = document.createElement("a");
  link.download = "客户设备管理列表.xlsx";
  link.href = href;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(href); // 释放内存
}

async function handleReaderData(raw: DataSrch) {
  const queue = new QueuePromise(true);
  await queue;
  const controller = new AbortController();
  try {
    state.loading = true;

    router.replace({ query: Object.assign(JSON.parse(JSON.stringify(route.query)), { page: state.page, size: state.size }) });

    await nextTick();
    const result0 = getList({ paging: { pageNumber: state.page, pageSize: state.size }, ...raw });
    const result1: ReturnType<typeof getGovTenantList> | null = user.hasPermission(资产管理中心_客户设备By政支_可读) ? getGovTenantList({ active: true, containerId: (user.currentTenant || {}).containerId }) : null;
    // const result2 = getVendorsList({ vendorType: "DEVICE" as const });
    // (async (req) => {
    //   const { success, message, data } = await ;
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    // })({ vendorType: "DEVICE" as const }),
    controller.signal.addEventListener("abort", () => {
      if (!result0.controller.signal.aborted) result0.controller.abort();
      if (result1) if (!result1.controller.signal.aborted) result1.controller.abort();
      // if (!result2.controller.signal.aborted) result2.controller.abort();
    });
    if (controller instanceof AbortController && !controller.signal.aborted) queue.appendController(controller);
    const [{ success, message, data, page, size, total }, { success: govTenantSuccess, message: govTenantMessage, data: govTenantData } /* , { success: vendorSuccess, message: vendorMessage, data: vendorData } */] = await Promise.all([result0, result1 || Promise.resolve({ success: true, message: "", data: [] }) /* , result2 */]);
    if (!govTenantSuccess) throw Object.assign(new Error(govTenantMessage), { success: govTenantSuccess, data: govTenantData });
    govTenantList.value = govTenantData instanceof Array ? govTenantData : [];
    // if (!vendorSuccess) throw Object.assign(new Error(vendorMessage), { success: vendorSuccess, data: vendorData });
    // vendorList.value = vendorData instanceof Array ? vendorData : [];
    if (!success) throw Object.assign(new Error(message), { success, data });
    state.list = data instanceof Array ? data : [];
    state.page = Number(page) || 1;
    state.size = Number(size) || 50;
    state.total = Number(total) || 0;
  } catch (error) {
    state.page = 1;
    state.size = 50;
    state.total = 0;
    ElMessage.error(error instanceof Error ? error.message : `${error}`);
  } finally {
    state.loading = false;
    if (controller instanceof AbortController && !controller.signal.aborted) controller.abort();
  }
  await nextTick();
  try {
    state.loading = true;
    await nextTick();
    remoteOnline.value.clear();
    if (state.list.length) {
      const { success, message, data } = await getRemoteOnlineByResources({ ids: state.list.map((v) => v.id) });
      if (!success) throw Object.assign(new Error(message), { success, data });
      const $data = data instanceof Array ? data : [];
      for (let i = 0; i < $data.length; i++) remoteOnline.value.set($data[i].resourceId, $data[i]);
    }
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : `${error}`);
  } finally {
    state.loading = false;
  }
}
async function handleUpdateData(raw: DataItem) {
  const $dialog = toValue(editorRef);
  if (!$dialog) return;
  await $dialog.opener({ ...raw }, async (form) => {
    try {
      state.loading = true;
      await nextTick();
      const { success, message, data } = await modData({ ...form, id: raw.id, modelIdent: "netcare_device", containerId: (user.currentTenant || {}).containerId as string });
      if (!success) throw Object.assign(new Error(message), { success, data });
      state.loading = false;
      ElMessage.success("操作成功");
      await handleReaderData(state.search);
    } catch (error) {
      state.loading = false;
      await handleReaderData(state.search);
      throw error;
    }
  });
}
// async function handleDeleteData(raw: DataItem) {
//   ElMessageBox.confirm(`确认删除`, "删除");
//   const $dialog = toValue(editorRef);
//   if (!$dialog) return;
//   await $dialog.opener({ ...raw }, async (form) => {
//     try {
//       state.loading = true;
//       await nextTick();
//       const { success, message, data } = await delData(form);
//       if (!success) throw Object.assign(new Error(message), { success, data });
//       state.loading = false;
//       ElMessage.success("操作成功");
//       await handleReaderData(state.search);
//     } catch (error) {
//       state.loading = false;
//       await handleReaderData(state.search);
//       throw error;
//     }
//   });
// }
</script>

<style lang="scss" scoped></style>
