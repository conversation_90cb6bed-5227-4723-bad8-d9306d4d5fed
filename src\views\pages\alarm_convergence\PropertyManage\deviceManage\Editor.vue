<template>
  <!-- 对话框表单 -->
  <component :is="ComponentRender" v-model:visible="data.visible" :handle-cancel="handleCancel">
    <template #header>
      {{ `${$params.id ? t("glob.edit") : t("glob.add")} ${t("devicesList.Device")}` }}
    </template>
    <template #default="{ width }">
      <FormModel ref="formRef" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @submit="handleFinish">
        <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Name')" tooltip="" prop="name" :rules="[buildValidatorData({ name: 'required', title: $t('devicesAdd.Name') })]">
          <el-input v-model="form.name" :placeholder="$t('glob.Please input field', { field: $t('devicesAdd.Name') })" clearable></el-input>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Tag')" tooltip="" prop="tags" :rules="[]">
          <el-select v-model="form.tags" :placeholder="$t('glob.Please input field', { field: $t('devicesAdd.Tag') })" remote allow-create default-first-option multiple clearable filterable class="tw-w-full"></el-select>
        </FormItem>
        <!-- <FormItem :span="width > 600 ? 12 : 24" :label="`设备别名`" tooltip="" prop="alias">
          <el-input v-model="form.alias" :placeholder="$t('glob.Please input field', { field: '设备别名' })" clearable></el-input>
        </FormItem> -->

        <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(资产管理中心_区域_可读)">
          <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Region')" tooltip="" prop="regionId" :rules="[buildValidatorData({ name: 'required', title: `区域` })]">
            <el-cascader :disabled="!userInfo.hasPermission(资产管理中心_区域_可读)" v-model="form.regionId" :options="regionTree" :placeholder="$params.id ? $params.regionDesc : $t('glob.Please select field', { field: '区域' })" :props="{ checkStrictly: true, emitPath: false, multiple: false, value: 'id', label: 'name' }" separator=" > " clearable filterable class="tw-w-full" @change="handleGetLocation"></el-cascader>
          </FormItem>
        </el-tooltip>
        <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(资产管理中心_场所_可读)">
          <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Location')" tooltip="" prop="locationId" :rules="[buildValidatorData({ name: 'required', title: `场所` })]">
            <el-select :disabled="!userInfo.hasPermission(资产管理中心_场所_可读)" v-model="form.locationId" :placeholder="$params.id ? $params.locationDesc : $t('glob.Please select field', { field: '场所' })" clearable filterable class="tw-w-full">
              <el-option v-for="item in locationList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </FormItem>
        </el-tooltip>
        <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.External Id')" tooltip="" prop="externalId" :rules="[]">
          <el-input v-model="form.externalId" :placeholder="$t('glob.Please input field', { field: $t('devicesAdd.External Id') })" clearable></el-input>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Description')" tooltip="" prop="description" :rules="[]">
          <el-input v-model="form.description" :placeholder="$t('glob.Please input field', { field: $t('devicesAdd.Description') })" clearable></el-input>
        </FormItem>

        <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Online Time')" tooltip="" prop="description" :rules="[]">
          <el-date-picker v-model="form.resourceOnlineTime" format="YYYY-MM-DD" value-format="x" type="date" :disabled-date="disableStartTime" :placeholder="$t('glob.Please select field', { field: $t('devicesAdd.Online Time') })" style="width: 100%" />
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Offline Time')" tooltip="" prop="description" :rules="[]">
          <el-date-picker v-model="form.resourceOfflineTime" format="YYYY-MM-DD" value-format="x" type="date" :disabled-date="disableEndTime" :placeholder="$t('glob.Please select field', { field: $t('devicesAdd.Offline Time') })" style="width: 100%" />
        </FormItem>
        <div style="width: 100%">
          <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Monitoring source')" tooltip="" prop="monitorSources" :rules="[]">
            <el-select v-model="form.monitorSources" :placeholder="$t('glob.Please select field', { field: $t('devicesAdd.Monitoring source') })" default-first-option multiple clearable filterable class="tw-w-full">
              <el-option v-for="item in monitorSourcesOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
              <!-- <el-option label="ARTHAS" value="ARTHAS"></el-option> -->

              <!-- <el-option label="NetCare V6" value="NETCARE_V6"></el-option>
              <el-option label="Other" value="OTHER"></el-option> -->
              <!-- <el-option label="CATEGRAF" value="CATEGRAF"></el-option> -->
            </el-select>
          </FormItem>
        </div>

        <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Active')" tooltip="" prop="active" :rules="[]">
          <el-checkbox v-model="form.active"></el-checkbox>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Deliver')" tooltip="" prop="active" :rules="[]">
          <el-checkbox v-model="form.delivery"></el-checkbox>
        </FormItem>
        <FormGroup :span="24" :label="$t('devicesAdd.Configuration information')" tooltip="">
          <template #default="{}">
            <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.IP address')" tooltip="" prop="config.ipAddress" :rules="[buildValidatorData({ name: 'address', title: $t('devicesAdd.IP address') })]">
              <el-input v-model="form.config.ipAddress" :placeholder="$t('glob.Please input field', { field: $t('devicesAdd.IP address') })" clearable></el-input>
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Dynamic IP')" :tooltip="$t('devicesAdd.Resolve IPs from device names and set up DNS monitoring and groups for clients')" prop="config.dynamicIp" :rules="[]">
              <el-checkbox v-model="form.config.dynamicIp"></el-checkbox>
            </FormItem>
            <!-- 告警分类 -->
            <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(资产管理中心_设备_分配告警分类)">
              <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Alert classifications')" tooltip="" prop="alertClassificationIds" :rules="[]">
                <el-select :disabled="!userInfo.hasPermission(资产管理中心_设备_分配告警分类)" v-model="form.alertClassificationIds" :placeholder="$t('glob.Please select field', { field: $t('devicesAdd.Alert classifications') })" default-first-option multiple clearable filterable class="tw-w-full">
                  <el-option v-for="item in alarmList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
              </FormItem>
            </el-tooltip>
            <FormItem :span="width > 600 ? 6 : 12" :label="$t('devicesAdd.Acknowledgement required')" tooltip="" prop="config.ackRequired" :rules="[]">
              <el-checkbox v-model="form.config.ackRequired" label="" :title="$t('devicesAdd.Acknowledgement required')" @change="form.config.nmsTicketing = false"></el-checkbox>
            </FormItem>
            <FormItem :span="width > 600 ? 6 : 12" :label="$t('devicesAdd.Nms ticketing')" tooltip="" prop="config.nmsTicketing" :rules="[]">
              <el-checkbox v-model="form.config.nmsTicketing" label="" :title="$t('devicesAdd.Nms ticketing')" :disabled="!form.config.ackRequired"></el-checkbox>
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Importance')" tooltip="" prop="importance" :rules="[]">
              <el-select v-model="form.importance" placeholder="" clearable filterable class="tw-w-full">
                <el-option v-for="item in deviceImportanceOption" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Login authentication')" tooltip="" prop="config.connectAuthType" :rules="[]">
              <el-select v-model="form.config.connectAuthType" :placeholder="$t('glob.Please select field', { field: $t('devicesAdd.Login authentication') })" filterable class="tw-w-full">
                <!-- <el-option label="Default" value="DEFAULT" class="tw-h-fit">
                  <div class="tw-flex tw-flex-col tw-items-start tw-justify-start tw-py-1">
                    <el-text class="tw-mr-auto tw-leading-[20px]">Default</el-text>
                    <el-text class="tw-mr-auto tw-leading-[16px]" size="small" type="info">User requires a current login.</el-text>
                  </div>
                </el-option> -->
                <el-option v-for="mode in loginModeOption" :key="`mode-${mode.value}`" :label="mode.label" :value="mode.value" class="tw-h-fit">
                  <div class="tw-flex tw-flex-col tw-items-start tw-justify-start tw-py-1">
                    <el-text class="tw-mr-auto tw-leading-[20px]">{{ mode.label }}</el-text>
                    <el-text v-for="desc in mode.desc" :key="'desc-' + desc" class="tw-mr-auto tw-mt-2 tw-leading-[16px]" size="small" type="info">{{ desc }}</el-text>
                  </div>
                </el-option>
                <!-- <el-option label="Require_Tfa" value="REQUIRE_TFA" class="tw-h-fit">
                  <div class="tw-flex tw-flex-col tw-items-start tw-justify-start tw-py-1">
                    <el-text class="tw-mr-auto tw-leading-[20px]">Require_Tfa</el-text>
                    <el-text class="tw-mr-auto tw-leading-[16px]" size="small" type="info">Connector authenticates user when connecting.<br />Onlytwo-factor authentication permitted.</el-text>
                  </div>
                </el-option> -->
              </el-select>
            </FormItem>
          </template>
        </FormGroup>
        <FormGroup :span="24" :label="$t('devicesAdd.Device informations')" tooltip="">
          <template #default="{}">
            <!-- 设备供应商 -->
            <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(资产管理中心_设备_分配设备供应商)">
              <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Device vendors')" tooltip="" prop="vendorIds" :rules="[]">
                <el-select :disabled="!userInfo.hasPermission(资产管理中心_设备_分配设备供应商)" v-model="form.vendorIds" :placeholder="$t('glob.Please select field', { field: $t('devicesAdd.Device vendors') })" default-first-option multiple clearable filterable class="tw-w-full">
                  <el-option v-for="item in vendorList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
              </FormItem>
            </el-tooltip>
            <FormItemTootip :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Model numbers')" :tooltip="$t('devicesAdd.model numbers discovered') + modelName" prop="config.modelNumbers" :rules="[]">
              <el-select v-model="form.config.modelNumbers" :placeholder="$t('glob.Please input field', { field: $t('devicesAdd.Model numbers') })" remote allow-create default-first-option multiple clearable filterable class="tw-w-full"></el-select>
            </FormItemTootip>
            <FormItemTootip :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Serial numbers')" :tooltip="$t('devicesAdd.serial numbers discovered') + serialNumber" prop="config.serialNumbers" :rules="[]">
              <el-select v-model="form.config.serialNumbers" :placeholder="$t('glob.Please input field', { field: $t('devicesAdd.Serial numbers') })" remote allow-create default-first-option multiple clearable filterable class="tw-w-full"></el-select>
            </FormItemTootip>
            <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Asset numbers')" tooltip="" prop="config.assetNumbers" :rules="[]">
              <el-select v-model="form.config.assetNumbers" :placeholder="$t('glob.Please input field', { field: $t('devicesAdd.Asset numbers') })" remote allow-create default-first-option multiple clearable filterable class="tw-w-full"></el-select>
            </FormItem>
            <!-- 设备类型 -->
            <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(资产管理中心_设备_分配设备类型)">
              <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Device types')" tooltip="" prop="typeIds" :rules="[]">
                <el-select :disabled="!userInfo.hasPermission(资产管理中心_设备_分配设备类型)" v-model="form.typeIds" :placeholder="$t('glob.Please select field', { field: $t('devicesAdd.Device types') })" default-first-option clearable filterable class="tw-w-full">
                  <el-option v-for="item in deviceTypeList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
              </FormItem>
            </el-tooltip>
            <!-- 设备分组 -->
            <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(资产管理中心_设备_分配设备分组)">
              <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Device groups')" tooltip="" prop="groupIds" :rules="[]">
                <el-select :disabled="!userInfo.hasPermission(资产管理中心_设备_分配设备分组)" v-model="form.groupIds" :placeholder="$t('glob.Please select field', { field: $t('devicesAdd.Device groups') })" default-first-option multiple clearable filterable class="tw-w-full">
                  <el-option v-for="item in deviceGroupList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
              </FormItem>
            </el-tooltip>
            <FormItem :span="width > 600 ? 12 : 24" :label="$t('devicesAdd.Business Unit')" tooltip="" prop="unit" :rules="[]">
              <el-input v-model="form.unit" :placeholder="$t('glob.Please input field', { field: $t('devicesAdd.Business Unit') })" clearable></el-input>
            </FormItem>
          </template>
        </FormGroup>
      </FormModel>
      <el-dialog :style="{ 'z-index': 9999 }" :title="$t('devicesAdd.Tips')" v-model="dialogVisible" :before-close="cancel" width="30%">
        <h2 style="color: #db3328">{{ `${$t("devicesAdd.IP Address Conflict, please modify the conflicting device IP")}` }}</h2>
        <div class="ipConflict">
          <div v-for="(v, i) in Conflict" :key="i">
            <p>
              {{ `${$t("devicesAdd.Conflicting customer")}` }}: <span>{{ v.conflictCustomerName }}</span>
            </p>
            <p>
              {{ `${$t("devicesAdd.Conflicting device")}` }}: <span>{{ v.hostName }}</span>
            </p>
            <p>
              {{ `${$t("devicesAdd.Conflicting IP")}` }}: <span>{{ v.conflictIp }}</span>
            </p>
          </div>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancel">{{ `${$t("glob.Cancel")}` }}</el-button>
            <el-button type="primary" @click="cancel">{{ `${$t("glob.Confirm")}` }}</el-button>
          </div>
        </template>
      </el-dialog>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </component>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, computed, h, createVNode, renderSlot, toRaw } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { templateRef } from "@vueuse/core";
import { buildTypeHelper } from "@/utils/type";
import { buildValidatorData } from "@/utils/validate";

import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import FormItemTootip from "@/components/formItem/FormItemTootip.vue";

import getUserInfo from "@/utils/getUserInfo";
import { getAlarmMerge } from "@/views/pages/apis/alarmMerge";
import { getTenantInfo } from "@/views/pages/apis/tenant";
import { getLocationList, getRegionsList, getNewRegionsList, getAlertClassificationsList, getVendorsList, getResourceTypeList, getDeviceGroupList, deviceImportanceOption, getDeviceDiscovery, getQueryLocation, loginModeOption, LoginMode, getNewQueryLocation, monitorSourcesOption } from "@/views/pages/apis/device";
import { getAlarmClassificationList } from "@/views/pages/apis/alarmClassification";

import { el } from "element-plus/es/locale";
import { 资产管理中心_设备_分配告警分类, 资产管理中心_区域_可读, 资产管理中心_场所_可读, 服务管理中心_告警分类_可读, 资产管理中心_设备供应商_可读, 资产管理中心_设备类型_可读, 资产管理中心_设备分组_可读, 资产管理中心_设备_分配设备供应商, 资产管理中心_设备_分配设备类型, 资产管理中心_设备_分配设备分组 } from "@/views/pages/permission";

const userInfo = getUserInfo();

const formRef = templateRef<InstanceType<typeof FormModel>>("formRef");
/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Item {
  id: string;
  modelIdent: string /** 模型标识 */;
  regionId: string /** 所在区域ID */;
  locationId: string /** 所在场所ID */;
  typeIds: string /** 资源类型ID列表 */;
  groupIds: string[] /** 设备组ID列表 */;
  notPermissionGroupIds: string[] /** 无权限设备组ID列表 */;
  vendorIds: string[] /** 服务商ID列表 */;
  notPermissionVendorIds: string[] /** 无权限服务商ID列表 */;
  alertClassificationIds: string[] /** 告警分类ID列表 */;
  notPermissionAlertClassificationIds: string[] /** 无权限告警分类ID列表 */;
  supportNoteIds: string[] /** 行动策略ID列表 */;
  assetNumber: string /** 资产编号 */;
  externalId: string /** 外部ID */;
  name: string /** 资源名称 **/;
  monitorSources: string[] /** 监控源 */;
  unit: string /** 业务单位 */;
  description: string /** 描述信息 */;
  timeZone: string /** 时区 */;
  importance: string /** 资源重要性 */;
  tags: string[] /** 标签列表 */;
  config: { ipAddress: string; dynamicIp: boolean; ackRequired: boolean; nmsTicketing: boolean; connectAuthType: keyof typeof LoginMode; modelNumbers: string[]; serialNumbers: string[]; assetNumbers: string[] } /** 配置信息 */;
  active: boolean /** 是否激活 */;
  delivery: boolean /* 是否交付*/;
  alias: string /** 资源别名 */;
  resourceOnlineTime: number /* 上线时间*/;
  resourceOfflineTime: number /* 下线时间*/;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const regionTree = ref<import("@/views/pages/apis/device").RegionsItem[]>([]);
const locationList = ref<import("@/views/pages/apis/device").LocationItem[]>([]);
const alarmList = ref<import("@/views/pages/apis/device").AlertClassificationsItem[]>([]);
const vendorList = ref<import("@/views/pages/apis/device").VendorsItem[]>([]);
const deviceTypeList = ref<import("@/views/pages/apis/device").ResourceTypeItem[]>([]);
const deviceGroupList = ref<import("@/views/pages/apis/device").DeviceGroupItem[]>([]);

const containerId = ref("");
/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  id: buildTypeHelper<Required<Item>["id"]>(""),
  modelIdent: buildTypeHelper<Required<Item>["modelIdent"]>(""),
  regionId: buildTypeHelper<Required<Item>["regionId"]>(""),
  locationId: buildTypeHelper<Required<Item>["locationId"]>(""),
  typeIds: buildTypeHelper<Required<Item>["typeIds"]>(""),
  groupIds: buildTypeHelper<Required<Item>["groupIds"]>([]),
  notPermissionGroupIds: buildTypeHelper<Required<Item>["notPermissionGroupIds"]>([]),
  vendorIds: buildTypeHelper<Required<Item>["vendorIds"]>([]),
  notPermissionVendorIds: buildTypeHelper<Required<Item>["notPermissionVendorIds"]>([]),
  alertClassificationIds: buildTypeHelper<Required<Item>["alertClassificationIds"]>([]),
  notPermissionAlertClassificationIds: buildTypeHelper<Required<Item>["notPermissionAlertClassificationIds"]>([]),
  supportNoteIds: buildTypeHelper<Required<Item>["supportNoteIds"]>([]),
  assetNumber: buildTypeHelper<Required<Item>["assetNumber"]>(""),
  externalId: buildTypeHelper<Required<Item>["externalId"]>(""),
  name: buildTypeHelper<Required<Item>["name"]>(""),
  alias: buildTypeHelper<Required<Item>["alias"]>("") /** 资源别名 */,
  monitorSources: buildTypeHelper<Required<Item>["monitorSources"]>([]),
  unit: buildTypeHelper<Required<Item>["unit"]>(""),
  description: buildTypeHelper<Required<Item>["description"]>(""),
  timeZone: buildTypeHelper<Required<Item>["timeZone"]>(""),
  importance: buildTypeHelper<Required<Item>["importance"]>("Unknown"),
  tags: buildTypeHelper<Required<Item>["tags"]>([]),
  config: buildTypeHelper<Required<Item>["config"]>({
    ipAddress: "" /* ip地址 */,
    dynamicIp: false /* 是否默认IP */,
    ackRequired: false /* 确认告警 */,
    nmsTicketing: false /* 自动事件 */,
    connectAuthType: LoginMode.DEFAULT /* 远程登录认证 */,
    modelNumbers: [] /* 型号 */,
    serialNumbers: [] /* 序列号 */,
    assetNumbers: [] /* 资产编号 */,
  }),
  active: buildTypeHelper<Required<Item>["active"]>(true),
  delivery: buildTypeHelper<Required<Item>["delivery"]>(false) /* 是否交付*/,
  resourceOnlineTime: buildTypeHelper<Required<Item>["resourceOnlineTime"]>("") /* 上线时间*/,
  resourceOfflineTime: buildTypeHelper<Required<Item>["resourceOfflineTime"]>("") /* 下线时间*/,
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
const allRegion = ref<Record<string, any>>([]);

async function handleGetLocation(val) {
  try {
    form.value.locationId = "";
    if (!val) return (locationList.value = []);
    const params = { paging: { pageNumber: 1, pageSize: 999999 }, regionId: val, active: true };

    const { success, message, data } = await getNewQueryLocation(params as any);
    if (!success) throw Object.assign(new Error(message), { success, data });
    locationList.value = (data instanceof Array ? data : []) as any;

    console.log(
      $params.value.locationId,
      locationList.value.find((v) => v.id === $params.value.locationId)
    );
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  await Promise.all([
    (async (req) => {
      const { success, message, data } = await getNewRegionsList(req);
      if (!success) throw Object.assign(new Error(message), { success, data });
      allRegion.value = data;
      regionTree.value = buildTree<import("@/views/pages/apis/device").RegionsItem>(data instanceof Array ? data : []);
    })({}),
    handleGetLocation(params.regionId ? params.regionId : ""),
    // (async (req) => {
    //   // const { success, message, data } = await getNewQueryLocation(req);
    //   // if (!success) throw Object.assign(new Error(message), { success, data });
    //   // locationList.value = data instanceof Array ? data : [];
    // })({ paging: { pageNumber: 1, pageSize: 999999 }, active: true }),
    (async (req) => {
      // const { success, message, data } = await getAlertClassificationsList(req);
      const { success, message, data } = await getAlarmClassificationList(req);
      if (!success) throw Object.assign(new Error(message), { success, data });
      alarmList.value = data instanceof Array ? data : [];
    })({}),
    (async (req) => {
      const { success, message, data } = await getVendorsList(req);
      if (!success) throw Object.assign(new Error(message), { success, data });
      vendorList.value = data instanceof Array ? data : [];
    })({ vendorType: "DEVICE" as const }),
    (async (req) => {
      const { success, message, data } = await getResourceTypeList(req);
      if (!success) throw Object.assign(new Error(message), { success, data });
      deviceTypeList.value = data instanceof Array ? data : [];
    })({}),
    (async (req) => {
      const { success, message, data } = await getDeviceGroupList(req);
      if (!success) throw Object.assign(new Error(message), { success, data });
      deviceGroupList.value = data instanceof Array ? data : [];
    })({}),
    //查询客户信息
    (async (req) => {
      const { success, message, data } = await getTenantInfo(req);
      if (!success) throw Object.assign(new Error(message), { success, data });
      containerId.value = data.containerId;
      // containerId.value = 11;
    })({}),
  ]);
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  if (!allRegion.value.find((v) => v.id === $params.value.regionId)) form.value.regionId = "";
  if (!locationList.value.find((v) => v.id === $params.value.locationId)) form.value.locationId = "";
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  return form;
}
/* ---------------------------------------------------------‖ ↑↑ 钩子 Start ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE ACHIEVE  END  ========================================================= */
/**
 * TODO: 本地方法
 */
interface Tree {
  children: Tree[];
  id: string;
  parentId: string;
  [key: string]: any;
}
function buildTree<T extends Tree>(data: T[]): T[] {
  for (let index = 0; index < data.length; index++) {
    if (!(data[index].children instanceof Array)) data[index].children = [];
    data[index].children.splice(0, data[index].children.length, ...data.filter((v) => v.parentId === data[index].id).map((v) => Object.assign(v, { isBuild: true })));
  }
  return data.filter((v) => {
    if ("isBuild" in v) {
      delete v.isBuild;
      return false;
    } else return true;
  });
}
/*  */
/**
 * TODO: 窗口方法
 */
interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 100,
});

const { t } = useI18n();

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

const modelName = ref("");
const serialNumber = ref("");
const dialogVisible = ref(false);
const Conflict = ref([]);
interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<Item>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Partial<Item>) => void;
  reject?: (value: Partial<Item>) => void;
  callback?: (form: Item) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item> & Record<string, any>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {}) as Item);
async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
    // 根据权限设置字段值
    form.alertClassificationIds = userInfo.hasPermission(服务管理中心_告警分类_可读) ? form.alertClassificationIds : [];
    const alarmArr = new Set(alarmList.value.map((item) => String(item.id)));
    form.alertClassificationIds = (form.alertClassificationIds || []).filter((id) => alarmArr.has(id));
    form.notPermissionAlertClassificationIds = (form.alertClassificationIds || []).filter((id) => !alarmArr.has(id));

    form.vendorIds = userInfo.hasPermission(资产管理中心_设备供应商_可读) ? form.vendorIds : [];
    const vendorAll = new Set(vendorList.value.map((item) => String(item.id)));
    form.vendorIds = (form.vendorIds || [])?.filter((id) => vendorAll.has(id));
    form.notPermissionVendorIds = (form.vendorIds || [])?.filter((id) => !vendorAll.has(id));

    form.typeIds = userInfo.hasPermission(资产管理中心_设备类型_可读) ? form.typeIds : "";
    const idsToCheck = Array.isArray(form.typeIds) ? form.typeIds : [form.typeIds];
    const deviceTypeAll = new Set(deviceTypeList.value.map((item) => item.id));
    form.typeIds = idsToCheck?.filter((id) => deviceTypeAll.has(id));

    form.groupIds = userInfo.hasPermission(资产管理中心_设备分组_可读) ? form.groupIds : [];
    const groupAll = new Set(deviceGroupList.value.map((item) => String(item.id)));
    form.groupIds = (form.groupIds || []).filter((id) => groupAll.has(id));
    form.notPermissionGroupIds = (form.groupIds || []).filter((id) => !groupAll.has(id));
    // console.log(form, 6666666);
    // form.resourceOfflineTime=form.resourceOfflineTime*1
    // form.resourceOnlineTime=form.resourceOnlineTime*1
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }

  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], cloneDeep(toRaw(util.value)) as never)) });
    },
    form as Required<Item>
  );
}

async function getAutoCloseEvent() {
  // // console.log(123456789);
  getAlarmMerge({})
    .then((res: any) => {
      // // console.log(res);
      if (res.success) {
        // const form = reactive({
        // // console.log(res, 55555555);
        if (res.data.autoEvent) {
          (form.value.config.ackRequired = true) /* 确认告警 */, (form.value.config.nmsTicketing = true);
        } else {
          (form.value.config.ackRequired = false) /* 确认告警 */, (form.value.config.nmsTicketing = false);
        }
      }
    })
    .catch((err) => {
      ElMessage.error(err.message);
    });
}
async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => formRef.value.validate(resolve));
  } catch (error) {
    return false;
  }
}

function cancel() {
  dialogVisible.value = false;
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(Object.assign(form.value, { containerId: containerId.value }) || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error.message == "IP地址冲突") {
      Conflict.value = error.data;
      dialogVisible.value = true;
    } else {
      if (error instanceof Error) ElMessage.error(error.message);
    }
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  form.value.resourceOfflineTime = form.value.resourceOfflineTime * 1;
  form.value.resourceOnlineTime = form.value.resourceOnlineTime * 1;
  // console.log(form.value);

  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();

defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, type: string, callback?: (form: Item) => Promise<void>): Promise<unknown> {
    if (data.visible) handleCancel();
    const wait = new Promise<Record<string, unknown>>((resolve, reject) => ((data.resolve = resolve), (data.reject = reject)));
    $params.value = cloneDeep(params);
    // form.value = { ...params };
    data.visible = true;
    data.loading = true;
    data.submitLoading = true;

    data.callback = callback;
    // // console.log(type);
    if (type !== "update") {
      setTimeout(() => {
        // getAutoCloseEvent();
      }, 800);
    } else {
      getDeviceDiscovery({ id: params.id }).then((res: any) => {
        if (res.success) {
          res.data.forEach((item: any) => [(modelName.value = item.modelName || ""), (serialNumber.value = item.serialNumber || "")]);
        }
      });
    }

    await nextTick();
    try {
      await runningInit($params.value);
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      handleCancel();
    }
    handleReset();
    data.loading = false;
    data.submitLoading = false;

    try {
      return await wait;
    } catch (error) {
      return error;
    }
  },
  async confirm<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.confirm(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async alert<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.alert(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "fit-content" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
});

function disableEndTime(time) {
  if (!form.value.resourceOnlineTime) {
    return false;
  } else if (form.value.resourceOnlineTime && time > form.value.resourceOnlineTime) {
    return false; // 当前日期不能小于开始日期
  } else {
    return true; // 其他情况都返回true，表示该日期无效
  }
}

function disableStartTime(time) {
  if (!form.value.resourceOfflineTime) {
    return false;
  } else if (form.value.resourceOfflineTime && time < form.value.resourceOfflineTime) {
    return false; // 当前日期不能小于开始日期
  } else {
    return true; // 其他情况都返回true，表示该日期无效
  }
}
</script>

<style scoped lang="scss">
.ipConflict {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  div {
    margin: 10px 0;
    span {
      color: #27a2e8;
    }
  }
}
</style>
