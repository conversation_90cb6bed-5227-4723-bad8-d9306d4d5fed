<!--  -->
<template>
  <div>
    <el-dialog :title="`${i18n.t('devicesInfo.Please select a service directory')}`" v-model="dialogVisible" :before-close="cancel" width="30%">
      <el-form ref="serviceForm" :rules="rules" :model="form">
        <el-form-item :label="`${i18n.t('devicesInfo.Service catalog')}：`" :label-width="formLabelWidth" prop="number">
          <el-select v-model="serviceCatalogId" multiple clearable filterable :placeholder="`${i18n.t('devicesInfo.Please select')}`">
            <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">{{ `${i18n.t("glob.NO")}` }}</el-button>
          <el-button type="primary" @click="submit" :loading="submitButLoading">{{ `${i18n.t("glob.OK")}` }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { bindServiceCatalog, getnewServiceCatalogList } from "@/views/pages/apis/deviceManage";
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMenuItem } from "element-plus";
import { useI18n } from "vue-i18n";
export default {
  props: {
    isAdd: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
  },
  emits: ["confirm"],
  data() {
    const i18n = useI18n();
    return {
      i18n,
      userInfo: getUserInfo(),
      serviceCatalogId: [],
      formLabelWidth: "120px",
      dialogVisible: false,

      title: "",
      options: [],
      resourceId: "",
      rules: {
        serviceCatalogId: [{ required: true, message: i18n.t("devicesInfo.Please select a service directory"), trigger: "change" }],
      },
      submitButLoading: false,
    };
  },
  watch: {},
  // created() {
  // },
  mounted() {
    this.getServiceCatalogList();
  },

  methods: {
    getServiceCatalogList() {
      if (!this.resourceId) return false;
      getnewServiceCatalogList({ resourceId: this.resourceId, containerId: this.userInfo.currentTenant.containerId, queryPermissionId: "515413313438351360", verifyPermissionIds: "" }).then((res) => {
        if (res) {
          let arr = [...res];
          this.options = arr;
        }
      });
    },
    cancel() {
      this.dialogVisible = false;
      this.serviceCatalogId = [];
      this.$emit("confirm", false);
    },
    submit() {
      if (this.serviceCatalogId.length > 0) {
        this.submitButLoading = true;
        let data = this.serviceCatalogId.map((id) => ({
          resourceId: this.resourceId,
          serviceCatalogId: id,
        }));
        bindServiceCatalog(data)
          .then((res) => {
            if (res.success) {
              this.$message.success("操作成功");
              this.dialogVisible = false;
              this.$emit("confirm", true);
            } else this.$message.error(JSON.parse(res.data)?.message);
          })
          .catch((e) => {
            if (e instanceof Error) ElMessage.error(e.message);
          })
          .finally(() => {
            this.submitButLoading = false;
          });
      } else {
        this.dialogVisible = false;
      }
    },
  },
  expose: ["dialogVisible", "serviceCatalogId", "resourceId", "disabledList", "getServiceCatalogList"],
};
</script>
<style scoped lang="scss"></style>
