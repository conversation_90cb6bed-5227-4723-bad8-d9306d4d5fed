export const stateEnum = [
  {
    key: "NOT_STARTED",
    value: "未开始",
    prop: {
      class: "elstyle-tag--info",
    },
  },
  {
    key: "NEW",
    value: "新建",
    prop: {
      class: "elstyle-tag--info",
    },
  },
  {
    key: "PROCESSING",
    value: "处理中",
    prop: {
      class: "elstyle-tag--warning",
    },
  },
  {
    prop: {
      class: "elstyle-tag--success",
    },
    key: "COMPLETED",
    value: "完成",
  },
  {
    prop: {
      class: "elstyle-tag--info",
    },
    key: "CLOSED",
    value: "关闭",
  },
  {
    prop: {
      class: "elstyle-tag--success",
    },
    key: "WAITING_FOR_RECEIVE",
    value: "未接手",
  },
  {
    prop: {
      class: "elstyle-tag--info",
    },
    key: "UNASSIGNED",
    value: "未分配",
  },
  {
    prop: {
      class: "elstyle-tag--danger",
    },
    key: "PROCESSING",
    value: "处理中",
  },
  {
    prop: {
      class: "elstyle-tag--danger",
    },
    key: "SUSPENDED",
    value: "挂起中",
  },
  {
    prop: {
      class: "elstyle-tag--warning",
    },
    key: "PENDING_APPROVAL",
    value: "挂起审批中",
  },
  {
    prop: {
      class: "elstyle-tag--success",
    },
    key: "COMPLETED",
    value: "完成",
  },
  {
    prop: {
      class: "elstyle-tag--info",
    },
    key: "CLOSED",
    value: "关闭",
  },
  {
    prop: {
      class: "elstyle-tag--info",
    },
    key: "AUTO_CLOSED",
    value: "自动关闭",
  },
];

export const eventStatus = {
  NOT_STARTED: { key: "NOT_STARTED", value: "未开始" },
  PROCESSING: {
    key: "PROCESSING",
    value: "处理中",
  },
  COMPLETED: {
    key: "COMPLETED",
    value: "完成",
  },
  CLOSED: {
    key: "CLOSED",
    value: "关闭",
  },
  AUTO_CLOSED: {
    key: "CLOSED",
    value: "自动关闭",
  },
  WAITING_FOR_RECEIVE: {
    key: "WAITING_FOR_RECEIVE",
    value: "未接手",
  },
  UNASSIGNED: {
    key: "UNASSIGNED",
    value: "未分配",
  },

  SUSPENDED: {
    key: "SUSPENDED",
    value: "挂起中",
  },
  PENDING_APPROVAL: {
    key: "PENDING_APPROVAL",
    value: "挂起审批中",
  },
  NEW: {
    key: "NEW",
    value: "新建",
  },
};
