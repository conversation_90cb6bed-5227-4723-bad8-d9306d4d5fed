<template>
  <div class="viewport" ref="viewport">
    <!-- <modal ref="modal" @reconnect="connect(query)" /> -->
    <!-- tabindex allows for div to be focused -->
    <div ref="display" class="display" tabindex="0" />
  </div>
</template>

<script>
import Guacamole from "guacamole-common-js";
import GuacMouse from "@/lib/GuacMouse";
import states from "@/lib/states";
import clipboard from "@/lib/clipboard";
import { ElMessage } from "element-plus";
import getUserInfo from "@/utils/getUserInfo";

import { setRemoteLoginLog } from "@/views/pages/apis/device";

Guacamole.Mouse = GuacMouse.mouse;

const wsUrl = `/gw_proxy/netcare/openapi/cloudCare/tool/remote/ws`;
// const wsUrl = `/gw_proxy/netcare/openapi/cloudCare/tool/guacamole/ws`;
// const httpUrl = `http://${location.host}/tunnel`

export default {
  components: {},
  props: {
    query: {
      type: String,
      required: true,
    },
    forceHttp: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  data() {
    return {
      connected: false,
      /**@type {Guacamole.Display} */
      display: null,
      currentAdjustedHeight: null,
      /**@type {Guacamole.Client} */
      client: null,
      /**@type {Guacamole.Keyboard} */
      keyboard: null,
      /**@type {Guacamole.Mouse} */
      mouse: null,
      lastEvent: null,
      connectionState: states.IDLE,
      errorMessage: "",
      arguments: {},
      lineInfo: JSON.parse(atob(this.$route.query.data)),
      connectId: "",
    };
  },
  watch: {
    connectionState(state) {
      // this.$refs.modal.show(state, this.errorMessage);
    },
  },
  mounted() {
    this.init();
    window.addEventListener("beforeunload", (event) => {
      event.preventDefault();
      if (this.client) this.handleClientClose();
    });
  },
  methods: {
    init(isReset = false) {
      this.connectId = new Date().getTime().toString(36) + Math.random().toString(36).substr(2, 9);

      const rdpData = Object.assign(JSON.parse(atob(this.$route.query.data)), { security: "rdp" });
      rdpData.id = this.connectId; /* 防止刷新id重复,id重新生成 */

      const _data = Object.assign(JSON.parse(atob(this.$route.query.data)));
      _data.id = this.connectId; /* 防止刷新id重复,id重新生成 */

      const queryString = [`Authorization=${getUserInfo().token}`];
      for (const [k, v] of Object.entries(Object.assign(this.$route.query, !isReset ? { data: btoa(JSON.stringify(_data)) } : { data: btoa(JSON.stringify(rdpData)) }))) {
        if (v) {
          queryString.push(`${k}=${encodeURIComponent(v)}`);
        }
      }

      if (queryString.join("&") && !this.connected) {
        this.connected = true;
        this.connect(queryString.join("&"));
      }
    },
    send(cmd) {
      if (!this.client) {
        return;
      }
      for (const c of cmd.data) {
        this.client.sendKeyEvent(1, c.charCodeAt(0));
      }
    },
    copy(cmd) {
      if (!this.client) {
        return;
      }
      clipboard.cache = {
        type: "text/plain",
        data: cmd.data,
      };
      clipboard.setRemoteClipboard(this.client);
    },
    handleMouseState(mouseState) {
      const scaledMouseState = Object.assign({}, mouseState, {
        x: mouseState.x / this.display.getScale(),
        y: mouseState.y / this.display.getScale(),
      });
      this.client.sendMouseState(scaledMouseState);
    },
    resize() {
      const elm = this.$refs.viewport;
      if (!elm || !elm.offsetWidth) {
        // resize is being called on the hidden window
        return;
      }

      let pixelDensity = window.devicePixelRatio || 1;
      const width = this.lineInfo.width;
      const height = this.lineInfo.height;
      if (this.display.getWidth() !== width || this.display.getHeight() !== height) {
        this.client.sendSize(width, height);
      }
      // setting timeout so display has time to get the correct size
      setTimeout(() => {
        const scale = Math.min(elm.clientWidth / Math.max(this.display.getWidth(), 1), elm.clientHeight / Math.max(this.display.getHeight(), 1));
        this.display.scale(scale);
      }, 100);
    },
    handleClientClose() {
      // const data = Object.assign(JSON.parse(atob(this.$route.query.data)));
      setRemoteLoginLog({ connectId: this.connectId });
    },
    connect(query) {
      // let tunnel;

      // if (window.WebSocket && !this.forceHttp) {
      const tunnel = new Guacamole.WebSocketTunnel(wsUrl);
      // }
      // else {
      //   tunnel = new Guacamole.HTTPTunnel(httpUrl, true)
      // }

      if (this.client) {
        this.display.scale(0);
        this.uninstallKeyboard();
      }

      this.client = new Guacamole.Client(tunnel);
      clipboard.install(this.client);

      tunnel.onerror = (status) => {
        // eslint-disable-next-line no-console
        ElMessage.error("连接失败");

        console.error(`Tunnel failed ${JSON.stringify(status)}`);
        this.connectionState = states.TUNNEL_ERROR;
      };

      tunnel.onstatechange = (state) => {
        switch (state) {
          // Connection is being established
          case Guacamole.Tunnel.State.CONNECTING:
            this.connectionState = states.CONNECTING;
            break;

          // Connection is established / no longer unstable
          case Guacamole.Tunnel.State.OPEN:
            this.connectionState = states.CONNECTED;
            // console.log("开始");
            break;

          // Connection is established but misbehaving
          case Guacamole.Tunnel.State.UNSTABLE:
            // TODO
            break;

          // Connection has closed
          case Guacamole.Tunnel.State.CLOSED:
            this.connectionState = states.DISCONNECTED;

            // console.log("结束");
            this.client.disconnect();
            this.connected = false;

            break;
        }
      };

      this.client.onstatechange = (clientState) => {
        switch (clientState) {
          case 0:
            this.connectionState = states.IDLE;
            break;
          case 1:
            // connecting ignored for some reason?
            break;
          case 2:
            this.connectionState = states.WAITING;
            break;
          case 3:
            this.connectionState = states.CONNECTED;
            window.addEventListener("resize", this.resize);
            this.$refs.viewport.addEventListener("mouseenter", this.resize);

            clipboard.setRemoteClipboard(this.client);

            // eslint-disable-next-line no-fallthrough
            break;
          case 4:
            break;
          case 5:
            // disconnected, disconnecting
            this.handleClientClose();
            break;
        }
      };

      this.client.onerror = (error) => {
        // eslint-disable-next-line no-console
        if (this.lineInfo.protocol === "rdp" && error.message.includes("untrusted/self-signed certificate")) {
          this.$message.warning("启用RDP兼容模式,连接中...");
          setTimeout(() => {
            this.init(true);
          }, 3000);
          return;
        }
        switch (error.code) {
          case 0x0100:
            this.$message.error("请求的操作不支持");
            break;
          case 0x0200:
            this.$message.error("由于内部故障，无法执行操作。");
            break;
          case 0x0201:
            this.$message.error("由于服务器繁忙，无法执行操作。");
            break;
          case 0x0202:
            this.$message.error("操作无法执行，因为上游服务器没有响应。");
            break;
          case 0x0203:
            this.$message.error("由于上游服务器的错误或其他意外情况，操作不成功。");
            break;
          case 0x0204:
            this.$message.error("请求的资源不存在，无法执行操作。");
            break;
          case 0x0205:
            this.$message.error("无法执行操作，因为请求的资源已经在使用中。");
            break;
          case 0x0206:
            this.$message.error("由于请求的资源现在已关闭，无法执行操作。");
            break;
          case 0x0207:
            this.$message.error("操作无法执行，因为上游服务器不存在。");
            break;
          case 0x0208:
            this.$message.error("无法执行操作，因为上游服务器无法为请求提供服务。");
            break;
          case 0x0209:
            this.$message.error("上游服务器内的会话已经结束，因为它与另一个会话冲突。");
            break;
          case 0x020a:
            this.$message.error("上游服务器内的会话已经结束，因为它看起来不活跃。");
            break;
          case 0x020b:
            this.$message.error("上游服务器内部会话被强制终止。");
            break;
          case 0x0300:
            this.$message.error("操作无法执行，因为给出了错误的参数。");
            break;
          case 0x0301:
            this.$message.error("账号密码错误");
            break;
          case 0x0303:
            this.$message.error("执行该操作的权限被拒绝，即使用户被授权，也不会被授予该操作。");
            break;
          case 0x0308:
            this.$message.error("客户端响应时间过长。");
            break;
          case 0x030d:
            this.$message.error("客户端发送数据过多。");
            break;
          case 0x030f:
            this.$message.error("客户端发送的数据类型不支持或不期望。");
            break;
          case 0x031d:
            this.$message.error("操作失败，当前客户端已经使用了太多的资源。");
            break;

          default:
            this.$message.error(error.message);
            break;
        }

        this.errorMessage = error.message;
        this.connectionState = states.CLIENT_ERROR;
      };

      this.client.onsync = () => {};

      // Test for argument mutability whenever an argument value is received
      this.client.onargv = (stream, mimetype, name) => {
        if (mimetype !== "text/plain") return;

        const reader = new Guacamole.StringReader(stream);

        // Assemble received data into a single string
        let value = "";
        reader.ontext = (text) => {
          value += text;
        };

        // Test mutability once stream is finished, storing the current value for the argument only if it is mutable
        reader.onend = () => {
          const stream = this.client.createArgumentValueStream("text/plain", name);
          stream.onack = (status) => {
            if (status.isError()) {
              // ignore reject
              return;
            }
            this.arguments[name] = value;
          };
        };
      };

      this.client.onclipboard = clipboard.onClipboard;
      this.display = this.client.getDisplay();
      const displayElm = this.$refs.display;
      displayElm.appendChild(this.display.getElement());
      displayElm.addEventListener("contextmenu", (e) => {
        e.stopPropagation();
        if (e.preventDefault) {
          e.preventDefault();
        }
        e.returnValue = false;
      });
      this.client.connect(query);
      window.onunload = () => this.client.disconnect();

      this.mouse = new Guacamole.Mouse(displayElm);
      // Hide software cursor when mouse leaves display
      this.mouse.onmouseout = () => {
        if (!this.display) return;
        this.display.showCursor(false);
      };

      // allows focusing on the display div so that keyboard doesn't always go to session
      displayElm.onclick = () => {
        displayElm.focus();
      };
      displayElm.onfocus = () => {
        displayElm.className = "focus";
      };
      displayElm.onblur = () => {
        displayElm.className = "";
      };

      this.keyboard = new Guacamole.Keyboard(displayElm);
      this.installKeyboard();
      this.mouse.onmousedown = this.mouse.onmouseup = this.mouse.onmousemove = this.handleMouseState;
      setTimeout(() => {
        this.resize();
        displayElm.focus();
      }, 1000); // $nextTick wasn't enough
    },
    installKeyboard() {
      this.keyboard.onkeydown = (keysym) => {
        this.client.sendKeyEvent(1, keysym);
      };
      this.keyboard.onkeyup = (keysym) => {
        this.client.sendKeyEvent(0, keysym);
      };
    },
    uninstallKeyboard() {
      this.keyboard.onkeydown = this.keyboard.onkeyup = () => {};
    },
  },
};
</script>

<style scoped>
.display {
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.viewport {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
