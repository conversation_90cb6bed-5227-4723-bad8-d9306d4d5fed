<template>
  <el-dialog :title="type === 'add' ? '新增设备' : '编辑设备'" v-model="dialogFormVisible" :before-close="cancel" width="50%">
    <div style="height: 400px" class="device-dialog">
      <el-scrollbar height="400px">
        <el-form :model="form" :inline="true" :rules="rules" ref="ruleForm" class="deviceForm">
          <el-form-item label="名称:" :label-width="formLabelWidth" prop="name">
            <el-input v-emoji style="width: 202px" v-model="form.name" autocomplete="off" placeholder="请输入设备名称"></el-input>
          </el-form-item>
          <el-form-item label="描述:" :label-width="formLabelWidth" prop="description">
            <el-input style="width: 202px" v-model="form.description" autocomplete="off" placeholder="请输入设备描述"></el-input>
          </el-form-item>
          <el-form-item label="区域:" :label-width="formLabelWidth" prop="regionId">
            <el-cascader placeholder="请选择区域" v-model="form.regionId" :options="allRegionSelect" :props="{ checkStrictly: true, value: 'id', label: 'name' }" clearable></el-cascader>

            <!-- <el-select v-model="form.regionId" placeholder="请选择区域">

            </el-select> -->
          </el-form-item>
          <el-form-item label="场所:" :label-width="formLabelWidth" prop="locationId">
            <el-select v-model="form.locationId" placeholder="请选择场所">
              <el-option v-for="item in locationList as any[]" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="外部ID:" :label-width="formLabelWidth" prop="externalId">
            <el-input style="width: 202px" v-model="form.externalId" autocomplete="off" placeholder="请输入外部ID"></el-input>
          </el-form-item>
          <el-form-item label="标签:" :label-width="formLabelWidth" prop="tags">
            <el-select v-model="form.tags" multiple remote filterable allow-create default-first-option :reserve-keyword="false" placeholder="请输入"> </el-select>

            <!-- <input-tag :type="'tags'" :tags="form.tags" size="medium" @blur="blurChange" @tagClose="tagClose" /> -->
          </el-form-item>

          <el-form-item label="监控源:" :label-width="formLabelWidth">
            <el-select v-model="form.monitorSources" multiple placeholder="请输入">
              <el-option v-for="item in monitorSourceList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>

            <!-- <el-select v-model="form.region" placeholder="请选择活动区域">
              <el-option label="区域一" value="shanghai"></el-option>
              <el-option label="区域二" value="beijing"></el-option>
            </el-select> -->
          </el-form-item>
          <el-form-item label="是否上线:" :label-width="formLabelWidth" prop="active">
            <el-checkbox v-model="form.active">上线</el-checkbox>
          </el-form-item>
          <!-- 分割线 -->
          <div class="divider"></div>
          <!-- 分割线 -->

          <el-form-item label="IP地址:" :label-width="formLabelWidth" prop="config.ipAddress">
            <el-input style="width: 202px" v-model="form.config.ipAddress" autocomplete="off" placeholder="请输入IP地址"></el-input>
          </el-form-item>
          <el-form-item label="是否为动态IP:" :label-width="formLabelWidth" prop="config.dynamicIp">
            <el-checkbox v-model="form.config.dynamicIp">动态IP</el-checkbox>
            <el-tooltip class="item" effect="dark" content="将使用设备名称解析设备的IP地址，需要为客户配置Dns监控和Dns组" placement="top">
              <el-icon style="color: #ffa320; cursor: pointer; margin-left: 10px"><el-icon-question /></el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="告警分类:" :label-width="formLabelWidth" prop="alertClassificationIds">
            <el-select v-model="form.alertClassificationIds" placeholder="请选择告警分类" multiple>
              <el-option v-for="item in alarmList as any[]" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否确认告警:" :label-width="formLabelWidth" prop="config.ackRequired">
            <el-checkbox v-model="form.config.ackRequired">确认告警</el-checkbox>
            是否自动事件:
            <el-checkbox v-model="form.config.nmsTicketing" :disabled="!form.config.ackRequired">自动事件</el-checkbox>
          </el-form-item>

          <el-form-item label="重要性:" :label-width="formLabelWidth" prop="importance">
            <el-select v-model="form.importance" placeholder="请选择重要性">
              <el-option label="High" value="High"></el-option>
              <el-option label="Medium" value="Medium"></el-option>
              <el-option label="Low" value="Low"></el-option>
              <el-option label="None" value="None"></el-option>
              <el-option label="Unknown" value="Unknown"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="远程登录认证:" :label-width="formLabelWidth" prop="config.connectAuthType">
            <el-select v-model="form.config.connectAuthType" remote clearable placeholder="请选择远程登录" class="login-auth" :style="{ width: '100%' }" popper-class="select" :popper-append-to-body="false">
              <el-option label="Default" value="DEFAULT">
                <!-- <div class="association-select">
                 <p>Default</p>
              <p class="summary">User requires a current login.</p>
            </div> -->

                <p>Default</p>
                <p>User requires a current login.</p>
              </el-option>
              <el-option label="On_Connect" value="ON_CONNECT">
                <p>On_Connect</p>
                <p>Connector authenticates user when connecting.</p>

                <!-- <div class="association-select">
              <p>On_Connect</p>
              <p class="summary">Connector authenticates user when connecting.</p>
            </div> -->
              </el-option>
              <el-option label="Require_Tfa" value="REQUIRE_TFA">
                <div>
                  <p>Require_Tfa</p>
                  <p>Connector authenticates user when connecting.</p>
                  <p>Onlytwo-factor authentication permitted.</p>
                </div>

                <!-- <div class="association-select">
              <p>Require_Tfa</p>
              <p class="summary">
                Connector authenticates user when connecting. <br />
                Onlytwo-factor authentication permitted.
              </p>
            </div> -->
              </el-option>
            </el-select>
          </el-form-item>

          <!-- 分割线 -->
          <div class="divider"></div>
          <!-- 分割线 -->
          <el-form-item label="供应商:" :label-width="formLabelWidth" prop="vendorIds">
            <el-select v-model="form.vendorIds" placeholder="请选择供应商" multiple>
              <el-option v-for="item in supplierList as any" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="型号:" :label-width="formLabelWidth" prop="config.modelNumbers">
            <el-select v-model="form.config.modelNumbers" multiple remote filterable allow-create default-first-option :reserve-keyword="false" placeholder="请输入"> </el-select>

            <!-- <input-tag :type="'modelNumbers'" :tags="form.config.modelNumbers" size="medium" @blur="blurChange" @tagClose="tagClose" /> -->
          </el-form-item>
          <el-form-item label="序列号:" :label-width="formLabelWidth" prop="config.serialNumbers">
            <el-select v-model="form.config.serialNumbers" multiple remote filterable allow-create default-first-option :reserve-keyword="false" placeholder="请输入"> </el-select>

            <!-- <input-tag :type="'serialNumbers'" :tags="form.config.serialNumbers" size="medium" @blur="blurChange" @tagClose="tagClose" /> -->
          </el-form-item>
          <el-form-item label="资产编号:" :label-width="formLabelWidth" prop="config.assetNumbers">
            <el-select v-model="form.config.assetNumbers" multiple remote filterable allow-create default-first-option :reserve-keyword="false" placeholder="请输入"> </el-select>

            <!-- <input-tag :type="'assetNumbers'" :tags="form.config.assetNumbers" size="medium" @blur="blurChange" @tagClose="tagClose" /> -->
          </el-form-item>
          <el-form-item label="设备类型:" :label-width="formLabelWidth" prop="typeIds">
            <el-select v-model="form.typeIds" placeholder="请选择设备类型" multiple>
              <el-option v-for="item in deviceTypeList as any" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备分组:" :label-width="formLabelWidth" prop="groupIds">
            <el-select v-model="form.groupIds" placeholder="请选择设备分组" multiple>
              <el-option v-for="item in deviceGroupList as any" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>

          <!-- <el-form-item label="名称:" :label-width="formLabelWidth" prop="unit">
            <el-input style="width: 202px" v-model="form.unit" autocomplete="off" placeholder="请输入设备名称"></el-input>
          </el-form-item> -->
          <el-form-item label="业务单位:" :label-width="formLabelWidth" prop="unit">
            <el-input style="width: 202px" v-model="form.unit" autocomplete="off" placeholder="请输入业务单位名称"></el-input>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { defineComponent } from "vue";
import { QuestionFilled as ElIconQuestion } from "@element-plus/icons-vue";
import mixin from "./mixin";
import regionMixin from "../regionManage/js/mixin";
import { addDevice, editDevice, getDeviceDetaile } from "@/views/pages/apis/deviceManage";
import { ElMessage } from "element-plus";
import inputTag from "@/components/input-tag";

import { getAlarmMerge } from "@/views/pages/apis/alarmMerge";

export default defineComponent({
  name: "EventCenterIntelNoiseReductCreate",
  components: {
    // inputTag,
    ElIconQuestion,
  },
  mixins: [mixin, regionMixin],
  props: {
    id: {
      type: String,
      default: "",
    },
  },
  emits: ["confirm"],
  data() {
    return {
      form: {
        modelIdent: "netcare_device", //模型标识
        regionId: "", //所在区域ID
        locationId: "", //所在场所ID
        typeIds: null, //设备类型ID列表
        groupIds: null, //设备组ID列表
        vendorIds: null, //服务商ID列表
        alertClassificationIds: null, //告警分类ID列表
        externalId: "", //外部ID
        name: "", //设备名称
        description: "", //设备描述
        timeZone: null, //时区
        importance: null, //资源重要性
        tags: [], //标签
        active: false, //是否激活
        config: {
          ipAddress: "", //ip地址
          dynamicIp: false, //是否默认IP
          ackRequired: false, //确认告警

          nmsTicketing: false, //自动事件
          connectAuthType: "DEFAULT", //远程登录认证
          modelNumbers: [], //型号
          serialNumbers: [], //序列号
          assetNumbers: [], //资产编号
        },
        monitorSources: [],
        unit: null,
      },
      monitorSourceList: [
        {
          label: "ZIBBX",
          value: "ZIBBX",
        },
        {
          label: "ARTHAS",
          value: "ARTHAS",
        },
        {
          label: "UNKNOWN",
          value: "UNKNOWN",
        },
        {
          label: "NETCARE_V6",
          value: "NETCARE_V6",
        },
        {
          label: "CATEGRAF",
          value: "CATEGRAF",
        },
      ],

      rules: {
        "name": [{ required: true, message: "请输入设备名称", trigger: "blur" }],
        "regionId": [{ required: true, message: "请选择区域", trigger: "change" }],
        "locationId": [{ required: true, message: "请选择场所", trigger: "change" }],
        "config.ipAddress": [{ required: true, message: "请输入IP地址", trigger: "blur" }],
      },
      checked: false,
      formLabelWidth: "120px",
      dialogFormVisible: false,
      type: "",
    };
  },
  watch: {
    "form.config.ackRequired": function (val) {
      if (!val) {
        this.form.config.nmsTicketing = false;
      }
    },
    "id"(val) {
      if (val) {
        this.getDetail();
      }
    },
  },
  mounted() {
    this.handleRefreshRegionTable();

    this.getLocationList();
    this.getRegionList();
    this.getdeviceTypeList();
    this.getSupplierList();
    this.getAlarmList();
    this.getGroupDevice();
    // // console.log(this.allRegionSelect);
    // this.getAutoCloseEvent();
  },
  methods: {
    getAutoCloseEvent() {
      // console.log(123456789);
      getAlarmMerge({})
        .then((res) => {
          // // console.log(res);
          if (res.success) {
            // const form = reactive({
            // console.log(res, 55555555);
          }
        })
        .catch((err) => {
          ElMessage.error(err.message);
        });
    },
    getDetail() {
      getDeviceDetaile({ id: this.id }).then((res) => {
        if (res.success) {
          let config = { ...res.data.config };
          config.dynamicIp = config.dynamicIp ? JSON.parse(config.dynamicIp) : false;
          config.ackRequired = config.ackRequired ? JSON.parse(config.ackRequired) : false;
          config.nmsTicketing = config.nmsTicketing ? JSON.parse(config.nmsTicketing) : false;
          config.modelNumbers = JSON.parse(config.modelNumbers);
          config.serialNumbers = JSON.parse(config.serialNumbers);
          config.assetNumbers = JSON.parse(config.assetNumbers);
          this.form = { ...res.data };
          this.form.active = res.data.active;
          this.form.config = config;
          delete this.form.id;
        }
      });
    },

    blurChange(data) {
      // console.log(data);
      // this.tags.push(data);
      if (data.type == "tags") {
        this.form[data.type].push(data.value);
      } else {
        this.form.config[data.type].push(data.value);
      }
      // console.log(this.form);
    },
    tagClose(data) {
      if (data.type == "tags") {
        this.form[data.type].splice(data.index, 1);
      } else {
        this.form.config[data.type].splice(data.index, 1);
      }

      // this.tags.splice(index, 1);
    },
    submit() {
      this.$refs["ruleForm"].validate((valid) => {
        // dynamicIp: false, //是否默认IP
        // ackRequired: false, //确认告警
        // nmsTicketing: false, //自动事件
        // connectAuthType: "DEFAULT", //远程登录认证
        // modelNumbers: [], //型号
        // serialNumbers: [], //序列号
        // assetNumbers: [], //资产编号
        let regionId = "";

        if (this.form.regionId instanceof Object) {
          regionId = this.form.regionId[this.form.regionId.length - 1];
        } else {
          regionId = this.form.regionId;
        }
        let alertClassificationIds = this.form.alertClassificationIds.length > 0 ? this.form.alertClassificationIds : null;
        let groupIds = this.form.groupIds.length > 0 ? this.form.groupIds : null;
        let typeIds = this.form.typeIds.length > 0 ? this.form.typeIds : null;
        let vendorIds = this.form.vendorIds.length > 0 ? this.form.vendorIds : null;
        let tags = this.form.tags.length > 0 ? this.form.tags : null;
        let config = { ...this.form.config };
        config.dynamicIp = String(config.dynamicIp);
        config.ackRequired = String(config.ackRequired);
        config.nmsTicketing = String(config.nmsTicketing);
        config.modelNumbers = JSON.stringify(config.modelNumbers);
        config.serialNumbers = JSON.stringify(config.serialNumbers);
        config.assetNumbers = JSON.stringify(config.assetNumbers);

        if (valid) {
          if (this.type === "add") {
            addDevice({
              ...this.form,
              regionId,
              config,
              alertClassificationIds,
              groupIds,
              typeIds,
              vendorIds,
              tags,
            }).then((res) => {
              if (res.success) {
                ElMessage.success("操作成功");
                this.dialogFormVisible = false;
                this.$refs["ruleForm"].resetFields();
                this.$emit("confirm", { id: this.id });
              } else {
                this.dialogFormVisible = false;
                this.$refs["ruleForm"].resetFields();
                ElMessage.error(JSON.parse(res.data)?.message || "操作失败");
              }
            });
          } else {
            let alertClassificationIds = this.form.alertClassificationIds.length > 0 ? this.form.alertClassificationIds : [];
            let groupIds = this.form.groupIds.length > 0 ? this.form.groupIds : [];
            let typeIds = this.form.typeIds.length > 0 ? this.form.typeIds : [];
            let vendorIds = this.form.vendorIds.length > 0 ? this.form.vendorIds : [];
            let tags = this.form.tags.length > 0 ? this.form.tags : null;
            editDevice({
              ...this.form,
              regionId,
              config,
              id: this.id,
              alertClassificationIds,
              groupIds,
              tags,
              typeIds,
              vendorIds,
            }).then((res) => {
              if (res.success) {
                ElMessage.success("操作成功");
                this.dialogFormVisible = false;
                this.$refs["ruleForm"].resetFields();
                this.$emit("confirm", { id: this.id });
              } else {
                this.dialogFormVisible = false;
                this.$refs["ruleForm"].resetFields();
                ElMessage.error(JSON.parse(res.data)?.message || "操作失败");
              }
            });
          }
          // alert('submit!');
        }
      });
    },
    cancel() {
      this.$refs["ruleForm"].resetFields();
      this.dialogFormVisible = false;
      this.$emit("confirm", { id: this.id });
    },
  },
  expose: ["type", "dialogFormVisible"],
});
</script>

<style lang="scss" scoped>
.device-dialog {
  overflow: auto;
  > .el-scrollbar {
    overflow: auto;
  }
}
.select {
  .el-select-dropdown__item {
    // display: flex;
    min-height: 90px !important;
    padding-left: 5px;
    box-sizing: border-box;
    // background: red;
  }
}
.options-name {
  padding-right: 10px;
  float: left;
  margin-top: -6px;
}
.options-msg {
  float: left;
  // font-size: 13px;
  display: block;
  position: absolute;
  margin-top: 20px;
}

.association-select {
  height: 500px;
}
.summary {
  width: 270px;
}
:deep(.deviceForm) {
  .elstyle-dialog__body {
    padding: 20px 0;
    box-sizing: border-box;
  }
}
.deviceForm {
  :deep(.el-form-item) {
    width: 45% !important;
    .el-input {
      width: 202px;
    }
  }
}

.divider {
  width: 100%;
  height: 5px;
  background: #eee;
  margin-bottom: 22px;
  border-radius: 5px;
}
</style>
