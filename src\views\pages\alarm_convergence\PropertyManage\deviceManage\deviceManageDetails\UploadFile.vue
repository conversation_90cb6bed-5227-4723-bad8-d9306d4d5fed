<template>
  <el-dialog :title="`${t('devicesInfo.Upload Files')}`" v-model="visible" draggable :before-close="(done) => handleCloses().finally(() => done(true))">
    <template #header></template>
    <template #default>
      <el-form ref="formRef" :model="form" :disabled="loading" label-width="auto" label-position="top">
        <el-scrollbar ref="scrollbarRef" :max-height="height">
          <el-form-item prop="file" :error="error" :rules="[{ required: true, type: 'array', message: `${t('devicesInfo.Please add the uploaded file')}` }]">
            <el-upload ref="uploadRef" v-model:file-list="form.file" :auto-upload="true" :before-upload="beforeUpload" :action method="post" name="file" :http-request="requestBatchUploadFile" :on-success="(response, file) => Object.assign(file, { response })" :on-error="(response, file) => ElMessage.error((error = Object.assign(file, { response }).response.message))" drag list-type="text" :multiple="true" style="width: 100%">
              <template #default>
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <p>
                  <el-text>{{ `${t("devicesInfo.Drag the file here or")}` }}</el-text
                  ><el-link type="primary" :underline="false">{{ `${t("devicesInfo.Click here to upload")}` }}</el-link>
                </p>
              </template>
              <template #tip>
                <el-text type="info"></el-text>
              </template>
              <template #file="{ file }: { file: UploadFile }">
                <div class="el-upload-list__item-info" style="vertical-align: top">
                  <a class="el-upload-list__item-name">
                    <el-icon class="el-icon--document" :class="getImage(file.name)" :size="42">
                      <!-- <Document></Document> -->
                    </el-icon>
                    <el-text class="el-upload-list__item-file-name" :title="file.name" :truncated="true">{{ file.name }}</el-text>
                  </a>
                  <el-progress v-if="file.status === 'uploading'" type="line" :stroke-width="4" :percentage="Number(file.percentage)" :style="{ marginTop: '18px', width: 'calc(100% - 8px)' }" />
                </div>
                <label class="el-upload-list__item-status-label" style="vertical-align: top; height: 42px; line-height: 42px">
                  <el-icon :class="['el-icon--upload-success', 'el-icon--circle-check']">
                    <CircleCheck></CircleCheck>
                  </el-icon>
                </label>
                <el-icon v-if="!(loading || disabled)" class="el-icon--close" @click="uploadRef!.handleRemove(file)" style="vertical-align: top; height: 42px; line-height: 42px">
                  <Close></Close>
                </el-icon>
              </template>
            </el-upload>
          </el-form-item>
        </el-scrollbar>
      </el-form>
    </template>
    <template #footer>
      <el-button :loading @click="handleCloses">{{ `${t("glob.Cancel")}` }}</el-button>
      <el-button :type="'primary'" :loading :disabled="form.file.some((v) => v.status !== 'success')" @click="handleSubmit">{{ `${t("glob.Confirm")}` }}</el-button>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ref, nextTick, toValue, toRaw, computed } from "vue";
import { UploadFilled, CircleCheck, Close } from "@element-plus/icons-vue";
import { ElDialog, ElMessage, ElForm, ElFormItem, ElScrollbar, ElUpload, ElProgress, ElIcon, ElText, ElLink } from "element-plus";
import { SERVER } from "@/api/service/common";
import { requestBatchUploadFile } from "@/api/oss";
import { useI18n } from "vue-i18n";
const error = ref("");
const height = ref(500);
const visible = ref(false);
const disabled = ref(false);
const loading = ref(false);
const formRef = ref<InstanceType<typeof import("element-plus").ElForm>>();
const uploadRef = ref<InstanceType<typeof import("element-plus").ElUpload>>();
const scrollbarRef = ref<InstanceType<typeof import("element-plus").ElScrollbar>>();

const { t } = useI18n();
const action = computed(() => {
  const src = new URL(`${SERVER.CMDB}/files/upload`, location.origin);
  src.searchParams.set("ossDirectoryPrefix", toValue(form).bucket);
  return `${src.pathname}${src.search}${src.hash}`;
});
// const accept = [
//   /*  */
//   ...["png", "jpg", "jpeg", "gif"],
//   ...["mp4", "3gp", "avi", "flv"],
//   ...["doc", "docx"],
//   ...["xls", "xlsx"],
//   ...["txt"],
// ];
/* ====================================================================== */
const getImage = (name: string) => {
  const type = name.substring(name.lastIndexOf(".") + 1).toLowerCase();
  if (["png", "jpg", "jpeg", "gif", "PNG", "JPG", "JPEG", "GIF"].map((v) => v.toLowerCase()).indexOf(type) != -1) {
    return "imgiconPng";
  } else if (["mp4", "3gp", "avi", "flv", "MP4", "3GP", "AVI", "FLV"].map((v) => v.toLowerCase()).indexOf(type) != -1) {
    return "imgiconVideo";
  } else if (["doc", "docx", "DOC", "DOCX"].map((v) => v.toLowerCase()).indexOf(type) != -1) {
    return "imgiconDoc";
  } else if (["xls", "xlsx", "XLS", "XLSX"].map((v) => v.toLowerCase()).indexOf(type) != -1) {
    return "imgiconXls";
  } else if (["txt", "TXT"].map((v) => v.toLowerCase()).indexOf(type) != -1) {
    return "imgiconTxt";
  } else {
    return "imgiconOther";
  }
};
const beforeUpload: import("element-plus").UploadProps["beforeUpload"] = (file) => {
  error.value = "";
  const type = file.name.substring(file.name.lastIndexOf(".") + 1);
  if (["html", "htm", "xhtml", "shtml", "js", "jsp", "jspa", "jspx", "jspf", "jsw", "jsv", "jhtml", "jtml", "php", "psp", "pht", "phtml", "php1", "php2", "php3", "php4", "php5", "php7", "asp", "aspx", "asa", "asax", "ascx", "ashx", "asmx", "cer", "der", "crt", "sh", "exe", "swf", "htaccess"].indexOf(type.toLowerCase()) !== -1) {
    ElMessage.error((error.value = "文件上传失败，格式错误!"));
    return false;
  }
  // if (file.size / 1024 / 1024 > 2) {
  //   ElMessage.error("文件大小不能超过 2MB!");
  //   return false;
  // }
  return true;
};

type UploadStatus = "ready" | "uploading" | "success" | "fail";
interface UploadRawFile extends File {
  uid: number;
}
interface UploadFile {
  name: string;
  percentage?: number;
  status: UploadStatus;
  size?: number;
  response?: unknown;
  uid: number;
  url?: string;
  raw?: UploadRawFile;
}
interface Form {
  bucket: string;
  file: (Omit<UploadFile, "status" | "uid"> & Partial<Pick<UploadFile, "status" | "uid">>)[];
}
const form = ref<Form>({
  bucket: "",
  file: [],
});
async function openerHook($form: Form) {
  form.value = await converForm($form);
}
async function converForm($form: Form) {
  return structuredClone(toRaw(toValue($form)));
}
/* ======================================================================================================================== */
async function validateForm() {
  const $formRef = toValue(formRef);
  if (!$formRef) return false;
  return await new Promise((resolve) => $formRef.validate((isValid) => resolve(isValid)));
}
/* ======================================================================================================================== */
const event = new EventTarget();
enum EventType {
  submit = "submit",
  closes = "closes",
}

async function handleSubmit() {
  if (toValue(loading)) return;
  try {
    loading.value = true;
    await nextTick();
    if (await validateForm()) event.dispatchEvent(new Event(EventType.submit));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
}

async function handleCloses() {
  if (toValue(loading)) return;
  try {
    loading.value = true;
    await nextTick();
    event.dispatchEvent(new Event(EventType.closes));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
}

defineExpose({
  async opener($form: Form, $callback: ($form: Form) => Promise<void>): Promise<Form> {
    try {
      error.value = "";
      loading.value = true;
      visible.value = true;
      await nextTick();
      await openerHook($form);
      loading.value = false;
      return new Promise<Form>((resolve, reject) => {
        const controller = new AbortController();
        const closes = async () => {
          if (!toValue(loading)) return;
          try {
            await nextTick();
            const result = structuredClone(toRaw(toValue(form)));
            controller.abort();
            reject(result);
          } catch (error) {
            if (error instanceof Error) ElMessage.error(error.message);
          } finally {
            loading.value = false;
          }
        };
        event.addEventListener(EventType.closes, closes, { signal: controller.signal });
        const submit = async () => {
          if (!toValue(loading)) return;
          try {
            await nextTick();
            const result = structuredClone(toRaw(toValue(form)));
            await $callback(result);
            controller.abort();
            resolve(result);
          } catch (error) {
            if (error instanceof Error) ElMessage.error(error.message);
          } finally {
            loading.value = false;
          }
        };
        event.addEventListener(EventType.submit, submit, { signal: controller.signal });
      }).finally(() => void (visible.value = false));
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      loading.value = false;
      return Promise.reject<Form>(structuredClone(toRaw(toValue(form)))).finally(() => void (visible.value = false));
    }
  },
});
</script>
<style scoped lang="scss">
.imgiconPng {
  background: url("../../../../assets/device/picture.png") no-repeat;
  background-size: 100% 100%;
}

.imgiconVideo {
  background: url("../../../../assets/device/picture.png") no-repeat;
  background-size: 100% 100%;
}

.imgiconDoc {
  background: url("../../../../assets/device/docx.png") no-repeat;
  background-size: 100% 100%;
}

.imgiconXls {
  background: url("../../../../assets/device/xlsx.png") no-repeat;
  background-size: 100% 100%;
}

.imgiconTxt {
  background: url("../../../../assets/device/txt.png") no-repeat;
  background-size: 100% 100%;
}

.imgiconOther {
  background: url("../../../../assets/device/picture.png") no-repeat;
  background-size: 100% 100%;
}
</style>
