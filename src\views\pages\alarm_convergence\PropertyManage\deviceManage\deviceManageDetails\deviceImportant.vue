<template>
  <el-dialog v-model="dialogVisible" :title="`${t('devicesInfo.Work order priority matrix')}`" width="65%" :before-close="handleClose">
    <el-card :body-style="{ padding: '20px', height: `550px`, width: `100%` }">
      <el-row :gutter="24" class="event_priority_center">
        <el-scrollbar :height="550">
          <el-col :span="24">
            <h2>{{ `${t("devicesInfo.Priority matrix")}` }}</h2>

            <el-row :gutter="24">
              <el-col :span="6" style="padding-right: 0">
                <div style="height: 174px; background: #f2f4f5"></div>
                <el-table stripe :data="deviceOrderMappingItems" border class="deviceImportant" :row-class-name="tableRowClassName">
                  <el-table-column prop="HIGH" :label="`${t('devicesInfo.Equipment importance')}`" align="center">
                    <template #default="scope">
                      <div style="height: 32px">
                        {{ scope.row.importance }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="MIDDLE" label=" " align="center">
                    <template #default="scope">
                      <div>
                        <el-icon><Right /></el-icon>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="LOW" :label="`${t('devicesInfo.Work order influence')}`" align="center">
                    <template #default="scope">
                      <div>
                        {{ scope.row.influence }}
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
              <el-col :span="18" style="padding-left: 0">
                <div class="alarm-urgent-list">
                  <div class="alarm-urgent-title">{{ `${t("devicesInfo.Alarm urgency")}` }}</div>
                  <el-table stripe :data="alarmStatus" border :show-header="false" class="matrix">
                    <el-table-column prop="Critical" label="Critical" align="center">
                      <template #default="{ row }">
                        <span :class="row.Critical">
                          {{ row.Critical }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="Major" label="Major " align="center">
                      <template #default="{ row }">
                        <span :class="row.Major">
                          {{ row.Major }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="Minor" label="Minor" align="center">
                      <template #default="{ row }">
                        <span :class="row.Minor">
                          {{ row.Minor }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="Warning" label="Warning" align="center">
                      <template #default="{ row }">
                        <span :class="row.Warning">
                          {{ row.Warning }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="Normal" label="" align="left"> </el-table-column>
                  </el-table>
                  <el-table stripe :data="alarmStatus" border :show-header="false" class="corresponding matrix">
                    <el-table-column prop="Critical" label="Critical" align="center">
                      <template #default="{ row }">
                        <span>
                          <el-icon><Bottom /></el-icon>
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="Major" label="Major " align="center">
                      <template #default="{ row }">
                        <span>
                          <el-icon><Bottom /></el-icon>
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="Minor" label="Minor" align="center">
                      <template #default="{ row }">
                        <span>
                          <el-icon><Bottom /></el-icon>
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="Warning" label="Warning" align="center">
                      <template #default="{ row }">
                        <span>
                          <el-icon><Bottom /></el-icon>
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="Normal" label="" align="left"> </el-table-column>
                  </el-table>
                  <div class="alarm-urgent-title" style="background: #f2f4f5">
                    {{ `${t("devicesInfo.Work order urgency")}` }}
                  </div>
                </div>
                <el-table stripe :data="tableData" border class="matrix deviceImportant" :row-class-name="tableRowClassNames">
                  <el-table-column prop="HIGH" :label="alertOrderMappingItems[0].urgency" align="center">
                    <template #default="scope">
                      <div>
                        {{ scope.row[alertOrderMappingItems[0].urgency] }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="Major" :label="alertOrderMappingItems[1].urgency" align="center">
                    <template #default="scope">
                      <div>
                        {{ scope.row[alertOrderMappingItems[1].urgency] }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="LOW" :label="alertOrderMappingItems[2].urgency" align="center">
                    <template #default="scope">
                      <div>
                        {{ scope.row[alertOrderMappingItems[2].urgency] }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="WARNING" :label="alertOrderMappingItems[3].urgency" align="center">
                    <template #default="scope">
                      <div>
                        {{ scope.row[alertOrderMappingItems[3].urgency] }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="Others" label="Unknown" align="center">
                    <template #default="scope">
                      <div>
                        {{ scope.row[alertOrderMappingItems[4].urgency] }}
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
          </el-col>
        </el-scrollbar>
      </el-row>
    </el-card>
  </el-dialog>
</template>

<script setup lang="ts" generic="T extends object">
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete, ArrowDown as ElIconArrowDown, Bottom, Right } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox } from "element-plus";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";
import { getMointerSourceMappingList, editMointerSourceMapping, getPriorityMatrixList, editPriorityMatrix, type ServiceCenterList as ItemData } from "@/views/pages/apis/eventPriority";
import getUserInfo from "@/utils/getUserInfo";
import type { TabsPaneContext } from "element-plus";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", {
    type: "error",
  });
  throw new Error("Component context initialization failed!");
}
const { t } = useI18n();
const route = useRoute();
const router = useRouter();
defineOptions({ name: "EventPriorityCenter" });
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const userInfo = getUserInfo();

// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
// const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);

const activeName = ref("first");
const dialogVisible = ref(false);
const handleClick = (tab: TabsPaneContext, event: Event) => {
  // console.log(tab, event);
};
const tableTitle = ref(["High", "Medium", "Low", "Normal", "Unknown"]);
const tableData = ref<[Record<string, string>, Record<string, string>, Record<string, string>, Record<string, string>, Record<string, string>]>([
  { High: "", Medium: "", Low: "", None: "", Unknown: "" },
  { High: "", Medium: "", Low: "", None: "", Unknown: "" },
  { High: "", Medium: "", Low: "", None: "", Unknown: "" },
  { High: "", Medium: "", Low: "", None: "", Unknown: "" },
  { High: "", Medium: "", Low: "", None: "", Unknown: "" },
]);

const alarmStatus = ref([
  {
    Critical: "Critical",
    Major: "Major",
    Minor: "Minor",
    Warning: "Warning",
    Calculating: "Calculating",
    Normal: "",
  },
]);
const deviceStatus = ref("");
const alertOrderMappingItems = ref([]);
const deviceOrderMappingItems = ref([]);
const levelOptions = ref([
  { label: "P1", value: "P1" },
  { label: "P2", value: "P2" },
  { label: "P3", value: "P3" },
  { label: "P4", value: "P4" },
  { label: "P5", value: "P5" },
  { label: "P6", value: "P6" },
  { label: "P7", value: "P7" },
]);
const statusOptions = ref([
  { label: "Critical", value: "Critical" },
  { label: "Major", value: "Major" },
  { label: "Minor", value: "Minor" },
  { label: "Normal", value: "Normal" },
  { label: "Unknown", value: "Unknown" },
  { label: "Warning", value: "Warning" },

  { label: "Informational", value: "Informational" },
  { label: "Calculating", value: "Calculating" },
  { label: "Symptom", value: "Symptom" },
  { label: "Monitoring", value: "Monitoring" },
]);
const valueList = ref<Record<string, string>>({
  Critical: "Critical",
  Major: "Major",
  Minor: "Minor",
  Normal: "Normal",
  Unknown: "Unknown",
  Warning: "Warning",
  Informational: "Informational",
  Calculating: "Calculating",
  Symptom: "Symptom",
  Monitoring: "Monitoring",
});
const deviceTypeList = ref<Record<string, string>>({
  High: "High",
  Medium: "Medium",
  Low: "Low",
  Normal: "None",
  Unknown: "Unknown",
});
const mappingList = ref<ItemData[]>([]);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  // getList();
  getPriorityList();
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
const tableIndex = ref(0);
const tableRowClassName = ({ row, rowIndex }: { row: string; rowIndex: number }) => {
  if (row.importance === deviceStatus.value) {
    tableIndex.value = rowIndex;
    return "warning-row";
  }
  return "";
};
const tableRowClassNames = ({ row, rowIndex }: { row: string; rowIndex: number }) => {
  if (tableIndex.value == rowIndex) {
    return "warning-row";
  }
  return "";
};
function priorityChange(row: { [x: string]: any }, thing: string | number, device: any) {
  // let deviceType = "";
  // for (let key in deviceTypeList.value) {
  //   if (device === deviceTypeList.value[key]) {
  //     deviceType = key;
  //   }
  // }
  // console.log(row);
  let obj = {
    influence: thing,
    urgency: thing,
    priority: row[thing],
  };

  editPriorityMatrix({ priorityMatrixItems: [obj] })
    .then((res) => {
      if (!res.success) throw new Error(res.message);
      ElMessage.success("操作成功");
      getPriorityList();
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
  // // console.log(obj);
}

function getPriorityList /* 获取事件优先级矩阵列表 */() {
  getPriorityMatrixList({})
    .then((res) => {
      if (!res.success) throw new Error(res.message);

      (res.data as unknown as { priorityMatrixItems: Record<string, string>[] }).priorityMatrixItems.forEach((v) => {
        if (v.influence === "High") {
          tableData.value[0][v.urgency as string] = v.priority;
        } else if (v.influence === "Medium") {
          tableData.value[1][v.urgency as string] = v.priority;
        } else if (v.influence === "Low") {
          tableData.value[2][v.urgency as string] = v.priority;
        } else if (v.influence === "None") {
          tableData.value[3][v.urgency as string] = v.priority;
        } else {
          tableData.value[4][v.urgency as string] = v.priority;
        }
      });

      alertOrderMappingItems.value = res.data.alertOrderMappingItems;
      deviceOrderMappingItems.value = res.data.deviceOrderMappingItems;
      // console.log(alertOrderMappingItems.value);
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
}

function getList /* 获取映射关系列表 */() {
  getMointerSourceMappingList({})
    .then((res) => {
      if (!res.success) throw new Error(res.message);
      mappingList.value = [...res.data];
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
}
function updateMointerSourceMapping(id: any, mappings: any) {
  let obj = {
    sourceId: id,
    mappings: [mappings],
  };
  // console.log(obj);
  editMointerSourceMapping(obj)
    .then((res) => {
      if (!res.success) throw new Error(res.message);

      ElMessage.success("状态修改成功");
      getList();
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
}
function handleCommand(val: ReturnType<typeof beforeHandleCommand>) {
  if (val.i != "alarm" && val.i != "device") {
    mappingList.value[val.i].mappings[val.index].eventSeverity = val.command.value;

    updateMointerSourceMapping(mappingList.value[val.i].sourceId, mappingList.value[val.i].mappings[val.index]);
  } else {
    if (val.i === "alarm") {
      editPriorityMatrix({
        alertOrderMappingItems: [
          {
            severity: alertOrderMappingItems.value[val.index].severity,
            urgency: val.command,
          },
        ],
      })
        .then((res) => {
          if (!res.success) throw new Error(res.message);
          ElMessage.success("操作成功");
          getPriorityList();
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        });
    } else {
      editPriorityMatrix({
        deviceOrderMappingItems: [
          {
            importance: deviceOrderMappingItems.value[val.index].importance,
            influence: val.command,
          },
        ],
      })
        .then((res) => {
          if (!res.success) throw new Error(res.message);
          ElMessage.success("操作成功");
          getPriorityList();
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        });
    }
  }
}
function beforeHandleCommand(command: { label: string; value: string }, index: number, i: number, type: string) {
  return {
    command: command,
    index: index,
    i: i,
    type: type,
  };
}
defineExpose({
  dialogVisible: dialogVisible,
  mounted: mounted,
  getList: getList,
  deviceStatus: deviceStatus,
});

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
// defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
:deep(.el-table .warning-row) {
  --el-table-tr-bg-color: yellow;
}
:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: transparent;
}
// .el-table .warning-row {
//   --el-table-tr-bg-color: var(--el-color-warning-light-9);
// }
.event_priority_center {
  > div {
    flex: 1;
    flex-direction: column;
    h3 {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 4px 12px;
      box-sizing: border-box;

      height: 40px;

      /* primary/cloudcare/fill/fill5 */

      background: #f7f8fa;
      border-radius: 2px;
      margin-bottom: 10px;
      /* Inside auto layout */

      align-self: stretch;
    }
    h2 {
      color: #409eff;
    }
    :deep(.deviceImportant) {
      .cell {
        height: 32px;
        line-height: 32px;
      }
    }
    .alarm-urgent-list {
      height: auto;
      border-left: 1px solid #ebeef5;
      .alarm-urgent-title {
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .alarm-urgent-title:first-child {
        border-top: 1px solid #ebeef5;
        border-right: 1px solid #ebeef5;
      }
      :deep(.el-table) {
        .cell {
          height: 35px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .corresponding {
        border-top: 0;
        span {
          color: #000;
        }
        :deep(.el-table__inner-wrapper::before) {
          background-color: none;
        }
      }
      span {
        color: #fff;
        padding: 2px 15px;
        box-sizing: border-box;
        border-radius: 20px;
      }
      span.Critical {
        background: #db3328;
      }
      span.Major {
        background: #f0ad4e;
      }
      span.Minor {
        background: #e9d310;
      }
      span.Warning {
        background: #31b0d5;
      }
      span.Normal {
        background: #5cb85c;
      }
      span.Calculating {
        background: #bd9fd9;
      }
    }
  }
  :deep(.el-table) {
    .el-table__border-left-patch {
      display: none;
    }
  }
  .matrix {
    ::deep(.el-table--border::before) {
      width: 0;
    }
  }
  .matrix::before {
    width: 0 !important;
  }
}
ol {
  display: flex;
  flex-wrap: wrap;
  background: #f7f8fa;
  > li {
    flex: 1;
    // width: 25%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    > span {
      padding: 2px 15px;
      box-sizing: border-box;
      border-radius: 20px;
      color: #fff;
    }
    border: 1px solid #e5e6eb;
  }
}
.alarm-status {
  display: flex;
  flex-wrap: wrap;
  > li {
    flex: 1;
    // width: 25%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    > span {
      padding: 2px 15px;
      box-sizing: border-box;
      border-radius: 20px;
      color: #fff;
    }
    border: 1px solid #e5e6eb;
  }
  > li {
    span {
      background: #d3d3d3;
    }
  }
  // > li:nth-child(-n + 5) {
  //   // border: 1px solid #E5E6EB;
  //   border-left: 1px solid #e5e6eb;
  //   border-top: 1px solid #e5e6eb;
  // }
  // > li:nth-child(n + 6) {
  //   border-left: 1px solid #e5e6eb;
  //   border-top: 1px solid #e5e6eb;
  //   border-bottom: 1px solid #e5e6eb;
  //   background: #f7f8fa;
  // }
  // > li:nth-child(5) {
  //   border-right: 1px solid #e5e6eb;
  // }
  // > li:nth-child(10) {
  //   border-right: 1px solid #e5e6eb;
  // }

  li.Critical {
    span {
      background: #db3328;
    }
  }
  li.Major {
    span {
      background: #f0ad4e;
    }
  }
  li.Minor {
    span {
      background: #e9d310;
    }
  }
  li.Warning {
    span {
      background: #31b0d5;
    }
  }
  li.Normal {
    span {
      background: #5cb85c;
    }
  }
  li.Calculating {
    span {
      background: #bd9fd9;
    }
  }
  li {
    span {
      background: #d3d3d3;
    }
  }
}
</style>
