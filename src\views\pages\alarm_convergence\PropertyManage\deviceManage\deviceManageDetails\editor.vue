<!--  -->
<template>
  <el-dialog class="device-editor" :title="type === 'add' ? `${i18n.t('devicesInfo.New Journal')}` : `${i18n.t('devicesInfo.Edit Journal')}`" v-model="dialogVisible" width="60%">
    <QuillEditor theme="snow" style="flex: 1; height: 400px" v-model:content="value" contentType="html" :toolbar="customToolbar" ref="QuillEditorRef"></QuillEditor>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">{{ `${i18n.t("glob.Cancel")}` }}</el-button>
        <el-button type="primary" @click="submit">{{ `${i18n.t("glob.Confirm")}` }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { QuillEditor, Quill } from "@vueup/vue-quill";

import { useI18n } from "vue-i18n";
import "quill/dist/quill.core.css"; // import styles
import "quill/dist/quill.snow.css"; // for snow theme
import "quill/dist/quill.bubble.css"; // for bubble theme
// 必须要引入
// import * as Quill from "quill";
// 新建的css文件的保存位置，如不同，需要修改
import "./editor.css";
// 自定义的字体，注意下面的内容要和css里面对应上
var fonts = ["SimSun", "SimHei", "Microsoft-YaHei", "KaiTi", "FangSong", "Arial", "Times-New-Roman", "sans-serif"];
// 自定义字号的大小，注意下面的内容要和css里面对应上
var sizes = [false, "16px", "18px", "20px", "22px", "26px", "28px", "30px"];
var Size = Quill.import("formats/size");
Size.whitelist = sizes;
var Font = Quill.import("formats/font");
Font.whitelist = fonts; //将字体加入到白名单
Quill.register(Font, true);
const titleConfig = [
  { Choice: ".ql-insertMetric", title: "跳转配置" },
  { Choice: ".ql-bold", title: "加粗" },
  { Choice: ".ql-italic", title: "斜体" },
  { Choice: ".ql-underline", title: "下划线" },
  { Choice: ".ql-header", title: "段落格式" },
  { Choice: ".ql-strike", title: "删除线" },
  { Choice: ".ql-blockquote", title: "块引用" },
  { Choice: ".ql-code", title: "插入代码" },
  { Choice: ".ql-code-block", title: "插入代码段" },
  { Choice: ".ql-font", title: "字体" },
  { Choice: ".ql-size", title: "字体大小" },
  { Choice: '.ql-list[value="ordered"]', title: "编号列表" },
  { Choice: '.ql-list[value="bullet"]', title: "项目列表" },
  { Choice: ".ql-direction", title: "文本方向" },
  { Choice: '.ql-header[value="1"]', title: "h1" },
  { Choice: '.ql-header[value="2"]', title: "h2" },
  { Choice: ".ql-align", title: "对齐方式" },
  { Choice: ".ql-color", title: "字体颜色" },
  { Choice: ".ql-background", title: "背景颜色" },
  { Choice: ".ql-image", title: "图像" },
  { Choice: ".ql-video", title: "视频" },
  { Choice: ".ql-link", title: "添加链接" },
  { Choice: ".ql-formula", title: "插入公式" },
  { Choice: ".ql-clean", title: "清除字体格式" },
  { Choice: '.ql-script[value="sub"]', title: "下标" },
  { Choice: '.ql-script[value="super"]', title: "上标" },
  { Choice: '.ql-indent[value="-1"]', title: "向左缩进" },
  { Choice: '.ql-indent[value="+1"]', title: "向右缩进" },
  { Choice: ".ql-header .ql-picker-label", title: "标题大小" },
  { Choice: '.ql-header .ql-picker-item[data-value="1"]', title: "标题一" },
  { Choice: '.ql-header .ql-picker-item[data-value="2"]', title: "标题二" },
  { Choice: '.ql-header .ql-picker-item[data-value="3"]', title: "标题三" },
  { Choice: '.ql-header .ql-picker-item[data-value="4"]', title: "标题四" },
  { Choice: '.ql-header .ql-picker-item[data-value="5"]', title: "标题五" },
  { Choice: '.ql-header .ql-picker-item[data-value="6"]', title: "标题六" },
  { Choice: ".ql-header .ql-picker-item:last-child", title: "标准" },
  { Choice: '.ql-size .ql-picker-item[data-value="small"]', title: "小号" },
  { Choice: '.ql-size .ql-picker-item[data-value="large"]', title: "大号" },
  { Choice: '.ql-size .ql-picker-item[data-value="huge"]', title: "超大号" },
  { Choice: ".ql-size .ql-picker-item:nth-child(2)", title: "标准" },
  { Choice: ".ql-align .ql-picker-item:first-child", title: "居左对齐" },
  { Choice: '.ql-align .ql-picker-item[data-value="center"]', title: "居中对齐" },
  { Choice: '.ql-align .ql-picker-item[data-value="right"]', title: "居右对齐" },
  { Choice: '.ql-align .ql-picker-item[data-value="justify"]', title: "两端对齐" },
];

export default {
  components: {
    QuillEditor,
  },
  // emits: ["dialogVisible"],
  data() {
    return {
      i18n: useI18n(),
      value: "",
      dialogVisible: false,
      customToolbar: [
        ["bold", "italic", "underline", "strike"], // toggled buttons
        ["blockquote", "code-block"],

        [{ header: 1 }, { header: 2 }], // custom button values
        [{ list: "ordered" }, { list: "bullet" }],
        [{ script: "sub" }, { script: "super" }], // superscript/subscript
        [{ indent: "-1" }, { indent: "+1" }], // outdent/indent
        [{ direction: "rtl" }], // text direction

        [{ size: sizes }], // custom dropdown
        [{ header: [1, 2, 3, 4, 5, 6, false] }],

        [{ color: [] }, { background: [] }], // dropdown with defaults from theme
        [{ font: fonts }],
        [{ align: [] }],

        ["clean"], // remove formatting button
      ],
      type: "",
    };
  },

  methods: {
    //、
    open(type, obj) {
      this.value = "   ";
      setTimeout(() => {
        for (let item of titleConfig) {
          let tip = document.querySelector(".ql-toolbar " + item.Choice);
          if (!tip) continue;
          tip.setAttribute("title", item.title);
          if (item.Choice == ".ql-image" || item.Choice == ".ql-video" || item.Choice == ".ql-link") {
            tip.style.display = "none"; // 或者使用 visibility: "hidden"
          }
        }
      }, 1000);

      this.dialogVisible = true;
      if (type != "add") {
        this.value = obj.logNote;
        this.logId = obj.id;
      } else this.value = "  ";

      this.type = type;
    },
    cancel() {
      this.dialogVisible = false;
      this.value = "";
    },
    submit() {
      // // console.log(this.$emit);
      this.$emit("submit", { type: this.type, value: this.value });
    },
  },
};
</script>
<style scoped lang="scss"></style>
