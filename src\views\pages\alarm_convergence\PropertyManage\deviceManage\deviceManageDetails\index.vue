<template>
  <el-card :style="{ height: 'calc(100% - 28px)' }" class="device-detail">
    <el-page-header @back="backRouter">
      <template #content>
        <el-row class="title" :gutter="24">
          <el-col :span="12">
            <p>
              {{ form.name }}
            </p>
            <p>
              {{ form.config.ipAddress }}
            </p>
          </el-col>
          <el-col :span="12">
            <div style="cursor: pointer; display: flex">
              <template v-if="userInfo.hasPermission(资产管理中心_设备_工具权限)">
                <span class="tw-h-fit">
                  <el-button type="primary" link>
                    <deviceTools :item="form" :list="deskTopObj.length" :show="form.showDesktop" :active="form.active && (form.allowTypes || []).length"></deviceTools>
                  </el-button>
                </span>
                <span class="tw-h-fit">
                  <el-button type="primary" link @click="ping(form)">
                    <Icon class="tw-mx-[2px]" name="local-DeviceWifi-line" :color="form.active ? 'var(--el-color-primary)' : '#888'"></Icon>
                  </el-button>
                </span>
                <span class="tw-h-fit">
                  <el-button type="primary" link @click="routeDevice(form)">
                    <Icon class="tw-mx-[2px]" name="local-SystemShare-line" :color="form.active ? 'var(--el-color-primary)' : '#888'"></Icon>
                  </el-button>
                </span>

                <span class="tw-h-fit">
                  <el-button type="primary" link @click="routerV6Busines(form.name)">
                    <Icon class="tw-mx-[2px]" name="local-DocumentNumbers-line" color="var(--el-color-primary)"></Icon>
                  </el-button>
                </span>

                <span class="tw-h-fit">
                  <el-button type="primary" link @click="handleViewContancts($route.params.id)">
                    <Icon class="tw-mx-[2px]" name="local-UserUser-3-line" color="var(--el-color-primary)"></Icon>
                  </el-button>
                </span>
              </template>
            </div>
          </el-col>
        </el-row>
      </template>
    </el-page-header>
    <div class="details" :style="{ height: 'calc(100% - 60px)' }">
      <div class="device-detail-tab-list">
        <el-tabs v-model="activeName">
          <el-tab-pane :label="`${i18n.t('devicesInfo.Details')}`" :name="`${i18n.t('devicesInfo.Details')}`" v-if="userInfo.hasPermission(资产管理中心_设备_可读)">
            <div class="message" v-show="userInfo.hasPermission(资产管理中心_设备_可读)" v-loading="detailLoading">
              <div>
                {{ `${i18n.t("devicesInfo.Basic information")}` }}
                <ul>
                  <li>
                    <span>{{ `${i18n.t("devicesInfo.Name")}` }}</span>
                    <span>{{ form.name }}</span>
                  </li>
                  <li v-if="ifShow(form.description)">
                    <span>{{ `${i18n.t("devicesInfo.Description")}` }}</span>
                    <span>{{ form.description ? form.description : "--" }}</span>
                  </li>
                  <li v-if="ifShow(form.config.ipAddress)">
                    <span>{{ `${i18n.t("devicesAdd.IP address")}` }}</span>
                    <span>{{ form.config.ipAddress ? form.config.ipAddress : "--" }}</span>
                  </li>
                  <li v-if="ifShow(form.importance)">
                    <span
                      >{{ `${i18n.t("devicesAdd.Importance")}` }} <el-icon @click="Important(form.importance)"><QuestionFilled /></el-icon
                    ></span>
                    <span>{{ form.importance }}</span>
                  </li>
                  <li v-if="ifShow(deviceSize.collectorName)">
                    <span>{{ `${i18n.t("devicesInfo.Poller")}` }}</span>
                    <span>{{ (deviceSize.collectorName == "null" ? "--" : deviceSize.collectorName) || "--" }}</span>
                  </li>
                  <li v-if="ifShow(form.monitorSources)">
                    <span>{{ `${i18n.t("devicesAdd.Monitoring source")}` }}</span>
                    <span>
                      <span v-for="(item, index) in form.monitorSources" :key="index"> {{ index != form.monitorSources.length - 1 ? item + " , " : item }}</span>
                      <span v-show="form.monitorSources.length < 1"> --</span>
                    </span>
                  </li>
                  <li v-if="ifShow(form.regionDesc)">
                    <!-- <span>{{ regionsOption[form.regionId + regionsOption[form.regionId]] ? regionsOption[form.regionId + regionsOption[form.regionId]] : "区域" }}</span> -->
                    <span>{{ "区域" }}</span>
                    <!-- <span>{{ form.regionNames }}</span> -->
                    <span>{{ form.regionDesc }}</span>
                  </li>
                  <li v-if="ifShow(form.locationId)" style="height: 90px">
                    <span>{{ `${i18n.t("devicesAdd.Location")}` }}</span>
                    <span style="-webkit-line-clamp: 10">
                      {{ form.locationId }}
                      <p v-for="(item, index) in locationAddress" :key="index" style="font-size: 12px; color: #444">
                        {{ item }}
                      </p>
                    </span>
                  </li>
                  <li v-if="ifShow(timeZoneConvert[locationOption[form.locationId + locationOption[form.locationId]]])">
                    <span> {{ `${i18n.t("devicesAdd.Time Zone")}` }}</span>
                    <span>{{ timeZoneConvert[locationOption[form.locationId + locationOption[form.locationId]]] ? timeZoneConvert[locationOption[form.locationId + locationOption[form.locationId]]] : "--" }}</span>
                  </li>
                  <li v-if="ifShow(form.vendors)">
                    <span>{{ `${i18n.t("devicesAdd.Device vendors")}` }}</span>
                    <span>
                      <el-tooltip class="box-item" effect="light" placement="top">
                        <template #content>
                          <p style="max-width: 800px">
                            {{ vendorInfo?.map((item) => item?.name).join() }}
                          </p>
                        </template>
                        <span>{{ vendorInfo?.map((item) => item?.name).join() }}</span>
                      </el-tooltip>
                      <span v-show="form.vendors?.length < 1"> --</span>
                    </span>
                  </li>
                  <li v-if="ifShow(form.typeIds)">
                    <span>{{ `${i18n.t("devicesAdd.Device types")}` }}</span>
                    <span>
                      <el-tooltip class="box-item" effect="light" placement="top">
                        <template #content>
                          <p style="max-width: 800px">
                            {{ deviceTypeInfo?.map((item) => item?.name).join() }}
                          </p>
                        </template>
                        <span> {{ deviceTypeInfo?.map((item) => item?.name).join() }}</span>
                      </el-tooltip>
                      <span v-show="form.typeIds.length < 1"> --</span>
                    </span>
                  </li>
                  <li v-if="ifShow(form.groups)">
                    <span>{{ `${i18n.t("devicesAdd.Device groups")}` }}</span>
                    <span v-if="userInfo.hasPermission(资产管理中心_设备分组_可读)">
                      <span v-for="(item, index) in deviceGroupInfo" :key="index"> {{ index != deviceGroupInfo.length - 1 ? item.name + " , " : item.name }}</span>
                      <span v-show="form.groups.length < 1"> --</span>
                    </span>
                  </li>
                  <li v-if="ifShow(form.config.modelNumbers)">
                    <span>{{ `${i18n.t("devicesAdd.Model numbers")}` }}</span>
                    <span>
                      <el-tooltip class="box-item" effect="light" placement="top">
                        <template #content>
                          <p style="max-width: 800px">
                            {{ form.config.modelNumbers.join() }}
                          </p>
                        </template>
                        <span>{{ form.config.modelNumbers.join() }}</span>
                      </el-tooltip>
                      <span v-show="form.config.modelNumbers.length < 1"> --</span>
                    </span>
                  </li>
                  <li v-if="ifShow(form.config.serialNumbers)">
                    <span>{{ `${i18n.t("devicesAdd.Serial numbers")}` }}</span>
                    <span>
                      <el-tooltip class="box-item" effect="light" placement="top">
                        <template #content>
                          <p style="max-width: 800px">
                            {{ form.config.serialNumbers.join() }}
                          </p>
                        </template>
                        <span>{{ form.config.serialNumbers.join() }}</span>
                      </el-tooltip>
                      <span v-show="form.config.serialNumbers.length < 1"> --</span>
                    </span>
                  </li>
                  <li v-if="ifShow(form.assetNumber)">
                    <span>{{ `${i18n.t("devicesAdd.model numbers discovered")}` }}</span>
                    <span>{{ form.assetNumber }}</span>
                  </li>
                  <li v-if="ifShow(deviceSize.deviceModel)">
                    <span>{{ `${i18n.t("devicesAdd.Asset numbers")}` }}</span>
                    <el-tooltip class="box-item" effect="light" placement="top">
                      <template #content>
                        <p style="max-width: 800px">
                          {{ deviceSize.deviceModel != "null" ? deviceSize.deviceModel : "" }}
                        </p>
                      </template>
                      <span>{{ deviceSize.deviceModel != "null" ? deviceSize.deviceModel : "" }}</span>
                    </el-tooltip>

                    <span v-show="deviceSize.deviceModel == 'null'">--</span>
                  </li>
                  <li v-if="ifShow(deviceSize.serialNumber)">
                    <span>{{ `${i18n.t("devicesAdd.serial numbers discovered")}` }}</span>
                    <el-tooltip class="box-item" effect="light" placement="top">
                      <template #content>
                        <p style="max-width: 800px">
                          {{ deviceSize.serialNumber != "null" ? deviceSize.serialNumber : "" }}
                        </p>
                      </template>
                      <span>{{ deviceSize.serialNumber != "null" ? deviceSize.serialNumber : "" }}</span>
                    </el-tooltip>

                    <span v-show="deviceSize.serialNumber == 'null'">--</span>
                  </li>
                  <li v-if="ifShow(form.alertClassifications)">
                    <span>{{ `${i18n.t("devicesAdd.Alert classifications")}` }}</span>
                    <span>
                      <span style="display: inline-block; border: 1px solid #dfdfdf; border-radius: 5px; padding: 1px 3px; margin-left: 3px" v-for="(item, index) in alarmInfo" :key="index"> {{ index != alarmInfo - 1 ? item.name : item.name }} </span>
                      <span v-show="form.alertClassifications.length < 1"> --</span>
                    </span>
                  </li>
                  <li v-if="ifShow(form.tags)">
                    <span>{{ `${i18n.t("devicesAdd.Tag")}` }}</span>
                    <span>
                      <span v-for="(item, index) in form.tags" :key="item"> {{ index != form.tags.length - 1 ? item + " , " : item }}</span>
                      <span v-show="form.tags.length < 1"> --</span>
                    </span>
                  </li>
                </ul>
              </div>
              <div class="monitor">
                <div>
                  {{ `${i18n.t("devicesInfo.Monitoring")}` }}
                  <el-table ref="singleTableRef" :data="deviceAlarmList" style="width: 100%" border>
                    <el-table-column type="index" width="50" />
                    <el-table-column property="name" :label="`${i18n.t('devicesInfo.Name')}`" />
                    <el-table-column property="duration" :label="`${i18n.t('devicesInfo.Duration')}`" />
                    <el-table-column property="severity" :label="`${i18n.t('devicesInfo.Status')}`" show-overflow-tooltip>
                      <template #default="{ row }">
                        <span
                          class="priority"
                          :style="{
                            background: eventColor[row.severity] ? eventColor[row.severity].color : '#5cb85c',
                          }"
                          @click="routerV6Network($route.params.id, row.businessId)"
                        >
                          {{ row.severity }}
                          <!-- {{ eventColor[row.severity] }}. -->
                        </span>
                      </template>
                    </el-table-column>

                    <el-table-column property="happenTime" :label="`${i18n.t('devicesInfo.occurrence time')}`">
                      <template #default="{ row }">
                        <div>
                          {{ moment(row.happenTime, "x").format("yyyy-MM-DD HH:mm:ss") }}
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <div>
                  {{ `${i18n.t("devicesInfo.System Details")}` }}
                  <ul>
                    <li v-for="item in systemItemInfoList" :key="item.name">
                      <span>{{ item.showName }}</span>
                      <span>{{ item.value }}</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane :label="`${i18n.t('devicesInfo.Contacts')}`" :name="`${i18n.t('devicesInfo.Contacts')}`" v-if="userInfo.hasPermission(资产管理中心_联系人_可读)">
            <template v-if="id && userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(资产管理中心_联系人_可读)" #default="{}">
              <ModelExpand
                :key="id"
                :id="id"
                type="resourceDetail"
                :viewer="{
                  contact: !userInfo.hasPermission(资产管理中心_联系人_查看联系人),
                }"
                :remove="{
                  contact: !userInfo.hasPermission(资产管理中心_设备_分配联系人),
                }"
                :create="{
                  contact: !userInfo.hasPermission(资产管理中心_设备_分配联系人),
                }"
                :showAdd="false"
                :showRemove="false"
                :allocation="true"
              ></ModelExpand>
              <!-- <bind-contacts ref="contactsRef" :row="form" :showButoon="false" /> -->
            </template>
          </el-tab-pane>
          <el-tab-pane :label="`${i18n.t('devicesInfo.Ticket')}`" :name="`${i18n.t('devicesInfo.Ticket')}`" v-if="userInfo.hasPermission(智能事件中心_客户_工单可读) || (userInfo.hasPermission('756062918394511360' as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))">
            <div class="work">
              <div v-if="(userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(智能事件中心_客户_工单可读)) || (userInfo.hasPermission('756062918394511360' as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))">
                <page-template :search-span="24" :showPaging="true" v-model:current-page="workOrderPaging.pageNumber" v-model:page-size="workOrderPaging.pageSize" height="650" :total="workOrderPaging.total" @current-change="getWorkOrderList()" @size-change="getWorkOrderList()">
                  <template #default="{}">
                    <el-table :data="workOrderList" stripe style="width: 100%" border :height="tableHeight">
                      <TableColumn prop="orderType" :label="`${i18n.t('devicesInfo.Type')}`" type="orderType" show-filter v-model:many-filtered-value="search.orderType" @filter-change="handleQuery()">
                        <template #default="{ row }">
                          <p>
                            {{ OrderType[row.orderType] }}
                          </p>
                        </template>
                      </TableColumn>
                      <TableColumn prop="identifier" :label="`${i18n.t('devicesInfo.Ticket')}`" type="tenantName" show-filter v-model:many-filtered-value="search.orderId" @filter-change="handleQuery()">
                        <template #default="{ row }">
                          <!-- <p v-if="row.id"> -->
                          <span v-if="(userInfo.hasPermission(智能事件中心_客户_工单可读) || (userInfo.hasPermission('756062918394511360' as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))) && row.orderId" style="color: #409eff; cursor: pointer" @click="routerOrder(row, '工单')">{{ row.orderId ? row.orderId : "" }}</span>
                          <span v-else>--</span>
                          <!-- </p> -->
                        </template>
                      </TableColumn>
                      <TableColumn prop="priority" :label="`${i18n.t('devicesInfo.Priority')}`">
                        <template #default="{ row }">
                          <i
                            class="priority-icon"
                            :style="{
                              background: priority[row.priority].color,
                            }"
                            :formatter="eventTableFormatter"
                          />
                          <span
                            class="priorityTag"
                            :style="{
                              background: priority[row.priority].color,
                            }"
                            >{{ row.priority }}</span
                          >
                        </template>
                      </TableColumn>

                      <TableColumn prop="status" :label="`${i18n.t('devicesInfo.Status')}`" :formatter="eventTableFormatter"> </TableColumn>
                      <TableColumn prop="summary" :label="`${i18n.t('devicesInfo.Summary')}`" :width="400" type="tenantName" show-filter v-model:many-filtered-value="search.orderSummary" @filter-change="handleQuery()"> </TableColumn>

                      <TableColumn prop="createTime" :label="`${i18n.t('devicesInfo.Created')}`">
                        <template #default="{ row }">
                          <!-- <div> -->
                          {{ moment(row.orderCreateTime, "x").format("yyyy-MM-DD HH:mm") }}
                          <!-- </div> -->
                        </template>
                      </TableColumn>

                      <TableColumn prop="updateTime" :label="`${i18n.t('devicesInfo.Modified')}`">
                        <template #default="{ row }">
                          <div>
                            {{ moment(row.orderUpdateTime, "x").format("yyyy-MM-DD HH:mm") }}
                          </div>
                        </template>
                      </TableColumn>
                    </el-table>
                  </template>
                </page-template>
              </div>

              <!-- <p v-show="workOrderList.length < 1">暂无数据</p> -->
            </div>
          </el-tab-pane>
          <el-tab-pane :label="`${i18n.t('devicesInfo.Support Notes')}`" :name="`${i18n.t('devicesInfo.Support Notes')}`" v-if="userInfo.hasPermission(资产管理中心_行动策略_可读)">
            <h3 class="strategy-title">{{ `${i18n.t("devicesInfo.Customer Support Instructions")}` }}</h3>
            <div class="strategy-box" v-if="supportList.length">
              <div class="strategy-left">{{ `${i18n.t("devicesInfo.Working Time Action Strategy")}` }}</div>
              <div class="strategy-right">{{ `${i18n.t("devicesInfo.Non working time action strategy")}` }}</div>
            </div>
            <template v-if="userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(资产管理中心_行动策略_可读)">
              <div class="strategy" v-for="item in supportList" :key="item.id">
                <div :class="item.workTimeFlag ? 'week' : ''" v-if="item.active">
                  <h3 class="strategy-title">
                    <span>{{ filteringType(item.supportBindType) }}</span>
                  </h3>
                  <template v-if="true">
                    <h2>
                      {{ item.name }}
                      <span
                        ><el-icon><Sunny></Sunny></el-icon
                      ></span>
                    </h2>
                    <div class="pre-line" v-html="item.activeNote ?? ''"></div>
                    <h4 v-if="!item.allDayFlag">
                      <span v-if="item.workTimeFlag">{{ item.activeConfig.timeForZone ? "注意：使用直到 " + item.activeConfig.timeForZone : "" }}</span>
                      <span v-else>{{ item.activeConfig.timeForZone ? "注意：从 " + item.activeConfig.timeForZone + " 开始使用" : "" }}</span>
                    </h4>
                  </template>
                </div>

                <template v-if="!isEmptyOrNullOrEmptyTags(item.inactiveNote)">
                  <div :class="!item.workTimeFlag ? 'week' : ''">
                    <h3 class="strategy-title">
                      <span>{{ filteringType(item.supportBindType) }}</span>
                    </h3>
                    <!-- v-if="!isEmptyOrNullOrEmptyTags(item.inactiveNote)" -->
                    <!-- workTimeFlag 为true是工作时间  allDayFlag  为true就是7*24的 -->
                    <h2>
                      {{ item.name }}
                      <span
                        ><el-icon><Moon></Moon></el-icon
                      ></span>
                    </h2>
                    <div class="pre-line" v-html="item.inactiveNote ?? ''"></div>
                    <h4 v-if="!item.allDayFlag">
                      <span v-if="!item.workTimeFlag">{{ item.activeConfig.timeForZone ? "注意：使用直到 " + item.activeConfig.timeForZone : "" }}</span>
                      <span v-else>{{ item.activeConfig.timeForZone ? `${i18n.t("devicesInfo.Attention: From")} ` + item.activeConfig.timeForZone + ` ${i18n.t("devicesInfo.start using")}` : "" }}</span>
                    </h4>
                  </div>
                </template>
              </div>
            </template>
          </el-tab-pane>
          <el-tab-pane :label="`${i18n.t('devicesInfo.Service Numbers')}`" :name="`${i18n.t('devicesInfo.Service Numbers')}`" v-if="userInfo.hasPermission(资产管理中心_服务编号_可读)">
            <div class="work" style="text-align: right">
              <h3 style="text-align: right; position: relative; z-index: 99">
                <el-button v-if="userInfo.hasPermission(资产管理中心_服务编号_新增)" type="primary" :icon="Plus" @click="serviceSize('add')"> {{ `${i18n.t("devicesInfo.Add Service Number")}` }} </el-button>
              </h3>
            </div>
            <page-template style="margin-top: -35px" :search-span="24" :showPaging="true" v-model:current-page="paging.pageNumber" v-model:page-size="paging.pageSize" :total="paging.total" @current-change="getServiceNumberList()" @size-change="getServiceNumberList()">
              <template #right> </template>
              <template #default="{}">
                <el-table :data="serviceList" stripe style="width: 100%" border height="550" v-loading="serviceLoading">
                  <el-table-column prop="serviceNumber.number" :label="`${i18n.t('devicesInfo.Number')}`" width="180"> </el-table-column>
                  <el-table-column prop="serviceNumber.description" :label="`${i18n.t('devicesInfo.Description')}`" width="180"> </el-table-column>
                  <el-table-column prop="vendors" :label="`${i18n.t('devicesInfo.Line Vendor')}`" width="180">
                    <template #default="{ row }">
                      <div v-if="row.vendors?.length > 0" @click="openVendor(row.vendors)" style="color: #409eff; cursor: pointer">
                        <!-- {{ row.vendors }} -->
                        <!-- <span v-for="(item, index) in row.vendors" :key="item.id"> {{ index != row.vendors.length - 1 ? item.name + "," : item.name }} </span> -->

                        {{ row.vendors?.map((item) => item?.name).join() }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="serviceNumber.type" :label="`${i18n.t('devicesInfo.Type')}`"> </el-table-column>
                  <el-table-column prop="serviceNumber.portName" :label="`${i18n.t('devicesInfo.Port')}`"> </el-table-column>
                  <el-table-column prop="serviceNumber.serviceLevel" :label="`${i18n.t('devicesInfo.Service Lever')}`"> </el-table-column>
                  <el-table-column prop="serviceNumber.progress" :label="`${i18n.t('devicesInfo.Bandwidth')}`">
                    <template #default="{ row }">
                      <div>
                        <template v-if="!!Number((row.serviceNumber || {}).progress || 0)">
                          {{ row.serviceNumber.progressUnit >= 1 && row.serviceNumber.progressUnit < 1000 ? row.serviceNumber.progress : "" }}
                          {{ row.serviceNumber.progressUnit >= 1000 && row.serviceNumber.progressUnit < 1000000 ? row.serviceNumber.progress / row.serviceNumber.progressUnit : "" }}
                          {{ row.serviceNumber.progressUnit >= 1000000 && row.serviceNumber.progressUnit < 1000000000 ? row.serviceNumber.progress / row.serviceNumber.progressUnit : "" }}
                          {{ row.serviceNumber.progressUnit >= 1000000000 && row.serviceNumber.progressUnit < 1000000000000 ? row.serviceNumber.progress / row.serviceNumber.progressUnit : "" }}
                          {{ row.serviceNumber.progressUnit >= 1000000000000 ? row.serviceNumber.progress / row.serviceNumber.progressUnit : "" }}
                          <!-- (row.serviceNumber.progress / row.serviceNumber.progressUnit >= 1000 ? row.serviceNumber.progress / row.serviceNumber.progressUnit / 1000 : row.serviceNumber.progress / row.serviceNumber.progressUnit) -->
                          <span v-show="row.serviceNumber.progressUnit == 1"> B </span>
                          <span v-show="row.serviceNumber.progressUnit == 1000 && row.serviceNumber.progressUnit >= 1000"> K </span>
                          <span v-show="row.serviceNumber.progressUnit >= 1000000 && row.serviceNumber.progressUnit < 1000000000"> M </span>
                          <span v-show="row.serviceNumber.progressUnit >= 1000000000 && row.serviceNumber.progressUnit < 1000000000000"> G </span>
                          <span v-show="row.serviceNumber.progressUnit >= 1000000000000"> T </span>
                          <!-- <span v-show="row.serviceNumber.progressUnit == 1 && row.serviceNumber.progress == 1"> B </span> -->
                          <!-- <span v-show="row.serviceNumber.progressUnit != 1 && row.serviceNumber.progress >= 1000">
                          {{ row.serviceNumber.progress / row.serviceNumber.progressUnit >= 1000 ? serviceNumber[row.serviceNumber.progress] : serviceNumber[row.serviceNumber.progressUnit] }}
                        </span> -->
                          <!-- {{ row.serviceNumber.progress / row.serviceNumber.progressUnit >= 1000 ? serviceNumber[row.serviceNumber.progress] : serviceNumber[row.serviceNumber.progressUnit] }} -->
                        </template>
                        <!-- <template v-else> {{ row.serviceNumber.progress }}B </template> -->
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="serviceNumber.product" :label="`${i18n.t('devicesInfo.Product')}`"> </el-table-column>
                  <el-table-column prop="serviceNumber.id" :label="`ID`"> </el-table-column>
                  <el-table-column prop="address" :label="`${i18n.t('devicesInfo.Operate')}`" width="180">
                    <template #default="{ row }">
                      <div>
                        <el-button type="text" @click="serviceSize('edit', row.serviceNumber.id)" v-if="userInfo.hasPermission(资产管理中心_服务编号_编辑)">{{ `${i18n.t("glob.edit")}` }}</el-button>
                        <el-button type="text" textColor="danger" @click="deleteService(row.serviceNumber)" v-if="userInfo.hasPermission(资产管理中心_服务编号_删除)">{{ `${i18n.t("glob.delete")}` }}</el-button>
                        <el-button type="text" @click="serviceSize('info', row.serviceNumber.id)" v-if="userInfo.hasPermission(资产管理中心_服务编号_可读)">详情</el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </page-template>
          </el-tab-pane>
          <el-tab-pane :label="`${i18n.t('devicesInfo.SLA')}`" :name="`${i18n.t('devicesInfo.SLA')}`" v-if="userInfo.hasPermission(服务管理中心_SLA配置_可读)">
            <h3 style="text-align: right; position: relative; z-index: 99">
              <el-button v-if="userInfo.hasPermission(资产管理中心_设备_绑定SLA)" type="primary" :icon="Plus" @click="bindSla('add')"> {{ `${i18n.t("devicesInfo.Bind SLA service")}` }} </el-button>
            </h3>
            <page-template style="margin-top: -35px" v-if="userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(服务管理中心_SLA配置_可读)" :search-span="24" :showPaging="true" v-model:current-page="slaPaging.pageNumber" height="650" v-model:page-size="slaPaging.pageSize" :total="slaPaging.total" @current-change="getSlaList()" @size-change="getSlaList()">
              <template #default="{}">
                <el-table :data="slaList.slice((slaPaging.pageNumber - 1) * slaPaging.pageSize, slaPaging.pageNumber * slaPaging.pageSize)" stripe style="width: 100%" border :height="tableHeight">
                  <el-table-column prop="ruleName" :label="`${i18n.t('devicesInfo.SLA name')}`"> </el-table-column>
                  <el-table-column prop="ruleDesc" :label="`${i18n.t('devicesInfo.Description')}`"> </el-table-column>

                  <el-table-column prop="address" :label="`${i18n.t('devicesInfo.Operate')}`">
                    <template #default="{ row }">
                      <div>
                        <el-button v-if="userInfo.hasPermission(资产管理中心_设备_绑定SLA)" type="text" textColor="danger" @click="deleteSla(row)">{{ `${i18n.t("glob.remove")}` }}</el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </page-template>
          </el-tab-pane>

          <el-tab-pane :label="`${i18n.t('devicesInfo.Alerts')}`" :name="`${i18n.t('devicesInfo.Alerts')}`" v-if="userInfo.hasPermission(监控管理中心_设备_可读)">
            <page-template :search-span="24" v-if="userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(监控管理中心_设备_可读)" height="650" :showPaging="true" v-model:current-page="alarmPaging.pageNumber" v-model:page-size="alarmPaging.pageSize" :total="alarmPaging.total" @current-change="getAlarmList()" @size-change="getAlarmList()">
              <template #left>
                <!-- <h3>13456</h3> -->

                <el-button type="primary" @click="routerV6deviceHistoryAll(id)">
                  <el-icon><BellFilled /></el-icon>
                  {{ `${i18n.t("devicesInfo.History Alarm")}` }}
                </el-button>
              </template>
              <template #default="{}">
                <el-table :data="alarmList" v-loading="alarmLoading" stripe style="width: 100%" border :height="tableHeight" class="device-alarm-table">
                  <TableColumn type="enum" show-filter v-model:filtered-value="alarmObj.eventSeverity" @filter-change="handleQuery('alarm')" prop="eventSeverity" :label="`${i18n.t('devicesInfo.Urgency')}`" :width="130" :filters="eventSeverityOption.map((v) => ({ ...v, text: v.label }))"></TableColumn>

                  <!-- <el-table-column prop="eventSeverity" label="紧急性">
                    <template #default="{ row }">
                      <span class="priority" :style="{ background: eventColor[row.eventSeverity].color }">
                        {{ row.eventSeverity }}
                      </span>
                    </template>
                  </el-table-column> -->
                  <TableColumn type="order" prop="title" :label="`${i18n.t('devicesInfo.Alerts')}`" show-filter v-model:many-filtered-value="search.alarm.alarmObj" @filter-change="handleQuery('alarm')">
                    <template #default="{ row }">
                      <div>
                        <p>{{ row.title }}</p>
                        <p>{{ row.desc }}</p>
                      </div>
                    </template>
                  </TableColumn>

                  <TableColumn type="order" prop="title" :label="`${i18n.t('devicesInfo.Ticket')}`" show-filter v-model:many-filtered-value="search.alarm.alarmOrderObj" @filter-change="handleQuery('alarm')">
                    <template #default="{ row }">
                      <template v-if="userInfo.hasPermission(智能事件中心_客户_工单可读) || (userInfo.hasPermission('756062918394511360' as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))">
                        <p v-for="item in row.orders" :key="item.orderId">
                          <span class="priority" style="display: inline-block; height: 23px; line-height: 12px" :style="{ background: priority[item.priority].color }">
                            {{ item ? item.priority : "" }}
                          </span>
                          &nbsp;&nbsp;&nbsp;
                          <!-- <router-link :to="{ name: '510685950393712640', params: { id: row.id }, query: { fallback: $route.name as string, tenant: row.tenantId,deviceId:$route.params.id} } " target="_blank" tag="a">
                              {{ row.id }}
                            </router-link> -->
                          <span style="color: #409eff; cursor: pointer" @click="routerOrder(item, '告警')">{{ item.orderId ? item.orderId : "" }}</span>
                          &nbsp;&nbsp;&nbsp;
                          <span>{{ item.state ? eventStatus[item.state]?.value : "" }}</span>
                        </p>
                        {{ row.orders.length < 1 ? "--" : "" }}
                      </template>
                      <template v-else>
                        {{ "--" }}
                      </template>
                    </template>
                  </TableColumn>
                  <TableColumn type="date" show-filter v-model:filtered-value="alarmObj.alertCreateTimeRange" filter-multiple @filter-change="handleQuery('alarm')" prop="alertCreateTime" :label="`${i18n.t('devicesInfo.Timestamp')}`" :width="160"></TableColumn>
                  <TableColumn type="date" show-filter v-model:filtered-value="alarmObj.alarmBoardConfirmedTime" filter-multiple @filter-change="handleQuery('alarm')" prop="alarmBoardConfirmedTime" :label="`${i18n.t('devicesInfo.Alarm Confirmation Time')}`" :width="160"></TableColumn>
                  <!-- <el-table-column prop="alertCreateTime" label="时间">
                    <template #default="{ row }">
                      <div>
                        {{ moment(row.alertCreateTime, "x").format("yyyy-MM-DD HH:mm") }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="alarmBoardConfirmedTime" label="确认时间">
                    <template #default="{ row }">
                      <div>
                        <span v-show="row.alarmBoardConfirmedTime">
                          {{ moment(row.alarmBoardConfirmedTime, "x").format("yyyy-MM-DD HH:mm") }}
                        </span>
                        <span v-show="!row.alarmBoardConfirmedTime"> -- </span>
                      </div>
                    </template>
                  </el-table-column> -->
                  <el-table-column prop="alarmBoardConfirmedPerson.username" :label="`${i18n.t('devicesInfo.Acked By')}`">
                    <template #default="{ row }">
                      <div>
                        {{ JSON.parse(row.alarmBoardConfirmedPerson) ? JSON.parse(row.alarmBoardConfirmedPerson).username : "--" }}
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </page-template>
          </el-tab-pane>

          <el-tab-pane :label="`${i18n.t('devicesInfo.Journals')}`" :name="`${i18n.t('devicesInfo.Journals')}`" v-if="userInfo.hasPermission(资产管理中心_设备日志_可读)">
            <h3 style="text-align: right; position: relative; z-index: 99">
              <el-button v-if="userInfo.hasPermission(资产管理中心_设备日志_新增)" type="primary" @click="openEditor('add')"> {{ `${i18n.t("devicesInfo.Add")}` }} </el-button>
            </h3>
            <page-template style="margin-top: -35px" :search-span="24" v-if="logList.length > 0 && userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(资产管理中心_设备日志_可读)" height="650" :showPaging="true" v-model:current-page="logPaging.pageNumber" v-model:page-size="logPaging.pageSize" :total="logPaging.total" @current-change="getLogList()" @size-change="getLogList()">
              <template #right> </template>

              <template #default="{}">
                <!-- <el-table :data="devicelogList" stripe style="width: 100%" border height="550">
                  <el-table-column prop="name" label="">

                  </el-table-column>
                </el-table> -->
                <ul class="device-log-list">
                  <li v-for="item in logList" :key="item.id">
                    <div class="log-content">
                      <div v-html="item.logNote"></div>
                    </div>
                    <div class="log-file">
                      <span v-for="file in item.fileInfos" :key="file.key">
                        <el-icon v-if="userInfo.hasPermission(资产管理中心_设备_编辑)" style="font-size: 12px; cursor: pointer" @click="saveBufferByLogger(file)">
                          <Download style="width: 1rem; height: 1rem"></Download>
                        </el-icon>
                        {{ file.name }}
                        <b>({{ (Number(file.size) / 1024 / 1024).toFixed(2) }} MB)</b>
                        <el-icon v-if="userInfo.hasPermission(资产管理中心_设备_编辑)" style="font-size: 12px; cursor: pointer" @click="doneDeleteByLogger({ ...file, id: item.id })">
                          <DeleteFilled style="width: 1rem; height: 1rem"></DeleteFilled>
                        </el-icon>
                      </span>
                    </div>
                    <div class="log-operate">
                      <div>
                        <p>
                          {{ item.createdBy != null ? JSON.parse(item.createdBy)?.username || "--" : "--" }}
                        </p>
                        <p>
                          {{ moment(item.createdTime, "x").format("yyyy-MM-DD HH:mm:ss") }}
                        </p>
                      </div>
                      <div>
                        <el-button v-if="userInfo.hasPermission(资产管理中心_设备_编辑)" type="primary" @click="openUploadByLogger(item.id)">{{ `${i18n.t("glob.Upload")}` }}</el-button>
                        <el-button v-if="userInfo.hasPermission(资产管理中心_设备日志_删除)" type="danger" @click="delLog(item)">{{ `${i18n.t("glob.delete")}` }}</el-button>
                        <el-button v-if="userInfo.hasPermission(资产管理中心_设备日志_编辑)" type="primary" @click="openEditor('edit', item)">{{ `${i18n.t("glob.edit")}` }}</el-button>
                      </div>
                    </div>
                  </li>
                </ul>
              </template>
            </page-template>
          </el-tab-pane>
          <el-tab-pane :label="`${i18n.t('devicesInfo.Files')}`" :name="`${i18n.t('devicesInfo.Files')}`" v-if="userInfo.hasPermission(资产管理中心_设备_可读)">
            <div class="work" style="text-align: right">
              <h3 style="text-align: right; position: relative; z-index: 99; right: 0">
                <el-button v-if="userInfo.hasPermission(资产管理中心_设备_编辑)" type="primary" @click="openUploadByResource(id)"> {{ `${i18n.t("devicesInfo.Upload Files")}` }} </el-button>
              </h3>
            </div>
            <!-- <h3 style="text-align: right; position: relative; z-index: 99">
            </h3> -->

            <page-template style="margin-top: -35px" :search-span="24" v-show="deviceFileList.length > 0 && userInfo.hasPermission(资产管理中心_设备_可读)" height="650" :showPaging="true" v-model:current-page="filePaging.pageNumber" v-model:page-size="filePaging.pageSize" :total="filePaging.total" @current-change="getFileList()" @size-change="getFileList()">
              <!-- <template #right v-if="false"> </template> -->
              <template #default="{}">
                <el-table :data="deviceFileList" stripe style="width: 100%" border :height="tableHeight">
                  <el-table-column show-overflow-tooltip prop="name" :label="`${i18n.t('devicesInfo.Files Name')}`"> </el-table-column>
                  <el-table-column show-overflow-tooltip prop="size" :label="`${i18n.t('devicesInfo.size')}`">
                    <template #default="{ row }">
                      <div>{{ (Number(row.size) / 1024 / 1024).toFixed(2) }} M</div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="logfile" :label="`${i18n.t('devicesInfo.Redo Log File')}`">
                    <template #default="{ row }">
                      <el-icon v-if="row.logfile"><Select></Select></el-icon>
                      <div v-else></div>
                    </template>
                  </el-table-column>
                  <el-table-column show-overflow-tooltip prop="createdBy" :label="`${i18n.t('devicesInfo.Builder')}`">
                    <template #default="{ row }">
                      <div>{{ JSON.parse(row.createdBy)?.username || "--" }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column show-overflow-tooltip prop="createdTime" label="创建时间">
                    <template #default="{ row }">
                      <div>{{ moment(row.createdTime, "x").format("yyyy-MM-DD HH:mm") }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column :label="`${i18n.t('devicesInfo.Operate')}`">
                    <template #default="{ row }">
                      <div v-if="!row.logfile">
                        <el-button v-if="userInfo.hasPermission(资产管理中心_设备_编辑)" type="text" @click="saveBufferByResource(row)">{{ `${i18n.t("glob.delete")}` }}</el-button>
                        <el-button v-if="userInfo.hasPermission(资产管理中心_设备_编辑)" type="text" @click="doneDeleteByResource(row)">{{ `${i18n.t("glob.download")}` }}</el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </page-template>
          </el-tab-pane>
          <el-tab-pane :label="`${i18n.t('devicesInfo.Service catalog')}`" :name="`${i18n.t('devicesInfo.Service catalog')}`" v-if="userInfo.hasPermission(服务管理中心_服务目录_可读)">
            <div class="server_catalog">
              <div>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px">
                  <h3>{{ `${i18n.t("devicesInfo.Service catalog")}` }}</h3>
                  <el-button type="primary" :icon="Plus" @click="bindServiceCatalog('add')"> {{ `${i18n.t("devicesInfo.Allocation")}` }} </el-button>
                </div>
                <el-table :show-header="false" :data="serviceCatalogList" style="width: 100%" :height="tableHeight">
                  <el-table-column prop="serviceCatalogName" label="">
                    <template #default="{ row }">
                      <div @click="servicePack(row)" style="color: #409eff; cursor: pointer">{{ row.serviceCatalogName }}</div>
                      <div>{{ row.serviceCatalogDesc }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="address" label="" width="80px">
                    <template #default="{ row }">
                      <div>
                        <el-button type="text" textColor="danger" @click="deleteServiceCatalog(row)">{{ `${i18n.t("devicesInfo.Delete")}` }}</el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div v-show="packSla" style="height: 650px; overflow-y: auto">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px">
                  <h3>{{ `${i18n.t("devicesInfo.Service pack")}` }} {{ CatalogName }}</h3>
                  <el-button type="primary" @click="packSla = false"> {{ `${i18n.t("devicesInfo.Close")}` }} </el-button>
                </div>
                <el-table :show-header="false" :data="servicePackList" style="width: 100%">
                  <el-table-column prop="serviceCatalogName" label="">
                    <template #default="{ row }">
                      <div style="color: #409eff; cursor: pointer">{{ row.servicePackage }}</div>
                      <div>{{ row.description }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="address" label="" width="80px">
                    <template #default="{ row }">
                      <div>
                        <!-- <el-button type="text" textColor="danger" @click="deleteServiceCatalog(row)">删除</el-button> -->
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <div v-if="SlaList.length > 0" style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px">
                  <h3>SLA {{ CatalogName }}</h3>
                </div>
                <el-table v-if="SlaList.length > 0" :show-header="false" :data="SlaList" style="width: 100%" @row-click="packTime">
                  <el-table-column prop="serviceCatalogName" label="">
                    <template #default="{ row }">
                      <div style="color: #409eff; cursor: pointer">{{ row.slaName }}</div>
                      <div>{{ row.slaDesc }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="address" label="" width="80px">
                    <template #default="{ row }">
                      <div>
                        <!-- <el-button type="text" textColor="danger" @click="deleteServiceCatalog(row)">删除</el-button> -->
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <editor ref="editorRef" @submit="confrimLog"></editor>
    <UploadFile ref="uploadFileRef"></UploadFile>
    <serviceDialog :isAdd="serviceType" :id="id" ref="serviceRef" @confirm="confirmService"></serviceDialog>
    <slaDialog :id="id" @confirm="confirmSla" ref="slaRef" :selectedIds="slaList.map((v) => v.ruleId)"> </slaDialog>
    <ServiceCatalog :id="id" @confirm="confirmServiceCatalog" ref="ServiceCatalogRef"> </ServiceCatalog>
    <deviceDetials ref="deviceDetialsRef"></deviceDetials>
    <deviceContacts ref="deviceContactsRef"></deviceContacts>
    <devicePing ref="devicePingRef"></devicePing>
    <deviceRoute ref="deviceRouteRef"></deviceRoute>
    <el-dialog v-model="dialogVisible" title="" width="25%" :before-close="handleClose">
      <div class="vendor-message">
        <p>{{ vendorList[0].name }}</p>
        <p>{{ vendorList[0].description }}</p>
        <h2 style="margin-top: 15px">
          <el-icon><Iphone /></el-icon>
          {{ vendorList[0].landlinePhone }}
        </h2>
        <h2>
          <el-icon><Phone /></el-icon> {{ vendorList[0].supportPhone }}
        </h2>
      </div>
    </el-dialog>

    <deviceImportant ref="importantRef"> </deviceImportant>

    <!-- 覆盖时间 -->
    <el-dialog v-model="dialogSlaTime" title="覆盖时间" width="60%" :before-close="handleClose">
      <div style="width: 100%" class="support-table-content" ref="tableContentRef">
        <el-table stripe border :data="SlaInfo.coverTimeCfg.coverWorkTime" style="width: 100%; margin-top: 30px" @header-click="(column, $event) => handleClick({ column, $event })">
          <el-table-column align="left" prop="week" width="56" fixed="left">
            <template #default="scope">
              <div @click="handleSelectTime('all', scope.$index, scope.row)">
                {{ scope.row.weekDay == 1 ? "周一" : scope.row.weekDay == 2 ? "周二" : scope.row.weekDay == 3 ? "周三" : scope.row.weekDay == 4 ? "周四" : scope.row.weekDay == 5 ? "周五" : scope.row.weekDay == 6 ? "周六" : "周日" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" width="44" v-for="(item, key) in 24" :key="`h-${key}`" :label="String(key)">
            <template #default="scope">
              <div @click="handleSelectTime(key, scope.$index, scope.row)" style="width: 100%; height: 100%">
                <el-button type="text" style="font-size: 15px; color: rgb(26, 190, 107)">
                  <el-icon v-if="scope.row.workTime && scope.row.workTime.length && scope.row.workTime.includes(key)">
                    <Check></Check>
                  </el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </el-card>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { nextTick } from "vue";
import mixin from "../mixin";
import ModelExpand from "@/views/pages/modelExpand/Model.vue";

import regionMixin from "../../regionManage/js/mixin";
import { getDeviceDetaile, getRegionsContacts, getLocationsDetaile } from "@/views/pages/apis/deviceManage";
import zone from "../../../../common/zone.json";
import pageTemplate from "@/components/pageTemplate";
import serviceDialog from "../serviceDialog.vue";
import UploadFile from "./UploadFile.vue";

import { Download, DeleteFilled, Plus, Iphone, Phone, QuestionFilled, BellFilled, Select } from "@element-plus/icons-vue";
import editor from "./editor.vue";
import { getServiceList, delService, getNetcareDevice } from "@/views/pages/apis/deviceManage";
import slaDialog from "../slaDialog.vue";
import ServiceCatalog from "../ServiceCatalog.vue";
import bindContacts from "@/components/bindContacts/vIndex.vue";
import moment from "moment";
import { routerV6, routerV6Busines, routerV6Network, routerV6deviceHistoryAll } from "@/views/pages/common/routeV6";
import { eventSeverityOption } from "@/views/pages/apis/event";
import { deviceRelationSlaList, getDeviceLogList, deleteDeviceDeviceLogFile, downloadDeviceLogFile, addDeviceLogFile, deleteDeviceLog, editDeviceLog, getDeviceWorkOrderList, OrderType, DataType, deviceRelationSupportList, uploadFile, deleteDeviceFile, getDeviceFileList, downloadDeviceFile, addDeviceLog, saveDeviceFile, getserviceCatalogList, removeServiceCatalog, getPackageList, getSlaList, getSlaTime } from "@/views/pages/apis/deviceManage";
import { deleteDeviceRelation } from "@/views/pages/apis/SlaConfig";
import { getAlertClassificationsList, getVendorsList, getResourceTypeList, getDeviceGroupList } from "@/views/pages/apis/device";
import { stateEnum, eventStatus } from "../common";
import { getAlarmBoardDetail } from "@/views/pages/apis/alarmBoard";
import priority from "../../../../common/priority";
import eventColor from "../../../../common/eventColor";
import { h } from "vue";
import { formatDate } from "../../../../common/dateChange";
import getUserInfo from "@/utils/getUserInfo";
import { Zone } from "@/utils/zone";
import { strategyZone } from "../../../../common/strategyZone";
import { Check, Sunny, Moon } from "@element-plus/icons-vue";
import deviceDetials from "@/components/deviceTool/deviceDetials.vue";
import deviceContacts from "@/components/deviceTool/deviceContacts.vue";
import devicePing from "@/components/deviceTool/ping.vue";
import deviceRoute from "@/components/deviceTool/tracerRoute.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { getLocationsTenantCurrent } from "@/views/pages/apis/locationManang";
import deviceImportant from "./deviceImportant.vue";
import deviceTools from "@/components/deviceTool/index.vue";
import { desktop, getDeviceDiscovery } from "@/views/pages/apis/device";
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
import {
  /*  */
  资产管理中心_设备_可读,
  智能事件中心_客户_工单可读,
  监控管理中心_设备_可读,
  资产管理中心_设备_工具权限,
  系统管理中心_客户管理_可读,
  资产管理中心_联系人_可读,
  资产管理中心_设备_分配联系人,
  智能事件中心_工单看板_可读,
  资产管理中心_行动策略_可读,
  资产管理中心_服务编号_可读,
  资产管理中心_服务编号_新增,
  资产管理中心_服务编号_编辑,
  资产管理中心_服务编号_删除,
  资产管理中心_设备_绑定SLA,
  服务管理中心_SLA配置_可读,
  监控管理中心_告警_可读,
  监控管理中心_告警板_可读,
  资产管理中心_设备日志_可读,
  资产管理中心_设备日志_新增,
  资产管理中心_设备_编辑,
  资产管理中心_设备日志_删除,
  资产管理中心_设备日志_编辑,
  资产管理中心_联系人_查看联系人,
  服务管理中心_服务目录_可读,
  智能事件中心_设备_工单可读,
  服务管理中心_告警分类_可读,
  资产管理中心_设备供应商_可读,
  资产管理中心_设备类型_可读,
  资产管理中心_设备分组_可读,
} from "@/views/pages/permission";
import { ElMessage, ElMessageBox } from "element-plus";
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
import { useI18n } from "vue-i18n";
import { useResizeObserver } from "@vueuse/core";
import handleToOrder from "@/views/pages/alarm_convergence/IntelligentEvents/eventBoard/toOrder";

// const tableContentRef = ref();
// const tableWidth = ref(0);
// useResizeObserver(tableContentRef, (entries) => {
//   const entry = entries[0];
//   const { width, height } = entry.contentRect;
//   tableWidth.value = ((width - 80) / 24).toFixed(0);
// });
const userInfo = getUserInfo();
// const refs = ref({});
export default {
  name: "deviceManageDetails",
  components: {
    pageTemplate,
    Check,
    Sunny,
    Moon,
    serviceDialog,
    slaDialog,
    ServiceCatalog,
    // bindContacts,
    Plus,
    UploadFile,
    editor,
    Download,
    DeleteFilled,
    ModelExpand,
    deviceDetials,
    deviceContacts,
    devicePing,
    deviceRoute,
    TableColumn,
    Iphone,
    Phone,
    Select,
    QuestionFilled,
    deviceImportant,
    BellFilled,
    deviceTools,
  },
  mixins: [mixin, regionMixin],
  data() {
    return {
      timeZoneHours: _timeZoneHours,
      i18n: useI18n(),
      eventSeverityOption,
      userInfo,
      paging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      uploadDialog: false,
      priority: priority,
      eventColor: eventColor,
      eventStatus: eventStatus,
      activeName: `${useI18n().t("devicesInfo.Details")}`,
      // activeName:
      //   sessionStorage.getItem("deviceTab") != "undefined" &&
      //   sessionStorage.getItem("deviceTab") != null
      //     ? sessionStorage.getItem("deviceTab")
      //     : "详述",
      content: `${useI18n().t("devicesInfo.Device details")}`,
      showCancel: false, //是否展示取消按钮
      showPercentage: false, //是否展示上传进度
      percentage: 0, //上传进度
      showFile: false, //展示上传后的文件列表
      uploadTitle: "",
      showUploadFial: false,
      fileList: [],
      id: this.$route.params.id,
      showImportant: false,
      tableHeight: "550",
      deskTopObj: [],
      form: {
        modelIdent: null, //模型标识
        regionId: null, //所在区域ID
        locationId: null, //所在场所ID
        typeIds: [], //设备类型ID列表
        groupIds: [], //设备组ID列表
        vendorIds: [], //服务商ID列表
        alertClassifications: [], //告警分类ID列表
        externalId: null, //外部ID
        name: null, //设备名称
        description: null, //设备描述
        timeZone: null, //时区
        importance: null, //资源重要性
        tags: [], //标签
        serviceNumbers: [],
        active: true, //是否激活
        assetNumber: null, //资产编号
        monitorSources: [], //监控源
        groups: [], //设备分组
        vendors: [],
        config: {
          ipAddress: "", //ip地址
          dynamicIp: "", //是否默认IP
          ackRequired: "", //确认告警

          nmsTicketing: "", //自动事件
          connectAuthType: "", //远程登录认证
          modelNumbers: [], //型号
          serialNumbers: [], //序列号
          assetNumbers: [], //资产编号
        },
      },
      routerV6Busines: routerV6Busines,
      routerV6Network: routerV6Network,
      routerV6deviceHistoryAll: routerV6deviceHistoryAll,
      zone: [...zone],
      timeZone: [...zone],
      timeZoneConvert: { ...Zone },
      deviceImgList: [
        {
          normalImg: require("@/views/pages/assets/device/desktop-normal.png"),
          offlineImg: require("@/views/pages/assets/device/desktop-offline.png"),
          warningImg: require("@/views/pages/assets/device/desktop-warning.png"),
        },
        {
          normalImg: require("@/views/pages/assets/device/wifi-normal.png"),
          offlineImg: require("@/views/pages/assets/device/wifi-offline.png"),
          warningImg: require("@/views/pages/assets/device/wifi-warning.png"),
        },
        {
          normalImg: require("@/views/pages/assets/device/share-normal.png"),
          offlineImg: require("@/views/pages/assets/device/share-offline.png"),
          warningImg: require("@/views/pages/assets/device/share-warning.png"),
        },
        {
          normalImg: require("@/views/pages/assets/device/vertical-normal.png"),
          offlineImg: require("@/views/pages/assets/device/vertical-offline.png"),
          warningImg: require("@/views/pages/assets/device/vertical-warning.png"),
        },
      ],
      weekDay: 1, //判断当前日期是周几
      serviceType: "",
      serviceList: [],
      serviceLoading: true,

      slaList: [],
      slaPaging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      workOrderList: [],
      workOrderPaging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },

      alarmList: [],
      alarmPaging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      supportList: [],
      deviceFileList: [],
      serviceCatalogList: [],
      servicePackList: [],
      SlaList: [],
      SlaInfo: {},
      packSla: false,
      CatalogName: "",
      filePaging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      logList: [],
      logPaging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      logId: "",
      timeHours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
      deviceSize: {},
      deviceAlarmList: [],
      systemItemInfoList: [],
      OrderType: OrderType,
      DataType: DataType,
      // state:{
      search: {
        orderType: {
          status: "include",
          type: "include",
          relation: "AND",
          firstName: "",
          lastName: "",
        },
        orderId: {
          status: "include",
          type: "include",
          relation: "AND",
          firstName: "",
          lastName: "",
        },
        orderSummary: {
          status: "include",
          type: "include",
          relation: "AND",
          firstName: "",
          lastName: "",
        },
        alarm: {
          eventSeverity: "",
          alarmObj: {
            status: "include",
            type: "include",
            relation: "AND",
            firstName: "",
            lastName: "",
          },
          alarmOrderObj: {
            status: "include",
            type: "include",
            relation: "AND",
            firstName: "",
            lastName: "",
          },
        },
      },
      state: {
        includeOrderType: [],
        excludeOrderType: [],
        includeOrderSummary: [],
        excludeOrderSummary: [],
        includeOrderIds: [],
        excludeOrderIds: [],
        orderTypeRelation: "AND",
        inOrderId: [],
        neInOrderId: [],
        inOrderSummary: [],
        neInOrderSummary: [],

        orderIdFilterRelation: "AND",

        orderSummaryFilterRelation: "AND",
      },
      alarmObj: {
        eventSeverity: "",
        includeAlerts: [],
        alertFilterRelation: "AND",
        excludeAlerts: [],
        includeOrderIds: [],
        orderIdFilterRelation: "AND",
        excludeOrderIds: [],
        alertCreateTimeRange: "",
        alarmBoardConfirmedTime: "",
        // includeOrderIds:[],
      },

      alarmLoading: false,
      locationOption: {},
      locationAddress: [],
      dialogVisible: false,
      dialogSlaTime: false,
      vendorList: [],
      serviceNumber: {
        "1": "B",
        "1000": "K",
        "1000000": "M",
        "1000000000": "G",
        "1000000000000": "T",
      },
      ciNeRootDevicev0: {},
      // }
      资产管理中心_设备_可读,
      资产管理中心_设备_工具权限,
      系统管理中心_客户管理_可读,
      资产管理中心_联系人_可读,
      资产管理中心_设备_分配联系人,
      智能事件中心_工单看板_可读,
      资产管理中心_行动策略_可读,
      资产管理中心_服务编号_可读,
      资产管理中心_服务编号_新增,
      资产管理中心_服务编号_编辑,
      资产管理中心_服务编号_删除,
      资产管理中心_设备_绑定SLA,
      服务管理中心_SLA配置_可读,
      监控管理中心_告警_可读,
      监控管理中心_告警板_可读,
      资产管理中心_设备日志_可读,
      资产管理中心_设备日志_新增,
      资产管理中心_设备_编辑,
      资产管理中心_设备日志_删除,
      资产管理中心_设备日志_编辑,
      服务管理中心_服务目录_可读,
      监控管理中心_设备_可读,
      智能事件中心_客户_工单可读,
      资产管理中心_联系人_查看联系人,
      智能事件中心_设备_工单可读,
      资产管理中心_设备分组_可读,
      deviceInfo: {},
      alarmListA: [],
      vendorListA: [],
      deviceTypeListA: [],
      deviceGroupListA: [],
      vendorInfo: [],
      alarmInfo: [],
      deviceTypeInfo: [],
      deviceGroupInfo: [],

      detailLoading: false,
    };
  },
  computed: {
    routeName() {
      return this.$route.name;
    },
  },
  watch: {
    regionsOptionArr() {
      this.setRegionName();
    },
    activeName(active) {
      if (active != undefined) sessionStorage.setItem("deviceTab", active);
      this.getDetail(); //设备详情
      switch (active) {
        case "详述":
          if (userInfo.hasPermission(资产管理中心_设备_可读)) {
            /* 详述 */
            this.getLocationList(); //场所信息
            this.getRegionList(); //区域信息
          }
          break;
        case "联系人":
          if (userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(资产管理中心_联系人_可读)) {
            /* 联系人[组件] */
          }
          break;
        case "工单":
          if ((userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(智能事件中心_客户_工单可读)) || (userInfo.hasPermission("756062918394511360" as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))) {
            /* 工单 */
            this.getWorkOrderList(); //工单列表
          }
          break;
        case "行动策略":
          if (userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(资产管理中心_行动策略_可读)) {
            /* 行动策略 */
            this.getSupportList(); //行动策略
          }
          break;
        case "服务编号":
          if (userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(资产管理中心_服务编号_可读)) {
            /* 服务编号 */
            this.getServiceNumberList(); //服务编号
          }
          break;
        case "SLA服务":
          if (userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(服务管理中心_SLA配置_可读)) {
            /* SLA服务 */
            this.getSlaList(); //sla配置
          }
          break;
        case "告警":
          if (userInfo.hasPermission(监控管理中心_设备_可读) && userInfo.hasPermission(系统管理中心_客户管理_可读) && userInfo.hasPermission(资产管理中心_设备_可读)) {
            /* 告警 */
            this.getAlarmList(); //告警列表
          }
          break;
        case "日志":
          if (userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(资产管理中心_设备日志_编辑)) {
            /* 日志 */
            this.getLogList(); //日志列表
          }
          break;
        case "文件":
          if (userInfo.hasPermission(资产管理中心_设备_可读)) {
            /* 文件 */
            this.getFileList(); //文件列表
          }
          break;
        case "服务目录":
          if (userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(服务管理中心_服务目录_可读)) {
            /* 文件 */
            this.getserviceCatalogList(); //文件列表
          }
          break;
      }
    },
  },
  mounted() {
    let today = new Date();
    this.weekDay = today.getDay();
    this.id = this.$route.params.id;
    this.netcareDevice();
    // this.getSupplierList();
    // this.getdeviceTypeList(); //设备信息
    if (userInfo.hasPermission(资产管理中心_设备_可读)) {
      /* 详述 */
      this.getLocationList(); //场所信息
      this.getRegionList(); //区域信息
    }
    if (userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(资产管理中心_联系人_可读)) {
      /* 联系人[组件] */
    }
    if (userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(智能事件中心_工单看板_可读)) {
      /* 工单 */
      this.getWorkOrderList(); //工单列表
    }
    if (userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(资产管理中心_行动策略_可读)) {
      /* 行动策略 */
      this.getSupportList(); //行动策略
    }
    if (userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(资产管理中心_服务编号_可读)) {
      /* 服务编号 */
      this.getServiceNumberList(); //服务编号
    }
    if (userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(服务管理中心_SLA配置_可读)) {
      /* SLA服务 */
      this.getSlaList(); //sla配置
    }
    if (userInfo.hasPermission(监控管理中心_设备_可读) && userInfo.hasPermission(系统管理中心_客户管理_可读) && userInfo.hasPermission(资产管理中心_设备_可读)) {
      /* 告警 */
      this.getAlarmList(); //告警列表
    }
    if (userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(资产管理中心_设备日志_编辑)) {
      /* 日志 */
      this.getLogList(); //日志列表
    }
    if (userInfo.hasPermission(资产管理中心_设备_可读)) {
      /* 文件 */
      this.getFileList(); //文件列表
    }
    if (userInfo.hasPermission(资产管理中心_设备_可读) && userInfo.hasPermission(服务管理中心_服务目录_可读)) {
      /* 服务目录 */
      this.getserviceCatalogList(); //服务目录列表
    }

    this.getDetail(); //设备详情

    // setTimeout(()=>{
    // },200)
    // this.getRequestList(); //服务请求

    // this.activeName = this.$route.query ? this.$route.query.type : "详述";
    // console.log(this.userInfo);

    // this.getVendors();
    // this.getAlertClassifications();
    // this.getResource();
    // this.getDeviceGroup();
  },
  beforeUnmount() {
    // 清理工作或者取消订阅等操作
    sessionStorage.setItem("deviceTab", "");
  },
  methods: {
    // async getVendors() {
    //   const { success, message, data } = await getVendorsList({ vendorType: "DEVICE", containerId: this.userInfo.currentTenant.containerId, queryPermissionId: "515410299369553920", verifyPermissionIds: "512903546983677952,512903566256504832,512903582660427776" });
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   this.vendorListA = data instanceof Array ? data : [];
    //   nextTick(() => {
    //     console.log(this.vendorListA, this.form.vendors);
    //     this.vendorInfo = this.filterByAlarmList(this.vendorListA, this.form.vendors);
    //   });
    // },
    // async getAlertClassifications() {
    //   const { success, message, data } = await getAlertClassificationsList({});
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   this.alarmListA = data instanceof Array ? data : [];
    //   nextTick(() => {
    //     this.alarmInfo = this.filterByAlarmList(this.alarmListA, this.form.alertClassifications);
    //   });
    // },
    // async getResource() {
    //   const { success, message, data } = await getResourceTypeList({ containerId: this.userInfo.currentTenant.containerId, queryPermissionId: "512873892906270720", verifyPermissionIds: "512878757501992960,512878782932058112,512881859223355392" });
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   this.deviceTypeListA = data instanceof Array ? data : [];
    //   nextTick(() => {
    //     this.deviceTypeInfo = this.filterByAlarmList(this.deviceTypeListA, this.form.typeIds);
    //   });
    // },
    // 设备分组
    // async getDeviceGroup() {
    //   const { success, message, data } = await getDeviceGroupList({ containerId: this.userInfo.currentTenant.containerId, queryPermissionId: "512862869692350464", verifyPermissionIds: "512862890017947648,512862911924797440,512879011144138752" });
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   this.deviceGroupListA = data instanceof Array ? data : [];
    //   nextTick(() => {
    //     this.deviceGroupInfo = this.filterByAlarmList(this.deviceGroupListA, this.form.groups);
    //   });
    // },
    filterByAlarmList(alarmList, arr) {
      // 1. 如果alarmList为空，直接返回空数组
      if (!alarmList || alarmList.length === 0) {
        return [];
      }
      // 2. 创建alarmList的ID映射表（使用字符串形式避免类型问题）
      const alarmIdMap = new Map();
      alarmList.forEach((item) => {
        alarmIdMap.set(String(item.id), item);
      });
      // 3. 过滤arr数组，只保留存在于alarmList中的项
      return arr.filter((item) => {
        return alarmIdMap.has(String(item.id));
      });
    },
    // ,
    handleViewContancts(id) {
      // deviceId.value = id;
      this.$refs["deviceContactsRef"].dialogFormVisible = true;
      // this.$refs['deviceContactsRef'].id = id;
      this.$refs["deviceContactsRef"].open(id);
    },
    setRegionName() {
      const getTreeRegion = (id, res) => {
        const findRegion: any = this.regionsOptionArr.find((v) => v.id === id);
        res.unshift(findRegion.name);
        return findRegion.parentId ? getTreeRegion(findRegion.parentId, res) : res;
      };
      const currentRegion = this.regionsOptionArr.find((v) => v.id === this.form.regionId) || {};
      const currentRegionParentId = (this.regionsOptionArr.find((v) => v.id === currentRegion.id) || {}).parentId || null;
      const result = [currentRegion.name];
      this.form.regionNames = (currentRegionParentId ? getTreeRegion(currentRegionParentId, result) : result).join("/");
    },
    moment,
    timeZoneSwitching() {
      const timeZone = this.timeZoneHours.find((item) => {
        if (item.zoneId == this.userInfo.zoneId) {
          return item.offsetHours;
        }
      });

      return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
    },
    Important(val) {
      // this.showImportant = true;
      // console.log(this.$refs.importantRef);
      this.$refs.importantRef.mounted();
      this.$refs.importantRef.getList();
      this.$refs.importantRef.deviceStatus = val;
      this.$refs.importantRef.dialogVisible = true;
    },
    openVendor(list) {
      // console.log(list);
      this.vendorList = [...list];
      this.dialogVisible = true;
    },
    /* 场所列表 */
    async getLocationList() {
      try {
        await nextTick();
        this.locationList = [];
        const { success, message, data } = await getLocationsTenantCurrent({});
        if (!success) throw Object.assign(new Error(message), { success, data });
        this.locationList = data instanceof Array ? data : [];
        this.locationOption = this.locationList.reduce((p, c) => Object.assign(p, { [c.id]: c.name, [c.id + c.name]: c.zoneId }), {});
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      }
    },
    handleQuery(type) {
      this.state.includeOrderType = [];
      this.state.excludeOrderType = [];
      this.state.includeOrderSummary = [];
      this.state.excludeOrderSummary = [];
      this.state.includeOrderIds = [];
      this.state.excludeOrderIds = [];
      this.state.inOrderId = [];
      this.state.neInOrderId = [];
      this.state.inOrderSummary = [];
      this.state.neInOrderSummary = [];
      this.state.orderTypeRelation = this.search.orderType.relation;
      this.state.orderIdFilterRelation = this.search.orderId.relation;
      this.state.orderSummaryFilterRelation = this.search.orderSummary.relation;

      this.alarmObj.includeAlerts = [];
      this.alarmObj.excludeAlerts = [];
      this.alarmObj.includeOrderIds = [];
      this.alarmObj.excludeOrderIds = [];
      this.alarmObj.alertFilterRelation = this.search.alarm.alarmObj.relation;
      this.alarmObj.orderIdFilterRelation = this.search.alarm.alarmOrderObj.relation;

      // console.log(this.search.alarm; type, this.alarmObj);

      //类型
      this.setFilterMessage(this.state.includeOrderType, this.state.excludeOrderType, [], [], this.search.orderType);
      //工单号
      this.setFilterMessage(this.state.includeOrderIds, this.state.excludeOrderIds, this.state.inOrderId, this.state.neInOrderId, this.search.orderId);
      //摘要
      this.setFilterMessage(this.state.includeOrderSummary, this.state.excludeOrderSummary, this.state.inOrderSummary, this.state.neInOrderSummary, this.search.orderSummary);

      //告警工单
      this.setFilterMessage(this.alarmObj.includeOrderIds, this.alarmObj.excludeOrderIds, [], [], this.search.alarm.alarmOrderObj);

      //告警摘要
      this.setFilterMessage(this.alarmObj.includeAlerts, this.alarmObj.excludeAlerts, [], [], this.search.alarm.alarmObj);

      if (typeof type === "string") {
        setTimeout(() => {
          this.getAlarmList();
        });
      } else {
        setTimeout(() => {
          this.getWorkOrderList();
        });
      }
    },
    setFilterMessage(includeData: any, excludeData: any, beData: any, notBeData: any, type: any) {
      switch (type.status) {
        case "include":
          includeData.push(type.firstName);

          break;
        case "exclude":
          excludeData.push(type.firstName);
          break;
        case "be":
          beData.push(type.firstName);
          break;
        case "notBe":
          notBeData.push(type.firstName);
          break;
      }

      switch (type.type) {
        case "include":
          includeData.push(type.lastName);
          break;
        case "exclude":
          excludeData.push(type.lastName);
          break;
        case "be":
          beData.push(type.firstName);
          break;
        case "notBe":
          notBeData.push(type.firstName);
          break;
      }
    },
    //跳转工单详情
    routerOrder(v) {
      handleToOrder(v.orderType, v.orderId, v.tenantId, { deviceId: this.$route.params.id });
    },
    netcareDevice() {
      getNetcareDevice({ id: this.id }).then((res) => {
        if (res.success) {
          this.deviceSize = { ...res.data.ciNeRootDeviceVO };
          this.deviceAlarmList = [...res.data.monitorVOList].map((item) => {
            return {
              ...item,
              happenTime: Number(item.happenTime) + this.timeZoneSwitching(),
            };
          });
          this.systemItemInfoList = [...res.data.systemItemInfoList];
          if (this.systemItemInfoList[3]) {
            const t = moment(this.systemItemInfoList[3].value, "YYYY-MM-DD HH:mm:ss").valueOf() + this.timeZoneSwitching();
            this.systemItemInfoList[3].value = moment(t).format("YYYY-MM-DD HH:mm:ss");
          }
        }
      });
    },

    routeDevice(props) {
      this.$refs.deviceRouteRef.open({
        ...this.form,
        id: this.$route.params.id,
      });
    },
    async ping(props: any) {
      await this.$refs.devicePingRef.open({
        ...this.form,
        id: this.$route.params.id,
      });
      // pingTo({ id: "523721061548687360" }).then((res) => {
      //   // console.log(res);
      // });
      // deviceId = row.id;
      // deviceContactsRef.dialogFormVisible = true;
    },

    //下载日志附件
    downloadLogFile(id, file) {
      // // console.log(id, file);
      downloadDeviceLogFile({ filePath: file.filePath }).then((res) => {
        const link = document.createElement("a");
        let blob = new Blob([res.data]);
        link.style.display = "none";
        link.href = URL.createObjectURL(blob);
        link.setAttribute("download", res.contentDisposition.filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      });
    },
    //删除日志附件
    deleteLogFile(id, file) {
      this.$confirm(`确定删除当前日志附件吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteDeviceDeviceLogFile({ id: id, filePath: file.filePath }).then((res) => {
            if (res.success) {
              this.$message.success("操作成功");

              this.getLogList(); //日志列表
            } else this.$message.error(JSON.parse(res.data)?.message);
          });
        })
        .catch(() => {
          //
        });
    },
    ifShow(type) {
      if (type == "" || type == undefined || type == null || type == "null") {
        return false;
      } else {
        return true;
      }
    },
    //下载文件
    // downloadFile(obj) {
    //   downloadDeviceFile({ filePath: obj.filePath }).then((res) => {
    //     const link = document.createElement("a");
    //     let blob = new Blob([res.data]);
    //     link.style.display = "none";
    //     link.href = URL.createObjectURL(blob);
    //     link.setAttribute("download", res.contentDisposition.filename);
    //     document.body.appendChild(link);
    //     link.click();
    //     document.body.removeChild(link);
    //   });
    // },
    //删除文件
    // delFile(obj) {
    //   this.$confirm(`确定删除${obj.name}?`, "提示", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: "warning",
    //   })
    //     .then(() => {
    //       deleteDeviceFile({ id: obj.id }).then((res) => {
    //         if (res.success) {
    //           this.$message.success("操作成功");
    //           // tableData.value.
    //           let totalPage = Math.ceil((this.filePaging.total - 1) / this.filePaging.pageSize);
    //           let currengpage = this.filePaging.pageNumber > totalPage ? totalPage : this.filePaging.pageNumber;
    //           this.filePaging.pageNumber = currengpage < 1 ? 1 : currengpage;

    //           this.getFileList();
    //         } else this.$message.error(JSON.parse(res.data)?.message);
    //       });
    //     })
    //     .catch(() => {
    //       //
    //       this.$message("取消操作");
    //     });
    // },
    async saveBufferByLogger(raw) {
      try {
        const { success, message, data, contentDisposition } = await downloadDeviceLogFile({ filePath: raw.filePath });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const link = document.createElement("a");
        let blob = new Blob([data]);
        link.style.display = "none";
        link.href = URL.createObjectURL(blob);
        link.setAttribute("download", contentDisposition.filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (error) {
        ElMessage.error(error instanceof Error ? error.message : `${error}`);
      }
    },
    async doneDeleteByLogger(raw) {
      await ElMessageBox.confirm(`确定删除当前日志附件吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        async beforeClose(action, instance, done) {
          if (instance.confirmButtonLoading) return;
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "Loading...";
            try {
              const { success, message, data } = await deleteDeviceDeviceLogFile({ id: raw.id, filePath: raw.filePath });
              if (!success) throw Object.assign(new Error(message), { success, data });
              done();
              ElMessage.success("操作成功");
            } catch (error) {
              ElMessage.error(error instanceof Error ? error.message : `${error}`);
              if (error instanceof Error) {
                instance.showConfirmButton = false;
                instance.message = error.message;
                instance.type = "error";
              }
            } finally {
              instance.confirmButtonText = "确定";
              instance.confirmButtonLoading = false;
            }
          } else {
            done();
          }
        },
      }).catch((error) => Promise.resolve(error));
      await this.getLogList();
    },
    async openUploadByLogger(id: string) {
      await this.$refs.uploadFileRef
        .opener({ file: [], bucket: "resource.log.file" }, async ($form) => {
          const fileResult = $form.file.filter((v) => v.status === "success").map((v) => v.response!);
          const { success, message, data } = await addDeviceLogFile(fileResult, id);
          if (!success) throw Object.assign(new Error(message), { success, data });
        })
        .catch((error) => Promise.resolve(error));
      await this.getLogList();
    },
    async saveBufferByResource(raw) {
      try {
        const { success, message, data, contentDisposition } = await downloadDeviceFile({ filePath: raw.filePath });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const link = document.createElement("a");
        let blob = new Blob([data]);
        link.style.display = "none";
        link.href = URL.createObjectURL(blob);
        link.setAttribute("download", contentDisposition.filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (error) {
        ElMessage.error(error instanceof Error ? error.message : `${error}`);
      }
    },
    async doneDeleteByResource(raw) {
      await ElMessageBox.confirm(`确定删除${raw.name}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        async beforeClose(action, instance, done) {
          if (instance.confirmButtonLoading) return;
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "Loading...";
            try {
              const { success, message, data } = await deleteDeviceFile({ id: raw.id });
              if (!success) throw Object.assign(new Error(message), { success, data });
              done();
              ElMessage.success("操作成功");
            } catch (error) {
              ElMessage.error(error instanceof Error ? error.message : `${error}`);
              if (error instanceof Error) {
                instance.showConfirmButton = false;
                instance.message = error.message;
                instance.type = "error";
              }
            } finally {
              instance.confirmButtonText = "确定";
              instance.confirmButtonLoading = false;
            }
          } else {
            done();
          }
        },
      }).catch((error) => Promise.resolve(error));
      await this.getFileList();
    },
    async openUploadByResource(id: string) {
      await this.$refs.uploadFileRef
        .opener({ file: [], bucket: "resource.file" }, async ($form) => {
          const fileResult = $form.file.filter((v) => v.status === "success").map((v) => v.response!);
          const { success, message, data } = await saveDeviceFile(fileResult, id);
          if (!success) throw Object.assign(new Error(message), { success, data });
        })
        .catch((error) => Promise.resolve(error));
      await this.getFileList();
    },
    //打开富文本编辑器弹框
    openEditor(type, obj) {
      // this.$refs.editorRef.type = type;
      if (type != "add") {
        this.logId = obj.id;
      }
      this.$refs.editorRef.open(type, obj);
    },
    //删除日志
    delLog(row) {
      this.$confirm(`确定删除当前日志吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // this.logList.length = this.logList.length - 1;
          deleteDeviceLog({ id: row.id }).then((res) => {
            if (res.success) {
              this.$message.success("操作成功");
              // if (this.logList.length == 0) {
              //   this.logPaging.pageNumber = 1;
              // }
              let totalPage = Math.ceil((this.logPaging.total - 1) / this.logPaging.pageSize);
              let currengpage = this.logPaging.pageNumber > totalPage ? totalPage : this.logPaging.pageNumber;
              this.logPaging.pageNumber = currengpage < 1 ? 1 : currengpage;
              // if (this.logList.slice((this.logPaging.pageNumber - 1) * this.logPaging.pageSize, this.logPaging.pageNumber * this.logPaging.pageSize).length == 0) {
              //   this.logPaging.pageNumber = 1;
              // }
              this.logPaging.pageNumber = 1;
              this.logPaging.pageSize = 10;
              this.getLogList(); //日志列表
            } else this.$message.error(JSON.parse(res.data)?.message);
          });
        })
        .catch(() => {
          //
        });
    },
    //日志富文本确认
    confrimLog(val) {
      if (val.type === "add") {
        addDeviceLog({
          resourceId: this.id,
          logNote: val.value,
        }).then((res) => {
          if (res.success) {
            this.$message.success("操作成功");
            this.$refs.editorRef.dialogVisible = false;
            this.logPaging.pageNumber = 1;
            this.logPaging.pageSize = 10;
            this.getLogList(); //日志列表
          } else {
            this.$message.error(JSON.parse(res.data)?.message);
          }
        });
      } else {
        editDeviceLog({
          id: this.logId,
          logNote: val.value,
        }).then((res) => {
          if (res.success) {
            this.$message.success("操作成功");
            this.$refs.editorRef.dialogVisible = false;
            this.logPaging.pageNumber = 1;
            this.logPaging.pageSize = 10;
            this.getLogList(); //日志列表
          } else {
            this.$message.error(JSON.parse(res.data)?.message);
          }
        });
      }
    },
    //获取日志列表
    async getLogList() {
      try {
        await nextTick();
        this.logList = [];
        const { success, message, data, page, size, total } = await getDeviceLogList({ id: this.id, pageNumber: this.logPaging.pageNumber, pageSize: this.logPaging.pageSize, sort: "createdTime,desc" });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        this.logList =
          data instanceof Array
            ? data.map((item) => {
                return {
                  ...item,
                  createdTime: Number(item.createdTime) + this.timeZoneSwitching(),
                  updatedTime: Number(item.updatedTime) + this.timeZoneSwitching(),
                };
              })
            : [];
        this.logPaging.total = total * 1;
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      }
    },
    // //给文件添加图片
    // fileHandle(data) {
    //   this.$refs.uploadFileRef.percentage = 100;
    //   this.$refs.uploadFileRef.showCancel = false;
    //   this.$refs.uploadFileRef.uploadTitle = "上传成功";

    //   data.forEach((item) => {
    //     let index = item.name.lastIndexOf(".");
    //     let type = item.name.substr(index + 1, item.name.length);
    //     if (["png", "jpg", "jpeg", "gif", "PNG", "JPG", "JPEG", "GIF"].indexOf(type) != -1) {
    //       item.className = "imgiconPng";
    //     } else if (["mp4", "3gp", "avi", "flv", "MP4", "3GP", "AVI", "FLV"].indexOf(type) != -1) {
    //       item.className = "imgiconVideo";
    //     } else if (["doc", "docx", "DOC", "DOCX"].indexOf(type) != -1) {
    //       item.className = "imgiconDoc";
    //     } else if (["xls", "xlsx", "XLS", "XLSX"].indexOf(type) != -1) {
    //       item.className = "imgiconXls";
    //     } else if (["txt", "TXT"].indexOf(type) != -1) {
    //       item.className = "imgiconTxt";
    //     } else {
    //       item.className = "imgiconOther";
    //     }
    //   });
    //   data.forEach((v, i) => {
    //     this.$refs.uploadFileRef.fileList.push(v);
    //   });
    // },
    // //文件上传
    // uploadSuccess(val) {
    //   if (val.operateType === "file") {
    //     let formData = new FormData();

    //     val.uploadFileList.forEach((item) => {
    //       formData.append("files", item.raw);
    //     });

    //     uploadFile(formData, "resource.file").then(({ success, data }) => {
    //       if (success) {
    //         this.fileHandle(data);
    //         this.$refs.uploadFileRef.timer = setInterval(() => {
    //           if (this.$refs.uploadFileRef.percentage < 100) {
    //             this.$refs.uploadFileRef.percentage = this.$refs.uploadFileRef.percentage + 1;
    //           }

    //           if (this.$refs.uploadFileRef.percentage > 100) {
    //             this.$refs.uploadFileRef.percentage = 100;
    //             clearInterval(this.$refs.uploadFileRef.timer);
    //             this.$refs.uploadFileRef.timer = null;
    //           }
    //           // // console.log(this.percentage);
    //         }, 10);
    //         this.saveFile(data, "file");
    //       } else {
    //         this.$refs.showUploadFial = true;
    //         this.$refs.uploadTitle = "文件上传失败，网络错误";
    //       }
    //     });
    //   } else {
    //     // // console.log(this.logId);
    //     let formData = new FormData();

    //     val.uploadFileList.forEach((item) => {
    //       formData.append("files", item.raw);
    //     });
    //     uploadFile(formData, "resource.log.file").then(({ success, data }) => {
    //       if (success) {
    //         this.fileHandle(data);

    //         this.saveFile(data, "logs");
    //       } else {
    //         this.$refs.showUploadFial = true;
    //         this.$refs.uploadTitle = "文件上传失败，网络错误";
    //       }
    //     });
    //   }
    // },
    // saveFile(data, type) {
    //   if (type === "file") {
    //     saveDeviceFile(data, this.id).then((res) => {
    //       if (res.success) {
    //         this.getFileList(); //文件列表
    //         // this.$refs.uploadFileRef.uploadDialog = false;
    //       }
    //     });
    //   } else {
    //     addDeviceLogFile(data, this.logId).then((res) => {
    //       if (res.success) {
    //         this.getLogList(); //日志列表
    //         // this.$refs.uploadFileRef.uploadDialog = false;
    //       }
    //     });
    //   }
    // },

    async getFileList() {
      try {
        await nextTick();
        this.deviceFileList = [];
        const { success, message, data, page, size, total } = await getDeviceFileList({ id: this.id, pageNumber: this.filePaging.pageNumber, pageSize: this.filePaging.pageSize, sort: "createdTime,desc" });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        this.deviceFileList =
          data instanceof Array
            ? data.map((item) => {
                return {
                  ...item,
                  createdTime: Number(item.createdTime) + this.timeZoneSwitching(),
                };
              })
            : [];
        this.filePaging.total = total * 1;
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      }
    },
    getSupportList() {
      deviceRelationSupportList({ id: this.id }).then((res) => {
        if (res.success) {
          let data = [...res.data.showSupportNotes];
          if (data)
            data.forEach((v, i) => {
              if (v.active) {
                let arrLength = 0;
                // if (v.activeConfig.useAutoTimeZone) {
                //   let timezoneOffset = -new Date().getTimezoneOffset();
                //   for (let i = 0; i < this.timeZone.length; i++) {
                //     if (this.timeZone[i].zoneId === v.activeConfig.timeZone) {
                //       v.activeConfig.activeHours.forEach((h) => {
                //         if (h.weekDay == this.weekDay) {
                //           let date = new Date();
                //           let year = date.getFullYear();
                //           let mon = date.getMonth();
                //           let day = date.getDate();
                //           let time = new Date(year, mon, day, h.hours.length == 24 ? 24 : v.activeTime, "00", "00").getTime() + (timezoneOffset - this.timeZone[i].offsetMinutes) * 60 * 1000;

                //           v.dateTime = formatDate(time, "yyyy-MM-dd HH:mm");

                //           v.showWorkTime = date > new Date(v.dateTime) ? false : true;
                //           if (h.hours.length == 0) {
                //             v.dateTime = "";
                //           }
                //         }
                //       });

                //       // console.log(v.allTime);
                //       break;
                //     } else {
                //       // arrLength = 0;
                //       v.activeConfig.activeHours.forEach((h) => {
                //         if (h.weekDay == this.weekDay) {
                //           let date = new Date();
                //           let year = date.getFullYear();
                //           let mon = date.getMonth();
                //           let day = date.getDate();
                //           let time = new Date(year, mon, day, h.hours.length == 24 ? 24 : v.activeTime, "00", "00").getTime() + timezoneOffset * 60 * 1000;

                //           v.dateTime = formatDate(time, "yyyy-MM-dd HH:mm");

                //           v.showWorkTime = date > new Date(v.dateTime) ? false : true;
                //           if (h.hours.length == 0) {
                //             v.dateTime = "";
                //           }
                //         }
                //         // // console.log(arrLength);
                //       });
                //     }
                //   }
                // } else {
                //   let arrLength = 0;
                //   let timezoneOffset = -new Date().getTimezoneOffset();

                //   if (timezoneOffset === strategyZone[v.activeConfig.timeZone]) {
                //     // console.log(v);
                //     v.activeConfig.activeHours.forEach((h) => {
                //       if (h.weekDay == this.weekDay) {
                //         let date = new Date();
                //         let year = date.getFullYear();
                //         let mon = date.getMonth();
                //         let day = date.getDate();
                //         let time = new Date(year, mon, day, h.hours.length == 24 ? 24 : v.activeTime, "00", "00").getTime();
                //         v.dateTime = formatDate(time, "yyyy-MM-dd HH:mm");

                //         v.showWorkTime = date > new Date(v.dateTime) ? false : true;
                //         if (h.hours.length == 0) {
                //           v.dateTime = "";
                //         }
                //       }
                //     });
                //   } else {
                //     let arrLength = 0;
                //     let timezoneOffset = -new Date().getTimezoneOffset();

                //     this.timeZone.forEach((item) => {
                //       if (item.zoneId === v.activeConfig.timeZone) {
                //         v.activeConfig.activeHours.forEach((h) => {
                //           if (h.weekDay == this.weekDay) {
                //             let date = new Date();
                //             let year = date.getFullYear();
                //             let mon = date.getMonth();
                //             let day = date.getDate();
                //             let time = new Date(year, mon, day, h.hours.length == 24 ? 24 : v.activeTime, "00", "00").getTime() + (timezoneOffset - item.offsetMinutes) * 60 * 1000;
                //             v.dateTime = formatDate(time, "yyyy-MM-dd HH:mm");

                //             let isWokrTime = h.hours.indexOf(v.activeTime - 1 - (timezoneOffset - item.offsetMinutes) / 60);

                //             v.showWorkTime = date > new Date(v.dateTime) ? false : true;
                //             if (h.hours.length == 0) {
                //               v.dateTime = "";
                //             }
                //           }
                //         });
                //       }
                //     });
                //   }
                // }

                v.activeConfig.activeHours.forEach((h) => {
                  if (h.hours.length == 24) {
                    arrLength = arrLength + 1;
                  }
                });

                if (arrLength === v.activeConfig.activeHours.length) {
                  v.allTime = true;
                }
              }
            });
          this.supportList = res.data.showSupportNotes ?? [];
          // this.supportList =
          //   [...res.data.showSupportNotes].map((item) => {
          //     return {
          //       ...item,
          //       activeConfig: {
          //         ...item.activeConfig,
          //         timeForZone: moment(moment(item.activeConfig.timeForZone, "YYYY-MM-DD HH:mm:ss").valueOf() + this.timeZoneSwitching()).format("YYYY-MM-DD HH:mm:ss"),
          //       },
          //     };
          //   }) || [];
        }
      });
    },
    getAlarmList() {
      // queryScope:'SERVICE_REQUEST'
      this.alarmLoading = true;
      getAlarmBoardDetail({
        deviceId: this.id,
        ...this.alarmPaging,
        sort: "alertCreateTime,desc",
        ...this.alarmObj,
      }).then((res) => {
        if (res.success) {
          this.alarmList = [...res.data].map((item) => {
            return {
              ...item,
              alertCreateTime: Number(item.alertCreateTime) + this.timeZoneSwitching(),
              alarmBoardConfirmedTime: item.alarmBoardConfirmedTime ? Number(item.alarmBoardConfirmedTime) + this.timeZoneSwitching() : item.alarmBoardConfirmedTime,
            };
          });
          this.alarmPaging.total = res.total * 1;

          this.alarmLoading = false;
        }
      });
    },
    //表格内容填充
    eventTableFormatter(_row, _col, v) {
      switch (_col.property) {
        case "status":
          if (!v) return h("el-tag", { class: "elstyle-tag--info" }, "--");
          // eslint-disable-next-line no-case-declarations, no-inner-declarations
          const currentState = stateEnum.find(({ key }) => key === v);

          return h("el-tag", { class: currentState?.prop?.class }, currentState?.value || "--");
        case "eventName":
          return v || "--";
        case "deNoiseName":
          return v || "--";
        default:
          return v || "--";
      }
    },
    // 筛选策略类型
    filteringType(supportBindType) {
      let translation = supportBindType === "CUSTOMER" ? `${this.i18n.t("devicesInfo.Customer engagement strategy")}` : supportBindType === "RESOURCE" ? `${this.i18n.t("devicesInfo.Equipment operation strategy")}` : supportBindType === "LOCATION" ? `${this.i18n.t("devicesInfo.Workplace work strategy")}` : supportBindType === "REGION" ? `${this.i18n.t("devicesInfo.Regional work strategy")}` : ""; // 默认值，防止意外的类型
      return translation;
    },
    tableFormatter(_row, _col, v) {
      switch (_col.property) {
        case "tenantId":
          return this.$pinia.state.value.info.tenants.find((i) => i.tenantId === v)?.tenantName || "--";
        case "serviceState":
          if (!v) return h("el-tag", { class: "elstyle-tag--info" }, "--");
          // eslint-disable-next-line no-case-declarations, no-inner-declarations
          const currentState = stateEnum.find(({ key }) => key === v);

          return h("el-tag", { class: currentState?.prop?.class }, currentState?.value || "--");
        case "eventName":
          return v || "--";
        case "deNoiseName":
          return v || "--";
        default:
          return v || "--";
      }
    },
    getWorkOrderList() {
      //
      getDeviceWorkOrderList({
        id: this.id,
        pageNumber: this.workOrderPaging.pageNumber,
        pageSize: this.workOrderPaging.pageSize,
        sort: "orderUpdateTime,desc",
        ...this.state,
      }).then((res) => {
        if (res.success) {
          this.workOrderList = [...res.data].map((item) => {
            return {
              ...item,
              orderCreateTime: Number(item.orderCreateTime) + this.timeZoneSwitching(),
              orderUpdateTime: Number(item.orderUpdateTime) + this.timeZoneSwitching(),
            };
          });
          this.workOrderPaging.total = res.total * 1;
        }
      });
    },
    // getRequestList() {
    //   // // console.log(getUserInfo().currentTenant, 555555);
    //   //getUserInfo()
    //   getDeviceRequestList({ deviceId: this.id, ...this.requestPaging, tenantId: getUserInfo().currentTenant.tenantId }).then((res) => {
    //     if (res.success) {
    //       this.requestList = [...res.data];
    //       this.requestPaging.total = res.total * 1;
    //     }
    //   });
    // },
    getSlaList() {
      deviceRelationSlaList({
        id: this.id,
        ruleName: "",
      }).then((res) => {
        if (res.success) {
          this.slaList = [...res.data];
          this.slaPaging.total = res.data.length;
        }
      });
    },
    //服务编号 新增/编辑事件操作
    confirmService(bool) {
      this.serviceType = "";
      // this.serviceLoading = true;
      // if (bool) {
      this.getServiceNumberList();
      // }
      // setTimeout(() => {
      //   this.serviceLoading = false;
      // },300);
    },
    //绑定sla规则
    bindSla() {
      this.$refs.slaRef.dialogVisible = true;
      this.$refs.slaRef.resourceId = this.id;
      this.$refs.slaRef.ruleId = "";
      // console.log(this.slaList);
      this.$refs.slaRef.disabledList = this.slaList;
    },
    //删除sla绑定
    deleteSla(row) {
      this.$confirm(`确定删除${row.ruleName}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.slaList.length = this.slaList.length - 1;
          deleteDeviceRelation({ id: this.id, slaId: row.ruleId }).then((res) => {
            if (res.success) {
              this.$message.success("操作成功");
              if (this.slaList.length == 0) {
                this.slaPaging.pageNumber = 1;
              }
              this.getSlaList();
            } else this.$message.error(JSON.parse(res.data)?.message);
          });
        })
        .catch(() => {
          //
        });
    },
    // 获取服务目录列表
    async getserviceCatalogList() {
      const parma = {
        containerId: this.userInfo.currentTenant.containerId,
        queryPermissionId: "659635446946463744",
        verifyPermissionIds: "659635513484902400,659635555297918976,659635670263791616",
        id: this.id,
      };
      try {
        await nextTick();
        this.serviceCatalogList = [];
        const { success, message, data, page, size, total } = await getserviceCatalogList(parma);
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        this.serviceCatalogList = data instanceof Array ? data : [];
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      }
    },
    //绑定服务目录
    bindServiceCatalog() {
      this.$refs.ServiceCatalogRef.dialogVisible = true;
      this.$refs.ServiceCatalogRef.resourceId = this.id;
      this.$refs.ServiceCatalogRef.serviceCatalogId = [];
      this.$refs.ServiceCatalogRef.getServiceCatalogList();
    },
    //删除服务目录
    deleteServiceCatalog(row) {
      this.$confirm(`确定删除${row.serviceCatalogName}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            removeServiceCatalog({ id: this.id, serviceCatalogId: row.serviceCatalogId })
              .then((res) => {
                if (res.success) {
                  this.$message.success("操作成功");
                  this.getserviceCatalogList();
                  this.packSla = false;
                  done();
                } else this.$message.error(JSON.parse(res.data)?.message);
              })
              .finally(() => {
                instance.confirmButtonLoading = false;
              });
          } else done();
        },
      })
        .then(() => {})
        .catch(() => {});
    },
    confirmServiceCatalog(bool) {
      if (bool) this.getserviceCatalogList();
    },
    // 查询服务包
    async servicePack(row) {
      this.packSla = true;
      this.CatalogName = row.serviceCatalogName;
      try {
        await nextTick();
        this.servicePackList = [];
        const { success, message, data, page, size, total } = await getPackageList({ id: row.serviceCatalogId });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        this.servicePackList = data instanceof Array ? data : [];
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      }
      try {
        await nextTick();
        this.SlaList = [];
        const { success, message, data, page, size, total } = await getSlaList({ id: row.serviceCatalogId });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        this.SlaList = data instanceof Array ? data : [];
        console.log("SlaList :>> ", this.SlaList);
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      }
    },
    // 查询覆盖时间
    async packTime(row, column, event) {
      // this.$refs.SlaTimeRef.dialogVisible = true;
      // this.$refs.SlaTimeRef.ruleId = row.slaId;
      // this.$refs.SlaTimeRef.tenantId = row.tenantId;
      try {
        await nextTick();
        this.SlaInfo = {};
        const { success, message, data, page, size, total } = await getSlaTime({ ruleId: row.slaId, tenantId: row.tenantId });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        this.SlaInfo = data;
        this.dialogSlaTime = true;
        console.log("SlaInfo :>> ", this.SlaInfo.coverTimeCfg.coverWorkTime);
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      }
    },
    confirmSla() {
      //
      this.getSlaList();
    },

    //获取服务编号列表
    getServiceNumberList() {
      this.serviceLoading = true;
      getServiceList({
        pageNumber: this.paging.pageNumber,
        pageSize: this.paging.pageSize,
        resourceId: this.id,
        containerId: this.userInfo.currentTenant.containerId,
        queryPermissionId: "576357337313312768",
        verifyPermissionIds: "576357477122048000,576357518356250624,576357563314995200,612902124509986816",
      })
        .then((res) => {
          if (res.success) {
            setTimeout(() => {
              this.serviceLoading = false;
            }, 300);
            this.serviceList = res.data;
            this.paging.total = Number(res.total);
          }
        })
        .catch((err) => {
          setTimeout(() => {
            this.serviceLoading = false;
          }, 300);
        });
    },
    //删除服务编号
    deleteService(row) {
      this.$confirm(`确定删除${row.number}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delService({ id: row.id }).then((res) => {
            if (res.success) {
              this.$message.success("操作成功");
              this.getServiceNumberList();
            } else this.$message.error(JSON.parse(res.data)?.message);
          });
        })
        .catch(() => {
          //
        });
      // delService({ id }).then(res => {

      // })
    },
    //编辑/新增服务编号
    serviceSize(type, id) {
      this.serviceType = type;
      (this.$refs.serviceRef as any).dialogVisible = true;
      (this.$refs.serviceRef as any).form.resourceId = this.id;

      if (id) {
        (this.$refs.serviceRef as any).form.id = id;
      }
    },
    backRouter /* 返回上一页 */() {
      if ("fallback" in this.$route.query && typeof this.$route.query.fallback === "string")
        this.$router.replace({
          name: this.$route.query.fallback,
          params: { id: this.$route.query.eventId },
        });
      else if (history.length === 1) window.close();
      else this.$router.back();
    },
    //设备详情
    async getDetail() {
      try {
        this.detailLoading = true;
        const [
          /*  */
          res,
          { success: groupSuccess, message: groupMessage, data: groupData },
          { success: vendorsSuccess, message: vendorsMessage, data: vendorsData },
          { success: alertClassificationsSuccess, message: alertClassificationsMessage, data: alertClassificationsData },
          { success: resourceTypeSuccess, message: resourceTypeMessage, data: resourceTypeData },
        ] = await Promise.all([
          /*  */
          getDeviceDetaile({ id: this.id }),
          getDeviceGroupList({ containerId: this.userInfo.currentTenant.containerId, queryPermissionId: "512862869692350464", verifyPermissionIds: "512862890017947648,512862911924797440,512879011144138752" }),
          getVendorsList({ vendorType: "DEVICE", containerId: this.userInfo.currentTenant.containerId, queryPermissionId: "515410299369553920", verifyPermissionIds: "512903546983677952,512903566256504832,512903582660427776" }),
          getAlertClassificationsList({}),
          getResourceTypeList({ containerId: this.userInfo.currentTenant.containerId, queryPermissionId: "512873892906270720", verifyPermissionIds: "512878757501992960,512878782932058112,512881859223355392" }),
        ]);
        if (!groupSuccess) throw Object.assign(new Error(groupMessage), { groupMessage, groupData });
        if (!vendorsSuccess) throw Object.assign(new Error(vendorsMessage), { vendorsMessage, vendorsData });
        if (!alertClassificationsSuccess) throw Object.assign(new Error(alertClassificationsMessage), { alertClassificationsMessage, alertClassificationsData });
        if (!resourceTypeSuccess) throw Object.assign(new Error(resourceTypeMessage), { resourceTypeMessage, resourceTypeData });
        //  const { success, message, data } = await getDeviceGroupList({ containerId: this.userInfo.currentTenant.containerId, queryPermissionId: "512862869692350464", verifyPermissionIds: "512862890017947648,512862911924797440,512879011144138752" });
        // if (!success) throw Object.assign(new Error(message), { success, data });
        this.deviceGroupListA = groupData instanceof Array ? groupData : [];
        this.vendorListA = vendorsData instanceof Array ? vendorsData : [];
        this.alarmListA = alertClassificationsData instanceof Array ? alertClassificationsData : [];
        this.deviceTypeListA = resourceTypeData instanceof Array ? resourceTypeData : [];

        // await getDeviceDetaile({ id: this.id }).then((res) => {
        if (res.success) {
          let config = { ...res.data.config };
          this.deviceInfo = res.data;
          // config.dynamicIp = JSON.parse(config.dynamicIp);
          // config.ackRequired = JSON.parse(config.ackRequired);
          // config.nmsTicketing = JSON.parse(config.nmsTicketing);
          config.modelNumbers = JSON.parse(config.modelNumbers || "[]");
          config.serialNumbers = JSON.parse(config.serialNumbers || "[]");
          config.assetNumbers = JSON.parse(config.assetNumbers || "[]");
          // this.form = { ...res.data };
          this.form.modelIdent = res.data.modelIdent; //模型标识
          this.form.locationId = res.data.locationDesc; //所在场所ID

          this.form.typeIds = res.data.resourceTypes; //设备类型ID列表
          console.log("res.data.resourceTypes :>> ", res.data.resourceTypes);
          this.form.groupIds = res.data.groupIds; //设备组ID列表
          this.form.vendors = res.data.vendors; //服务商ID列表
          this.form.alertClassifications = res.data.alertClassifications; //告警分类ID列表
          this.form.externalId = res.data.externalId; //外部ID
          this.form.name = res.data.name; //设备名称
          this.form.description = res.data.description; //设备描述
          this.form.timeZone = res.data.timeZone; //时区
          this.form.importance = res.data.importance; //资源重要性
          this.form.tags = res.data.tags; //标签
          this.form.active = res.data.active; //是否激活
          this.form.regionId = res.data.regionId; //所在区域ID
          this.form.config = config;
          this.form.serviceNumbers = res.data.serviceNumbers;
          this.form.assetNumber = res.data.assetNumber;
          this.form.monitorSources = res.data.monitorSources;
          this.form.groups = res.data.groups;
          this.form.regionDesc = res.data.regionDesc;
          this.$nextTick(() => this.setRegionName());

          // nextTick(() => {
          this.deviceGroupInfo = this.filterByAlarmList(this.deviceGroupListA, res.data.groups);
          this.vendorInfo = this.filterByAlarmList(this.vendorListA, this.form.vendors);
          this.alarmInfo = this.filterByAlarmList(this.alarmListA, this.form.alertClassifications);
          this.deviceTypeInfo = this.filterByAlarmList(this.deviceTypeListA, this.form.typeIds);

          // });

          zone.forEach((v) => {
            if (v.zoneId == this.locationOption[this.form.locationId + this.locationOption[this.form.locationId]]) {
              return (this.form.timeZone = v.displayName);
            }
          });
          if (res.data.locationId) {
            getLocationsDetaile({ id: res.data.locationId }).then((res) => {
              if (res.success) {
                this.locationAddress = res.data;
              }
            });
          }
        }
      } catch (error) {
        error instanceof Error && this.$message.error(error.message);
      } finally {
        this.detailLoading = false;
      }

      //   this.getDeviceGroup();
      // });
      await desktop({ deviceIds: this.id })
        .then((res) => {
          // console.log(res, 6666);
          if (res.success) {
            this.deskTopObj = res.data;
            this.deskTopObj.forEach((item) => {
              this.form.showDesktop = item.icoShow;
              this.form.allowTypes = item.allowTypes;
            });
          }
          // if (res.success) discovery.value = res.data;
        })
        .catch((err) => {
          //
        });
    },
    isEmptyOrNullOrEmptyTags(str) {
      // 如果字符串是null或undefined，直接返回true
      // 如果字符串为 null 或 undefined，直接返回 true
      if (str === null || str === undefined) {
        return true;
      }
      // 去除字符串中的空格和 HTML 标签，然后检查是否为空
      const cleanedStr = str
        .replace(/<[^>]*>/g, "")
        .replace(/&nbsp;/g, "")
        .trim();
      // 检查 cleanedStr 是否为空
      return cleanedStr === "";
    },
  },
};
</script>

<style lang="scss" scoped>
.vendor-message {
  padding: 0 15px;
  box-sizing: border-box;
  > p:first-child {
    font-size: 22px;
  }

  h2 {
    border: 1px solid #ddd;
    padding: 10px;
    box-sizing: border-box;
    border-radius: 3px;
    color: #3e97ff;
  }
}
:deep(.el-page-header__header) {
  display: flex;
  background: #fff;
  .el-page-header__left {
    flex: 1;
  }
  .el-page-header__content {
    flex: 1;
  }
}
:deep(.el-button--text:not(.is-disabled):focus) {
  color: #409eff;
}

.device-alarm-table {
  :deep(.cell) {
    line-height: 30px;
  }
}
.device-log-list {
  width: 100%;
  height: auto;
  // border: 1px solid #e4eced;
  padding: 20px;
  box-sizing: border-box;
  // min-height: 550px;
  > li {
    width: 100%;
    height: auto;
    border: 1px solid #e4eced;
    padding: 10px 20px;
    box-sizing: border-box;
    // min-height: 200px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    margin-bottom: 10px;
    // justify-content: space-between;
    > .log-content {
      // flex: 1;
      border-bottom: 1px solid #e4eced;
      height: auto;
      margin-bottom: 10px;
    }
    > .log-file {
      flex: none;
      // border-bottom: 1px solid #e4eced;
      height: auto;
      // padding: 8px 0;
      box-sizing: border-box;
      display: flex;
      flex-wrap: wrap;
      span {
        display: block;
        flex: none;
        height: 35px;
        padding: 8px;
        box-sizing: border-box;
        background: rgb(250, 250, 250);
        color: rgb(72, 137, 237);
        border: 1px solid #e4eced;
        margin: 0 10px 10px 0;
      }
    }
    > .log-operate {
      flex: none;
      border-top: 1px solid #e4eced;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: auto;
    }
  }
}
.imgList {
  display: flex;
  flex-direction: column;
  > li {
    display: flex;
    align-items: center;
    padding: 8px 0;
    box-sizing: border-box;
    .imgiconPng {
      background: url("../../../../assets/device/picture.png") no-repeat;
      background-size: 100% 100%;
    }
    .imgiconVideo {
      background: url("../../../../assets/device/picture.png") no-repeat;
      background-size: 100% 100%;
    }
    .imgiconDoc {
      background: url("../../../../assets/device/docx.png") no-repeat;
      background-size: 100% 100%;
    }
    .imgiconXls {
      background: url("../../../../assets/device/xlsx.png") no-repeat;
      background-size: 100% 100%;
    }
    .imgiconTxt {
      background: url("../../../../assets/device/txt.png") no-repeat;
      background-size: 100% 100%;
    }
    .imgiconOther {
      background: url("../../../../assets/device/picture.png") no-repeat;
      background-size: 100% 100%;
    }

    .file-img {
      width: 42px;
      height: 42px;
      margin-right: 10px;
    }
  }
}
.upload-fail {
  display: flex;
  align-items: center;
}
.device-detail {
  overflow-y: auto;
}
.device-detail-tab-list {
  padding: 0 20px;
  box-sizing: border-box;
  background: #fff;
  :deep(.el-tabs__content) {
    overflow-y: hidden;
  }
  // .el-tabs__content::-webkit-scrollbar-vertical {
  //     display: none; /* 将垂直滚动条隐藏起来 */
  //   }
}
.priority {
  color: #fff;
  padding: 5px 10px;
  box-sizing: border-box;
  border-radius: 20px;
  cursor: pointer;
}
.priorityTag {
  color: #fff;
  padding: 5px 10px;
  box-sizing: border-box;
  border-radius: 20px;
}
.details {
  background: #fff;
}
.title {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  p {
    line-height: 30px;
    font-size: 18px;
  }
}
.device-img {
  display: flex;
  > img {
    margin-right: 8px;
    cursor: pointer;
    width: 25px;
    height: auto;
  }
}
// ::v-deep .elstyle-tabs__nav-wrap {
//   padding: 0 20px;
//   box-sizing: border-box;
// }
// ::v-deep .elstyle-tab-pane {
//   padding: 0 20px;
//   box-sizing: border-box;
// }
.message {
  width: 100%;
  height: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  > div {
    flex: none;
    width: 48%;
    border: 1px solid #ddd;
    padding: 15px;
    box-sizing: border-box;
    > ul {
      width: 100%;
    }
  }

  ul > li {
    width: 100%;
    display: flex;
    justify-content: space-between;
    min-height: 40px;
    align-items: center;
    padding: 10px 15px 0;
    box-sizing: border-box;

    > span:first-child {
      display: flex;
      min-width: 120px;
      flex: none;
      justify-content: flex-start;
    }
    > span {
      flex: 1;
      display: flex;
      //
      flex-wrap: wrap;
      justify-content: flex-end;
      word-wrap: break-word;
      word-break: break-all;
    }
    > span:nth-child(even) {
      // flex: none;
      width: calc(100% - 120px);
      text-align: right;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      // display: flex;
      // justify-content: flex-end;
    }
  }
  ul > li:nth-child(odd) {
    border-bottom: 1px solid #ddd;
    background: rgb(241, 241, 241);
  }
  .monitor {
    display: flex;
    flex-direction: column;
    border: 0 !important;
    padding: 0;
    > div {
      flex: 1;
      border: 1px solid #ddd;
      padding: 15px;
      box-sizing: border-box;
    }
    > div:last-child {
      flex: none;
      height: 49%;
      margin-top: 1%;
    }
  }
}
.work {
  width: 100%;
  // height: auto;
  > div {
    // margin-bottom: 30px;
    // border: 1px solid #ddd;
    // padding: 20px;
    // box-sizing: border-box;

    // height: 300px;
    // overflow-y: auto;
  }
  :deep(.cell) {
    // height: 35px;
    // line-height: 30px;
  }
  h3 {
    margin-bottom: 0px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
.strategy-title {
  padding: 10px;
  box-sizing: border-box;
}
.strategy {
  width: 100%;
  height: auto;
  display: flex;
  justify-content: space-between;
  //

  > div {
    margin-right: 10px;
    width: 48%;
    min-height: 200px;
    border: 1px solid #ddd;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
    // align-items: center;
    > h2 {
      flex: none;
      width: 100%;
      height: 50px;
      line-height: 50px;
      padding: 0 15px;
      background: #ddd;
      // border-bottom: 1px solid #ddd;
      display: flex;
      justify-content: space-between;
    }
    div {
      width: 100%;
      min-height: 100px;
      flex: 1;
      // line-height: 100px;
      padding-left: 15px;
    }
    h4 {
      border-top: 1px solid #ddd;
      width: 100%;
      height: 50px;
      line-height: 50px;
      padding-left: 15px;
    }
    > div {
      overflow-x: auto;
    }
  }
  > div.week {
    border-color: #3e97ff;
    border: 1px solid #3e97ff;
    > h2 {
      background: #3e97ff;
      color: #fff;
    }
  }
}
.server_catalog {
  display: flex;
  justify-content: space-between;
  > div {
    width: 49%;
    border: 1px solid #dddddd;
    padding: 20px;
  }
}
.strategy-box {
  display: flex;
  margin: 10px;
  .strategy-left {
    width: 49%;
  }
  .strategy-right {
    margin-left: 50px;
  }
}
.pre-line {
  white-space: pre-line;
}
</style>
