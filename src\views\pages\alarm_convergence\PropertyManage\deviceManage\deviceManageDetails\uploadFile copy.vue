<!--  -->
<template>
  <el-dialog title="上传文件" v-model="uploadDialog" width="35%">
    <el-upload class="mk" multiple :show-file-list="false" :http-request="shangchuan" :auto-upload="true" :on-progress="handleProgress" :on-change="handleFileChange" :on-success="successs">
      <el-button type="primary" @click="chooseFile">选择文件</el-button>
      <!-- <template #tip>
          <div class="el-upload__tip">jpg/png files with a size less than 500kb</div>
        </template> -->
    </el-upload>
    <el-upload style="margin-top: 10px" class="upload-demo" ref="uploadRef" drag :http-request="shangchuan" :auto-upload="true" multiple :show-file-list="false" :on-success="successs" :on-progress="handleProgress" :on-change="handleFileChange">
      <template #default>
        <div v-show="!showFile">
          <el-icon style="font-size: 40px"><UploadFilled style="width: 1em; height: 1em" /></el-icon>
          <div class="el-upload__text">拖拽文件至此处</div>
        </div>

        <!-- zidingyimubanneirong -->
        <!-- <div class="el-upload__tip">jpg/png files with a size less than 500kb</div> -->

        <div v-show="showPercentage && !showUploadFial" style="text-align: left">
          <span>
            {{ uploadTitle }}
          </span>
          <el-progress :percentage="percentage" />
        </div>
        <div class="upload-fail" v-show="showUploadFial">
          <el-icon style="font-size: 14px; color: red">
            <Warning style="width: 1em; height: 1em" />
          </el-icon>
          {{ uploadTitle }}

          <el-button type="text" @click.stop="referUplaod">重新上传</el-button>
        </div>
      </template>
      <template #tip>
        <ul class="imgList">
          <li v-for="item in fileList" :key="item">
            <div class="file-img" :class="item.className"></div>
            <div>{{ item.name }} {{ (Number(item.size) / 1024 / 1024).toFixed(1) }} M</div>
            <!-- <el-image :src="item" :preview-src-list="fileList"> </el-image> -->
          </li>
        </ul>
      </template>
    </el-upload>
    <template #footer>
      <el-button v-show="showCancel" @click="cancelUpload">取消</el-button>
      <!-- <el-button @click="uploadSuccess">提交</el-button> -->
    </template>
  </el-dialog>
</template>
<script>
import { UploadFilled, Warning } from "@element-plus/icons-vue";
export default {
  name: "upload-fail",
  components: {
    UploadFilled,
    Warning,
  },
  emits: ["uploadDialog"],
  data() {
    return {
      showCancel: false, //是否展示取消按钮
      showPercentage: false, //是否展示上传进度
      percentage: 0, //上传进度
      showFile: false, //展示上传后的文件列表
      uploadTitle: "",
      showUploadFial: false,
      fileList: [],
      uploadDialog: false,
      operateType: "",
      uploadFileList: [],
      timer: null,
      type: "",
    };
  },
  methods: {
    open() {
      this.uploadDialog = true;
      this.showCancel = false; //是否展示取消按钮
      this.showPercentage = false; //是否展示上传进度
      this.percentage = 0; //上传进度
      this.showFile = false; //展示上传后的文件列表
      this.uploadTitle = "";
      this.showUploadFial = false;
      this.fileList = [];
      this.uploadFileList = [];
      clearInterval(this.timer);
      this.timer = null;

      // ;

      // this.operateType = "";
    },
    chooseFile() {
      this.$refs.uploadRef.clearFiles();
      clearInterval(this.timer);
    },
    //取消上传
    cancelUpload() {
      this.$refs.uploadRef.abort();
      this.showUploadFial = true;
      this.uploadTitle = "文件上传失败，用户取消";
      clearInterval(this.timer);
      this.$refs.uploadRef.clearFiles();
      this.timer = null;
    },
    referUplaod() {
      document.querySelector(".el-upload__input").click();
    },
    //文件上传
    handleFileChange(file, fileList) {
      this.uploadFileList = [];
      this.percentage = 0;
      let index = file.name.lastIndexOf(".");
      let type = file.name.substr(index + 1, file.name.length);
      this.showCancel = true;
      if (["html", "htm", "xhtml", "shtml", "js", "jsp", "jspa", "jspx", "jspf", "jsw", "jsv", "jhtml", "jtml", "php", "psp", "pht", "phtml", "php1", "php2", "php3", "php4", "php5", "php7", "asp", "aspx", "asa", "asax", "ascx", "ashx", "asmx", "cer", "der", "crt", "sh", "exe", "swf", "htaccess"].indexOf(type.toLowerCase()) != -1) {
        this.showUploadFial = true;
        this.uploadTitle = "文件上传失败，格式错误";
      } else {
        this.$emit("submit", { uploadFileList: fileList, operateType: this.operateType });
        this.$refs.uploadRef.clearFiles();
        this.showUploadFial = false;
        this.showPercentage = true;
        // this.fileList = fileList;
        // this.uploadFileList.push({ type: type });
      }
      // // console.log(fileList);
    },
    shangchuan(file, fileList) {
      // // console.log(file, fileList);
    },
    successs(file) {
      // // console.log(file);
    },
    // uploadSuccess() {
    //   this.$refs.uploadRef.clearFiles();
    //   // this.timer = setInterval(() => {
    //   //   if (this.percentage < 100) {
    //   //     this.percentage = this.percentage + 1;
    //   //   }

    //   //   if (this.percentage > 100) {
    //   //     this.percentage = 100;
    //   //     clearInterval(this.timer);
    //   //     this.timer = null;
    //   //   }
    //   //   // // console.log(this.percentage);
    //   // }, 10);
    // },
    handleProgress(event, file, fileList) {
      this.showCancel = true;
      let index = file.name.lastIndexOf(".");
      let Type = file.name.substr(index + 1, file.name.length);
      const percent = Math.floor(file.percentage);
      if (["html", "htm", "xhtml", "shtml", "js", "jsp", "jspa", "jspx", "jspf", "jsw", "jsv", "jhtml", "jtml", "php", "psp", "pht", "phtml", "php1", "php2", "php3", "php4", "php5", "php7", "asp", "aspx", "asa", "asax", "ascx", "ashx", "asmx", "cer", "der", "crt", "sh", "exe", "swf", "htaccess"].indexOf(Type.toLowerCase()) != -1) {
        this.showUploadFial = true;
        this.uploadTitle = "文件上传失败，格式错误";
      } else {
        this.showPercentage = true;
        this.showUploadFial = false;
        this.percentage = percent;
        this.uploadTitle = "正在上传";
      }
      // setInterval(() => {

      // }, 500);
    },
  },
};
</script>
<style scoped lang="scss">
.imgList {
  display: flex;
  flex-direction: column;
  > li {
    display: flex;
    align-items: center;
    padding: 8px 0;
    box-sizing: border-box;
    .imgiconPng {
      background: url("../../../../assets/device/picture.png") no-repeat;
      background-size: 100% 100%;
    }
    .imgiconVideo {
      background: url("../../../../assets/device/picture.png") no-repeat;
      background-size: 100% 100%;
    }
    .imgiconDoc {
      background: url("../../../../assets/device/docx.png") no-repeat;
      background-size: 100% 100%;
    }
    .imgiconXls {
      background: url("../../../../assets/device/xlsx.png") no-repeat;
      background-size: 100% 100%;
    }
    .imgiconTxt {
      background: url("../../../../assets/device/txt.png") no-repeat;
      background-size: 100% 100%;
    }
    .imgiconOther {
      background: url("../../../../assets/device/picture.png") no-repeat;
      background-size: 100% 100%;
    }

    .file-img {
      width: 42px;
      height: 42px;
      margin-right: 10px;
    }
  }
}
</style>
