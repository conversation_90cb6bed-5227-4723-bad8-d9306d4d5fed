<template>
  <el-scrollbar :height="height" class="device-list">
    <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
      <pageTemplate v-model:currentPage="state.page" v-model:pageSize="state.size" :total="state.total" :height="height - 40" :show-paging="true" @size-change="handleCommand(command.Request)" @current-change="handleCommand(command.Request)">
        <template #left>
          <div>
            <el-switch v-model="state.search.active" @change="activeChange" inline-prompt :active-text="i18n.t('devicesList.Active devices')" :inactive-text="i18n.t('devicesList.All devices')" />
          </div>
          <!-- <el-input v-model="state.search.keyword" :placeholder="$t('glob.Please input field', { field: $t('glob.Keyword') })" @keyup.enter="handleCommand(command.Request)">
            <template #append>
              <el-button :icon="Search" @click.stop="handleCommand(command.Request)" />
            </template>
          </el-input> -->
        </template>
        <template #right>
          <span class="tw-mx-[12px] tw-h-fit">
            <el-input v-model="state.search.fullQuery" @keyup.enter="handleCommand(command.Request)" :placeholder="i18n.t('devicesList.Search devices')" class="input-with-select">
              <template #append>
                <el-button :icon="Search" @click="handleCommand(command.Request)" />
              </template>
            </el-input>
          </span>
          <el-button v-if="userInfo.hasPermission(资产管理中心_设备_新增)" :disabled="state.loading" type="primary" :icon="Plus" @click="handleCommand(command.Create, {})">{{ $t("devicesList.New Device") }}</el-button>

          <el-button v-if="userInfo.hasPermission(监控管理中心_设备_可读)" :disabled="state.loading" type="primary" :icon="Download" @click="handleCommand(command.Export)">{{ $t("glob.Export") }}</el-button>

          <el-button v-if="userInfo.hasPermission(监控管理中心_设备_可读)" type="primary" :disabled="state.loading" @click="routerV6NetworkAll()">{{ $t("devicesList.Status") }}</el-button>

          <el-button v-if="userInfo.hasPermission(监控管理中心_设备_可读)" type="primary" :disabled="state.loading" @click="routerV6Busines('')">{{ $t("devicesList.Statistics") }}</el-button>

          <el-button :disabled="state.loading" type="default" :icon="Refresh" @click="handleCommand(command.Refresh)"></el-button>
        </template>
        <template #default="{ height: tableHeight }">
          <el-table v-loading="state.loading" ref="tableRef" :data="dataList" row-key="id" :height="tableHeight" :expand-row-keys="state.expand" :current-row-key="state.current" :row-class-name="({ row }) => ((row as DataItem).active ? '' : 'warning-row')" style="width: 100%" @expand-change="handleExpand">
            <TableColumn type="expand" :width="45" :list="<DataItem[]>[]">
              <template #default="{ row, expanded }">
                <ModelExpand
                  v-if="row.id && expanded"
                  :key="row.id"
                  :id="row.id"
                  type="resource"
                  :create="{
                    contact: !userInfo.hasPermission(资产管理中心_设备_分配联系人),
                  }"
                  :viewer="{
                    contact: !userInfo.hasPermission(资产管理中心_联系人_查看联系人),
                  }"
                  :remove="{
                    contact: !userInfo.hasPermission(资产管理中心_设备_分配联系人),
                  }"
                ></ModelExpand>
              </template>
            </TableColumn>
            <TableColumn prop="deviceName" :list="<DataItem[]>[]" type="deviceName" :label="i18n.t('devicesList.Device')" :width="180" show-filter v-model:many-filtered-value="searchByDevicesOrIps" @filter-change="queryData()">
              <template #default="{ row }">
                <div>
                  <div v-if="row.id" style="cursor: pointer" @click="openDevice(row)">
                    <!-- <router-link :to="{ name: '509596457372745728', params: { id: row.id || '' }, query: { fallback: $route.name as string,tenant:row.tenantId } } " target="_blank" tag="a"> -->
                    <el-text :type="currentDeviceId === row.id ? 'danger' : 'primary'" tag="b" @click="() => (currentDeviceId = row.id)">{{ row.name }}</el-text>
                    <!-- </router-link> -->
                  </div>
                  <div v-if="row.id" style="cursor: pointer" @click="openDevice(row)">
                    <!-- <router-link :to="{ name: '509596457372745728', params: { id: row.id || '' }, query: { fallback: $route.name as string,tenant:row.tenantId } } " target="_blank" tag="a"> -->
                    <el-text type="info">{{ row.config.ipAddress }}</el-text>
                    <!-- </router-link> -->
                  </div>
                  <div v-if="row.id && !row.active" style="color: red">{{ $t("devicesList.Not Active") }}</div>

                  <div v-if="row.id && row.tags instanceof Array && row.tags.length">
                    <!-- {{ (column, row) }} -->
                    <!-- <template v-if="(row[column.property] || []).length"> -->
                    <!-- <el-tag v-for="(item, index) in row.tags" :key="`${row.id}_${index}`" class="tw-mx-[2px] tw-max-w-full tw-align-bottom" size="small" :title="item">{{ item }}</el-tag> -->
                    <!-- </template> -->

                    <el-text type="info">Tag: {{ (row.tags instanceof Array ? row.tags : []).join(",") }} </el-text>
                  </div>
                  <div style="cursor: pointer; display: flex">
                    <template v-if="userInfo.hasPermission(资产管理中心_设备_工具权限)">
                      <span class="tw-h-fit">
                        <el-button v-if="row.showDesktop" type="primary" link :disabled="!row.allowTypes.length" @click="oepnVnc(row)">
                          <Icon class="tw-mx-[2px]" name="local-DeviceMac-line" v-preventReClick :color="row.active && row.allowTypes.length ? 'var(--el-color-primary)' : '#888'"></Icon>
                          <!-- <deviceTools :item="row" :list="deskTopObj.length" :show="row.showDesktop" :active="row.active"></deviceTools> -->
                        </el-button>
                      </span>
                      <span class="tw-h-fit">
                        <el-button type="primary" link @click="ping(row)">
                          <Icon class="tw-mx-[2px]" name="local-DeviceWifi-line" :color="row.active ? 'var(--el-color-primary)' : '#888'"></Icon>
                        </el-button>
                      </span>
                      <span class="tw-h-fit">
                        <el-button type="primary" link @click="routeDevice(row)">
                          <Icon class="tw-mx-[2px]" name="local-SystemShare-line" :color="row.active ? 'var(--el-color-primary)' : '#888'"></Icon>
                        </el-button>
                      </span>

                      <span class="tw-h-fit">
                        <el-button type="primary" link @click="routerV6Busines(row.name)">
                          <Icon class="tw-mx-[2px]" name="local-DocumentNumbers-line" color="var(--el-color-primary)"></Icon>
                        </el-button>
                      </span>
                    </template>

                    <span class="tw-h-fit">
                      <el-button type="primary" link @click="preview(row.id)">
                        <Icon class="tw-mx-[2px]" name="local-SystemError-warning-line" color="var(--el-color-primary)"></Icon>
                      </el-button>
                    </span>

                    <span class="tw-h-fit">
                      <el-button type="primary" link @click="contancts(row.id)">
                        <Icon :disabled="true" class="tw-mx-[2px]" name="local-UserUser-3-line" color="var(--el-color-primary)"></Icon>
                      </el-button>
                    </span>
                  </div>
                </div>
              </template>
            </TableColumn>
            <!-- <TableColumn prop="tags" label="是否激活" :width="80">
              <template #default="{ row, column }">
                <el-text :type="row.active == true ? 'success' : 'danger'">{{ row.active == true ? "是" : "否" }}</el-text>
              </template>
            </TableColumn> -->
            <!-- <TableColumn prop="tags" label="标签" :width="80">
              <template #default="{ row, column }">
                <template v-if="(row[column.property] || []).length">
                  <el-tag v-for="(item, index) in row[column.property]" :key="`${row.id}_${index}`" class="tw-mx-[2px] tw-max-w-full tw-align-bottom" type="" size="small" :title="item">{{ item }}</el-tag>
                </template>
                <template v-else>
                  <span>--</span>
                </template>
              </template>
            </TableColumn> -->
            <!-- <TableColumn prop="alertClassifications" label="告警分类" :min-width="80">
              <template #default="{ row, column }">
                <template v-if="(row[column.property] || []).length">
                  <el-tag v-for="item in row[column.property]" :key="`${row.id}_${item.id}`" class="tw-mx-[2px] tw-max-w-full tw-align-bottom" type="" size="small">{{ item.name }}</el-tag>
                </template>
                <template v-else>
                  <span>--</span>
                </template>
              </template>
            </TableColumn> -->
            <!-- <TableColumn prop="typeIds" label="服务包">
              <template #default="">
                {{ "--" }}
              </template>
            </TableColumn> -->
            <TableColumn prop="monitorSources" :label="i18n.t('devicesList.monitoring source')">
              <template #default="{ row, column }">
                <template v-if="(row[column.property] || []).length">
                  <el-tag v-for="item in row[column.property]" :key="`${row.id}_${item}`" class="tw-mx-[2px] tw-max-w-full tw-align-bottom" type="" size="small">{{ item }}</el-tag>
                </template>
                <template v-else>
                  <span>--</span>
                </template>
              </template>
            </TableColumn>
            <TableColumn prop="locationDesc" :list="<DataItem[]>[]" :label="i18n.t('devicesList.Location')" type="locationDesc" show-filter v-model:many-filtered-value="searchByLocations" @filter-change="queryData()" width="150">
              <template #default="{ row }: { row: DataItem }">
                <!-- <div>{{ row.regionDesc }}</div>
                <div>{{ row.locationDesc }}</div> -->
                <p v-for="(str, idx) in (row as any).locationDescStrArr" :key="`${row.id}-region-${idx}`" :class="`${!idx || idx === (row as any).locationDescStrArr.length - 1 ? 'tw-text-base	' : 'tw-text-sm'}`">{{ str }}</p>
              </template>
            </TableColumn>
            <TableColumn prop="importance" :list="<DataItem[]>[]" :label="i18n.t('devicesList.NMS settings')" type="deviceMessage" show-filter v-model:many-filtered-value="searchByNmsSettings" @filter-change="queryData()">
              <template #default="{ row, column }: { row: DataItem }">
                <div class="network-settings">
                  <p v-show="!JSON.parse(row.config.nmsTicketing)">
                    <svg t="1704279973051" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1475" width="16" height="16">
                      <path d="M512 85.333333c235.648 0 426.666667 191.018667 426.666667 426.666667s-191.018667 426.666667-426.666667 426.666667S85.333333 747.648 85.333333 512 276.352 85.333333 512 85.333333zM170.666667 512a341.333333 341.333333 0 0 0 550.016 270.08L241.92 303.36A339.84 339.84 0 0 0 170.666667 512z m341.333333-341.333333a339.882667 339.882667 0 0 0-209.877333 72.106666L781.226667 721.92A341.333333 341.333333 0 0 0 512 170.666667z" fill="#000000" p-id="1476"></path>
                    </svg>

                    {{ $t("devicesList.Event function") }}
                  </p>
                  <p v-show="!JSON.parse(row.config.ackRequired)">
                    <svg t="1704279973051" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1475" width="16" height="16">
                      <path d="M512 85.333333c235.648 0 426.666667 191.018667 426.666667 426.666667s-191.018667 426.666667-426.666667 426.666667S85.333333 747.648 85.333333 512 276.352 85.333333 512 85.333333zM170.666667 512a341.333333 341.333333 0 0 0 550.016 270.08L241.92 303.36A339.84 339.84 0 0 0 170.666667 512z m341.333333-341.333333a339.882667 339.882667 0 0 0-209.877333 72.106666L781.226667 721.92A341.333333 341.333333 0 0 0 512 170.666667z" fill="#000000" p-id="1476"></path>
                    </svg>
                    {{ $t("devicesList.Caution function") }}
                  </p>
                  <p v-show="!row.delivery">
                    <svg t="1704279973051" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1475" width="16" height="16">
                      <path d="M512 85.333333c235.648 0 426.666667 191.018667 426.666667 426.666667s-191.018667 426.666667-426.666667 426.666667S85.333333 747.648 85.333333 512 276.352 85.333333 512 85.333333zM170.666667 512a341.333333 341.333333 0 0 0 550.016 270.08L241.92 303.36A339.84 339.84 0 0 0 170.666667 512z m341.333333-341.333333a339.882667 339.882667 0 0 0-209.877333 72.106666L781.226667 721.92A341.333333 341.333333 0 0 0 512 170.666667z" fill="#000000" p-id="1476"></path>
                    </svg>
                    {{ $t("devicesList.Deliver") }}
                  </p>
                  <p v-show="row.config.connectAuthType">
                    <Icon class="tw-mx-[2px]" name="local-DeviceMac-line" color="#000"></Icon>
                    Require {{ row.config.connectAuthType }}
                  </p>

                  <div>
                    {{ $t("devicesList.Importance") }}:<el-text type="primary">{{ (find(deviceImportanceOption, (v) => v.value === row.importance) || { label: row.importance }).label }}</el-text>
                  </div>
                  <div>
                    {{ $t("devicesList.Online Time") }}:<el-text :type="(remoteOnline.get(row.id) || { resourceId: row.id, online: false }).online ? 'success' : 'danger'">{{ (remoteOnline.get(row.id) || { resourceId: row.id, online: false }).online ? $t("glob.Yes") : $t("glob.No") }}</el-text>
                  </div>
                  <!-- <div>
                    是否激活:<el-text :type="row.active == true ? 'success' : 'danger'">{{ row.active == true ? "是" : "否" }}</el-text>
                  </div> -->
                </div>

                <div v-if="userInfo.hasPermission(服务管理中心_告警分类_可读)">
                  <template v-if="(row.alertClassifications || []).length">
                    <el-tag v-for="item in filterByAlarmList(alarmList, row.alertClassifications)" :key="`${row.id}_${item.id}`" class="tw-mx-[2px] tw-max-w-full tw-align-bottom" size="small">{{ item.name }}</el-tag>
                  </template>
                </div>
              </template>
            </TableColumn>
            <TableColumn :label="i18n.t('devicesList.Device Information')" :list="<DataItem[]>[]" type="deviceMessage" show-filter v-model:many-filtered-value="searchByDeviceBasicInfo" @filter-change="queryData()">
              <template #default="{ row }">
                <ul class="basicInfo">
                  <li
                    v-if="
                      row?.serviceNumbers
                        ?.map((v) => v.number)
                        .filter((v) => v)
                        .join()
                    "
                  >
                    <span>{{ $t("devicesList.Service Number") }}</span>
                    <span v-if="userInfo.hasPermission(资产管理中心_服务编号_可读)" :class="!row.showServiceNumber ? 'hidden' : ''" @click="() => (row.showServiceNumber = !row.showServiceNumber)">
                      <el-tooltip class="box-item" effect="dark" placement="top-start">
                        <template #content>
                          <p style="max-width: 800px">
                            {{
                              row?.serviceNumbers
                                ?.map((v) => v.number)
                                .filter((v) => v)
                                .join()
                            }}
                          </p>
                        </template>
                        <el-text :truncated="!row.showServiceNumber" class="tw-mx-1 tw-align-middle">{{
                          row?.serviceNumbers
                            ?.map((v) => v.number)
                            .filter((v) => v)
                            .join()
                        }}</el-text>
                      </el-tooltip>
                    </span>
                    <!-- <span class="show" v-show="row.showServiceNumber === true">
                      <el-tooltip class="box-item" effect="dark" placement="top-start">
                        <template #content>
                          <p style="max-width: 800px">{{ row?.serviceNumbers?.map((v) => v.number).join() }}</p>
                        </template>
                        {{ row?.serviceNumbers?.map((v) => v.number).join() }}
                      </el-tooltip>
                    </span> -->
                  </li>
                  <li v-if="discovery.has(row.id)">
                    <span>{{ $t("devicesList.Device Model") }}</span>
                    <span :class="!row.showEquipmentModel ? 'hidden' : ''" @click="() => (row.showEquipmentModel = !row.showEquipmentModel)">
                      <el-tooltip class="box-item" effect="dark" placement="top-start">
                        <template #content>
                          <p style="max-width: 800px">
                            <span v-if="discovery.has(row.id)">{{ discovery.get(row.id)!.serialNumber }}</span>
                          </p>
                        </template>
                        <el-text :truncated="!row.showSerialNumber" class="tw-mx-1 tw-align-middle">
                          <span v-if="discovery.has(row.id)">{{ discovery.get(row.id)!.serialNumber }}</span>
                        </el-text>
                      </el-tooltip>
                    </span>
                  </li>
                  <li v-if="discovery.has(row.id)">
                    <span>{{ $t("devicesList.Serial number") }}</span>
                    <span :class="!row.showSerialNumber ? 'hidden' : ''" @click="() => (row.showSerialNumber = !row.showSerialNumber)">
                      <el-tooltip class="box-item" effect="dark" placement="top-start">
                        <template #content>
                          <p style="max-width: 800px">
                            <span v-if="discovery.has(row.id)">{{ discovery.get(row.id)!.modelName }}</span>
                          </p>
                        </template>
                        <el-text :truncated="!row.showSerialNumber" class="tw-mx-1 tw-align-middle">
                          <span v-if="discovery.has(row.id)">{{ discovery.get(row.id)!.modelName }}</span>
                        </el-text>
                      </el-tooltip>
                    </span>

                    <!-- <span class="show" v-for="item in discovery" :key="item.resourceId" v-show="item.resourceId == row.id && item.showModeSize == true">
                      <el-tooltip class="box-item" effect="dark" placement="top-start">
                        <template #content>
                          <p style="max-width: 800px">{{ item.modelName }}</p>
                        </template>
                        {{ item.modelName }}
                      </el-tooltip>
                    </span> -->
                  </li>
                  <li v-if="row.config?.assetNumbers.map((v) => v).join()">
                    <span>{{ $t("devicesList.Asset number") }}</span>
                    <!-- <span class="property-size">
                      {{ (row as DataItem).config.assetNumbers.map((v) => v).join() }}
                    </span> -->
                    <span :class="!row.showAssetNumber ? 'hidden' : ''" @click="() => (row.showAssetNumber = !row.showAssetNumber)">
                      <el-tooltip class="box-item" effect="dark" placement="top-start">
                        <template #content>
                          <p style="max-width: 800px">
                            {{ row.config?.assetNumbers.map((v) => v).join() }}
                          </p>
                        </template>
                        <el-text :truncated="!row.showAssetNumber" class="tw-mx-1 tw-align-middle"> {{ row.config?.assetNumbers.map((v) => v).join() }}</el-text>
                      </el-tooltip>
                    </span>
                    <!-- <span class="show" v-show="showProperty === true">
                      <el-tooltip class="box-item" effect="dark" placement="top-start">
                        <template #content>
                          <p style="max-width: 800px">{{ row.config?.assetNumbers.map((v) => v).join() }}</p>
                        </template>
                        {{ row.config?.assetNumbers.map((v) => v).join() }}
                      </el-tooltip>
                    </span> -->
                  </li>

                  <li v-if="(row.vendors || []).map((v) => v.name).join()">
                    <span>{{ $t("devicesList.Device supplier") }}</span>
                    <!-- <span class="property-size">
                      {{ (row as DataItem).config.assetNumbers.map((v) => v).join() }}
                    </span> -->
                    <span v-if="userInfo.hasPermission(资产管理中心_设备供应商_可读)" :class="!row.showVendor ? 'hidden' : ''" @click="() => (row.showVendor = !row.showVendor)">
                      <el-tooltip class="box-item" effect="dark" placement="top-start">
                        <template #content>
                          <p style="max-width: 800px" v-for="item in filterByAlarmList(vendorList, row.vendors)" :key="`${row.id}_${item.id}`">
                            {{ item.name }}
                          </p>
                        </template>
                        <el-text :truncated="!row.showVendor" class="tw-mx-1 tw-align-middle">
                          <span v-for="item in filterByAlarmList(vendorList, row.vendors)" :key="`${row.id}_${item.id}`">
                            {{ item.name }}
                          </span>
                        </el-text>
                      </el-tooltip>
                    </span>
                    <!-- <span class="show" v-show="showProperty === true">
                      <el-tooltip class="box-item" effect="dark" placement="top-start">
                        <template #content>
                          <p style="max-width: 800px">{{ (row.vendors || []).map((v) => v.name).join() }}</p>
                        </template>
                        {{ (row.vendors || []).map((v) => v.name).join() }}
                      </el-tooltip>
                    </span> -->
                  </li>
                  <li v-if="(row.resourceTypes || []).map((v) => v.name).join()">
                    <span>{{ $t("devicesList.Device type") }}</span>
                    <!-- <span class="property-size">
                      {{ (row as DataItem).config.assetNumbers.map((v) => v).join() }}
                    </span> -->
                    <span v-if="userInfo.hasPermission(资产管理中心_设备类型_可读)" :class="!row.showDeviceType ? 'hidden' : ''" @click="() => (row.showDeviceType = !row.showDeviceType)">
                      <el-tooltip class="box-item" effect="dark" placement="top-start">
                        <template #content>
                          <p style="max-width: 800px" v-for="item in filterByAlarmList(deviceTypeList, row.resourceTypes)" :key="`${row.id}_${item.id}`">
                            {{ item.name }}
                          </p>
                        </template>
                        <el-text :truncated="!row.showDeviceType" class="tw-mx-1 tw-align-middle">
                          <span v-for="item in filterByAlarmList(deviceTypeList, row.resourceTypes)" :key="`${row.id}_${item.id}`">
                            {{ item.name }}
                          </span>
                        </el-text>
                      </el-tooltip>
                    </span>
                    <!-- <span class="show" v-show="showProperty === true">
                      <el-tooltip class="box-item" effect="dark" placement="top-start">
                        <template #content>
                          <p style="max-width: 800px">{{ (row.resourceTypes || []).map((v) => v.name).join() }}</p>
                        </template>
                        {{ (row.resourceTypes || []).map((v) => v.name).join() }}
                      </el-tooltip>
                    </span> -->
                  </li>
                  <li v-if="(row.groups || []).map((v) => v.name).join()">
                    <span>{{ $t("devicesList.Device group") }}</span>
                    <!-- <span class="property-size">
                      {{ (row as DataItem).config.assetNumbers.map((v) => v).join() }}
                    </span> -->
                    <span v-if="userInfo.hasPermission(资产管理中心_设备分组_可读)" :class="!row.showDeviceGroup ? 'hidden' : ''" @click="() => (row.showDeviceGroup = !row.showDeviceGroup)">
                      <el-tooltip class="box-item" effect="dark" placement="top-start">
                        <template #content>
                          <p style="max-width: 800px" v-for="item in filterByAlarmList(deviceGroupList, row.groups)" :key="`${row.id}_${item.id}`">
                            {{ item.name }}
                          </p>
                        </template>
                        <el-text :truncated="!row.showDeviceType" class="tw-mx-1 tw-align-middle">
                          <span v-for="item in filterByAlarmList(deviceGroupList, row.groups)" :key="`${row.id}_${item.id}`">
                            {{ item.name }}
                          </span>
                        </el-text>
                      </el-tooltip>
                    </span>
                    <!-- <span class="show" v-show="showProperty === true">
                      <el-tooltip class="box-item" effect="dark" placement="top-start">
                        <template #content>
                          <p style="max-width: 800px">{{ (row.groups || []).map((v) => v.name).join() }}</p>
                        </template>
                        {{ (row.groups || []).map((v) => v.name).join() }}
                      </el-tooltip>
                    </span> -->
                  </li>
                  <li v-if="row.unit">
                    <span>{{ $t("devicesList.Business Unit") }}</span>
                    <!-- <span class="property-size">
                      {{ (row as DataItem).config.assetNumbers.map((v) => v).join() }}
                    </span> -->
                    <span :class="!row.showUnit ? 'hidden' : ''" @click="() => (row.showUnit = !row.showUnit)">
                      <el-tooltip class="box-item" effect="dark" placement="top-start">
                        <template #content>
                          <p style="max-width: 800px">{{ row.unit }}</p>
                        </template>
                        <el-text :truncated="!row.showUnit" class="tw-mx-1 tw-align-middle"> {{ row.unit }}</el-text>
                      </el-tooltip>
                    </span>
                    <!-- <span class="show" v-show="showProperty === true">
                      <el-tooltip class="box-item" effect="dark" placement="top-start">
                        <template #content>
                          <p style="max-width: 800px">{{ row.config?.assetNumbers.map((v) => v).join() }}</p>
                        </template>
                        {{ row.config?.assetNumbers.map((v) => v).join() }}
                      </el-tooltip>
                    </span> -->
                  </li>
                </ul>
              </template>
            </TableColumn>

            <TableColumn type="condition" :prop="`id`" :label="`ID`" :showOverflowTooltip="true" width="180"></TableColumn>

            <!-- <TableColumn prop="vendors" label="供应商">
              <template #default="{ row, column }">
                <span v-if="!row[column.property].length" class="tw-text-[var(--el-disabled-text-color)]">--</span>
                <el-tag v-for="item in row[column.property]" :key="item.id" class="tw-mx-[2px] tw-max-w-full tw-align-bottom" type="" size="small">{{ item.name }}</el-tag>
              </template>
            </TableColumn> -->

            <TableColumn :list="<DataItem[]>[]" :label="$t('glob.operate')" align="left" :width="170">
              <template #default="{ row }">
                <span>
                  <el-link v-if="row.hasPermissionIds.includes(资产管理中心_设备_编辑)" :disabled="state.loading" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleCommand(command.Modify, row)">{{ $t("glob.edit") }}</el-link>
                </span>
                <!-- <span>
                  <el-link :v-if="!userInfo.hasPermission(资产管理中心_设备_删除)" :disabled="state.loading" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleCommand(command.Delete, row)">{{ row.active ? "下线" : "上线" }}</el-link>
                </span> -->
                <el-dropdown trigger="click" @command="$event.callback(row)" style="vertical-align: middle">
                  <el-link type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle">{{ $t("glob.More") }}</el-link>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{ callback: () => openModel(row) }" class="tw-text-[var(--el-color-danger)]">
                        <el-link type="primary" :underline="false">{{ $t("devicesList.Simulate alarm") }}</el-link>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>

                <span>
                  <!-- 设备管理('603899668597833728') -->
                  <el-link :type="row.hasPermissionIds.includes(资产管理中心_设备_安全) ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="row.hasPermissionIds.includes(资产管理中心_设备_安全) ? state.loading : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
                </span>
              </template>
            </TableColumn>
          </el-table>
        </template>
      </pageTemplate>
    </el-card>
    <Editor ref="editorRef" :title="i18n.t('devicesList.Device')" display="dialog">
      <template #deleteItem="{ params }">
        <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
          <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
          <p class="title">{{ $t("glob.Confirm") }} {{ params.active ? $t("glob.online") : $t("glob.Offline") }} {{ params.name }} {{ props.title }}{{ $t("glob.what") }}？</p>
        </div>
      </template>
    </Editor>
    <modelAlarm ref="modelAlarmRef"></modelAlarm>
    <deviceDetials ref="deviceDetialsRef"></deviceDetials>
    <deviceContacts ref="deviceContactsRef"></deviceContacts>
    <devicePing ref="devicePingRef"></devicePing>
    <deviceRoute ref="deviceRouteRef"></deviceRoute>
    <noVnc ref="noVncRef"></noVnc>
  </el-scrollbar>
</template>

<script setup lang="ts" generic="T extends object">
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete, Refresh, MoreFilled, Download, UserFilled, Postcard, InfoFilled } from "@element-plus/icons-vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import pageTemplate from "@/components/pageTemplate.vue";
import { ElTable } from "element-plus";
import Editor from "./Editor.vue";
import treeAuth from "@/components/treeAuth/index.vue";

import modelAlarm from "./modelAlarm.vue";
import ModelExpand from "@/views/pages/modelExpand/Model.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox } from "element-plus";
import getUserInfo from "@/utils/getUserInfo";
import { routerV6, routerV6Busines, routerV6NetworkAll } from "@/views/pages/common/routeV6";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
import { getDiscoveryByResources, getRemoteDesktopByResources, getRemoteOnlineByResources, type ResourcesItem as DataItem } from "@/views/pages/apis/device";
import { getDeviceList as getItemList, LoginMode } from "@/views/pages/apis/device";
import { filter, find, isArray } from "lodash-es";
import { addDeviceData as addItemData, modDeviceData as setItemData, modDeviceData as modItemData, delDeviceData as delItemData, extDeviceList as extItemList } from "@/views/pages/apis/device";
import { desktop, getDeviceDiscovery } from "@/views/pages/apis/device";
import { deviceImportance, deviceImportanceOption } from "@/views/pages/apis/device";
import { getRegionsTenantCurrent } from "@/views/pages/apis/regionManage";
import deviceDetials from "@/components/deviceTool/deviceDetials.vue";
import deviceContacts from "@/components/deviceTool/deviceContacts.vue";
import devicePing from "@/components/deviceTool/ping.vue";
import deviceRoute from "@/components/deviceTool/tracerRoute.vue";
import noVnc from "./noVnc.vue";

import { getAlertClassificationsList, getVendorsList, getResourceTypeList, getDeviceGroupList } from "@/views/pages/apis/device";
import deviceTools from "@/components/deviceTool/index.vue";

import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";
import { 监控管理中心_设备_可读, 资产管理中心_设备_可读, 资产管理中心_设备_新增, 资产管理中心_设备_分配联系人, 资产管理中心_设备_工具权限, 资产管理中心_设备_安全, 资产管理中心_设备_分配设备分组, 资产管理中心_设备_分配设备类型, 资产管理中心_设备_分配设备供应商, 资产管理中心_设备_分配行动策略, 资产管理中心_设备_查看设备网络状态, 资产管理中心_设备_查看设备统计图, 资产管理中心_设备_编辑, 资产管理中心_联系人_可读, 资产管理中心_联系人_查看联系人, 服务管理中心_告警分类_可读, 资产管理中心_设备供应商_可读, 资产管理中心_设备类型_可读, 资产管理中心_设备分组_可读, 资产管理中心_服务编号_可读 } from "@/views/pages/permission";

import validPwd from "@/views/pages/alarm_convergence/PropertyManage/passwordWallet/validPwd";

import { getMFAMethods, MFAMethod } from "@/api/system";
import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";

/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "global" });
const route = useRoute();
const router = useRouter();
const userInfo = getUserInfo();
const containerId = ref("");
const dialogVisibleshow = ref(false);

const $filter0 = ref(exoprtMatch1);
defineOptions({ name: "alarmBoard" });
const treeStyle = ref({
  pointerEvents: "none",
});
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */

const width = inject("width", ref(0));
const height = inject("height", ref(0));

interface Props {
  title?: string;
}

const props = withDefaults(defineProps<Props>(), { title: "设备" });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

const tableRef = ref<InstanceType<typeof ElTable>>();
const editorRef = ref<InstanceType<typeof Editor>>();
const modelAlarmRef = ref<InstanceType<typeof modelAlarm>>();
const deviceDetialsRef = ref<InstanceType<typeof deviceDetials>>();
const deviceContactsRef = ref<InstanceType<typeof deviceContacts>>();
const deviceRouteRef = ref<InstanceType<typeof deviceRoute>>();
const devicePingRef = ref<InstanceType<typeof devicePing>>();
const noVncRef = ref<InstanceType<typeof noVnc>>();

function routeDevice(props) {
  deviceRouteRef.value.open(props);
}

// function showSecurityTree(row) {
//   containerId.value = row.containerId;

//   dialogVisibleshow.value = true;
// }
async function oepnVnc(row) {
  try {
    if (row.config.connectAuthType === LoginMode.ON_CONNECT) {
      const { success: validSuccess } = (await validPwd(false)) as any;
      if (!validSuccess) return;
    } else if (row.config.connectAuthType === LoginMode.REQUIRE_TFA) {
      const { data, message, success } = await getMFAMethods({});
      if (!success) throw new Error(message);
      const isMfa = data.includes(MFAMethod.TOTP);
      if (!isMfa) throw new Error("请开启双因素认证");
      const { success: validSuccess } = (await validPwd(isMfa)) as any;
      if (!validSuccess) return;
    }
    noVncRef.value.device = { ...row };
    noVncRef.value.title = `${i18n.t("devicesList.connection to")} ` + row.name;
    noVncRef.value.dialogVisible = true;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
  // console.log(noVncRef.value.device)
}
function openDevice(props) {
  userInfo.cutTenant(((userInfo.currentTenant || {}).id || props.tenantId) as string).finally(async () => {
    const { href } = router.resolve({
      name: "509596457372745728",
      params: { id: props.id },
      query: {
        // fallback: route.name,
        tenant: props.tenantId,
      },
    });

    const link = document.createElement("a");
    link.href = href;
    link.target = "_blank"; // 强制新窗口
    link.rel = "noopener noreferrer"; // 安全防护
    link.style.opacity = "0";
    link.style.position = "absolute";
    link.style.pointerEvents = "none";
    document.body.appendChild(link);
    link.click();
    setTimeout(() => {
      document.body.removeChild(link);
    }, 100);
  });
}
async function ping(props: any) {
  devicePingRef.value.open(props);
  // pingTo({ id: "523721061548687360" }).then((res) => {
  //   // console.log(res);
  // });
  // deviceId.value = row.id;
  // deviceContactsRef.value.dialogFormVisible = true;
}

const showProperty = ref(false);
const showService = ref(false);
// const tableRowClassName = ({ row, rowIndex }: { row: User; rowIndex: number }) => {
//   if (!row.active) {
//     return "warning-row";
//   }
//   return "";
// };

function contancts(id) {
  // deviceId.value = id;
  deviceContactsRef.value.dialogFormVisible = true;
  // deviceContactsRef.value.id = id;
  deviceContactsRef.value.open(id);
}
function preview(id) {
  // deviceId.value = id;
  deviceDetialsRef.value.open(id);
  deviceDetialsRef.value.dialogFormVisible = true;
}
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */
type DeviceGroupList = Record<"code" | "cnName" | "enName", string> & { contact: import("@/views/pages/apis/device").ContactItem[]; value: string[] };
enum command {
  Refresh = "Refresh",
  Request = "Request",
  Preview = "Preview",
  Create = "Create",
  Update = "Update",
  Modify = "Modify",
  Delete = "Delete",
  Export = "Export",
  Contact = "Contact",
}
/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface State<T, P> {
  loading: boolean;
  expand: string[];
  select: string[];
  current: string | undefined;
  search: P;
  list: T[];
  page: number;
  size: number;
  total: number;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const timer = ref<null | number>(null);
const autoRefreshTime = ref(0);

const final = readonly({
  pagination: true,
});

type ShowBasicInfo = Record<"showServiceNumber" | "showEquipmentModel" | "showSerialNumber" | "showAssetNumber" | "showVendor" | "showDeviceType" | "showDeviceGroup" | "showUnit", boolean>;
type SearchParams = Omit<typeof getItemList extends (req: infer P) => any ? P : never, "paging" | "containerId" | "queryPermissionId" | "verifyPermissionIds" | "controller" | "slot">;
const state = reactive<State<DataItem & Partial<ShowBasicInfo>, Required<SearchParams>>>({
  loading: false,
  expand: [],
  select: [],
  current: undefined,
  search: {
    resourcePoolId: "",
    name: "",
    modelIdent: "",
    modelIdents: [],
    regionId: "",
    regionName: "",
    regionIds: [],
    locationId: "",
    groupId: "",
    vendorId: "",
    supportNoteId: "",
    resourceTypeId: "",
    alertClassificationId: "",
    active: true,
    delivery: false,
    ipAddress: "",
    locationFilterRelation: "AND",
    includeLocations: [],
    excludeLocations: [],
    deviceOrIpFilterRelation: "AND",
    includeDevicesOrIps: [],
    excludeDevicesOrIps: [],
    nmsSettingsFilterRelation: "AND",
    includeNmsSettings: [],
    excludeNmsSettings: [],
    deviceBasicInfoFilterRelation: "AND",
    includeDeviceBasicInfo: [],
    excludeDeviceBasicInfo: [],
    fullQuery: "",
  },
  list: [],
  page: 1,
  size: 50,
  total: 0,
});

const currentDeviceId = ref<string>(""); // 选中设备ID

const searchType0ByDevicesOrIps = ref<"include" | "exclude">("include");
const searchType1ByDevicesOrIps = ref<"include" | "exclude">("include");
const searchByDevicesOrIps = computed({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByDevicesOrIps) === "include") value0 = state.search.includeDevicesOrIps[0] || "";
    if (toValue(searchType0ByDevicesOrIps) === "exclude") value0 = state.search.excludeDevicesOrIps[0] || "";
    let value1 = "";
    if (toValue(searchType1ByDevicesOrIps) === "include") value1 = state.search.includeDevicesOrIps[state.search.includeDevicesOrIps.length - 1] || "";
    if (toValue(searchType1ByDevicesOrIps) === "exclude") value1 = state.search.excludeDevicesOrIps[state.search.excludeDevicesOrIps.length - 1] || "";
    return {
      status: toValue(searchType0ByDevicesOrIps),
      type: toValue(searchType1ByDevicesOrIps),
      relation: state.search.deviceOrIpFilterRelation,
      firstName: value0,
      lastName: value1,
    };
  },
  set: (v) => {
    searchType0ByDevicesOrIps.value = v.status;
    searchType1ByDevicesOrIps.value = v.type;
    state.search.deviceOrIpFilterRelation = v.relation;
    state.search.includeDevicesOrIps = [...(v.status === "include" ? [v.firstName] : []), ...(v.type === "include" ? [v.lastName] : [])];
    state.search.excludeDevicesOrIps = [...(v.status === "exclude" ? [v.firstName] : []), ...(v.type === "exclude" ? [v.lastName] : [])];
  },
});
const searchType0ByLocations = ref<"include" | "exclude">("include");
const searchType1ByLocations = ref<"include" | "exclude">("include");
const searchByLocations = computed({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByLocations) === "include") value0 = state.search.includeLocations[0] || "";
    if (toValue(searchType0ByLocations) === "exclude") value0 = state.search.excludeLocations[0] || "";
    let value1 = "";
    if (toValue(searchType1ByLocations) === "include") value1 = state.search.includeLocations[state.search.includeLocations.length - 1] || "";
    if (toValue(searchType1ByLocations) === "exclude") value1 = state.search.excludeLocations[state.search.excludeLocations.length - 1] || "";
    return {
      status: toValue(searchType0ByLocations),
      type: toValue(searchType1ByLocations),
      relation: state.search.locationFilterRelation,
      firstName: value0,
      lastName: value1,
    };
  },
  set: (v) => {
    searchType0ByLocations.value = v.status;
    searchType1ByLocations.value = v.type;
    state.search.locationFilterRelation = v.relation;
    state.search.includeLocations = [...(v.status === "include" ? [v.firstName] : []), ...(v.type === "include" ? [v.lastName] : [])];
    state.search.excludeLocations = [...(v.status === "exclude" ? [v.firstName] : []), ...(v.type === "exclude" ? [v.lastName] : [])];
  },
});
const searchType0ByNmsSettings = ref<"include" | "exclude">("include");
const searchType1ByNmsSettings = ref<"include" | "exclude">("include");
const searchByNmsSettings = computed({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByNmsSettings) === "include") value0 = state.search.includeNmsSettings[0] || "";
    if (toValue(searchType0ByNmsSettings) === "exclude") value0 = state.search.excludeNmsSettings[0] || "";
    let value1 = "";
    if (toValue(searchType1ByNmsSettings) === "include") value1 = state.search.includeNmsSettings[state.search.includeNmsSettings.length - 1] || "";
    if (toValue(searchType1ByNmsSettings) === "exclude") value1 = state.search.excludeNmsSettings[state.search.excludeNmsSettings.length - 1] || "";
    return {
      status: toValue(searchType0ByNmsSettings),
      type: toValue(searchType1ByNmsSettings),
      relation: state.search.nmsSettingsFilterRelation,
      firstName: value0,
      lastName: value1,
    };
  },
  set: (v) => {
    searchType0ByNmsSettings.value = v.status;
    searchType1ByNmsSettings.value = v.type;
    state.search.nmsSettingsFilterRelation = v.relation;
    state.search.includeNmsSettings = [...(v.status === "include" ? [v.firstName] : []), ...(v.type === "include" ? [v.lastName] : [])];
    state.search.excludeNmsSettings = [...(v.status === "exclude" ? [v.firstName] : []), ...(v.type === "exclude" ? [v.lastName] : [])];
  },
});
const searchType0ByDeviceBasicInfo = ref<"include" | "exclude">("include");
const searchType1ByDeviceBasicInfo = ref<"include" | "exclude">("include");
const searchByDeviceBasicInfo = computed({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByDeviceBasicInfo) === "include") value0 = state.search.includeDeviceBasicInfo[0] || "";
    if (toValue(searchType0ByDeviceBasicInfo) === "exclude") value0 = state.search.excludeDeviceBasicInfo[0] || "";
    let value1 = "";
    if (toValue(searchType1ByDeviceBasicInfo) === "include") value1 = state.search.includeDeviceBasicInfo[state.search.includeDeviceBasicInfo.length - 1] || "";
    if (toValue(searchType1ByDeviceBasicInfo) === "exclude") value1 = state.search.excludeDeviceBasicInfo[state.search.excludeDeviceBasicInfo.length - 1] || "";
    return {
      status: toValue(searchType0ByDeviceBasicInfo),
      type: toValue(searchType1ByDeviceBasicInfo),
      relation: state.search.deviceBasicInfoFilterRelation,
      firstName: value0,
      lastName: value1,
    };
  },
  set: (v) => {
    searchType0ByDeviceBasicInfo.value = v.status;
    searchType1ByDeviceBasicInfo.value = v.type;
    state.search.deviceBasicInfoFilterRelation = v.relation;
    state.search.includeDeviceBasicInfo = [...(v.status === "include" ? [v.firstName] : []), ...(v.type === "include" ? [v.lastName] : [])];
    state.search.excludeDeviceBasicInfo = [...(v.status === "exclude" ? [v.firstName] : []), ...(v.type === "exclude" ? [v.lastName] : [])];
  },
});
const dataList = computed(() => state.list);
const deviceId = ref("");
const expand = computed(() => filter<DataItem>(state.list, (row) => state.expand.includes(row.id)));
const select = computed(() => filter<DataItem>(state.list, (row) => state.select.includes(row.id)));
const current = computed(() => find<DataItem>(state.list, (row) => row.id === state.current));

const discovery = ref(new Map<string, (ReturnType<typeof getDiscoveryByResources> extends Promise<{ data: infer P }> ? P : never)[number]>());
const showModeSize = ref(false);
const showSerialSize = ref(false);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  nextTick(async () => {
    try {
      await handleCommand(command.Refresh);
      autoRefreshTime.value = 60;
    } catch (error) {
      /*  */
    }
  });
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {
  if (timer.value !== null) {
    window.clearInterval(timer.value);
    timer.value = null;
  }
}
function beforeDestroy() {
  if (timer.value !== null) {
    window.clearInterval(timer.value);
    timer.value = null;
  }
}
function destroyed() {}

watch(autoRefreshTime, (autoRefreshTime) => {
  if (timer.value !== null) {
    window.clearInterval(timer.value);
    timer.value = null;
  }
  if (autoRefreshTime) timer.value = window.setInterval(queryData, autoRefreshTime * 1000);
});

function openMessage() {
  ElMessage.success(`${i18n.t("devicesList.Please go to NetCare v6 to view device statistics information")}`);
}

async function activeChange() {
  state.loading = true;
  queryData();
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
async function handleCommand(type: command, data?: Record<string, unknown>) {
  const time = autoRefreshTime.value;
  autoRefreshTime.value = 0;
  try {
    await nextTick();
    switch (type) {
      case command.Refresh:
        state.loading = true;
        await resetData();
        await queryData();
        break;
      case command.Request:
        state.loading = true;
        await queryData();
        break;
      case command.Preview:
        await previewItem(data as Record<string, unknown>);
        break;
      case command.Create:
        await createItem(data as Record<string, unknown>);
        state.loading = true;
        await resetData();
        await queryData();
        break;
      case command.Update:
        await rewriteItem(data as Record<string, unknown>);
        state.loading = true;
        await resetData();
        await queryData();
        break;
      case command.Modify:
        await modifyItem(data as Record<string, unknown>);
        state.loading = true;
        // await resetData();
        await queryData();
        break;
      case command.Delete:
        await deleteItem(data as Record<string, unknown>);
        state.loading = true;
        await resetData();
        await queryData();
        break;
      case command.Export: {
        await exportItem();
        break;
      }
    }
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await resetData();
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
      await queryData();
    }
  } finally {
    autoRefreshTime.value = time;
    state.loading = false;
  }
}
async function resetData() {
  state.list = [];
  state.page = 1;
  state.size = 50;
  state.total = 0;
  // for (const key in state.search) {
  //   if (Object.prototype.hasOwnProperty.call(state.search, key)) {
  //     delete state.search[key];
  //   }
  // }
  await nextTick();
}
async function openModel(row: Record<string, unknown>) {
  if (!modelAlarmRef.value) return row;
  await modelAlarmRef.value.open(row);
}

function handleExpand(row: DataItem, expandedRows: DataItem[]) {
  state.expand = expandedRows.map(({ id }) => id);
  if (find(expandedRows, ({ id }) => row.id === id)) {
    /*  */
  } else {
    /*  */
  }
}

// function setFilterMessage(includeData: any, excludeData: any, type: any) {
//   switch (type.status) {
//     case "include":
//       includeData.push(type.firstName);

//       break;
//     case "exclude":
//       excludeData.push(type.firstName);

//       break;
//   }

//   switch (type.type) {
//     case "include":
//       includeData.push(type.lastName);

//       break;
//     case "exclude":
//       excludeData.push(type.lastName);

//       break;
//   }
// }

const deskTopObj = ref(new Map<string, (ReturnType<typeof getRemoteDesktopByResources> extends Promise<{ data: infer P }> ? P : never)[number]>());
const remoteOnline = ref(new Map<string, (ReturnType<typeof getRemoteOnlineByResources> extends Promise<{ data: infer P }> ? P : never)[number]>());
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
async function queryData() {
  const { success, message, data, page, size, total } = await getItemList({ ...state.search, paging: { pageNumber: state.page, pageSize: state.size } });
  state.loading = false;

  // // console.log(contactGroupData);
  if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });

  state.list.splice(
    0,
    state.list.length,
    ...(data instanceof Array ? data : []).map((v) => {
      Object.assign(v.config, { modelNumbers: tryJSON(v.config.modelNumbers) || [], serialNumbers: tryJSON(v.config.serialNumbers) || [], assetNumbers: tryJSON(v.config.assetNumbers) || [], dynamicIp: tryJSON(v.config.dynamicIp) || false, ackRequired: tryJSON(v.config.ackRequired) || false, nmsTicketing: tryJSON(v.config.nmsTicketing) || false });
      return v;
    })
  );

  state.page = Number(page) || 1;
  state.size = Number(size) || 20;
  state.total = Number(total) || 0;

  const { data: regionData, success: regionSuccess, message: regionMessage } = await getRegionsTenantCurrent({});
  if (!regionSuccess) throw Object.assign(new Error(regionMessage), { regionSuccess, regionData });

  const getTreeRegion = (id, res) => {
    const findRegion: any = regionData.find((v) => v.id === id) || {};
    res.unshift(findRegion.name);
    return findRegion.parentId ? getTreeRegion(findRegion.parentId, res) : res;
  };

  await ((ids): Promise<void[]> =>
    Promise.all([
      (async () => {
        for (let i = 0; i < state.list.length; i++) {
          // const currentRegionParentId = (regionData.find((v) => v.id === state.list[i].regionId) || {}).parentId || null;
          // const result = [state.list[i].regionDesc];
          // const locationDescStrArr = currentRegionParentId ? getTreeRegion(currentRegionParentId, result) : result;
          // (state.list[i] as any).locationDescStrArr = [...locationDescStrArr.filter((v, idx) => !idx || idx === locationDescStrArr.length - 1), state.list[i].locationDesc];
          (state.list[i] as any).locationDescStrArr = [...(state.list[i].regionDesc || "").split("/"), state.list[i].locationDesc];
        }
      })(),
      (async () => {
        discovery.value.clear();
        state.list.forEach((v, i) => {
          v.showServiceNumber = false;
          v.showEquipmentModel = false;
          v.showSerialNumber = false;
          v.showAssetNumber = false;
          v.showVendor = false;
          v.showDeviceType = false;
          v.showDeviceGroup = false;
          v.showUnit = false;
          v.config.modelNumbers = v.config.modelNumbers || [];
          v.config.serialNumbers = v.config.serialNumbers || [];

          discovery.value.forEach((item) => {
            // console.log(v.id, item.resourceId);
            if (v.id === item.resourceId) {
              // v.config.modelNumbers.unshift(item.modelName);
              // v.config.serialNumbers.unshift(item.serialNumber);
              if (item.serialNumber != null) {
                item.serialNumber += v.config.modelNumbers.join();
              } else {
                item.serialNumber = v.config.modelNumbers.join();
              }
              if (item.modelName != null) {
                item.modelName += v.config.serialNumbers.join();
              } else {
                item.modelName = v.config.serialNumbers.join();
              }

              // item.modelName += v.config.serialNumbers.join();
              item.showSerialSize = false;
              item.showModeSize = false;
            }
          });
        });

        if (!ids.length) return;
        const { success, message, data } = await getDiscoveryByResources({ ids });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const $data = data instanceof Array ? data : [];
        for (let i = 0; i < $data.length; i++) discovery.value.set($data[i].resourceId, $data[i]);

        state.list.forEach((v, i) => {
          v.showServiceNumber = false;
          v.showEquipmentModel = false;
          v.showSerialNumber = false;
          v.showAssetNumber = false;
          v.showVendor = false;
          v.showDeviceType = false;
          v.showDeviceGroup = false;
          v.showUnit = false;
          v.config.modelNumbers = v.config.modelNumbers || [];
          v.config.serialNumbers = v.config.serialNumbers || [];

          const item = discovery.value.get(v.id);
          if (item) {
            // v.config.modelNumbers.unshift(item.modelName);
            // v.config.serialNumbers.unshift(item.serialNumber);
            if (item.serialNumber != null) item.serialNumber += v.config.modelNumbers.join();
            else item.serialNumber = v.config.modelNumbers.join();

            if (item.modelName != null) item.modelName += v.config.serialNumbers.join();
            else item.modelName = v.config.serialNumbers.join();

            // item.modelName += v.config.serialNumbers.join();
            item.showSerialSize = false;
            item.showModeSize = false;
          }
        });
      })(),
      (async () => {
        deskTopObj.value.clear();
        state.list.forEach((v, i) => {
          v.showDesktop = false;
          v.allowTypes = [];
        });
        if (!ids.length) return;
        const { success, message, data } = await getRemoteDesktopByResources({ deviceIds: ids });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const $data = data instanceof Array ? data : [];
        for (let i = 0; i < $data.length; i++) deskTopObj.value.set($data[i].resourceId, $data[i]);

        state.list.forEach((v, i) => {
          const item = deskTopObj.value.get(v.id);
          if (item) {
            v.showDesktop = item.icoShow;
            v.allowTypes = item.allowTypes;
          }
        });
      })(),
      (async () => {
        remoteOnline.value.clear();
        if (!ids.length) return;
        const { success, message, data } = await getRemoteOnlineByResources({ ids });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const $data = data instanceof Array ? data : [];
        for (let i = 0; i < $data.length; i++) remoteOnline.value.set($data[i].resourceId, $data[i]);
      })(),
    ]))(data.map((v) => v.id));
}
async function createItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, "add", async (form) => {
    // let containerId = "";
    // for (let i = 0; i < userInfo.tenants.length; i++) {
    //   if (userInfo.tenants[i].id === userInfo.currentTenantId) containerId = userInfo.tenants[i].containerId;
    // }
    console.log(form);
    const { success, message, data } = await addItemData({ containerId: form.containerId, delivery: form.delivery, alias: form.alias, resourceOnlineTime: form.resourceOnlineTime, resourceOfflineTime: form.resourceOfflineTime, regionId: <string>form.regionId /* 区域ID, 选择性更新时如果想置空, 则传0 */ || "0", modelIdent: "netcare_device", locationId: <string>form.locationId /* 场所ID, 选择性更新时如果想置空, 则传0 */ || "0", typeIds: form.typeIds ? [form.typeIds] : [] /* 资源类型ID列表 */, groupIds: <string[]>form.groupIds /* 设备组ID列表 */, vendorIds: <string[]>form.vendorIds /* 服务商ID列表 */, alertClassificationIds: <string[]>form.alertClassificationIds /* 告警分类ID列表 */, supportNoteIds: <string[]>form.supportNoteIds /* 行动策略ID列表 */, assetNumber: <string>form.assetNumber /* 资产编号 */, externalId: <string>form.externalId /* 外部ID */, name: <string>form.name /* 资源名称 */, monitorSources: <string[]>form.monitorSources /* 监控源 */, unit: <string>form.unit /* 业务单位 */, description: <string>form.description /* 描述信息 */, timeZone: <string>form.timeZone /* 时区 */, importance: <deviceImportance>form.importance /* 资源重要性枚举：High :至关重要的、Medium :中、Low :低、None :无、Unknown :未知的 */ || null, tags: <string[]>form.tags /* 标签列表 */, active: <boolean>form.active /* 是否激活 */, config: Object.assign(form.config, { modelNumbers: JSON.stringify(form.config.modelNumbers || []), serialNumbers: JSON.stringify(form.config.serialNumbers || []), assetNumbers: JSON.stringify(form.config.assetNumbers || []), dynamicIp: JSON.stringify(form.config.dynamicIp || false), ackRequired: JSON.stringify(form.config.ackRequired || false), nmsTicketing: JSON.stringify(form.config.nmsTicketing || false) }) /* 选择性更新时, config中的属性也是逐个更新 */ });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${i18n.t("axios.Operation successful")}`);
  });
}
async function previewItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form) => {
    const { success, message, data } = await setItemData({ delivery: form.delivery, alias: form.alias, resourceOnlineTime: form.resourceOnlineTime, resourceOfflineTime: form.resourceOfflineTime, id: <string>row.id, regionId: <string>form.regionId /* 区域ID, 选择性更新时如果想置空, 则传0 */ || "0", locationId: <string>form.locationId /* 场所ID, 选择性更新时如果想置空, 则传0 */ || "0", typeIds: form.typeIds ? [form.typeIds] : [] /* 资源类型ID列表 */, groupIds: <string[]>form.groupIds /* 设备组ID列表 */, vendorIds: <string[]>form.vendorIds /* 服务商ID列表 */, alertClassificationIds: <string[]>form.alertClassificationIds /* 告警分类ID列表 */, supportNoteIds: <string[]>form.supportNoteIds /* 行动策略ID列表 */, assetNumber: <string>form.assetNumber /* 资产编号 */, externalId: <string>form.externalId /* 外部ID */, name: <string>form.name /* 资源名称 */, monitorSources: <string[]>form.monitorSources /* 监控源 */, unit: <string>form.unit /* 业务单位 */, description: <string>form.description /* 描述信息 */, timeZone: <string>form.timeZone /* 时区 */, importance: <deviceImportance>form.importance /* 资源重要性枚举：High :至关重要的、Medium :中、Low :低、None :无、Unknown :未知的 */ || null, tags: <string[]>form.tags /* 标签列表 */, active: <boolean>form.active /* 是否激活 */, config: Object.assign(form.config, { modelNumbers: JSON.stringify(form.config.modelNumbers || []), serialNumbers: JSON.stringify(form.config.serialNumbers || []), assetNumbers: JSON.stringify(form.config.assetNumbers || []), dynamicIp: JSON.stringify(form.config.dynamicIp || false), ackRequired: JSON.stringify(form.config.ackRequired || false), nmsTicketing: JSON.stringify(form.config.nmsTicketing || false) }) /* 选择性更新时, config中的属性也是逐个更新 */ });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${i18n.t("axios.Operation successful")}`);
  });
}
async function rewriteItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open({ ...row, resourceOfflineTime: row.resourceOfflineTime * 1, resourceOnlineTime: row.resourceOnlineTime * 1 }, async (form) => {
    const { success, message, data } = await setItemData({ delivery: form.delivery, alias: form.alias, resourceOnlineTime: form.resourceOnlineTime, resourceOfflineTime: form.resourceOfflineTime, id: <string>row.id, regionId: <string>form.regionId /* 区域ID, 选择性更新时如果想置空, 则传0 */ || "0", locationId: <string>form.locationId /* 场所ID, 选择性更新时如果想置空, 则传0 */ || "0", typeIds: form.typeIds ? [form.typeIds] : [] /* 资源类型ID列表 */, groupIds: <string[]>form.groupIds /* 设备组ID列表 */, vendorIds: <string[]>form.vendorIds /* 服务商ID列表 */, alertClassificationIds: <string[]>form.alertClassificationIds /* 告警分类ID列表 */, supportNoteIds: <string[]>form.supportNoteIds /* 行动策略ID列表 */, assetNumber: <string>form.assetNumber /* 资产编号 */, externalId: <string>form.externalId /* 外部ID */, name: <string>form.name /* 资源名称 */, monitorSources: <string[]>form.monitorSources /* 监控源 */, unit: <string>form.unit /* 业务单位 */, description: <string>form.description /* 描述信息 */, timeZone: <string>form.timeZone /* 时区 */, importance: <deviceImportance>form.importance /* 资源重要性枚举：High :至关重要的、Medium :中、Low :低、None :无、Unknown :未知的 */ || null, tags: <string[]>form.tags /* 标签列表 */, active: <boolean>form.active /* 是否激活 */, config: Object.assign(form.config, { modelNumbers: JSON.stringify(form.config.modelNumbers || []), serialNumbers: JSON.stringify(form.config.serialNumbers || []), assetNumbers: JSON.stringify(form.config.assetNumbers || []), dynamicIp: JSON.stringify(form.config.dynamicIp || false), ackRequired: JSON.stringify(form.config.ackRequired || false), nmsTicketing: JSON.stringify(form.config.nmsTicketing || false) }) /* 选择性更新时, config中的属性也是逐个更新 */ });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${i18n.t("axios.Operation successful")}`);
  });
}
async function modifyItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;

  await editorRef.value.open(row, "update", async (form) => {
    const { success, message, data } = await modItemData({
      /*  */
      delivery: form.delivery,
      alias: form.alias,
      resourceOnlineTime: form.resourceOnlineTime,
      resourceOfflineTime: form.resourceOfflineTime,
      id: <string>row.id,
      regionId: <string>form.regionId /* 区域ID, 选择性更新时如果想置空, 则传0 */ || "0",
      locationId: <string>form.locationId /* 场所ID, 选择性更新时如果想置空, 则传0 */ || "0",
      typeIds: form.typeIds ? [form.typeIds] : [] /* 资源类型ID列表 */,
      groupIds: <string[]>form.groupIds /* 设备组ID列表 */,
      vendorIds: <string[]>form.vendorIds /* 服务商ID列表 */,
      alertClassificationIds: <string[]>form.alertClassificationIds /* 告警分类ID列表 */,
      supportNoteIds: <string[]>form.supportNoteIds /* 行动策略ID列表 */,
      assetNumber: <string>form.assetNumber /* 资产编号 */,
      externalId: <string>form.externalId /* 外部ID */,
      name: <string>form.name /* 资源名称 */,
      monitorSources: <string[]>form.monitorSources /* 监控源 */,
      unit: <string>form.unit /* 业务单位 */,
      description: <string>form.description /* 描述信息 */,
      timeZone: <string>form.timeZone /* 时区 */,
      importance: <deviceImportance>form.importance /* 资源重要性枚举：High :至关重要的、Medium :中、Low :低、None :无、Unknown :未知的 */ || null,
      tags: <string[]>form.tags /* 标签列表 */,
      active: <boolean>form.active /* 是否激活 */,
      config: Object.assign(form.config, {
        /*  */
        modelNumbers: JSON.stringify(form.config.modelNumbers || []),
        serialNumbers: JSON.stringify(form.config.serialNumbers || []),
        assetNumbers: JSON.stringify(form.config.assetNumbers || []),
        dynamicIp: JSON.stringify(form.config.dynamicIp || false),
        ackRequired: JSON.stringify(form.config.ackRequired || false),
        nmsTicketing: JSON.stringify(form.config.nmsTicketing || false),
      }) /* 选择性更新时, config中的属性也是逐个更新 */,
    });
    if (!success) throw Object.assign(new Error(message), { success, data });

    state.list.forEach((v, i) => {
      if (v.id === data.id) {
        v = data;
      }
    });

    ElMessage.success(`${"操作成功，设备信息已更新"}`);
  });
}
async function deleteItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.confirm({ ...row, $slot: "deleteItem", $title: `${row.active ? "下线" : "上线"}${props.title}` }, async (form) => {
    const { success, message, data } = await modItemData({ id: <string>row.id, regionId: <string>row.regionId /* 区域ID, 选择性更新时如果想置空, 则传0 */ || "0", locationId: <string>row.locationId /* 场所ID, 选择性更新时如果想置空, 则传0 */ || "0", typeIds: form.typeIds ? [form.typeIds] : [] /* 资源类型ID列表 */, groupIds: <string[]>row.groupIds /* 设备组ID列表 */, vendorIds: <string[]>row.vendorIds /* 服务商ID列表 */, alertClassificationIds: <string[]>row.alertClassificationIds /* 告警分类ID列表 */, supportNoteIds: <string[]>row.supportNoteIds /* 行动策略ID列表 */, assetNumber: <string>row.assetNumber /* 资产编号 */, externalId: <string>row.externalId /* 外部ID */, name: <string>row.name /* 资源名称 */, monitorSources: <string[]>row.monitorSources /* 监控源 */, unit: <string>row.unit /* 业务单位 */, description: <string>row.description /* 描述信息 */, timeZone: <string>row.timeZone /* 时区 */, importance: <deviceImportance>row.importance /* 资源重要性枚举：High :至关重要的、Medium :中、Low :低、None :无、Unknown :未知的 */ || null, tags: <string[]>row.tags /* 标签列表 */, active: <boolean>!row.active /* 是否激活 */, config: Object.assign(row.config, { modelNumbers: JSON.stringify(row.config.modelNumbers || []), serialNumbers: JSON.stringify(row.config.serialNumbers || []), assetNumbers: JSON.stringify(row.config.assetNumbers || []), dynamicIp: JSON.stringify(row.config.dynamicIp || false), ackRequired: JSON.stringify(row.config.ackRequired || false), nmsTicketing: JSON.stringify(row.config.nmsTicketing || false) }) /* 选择性更新时, config中的属性也是逐个更新 */ });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`${"操作成功"}`);
  });
}
async function exportItem() {
  const { success, message, data, contentDisposition } = await extItemList({ ...state.search, active: state.search.active ? state.search.active : undefined, name: state.search.name, paging: { pageNumber: state.page, pageSize: state.size } });
  if (!success) throw Object.assign(new Error(message), { success, data, contentDisposition });
  if (data instanceof Blob) {
    const link = Object.assign(document.createElement("a"), { href: URL.createObjectURL(data), download: (contentDisposition || {}).filename });
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    await nextTick();
    URL.revokeObjectURL(link.href);
  }
}

function tryJSON(params: string) {
  try {
    return JSON.parse(params);
  } catch (error) {
    return null;
  }
}

const alarmList = ref<import("@/views/pages/apis/device").AlertClassificationsItem[]>([]);
const vendorList = ref<import("@/views/pages/apis/device").VendorsItem[]>([]);
const deviceTypeList = ref<import("@/views/pages/apis/device").ResourceTypeItem[]>([]);
const deviceGroupList = ref<import("@/views/pages/apis/device").DeviceGroupItem[]>([]);
getVendors();
async function getVendors() {
  const { success, message, data } = await getVendorsList({ vendorType: "DEVICE", containerId: userInfo.currentTenant.containerId, queryPermissionId: "515410299369553920", verifyPermissionIds: "512903546983677952,512903566256504832,512903582660427776" });
  if (!success) throw Object.assign(new Error(message), { success, data });
  vendorList.value = data instanceof Array ? data : [];
}
getAlertClassifications();
async function getAlertClassifications() {
  const { success, message, data } = await getAlertClassificationsList({});
  if (!success) throw Object.assign(new Error(message), { success, data });
  alarmList.value = data instanceof Array ? data : [];
}
getResource();
async function getResource() {
  const { success, message, data } = await getResourceTypeList({ containerId: userInfo.currentTenant.containerId, queryPermissionId: "512873892906270720", verifyPermissionIds: "512878757501992960,512878782932058112,512881859223355392" });
  if (!success) throw Object.assign(new Error(message), { success, data });
  deviceTypeList.value = data instanceof Array ? data : [];
}

getDeviceGroup();
async function getDeviceGroup() {
  const { success, message, data } = await getDeviceGroupList({ containerId: userInfo.currentTenant.containerId, queryPermissionId: "512862869692350464", verifyPermissionIds: "512862890017947648,512862911924797440,512879011144138752" });
  if (!success) throw Object.assign(new Error(message), { success, data });
  deviceGroupList.value = data instanceof Array ? data : [];
}
function filterByAlarmList(alarmList, arr) {
  // 1. 如果alarmList为空，直接返回空数组
  if (!alarmList || alarmList.length === 0) {
    return "";
  }
  // 2. 创建alarmList的ID映射表（使用字符串形式避免类型问题）
  const alarmIdMap = new Map();
  alarmList.forEach((item) => {
    alarmIdMap.set(String(item.id), item);
  });
  // 3. 过滤arr数组，只保留存在于alarmList中的项
  return arr.filter((item) => {
    return alarmIdMap.has(String(item.id));
  });
}

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.basicInfo {
  > li {
    display: flex;
    align-items: center;
    :deep(.el-tooltip__popper) {
      width: 800px;
      min-width: 400px;
      max-width: 800px;
    }
    > span:first-child {
      flex: none;
      display: block;
      font-size: 12px;
      // width: 65px;
      font-weight: normal;
    }
    > span {
      flex: 1;
      font-weight: 700;
    }
    > span.hidden {
      flex: 1;
      font-weight: 700;
      // text-overflow: ellipsis;
      // display: -webkit-box;
      // -webkit-line-clamp: 2;
      // line-clamp: 2;
      // -webkit-box-orient: vertical;
      /*1. 先强制一行内显示文本*/
      white-space: nowrap;
      /*2. 超出的部分隐藏*/
      overflow: hidden;

      /*3. 文字用省略号替代超出的部分*/
      text-overflow: ellipsis;
    }
    > span.show {
      flex: 1;
      font-weight: 700;
    }
    .property-size {
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
}
.network-settings {
  font-weight: 700;
  p {
    display: flex;
    align-items: center;
  }
}
.basic_info {
  :deep(.el-descriptions__body) {
    background-color: transparent;
    tr {
      background-color: transparent;
    }
    .el-descriptions__cell {
      padding-bottom: 0px !important;
    }
  }
}
.device-list {
  :deep(.el-table) {
    .warning-row {
      // --el-table-tr-bg-color: #909399;
      background: rgba($color: #ebeef5, $alpha: 0.3);
    }
  }
}
</style>
