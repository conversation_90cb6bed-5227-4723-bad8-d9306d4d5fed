// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { getdeviceTypeList } from "@/views/pages/apis/deviceManage";
import { getdeviceGroupList } from "@/views/pages/apis/deviceManage";
import { getAlarmClassificationList } from "@/views/pages/apis/alarmClassification";
import { getRegionsTenantCurrent } from "@/views/pages/apis/regionManage";
import { getLocationsTenantCurrent } from "@/views/pages/apis/locationManang";

import { getSupplierList, getLineSupplierList } from "@/views/pages/apis/supplier";
export default {
  data() {
    return {
      deviceGroupList: [],
      deviceTypeList: [],
      alarmList: [],
      locationList: [],
      supplierList: [],
      deviceGroupOption: {},
      deviceTypeOption: {},
      alarmOption: {},
      locationOption: {},
      supplierOption: {},
      regionsOption: {},
      regionsOptionArr: [],
      supplierLineList: [],

      supplierLineOption: {},
    };
  },
  mounted() {
    this.getLocationList();
  },
  methods: {
    getLocationList() {
      //场所列表
      getLocationsTenantCurrent({}).then((res) => {
        this.locationList = res.data;
        const options = {};
        res.data.forEach((v, i) => {
          options[v.id] = v.name;
          options[v.id + v.name] = v.zoneId;
        });
        // console.log(options);
        this.locationOption = options;
      });
    },
    getRegionList() {
      //区域列表
      getRegionsTenantCurrent({}).then((res) => {
        const options = {};
        this.regionsOptionArr = res.data;
        res.data.forEach((v, i) => {
          options[v.id] = v.name;
          options[v.id + v.name] = v.label;
        });
        this.regionsOption = options;
      });
    },
    getdeviceTypeList() {
      //设备类型列表
      getdeviceTypeList({}).then((res) => {
        if (res.success) {
          this.deviceTypeList = res.data;
          const options = {};
          res.data.forEach((v, i) => {
            options[v.id] = v.name;
          });
          this.deviceTypeOption = options;
        }
      });
    },
    getGroupDevice() {
      //设备分组列表
      getdeviceGroupList({}).then((res) => {
        this.deviceGroupList = res.data;

        const options = {};
        res.data.forEach((v, i) => {
          options[v.id] = v.name;
        });
        this.deviceGroupOption = options;
      });
    },
    getAlarmList() {
      //告警分类列表
      getAlarmClassificationList({}).then((res) => {
        this.alarmList = res.data;
        const options = {};
        res.data.forEach((v, i) => {
          options[v.id] = v.name;
        });
        this.alarmOption = options;
      });
    },
    getSupplierList() {
      //设备服务商列表
      getSupplierList({}).then((res) => {
        this.supplierList = res.data;
        const options = {};
        res.data.forEach((v, i) => {
          options[v.id] = v.name;
        });
        this.supplierOption = options;
      });
    },
    getLineSupplierList() {
      //线路服务商列表
      getLineSupplierList({}).then((res) => {
        this.supplierLineList = res.data;
        const options = {};
        res.data.forEach((v, i) => {
          options[v.id] = v.name;
        });
        this.supplierLineOption = options;
      });
    },
  },
};
