<template>
  <el-dialog title="模拟告警" v-model="dialogFormVisible" :before-close="cancel" width="50%">
    <div style="height: 200px" class="device-dialog">
      <el-scrollbar height="200px">
        <el-form :model="form" ref="ruleForm" :rules="rules" class="deviceForm">
          <el-form-item label="告警标题:" :label-width="formLabelWidth" prop="title">
            <el-input v-emoji v-model="form.title" autocomplete="off" placeholder="请输入告警标题"></el-input>
          </el-form-item>
          <el-form-item label="告警摘要:" :label-width="formLabelWidth" prop="digest">
            <el-input v-model="form.digest" autocomplete="off" placeholder="请输入告警摘要"></el-input>
          </el-form-item>
          <el-form-item label="告警类型:" :label-width="formLabelWidth" prop="severity">
            <!-- <el-input style="width: 202px" v-model="form.digest" autocomplete="off" placeholder="请输入设备描述"></el-input> -->
            <el-radio-group v-model="form.severity">
              <el-radio :label="'Critical'">Critical</el-radio>
              <el-radio :label="'Major'">Major</el-radio>
              <el-radio :label="'Minor'">Minor</el-radio>
              <el-radio :label="'Warning'"> Warning</el-radio>
              <el-radio :label="'Normal'"> Normal</el-radio>
              <el-radio :label="'Unknown'">Unknown</el-radio>
              <el-radio :label="'Monitoring'">Monitoring</el-radio>
              <el-radio :label="'Informational'">Informational</el-radio>
              <el-radio :label="'Calculating'">Calculating</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { defineComponent } from "vue";
import { QuestionFilled as ElIconQuestion } from "@element-plus/icons-vue";
import mixin from "./mixin";
import regionMixin from "../regionManage/js/mixin";
import { addModelAlram } from "@/views/pages/apis/device";
import { ElMessage } from "element-plus";
import inputTag from "@/components/input-tag";

import { getAlarmMerge } from "@/views/pages/apis/alarmMerge";

export default defineComponent({
  name: "EventCenterIntelNoiseReductCreate",
  components: {
    // inputTag,
    ElIconQuestion,
  },
  mixins: [mixin, regionMixin],
  props: {
    id: {
      type: String,
      default: "",
    },
  },
  emits: ["confirm"],
  data() {
    return {
      form: {
        // modelIdent: "netcare_device", //模型标识
        severity: "Critical",
        title: "告警标题",
        digest: "告警摘要",
        deviceId: "",
      },
      rules: {
        title: [{ required: true, message: "请输入告警标题", trigger: "blur" }],
        digest: [{ required: true, message: "请输入告警摘要", trigger: "blur" }],
        severity: [{ required: true, message: "请选择告警类型", trigger: "change" }],
      },
      dialogFormVisible: false,
      formLabelWidth: "120px",
    };
  },

  mounted() {},
  methods: {
    open(row) {
      this.dialogFormVisible = true;
      // this.form.severity = "";
      // this.form.title = "";
      // this.form.digest = "";
      // // console.log(row);
      this.form.deviceId = row.id;
    },
    submit() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          addModelAlram({ ...this.form })
            .then((res) => {
              // // console.log(res);
              if (res.success) {
                this.$message.success("操作成功");
              } else {
                this.$message.error(res.data?.message);
              }
              this.dialogFormVisible = false;
            })
            .catch((err) => {
              this.$message.error(err.message);
            });
        }
      });

      // // console.log(this.form);
    },
    cancel() {
      this.$refs["ruleForm"].resetFields();
      this.dialogFormVisible = false;
      this.$emit("confirm", { id: this.id });
    },
  },
  expose: ["type", "dialogFormVisible", "open"],
});
</script>

<style lang="scss" scoped>
.device-dialog {
  overflow: auto;
  > .el-scrollbar {
    overflow: auto;
  }
}
.select {
  .el-select-dropdown__item {
    // display: flex;
    min-height: 90px !important;
    padding-left: 5px;
    box-sizing: border-box;
    // background: red;
  }
}
.options-name {
  padding-right: 10px;
  float: left;
  margin-top: -6px;
}

:deep(.deviceForm) {
  .elstyle-dialog__body {
    padding: 20px 0;
    box-sizing: border-box;
  }
}

.divider {
  width: 100%;
  height: 5px;
  background: #eee;
  margin-bottom: 22px;
  border-radius: 5px;
}
</style>
