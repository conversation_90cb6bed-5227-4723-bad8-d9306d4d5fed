<template>
  <el-dialog v-model="dialogVisible" :title="title" width="500" :before-close="handleClose">
    <p v-for="item in device.allowTypes" :key="item" class="desk">
      <el-button @click="openDesk(item)" type="primary" link> —> {{ item }}{{ `${i18n.t("devicesList.connect")}` }}</el-button>
    </p>

    <selectPasswrodWallet ref="selectPasswrodWalletRef"></selectPasswrodWallet>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ `${i18n.t("glob.Cancel")}` }}</el-button>
      </div>
    </template>
    <!-- <deskTop v-if="connect" :query="query" :force-http="forceHttp" /> -->
  </el-dialog>
</template>

<script>
import getUserInfo from "@/utils/getUserInfo";
// import deskTop from "./deskTop.vue";
import { useRoute, useRouter } from "vue-router";

import selectPasswrodWallet from "@/views/pages/alarm_convergence/PropertyManage/passwordWallet/selectPasswrodWallet.vue";

import validPwd from "@/views/pages/alarm_convergence/PropertyManage/passwordWallet/validPwd";
import { useI18n } from "vue-i18n";
export default {
  name: "GuacamoleClient",
  components: {
    // deskTop,
    selectPasswrodWallet,
  },
  props: {
    deviceId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      i18n: useI18n(),
      dialogVisible: false,
      title: "",
      device: {},
      userInfo: getUserInfo(),
      type: "",
      forceHttp: false,
      connect: false,
      router: useRouter(),
    };
  },
  computed: {
    queryObj() {
      return {
        resourceId: this.device.id ,
        protocol: this.type,
        tenantId: this.userInfo.currentTenantId,
        // 'ignore-cert': this.ignoreCert,
        width: 1280,
        height: 397,
        Authorization: this.userInfo.token,
      };
    },
    query() {
      const queryString = [];
      for (const [k, v] of Object.entries(this.queryObj)) {
        if (v) {
          queryString.push(`${k}=${encodeURIComponent(v)}`);
        }
      }
      return queryString.join("&");
    },
  },
  mounted() {
    // console.log(this.userInfo);
  },
  methods: {
    handleClose() {
      this.dialogVisible = false;
    },
    openDesk(item) {
      this.$refs.selectPasswrodWalletRef.handleOpen(this.queryObj.resourceId || this.deviceId, item);

      // this.type = item;
      // this.connect = true;
      // if (window.localStorage) {
      //   window.localStorage.setItem("query", JSON.stringify(this.queryObj));
      // }
      // const { href } = this.router.resolve({
      //   name: "vnc",
      //   // params: { id: props.id },
      //   query: this.queryObj,
      // });

      // window.open(href, "_blank");
      // console.log(this.query);
    },
  },
  expose: ["dialogVisible", "handleClose", "title", "device"],
};
</script>

<style scoped>
.desk {
  color: #409eff;
  cursor: pointer;
}
</style>
