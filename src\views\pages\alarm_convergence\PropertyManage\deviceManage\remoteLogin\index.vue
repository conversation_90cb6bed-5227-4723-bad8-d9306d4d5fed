<template>
  <el-card id="display-box" :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <el-page-header @back="() => handleBack()">
      <template #content>
        <span class="text-large font-600 mr-3"> 远程登录 </span>
      </template>
    </el-page-header>
    <div class="tw-mt-4" id="diaplay" ref="diaplay" tabindex="0"></div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, inject, computed, onMounted, onBeforeUnmount, nextTick } from "vue";
// import { onBeforeRouteLeave } from "vue-router";
import { useRouter, useRoute } from "vue-router";
import getUserInfo from "@/utils/getUserInfo";

import { ElMessage } from "element-plus";
import Guacamole from "guacamole-common-js";

// import TUNNEL_STATES from "@/lib/states";

const userInfo = getUserInfo();
interface Props {
  width?: number;
  height?: number;
  title?: string;
}

const router = useRouter();
const route = useRoute();

const props = withDefaults(defineProps<Props>(), { title: "资源池" });

const _width = inject("width", ref(0));
const _height = inject("height", ref(0));

const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);

const diaplay = ref<any>();
const token = userInfo.token;
const guac = ref<any>();
const keyboard = ref<any>();
const mouse = ref<any>();
async function handleInfo() {
  diaplay.value = document.getElementById("diaplay");
  await nextTick();
  guac.value = new Guacamole.Client(new Guacamole.WebSocketTunnel(`/gw_proxy/netcare/openapi/cloudCare/tool/remote/ws?token=${route.params.token}&data=${route.params.data}`));

  diaplay.value.appendChild(guac.value.getDisplay().getElement());

  guac.value.onerror = function (error) {
    var code = error.code;
    if (code === "0x0301") {
      ElMessage.error("用户名或密码错误");
    } else if (code === "0x0303") {
      ElMessage.error("没有权限");
    } else {
      ElMessage.error(error.message);
    }
  };

  // Connect
  guac.value.connect("");

  // Mouse
  mouse.value = new Guacamole.Mouse(guac.value.getDisplay().getElement());

  mouse.value.onmousedown =
    mouse.value.onmouseup =
    mouse.value.onmousemove =
      function (mouseState) {
        guac.value.sendMouseState(mouseState);
      };

  // Keyboard
  keyboard.value = new Guacamole.Keyboard(diaplay.value);
  // keyboard.value = new Guacamole.Keyboard(document);

  setTimeout(() => {
    diaplay.value.focus();
  }, 1000); // $nextTick wasn't enough

  keyboard.value.onkeydown = function (keysym) {
    guac.value.sendKeyEvent(1, keysym);
  };

  keyboard.value.onkeyup = function (keysym) {
    guac.value.sendKeyEvent(0, keysym);
  };
}

function handleBack() {
  if (history.length === 1) window.close();
  else router.back();
}

// Disconnect on close
onBeforeUnmount(async () => {
  guac.value.disconnect();
  keyboard.value.onkeydown = null;
  keyboard.value.onkeyup = null;
  await nextTick();
  guac.value = null;
});

onMounted(async () => {
  handleInfo();
});
</script>

<style lang="scss">
#diaplay {
  canvas {
    z-index: 999 !important;
  }
}
</style>
