<!--  -->
<template>
  <div>
    <el-dialog :title="title" v-model="dialogVisible" width="45%" :before-close="cancel">
      <el-form :model="form" :disabled="type" :rules="rules" ref="serviceFormRef">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="服务编号：" :label-width="formLabelWidth" prop="number">
              <el-input v-model="form.number" autocomplete="off"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="描述：" :label-width="formLabelWidth" prop="description">
              <el-input v-model="form.description" type="textarea" autocomplete="off"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(资产管理中心_线路供应商_可读)">
              <el-form-item label="线路供应商：" :label-width="formLabelWidth">
                <el-select :disabled="!userInfo.hasPermission(资产管理中心_线路供应商_可读)" v-model="form.vendorIds" :placeholder="!type ? `请选择线路供应商` : ''" clearable style="width: 100%">
                  <el-option v-for="item in vendorListA" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-tooltip>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型：" :label-width="formLabelWidth" prop="type">
              <el-input v-model="form.type" autocomplete="off"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="线路带宽：" :label-width="formLabelWidth" prop="progress">
              <div style="display: flex">
                <el-input v-model="form.progress" autocomplete="off" style="width: 60%" @keyup="form.progress = form.progress.replace(/[^\d.]/g, '')"></el-input>
                <el-select v-model="form.progressUnit" style="width: 30%">
                  <el-option value="1" label="B">B</el-option>
                  <el-option value="1000" label="K"> K</el-option>
                  <el-option value="1000000" label="M">M</el-option>
                  <el-option value="1000000000" label="G">G</el-option>
                </el-select>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品：" :label-width="formLabelWidth" prop="product">
              <el-input v-model="form.product" autocomplete="off"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="端口名称：" :label-width="formLabelWidth" prop="portName">
              <el-input v-model="form.portName" autocomplete="off"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务等级：" :label-width="formLabelWidth" prop="serviceLevel">
              <el-input v-model="form.serviceLevel" autocomplete="off"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-collapse v-model="activeNames">
          <el-collapse-item title="线路附加信息" name="1">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="服务类型：" :label-width="formLabelWidth">
                  <el-select v-model="form.serviceType" :placeholder="!type ? `请选择服务类型` : ''" clearable :value-on-clear="''" style="width: 100%">
                    <el-option v-for="item in serviceTypeList" :key="item.value" :label="item.value" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="接入技术：" :label-width="formLabelWidth">
                  <el-select v-model="form.technology" :placeholder="!type ? `请选择接入技术` : ''" clearable :value-on-clear="''" style="width: 100%">
                    <el-option v-for="item in technologyList" :key="item.value" :label="item.value" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="Overlay：" :label-width="formLabelWidth" prop="overlay">
                  <el-select v-model="form.overlay" :placeholder="!type ? `请选择Overlay` : ''" clearable :value-on-clear="null" style="width: 100%">
                    <el-option v-for="item in overlayList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="Overlay类型：" :label-width="formLabelWidth" prop="overlayType">
                  <el-select v-model="form.overlayType" :placeholder="!type ? `请选择Overlay类型` : ''" clearable :value-on-clear="''" style="width: 100%">
                    <el-option v-for="item in overlayTypeList" :key="item.value" :label="item.value" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="Comments：" :label-width="formLabelWidth">
                  <el-select v-model="form.comments" :placeholder="!type ? `请选择Comments` : ''" clearable :value-on-clear="''" style="width: 100%">
                    <el-option v-for="item in commentsList" :key="item.value" :label="item.value" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="LAN Handoff：" :label-width="formLabelWidth">
                  <el-select v-model="form.lanHandoff" :placeholder="!type ? `请选择LAN Handoff` : ''" clearable :value-on-clear="''" style="width: 100%">
                    <el-option v-for="item in lanHandoffList" :key="item.value" :label="item.value" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="安装时间：" :label-width="formLabelWidth" prop="installDate">
                  <el-date-picker v-model="form.installDate" format="YYYY-MM-DD" value-format="x" type="date" :placeholder="!type ? $t('glob.Please select field', { field: '安装时间' }) : ''" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="每月费用(元)：" :label-width="formLabelWidth" prop="monthlyCharge">
                  <el-input type="number" v-model="form.monthlyCharge" autocomplete="off" :min="0"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">{{ `${i18n.t("glob.Cancel")}` }}</el-button>
          <el-button type="primary" @click="submit">{{ `${i18n.t("glob.Confirm")}` }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import mixin from "./mixin";
import { addService, setService, getServiceDetails } from "@/views/pages/apis/deviceManage";
import getUserInfo from "@/utils/getUserInfo";
import { 资产管理中心_线路供应商_可读 } from "@/views/pages/permission";
import { useI18n } from "vue-i18n";
import { getVendorsList } from "@/views/pages/apis/device";
export default {
  mixins: [mixin],
  props: {
    isAdd: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
  },
  emits: ["confirm"],
  data() {
    return {
      i18n: useI18n(),
      userInfo: getUserInfo(),
      isEmpty: true,
      form: {
        number: "",
        resourceId: this.$props.id,
        vendorIds: "",
        type: "",
        progress: "",
        product: "",
        description: "",
        id: "",
        portName: "",
        serviceLevel: "",
        progressUnit: "1000",
        serviceType: "",
        technology: "",
        overlay: "",
        overlayType: "",
        comments: "",
        lanHandoff: "",
        installDate: "",
        monthlyCharge: "",
      },
      formLabelWidth: "120px",
      dialogVisible: false,
      rules: {
        number: [{ required: true, message: "请输入服务编号", trigger: "blur" }],
        overlayType: [
          {
            validator: (rule, value, callback) => (this.form.overlay ? (this.form.overlayType ? callback() : callback(new Error("请选择Overlay类型"))) : callback()),
            trigger: "blur",
          },
        ],
        monthlyCharge: [
          {
            validator: (rule, value, callback) => (this.form.monthlyCharge && isNaN(this.form.monthlyCharge) ? callback(new Error("请输入数字")) : Number(this.form.monthlyCharge) >= 0 ? callback() : callback(new Error(`请输入正数`))),
            trigger: ["blur", "change"],
          },
        ],
      },
      title: "",
      type: "",
      activeNames: ["0"],
      资产管理中心_线路供应商_可读,
      serviceTypeList: [{ value: "Internet" }, { value: "Corporate" }, { value: "VPN" }],
      technologyList: [{ value: "ADSL" }, { value: "SDSL" }, { value: "VDSL" }, { value: "XDSL" }, { value: "DSL" }, { value: "FTTH" }],
      overlayList: [
        { label: "是", value: true },
        { label: "否", value: false },
      ],
      overlayTypeList: [{ value: "Meraki" }, { value: "ANIRA" }, { value: "Other VPN" }, { value: "N/A" }],
      commentsList: [{ value: "Underlay CT" }, { value: "Underlay CU" }, { value: "Underlay CM" }],
      lanHandoffList: [{ value: "Fiber" }, { value: "Copper" }],
      vendorListA: [],
    };
  },
  watch: {
    isAdd(val) {
      this.type = val == "info" ? true : false;
      this.getLineSupplierList();
      this.title = val == "add" ? "新增服务编号" : val === "info" ? "服务编号详情" : "编辑服务编号";
      if (val === "edit" || val === "info") {
        this.getServiceDetail();
      } else {
        this.isEmpty = true;
        this.activeNames = ["0"];
        this.form.vendorIds = "";
        this.form.portName = "";
        this.form.serviceLevel = "";
        this.form.serviceType = "";
        this.form.technology = "";
        this.form.overlay = "";
        this.form.overlayType = "";
        this.form.comments = "";
        this.form.lanHandoff = "";
        this.form.installDate = "";
        this.form.monthlyCharge = "";
        this.form.progressUnit = "1000";
      }
    },
  },
  mounted() {
    this.getVendors();
  },

  methods: {
    async getVendors() {
      const { success, message, data } = await getVendorsList({ vendorType: "LINE", containerId: this.userInfo.currentTenant.containerId, queryPermissionId: "515410299369553920", verifyPermissionIds: "512903546983677952,512903566256504832,512903582660427776" });
      if (!success) throw Object.assign(new Error(message), { success, data });
      this.vendorListA = data instanceof Array ? data : [];
    },
    getServiceDetail() {
      getServiceDetails({ id: this.form.id }).then((res) => {
        // let vendorIds = [];
        // if (res.success && res.data.vendors) {
        //   res.data.vendors.forEach((item) => {
        //     vendorIds.push(item.id);
        //   });
        // }
        this.form = { ...res.data.serviceNumber };
        // 当用户无权限时清空供应商选择
        // this.form.vendorIds = this.userInfo.hasPermission(资产管理中心_线路供应商_可读) ? res.data.serviceNumber.vendorIds[0] : "";

        this.form.vendorIds = this.userInfo.hasPermission(资产管理中心_线路供应商_可读) ? this.form.vendorIds : "";
        const idsToCheck = Array.isArray(this.form.vendorIds) ? this.form.vendorIds : [this.form.vendorIds];
        const vendorAll = new Set(this.vendorListA.map((item) => item.id));
        this.form.vendorIds = idsToCheck.filter((id) => vendorAll.has(id)).length == 0 ? "" : idsToCheck.filter((id) => vendorAll.has(id))[0];

        this.form.progressUnit = res.data.serviceNumber.progressUnit || "K";
        if (res.data.serviceNumber.progress && res.data.serviceNumber.progressUnit) {
          this.form.progress = parseFloat(res.data.serviceNumber.progress / res.data.serviceNumber.progressUnit);
        } else {
          this.form.progress = "";
        }
        if (!this.form.serviceType && !this.form.technology && !this.form.overlay && !this.form.overlayType && !this.form.comments && !this.form.lanHandoff && !this.form.installDate && !this.form.monthlyCharge) {
          this.isEmpty = false;
        } else {
          this.activeNames = ["1"];
          this.isEmpty = true;
        }
        // this.form.progressUnit=
        // switch (res.data.serviceNumber.progressUnit) {
        //   case "1":
        //     this.form.progressUnit = "1";
        //     this.form.progress = parseFloat(res.data.serviceNumber);
        //     break;
        //   case "1024":
        //     this.form.progressUnit = "1024";
        //     this.form.progress = parseFloat(res.data.serviceNumber / 1024);

        //     break;
        //   case "1024000":
        //     this.form.progressUnit = "1024000";
        //     this.form.progress = parseFloat(res.data.serviceNumber / 1024000);

        //     break;
        //   case "G":
        //     this.form.progressUnit = "1024000000";
        //     this.form.progress = parseFloat(res.data.serviceNumber / 1024000000);

        //     break;
        // }
      });
    },

    cancel() {
      this.dialogVisible = false;
      this.$emit("confirm", false);
      this.$refs["serviceFormRef"].resetFields();
      this.$refs["serviceFormRef"].clearValidate();
    },
    submit() {
      this.$refs["serviceFormRef"].validate((valid) => {
        if (valid) {
          // alert("submit!");
          if (this.isAdd === "add") {
            delete this.form.id;
            this.form.monthlyCharge = Number(this.form.monthlyCharge);
            addService({ ...this.form, progress: this.form.progress * this.form.progressUnit, vendorIds: [this.form.vendorIds] })
              .then((res) => {
                // console.log(res);
                if (res.success) {
                  this.$message.success("操作成功");
                  this.$refs["serviceFormRef"].resetFields();
                  this.dialogVisible = false;
                  this.$emit("confirm", true);
                } else this.$message.error(JSON.parse(res.data)?.message);
              })
              .catch((err) => {
                this.$message.error(err?.message);
              });
          } else {
            setService({ ...this.form, progress: this.form.progress * this.form.progressUnit, vendorIds: [this.form.vendorIds] })
              .then((res) => {
                if (res.success) {
                  this.$message.success("操作成功");
                  this.$refs["serviceFormRef"].resetFields();
                  this.dialogVisible = false;
                  this.$emit("confirm", true);
                } else this.$message.error(JSON.parse(res.data)?.message);
              })
              .catch((err) => {
                this.$message.error(err?.message);
              });
          }
        }
      });
    },
    disableStartTime(time) {
      if (!this.form.installDate) {
        return false;
      } else if (this.form.installDate && time < this.form.installDate) {
        return false; // 当前日期不能小于开始日期
      } else {
        return true; // 其他情况都返回true，表示该日期无效
      }
    },
  },
  expose: ["dialogVisible", "title", "form"],
};
</script>
<style scoped lang="scss"></style>
