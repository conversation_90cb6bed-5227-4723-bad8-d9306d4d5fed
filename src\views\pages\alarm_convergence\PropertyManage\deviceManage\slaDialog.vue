<!--  -->
<template>
  <div>
    <el-dialog :title="`${i18n.t('devicesInfo.Bind SLA service')}`" v-model="dialogVisible" :before-close="cancel" width="30%">
      <el-form ref="serviceForm">
        <el-form-item :label="`${i18n.t('devicesInfo.Select SLA service')}` + '：'" :label-width="formLabelWidth" prop="number">
          <el-select v-model="ruleId" clearable filterable :placeholder="`${i18n.t('devicesInfo.Please Select')}`">
            <el-option v-for="item in options" :key="item.ruleId" :label="item.ruleName" :disabled="item.disabled ? item.disabled : false" :value="item.ruleId"> </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">{{ `${i18n.t("glob.Cancel")}` }}</el-button>
          <el-button type="primary" @click="submit">{{ `${i18n.t("glob.Confirm")}` }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { deviceRelationSla } from "@/views/pages/apis/deviceManage";
import { getSlaEnableListV2, getnewSlaConfigByPage, getnewSlaquerySlaList } from "@/views/pages/apis/SlaConfig";
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMenuItem } from "element-plus";
import { useI18n } from "vue-i18n";
export default {
  props: {
    isAdd: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },

    selectedIds: {
      type: Array,
      default: () => [],
    },
  },
  emits: ["confirm"],
  data() {
    return {
      i18n: useI18n(),
      userInfo: getUserInfo(),
      ruleId: "",
      formLabelWidth: "120px",
      dialogVisible: false,

      title: "",
      options: [],
      resourceId: "",
      disabledList: [],
    };
  },
  watch: {
    disabledList(data) {
      this.getSlaList();
      // for (let i = 0; i < data.length; i++) {
      //   this.options.findIndex((item) => {
      //     if (item.ruleId === data[i].ruleId) {
      //       item.disabled = true;
      //     }
      //   });
      //   // 前两个数据的key值相同，打印出对应的下标 ，后面找不相同的key  输出-1
      // }
    },
  },

  // created() {
  // },
  mounted() {
    this.getSlaList();
  },

  methods: {
    getSlaList() {
      getnewSlaquerySlaList({ containerId: this.userInfo.currentTenant.containerId, queryPermissionId: "515413313438351360", verifyPermissionIds: "" }).then((res) => {
        if (res) {
          let arr = [...res];
          // for (let i = 0; i < this.disabledList.length; i++) {
          //   arr.findIndex((item) => {
          //     // item.disabled = false;
          //     if (item.ruleId === this.disabledList[i].ruleId) {
          //       item.disabled = true;
          //     }
          //   });
          //   // 前两个数据的key值相同，打印出对应的下标 ，后面找不相同的key  输出-1
          // }
          this.options = arr.filter((item) => !this.selectedIds.includes(item.ruleId));
        }
      });
    },
    cancel() {
      this.dialogVisible = false;
      this.$emit("confirm", false);
    },
    submit() {
      let data = {
        resourceId: this.resourceId,
        ruleId: this.ruleId,
      };

      deviceRelationSla(data)
        .then((res) => {
          if (res.success) {
            this.$message.success(`${this.i18n.t("axios.Operation successful")}`);

            this.dialogVisible = false;
            this.$emit("confirm", true);
          } else this.$message.error(JSON.parse(res.data)?.message);
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        });
    },
  },
  expose: ["dialogVisible", "ruleId", "resourceId", "disabledList"],
};
</script>
<style scoped lang="scss"></style>
