<template>
  <div>
    <el-dialog :title="title" v-model="dialogFormVisible" :before-close="handleClose" width="50vw">
      <el-form :model="form" :rules="rules" ref="ruleForm">
        <el-form-item label="设备类型名称:" :label-width="formLabelWidth" prop="name">
          <el-input v-model="form.name" autocomplete="off" placeholder="请输入设备类型名称"></el-input>
        </el-form-item>
        <el-form-item label="英文名称:" :label-width="formLabelWidth">
          <el-input v-model="form.enName" autocomplete="off" placeholder="请输入英文名称" @input="validateInput"></el-input>
        </el-form-item>
        <el-form-item label="描述:" :label-width="formLabelWidth" prop="description">
          <el-input type="textarea" v-model="form.description" autocomplete="off" :rows="2" placeholder="请输入描述"></el-input>
        </el-form-item>
        <!-- <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(服务管理中心_设备分组_分配告警分类)"> -->
        <el-form-item label="告警分类:" :label-width="formLabelWidth" prop="alertClassificationIds">
          <!-- :disabled="!userInfo.hasPermission(服务管理中心_设备分组_分配告警分类)" -->
          <el-select v-model="form.alertClassificationIds" placeholder="请选择告警分类" multiple :style="{ width: '100%' }">
            <el-option v-for="item in options" :key="item.name" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <!-- </el-tooltip> -->
        <el-form-item v-if="!form.id" :label="`选择安全目录`" tooltip="" prop="containerId" :rules="[buildValidatorData({ name: 'required', title: `请选择安全目录` })]" style="margin-top: 10px">
          <treeAuth v-if="dialogFormVisible" ref="treeAuthRef" :treeStyle="treeStyle"></treeAuth>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel()">取 消</el-button>
          <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { defineComponent } from "vue";
import { ElMessage, ElMenuItem } from "element-plus";

import { adddeviceType, editdeviceType } from "@/views/pages/apis/deviceManage";
import { getAlarmClassificationList } from "@/views/pages/apis/alarmClassification";
import { buildValidatorData, validatorPattern } from "@/utils/validate";
import treeAuth from "@/components/treeAuth/index.vue";
import getUserInfo from "@/utils/getUserInfo";
import { 服务管理中心_设备分组_分配告警分类, 服务管理中心_告警分类_可读 } from "@/views/pages/permission";
export default defineComponent({
  name: "supplierCreate",
  components: {
    treeAuth,
  },
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      userInfo: getUserInfo(),
      form: {
        name: "",
        description: "",
        report: false,
        alertClassificationIds: "",
        enName: "",
        containerId: "",
      },
      rules: {
        name: [{ required: true, message: "请输入设备类型名称", trigger: "blur" }],
      },
      title: "",
      checked: false,
      dialogFormVisible: false,
      formLabelWidth: "130px",
      type: "",
      options: [],
      value: "",
      disabled: false,
      buildValidatorData: buildValidatorData,
      treeStyle: {
        width: "300px",
        height: "150px",
      },
      服务管理中心_告警分类_可读,
    };
  },
  watch: {
    dialog(val) {
      // this.$refs["ruleForm"].clearValidate();
      this.dialogFormVisible = val;
      this.$nextTick(() => {
        // console.log(this.$refs.treeAuthRef);
        this.$refs.treeAuthRef && this.$refs.treeAuthRef.getSafeContaine();
      });
      //
      if (this.type === "add") {
        for (var key in this.form) {
          this.form[key] = null;
        }
      }
      this.form.alertClassificationIds = this.userInfo.hasPermission(服务管理中心_告警分类_可读) ? this.form.alertClassificationIds : [];
      const vendorAll = new Set(this.options.map((item) => String(item.id)));
      this.form.alertClassificationIds = this.form.alertClassificationIds.filter((id) => vendorAll.has(id));
    },
    type(val) {
      if (val === "add") {
        for (var key in this.form) {
          this.form[key] = null;
        }
      }
    },
  },
  created() {
    // console.log(this.$props, 5555);
  },
  mounted() {
    this.getAlarmList();
  },
  methods: {
    validateInput(value) {
      // 只允许输入数字和字母
      const reg = /^[A-Za-z0-9]*(\s[A-Za-z0-9]*)*$/;
      if (reg.test(value)) {
        return true;
      } else {
        this.form.enName = this.form.enName.replace(/([^a-zA-Z0-9\s])/g, "");
        return false;
      }
    },
    getAlarmList() {
      getAlarmClassificationList({}).then((res) => {
        if (res.success) {
          this.options = [...res.data];
        } else {
          ElMessage.error(JSON.parse(res.data)?.message + "操作失败");
        }
      });
    },
    async confirm(formName) {
      if (!this.form.id) this.form.containerId = this.$refs.treeAuthRef?.treeItem?.id;
      await this.$nextTick();
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          if (this.type === "add") {
            adddeviceType({ ...this.form })
              .then((res) => {
                if (res.success) {
                  ElMessage.success("新增成功");
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                  this.dialogFormVisible = false;
                } else {
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                  ElMessage.error(JSON.parse(res.data)?.message + "操作失败");
                }
              })
              .catch((error) => {
                error instanceof Error && this.$message.error(error.message);
              });
          } else {
            editdeviceType({ ...this.form, containerId: this.$refs.treeAuthRef?.treeItem?.id })
              .then((res) => {
                if (res.success) {
                  ElMessage.success("修改成功");
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                  this.dialogFormVisible = false;
                } else {
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                  ElMessage.error(JSON.parse(res.data)?.message + "操作失败");
                }
              })
              .catch((error) => {
                error instanceof Error && this.$message.error(error.message);
              });
          }
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      // this.dialogFormVisible = false;
    },
    cancel() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
    },
    blurChange(data) {
      this.tags.push(data);
    },
    tagClose(index) {
      this.tags.splice(index, 1);
    },
  },
  expose: ["type", "disabled", "title", "form"],
});
</script>
