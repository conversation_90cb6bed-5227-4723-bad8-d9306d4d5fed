<template>
  <el-dialog :title="`${isEdit ? t('glob.Edit Data', { value: t('userGroup.User group') }) : t('glob.New Data', { value: t('userGroup.User group') })}`" v-model="dialogFormVisible" :before-close="beforeClose">
    <el-form ref="form" :model="form" label-width="100px" label-position="right" :rules="rules">
      <el-row :gutter="24">
        <el-col :span="23">
          <el-form-item :label="t('userGroup.Name')" prop="name">
            <el-input v-model="form.name" :placeholder="t('userGroup.Please enter the name')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="23">
          <el-form-item :label="t('userGroup.Description')" prop="note">
            <el-input type="textarea" v-model="form.note" :placeholder="t('userGroup.Please enter the description')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="23">
          <el-form-item label="Email" prop="email">
            <el-input v-model="form.email" :placeholder="t('userGroup.Please enter the email')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="23">
          <el-form-item :label="t('userGroup.Time zone')" prop="zoneId">
            <el-select v-model="form.zoneId" :placeholder="t('userGroup.Please choose a time zone')" filterable clearable :style="{ width: '100%' }">
              <el-option v-for="item in zone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="23">
          <el-form-item :label="t('userGroup.Language')" prop="language">
            <el-select v-model="form.language" :placeholder="t('userGroup.Please choose a language')" :style="{ width: '100%' }" filterable ref="select">
              <el-option v-for="item in localesOption" :key="item.value" :label="item.label" :value="item.value">
                <div :style="{ background: `url(${item.icon}) no-repeat left / auto calc(100% - 12px)`, paddingLeft: '30px' }">{{ item.label }}</div>
              </el-option>
              <template #tag> 123456 </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :offset="1">
          <el-form-item :label="t('userGroup.Two-factor authentication')" prop="enabelMfa">
            <el-checkbox v-model="form.enabelMfa"></el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item :label="t('userGroup.Active')" prop="active">
            <el-checkbox v-model="form.active"></el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="beforeClose">{{ t("glob.Cancel") }}</el-button>
        <el-button type="primary" @click="submitForm">{{ t("glob.Confirm") }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { ref, reactive, readonly, computed, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch } from "vue";
import { createRegionsTenantCurrent, editRegionsById } from "@/views/pages/apis/regionManage";
import { ElMessage } from "element-plus";
import zone from "@/views/pages/common/zone.json";
import { localesOption } from "@/api/locale";
import { addUserGroups, editUserGroups } from "@/views/pages/apis/groupManage";
import { getTenantInfo } from "@/views/pages/apis/tenant";
import { useI18n } from "vue-i18n";
export default {
  components: {},
  emits: ["refresh", "custom-event"],
  // components: { aMap },
  data() {
    const t = useI18n().t;
    return {
      t /* 翻译方法 */,
      zone,
      localesOption,
      dialogFormVisible: false,
      form: {
        id: "",
        name: "",
        note: "",
        email: "",
        zoneId: "",
        language: "",
        active: true,
        enabelMfa: true,
        containerId: "",
      },
      isEdit: false,
    };
  },
  computed: {
    rules() {
      return {
        name: [
          {
            required: true,
            message: this.t("userGroup.Please enter the name"),
            trigger: ["bulr", "change"],
          },
        ],
      };
    },
  },
  mounted() {
    this.getInfo();
  },
  methods: {
    // 获取当前用户信息
    async getInfo() {
      await getTenantInfo({})
        .then(({ success, data }) => {
          if (success) {
            this.form.containerId = data.containerId;
          } else ElMessage.error(JSON.parse(data)?.message || this.t("axios.Operation failure"));
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        });
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) return valid;
        const params = {
          id: this.form.id,
          name: this.form.name,
          note: this.form.note,
          email: this.form.email,
          zoneId: this.form.zoneId,
          language: this.form.language,
          active: this.form.active,
          enabelMfa: this.form.enabelMfa,
          containerId: this.form.containerId,
        };
        if (!this.form.id) Object.assign(params, { parentId: this.form.parentId });
        (this.form.id ? editUserGroups : addUserGroups)(params, this.form?.id || null)
          .then(({ success, data }) => {
            if (success) {
              ElMessage.success(this.t("axios.Operation successful"));
              this.$emit("custom-event");
              this.beforeClose();
            } else ElMessage.error(JSON.parse(data)?.message || this.t("axios.Operation failure"));
          })
          .catch((e) => {
            if (e instanceof Error) ElMessage.error(e.message);
          });
      });
    },
    open(row = {}) {
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.form = {
          id: row.id,
          name: row.name,
          note: row.note,
          email: row.email,
          zoneId: row.zoneId,
          language: row.language,
          active: row.active === undefined ? true : row.active,
          enabelMfa: row.mfaEnabled,
          containerId: this.form.containerId,
        };
      });
      this.isEdit = row.id ? true : false;
    },
    setPosition(v) {
      this.form.latitude = v.lat;
      this.form.longitude = v.lng;
    },
    beforeClose(done) {
      this.$refs.form.clearValidate();
      this.$refs.form.resetFields();
      if (done instanceof Function) done();
      else this.dialogFormVisible = false;
    },
  },
  expose: ["open", "beforeClose"],
};
</script>
<style lang="scss" scoped>
.res-dialog {
  :deep(.el-dialog__body) {
    overflow: auto !important;
    height: 400px !important;
  }
}
</style>
