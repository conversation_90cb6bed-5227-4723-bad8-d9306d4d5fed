<template>
  <el-scrollbar class="device-list">
    <el-card :body-style="{ padding: '20px' }">
      <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="handleRefreshTable()" @current-change="handleRefreshTable()">
        <template #left>
          <div style="font-size: 14px; font-weight: bold">{{ t("userGroup.User group") }} - {{ userInfo.currentTenant.name }}[{{ userInfo.currentTenant.abbreviation }}]</div>
        </template>
        <template #right>
          <div>
            <span class="tw-ml-[12px] tw-h-fit">
              <el-button v-if="userInfo.hasPermission('512903291017887744')" type="primary" :icon="Plus" @click="handleCommand(command.Create, {})">{{ $t("glob.New Data", { value: t("userGroup.User group") }) }}</el-button>
            </span>
          </div>
        </template>
        <template #default="{ height: tableHeight }">
          <el-table :data="tableData" :height="`${tableHeight}px`" :expand-row-keys="expands" :row-key="getRowKeys" :default-expand-all="false" stripe @expand-change="handleExpandChange">
            <el-table-column type="expand">
              <template #default="{ row, column, $index }">
                <div>
                  <div class="box-table">
                    <div class="box">
                      <div>{{ t("userGroup.Users") }}</div>
                      <!-- <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission('513154549993701376')"> -->
                      <span class="tw-ml-[12px] tw-h-fit">
                        <el-button v-if="userInfo.hasPermission('513154549993701376')" type="primary" @click="handleCommand(command.CreateUser, {})">{{ t("userGroup.Add user") }}</el-button>
                      </span>
                      <!-- </el-tooltip> -->
                    </div>
                    <el-table border :show-header="false" :data="userList" style="width: 100%">
                      <el-table-column prop="date" label="">
                        <template #default="{ row, column, $index }">
                          <div>
                            <P class="tw-text-base">{{ row.name }}</P>
                            <P class="tw-text-sm">{{ row.account }}</P>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="address" label="" width="180">
                        <template #default="{ row, column, $index }">
                          <!-- <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission('513154549993701376')"> -->
                          <span>
                            <el-link v-if="userInfo.hasPermission('513154549993701376')" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleCommand(command.DeleteUser, row)">{{ t("userGroup.Remove") }}</el-link>
                          </span>
                          <!-- </el-tooltip> -->
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                  <div class="box-table">
                    <div class="box">
                      <div>{{ t("userGroup.User groups") }}</div>
                      <!-- <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission('610990928127066112')"> -->
                      <span class="tw-ml-[12px] tw-h-fit">
                        <el-button v-if="userInfo.hasPermission('610990928127066112')" type="primary" @click="handleCommand(command.CreateGroup, {})">{{ t("userGroup.Add user group") }}</el-button>
                      </span>
                      <!-- </el-tooltip> -->
                    </div>
                    <el-table border :show-header="false" :data="groupList" style="width: 100%">
                      <el-table-column prop="date" label="" width="">
                        <template #default="{ row, column, $index }">
                          <div>
                            {{ row.name }}
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="address" label="" width="180">
                        <template #default="{ row, column, $index }">
                          <!-- <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission('610990928127066112')"> -->
                          <span>
                            <el-link v-if="userInfo.hasPermission('610990928127066112')" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleCommand(command.DeleteGroup, row)">{{ t("userGroup.Remove") }}</el-link>
                          </span>
                          <!-- </el-tooltip> -->
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
                <!-- <el-empty v-else :description="$t('glob.noPower')" class="tw-h-full" /> -->
              </template>
            </el-table-column>

            <TableColumn type="condition" :prop="`name`" :label="t('userGroup.Name')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="handleRefreshTable()"></TableColumn>

            <TableColumn type="condition" :prop="`email`" :label="`Email`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByEmail" :filters="$filter2" @filter-change="handleRefreshTable()"></TableColumn>
            <!-- <el-table-column label="名称" prop="name"> </el-table-column> -->
            <!-- <el-table-column label="Email" prop="email"> </el-table-column> -->
            <!-- <el-table-column label="所有客户" prop=""> </el-table-column> -->
            <!-- <el-table-column label="双因素认证" prop="mfaEnabled">
              <template #default="{ row, column, $index }">
                <div style="font-size: 18">
                  {{ row.mfaEnabled ? "√" : "×" }}
                </div>
              </template></el-table-column
            > -->

            <!-- <TableColumn
              type="enum"
              :prop="`mfaEnabled`"
              :label="`双因素认证`"
              :width="114"
              show-filter
              v-model:filtered-value="search.mfaEnabled"
              :filters="[
                { value: 'true', text: '√' },
                { value: 'false', text: '×' },
                // { value: 'DEFAULT', text: '默认' },
              ]"
              @filter-change="handleRefreshTable()"
            >
              <template #default="{ row, column, $index }">
                <div style="font-size: 18">
                  {{ row.mfaEnabled ? "√" : "×" }}
                </div>
              </template>
            </TableColumn> -->
            <!-- <el-table-column label="激活" prop="active">
              <template #default="{ row, column, $index }">
                <div style="font-size: 18">{{ row.active ? "√" : "×" }}</div>
              </template>
            </el-table-column> -->
            <TableColumn
              type="enum"
              :prop="`active`"
              :label="t('userGroup.Active')"
              :width="114"
              show-filter
              v-model:filtered-value="search.active"
              :filters="[
                { value: 'true', text: '√' },
                { value: 'false', text: '×' },
                // { value: 'DEFAULT', text: '默认' },
              ]"
              @filter-change="handleRefreshTable()"
            >
              <template #default="{ row, column, $index }">
                <div style="font-size: 18">{{ row.active ? "√" : "×" }}</div>
              </template>
            </TableColumn>
            <!-- <el-table-column type="index" label="ID" :min-width="60">
              <template #default="scope">
                <span>{{ scope.$index + 1 + (paging.pageNumber - 1) * paging.pageSize }}</span>
              </template>
            </el-table-column> -->
            <el-table-column prop="">
              <template #default="{ row, column, $index }">
                <!-- <el-tooltip :content="$t('glob.noPower')" v-if="userInfo.hasPermission('512903326241652736')"> -->
                <span>
                  <el-link v-if="userInfo.hasPermission('512903326241652736')" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleCommand(command.Update, row)">{{ $t("glob.edit") }}</el-link>
                </span>
                <!-- </el-tooltip> -->
                <!-- <el-tooltip :content="$t('glob.noPower')" v-if="userInfo.hasPermission('512903351881433088')"> -->
                <span>
                  <el-link v-if="userInfo.hasPermission('512903351881433088')" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleCommand(command.Delete, row)">{{ $t("glob.delete") }}</el-link>
                </span>
                <!-- </el-tooltip> -->
                <span>
                  <!-- 用户组管理('604221467017609216') -->
                  <el-link :type="userInfo.hasPermission('623053128442314752') ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="userInfo.hasPermission('623053128442314752') ? false : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
                </span>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </pageTemplate>
    </el-card>
    <Editor ref="addGroup" @custom-event="handleRefreshTable" />
    <el-dialog :title="t('userGroup.Add user group')" v-model="dialogAddGroup" :before-close="beforeCloseGroup" width="500">
      <el-form ref="addGroupform" :model="addGroupform" label-width="80px" :rules="rules">
        <el-row :gutter="24">
          <el-col>
            <el-form-item :label="t('userGroup.User group')" prop="userGroupIds">
              <el-select filterable class="tw-w-full" v-model="addGroupform.userGroupIds" multiple :placeholder="t('userGroup.Please choose user groups')" @change="cascaderChangeG">
                <el-option v-for="item in userGroupOption" :key="item.id" :label="item.name" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="beforeCloseGroup">{{ t("glob.Cancel") }}</el-button>
          <el-button type="primary" @click="submitFormGroup">{{ t("glob.Confirm") }}</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="t('userGroup.Add user')" v-model="dialogAddUser" :before-close="beforeCloseUser" width="500">
      <el-form ref="addUserform" :model="addUserform" label-width="80px" :rules="rules">
        <el-row :gutter="24">
          <el-col>
            <el-form-item :label="t('userGroup.Users')" prop="userGroupIds">
              <el-select filterable :filter-method="handleUserFilter" style="width: 300px" v-model="addUserform.userIds" multiple :placeholder="t('userGroup.Please choose users')" @change="cascaderChangeU">
                <el-option class="select_item" v-for="item in userOption" :key="item.id" :label="item.name" :value="item.id">
                  <div style="height: 50px">
                    <p style="color: #8492a6; font-weight: 800; font-size: 16px; height: 35px; display: block">
                      {{ item.name }}
                    </p>
                    <p style="color: #8492a6; font-size: 13px; display: block; margin-top: -10px; height: 20px">{{ t("userManagement.Username") }}:{{ item.account }}</p>
                    <p style="color: #8492a6; font-size: 13px; display: block; height: 20px">{{ t("userManagement.Email") }}:{{ item.email }}</p>
                    <p style="color: #8492a6; font-size: 13px; display: block; height: 20px">
                      {{ item.name }}
                    </p>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="beforeCloseUser">{{ t("glob.Cancel") }}</el-button>
          <el-button type="primary" @click="submitFormUser">{{ t("glob.Confirm") }}</el-button>
        </div>
      </template>
    </el-dialog>
  </el-scrollbar>
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { ref, reactive, readonly, computed, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch } from "vue";
import getUserInfo from "@/utils/getUserInfo";
import Editor from "./Editor.vue";
import pageTemplate from "@/components/pageTemplate.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getGroup } from "@/api/personnel";
import treeAuth from "@/components/treeAuth/index.vue";
import { getUserGroupsList, delUserGroups, getUserList, getAllotUserList, addUsers, delUsers, getGroupList, getAllotGroupList, addGroups, delGroups } from "@/views/pages/apis/groupManage";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";

import TableColumn from "@/components/tableColumn/TableColumn.vue";

import { useI18n } from "vue-i18n";

import { exoprtMatch1, exoprtMatch2,exoprtMatch3 } from "@/components/tableColumn/common";

export default {
  components: {
    Editor,
    pageTemplate,
    treeAuth,
    TableColumn,
  },
  inject: ["height"],
  data() {
    const t = useI18n().t;
    return {
      t /* 翻译方法 */,
      // 安全容器
      dialogVisibleshow: false,
      treeStyle: {
        pointerEvents: "none",
      },
      containerId: "",
      // 用户组开始
      dialogAddGroup: false,
      addGroupform: {
        userGroupIds: [],
        id: "",
      },
      userGroupOption: [],
      // 用户组结束
      // 用户开始
      dialogAddUser: false,
      addUserform: {
        userIds: [],
        id: "",
      },
      userOption: [], // 选择器展示的用户
      userAll: [], // 全部用户
      // 用户结束
      paging: {
        pageNumber: 1,
        pageSize: 50,
        total: 0,
      },
      form: {
        id: "",
        regionId: "" /** 所在区域ID */,
        locationId: "" /** 所在场所ID */,
        name: "" /** 资源名称 **/,
        description: "" /** 描述信息 */,
        tags: "" /** 标签列表 */,
        active: false /** 是否激活 */,
        delivery: false /* 是否交付*/,
      },
      tableData: [],
      userList: [],
      groupList: [],
      userInfo: getUserInfo(),
      props: { title: t("userGroup.User group") },
      command: {
        Create: "Create",
        Update: "Update",
        Delete: "Delete",
        CreateUser: "CreateUser",
        DeleteUser: "DeleteUser",
        CreateGroup: "CreateGroup",
        DeleteGroup: "DeleteGroup",
      },
      expands: [],
      Security,
      $filter0: exoprtMatch1,
      $filter2: exoprtMatch3,
      $filter1: exoprtMatch2,
      searchType0ByName: "include",
      searchType1ByName: "include",

      searchType0ByEmail: "eq",
      searchType1ByEmail: "eq",

      search: {
        eqName: [] /* 等于的用户名称 */,
        includeName: [] /* 包含的用户名称 */,
        nameFilterRelation: "AND" /* 用户名称过滤关系(AND,OR) */,
        neName: [] /* 不等于的用户名称 */,
        excludeName: [] /* 不包含的用户名称 */,

        eqEmail: [] /* 等于的邮箱 */,
        includeEmail: [] /* 包含的邮箱 */,
        emailFilterRelation: "AND" /* 邮箱过滤关系(AND,OR) */,
        neEmail: [] /* 不等于的邮箱 */,
        excludeEmail: [] /* 不包含的邮箱 */,
      },
    };
  },
  computed: {
    searchByName: {
      get: function () {
        let value0 = "";
        if (this.searchType0ByName === "include") value0 = this.search.includeName[0] || "";
        if (this.searchType0ByName === "exclude") value0 = this.search.excludeName[0] || "";
        if (this.searchType0ByName === "eq") value0 = this.search.eqName[0] || "";
        if (this.searchType0ByName === "ne") value0 = this.search.neName[0] || "";
        let value1 = "";
        if (this.searchType1ByName === "include") value1 = this.search.includeName[this.search.includeName.length - 1] || "";
        if (this.searchType1ByName === "exclude") value1 = this.search.excludeName[this.search.excludeName.length - 1] || "";
        if (this.searchType1ByName === "eq") value1 = this.search.eqName[this.search.eqName.length - 1] || "";
        if (this.searchType1ByName === "ne") value1 = this.search.neName[this.search.neName.length - 1] || "";
        return {
          type0: this.searchType0ByName,
          type1: this.searchType1ByName,
          relation: this.search.nameFilterRelation,
          value0,
          value1,
          input0: "",
          // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
          input1: "",
          // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
        };
      },
      set: function (v) {
        this.searchType0ByName = v.type0;
        this.searchType1ByName = v.type1;
        this.search.nameFilterRelation = v.relation;
        this.search.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
        this.search.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
        this.search.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
        this.search.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
      },
    },

    searchByEmail: {
      get: function () {
        let value0 = "";
        if (this.searchType0ByEmail === "include") value0 = this.search.includeEmail[0] || "";
        if (this.searchType0ByEmail === "exclude") value0 = this.search.excludeEmail[0] || "";
        if (this.searchType0ByEmail === "eq") value0 = this.search.eqEmail[0] || "";
        if (this.searchType0ByEmail === "ne") value0 = this.search.neEmail[0] || "";
        let value1 = "";
        if (this.searchType1ByEmail === "include") value1 = this.search.includeEmail[this.search.includeEmail.length - 1] || "";
        if (this.searchType1ByEmail === "exclude") value1 = this.search.excludeEmail[this.search.excludeEmail.length - 1] || "";
        if (this.searchType1ByEmail === "eq") value1 = this.search.eqEmail[this.search.eqEmail.length - 1] || "";
        if (this.searchType1ByEmail === "ne") value1 = this.search.neEmail[this.search.neEmail.length - 1] || "";
        return {
          type0: this.searchType0ByEmail,
          type1: this.searchType1ByEmail,
          relation: this.search.emailFilterRelation,
          value0,
          value1,
          input0: "",
          // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
          input1: "",
          // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
        };
      },
      set: function (v) {
        this.searchType0ByEmail = v.type0;
        this.searchType1ByEmail = v.type1;
        this.search.emailFilterRelation = v.relation;
        this.search.includeEmail = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
        this.search.excludeEmail = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
        this.search.eqEmail = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
        this.search.neEmail = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
      },
    },
  },
  created() {},
  mounted() {
    this.getGroupsList();
    this.handleRefreshTable();
  },
  methods: {
    handleCommand(type, row) {
      switch (type) {
        case this.command.Create:
          this.$refs.addGroup.open(row);
          break;
        case this.command.Update:
          this.$refs.addGroup.open(row);
          break;
        case this.command.Delete: //删除用户组
          ElMessageBox.confirm(`${this.t("userGroup.Are you sure you want to delete the user group", { name: row.name })}`, this.t("userGroup.Reminder"), {
            confirmButtonText: this.t("glob.Confirm"),
            cancelButtonText: this.t("glob.Cancel"),
            type: "warning",
          })
            .then(async () => {
              try {
                const { success, data, message } = await delUserGroups({
                  id: row.id,
                });
                if (!success) throw new Error(message);
                ElMessage.success(this.t("axios.Operation successful"));
                this.handleRefreshTable();
              } catch (error) {
                error instanceof Error && ElMessage.error(error.message);
              }
            })
            .catch((err) => {});
          break;
        case this.command.CreateGroup:
          this.dialogAddGroup = true;
          break;
        case this.command.DeleteGroup: //删除用户组下面的用户组
          const paramsGroup = {
            childId: row.id,
            id: this.userGroupId,
          };
          ElMessageBox.confirm(`${this.t("userGroup.Are you sure you want to remove the user group", { name: row.name })}`, this.t("userGroup.Reminder"), {
            confirmButtonText: this.t("glob.Confirm"),
            cancelButtonText: this.t("glob.Cancel"),
            type: "warning",
          })
            .then(async () => {
              try {
                const { success, data, message } = await delGroups(paramsGroup);
                if (!success) throw new Error(message);
                ElMessage.success(this.t("axios.Operation successful"));
                this.getUserGroupList(this.userGroupId);
                this.beforeCloseGroup();
              } catch (error) {
                error instanceof Error && ElMessage.error(error.message);
              }
            })
            .catch((err) => {});
          break;
        case this.command.CreateUser:
          this.dialogAddUser = true;
          this.getUserGroupList(this.userGroupId);
          break;
        case this.command.DeleteUser: //删除用户组下面的用户
          const paramsUser = {
            userIds: [row.id],
            id: this.userGroupId,
          };
          ElMessageBox.confirm(`${this.t("userGroup.Are you sure you want to remove the user", { name: row.name })}`, this.t("userGroup.Reminder"), {
            confirmButtonText: this.t("glob.Confirm"),
            cancelButtonText: this.t("glob.Cancel"),
            type: "warning",
          })
            .then(async () => {
              try {
                const { success, data, message } = await delUsers(paramsUser);
                if (!success) throw new Error(message);
                ElMessage.success(this.t("axios.Operation successful"));
                this.getUserGroupList(this.userGroupId);
                this.beforeCloseUser();
              } catch (error) {
                error instanceof Error && ElMessage.error(error.message);
              }
            })
            .catch((err) => {});
          break;
      }
    },

    beforeCloseGroup(done) {
      const addGroupform = this.$refs.addGroupform;
      if (addGroupform) {
        addGroupform.clearValidate();
        addGroupform.resetFields();
      }
      this.addGroupform.userGroupIds = [];
      if (done instanceof Function) done();
      else this.dialogAddGroup = false;
    },
    beforeCloseUser(done) {
      const addUserform = this.$refs.addUserform;
      if (addUserform) {
        addUserform.clearValidate();
        addUserform.resetFields();
      }
      this.addUserform.userIds = [];
      if (done instanceof Function) done();
      else this.dialogAddUser = false;
    },
    // ---------------------用户组开始-------------------------
    async getUserGroupsList() {
      await (async () => {
        const { success, message, data } = await getUserGroupsList({});
        if (!success) throw Object.assign(new Error(message), { success, data });
      })();
    },
    handleRefreshTable() {
      const params = {
        pageNumber: this.paging.pageNumber,
        pageSize: this.paging.pageSize,
        ...this.search,
      };
      getUserGroupsList(params).then(({ success, data, total }) => {
        if (success) {
          this.paging.total = Number(total);
          this.tableData = data;
          if (!this.tableData.length && this.paging.pageNumber !== 1) {
            this.paging.pageNumber = 1;
            this.handleRefreshTable();
          }
        } else console.error(JSON.parse(data)?.message || data);
      });
    },
    // ---------------------用户组结束-------------------------
    // ---------------------添加子用户组--------------------
    cascaderChangeG(val) {
      this.addGroupform.userGroupIds = [];
      val.forEach((v, i) => {
        if (v.length > 1) {
          this.addGroupform.userGroupIds.push(v);
        }
      });
    },
    cascaderChangeU(val) {
      this.addUserform.userIds = [];
      val.forEach((v, i) => {
        if (v.length > 1) {
          this.addUserform.userIds.push(v);
        }
      });
    },
    async getGroupsList() {
      await (async () => {
        const { success, message, data } = await getGroup({});
        if (!success) throw Object.assign(new Error(message), { success, data });
        this.userGroupOption = data;
      })();
    },
    // 用户组添加用户
    submitFormUser() {
      const params = {
        userIds: this.addUserform.userIds,
        id: this.userGroupId,
      };
      addUsers(params)
        .then(({ success, data }) => {
          if (success) {
            ElMessage.success(this.t("axios.Operation successful"));
            this.beforeCloseUser();
            this.getUserGroupList(this.userGroupId);
            this.addUserform.userIds = [];
          } else ElMessage.error(JSON.parse(data)?.message || this.t("axios.Operation failure"));
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        });
    },
    // 用户组添加用户组
    submitFormGroup() {
      const params = {
        userGroupIds: this.addGroupform.userGroupIds,
        id: this.userGroupId,
      };
      addGroups(params)
        .then(({ success, data }) => {
          if (success) {
            ElMessage.success(this.t("axios.Operation successful"));
            this.beforeCloseGroup();
            this.getUserGroupList(this.userGroupId);
            this.addGroupform.userGroupIds = [];
          } else ElMessage.error(JSON.parse(data)?.message || this.t("axios.Operation failure"));
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        });
    },

    // 列表数据点击获取用户组&用户列表数据
    // eslint-disable-next-line no-undef
    getRowKeys(row) {
      return row.id;
    },
    async handleExpandChange(row, expandedRows) {
      if (expandedRows.length) {
        //展开
        this.expands = []; //先干掉之前展开的行
        if (row) {
          this.expands.push(row.id); //push新的行 (原理有点类似防抖)
        }
      } else {
        this.expands = []; //折叠 就清空expand-row-keys对应的数组
      }
      this.userGroupId = row.id;
      await this.getUserGroupList(this.userGroupId);
    },
    handleUserFilter(val) {
      if (val) this.userOption = this.userAll.filter((v) => v.account.includes(val) || v.name.includes(val));
      else this.userOption = this.userAll;
    },
    async getUserGroupList(userGroupId) {
      // 获取用户列表
      await (async () => {
        const { success, message, data } = await getUserList({
          id: userGroupId,
        });
        if (!success) throw Object.assign(new Error(message), { success, data });
        this.userList = data;
      })();
      // 获取可分配用户列表
      await (async () => {
        const { success, message, data } = await getAllotUserList({
          id: userGroupId,
        });
        if (!success) throw Object.assign(new Error(message), { success, data });
        this.userAll = JSON.parse(JSON.stringify(data instanceof Array ? data : []));
      })();
      // 获取用户组
      await (async () => {
        const { success, message, data } = await getGroupList({
          id: userGroupId,
        });
        if (!success) throw Object.assign(new Error(message), { success, data });
        this.groupList = data;
      })();
      // 获取可分配用户组列表
      await (async () => {
        const { success, message, data } = await getAllotGroupList({
          id: userGroupId,
        });
        if (!success) throw Object.assign(new Error(message), { success, data });
        this.userGroupOption = data;
      })();
    },
    // 安全容器
    showSecurityTree,
  },
};
</script>

<style scoped lang="scss">
.box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.box-table {
  padding: 10px 20px;
}

.select_item {
  min-height: 30px !important;
  height: 100px !important;
  font-size: 12px;
}
.select_item_once {
  height: 60px !important;
}
</style>
