<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="handleRefreshLocationTable()" @current-change="handleRefreshLocationTable()">
      <template #left>
        <el-input style="width: 300px" v-model="searchForm.keyword" :placeholder="$t('location.Please enter the name')" @keyup.enter="handleRefreshLocationTable()">
          <template #append>
            <el-button :icon="Search" @click="handleRefreshLocationTable()" />
          </template>
        </el-input>
      </template>
      <template #right>
        <span class="">
          <el-button v-if="userInfo.hasPermission(资产管理中心_场所_新增)" type="primary" :icon="Plus" @click="handleOpenEdit()">{{ $t("glob.New Data", { value: $t("location.Location") }) }}</el-button>
        </span>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table v-loading="loading" stripe :data="tableLocationData" row-key="id" :height="tableHeight" style="width: 100%">
          <el-table-column type="expand" width="50" align="left">
            <template #default="{ row, expanded }">
              <el-row>
                <el-col :style="{ marginBottom: '20px' }">
                  <div class="contact-item">
                    <template v-if="row.address && row.address.length">
                      <div class="contact">
                        <span class="name">地址</span>
                      </div>
                      <el-form ref="form" :model="{}" label-width="0" label-position="left">
                        <el-form-item v-for="item in row.address" :key="`address-${item}`" style="margin-bottom: 0px">
                          <span class="form-content">{{ item }}</span>
                        </el-form-item>
                      </el-form>
                    </template>
                    <div class="contact" v-else>
                      <span class="name">{{ $t("location.No address") }}</span>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <ModelExpand
                v-if="row.id && expanded"
                :key="row.id"
                type="location"
                :id="row.id"
                :create="{
                  contact: !userInfo.hasPermission(资产管理中心_场所_分配联系人),
                }"
                :viewer="{
                  contact: !userInfo.hasPermission(资产管理中心_联系人_查看联系人),
                }"
                :remove="{
                  contact: !userInfo.hasPermission(资产管理中心_场所_分配联系人),
                }"
              ></ModelExpand>
              <!-- <bind-contacts v-loading="row.loading" :ref="`contacts-${row.id}`" :row="row" @handleAssignContacts="handleAssignContacts" @handleDelContact="handleDelContact" /> -->
            </template>
          </el-table-column>
          <!-- searchByDescription
searchByArea
searchByRegion
searchByZoneId
searchByExternalId -->
          <!-- <el-table-column prop="name" label="名称" :formatter="formatterTable" align="left"></el-table-column> -->
          <TableColumn type="condition" :prop="`name`" :label="$t('location.Name')" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="handleRefreshLocationTable()" :formatter="formatterTable"> </TableColumn>
          <!-- <el-table-column prop="description" label="描述" :formatter="formatterTable" align="left"></el-table-column> -->
          <TableColumn type="condition" :prop="`description`" :label="$t('location.Description')" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByDescription" :filters="$filter0" @filter-change="handleRefreshLocationTable()" :formatter="formatterTable"> </TableColumn>

          <!-- <el-table-column prop="area" label="区域" :formatter="formatterTable" align="left"></el-table-column> -->
          <TableColumn type="condition" :prop="`area`" :label="$t('location.Region')" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByArea" :filters="$filter0" @filter-change="handleRefreshLocationTable()" :formatter="formatterTable"> </TableColumn>

          <!-- <el-table-column prop="regionId" label="地区" :formatter="formatterTable" align="left"></el-table-column> -->
          <TableColumn type="condition" :prop="`regionId`" :label="$t('location.Area')" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByRegion" :filters="$filter0" @filter-change="handleRefreshLocationTable()" :formatter="formatterTable"> </TableColumn>

          <!-- <el-table-column prop="address" label="地址" :formatter="formatterTable" align="left" >
            <template></template>
          </el-table-column> -->
          <!-- <el-table-column prop="zoneId" label="时区" :formatter="formatterTable" align="left"></el-table-column> -->
          <TableColumn type="condition" :prop="`zoneId`" :label="$t('location.Time Zone')" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByZoneId" :filters="$filter0" @filter-change="handleRefreshLocationTable()" :formatter="formatterTable"> </TableColumn>

          <!-- <el-table-column prop="externalId" label="外部ID" :formatter="formatterTable" align="left"></el-table-column> -->
          <TableColumn type="condition" :prop="`id`" :label="`ID`" :formatter="formatterTable"> </TableColumn>
          <!-- :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByExternalId" :filters="$filter0" @filter-change="handleRefreshLocationTable()" -->

          <TableColumn type="enum" :label="$t('location.Active')" :min-width="100" :showOverflowTooltip="true" show-filter v-model:filtered-value="searchForm.active" :filters="['true', 'false'].map((v) => ({ value: v, text: v === 'true' ? $t('location.Activation') : $t('location.Not Active') }))" @filter-change="handleRefreshLocationTable()">
            <template #default="{ row }">
              <el-text :type="row.active == true ? 'success' : 'info'">{{ row.active == true ? $t("location.Activation") : $t("location.Not Active") }}</el-text>
            </template>
          </TableColumn>

          <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="120">
            <template #default="{ row }">
              <span>
                <el-link v-if="row.hasPermissionIds.includes(资产管理中心_场所_编辑)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleOpenEdit(row)">{{ $t("glob.edit") }}</el-link>
              </span>
              <span>
                <el-link v-if="row.hasPermissionIds.includes(资产管理中心_场所_删除)" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleDelLocation(row)">{{ $t("glob.delete") }}</el-link>
              </span>
              <span>
                <!-- 场所('603899933644292096') -->
                <el-link :type="row.hasPermissionIds.includes(资产管理中心_场所_安全) ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="row.hasPermissionIds.includes(资产管理中心_场所_安全) ? loading : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
              </span>
              <!-- <el-button type="text" @click="handleOpenEdit(row)">编辑</el-button>
              <el-button type="text" textColor="danger" @click="handleDelLocation(row)">删除</el-button> -->
            </template>
          </el-table-column>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
  <Editor ref="editorRef" title="时区"></Editor>
  <location-edit ref="locationEditRef" @refresh="handleRefreshLocationTable" />
  <!-- <assign-contacts ref="assignContactsRef" @submitForm="handleSubmitAssignContacts" /> -->
  <el-dialog v-model="dialogVisibleshow" title="查看安全目录" width="500" :before-close="handleClose">
    <treeAuth :proptreeId="containerId" :treeStyle="treeStyle" ref="treeAuthRef"></treeAuth>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisibleshow = false">取消</el-button>
      </div>
      <!-- <assign-contacts ref="assignContactsRef" @submitForm="handleSubmitAssignContacts" /> -->
    </template>
  </el-dialog>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import zone from "@/views/pages/common/zone.json";
import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
import treeAuth from "@/components/treeAuth/index.vue";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete, Download } from "@element-plus/icons-vue";

import deviceCreate from "./deviceCreate.vue";
// import bindContacts from "module";
// import assignContacts from "module";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMessageBox } from "element-plus";
import pageTemplate from "@/components/pageTemplate.vue";
import Editor from "./Editor.vue";

import locationEdit from "./locationEdit.vue";
import ModelExpand from "@/views/pages/modelExpand/Model.vue";
// import bindContacts from "@/components/bindContacts/vIndex";
// import assignContacts from "@/components/bindContacts/assignContacts";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";

import TableColumn from "@/components/tableColumn/TableColumn.vue";

/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";

import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";
import { getLocationsTenantCurrent, delLocationsTenantCurrent, setLocationsContacts, getLocationsContacts, delLocationsContacts, type Locations as DataItem } from "@/views/pages/apis/locationManang";
import { getRegionsTenantCurrent, delRegionsById } from "@/views/pages/apis/regionManage";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
import { 资产管理中心_场所_可读, 资产管理中心_场所_新增, 资产管理中心_场所_编辑, 资产管理中心_场所_删除, 资产管理中心_场所_分配联系人, 资产管理中心_场所_分配行动策略, 资产管理中心_场所_安全, 资产管理中心_联系人_可读, 资产管理中心_联系人_查看联系人 } from "@/views/pages/permission";
const ctx = getCurrentInstance()!;
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "locationManang" });
const editorRef = ref<InstanceType<typeof Editor>>();
// const assignContactsRef = ref<InstanceType<typeof assignContacts>>();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));
const dialogVisibleshow = ref(false);
const containerId = ref("");
const siteConfig = useSiteConfig();
const userInfo = getUserInfo();
const locationEditRef = ref<InstanceType<typeof locationEdit>>();
const treeStyle = ref({
  pointerEvents: "none",
});

// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

// const assignContacts = ref<InstanceType<typeof AssignContacts>>();
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);

const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);

const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);

// 搜索关键字
const searchForm = ref<Record<string, string>>({
  eqName: [] /* 等于的场所名称 */,
  includeName: [] /* 包含的场所名称 */,
  nameFilterRelation: "AND" /* 场所名称过滤关系(AND,OR) */,
  neName: [] /* 不等于的场所名称 */,
  excludeName: [] /* 不包含的场所名称 */,

  eqDescription: [] /* 等于的场所描述 */,
  includeDescription: [] /* 包含的场所描述 */,
  descriptionFilterRelation: "AND" /* 场所描述过滤关系(AND,OR) */,
  neDescription: [] /* 不等于的场所描述 */,
  excludeDescription: [] /* 不包含的场所描述 */,

  eqArea: [] /* 等于的场所区域 */,
  includeArea: [] /* 包含的场所区域 */,
  areaFilterRelation: "AND" /* 场所区域过滤关系(AND,OR) */,
  neArea: [] /* 不等于的场所区域 */,
  excludeArea: [] /* 不包含的场所区域 */,

  eqRegion: [] /* 等于的场所地区 */,
  includeRegion: [] /* 包含的场所地区 */,
  regionFilterRelation: "AND" /* 场所地区过滤关系(AND,OR) */,
  neRegion: [] /* 不等于的场所地区 */,
  excludeRegion: [] /* 不包含的场所地区 */,

  eqZoneId: [] /* 等于的场所时区ID */,
  includeZoneId: [] /* 包含的场所时区ID */,
  zoneIdFilterRelation: "AND" /* 场所时区ID过滤关系(AND,OR) */,
  neZoneId: [] /* 不等于的场所时区ID */,
  excludeZoneId: [] /* 不包含的场所时区ID */,

  eqExternalId: [] /* 等于的场所外部ID */,
  includeExternalId: [] /* 包含的场所外部ID */,
  externalIdFilterRelation: "AND" /* 场所外部ID过滤关系(AND,OR) */,
  neExternalId: [] /* 不等于的场所外部ID */,
  excludeExternalId: [] /* 不包含的场所外部ID */,

  active: "",
});

const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByName) === "include") value0 = searchForm.value.includeName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = searchForm.value.excludeName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = searchForm.value.eqName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = searchForm.value.neName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = searchForm.value.includeName[searchForm.value.includeName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = searchForm.value.excludeName[searchForm.value.excludeName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = searchForm.value.eqName[searchForm.value.eqName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = searchForm.value.neName[searchForm.value.neName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: searchForm.value.nameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.nameFilterRelation = v.relation;
    searchForm.value.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByDescription = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByDescription) === "include") value0 = searchForm.value.includeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "exclude") value0 = searchForm.value.excludeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "eq") value0 = searchForm.value.eqDescription[0] || "";
    if (toValue(searchType0ByDescription) === "ne") value0 = searchForm.value.neDescription[0] || "";
    let value1 = "";
    if (toValue(searchType1ByDescription) === "include") value1 = searchForm.value.includeDescription[searchForm.value.includeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "exclude") value1 = searchForm.value.excludeDescription[searchForm.value.excludeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "eq") value1 = searchForm.value.eqDescription[searchForm.value.eqDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "ne") value1 = searchForm.value.neDescription[searchForm.value.neDescription.length - 1] || "";
    return {
      type0: toValue(searchType0ByDescription),
      type1: toValue(searchType1ByDescription),
      relation: searchForm.value.descriptionFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByDescription.value = v.type0 as typeof searchType0ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchType1ByDescription.value = v.type1 as typeof searchType1ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.descriptionFilterRelation = v.relation;
    searchForm.value.includeDescription = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeDescription = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqDescription = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neDescription = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByArea = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByArea = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByArea = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByArea) === "include") value0 = searchForm.value.includeArea[0] || "";
    if (toValue(searchType0ByArea) === "exclude") value0 = searchForm.value.excludeArea[0] || "";
    if (toValue(searchType0ByArea) === "eq") value0 = searchForm.value.eqArea[0] || "";
    if (toValue(searchType0ByArea) === "ne") value0 = searchForm.value.neArea[0] || "";
    let value1 = "";
    if (toValue(searchType1ByArea) === "include") value1 = searchForm.value.includeArea[searchForm.value.includeArea.length - 1] || "";
    if (toValue(searchType1ByArea) === "exclude") value1 = searchForm.value.excludeArea[searchForm.value.excludeArea.length - 1] || "";
    if (toValue(searchType1ByArea) === "eq") value1 = searchForm.value.eqArea[searchForm.value.eqArea.length - 1] || "";
    if (toValue(searchType1ByArea) === "ne") value1 = searchForm.value.neArea[searchForm.value.neArea.length - 1] || "";
    return {
      type0: toValue(searchType0ByArea),
      type1: toValue(searchType1ByArea),
      relation: searchForm.value.areaFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByArea.value = v.type0 as typeof searchType0ByArea extends import("vue").Ref<infer T> ? T : string;
    searchType1ByArea.value = v.type1 as typeof searchType1ByArea extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.areaFilterRelation = v.relation;
    searchForm.value.includeArea = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeArea = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqArea = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neArea = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByRegion = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByRegion = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByRegion = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByRegion) === "include") value0 = searchForm.value.includeRegion[0] || "";
    if (toValue(searchType0ByRegion) === "exclude") value0 = searchForm.value.excludeRegion[0] || "";
    if (toValue(searchType0ByRegion) === "eq") value0 = searchForm.value.eqRegion[0] || "";
    if (toValue(searchType0ByRegion) === "ne") value0 = searchForm.value.neRegion[0] || "";
    let value1 = "";
    if (toValue(searchType1ByRegion) === "include") value1 = searchForm.value.includeRegion[searchForm.value.includeRegion.length - 1] || "";
    if (toValue(searchType1ByRegion) === "exclude") value1 = searchForm.value.excludeRegion[searchForm.value.excludeRegion.length - 1] || "";
    if (toValue(searchType1ByRegion) === "eq") value1 = searchForm.value.eqRegion[searchForm.value.eqRegion.length - 1] || "";
    if (toValue(searchType1ByRegion) === "ne") value1 = searchForm.value.neRegion[searchForm.value.neRegion.length - 1] || "";
    return {
      type0: toValue(searchType0ByRegion),
      type1: toValue(searchType1ByRegion),
      relation: searchForm.value.regionFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByRegion.value = v.type0 as typeof searchType0ByRegion extends import("vue").Ref<infer T> ? T : string;
    searchType1ByRegion.value = v.type1 as typeof searchType1ByRegion extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.regionFilterRelation = v.relation;
    searchForm.value.includeRegion = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeRegion = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqRegion = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neRegion = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByZoneId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByZoneId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByZoneId = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByZoneId) === "include") value0 = searchForm.value.includeZoneId[0] || "";
    if (toValue(searchType0ByZoneId) === "exclude") value0 = searchForm.value.excludeZoneId[0] || "";
    if (toValue(searchType0ByZoneId) === "eq") value0 = searchForm.value.eqZoneId[0] || "";
    if (toValue(searchType0ByZoneId) === "ne") value0 = searchForm.value.neZoneId[0] || "";
    let value1 = "";
    if (toValue(searchType1ByZoneId) === "include") value1 = searchForm.value.includeZoneId[searchForm.value.includeZoneId.length - 1] || "";
    if (toValue(searchType1ByZoneId) === "exclude") value1 = searchForm.value.excludeZoneId[searchForm.value.excludeZoneId.length - 1] || "";
    if (toValue(searchType1ByZoneId) === "eq") value1 = searchForm.value.eqZoneId[searchForm.value.eqZoneId.length - 1] || "";
    if (toValue(searchType1ByZoneId) === "ne") value1 = searchForm.value.neZoneId[searchForm.value.neZoneId.length - 1] || "";
    return {
      type0: toValue(searchType0ByZoneId),
      type1: toValue(searchType1ByZoneId),
      relation: searchForm.value.zoneIdFilterRelation,
      value0,
      value1,
      // input0: "",
      input0: zone.reduce((p, c) => (p.append(c.zoneId, c.displayName), p), new URLSearchParams()).toString(),
      // input1: "",
      input1: zone.reduce((p, c) => (p.append(c.zoneId, c.displayName), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByZoneId.value = v.type0 as typeof searchType0ByZoneId extends import("vue").Ref<infer T> ? T : string;
    searchType1ByZoneId.value = v.type1 as typeof searchType1ByZoneId extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.zoneIdFilterRelation = v.relation;
    searchForm.value.includeZoneId = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeZoneId = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqZoneId = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neZoneId = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByExternalId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByExternalId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByExternalId = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByExternalId) === "include") value0 = searchForm.value.includeExternalId[0] || "";
    if (toValue(searchType0ByExternalId) === "exclude") value0 = searchForm.value.excludeExternalId[0] || "";
    if (toValue(searchType0ByExternalId) === "eq") value0 = searchForm.value.eqExternalId[0] || "";
    if (toValue(searchType0ByExternalId) === "ne") value0 = searchForm.value.neExternalId[0] || "";
    let value1 = "";
    if (toValue(searchType1ByExternalId) === "include") value1 = searchForm.value.includeExternalId[searchForm.value.includeExternalId.length - 1] || "";
    if (toValue(searchType1ByExternalId) === "exclude") value1 = searchForm.value.excludeExternalId[searchForm.value.excludeExternalId.length - 1] || "";
    if (toValue(searchType1ByExternalId) === "eq") value1 = searchForm.value.eqExternalId[searchForm.value.eqExternalId.length - 1] || "";
    if (toValue(searchType1ByExternalId) === "ne") value1 = searchForm.value.neExternalId[searchForm.value.neExternalId.length - 1] || "";
    return {
      type0: toValue(searchType0ByExternalId),
      type1: toValue(searchType1ByExternalId),
      relation: searchForm.value.externalIdFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByExternalId.value = v.type0 as typeof searchType0ByExternalId extends import("vue").Ref<infer T> ? T : string;
    searchType1ByExternalId.value = v.type1 as typeof searchType1ByExternalId extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.externalIdFilterRelation = v.relation;
    searchForm.value.includeExternalId = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeExternalId = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqExternalId = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neExternalId = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const zoneObj = ref({});
const tableLocationData = ref([]);
const tableData = ref<DataItem[]>([]);
const paging = reactive({
  pageNumber: 1,
  pageSize: 50,
  total: 0,
});

const allRegion = ref([]);
const allRegionByPage = ref([]);
const allRegionSelect = ref([]);

const refresh = inject("refresh");

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  handleRefreshRegionTable();
  handleRefreshLocationTable();
}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
// function handleDelContact(v) {
//   ElMessageBox.confirm(`确定删除该${v.tab.label}"${v.contact.name}"`, "提示", {
//     confirmButtonText: "确定",
//     cancelButtonText: "取消",
//     type: "warning",
//   }).then(() => {
//     const params = {
//       contactId: v.contact.id,
//       contactType: v.tab.name,
//     };
//     delLocationsContacts(params, v.id).then(({ success, data }) => {
//       if (success) {
//         ElMessage.success("操作成功");
//         expandChange(tableLocationData.value.find((i) => i.id === v.id));
//       } else ElMessage.error(JSON.parse(data)?.message || "操作失败");
//     });
//   });
// }
// function expandChange(row: DataItem, expand: DataItem[]) {
//   nextTick(() => {
//     setTimeout(() => {
//       if (!ctx.refs[`contacts-${row.id}`]) return false;
//       if (!ctx.refs[`contacts-${row.id}`].tabs || !ctx.refs[`contacts-${row.id}`].tabs.length) ctx.refs[`contacts-${row.id}`].getTabs();
//       getLocationsContacts({ id: row.id }).then(({ success, data }) => {
//         if (success) {
//           nextTick(() => {
//             ctx.refs[`contacts-${row.id}`]?.setContacts(data);
//           });
//         }
//       });
//     }, 200);
//   });
// }
// function handleSubmitAssignContacts(v) {
//   setLocationsContacts(v.params, v.id).then(({ success, data }) => {
//     if (success) {
//       ElMessage.success("操作成功");
//       // expandChange(tableLocationData.value.find((i) => i.id === v.id));
//       assignContactsRef.value?.beforeClose();
//     } else ElMessage.error(JSON.parse(data)?.message || "操作失败");
//   });
// }
// function handleAssignContacts({ rowId, type, contacts }) {
//   assignContactsRef.value?.open(rowId, type, contacts);
// }
function handleDelLocation({ id, name }) {
  ElMessageBox.confirm(`确定删除${name}?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      const { success, data, message } = await delLocationsTenantCurrent({ id });
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
      handleRefreshLocationTable();
    } catch (error) {
      error instanceof Error && ElMessage.error(error.message);
    }
  });
}
function handleRefreshLocationTable() {
  const params = {
    pageNumber: paging.pageNumber,
    pageSize: paging.pageSize,
    // keyword: searchForm.value.keyword,
    ...searchForm.value,
  };
  getLocationsTenantCurrent(params).then(({ success, data, total }) => {
    if (success) {
      paging.total = Number(total);
      tableLocationData.value = data.map((v) => v);
      if (!tableLocationData.value.length && paging.pageNumber !== 1) {
        paging.pageNumber = 1;
        handleRefreshLocationTable();
      }
    } else console.error(JSON.parse(data)?.message || "列表获取失败");
  });
}
function formatterTable(_row, _col, _v) {
  // // console.log(v);
  switch (_col.property) {
    case "regionId":
      let val = allRegion.value.find((i) => i.id == _v);
      return val?.parentId == null ? "--" : val.name;
    // return val.parentId == null ? val : topParent(val.parentId);
    // return allRegion.value.find((i) => i.id === v)?.name || "--";
    case "area":
      let name = "";
      allRegion.value.forEach((v, i) => {
        if (v.id === _row.regionId) {
          name = topParent(v.id)?.name || "--";
        }
      });
      // return "";
      return name;
    case "zoneId":
      return zone.find((i) => i.zoneId === _v)?.displayName || "--";
    default:
      return _v || "--";
  }
}

function topParent(id) {
  var obj = allRegion.value.find((v) => v.id == id) || {};
  return !obj.parentId ? obj : topParent(obj.parentId);
}
function handleOpenEdit(row = {}) {
  locationEditRef.value.open(row);
}
function handleSizeChange(v) {
  paging.pageSize = v;
  handleRefreshLocationTable();
}
function handleCurrentPageChange(v) {
  paging.pageNumber = v;
  handleRefreshLocationTable();
}
function handleRefreshRegionTable() {
  getRegionsTenantCurrent({ sort: "createdTime,desc", active: true }).then(({ success, data }) => {
    if (success) {
      tableData.value = setTableData(data);
      allRegionSelect.value = JSON.parse(JSON.stringify(tableData.value));
      tableData.value = setTableDataByPage(tableData.value);
      if (!tableData.value.length && paging.pageNumber !== 1) {
        paging.pageNumber = 1;
        handleRefreshRegionTable();
      }
    } else console.error(JSON.parse(data)?.message || "列表获取失败");
  });
}
function setTableDataByPage(data) {
  let result = [];
  for (var i = 0, len = data.length; i < len; i += paging.pageSize) {
    result.push(data.slice(i, i + paging.pageSize));
  }
  allRegionByPage.value = result;
  return result[paging.pageNumber - 1] || [];
}
function setTableData(data) {
  allRegion.value = JSON.parse(JSON.stringify(data));
  let _formatter = (list) => {
    for (let i = 0; i < list.length; i++) {
      list[i].children = [];
      list[i].isEdit = false;
      let _filter = allRegion.value.filter((v) => {
        return list[i].id === v.parentId;
      });
      if (_filter && _filter?.length) {
        list[i].children = _filter;
        _formatter(list[i].children);
      }
    }
  };
  let result = data.filter((v) => !v.parentId);
  _formatter(result);
  _formatter = null;
  return result;
}
function handleDelRegion(row) {
  ElMessageBox.confirm(`确定删除区域"${row.name}"?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // delRegionsById({ id: row.id }).then(({ success, data }) => {
      //   if (success) {
      //     ElMessage.success("操作成功");
      //     refresh ? refresh() : handleRefreshRegionTable();
      //   } else ElMessage.error(JSON.parse(data)?.message || "操作失败");
      // });
    })
    .catch(() => {
      /* code */
    });
}
function handleAddChild(row) {
  const editData = row.children.find(({ id }) => !id);
  const newRegion = Object.assign(
    {
      parentId: row.id,
      isEdit: true,
      label: "",
      name: "",
      description: "",
      externalId: "",
    },
    editData || {}
  );
  // row.children = row.children.filter((v) => v.id)
  if (row.isExpend) {
    row.children.unshift(newRegion);
  } else {
    ctx.refs[`${row.parentId}-table`]?.toggleRowExpansion(row);
    row.children.unshift(newRegion);
  }
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var.scss";

.contact-item {
  padding: 12px 24px !important;
  background: #f7f8fa;
  .contact {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .elstyle-button--text {
    font-family: "PingFang SC";
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
  }
  .elstyle-form-item {
    margin-bottom: 0;
  }
  // ::v-deep .elstyle-form-item__label {
  //   font-family: "PingFang SC";
  //   font-style: normal;
  //   font-weight: 400;
  //   font-size: 14px;
  //   color: #86909c;
  // }
  .form-content {
    font-family: "PingFang SC";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    color: map-get($text-color, regular);
  }
  .name {
    font-family: "PingFang SC";
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: $color-black;
  }
}
</style>
