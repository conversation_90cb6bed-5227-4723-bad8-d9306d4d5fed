// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import zone from "@/views/pages/common/contactsZone.json";
import countries from "@/views/pages/common/countries.json";
import { getRegionsTenantCurrent } from "@/views/pages/apis/regionManage";
export default {
  inject: ["refresh"],
  data() {
    return {
      paging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      zone,
      countries,
      allRegion: [],
      allRegionByPage: [],
      allRegionSelect: [],
    };
  },
  methods: {
    handleRefreshRegionTable() {
      getRegionsTenantCurrent({ sort: "createdTime,desc", active: true }).then(({ success, data }) => {
        if (success) {
          this.tableData = this.setTableData(data);
          this.allRegionSelect = JSON.parse(JSON.stringify(this.tableData)).filter((v) => v.active);

          this.paging.total = this.tableData.length;
          this.tableData = this.setTableDataByPage(this.tableData);
          if (!this.tableData.length && this.paging.pageNumber !== 1) {
            this.paging.pageNumber = 1;
            this.handleRefreshRegionTable();
          }
          // this.allRegionSelect.push({
          //   name: "不选择",
          //   label: "不选择",
          //   id: null,
          //   children: [],
          // });
          // console.log(this.allRegionSelec);
        } else console.error(JSON.parse(data)?.message || "列表获取失败");
      });
    },
    setTableDataByPage(data) {
      const result = [];
      for (let i = 0, len = data.length; i < len; i += this.paging.pageSize) {
        result.push(data.slice(i, i + this.paging.pageSize));
      }
      this.allRegionByPage = result;
      return result[this.paging.pageNumber - 1] || [];
    },
    setTableData(data) {
      this.allRegion = JSON.parse(JSON.stringify(data));
      let _formatter = (list) => {
        for (let i = 0; i < list.length; i++) {
          list[i].children = [];
          list[i].isEdit = false;
          const _filter = this.allRegion.filter((v) => {
            return list[i].id === v.parentId;
          });
          if (_filter && _filter?.length) {
            list[i].children = _filter;
            _formatter(list[i].children);
          }
        }
      };
      const result = data.filter((v) => !v.parentId);
      _formatter(result);
      _formatter = null;
      // console.log("result", result);
      return result;
    },
  },
};
