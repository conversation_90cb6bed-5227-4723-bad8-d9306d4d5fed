<template>
  <el-dialog :title="$t(isEdit ? 'glob.Edit Data' : 'glob.New Data', { value: $t('location.Location') })" v-model="dialogFormVisible" :before-close="beforeClose" class="operate-dialog">
    <!-- <el-dialog :title="`${isEdit ? '编辑' : '新增'}场所`" v-model="dialogFormVisible" :before-close="beforeClose" class="operate-dialog"> -->
    <!-- <el-scrollbar class="dialog-scrollbar" :height="height / 2" :style="{ width: '100%' }"> -->
    <el-form ref="form" :model="form" label-width="120px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item :label="$t('location.Name')" prop="name">
            <el-input v-model="form.name" :placeholder="$t('location.Please enter the name')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('location.Time Zone')" prop="zoneId">
            <el-select v-model="form.zoneId" :placeholder="$t('location.Please choose a time zone')" filterable clearable :style="{ width: '100%' }">
              <el-option v-for="item in zone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="24" :lg="12">
          <el-form-item :label="$t('location.Active')" prop="active">
            <el-checkbox v-model="form.active"></el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('location.Description')" prop="description">
            <el-input type="textarea" v-model="form.description" :placeholder="$t('location.Description')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('location.External Id')" prop="externalId">
            <el-input v-model="form.externalId" :placeholder="$t('location.External Id')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('location.Country')" prop="country">
            <el-select v-model="form.country" :placeholder="$t('location.Please choose a country')" filterable clearable :style="{ width: '100%' }" @change="changeQueryProvince">
              <el-option v-for="item in countries" :key="item.code" :label="$t('location.Country') == '国家' ? item.cnName : item.enName" :value="item.code"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.country === 'CN'">
          <el-form-item :label="$t('location.province')" prop="province">
            <el-select v-model="form.province" :placeholder="$t('location.Please select the province')" filterable clearable :style="{ width: '100%' }" @change="changeQueryCity">
              <el-option v-for="(item, index) in ProvinceSelect" :key="index" :label="isZh ? item.cnName : item.enName" :value="item.indexCode"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('location.city')" prop="city" v-if="form.country === 'CN'">
            <el-select v-model="form.city" :placeholder="$t('location.Please choose a city')" filterable clearable :style="{ width: '100%' }">
              <el-option v-for="(item, index) in CitySelect" :key="index" :label="isZh ? item.cnName : item.enName" :value="item.indexCode"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="regionFormLabel" prop="regionId">
            <el-cascader v-model="form.regionId" :options="allRegionSelect" :props="zoneProps" :placeholder="$t('location.Please choose a region')" :style="{ width: '100%' }"></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('location.Address')" prop="address">
            <el-input v-for="item in 3" :key="`address-${item}`" v-model="form.address[item - 1]" :placeholder="$t('location.Please enter address')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24"></el-col>
      </el-row>
    </el-form>
    <!-- </el-scrollbar> -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="beforeClose">{{ $t("glob.Cancel") }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t("glob.Confirm") }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { defineComponent } from "vue";
import { ElMessage } from "element-plus";
import mixin from "./js/minxin";
import city from "@/views/pages/common/city.json";
// import regionMixin from "../regionManage/js/mixin";
import { locationsTenantCurrent, putLocationsTenantCurrent, getRegion } from "@/views/pages/apis/locationManang";
import { getTenantInfo } from "@/views/pages/apis/tenant";
import { el } from "element-plus/es/locale";
export default defineComponent({
  components: {},
  mixins: [mixin],
  inject: ["height"],
  emits: ["refresh"],

  data() {
    return {
      dialogFormVisible: false,
      form: {
        address: [],
        regionId: [],
        active: true,
        zoneId: "",
      },
      zoneProps: {
        checkStrictly: true,
        value: "id",
        label: "name",
        children: "children",
      },
      isEdit: false,
      cityJosn: city,
      containerId: "",
      ProvinceSelect: [],
      CitySelect: [],
    };
  },
  computed: {
    rules() {
      return {
        name: [
          {
            required: true,
            message: this.$t("location.Please enter the name"),
            trigger: ["blur", "change"],
          },
        ],

        regionId: [
          {
            required: this.isEdit ? false : true,
            message: this.$t("location.Please choose a region"),
            trigger: ["blur", "change"],
          },
        ],
      };
    },
    regionFormLabel() {
      try {
        return (this.allRegion.find((v) => v.id === this.form.regionId[this.form.regionId.length - 1]) || {}).label || this.$t("location.Region");
      } catch (err) {
        return this.$t("location.Region");
      }
    },
    isZh() {
      return this.$t("location.province") === "省份";
    },
  },
  watch: {},
  created() {
    this.handleRefreshRegionTable();
  },
  mounted() {
    // console.log(this.cityJosn);
    // this.$nextTick(() => {
    //   this.handleRefreshRegionTable();
    //   // this.allRegionSelect.push({
    //   //   id: "123123",
    //   //   name: "空",
    //   //   children: [],
    //   // });
    //   // console.log(this.allRegionSelect);
    // });
  },
  methods: {
    async changeQueryProvince(clearCity = true) {
      this.provinceloading = true;
      const { success, message, data } = await getRegion({ parentIndexCode: 100000 });
      if (!success) throw Object.assign(new Error(message), { success, data });

      if (clearCity) this.form.city = ""; // 控制是否清空 city
      this.ProvinceSelect = data || [];
    },

    async changeQueryCity(e) {
      this.form.city = ""; // 每次省份切换，清空城市选择
      const { success, message, data } = await getRegion({ parentIndexCode: e });
      if (!success) throw Object.assign(new Error(message), { success, data });
      this.CitySelect = data || [];
    },

    async runningInit() {
      if (!this.isEdit) {
        (async (req) => {
          const { success, message, data } = await getTenantInfo(req);
          if (!success) throw Object.assign(new Error(message), { success, data });

          this.containerId = data.containerId;
          this.form.zoneId = data.zoneId || "China Standard Time";
        })({});
      }
    },
    // open(row = {}) {
    //   this.dialogFormVisible = true;
    //   this.isEdit = !!row.id;
    //   if (this.isEdit) {
    //     this.$nextTick(() => {
    //       this.form = JSON.parse(
    //         JSON.stringify({
    //           id: row.id || null,
    //           name: row.name,
    //           description: row.description,
    //           externalId: row.externalId,
    //           address: row.address,
    //           zoneId: row.zoneId,
    //           country: row.country,
    //           regionId: row.regionId ? this.setShowRegionId(row.regionId) : [],
    //           active: row.active,
    //           province: row.province,
    //           city: row.city,
    //         })
    //       );
    //       for (let key in this.cityJosn[0]["100000"]) {
    //         if (this.cityJosn[0]["100000"][key] == row.province) {
    //           this.form.province = key;
    //         }
    //       }
    //       for (let key in this.cityJosn[0][this.form.province]) {
    //         if (this.cityJosn[0][this.form.province][key] == row.city) {
    //           this.form.city = key;
    //         }
    //       }
    //     });
    //     if (!row.zoneId) {
    //       this.runningInit();
    //     }
    //   }else{
    //     this.runningInit();
    //   }
    // },
    async open(row = {}) {
      this.dialogFormVisible = true;
      this.isEdit = !!row.id;
      // 初始化表单数据
      if (this.isEdit) {
        this.$nextTick(() => {
          this.form = {
            id: row.id || null,
            name: row.name,
            description: row.description,
            externalId: row.externalId,
            address: row.address,
            zoneId: row.zoneId,
            country: row.country === "100000" ? "CN" : row.country,
            regionId: row.regionId ? this.setShowRegionId(row.regionId) : [],
            active: row.active,
            province: "",
            city: "",
          };
        });
        if (row.country === "100000") {
          // 先加载省份列表
          await this.changeQueryProvince(false);

          // 省份匹配
          const provinceMatch = this.ProvinceSelect.find((p) => p.indexCode === row.province);
          if (provinceMatch) {
            this.form.province = provinceMatch.indexCode;

            // 加载城市
            await this.changeQueryCity(provinceMatch.indexCode);

            // 城市匹配
            const cityMatch = this.CitySelect.find((c) => c.indexCode === row.city);
            if (cityMatch) {
              this.form.city = cityMatch.indexCode;
            }
          }
        }
      }
      // 统一调用 runningInit
      if (!row.zoneId || !this.isEdit) {
        this.runningInit();
      }
    },

    // 工具函数：根据值查找对应的键
    // findKeyByValue(obj, value) {
    //   for (let key in obj) {
    //     if (obj[key] === value) {
    //       return key;
    //     }
    //   }
    //   return null;
    // },
    setShowRegionId(id) {
      let result = [id];
      let current = this.allRegion.find((v) => v.id === id);
      let forRegion = (c) => {
        const r = this.allRegion.find((v) => v.id === c?.parentId) || null;
        if (r) {
          result.unshift(r.id);
          if (r.parentId) forRegion(r);
        }
      };
      if (current?.parentId) forRegion(current);
      return result;
    },
    beforeClose(done) {
      this.$refs.form.clearValidate();
      this.$refs.form.resetFields();
      if (done instanceof Function) done();
      else this.dialogFormVisible = false;
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) return valid;
        const params = {
          ...this.form,
          country: this.form.country === "CN" ? "100000" : this.form.country,
          regionId: (this.form.regionId && this.form.regionId[this.form.regionId.length - 1]) || null,
          zoneId: this.form.zoneId == "" ? null : this.form.zoneId,
          containerId: this.containerId,

          countryName: (this.countries.find((v) => v.code === this.form.country) || {}).cnName || "",
          provinceName: (this.ProvinceSelect.find((v) => v.indexCode === this.form.province) || {}).cnName || "",
          cityName: (this.CitySelect.find((v) => v.indexCode === this.form.city) || {}).cnName || "",
        };
        (this.isEdit ? putLocationsTenantCurrent : locationsTenantCurrent)(params)
          .then(({ success, data }) => {
            // console.log(success, data);
            if (success) {
              ElMessage.success(this.$t("axios.Operation successful"));
              this.beforeClose();
              this.$emit("refresh");
            } else ElMessage.error(JSON.parse(data)?.message || this.$t("axios.Operation failure"));
          })
          .catch((err) => {
            ElMessage.error(err?.message || this.$t("axios.Operation failure"));
          });
      });
    },
    expose: ["open", "beforeClose"],
  },
});
</script>

<style lang="scss" scoped>
.dialog-scrollbar .el-scrollbar__wrap {
  overflow-x: hidden;
}
</style>
