<template>
  <div class="tw-my-4">
    <div class="tw-mb-4 tw-flex tw-items-center tw-justify-between">
      <span class="tw-font-bold">分配设备</span>
      <el-button type="primary" :icon="Plus" @click="handleAddItem" :loading="butLoading" v-if="userInfo.hasPermission(安全管理中心_密码钱包_分配设备)">分配设备</el-button>
    </div>
    <el-table :data="state.tableData" border stripe v-loading="state.loading">
      <el-table-column type="default" prop="name" label="设备"></el-table-column>
      <el-table-column type="default" prop="locationDesc" label="场所"></el-table-column>
      <el-table-column type="default" label="操作" width="200" v-if="userInfo.hasPermission(安全管理中心_密码钱包_分配设备)">
        <template #default="{ row }">
          <el-popconfirm :title="`确定移除设备'${row.name}'吗?`" @confirm="handleDelItem(row)">
            <template #reference>
              <el-button type="danger" link>移除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, h, defineComponent, nextTick, computed, inject } from "vue";

import { Plus } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, ElForm, ElFormItem, ElSelect, ElOption } from "element-plus";

import { getEquipmentQuery as getData, getResourceNotContain as getDevice, addEquipment as addData, delEquipment as delData, ResourceItem } from "@/views/pages/apis/passwordWallet";

import getUserInfo from "@/utils/getUserInfo";

import { 安全管理中心_密码钱包_分配设备, 安全管理中心_设备_分配密码钱包 } from "@/views/pages/permission";

const userInfo = getUserInfo();

interface Props {
  parentId: string;
  containerId: string;
  tenantId: string;
}

const props = withDefaults(defineProps<Props>(), {
  parentId: "",
  containerId: "",
  tenantId: "",
});

const tenantId = computed(() => props.tenantId || /* inject("tenantId") || */ "");
const containerId = computed(() => props.containerId || /* inject("containerId") || */ "");

// tenantId.value;
// containerId.value;
// console.log(tenantId.value, props.tenantId || inject("tenantId"));
// console.log(containerId.value, props.containerId || inject("containerId"));

const state = ref<Record<string, any>>({
  loading: false,
  tableData: [],
});

const butLoading = ref<boolean>(false);

async function handleDelItem(row) {
  try {
    const { success, message } = await delData({ parentId: props.parentId, id: row.id });
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    handleRefresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

const deviceId = ref<string[]>([]);
async function handleAddItem() {
  try {
    butLoading.value = true;
    const params = {
      containerId: containerId.value,
      tenantId: tenantId.value,
      ids: state.value.tableData.map((v) => v.deviceId),
    };
    let rusult: ResourceItem[] = [];
    if (userInfo.hasPermission(安全管理中心_设备_分配密码钱包)) {
      const { data, success, message } = await getDevice({ ...params });
      if (!success) throw new Error(message);
      rusult = data;
    }
    deviceId.value = [];
    await nextTick();
    butLoading.value = false;
    ElMessageBox({
      title: "选择设备",
      message: h(
        defineComponent({
          setup() {
            return () =>
              h(ElForm, { ref: "createFormRef", style: { width: "396px" }, model: {}, labelPosition: "left" }, [
                h(
                  ElFormItem,
                  { label: "设备", prop: "versionId", rules: [{ required: true, validator: (rule: any, value: any, callback: any) => (!deviceId.value ? callback(new Error("设备不能为空")) : callback()), trigger: "blur" }] },
                  h(
                    ElSelect,
                    {
                      "class": "tw-w-full",
                      "modelValue": deviceId.value,
                      "onUpdate:modelValue": ($event) => (deviceId.value = $event),
                      "filterable": true,
                      "multiple": true,
                    },
                    rusult.map((v) => h(ElOption, { key: `option-${v.id}`, label: v.name, value: v.id } /* [h("p", v.name), h("p", v.description)] */))
                  )
                ),
              ]);
          },
        })
      ),
      beforeClose: async (action, instance, done) => {
        if (action === "confirm") {
          try {
            if (!(deviceId.value instanceof Array) || !deviceId.value.length) throw new Error("设备不能为空");
            const { message, success } = await addData(
              { parentId: props.parentId },
              rusult.filter((v) => deviceId.value.includes(v.id))
            );
            if (!success) throw new Error(message);
            ElMessage.success("操作成功");
            handleRefresh();
            done();
          } catch (error) {
            error instanceof Error && ElMessage.error(error.message);
          }
        } else done();
      },
    }).catch(() => Promise.resolve("cancel"));
  } catch (error) {
    butLoading.value = false;
    error instanceof Error && ElMessage.error(error.message);
  }
}

async function handleRefresh() {
  try {
    state.value.loading = true;
    const { data, message, success } = await getData({ parentId: props.parentId });
    if (!success) throw new Error(message);
    state.value.tableData = data;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    state.value.loading = false;
  }
}

onMounted(() => {
  handleRefresh();
});
</script>
