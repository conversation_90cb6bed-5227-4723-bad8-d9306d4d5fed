<template>
  <div class="tw-my-4">
    <div class="tw-mb-4 tw-flex tw-items-center tw-justify-between">
      <span class="tw-font-bold">默认登录凭证</span>
      <el-button type="primary" :icon="Plus" @click="handleAddItem" v-if="userInfo.hasPermission(安全管理中心_密码钱包_编辑)">新增登录凭证</el-button>
    </div>
    <el-form ref="formRef" :model="state" :rules="rules">
      <el-table :data="state.tableData" border stripe v-loading="state.loading">
        <el-table-column type="default" prop="name" label="名称">
          <template #default="{ row, $index }">
            <el-form-item v-if="row.isEdit" :prop="`tableData[${$index}].name`" :rules="[{ required: row.isEdit, message: '名称不能为空', trigger: 'blur' }]">
              <el-input v-model="row.name"></el-input>
            </el-form-item>
            <div v-else>{{ row.name }}</div>
          </template>
        </el-table-column>
        <el-table-column type="default" prop="description" label="描述">
          <template #default="{ row, $index }">
            <el-form-item v-if="row.isEdit" :prop="`tableData[${$index}].description`">
              <el-input v-model="row.description"></el-input>
            </el-form-item>
            <div v-else>{{ row.description }}</div>
          </template>
        </el-table-column>
        <el-table-column type="default" prop="" label="Connector">
          <template #default="{ row, $index }">
            <el-form-item v-if="row.isEdit" :prop="`tableData[${$index}].connectors`" :rules="[{ required: row.isEdit, message: 'Connector不能为空', trigger: 'blur' }]">
              <el-select class="tw-w-full" v-model="row.connectors" multiple>
                <el-option v-for="connector in ['Telnet', 'SSH', 'Remote Desktop']" :key="`connector-${connector}`" :label="connector" :value="connector"></el-option>
              </el-select>
            </el-form-item>
            <div v-else>
              <el-tag v-for="tag in row.connectors" :key="`tag-${tag}`" class="tw-mb-2 tw-mr-2">{{ tag }}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column type="default" prop="" label="配置">
          <template #default="{ row, $index }">
            <el-form-item label="账号" :prop="`tableData[${$index}].userName`" :rules="[{ required: false, message: '账号不能为空', trigger: 'blur' }]">
              <template #label>账号</template>
              <el-input v-if="row.isEdit" v-model="row.userName" :disabled="!!row.id"></el-input>
              <div v-else>{{ row.userName }}</div>
            </el-form-item>
            <el-form-item label="密码" :prop="`tableData[${$index}].passWord`" :rules="[{ required: false, message: '密码不能为空', trigger: 'blur' }]">
              <template #label>密码</template>
              <el-input v-if="row.isEdit" v-model="row.passWord" type="password" show-password :disabled="!!row.id"></el-input>
              <div v-else>******</div>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column type="default" prop="" label="生效开始时间">
          <template #default="{ row, $index }">
            <el-form-item v-if="row.isEdit" :prop="`tableData[${$index}].startTime`" :rules="[{ required: false, message: '生效开始时间不能为空', trigger: 'blur' }]">
              <el-date-picker v-model="row.startTime" class="tw-w-full" type="date" value-format="YYYY-MM-DD" />
            </el-form-item>
            <div v-else>{{ row.startTime }}</div>
          </template>
        </el-table-column>
        <el-table-column type="default" prop="" label="生效结束时间">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isEdit"
              :prop="`tableData[${$index}].endTime`"
              :rules="[
                { required: false, message: '生效结束时间不能为空', trigger: 'blur' },
                { validator: validEndData, trigger: ['change', 'blur'] },
              ]"
            >
              <el-date-picker v-model="row.endTime" class="tw-w-full" type="date" value-format="YYYY-MM-DD" />
            </el-form-item>
            <div v-else>{{ row.endTime }}</div>
          </template>
        </el-table-column>
        <el-table-column type="default" prop="" label="操作" width="200" v-if="userInfo.hasPermission(安全管理中心_密码钱包_编辑)">
          <template #default="{ row }">
            <template v-if="!row.isEdit">
              <div>
                <el-button type="primary" link @click="handleViewAccountOpen(row)">展开</el-button>
                <el-button type="primary" link @click="handleSetItem(row)">编辑</el-button>
                <el-popconfirm :title="`确定删除登录凭证'${row.name}'吗?`" @confirm="handleDelItem(row)">
                  <template #reference>
                    <el-button type="danger" link>删除</el-button>
                  </template>
                </el-popconfirm>
              </div>
            </template>

            <template v-else>
              <div>
                <el-button type="primary" link @click="handleSubmit(row)">保存</el-button>
                <el-button type="primary" link @click="handleRefresh">取消</el-button>
              </div>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </el-form>

    <el-dialog
      v-model="dialogVisible"
      title="展开"
      width="500"
      append-to-body
      :before-close="
        (done) => {
          viewAccountForm.showPwd = false;
          done();
        }
      "
    >
      <el-form :model="viewAccountForm">
        <el-form-item :label="`账号`">
          <el-input :model-value="viewAccountForm.userName" disabled></el-input>
        </el-form-item>
        <el-form-item :label="`密码`" v-loading="viewAccountForm.passwordLoading">
          <el-input :model-value="viewAccountForm.passWord" disabled :type="!viewAccountForm.showPwd ? 'password' : 'text'"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleGetPwd" v-if="userInfo.hasPermission(安全管理中心_密码钱包_可读密码凭证)">{{ viewAccountForm.showPwd ? "隐 藏" : "展 开" }}</el-button>
          <el-button type="primary" @click="handleEditPwd" v-if="userInfo.hasPermission(安全管理中心_密码钱包_设置密码凭证)">编 辑</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogEditPasswordVisible" title="编辑" width="500" append-to-body>
      <el-form :model="editPasswordForm">
        <el-form-item :label="`账号`">
          <el-input v-model="editPasswordForm.userName"></el-input>
        </el-form-item>
        <el-form-item :label="`密码`">
          <el-input v-model="editPasswordForm.passWord" :type="'password'" show-password></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="() => (dialogEditPasswordVisible = false)">取 消</el-button>
          <el-button type="primary" @click="handleSubmitPassword">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, nextTick, computed, inject } from "vue";

import { Plus } from "@element-plus/icons-vue";

import { getLoginCredentials as getData, addLoginCredentials as addData, setLoginCredentials as setData, delLoginCredentials as delData, getAccountPassword } from "@/views/pages/apis/passwordWallet";
import type { LoginCredentialsItem } from "@/views/pages/apis/passwordWallet";

import { ElMessage, FormInstance } from "element-plus";

import getUserInfo from "@/utils/getUserInfo";

import validPwd from "./validPwd";

import { getMFAMethods, MFAMethod } from "@/api/system";

import { 安全管理中心_密码钱包_可读密码凭证, 安全管理中心_密码钱包_设置密码凭证, 安全管理中心_密码钱包_编辑 } from "@/views/pages/permission";

const userInfo = getUserInfo();
interface Props {
  parentId: string;
  containerId: string;
  walletInfo: Record<"id" | "readTime" | "setTime", string>;
}

const props = withDefaults(defineProps<Props>(), {
  parentId: "",
  containerId: "",
  walletInfo: () => ({}) as Props["walletInfo"],
});

const containerId = computed(() => props.containerId || inject("containerId") || "");

const newItem = /* 新增登录凭证 */ {
  isEdit: true,
};

const state = ref<Record<string, any>>({
  loading: false,
  tableData: [],
});

const rules = ref({});

const formRef = ref<FormInstance>();

const dialogVisible = ref<boolean>(false);
const dialogEditPasswordVisible = ref<boolean>(false);

const viewAccountForm = ref<Record<string, any>>({
  passwordLoading: false,
  showPwd: false,
});

const editPasswordForm = ref<Record<string, any>>({});

function validEndData(rule: any, value: any, callback: any) {
  const current = state.value.tableData.find((v) => v.isEdit);
  if (!current.startTime) return callback();
  else new Date(current.endTime) >= new Date(current.startTime) ? callback() : callback(new Error("结束时间不能小于开始时间"));
}

async function handleSubmitPassword() {
  try {
    const row = state.value.tableData.find((v) => v.id === viewAccountForm.value.id);
    const { message, success } = await setData({ id: row.id, parentId: props.parentId, name: row.name, description: row.description, connectors: row.connectors, userName: editPasswordForm.value.userName, passWord: editPasswordForm.value.passWord, startTime: row.startTime, endTime: row.endTime });
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    handleRefresh();
    dialogEditPasswordVisible.value = false;
    if (viewAccountForm.value.showPwd) viewAccountForm.value.passWord = await getPwd();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

async function handleViewAccountOpen(row) {
  dialogVisible.value = true;
  await nextTick();
  viewAccountForm.value.id = row.id;
  viewAccountForm.value.userName = row.userName;
  viewAccountForm.value.passWord = row.passWord;
}

async function handleGetPwd() {
  try {
    if (viewAccountForm.value.showPwd) return (viewAccountForm.value.showPwd = false);
    const sessionKey = `${userInfo.userId}_${props.walletInfo.id}`;
    if (!sessionStorage.getItem(sessionKey) || Number(atob(sessionStorage.getItem(sessionKey) as string)) + 1000 * 60 * Number(props.walletInfo.readTime) < new Date().getTime()) {
      const { data: mfaData, message: mfaMessage, success: mfaSuccess } = await getMFAMethods({});
      if (!mfaSuccess) throw new Error(mfaMessage);
      const isMfa = mfaData.includes(MFAMethod.TOTP);
      const { data: _data, message: _message, success: _success }: any = await validPwd(isMfa);
      if (!_success) return;
      sessionStorage.setItem(sessionKey, btoa(new Date().getTime().toString()));
    }
    viewAccountForm.value.passwordLoading = true;
    const data = await getPwd();
    viewAccountForm.value.passWord = data;
    viewAccountForm.value.showPwd = true;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    viewAccountForm.value.passwordLoading = false;
  }
}

async function handleEditPwd() {
  const sessionKey = `${userInfo.userId}_${props.walletInfo.id}`;
  if (!sessionStorage.getItem(sessionKey) || Number(atob(sessionStorage.getItem(sessionKey) as string)) + 1000 * 60 * Number(props.walletInfo.setTime) < new Date().getTime()) {
    const { data: mfaData, message: mfaMessage, success: mfaSuccess } = await getMFAMethods({});
    if (!mfaSuccess) throw new Error(mfaMessage);
    const isMfa = mfaData.includes(MFAMethod.TOTP);
    const { data: _data, message: _message, success: _success }: any = await validPwd(isMfa);
    if (!_success) return;
    sessionStorage.setItem(sessionKey, btoa(new Date().getTime().toString()));
  }

  dialogEditPasswordVisible.value = true;
  await nextTick();
  editPasswordForm.value.userName = viewAccountForm.value.userName;
  editPasswordForm.value.passWord = await getPwd();
}

async function getPwd() {
  const { data, message, success } = await getAccountPassword({ parentId: props.parentId, id: viewAccountForm.value.id, containerId: containerId.value as string });
  if (!success) throw new Error(message);
  return data.passWord;
}

function handleAddItem() {
  if (state.value.tableData.filter((v) => v.isEdit).length) ElMessage.warning("请先保存正在编辑的信息");
  else state.value.tableData.push(JSON.parse(JSON.stringify(newItem)));
}

function handleSetItem(row) {
  if (state.value.tableData.filter((v) => v.isEdit).length) ElMessage.warning("请先保存正在编辑的信息");
  else row.isEdit = true;
}

function handleSubmit(row) {
  formRef.value &&
    formRef.value.validate(async (valid) => {
      if (!valid) return;
      try {
        switch (!!row.id) {
          case true:
            const setParams = {
              id: row.id,
              parentId: props.parentId,
              name: row.name,
              description: row.description,
              connectors: row.connectors,
              // userName: row.userName,
              // passWord: row.passWord,
              startTime: row.startTime,
              endTime: row.endTime,
            };
            const { message: setMessage, success: setSuccess } = await setData({ ...setParams });
            if (!setSuccess) throw new Error(setMessage);
            ElMessage.success("操作成功");
            handleRefresh();
            break;
          default:
            const addParams = {
              parentId: props.parentId,
              name: row.name,
              description: row.description,
              connectors: row.connectors,
              userName: row.userName,
              passWord: row.passWord,
              startTime: row.startTime,
              endTime: row.endTime,
            };
            const { message: addMessage, success: addSuccess } = await addData({ ...addParams });
            if (!addSuccess) throw new Error(addMessage);
            ElMessage.success("操作成功");
            handleRefresh();
            break;
        }
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    });
}

async function handleDelItem(row) {
  try {
    const { message, success } = await delData({ parentId: props.parentId, id: row.id });
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    handleRefresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

async function handleRefresh() {
  try {
    state.value.loading = true;
    const { data, message, success } = await getData({ parentId: props.parentId });
    if (!success) throw new Error(message);
    state.value.tableData = data.map((v) => Object.assign(v, { isEdit: false }));
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    state.value.loading = false;
  }
}

onMounted(() => {
  handleRefresh();
});
</script>
