<template>
  <div class="tw-my-4">
    <div class="tw-mb-4 tw-flex tw-items-center tw-justify-between">
      <span class="tw-font-bold">默认登录凭证组</span>
      <el-button type="primary" :icon="Plus" @click="handleAddItem" v-if="userInfo.hasPermission(安全管理中心_密码钱包_编辑)">新增登录凭证组</el-button>
    </div>
    <el-form :model="state" ref="formRef" :rules="{}">
      <el-table :data="state.tableData" border stripe v-loading="state.loading">
        <el-table-column type="expand">
          <template #default="{ row }">
            <div class="tw-px-4">
              <DefaultLoginCredentials :walletInfo="props.walletInfo" :parentId="row.id" :containerId="props.containerId" :tenantId="props.tenantId" />
              <AllotDevice :parentId="row.id" :containerId="props.containerId" :tenantId="props.tenantId" />
            </div>
          </template>
        </el-table-column>
        <el-table-column type="default" prop="groupName" label="组名">
          <template #default="{ row, $index }">
            <el-form-item v-if="row.isEdit" :prop="`tableData[${$index}].groupName`" :rules="[{ required: row.isEdit, message: '组名不能为空', trigger: 'blur' }]">
              <el-input v-model="row.groupName"></el-input>
            </el-form-item>
            <div v-else>{{ row.groupName }}</div>
          </template>
        </el-table-column>
        <el-table-column type="default" prop="" label="操作" width="200" v-if="userInfo.hasPermission(安全管理中心_密码钱包_编辑)">
          <template #default="{ row }">
            <template v-if="row.isEdit">
              <el-button type="primary" link @click="handleSubmit(row)">保存</el-button>
              <el-button type="primary" link @click="handleRefresh"> 取消</el-button>
            </template>

            <template v-else>
              <el-button type="primary" link @click="handleSetItem(row)">编辑</el-button>
              <el-popconfirm :title="`确定移除登录凭证组'${row.groupName}'吗?`" @confirm="handleDelItem(row)">
                <template #reference>
                  <el-button type="danger" link>移除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, provide } from "vue";

import { Plus } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

import { addLoginCredentialsGroup as addData, getLoginCredentialsGroup as getData, setLoginCredentialsGroup as setData, delLoginCredentialsGroup as delData } from "@/views/pages/apis/passwordWallet";

import DefaultLoginCredentials from "./DefaultLoginCredentials.vue";
import AllotDevice from "./AllotDevice.vue";

import { 安全管理中心_密码钱包_编辑 } from "@/views/pages/permission";

import getUserInfo from "@/utils/getUserInfo";

const userInfo = getUserInfo();

interface Props {
  parentId: string;
  containerId: string;
  tenantId: string;
  walletInfo: Record<"id" | "readTime" | "setTime", string>;
}

const props = withDefaults(defineProps<Props>(), {
  parentId: "",
  containerId: "",
  tenantId: "",
  walletInfo: () => ({}) as Props["walletInfo"],
});

// provide("containerId", props.containerId);
// provide("tenantId", props.tenantId);

const newItem = {
  isEdit: true,
};

const formRef = ref();

const state = ref<Record<string, any>>({
  loading: false,
  tableData: [],
});

function handleSubmit(row) {
  formRef.value &&
    formRef.value.validate(async (valid) => {
      if (!valid) return false;
      try {
        switch (!!row.id) {
          case true:
            /* set */
            const { success: setSuccess, message: setMessage } = await setData({ parentId: props.parentId, name: row.groupName, id: row.id });
            if (!setSuccess) throw new Error(setMessage);
            ElMessage.success("操作成功");
            handleRefresh();
            break;
          default:
            /* add */
            const { success: addSuccess, message: addMessage } = await addData({ parentId: props.parentId, name: row.groupName });
            if (!addSuccess) throw new Error(addMessage);
            ElMessage.success("操作成功");
            handleRefresh();
            break;
        }
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    });

  // finally {
  // }
}

async function handleDelItem(row) {
  try {
    const { success, message } = await delData({ parentId: props.parentId, id: row.id });
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    handleRefresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

async function handleAddItem() {
  if (state.value.tableData.filter((v) => v.isEdit).length) return ElMessage.warning("请先保存正在编辑的信息");
  state.value.tableData.push(JSON.parse(JSON.stringify(newItem)));
}

async function handleSetItem(row) {
  if (state.value.tableData.filter((v) => v.isEdit).length) return ElMessage.warning("请先保存正在编辑的信息");
  row.isEdit = true;
}

async function handleRefresh() {
  try {
    state.value.loading = true;
    const { data, message, success } = await getData({ parentId: props.parentId });
    if (!success) throw new Error(message);
    state.value.tableData = data.map((v) => Object.assign(v, { isEdit: false }));
  } catch (error) {
    error instanceof Error && ElMessage.error(error);
  } finally {
    state.value.loading = false;
  }
}

onMounted(() => {
  handleRefresh();
});
</script>
