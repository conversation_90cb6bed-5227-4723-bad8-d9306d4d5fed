<template>
  <el-dialog v-model="dialogVisible" :title="`${isEdit ? '编辑' : '新增'}密码钱包`" width="50vw" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="formRules" :label-width="'80px'">
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" placeholder="名称"></el-input>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="form.description" :rows="4" type="textarea" placeholder="描述" />
      </el-form-item>
      <el-form-item label="安全容器" prop="containerIds" v-if="!isEdit">
        <treeAuth
          v-if="dialogVisible"
          ref="treeAuthRef"
          :treeStyle="{
            width: '300px',
            height: '150px',
          }"
        ></treeAuth>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, nextTick, computed } from "vue";

import treeAuth from "@/components/treeAuth/index.vue";

import { ElMessage, type FormInstance } from "element-plus";

import { addPasswordWallet, setPasswordWallet, type AddReqBody, type SetReqBody } from "@/views/pages/apis/passwordWallet";

import getUserInfo from "@/utils/getUserInfo";

const dialogVisible = ref<boolean>(false);

const userInfo = getUserInfo();

const emits = defineEmits(["refresh"]);

const form = ref<AddReqBody & Partial<SetReqBody>>({
  id: "",
  name: "",
  description: "",
  containerIds: [],
  readTime: "",
  setTime: "",
  enable: false,
});

const isEdit = computed(() => !!form.value.id);

const formRules = ref({
  name: [{ required: true, message: "请输入", trigger: ["blur", "change"] }],
  containerIds: [{ required: true, message: "请选择安全容器", trigger: ["blur", "change"] }],
});

const formRef = ref<FormInstance>();

const treeAuthRef = ref<InstanceType<typeof treeAuth>>();

async function handleClose(done) {
  formRef.value && formRef.value.resetFields();

  await nextTick();

  if (done instanceof Function) done();
  else dialogVisible.value = false;
}

async function handleOpen(row) {
  dialogVisible.value = true;

  await nextTick();

  if (row.id) form.value = JSON.parse(JSON.stringify(row));

  treeAuthRef.value && treeAuthRef.value.chooseTree({ id: (userInfo.currentTenant || {}).containerId });
}

async function handleSubmit() {
  if (!isEdit.value) form.value.containerIds = treeAuthRef.value && treeAuthRef.value.treeId ? [treeAuthRef.value.treeId] : [];

  await nextTick();

  if (!formRef.value) return;

  formRef.value.validate(async (valid: any) => {
    if (!valid) return;

    try {
      const { success, message } = await (isEdit.value ? setPasswordWallet({ id: form.value.id, name: form.value.name, description: form.value.description, tenantId: userInfo.tenantId, readTime: form.value.readTime, setTime: form.value.setTime, enable: form.value.enable } as Partial<SetReqBody>) : addPasswordWallet({ name: form.value.name, containerIds: form.value.containerIds, description: form.value.description }));
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
      await nextTick();

      handleClose(null);

      emits("refresh");
    } catch (error) {
      error instanceof Error && ElMessage.error(error.message);
    }
  });
}

defineExpose({ open: handleOpen });
</script>
