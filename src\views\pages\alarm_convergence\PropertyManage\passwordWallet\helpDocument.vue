<template>
  <el-dialog v-model="dialogVisible" title="密码钱包配置" width="50%" :close-on-click-modal="false">
    <el-scrollbar height="70vh">
      <el-card class="box-card tw-mb-4" v-for="(item, index) in documents" :key="`document-${index}`">
        <template #header>
          <div>
            {{ item.title }}
          </div>
        </template>

        <p class="tw-py-2" v-for="(content, contentIndex) in item.content" :key="`${index}-${contentIndex}`">{{ content }}</p>
      </el-card>
    </el-scrollbar>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";

const dialogVisible = ref<boolean>(false);

const documents = ref<Record<string, any>[]>([
  {
    title: "总体介绍",
    content: [
      /* */
      "密码钱包是在平台中存放设备的登录凭据的地方。",
      "可以在单个客户下创建多个密码钱包，不同的用户组可以访问这些密码钱包，例如，可以为一线人员和管理员赋予密码钱包的相关权限。",
      "密码钱包可以包含多个凭据。存储在密码钱包中的凭据可以由有权访问密码钱包的用户根据需要进行查看。",
      "用户需要在查看凭据之前重新向平台进行身份验证。经过身份验证后，他们将在定义为“可读权限时间(分钟)”的时间段内被授予对密码钱包的读取访问权。",
      "如果用户需要对密码凭据有更新权限，则授予他们在定义为“设置权限时间(分钟)”的持续时间内更新凭据的权限。",
      "密码钱包还与平台的connector功能模块集成使用。",
      "当用户使用connector从平台启动与设备的连接时，如果在密码钱包中定义了设备的凭据，则用户远程登录设备的话，可以选择使用设备的凭据来进行登录认证。",
      "实际设备的密码不会显示给用户——connector代理连接会安全地进行身份验证，不会将凭据直接暴露给正在连接的用户。",
    ],
  },
  {
    title: "默认凭据",
    content: [
      /* */
      "默认的凭证用于该客户下的所有设备。",
      "如果没有为客户下的设备指定明确的凭据组，就使用默认的凭据。",
      "如果没有为客户下的设备指定明确的凭据，connector允许用户从默认的凭证中选择设备的账号密码。",
      "一般情况下，定义一个默认的凭据就可以满足业务场景的需求了。",
    ],
  },
  {
    title: "凭据组",
    content: [
      /* */
      "凭据组，是对凭据进行逻辑分组，设备配置了凭据组，就不会使用默认的凭据来进行登录认证了。",
      "例如，根据业务场景的不同，会为域服务器、独立服务器、路由器和交换机等设备配置特定的凭据组。",
      "可以将单个的设备分配给这个凭据组。",
      "只有凭据组下分配了该设备，其凭据才对该设备起效。",
      "在使用connector或从设备页面查看密码钱包时，凭据将根据设备特定分配给的组进行优先显示。",
      "如果一个设备被分配到一个或多个凭据组，connector将仅显示该设备特定分配到的凭据。",
      "如果没有指定任何的凭证组，就应用默认凭证。",
    ],
  },
  {
    title: "凭据配置",
    content: [
      /* */
      "当使用Telnet连接到设备时，会提醒用户选择密码凭证。然后使用凭据进行身份验证。",
      `如果凭证里没有设备密码，则会跳转带手动登录页面，connector将展示:"Username:"and"Password:”，提醒用户手动输入。`,
      "connector向用户提供凭据时，会根据凭据的“有效起始日期”和“有效截止日期”做展示过滤，只有在当前时间段内生效的凭证才会被选择到。",
      "如果设置了“有效起始日期”和“有效截止日期”，这两个日期中的任何一个，连接器确保只展示有效的凭据。此外，当凭据的有效期到期时，连接器会自动终止使用这些凭据建立的任何正在进行的连接。",
    ],
  },
  {
    title: "审计日志",
    content: [
      /* */
      "每当用户查看或使用密码库中的凭据连接到设备时，都会创建一个日志，用于安全审计。",
    ],
  },
]);

function handleOpen() {
  dialogVisible.value = true;
}

defineExpose({
  handleOpen,
});
</script>

<style lang="scss" scoped>
.box-card :deep(.el-card__header) {
  background-color: #f2f3f5;
}
</style>
