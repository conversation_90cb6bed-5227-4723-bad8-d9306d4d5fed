<template>
  <el-card>
    <pageTemplate v-model:currentPage="state.page" v-model:pageSize="state.size" :total="state.total" :height="height - 40" @size-change="handleRefresh" @current-change="handleRefresh">
      <template #left>
        <div class="tw-text-[14px] tw-font-bold">密码钱包 - {{ userInfo.currentTenant.name }}[{{ userInfo.currentTenant.abbreviation }}]</div>
      </template>
      <template #right>
        <div>
          <el-button type="primary" :icon="Plus" v-if="userInfo.hasPermission(安全管理中心_密码钱包_新增)" @click="hendleCreate">新增密码钱包</el-button>
          <el-button :icon="WarningFilled" @click="() => helpDocumentRef && helpDocumentRef.handleOpen()">帮助文档</el-button>
        </div>
      </template>

      <template #default="{ height: tableHeight }">
        <el-form :model="state" ref="formRef">
          <el-table :data="state.data" border stripe style="width: 100%" :height="tableHeight" :row-key="(row) => row.id" :expand-row-keys="expands" @expand-change="expandColumn" v-loading="state.loading">
            <el-table-column type="expand">
              <template #default="{ row }">
                <div class="tw-px-4">
                  <DefaultLoginCredentials :walletInfo="{ id: row.id, readTime: row.readTime, setTime: row.setTime }" :parentId="row.id" :containerId="row.containerIds.find((v) => v) || ''" />
                  <AllotDevice :walletInfo="{ id: row.id, readTime: row.readTime, setTime: row.setTime }" :parentId="row.id" :containerId="row.containerIds.find((v) => v) || ''" :tenantId="row.tenantId" />
                  <DefaultLoginCredentialsGroup :walletInfo="{ id: row.id, readTime: row.readTime, setTime: row.setTime }" :parentId="row.id" :containerId="row.containerIds.find((v) => v) || ''" :tenantId="row.tenantId" />
                </div>
              </template>
            </el-table-column>

            <TableColumn type="condition" prop="name" label="名称" filter-multiple show-filter v-model:custom-filtered-value="searchPwdName" :filters="$filter0" @filter-change="handleQuery()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].name`" :rules="[{ required: row.isEdit, message: '名称不能为空', trigger: 'blur' }]">
                  <el-input v-model="row.name"></el-input>
                </el-form-item>
                <template v-else>{{ row.name }}</template>
              </template>
            </TableColumn>
            <TableColumn type="condition" prop="description" label="描述" filter-multiple show-filter v-model:custom-filtered-value="searchPwdDesc" :filters="$filter0" @filter-change="handleQuery()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].description`">
                  <el-input v-model="row.description"></el-input>
                </el-form-item>
                <template v-else>{{ row.description }}</template>
              </template>
            </TableColumn>
            <TableColumn type="condition" prop="readTime" label="可读访问时间(分钟)" filter-multiple show-filter v-model:custom-filtered-value="searchDuration" :filters="$filter2" @filter-change="handleQuery()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].readTime`" :rules="[{ required: row.isEdit, message: '可读访问时间不能为空', trigger: 'blur' }]">
                  <el-input v-model="row.readTime"></el-input>
                </el-form-item>
                <template v-else>{{ row.readTime }}</template>
              </template>
            </TableColumn>
            <TableColumn type="condition" prop="setTime" label="设置密码访问时间(分钟)" filter-multiple show-filter v-model:custom-filtered-value="searchProjectLevel" :filters="$filter2" @filter-change="handleQuery()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].setTime`" :rules="[{ required: row.isEdit, message: '设置密码访问时间不能为空', trigger: 'blur' }]">
                  <el-input v-model="row.setTime"></el-input>
                </el-form-item>
                <template v-else>{{ row.setTime }}</template>
              </template>
            </TableColumn>
            <TableColumn type="enum" :prop="`enable`" label="激活" :width="80" :showOverflowTooltip="true" show-filter v-model:filtered-value="search.active" :filters="[false, true].map((v) => ({ value: v, text: v ? '是' : '否' }))" @filter-change="handleQuery()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].enable`" :rules="[{ required: row.isEdit, message: '激活不能为空', trigger: 'blur' }]">
                  <el-select v-model="row.enable">
                    <el-option v-for="enable in [true, false]" :key="`enable-${Number(enable)}`" :label="enable ? '是' : '否'" :value="enable"></el-option>
                  </el-select>
                </el-form-item>
                <template v-else>{{ row.enable ? "是" : "否" }}</template>
              </template>
            </TableColumn>
            <TableColumn type="default" label="操作">
              <template #default="{ row }">
                <template v-if="row.isEdit">
                  <el-button type="primary" link @click="handleSetSubmit(row)">保存</el-button>
                  <el-button type="primary" link @click="handleRefresh">取消</el-button>
                </template>
                <template v-else>
                  <el-button type="primary" link @click="hendleSetItem(row)" v-if="row.permissions.includes(安全管理中心_密码钱包_编辑)">编辑</el-button>
                  <el-popconfirm :title="`确定删除密码钱包'${row.name}'吗?`" @confirm="handleDelItem(row)" v-if="row.permissions.includes(安全管理中心_密码钱包_删除)">
                    <template #reference>
                      <el-button type="danger" link>删除</el-button>
                    </template>
                  </el-popconfirm>
                  <el-button type="primary" link @click="hendleViewLog(row)" v-if="row.permissions.includes(安全管理中心_密码钱包_查看日志)">日志</el-button>
                  <el-button type="primary" link :icon="Security" style="font-size: 22px" v-if="row.permissions.includes(安全管理中心_密码钱包_安全)" @click="showSecurityTree({ containerId: row.containerIds.find((v) => v) })"></el-button>
                </template>
              </template>
            </TableColumn>
          </el-table>
        </el-form>

        <passwordWalletEdit ref="passwordWalletEditRef" @refresh="handleRefresh" />
        <passwordWalletLog ref="passwordWalletLogRef" />
        <helpDocument ref="helpDocumentRef" />
      </template>
    </pageTemplate>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, inject, onMounted, nextTick, computed, toValue } from "vue";

import { ElMessage, type TableColumnCtx, FormInstance } from "element-plus";
import { Plus, WarningFilled } from "@element-plus/icons-vue";
import Security from "@/assets/dp.vue";

import { sizes } from "@/utils/common";

import getUserInfo from "@/utils/getUserInfo";

import pageTemplate from "@/components/pageTemplate.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";

import passwordWalletEdit from "./edit.vue";
import passwordWalletLog from "./log.vue";
import helpDocument from "./helpDocument.vue";

import DefaultLoginCredentials from "./DefaultLoginCredentials.vue";
import AllotDevice from "./AllotDevice.vue";
import DefaultLoginCredentialsGroup from "./DefaultLoginCredentialsGroup.vue";

import { getPasswordWallet as getData, delPasswordWallet as delData, setPasswordWallet as setData } from "@/views/pages/apis/passwordWallet";
import type { PasswordWalletItem } from "@/views/pages/apis/passwordWallet";

import showSecurityTree from "@/components/security-container";

import { 安全管理中心_密码钱包_安全, 安全管理中心_密码钱包_新增, 安全管理中心_密码钱包_编辑, 安全管理中心_密码钱包_删除, 安全管理中心_密码钱包_查看日志 } from "@/views/pages/permission";

const width = inject("width", ref(0));
const height = inject("height", ref(0));

const userInfo: any = getUserInfo();

type DataItem = PasswordWalletItem;

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: any };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: TableColumnCtx<DataItem>, value: T[keyof T]) => string | import("vue").VNode; showOverflowTooltip?: boolean }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}

const state = reactive<StateData<DataItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  column: [],
  data: [],
  page: 1,
  size: 50,
  sizes,
  total: 0,
});

const passwordWalletEditRef = ref<InstanceType<typeof passwordWalletEdit>>();
const passwordWalletLogRef = ref<InstanceType<typeof passwordWalletLog>>();
const helpDocumentRef = ref<InstanceType<typeof helpDocument>>();

const formRef = ref<FormInstance>();

function hendleCreate(row) {
  if (!passwordWalletEditRef.value) return false;
  passwordWalletEditRef.value.open(row);
}

function hendleSetItem(row) {
  if (state.data.filter((v: any) => v.isEdit).length) return ElMessage.warning("请先保存正在编辑的信息");

  row.isEdit = true;
}

function handleSetSubmit(row) {
  formRef.value &&
    formRef.value.validate(async (valid) => {
      if (!valid) return;
      try {
        const params = { id: row.id, name: row.name, description: row.description, tenantId: row.tenantId, readTime: row.readTime, setTime: row.setTime, enable: row.enable };
        const { message, success } = await setData(params);
        if (!success) throw new Error(message);
        ElMessage.success("操作成功");
        await nextTick();
        handleRefresh();
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    });
}

function hendleViewLog(row) {
  if (!passwordWalletLogRef.value) return false;
  passwordWalletLogRef.value.open(row);
}

async function handleDelItem(row) {
  try {
    const { message, success } = await delData(row);
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    handleRefresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

const expands = ref<string[]>([]);
function expandColumn(row, expandedRows) {
  // 每次只展开一行
  if (expandedRows.length) {
    expands.value = [];
    if (row) {
      expands.value.push(row.id);
    }
  } else {
    expands.value = [];
  }
}
const search = ref<Record<string, any>>({
  // 密码钱包名称
  eqName: [],
  inName: [],
  nameFilterRelation: "AND",
  neName: [],
  excludeName: [],
  // 描述
  inDesc: [],
  excludeDesc: [],
  descFilterRelation: "AND",
  eqDesc: [],
  neDesc: [],
  // 设置权限时间
  eqSetTime: [],
  neSetTime: [],
  setTimeFilterRelation: "AND",
  // 可读权限时间
  eqReadTime: [],
  neReadTime: [],
  readTimeFilterRelation: "AND",
});
import { exoprtMatch1, exoprtMatch2, exoprtMatch3 } from "@/components/tableColumn/common";
const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);
const $filter2 = ref(exoprtMatch3);
const searchForm = ref<Record<string, any>>({});
// 设置权限时间
const searchProjectLevel0 = ref<"eq" | "ne">("eq");
const searchProjectLevel1 = ref<"eq" | "ne">("eq");
const searchProjectLevel = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchProjectLevel0) === "eq") value0 = search.value.eqSetTime[0] || "";
    if (toValue(searchProjectLevel0) === "ne") value0 = search.value.neSetTime[0] || "";

    let value1 = "";
    if (toValue(searchProjectLevel1) === "eq") value1 = search.value.eqSetTime[1] || "";
    if (toValue(searchProjectLevel1) === "ne") value1 = search.value.neSetTime[1] || "";

    return {
      type0: toValue(searchProjectLevel0),
      type1: toValue(searchProjectLevel1),
      relation: search.value.setTimeFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchProjectLevel0.value = v.type0 as "eq" | "ne";
    searchProjectLevel1.value = v.type1 as "eq" | "ne";
    search.value.setTimeFilterRelation = v.relation;
    search.value.eqSetTime = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neSetTime = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// 可读权限时间
const searchDuration0 = ref<"eq" | "ne">("eq");
const searchDuration1 = ref<"eq" | "ne">("eq");
const searchDuration = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchDuration0) === "eq") value0 = search.value.eqReadTime[0] || "";
    if (toValue(searchDuration0) === "ne") value0 = search.value.neReadTime[0] || "";

    let value1 = "";
    if (toValue(searchDuration1) === "eq") value1 = search.value.eqReadTime[1] || "";
    if (toValue(searchDuration1) === "ne") value1 = search.value.neReadTime[1] || "";

    return {
      type0: toValue(searchDuration0),
      type1: toValue(searchDuration1),
      relation: search.value.readTimeFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchDuration0.value = v.type0 as "eq" | "ne";
    searchDuration1.value = v.type1 as "eq" | "ne";
    search.value.readTimeFilterRelation = v.relation;
    search.value.eqReadTime = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neReadTime = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
// 密码钱包名称
const searchPwd0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchPwd1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchPwdName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchPwd0ByName) === "include") value0 = search.value.inName[0] || "";
    if (toValue(searchPwd0ByName) === "exclude") value0 = search.value.excludeName[0] || "";
    if (toValue(searchPwd0ByName) === "eq") value0 = search.value.eqName[0] || "";
    if (toValue(searchPwd0ByName) === "ne") value0 = search.value.neName[0] || "";
    let value1 = "";
    if (toValue(searchPwd1ByName) === "include") value1 = search.value.inName[search.value.inName.length - 1] || "";
    if (toValue(searchPwd1ByName) === "exclude") value1 = search.value.excludeName[search.value.excludeName.length - 1] || "";
    if (toValue(searchPwd1ByName) === "eq") value1 = search.value.eqName[search.value.eqName.length - 1] || "";
    if (toValue(searchPwd1ByName) === "ne") value1 = search.value.neName[search.value.neName.length - 1] || "";
    return {
      type0: toValue(searchPwd0ByName),
      type1: toValue(searchPwd1ByName),
      relation: search.value.nameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchPwd0ByName.value = v.type0 as typeof searchPwd0ByName extends import("vue").Ref<infer T> ? T : string;
    searchPwd1ByName.value = v.type1 as typeof searchPwd1ByName extends import("vue").Ref<infer T> ? T : string;
    search.value.nameFilterRelation = v.relation;
    search.value.inName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
// 描述
const searchPwd0ByDesc = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchPwd1ByDesc = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchPwdDesc = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchPwd0ByDesc) === "include") value0 = search.value.inDesc[0] || "";
    if (toValue(searchPwd0ByDesc) === "exclude") value0 = search.value.excludeDesc[0] || "";
    if (toValue(searchPwd0ByDesc) === "eq") value0 = search.value.eqDesc[0] || "";
    if (toValue(searchPwd0ByDesc) === "ne") value0 = search.value.neDesc[0] || "";
    let value1 = "";
    if (toValue(searchPwd1ByDesc) === "include") value1 = search.value.inDesc[search.value.inDesc.length - 1] || "";
    if (toValue(searchPwd1ByDesc) === "exclude") value1 = search.value.excludeDesc[search.value.excludeDesc.length - 1] || "";
    if (toValue(searchPwd1ByDesc) === "eq") value1 = search.value.eqDesc[search.value.eqDesc.length - 1] || "";
    if (toValue(searchPwd1ByDesc) === "ne") value1 = search.value.neDesc[search.value.neDesc.length - 1] || "";
    return {
      type0: toValue(searchPwd0ByDesc),
      type1: toValue(searchPwd1ByDesc),
      relation: search.value.descFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchPwd0ByDesc.value = v.type0 as typeof searchPwd0ByDesc extends import("vue").Ref<infer T> ? T : string;
    searchPwd1ByDesc.value = v.type1 as typeof searchPwd1ByDesc extends import("vue").Ref<infer T> ? T : string;
    search.value.descFilterRelation = v.relation;
    search.value.inDesc = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeDesc = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqDesc = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neDesc = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

async function handleQuery() {
  state.page = 1;
  await nextTick();
  await handleRefresh();
}

async function handleRefresh() {
  state.loading = true;
  try {
    const { data, message, success, total } = await getData({ pageNumber: state.page, pageSize: state.size, ...search.value });
    if (!success) throw new Error(message);
    state.data = data.map((v) => Object.assign(v, { isEdit: false }));
    state.total = Number(total);
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    state.loading = false;
  }
}

onMounted(() => {
  handleRefresh();
});
</script>
