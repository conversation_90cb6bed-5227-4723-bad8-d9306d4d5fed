<template>
  <el-dialog v-model="dialogVisible" title="日志" width="50%" :before-close="handleClose" :close-on-click-modal="false">
    <el-table :data="state.tableData" border stripe height="500" v-loading="state.loading">
      <el-table-column prop="userName" label="用户"></el-table-column>
      <el-table-column prop="operate" label="操作"></el-table-column>
      <el-table-column prop="loginCredentialsName" label="登录凭证"></el-table-column>
      <el-table-column prop="equipment" label="设备"></el-table-column>
      <el-table-column prop="ipTime" label="IP时间"></el-table-column>
      <el-table-column prop="ipAddress" label="IP"></el-table-column>
    </el-table>
    <div class="tw-flex tw-w-full tw-justify-end tw-pt-4">
      <el-pagination v-model:current-page="state.page" v-model:page-size="state.size" :page-sizes="sizes" size="small" layout="total, sizes, prev, pager, next, jumper" :total="state.total" @size-change="handleRefresh" @current-change="handleRefresh" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, nextTick } from "vue";

import { getPasswordWalletsLog } from "@/views/pages/apis/passwordWallet";
import { ElMessage } from "element-plus";

import { sizes } from "@/utils/common";
import getUserInfo from "@/utils/getUserInfo";
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);
import moment from "moment";
const dialogVisible = ref<boolean>(false);

const state = ref<Record<string, any>>({
  page: 1,
  size: 10,
  total: 0,
  loading: false,
  tableData: [],
});
function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == getUserInfo().zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
function handleClose(done) {
  if (done instanceof Function) done();
  else dialogVisible.value = false;
}

const detail = ref<Record<string, any>>({});

async function handleOpen(row) {
  state.value.page = 1;
  state.value.size = 10;
  dialogVisible.value = true;
  detail.value = row;
  await nextTick();
  handleRefresh();
}

async function handleRefresh() {
  try {
    state.value.loading = true;
    const { data, message, success, total } = await getPasswordWalletsLog({ parentId: detail.value.id, pageNumber: state.value.page, pageSize: state.value.size });
    if (!success) throw new Error(message);
    state.value.tableData = data.map((item) => {
      return {
        ...item,
        ipTime: moment(moment(item.ipTime, "YYYY-MM-DD HH:mm:ss.SSS").valueOf() + timeZoneSwitching()).format("YYYY-MM-DD HH:mm:ss.SSS"),
      };
    });
    state.value.total = Number(total);
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    state.value.loading = false;
  }
}

defineExpose({
  open: handleOpen,
});
</script>
