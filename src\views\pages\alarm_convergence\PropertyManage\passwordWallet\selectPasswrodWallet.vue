<template>
  <transition name="el-fade-in-linear">
    <div v-if="isShow" class="tw-fixed tw-left-0 tw-top-0 tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center tw-bg-[var(--el-overlay-color-lighter)]">
      <div class="tw-relative tw-h-[68%] tw-w-[50%] tw-overflow-hidden tw-rounded-md tw-bg-[#fff]">
        <el-row class="tw-h-full">
          <el-col :span="8" class="tw-h-full tw-bg-[var(--el-color-primary)]">
            <!-- left -->
            <div class="tw-p-4">
              <p class="tw-text-xl tw-font-semibold tw-text-white">登录凭证</p>
              <p class="tw-pt-10 tw-text-xl tw-leading-6 tw-text-white">
                <el-icon><WarningFilled /></el-icon>
              </p>
              <p class="tw-pt-2 tw-text-lg tw-leading-6 tw-text-white">您可以选择登录凭据登录设备; 也可以选择手动登录</p>
            </div>
          </el-col>
          <el-col :span="16" class="tw-h-full">
            <!-- right -->
            <div class="tw-flex tw-justify-end tw-pr-2 tw-pt-2">
              <!-- 关闭按钮 -->
              <el-button type="info" link :icon="Close" @click="handleClose" class="tw-text-xl"></el-button>
            </div>
            <div class="tw-mt-2" :style="{ height: 'calc(100% - 90px)' }">
              <el-table ref="credentialTabel" :data="tableData" border stripe style="width: 100%" :show-header="false" highlight-current-row @current-change="handleCurrentChange">
                <el-table-column prop="name" />
              </el-table>
            </div>
            <div class="tw-flex tw-justify-end tw-pr-3 tw-pt-2">
              <el-button @click="handleManualLogin">手动登录</el-button>
              <el-button type="primary" @click="handleSubmit">登录</el-button>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { nextTick, ref, inject, h, defineComponent } from "vue";
import { useRouter, useRoute } from "vue-router";

import { WarningFilled, Close } from "@element-plus/icons-vue";

import { getLoginCredentialQueryAllLognins } from "@/views/pages/apis/passwordWallet";
import { ElMessage, ElTable, ElMessageBox, ElForm, ElFormItem, ElSelect, ElOption, ElInput } from "element-plus";

import getUserInfo from "@/utils/getUserInfo";

import JSEncrypt from "jsencrypt";

const router = useRouter();
const route = useRoute();

const userInfo = getUserInfo();

const width = inject("width", ref(0));
const height = inject("height", ref(0));

const isShow = ref<boolean>(false);

const tableData = ref<Record<string, any>[]>([]);

const credentialTabel = ref<InstanceType<typeof ElTable>>();

const currentCredential = ref<Record<string, any>>({});

const linkInfo = ref<Record<string, any>>({});

/**
 * @desc 登录
 */
async function handleSubmit() {
  if (currentCredential.value.id) {
    const _data = {
      id: new Date().getTime().toString(36) + Math.random().toString(36).substr(2, 9), // 随机一个uuid
      protocol: linkInfo.value.connectors, // telnet/ssh/rdp
      width: parseInt(Number(width.value) as any), // 前端获取浏览器的宽高
      height: parseInt(Number(height.value) as any), // 前端获取浏览器的宽高
      username: currentCredential.value.userName, // 密码钱包存的用户名
      password: currentCredential.value.passWord, // 密码钱包存的密码 要rsa加密一下
    };

    const { href } = router.resolve({
      name: "vnc",
      query: {
        data: btoa(JSON.stringify(_data)),
        tenantId: userInfo.currentTenantId,
        resourceId: linkInfo.value.deviceId,
      },
    });

    window.open(href, "_blank");
    await nextTick();
    handleClose();
  } else ElMessage.warning("请选择登录凭证");
}

type ManualLogin = { username: string; password: string };
const manualLogin = ref<ManualLogin>({} as ManualLogin);

/**
 * @desc 手动登录
 * @desc rdp需要输入账号密码
 */
async function handleManualLogin() {
  manualLogin.value.username = "";
  manualLogin.value.password = "";

  await nextTick();

  const _data = {
    id: new Date().getTime().toString(36) + Math.random().toString(36).substr(2, 9), // 随机一个uuid
    protocol: linkInfo.value.connectors, // telnet/ssh/rdp
    width: parseInt(Number(width.value) as any), // 前端获取浏览器的宽高
    height: parseInt(Number(height.value) as any), // 前端获取浏览器的宽高
    username: "", // 密码钱包存的用户名
    password: "", // 密码钱包存的密码 要rsa加密一下
  };

  if (linkInfo.value.connectors === "rdp") {
    const result: any = await (function () {
      return new Promise((resolve) => {
        ElMessageBox({
          title: "手动登录",
          message: h(
            defineComponent({
              setup() {
                return () => h(ElForm, { ref: "createFormRef", style: { width: "396px" }, model: {}, labelPosition: "left" }, [h(ElFormItem, { label: "账号", prop: "username", rules: [{ required: true, validator: (rule: any, value: any, callback: any) => (!manualLogin.value.username ? callback(new Error("账号不能为空")) : callback()), trigger: "blur" }] }, h(ElInput, { "class": "tw-w-full", "modelValue": manualLogin.value.username, "onUpdate:modelValue": ($event) => (manualLogin.value.username = $event) })), h(ElFormItem, { label: "密码", prop: "password", rules: [{ required: true, validator: (rule: any, value: any, callback: any) => (!manualLogin.value.password ? callback(new Error("密码不能为空")) : callback()), trigger: "blur" }] }, h(ElInput, { "class": "tw-w-full", "modelValue": manualLogin.value.password, "onUpdate:modelValue": ($event) => (manualLogin.value.password = $event), "type": "password", "showPassword": true }))]);
              },
            })
          ),
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                if (!manualLogin.value.username || !manualLogin.value.password) return ElMessage.error("请输入账号密码");
                done();
                resolve({ username: manualLogin.value.username, password: rsaPassword(manualLogin.value.password) });
              } catch (error) {
                error instanceof Error && ElMessage.error(error.message);
              }
            } else {
              done();
              resolve({});
            }
          },
        }).catch(() => resolve({}));
      });
    })();

    if ("username" in result && "password" in result) {
      _data.username = result.username;
      _data.password = result.password;
    } else return;
  }

  const { href } = router.resolve({
    name: "vnc",
    query: {
      data: btoa(JSON.stringify(_data)),
      tenantId: userInfo.currentTenantId,
      resourceId: linkInfo.value.deviceId || route.params.id,
    },
  });

  window.open(href, "_blank");
  await nextTick();
  handleClose();
}

/**
 * @desc rsa加密
 * @param data 加密值
 */
function rsaPassword(data) {
  var jsencrypt = new JSEncrypt();
  jsencrypt.setPublicKey("MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJgMceW2u67V6oDOqz8NVj46KDYNCz7bgffSHilW7878Tsg-k7trVMsa1bzMReLP6kPduVaxILIy-o3guhK0PpMCAwEAAQ");
  return jsencrypt.encrypt(data);
}
function handleCurrentChange(currentRow) {
  currentCredential.value = currentRow;
}

async function handleRefresh({ deviceId, connectors }) {
  try {
    linkInfo.value = { deviceId, connectors };

    const { data, message, success } = await getLoginCredentialQueryAllLognins({ deviceId, connectors });
    if (!success) throw new Error(message);
    tableData.value = data;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

async function handleClose() {
  isShow.value = false;

  await nextTick();
  currentCredential.value = {};
}

async function handleOpen(deviceId, connectors) {
  handleRefresh({ deviceId: deviceId || route.params.id, connectors });

  await nextTick();

  isShow.value = true;
}

defineExpose({
  handleClose,
  handleOpen,
});
</script>
