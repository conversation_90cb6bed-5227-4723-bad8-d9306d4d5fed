import { h, reactive } from "vue";

import { ElMessage, ElMessageBox, ElForm, ElFormItem, ElInput } from "element-plus";

import { verifyPwd, verifyCode } from "@/views/pages/apis/passwordWallet";

import getUserInfo from "@/utils/getUserInfo";

export default function (isMfa: boolean) {
  return new Promise((resolve, reject) => {
    const userInfo = getUserInfo();
    const form = reactive<{ loading: boolean; data: Record<"secretCode", string>; message: string; ref: InstanceType<typeof ElForm> | null }>({
      loading: false,
      message: "",
      data: {
        secretCode: "",
      },
      ref: null,
    });
    const message = h({
      setup(_$props, _$ctx) {
        return () => {
          if (form.message) return h("div", form.message);
          else {
            return h(
              ElForm,
              { model: form.data, labelPosition: "left", ref: ($form) => (form.ref = $form as InstanceType<typeof ElForm>) },
              {
                default() {
                  return [
                    h(
                      ElFormItem,
                      {
                        label: "账号",
                      },
                      () => `${userInfo.account}@${userInfo.tenantAbbreviation}`
                    ),
                    h(
                      ElFormItem,
                      {
                        prop: "secretCode",
                        rules: [
                          { required: true, message: "请输入密码", trigger: "blur" },
                          // { pattern: /^((?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*?])|(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])|(?=.*[a-z])(?=.*[0-9])(?=.*[~!@#$%^&*?])|(?=.*[A-Z])(?=.*[0-9])(?=.*[~!@#$%^&*?])).{8,25}$/, message: "密码为8-25位包含英文大小写、数字、特殊字符(~!@#$%^&*?)中的三种", trigger: "blur" },
                        ],
                        label: "密码",
                      },
                      () => h(ElInput, { "type": "password", "autocomplete": "new-password", "showPassword": true, "modelValue": form.data.secretCode, "onUpdate:modelValue": ($event) => (form.data.secretCode = $event), "placeholder": "请输入密码" })
                    ),
                  ];
                },
              }
            );
          }
        };
      },
    });

    ElMessageBox({
      title: "身份认证",
      type: "",
      message,
      showCancelButton: true,
      showConfirmButton: true,
      draggable: true,
      showClose: false,
      closeOnClickModal: false,
      closeOnHashChange: false,
      async beforeClose(action, instance, done) {
        if (action === "confirm") {
          instance.confirmButtonLoading = true;
          instance.confirmButtonText = "Loading...";
          const valid = await new Promise((resolve) => form.ref?.validate(resolve));
          if (!valid) {
            instance.confirmButtonLoading = false;
            instance.confirmButtonText = "确认";
            return;
          }
          try {
            const { success, message, data } = await verifyPwd(form.data);
            if (success) {
              instance.confirmButtonLoading = false;
              instance.confirmButtonText = "确认";
              if (isMfa) {
                const { success: mfaSuccess } = (await validMfa()) as any;
                if (!mfaSuccess) reject({ success: false });
              }
              resolve({ success, message, data });
              ElMessage.success("校验成功");
              done();
            } else throw Object.assign(new Error(message), { success, data });
          } catch (error) {
            if (error instanceof Error) {
              reject({ ...error });
              instance.showConfirmButton = false;
              instance.showInput = false;
              form.message = error.message;
              instance.type = "error";
            }
          } finally {
            instance.confirmButtonLoading = false;
          }
        } else {
          reject({ success: false });
          done();
        }
      },
    })
      .then(() => {})
      .catch(() => {});
  });
}

function validMfa() {
  return new Promise((resolve, reject) => {
    const userInfo = getUserInfo();

    const form = reactive<{ loading: boolean; data: Record<"code" | "userId", string>; message: string; ref: InstanceType<typeof ElForm> | null }>({
      loading: false,
      message: "",
      data: {
        userId: userInfo.userId,
        code: "",
      },
      ref: null,
    });
    const message = h({
      setup(_$props, _$ctx) {
        return () => {
          if (form.message) return h("div", form.message);
          else {
            return h(
              ElForm,
              { model: form.data, labelPosition: "left", ref: ($form) => (form.ref = $form as InstanceType<typeof ElForm>) },
              {
                default() {
                  return [
                    h(
                      ElFormItem,
                      {
                        prop: "code",
                        rules: [{ required: true, message: "请输入动态码验证", trigger: "blur" }],
                        label: "动态码验证",
                      },
                      () => h(ElInput, { "type": "password", "autocomplete": "new-password", "showPassword": true, "modelValue": form.data.code, "onUpdate:modelValue": ($event) => (form.data.code = $event), "placeholder": "请输入动态码验证" })
                    ),
                  ];
                },
              }
            );
          }
        };
      },
    });

    ElMessageBox({
      title: "双因素认证",
      type: "",
      message,
      showCancelButton: true,
      showConfirmButton: true,
      draggable: true,
      showClose: false,
      closeOnClickModal: false,
      closeOnHashChange: false,
      async beforeClose(action, instance, done) {
        if (action === "confirm") {
          instance.confirmButtonLoading = true;
          instance.confirmButtonText = "Loading...";
          const valid = await new Promise((resolve) => form.ref?.validate(resolve));
          if (!valid) {
            instance.confirmButtonLoading = false;
            instance.confirmButtonText = "确认";
            return;
          }
          try {
            const { success, message, data } = await verifyCode(form.data);
            if (success) {
              instance.confirmButtonLoading = false;
              instance.confirmButtonText = "确认";
              done();
              resolve({ success, message, data });
            } else throw Object.assign(new Error(message), { success, data });
          } catch (error) {
            if (error instanceof Error) {
              reject({ ...error });
              instance.showConfirmButton = false;
              instance.showInput = false;
              form.message = error.message;
              instance.type = "error";
            }
          } finally {
            instance.confirmButtonLoading = false;
          }
        } else {
          reject({ success: false });
          done();
        }
      },
    })
      .then(() => {})
      .catch(() => {});
  });
}
