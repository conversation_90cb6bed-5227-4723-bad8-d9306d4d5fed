<template>
  <el-dialog v-model="dialogVisible" :title="`${isEdit ? '编辑' : '新增'}项目`" width="45%" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="formRules" :label-width="'100px'">
      <el-form-item label="项目名称" prop="projectName">
        <el-input v-model="form.projectName" placeholder="请输入项目名称"></el-input>
      </el-form-item>
      <el-form-item label="统一服务编号" prop="uniformServiceCode">
        <el-input v-model="form.uniformServiceCode" placeholder="请输入统一服务编号" />
      </el-form-item>
      <el-form-item label="项目编码" prop="projectCode">
        <el-input v-model="form.projectCode" placeholder="请输入项目编码" />
      </el-form-item>
      <el-form-item prop="active">
        <el-checkbox v-model="form.active" label="是否激活"></el-checkbox>
      </el-form-item>
      <el-form-item label="安全容器" prop="containerId" v-if="!isEdit">
        <treeAuth
          v-if="dialogVisible"
          ref="treeAuthRef"
          :treeStyle="{
            width: '300px',
            height: '150px',
          }"
        ></treeAuth>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, nextTick, computed } from "vue";

import treeAuth from "@/components/treeAuth/index.vue";

import { ElMessage, type FormInstance } from "element-plus";

import { addAllProjectPlans, setAllProjectPlans, type AddReqBody, type SetReqBody } from "@/views/pages/apis/projectPlan";

import getUserInfo from "@/utils/getUserInfo";

const dialogVisible = ref<boolean>(false);

const userInfo = getUserInfo();
console.log(userInfo, 50);

const emits = defineEmits(["refresh"]);
defineOptions({ name: "AddProject" });

const form = ref<AddReqBody & Partial<SetReqBody>>({
  id: "",
  projectName: "",
  projectCode: "",
  uniformServiceCode: "",
  projectLevel: "",
  range: {},
  containerId: "",
  slaId: "",
  slaName: "",
  active: true,
});

const isEdit = computed(() => !!form.value.id);

const formRules = ref({
  projectName: [{ required: true, message: "请输入项目名称", trigger: ["blur", "change"] }],
  containerId: [{ required: true, message: "请选择安全容器", trigger: ["blur", "change"] }],
});

const formRef = ref<FormInstance>();

const treeAuthRef = ref<InstanceType<typeof treeAuth>>();

async function handleClose(done) {
  formRef.value && formRef.value.resetFields();

  await nextTick();

  if (done instanceof Function) done();
  else dialogVisible.value = false;
}

async function handleOpen(row) {
  dialogVisible.value = true;

  await nextTick();

  if (row.id) form.value = JSON.parse(JSON.stringify(row));

  treeAuthRef.value && treeAuthRef.value.chooseTree({ id: (userInfo.currentTenant || {}).containerId });
}

async function handleSubmit() {
  if (!isEdit.value) form.value.containerId = treeAuthRef.value && treeAuthRef.value.treeId ? treeAuthRef.value.treeId : "";

  await nextTick();

  if (!formRef.value) return;

  formRef.value.validate(async (valid: any) => {
    if (!valid) return;
    const addArguments = {
      projectName: form.value.projectName,
      projectCode: form.value.projectCode,
      uniformServiceCode: form.value.uniformServiceCode,
      active: form.value.active,
      containerId: form.value.containerId,
    };
    try {
      const { success, message } = await (isEdit.value ? setAllProjectPlans(addArguments) : addAllProjectPlans(addArguments as any));
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
      await nextTick();

      handleClose(null);

      emits("refresh");
    } catch (error) {
      error instanceof Error && ElMessage.error(error.message);
    }
  });
}

defineExpose({ open: handleOpen });
</script>
