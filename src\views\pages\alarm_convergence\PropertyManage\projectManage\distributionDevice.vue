<template>
  <div class="tw-my-4">
    <div class="tw-mb-4 tw-flex tw-items-center tw-justify-between">
      <span class="tw-font-bold">{{ t("project.Allocate Device") }}</span>
      <el-button type="primary" :icon="Plus" @click="handleAddItem" :loading="butLoading" v-if="userInfo.hasPermission('701289365304770560')">{{ t("project.Allocate Device") }}</el-button>
    </div>
    <el-table :data="state.tableData" border stripe v-loading="state.loading">
      <el-table-column type="default" prop="name" :label="t('project.Name')">
        <template #default="{ row }">
          <div>
            <div style="color: #409eff; cursor: pointer" @click="opedeviceDetail(row)">
              {{ row.name || "--" }}
            </div>
            <div style="font-size: 12px">
              {{ row.config?.ipAddress || "--" }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column type="default" prop="description" :label="t('project.Description')"></el-table-column>
      <el-table-column type="default" prop="active" :label="t('project.Activate')">
        <template #default="scope">
          <el-text type="primary" v-if="scope.row.active">{{ t("glob.Yes") }}</el-text>
          <el-text type="danger" v-else>{{ t("glob.Not") }}</el-text>
        </template>
      </el-table-column>
      <el-table-column type="default" :label="t('glob.operate')" width="200">
        <template #default="{ row }">
          <el-popconfirm :title="t('project.Deallocate current data')" @confirm="handleDelItem(row)">
            <template #reference>
              <el-button type="danger" link v-if="userInfo.hasPermission('689703964815392768')">{{ t("project.Deallocate Device") }}</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, h, defineComponent, nextTick, computed, inject } from "vue";

import { Plus } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, ElForm, ElFormItem, ElSelect, ElOption, ElCheckbox } from "element-plus";

import { ProjectaddProdDevice, ProjectdelProdDevice } from "@/views/pages/apis/projectPlan";

import getUserInfo from "@/utils/getUserInfo";
import { getDeviceById, getDeviceList } from "@/views/pages/apis/alarmBoard";
import { queryResourceList } from "@/views/pages/apis/index";
import { useRoute, useRouter } from "vue-router";

import { useI18n } from "vue-i18n";

const { t } = useI18n();

interface Emits {
  (event: "refresh", data: unknown): void;
}
const emits = defineEmits<Emits>();
const route = useRoute();
const router = useRouter();

import {} from "@/views/pages/permission";

defineOptions({ name: "DistributionDevice" });

const userInfo: any = getUserInfo();

interface Props {
  parentId: string;
  containerId: string;
  tenantId: string;
  deviceIds: string[]; // 显式声明类型
}

const props = withDefaults(defineProps<Props>(), {
  parentId: "",
  containerId: "",
  tenantId: "",
  deviceIds: undefined,
});

const state = ref<Record<string, any>>({
  loading: false,
  tableData: [],
});

const butLoading = ref<boolean>(false);

function opedeviceDetail(item) {
  const { href } = router.resolve({
    name: "509596457372745728",
    params: { id: item.id },
    query: {
      fallback: route.name,
      tenant: item.tenantId,
    },
  });
  window.open(href, item.id);
}

async function handleDelItem(row) {
  try {
    const { success, message, data } = await ProjectdelProdDevice({ id: props.parentId, deviceId: row.id });
    if (!success) throw new Error(message);
    ElMessage.success(t("axios.Operation successful"));
    handleRefresh(data.deviceIds && data.deviceIds.length && data.deviceIds.join(","));
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

const deviceId = ref<string[]>([]);

const showAllDevices = ref(false); // 用于控制是否展示所有设备

async function handleAddItem() {
  try {
    butLoading.value = true;
    // 获取激活设备和所有设备数据
    const [activeResult, allResult] = await Promise.all([
      queryResourceList({ active: true, queryType: "projectManage" }), // 获取激活设备
      queryResourceList({}), // 获取所有设备
    ]);

    if (!activeResult.success || !allResult.success) {
      throw new Error(activeResult.message || allResult.message);
    }
    const activeDevices = !userInfo.hasPermission("701290146212872192") ? [] : activeResult.data || []; // 激活设备
    const allDevices = !userInfo.hasPermission("701290146212872192") ? [] : allResult.data || []; // 所有设备
    deviceId.value = [];
    showAllDevices.value = false;
    await nextTick();
    butLoading.value = false;
    ElMessageBox({
      title: t("project.Allocate Device"),
      message: h(
        defineComponent({
          setup() {
            return () =>
              h(ElForm, { ref: "createFormRef", style: { width: "396px" }, model: {}, labelPosition: "left" }, [
                h(
                  ElFormItem,
                  {
                    label: t("project.Name"),
                    prop: "versionId",
                    rules: [
                      {
                        required: true,
                        validator: (rule, value, callback) => (!deviceId.value ? callback(new Error(t("project.Device cannot be empty"))) : callback()),
                        trigger: "blur",
                      },
                    ],
                  },
                  [
                    h(
                      ElSelect,
                      {
                        "class": "tw-w-full",
                        "modelValue": deviceId.value,
                        "onUpdate:modelValue": ($event) => (deviceId.value = $event),
                        "filterable": true,
                        "multiple": true,
                      },
                      () => (showAllDevices.value ? allDevices : activeDevices).map((v) => h(ElOption, { key: `option-${v.id}`, label: v.name, value: v.id }))
                    ),
                  ]
                ),
                // 添加一个复选框，用于切换是否展示所有设备
                h(
                  ElFormItem,
                  {},
                  h(
                    ElCheckbox,
                    {
                      "modelValue": showAllDevices.value,
                      "onUpdate:modelValue": (value) => (showAllDevices.value = value as boolean),
                    },
                    t("project.Show All Devices")
                  )
                ),
              ]);
          },
        })
      ),
      beforeClose: async (action, instance, done) => {
        if (action === "confirm") {
          try {
            if (!(deviceId.value instanceof Array) || !deviceId.value.length) throw new Error(t("project.Please select device"));
            if (deviceId.value instanceof Array && deviceId.value.length > 30) throw new Error(t("project.Number of devices exceeds the limit. The maximum allowed is 30 devices"));
            const { message, success, data } = await ProjectaddProdDevice({
              id: props.parentId,
              deviceIds: deviceId.value.join(","),
              deviceNames: (showAllDevices.value ? allDevices : activeDevices)
                .filter((v) => deviceId.value.includes(v.id))
                .map((v) => v.name)
                .join(","),
            });
            if (!success) throw new Error(message);
            await emits("refresh");
            ElMessage.success(t("axios.Operation successful"));
            await handleRefresh(data.deviceIds && data.deviceIds.join(","));
            done();
          } catch (error) {
            error instanceof Error && ElMessage.error(error.message);
          }
        } else done();
      },
    }).catch(() => Promise.resolve("cancel"));
  } catch (error) {
    butLoading.value = false;
    error instanceof Error && ElMessage.error(error.message);
  }
}

async function handleRefresh(items) {
  try {
    state.value.loading = true;
    const { data, message, success } = await getDeviceList({ ids: items });
    if (!success) throw new Error(message);
    state.value.tableData = data;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    state.value.loading = false;
  }
}

onMounted(() => {
  if (props.deviceIds.length !== 0) handleRefresh(props.deviceIds.join(","));
});
</script>
