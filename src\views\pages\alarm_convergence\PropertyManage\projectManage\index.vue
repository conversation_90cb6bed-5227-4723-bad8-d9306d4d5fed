<template>
  <el-card>
    <pageTemplate v-model:currentPage="state.page" v-model:pageSize="state.size" :total="state.total" :height="height - 40" @size-change="handleRefresh" @current-change="handleRefresh" :showPaging="false">
      <template #left>
        <div class="tw-text-[14px] tw-font-bold">{{ t("project.Project Management") }} - {{ userInfo.currentTenant.name }}[{{ userInfo.currentTenant.abbreviation }}]</div>
      </template>
      <template #right>
        <div>
          <el-button type="primary" :icon="Plus" v-if="userInfo.hasPermission('689703723668078592')" @click="hendleCreate">{{ t("glob.Add Data", { value: t("project.Project") }) }}</el-button>
        </div>
      </template>
      <template #default="{ height: tableHeight }">
        <el-form :model="state" ref="formRef">
          <el-table :data="state.data" border stripe style="width: 100%" :height="tableHeight" :row-key="(row) => row.id" :expand-row-keys="expands" @expand-change="expandColumn" v-loading="state.loading">
            <el-table-column type="expand">
              <template #default="{ row }">
                <div class="tw-mt-2 tw-px-4">
                  <projectFiles :parentId="row.id" />
                  <distributionDevice :parentId="row.id" :deviceIds="row.deviceIds" @refresh="handleRefresh" />
                </div>
              </template>
            </el-table-column>
            <!--  -->
            <TableColumn type="condition" prop="projectName" :label="t('project.Project Name')" filter-multiple show-filter v-model:custom-filtered-value="searchProjectName" :filters="$filter0" @filter-change="handleRefresh()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].projectName`" :rules="[{ required: row.isEdit, message: '项目名称不能为空', trigger: 'blur' }]">
                  <el-input v-model="row.projectName"></el-input>
                </el-form-item>
                <template v-else>{{ row.projectName }}</template>
              </template>
            </TableColumn>
            <TableColumn type="condition" prop="uniformServiceCode" :label="t('project.Unified Service Code')" filter-multiple show-filter v-model:custom-filtered-value="searchServiceCode" :filters="$filter0" @filter-change="handleRefresh()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].uniformServiceCode`">
                  <el-input v-model="row.uniformServiceCode"></el-input>
                </el-form-item>
                <template v-else>{{ row.uniformServiceCode }}</template>
              </template>
            </TableColumn>

            <TableColumn type="condition" prop="projectCode" :label="t('project.Project Code')" filter-multiple show-filter v-model:custom-filtered-value="searchProjectCode" :filters="$filter0" @filter-change="handleRefresh()">
              <!-- <TableColumn type="default" prop="projectCode" :label="t('project.Project Code')"> -->
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].projectCode`">
                  <el-input v-model="row.projectCode"></el-input>
                </el-form-item>
                <template v-else>{{ row.projectCode }}</template>
              </template>
            </TableColumn>
            <TableColumn type="condition" prop="projectLevel" :label="t('project.Level')" filter-multiple show-filter v-model:custom-filtered-value="searchProjectLevel" :filters="$filter2" @filter-change="handleRefresh()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].projectLevel`">
                  <el-input v-model="row.projectLevel"></el-input>
                </el-form-item>
                <template v-else>{{ row.projectLevel }}</template>
              </template>
            </TableColumn>
            <TableColumn type="default" prop="range" :label="t('project.Project Validity Period')" width="250">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].range`">
                  <el-date-picker v-model="dateRange" type="daterange" unlink-panels range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" size="size" />
                </el-form-item>
                <template v-else>{{ row.range && formatRange(row.range) }}</template>
              </template>
            </TableColumn>
            <TableColumn type="condition" prop="duration" :label="t('project.Project Duration')" filter-multiple show-filter v-model:custom-filtered-value="searchDuration" :filters="$filter2" @filter-change="handleRefresh()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].duration`">
                  <el-input v-model="row.duration"></el-input>
                </el-form-item>
                <template v-else>{{ row.duration }}</template>
              </template>
            </TableColumn>
            <TableColumn type="condition" prop="sla" :label="t('project.SLA')" filter-multiple show-filter v-model:custom-filtered-value="searchSlaName" :filters="$filter0" @filter-change="handleRefresh()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].slaName`">
                  <el-select v-model="row.slaId" clearable filterable placeholder="请选择">
                    <el-option v-for="slaItem in slaOptions" :key="slaItem.ruleId" :label="slaItem.ruleName" :value="slaItem.ruleId"> </el-option>
                  </el-select>
                </el-form-item>
                <template v-else>{{ row.slaName }}</template>
              </template>
            </TableColumn>
            <TableColumn type="enum" :prop="`active`" :label="$t('project.Activate')" :width="80" :showOverflowTooltip="true" show-filter v-model:filtered-value="search.active" :filters="[false, true].map((v) => ({ value: v, text: v === false ? '✕' : '✓' }))" @filter-change="handleRefresh()">
              <template #default="{ row, index }">
                <el-form-item v-if="row.isEdit" :prop="`data[${index}].active`">
                  <el-checkbox v-model="row.active"></el-checkbox>
                </el-form-item>
                <template v-else>{{ row.active ? "√" : "✕" }}</template>
              </template>
            </TableColumn>
            <TableColumn type="default" :label="t('glob.operate')">
              <template #default="{ row }">
                <template v-if="row.isEdit">
                  <el-button type="primary" v-if="row.verifyPermissionIds.includes('689703964815392768')" link @click="handleSetSubmit(row)">{{ t("glob.Save") }}</el-button>
                  <el-button type="primary" v-if="row.verifyPermissionIds.includes('689703964815392768')" link @click="handleRefresh">{{ t("glob.Cancel") }}</el-button>
                </template>
                <template v-else>
                  <el-button type="primary" link v-if="row.verifyPermissionIds.includes('689703964815392768')" @click="hendleSetItem(row)">{{ t("glob.edit") }}</el-button>
                  <el-popconfirm :title="t(`project.Are you sure you want to delete`, { name: row.projectName })" @confirm="handleDelItem(row)">
                    <template #reference>
                      <el-button type="danger" v-if="row.verifyPermissionIds.includes('689703997136699392')" link>{{ t("glob.delete") }}</el-button>
                    </template>
                  </el-popconfirm>
                  <el-button type="primary" link :icon="Security" style="font-size: 22px" v-if="row.verifyPermissionIds.includes('689703905411465216')" @click="showSecurityTree({ containerId: row.containerId })"></el-button>
                </template>
              </template>
            </TableColumn>
          </el-table>
        </el-form>

        <projectManageAdd ref="addProjectRef" @refresh="handleRefresh" />
      </template>
    </pageTemplate>
  </el-card>
</template>

<script setup lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { ref, reactive, inject, onMounted, nextTick, toValue, computed } from "vue";

import { ElMessage, type TableColumnCtx, FormInstance } from "element-plus";
import { Plus, WarningFilled, Check } from "@element-plus/icons-vue";
import Security from "@/assets/dp.vue";

import { sizes } from "@/utils/common";

import getUserInfo from "@/utils/getUserInfo";

import pageTemplate from "@/components/pageTemplate.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";

import { getAllProjectPlans, addAllProjectPlans, setAllProjectPlans, delAllProjectPlans } from "@/views/pages/apis/projectPlan";
import { getnewSlaquerySlaList } from "@/views/pages/apis/SlaConfig";

import showSecurityTree from "@/components/security-container";
import projectManageAdd from "./addProject.vue";
import projectFiles from "./projectFiles.vue";
import distributionDevice from "./distributionDevice.vue";
import moment from "moment";

import {} from "@/views/pages/permission";

import { useI18n } from "vue-i18n";

const { t } = useI18n();

defineOptions({ name: "ProjectManage" });

const width = inject("width", ref(0));
const height = inject("height", ref(0));
const dateRange = ref(); // [startDate, endDate]
const clicktype = ref(true); // [startDate, endDate]
const slaOptions = ref([]);

const userInfo: any = getUserInfo();

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: any };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: TableColumnCtx<Record<string, any>>, value: T[keyof T]) => string | import("vue").VNode; showOverflowTooltip?: boolean }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}

const state = reactive<StateData<[]>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  column: [],
  data: [],
  page: 1,
  size: 50,
  sizes,
  total: 0,
});

const search = ref({
  // 项目名称
  eqProjectName: [],
  includeProjectName: [],
  projectNameFilterRelation: "AND",
  neProjectName: [],
  excludeProjectName: [],
  // 统一服务编码
  eqUniformServiceCode: [],
  includeUniformServiceCode: [],
  uniformServiceCodeFilterRelation: "AND",
  neUniformServiceCode: [],
  excludeUniformServiceCode: [],
  // 设备编码
  includeProjectCode: [],
  excludeProjectCode: [],
  projectCodeFilterRelation: "AND",
  eqProjectCode: [],
  neProjectCode: [],
  // 级别
  eqProjectLevel: [],
  neProjectLevel: [],
  projectLevelFilterRelation: "AND",
  // 项目持续时间
  eqDuration: [],
  neDuration: [],
  durationFilterRelation: "AND",
  //sla
  includeSlaName: [],
  excludeSlaName: [],
  slaNameFilterRelation: "AND",
  eqSlaName: [],
  neSlaName: [],
  // 是否激活
  active: "",
});
import { exoprtMatch1, exoprtMatch2, exoprtMatch3 } from "@/components/tableColumn/common";
const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);
const $filter2 = ref(exoprtMatch3);
const searchForm = ref<Record<string, any>>({});
// 项目名称
const searchProject0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchProject1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchProjectName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchProject0ByName) === "include") value0 = search.value.includeProjectName[0] || "";
    if (toValue(searchProject0ByName) === "exclude") value0 = search.value.excludeProjectName[0] || "";
    if (toValue(searchProject0ByName) === "eq") value0 = search.value.eqProjectName[0] || "";
    if (toValue(searchProject0ByName) === "ne") value0 = search.value.neProjectName[0] || "";
    let value1 = "";
    if (toValue(searchProject1ByName) === "include") value1 = search.value.includeProjectName[search.value.includeProjectName.length - 1] || "";
    if (toValue(searchProject1ByName) === "exclude") value1 = search.value.excludeProjectName[search.value.excludeProjectName.length - 1] || "";
    if (toValue(searchProject1ByName) === "eq") value1 = search.value.eqProjectName[search.value.eqProjectName.length - 1] || "";
    if (toValue(searchProject1ByName) === "ne") value1 = search.value.neProjectName[search.value.neProjectName.length - 1] || "";
    return {
      type0: toValue(searchProject0ByName),
      type1: toValue(searchProject1ByName),
      relation: search.value.projectNameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchProject0ByName.value = v.type0 as typeof searchProject0ByName extends import("vue").Ref<infer T> ? T : string;
    searchProject1ByName.value = v.type1 as typeof searchProject1ByName extends import("vue").Ref<infer T> ? T : string;
    search.value.projectNameFilterRelation = v.relation;
    search.value.includeProjectName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeProjectName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqProjectName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neProjectName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
// 项目编码
const searchProject0ByCode = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchProject1ByCode = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchProjectCode = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchService0ByCode) === "include") value0 = search.value.includeProjectCode[0] || "";
    if (toValue(searchService0ByCode) === "exclude") value0 = search.value.excludeProjectCode[0] || "";
    if (toValue(searchService0ByCode) === "eq") value0 = search.value.eqProjectCode[0] || "";
    if (toValue(searchService0ByCode) === "ne") value0 = search.value.neProjectCode[0] || "";
    let value1 = "";
    if (toValue(searchService1ByCode) === "include") value1 = search.value.includeProjectCode[search.value.includeProjectCode.length - 1] || "";
    if (toValue(searchService1ByCode) === "exclude") value1 = search.value.excludeProjectCode[search.value.excludeProjectCode.length - 1] || "";
    if (toValue(searchService1ByCode) === "eq") value1 = search.value.eqProjectCode[search.value.eqProjectCode.length - 1] || "";
    if (toValue(searchService1ByCode) === "ne") value1 = search.value.neProjectCode[search.value.neProjectCode.length - 1] || "";
    return {
      type0: toValue(searchService0ByCode),
      type1: toValue(searchService1ByCode),
      relation: search.value.projectCodeFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchService0ByCode.value = v.type0 as typeof searchService0ByCode extends import("vue").Ref<infer T> ? T : string;
    searchService1ByCode.value = v.type1 as typeof searchService1ByCode extends import("vue").Ref<infer T> ? T : string;
    search.value.projectCodeFilterRelation = v.relation;
    search.value.includeProjectCode = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeProjectCode = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqProjectCode = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neProjectCode = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
// 统一服务编码
const searchService0ByCode = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchService1ByCode = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchServiceCode = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchProject0ByCode) === "include") value0 = search.value.includeUniformServiceCode[0] || "";
    if (toValue(searchProject0ByCode) === "exclude") value0 = search.value.excludeUniformServiceCode[0] || "";
    if (toValue(searchProject0ByCode) === "eq") value0 = search.value.eqUniformServiceCode[0] || "";
    if (toValue(searchProject0ByCode) === "ne") value0 = search.value.neUniformServiceCode[0] || "";
    let value1 = "";
    if (toValue(searchProject1ByCode) === "include") value1 = search.value.includeUniformServiceCode[search.value.includeUniformServiceCode.length - 1] || "";
    if (toValue(searchProject1ByCode) === "exclude") value1 = search.value.excludeUniformServiceCode[search.value.excludeUniformServiceCode.length - 1] || "";
    if (toValue(searchProject1ByCode) === "eq") value1 = search.value.eqUniformServiceCode[search.value.eqUniformServiceCode.length - 1] || "";
    if (toValue(searchProject1ByCode) === "ne") value1 = search.value.neUniformServiceCode[search.value.neUniformServiceCode.length - 1] || "";
    return {
      type0: toValue(searchProject0ByCode),
      type1: toValue(searchProject1ByCode),
      relation: search.value.uniformServiceCodeFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchProject0ByCode.value = v.type0 as typeof searchProject0ByCode extends import("vue").Ref<infer T> ? T : string;
    searchProject1ByCode.value = v.type1 as typeof searchProject1ByCode extends import("vue").Ref<infer T> ? T : string;
    search.value.uniformServiceCodeFilterRelation = v.relation;
    search.value.includeUniformServiceCode = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeUniformServiceCode = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqUniformServiceCode = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neUniformServiceCode = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
// 级别
const searchProjectLevel0 = ref<"eq" | "ne">("eq");
const searchProjectLevel1 = ref<"eq" | "ne">("eq");
const searchProjectLevel = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchProjectLevel0) === "eq") value0 = search.value.eqProjectLevel[0] || "";
    if (toValue(searchProjectLevel0) === "ne") value0 = search.value.neProjectLevel[0] || "";

    let value1 = "";
    if (toValue(searchProjectLevel1) === "eq") value1 = search.value.eqProjectLevel[1] || "";
    if (toValue(searchProjectLevel1) === "ne") value1 = search.value.neProjectLevel[1] || "";

    return {
      type0: toValue(searchProjectLevel0),
      type1: toValue(searchProjectLevel1),
      relation: search.value.projectLevelFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchProjectLevel0.value = v.type0 as "eq" | "ne";
    searchProjectLevel1.value = v.type1 as "eq" | "ne";
    search.value.projectLevelFilterRelation = v.relation;
    search.value.eqProjectLevel = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neProjectLevel = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// 项目持续时间
const searchDuration0 = ref<"eq" | "ne">("eq");
const searchDuration1 = ref<"eq" | "ne">("eq");
const searchDuration = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchDuration0) === "eq") value0 = search.value.eqDuration[0] || "";
    if (toValue(searchDuration0) === "ne") value0 = search.value.neDuration[0] || "";

    let value1 = "";
    if (toValue(searchDuration1) === "eq") value1 = search.value.eqDuration[1] || "";
    if (toValue(searchDuration1) === "ne") value1 = search.value.neDuration[1] || "";

    return {
      type0: toValue(searchDuration0),
      type1: toValue(searchDuration1),
      relation: search.value.durationFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchDuration0.value = v.type0 as "eq" | "ne";
    searchDuration1.value = v.type1 as "eq" | "ne";
    search.value.durationFilterRelation = v.relation;
    search.value.eqDuration = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neDuration = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// SLA名称
const searchSlaName0 = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchSlaName1 = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchSlaName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchSlaName0) === "include") value0 = search.value.includeSlaName[0] || "";
    if (toValue(searchSlaName0) === "exclude") value0 = search.value.excludeSlaName[0] || "";
    if (toValue(searchSlaName0) === "eq") value0 = search.value.eqSlaName[0] || "";
    if (toValue(searchSlaName0) === "ne") value0 = search.value.neSlaName[0] || "";

    let value1 = "";
    if (toValue(searchSlaName1) === "include") value1 = search.value.includeSlaName[1] || "";
    if (toValue(searchSlaName1) === "exclude") value1 = search.value.excludeSlaName[1] || "";
    if (toValue(searchSlaName1) === "eq") value1 = search.value.eqSlaName[1] || "";
    if (toValue(searchSlaName1) === "ne") value1 = search.value.neSlaName[1] || "";

    return {
      type0: toValue(searchSlaName0),
      type1: toValue(searchSlaName1),
      relation: search.value.slaNameFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchSlaName0.value = v.type0 as "include" | "exclude" | "eq" | "ne";
    searchSlaName1.value = v.type1 as "include" | "exclude" | "eq" | "ne";
    search.value.slaNameFilterRelation = v.relation;
    search.value.includeSlaName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    search.value.excludeSlaName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    search.value.eqSlaName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    search.value.neSlaName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

//
const addProjectRef = ref<InstanceType<typeof projectManageAdd>>();

const formRef = ref<FormInstance>();
function formatRange(range) {
  if (!range || !range.start || !range.end) return "--";
  const startDate = moment(Number(range.start)).format("YYYY-MM-DD");
  const endDate = moment(Number(range.end)).format("YYYY-MM-DD");
  return `${startDate} 至 ${endDate}`;
}
function hendleCreate(row) {
  if (!addProjectRef.value) return false;
  addProjectRef.value.open(row);
}
function clickEnable() {
  clicktype.value = !clicktype.value;
}
// eslint-disable-next-line @typescript-eslint/no-unused-vars

async function getSlaList() {
  const parameters = {
    containerId: userInfo.currentTenant.containerId,
    queryPermissionId: "515413313438351360",
    verifyPermissionIds: "",
  };
  try {
    const res = await getnewSlaquerySlaList(parameters);
    if (res) slaOptions.value = res;
  } catch (error) {
    error instanceof Error && ElMessage.error(error);
  }
}

function hendleSetItem(row) {
  if (state.data.filter((v: any) => v.isEdit).length) return ElMessage.warning(t("orderGroup.Please save the information that you are currently editing first"));
  row.isEdit = true;
  // 转换 row.range 为 dateRange 所需的格式
  if (row.range && row.range.start && row.range.end) {
    dateRange.value = [moment(Number(row.range.start)).toISOString(), moment(Number(row.range.end)).toISOString()];
  } else {
    dateRange.value = [];
  }
}

function handleSetSubmit(row) {
  const range = dateRange.value instanceof Array && dateRange.value.length ? { start: moment(dateRange.value[0]).valueOf(), end: moment(dateRange.value[1]).valueOf() } : { start: null, end: null };
  const selectedSla = slaOptions.value.find((item) => item.ruleId === row.slaId) || {};
  formRef.value &&
    formRef.value.validate(async (valid) => {
      if (!valid) return;
      try {
        const { id, projectName, projectCode, tenantId, uniformServiceCode, projectLevel, active, slaId, duration } = row;
        const params = {
          id,
          projectName,
          projectCode,
          tenantId,
          duration,
          uniformServiceCode,
          projectLevel,
          slaId,
          slaName: selectedSla?.ruleName || "",
          active,
          range: range || {},
        };
        const { message, success } = await setAllProjectPlans(params);
        if (!success) throw new Error(message);
        ElMessage.success(t("axios.Operation successful"));
        await nextTick();
        handleRefresh();
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    });
}

async function handleDelItem(row) {
  try {
    const { message, success } = await delAllProjectPlans({ prodId: row.id });
    if (!success) throw new Error(message);
    ElMessage.success(t("axios.Operation successful"));
    handleRefresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

const expands = ref<string[]>([]);
function expandColumn(row, expandedRows) {
  // 每次只展开一行
  if (expandedRows.length) {
    expands.value = [];
    if (row) {
      expands.value.push(row.id);
    }
  } else {
    expands.value = [];
  }
}

async function handleRefresh() {
  console.log("search :>> ", search.value);
  state.loading = true;
  try {
    const { data, message, success, total } = await getAllProjectPlans(search.value);
    if (!success) throw new Error(message);
    state.data = data.map((v) => Object.assign(v, { isEdit: false }));
    state.total = Number(total);
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    state.loading = false;
  }
}

onMounted(() => {
  handleRefresh();
  getSlaList();
});
</script>
