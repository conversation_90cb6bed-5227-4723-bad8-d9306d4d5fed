<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="handleRefreshRegionTable()" @current-change="handleRefreshRegionTable()">
      <template #right>
        <span class="">
          <el-button v-if="userInfo.hasPermission(资产管理中心_区域_新增)" type="primary" :icon="Plus" @click="handleOpenEdit()">{{ $t("region.New Data", { value: $t("region.Region") }) }}</el-button>
        </span>
      </template>
      <template #default="{ height: tableHeight }">
        <!-- :expand-row-keys="tableexpand" -->
        <el-table v-loading="loading" stripe :data="tableData" :height="tableHeight" row-key="id" :tree-props="{ children: '_' }" :indent="16" style="width: 100%">
          <el-table-column type="expand" label="" align="left" :width="48">
            <template #default="{ row: baseRow, store: { states }, $index, expanded }">
              <div v-if="expanded">
                <el-table :data="baseRow.children" :show-header="false" :row-key="toValue(states.rowKey)" :default-expanded-keys="[]" :indent="toValue(states.indent)" :row-class-name="({ rowIndex }) => ($index + (rowIndex % 2) ? '' : 'el-table__row--striped')" :tree-props="{ children: 'children' }" class="tw-mb-[8px] tw-mt-[-8px]" :cell-style="(data) => (data.column.property !== 'operation' ? { 'padding-left': '32px' } : {})">
                  <el-table-column prop="name" :label="$t('region.Name')" :formatter="formatterTable">
                    <template #default="{ row, column }">
                      <span>{{ row[column.property] || "--" }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column prop="label" label="标签" :formatter="formatterTable" :width="240">
                    <!-- <template #default="{ row, column }">
                      <span :style="{ marginLeft: `${row.depth * toValue(states.indent)}px` }">{{ row[column.property] || "--" }}</span>
                    </template> -->
                  </el-table-column>

                  <el-table-column prop="description" label="描述" :formatter="formatterTable" :width="240"></el-table-column>
                  <el-table-column prop="id" label="ID" :formatter="formatterTable"></el-table-column>
                  <el-table-column prop="active" label="是否激活" :formatter="formatterTable" :min-width="100">
                    <template #default="{ row }">
                      <el-text :type="row.active == true ? 'success' : 'info'">{{ row.active == true ? $t("region.Activation") : $t("region.Not Active") }}</el-text>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('glob.operate')" prop="operation" align="left" fixed="right" :width="210">
                    <template #default="{ row }">
                      <span class="">
                        <el-link v-if="row.hasPermissionIds.includes(资产管理中心_区域_新增)" type="primary" :underline="false" :icon="Plus" class="tw-mx-[2.5px] tw-align-middle" @click="handleOpenEdit({ parentId: row.id })">{{ $t("region.New Data", { value: $t("region.Area") }) }}</el-link>
                      </span>
                      <span class="">
                        <el-link v-if="row.hasPermissionIds.includes(资产管理中心_区域_编辑)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleOpenEdit(row)">{{ $t("glob.edit") }}</el-link>
                      </span>
                      <span class="">
                        <el-link v-if="row.hasPermissionIds.includes(资产管理中心_区域_删除)" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleDelRegion(row)">{{ $t("glob.delete") }}</el-link>
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
                <ModelExpand
                  v-if="baseRow.id && expanded"
                  :key="baseRow.id"
                  type="regions"
                  :id="baseRow.id"
                  :create="{
                    contact: !userInfo.hasPermission(资产管理中心_区域_分配联系人),
                  }"
                  :viewer="{
                    contact: !userInfo.hasPermission(资产管理中心_联系人_查看联系人),
                  }"
                  :remove="{
                    contact: !userInfo.hasPermission(资产管理中心_区域_分配联系人),
                  }"
                ></ModelExpand>
              </div>
            </template>
          </el-table-column>
          <TableColumn type="condition" :prop="`name`" :label="$t('region.Name')" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="handleRefreshRegionTable()" :formatter="formatterTable"> </TableColumn>

          <TableColumn type="condition" :prop="`label`" :label="$t('region.Label')" width="240" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByLabel" :filters="$filter0" @filter-change="handleRefreshRegionTable()" :formatter="formatterTable"> </TableColumn>

          <!-- <el-table-column prop="description" label="描述" :formatter="formatterTable" :width="240"></el-table-column> -->
          <TableColumn type="condition" :prop="`description`" :label="$t('region.Description')" width="240" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByDescription" :filters="$filter0" @filter-change="handleRefreshRegionTable()" :formatter="formatterTable"> </TableColumn>

          <!-- <el-table-column prop="externalId" label="外部ID" :formatter="formatterTable" :width="160"></el-table-column> -->
          <TableColumn type="condition" :prop="`id`" :label="`ID`" :formatter="formatterTable"> </TableColumn>
          <!-- :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchExternalId" :filters="$filter0" @filter-change="handleRefreshRegionTable()"  -->

          <!-- <el-table-column prop="label" :label="i18n.t('Is Active')" :width="100" :showOverflowTooltip="true" show-filter v-model:filtered-value="searchForm.active" :filters="['true', 'false'].map((v) => ({ value: v, text: v === 'true' ? '激活' : '未激活' }))" @filter-change="handleRefreshRegionTable()">
            <template #default="{ row }">
              <el-text :type="row.active == true ? 'success' : 'info'">{{ row.active == true ? "激活" : "未激活" }}</el-text>
            </template>
          </el-table-column> -->
          <TableColumn type="enum" :label="$t('region.Is Active')" :min-width="100" :showOverflowTooltip="true" show-filter v-model:filtered-value="searchForm.active" :filters="['true', 'false'].map((v) => ({ value: v, text: v === 'true' ? $t('region.Activation') : $t('region.Not Active') }))" @filter-change="handleRefreshRegionTable()">
            <template #default="{ row }">
              <el-text :type="row.active == true ? 'success' : 'info'">{{ row.active == true ? $t("region.Activation") : $t("region.Not Active") }}</el-text>
            </template>
          </TableColumn>
          <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="210">
            <template #default="{ row }">
              <span class="">
                <el-link v-if="row.hasPermissionIds.includes(资产管理中心_区域_新增)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleOpenEdit({ parentId: row.id })">{{ $t("region.New Data", { value: $t("region.Area") }) }}</el-link>
              </span>
              <span class="">
                <el-link v-if="row.hasPermissionIds.includes(资产管理中心_区域_编辑)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleOpenEdit(row)">{{ $t("glob.edit") }}</el-link>
              </span>
              <span class="">
                <el-link v-if="row.hasPermissionIds.includes(资产管理中心_区域_删除)" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleDelRegion(row)">{{ $t("glob.delete") }}</el-link>
              </span>
              <span class="">
                <!-- 区域('603899903264948224') -->
                <el-link :type="row.hasPermissionIds.includes(资产管理中心_区域_安全) ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="row.hasPermissionIds.includes(资产管理中心_区域_安全) ? loading : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
  <Editor ref="editorRef" title="时区"></Editor>
  <region-edit ref="regionEditRef" @refresh="handleRefreshRegionTable" />
  <el-dialog v-model="dialogVisibleshow" title="查看安全目录" width="500">
    <treeAuth :proptreeId="containerId" :treeStyle="treeStyle" ref="treeAuthRef"></treeAuth>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisibleshow = false">取消</el-button>
      </div>
      <!-- <assign-contacts ref="assignContactsRef" @submitForm="handleSubmitAssignContacts" /> -->
    </template>
  </el-dialog>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, toValue, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useTemplateRefsList } from "@vueuse/core";
import { useI18n } from "vue-i18n";

import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete, Download } from "@element-plus/icons-vue";

import deviceCreate from "./deviceCreate.vue";
import treeAuth from "@/components/treeAuth/index.vue";
// import bindContacts from "module";
// import assignContacts from "module";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMessageBox } from "element-plus";
import pageTemplate from "@/components/pageTemplate.vue";
import Editor from "./Editor.vue";

import regionEdit from "./regionEdit.vue";
import ModelExpand from "@/views/pages/modelExpand/Model.vue";
// import regionTable from "./regionTable.vue";
// import bindContacts from "@/components/bindContacts/vIndex";
// import assignContacts from "@/components/bindContacts/assignContacts";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";

import TableColumn from "@/components/tableColumn/TableColumn.vue";

/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";

import { getRegionsTenantCurrent, delRegionsById, getRegionTree } from "@/views/pages/apis/regionManage";
import { getDeviceList, deleteDevice, getRegionsContacts, delRegionsContacts, exportDevice, type SlaConfigList as DataItem } from "@/views/pages/apis/deviceManage";
import { setRegionsContacts } from "@/views/pages/apis/regionManage";
import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
import { 资产管理中心_区域_可读, 资产管理中心_区域_新增, 资产管理中心_区域_编辑, 资产管理中心_区域_删除, 资产管理中心_区域_分配联系人, 资产管理中心_区域_分配行动策略, 资产管理中心_区域_安全, 资产管理中心_区域_所有权限, 资产管理中心_联系人_可读, 资产管理中心_联系人_查看联系人 } from "@/views/pages/permission";
const ctx = getCurrentInstance()!;
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "regionManage" });
const treeStyle = ref({
  pointerEvents: "none",
});
const editorRef = ref<InstanceType<typeof Editor>>();
// const assignContactsRef = ref<InstanceType<typeof assignContacts>>();
const regionEditRef = ref<InstanceType<typeof regionEdit>>();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const dialogVisibleshow = ref(false);

const siteConfig = useSiteConfig();
const userInfo = getUserInfo();

// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

// const assignContacts = ref<InstanceType<typeof AssignContacts>>();
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);

const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);

const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);

// 搜索关键字
const searchForm = ref<Record<string, any>>({
  eqName: [] /* 等于的区域名称 */,
  includeName: [] /* 包含的区域名称 */,
  nameFilterRelation: "AND" /* 区域名称过滤关系(AND,OR) */,
  neName: [] /* 不等于的区域名称 */,
  excludeName: [] /* 不包含的区域名称 */,

  eqLabel: [] /* 等于的区域标签 */,
  includeLabel: [] /* 包含的区域标签 */,
  labelFilterRelation: "AND" /* 区域标签过滤关系(AND,OR) */,
  neLabel: [] /* 不等于的区域标签 */,
  excludeLabel: [] /* 不包含的区域标签 */,

  eqDescription: [] /* 等于的区域描述 */,
  includeDescription: [] /* 包含的区域描述 */,
  descriptionFilterRelation: "" /* 区域描述过滤关系(AND,OR) */,
  neDescription: [] /* 不等于的区域描述 */,
  excludeDescription: [] /* 不包含的区域描述 */,

  eqExternalId: [] /* 等于的区域外部ID */,
  includeExternalId: [] /* 包含的区域外部ID */,
  externalIdFilterRelation: "AND" /* 区域外部ID过滤关系(AND,OR) */,
  neExternalId: [] /* 不等于的区域外部ID */,
  excludeExternalId: [] /* 不包含的区域外部ID */,

  active: "",
});

const searchType0ExternalId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ExternalId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchExternalId = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ExternalId) === "include") value0 = searchForm.value.includeExternalId[0] || "";
    if (toValue(searchType0ExternalId) === "exclude") value0 = searchForm.value.excludeExternalId[0] || "";
    if (toValue(searchType0ExternalId) === "eq") value0 = searchForm.value.eqExternalId[0] || "";
    if (toValue(searchType0ExternalId) === "ne") value0 = searchForm.value.neExternalId[0] || "";
    let value1 = "";
    if (toValue(searchType1ExternalId) === "include") value1 = searchForm.value.includeExternalId[searchForm.value.includeExternalId.length - 1] || "";
    if (toValue(searchType1ExternalId) === "exclude") value1 = searchForm.value.excludeExternalId[searchForm.value.excludeExternalId.length - 1] || "";
    if (toValue(searchType1ExternalId) === "eq") value1 = searchForm.value.eqExternalId[searchForm.value.eqExternalId.length - 1] || "";
    if (toValue(searchType1ExternalId) === "ne") value1 = searchForm.value.neExternalId[searchForm.value.neExternalId.length - 1] || "";
    return {
      type0: toValue(searchType0ExternalId),
      type1: toValue(searchType1ExternalId),
      relation: searchForm.value.externalIdFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ExternalId.value = v.type0 as typeof searchType0ExternalId extends import("vue").Ref<infer T> ? T : string;
    searchType1ExternalId.value = v.type1 as typeof searchType1ExternalId extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.externalIdFilterRelation = v.relation;
    searchForm.value.includeExternalId = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeExternalId = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqExternalId = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neExternalId = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByDescription = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByDescription) === "include") value0 = searchForm.value.includeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "exclude") value0 = searchForm.value.excludeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "eq") value0 = searchForm.value.eqDescription[0] || "";
    if (toValue(searchType0ByDescription) === "ne") value0 = searchForm.value.neDescription[0] || "";
    let value1 = "";
    if (toValue(searchType1ByDescription) === "include") value1 = searchForm.value.includeDescription[searchForm.value.includeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "exclude") value1 = searchForm.value.excludeDescription[searchForm.value.excludeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "eq") value1 = searchForm.value.eqDescription[searchForm.value.eqDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "ne") value1 = searchForm.value.neDescription[searchForm.value.neDescription.length - 1] || "";
    return {
      type0: toValue(searchType0ByDescription),
      type1: toValue(searchType1ByDescription),
      relation: searchForm.value.descriptionFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByDescription.value = v.type0 as typeof searchType0ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchType1ByDescription.value = v.type1 as typeof searchType1ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.descriptionFilterRelation = v.relation;
    searchForm.value.includeDescription = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeDescription = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqDescription = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neDescription = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByLabel = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByLabel = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByLabel = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByLabel) === "include") value0 = searchForm.value.includeLabel[0] || "";
    if (toValue(searchType0ByLabel) === "exclude") value0 = searchForm.value.excludeLabel[0] || "";
    if (toValue(searchType0ByLabel) === "eq") value0 = searchForm.value.eqLabel[0] || "";
    if (toValue(searchType0ByLabel) === "ne") value0 = searchForm.value.neLabel[0] || "";
    let value1 = "";
    if (toValue(searchType1ByLabel) === "include") value1 = searchForm.value.includeLabel[searchForm.value.includeLabel.length - 1] || "";
    if (toValue(searchType1ByLabel) === "exclude") value1 = searchForm.value.excludeLabel[searchForm.value.excludeLabel.length - 1] || "";
    if (toValue(searchType1ByLabel) === "eq") value1 = searchForm.value.eqLabel[searchForm.value.eqLabel.length - 1] || "";
    if (toValue(searchType1ByLabel) === "ne") value1 = searchForm.value.neLabel[searchForm.value.neLabel.length - 1] || "";
    return {
      type0: toValue(searchType0ByLabel),
      type1: toValue(searchType1ByLabel),
      relation: searchForm.value.labelFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByLabel.value = v.type0 as typeof searchType0ByLabel extends import("vue").Ref<infer T> ? T : string;
    searchType1ByLabel.value = v.type1 as typeof searchType1ByLabel extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.labelFilterRelation = v.relation;
    searchForm.value.includeLabel = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeLabel = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqLabel = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neLabel = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByName) === "include") value0 = searchForm.value.includeName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = searchForm.value.excludeName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = searchForm.value.eqName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = searchForm.value.neName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = searchForm.value.includeName[searchForm.value.includeName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = searchForm.value.excludeName[searchForm.value.excludeName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = searchForm.value.eqName[searchForm.value.eqName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = searchForm.value.neName[searchForm.value.neName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: searchForm.value.nameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.nameFilterRelation = v.relation;
    searchForm.value.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const tableData = ref<DataItem[]>([]);
const paging = reactive({
  pageNumber: 1,
  pageSize: 50,
  total: 0,
});

const allRegion = ref([]);
const allRegionByPage = ref([]);
const allRegionSelect = ref([]);
const containerId = ref("");

// const refs = reactive<Record<string, InstanceType<typeof bindContacts>>>({});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  handleRefreshRegionTable();
}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

function handleDelContact(v) {
  ElMessageBox.confirm(`确定删除该${v.tab.label}"${v.contact.name}"`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      const params = {
        contactId: v.contact.id,
        contactType: v.tab.name,
      };
      delRegionsContacts(params, v.id).then(({ success, data }) => {
        if (success) {
          ElMessage.success("操作成功");
          expandChange(tableData.value.find((i) => i.id === v.id));
        } else ElMessage.error(JSON.parse(data)?.message || "操作失败");
      });
    })
    .catch(() => {
      /*  */
    });
}
const tableexpand = ref<string[]>([]);
function expandChange(row: DataItem, expand: DataItem[]) {
  tableexpand.value = expand.map((v) => v.id);
  // nextTick(() => {
  //   setTimeout(() => {
  //     if (!refs[`contacts-${row.id}`]) return false;
  //     if (!refs[`contacts-${row.id}`].tabs || !refs[`contacts-${row.id}`].tabs.length) refs[`contacts-${row.id}`].getTabs(); // 如果已经获取了tab不在重新请求防止赋值
  //     getRegionsContacts({ id: row.id }).then(({ success, data }) => {
  //       if (success) {
  //         nextTick(() => {
  //           refs[`contacts-${row.id}`]?.setContacts(data);
  //         });
  //       }
  //     });
  //   }, 500);
  // });
}
// function handleSubmitAssignContacts(v) {
//   setRegionsContacts(v.params, v.id).then(({ success, data }) => {
//     if (success) {
//       ElMessage.success("操作成功");
//       expandChange(tableData.value.find((i) => i.id === v.id));

//       assignContactsRef.value?.beforeClose();
//     } else ElMessage.error(JSON.parse(data)?.message || "操作失败");
//   });
// }
// function handleAssignContacts({ rowId, type }) /*分配联系人*/ {
//   assignContactsRef.value?.open(rowId, type);
// }
// function editRowState(row) {
//   let getRow = (list) => {
//     for (let i = 0; i < list.length; i++) {
//       if (row.id === list[i].id) {
//         // console.log(list[i]);
//         list[i].isEdit = !list[i].isEdit;
//         break;
//       } else getRow(list[i].children);
//     }
//   };
//   getRow(tableData.value);
// }

function handleOpenEdit(row = {}) {
  regionEditRef.value.open(row);
}
// function handleSizeChange(v) {
//   paging.pageSize = v;
//   handleRefreshRegionTable();
// }
// function handleCurrentPageChange(v) {
//   paging.pageNumber = v;
//   handleRefreshRegionTable();
// }
async function handleRefreshRegionTable() {
  const { success, data, message, page, size, total } = await getRegionTree({ ...searchForm.value, pageNumber: paging.pageNumber, pageSize: paging.pageSize, parentId: "-1", sort: "createdTime,desc" });

  if (!success) throw new Error(message);
  tableData.value = setTreeDepth(data instanceof Array ? data : []);

  paging.pageNumber = Number(page);
  paging.pageSize = Number(size);
  paging.total = Number(total);
}

function setTreeDepth(data: DataItem[], depth = 0) {
  return data.map((v) => Object.assign(v, { depth, children: setTreeDepth(v.children instanceof Array ? v.children : [], depth + 1) }));
}
function deleteTreeData(data: DataItem[], findData: DataItem) {
  for (let index = 0; index < data.length; index++) {
    if (data[index] === findData) {
      data.splice(index, 1);
      return data;
    }
    const isFind = deleteTreeData(data[index].children, findData);
    if (isFind.length) return isFind;
  }
  return [];
}
function setTableDataByPage(data) {
  const result = [];
  for (let i = 0, len = data.length; i < len; i += paging.pageSize) {
    result.push(data.slice(i, i + paging.pageSize));
  }
  allRegionByPage.value = result;
  return result[paging.pageNumber - 1] || [];
}
function setTableData(data) {
  allRegion.value = JSON.parse(JSON.stringify(data));
  let _formatter = (list) => {
    for (let i = 0; i < list.length; i++) {
      list[i].children = [];
      list[i].isEdit = false;
      const _filter = allRegion.value.filter((v) => {
        return list[i].id === v.parentId;
      });
      if (_filter && _filter?.length) {
        list[i].children = _filter;
        _formatter(list[i].children);
      }
    }
  };
  const result = data.filter((v) => !v.parentId);
  _formatter(result);
  _formatter = null;
  // console.log("result", result);
  return result;
}
function handleDelRegion(row) {
  ElMessageBox.confirm(`确定删除区域"${row.name}"?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      const { success, data, message } = await delRegionsById({ id: row.id });
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
      handleRefreshRegionTable();
    } catch (error) {
      error instanceof Error && ElMessage.error(error.message);
    }
  });
}
async function handleAddChild(row) {
  await nextTick();
  // console.log(ctx.refs[`table-${row.id}`]);
  ctx.refs[`table-${row.id}`].handleAddRegion();

  // const editData = row.children.find(({ id }) => !id);
  // // // console.log(row, 7777777);
  // const newRegion = Object.assign(
  //   {
  //     parentId: row.id,
  //     isEdit: true,
  //     label: "",
  //     name: "",
  //     description: "",
  //     externalId: "",
  //   },
  //   editData || {}
  // );
  // // row.children = row.children.filter((v) => v.id)
  // if (row.isExpend) {
  //   row.children.unshift(newRegion);
  // } else {
  //   ctx.refs[`${row.parentId}-table`]?.toggleRowExpansion(row);
  //   row.children.unshift(newRegion);
  // }
}
function formatterTable(_row, _col, v) {
  switch (_col.property) {
    default:
      return v || "--";
  }
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
