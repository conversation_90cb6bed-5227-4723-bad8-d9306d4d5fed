<template>
  <el-dialog :title="$t(isEdit ? 'region.Edit Data' : 'region.New Data', { value: form.parentId ? $t('region.Area') : $t('region.Region') })" v-model="dialogFormVisible" :before-close="beforeClose">
    <!-- <el-scrollbar :height="500"> -->
    <el-form ref="form" :model="form" label-width="80px" :rules="rules">
      <el-row :gutter="20">
        <el-col :sm="24" :lg="12">
          <el-form-item :label="$t('region.Label')" prop="label">
            <el-input v-model="form.label" :placeholder="$t('region.Please enter the label')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :sm="24" :lg="11">
          <el-form-item :label="$t('region.Name')" prop="name">
            <el-input v-model="form.name" :placeholder="$t('region.Please enter the name')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :sm="24" :lg="12">
          <el-form-item :label="$t('region.Active')" prop="active">
            <el-checkbox v-model="form.active"></el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :span="23">
          <el-form-item :label="$t('region.Description')" prop="description">
            <el-input type="textarea" v-model="form.description" :placeholder="$t('region.Please enter the description')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="23">
          <el-form-item :label="$t('region.External Id')" prop="externalId">
            <el-input v-model="form.externalId" :placeholder="$t('region.Please enter the external Id')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :sm="24" :lg="12">
          <el-form-item :label="$t('region.Longitude')" prop="longitude">
            <el-input v-model="form.longitude" :placeholder="$t('region.Please enter the longitude')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :sm="23" :lg="11">
          <el-form-item :label="$t('region.Latitude')" prop="latitude">
            <el-input v-model="form.latitude" :placeholder="$t('region.Please enter the latitude')"></el-input>
          </el-form-item>
        </el-col>
        <!-- <FormItem :span="width > 600 ? 12 : 24" :label="`是否激活`" tooltip="" prop="active" :rules="[]">
          <el-checkbox v-model="form.active"></el-checkbox>
        </FormItem> -->
      </el-row>
    </el-form>
    <!-- </el-scrollbar> -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="beforeClose">{{ $t("glob.Cancel") }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t("glob.Confirm") }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

// import aMap from "@/components/aMap.vue";
import { createRegionsTenantCurrent, editRegionsById } from "@/views/pages/apis/regionManage";
import { ElMessage } from "element-plus";
import { getTenantInfo } from "@/views/pages/apis/tenant";

export default {
  components: {},
  emits: ["refresh"],
  // components: { aMap },
  data() {
    return {
      dialogFormVisible: false,
      form: {
        id: "",
        parentId: "" /* 父区域ID */,
        label: "" /*  */,
        name: "" /* 区域名 */,
        description: "" /* 描述信息 */,
        externalId: "" /* 外部ID */,
        latitude: "" /* 纬度 */,
        longitude: "" /* 经度 */,
        active: true /* 是否激活 */,
        containerId: "" /* 安全容器ID */,
      },
      // form: {
      //   latitude: "",
      //   longitude: "",
      // },
      isEdit: false,
    };
  },
  computed: {
    rules() {
      return {
        name: [
          {
            required: true,
            message: this.$t("region.Please enter the name"),
            trigger: ["bulr", "change"],
          },
        ],
      };
    },
  },
  created() {
    // this.runningInit();
  },
  methods: {
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) return valid;
        const params = {
          label: this.form.label,
          name: this.form.name,
          description: this.form.description,
          externalId: this.form.externalId,
          latitude: this.form.latitude,
          longitude: this.form.longitude,
          active: this.form.active,
          containerId: this.form.containerId,
        };
        // console.log(params);

        if (!this.form.id) Object.assign(params, { parentId: this.form.parentId });
        (this.form.id ? editRegionsById : createRegionsTenantCurrent)(params, this.form?.id || null)
          .then(({ success, data }) => {
            if (success) {
              ElMessage.success("操作成功");
              this.$emit("refresh");
              this.beforeClose();
            } else ElMessage.error(JSON.parse(data)?.message || "操作失败");
          })
          .catch((e) => {
            if (e instanceof Error) ElMessage.error(e.message);
          });
      });
    },
    open(row = {}) {
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.form = {
          id: row.id,
          parentId: row.parentId,
          label: row.label,
          name: row.name,
          description: row.description,
          externalId: row.externalId,
          latitude: row.latitude,
          longitude: row.longitude,
          active: row.id ? row.active : true,
          containerId: row.containerId,
        };
      });
      this.isEdit = row.id ? true : false;
      this.runningInit();
    },
    setPosition(v) {
      this.form.latitude = v.lat;
      this.form.longitude = v.lng;
    },
    beforeClose(done) {
      this.$refs.form.clearValidate();
      this.$refs.form.resetFields();
      if (done instanceof Function) done();
      else this.dialogFormVisible = false;
    },
    async runningInit() {
      (async (req) => {
        const { success, message, data } = await getTenantInfo(req);
        if (!success) throw Object.assign(new Error(message), { success, data });

        this.form.containerId = data.containerId;
      })({});
    },
  },
  expose: ["open", "beforeClose"],
};
</script>
<style lang="scss" scoped>
.res-dialog {
  :deep(.el-dialog__body) {
    overflow: auto !important;
    height: 400px !important;
  }
}
</style>
