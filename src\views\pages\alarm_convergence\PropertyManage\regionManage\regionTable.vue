<template>
  <!-- <el-button @click="handleAddRegion">...</el-button> -->
  <div>
    <el-table stripe :ref="`${parentId}-table`" :data="tableData" style="width: 100%" :show-header="showHeader" :row-class-name="getRowClass" @expand-change="expandChange">
      <el-table-column type="expand" width="50">
        <template #default="scope">
          <region-table :ref="`table-${scope.row.id}`" :parentId="scope.row.id" :showHeader="false" />
        </template>
      </el-table-column>
      <el-table-column :prop="item.prop" :label="item.label" v-for="item in tableCols" :key="`${parentId}-child-${item.prop}`">
        <template #default="{ row }">
          <div v-show="row.isEdit">
            <el-input v-show="row.isEdit" v-model="row[item.prop]" :placeholder="`请输入${item.label}`"></el-input>
          </div>
          <div v-show="!row.isEdit">
            {{ row[item.prop] || "--" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="left" width="200">
        <template #default="{ row, $index }">
          <div v-show="row.isEdit">
            <el-button type="text" @click="handleEditChild(row)">保存</el-button>
            <el-button type="text" textColor="danger" @click="handleEditState(row, $index)">取消</el-button>
          </div>
          <div v-show="!row.isEdit">
            <el-button type="text" @click="() => (row.isEdit = true)">编辑</el-button>
            <el-button type="text" @click="handleAddChildRegion(row)">新增区域</el-button>
            <el-button type="text" textColor="danger" @click="handleDelRegion(row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { createRegionsTenantCurrent, editRegionsById } from "@/views/pages/apis/regionManage";
import { getRegionTree, delRegionsById } from "@/views/pages/apis/regionManage";
import { ElMessage } from "element-plus";
import { defineComponent, defineAsyncComponent } from "vue";
import { ElMessageBox } from "element-plus";

import mixin from "./js/mixin";
export default defineComponent({
  name: "regionTable",
  // inject: ["refresh"],
  // components: { regionTable: defineAsyncComponent((_) => import("./regionTable")) },
  mixins: [mixin],
  props: {
    showHeader: {
      type: Boolean,
      default: true,
    },
    parentId: {
      type: String || Number,
      default: "",
    },
  },
  emits: ["refresh"],
  data() {
    return {
      getRowKeys(row) {
        return row.id;
      },
      tableData: [],
      expandRow: [], // 展开的数据
    };
  },
  computed: {
    tableCols() {
      return [
        { prop: "label", label: "标签" },
        { prop: "name", label: "名称" },
        { prop: "description", label: "描述" },
        { prop: "externalId", label: "外部ID" },
      ];
    },
  },
  watch: {
    // data() {
    //   this.setTable();
    // },
  },
  mounted() {
    this.handleRefresh();
  },
  methods: {
    handleDelRegion(row) {
      ElMessageBox.confirm(`确定删除区域"${row.name}"?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const { success, message } = await delRegionsById({ id: row.id });
            if (!success) throw new Error(message);
            ElMessage.success("操作成功");
            // this.refresh ? this.refresh() : this.handleRefreshRegionTable();
            this.tableData = [];
            this.handleRefresh();
          } catch (error) {
            error instanceof Error && ElMessage.error(error.message);
          }
        })
        .catch(() => {
          /* code */
        });
    },
    handleAddChildRegion(row) {
      if (!this.expandRow.includes(row)) {
        this.expandRow.push(row);
        this.$refs[`${this.parentId}-table`].toggleRowExpansion(row);
      }
      this.$nextTick(() => {
        setTimeout(() => {
          this.$refs[`table-${row.id}`].handleAddRegion();
        }, 0);
      });
    },
    handleAddRegion() {
      this.tableData.unshift({ parentId: this.parentId, isEdit: true, label: "", name: "", description: "", externalId: "" });
    },
    async handleRefresh() {
      const { success, data, message } = await getRegionTree({ parentId: this.parentId, sort: "createdTime,desc" });
      if (!success) throw new Error(message);
      this.tableData = this.tableData.concat(data);
    },
    expandChange(row, rows) {
      if (!rows.some((r) => r.id === row.id)) {
        this.expandRow.splice(this.expandRow.indexOf(row), 1);
      } else {
        this.expandRow.push(row);
      }
    },
    handleEditState(row, index) {
      row.isEdit = !row.isEdit;
      this.tableData.splice(index, 1);
    },
    handleEditChild(row) {
      if (!row.name) return ElMessage.error("请输入名称");
      const params = {
        label: row.label,
        name: row.name,
        description: row.description,
        externalId: row.externalId,
        parentId: row.parentId || this.parentId,
      };
      const api = { createRegionsTenantCurrent, editRegionsById };
      api[row.id ? "editRegionsById" : "createRegionsTenantCurrent"](params, row.id).then(({ success, data }) => {
        if (success) {
          ElMessage.success("操作成功");
          row.isEdit = false;
          this.tableData = [];
          this.handleRefresh();
        } else ElMessage.error(JSON.parse(data)?.message || "操作失败");
      });
    },
    getRowClass({ row }) {
      return !row.isEdit ? ["row-expand-has"] : ["row-expand-unhas"];
    },
  },
  expose: ["handleAddRegion"],
});
</script>

<style lang="scss" scoped>
:deep() .row-expand-unhas .el-table__expand-column {
  pointer-events: none;
}
:deep() .row-expand-unhas .el-table__expand-column .el-icon {
  visibility: hidden;
}
</style>
