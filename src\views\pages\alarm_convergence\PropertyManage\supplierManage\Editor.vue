<template>
  <!-- 对话框表单 -->
  <el-drawer class="el-card tw-bottom-[16px] tw-right-[16px] tw-top-[120px]" :size="width" :style="{ height: `${height}px` }" v-model="data.visible" :close-on-click-modal="false" :show-close="false" :modal="false" :before-close="handleCancel">
    <template #header>
      <div class="tw-flex tw-h-[30px] tw-flex-nowrap tw-items-center">
        <el-page-header class="tw-mr-auto" :content="`${$params.id ? t('glob.edit') : t('glob.add')}${props.title}`" @back="handleCancel()"></el-page-header>
      </div>
    </template>
    <template #default>
      <el-scrollbar v-loading="state.loading">
        <!--  -->
      </el-scrollbar>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </el-drawer>
  <!-- <SlaDownDialog ref="DownSlaConfig" :addType="type" :options="options" @confirmMsg="confirmMsg"></SlaDownDialog> -->
</template>

<!--  generic="Item extends Record<'id', string> & Record<string, unknown>" -->
<script setup lang="ts" name="EditorForm">
/* eslint-disable no-unused-vars, @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, inject, h } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { ElMessage } from "element-plus";
import { TypeHelper } from "@/utils/type";

import getUserInfo from "@/utils/getUserInfo";
import { getSafeContaineList } from "@/api/personnel";

const userInfo = getUserInfo();
/**
 * TODO: 本地方法
 */
/*  */
/**
 * TODO: 窗口方法
 */
interface DataItem {
  id: string;
}
type Item = Omit<DataItem, "createdTime" | "updatedTime" | "id" | "status"> & { id: string };

interface Props {
  title: string;
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  labelWidth: 116,
});

const { t } = useI18n();

const width = inject<import("vue").Ref<number>>("width", ref(100));
const height = inject<import("vue").Ref<number>>("height", ref(100));

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<DataItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Partial<Item>) => void;
  reject?: (value: Partial<Item>) => void;
  callback?: (form: Item & { [key: string]: unknown }) => Promise<boolean>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item>>({});

type DefaultForm<T> = { [P in keyof T]: { value: T[P]; test: (v: any) => v is T[P]; transfer: (fv: any, ov: T[P]) => T[P] } };
const defaultForm = readonly<DefaultForm<Required<Item>>>({
  id: { value: "", ...TypeHelper.string },
});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  form = cloneDeep(form);

  // try {
  //   getCustomerList();
  //   timeZone.value.unshift({ zoneId: "客户默认", displayName: "客户默认" }, { zoneId: "设备默认", displayName: "设备默认" });
  //   let map = new Map();
  //   for (let item of timeZone.value) {
  //     map.set(item.zoneId, item);
  //   }
  //   timeZone.value = [...map.values()];
  //   getSlaDetail();
  // } catch (error) {
  //   /*  */
  // }

  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], util.value as never)) });
    },
    form as Required<Item>
  );
}
async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => resolve(true));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") {
      const valid = await data.callback($form);
      if (!valid) throw new Error("Error");
    }

    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    // formRef.value && formRef.value.clearValidate();
    // await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

defineExpose({
  close: handleCancel,
  async open(params: Partial<Item>, callback?: (form: Item) => Promise<boolean>) {
    if (data.visible) {
      return await new Promise((resolve) => {
        ElMessage.warning("先关闭其他弹窗再重试！");
        resolve(params);
      });
    } else {
      $params.value = cloneDeep(params);
      data.visible = true;
      data.loading = true;
      data.submitLoading = true;
      data.callback = callback;
      try {
        return await new Promise((resolve, reject) => {
          data.resolve = resolve;
          data.reject = reject;
          nextTick(async () => {
            await nextTick();
            handleReset();
            data.loading = false;
            data.submitLoading = false;
          });
        });
      } catch (error) {
        return error;
      }
    }
  },
});
</script>

<style scoped lang="scss">
.edit_sla_config {
  :deep() {
    .el-input-number {
      width: 50px !important;

      .elstyle-input {
        width: 50px;

        input {
          padding: 0;
        }
      }
    }

    .el-input-number__increase {
      display: none;
    }

    .el-input-number__decrease {
      display: none;
    }

    .el-card {
      margin-top: 20px;
    }

    .el-form-item__content .el-form-item-content {
      display: flex;
      flex-direction: column;
    }

    .el-table .cell {
      padding: 0 !important;
    }

    .el-table .el-table__cell {
      padding: 0 !important;
      height: 50px;
    }
  }
}
.priority-icon {
  width: 10px;
  height: 10px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 10px;
}
.state {
  padding: 2px 10px;
  box-sizing: border-box;
  border-radius: 20px;
}
.modules-item {
  .modules-title {
    color: rgba(32, 128, 255, 1);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}
.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}
</style>
