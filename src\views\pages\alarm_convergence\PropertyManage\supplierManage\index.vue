<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="supplierChange()" @current-change="supplierChange()">
      <template #left>
        <el-radio-group v-model="tabPosition" @change="supplierChange">
          <el-radio-button v-if="userInfo.hasPermission(资产管理中心_设备供应商_管理) || (userInfo.hasPermission(资产管理中心_设备供应商_可读) && userInfo.hasPermission(资产管理中心_设备供应商_新增) && userInfo.hasPermission(资产管理中心_设备供应商_编辑) && userInfo.hasPermission(资产管理中心_设备供应商_删除))" label="DEVICE">{{ t("supplierManage.Equipment Vendor") }}</el-radio-button>
          <el-radio-button v-if="userInfo.hasPermission(资产管理中心_线路供应商_管理) || (userInfo.hasPermission(资产管理中心_线路供应商_可读) && userInfo.hasPermission(资产管理中心_线路供应商_新增) && userInfo.hasPermission(资产管理中心_线路供应商_编辑) && userInfo.hasPermission(资产管理中心_线路供应商_删除))" label="LINE">{{ t("supplierManage.Circuit Provider") }}</el-radio-button>
        </el-radio-group>
      </template>
      <template #right>
        <template v-if="tabPosition == 'DEVICE' && (userInfo.hasPermission(资产管理中心_设备供应商_管理) || (userInfo.hasPermission(资产管理中心_设备供应商_可读) && userInfo.hasPermission(资产管理中心_设备供应商_新增) && userInfo.hasPermission(资产管理中心_设备供应商_编辑) && userInfo.hasPermission(资产管理中心_设备供应商_删除)))">
          <el-button v-if="userInfo.hasPermission(资产管理中心_设备供应商_新增)" type="primary" :icon="Plus" @click="supplierDialog('add', {}, tabPosition)">{{ t("glob.New Data", { value: t("supplierManage.Vendor") }) }}</el-button>
        </template>
        <template v-if="tabPosition == 'LINE' && (userInfo.hasPermission(资产管理中心_线路供应商_管理) || (userInfo.hasPermission(资产管理中心_线路供应商_可读) && userInfo.hasPermission(资产管理中心_线路供应商_新增) && userInfo.hasPermission(资产管理中心_线路供应商_编辑) && userInfo.hasPermission(资产管理中心_线路供应商_删除)))">
          <el-button v-if="userInfo.hasPermission(资产管理中心_线路供应商_新增)" type="primary" :icon="Plus" @click="supplierDialog('add', {}, tabPosition)">{{ t("glob.New Data", { value: t("supplierManage.Provider") }) }}供应商</el-button>
        </template>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table v-if="tabPosition == 'DEVICE' && (userInfo.hasPermission(资产管理中心_设备供应商_管理) || (userInfo.hasPermission(资产管理中心_设备供应商_可读) && userInfo.hasPermission(资产管理中心_设备供应商_新增) && userInfo.hasPermission(资产管理中心_设备供应商_编辑) && userInfo.hasPermission(资产管理中心_设备供应商_删除)))" v-loading="loading" stripe :data="tableData.slice((paging.pageNumber - 1) * paging.pageSize, paging.pageNumber * paging.pageSize)" row-key="id" :height="tableHeight" :expand-row-keys="expandList" style="width: 100%">
          <el-table-column type="expand">
            <template #default="{ row, expanded }">
              <div v-if="!userInfo.hasPermission(资产管理中心_设备_可读)">
                <el-empty :description="t(`supplierManage.You donT have read permissions for devices to use this feature`)"></el-empty>
              </div>
              <ModelExpand
                v-else-if="row.id && expanded"
                :key="row.id"
                :id="row.id"
                type="vendor"
                :create="{
                  resource: !userInfo.hasPermission(资产管理中心_设备供应商_分配设备),
                }"
                :viewer="{}"
                :remove="{
                  resource: !userInfo.hasPermission(资产管理中心_设备供应商_分配设备),
                }"
              ></ModelExpand>
              <!-- <bindDevice :ref="'bindDeviceRef' + row.id" @delete="delRelation" :key="row.id" :id="row.id" @confirm="bindDeviceList" :title="['分配设备']" :list="[[]]"></bindDevice> -->
            </template>
          </el-table-column>
          <TableColumn type="condition" :prop="`name`" :label="t('supplierManage.Name')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="handleQuery()" :formatter="formatterTable"></TableColumn>

          <TableColumn type="condition" :prop="`description`" :label="t('supplierManage.Description')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByDescription" :filters="$filter0" @filter-change="handleQuery()" :formatter="formatterTable"></TableColumn>

          <TableColumn type="condition" :prop="`landlinePhone`" :label="t('supplierManage.Landline')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByLandlinePhone" :filters="$filter2" @filter-change="handleQuery()" :formatter="formatterTable"></TableColumn>

          <TableColumn type="condition" :prop="`supportPhone`" :label="t('supplierManage.Support Phone')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchBySupportPhone" :filters="$filter2" @filter-change="handleQuery()" :formatter="formatterTable"></TableColumn>

          <TableColumn type="condition" :prop="`contactName`" :label="t('supplierManage.Contact Person')" :width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByContactName" :filters="$filter0" @filter-change="handleQuery()" :formatter="formatterTable"></TableColumn>

          <TableColumn type="condition" :prop="`email`" :label="t('supplierManage.Email')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByEmail" :filters="$filter2" @filter-change="handleQuery()" :formatter="formatterTable"></TableColumn>

          <TableColumn type="condition" :prop="`id`" :label="`ID`" :showOverflowTooltip="true"></TableColumn>

          <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="175">
            <template #default="{ row }">
              <span>
                <el-link v-if="row.verifyPermissionIds.includes(资产管理中心_设备供应商_可读)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="supplierDialog('watch', row, tabPosition)">{{ $t("glob.Cat") }}</el-link>
              </span>
              <span>
                <el-link v-if="row.verifyPermissionIds.includes(资产管理中心_设备供应商_编辑)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="supplierDialog('edit', row, tabPosition)">{{ $t("glob.edit") }}</el-link>
              </span>
              <span>
                <el-link v-if="row.verifyPermissionIds.includes(资产管理中心_设备供应商_删除)" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="delItem(row)">{{ $t("glob.delete") }}</el-link>
              </span>
              <span>
                <!-- 设备供应商('604206410321887232') -->
                <el-link :type="row.verifyPermissionIds.includes(资产管理中心_设备供应商_安全) ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="row.verifyPermissionIds.includes(资产管理中心_设备供应商_安全) ? loading : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
              </span>
            </template>
          </el-table-column>
        </el-table>
        <el-table v-if="tabPosition == 'LINE' && userInfo.hasPermission(资产管理中心_线路供应商_可读) && userInfo.hasPermission(资产管理中心_线路供应商_新增) && userInfo.hasPermission(资产管理中心_线路供应商_编辑) && userInfo.hasPermission(资产管理中心_线路供应商_删除)" v-loading="loading" stripe :data="tableData.slice((paging.pageNumber - 1) * paging.pageSize, paging.pageNumber * paging.pageSize)" row-key="id" :height="tableHeight" :expand-row-keys="expandList" style="width: 100%">
          <!-- <el-table-column type="expand">
            <template #default="{ row, expanded }">
              <div v-if="!userInfo.hasPermission(资产管理中心_线路编号_可读)">
                <el-empty description="没有设备的可读权限无法使用此功能"></el-empty>
              </div>
              <ModelExpand
                v-else-if="row.id && expanded"
                :key="row.id"
                :id="row.id"
                type="vendor"
                :create="{
                  resource: !userInfo.hasPermission(资产管理中心_线路供应商_分配线路编号),
                }"
                :viewer="{}"
                :remove="{
                  resource: !userInfo.hasPermission(资产管理中心_线路供应商_分配线路编号),
                }"
              ></ModelExpand>
            </template>
          </el-table-column> -->
          <TableColumn type="condition" :prop="`name`" :label="t('supplierManage.Name')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="handleQuery()" :formatter="formatterTable"></TableColumn>

          <TableColumn type="condition" :prop="`description`" :label="t('supplierManage.Description')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByDescription" :filters="$filter0" @filter-change="handleQuery()" :formatter="formatterTable"></TableColumn>

          <TableColumn type="condition" :prop="`landlinePhone`" :label="t('supplierManage.Landline')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByLandlinePhone" :filters="$filter0" @filter-change="handleQuery()" :formatter="formatterTable"></TableColumn>

          <TableColumn type="condition" :prop="`supportPhone`" :label="t('supplierManage.Support Phone')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchBySupportPhone" :filters="$filter0" @filter-change="handleQuery()" :formatter="formatterTable"></TableColumn>

          <TableColumn type="condition" :prop="`contactName`" :label="t('supplierManage.Contact Person')" :width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByContactName" :filters="$filter0" @filter-change="handleQuery()" :formatter="formatterTable"></TableColumn>

          <TableColumn type="condition" :prop="`email`" :label="t('supplierManage.Email')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByEmail" :filters="$filter0" @filter-change="handleQuery()" :formatter="formatterTable"></TableColumn>

          <TableColumn type="condition" :prop="`id`" :label="`ID`" :showOverflowTooltip="true"></TableColumn>

          <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="175">
            <template #default="{ row }">
              <span>
                <el-link v-if="row.verifyPermissionIds.includes(资产管理中心_设备供应商_新增)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="supplierDialog('watch', row, tabPosition)">{{ $t("glob.Cat") }}</el-link>
              </span>
              <span>
                <el-link v-if="row.verifyPermissionIds.includes(资产管理中心_线路供应商_编辑)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="supplierDialog('edit', row, tabPosition)">{{ $t("glob.edit") }}</el-link>
              </span>
              <span>
                <el-link v-if="row.verifyPermissionIds.includes(资产管理中心_线路供应商_删除)" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="delItem(row)">{{ $t("glob.delete") }}</el-link>
              </span>
              <span>
                <!-- 线路供应商('604206449916116992') -->
                <el-link :type="row.verifyPermissionIds.includes(资产管理中心_线路供应商_安全) ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="row.verifyPermissionIds.includes(资产管理中心_线路供应商_安全) ? loading : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
  <supplierCreate :dialog="dialog" ref="supplier" @dialogClose="dialogClose"></supplierCreate>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete, Download } from "@element-plus/icons-vue";

import pageTemplate from "@/components/pageTemplate.vue";

import TableColumn from "@/components/tableColumn/TableColumn.vue";
// import Editor from "./Editor.vue";

import supplierCreate from "./supplierCreate.vue";
// import bindDevice from "@/components/bindDevice/bindDevice.vue";
import ModelExpand from "@/views/pages/modelExpand/Model.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { useTemplateRefsList } from "@vueuse/core";
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMessageBox } from "element-plus";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";

import { getSupplierList, getLineSupplierList, deleteSupplier, getSupplierDetail, getSupplierDetailDesensitized } from "@/views/pages/apis/supplier";
import { vendorsRelationDevice, getDeviceList, vendorsDelRelationDevice, type SlaConfigList as DataItem } from "@/views/pages/apis/deviceManage";
import { getSafeContaineList } from "@/api/personnel";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
import { 资产管理中心_设备_可读 } from "@/views/pages/permission";
import { 资产管理中心_设备供应商_管理, 资产管理中心_设备供应商_可读, 资产管理中心_设备供应商_新增, 资产管理中心_设备供应商_编辑, 资产管理中心_设备供应商_删除, 资产管理中心_设备供应商_分配设备, 资产管理中心_设备供应商_安全 } from "@/views/pages/permission";
import { 资产管理中心_线路供应商_管理, 资产管理中心_线路供应商_可读, 资产管理中心_线路供应商_新增, 资产管理中心_线路供应商_编辑, 资产管理中心_线路供应商_删除, 资产管理中心_线路供应商_安全 } from "@/views/pages/permission";
import { exoprtMatch1, exoprtMatch2, exoprtMatch3 } from "@/components/tableColumn/common";
const ctx = getCurrentInstance()!;
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n();
const { t } = i18n;
const route = useRoute();
const router = useRouter();
defineOptions({ name: "supplierManage" });
// const editorRef = ref<InstanceType<typeof Editor>>();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const siteConfig = useSiteConfig();
const userInfo = getUserInfo();
const tabPosition = ref("DEVICE");
const refs = useTemplateRefsList();
// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

// const assignContacts = ref<InstanceType<typeof AssignContacts>>();
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);

const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);
const $filter2 = ref(exoprtMatch3);
const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);

// 搜索关键字
const searchForm = ref<Record<string, any>>({
  eqName: [] /* 等于的供应商名称 */,
  includeName: [] /* 包含的供应商名称 */,
  nameFilterRelation: "AND" /* 供应商名称过滤关系(AND,OR) */,
  neName: [] /* 不等于的供应商名称 */,
  excludeName: [] /* 不包含的供应商名称 */,

  eqDescription: [] /* 等于的供应商描述 */,
  includeDescription: [] /* 包含的供应商描述 */,
  descriptionFilterRelation: "AND" /* 供应商描述过滤关系(AND,OR) */,
  neDescription: [] /* 不等于的供应商描述 */,
  excludeDescription: [] /* 不包含的供应商描述 */,

  includeLandlinePhone: [],
  excludeLandlinePhone: [],
  eqLandlinePhone: [] /* 等于的供应商固定电话 */,
  landlinePhoneFilterRelation: "AND" /* 供应商固定电话过滤关系(AND,OR) */,
  neLandlinePhone: [] /* 不等于的供应商固定电话 */,

  includeSupportPhone: [],
  excludeSupportPhone: [],
  eqSupportPhone: [] /* 等于的供应商支持电话 */,
  supportPhoneFilterRelation: "AND" /* 供应商支持电话过滤关系(AND,OR) */,
  neSupportPhone: [] /* 不等于的供应商支持电话 */,

  eqContactName: [] /* 等于的供应商联系人姓名 */,
  includeContactName: [] /* 包含的供应商联系人姓名 */,
  contactNameFilterRelation: "AND" /* 供应商联系人姓名过滤关系(AND,OR) */,
  neContactName: [] /* 不等于的供应商联系人姓名 */,
  excludeContactName: [] /* 不包含的供应商联系人姓名 */,

  includeEmail: [],
  excludeEmail: [],
  eqEmail: [] /* 等于的供应商电子邮箱 */,
  emailFilterRelation: "" /* 供应商电子邮箱过滤关系(AND,OR) */,
  neEmail: [] /* 不等于的供应商电子邮箱 */,
});

const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByName) === "include") value0 = searchForm.value.includeName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = searchForm.value.excludeName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = searchForm.value.eqName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = searchForm.value.neName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = searchForm.value.includeName[searchForm.value.includeName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = searchForm.value.excludeName[searchForm.value.excludeName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = searchForm.value.eqName[searchForm.value.eqName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = searchForm.value.neName[searchForm.value.neName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: searchForm.value.nameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.nameFilterRelation = v.relation;
    searchForm.value.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByDescription = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByDescription) === "include") value0 = searchForm.value.includeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "exclude") value0 = searchForm.value.excludeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "eq") value0 = searchForm.value.eqDescription[0] || "";
    if (toValue(searchType0ByDescription) === "ne") value0 = searchForm.value.neDescription[0] || "";
    let value1 = "";
    if (toValue(searchType1ByDescription) === "include") value1 = searchForm.value.includeDescription[searchForm.value.includeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "exclude") value1 = searchForm.value.excludeDescription[searchForm.value.excludeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "eq") value1 = searchForm.value.eqDescription[searchForm.value.eqDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "ne") value1 = searchForm.value.neDescription[searchForm.value.neDescription.length - 1] || "";
    return {
      type0: toValue(searchType0ByDescription),
      type1: toValue(searchType1ByDescription),
      relation: searchForm.value.descriptionFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByDescription.value = v.type0 as typeof searchType0ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchType1ByDescription.value = v.type1 as typeof searchType1ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.descriptionFilterRelation = v.relation;
    searchForm.value.includeDescription = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeDescription = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqDescription = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neDescription = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByLandlinePhone = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchType1ByLandlinePhone = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchByLandlinePhone = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    //   eqLandlinePhone: [] /* 等于的供应商固定电话 */,
    // landlinePhoneFilterRelation: "AND" /* 供应商固定电话过滤关系(AND,OR) */,
    // neLandlinePhone: [] /* 不等于的供应商固定电话 */,

    let value0 = "";
    if (toValue(searchType0ByLandlinePhone) === "include") value0 = searchForm.value.includeLandlinePhone[0] || "";
    if (toValue(searchType0ByLandlinePhone) === "exclude") value0 = searchForm.value.excludeLandlinePhone[0] || "";
    if (toValue(searchType0ByLandlinePhone) === "eq") value0 = searchForm.value.eqLandlinePhone[0] || "";
    if (toValue(searchType0ByLandlinePhone) === "ne") value0 = searchForm.value.neLandlinePhone[0] || "";
    let value1 = "";
    if (toValue(searchType1ByLandlinePhone) === "include") value1 = searchForm.value.includeLandlinePhone[searchForm.value.includeLandlinePhone.length - 1] || "";
    if (toValue(searchType1ByLandlinePhone) === "exclude") value1 = searchForm.value.excludeLandlinePhone[searchForm.value.excludeLandlinePhone.length - 1] || "";
    if (toValue(searchType1ByLandlinePhone) === "eq") value1 = searchForm.value.eqLandlinePhone[searchForm.value.eqLandlinePhone.length - 1] || "";
    if (toValue(searchType1ByLandlinePhone) === "ne") value1 = searchForm.value.neLandlinePhone[searchForm.value.neLandlinePhone.length - 1] || "";
    return {
      type0: toValue(searchType0ByLandlinePhone),
      type1: toValue(searchType1ByLandlinePhone),
      relation: searchForm.value.landlinePhoneFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByLandlinePhone.value = v.type0 as typeof searchType0ByLandlinePhone extends import("vue").Ref<infer T> ? T : string;
    searchType1ByLandlinePhone.value = v.type1 as typeof searchType1ByLandlinePhone extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.landlinePhoneFilterRelation = v.relation;
    searchForm.value.includeLandlinePhone = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeLandlinePhone = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqLandlinePhone = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neLandlinePhone = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0BySupportPhone = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchType1BySupportPhone = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchBySupportPhone = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0BySupportPhone) === "include") value0 = searchForm.value.includeSupportPhone[0] || "";
    if (toValue(searchType0BySupportPhone) === "exclude") value0 = searchForm.value.excludeSupportPhone[0] || "";
    if (toValue(searchType0BySupportPhone) === "eq") value0 = searchForm.value.eqSupportPhone[0] || "";
    if (toValue(searchType0BySupportPhone) === "ne") value0 = searchForm.value.neSupportPhone[0] || "";
    let value1 = "";
    if (toValue(searchType1BySupportPhone) === "include") value1 = searchForm.value.includeSupportPhone[searchForm.value.includeSupportPhone.length - 1] || "";
    if (toValue(searchType1BySupportPhone) === "exclude") value1 = searchForm.value.excludeSupportPhone[searchForm.value.excludeSupportPhone.length - 1] || "";
    if (toValue(searchType1BySupportPhone) === "eq") value1 = searchForm.value.eqSupportPhone[searchForm.value.eqSupportPhone.length - 1] || "";
    if (toValue(searchType1BySupportPhone) === "ne") value1 = searchForm.value.neSupportPhone[searchForm.value.neSupportPhone.length - 1] || "";
    return {
      type0: toValue(searchType0BySupportPhone),
      type1: toValue(searchType1BySupportPhone),
      relation: searchForm.value.supportPhoneFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0BySupportPhone.value = v.type0 as typeof searchType0BySupportPhone extends import("vue").Ref<infer T> ? T : string;
    searchType1BySupportPhone.value = v.type1 as typeof searchType1BySupportPhone extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.supportPhoneFilterRelation = v.relation;
    searchForm.value.includeSupportPhone = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeSupportPhone = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqSupportPhone = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neSupportPhone = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByContactName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByContactName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByContactName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByContactName) === "include") value0 = searchForm.value.includeContactName[0] || "";
    if (toValue(searchType0ByContactName) === "exclude") value0 = searchForm.value.excludeContactName[0] || "";
    if (toValue(searchType0ByContactName) === "eq") value0 = searchForm.value.eqContactName[0] || "";
    if (toValue(searchType0ByContactName) === "ne") value0 = searchForm.value.neContactName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByContactName) === "include") value1 = searchForm.value.includeContactName[searchForm.value.includeContactName.length - 1] || "";
    if (toValue(searchType1ByContactName) === "exclude") value1 = searchForm.value.excludeContactName[searchForm.value.excludeContactName.length - 1] || "";
    if (toValue(searchType1ByContactName) === "eq") value1 = searchForm.value.eqContactName[searchForm.value.eqContactName.length - 1] || "";
    if (toValue(searchType1ByContactName) === "ne") value1 = searchForm.value.neContactName[searchForm.value.neContactName.length - 1] || "";
    return {
      type0: toValue(searchType0ByContactName),
      type1: toValue(searchType1ByContactName),
      relation: searchForm.value.contactNameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByContactName.value = v.type0 as typeof searchType0ByContactName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByContactName.value = v.type1 as typeof searchType1ByContactName extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.contactNameFilterRelation = v.relation;
    searchForm.value.includeContactName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeContactName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqContactName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neContactName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByEmail = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchType1ByEmail = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchByEmail = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByEmail) === "include") value0 = searchForm.value.includeEmail[0] || "";
    if (toValue(searchType0ByEmail) === "exclude") value0 = searchForm.value.excludeEmail[0] || "";
    if (toValue(searchType0ByEmail) === "eq") value0 = searchForm.value.eqEmail[0] || "";
    if (toValue(searchType0ByEmail) === "ne") value0 = searchForm.value.neEmail[0] || "";
    let value1 = "";
    if (toValue(searchType1ByEmail) === "include") value1 = searchForm.value.includeEmail[searchForm.value.includeEmail.length - 1] || "";
    if (toValue(searchType1ByEmail) === "exclude") value1 = searchForm.value.excludeEmail[searchForm.value.excludeEmail.length - 1] || "";
    if (toValue(searchType1ByEmail) === "eq") value1 = searchForm.value.eqEmail[searchForm.value.eqEmail.length - 1] || "";
    if (toValue(searchType1ByEmail) === "ne") value1 = searchForm.value.neEmail[searchForm.value.neEmail.length - 1] || "";
    return {
      type0: toValue(searchType0ByEmail),
      type1: toValue(searchType1ByEmail),
      relation: searchForm.value.emailFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByEmail.value = v.type0 as typeof searchType0ByEmail extends import("vue").Ref<infer T> ? T : string;
    searchType1ByEmail.value = v.type1 as typeof searchType1ByEmail extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.emailFilterRelation = v.relation;
    searchForm.value.includeEmail = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeEmail = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqEmail = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neEmail = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const tableLocationData = ref([]);
const tableData = ref<DataItem[]>([]);
const paging = reactive({
  pageNumber: 1,
  pageSize: 50,
  total: 0,
});

const allRegion = ref([]);
const allRegionByPage = ref([]);
const allRegionSelect = ref([]);

const refresh = inject("refresh");

const dialog = ref(false);
const expandList = ref([]);

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  getList();
}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
import treeAuth from "@/components/treeAuth/index.vue";
const treeStyle = ref({
  pointerEvents: "none",
});
const containerId = ref("");
const dialogVisibleshow = ref(false);

function formatterTable(_row, _col, v) {
  switch (_col.property) {
    default:
      return v || "--";
  }
}
function getDetails(row) {
  getDeviceList({ pageNumber: 1, pageSize: 10000, vendorId: row.id }).then((res) => {
    if (res.success) {
      // options = [...res.data];
      let data = [];
      res.data.forEach((item) => {
        data.push({
          name: item.name,
          detail: item.config.ipAddress,
          id: item.id,
        });
      });
      ctx.refs["bindDeviceRef" + row.id].setContacts(0, data);
    } else {
      ElMessage.error(JSON.parse(res.data)?.message);
    }
  });
}
// //绑定设备关联关系
// function bindDeviceList(val) {
//   let deviceId = val.ids[0];

//   vendorsRelationDevice({
//     resourceIds: deviceId,
//     id: val.expandId,
//   }).then((res) => {
//     if (res.success) {
//       ElMessage.success("操作成功");
//       getDetails({ id: val.expandId });
//       // expandChange({
//       //   id: val.expandId,
//       // });
//     }
//   });
// }
// //取消关联设备
// function delRelation(val) {
//   let deviceId = val.ids;
//   vendorsDelRelationDevice({
//     resourceIds: deviceId,
//     id: val.expandId,
//   }).then((res) => {
//     if (res.success) {
//       ElMessage.success("操作成功");
//       getDetails({ id: val.expandId });
//       // expandChange({
//       //   id: val.expandId,
//       // });
//     }
//   });
// }

async function handleQuery() {
  paging.pageNumber = 1;
  await nextTick();
  await supplierChange();
}

async function supplierChange(val: string) {
  loading.value = true;
  tableData.value = [];
  if (tabPosition.value === "DEVICE") {
    await getList();
  } else {
    await getLineSupplierList({
      ...searchForm.value,
    }).then((res) => {
      if (res.success) {
        tableData.value = [...res.data];
        paging.total = res.data.length;
      } else {
        ElMessage.error(JSON.parse(res.data)?.message + t("axios.Operation failure"));
      }
    });
  }

  loading.value = false;
}
function getList() {
  getSupplierList({
    ...searchForm.value,
  }).then((res) => {
    if (res.success) {
      tableData.value = [...res.data];
      paging.total = res.data.length;
    } else {
      ElMessage.error(JSON.parse(res.data)?.message + t("axios.Operation failure"));
    }
  });
}
function supplierDialog(type, row, vendorType) {
  ctx.refs.supplier.type = type;
  ctx.refs.supplier.vendorType = vendorType;
  ctx.refs.supplier.safeContaineTree = [];
  getSafeContaineList({}).then((res) => {
    if (res.success) {
      ctx.refs.supplier.safeContaineTree = res.data instanceof Array ? res.data : [];
    }
  });
  if (type === "add") {
    ctx.refs.supplier.form = { name: "", description: "", landlinePhone: "", supportPhone: "", contactName: "", email: "", address: "", containerId: "" };
    ctx.refs.supplier.disabled = false;
    ctx.refs.supplier.title = vendorType == "DEVICE" ? t("glob.New Data", { value: t("supplierManage.Equipment Vendor") }) : t("glob.New Data", { value: t("supplierManage.Circuit Provider") });
  } else if (type == "watch") {
    getSupplierDetail({ id: row.id }).then((res) => {
      if (res.success) {
        ctx.refs.supplier.form = { ...res.data };
      }
    });
    ctx.refs.supplier.disabled = true;
    ctx.refs.supplier.title = vendorType == "DEVICE" ? t("glob.view Data", { value: t("supplierManage.Equipment Vendor") }) : t("glob.view Data", { value: t("supplierManage.Circuit Provider") });
  } else {
    getSupplierDetail({ id: row.id }).then((res) => {
      if (res.success) {
        ctx.refs.supplier.form = { ...res.data };
      }
    });
    ctx.refs.supplier.disabled = false;
    ctx.refs.supplier.title = vendorType == "DEVICE" ? t("glob.Edit Data", { value: t("supplierManage.Equipment Vendor") }) : t("glob.Edit Data", { value: t("supplierManage.Circuit Provider") });

    // getSupplierDetailDesensitized({ id: row.id }).then((res) => {

    // });
  }
  dialog.value = true;
}
function dialogClose(bool) {
  dialog.value = bool;
  if (tabPosition.value === "DEVICE") {
    getList();
  } else {
    getLineSupplierList({
      ...searchForm.value,
    }).then((res) => {
      if (res.success) {
        tableData.value = [...res.data];
        paging.total = res.data.length;
      } else {
        ElMessage.error(JSON.parse(res.data)?.message + t("axios.Operation failure"));
      }
    });
  }
}
function delItem(row, index) {
  ElMessageBox.confirm(`确定删除${row.name}?`, t("glob.tip"), {
    confirmButtonText: t("glob.Confirm"),
    cancelButtonText: t("glob.Cancel"),
    type: "warning",
  })
    .then(() => {
      deleteSupplier({
        id: row.id,
      })
        .then((res) => {
          // console.log(res);
          if (res.success) {
            ElMessage.success(t("axios.Operation successful"));
            if (tableData.value.slice((paging.pageNumber - 1) * paging.pageSize, paging.pageNumber * paging.pageSize).length == 0) {
              paging.pageNumber = 1;
            }
            supplierChange();
          } else {
            ElMessage.error(JSON.parse(res.data)?.message + t("axios.Operation failure"));
          }
        })
        .catch((e) => {
          if (e instanceof Error) ElMessage.error(e.message);
        });
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
}
function expandChange(row: DataItem, expand: DataItem[]) {
  expandList.value = expand.map((v) => v.id);
  if (expand.length > 0) {
    getDetails(row);
  } else {
    ctx.refs["bindDeviceRef" + row.id].setContacts(0, []);
  }
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
