<template>
  <div>
    <el-dialog :title="title" v-model="dialogFormVisible" append-to-body :before-close="handleClose" width="50vw">
      <el-scrollbar height="calc(70vh - 176px)">
        <el-form :model="form" :rules="rules" ref="ruleForm" :disabled="disabled">
          <el-form-item label="名称:" :label-width="formLabelWidth" prop="name">
            <el-input v-model="form.name" autocomplete="off" placeholder="请输入名称"></el-input>
          </el-form-item>
          <el-form-item label="描述:" :label-width="formLabelWidth" prop="description">
            <el-input type="textarea" v-model="form.description" autocomplete="off" :rows="2" placeholder="请输入描述"></el-input>
          </el-form-item>

          <el-form-item label="固定电话:" :label-width="formLabelWidth" prop="landlinePhone">
            <el-input v-model="form.landlinePhone" autocomplete="off" :rows="2" placeholder="请输入固定电话"></el-input>
          </el-form-item>
          <el-form-item label="支撑电话:" :label-width="formLabelWidth" prop="supportPhone">
            <el-input v-model="form.supportPhone" autocomplete="off" :rows="2" placeholder="请输入支持电话"></el-input>
          </el-form-item>
          <el-form-item label="联系人姓名:" :label-width="formLabelWidth" prop="contactName">
            <el-input v-model="form.contactName" autocomplete="off" :rows="2" placeholder="请输入联系人姓名"></el-input>
          </el-form-item>
          <el-form-item label="邮箱:" :label-width="formLabelWidth" prop="email">
            <el-input v-model="form.email" autocomplete="off" :rows="2" placeholder="请输入邮箱"></el-input>
          </el-form-item>
          <el-form-item label="地址:" :label-width="formLabelWidth" prop="address">
            <el-input v-model="form.address" autocomplete="off" :rows="2" placeholder="请输入地址"></el-input>
          </el-form-item>
          <el-form-item v-show="!form.id" :label="`选择安全目录`" tooltip="" prop="" :rules="[buildValidatorData({ name: 'required', title: `请选择安全目录` })]" style="margin-top: 10px">
            <treeAuth v-if="dialogFormVisible" ref="treeAuthRef" :treeStyle="treeStyle"></treeAuth>
          </el-form-item>
          <!-- <el-form-item label="选择安全目录" :label-width="formLabelWidth" prop="containerId">
        </el-form-item> -->
        </el-form>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer" v-show="!disabled">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent } from "vue";
import { ElMessage, ElMenuItem } from "element-plus";

import { addSupplier, editSupplier } from "@/views/pages/apis/supplier";
import treeAuth from "@/components/treeAuth/index.vue";
import { buildValidatorData, validatorPattern } from "@/utils/validate";

export default defineComponent({
  name: "supplierCreate",
  components: {
    treeAuth,
  },
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      form: {
        name: "",
        description: "",
        landlinePhone: "",
        supportPhone: "",
        contactName: "",
        email: "",
        address: "",
        containerId: "",
      },

      rules: {
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
      },
      title: "",
      checked: false,
      dialogFormVisible: false,
      formLabelWidth: "130px",
      type: "",
      vendorType: "",
      disabled: false,
      safeContaineTree: [],
      buildValidatorData: buildValidatorData,
      treeStyle: {
        width: "300px",
        height: "150px",
      },
    };
  },
  watch: {
    dialog(val) {
      // this.$refs["ruleForm"].clearValidate();
      this.dialogFormVisible = val;
      this.$nextTick(() => {
        // console.log(this.$refs.treeAuthRef);
        // this.$refs.treeAuthRef.getSafeContaine();
      });
    },
    type(val) {
      if (val === "add") {
        for (var key in this.form) {
          this.form[key] = null;
        }
      }
    },
  },
  created() {
    // console.log(this.$props, 5555);
  },
  methods: {
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.type === "add") {
            const treeItemId = this.form.containerId || this.$refs.treeAuthRef.treeItem.id;
            if (!treeItemId) {
              ElMessage.error("请选择安全目录");
              return;
            }
            addSupplier({
              ...this.form,
              vendorType: this.vendorType,
              containerId: this.$refs.treeAuthRef?.treeItem?.id,
            })
              .then((res) => {
                if (res.success) {
                  ElMessage.success("新增成功");
                  this.$refs[formName].resetFields();
                  this.$emit("dialogClose", false);
                } else {
                  ElMessage.error(JSON.parse(res.data)?.message + "操作失败");
                }
              })
              .catch((e) => {
                if (e instanceof Error) ElMessage.error(e.message);
              });
          } else {
            editSupplier({
              ...this.form,
              vendorType: this.vendorType,
              containerId: this.$refs.treeAuthRef?.treeItem?.id,
            })
              .then((res) => {
                if (res.success) {
                  ElMessage.success("修改成功");
                  this.$refs[formName].resetFields();
                  this.$emit("dialogClose", false);
                } else {
                  ElMessage.error(JSON.parse(res.data)?.message + "操作失败");
                }
              })
              .catch((e) => {
                if (e instanceof Error) ElMessage.error(e.message);
              });
          }
          // this.dialogFormVisible = false;
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$refs["ruleForm"].resetFields();

      this.$emit("dialogClose", false);

      // this.dialogFormVisible = false;
    },
    cancel() {
      this.$refs["ruleForm"].resetFields();

      this.$emit("dialogClose", false);
    },
    blurChange(data) {
      this.tags.push(data);
    },
    tagClose(index) {
      this.tags.splice(index, 1);
    },
  },
  expose: ["type", "disabled", "title", "form", "vendorType"],
});
</script>
