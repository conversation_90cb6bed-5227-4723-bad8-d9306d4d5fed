<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="getList()" @current-change="getList()">
      <!-- <template #left>
        <el-input v-model="ServiceSearch" placeholder="请输入告警降级配置名称" @keyup.enter="getSlaList()">
          <template #append>
            <el-button :icon="Search" @click="getSlaList()" />
          </template>
        </el-input>
      </template> -->
      <template #right>
        <span class="">
          <el-button v-if="userInfo.hasPermission(资产管理中心_行动策略_新增)" type="primary" :icon="Plus" @click="deviceDialog('add')">{{ $t("supportNote.New Strategy") }}</el-button>
        </span>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table v-loading="loading" stripe :data="tableData.slice((paging.pageNumber - 1) * paging.pageSize, paging.pageNumber * paging.pageSize)" row-key="id" :height="tableHeight" :expand-row-keys="expandList" style="width: 100%" @expand-change="expandChange">
          <el-table-column type="expand">
            <template #default="{ row, expanded }">
              <strategy v-if="expanded" :detail="row" :width="width - 40" @confirm="update" @child-event="getList" @refresh="getList">
                <ModelExpand
                  v-if="row.id && expanded"
                  :key="row.id"
                  :id="row.id"
                  type="strategy"
                  :create="{
                    resource: !userInfo.hasPermission(资产管理中心_行动策略_分配设备),
                    location: !userInfo.hasPermission(资产管理中心_行动策略_分配场所),
                    region: !userInfo.hasPermission(资产管理中心_行动策略_分配区域),
                  }"
                  :viewer="{
                    resource: false,
                    location: false,
                    region: false,
                  }"
                  :remove="{
                    resource: !userInfo.hasPermission(资产管理中心_行动策略_分配设备),
                    location: !userInfo.hasPermission(资产管理中心_行动策略_分配场所),
                    region: !userInfo.hasPermission(资产管理中心_行动策略_分配区域),
                  }"
                ></ModelExpand>
              </strategy>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="name" :formatter="formatterTable" label="行动策略名称"></el-table-column> -->
          <TableColumn type="condition" :prop="`name`" :label="$t('supportNote.Strategy name')" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="getList()" :formatter="formatterTable"> </TableColumn>
          <!-- <el-table-column prop="active" label="是否激活">
            <template #default="scope">
              <el-switch v-model="scope.row.active" :disabled="!userInfo.hasPermission(资产管理中心_行动策略_编辑)" active-color="#13ce66" @change="updateActive(scope.row)"></el-switch>
            </template>
          </el-table-column> -->
          <TableColumn type="enum" :prop="`activeValue`" :label="$t('supportNote.Is Active')" :showOverflowTooltip="true" show-filter v-model:filtered-value="searchForm.active" :filters="['true', 'false'].map((v) => ({ value: v, text: v === 'true' ? '是' : '否' }))" @filter-change="getList()">
            <template #default="scope">
              <el-switch v-model="scope.row.active" :disabled="!userInfo.hasPermission(资产管理中心_行动策略_编辑)" active-color="#13ce66" @change="updateActive(scope.row)"></el-switch>
            </template>
          </TableColumn>
          <!-- <el-table-column prop="description" :formatter="formatterTable" label="描述"></el-table-column> -->
          <TableColumn type="condition" :prop="`description`" :label="$t('supportNote.Description')" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByDescription" :filters="$filter0" @filter-change="getList()" :formatter="formatterTable"> </TableColumn>
          <TableColumn type="condition" :prop="`id`" :label="`ID`" :showOverflowTooltip="true"></TableColumn>
          <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="120">
            <template #default="{ row }">
              <span v-if="!row.global">
                <el-link v-if="row.verifyPermissionIds.includes(资产管理中心_行动策略_编辑)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="deviceDialog('edit', row)">{{ $t("glob.edit") }}</el-link>
              </span>
              <span v-if="!row.global">
                <el-link v-if="row.verifyPermissionIds.includes(资产管理中心_行动策略_删除)" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="delItem(row)">{{ $t("glob.delete") }}</el-link>
              </span>
              <span>
                <!-- 行动策略('603900114561400832') -->
                <el-link :type="row.verifyPermissionIds.includes(资产管理中心_行动策略_安全) ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="row.verifyPermissionIds.includes(资产管理中心_行动策略_安全) ? loading : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
  <Editor ref="editorRef" title="行动策略"></Editor>
  <supportNotesCreate :dialog="dialog" ref="supplier" @dialogClose="dialogClose"></supportNotesCreate>
  <el-dialog v-model="dialogVisibleshow" title="查看安全目录" width="500" :before-close="handleClose">
    <treeAuth :proptreeId="containerId" :treeStyle="treeStyle" ref="treeAuthRef"></treeAuth>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisibleshow = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
import treeAuth from "@/components/treeAuth/index.vue";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete, Download } from "@element-plus/icons-vue";

import Editor from "./Editor.vue";

import assignContacts from "@/components/bindContacts/assignContacts";
import supportNotesCreate from "./supportNotesCreate.vue";
import ModelExpand from "@/views/pages/modelExpand/Model.vue";
import strategy from "./strategy.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { useTemplateRefsList } from "@vueuse/core";
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMessageBox } from "element-plus";
import pageTemplate from "@/components/pageTemplate.vue";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";
import { getSupport_notesList, getnewSupport_notesList, deleteSupport_notes, editSupport_notes, Support_notesSetHours, updateSupport_notesActive, type SlaConfigList as DataItem, getAvailableTreeLine } from "@/views/pages/apis/supportNotes";
import { getAlarmClassificationList } from "@/views/pages/apis/alarmClassification";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
import { 资产管理中心_行动策略_可读, 资产管理中心_行动策略_分配客户, 资产管理中心_行动策略_新增, 资产管理中心_行动策略_编辑, 资产管理中心_行动策略_删除, 资产管理中心_行动策略_分配设备, 资产管理中心_行动策略_分配场所, 资产管理中心_行动策略_分配区域, 资产管理中心_行动策略_安全, 安全管理中心_权限管理_安全 } from "@/views/pages/permission";
import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";
const ctx = getCurrentInstance()!;
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "deviceGroupManage" });
const editorRef = ref<InstanceType<typeof Editor>>();
const assignContactsRef = ref<InstanceType<typeof assignContacts>>();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const siteConfig = useSiteConfig();
const userInfo = getUserInfo();
const userInfoS = getUserInfo();
const treeStyle = ref({
  pointerEvents: "none",
});

const refs = useTemplateRefsList();

// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

// const assignContacts = ref<InstanceType<typeof AssignContacts>>();
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);

const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);

const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);

// 搜索关键字
const searchForm = ref<Record<string, any>>({
  eqName: [] /* 等于的行动策略名称 */,
  includeName: [] /* 包含的行动策略名称 */,
  nameFilterRelation: "AND" /* 行动策略名称过滤关系(AND,OR) */,
  neName: [] /* 不等于的行动策略名称 */,
  excludeName: [] /* 不包含的行动策略名称 */,

  eqDescription: [] /* 等于的行动策略描述 */,
  includeDescription: [] /* 包含的行动策略描述 */,
  descriptionFilterRelation: "AND" /* 行动策略描述过滤关系(AND,OR) */,
  neDescription: [] /* 不等于的行动策略描述 */,
  excludeDescription: [] /* 不包含的行动策略描述 */,
});
const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByName) === "include") value0 = searchForm.value.includeName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = searchForm.value.excludeName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = searchForm.value.eqName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = searchForm.value.neName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = searchForm.value.includeName[searchForm.value.includeName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = searchForm.value.excludeName[searchForm.value.excludeName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = searchForm.value.eqName[searchForm.value.eqName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = searchForm.value.neName[searchForm.value.neName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: searchForm.value.nameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.nameFilterRelation = v.relation;
    searchForm.value.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByDescription = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByDescription = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByDescription) === "include") value0 = searchForm.value.includeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "exclude") value0 = searchForm.value.excludeDescription[0] || "";
    if (toValue(searchType0ByDescription) === "eq") value0 = searchForm.value.eqDescription[0] || "";
    if (toValue(searchType0ByDescription) === "ne") value0 = searchForm.value.neDescription[0] || "";
    let value1 = "";
    if (toValue(searchType1ByDescription) === "include") value1 = searchForm.value.includeDescription[searchForm.value.includeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "exclude") value1 = searchForm.value.excludeDescription[searchForm.value.excludeDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "eq") value1 = searchForm.value.eqDescription[searchForm.value.eqDescription.length - 1] || "";
    if (toValue(searchType1ByDescription) === "ne") value1 = searchForm.value.neDescription[searchForm.value.neDescription.length - 1] || "";
    return {
      type0: toValue(searchType0ByDescription),
      type1: toValue(searchType1ByDescription),
      relation: searchForm.value.descriptionFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByDescription.value = v.type0 as typeof searchType0ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchType1ByDescription.value = v.type1 as typeof searchType1ByDescription extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.descriptionFilterRelation = v.relation;
    searchForm.value.includeDescription = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeDescription = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqDescription = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neDescription = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const tableData = ref<DataItem[]>([]);
const expandList = ref<string[]>([]);
const paging = reactive({
  pageNumber: 1,
  pageSize: 50,
  total: 0,
});

const allRegion = ref([]);
const allRegionByPage = ref([]);
const allRegionSelect = ref([]);

const deviceList = ref([]);
const deviceGroupList = ref([]);
const deviceTypeList = ref([]);
const dialog = ref(false);
const options = ref({});
const containerId = ref("");
const dialogVisibleshow = ref(false);
const treeAuthRef = ref<InstanceType<typeof treeAuth>>();
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  getList();
}
function beforeMount() {}
function mounted() {
  loading.value = true;
  getList();
  getAlarmClassificationList({})
    .then(({ success, message, data }) => {
      if (!success) throw new Error(message);
      options.value = data.reduce((p, c) => ({ ...p, [c.id]: c.name }), {});
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {
      loading.value = false;
    });
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

function formatterTable(_row, _col, v) {
  switch (_col.property) {
    default:
      return v || "--";
  }
}
function expandChange(row, expandedRows) {
  if (expandedRows.length) {
    //展开
    expandList.value = []; //先干掉之前展开的行
    if (row) {
      expandList.value.push(row.id); //push新的行 (原理有点类似防抖)
    }
  } else {
    expandList.value = []; //折叠 就清空expand-row-keys对应的数组
  }
  row.locations = [];
  row.regions = [];
  row.resources = [];
}
function updateActive(val) {
  // console.log(val, 444);
  updateSupport_notesActive({
    id: val.id,
    value: val.active,
  }).then((res) => {
    if (res.success) {
      ElMessage.success("操作成功");
      getList();
    } else {
      ElMessage.error(JSON.parse(res.data)?.message);
    }
  });
}

//修改表格展开行数据
function update(val) {
  // Support_notesSetHours({
  //   id: val.id,
  //   autoTimeZone: val.activeConfig.useAutoTimeZone ? getUserInfo()?.zoneId : "",
  // });
  let obj = { ...val };
  // if (val.activeConfig.timeZone == "自动时区") {
  //   obj.activeConfig.timeZone = getUserInfo()?.zoneId;
  // }
  editSupport_notes({ ...obj })
    .then((res) => {
      if (res.success) {
        ElMessage.success("操作成功");
        getList();
      } else {
        ElMessage.error(JSON.parse(res.data)?.message);
      }
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
}
//获取列表
async function getList() {
  try {
    const { data, message, success } = await getAvailableTreeLine({ permissionId: 安全管理中心_权限管理_安全, containerId: userInfoS.currentTenant.containerId });
    if (!success) throw new Error(message);
    getnewSupport_notesList({ containerId: userInfoS.currentTenant.containerId, queryPermissionId: "513151297004765184", verifyPermissionIds: "513151366449856512,513151384703467520,612901276052619264", containerList: (data || []).join(","), ...searchForm.value }).then((res) => {
      if (res.success) {
        tableData.value = [...res.data.map((v) => Object.assign({ ...v }, { activeValue: String(v.active) }))];
        paging.total = res.data.length;
      } else {
        ElMessage.error(JSON.parse(res.data)?.message);
      }
    });
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}
function deviceDialog(type, row) {
  ctx.refs.supplier.type = type;
  if (type === "add") {
    ctx.refs.supplier.title = "新增行动策略";
  } else {
    ctx.refs.supplier.form = {
      name: row.name,
      description: row.description,
      inactiveNote: row.inactiveNote,
      activeNote: row.activeNote,
      id: row.id,
    };
    ctx.refs.supplier.title = "编辑行动策略";
  }
  dialog.value = true;
}
function handleSizeChange(v) {
  paging.pageSize = v;
  paging.pageNumber = 1;
}
function handleCurrentPageChange(v) {
  paging.pageNumber = v;
}
function dialogClose(bool) {
  dialog.value = bool;
  getList();
}
function delItem(row, index) {
  ElMessageBox.confirm(`确定删除${row.name}?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // // tableData.value.length = tableData.value.length - 1;
      deleteSupport_notes({
        id: row.id,
      })
        .then((res) => {
          if (res.success) {
            ElMessage.success("删除成功");
            if (tableData.value.slice((paging.pageNumber - 1) * paging.pageSize, paging.pageNumber * paging.pageSize).length == 0) {
              paging.pageNumber = 1;
            }
            getList();
          } else {
            ElMessage.error(JSON.parse(res.data)?.message);
          }
        })
        .catch((e) => {
          // console.log(e);
          if (e instanceof Error) ElMessage.error(e.message);
        });
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
