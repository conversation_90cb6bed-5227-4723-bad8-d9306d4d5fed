<template>
  <el-form :model="form" :style="{ marginTop: '10px', padding: '0 10px' }" label-position="left" label-width="160px" :rules="rules" ref="roleFormRef" label-suffix=":">
    <el-row :gutter="20">
      <!-- <el-col :span="12">
        <el-form-item label="行动策略名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入行动策略名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="行动策略描述">
          <el-input type="textarea" v-model="form.description" placeholder="请输入行动策略描述" />
        </el-form-item>
      </el-col> -->
      <el-col :span="12">
        <el-form-item label="工作时间">
          <!--  -->
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="选择时区">
          <el-select v-model="form.activeConfig.timeZone" filterable placeholder="请选择" class="tw-w-full">
            <el-option v-for="item in timeZone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"> </el-option>
          </el-select>
        </el-form-item>
      </el-col>

      <div style="width: 100%" class="support-table-content" ref="tableContentRef">
        <el-table stripe border :data="form.activeConfig.activeHours" style="width: 100%" @header-click="(column: Record<string, any>, $event: Event) => handleClick({ column, evene: $event })">
          <el-table-column align="left" width="80" prop="week" fixed="left">
            <template #default="scope">
              <div @click="handleSelectTime('all', scope.$index, scope.row)">
                {{ scope.row.weekDay == 1 ? "周一" : scope.row.weekDay == 2 ? "周二" : scope.row.weekDay == 3 ? "周三" : scope.row.weekDay == 4 ? "周四" : scope.row.weekDay == 5 ? "周五" : scope.row.weekDay == 6 ? "周六" : "周日" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column :width="tableWidth" align="left" v-for="(item, key) in 24" :key="`h-${key}`" :label="String(key)" class-name="tw-py-0">
            <template #header="{ column }">
              <div class="tw-my-[12px]">{{ column.label }}</div>
            </template>
            <template #default="scope">
              <div class="tw-mx-[-12px] tw-flex tw-h-[50px] tw-items-center tw-justify-center tw-text-[20px]" @click="handleSelectTime(key, scope.$index, scope.row)" style="height: 100%" :class="scope.row.hours && scope.row.hours.length && scope.row.hours.includes(key) ? 'sun' : 'moon'">
                <el-icon v-if="scope.row.hours && scope.row.hours.length && scope.row.hours.includes(key)" class="tw-text-white"><Sunny></Sunny></el-icon>
                <el-icon v-else class="tw-text-black"><Moon></Moon></el-icon>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-col :span="12" class="tw-pb-[18px]">
        <el-form-item label="工作时间行动策略">
          <el-icon><Sunny></Sunny></el-icon>
        </el-form-item>
        <div class="tw-flex tw-min-h-[250px] tw-flex-col">
          <QuillEditor theme="snow" class="tw-h-[250px] tw-w-full" style="flex: 1" v-model:content="form.activeNote" contentType="html" :modules="modules" toolbar="full"></QuillEditor>
        </div>
      </el-col>
      <el-col :span="12" class="tw-pb-[18px]">
        <el-form-item label="非工作时间行动策略">
          <el-icon><Moon></Moon></el-icon>
        </el-form-item>
        <div class="second tw-flex tw-min-h-[250px] tw-flex-col">
          <QuillEditor theme="snow" class="tw-h-[250px] tw-w-full" style="flex: 1" v-model:content="form.inactiveNote" contentType="html" :modules="modules" toolbar="full"></QuillEditor>
        </div>
        <!-- <Editor theme="snow" ref="inActiveNote" @getValue="getEditorInactiveNote" qlEditorHeight="250"></Editor> -->
      </el-col>
      <el-col :span="24" class="tw-pb-[18px]" style="text-align: center">
        <el-button type="primary" @click="CreateSlaDownConfig" v-if="form.global" :disabled="form.global"> 保 存</el-button>

        <el-button type="primary" @click="CreateSlaDownConfig" v-else :disabled="!userInfo.hasPermission(资产管理中心_行动策略_编辑)"> 保 存</el-button>
      </el-col>
      <el-col :span="24">
        <el-row style="width: 100%">
          <el-col :span="24" style="text-align: right">
            <span v-if="userInfo.hasPermission(资产管理中心_行动策略_分配客户) && userInfo.hasPermission(资产管理中心_客户_分配行动策略)" class="tw-ml-auto tw-h-fit">
              <el-button type="primary" :icon="Plus" @click="handleAssignCustomer">分配客户</el-button>
            </span>
          </el-col>
          <el-col>
            <el-table stripe :data="customsList" style="width: 100%; margin-top: 30px">
              <el-table-column align="left" label="名称" prop="name">
                <template #default="{ row, column, $index }">
                  <span>{{ row.name }} [{{ row.abbreviation }}]</span>
                </template>
              </el-table-column>
              <el-table-column align="left" label="描述" prop="description"> </el-table-column>
              <el-table-column align="left" label="是否激活" prop="activated">
                <template #default="scope">
                  <el-text type="primary" v-if="scope.row.active">是</el-text>
                  <el-text type="danger" v-else>否</el-text>
                </template>
              </el-table-column>

              <el-table-column align="left" label="操作">
                <template #default="scope">
                  <el-popconfirm :title="delTitle" @confirm="delCostom('area', scope.$index, scope.row)">
                    <template #reference>
                      <el-button type="text" textColor="danger" @click="delCostomLevel('area', scope.$index, scope.row)">取消分配</el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col :span="24" style="text-align: right; margin-top: 30px">
            <span v-if="userInfo.hasPermission(资产管理中心_行动策略_分配区域)" class="tw-ml-auto tw-h-fit">
              <el-button type="primary" :icon="Plus" @click="addModule('area')">分配区域</el-button>
            </span>
          </el-col>
          <el-col>
            <el-table stripe :data="areaList" style="width: 100%; margin-top: 30px">
              <el-table-column align="left" label="名称" prop="name"> </el-table-column>
              <el-table-column align="left" label="描述" prop="description"> </el-table-column>
              <el-table-column align="left" label="是否激活" prop="active">
                <template #default="scope">
                  <el-text type="primary" v-if="scope.row.active">是</el-text>
                  <el-text type="danger" v-else>否</el-text>
                </template>
              </el-table-column>

              <el-table-column align="left" label="操作">
                <template #default="scope">
                  <!-- <el-button type="text" v-show="!scope.row.state" @click="confirmLevel('response', scope.$index, scope.row)">保存</el-button> -->

                  <el-popconfirm :title="delTitle" @confirm="delConfirm('area', scope.$index, scope.row)">
                    <template #reference>
                      <el-button type="text" textColor="danger" @click="delLevel('area', scope.$index, scope.row)">取消分配</el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col :span="24" style="margin-top: 20px; text-align: right">
            <span v-if="userInfo.hasPermission(资产管理中心_行动策略_分配场所)" class="tw-ml-auto tw-h-fit">
              <el-button type="primary" :icon="Plus" @click="addModule('location')">分配场所</el-button>
            </span>
          </el-col>
          <el-col>
            <el-table stripe :data="locationList" style="width: 100%; margin-top: 30px">
              <el-table-column align="left" label="名称" prop="name"> </el-table-column>
              <el-table-column align="left" label="描述" prop="description"> </el-table-column>
              <el-table-column align="left" label="是否激活" prop="active">
                <template #default="scope">
                  <el-text type="primary" v-if="scope.row.active">是</el-text>
                  <el-text type="danger" v-else>否</el-text>
                </template>
              </el-table-column>
              <el-table-column align="left" label="操作">
                <template #default="scope">
                  <!-- <el-button type="text" v-show="!scope.row.state" @click="confirmLevel('response', scope.$index, scope.row)">保存</el-button> -->

                  <el-popconfirm :title="delTitle" @confirm="delConfirm('location', scope.$index, scope.row)">
                    <template #reference>
                      <el-button type="text" textColor="danger" @click="delLevel('location', scope.$index, scope.row)">取消分配</el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col :span="24" style="margin-top: 20px; text-align: right">
            <span v-if="userInfo.hasPermission(资产管理中心_行动策略_分配设备)" class="tw-ml-auto tw-h-fit">
              <el-button type="primary" :icon="Plus" @click="addModule('device')">分配设备</el-button>
            </span>
          </el-col>
          <el-col>
            <el-table stripe :data="devicesList" style="width: 100%; margin-top: 30px">
              <el-table-column align="left" label="名称" prop="name">
                <template #default="{ row }">
                  <div>
                    <div style="color: #409eff; cursor: pointer" @click="openDevice(row)">
                      {{ row.name }}
                    </div>
                    <div style="font-size: 12px">
                      {{ row.detail }}
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column align="left" label="描述" prop="description"> </el-table-column>
              <el-table-column align="left" label="是否激活" prop="active">
                <template #default="{ row }">
                  <el-text type="primary" v-if="row.active">是</el-text>
                  <el-text type="danger" v-else>否</el-text>
                </template>
              </el-table-column>
              <el-table-column align="left" label="操作">
                <template #default="scope">
                  <!-- <el-button type="text" v-show="!scope.row.state" @click="confirmLevel('response', scope.$index, scope.row)">保存</el-button> -->

                  <el-popconfirm :title="delTitle" @confirm="delConfirm('device', scope.$index, scope.row)">
                    <template #reference>
                      <el-button type="text" textColor="danger" @click="delLevel('device', scope.$index, scope.row)">取消分配</el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
            <div class="tw-flex tw-justify-end tw-py-4">
              <el-pagination class="el-pagination--small" v-model:current-page="devicesListPage.pageNumber" v-model:page-size="devicesListPage.pageSize" :page-sizes="sizes" layout="total, sizes, prev, pager, next, jumper" :total="devicesListPage.total" @size-change="getDetail({ id: props.detail.id }, form)" @current-change="getDetail({ id: props.detail.id }, form)" />
            </div>
          </el-col>
        </el-row>
        <!-- <bindDevice ref="bindDeviceRef" @delete="delRelation" @confirm="bindRelation" :id="form.id" :list="[areaList, locationList, devicesList]" :title="['分配区域', '分配场所', '分配设备']"></bindDevice> -->
      </el-col>
      <alarmDownDialog ref="DownSlaConfig" :addType="dialogType" :options="options" :allOptions="allOptions" @confirmMsg="confirmMsg"></alarmDownDialog>
      <customDownDialog ref="customConfig" :options="options" :allOptions="allOptions" @customMsg="customMsg"></customDownDialog>
      <!-- <el-form-item>
        <bindDevice :id="'1'" :list="[]" :title="'分配场所'"></bindDevice>
      </el-form-item>
      <el-form-item>
        <bindDevice :id="'2'" :list="[]" :title="'分配设备'"></bindDevice>
      </el-form-item> -->
      <!-- <el-form-item style="width: 100%"> -->

      <!-- </el-form-item> -->
    </el-row>
  </el-form>
</template>

<script lang="ts" setup generic="T extends { [key: string]: unknown; id: string }">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";
import { ElMessage, ElMenuItem, ElForm, ElMessageBox } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { QuillEditor } from "@vueup/vue-quill";
import BlotFormatter from "quill-blot-formatter";
import "@vueup/vue-quill/dist/vue-quill.snow.css";
// 注册
import { Sunny, Moon } from "@element-plus/icons-vue";
import { Support_notesRelationArea, Support_notesRelationLocation, Support_notesRelationDevice, Support_notesDelRelationDevice, Support_notesDelRelationArea, Support_notesDelRelationLocation } from "@/views/pages/apis/supportNotes";
// import Editor from "@/components/Editor/indexEditor.vue";
import _timeZone from "@/views/pages/common/strategyZone.json";
import bindDevice from "@/components/bindDevice/bindDevice.vue";
import { getDeviceList } from "@/views/pages/apis/device";
import { getRegionsTenantCurrent } from "@/views/pages/apis/regionManage";
import { getLocationsTenantCurrent } from "@/views/pages/apis/locationManang";

import getUserInfo from "@/utils/getUserInfo";
import alarmDownDialog from "@/views/pages/alarm_convergence/ConfigCenter/SlaDownConfig/alarmDownDialog.vue";
import customDownDialog from "@/views/pages/alarm_convergence/ConfigCenter/SlaDownConfig/customDownDialog.vue";
import { querySupportAssignedList, queryLocationList, queryResourceList, queryRegionsList } from "@/views/pages/apis/index";
import { useResizeObserver } from "@vueuse/core";
import { Support_notesGlobalRelationTenant, Support_notesDelGlobalRelationTenant, getSupport_noteslGlobalTenant, getSupportNoteDetailById } from "@/views/pages/apis/supportNotes";
import { getReportList, addTenantReport as addData, editReport as setData, type Reports as DataItem, reportStateOption, reportModelOption, enableReport, delTenantReport, getTenantReportList as getData } from "@/views/pages/apis/reports";
import { getSupport_notesList, deleteSupport_notes, editSupport_notes, Support_notesSetHours } from "@/views/pages/apis/supportNotes";

import { 资产管理中心_行动策略_可读, 资产管理中心_行动策略_分配客户, 资产管理中心_行动策略_新增, 资产管理中心_行动策略_编辑, 资产管理中心_行动策略_删除, 资产管理中心_行动策略_分配设备, 资产管理中心_行动策略_分配场所, 资产管理中心_行动策略_分配区域, 资产管理中心_行动策略_安全, 资产管理中心_设备_可读, 资产管理中心_场所_可读, 资产管理中心_场所_分配行动策略, 资产管理中心_设备_分配行动策略, 资产管理中心_区域_可读, 资产管理中心_区域_分配行动策略, 资产管理中心_客户_分配行动策略 } from "@/views/pages/permission";

import { sizes } from "@/utils/common";

// import ModelExpand from "@/views/pages/modelExpand/Model.vue";
const userInfo = getUserInfo();
const route = useRoute();
const router = useRouter();
const modules = {
  name: "blotFormatter",
  module: BlotFormatter,
};
interface Emits {
  ($event: "confirm", data: Record<string, any>): any;
  ($event: "child-event", data: Record<string, any>): any;
  ($event: "refresh", data: unknown): any;
}
const emits = defineEmits<Emits>();

interface Props {
  detail: T;
  width: number;
}
const props = withDefaults(defineProps<Props>(), {
  width: 0,
  detail: () => ({}) as T,
});

const roleFormRef = ref<InstanceType<typeof ElForm>>();
const DownSlaConfig = ref<InstanceType<typeof alarmDownDialog>>();
const customConfig = ref<InstanceType<typeof customDownDialog>>();
// const bindDeviceRef = ref<InstanceType<typeof bindDevice>>();

const id = ref("");
const rules = reactive({
  name: [{ required: true, message: "请输入行动策略名称", trigger: "blur" }],
});
const timeZone = ref(_timeZone);
const basicClassInput = reactive({
  width: "25.8vw",
}); /* 输入框选择器基本样式 */
const basicClassInputDown = reactive({
  width: "20.8vw",
}); /* 输入框选择器基本样式 */
const type = ref("");
//工作时间降级
const areaList = ref<Record<"id" | "name" | "detail", string>[]>([]); //区域表格数组
const locationList = ref<Record<"id" | "name" | "detail", string>[]>([]); //场所表格数据
const devicesList = ref<Record<"id" | "name" | "detail", string>[]>([]); //设备列表
const customsList = ref<Record<"id" | "name" | "detail", string>[]>([]); //客户列表
const form = reactive<Record<string, any>>({ ...props.detail });
const ctx = getCurrentInstance()!;

const devicesListPage = ref<{ pageNumber: number; pageSize: number; total: number }>({
  pageNumber: 1,
  pageSize: 20,
  total: 0,
});

const allBool = ref([false, false, false, false, false, false, false]);
const tableContentRef = ref();
const tableWidth = ref(0);
useResizeObserver(tableContentRef, (entries) => {
  const entry = entries[0];
  const { width, height } = entry.contentRect;
  tableWidth.value = ((width - 80) / 24).toFixed(0);
});

const titleConfig = [
  { Choice: ".ql-insertMetric", title: "跳转配置" },
  { Choice: ".ql-bold", title: "加粗" },
  { Choice: ".ql-italic", title: "斜体" },
  { Choice: ".ql-underline", title: "下划线" },
  { Choice: ".ql-header", title: "段落格式" },
  { Choice: ".ql-strike", title: "删除线" },
  { Choice: ".ql-blockquote", title: "块引用" },
  { Choice: ".ql-code", title: "插入代码" },
  { Choice: ".ql-code-block", title: "插入代码段" },
  { Choice: ".ql-font", title: "字体" },
  { Choice: ".ql-size", title: "字体大小" },
  { Choice: '.ql-list[value="ordered"]', title: "编号列表" },
  { Choice: '.ql-list[value="bullet"]', title: "项目列表" },
  { Choice: ".ql-direction", title: "文本方向" },
  { Choice: '.ql-header[value="1"]', title: "h1" },
  { Choice: '.ql-header[value="2"]', title: "h2" },
  { Choice: ".ql-align", title: "对齐方式" },
  { Choice: ".ql-color", title: "字体颜色" },
  { Choice: ".ql-background", title: "背景颜色" },
  { Choice: ".ql-image", title: "图像" },
  { Choice: ".ql-video", title: "视频" },
  { Choice: ".ql-link", title: "添加链接" },
  { Choice: ".ql-formula", title: "插入公式" },
  { Choice: ".ql-clean", title: "清除字体格式" },
  { Choice: '.ql-script[value="sub"]', title: "下标" },
  { Choice: '.ql-script[value="super"]', title: "上标" },
  { Choice: '.ql-indent[value="-1"]', title: "向左缩进" },
  { Choice: '.ql-indent[value="+1"]', title: "向右缩进" },
  { Choice: ".ql-header .ql-picker-label", title: "标题大小" },
  { Choice: '.ql-header .ql-picker-item[data-value="1"]', title: "标题一" },
  { Choice: '.ql-header .ql-picker-item[data-value="2"]', title: "标题二" },
  { Choice: '.ql-header .ql-picker-item[data-value="3"]', title: "标题三" },
  { Choice: '.ql-header .ql-picker-item[data-value="4"]', title: "标题四" },
  { Choice: '.ql-header .ql-picker-item[data-value="5"]', title: "标题五" },
  { Choice: '.ql-header .ql-picker-item[data-value="6"]', title: "标题六" },
  { Choice: ".ql-header .ql-picker-item:last-child", title: "标准" },
  { Choice: '.ql-size .ql-picker-item[data-value="small"]', title: "小号" },
  { Choice: '.ql-size .ql-picker-item[data-value="large"]', title: "大号" },
  { Choice: '.ql-size .ql-picker-item[data-value="huge"]', title: "超大号" },
  { Choice: ".ql-size .ql-picker-item:nth-child(2)", title: "标准" },
  { Choice: ".ql-align .ql-picker-item:first-child", title: "居左对齐" },
  {
    Choice: '.ql-align .ql-picker-item[data-value="center"]',
    title: "居中对齐",
  },
  {
    Choice: '.ql-align .ql-picker-item[data-value="right"]',
    title: "居右对齐",
  },
  {
    Choice: '.ql-align .ql-picker-item[data-value="justify"]',
    title: "两端对齐",
  },
];

watch(
  () => props.detail,
  async () => {
    await nextTick();
    timeZone.value.unshift({
      zoneId: "自动时区",
      displayName: "自动时区",
    });

    let map = new Map();
    for (let item of timeZone.value) {
      map.set(item.zoneId, item);
    }
    timeZone.value = [...map.values()];
    // bindDeviceRef.value.contacts = [[], [], []];
    // bindDeviceRef.value.contacts = [[], [], []];
    getDetail({ id: props.detail.id }, props);
    getCostom(props);

    // if (props.detail.activeConfig.timeZone == getUserInfo()?.zoneId) {
    //   form.activeConfig.timeZone = "自动时区";
    // }
    form.activeConfig.activeHours.forEach((item: any) => {
      // // console.log(item);
      if (item.hours.length > 23) {
        allBool.value[item.weekDay - 1] = true;
      } else {
        allBool.value[item.weekDay - 1] = false;
      }
    });
    document.getElementsByClassName("ql-editor")[0].dataset.placeholder = "";
    for (let item of titleConfig) {
      let tip = document.querySelector(".ql-toolbar " + item.Choice);
      let tip1 = document.querySelector(".second " + ".ql-toolbar " + item.Choice);

      if (!tip) continue;
      tip.setAttribute("title", item.title);
      if (item.Choice == ".ql-image" || item.Choice == ".ql-video" || item.Choice == ".ql-link") {
        tip.style.display = "none"; // 或者使用 visibility: "hidden"
      }
      tip1.setAttribute("title", item.title);
    }
  },
  { immediate: true, deep: true }
);

const options = ref([]);
const dialogType = ref("");
const allOptions = ref([]);
//新增区域/场所/设备
function addModule(type) {
  options.value = [];
  allOptions.value = [];
  dialogType.value = type;

  const params = {
    pageNumber: 1,
    pageSize: 9999,
    active: true,
  };

  const queryPermissionIds = {
    location: 资产管理中心_场所_分配行动策略,
    area: 资产管理中心_区域_分配行动策略,
    default: 资产管理中心_设备_分配行动策略,
  };

  params.queryPermissionId = queryPermissionIds[type] || queryPermissionIds["default"];

  // const permissionMapping = {
  //   location: 资产管理中心_场所_可读,
  //   area: 资产管理中心_区域_可读,
  //   default: 资产管理中心_设备_可读,
  // };

  const queryMapping = {
    location: queryLocationList,
    area: queryRegionsList,
    default: queryResourceList,
  };

  const assignedIdsMapping = {
    location: "locationIds",
    area: "regionIds",
    default: "resourceIds",
  };

  // const userPermission = permissionMapping[type] || permissionMapping.default;
  const queryFunction = queryMapping[type] || queryMapping.default;
  const assignedIdType = assignedIdsMapping[type] || assignedIdsMapping.default;

  // if (!userInfo.hasPermission(userPermission)) {
  //   options.value = [];
  //   return;
  // }

  const fetchData = (queryFunc) => {
    return new Promise((resolve, reject) => {
      if (!userInfo.hasPermission(params.queryPermissionId)) return resolve([]);
      queryFunc(params)
        .then((res) => {
          if (res.success) {
            resolve(res.data);
          } else {
            reject(res.error);
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  };

  Promise.all([fetchData(queryFunction), fetchData(querySupportAssignedList)])
    .then((result) => {
      const [allItems, assignedData] = result;
      const assignedIds = assignedData[assignedIdType];
      const newArray = allItems.filter((item) => !assignedIds.includes(item.id));
      options.value = newArray;
      allOptions.value = allItems;
    })
    .catch((error) => {
      console.error("Error fetching data:", error);
    });

  DownSlaConfig.value.dialogVisible = true;
  DownSlaConfig.value.value = "";
  DownSlaConfig.value.areaList = [];
}

//分配客户
function addCostom() {
  const params = {
    pageNumber: 1,
    pageSize: 9999,
    active: true,
    queryPermissionId: 资产管理中心_客户_分配行动策略,
  };

  const activeTenants = userInfo.tenants.filter((tenant) => tenant.activated);
  customConfig.value.dialogVisible = true;
  customConfig.value.customvalue = [];
  options.value = [];
  allOptions.value = [];

  const fetchAssignedTenantIds = () => {
    return new Promise((resolve, reject) => {
      querySupportAssignedList(params)
        .then((res) => {
          if (res.success) {
            resolve(res.data.assignedTenantIds);
          } else {
            reject(res.error);
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  };

  if (!userInfo.hasPermission(params.queryPermissionId)) return;
  fetchAssignedTenantIds()
    .then((assignedTenantIds) => {
      const newArray = activeTenants.filter((item) => !assignedTenantIds.includes(item.id));
      options.value = newArray;
      allOptions.value = activeTenants;
    })
    .catch((error) => {
      console.error("Error fetching assigned tenant IDs:", error);
    });
}

//确认客户
async function customMsg(val) {
  bindRelationCoustom(val);
  // const { success, message, data } = await Support_notesGlobalRelationTenant({ id: form.id, tenantIds: val.value });
  // if (!success) {
  //   await getDetail({ id: form.id }, form);
  //   throw Object.assign(new Error(message));
  // } else {
  //   ElMessage.success("操作成功");

  //   await getDetail({ id: form.id }, form);
  //   customConfig.value.dialogVisible = false;
  // }
}
//确认当前保存的数据
function confirmMsg(val) {
  // // console.log(val.value);
  bindRelation(val);
}
// //删除
const delTitle = ref("");
function delLevel(type, index, data) {
  delTitle.value = "确认取消分配当前数据吗？";
}
function delCostomLevel(type, index, data) {
  delTitle.value = "确认取消分配当前数据吗？";
}
//客户删除
function delCostom(type, index, data) {
  Support_notesDelGlobalRelationTenant({ id: form.id, tenantIds: [data.id] })
    .then(({ success, message, data }) => {
      if (!success) {
        throw Object.assign(new Error(message));
      } else {
        getSupport_notesList({ active: true }).then((res) => {
          if (res.success) {
            let newform = res.data.find((item) => {
              return item.id == form.id;
            });
            getCostom(newform);
            emits("child-event");
          }
        });
        ElMessage.success("操作成功");

        // getDetail({ id: form.id }, form);
        customConfig.value.dialogVisible = false;
      }
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
}
function delConfirm(type, index, data) {
  delRelation({ type, id: data.id });
}
function getCostom(rowall) {
  if (rowall !== undefined) {
    let tenantIds = (rowall.detail && rowall.detail.assignedTenantIds) || rowall.assignedTenantIds || [];

    customsList.value = []; //客户列表
    if (tenantIds.length > 0) {
      getData({ tenantIds })
        .then(({ success, message, data }) => {
          // customsList.value = data;
          type Item = typeof customsList.value;
          customsList.value = data.reduce(
            (p, v) =>
              p.concat({
                id: v.id,
                name: v.name,
                abbreviation: v.abbreviation,
                detail: "",
                active: v.activated,
              }),
            [] as Item
          );
        })
        .catch((error) => {
          if (error instanceof Error) ElMessage.error(error.message);
        });
    } else {
      // throw Object.assign(new Error(message));
    }
  }
}
function getDetail(row: Partial<{ id: string }>, rowall) {
  if (userInfo.hasPermission(资产管理中心_设备_可读)) {
    getDeviceList({
      paging: {
        pageNumber: devicesListPage.value.pageNumber,
        pageSize: devicesListPage.value.pageSize,
      },
      supportNoteId: row.id,
    })
      .then(({ success, message, data, page, total }) => {
        if (!success) throw new Error(message);
        type Item = typeof devicesList.value;
        devicesList.value = data.reduce(
          (p, v) =>
            p.concat({
              id: v.id,
              name: v.name,
              detail: v.config.ipAddress,
              active: v.active,
            }),
          [] as Item
        );
        devicesListPage.value.pageNumber = Number(page);
        devicesListPage.value.total = Number(total);
        // bindDeviceRef.value.setContacts(2, data);
      })
      .catch((e) => {
        if (e instanceof Error) ElMessage.error(e.message);
      });
  }

  if (userInfo.hasPermission(资产管理中心_区域_可读)) {
    getRegionsTenantCurrent({ supportNoteId: row.id })
      .then(({ success, message, data }) => {
        if (!success) throw new Error(message);
        type Item = typeof areaList.value;
        areaList.value = data.reduce(
          (p, v) =>
            p.concat({
              id: v.id,
              name: v.name,
              detail: v.description || "",
              active: v.active,
            }),
          [] as Item
        );

        // bindDeviceRef.value.setContacts(0, data);
      })
      .catch((e) => {
        if (e instanceof Error) ElMessage.error(e.message);
      });
  }
  if (userInfo.hasPermission(资产管理中心_场所_可读)) {
    getLocationsTenantCurrent({ supportNoteId: row.id })
      .then(({ success, message, data }) => {
        if (!success) throw new Error(message);
        type Item = typeof locationList.value;
        locationList.value = data.reduce(
          (p, v) =>
            p.concat({
              id: v.id,
              name: v.name,
              detail: v.description || "",
              active: v.active,
            }),
          [] as Item
        );
        // bindDeviceRef.value.setContacts(1, data);
      })
      .catch((e) => {
        if (e instanceof Error) ElMessage.error(e.message);
      });
  }
}
//绑定客户
async function bindRelationCoustom(item: Record<string, any>) {
  try {
    let res: Promise<import("@/api/service/common").Response<any>> | null = null;

    res = Support_notesGlobalRelationTenant({
      id: form.id,
      tenantIds: item.value,
    });

    if (res) {
      const { success, message } = await res;

      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
      customConfig.value.dialogVisible = false;
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    emits("refresh");
    // getSupport_notesList({ active: true }).then((res) => {
    //   if (res.success) {
    //     let newform = res.data.find((item) => {
    //       return item.id == form.id;
    //     });
    //     getCostom(newform);
    //   }
    // });
  }
}
//绑定关联关系
async function bindRelation(item: Record<string, any>) {
  try {
    let res: Promise<import("@/api/service/common").Response<any>> | null = null;
    switch (item.type) {
      case "area":
        res = Support_notesRelationArea({
          ids: item.value,
          supportNoteIds: [form.id],
        });
        break;
      case "location":
        res = Support_notesRelationLocation({
          ids: item.value,
          supportNoteIds: [form.id],
        });
        break;
      default:
        res = Support_notesRelationDevice({
          resourceIds: item.value,
          id: [form.id],
        });
        break;
    }
    if (res) {
      const { success, message } = await res;

      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
      DownSlaConfig.value.dialogVisible = false;
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    getDetail({ id: form.id }, form);
  }
}
//删除关联关系
async function delRelation(item: Record<string, any>) {
  try {
    let res: Promise<import("@/api/service/common").Response<any>> | null = null;
    switch (item.type) {
      case "area":
        res = Support_notesDelRelationArea({
          ids: [item.id.toString()],
          supportNoteIds: [form.id],
        });
        break;
      case "location":
        res = Support_notesDelRelationLocation({
          ids: [item.id.toString()],
          supportNoteIds: [form.id],
        });
        break;
      default:
        res = Support_notesDelRelationDevice({
          resourceIds: [item.id.toString()],
          id: [form.id],
        });
        break;
    }
    if (res) {
      const { success, message } = await res;
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    getDetail({ id: form.id }, form);
  }
}

//覆盖时间列
function handleClick({ column, evene }: { column: Record<string, any>; evene: Event }) {
  let index = Number(column.label);
  const { activeHours } = form.activeConfig;

  let hours = [];
  activeHours.forEach((v) => {
    hours = hours.concat(v.hours);
  });

  const isActive = hours.includes(index) && hours.filter((v) => v === index).length === 7; // 是否激活
  // console.log(isActive);

  activeHours.forEach((v: Record<string, any>, i: number) => {
    let delIndex = v.hours.indexOf(index);
    if (isActive) {
      v.hours.splice(delIndex, 1);
    } else {
      v.hours.push(index);
    }

    v.hours = [...new Set(v.hours.sort((a, b) => a - b))];
  });
}
//覆盖时间
function handleSelectTime(key: string | number, weekIndex: number, row: Record<string, any>) {
  if (key === "all") {
    allBool.value[weekIndex] = !allBool.value[weekIndex];

    let data = [];
    for (let i = 0; i < 24; i++) {
      data.push(i);
    }

    row.hours = [...new Set(data)];

    if (!allBool.value[weekIndex]) {
      row.hours = [];
    }
  } else {
    const index = row.hours.indexOf(key);
    if (index == -1) {
      row.hours.push(key);
    } else row.hours.splice(index, 1);
  }
}

function openDevice(props) {
  const { href } = router.resolve({
    name: "509596457372745728",
    params: { id: props.id },
    query: {
      fallback: route.name,
      tenant: props.tenantId,
    },
  });
  window.open(href, props.id);
}
//新增服务
function CreateSlaDownConfig() {
  // build();

  if (!roleFormRef.value) return;
  roleFormRef.value.validate((valid) => {
    if (valid) {
      // form.activeConfig.useAutoTimeZone = form.activeConfig.timeZone === getUserInfo()?.zoneId || form.activeConfig.timeZone == '自动时区';
      form.activeConfig.useAutoTimeZone = form.activeConfig.timeZone == "自动时区";
      let data = {
        name: form.name,
        description: form.description,
        activeConfig: form.activeConfig,
        activeNote: form.activeNote,
        inactiveNote: form.inactiveNote,
        id: form.id,
      };
      // if (effectTimeCfg.value.timeZone === "自动时区") {
      //   effectTimeCfg.value.useCustomerTimeZone = true;
      //   effectTimeCfg.value.useDeviceTimeZone = false;
      // }
      emits("confirm", data);

      setTimeout(() => {
        // if (props.detail.activeConfig.timeZone == getUserInfo()?.zoneId) {
        //   form.activeConfig.timeZone = "自动时区";
        // }
      });
    } else {
      ElMessage.error("请输入行动策略名称");
    }
  });
}

const paging = reactive({
  pageNumber: 1,
  pageSize: 10,
  total: 0,
});

const allRegion = ref([]);
const allRegionByPage = ref([]);
const allRegionSelect = ref([]);
const tableData = ref([]);
// handleRefreshRegionTable();
function handleRefreshRegionTable() {
  getRegionsTenantCurrent({ sort: "createdTime,desc", active: true }).then(({ success, data }) => {
    if (success) {
      tableData.value = setTableData(data.sort((x, y) => y.createdTime - x.createdTime));
      allRegionSelect.value = JSON.parse(JSON.stringify(tableData.value));

      paging.total = tableData.value.length;
      tableData.value = setTableDataByPage(tableData.value);
      if (!tableData.value.length && paging.pageNumber !== 1) {
        paging.pageNumber = 1;
        handleRefreshRegionTable();
      }
    } else console.error(JSON.parse(data)?.message || "列表获取失败");
  });
}
function setTableDataByPage(data) {
  const result = [];
  for (let i = 0, len = data.length; i < len; i += paging.pageSize) {
    result.push(data.slice(i, i + paging.pageSize));
  }
  allRegionByPage.value = result;
  return result[paging.pageNumber - 1] || [];
}
function setTableData(data) {
  allRegion.value = JSON.parse(JSON.stringify(data));
  let _formatter = (list) => {
    for (let i = 0; i < list.length; i++) {
      list[i].children = [];
      list[i].isEdit = false;
      const _filter = allRegion.value.filter((v) => {
        return list[i].id === v.parentId;
      });
      if (_filter && _filter?.length) {
        list[i].children = _filter;
        _formatter(list[i].children);
      }
    }
  };
  const result = data.filter((v) => !v.parentId);
  _formatter(result);
  _formatter = null;
  // console.log("result", result);
  return result;
}

function handleAssignCustomer() {
  const tenant = userInfo.currentTenant || {};
  const tenantName = `${tenant.name}[${tenant.abbreviation}]`;
  ElMessageBox.confirm(`确定分配给客户${tenantName}?`, "分配客户", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      bindRelationCoustom({ value: [tenant.id] });
    })
    .catch(() => {});
}
</script>

<style lang="scss" scoped>
:deep(.ql-blank) {
  height: 184px;
}
.work-editor {
  padding: 0 20px;
  box-sizing: border-box;
}
.modules-item {
  .modules-title {
    color: rgba(32, 128, 255, 1);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}
.sun {
  background: rgb(26, 190, 107);
  :deep() .elstyle-button--text {
    color: #fff;
  }
}
.moon {
  background: #fff;
  :deep() .elstyle-button--text {
    color: rgb(153, 153, 153);
  }
}
.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}
</style>
