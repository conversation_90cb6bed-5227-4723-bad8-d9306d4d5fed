<template>
  <div>
    <el-dialog :title="title" v-model="dialogFormVisible" :before-close="handleClose" width="50vw">
      <el-form :model="form" :rules="rules" ref="ruleForm">
        <el-form-item :label="$t('supportNote.Name')" :label-width="formLabelWidth" prop="name">
          <el-input v-model="form.name" autocomplete="off" :placeholder="$t('supportNote.Please enter an action policy name')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('supportNote.Description')" :label-width="formLabelWidth" prop="description">
          <el-input type="textarea" v-model="form.description" autocomplete="off" :rows="2" :placeholder="$t('supportNote.Please enter a description')"></el-input>
        </el-form-item>
        <el-form-item v-if="type == 'add'" :label="$t('supportNote.Select a security directory')" :label-width="formLabelWidth">
          <treeAuth ref="treeAuthRef" :treeStyle="treeStyle"></treeAuth>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel()">{{ $t("glob.Cancel") }}</el-button>
          <el-button type="primary" @click="confirm('ruleForm')">{{ $t("glob.Confirm") }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent } from "vue";
import { ElMessage, ElMenuItem } from "element-plus";

import { addSupport_notes, editSupport_notes } from "@/views/pages/apis/supportNotes";
import { getAlarmClassificationList } from "@/views/pages/apis/alarmClassification";
import treeAuth from "@/components/treeAuth/index.vue";

export default defineComponent({
  name: "supplierCreate",
  components: {
    treeAuth,
  },
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      form: {
        name: "",
        description: "",
        activeConfig: {
          useAutoTimeZone: true,
          timeZone: "自动时区",
          activeHours: {
            weekDay: 0,
            hours: [],
          },
        },
        activeNote: "",
        inactiveNote: "",
        containerId: "",
      },
      containerIdS: null,
      rules: {
        name: [{ required: true, message: "请输入行动策略名称", trigger: "blur" }],
      },
      title: "",
      checked: false,
      dialogFormVisible: false,
      formLabelWidth: "130px",
      type: "",
      options: [],
      value: "",

      disabled: "",
      treeStyle: {
        width: "300px",
        height: "150px",
      },
    };
  },
  watch: {
    dialog(val) {
      // this.$refs["ruleForm"].clearValidate();
      this.dialogFormVisible = val;
    },
    type(val) {
      if (val === "add") {
        for (var key in this.form) {
          this.form[key] = null;
        }
      }
      // console.log(this.form);
    },
  },
  created() {
    // console.log(this.$props, 5555);
    this.getAlarmList();
  },
  methods: {
    getAlarmList() {
      getAlarmClassificationList({}).then((res) => {
        if (res.success) {
          this.options = [...res.data];
        } else {
          ElMessage.error(JSON.parse(res.data)?.message + "操作失败");
        }
      });
    },
    confirm(formName) {
      if (this.type == "add") {
        this.form.containerId = this.$refs.treeAuthRef.treeItem.id;
        const treeItemId = this.form.containerId || this.$refs.treeAuthRef.treeItem.id;
        if (!treeItemId) {
          ElMessage.error("请选择安全目录");
          return;
        }
      } else {
        this.form.containerId = "";
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.form.activeConfig = {
            useAutoTimeZone: true,
            timeZone: "自动时区",
            activeHours: [
              {
                weekDay: 1,
                hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
              },
              {
                weekDay: 2,
                hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
              },
              {
                weekDay: 3,
                hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
              },
              {
                weekDay: 4,
                hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
              },
              {
                weekDay: 5,
                hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
              },
              {
                weekDay: 6,
                hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
              },
              {
                weekDay: 7,
                hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
              },
            ],
          };
          if (this.type === "add") {
            addSupport_notes(this.form)
              .then((res) => {
                if (res.success) {
                  ElMessage.success("新增成功");
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                  this.$refs.treeAuthRef.getSafeContaine();
                  this.$refs.treeAuthRef.treeId = -1;
                  this.$refs.treeAuthRef.treeItem.id = false;
                } else {
                  ElMessage.error(JSON.parse(res.data)?.message);
                  this.$emit("dialogClose", false);
                }
              })
              .catch((e) => {
                if (e instanceof Error) ElMessage.error(e.message);
                this.$emit("dialogClose", false);
              });
          } else {
            editSupport_notes(this.form)
              .then((res) => {
                if (res.success) {
                  ElMessage.success("修改成功");
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                } else {
                  ElMessage.error(JSON.parse(res.data)?.message);
                  this.$emit("dialogClose", false);
                }
              })
              .catch((e) => {
                if (e instanceof Error) ElMessage.error(e.message);
                this.$emit("dialogClose", false);
              });
          }
          this.dialogFormVisible = false;
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      this.$refs.treeAuthRef.getSafeContaine();
      this.$refs.treeAuthRef.treeId = -1;
      this.$refs.treeAuthRef.treeItem.id = false;
    },
    cancel() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      this.$refs.treeAuthRef.getSafeContaine();
      this.$refs.treeAuthRef.treeId = -1;
      this.$refs.treeAuthRef.treeItem.id = false;
    },
    blurChange(data) {
      this.tags.push(data);
    },
    tagClose(index) {
      this.tags.splice(index, 1);
    },
  },
  expose: ["type", "disabled", "title", "form", "value"],
});
</script>
