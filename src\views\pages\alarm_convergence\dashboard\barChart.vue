<template>
  <div class="box">
    <div id="container1" ref="barChartRef"></div>
  </div>
</template>

<script setup lang="ts" generic="T extends object">
import { ref, shallowRef, reactive, readonly, computed, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, watch } from "vue";
import * as echarts from "echarts";
defineOptions({});

const ctx = getCurrentInstance();
if (!ctx) throw new Error("Component context initialization failed!");
// // console.log(ctx);

const props = defineProps({
  id: {},
  data: {},
  colorData: {},
});
const barChartRef = ref<HTMLElement>();
const myChart1 = shallowRef<any>();
/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */

/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}

function mounted() {
  myChart1.value = echarts.init(barChartRef.value!);
  // 绘制图表
  myChart1.value.setOption({
    color: ["#3E97FF", "#3E97FF", "#3E97FF", "#3E97FF", "#3E97FF"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "8%",
      containLabel: true,
    },
    xAxis: [
      {
        type: "category",
        data: ["新建", "处理中", "挂起中", "审批中", "已完成"],
        axisTick: {
          alignWithLabel: true,
        },
      },
    ],
    yAxis: [
      {
        type: "value",
      },
    ],
    series: [
      {
        name: "Direct",
        type: "bar",
        barWidth: "60%",
        data: props.data,
      },
    ],
  });
  const box: any = document.querySelector(".box");
  const resizeObserver = new ResizeObserver(() => {
    myChart1.value.resize();
    // console.log(myChart1);
  });
  resizeObserver.observe(box);

  window.onresize = function () {
    //自适应大小
    myChart1.value.resize();
  };
}

watch(
  () => props.data,
  async (data) => {
    myChart1.value = echarts.init(barChartRef.value!);
    // 绘制图表
    myChart1.value.setOption({
      color: ["#3E97FF", "#3E97FF", "#3E97FF", "#3E97FF", "#3E97FF"],
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "8%",
        containLabel: true,
      },
      xAxis: [
        {
          type: "category",
          data: ["新建", "处理中", "挂起中", "审批中", "已完成"],
          axisTick: {
            alignWithLabel: true,
          },
        },
      ],
      yAxis: [
        {
          type: "value",
        },
      ],
      series: [
        {
          name: "Direct",
          type: "bar",
          barWidth: "60%",
          data: data,
        },
      ],
    });
    const box: any = document.querySelector(".box");
    const resizeObserver = new ResizeObserver(() => {
      myChart1.value.resize();
      // console.log(myChart1);
    });
    resizeObserver.observe(box);

    window.onresize = function () {
      //自适应大小
      myChart1.value.resize({
        heiht: 320,
      });
    };
  }
  // { immediate: true }
);
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

function sum(arr: any) {
  let number = 0;
  arr.forEach((v: any) => {
    number += v.value;
  });

  return number;
}

interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
defineExpose({});

beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ beforeMount, ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ beforeMount, ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ beforeMount, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  height: 250px;
}
#container1 {
  width: 100%;
  height: 250px;
}
</style>
