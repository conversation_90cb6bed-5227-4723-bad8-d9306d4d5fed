<template>
  <div id="alarm-message" ref="circleChartRef"></div>
</template>

<script setup lang="ts" generic="T extends object">
import { ref, reactive, readonly, computed, watch, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";
import * as echarts from "echarts";
defineOptions({});

const ctx = getCurrentInstance();
if (!ctx) throw new Error("Component context initialization failed!");
// // console.log(ctx);

const props = defineProps({
  id: {},
  data: {},
  colorData: {},
});
const circleChartRef = ref<HTMLElement>();
const myChart1 = ref<any>();
/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */

/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  myChart1.value = echarts.init(circleChartRef.value!);
  // 绘制图表
  let title = "告警总数";
  let total = sum(props.data);
  let data = props.data;
  let color = props.colorData;

  myChart1.value.setOption({
    color,
    title: {
      subtext: total,
      subtextStyle: {
        align: "center",
        fontSize: 24,
        color: "#1D2129 ",
      },
      text: title,
      textAlign: "center",
      x: "48%",
      y: "35%",
      textStyle: {
        fontSize: 12,
        color: "#4E5969",
      },
    },

    series: [
      {
        type: "pie",
        radius: ["70%", "85%"],
        // center: ["50%", "60%"],
        avoidLabelOverlap: false,
        label: {
          position: "center",
          show: false,
          fontSize: 12,
          fontWeight: "bold",

          color: "#4E5969",
        },

        emphasis: {
          label: {
            show: false,
            fontSize: 20,
            fontWeight: "bold",
          },
        },
        labelLine: {
          show: false,
        },
        data: data,
      },
    ],
  });

  //绑定滑入事件
  myChart1.value.on("mouseover", (params: any) => {
    let currName = params.name; //当前滑入选中的块 对应的值
    let op = myChart1.value.getOption(); //获取当前的option

    op.title[0].text = currName; //改变标题
    op.title[0].subtext = params.value; //改变对应的值
    myChart1.value.setOption(op, true); //重新渲染
  });
  //滑出还原
  myChart1.value.on("mouseout", () => {
    let op = myChart1.value.getOption();
    op.title[0].text = title; //title为原本标题
    op.title[0].subtext = total; //total为原来计算的总数
    myChart1.value.setOption(op, true);
  });
  window.onresize = function () {
    //自适应大小
    // console.log(123456789);
    myChart1.value.resize();
  };
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

function sum(arr: any) {
  let number = 0;
  arr.forEach((v: any) => {
    number += Number(v.value);
  });
  return number;
}

interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
defineExpose({});

beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ beforeMount, ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ beforeMount, ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ beforeMount, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
</script>

<style scoped lang="scss">
#alarm-message {
  width: 100%;
  height: 160px;
}
#alarm-prop {
  width: 100px;
  height: 160px;
}
</style>
