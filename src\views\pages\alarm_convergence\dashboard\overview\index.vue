<template>
  <div class="tw-relative">
    <el-scrollbar :height="height">
      <div class="overview" :style="{ width: `${width - 6}px` }">
        <el-row :gutter="10">
          <el-col v-for="item in modules" :key="item.flag" :span="item.span" class="tw-mt-[10px]">
            <el-card :bodyStyle="{ height: `${moduleHeight}px`, paddingTop: 0 }">
              <template #header>
                <div class="tw-flex tw-items-center tw-justify-between">
                  <div class="tw-flex tw-cursor-pointer tw-items-center tw-justify-between" @click="item.toRouter()">
                    <span>{{ item.title }}</span>
                    <el-icon><CaretRight /></el-icon>
                  </div>

                  <div class="tw-flex tw-items-center tw-justify-between">
                    <el-select class="tw-w-[120px]" :modelValue="item.componentProps.time" :onUpdate:modelValue="($event) => (item.componentProps.time = $event)" placeholder="Select">
                      <el-option v-for="time in item.timeOptions" :key="`${item.flag}-${time.value}`" :label="time.label" :value="time.value" />
                    </el-select>

                    <el-dropdown @command="(command) => command()" :disabled="!item.moreOption || !item.moreOption.length">
                      <div style="transform: rotate(90deg)">
                        <el-icon :size="16" color=""><MoreFilled /></el-icon>
                      </div>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item v-for="more in item.moreOption || []" :key="`${item.flag}-${more.label}`" :command="more.command">{{ more.label }}</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </template>
              <div class="tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center">
                <component :is="item.component" :time="item.componentProps.time"></component>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-scrollbar>
    <assetSituationMore ref="assetSituationMoreRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject, reactive, nextTick, onMounted } from "vue";

import { useRouter } from "vue-router";

import { MoreFilled, CaretRight } from "@element-plus/icons-vue";

import { timeOptions } from "@/views/pages/apis/overview";

import { 资产管理中心_设备_可读, 智能事件中心_客户_工单可读, 智能事件中心_二维码报障_可读, 智能事件中心_项目_工单可读, 智能事件中心_联系人_工单可读, 智能事件中心_设备_工单可读 } from "@/views/pages/permission";

import getUserInfo from "@/utils/getUserInfo";

import moduleAlarmSituation from "@/views/pages/alarm_convergence/dashboard/overview/module/alarmSituation.vue";
import moduleOrderSituation from "@/views/pages/alarm_convergence/dashboard/overview/module/orderSituation.vue";
import moduleOrderCloseReasons from "@/views/pages/alarm_convergence/dashboard/overview/module/orderCloseReasons.vue";
import moduleTop5CoreIndicators from "@/views/pages/alarm_convergence/dashboard/overview/module/top5CoreIndicators.vue";
import moduleAssetSituation from "@/views/pages/alarm_convergence/dashboard/overview/module/assetSituation.vue";

import { routerV6Busines } from "@/views/pages/common/routeV6";

import assetSituationMore from "@/views/pages/alarm_convergence/dashboard/overview/module/assetSituationMore.vue";

const router = useRouter();

const userInfo = getUserInfo();

interface Props {
  width?: number;
  height?: number;
  title?: string;
}
const props = withDefaults(defineProps<Props>(), { title: "总览" });

const _width = inject("width", ref(0));
const _height = inject("height", ref(0));

const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);

const orderPermission = userInfo.hasPermission(智能事件中心_客户_工单可读) || (userInfo.hasPermission(智能事件中心_项目_工单可读) && userInfo.hasPermission("756061441173225472" as any)) || (userInfo.hasPermission(智能事件中心_联系人_工单可读) && userInfo.hasPermission("756062477225033728" as any)) || (userInfo.hasPermission(智能事件中心_设备_工单可读) && userInfo.hasPermission("756062918394511360" as any));

const devicePermission = userInfo.hasPermission(资产管理中心_设备_可读);

const onlyOrder = orderPermission && !devicePermission; // 只有工单权限
const onlyDevice = !orderPermission && devicePermission; // 只有设备权限

const moduleHeight = computed(() => {
  return width.value < 1000 ? 400 : onlyOrder ? height.value - 68 - 20 : 400;
});

const assetSituationMoreRef = ref(); // 资产更多

// 防止宽度变化computed重新计算导致参数重置
const componentProps = ref({
  alarmSituation: { time: "TODAY" },
  orderSituation: { time: "TODAY" },
  orderCloseReasons: { time: "TODAY" },
  top5CoreIndicators: { time: "TODAY" },
  assetSituation: { time: "TODAY" },
});

const modules: any = computed(() => {
  const alarmSituation = {
    flag: "alarmSituation",
    title: "告警情况",
    span: width.value < 1000 ? 24 : onlyDevice ? 9 : width.value > 1600 ? 9 : 12,
    component: moduleAlarmSituation,
    componentProps: componentProps.value["alarmSituation"],
    timeOptions: timeOptions,
    isShow: devicePermission,
    toRouter: () => {
      router.push({
        name: "597240544460013568", // 告警
      });
    },
  };

  const orderSituation = {
    flag: "orderSituation",
    title: "工单情况",
    span: width.value < 1000 ? 24 : onlyOrder ? 18 : width.value > 1600 ? 10 : 12,
    component: moduleOrderSituation,
    componentProps: componentProps.value["orderSituation"],
    timeOptions: timeOptions,
    isShow: orderPermission,
    toRouter: () => {
      router.push({
        name: "743264910233829376", // 工单
      });
    },
  };

  const orderCloseReasons = {
    flag: "orderCloseReasons",
    title: "工单关闭原因统计",
    span: width.value < 1000 ? 24 : onlyOrder ? 6 : width.value > 1600 ? 5 : 7,
    component: moduleOrderCloseReasons,
    componentProps: componentProps.value["orderCloseReasons"],
    timeOptions: timeOptions,
    isShow: orderPermission,
    toRouter: () => {
      router.push({
        name: "743264910233829376", // 工单
      });
    },
  };
  const top5CoreIndicators = {
    flag: "top5CoreIndicators",
    title: "核心指标Top5",
    span: width.value < 1000 ? 24 : onlyDevice ? 15 : width.value > 1600 ? 14 : 24 - 7,
    component: moduleTop5CoreIndicators,
    componentProps: componentProps.value["top5CoreIndicators"],
    timeOptions: timeOptions.filter((v) => v.value !== "All"),
    isShow: devicePermission,
    toRouter: () => {
      routerV6Busines(""); // 跳转V6统计
    },
  };
  const assetSituation = {
    flag: "assetSituation",
    title: "资产情况",
    span: width.value < 1000 ? 24 : onlyDevice ? 24 : width.value > 1600 ? 10 : 24,
    component: moduleAssetSituation,
    componentProps: componentProps.value["assetSituation"],
    timeOptions: timeOptions,
    isShow: devicePermission,
    toRouter: () => {
      router.push({
        name: "508092969845260288", // 设备管理
      });
    },
    moreOption: [
      {
        label: "查看更多",
        command: () => {
          assetSituationMoreRef.value && assetSituationMoreRef.value.open();
        },
      },
    ],
  };
  return reactive([alarmSituation, orderSituation, orderCloseReasons, top5CoreIndicators, assetSituation].filter((v) => v.isShow));
});

onMounted(() => {
  // nextTick(() => (moduleHeight.value = height.value / 2));
});
</script>

<style lang="scss">
.overview {
  .el-card__header {
    border: 0;
  }
}
</style>
