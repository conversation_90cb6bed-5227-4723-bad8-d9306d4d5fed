<template>
  <el-scrollbar :height="height">
    <div class="overview">
      <div class="home-header">
        <div class="alarm-message">
          <div class="title alarm-message-title">
            <b>告警情况</b>
            <ul>
              <li>全部 <span></span></li>
              <li>Netcare v6 <span></span></li>
              <li>prometheus <span></span></li>
              <li>Nagios <span></span></li>
              <li>faicon <span></span></li>
            </ul>
          </div>
          <div class="alarm-number">
            <img src="@/views/pages/assets/overview/alarm.png" alt="" />
            <div>
              <p style="color: #4e5969">告警中</p>
              <p style="color: #000">258</p>
            </div>
            <div>
              <p>critical</p>
              <p style="color: #db3328">68</p>
            </div>
            <div>
              <p>major</p>
              <p style="color: #f0ad4e">28</p>
            </div>
            <div>
              <p>minor</p>
              <p style="color: #e9d310">76</p>
            </div>
            <div>
              <p>warning</p>
              <p style="color: #31b0d5">26</p>
            </div>
          </div>
          <div class="alarm-chart">
            <div class="alarm_box">
              <circleChartVue
                v-if="alarmShow"
                :id="'alarm-message'"
                :data="[
                  // { value: `${alarmData.alertingCount}`, name: '告警中' },
                  { value: 119, name: 'cirtical' },
                  { value: 56, name: 'minor' },
                  { value: 373, name: 'warning' },
                  { value: 28, name: 'normal' },
                  { value: 1, name: 'informational' },
                  { value: 31, name: 'major' },
                ]"
                :colorData="['#db3328', '#F0AD4E', '#E9D310', '#31B0D5', '#5CB85C', '#7CC67D', '#9CD49D', '#BCFFBE', '#D3D3D3', '#DCEFDE']"
              ></circleChartVue>
            </div>
            <ul>
              <li>
                <div>
                  <i></i>
                  <p>normal</p>
                  <span>28</span>
                </div>
              </li>
              <li>
                <div>
                  <i style="background: #7cc67d"></i>
                  <p>informational</p>
                  <span>1</span>
                </div>
              </li>
              <li>
                <div>
                  <i style="background: #9cd49d"></i>
                  <p>cirtical</p>
                  <span>119</span>
                </div>
              </li>
              <li>
                <div>
                  <i style="background: #bcffbe"></i>
                  <p>minor</p>
                  <span>56</span>
                </div>
              </li>
              <li>
                <div>
                  <i style="background: #d3d3d3"></i>
                  <p>warning</p>
                  <span>373</span>
                </div>
              </li>
              <li>
                <div>
                  <i style="background: #dcefde"></i>
                  <p>major</p>
                  <span>31</span>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div class="work-order">
          <div class="title">
            <b>工单情况</b>
          </div>
          <div class="alarm-number">
            <img src="@/views/pages/assets/overview/workorder.png" alt="" />
            <div>
              <p style="color: #4e5969">工单总数</p>
              <p style="color: #000">304</p>
            </div>
            <div>
              <p>平均响应时长MTTA</p>
              <p style="color: #3e97ff">5 min</p>
            </div>
            <div>
              <p>平均处理时长MTTR</p>
              <p style="color: #3e97ff">45 min</p>
            </div>
          </div>
          <ul>
            <li>
              <span> P1 42 </span>
            </li>
            <li>
              <span> P2 66 </span>
            </li>
            <li>
              <span> P3 128 </span>
            </li>
            <li>
              <span> P4 12 </span>
            </li>
          </ul>
          <ul class="ui-order">
            <li>
              <span> P5 40 </span>
            </li>
            <li>
              <span> P6 8 </span>
            </li>
            <li>
              <span> P7 8 </span>
            </li>
            <li>
              <span> P8 0 </span>
            </li>
          </ul>
          <div class="work-order-chart">
            <barChartVue :data="[8, 214, 12, 14, 56]"></barChartVue>
          </div>
        </div>
        <!-- <div class="alarm-prop">
          <div class="title">
            <b>告警压缩占比</b>
          </div>
          <div class="alarm-prop-chart">
            <circleChartVue
              id="alarm-prop"
              :data="[
                { value: 54567, name: '有效告警' },
                { value: 24556, name: '压缩告警' },
              ]"
              :colorData="['#db3328', '#3e97ff']"
            ></circleChartVue>
          </div>
          <ul class="alarm-prop-msg">
            <li>
              <div>
                <i style="background: #db3328"></i>
                <p>有效告警</p>
                <span>54567</span>
              </div>
            </li>
            <li>
              <div>
                <i style="background: #3e97ff"></i>
                <p>压缩告警</p>
                <span>24556</span>
              </div>
            </li>
          </ul>
        </div> -->
        <div class="notice">
          <!-- <div class="title">
            <b>公告</b>
            <span>更多</span>
          </div>
          <div class="notice-notify">
            <p>
              <span> [版本升级] </span>
              cloud care版本升级发布通知...
            </p>
            <p>
              <span> [电信割接] </span>
              电信IDC网络割接通知
            </p>
          </div> -->
          <div class="notice-alarm">
            <dl>
              <dt>
                <img src="@/views/pages/assets/overview/alarm.png" alt="" />
                告警
              </dt>
              <dd>
                <p v-for="item in alarmTop" :key="item.id">
                  <span> {{ item.eventSeverity }}</span>
                  <b :title="item.desc">{{ item.desc }}</b>
                </p>
                <!-- <p>
                  <span> 3级</span>
                  <b title=" CiscoA-Interface Egress Thresholding Level 3">
                    CiscoA-Interface Egress Thresholding Level 3</b
                  >
                </p>
                <p>
                  <span> 3级</span>
                  H3CB-Udp Jitter Packet Loss Threshold Normal
                </p>
                <p>
                  <span> 3级</span>
                  <b title=" CiscoA-Interface Egress Thresholding Level 3">
                    CiscoA-Interface Egress Thresholding Level 3</b
                  >
                </p>
                <p>
                  <span> 3级</span>
                  H3CB-Udp Jitter Packet Loss Threshold Normal
                </p> -->
              </dd>
            </dl>
            <dl>
              <dt>
                <img src="@/views/pages/assets/overview/workorder.png" alt="" />
                工单
              </dt>
              <dd>
                <p v-for="item in orderTop" :key="item.id">
                  <span> {{ item.originPriority }}</span>
                  <b :title="item.summary">{{ item.summary }}</b>
                </p>
                <!-- <p>
                  <span> 2级</span>
                  wifirouter Device State Down
                </p>
                <p>
                  <span> 1级</span>
                  gd_cesw01.ideal.gnc Config Change
                </p>
                <p>
                  <span> 2级</span>
                  wifirouter Device State Down
                </p>
                <p>
                  <span> 1级</span>
                  gd_cesw01.ideal.gnc Config Change
                </p> -->
              </dd>
            </dl>
          </div>
        </div>
      </div>
      <div class="home-main">
        <div class="title">
          <b style="margin-right: 10px; margin-top: -3px">资产情况</b>
          <el-checkbox-group v-model="checkboxGroup3" size="small">
            <el-checkbox-button v-for="city in cities" :label="city" :key="city">{{ city }}</el-checkbox-button>
          </el-checkbox-group>
          <!-- <ul>
            <li>全部<span></span></li>
            <li v-for="item in current" :key="item.id" @click="getCurrentA(item.id)">{{ item.name }}<span></span></li>
          </ul> -->
        </div>
        <div class="home-main-desc">
          <div class="home-main-desc-left">
            <div style="margin-bottom: 15px" class="home-main-desc-left-title">
              <span>
                <img src="@/views/pages/assets/overview/wifiTitle.png" alt="" />
                网络
              </span>
              <span>
                <el-icon><el-icon-arrow-left /></el-icon>
                <el-icon><el-icon-arrow-right /></el-icon>
              </span>
            </div>
            <div class="home-main-desc-left-line">
              <div>
                <p>
                  <span
                    ><img src="@/views/pages/assets/overview/wifi.png" alt="" />
                    线路类型1: OTN
                  </span>
                  <b>60</b>
                </p>
                <ul>
                  <li>
                    <div>
                      <i></i>
                      正常
                      <p>
                        <span></span>
                      </p>
                      <b>40</b>
                      <span>条</span>
                    </div>
                  </li>
                  <li>
                    <div>
                      <i style="background-color: red"></i>
                      告警
                      <p>
                        <span></span>
                      </p>
                      <b>20</b>
                      <span>条</span>
                    </div>
                  </li>
                  <li>
                    <div>
                      <i style="background-color: #3e97ff"></i>
                      总宽带
                      <p>
                        <span></span>
                      </p>
                      <b>200</b>
                      <span>M</span>
                    </div>
                  </li>
                </ul>
              </div>
              <div>
                <p>
                  <span
                    ><img src="@/views/pages/assets/overview/wifi.png" alt="" />
                    线路类型2: CN2
                  </span>
                  <b>33</b>
                </p>
                <ul>
                  <li>
                    <div>
                      <i></i>
                      正常
                      <p>
                        <span></span>
                      </p>
                      <b>23</b>
                      <span>条</span>
                    </div>
                  </li>
                  <li>
                    <div>
                      <i style="background-color: red"></i>
                      告警
                      <p>
                        <span></span>
                      </p>
                      <b>10</b>
                      <span>条</span>
                    </div>
                  </li>
                  <li>
                    <div>
                      <i style="background-color: #3e97ff"></i>
                      总宽带
                      <p>
                        <span></span>
                      </p>
                      <b>200</b>
                      <span>M</span>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
            <div class="home-main-desc-left-alarm">
              <ul>
                <li v-for="item in resourceList" :key="item.id">
                  <div v-if="item.totalCount == 0 ? false : true">
                    <div class="justify">
                      <b
                        ><img src="@/views/pages/assets/overview/路由器@2x.png" alt="" /> {{ item.resourceTypeName }}(<i style="color: #000">{{ item.totalCount }}</i
                        >)
                      </b>
                      <span style="margin-left: -10px">
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中{{ item.runningCount }}
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">{{ item.alarmCount }}</span> <i /> 停用中 {{ item.offlineCount }} 个
                    </div>
                    <!-- <div class="home-main-desc-alarm-number">
                      运行中
                      <p><span class="xs"></span></p>
                      <p style="margin-left: 2px">115个</p>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警中
                      <p><span class="xs"></span></p>
                      <p style="margin-left: 2px">115个</p>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      停运中
                      <p><span class="xs"></span></p>
                      <p style="margin-left: 2px">115个</p>
                    </div> -->
                    <p>
                      <span class="alarm-red"></span>
                      <span class="alarm-grey"></span>
                      <span class="alarm-green"></span>
                    </p>
                  </div>
                  <!-- <img style="" v-if="item.totalCount == 0 ? true : false" src="@/assets/minSUO.png" alt="" srcset="" /> -->
                </li>
                <!-- <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/路由器@2x.png" alt="" /> 交换机(<i style="color: #000">87</i>) </b>
                      <span>
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中 68
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">16</span> <i />
                      停用中 3 个
                    </div>
                    <p>
                      <span class="alarm-red" style="width: 20%"></span>
                      <span class="alarm-grey" style="width: 5%"></span>
                      <span class="alarm-green"></span>
                    </p>
                  </div>
                </li>
                <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/OLT设备@2x.png" alt="" /> OLT设备(<i style="color: #000">200</i>) </b>
                      <span>
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中 73
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">34</span> <i />
                      停用中 93 个
                    </div>
                    <p>
                      <span class="alarm-red" style="width: 27%"></span>
                      <span class="alarm-grey" style="width: 40%"></span>
                      <span class="alarm-green"></span>
                    </p>
                  </div>
                </li>
                <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/防火墙@2x.png" alt="" /> 防火墙(<i style="color: #000">280</i>) </b>
                      <span>
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中 210
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">13</span> <i />
                      停用中 57 个
                    </div>
                    <p>
                      <span class="alarm-red" style="width: 15%"></span>
                      <span class="alarm-grey" style="width: 25%"></span>
                      <span class="alarm-green"></span>
                    </p>
                  </div>
                </li>
                <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/<EMAIL>" alt="" /> AC(<i style="color: #000">68</i>) </b>
                      <span>
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中 51
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">8</span> <i />
                      停用中 9 个
                    </div>
                    <p>
                      <span class="alarm-red" style="width: 10%"></span>
                      <span class="alarm-grey" style="width: 12%"></span>
                      <span class="alarm-green"></span>
                    </p>
                  </div>
                </li>
                <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/<EMAIL>" alt="" /> AP(<i style="color: #000">211</i>) </b>
                      <span>
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中 189
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">22</span> <i />
                      停用中 2 个
                    </div>
                    <p>
                      <span class="alarm-red" style="width: 20%"></span>
                      <span class="alarm-grey" style="width: 5%"></span>
                      <span class="alarm-green"></span>
                    </p>
                  </div>
                </li>-->
              </ul>
            </div>
          </div>
          <div class="home-main-desc-left">
            <div class="home-main-desc-left-title">
              <span>
                <img src="@/views/pages/assets/overview/数据中心@2x.png" alt="" />
                数据中心
              </span>
              <span>
                <el-icon><el-icon-arrow-left /></el-icon>
                <el-icon><el-icon-arrow-right /></el-icon>
              </span>
            </div>
            <!-- <div style="margin-top: 40px">
              <img style="margin-top: -20px" src="@/assets/maxSUO1.png" alt="" srcset="" />
            </div> -->
            <div class="home-main-desc-left-line" id="middle">
              <div class="data-center">
                <div>
                  <div class="data-center-center">
                    <img src="@/views/pages/assets/overview/<EMAIL>" alt="" />
                  </div>
                  <div class="shuju">
                    数据中心
                    <p>
                      <span></span>
                    </p>
                    23个
                  </div>
                </div>
                <div>
                  <div class="data-center-center">
                    <img src="@/views/pages/assets/overview/machineRoom.png" alt="" />
                  </div>

                  <div class="shuju">
                    机房
                    <p>
                      <span></span>
                    </p>
                    125个
                  </div>
                </div>
                <div>
                  <div class="data-center-center">
                    <img src="@/views/pages/assets/overview/Cabinet.png" alt="" />
                  </div>

                  <div class="shuju">
                    机柜
                    <p>
                      <span></span>
                    </p>
                    7600个
                  </div>
                </div>
              </div>
            </div>
            <div class="home-main-desc-left-alarm">
              <ul>
                <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/路由器@2x.png" alt="" /> 安全设备(<i style="color: #000">187</i>) </b>
                      <span style="margin-left: -10px">
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中133
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">4</span> <i />
                      停用中 50 个
                    </div>
                    <p>
                      <span class="alarm-red" style="width: 20%"></span>
                      <span class="alarm-grey" style="width: 38%"></span>
                      <span class="alarm-green"></span>
                    </p>
                  </div>
                </li>
                <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/<EMAIL>" alt="" /> 服务器(<i style="color: #000">149</i>) </b>
                      <span>
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中 96
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">2</span> <i />
                      停用中 21 个
                    </div>
                    <p>
                      <span class="alarm-red"></span>
                      <span class="alarm-grey"></span>
                      <span class="alarm-green"></span>
                    </p>
                  </div>
                </li>
                <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/存储@2x.png" alt="" /> 存储(<i style="color: #000">520</i>) </b>
                      <span style="margin-left: -10px">
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中508
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">5</span> <i />
                      停用中 12 个
                    </div>
                    <p>
                      <span class="alarm-red"></span>
                      <span class="alarm-grey"></span>
                      <span class="alarm-green"></span>
                    </p>
                  </div>
                </li>
                <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/路由器@2x.png" alt="" /> 数据库(<i style="color: #000">16</i>) </b>
                      <span>
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中 16
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">0</span> <i />
                      停用中 0 个
                    </div>
                    <p>
                      <span class="alarm-green" style="width: 100%"></span>
                      <!-- <span class="alarm-red"></span>
                      <span class="alarm-grey"></span> -->
                    </p>
                  </div>
                </li>
                <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/路由器@2x.png" alt="" /> 空调系统(<i style="color: #000">100</i>) </b>
                      <span>
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中 46
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">12</span> <i />
                      停用中 42 个
                    </div>
                    <p>
                      <span class="alarm-red"></span>
                      <span class="alarm-grey"></span>
                      <span class="alarm-green"></span>
                    </p>
                  </div>
                </li>
                <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/动力系统@2x.png" alt="" /> 动环系统(<i style="color: #000">12</i>) </b>
                      <span>
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中 12
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">0</span> <i />
                      停用中 0 个
                    </div>
                    <p>
                      <span class="alarm-green" style="width: 100%"></span>
                      <!-- <span class="alarm-red" style="width: 60%"></span>
                      <span class="alarm-grey" style="28%"></span> -->
                      <!-- <span class="alarm-green"></span> -->
                    </p>
                  </div>
                </li>
              </ul>
            </div>
          </div>
          <div class="home-main-desc-left">
            <div class="home-main-desc-left-title">
              <span>
                <img src="@/views/pages/assets/overview/云@2x.png" alt="" />
                云
              </span>
              <span>
                <el-icon><el-icon-arrow-left /></el-icon>
                <el-icon><el-icon-arrow-right /></el-icon>
              </span>
            </div>
            <!-- <div style="margin-top: 40px">
              <img style="margin-top: -20px" src="@/assets/maxSUO.png" alt="" srcset="" />
            </div> -->
            <div class="home-main-desc-left-line">
              <div>
                <p>
                  <span
                    ><img src="@/views/pages/assets/overview/天翼云@2x.png" alt="" />
                    天翼云
                  </span>
                  <b>128</b>
                </p>
                <ul>
                  <li>
                    <div>
                      CPU
                      <p>
                        <span></span>
                      </p>
                      <b>10240</b>
                      <span>核</span>
                    </div>
                  </li>
                  <li>
                    <div>
                      内存
                      <p>
                        <span></span>
                      </p>
                      <b>20480</b>
                      <span>G</span>
                    </div>
                  </li>
                  <li>
                    <div>
                      存储
                      <p>
                        <span></span>
                      </p>
                      <b>102400</b>
                      <span>G</span>
                    </div>
                  </li>
                </ul>
              </div>
              <div>
                <p>
                  <span
                    ><img src="@/views/pages/assets/overview/阿里云@2x.png" alt="" />
                    阿里云
                  </span>
                  <b>128</b>
                </p>
                <ul>
                  <li>
                    <div>
                      CPU
                      <p>
                        <span></span>
                      </p>
                      <b>3840</b>
                      <span>核</span>
                    </div>
                  </li>
                  <li>
                    <div>
                      内存
                      <p>
                        <span></span>
                      </p>
                      <b>7680</b>
                      <span>G</span>
                    </div>
                  </li>
                  <li>
                    <div>
                      存储
                      <p>
                        <span></span>
                      </p>
                      <b>30720</b>
                      <span>G</span>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
            <div style="margin-top: 15px" class="home-main-desc-left-alarm">
              <ul>
                <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/虚拟机@2x.png" alt="" /> 虚拟机(<i style="color: #000">214</i>) </b>
                      <span style="margin-left: -10px">
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中214
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">6</span> <i />
                      停用中 0 个
                    </div>
                    <p>
                      <!-- <span class="alarm-red"></span>
                      <span class="alarm-grey"></span> -->
                      <span class="alarm-green" style="width: 100%"></span>
                    </p>
                  </div>
                </li>
                <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/路由器@2x.png" alt="" /> 子网(<i style="color: #000">87</i>) </b>
                      <span>
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中 68
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">18</span> <i />
                      停用中 3 个
                    </div>
                    <p>
                      <span class="alarm-red"></span>
                      <span class="alarm-grey"></span>
                      <span class="alarm-green"></span>
                    </p>
                  </div>
                </li>
                <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/中间件@2x.png" alt="" /> 中间件(<i style="color: #000">99</i>) </b>
                      <span>
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中 95
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">1</span> <i />
                      停用中 2 个
                    </div>
                    <p>
                      <span class="alarm-red" style="width: 5%"></span>
                      <span class="alarm-grey" style="width: 10%"></span>
                      <span class="alarm-green"></span>
                    </p>
                  </div>
                </li>
                <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/云主机@2x.png" alt="" /> 云主机(<i style="color: #000">257</i>) </b>
                      <span style="margin-left: -10px">
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中227
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">10</span> <i />
                      停用中 20 个
                    </div>
                    <p>
                      <span class="alarm-red" style="width: 33%"></span>
                      <span class="alarm-grey" style="width: 33%"></span>
                      <span class="alarm-green"></span>
                    </p>
                  </div>
                </li>
                <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/容器@2x.png" alt="" /> 容器(<i style="color: #000">27</i>) </b>
                      <span>
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中 17
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">4</span> <i />
                      停用中 6 个
                    </div>
                    <p>
                      <span class="alarm-red" style="width: 20%"></span>
                      <span class="alarm-grey" style="width: 56%"></span>
                      <span class="alarm-green"></span>
                    </p>
                  </div>
                </li>
                <li>
                  <div>
                    <div class="justify">
                      <b><img src="@/views/pages/assets/overview/动力系统@2x.png" alt="" /> 操作系统(<i style="color: #000">79</i>) </b>
                      <span>
                        <el-icon><el-icon-video-play /></el-icon>
                        运行中 68
                      </span>
                    </div>
                    <div class="home-main-desc-alarm-number">
                      告警 <span class="red">8</span> <i />
                      停用中 3 个
                    </div>
                    <p>
                      <span class="alarm-red" style="width: 40%"></span>
                      <span class="alarm-grey" style="width: 40%"></span>
                      <span class="alarm-green"></span>
                    </p>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent } from "vue";
import circleChartVue from "../circleChart.vue";
import barChartVue from "../barChart.vue";
import { getHomeCountAboutTotal, getOrderHomestatistics, getAllcurrent, getResourceTypes, getOrderHomeCounts, getOrderListTop, getAlarmListTop, getOrderInfo, type OrderInfo } from "@/api/index";
import { el } from "element-plus/es/locale";
const cityOptions = ["全局", "上海", "广州", "深圳"];
export default {
  data() {
    return {
      alarmData: {},
      orderData: {},
      current: {},
      alarmShow: false,
      orderShow: false,
      resourceList: [
        {
          resourceTypeName: "路由器",
          totalCount: 201,
          runningCount: 200,
          alarmCount: 65,
          offlineCount: 1,
        },
        {
          resourceTypeName: "交换机",
          totalCount: 87,
          runningCount: 84,
          alarmCount: 16,
          offlineCount: 3,
        },
        {
          resourceTypeName: "OLT设备",
          totalCount: 200,
          runningCount: 197,
          alarmCount: 34,
          offlineCount: 3,
        },
        {
          resourceTypeName: "防火墙",
          totalCount: 280,
          runningCount: 278,
          alarmCount: 13,
          offlineCount: 2,
        },
        {
          resourceTypeName: "AC",
          totalCount: 68,
          runningCount: 59,
          alarmCount: 8,
          offlineCount: 9,
        },
        {
          resourceTypeName: "AP",
          totalCount: 213,
          runningCount: 211,
          alarmCount: 22,
          offlineCount: 2,
        },
      ],
      orderMessageData: {},
      orderTop: [],
      alarmTop: [],
      orderInfo: <OrderInfo>{},
      cities: cityOptions,
      checkboxGroup3: ["全局"],
    };
  },
  created() {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    this.getOrderList();
  },
  mounted() {
    this.$nextTick(() => {
      this.getList();
    });
    this.getOrderListFive();
  },
  methods: {
    async getList() {
      // 获取告警
      await (async () => {
        const { success, message, data } = await getHomeCountAboutTotal({});
        if (!success) throw Object.assign(new Error(message), { success, data });
        this.alarmData = data;
        this.alarmShow = true;
      })();
      // 获取工单
      // await (async () => {
      //   const { success, message, data } = await getOrderHomestatistics({});
      //   if (!success) throw Object.assign(new Error(message), { success, data });
      //   this.orderData = data;
      //   this.orderShow = true;
      // })();

      // 获取租户区域
      await (async () => {
        const { success, message, data } = await getAllcurrent({});
        if (!success) throw Object.assign(new Error(message), { success, data });
        this.current = data;
      })();
      // 获取资源
      await (async () => {
        const { success, message, data } = await getResourceTypes({});
        if (!success) throw Object.assign(new Error(message), { success, data });
        // this.resourceList = data.filter((item) => item.totalCount > 0).concat(data.filter((item) => item.totalCount === 0));
        // this.resourceList = [{}, {}];
        // console.log(this.resourceList, "123");
      })();
    },
    async getOrderList() {
      // await (async () => {
      //   const { success, message, data } = await getOrderHomeCounts({});
      //   if (!success) throw Object.assign(new Error(message), { success, data });
      //   this.orderMessageData = data;
      //   this.orderShow = true;
      // })();

      try {
        const { success, data, message } = await getOrderInfo({});
        console.log(success, data, message, "123123123123");
        if (!success) throw new Error(message);
        this.orderInfo = data;
      } catch (error) {
        error instanceof Error && this.$message.error(error.message);
      }
    },
    async getOrderListFive() {
      await (async () => {
        const { success, message, data } = await getOrderListTop({});
        if (!success) throw Object.assign(new Error(message), { success, data });
        this.orderTop = data;
      })();
      await (async () => {
        const { success, message, data } = await getAlarmListTop({});
        if (!success) throw Object.assign(new Error(message), { success, data });
        this.alarmTop = data;
      })();
    },
  },
};
</script>
<script setup lang="ts" name="event_center/dashboard">
import { ref, reactive, readonly, computed, watch, provide, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";
/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 引入 Start ↓↓ ‖----------------------------------------------------------- */
import { ArrowLeft as ElIconArrowLeft, ArrowRight as ElIconArrowRight, VideoPlay as ElIconVideoPlay } from "@element-plus/icons-vue";
import { useConfig } from "@/stores/config";

const height = inject("height", ref(0));

defineOptions({});

const ctx = getCurrentInstance();
if (!ctx) throw new Error("Component context initialization failed!");
// // console.log(ctx);

// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
// const loading = ref<boolean>(false);
// const state = reactive<{
// }>({
// });
// const name = computed(() => name);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
/* --------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖--------‖ ↓↓ 导出 Start ↓↓ ‖-------------------------------------------- */
// interface Slots {
//   default(props: T): any;
// }
// defineSlots<Slots>();
// defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  导出 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */
beforeCreate();
nextTick(created);
nextTick(mounted);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// // onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "onErrorCaptured"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "onRenderTracked"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "onRenderTriggered"), ctx);
onActivated(/* 若组件实例是 <KeepAlive> 缓存树的一部分，当组件被插入到 DOM 中时调用 */ activated, ctx);
onDeactivated(/* 若组件实例是 <KeepAlive> 缓存树的一部分，当组件从 DOM 中被移除时调用 */ deactivated, ctx);
</script>

<style scoped lang="scss">
* {
  list-style: none;
  font-weight: 400;
}
.overview {
  width: 100%;
  height: calc(100% - 28px);
  display: flex;
  flex-direction: column;

  .alarm-message-title {
    flex-direction: column;
    b {
      display: flex;
    }
  }
  .title {
    flex: none;

    // justify-content: space-between;
    // height: 40px;
    > b {
      // display: flex;
      font-size: 16px;
      font-family:
        PingFang SC-Medium,
        PingFang SC;
      font-weight: 900;
      color: #1d2129;
      line-height: 30px;
    }
    ul {
      flex: none;
      width: auto;
      // height: 24px;
      height: 35px;
      display: flex;
      margin-left: 20px;
      overflow-x: scroll; /* 设置溢出滚动 */
      white-space: nowrap;
      overflow-y: hidden;
      /* 隐藏滚动条 */
      scrollbar-width: none; /* firefox */
      -ms-overflow-style: none; /* IE 10+ */
      padding: 5px 0px !important;
      border-radius: 3px;

      > li {
        flex: none;
        padding: 0 15px;
        background: #f2f3f5;
        box-sizing: border-box;
        cursor: pointer;
        color: #86909c;
        display: flex;
        align-items: center;
        // justify-content: center;
        text-align: center;

        > span {
          display: flex;
          height: 14px;
          width: 2px;
          background: #c9cdd4;
          position: relative;
          right: -15px;
        }
        // border-right: 1px solid #c9cdd4;
      }
      > li:last-child {
        span {
          display: none;
        }
      }
    }
    ul::-webkit-scrollbar {
      display: none;
    }
    > span {
      color: #86909c;
      font-size: 12px;
      cursor: pointer;
    }
  }
  // .ui-order {
  //   display: flex;
  //   li {
  //     width: auto;
  //   }
  // }
  .alarm-number {
    width: 100%;
    flex: none;

    display: flex;
    margin-top: 10px;
    > img {
      width: 40px;
      height: 40px;
    }
    > div {
      flex: none;
      text-align: center;
      padding: 0 8px;
      box-sizing: border-box;
      > p:first-child {
        color: #86909c;
      }
      > p:last-child {
        font-weight: 700;
      }
    }
  }
  > .home-header {
    width: 100%;
    height: auto;
    // background: #fff;
    display: flex;
    > div {
      flex: none;
      padding: 20px 10px 0;
      box-sizing: border-box;
    }
    .alarm-message {
      width: 30%;
      background: var(--el-fill-color-blank);

      display: flex;
      flex-direction: column;

      .alarm-chart {
        flex: 1;
        width: 100%;
        display: flex;

        ul {
          padding: 18px 0;
          box-sizing: border-box;
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          > li {
            flex: none;
            width: 50%;
            > div {
              position: relative;

              box-sizing: border-box;
              i {
                position: absolute;
                display: block;
                height: 100%;
                width: 4px;
                border-radius: 4px;
                background: #5cb85c;
              }
              p {
                padding-left: 8px;
                color: #86909c;
                font-size: 12px;
                box-sizing: border-box;
              }
              span {
                padding-left: 8px;
                color: #4e5969;
                font-size: 16px;

                font-weight: bold;
                box-sizing: border-box;
              }
            }
          }
        }
        > div {
          width: 200px;
        }
      }
    }
    .work-order {
      width: 46%;
      background: var(--el-fill-color-blank);
      margin-left: 1%;
      padding: 20px 10px 0;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      ul {
        display: flex;
        padding-top: 10px;
        box-sizing: border-box;
        li {
          flex: 1;
        }
        li:first-child {
          > span {
            background: rgba(211, 59, 59, 0.1);
            color: #db3328;
            padding: 0 3px;
            box-sizing: border-box;
            font-size: 12px;
          }
        }
        li:nth-child(2) {
          > span {
            background: rgba(240, 173, 78, 0.1);
            color: #f0ad4e;
            padding: 0 3px;
            box-sizing: border-box;
            font-size: 12px;
          }
        }
        li:nth-child(3) {
          > span {
            background: rgba(233, 211, 16, 0.1);
            color: #e9d310;
            padding: 0 3px;
            box-sizing: border-box;
            font-size: 12px;
          }
        }
        li:nth-child(4) {
          > span {
            background: rgba(50, 137, 190, 0.1);
            color: #31b0d5;
            padding: 0 3px;
            box-sizing: border-box;
            font-size: 12px;
          }
        }
      }
      .ui-order {
        li:nth-child(1) {
          > span {
            background: rgba(57, 160, 84, 0.1);
            color: #5cb85c;
            padding: 0 3px;
            box-sizing: border-box;
            font-size: 12px;
          }
        }
        li:nth-child(2) {
          > span {
            background: rgba(57, 160, 84, 0.1);
            color: #5cb85c;
            padding: 0 3px;
            box-sizing: border-box;
            font-size: 12px;
          }
        }
        li:nth-child(3) {
          > span {
            background: rgba(57, 160, 84, 0.1);
            color: #5cb85c;
            padding: 0 3px;
            box-sizing: border-box;
            font-size: 12px;
          }
        }
        li:nth-child(4) {
          > span {
            background: rgba(57, 160, 84, 0.1);
            color: #5cb85c;
            padding: 0 3px;
            box-sizing: border-box;
            font-size: 12px;
          }
        }
      }
      .work-order-chart {
        flex: 1;
      }
    }
    .alarm-prop {
      width: 16%;
      margin-left: 1%;
      background: var(--el-fill-color-blank);
      display: flex;
      flex-direction: column;
      > div {
        flex: none;
      }
      > .alarm-prop-chart {
        width: 100%;
      }
      > ul {
        flex: 1;
        padding: 18px 0;
        box-sizing: border-box;

        display: flex;
        flex-wrap: wrap;
        align-items: flex-end;
        > li {
          flex: none;
          width: 50%;
          > div {
            position: relative;

            box-sizing: border-box;
            i {
              position: absolute;
              display: block;
              height: 100%;
              width: 4px;
              border-radius: 4px;
              background: #5cb85c;
            }
            p {
              padding-left: 8px;
              color: #86909c;
              font-size: 12px;
              box-sizing: border-box;
            }
            span {
              padding-left: 8px;
              color: #4e5969;
              font-size: 16px;

              font-weight: bold;
              box-sizing: border-box;
            }
          }
        }
      }
    }
    .notice {
      margin-left: 1%;
      width: 21%;
      background: var(--el-fill-color-blank);
      > .notice-notify {
        padding: 10px 0;
        box-sizing: border-box;
        > p {
          color: #1d2129;
          span {
            color: #86909c;
          }
        }
        border-bottom: 1px solid #c9cdd4;
      }
      .notice-alarm {
        width: 100%;
        > dl {
          width: 100%;
          padding: 20px 0;
          box-sizing: border-box;
          display: flex;

          > dt {
            width: 50px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #2b2f39;
            font-size: 14px;
            img {
              width: 36px;
              height: auto;
              margin-bottom: 5px;
            }
          }
          > dd {
            flex: 1;
            overflow: hidden;
            p {
              width: 100%;
              overflow: hidden; //（文字长度超出限定宽度，则隐藏超出的内容）
              white-space: nowrap; //（设置文字在一行显示，不能换行）
              text-overflow: ellipsis; //（规定当文本溢出时，显示省略符号来代表被修剪的文本）
              span {
                color: red;
                box-sizing: border-box;
                padding: 0 10px;
                box-sizing: border-box;
              }
            }
          }
        }
        > dl:first-child {
          border-bottom: 1px solid #c9cdd4;
        }
        > dl:last-child {
          > dd p span {
            color: #f0ad4e;
            // padding: 0 10px;
            // box-sizing: border-box;
          }
        }
      }
    }
  }
  .home-main {
    flex: none;
    width: 100%;
    height: auto;
    margin-top: 1.5%;
    background: var(--el-fill-color-blank);
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .title {
      // width: 25%;
      display: flex;
      box-sizing: border-box;
      b {
        display: inline;
      }
      ul {
        width: 700px;
        display: flex;
        align-items: center;
        padding: 5px 0px !important;
        border-radius: 3px;
        flex-wrap: wrap;
        height: auto;

        > li {
          flex: none;
          display: flex;
          align-items: center;
          // justify-content: center;
          text-align: center;
          // border-right: 1px solid #c9cdd4;
          > span {
            display: flex;
            height: 14px;
            width: 2px;
            background: #c9cdd4;
            position: relative;
            right: -15px;
          }
          // border-right: 1px solid #c9cdd4;
        }
        > li:last-child {
          span {
            display: none;
          }
        }
      }
    }

    .home-main-desc {
      flex: 1;
      display: flex;
      > div {
        flex: 1;
        padding-right: 15px;
        box-sizing: border-box;
        .home-main-desc-left-title {
          margin-top: 10px;
          height: 48px;
          background: rgba(62, 151, 255, 0.1);
          padding: 0 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          span {
            display: flex;
            align-items: center;
            font-size: 16px;
            color: #1d2129;
            > i {
              margin: 0 8px;
              font-size: 6px;
              cursor: pointer;
            }
          }
          img {
            width: 32px;
            height: auto;
            margin-right: 10px;
          }
        }
        .home-main-desc-left-line {
          display: flex;
          // margin-bottom: 15px;
          padding: 20px 0px 10px;
          box-sizing: border-box;
          > div {
            padding: 0 10px;
            flex: 1;

            > p {
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: space-between;
              img {
                width: 40px;
                height: auto;
                padding-right: 10px;
              }
              > span {
                display: flex;
                font-size: 14px;
                color: #4e5969;
                align-items: center;
              }
              b {
                font-size: 16px;
                color: #1d2129;
              }
            }
            > ul {
              li {
                display: flex;
                > div {
                  width: 100%;
                  display: flex;
                  align-items: center;
                  padding: 5px 0;
                  box-sizing: border-box;
                  p {
                    flex: 1;
                    height: 1px;
                    padding: 0 15px;
                    box-sizing: border-box;
                    span {
                      display: block;
                      height: 1px;
                      border: 1px dashed #86909c;
                    }
                  }
                  i {
                    display: block;
                    width: 6px;
                    height: 6px;
                    background: #5cb85c;
                    border-radius: 50%;
                    margin-right: 5px;
                    box-sizing: border-box;
                  }
                  b {
                    font-weight: normal;
                  }
                  > span {
                    color: #86909c;
                    padding: 0 5px;
                    box-sizing: border-box;
                  }
                }
              }
            }
          }
          > div:first-child {
            padding-right: 10px;
            box-sizing: border-box;
            border-right: 1px solid #e5e6eb;
          }
        }
        #middle {
          padding-left: 0 !important;
          padding-right: 0 !important;
        }
        .home-main-desc-left-alarm {
          ul {
            display: flex;
            flex-wrap: wrap;
            li:nth-child(2n + 1) {
              padding-right: 7px;
            }
            li:nth-child(2n) {
              padding-left: 7px;
            }
            > li {
              width: 50%;
              height: auto;
              padding: 5px 0px 5px 0;

              box-sizing: border-box;

              > div {
                border-radius: 3px;
                background: #f7f8fa;
                padding: 10px;
                box-sizing: border-box;
                > div {
                  display: flex;
                  align-items: center;
                  margin-bottom: 5px;
                  img {
                    width: 20px;
                    height: auto;
                  }
                }
                > div.justify {
                  justify-content: space-between;
                  b {
                    display: block;
                    align-items: center;
                    font-size: 14px;
                    color: #4e5969;
                    width: 8em;

                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    img {
                      float: left;
                    }
                  }
                  span {
                    padding: 2px 10px;
                    box-sizing: border-box;
                    background: rgba(57, 160, 84, 0.1);
                    color: #5cb85c;
                    border-radius: 2px;

                    display: block;
                    align-items: center;

                    width: 8em;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    i {
                      margin-right: 5px;
                    }
                  }
                }
                > div.home-main-desc-alarm-number {
                  font-size: 12px;
                  display: flex;
                  color: #4e5969;
                  .red {
                    color: #db3328;
                    font-size: 14px;
                    padding: 0 5px;
                    font-weight: 900;
                  }
                  i {
                    display: block;
                    height: 14px;
                    width: 1px;
                    background: #c9cdd4;
                    margin: 0 5px;
                  }
                }
                > p {
                  width: 100%;
                  height: 4px;
                  display: flex;
                  > span {
                    flex: none;
                  }
                  > span.alarm-red {
                    display: flex;
                    width: 30%;
                    height: 100%;
                    background: #db3328;
                  }
                  > span.alarm-grey {
                    display: flex;
                    width: 30%;
                    height: 100%;
                    background: #c9cdd4;
                  }
                  > span.alarm-green {
                    display: flex;
                    flex: 1;
                    height: 100%;
                    background: rgba(57, 160, 84, 0.1);
                  }
                }
              }
            }
          }
        }
      }
    }
    .data-center {
      margin-bottom: 10px;
      border: none !important;
      display: flex;
      > div {
        flex: 1;
        padding: 0 10px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        // justify-content: center;
        .data-center-center {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        > div {
          display: flex;
          align-items: center;
          font-size: 12px;
          p {
            flex: 1;
            padding: 0 10px;
            box-sizing: border-box;
            height: 1px;
            display: flex;
            span {
              width: 100%;
              height: 1px;
              border: 1px dashed #86909c;
            }
          }
        }
      }
      img {
        width: 85px;
        height: auto;
      }
    }
  }
}
.xs {
  height: 1px;
  width: 100px;
  border: 1px dashed #86909c;
  display: block;
  margin-left: 10px;
}
.shuju {
  margin-top: 10px;
}
.alarm_box {
  margin-top: 30px;
}
</style>
