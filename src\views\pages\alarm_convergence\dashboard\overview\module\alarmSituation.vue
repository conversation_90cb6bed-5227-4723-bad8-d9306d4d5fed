<template>
  <div ref="boxRef" class="tw-h-full tw-w-full" v-loading="loading">
    <div class="tw-m-auto tw-flex tw-w-fit tw-items-center tw-justify-between tw-bg-[#E5E6EB] tw-p-[4px] tw-text-[14px] tw-font-normal tw-text-[#4E5969]">
      <div class="tw-flex tw-px-[12px] tw-py-[2px]">
        <!-- {{ ratio }} -->
        <el-dropdown @command="(command) => (dataType = command)">
          <el-link link type="primary" underline="never">
            {{ dataTypeText }}
          </el-link>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="item in dataTypeOption" :key="`data-type-${item.value}`" :command="item.value">{{ item.label }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div v-for="monitor in monitorSourcesOption" :key="`monitorType-${monitor.value}`" class="tw-px-[12px] tw-py-[2px]" :class="actionMonitor === monitor.value ? `tw-bg-[#fff] tw-text-[var(--el-color-primary)]` : ``" @click="() => (actionMonitor = monitor.value)">{{ monitor.label }}</div>
    </div>
    <div class="tw-flex tw-h-full tw-w-full tw-items-center tw-justify-between">
      <div>
        <div class="tw-flex tw-items-center" v-for="item in leftAlarmCount" :key="`leftAlarmCount-${item.title}`">
          <div class="tw-mr-[18px] tw-h-[36px] tw-w-[36px]">
            <img class="tw-h-full tw-w-full" :src="item.image" alt="" />
          </div>
          <div>
            <p class="tw-text-[16px] tw-font-normal tw-leading-[32px]">{{ item.title }}</p>
            <p class="tw-text-[22px] tw-font-bold" :style="{ color: item.color }">{{ (dataType === DataType.all ? allData : alertingData)[item.title] }}</p>
          </div>
        </div>
      </div>
      <div class="tw-relative tw-h-full tw-w-full">
        <div class="tw-absolute tw-left-[0] tw-top-[0] tw-flex tw-h-full tw-w-full tw-flex-wrap tw-items-center tw-justify-center">
          <div class="tw-text-center">
            <p class="tw-text-[28px] tw-font-bold">{{ alarmTotalFormat }}</p>
            <p class="tw-flex tw-items-center tw-text-[14px] tw-font-normal tw-text-[#858D9D]">
              {{ t("alarm.Total") }}
              <span class="tw-flex tw-items-center" :style="{ color: ratio.isRise ? 'var(--el-color-success)' : 'var(--el-color-danger)' }">
                <el-icon :size="16" v-if="ratio.isRise"><CaretTop /></el-icon>
                <el-icon :size="16" v-else><CaretBottom /></el-icon>
                {{ ratio.number }}%
              </span>
            </p>
          </div>
        </div>
        <div ref="echartRef" class="tw-h-full tw-w-full"></div>
      </div>
      <div class="tw-min-w-[25%]">
        <div class="tw-flex tw-w-full tw-items-center tw-justify-between tw-leading-[32px]" v-for="item in rightAlarmCount" :key="`rightAlarmCount-${item.title}`">
          <div class="tw-flex tw-items-center tw-justify-between tw-font-normal">
            <div class="tw-mr-[8px] tw-h-[8px] tw-w-[8px] tw-rounded-[50%]" :style="{ backgroundColor: item.color }"></div>
            <span class="tw-text-[16px] tw-text-[#383874]"></span>{{ item.title }}
          </div>
          <div class="tw-text-[16px] tw-font-bold tw-text-[#383874]">{{ (dataType === DataType.all ? allData : alertingData)[item.title] }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick, watch, onBeforeUnmount } from "vue";

import { CaretTop, CaretBottom } from "@element-plus/icons-vue";

import * as echarts from "echarts";

import { useI18n } from "vue-i18n";
const { t } = useI18n();

// import { MonitorSources, monitorSourcesOption } from "@/views/pages/apis/device";

import { getAlarmSituation } from "@/views/pages/apis/overview";
import { ElMessage } from "element-plus";

interface Props {
  time: string;
}

const props = withDefaults(defineProps<Props>(), { time: "" });

watch(
  () => props.time,
  () => handleGetAlarmSituation()
);

enum DataType {
  all = "all",
  alarting = "alarting",
}

const dataTypeOption = ref([
  {
    value: DataType.all,
    label: t("alarm.All"),
  },
  {
    value: DataType.alarting,
    label: t("alarm.Unacknowledge"),
  },
]);

const dataType = ref<string>(DataType.all);

watch(
  () => dataType.value,
  () => handleEchartInit()
);

const monitorSourcesOption = ref([
  // { value: "STANDARD", label: "标准集成" },
  { value: "NET_CARE", label: "NetCare" },
  // { value: "PROMETHEUS", label: "Prometheus" },
  { value: "N9E", label: "Nightingale V6" },
  // { value: "IDEAL_METRIC", label: "Ideal Metric" },
  // { value: "ZABBIX", label: "Zabbix" },
  // { value: "UNKNOWN", label: "Unknown" },
]);

const dataTypeText = computed(() => (dataTypeOption.value.find((v) => v.value === dataType.value) || {}).label || "");

// const monitorOption = ref(monitorSourcesOption.value.map((v) => Object.assign(v, v.value === "NIGHTINGALE_V6" ? { value: "N9E" } : {})));

const actionMonitor = ref((monitorSourcesOption.value.find((v) => v) || {}).value);

watch(
  () => actionMonitor.value,
  () => handleGetAlarmSituation()
);

const alarmTotal = ref<number>(0);

const alarmTotalFormat = computed(() => (alarmTotal.value || 0).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));

const ratio = computed(() => {
  const number = `${(dataType.value === DataType.all ? chartData.value.normalChangeRate : chartData.value.changeRate) || 0}`;
  return { isRise: Number(number) >= 0 ? true : false, number: Math.round(Number(number.replace("-", ""))) };
});

const echartRef = ref<HTMLDivElement>();

const chartData = ref<Record<string, any>>({});

const allData = computed(() => ({
  Critical: chartData.value.criticalCount || 0,
  Major: chartData.value.majorCount || 0,
  Minor: chartData.value.minorCount || 0,
  Warning: chartData.value.warningCount || 0,
  Normal: chartData.value.normalCount || 0,
  Calculating: chartData.value.calculatingCount || 0,
  Monitoring: chartData.value.monitoringCount || 0,
  Informational: chartData.value.informationalCount || 0,
  Symptom: chartData.value.symptomCount || 0,
  Unknown: chartData.value.unKnownCount || 0,
}));

const alertingData = computed(() => ({
  Critical: chartData.value.alertingCriticalCount || 0,
  Major: chartData.value.alertingMajorCount || 0,
  Minor: chartData.value.alertingMinorCount || 0,
  Warning: chartData.value.alertingWarningCount || 0,
  Normal: chartData.value.alertingNormalCount || 0,
  Calculating: chartData.value.alertingCalculatingCount || 0,
  Monitoring: chartData.value.alertingMonitoringCount || 0,
  Informational: chartData.value.alertingInformationalCount || 0,
  Symptom: chartData.value.alertingSymptomCount || 0,
  Unknown: chartData.value.alertingUnKnownCount || 0,
}));

const leftAlarmCount = ref([
  {
    title: "Critical",
    color: "#DB3328",
    image: require("@/views/pages/alarm_convergence/dashboard/overview/assets/Critical.png"),
  },
  {
    title: "Major",
    color: "#F0AD4E",
    image: require("@/views/pages/alarm_convergence/dashboard/overview/assets/Major.png"),
  },
  {
    title: "Minor",
    color: "#E9D310",
    image: require("@/views/pages/alarm_convergence/dashboard/overview/assets/Minor.png"),
  },
  {
    title: "Warning",
    color: "#31B0D5",
    image: require("@/views/pages/alarm_convergence/dashboard/overview/assets/Warning.png"),
  },
]);

const rightAlarmCount = ref([
  {
    title: "Normal",
    color: "#5CB85C",
  },
  {
    title: "Symptom",
    color: "#8E71D4",
  },
  {
    title: "Monitoring",
    color: "#6F94E6",
  },
  {
    title: "Informational",
    color: "#D3D3D3",
  },
  // {
  //   title: "Symptom",
  //   color: "#D3D3D3",
  // },
  {
    title: "Unknown",
    color: "#D3D3D3",
  },
]);

const myChart = ref<any>();

const loading = ref(false);

async function handleGetAlarmSituation() {
  try {
    loading.value = true;
    const { data, message, success } = await getAlarmSituation({ homeTime: props.time, sourceType: actionMonitor.value });
    if (!success) throw new Error(message);
    chartData.value = data;

    //  防止快速切换页面导致echartRef.value为空
    if (!echartRef.value) return;

    myChart.value = echarts.init(echartRef.value);

    handleEchartInit();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}

async function handleEchartInit() {
  alarmTotal.value = dataType.value === DataType.all ? chartData.value.totalCount : chartData.value.alertingCount;
  const legendData = leftAlarmCount.value.map((v) => v.title).concat(rightAlarmCount.value.map((v) => v.title));
  const seriesData = legendData.reduce((a, c) => Object.assign(a, { [c]: 0 }), {});
  const option = {
    title: {},
    tooltip: {
      trigger: "item",
      formatter: "{b} : {c} ({d}%)",
      appendToBody: true,
    },
    toolbox: {
      show: true,
    },
    series: [
      {
        name: "Radius Mode",
        type: "pie",
        radius: ["60%", "90%"],
        roseType: "radius",
        itemStyle: {
          borderRadius: 1,
        },
        label: {
          show: false,
        },
        emphasis: {
          label: {
            show: false,
          },
        },
        data: Object.keys(seriesData).map((v) => ({ value: (dataType.value === DataType.all ? allData.value : alertingData.value)[v], name: v, itemStyle: { color: (leftAlarmCount.value.concat(rightAlarmCount.value as any).find((f) => f.title === v) || {}).color || "#D3D3D3" } })),
      },
    ],
  };

  myChart.value.setOption(option, true);

  observer.observe(boxRef.value);
}

let observer: any = null;

const boxRef = ref();

onMounted(() => {
  nextTick(() => handleGetAlarmSituation());

  observer = new ResizeObserver(() => {
    myChart.value && myChart.value.resize();
  });

  if (boxRef.value) {
    observer.observe(boxRef.value);
  }
});

onBeforeUnmount(() => {
  if (observer && boxRef.value) {
    observer.unobserve(boxRef.value);
  }
});
</script>
