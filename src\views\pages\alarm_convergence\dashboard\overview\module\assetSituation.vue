<template>
  <component :is="props.showAll ? ElScrollbar : 'div'" class="tw-h-full tw-w-full" :height="props.showAll ? props.height - 90 : undefined">
    <div ref="boxRef" class="tw-h-full tw-w-full" v-loading="loading">
      <div class="tw-mb-[18px] tw-flex tw-items-center tw-justify-end">
        <el-select v-model="regionId" style="width: 240px" :empty-values="[null, undefined]" :value-on-clear="null" filterable>
          <el-option :label="t('alarm.Normal')" :value="''" />
          <el-option v-for="item in regionOption" :key="item.id" :label="item.regionName" :value="item.id" />
        </el-select>
      </div>
      <div class="tw-flex" :class="{ 'tw-flex-wrap': props.showAll }">
        <div :style="{ width: `calc(${100 / leng}% ${props.showAll ? ' - 20px' : ''})` }" class="tw-mx-[10px]" :class="{ 'tw-my-[10px]': props.showAll }" v-for="(item, idx) in showLineType" :key="item">
          <div class="tw-flex tw-items-center">
            <div class="tw-mr-[23px] tw-h-[56px]">
              <img class="tw-h-full tw-w-full" src="../assets/xl-icon.png" alt="" />
            </div>
            <div>
              <p class="tw-text-[16px] tw-text-[#383874]">{{t('alarm.Line Type')}} {{ idx + 1 }}: {{ item.lineType }}</p>
              <p class="tw-text-[28px] tw-font-bold tw-text-[#383874]">{{ Number(item.abnormalLineCount) + Number(item.normalLineCount) }}</p>
            </div>
          </div>

          <div
            v-for="(statusItem, statusIdx) in [
              { label: t('alarm.Normal'), color: '#5CB85C', unit: t('alarm.Counts'), key: 'normalLineCount' },
              { label: t('alarm.AlertFiring'), color: '#DB3328', unit:t('alarm.Counts'), key: 'abnormalLineCount' },
              { label: t('alarm.Total bandwidth'), color: '#3E97FF', unit: '', key: 'totalBandwidth' },
            ]"
            :key="statusIdx"
            class="tw-flex tw-items-center tw-justify-between tw-leading-[50px]"
          >
            <div class="tw-flex tw-items-center">
              <div class="tw-mr-[8px] tw-h-[8px] tw-w-[8px] tw-rounded-[50%]" :style="{ background: statusItem.color }"></div>
              <div class="tw-text-[16px] tw-text-[#383874]">{{ statusItem.label }}</div>
            </div>

            <div class="tw-mx-[12px] tw-w-full tw-flex-1 tw-border tw-border-t tw-border-dashed"></div>

            <div>
              <span class="tw-text-[16px] tw-font-bold">{{ statusItem.key == "totalBandwidth" ? formatFileSize(Number(item[statusItem.key])) : item[statusItem.key] }}</span>
              <span class="tw-text-[16px]">{{ statusItem.unit }}</span>
            </div>
          </div>
        </div>
      </div>
      <div :gutter="20" class="tw-my-[12px] tw-flex" :class="{ 'tw-flex-wrap': props.showAll }">
        <div :style="{ width: `calc(${100 / leng}% ${props.showAll ? ' - 20px' : ''})` }" class="tw-mx-[10px]" :class="{ 'tw-my-[10px]': props.showAll }" v-for="item in showDeviceType" :key="item">
          <div class="tw-bg-[#F7F8FA]" :style="{ padding: '20px' }">
            <div class="tw-flex tw-items-center tw-justify-between">
              <div class="tw-flex">
                <img class="tw-h-[20px] tw-w-[20px]" src="../assets/route-icon.png" alt="" />
                <span>{{ item.typename }}({{ item.totalDeviceCount }})</span>
              </div>
              <div class="tw-flex tw-w-fit tw-items-center tw-px-[12px] tw-py-[6px] tw-text-[12px] tw-text-[#5CB85C]" :style="{ background: 'rgba(57, 160, 84, 0.1)' }">
                <span class="tw-flex tw-items-center">
                  <el-icon class="tw-mr-[4px]" color="#5CB85C" :size="16"><VideoPlay /></el-icon>{{t('alarm.Running')}}
                </span>
                <span class="tw-ml-[8px]">{{ item.totalDeviceCount - item.alarmDeviceCount - item.inactiveDeviceCount }}</span>
              </div>
            </div>

            <div>
              <div>
                <span class="tw-text-[12px] tw-text-[#4E5969]"
                  >{{t('alarm.AlertFiring')}} <span class="tw-text-[14px] tw-font-semibold tw-text-[#DB3328]">{{ item.alarmDeviceCount }}</span> {{ t('alarm.individual') }}</span
                >
                <el-divider direction="vertical" />
                <span class="tw-text-[12px] tw-text-[#4E5969]"
                  >{{t('alarm.Disabled')}} <span class="tw-text-[14px] tw-font-semibold tw-text-[#86909C]">{{ item.inactiveDeviceCount }}</span>  {{ t('alarm.individual') }}</span
                >
              </div>

              <div class="tw-relative tw-h-[8px] tw-w-full tw-bg-[#C9CDD4]">
                <div class="tw-absolute tw-left-0 tw-top-0 tw-h-full tw-bg-[#DB3328]" :style="{ width: `${calculatePercentage(item.alarmDeviceCount, item.totalDeviceCount)}%` }"></div>
                <div class="tw-absolute tw-right-0 tw-top-0 tw-h-full tw-bg-[#5CB85C]" :style="{ width: `${calculatePercentage(item.totalDeviceCount - item.alarmDeviceCount - item.inactiveDeviceCount, item.totalDeviceCount)}%` }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </component>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, onBeforeUnmount } from "vue";
import { VideoPlay } from "@element-plus/icons-vue";

import { getRegion, getDeviceType, getLineType } from "@/views/pages/apis/overview";

import { ElMessage, ElScrollbar } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

interface Props {
  time: string;
  showAll: boolean;
  height: number;
}

const props = withDefaults(defineProps<Props>(), { time: "", showAll: false });

watch(
  () => props.time,
  () => handleRefresh()
);

const regionOption = ref<any>([]);

const regionId = ref("");

watch(
  () => regionId.value,
  () => handleRefresh()
);

async function handleGetRegion() {
  const { data, message, success } = await getRegion({});
  if (!success) throw new Error(message);
  regionOption.value = data;
}

const lineType = ref<Record<string, any>>([]);
const deviceType = ref<Record<string, any>>([]);

const boxRef = ref();

const loading = ref(false);

async function handleRefresh() {
  try {
    loading.value = true;

    const params = Object.assign({ homeTime: props.time, regionId: regionId.value }, regionId.value ? {} : { allGlobal: true });

    const [
      /*  */
      { data: deviceTypeData, message: deviceTypeMessage, success: deviceTypeSuccess },
      { data: lineTypeData, message: lineTypeMessage, success: lineTypeSuccess },
    ] = await Promise.all([
      /*  */
      getDeviceType(params),
      getLineType(params),
    ]);
    if (!deviceTypeSuccess) throw new Error(deviceTypeMessage);
    if (!lineTypeSuccess) throw new Error(lineTypeMessage);

    lineType.value = lineTypeData.homeLines;
    deviceType.value = deviceTypeData.deviceTypes;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}

function calculatePercentage(part, total, decimals = 2) {
  if (total <= 0) return 0; // 避免除以零
  return parseFloat(((part / total) * 100).toFixed(decimals));
}

let observer: any = null;

const width = ref(0);

const leng = computed(() => Math.floor(width.value / 300));

const showLineType: any = computed(() => (props.showAll ? lineType.value : lineType.value.length > leng.value ? lineType.value.slice(0, leng.value) : lineType.value));

const showDeviceType: any = computed(() => (props.showAll ? deviceType.value : deviceType.value.length > leng.value ? deviceType.value.slice(0, leng.value) : deviceType.value));

// // 换算
function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + " " + sizes[i];
}

onMounted(() => {
  Promise.all([handleGetRegion(), handleRefresh()]);

  observer = new ResizeObserver((entries) => {
    for (let entry of entries) {
      width.value = entry.contentRect.width;
    }
  });

  if (boxRef.value) {
    observer.observe(boxRef.value);
  }
});

onBeforeUnmount(() => {
  if (observer && boxRef.value) {
    observer.unobserve(boxRef.value);
  }
});
</script>
