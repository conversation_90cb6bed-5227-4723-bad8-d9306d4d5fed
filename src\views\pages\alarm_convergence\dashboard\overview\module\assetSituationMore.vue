<template>
  <div class="overview tw-absolute tw-left-0 tw-top-0 tw-h-full tw-w-full" v-if="dialogVisible">
    <el-card :bodyStyle="{ paddingTop: 0 }">
      <template #header>
        <div class="tw-flex tw-items-center tw-justify-between">
          <div class="tw-flex tw-cursor-pointer tw-items-center tw-justify-between">
            <span>{{ props.title }}</span>
          </div>

          <div class="tw-flex tw-items-center tw-justify-between">
            <el-select class="tw-w-[180px]" :modelValue="time" :onUpdate:modelValue="($event) => (time = $event)" placeholder="Select">
              <el-option v-for="item in timeOptions" :key="`${item.value}`" :label="item.label" :value="item.value" />
            </el-select>

            <el-button link @click="handleClose">
              <el-icon class="tw-ml-[10px]" :size="16" color="var(--el-text-color-regular)"><CloseBold /></el-icon>
            </el-button>
          </div>
        </div>
      </template>
      <assetSituation :time="time" :showAll="true" :height="height" />
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, inject, computed } from "vue";

import assetSituation from "@/views/pages/alarm_convergence/dashboard/overview/module/assetSituation.vue";

import { timeOptions } from "@/views/pages/apis/overview";

import { CloseBold } from "@element-plus/icons-vue";

import { useI18n } from "vue-i18n";
const { t } = useI18n();
import { i18n } from "@/lang/index";

interface Props {
  width?: number;
  height?: number;
  title?: string;
}
const props = withDefaults(defineProps<Props>(), { title:  i18n.global.t("alarm.Devices") });

const _width = inject("width", ref(0));
const _height = inject("height", ref(0));

const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);

const dialogVisible = ref(false);

const time = ref("TODAY");

function handleClose(done) {
  if (done instanceof Function) done();
  else dialogVisible.value = false;
}

defineExpose({
  open: () => {
    dialogVisible.value = true;
  },
});
</script>
