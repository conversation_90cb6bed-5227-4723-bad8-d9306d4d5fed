<template>
  <div ref="boxRef" class="tw-h-full tw-w-full" v-loading="loading">
    <div class="tw-relative tw-h-[50%] tw-w-full">
      <div class="tw-absolute tw-left-[0] tw-top-[0] tw-flex tw-h-full tw-w-full tw-flex-wrap tw-items-center tw-justify-center">
        <div class="tw-text-center">
          <p class="tw-text-[28px] tw-font-bold">{{ closeTotal }}</p>
          <p v-if="props.time !== 'All' || !timeOptions.find((v) => v.value === props.time)" class="tw-flex tw-items-center tw-text-[14px] tw-font-normal tw-text-[#858D9D]">
            较{{ (timeOptions.find((v) => v.value === props.time) || {}).label }}平均
            <!-- <span class="tw-flex tw-items-center" :style="{ color: 'var(--el-color-success)' }">
              <el-icon :size="16"><CaretTop /></el-icon>
              {{ 10 }}%
            </span> -->

            <span class="tw-flex tw-items-center" :style="{ color: ratio.isRise ? 'var(--el-color-success)' : 'var(--el-color-danger)' }">
              <el-icon :size="16" v-if="ratio.isRise"><CaretTop /></el-icon>
              <el-icon :size="16" v-else><CaretBottom /></el-icon>
              {{ ratio.number }}%
            </span>
          </p>
        </div>
      </div>
      <div ref="echartRef" class="tw-h-full tw-w-full"></div>
    </div>
    <div class="tw-mt-[40px]">
      <div v-for="(item, idx) in Object.keys(chartData.closeReasonTop5)" :key="`${item}-${chartData.closeReasonTop5[item]}`" class="tw-flex tw-items-center tw-justify-between tw-leading-[30px]">
        <div class="tw-flex tw-w-[60%] tw-items-center tw-truncate tw-text-[16px]">
          <div class="tw-mr-[8px] tw-h-[8px] tw-w-[8px] tw-rounded-[50%]" :style="{ background: top5Color[idx] }"></div>
          <div class="tw-flex-1 tw-truncate" :title="item">
            {{ item }}
          </div>
        </div>
        <div class="tw-text-[16px] tw-font-bold">{{ chartData.closeReasonTop5[item] }}</div>
        <div class="tw-text-[16px]">{{ calculatePercentage(chartData.closeReasonTop5[item], closeTotal, 1) }}%</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onBeforeUnmount } from "vue";

import { CaretTop, CaretBottom } from "@element-plus/icons-vue";

import { getOrderCloseReasons } from "@/views/pages/apis/overview";

import { ElMessage } from "element-plus";

import { timeOptions } from "@/views/pages/apis/overview";

import * as echarts from "echarts";

const echartRef = ref<HTMLDivElement>();

let myChart: any = null;

const chartData = ref<any>({
  closeReasonTop5: {},
});

const closeTotal = ref(0);

interface Props {
  time: string;
}

const top5Color = ["#883DCF", "#2BB2FE", "#EB3D4D", "#F9C80E", "#5cb85c"];

const props = withDefaults(defineProps<Props>(), { time: "" });

watch(
  () => props.time,
  () => handleRefresh()
);

const ratio = computed(() => {
  const number = `${chartData.value.closeChangeRate || 0}`;
  return { isRise: Number(number) >= 0 ? true : false, number: Math.round(Number(number.replace("-", ""))) };
});

function handleEchartInit() {
  const option = {
    tooltip: {
      trigger: "item",
      formatter: "{b} : {c} ({d}%)",
      appendToBody: true,
    },
    legend: {
      show: false,
    },
    series: [
      {
        name: "Access From",
        type: "pie",
        radius: ["85%", "95%"],
        avoidLabelOverlap: false,
        padAngle: 2,
        itemStyle: {
          borderRadius: 5,
        },
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        labelLine: {
          show: false,
        },
        data: Object.keys(chartData.value.closeReason).map((v) => ({ value: chartData.value.closeReason[v], name: v })),
      },
    ],
  };

  myChart.setOption(option, true);

  // const resizeObserver = new ResizeObserver(() => {
  //   myChart.resize();
  //   // console.log(myChart);
  // });
  // echartRef.value && resizeObserver.observe(echartRef.value);

  // window.onresize = function () {
  //   //自适应大小
  //   myChart.resize();
  // };
}

const loading = ref(false);

async function handleRefresh() {
  try {
    loading.value = true;
    const { data, message, success } = await getOrderCloseReasons({ homeTime: props.time });
    if (!success) throw new Error(message);
    chartData.value = data;
    closeTotal.value = data.closeTotal;

    //  防止快速切换页面导致echartRef.value为空
    if (!echartRef.value) return;

    myChart = echarts.init(echartRef.value);

    handleEchartInit();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}

function calculatePercentage(part, total, decimals = 2) {
  if (total === 0) return 0; // 避免除以零
  return parseFloat(((part / total) * 100).toFixed(decimals));
}

let observer: any = null;
const boxRef = ref();

onMounted(() => {
  handleRefresh();

  observer = new ResizeObserver(() => {
    setTimeout(() => myChart && myChart.resize());
  });

  if (boxRef.value) {
    observer.observe(boxRef.value);
  }
});

onBeforeUnmount(() => {
  if (observer && boxRef.value) {
    observer.unobserve(boxRef.value);
  }
});
</script>
