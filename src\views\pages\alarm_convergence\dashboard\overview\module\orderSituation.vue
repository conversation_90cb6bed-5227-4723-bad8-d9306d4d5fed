<template>
  <div ref="boxRef" class="tw-flex tw-h-full tw-w-full tw-flex-col" v-loading="loading">
    <div class="tw-flex tw-justify-between">
      <div>
        <div>
          <p class="tw-text-base tw-font-normal">工单总数</p>
          <p class="tw-text-[28px] tw-font-bold">{{ orderTotalFormat }}</p>
        </div>
      </div>
      <div class="tw-flex tw-items-center tw-justify-between tw-bg-[#F8F9FA] tw-p-[12px]">
        <div class="tw-text-right">
          <p class="tw-text-[16px] tw-font-bold tw-text-[#383874]">{{ responseTimeTotalFormat }}</p>
          <p>平均响应时间MTTA</p>
        </div>
        <img class="tw-ml-[5px] tw-h-[35px]" src="../assets/Response.png" alt="" />
      </div>
      <div class="tw-flex tw-items-center tw-justify-between tw-bg-[#F8F9FA] tw-p-[12px]">
        <div class="tw-text-right">
          <p class="tw-text-[16px] tw-font-bold tw-text-[#383874]">{{ evaluateTimeTotalFormat }}</p>
          <p>平均处理时间MTTR</p>
        </div>
        <img class="tw-ml-[5px] tw-h-[35px]" src="../assets/Handle.png" alt="" />
      </div>
    </div>

    <div class="tw-m-auto tw-my-2 tw-flex tw-w-fit tw-items-center tw-justify-between tw-bg-[#E5E6EB] tw-p-[4px] tw-text-[14px] tw-font-normal tw-text-[#4E5969]">
      <div v-for="ticket in ticketType" :key="`ticketType-${ticket.value}`" class="tw-cursor-pointer tw-px-[12px] tw-py-[2px]" :class="actionTicket === ticket.value ? `tw-bg-[#fff] tw-text-[var(--el-color-primary)]` : ``" @click="() => (actionTicket = ticket.value)">{{ ticket.label }}</div>
    </div>

    <div class="tw-m-auto tw-flex tw-w-fit tw-items-center tw-justify-between">
      <div
        v-for="priorityItem in priorityCount"
        :key="`${actionTicket}-${priorityItem.value}`"
        :class="`tw-mx-[5px] tw-rounded tw-px-[8px] tw-py-[2px] tw-text-[14px]`"
        :style="{
          color: priorityItem.color,
          backgroundColor:
            {
              [priority.P1]: 'rgba(211, 59, 59, 0.1)',
              [priority.P2]: 'rgba(211, 187, 57, 0.1)',
              [priority.P3]: 'rgba(50, 137, 190, 0.1)',
              VERY_URGENT: 'rgba(211, 59, 59, 0.1)',
              URGENT: 'rgba(211, 187, 57, 0.1)',
              NORMAL: 'rgba(50, 137, 190, 0.1)',
            }[priorityItem.value] || 'rgba(82, 196, 26, 0.1)',
        }"
      >
        <span class="">{{ priorityItem.label }} {{ priorityItem.count || 0 }}</span>
      </div>
      <!-- border: `1px solid ${priority.color}` -->
    </div>

    <div ref="echartRef" class="tw-w-full tw-flex-1"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch, onBeforeUnmount, markRaw } from "vue";

import { priority, priorityOption } from "@/views/pages/apis/eventPriority";

import { getOrderSituation } from "@/views/pages/apis/overview";

import { eventStateOption, serviceStateOption } from "@/views/pages/apis/event";
import { changeStateOption } from "@/views/pages/apis/change";
import { questionStateOption } from "@/views/pages/apis/question";
import { statusOption as qrcodeStatusOption, urgencyOption } from "@/views/pages/apis/qrcode";
import { ElMessage } from "element-plus";

import * as echarts from "echarts";

interface Props {
  time: string;
}

const props = withDefaults(defineProps<Props>(), { time: "" });

watch(
  () => props.time,
  () => getEventCountData()
);

const orderTotal = ref(0);

const orderTotalFormat = computed(() => orderTotal.value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","));

enum TicketType {
  Event = "event",
  Service = "service",
  Change = "change",
  Question = "question",
  Qrcode = "qrcode",
}

const ticketType = ref([
  {
    value: TicketType.Event,
    label: "事件",
  },
  {
    value: TicketType.Service,
    label: "服务请求",
  },
  {
    value: TicketType.Change,
    label: "变更",
  },
  {
    value: TicketType.Question,
    label: "问题",
  },
  {
    value: TicketType.Qrcode,
    label: "二维码报障",
  },
]);

const actionTicket = ref((ticketType.value.find((v) => v) || {}).value);

watch(
  () => actionTicket.value,
  () => handleEchartInit()
);

const echartRef = ref<HTMLDivElement>();

const myChart = ref<any>();

const echartData = ref<any>({});

const priorityCount = ref<any>({});

const evaluateTimeTotal = ref<any>(0);
const responseTimeTotal = ref<any>(0);

const evaluateTimeTotalFormat = computed(() => {
  const days = Math.floor(evaluateTimeTotal.value / (24 * 60));
  const hours = Math.floor((evaluateTimeTotal.value % (24 * 60)) / 60);
  const minutes = evaluateTimeTotal.value % 60;
  return (days ? `${days}d` : ``) + (hours ? `${hours}hours` : ``) + `${minutes}mins`;
});

const responseTimeTotalFormat = computed(() => {
  const days = Math.floor(responseTimeTotal.value / (24 * 60));
  const hours = Math.floor((responseTimeTotal.value % (24 * 60)) / 60);
  const minutes = responseTimeTotal.value % 60;
  return (days ? `${days}d` : ``) + (hours ? `${hours}hours` : ``) + `${minutes}mins`;
});

const loading = ref(false);

async function getEventCountData() {
  try {
    loading.value = true;

    const { success, message, data } = await getOrderSituation({ homeTime: props.time });

    if (!success) throw new Error(message);

    const { eventMap, serviceMap, changeCountMap, questionMap, qrCode, eventTotal, evaluateTime, responseTime } = data;
    echartData.value = { eventMap, serviceMap, changeCountMap, questionMap, qrCode };

    orderTotal.value = Number(eventTotal);
    evaluateTimeTotal.value = Math.round(Number(evaluateTime));
    responseTimeTotal.value = Math.round(Number(responseTime));

    //  防止快速切换页面导致echartRef.value为空
    if (!echartRef.value) return;

    myChart.value = markRaw(echarts.init(echartRef.value));

    nextTick(() => {
      handleEchartInit();
    });
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}

async function handleEchartInit() {
  const { eventMap, serviceMap, changeCountMap, questionMap, qrCode } = echartData.value;
  const config: any = {};

  const data = {
    [TicketType.Event]: eventMap.EVENT,
    [TicketType.Service]: serviceMap.SERVICE,
    [TicketType.Change]: changeCountMap.CHANGE,
    [TicketType.Question]: questionMap.QUESTION,
    [TicketType.Qrcode]: qrCode.QRCode,
  }[actionTicket.value as TicketType];

  const orderState = {
    /* 工单状态 */
    [TicketType.Event]: eventStateOption,
    [TicketType.Service]: serviceStateOption,
    [TicketType.Change]: changeStateOption,
    [TicketType.Question]: questionStateOption,
    [TicketType.Qrcode]: qrcodeStatusOption,
  }[actionTicket.value as TicketType];

  // const status = Object.keys(orderState);
  const status = orderState.map((v) => v.value);
  config.data = ({ [TicketType.Qrcode]: urgencyOption }[actionTicket.value as TicketType] || priorityOption).reduce((a, c) => Object.assign(a, { [c.value]: [] }), {} as any);

  config.xAxisData = orderState.filter((v) => status.includes(v.value)).map((v) => v.label);

  for (let i = 0; i < Object.keys(config.data).length; i++) {
    const yxj = Object.keys(config.data)[i];
    for (let p = 0; p < status.length; p++) {
      config.data[yxj].push(data[status[p]][yxj]);
    }
  }

  nextTick(() => {
    const priorityTotal = Object.keys(config.data).reduce((a, c) => Object.assign(a, { [c]: config.data[c].reduce((p, t) => Number(p) + Number(t), 0) }), {});
    priorityCount.value = (actionTicket.value !== TicketType.Qrcode ? priorityOption : urgencyOption).map((v) => Object.assign(v, { count: priorityTotal[v.value] }));
  });

  config.series = Object.keys(config.data)
    .map((v: string) => {
      return {
        name: actionTicket.value !== TicketType.Qrcode ? v : (urgencyOption.find((f) => f.value === v) || {}).label,
        type: "bar",
        // coordinateSystem: "cartesian2d",
        // coordinateSystem: "polar",
        stack: "Ad",
        emphasis: {
          focus: "series",
        },
        data: config.data[v],
        itemStyle: {
          color: ((actionTicket.value !== TicketType.Qrcode ? priorityOption : urgencyOption).find((f) => f.value === v) || {}).color,
        },
      };
    })
    .reverse();

  const option = {
    tooltip: {
      trigger: "item",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      show: false,
    },
    grid: {
      left: "3%",
      right: "3%",
      bottom: "8%",
      containLabel: true,
    },
    xAxis: [
      {
        type: "category",
        data: config.xAxisData,

        axisLabel: {
          interval: 0, // 强制显示所有标签
          // margin: 15, // 标签与轴线的间距
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "数量统计",
        min: 0,
      },
    ],
    series: config.series,
  };

  myChart.value.setOption(option, true);

  //
  //   // const resizeObserver = new ResizeObserver(() => {
  //   //   myChart.value && myChart.value.resize();
  //   // });
  //   // echartRef.value && resizeObserver.observe(echartRef.value);
  //   // window.onresize = function () {
  //   //   //自适应大小
  //   //   myChart.value && myChart.value.resize();
  //   // };
  //   // window.addEventListener("resize", () => myChart.value && myChart.value.resize());
  // });
}

let observer: any = null;

const boxRef = ref();

onMounted(() => {
  nextTick(() => {
    getEventCountData();
  });

  observer = new ResizeObserver(() => {
    try {
      myChart.value && myChart.value.resize();
    } catch (error) {
      /*  */
    }
  });

  if (boxRef.value) {
    observer.observe(boxRef.value);
  }
});

onBeforeUnmount(() => {
  if (observer && boxRef.value) {
    observer.unobserve(boxRef.value);
  }
});
</script>
