<template>
  <el-row :gutter="20" class="tw-h-full tw-w-full" v-loading="loading">
    <el-col :span="6" v-for="item in indicator" :key="item.title">
      <div>
        <p class="tw-text-[16px] tw-text-[#383874]">{{ item.title }}</p>
        <p class="tw-flex tw-items-baseline">
          <span class="tw-text-[28px] tw-font-bold">{{ item.unit === "hours" ? msToHours(Number(item.utilizationRate || "0")) : formatNumber(Number(item.utilizationRate || "0")) }}{{ item.unit }}</span>
          <span class="tw-flex tw-h-fit tw-items-center" :style="{ color: item.ratio.isRise ? 'var(--el-color-success)' : 'var(--el-color-danger)' }">
            <el-icon v-if="item.ratio.isRise"><CaretTop /></el-icon>
            <el-icon v-else><CaretBottom /></el-icon>
            {{ item.ratio.number }}%
          </span>
        </p>
        <!-- <p class="tw-text-[14px] tw-text-[#858D9D]">较近7日平均上升10%</p> -->
      </div>

      <div>
        <div class="tw-relative tw-my-[12px] tw-h-[45px] tw-w-full" v-for="device in item.devices" :key="device.name">
          <div class="tw-absolute tw-left-0 tw-top-0 tw-h-full" :style="{ width: `${device.progress}%`, background: Number(device.progress || 0) >= 90 ? 'rgba(211, 59, 59, 0.1)' : 'rgba(197, 224, 255, 1)' }"></div>
          <div class="tw-absolute tw-left-0 tw-top-0 tw-flex tw-h-full tw-w-full tw-items-center tw-justify-between tw-px-[10px]">
            <span class="tw-text-[14px] tw-font-medium tw-text-[#1D2630]">{{ device.name }}</span>
            <span class="tw-text-[14px] tw-font-bold tw-text-[#1D2630]">{{ formatNumber(Number(device.value || "0")) }}{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import { CaretTop, CaretBottom } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

import { getTop5CoreIndicators } from "@/views/pages/apis/overview";

interface Props {
  time: string;
}

const props = withDefaults(defineProps<Props>(), { time: "" });

watch(
  () => props.time,
  () => handleRefresh()
);

const indicator = ref<Record<string, any>>([]);

function formatNumber(num) {
  // 先将数字转换为字符串，检查是否有小数部分
  const str = num.toString();
  if (str.indexOf(".") === -1) {
    // 没有小数部分，直接返回整数
    return num;
  } else {
    // 有小数部分，保留两位
    return num.toFixed(2);
  }
}

function msToHours(ms, decimalPlaces = 2) {
  const str = Number(ms) / (1000 * 60 * 60);
  if (str.toString().indexOf(".") === -1) {
    // 没有小数部分，直接返回整数
    return str;
  } else {
    // 有小数部分，保留两位
    return str.toFixed(decimalPlaces);
  }
}

const loading = ref(false);

const ratio = (num) => {
  const number = `${num || 0}`;
  return { isRise: Number(number) >= 0 ? true : false, number: Math.round(Number(number.replace("-", ""))) };
};

async function handleRefresh() {
  try {
    indicator.value = [];

    loading.value = true;
    const { data, message, success } = await getTop5CoreIndicators({ homeTime: props.time });
    if (!success) throw new Error(message);
    // cpu
    indicator.value.push({
      title: "CPU使用率",
      unit: "%",
      ratio: ratio(data.cpuChage || 0),
      utilizationRate: data.cpuAvg || 0,
      devices: data.cpu.map((item) => ({ name: item.resourceName, value: item.valueAvg, progress: Number(item.valueAvg) < 0 ? 0 : Number(item.valueAvg) > 100 ? 100 : item.valueAvg })),
    });

    indicator.value.push({
      title: "内存使用率",
      unit: "%",
      ratio: ratio(data.memoryChage || 0),
      utilizationRate: data.memoryAvg || 0,
      devices: data.memory.map((item) => ({ name: item.resourceName, value: item.valueAvg, progress: Number(item.valueAvg) < 0 ? 0 : Number(item.valueAvg) > 100 ? 100 : item.valueAvg })),
    });

    indicator.value.push({
      title: "线路利用率",
      unit: "%",
      ratio: ratio(data.linkChage || 0),
      utilizationRate: data.linkAvg || 0,
      devices: data.link.map((item) => ({ name: item.resourceName, value: item.valueAvg, progress: Number(item.valueAvg) < 0 ? 0 : Number(item.valueAvg) > 100 ? 100 : item.valueAvg })),
    });

    indicator.value.push({
      title: "设备在线时长",
      unit: "hours",
      ratio: ratio(data.deviceOnlineTimeChage || 0),
      utilizationRate: data.deviceOnlineTimeAvg || 0,
      devices: data.systeamTime.map((item) => ({ name: item.resourceName, value: msToHours(item["upTimes"]), progress: (Number(item.upTimes) / Number(item.times)) * 100 < 0 ? 0 : (Number(item.upTimes) / Number(item.times)) * 100 > 100 ? 100 : (Number(item.upTimes) / Number(item.times)) * 100 })),
    });
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  handleRefresh();
});
</script>
