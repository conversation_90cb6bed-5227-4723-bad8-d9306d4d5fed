<template>
  <div class="tw-flex tw-h-[50px] tw-flex-nowrap tw-items-center tw-pb-[20px]">
    <el-page-header class="tw-w-full" :content="`${$t('generalDetails.Change Details')}${state.data.draft ? `(草稿)` : route.params.id || ''}`" @back="handleCancel()">
      <template #extra>
        <div class="event">
          <el-row :gutter="24" type="flex" justify="end" align="middle">
            <el-col :span="7" :style="{ display: 'flex', justifyContent: 'flex-end' }">
              <el-button-group class="tw-flex">
                <el-button :type="(currentEventState.type as '') || ''" :disabled="state.data.changeState === changeOperate.CLOSED" @click.stop>{{ currentEventState.label || "" }}</el-button>
                <!-- <el-button v-if="state.data.operation == changeOperate.COMPLETE" type="success" :disabled="state.data.changeState === changeOperate.CLOSED" @click.stop> 处理中</el-button> -->

                <el-dropdown @command="$event.command()" :disabled="state.data.changeState === changeOperate.CLOSED">
                  <el-button :type="(currentEventState.type as '') || ''" :disabled="state.data.changeState === changeOperate.CLOSED" @click.stop>
                    <template v-if="!!operation">
                      <span v-if="OperateChanged" class="tw-mr-1 tw-text-white">*</span>
                      {{ $t("generalDetails." + (find(changeOperateOption, (v) => v.value === operation) || {}).label) }}</template
                    >

                    <template v-else>
                      <span v-if="OperateChanged" class="tw-mr-1 tw-text-white">*</span>
                      {{ $t("generalDetails." + stateRightText) }}
                    </template>
                    <el-icon class="tw-ml-2"><ArrowDown></ArrowDown></el-icon>
                  </el-button>
                  <!-- <el-button v-if="state.data.operation === changeOperate.COMPLETE" type="success" :disabled="state.data.changeState === changeOperate.CLOSED" @click.stop>
                    <template v-if="!!operation">{{ (find(changeOperateOption, (v) => v.value === operation) || {}).label }}</template>

                    <template v-else>
                      {{ stateRightText }}
                    </template>
                    <el-icon class="tw-ml-2"><ArrowDown></ArrowDown></el-icon>
                  </el-button> -->

                  <template #dropdown>
                    <el-dropdown-menu class="tw-min-w-[100px]">
                      <el-dropdown-item
                        v-for="item in [
                          // changeStateWithdraw, changeStateAbandon, changeStateComplete, changeStateClose
                          // { command: changeOperate.DISPOSE, /*   */ execute: () => handleDispose(state.data as DataItem), /*                                             */ center: $t('generalDetails.handle'), disabled: [changeState.UN_APPROVED, changeState.SUSPEND, changeState.PROCESSING, changeState.AUTO_CLOSED, changeState.CLOSED].includes((state.data.changeState as changeState) || ('' as changeState)) || state.data.approveState === 'UN_APPROVE' || !verifyPermissionIds.includes(智能事件中心_变更工单_更新) },
                          { command: changeOperate.WITHDRAW, /*  */ execute: () => handleClose(state.data as DataItem, changeOperate.WITHDRAW, changeStateWithdraw), /*  */ center: $t('generalDetails.Withdrawal'), disabled: state.data.draft ? /* state.data.approveState == 'APPROVED' || */ state.data.operation == 'WITHDRAW' || [changeState.PROCESSING, changeState.AUTO_CLOSED, changeState.CLOSED].includes((state.data.changeState as changeState) || ('' as changeState)) : ![changeState.PROCESSING].includes((state.data.changeState as changeState) || ('' as changeState)) },
                          { command: changeOperate.ABANDON, /*   */ execute: () => handleClose(state.data as DataItem, changeOperate.ABANDON, changeStateAbandon), /*    */ center: $t('generalDetails.Abandoning'), disabled: [changeState.AUTO_CLOSED, changeState.CLOSED].includes((state.data.changeState as changeState) || ('' as changeState)) || state.data.approveState === 'UN_APPROVE' },
                          { command: changeOperate.COMPLETE, /*  */ execute: () => handleClose(state.data as DataItem, changeOperate.COMPLETE, changeStateComplete), /*  */ center: $t('generalDetails.Completed'), disabled: [changeState.AUTO_CLOSED, changeState.CLOSED].includes((state.data.changeState as changeState) || ('' as changeState)) || state.data.approveState === 'UN_APPROVE' },
                          { command: changeOperate.CLOSED, /*    */ execute: () => handleClose(state.data as DataItem, changeOperate.CLOSED, changeStateClose), /*       */ center: $t('generalDetails.Close'), disabled: !orderIsClose },
                        ]"
                        :key="item.command"
                        :command="{ command: item.execute }"
                        :disabled="state.loading || item.disabled"
                      >
                        {{ item.center }}
                        <!-- {{ state.data.approveState != "APPROVED" }} -->
                        <!-- {{ ![changeOperate.COMPLETE].includes((state.data.operation as changeOperate) || ("" as changeOperate)) }} -->
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-button-group>

              <!-- -->
            </el-col>
            <el-col :span="7" style="display: flex">
              <span v-if="userGroupChanged" class="tw-mr-1 tw-text-red-500">*</span>
              <el-select clearable :model-value="userGroups.find((v) => v.id === state.data.teamId) ? state.data.teamId : ''" @change="handleChangeUserGroup" :placeholder="$t('generalDetails.Please select a user group')" filterable :disabled="[changeState.UN_APPROVED, changeState.AUTO_CLOSED, changeState.CLOSED].includes((state.data.changeState as changeState) || ('' as changeState)) || state.data.approveState != 'APPROVED'">
                <el-option v-for="userGroup in userGroups" :key="userGroup.id" :label="userGroup.name" :value="userGroup.id as string"></el-option>
              </el-select>
            </el-col>
            <el-col :span="7" style="display: flex">
              <span v-if="userChanged" class="tw-mr-1 tw-text-red-500">*</span>
              <el-select :model-value="userList.find((v) => v.id === state.data.userId) ? state.data.userId : ''" @change="handleChangeUser" :placeholder="$t('generalDetails.Please select a user')" filterable :disabled="[changeState.UN_APPROVED, changeState.AUTO_CLOSED, changeState.CLOSED].includes((state.data.changeState as changeState) || ('' as changeState)) || state.data.approveState != 'APPROVED'">
                <el-option v-for="user in userList" :key="user.id" :label="user.name + `(${user.account}@${user.tenantAbbreviation})`" :value="user.id"></el-option>
              </el-select>
            </el-col>
            <el-col :span="3" :style="{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }">
              <el-tooltip :visible="state.data.draft" placement="top" effect="light" popper-class="draft-tooltip">
                <template #content>
                  <span>{{ $t("generalDetails.Click to save and create a work order") }}</span>
                </template>
                <el-button type="primary" :disabled="!verifyPermissionIds.includes(智能事件中心_变更工单_更新) && !verifyPermissionIds.includes(智能事件中心_工单_更新)" v-preventReClick @click="handleChangeSave">{{ $t("generalDetails.Save") }}</el-button>
              </el-tooltip>
            </el-col>
            <!-- <el-col :span="3" :style="{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }">
              <el-checkbox v-model="typeOfTransferOrUpgrade" false-label="transfer" true-label="eventUpgrade" style="margin-right: 5px" :disabled="[changeState.NOT_STARTED, changeState.NOT_STARTED, changeState.CLOSED].includes(state.data.changeState as changeState || '' as changeState)">升级</el-checkbox>
              <el-tooltip class="item" effect="dark" content="若需要升级请先点选升级复选框" placement="bottom">
                <el-icon class="tipIcon"><InfoFilled></InfoFilled></el-icon>
              </el-tooltip>
            </el-col> -->
          </el-row>
        </div>
      </template>
    </el-page-header>
  </div>
  <el-scrollbar :height="height - 50">
    <el-card v-loading="state.loading" class="tw-mb-[18px] tw-h-[152px]">
      <template #header>
        <div style="display: flex; justify-content: space-between">
          <el-button link type="primary" class="tw-font-semibold" :style="{ color: 'var(--el-color-primary)' }" :disabled="[changeState.AUTO_CLOSED, changeState.CLOSED].includes((state.data.changeState as changeState) || ('' as changeState))" v-preventReClick @click="handleEditSummary">
            <span v-if="digestChanged" style="color: red; margin-left: 2px">*</span>
            <span>{{ state.data.digest || "--" }}</span>
          </el-button>
          <div style="display: flex">
            <el-text type="primary">{{ tickGroupConfig.ticketClassificationNames.join("/") }}</el-text>
            <div style="margin-right: 2px" v-for="itemA in sortedLocalesOption" :key="itemA.value">
              <el-tooltip v-if="isLanguageMatch(itemA.value)" class="item" effect="light" :content="getTooltipContent(itemA.value, itemA.zh_label)" placement="bottom">
                <div
                  :style="{
                    background: `url(${itemA.icon}) no-repeat left / auto`,
                    paddingLeft: '30px',
                    width: '22px',
                    height: '22px',
                  }"
                ></div>
              </el-tooltip>
            </div>
            <div>{{ state.data.category }}</div>
          </div>
        </div>
      </template>
      <template #default>
        <FormModel :model="form" :style="{ marginBottom: '-18px' }">
          <!-- <FormItem :span="8" label="创建时间" tooltip="" prop="" :rules="[]">{{ state.data.collectTime ? moment(state.data.collectTime, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</FormItem> -->
          <!-- <FormItem :span="8" label="持续时间" tooltip="" prop="" :rules="[]">{{ state.data.collectTime ? moment(state.data.collectTime, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</FormItem> -->
          <FormItem :span="6" :label="$t('generalDetails.Priority')" tooltip="" prop="" :rules="[]">
            <template #label>
              <span>
                <span v-if="priorityChanged" style="color: red">*</span>
                {{ $t("generalDetails.Priority") }}
              </span>
            </template>
            <el-dropdown trigger="click" @command="handleSetPriority($event, state.data)" :disabled="[changeState.AUTO_CLOSED, changeState.CLOSED].includes((state.data.changeState as changeState) || ('' as changeState))">
              <span class="el-dropdown-link">
                <i class="priority-icon" :style="{ backgroundColor: currentEventState.color || '' }" />
                <span class="tw-align-[2px]" :style="{ color: currentPriority.color || '' }">{{ state.data.priority }}</span>
                <el-icon class="el-icon--right"><ArrowDown></ArrowDown></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="(value, key) in priorityOption" :key="`priority-${key}`" :command="value.value">{{ value.label }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </FormItem>
          <FormItem :span="6" :label="$t('generalDetails.Urgency')" tooltip="" prop="" :rules="[]">
            <template #label>
              <span>
                <span v-if="urgencyChanged" style="color: red">*</span>
                {{ $t("generalDetails.Urgency") }}
              </span>
            </template>
            <el-select :model-value="state.data.urgency || ('' as string)" filterable @change="handleSetSeverity($event, state.data)" :disabled="[changeState.AUTO_CLOSED, changeState.CLOSED].includes((state.data.changeState as changeState) || ('' as changeState))">
              <el-option
                v-for="item in deviceImportanceOption
                  .map((v) => ({
                    ...v,
                    priority: priorityMatrix.filter((raw) => raw.eventSeverity === v.value),
                  }))
                  .filter((v) => v.priority.length)"
                :key="item.value"
                :label="`${item.label}`"
                :value="item.value"
              ></el-option>
              <!-- item.priority.map(v => `${v.priority}：${(find(deviceImportanceOption, (raw) => raw.value === v.deviceImportance) || {}).label || v.deviceImportance}`).join('，') -->
            </el-select>
          </FormItem>
          <FormItem :span="6" :label="$t('generalDetails.Impact')" tooltip="" prop="" :rules="[]">
            <template #label>
              <span>
                <span v-if="impactChanged" style="color: red">*</span>
                {{ $t("generalDetails.Impact") }}
              </span>
            </template>
            <el-select :model-value="state.data.influence || ('' as string)" filterable @change="handleSetImportance($event, state.data)" :disabled="[changeState.AUTO_CLOSED, changeState.CLOSED].includes((state.data.changeState as changeState) || ('' as changeState))">
              <el-option
                v-for="item in deviceImportanceOption.map((v) => ({
                  ...v,
                  priority: priorityMatrix.filter((raw) => raw.deviceImportance === v.value),
                }))"
                :key="item.value"
                :label="`${item.label}`"
                :value="item.value"
              ></el-option>
              <!-- item.priority.map(v => `${v.priority}：${(find(eventSeverityOption, (raw) => raw.value === v.eventSeverity) || {}).label || v.eventSeverity}`).join('，') -->
            </el-select>
          </FormItem>

          <!-- <el-col :span="24" class="tw-mb-[18px] tw-bg-[var(--el-bg-color-page)] tw-py-2">SLA情况</el-col>
        <FormItem :span="8" label="响应时限情况" tooltip="" prop="" :rules="[]">
          <el-progress :percentage="responseTimePercentage" :color="resolveUrgencyType.color" :format="() => `${state.data.responseTime || 0}分钟 / ${state.data.responseLimit || 0}分钟`" class="tw-w-full"></el-progress>
        </FormItem>
        <el-col :span="1"></el-col>
        <FormItem :span="8" label="解决时限情况" tooltip="" prop="" :rules="[]">
          <el-progress :percentage="resolveTimePercentage" :color="resolveUrgencyType.color" :format="() => `${state.data.resolveTime || 0}分钟 / ${state.data.resolveLimit || 0}分钟`" class="tw-w-full"></el-progress>
        </FormItem> -->
        </FormModel>
        <div></div>
        <!-- <pre>{{ state.data }}</pre> -->
      </template>
    </el-card>
    <completeCode v-if="[changeState.CLOSED, changeState.AUTO_CLOSED].includes((state.data.changeState as changeState) || ('' as changeState))" class="tw-mb-[18px]" :title="currentEventState.label || ''" :finishCodeName="state.data.code?.name || ''" :finishCodeDesc="state.data.code?.desc || ''" :finishContent="state.data.content || ''" />
    <el-card v-loading="state.loading">
      <template #header>
        <el-radio-group :model-value="(route.query.type as string) || ''" @change="router.push({ query: { ...route.query, type: $event as string } })">
          <el-radio-button :label="pageType.details"
            ><el-badge class="mark"
              ><div class="tw-px-[1em]">{{ $t("generalDetails.Details") }}</div></el-badge
            ></el-radio-button
          >
          <el-radio-button :label="pageType.subtotal">
            <el-badge class="mark" :value="Number(tabCounts.noteCount) || undefined">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Journals") }}</div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.approve"
            ><el-badge class="mark" :value="Number(tabCounts.approveCount) || undefined"
              ><div class="tw-px-[1em]">{{ $t("generalDetails.Examine and approve") }}</div></el-badge
            ></el-radio-button
          >
          <el-radio-button :label="pageType.device"
            ><el-badge class="mark" :value="Number(tabCounts.deviceCount) || undefined"
              ><div class="tw-px-[1em]">{{ $t("generalDetails.Devices") }}</div></el-badge
            ></el-radio-button
          >
          <el-radio-button :label="pageType.contacts"
            ><el-badge class="mark" :value="Number(tabCounts.contactCount) || undefined"
              ><div class="tw-px-[1em]">{{ $t("generalDetails.Contacts") }}</div></el-badge
            ></el-radio-button
          >
          <el-radio-button :label="pageType.project">
            <el-badge class="mark" :value="undefined">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Projects") }}</div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.related" :disabled="!userInfo.hasPermission(智能事件中心_变更工单_可读) && !userInfo.hasPermission(智能事件中心_工单_可读)"
            ><el-badge class="mark" :value="Number(tabCounts.relationCount) || undefined"
              ><div class="tw-px-[1em]">{{ $t("generalDetails.link") }}</div></el-badge
            ></el-radio-button
          >
          <el-radio-button :label="pageType.alarm">
            <el-badge class="mark">
              <div class="tw-flex tw-px-[1em]">
                <span>{{ $t("generalDetails.Alerts") }}</span>
                <template v-if="state.data.collectAlertEcho">
                  <span class="unconfirmed tw-mx-[5px]">{{ Number(tabCounts.unconfirmed) }}</span>
                  <span class="confirmed tw-mx-[5px]">{{ Number(tabCounts.confirmed) }}</span>
                </template>
                <FontAwesomeIcon v-else class="tw-mx-[5px]" :icon="faBan"></FontAwesomeIcon>
              </div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.sla"
            ><el-badge class="mark"
              ><div class="tw-px-[1em]">{{ $t("generalDetails.SLA") }}</div></el-badge
            ></el-radio-button
          >
          <el-radio-button :label="pageType.strategy"
            ><el-badge class="mark" :value="Number(tabCounts.actionCount) || undefined"
              ><div class="tw-px-[1em]">{{ $t("generalDetails.Support Notes") }}</div></el-badge
            ></el-radio-button
          >
          <el-radio-button :label="pageType.file"
            ><el-badge class="mark" :value="Number(tabCounts.fileCount) || undefined"
              ><div class="tw-px-[1em]">{{ $t("generalDetails.Files") }}</div></el-badge
            ></el-radio-button
          >
          <el-radio-button :label="pageType.dynamics"
            ><el-badge class="mark"
              ><div class="tw-px-[1em]">{{ $t("generalDetails.History Log") }}</div></el-badge
            ></el-radio-button
          >
        </el-radio-group>
      </template>
      <template #default>
        <el-scrollbar>
          <div v-if="((route.query.type as string) || '') === pageType.details">
            <!-- 详述 -->
            <ModelDetails
              :data="state.data"
              @change-date="
                (v) => {
                  state.data.startTime = v.startTime;
                  state.data.endTime = v.endTime;
                }
              "
              @change-desc="(v) => (state.data.desc = v)"
              :refresh="handleRefresh"
              :height="0"
            ></ModelDetails>
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.subtotal">
            <!-- 小记 -->
            <ModelNotes ref="modelNotesRef" :data="state.data" :refresh="handleRefresh" :height="0"></ModelNotes>
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.approve">
            <!-- 审批 -->
            <ModelApprove :data="state.data" :refresh="handleRefresh" :height="0"></ModelApprove>
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.device">
            <!-- 设备 -->
            <ModelDevices :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.contacts">
            <!-- 联系人 -->
            <ModelContacts :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.project">
            <ModelProject :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.related">
            <!-- 关联 -->
            <ModelAssociation :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.alarm">
            <!-- 告警记录 -->
            <ModelAlarm
              :data="state.data"
              :refresh="handleRefresh"
              :handleEnd="handleAlarm"
              :height="0"
              @confrim="
                (v) => {
                  collectAlert = v.collectAlert;
                }
              "
            />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.sla">
            <!-- SLA -->
            <ModelSLA :data="state.data" :refresh="handleRefresh" :height="0"></ModelSLA>
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.strategy">
            <!-- 行动策略 -->
            <ModelStrategy :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.file">
            <!-- 文件 -->
            <ModelFiles :data="state.data as DataItem" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.dynamics">
            <!-- 动态 -->
            <ModelDynamics :data="state.data" :refresh="handleRefresh" :height="0"></ModelDynamics>
          </div>
        </el-scrollbar>
      </template>
    </el-card>
  </el-scrollbar>
  <!-- <EventPend ref="pendRef" :refresh="handleRefresh"></EventPend> -->

  <EventEnd
    ref="endRef"
    :refresh="handleRefresh"
    :ticketTemplateId="state.data.ticketTemplateId"
    @end="
      (v) => {
        // console.log(v);
        completeQuery = v.params;
        notesQuery = v.notesForm;
        operation = v.type;
      }
    "
    :height="height - 317"
  ></EventEnd>

  <Editor ref="editorRef" :title="$t('generalDetails.Change')" display="dialog">
    <template #setPriority="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          {{ $t("generalDetails.Confirm settings") }}
          <span>{{ ((params.raw || {}) as AnyObject).summary as string }}</span>
          {{ $t("generalDetails.Change the urgency to") }}
          <span>{{ params.label }}</span>
          {{ $t("generalDetails.Is that okay") }}
        </p>
      </div>
    </template>
    <template #setImportance="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          {{ $t("generalDetails.Confirm settings") }}
          <span>{{ ((params.raw || {}) as AnyObject).summary as string }}</span>
          {{ $t("generalDetails.The importance of the event is") }}
          <span>{{ params.label }}</span>
          {{ $t("generalDetails.Is that okay") }}
        </p>
      </div>
    </template>
    <template #setSeverity="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          {{ $t("generalDetails.Confirm settings") }}
          <span>{{ ((params.raw || {}) as AnyObject).summary as string }}</span>
          {{ $t("generalDetails.The urgency of the event is") }}
          <span>{{ params.label }}</span>
          {{ $t("generalDetails.Is that okay?") }}
        </p>
      </div>
    </template>
  </Editor>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch, computed, h, provide, toRefs } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Refresh, InfoFilled, ArrowDown, CircleClose } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";
import { ElTable, ElIcon } from "element-plus";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import ModelDetails from "./models/details.vue";
import ModelSubtotal from "./models/subtotal.vue";
import ModelSLA from "./models/sla.vue";
import ModelDynamics from "./models/dynamics.vue";

import ModelNotes from "./models/notes.vue";

import ModelDevices from "./models/devices.vue";

import ModelContacts from "./models/contacts.vue";

import ModelProject from "@/views/pages/alarm_convergence/details/eventDetail/models/project.vue";

import ModelAssociation from "./models/association.vue";

import ModelFiles from "./models/files.vue";

import ModelAlarm from "./models/alarm.vue";

import ModelStrategy from "./models/strategy.vue";

// import EventPend from "./models/pend.vue";

import EventEnd from "./models/end.vue";

import ModelApprove from "./models/approve.vue";

import Editor from "./Editor.vue";

import completeCode from "@/components/completeCode.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox } from "element-plus";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { useSiteConfig } from "@/stores/siteConfig";
import getUserInfo from "@/utils/getUserInfo";
import { find } from "lodash-es";
import moment from "moment";

import { type BaseItem, DataItem, type Item } from "./helper";
import { state } from "./helper";
import { resetData, command } from "./helper";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
// import { changeState, changeStateOption, eventSeverity, eventSeverityOption, priority, priorityOption } from "@/views/pages/apis/question";
import { changeOperate, changeState, changeStateOption, eventSeverity, eventSeverityOption, priority, priorityOption, changeStateDispose as dispose, changeStateWithdraw, changeStateAbandon, changeStateComplete, changeStateClose, changeUser, changeUserGroup, getChangeDetailById as getItemData, setChangePriority, setChangeImportance, setChangeSeverity, setChangeDigest as editDigest, changeOperateOption, setChangeEditable, getChangeTabCount, type ChangeTabCount } from "@/views/pages/apis/change";
import { deviceImportance, deviceImportanceOption } from "@/views/pages/apis/device";

// import { getEventData as getItemData } from "@/views/pages/apis/event";

import { /* setChangePriority, setChangeImportance, setChangeSeverity, setChangeStateProcessing,*/ questionTransfer } from "@/views/pages/apis/question";

import { addEventData as addItemData, setEventData as setItemData, modEventData as modItemData, delEventData as delItemData } from "@/views/pages/apis/event";
import { setEventDataByTakeOver, setEventDataByApprove, setEventDataByTransferOrUpgrade, type TimeLimit } from "@/views/pages/apis/event";
import { getPriorityMatrixList } from "@/views/pages/apis/eventPriority";
import { getGroupList, getUserByGroup, type GroupItem, type EntrustUserItem } from "@/api/personnel";

import { faBan } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { 智能事件中心_变更工单_可读, 智能事件中心_工单_可读, 智能事件中心_变更工单_更新, 智能事件中心_工单_更新 } from "@/views/pages/permission";
import { localesOption } from "@/api/locale.ts";
import { getTenantInfo } from "@/views/pages/apis/tenant";
import { getContactTypes as getType, type ContactsTypeItem, type ContactsItem } from "@/views/pages/apis/contacts";
import { eventBatchContact as getData, eventBatchdesensitized, eventAddContact as addData, eventDelContact as delData, getChangeContacts } from "@/views/pages/apis/eventManage";

import { getCloseDirectly, getOrderUserGroup, AssignableTicketType, UserGroupConfigurationItem, getOrderUserGroupIsClose, getTicketClassificationNames } from "@/views/pages/apis/orderGroup";
import { 智能事件中心_服务请求工单_编辑小记, 智能事件中心_事件工单_编辑小记, 智能事件中心_DICT事件管理_编辑小记, 智能事件中心_DICT服务请求_编辑小记, 智能事件中心_变更工单_编辑小记, 智能事件中心_问题工单_编辑小记, 智能事件中心_发布管理_编辑小记 } from "@/views/pages/permission";
import { addOrderNode } from "@/views/pages/apis/event";
/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const contactsType = ref<{ contactId: string; contactType: string }[]>([]);
const contacts = ref<ContactsItem[]>([]);
const uniqueByLanguage = ref<ContactsItem[]>([]);
const sortedLocalesOption = ref<ContactsItem[]>(localesOption);
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}

const modelNotesRef = ref();
const { t } = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "alarmBoard" });
const siteConfig = useSiteConfig();
const userInfo = getUserInfo();
provide("detailData", toRefs(state).data);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
interface Props {
  width?: number;
  height?: number;
  title?: string;
}

const props = withDefaults(defineProps<Props>(), { title: "告警" });

const stateRightText = computed(() => {
  switch (state.data.changeState) {
    case changeState.SUSPEND:
      const currentTime = new Date().getTime();
      const data = JSON.parse(sessionStorage.getItem("changeDetail") || "{}");
      if (!data.approval) return "已审批";
      if (data.approveState === "UN_APPROVE") return "未审批";
      else if (data.startTime && data.endTime && Number(data.startTime) < currentTime && Number(data.endTime) > currentTime) return "开始";
      else if (data.endTime && Number(data.endTime) < currentTime) return "结束";
      else return "已审批";
    case changeState.PROCESSING:
      if (state.data.approveState === "UN_APPROVE") return "未审批";
      else return "处理";
    case changeState.AUTO_CLOSED:
    case changeState.CLOSED:
      return (changeOperateOption.find((v) => v.value === state.data.operation) || {}).label;
    default:
      return "";
  }
});

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

const _width = inject("width", ref(0));
const _height = inject("height", ref(0));

const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);

const tableRef = ref<InstanceType<typeof ElTable>>();
const editorRef = ref<InstanceType<typeof Editor>>();

const verifyPermissionIds = ref<string[]>([]);
provide("verifyPermissionIds", verifyPermissionIds);

/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Form {
  priority: string;
}
interface AnyObject {
  [key: string]: any;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const timer = ref<null | NodeJS.Timer>(null);
const autoRefreshTime = ref(0);

enum pageType {
  details = "",
  subtotal = "subtotal",
  device = "device",

  contacts = "contacts",
  related = "related",
  alarm = "alarm",
  sla = "sla",
  strategy = "strategy",
  file = "file",
  dynamics = "dynamics",
  approve = "approve",
  project = "project",
}

const currentEventState = computed(() => find(changeStateOption, (v) => v.value === state.data.changeState) || ({} as Partial<(typeof changeStateOption)[number]>));
const currentPriority = computed(() => find(priorityOption, (v) => v.value === state.data.priority) || ({} as Partial<(typeof priorityOption)[number]>));

// const responseUrgencyType = computed(() => setSlaState((state.data.slaTimeLimit || {}).responseTimeLimits || [], Number(state.data.responseTime) || 0));
// const resolveUrgencyType = computed(() => setSlaState((state.data.slaTimeLimit || {}).completedTimeLimits || [], Number(state.data.resolveTime) || 0));

// function setSlaState(list: TimeLimit[], val: number) {
//   let result = urgencyType.BREACH;
//   list.sort((a, b) => (Number(b.tolerateMinutes) || 0) - (Number(a.tolerateMinutes) || 0)).forEach((el) => ((Number(el.tolerateMinutes) || 0) >= val || list.length === 1) && (result = el.urgencyType));
//   return find(urgencyTypeOption, ({ value }) => value === result) || { label: "致命", value: "BREACH", color: "#ED4013" };
// }

const eventStat = ref<{ label: string; value: changeState; color?: string; count: number }[]>([]);

const form = reactive<Form>({
  priority: "",
});

// const responseTimePercentage = computed(() => (((Number(state.data.responseTime) || 0) / (Number(state.data.responseLimit) || 0)) * 100 > 100 ? 100 : (Number(state.data.responseTime) / Number(state.data.responseLimit)) * 100) || 0);
// const resolveTimePercentage = computed(() => (((Number(state.data.resolveTime) || 0) / (Number(state.data.resolveLimit) || 0)) * 100 > 100 ? 100 : (Number(state.data.resolveTime) / Number(state.data.resolveLimit)) * 100) || 0);

const priorityMatrix = ref<{ eventSeverity: eventSeverity; deviceImportance: deviceImportance; priority: priority }[]>([]);
const userGroups = ref<Record<string, any>[]>([]);
const userList = ref<EntrustUserItem[]>([]);
const collectAlert = ref(true);
const typeOfTransferOrUpgrade = ref<"transfer" | "eventUpgrade">("transfer");
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  handleRefresh().then(() => (autoRefreshTime.value = 60));
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {
  if (timer.value !== null) {
    clearInterval(timer.value);
    timer.value = null;
  }
}
function beforeDestroy() {
  if (timer.value !== null) {
    clearInterval(timer.value);
    timer.value = null;
  }
}
function destroyed() {}

watch(autoRefreshTime, (autoRefreshTime) => {
  if (timer.value !== null) timer.value = (clearInterval(timer.value), null);
  if (autoRefreshTime) timer.value = setInterval(queryData, autoRefreshTime * 1000);
});

type typeTransferOrUpgrade = { userGroupId: string; userId: string };
const transferOrUpgrade = ref<typeTransferOrUpgrade>({} as typeTransferOrUpgrade);

const operation = ref<changeOperate>("" as changeOperate);

type CompleteQuery = { code: { name: string; desc: string }; content: string; closeAlert: boolean };
type NotesQuery = { content: string; privateAble: boolean };
const notesQuery = ref<NotesQuery>({} as NotesQuery);
const completeQuery = ref<CompleteQuery>({} as CompleteQuery);
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

async function handleChangeSave() {
  try {
    const params = {
      id: route.params.id,
      operation: operation.value || null,
      digest: state.data.digest,
      priority: state.data.priority,
      urgency: state.data.urgency,
      influence: state.data.influence,
      teamId: state.data.teamId,
      userId: state.data.userId,
      desc: state.data.desc,
      externalId: state.data.externalId,
      startTime: state.data.startTime || null,
      endTime: state.data.endTime || null,
      ...completeQuery.value,
      collectAlertClose: collectAlert.value,
    };
    // // console.log(params);
    const { success, message } = await setChangeEditable(params);
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    if (hasValidContent(notesQuery.value.content)) {
      submitNote();
    }
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    await handleRefresh();
  }
}
const hasValidContent = (html) => {
  if (html === undefined || html === null) {
    return false;
  }
  // 1. 过滤空标签（包括自闭合标签）
  const cleanedHtml = html.replace(/<([a-z][a-z0-9]*)\b[^>]*>(?:\s|&nbsp;)*<\/\1>|<\w+\s*\/>/gi, "");
  // 2. 移除所有空格（包括换行、制表符等）
  const trimmedContent = cleanedHtml.replace(/\s+/g, "").trim();
  // 3. 返回是否有有效内容
  return trimmedContent.length > 0;
};
async function submitNote() {
  try {
    const formData = new FormData();
    formData.append("nodeContent", notesQuery.value.content);
    formData.append("privateAble", notesQuery.value.privateAble as any);
    formData.append("privateCustomerId", userInfo.tenantId);
    formData.append("tenantId", (userInfo.currentTenant || {}).id as string);
    formData.append("orderType", "CHANGE");
    formData.append("permissionId", EditNodePermissionId["CHANGE"]);
    formData.append("orderId", route.params.id as string);
    formData.append("orderIdsJson", JSON.stringify([route.params.id]));
    const { success, message } = await addOrderNode(formData as any);
    if (!success) throw new Error(message);
    //ElMessage.success("操作成功");
    if (modelNotesRef.value) {
      modelNotesRef.value.getEventNotes(); // 安全调用
    }
    handleRefresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    await queryData();
  }
}
enum EditNodePermissionId {
  EVENT_ORDER = 智能事件中心_事件工单_编辑小记,
  SERVICE_REQUEST = 智能事件中心_服务请求工单_编辑小记,
  DICT_EVENT_ORDER = 智能事件中心_DICT事件管理_编辑小记,
  DICT_SERVICE_REQUEST = 智能事件中心_DICT服务请求_编辑小记,
  CHANGE = "612915766462775296", //智能事件中心_变更工单_编辑小记 and 智能事件中心_小记_新增,
  QUESTION = 智能事件中心_问题工单_编辑小记,
  PUBLISH = 智能事件中心_发布管理_编辑小记,
}
function handleEventOperateCommand(v: changeState) {
  switch (v) {
    case changeState.PROCESSING:
      // 处理中
      // handleBatchAccept(detailData);
      break;
    // case changeState.COMPLETED:
    //   // 完成
    //   // handleEventEnd(detailData, "Finish");
    //   break;
    case changeState.CLOSED:
      // 关闭
      // handleEventEnd(detailData, "Close");
      break;
  }
}
const priorityChanged = ref(false);
async function handleSetPriority(priority: priority, raw: Partial<DataItem>) {
  priorityChanged.value = true;
  // const priorityItem = (find(priorityOption, ({ value }) => value === priority) || {}).label || priority;
  // (0 , _views_pages_apis_serviceRequest__WEBPACK_IMPORTED_MODULE_22__.setChangePriority) is not a function
  // if (!editorRef.value) return;
  // const time = autoRefreshTime.value;
  try {
    state.data.priority = priority;
    // const { success, message, data } = await setChangePriority({ id: raw.id as string, priority });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功修改优先级`);
    // autoRefreshTime.value = 0;
    // state.loading = true;

    // const params = { priority, label: priorityItem, raw };
    // const form = {};
    // await editorRef.value.confirm({ ...form, ...params, $type: "warning", $title: `修改优先级`, $slot: "setPriority" }, async (form: Record<string, unknown>) => {

    // });
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    // state.loading = false;
  }
}
const impactChanged = ref(false);
async function handleSetImportance(importance: deviceImportance, raw: Partial<DataItem>) {
  impactChanged.value = true;
  // const importanceItem = (find(deviceImportanceOption, ({ value }) => value === importance) || {}).label || importance;

  // if (!editorRef.value) return;
  // const time = autoRefreshTime.value;
  try {
    state.data.influence = importance;
    setPriority();
    // const { success, message, data } = await setChangeImportance({ id: raw.id as string, importance });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功修改重要性`);
    // autoRefreshTime.value = 0;
    // state.loading = true;

    // const params = { importance, label: importanceItem, raw };
    // const form = {};
    // await editorRef.value.confirm({ ...form, ...params, $type: "warning", $title: `修改重要性`, $slot: "setImportance" }, async (form: Record<string, unknown>) => {

    // });
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // // autoRefreshTime.value = time;
    // state.loading = false;
  }
}
// 在组件中声明响应式变量
const urgencyChanged = ref(false);

async function handleSetSeverity(severity: eventSeverity, raw: Partial<DataItem>) {
  // 设置变更标志
  urgencyChanged.value = true;
  // const severityItem = (find(eventSeverityOption, ({ value }) => value === severity) || {}).label || severity;

  // if (!editorRef.value) return;
  // const time = autoRefreshTime.value;
  try {
    state.data.urgency = severity;
    setPriority();
    // const { success, message, data } = await setChangeSeverity({ id: raw.id as string, severity });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功修改紧急性`);
    // autoRefreshTime.value = 0;
    // state.loading = true;

    // const params = { severity, label: severityItem, raw };
    // const form = {};
    // await editorRef.value.confirm({ ...form, ...params, $type: "warning", $title: `修改紧急性`, $slot: "setSeverity" }, async (form: Record<string, unknown>) => {

    // });
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    // state.loading = false;
  }
}

function setPriority() {
  if (state.data.urgency && state.data.influence) state.data.priority = (find(priorityMatrix.value, (v) => v.eventSeverity === state.data.urgency && v.deviceImportance === state.data.influence) || {}).priority || state.data.priority;
}

async function handleDispose(row: DataItem) {
  try {
    operation.value = changeOperate.DISPOSE;
    OperateChanged.value = true;
    // const { success, data, message } = await dispose({ id: row.id });
    // if (!success) throw new Error(message);
    // ElMessage.success("操作成功");
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    // handleRefresh();
  }
}

async function getContact() {
  const id = state.data.id || route.params.id;
  try {
    const [{ success: contactSuccess, message: contactMessage, data: contactData }, { success: tabsSuccess, message: tabsMessage, data: tabsData }] = await Promise.all([getChangeContacts({ id }), getType({})]);
    if (!contactSuccess) throw Object.assign(new Error(contactMessage), { success: contactSuccess, data: contactData });
    contactsType.value = contactData instanceof Array ? contactData : [];
    if (contactSuccess) {
      const { success, message, data } = await eventBatchdesensitized({ deviceIds: Array.from(contactsType.value.reduce((p, c) => p.add(c.contactId), new Set<string>()).values()) });
      if (!success) throw Object.assign(new Error(message), { success, data });
      contacts.value = data instanceof Array ? data : [];
      const { success: successTenant, message: messageTenant, data: dataTenant } = await getTenantInfo({});
      if (!successTenant) throw Object.assign(new Error(messageTenant), { success: successTenant, dataTenant });
      contacts.value.unshift(dataTenant);
      // 去重方法
      uniqueByLanguage.value = contacts.value.reduce((acc, current) => {
        // 查找当前数组中是否已存在相同 language 的项
        const existingIndex = acc.findIndex((item) => item.language === current.language);

        if (existingIndex === -1) {
          // 如果不存在，直接添加当前项
          acc.push(current);
        } else {
          // 如果已存在，判断当前项是否更优（tenantName 为假）
          const existingItem = acc[existingIndex];
          if (!current.tenantName && existingItem.tenantName) {
            // 替换为更优的当前项（tenantName 为假）
            acc.splice(existingIndex, 1, current);
          }
        }
        return acc;
      }, []);
      // 提取 uniqueByLanguage 中的语言顺序
      const languageOrder = uniqueByLanguage.value.map((item) => item.language);

      // 根据语言顺序对 localesOption 进行排序
      sortedLocalesOption.value.sort((a, b) => {
        const indexA = languageOrder.indexOf(a.value);
        const indexB = languageOrder.indexOf(b.value);
        return indexA - indexB;
      });
    }
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : `${error}`);
  }
}

function isLanguageMatch(value) {
  return uniqueByLanguage.value.some((item) => item.language === value);
}
// 新增方法
function getTooltipContent(language, zhLabel) {
  const matchedItem = uniqueByLanguage.value.find((item) => item.language === language);
  return matchedItem?.tenantName ? `联系人: ${zhLabel}` : `客户: ${zhLabel}`;
}
async function handleRefresh() {
  try {
    state.loading = true;
    await nextTick();
    await resetData();
    await queryData();
    await getTabCount();
    await getContact();
    await resetChangeState();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
const tabCounts = ref<ChangeTabCount>({} as ChangeTabCount);
async function getTabCount() {
  try {
    const { success, data, message } = await getChangeTabCount({ id: route.params.id as string });
    if (!success) throw new Error(message);
    tabCounts.value = data;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}
async function handleQuery() {
  try {
    state.loading = true;
    await nextTick();
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}

const tickGroupConfig = ref({
  closeDirectly: false,
  isClose: false,
  ticketClassificationNames: [],
});

const orderIsClose = computed(() => {
  if (["APPROVED"].includes(state.data.approveState as string) && ![changeOperate.ABANDON].includes(state.data.operation as changeOperate) && ![changeState.CLOSED].includes(state.data.changeState as changeState)) {
    if (tickGroupConfig.value.closeDirectly && tickGroupConfig.value.isClose) return true;
    else if ([changeOperate.COMPLETE].includes((state.data.operation as changeOperate) || ("" as changeOperate)) && tickGroupConfig.value.isClose) return true;
    else return false;
  } else return false;
});
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

async function getTackerGroupConfig() {
  try {
    const [
      /* 是否直接关闭 */
      { data: closeDirectlyData, message: closeDirectlyMessage, success: closeDirectlySuccess },
      { data: isCloseData, message: isCloseMessage, success: isCloseSuccess },
      ticketClassificationNames,
    ] = await Promise.all([
      /* 获取是否直接关闭 */
      getCloseDirectly({ tenantId: (userInfo.currentTenant || {}).id, ticketTemplateId: (state.data as any).ticketTemplateId }),
      getOrderUserGroupIsClose({ tenantId: (userInfo.currentTenant || {}).id, type: AssignableTicketType.event, userId: userInfo.userId, ticketTemplateId: (state.data as any).ticketTemplateId }),
      getTicketClassificationNames(AssignableTicketType.change, (state.data as any).ticketClassificationId, (state.data as any).ticketTemplateId),
    ]);
    if (!closeDirectlySuccess) throw new Error(closeDirectlyMessage);
    if (!isCloseSuccess) throw new Error(isCloseMessage);
    tickGroupConfig.value.closeDirectly = closeDirectlyData;
    tickGroupConfig.value.isClose = isCloseData;
    tickGroupConfig.value.ticketClassificationNames = ticketClassificationNames;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}
async function queryData() {
  try {
    const [
      /*  */
      { success, message, data },
      { success: prioritySuccess, message: priorityMessage, data: priorityData },
    ] = await Promise.all([
      /*  */
      getItemData({ id: route.params.id as string }),
      getPriorityMatrixList({}),
      /* getUserGroupByPermissionIds({ queryPermissionId: 安全管理中心_用户组管理_可读, verifyPermissionIds: [智能事件中心_用户组_分配工单] }) */ /* ({ appId: (siteConfig.baseInfo || {}).app, external: false, ownToLoginUser: true }) */

      // getGroupList({ appId: (siteConfig.baseInfo || {}).app, external: true }),
      ,
    ]);
    if (!success) throw Object.assign(new Error(message), { success, data });
    if (!prioritySuccess) throw Object.assign(new Error(priorityMessage), { success: prioritySuccess, data: priorityData });
    const { success: userGroupSuccess, message: userGroupMessage, data: userGroupData } = await getOrderUserGroup({ tenantId: (userInfo.currentTenant || {}).id, type: AssignableTicketType.change, ticketTemplateId: (data as any).ticketTemplateId });
    if (!userGroupSuccess) throw Object.assign(new Error(userGroupMessage), { success: userGroupSuccess, data: userGroupData });

    await (async (req: { userGroup: string }) => {
      try {
        if (!req.userGroup) return;
        const { success, message, data: res } = await getUserByGroup({ id: req.userGroup });
        if (!success) throw Object.assign(new Error(message), { success, data: res });
        userList.value = res;
      } catch (error) {
        if (error instanceof Error) ElMessage.error(message);
      }
    })({ userGroup: data.teamId });

    priorityMatrix.value = priorityData.priorityMatrixItems.map((v) => {
      return {
        eventSeverity: v.urgency,
        deviceImportance: v.influence,
        priority: v.priority,
      };
    });
    // userGroups.value = userGroupData;
    userGroups.value = userGroupData.map((v) => ({ id: v.userGrouptId, name: v.userGroupName, tenantAbbreviation: v.abbreviation }));

    state.data = data;
    sessionStorage.setItem("changeDetail", JSON.stringify(state.data));
    verifyPermissionIds.value = data.verifyPermissionIds || [];

    getTackerGroupConfig();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  }
}
async function createItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await addItemData({ ...form });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`成功确认告警`);
  });
}
// async function previewItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setItemData({ ids: form.ids as string[], ...form });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
// async function rewriteItem(row: Record<string, unknown>) {
//   const title = (find(priorityOption, ({ value }) => value === row.priority) || {}).label || row.priority;
//   if (!editorRef.value) return row;
//   const params = { select: (<Item[]>row.select).filter((v) => row.priority !== v.priority) };
//   await editorRef.value.confirm({ ...row, ...params, $type: "warning", $title: `批量${title}`, $slot: "batchConfirm" }, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setEventDataByPriority({ id: (<Item[]>form.select).map((v) => v.id), priority: form.priority as priority });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`成功${form.$title}`);
//   });
// }
// async function modifyItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setItemData({ ids: form.ids as string[], ...form });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
// async function deleteItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await delItemData(form);
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
function handleCancel() {
  if ("fallback" in route.query && typeof route.query.fallback === "string") router.push({ name: route.query.fallback, params: { id: route.query.deviceId }, query: { type: route.query.status } });
  else if (history.length === 1) window.close();
  else router.back();
}

async function handleAccept(row: Partial<DataItem>) /* 接手 */ {
  try {
    state.loading = true;
    // serviceRequestId: string | number;
    // tenantId: string | number;
    // const { success, message, data } = await setchangeStateProcessing({ id: row.id as string });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`操作成功`);
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    await queryData();
    state.loading = false;
  }
}
const digestChanged = ref(false);
async function handleEditSummary() {
  ElMessageBox.prompt("请输入摘要", "编辑摘要", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputValue: state.data.digest,
    inputValidator: (value: string) => {
      return !!value;
    },
    inputErrorMessage: "请输入摘要",
    beforeClose: async (action, instance, done) => {
      try {
        if (action === "confirm") {
          digestChanged.value = true;
          state.data.digest = instance.inputValue;
          // const { success, data, message } = await editDigest({ id: route.params.id as string, digest: instance.inputValue });
          // if (!success) throw new Error(message);
          // ElMessage.success("操作成功");
          // handleRefresh();
          done();
        } else done();
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    },
  })
    .then(() => {
      /* code */
    })
    .catch(() => {
      /* code */
    });
}
const endRef = ref<InstanceType<typeof EventEnd>>();
const OperateChanged = ref(false);
async function handleClose(row: DataItem, operateType: changeOperate, api: any) {
  try {
    if (!endRef.value) return false;
    endRef.value.open(row, operateType, api);
    OperateChanged.value = true;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    // handleRefresh();
  }
}
async function handleAlarm(row: DataItem, operateType: changeOperate, api: any) {
  try {
    if (!endRef.value) return false;
    endRef.value.open(row, operateType, api);
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    // handleRefresh();
  }
}
const userGroupChanged = ref(false);
async function handleChangeUserGroup(id: string) {
  try {
    // const { success, data, message } = await changeUserGroup({ id: route.params.id as string, teamId: id });
    // if (!success) throw new Error(message);
    state.data.teamId = id;
    userGroupChanged.value = true;
    await (async (req: { userGroup: string }) => {
      try {
        if (!req.userGroup) return;
        const { success, message, data: res } = await getUserByGroup({ id: req.userGroup });
        if (!success) throw Object.assign(new Error(message), { success, data: res });
        userList.value = res;
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      }
    })({ userGroup: id });
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    // handleRefresh();
  }
}
const userChanged = ref(false);
async function handleChangeUser(id: string) {
  try {
    // const { success, data, message } = await changeUser({ id: route.params.id as string, userId: id });
    // if (!success) throw new Error(message);
    state.data.userId = id;
    userChanged.value = true;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    // handleRefresh();
  }
}
async function resetChangeState() {
  priorityChanged.value = false;
  impactChanged.value = false;
  urgencyChanged.value = false;
  OperateChanged.value = false;
  digestChanged.value = false;
  userGroupChanged.value = false;
  userChanged.value = false;
}

// const endRef = ref<InstanceType<typeof EventEnd>>();
// async function handleEnd(data: Partial<DataItem>, type: string) {
//   if (!endRef.value) return false;
//   endRef.value.open(data, type);
// }

// const pendRef = ref<InstanceType<typeof EventPend>>();
// async function handleApprove(row: Partial<DataItem>) /* 挂起 */ {
//   if (!pendRef.value) return false;
//   pendRef.value.open(state.data);
// }
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
defineSlots<{}>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var.scss";

.el-radio-button.is-active {
  .unconfirmed {
    color: $color-white;
  }
  .confirmed {
    color: $color-white;
  }
}

.el-radio-button {
  .unconfirmed {
    color: $color-danger;
  }
  .confirmed {
    color: $color-black;
  }
}

.event {
  :deep(.draft-tooltip) {
    background: var($color-danger) !important;
  }
}
</style>

<style lang="scss">
@import "@/styles/theme/common/var.scss";
.draft-tooltip {
  background: $color-danger !important;
  border: 1px solid $color-danger;
  color: #fff;
}

.draft-tooltip .el-popper__arrow::before {
  background: $color-danger !important;
  border: 1px solid $color-danger;
}
</style>
