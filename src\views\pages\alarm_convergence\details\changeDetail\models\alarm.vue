<template>
  <page-template v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 10" :show-paging="true" @size-change="handleRefresh()" @current-change="handleRefresh()">
    <template #right>
      <el-form :model="{}" :inline="true">
        <el-form-item :label="$t('generalDetails.Collect alarms')">
          <el-switch v-model="collectAlert" :disabled="!data.collectAlertEcho || [changeState.UN_APPROVED].includes(data.changeState)" @change="handleCollectAlertChange"></el-switch>
          <el-tooltip class="item" effect="dark" :content="$t('generalDetails.This operation cannot be undone')" placement="top" style="vertical-align: middle">
            <el-icon class="tipIcon" :style="{ marginLeft: '5px' }"><InfoFilled /></el-icon>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-dropdown split-button type="primary" trigger="click" @click="handleCommand" @command="handleCommand" :disabled="!userInfo.hasPermission(资产管理中心_设备_确认告警) || [changeState.UN_APPROVED].includes(data.changeState) || (tableData instanceof Array && !tableData.length) || [changeState.AUTO_CLOSED].includes(data.changeState) || [changeState.CLOSED].includes(data.changeState)">
            {{ $t("generalDetails.Confirm") }}
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="selectAll">{{ $t("generalDetails.Select All (current page)") }}</el-dropdown-item>
                <el-dropdown-item command="closeSelect">{{ $t("generalDetails.Clear reselection (current page)") }}</el-dropdown-item>
                <el-dropdown-item command="confirmAll">{{ $t("generalDetails.Confirm all (all pages)") }}</el-dropdown-item>
                <el-dropdown-item command="confirmAllAndComplete">{{ $t("generalDetails.Confirm all and complete (all pages)") }}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-form-item>
        <el-form-item>
          <el-button :icon="RefreshIcon" @click="handleRefresh"></el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #default>
      <el-table stripe ref="multipleTable" :data="tableData" style="width: 100%" :height="height - 100" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="left" :selectable="selectable" />
        <el-table-column prop="eventSeverity" :label="$t('generalDetails.Urgency')" align="left" width="160">
          <template #default="{ row }">
            <div>
              <!-- {{ eventSeverityOption }} -->
              <div v-for="item in eventSeverityOption" :key="item.label">
                <span class="alarmUrgency" v-if="item.value === row.eventSeverity" :style="'background:' + item.color">
                  {{ row.eventSeverity }}
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- <TableColumn type="enum" prop="eventSeverity" label="紧急性" :width="160" :filters="eventSeverityOption.map((v) => ({ ...v, text: v.label }))"></TableColumn> -->
        <el-table-column prop="name" :label="$t('generalDetails.Devices')" align="left" :formatter="tableFormatter" width="180">
          <template #default="{ row }">
            <div v-if="row?.device">
              <p class="device-name">
                <!-- <router-link :to="{ name: 'deviceManageDetails', query: { ...$route.query, backUrl: $route.fullPath }, params: { id: row.deviceId } }">
                  <template #default="{ href }">
                    <el-link type="primary" :href="href" :underline="false">{{ row.device.name }}</el-link>
                  </template>
                </router-link>

                <el-link v-else type="primary" :href="`${$pinia.state.value?.site?.baseInfo?.path}/alarm_convergence/deviceManage/details/${row.device.id}?backUrl=${$route.path}`" :underline="false">{{ row.device.name }}</el-link> -->

                <el-button link type="primary" @click="() => $router.push(`/event/property_manage/deviceManage/details/${row.device.id}?backUrl=${$route.path}&tenant=${row.tenantId}`)">{{ row.device.name }}</el-button>
              </p>
              <p class="device-ip">{{ row.device?.config?.ipAddress || "--" }}</p>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('generalDetails.Tool')" width="180" align="left" :formatter="tableFormatter">
          <template #default="{ row }">
            <!-- <div style="cursor: pointer">
              <Icon class="tw-mx-[2px]" name="local-DeviceMac-line" color="var(--el-color-primary)"></Icon>
              <Icon class="tw-mx-[2px]" name="local-DeviceWifi-line" color="var(--el-color-primary)" ></Icon>
              <Icon class="tw-mx-[2px]" name="local-SystemShare-line" color="var(--el-color-primary)" @click="routeDevice(row)"></Icon>
              <Icon class="tw-mx-[2px]" name="local-DocumentNumbers-line" color="var(--el-color-primary)"></Icon>
              <Icon class="tw-mx-[2px]" name="local-SystemError-warning-line" color="var(--el-color-primary)" @click="preview(row.id)"></Icon>
              <Icon class="tw-mx-[2px]" name="local-UserUser-3-line" color="var(--el-color-primary)" @click="contancts(row.id)"></Icon>

            </div> -->
            <div style="cursor: pointer; display: flex">
              <span class="tw-h-fit">
                <el-button type="primary" link>
                  <deviceTools :item="row" :list="deskTopObj.length" :show="row.showDesktop" :active="row.active && (row.allowTypes || []).length"></deviceTools>
                </el-button>
              </span>
              <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission('611865118543708160')">
                <span class="tw-h-fit">
                  <el-button :disabled="!userInfo.hasPermission('611865118543708160')" type="primary" link @click="ping(row)">
                    <Icon class="tw-mx-[2px]" name="local-DeviceWifi-line" color="var(--el-color-primary)"></Icon>
                  </el-button>
                </span>
              </el-tooltip>
              <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission('611865118543708160')">
                <span class="tw-h-fit">
                  <el-button :disabled="!userInfo.hasPermission('611865118543708160')" type="primary" link @click="routeDevice(row)">
                    <Icon class="tw-mx-[2px]" name="local-SystemShare-line" color="var(--el-color-primary)"></Icon>
                  </el-button>
                </span>
              </el-tooltip>
              <span class="tw-h-fit">
                <el-button type="primary" link @click="routerV6Busines(row.deviceName)">
                  <Icon class="tw-mx-[2px]" name="local-DocumentNumbers-line" color="var(--el-color-primary)"></Icon>
                </el-button>
              </span>

              <span class="tw-h-fit">
                <el-button type="primary" link @click="preview(row.deviceId)">
                  <Icon class="tw-mx-[2px]" name="local-SystemError-warning-line" color="var(--el-color-primary)"></Icon>
                </el-button>
              </span>

              <span class="tw-h-fit">
                <el-button type="primary" link @click="contancts(row.deviceId)">
                  <Icon :disabled="true" class="tw-mx-[2px]" name="local-UserUser-3-line" color="var(--el-color-primary)"></Icon>
                </el-button>
              </span>

              <!-- <deviceTools :item="row"></deviceTools> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('generalDetails.Alert')" width="300">
          <template #default="{ row }">
            <p class="alarm-title">{{ row.title }}</p>
            <p class="alarm-desc">{{ row.desc }}</p>
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('generalDetails.Time')" align="left" width="160">
          <template #default="{ row }">
            {{ formatDate(row.alertCreateTime) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('generalDetails.Alarm code')" align="left">
          <template #default="{ row }">
            <el-button type="text" @click="handleViewCode(row)">{{ $t("generalDetails.View") }}</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="eventConfirmedPerson" :label="$t('generalDetails.responder')" align="left" :formatter="tableFormatter"></el-table-column>
        <el-table-column prop="eventConfirmedTime" :label="$t('generalDetails.Confirmation time')" align="left" width="160" :formatter="tableFormatter"></el-table-column>
      </el-table>
      <!-- <event-alarm-code ref="eventAlarmCode" />
      <alarm-confirm ref="alarmConfirm" @refresh="handleRefresh" /> -->
    </template>
  </page-template>
  <EditorAlarm ref="EditorAlarm" :title="$t('generalDetails.Acknowledge')" />
  <el-dialog v-model="alarmCodeDialog" :title="$t('generalDetails.View Alert Code')">
    <el-scrollbar height="500px">
      <VueCodeMirror v-model="code" :extensions="[javascript()]" :disabled="true" />
    </el-scrollbar>
  </el-dialog>
  <deviceDetials ref="deviceDetialsRef"></deviceDetials>
  <deviceContacts ref="deviceContactsRef"></deviceContacts>
  <devicePing ref="devicePingRef"></devicePing>
  <deviceRoute ref="deviceRouteRef"></deviceRoute>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
// import { getEventAlarmRecord } from "@/api/eventManage";
import { getAlarmByOrderId, getDeviceById } from "@/views/pages/apis/alarmBoard";
import { setChangeCollectAlertDisable, changeOperate, changeStateComplete, changeState } from "@/views/pages/apis/change";
import pageTemplate from "@/components/pageTemplate.vue";

import VueCodeMirror from "@/components/formItem/codemirror/index.vue";
import { javascript } from "@codemirror/lang-javascript";
import { getAlarmCodePayload } from "@/views/pages/apis/alarmBoard";
// import { formatDate } from "@/service/dateChange.ts";
// import { formatDate } from "@/service/dateChange.ts";
import { timeFormat } from "@/utils/date";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { routerV6, routerV6Busines } from "@/views/pages/common/routeV6";
import { alarmEventConfirm, alarmEventAllAlarmConfirm } from "@/views/pages/apis/alarmBoard";
import { InfoFilled } from "@element-plus/icons-vue";
import EditorAlarm from "./EditorAlarm.vue";
import { eventSeverity, eventSeverityOption } from "@/views/pages/apis/event";

import deviceDetials from "@/components/deviceTool/deviceDetials.vue";
import deviceContacts from "@/components/deviceTool/deviceContacts.vue";
import devicePing from "@/components/deviceTool/ping.vue";
import deviceRoute from "@/components/deviceTool/tracerRoute.vue";
import { OrderType } from "@/views/pages/apis/association";
// import eventAlarmCode from "./eventAlarmCode.vue";
// import alarmConfirm from "../alarmBoard/alarmConfirm.vue";
import deviceTools from "@/components/deviceTool/index.vue";

import { desktop, getDeviceDiscovery } from "@/views/pages/apis/device";

import getUserInfo from "@/utils/getUserInfo";

import { 资产管理中心_设备_确认告警 } from "@/views/pages/permission";
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
import { useI18n } from "vue-i18n";

import { Refresh } from "@element-plus/icons-vue";

export default {
  components: {
    pageTemplate /* eventAlarmCode, alarmConfirm */,
    EditorAlarm,
    InfoFilled,
    VueCodeMirror,
    deviceDetials,
    deviceContacts,
    devicePing,
    deviceRoute,
    deviceTools,
  },
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
    height: {
      type: [Number, String],
    },
    handleEnd: Function,
    refresh: Function,
  },
  data() {
    return {
      eventSeverityOption,
      tableData: [],
      paging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      collectAlert: true,
      multipleSelection: [],
      javascript,
      code: "",
      alarmCodeDialog: false,
      userInfo: getUserInfo(),
      changeState,
      OrderType: OrderType,
      routerV6Busines: routerV6Busines,
      deskTopObj: [],
      资产管理中心_设备_确认告警,
      timeZoneHours: _timeZoneHours,
      RefreshIcon: Refresh,
      i18n: useI18n(),
    };
  },
  watch: {
    data(v) {
      this.collectAlert = v?.collectAlertEcho;
      this.handleRefresh();
    },
  },
  created() {
    this.handleRefresh();
  },
  methods: {
    timeZoneSwitching() {
      const timeZone = this.timeZoneHours.find((item) => {
        if (item.zoneId == this.userInfo.zoneId) {
          return item.offsetHours;
        }
      });

      return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
    },
    routeDevice(props) {
      let obj = {
        id: props.deviceId,
        config: {
          ipAddress: props.deviceIp,
        },
        name: props.deviceName,
      };
      this.$refs.deviceRouteRef.open(obj);
    },
    async ping(props: any) {
      let obj = {
        id: props.deviceId,
        config: {
          ipAddress: props.deviceIp,
        },
        name: props.deviceName,
      };
      await this.$refs.devicePingRef.open(obj);
      // pingTo({ id: "523721061548687360" }).then((res) => {
      //   // console.log(res);
      // });
      // deviceId = row.id;
      // deviceContactsRef.dialogFormVisible = true;
    },

    contancts(id) {
      // deviceId = id;
      this.$refs.deviceContactsRef.dialogFormVisible = true;
      this.$refs.deviceContactsRef.open(id);
    },
    preview(id) {
      // deviceId = id;
      this.$refs.deviceDetialsRef.open(id);
      this.$refs.deviceDetialsRef.dialogFormVisible = true;
    },
    formatDate(v) {
      try {
        return timeFormat(Number(v));
      } catch (error) {
        return "--";
      }
    },
    handleCollectAlertChange(v) {
      // if (!v) {
      //   setChangeCollectAlertDisable({ id: this.$route.params.id }).then(({ success, data }) => {
      //     if (success) {
      //       this.$message.success("操作成功");
      //       this.refresh && this.refresh();
      //     } else this.$message.error(JSON.parse(data)?.message || "操作失败");
      //   });
      // }
      this.$emit("confrim", { collectAlert: this.collectAlert });
    },
    async handleViewCode(row) {
      try {
        const { success, data, message } = await getAlarmCodePayload({ id: row.id });
        if (!success) throw new Error(message);
        this.code = data;
        this.alarmCodeDialog = true;
      } catch (error) {
        error instanceof Error && this.$message.error(error.message);
      }
    },
    handleSelectionChange(v) {
      this.multipleSelection = v;
    },
    async handleCommand(v) {
      const { open } = this.$refs.EditorAlarm;
      switch (v) {
        case "selectAll":
          this.$refs.multipleTable.clearSelection();
          this.tableData.forEach((i) => {
            if (!i.alarmBoardConfirmed) this.$refs.multipleTable.toggleRowSelection(i);
          });
          break;
        case "closeSelect":
          this.$refs.multipleTable.clearSelection();
          break;
        case "confirmAll":
          if (!open) return false;
          await open({}, async () => {
            const { success, message, data } = await alarmEventAllAlarmConfirm({
              id: this.$route.params.id,
            });
            if (!success) throw Object.assign(new Error(message), { success, data });
            this.$message.success(`成功确认告警`);
            this.handleRefresh();
          });
          break;
        case "confirmAllAndComplete":
          // this.$emit("handleConifrmAllAndComplete", "ConfirmAndFinish");
          this.handleEnd(this.data, changeOperate.CONFIRM_COMPLETE, changeStateComplete);
          break;
        default:
          if (!this.multipleSelection || !this.multipleSelection.length) return this.$message.error("请选择一条告警");
          if (!open) return false;
          await open({ ids: this.multipleSelection.map(({ id }) => id) }, async (form: Record<string, unknown>) => {
            const { success, message, data } = await alarmEventConfirm([...form.ids], this.$route.params.id);
            if (!success) throw Object.assign(new Error(message), { success, data });
            this.$message.success(`成功确认告警`);
            this.handleRefresh();
          });
          break;
      }
    },
    async handleRefresh() {
      const params = {
        orderId: this.$route.params.id,
        pageNumber: this.paging.pageNumber,
        pageSize: this.paging.pageSize,
        orderType: this.OrderType.CHANGE,
      };
      await getAlarmByOrderId(params).then(({ success, data, total, page, size }) => {
        if (success) {
          this.tableData = data;
          this.tableData && this.tableData.length && this.getDevices(this.tableData.map(({ deviceId }) => deviceId).join());
          this.paging.total = Number(total);
        }
      });
      await desktop({ deviceIds: this.tableData.map(({ deviceId }) => deviceId).join() })
        .then((res) => {
          // console.log(res, 6666);
          if (res.success) {
            this.deskTopObj = res.data;
            this.tableData.forEach((v, i) => {
              v.showDesktop = false;
              v.allowTypes = [];

              this.deskTopObj.forEach((item) => {
                // console.log(v);

                if (v.deviceId == item.resourceId) {
                  v.showDesktop = item.icoShow;
                  v.allowTypes = item.allowTypes;
                }
              });
            });
          }
          // if (res.success) discovery.value = res.data;
        })
        .catch((err) => {
          //
        });
    },
    getDevices(ids) {
      getDeviceById({ ids }).then(({ success, data }) => {
        if (success) {
          this.tableData = this.tableData.map((v) => {
            return {
              ...v,
              device: data.find((f) => f.id === v.deviceId) || {},
              eventConfirmedTime: v.eventConfirmedTime ? v.eventConfirmedTime + this.timeZoneSwitching() : v.eventConfirmedTime,
              alertCreateTime: v.alertCreateTime ? v.alertCreateTime + this.timeZoneSwitching() : v.alertCreateTime,
            };
          });
        }
      });
    },
    handleCurrentPageChange(v) {
      this.paging.pageNumber = v;
    },
    handleSizeChange(v) {
      this.paging.pageSize = v;
    },
    tableFormatter(_row, _col, v) {
      switch (_col.property) {
        case "eventConfirmedTime":
          return v ? this.formatDate(v) : "--";
        case "eventConfirmedPerson":
          return JSON.parse(v)?.username || "--";
        default:
          return v || "--";
      }
    },
    selectable(row) {
      return !row.eventConfirmed && row.eventSeverity != "Normal";
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/theme/common/var.scss";

.alarmUrgency {
  width: auto;
  display: inline-block;
  color: #fff;
  padding: 2px 10px;
  border-radius: 20px;
  align-items: center;
  justify-content: center;
}
.alarm-tools {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.alarm-title {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: getCssVar("font-weight-primary");
  font-size: map-get($font-size, base);
  line-height: getCssVar("font-line-height-primary");
  color: map-get($text-color, primary);
}
.alarm-desc {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: map-get($font-size, base);
  line-height: 18px;
  color: map-get($text-color, regular);
}
// ::v-deep .device-name {
//   font-family: "PingFang SC";
//   font-style: normal;
//   font-weight: 400;
//   font-size: map-get($font-size, base);
//   // line-height: 20px;
//   color: $color-primary;
// }
// ::v-deep .device-ip {
//   font-family: "PingFang SC";
//   font-style: normal;
//   font-weight: 400;
//   font-size: map-get($font-size, base);
//   // line-height: 20px;
//   color: map-get($text-color, secondary);
// }

// ::v-deep .elstyle-form-item {
//   margin-bottom: 0 !important;
// }

.tipIcon {
  color: map-get($text-color, secondary);
  font-size: map-get($font-size, medium);
}
</style>
