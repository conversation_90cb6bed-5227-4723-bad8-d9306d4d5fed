<!-- eslint-disable no-undef -->
<template>
  <page-template :searchSpan="24" :headButtonSpan="24" :showPaging="false">
    <template #right>
      <el-button type="primary" :icon="Plus" :disabled="!verifyPermissionIds.includes(智能事件中心_变更工单_关联工单) || [changeState.UN_APPROVED, changeState.AUTO_CLOSED, changeState.CLOSED].includes(data.changeState)" @click="handleAddAssociation">{{ $t("generalDetails.Add Association") }}</el-button>
    </template>
    <template #default>
      <el-table :data="tableData" stripe style="width: 100%" :height="height - 60">
        <el-table-column prop="orderType" :label="$t('generalDetails.Type')" :formatter="tableFormatter" align="left"></el-table-column>
        <el-table-column prop="orderId" :label="$t('generalDetails.Ticket')" :formatter="tableFormatter" align="left">
          <template #default="{ row }">
            <el-link type="primary" :underline="false" @click="handleToAssociationInfo(row)">{{ row.orderId }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="address" :label="$t('generalDetails.Incident Priority')" :formatter="tableFormatter" min-width="100" align="left">
          <template #default="{ row }">
            <i class="priority-icon" :style="{ background: priority[row.priority].color }" />
            <span :style="{ color: priority[row.priority].color }">{{ row.priority }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="state" :label="$t('generalDetails.Status')" :formatter="tableFormatter" align="left"></el-table-column>
        <el-table-column prop="digest" :label="$t('generalDetails.Digest')" :formatter="tableFormatter" align="left" min-width="280"></el-table-column>
        <el-table-column :label="$t('generalDetails.Operate')" width="80" align="left">
          <template #default="{ row }">
            <el-button type="text" textColor="danger" :disabled="!verifyPermissionIds.includes(智能事件中心_变更工单_关联工单) || [changeState.UN_APPROVED, changeState.AUTO_CLOSED, changeState.CLOSED].includes(data.changeState)" @click="handleRemoveAssociation(row)">{{ $t("generalDetails.Remove") }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <event-add-association ref="eventAddAssociation" :title="$t('generalDetails.link')" :data="tableData" @refresh="handleRefreshTable" :height="height" :approveState="data.approveState" :publishType="data.changeType" />
    </template>
  </page-template>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { Plus } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";
import eventAddAssociation from "./createAssociation.vue";
import { eventGetAssociationList, eventRemoveAssociation } from "@/views/pages/apis/eventManage";
import { eventGetAssociation } from "@/views/pages/apis/association";
import priority from "@/views/pages/common/priority";
import { h } from "vue";
import { eventStateOption, serviceStateOption } from "@/views/pages/apis/event";
import { changeStateOption, changeState } from "@/views/pages/apis/change";
import { questionStateOption } from "@/views/pages/apis/question";
import { publishStateOption } from "@/views/pages/apis/publish";
import { eventCreateAssociationCascade, orderType, secureAssociation, OrderType as DataType } from "@/views/pages/apis/association";
import useCurrentInstance from "@/utils/useCurrentInstance";
import getUserInfo from "@/utils/getUserInfo";
import { 智能事件中心_变更工单_关联工单 } from "@/views/pages/permission";
import { useI18n } from "vue-i18n";
import handleToOrder from "@/views/pages/alarm_convergence/IntelligentEvents/eventBoard/toOrder";
export default {
  components: { pageTemplate, eventAddAssociation },
  inject: ["verifyPermissionIds"],
  props: {
    height: {
      type: [Number, String],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    refresh: {},
  },
  data() {
    return {
      Plus,
      priority,
      changeState,
      paging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      proxy: useCurrentInstance().proxy,
      userInfo: getUserInfo(),
      智能事件中心_变更工单_关联工单,
      i18n: useI18n(),
    };
  },
  created() {
    this.handleRefreshTable();
  },
  methods: {
    handleToAssociationInfo(v) {
      handleToOrder(v.orderType, v.orderId, v.tenantId);
    },
    handleRefreshTable() {
      const params = {
        orderId: this.$route.params.id,
      };
      eventGetAssociation(params).then(({ success, data, total }) => {
        if (success) {
          this.tableData = data;
          this.paging.total = Number(total);
        }
      });
    },
    handleRemoveAssociation(row) {
      this.$confirm("确定移除该关联工单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          secureAssociation({ fromId: this.$route.params.id, toId: row.orderId }).then(({ success, data }) => {
            if (success) {
              // // console.log(this, 555555);
              this.$props.refresh();
              this.$message.success("操作成功");
              this.handleRefreshTable();
            } else this.$message.error(JSON.parse(data)?.message || "操作失败");
          });
        })
        .catch(() => {
          // ...code
        });
    },
    async handleAddAssociation() {
      // ...code
      // this.$refs.eventAddAssociation.open();

      const { open } = this.$refs.eventAddAssociation;
      if (!open) return false;
      await open({}, async (form) => {
        try {
          const { success, message } = await eventCreateAssociationCascade({ fromId: this.$route.params.id, toId: form.id });
          if (!success) throw new Error(message);
          this.$message.success("操作成功");
          this.handleRefreshTable();
          this.$props.refresh();
        } catch (error) {
          error instanceof Error && this.$message.error(error.message);
        }
      });
    },
    tableFormatter(_row, _col, v) {
      switch (_col.property) {
        case "orderType":
          return (orderType.find((f) => f.value === v) || {}).label || "--";
        case "associateType":
          // eslint-disable-next-line no-case-declarations
          const type = {
            EVENT: "事件",
          };
          return type[v] || "--";
        case "state":
          if (_row.orderType === DataType.EVENT_ORDER) return eventStateOption.find((f) => f.value === v)?.label || "--";
          else if (_row.orderType === DataType.SERVICE_REQUEST) return serviceStateOption.find((f) => f.value === v)?.label || "--";
          else if (_row.orderType === DataType.CHANGE) return changeStateOption.find((f) => f.value === v)?.label || "--";
          else if (_row.orderType === DataType.QUESTION) return questionStateOption.find((f) => f.value === v)?.label || "--";
          else if (_row.orderType === DataType.PUBLISH) return publishStateOption.find((f) => f.value === v)?.label || "--";
          else return "--";
        default:
          return v || "--";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.priority-icon {
  width: 10px;
  height: 10px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 10px;
}
</style>
