<template>
  <el-form :model="form" label-position="top">
    <el-form-item>
      <template #label>
        <div class="info-desc">
          <div>
            <span v-if="timestampChanged" style="color: red">*</span>
            <span>{{ $t("generalDetails.Timestamp") }}</span>
          </div>
          <el-button style="z-index: 1" type="primary">{{ (find(changeType, (v) => v.value === props.data.changeType) || {}).label }}</el-button>
        </div>
      </template>
      <el-date-picker :model-value="startEndTime" @update:model-value="($event) => handleChangeTime($event)" type="datetimerange" :range-separator="$t('generalDetails.arrive')" :start-placeholder="$t('generalDetails.Start')" :end-placeholder="$t('generalDetails.End')" :clearable="false" :disabled="[changeState.UN_APPROVED].includes((props.data.changeState as changeState) || ('' as changeState))" value-format="x" format="YYYY-MM-DD HH:mm" />
    </el-form-item>
    <el-form-item>
      <template #label>
        <div class="info-desc">
          <span>{{ $t("generalDetails.Description") }}</span>
          <el-button style="z-index: 1" type="primary" @click="handleDescEdit" :disabled="(!verifyPermissionIds.includes('612915585189150720') && !verifyPermissionIds.includes('777393423094120448')) || [changeState.UN_APPROVED, changeState.AUTO_CLOSED, changeState.CLOSED].includes((props.data.changeState as changeState) || ('' as changeState))">{{ isEdit ? $t("generalDetails.Save") : $t("generalDetails.Edit") }}</el-button>
        </div>
      </template>
      <div class="tw-flex tw-min-h-[250px] tw-w-full tw-flex-col" v-if="isEdit" @keyup.enter.stop>
        <!-- <QuillEditor theme="snow" style="flex: 1" :content="isEdit ? form.description : props.data.desc" @update:content="form.description = $event" contentType="html" toolbar="full" :enable="isEdit" :read-only="!isEdit"></QuillEditor> -->
        <el-input v-model="form.description" :rows="6" type="textarea" :placeholder="$t('generalDetails.Please enter description')" />
      </div>
      <div class="el-input el-input__wrapper tw-flex tw-min-h-[120px] tw-w-full tw-flex-col tw-items-start tw-justify-start tw-p-4" v-else @keyup.enter.stop>
        <div v-html="props.data.desc"></div>
      </div>
    </el-form-item>
    <el-form-item :label="$t('generalDetails.External ID')">
      <template #label>
        <div class="info-desc">
          <span> {{ $t("generalDetails.External ID") }}</span>
          <el-button style="z-index: 1" type="primary" :disabled="(!verifyPermissionIds.includes('612915585189150720') && !verifyPermissionIds.includes('777393423094120448')) || [changeState.UN_APPROVED, changeState.AUTO_CLOSED, changeState.CLOSED].includes((props.data.changeState as changeState) || ('' as changeState))" @click="handleExternal">{{ isExternal ? $t("generalDetails.Save") : $t("generalDetails.Edit") }}</el-button>
        </div>
      </template>
      <el-input :disabled="!isExternal" :model-value="isExternal ? form.externalId : props.data.externalId" @update:model-value="form.externalId = $event"></el-input>
    </el-form-item>
    <el-form-item>
      <el-row class="el-input el-input__wrapper">
        <el-col :span="12" class="tw-h-[calc(var(--el-component-size)*3)] tw-text-left">
          <p>{{ $t("generalDetails.Modified") }}</p>
          <p class="tw-h-[var(--el-component-size)]">
            <el-icon :size="16" class="tw-mr-[6px] tw-align-middle"><UserFilled /></el-icon>{{ updated.name || "--" }}
          </p>
          <p>{{ updated.updateTime ? moment(`${updated.updateTime}`, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</p>
        </el-col>
        <el-col :span="12" class="tw-h-[calc(var(--el-component-size)*3)] tw-text-right">
          <p>{{ $t("generalDetails.Created") }}</p>
          <p class="tw-h-[var(--el-component-size)]">
            <el-icon :size="16" class="tw-mr-[6px] tw-align-middle"><UserFilled /></el-icon>{{ collector.name || "--" }}
          </p>
          <p>{{ collector.collectTime ? moment(`${collector.collectTime}`, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</p>
        </el-col>
      </el-row>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, watch, inject } from "vue";
import { useRoute, useRouter } from "vue-router";

import { ElMessage } from "element-plus";

import { UserFilled } from "@element-plus/icons-vue";
import { setChangeDesc, changeType, setChangeTime, changeState } from "@/views/pages/apis/change";
import { getChangeDetailById as getItemData } from "@/views/pages/apis/change";

import { QuillEditor } from "@vueup/vue-quill";
import moment from "moment";
import { find } from "lodash-es";

import { timeFormats } from "@/utils/date";
import getUserInfo from "@/utils/getUserInfo";
const userInfo = getUserInfo();
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);
import { useI18n } from "vue-i18n";
const { t } = useI18n();
defineOptions({ name: "ModelDetails" });

const props = withDefaults(defineProps<{ data: Partial<import("../helper").DataItem>; height: number; refresh: () => Promise<void> }>(), { data: () => ({}) });
const emits = defineEmits(["changeDate", "changeDesc"]);

const verifyPermissionIds = inject("verifyPermissionIds") as string[];

const route = useRoute();
const router = useRouter();

const form = ref({ description: "", externalId: "" });
const isEdit = ref(false);
const isExternal = ref(false);
const updated = reactive({ name: "", updateTime: 0 });
const collector = reactive({ name: "", collectTime: 0 });

const startEndTime = ref<[string, string]>(["", ""]);
const timestampChanged = ref(false);
// console.log(props, 777777);
getDetails();
function getDetails() {
  getItemData({ id: route.params.id as string }).then((res) => {
    // console.log(res, 6666);
    if (res.success) {
      const start = res.data.startTime ? Number(res.data.startTime) : Date.now();
      const end = res.data.endTime ? Number(res.data.endTime) : Date.now();
      startEndTime.value = start && end ? [timeFormats(start + timeZoneSwitching()), timeFormats(end + timeZoneSwitching())] : ["", ""];
    }
  });
}
function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == userInfo.zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
watch(
  () => props.data,
  async (data) => {
    timestampChanged.value = false;
    await nextTick();
    if (data.createdBy) collector.name = JSON.parse(data.createdBy).username;
    collector.collectTime = Math.max(Number(data.createdTime) || 0, 0) + timeZoneSwitching();
    try {
      updated.name = JSON.parse(data.updatedBy || "{}").username || "";
    } catch (error) {
      updated.name = "";
    }

    updated.updateTime = Math.max(Number(data.updatedTime) || 0, 0) + timeZoneSwitching();
  },
  { immediate: true }
);

async function handleDescEdit() {
  if (isEdit.value) {
    emits("changeDesc", form.value.description);
    isEdit.value = false;
    try {
      const { success, message } = await setChangeDesc({ id: props.data.id as string, desc: form.value.description, externalId: form.value.externalId });
      if (!success) throw new Error(message);
      ElMessage.success(t("generalDetails.Operation successful"));
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      props.refresh();
    }
  } else {
    isEdit.value = true;
    form.value.description = form.value.description || props.data.desc || "";
    form.value.externalId = props.data.externalId || "";
  }
}

async function handleExternal() {
  if (isExternal.value) {
    emits("changeDesc", form.value.description);
    isExternal.value = false;
    try {
      const { success, message } = await setChangeDesc({ id: props.data.id as string, desc: form.value.description, externalId: form.value.externalId });
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      props.refresh();
    }
  } else {
    form.value.description = form.value.description || props.data.desc || "";
    form.value.externalId = props.data.externalId || "";
    isExternal.value = true;
  }
}
async function handleChangeTime(v: Date[]) {
  try {
    timestampChanged.value = true;
    // v=[
    //   "2025-06-19 00:00",
    //   "2025-07-16 00:00"
    // ]
    // startEndTime.value = [timeFormats(v[0].getTime()), timeFormats(v[1].getTime())];
    // emits("changeDate", { startTime: v[0].getTime().toString(), endTime: v[1].getTime().toString() });
    // 将字符串转换为 Date 对象
    const dates = v.map((str) => new Date(str));

    // 然后使用转换后的 Date 对象
    startEndTime.value = [timeFormats(dates[0].getTime()), timeFormats(dates[1].getTime())];

    emits("changeDate", {
      startTime: dates[0].getTime().toString(),
      endTime: dates[1].getTime().toString(),
    });
    // const { success, message } = await setChangeTime({ id: route.params.id as string, startTime: v[0].getTime().toString(), endTime: v[1].getTime().toString() });
    // if (!success) throw new Error(message);
    // props.refresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}
</script>

<style lang="scss" scoped>
.info-desc {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
