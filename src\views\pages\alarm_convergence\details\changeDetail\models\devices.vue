<template>
  <pageTemplate :show-paging="false">
    <template #right>
      <el-button type="primary" :icon="Plus" @click="handleAddRule" :disabled="!verifyPermissionIds.includes(智能事件中心_变更工单_分配设备) || [changeState.UN_APPROVED, changeState.AUTO_CLOSED, changeState.CLOSED].includes(data.changeState)">{{ $t("generalDetails.Add device rules") }}</el-button>
    </template>
    <template #default>
      <el-table stripe :data="ruleTable" style="width: 100%" v-loading="ruleLoading">
        <el-table-column prop="regionId" :label="$t('generalDetails.Region')" :formatter="ruleFormatter"></el-table-column>
        <el-table-column prop="locationId" :label="$t('generalDetails.Location')" :formatter="ruleFormatter"></el-table-column>
        <el-table-column prop="deviceGroupId" :label="$t('generalDetails.Device grouping')" :formatter="ruleFormatter"></el-table-column>
        <el-table-column prop="deviceTypeId" :label="$t('generalDetails.Device Type')" :formatter="ruleFormatter"></el-table-column>
        <el-table-column :label="$t('generalDetails.Assign device contacts')">
          <template #default="{ row }">
            <el-icon v-if="row.autoAllocateContact"><Check /></el-icon>
          </template>
        </el-table-column>
        <el-table-column :label="$t('generalDetails.Operate')" width="80">
          <template #default="{ row }">
            <el-button link type="primary" :disabled="!verifyPermissionIds.includes(智能事件中心_变更工单_分配设备) || [changeState.UN_APPROVED, changeState.AUTO_CLOSED, changeState.CLOSED].includes(data.changeState)" @click="handleDelRule({ ...row })">{{ $t("generalDetails.Delete") }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>
  </pageTemplate>
  <br />
  <pageTemplate :show-paging="false">
    <template #right>
      <el-button type="primary" :icon="Plus" :disabled="!verifyPermissionIds.includes(智能事件中心_变更工单_分配设备) || [changeState.UN_APPROVED, changeState.AUTO_CLOSED, changeState.CLOSED].includes(data.changeState)" @click="handleAddDevice">{{ $t("generalDetails.Add Device") }}</el-button>
    </template>
    <template #default>
      <el-table stripe :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" :label="$t('generalDetails.Device')" width="180">
          <template #default="{ row }">
            <div v-if="row.id" style="color: #409eff" @click="openDevice(row)">
              {{ row.name }}
            </div>
            <div v-if="row.id" style="color: #409eff">
              {{ row.config.ipAddress }}
            </div>
            <div style="cursor: pointer; display: flex">
              <template v-if="userInfo.hasPermission('611865118543708160')">
                <span class="tw-h-fit">
                  <el-button type="primary" link>
                    <deviceTools :item="row" :list="deskTopObj.length" :show="row.showDesktop" :active="row.active"></deviceTools>
                  </el-button>
                </span>
                <span class="tw-h-fit">
                  <el-button type="primary" link @click="ping(row)">
                    <Icon class="tw-mx-[2px]" name="local-DeviceWifi-line" :color="row.active ? 'var(--el-color-primary)' : '#888'"></Icon>
                  </el-button>
                </span>
                <span class="tw-h-fit">
                  <el-button type="primary" link @click="routeDevice(row)">
                    <Icon class="tw-mx-[2px]" name="local-SystemShare-line" :color="row.active ? 'var(--el-color-primary)' : '#888'"></Icon>
                  </el-button>
                </span>
              </template>
              <span class="tw-h-fit">
                <el-button type="primary" link @click="routerV6Busines(row.name)">
                  <Icon class="tw-mx-[2px]" name="local-DocumentNumbers-line" color="var(--el-color-primary)"></Icon>
                </el-button>
              </span>
              <span class="tw-h-fit">
                <el-button type="primary" link @click="preview(row.id)">
                  <Icon class="tw-mx-[2px]" name="local-SystemError-warning-line" color="var(--el-color-primary)"></Icon>
                </el-button>
              </span>

              <span class="tw-h-fit">
                <el-button type="primary" link @click="contancts(row.id)">
                  <Icon :disabled="true" class="tw-mx-[2px]" name="local-UserUser-3-line" color="var(--el-color-primary)"></Icon>
                </el-button>
              </span>

              <!-- <deviceTools :item="row"></deviceTools> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="regionId" :label="$t('generalDetails.Region')" :formatter="tableFormatter"></el-table-column>
        <el-table-column prop="locationId" :label="$t('generalDetails.Location')" :formatter="tableFormatter"></el-table-column>
        <el-table-column prop="importance" :label="$t('generalDetails.Importance')" :formatter="tableFormatter"></el-table-column>
        <el-table-column prop="serialNumbers" :label="$t('generalDetails.Serial Number')" :formatter="tableFormatter"></el-table-column>
        <el-table-column prop="assetNumbers" :label="$t('generalDetails.Asset Number')" :formatter="tableFormatter"></el-table-column>
        <el-table-column prop="serviceLevel" :label="$t('generalDetails.Service Level')" :formatter="tableFormatter"></el-table-column>
        <el-table-column prop="serviceNumbers" :label="$t('generalDetails.Service Number')">
          <template #default="{ row }">
            <el-tooltip class="item" effect="dark" placement="top-start">
              <template #content>
                <p>{{ row.serviceNumbers.map((item) => item.number).join() }}</p>
                <p>{{ row.serviceNumbers.map((item) => item.product).join() }}</p>
                <p>{{ row.serviceNumbers.map((item) => item.progress).join() }}</p>
                <p>{{ row.serviceNumbers.map((item) => item.type).join() }}</p>
                <!-- {{ row.serviceNumbers.map((item) => item.vendorIds.map((v) => supplierOption[v])) }} -->

                <span v-for="item in row.serviceNumbers" :key="item.id">
                  {{ item.vendorIds.map((v) => supplierOption[v]).join() }}
                </span>
              </template>
              <span>
                <!-- {{ supplierOption }} -->
                {{ row.serviceNumbers.map((item) => item.number).join() || "--" }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column :label="$t('generalDetails.Operate')" width="80">
          <template #default="{ row }">
            <el-button link type="primary" :disabled="!verifyPermissionIds.includes(智能事件中心_变更工单_分配设备) || [changeState.UN_APPROVED, changeState.AUTO_CLOSED, changeState.CLOSED].includes(data.changeState)" @click="handleDelDevice({ ...row })">{{ $t("generalDetails.Remove") }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>
  </pageTemplate>
  <createDevice ref="createDevice" :title="$t('generalDetails.Device')" :height="height">
    <template #del="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          {{ $t("generalDetails.Are you sure you want to remove the device") }}
          <span>{{ (params || {}).name }}</span>
          {{ $t("generalDetails.what") }}？
        </p>
      </div>
    </template>
  </createDevice>
  <createRule ref="createRuleRef" title="设备规则">
    <template #del>
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">确定删除此规则吗？</p>
      </div>
    </template>
  </createRule>
  <deviceDetials ref="deviceDetialsRef"></deviceDetials>
  <deviceContacts ref="deviceContactsRef"></deviceContacts>
  <devicePing ref="devicePingRef"></devicePing>
  <deviceRoute ref="deviceRouteRef"></deviceRoute>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { watch } from "vue";
import pageTemplate from "@/components/pageTemplate";
import { getDeviceById, getDeviceList } from "@/views/pages/apis/alarmBoard";
import { Plus, Delete, Check, InfoFilled } from "@element-plus/icons-vue";
import createDevice from "./createDevice.vue";
import createRule from "./createRule.vue";
import { addChangeDevices as addData, delChangeDevice as delData, deviceAllocateRule as getRule, addDeviceAllocateRule as addRule, delDeviceAllocateRule as delRule, changeState } from "@/views/pages/apis/change";
import { getRegionsTenantCurrent } from "@/views/pages/apis/regionManage";
import { getLocationsTenantCurrent } from "@/views/pages/apis/locationManang";
import { getdeviceGroupList, getdeviceTypeList } from "@/views/pages/apis/deviceManage";
import { h } from "vue";
import getUserInfo from "@/utils/getUserInfo";
import deviceDetials from "@/components/deviceTool/deviceDetials.vue";
import deviceContacts from "@/components/deviceTool/deviceContacts.vue";
import devicePing from "@/components/deviceTool/ping.vue";
import deviceRoute from "@/components/deviceTool/tracerRoute.vue";
import { getLineSupplierList } from "@/views/pages/apis/supplier";
import { routerV6, routerV6Busines } from "@/views/pages/common/routeV6";
import deviceTools from "@/components/deviceTool/index.vue";
import { desktop, getDeviceDiscovery } from "@/views/pages/apis/device";

import { 智能事件中心_变更工单_分配设备 } from "@/views/pages/permission";
import { useI18n } from "vue-i18n";
export default {
  components: { pageTemplate, createDevice, createRule, Check, InfoFilled, deviceDetials, deviceContacts, devicePing, deviceRoute, deviceTools },
  inject: ["verifyPermissionIds"],
  props: {
    height: {
      type: [Number, String],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    refresh: Function,
  },
  emits: ["refresh"],
  data() {
    return {
      Plus,
      Delete,
      changeState,
      tableData: [],
      ruleTable: [],
      regions: [],
      locations: [],
      deviceGroups: [],
      deviceTypes: [],
      userInfo: getUserInfo(),
      supplierOption: {},
      routerV6Busines: routerV6Busines,
      deskTopObj: [],

      智能事件中心_变更工单_分配设备,
      i18n: useI18n(),
      loading: false,
      ruleLoading: true,
    };
  },
  // watch: {
  //   data: {
  //     async handler({ deviceIds }) {
  //       if (deviceIds && deviceIds instanceof Array && deviceIds.length) this.tableData = await this.getDevices(deviceIds.join());
  //       else this.tableData = [];
  //     },
  //     immediate: true,
  //   },
  // },
  async created() {
    await Promise.all([this.getRegions(), this.getLocations(), this.getDeviceGuoups(), this.getDeviceTypes()]);
    await this.getDeviceRule();
    let unWatch;
    unWatch = watch(
      () => this.data,
      async ({ deviceIds }) => {
        if (deviceIds && deviceIds instanceof Array && deviceIds.length) {
          unWatch && (unWatch = void unWatch());
          watch(
            () => this.data,
            async ({ deviceIds }) => {
              if (deviceIds && deviceIds instanceof Array && deviceIds.length) {
                this.loading = true;
                this.tableData = await this.getDevices(deviceIds.join());
                this.loading = false;
                desktop({ deviceIds: deviceIds.join() })
                  .then((res) => {
                    // console.log(res, 6666);
                    if (res.success) {
                      this.deskTopObj = res.data;
                      this.tableData.forEach((v, i) => {
                        v.showDesktop = false;
                        v.allowTypes = [];

                        this.deskTopObj.forEach((item) => {
                          // console.log(v);

                          if (v.id === item.resourceId) {
                            v.showDesktop = item.icoShow;
                            v.allowTypes = item.allowTypes;
                          }
                        });
                      });
                    }
                    // if (res.success) discovery.value = res.data;
                  })
                  .catch((err) => {
                    //
                  });
              } else this.tableData = [];
            },
            { deep: true, immediate: true }
          );
        }
      },
      { deep: true, immediate: true }
    );
  },
  methods: {
    openDevice(props) {
      const { href } = this.$router.resolve({
        name: "509596457372745728",
        params: { id: props.id },
        query: {
          fallback: this.$route.name,
          tenant: props.tenantId,
          eventId: this.$route.params.id,
        },
      });

      window.open(href, props.id);

      // { name: '509596457372745728', params: { id: row.id || '' }, query: { fallback: $route.name as string,tenant:row.tenantId } }
    },
    getSupplier() {
      //服务商列表
      getLineSupplierList({}).then((res) => {
        const options = {};
        res.data.forEach((v, i) => {
          options[v.id] = v.name;
        });
        this.supplierOption = options;
        // console.log(this.supplierOption, 1111111);
      });
    },
    routeDevice(props) {
      this.$refs.deviceRouteRef.open(props);
    },
    async ping(props: any) {
      await this.$refs.devicePingRef.open(props);
      // pingTo({ id: "523721061548687360" }).then((res) => {
      //   // console.log(res);
      // });
      // deviceId = row.id;
      // deviceContactsRef.dialogFormVisible = true;
    },

    contancts(id) {
      // deviceId = id;
      this.$refs.deviceContactsRef.dialogFormVisible = true;
      this.$refs.deviceContactsRef.open(id);
    },
    preview(id) {
      // deviceId = id;
      this.$refs.deviceDetialsRef.open(id);
      this.$refs.deviceDetialsRef.dialogFormVisible = true;
    },
    ruleFormatter(_row, _col, v) {
      switch (_col.property) {
        case "regionId":
          return String((this.regions.find((f) => f.id === v) || {}).name || "--");
        case "locationId":
          return String((this.locations.find((f) => f.id === v) || {}).name || "--");
        case "deviceTypeId":
          return String((this.deviceTypes.find((f) => f.id === v) || {}).name || "--");
        case "deviceGroupId":
          return String((this.deviceGroups.find((f) => f.id === v) || {}).name || "--");
        default:
          return String(v || "--");
      }
    },
    async getRegions() {
      const { success, data, message } = await getRegionsTenantCurrent({});
      if (!success) throw new Error(message);
      this.regions = data;
    },
    async getLocations() {
      const { success, data, message } = await getLocationsTenantCurrent({});
      if (!success) throw new Error(message);
      this.locations = data;
    },
    async getDeviceGuoups() {
      const { success, data, message } = await getdeviceGroupList({ pageNumber: 1, pageSize: 99999 });
      if (!success) throw new Error(message);
      this.deviceGroups = data;
    },
    async getDeviceTypes() {
      const { success, data, message } = await getdeviceTypeList({ pageNumber: 1, pageSize: 99999 });
      if (!success) throw new Error(message);
      this.deviceTypes = data;
    },
    async handleAddRule() {
      const { open } = this.$refs.createRuleRef;
      if (!open) return false;
      await open({}, async (form) => {
        // // console.log(form);
        // debugger;
        const { success, message } = await addRule({ ...form, regionId: form.regionId[form.regionId.length - 1] });
        if (!success) throw new Error(message);
        this.$message.success("操作成功");
        this.getDeviceRule();
      });
    },
    async getDeviceRule() {
      const { success, data, message } = await getRule({ orderId: this.$route.params.id });
      if (!success) {
        this.ruleLoading = false;
        throw new Error(message);
      } else {
        this.ruleTable = data;
        this.ruleLoading = false;
      }
    },
    async handleDelDevice(row) {
      const { confirm } = this.$refs.createDevice;
      if (!confirm) return;
      try {
        const params = { ...row };
        const form = {};
        await confirm({ ...form, ...params, $title: `移除设备`, $slot: "del" }, async (form) => {
          const { success, message, data } = await delData({ id: this.$route.params.id, deviceId: row.id });
          if (!success) throw Object.assign(new Error(message), { success, data });
          this.$message.success(`成功${form.$title}`);
        });
      } catch (error) {
        if (error instanceof Error) {
          const message = error.message;
          await new Promise((resolve) => this.$message.error({ message, onClose: () => resolve(null) }));
        }
      } finally {
        this.refresh();
      }
    },
    async handleDelRule(row) {
      const { confirm } = this.$refs.createRuleRef;
      if (!confirm) return;
      try {
        const params = { ...row };
        const form = {};
        await confirm({ ...form, ...params, $title: `删除规则`, $slot: "del" }, async (form) => {
          const { success, message, data } = await delRule({ id: row.id });
          if (!success) throw Object.assign(new Error(message), { success, data });
          this.$message.success(`成功${form.$title}`);
        });
      } catch (error) {
        if (error instanceof Error) {
          const message = error.message;
          await new Promise((resolve) => this.$message.error({ message, onClose: () => resolve(null) }));
        }
      } finally {
        this.getDeviceRule();
      }
    },
    async handleAddDevice() {
      const { open } = this.$refs.createDevice;
      if (!open) return false;
      await open({ devices: this.tableData }, async (form) => {
        const { success, message } = await addData({ id: this.$route.params.id, deviceIds: form.deviceId, autoAllocateContact: form.autoAllocateContact });
        if (!success) throw new Error(message);
        this.$message.success("操作成功");
        this.refresh();
      });
    },
    async getDevices(ids) {
      this.getSupplier();
      try {
        const { success, message, data } = await getDeviceList({ ids });
        if (!success) throw new Error(message);

        return data instanceof Array ? data : [];
      } catch (error) {
        this.loading = false;
        if (error instanceof Error) await new Promise((resolve) => this.$message.error({ message: error.message, onClose: () => resolve(null) }));
      }
      // desktop({ deviceIds: ids })
      //   .then((res) => {
      //     // console.log(res, 6666);
      //     if (res.success) {
      //       this.deskTopObj = res.data;
      //       this.tableData.forEach((v, i) => {
      //         v.showDesktop = false;
      //         v.allowTypes = [];

      //         this.deskTopObj.forEach((item) => {
      //           // console.log(v);

      //           if (v.id == item.resourceId) {
      //             v.showDesktop = item.icoShow;
      //             v.allowTypes = item.allowTypes;
      //           }
      //         });
      //       });
      //     }
      //     // if (res.success) discovery.value = res.data;
      //   })
      //   .catch((err) => {
      //     //
      //   });
      return [];
    },
    tableFormatter(_row, _col, v) {
      const deviceImgList = [
        {
          normalImg: require("@/views/pages/assets/device/desktop-normal.png"),
          offlineImg: require("@/views/pages/assets/device/desktop-offline.png"),
          warningImg: require("@/views/pages/assets/device/desktop-warning.png"),
        },
        {
          normalImg: require("@/views/pages/assets/device/wifi-normal.png"),
          offlineImg: require("@/views/pages/assets/device/wifi-offline.png"),
          warningImg: require("@/views/pages/assets/device/wifi-warning.png"),
        },
        {
          normalImg: require("@/views/pages/assets/device/share-normal.png"),
          offlineImg: require("@/views/pages/assets/device/share-offline.png"),
          warningImg: require("@/views/pages/assets/device/share-warning.png"),
        },
        {
          normalImg: require("@/views/pages/assets/device/vertical-normal.png"),
          offlineImg: require("@/views/pages/assets/device/vertical-offline.png"),
          warningImg: require("@/views/pages/assets/device/vertical-warning.png"),
        },
        {
          normalImg: require("@/views/pages/assets/device/exclamation-normal.png"),
          offlineImg: require("@/views/pages/assets/device/exclamation-offline.png"),
          warningImg: require("@/views/pages/assets/device/exclamation-warning.png"),
        },
        {
          normalImg: require("@/views/pages/assets/device/user-normal.png"),
          offlineImg: require("@/views/pages/assets/device/user-offline.png"),
          warningImg: require("@/views/pages/assets/device/user-warning.png"),
        },
      ];
      switch (_col.property) {
        case "tools":
          return h("div", { class: "tw-flex tw-justify-around" }, [
            ...deviceImgList.map((v, i) => {
              return h("el-button", { type: "text" }, [h("img", { src: deviceImgList[i].normalImg })]);
            }),
          ]);
        case "regionId":
          return String((this.regions.find((f) => f.id === v) || {}).name || "--");
        case "locationId":
          return String((this.locations.find((f) => f.id === v) || {}).name || "--");
        case "serialNumbers":
          return _row.config.serialNumbers ? JSON.parse(_row.config.serialNumbers).join() || "--" : "--";
        case "assetNumbers":
          return _row.config.assetNumbers ? JSON.parse(_row.config.assetNumbers).join() || "--" : "--";
        case "serviceLevel":
          return "--";
        case "serviceNumbers":
          return _row.serviceNumbers.map((item) => item.number).join() || "--";
        default:
          return String(v || "--");
      }
    },
  },
};
</script>
