<template>
  <el-row>
    <el-col>
      <el-button type="primary" :icon="Plus" @click="openDrawer" :disabled="(!verifyPermissionIds.includes('612915766462775296') && !verifyPermissionIds.includes('777395253240922112')) || [changeState.UN_APPROVED, changeState.AUTO_CLOSED, changeState.CLOSED].includes(data.changeState)">{{ $t("generalDetails.Add Journal") }}</el-button>
    </el-col>
    <el-col :style="{ marginTop: '10px' }">
      <el-row>
        <el-table ref="table" :data="notes" style="width: 100%" :show-header="false" :height="height - 60">
          <el-table-column>
            <template #default="{ row }">
              <div class="note-item">
                <div>
                  <!-- <el-avatar icon="el-icon-user-solid" >{{ row.noteCreateUserName.slice(-2) }}</el-avatar> -->
                  <el-avatar :size="36" fit="fill">{{ row.noteCreateUserName?.slice(-2) }}</el-avatar>
                </div>
                <div :style="{ marginLeft: '10px', flex: 1 }">
                  <p class="note-user">
                    <span class="note-user-h"> {{ row.noteCreateUserName }}</span>
                    <span v-if="row.privateAble" class="note-user-b">
                      <span>
                        <svg t="1717468891569" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5996" width="20" height="20"><path d="M713.04314 394.841279l-33.486639 0 0-67.070491c0-93.874947-73.742441-167.623528-167.620458-167.623528-93.777733 0-167.572362 73.748581-167.572362 167.623528l0 67.070491-33.533711 0c-36.82466 0-67.019326 30.147594-67.019326 66.965091l0 335.251148c0 36.817497 30.194666 66.965091 67.019326 66.965091l402.212146 0c36.922897 0 67.069468-30.147594 67.069468-66.965091L780.111584 461.806369C780.113631 424.989896 749.966037 394.841279 713.04314 394.841279M511.937067 696.500388c-36.820567 0-67.019326-30.194666-67.019326-67.067421 0-36.875825 30.198759-67.070491 67.019326-67.070491 36.920851 0 67.070491 30.194666 67.070491 67.070491C579.007558 666.306746 548.857917 696.500388 511.937067 696.500388M615.877243 394.841279l-207.830211 0 0-67.070491c0-57.006285 46.933892-103.892081 103.889011-103.892081 57.053357 0 103.9412 46.885797 103.9412 103.892081L615.877243 394.841279z" fill="#231815" p-id="5997"></path></svg>
                      </span>
                      <span class="note-user-z"> {{ row.privateCustomerAbbreviate }}私有</span>
                    </span>
                  </p>
                  <div class="note-content">
                    <div class="ql-snow ql-editor" v-html="row.noteContent" />
                  </div>
                  <ul class="note-files">
                    <li class="note-file-item" v-for="item in row.attachmentList" :key="`attachmentList-${item.attachmentName}`">
                      <div>
                        <i class="el-icon-document-checked file-icon"></i>
                        <span class="file-name">{{ item.attachmentName }}</span>
                      </div>
                      <div>
                        <el-button type="text" :icon="Download" :disabled="(!verifyPermissionIds.includes('612915766462775296') && !verifyPermissionIds.includes('777395288988975104')) || [changeState.UN_APPROVED, changeState.AUTO_CLOSED, changeState.CLOSED].includes(data.changeState)" @click="handleDownloadFile(item)"></el-button>
                        <el-button type="text" textColor="danger" :disabled="(!verifyPermissionIds.includes('612915766462775296') && !verifyPermissionIds.includes('777395288988975104')) || [changeState.UN_APPROVED, changeState.AUTO_CLOSED, changeState.CLOSED].includes(data.changeState)" @click="handleDelNoteFile(item, row.noteId)"></el-button>
                      </div>
                    </li>
                  </ul>
                  <div class="node-operate">
                    <span class="node-date">{{ formatDate(row.noteCreateTime) }}</span>
                    <div>
                      <el-button type="text" :disabled="(!verifyPermissionIds.includes('612915766462775296') && !verifyPermissionIds.includes('777395288988975104')) || [changeState.UN_APPROVED, changeState.AUTO_CLOSED, changeState.CLOSED].includes(data.changeState)" @click="openDrawer(row)">{{ $t("generalDetails.Edit") }}</el-button>
                      <el-button type="text" textColor="danger" :disabled="(!verifyPermissionIds.includes('612915766462775296') && !verifyPermissionIds.includes('777395358215962624')) || [changeState.UN_APPROVED, changeState.AUTO_CLOSED, changeState.CLOSED].includes(data.changeState)" @click="handleDelNote(row)">{{ $t("generalDetails.Delete") }}</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
    </el-col>
    <event-create-note ref="eventCreateNote" @refresh="getEventNotes" orderType="CHANGE" />
  </el-row>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import EventCreateNote from "@/views/pages/alarm_convergence/details/eventDetail/models/createNote.vue";
import { delNoteFile } from "@/views/pages/apis/eventManage";
import { getChangeNotes, delChangeNote, changeState } from "@/views/pages/apis/change";
import { getFile, download } from "@/views/pages/apis/file";
import { getAvatar } from "@/api/system";
import { timeFormat } from "@/utils/date.ts";
import { Plus, Download, Delete } from "@element-plus/icons-vue";
import getUserInfo from "@/utils/getUserInfo";
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
import { useI18n } from "vue-i18n";

export default {
  components: { EventCreateNote },
  inject: ["verifyPermissionIds"],
  props: {
    height: {
      type: [Number, String],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    refresh: Function,
  },
  data() {
    return {
      tableData: [],

      notes: [],
      Plus,
      Download,
      Delete,
      changeState,
      userInfo: getUserInfo(),
      timeZoneHours: _timeZoneHours,
      i18n: useI18n(),
    };
  },
  created() {
    this.getEventNotes();
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.tableHeight = window.innerHeight - 39 - this.$refs.table.$el.getBoundingClientRect().top;
    // });
  },
  methods: {
    timeZoneSwitching() {
      const timeZone = this.timeZoneHours.find((item) => {
        if (item.zoneId == this.userInfo.zoneId) {
          return item.offsetHours;
        }
      });

      return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
    },
    formatDate(v) {
      try {
        return timeFormat(Number(v));
      } catch (error) {
        return "--";
      }
    },
    handleDelNoteFile({ attachmentUrl, attachmentKey, attachmentId }, noteId) {
      this.$confirm(this.i18n.t("generalDetails.Confirm to delete the attachment of this note") + "?", this.i18n.t("generalDetails.prompt"), {
        confirmButtonText: this.i18n.t("generalDetails.confirm"),
        cancelButtonText: this.i18n.t("generalDetails.Cancel"),
        type: "warning",
      })
        .then(() => {
          delNoteFile({ attachmentUrl: attachmentUrl.substring(1), attachmentKey, noteId, attachmentId }).then(({ success, data }) => {
            if (success) {
              this.$message.success(this.i18n.t("generalDetails.Operation successful"));
              this.getEventNotes();
            } else this.$message.error(JSON.parse(data)?.message || this.i18n.t("generalDetails.Operation failed"));
          });
        })
        .catch(() => {
          //....
        });
    },
    handleDownloadFile(file) {
      if (!file.attachmentUrl) return false;
      getFile({ fileUrl: file.attachmentUrl }).then(({ success, data }) => {
        if (success) {
          download(file.attachmentName, data);
        } else this.$message.error(JSON.parse(data)?.message || this.i18n.t("generalDetails.Download failed"));
      });
    },
    handleDelNote(item) {
      this.$confirm(this.i18n.t("generalDetails.Confirm to delete this note") + "?", this.i18n.t("generalDetails.prompt"), {
        confirmButtonText: this.i18n.t("generalDetails.confirm"),
        cancelButtonText: this.i18n.t("generalDetails.Cancel"),
        type: "warning",
      }).then(() => {
        const params = {
          id: this.$route.params.id,
          noteId: item.noteId,
          tenantId: this.userInfo.currentTenantId,
        };
        delChangeNote(params).then(({ success, data }) => {
          if (success) {
            this.$message.success(this.i18n.t("generalDetails.Operation successful"));
            this.getEventNotes();
          } else this.$message.error(JSON.parse(data)?.message || this.i18n.t("generalDetails.Operation failed"));
        });
      });
    },
    getEventNotes() {
      getChangeNotes({ id: this.$route.params.id }).then(({ success, data }) => {
        if (success) {
          this.notes = data.map(async (v) => {
            const src = await getAvatar({ filePath: v.noteCreateUserProfilePicture });
            return Object.assign(v, { src });
          });
          Promise.all(this.notes).then((res) => {
            this.notes = res.map((v) => {
              return {
                ...v,
                noteCreateTime: Number(v.noteCreateTime) + this.timeZoneSwitching(),
              };
            });
          });
          this.refresh();
        } else this.$message.error(JSON.parse(data)?.message || this.i18n.t("generalDetails.Failed to retrieve event notes"));
      });
    },
    openDrawer(row = {}) {
      this.$refs.eventCreateNote.open(row);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/theme/common/var.scss";
.note-item {
  display: flex;
  .note-user {
    font-size: map-get($map: $font-size, $key: base);
    font-family:
      PingFang SC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #4e5969;
    line-height: 22px;
    display: flex;
    .note-user-b {
      display: flex;
      .note-user-h {
        margin-right: 5px;
      }
      .note-user-z {
        font-size: 14px;
        color: #1595e0;
      }
    }
  }
  .node-date {
    font-size: map-get($map: $font-size, $key: base);
    font-family:
      PingFang SC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #86909c;
    line-height: 22px;
  }
  .node-operate {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .note-content {
    font-size: map-get($map: $font-size, $key: base);
    font-family:
      PingFang SC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #1d2129;
    line-height: 22px;
    height: auto !important;
    padding: 0 !important;
  }
  .note-file-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 7px 12px;
    background: #f7f8fa;
    border-radius: 2px;
    .file-icon {
      margin-right: 14.33px;
    }
    .file-name {
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      color: #1d2129;
    }
  }
}
</style>
