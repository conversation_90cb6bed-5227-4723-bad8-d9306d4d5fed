<template>
  <pageTemplate :show-paging="false" :height="height">
    <template #right>
      <el-button type="primary" @click="handleDistributionProject">分配项目</el-button>
    </template>

    <template #default>
      <!-- <el-button type="primary">分配项目</el-button> -->
      <el-table v-loading="loading" :data="tableData" :height="height - 60" :style="{ width: `100%`, margin: '0 auto' }">
        <TableColumn type="default" label="项目名称" prop="projectName"></TableColumn>
        <TableColumn type="default" label="统一服务编号" prop="uniformServiceCode"></TableColumn>
        <TableColumn type="default" label="项目编号" prop="projectCode"></TableColumn>
        <TableColumn type="default" label="项目级别" prop="projectLevel"></TableColumn>
        <TableColumn type="default" label="项目有效期" prop="range"></TableColumn>
        <!-- <TableColumn type="default" label="售后总集"></TableColumn> -->
      </el-table>
    </template>
  </pageTemplate>
</template>

<script setup lang="ts">
import { computed, ref, toRefs, h, defineComponent, onMounted, watch } from "vue";
import { useRoute } from "vue-router";

import { EventItem, type priority } from "@/views/pages/apis/event";

import TableColumn from "@/components/tableColumn/TableColumn.vue";
import pageTemplate from "@/components/pageTemplate.vue";
import { ElMessageBox, ElMessage, ElFormItem, ElForm, FormInstance, ElSelect, ElOption } from "element-plus";
import { getAllProjectPlans } from "@/views/pages/apis/projectPlan";
import { getProjectsByEventId, replaceProjectByEventId, ProjectItem, getDictOrderProject } from "@/views/pages/apis/projectManage";
import moment from "moment";
import getUserInfo from "@/utils/getUserInfo";

import { 配置管理中心_项目管理_可读 } from "@/views/pages/permission";

const userInfo = getUserInfo();
const route = useRoute();

const emits = defineEmits(["refresh"]);

interface Props {
  height: number;
  data: Partial<EventItem>;
  refresh: () => Promise<void>;
}

const props = withDefaults(defineProps<Props>(), {
  height: 0,
  data: () => <Partial<EventItem>>{},
});

const { height, data: detail } = toRefs(props);

watch(
  () => detail.value,
  (v) => (v || {}).projectId || handleRefresh(),
  { immediate: true, deep: true }
);

const loading = ref<boolean>(false);

const project = ref<Record<string, any>>({});

const tableData = computed(() => (userInfo.hasPermission(配置管理中心_项目管理_可读) && Object.keys(project.value).length ? [{ projectCode: project.value.projectCode, projectId: project.value.projectId, projectLevel: project.value.projectLevel, projectName: project.value.projectName, uniformServiceCode: project.value.uniformServiceCode, range: project.value.range && project.value.range.start && project.value.range.end ? `${moment(Number(project.value.range.start)).format("YYYY-MM-DD")} 至 ${moment(Number(project.value.range.end)).format("YYYY-MM-DD")}` : `` }] : []));

// detail.value.range

const distributionProjectForm = ref<Record<string, any>>({
  projectId: "",
});
const distributionProjectFormRef = ref<FormInstance>();

async function handleDistributionProject() {
  let projects: ProjectItem[] = [];
  if (userInfo.hasPermission(配置管理中心_项目管理_可读)) {
    const { data, success, message } = await getProjectsByEventId({ id: props.data.projectId });
    if (!success) throw new Error(message);
    projects = data;
  }

  ElMessageBox({
    title: `分配项目`,
    message: h(
      defineComponent({
        setup() {
          return () =>
            h(
              ElForm,
              { model: distributionProjectForm, ref: (v) => (distributionProjectFormRef.value = v as FormInstance), rules: { projectId: [{ required: true, message: "请选择项目名称", trigger: ["change", "blur"] }] } },
              h(
                ElFormItem,
                { label: "项目名称", prop: "projectId" },
                h(
                  ElSelect,
                  { "modelValue": distributionProjectForm.value.projectId, "onUpdate:modelValue": (v) => (distributionProjectForm.value.projectId = v) },
                  projects.map((v) => h(ElOption, { label: v.projectName, value: v.id, class: "tw-h-[auto]" }, [h("div", [h("p", { class: "tw-text-base tw-font-semibold tw-leading-8" }, v.projectName), h("p", { class: "tw-text-sm tw-leading-7" }, `统一服务编号：${v.uniformServiceCode}`), h("p", { class: "tw-text-sm tw-leading-7" }, `项目编号：${v.projectCode}`)])]))
                )
              )
            );
        },
      })
    ),
    beforeClose: async (action, instance, done) => {
      if (action === "confirm") {
        distributionProjectFormRef.value &&
          distributionProjectFormRef.value.validate(async (valid) => {
            if (!valid) return;
            try {
              const { id, projectName, projectCode, projectLevel, uniformServiceCode, range } = projects.find((v) => v.id === distributionProjectForm.value.projectId) as ProjectItem;
              const { message, success } = await replaceProjectByEventId({ id: route.params.id, projectId: id, projectName, projectCode, projectLevel, uniformServiceCode, range });
              if (!success) throw new Error(message);
              ElMessage.success("操作成功");
              distributionProjectFormRef.value && distributionProjectFormRef.value.resetFields();
              emits("refresh");
              done();
            } catch (error) {
              error instanceof Error && ElMessage.error(error.message);
            } finally {
              props.refresh();
            }
          });
      } else {
        done();
      }
    },
  })
    .then(() => {})
    .catch(() => {});
}

async function handleRefresh() {
  try {
    const { data, message, success } = await getDictOrderProject({ id: detail.value.projectId });
    if (!success) throw new Error(message);
    project.value = data;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}
</script>
