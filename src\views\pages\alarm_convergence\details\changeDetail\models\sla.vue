<template>
  <pageTemplate :show-paging="false" :height="height">
    <template #default>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-table v-loading="state.loading" :data="state.data.resp" :height="height - 60" :style="{ width: `100%`, margin: '0 auto' }">
            <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :min-width="column.width || 120" :="column.showOverflowTooltip" :formatter="column.formatter" />
          </el-table>
        </el-col>

        <el-col :span="12">
          <el-table v-loading="state.loading" :data="state.data.resolve" :height="height - 60" :style="{ width: `100%`, margin: '0 auto' }">
            <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :min-width="column.width || 120" :="column.showOverflowTooltip" :formatter="column.formatter" />
          </el-table>
        </el-col>
      </el-row>
    </template>
  </pageTemplate>
</template>

<script lang="ts" setup>
import { ref, inject, toRefs, reactive, onMounted, nextTick, h } from "vue";

import { sizes } from "@/utils/common";

import { timeFormat } from "@/utils/date";

import { urgencyTypeOption } from "@/views/pages/apis/event";

import pageTemplate from "@/components/pageTemplate.vue";

import type { TableColumnCtx } from "element-plus";

import { ElMessage } from "element-plus";

import { eventSla as getData, Resp as DataItem } from "@/views/pages/apis/eventManage";

import { type priority } from "@/views/pages/apis/event";
import { Item } from "../helper";

import { useI18n } from "vue-i18n";
const { t } = useI18n();
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
import getUserInfo from "@/utils/getUserInfo";
const timeZoneHours = ref(_timeZoneHours);
import moment from "moment";
function timeZoneSwitching(): number {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == getUserInfo().zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}

// import { useRoute } from "vue-router";

// const route = useRoute();
interface Props {
  height: number;
  data: Partial<Item>;
}

// const width = inject("width", ref(0));

const props = withDefaults(defineProps<Props>(), {
  height: 0,
  data: () => <Partial<Item>>{},
});

const { height, data: detail } = toRefs(props);

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: TableColumnCtx<DataItem>, value: T[keyof T]) => string | import("vue").VNode; showOverflowTooltip?: boolean }[];
  data: {
    resp: T[];
    resolve: T[];
  };
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<Partial<DataItem>>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  column: [
    {
      key: "urgencyType",
      label:t('generalDetails.SLA status'),
      formatter: (_row, _col, v) => {
        const urgencyType = urgencyTypeOption.find((el) => el.value === v);
        return h("span", { class: " tw-rounded-4xl tw-text-white slaStatus", style: { background: urgencyType?.color, padding: "3px 15px", borderRadius: "15px" } }, `${v?.charAt(0)}${v?.substring(1).toLowerCase()}`);
      },
    },
    {
      key: "name",
      label:t('generalDetails.Level'),
      formatter: (_row, _col, v) => {
        return `${_row.name ? _row.name + "-" : ""}${_row.description ? _row.description + "-" : ""}${_row.definition ? _row.definition : "-"}`;
      },
    },
    // { key: "urgencyType", label: "级别" },
    {
      key: "toleranceTime",
      label:t('generalDetails.Tolerance time'),
      formatter: (_row, _col, v) => {
        return (
          ["day", "hour", "minute"].reduce((k: string, p: string) => {
            if (Number(_row[p as keyof DataItem])) k += `${_row[p as keyof DataItem]}${p}`;
            return k;
          }, "") || "--"
        );
      },
    },
    {
      key: "createTime",
      label:t('generalDetails.Creation time'),
      formatter: (_row, _col, v) => {
        try {
          return v;
        } catch {
          return v || "--";
        }
      },
    },
  ],
  data: {
    resp: [],
    resolve: [],
  },
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

async function getEventSla() {
  if (!detail.value.originSlaId) return false;
  try {
    const { success, data, message } = await getData({ originSlaId: detail.value.originSlaId as string, originPriority: detail.value.originPriority as priority });
    if (!success) throw new Error(message);
    // state.data.resolve = data.resolve.map((v) => ({ ...v, createTime: data.createTime }));
    // state.data.resp = data.resp.map((v) => ({ ...v, createTime: data.createTime }));
    const newTime = timeFormat(Number(data.createTime));
    const TimeS = moment(moment(newTime, "YYYY-MM-DD HH:mm:ss").valueOf() + timeZoneSwitching()).format("YYYY-MM-DD HH:mm:ss");
    state.data.resolve = data.resolve.map((v) => ({ ...v, createTime: data.createTime != "0" ? Number(data.createTime) + timeZoneSwitching() : TimeS }));
    state.data.resp = data.resp.map((v) => ({ ...v, createTime: data.createTime != "0" ? Number(data.createTime) + timeZoneSwitching() : TimeS }));
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

onMounted(async () => {
  await nextTick();
  getEventSla();
});

// function formatter(_row: DataItem, _col: TableColumnCtx<DataItem>, v: DataItem[keyof DataItem]): string | import("vue").VNode {
//   return "---";
// }
</script>

<style lang="scss" scoped></style>
