<template>
  <el-form :model="form" label-position="top">
    <!-- <el-form-item v-if="kbDesc">
      <div class="el-input el-input__wrapper tw-flex tw-min-h-[32px] tw-w-full tw-flex-col tw-items-start tw-justify-start">
        <div>{{ kbDesc }}</div>
      </div>
    </el-form-item> -->

    <el-form-item>
      <template #label>
        <div class="info-desc">
          <span>客保工单号</span>
          <el-button style="z-index: 1" type="primary" :disabled="!verifyPermissionIds.includes(智能事件中心_DICT事件管理_更新) || [eventState.NEW, eventState.CLOSED, eventState.AUTO_CLOSED].includes((props.data.eventState as eventState) || ('' as eventState))" @click="handleKbcodeEdit">{{ isEditKb ? "保存" : "编辑" }}</el-button>
        </div>
      </template>
      <div class="tw-flex tw-min-h-[32px] tw-w-full tw-flex-col" v-if="isEditKb" @keyup.enter.stop>
        <!-- <QuillEditor theme="snow" style="flex: 1" :content="isEdit ? form.description : props.data.description" @update:content="form.description = $event" contentType="html" toolbar="full" :enable="isEdit" :read-only="!isEdit"></QuillEditor> -->
        <el-input v-model="form.kbServiceCode" placeholder="请输入客保工单号" />
      </div>
      <div class="el-input el-input__wrapper tw-flex tw-min-h-[32px] tw-w-full tw-flex-col tw-items-start tw-justify-start" v-else @keyup.enter.stop>
        <div>{{ props.data.kbServiceCode }}</div>
      </div>
    </el-form-item>

    <el-form-item>
      <template #label>
        <div class="info-desc">
          <span>描述</span>
          <el-button style="z-index: 1" type="primary" :disabled="!verifyPermissionIds.includes(智能事件中心_DICT事件管理_更新) || [eventState.NEW, eventState.CLOSED, eventState.AUTO_CLOSED].includes((props.data.eventState as eventState) || ('' as eventState))" @click="handleDescEdit">{{ isEdit ? "保存" : "编辑" }}</el-button>
        </div>
      </template>
      <div class="tw-flex tw-min-h-[120px] tw-w-full tw-flex-col" v-if="isEdit" @keyup.enter.stop>
        <!-- <QuillEditor theme="snow" style="flex: 1" :content="isEdit ? form.description : props.data.description" @update:content="form.description = $event" contentType="html" toolbar="full" :enable="isEdit" :read-only="!isEdit"></QuillEditor> -->
        <el-input v-model="form.description" :rows="6" type="textarea" placeholder="请输入描述" />
      </div>
      <div class="el-input el-input__wrapper tw-flex tw-min-h-[120px] tw-w-full tw-flex-col tw-items-start tw-justify-start" v-else @keyup.enter.stop>
        <div>{{ props.data.description }}</div>
      </div>
    </el-form-item>
    <el-form-item label="外部ID">
      <el-input :model-value="isEdit ? form.externalId : props.data.externalId" @update:model-value="form.externalId = $event" :disabled="!isEdit"></el-input>
    </el-form-item>
    <el-form-item>
      <el-row class="el-input el-input__wrapper">
        <el-col :span="12" class="tw-h-[calc(var(--el-component-size)*3)] tw-text-left">
          <p>修改</p>
          <p class="tw-h-[var(--el-component-size)]">
            <el-icon :size="16" class="tw-mr-[6px] tw-align-middle"><UserFilled /></el-icon>{{ updated.name || "--" }}
          </p>
          <p>{{ updated.updateTime ? moment(`${updated.updateTime}`, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</p>
        </el-col>
        <el-col :span="12" class="tw-h-[calc(var(--el-component-size)*3)] tw-text-right">
          <p>创建</p>
          <p class="tw-h-[var(--el-component-size)]">
            <el-icon :size="16" class="tw-mr-[6px] tw-align-middle"><UserFilled v-if="props.data.manualCreate" /><Icon v-else name="local-DeviceWifi-fill" /></el-icon>{{ collector.name || "--" }}
          </p>
          <p>{{ collector.collectTime ? moment(`${collector.collectTime}`, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</p>
        </el-col>
      </el-row>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, watch, computed, inject } from "vue";
import { useRoute, useRouter } from "vue-router";

import { ElMessage } from "element-plus";

import { UserFilled } from "@element-plus/icons-vue";
import { setEventDataByDescription, eventState, setEventDataByKbcode } from "@/views/pages/apis/dictEvent";

import { QuillEditor } from "@vueup/vue-quill";
import moment from "moment";
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
import { 智能事件中心_DICT事件管理_可读, 智能事件中心_DICT事件管理_更新 } from "@/views/pages/permission";
const timeZoneHours = ref(_timeZoneHours);

defineOptions({ name: "ModelDetails" });

const verifyPermissionIds = inject("verifyPermissionIds") as string[];

const props = withDefaults(defineProps<{ data: Partial<import("../helper").DataItem> & Record<string, any>; height: number; refresh: () => Promise<void> }>(), { data: () => ({}) });
const emits = defineEmits(["changeDesc"]);

const route = useRoute();
const router = useRouter();

const form = ref({ description: "", externalId: "", kbServiceCode: "" });
const isEdit = ref(false);
const isEditKb = ref(false);

const updated = reactive({ name: "", updateTime: 0 });
const collector = reactive({ name: "", collectTime: 0 });
import getUserInfo from "@/utils/getUserInfo";
const userInfo = getUserInfo();

const kbDesc = computed(() => `${props.data.projectName ? "项目名称：" + props.data.projectName + ";" : ""}${props.data.projectCode ? "项目编号：" + props.data.projectCode + ";" : ""}${props.data.kbServiceCode ? "客保工单号：" + props.data.kbServiceCode + ";" : ""}${props.data.serviceCode ? "服务工单号：" + props.data.serviceCode + ";" : ""}${props.data.uniformServiceCode ? "统一服务编号：" + props.data.uniformServiceCode + ";" : ""}`);
function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == userInfo.zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
watch(
  () => props.data,
  async (data) => {
    await nextTick();
    if (data.collector || data.createdBy) collector.name = data.manualCreate ? JSON.parse(data.createdBy as string)?.username : data.collector;
    collector.collectTime = Math.max(Number(data.createTime) || 0, 0) + timeZoneSwitching();
    try {
      updated.name = JSON.parse(data.updatedBy || "{}").username || "";
    } catch (error) {
      updated.name = "";
    }
    updated.updateTime = Math.max(Number(data.updateTime) || 0, 0) + timeZoneSwitching();
  },
  { immediate: true }
);

async function handleDescEdit() {
  if (isEdit.value) {
    emits("changeDesc", form.value.description);
    isEdit.value = false;
    try {
      const { success, message } = await setEventDataByDescription({ id: props.data.id as string, desc: form.value.description, externalId: form.value.externalId });
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      props.refresh();
    }
  } else {
    form.value.description = props.data.description || "";
    form.value.externalId = props.data.externalId || "";
    isEdit.value = true;
  }
}

async function handleKbcodeEdit() {
  if (isEditKb.value) {
    // emits("changeDesc", form.value.description);
    isEditKb.value = false;
    try {
      // const { success, message } = await setEventDataByDescription({ id: props.data.id as string, desc: form.value.description, externalId: form.value.externalId });
      // if (!success) throw new Error(message);
      const { message, success } = await setEventDataByKbcode({
        id: props.data.id as string,
        kbServiceCode: form.value.kbServiceCode,
      });
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      props.refresh();
    }
  } else {
    console.log(props.data);
    form.value.kbServiceCode = props.data.kbServiceCode || "";
    form.value.externalId = props.data.externalId || "";
    isEditKb.value = true;
  }
}
</script>

<style lang="scss" scoped>
.info-desc {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
