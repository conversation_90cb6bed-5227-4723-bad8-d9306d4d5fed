<template>
  <pageTemplate :show-paging="false" :height="height">
    <template #default>
      <el-row :gutter="24">
        <el-col :span="12">
          <h3 style="font-size: 18px; color: #606266; margin-bottom: 10px">响应</h3>
          <el-table v-loading="state.loading" :data="state.data.resp" :height="height - 60" :style="{ width: `100%`, margin: '0 auto' }">
            <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :min-width="column.width || 120" :="column.showOverflowTooltip" :formatter="column.formatter" />
          </el-table>
        </el-col>

        <el-col :span="12">
          <h3 style="font-size: 18px; color: #606266; margin-bottom: 10px">解决</h3>

          <el-table v-loading="state.loading" :data="state.data.resolve" :height="height - 60" :style="{ width: `100%`, margin: '0 auto' }">
            <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :min-width="column.width || 120" :="column.showOverflowTooltip" :formatter="column.formatter" />
          </el-table>
        </el-col>
      </el-row>
    </template>
  </pageTemplate>
</template>

<script lang="ts" setup>
import { ref, inject, toRefs, reactive, onMounted, nextTick, h, watch } from "vue";

import { sizes } from "@/utils/common";

import { timeFormat } from "@/utils/date";

import { urgencyTypeOption } from "@/views/pages/apis/event";

import pageTemplate from "@/components/pageTemplate.vue";

import type { TableColumnCtx } from "element-plus";

import { ElMessage } from "element-plus";

import { dictEventSla as getData, Resp as DataItem } from "@/views/pages/apis/eventManage";

import { EventItem, type priority } from "@/views/pages/apis/event";

import _timeZoneHours from "@/views/pages/common/offsetHours.json";
import getUserInfo from "@/utils/getUserInfo";
const timeZoneHours = ref(_timeZoneHours);
import moment from "moment";
function timeZoneSwitching(): number {
  const timeZone = timeZoneHours.value.find((item) => item.zoneId == getUserInfo().zoneId);
  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
// import { useRoute } from "vue-router";

// const route = useRoute();
interface Props {
  height: number;
  data: Partial<EventItem>;
}

// const width = inject("width", ref(0));

const props = withDefaults(defineProps<Props>(), {
  height: 0,
  data: () => <Partial<EventItem>>{},
});

const { height, data: detail } = toRefs(props);

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: TableColumnCtx<DataItem>, value: T[keyof T]) => string | import("vue").VNode; showOverflowTooltip?: boolean }[];
  data: {
    resp: T[];
    resolve: T[];
  };
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<Partial<DataItem>>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  column: [
    {
      key: "urgencyType",
      label: "SLA状态",
      formatter: (_row, _col, v) => {
        const urgencyType = urgencyTypeOption.find((el) => el.value === v);
        return h("span", { class: " tw-rounded-4xl tw-text-white slaStatus", style: { background: urgencyType?.color, padding: "3px 15px", borderRadius: "15px" } }, `${v?.charAt(0)}${v?.substring(1).toLowerCase()}`);
      },
    },
    {
      key: "name",
      label: "级别",
      formatter: (_row, _col, v) => {
        return `${_row.name ? _row.name + "-" : ""}${_row.description ? _row.description + "-" : ""}${_row.definition ? _row.definition : "-"}`;
      },
    },
    {
      key: "toleranceTime",
      label: "容忍时间",
      formatter: (_row, _col, v) => {
        return (
          ["day", "hour", "minute"].reduce((k: string, p: string) => {
            if (Number(_row[p as keyof DataItem])) k += `${_row[p as keyof DataItem]}${p === "minute" ? "min" : p.charAt(0)}`;
            return k;
          }, "") || "--"
        );
      },
    },
    // {
    //   key: "createTime",
    //   label: "创建时间",
    //   formatter: (_row, _col, v) => {
    //     try {
    //       return v;
    //     } catch {
    //       return v || "--";
    //     }
    //   },
    // },
  ],
  data: {
    resp: [],
    resolve: [],
  },
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

watch(
  () => props.data,

  (v) => {
    if (v.slaSnapshotId) getEventSla();
    else {
      state.data.resolve = [];
      state.data.resp = [];
    }
  },
  { immediate: true }
);

async function getEventSla() {
  let queryS = {
    slaSnapshotId: detail.value.slaSnapshotId as string,
    // priority: detail.value.manualCreate == true ? (detail.value.priority as priority) : (detail.value.originPriority as priority),
    priority: detail.value.priority as priority,
  };
  try {
    const { success, data, message } = await getData(queryS);
    if (!success) throw new Error(message);
    const order = ["WARNING", "IMMINENT", "BREACH"];
    data.resolve = data.resolve instanceof Array && data.resolve.length ? (order.map((v) => data.resolve.find((f) => f.urgencyType === v)) as DataItem[]) : [];
    data.resp = data.resp instanceof Array && data.resp.length ? (order.map((v) => data.resp.find((f) => f.urgencyType === v)) as DataItem[]) : [];
    const newTime = timeFormat(Number(data.createTime));
    const TimeS = moment(moment(newTime, "YYYY-MM-DD HH:mm:ss").valueOf() + timeZoneSwitching()).format("YYYY-MM-DD HH:mm:ss");
    state.data.resolve = data.resolve.map((v) => ({ ...v, createTime: data.createTime != "0" ? moment(Number(data.createTime) + timeZoneSwitching()).format("YYYY-MM-DD HH:mm:ss") : TimeS }));
    state.data.resp = data.resp.map((v) => ({ ...v, createTime: data.createTime != "0" ? moment(Number(data.createTime) + timeZoneSwitching()).format("YYYY-MM-DD HH:mm:ss") : TimeS }));
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

// onMounted(async () => {
//   await nextTick();
//   getEventSla();
// });

// function formatter(_row: DataItem, _col: TableColumnCtx<DataItem>, v: DataItem[keyof DataItem]): string | import("vue").VNode {
//   return "---";
// }
</script>

<style lang="scss" scoped>
:v-deep(.slaStatus) {
  padding: 0 20px !important;
  box-sizing: border-box;
}
</style>
