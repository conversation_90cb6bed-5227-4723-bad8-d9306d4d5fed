<template>
  <el-form :model="form" label-position="top">
    <el-form-item>
      <template #label>
        <div class="info-desc">
          <span>描述</span>
          <el-button style="z-index: 1" type="primary" @click="handleDescEdit">{{ isEdit ? "保存" : "编辑" }}</el-button>
        </div>
      </template>
      <div class="tw-flex tw-min-h-[250px] tw-w-full tw-flex-col" v-if="isEdit" @keyup.enter.stop>
        <QuillEditor theme="snow" style="flex: 1" :content="isEdit ? form.description : props.data.description" @update:content="form.description = $event" contentType="html" toolbar="full" :enable="isEdit" :read-only="!isEdit"></QuillEditor>
      </div>
      <div class="el-input el-input__wrapper tw-flex tw-min-h-[120px] tw-w-full tw-flex-col tw-justify-start tw-p-4" v-else @keyup.enter.stop>
        <div class="tw-mx-auto" v-html="props.data.description"></div>
      </div>
    </el-form-item>
    <el-form-item label="外部ID">
      <el-input :model-value="isEdit ? form.externalId : props.data.externalId" @update:model-value="form.externalId = $event" :disabled="!isEdit"></el-input>
    </el-form-item>
    <el-form-item>
      <el-row class="el-input el-input__wrapper">
        <el-col :span="12" class="tw-h-[calc(var(--el-component-size)*3)] tw-text-left">
          <p>修改</p>
          <p class="tw-h-[var(--el-component-size)]">
            <el-icon :size="16" class="tw-mr-[6px] tw-align-middle"><UserFilled /></el-icon>{{ updated.name || "--" }}
          </p>
          <p>{{ updated.updateTime ? moment(`${updated.updateTime}`, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</p>
        </el-col>
        <el-col :span="12" class="tw-h-[calc(var(--el-component-size)*3)] tw-text-right">
          <p>创建</p>
          <p class="tw-h-[var(--el-component-size)]">
            <el-icon :size="16" class="tw-mr-[6px] tw-align-middle"><Icon name="local-DeviceWifi-fill" /></el-icon>{{ collector.name || "--" }}
          </p>
          <p>{{ collector.collectTime ? moment(`${collector.collectTime}`, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</p>
        </el-col>
      </el-row>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, watch } from "vue";
import { useRoute, useRouter } from "vue-router";

import { ElMessage } from "element-plus";

import { UserFilled } from "@element-plus/icons-vue";
import { setEventDataByDescription } from "@/views/pages/apis/event";

import { QuillEditor } from "@vueup/vue-quill";
import moment from "moment";

defineOptions({ name: "ModelSubtotal" });

const props = withDefaults(defineProps<{ data: Partial<import("../helper").DataItem>; height: number; refresh: () => Promise<void> }>(), { data: () => ({}) });
const emits = defineEmits<{}>();

const route = useRoute();
const router = useRouter();

const form = ref({ description: "", externalId: "" });
const isEdit = ref(false);

const updated = reactive({ name: "", updateTime: 0 });
const collector = reactive({ name: "", collectTime: 0 });

watch(
  () => props.data,
  async (data) => {
    await nextTick();
    if (data.collector) collector.name = data.collector;
    collector.collectTime = Math.max(Number(data.collectTime) || 0, 0);
    try {
      updated.name = JSON.parse(data.updatedBy || "{}").username || "";
    } catch (error) {
      updated.name = "";
    }
    updated.updateTime = Math.max(Number(data.updateTime) || 0, 0);
  },
  { immediate: true }
);

async function handleDescEdit() {
  if (isEdit.value) {
    try {
      const { success, message } = await setEventDataByDescription({ id: props.data.id as string, desc: form.value.description, externalId: form.value.externalId });
      if (!success) throw new Error(message);
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      isEdit.value = false;
      props.refresh();
    }
  } else {
    isEdit.value = true;
    form.value.description = props.data.description || "";
    form.value.externalId = props.data.externalId || "";
  }
}
</script>

<style lang="scss" scoped>
.info-desc {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
