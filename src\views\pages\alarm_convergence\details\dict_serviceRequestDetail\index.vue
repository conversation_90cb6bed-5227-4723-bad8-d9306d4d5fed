<template>
  <div class="tw-flex tw-h-[50px] tw-flex-nowrap tw-items-center tw-pb-[20px]">
    <el-page-header class="tw-w-full" :content="'服务请求详情   ' + route.params.id" @back="handleCancel()">
      <template #extra>
        <div>
          <el-row :gutter="8" type="flex" justify="end" align="middle">
            <el-col :span="7" :style="{ display: 'flex', justifyContent: 'flex-end' }">
              <el-button-group class="tw-flex">
                <el-button :type="(currentEventState.type as '') || ''" :disabled="state.data.operation === serviceOperation.CLOSE" @click.stop>{{ currentEventState.label || "" }}</el-button>
                <el-dropdown @command="$event.command()">
                  <el-button :type="(currentEventState.type as '') || ''" @click.stop>
                    <template v-if="!!operation">{{ (find(serviceOperationOption, (v) => v.value === operation) || {}).label }}</template>
                    <template v-else-if="!!stateRightText">{{ stateRightText }}</template>
                    <template v-else>
                      <template v-if="state.data.serviceState === serviceState.PROCESSING">处理</template>
                      <template v-else-if="state.data.serviceState === serviceState.NEW">新建</template>
                      <template v-else-if="state.data.serviceState === serviceState.CUSTOMER_SUSPENDED">客户挂起</template>
                      <template v-else-if="state.data.serviceState === serviceState.SERVICE_PROVIDER_SUSPENDED">供应商挂起</template>

                      <template v-else-if="state.data.serviceState === serviceState.COMPLETED">完成</template>
                      <template v-else-if="state.data.operation === serviceOperation.CLOSE || state.data.serviceState === serviceState.AUTO_CLOSED || state.data.serviceState === serviceState.CLOSED">关闭</template>
                    </template>
                    <el-icon class="tw-ml-2"><ArrowDown></ArrowDown></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu class="tw-min-w-[100px]">
                      <el-dropdown-item
                        v-for="item in [
                          { command: serviceState.PROCESSING, /*          */ execute: () => handleAccept(state.data), /*                                    */ center: '处理', /*     */ disabled: [serviceState.PROCESSING].includes(state.data.serviceState || ('' as serviceState)) || (!verifyPermissionIds.includes(智能事件中心_DICT服务请求_更新) && !verifyPermissionIds.includes(智能事件中心_工单_更新)) },
                          { command: serviceState.CUSTOMER_SUSPENDED, /*  */ execute: () => handleApprove(state.data, serviceState.CUSTOMER_SUSPENDED), /*  */ center: '客户挂起' /*  */, disabled: [serviceState.NEW].includes(state.data.serviceState || ('' as serviceState)) || [serviceOperation.CUSTOMER_SUSPENDED].includes(state.data.operation as serviceOperation) },
                          { command: serviceState.SERVICE_PROVIDER_SUSPENDED, /*  */ execute: () => handleApprove(state.data, serviceState.SERVICE_PROVIDER_SUSPENDED), /*  */ center: '供应商挂起' /*  */, disabled: [serviceState.NEW].includes(state.data.serviceState || ('' as serviceState)) || [serviceOperation.SERVICE_PROVIDER_SUSPENDED].includes(state.data.operation as serviceOperation) },

                          { command: serviceState.COMPLETED, /*           */ execute: () => handleEnd(state.data, 'Finish'), /*                             */ center: '完成', /*     */ disabled: [serviceState.NEW, serviceState.COMPLETED].includes(state.data.serviceState || ('' as serviceState)) || state.data.operation === serviceOperation.FINISHED },
                          { command: serviceState.CLOSED, /*              */ execute: () => handleEnd(state.data, 'Close'), /*                              */ center: '关闭', /*     */ disabled: !orderIsClose },
                        ]"
                        :key="item.command"
                        :command="{ command: item.execute }"
                        :disabled="state.loading || item.disabled"
                      >
                        {{ item.center }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-button-group>

              <!-- -->
            </el-col>
            <el-col :span="7">
              <!-- (find(userGroups, (v) => v.id === (state.data.displayUserGroupId || state.data.currentOwnerId)) || {}).id -->
              <el-select :model-value="transferOrUpgrade.userGroupId" @change="handleSetTransferOrUpgrade({ type: typeOfTransferOrUpgrade, userGroupId: $event, userId: '' }, state.data)" placeholder="请选择用户组" filterable :disabled="[serviceState.CLOSED].includes(state.data.serviceState || ('' as serviceState)) || (!userInfo.hasPermission(智能事件中心_DICT服务请求_分配用户组) && !userInfo.hasPermission(智能事件中心_工单_分配用户组))">
                <el-option v-for="userGroup in userGroups" :key="userGroup.id" :label="`${userGroup.name}${userGroup.tenantAbbreviation ? '[' + userGroup.tenantAbbreviation + ']' : ''}`" :value="userGroup.id"></el-option>
              </el-select>
            </el-col>
            <el-col :span="7">
              <!-- (find(userList, (v) => v.userId === state.data.currentOwnerId) || {}).userId -->
              <el-select :model-value="transferOrUpgrade.userId" @change="handleSetTransferOrUpgrade({ type: typeOfTransferOrUpgrade, userGroupId: transferOrUpgrade.userGroupId, userId: $event }, state.data)" placeholder="请选择用户" filterable :disabled="[serviceState.CLOSED].includes(state.data.serviceState || ('' as serviceState)) || (!userInfo.hasPermission(智能事件中心_DICT服务请求_分配用户) && !userInfo.hasPermission(智能事件中心_工单_分配用户))">
                <el-option v-for="user in userList" :key="user.id" :label="user.name + `(${user.account}@${user.tenantAbbreviation})`" :value="user.id"></el-option>
              </el-select>
            </el-col>
            <!-- <el-col :span="3" :style="{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }">
              <el-checkbox v-model="typeOfTransferOrUpgrade" false-label="transfer" true-label="eventUpgrade" style="margin-right: 5px" :disabled="[, , serviceState.CLOSED].includes(state.data.serviceState || '' as serviceState)">升级</el-checkbox>
              <el-tooltip class="item" effect="dark" content="若需要升级请先点选升级复选框" placement="bottom">
                <el-icon class="tipIcon"><InfoFilled></InfoFilled></el-icon>
              </el-tooltip>
            </el-col> -->
            <el-col :span="3" :style="{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }">
              <el-button type="primary" :disabled="!verifyPermissionIds.includes(智能事件中心_DICT服务请求_更新) && !verifyPermissionIds.includes(智能事件中心_工单_更新)" v-preventReClick @click="handleServiceSave">保存</el-button>
            </el-col>
          </el-row>
        </div>
      </template>
    </el-page-header>
  </div>
  <el-scrollbar class="tw-h-fit" :height="height - 50">
    <el-card v-loading="state.loading" class="tw-mb-[18px]" style="max-height: 500px">
      <template #header>
        <div style="display: flex; justify-content: space-between">
          <el-button link type="primary" class="tw-font-semibold" :style="{ color: 'var(--el-color-primary)' }" :disabled="[serviceState.CLOSED, serviceState.AUTO_CLOSED].includes(state.data.serviceState)" v-preventReClick @click="handleEditSummary">{{ state.data.title || "--" }}</el-button>

          <div style="display: flex">
            <div>
              {{ tickGroupConfig.ticketClassificationNames.join("/") }}
            </div>
          </div>
        </div>
      </template>
      <template #default>
        <FormModel :model="form" :style="{ marginBottom: '-18px' }">
          <!-- <FormItem :span="8" label="创建时间" tooltip="" prop="" :rules="[]">{{ state.data.collectTime ? moment(state.data.collectTime, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</FormItem> -->
          <!-- <FormItem :span="8" label="持续时间" tooltip="" prop="" :rules="[]">{{ state.data.collectTime ? moment(state.data.collectTime, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</FormItem> -->
          <FormItem :span="6" label="优先级" tooltip="" prop="" :rules="[]">
            <el-dropdown trigger="click" @command="handleSetPriority($event, state.data)" :disabled="[serviceState.COMPLETED, serviceState.CLOSED].includes(state.data.serviceState || ('' as serviceState)) || state.data.operation === serviceOperation.CLOSE">
              <span class="el-dropdown-link">
                <i class="priority-icon" :style="{ backgroundColor: currentEventState.color || '' }" />
                <span class="tw-align-[2px]" :style="{ color: currentPriority.color || '' }">{{ state.data.priority }}</span>
                <el-icon class="el-icon--right"><ArrowDown></ArrowDown></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="(value, key) in priorityOption" :key="`priority-${key}`" :command="value.value">{{ value.label }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </FormItem>
          <FormItem :span="6" label="紧急性" tooltip="" prop="" :rules="[]">
            <el-select :model-value="state.data.urgency || ('' as string)" filterable @change="handleSetSeverity($event, state.data)" :disabled="[serviceState.COMPLETED, serviceState.CLOSED].includes(state.data.serviceState || ('' as serviceState)) || state.data.operation === serviceOperation.CLOSE">
              <el-option v-for="item in deviceImportanceOption.map((v) => ({ ...v, priority: priorityMatrix.filter((raw) => raw.eventSeverity === v.value) })).filter((v) => v.priority.length)" :key="item.value" :label="`${item.label}`" :value="item.value"></el-option>
              <!-- item.priority.map(v => `${v.priority}：${(find(deviceImportanceOption, (raw) => raw.value === v.deviceImportance) || {}).label || v.deviceImportance}`).join('，') -->
            </el-select>
          </FormItem>
          <FormItem :span="6" label="影响性" tooltip="" prop="" :rules="[]">
            <el-select :model-value="state.data.influence || ('' as string)" filterable @change="handleSetImportance($event, state.data)" :disabled="[serviceState.COMPLETED, serviceState.CLOSED].includes(state.data.serviceState || ('' as serviceState)) || state.data.operation === serviceOperation.CLOSE">
              <el-option v-for="item in deviceImportanceOption.map((v) => ({ ...v, priority: priorityMatrix.filter((raw) => raw.deviceImportance === v.value) }))" :key="item.value" :label="`${item.label}`" :value="item.value"></el-option>
              <!-- item.priority.map(v => `${v.priority}：${(find(eventSeverityOption, (raw) => raw.value === v.eventSeverity) || {}).label || v.eventSeverity}`).join('，') -->
            </el-select>
          </FormItem>

          <!-- <el-col :span="24" class="tw-mb-[18px] tw-bg-[var(--el-bg-color-page)] tw-py-2">SLA情况</el-col>
        <FormItem :span="8" label="响应时限情况" tooltip="" prop="" :rules="[]">
          <el-progress :percentage="responseTimePercentage" :color="resolveUrgencyType.color" :format="() => `${state.data.responseTime || 0}分钟 / ${state.data.responseLimit || 0}分钟`" class="tw-w-full"></el-progress>
        </FormItem>
        <el-col :span="1"></el-col>
        <FormItem :span="8" label="解决时限情况" tooltip="" prop="" :rules="[]">
          <el-progress :percentage="resolveTimePercentage" :color="resolveUrgencyType.color" :format="() => `${state.data.resolveTime || 0}分钟 / ${state.data.resolveLimit || 0}分钟`" class="tw-w-full"></el-progress>
        </FormItem> -->

          <el-col :span="24" class="tw-mb-[18px] tw-bg-[var(--el-bg-color-page)] tw-py-2" v-if="Number(slaTimeLimit.responseLimit) || Number(slaTimeLimit.resolveLimit)">SLA情况</el-col>
          <FormItem :span="12" label="响应时限情况" tooltip="" prop="" :rules="[]" v-if="Number(slaTimeLimit.responseLimit)">
            <!-- v-->
            <el-progress :percentage="responseTimePercentage" :color="state.data.serviceState != 'NEW' ? '#ccc' : responseUrgencyType.color" :format="() => `${convertMinutes(slaTimeLimit.responseTime) || 0} / ${convertMinutes(slaTimeLimit.responseLimit) || 0}`" class="tw-w-full"></el-progress>
          </FormItem>
          <FormItem :span="12" label="解决时限情况" tooltip="" prop="" :rules="[]" v-if="Number(slaTimeLimit.resolveLimit)">
            <!-- -->
            <el-progress :percentage="resolveTimePercentage" :color="[serviceState.COMPLETED, serviceState.CLOSED, serviceState.AUTO_CLOSED].includes(state.data.serviceState || ('' as serviceState)) ? '#ccc' : resolveUrgencyType.color" :format="() => `${convertMinutes(slaTimeLimit.resolveTime) || 0} / ${convertMinutes(slaTimeLimit.resolveLimit) || 0}`" class="tw-w-full"></el-progress>
          </FormItem>

          <el-col :span="24"></el-col>
          <template v-if="[serviceState.SUSPENDED, serviceState.CUSTOMER_SUSPENDED, serviceState.SERVICE_PROVIDER_SUSPENDED, serviceState.PENDING_APPROVAL].includes(state.data.serviceState as serviceState as never)">
            <FormItem :span="6" label="暂停，直到" tooltip="" prop="" :rules="[]" v-if="state.data.currentSuspendPauseTime">{{ moment(Number(state.data.currentSuspendPauseTime)).format("YYYY-MM-DD HH:mm:ss") }}</FormItem>
            <FormItem :span="6" label="请求，直到" tooltip="" prop="" :rules="[]" v-if="state.data.currentSuspendRequestTime && ![serviceState.SUSPENDED].includes(state.data.serviceState as serviceState as never)">{{ moment(Number(state.data.currentSuspendRequestTime)).format("YYYY-MM-DD HH:mm:ss") }}</FormItem>
            <FormItem v-if="state.data.suspendRecordCause" :span="6" style="font-weight: 700">挂起原因: {{ state.data.suspendRecordCause }}</FormItem>
          </template>
          <FormItem :span="6" label="" tooltip="" prop="" :rules="[]" v-if="state.data.currentSuspendRequestTime && state.data.pendingSuspend && [serviceState.SUSPENDED, serviceState.CUSTOMER_SUSPENDED, serviceState.SERVICE_PROVIDER_SUSPENDED, serviceState.PENDING_APPROVAL].includes(state.data.serviceState || ('' as serviceState))">
            <el-button type="primary" :disabled="!tickGroupConfig.orderApprovalPermission" v-preventReClick @click="handleApproveHangUp(true)">通过</el-button>
            <el-button type="danger" :disabled="!tickGroupConfig.orderApprovalPermission" v-preventReClick @click="handleApproveHangUp(false)">拒绝</el-button>
          </FormItem>
        </FormModel>
        <div></div>
        <!-- <pre>{{ state.data }}</pre> -->
      </template>
    </el-card>
    <completeCode v-if="[serviceState.CLOSED, serviceState.AUTO_CLOSED].includes(state.data.serviceState || ('' as serviceState))" class="tw-mb-[18px]" :title="currentEventState.label || ''" :finishCodeName="state.data.finishCodeName || ''" :finishCodeDesc="state.data.finishCodeDesc || ''" :finishContent="state.data.finishContent || ''" />
    <el-card v-loading="state.loading">
      <template #header>
        <el-radio-group :model-value="(route.query.type as string) || ''" @change="router.push({ query: { ...route.query, type: $event as string } })">
          <el-radio-button :label="pageType.details">
            <el-badge class="mark"> <div class="tw-px-[1em]">详述</div> </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.subtotal">
            <el-badge class="mark" :value="tabCounts.noteCount || undefined"> <div class="tw-px-[1em]">小记</div> </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.device">
            <el-badge class="mark" :value="tabCounts.deviceCount || undefined"> <div class="tw-px-[1em]">设备</div> </el-badge></el-radio-button
          >
          <el-radio-button :label="pageType.contacts">
            <el-badge class="mark" :value="tabCounts.contactCount || undefined"> <div class="tw-px-[1em]">联系人</div> </el-badge></el-radio-button
          >
          <el-radio-button :label="pageType.project">
            <el-badge class="mark" :value="undefined">
              <div class="tw-px-[1em]">项目</div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.related" :disabled="!userInfo.hasPermission(智能事件中心_DICT服务请求_可读) && !userInfo.hasPermission(智能事件中心_工单_可读)">
            <el-badge class="mark" :value="tabCounts.relationCount || undefined"> <div class="tw-px-[1em]">关联</div> </el-badge></el-radio-button
          >
          <el-radio-button :label="pageType.alarm">
            <el-badge class="mark"> <div class="tw-px-[1em]">告警记录</div> </el-badge></el-radio-button
          >
          <!-- <el-radio-button :label="pageType.sla" :disabled="!userInfo.hasPermission(智能事件中心_DICT服务请求_可读)">
            <el-badge class="mark"> <div class="tw-px-[1em]">SLA</div> </el-badge></el-radio-button
          > -->
          <el-radio-button :label="pageType.strategy">
            <el-badge class="mark" :value="Number(tabCounts.actionCount) || undefined"> <div class="tw-px-[1em]">行动策略</div> </el-badge></el-radio-button
          >
          <el-radio-button :label="pageType.file">
            <el-badge class="mark" :value="tabCounts.fileCount || undefined"> <div class="tw-px-[1em]">文件</div> </el-badge></el-radio-button
          >
          <el-radio-button :label="pageType.dynamics">
            <el-badge class="mark"> <div class="tw-px-[1em]">历史日志</div> </el-badge></el-radio-button
          >
        </el-radio-group>
      </template>
      <template #default>
        <el-scrollbar>
          <div v-if="((route.query.type as string) || '') === pageType.details">
            <!-- 详述 -->
            <ModelDetails :data="state.data" :refresh="handleRefresh" @changeDesc="(v) => (state.data.description = v)" :height="0"></ModelDetails>
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.subtotal">
            <!-- 小计 -->
            <ModelNotes ref="modelNotesRef" :data="state.data" :refresh="handleRefresh" :height="0"></ModelNotes>
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.device">
            <!-- 设备 -->
            <ModelDevices :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.contacts">
            <!-- 联系人 -->
            <ModelContacts :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>

          <div v-if="((route.query.type as string) || '') === pageType.project">
            <!-- 项目 -->
            <ModelProject :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.related">
            <!-- 关联 -->
            <ModelAssociation :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.alarm">
            <!-- 告警记录 -->
            <ModelAlarm :data="state.data" :refresh="handleRefresh" :handleEnd="handleEnd" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.sla">
            <!-- SLA -->
            <ModelSLA :data="state.data" :refresh="handleRefresh" :height="0"></ModelSLA>
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.strategy">
            <!-- 行动策略 -->
            <ModelStrategy :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.file">
            <!-- 文件 -->
            <ModelFiles :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.dynamics">
            <!-- 动态 -->
            <ModelDynamics :data="state.data" :refresh="handleRefresh" :height="0"></ModelDynamics>
          </div>
        </el-scrollbar>
      </template>
    </el-card>
  </el-scrollbar>

  <EventPend
    ref="pendRef"
    :ticketTemplateId="state.data.ticketTemplateId"
    :refresh="handleRefresh"
    @pend="
      (v) => {
        pendQuery = v.params;
        operation = v.type;
      }
    "
  ></EventPend>

  <EventEnd
    ref="endRef"
    :refresh="handleRefresh"
    :ticketTemplateId="state.data.ticketTemplateId"
    @end="
      (v) => {
        // console.log(v.params);
        notesQuery = v.notesForm;
        completeQuery = v.params;
        operation = v.type === 'Finish' ? serviceOperation.FINISHED : serviceOperation.CLOSE;
      }
    "
    :height="height - 317"
  ></EventEnd>

  <Editor ref="editorRef" title="事件" display="dialog">
    <template #setPriority="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          确定设置
          <span>{{ ((params.raw || {}) as AnyObject).summary as string }}</span>
          事件优先级为
          <span>{{ params.label }}</span>
          吗？
        </p>
      </div>
    </template>
    <template #setImportance="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          确定设置
          <span>{{ ((params.raw || {}) as AnyObject).summary as string }}</span>
          事件重要性为
          <span>{{ params.label }}</span>
          吗？
        </p>
      </div>
    </template>
    <template #setSeverity="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          确定设置
          <span>{{ ((params.raw || {}) as AnyObject).summary as string }}</span>
          事件紧急性为
          <span>{{ params.label }}</span>
          吗？
        </p>
      </div>
    </template>
  </Editor>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch, computed, h, provide, toRefs } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Refresh, InfoFilled, ArrowDown } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";
import { ElTable, ElIcon } from "element-plus";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import ModelDetails from "./models/details.vue";
import ModelSubtotal from "./models/subtotal.vue";
import ModelSLA from "./models/sla.vue";
import ModelDynamics from "./models/dynamics.vue";

import ModelNotes from "./models/notes.vue";

import ModelDevices from "./models/devices.vue";

import ModelContacts from "./models/contacts.vue";

import ModelAssociation from "./models/association.vue";

import ModelFiles from "./models/files.vue";

import ModelAlarm from "./models/alarm.vue";

import ModelStrategy from "./models/strategy.vue";

import ModelProject from "./models/project.vue";

import EventPend from "./models/pend.vue";

import EventEnd from "./models/end.vue";

import Editor from "./Editor.vue";

import completeCode from "@/components/completeCode.vue";

/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox } from "element-plus";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { useSiteConfig } from "@/stores/siteConfig";
import getUserInfo from "@/utils/getUserInfo";
import { find } from "lodash-es";
import moment from "moment";

import { type BaseItem, DataItem, type Item } from "./helper";
import { state } from "./helper";
import { resetData, command } from "./helper";

/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
import { serviceState, serviceStateOption, eventSeverity, eventSeverityOption, priority, priorityOption, urgencyType, urgencyTypeOption, serviceOperation, serviceOperationOption } from "@/views/pages/apis/event";
import { deviceImportance, deviceImportanceOption } from "@/views/pages/apis/device";

// import { getEventData as getItemData } from "@/views/pages/apis/event";

import { getDictServiceRequestDeteil as getItemData, serviceRequestUpdatePriority as setEventDataByPriority, setServiceRequestDataByImportance, setServiceRequestDataBySeverity, serviceRequestStart, serviceRequestTransfer, getDictDetailTapCountById, type TabCount, setDictServiceUpdateEditable } from "@/views/pages/apis/serviceRequest";
import { addEventData as addItemData, setEventData as setItemData, modEventData as modItemData, delEventData as delItemData } from "@/views/pages/apis/event";
import { setEventDataByTakeOver, setEventDataByApprove, setEventDataByTransferOrUpgrade, type TimeLimit, dictServiceApproveHangUp } from "@/views/pages/apis/event";
import { getPriorityMatrixList } from "@/views/pages/apis/eventPriority";
import { getUserGroupByPermissionIds, getUserByGroup, type GroupItem, type EntrustUserItem } from "@/api/personnel";
import { eventEditSummary as editSummary, type SlaTimeLimit, getDictServiceRequestSlaTimeLimitById } from "@/views/pages/apis/eventManage";

// import { 智能事件中心_服务请求工单_审批, 智能事件中心_服务请求工单_分配用户, 智能事件中心_服务请求工单_分配用户组 } from "@/views/pages/permission";
import { 智能事件中心_工单_可读, 智能事件中心_DICT服务请求_更新, 智能事件中心_工单_更新, 智能事件中心_DICT服务请求_分配用户组, 智能事件中心_DICT服务请求_分配用户, 智能事件中心_工单_分配用户组, 智能事件中心_工单_分配用户, 智能事件中心_DICT服务请求_审批, 智能事件中心_DICT服务请求_可读, 安全管理中心_用户组管理_可读, 智能事件中心_用户组_分配工单 } from "@/views/pages/permission";
import _timeZoneHours from "@/views/pages/common/offsetHours.json";

import { getCloseDirectly, getOrderUserGroup, AssignableTicketType, UserGroupConfigurationItem, getOrderUserGroupIsClose, getOrderApprovalPermission, getTicketClassificationNames } from "@/views/pages/apis/orderGroup";

import { 智能事件中心_服务请求工单_编辑小记, 智能事件中心_事件工单_编辑小记, 智能事件中心_DICT事件管理_编辑小记, 智能事件中心_DICT服务请求_编辑小记, 智能事件中心_变更工单_编辑小记, 智能事件中心_问题工单_编辑小记, 智能事件中心_发布管理_编辑小记 } from "@/views/pages/permission";
import { addOrderNode } from "@/views/pages/apis/event";
const timeZoneHours = ref(_timeZoneHours);

/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}

const modelNotesRef = ref();
const { t } = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "alarmBoard" });
const siteConfig = useSiteConfig();
const userInfo = getUserInfo();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
interface Props {
  width?: number;
  height?: number;
  title?: string;
}
const props = withDefaults(defineProps<Props>(), { title: "告警" });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

const _width = inject("width", ref(0));
const _height = inject("height", ref(0));

const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);

const tableRef = ref<InstanceType<typeof ElTable>>();
const editorRef = ref<InstanceType<typeof Editor>>();
provide("detailData", toRefs(state).data);

const verifyPermissionIds = ref<string[]>([]);
provide("verifyPermissionIds", verifyPermissionIds);
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Form {
  priority: string;
}
interface AnyObject {
  [key: string]: any;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const timer = ref<null | NodeJS.Timer>(null);
const autoRefreshTime = ref(0);

enum pageType {
  details = "",
  subtotal = "subtotal",
  device = "device",
  contacts = "contacts",
  related = "related",
  alarm = "alarm",
  sla = "sla",
  strategy = "strategy",
  file = "file",
  dynamics = "dynamics",
  project = "project",
}

const currentEventState = computed(() => find(serviceStateOption, (v) => v.value === state.data.serviceState) || ({} as Partial<(typeof serviceStateOption)[number]>));
const currentPriority = computed(() => find(priorityOption, (v) => v.value === state.data.priority) || ({} as Partial<(typeof priorityOption)[number]>));

const stateRightText = computed(() => (serviceOperationOption.find(({ value }) => value === state.data.operation) || {}).label);

// const responseUrgencyType = computed(() => setSlaState((state.data.slaTimeLimit || {}).responseTimeLimits || [], Number(state.data.responseTime) || 0));
// const resolveUrgencyType = computed(() => setSlaState((state.data.slaTimeLimit || {}).completedTimeLimits || [], Number(state.data.resolveTime) || 0));

function setSlaState(list: TimeLimit[], val: number, type: "response" | "resolve") {
  if (!(list instanceof Array) || !list.length) {
    const percent = {
      response: (Number(slaTimeLimit.value.responseTime) / Number(slaTimeLimit.value.responseLimit)) * 100,
      resolve: (Number(slaTimeLimit.value.resolveTime) / Number(slaTimeLimit.value.resolveLimit)) * 100,
    }[type];
    if (percent > 100) return { label: "", value: "", color: "#db3328" };
    else if (percent > 66) return { label: "", value: "", color: "#f0ad4e" };
    else if (percent > 33) return { label: "", value: "", color: "#5cb85c" };
    else return { label: "", value: "", color: "#3E97FF" };
  }
  let result = urgencyType.NORMAL;
  list.unshift({
    urgencyType: "NORMAL",
    tolerateMinutes: 0,
  });
  list.sort((a: any, b: any) => {
    return Number(a.tolerateMinutes) - Number(b.tolerateMinutes);
  });

  let index = list.findIndex((v, i) => {
    if (v.tolerateMinutes > val) {
      // console.log(i);
      return i;
    }
  });
  if (index != -1) {
    result = list[index - 1].urgencyType;
  } else {
    result = list[list.length - 1].urgencyType;
  }
  // list.sort((a, b) => (Number(b.tolerateMinutes) || 0) - (Number(a.tolerateMinutes) || 0)).forEach((el) => ((Number(el.tolerateMinutes) || 0) >= val || list.length === 1) && (result = el.urgencyType));
  return find(urgencyTypeOption, ({ value }) => value === result) || { label: "", value: "", color: "#eee" };
}

// const responseTimePercentage = computed(() => (((Number(state.data.responseTime) || 0) / (Number(state.data.responseLimit) || 0)) * 100 > 100 ? 100 : (Number(state.data.responseTime) / Number(state.data.responseLimit)) * 100) || 0);
// const resolveTimePercentage = computed(() => (((Number(state.data.resolveTime) || 0) / (Number(state.data.resolveLimit) || 0)) * 100 > 100 ? 100 : (Number(state.data.resolveTime) / Number(state.data.resolveLimit)) * 100) || 0);

const responseTimePercentage = computed(() => (((Number(slaTimeLimit.value.responseTime) || 0) / (Number(slaTimeLimit.value.responseLimit) || 0)) * 100 > 100 ? 100 : (Number(slaTimeLimit.value.responseTime) / Number(slaTimeLimit.value.responseLimit)) * 100) || 0);
const resolveTimePercentage = computed(() => (((Number(slaTimeLimit.value.resolveTime) || 0) / (Number(slaTimeLimit.value.resolveLimit) || 0)) * 100 > 100 ? 100 : (Number(slaTimeLimit.value.resolveTime) / Number(slaTimeLimit.value.resolveLimit)) * 100) || 0);

const responseUrgencyType = computed(() => setSlaState((state.data.slaTimeLimit || {}).responseTimeLimits || [], Number(slaTimeLimit.value.responseTime) || 0, "response"));
const resolveUrgencyType = computed(() => setSlaState((state.data.slaTimeLimit || {}).completedTimeLimits || [], Number(slaTimeLimit.value.resolveTime) || 0, "resolve"));

const priorityMatrix = ref<{ eventSeverity: eventSeverity; deviceImportance: deviceImportance; priority: priority }[]>([]);
const userGroups = ref<Record<string, any>[]>([]);
const userList = ref<EntrustUserItem[]>([]);
const typeOfTransferOrUpgrade = ref<"transfer" | "eventUpgrade">("transfer");

type typeTransferOrUpgrade = { userGroupId: string; userId: string };
const transferOrUpgrade = ref<typeTransferOrUpgrade>({} as typeTransferOrUpgrade);

const operation = ref<serviceOperation>("" as serviceOperation);
type PendQuery = { durationMinutes: Number; cause: string };
const pendQuery = ref<PendQuery>({} as PendQuery);

type CompleteQuery = { completeInfo: { finishCodeName: string; finishCodeDesc: string; finishContent: string }; closeAlert: boolean };
const completeQuery = ref<CompleteQuery>({} as CompleteQuery);
type NotesQuery = { content: string; privateAble: boolean };
const notesQuery = ref<NotesQuery>({} as NotesQuery);

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  handleRefresh().then(() => (autoRefreshTime.value = 60));
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {
  if (timer.value !== null) {
    clearInterval(timer.value);
    timer.value = null;
  }
}
function beforeDestroy() {
  if (timer.value !== null) {
    clearInterval(timer.value);
    timer.value = null;
  }
}
function destroyed() {}

watch(autoRefreshTime, (autoRefreshTime) => {
  if (timer.value !== null) timer.value = (clearInterval(timer.value), null);
  if (autoRefreshTime) timer.value = setInterval(getSlaTimeLimit, autoRefreshTime * 1000);
});

const slaTimeLimit = ref<SlaTimeLimit>({});
async function getSlaTimeLimit() {
  try {
    const { success, data, message } = await getDictServiceRequestSlaTimeLimitById({ id: route.params.id as string });
    if (!success) throw new Error(message);
    slaTimeLimit.value = data;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

function convertMinutes(minutes: any) {
  var seconds = minutes * 60; // 将分钟转化为秒数

  var years = Math.floor(seconds / 31536000); // 每年有31536000秒（平均）
  seconds -= years * 31536000;

  var months = Math.floor(seconds / 2592000); // 每月有2592000秒（平均）
  seconds -= months * 2592000;

  var weeks = Math.floor(seconds / 604800); // 每周有604800秒（平均）
  seconds -= weeks * 604800;

  var days = Math.floor(seconds / 86400); // 每天有86400秒（平均）
  seconds -= days * 86400;

  var hours = Math.floor(seconds / 3600); // 每小时有3600秒
  seconds -= hours * 3600;

  const mins = minutes % 60;

  return `${years > 0 ? years + "Y" : ""}${months > 0 ? months + "M" : ""}${weeks > 0 ? weeks + "W" : ""}${days > 0 ? days + "D" : ""}${hours > 0 ? hours + "H" : ""}${mins > 0 ? mins + "m" : ""}`;
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
function timeZoneSwitching(): number {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == userInfo.zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
async function handleServiceSave() {
  try {
    const params = {
      id: route.params.id,
      priority: state.data.priority,
      urgency: state.data.urgency,
      influence: state.data.influence,
      desc: state.data.description,
      externalId: state.data.externalId,
      title: state.data.title,
      operation: operation.value || null,
      completeInfo: completeQuery.value,
      ...transferOrUpgrade.value,
      ...pendQuery.value,
    };
    // console.log(params);
    const { success, data, message } = await setDictServiceUpdateEditable(params);
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    if (hasValidContent(notesQuery.value.content)) {
      submitNote();
    }
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    await handleRefresh();
  }
}
const hasValidContent = (html) => {
  if (html === undefined || html === null) {
    return false;
  }
  // 1. 过滤空标签（包括自闭合标签）
  const cleanedHtml = html.replace(/<([a-z][a-z0-9]*)\b[^>]*>(?:\s|&nbsp;)*<\/\1>|<\w+\s*\/>/gi, "");
  // 2. 移除所有空格（包括换行、制表符等）
  const trimmedContent = cleanedHtml.replace(/\s+/g, "").trim();
  // 3. 返回是否有有效内容
  return trimmedContent.length > 0;
};
async function submitNote() {
  try {
    const formData = new FormData();
    formData.append("nodeContent", notesQuery.value.content);
    formData.append("privateAble", notesQuery.value.privateAble as any);
    formData.append("privateCustomerId", userInfo.tenantId);
    formData.append("tenantId", (userInfo.currentTenant || {}).id as string);
    formData.append("orderType", "DICT_SERVICE_REQUEST");
    formData.append("permissionId", EditNodePermissionId["DICT_SERVICE_REQUEST"]);
    formData.append("orderId", route.params.id as string);
    formData.append("orderIdsJson", JSON.stringify([route.params.id]));
    const { success, message } = await addOrderNode(formData as any);
    if (!success) throw new Error(message);
    //ElMessage.success("操作成功");
    if (modelNotesRef.value) {
      modelNotesRef.value.getEventNotes(); // 安全调用
    }
    handleRefresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    await queryData();
  }
}
enum EditNodePermissionId {
  EVENT_ORDER = 智能事件中心_事件工单_编辑小记,
  SERVICE_REQUEST = 智能事件中心_服务请求工单_编辑小记,
  DICT_EVENT_ORDER = 智能事件中心_DICT事件管理_编辑小记,
  DICT_SERVICE_REQUEST = "690043142585450496", //智能事件中心_DICT服务请求_编辑小记 and 智能事件中心_小记_新增,
  CHANGE = 智能事件中心_变更工单_编辑小记,
  QUESTION = 智能事件中心_问题工单_编辑小记,
  PUBLISH = 智能事件中心_发布管理_编辑小记,
}
function handleEventOperateCommand(v: serviceState) {
  switch (v) {
    case serviceState.PROCESSING:
      // 处理中
      // handleBatchAccept(detailData);
      break;
    case serviceState.COMPLETED:
      // 完成
      // handleEventEnd(detailData, "Finish");
      break;
    case serviceState.CLOSED:
      // 关闭
      // handleEventEnd(detailData, "Close");
      break;
  }
}

// async function handleCommand(type: command, data?: Record<string, unknown>) {
//   const time = autoRefreshTime.value;
//   autoRefreshTime.value = 0;
//   try {
//     state.loading = true;
//     await nextTick();
//     switch (type) {
//       case command.Refresh:
//         await resetData();
//         await queryData();
//         break;
//       case command.Request:
//         await queryData();
//         break;
//       case command.Preview:
//         await previewItem(data as Record<string, unknown>);
//         break;
//       case command.Create:
//         await createItem(data as Record<string, unknown>);
//         await resetData();
//         await queryData();
//         break;
//       case command.Update:
//         await rewriteItem(data as Record<string, unknown>);
//         await resetData();
//         await queryData();
//         break;
//       case command.Modify:
//         await modifyItem(data as Record<string, unknown>);
//         await resetData();
//         await queryData();
//         break;
//       case command.Delete:
//         await deleteItem(data as Record<string, unknown>);
//         await resetData();
//         await queryData();
//         break;
//     }
//   } catch (error) {
//     if (error instanceof Error) {
//       const message = error.message;
//       await resetData();
//       await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
//       await queryData();
//     }
//   } finally {
//     autoRefreshTime.value = time;
//     state.loading = false;
//   }
// }
async function handleSetTransferOrUpgrade(req: { userGroupId: string; userId: string; type: "transfer" | "eventUpgrade" }, raw: Partial<DataItem>) {
  // const time = autoRefreshTime.value;
  try {
    // autoRefreshTime.value = 0;
    // state.loading = true;
    // const { success, data, message } = await serviceRequestTransfer({
    //   serviceRequestId: route.params.id as string,
    //   userGroupId: req.userGroupId,
    //   userId: req.userId,
    //   tenantId: userInfo.currentTenantId,
    // });
    // if (!success) throw new Error(message);
    // ElMessage.success(`操作成功`);
    // const { success, message, data } = await setEventDataByTransferOrUpgrade({ id: raw.id as string, ...req });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功${req.type === "eventUpgrade" ? "升级事件" : "转交事件"}`);

    transferOrUpgrade.value.userGroupId = req.userGroupId;
    transferOrUpgrade.value.userId = req.userId;
    await (async (req?: { id: string }) => {
      try {
        if (!req) return;
        const { success, message, data: res } = await getUserByGroup({ id: req.id });
        if (!success) throw Object.assign(new Error(message), { success, data: res });
        userList.value = res;
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      }
    })({ id: req.userGroupId });
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    state.loading = false;
  }
}
async function handleSetPriority(priority: priority, raw: Partial<DataItem>) {
  // const priorityItem = (find(priorityOption, ({ value }) => value === priority) || {}).label || priority;

  // if (!editorRef.value) return;
  // const time = autoRefreshTime.value;
  try {
    // autoRefreshTime.value = 0;
    // state.loading = true;

    // const params = { priority, label: priorityItem, raw };
    // const form = {};
    // await editorRef.value.confirm({ ...form, ...params, $type: "warning", $title: `修改优先级`, $slot: "setPriority" }, async (form: Record<string, unknown>) => {

    // const { success, message, data } = await setEventDataByPriority({ eventId: raw.id as string, priority });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功修改优先级`);
    // });

    state.data.priority = priority;
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    state.loading = false;
  }
}
async function handleSetImportance(importance: deviceImportance, raw: Partial<DataItem>) {
  // const importanceItem = (find(deviceImportanceOption, ({ value }) => value === importance) || {}).label || importance;

  // if (!editorRef.value) return;
  // const time = autoRefreshTime.value;
  try {
    // autoRefreshTime.value = 0;
    // state.loading = true;

    // const params = { importance, label: importanceItem, raw };
    // const form = {};
    // await editorRef.value.confirm({ ...form, ...params, $type: "warning", $title: `修改重要性`, $slot: "setImportance" }, async (form: Record<string, unknown>) => {

    // const { success, message, data } = await setServiceRequestDataByImportance({ id: raw.id as string, importance });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功修改重要性`);
    // });

    state.data.influence = importance;
    setPriority();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    state.loading = false;
  }
}
async function handleSetSeverity(severity: eventSeverity, raw: Partial<DataItem>) {
  // const severityItem = (find(eventSeverityOption, ({ value }) => value === severity) || {}).label || severity;

  // if (!editorRef.value) return;
  // const time = autoRefreshTime.value;
  try {
    // autoRefreshTime.value = 0;
    // state.loading = true;

    // const params = { severity, label: severityItem, raw };
    // const form = {};
    // await editorRef.value.confirm({ ...form, ...params, $type: "warning", $title: `修改紧急性`, $slot: "setSeverity" }, async (form: Record<string, unknown>) => {

    // const { success, message, data } = await setServiceRequestDataBySeverity({ id: raw.id as string, severity });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功修改紧急性`);
    // });

    state.data.urgency = severity;
    setPriority();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    state.loading = false;
  }
}

function setPriority() {
  if (state.data.urgency && state.data.influence) state.data.priority = (find(priorityMatrix.value, (v) => v.eventSeverity === state.data.urgency && v.deviceImportance === state.data.influence) || {}).priority || state.data.priority;
}

const tabCounts = ref<TabCount>({} as TabCount);
async function getTabCount() {
  const { success, data, message } = await getDictDetailTapCountById({ id: route.params.id as string });
  if (!success) throw new Error(message);
  tabCounts.value = data;
}

async function handleRefresh() {
  try {
    state.loading = true;
    await getSlaTimeLimit();
    await nextTick();
    await resetData();
    await queryData();
    await getTabCount();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
async function handleQuery() {
  try {
    state.loading = true;
    await nextTick();
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}

const tickGroupConfig = ref({
  closeDirectly: false,
  isClose: false,
  orderApprovalPermission: false,
  ticketClassificationNames: [],
});

const orderIsClose = computed(() => {
  if ([serviceState.NEW].includes(state.data.serviceState || ("" as serviceState))) return false;
  if (tickGroupConfig.value.closeDirectly && tickGroupConfig.value.isClose) return true;
  else if (([serviceState.NEW, serviceState.PROCESSING].includes(state.data.serviceState || ("" as serviceState)) || state.data.operation === serviceOperation.FINISHED) && tickGroupConfig.value.isClose) return true;
  else return false;
});
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

async function getTackerGroupConfig() {
  try {
    const [
      /* 是否直接关闭 */
      { data: closeDirectlyData, message: closeDirectlyMessage, success: closeDirectlySuccess },
      { data: isCloseData, message: isCloseMessage, success: isCloseSuccess },
      { data: orderApprovalPermissionData, message: orderApprovalPermissionMessage, success: orderApprovalPermissionSuccess },
      ticketClassificationNames,
    ] = await Promise.all([
      /* 获取是否直接关闭 */
      getCloseDirectly({ tenantId: (userInfo.currentTenant || {}).id, ticketTemplateId: (state.data as any).ticketTemplateId }),
      getOrderUserGroupIsClose({ tenantId: (userInfo.currentTenant || {}).id, type: AssignableTicketType.dictservice, userId: userInfo.userId, ticketTemplateId: (state.data as any).ticketTemplateId }),
      getOrderApprovalPermission({ tenantId: (userInfo.currentTenant || {}).id, userId: state.data.currentOwnerId, type: AssignableTicketType.dictservice, ticketTemplateId: (state.data as any).ticketTemplateId, orderId: state.data.id }),
      getTicketClassificationNames(AssignableTicketType.dictservice, (state.data as any).ticketClassificationId, (state.data as any).ticketTemplateId),
    ]);
    if (!closeDirectlySuccess) throw new Error(closeDirectlyMessage);
    if (!isCloseSuccess) throw new Error(isCloseMessage);
    if (!orderApprovalPermissionSuccess) throw new Error(orderApprovalPermissionMessage);

    tickGroupConfig.value.closeDirectly = closeDirectlyData;
    tickGroupConfig.value.isClose = isCloseData;
    tickGroupConfig.value.orderApprovalPermission = orderApprovalPermissionData;
    tickGroupConfig.value.ticketClassificationNames = ticketClassificationNames;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

async function queryData() {
  try {
    const [
      /*  */
      { success, message, data },
      { success: prioritySuccess, message: priorityMessage, data: priorityData },
    ] = await Promise.all([
      /*  */
      getItemData({ id: route.params.id as string }),
      getPriorityMatrixList({}),

      // getUserGroupByPermissionIds({ queryPermissionId: 安全管理中心_用户组管理_可读, verifyPermissionIds: [智能事件中心_用户组_分配工单] }),
    ]);
    if (!success) throw Object.assign(new Error(message), { success, data });
    if (!prioritySuccess) throw Object.assign(new Error(priorityMessage), { success: prioritySuccess, data: priorityData });
    const { success: userGroupSuccess, message: userGroupMessage, data: userGroupData } = await getOrderUserGroup({ tenantId: (userInfo.currentTenant || {}).id, type: AssignableTicketType.dictservice, ticketTemplateId: (data as any).ticketTemplateId });
    if (!userGroupSuccess) throw Object.assign(new Error(userGroupMessage), { success: userGroupSuccess, data: userGroupData });

    await (async (req?: UserGroupConfigurationItem) => {
      try {
        if (!req) return;
        const { success, message, data: res } = await getUserByGroup({ id: req.userGrouptId });
        if (!success) throw Object.assign(new Error(message), { success, data: res });
        userList.value = res;
      } catch (error) {
        if (error instanceof Error) ElMessage.error(message);
      }
    })(find(userGroupData, (v) => v.userGrouptId === (data.displayUserGroupId || data.currentOwnerId)));

    priorityMatrix.value = priorityData.priorityMatrixItems.map((v) => {
      return {
        eventSeverity: v.urgency,
        deviceImportance: v.influence,
        priority: v.priority,
      };
    });
    // userGroups.value = userGroupData;
    userGroups.value = userGroupData.map((v) => ({ id: v.userGrouptId, name: v.userGroupName, tenantAbbreviation: v.abbreviation }));

    setTimeout(() => {
      transferOrUpgrade.value.userGroupId = (find(userGroups.value, (v) => v.id === (state.data.displayUserGroupId || state.data.currentOwnerId)) || {}).id as string;
      transferOrUpgrade.value.userId = (find(userList.value, (v) => v.id === state.data.currentOwnerId) || {}).id as string;
    }, 0);

    state.data = data;
    state.data.currentSuspendPauseTime = state.data.currentSuspendPauseTime ? Number(state.data.currentSuspendPauseTime) + timeZoneSwitching() : state.data.currentSuspendPauseTime;
    state.data.currentSuspendRequestTime = state.data.currentSuspendRequestTime ? Number(state.data.currentSuspendRequestTime) + timeZoneSwitching() : state.data.currentSuspendRequestTime;

    verifyPermissionIds.value = data.verifyPermissionIds || [];

    getTackerGroupConfig();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  }
}
async function createItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await addItemData({ ...form });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`成功确认告警`);
  });
}
/** 审批 */
async function handleApproveHangUp(approve: Boolean) {
  try {
    const { success, message } = await dictServiceApproveHangUp({ id: route.params.id as string, approve: approve as boolean });
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    await queryData();
  }
}
// async function previewItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setItemData({ ids: form.ids as string[], ...form });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
// async function rewriteItem(row: Record<string, unknown>) {
//   const title = (find(priorityOption, ({ value }) => value === row.priority) || {}).label || row.priority;
//   if (!editorRef.value) return row;
//   const params = { select: (<Item[]>row.select).filter((v) => row.priority !== v.priority) };
//   await editorRef.value.confirm({ ...row, ...params, $type: "warning", $title: `批量${title}`, $slot: "batchConfirm" }, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setEventDataByPriority({ id: (<Item[]>form.select).map((v) => v.id), priority: form.priority as priority });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`成功${form.$title}`);
//   });
// }
// async function modifyItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setItemData({ ids: form.ids as string[], ...form });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
// async function deleteItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await delItemData(form);
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
function handleCancel() {
  if ("fallback" in route.query && typeof route.query.fallback === "string") router.push({ name: route.query.fallback, params: { id: route.query.deviceId }, query: { type: route.query.status } });
  else if (history.length === 1) window.close();
  else router.back();
}

async function handleAccept(row: Partial<DataItem>) /* 接手 */ {
  try {
    operation.value = serviceOperation.TAKE_OVER;
    // state.loading = true;
    // // serviceRequestId: string | number;
    // // tenantId: string | number;
    // const { success, message, data } = await serviceRequestStart({ serviceRequestId: row.id as string, tenantId: userInfo.currentTenantId });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功接手事件`);
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // state.loading = false;
  }
}

async function handleEditSummary() {
  ElMessageBox.prompt("请输入摘要", "编辑摘要", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputValue: state.data.title,
    inputValidator: (value: string) => {
      return !!value;
    },
    inputErrorMessage: "请输入摘要",
    beforeClose: async (action, instance, done) => {
      try {
        if (action === "confirm") {
          // const { success, data, message } = await editSummary({ id: route.params.id as string, desc: instance.inputValue });
          // if (!success) throw new Error(message);
          // ElMessage.success("操作成功");
          // handleRefresh();
          state.data.title = instance.inputValue;
          done();
        } else done();
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    },
  })
    .then(() => {
      /* code */
    })
    .catch(() => {
      /* code */
    });
}

const endRef = ref<InstanceType<typeof EventEnd>>();
async function handleEnd(data: Partial<DataItem>, type: string) {
  if (!endRef.value) return false;
  endRef.value.open(data, type);
}

const pendRef = ref<InstanceType<typeof EventPend>>();
async function handleApprove(row: Partial<DataItem>, approveType: serviceState.CUSTOMER_SUSPENDED | serviceState.SERVICE_PROVIDER_SUSPENDED) /* 挂起 */ {
  if (!pendRef.value) return false;
  pendRef.value.open(row, approveType);
  // try {
  //   state.loading = true;
  //   const { success, message, data } = await setEventDataByApprove({ id: row.id as string, approve: false });
  //   if (!success) throw Object.assign(new Error(message), { success, data });
  //   ElMessage.success(`成功接手事件`);
  // } catch (error) {
  //   if (error instanceof Error) {
  //     const message = error.message;
  //     await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
  //   }
  // } finally {
  //   await queryData();
  //   state.loading = false;
  // }
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
defineSlots<{}>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
