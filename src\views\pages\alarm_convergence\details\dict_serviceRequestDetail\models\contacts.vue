<template>
  <el-scrollbar :style="{ height: `${height - 95}px` }">
    <div v-for="tab in tabs" :key="tab.code">
      <el-row :gutter="16" class="tw-mx-[2px]">
        <el-col :span="12" class="tw-mb-[16px] tw-flex">
          <h3 style="font-weight: 400; font-size: 14px; color: #000">
            {{ tab.cnName }}
          </h3>
        </el-col>
        <el-col :span="12" class="tw-mb-[16px] tw-flex">
          <el-tooltip :content="$t('glob.noPower')" :disabled="verifyPermissionIds.includes(智能事件中心_DICT服务请求_分配联系人)">
            <span class="tw-ml-auto tw-h-fit">
              <el-button type="primary" :disabled="!verifyPermissionIds.includes(智能事件中心_DICT服务请求_分配联系人) || [serviceState.NEW, serviceState.CLOSED, serviceState.AUTO_CLOSED].includes((detail.serviceState as serviceState) || ('' as serviceState))" @click="createItem(tab)">分配{{ tab.cnName }}</el-button>
            </span>
          </el-tooltip>
        </el-col>
        <template v-for="item in contacts.filter((v) => v.contactType.includes(tab.code))" :key="item">
          <el-col :xs="12" :sm="12" :md="12" :lg="8" :xl="6" class="tw-mb-[16px]" :span="8">
            <el-card shadow="never" :body-style="{ padding: '6px 20px' }" :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
              <template #header>
                <div class="tw-flex tw-items-center">
                  <el-icon color="var(--el-color-primary)" :size="16" class="tw-mr-1">
                    <Postcard />
                  </el-icon>
                  <el-text type="primary" class="tw-mr-auto tw-w-[calc(100%_-_265px)] tw-text-[14px] tw-leading-[16px]" truncated :title="item.name">{{ item.name }}</el-text>
                  <div style="display: flex; align-items: center">
                    <div style="margin-right: 2px" v-for="itemA in localesOption" :key="itemA.value">
                      <div v-if="itemA.value == item.language" :style="{ background: `url(${itemA.icon}) no-repeat left / auto`, paddingLeft: '30px' }">{{ itemA.label }}</div>
                    </div>
                    <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(资产管理中心_联系人_查看联系人)">
                      <span class="tw-ml-auto tw-h-fit">
                        <el-link type="danger" :underline="false" :disabled="!userInfo.hasPermission(资产管理中心_联系人_查看联系人) || [serviceState.NEW, serviceState.CLOSED, serviceState.AUTO_CLOSED].includes((detail.serviceState as serviceState) || ('' as serviceState))" @click="viewContactDetail(item as ContactsItem)">{{ $t("glob.Cat") }}</el-link>
                      </span>
                    </el-tooltip>

                    <el-popconfirm :width="200" :disabled="!verifyPermissionIds.includes(智能事件中心_DICT服务请求_分配联系人) || [serviceState.NEW, serviceState.CLOSED, serviceState.AUTO_CLOSED].includes((detail.serviceState as serviceState) || ('' as serviceState))" :title="`确定${$t('glob.remove')} ${item.name} ${tab.cnName}?`" :confirm-button-text="$t('glob.remove')" confirm-button-type="danger" @confirm="delItem({ ...item, code: tab.code })">
                      <template #reference>
                        <span>
                          <el-tooltip :content="$t('glob.noPower')" :disabled="verifyPermissionIds.includes(智能事件中心_DICT服务请求_分配联系人)">
                            <span class="tw-ml-auto tw-h-fit">
                              <el-link type="danger" :underline="false" :disabled="!verifyPermissionIds.includes(智能事件中心_DICT服务请求_分配联系人) || [serviceState.NEW, serviceState.CLOSED, serviceState.AUTO_CLOSED].includes((detail.serviceState as serviceState) || ('' as serviceState))" class="tw-ml-[8px]">{{ $t("glob.remove") }}</el-link>
                            </span>
                          </el-tooltip>
                        </span>
                      </template>
                    </el-popconfirm>
                  </div>
                </div>
              </template>
              <template #default>
                <div class="tw-flex tw-h-[24px] tw-items-center" title="固定电话">
                  <el-icon class="tw-mr-2">
                    <Phone />
                  </el-icon>
                  <el-text type="info" class="tw-text-[14px]">{{ item.landlinePhone || "--" }}</el-text>
                </div>
                <div class="tw-flex tw-h-[24px] tw-items-center" title="移动电话">
                  <el-icon class="tw-mr-2">
                    <Iphone />
                  </el-icon>
                  <el-text type="info" class="tw-text-[14px]">{{ item.mobilePhone || "--" }}</el-text>
                </div>
                <div class="tw-flex tw-h-[24px] tw-items-center" title="邮箱">
                  <el-icon class="tw-mr-2">
                    <Message />
                  </el-icon>
                  <el-text type="info" class="tw-text-[14px]">{{ item.email || "--" }}</el-text>
                </div>
              </template>
              <template #footer> {{ item.tenantName + `[${item.tenantAbbreviation}]` }}</template>
            </el-card>
          </el-col>
        </template>
      </el-row>
    </div>
  </el-scrollbar>
  <createContact ref="editorRef" title="联系人" :tabs="tabs" :types="contactsType" :contacts="contacts">
    <template #del="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]">
          <InfoFilled></InfoFilled>
        </el-icon>
        <p class="title">
          确定移除联系人
          <span>{{ ((params || {}) as anyObj).name as string }}</span>
          吗？
        </p>
      </div>
    </template>
  </createContact>
  <contactView ref="contactViewRef"></contactView>
</template>

<script setup lang="ts" name="event-contacts">
import { ref, toRefs, onMounted, nextTick, watch, inject } from "vue";

import { Plus, PhoneFilled, Iphone, Message, Phone } from "@element-plus/icons-vue";

import { ElMessage } from "element-plus";

import type { ServiceItem } from "@/views/pages/apis/event";
import { serviceState } from "@/views/pages/apis/event";

import { getContactTypes as getType, type ContactsTypeItem, type ContactsItem } from "@/views/pages/apis/contacts";

import { getServiceRequestContact as getData, eventBatchdesensitized, addDictServiceRequestContact as addData, delDictServiceRequestContact as delData, getDictChangeContacts } from "@/views/pages/apis/serviceRequest";

import pageTemplate from "@/components/pageTemplate.vue";

import FormModel from "@/components/formItem/FormModel.vue";

import FormItem from "@/components/formItem/FormItem.vue";

import contactView from "@/views/pages/alarm_convergence/PropertyManage/contactsManage/contactsEdit.vue";

import createContact from "./createContact.vue";

import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";

import getUserInfo from "@/utils/getUserInfo";

import { 资产管理中心_联系人_查看联系人, 智能事件中心_DICT服务请求_分配联系人 } from "@/views/pages/permission";
import { localesOption } from "@/api/locale.ts";

const userInfo = getUserInfo();

const route = useRoute();

const { t } = useI18n();

const verifyPermissionIds = inject("verifyPermissionIds") as string[];

interface Props {
  height: number;
  data: Partial<ServiceItem>;
  refresh: () => Promise<void>;
}

const props = withDefaults(defineProps<Props>(), {
  height: 0,
  data: () => <Partial<ServiceItem>>{},
});

const { height, data: detail } = toRefs(props);
const contacts = ref<(Partial<ContactsItem> & Record<"contactType", string>)[]>([]);

watch(
  () => props.data,
  (v) => {
    querysItem();
  },
  { immediate: true }
);

const activeName = ref<string>("");

const tabs = ref<ContactsTypeItem[]>([]);
const contactsType = ref<{ contactId: string; contactType: string }[]>([]);

const contactViewRef = ref<InstanceType<typeof contactView>>();
async function viewContactDetail(row: ContactsItem) {
  if (!contactViewRef.value) return false;
  contactViewRef.value.open(row, true);
}

async function getContactsType() {
  try {
    const { data, success, message } = await getType({});
    if (!success) throw new Error(message);
    tabs.value = data;
    if (!activeName.value) activeName.value = (data.find((v) => v) || {}).code || "";
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
}
const editorRef = ref<InstanceType<typeof createContact>>();
async function createItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  const params = {
    contactType: row.code,
    types: tabs.value,
    contactsArr: contacts.value,
  };
  await editorRef.value.open({ row, ...params }, async (form: Record<string, unknown>) => {
    const { success, message, data } = await addData({ id: <string>route.params.id, contactType: <string>form.contactType, contactIds: <string[]>form.contactId });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(t("axios.Operation successful"));
    props.refresh();
  });
}

async function delItem(row: Record<string, unknown>) {
  // if (!editorRef.value) return;
  try {
    // const params = { ...row };
    // const form = {};
    // await editorRef.value.confirm({ ...form, ...params, $title: `移除联系人`, $slot: "del" }, async (form: Record<string, unknown>) => {
    const { success, message, data } = await delData({ id: <string>route.params.id, contactType: row.code, contactIds: [row.id] });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`操作成功}`);
    props.refresh();
    // });
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    props.refresh();
  }
}

async function querysItem() {
  let ids = "";
  let contactsData = [];
  await getDictChangeContacts({ id: route.params.id }).then((res: any) => {
    if (res.success) {
      contactsData = res.data;
      contactsType.value = res.data instanceof Array ? res.data : [];

      ids = res.data.map((v) => v.contactId);
    }
  });
  // const ids = (detail.value?.contacts || []).map((v) => v.contactId);
  if (!ids || !ids.length) {
    contacts.value = [];
    return false;
  }
  const { success, data, message } = await eventBatchdesensitized({ ids });
  if (!success) throw new Error(message);
  contacts.value = data.map((v) => ({
    ...v,
    contactType:
      (contactsData || [])
        .filter((f) => f.contactId === v.id)
        .map((m) => m.contactType)
        .join() || "",
  }));
}

onMounted(() => {
  getContactsType();
});
</script>

<style lang="scss" scoped>
@import "@/styles/theme/common/var.scss";

.contact-item {
  background: #f7f8fa;

  .contact-name {
    color: $color-black;
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }

  .contact-cn-name {
    font-family: "PingFang SC";
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: map-get($text-color, regular);
  }

  .contact-content {
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    color: $color-black;
  }

  .contact-label {
    color: map-get($text-color, regular);
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
}
</style>
