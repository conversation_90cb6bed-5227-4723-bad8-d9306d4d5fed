<template>
  <el-form :model="form" label-position="top" v-show="userInfo.hasPermission(智能事件中心_DICT服务请求_可读) || userInfo.hasPermission(智能事件中心_工单_可读)">
    <el-form-item>
      <template #label>
        <div class="info-desc">
          <span>服务工单号</span>
          <el-button style="z-index: 1" type="primary" :disabled="(!verifyPermissionIds.includes(智能事件中心_DICT服务请求_更新) && !verifyPermissionIds.includes(智能事件中心_工单_更新)) || [serviceState.NEW, serviceState.CLOSED, serviceState.AUTO_CLOSED].includes((props.data.serviceState as serviceState) || ('' as serviceState))" @click="handleServiceCodeEdit">{{ isEditServiceCode ? "保存" : "编辑" }}</el-button>
        </div>
      </template>
      <div class="tw-flex tw-min-h-[32px] tw-w-full tw-flex-col" v-if="isEditServiceCode" @keyup.enter.stop>
        <el-input v-model="form.serviceCode" placeholder="请输入描述" />
      </div>
      <div class="el-input el-input__wrapper tw-flex tw-min-h-[32px] tw-w-full tw-flex-col tw-items-start tw-justify-start" v-else @keyup.enter.stop>
        <div v-html="props.data.serviceCode"></div>
      </div>
    </el-form-item>

    <el-form-item>
      <template #label>
        <div class="info-desc">
          <span>期望响应时间</span>
          <el-button style="z-index: 1" type="primary" :disabled="(!verifyPermissionIds.includes(智能事件中心_DICT服务请求_更新) && !verifyPermissionIds.includes(智能事件中心_工单_更新)) || [serviceState.NEW, serviceState.CLOSED, serviceState.AUTO_CLOSED].includes((props.data.serviceState as serviceState) || ('' as serviceState))" @click="handleExpectEndTimeEdit('expectStartTime')">编辑</el-button>
        </div>
      </template>
      <div class="el-input el-input__wrapper tw-flex tw-min-h-[32px] tw-w-full tw-flex-col tw-items-start tw-justify-start" @keyup.enter.stop>
        <div>{{ props.data.expectStartTime ? moment(Number(props.data.expectStartTime)).format("YYYY-MM-DD HH:mm:ss") : "" }}</div>
      </div>
    </el-form-item>

    <el-form-item>
      <template #label>
        <div class="info-desc">
          <span>期望完成时间</span>
          <el-button style="z-index: 1" type="primary" :disabled="(!verifyPermissionIds.includes(智能事件中心_DICT服务请求_更新) && !verifyPermissionIds.includes(智能事件中心_工单_更新)) || [serviceState.NEW, serviceState.CLOSED, serviceState.AUTO_CLOSED].includes((props.data.serviceState as serviceState) || ('' as serviceState))" @click="handleExpectEndTimeEdit('expectEndTime')">编辑</el-button>
        </div>
      </template>
      <div class="el-input el-input__wrapper tw-flex tw-min-h-[32px] tw-w-full tw-flex-col tw-items-start tw-justify-start" @keyup.enter.stop>
        <div>{{ props.data.expectEndTime ? moment(Number(props.data.expectEndTime)).format("YYYY-MM-DD HH:mm:ss") : "" }}</div>
      </div>
    </el-form-item>

    <el-form-item>
      <template #label>
        <div class="info-desc">
          <span>描述</span>
          <el-button style="z-index: 1" type="primary" :disabled="(!verifyPermissionIds.includes(智能事件中心_DICT服务请求_更新) && !verifyPermissionIds.includes(智能事件中心_工单_更新)) || [serviceState.NEW, serviceState.CLOSED, serviceState.AUTO_CLOSED].includes((props.data.serviceState as serviceState) || ('' as serviceState))" @click="handleDescEdit">{{ isEdit ? "保存" : "编辑" }}</el-button>
        </div>
      </template>
      <div class="tw-flex tw-min-h-[120px] tw-w-full tw-flex-col" v-if="isEdit" @keyup.enter.stop>
        <!-- <QuillEditor theme="snow" style="flex: 1" :content="isEdit ? form.description : props.data.description" @update:content="form.description = $event" contentType="html" toolbar="full" :enable="isEdit" :read-only="!isEdit"></QuillEditor> -->
        <el-input v-model="form.description" :rows="6" type="textarea" placeholder="请输入描述" />
      </div>
      <div class="el-input el-input__wrapper tw-flex tw-min-h-[120px] tw-w-full tw-flex-col tw-items-start tw-justify-start tw-p-4" v-else @keyup.enter.stop>
        <div v-html="props.data.description"></div>
      </div>
    </el-form-item>
    <el-form-item label="外部ID">
      <el-input :model-value="isEdit ? form.externalId : props.data.externalId" @update:model-value="form.externalId = $event" :disabled="!isEdit"></el-input>
    </el-form-item>
    <el-form-item>
      <el-row class="el-input el-input__wrapper">
        <el-col :span="12" class="tw-h-[calc(var(--el-component-size)*3)] tw-text-left">
          <p>修改</p>
          <p class="tw-h-[var(--el-component-size)]">
            <el-icon :size="16" class="tw-mr-[6px] tw-align-middle"><UserFilled /></el-icon>{{ updated.name || "--" }}
          </p>
          <p>{{ updated.updateTime ? moment(`${updated.updateTime}`, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</p>
        </el-col>
        <el-col :span="12" class="tw-h-[calc(var(--el-component-size)*3)] tw-text-right">
          <p>创建</p>
          <p class="tw-h-[var(--el-component-size)]">
            <el-icon :size="16" class="tw-mr-[6px] tw-align-middle"><UserFilled /></el-icon>{{ collector.name || "--" }}
          </p>
          <p>{{ collector.collectTime ? moment(`${collector.collectTime}`, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</p>
        </el-col>
      </el-row>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, watch, defineComponent, h, inject } from "vue";
import { useRoute, useRouter } from "vue-router";

import { ElMessage, ElMessageBox, ElForm, ElFormItem, FormInstance, ElDatePicker } from "element-plus";

import { UserFilled } from "@element-plus/icons-vue";
// import { QuillEditor } from "@vueup/vue-quill";
import moment from "moment";
import { setDictServiceRequestDataByDescription, setServiceRequestDataByServiceCode, setExpectTimeByServiceId } from "@/views/pages/apis/serviceRequest";
import { serviceState } from "@/views/pages/apis/event";

import { 智能事件中心_DICT服务请求_更新, 智能事件中心_工单_更新, 智能事件中心_DICT服务请求_可读, 智能事件中心_工单_可读 } from "@/views/pages/permission";

defineOptions({ name: "ModelDetails" });
import getUserInfo from "@/utils/getUserInfo";
const userInfo = getUserInfo();
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);

const props = withDefaults(defineProps<{ data: Partial<import("../helper").DataItem>; height: number; refresh: () => Promise<void> }>(), { data: () => ({}) });
const emits = defineEmits(["changeDesc"]);

const route = useRoute();
const router = useRouter();

const verifyPermissionIds = inject("verifyPermissionIds") as string[];

const form = ref({ description: "", externalId: "", serviceCode: "" });
const isEdit = ref(false);
const isEditServiceCode = ref(false);

const updated = reactive({ name: "", updateTime: 0 });
const collector = reactive({ name: "", collectTime: 0 });
function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == userInfo.zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
watch(
  () => props.data,
  async (data) => {
    await nextTick();
    if (data.createdBy) collector.name = JSON.parse(data.createdBy)?.username;
    collector.collectTime = Math.max(Number(data.createTime) || 0, 0) + timeZoneSwitching();
    try {
      updated.name = JSON.parse(data.updatedBy || "{}").username || "";
    } catch (error) {
      updated.name = "";
    }
    updated.updateTime = Math.max(Number(data.updateTime) || 0, 0) + timeZoneSwitching();
  },
  { immediate: true }
);

async function handleDescEdit() {
  if (isEdit.value) {
    emits("changeDesc", form.value.description);
    isEdit.value = false;
    try {
      const { success, message } = await setDictServiceRequestDataByDescription({ id: props.data.id as string, description: form.value.description, externalId: form.value.externalId });
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      props.refresh();
    }
  } else {
    isEdit.value = true;
    form.value.description = form.value.description || props.data.description || "";
    form.value.externalId = props.data.externalId || "";
  }
}

async function handleServiceCodeEdit() {
  if (isEditServiceCode.value) {
    // emits("changeDesc", form.value.serviceCode);
    isEditServiceCode.value = false;
    try {
      const { success, message } = await setServiceRequestDataByServiceCode({ id: props.data.id as string, serviceCode: form.value.serviceCode, externalId: form.value.externalId });
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      props.refresh();
    }
  } else {
    isEditServiceCode.value = true;
    form.value.serviceCode = form.value.serviceCode || props.data.serviceCode || "";
    form.value.externalId = props.data.externalId || "";
  }
}

const changeTimeForm = ref({
  date: "",
});
const changeTimeFormRef = ref<FormInstance>();

async function handleExpectEndTimeEdit(type) {
  const title = `期望${{ expectEndTime: "完成", expectStartTime: "响应" }[type]}时间`;
  await ElMessageBox({
    title: `编辑${title}`,
    message: h(
      defineComponent({
        setup() {
          return () =>
            h(
              ElForm,
              {
                model: changeTimeForm.value,
                ref: (v) => (changeTimeFormRef.value = v as FormInstance),
                rules: {
                  date: [
                    {
                      required: true,
                      validator: (rule: any, value: any, callback: any) => {
                        if (!changeTimeForm.value.date) return callback(new Error(`请选择${title}`));
                        if (Number(changeTimeForm.value.date) < new Date().getTime()) return callback(new Error(`请选择${moment().format("YYYY-MM-DD HH:mm:ss")}之后的时间`));
                        callback();
                      },
                      trigger: ["change", "blur"],
                    },
                  ],
                },
                labelPosition: "top",
              },
              h(ElFormItem, { label: `${title}`, prop: "date" }, [h(ElDatePicker, { "class": "tw-w-full", "modelValue": changeTimeForm.value.date, "onUpdate:modelValue": (v) => (changeTimeForm.value.date = v), "type": "datetime", "valueFormat": "x", "placeholder": `请选择${title}`, "disabledDate": (time) => time.getTime() < Date.now() - 8.64e7 })])
            );
        },
      })
    ),
    beforeClose: async (action, instance, done) => {
      if (action === "confirm") {
        changeTimeFormRef.value &&
          changeTimeFormRef.value.validate(async (valid) => {
            if (!valid) return;
            try {
              const params = {
                expectStartTime: props.data.expectStartTime,
                expectEndTime: props.data.expectEndTime,
              };
              params[type] = changeTimeForm.value.date;
              const { message, success } = await setExpectTimeByServiceId(Object.assign({ id: route.params.id }, params));
              if (!success) throw new Error(message);
              ElMessage.success("操作成功");
              changeTimeFormRef.value && changeTimeFormRef.value.resetFields();
              done();
            } catch (error) {
              error instanceof Error && ElMessage.error(error.message);
            } finally {
              props.refresh();
            }
          });
      } else {
        done();
      }
    },
  })
    .then(() => {})
    .catch(() => {});
}
</script>

<style lang="scss" scoped>
.info-desc {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
