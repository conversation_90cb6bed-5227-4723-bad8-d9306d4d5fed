<template>
  <el-table stripe :data="tableData || []" :height="550">
    <!-- <el-table-column prop="dynamicCreateTime" :label="$t('generalDetails.Time')" width="300">
      <template #default="{ row }">{{ moment(row.dynamicCreateTime, "x").format("YYYY-MM-DD HH:mm:ss") }}</template>
    </el-table-column>
    <el-table-column prop="dynamicContent" :label="$t('generalDetails.Dynamic state')">
      <template #default="{ row }">
        <div>
          {{ row.eventCreatorName }}
          {{ row.eventTransferName }}
          {{ row.dynamicContent }}
        </div>
      </template>
    </el-table-column> -->
    <el-table-column prop="operation" label="操作类型">
      <template #default="{ row }">
        {{ row.operation }}
      </template>
    </el-table-column>
    <el-table-column prop="property" label="属性">
      <template #default="{ row }">
        {{ row.property }}
      </template>
    </el-table-column>
    <el-table-column prop="dynamicContent" label="原有值">
      <template #default="{ row }">
        {{ row.oldValue }}
      </template>
    </el-table-column>
    <el-table-column prop="dynamicContent" label="更改后">
      <template #default="{ row }">
        {{ row.newValue }}
      </template>
    </el-table-column>
    <el-table-column prop="dynamicContent" label="创建人">
      <template #default="{ row }">
        {{ row.eventCreatorName }}
      </template>
    </el-table-column>
    <el-table-column prop="dynamicCreateTime" label="时间戳">
      <template #default="{ row }">{{ moment(row.dynamicCreateTime, "x").format("YYYY-MM-DD HH:mm:ss") }}</template>
    </el-table-column>
  </el-table>
</template>

<script lang="ts" setup>
import moment from "moment";
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";

import { getDictRequestTrendList, type Dynamic } from "@/views/pages/apis/eventManageTrends";
defineOptions({ name: "ModelDynamics" });
import getUserInfo from "@/utils/getUserInfo";
import { useRoute } from "vue-router";
const route = useRoute();
const userInfo = getUserInfo();
const props = withDefaults(defineProps<{ data: Partial<import("../helper").DataItem>; height: number; refresh: () => Promise<void> }>(), { data: () => ({}) });
const tableData = ref<Dynamic[]>([]);
getTrendsList();
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);


function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == getUserInfo().zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}

function getTrendsList() {
  getDictRequestTrendList({ id: route.params.id, tenantId: userInfo.currentTenantId }).then((res: any) => {
    if (res.success) {
      tableData.value = [...res.data].map((item: any) => {
        return {
          ...item,
          dynamicCreateTime: Number(item.dynamicCreateTime) + timeZoneSwitching(),
        };
      });
    }
  });
}
</script>

<style lang="scss" scoped></style>
