<template>
  <pageTemplate :show-paging="false" :height="height">
    <template #right>
      <el-button type="primary" :icon="Upload" :disabled="(!verifyPermissionIds.includes(智能事件中心_DICT服务请求_更新) && !verifyPermissionIds.includes(智能事件中心_工单_更新)) || [serviceState.NEW, serviceState.CLOSED, serviceState.AUTO_CLOSED].includes((detail.serviceState as serviceState) || ('' as serviceState))" @click="handleStateCreate({})">上传文件</el-button>
    </template>
    <template #default>
      <el-table v-loading="state.loading" :data="state.data" :height="height - 60" :style="{ width: `${width}px`, margin: '0 auto' }">
        <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :min-width="column.width || 120" :showOverflowTooltip="column.showOverflowTooltip" :formatter="column.formatter" />
        <el-table-column :label="t('glob.operate')" align="left" header-align="left" :width="110" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" :disabled="(!verifyPermissionIds.includes(智能事件中心_DICT服务请求_更新) && !verifyPermissionIds.includes(智能事件中心_工单_更新)) || [serviceState.NEW, serviceState.CLOSED, serviceState.AUTO_CLOSED].includes((detail.serviceState as serviceState) || ('' as serviceState))" @click="downloadItem({ ...row })">下载</el-button>
            <el-button link type="danger" size="small" :disabled="(!verifyPermissionIds.includes(智能事件中心_DICT服务请求_更新) && !verifyPermissionIds.includes(智能事件中心_工单_更新)) || [serviceState.NEW, serviceState.CLOSED, serviceState.AUTO_CLOSED].includes((detail.serviceState as serviceState) || ('' as serviceState))" @click="delItem({ ...row })">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <uploadFile ref="editorRef" title="上传文件">
        <template #delFile="{ params }">
          <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
            <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
            <p class="title">
              确定删除文件
              <span>{{ ((params || {}) as anyObj).fileName as string }}</span>
              吗？
            </p>
          </div>
        </template>
      </uploadFile>
    </template>
  </pageTemplate>
</template>

<script setup lang="ts" name="event-files">
import { inject, onMounted, reactive, ref, toRefs } from "vue";

import pageTemplate from "@/components/pageTemplate.vue";

import uploadFile from "./uploadFiles.vue";

import { sizes } from "@/utils/common";

import { timeFormat } from "@/utils/date";

import { ElMessage, type TableColumnCtx } from "element-plus";

import { Download, Delete, Upload, InfoFilled } from "@element-plus/icons-vue";

import { FileType, type flieList as DataItem, getModelFiles as getData, dictServiceRequsestUploadFile as addData, delModelFile as delData, getFile as downloadData, download } from "@/views/pages/apis/file";

import { useRoute } from "vue-router";

import { useI18n } from "vue-i18n";

import getUserInfo from "@/utils/getUserInfo";

import { DataItem as DetailData } from "../helper";

import { serviceState } from "@/views/pages/apis/event";

import { 智能事件中心_DICT服务请求_更新, 智能事件中心_工单_更新 } from "@/views/pages/permission";
import _timeZoneHours from "@/views/pages/common/offsetHours.json";

const userInfo = getUserInfo();
const timeZoneHours = ref(_timeZoneHours);

const { t } = useI18n();

const verifyPermissionIds = inject("verifyPermissionIds") as string[];

const route = useRoute();

const width = inject("width", ref(0));

interface Props {
  height: number;
  refresh: any;
  data: Partial<DetailData>;
}

const props = withDefaults(defineProps<Props>(), {
  height: 0,
  data: () => ({}) as DetailData,
  refresh: () => {},
});

const { height, data: detail } = toRefs(props);

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: TableColumnCtx<DataItem>, value: T[keyof T]) => string | import("vue").VNode; showOverflowTooltip?: boolean }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<DataItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  column: [
    { key: "fileName", label: "文件名称", formatter },
    { key: "fileSize", label: "大小", formatter },
    { key: "createdBy", label: "创建人", formatter },
    { key: "createdTime", label: "创建时间", formatter },
    { key: "uploadedByNote", label: "小记", formatter },
  ],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

function formatter(_row: DataItem, _col: TableColumnCtx<DataItem>, v: DataItem[keyof DataItem]): string | import("vue").VNode {
  switch (_col.rawColumnKey) {
    case "fileSize":
      return v ? v + "kb" : "--";
    case "createdBy":
      return JSON.parse(v as string)?.username || "--";
    case "createdTime":
      return timeFormat(Number(v));
    case "uploadedByNote":
      return v ? "是" : "否";
    default:
      return (v as string) || "--";
  }
}
function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == getUserInfo().zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
async function downloadItem(row: Record<string, unknown>) {
  try {
    const { success, data, message } = await downloadData({ bucket: row.bucketName as string, path: row.keyName as string });
    if (!success) throw new Error(message);
    download(row.fileName as string, data);
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

const editorRef = ref<InstanceType<typeof uploadFile>>();
async function createItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await addData({ ...form }, { fileType: FileType.SERVICE, noteCreated: false, id: route.params.id as string });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(t("axios.Operation successful"));
  });
}

async function delItem(row: Record<string, unknown>) {
  if (!editorRef.value) return;
  try {
    state.loading = true;
    const params = { ...row };
    const form = {};
    await editorRef.value.confirm({ ...form, ...params, $title: `删除文件`, $slot: "delFile" }, async (form: Record<string, unknown>) => {
      const { success, message, data } = await delData({ id: row.id as string });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`成功${form.$title}`);
      await props.refresh();
    });
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
    await handleStateRefresh();
  }
}

async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  let controller = new AbortController();
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const { success, data, message } = await getData({ id: route.params.id as string });
    const newdata = data.map((item) => {
      return {
        ...item,
        createdTime: Number(item.createdTime) + timeZoneSwitching(),
      };
    });
    if (!success) throw new Error(message);
    return newdata;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
    return [];
  }
}

async function handleStateRefresh() {
  if (state.loading) return;
  state.loading = true;
  state.data.splice(0, state.data.length, ...(await querysItem({})));
  state.loading = false;
}

async function handleStateCreate(params: Partial<DataItem> & Record<string, unknown>) {
  await createItem(params as Record<string, unknown>);
  await handleStateRefresh();
  await props.refresh();
}

onMounted(() => {
  handleStateRefresh();
});
</script>
