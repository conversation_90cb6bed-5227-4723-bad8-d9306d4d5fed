<template>
  <el-dialog :title="`挂起`" v-model="dialogFormVisible" :before-close="beforeClose" width="800px">
    <el-form ref="form" :model="form" label-width="100px" :rules="rules">
      <el-alert type="warning" show-icon :title="maxSuspensionTimeMessageTitle"></el-alert>
      <el-row :gutter="20" class="slider-container" type="flex" justify="center" align="middle">
        <el-icon style="position: absolute; left: 40px; top: -5px; cursor: pointer" @click="handlePlayClick('left')"><CaretLeft /></el-icon>
        <el-col :span="1">
          <el-icon class="arrow-icon" @click="handleArrowClick('left')"><ArrowLeft /></el-icon>
        </el-col>
        <el-col :span="20">
          <el-slider ref="sliderRef" v-model="hourValue" :max="maxHour" :step="0.25" :format-tooltip="formatTime"> </el-slider>
        </el-col>
        <el-col :span="2">
          <el-icon @click="handleArrowClick('right')"><ArrowRight /></el-icon>
        </el-col>
        <el-icon style="position: absolute; right: 40px; top: -5px; cursor: pointer" @click="handlePlayClick('right')"><CaretRight /></el-icon>
      </el-row>

      <div style="text-align: center; margin-top: -15px; display: flex; justify-content: center; align-items: center; position: relative; width: 100%">
        <!-- 日期居中 -->
        <span>{{ formattedDate }}</span>

        <!-- 24h 右侧定位 -->
        <span style="margin-left: auto; position: absolute; right: 50px">
          {{ splitMaxHour?.weeks ? `${splitMaxHour.weeks} wk` : "" }}
          {{ splitMaxHour?.days ? `${splitMaxHour.days}d` : "" }}
          {{ splitMaxHour?.hours ? `${splitMaxHour.hours} h` : "" }}
          {{ splitMaxHour?.minutes ? `${splitMaxHour.minutes} min` : "" }}
        </span>
      </div>
      <!-- <el-form-item label="挂起时间" :style="{ marginTop: '10px' }">
        <div :style="{ display: 'flex', justifyContent: 'space-between' }">
          <el-input-number v-model.number="form.hangUpDay" :max="56" :min="0" :style="{ width: '175px' }" @change="handleDayChange"></el-input-number>
          <span class="tw-mx-3">天</span>
          <el-input-number v-model.number="form.hangUpHour" :max="hangUpHourMax" :min="0" :style="{ width: '175px' }"></el-input-number>
          <span class="tw-mx-3">时</span>
          <el-input-number v-model.number="form.hangUpMinute" :max="hangUpMinuteMax" :min="0" :style="{ width: '175px' }"></el-input-number>
          <span class="tw-mx-3">分</span>
        </div>
      </el-form-item> -->

      <el-form-item label="挂起原因" style="margin-top: 10px" prop="cause">
        <el-input v-model="form.cause" placeholder="请输入挂起原因" />
      </el-form-item>
      <el-form-item label="挂起小记" prop="suspendRecordNote">
        <div class="tw-flex tw-min-h-[300px] tw-w-full tw-flex-col">
          <QuillEditor ref="quillEditor" :key="editorKey" theme="snow" style="flex: 1" :content="form.suspendRecordNote" @update:content="form.suspendRecordNote = $event" contentType="html" :toolbar="customToolbar" :enable="true"></QuillEditor>
        </div>
        <div>
          <el-checkbox v-model="form.privateAble">私人客户[{{ tenantAbbreviation }}]</el-checkbox>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" :loading="butLoading" @click="submitForm()">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { ref } from "vue";

import { eventPend } from "@/views/pages/apis/eventManage";
import { QuillEditor } from "@vueup/vue-quill";
import BlotFormatter from "quill-blot-formatter";
import { CaretRight, CaretLeft } from "@element-plus/icons-vue";
// import editor from "@/components/Editor/indexEditor.vue";
import { getOrderGroupFindConfiguration, AssignableTicketType, maxSuspensionTimeOption } from "@/views/pages/apis/orderGroup";
import getUserInfo from "@/utils/getUserInfo";
import { addOrderNode } from "@/views/pages/apis/event";
import { useRoute } from "vue-router";
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue";
const modules = ref({
  name: "blotFormatter",
  module: BlotFormatter,
});
export default {
  components: { QuillEditor, ArrowLeft, ArrowRight, CaretRight, CaretLeft },
  props: {
    refresh: Function,
    ticketTemplateId: String,
  },
  emits: ["pend"],
  data() {
    return {
      dialogFormVisible: false,
      form: {
        hangUpDay: 0,
        hangUpHour: 0,
        hangUpMinute: 0,
        suspendRecordNote: "",
        cause: "",
        privateAble: false,
      },
      event: {},
      butLoading: false,
      hangUpHourMax: 23,
      hangUpMinuteMax: 59,
      suspendType: "",
      userInfo: getUserInfo(),
      maxSuspensionTime: 0,
      // 临界值中间值 是maxHour的中间值
      hourValue: 0,
      // 用户总的挂起时间
      maxHour: 0,
      // 点击前的值蓝色最大的范围
      baseThreshold: 0,
      editorKey: 0,
      allowAutoHourValue: true,
      route: useRoute(),
      customToolbar: [
        ["bold", "italic", "underline", "strike"],
        ["blockquote", "code", "code-block"],
        [{ header: [1, 2, 3, 4, 5, 6, false] }],
        [{ list: "ordered" }, { list: "bullet" }],
        [{ script: "sub" }, { script: "super" }],
        [{ indent: "-1" }, { indent: "+1" }],
        [{ direction: "rtl" }],
        [{ font: [] }],
        [{ size: ["small", false, "large", "huge"] }],
        [{ color: [] }, { background: [] }],
        [{ align: [] }],
        ["clean"],
        // 其他需要的按钮...
      ],
    };
  },
  computed: {
    rules() {
      return {};
    },
    maxSuspensionTimeMessageTitle() {
      const currentOption = maxSuspensionTimeOption.find((v) => v.value === `${this.maxSuspensionTime}`);
      return currentOption ? `挂起超过${currentOption.label}将需要管理员审批，最多挂起不超过56天。` : "挂起需要审批，最长挂起不超过8weeks（56天）。";
    },
    tenantAbbreviation() {
      for (let i = 0; i < this.userInfo.tenants.length; i++) {
        if (this.userInfo.tenants[i].id === this.userInfo.tenantId) return this.userInfo.tenants[i].abbreviation;
      }
      return "";
    },

    splitMaxHour() {
      const totalMinutes = this.maxHour * 60;

      const weeks = Math.floor(totalMinutes / (60 * 24 * 7));
      const days = Math.floor((totalMinutes % (60 * 24 * 7)) / (60 * 24));
      const hours = Math.floor((totalMinutes % (60 * 24)) / 60);
      const minutes = totalMinutes % 60;
      console.log(totalMinutes, "999999");
      return { weeks, days, hours, minutes };
    },

    calculatedDate() {
      const currentDate = new Date(); // 获取当前日期和时间

      // 确保 hourValue 和 minuteValue 是有效的数字
      const totalMinutes = (isNaN(this.hourValue) ? 0 : this.hourValue) * 60 + (isNaN(this.minuteValue) ? 0 : this.minuteValue);

      // 如果滑动条值无效，返回当前时间
      if (totalMinutes < 0) {
        return currentDate;
      }

      const newDate = new Date(currentDate.getTime() + totalMinutes * 60 * 1000); // 将滑动条值加到当前时间
      return newDate;
    },

    formattedDate() {
      const date = this.calculatedDate;
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");

      return `${year}年${month}月${day}日 ${hours}:${minutes}`;
    },
  },
  watch: {
    maxHour: {
      handler(newVal) {
        if (this.allowAutoHourValue) {
          // 默认设置成蓝色区终点或中间值
          this.hourValue = this.baseThreshold;
        }
        this.updateTrackColor();
      },
      immediate: true,
    },
    hourValue: {
      handler() {
        this.updateTrackColor();
      },
      immediate: true,
    },
    // baseThreshold: {
    //   handler() {
    //     this.updateTrackColor();
    //   },
    //   immediate: true,
    // },
  },
  created() {},
  methods: {
    async handleGetMaxSuspensionTime() {
      try {
        const { data, message, success } = await getOrderGroupFindConfiguration({ tenantId: (this.userInfo.currentTenant || {}).id, type: AssignableTicketType.dictservice, ticketTemplateId: this.ticketTemplateId });
        if (!success) throw new Error(message);
        this.maxSuspensionTime = data;
      } catch (error) {
        error instanceof Error && this.$message.error(error.message);
      }
    },
    handleDayChange(v) {
      if (v === 56) {
        this.form.hangUpHour = 0;
        this.form.hangUpMinute = 0;
        this.hangUpHourMax = 0;
        this.hangUpMinuteMax = 0;
      } else {
        this.hangUpHourMax = 23;
        this.hangUpMinuteMax = 59;
      }
    },
    beforeClose(done) {
      this.$refs.form.clearValidate();
      this.$refs.form.resetFields();
      this.form.privateAble = false;
      this.editorKey++; // 增加 editorKey 的变化，触发 QuillEditor 重渲染
      // this.form = {
      //   hangUpDay: 0,
      //   hangUpHour: 1,
      //   hangUpMinute: 0,
      //   cause: "",
      // };
      if (done instanceof Function) done();
      else this.dialogFormVisible = false;
    },
    submitForm() {
      // this.$refs.form.validate((valid) => {
      //   if (!valid) return false;
      //   const params = {
      //     eventId: this.event.id,
      //     cause: this.form.cause,
      //     durationMinutes: (this.form.hangUpDay || 0) * 24 * 60 + (this.form.hangUpHour || 0) * 60 + this.form.hangUpMinute || 0,
      //   };
      //   eventPend(params).then(({ success, data }) => {
      //     if (success) {
      //       this.$message.success("操作成功");
      //       this.refresh instanceof Function && this.refresh();
      //       this.beforeClose();
      //     } else this.$message.error(JSON.parse(data)?.message || "操作失败");
      //   });
      // });
      this.$refs.form.validate(async (valid) => {
        if (!valid) return false;

        try {
          const totalMinutes = Math.round(this.hourValue * 60);
          // 分解为天、小时、分钟
          this.form.hangUpDay = Math.floor(totalMinutes / (24 * 60));
          const remaining = totalMinutes % (24 * 60);
          this.form.hangUpHour = Math.floor(remaining / 60);
          this.form.hangUpMinute = remaining % 60;
          if (this.form.suspendRecordNote) {
            const formData = new FormData();
            formData.append("nodeContent", this.form.suspendRecordNote as any);
            formData.append("privateAble", this.form.privateAble);
            formData.append("privateCustomerId", this.userInfo.tenantId);
            formData.append("tenantId", (this.userInfo.currentTenant || {}).id as string);
            formData.append("orderType", "DICT_SERVICE_REQUEST");
            formData.append("permissionId", "690043142585450496");
            formData.append("orderId", this.route.params.id as string);
            formData.append("orderIdsJson", JSON.stringify([this.route.params.id]));
            const { success, message } = await addOrderNode(formData as any);
            if (!success) throw new Error(message);
          }

          const params = {
            durationMinutes: (this.form.hangUpDay || 0) * 24 * 60 + (this.form.hangUpHour || 0) * 60 + this.form.hangUpMinute || 0,
            // suspendRecordNote: this.form.suspendRecordNote,
            cause: this.form.cause,
            // privateCustomerId: this.userInfo.tenantId,
            // privateAble: this.form.privateAble,
          };
          this.$emit("pend", { params, type: this.suspendType });

          // this.$emit("pend", { params: { cause: this.form.cause, durationMinutes: (this.form.hangUpDay || 0) * 24 * 60 + (this.form.hangUpHour || 0) * 60 + this.form.hangUpMinute || 0 }, type: this.suspendType });
          this.beforeClose();
        } catch (error) {
          error instanceof Error && this.$message.error(error.message);
        }
      });
    },
    async open(v, suspendType) {
      await this.handleGetMaxSuspensionTime();

      this.allowAutoHourValue = false; // 禁止 watch 自动设置
      this.event = v;
      this.dialogFormVisible = true;
      this.suspendType = suspendType;

      if (Number(this.maxSuspensionTime) == 0) {
        this.maxHour = 1344; // 挂起时间为0 默认最大时长为1344小时，即56天
      } else {
        this.maxHour = Number(this.maxSuspensionTime / 60); // 转为小时
      }
      this.baseThreshold = this.maxHour;
      this.hourValue = this.maxHour / 2;
      this.$nextTick(() => {
        this.updateTrackColor();
        this.allowAutoHourValue = true;
      });
    },
    formatTime(value) {
      const totalMinutes = Math.round(value * 60);

      const weeks = Math.floor(totalMinutes / (60 * 24 * 7));
      const days = Math.floor((totalMinutes % (60 * 24 * 7)) / (60 * 24));
      const hours = Math.floor((totalMinutes % (60 * 24)) / 60);
      const minutes = totalMinutes % 60;

      let parts: string[] = [];

      if (weeks > 0) parts.push(`${weeks}wk `);
      if (days > 0) parts.push(`${days}d `);
      if (hours > 0) parts.push(`${hours}h `);
      if (minutes > 0) parts.push(`${minutes}min`);

      return parts.length > 0 ? parts.join("").trim() : "0min";
    },
    // 处理箭头点击
    handleArrowClick(direction) {
      const step = 0.25;
      if (direction === "left") {
        this.hourValue = Math.max(0, this.hourValue - step);
      } else {
        // 限制不超过当前maxHour
        this.hourValue = Math.min(this.maxHour, this.hourValue + step);
      }
    },
    updateTrackColor() {
      this.$nextTick(() => {
        const slider = this.$refs.sliderRef?.$el;
        const runway = slider?.querySelector(".el-slider__runway");
        if (!runway) return;

        const stepUnit = 15; // 15分钟一个步长
        const totalSteps = (this.maxHour * 60) / stepUnit;
        const currentStep = (this.hourValue * 60) / stepUnit;
        const thresholdStep = (this.baseThreshold * 60) / stepUnit;

        const percent = (step) => (step / totalSteps) * 100;

        const currentPercent = percent(currentStep);
        const thresholdPercent = percent(thresholdStep);

        // 视觉提前触发的红色区域（单位：百分比）
        const redVisualTriggerOffset = 0.5; // 0.5%内就触发红色

        const isVisuallyOver = currentPercent >= thresholdPercent - redVisualTriggerOffset;

        let gradient = "";

        if (!isVisuallyOver) {
          gradient = `linear-gradient(to right,
        #409EFF 0%,
        #409EFF ${currentPercent}%,
        #e4e7ed ${currentPercent}%,
        #e4e7ed 100%)`;
        } else {
          gradient = `linear-gradient(to right,
        #409EFF 0%,
        #409EFF ${thresholdPercent}%,
        #F56C6C ${thresholdPercent}%,
        #F56C6C ${currentPercent}%,
        #e4e7ed ${currentPercent}%,
        #e4e7ed 100%)`;
        }

        runway.style.setProperty("background", gradient, "important");
      });
    },

    handlePlayClick(direction) {
      this.allowAutoHourValue = false; // 禁止 watch 自动设置

      if (direction === "right") {
        this.maxHour = 1344; // 增加最大时间(平台最大挂起时间 8周)

        this.hourValue = this.baseThreshold; // 设置为蓝色区域终点
      } else {
        // 向左恢复默认挂起时间
        if (Number(this.maxSuspensionTime) === 0) {
          this.maxHour = 1344; // 挂起时间为0 默认最大时长为1344小时，即56天
        } else {
          this.maxHour = Number((this.maxSuspensionTime / 60).toFixed(2));
        }

        this.hourValue = this.maxHour / 2; // 设置新的 hourValue
        this.baseThreshold = this.maxHour;
      }

      // 在更改 maxHour 后，确保更新时间和颜色
      this.$nextTick(() => {
        this.updateTrackColor();
        this.allowAutoHourValue = true; // 恢复自动行为
      });
    },
  },
};
</script>
<style scoped>
/* 核心对齐样式 */
.slider-container {
  margin: 16px 0;

  /* 对齐修正 */
  .el-col {
    display: flex;
    align-items: center;
    height: 40px; /* 与滑块高度匹配 */
    cursor: pointer;

    /* 图标对齐 */
    .arrow-icon {
      margin: 0 auto; /* 水平居中 */
    }
  }

  /* 滑块高度适配 */
  :deep(.el-slider) {
    margin: 12px 0;
  }

  /* 精确对齐修正 */
  .aligned-slider {
    :deep(.el-slider__runway) {
      margin: 12px 0;
    }
  }
}

/* 文本对齐 */
.header,
.date-display {
  text-align: center;
  margin: 8px 0;
  color: #606266;
}
.el-slider__runway {
  height: 6px;
  border-radius: 3px;
}
:deep(.el-slider__bar) {
  background-color: transparent !important;
}

/* 保持原有功能实现 */
</style>
