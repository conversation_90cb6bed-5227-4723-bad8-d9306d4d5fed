<template>
  <div class="tw-flex tw-h-[50px] tw-flex-nowrap tw-items-center tw-pb-[20px]">
    <el-page-header class="tw-w-full" :content="`${$t('generalDetails.Incident Details')}${state.data.draft ? `(草稿)` : state.data.id || ''}`" @back="handleCancel()">
      <template #extra>
        <div class="event">
          <el-row :gutter="8" type="flex" justify="end" align="middle">
            <el-col :span="7" :style="{ display: 'flex', justifyContent: 'flex-end' }">
              <el-button-group :style="{ display: 'flex' }">
                <el-button :type="currentEventState.type || ''" :disabled="state.data.operation === EventOperation.CLOSE" @click.stop>{{ currentEventState.label || "" }}</el-button>
                <el-dropdown @command="$event.command()" :disabled="!userInfo.hasPermission('612912833616674816')">
                  <el-button :type="currentEventState.type || ''" @click.stop :disabled="isLockEndTime">
                    <!-- <template v-if="!!operation">
                      {{ (find(eventOperationOption, (v) => v.value === operation) || {}).label }}
                    </template>
                    <template v-else-if="!!stateRightText"> {{ stateRightText }}</template> -->

                    <template v-if="!!operation">
                      <span v-if="OperateChanged" class="tw-mr-1 tw-text-white">*</span>
                      {{ $t("generalDetails." + (find(eventOperationOption, (v) => v.value === operation) || {}).label) }}</template
                    >

                    <template v-else-if="!!stateRightText">
                      <span v-if="OperateChanged" class="tw-mr-1 tw-text-white">*</span>
                      {{ $t("generalDetails." + stateRightText) }}
                    </template>
                    <template v-else>
                      <template v-if="state.data.eventState === eventState.NEW">{{ $t("generalDetails.newly built") }}</template>
                      <template v-else-if="state.data.eventState === eventState.PROCESSING || state.data.eventState === eventState.WAITING_FOR_RECEIVE">{{ $t("generalDetails.handle") }}</template>
                      <template v-else-if="state.data.eventState === eventState.SUSPENDED || state.data.eventState === eventState.PENDING_APPROVAL">{{ $t("generalDetails.Suspended") }}</template>
                      <template v-else-if="state.data.eventState === eventState.CUSTOMER_SUSPENDED">{{ $t("generalDetails.Customer suspended") }}</template>
                      <template v-else-if="state.data.eventState === eventState.SERVICE_PROVIDER_SUSPENDED">{{ $t("generalDetails.Supplier suspended") }}</template>
                      <template v-else-if="state.data.eventState === eventState.COMPLETED">{{ $t("generalDetails.Solve") }}</template>
                      <template v-else-if="state.data.eventState === eventState.CLOSED || state.data.eventState === eventState.AUTO_CLOSED">{{ $t("generalDetails.close") }}</template>
                    </template>
                    <el-icon class="tw-ml-2"><ArrowDown></ArrowDown></el-icon>
                  </el-button>
                  <template #dropdown v-if="!isLockEndTime">
                    <el-dropdown-menu class="tw-min-w-[100px]">
                      <el-dropdown-item
                        v-for="item in [
                          { command: eventState.PROCESSING, /*                   */ execute: () => handleAccept(state.data), /*                                          */ center: $t('generalDetails.处理'), /*         */ disabled: [eventState.PROCESSING].includes(state.data.eventState!) },
                          { command: eventState.CUSTOMER_SUSPENDED, /*           */ execute: () => handleApprove(state.data, eventState.CUSTOMER_SUSPENDED), /*          */ center: $t('generalDetails.客户挂起'), /*     */ disabled: [eventState.WAITING_FOR_RECEIVE, eventState.NEW].includes(state.data.eventState!) || [EventOperation.CUSTOMER_SUSPENDED].includes(state.data.operation as EventOperation) },
                          { command: eventState.SERVICE_PROVIDER_SUSPENDED, /*   */ execute: () => handleApprove(state.data, eventState.SERVICE_PROVIDER_SUSPENDED), /*  */ center: $t('generalDetails.供应商挂起'), /*   */ disabled: [eventState.WAITING_FOR_RECEIVE, eventState.NEW].includes(state.data.eventState!) || [EventOperation.SERVICE_PROVIDER_SUSPENDED].includes(state.data.operation as EventOperation) },
                          { command: eventState.COMPLETED, /*                    */ execute: () => handleEnd(state.data, 'Finish'), /*                                   */ center: $t('generalDetails.已解决'), /*         */ disabled: [eventState.WAITING_FOR_RECEIVE, eventState.COMPLETED, eventState.NEW, eventState.AUTO_CLOSED].includes(state.data.eventState!) || state.data.operation === EventOperation.FINISHED },
                          { command: eventState.CLOSED, /*                       */ execute: () => handleEnd(state.data, 'Close'), /*                                    */ center: $t('generalDetails.关闭'), /*         */ disabled: !orderIsClose },
                        ]"
                        :key="item.command"
                        :command="{ command: item.execute }"
                        :disabled="state.loading || item.disabled"
                      >
                        {{ item.center }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-button-group>

              <!-- -->
            </el-col>
            <el-col :span="6" style="display: flex">
              <span v-if="userGroupChanged" class="tw-mr-1 tw-text-red-500">*</span>
              <!-- (find(userGroups, (v) => v.id === (state.data.userGroupId || state.data.displayUserGroupId)) || {}).id -->
              <el-select
                :model-value="userGroups.find((v) => v.id === transferOrUpgrade.userGroupId) ? transferOrUpgrade.userGroupId : ''"
                clearable
                :value-on-clear="''"
                @change="
                  handleSetTransferOrUpgrade(
                    {
                      type: typeOfTransferOrUpgrade,
                      userGroupId: $event,
                      userId: '',
                      status: 'group',
                    },
                    state.data
                  )
                "
                :placeholder="$t('generalDetails.Please select a user group')"
                filterable
                :disabled="[eventState.NEW, eventState.WAITING_FOR_RECEIVE, eventState.COMPLETED, eventState.CLOSED, eventState.AUTO_CLOSED].includes(state.data.eventState || ('' as eventState)) || state.data.operation === EventOperation.CLOSE || !userInfo.hasPermission(智能事件中心_事件工单_分配用户组) || isLockEndTime"
              >
                <el-option v-for="userGroup in userGroups" :key="userGroup.id" :label="`${userGroup.name}${userGroup.tenantAbbreviation ? '[' + userGroup.tenantAbbreviation + ']' : ''}`" :value="userGroup.id"></el-option>
              </el-select>
            </el-col>
            <el-col :span="6" style="display: flex">
              <span v-if="userChanged" class="tw-mr-1 tw-text-red-500">*</span>
              <!-- (find(userList, (v) => v.userId === state.data.actorId) || {}).userId -->
              <el-select
                :model-value="userList.find((v) => v.id === transferOrUpgrade.userId) ? transferOrUpgrade.userId : ''"
                @change="
                  handleSetTransferOrUser(
                    {
                      type: typeOfTransferOrUpgrade,
                      userGroupId: transferOrUpgrade.userGroupId,
                      userId: $event,
                      status: 'user',
                    },
                    state.data
                  )
                "
                :placeholder="$t('generalDetails.Please select a user')"
                filterable
                :disabled="[eventState.NEW, eventState.WAITING_FOR_RECEIVE, eventState.COMPLETED, eventState.CLOSED, eventState.AUTO_CLOSED].includes(state.data.eventState || ('' as eventState)) || state.data.operation === EventOperation.CLOSE || !userInfo.hasPermission(智能事件中心_事件工单_分配用户) || isLockEndTime"
              >
                <el-option v-for="user in userList" :key="user.id" :label="user.name + `(${user.account}@${user.tenantAbbreviation})`" :value="user.id"></el-option>
              </el-select>
            </el-col>
            <el-col
              :span="3"
              :style="{
                display: 'flex',
                justifyContent: 'flex-end',
                alignItems: 'center',
              }"
            >
              <el-checkbox v-model="typeOfTransferOrUpgrade" false-label="transfer" true-label="eventUpgrade" style="margin-right: 5px" :disabled="[eventState.WAITING_FOR_RECEIVE, eventState.CLOSED].includes(state.data.eventState || ('' as eventState))">{{ $t("generalDetails.Upgrade") }}</el-checkbox>
              <el-tooltip class="item" effect="dark" content="若需要升级请先点选升级复选框" placement="bottom">
                <el-icon class="tipIcon"><InfoFilled></InfoFilled></el-icon>
              </el-tooltip>
            </el-col>
            <el-col :span="2" :style="{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }" class="save">
              <el-tooltip :visible="state.data.draft" placement="top" effect="light" popper-class="draft-tooltip">
                <template #content>
                  <span>{{ $t("generalDetails.Click to save and create a work order") }}</span>
                </template>

                <el-button type="primary" v-preventReClick @click="handleEventSave" :disabled="!verifyPermissionIds.includes('612912833616674816') || (state.data.draft ? false : isLockEndTime)">{{ $t("generalDetails.Save") }}</el-button>
              </el-tooltip>
            </el-col>
          </el-row>
        </div>
      </template>
    </el-page-header>
  </div>
  <el-scrollbar class="tw-h-fit" :height="height - 50">
    <el-card v-loading="state.loading" class="tw-mb-[18px]" style="max-height: 500px">
      <template #header>
        <div style="display: flex; justify-content: space-between">
          <el-button link type="primary" class="tw-font-semibold" :style="{ color: 'var(--el-color-primary)' }" :disabled="[eventState.COMPLETED, eventState.WAITING_FOR_RECEIVE, eventState.CLOSED, eventState.AUTO_CLOSED].includes(state.data.eventState || ('' as eventState)) || isLockEndTime" v-preventReClick @click="handleEditSummary">
            <span v-if="digestChanged" style="color: red; margin-left: 2px">*</span>
            {{ state.data.summary || "--" }}
          </el-button>
          <div style="display: flex">
            <el-text type="primary">{{ tickGroupConfig.ticketClassificationNames.join("/") }}</el-text>
            <div style="margin-right: 2px" v-for="itemA in sortedLocalesOption" :key="itemA.value">
              <el-tooltip v-if="isLanguageMatch(itemA.value)" class="item" effect="light" :content="getTooltipContent(itemA.value, itemA.zh_label)" placement="bottom">
                <div
                  :style="{
                    background: `url(${itemA.icon}) no-repeat left / auto`,
                    paddingLeft: '30px',
                    width: '22px',
                    height: '22px',
                  }"
                ></div>
              </el-tooltip>
            </div>
            <div>{{ state.data.category }}</div>
          </div>
        </div>
      </template>
      <template #default>
        <FormModel :model="form" :style="{ marginBottom: '-18px' }">
          <!-- <FormItem :span="8" label="创建时间" tooltip="" prop="" :rules="[]">{{ state.data.collectTime ? moment(state.data.collectTime, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</FormItem> -->
          <!-- <FormItem :span="8" label="持续时间" tooltip="" prop="" :rules="[]">{{ state.data.collectTime ? moment(state.data.collectTime, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</FormItem> -->
          <FormItem :span="6" :label="$t('generalDetails.Priority')" tooltip="" prop="" :rules="[]">
            <template #label>
              <span>
                <span v-if="priorityChanged" style="color: red">*</span>
                {{ $t("generalDetails.Priority") }}
              </span>
            </template>
            <el-dropdown trigger="click" @command="handleSetPriority($event, state.data)" :disabled="[eventState.COMPLETED, eventState.WAITING_FOR_RECEIVE, eventState.CLOSED, eventState.AUTO_CLOSED].includes(state.data.eventState || ('' as eventState)) || state.data.operation === EventOperation.CLOSE || isLockEndTime">
              <span class="el-dropdown-link">
                <i class="priority-icon" :style="{ backgroundColor: currentEventState.color || '' }" />
                <span class="tw-align-[2px]" :style="{ color: currentPriority.color || '' }">{{ state.data.priority }}</span>
                <el-icon class="el-icon--right"><ArrowDown></ArrowDown></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="(value, key) in priorityOption" :key="`priority-${key}`" :command="value.value">{{ value.label }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </FormItem>
          <FormItem :span="6" :label="$t('generalDetails.Urgency')" tooltip="" prop="" :rules="[]">
            <template #label>
              <span>
                <span v-if="urgencyChanged" style="color: red">*</span>
                {{ $t("generalDetails.Urgency") }}
              </span>
            </template>
            <el-select :model-value="state.data.urgency || ('' as string)" filterable @change="handleSetSeverity($event, state.data)" :disabled="[eventState.COMPLETED, eventState.WAITING_FOR_RECEIVE, eventState.CLOSED, eventState.AUTO_CLOSED].includes(state.data.eventState || ('' as eventState)) || state.data.operation === EventOperation.CLOSE || isLockEndTime">
              <el-option
                v-for="item in deviceImportanceOption
                  .map((v) => ({
                    ...v,
                    priority: priorityMatrix.filter((raw) => raw.eventSeverity === v.value),
                  }))
                  .filter((v) => v.priority.length)"
                :key="item.value"
                :label="`${item.label}`"
                :value="item.value"
              ></el-option>
              <!-- item.priority.map(v => `${v.priority}：${(find(deviceImportanceOption, (raw) => raw.value === v.deviceImportance) || {}).label || v.deviceImportance}`).join('，') -->
            </el-select>
          </FormItem>
          <FormItem :span="6" :label="$t('generalDetails.Impact')" tooltip="" prop="" :rules="[]">
            <template #label>
              <span>
                <span v-if="impactChanged" style="color: red">*</span>
                {{ $t("generalDetails.Impact") }}
              </span>
            </template>
            <el-select :model-value="state.data.influence || ('' as string)" filterable @change="handleSetImportance($event, state.data)" :disabled="[eventState.COMPLETED, eventState.WAITING_FOR_RECEIVE, eventState.CLOSED, eventState.AUTO_CLOSED].includes(state.data.eventState || ('' as eventState)) || state.data.operation === EventOperation.CLOSE || isLockEndTime">
              <el-option
                v-for="item in deviceImportanceOption.map((v) => ({
                  ...v,
                  priority: priorityMatrix.filter((raw) => raw.deviceImportance === v.value),
                }))"
                :key="item.value"
                :label="`${item.label}`"
                :value="item.value"
              ></el-option>
              <!-- item.priority.map(v => `${v.priority}：${(find(eventSeverityOption, (raw) => raw.value === v.eventSeverity) || {}).label || v.eventSeverity}`).join('，') -->
            </el-select>
          </FormItem>
          <template v-if="!state.data.draft">
            <el-col :span="24" class="tw-mb-[18px] tw-bg-[var(--el-bg-color-page)] tw-py-2" v-if="Number(slaTimeLimit.responseLimit) || Number(slaTimeLimit.resolveLimit)">{{ $t("generalDetails.SLA situation") }}</el-col>
            <FormItem :span="12" :label="$t('generalDetails.Response')" tooltip="" prop="" :rules="[]" v-if="Number(slaTimeLimit.responseLimit)">
              <!-- -->
              <el-progress :percentage="responseTimePercentage" :color="state.data.eventState != 'NEW' ? '#ccc' : responseUrgencyType.color" :format="() => `${convertMinutes(slaTimeLimit.responseTime) || 0} / ${convertMinutes(slaTimeLimit.responseLimit) || 0}`" class="tw-w-full"></el-progress>
            </FormItem>
            <FormItem :span="12" :label="$t('generalDetails.Resolution')" tooltip="" prop="" :rules="[]" v-if="Number(slaTimeLimit.resolveLimit)">
              <!-- -->
              <el-progress :percentage="resolveTimePercentage" :color="[eventState.COMPLETED, eventState.CLOSED, eventState.AUTO_CLOSED].includes(state.data.eventState || ('' as eventState)) ? '#ccc' : resolveUrgencyType.color" :format="() => `${convertMinutes(slaTimeLimit.resolveTime) || 0} / ${convertMinutes(slaTimeLimit.resolveLimit) || 0}`" class="tw-w-full"></el-progress>
            </FormItem>
            <el-col :span="24"></el-col>
            <template v-if="[eventState.CUSTOMER_SUSPENDED, eventState.SERVICE_PROVIDER_SUSPENDED, eventState.SUSPENDED, eventState.PENDING_APPROVAL].includes(state.data.eventState as eventState)">
              <FormItem :span="6" :label="$t('generalDetails.Pause until')" tooltip="" prop="" :rules="[]" v-if="state.data.currentSuspendPauseTime">{{ moment(Number(state.data.currentSuspendPauseTime)).format("YYYY-MM-DD HH:mm:ss") }}</FormItem>

              <FormItem :span="6" :label="$t('generalDetails.Request until')" tooltip="" prop="" :rules="[]" v-if="state.data.currentSuspendRequestTime">{{ moment(Number(state.data.currentSuspendRequestTime)).format("YYYY-MM-DD HH:mm:ss") }}</FormItem>
              <FormItem v-if="state.data.suspendRecordCause" :span="6" style="font-weight: 700">挂起原因: {{ state.data.suspendRecordCause }}</FormItem>
            </template>
            <FormItem :span="6" label="" tooltip="" prop="" :rules="[]" v-if="state.data.currentSuspendRequestTime && state.data.pendingSuspend && [eventState.SERVICE_PROVIDER_SUSPENDED, eventState.PENDING_APPROVAL, eventState.SUSPENDED].includes(state.data.eventState || ('' as eventState))">
              <el-button type="primary" :disabled="!tickGroupConfig.orderApprovalPermission" v-preventReClick @click="handleApproveHangUp(true)">{{ $t("generalDetails.pass") }}</el-button>
              <el-button type="danger" :disabled="!tickGroupConfig.orderApprovalPermission" v-preventReClick @click="handleApproveHangUp(false)">{{ $t("generalDetails.refuse") }}</el-button>
            </FormItem>
          </template>
        </FormModel>
        <div></div>
        <!-- <pre>{{ state.data }}</pre> -->
      </template>
    </el-card>
    <completeCode v-if="[eventState.CLOSED, eventState.AUTO_CLOSED].includes(state.data.eventState || ('' as eventState))" class="tw-mb-[18px]" :title="currentEventState.label || ''" :finishCodeName="state.data.completeInfo?.finishCodeName || ''" :finishCodeDesc="state.data.completeInfo?.finishCodeDesc || ''" :finishContent="state.data.completeInfo?.finishContent || ''" />
    <el-card v-loading="state.loading">
      <template #header>
        <el-radio-group :model-value="(route.query.type as string) || ''" @change="router.push({ query: { ...route.query, type: $event as string } })">
          <el-radio-button :label="pageType.details" :disabled="isLockEndTime">
            <el-badge class="mark">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Details") }}</div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.subtotal" :disabled="isLockEndTime">
            <el-badge class="mark" :value="tabCounts.noteCount || undefined">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Journals") }}</div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.device" :disabled="isLockEndTime">
            <el-badge class="mark" :value="showdeviceBadgeValue">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Devices") }}</div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.contacts" :disabled="isLockEndTime">
            <el-badge class="mark" :value="showContactBadgeValue">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Contacts") }}</div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.project" :disabled="isLockEndTime">
            <el-badge class="mark" :value="undefined">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Projects") }}</div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.related" :disabled="isLockEndTime">
            <el-badge class="mark" :value="tabCounts.relationCount || undefined">
              <div class="tw-px-[1em]">{{ $t("generalDetails.link") }}</div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.alarm" :disabled="isLockEndTime">
            <el-badge class="mark">
              <div class="tw-flex tw-px-[1em]">
                <span>{{ $t("generalDetails.Alerts") }}</span>
                <template v-if="state.data.collectAlertEcho">
                  <span v-show="userInfo.hasPermission(监控管理中心_设备_可读) && userInfo.hasPermission(系统管理中心_客户管理_可读) && userInfo.hasPermission(资产管理中心_设备_可读)" class="unconfirmed tw-mx-[5px]">{{ tabCounts.unconfirmed }}</span>
                  <span v-show="userInfo.hasPermission(监控管理中心_设备_可读) && userInfo.hasPermission(系统管理中心_客户管理_可读) && userInfo.hasPermission(资产管理中心_设备_可读)" class="confirmed tw-mx-[5px]">{{ tabCounts.confirmed }}</span>
                </template>
                <FontAwesomeIcon v-else class="tw-mx-[5px]" :icon="faBan"></FontAwesomeIcon>
              </div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.sla" :disabled="isLockEndTime">
            <el-badge class="mark"> <div class="tw-px-[1em]">SLA</div> </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.strategy" :disabled="isLockEndTime">
            <el-badge class="mark" :value="showBadgeValue"
              ><div class="tw-px-[1em]">{{ $t("generalDetails.Support Notes") }}</div></el-badge
            >
          </el-radio-button>
          <el-radio-button :label="pageType.file" :disabled="isLockEndTime">
            <el-badge class="mark" :value="tabCounts.fileCount || undefined">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Files") }}</div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.dynamics" :disabled="isLockEndTime">
            <el-badge class="mark">
              <div class="tw-px-[1em]">{{ $t("generalDetails.History Log") }}</div>
            </el-badge>
          </el-radio-button>
        </el-radio-group>
      </template>
      <template #default>
        <el-scrollbar>
          <div v-if="((route.query.type as string) || '') === pageType.details">
            <!-- 详述 -->
            <ModelDetails :data="state.data" :refresh="handleRefresh" @changeDesc="(v) => (state.data.description = v)" :height="0"></ModelDetails>
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.subtotal">
            <!-- 小计 -->
            <ModelNotes ref="modelNotesRef" :data="state.data" :refresh="handleRefresh" :height="0"></ModelNotes>
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.device">
            <!-- 设备 -->
            <ModelDevices :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.contacts">
            <!-- 联系人 -->
            <ModelContacts :data="state.data" :refresh="handleRefresh" :height="0" />
            <!-- <ModelExpand v-if="route.params.id" :key="route.params.id" :id="(route.params.id as string)" type="event"></ModelExpand> -->
          </div>

          <div v-if="((route.query.type as string) || '') === pageType.project">
            <ModelProject :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.related">
            <!-- 关联 -->
            <ModelAssociation :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.alarm">
            <!-- 告警记录 -->
            <ModelAlarm :data="state.data" :refresh="handleRefresh" :handleEnd="handleAlarm" ref="ModelAlarmRef" @confrim="(v) => ((collectAlert = v.collectAlert), (operation = v.operation))" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.sla">
            <!-- SLA -->
            <ModelSLA :data="state.data" :refresh="handleRefresh" :height="0"></ModelSLA>
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.strategy">
            <!-- 行动策略 -->
            <ModelStrategy :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.file">
            <!-- 文件 -->
            <ModelFiles :data="state.data as DataItem" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.dynamics">
            <!-- 动态 -->
            <ModelDynamics :data="state.data" :refresh="handleRefresh" :height="0"></ModelDynamics>
          </div>
        </el-scrollbar>
      </template>
    </el-card>
  </el-scrollbar>
  <EventPend
    ref="pendRef"
    :refresh="handleRefresh"
    :ticketTemplateId="state.data.ticketTemplateId"
    @pend="
      (v) => {
        pendQuery = v.params;
        operation = v.type;
      }
    "
  ></EventPend>

  <EventEnd
    ref="endRef"
    :refresh="handleRefresh"
    :ticketTemplateId="state.data.ticketTemplateId"
    @end="
      (v) => {
        completeQuery = v.params;
        ModelAlarm;
        operation = v.type === 'Finish' || v.type == 'ConfirmAndFinish' ? EventOperation.FINISHED : EventOperation.CLOSE;
        console.log(v);
        notesQuery = v.notesForm;
      }
    "
    :height="height - 317"
  ></EventEnd>

  <Editor ref="editorRef" :title="$t('generalDetails.event')" display="dialog">
    <template #setPriority="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          {{ $t("generalDetails.Confirm settings") }}
          <span>{{ ((params.raw || {}) as AnyObject).summary as string }}</span>
          {{ $t("generalDetails.The priority of the event is") }}
          <span>{{ params.label }}</span>
          {{ $t("generalDetails.Is that okay") }}
        </p>
      </div>
    </template>
    <template #setImportance="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          {{ $t("generalDetails.Confirm settings") }}
          <span>{{ ((params.raw || {}) as AnyObject).summary as string }}</span>
          {{ $t("generalDetails.The importance of the event is?") }}
          <span>{{ params.label }}</span>
          {{ $t("generalDetails.Is that okay") }}
        </p>
      </div>
    </template>
    <template #setSeverity="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          {{ $t("generalDetails.Confirm settings") }}
          <span>{{ ((params.raw || {}) as AnyObject).summary as string }}</span>
          {{ $t("generalDetails.The urgency of the event is?") }}
          <span>{{ params.label }}</span>
          {{ $t("generalDetails.Is that okay") }}
        </p>
      </div>
    </template>
  </Editor>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { toRef, ref, reactive, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch, computed, h, provide, toRefs } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Refresh, InfoFilled, ArrowDown, CircleClose } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";
import { ElTable, ElIcon } from "element-plus";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import ModelDetails from "./models/details.vue";
import ModelSubtotal from "./models/subtotal.vue";
import ModelSLA from "./models/sla.vue";
import ModelDynamics from "./models/dynamics.vue";

import ModelNotes from "./models/notes.vue";

import ModelDevices from "./models/devices.vue";

import ModelContacts from "./models/contacts.vue";
// import ModelExpand from "@/views/pages/modelExpand/Model.vue";

import ModelProject from "./models/project.vue";

import ModelAssociation from "./models/association.vue";

import ModelFiles from "./models/files.vue";

import ModelAlarm from "./models/alarm.vue";

import ModelStrategy from "./models/strategy.vue";

import EventPend from "./models/pend.vue";

import EventEnd from "./models/end.vue";

import Editor from "./Editor.vue";

import completeCode from "@/components/completeCode.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox } from "element-plus";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { useSiteConfig } from "@/stores/siteConfig";
import getUserInfo from "@/utils/getUserInfo";
import { find } from "lodash-es";
import moment from "moment";

import { type BaseItem, DataItem, type Item } from "./helper";
import { state } from "./helper";
import { resetData, command } from "./helper";

/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
import { eventState, eventStateOption, eventSeverity, eventSeverityOption, priority, priorityOption, urgencyType, urgencyTypeOption } from "@/views/pages/apis/event";
import { deviceImportance, deviceImportanceOption } from "@/views/pages/apis/device";

import { getEventData as getItemData } from "@/views/pages/apis/event";
import { addEventData as addItemData, setEventData as setItemData, modEventData as modItemData, delEventData as delItemData } from "@/views/pages/apis/event";
import { setEventDataByPriority, setEventDataByTakeOver, setEventDataByApprove, setEventDataByImportance, setEventDataBySeverity, setEventDataByTransferOrUpgrade, type TimeLimit, eventOperationOption, EventOperation } from "@/views/pages/apis/event";
import { getPriorityMatrixList } from "@/views/pages/apis/eventPriority";
import { getUserGroupByPermissionIds, getUserByGroup, type GroupItem, type EntrustUserItem } from "@/api/personnel";
import { eventEditSummary as editSummary, getDetailTapCountById, type TabCount, setEventEditable, getEventSlaTimeLimitById, type SlaTimeLimit, eventApproveHangUp } from "@/views/pages/apis/eventManage";

import { faBan } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import _timeZoneHours from "@/views/pages/common/offsetHours.json";
import { localesOption } from "@/api/locale";
import { getTenantInfo } from "@/views/pages/apis/tenant";
import { getContactTypes as getType, type ContactsTypeItem, type ContactsItem } from "@/views/pages/apis/contacts";
import { eventBatchContact as getData, eventBatchdesensitized, eventAddContact as addData, eventDelContact as delData, getChangeContacts } from "@/views/pages/apis/eventManage";

import { getCloseDirectly, getOrderUserGroup, AssignableTicketType, UserGroupConfigurationItem, getOrderUserGroupIsClose, getOrderApprovalPermission, getTicketClassificationNames } from "@/views/pages/apis/orderGroup";

import { 智能事件中心_服务请求工单_编辑小记, 智能事件中心_事件工单_编辑小记, 智能事件中心_DICT事件管理_编辑小记, 智能事件中心_DICT服务请求_编辑小记, 智能事件中心_变更工单_编辑小记, 智能事件中心_问题工单_编辑小记, 智能事件中心_发布管理_编辑小记 } from "@/views/pages/permission";
import { 智能事件中心_事件工单_审批, 智能事件中心_事件工单_分配用户组, 智能事件中心_事件工单_分配用户, 智能事件中心_用户组_分配工单, 智能事件中心_用户_分配工单, 安全管理中心_用户组管理_可读, 监控管理中心_设备_可读, 资产管理中心_行动策略_可读, 资产管理中心_设备_可读, 系统管理中心_客户管理_可读, 资产管理中心_联系人_可读 } from "@/views/pages/permission";
import { addOrderNode } from "@/views/pages/apis/event";

const contactsType = ref<{ contactId: string; contactType: string }[]>([]);
const contacts = ref<ContactsItem[]>([]);
const uniqueByLanguage = ref<ContactsItem[]>([]);
const sortedLocalesOption = ref<ContactsItem[]>(localesOption);
const timeZoneHours = ref(_timeZoneHours);

// h(FontAwesomeIcon, { icon: faBan });

/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const OperateChanged = ref(false);
const modelNotesRef = ref();
const alertOrderMappingItems = ref([]);
const ModelAlarmRef = ref<InstanceType<typeof ModelAlarm>>();
const { t } = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "alarmBoard" });
const siteConfig = useSiteConfig();
const userInfo = getUserInfo();

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
interface Props {
  width?: number;
  height?: number;
  title?: string;
}
const props = withDefaults(defineProps<Props>(), { title: "告警" });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

const _width = inject("width", ref(0));
const _height = inject("height", ref(0));

const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);

const tableRef = ref<InstanceType<typeof ElTable>>();
const editorRef = ref<InstanceType<typeof Editor>>();

// provide(
//   "detailData",
//   computed(() => state.data)
// );
provide("detailData", toRefs(state).data);

const verifyPermissionIds = ref<string[]>([]);
provide("verifyPermissionIds", verifyPermissionIds);

const stateRightText = computed(() => (eventOperationOption.find(({ value }) => value === state.data.operation) || {}).label);

/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Form {
  priority: string;
}
interface AnyObject {
  [key: string]: any;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const timer = ref<null | NodeJS.Timer>(null);
const autoRefreshTime = ref(0);

enum pageType {
  details = "",
  subtotal = "subtotal",
  device = "device",
  contacts = "contacts",
  related = "related",
  alarm = "alarm",
  sla = "sla",
  strategy = "strategy",
  file = "file",
  dynamics = "dynamics",
  project = "project",
}

const currentEventState = computed(() => find(eventStateOption, (v) => v.value === state.data.eventState) || ({} as Partial<(typeof eventStateOption)[number]>));
const currentPriority = computed(() => find(priorityOption, (v) => v.value === state.data.priority) || ({} as Partial<(typeof priorityOption)[number]>));

const responseUrgencyType = computed(() => setSlaState((state.data.slaTimeLimit || {}).responseTimeLimits || [], Number(slaTimeLimit.value.responseTime) || 0));
const resolveUrgencyType = computed(() => setSlaState((state.data.slaTimeLimit || {}).completedTimeLimits || [], Number(slaTimeLimit.value.resolveTime) || 0));
const showBadgeValue = computed(() => {
  if (userInfo.hasPermission(资产管理中心_行动策略_可读) && userInfo.hasPermission(资产管理中心_设备_可读)) {
    return Number(tabCounts.value.actionCount) || undefined;
  }
  return undefined;
});
const showContactBadgeValue = computed(() => {
  if (userInfo.hasPermission(资产管理中心_联系人_可读) && userInfo.hasPermission(资产管理中心_设备_可读)) {
    return Number(tabCounts.value.contactCount) || undefined;
  }
  return undefined;
});
const showdeviceBadgeValue = computed(() => {
  if (userInfo.hasPermission(资产管理中心_设备_可读)) {
    return Number(tabCounts.value.deviceCount) || undefined;
  }
  return undefined;
});

const isLockEndTime = computed(() => {
  if (state.data.lockEndTime === "-1") return false;
  else if (new Date() <= new Date(Number(state.data.lockEndTime))) return true;
  else return false;
});

function timeZoneSwitching(): number {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == userInfo.zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
function setSlaState(list: TimeLimit[], val: number) {
  let result = urgencyType.NORMAL;
  list.unshift({
    urgencyType: "NORMAL",
    tolerateMinutes: 0,
  });
  list.sort((a: any, b: any) => {
    return Number(a.tolerateMinutes) - Number(b.tolerateMinutes);
  });

  let index = list.findIndex((v, i) => {
    if (v.tolerateMinutes > val) {
      // console.log(i);
      return i;
    }
  });
  if (index != -1) {
    result = list[index - 1].urgencyType;
  } else {
    result = list[list.length - 1].urgencyType;
  }
  // list.sort((a, b) => (Number(b.tolerateMinutes) || 0) - (Number(a.tolerateMinutes) || 0)).forEach((el) => ((Number(el.tolerateMinutes) || 0) >= val || list.length === 1) && (result = el.urgencyType));
  return find(urgencyTypeOption, ({ value }) => value === result) || { label: "", value: "", color: "#eee" };
}

const eventStat = ref<{ label: string; value: eventState; color?: string; count: number }[]>([]);

const form = reactive<Form>({
  priority: "",
});

// function convertMinutes(minutes: any) {
//   const duration = moment.duration(minutes, "minute");
//   const days = Math.floor(minutes / (60 * 24));
//   const hours = Math.floor((minutes % (60 * 24)) / 60);
//   const year = Math.floor(duration._data.years);
//   const months = Math.floor(minutes / (60 * 24 * 30));
//   const mins = minutes % 60;
//   const week = Math.floor(minutes / (7 * 24 * 60));
// }
function convertMinutes(minutes: any) {
  var seconds = minutes * 60; // 将分钟转化为秒数

  var years = Math.floor(seconds / 31536000); // 每年有31536000秒（平均）
  seconds -= years * 31536000;

  var months = Math.floor(seconds / 2592000); // 每月有2592000秒（平均）
  seconds -= months * 2592000;

  var weeks = Math.floor(seconds / 604800); // 每周有604800秒（平均）
  seconds -= weeks * 604800;

  var days = Math.floor(seconds / 86400); // 每天有86400秒（平均）
  seconds -= days * 86400;

  var hours = Math.floor(seconds / 3600); // 每小时有3600秒
  seconds -= hours * 3600;

  const mins = minutes % 60;

  return `${years > 0 ? years + "Y" : ""}${months > 0 ? months + "M" : ""}${weeks > 0 ? weeks + "W" : ""}${days > 0 ? days + "D" : ""}${hours > 0 ? hours + "H" : ""}${mins > 0 ? mins + "m" : ""}`;
}

const responseTimePercentage = computed(() => (((Number(slaTimeLimit.value.responseTime) || 0) / (Number(slaTimeLimit.value.responseLimit) || 0)) * 100 > 100 ? 100 : (Number(slaTimeLimit.value.responseTime) / Number(slaTimeLimit.value.responseLimit)) * 100) || 0);
const resolveTimePercentage = computed(() => (((Number(slaTimeLimit.value.resolveTime) || 0) / (Number(slaTimeLimit.value.resolveLimit) || 0)) * 100 > 100 ? 100 : (Number(slaTimeLimit.value.resolveTime) / Number(slaTimeLimit.value.resolveLimit)) * 100) || 0);

const priorityMatrix = ref<{ eventSeverity: eventSeverity; deviceImportance: deviceImportance; priority: priority }[]>([]);
const userGroups = ref<Record<string, any>[]>([]);
const userList = ref<EntrustUserItem[]>([]);
const typeOfTransferOrUpgrade = ref<"transfer" | "eventUpgrade">("transfer");

type typeTransferOrUpgrade = { userGroupId: string; userId: string };
const transferOrUpgrade = ref<typeTransferOrUpgrade>({} as typeTransferOrUpgrade);

const operation = ref<EventOperation>("" as EventOperation);
const collectAlert = ref(true);

type PendQuery = { durationMinutes: Number; suspendRecordCause: string; suspendRecordNote: string; privateCustomerId: string; privateAble: boolean };
const pendQuery = ref<PendQuery>({} as PendQuery);

type CompleteQuery = { completeInfo: { finishCodeName: string; finishCodeDesc: string; finishContent: string }; closeAlert: boolean };
const completeQuery = ref<CompleteQuery>({} as CompleteQuery);
type NotesQuery = { content: string; privateAble: boolean };
const notesQuery = ref<NotesQuery>({} as NotesQuery);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  handleRefresh().then(() => (autoRefreshTime.value = 60));
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {
  if (timer.value !== null) {
    clearInterval(timer.value);
    timer.value = null;
  }
}
function beforeDestroy() {
  if (timer.value !== null) {
    clearInterval(timer.value);
    timer.value = null;
  }
}
function destroyed() {}

watch(autoRefreshTime, (autoRefreshTime) => {
  if (timer.value !== null) timer.value = (clearInterval(timer.value), null);
  if (autoRefreshTime) timer.value = setInterval(getEventSlaTimeLimit, autoRefreshTime * 1000);
});
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

const slaTimeLimit = ref<SlaTimeLimit>({});
async function getEventSlaTimeLimit() {
  try {
    const { success, data, message } = await getEventSlaTimeLimitById({ id: route.params.id as string });
    if (!success) throw new Error(message);
    slaTimeLimit.value = data;
  } catch (error) {
    error instanceof Error && ElMessage.success(error.message);
  }
}

async function handleEventSave() {
  // console.log(collectAlert.value);
  try {
    const params: Partial<Record<string, unknown>> = {
      id: route.params.id,
      priority: state.data.priority,
      urgency: state.data.urgency,
      influence: state.data.influence,
      ...transferOrUpgrade.value,
      desc: state.data.description,
      externalId: state.data.externalId,
      summary: state.data.summary,
      operation: operation.value || null,
      ...pendQuery.value,
      ...completeQuery.value,
      collectAlertClose: collectAlert.value,
    };
    if (params.userGroupId || params.userId) params.upgradeFlag = typeOfTransferOrUpgrade.value === "eventUpgrade";
    const { success, data, message } = await setEventEditable(params);
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    if (hasValidContent(notesQuery.value.content)) {
      submitNote();
    }
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    await handleRefresh();
  }
}
const hasValidContent = (html) => {
  if (html === undefined || html === null) {
    return false;
  }
  // 1. 过滤空标签（包括自闭合标签）
  const cleanedHtml = html.replace(/<([a-z][a-z0-9]*)\b[^>]*>(?:\s|&nbsp;)*<\/\1>|<\w+\s*\/>/gi, "");
  // 2. 移除所有空格（包括换行、制表符等）
  const trimmedContent = cleanedHtml.replace(/\s+/g, "").trim();
  // 3. 返回是否有有效内容
  return trimmedContent.length > 0;
};
async function submitNote() {
  try {
    if (!notesQuery.value.content && ![EventOperation.FINISHED, EventOperation.CLOSE].includes((operation.value || "") as EventOperation)) return;
    const formData = new FormData();
    formData.append("nodeContent", notesQuery.value.content);
    formData.append("privateAble", !!notesQuery.value.privateAble as never);
    formData.append("privateCustomerId", userInfo.tenantId);
    formData.append("tenantId", (userInfo.currentTenant || {}).id as string);
    formData.append("orderType", "EVENT_ORDER");
    formData.append("permissionId", EditNodePermissionId["EVENT_ORDER"]);
    formData.append("orderId", route.params.id as string);
    formData.append("orderIdsJson", JSON.stringify([route.params.id]));
    const { success, message } = await addOrderNode(formData as any);
    if (!success) throw new Error(message);
    //ElMessage.success("操作成功");
    if (modelNotesRef.value) {
      modelNotesRef.value.getEventNotes(); // 安全调用
    }
    handleRefresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    await queryData();

    notesQuery.value = {} as NotesQuery;
  }
}
enum EditNodePermissionId {
  EVENT_ORDER = 智能事件中心_事件工单_编辑小记,
  SERVICE_REQUEST = 智能事件中心_服务请求工单_编辑小记,
  DICT_EVENT_ORDER = 智能事件中心_DICT事件管理_编辑小记,
  DICT_SERVICE_REQUEST = 智能事件中心_DICT服务请求_编辑小记,
  CHANGE = 智能事件中心_变更工单_编辑小记,
  QUESTION = 智能事件中心_问题工单_编辑小记,
  PUBLISH = 智能事件中心_发布管理_编辑小记,
}

function handleEventOperateCommand(v: eventState) {
  switch (v) {
    case eventState.PROCESSING:
      // 处理中
      // handleBatchAccept(detailData);
      break;
    case eventState.SUSPENDED:
      // 挂起
      // handleEventPend(detailData);
      break;
    case eventState.COMPLETED:
      // 完成
      // handleEventEnd(detailData, "Finish");
      break;
    case eventState.CLOSED:
      // 关闭
      // handleEventEnd(detailData, "Close");
      break;
  }
}

// async function handleCommand(type: command, data?: Record<string, unknown>) {
//   const time = autoRefreshTime.value;
//   autoRefreshTime.value = 0;
//   try {
//     state.loading = true;
//     await nextTick();
//     switch (type) {
//       case command.Refresh:
//         await resetData();
//         await queryData();
//         break;
//       case command.Request:
//         await queryData();
//         break;
//       case command.Preview:
//         await previewItem(data as Record<string, unknown>);
//         break;
//       case command.Create:
//         await createItem(data as Record<string, unknown>);
//         await resetData();
//         await queryData();
//         break;
//       case command.Update:
//         await rewriteItem(data as Record<string, unknown>);
//         await resetData();
//         await queryData();
//         break;
//       case command.Modify:
//         await modifyItem(data as Record<string, unknown>);
//         await resetData();
//         await queryData();
//         break;
//       case command.Delete:
//         await deleteItem(data as Record<string, unknown>);
//         await resetData();
//         await queryData();
//         break;
//     }
//   } catch (error) {
//     if (error instanceof Error) {
//       const message = error.message;
//       await resetData();
//       await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
//       await queryData();
//     }
//   } finally {
//     autoRefreshTime.value = time;
//     state.loading = false;
//   }
// }
const userGroupChanged = ref(false);
async function handleSetTransferOrUpgrade(req: { userGroupId: string; userId: string; type: "transfer" | "eventUpgrade"; status: string }, raw: Partial<DataItem>) {
  // const time = autoRefreshTime.value;
  try {
    // autoRefreshTime.value = 0;
    // state.loading = true;
    // const { success, message, data } = await setEventDataByTransferOrUpgrade({ id: raw.id as string, ...req });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功${req.type === "eventUpgrade" ? "升级事件" : "转交事件"}`);

    // 三期
    transferOrUpgrade.value.userGroupId = req.userGroupId;
    userGroupChanged.value = true;
    if (req.status === "user") {
      transferOrUpgrade.value.userId = req.userId;
    } else {
      transferOrUpgrade.value.userId = "";
    }

    if (req.userGroupId) {
      await (async (req?: { id: string }) => {
        try {
          if (!req) return;
          const { success, message, data: res } = await getUserByGroup({ id: req.id as string });
          if (!success) throw Object.assign(new Error(message), { success, data: res });
          userList.value = res;
        } catch (error) {
          if (error instanceof Error) ElMessage.error(error.message);
        }
      })({ id: req.userGroupId });
    }
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    // state.loading = false;
  }
}
const userChanged = ref(false);
async function handleSetTransferOrUser(req: { userGroupId: string; userId: string; type: "transfer" | "eventUpgrade"; status: string }, raw: Partial<DataItem>) {
  try {
    // 三期
    transferOrUpgrade.value.userGroupId = req.userGroupId;
    userChanged.value = true;
    if (req.status === "user") {
      transferOrUpgrade.value.userId = req.userId;
    } else {
      transferOrUpgrade.value.userId = "";
    }

    if (req.userGroupId) {
      await (async (req?: { id: string }) => {
        try {
          if (!req) return;
          const { success, message, data: res } = await getUserByGroup({ id: req.id as string });
          if (!success) throw Object.assign(new Error(message), { success, data: res });
          userList.value = res;
        } catch (error) {
          if (error instanceof Error) ElMessage.error(error.message);
        }
      })({ id: req.userGroupId });
    }
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
  }
}
const priorityChanged = ref(false);
async function handleSetPriority(priority: priority, raw: Partial<DataItem>) {
  priorityChanged.value = true;
  // const priorityItem = (find(priorityOption, ({ value }) => value === priority) || {}).label || priority;

  // if (!editorRef.value) return;
  // const time = autoRefreshTime.value;
  try {
    // autoRefreshTime.value = 0;
    // state.loading = true;

    // const params = { priority, label: priorityItem, raw };
    // const form = {};
    // await editorRef.value.confirm({ ...form, ...params, $type: "warning", $title: `修改优先级`, $slot: "setSeverity" }, async (form: Record<string, unknown>) => {

    // 修改优先级请求,三期改为保存按钮弃用
    // const { success, message, data } = await setEventDataByPriority({ id: raw.id as string, priority });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功修改优先级`);
    // // console.log(priority);

    // 三期
    state.data.priority = priority;
    // });
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    state.loading = false;
  }
}
const impactChanged = ref(false);
async function handleSetImportance(importance: deviceImportance, raw: Partial<DataItem>) {
  impactChanged.value = true;
  // const importanceItem = (find(deviceImportanceOption, ({ value }) => value === importance) || {}).label || importance;

  // if (!editorRef.value) return;
  // const time = autoRefreshTime.value;
  try {
    // autoRefreshTime.value = 0;
    // state.loading = true;

    // const params = { importance, label: importanceItem, raw };
    // const form = {};
    // await editorRef.value.confirm({ ...form, ...params, $type: "warning", $title: `修改重要性`, $slot: "setImportance" }, async (form: Record<string, unknown>) => {

    // 修改重要性,三期改为保存按钮弃用
    // const { success, message, data } = await setEventDataByImportance({ id: raw.id as string, importance });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功修改重要性`);
    // });

    // 三期
    state.data.influence = importance;
    setPriority();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    state.loading = false;
  }
}
const urgencyChanged = ref(false);

async function handleSetSeverity(severity: eventSeverity, raw: Partial<DataItem>) {
  // 设置变更标志
  urgencyChanged.value = true;
  // const severityItem = (find(eventSeverityOption, ({ value }) => value === severity) || {}).label || severity;

  // if (!editorRef.value) return;
  // const time = autoRefreshTime.value;
  try {
    // autoRefreshTime.value = 0;
    // state.loading = true;

    // const params = { severity, label: severityItem, raw };
    // const form = {};
    // await editorRef.value.confirm({ ...form, ...params, $type: "warning", $title: `修改紧急性`, $slot: "setSeverity" }, async (form: Record<string, unknown>) => {

    // 修改紧急性,三期改为保存按钮弃用
    // const { success, message, data } = await setEventDataBySeverity({ id: raw.id as string, severity });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功修改紧急性`);
    // });

    // 三期
    state.data.urgency = severity;
    setPriority();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    state.loading = false;
  }
}

function setPriority() {
  if (state.data.urgency && state.data.influence) state.data.priority = (find(priorityMatrix.value, (v) => v.eventSeverity === state.data.urgency && v.deviceImportance === state.data.influence) || {}).priority || state.data.priority;
}
async function getContact() {
  const id: string = route.params.id;
  try {
    const [{ success: contactSuccess, message: contactMessage, data: contactData }, { success: tabsSuccess, message: tabsMessage, data: tabsData }] = await Promise.all([getChangeContacts({ id }), getType({})]);
    if (!contactSuccess) throw Object.assign(new Error(contactMessage), { success: contactSuccess, data: contactData });
    contactsType.value = contactData instanceof Array ? contactData : [];
    if (contactSuccess) {
      const { success, message, data } = await eventBatchdesensitized({ deviceIds: Array.from(contactsType.value.reduce((p, c) => p.add(c.contactId), new Set<string>()).values()) });
      if (!success) throw Object.assign(new Error(message), { success, data });
      contacts.value = data instanceof Array ? data : [];
      const { success: successTenant, message: messageTenant, data: dataTenant } = await getTenantInfo({});
      if (!successTenant) throw Object.assign(new Error(messageTenant), { success: successTenant, dataTenant });
      contacts.value.unshift(dataTenant);
      // 去重方法
      uniqueByLanguage.value = contacts.value.reduce((acc, current) => {
        // 查找当前数组中是否已存在相同 language 的项
        const existingIndex = acc.findIndex((item) => item.language === current.language);

        if (existingIndex === -1) {
          // 如果不存在，直接添加当前项
          acc.push(current);
        } else {
          // 如果已存在，判断当前项是否更优（tenantName 为假）
          const existingItem = acc[existingIndex];
          if (!current.tenantName && existingItem.tenantName) {
            // 替换为更优的当前项（tenantName 为假）
            acc.splice(existingIndex, 1, current);
          }
        }
        return acc;
      }, []);
      // 提取 uniqueByLanguage 中的语言顺序
      const languageOrder = uniqueByLanguage.value.map((item) => item.language);

      // 根据语言顺序对 localesOption 进行排序
      sortedLocalesOption.value.sort((a, b) => {
        const indexA = languageOrder.indexOf(a.value);
        const indexB = languageOrder.indexOf(b.value);
        return indexA - indexB;
      });
    }
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : `${error}`);
  }
}

function isLanguageMatch(value) {
  return uniqueByLanguage.value.some((item) => item.language === value);
}
// 新增方法
function getTooltipContent(language, zhLabel) {
  const matchedItem = uniqueByLanguage.value.find((item) => item.language === language);
  return matchedItem?.tenantName ? `联系人: ${zhLabel}` : `客户: ${zhLabel}`;
}
async function handleRefresh() {
  try {
    state.loading = true;
    await getEventSlaTimeLimit();
    await nextTick();
    await resetData();
    await queryData();
    await getTabCount();
    await getContact();
    await resetChangeState();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
async function handleQuery() {
  try {
    state.loading = true;
    await nextTick();
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
const tabCounts = ref<TabCount>({} as TabCount);
async function getTabCount() {
  const { success, data, message } = await getDetailTapCountById({ id: route.params.id as string });
  if (!success) throw new Error(message);
  tabCounts.value = data;
}

const tickGroupConfig = ref({
  closeDirectly: false,
  isClose: false,
  orderApprovalPermission: false,
  ticketClassificationNames: [],
});

const orderIsClose = computed(() => {
  if ([eventState.NEW].includes(state.data.eventState as eventState)) return false;
  if (tickGroupConfig.value.closeDirectly && tickGroupConfig.value.isClose) return true;
  else if ([EventOperation.FINISHED].includes(state.data.operation as EventOperation) && tickGroupConfig.value.isClose) return true;
  else return false;
});

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

async function getTackerGroupConfig() {
  try {
    const [
      /* 是否直接关闭 */
      { data: closeDirectlyData, message: closeDirectlyMessage, success: closeDirectlySuccess },
      { data: isCloseData, message: isCloseMessage, success: isCloseSuccess },
      { data: orderApprovalPermissionData, message: orderApprovalPermissionMessage, success: orderApprovalPermissionSuccess },
      ticketClassificationNames,
    ] = await Promise.all([
      /* 获取是否直接关闭 */
      getCloseDirectly({ tenantId: (userInfo.currentTenant || {}).id, ticketTemplateId: (state.data as any).ticketTemplateId }),
      getOrderUserGroupIsClose({ tenantId: (userInfo.currentTenant || {}).id, type: AssignableTicketType.event, userId: userInfo.userId, ticketTemplateId: (state.data as any).ticketTemplateId }),
      getOrderApprovalPermission({ tenantId: (userInfo.currentTenant || {}).id, userId: userInfo.userId, type: AssignableTicketType.event, ticketTemplateId: (state.data as any).ticketTemplateId, orderId: state.data.id }),
      getTicketClassificationNames(AssignableTicketType.event, (state.data as any).ticketClassificationId, (state.data as any).ticketTemplateId),
    ]);
    if (!closeDirectlySuccess) throw new Error(closeDirectlyMessage);
    if (!isCloseSuccess) throw new Error(isCloseMessage);
    if (!orderApprovalPermissionSuccess) throw new Error(orderApprovalPermissionMessage);
    tickGroupConfig.value.closeDirectly = closeDirectlyData;
    tickGroupConfig.value.isClose = isCloseData;
    tickGroupConfig.value.orderApprovalPermission = orderApprovalPermissionData;
    tickGroupConfig.value.ticketClassificationNames = ticketClassificationNames;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

async function queryData() {
  c
  try {
    const [
      /*  */
      { success, message, data },
      { success: prioritySuccess, message: priorityMessage, data: priorityData },
    ] = await Promise.all([
      /*  */
      getItemData({ id: route.params.id as string }),
      getPriorityMatrixList({}),
      /* getUserGroupByPermissionIds({ queryPermissionId: 安全管理中心_用户组管理_可读, verifyPermissionIds: [智能事件中心_用户组_分配工单] }) */ /* ({ appId: (siteConfig.baseInfo || {}).app, external: false, ownToLoginUser: true }) */
      ,
    ]);
    if (!success) throw Object.assign(new Error(message), { success, data });
    if (!prioritySuccess) throw Object.assign(new Error(priorityMessage), { success: prioritySuccess, data: priorityData });
    const {
      success: userGroupSuccess,
      message: userGroupMessage,
      data: userGroupData,
    } = await getOrderUserGroup({
      type: AssignableTicketType.event,
      ...((state.data as any).ticketTemplateId ? { ticketTemplateId: (state.data as any).ticketTemplateId } : { ticketGroupId: (state.data as any).ticketGroupId }),
    });
    if (!userGroupSuccess) throw Object.assign(new Error(userGroupMessage), { success: userGroupSuccess, data: userGroupData });

    await (async (req?: UserGroupConfigurationItem) => {
      try {
        if (!req) return;
        const { success, message, data: res } = await getUserByGroup({ id: req.userGrouptId as string });
        if (!success) throw Object.assign(new Error(message), { success, data: res });
        userList.value = res;
      } catch (error) {
        if (error instanceof Error) ElMessage.error(message);
      }
    })(find(userGroupData, (v) => v.userGrouptId === (data.userGroupId || data.displayUserGroupId)));
    alertOrderMappingItems.value = priorityData.alertOrderMappingItems.map((v) => {
      return {
        label: v.severity,
        value: v.severity,
      };
    });
    priorityMatrix.value = priorityData.priorityMatrixItems.map((v) => {
      return {
        eventSeverity: v.urgency,
        deviceImportance: v.influence,
        priority: v.priority,
      };
    });

    userGroups.value = userGroupData.map((v) => ({ id: v.userGrouptId, name: v.userGroupName, tenantAbbreviation: v.abbreviation }));
    // if (userInfo.currentTenantId !== data.tenantId) userInfo.cutTenant(data.tenantId);
    setTimeout(() => {
      transferOrUpgrade.value.userGroupId = (find(userGroups.value, (v) => v.id === (state.data.userGroupId || state.data.displayUserGroupId)) || {}).id as string;
      if (state.data.userGroupId == null) transferOrUpgrade.value.userId = (find(userList.value, (v) => v.id === state.data.actorId) || {}).id as string;
    }, 0);

    state.data = data;
    state.data.currentSuspendPauseTime = state.data.currentSuspendPauseTime ? Number(state.data.currentSuspendPauseTime) + timeZoneSwitching() : state.data.currentSuspendPauseTime;
    state.data.currentSuspendRequestTime = state.data.currentSuspendRequestTime ? Number(state.data.currentSuspendRequestTime) + timeZoneSwitching() : state.data.currentSuspendRequestTime;

    verifyPermissionIds.value = data.verifyPermissionIds || [];

    await getTackerGroupConfig();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  }
}

async function createItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await addItemData({ ...form });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`成功确认告警`);
  });
}

/** 审批 */
async function handleApproveHangUp(approve: Boolean) {
  try {
    const { success, message } = await eventApproveHangUp({ id: route.params.id as string, approve: approve as boolean });
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    await queryData();
  }
}
// async function previewItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setItemData({ ids: form.ids as string[], ...form });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
// async function rewriteItem(row: Record<string, unknown>) {
//   const title = (find(priorityOption, ({ value }) => value === row.priority) || {}).label || row.priority;
//   if (!editorRef.value) return row;
//   const params = { select: (<Item[]>row.select).filter((v) => row.priority !== v.priority) };
//   await editorRef.value.confirm({ ...row, ...params, $type: "warning", $title: `批量${title}`, $slot: "batchConfirm" }, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setEventDataByPriority({ id: (<Item[]>form.select).map((v) => v.id), priority: form.priority as priority });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`成功${form.$title}`);
//   });
// }
// async function modifyItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setItemData({ ids: form.ids as string[], ...form });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
// async function deleteItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await delItemData(form);
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
function handleCancel() {
  if ("fallback" in route.query && typeof route.query.fallback === "string") router.push({ name: route.query.fallback, params: { id: route.query.deviceId }, query: { type: route.query.status } });
  else if (history.length === 1) window.close();
  else router.back();
}

async function handleAccept(row: Partial<DataItem>) /* 接手 */ {
  try {
    operation.value = EventOperation.TAKE_OVER;
    OperateChanged.value = true;
    // state.loading = true;
    // const { success, message, data } = await setEventDataByTakeOver({ id: row.id as string });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功接手事件`);
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    state.loading = false;
  }
}

const digestChanged = ref(false);
async function handleEditSummary() {
  ElMessageBox.prompt("请输入摘要", "编辑摘要", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputValue: state.data.summary,
    inputValidator: (value: string) => {
      return !!value;
    },
    inputErrorMessage: "请输入摘要",
    beforeClose: async (action, instance, done) => {
      try {
        if (action === "confirm") {
          digestChanged.value = true;
          state.data.summary = instance.inputValue;
          //   const { success, data, message } = await editSummary({ id: route.params.id as string, desc: instance.inputValue });
          //   if (!success) throw new Error(message);
          //   ElMessage.success("操作成功");
          //   handleRefresh();
          done();
        } else done();
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    },
  })
    .then(() => {
      /* code */
    })
    .catch(() => {
      /* code */
    });
}

const endRef = ref<InstanceType<typeof EventEnd>>();
async function handleEnd(data: Partial<DataItem>, type: string) {
  if (!endRef.value) return false;
  // console.log(type, data, 5555);
  // if (type === "ConfirmAndFinish") {
  //   operation.value = "FINISHED";
  // }
  endRef.value.open(data, type);
  OperateChanged.value = true;
}
async function handleAlarm(data: Partial<DataItem>, type: string) {
  if (!endRef.value) return false;
  // console.log(type, data, 5555);
  // if (type === "ConfirmAndFinish") {
  //   operation.value = "FINISHED";
  // }
  endRef.value.open(data, type);
}

const pendRef = ref<InstanceType<typeof EventPend>>();

async function handleApprove(row: Partial<DataItem>, approveType: eventState.CUSTOMER_SUSPENDED | eventState.SERVICE_PROVIDER_SUSPENDED) /* 挂起 */ {
  if (!pendRef.value) return false;
  // operation.value = approveType as never;
  pendRef.value.open(row, approveType);
  OperateChanged.value = true;
  // try {
  //   state.loading = true;
  //   const { success, message, data } = await setEventDataByApprove({ id: row.id as string, approve: false });
  //   if (!success) throw Object.assign(new Error(message), { success, data });
  //   ElMessage.success(`成功接手事件`);
  // } catch (error) {
  //   if (error instanceof Error) {
  //     const message = error.message;
  //     await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
  //   }
  // } finally {
  //   await queryData();
  //   state.loading = false;
  // }
}

async function resetChangeState() {
  priorityChanged.value = false;
  impactChanged.value = false;
  urgencyChanged.value = false;
  OperateChanged.value = false;
  digestChanged.value = false;
  userGroupChanged.value = false;
  userChanged.value = false;
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
defineSlots<{}>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var.scss";

.el-radio-button.is-active {
  .unconfirmed {
    color: $color-white;
  }
  .confirmed {
    color: $color-white;
  }
}

.el-radio-button {
  .unconfirmed {
    color: $color-danger;
  }
  .confirmed {
    color: $color-black;
  }
}

.event {
  :deep(.draft-tooltip) {
    background: var($color-danger) !important;
  }
}
</style>

<style lang="scss">
@import "@/styles/theme/common/var.scss";
.draft-tooltip {
  background: $color-danger !important;
  border: 1px solid $color-danger;
  color: #fff;
}

.draft-tooltip .el-popper__arrow::before {
  background: $color-danger !important;
  border: 1px solid $color-danger;
}
</style>
