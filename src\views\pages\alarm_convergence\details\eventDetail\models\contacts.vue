<template>
  <el-scrollbar :style="{ height: `${height - 95}px` }">
    <div v-for="tab in tabs" :key="tab.code">
      <el-row v-loading="loading" :gutter="16" class="tw-mx-[2px]">
        <el-col :span="12" class="tw-mb-[16px] tw-flex">
          <h3 style="font-weight: 400; font-size: 14px; color: #000">
            {{ $t(`generalDetails.${tab.cnName}`)}}
          </h3>
        </el-col>
        <el-col :span="12" class="tw-mb-[16px] tw-flex">
          <el-tooltip :content="$t('glob.noPower')" :disabled="verifyPermissionIds.includes('612913037728284672')">
            <span class="tw-ml-auto tw-h-fit">
              <el-button type="primary" :disabled="!verifyPermissionIds.includes('612913037728284672') || [eventState.CLOSED, eventState.AUTO_CLOSED].includes((detail.eventState as eventState) || ('' as eventState))" @click="createItem(tab)">{{ $t("generalDetails.Assign") + " " + $t(`generalDetails.${tab.cnName}`) }}</el-button>
            </span>
          </el-tooltip>
        </el-col>
        <template v-for="contact in contactsObj[tab.code]" :key="`contact_${contact.id}`">
          <el-col :xs="12" :sm="12" :md="12" :lg="8" :xl="6" class="tw-mb-[16px]" :span="8">
            <el-card shadow="never" :body-style="{ padding: '6px 20px' }" :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
              <template #header>
                <div class="tw-flex tw-items-center">
                  <el-icon color="var(--el-color-primary)" :size="16" class="tw-mr-1">
                    <Postcard />
                  </el-icon>
                  <el-text type="primary" class="tw-mr-auto tw-w-[calc(100%_-_265px)] tw-text-[14px] tw-leading-[16px]" truncated :title="contact.name">{{ contact.name }}</el-text>
                  <div style="display: flex; align-items: center">
                    <div style="margin-right: 2px" v-for="itemA in localesOption" :key="itemA.value">
                      <div v-if="itemA.value == contact.language" :style="{ background: `url(${itemA.icon}) no-repeat left / auto`, paddingLeft: '30px' }">{{ itemA.label }}</div>
                    </div>
                    <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(资产管理中心_联系人_查看联系人) && ![eventState.CLOSED, eventState.AUTO_CLOSED].includes((detail.eventState || '') as eventState)">
                      <span class="tw-ml-auto tw-h-fit">
                        <el-link type="danger" :underline="false" :disabled="!userInfo.hasPermission(资产管理中心_联系人_查看联系人) || [eventState.CLOSED, eventState.AUTO_CLOSED].includes((detail.eventState as eventState) || ('' as eventState))" @click="viewContactDetail(contact)">{{ $t("glob.Cat") }}</el-link>
                      </span>
                    </el-tooltip>
                    <el-popconfirm :width="200" :disabled="!verifyPermissionIds.includes('612913037728284672') || [eventState.CLOSED, eventState.AUTO_CLOSED].includes((detail.eventState as eventState) || ('' as eventState))" :title="`${$t('generalDetails.confirm')} ${$t('glob.remove')} ${contact.name} ${$t(`generalDetails.${tab.cnName}`)}?`" :confirm-button-text="$t('glob.remove')" confirm-button-type="danger" @confirm="delItem({ ...contact, code: tab.code })">
                      <template #reference>
                        <span>
                          <el-tooltip :content="$t('glob.noPower')" :disabled="verifyPermissionIds.includes('612913037728284672') && ![eventState.CLOSED, eventState.AUTO_CLOSED].includes((detail.eventState || '') as eventState)">
                            <span class="tw-ml-auto tw-h-fit">
                              <el-link type="danger" :underline="false" :disabled="!verifyPermissionIds.includes('612913037728284672') || [eventState.CLOSED, eventState.AUTO_CLOSED].includes((detail.eventState as eventState) || ('' as eventState))" class="tw-ml-[8px]">{{ $t("glob.remove") }}</el-link>
                            </span>
                          </el-tooltip>
                        </span>
                      </template>
                    </el-popconfirm>
                  </div>
                </div>
              </template>
              <template #default>
                <div class="tw-flex tw-h-[24px] tw-items-center" title="固定电话">
                  <el-icon class="tw-mr-2">
                    <Phone />
                  </el-icon>
                  <el-text type="info" truncated class="tw-text-[14px]" :title="contact.landlinePhone">{{ contact.landlinePhone || "--" }}</el-text>
                </div>
                <div class="tw-flex tw-h-[24px] tw-items-center" title="移动电话">
                  <el-icon class="tw-mr-2">
                    <Iphone />
                  </el-icon>
                  <el-text type="info" truncated class="tw-text-[14px]" :title="contact.mobilePhone">{{ contact.mobilePhone || "--" }}</el-text>
                </div>
                <div class="tw-flex tw-h-[24px] tw-items-center" title="邮箱">
                  <el-icon class="tw-mr-2">
                    <Message />
                  </el-icon>
                  <el-text type="info" truncated class="tw-text-[14px]" :title="contact.email">{{ contact.email || "--" }}</el-text>
                </div>
              </template>
              <template #footer> {{ contact.tenantName + `[${contact.tenantAbbreviation}]` }}</template>
            </el-card>
          </el-col>
        </template>
      </el-row>
    </div>
  </el-scrollbar>
  <createContact ref="editorRef" title="联系人" :tabs="tabs" :types="contactsType" :contacts="contacts">
    <template #del="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]">
          <InfoFilled></InfoFilled>
        </el-icon>
        <p class="title">
          {{$t("generalDetails.Confirm to remove contact person")}}
          <span>{{ ((params || {}) as anyObj).name as string }}</span>
          {{$t("generalDetails.Is that okay")}}
        </p>
      </div>
    </template>
  </createContact>
  <contactView ref="contactViewRef"></contactView>
</template>

<script setup lang="ts" name="event-contacts">
import { ref, toRefs, onMounted, nextTick, watch, toValue, inject } from "vue";

import { Plus, PhoneFilled, Iphone, Message, Phone, Postcard } from "@element-plus/icons-vue";

import { ElMessage } from "element-plus";

import type { EventItem } from "@/views/pages/apis/event";

import { eventState } from "@/views/pages/apis/event";

import { getContactTypes as getType, type ContactsTypeItem, type ContactsItem } from "@/views/pages/apis/contacts";

import { eventBatchContact as getData, eventBatchdesensitized, eventAddContact as addData, eventDelContact as delData, getChangeContacts } from "@/views/pages/apis/eventManage";

import createContact from "./createContact.vue";

import contactView from "@/views/pages/alarm_convergence/PropertyManage/contactsManage/contactsEdit.vue";

import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";

import getUserInfo from "@/utils/getUserInfo";

import { 资产管理中心_联系人_查看联系人, 资产管理中心_设备_可读, 资产管理中心_联系人_可读 } from "@/views/pages/permission";
import { localesOption } from "@/api/locale.ts";

const loading = ref(false);

const userInfo = getUserInfo();

const route = useRoute();

const { t } = useI18n();

const verifyPermissionIds: string[] = inject("verifyPermissionIds") as string[];

interface Props {
  height: number;
  data: Partial<EventItem>;
  refresh: () => Promise<void>;
}

const props = withDefaults(defineProps<Props>(), {
  height: 0,
  data: () => ({}) as Partial<EventItem>,
});

const { height, data: detail } = toRefs(props);
const tabs = ref<ContactsTypeItem[]>([]);
const contactsType = ref<{ contactId: string; contactType: string }[]>([]);
const contacts = ref<ContactsItem[]>([]);
const contactsObj = ref<Record<string, ContactsItem[]>>({});

watch(
  () => props.data,
  (data) => {
    // if (userInfo.hasPermission(资产管理中心_联系人_可读)) {
    querysItem(data);
    // }
  },
  { immediate: true }
);

const contactViewRef = ref<InstanceType<typeof contactView>>();
async function viewContactDetail(row: ContactsItem) {
  if (!contactViewRef.value) return false;
  contactViewRef.value.open(row, true);
}

const editorRef = ref<InstanceType<typeof createContact>>();
async function createItem(row: ContactsTypeItem) {
  if (!editorRef.value) return row;
  await editorRef.value.open({ contactType: row.code }, async (form) => {
    const { success, message, data } = await addData({ id: <string>route.params.id, contactType: form.contactType, contactIds: form.contactId });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(t("axios.Operation successful"));
    props.refresh();
  });
}

async function delItem(row: ContactsItem & { code: string }) {
  try {
    loading.value = true;
    await nextTick();
    const { success, message, data } = await delData({ id: <string>route.params.id, contactType: row.code, contactIds: [row.id] });
    if (!success) throw Object.assign(new Error(message), { success, data });
    loading.value = false;
    ElMessage.success(`操作成功`);
  } catch (error) {
    loading.value = false;
    ElMessage.error(error instanceof Error ? error.message : `${error}`);
  } finally {
    props.refresh();
  }
}

async function querysItem(raw: Partial<EventItem>) {
  const id = raw.id;
  if (!id) return;
  try {
    loading.value = true;
    contactsObj.value = {};
    await nextTick();
    const [{ success: contactSuccess, message: contactMessage, data: contactData }, { success: tabsSuccess, message: tabsMessage, data: tabsData }] = await Promise.all([getChangeContacts({ id }), getType({})]);
    if (!contactSuccess) throw Object.assign(new Error(contactMessage), { success: contactSuccess, data: contactData });
    contactsType.value = contactData instanceof Array ? contactData : [];
    if (!tabsSuccess) throw Object.assign(new Error(tabsMessage), { success: tabsSuccess, data: tabsData });
    tabs.value = tabsData instanceof Array ? tabsData : [];
    if (contactsType.value.length) {
      const { success, message, data } = await eventBatchdesensitized({ deviceIds: Array.from(contactsType.value.reduce((p, c) => p.add(c.contactId), new Set<string>()).values()) });
      // const { success, message, data } = await getData({ ids: Array.from(new Set(contactsType.value.map((v) => v.contactId))) });
      if (!success) throw Object.assign(new Error(message), { success, data });
      contacts.value = data instanceof Array ? data : [];

      contactsObj.value = tabs.value.reduce(
        (t, c) =>
          Object.assign(t, {
            [c.code]: (function (list) {
              return data.filter((v) => list.map((i) => i.contactId).includes(v.id));
            })(contactsType.value.filter((v) => v.contactType === c.code)),
          }),
        {}
      );
    }
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : `${error}`);
  } finally {
    loading.value = false;
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/theme/common/var.scss";

.contact-item {
  background: #f7f8fa;

  .contact-name {
    color: $color-black;
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }

  .contact-cn-name {
    font-family: "PingFang SC";
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: map-get($text-color, regular);
  }

  .contact-content {
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    color: $color-black;
  }

  .contact-label {
    color: map-get($text-color, regular);
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
}
</style>
