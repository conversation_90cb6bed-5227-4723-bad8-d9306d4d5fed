<template>
  <!-- 对话框表单 -->
  <component :is="ComponentRender" v-model:visible="data.visible" :handle-cancel="handleCancel">
    <template #header>
      <!-- {{ `${$params.id ? t("glob.edit") : t("glob.add")}${props.title}` }} -->
      {{ `${$params.id ? t("glob.edit") : t("glob.add")}${props.title}` }}
    </template>
    <template #default>
      <FormModel ref="formRef" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @submit="handleFinish">
        <template #default="{}">
          <FormItem v-if="mergedSubclassOptions.length > 1" :span="width > 675 ? 24 : 24" :label="`${props.title}子类型`" tooltip="" prop="ticketSubtype" :rules="[{ required: true, message: '请选择子类型', trigger: 'bulr' }]">
            <el-select v-model="form.ticketSubtype" placeholder="请选择子类型" clearable filterable class="tw-w-full" @change="handleSubtypeChange">
              <el-option v-for="item in mergedSubclassOptions" :key="item.id" :label="item.compositeLabel" :value="item.orderSubclass"> </el-option>
            </el-select>
          </FormItem>
          <FormItem v-if="isAssociationCreate" :span="24" :label="`选择时间`" tooltip="" prop="contactType" :rules="[]">
            <el-date-picker v-model="form.date" type="datetimerange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" />
          </FormItem>
          <FormItem :span="24" :label="`${!isAssociationCreate ? '变更名称' : '摘要'}`" tooltip="" prop="contactType" :rules="[]">
            <el-input v-model="form.digest" :placeholder="`请输入${!isAssociationCreate ? '变更名称' : '摘要'}`" />
          </FormItem>
          <FormItem v-if="!isAssociationCreate" :span="24" :label="`变更描述`" tooltip="" prop="contactType" :rules="[]">
            <el-input v-model="form.desc" :rows="2" type="textarea" placeholder="请输入" />
          </FormItem>
          <FormItem :span="24" :label="`优先级`" tooltip="" prop="contactType" :rules="[]">
            <el-select v-model="form.priority" class="m-2 tw-w-full" placeholder="请选择优先级">
              <el-option v-for="item in Object.keys(priority)" :key="`priority-${item}`" :label="item" :value="item" />
            </el-select>
          </FormItem>
          <FormItem v-if="isAssociationCreate" :span="24" :label="`描述`" tooltip="" prop="contactType" :rules="[]">
            <div class="tw-flex tw-min-h-[500px] tw-w-full tw-flex-col">
              <QuillEditor theme="snow" style="flex: 1" :content="form.desc" @update:content="form.desc = $event" contentType="html" toolbar="full" :enable="true"></QuillEditor>
            </div>
          </FormItem>
        </template>
      </FormModel>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </component>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, toRaw, nextTick, computed, h, createVNode, renderSlot, inject, watch } from "vue";
import { useFileDialog } from "@vueuse/core";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { ElMessage, ElMessageBox, type TableColumnCtx } from "element-plus";
import { UploadFilled, Plus } from "@element-plus/icons-vue";
import { buildTypeHelper } from "@/utils/type";
import { buildValidatorData } from "@/utils/validate";
import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import { useRoute } from "vue-router";

import { changeType, ChangeType as Types } from "@/views/pages/apis/change";

import { priority } from "@/views/pages/apis/event";

import { QuillEditor } from "@vueup/vue-quill";

import { DataItem } from "../helper";
import { findconfiguration } from "@/views/pages/apis/ticketTemplate";
import getUserInfo from "@/utils/getUserInfo";
import { getAllticketTemplates } from "@/views/pages/apis/ticketTemplate";

// import { getAlertStat } from "@/views/pages/apis/event";

// const { open, reset, onChange, files } = useFileDialog({});
// onChange((files) => {
//   form.value.files = Array.from(files || []);
// });
const route = useRoute();
const { t } = useI18n();
const formRef = ref<InstanceType<typeof FormModel>>();
const isAssociationCreate = computed(() => !!route.params.id); // 判读是否为关联创建

const detailData = ref({} as DataItem);
const changetypelist = ref<Record<string, any>[]>([]);
const userInfo = getUserInfo();
const ordertypeArrlist = ref([
  { label: "一般变更", value: "ORDINARY" },
  { label: "标准变更", value: "STANDARD" },
  { label: "紧急变更", value: "URGENCY" },
  { label: "重大变更", value: "IMPORTANT" },
]);

const orderSubclasslist = ref([]);
watch(
  inject("detailData", ref({} as DataItem)),
  (v) => {
    detailData.value = v;
  },
  { immediate: true }
);

interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 120,
});

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Item {
  changeType: keyof typeof Types;
  date: [string, string];
  priority: keyof typeof priority;
  digest: string;
  desc: string;
  startTime: string;
  endTime: string;

  ticketSubtype: string /* 子类型 */;
  ticketTemplateId: string /* 模板id */;
}

// interface DataItem {
//   id: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction?: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  changeType: buildTypeHelper<Item["changeType"]>(Types.ORDINARY),
  date: buildTypeHelper<Item["date"]>(["", ""]),
  priority: buildTypeHelper<Item["priority"]>(priority.P7),
  digest: buildTypeHelper<Item["digest"]>(detailData.value.summary),
  desc: buildTypeHelper<Item["desc"]>(detailData.value.description),
  startTime: buildTypeHelper<Item["startTime"]>(""),
  endTime: buildTypeHelper<Item["endTime"]>(""),
  ticketSubtype: buildTypeHelper<Required<Item>["ticketSubtype"]>(""),
  ticketTemplateId: buildTypeHelper<Required<Item>["ticketTemplateId"]>(""),
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  // handlechangeType();
  if (params) {
    return;
  }
}

//查询变更类型
async function handlechangeType() {
  try {
    const { message, success, data } = await findconfiguration({ ticketType: "change", tenantId: userInfo.currentTenant.id });
    if (!success) throw new Error(message);
    changetypelist.value = data || [];
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}
const filteredChangeType = computed(() => {
  if (changetypelist.value.length === 0) return [];

  return changeType.filter((item) => changetypelist.value.includes(item.value as any));
});

async function getorderSub() {
  const params = {
    pageNumber: 1,
    pageSize: 9999,
    containerId: userInfo?.currentTenant?.containerId,
    queryPermissionId: "733532355297280000",
    verifyPermissionIds: "733532411236712448,733532436834549760,733532469994717184",
  };
  try {
    const { data, message, success, total } = await getAllticketTemplates({ ...params });
    if (!success) throw new Error(message);
    orderSubclasslist.value = data.filter((item) => {
      return item.ticketType === "change";
    });

    if (orderSubclasslist.value.length === 1) {
      form.value.ticketTemplateId = orderSubclasslist.value[0].id;
    }
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

const handleSubtypeChange = (val: string) => {
  console.log(val);
  const selected = orderSubclasslist.value.find((item) => item.orderSubclass === val);
  console.log(selected);

  if (selected) {
    form.value.ticketTemplateId = selected.id;
  }
};
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  const time: { startTime: string; endTime: string } = {
    startTime: "",
    endTime: "",
  };
  try {
    time.startTime = (<Item["date"]>form.date)[0] ? new Date((<Item["date"]>form.date)[0]).getTime().toString() : "";
    time.endTime = (<Item["date"]>form.date)[1] ? new Date((<Item["date"]>form.date)[1]).getTime().toString() : "";
  } catch (error) {
    time.startTime = "";
    time.endTime = "";
  }
  return {
    changeType: form.changeType,
    priority: form.priority,
    digest: form.digest,
    desc: form.desc,
    ticketSubtype: form.ticketSubtype,
    ticketTemplateId: form.ticketTemplateId,
    ...time,
  };
}
const mergedSubclassOptions = computed(() => {
  return (orderSubclasslist.value || []).map((item) => {
    const typeLabel = ordertypeArrlist.value.find((t) => t.value === item.type)?.label || "null";
    return {
      ...item,
      compositeLabel: `${typeLabel}(${item.orderSubclass})`,
    };
  });
});
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
// const validator = (_rule: unknown, value: unknown, callback: (error?: string | Error | undefined) => void): void => {
//   callback(form.value.files instanceof Array ? (form.value.files.length > 0 ? void 0 : new Error("请选择上传文件")) : new Error("请选择上传文件"));
// };
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 窗口方法
 */
interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: TableColumnCtx<DataItem>, value: T[keyof T]) => string | import("vue").VNode; showOverflowTooltip?: boolean }[];
}
const state = reactive<StateData<DataItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
  column: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Record<string, unknown>) => void;
  reject?: (value: Error) => void;
  callback?: (form: Record<string, unknown>) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Record<string, unknown>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(function (formResult, [key, util]) {
    if (!util) return formResult;
    else if (util.test(formResult[key])) return formResult;
    else
      return Object.assign(formResult, {
        [key]: util.transfer(formResult[key], util.value as never),
      });
  }, form as Required<Item>);
}

async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => (formRef.value ? formRef.value.validate(resolve) : resolve(false)));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();

defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, callback?: (form: Record<string, unknown>) => Promise<void>) {
    await getorderSub();
    if (form.value.ticketTemplateId) {
      params.ticketTemplateId = form.value.ticketTemplateId;
    }
    if (data.visible) {
      return await new Promise((resolve) => {
        ElMessage.warning("先关闭其他弹窗再重试！");
        resolve(params);
      });
    } else {
      $params.value = cloneDeep(params);
      data.visible = true;
      data.loading = true;
      data.submitLoading = true;
      data.callback = callback;
      try {
        return await new Promise((resolve, reject) => {
          data.resolve = resolve;
          data.reject = reject;
          nextTick(async () => {
            await nextTick();
            try {
              await runningInit($params.value);
            } catch (error) {
              if (error instanceof Error) ElMessage.error(error.message);
              handleCancel();
            }
            handleReset();
            data.loading = false;
            data.submitLoading = false;
          });
        });
      } catch (error) {
        return error;
      }
    }
  },
  async confirm<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.confirm(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
});
</script>

<style scoped lang="scss">
:deep(.ql-blank) {
  height: 400px;
}
.edit_sla_config {
  :deep() {
    .el-input-number {
      width: 50px !important;

      .elstyle-input {
        width: 50px;

        input {
          padding: 0;
        }
      }
    }

    .el-input-number__increase {
      display: none;
    }

    .el-input-number__decrease {
      display: none;
    }

    .el-card {
      margin-top: 20px;
    }

    .el-form-item__content .el-form-item-content {
      display: flex;
      flex-direction: column;
    }

    .el-table .cell {
      padding: 0 !important;
    }

    .el-table .el-table__cell {
      padding: 0 !important;
      height: 50px;
    }
  }
}
.priority-icon {
  width: 10px;
  height: 10px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 10px;
}
.state {
  padding: 2px 10px;
  box-sizing: border-box;
  border-radius: 20px;
}
.modules-item {
  .modules-title {
    color: rgba(32, 128, 255, 1);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}
.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}

// :deep(.search-select) {
//   :deep(.el-select-dropdown__item) {
//     height: auto;
//   }
// }
</style>
