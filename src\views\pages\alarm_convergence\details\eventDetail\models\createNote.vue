<template>
  <el-dialog v-model="drawer" :title="`${isEdit ? $t('generalDetails.Revise notes') : $t('generalDetails.Add a note')}`" :before-close="handleClose">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="0" v-loading="fileLoading" :element-loading-text="$t('generalDetails.Loading')">
      <el-form-item prop="content" v-if="drawer">
        <div class="tw-flex tw-min-h-[350px] tw-w-full tw-flex-col">
          <QuillEditor theme="snow" class="tw-h-[350px] tw-w-full" :content="form.content" @update:content="form.content = $event" contentType="html" toolbar="full" :enable="true" :modules="modules"></QuillEditor>
        </div>
      </el-form-item>

      <el-form-item :label="$t('generalDetails.Upload Attachments')" label-width="auto" prop="fileList">
        <div class="tw-w-full">
          <el-button type="primary" @click="() => openFileDialog && openFileDialog()">{{ $t("generalDetails.Click to upload") }}</el-button>
          <div>
            <el-checkbox v-model="form.privateAble">{{ $t("generalDetails.Private Customer") }}[{{ tenantAbbreviation }}]</el-checkbox>
          </div>
          <transition-group tag="ul" :class="['el-upload-list', 'el-upload-list--text']" name="el-upload-list">
            <li v-for="(file, fileIdx) in form.fileList" :key="`${fileIdx}.${file.name}`" :class="['el-upload-list__item']" tabindex="0" @keydown.delete.stop>
              <div :class="['el-upload-list__item-info']">
                <a :class="['el-upload-list__item-name']" @click.prevent.stop>
                  <el-icon :class="['el-icon--document']"><Document /></el-icon>
                  <span :class="['el-upload-list__item-file-name']" :title="file.name"> {{ file.name }} </span>
                </a>
              </div>

              <label :class="['el-upload-list__item-status-label']">
                <el-icon :class="['el-icon--upload-success', 'el-icon--circle-check']"><CircleCheck /></el-icon>
              </label>

              <!-- 删除按钮 -->
              <el-icon :class="['el-icon--close']" @click.stop="handleRemove(file)"><Close /></el-icon>
            </li>
          </transition-group>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ $t("generalDetails.Cancel") }}</el-button>
        <el-button type="primary" @click="handleSubmit">{{ loading ? $t("generalDetails.Submitting") + "......" : isEdit ? $t("generalDetails.Modified") : $t("generalDetails.Add") }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from "vue";
import { useRoute } from "vue-router";
import { useFileDialog } from "@vueuse/core";
import { ElMessage, FormInstance } from "element-plus";
import { Document, CircleCheck, Close } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";
import { QuillEditor } from "@vueup/vue-quill";

import getUserInfo from "@/utils/getUserInfo";

import BlotFormatter from "quill-blot-formatter";

import { addOrderNode, editOrderNode, batchOrderNode } from "@/views/pages/apis/event";

import { 智能事件中心_服务请求工单_编辑小记, 智能事件中心_事件工单_编辑小记, 智能事件中心_DICT事件管理_编辑小记, 智能事件中心_DICT服务请求_编辑小记, 智能事件中心_变更工单_编辑小记, 智能事件中心_问题工单_编辑小记, 智能事件中心_发布管理_编辑小记 } from "@/views/pages/permission";

interface Props {
  orderType: string;
  batchIds: string[];
}

const props = withDefaults(defineProps<Props>(), {
  orderType: "",
  batchIds: () => [] as Props["batchIds"],
});

const modules = ref({
  name: "blotFormatter",
  module: BlotFormatter,
});

enum EditNodePermissionId {
  DICT_SERVICE_REQUEST = "690043142585450496", //智能事件中心_DICT服务请求_编辑小记 and 智能事件中心_小记_新增,
  DICT_EVENT_ORDER = "690042247927824384", //智能事件中心_DICT事件管理_编辑小记 and 智能事件中心_小记_新增,
  CHANGE = "612915766462775296", //智能事件中心_变更工单_编辑小记 and 智能事件中心_小记_新增,
  EVENT_ORDER = "612912935169163264", //智能事件中心_事件工单_编辑小记 and 智能事件中心_小记_新增,
  SERVICE_REQUEST = "612914465268039680", //智能事件中心_服务请求工单_编辑小记 and 智能事件中心_小记_新增,
  PUBLISH = "612916614056116224", //智能事件中心_发布管理_编辑小记 and 智能事件中心_小记_新增,
  QUESTION = "612916128183746560", //智能事件中心_问题工单_编辑小记 and 智能事件中心_小记_新增,
}

const route = useRoute();

const userInfo = getUserInfo();

const emits = defineEmits(["refresh"]);

const titleConfig = ref([
  { Choice: ".ql-insertMetric", title: "跳转配置" },
  { Choice: ".ql-bold", title: "加粗" },
  { Choice: ".ql-italic", title: "斜体" },
  { Choice: ".ql-underline", title: "下划线" },
  { Choice: ".ql-header", title: "段落格式" },
  { Choice: ".ql-strike", title: "删除线" },
  { Choice: ".ql-blockquote", title: "块引用" },
  { Choice: ".ql-code", title: "插入代码" },
  { Choice: ".ql-code-block", title: "插入代码段" },
  { Choice: ".ql-font", title: "字体" },
  { Choice: ".ql-size", title: "字体大小" },
  { Choice: '.ql-list[value="ordered"]', title: "编号列表" },
  { Choice: '.ql-list[value="bullet"]', title: "项目列表" },
  { Choice: ".ql-direction", title: "文本方向" },
  { Choice: '.ql-header[value="1"]', title: "h1" },
  { Choice: '.ql-header[value="2"]', title: "h2" },
  { Choice: ".ql-align", title: "对齐方式" },
  { Choice: ".ql-color", title: "字体颜色" },
  { Choice: ".ql-background", title: "背景颜色" },
  { Choice: ".ql-image", title: "图像" },
  { Choice: ".ql-video", title: "视频" },
  { Choice: ".ql-link", title: "添加链接" },
  { Choice: ".ql-formula", title: "插入公式" },
  { Choice: ".ql-clean", title: "清除字体格式" },
  { Choice: '.ql-script[value="sub"]', title: "下标" },
  { Choice: '.ql-script[value="super"]', title: "上标" },
  { Choice: '.ql-indent[value="-1"]', title: "向左缩进" },
  { Choice: '.ql-indent[value="+1"]', title: "向右缩进" },
  { Choice: ".ql-header .ql-picker-label", title: "标题大小" },
  { Choice: '.ql-header .ql-picker-item[data-value="1"]', title: "标题一" },
  { Choice: '.ql-header .ql-picker-item[data-value="2"]', title: "标题二" },
  { Choice: '.ql-header .ql-picker-item[data-value="3"]', title: "标题三" },
  { Choice: '.ql-header .ql-picker-item[data-value="4"]', title: "标题四" },
  { Choice: '.ql-header .ql-picker-item[data-value="5"]', title: "标题五" },
  { Choice: '.ql-header .ql-picker-item[data-value="6"]', title: "标题六" },
  { Choice: ".ql-header .ql-picker-item:last-child", title: "标准" },
  { Choice: '.ql-size .ql-picker-item[data-value="small"]', title: "小号" },
  { Choice: '.ql-size .ql-picker-item[data-value="large"]', title: "大号" },
  { Choice: '.ql-size .ql-picker-item[data-value="huge"]', title: "超大号" },
  { Choice: ".ql-size .ql-picker-item:nth-child(2)", title: "标准" },
  { Choice: ".ql-align .ql-picker-item:first-child", title: "居左对齐" },
  { Choice: '.ql-align .ql-picker-item[data-value="center"]', title: "居中对齐" },
  { Choice: '.ql-align .ql-picker-item[data-value="right"]', title: "居右对齐" },
  { Choice: '.ql-align .ql-picker-item[data-value="justify"]', title: "两端对齐" },
]);

const { t } = useI18n();

const drawer = ref<boolean>(false);

const nodeDetail = ref<Record<string, any>>({});

const isEdit = computed(() => !!nodeDetail.value.noteId);

const isBatch = computed(() => props.batchIds instanceof Array && props.batchIds.length);

const fileLoading = ref<boolean>(false);

const formRef = ref<FormInstance>();

const form = ref<Record<string, any>>({
  content: "",
  privateAble: false,
  fileList: [],
  delFileId: [],
});

const rules = ref({
  content: [
    {
      validator: (rule, value, callback) => {
        if (form.value.content) return callback();
        else callback(new Error(t("generalDetails.Journal cannot be empty")));
      },
      trigger: ["blur", "change"],
    },
  ],
});

const tenantAbbreviation = computed(() => {
  {
    for (let i = 0; i < userInfo.tenants.length; i++) {
      if (userInfo.tenants[i].id === userInfo.tenantId) return userInfo.tenants[i].abbreviation;
    }
    return "";
  }
});

const loading = ref<boolean>(false);

function handleSubmit() {
  formRef.value &&
    formRef.value.validate(async (valid) => {
      try {
        if (!valid) return;

        loading.value = true;

        const formData = new FormData();

        formData.append("nodeContent", form.value.content);
        formData.append("privateAble", form.value.privateAble as any);
        formData.append("privateCustomerId", userInfo.tenantId);
        formData.append("tenantId", (userInfo.currentTenant || {}).id as string);

        if (!isEdit.value && isBatch.value) formData.append("orderIdsJson", JSON.stringify(props.batchIds));
        else {
          formData.append("orderId", route.params.id as string);
          formData.append("orderIdsJson", JSON.stringify([route.params.id]));
        }

        formData.append("orderType", props.orderType);
        formData.append("permissionId", EditNodePermissionId[props.orderType]);
        for (let i = 0; i < form.value.fileList.length; i++) {
          const file = form.value.fileList[i];
          if (!file.id) formData.append("files", file as any);
        }

        if (isEdit.value) {
          formData.append("noteId", nodeDetail.value.noteId);
          formData.append("delFileIdsJson", JSON.stringify(form.value.delFileId));
        }

        const { message, success } = await { add: addOrderNode, edit: editOrderNode, batch: batchOrderNode }[!isEdit.value && isBatch.value ? "batch" : isEdit.value ? "edit" : "add"](formData as any);
        if (!success) throw new Error(message);
        ElMessage.success(t("axios.Operation successful"));
        loading.value = false;
        handleClose();
        emits("refresh");
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
        loading.value = false;
      }
    });
}

function handleClose(done?) {
  formRef.value && formRef.value.resetFields();

  form.value.privateAble = false;
  form.value.delFileId = [];

  nextTick(() => (done instanceof Function ? done() : (drawer.value = false)));
}

async function open(row) {
  drawer.value = true;

  nodeDetail.value = JSON.parse(JSON.stringify(row || {}));

  await nextTick();

  for (let item of titleConfig.value) {
    const tip = document.querySelector(".ql-toolbar " + item.Choice) as HTMLElement;
    if (!tip) continue;
    tip.setAttribute("title", item.title);
    if (item.Choice == ".ql-image" || item.Choice == ".ql-video" || item.Choice == ".ql-link") {
      tip.style.display = "none"; // 或者使用 visibility: "hidden"
    }
  }

  if (isEdit.value) {
    form.value.content = row.noteContent;
    form.value.privateAble = row.privateAble;
    form.value.fileList = row.attachmentList.map((v) => ({ name: v.attachmentName, id: v.attachmentId }));
  }
}

/**
 * @desc 删除文件
 * @param v
 */
async function handleRemove(v: any) {
  /* 保存已上传文件ID,编辑时触发 */
  if (v.id) form.value.delFileId.push(v.id);

  await nextTick();

  form.value.fileList.splice(form.value.fileList.indexOf(v as never), 1);
}

/**
 * 上传文件方法
 */
const openFileDialog = ref<Function | null>(null);

onMounted(() => {
  const { open, onChange } = useFileDialog({ multiple: true, reset: true });
  openFileDialog.value = open;
  onChange(async (row) => (row instanceof FileList && row.length ? form.value.fileList.push(...row) : void 0));
});

defineExpose({ open });
</script>
