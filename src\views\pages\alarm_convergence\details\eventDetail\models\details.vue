<template>
  <el-form :model="form" label-position="top">
    <!-- <el-form-item v-if="kbDesc">
      <div class="el-input el-input__wrapper tw-flex tw-min-h-[32px] tw-w-full tw-flex-col tw-items-start tw-justify-start">
        <div>{{ kbDesc }}</div>
      </div>
    </el-form-item> -->
    <el-form-item>
      <template #label>
        <div class="info-desc">
          <span> {{ $t("generalDetails.Description") }}</span>
          <el-button style="z-index: 1" type="primary" :disabled="(!userInfo.hasPermission('612912833616674816') && !userInfo.hasPermission('777393423094120448')) || [eventState.NEW, eventState.CLOSED, eventState.AUTO_CLOSED].includes((props.data.eventState as eventState) || ('' as eventState))" @click="handleDescEdit('description')">{{ isEdit ? $t("generalDetails.Save") : $t("generalDetails.Edit") }}</el-button>
        </div>
      </template>
      <div class="tw-flex tw-min-h-[120px] tw-w-full tw-flex-col" v-if="isEdit" @keyup.enter.stop>
        <!-- <QuillEditor theme="snow" style="flex: 1" :content="isEdit ? form.description : props.data.description" @update:content="form.description = $event" contentType="html" toolbar="full" :enable="isEdit" :read-only="!isEdit"></QuillEditor> -->
        <el-input v-model="form.description" :rows="6" type="textarea" placeholder="请输入描述" />
      </div>
      <div class="el-input el-input__wrapper tw-flex tw-min-h-[120px] tw-w-full tw-flex-col tw-items-start tw-justify-start" v-else @keyup.enter.stop>
        <div>{{ props.data.description }}</div>
      </div>
    </el-form-item>
    <el-form-item :label="$t('generalDetails.External ID')">
      <template #label>
        <div class="info-desc">
          <span> {{ $t("generalDetails.External ID") }}</span>
          <el-button style="z-index: 1" type="primary" :disabled="(!userInfo.hasPermission('612912833616674816') && !userInfo.hasPermission('777393423094120448')) || [eventState.NEW, eventState.CLOSED, eventState.AUTO_CLOSED].includes((props.data.eventState as eventState) || ('' as eventState))" @click="handleDescEdit('kbServiceCode')">{{ iskbServiceCodeEdit ? $t("generalDetails.Save") : $t("generalDetails.Edit") }}</el-button>
        </div>
      </template>
      <el-input :model-value="iskbServiceCodeEdit ? form.kbServiceCode : props.data.kbServiceCode" @update:model-value="form.kbServiceCode = $event" :disabled="!iskbServiceCodeEdit"></el-input>
    </el-form-item>
    <el-form-item>
      <el-row class="el-input el-input__wrapper">
        <el-col :span="12" class="tw-h-[calc(var(--el-component-size)*3)] tw-text-left">
          <p>{{ $t("generalDetails.Modified") }}</p>
          <p class="tw-h-[var(--el-component-size)]">
            <el-icon :size="16" class="tw-mr-[6px] tw-align-middle"><UserFilled /></el-icon>{{ updated.name || "--" }}
          </p>
          <p>{{ updated.updateTime ? moment(`${updated.updateTime}`, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</p>
        </el-col>
        <el-col :span="12" class="tw-h-[calc(var(--el-component-size)*3)] tw-text-right">
          <p>{{ $t("generalDetails.Created") }}</p>
          <p class="tw-h-[var(--el-component-size)]">
            <el-icon :size="16" class="tw-mr-[6px] tw-align-middle"><UserFilled v-if="props.data.manualCreate" /><Icon v-else name="local-DeviceWifi-fill" /></el-icon>{{ collector.name || "--" }}
          </p>
          <p>{{ collector.collectTime ? moment(`${collector.collectTime}`, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</p>
        </el-col>
      </el-row>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, watch, computed } from "vue";
import { useRoute, useRouter } from "vue-router";

import { ElMessage } from "element-plus";

import { UserFilled } from "@element-plus/icons-vue";
import { setEventDataByDescription, eventState } from "@/views/pages/apis/event";

import { QuillEditor } from "@vueup/vue-quill";
import moment from "moment";
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);

defineOptions({ name: "ModelDetails" });

const props = withDefaults(defineProps<{ data: Partial<import("../helper").DataItem> & Record<string, any>; height: number; refresh: () => Promise<void> }>(), { data: () => ({}) });
const emits = defineEmits(["changeDesc"]);

const route = useRoute();
const router = useRouter();

const form = ref({ description: "", kbServiceCode: "" });
const isEdit = ref(false);
const iskbServiceCodeEdit = ref(false);

const updated = reactive({ name: "", updateTime: 0 });
const collector = reactive({ name: "", collectTime: 0 });
import getUserInfo from "@/utils/getUserInfo";
const userInfo = getUserInfo();

// const kbDesc = computed(() => `${props.data.projectName ? "项目名称：" + props.data.projectName + ";" : ""}${props.data.projectCode ? "项目编号：" + props.data.projectCode + ";" : ""}${props.data.kbServiceCode ? "客保工单号：" + props.data.kbServiceCode + ";" : ""}${props.data.serviceCode ? "服务工单号：" + props.data.serviceCode + ";" : ""}${props.data.uniformServiceCode ? "统一服务编号：" + props.data.uniformServiceCode + ";" : ""}`);
function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == userInfo.zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
watch(
  () => props.data,
  async (data) => {
    await nextTick();
    if (data.collector || data.createdBy) collector.name = data.manualCreate ? JSON.parse(data.createdBy as string)?.username : data.collector;
    collector.collectTime = Math.max(Number(data.createTime) || 0, 0) + timeZoneSwitching();
    try {
      updated.name = JSON.parse(data.updatedBy || "{}").username || "";
    } catch (error) {
      updated.name = "";
    }
    updated.updateTime = Math.max(Number(data.updateTime) || 0, 0) + timeZoneSwitching();
  },
  { immediate: true }
);

async function handleDescEdit(type: "description" | "kbServiceCode") {
  if (type === "description" ? isEdit.value : iskbServiceCodeEdit.value) {
    emits("changeDesc", form.value.description);
    if (type === "description") isEdit.value = false;
    else iskbServiceCodeEdit.value = false;
    try {
      const { success, message } = await setEventDataByDescription({ id: props.data.id as string, desc: form.value.description, kbServiceCode: form.value.kbServiceCode });
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      props.refresh();
    }
  } else {
    form.value.description = props.data.description || "";
    form.value.kbServiceCode = props.data.kbServiceCode || "";
    if (type === "description") isEdit.value = true;
    else iskbServiceCodeEdit.value = true;
  }
}
// async function handleExternal() {
//   if (isExternal.value) {
//     emits("changeDesc", form.value.description);
//     isExternal.value = false;
//     try {
//       const { success, message } = await setEventDataByDescription({ id: props.data.id as string, desc: form.value.description, externalId: form.value.externalId });
//       if (!success) throw new Error(message);
//       ElMessage.success("操作成功");
//     } catch (error) {
//       if (error instanceof Error) ElMessage.error(error.message);
//     } finally {
//       props.refresh();
//     }
//   } else {
//     form.value.description = props.data.description || "";
//     form.value.externalId = props.data.externalId || "";
//     isExternal.value = true;
//   }
// }
</script>

<style lang="scss" scoped>
.info-desc {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
