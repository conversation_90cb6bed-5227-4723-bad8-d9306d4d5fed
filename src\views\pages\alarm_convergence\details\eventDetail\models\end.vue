<template>
  <EditorDialog :title="dialogTitle" v-model:visible="dialogFormVisible" :before-close="beforeClose" class="event-end">
    <template #header>
      <!-- {{ `${$params.id ? t("glob.edit") : t("glob.add")}${props.title}` }} -->
      {{ dialogTitle }}
    </template>
    <el-scrollbar :height="height">
      <div class="event-end">
        <div class="tw-mb-[8px] tw-flex tw-items-center tw-justify-between">
          <h3>{{ $t("generalDetails.Close code") }}</h3>
          <el-input
            class="tw-w-[300px]"
            :model-value="searchEndCode"
            :onUpdate:modelValue="
              (v) => {
                searchEndCode = `${v}`;
                $refs.treeRef && $refs.treeRef.filter(v);
              }
            "
             :placeholder="$t('generalDetails.mohu')"
          ></el-input>
        </div>
        <el-form ref="form" :model="form" label-width="120px" :rules="rules">
          <el-form-item label="" label-width="0" prop="currentCodeConfig">
            <el-row class="complete-code-tree">
              <el-col :span="isTreeData ? 8 : 24" style="border-right: 2px solid #eee">
                <el-scrollbar class="scrollConfig">
                  <el-tree ref="treeRef" :data="treeData" :props="defaultProps" :default-expand-all="true" :filter-node-method="filterNode" @node-click="handleNodeClick" node-key="id" highlight-current></el-tree>
                </el-scrollbar>
              </el-col>
              <el-col :span="16" v-if="isTreeData">
                <el-scrollbar class="scrollConfig">
                  <el-table ref="singleTable" :show-header="false" :data="tableData" highlight-current-row @current-change="handleCurrentChange" style="width: 100%">
                    <el-table-column property="codeName">
                      <template #default="{ row }">
                        <p>{{ row.codeName }}</p>
                        <p>{{ row.codeDesc }}</p>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-scrollbar>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="" label-width="0" prop="finishContent">
            <span>{{$t("generalDetails.Close Content")}}</span>
            <el-input :placeholder="$t('generalDetails.be closed')" v-model="form.finishContent"></el-input>
          </el-form-item>
          <el-form-item v-if="dialogType=='Close' || dialogType=='Finish'"  label="" label-width="0" prop="content">
            <!-- 变更已解决 -->
            <div class="tw-flex tw-h-[300px] tw-w-full tw-flex-col">
              <span>{{ $t("generalDetails.Notes") }}</span>
              <QuillEditor theme="snow" class="tw-h-[300px] tw-w-full" :content="notesForm.content" @update:content="notesForm.content = $event" contentType="html" toolbar="full" :enable="true"></QuillEditor>
            </div>
          </el-form-item>
          <el-form-item v-if="dialogType=='Close' || dialogType=='Finish'"  label="" label-width="0" prop="privateAble">
            <div class="tw-flex tw-h-[300px] tw-w-full tw-flex-col">
              <el-checkbox v-model="notesForm.privateAble">{{ $t("generalDetails.Private Customer") }}[{{ tenantAbbreviation }}]</el-checkbox>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-scrollbar>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="beforeClose">{{ $t("generalDetails.Cancel") }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t("generalDetails.confirm") }}</el-button>
      </div>
    </template>
  </EditorDialog>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { getCodeConfigList, getCodelistByOrderList } from "@/views/pages/apis/completeCodeConfig";
import { eventFinish, eventClose } from "@/views/pages/apis/eventManage";
import { QuillEditor } from "@vueup/vue-quill";
import EditorDialog from "@/components/editor/EditorDialog.vue";
import getUserInfo from "@/utils/getUserInfo";
export default {
  components: { QuillEditor, EditorDialog },
  props: {
    refresh: Function,
    height: Number,
    ticketTemplateId: String,
  },
  emits: ["end"],
  data() {
    return {
      dialogFormVisible: false,
      form: {},
      notesForm: {
        privateAble: false,
        content: ""
      },
      objectEnum: [],
      treeData: [],
      defaultProps: {
        children: "children",
        label: "codeName",
        value: "id",
      },
      tableData: [],
      currentRow: {},
      currentCodeConfig: {},
      dialogType: "Finish",
      searchEndCode: "",
      userInfo: getUserInfo(),
    };
  },
  computed: {
    isTreeData: function () {
      return this.treeData instanceof Array && this.treeData.length;
    },
    dialogTitle() {
      if (this.dialogType !== 'Finish' || this.dialogType !== 'Close') {
        this.notesForm={
          privateAble: false,
          content: ""
        }
      }
      return this.dialogType === "ConfirmAndFinish" ? "确认告警并完成" : this.dialogType === "Finish" ? "已解决" : "关闭";
    },
    tenantAbbreviation() {
      for (let i = 0; i < this.userInfo.tenants.length; i++) {
        if (this.userInfo.tenants[i].id === this.userInfo.tenantId) return this.userInfo.tenants[i].abbreviation;
      }
      return "";
    },
    rules() {
      return {
        // currentCodeConfig: [
        //   {
        //     validator: (rule, value, callback) => {
        //       if ((this.currentCodeConfig || {}).codeName) return callback();
        //       callback(new Error("请选择完成代码"));
        //     },
        //     trigger: ["change", "blur"],
        //   },
        // ],
        // finishContent: [
        //   {
        //     validator: (rule, value, callback) => {
        //       if (this.form.finishContent) return callback();
        //       callback(new Error("请输入内容"));
        //     },
        //   },
        // ],
      };
    },
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.codeName.includes(value);
    },
    submitForm() {
      this.$refs["form"].validate(async (valid) => {
        // 先检查两个字段是否均为空
        if (!this.currentCodeConfig.codeName && !this.form.finishContent) {
          this.$message.error("请至少填写一个字段（关闭代码或关闭内容）");
          return false;
        }
        if (!valid) return false;
        if (this.dialogType === "ConfirmAndFinish") {
          const params = {
            eventId: this.currentRow.id,
            completeInfo: {
              finishCodeName: this.currentCodeConfig.codeName,
              finishCodeDesc: this.currentCodeConfig.codeDesc,
              finishContent: this.form.finishContent,
            },
            closeAlert: this.dialogType === "ConfirmAndFinish",
          };
          try {
            const { success, message } = await eventFinish(params);
            if (!success) throw new Error(message);
            this.$message.success("操作成功");
            this.$emit("end", {
              params: {
                completeInfo: {
                  finishCodeName: this.currentCodeConfig.codeName,
                  finishCodeDesc: this.currentCodeConfig.codeDesc,
                  finishContent: this.form.finishContent,
                },
                // closeAlert: this.dialogType === "ConfirmAndFinish",
              },
              type: this.dialogType === "ConfirmAndFinish" ? "Finish" : this.dialogType,
              notesForm: this.notesForm,
            });
            this.beforeClose();
            this.refresh instanceof Function && this.refresh();
          } catch (error) {
            error instanceof Error && this.$message.error(error.message);
          }
        } else {
          this.$emit("end", {
            params: {
              completeInfo: {
                finishCodeName: this.currentCodeConfig.codeName,
                finishCodeDesc: this.currentCodeConfig.codeDesc,
                finishContent: this.form.finishContent,
              },
              // closeAlert: this.dialogType === "ConfirmAndFinish",
            },
            type: this.dialogType === "ConfirmAndFinish" ? "Finish" : this.dialogType,
            notesForm: this.notesForm,
          });
          this.beforeClose();
        }
      });
    },
    getEditorValue(v) {
      this.form.finishContent = v;
    },
    handleNodeClick(data) {
      this.tableData = [];
      // 当节点有子节点时不进行赋值
      if (data.children?.length) return;
      
      // 仅处理无子节点的叶子节点
      if (!data.parentId) {
        // 处理根节点特殊情况
        this.tableData = this.treeData.filter(v => !v.children?.length);
      } else {
        this.tableData.push(data);
        this.$nextTick(() => 
          this.$refs.singleTable.setCurrentRow(data)
        );
      }
      this.$refs.form.clearValidate();
    },
    handleCurrentChange(v) {
      if (!v) return;
      this.currentCodeConfig = v;
      this.$refs.treeRef.setCurrentKey(v.id);
    },
    open(row, tp) {
      this.dialogType = tp;
      this.dialogFormVisible = true;
      this.currentRow = row;
      // // console.log(row, 5555, tp);
      if (tp === "Close" || tp === "Finish") {
        this.currentCodeConfig = {
          codeName: (row.completeInfo || {}).finishCodeName || "",
          codeDesc: (row.completeInfo || {}).finishCodeDesc || "",
        };
      }
      this.getCodeConfig();
      // this.notesForm.privateAble = false;
      // this.notesForm.content = "";
    },
    beforeClose(done) {
      this.$refs.form.clearValidate();
      this.$refs.form.resetFields();
      if (this.$refs.singleTable) this.$refs.singleTable.setCurrentRow();
      this.form.finishContent = "";
      if (done instanceof Function) done();
      else this.dialogFormVisible = false;
    },
    getCodeConfig() {
      getCodelistByOrderList({
        codeType: "EVENT",
        containerId: getUserInfo().currentTenant.containerId,
        queryPermissionId: "517242654942035968",
        ticketTemplateId: this.ticketTemplateId,
      }).then(({ success, data }) => {
        if (success) {
          this.treeData = this.setTableData(data, true);
          // this.tableData = this.treeData.filter((v) => !v.children || !v.children.length);
          if (this.dialogType === "Close" || this.dialogType === "Finish") {
            this.$nextTick(() => {
              this.form.finishContent = (this.currentRow.completeInfo || {}).finishContent || "";
              let setCurrentRow = (list) => {
                // // console.log(list);
                if (!list || !list.length) return false;
                for (let i = 0; i <= list.length; i++) {
                  if (!list[i]) break;
                  if (list[i].codeName === this.currentCodeConfig.codeName) {
                    this.handleNodeClick(list[i]);
                    if (this.$refs.singleTable) this.$refs.singleTable.setCurrentRow(list[i]);
                    break;
                  } else setCurrentRow(list[i].children);
                }
              };
              setCurrentRow(this.treeData);
              setCurrentRow = null;
            });
          }
        }
      });
    },
    setTableData(data, isFilterateDisable = false) {
      this.allConfig = JSON.parse(JSON.stringify(data));
      let _formatter = (list) => {
        for (let i = 0; i < list.length; i++) {
          list[i].children = [];
          let _filter = this.allConfig.filter((v) => {
            return isFilterateDisable ? v.status && list[i].id === v.parentId : list[i].id === v.parentId;
          });
          if (_filter && _filter?.length) {
            list[i].children = !isFilterateDisable ? _filter : _filter.filter((v) => v.status);
            _formatter(list[i].children, list[i].id);
          }
        }
      };
      let result = data.filter((v) => (isFilterateDisable ? v.status && !v.parentId : !v.parentId));
      _formatter(result);
      _formatter = null;
      return result;
    },
  },
};
</script>

<style lang="scss" scoped>
// @import "@/assets/style/theme/common/var";
@import "@/styles/theme/common/var.scss";

.event-end {
  :deep(.el-form-item__content) {
    width: 100px;
  }

  :deep(.el-table__body tr.current-row > td.el-table__cell) {
    background-color: $color-primary !important;
    color: $color-white;
  }

  :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    background-color: $color-primary !important; /* 背景颜色 */
    color: $color-white !important; /* 文字颜色 */
  }

  :deep(.elstyle-tree-node__conten) {
    height: auto !important;
  }
  .scrollConfig {
    height: 250px;
  }

  :deep(.elstyle-dialog) {
    margin-top: 8vh !important;
  }

  .complete-code-tree {
    border: 1px solid map-get($border-color, light);
    width: 100%;
  }
}
</style>
