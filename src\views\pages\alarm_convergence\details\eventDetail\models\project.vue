<template>
  <pageTemplate :show-paging="false" :height="height">
    <template #right>
      <el-button type="primary" @click="handleDistributionProject" :loading="butLoading">{{ t("glob.Allocate the", { value: t("project.Project") }) }}</el-button>
    </template>

    <template #default>
      <el-table v-loading="loading" :data="tableData" :height="height - 60" :style="{ width: `100%`, margin: '0 auto' }">
        <TableColumn type="default" :label="t('project.Project Name')" prop="projectName"></TableColumn>
        <TableColumn type="default" :label="t('project.Unified Service Code')" prop="uniformServiceCode"></TableColumn>
        <TableColumn type="default" :label="t('project.Project Code')" prop="projectCode"></TableColumn>
        <TableColumn type="default" :label="t('project.Level')" prop="projectLevel"></TableColumn>
        <TableColumn type="default" :label="t('project.Project Validity Period')" prop="range"></TableColumn>
        <TableColumn type="default" :label="t('glob.operate')" width="80" v-if="![OrderType.DICT_EVENT_ORDER, OrderType.DICT_SERVICE_REQUEST].includes(props.data.orderType as OrderType)">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleRemoveProject(row)">{{ t("glob.remove") }}</el-button>
          </template>
        </TableColumn>
      </el-table>
    </template>
  </pageTemplate>
</template>

<script setup lang="ts">
import { computed, ref, toRefs, h, defineComponent, onMounted, watch } from "vue";
import { useRoute } from "vue-router";

import { EventItem, type priority } from "@/views/pages/apis/event";

import TableColumn from "@/components/tableColumn/TableColumn.vue";
import pageTemplate from "@/components/pageTemplate.vue";
import { ElMessageBox, ElMessage, ElFormItem, ElForm, FormInstance, ElSelect, ElOption, Action, MessageBoxState } from "element-plus";
// import { getAllProjectPlans } from "@/views/pages/apis/projectPlan";
import { getProjectsByEventId, replaceProjectByEventId, ProjectItem, getDictOrderProject, removeProjectByEventId } from "@/views/pages/apis/projectManage";
import moment from "moment";
import getUserInfo from "@/utils/getUserInfo";

import { 配置管理中心_项目管理_可读 } from "@/views/pages/permission";

import { OrderType } from "@/views/pages/apis/association";

import { useI18n } from "vue-i18n";

const userInfo = getUserInfo();
const route = useRoute();

const emits = defineEmits(["refresh"]);

const { t } = useI18n();

interface Props {
  height: number;
  data: any;
  refresh: () => Promise<void>;
}

const props = withDefaults(defineProps<Props>(), {
  height: 0,
  data: () => ({}),
});

const { height, data: detail } = toRefs(props);

const loading = ref<boolean>(false);

const butLoading = ref<boolean>(false);

const project = ref<Record<string, any>>({});

const tableData = computed(() => (userInfo.hasPermission(配置管理中心_项目管理_可读) && Object.keys(project.value).length ? [{ projectCode: project.value.projectCode, projectId: project.value.id, projectLevel: project.value.projectLevel, projectName: project.value.projectName, uniformServiceCode: project.value.uniformServiceCode, range: project.value.range && project.value.range.start && project.value.range.end ? `${moment(Number(project.value.range.start)).format("YYYY-MM-DD")} 至 ${moment(Number(project.value.range.end)).format("YYYY-MM-DD")}` : `` }] : []));

watch(
  () => detail.value,
  (v) => ((v || {}).projectId ? handleRefresh() : (project.value = {})),
  { immediate: true, deep: true }
);

const distributionProjectForm = ref<Record<string, any>>({
  projectId: "",
});
const distributionProjectFormRef = ref<FormInstance>();

function handleRemoveProject(row) {
  ElMessageBox.confirm(t("project.Are you sure you want to reomve", { name: row.projectName }), t("glob.prompt"), {
    confirmButtonText: t("glob.Confirm"),
    cancelButtonText: t("glob.Cancel"),
    type: "warning",
    beforeClose: async (action: Action, instance: MessageBoxState, done: () => void) => {
      if (action === "confirm") {
        /*  */
        try {
          const { success, message } = await removeProjectByEventId({ eventId: route.params.id, projectId: row.projectId, orderType: props.data.orderType });
          if (!success) throw new Error(message);
          ElMessage.success(t("axios.Operation successful"));
          // emits("refresh");
          done();
        } catch (error) {
          error instanceof Error && ElMessage.error(error.message);
        } finally {
          props.refresh();
        }
      } else done();
    },
  })
    .then(() => {})
    .catch(() => {});
}

async function handleDistributionProject() {
  let projects: ProjectItem[] = [];
  butLoading.value = true;
  if (userInfo.hasPermission(配置管理中心_项目管理_可读)) {
    const { data, success, message } = await getProjectsByEventId({ id: props.data.projectId || "" });
    if (!success) throw new Error(message);
    projects = data;
  }

  ElMessageBox({
    title: t("glob.Allocate the", { value: t("project.Project") }),
    message: h(
      defineComponent({
        setup() {
          return () =>
            h(
              ElForm,
              { model: distributionProjectForm, ref: (v) => (distributionProjectFormRef.value = v as FormInstance), rules: { projectId: [{ required: true, message: t("project.Please select", { valie: t("project.Project Name") }), trigger: ["change", "blur"] }] } },
              h(
                ElFormItem,
                { label: t("project.Project Name"), prop: "projectId" },
                h(
                  ElSelect,
                  { "modelValue": distributionProjectForm.value.projectId, "onUpdate:modelValue": (v) => (distributionProjectForm.value.projectId = v) },
                  projects.map((v) => h(ElOption, { label: v.projectName, value: v.id, class: "tw-h-[auto]" }, [h("div", [h("p", { class: "tw-text-base tw-font-semibold tw-leading-8" }, v.projectName), h("p", { class: "tw-text-sm tw-leading-7" }, `${t("project.Unified Service Code")}：${v.uniformServiceCode}`), h("p", { class: "tw-text-sm tw-leading-7" }, `${t("project.Project Code")}：${v.projectCode}`)])]))
                )
              )
            );
        },
      })
    ),
    beforeClose: async (action, instance, done) => {
      if (action === "confirm") {
        distributionProjectFormRef.value &&
          distributionProjectFormRef.value.validate(async (valid) => {
            if (!valid) return;
            try {
              const { id } = projects.find((v) => v.id === distributionProjectForm.value.projectId) as ProjectItem;
              const { message, success } = await replaceProjectByEventId({ eventId: route.params.id, projectId: id, orderType: props.data.orderType });
              if (!success) throw new Error(message);
              ElMessage.success(t("axios.Operation successful"));
              distributionProjectFormRef.value && distributionProjectFormRef.value.resetFields();
              emits("refresh");
              done();
            } catch (error) {
              error instanceof Error && ElMessage.error(error.message);
            } finally {
              props.refresh();
            }
          });
      } else {
        done();
      }

      butLoading.value = false;
    },
  })
    .then(() => {})
    .catch(() => {});
}

async function handleRefresh() {
  try {
    const { data, message, success } = await getDictOrderProject({ id: detail.value.projectId });
    if (!success) throw new Error(message);
    project.value = data;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}
</script>
