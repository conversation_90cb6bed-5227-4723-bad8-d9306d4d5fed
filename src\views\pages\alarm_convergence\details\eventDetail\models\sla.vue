<template>
  <el-row>
    <el-col v-for="item in timeOutWornRecord.filter((v) => v.tableData.length)" :key="item.value">
      <div class="tw-py-2 tw-font-bold">{{ item.label }}：</div>
      <el-table v-loading="loading" :data="item.tableData" :height="height - 60" :style="{ width: `100%`, margin: '0 auto' }">
        <el-table-column label="SLA状态" prop="urgencyType" :formatter="tableFormatter" />
        <el-table-column label="计时等级" prop="resolveName">
          <template #default="{ row }">
            <div>{{ row.resolveName }}</div>
            <div class="tw-text-[#909399]">
              <i>{{ row.resolveDescription }}</i>
            </div>
            <div class="tw-text-[#909399]">定义：{{ row.resolveDefinition }}</div>
          </template>
        </el-table-column>
        <el-table-column label="提醒" prop="warnMinutes" :formatter="tableFormatter" />
        <el-table-column label="持续时间" prop="durationMilliSeconds" :formatter="tableFormatter" />
        <el-table-column label="时间戳" prop="stateAt" :formatter="tableFormatter" />
      </el-table>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { ref, inject, toRefs, reactive, onMounted, nextTick, h, watch, computed } from "vue";

import { useRoute } from "vue-router";

import { sizes } from "@/utils/common";

import { urgencyTypeOption } from "@/views/pages/apis/event";

import type { TableColumnCtx } from "element-plus";

import { ElMessage } from "element-plus";

import { eventSla as getData, TimeOutWornRecordItem as DataItem, getTimeOutWornRecords } from "@/views/pages/apis/eventManage";

import { EventItem, type priority } from "@/views/pages/apis/event";

import _timeZoneHours from "@/views/pages/common/offsetHours.json";
import { useI18n } from "vue-i18n";
import moment from "moment";

const route = useRoute();
interface Props {
  height: number;
  data: Partial<EventItem>;
}

// const width = inject("width", ref(0));

const props = withDefaults(defineProps<Props>(), {
  height: 0,
  data: () => <Partial<EventItem>>{},
});

const { height, data: detail } = toRefs(props);
const { t } = useI18n();

function tableFormatter(row, col, v) {
  switch (col.property) {
    case "urgencyType":
      const urgencyType = urgencyTypeOption.find((el) => el.value === v);
      return h("span", { class: " tw-rounded-4xl tw-text-white slaStatus", style: { background: urgencyType?.color, padding: "3px 15px", borderRadius: "15px" } }, `${v?.charAt(0)}${v?.substring(1).toLowerCase()}`);
    case "warnMinutes":
      // 计算天数、小时和剩余的分钟
      try {
        const minutes = Number(v);
        const days = Math.floor(minutes / (60 * 24));
        const remainingHours = minutes % (60 * 24);
        const hours = Math.floor(remainingHours / 60);
        const remainingMinutes = minutes % 60;

        // 构建结果字符串
        let result = "";
        if (days > 0) {
          result += `${days}d`;
        }
        if (hours > 0 || days > 0) {
          // 如果有天数，即使小时为0也显示
          result += `${hours}h`;
        }
        result += `${remainingMinutes}min`;

        return result;
      } catch (error) {
        return v;
      }

    case "durationMilliSeconds":
      try {
        const ms = Number(v);

        const msPerMinute = 60 * 1000;
        const msPerHour = 60 * msPerMinute;
        const msPerDay = 24 * msPerHour;

        const days = Math.floor(ms / msPerDay);
        const remainingHours = ms % msPerDay;
        const hours = Math.floor(remainingHours / msPerHour);
        const remainingMinutes = remainingHours % msPerHour;
        const minutes = Math.floor(remainingMinutes / msPerMinute);

        let result = "";
        if (days > 0) result += `${days}d`;
        if (hours > 0 || days > 0) result += `${hours}h`;
        result += `${minutes}min`;

        return result.trim();
      } catch (error) {
        return v;
      }

    case "stateAt":
      try {
        return moment(Number(v)).format("YYYY-MM-DD HH:mm:ss");
      } catch (error) {
        return v;
      }

    default:
      break;
  }
}

const loading = ref(false);

const timeOutWornRecord = ref<{ value: string; label: string; tableData: DataItem[] }[]>([
  { value: "RESPONSE_TIMEOUT", label: "响应超时", tableData: [] },
  { value: "RESOLVE_TIMEOUT", label: "处理超时", tableData: [] },
  { value: "RESPONSE_UPDATE_TIMEOUT", label: "处理更新超时", tableData: [] },
  { value: "SUSPEND_UPDATE_TIMEOUT", label: "挂起更新超时", tableData: [] },
]);

async function getEventSla() {
  let queryS = {
    // slaSnapshotId: detail.value.slaSnapshotId as string,
    // // priority: detail.value.manualCreate == true ? (detail.value.priority as priority) : (detail.value.originPriority as priority),
    // priority: detail.value.priority as priority,

    orderId: route.params.id,
  };
  try {
    loading.value = true;
    const { data, message, success } = await getTimeOutWornRecords(queryS);
    if (!success) throw new Error(message);

    data.forEach((v) => {
      const item = timeOutWornRecord.value.find((f) => f.value === v.delayType);
      if (item) item.tableData.push(v as DataItem);
    });

    // const { success, data, message } = await getData(queryS);
    // if (!success) throw new Error(message);
    // const order = ["WARNING", "IMMINENT", "BREACH"];
    // data.resolve = data.resolve instanceof Array && data.resolve.length ? (order.map((v) => data.resolve.find((f) => f.urgencyType === v)) as DataItem[]) : [];
    // data.resp = data.resp instanceof Array && data.resp.length ? (order.map((v) => data.resp.find((f) => f.urgencyType === v)) as DataItem[]) : [];
    // const newTime = timeFormat(Number(data.createTime));
    // const TimeS = moment(moment(newTime, "YYYY-MM-DD HH:mm:ss").valueOf() + timeZoneSwitching()).format("YYYY-MM-DD HH:mm:ss");
    // state.data.resolve = data.resolve.map((v) => ({ ...v, createTime: data.createTime != "0" ? moment(Number(data.createTime) + timeZoneSwitching()).format("YYYY-MM-DD HH:mm:ss") : TimeS }));
    // state.data.resp = data.resp.map((v) => ({ ...v, createTime: data.createTime != "0" ? moment(Number(data.createTime) + timeZoneSwitching()).format("YYYY-MM-DD HH:mm:ss") : TimeS }));
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  await nextTick();
  getEventSla();
});

// function formatter(_row: DataItem, _col: TableColumnCtx<DataItem>, v: DataItem[keyof DataItem]): string | import("vue").VNode {
//   return "---";
// }
</script>

<style lang="scss" scoped>
:v-deep(.slaStatus) {
  padding: 0 20px !important;
  box-sizing: border-box;
}
</style>
