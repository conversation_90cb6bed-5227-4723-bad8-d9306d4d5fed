<template>
  <!-- 事件详情 -->
  <pageTemplate :show-paging="false" :height="height" class="strategys">
    <template #default>
      <el-row :gutter="20">
        <!-- 设备＞场所＞区域＞客户排序 -->
        <!-- 设备工作策略 resourceSupportNotes-->
        <!-- 场所工作策略 locationSupportNotes-->
        <!-- 区域工作策略 regionSupportNotes-->
        <!-- 客户工作策略 tenantSupportNotes-->
        <el-col :span="24" v-for="item in resourceSupportNotesData" :key="item.id" class="tw-mb-5">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-card :class="item.workTimeFlag ? 'strategy-item ' : ''" :body-style="{ padding: 0 }" class="tw-h-full">
                <template #header>
                  <div class="workTitle">
                    {{ item.name }}<span>{{ filteringType(item.supportBindType) }} </span>
                  </div>
                </template>
                <!--  -->
                <el-scrollbar :ref="(v) => (strategyRefs[`${item.id}-left`] = v)" :height="strategyHeights[item.id]" :class="`tw-h-[${strategyHeights[item.id]}px]`">
                  <div class="pre-line tw-items-center tw-p-5 tw-pb-0" v-html="item.activeNote ?? ''"></div>
                </el-scrollbar>
                <el-divider v-if="!item.allDayFlag" />
                <div class="tw-p-5 tw-pt-0" v-if="!item.allDayFlag">
                  <span v-if="item.workTimeFlag">{{ item.activeConfig.timeForZone ? "注意：使用直到 " + item.activeConfig.timeForZone : "" }}</span>
                  <span v-else>{{ item.activeConfig.timeForZone ? "注意：从 " + item.activeConfig.timeForZone + " 开始使用" : "" }}</span>
                </div>
                <!-- <el-divider style="margin: 0" /> -->
                <template #footer>
                  <div class="tags">
                    <img class="tags-icon" src="@/assets/tags.png" />
                    <span>{{ item.publicTags }}</span>
                  </div>
                </template>
              </el-card>
            </el-col>
            <el-col :span="12" v-if="!isEmptyOrNullOrEmptyTags(item.inactiveNote)">
              <el-card :class="!item.workTimeFlag ? 'strategy-item' : ''" :body-style="{ padding: 0, height: 'auto' }">
                <template #header>
                  <div class="workTitle">
                    {{ item.name }}<span>{{ filteringType(item.supportBindType) }} </span>
                  </div>
                </template>
                <!-- height="150" class="tw-h-[150px]" -->
                <el-scrollbar :ref="(v) => (strategyRefs[`${item.id}-right`] = v)" :height="strategyHeights[item.id]" :class="`tw-h-[${strategyHeights[item.id]}px]`">
                  <div class="pre-line tw-items-center tw-p-5 tw-pb-0" v-html="item.inactiveNote ?? ''"></div>
                </el-scrollbar>
                <el-divider v-if="!item.allDayFlag" />
                <div class="tw-p-5 tw-pt-0" v-if="!item.allDayFlag">
                  <span v-if="!item.workTimeFlag">{{ item.activeConfig.timeForZone ? "注意：使用直到 " + item.activeConfig.timeForZone : "" }}</span>
                  <span v-else>{{ item.activeConfig.timeForZone ? "注意：从 " + item.activeConfig.timeForZone + " 开始使用" : "" }}</span>
                </div>
                <!-- <el-divider style="margin: 0" /> -->
                <template #footer>
                  <div class="tags">
                    <img class="tags-icon" src="@/assets/tags.png" />
                    <span>{{ item.publicTags }}</span>
                  </div>
                </template>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </template>
  </pageTemplate>
</template>

<script setup lang="ts" name="event-strategy">
import pageTemplate from "@/components/pageTemplate.vue";
import { computed, onMounted, ref, toRefs, watch, nextTick } from "vue";
import { getResourcesSupportNotes as getData, type SupportNotes as Item } from "@/views/pages/apis/eventManage";
import { ElMessage } from "element-plus";
import { EventItem } from "@/views/pages/apis/event";
import zone from "@/views/pages/common/strategyZone.json";
import getUserInfo from "@/utils/getUserInfo";
import { timeFormat } from "@/utils/date";
import { formatDate } from "@/views/pages/common/dateChange";
import { strategyZone } from "../../../../common/strategyZone";
import { publicEncrypt } from "crypto";
import { 资产管理中心_行动策略_可读, 资产管理中心_设备_可读 } from "@/views/pages/permission";

interface Props {
  height: number;
  data: Partial<EventItem>;
}

// type Item = SupportNotes & Record<"dateTime", "string">;

const props = withDefaults(defineProps<Props>(), {
  height: 0,
  data: () => <Partial<EventItem>>{},
});

const { height, data: detail } = toRefs(props);

watch(
  () => props.data,
  ({ deviceIds }) => {
    if (getUserInfo().hasPermission(资产管理中心_行动策略_可读) && getUserInfo().hasPermission(资产管理中心_设备_可读)) {
      deviceIds && deviceIds instanceof Array && deviceIds.length && queryItems();
    }
  },
  { immediate: true }
);

const resourceSupportNotesData = ref<Item[]>([]);
const priority = {
  RESOURCE: 1,
  LOCATION: 2,
  REGION: 3,
  CUSTOMER: 4,
};

// const locationSupportNotesData = ref<Item[]>([]);
// const regionSupportNotesData = ref<Item[]>([]);
// const tenantSupportNotesData = ref<Item[]>([]);
const weekDay = ref<number>(new Date().getDay());
async function queryItems() {
  try {
    const { success, data, message } = await getData({ ids: detail.value.deviceIds?.join() as string, containsGlobal: false });
    if (!success) throw new Error(message);

    data.showSupportNotes.forEach((v, i) => {
      if (v.active) {
        let arrLength = 0;
        // if (v.activeConfig.useAutoTimeZone) {
        //   let timezoneOffset = -new Date().getTimezoneOffset();
        //   for (let i = 0; i < zone.length; i++) {
        //     if (zone[i].zoneId === getUserInfo()?.zoneId) {
        //       v.activeConfig.activeHours.forEach((h) => {
        //         if (h.weekDay == weekDay.value) {
        //           let date = new Date();
        //           let year = date.getFullYear();
        //           let mon = date.getMonth();
        //           let day = date.getDate();
        //           let time = new Date(year, mon, day, h.hours.length == 24 ? 24 : v.activeTime, "00", "00").getTime() + (timezoneOffset - zone[i].offsetMinutes) * 60 * 1000;

        //           v.dateTime = formatDate(time, "yyyy-MM-dd HH:mm");

        //           v.showWorkTime = date > new Date(v.dateTime) ? false : true;
        //           if (h.hours.length == 0) {
        //             v.dateTime = "";
        //           }
        //         }
        //       });

        //       // console.log(v.allTime);
        //       break;
        //     } else {
        //       // arrLength = 0;
        //       v.activeConfig.activeHours.forEach((h) => {
        //         if (h.weekDay == weekDay.value) {
        //           let date = new Date();
        //           let year = date.getFullYear();
        //           let mon = date.getMonth();
        //           let day = date.getDate();
        //           let time = new Date(year, mon, day, h.hours.length == 24 ? 24 : v.activeTime, "00", "00").getTime() + timezoneOffset * 60 * 1000;

        //           v.dateTime = formatDate(time, "yyyy-MM-dd HH:mm");

        //           v.showWorkTime = date > new Date(v.dateTime) ? false : true;
        //           if (h.hours.length == 0) {
        //             v.dateTime = "";
        //           }
        //         }
        //         // // console.log(arrLength);
        //       });
        //     }
        //   }
        // } else {
        //   let arrLength = 0;
        //   let timezoneOffset = -new Date().getTimezoneOffset();

        //   if (timezoneOffset === strategyZone[v.activeConfig.timeZone]) {
        //     // console.log(v);
        //     v.activeConfig.activeHours.forEach((h) => {
        //       if (h.weekDay == weekDay.value) {
        //         let date = new Date();
        //         let year = date.getFullYear();
        //         let mon = date.getMonth();
        //         let day = date.getDate();
        //         let time = new Date(year, mon, day, h.hours.length == 24 ? 24 : v.activeTime, "00", "00").getTime();
        //         v.dateTime = formatDate(time, "yyyy-MM-dd HH:mm");

        //         v.showWorkTime = date > new Date(v.dateTime) ? false : true;
        //         if (h.hours.length == 0) {
        //           v.dateTime = "";
        //         }
        //       }
        //     });
        //   } else {
        //     let arrLength = 0;
        //     let timezoneOffset = -new Date().getTimezoneOffset();

        //     zone.forEach((item) => {
        //       if (item.zoneId === v.activeConfig.timeZone) {
        //         v.activeConfig.activeHours.forEach((h) => {
        //           if (h.weekDay == weekDay.value) {
        //             let date = new Date();
        //             let year = date.getFullYear();
        //             let mon = date.getMonth();
        //             let day = date.getDate();
        //             let time = new Date(year, mon, day, h.hours.length == 24 ? 24 : v.activeTime, "00", "00").getTime() + (timezoneOffset - item.offsetMinutes) * 60 * 1000;
        //             v.dateTime = formatDate(time, "yyyy-MM-dd HH:mm");

        //             let isWokrTime = h.hours.indexOf(v.activeTime - 1 - (timezoneOffset - item.offsetMinutes) / 60);

        //             v.showWorkTime = date > new Date(v.dateTime) ? false : true;
        //             if (h.hours.length == 0) {
        //               v.dateTime = "";
        //             }
        //           }
        //         });
        //       }
        //     });
        //   }
        // }

        v.activeConfig.activeHours.forEach((h) => {
          if (h.hours.length == 24) {
            arrLength = arrLength + 1;
          }
        });

        if (arrLength === v.activeConfig.activeHours.length) {
          v.allTime = true;
        }
        switch (v.supportBindType) {
          case "RESOURCE":
            return (v.publicTags = v.resourceTags ? v.resourceTags.toString() : "");
          case "LOCATION":
            return (v.publicTags = v.locationTags ? v.locationTags.toString() : "");
          case "CUSTOMER":
            return (v.publicTags = v.customerTags ? v.customerTags.toString() : "");
          case "REGION":
            return (v.publicTags = v.regionTags ? v.regionTags.toString() : "");
          default:
            return (v.publicTags = "");
        }
      }
    });
    data.showSupportNotes.sort((a, b) => {
      const priorityA = a.supportBindType !== null && priority[a.supportBindType] !== undefined ? priority[a.supportBindType] : 5;
      const priorityB = b.supportBindType !== null && priority[b.supportBindType] !== undefined ? priority[b.supportBindType] : 5;
      return priorityA - priorityB;
    });
    resourceSupportNotesData.value = data.showSupportNotes;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

// 筛选策略类型
function filteringType(supportBindType) {
  let translation = supportBindType === "CUSTOMER" ? "客户工作策略" : supportBindType === "RESOURCE" ? "设备工作策略" : supportBindType === "LOCATION" ? "场所工作策略" : supportBindType === "REGION" ? "区域工作策略" : ""; // 默认值，防止意外的类型
  return translation;
}

function isEmptyOrNullOrEmptyTags(str) {
  // 如果字符串是null或undefined，直接返回true
  // 如果字符串为 null 或 undefined，直接返回 true
  if (str === null || str === undefined) {
    return true;
  }
  // 去除字符串中的空格和 HTML 标签，然后检查是否为空
  const cleanedStr = str
    .replace(/<[^>]*>/g, "")
    .replace(/&nbsp;/g, "")
    .trim();
  // 检查 cleanedStr 是否为空
  return cleanedStr === "";
}
const isRestDay = computed(() => {
  return weekDay.value === 6 || weekDay.value === 7;
});

const strategyRefs = ref<Record<string, any>>({});

const strategyHeights = computed(() =>
  resourceSupportNotesData.value.reduce((p, t) => {
    return Object.assign(p, {
      [`${t.id}`]: Math.max(
        ...[
          /*  */
          ((strategyRefs.value[`${t.id}-left`] || {}).wrapRef || {}).offsetHeight || 0,
          ((strategyRefs.value[`${t.id}-right`] || {}).wrapRef || {}).offsetHeight || 0,
        ]
      ),
    });
  }, {})
);

onMounted(() => {});
</script>

<style lang="scss" scoped>
@import "@/styles/theme/common/var.scss";

.strategys .strategy-item {
  :deep(.el-card) {
    border: 1px solid $color-primary;
  }
  :deep(.el-card__header) {
    background: $color-primary;
    color: $color-white;
  }
}
.workTitle {
  display: flex;
  justify-content: space-between;
}
.tags {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  img {
    width: 15px;
    height: 15px;
    margin-right: 5px;
  }
  span {
    display: inline-block;
    width: 100%;
    word-wrap: break-word;
  }
}
</style>
