<template>
  <div class="tw-flex tw-h-[50px] tw-flex-nowrap tw-items-center tw-pb-[20px]">
    <el-page-header class="tw-w-full" :content="`${'发布详情'}${state.data.draft ? `(草稿)` : route.params.id || ''}`" @back="handleCancel()">
      <template #extra>
        <div class="event">
          <el-row :gutter="8" type="flex" justify="end" align="middle">
            <el-col :span="7" :style="{ display: 'flex', justifyContent: 'flex-end' }">
              <el-button-group :style="{ display: 'flex' }">
                <el-button :type="(currentEventState.type as '') || ''" :disabled="state.data.operation === publishOperate.CLOSE" @click.stop>{{ currentEventState.label || "" }}</el-button>
                <el-dropdown @command="$event.command()">
                  <el-button :type="(currentEventState.type as '') || ''" @click.stop>
                    <!-- <template v-if="!!operation && !state.data.rejectAble">{{ (find(publishOperateOption, (v) => v.value === operation) || {}).label }}</template>
                    <template v-else-if="!!stateRightText"> {{ stateRightText }}</template> -->
                    <!-- <template v-else>
                      <template v-if="state.data.publishState === publishState.PROCESSING || state.data.publishState === publishState.PROCESSING">处理</template>
                    </template> -->
                    <template v-if="!!operation && !state.data.rejectAble">
                      <span v-if="OperateChanged" class="tw-mr-1 tw-text-white">*</span>
                      {{ $t("generalDetails." + (find(publishOperateOption, (v) => v.value === operation) || {}).label) }}</template
                    >

                    <template v-else-if="!!stateRightText">
                      <span v-if="OperateChanged" class="tw-mr-1 tw-text-white">*</span>
                      {{ $t("generalDetails." + stateRightText) }}
                    </template>
                    <el-icon class="tw-ml-2"><ArrowDown></ArrowDown></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu class="tw-min-w-[100px]">
                      <el-dropdown-item
                        v-for="item in [
                          // changeStateWithdraw, changeStateAbandon, changeStateComplete, changeStateClose
                          { command: publishOperate.DISPOSE, /*   */ execute: () => handleDispose({ operation: 'DISPOSE' } as DataItem), /*                                 */ center: $t('generalDetails.处理'), /*  */ disabled: [publishState.AUTO_CLOSED, publishState.CLOSED, publishState.PROCESSING].includes((state.data.publishState as publishState) || ('' as publishState)) && state.data.rejectAble },
                          { command: publishOperate.WITHDRAW, /*  */ execute: () => handleClose(state.data as DataItem, publishOperate.WITHDRAW, setPublishItemDetail), /*  */ center: $t('generalDetails.撤回'), /*  */ disabled: (state.data.approveState == 'UN_APPROVE' && state.data.publishState != 'NEW' ? false : true) || state.data.rejectAble },
                          { command: publishOperate.COMPLETE, /*  */ execute: () => handleClose(state.data as DataItem, publishOperate.COMPLETE, setPublishItemDetail), /*  */ center: $t('generalDetails.完成'), /*  */ disabled: orderIsCompleted },
                          { command: publishOperate.CLOSE, /*     */ execute: () => handleClose(state.data as DataItem, publishOperate.CLOSE, setPublishItemDetail), /*     */ center: $t('generalDetails.关闭'), /*  */ disabled: !orderIsClose },
                        ]"
                        :key="item.command"
                        :command="{ command: item.execute }"
                        :disabled="state.loading || item.disabled"
                      >
                        {{ item.center }}
                        <!-- {{}} -->
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-button-group>

              <!-- -->
            </el-col>
            <el-col :span="7" style="display: flex">
              <span v-if="userGroupChanged" class="tw-mr-1 tw-text-red-500">*</span>
              <el-select clearable :model-value="userGroups.find((v) => v.id === transferOrUpgrade.userGroupId) ? transferOrUpgrade.userGroupId : ''" @change="handleChangeUserGroup" placeholder="请选择用户组" filterable :disabled="!userInfo.hasPermission(智能事件中心_发布工单_分配用户组) || [publishState.AUTO_CLOSED, publishState.CLOSED].includes((state.data.publishState as publishState) || ('' as publishState))">
                <el-option v-for="userGroup in userGroups" :key="userGroup.id" :label="userGroup.name" :value="userGroup.id as string"></el-option>
              </el-select>
            </el-col>
            <el-col :span="7" style="display: flex">
              <span v-if="userChanged" class="tw-mr-1 tw-text-red-500">*</span>
              <el-select :model-value="userList.find((v) => v.id === transferOrUpgrade.userId) ? transferOrUpgrade.userId : ''" @change="handleChangeUser" placeholder="请选择用户" filterable :disabled="!userInfo.hasPermission(智能事件中心_发布工单_分配用户) || [publishState.AUTO_CLOSED, publishState.CLOSED].includes((state.data.publishState as publishState) || ('' as publishState))">
                <el-option v-for="user in userList" :key="user.id" :label="user.name + `(${user.account}@${user.tenantAbbreviation})`" :value="user.id"></el-option>
              </el-select>
            </el-col>
            <el-col :span="3" :style="{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }">
              <el-tooltip :visible="state.data.draft" placement="top" effect="light" popper-class="draft-tooltip">
                <template #content>
                  <span>{{ $t("generalDetails.Click to save and create a work order") }}</span>
                </template>
                <el-button type="primary" v-preventReClick @click="handleChangeSave" :disabled="!verifyPermissionIds.includes('612916482183004160') || state.data.rejectAble /* [publishState.NEW].includes(state.data.publishState) && */ /* operation !== publishOperate.DISPOSE */">保存</el-button>
              </el-tooltip>
            </el-col>
            <!-- <el-col :span="3" :style="{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }">
              <el-checkbox v-model="typeOfTransferOrUpgrade" false-label="transfer" true-label="eventUpgrade" style="margin-right: 5px" :disabled="[publishState.NOT_STARTED, publishState.NOT_STARTED, publishState.CLOSED].includes(state.data.publishState as publishState || '' as publishState)">升级</el-checkbox>
              <el-tooltip class="item" effect="dark" content="若需要升级请先点选升级复选框" placement="bottom">
                <el-icon class="tipIcon"><InfoFilled></InfoFilled></el-icon>
              </el-tooltip>
            </el-col> -->
          </el-row>
        </div>
      </template>
    </el-page-header>
  </div>
  <el-scrollbar :height="height - 50">
    <el-card v-loading="state.loading" class="tw-mb-[18px]">
      <template #header>
        <div style="display: flex; justify-content: space-between">
          <el-button link type="primary" class="tw-font-semibold" :style="{ color: 'var(--el-color-primary)' }" :disabled="[publishState.AUTO_CLOSED, publishState.CLOSED].includes((state.data.publishState as publishState) || ('' as publishState))" v-preventReClick @click="handleEditSummary">
            <span v-if="digestChanged" style="color: red; margin-left: 2px">*</span>
            <span>{{ state.data.digest || "--" }}</span>
          </el-button>
          <div style="display: flex">
            <el-text type="primary">{{ tickGroupConfig.ticketClassificationNames.join("/") }}</el-text>
            <div style="margin-right: 2px" v-for="itemA in sortedLocalesOption" :key="itemA.value">
              <el-tooltip v-if="isLanguageMatch(itemA.value)" class="item" effect="light" :content="getTooltipContent(itemA.value, itemA.zh_label)" placement="bottom">
                <div
                  :style="{
                    background: `url(${itemA.icon}) no-repeat left / auto`,
                    paddingLeft: '30px',
                    width: '22px',
                    height: '22px',
                  }"
                ></div>
              </el-tooltip>
            </div>
            <div>{{ state.data.category }}</div>
          </div>
        </div>
      </template>
      <template #default>
        <FormModel :model="form" :style="{ marginBottom: '-18px' }">
          <!-- <FormItem :span="8" label="创建时间" tooltip="" prop="" :rules="[]">{{ state.data.collectTime ? moment(state.data.collectTime, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</FormItem> -->
          <!-- <FormItem :span="8" label="持续时间" tooltip="" prop="" :rules="[]">{{ state.data.collectTime ? moment(state.data.collectTime, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</FormItem> -->
          <FormItem :span="3" :label="$t('generalDetails.Priority')" tooltip="" prop="" :rules="[]">
            <template #label>
              <span>
                <span v-if="priorityChanged" style="color: red">*</span>
                {{ $t("generalDetails.Priority") }}
              </span>
            </template>
            <el-dropdown trigger="click" @command="handleSetPriority($event, state.data)" :disabled="[publishState.AUTO_CLOSED, publishState.CLOSED].includes((state.data.publishState as publishState) || ('' as publishState))">
              <span class="el-dropdown-link">
                <i class="priority-icon" :style="{ backgroundColor: currentEventState.color || '' }" />
                <span class="tw-align-[2px]" :style="{ color: currentPriority.color || '' }">{{ state.data.priority }}</span>
                <el-icon class="el-icon--right"><ArrowDown></ArrowDown></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="(value, key) in priorityOption" :key="`priority-${key}`" :command="value.value">{{ value.label }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </FormItem>
          <FormItem :span="4" label="紧急性" tooltip="" prop="" :rules="[]">
            <template #label>
              <span>
                <span v-if="urgencyChanged" style="color: red">*</span>
                {{ $t("generalDetails.Urgency") }}
              </span>
            </template>
            <el-select :model-value="state.data.urgency || ('' as string)" filterable @change="handleSetSeverity($event, state.data)" :disabled="[publishState.AUTO_CLOSED, publishState.CLOSED].includes((state.data.publishState as publishState) || ('' as publishState))">
              <el-option v-for="item in deviceImportanceOption.map((v) => ({ ...v, priority: priorityMatrix.filter((raw) => raw.eventSeverity === v.value) })).filter((v) => v.priority.length)" :key="item.value" :label="`${item.label}`" :value="item.value"></el-option>
            </el-select>
          </FormItem>
          <FormItem :span="4" label="影响性" tooltip="" prop="" :rules="[]">
            <template #label>
              <span>
                <span v-if="impactChanged" style="color: red">*</span>
                {{ $t("generalDetails.Impact") }}
              </span>
            </template>
            <el-select :model-value="state.data.influence || ('' as string)" filterable @change="handleSetImportance($event, state.data)" :disabled="[publishState.AUTO_CLOSED, publishState.CLOSED].includes((state.data.publishState as publishState) || ('' as publishState))">
              <el-option v-for="item in deviceImportanceOption.map((v) => ({ ...v, priority: priorityMatrix.filter((raw) => raw.deviceImportance === v.value) }))" :key="item.value" :label="`${item.label}`" :value="item.value"></el-option>
            </el-select>
          </FormItem>

          <FormItem :span="24" label="发布类型" tooltip="" prop="" :rules="[]">
            <el-tag effect="plain" size="medium">
              {{ publishTypeOption.find((opt) => opt.value === state.data.publishType)?.label }}
            </el-tag>
          </FormItem>
          <!-- <el-col :span="24" class="tw-mb-[18px] tw-bg-[var(--el-bg-color-page)] tw-py-2">SLA情况</el-col>
        <FormItem :span="8" label="响应时限情况" tooltip="" prop="" :rules="[]">
          <el-progress :percentage="responseTimePercentage" :color="resolveUrgencyType.color" :format="() => `${state.data.responseTime || 0}分钟 / ${state.data.responseLimit || 0}分钟`" class="tw-w-full"></el-progress>
        </FormItem>
        <el-col :span="1"></el-col>
        <FormItem :span="8" label="解决时限情况" tooltip="" prop="" :rules="[]">
          <el-progress :percentage="resolveTimePercentage" :color="resolveUrgencyType.color" :format="() => `${state.data.resolveTime || 0}分钟 / ${state.data.resolveLimit || 0}分钟`" class="tw-w-full"></el-progress>
        </FormItem> -->
        </FormModel>
        <div></div>
        <!-- <pre>{{ state.data }}</pre> -->
      </template>
    </el-card>
    <el-card class="tw-mb-[18px] tw-h-[520px]">
      <template #header>
        <el-button link type="primary" class="tw-font-semibold" :style="{ color: 'var(--el-color-primary)' }">{{ "发布日历" }}</el-button>
      </template>
      <template #default>
        <div class="calendar">
          <el-calendar v-model="dateValue">
            <template #date-cell="{ data }">
              <div v-preventReClick @click="getMonthPublishList(data.day)" class="calendar-div">
                <p :class="data.isSelected ? 'is-selected' : ''">
                  {{ data.day.split("-")[2] }}
                  <span v-for="item in publishDate" class="on" :key="item">
                    <!-- {{ item }} -->

                    {{ data.day === item ? "Δ" : "" }}
                  </span>
                  <!-- {{ data.isSelected ? "✔️" : "" }} -->
                </p>
              </div>
            </template>
          </el-calendar>
          <el-table v-loading="calendarLoading" ref="tableRef" :data="dataList" row-key="id" :height="400" style="width: 95%">
            <el-table-column prop="identifier" label="工单">
              <template #default="{ row, column }">
                <router-link v-if="row.id" :to="{ name: '519831507997556736', params: { id: row.id }, query: { fallback: route.name as string } }">
                  <template #default="{ href }">
                    <el-link type="primary" :href="href" target="_blank" :underline="false" class="tw-ml-[6px]" @click.stop>{{ row[column.property] }}</el-link>
                  </template>
                </router-link>
              </template>
            </el-table-column>
            <el-table-column prop="publishType" label="发布类型" :formatter="formatterTable"> </el-table-column>
            <!-- <TableColumn type="enum" prop="publishType" label="发布类型" :width="120"></TableColumn> -->
            <!-- :filters="priorityOption.map((v) => ({ ...v, text: v.label }))" -->
            <TableColumn prop="priority" label="优先级" :width="120">
              <template #default="{ row }">
                <div v-for="item in priorityOption" :key="item.label">
                  <span v-show="item.value === row.priority" class="tw-rounded-full tw-px-3 tw-py-1 tw-text-white" :style="{ background: item.color }">
                    {{ row.priority }}
                  </span>
                </div>
              </template>
            </TableColumn>

            <!-- <el-table-column prop="priority" label="优先级" /> -->

            <!-- </el-table> -->
            <el-table-column prop="digest" label="摘要" />
            <el-table-column prop="startTime" label="计划开始时间">
              <template #default="{ row }">
                {{ row.startTime ? moment(row.startTime, "x").format("yyyy-MM-DD HH:mm") : "--" }}
              </template>
            </el-table-column>
            <el-table-column prop="endTime" label="计划结束时间">
              <template #default="{ row }">
                {{ row.endTime ? moment(row.endTime, "x").format("yyyy-MM-DD HH:mm") : "--" }}
              </template>
            </el-table-column>
            <el-table-column prop="createdBy" label="发布申请人" :formatter="formatterTable" />
          </el-table>
        </div>
        <!-- <pre>{{ state.data }}</pre> -->
      </template>
    </el-card>
    <completeCode v-if="[publishState.CLOSED, publishState.AUTO_CLOSED].includes((state.data.publishState as publishState) || ('' as publishState))" class="tw-mb-[18px]" :title="currentEventState.label || ''" :finishCodeName="state.data.completeInfo?.finishCodeName || ''" :finishCodeDesc="state.data.completeInfo?.finishCodeDesc || ''" :finishContent="state.data.completeInfo.finishContent || ''" />
    <el-card v-loading="state.loading">
      <template #header>
        <el-radio-group :model-value="(route.query.type as string) || ''" @change="router.push({ query: { ...route.query, type: $event as string } })">
          <el-radio-button :label="pageType.details">详述</el-radio-button>
          <el-radio-button :label="pageType.subtotal">
            <el-badge class="mark" :value="tabCounts.noteCount || undefined"> <div class="tw-px-[1em]">小记</div> </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.approve"
            ><el-badge class="mark" :value="Number(tabCounts.approveCount) || undefined"><div class="tw-px-[1em]">审批</div></el-badge></el-radio-button
          >
          <el-radio-button :label="pageType.device">
            <el-badge class="mark" :value="tabCounts.deviceCount || undefined"> <div class="tw-px-[1em]">设备</div> </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.contacts">
            <el-badge class="mark" :value="tabCounts.contactCount || undefined"> <div class="tw-px-[1em]">联系人</div> </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.project">
            <el-badge class="mark" :value="undefined">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Projects") }}</div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.related" :disabled="!userInfo.hasPermission('520408475474329600')">
            <el-badge class="mark" :value="tabCounts.relationCount || undefined"> <div class="tw-px-[1em]">关联</div> </el-badge>
          </el-radio-button>
          <!-- <el-radio-button :label="pageType.alarm">
            <el-badge class="mark">
              <div class="tw-flex tw-px-[1em]">
                <span>告警记录</span>
                <template v-if="Number(tabCounts.unconfirmed) && state.data.collectAlert">
                  <span class="unconfirmed tw-mx-[5px]">{{ tabCounts.unconfirmed }}</span>
                  <span class="confirmed tw-mx-[5px]">{{ tabCounts.confirmed }}</span>
                </template>
                <el-icon v-else class="tw-mx-[5px]"><CircleClose /></el-icon>
              </div>
            </el-badge>
          </el-radio-button> -->
          <el-radio-button :label="pageType.sla">
            <el-badge class="mark"> <div class="tw-px-[1em]">SLA</div> </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.strategy">
            <el-badge class="mark" :value="Number(tabCounts.actionCount) || undefined"><div class="tw-px-[1em]">行动策略</div></el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.file">
            <el-badge class="mark" :value="tabCounts.fileCount || undefined"> <div class="tw-px-[1em]">文件</div> </el-badge>
          </el-radio-button>

          <el-radio-button :label="pageType.dynamics">历史日志</el-radio-button>
        </el-radio-group>
      </template>
      <template #default>
        <el-scrollbar>
          <div v-if="((route.query.type as string) || '') === pageType.details">
            <!-- 详述 -->
            <ModelDetails
              :data="state.data"
              @change-date="
                (v) => {
                  state.data.startTime = v.startTime;
                  state.data.endTime = v.endTime;
                }
              "
              @change-desc="(v) => (state.data.desc = v)"
              :refresh="handleRefresh"
              :height="0"
            ></ModelDetails>
          </div>

          <div v-if="((route.query.type as string) || '') === pageType.subtotal">
            <!-- 小记 -->
            <ModelNotes ref="modelNotesRef" :data="state.data" :height="0"></ModelNotes>
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.approve">
            <!-- 审批 -->
            <ModelApprove :data="state.data" :refresh="handleRefresh" :height="0"></ModelApprove>
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.device">
            <!-- 设备 -->
            <ModelDevices :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.contacts">
            <!-- 联系人 -->
            <ModelContacts :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.project">
            <ModelProject :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.related">
            <!-- 关联 -->
            <ModelAssociation :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.alarm">
            <!-- 告警记录 -->
            <ModelAlarm :data="state.data" :refresh="handleRefresh" :handleEnd="handleAlarm" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.sla">
            <!-- SLA -->
            <ModelSLA :data="state.data" :refresh="handleRefresh" :height="0"></ModelSLA>
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.strategy">
            <!-- 行动策略 -->
            <ModelStrategy :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.file">
            <!-- 文件 -->
            <ModelFiles :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.dynamics">
            <!-- 动态 -->
            <ModelDynamics :data="state.data" :refresh="handleRefresh" :height="0"></ModelDynamics>
          </div>
        </el-scrollbar>
      </template>
    </el-card>
  </el-scrollbar>
  <EventPend ref="pendRef" :refresh="handleRefresh"></EventPend>

  <EventEnd
    ref="endRef"
    :refresh="handleRefresh"
    :ticketTemplateId="state.data.ticketTemplateId"
    @end="
      (v) => {
        // console.log(v);
        completeQuery = v.params;
        operation = v.type;
        notesQuery = v.notesForm;
      }
    "
    :height="height - 317"
  ></EventEnd>

  <Editor ref="editorRef" title="变更" display="dialog">
    <template #setPriority="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          确定设置
          <span>{{ ((params.raw || {}) as AnyObject).summary as string }}</span>
          变更优先级为
          <span>{{ params.label }}</span>
          吗？
        </p>
      </div>
    </template>
    <template #setImportance="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          确定设置
          <span>{{ ((params.raw || {}) as AnyObject).summary as string }}</span>
          变更重要性为
          <span>{{ params.label }}</span>
          吗？
        </p>
      </div>
    </template>
    <template #setSeverity="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          确定设置
          <span>{{ ((params.raw || {}) as AnyObject).summary as string }}</span>
          变更紧急性为
          <span>{{ params.label }}</span>
          吗？
        </p>
      </div>
    </template>
  </Editor>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, inject, watchEffect, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch, computed, h, provide, toRefs } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Refresh, InfoFilled, ArrowDown } from "@element-plus/icons-vue";
// import pageTemplate from "@/components/pageTemplate.vue";
import { ElTable, ElIcon } from "element-plus";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";

import ModelDetails from "./models/details.vue";
import ModelSubtotal from "./models/subtotal.vue";
import ModelSLA from "./models/sla.vue";
import ModelDynamics from "./models/dynamics.vue";

import ModelNotes from "./models/notes.vue";

import ModelDevices from "./models/devices.vue";

import ModelContacts from "./models/contacts.vue";

import ModelProject from "@/views/pages/alarm_convergence/details/eventDetail/models/project.vue";

import ModelAssociation from "./models/association.vue";

import ModelFiles from "./models/files.vue";

import ModelAlarm from "./models/alarm.vue";

import ModelStrategy from "./models/strategy.vue";

import EventPend from "./models/pend.vue";

import EventEnd from "./models/end.vue";

import ModelApprove from "./models/approve.vue";

import Editor from "./Editor.vue";

import completeCode from "@/components/completeCode.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox } from "element-plus";
// import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { useSiteConfig } from "@/stores/siteConfig";
import getUserInfo from "@/utils/getUserInfo";
import { find } from "lodash-es";
import moment from "moment";

import { type BaseItem, DataItem, type Item } from "./helper";
import { state } from "./helper";
import { resetData, command } from "./helper";

/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
// import { publishState, publishStateOption, eventSeverity, eventSeverityOption, priority, priorityOption } from "@/views/pages/apis/question";
import { eventSeverity, eventSeverityOption, priority, priorityOption, changeStateDispose as dispose, changeStateWithdraw, changeStateAbandon, changeStateComplete, changeStateClose, changeUser, changeUserGroup } from "@/views/pages/apis/change";
import { publishStateOption, publishState, publishOperateOption, setPublishItemDetail, getPublishDateList } from "@/views/pages/apis/publish";
import { deviceImportance, deviceImportanceOption } from "@/views/pages/apis/device";

// import { getEventData as getItemData } from "@/views/pages/apis/event";

import { /* setChangePriority, setChangeImportance, setChangeSeverity, setChangeStateProcessing,*/ questionTransfer } from "@/views/pages/apis/question";

import { getPublishDetail as getItemData, publishOperate, publishType, publishTypeOption, getDatePublishList, type publishItem, setPublishItemImportance, setPublishItemEmergent } from "@/views/pages/apis/publish";
// setChangePriority,setChangeImportance, setChangeSeverity

import { addEventData as addItemData, setEventData as setItemData, modEventData as modItemData, delEventData as delItemData } from "@/views/pages/apis/event";
import { setEventDataByTakeOver, setEventDataByApprove, setEventDataByTransferOrUpgrade, type TimeLimit } from "@/views/pages/apis/event";
import { getPriorityMatrixList } from "@/views/pages/apis/eventPriority";
import { getGroupList, getUserByGroup, type GroupItem, type EntrustUserItem } from "@/api/personnel";
import { setChangeDigest as editDigest /*changeOperateOption*/ } from "@/views/pages/apis/change";

import { getDetailTapCountById, type TabCount } from "@/views/pages/apis/publish";

import { 智能事件中心_发布工单_分配用户, 智能事件中心_发布工单_分配用户组 } from "@/views/pages/permission";
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);
import { localesOption } from "@/api/locale";
import { getTenantInfo } from "@/views/pages/apis/tenant";
import { getContactTypes as getType, type ContactsTypeItem, type ContactsItem } from "@/views/pages/apis/contacts";
import { eventBatchContact as getData, eventBatchdesensitized, eventAddContact as addData, eventDelContact as delData, getChangeContacts } from "@/views/pages/apis/eventManage";

import { getCloseDirectly, getOrderUserGroup, AssignableTicketType, UserGroupConfigurationItem, getOrderUserGroupIsClose, getTicketClassificationNames } from "@/views/pages/apis/orderGroup";

import { 智能事件中心_服务请求工单_编辑小记, 智能事件中心_事件工单_编辑小记, 智能事件中心_DICT事件管理_编辑小记, 智能事件中心_DICT服务请求_编辑小记, 智能事件中心_变更工单_编辑小记, 智能事件中心_问题工单_编辑小记, 智能事件中心_发布管理_编辑小记 } from "@/views/pages/permission";
import { addOrderNode } from "@/views/pages/apis/event";

import { getticketConfigurations } from "@/views/pages/apis/ticketTemplate";

/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const contactsType = ref<{ contactId: string; contactType: string }[]>([]);
const contacts = ref<ContactsItem[]>([]);
const uniqueByLanguage = ref<ContactsItem[]>([]);
const sortedLocalesOption = ref<ContactsItem[]>(localesOption);
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const modelNotesRef = ref();
const { t } = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "alarmBoard" });
const siteConfig = useSiteConfig();
const userInfo = getUserInfo();
provide("detailData", toRefs(state).data);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
interface Props {
  width?: number;
  height?: number;
  title?: string;
}

const dateValue = ref(new Date());

const calendarLoading = ref<boolean>(true);

const props = withDefaults(defineProps<Props>(), { title: "告警" });
const dataList = ref<publishItem[]>([]);
const stateRightText = computed(() => {
  if (state.data.rejectAble) return "关闭";
  //if (!state.data.approval) return "已审批";
  switch (state.data.publishState) {
    case publishState.NEW:
      return "新建";

    case publishState.PROCESSING:
      if (state.data.approveState === "UN_APPROVE") return "未审批";
      else if (state.data.approveState === "APPROVED") {
        if (!state.data.operation) {
          return "已审批";
        } else {
          return "处理";
        }
      }

    case publishState.AUTO_CLOSED:
    case publishState.CLOSED:
      return (publishOperateOption.find((v) => v.value === state.data.operation) || {}).label;
    default:
      return "";
  }
});

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

const _width = inject("width", ref(0));
const _height = inject("height", ref(0));

const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);

const tableRef = ref<InstanceType<typeof ElTable>>();
const editorRef = ref<InstanceType<typeof Editor>>();

const verifyPermissionIds = ref<string[]>([]);
provide("verifyPermissionIds", verifyPermissionIds);
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Form {
  priority: string;
}
interface AnyObject {
  [key: string]: any;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const timer = ref<null | NodeJS.Timer>(null);
const autoRefreshTime = ref(0);

enum pageType {
  details = "",
  subtotal = "subtotal",
  device = "device",
  contacts = "contacts",
  related = "related",
  alarm = "alarm",
  sla = "sla",
  strategy = "strategy",
  file = "file",
  dynamics = "dynamics",
  approve = "approve",
  project = "project",
}

const currentEventState = computed(() => find(publishStateOption, (v) => v.value === state.data.publishState) || ({} as Partial<(typeof publishStateOption)[number]>));
const currentPriority = computed(() => find(priorityOption, (v) => v.value === state.data.priority) || ({} as Partial<(typeof priorityOption)[number]>));

// const responseUrgencyType = computed(() => setSlaState((state.data.slaTimeLimit || {}).responseTimeLimits || [], Number(state.data.responseTime) || 0));
// const resolveUrgencyType = computed(() => setSlaState((state.data.slaTimeLimit || {}).completedTimeLimits || [], Number(state.data.resolveTime) || 0));

// function setSlaState(list: TimeLimit[], val: number) {
//   let result = urgencyType.BREACH;
//   list.sort((a, b) => (Number(b.tolerateMinutes) || 0) - (Number(a.tolerateMinutes) || 0)).forEach((el) => ((Number(el.tolerateMinutes) || 0) >= val || list.length === 1) && (result = el.urgencyType));
//   return find(urgencyTypeOption, ({ value }) => value === result) || { label: "致命", value: "BREACH", color: "#ED4013" };
// }

const eventStat = ref<{ label: string; value: publishState; color?: string; count: number }[]>([]);

const form = reactive<Form>({
  priority: "",
});

// const responseTimePercentage = computed(() => (((Number(state.data.responseTime) || 0) / (Number(state.data.responseLimit) || 0)) * 100 > 100 ? 100 : (Number(state.data.responseTime) / Number(state.data.responseLimit)) * 100) || 0);
// const resolveTimePercentage = computed(() => (((Number(state.data.resolveTime) || 0) / (Number(state.data.resolveLimit) || 0)) * 100 > 100 ? 100 : (Number(state.data.resolveTime) / Number(state.data.resolveLimit)) * 100) || 0);

const priorityMatrix = ref<{ eventSeverity: eventSeverity; deviceImportance: deviceImportance; priority: priority; publishType: publishType }[]>([]);
const userGroups = ref<Record<string, any>[]>([]);
const userList = ref<EntrustUserItem[]>([]);
// const typeOfTransferOrUpgrade = ref<"transfer" | "eventUpgrade">("transfer");
type typeTransferOrUpgrade = { userGroupId: string; userId: string };
const transferOrUpgrade = ref<typeTransferOrUpgrade>({} as typeTransferOrUpgrade);
const publishDate = ref<string[]>([] as string[]);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  handleRefresh().then(() => (autoRefreshTime.value = 60));
}

function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {
  if (timer.value !== null) {
    clearInterval(timer.value);
    timer.value = null;
  }
}
function beforeDestroy() {
  if (timer.value !== null) {
    clearInterval(timer.value);
    timer.value = null;
  }
}
function destroyed() {}

watch(autoRefreshTime, (autoRefreshTime) => {
  if (timer.value !== null) timer.value = (clearInterval(timer.value), null);
  if (autoRefreshTime) timer.value = setInterval(queryData, autoRefreshTime * 1000);
});
watch(route, (route) => {
  // // console.log(route);
  queryData();
});

watch(dateValue, (dateValue) => {
  let year = dateValue.getFullYear();
  let month = dateValue.getMonth() + 1;
  let dateTime = year + "-" + month;
  // // console.log(dateTime);
  publishDateList(dateTime);

  // if (timer.value !== null) timer.value = (clearInterval(timer.value), null);
  // if (dateValue) timer.value = setInterval(queryData, dateValue * 1000);
});
watchEffect(() => {
  let year = dateValue.value.getFullYear();
  let month = dateValue.value.getMonth() + 1;
  let dateTime = year + "-" + month;
  // // console.log(dateTime);
  publishDateList(dateTime);
});

/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const operation = ref<publishOperate>("" as publishOperate);
type CompleteQuery = { completeInfo: { finishCodeName: string; finishCodeDesc: string; finishContent: string } };
const completeQuery = ref<CompleteQuery>({} as CompleteQuery);
type NotesQuery = { content: string; privateAble: boolean };
const notesQuery = ref<NotesQuery>({} as NotesQuery);
getMonthPublishList("");

function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == getUserInfo().zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
async function getMonthPublishList(date: string) {
  // console.log(123456789);
  calendarLoading.value = true;
  let year = dateValue.value.getFullYear();
  let month = dateValue.value.getMonth() + 1;
  let day = dateValue.value.getDate();
  let dateTime = year + "-" + month + "-" + day;
  // // console.log(date);

  try {
    const { success, message, data } = await getPublishDateList({ date: date ? date : dateTime });
    if (!success) throw new Error(message);
    dataList.value = data.map((item) => {
      return {
        ...item,
        startTime: item.startTime ? Number(item.startTime) + timeZoneSwitching() : item.startTime,
        endTime: item.endTime ? Number(item.endTime) + timeZoneSwitching() : item.endTime,
      };
    });
    calendarLoading.value = false;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    await queryData();
  }
}

async function publishDateList(date: string) {
  try {
    const { success, message, data } = await getDatePublishList({ date: date });
    if (!success) throw new Error(message);
    // ElMessage.success("操作成功");
    // // console.log(data);
    publishDate.value = data;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    await queryData();
  }
}
async function handleChangeSave() {
  try {
    const params = {
      id: route.params.id,
      operation: operation.value ? operation.value : null,
      digest: state.data.digest,
      priority: state.data.priority,
      urgency: state.data.urgency,
      influence: state.data.influence,

      actorId: state.data.actorId,
      ...transferOrUpgrade.value,
      //  userId: state.data.userId,
      //userGroupId: state.data.userGroupId,
      publishType: state.data.publishType,
      desc: state.data.desc,
      externalId: state.data.externalId,
      startTime: state.data.startTime || null,
      endTime: state.data.endTime || null,
      ...completeQuery.value,
      // completeInfo: JSON.stringify(completeQuery.value) != "{}" ? completeQuery.value : undefined,
    };
    // // console.log(params);
    const { success, message } = await setPublishItemDetail(params);
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    if (hasValidContent(notesQuery.value.content)) {
      submitNote();
    }
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    await handleRefresh();
  }
}
const hasValidContent = (html) => {
  if (html === undefined || html === null) {
    return false;
  }
  // 1. 过滤空标签（包括自闭合标签）
  const cleanedHtml = html.replace(/<([a-z][a-z0-9]*)\b[^>]*>(?:\s|&nbsp;)*<\/\1>|<\w+\s*\/>/gi, "");
  // 2. 移除所有空格（包括换行、制表符等）
  const trimmedContent = cleanedHtml.replace(/\s+/g, "").trim();
  // 3. 返回是否有有效内容
  return trimmedContent.length > 0;
};
async function submitNote() {
  try {
    const formData = new FormData();
    formData.append("nodeContent", notesQuery.value.content);
    formData.append("privateAble", notesQuery.value.privateAble as any);
    formData.append("privateCustomerId", userInfo.tenantId);
    formData.append("tenantId", (userInfo.currentTenant || {}).id as string);
    formData.append("orderType", "PUBLISH");
    formData.append("permissionId", EditNodePermissionId["PUBLISH"]);
    formData.append("orderId", route.params.id as string);
    formData.append("orderIdsJson", JSON.stringify([route.params.id]));
    const { success, message } = await addOrderNode(formData as any);
    if (!success) throw new Error(message);
    //ElMessage.success("操作成功");
    if (modelNotesRef.value) {
      modelNotesRef.value.getEventNotes(); // 安全调用
    }
    handleRefresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    await queryData();
  }
}
enum EditNodePermissionId {
  EVENT_ORDER = 智能事件中心_事件工单_编辑小记,
  SERVICE_REQUEST = 智能事件中心_服务请求工单_编辑小记,
  DICT_EVENT_ORDER = 智能事件中心_DICT事件管理_编辑小记,
  DICT_SERVICE_REQUEST = 智能事件中心_DICT服务请求_编辑小记,
  CHANGE = 智能事件中心_变更工单_编辑小记,
  QUESTION = 智能事件中心_问题工单_编辑小记,
  PUBLISH = 智能事件中心_发布管理_编辑小记,
}

function formatterTable(_row: any, _col: any, v: any) {
  // // console.log(_row, _col, v);
  switch (_col.property) {
    case "publishType":
      // let id = "";

      // return "";
      return publishTypeOption.find((item) => item.value === v)?.label || "--";
    case "createdBy":
      // let id = "";

      // return "";
      return JSON.parse(v)?.username || "--";
  }
}

function handleEventOperateCommand(v: publishState) {
  switch (v) {
    case publishState.PROCESSING:
      // 处理中
      // handleBatchAccept(detailData);
      break;
    // case publishState.COMPLETED:
    //   // 完成
    //   // handleEventEnd(detailData, "Finish");
    //   break;
    case publishState.CLOSED:
      // 关闭
      // handleEventEnd(detailData, "Close");
      break;
  }
}

// async function handleCommand(type: command, data?: Record<string, unknown>) {
//   const time = autoRefreshTime.value;
//   autoRefreshTime.value = 0;
//   try {
//     state.loading = true;
//     await nextTick();
//     switch (type) {
//       case command.Refresh:
//         await resetData();
//         await queryData();
//         break;
//       case command.Request:
//         await queryData();
//         break;
//       case command.Preview:
//         await previewItem(data as Record<string, unknown>);
//         break;
//       case command.Create:
//         await createItem(data as Record<string, unknown>);
//         await resetData();
//         await queryData();
//         break;
//       case command.Update:
//         await rewriteItem(data as Record<string, unknown>);
//         await resetData();
//         await queryData();
//         break;
//       case command.Modify:
//         await modifyItem(data as Record<string, unknown>);
//         await resetData();
//         await queryData();
//         break;
//       case command.Delete:
//         await deleteItem(data as Record<string, unknown>);
//         await resetData();
//         await queryData();
//         break;
//     }
//   } catch (error) {
//     if (error instanceof Error) {
//       const message = error.message;
//       await resetData();
//       await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
//       await queryData();
//     }
//   } finally {
//     autoRefreshTime.value = time;
//     state.loading = false;
//   }
// }
async function handleSetTransferOrUpgrade(req: { userGroupId: string; userId: string; type: "transfer" | "eventUpgrade" }, raw: Partial<DataItem>) {
  const time = autoRefreshTime.value;
  transferOrUpgrade.value.userGroupId = req.userGroupId;
  transferOrUpgrade.value.userId = req.userId;
  try {
    autoRefreshTime.value = 0;
    state.loading = true;
    const { success, data, message } = await questionTransfer({
      id: route.params.id as string,
      userGroupId: req.userGroupId,
      userId: req.userId,
    });
    if (!success) throw new Error(message);
    ElMessage.success(`操作成功`);
    // const { success, message, data } = await setEventDataByTransferOrUpgrade({ id: raw.id as string, ...req });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功${req.type === "eventUpgrade" ? "升级变更" : "转交变更"}`);
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    await queryData();
    autoRefreshTime.value = time;
    state.loading = false;
  }
}
const priorityChanged = ref(false);
async function handleSetPriority(priority: priority, raw: Partial<DataItem>) {
  // const priorityItem = (find(priorityOption, ({ value }) => value === priority) || {}).label || priority;
  // (0 , _views_pages_apis_serviceRequest__WEBPACK_IMPORTED_MODULE_22__.setChangePriority) is not a function
  // if (!editorRef.value) return;
  // const time = autoRefreshTime.value;
  try {
    state.data.priority = priority;
    priorityChanged.value = true;
    // const { success, message, data } = await setChangePriority({ id: raw.id as string, priority });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功修改优先级`);
    // autoRefreshTime.value = 0;
    // state.loading = true;
    // const params = { priority, label: priorityItem, raw };
    // const form = {};
    // await editorRef.value.confirm({ ...form, ...params, $type: "warning", $title: `修改优先级`, $slot: "setPriority" }, async (form: Record<string, unknown>) => {
    // });
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    state.loading = false;
  }
}
const impactChanged = ref(false);
async function handleSetImportance(importance: deviceImportance, raw: Partial<DataItem>) {
  // const importanceItem = (find(deviceImportanceOption, ({ value }) => value === importance) || {}).label || importance;

  // if (!editorRef.value) return;
  // const time = autoRefreshTime.value;
  try {
    state.data.influence = importance;
    impactChanged.value = true;
    setPriority();
    // const { success, message, data } = await setPublishItemImportance({ id: route.params.id as string, importance });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // if (success) {
    //   // // console.log(data);
    //   setPriority();
    //   // state.data.priority = data;
    // }
    // ElMessage.success(`成功修改重要性`);
    // await queryData();
    // autoRefreshTime.value = 0;
    // state.loading = true;
    // const params = { importance, label: importanceItem, raw };
    // const form = {};
    // await editorRef.value.confirm({ ...form, ...params, $type: "warning", $title: `修改重要性`, $slot: "setImportance" }, async (form: Record<string, unknown>) => {
    // });
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    state.loading = false;
  }
}
const urgencyChanged = ref(false);
async function handleSetSeverity(severity: eventSeverity, raw: Partial<DataItem>) {
  try {
    state.data.urgency = severity;
    urgencyChanged.value = true;
    setPriority();
    // // console.log(route.params.id);
    // const { success, message, data } = await setPublishItemEmergent({ id: route.params.id as string, severity });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // // ElMessage.success(`成功修改紧急性`);
    // if (success) {
    //   // state.data.priority = data;
    //   setPriority();
    // }
    // await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    state.loading = false;
  }
}

function setPriority() {
  if (state.data.urgency && state.data.influence) state.data.priority = (find(priorityMatrix.value, (v) => v.eventSeverity === state.data.urgency && v.deviceImportance === state.data.influence) || {}).priority || state.data.priority;
}

async function handleSetPublishType(publishType: publishType, raw: Partial<DataItem>) {
  try {
    state.data.publishType = publishType;
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    state.loading = false;
  }
}
const OperateChanged = ref(false);
async function handleDispose(row: DataItem) {
  try {
    operation.value = publishOperate.DISPOSE;
    OperateChanged.value = true;
    // const params = {
    //   id: route.params.id,
    //   operation: row.operation || null,
    // };

    // const { success, message } = await setPublishItemDetail(params);
    // if (!success) throw new Error(message);
    // ElMessage.success("操作成功");
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    await queryData();
  }
  // try {
  //   const { success, data, message } = await dispose({ id: row.id });
  //   if (!success) throw new Error(message);
  //   ElMessage.success("操作成功");
  // } catch (error) {
  //   error instanceof Error && ElMessage.error(error.message);
  // } finally {
  //   handleRefresh();
  // }
}
async function getContact() {
  const id = state.data.id || route.params.id;
  try {
    const [{ success: contactSuccess, message: contactMessage, data: contactData }, { success: tabsSuccess, message: tabsMessage, data: tabsData }] = await Promise.all([getChangeContacts({ id }), getType({})]);
    if (!contactSuccess) throw Object.assign(new Error(contactMessage), { success: contactSuccess, data: contactData });
    contactsType.value = contactData instanceof Array ? contactData : [];
    if (contactSuccess) {
      const { success, message, data } = await eventBatchdesensitized({ deviceIds: Array.from(contactsType.value.reduce((p, c) => p.add(c.contactId), new Set<string>()).values()) });
      if (!success) throw Object.assign(new Error(message), { success, data });
      contacts.value = data instanceof Array ? data : [];
      const { success: successTenant, message: messageTenant, data: dataTenant } = await getTenantInfo({});
      if (!successTenant) throw Object.assign(new Error(messageTenant), { success: successTenant, dataTenant });
      contacts.value.unshift(dataTenant);
      // 去重方法
      uniqueByLanguage.value = contacts.value.reduce((acc, current) => {
        // 查找当前数组中是否已存在相同 language 的项
        const existingIndex = acc.findIndex((item) => item.language === current.language);

        if (existingIndex === -1) {
          // 如果不存在，直接添加当前项
          acc.push(current);
        } else {
          // 如果已存在，判断当前项是否更优（tenantName 为假）
          const existingItem = acc[existingIndex];
          if (!current.tenantName && existingItem.tenantName) {
            // 替换为更优的当前项（tenantName 为假）
            acc.splice(existingIndex, 1, current);
          }
        }
        return acc;
      }, []);
      // 提取 uniqueByLanguage 中的语言顺序
      const languageOrder = uniqueByLanguage.value.map((item) => item.language);

      // 根据语言顺序对 localesOption 进行排序
      sortedLocalesOption.value.sort((a, b) => {
        const indexA = languageOrder.indexOf(a.value);
        const indexB = languageOrder.indexOf(b.value);
        return indexA - indexB;
      });
    }
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : `${error}`);
  }
}

function isLanguageMatch(value) {
  return uniqueByLanguage.value.some((item) => item.language === value);
}
// 新增方法
function getTooltipContent(language, zhLabel) {
  const matchedItem = uniqueByLanguage.value.find((item) => item.language === language);
  return matchedItem?.tenantName ? `联系人: ${zhLabel}` : `客户: ${zhLabel}`;
}
async function handleRefresh() {
  try {
    state.loading = true;
    await nextTick();
    await resetData();
    await queryData();
    await getTabCount();
    await getContact();
    await resetChangeState();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
async function handleQuery() {
  try {
    state.loading = true;
    await nextTick();
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}

const tabCounts = ref<TabCount>({} as TabCount);
async function getTabCount() {
  const { success, data, message } = await getDetailTapCountById({ id: route.params.id as string });
  if (!success) throw new Error(message);
  tabCounts.value = data;
}

const tickGroupConfig = ref({
  closeDirectly: false,
  isClose: false,
  approval: false, // 是否需要审批
  ticketClassificationNames: [],
});

/**
 * 工单是否可以关闭
 * false 不可以关闭
 * true 可以关闭
 */
const orderIsClose = computed(() => {
  /* 草稿不可以关闭工单 */
  if (state.data.draft) return false;

  if (tickGroupConfig.value.closeDirectly && tickGroupConfig.value.isClose) return true;
  else if ([publishOperate.COMPLETE].includes(state.data.operation as publishOperate) && tickGroupConfig.value.isClose) return true;
  else return false;

  // if ([publishState.NEW].includes(state.data.publishState as publishState)) return false;
  // if (tickGroupConfig.value.closeDirectly && tickGroupConfig.value.isClose) return true;
  // else if ([publishOperate.COMPLETE].includes(state.data.operation as publishOperate) && tickGroupConfig.value.isClose) return true;
  // else return false;
  // if (![publishState.CLOSED].includes((state.data.publishState || "") as publishState) && tickGroupConfig.value.closeDirectly && tickGroupConfig.value.isClose) return true;
  // // eslint-disable-next-line no-constant-condition
  // // else if ((state.data.operation == "COMPLETE" ? false : true || state.data.approveState == "UN_APPROVE" ? true : false) && tickGroupConfig.value.isClose) return true;
  // /* CLOSED CLOSE  */ else if ([publishState.CLOSED].includes((state.data.publishState || "") as publishState) && ["COMPLETE"].includes(state.data.operation || "") && tickGroupConfig.value.isClose) return true;
  // else return false;
});

/**
 * @return false 可以完成 true 不可以完成
 */
const orderIsCompleted = computed(() => {
  if (!tickGroupConfig.value.approval) return false;
  else return [publishState.AUTO_CLOSED, publishState.CLOSED, publishState.NEW].includes((state.data.publishState as publishState) || ("" as publishState)) || state.data.approveState == "UN_APPROVE" ? true : false;
});

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
async function getTackerGroupConfig() {
  try {
    const [
      /* 是否直接关闭 */
      { data: closeDirectlyData, message: closeDirectlyMessage, success: closeDirectlySuccess },
      { data: isCloseData, message: isCloseMessage, success: isCloseSuccess },
      { data: templateData, message: templateMessage, success: templateSuccess },
      ticketClassificationNames,
    ] = await Promise.all([
      /* 获取是否直接关闭 */
      getCloseDirectly({ tenantId: (userInfo.currentTenant || {}).id, ticketTemplateId: (state.data as any).ticketTemplateId }),
      getOrderUserGroupIsClose({ tenantId: (userInfo.currentTenant || {}).id, type: AssignableTicketType.question, userId: userInfo.userId, ticketTemplateId: (state.data as any).ticketTemplateId }),
      getticketConfigurations({ ticketTemplatesId: (state.data as any).ticketTemplateId }),
      getTicketClassificationNames(AssignableTicketType.publish, (state.data as any).ticketClassificationId, (state.data as any).ticketTemplateId),
    ]);
    if (!closeDirectlySuccess) throw new Error(closeDirectlyMessage);
    if (!isCloseSuccess) throw new Error(isCloseMessage);
    tickGroupConfig.value.approval = templateData.approval;
    tickGroupConfig.value.closeDirectly = closeDirectlyData;
    tickGroupConfig.value.isClose = isCloseData;
    tickGroupConfig.value.ticketClassificationNames = ticketClassificationNames;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

async function queryData() {
  try {
    const [
      /*  */
      { success, message, data },
      { success: prioritySuccess, message: priorityMessage, data: priorityData },
    ] = await Promise.all([
      /*  */
      getItemData({ id: route.params.id as string }),
      getPriorityMatrixList({}),

      // getGroupList({ appId: (siteConfig.baseInfo || {}).app, external: true }),
    ]);
    if (!success) throw Object.assign(new Error(message), { success, data });
    if (!prioritySuccess) throw Object.assign(new Error(priorityMessage), { success: prioritySuccess, data: priorityData });
    const { success: userGroupSuccess, message: userGroupMessage, data: userGroupData } = await getOrderUserGroup({ tenantId: (userInfo.currentTenant || {}).id, type: AssignableTicketType.publish, ticketTemplateId: (data as any).ticketTemplateId });
    if (!userGroupSuccess) throw Object.assign(new Error(userGroupMessage), { success: userGroupSuccess, data: userGroupData });

    await (async (req?: UserGroupConfigurationItem) => {
      try {
        if (!req) return;
        const { success, message, data: res } = await getUserByGroup({ id: req.userGrouptId as string });
        if (!success) throw Object.assign(new Error(message), { success, data: res });
        userList.value = res;
      } catch (error) {
        if (error instanceof Error) ElMessage.error(message);
      }
    })(find(userGroupData, (v) => v.userGrouptId === (data.userGroupId || data.displayUserGroupId)));
    // priorityMatrix.value = priorityData.items;
    // userGroups.value = userGroupData;

    priorityMatrix.value = priorityData.priorityMatrixItems.map((v) => {
      return {
        eventSeverity: v.urgency,
        deviceImportance: v.influence,
        priority: v.priority,
      };
    });
    // userGroups.value = userGroupData;
    userGroups.value = userGroupData.map((v) => ({ id: v.userGrouptId, name: v.userGroupName, tenantAbbreviation: v.abbreviation }));

    setTimeout(() => {
      transferOrUpgrade.value.userGroupId = (find(userGroups.value, (v) => v.id === (state.data.userGroupId || state.data.displayUserGroupId)) || {}).id as string;
      if (state.data.userGroupId == null) transferOrUpgrade.value.userId = (find(userList.value, (v) => v.id === state.data.actorId) || {}).id as string;
    }, 0);
    state.data = data;

    verifyPermissionIds.value = data.verifyPermissionIds || [];

    getTackerGroupConfig();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  }
}
async function createItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await addItemData({ ...form });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`成功确认告警`);
  });
}
// async function previewItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setItemData({ ids: form.ids as string[], ...form });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
// async function rewriteItem(row: Record<string, unknown>) {
//   const title = (find(priorityOption, ({ value }) => value === row.priority) || {}).label || row.priority;
//   if (!editorRef.value) return row;
//   const params = { select: (<Item[]>row.select).filter((v) => row.priority !== v.priority) };
//   await editorRef.value.confirm({ ...row, ...params, $type: "warning", $title: `批量${title}`, $slot: "batchConfirm" }, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setEventDataByPriority({ id: (<Item[]>form.select).map((v) => v.id), priority: form.priority as priority });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`成功${form.$title}`);
//   });
// }
// async function modifyItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setItemData({ ids: form.ids as string[], ...form });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
// async function deleteItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await delItemData(form);
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
function handleCancel() {
  if ("fallback" in route.query && typeof route.query.fallback === "string") router.push({ name: route.query.fallback, params: { id: route.query.deviceId }, query: { type: route.query.status } });
  else if (history.length === 1) window.close();
  else router.back();
}

async function handleAccept(row: Partial<DataItem>) /* 接手 */ {
  try {
    state.loading = true;
    // serviceRequestId: string | number;
    // tenantId: string | number;
    // const { success, message, data } = await setchangeStateProcessing({ id: row.id as string });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`操作成功`);
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    await queryData();
    state.loading = false;
  }
}
const digestChanged = ref(false);
async function handleEditSummary() {
  ElMessageBox.prompt("请输入摘要", "编辑摘要", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputValue: state.data.digest,
    inputValidator: (value: string) => {
      return !!value;
    },
    inputErrorMessage: "请输入摘要",
    beforeClose: async (action, instance, done) => {
      try {
        if (action === "confirm") {
          state.data.digest = instance.inputValue;
          digestChanged.value = true;
          // const { success, data, message } = await editDigest({ id: route.params.id as string, digest: instance.inputValue });
          // if (!success) throw new Error(message);
          // ElMessage.success("操作成功");
          // handleRefresh();
          done();
        } else done();
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    },
  })
    .then(() => {
      /* code */
    })
    .catch(() => {
      /* code */
    });
}
const endRef = ref<InstanceType<typeof EventEnd>>();
async function handleClose(row: DataItem, operateType: publishOperate, api: any) {
  try {
    if (!endRef.value) return false;
    endRef.value.open(row, operateType, api);
    OperateChanged.value = true;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    // handleRefresh();
  }
}
async function handleAlarm(row: DataItem, operateType: publishOperate, api: any) {
  try {
    if (!endRef.value) return false;
    endRef.value.open(row, operateType, api);
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    // handleRefresh();
  }
}
const userGroupChanged = ref(false);
async function handleChangeUserGroup(id: string) {
  try {
    transferOrUpgrade.value.userGroupId = id;
    userGroupChanged.value = true;
    await (async (req: { userGroup: string }) => {
      try {
        if (!req.userGroup) return;
        const { success, message, data: res } = await getUserByGroup({ id: req.userGroup });
        if (!success) throw Object.assign(new Error(message), { success, data: res });
        userList.value = res;
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      }
    })({ userGroup: id });
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    // handleRefresh();
  }
}
const userChanged = ref(false);
async function handleChangeUser(id: string) {
  try {
    transferOrUpgrade.value.userId = id;
    userChanged.value = true;
    // const { success, data, message } = await changeUser({ id: route.params.id as string, userId: id });
    // if (!success) throw new Error(message);
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    // handleRefresh();
  }
}
async function resetChangeState() {
  priorityChanged.value = false;
  impactChanged.value = false;
  urgencyChanged.value = false;
  OperateChanged.value = false;
  digestChanged.value = false;
  userGroupChanged.value = false;
  userChanged.value = false;
}
// const endRef = ref<InstanceType<typeof EventEnd>>();
// async function handleEnd(data: Partial<DataItem>, type: string) {
//   if (!endRef.value) return false;
//   endRef.value.open(data, type);
// }

// const pendRef = ref<InstanceType<typeof EventPend>>();
// async function handleApprove(row: Partial<DataItem>) /* 挂起 */ {
//   if (!pendRef.value) return false;
//   pendRef.value.open(state.data);
// }
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
defineSlots<{}>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
:deep(.el-calendar-day) {
  padding: 0;
  .calendar-div {
    width: 100%;
    height: 100%;
    padding: 8px;
    box-sizing: border-box;
  }
}
.on {
  display: flex;
  color: rgb(64, 158, 255);
  justify-content: center;
}
:deep(.calendar) {
  display: flex;
  justify-content: space-between;
  .el-calendar {
    width: 400px;

    .el-calendar__body {
      .el-calendar-table {
        tbody {
          .el-calendar-table__row {
            td {
              height: 60px;
              // line-height: 30px;
              text-align: center;
              // display: flex;
              // align-items: center;
              > div {
                height: 100%;
              }
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
@import "@/styles/theme/common/var.scss";
.draft-tooltip {
  background: $color-danger !important;
  border: 1px solid $color-danger;
  color: #fff;
}

.draft-tooltip .el-popper__arrow::before {
  background: $color-danger !important;
  border: 1px solid $color-danger;
}
</style>
