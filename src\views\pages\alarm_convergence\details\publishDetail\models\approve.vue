<template>
  <pageTemplate :height="height - 20" :show-paging="false">
    <template #right v-if="userInfo.hasPermission(智能事件中心_发布工单_分配用户组)">
      <el-button :icon="Plus" type="primary" @click="handleCreateApprove" :disabled="!userInfo.hasPermission(智能事件中心_发布工单_分配用户组) || [publishState.AUTO_CLOSED, publishState.CLOSED, publishState.NEW].includes((data.publishState as publishState) || ('' as publishState))">创建审批</el-button>
    </template>
    <template #default>
      <el-table v-loading="state.loading" :data="state.data" :height="height - 60" :style="{ width: `${width}px`, margin: '0 auto' }">
        <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :min-width="column.width || 120" :showOverflowTooltip="column.showOverflowTooltip" :formatter="column.formatter" />
        <el-table-column :label="t('glob.operate')" align="left" header-align="left" :width="110" fixed="right">
          <template #default="{ row }">
            <el-button link type="danger" size="small" @click="delItem({ ...row })" :disabled="!userInfo.hasPermission('612916552185937920') || [publishState.AUTO_CLOSED, publishState.CLOSED, publishState.NEW].includes((data.publishState as publishState) || ('' as publishState))">移除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <createApprove ref="createApproveRef" :publishType="props.data.publishType" :ticketTemplateId="props.data.ticketTemplateId" :existingUserGroup="existingUserGroup">
        <template #del="{ params }">
          <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
            <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
            <p class="title">
              确定移除用户组
              <span>{{ ((params || {}) as anyObj).teamName as string }}</span>
              吗？
            </p>
          </div>
        </template>
        <template #approve="{ params }">
          <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
            <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
            <p class="title">
              确定{{ params.$title }}
              <span>{{ ((params || {}) as anyObj).teamName as string }}</span>
              吗？
            </p>
          </div>
        </template>
      </createApprove>
    </template>
  </pageTemplate>
</template>

<script setup lang="ts" name="change-approve">
import { publishState } from "@/views/pages/apis/publish";
import pageTemplate from "@/components/pageTemplate.vue";
import createApprove from "./createApprove.vue";
import { Item } from "../helper";
import { sizes } from "@/utils/common";
import { inject, reactive, ref, toRefs, onMounted, h, getCurrentInstance, computed } from "vue";
import type { ApproveItem as DataItem } from "@/views/pages/apis/change";
import { type TableColumnCtx, type Action, ElMessage, ElMessageBox, ElButton, ElTag } from "element-plus";
import { Plus, Delete, InfoFilled } from "@element-plus/icons-vue";

import { getApproves as getData, addPublishApprove as addData, delPublishApprove as delData, publishApproveRefuse as refuse, publishApprovePass as pass, approveStateOption, ApproveState } from "@/views/pages/apis/publish";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import { timeFormat } from "@/utils/date";
import getUserInfo from "@/utils/getUserInfo";
import { 智能事件中心_发布工单_分配用户组 } from "@/views/pages/permission";
const userInfo = getUserInfo();
const { t } = useI18n();
const route = useRoute();

const { proxy } = getCurrentInstance()!;
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);
function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == getUserInfo().zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}

interface Props {
  height: number;
  data: Partial<Item>;
  refresh: () => Promise<void>;
}
const width = inject("width", ref(0));
const props = withDefaults(defineProps<Props>(), {
  height: 0,
  data: () => <Partial<Item>>{},
});

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: TableColumnCtx<DataItem>, value: T[keyof T]) => string | import("vue").VNode; showOverflowTooltip?: boolean }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<DataItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  column: [
    { key: "teamName", label: "用户组" },
    {
      key: "approveState",
      label: "状态",
      formatter: (_row, _col, v) => {
        const flag = approveStateOption.find(({ value }) => value === v);
        return h(ElTag, { effect: "dark", round: true, type: (flag || {}).type }, ((flag || {}).label as string) || "--");
      },
    },
    {
      key: "hasAuthority",
      label: "审批",
      formatter: (_row, _col, v) => {
        // console.log(v, !userInfo.hasPermission('612916552185937920'));
        if (_row.approveState !== ApproveState.SUSPEND) return "";
        if (props.data.publishState == "CLOSED") return "";
        return h("div", {}, [
          h(
            ElButton,
            {
              type: "",
              size: "small",
              disabled: !v,
              onClick: () => handlePass(_row),
            },
            "通过"
          ),
          h(
            ElButton,
            {
              type: "",
              size: "small",
              disabled: !v,
              onClick: () => handleRefuse(_row),
            },
            "拒绝"
          ),
        ]);
      },
    },
    {
      key: "updatedBy",
      label: "修改人",
      formatter: (_row, _col, v) => {
        return JSON.parse(v as string)?.username || JSON.parse(_row.createdBy as string)?.username || "--";
      },
    },
    {
      key: "updatedTime",
      label: "修改时间",
      formatter: (_row, _col, v) => {
        return timeFormat(Number(v));
      },
    },
  ],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

const { height, data: detail } = toRefs(props);

const createApproveRef = ref<InstanceType<typeof createApprove>>();
async function handleCreateApprove() {
  if (!createApproveRef.value) return false;
  await createApproveRef.value.open({ data: state.data }, async (form: Record<string, unknown>) => {
    const { success, message } = await addData({ id: route.params.id as string, teamId: form.teamId as string });
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    await queryItem();
  });
}

const existingUserGroup: string[] = computed(() => {
  const userGroups: any = [];
  state.data.forEach((v: any) => userGroups.push(...(v.userGroups as any)));
  return Array.from(new Set(userGroups.map((v) => v.id))) as string[];
});

async function queryItem() {
  try {
    const { success, data, message, page, size, total } = await getData({ id: route.params.id as string, pageNumber: state.page, pageSize: state.size });
    if (!success) throw new Error(message);
    // state.data = data.map((v) => ({ ...v, teamName: v.userGroups.map((m) => m.name).join() }));
    state.data = data.map((v) => ({ ...v, teamName: v.userGroups.map((m) => m.name).join(), updatedTime: Number(v.updatedTime) + timeZoneSwitching() }));

    state.page = page;
    state.size = size;
    state.total = total;
    props.refresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
    state.page = 1;
    state.size = 20;
    state.total = 0;
  }
}

async function delItem(row: DataItem) {
  if (!createApproveRef.value) return;
  try {
    state.loading = true;
    const params = { ...row };
    const form = {};
    await createApproveRef.value.confirm({ ...form, ...params, $title: `移除审批`, $slot: "del" }, async (form: Record<string, unknown>) => {
      const { success, message, data } = await delData({ id: route.params.id as string, approveId: row.id });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`成功${form.$title}`);
    });
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
    await queryItem();
  }
}

async function handleRefuse(row: DataItem) {
  if (!createApproveRef.value) return;
  try {
    state.loading = true;
    const params = { ...row };
    const form = {};
    await createApproveRef.value.confirm({ ...form, ...params, $title: `拒绝`, $slot: "approve" }, async (form: Record<string, unknown>) => {
      const { success, message, data } = await refuse({ id: route.params.id as string, approveId: row.id });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`${form.$title}成功`);
    });
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
    await queryItem();
    props.refresh();
  }
}

async function handlePass(row: DataItem) {
  if (!createApproveRef.value) return;
  try {
    state.loading = true;
    const params = { ...row };
    const form = {};
    await createApproveRef.value.confirm({ ...form, ...params, $title: `通过`, $slot: "approve" }, async (form: Record<string, unknown>) => {
      const { success, message, data } = await pass({ id: route.params.id as string, approveId: row.id });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`${form.$title}成功`);
    });
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
    await queryItem();
    props.refresh();
  }
}

onMounted(() => {
  queryItem();
});
</script>
