<template>
  <!-- 对话框表单 -->
  <component :is="ComponentRender" v-model:visible="data.visible" :handle-cancel="handleCancel">
    <template #header>
      <!-- {{ `${$params.id ? t("glob.edit") : t("glob.add")}${props.title}` }} -->
      {{ `添加${props.title}` }}
    </template>
    <template #default>
      <FormModel ref="formRef" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @submit="handleFinish">
        <template #default="{}">
          <FormItem :span="24" :label="`选择类型`" tooltip="" prop="contactType" :rules="[]">
            <el-select v-model="form.contactType" style="width: 300px" placeholder="请选择类型">
              <el-option v-for="item in $params.types as Record<string, string>[]" :key="`type-${item.code}`" :label="item.cnName" :value="item.code" />
            </el-select>
          </FormItem>
          <FormItem :span="24" :label="`选择客户`" tooltip="" prop="tenantId" :rules="[{ required: true, message: `请选择客户`, trigger: ['change'] }]">
            <el-select v-model="form.tenantId" style="width: 300px" placeholder="请选择客户" @change="searchContactlist(form.tenantId)" filterable>
              <el-option style="width: 300px" v-for="item in tenants" :key="item.id" :label="`${item.name} [${item.abbreviation}]`" :value="item.id"></el-option>
            </el-select>
          </FormItem>
          <FormItem :span="24" :label="`选择联系人`" tooltip="" prop="contactId" :rules="[{ required: true, message: `请选择选择联系人`, trigger: ['change'] }]">
            <el-select v-model="form.contactId" style="width: 300px" placeholder="请选择选择联系人" multiple>
              <el-option :class="item.email && item.mobilePhone ? 'select_item' : item.email || item.mobilePhone ? 'select_item_once' : ''" v-for="(item, i) in contactOptions.filter((v) => !activeKeys.includes(v.id))" :key="`contact-${item.id}`" :label="item.name" :value="item.id">
                <p>{{ item.name }}</p>
                <p v-if="item.email" style="color: #8492a6; font-size: 13px; display: block; margin-top: -10px; height: 20px">{{ item.email }}</p>
                <p v-if="item.mobilePhone && item.email" style="color: #8492a6; font-size: 13px; display: block; height: 20px">{{ item.mobilePhone }}</p>
                <p v-else style="color: #8492a6; font-size: 13px; display: block; margin-top: -10px; height: 20px">{{ item.mobilePhone }}</p>
              </el-option>
            </el-select>
          </FormItem>
        </template>
      </FormModel>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">确认</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </component>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, computed, h, createVNode, renderSlot, toValue } from "vue";
import { useFileDialog } from "@vueuse/core";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import { buildTypeHelper } from "@/utils/type";

import { buildValidatorData } from "@/utils/validate";

import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import { useRoute } from "vue-router";

import { getContactsQuery, getTenantIdContact, getContactTypes as getType, type ContactsTypeItem, type ContactsItem } from "@/views/pages/apis/contacts";
import { getUserTenants, type TenantItem } from "@/api/system";
import getUserInfo from "@/utils/getUserInfo";
// import { getAlertStat } from "@/views/pages/apis/event";

// const { open, reset, onChange, files } = useFileDialog({});
// onChange((files) => {
//   form.value.files = Array.from(files || []);
// });
const route = useRoute();
const { t } = useI18n();
const formRef = ref<InstanceType<typeof FormModel>>();

interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
  tabs: ContactsTypeItem[];
  types: { contactId: string; contactType: string }[];
  contacts: ContactsItem[];
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 120,
  tabs: () => [],
  types: () => [],
  contacts: () => [],
});
const activeKeys = computed(() => props.types.filter((v) => v.contactType === toValue(form).contactType).map((v) => v.contactId));

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Item {
  id: string;
  contactType: string;
  contactId: string[];
  tenantId: string;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction?: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  id: buildTypeHelper<Item["id"]>(route.params.id as string),
  contactType: buildTypeHelper<Item["contactType"]>(""),
  contactId: buildTypeHelper<Item["contactId"]>([]),
  tenantId: buildTypeHelper<Item["tenantId"]>(""),
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  contactOptions.value = [];
  if (params) {
    return;
  }
}

/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  // await getContactOptions();
  const { success, data, message } = await getUserTenants({ token: getUserInfo().token });
  if (!success) throw new Error(message);
  tenants.value = data instanceof Array ? data : [];

  form.value.tenantId = (getUserInfo().currentTenant || {}).id || "";

  searchContactlist(form.value.tenantId);
  // if ($params.value.ids) {
  //   const { success, message, data } = await getAlertStat({ ids: params.ids as string[] });
  //   if (!success) throw new Error(message);
  //   $params.value = {
  //     ids: $params.value.ids,
  //     ...data,
  //   };
  // }
}
async function searchContactlist(tenantId) {
  const { success, data, message } = await getTenantIdContact({ id: tenantId });
  if (!success) throw new Error(message);
  contactOptions.value = data instanceof Array ? data : [];
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  return form;
}

// const contactOptions = ref<ContactsItem[]>();
const contactOptions = ref<Record<string, unknown>[]>([]);
const tenants = ref<TenantItem[]>([]);
async function getContactOptions() {
  const { success, data, message } = await getContactsQuery({ pageNumber: 1, pageSize: 99999 });
  if (!success) throw new Error(message);
  let valuesArray1 = data.reduce(function (a, c) {
    a[c.id] = c.id;
    return a;
  }, {});
  let valuesArray2 = $params.value.contactsArr.reduce(function (a, c) {
    a[c.id] = c.id;
    return a;
  }, {});
  var result = data
    .filter(function (c) {
      return !valuesArray2[c.id];
    })
    .concat(
      $params.value.contactsArr.filter(function (c) {
        return !valuesArray1[c.id];
      })
    );
  let arr = [];
  arr = $params.value.contactsArr.filter((itemB: any) => {
    return !itemB.contactType.includes($params.value.contactType);
  });
  // // console.log(arr);

  contactOptions.value = [...arr, ...result];
}

/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
// const validator = (_rule: unknown, value: unknown, callback: (error?: string | Error | undefined) => void): void => {
//   callback(form.value.files instanceof Array ? (form.value.files.length > 0 ? void 0 : new Error("请选择上传文件")) : new Error("请选择上传文件"));
// };
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 窗口方法
 */
interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<Item>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Record<string, unknown>) => void;
  reject?: (value: Error) => void;
  callback?: (form: Record<string, unknown>) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Record<string, unknown>>({
  types: <ContactsTypeItem[]>[],
});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(function (formResult, [key, util]) {
    if (!util) return formResult;
    else if (util.test(formResult[key])) return formResult;
    else
      return Object.assign(formResult, {
        [key]: util.transfer(formResult[key], util.value as never),
      });
  }, form as Required<Item>);
}

async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => (formRef.value ? formRef.value.validate(resolve) : resolve(false)));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();

defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, callback?: (form: Record<string, unknown>) => Promise<void>) {
    if (data.visible) {
      return await new Promise((resolve) => {
        ElMessage.warning("先关闭其他弹窗再重试！");
        resolve(params);
      });
    } else {
      $params.value = cloneDeep(params);
      data.visible = true;
      data.loading = true;
      data.submitLoading = true;
      data.callback = callback;
      try {
        return await new Promise((resolve, reject) => {
          data.resolve = resolve;
          data.reject = reject;
          nextTick(async () => {
            await nextTick();
            try {
              await runningInit($params.value);
            } catch (error) {
              if (error instanceof Error) ElMessage.error(error.message);
              handleCancel();
            }
            handleReset();
            data.loading = false;
            data.submitLoading = false;
          });
        });
      } catch (error) {
        return error;
      }
    }
  },
  async confirm<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.confirm(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
});
</script>

<style scoped lang="scss">
.select_item {
  min-height: 30px !important;
  height: 80px !important;
  // line-height: 25px;
  font-size: 12px;
}
.select_item_once {
  height: 60px !important;
}
.edit_sla_config {
  :deep() {
    .el-input-number {
      width: 50px !important;

      .elstyle-input {
        width: 50px;

        input {
          padding: 0;
        }
      }
    }

    .el-input-number__increase {
      display: none;
    }

    .el-input-number__decrease {
      display: none;
    }

    .el-card {
      margin-top: 20px;
    }

    .el-form-item__content .el-form-item-content {
      display: flex;
      flex-direction: column;
    }

    .el-table .cell {
      padding: 0 !important;
    }

    .el-table .el-table__cell {
      padding: 0 !important;
      height: 50px;
    }
  }
}
.priority-icon {
  width: 10px;
  height: 10px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 10px;
}
.state {
  padding: 2px 10px;
  box-sizing: border-box;
  border-radius: 20px;
}
.modules-item {
  .modules-title {
    color: rgba(32, 128, 255, 1);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}
.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}
</style>
