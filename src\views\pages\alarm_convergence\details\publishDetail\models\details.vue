<template>
  <el-form :model="form" label-position="top" v-show="userInfo.hasPermission('520408475474329600') || userInfo.hasPermission('777393341078700032')">
    <el-form-item>
      <template #label>
        <div class="info-desc">
          <div>
            <span v-if="timestampChanged" style="color: red">*</span>
            <span>发布时间</span>
          </div>
          <el-button style="z-index: 1" type="primary">{{ (find(publishTypeOption, (v) => v.value === props.data.publishType) || {}).label }}</el-button>
        </div>
      </template>
      <!-- <el-date-picker :model-value="startEndTime"
      @update:model-value="($event) => handleChangeTime($event)"
      type="datetimerange" range-separator="到" start-placeholder="开始时间" end-placeholder="结束时间" /> -->
      <el-date-picker
        v-model="startTime"
        type="datetime"
        placeholder="选择开始时间"
        @update:model-value="($event) => handleChangeTime($event, 'start')"
        format="YYYY-MM-DD HH:mm"
        :disabled-date="
          (date: any) => {
            if (endTime) {
              if (date >= endTime - 8.64e7) {
                return true;
              } else {
                return false;
              }
            } else {
              return false;
            }
          }
        "
        :disabled="[publishState.AUTO_CLOSED, publishState.CLOSED, publishState.NEW].includes((data.publishState as publishState) || ('' as publishState))"
      >
      </el-date-picker>

      <el-date-picker
        :default-time="new Date(0, 0, 0, 23, 59, 59)"
        style="margin-left: 30px"
        v-model="endTime"
        type="datetime"
        format="YYYY-MM-DD HH:mm"
        placeholder="选择结束时间"
        @update:model-value="($event) => handleChangeTime($event, 'end')"
        :disabled-date="
          (date: any) => {
            if (date <= startTime - 8.64e7) {
              return true;
            } else {
              return false;
            }
          }
        "
        :disabled="[publishState.AUTO_CLOSED, publishState.CLOSED, publishState.NEW].includes((data.publishState as publishState) || ('' as publishState))"
      >
      </el-date-picker>
    </el-form-item>
    <el-form-item>
      <template #label>
        <div class="info-desc">
          <span>描述</span>
          <el-button style="z-index: 1" type="primary" @click="handleDescEdit" :disabled="(!verifyPermissionIds.includes('612916482183004160') && !verifyPermissionIds.includes('777393423094120448')) || [publishState.AUTO_CLOSED, publishState.CLOSED, publishState.NEW].includes((data.publishState as publishState) || ('' as publishState))">{{ isEdit ? $t("generalDetails.Save") : $t("generalDetails.Edit") }}</el-button>
        </div>
      </template>
      <div class="tw-flex tw-min-h-[250px] tw-w-full tw-flex-col" v-if="isEdit" @keyup.enter.stop>
        <!-- <QuillEditor theme="snow" style="flex: 1" :content="isEdit ? form.description : props.data.desc" @update:content="form.description = $event" contentType="html" toolbar="full" :enable="isEdit" :read-only="!isEdit"></QuillEditor> -->
        <el-input v-model="form.description" :rows="6" type="textarea" placeholder="请输入描述" />
      </div>
      <div class="el-input el-input__wrapper tw-flex tw-min-h-[120px] tw-w-full tw-flex-col tw-items-start tw-justify-start tw-p-4" v-else @keyup.enter.stop>
        <div v-html="props.data.desc"></div>
      </div>
    </el-form-item>
    <el-form-item :label="$t('generalDetails.External ID')">
      <template #label>
        <div class="info-desc">
          <span> {{ $t("generalDetails.External ID") }}</span>
          <el-button style="z-index: 1" type="primary" :disabled="(!verifyPermissionIds.includes('612916482183004160') && !verifyPermissionIds.includes('777393423094120448')) || [publishState.AUTO_CLOSED, publishState.CLOSED, publishState.NEW].includes((data.publishState as publishState) || ('' as publishState))" @click="handleExternal">{{ isExternal ? $t("generalDetails.Save") : $t("generalDetails.Edit") }}</el-button>
        </div>
      </template>
      <el-input :disabled="!isExternal" :model-value="isExternal ? form.externalId : props.data.externalId" @update:model-value="form.externalId = $event"></el-input>
    </el-form-item>
    <el-form-item>
      <el-row class="el-input el-input__wrapper">
        <el-col :span="12" class="tw-h-[calc(var(--el-component-size)*3)] tw-text-left">
          <p>修改</p>
          <p class="tw-h-[var(--el-component-size)]">
            <el-icon :size="16" class="tw-mr-[6px] tw-align-middle"><UserFilled /></el-icon>{{ updated.name || "--" }}
          </p>
          <p>{{ updated.updateTime ? moment(`${updated.updateTime}`, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</p>
        </el-col>
        <el-col :span="12" class="tw-h-[calc(var(--el-component-size)*3)] tw-text-right">
          <p>创建</p>
          <p class="tw-h-[var(--el-component-size)]">
            <el-icon :size="16" class="tw-mr-[6px] tw-align-middle"><UserFilled /></el-icon>{{ collector.name || "--" }}
          </p>
          <p>{{ collector.collectTime ? moment(`${collector.collectTime}`, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</p>
        </el-col>
      </el-row>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, watch, inject } from "vue";
import { useRoute, useRouter } from "vue-router";

import { ElMessage } from "element-plus";

import { UserFilled } from "@element-plus/icons-vue";
import { publishType, publishTypeOption, setPublishItemDetail, publishState, setPublishItemDesc } from "@/views/pages/apis/publish";

import { QuillEditor } from "@vueup/vue-quill";
import moment from "moment";
import { find } from "lodash-es";

import { timeFormat } from "@/utils/date";
import { format } from "path";

import getUserInfo from "@/utils/getUserInfo";
const userInfo = getUserInfo();
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);

const verifyPermissionIds = inject("verifyPermissionIds") as string[];

defineOptions({ name: "ModelDetails" });

const props = withDefaults(defineProps<{ data: Partial<import("../helper").DataItem>; height: number; refresh: () => Promise<void> }>(), { data: () => ({}) });
const emits = defineEmits(["changeDate", "changeDesc"]);

const route = useRoute();
const router = useRouter();

const form = ref({ description: "", externalId: "" });
const isEdit = ref(false);
const isExternal = ref(false);

const updated = reactive({ name: "", updateTime: 0 });
const collector = reactive({ name: "", collectTime: 0 });

const startEndTime = ref<[string, string]>(["", ""]);
const startTime = ref();
const endTime = ref();
const timestampChanged = ref(false);
function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == userInfo.zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
watch(
  () => props.data,
  async (data) => {
    timestampChanged.value = false;
    await nextTick();
    if (data.createdBy) collector.name = JSON.parse(data.createdBy).username;
    collector.collectTime = Math.max(Number(data.createdTime) || 0, 0) + timeZoneSwitching();
    try {
      updated.name = JSON.parse(data.updatedBy || "{}").username || "";
    } catch (error) {
      updated.name = "";
    }
    if (data.startTime && data.endTime) (startTime.value = Number(data.startTime) + timeZoneSwitching()), (endTime.value = Number(data.endTime) + timeZoneSwitching());
    // console.log(startTime.value, endTime.value);
    updated.updateTime = Math.max(Number(data.updatedTime) || 0, 0) + timeZoneSwitching();
  },
  { immediate: true }
);
async function handleExternal() {
  if (isExternal.value) {
    emits("changeDesc", form.value.description);
    isExternal.value = false;
    try {
      const { success, message } = await setPublishItemDesc({ id: props.data.id as string, description: form.value.description, externalId: form.value.externalId });
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      props.refresh();
    }
  } else {
    form.value.description = form.value.description || props.data.desc || "";
    form.value.externalId = props.data.externalId || "";
    isExternal.value = true;
  }
}
async function handleDescEdit() {
  if (isEdit.value) {
    emits("changeDesc", form.value.description);
    isEdit.value = false;
    try {
      const { success, message } = await setPublishItemDesc({ id: props.data.id as string, description: form.value.description, externalId: form.value.externalId });
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      props.refresh();
    }
  } else {
    isEdit.value = true;
    form.value.description = form.value.description || props.data.desc || "";
    form.value.externalId = props.data.externalId || "";
  }
}
async function handleChangeTime(v: any, type: string) {
  try {
    timestampChanged.value = true;
    let start = new Date(startTime.value);
    let end = new Date(endTime.value);

    // if (type === "start") {
    //   startTime = v.getTime();
    // } else {
    //   endTime = v.getTime();
    // }
    // console.log(start, end);
    // format();
    // startEndTime.value = [timeFormat(v[0].getTime()), timeFormat(v[1].getTime())];

    emits("changeDate", { startTime: start?.getTime(), endTime: end !== "" ? end.getTime() : "" });
    // const { success, message } = await setChangeTime({ id: route.params.id as string, startTime: v[0].getTime().toString(), endTime: v[1].getTime().toString() });
    // if (!success) throw new Error(message);
    // props.refresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}
</script>

<style lang="scss" scoped>
.info-desc {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
