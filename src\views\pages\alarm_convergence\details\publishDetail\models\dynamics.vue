<template>
  <el-table stripe :data="tableData || []" :height="550">
    <el-table-column prop="dynamicCreateTime" label="时间" width="300">
      <template #default="{ row }">{{ moment(row.dynamicCreateTime, "x").format("YYYY-MM-DD HH:mm:ss") }}</template>
    </el-table-column>
    <el-table-column prop="dynamicContent" label="动态">
      <template #default="{ row }">
        <div v-if="!row.eventTransferName">
          {{ row.eventCreatorName }}
          {{ row.eventTransferName }}
          {{ row.dynamicContent }}
        </div>
        <div v-else>
          {{ row.eventCreatorName }}
          {{ row.dynamicContent }}
          {{ row.eventTransferName }}
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script lang="ts" setup>
import moment from "moment";
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";
import { ElMessage } from "element-plus";
import { getPublishTrendList, type Dynamic } from "@/views/pages/apis/eventManageTrends";
import { useRoute } from "vue-router";
defineOptions({ name: "ModelDynamics" });
import getUserInfo from "@/utils/getUserInfo";
const userInfo = getUserInfo();
const props = withDefaults(defineProps<{ data: Partial<import("../helper").DataItem>; height: number; refresh: () => Promise<void> }>(), { data: () => ({}) });

const route = useRoute();
const tableData = ref<Dynamic[]>([]);
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);

function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == getUserInfo().zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
async function getTrendsList() {
  try {
    // // console.log(props.data);
    const { success, data, message } = await getPublishTrendList({ id: route.params.id, tenantId: userInfo.currentTenantId });
    if (!success) throw new Error(message);
    // console.log(data, 55555);
    tableData.value = data.sort((a, b) => Number(b.dynamicCreateTime) - Number(a.dynamicCreateTime));
    tableData.value = data.map((item: any) => {
      return {
        ...item,
        dynamicCreateTime: Number(item.dynamicCreateTime) + timeZoneSwitching(),
      };
    });
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

onMounted(() => {
  getTrendsList();
});
</script>

<style lang="scss" scoped></style>
