<template>
  <el-dialog :title="`挂起`" v-model="dialogFormVisible" :before-close="beforeClose" width="800px">
    <el-form ref="form" :model="form" label-width="100px" :rules="rules">
      <el-alert type="warning" show-icon title="挂起超过8小时将需要管理员审批，最多挂起不超过7天。"></el-alert>

      <el-form-item label="挂起时间" :style="{ marginTop: '10px' }">
        <div :style="{ display: 'flex', justifyContent: 'space-between' }">
          <el-input-number v-model.number="form.hangUpDay" :max="7" :min="0" :style="{ width: '175px' }" @change="handleDayChange"></el-input-number>
          <span class="tw-mx-3">天</span>
          <el-input-number v-model.number="form.hangUpHour" :max="hangUpHourMax" :min="0" :style="{ width: '175px' }"></el-input-number>
          <span class="tw-mx-3">时</span>
          <el-input-number v-model.number="form.hangUpMinute" :max="hangUpMinuteMax" :min="0" :style="{ width: '175px' }"></el-input-number>
          <span class="tw-mx-3">分</span>
        </div>
      </el-form-item>

      <el-form-item label="挂起原因">
        <div class="tw-flex tw-min-h-[300px] tw-w-full tw-flex-col">
          <QuillEditor theme="snow" style="flex: 1" :content="form.cause" @update:content="form.cause = $event" contentType="html" toolbar="full" :enable="true"></QuillEditor>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" :loading="butLoading" @click="submitForm()">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { eventPend } from "@/views/pages/apis/eventManage";
import { QuillEditor } from "@vueup/vue-quill";
// import editor from "@/components/Editor/indexEditor.vue";
export default {
  components: { QuillEditor },
  props: {
    refresh: Function,
  },
  data() {
    return {
      dialogFormVisible: false,
      form: {
        hangUpDay: 0,
        hangUpHour: 1,
        hangUpMinute: 0,
        cause: "",
      },
      event: {},
      butLoading: false,
      hangUpHourMax: 23,
      hangUpMinuteMax: 59,
    };
  },
  computed: {
    rules() {
      return {};
    },
  },
  methods: {
    handleDayChange(v) {
      if (v === 7) {
        this.form.hangUpHour = 0;
        this.form.hangUpMinute = 0;
        this.hangUpHourMax = 0;
        this.hangUpMinuteMax = 0;
      } else {
        this.hangUpHourMax = 23;
        this.hangUpMinuteMax = 59;
      }
    },
    beforeClose(done) {
      this.$refs.form.clearValidate();
      this.$refs.form.resetFields();
      if (done instanceof Function) done();
      else this.dialogFormVisible = false;
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return false;
        const params = {
          eventId: this.event.id,
          cause: this.form.cause,
          durationMinutes: (this.form.hangUpDay || 0) * 24 * 60 + (this.form.hangUpHour || 0) * 60 + this.form.hangUpMinute || 0,
        };
        eventPend(params).then(({ success, data }) => {
          if (success) {
            this.$message.success("操作成功");
            this.refresh instanceof Function && this.refresh();
            this.beforeClose();
          } else this.$message.error(JSON.parse(data)?.message || "操作失败");
        });
      });
    },
    open(v) {
      this.event = v;
      this.dialogFormVisible = true;
    },
  },
};
</script>
