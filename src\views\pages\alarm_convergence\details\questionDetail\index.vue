<template>
  <div class="tw-flex tw-h-[50px] tw-flex-nowrap tw-items-center tw-pb-[20px]">
    <el-page-header class="tw-w-full" :content="`${$t('generalDetails.Problem Details')}${state.data.draft ? `(草稿)` : state.data.id || ''}`" @back="handleCancel()">
      <template #extra>
        <div>
          <el-row :gutter="8" type="flex" justify="end" align="middle">
            <el-col :span="7" :style="{ display: 'flex', justifyContent: 'flex-end' }">
              <el-button-group :style="{ display: 'flex' }">
                <el-button :type="(currentEventState.type as '') || ''" :disabled="state.data.operation === questionOperation.CLOSE" @click.stop>{{ currentEventState.label || "" }}</el-button>
                <el-dropdown @command="$event.command()">
                  <el-button :type="(currentEventState.type as '') || ''" @click.stop>
                    <template v-if="!!operation">
                      <span v-if="OperateChanged" class="tw-mr-1 tw-text-white">*</span>
                      {{ $t("generalDetails." + (find(questionOperationOption, (v) => v.value === operation) || {}).label) }}</template
                    >

                    <template v-else-if="!!stateRightText">
                      <span v-if="OperateChanged" class="tw-mr-1 tw-text-white">*</span>
                      {{ $t("generalDetails." + stateRightText) }}
                    </template>
                    <template v-else>
                      <template v-if="state.data.questionState === questionState.NEW">{{ $t("generalDetails.newly built") }}</template>
                      <template v-if="state.data.questionState === questionState.PROCESSING">{{ $t("generalDetails.handle") }}</template>
                      <template v-else-if="state.data.questionState === questionState.COMPLETED">{{ $t("generalDetails.complete") }}</template>
                      <template v-else-if="state.data.operation === questionOperation.CLOSE || state.data.questionState === questionState.AUTO_CLOSED || state.data.questionState === questionState.CLOSED">{{ $t("generalDetails.close") }}</template>
                    </template>
                    <el-icon class="tw-ml-2"><ArrowDown></ArrowDown></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu class="tw-min-w-[100px]">
                      <el-dropdown-item
                        v-for="item in [
                          { command: questionState.PROCESSING, /*  */ execute: () => handleAccept(state.data), /*         */ center: $t('generalDetails.处理'), /*  */ disabled: [questionState.PROCESSING].includes((state.data.questionState as questionState) || ('' as questionState)) || !verifyPermissionIds.includes('612916051063078912') },
                          { command: questionState.COMPLETED, /*   */ execute: () => handleEnd(state.data, 'Finish'), /*  */ center: $t('generalDetails.完成'), /*  */ disabled: [questionState.NEW, questionState.COMPLETED].includes((state.data.questionState as questionState) || ('' as questionState)) || state.data.operation === questionOperation.FINISHED },
                          { command: questionState.CLOSED, /*      */ execute: () => handleEnd(state.data, 'Close'), /*   */ center: $t('generalDetails.关闭'), /*  */ disabled: !orderIsClose },
                        ]"
                        :key="item.command"
                        :command="{ command: item.execute }"
                        :disabled="state.loading || item.disabled"
                      >
                        {{ item.center }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-button-group>

              <!-- -->
            </el-col>
            <el-col :span="7" style="display: flex">
              <span v-if="userGroupChanged" class="tw-mr-1 tw-text-red-500">*</span>
              <el-select clearable :model-value="userGroups.find((v) => v.id === transferOrUpgrade.userGroupId) ? transferOrUpgrade.userGroupId : ''" @change="handleSetTransferOrUpgrade({ type: typeOfTransferOrUpgrade, userGroupId: $event, userId: '' }, state.data)" :placeholder="$t('generalDetails.Please select a user group')" filterable :disabled="[questionState.NEW, questionState.CLOSED, questionState.COMPLETED, questionState.AUTO_CLOSED, questionState.CLOSED].includes((state.data.questionState as questionState) || ('' as questionState)) || !userInfo.hasPermission(智能事件中心_问题工单_分配用户组)">
                <el-option v-for="userGroup in userGroups" :key="userGroup.id" :label="userGroup.name" :value="userGroup.id"></el-option>
              </el-select>
            </el-col>
            <el-col :span="7" style="display: flex">
              <span v-if="userChanged" class="tw-mr-1 tw-text-red-500">*</span>
              <el-select :model-value="userList.find((v) => v.id === transferOrUpgrade.userId) ? transferOrUpgrade.userId : ''" @change="handleChangeUser({ type: typeOfTransferOrUpgrade, userGroupId: transferOrUpgrade.userGroupId, userId: $event }, state.data)" :placeholder="$t('generalDetails.Please select a user')" filterable :disabled="[questionState.NEW, questionState.CLOSED, questionState.COMPLETED, questionState.AUTO_CLOSED, questionState.CLOSED].includes((state.data.questionState as questionState) || ('' as questionState)) || !userInfo.hasPermission(智能事件中心_问题工单_分配用户)">
                <el-option v-for="user in userList" :key="user.id" :label="user.name + `(${user.account}@${user.tenantAbbreviation})`" :value="user.id"></el-option>
              </el-select>
            </el-col>
            <!-- <el-col :span="3" :style="{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }">
              <el-checkbox v-model="typeOfTransferOrUpgrade" false-label="transfer" true-label="eventUpgrade" style="margin-right: 5px" :disabled="[questionState.NEW, questionState.NEW, questionState.CLOSED].includes(state.data.questionState as questionState || '' as questionState)">升级</el-checkbox>
              <el-tooltip class="item" effect="dark" content="若需要升级请先点选升级复选框" placement="bottom">
                <el-icon class="tipIcon"><InfoFilled></InfoFilled></el-icon>
              </el-tooltip>
            </el-col> -->
            <el-col :span="3" :style="{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }">
              <el-tooltip :visible="state.data.draft" placement="top" effect="light" popper-class="draft-tooltip">
                <template #content>
                  <span>{{ $t("generalDetails.Click to save and create a work order") }}</span>
                </template>
                <el-button type="primary" v-preventReClick @click="handleQuestionSave" :disabled="!verifyPermissionIds.includes('612916051063078912') || state.data.draft ? false : false">{{ $t("generalDetails.Save") }}</el-button>
              </el-tooltip>
            </el-col>
          </el-row>
        </div>
      </template>
    </el-page-header>
  </div>
  <el-scrollbar class="tw-h-fit" :height="height - 50">
    <el-card v-loading="state.loading" class="tw-mb-[18px] tw-h-[152px]">
      <template #header>
        <div style="display: flex; justify-content: space-between">
          <el-button link type="primary" class="tw-font-semibold" :style="{ color: 'var(--el-color-primary)' }" :disabled="[questionState.CLOSED, questionState.AUTO_CLOSED].includes(state.data.questionState)" v-preventReClick @click="handleEditSummary">
            <span v-if="digestChanged" style="color: red; margin-left: 2px">*</span>
            <span>{{ state.data.digest || "--" }}</span>
          </el-button>
          <div style="display: flex">
            <el-text type="primary">{{ tickGroupConfig.ticketClassificationNames.join("/") }}</el-text>
            <div style="margin-right: 2px" v-for="itemA in sortedLocalesOption" :key="itemA.value">
              <el-tooltip v-if="isLanguageMatch(itemA.value)" class="item" effect="light" :content="getTooltipContent(itemA.value, itemA.zh_label)" placement="bottom">
                <div
                  :style="{
                    background: `url(${itemA.icon}) no-repeat left / auto`,
                    paddingLeft: '30px',
                    width: '22px',
                    height: '22px',
                  }"
                ></div>
              </el-tooltip>
            </div>
            <div>{{ state.data.category }}</div>
          </div>
        </div>
      </template>
      <template #default>
        <FormModel :model="form" :style="{ marginBottom: '-18px' }">
          <!-- <FormItem :span="8" label="创建时间" tooltip="" prop="" :rules="[]">{{ state.data.collectTime ? moment(state.data.collectTime, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</FormItem> -->
          <!-- <FormItem :span="8" label="持续时间" tooltip="" prop="" :rules="[]">{{ state.data.collectTime ? moment(state.data.collectTime, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</FormItem> -->
          <FormItem :span="6" :label="$t('generalDetails.Priority')" tooltip="" prop="" :rules="[]">
            <template #label>
              <span>
                <span v-if="priorityChanged" style="color: red">*</span>
                {{ $t("generalDetails.Priority") }}
              </span>
            </template>
            <el-dropdown trigger="click" @command="handleSetPriority($event, state.data)" :disabled="[questionState.COMPLETED, questionState.CLOSED, questionState.AUTO_CLOSED].includes((state.data.questionState as questionState) || ('' as questionState)) || state.data.operation === questionOperation.CLOSE">
              <span class="el-dropdown-link">
                <i class="priority-icon" :style="{ backgroundColor: currentEventState.color || '' }" />
                <span class="tw-align-[2px]" :style="{ color: currentPriority.color || '' }">{{ state.data.priority }}</span>
                <el-icon class="el-icon--right"><ArrowDown></ArrowDown></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="(value, key) in priorityOption" :key="`priority-${key}`" :command="value.value">{{ value.label }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </FormItem>
          <FormItem :span="6" :label="$t('generalDetails.Urgency')" tooltip="" prop="" :rules="[]">
            <template #label>
              <span>
                <span v-if="urgencyChanged" style="color: red">*</span>
                {{ $t("generalDetails.Urgency") }}
              </span>
            </template>
            <el-select :model-value="state.data.urgency || ('' as string)" filterable @change="handleSetSeverity($event, state.data)" :disabled="[questionState.COMPLETED, questionState.CLOSED, questionState.AUTO_CLOSED].includes((state.data.questionState as questionState) || ('' as questionState)) || state.data.operation === questionOperation.CLOSE">
              <el-option v-for="item in deviceImportanceOption.map((v) => ({ ...v, priority: priorityMatrix.filter((raw) => raw.eventSeverity === v.value) })).filter((v) => v.priority.length)" :key="item.value" :label="`${item.label}`" :value="item.value"></el-option>
              <!-- item.priority.map(v => `${v.priority}：${(find(deviceImportanceOption, (raw) => raw.value === v.deviceImportance) || {}).label || v.deviceImportance}`).join('，') -->
            </el-select>
          </FormItem>
          <FormItem :span="6" :label="$t('generalDetails.Impact')" tooltip="" prop="" :rules="[]">
            <template #label>
              <span>
                <span v-if="impactChanged" style="color: red">*</span>
                {{ $t("generalDetails.Impact") }}
              </span>
            </template>
            <el-select :model-value="state.data.influence || ('' as string)" filterable @change="handleSetImportance($event, state.data)" :disabled="[questionState.COMPLETED, questionState.CLOSED, questionState.AUTO_CLOSED].includes((state.data.questionState as questionState) || ('' as questionState)) || state.data.operation === questionOperation.CLOSE">
              <el-option v-for="item in deviceImportanceOption.map((v) => ({ ...v, priority: priorityMatrix.filter((raw) => raw.deviceImportance === v.value) }))" :key="item.value" :label="`${item.label}`" :value="item.value"></el-option>
              <!-- item.priority.map(v => `${v.priority}：${(find(eventSeverityOption, (raw) => raw.value === v.eventSeverity) || {}).label || v.eventSeverity}`).join('，') -->
            </el-select>
          </FormItem>

          <!-- <el-col :span="24" class="tw-mb-[18px] tw-bg-[var(--el-bg-color-page)] tw-py-2">SLA情况</el-col>
        <FormItem :span="8" label="响应时限情况" tooltip="" prop="" :rules="[]">
          <el-progress :percentage="responseTimePercentage" :color="resolveUrgencyType.color" :format="() => `${state.data.responseTime || 0}分钟 / ${state.data.responseLimit || 0}分钟`" class="tw-w-full"></el-progress>
        </FormItem>
        <el-col :span="1"></el-col>
        <FormItem :span="8" label="解决时限情况" tooltip="" prop="" :rules="[]">
          <el-progress :percentage="resolveTimePercentage" :color="resolveUrgencyType.color" :format="() => `${state.data.resolveTime || 0}分钟 / ${state.data.resolveLimit || 0}分钟`" class="tw-w-full"></el-progress>
        </FormItem> -->
        </FormModel>
        <div></div>
        <!-- <pre>{{ state.data }}</pre> -->
      </template>
    </el-card>
    <completeCode v-if="[questionState.CLOSED, questionState.AUTO_CLOSED].includes(state.data.questionState || ('' as questionState))" class="tw-mb-[18px]" :title="currentEventState.label || ''" :finishCodeName="state.data.completeInfo?.finishCodeName || ''" :finishCodeDesc="state.data.completeInfo?.finishCodeDesc || ''" :finishContent="state.data.completeInfo?.finishContent || ''" />
    <el-card v-loading="state.loading">
      <template #header>
        <el-radio-group :model-value="(route.query.type as string) || ''" @change="router.push({ query: { ...route.query, type: $event as string } })">
          <el-radio-button :label="pageType.details">
            <el-badge class="mark">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Details") }}</div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.subtotal">
            <el-badge class="mark" :value="tabCounts.noteCount || undefined">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Journals") }}</div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.device">
            <el-badge class="mark" :value="tabCounts.deviceCount || undefined">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Devices") }}</div>
            </el-badge></el-radio-button
          >
          <el-radio-button :label="pageType.contacts">
            <el-badge class="mark" :value="tabCounts.contactCount || undefined">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Contacts") }}</div>
            </el-badge></el-radio-button
          >
          <el-radio-button :label="pageType.project">
            <el-badge class="mark" :value="undefined">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Projects") }}</div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.related" :disabled="!userInfo.hasPermission('515390468603772928')">
            <el-badge class="mark" :value="tabCounts.relationCount || undefined">
              <div class="tw-px-[1em]">{{ $t("generalDetails.link") }}</div>
            </el-badge></el-radio-button
          >
          <el-radio-button :label="pageType.alarm">
            <el-badge class="mark">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Alerts") }}</div>
            </el-badge>
          </el-radio-button>
          <el-radio-button :label="pageType.sla">
            <el-badge class="mark"> <div class="tw-px-[1em]">SLA</div> </el-badge></el-radio-button
          >
          <el-radio-button :label="pageType.strategy">
            <el-badge class="mark" :value="Number(tabCounts.actionCount) || undefined">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Support Notes") }}</div>
            </el-badge></el-radio-button
          >
          <el-radio-button :label="pageType.file">
            <el-badge class="mark" :value="tabCounts.fileCount || undefined">
              <div class="tw-px-[1em]">{{ $t("generalDetails.Files") }}</div>
            </el-badge></el-radio-button
          >
          <el-radio-button :label="pageType.dynamics">
            <el-badge class="mark">
              <div class="tw-px-[1em]">{{ $t("generalDetails.History Log") }}</div>
            </el-badge></el-radio-button
          >
        </el-radio-group>
      </template>
      <template #default>
        <el-scrollbar>
          <div v-if="((route.query.type as string) || '') === pageType.details">
            <!-- 详述 -->
            <ModelDetails :data="state.data" :refresh="handleRefresh" @changeDesc="(v) => (state.data.description = v)" :height="0"></ModelDetails>
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.subtotal">
            <!-- 小计 -->
            <ModelNotes ref="modelNotesRef" :data="state.data" :refresh="handleRefresh" :height="0"></ModelNotes>
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.device">
            <!-- 设备 -->
            <ModelDevices :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.contacts">
            <!-- 联系人 -->
            <ModelContacts :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.project">
            <ModelProject :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.related">
            <!-- 关联 -->
            <ModelAssociation :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.alarm">
            <!-- 告警记录 -->
            <ModelAlarm :data="state.data" :refresh="handleRefresh" :handleEnd="handleAlarm" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.sla">
            <!-- SLA -->
            <ModelSLA :data="state.data" :refresh="handleRefresh" :height="0"></ModelSLA>
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.strategy">
            <!-- 行动策略 -->
            <ModelStrategy :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.file">
            <!-- 文件 -->
            <ModelFiles :data="state.data" :refresh="handleRefresh" :height="0" />
          </div>
          <div v-if="((route.query.type as string) || '') === pageType.dynamics">
            <!-- 动态 -->
            <ModelDynamics :data="state.data" :refresh="handleRefresh" :height="0"></ModelDynamics>
          </div>
        </el-scrollbar>
      </template>
    </el-card>
  </el-scrollbar>
  <!-- <EventPend ref="pendRef" :refresh="handleRefresh"></EventPend> -->

  <EventEnd
    ref="endRef"
    :refresh="handleRefresh"
    :ticketTemplateId="state.data.ticketTemplateId"
    @end="
      (v) => {
        completeQuery = v.params;
        operation = v.type === 'Finish' ? questionOperation.FINISHED : questionOperation.CLOSE;
        notesQuery = v.notesForm;
      }
    "
    :height="height - 317"
  ></EventEnd>

  <Editor ref="editorRef" title="事件" display="dialog">
    <template #setPriority="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          确定设置
          <span>{{ ((params.raw || {}) as AnyObject).summary as string }}</span>
          事件优先级为
          <span>{{ params.label }}</span>
          吗？
        </p>
      </div>
    </template>
    <template #setImportance="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          确定设置
          <span>{{ ((params.raw || {}) as AnyObject).summary as string }}</span>
          事件重要性为
          <span>{{ params.label }}</span>
          吗？
        </p>
      </div>
    </template>
    <template #setSeverity="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          确定设置
          <span>{{ ((params.raw || {}) as AnyObject).summary as string }}</span>
          事件紧急性为
          <span>{{ params.label }}</span>
          吗？
        </p>
      </div>
    </template>
  </Editor>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch, computed, h, provide, toRefs } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Refresh, InfoFilled, ArrowDown } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";
import { ElTable, ElIcon } from "element-plus";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import ModelDetails from "./models/details.vue";
import ModelSubtotal from "./models/subtotal.vue";
import ModelSLA from "./models/sla.vue";
import ModelDynamics from "./models/dynamics.vue";

import ModelNotes from "./models/notes.vue";

import ModelDevices from "./models/devices.vue";

import ModelContacts from "./models/contacts.vue";

import ModelProject from "@/views/pages/alarm_convergence/details/eventDetail/models/project.vue";

import ModelAssociation from "./models/association.vue";

import ModelFiles from "./models/files.vue";

import ModelAlarm from "./models/alarm.vue";

import ModelStrategy from "./models/strategy.vue";

// import EventPend from "./models/pend.vue";

import EventEnd from "./models/end.vue";

import Editor from "./Editor.vue";

import completeCode from "@/components/completeCode.vue";

/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox } from "element-plus";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { useSiteConfig } from "@/stores/siteConfig";
import getUserInfo from "@/utils/getUserInfo";
import { find } from "lodash-es";
import moment from "moment";

import { type BaseItem, DataItem, type Item } from "./helper";
import { state } from "./helper";
import { resetData, command } from "./helper";

/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
import { questionState, questionStateOption, eventSeverity, eventSeverityOption, priority, priorityOption, questionOperation, setQuestionEditable, questionOperationOption } from "@/views/pages/apis/question";
import { deviceImportance, deviceImportanceOption } from "@/views/pages/apis/device";

// import { getEventData as getItemData } from "@/views/pages/apis/event";

import { getQuestionDetail as getItemData, setQuestionPriority, setQuestionImportance, setQuestionSeverity, setQuestionStateProcessing, questionTransfer, getDetailTapCountById, type TabCount } from "@/views/pages/apis/question";
import { addEventData as addItemData, setEventData as setItemData, modEventData as modItemData, delEventData as delItemData } from "@/views/pages/apis/event";
import { setEventDataByTakeOver, setEventDataByApprove, setEventDataByTransferOrUpgrade, type TimeLimit } from "@/views/pages/apis/event";
import { getPriorityMatrixList } from "@/views/pages/apis/eventPriority";
import { getGroupList, getUserByGroup, type GroupItem, type EntrustUserItem } from "@/api/personnel";
import { eventEditSummary as editSummary } from "@/views/pages/apis/eventManage";

import { 智能事件中心_问题工单_分配用户, 智能事件中心_问题工单_分配用户组, 智能事件中心_问题工单_可读 } from "@/views/pages/permission";
import { localesOption } from "@/api/locale.ts";
import { getTenantInfo } from "@/views/pages/apis/tenant";
import { getContactTypes as getType, type ContactsTypeItem, type ContactsItem } from "@/views/pages/apis/contacts";
import { eventBatchContact as getData, eventBatchdesensitized, eventAddContact as addData, eventDelContact as delData, getChangeContacts } from "@/views/pages/apis/eventManage";

import { getCloseDirectly, getOrderUserGroup, AssignableTicketType, UserGroupConfigurationItem, getOrderUserGroupIsClose, getTicketClassificationNames } from "@/views/pages/apis/orderGroup";
import { 智能事件中心_服务请求工单_编辑小记, 智能事件中心_事件工单_编辑小记, 智能事件中心_DICT事件管理_编辑小记, 智能事件中心_DICT服务请求_编辑小记, 智能事件中心_变更工单_编辑小记, 智能事件中心_问题工单_编辑小记, 智能事件中心_发布管理_编辑小记 } from "@/views/pages/permission";
import { addOrderNode } from "@/views/pages/apis/event";

/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const contactsType = ref<{ contactId: string; contactType: string }[]>([]);
const contacts = ref<ContactsItem[]>([]);
const uniqueByLanguage = ref<ContactsItem[]>([]);
const sortedLocalesOption = ref<ContactsItem[]>(localesOption);
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const modelNotesRef = ref();
const { t } = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "alarmBoard" });
const siteConfig = useSiteConfig();
const userInfo = getUserInfo();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
interface Props {
  width?: number;
  height?: number;
  title?: string;
}
const props = withDefaults(defineProps<Props>(), { title: "告警" });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

const _width = inject("width", ref(0));
const _height = inject("height", ref(0));

const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);

const tableRef = ref<InstanceType<typeof ElTable>>();
const editorRef = ref<InstanceType<typeof Editor>>();
provide("detailData", toRefs(state).data);

const stateRightText = computed(() => (questionOperationOption.find(({ value }) => value === state.data.operation) || {}).label);

const verifyPermissionIds = ref<string[]>([]);
provide("verifyPermissionIds", verifyPermissionIds);
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Form {
  priority: string;
}
interface AnyObject {
  [key: string]: any;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const timer = ref<null | NodeJS.Timer>(null);
const autoRefreshTime = ref(0);

enum pageType {
  details = "",
  subtotal = "subtotal",
  device = "device",
  contacts = "contacts",
  related = "related",
  alarm = "alarm",
  sla = "sla",
  strategy = "strategy",
  file = "file",
  dynamics = "dynamics",
  project = "project",
}

const currentEventState = computed(() => find(questionStateOption, (v) => v.value === state.data.questionState) || ({} as Partial<(typeof questionStateOption)[number]>));
const currentPriority = computed(() => find(priorityOption, (v) => v.value === state.data.priority) || ({} as Partial<(typeof priorityOption)[number]>));

// const responseUrgencyType = computed(() => setSlaState((state.data.slaTimeLimit || {}).responseTimeLimits || [], Number(state.data.responseTime) || 0));
// const resolveUrgencyType = computed(() => setSlaState((state.data.slaTimeLimit || {}).completedTimeLimits || [], Number(state.data.resolveTime) || 0));

// function setSlaState(list: TimeLimit[], val: number) {
//   let result = urgencyType.BREACH;
//   list.sort((a, b) => (Number(b.tolerateMinutes) || 0) - (Number(a.tolerateMinutes) || 0)).forEach((el) => ((Number(el.tolerateMinutes) || 0) >= val || list.length === 1) && (result = el.urgencyType));
//   return find(urgencyTypeOption, ({ value }) => value === result) || { label: "致命", value: "BREACH", color: "#ED4013" };
// }

const eventStat = ref<{ label: string; value: questionState; color?: string; count: number }[]>([]);

const form = reactive<Form>({
  priority: "",
});

// const responseTimePercentage = computed(() => (((Number(state.data.responseTime) || 0) / (Number(state.data.responseLimit) || 0)) * 100 > 100 ? 100 : (Number(state.data.responseTime) / Number(state.data.responseLimit)) * 100) || 0);
// const resolveTimePercentage = computed(() => (((Number(state.data.resolveTime) || 0) / (Number(state.data.resolveLimit) || 0)) * 100 > 100 ? 100 : (Number(state.data.resolveTime) / Number(state.data.resolveLimit)) * 100) || 0);

const priorityMatrix = ref<{ eventSeverity: eventSeverity; deviceImportance: deviceImportance; priority: priority }[]>([]);
const userGroups = ref<Record<string, any>[]>([]);
const userList = ref<EntrustUserItem[]>([]);
const typeOfTransferOrUpgrade = ref<"transfer" | "eventUpgrade">("transfer");

type typeTransferOrUpgrade = { userGroupId: string; userId: string };
const transferOrUpgrade = ref<typeTransferOrUpgrade>({} as typeTransferOrUpgrade);

const operation = ref<questionOperation>("" as questionOperation);

type CompleteQuery = { completeInfo: { finishCodeName: string; finishCodeDesc: string; finishContent: string }; closeAlert: boolean };
const completeQuery = ref<CompleteQuery>({} as CompleteQuery);
type NotesQuery = { content: string; privateAble: boolean };
const notesQuery = ref<NotesQuery>({} as NotesQuery);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  handleRefresh().then(() => (autoRefreshTime.value = 60));
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {
  if (timer.value !== null) {
    clearInterval(timer.value);
    timer.value = null;
  }
}
function beforeDestroy() {
  if (timer.value !== null) {
    clearInterval(timer.value);
    timer.value = null;
  }
}
function destroyed() {}

watch(autoRefreshTime, (autoRefreshTime) => {
  if (timer.value !== null) timer.value = (clearInterval(timer.value), null);
  if (autoRefreshTime) timer.value = setInterval(queryData, autoRefreshTime * 1000);
});
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
async function handleQuestionSave() {
  try {
    const params = {
      id: route.params.id,
      priority: state.data.priority,
      urgency: state.data.urgency,
      influence: state.data.influence,
      ...transferOrUpgrade.value,
      desc: state.data.description,
      externalId: state.data.externalId,
      digest: state.data.digest,
      operation: operation.value || null,
      completeInfo: completeQuery.value,
    };
    const { success, data, message } = await setQuestionEditable(params);
    if (!success) throw new Error(message);
    ElMessage.success("操作成功");
    if (hasValidContent(notesQuery.value.content)) {
      submitNote();
    }
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    await handleRefresh();
  }
}
const hasValidContent = (html) => {
  if (html === undefined || html === null) {
    return false;
  }
  // 1. 过滤空标签（包括自闭合标签）
  const cleanedHtml = html.replace(/<([a-z][a-z0-9]*)\b[^>]*>(?:\s|&nbsp;)*<\/\1>|<\w+\s*\/>/gi, "");
  // 2. 移除所有空格（包括换行、制表符等）
  const trimmedContent = cleanedHtml.replace(/\s+/g, "").trim();
  // 3. 返回是否有有效内容
  return trimmedContent.length > 0;
};
async function submitNote() {
  try {
    const formData = new FormData();
    formData.append("nodeContent", notesQuery.value.content);
    formData.append("privateAble", notesQuery.value.privateAble as any);
    formData.append("privateCustomerId", userInfo.tenantId);
    formData.append("tenantId", (userInfo.currentTenant || {}).id as string);
    formData.append("orderType", "QUESTION");
    formData.append("permissionId", EditNodePermissionId["QUESTION"]);
    formData.append("orderId", route.params.id as string);
    formData.append("orderIdsJson", JSON.stringify([route.params.id]));
    const { success, message } = await addOrderNode(formData as any);
    if (!success) throw new Error(message);
    //ElMessage.success("操作成功");
    if (modelNotesRef.value) {
      modelNotesRef.value.getEventNotes(); // 安全调用
    }
    handleRefresh();
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    await queryData();
  }
}
enum EditNodePermissionId {
  EVENT_ORDER = 智能事件中心_事件工单_编辑小记,
  SERVICE_REQUEST = 智能事件中心_服务请求工单_编辑小记,
  DICT_EVENT_ORDER = 智能事件中心_DICT事件管理_编辑小记,
  DICT_SERVICE_REQUEST = 智能事件中心_DICT服务请求_编辑小记,
  CHANGE = 智能事件中心_变更工单_编辑小记,
  QUESTION = 智能事件中心_问题工单_编辑小记,
  PUBLISH = 智能事件中心_发布管理_编辑小记,
}

function handleEventOperateCommand(v: questionState) {
  switch (v) {
    case questionState.PROCESSING:
      // 处理中
      // handleBatchAccept(detailData);
      break;
    case questionState.COMPLETED:
      // 完成
      // handleEventEnd(detailData, "Finish");
      break;
    case questionState.CLOSED:
      // 关闭
      // handleEventEnd(detailData, "Close");
      break;
  }
}

// async function handleCommand(type: command, data?: Record<string, unknown>) {
//   const time = autoRefreshTime.value;
//   autoRefreshTime.value = 0;
//   try {
//     state.loading = true;
//     await nextTick();
//     switch (type) {
//       case command.Refresh:
//         await resetData();
//         await queryData();
//         break;
//       case command.Request:
//         await queryData();
//         break;
//       case command.Preview:
//         await previewItem(data as Record<string, unknown>);
//         break;
//       case command.Create:
//         await createItem(data as Record<string, unknown>);
//         await resetData();
//         await queryData();
//         break;
//       case command.Update:
//         await rewriteItem(data as Record<string, unknown>);
//         await resetData();
//         await queryData();
//         break;
//       case command.Modify:
//         await modifyItem(data as Record<string, unknown>);
//         await resetData();
//         await queryData();
//         break;
//       case command.Delete:
//         await deleteItem(data as Record<string, unknown>);
//         await resetData();
//         await queryData();
//         break;
//     }
//   } catch (error) {
//     if (error instanceof Error) {
//       const message = error.message;
//       await resetData();
//       await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
//       await queryData();
//     }
//   } finally {
//     autoRefreshTime.value = time;
//     state.loading = false;
//   }
// }
const userGroupChanged = ref(false);
async function handleSetTransferOrUpgrade(req: { userGroupId: string; userId: string; type: "transfer" | "eventUpgrade" }, raw: Partial<DataItem>) {
  // const time = autoRefreshTime.value;
  try {
    transferOrUpgrade.value.userGroupId = req.userGroupId;
    transferOrUpgrade.value.userId = req.userId;
    userGroupChanged.value = true;
    await (async (req?: { id: string }) => {
      try {
        if (!req) return;
        const { success, message, data: res } = await getUserByGroup({ id: req.id });
        if (!success) throw Object.assign(new Error(message), { success, data: res });
        userList.value = res;
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      }
    })({ id: req.userGroupId });

    // autoRefreshTime.value = 0;
    // state.loading = true;
    // const { success, data, message } = await questionTransfer({
    //   id: route.params.id as string,
    //   userGroupId: req.userGroupId,
    //   userId: req.userId,
    // });
    // if (!success) throw new Error(message);
    // ElMessage.success(`操作成功`);
    // const { success, message, data } = await setEventDataByTransferOrUpgrade({ id: raw.id as string, ...req });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功${req.type === "eventUpgrade" ? "升级事件" : "转交事件"}`);
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    // state.loading = false;
  }
}
const userChanged = ref(false);
async function handleChangeUser(req: { userGroupId: string; userId: string; type: "transfer" | "eventUpgrade" }, raw: Partial<DataItem>) {
  // const time = autoRefreshTime.value;
  try {
    transferOrUpgrade.value.userGroupId = req.userGroupId;
    transferOrUpgrade.value.userId = req.userId;
    userChanged.value = true;
    await (async (req?: { id: string }) => {
      try {
        if (!req) return;
        const { success, message, data: res } = await getUserByGroup({ id: req.id });
        if (!success) throw Object.assign(new Error(message), { success, data: res });
        userList.value = res;
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      }
    })({ id: req.userGroupId });

    // autoRefreshTime.value = 0;
    // state.loading = true;
    // const { success, data, message } = await questionTransfer({
    //   id: route.params.id as string,
    //   userGroupId: req.userGroupId,
    //   userId: req.userId,
    // });
    // if (!success) throw new Error(message);
    // ElMessage.success(`操作成功`);
    // const { success, message, data } = await setEventDataByTransferOrUpgrade({ id: raw.id as string, ...req });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功${req.type === "eventUpgrade" ? "升级事件" : "转交事件"}`);
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    // state.loading = false;
  }
}
const priorityChanged = ref(false);
async function handleSetPriority(priority: priority, raw: Partial<DataItem>) {
  priorityChanged.value = true;
  // const priorityItem = (find(priorityOption, ({ value }) => value === priority) || {}).label || priority;
  // // (0 , _views_pages_apis_serviceRequest__WEBPACK_IMPORTED_MODULE_22__.setQuestionPriority) is not a function
  // if (!editorRef.value) return;
  // const time = autoRefreshTime.value;
  try {
    // autoRefreshTime.value = 0;
    // state.loading = true;

    // const params = { priority, label: priorityItem, raw };
    // const form = {};
    // await editorRef.value.confirm({ ...form, ...params, $type: "warning", $title: `修改优先级`, $slot: "setPriority" }, async (form: Record<string, unknown>) => {
    // const { success, message, data } = await setQuestionPriority({ id: raw.id as string, priority });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功修改优先级`);

    state.data.priority = priority;
    // });
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    state.loading = false;
  }
}
const impactChanged = ref(false);
async function handleSetImportance(importance: deviceImportance, raw: Partial<DataItem>) {
  // const importanceItem = (find(deviceImportanceOption, ({ value }) => value === importance) || {}).label || importance;
  impactChanged.value = true;
  // if (!editorRef.value) return;
  // const time = autoRefreshTime.value;
  try {
    // autoRefreshTime.value = 0;
    // state.loading = true;

    // const params = { importance, label: importanceItem, raw };
    // const form = {};
    // await editorRef.value.confirm({ ...form, ...params, $type: "warning", $title: `修改重要性`, $slot: "setImportance" }, async (form: Record<string, unknown>) => {
    // const { success, message, data } = await setQuestionImportance({ id: raw.id as string, importance });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功修改重要性`);

    state.data.influence = importance;
    setPriority();
    // });
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    state.loading = false;
  }
}
const urgencyChanged = ref(false);
async function handleSetSeverity(severity: eventSeverity, raw: Partial<DataItem>) {
  urgencyChanged.value = true;
  // const severityItem = (find(eventSeverityOption, ({ value }) => value === severity) || {}).label || severity;

  // if (!editorRef.value) return;
  // const time = autoRefreshTime.value;
  try {
    // autoRefreshTime.value = 0;
    // state.loading = true;

    // const params = { severity, label: severityItem, raw };
    // const form = {};
    // await editorRef.value.confirm({ ...form, ...params, $type: "warning", $title: `修改紧急性`, $slot: "setSeverity" }, async (form: Record<string, unknown>) => {
    // const { success, message, data } = await setQuestionSeverity({ id: raw.id as string, severity });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`成功修改紧急性`);

    state.data.urgency = severity;
    setPriority();
    // });
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // autoRefreshTime.value = time;
    state.loading = false;
  }
}

function setPriority() {
  if (state.data.urgency && state.data.influence) state.data.priority = (find(priorityMatrix.value, (v) => v.eventSeverity === state.data.urgency && v.deviceImportance === state.data.influence) || {}).priority || state.data.priority;
}
const tabCounts = ref<TabCount>({} as TabCount);
async function getTabCount() {
  const { success, data, message } = await getDetailTapCountById({ id: route.params.id as string });
  if (!success) throw new Error(message);
  tabCounts.value = data;
}
async function getContact() {
  const id = state.data.id || route.params.id;
  try {
    const [{ success: contactSuccess, message: contactMessage, data: contactData }, { success: tabsSuccess, message: tabsMessage, data: tabsData }] = await Promise.all([getChangeContacts({ id }), getType({})]);
    if (!contactSuccess) throw Object.assign(new Error(contactMessage), { success: contactSuccess, data: contactData });
    contactsType.value = contactData instanceof Array ? contactData : [];
    if (contactSuccess) {
      const { success, message, data } = await eventBatchdesensitized({ deviceIds: Array.from(contactsType.value.reduce((p, c) => p.add(c.contactId), new Set<string>()).values()) });
      if (!success) throw Object.assign(new Error(message), { success, data });
      contacts.value = data instanceof Array ? data : [];
      const { success: successTenant, message: messageTenant, data: dataTenant } = await getTenantInfo({});
      if (!successTenant) throw Object.assign(new Error(messageTenant), { success: successTenant, dataTenant });
      contacts.value.unshift(dataTenant);
      // 去重方法
      uniqueByLanguage.value = contacts.value.reduce((acc, current) => {
        // 查找当前数组中是否已存在相同 language 的项
        const existingIndex = acc.findIndex((item) => item.language === current.language);

        if (existingIndex === -1) {
          // 如果不存在，直接添加当前项
          acc.push(current);
        } else {
          // 如果已存在，判断当前项是否更优（tenantName 为假）
          const existingItem = acc[existingIndex];
          if (!current.tenantName && existingItem.tenantName) {
            // 替换为更优的当前项（tenantName 为假）
            acc.splice(existingIndex, 1, current);
          }
        }
        return acc;
      }, []);
      // 提取 uniqueByLanguage 中的语言顺序
      const languageOrder = uniqueByLanguage.value.map((item) => item.language);

      // 根据语言顺序对 localesOption 进行排序
      sortedLocalesOption.value.sort((a, b) => {
        const indexA = languageOrder.indexOf(a.value);
        const indexB = languageOrder.indexOf(b.value);
        return indexA - indexB;
      });
    }
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : `${error}`);
  }
}

function isLanguageMatch(value) {
  return uniqueByLanguage.value.some((item) => item.language === value);
}
// 新增方法
function getTooltipContent(language, zhLabel) {
  const matchedItem = uniqueByLanguage.value.find((item) => item.language === language);
  return matchedItem?.tenantName ? `联系人: ${zhLabel}` : `客户: ${zhLabel}`;
}
async function handleRefresh() {
  try {
    state.loading = true;
    await nextTick();
    await resetData();
    await queryData();
    await getTabCount();
    await getContact();
    await resetChangeState();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
async function handleQuery() {
  try {
    state.loading = true;
    await nextTick();
    await queryData();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}

const tickGroupConfig = ref({
  closeDirectly: false,
  isClose: false,
  ticketClassificationNames: [],
});

const orderIsClose = computed(() => {
  if (tickGroupConfig.value.closeDirectly && tickGroupConfig.value.isClose) return true;
  else if (state.data.operation === questionOperation.FINISHED && tickGroupConfig.value.isClose) return true;
  else return false;
});
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
async function getTackerGroupConfig() {
  try {
    const [
      /* 是否直接关闭 */
      { data: closeDirectlyData, message: closeDirectlyMessage, success: closeDirectlySuccess },
      { data: isCloseData, message: isCloseMessage, success: isCloseSuccess },
      ticketClassificationNames,
    ] = await Promise.all([
      /* 获取是否直接关闭 */
      getCloseDirectly({ tenantId: (userInfo.currentTenant || {}).id, ticketTemplateId: (state.data as any).ticketTemplateId }),
      getOrderUserGroupIsClose({ tenantId: (userInfo.currentTenant || {}).id, type: AssignableTicketType.question, userId: userInfo.userId, ticketTemplateId: (state.data as any).ticketTemplateId }),
      getTicketClassificationNames(AssignableTicketType.question, (state.data as any).ticketClassificationId, (state.data as any).ticketTemplateId),
    ]);
    if (!closeDirectlySuccess) throw new Error(closeDirectlyMessage);
    if (!isCloseSuccess) throw new Error(isCloseMessage);
    tickGroupConfig.value.closeDirectly = closeDirectlyData;
    tickGroupConfig.value.isClose = isCloseData;
    tickGroupConfig.value.ticketClassificationNames = ticketClassificationNames;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

async function queryData() {
  try {
    const [
      /*  */
      { success, message, data },
      { success: prioritySuccess, message: priorityMessage, data: priorityData },
    ] = await Promise.all([
      /*  */
      getItemData({ id: route.params.id as string }),
      getPriorityMatrixList({}),
      // getGroupList({ appId: (siteConfig.baseInfo || {}).app, external: true }),
      ,
    ]);
    if (!success) throw Object.assign(new Error(message), { success, data });
    if (!prioritySuccess) throw Object.assign(new Error(priorityMessage), { success: prioritySuccess, data: priorityData });
    const { success: userGroupSuccess, message: userGroupMessage, data: userGroupData } = await getOrderUserGroup({ tenantId: (userInfo.currentTenant || {}).id, type: AssignableTicketType.question, ticketTemplateId: (data as any).ticketTemplateId });
    if (!userGroupSuccess) throw Object.assign(new Error(userGroupMessage), { success: userGroupSuccess, data: userGroupData });

    await (async (req?: UserGroupConfigurationItem) => {
      try {
        if (!req) return;
        const { success, message, data: res } = await getUserByGroup({ id: req.userGrouptId });
        if (!success) throw Object.assign(new Error(message), { success, data: res });
        userList.value = res;
      } catch (error) {
        if (error instanceof Error) ElMessage.error(message);
      }
    })(find(userGroupData, (v) => v.userGrouptId === (data.displayUserGroupId || data.userGroupId)));

    priorityMatrix.value = priorityData.priorityMatrixItems.map((v) => {
      return {
        eventSeverity: v.urgency,
        deviceImportance: v.influence,
        priority: v.priority,
      };
    }) as any;
    userGroups.value = userGroupData.map((v) => ({ id: v.userGrouptId, name: v.userGroupName, tenantAbbreviation: v.abbreviation }));

    setTimeout(() => {
      transferOrUpgrade.value.userGroupId = (find(userGroups.value, (v) => v.id === (state.data.displayUserGroupId || state.data.userGroupId)) || {}).id as string;
      if (state.data.userGroupId == null) transferOrUpgrade.value.userId = (find(userList.value, (v) => v.id === state.data.actorId) || {}).id as string;
    }, 0);

    //     (find(userGroups, (v) => v.id === (state.data.displayUserGroupId || state.data.userGroupId)) || {}).id
    // (find(userList, (v) => v.userId === state.data.actorId) || {}).userId

    state.data = data;

    verifyPermissionIds.value = data.verifyPermissionIds || [];

    getTackerGroupConfig();
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  }
}
async function createItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  await editorRef.value.open(row, async (form: Record<string, unknown>) => {
    const { success, message, data } = await addItemData({ ...form });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`成功确认告警`);
  });
}
// async function previewItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setItemData({ ids: form.ids as string[], ...form });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
// async function rewriteItem(row: Record<string, unknown>) {
//   const title = (find(priorityOption, ({ value }) => value === row.priority) || {}).label || row.priority;
//   if (!editorRef.value) return row;
//   const params = { select: (<Item[]>row.select).filter((v) => row.priority !== v.priority) };
//   await editorRef.value.confirm({ ...row, ...params, $type: "warning", $title: `批量${title}`, $slot: "batchConfirm" }, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setEventDataByPriority({ id: (<Item[]>form.select).map((v) => v.id), priority: form.priority as priority });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`成功${form.$title}`);
//   });
// }
// async function modifyItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await setItemData({ ids: form.ids as string[], ...form });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
// async function deleteItem(row: Record<string, unknown>) {
//   if (!editorRef.value) return row;
//   await editorRef.value.open(row, async (form: Record<string, unknown>) => {
//     const { success, message, data } = await delItemData(form);
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     ElMessage.success(`${t("axios.Operation successful")}`);
//   });
// }
function handleCancel() {
  if ("fallback" in route.query && typeof route.query.fallback === "string") router.push({ name: route.query.fallback, params: { id: route.query.deviceId }, query: { type: route.query.status } });
  else if (history.length === 1) window.close();
  else router.back();
}
const OperateChanged = ref(false);
async function handleAccept(row: Partial<DataItem>) /* 接手 */ {
  try {
    operation.value = questionOperation.TAKE_OVER;
    OperateChanged.value = true;

    // state.loading = true;
    // // serviceRequestId: string | number;
    // // tenantId: string | number;
    // const { success, message, data } = await setQuestionStateProcessing({ id: row.id as string });
    // if (!success) throw Object.assign(new Error(message), { success, data });
    // ElMessage.success(`操作成功`);
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    // await queryData();
    // state.loading = false;
  }
}

const digestChanged = ref(false);
async function handleEditSummary() {
  ElMessageBox.prompt("请输入摘要", "编辑摘要", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputValue: state.data.digest,
    inputValidator: (value: string) => {
      return !!value;
    },
    inputErrorMessage: "请输入摘要",
    beforeClose: async (action, instance, done) => {
      try {
        if (action === "confirm") {
          // const { success, data, message } = await editSummary({ id: route.params.id as string, desc: instance.inputValue });
          // if (!success) throw new Error(message);
          // ElMessage.success("操作成功");
          // handleRefresh();
          state.data.digest = instance.inputValue;
          digestChanged.value = true;
          done();
        } else done();
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    },
  })
    .then(() => {
      /* code */
    })
    .catch(() => {
      /* code */
    });
}

const endRef = ref<InstanceType<typeof EventEnd>>();
async function handleEnd(data: Partial<DataItem>, type: string) {
  if (!endRef.value) return false;
  endRef.value.open(data, type);
  OperateChanged.value = true;
}

async function handleAlarm(data: Partial<DataItem>, type: string) {
  if (!endRef.value) return false;
  endRef.value.open(data, type);
}

async function resetChangeState() {
  priorityChanged.value = false;
  impactChanged.value = false;
  urgencyChanged.value = false;
  OperateChanged.value = false;
  digestChanged.value = false;
  userGroupChanged.value = false;
  userChanged.value = false;
}
// const pendRef = ref<InstanceType<typeof EventPend>>();
// async function handleApprove(row: Partial<DataItem>) /* 挂起 */ {
//   if (!pendRef.value) return false;
//   pendRef.value.open(state.data);
//   // try {
//   //   state.loading = true;
//   //   const { success, message, data } = await setEventDataByApprove({ id: row.id as string, approve: false });
//   //   if (!success) throw Object.assign(new Error(message), { success, data });
//   //   ElMessage.success(`成功接手事件`);
//   // } catch (error) {
//   //   if (error instanceof Error) {
//   //     const message = error.message;
//   //     await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
//   //   }
//   // } finally {
//   //   await queryData();
//   //   state.loading = false;
//   // }
// }
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
defineSlots<{}>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>

<style lang="scss">
@import "@/styles/theme/common/var.scss";
.draft-tooltip {
  background: $color-danger !important;
  border: 1px solid $color-danger;
  color: #fff;
}

.draft-tooltip .el-popper__arrow::before {
  background: $color-danger !important;
  border: 1px solid $color-danger;
}
</style>
