<template>
  <el-form :model="form" label-position="top">
    <el-form-item>
      <template #label>
        <div class="info-desc">
          <span>{{ $t("generalDetails.Description") }}</span>
          <el-button style="z-index: 1" type="primary" :disabled="(!verifyPermissionIds.includes('612916051063078912') && !verifyPermissionIds.includes('777393423094120448')) || [questionState.NEW, questionState.CLOSED, questionState.AUTO_CLOSED].includes((props.data.questionState as questionState) || ('' as questionState))" @click="handleDescEdit">{{ isEdit ? $t("generalDetails.Save") : $t("generalDetails.Edit") }}</el-button>
        </div>
      </template>
      <div class="tw-flex tw-min-h-[120px] tw-w-full tw-flex-col" v-if="isEdit" @keyup.enter.stop>
        <!-- <QuillEditor theme="snow" style="flex: 1" :content="isEdit ? form.description : props.data.description" @update:content="form.description = $event" contentType="html" toolbar="full" :enable="isEdit" :read-only="!isEdit"></QuillEditor> -->
        <el-input v-model="form.description" :rows="6" type="textarea" placeholder="请输入描述" />
      </div>
      <div class="el-input el-input__wrapper tw-flex tw-min-h-[120px] tw-w-full tw-flex-col tw-items-start tw-justify-start tw-p-4" v-else @keyup.enter.stop>
        <div v-html="props.data.description"></div>
      </div>
    </el-form-item>
    <el-form-item :label="$t('generalDetails.External ID')">
      <template #label>
        <div class="info-desc">
          <span> {{ $t("generalDetails.External ID") }}</span>
          <el-button style="z-index: 1" type="primary" :disabled="(!verifyPermissionIds.includes('612916051063078912') && !verifyPermissionIds.includes('777393423094120448')) || [questionState.NEW, questionState.CLOSED, questionState.AUTO_CLOSED].includes((props.data.questionState as questionState) || ('' as questionState))" @click="handleExternal">{{ isExternal ? $t("generalDetails.Save") : $t("generalDetails.Edit") }}</el-button>
        </div>
      </template>
      <el-input :disabled="!isExternal" :model-value="isExternal ? form.externalId : props.data.externalId" @update:model-value="form.externalId = $event"></el-input>
    </el-form-item>
    <el-form-item>
      <el-row class="el-input el-input__wrapper">
        <el-col :span="12" class="tw-h-[calc(var(--el-component-size)*3)] tw-text-left">
          <p>{{ $t("generalDetails.Modified") }}</p>
          <p class="tw-h-[var(--el-component-size)]">
            <el-icon :size="16" class="tw-mr-[6px] tw-align-middle"><UserFilled /></el-icon>{{ updated.name || "--" }}
          </p>
          <p>{{ updated.updateTime ? moment(`${updated.updateTime}`, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</p>
        </el-col>
        <el-col :span="12" class="tw-h-[calc(var(--el-component-size)*3)] tw-text-right">
          <p>{{ $t("generalDetails.Created") }}</p>
          <p class="tw-h-[var(--el-component-size)]">
            <el-icon :size="16" class="tw-mr-[6px] tw-align-middle"><UserFilled /></el-icon>{{ collector.name || "--" }}
          </p>
          <p>{{ collector.collectTime ? moment(`${collector.collectTime}`, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</p>
        </el-col>
      </el-row>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, watch, inject } from "vue";
import { useRoute, useRouter } from "vue-router";

import { ElMessage } from "element-plus";

import { UserFilled } from "@element-plus/icons-vue";
import { setQuestionDataByDescription } from "@/views/pages/apis/question";

import { QuillEditor } from "@vueup/vue-quill";
import moment from "moment";
import { questionState } from "@/views/pages/apis/question";
import getUserInfo from "@/utils/getUserInfo";
const userInfo = getUserInfo();
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);
defineOptions({ name: "ModelDetails" });

const verifyPermissionIds = inject("verifyPermissionIds") as string[];

const props = withDefaults(defineProps<{ data: Partial<import("../helper").DataItem>; height: number; refresh: () => Promise<void> }>(), { data: () => ({}) });
const emits = defineEmits(["changeDesc"]);

const route = useRoute();
const router = useRouter();

const form = ref({ description: "", externalId: "" });
const isEdit = ref(false);
const isExternal = ref(false);

const updated = reactive({ name: "", updateTime: 0 });
const collector = reactive({ name: "", collectTime: 0 });
function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == userInfo.zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
watch(
  () => props.data,
  async (data) => {
    await nextTick();
    if (data.creatorName) collector.name = JSON.parse(data.creatorName).username;
    collector.collectTime = Math.max(Number(data.createTime) || 0, 0) + timeZoneSwitching();
    try {
      updated.name = JSON.parse(data.updaterName || "{}").username || "";
    } catch (error) {
      updated.name = "";
    }
    updated.updateTime = Math.max(Number(data.updateTime) || 0, 0) + timeZoneSwitching();
  },
  { immediate: true }
);
async function handleExternal() {
  if (isExternal.value) {
    emits("changeDesc", form.value.description);
    isExternal.value = false;
    try {
      const { success, message } = await setQuestionDataByDescription({ id: props.data.id as string, description: form.value.description, externalId: form.value.externalId });
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      props.refresh();
    }
  } else {
    form.value.description = form.value.description || props.data.description || "";
    form.value.externalId = props.data.externalId || "";
    isExternal.value = true;
  }
}
async function handleDescEdit() {
  if (isEdit.value) {
    emits("changeDesc", form.value.description);
    isEdit.value = false;
    try {
      const { success, message } = await setQuestionDataByDescription({ id: props.data.id as string, description: form.value.description, externalId: form.value.externalId });
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      props.refresh();
    }
  } else {
    isEdit.value = true;
    form.value.description = form.value.description || props.data.description || "";
    form.value.externalId = props.data.externalId || "";
  }
}
</script>

<style lang="scss" scoped>
.info-desc {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
