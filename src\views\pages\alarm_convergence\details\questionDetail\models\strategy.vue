<template>
  <!-- 问题详情 -->
  <pageTemplate :show-paging="false" :height="height" class="strategys">
    <template #default>
      <el-row :gutter="20">
        <el-col :span="24" v-for="item in strategys" :key="item.id" class="tw-mb-5">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-card :class="item.workTimeFlag ? 'strategy-item' : ''" :body-style="{ padding: 0 }">
                <template #header>
                  {{ item.name }}
                </template>
                <el-scrollbar :ref="(v) => (strategyRefs[`${item.id}-left`] = v)" :height="strategyHeights[item.id]" :class="`tw-h-[${strategyHeights[item.id]}px]`">
                  <div class="pre-line tw-items-center tw-p-5 tw-pb-0" v-html="item.activeNote ?? ''"></div>
                </el-scrollbar>
                <el-divider v-if="!item.allDayFlag" />
                <div class="tw-p-5 tw-pt-0" v-if="!item.allDayFlag">
                  <span v-if="item.workTimeFlag">{{ item.activeConfig.timeForZone ? "注意：使用直到 " + item.activeConfig.timeForZone : "" }}</span>
                  <span v-else>{{ item.activeConfig.timeForZone ? "注意：从 " + item.activeConfig.timeForZone + " 开始使用" : "" }}</span>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12" v-if="!isEmptyOrNullOrEmptyTags(item.inactiveNote)">
              <el-card :class="!item.workTimeFlag ? 'strategy-item' : ''" :body-style="{ padding: 0 }">
                <template #header>
                  {{ item.name }}
                </template>
                <el-scrollbar :ref="(v) => (strategyRefs[`${item.id}-right`] = v)" :height="strategyHeights[item.id]" :class="`tw-h-[${strategyHeights[item.id]}px]`">
                  <div class="pre-line tw-items-center tw-p-5 tw-pb-0" v-html="item.inactiveNote ?? ''"></div>
                </el-scrollbar>
                <el-divider v-if="!item.allDayFlag" />
                <div class="tw-p-5 tw-pt-0" v-if="!item.allDayFlag">
                  <span v-if="!item.workTimeFlag">{{ item.activeConfig.timeForZone ? "注意：使用直到 " + item.activeConfig.timeForZone : "" }}</span>
                  <span v-else>{{ item.activeConfig.timeForZone ? "注意：从 " + item.activeConfig.timeForZone + " 开始使用" : "" }}</span>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </template>
  </pageTemplate>
</template>

<script setup lang="ts" name="event-strategy">
import pageTemplate from "@/components/pageTemplate.vue";
import { computed, onMounted, ref, toRefs, watch } from "vue";
import { getResourcesSupportNotes as getData, type SupportNotes } from "@/views/pages/apis/eventManage";
import { ElMessage } from "element-plus";
// import { EventItem } from "@/views/pages/apis/event";
import zone from "@/views/pages/common/zone.json";
import getUserInfo from "@/utils/getUserInfo";
import { timeFormat } from "@/utils/date";
import { Item } from "../helper";
import { formatDate } from "@/views/pages/common/dateChange";
import { strategyZone } from "../../../../common/strategyZone";

interface Props {
  height: number;
  data: Partial<Item>;
}

// type Item = SupportNotes & Record<"dateTime", "string">;

const props = withDefaults(defineProps<Props>(), {
  height: 0,
  data: () => <Partial<Item>>{},
});

const { height, data: detail } = toRefs(props);

watch(
  () => props.data,
  ({ deviceIds }) => {
    deviceIds && deviceIds instanceof Array && deviceIds.length && queryItems();
  },
  { immediate: true }
);

const strategys = ref<SupportNotes[]>([]);
const weekDay = ref<number>(new Date().getDay());
function isEmptyOrNullOrEmptyTags(str) {
  // 如果字符串是null或undefined，直接返回true
  // 如果字符串为 null 或 undefined，直接返回 true
  if (str === null || str === undefined) {
    return true;
  }
  // 去除字符串中的空格和 HTML 标签，然后检查是否为空
  const cleanedStr = str
    .replace(/<[^>]*>/g, "")
    .replace(/&nbsp;/g, "")
    .trim();
  // 检查 cleanedStr 是否为空
  return cleanedStr === "";
}
async function queryItems() {
  try {
    const { success, data, message } = await getData({ ids: detail.value.deviceIds?.join() as string, containsGlobal: false });
    if (!success) throw new Error(message);

    (data.showSupportNotes instanceof Array ? data.showSupportNotes : []).forEach((v, i) => {
      if (v.active) {
        let arrLength = 0;
        // if (v.activeConfig.useAutoTimeZone) {
        //   let timezoneOffset = -new Date().getTimezoneOffset();
        //   for (let i = 0; i < zone.length; i++) {
        //     if (zone[i].zoneId === getUserInfo()?.zoneId) {
        //       v.activeConfig.activeHours.forEach((h) => {
        //         if (h.weekDay == weekDay.value) {
        //           let date = new Date();
        //           let year = date.getFullYear();
        //           let mon = date.getMonth();
        //           let day = date.getDate();
        //           let time = new Date(year, mon, day, h.hours.length == 24 ? 24 : v.activeTime, "00", "00").getTime() + (timezoneOffset - zone[i].offsetMinutes) * 60 * 1000;

        //           v.dateTime = formatDate(time, "yyyy-MM-dd HH:mm");

        //           v.showWorkTime = date > new Date(v.dateTime) ? false : true;
        //           if (h.hours.length == 0) {
        //             v.dateTime = "";
        //           }
        //         }
        //       });

        //       // console.log(v.allTime);
        //       break;
        //     } else {
        //       // arrLength = 0;
        //       v.activeConfig.activeHours.forEach((h) => {
        //         if (h.weekDay == weekDay.value) {
        //           let date = new Date();
        //           let year = date.getFullYear();
        //           let mon = date.getMonth();
        //           let day = date.getDate();
        //           let time = new Date(year, mon, day, h.hours.length == 24 ? 24 : v.activeTime, "00", "00").getTime() + timezoneOffset * 60 * 1000;

        //           v.dateTime = formatDate(time, "yyyy-MM-dd HH:mm");

        //           v.showWorkTime = date > new Date(v.dateTime) ? false : true;
        //           if (h.hours.length == 0) {
        //             v.dateTime = "";
        //           }
        //         }
        //         // // console.log(arrLength);
        //       });
        //     }
        //   }
        // } else {
        //   let arrLength = 0;
        //   let timezoneOffset = -new Date().getTimezoneOffset();

        //   if (timezoneOffset === strategyZone[v.activeConfig.timeZone]) {
        //     // console.log(v);
        //     v.activeConfig.activeHours.forEach((h) => {
        //       if (h.weekDay == weekDay.value) {
        //         let date = new Date();
        //         let year = date.getFullYear();
        //         let mon = date.getMonth();
        //         let day = date.getDate();
        //         let time = new Date(year, mon, day, h.hours.length == 24 ? 24 : v.activeTime, "00", "00").getTime();
        //         v.dateTime = formatDate(time, "yyyy-MM-dd HH:mm");

        //         v.showWorkTime = date > new Date(v.dateTime) ? false : true;
        //         if (h.hours.length == 0) {
        //           v.dateTime = "";
        //         }
        //       }
        //     });
        //   } else {
        //     let arrLength = 0;
        //     let timezoneOffset = -new Date().getTimezoneOffset();

        //     zone.forEach((item) => {
        //       if (item.zoneId === v.activeConfig.timeZone) {
        //         v.activeConfig.activeHours.forEach((h) => {
        //           if (h.weekDay == weekDay.value) {
        //             let date = new Date();
        //             let year = date.getFullYear();
        //             let mon = date.getMonth();
        //             let day = date.getDate();
        //             let time = new Date(year, mon, day, h.hours.length == 24 ? 24 : v.activeTime, "00", "00").getTime() + (timezoneOffset - item.offsetMinutes) * 60 * 1000;
        //             v.dateTime = formatDate(time, "yyyy-MM-dd HH:mm");

        //             let isWokrTime = h.hours.indexOf(v.activeTime - 1 - (timezoneOffset - item.offsetMinutes) / 60);

        //             v.showWorkTime = date > new Date(v.dateTime) ? false : true;
        //             if (h.hours.length == 0) {
        //               v.dateTime = "";
        //             }
        //           }
        //         });
        //       }
        //     });
        //   }
        // }

        v.activeConfig.activeHours.forEach((h) => {
          if (h.hours.length == 24) {
            arrLength = arrLength + 1;
          }
        });

        if (arrLength === v.activeConfig.activeHours.length) {
          v.allTime = true;
        }
      }
    });

    strategys.value = data.showSupportNotes;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

const isRestDay = computed(() => {
  return weekDay.value === 6 || weekDay.value === 7;
});

const strategyRefs = ref<Record<string, any>>({});

const strategyHeights = computed(() =>
  strategys.value.reduce((p, t) => {
    return Object.assign(p, {
      [`${t.id}`]: Math.max(
        ...[
          /*  */
          ((strategyRefs.value[`${t.id}-left`] || {}).wrapRef || {}).offsetHeight || 0,
          ((strategyRefs.value[`${t.id}-right`] || {}).wrapRef || {}).offsetHeight || 0,
        ]
      ),
    });
  }, {})
);

onMounted(() => {});
</script>

<style lang="scss" scoped>
@import "@/styles/theme/common/var.scss";

.strategys .strategy-item {
  :deep(.el-card) {
    border: 1px solid $color-primary;
  }
  :deep(.el-card__header) {
    background: $color-primary;
    color: $color-white;
  }
}
</style>
