<template>
  <page-template :searchSpan="24" :headButtonSpan="24" :showPaging="false">
    <template #right>
      <el-button type="primary" :icon="Plus" :disabled="(!verifyPermissionIds.includes('777393727017582592') && !verifyPermissionIds.includes('612914626438365184')) || [serviceState.NEW, serviceState.CLOSED, serviceState.AUTO_CLOSED].includes(data.serviceState)" @click="handleAddAssociation">{{ $t("generalDetails.Add Association") }}</el-button>
    </template>
    <template #default>
      <el-table :data="tableData" stripe style="width: 100%" :height="height - 60">
        <el-table-column prop="orderType" :label="$t('generalDetails.Type')" :formatter="tableFormatter" align="left"></el-table-column>
        <el-table-column prop="orderId" :label="$t('generalDetails.Ticket')" :formatter="tableFormatter" align="left">
          <template #default="{ row }">
            <el-link type="primary" :underline="false" @click="handleToAssociationInfo(row)">{{ row.orderId }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="address" :label="$t('generalDetails.Incident Priority')" :formatter="tableFormatter" min-width="100" align="left">
          <template #default="{ row }">
            <i class="priority-icon" :style="{ background: priority[row.priority].color }" />
            <span :style="{ color: priority[row.priority].color }">{{ row.priority }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="state" :label="$t('generalDetails.Status')" :formatter="tableFormatter" align="left"></el-table-column>
        <el-table-column prop="digest" :label="$t('generalDetails.Digest')" :formatter="tableFormatter" align="left" min-width="280"></el-table-column>
        <el-table-column :label="$t('generalDetails.Operate')" width="80" align="left">
          <template #default="{ row }">
            <el-button type="text" textColor="danger" :disabled="(!verifyPermissionIds.includes('777393727017582592') && !verifyPermissionIds.includes('612914626438365184')) || [serviceState.NEW, serviceState.CLOSED, serviceState.AUTO_CLOSED].includes(data.serviceState)" @click="handleRemoveAssociation(row)">{{ $t("generalDetails.Remove") }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <event-add-association ref="eventAddAssociation" :title="$t('generalDetails.link')" :data="tableData" @refresh="handleRefreshTable" :height="height" />
    </template>
  </page-template>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { Plus } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";
import eventAddAssociation from "./createAssociation.vue";
import { eventGetAssociationList, eventRemoveAssociation } from "@/views/pages/apis/eventManage";
import { eventGetAssociation } from "@/views/pages/apis/association";
import priority from "@/views/pages/common/priority";
import { h } from "vue";
import { eventStateOption, serviceStateOption, serviceState } from "@/views/pages/apis/event";
import { changeStateOption } from "@/views/pages/apis/change";
import { questionStateOption } from "@/views/pages/apis/question";
import { eventCreateAssociationCascade, orderType, secureAssociation, OrderType as DataType } from "@/views/pages/apis/association";
import useCurrentInstance from "@/utils/useCurrentInstance";
import getUserInfo from "@/utils/getUserInfo";
import { useI18n } from "vue-i18n";
export default {
  components: { pageTemplate, eventAddAssociation },
  inject: ["verifyPermissionIds"],
  props: {
    height: {
      type: [Number, String],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    refresh: Function,
  },
  data() {
    return {
      Plus,
      priority,
      paging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      proxy: useCurrentInstance().proxy,
      userInfo: getUserInfo(),
      serviceState,
      i18n: useI18n(),
    };
  },
  created() {
    this.handleRefreshTable();
  },
  methods: {
    handleToAssociationInfo(v) {
      switch (v.orderType) {
        case DataType.QUESTION:
          this.$router.push({ name: "515035822471249920", params: { id: v.orderId }, query: { fallback: this.$route.query.fallback, tenant: v.tenantId } });
          break;
        case DataType.EVENT_ORDER:
          this.$router.push({ name: "510685950393712640", params: { id: v.orderId }, query: { fallback: this.$route.query.fallback, tenant: v.tenantId } });
          break;
        case DataType.SERVICE_REQUEST:
          this.$router.push({ name: "514703398516293632", params: { id: v.orderId }, query: { fallback: this.$route.query.fallback, tenant: v.tenantId } });
          setTimeout(() => {
            this.proxy.eventBus.emit("onTabViewRefresh", this.$route);
          }, 0);
          break;
        case DataType.CHANGE:
          this.$router.push({ name: "515123784953364480", params: { id: v.orderId }, query: { fallback: this.$route.query.fallback, tenant: v.tenantId } });
          break;
        default:
          break;
      }
    },
    handleRefreshTable() {
      this.refresh();
      const params = {
        orderId: this.$route.params.id,
      };
      eventGetAssociation(params).then(({ success, data, total }) => {
        if (success) {
          this.tableData = data;
          this.paging.total = Number(total);
        }
      });
    },
    handleRemoveAssociation(row) {
      this.$confirm(`${this.i18n.t("generalDetails.Confirm to remove the associated work order")}?`, `${this.i18n.t("generalDetails.prompt")}`, {
        confirmButtonText: `${this.i18n.t("generalDetails.confirm")}`,
        cancelButtonText: `${this.i18n.t("generalDetails.Cancel")}`,
        type: "warning",
      })
        .then(() => {
          secureAssociation({ fromId: this.$route.params.id, toId: row.orderId }).then(({ success, data }) => {
            if (success) {
              this.$message.success(`${this.i18n.t("generalDetails.Operation successful")}`);
              this.handleRefreshTable();
            } else this.$message.error(JSON.parse(data)?.message || `${this.i18n.t("generalDetails.Operation failed")}`);
          });
        })
        .catch(() => {
          // ...code
        });
    },
    async handleAddAssociation() {
      // ...code
      // this.$refs.eventAddAssociation.open();

      const { open } = this.$refs.eventAddAssociation;
      if (!open) return false;
      await open({}, async (form) => {
        try {
          const { success, message } = await eventCreateAssociationCascade({ fromId: this.$route.params.id, toId: form.id });
          if (!success) throw new Error(message);
          this.$message.success(`${this.i18n.t("generalDetails.Operation successful")}`);
          this.handleRefreshTable();
        } catch (error) {
          error instanceof Error && this.$message.error(error.message);
        }
      });
    },
    tableFormatter(_row, _col, v) {
      switch (_col.property) {
        case "orderType":
          return (orderType.find((f) => f.value === v) || {}).label || "--";
        case "associateType":
          // eslint-disable-next-line no-case-declarations
          const type = {
            EVENT: "事件",
          };
          return type[v] || "--";
        case "state":
          if (_row.orderType === DataType.EVENT_ORDER) return eventStateOption.find((f) => f.value === v)?.label || "--";
          else if (_row.orderType === DataType.SERVICE_REQUEST) return serviceStateOption.find((f) => f.value === v)?.label || "--";
          else if (_row.orderType === DataType.CHANGE) return changeStateOption.find((f) => f.value === v)?.label || "--";
          else if (_row.orderType === DataType.QUESTION) return questionStateOption.find((f) => f.value === v)?.label || "--";
          else return "--";
        default:
          return v || "--";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.priority-icon {
  width: 10px;
  height: 10px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 10px;
}
</style>
