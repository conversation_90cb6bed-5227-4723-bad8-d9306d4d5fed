<template>
  <el-form :model="form" label-position="top">
    <el-form-item>
      <template #label>
        <div class="info-desc">
          <span>{{ $t("generalDetails.Description") }}</span>
          <el-button style="z-index: 1" type="primary" :disabled="!verifyPermissionIds.includes('612914331784314880') || [serviceState.NEW, serviceState.CLOSED, serviceState.AUTO_CLOSED].includes((props.data.serviceState as serviceState) || ('' as serviceState))" @click="handleDescEdit('description')">{{ isEdit ? `${t("generalDetails.Save")}` : `${t("generalDetails.Edit")}` }}</el-button>
        </div>
      </template>
      <div class="tw-flex tw-min-h-[120px] tw-w-full tw-flex-col" v-if="isEdit" @keyup.enter.stop>
        <!-- <QuillEditor theme="snow" style="flex: 1" :content="isEdit ? form.description : props.data.description" @update:content="form.description = $event" contentType="html" toolbar="full" :enable="isEdit" :read-only="!isEdit"></QuillEditor> -->
        <el-input v-model="form.description" :rows="6" type="textarea" :placeholder="$t('generalDetails.Please enter description')" />
      </div>
      <div class="el-input el-input__wrapper tw-flex tw-min-h-[120px] tw-w-full tw-flex-col tw-items-start tw-justify-start tw-p-4" v-else @keyup.enter.stop>
        <div v-html="props.data.description"></div>
      </div>
    </el-form-item>
    <el-form-item :label="$t('generalDetails.External ID')">
      <template #label>
        <div class="info-desc">
          <span>{{ $t("generalDetails.External ID") }}</span>
          <el-button style="z-index: 1" type="primary" :disabled="!verifyPermissionIds.includes('612914331784314880') || [serviceState.NEW, serviceState.CLOSED, serviceState.AUTO_CLOSED].includes((props.data.serviceState as serviceState) || ('' as serviceState))" @click="handleDescEdit('externalId')">{{ isServiceCode ? `${t("generalDetails.Save")}` : `${t("generalDetails.Edit")}` }}</el-button>
        </div>
      </template>
      <el-input :model-value="form.externalId" @update:model-value="form.externalId = $event" :disabled="!isServiceCode"></el-input>
    </el-form-item>
    <el-form-item>
      <el-row class="el-input el-input__wrapper">
        <el-col :span="12" class="tw-h-[calc(var(--el-component-size)*3)] tw-text-left">
          <p>{{ $t("generalDetails.Modified") }}</p>
          <p class="tw-h-[var(--el-component-size)]">
            <el-icon :size="16" class="tw-mr-[6px] tw-align-middle"><UserFilled /></el-icon>{{ updated.name || "--" }}
          </p>
          <p>{{ updated.updateTime ? moment(`${updated.updateTime}`, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</p>
        </el-col>
        <el-col :span="12" class="tw-h-[calc(var(--el-component-size)*3)] tw-text-right">
          <p>{{ $t("generalDetails.Created") }}</p>
          <p class="tw-h-[var(--el-component-size)]">
            <el-icon :size="16" class="tw-mr-[6px] tw-align-middle"><UserFilled /></el-icon>{{ collector.name || "--" }}
          </p>
          <p>{{ collector.collectTime ? moment(`${collector.collectTime}`, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</p>
        </el-col>
      </el-row>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, watch, inject } from "vue";
import { useRoute, useRouter } from "vue-router";

import { ElMessage } from "element-plus";

import { UserFilled } from "@element-plus/icons-vue";
// import { QuillEditor } from "@vueup/vue-quill";
import moment from "moment";
import { setServiceRequestDataByDescription } from "@/views/pages/apis/serviceRequest";
import { serviceState } from "@/views/pages/apis/event";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
defineOptions({ name: "ModelDetails" });
import getUserInfo from "@/utils/getUserInfo";
const userInfo = getUserInfo();
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);

const props = withDefaults(defineProps<{ data: Partial<import("../helper").DataItem>; height: number; refresh: () => Promise<void> }>(), { data: () => ({}) });
const emits = defineEmits(["changeDesc"]);

const route = useRoute();
const router = useRouter();

const verifyPermissionIds: string[] = inject("verifyPermissionIds") || [];

const form = ref({ description: "", externalId: "" });
const isEdit = ref(false);

const isServiceCode = ref(false);

const updated = reactive({ name: "", updateTime: 0 });
const collector = reactive({ name: "", collectTime: 0 });
function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == userInfo.zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
watch(
  () => props.data,
  async (data) => {
    await nextTick();
    if (data.createdBy) collector.name = JSON.parse(data.createdBy)?.username;
    collector.collectTime = Math.max(Number(data.createTime) || 0, 0) + timeZoneSwitching();
    try {
      updated.name = JSON.parse(data.updatedBy || "{}").username || "";
    } catch (error) {
      updated.name = "";
    }
    updated.updateTime = Math.max(Number(data.updateTime) || 0, 0) + timeZoneSwitching();
    form.value.externalId = props.data.externalId || "";
  },
  { immediate: true }
);

async function handleDescEdit(type: "description" | "externalId") {
  if (type === "description" ? isEdit.value : isServiceCode.value) {
    emits("changeDesc", form.value.description);
    if (type === "description") isEdit.value = false;
    else isServiceCode.value = false;
    try {
      const { success, message } = await setServiceRequestDataByDescription({ id: props.data.id as string, description: form.value.description, externalId: form.value.externalId });
      if (!success) throw new Error(message);
      ElMessage.success(t("generalDetails.Operation successful"));
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      props.refresh();
    }
  } else {
    if (type === "description") isEdit.value = true;
    else isServiceCode.value = true;
    form.value.description = form.value.description || props.data.description || "";
    form.value.externalId = props.data.externalId || "";
  }
}

async function handleExternal() {
  if (isExternal.value) {
    emits("changeDesc", form.value.description);
    isExternal.value = false;
    try {
      const { success, message } = await setServiceRequestDataByDescription({ id: props.data.id as string, description: form.value.description, externalId: form.value.externalId });
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      props.refresh();
    }
  } else {
    form.value.description = props.data.description || "";
    form.value.externalId = props.data.externalId || "";
    isExternal.value = true;
  }
}
</script>

<style lang="scss" scoped>
.info-desc {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
