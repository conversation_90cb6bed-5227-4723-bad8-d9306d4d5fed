<template>
  <el-table stripe :data="tableData || []" :height="550">
    <!-- <el-table-column prop="dynamicCreateTime" :label="$t('generalDetails.Time')" width="300">
      <template #default="{ row }">{{ moment(row.dynamicCreateTime, "x").format("YYYY-MM-DD HH:mm:ss") }}</template>
    </el-table-column>
    <el-table-column prop="dynamicContent" :label="$t('generalDetails.Dynamic state')">
      <template #default="{ row }">
        <div v-if="!row.eventTransferName">
          {{ row.eventCreatorName }}
          {{ row.eventTransferName }}
          {{ row.dynamicContent }}
        </div>
        <div v-else>
          {{ row.eventCreatorName }}
          {{ row.dynamicContent }}
          {{ row.eventTransferName }}
        </div>
      </template>
    </el-table-column> -->
    <el-table-column prop="operation" label="操作类型">
      <template #default="{ row }">
        {{ getOperationLabel(row.operation) }}
      </template>
    </el-table-column>
    <el-table-column prop="property" label="属性">
      <template #default="{ row }">
        {{ row.property }}
      </template>
    </el-table-column>
    <el-table-column prop="dynamicContent" label="原有值">
      <template #default="{ row }">
        {{ row.oldValue }}
      </template>
    </el-table-column>
    <el-table-column prop="dynamicContent" label="更改后">
      <template #default="{ row }">
        {{ row.newValue }}
      </template>
    </el-table-column>
    <el-table-column prop="dynamicContent" label="创建人">
      <template #default="{ row }">
        {{ row.eventCreatorName }}
      </template>
    </el-table-column>
    <el-table-column prop="dynamicCreateTime" label="时间戳">
      <template #default="{ row }">{{ moment(row.dynamicCreateTime, "x").format("YYYY-MM-DD HH:mm:ss") }}</template>
    </el-table-column>
  </el-table>
</template>

<script lang="ts" setup>
import moment from "moment";
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";

import { getRequestTrendList, type Dynamic } from "@/views/pages/apis/eventManageTrends";
defineOptions({ name: "ModelDynamics" });
import getUserInfo from "@/utils/getUserInfo";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const route = useRoute();
const userInfo = getUserInfo();
const props = withDefaults(defineProps<{ data: Partial<import("../helper").DataItem>; height: number; refresh: () => Promise<void> }>(), { data: () => ({}) });
const tableData = ref<Dynamic[]>([]);
getTrendsList();
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);

const operationTypeList = ref([
  { label: "新建", value: "CREATE" },
  { label: "确认告警", value: "CONFIRM_ALARM" },
  { label: "关闭", value: "CLOSED" },
  { label: "新增小记", value: "ADD_NOTE" },
  { label: "编辑小记", value: "EDIT_NOTE" },
  { label: "删除小记", value: "DELETE_NOTE" },
  { label: "处理中", value: "PROCESSING" },
  { label: "挂起中", value: "SUSPENDED" },
  { label: "重新将已完成或者关闭的工单改成处理中处理中", value: "CREATE" },
  { label: "已解决或完成", value: "COMPLETED" },
  { label: "更新", value: "UPDATE" },
  //todo
  { label: "结束挂起", value: "UN_SUSPENDED" },
  { label: "处理", value: "TAKE_OVER" },
  { label: "转派", value: "TRANSFER" },
  { label: "转派用户", value: "TRANSFER_USER" },
  { label: "转派用户组", value: "TRANSFER_GROUP" },
  { label: "完结", value: "FINISHED" },
  { label: "挂起审批不通过", value: "SUSPEND_APPROVE_REJECT" },
  { label: "挂起审批通过", value: "SUSPEND_APPROVE_PASS" },
  { label: "修改优先级", value: "CHANGE_PRIORITY" },
  { label: "更新工单标题、详述", value: "UPDATE_TITLE_DESCRIPTION" },
  { label: "事件状态更改", value: "CHANGE_STATUS" },
  { label: "分配用户组", value: "ASSIGN_GROUP" },
  { label: "分配用户", value: "ASSIGN_USER" },
  { label: "事件升级", value: "UPGRADE" },
  { label: "审批通过", value: "APPROVE" },
  { label: "审批拒绝", value: "REJECT" },
  { label: "分配设备", value: "ASSIGN_DEVICE" },
  { label: "分配联系人", value: "ASSIGN_CONTACT" },
  { label: "文件管理", value: "FILE_MANAGE" },
  { label: "新增关联", value: "ADD_RELATION" },
  { label: "事件超时", value: "TIMEOUT" },
  { label: "挂起待审批中", value: "PENDING_APPROVAL" },
  { label: "自动关闭", value: "AUTO_CLOSED" },
  { label: "响应超时", value: "RESPONSE_TIMEOUT" },
  { label: "处理超时", value: "RESOLVE_TIMEOUT" },
  { label: "处理更新超时", value: "RESOLVE_UPDATE_TIMEOUT" },
  { label: "挂起更新超时", value: "SUSPEND_UPDATE_TIMEOUT" },
  { label: "全部", value: "ALL" },
]);

// 根据操作类型的value值获取对应的label
function getOperationLabel(operationValue: string): string {
  const operation = operationTypeList.value.find((item) => item.value === operationValue);
  return operation ? operation.label : operationValue; // 如果找不到对应的label，返回原始值
}

function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == getUserInfo().zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}

function getTrendsList() {
  getRequestTrendList({ id: route.params.id, tenantId: userInfo.currentTenantId }).then((res: any) => {
    if (res.success) {
      tableData.value = [...res.data].map((item: any) => {
        return {
          ...item,
          dynamicCreateTime: Number(item.dynamicCreateTime) + timeZoneSwitching(),
        };
      });
    }
  });
}
</script>

<style lang="scss" scoped></style>
