<template>
  <el-dialog :title="$t('generalDetails.Pending')" v-model="dialogFormVisible" :before-close="beforeClose" width="800px">
    <el-form ref="form" :model="form" label-width="100px" :rules="rules">
      <el-alert type="warning" show-icon :title="maxSuspensionTimeMessageTitle"></el-alert>

      <el-form-item :label="$t('generalDetails.Pending Time')" :style="{ marginTop: '10px' }">
        <div :style="{ display: 'flex', justifyContent: 'space-between' }">
          <el-input-number v-model.number="form.hangUpDay" :max="56" :min="0" :style="{ width: '175px' }" @change="handleDayChange"></el-input-number>
          <span class="tw-mx-3">{{ $t("generalDetails.D") }}</span>
          <el-input-number v-model.number="form.hangUpHour" :max="hangUpHourMax" :min="0" :style="{ width: '175px' }"></el-input-number>
          <span class="tw-mx-3">{{ $t("generalDetails.H") }}</span>
          <el-input-number v-model.number="form.hangUpMinute" :max="hangUpMinuteMax" :min="0" :style="{ width: '175px' }"></el-input-number>
          <span class="tw-mx-3">{{ $t("generalDetails.Min") }}</span>
        </div>
      </el-form-item>

      <el-form-item :label="$t('generalDetails.Pending Reason')">
        <div class="tw-flex tw-min-h-[300px] tw-w-full tw-flex-col">
          <QuillEditor theme="snow" style="flex: 1" :content="form.cause" @update:content="form.cause = $event" contentType="html" toolbar="full" :enable="true"></QuillEditor>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t("generalDetails.Cancel") }}</el-button>
        <el-button type="primary" :loading="butLoading" @click="submitForm()">{{ $t("generalDetails.confirm") }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { eventPend } from "@/views/pages/apis/eventManage";
import { QuillEditor } from "@vueup/vue-quill";
// import editor from "@/components/Editor/indexEditor.vue";
import { useI18n } from "vue-i18n";

import { getOrderGroupFindConfiguration, AssignableTicketType, maxSuspensionTimeOption } from "@/views/pages/apis/orderGroup";
import getUserInfo from "@/utils/getUserInfo";
export default {
  components: { QuillEditor },
  props: {
    refresh: Function,
    ticketTemplateId: String,
  },
  data() {
    return {
      dialogFormVisible: false,
      form: {
        hangUpDay: 0,
        hangUpHour: 1,
        hangUpMinute: 0,
        cause: "",
      },
      event: {},
      butLoading: false,
      hangUpHourMax: 23,
      hangUpMinuteMax: 59,
      suspendType: "",
      i18n: useI18n(),
      userInfo: getUserInfo(),
      maxSuspensionTime: 0,
    };
  },
  computed: {
    rules() {
      return {};
    },
    maxSuspensionTimeMessageTitle() {
      const currentOption = maxSuspensionTimeOption.find((v) => v.value === `${this.maxSuspensionTime}`);
      return currentOption ? `挂起超过${currentOption.label}将需要管理员审批，最多挂起不超过56天。` : "挂起需要审批，最长挂起不超过8weeks（56天）。";
    },
  },
  created() {},
  methods: {
    async handleGetMaxSuspensionTime() {
      try {
        const { data, message, success } = await getOrderGroupFindConfiguration({ tenantId: (this.userInfo.currentTenant || {}).id, type: AssignableTicketType.service, ticketTemplateId: this.ticketTemplateId });
        if (!success) throw new Error(message);
        this.maxSuspensionTime = data;
      } catch (error) {
        error instanceof Error && this.$message.error(error.message);
      }
    },
    handleDayChange(v) {
      if (v === 56) {
        this.form.hangUpHour = 0;
        this.form.hangUpMinute = 0;
        this.hangUpHourMax = 0;
        this.hangUpMinuteMax = 0;
      } else {
        this.hangUpHourMax = 23;
        this.hangUpMinuteMax = 59;
      }
    },
    beforeClose(done) {
      this.$refs.form.clearValidate();
      this.$refs.form.resetFields();
      if (done instanceof Function) done();
      else this.dialogFormVisible = false;
    },
    submitForm() {
      // this.$refs.form.validate((valid) => {
      //   if (!valid) return false;
      //   const params = {
      //     eventId: this.event.id,
      //     cause: this.form.cause,
      //     durationMinutes: (this.form.hangUpDay || 0) * 24 * 60 + (this.form.hangUpHour || 0) * 60 + this.form.hangUpMinute || 0,
      //   };
      //   eventPend(params).then(({ success, data }) => {
      //     if (success) {
      //       this.$message.success("操作成功");
      //       this.refresh instanceof Function && this.refresh();
      //       this.beforeClose();
      //     } else this.$message.error(JSON.parse(data)?.message || "操作失败");
      //   });
      // });
      this.$refs.form.validate(async (valid) => {
        if (!valid) return false;
        try {
          this.$emit("pend", { params: { cause: this.form.cause, durationMinutes: (this.form.hangUpDay || 0) * 24 * 60 + (this.form.hangUpHour || 0) * 60 + this.form.hangUpMinute || 0 }, type: this.suspendType });
          this.beforeClose();
        } catch (error) {
          error instanceof Error && this.$message.error(error.message);
        }
      });
    },
    open(v, suspendType) {
      this.handleGetMaxSuspensionTime();

      console.log(v, suspendType, "0000");

      this.event = v;
      this.dialogFormVisible = true;
      this.suspendType = suspendType;
    },
  },
};
</script>
