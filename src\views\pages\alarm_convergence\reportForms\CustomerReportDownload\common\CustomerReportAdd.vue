<template>
  <div>
    <el-dialog :title="title" v-model="dialogFormVisible" :before-close="handleClose" width="50vw">
      <el-form :model="form" :rules="rules" ref="ruleForm">
        <el-form-item label="名称:" :label-width="formLabelWidth" prop="name">
          <el-input v-model="form.name" autocomplete="off" placeholder="请输入名称"></el-input>
        </el-form-item>
        <el-form-item label="描述:" :label-width="formLabelWidth" prop="description">
          <el-input type="textarea" v-model="form.description" autocomplete="off" :rows="2" placeholder="请输入描述"></el-input>
        </el-form-item>
        <!-- <el-form-item v-if="type == 'add'" label="选择安全目录:" prop="containerId" :label-width="formLabelWidth">
          <treeAuth ref="treeAuthRef" :treeStyle="treeStyle"></treeAuth>
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel()">取 消</el-button>
          <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent } from "vue";
import { ElMessage, ElMenuItem } from "element-plus";

import { AddReportsCustomer, editReportsCustomer } from "@/views/pages/apis/reportsCustomerReportDownload";
// import treeAuth from "@/components/treeAuth/index.vue";

export default defineComponent({
  name: "CustomerReportAdd",
  components: {
    // treeAuth,
  },
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      form: {
        // containerId: "",
        name: "",
        description: "",
      },
      containerIdS: null,
      rules: {
        name: [{ required: true, message: "请输入模版名称", trigger: "blur" }],
        // containerId: [
        //   {
        //     required: true,
        //     validator: (rule, value, callback) => {
        //       if (this.$refs.treeAuthRef.treeItem.id) callback();
        //       else callback(new Error("请选择安全目录"));
        //     },
        //   },
        // ],
      },
      title: "",
      checked: false,
      dialogFormVisible: false,
      formLabelWidth: "130px",
      type: "",
      value: "",
      disabled: "",
      treeStyle: {
        width: "300px",
        height: "150px",
      },
    };
  },
  watch: {
    dialog(val) {
      this.dialogFormVisible = val;
    },
    type(val) {
      if (val === "add") {
        for (var key in this.form) {
          this.form[key] = null;
        }
      }
    },
  },
  created() {},
  methods: {
    confirm(formName) {
      // if (this.type == "add") {
      //   this.form.containerId = this.$refs.treeAuthRef.treeItem.id;
      //   if (!this.form.containerId) {
      //     ElMessage.error("请选择安全目录");
      //     return;
      //   }
      // } else {
      //   this.form.containerId = "";
      // }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.type === "add") {
            AddReportsCustomer(this.form)
              .then((res) => {
                if (res.success) {
                  ElMessage.success("新增成功");
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                  // this.$refs.treeAuthRef.getSafeContaine();
                  // this.$refs.treeAuthRef.treeId = -1;
                  // this.$refs.treeAuthRef.treeItem.id = "";
                } else {
                  ElMessage.error(JSON.parse(res.data)?.message);
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                  // this.$refs.treeAuthRef.getSafeContaine();
                  // this.$refs.treeAuthRef.treeId = -1;
                  // this.$refs.treeAuthRef.treeItem.id = "";
                }
              })
              .catch((e) => {
                if (e instanceof Error) ElMessage.error(e.message);
                // this.$refs.treeAuthRef.getSafeContaine();
                // this.$refs.treeAuthRef.treeId = -1;
                this.$refs[formName].resetFields();
                // this.$refs.treeAuthRef.treeItem.id = "";
                // this.$emit("dialogClose", false);
              });
          } else {
            editReportsCustomer(this.form)
              .then((res) => {
                if (res.success) {
                  ElMessage.success("修改成功");
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                } else {
                  ElMessage.error(JSON.parse(res.data)?.message);
                  this.$emit("dialogClose", false);
                }
              })
              .catch((e) => {
                if (e instanceof Error) ElMessage.error(e.message);
                // this.$emit("dialogClose", false);
              });
          }
          // this.dialogFormVisible = false;
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      if (this.type == "add") {
        // this.$refs.treeAuthRef.getSafeContaine();
        // this.$refs.treeAuthRef.treeId = -1;
      }
    },
    cancel() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      if (this.type == "add") {
        // this.$refs.treeAuthRef.getSafeContaine();
        // this.$refs.treeAuthRef.treeId = -1;
      }
    },
    blurChange(data) {
      this.tags.push(data);
    },
    tagClose(index) {
      this.tags.splice(index, 1);
    },
  },
  expose: ["type", "disabled", "title", "form", "value"],
});
</script>
