<template>
  <el-scrollbar :height="550" v-loading="boxLoading">
    <div class="box_F">
      <!-- {{ CustomerReport.id }} -->
      <h1 class="topH">插入图表</h1>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="demo-collapse">
            <el-collapse v-model="activeName" accordion @change="handleChange">
              <el-collapse-item v-for="parent in templateConfiguration" :key="parent.id" :title="parent.serviceName" :name="parent.id" @click="clickColl(parent.serviceName)">
                <div v-loading="sunloading">
                  <div class="hover_item" v-for="item in templateConfigurationSun" :key="item.id" style="margin: 10px 0 10px 0" @click="selectItem(item.id, item.serviceTypeName)" :class="{ 'selected-item': selectedItem === item.id }">
                    <p style="cursor: pointer">{{ item.serviceTypeName }}</p>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-col>
        <el-col :span="16">
          <div class="costomright">
            <!-- top -->
            <div class="righttop">
              <el-scrollbar :height="350">
                <el-tabs type="card" v-model="activetabsName" @tab-click="handleClick">
                  <!-- 图表部分 -->
                  <el-tab-pane label="图表" name="first" :disabled="typeDisabled">
                    <template v-if="chartList.length > 0">
                      <el-row :gutter="20" style="margin-top: 10px" v-for="(item, index) in chartList" :key="index" @click="selectRow(item, item.id)">
                        <el-col :span="2" style="display: flex; justify-content: center; align-items: center">
                          <img v-if="item.type == 'lateralChart'" style="width: 40px; height: 40px" src="./img/barchart.jpg" :fit="fit" />
                          <img v-else-if="item.type == 'lineChart'" style="width: 40px; height: 40px" src="./img/linechart.jpg" :fit="fit" />
                          <img v-else-if="item.type == 'barChart'" style="width: 40px; height: 40px" src="./img/barchart.jpg" :fit="fit" />
                          <img v-else-if="item.type == 'linesChart'" style="width: 40px; height: 40px" src="./img/linechart.jpg" :fit="fit" />
                          <img v-else style="width: 40px; height: 40px" src="./img/piechart.jpg" :fit="fit" />
                        </el-col>
                        <el-col v-loading="chartLoading" :span="16" :class="{ 'selected-row': selectedRow === item.id }" style="cursor: pointer">
                          <p style="font-weight: 700; font-size: 15px">{{ item.name }}</p>
                          <p style="font-weight: 700; font-size: 13px">({{ item.description }})</p>
                          <!-- <p v-else style="font-weight: 700; font-size: 15px">{{ item.name }} ({{ item.description }})</p> -->
                          <p>{{ item.remark }}</p>
                        </el-col>
                        <el-col v-if="topShow" :span="4" :class="{ 'selected-row': selectedRow === item.id }" style="display: flex; justify-content: center; align-items: center">
                          <el-select v-model="item.topNum" placeholder="请选择" @change="statusChange(item)">
                            <el-option v-for="item1 in statusList" :key="item1.value" :label="item1.label" :value="item1.value"> </el-option>
                          </el-select>
                        </el-col>
                      </el-row>
                    </template>
                    <template v-else>
                      <el-empty :image-size="200" />
                    </template>
                  </el-tab-pane>
                  <!-- Excel部分 -->
                  <el-tab-pane label="Excel" name="second">
                    <template v-if="tablechartList.length > 0">
                      <el-row :gutter="20" style="margin-top: 10px" v-for="(item, index) in tablechartList" :key="index" @click="selectTableRow(item, item.id)">
                        <el-col :span="2" style="display: flex; justify-content: center; align-items: center">
                          <img style="width: 40px; height: 40px" src="./img/excel.jpg" :fit="fit" />
                        </el-col>
                        <el-col :span="16" :class="{ 'selected-row': selectedTableRow === item.id }" style="cursor: pointer">
                          <!-- <template v-if="typeName == 'Interface'"> -->
                          <p style="font-weight: 700; font-size: 15px">{{ item.name }}</p>
                          <p v-if="typeName !== 'IPSLA'" style="font-weight: 700; font-size: 13px">({{ item.description }})</p>
                          <!-- </template> -->
                          <!-- <p v-else style="font-weight: 700; font-size: 15px">{{ item.name }} ({{ item.description }})</p> -->
                          <p>{{ item.remark }}</p>
                        </el-col>
                        <el-col v-if="topShow" :span="4" :class="{ 'selected-row': selectedTableRow === item.id }" style="display: flex; justify-content: center; align-items: center">
                          <el-select v-model="item.topNum" placeholder="请选择" @change="statusChange(item)">
                            <el-option v-for="item1 in statusList" :key="item1.value" :label="item1.label" :value="item1.value"> </el-option>
                          </el-select>
                        </el-col>
                      </el-row>
                    </template>

                    <template v-else>
                      <el-empty :image-size="200" />
                    </template>
                  </el-tab-pane>
                  <!-- 表格部分 -->
                  <el-tab-pane label="表格" name="third">
                    <template v-if="tableReportlist.length > 0">
                      <el-row :gutter="20" style="margin-top: 10px" v-for="(item, index) in tableReportlist" :key="index" @click="selectTableReportRow(item, item.id)">
                        <el-col :span="2" style="display: flex; justify-content: center; align-items: center">
                          <img style="width: 40px; height: 40px" src="./img/table.png" :fit="fit" />
                        </el-col>
                        <el-col :span="16" :class="{ 'selected-row': selectedtableReportRow === item.id }" style="cursor: pointer">
                          <p style="font-weight: 700; font-size: 15px">{{ item.name }}</p>
                          <p v-if="typeName !== 'IPSLA'" style="font-weight: 700; font-size: 13px">({{ item.description }})</p>
                          <p>{{ item.remark }}</p>
                        </el-col>
                        <el-col v-if="topShow" :span="4" :class="{ 'selected-row': selectedtableReportRow === item.id }" style="display: flex; justify-content: center; align-items: center">
                          <el-select v-model="item.topNum" placeholder="请选择" @change="statusChange(item)">
                            <el-option v-for="item1 in statusList" :key="item1.value" :label="item1.label" :value="item1.value"> </el-option>
                          </el-select>
                        </el-col>
                      </el-row>
                    </template>

                    <template v-else>
                      <el-empty :image-size="200" />
                    </template>
                  </el-tab-pane>
                </el-tabs>
              </el-scrollbar>
            </div>
            <!-- border-bottom -->
            <div class="rightbottom">
              <el-button v-show="groupTypeButton" v-for="(button, index) in buttons" :key="index" :type="selectedButton === index ? 'primary' : 'default'" @click="selectButtonType(index)">
                {{ button.label }}
              </el-button>
              <el-button type="primary" @click="selectButton()"> 插入 </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-scrollbar>
  <!-- 预览图表 -->
  <el-scrollbar v-if="previewItems.length > 0">
    <el-row style="margin-bottom: 10px">
      <el-col :span="22">
        <h1 class="imgH" style="font-weight: 700; margin-left: 20px">预览图表</h1>
      </el-col>
      <el-col :span="2">
        <el-button @click="clearAllItems">一键清除</el-button>
      </el-col>
    </el-row>
    <div class="PreviewArea" v-loading="PreviewLoading">
      <el-row v-for="item in previewItems" :key="item.id">
        <el-col :span="12" style="width: 100%; margin-left: 30px">
          <p style="font-weight: 700; font-size: 16px">{{ item.name }}({{ item.description }})</p>
          <p style="font-weight: 700; font-size: 13px; margin-top: 10px">
            {{ item.group === "device" ? "设备" : item.group === "deviceType" ? "设备类型" : item.group === "region" ? "区域" : item.group === "location" ? "场所" : "" }}
          </p>

          <img :src="item.base64DesignSketch" />
          <p style="margin-bottom: 10px">{{ item.description }}</p>
        </el-col>
        <el-col :span="6">
          <el-button class="chartbtn" @click="removeItem(index, item)">取消</el-button>
        </el-col>
      </el-row>
    </div>
  </el-scrollbar>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent, ref, toValue, h, provide, computed, watch, toRef } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getReportsCustomerTypeList, getReportsCustomerdetails, getReportsCustomerServiceList, getReportsCustomerchartFromList, getReportsInsert, getReportsDel, getReportsALLDel } from "@/views/pages/apis/reportsCustomerReportDownload";

export default defineComponent({
  name: "CustomerReportDetails",
  props: {
    // eslint-disable-next-line vue/prop-name-casing
    CustomerReport: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      activeName: "",
      activetabsName: "first",
      selectedItem: "",
      typeName: "",
      selectedButton: null,
      selectedRow: null,
      selectedTableRow: null,
      selectedtableReportRow: null,
      previewItems: [], //图片list
      previewTableItems: [], //表格list
      sunloading: true,
      chartLoading: true,
      boxLoading: true,
      PreviewLoading: true,
      typeDisabled: false,
      templateConfiguration: [],
      templateConfigurationSun: [],
      configObj: {},
      statusType: "5",
      checkboxGroup1: "图表",
      groupTypeButton: true,
      topShow: true,
      url: "https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg",
      // top列表
      statusList: [
        {
          label: "TOP5",
          value: 5,
        },
        {
          label: "TOP10",
          value: 10,
        },
        {
          label: "TOP20",
          value: 20,
        },
        {
          label: "TOP25",
          value: 25,
        },
        {
          label: "TOP50",
          value: 50,
        },
        {
          label: "TOP100",
          value: 100,
        },
      ],
      chartList: [],
      //Excel列表
      tablechartList: [],
      //表格列表
      tableReportlist: [],

      buttons: [{ label: "按设备分组" }, { label: "按设备类型" }, { label: "按区域" }, { label: "按场所" }],
    };
  },
  watch: {},
  created() {
    this.getdetailsList();
    this.getReportsCustomerParentList();
  },
  methods: {
    //change改变的时候去查询对应的子类
    handleChange(val) {
      this.getReportsCustomerSunList(val);
    },
    clickColl(serviceName) {
      if (serviceName == "服务台") {
        this.groupTypeButton = false;
        this.topShow = false;
      } else {
        this.groupTypeButton = true;
        this.topShow = true;
      }
    },
    changeConfig(val) {},
    selectItem(itemId, itemName) {
      const excludedItems = ["Incidents", "Tickets", "Change", "Problem", "Request"];
      this.groupTypeButton = !excludedItems.includes(itemName);
      this.typeName = itemName;
      const disabledItems = ["Netflow&Sflow", "IPSLA", "QOS"];
      if (disabledItems.includes(itemName)) {
        this.typeDisabled = true;
        this.activetabsName = "second";
      } else {
        this.typeDisabled = false;
        this.activetabsName = "first";
      }

      this.selectedItem = itemId;
      this.getReportsChartList(itemId);
    },
    //查询父类
    async getReportsCustomerParentList() {
      const { success, message, data, code } = await getReportsCustomerServiceList({});
      if (code == 200) {
        this.templateConfiguration = data;
        this.activeName = data[0].id;
        this.handleChange(this.activeName);
      }
    },

    //查询子类
    async getReportsCustomerSunList(val) {
      this.sunloading = true;
      try {
        const { data, code } = await getReportsCustomerTypeList({ serviceId: val });
        if (code == 200) {
          this.sunloading = false;
        }

        this.templateConfigurationSun = data;
        this.selectedItem = data[0].id;
        this.getReportsChartList(this.selectedItem);
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    },
    //查询图表类型
    async getReportsChartList(itemId) {
      this.chartLoading = true;
      try {
        const { data, code } = await getReportsCustomerchartFromList({ serviceTypeId: itemId });
        if (code == 200) {
          this.chartLoading = false;
          this.chartList = data.filter((item) => item.type != "Form" && item.type != "Table");
          this.tablechartList = data.filter((item) => item.type == "Form");
          this.tableReportlist = data.filter((item) => item.type == "Table");
        }
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    },
    //查询图表详情
    async getdetailsList() {
      this.boxLoading = true;
      this.PreviewLoading = true;
      try {
        const { data, code } = await getReportsCustomerdetails({ id: this.CustomerReport.id });
        if (code == 200) {
          this.previewItems = data.chartFormList || [];
          this.previewTableItems = (data.chartFormList && data.chartFormList.filter((item) => item.type == "Form")) || [];
          this.boxLoading = false;
          this.PreviewLoading = false;
        }
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    },
    // top5 10切换
    statusChange() {},
    selectButtonType(index) {
      // 如果当前按钮已经被选中，则取消选中，设置为 null
      if (this.selectedButton === index) {
        this.selectedButton = null;
      } else {
        // 如果当前按钮没有被选中，则设置为该按钮的 index
        this.selectedButton = index;
      }
    },

    // 底部按钮 button切换
    async selectButton() {
      const groupTypes = ["device", "deviceType", "region", "location"];
      const groupType = groupTypes[this.selectedButton] || "";
      const { name, description, serviceTypeId, topNum, id, tenantId, code } = this.configObj;

      // 统一条件判断
      const conditions = {
        first: {
          check: this.selectedRow !== null,
          message: "请先选择需要插入的图表",
          errorMessage: "图表",
        },
        second: {
          check: this.selectedTableRow !== null,
          message: "请先选择需要插入的Excel",
          errorMessage: "Excel",
        },
        default: {
          check: this.selectedtableReportRow !== null,
          message: "请先选择需要插入的表格",
          errorMessage: "表格",
        },
      };

      const currentCondition = this.activetabsName === "first" ? conditions.first : this.activetabsName === "second" ? conditions.second : conditions.default;

      if (!currentCondition.check) {
        ElMessage.warning(currentCondition.message);
        return;
      }

      try {
        const params = {
          reportId: this.CustomerReport.id,
          id,
          name,
          description,
          code,
          serviceTypeId,
          tenantId,
          topNum,
          group: groupType || null,
        };

        const { code: responseCode } = await getReportsInsert(params);

        if (responseCode == 200) {
          this.getdetailsList();
          ElMessage.success(`${currentCondition.errorMessage}插入成功`);
        }
      } catch (error) {
        ElMessage.warning(error instanceof Error ? error.message : "操作失败");
      }
    },
    // row图表点击事件
    selectRow(item, index) {
      this.selectedRow = index;
      this.configObj = item;
    },
    // 删除图表
    async removeItem(index, item) {
      const { success, message, data, code } = await getReportsDel({ reportId: this.CustomerReport.id, chartFormInsertId: item.id });
      if (code == 200) {
        ElMessage.success("移除成功");
        this.getdetailsList();
      }
      // this.previewItems.splice(index, 1);
    },
    //一键清除图表
    async clearAllItems() {
      const { code } = await getReportsALLDel({ reportId: this.CustomerReport.id });
      if (code == 200) {
        ElMessage.success("移除成功");
        this.getdetailsList();
      }
      // this.previewItems = [];
    },
    // start excel----------------------------------------------------------------------------------------------
    selectTableRow(item, index) {
      this.selectedTableRow = index;
      this.configObj = item;
    },
    // start 表格----------------------------------------------------------------------------------------------
    selectTableReportRow(item, index) {
      this.selectedtableReportRow = index;
      this.configObj = item;
    },
    // tabs 切换
    handleClick(e) {
      this.activetabsName = e.props.name;
    },
  },
});
</script>
<style scoped>
.box_F {
  margin: 20px;
  .selected-item {
    color: #1684fc;
  }
  .topH {
    font-weight: 700;
    margin-bottom: 10px;
  }
  .demo-collapse {
    /* height: 450px; */
    border: 1px solid #ccc;
    padding: 10px;
    padding-top: 0px;
  }

  .costomright {
    /* background-color: red; */
    /* height: 100px;
    width: 100px; */
    padding: 10px;
    /* height: 450px; */
    border: 1px solid #ccc;
    display: flex;
    flex-direction: column;
    .righttop {
      height: 380px;
    }
    .rightbottom {
      display: flex;
      flex: 1;
      height: 50px;
      border-top: 1px solid #ccc;
      padding: 20px;
      justify-content: flex-end;
    }
    .checkbox {
      padding: 10px;
    }
  }
  .selected-row {
    background-color: #f0f0f0;
  }
}
.boximg {
  display: flex;
  justify-content: center; /* Center horizontally */
  align-items: center; /* Center vertically */
}
.chartbtn {
  position: absolute;
  top: 50%;
  /* right: 10px; */
  margin-left: 10px;
}
.el-card.is-always-shadow {
  box-shadow: none;
}

.hover_item p {
  cursor: pointer;
  transition: color 0.3s;
}
.hover_item p:hover {
  background-color: #f2f4f5;
}
</style>
