<template>
  <div>
    <el-dialog :title="title" align-center v-model="dialogFormVisible" :before-close="handleClose" width="30%">
      <div class="FN">
        <!-- {{ $route.query.active }} -->
        <el-input class="FN_IN" v-model="ReportName" style="max-width: 600px" placeholder="请输入报告名称">
          <template #append>.docx</template>
        </el-input>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="backFileName()">返回</el-button>
          <el-button type="primary" :loading="downLoading" @click="Downloadback()">生成报告</el-button>
          <el-button @click="cancelFileName()">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent } from "vue";
import { generateTemplateReport } from "@/views/pages/apis/reportsCustomerReportDownload";
import moment from "moment";
import { ElMessage, ElMenuItem, dayjs } from "element-plus";

export default defineComponent({
  name: "FileName",
  props: {
    // eslint-disable-next-line vue/prop-name-casing
    FileNamedialog: {
      type: Boolean,
      default: false,
    },
    // 客户数据
    checkCustomerList: {
      type: Array,
      default: () => [],
    },
    // 当前行数据
    rowData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    dateRangeTime: {
      type: Object,
      default: () => {
        return {};
      },
    },
    //客户工作时间
    weekDateobject: {
      type: Object,
      default: () => {},
    },
    //客户自定义小时
    weeknumHoursobject: {
      type: Object,
      default: () => {},
    },
    zoneType: {
      type: String,
      default: "",
    },
    zoneId: {
      type: String,
      default: "",
    },
    weekendTime: {
      type: Boolean,
      default: true,
    },
    workingTime: {
      type: Boolean,
      default: true,
    },
  },
  emits: ["dialogCloseFileName"],
  data() {
    return {
      ReportName: "Reports",
      title: "文件名称",
      dialogFormVisible: false,
      downLoading: false,
      customertIds: [],
    };
  },
  watch: {
    FileNamedialog(val) {
      this.dialogFormVisible = val;
    },
  },
  created() {},
  methods: {
    handleClose() {
      this.$emit("dialogCloseFileName", false);
    },
    //返回
    backFileName() {
      this.$emit("dialogCloseFileName", false);
    },
    //取消
    cancelFileName() {
      this.$emit("dialogCloseFileName", false, 1);
    },
    //生成报告
    async Downloadback() {
      this.downLoading = true;
      const tenantIds = this.checkCustomerList.map((item) => item.id);
      const tenantNames = this.checkCustomerList.map((item) => item.name);
      const workingParameter = {
        startWeekDate: this.weekDateobject?.startWeekDate,
        endWeekDate: this.weekDateobject?.endWeekDate,
        zoneId: this.weekDateobject?.zoneId,
        zoneType: "client",
      };
      const customParameter = {
        startHours: this.weeknumHoursobject?.numStar,
        endHours: this.weeknumHoursobject?.numEnd,
        startWeekDate: this.weekDateobject?.startWeekDate,
        endWeekDate: this.weekDateobject?.endWeekDate,
        zoneType: this.zoneType,
        zoneId: this.zoneType == "customize" ? [this.zoneId] : this.weekDateobject?.zoneId,
        isALLWeek: this.weekendTime ? 1 : 0,
      };
      const objParameter = this.workingTime ? workingParameter : customParameter;
      try {
        const { success, message, data, code } = await generateTemplateReport({
          statementId: this.$route.query.active == "custom" ? this.rowData.id : this.rowData.code,
          tenantIds,
          tenantNames,
          templatName: this.rowData.name,
          startTime: moment(this.dateRangeTime[0].getTime()).format("YYYY-MM-DD"),
          endTime: moment(this.dateRangeTime[1].getTime()).format("YYYY-MM-DD"),
          reportName: this.ReportName,
          ...objParameter,
        });
        if (code == 200) {
          ElMessage.success("生成下载列表成功");
          this.downLoading = false;
          this.$router.push({
            path: this.$route.path,
            query: {
              ...this.$route.query,
              active: "download",
            },
          });
        }
        if (!success) throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        this.downLoading = false;
        ElMessage.error(error.message || "发生未知错误");
      }
    },
  },
});
</script>
<style scoped>
.my-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 16px;
}
.vertical-checkbox {
  display: block;
  margin-bottom: 10px;
}
.borderTop {
  border-top: 1px solid #ccc;
  margin-bottom: 10px;
}
.FN {
  height: 150px;
}
.FN_IN {
  margin-top: 50px;
}
</style>
