<template>
  <div>
    <el-dialog :title="title" v-model="dialogFormVisible" :before-close="handleClose" width="35%">
      {{ userInfo }}

      <el-checkbox v-model="checkAllCustomer" :indeterminate="isIndeterminate" label="所有客户" size="large" @change="handleCheckAllChange" />
      <el-input v-model="keyword" style="width: 200px; float: right" placeholder="请输入客户名称" @keyup.enter="handleRefreshLocationTable()">
        <template #append>
          <el-button :icon="Search" @click="handleRefreshLocationTable()" />
        </template>
      </el-input>
      <div class="borderTop"></div>
      <el-scrollbar :height="320" v-loading="Customerloading">
        <el-checkbox-group :model-value="checkedCities.map((city) => city.id)" @change="handleCheckedCitiesChange">
          <el-checkbox v-for="(city, index) in cities" :key="index" :label="city.id" :value="city.id" class="vertical-checkbox"> {{ city.name }} [{{ city.abbreviation }} ]</el-checkbox>
        </el-checkbox-group>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelSelectCustomer()">取 消</el-button>
          <el-button type="primary" @click="NextSelectCustomer()">下一步</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 选择客户时间-->
    <SelectCustomerTime :dialog="CustomerTimedialog" ref="CustomerTimeRef" :checkCustomerList="selectedNames" :rowData="rowData" @dialogClose="dialogCloseCustomerTime"></SelectCustomerTime>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent } from "vue";
import SelectCustomerTime from "./SelectCustomerTime.vue";
import { getTenantList } from "@/api/personnel";
import { log } from "console";
import { ElMessage, ElMenuItem, dayjs } from "element-plus";
import { useUsersInfo } from "@/stores/infoByUsers";

export default defineComponent({
  name: "SelectCustomer",
  components: { SelectCustomerTime },
  props: {
    // eslint-disable-next-line vue/prop-name-casing
    Customerdialog: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    activeType: {
      type: String,
      default: "",
    },
  },
  emits: ["dialogCloseCustomer"],
  data() {
    return {
      checkAllCustomer: false,
      checkedCities: [],
      checkCustomerList: [],
      title: "选择客户",
      dialogFormVisible: false,
      CustomerTimedialog: false,
      checkAll: false,
      isIndeterminate: false,
      cities: [],
      // cities: ["Adrianna Papell, LLC_国际 ladriannapapel]", "Beijing", "Guangzhou", "Shenzhen", "Shanghai", "Beijing", "Guangzhou", "Shenzhen", "Shanghai", "Beijing", "Guangzhou", "Shenzhen"],
      keyword: "",
      Customerloading: false,
      selectedNames: [],
      usersInfo: useUsersInfo(),
    };
  },
  watch: {
    Customerdialog(val) {
      this.dialogFormVisible = val;
      this.checkedCities = [];
      this.checkAllCustomer = false;
    },
  },
  created() {
    this.getAllCustom();
  },
  methods: {
    handleClose() {
      this.$emit("dialogCloseCustomer", false);
    },
    cancelSelectCustomer() {
      this.$emit("dialogCloseCustomer", false);
    },
    // 点击下一步
    NextSelectCustomer() {
      if (this.selectedNames.length == 0) {
        ElMessage.warning("请先选择客户");
        return;
      }
      this.CustomerTimedialog = true;
    },
    //取消彈框
    dialogCloseCustomerTime(type, val = 0) {
      this.CustomerTimedialog = false;
      val && this.cancelSelectCustomer();
    },
    //查询客户
    async getAllCustom() {
      // this.Customerloading = true;
      // const { success, message, data, code } = await getTenantList({ keyword: this.keyword, paging: { pageNumber: 1, pageSize: 9999 } });
      // if (code == 200) {
      //   this.Customerloading = false;
      //   this.cities = data;
      // }
      this.cities = this.usersInfo.tenants.filter((v) => (this.keyword ? (v.name || "").indexOf(this.keyword) !== -1 || (v.abbreviation || "").indexOf(this.keyword) !== -1 : true));
    },
    //选择客户
    // this.cities =
    handleCheckedCitiesChange(value) {
      // 更新 checkedCities 为包含 { id, name } 的对象
      this.checkedCities = this.cities.filter((city) => value.includes(city.id));

      // 计算已选中的城市数量
      let checkedCount = this.checkedCities.length;
      this.checkAll = checkedCount === this.cities.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length;

      // 输出选中的城市的名称
      this.selectedNames = this.checkedCities.map((city) => ({ id: city.id, name: city.name }));

      // let checkedCount = value.length;
      // this.checkCustomerList = value;
      // this.checkAll = checkedCount === this.cities.length;
      // this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length;
    },
    //所有客户
    handleCheckAllChange(val) {
      if (val) {
        this.checkedCities = this.cities.map((city) => ({ id: city.id, name: city.name }));
        this.selectedNames = this.checkedCities.map((city) => ({ id: city.id, name: city.name }));
      } else {
        this.checkedCities = [];
        this.selectedNames = [];
      }
      this.isIndeterminate = false;
    },
    //搜索
    handleRefreshLocationTable() {
      this.getAllCustom();
    },
  },
});
</script>
<style scoped>
.my-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 16px;
}
.vertical-checkbox {
  display: block;
  margin-bottom: 10px;
}
.borderTop {
  border-top: 1px solid #ccc;
  margin-bottom: 10px;
}
</style>
