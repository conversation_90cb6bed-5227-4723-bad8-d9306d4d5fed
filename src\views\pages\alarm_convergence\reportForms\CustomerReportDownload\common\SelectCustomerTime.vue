<template>
  <div>
    <el-dialog :title="title" :show-close="false" v-model="dialogFormVisible" :before-close="handleClose" width="40%">
      <el-row :gutter="20">
        <el-col :span="6" style="border-right: 1px solid #ccc">
          <div class="timeleft">
            <p style="margin-bottom: 5px">提醒 :</p>
            <p>默认选择时间为当月1号00:00:00至当月最后一天的23:59:59,下载的报表为当月的数据。</p>
            <p class="MT">如果需要下载一周的数据, 需要选择周一00:00:00的至周日的23:59:59。</p>
            <p class="MT">默认选择工作时间，是按照配置的客户工作时间，客户的时区来下载报告。</p>
            <p class="MT">可以自定义报告的时间范围，自定义是否去除周末时间，不勾选包含周末，则自动去除周末时间段的数据。</p>
            <p class="MT">此外，还可以自定义选择时区，下载该时区的报告数据。</p>
          </div>
        </el-col>
        <el-col :span="18" class="timeright">
          <el-date-picker v-model="dateRangeTime" :clearable="false" type="datetimerange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" @update:model-value="($event) => handleChangeTime($event)" />
          <div style="height: 100px"></div>
          <el-row>
            <el-col :span="24">
              <el-checkbox style="display: flex" v-model="workingTime" label="工作时间" size="large" @change="handleCustomizeTimeChange(1)" />
              <el-checkbox v-model="customizeTime" label="自定义时间范围" size="large" @change="handleCustomizeTimeChange(2)" />
              <div v-if="customizeTime">
                <p style="margin-top: 10px; margin-bottom: 10px">选择时间</p>
                <el-input-number style="margin-right: 10px" v-model="weeknumHours.numStar" :min="0" :max="23" @change="handleChangeStar" />
                <el-input-number v-model="weeknumHours.numEnd" :min="weeknumHours.numStar" max="23" @change="handleChangeEnd" />
                <el-checkbox style="display: flex" v-model="weekendTime" label="包含周末" size="large" />
                <p style="margin-top: 20px">选择时区</p>
                <div class="B">
                  <el-button :type="selectedButton === 'equipment' ? 'primary' : ''" @click="selectButton('equipment')">设备时区</el-button>
                  <el-button :type="selectedButton === 'client' ? 'primary' : ''" @click="selectButton('client')">客户时区</el-button>
                  <el-button :type="selectedButton === 'customize' ? 'primary' : ''" @click="selectButton('customize')">自定义时区</el-button>
                </div>
              </div>
            </el-col>
            <el-col :span="16" class="A">
              <el-select v-if="selectedButton === 'customize' && customizeTime" v-model="form.zoneId" filterable placeholder="请选择" style="width: 305px; margin-top: 5px">
                <el-option v-for="item in timeZone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"> </el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="BackSelectCustomerTime()">返 回</el-button>
          <el-button type="primary" @click="NextSelectCustomerTime()">下一步</el-button>
          <el-button @click="cancelSelectCustomerTime()">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 选择文件位置 -->
    <FileName :rowData="rowData" :weeknumHoursobject="weeknumHours" :weekDateobject="weekDateobj" :weekendTime="weekendTime" :dateRangeTime="dateRangeTime" :zoneType="selectedButton" :zoneId="form.zoneId" :checkCustomerList="checkCustomerList" :FileNamedialog="FileNameSdialog" :workingTime="workingTime" ref="FileNameS" @dialogCloseFileName="dialogCloseCustomerTime"></FileName>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent, ref, toValue, h, provide, computed, watch, toRef } from "vue";
import { ElMessage, ElMenuItem, dayjs } from "element-plus";

import _timeZone from "@/views/pages/common/contactsZone.json";
import FileName from "./FileName.vue";
import { getTenantNotesAll } from "@/views/pages/apis/reportsCustomerReportDownload";

export default defineComponent({
  name: "SelectCustomerTime",
  components: { FileName },

  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
    // 客户数据
    checkCustomerList: {
      type: Array,
      default: () => [],
    },
    // 当前行数据
    rowData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      form: {
        zoneId: "",
      },
      FileNameSdialog: false,
      selectedButton: "client",
      timeZone: _timeZone,
      shuzu: [],
      title: "选择时间",
      // startTime: dayjs().startOf("month").format("X"),
      // endTime: dayjs().endOf("month").format("X"),
      // dateRangeTime: [],
      defaultTime: ["00:00:00", "23:59:59"],
      dateRangeTime: [new Date(dayjs().startOf("month").format("X") * 1000), new Date(dayjs().endOf("month").format("X") * 1000)],
      dialogFormVisible: false,
      isIndeterminate: true,
      CustomerTimedialog: false,
      workingTime: true,
      weekendTime: false,
      customizeTime: false,
      weeknumHours: {
        numStar: 8,
        numEnd: 18,
      },
      weekDateobj: {},
    };
  },
  watch: {
    dialog(val) {
      this.dialogFormVisible = val;
    },
  },
  created() {},
  methods: {
    log: console.log,
    dayjs,
    handleClose() {
      this.$emit("dialogClose", false);
    },
    //取消关闭弹框
    cancelSelectCustomerTime() {
      this.$emit("dialogClose", false, 1);
    },
    //返回
    BackSelectCustomerTime() {
      this.$emit("dialogClose", false);
    },
    NextSelectCustomerTime() {
      this.FileNameSdialog = true;
      this.getcustomerTimeZone();
    },
    handleCalendarChange(e) {},
    async handleChangeTime(v: Date[]) {
      try {
        this.dateRangeTime = [v[0], v[1]];
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    },
    selectButton(type) {
      this.selectedButton = type;
    },
    handleCustomizeTimeChange(selected) {
      if (selected === 1 && this.workingTime) {
        this.customizeTime = false; // 勾选工作时间，取消自定义时间范围
      } else if (selected === 2 && this.customizeTime) {
        this.workingTime = false; // 勾选自定义时间范围，取消工作时间
      }
    },

    dialogCloseCustomerTime(type, val = 0) {
      this.FileNameSdialog = false;
      val && this.cancelSelectCustomerTime();
    },
    handleChangeStar() {},
    handleChangeEnd() {},
    //查询客户的工作时区
    getcustomerTimeZone() {
      const customertIds = this.checkCustomerList.map((item) => item.id).join(",");
      getTenantNotesAll({
        ids: customertIds,
      }).then((res) => {
        // 获取所有的工作时间和时区
        const tenantHoursArr = res.data.map((config) => config.tenantConfig);
        // 提取所有设备时区和工作时间
        const tenantHourstimeZone = tenantHoursArr.map((item) => item.timeZone);
        const tenantHoursWork = tenantHoursArr.map((item) => item.tenantHours);

        // 提取每周的开始和结束 weekDay
        const startArr = [];
        const endArr = [];

        tenantHoursWork.forEach((week) => {
          let start = null;
          let end = null;

          week.forEach((day) => {
            if (day.hours.length > 0) {
              // 如果 start 为空，则设置为当前周的 weekDay
              if (start === null) {
                start = day.weekDay;
              }
              // 更新 end 为当前周的 weekDay
              end = day.weekDay;
            }
          });

          // 将每周的 start 和 end 分别加入对应数组
          startArr.push(start);
          endArr.push(end);
        });

        // 合并所有周的 start 和 end
        this.weekDateobj = {
          startWeekDate: startArr,
          endWeekDate: endArr,
          zoneId: tenantHourstimeZone,
        };

        // this.weekDateobj = {
        //   startWeekDate: mapWeekDay(workingDays[0]?.weekDay || 0),
        //   endWeekDate: mapWeekDay(workingDays[workingDays.length - 1]?.weekDay || 0),
        // };

        // let tenantHours = res.data.tenantConfig.tenantHours;
        // // 定义映射规则，周日变成 1，周六变成 7，其他正常
        // const mapWeekDay = (original) => {
        //   if (original === 7) return 1; // 周日映射为 1
        //   if (original === 6) return 7; // 周六映射为 7
        //   return original + 1; // 其他星期加 1
        // };
        // // 筛选出有工作时间的天
        // const workingDays = tenantHours.filter((day) => day.hours.length > 0);
        // this.weekDateobj = {
        //   startWeekDate: mapWeekDay(workingDays[0]?.weekDay || 0),
        //   endWeekDate: mapWeekDay(workingDays[workingDays.length - 1]?.weekDay || 0),
        // };
      });
    },
  },
});
</script>
<style scoped>
.my-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 16px;
}
.vertical-checkbox {
  display: block;
  margin-bottom: 10px;
}
.borderTop {
  border-top: 1px solid #ccc;
  margin-bottom: 10px;
}
.timeleft {
  /* background-color: #daa; */
  /* height: 500px; */
}
.timeright {
  /* background-color: #ccc; */
}
.MT {
  margin-top: 15px;
}
.A {
  margin-top: 8px;
}
.B {
  margin-top: 8px;
}
</style>
