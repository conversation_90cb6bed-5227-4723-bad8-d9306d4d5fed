<template>
  <el-tabs :model-value="active" :before-leave="setActive" class="custom_report" :style="{ width: `${$width}px` }">
    <el-tab-pane label="固定模板" :name="ActiveType.template" v-if="userInfo.hasPermission(报表管理中心_客户_自定义报告可读)">
      <div class="tabs-pane" :style="{ height: `${$height - 40}px` }" v-if="active === ActiveType.template">
        <TemplateReport :width="$width - 24" :height="$height - 64"></TemplateReport>
      </div>
    </el-tab-pane>
    <el-tab-pane label="自定义报告模板" :name="ActiveType.custom" v-if="userInfo.hasPermission(报表管理中心_客户_自定义报告管理)">
      <div class="tabs-pane" :style="{ height: `${$height - 40}px` }" v-if="active === ActiveType.custom">
        <CustomReport :width="$width - 24" :height="$height - 64"></CustomReport>
      </div>
    </el-tab-pane>
    <el-tab-pane label="下载列表" :name="ActiveType.download">
      <div class="tabs-pane" :style="{ height: `${$height - 40}px` }" v-if="active === ActiveType.download">
        <DownloadList :width="$width - 24" :height="$height - 64"></DownloadList>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>

<script lang="ts" setup>
import { ref, shallowRef, computed, inject, toValue, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElTabs, ElTabPane } from "element-plus";
import TemplateReport from "./module/TemplateReport.vue";
import CustomReport from "./module/CustomReport.vue";
import DownloadList from "./module/DownloadList.vue";

import getUserInfo from "@/utils/getUserInfo";

import { 报表管理中心_客户_安全, 报表管理中心_客户_自定义报告可读, 报表管理中心_客户_自定义报告管理 } from "@/views/pages/permission";

defineOptions({ name: "CustomerReportDownload" });

const userInfo = getUserInfo();

const route = useRoute();
const router = useRouter();

const $width = inject("width", ref(0));
const $height = inject("height", ref(0));

enum ActiveType {
  template = "template",
  custom = "custom",
  download = "download",
}
const active = computed(() => (`${route.query.active}` in ActiveType ? (route.query.active as ActiveType) : userInfo.hasPermission(报表管理中心_客户_自定义报告可读) ? ActiveType.template : userInfo.hasPermission(报表管理中心_客户_自定义报告管理) ? ActiveType.custom : ActiveType.download));
function setActive(active: string | number) {
  return new Promise<void>((resolve, reject) => {
    router
      .replace({ query: { ...route.query, active } })
      .then(() => resolve())
      .catch(() => reject());
  }).then(() => changeActive());
}

async function changeActive() {}
</script>

<style lang="scss" scoped>
.custom_report {
  :deep(.el-tabs__header) {
    margin: 0px;
  }
  .tabs-pane {
    background-color: var(--el-fill-color-blank);
    padding: 12px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    box-shadow: 0 0 1px 1px inset var(--el-border-color);
  }
}
</style>
