<template>
  <!-- 自定义模板 -->
  <div :class="$style['mainer']">
    <div :class="$style['header']">
      <div :class="$style['rightr']">
        <el-button v-if="userInfo.hasPermission(报表管理中心_自定义报表_新增)" type="primary" :disabled="state.loading" @click="handleCreate('add', null)">新增模板</el-button>
      </div>
    </div>
    <div :class="$style['bodyer']">
      <el-table :data="state.list" :height="props.height - 100" border stripe v-loading="state.loading" :row-key="getRowKeys" :expand-row-keys="customsExpands" @expand-change="handleExpandChange">
        <el-table-column type="expand">
          <template #default="{ row, expanded }">
            <CustomerReportDetails v-if="expanded" :CustomerReport="row"></CustomerReportDetails>
          </template>
        </el-table-column>
        <el-table-column label="模板名称" prop="name"></el-table-column>
        <el-table-column label="描述" prop="description"></el-table-column>
        <el-table-column label="激活" prop="enable">
          <template #default="scope">
            <span>
              <el-switch v-model="scope.row.enable" active-color="#13ce66" @change="ActivetemplateReport(scope.row)"></el-switch>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{ row }">
            <span class="el-button is-link">
              <el-button v-if="userInfo.hasPermission(报表管理中心_自定义报表_编辑)" link type="primary" size="small" @click="handleCreate('edit', row)" style="font-size: 14px"> 编辑 </el-button>
            </span>
            <span class="el-button is-link">
              <el-button v-if="userInfo.hasPermission(报表管理中心_自定义报表_删除)" link type="primary" size="small" @click="delcustomReport(row as Record<string, any>)" style="font-size: 14px"> 删除 </el-button>
            </span>
            <span class="el-button is-link">
              <el-button v-if="userInfo.hasPermission(报表管理中心_自定义报表_下载)" :disabled="!row.enable" link type="primary" size="small" @click="downcustomGenerate(row as Record<string, any>)" style="font-size: 14px"> 下载 </el-button>
            </span>
            <!-- <span>
              <el-link :type="userInfo.hasPermission('612169263167307776') ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="userInfo.hasPermission('612169263167307776') ? false : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
            </span> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div :class="$style['footer']">
      <el-pagination v-model:current-page="state.page" v-model:page-size="state.size" :page-sizes="sizes" :disabled="state.loading" layout="total, ->, sizes, prev, pager, next, jumper" :total="state.total" style="width: 100%" @size-change="getData()" @current-change="getData()"></el-pagination>
    </div>
  </div>
  <!-- 新增报告 -->
  <CustomerReportAdd :dialog="CustomerReportAddDialog" ref="CustomerReportAddRef" @dialogClose="dialogClose"></CustomerReportAdd>
  <SelectCustomer :activeType="$route.query.active" :rowData="rowData" :Customerdialog="Customerdialog" ref="CustomerRef" @dialogCloseCustomer="dialogCloseCustomer"></SelectCustomer>
</template>

<script lang="ts" setup>
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, watch, computed, readonly, reactive, toValue } from "vue";

import { getReportsCustomerList, enableCustomerReport, delReportsCustomerList } from "@/views/pages/apis/reportsCustomerReportDownload";

import CustomerReportDetails from "../common/CustomerReportDetails.vue";
import getUserInfo from "@/utils/getUserInfo";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";
import type { Props } from "./props";
import { sizes } from "@/utils/common";
import { filter, find } from "lodash-es";
import CustomerReportAdd from "../common/CustomerReportAdd.vue";
import SelectCustomer from "../common/SelectCustomer.vue";

import { 报表管理中心_自定义报表_新增, 报表管理中心_自定义报表_编辑, 报表管理中心_自定义报表_删除, 报表管理中心_自定义报表_下载 } from "@/views/pages/permission";

import { ElMessage, ElMessageBox, ElText } from "element-plus";
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
async function getList(_req: {}) {
  return { data: [] as { id: string }[] };
}

const props = withDefaults(defineProps<Props>(), { width: 0, height: 0 });

interface State<T, P> {
  loading: boolean;
  expand: string[];
  select: string[];
  current: string | undefined;
  search: P;
  sort?: { prop: string; order: "ascending" | "descending" };
  list: T[];
  page: number;
  size: number;
  total: number;
}
const final = readonly({ pagination: false });
const rowData = ref({});
const activeName = ref("1");
const CustomerReportAddDialog = ref(false);
const Customerdialog = ref(false);
const userInfo = getUserInfo();
const customsExpands = ref<string[]>([]);
type DataItem = typeof getList extends (req: any) => Promise<{ data: (infer T)[] }> ? T : never;
type ParamsData = Omit<typeof getList extends (req: infer P) => Promise<{ data: DataItem[] }> ? P : never, "type" | "paging" | "sort">;
const state = reactive<State<DataItem, ParamsData>>({
  loading: false,
  expand: [],
  select: [],
  current: undefined,
  search: {},
  sort: undefined,
  list: [],
  // list: [
  //   {
  //     id: "594427364331487232",
  //     tenantIds: ["514960568411488256"],
  //     name: "自定义报告",
  //     type: "MONTH",
  //     template: "CUSTOMER_MONTH",
  //     enable: false,
  //     startTime: null,
  //     endTime: null,
  //     state: "DEFAULT",
  //     createdBy: '{"userId":509621665794097152,"username":"宋志宗1"}',
  //     updatedBy: '{"userId":509621665794097152,"username":"宋志宗1"}',
  //     createdTime: "1709197725474",
  //     updatedTime: "1710751195935",
  //   },
  //   {
  //     id: "582017279651217408",
  //     tenantIds: [],
  //     name: "自定义报告测试",
  //     type: "MONTH",
  //     template: "CUSTOMER_MONTH",
  //     enable: true,
  //     startTime: "00:00:00",
  //     endTime: "23:59:59",
  //     state: "DEFAULT",
  //     createdBy: '{"userId":509621665794097152,"username":"宋志宗1"}',
  //     updatedBy: '{"userId":509621665794097152,"username":"宋志宗1"}',
  //     createdTime: "1706238930717",
  //     updatedTime: "1710743702492",
  //   },
  // ],
  page: 1,
  size: 50,
  total: 0,
});
const dataList = computed(() => (final.pagination ? state.list.slice((state.page - 1) * state.size, state.page * state.size) : state.list));
const expand = computed(() => filter<DataItem>(state.list, (row: DataItem) => state.expand.includes(row.id)));
const select = computed(() => filter<DataItem>(state.list, (row: DataItem) => state.select.includes(row.id)));
const current = computed(() => find<DataItem>(state.list, (row: DataItem) => row.id === state.current));

async function getData() {
  state.loading = true;
  const { success, message, data, page: page = 1, size: size = 50, total: total = 0 } = await getReportsCustomerList({ pageNumber: state.page, pageSize: state.size });
  if (success) {
    state.page = Number(page);
    state.size = Number(size);
    state.total = Number(total);
    state.list = data instanceof Array ? data : [];
    state.loading = false;
  } else throw Object.assign(new Error(message), { success, data });
}

// 新增编辑SLA
async function handleCreate(type, row) {
  try {
    ctx.refs.CustomerReportAddRef.type = type;
    if (type === "add") {
      ctx.refs.CustomerReportAddRef.title = "新增报告模板";
    } else {
      ctx.refs.CustomerReportAddRef.form = {
        name: row.name,
        description: row.description,
        id: row.id,
      };
      ctx.refs.CustomerReportAddRef.title = "编辑报告模板";
    }
    CustomerReportAddDialog.value = true;
  } catch (error) {
    /*  */
  } finally {
    /*  */
  }
}
// 删除报告
function delcustomReport(row) {
  ElMessageBox.confirm(`确定删除该报告模版吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        const { success, data, message } = await delReportsCustomerList({ id: row.id });
        if (!success) throw new Error(message);
        ElMessage.success("操作成功");
        getData();
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    })
    .catch((err) => {
      //
    });
}
//关闭弹框
function dialogClose(bool) {
  CustomerReportAddDialog.value = bool;
  getData();
}
function getRowKeys(row) {
  return row.id;
}
async function handleExpandChange(row, expandedRows) {
  customsExpands.value = expandedRows.length && row ? [row.id] : [];
}
async function ActivetemplateReport(row) {
  try {
    const { success, message, data } = await enableCustomerReport({ id: row.id as string, type: !row.enable ? "disable" : "enable" });
    if (success) {
      ElMessage.success("操作成功");
      getData();
    }
    return { success, message };
  } catch (error) {
    ElMessage.error(error.message);
  }
}

function mounted() {
  getData();
}
// 生成自定义模板
function downcustomGenerate(row) {
  Customerdialog.value = true;
  rowData.value = row;
}
//关闭弹窗
function dialogCloseCustomer() {
  Customerdialog.value = false;
}

// beforeCreate();
// nextTick(created);
onMounted(mounted, ctx);
// onUpdated(updated, ctx);
// onUnmounted(destroyed, ctx);
// onBeforeMount(beforeMount, ctx);
// onBeforeUpdate(beforeUpdate, ctx);
// onBeforeUnmount(beforeDestroy, ctx);
// onActivated(activated, ctx);
// onDeactivated(deactivated, ctx);
</script>

<style lang="scss" scoped module>
.mainer {
  width: v-bind("`${props.width}px`");
  .header {
    height: 30px;
    display: flex;
    align-items: flex-start;
    .lefter {
      margin-right: auto;
    }
    .center {
      margin-left: auto;
      margin-right: auto;
    }
    .rightr {
      margin-left: auto;
      margin-top: -5px;
    }
  }
  .bodyer {
    height: v-bind("`calc(${props.height}px - 100px)`");
  }
  .footer {
    height: 50px;
    display: flex;
    align-items: flex-end;
  }
}
</style>
