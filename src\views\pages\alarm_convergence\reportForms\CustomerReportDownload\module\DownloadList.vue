<template>
  <!-- 下载列表-->
  <div :class="$style['mainer']">
    <div :class="$style['header']">
      <div :class="$style['rightr']">
        <el-tooltip placement="left-end" content="状态30s自动更新">
          <el-button :disabled="state.loading" type="default" :icon="Refresh" :title="'刷新'" @click="getData()"></el-button>
        </el-tooltip>
      </div>
    </div>
    <div :class="$style['bodyer']">
      <el-table :data="state.list" :height="props.height - 100" v-loading="state.loading" border stripe>
        <el-table-column label="模板名称" prop="templatName"></el-table-column>
        <el-table-column label="报告名称" prop="reportName"></el-table-column>
        <el-table-column label="客户" prop="tenantName"></el-table-column>
        <el-table-column label="状态" prop="state" width="150" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.state)" :closable="false">
              <span :style="{ cursor: scope.row.state === 'GENERATING' || scope.row.state === 'DEFAULT' ? 'pointer' : '' }" @click="handleClick(scope.row)">
                {{ getStatusLabel(scope.row.state) }}
              </span>
            </el-tag>
            <!-- <span> {{ getStatusLabel(scope.row.state) }}</span> -->
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <span class="el-button is-link">
              <el-button link type="primary" size="small" :disabled="row.state != 'COMPLETED'" style="font-size: 14px" @click="downLoadgenerate(row as Record<string, any>)"> 下载 </el-button>
            </span>
            <span class="el-button is-link">
              <el-button link type="primary" size="small" @click="delcustomReport(row as Record<string, any>)" style="font-size: 14px"> 删除 </el-button>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div :class="$style['footer']">
      <el-pagination v-model:current-page="state.page" v-model:page-size="state.size" :disabled="state.loading" layout="total, ->, sizes, prev, pager, next, jumper" :total="state.total" style="width: 100%" @size-change="getData()" @current-change="getData()"></el-pagination>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, shallowRef, reactive, readonly, computed, inject, toValue, nextTick, onMounted, getCurrentInstance, onBeforeUnmount } from "vue";
import { Search, Plus, Refresh, InfoFilled, CaretBottom, CaretRight, Close } from "@element-plus/icons-vue";

import { downloadListTemplateReportList, getFindTemplate, getMarkFindTemplate, delTemplateReportList, downTemplateReportFile, getdownloadListTemplate } from "@/views/pages/apis/reportsCustomerReportDownload";
import { ElMessage, ElMessageBox, ElText } from "element-plus";
import getUserInfo from "@/utils/getUserInfo";
import type { Props } from "./props";
import { filter, find } from "lodash-es";
const ctx = getCurrentInstance();
type TagType = "success" | "warning" | "info" | "primary" | "danger";
const userInfo = getUserInfo();
async function getList(_req: {}) {
  return { data: [] as { id: string }[] };
}

const props = withDefaults(defineProps<Props>(), { width: 0, height: 0 });
// constants/status.js

const DownloadListStatus = ref({
  DEFAULT: "DEFAULT",
  GENERATING: "GENERATING",
  CANCELED: "CANCELED",
  FAILED: "FAILED",
  COMPLETED: "COMPLETED",
});

interface State<T, P> {
  loading: boolean;
  expand: string[];
  select: string[];
  current: string | undefined;
  search: P;
  sort?: { prop: string; order: "ascending" | "descending" };
  list: T[];
  page: number;
  size: number;
  total: number;
}
const final = readonly({ pagination: false });
const intervalId = ref<number | null>(null);
type DataItem = typeof getList extends (req: any) => Promise<{ data: (infer T)[] }> ? T : never;
type ParamsData = Omit<typeof getList extends (req: infer P) => Promise<{ data: DataItem[] }> ? P : never, "type" | "paging" | "sort">;
const state = reactive<State<DataItem, ParamsData>>({
  loading: false,
  expand: [],
  select: [],
  current: undefined,
  search: {},
  sort: undefined,
  list: [],
  page: 1,
  size: 50,
  total: 0,
});
const dataList = computed(() => (final.pagination ? state.list.slice((state.page - 1) * state.size, state.page * state.size) : state.list));
const expand = computed(() => filter<DataItem>(state.list, (row: DataItem) => state.expand.includes(row.id)));
const select = computed(() => filter<DataItem>(state.list, (row: DataItem) => state.select.includes(row.id)));
const current = computed(() => find<DataItem>(state.list, (row: DataItem) => row.id === state.current));

async function getData() {
  state.loading = true;
  const { success, message, data, page: page = 1, size: size = 50, total: total = 0 } = await getdownloadListTemplate({ pageNumber: state.page, pageSize: state.size });
  if (success) {
    state.page = Number(page);
    state.size = Number(size);
    state.total = Number(total);
    state.list = data instanceof Array ? data : [];
    if (state.list.length > 0) {
      if (intervalId.value === null) {
        intervalId.value = setInterval(getData, 30000) as unknown as number;
      }
    } else {
      // 如果列表为空，确保清除定时器
      if (intervalId.value !== null) {
        clearInterval(intervalId.value);
        intervalId.value = null;
      }
    }

    state.loading = false;
  } else throw Object.assign(new Error(message), { success, data });
}
function getStatusLabel(status) {
  switch (status) {
    case DownloadListStatus.value.DEFAULT:
      return "生成报告";
    case DownloadListStatus.value.GENERATING:
      return "查看生成报表";
    case DownloadListStatus.value.CANCELED:
      return "已取消";
    case DownloadListStatus.value.FAILED:
      return "失败";
    case DownloadListStatus.value.COMPLETED:
      return "完成";
    default:
      return "未知";
  }
}
function getStatusType(status: string): TagType {
  switch (status) {
    case DownloadListStatus.value.DEFAULT:
      return "info";
    case DownloadListStatus.value.GENERATING:
      return "warning";
    case DownloadListStatus.value.CANCELED:
      return "danger";
    case DownloadListStatus.value.FAILED:
      return "danger";
    case DownloadListStatus.value.COMPLETED:
      return "success";
    default:
      return "info";
  }
}
function mounted() {
  getData();
}

function delcustomReport(row) {
  ElMessageBox.confirm(`确定删除该报告模版吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        const { success, data, message } = await delTemplateReportList({ id: row.id });
        if (!success) throw new Error(message);
        ElMessage.success("操作成功");
        getData();
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    })
    .catch((err) => {
      //
    });
}
async function clickMarkdownload(item) {
  const { success, message, code, data } = await getMarkFindTemplate({ id: item.id as string });
  getData();
}
async function clickdownloadStatus(item) {
  const params = {
    id: item.id as string,
    hasCustomerReadPermission: userInfo.hasPermission("638966735935897600") ? true : false,
  };
  const { success, message, code, data } = await getFindTemplate(params);
  if (code == 200) {
    ElMessage.success(data);
  } else if (code == 400) {
    ElMessage.warning(message);
  } else {
    ElMessage.error(message);
  }
  getData();
}
function handleClick(row) {
  if (row.state === "GENERATING") {
    clickdownloadStatus(row);
  } else if (row.state === "DEFAULT") {
    clickMarkdownload(row);
  }
}
function downLoadgenerate(item: any) {
  downTemplateReportFile({ statementId: item.statementId as string, tenantId: item.tenantId, reportId: item.reportId, reportName: item.reportName })
    // downTemplateReportFile({ statementId: 10, tenantId: 651937627661926400, reportId: "856", reportName: "demo3" })
    .then((res) => {
      const link = document.createElement("a");
      let blob = new Blob([(res as any).data], {
        type: "application/msword;charset=utf-8",
      });
      link.style.display = "none";
      link.href = URL.createObjectURL(blob);
      link.setAttribute("download", item.reportName + "" + ".docx");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    })
    .catch((err) => {
      // console.log(err)

      ElMessage.error(err.message);
    });
}

onMounted(mounted, ctx);
onBeforeUnmount(() => {
  if (intervalId.value !== null) {
    clearInterval(intervalId.value);
  }
});
</script>

<style lang="scss" scoped module>
.mainer {
  width: v-bind("`${props.width}px`");
  .header {
    height: 30px;
    display: flex;
    align-items: flex-start;
    .lefter {
      margin-right: auto;
    }
    .center {
      margin-left: auto;
      margin-right: auto;
    }
    .rightr {
      margin-left: auto;
      margin-top: -5px;
    }
  }
  .bodyer {
    height: v-bind("`calc(${props.height}px - 100px)`");
  }
  .footer {
    height: 50px;
    display: flex;
    align-items: flex-end;
  }
}
</style>
