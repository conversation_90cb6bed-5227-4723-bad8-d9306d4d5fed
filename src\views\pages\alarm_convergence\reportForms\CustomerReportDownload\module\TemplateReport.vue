<template>
  <!-- 固定模板 -->
  <div :class="$style['mainer']">
    <div :class="$style['header']"></div>
    <div :class="$style['bodyer']">
      <el-table :data="state.list" :height="props.height - 100" border stripe v-loading="state.loading" :row-key="getRowKeys" :expand-row-keys="TemplateReportExpands" @expand-change="handleExpandChange">
        <el-table-column type="expand">
          <template #default="{ row }">
            <div class="image-container" v-loading="templatelLoading">
              <el-image :src="imageSUrl" fit="cover" />
              <!-- <el-image :src="`/gw_proxy/ops/event_center/${row.photoUrl}`" fit="cover" /> -->
              <!-- <el-image :src="`https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg`" fit="cover" /> -->

              <!-- <el-image :src="templateUrl" fit="cover">
                <template #error>
                  <div class="image-slot" style="margin-left: 10px">
                    <el-empty description="No Data" />
                  </div>
                </template>
              </el-image> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="模板名称" prop="name"></el-table-column>
        <el-table-column label="描述" prop="desc"></el-table-column>
        <el-table-column label="激活" prop="active">
          <template #default="scope">
            <span>
              <el-switch v-model="scope.row.active" active-color="#13ce66" @change="ActivetemplateReport($event, scope.row)"></el-switch>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{ row }">
            <span class="el-button is-link">
              <el-button link type="primary" size="small" :disabled="!row.active" @click="downLoadgenerate(row as Record<string, any>)" style="font-size: 14px"> 下载 </el-button>
              <!-- <el-button link :icon="Download" type="primary" size="small" @click="downLoadgenerate(row as Record<string, any>)" style="font-size: 14px"> 下载 </el-button> -->
            </span>
            <span>
              <el-link :type="userInfo.hasPermission('612169263167307776') ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="userInfo.hasPermission('612169263167307776') ? false : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- <div :class="$style['footer']">
      <el-pagination v-model:current-page="state.page" v-model:page-size="state.size" :page-sizes="sizes" :disabled="state.loading" layout="total, ->, sizes, prev, pager, next, jumper" :total="400" style="width: 100%" @size-change="getData()" @current-change="getData()"></el-pagination>
    </div> -->
  </div>
  <!-- 客户列表 -->
  <SelectCustomer :activeType="$route.query.active" :rowData="rowData" :Customerdialog="Customerdialog" ref="CustomerRef" @dialogCloseCustomer="dialogCloseCustomer"></SelectCustomer>
</template>

<script lang="ts" setup>
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, shallowRef, reactive, readonly, computed, inject, toValue, nextTick, onMounted, getCurrentInstance } from "vue";
import { Download } from "@element-plus/icons-vue";
import getUserInfo from "@/utils/getUserInfo";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";
import SelectCustomer from "../common/SelectCustomer.vue";

import { getTemplateReportList, getActiveTemplateReport, getImage } from "@/views/pages/apis/reportsCustomerReportDownload";

import { ElMessage, ElMessageBox, ElText } from "element-plus";

import type { Props } from "./props";
import { sizes } from "@/utils/common";
import { filter, find } from "lodash-es";

async function getList(_req: {}) {
  return { data: [] as { id: string }[] };
}

const props = withDefaults(defineProps<Props>(), { width: 0, height: 0 });

interface State<T, P> {
  loading: boolean;
  expand: string[];
  select: string[];
  current: string | undefined;
  search: P;
  sort?: { prop: string; order: "ascending" | "descending" };
  list: T[];
  page: number;
  size: number;
  total: number;
}
const ctx = getCurrentInstance();
// const templateUrl = ref("https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg");
const templateUrl = ref("https://192.168.1.158/gw_proxy/ops/event_center/oss/show/tslf-509621665546633216/ops/ec/event/file/2024-07/f6ca051e-44e9-4800-ba7a-0acbdca05198/QQ%E6%88%AA%E5%9B%BE20240717151144.png");
const final = readonly({ pagination: false });
const Customerdialog = ref(false);
const TemplateReportExpands = ref<string[]>([]);
const rowData = ref({});
const imageSUrl = ref("");
const templatelLoading = ref(false);

const userInfo = getUserInfo();
type DataItem = typeof getList extends (req: any) => Promise<{ data: (infer T)[] }> ? T : never;
type ParamsData = Omit<typeof getList extends (req: infer P) => Promise<{ data: DataItem[] }> ? P : never, "type" | "paging" | "sort">;
const state = reactive<State<DataItem, ParamsData>>({
  loading: false,
  expand: [],
  select: [],
  current: undefined,
  search: {},
  sort: undefined,
  list: [],
  page: 1,
  size: 20,
  total: 0,
});
const dataList = computed(() => (final.pagination ? state.list.slice((state.page - 1) * state.size, state.page * state.size) : state.list));
const expand = computed(() => filter<DataItem>(state.list, (row: DataItem) => state.expand.includes(row.id)));
const select = computed(() => filter<DataItem>(state.list, (row: DataItem) => state.select.includes(row.id)));
const current = computed(() => find<DataItem>(state.list, (row: DataItem) => row.id === state.current));

// 客户列表
async function getData() {
  state.loading = true;
  const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await getTemplateReportList({});
  if (success) {
    state.page = Number(page);
    state.size = Number(size);
    state.total = Number(total);
    state.list = data instanceof Array ? data : [];
    state.loading = false;
  } else throw Object.assign(new Error(message), { success, data });
}
// 是否激活客户模板
async function ActivetemplateReport(active, item) {
  const { success, message, code } = await getActiveTemplateReport({ type: item.type as string, active });
  if (code == 200) {
    ElMessage.success("操作成功");
    this.getData();
  } else {
    ElMessage.error("操作失败");
  }
}

function blobToBase64(blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(blob); // 读取 Blob 并将其转换为 Base64
    reader.onloadend = () => resolve(reader.result); // 成功后返回 Base64 字符串
    reader.onerror = (error) => reject(error); // 处理错误
  });
}

async function getIamgeS(url) {
  templatelLoading.value = true;
  const res = await getImage({ url });
  console.log("🚀 ~ getIamgeS ~ res:", res);

  // 示例用法
  const blob = new Blob([res.data], { type: "image/png" });

  blobToBase64(blob)
    .then((base64String) => {
      imageSUrl.value = base64String;
      templatelLoading.value = false;
      console.log(base64String); // 输出 Base64 编码的字符串
    })
    .catch((error) => {
      console.error("转换失败:", error);
    });

  // const { success, message, code } = await getImage({});
  // if (code == 200) {
  //   // ElMessage.success("操作成功");
  //   // this.getData();
  // } else {
  //   // ElMessage.error("操作失败");
  // }
}

// 下载客户报告  固定模板
function downLoadgenerate(row) {
  Customerdialog.value = true;
  rowData.value = row;
}
//關閉客戶彈框
function dialogCloseCustomer() {
  Customerdialog.value = false;
}
function getRowKeys(row) {
  return row.code;
}
async function handleExpandChange(row, expandedRows) {
  TemplateReportExpands.value = expandedRows.length && row ? [row.code] : [];
  getIamgeS(row.photoUrl);
}
function mounted() {
  getData();
}

onMounted(mounted, ctx);
</script>

<style lang="scss" scoped module>
.mainer {
  width: v-bind("`${props.width}px`");
  .header {
    height: 30px;
    display: flex;
    align-items: flex-start;
    .lefter {
      margin-right: auto;
    }
    .center {
      margin-left: auto;
      margin-right: auto;
    }
    .rightr {
      margin-left: auto;
    }
  }
  .bodyer {
    height: v-bind("`calc(${props.height}px - 100px)`");
  }
  .footer {
    height: 50px;
    display: flex;
    align-items: flex-end;
  }
}
.image-container {
  width: 100%;
}
.el-image {
  width: 100%;
  height: auto;
}
</style>
