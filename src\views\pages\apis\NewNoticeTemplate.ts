import { SERVER, Method, type Response, type RequestBase, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

/**
 * 模块Api示例
 */
export interface ModuleItem {
  [key: string]: unknown;
  id: string;
  name: string;
}
export function getSmsList /* 分页查询短信模板 */(data: { pageNumber: Number; pageSize: Number } & RequestBase) {
  const params = new URLSearchParams();
  bindParamByObj(
    {
      pageNumber: data.pageNumber,
      pageSize: data.pageSize,
      containerId: data.containerId,
      queryPermissionId: data.queryPermissionId,
      verifyPermissionIds: data.verifyPermissionIds,
      active: data.active,
    },
    params
  );

  bindParamByObj(
    {
      ...([...(data.includeName instanceof Array ? data.includeName : []), ...(data.excludeName instanceof Array ? data.excludeName : []), ...(data.eqName instanceof Array ? data.eqName : []), ...(data.neName instanceof Array ? data.neName : [])].filter((v) => v).length ? { nameFilterRelation: data.nameFilterRelation === "OR" ? "OR" : "AND", includeName: data.includeName instanceof Array && data.includeName.length ? data.includeName.join(",") : void 0, excludeName: data.excludeName instanceof Array && data.excludeName.length ? data.excludeName.join(",") : void 0, eqName: data.eqName instanceof Array && data.eqName.length ? data.eqName.join(",") : void 0, neName: data.neName instanceof Array && data.neName.length ? data.neName.join(",") : void 0 } : {}),

      ...([...(data.includeDescription instanceof Array ? data.includeDescription : []), ...(data.excludeDescription instanceof Array ? data.excludeDescription : []), ...(data.eqDescription instanceof Array ? data.eqDescription : []), ...(data.neDescription instanceof Array ? data.neDescription : [])].filter((v) => v).length ? { descriptionFilterRelation: data.descriptionFilterRelation === "OR" ? "OR" : "AND", includeDescription: data.includeDescription instanceof Array && data.includeDescription.length ? data.includeDescription.join(",") : void 0, excludeDescription: data.excludeDescription instanceof Array && data.excludeDescription.length ? data.excludeDescription.join(",") : void 0, eqDescription: data.eqDescription instanceof Array && data.eqDescription.length ? data.eqDescription.join(",") : void 0, neDescription: data.neDescription instanceof Array && data.neDescription.length ? data.neDescription.join(",") : void 0 } : {}),

      active: data.active,
    },
    params
  );
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_template/2.0/filter`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params,
    data: {},
  });
}
export function addSmsTemplates /* 新增短信模板 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_template`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "desc", "active", "containerId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function editSmsTemplates /* 更新短信模板 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_template/${data.id}`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "desc", "active", "containerId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function delSmsTemplates /* 删除短信模板 */(data: Record<"id", string> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_template/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function getSmsDetails /* 根据id查询短信模板详情 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_template/${data.id}/details`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function customSmsContent /* 自定义短信内容 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_template/${data.id}/message_text`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["messageText", "messageFields"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function getEmailList /* 分页查询邮件模板 */(data: { pageNumber: Number; pageSize: Number } & RequestBase) {
  const params = new URLSearchParams();
  bindParamByObj(
    {
      pageNumber: data.pageNumber,
      pageSize: data.pageSize,
      containerId: data.containerId,
      queryPermissionId: data.queryPermissionId,
      verifyPermissionIds: data.verifyPermissionIds,
    },
    params
  );

  bindParamByObj(
    {
      ...([...(data.includeName instanceof Array ? data.includeName : []), ...(data.excludeName instanceof Array ? data.excludeName : []), ...(data.eqName instanceof Array ? data.eqName : []), ...(data.neName instanceof Array ? data.neName : [])].filter((v) => v).length ? { nameFilterRelation: data.nameFilterRelation === "OR" ? "OR" : "AND", includeName: data.includeName instanceof Array && data.includeName.length ? data.includeName.join(",") : void 0, excludeName: data.excludeName instanceof Array && data.excludeName.length ? data.excludeName.join(",") : void 0, eqName: data.eqName instanceof Array && data.eqName.length ? data.eqName.join(",") : void 0, neName: data.neName instanceof Array && data.neName.length ? data.neName.join(",") : void 0 } : {}),

      ...([...(data.includeDescription instanceof Array ? data.includeDescription : []), ...(data.excludeDescription instanceof Array ? data.excludeDescription : []), ...(data.eqDescription instanceof Array ? data.eqDescription : []), ...(data.neDescription instanceof Array ? data.neDescription : [])].filter((v) => v).length ? { descriptionFilterRelation: data.descriptionFilterRelation === "OR" ? "OR" : "AND", includeDescription: data.includeDescription instanceof Array && data.includeDescription.length ? data.includeDescription.join(",") : void 0, excludeDescription: data.excludeDescription instanceof Array && data.excludeDescription.length ? data.excludeDescription.join(",") : void 0, eqDescription: data.eqDescription instanceof Array && data.eqDescription.length ? data.eqDescription.join(",") : void 0, neDescription: data.neDescription instanceof Array && data.neDescription.length ? data.neDescription.join(",") : void 0 } : {}),

      active: data.active,
    },
    params
  );
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_template/2.0/filter`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params,
    data: {},
  });
}
export function addEmailTemplates /* 新增邮件模板 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_template`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "desc", "active", "containerId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function editEmailTemplates /* 更新邮件模板 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_template`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "desc", "active", "containerId", "id"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function delEmailTemplates /* 删除邮件模板 */(data: Record<"id", string> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_template/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function getEmailDetails /* 根据id查询邮件模板详情 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_template/${data.id}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function customEmailContent /* 自定义邮件内容 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_template/${data.id}/content_config`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["subjectCustomText", "subjectKeys", "contentKeys", "contentCustomText"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function getVisibleTenants /* 获取当前用户可访问客户列表 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/current_user/visible_tenants`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
export function getQueryContactList /* 根据租户Id查询联系人,脱敏 */(data: Partial<ModuleItem> & RequestBase) {
  data["permissionId"] = "513148893207199744"
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.CMDB}/contacts/desensitized/queryContactListByTenantId`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
export function getUserbyGroupsList /* 获取指定租户下的用户组列表 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/user_groups/findByTenantIdAndPermissionId?tenantId=${data.tenantId}&permissionId=512890964822458368`,
    // url: `${SERVER.IAM}/tenants/${data.tenantId}/user_groups`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
export function getUserList /* 查询租户下的用户列表(强制脱敏) */(data: Partial<ModuleItem> & RequestBase) {
  data["permissionId"] = "515414964874248192"
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/users/getUserByTenantId/desensitized`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}