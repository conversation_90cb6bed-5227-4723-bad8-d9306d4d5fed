import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";

export interface NoticeSubList {
  id: string;
  name: string;
  rangeInfos: object[];
  object: string;
  objectInfos: object[];
  indate: string;
  startTime: string;
  endTime: string;
  createdTime: string;
  updatedTime: string;
  state: boolean;
}

/**
 * @desc 分页查询通知订阅列表
 *
 * @export
 * @param {({ pageNumber: number; pageSize: number; name?: string; rangeName?: string; object?: string; objectName?: string } & RequestBase)} data
 * @return {*}
 */
export function getNoticeSubByPage(
  data: {
    pageNumber: number;
    pageSize: number;
    name?: string;
    rangeName?: string;
    object?: string;
    objectName?: string;
  } & RequestBase
) {
  return request<NoticeSubList[]>({
    url: `${SERVER.EVENT_CENTER}/notify_subscription`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function getNoticeSubById(data: { id: string } & RequestBase) {
  return request<NoticeSubList>({
    url: `${SERVER.EVENT_CENTER}/notify_subscription/${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

interface PostNoticeSub {
  name: string;
  range: keyof typeof rangeEnum;
  rangeInfos: object[];
  object: string;
  objectInfos: object[];
  indate: string;
  startTime?: string;
  endTime?: string;
  policies: object[];
  id?: string;
}
/**
 * @desc 新建通知订阅
 *
 * @export
 * @param {(PostNoticeSub & RequestBase)} data
 * @return {*}
 */
export function postNoticeSub(data: PostNoticeSub & RequestBase) {
  return request<unknown, Response<string | object[]>>({
    url: `${SERVER.EVENT_CENTER}/notify_subscription`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
/**
 * @desc 更新通知订阅信息
 * @param data
 * @returns
 */
export function putNoticeSub(data: PostNoticeSub & RequestBase) {
  return request<unknown, Response<string | object[]>>({
    url: `${SERVER.EVENT_CENTER}/notify_subscription`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * @desc 启用
 *
 * @export
 * @param {({id: string} & RequestBase)} data
 * @return {*}
 */
export function enableNoticeSub(data: { id: string } & RequestBase) {
  return request<unknown, Response<string | object[]>>({
    url: `${SERVER.EVENT_CENTER}/notify_subscription/${data.id}/enable`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * @desc 禁用
 *
 * @export
 * @param {({id: string} & RequestBase)} data
 * @return {*}
 */
export function disableNoticeSub(data: { id: string } & RequestBase) {
  return request<unknown, Response<string | object[]>>({
    url: `${SERVER.EVENT_CENTER}/notify_subscription/${data.id}/disable`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function delNoticeSub(data: { id: string } & RequestBase) {
  return request<unknown, Response<string | object[]>>({
    url: `${SERVER.EVENT_CENTER}/notify_subscription/${data.id}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/*  */
export enum rangeEnum /* 订阅范围	*/ {
  "ALL_SERVICE" = "全部服务",
  "SERVICE" = "服务",
  "DENOISE" = "降噪",
}

export enum objectEnum /* object */ {
  "TEAM" = "用户组",
  "USER" = "个人",
  "CUSTOMER" = "客户",
}

export enum indateEnum /* 订阅时效 */ {
  "LONG" = "长期",
  "SHORT" = "短期",
}

export enum policiesTypeEnum /* 通知类型	*/ {
  // "ALARM" = "报警",
  "EVENT" = "事件",
  // "BREAKDOWN" = "故障",
}

export enum priorityEnum /* 优先级 */ {
  "P1" = "P1",
  "P2" = "P2",
  "P3" = "P3",
  "P4" = "P4",
  "P5" = "P5",
  "P6" = "P6",
  "P7" = "P7",
  "P8" = "P8",
  "P9" = "P9",
}

export enum orderTypeEnum /* 工单类型 */ {
  "EVENT_TRIGGER" = "事件触发",
  "EVENT_ACK" = "事件接手",
  "EVENT_DELIVER" = "事件转交",
  "EVENT_FINISH" = "事件完成",
}

export enum timeQuantumEnum /* 通知时段 */ {
  "ALL" = "全部时段",
  "WORKING" = "工作时段",
  "NONWORKING" = "非工作时段",
}

export enum notifyChannelsEnum /* 通知渠道 */ {
  "EMAIL" = "邮件",
}
/*  */
