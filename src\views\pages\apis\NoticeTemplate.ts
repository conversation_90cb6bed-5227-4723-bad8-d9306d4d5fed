import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";

import { priority } from "./eventPriority";

export interface EmailTemplate {
  id: string;
  tenantId: string;
  name: string;
  desc: string;
  recipients: string[];
  generateEvent: boolean;
  eventStates: object[];
  noteOperations: object[];
  subjects: string[];
  templateUrl: string;
  enable: boolean;
  createdBy: string;
  updatedBy: string;
  createdTime: string;
  updatedTime: string;
}

export function getEmailTemplate(data: { name: string; pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<EmailTemplate[]>>({
    url: `${SERVER.EVENT_CENTER}/email_template`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

interface CreateParams {
  name: string;
  desc: string;
  recipients: string[];
  carbonCopies: string[];
  generateEvent: boolean;
  eventStates: object[];
  noteOperations: object[];
  subjectKeys: string[];
  subjectText: string;
  templateUrl: object;
}

export function createEmailTemplate(data: CreateParams & RequestBase) {
  return request<never, Response<EmailTemplate>>({
    url: `${SERVER.EVENT_CENTER}/email_template`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function enableAndDisableEmailTemplate(data: { id: string | number; type: string } & RequestBase) {
  return request<never, Response<EmailTemplate>>({
    url: `${SERVER.EVENT_CENTER}/email_template/${data.id}/${data.type}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function delEmailTemplate(data: { id: string | number } & RequestBase) {
  return request<never, Response<EmailTemplate>>({
    url: `${SERVER.EVENT_CENTER}/email_template/${data.id}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function getEmailTemplateInfo(data: { id: string | number } & RequestBase) {
  return request<never, Response<EmailTemplate>>({
    url: `${SERVER.EVENT_CENTER}/email_template/${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function editEmailTemplate(data: CreateParams & RequestBase) {
  return request<never, Response<EmailTemplate>>({
    url: `${SERVER.EVENT_CENTER}/email_template`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { ...data },
  });
}

export function uploadFile(data: { file: File } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/email_template/upload_html`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export interface SmsTempleteItem {
  id: string /* 主键 */;
  tenantId: string /* 租户id */;
  name: string /* 模板名称 */;
  desc: string /* 模板描述 */;
  deviceId: string /* 设备id,-1为全部设备 */;
  deviceContacts: /* 设备联系人 */ "NOTIFY_CONTACT" | "FIELD_CONTACT" | "TECHNICAL_CONTACT" /*  枚举类型: NOTIFY_CONTACT :通知联系人 | FIELD_CONTACT :现场联系人 | TECHNICAL_CONTACT :技术联系人 */[];
  regionId: string /* 区域id */;
  regionContacts: /* 区域联系人 */ string[];
  locationId: string /* 场所id */;
  locationContacts: /* 场所联系人 */ string[];
  createEvent: boolean /* 新建事件 */;
  eventOperations: /* 处理事件 */ {
    priority?: priority /* 事件优先级 枚举类型: P1 :P1~P7 优先级递减 | P2 :P2 | P3 :P3 | P4 :P4 | P5 :P5 | P6 :P6 | P7 :P7 | P8 :P8 手动 */;
    operation?: "TAKE_OVER" | "DELIVER_TO" | "UPGRADE" | "CUSTOMER_SUSPENDED" | "SERVICE_PROVIDER_SUSPENDED" | "FINISHED" | "CLOSE" /* 事件操作 枚举类型: TAKE_OVER :处理 | DELIVER_TO :转交 | UPGRADE :升级 | CUSTOMER_SUSPENDED :客户挂起 | SERVICE_PROVIDER_SUSPENDED :供应商挂起 | FINISHED :完成 | CLOSE :关闭 */;
  }[];
  noteOperations: /* 小记操作 */ {
    priority?: priority /* 优先级 枚举类型: P1 :P1~P7 优先级递减 | P2 :P2 | P3 :P3 | P4 :P4 | P5 :P5 | P6 :P6 | P7 :P7 | P8 :P8 手动 */;
    operation?: "CREATE" | "UPDATE" | "DELETE" /* 小记操作 枚举类型: CREATE :新增 | UPDATE :编辑 | DELETE :删除 */;
  }[];
  messageFields: /* 短信字段 */ "CUSTOMER_NAME" | "TRIGGER_TIME" | "ORDER_NUMBER" | "ORDER_DIGEST" | "ALERT_NUMBER" | "ORDER_STATE" | "DEVICE_NAME" | "CONTACT_NAME" /*  枚举类型: CUSTOMER_NAME :客户名称 | TRIGGER_TIME :触发时间 | ORDER_NUMBER :工单编号 | ORDER_DIGEST :工单摘要 | ALERT_NUMBER :告警数量 | ORDER_STATE :工单状态 | DEVICE_NAME :设备名称 | CONTACT_NAME :联系人名称 */[];
  messageText: string /* 短信内容 */;
  enable: boolean /* 使用状态 */;
  createdBy?: string /* 创建人信息 */;
  updatedBy?: string /* 更新人信息 */;
  createdTime?: string /* 创建时间 */;
  updatedTime?: string /* 更新时间 */;
}

export function /* 分页查询短信模板 */ getSmsTemplate(req: Partial<Record<"name" | "pageNumber" | "pageSize" | "sort", string>> & RequestBase) {
  // const header = new Headers();

  const params = { name: req.name /* 模板名称 */, pageNumber: req.pageNumber /* 页码, 默认第一页 */, pageSize: req.pageSize /* 页大小, 默认10 */, sort: req.sort || "" /* undefined */ };

  const data = {};

  return request<never, Response<SmsTempleteItem[]>>({ url: `${SERVER.EVENT_CENTER}/message_template`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function /* 根据id查询短信模板详情 */ getSmsTemplateDetail(req: { id: string } & RequestBase) {
  const params = {};
  const data = {};

  return request<never, Response<SmsTempleteItem>>({ url: `${SERVER.EVENT_CENTER}/message_template/${req.id /* 模板id */}/details`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function /* 删除短信模板 */ delSmsTemplate(req: { id: string } & RequestBase) {
  const params = {};
  const data = {};
  return request<never, Response<null>>({ url: `${SERVER.EVENT_CENTER}/message_template/${req.id /* 模板id */}`, method: Method.Delete, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function /* 启用短信模板 */ enableSmsTemplate(req: { id: string } & RequestBase) {
  const params = {};
  const data = {};
  return request<never, Response<SmsTempleteItem>>({ url: `${SERVER.EVENT_CENTER}/message_template/${req.id /* 模板id */}/enable`, method: Method.Patch, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function /* 禁用短信模板 */ disableSmsTemplate(req: { id: string } & RequestBase) {
  const params = {};
  const data = {};
  return request<never, Response<SmsTempleteItem>>({ url: `${SERVER.EVENT_CENTER}/message_template/${req.id /* 模板id */}/disable`, method: Method.Patch, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function /* 新增短信模板 */ addSmsTemplate(req: { name: string; desc?: string; deviceId: string; deviceContacts?: string[]; regionId: string; regionContacts?: string[]; locationId: string; locationContacts?: string[]; createEvent?: boolean; eventOperations?: { priority?: "P1" | "P2" | "P3" | "P4" | "P5" | "P6" | "P7" | "P8"; operation?: "TAKE_OVER" | "DELIVER_TO" | "UPGRADE" | "CUSTOMER_SUSPENDED" | "SERVICE_PROVIDER_SUSPENDED" | "FINISHED" | "CLOSE" }[]; noteOperations?: { priority?: "P1" | "P2" | "P3" | "P4" | "P5" | "P6" | "P7" | "P8"; operation?: "CREATE" | "UPDATE" | "DELETE" }[]; messageFields?: string[]; messageText?: string } & RequestBase) {
  const params = {};
  const data = { name: req.name /* 模板名称 */, desc: req.desc /* 模板描述 */, deviceId: req.deviceId /* 设备id,-1为全部设备 */, deviceContacts: req.deviceContacts /* 设备联系人 */, regionId: req.regionId /* 区域id */, regionContacts: req.regionContacts /* 区域联系人 */, locationId: req.locationId /* 场所id */, locationContacts: req.locationContacts /* 场所联系人 */, createEvent: req.createEvent /* 新建事件 */, eventOperations: req.eventOperations /* 处理事件 */, noteOperations: req.noteOperations /* 小记操作 */, messageFields: req.messageFields /* 短信字段 */, messageText: req.messageText /* 短信内容 */ };

  return request<never, Response<SmsTempleteItem>>({ url: `${SERVER.EVENT_CENTER}/message_template`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function /* 更新短信模板 */ setSmsTemplate(req: { id: string; name: string; desc?: string; deviceId: string; deviceContacts?: string[]; regionId: string; regionContacts?: string[]; locationId: string; locationContacts?: string[]; createEvent?: boolean; eventOperations?: { priority?: "P1" | "P2" | "P3" | "P4" | "P5" | "P6" | "P7" | "P8"; operation?: "TAKE_OVER" | "DELIVER_TO" | "UPGRADE" | "CUSTOMER_SUSPENDED" | "SERVICE_PROVIDER_SUSPENDED" | "FINISHED" | "CLOSE" }[]; noteOperations?: { priority?: "P1" | "P2" | "P3" | "P4" | "P5" | "P6" | "P7" | "P8"; operation?: "CREATE" | "UPDATE" | "DELETE" }[]; messageFields?: string[]; messageText?: string } & RequestBase) {
  const params = {};
  const data = { name: req.name /* 模板名称 */, desc: req.desc /* 模板描述 */, deviceId: req.deviceId /* 设备id,-1为全部设备 */, deviceContacts: req.deviceContacts /* 设备联系人 */, regionId: req.regionId /* 区域id */, regionContacts: req.regionContacts /* 区域联系人 */, locationId: req.locationId /* 场所id */, locationContacts: req.locationContacts /* 场所联系人 */, createEvent: req.createEvent /* 新建事件 */, eventOperations: req.eventOperations /* 处理事件 */, noteOperations: req.noteOperations /* 小记操作 */, messageFields: req.messageFields /* 短信字段 */, messageText: req.messageText /* 短信内容 */ };

  return request<never, Response<SmsTempleteItem>>({ url: `${SERVER.EVENT_CENTER}/message_template/${req.id /* 模板id */}`, method: Method.Put, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
