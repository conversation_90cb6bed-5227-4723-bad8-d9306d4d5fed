import { SERVER, Method, type Response, type RequestBase, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";
import { 资产管理中心_设备_可读 } from "@/views/pages/permission";

/**
 * 模块Api示例
 */
export interface ModuleItem {
  [key: string]: unknown;
  id: string;
  name: string;
}
export function getMsgStrategyList /* 分页查询短信发送策略 */(data: { pageNumber: Number; pageSize: Number } & RequestBase) {
  const params = new URLSearchParams();

  bindParamByObj(
    {
      pageNumber: data.pageNumber,
      pageSize: data.pageSize,
      containerId: data.containerId,
      queryPermissionId: data.queryPermissionId,
      verifyPermissionIds: data.verifyPermissionIds,
      containerList: data.containerList,
    },
    params
  );

  bindParamByObj(
    {
      ...([...(data.includeName instanceof Array ? data.includeName : []), ...(data.excludeName instanceof Array ? data.excludeName : []), ...(data.eqName instanceof Array ? data.eqName : []), ...(data.neName instanceof Array ? data.neName : [])].filter((v) => v).length ? { nameFilterRelation: data.nameFilterRelation === "OR" ? "OR" : "AND", includeName: data.includeName instanceof Array && data.includeName.length ? data.includeName.join(",") : void 0, excludeName: data.excludeName instanceof Array && data.excludeName.length ? data.excludeName.join(",") : void 0, eqName: data.eqName instanceof Array && data.eqName.length ? data.eqName.join(",") : void 0, neName: data.neName instanceof Array && data.neName.length ? data.neName.join(",") : void 0 } : {}),

      ...([...(data.includeDescription instanceof Array ? data.includeDescription : []), ...(data.excludeDescription instanceof Array ? data.excludeDescription : []), ...(data.eqDescription instanceof Array ? data.eqDescription : []), ...(data.neDescription instanceof Array ? data.neDescription : [])].filter((v) => v).length ? { descriptionFilterRelation: data.descriptionFilterRelation === "OR" ? "OR" : "AND", includeDescription: data.includeDescription instanceof Array && data.includeDescription.length ? data.includeDescription.join(",") : void 0, excludeDescription: data.excludeDescription instanceof Array && data.excludeDescription.length ? data.excludeDescription.join(",") : void 0, eqDescription: data.eqDescription instanceof Array && data.eqDescription.length ? data.eqDescription.join(",") : void 0, neDescription: data.neDescription instanceof Array && data.neDescription.length ? data.neDescription.join(",") : void 0 } : {}),

      ...([...(data.includeTemplate instanceof Array ? data.includeTemplate : []), ...(data.excludeTemplate instanceof Array ? data.excludeTemplate : []), ...(data.eqTemplate instanceof Array ? data.eqTemplate : []), ...(data.neTemplate instanceof Array ? data.neTemplate : [])].filter((v) => v).length ? { templateFilterRelation: data.templateFilterRelation === "OR" ? "OR" : "AND", includeTemplate: data.includeTemplate instanceof Array && data.includeTemplate.length ? data.includeTemplate.join(",") : void 0, excludeTemplate: data.excludeTemplate instanceof Array && data.excludeTemplate.length ? data.excludeTemplate.join(",") : void 0, eqTemplate: data.eqTemplate instanceof Array && data.eqTemplate.length ? data.eqTemplate.join(",") : void 0, neTemplate: data.neTemplate instanceof Array && data.neTemplate.length ? data.neTemplate.join(",") : void 0 } : {}),

      ...([...(data.includeTrigger instanceof Array ? data.includeTrigger : []), ...(data.excludeTrigger instanceof Array ? data.excludeTrigger : []), ...(data.eqTrigger instanceof Array ? data.eqTrigger : []), ...(data.neTrigger instanceof Array ? data.neTrigger : [])].filter((v) => v).length ? { triggerFilterRelation: data.triggerFilterRelation === "OR" ? "OR" : "AND", includeTrigger: data.includeTrigger instanceof Array && data.includeTrigger.length ? data.includeTrigger.join(",") : void 0, excludeTrigger: data.excludeTrigger instanceof Array && data.excludeTrigger.length ? data.excludeTrigger.join(",") : void 0, eqTrigger: data.eqTrigger instanceof Array && data.eqTrigger.length ? data.eqTrigger.join(",") : void 0, neTrigger: data.neTrigger instanceof Array && data.neTrigger.length ? data.neTrigger.join(",") : void 0 } : {}),

      active: data.active || void 0,
      defaultable: data.defaultable || void 0,
    },
    params
  );

  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_strategy/2.0/filter`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params,
    data: {},
  });
}
export function addSmsStrategy /* 新增短信发送策略 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_strategy/create`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "desc", "active", "containerId", "defaultable"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function editSmsStrategy /* 更新短信发送策略 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_strategy/${data.id}/update`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "desc", "active", "containerId", "defaultable"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function DefaultSmsStrategy /* 验证是否默认短信发送策略 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_strategy/hasDefaultEmailStrategy`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["id"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function delSmsStrategy /* 删除短信发送策略 */(data: Record<"id", string> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_strategy/${data.id}/delete`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function getSmsStrategyDetails /* 根据id查询短信发送策略详情 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_strategy/${data.id}/details`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function customSmsContent /* 保存短信发送策略收件人及关联、触发模版接口 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_strategy/${data.id}/save_recipient`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["recipientTypes", "recipients", "userGroupIds", "userIds", "smsTemplateId", "triggerTemplateId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//短信发送策略关联租户
export function sms_assign_customers(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_strategy/${data.id}/assign_customers`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["customerIds", "assignType"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//短信发送策略关联区域
export function sms_assign_regions(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_strategy/${data.id}/assign_regions`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["regionIds", "assignType"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//短信发送策略关联场所
export function sms_assign_locations(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_strategy/${data.id}/assign_locations`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["locationIds", "assignType"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//短信发送策略关联设备
export function sms_assign_devices(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_strategy/${data.id}/assign_devices`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["deviceIds", "assignType"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//邮件发送策略关联用户组
export function sms_assign_userGroups(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_strategy/${data.id}/assign_userGroups`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["ids", "assignType"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//邮件发送策略关联用户
export function sms_assign_user(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_strategy/${data.id}/assign_user`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["ids", "assignType"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//邮件发送策略关联联系人
export function sms_assign_contacts(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/message_strategy/${data.id}/assign_contacts`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["ids", "assignType"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//获取设备列表
export function getAllDeviceList(data: { pageNumber: number; pageSize: number; name?: string } & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.CMDB}/resources/tenant/current/desensitized`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//根据用户Id集合查询用户信息
export function getAllUsersList(data: Partial<ModuleItem> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/users/batch_get/desensitized`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["ids"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

//根据用户组Id集合查询用户组信息
export function getAllUsersGroupsList(data: Partial<ModuleItem> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/user_groups/batch_get/desensitized`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["ids"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//根据用户组Id集合查询联系人信息
export function getAllContactsList(data: Partial<ModuleItem> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.CMDB}/contacts/batch_get/desensitized`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["ids"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { permissionId: "513148893207199744" }),
  });
}
//获取区域列表
export function getAllRegionsList(data: Partial<ModuleItem> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.CMDB}/regions/batch`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["ids"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//获取场所列表
export function getAllLocationsList(data: Partial<ModuleItem> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.CMDB}/locations/batch`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["ids"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function getDeviceList /* 获取所有策略设备列表 */(data: { pageNumber: Number; pageSize: Number } & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    // url: `${SERVER.CMDB}/resources/${data.ids}/batch/detail/desensitized`,
    url: `${SERVER.CMDB}/resources/2.0/batch/detail/desensitized`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: Object.assign(
      ["ids"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
      {
        permissionId: 资产管理中心_设备_可读,
      }
    ),
  });
}
/**********************************************   邮件发送策略  ***************************************************************** */
export function getEmailStrategyList /* 分页查询邮件发送策略 */(data: { pageNumber: Number; pageSize: Number } & RequestBase) {
  const params = new URLSearchParams();

  bindParamByObj(
    {
      pageNumber: data.pageNumber,
      pageSize: data.pageSize,
      containerId: data.containerId,
      queryPermissionId: data.queryPermissionId,
      verifyPermissionIds: data.verifyPermissionIds,
      containerList: data.containerList,
    },
    params
  );

  bindParamByObj(
    {
      ...([...(data.includeName instanceof Array ? data.includeName : []), ...(data.excludeName instanceof Array ? data.excludeName : []), ...(data.eqName instanceof Array ? data.eqName : []), ...(data.neName instanceof Array ? data.neName : [])].filter((v) => v).length ? { nameFilterRelation: data.nameFilterRelation === "OR" ? "OR" : "AND", includeName: data.includeName instanceof Array && data.includeName.length ? data.includeName.join(",") : void 0, excludeName: data.excludeName instanceof Array && data.excludeName.length ? data.excludeName.join(",") : void 0, eqName: data.eqName instanceof Array && data.eqName.length ? data.eqName.join(",") : void 0, neName: data.neName instanceof Array && data.neName.length ? data.neName.join(",") : void 0 } : {}),

      ...([...(data.includeDescription instanceof Array ? data.includeDescription : []), ...(data.excludeDescription instanceof Array ? data.excludeDescription : []), ...(data.eqDescription instanceof Array ? data.eqDescription : []), ...(data.neDescription instanceof Array ? data.neDescription : [])].filter((v) => v).length ? { descriptionFilterRelation: data.descriptionFilterRelation === "OR" ? "OR" : "AND", includeDescription: data.includeDescription instanceof Array && data.includeDescription.length ? data.includeDescription.join(",") : void 0, excludeDescription: data.excludeDescription instanceof Array && data.excludeDescription.length ? data.excludeDescription.join(",") : void 0, eqDescription: data.eqDescription instanceof Array && data.eqDescription.length ? data.eqDescription.join(",") : void 0, neDescription: data.neDescription instanceof Array && data.neDescription.length ? data.neDescription.join(",") : void 0 } : {}),

      ...([...(data.includeTemplate instanceof Array ? data.includeTemplate : []), ...(data.excludeTemplate instanceof Array ? data.excludeTemplate : []), ...(data.eqTemplate instanceof Array ? data.eqTemplate : []), ...(data.neTemplate instanceof Array ? data.neTemplate : [])].filter((v) => v).length ? { templateFilterRelation: data.templateFilterRelation === "OR" ? "OR" : "AND", includeTemplate: data.includeTemplate instanceof Array && data.includeTemplate.length ? data.includeTemplate.join(",") : void 0, excludeTemplate: data.excludeTemplate instanceof Array && data.excludeTemplate.length ? data.excludeTemplate.join(",") : void 0, eqTemplate: data.eqTemplate instanceof Array && data.eqTemplate.length ? data.eqTemplate.join(",") : void 0, neTemplate: data.neTemplate instanceof Array && data.neTemplate.length ? data.neTemplate.join(",") : void 0 } : {}),

      ...([...(data.includeTrigger instanceof Array ? data.includeTrigger : []), ...(data.excludeTrigger instanceof Array ? data.excludeTrigger : []), ...(data.eqTrigger instanceof Array ? data.eqTrigger : []), ...(data.neTrigger instanceof Array ? data.neTrigger : [])].filter((v) => v).length ? { triggerFilterRelation: data.triggerFilterRelation === "OR" ? "OR" : "AND", includeTrigger: data.includeTrigger instanceof Array && data.includeTrigger.length ? data.includeTrigger.join(",") : void 0, excludeTrigger: data.excludeTrigger instanceof Array && data.excludeTrigger.length ? data.excludeTrigger.join(",") : void 0, eqTrigger: data.eqTrigger instanceof Array && data.eqTrigger.length ? data.eqTrigger.join(",") : void 0, neTrigger: data.neTrigger instanceof Array && data.neTrigger.length ? data.neTrigger.join(",") : void 0 } : {}),

      active: data.active || void 0,
      defaultable: data.defaultable || void 0,
    },
    params
  );
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_strategy/2.0/filter`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params,
    data: {},
  });
}

export function addEmailStrategy /* 新增邮件发送策略 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_strategy/create`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "desc", "active", "containerId", "defaultable"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function editEmailStrategy /* 更新邮件发送策略 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_strategy/${data.id}/update`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "desc", "active", "containerId", "defaultable"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function DefaultEmailStrategy /* 验证是否默认邮件发送策略 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_strategy/hasDefaultEmailStrategy`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["id"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function delEmailStrategy /* 删除邮件发送策略 */(data: Record<"id", string> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_strategy/${data.id}/delete`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function getEmailStrategyDetails /* 根据id查询邮件发送策略详情 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_strategy/${data.id}/details`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function customEmailContent /* 保存邮件发送策略收件人及关联、触发模版接口 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_strategy/${data.id}/save_recipient`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["recipientTypes", "recipients", "userGroupIds", "userIds", "emailTemplateId", "triggerTemplateId", "ccRecipients", "ccRecipientTypes"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

//邮件发送策略关联租户
export function email_assign_customers(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_strategy/${data.id}/assign_customers`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["customerIds", "assignType"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//邮件发送策略关联用户组
export function email_assign_userGroups(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_strategy/${data.id}/assign_userGroups`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["ids", "assignType"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//邮件发送策略关联用户
export function email_assign_user(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_strategy/${data.id}/assign_user`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["ids", "assignType"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//邮件发送策略关联联系人
export function email_assign_contacts(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_strategy/${data.id}/assign_contacts`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["ids", "assignType"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//邮件发送策略关联区域
export function email_assign_regions(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_strategy/${data.id}/assign_regions`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["regionIds", "assignType"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//邮件发送策略关联场所
export function email_assign_locations(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_strategy/${data.id}/assign_locations`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["locationIds", "assignType"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//邮件发送策略关联设备
export function email_assign_devices(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/email_strategy/${data.id}/assign_devices`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["deviceIds", "assignType"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
