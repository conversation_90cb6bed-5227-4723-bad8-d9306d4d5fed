import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";

export interface ServiceCenterList {
  id: string;
  slaRuleId: string; //sla规则id
  degradeId: string; //降级策略id
  slaRuleName: string; //sla服务名称
  serviceName: string; //服务中心名称
  serviceDesc: string; //服务中心描述
  degradeStrategyName: string; //降级策略名称
  serviceStatus: string; //服务中心状态
}
//服务中心列表
export function getServiceCenterByPage(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<ServiceCenterList[]>({
    url: `${SERVER.EVENT_CENTER}/service/list`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//sla服务查询
export function getSlaServiceByPage(data: { pageNumber: number; pageSize: number; id: number } & RequestBase) {
  return request<ServiceCenterList[]>({
    url: `${SERVER.EVENT_CENTER}/${data.id}/detail`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//服务中心新增
export function AddServiceCenter(
  data: {
    serviceName: string;
    slaRuleName: string;
    slaRuleId: number;
    degradeId: number;
    serviceDesc?: string;
    degradeName?: string;
  } & RequestBase
) {
  return request<ServiceCenterList[]>({
    url: `${SERVER.EVENT_CENTER}/service/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//服务中心编辑
export function EditServiceCenter(
  data: {
    serviceName: string;
    slaRuleName: string;
    slaRuleId: number;
    degradeId: number;
    serviceDesc?: string;
    degradeName?: string;
  } & RequestBase
) {
  return request<ServiceCenterList[]>({
    url: `${SERVER.EVENT_CENTER}/service/edit`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//服务中心删除
export function DeleteServiceCenter(
  data: {
    serviceName: string;
    slaRuleName: string;
    slaRuleId: number;
    degradeId: number;
    serviceDesc?: string;
    degradeName?: string;
  } & RequestBase
) {
  return request<ServiceCenterList[]>({
    url: `${SERVER.EVENT_CENTER}/service/${data.id}/delete`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//服务中心启用
export function EnableServiceCenter(
  data: {
    serviceName: string;
    slaRuleName: string;
    slaRuleId: number;
    degradeId: number;
    serviceDesc?: string;
    degradeName?: string;
  } & RequestBase
) {
  return request<ServiceCenterList[]>({
    url: `${SERVER.EVENT_CENTER}/service/${data.id}/enable?enabled=${data.states}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//服务中心禁用
export function DisableServiceCenter(
  data: {
    serviceName: string;
    slaRuleName: string;
    slaRuleId: number;
    degradeId: number;
    serviceDesc?: string;
    degradeName?: string;
  } & RequestBase
) {
  return request<ServiceCenterList[]>({
    url: `${SERVER.EVENT_CENTER}/service/${data.id}/enable?enabled=${data.states}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
