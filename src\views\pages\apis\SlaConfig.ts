import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";

export interface SlaConfigList {
  id: string;
  itemId: string; //sla条目id
  level: string; //优先级
  effectiveBegin: object[]; //开始生效时间
  effectiveEnd: object[]; //结束生效时间
  response: object[]; //响应时限
  resolve: object[]; //解决时限
  ruleId: string; //sla规则id
  ruleName: string; //sla规则名称
  ruleDesc: string; //sla规则描述
  ruleType: string; //sla规则类型
  slaRuleId: string; //sla规则id
  degradeId: string; //降级策略id
  slaRuleName: string; //sla服务名称
  tenantId: string;

  status: string;
}
//Sla配置列表
export function getSlaConfigByPage(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/list`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function getnewSlaConfigByPage(data: { pageNumber: number; pageSize: number } & RequestBase) {
  const params = new URLSearchParams();

  bindSearchParams({ pageNumber: data.pageNumber, pageSize: data.pageSize, tenantId: data.tenantId, containerId: data.containerId, queryPermissionId: data.queryPermissionId, verifyPermissionIds: data.verifyPermissionIds }, params);

  bindSearchParams(
    {
      ...([...(data.includeName instanceof Array ? data.includeName : []), ...(data.excludeName instanceof Array ? data.excludeName : []), ...(data.eqName instanceof Array ? data.eqName : []), ...(data.neName instanceof Array ? data.neName : [])].filter((v) => v).length ? { nameFilterRelation: data.nameFilterRelation === "OR" ? "OR" : "AND", includeName: data.includeName instanceof Array && data.includeName.length ? data.includeName.join(",") : void 0, excludeName: data.excludeName instanceof Array && data.excludeName.length ? data.excludeName.join(",") : void 0, eqName: data.eqName instanceof Array && data.eqName.length ? data.eqName.join(",") : void 0, neName: data.neName instanceof Array && data.neName.length ? data.neName.join(",") : void 0 } : {}),

      ...([...(data.includeDescription instanceof Array ? data.includeDescription : []), ...(data.excludeDescription instanceof Array ? data.excludeDescription : []), ...(data.eqDescription instanceof Array ? data.eqDescription : []), ...(data.neDescription instanceof Array ? data.neDescription : [])].filter((v) => v).length ? { descriptionFilterRelation: data.descriptionFilterRelation === "OR" ? "OR" : "AND", includeDescription: data.includeDescription instanceof Array && data.includeDescription.length ? data.includeDescription.join(",") : void 0, excludeDescription: data.excludeDescription instanceof Array && data.excludeDescription.length ? data.excludeDescription.join(",") : void 0, eqDescription: data.eqDescription instanceof Array && data.eqDescription.length ? data.eqDescription.join(",") : void 0, neDescription: data.neDescription instanceof Array && data.neDescription.length ? data.neDescription.join(",") : void 0 } : {}),

      defaultSla: data.defaultSla,
    },
    params
  );
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/2.0/list/filter`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params,
    data: {},
  });
}

export function getnewSlaquerySlaList(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/querySlaList`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//获取时区
export function getTimezoneList(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/timezone/list`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
  });
}
//添加事件响应等级
export function addResponseLevel(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/item/level/createResponse`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

// //iam客户列表
// export function getCustomerList(
//   data: {
//     pageNumber: number;
//     pageSize: number;
//     name?: string;
//     arrangeState?: boolean;
//   } & RequestBase
// ) {
//   return request<never, Response<SlaConfigList[]>>({
//     url: `${SERVER.IAM}/current_user/profile`,
//     method: Method.Get,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: data,
//     data: {},
//   });
// }
//告警降级配置列表
export function getSlaDownConfigByPage(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/degrade/list`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//告警降级配置新增
export function AddSlaDownConfig(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/degrade/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//新增Sla规则
export function AddSlaConfig(data: { ruleName: string; ruleType: string; ruleDesc?: string } & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//全局告警降级配置新增
export function AddSlaDownGlobalConfig(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/degrade/createGlobal`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//编辑告警降级
export function GlobalSlaDownConfigEdit(
  data: {
    serviceName: string;
    slaRuleName: string;
    slaRuleId: number;
    degradeId: number;
    serviceDesc?: string;
    degradeName?: string;
  } & RequestBase
) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/degrade/editGlobal`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//新增全局Sla规则
export function AddGlobalSlaConfig(data: { ruleName: string; ruleType: string; ruleDesc?: string } & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/create/global`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//编辑Sla规则
export function EditSlaConfig(data: { ruleName: string; ruleType: string; ruleDesc?: string } & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/update/${data.ruleId}`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//编辑全局Sla规则
export function EditGlobalSlaConfig(data: { ruleName: string; ruleType: string; ruleDesc?: string } & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/update/global/${data.ruleId}`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//新增Sla子规则
export function AddSubrule(data: { ruleName: string; ruleType: string; ruleDesc?: string } & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/item/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//sla规则详情
export function DetailSlaConfig(
  data: /* {
    serviceName: string;
    slaRuleName: string;
    slaRuleId: number;
    degradeId: number;
    serviceDesc?: string;
    degradeName?: string;
  } */ RequestBase
) {
  return request<never, Response<Record<string, any>>>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/${data.ruleId}/detail`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//sla规则删除
export function DelSlaConfig(
  data: Record<string, unknown> /* {
    serviceName: string;
    slaRuleName: string;
    slaRuleId: number;
    degradeId: number;
    serviceDesc?: string;
    degradeName?: string;
  } */ &
    RequestBase
) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/${data.ruleId}/delete`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//告警降级策略删除
export function DelSlaDownConfig(
  data: /* {
    serviceName: string;
    slaRuleName: string;
    id: number;
    degradeId: number;
    serviceDesc?: string;
    degradeName?: string;
  } & */ RequestBase
) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/degrade/${data.id}/delete`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//sla启用/启用
export function EnableSlaConfig(
  data: Record<string, unknown> /* {
    serviceName: string;
    slaRuleName: string;
    slaRuleId: number;
    degradeId: number;
    serviceDesc?: string;
    degradeName?: string;
  } */ &
    RequestBase
) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/disable`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//告警降级策略启用
export function EnableSlaDownConfig(
  data: /* {
    serviceName: string;
    slaRuleName: string;
    slaRuleId: number;
    degradeId: number;
    serviceDesc?: string;
    degradeName?: string;
  } &  */ RequestBase
) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/degrade/${data.id}/enable`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//告警降级策略禁用
export function DisableSlaDownConfig(
  data: /* {
    serviceName: string;
    slaRuleName: string;
    slaRuleId: number;
    degradeId: number;
    serviceDesc?: string;
    degradeName?: string;
  } & */ RequestBase
) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/degrade/${data.id}/enable`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//告警降级详情
export function SlaDownConfigDetaile(
  data: {
    serviceName: string;
    slaRuleName: string;
    slaRuleId: number;
    degradeId: number;
    serviceDesc?: string;
    degradeName?: string;
  } & RequestBase
) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/degrade/${data.id}/detail`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//编辑告警降级
export function SlaDownConfigEdit(
  data: {
    serviceName: string;
    slaRuleName: string;
    slaRuleId: number;
    degradeId: number;
    serviceDesc?: string;
    degradeName?: string;
  } & RequestBase
) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/degrade/edit`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//更改sla降级默认
export function SlaConfigStatus(
  data: /* {
    serviceName: string;
    slaRuleName: string;
    slaRuleId: number;
    degradeId: number;
    serviceDesc?: string;
    degradeName?: string;
  } &  */ RequestBase
) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/${data.id}/default`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//更改告警降级默认
export function SlaDownConfigStatus(
  data: /* {
    serviceName: string;
    slaRuleName: string;
    slaRuleId: number;
    degradeId: number;
    serviceDesc?: string;
    degradeName?: string;
  } &  */ RequestBase
) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/degrade/${data.id}/default`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
interface EditSlaEntry {
  itemId: number;
  ruleId: number;
  level: string;
  effectiveBegin: object[];
  effectiveEnd: object[];
  response: object[];
  resolve: object[];
  overdueNotice: boolean;
  overdueNoticeTime: number;
}
/**
 * @desc 编辑sla条目
 *
 * @export
 * @param {(PostNoticeSub & RequestBase)} data
 * @return {*}
 */
//sla条目编辑
export function EditSlaEntry(data: EditSlaEntry & RequestBase) {
  return request<unknown, Response<string | object[]>[]>({
    url: `${SERVER.EVENT_CENTER}/sla/item/edit`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//获取默认sla规则
export function getSlaDefault(data: EditSlaEntry & RequestBase) {
  return request<unknown, Response<string | object[]>[]>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/default`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//获取默认降级规则
export function getDegradeDefault(data: EditSlaEntry & RequestBase) {
  return request<unknown, Response<string | object[]>[]>({
    url: `${SERVER.EVENT_CENTER}/degrade/getDefault`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//获取启用状态下的sla
export function getSlaEnableList(data: EditSlaEntry & RequestBase) {
  return request<unknown, Response<string | object[]>[]>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/enable/list`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//获取启用状态下的sla
export function getSlaEnableListV2(data: EditSlaEntry & RequestBase) {
  return request<unknown, Response<string | object[]>[]>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/enable/${data.containerId}/list`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//移除设备关联sla
export function deleteDeviceRelation(data: EditSlaEntry & RequestBase) {
  return request<unknown, Response<string | object[]>[]>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/device/${data.id}/remove/${data.slaId}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//告警降级分配区域
export function slaDownConfigRelation(data: RequestBase) {
  return request<unknown, Response<string | object[]>[]>({
    url: `${SERVER.EVENT_CENTER}/degrade/${data.id}/${data.type}`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {
      locations: data.locations,
      devices: data.devices,
      regions: data.regions,
    },
  });
}
//告警降级删除分配区域
export function slaUnDownConfigRelation(data: RequestBase) {
  return request<unknown, Response<string | object[]>[]>({
    url: `${SERVER.EVENT_CENTER}/degrade/${data.id}/${data.type}`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {
      locations: data.locations,
      devices: data.devices,
      regions: data.regions,
    },
  });
}

// --------------------------------------------sla2.0--------------------------------------------------------
// 新增SLA
export function AddNewSlaConfig(data: { ruleName: string; ruleDesc?: string; containerId: string } & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/2.0/rule`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//编辑SlA
export function EditNewSlaConfig(data: { ruleName: string; ruleDesc?: string } & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/sla/2.0/rule/${data.ruleId}`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//sla配置2.0--------------------------
//告警降级配置列表
export function getNewSlaDownConfigByPage(req: { pageNumber: number; pageSize: number } & RequestBase) {
  const data = {
    ...([...(req.includeName instanceof Array ? req.includeName : []), ...(req.excludeName instanceof Array ? req.excludeName : []), ...(req.eqName instanceof Array ? req.eqName : []), ...(req.neName instanceof Array ? req.neName : [])].filter((v) => v).length ? { nameFilterRelation: req.nameFilterRelation === "OR" ? "OR" : "AND", includeName: req.includeName instanceof Array && req.includeName.length ? req.includeName.join(",") : void 0, excludeName: req.excludeName instanceof Array && req.excludeName.length ? req.excludeName.join(",") : void 0, eqName: req.eqName instanceof Array && req.eqName.length ? req.eqName.join(",") : void 0, neName: req.neName instanceof Array && req.neName.length ? req.neName.join(",") : void 0 } : {}),

    ...([...(req.includeDescription instanceof Array ? req.includeDescription : []), ...(req.excludeDescription instanceof Array ? req.excludeDescription : []), ...(req.eqDescription instanceof Array ? req.eqDescription : []), ...(req.neDescription instanceof Array ? req.neDescription : [])].filter((v) => v).length ? { descriptionFilterRelation: req.descriptionFilterRelation === "OR" ? "OR" : "AND", includeDescription: req.includeDescription instanceof Array && req.includeDescription.length ? req.includeDescription.join(",") : void 0, excludeDescription: req.excludeDescription instanceof Array && req.excludeDescription.length ? req.excludeDescription.join(",") : void 0, eqDescription: req.eqDescription instanceof Array && req.eqDescription.length ? req.eqDescription.join(",") : void 0, neDescription: req.neDescription instanceof Array && req.neDescription.length ? req.neDescription.join(",") : void 0 } : {}),

    defaultable: req.defaultable,
  };

  const params = {
    pageNumber: req.pageNumber,
    pageSize: req.pageSize,
    degradeName: req.degradeName,
    degradeStatus: req.degradeStatus,
  };

  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/degrade/2.0/filter`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}
//新增
export function AddNewSlaDownConfig(data: {} & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/degrade/2.0/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//编辑
export function EditNewSlaDownConfig(data: {} & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/degrade/2.0/edit`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//编辑
export function DelNewSlaDownConfig(data: {} & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/degrade/2.0/delete/${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//详情
export function DetailNewSlaDownConfig(data: RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/degrade/2.0/detail/${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//保存所有的配置
export function AddAllNewSlaDownConfig(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.EVENT_CENTER}/degrade/${data.id}/assignWorkTime`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function hasSlaDownDefaultCloseCode(data: Partial<SlaConfigList> & RequestBase) {
  return request<SlaConfigList>({
    url: `${SERVER.EVENT_CENTER}/degrade/hasDefaultCloseCode/${data.id}`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//是否有默认的SLA
export function hasSLAcheckDefault(data: { tenantId: string } & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  return request<SlaConfigList>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/checkDefault?tenantId=${data.tenantId}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params,
    data: {},
  });
}
