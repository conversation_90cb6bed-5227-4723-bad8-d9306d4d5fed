import { SERVER, Method, type Response, type RequestBase, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

/**
 * 模块Api示例
 */
export interface ModuleItem {
  [key: string]: unknown;
  id: string;
  name: string;
}
export function getTriggerList /* 分页查询触发条件模版 */(data: { pageNumber: Number; pageSize: Number } & RequestBase) {
  const params = new URLSearchParams();
  bindParamByObj(
    {
      pageNumber: data.pageNumber,
      pageSize: data.pageSize,
      containerId: data.containerId,
      queryPermissionId: data.queryPermissionId,
      verifyPermissionIds: data.verifyPermissionIds,
      active: data.active,
    },
    params
  );

  bindParamByObj(
    {
      ...([...(data.includeName instanceof Array ? data.includeName : []), ...(data.excludeName instanceof Array ? data.excludeName : []), ...(data.eqName instanceof Array ? data.eqName : []), ...(data.neName instanceof Array ? data.neName : [])].filter((v) => v).length ? { nameFilterRelation: data.nameFilterRelation === "OR" ? "OR" : "AND", includeName: data.includeName instanceof Array && data.includeName.length ? data.includeName.join(",") : void 0, excludeName: data.excludeName instanceof Array && data.excludeName.length ? data.excludeName.join(",") : void 0, eqName: data.eqName instanceof Array && data.eqName.length ? data.eqName.join(",") : void 0, neName: data.neName instanceof Array && data.neName.length ? data.neName.join(",") : void 0 } : {}),

      ...([...(data.includeDescription instanceof Array ? data.includeDescription : []), ...(data.excludeDescription instanceof Array ? data.excludeDescription : []), ...(data.eqDescription instanceof Array ? data.eqDescription : []), ...(data.neDescription instanceof Array ? data.neDescription : [])].filter((v) => v).length ? { descriptionFilterRelation: data.descriptionFilterRelation === "OR" ? "OR" : "AND", includeDescription: data.includeDescription instanceof Array && data.includeDescription.length ? data.includeDescription.join(",") : void 0, excludeDescription: data.excludeDescription instanceof Array && data.excludeDescription.length ? data.excludeDescription.join(",") : void 0, eqDescription: data.eqDescription instanceof Array && data.eqDescription.length ? data.eqDescription.join(",") : void 0, neDescription: data.neDescription instanceof Array && data.neDescription.length ? data.neDescription.join(",") : void 0 } : {}),

      active: data.active || void 0,
    },
    params
  );
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/trigger_condition/2.0/list/filter`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params,
    data: {},
  });
}
export function addTriggerCondition /* 创建触发条件模版 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/trigger_condition/create`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "desc", "active", "containerId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function editTriggerCondition /* 更新触发条件模版 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/trigger_condition/${data.id}/update`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "desc", "active", "containerId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function delTriggerCondition /* 删除触发条件模版 */(data: Record<"id", string> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/trigger_condition/${data.id}/delete`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function getTriggerDetails /* 根据id查询触发条件详情 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/trigger_condition/${data.id}/details`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
let selectedOperation: any;
export function triggerConditionAdd /* 新增触发条件 */(data: Partial<ModuleItem> & RequestBase) {
  const operations = {
    EVENT: "eventOperation",
    SERVICE: "serviceOperation",
    QUESTION: "questionOperation",
    CHANGE: "changeOperation",
    PUBLISH: "publishOperation",
  };
  selectedOperation = operations[`${data.conditionType}`] || null;
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/trigger_condition/${data.id}/addCondition/${data.conditionType}`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),

    data: [`${selectedOperation}`].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
let selectedOperationA: any;
export function triggerConditionEdit /* 编辑触发条件 */(data: Partial<ModuleItem> & RequestBase) {
  const operations = {
    EVENT: "eventOperation",
    SERVICE: "serviceOperation",
    QUESTION: "questionOperation",
    CHANGE: "changeOperation",
    PUBLISH: "publishOperation",
  };
  selectedOperationA = operations[`${data.conditionType}`] || null;
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/trigger_condition/${data.id}/editCondition/${data.conditionType}`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [`${selectedOperationA}`].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function triggerConditionDel /* 删除触发条件模版 */(data: Record<"id", string> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/trigger_condition/${data.id}/deleteCondition/${data.conditionType}/${data.operationId}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
