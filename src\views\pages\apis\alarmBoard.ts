import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";
import { 资产管理中心_设备_可读 } from "@/views/pages/permission";



export interface AlarmQuery {
  tenantId: string;
  serviceRequestId: string;
  orderId: string;
  pageNumber: string;
  pageSize: string;
}

export function getAlarmBoard(data: Partial<AlarmQuery> & RequestBase) {
  return request<never, Response<string>>({
    url: `${SERVER.EVENT_CENTER}/alert`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { ...data },
    data: {},
  });
}

export function getAlarmBoardDetail(data: { includeAlerts: string[]; excludeAlerts: string[]; includeOrderIds: string[]; excludeOrderIds: string[] } & RequestBase) {
  const params = new URLSearchParams({ pageNumber: String(data?.pageNumber || 0), pageSize: String(data?.pageSize || 0) });
  (data.sort instanceof Array ? data.sort : []).forEach((v) => params.append("sort", v));

  bindSearchParams(
    {
      alertCreateTimeRange: data.alertCreateTimeRange,
      eventSeverity: data.eventSeverity,
      deviceId: data.deviceId,
      sort: data.sort,
      alarmBoardConfirmedTime: data.alarmBoardConfirmedTime,
      includeAlerts: data.includeAlerts != undefined ? data.includeAlerts.join() : [],
      excludeAlerts: data.excludeAlerts != undefined ? data.excludeAlerts.join() : [],
      includeOrderIds: data.includeOrderIds != undefined ? data.includeOrderIds.join() : [],
      excludeOrderIds: data.excludeOrderIds != undefined ? data.excludeOrderIds.join() : [],
      alertFilterRelation: data.alertFilterRelation,
      orderIdFilterRelation: data.orderIdFilterRelation,
    },
    params
  );

  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/alert/query_by_device`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params,
    data: {},
  });
}

export function getAlarmByOrderId(data: { orderId: string; pageNumber: number; pageSize: number } & RequestBase) {
  return request<unknown, Response<Record<string, string>[]>>({
    url: `${SERVER.EVENT_CENTER}/alert/${data.orderType}/query_by_order`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { ...data, permissionId: "612917424815079424" },
    data: {},
  });
}

export function getAlarmCodePayload(data: { id: string } & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/alert/${data.id}/payload`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { ...data },
    data: {},
  });
}

export function getAlarmBoardCount(data: { ids: string } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/alert/count/${data.ids}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function alarmCountEvent(data: { id: string } & RequestBase) {
  return request<unknown, Response<{ alertCount: string; deviceCount: string }>>({
    url: `${SERVER.EVENT_CENTER}/alert/count/${data.id}/event`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

// 告警板中的告警确认
export function alarmBoardConfirm(data: object & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/alert/alert_board/confirm`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function alarmEventConfirm(data: object & RequestBase, id: string) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/alert/event/confirm/${"612917424815079424"}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { orderId: id },
    data: data,
  });
}

export function alarmEventAllAlarmConfirm(data: { id: string } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/alert/event/${data.id}/confirm/${"612917424815079424"}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function getDeviceIdsById(data: { serviceRequestId: string; orderId: string } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/alert/device_id`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { ...data },
    data: {},
  });
}

export function getDeviceById(data: { ids: string } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/resources/${data.ids}/batch/detail/desensitized`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export interface ModuleItem {
  [key: string]: unknown;
  id: string;
  name: string;
}
export function getDeviceList /* 获取所有策略设备列表 */(data: { pageNumber: Number; pageSize: Number } & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    // url: `${SERVER.CMDB}/resources/${data.ids}/batch/detail/desensitized`,
    url: `${SERVER.CMDB}/resources/2.0/batch/detail/desensitized`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: Object.assign(
      ["ids"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
      {
        permissionId: 资产管理中心_设备_可读,
      }
    ),
  });
}