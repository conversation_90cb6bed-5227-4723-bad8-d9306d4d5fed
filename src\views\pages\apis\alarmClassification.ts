import { SERVER, Method, type Response, type RequestBase, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

/**
 * @description 获取所有告警分类-响应体
 * @url http://*************:3000/project/47/interface/api/23157
 */
export interface AlertClassifications {
  /** 主键 */
  id: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 分类名称 */
  name: string;
  /** 描述信息 */
  description?: string;
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}

/**
 * @description 获取所有告警分类
 * @url http://*************:3000/project/47/interface/api/23157
 */
export async function getAlarmClassificationList(req: { containerId: string; queryPermissionId: string; verifyPermissionIds: string } & Record<string, any>) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/alert_classifications/2.0/filter`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId, queryPermissionId: req.queryPermissionId, verifyPermissionIds: req.verifyPermissionIds }, $req.params);
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 服务管理中心_告警分类_可读, 服务管理中心_告警分类_新增, 服务管理中心_告警分类_编辑, 服务管理中心_告警分类_删除, 服务管理中心_告警分类_安全 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [服务管理中心_告警分类_可读].join(","), verifyPermissionIds: [服务管理中心_告警分类_新增, 服务管理中心_告警分类_编辑, 服务管理中心_告警分类_删除, 服务管理中心_告警分类_安全].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }

        bindParamByObj(
          {
            ...([...(req.includeName instanceof Array ? req.includeName : []), ...(req.excludeName instanceof Array ? req.excludeName : []), ...(req.eqName instanceof Array ? req.eqName : []), ...(req.neName instanceof Array ? req.neName : [])].filter((v) => v).length ? { nameFilterRelation: req.nameFilterRelation === "OR" ? "OR" : "AND", includeName: req.includeName instanceof Array && req.includeName.length ? req.includeName.join(",") : void 0, excludeName: req.excludeName instanceof Array && req.excludeName.length ? req.excludeName.join(",") : void 0, eqName: req.eqName instanceof Array && req.eqName.length ? req.eqName.join(",") : void 0, neName: req.neName instanceof Array && req.neName.length ? req.neName.join(",") : void 0 } : {}),

            ...([...(req.includeDescription instanceof Array ? req.includeDescription : []), ...(req.excludeDescription instanceof Array ? req.excludeDescription : []), ...(req.eqDescription instanceof Array ? req.eqDescription : []), ...(req.neDescription instanceof Array ? req.neDescription : [])].filter((v) => v).length ? { descriptionFilterRelation: req.descriptionFilterRelation === "OR" ? "OR" : "AND", includeDescription: req.includeDescription instanceof Array && req.includeDescription.length ? req.includeDescription.join(",") : void 0, excludeDescription: req.excludeDescription instanceof Array && req.excludeDescription.length ? req.excludeDescription.join(",") : void 0, eqDescription: req.eqDescription instanceof Array && req.eqDescription.length ? req.eqDescription.join(",") : void 0, neDescription: req.neDescription instanceof Array && req.neDescription.length ? req.neDescription.join(",") : void 0 } : {}),
          },
          $req.params
        );
        return $req;
      })
      .then(($req) => request<never, Response<AlertClassifications[]>>($req)),
    { controller }
  );
}

//新增告警分类
export function addAlarmClassification(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<AlertClassifications>>({
    url: `${SERVER.CMDB}/alert_classifications`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//编辑告警分类
export function editAlarmClassification(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<AlertClassifications>>({
    url: `${SERVER.CMDB}/alert_classifications/${data.id}`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//删除告警分类
export function deleteAlarmClassification(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<AlertClassifications>>({
    url: `${SERVER.CMDB}/alert_classifications/${data.id}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//关联设备
export function AlarmClassificationRelationDevice(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<AlertClassifications>>({
    url: `${SERVER.CMDB}/alert_classifications/${data.id}/add/resources`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//移除设备
export function AlarmClassificationDelRelationDevice(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<AlertClassifications>>({
    url: `${SERVER.CMDB}/alert_classifications/${data.id}/remove/resources`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
