import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";

// export interface AlarmQuery {
//   tenantId: string;
//   serviceRequestId: string;
//   orderId: string;
//   pageNumber: string;
//   pageSize: string;
// }

export function getAlarmMerge(data: {} & RequestBase) {
  return request<never, Response<string>>({
    url: `${SERVER.EVENT_CENTER}/auto_event_config`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function setAlarmMerge(data: {} & RequestBase) {
  return request<never, Response<string>>({
    url: `${SERVER.EVENT_CENTER}/auto_event_config`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
