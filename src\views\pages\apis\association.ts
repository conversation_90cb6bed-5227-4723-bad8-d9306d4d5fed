import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";
import { priority } from "./eventPriority";
export { priority };

export enum OrderType {
  /**@type {string} - 事件单 */
  EVENT_ORDER = "EVENT_ORDER",
  /**@type {string} - 服务请求 */
  SERVICE_REQUEST = "SERVICE_REQUEST",
  /**@type {string} - 变更 */
  CHANGE = "CHANGE",
  /**@type {string} - 问题 */
  QUESTION = "QUESTION",
  /**@type {string} - 发布 */
  PUBLISH = "PUBLISH",
  /**@type {string} - DICT事件 */
  DICT_EVENT_ORDER = "DICT_EVENT_ORDER",
  /**@type {string} - DICT服务请求 */
  DICT_SERVICE_REQUEST = "DICT_SERVICE_REQUEST",
}

export enum OrderState {
  NEW = "NEW",
  UNASSIGNED = "UNASSIGNED",
  WAITING_FOR_RECEIVE = "WAITING_FOR_RECEIVE",
  PROCESSING = "PROCESSING",
  PENDING_APPROVAL = "PENDING_APPROVAL",
  CUSTOMER_SUSPENDED = "CUSTOMER_SUSPENDED",
  SERVICE_PROVIDER_SUSPENDED = "SERVICE_PROVIDER_SUSPENDED",
  SUSPENDED = "SUSPENDED",
  COMPLETED = "COMPLETED",
  CLOSED = "CLOSED",
  AUTO_CLOSED = "AUTO_CLOSED",
  SUSPEND = "SUSPEND",
  DISPOSE = "DISPOSE",
  TRANSFER = "TRANSFER",
  WITHDRAW = "WITHDRAW",
  COMPLETE = "COMPLETE",
  CLOSE = "CLOSE",
  NOT_STARTED = "NOT_STARTED",
  ABANDON = "ABANDON",
  CONFIRM_COMPLETE = "CONFIRM_COMPLETE",
}

export const orderState = [
  { label: "新建", value: OrderState.NEW, color: "#", type: "danger" },
  { label: "未开始", value: OrderState.NOT_STARTED, color: "#", type: "danger" },
  { label: "处理中", value: OrderState.PROCESSING, color: "#", type: "warning" },
  { label: "完成", value: OrderState.COMPLETED, color: "#", type: "success" },
  { label: "自动关闭", value: OrderState.AUTO_CLOSED, color: "#", type: "" },
  { label: "挂起", value: OrderState.SUSPENDED, color: "#", type: "" },
  { label: "待审批", value: OrderState.PENDING_APPROVAL, color: "#", type: "" },

  { label: "客户挂起", value: OrderState.CUSTOMER_SUSPENDED, color: "#", type: "" },
  { label: "服务商挂起", value: OrderState.SERVICE_PROVIDER_SUSPENDED, color: "#", type: "" },
  { label: "关闭", value: OrderState.CLOSED, color: "#", type: "" },
  { label: "关闭", value: OrderState.CLOSE },
  { label: "撤回", value: OrderState.WITHDRAW },
  { label: "转交", value: OrderState.TRANSFER },
  { label: "舍弃", value: OrderState.ABANDON },
  { label: "处理", value: OrderState.DISPOSE },
  { label: "确认全部并完成", value: OrderState.CONFIRM_COMPLETE },
];

export const orderType = [
  { label: "事件", value: OrderType.EVENT_ORDER },
  { label: "服务请求", value: OrderType.SERVICE_REQUEST },
  { label: "变更", value: OrderType.CHANGE },
  { label: "问题", value: OrderType.QUESTION },
  { label: "发布", value: OrderType.PUBLISH },
];

export interface AssociationV2Owner {
  orderId: string;
  orderType: keyof typeof OrderType;
  priority: keyof typeof priority;
  state: string;
  digest: string;
}

export function getAssociationV2Owner(data: { orderId: string } & RequestBase) {
  return request<unknown, Response<AssociationV2Owner[]>>({
    url: `${SERVER.EVENT_CENTER}/association_v2/${data.orderId}/owner`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { ...data },
    data: {},
  });
}
//  新工单
export function getAssociationV2FindNoNow(data: { orderId: string,queryId: string } & RequestBase) {
  return request<unknown, Response<AssociationV2Owner[]>>({
    url: `${SERVER.EVENT_CENTER}/association_v2/findNoNow`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { ...data },
    data: {},
  });
}
export interface AssociationV2ByRegexByPage {
  orderId: string;
  orderType: keyof typeof OrderType;
  priority: keyof typeof priority;
  state: string;
  digest: string;
}
export function getAssociation_v2ByRegexByPage(data: { orderId: string; pageNumber: number; pageSize: number } & RequestBase) {
  return request<unknown, Response<AssociationV2ByRegexByPage[]>>({
    url: `${SERVER.EVENT_CENTER}/association_v2`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { ...data },
    data: {},
  });
}

export function eventGetAssociation(data: { orderId: string } & RequestBase) {
  return request<unknown, Response<AssociationV2Owner[]>>({
    url: `${SERVER.EVENT_CENTER}/association_v2/${data.orderId}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { ...data, permissionId: "612917477428428800" },
    data: {},
  });
}

export function eventCreateAssociationCascade(data: { fromId: string; toId: string; permissionId: string } & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/association_v2/cascade`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { ...data },
  });
}

export function secureAssociation(data: { fromId: string; toId: string } & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/association_v2/${data.fromId}/${data.toId}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
