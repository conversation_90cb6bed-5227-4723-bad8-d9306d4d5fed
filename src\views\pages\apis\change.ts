import { SERVER, Method, type Response, type RequestBase, bindSearchParams, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

import { priority, priorityOption } from "./eventPriority";
export { priority, priorityOption };
import { eventSeverityOption, eventSeverity } from "./event";
export { eventSeverityOption, eventSeverity };
import { deviceImportance, deviceImportanceOption } from "./device";
export { deviceImportance, deviceImportanceOption };

import { 智能事件中心_变更工单_分配设备 } from "@/views/pages/permission";

import { i18n } from "@/lang/index";

import { OrderType } from "@/views/pages/apis/association";

const { t } = i18n.global;

/**
 * 基本分页字段
 */
interface PageFilter {
  paging: { pageNumber: number; pageSize: number };
  sort: string[];
}
/**
 * 基本时间字段
 */
interface DateRangeFilter {
  /** 创建时间起始时间 */
  createTimeStart: string;
  /** 创建时间结束时间 */
  createTimeEnd: string;
  /** 修改时间起始时间 */
  updateTimeStart: string;
  /** 修改时间结束时间 */
  updateTimeEnd: string;
  /** 紧凑模式下时间筛选-起始时间 */
  compactTimeStart: string;
  /** 紧凑模式下时间筛选-结束时间 */
  compactTimeEnd: string;
}
/**
 * 类型校验合并
 */
type Merge<T1 extends object, T2 extends object, MT = T1 & T2> = { [MK in keyof MT]: MT[MK] };
/**
 * 条件过滤器方法
 */
type ConditionFilter<N extends string, TT extends string, F extends string, TE extends string, OT = { [P in N extends `${infer F}${infer E}` ? `${TT}${Uppercase<F>}${E}` : never]: string[] } & { [P in F extends `${infer F}${infer E}` ? `${N}${Uppercase<F>}${E}` : never]: TE }> = { [P in keyof OT]: OT[P] };
/**
 * 类型校对方法
 */
type HasConditionFilter<Diff0 extends object, Diff1 extends object> = Merge<Record<keyof Omit<Diff0, keyof Diff1>, 0>, Record<keyof Omit<Diff1, keyof Diff0>, 1>>;

export enum ChangeType {
  ORDINARY = "ORDINARY",
  STANDARD = "STANDARD",
  URGENCY = "URGENCY",
  IMPORTANT = "IMPORTANT",
}

export const changeType = [
  { label: "一般变更", value: ChangeType.ORDINARY },
  { label: "标准变更", value: ChangeType.STANDARD },
  { label: "紧急变更 ", value: ChangeType.URGENCY },
  { label: "重大变更", value: ChangeType.IMPORTANT },
];

export enum changeState {
  UN_APPROVED = "UN_APPROVED",
  SUSPEND = "SUSPEND",
  PROCESSING = "PROCESSING",
  AUTO_CLOSED = "AUTO_CLOSED",
  CLOSED = "CLOSED",
}

export enum changeOperate {
  CLOSED = "CLOSE",
  WITHDRAW = "WITHDRAW",
  ABANDON = "ABANDON",
  COMPLETE = "COMPLETE",
  DISPOSE = "DISPOSE",
  // confirmAllAndComplete
  CONFIRM_COMPLETE = "CONFIRM_COMPLETE",
}

export const changeOperateOption = [
  { label: "关闭", value: changeOperate.CLOSED },
  { label: "撤回", value: changeOperate.WITHDRAW },
  { label: "舍弃", value: changeOperate.ABANDON },
  { label: "完成", value: changeOperate.COMPLETE },
  { label: "处理", value: changeOperate.DISPOSE },
  { label: "确认全部并完成", value: changeOperate.CONFIRM_COMPLETE },
];

export const changeStateOption: { label: string; value: keyof typeof changeState; color?: string; type?: "success" | "warning" | "info" | "danger" }[] = [
  // { label: "未审批", value: changeState.UN_APPROVED, color: "#ED4013", type: "danger" },
  { label: t("event.挂起"), value: changeState.SUSPEND, color: "#2CB6F4", type: "warning" },
  { label: t("event.处理中"), value: changeState.PROCESSING, color: "#3EBE6B", type: "success" },
  { label: t("event.自动关闭"), value: changeState.AUTO_CLOSED, color: "#3EBE6B", type: void 0 },
  { label: t("event.关闭"), value: changeState.CLOSED, color: "#3EBE6B", type: void 0 },
];

export interface Change {
  id: string;
  tenantId: string;
  identifier: string;
  changeType: string;
  digest: string;
  priority: string;
  changeState: string;
  teamId: string;
  userId: string;
  importance: string;
  severity: string;
  startTime: string;
  endTime: string;
  desc: string;
  collectAlert: boolean;
  alertNumber: string;
  contacts: {
    contactId: string;
    contactType: string;
  }[];
  deviceIds: string[];
  createdBy: string;
  updatedBy: string;
  createdTime: string;
  updatedTime: string;
  externalId: string;
  code: {
    name: string;
    desc: string;
  };
  content: string;
  dynamicList: {
    dynamicId: string;
    eventId: string;
    dynamicContent: string;
    eventCreatorId: string;
    eventCreatorName: string;
    eventTransferId: string;
    eventTransferName: string;
    dynamicCreateTime: string;
  }[];
  operation: changeOperate;
  approveState: "UN_APPROVE" | "APPROVED";
  originSlaId: string;
  originPriority: priority;

  draft: boolean;

  verifyPermissionIds: string[];
  orderType: keyof typeof OrderType;
}

/**
 * @description 变更列表
 */
export interface ChangeItem {
  /** 主键 */
  id: string;
  /** 租户id */
  tenantId: string;
  /** 租户名称 */
  tenantName: string;
  /** 租户缩写 */
  tenantAbbreviation: string;
  /** 工单编号 */
  identifier: string;
  /** 工单类型 */
  orderType: /* 枚举: EVENT_ORDER :事件单 | SERVICE_REQUEST :服务请求 | CHANGE :变更 | QUESTION :问题 | PUBLISH :发布 */ import("./association").OrderType.CHANGE;
  /** 类型 */
  changeType: /* 枚举: ORDINARY :一般 | STANDARD :标准 | URGENCY :紧急 | IMPORTANT :重大 */ "ORDINARY" | "STANDARD" | "URGENCY" | "IMPORTANT";
  /** 摘要 */
  digest: string;
  /** 优先级 */
  priority: /* 枚举: P1 :P1~P7 优先级递减 | P2 :P2 | P3 :P3 | P4 :P4 | P5 :P5 | P6 :P6 | P7 :P7 | P8 :P8 手动 */ "P1" | "P2" | "P3" | "P4" | "P5" | "P6" | "P7" | "P8";
  /** 状态 */
  changeState: /* 枚举: SUSPEND :挂起 | PROCESSING :处理中 | AUTO_CLOSED :自动关闭 | CLOSED :关闭 */ "SUSPEND" | "PROCESSING" | "AUTO_CLOSED" | "CLOSED";
  /** 审批状态 */
  approveState: /* 枚举: UN_APPROVE :未审批 | APPROVED :已审批 */ "UN_APPROVE" | "APPROVED";
  /** 操作 */
  operation: /* 枚举: DISPOSE :处理 | WITHDRAW :撤回 | ABANDON :舍弃 | COMPLETE :完成 | CLOSE :关闭 */ "DISPOSE" | "WITHDRAW" | "ABANDON" | "COMPLETE" | "CLOSE";
  /** 用户组id */
  teamId?: string;
  /** 用户id */
  userId?: string;
  /** 处理人id */
  actorId: string;
  /** 处理人名称 */
  actorName: string;
  /** 重要性 */
  importance?: /* 枚举: High :高 | Medium :中 | Low :低 | None :无 | Unknown :未知 */ "High" | "Medium" | "Low" | "None" | "Unknown";
  /** 紧急性 */
  severity?: /* 枚举: Critical :Critical | Major :Major | Minor :Minor | Warning :Warning | Unknown :Unknown | Normal :Normal | Informational :Informational | Calculating :Calculating | Symptom :Symptom | Monitoring :Monitoring | Others :在映射中展示Critical -- Monitoring, */ "Critical" | "Major" | "Minor" | "Warning" | "Unknown" | "Normal" | "Informational" | "Calculating" | "Symptom" | "Monitoring" | "Others";
  /** 工单影响性 */
  influence: /* 枚举: High :High | Medium :Medium | Low :Low | None :None | Unknown :Unknown */ "High" | "Medium" | "Low" | "None" | "Unknown";
  /** 工单紧急性 */
  urgency: /* 枚举: High :High | Medium :Medium | Low :Low | None :None | Unknown :Unknown */ "High" | "Medium" | "Low" | "None" | "Unknown";
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 描述 */
  desc: string;
  /** 外部id */
  externalId: string;
  /** 收集告警，默认开启，关闭后不能启用 */
  collectAlert: boolean;
  collectAlertEcho?: boolean;
  /** 告警数量 */
  alertNumber: string;
  /** 联系人列表 */
  contacts: {
    /** 联系人id */
    contactId: string;
    /** 联系人类型 */
    contactType: /* 枚举: Notification :通知联系人 | Technical :技术联系人 | OnSite :现场联系人 */ "Notification" | "Technical" | "OnSite";
  }[];
  /** 设备id列表 */
  deviceIds: string[];
  /** 创建人信息 */
  createdBy?: string;
  /** 更新人信息 */
  updatedBy?: string;
  /** 完结代码 */
  code?: {
    /** 名称 */
    name: string;
    /** 描述 */
    desc: string;
  };
  /** 完结内容 */
  content: string;
  /** 创建时间 */
  createdTime?: string;
  /** 更新时间 */
  updatedTime?: string;

  category: string;
}

export function changeCreate(data: Partial<{ changeType: keyof typeof ChangeType; startTime: string; endTime: string; digest: string; priority: string; desc: string }> & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
    data: ["changeType", "startTime", "endTime", "digest", "priority", "desc", "ticketClassificationId", "ticketSubtype", "ticketTemplateId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

// export function getChangeItems(data: {} & RequestBase) {
//   const params = new URLSearchParams({ pageNumber: String(data.pageNumber || 0), pageSize: String(data.pageSize || 0) });
//   (data.sort instanceof Array ? data.sort : []).forEach((v) => params.append("sort", v));
//   bindSearchParams({ identifier: data.identifier, changeState: data.changeState, digest: data.digest, priority: data.priority, tenantId: data.tenantId, createdBy: { username: data.responsibleName }, actorName: data.actorName, permissionId: "612917524815675392" }, params);
//   bindSearchParams({ createTimeStart: ((data.createTime as Record<string, string>) || {}).start, createTimeEnd: ((data.createTime as Record<string, string>) || {}).end }, params);
//   bindSearchParams({ updateTimeStart: ((data.updateTime as Record<string, string>) || {}).start, updateTimeEnd: ((data.updateTime as Record<string, string>) || {}).end }, params);
//   return request<unknown, Response<Change>>({
//     url: `${SERVER.EVENT_CENTER}/change/current`,
//     method: Method.Get,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params,
//     data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
//   });
// }

export interface ChangeQuery {
  tenantName?: /** 租户名称 */ string;
  priority?: /** 优先级 */ string;
  state?: string;
  changeState?: /** 变更状态 SUSPEND :挂起 PROCESSING :处理中 AUTO_CLOSED :自动关闭 CLOSED :关闭 */ string;
  identifier?: /** 工单号 */ string;
  digest?: /** 摘要 */ string;
  createdBy: { userId: string; username: string };
  updatedBy: { userId: string; username: string };
  actorName?: /** 处理人 */ string;
  permissionId?: string;
  boardOrNot?: boolean;
}
/**
 * @description 分页查询变更列表,当前租户
 * @url http://*************:3000/project/17/interface/api/3773
 */
export function getChangeItems(req: ChangeQuery & PageFilter & { createdTimeRange: { start: string; end: string }; updatedTimeRange: { start: string; end: string } } & Merge<ConditionFilter<"tenantName" | "orderId" | "orderSummary" | "state" | "actorName" | "responsibleName", "include" | "exclude" | "eq" | "ne", "FilterRelation", "AND" | "OR">, ConditionFilter<"alarmCount", "eq" | "ne" | "ge" | "gt" | "le" | "lt" | "isNull" | "isNotNull", "FilterRelation", "AND" | "OR">>) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/change/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj(
          {
            /*  */
            tenantName: req.tenantName /* 租户名称 */,
            priority: req.priority || void 0 /* 优先级 */,
            state: req.state,
            changeState: !req.state ? req.changeState : void 0 /* 变更状态SUSPEND :挂起PROCESSING :处理中AUTO_CLOSED :自动关闭CLOSED :关闭 */,
            identifier: req.identifier /* 工单号 */,
            digest: req.digest /* 摘要 */,

            createdTimeRange: req.createdTimeRange,
            updatedTimeRange: req.updatedTimeRange,

            createdBy: req.createdBy,
            updatedBy: req.updatedBy,
            actorName: req.actorName /* 处理人 */,

            /* 租户名称 */
            ...([...(req.includeTenantName instanceof Array ? req.includeTenantName : []), ...(req.excludeTenantName instanceof Array ? req.excludeTenantName : []), ...(req.eqTenantName instanceof Array ? req.eqTenantName : []), ...(req.neTenantName instanceof Array ? req.neTenantName : [])].filter((v) => v).length ? { tenantNameFilterRelation: req.tenantNameFilterRelation === "OR" ? "OR" : "AND", includeTenantName: req.includeTenantName instanceof Array && req.includeTenantName.length ? req.includeTenantName.join(",") : void 0, excludeTenantName: req.excludeTenantName instanceof Array && req.excludeTenantName.length ? req.excludeTenantName.join(",") : void 0, eqTenantName: req.eqTenantName instanceof Array && req.eqTenantName.length ? req.eqTenantName.join(",") : void 0, neTenantName: req.neTenantName instanceof Array && req.neTenantName.length ? req.neTenantName.join(",") : void 0 } : {}),

            /* 工单号 */
            ...([...(req.includeOrderId instanceof Array ? req.includeOrderId : []), ...(req.excludeOrderId instanceof Array ? req.excludeOrderId : []), ...(req.eqOrderId instanceof Array ? req.eqOrderId : []), ...(req.neOrderId instanceof Array ? req.neOrderId : [])].filter((v) => v).length ? { orderIdFilterRelation: req.orderIdFilterRelation === "OR" ? "OR" : "AND", includeOrderIds: req.includeOrderId instanceof Array && req.includeOrderId.length ? req.includeOrderId.join(",") : void 0, excludeOrderIds: req.excludeOrderId instanceof Array && req.excludeOrderId.length ? req.excludeOrderId.join(",") : void 0, eqOrderIds: req.eqOrderId instanceof Array && req.eqOrderId.length ? req.eqOrderId.join(",") : void 0, neOrderIds: req.neOrderId instanceof Array && req.neOrderId.length ? req.neOrderId.join(",") : void 0 } : {}),

            /* 工单摘要 */
            ...([...(req.includeOrderSummary instanceof Array ? req.includeOrderSummary : []), ...(req.excludeOrderSummary instanceof Array ? req.excludeOrderSummary : []), ...(req.eqOrderSummary instanceof Array ? req.eqOrderSummary : []), ...(req.neOrderSummary instanceof Array ? req.neOrderSummary : [])].filter((v) => v).length ? { orderSummaryFilterRelation: req.orderSummaryFilterRelation === "OR" ? "OR" : "AND", includeOrderSummary: req.includeOrderSummary instanceof Array && req.includeOrderSummary.length ? req.includeOrderSummary.join(",") : void 0, excludeOrderSummary: req.excludeOrderSummary instanceof Array && req.excludeOrderSummary.length ? req.excludeOrderSummary.join(",") : void 0, eqOrderSummary: req.eqOrderSummary instanceof Array && req.eqOrderSummary.length ? req.eqOrderSummary.join(",") : void 0, neOrderSummary: req.neOrderSummary instanceof Array && req.neOrderSummary.length ? req.neOrderSummary.join(",") : void 0 } : {}),

            /* 状态 */
            ...([...(req.includeState instanceof Array ? req.includeState : []), ...(req.excludeState instanceof Array ? req.excludeState : []), ...(req.eqState instanceof Array ? req.eqState : []), ...(req.neState instanceof Array ? req.neState : [])].filter((v) => v).length ? { stateFilterRelation: req.stateFilterRelation === "OR" ? "OR" : "AND", includeStates: req.includeState instanceof Array && req.includeState.length ? req.includeState.join(",") : void 0, excludeStates: req.excludeState instanceof Array && req.excludeState.length ? req.excludeState.join(",") : void 0, eqStates: req.eqState instanceof Array && req.eqState.length ? req.eqState.join(",") : void 0, neStates: req.neState instanceof Array && req.neState.length ? req.neState.join(",") : void 0 } : {}),

            /* 告警数 */
            ...([...(req.eqAlarmCount instanceof Array ? req.eqAlarmCount : []), ...(req.neAlarmCount instanceof Array ? req.neAlarmCount : []), ...(req.geAlarmCount instanceof Array ? req.geAlarmCount : []), ...(req.gtAlarmCount instanceof Array ? req.gtAlarmCount : []), ...(req.leAlarmCount instanceof Array ? req.leAlarmCount : []), ...(req.ltAlarmCount instanceof Array ? req.ltAlarmCount : []), ...(req.isNullAlarmCount instanceof Array ? req.isNullAlarmCount : []), ...(req.isNotNullAlarmCount instanceof Array ? req.isNotNullAlarmCount : [])].filter((v) => v).length ? { alarmCountFilterRelation: req.alarmCountFilterRelation === "OR" ? "OR" : "AND", eqAlarmCount: req.eqAlarmCount instanceof Array && req.eqAlarmCount.length ? req.eqAlarmCount.join(",") : void 0, neAlarmCount: req.neAlarmCount instanceof Array && req.neAlarmCount.length ? req.neAlarmCount.join(",") : void 0, geAlarmCount: req.geAlarmCount instanceof Array && req.geAlarmCount.length ? req.geAlarmCount.join(",") : void 0, gtAlarmCount: req.gtAlarmCount instanceof Array && req.gtAlarmCount.length ? req.gtAlarmCount.join(",") : void 0, leAlarmCount: req.leAlarmCount instanceof Array && req.leAlarmCount.length ? req.leAlarmCount.join(",") : void 0, ltAlarmCount: req.ltAlarmCount instanceof Array && req.ltAlarmCount.length ? req.ltAlarmCount.join(",") : void 0, isNullAlarmCount: req.isNullAlarmCount instanceof Array && req.isNullAlarmCount.length ? req.isNullAlarmCount.join(",") : void 0, isNotNullAlarmCount: req.isNotNullAlarmCount instanceof Array && req.isNotNullAlarmCount.length ? req.isNotNullAlarmCount.join(",") : void 0 } : {}),

            /* 处理人 */
            ...([...(req.includeActorName instanceof Array ? req.includeActorName : []), ...(req.excludeActorName instanceof Array ? req.excludeActorName : []), ...(req.eqActorName instanceof Array ? req.eqActorName : []), ...(req.neActorName instanceof Array ? req.neActorName : [])].filter((v) => v).length ? { actorNameFilterRelation: req.actorNameFilterRelation === "OR" ? "OR" : "AND", includeActorName: req.includeActorName instanceof Array && req.includeActorName.length ? req.includeActorName.join(",") : void 0, excludeActorName: req.excludeActorName instanceof Array && req.excludeActorName.length ? req.excludeActorName.join(",") : void 0, eqActorName: req.eqActorName instanceof Array && req.eqActorName.length ? req.eqActorName.join(",") : void 0, neActorName: req.neActorName instanceof Array && req.neActorName.length ? req.neActorName.join(",") : void 0 } : {}),

            /* 负责人 */
            ...([...(req.includeResponsibleName instanceof Array ? req.includeResponsibleName : []), ...(req.excludeResponsibleName instanceof Array ? req.excludeResponsibleName : []), ...(req.eqResponsibleName instanceof Array ? req.eqResponsibleName : []), ...(req.neResponsibleName instanceof Array ? req.neResponsibleName : [])].filter((v) => v).length ? { responsibleNameFilterRelation: req.responsibleNameFilterRelation === "OR" ? "OR" : "AND", includeResponsibleName: req.includeResponsibleName instanceof Array && req.includeResponsibleName.length ? req.includeResponsibleName.join(",") : void 0, excludeResponsibleName: req.excludeResponsibleName instanceof Array && req.excludeResponsibleName.length ? req.excludeResponsibleName.join(",") : void 0, eqResponsibleName: req.eqResponsibleName instanceof Array && req.eqResponsibleName.length ? req.eqResponsibleName.join(",") : void 0, neResponsibleName: req.neResponsibleName instanceof Array && req.neResponsibleName.length ? req.neResponsibleName.join(",") : void 0 } : {}),

            pageNumber: req.paging.pageNumber /* 页码, 默认第一页 */,
            pageSize: req.paging.pageSize /* 页大小, 默认10 */,
            sort: req.sort,
            permissionId: "612917524815675392",
          },
          $req.params
        );
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, Response<ChangeItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 分页查询变更列表
 * @url http://*************:3000/project/17/interface/api/3773
 */
export async function getChangeList(req: { type: "" | "owner" | "current" } & Record<string, any> & ChangeQuery & PageFilter & DateRangeFilter & Merge<ConditionFilter<"tenantName" | "orderId" | "orderSummary" | "state" | "actorName" | "responsibleName", "include" | "exclude" | "eq" | "ne", "FilterRelation", "AND" | "OR">, ConditionFilter<"alarmCount", "eq" | "ne" | "ge" | "gt" | "le" | "lt" | "isNull" | "isNotNull", "FilterRelation", "AND" | "OR">> & ConditionFilter<"compactActorName", "eq" | "ne" | "include" | "exclude", "FilterRelation", "AND" | "OR">) {
  const controller = new AbortController();

  const userInfo = (await import("@/utils/getUserInfo")).default();
  const { 智能事件中心_客户_工单可读, 智能事件中心_项目_工单可读, 智能事件中心_联系人_工单可读, 智能事件中心_设备_工单可读 } = await import("@/views/pages/permission");

  let urlFlag: "all" | "permission" = "all";
  let url;
  let method;

  if (userInfo.hasPermission(智能事件中心_客户_工单可读)) {
    urlFlag = "all";
    url = `${SERVER.EVENT_CENTER}/change${req.type ? `/${req.type}` : ""}`;
    method = Method.Get;
  } else if (
    /*  */
    (userInfo.hasPermission("756061441173225472" as any) && userInfo.hasPermission(智能事件中心_项目_工单可读)) ||
    (userInfo.hasPermission("756062477225033728" as any) && userInfo.hasPermission(智能事件中心_联系人_工单可读)) ||
    (userInfo.hasPermission("756062918394511360" as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))
  ) {
    urlFlag = "permission";
    url = {
      "": `${SERVER.EVENT_CENTER}/allChangeList`,
      "owner": `${SERVER.EVENT_CENTER}/change/myChangeList`,
    }[req.type];
    method = Method.Post;
  }

  if (!url) return { success: true, data: [], message: "" };

  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url, method, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();

        const query = {
          /*  */
          tenantName: req.tenantName /* 租户名称 */,
          priority: req.priority || void 0 /* 优先级 */,
          queryType: req.queryType /* 工单参数 */,
          state: req.state,
          changeState: req.changeState /* 变更状态SUSPEND :挂起PROCESSING :处理中AUTO_CLOSED :自动关闭CLOSED :关闭 */,
          identifier: req.identifier /* 工单号 */,
          digest: req.digest /* 摘要 */,
          createdBy: req.createdBy,
          updatedBy: req.updatedBy,
          actorName: req.actorName /* 处理人 */,
          boardOrNot: "boardOrNot" in req ? req.boardOrNot : true,

          /* 创建时间 */
          ...(req.createTimeStart && req.createTimeEnd ? { createdTimeRange: { start: req.createTimeStart || void 0, end: req.createTimeEnd || void 0 } } : {}),
          /* 修改时间 */
          ...(req.updateTimeStart && req.updateTimeEnd ? { updatedTimeRange: { start: req.updateTimeStart || void 0, end: req.updateTimeEnd || void 0 } } : {}),
          /* 创建时间AND修改时间 */
          ...(req.compactTimeStart && req.compactTimeEnd ? { compactTimeStart: req.compactTimeStart, compactTimeEnd: req.compactTimeEnd } : {}),

          /* 租户名称 */
          ...([...(req.includeTenantName instanceof Array ? req.includeTenantName : []), ...(req.excludeTenantName instanceof Array ? req.excludeTenantName : []), ...(req.eqTenantName instanceof Array ? req.eqTenantName : []), ...(req.neTenantName instanceof Array ? req.neTenantName : [])].filter((v) => v).length ? { tenantNameFilterRelation: req.tenantNameFilterRelation === "OR" ? "OR" : "AND", includeTenantName: req.includeTenantName instanceof Array && req.includeTenantName.length ? req.includeTenantName.join(",") : void 0, excludeTenantName: req.excludeTenantName instanceof Array && req.excludeTenantName.length ? req.excludeTenantName.join(",") : void 0, eqTenantName: req.eqTenantName instanceof Array && req.eqTenantName.length ? req.eqTenantName.join(",") : void 0, neTenantName: req.neTenantName instanceof Array && req.neTenantName.length ? req.neTenantName.join(",") : void 0 } : {}),

          /* 工单号 */
          ...([...(req.includeOrderId instanceof Array ? req.includeOrderId : []), ...(req.excludeOrderId instanceof Array ? req.excludeOrderId : []), ...(req.eqOrderId instanceof Array ? req.eqOrderId : []), ...(req.neOrderId instanceof Array ? req.neOrderId : [])].filter((v) => v).length ? { orderIdFilterRelation: req.orderIdFilterRelation === "OR" ? "OR" : "AND", includeOrderIds: req.includeOrderId instanceof Array && req.includeOrderId.length ? req.includeOrderId.join(",") : void 0, excludeOrderIds: req.excludeOrderId instanceof Array && req.excludeOrderId.length ? req.excludeOrderId.join(",") : void 0, eqOrderIds: req.eqOrderId instanceof Array && req.eqOrderId.length ? req.eqOrderId.join(",") : void 0, neOrderIds: req.neOrderId instanceof Array && req.neOrderId.length ? req.neOrderId.join(",") : void 0 } : {}),

          /* 工单摘要 */
          ...([...(req.includeOrderSummary instanceof Array ? req.includeOrderSummary : []), ...(req.excludeOrderSummary instanceof Array ? req.excludeOrderSummary : []), ...(req.eqOrderSummary instanceof Array ? req.eqOrderSummary : []), ...(req.neOrderSummary instanceof Array ? req.neOrderSummary : [])].filter((v) => v).length ? { orderSummaryFilterRelation: req.orderSummaryFilterRelation === "OR" ? "OR" : "AND", includeOrderSummary: req.includeOrderSummary instanceof Array && req.includeOrderSummary.length ? req.includeOrderSummary.join(",") : void 0, excludeOrderSummary: req.excludeOrderSummary instanceof Array && req.excludeOrderSummary.length ? req.excludeOrderSummary.join(",") : void 0, eqOrderSummary: req.eqOrderSummary instanceof Array && req.eqOrderSummary.length ? req.eqOrderSummary.join(",") : void 0, neOrderSummary: req.neOrderSummary instanceof Array && req.neOrderSummary.length ? req.neOrderSummary.join(",") : void 0 } : {}),
          // 外部id
          ...([...(req.includeExternalId instanceof Array ? req.includeExternalId : []), ...(req.excludeExternalId instanceof Array ? req.excludeExternalId : []), ...(req.eqExternalId instanceof Array ? req.eqExternalId : []), ...(req.neExternalId instanceof Array ? req.neExternalId : [])].filter((v) => v).length ? { externalIdFilterRelation: req.externalIdFilterRelation === "OR" ? "OR" : "AND", includeExternalId: req.includeExternalId instanceof Array && req.includeExternalId.length ? req.includeExternalId.join(",") : void 0, excludeExternalId: req.excludeExternalId instanceof Array && req.excludeExternalId.length ? req.excludeExternalId.join(",") : void 0, eqExternalId: req.eqExternalId instanceof Array && req.eqExternalId.length ? req.eqExternalId.join(",") : void 0, neExternalId: req.neExternalId instanceof Array && req.neExternalId.length ? req.neExternalId.join(",") : void 0 } : {}),

          /* 状态 */
          ...([...(req.includeState instanceof Array ? req.includeState : []), ...(req.excludeState instanceof Array ? req.excludeState : []), ...(req.eqState instanceof Array ? req.eqState : []), ...(req.neState instanceof Array ? req.neState : [])].filter((v) => v).length ? { stateFilterRelation: req.stateFilterRelation === "OR" ? "OR" : "AND", includeStates: req.includeState instanceof Array && req.includeState.length ? req.includeState.join(",") : void 0, excludeStates: req.excludeState instanceof Array && req.excludeState.length ? req.excludeState.join(",") : void 0, eqStates: req.eqState instanceof Array && req.eqState.length ? req.eqState.join(",") : void 0, neStates: req.neState instanceof Array && req.neState.length ? req.neState.join(",") : void 0 } : {}),

          /* 告警数 */
          ...([...(req.eqAlarmCount instanceof Array ? req.eqAlarmCount : []), ...(req.neAlarmCount instanceof Array ? req.neAlarmCount : []), ...(req.geAlarmCount instanceof Array ? req.geAlarmCount : []), ...(req.gtAlarmCount instanceof Array ? req.gtAlarmCount : []), ...(req.leAlarmCount instanceof Array ? req.leAlarmCount : []), ...(req.ltAlarmCount instanceof Array ? req.ltAlarmCount : []), ...(req.isNullAlarmCount instanceof Array ? req.isNullAlarmCount : []), ...(req.isNotNullAlarmCount instanceof Array ? req.isNotNullAlarmCount : [])].filter((v) => v).length ? { alarmCountFilterRelation: req.alarmCountFilterRelation === "OR" ? "OR" : "AND", eqAlarmCount: req.eqAlarmCount instanceof Array && req.eqAlarmCount.length ? req.eqAlarmCount.join(",") : void 0, neAlarmCount: req.neAlarmCount instanceof Array && req.neAlarmCount.length ? req.neAlarmCount.join(",") : void 0, geAlarmCount: req.geAlarmCount instanceof Array && req.geAlarmCount.length ? req.geAlarmCount.join(",") : void 0, gtAlarmCount: req.gtAlarmCount instanceof Array && req.gtAlarmCount.length ? req.gtAlarmCount.join(",") : void 0, leAlarmCount: req.leAlarmCount instanceof Array && req.leAlarmCount.length ? req.leAlarmCount.join(",") : void 0, ltAlarmCount: req.ltAlarmCount instanceof Array && req.ltAlarmCount.length ? req.ltAlarmCount.join(",") : void 0, isNullAlarmCount: req.isNullAlarmCount instanceof Array && req.isNullAlarmCount.length ? req.isNullAlarmCount.join(",") : void 0, isNotNullAlarmCount: req.isNotNullAlarmCount instanceof Array && req.isNotNullAlarmCount.length ? req.isNotNullAlarmCount.join(",") : void 0 } : {}),

          /* 处理人 */
          ...([...(req.includeActorName instanceof Array ? req.includeActorName : []), ...(req.excludeActorName instanceof Array ? req.excludeActorName : []), ...(req.eqActorName instanceof Array ? req.eqActorName : []), ...(req.neActorName instanceof Array ? req.neActorName : [])].filter((v) => v).length ? { actorNameFilterRelation: req.actorNameFilterRelation === "OR" ? "OR" : "AND", includeActorName: req.includeActorName instanceof Array && req.includeActorName.length ? req.includeActorName.join(",") : void 0, excludeActorName: req.excludeActorName instanceof Array && req.excludeActorName.length ? req.excludeActorName.join(",") : void 0, eqActorName: req.eqActorName instanceof Array && req.eqActorName.length ? req.eqActorName.join(",") : void 0, neActorName: req.neActorName instanceof Array && req.neActorName.length ? req.neActorName.join(",") : void 0 } : {}),

          /* 负责人 */
          ...([...(req.includeResponsibleName instanceof Array ? req.includeResponsibleName : []), ...(req.excludeResponsibleName instanceof Array ? req.excludeResponsibleName : []), ...(req.eqResponsibleName instanceof Array ? req.eqResponsibleName : []), ...(req.neResponsibleName instanceof Array ? req.neResponsibleName : [])].filter((v) => v).length ? { responsibleNameFilterRelation: req.responsibleNameFilterRelation === "OR" ? "OR" : "AND", includeResponsibleName: req.includeResponsibleName instanceof Array && req.includeResponsibleName.length ? req.includeResponsibleName.join(",") : void 0, excludeResponsibleName: req.excludeResponsibleName instanceof Array && req.excludeResponsibleName.length ? req.excludeResponsibleName.join(",") : void 0, eqResponsibleName: req.eqResponsibleName instanceof Array && req.eqResponsibleName.length ? req.eqResponsibleName.join(",") : void 0, neResponsibleName: req.neResponsibleName instanceof Array && req.neResponsibleName.length ? req.neResponsibleName.join(",") : void 0 } : {}),

          /* 负责人AND处理人 */
          ...([...(req.eqCompactActorName instanceof Array ? req.eqCompactActorName : []), ...(req.neCompactActorName instanceof Array ? req.neCompactActorName : []), ...(req.includeCompactActorName instanceof Array ? req.includeCompactActorName : []), ...(req.excludeCompactActorName instanceof Array ? req.excludeCompactActorName : [])].filter((v) => v).length ? { compactActorNameFilterRelation: req.compactActorNameFilterRelation === "OR" ? "OR" : "AND", compactEqActorName: req.eqCompactActorName instanceof Array && req.eqCompactActorName.length ? req.eqCompactActorName.join(",") : void 0, compactNeActorName: req.neCompactActorName instanceof Array && req.neCompactActorName.length ? req.neCompactActorName.join(",") : void 0, compactIncludeActorName: req.includeCompactActorName instanceof Array && req.includeCompactActorName.length ? req.includeCompactActorName.join(",") : void 0, compactExcludeActorName: req.excludeCompactActorName instanceof Array && req.excludeCompactActorName.length ? req.excludeCompactActorName.join(",") : void 0 } : {}),

          ...([...(req.eqTicketGroupName instanceof Array ? req.eqTicketGroupName : []), ...(req.neTicketGroupName instanceof Array ? req.neTicketGroupName : []), ...(req.inTicketGroupName instanceof Array ? req.inTicketGroupName : []), ...(req.excludeTicketGroupName instanceof Array ? req.excludeTicketGroupName : [])].filter((v) => v).length ? { ticketGroupNameFilterRelation: req.ticketGroupNameFilterRelation === "OR" ? "OR" : "AND", eqTicketGroupName: req.eqTicketGroupName instanceof Array && req.eqTicketGroupName.length ? req.eqTicketGroupName.join(",") : void 0, neTicketGroupName: req.neTicketGroupName instanceof Array && req.neTicketGroupName.length ? req.neTicketGroupName.join(",") : void 0, inTicketGroupName: req.inTicketGroupName instanceof Array && req.inTicketGroupName.length ? req.inTicketGroupName.join(",") : void 0, excludeTicketGroupName: req.excludeTicketGroupName instanceof Array && req.excludeTicketGroupName.length ? req.excludeTicketGroupName.join(",") : void 0 } : {}),

          ...([...(req.eqUserGroupName instanceof Array ? req.eqUserGroupName : []), ...(req.neUserGroupName instanceof Array ? req.neUserGroupName : []), ...(req.inUserGroupName instanceof Array ? req.inUserGroupName : []), ...(req.excludeUserGroupName instanceof Array ? req.excludeUserGroupName : [])].filter((v) => v).length ? { userGroupNameFilterRelation: req.userGroupNameFilterRelation === "OR" ? "OR" : "AND", eqUserGroupName: req.eqUserGroupName instanceof Array && req.eqUserGroupName.length ? req.eqUserGroupName.join(",") : void 0, neUserGroupName: req.neUserGroupName instanceof Array && req.neUserGroupName.length ? req.neUserGroupName.join(",") : void 0, inUserGroupName: req.inUserGroupName instanceof Array && req.inUserGroupName.length ? req.inUserGroupName.join(",") : void 0, excludeUserGroupName: req.excludeUserGroupName instanceof Array && req.excludeUserGroupName.length ? req.excludeUserGroupName.join(",") : void 0 } : {}),

          ...([...(req.compactEqTicketName instanceof Array ? req.compactEqTicketName : []), ...(req.compactNeTicketName instanceof Array ? req.compactNeTicketName : []), ...(req.compactIncludeTicketName instanceof Array ? req.compactIncludeTicketName : []), ...(req.compactExcludeTicketName instanceof Array ? req.compactExcludeTicketName : [])].filter((v) => v).length ? { compactTicketNameFilterRelation: req.compactTicketNameFilterRelation === "OR" ? "OR" : "AND", compactEqTicketName: req.compactEqTicketName instanceof Array && req.compactEqTicketName.length ? req.compactEqTicketName.join(",") : void 0, compactNeTicketName: req.compactNeTicketName instanceof Array && req.compactNeTicketName.length ? req.compactNeTicketName.join(",") : void 0, compactIncludeTicketName: req.compactIncludeTicketName instanceof Array && req.compactIncludeTicketName.length ? req.compactIncludeTicketName.join(",") : void 0, compactExcludeTicketName: req.compactExcludeTicketName instanceof Array && req.compactExcludeTicketName.length ? req.compactExcludeTicketName.join(",") : void 0 } : {}),
          permissionId: req.permissionId || (await import("@/views/pages/permission")).智能事件中心_客户_工单可读,
        };

        bindParamByObj(
          Object.assign(
            {
              pageNumber: req.paging.pageNumber /* 页码, 默认第一页 */,
              pageSize: req.paging.pageSize /* 页大小, 默认10 */,
              sort: req.sort,
            },
            urlFlag === "all" ? query : {}
          ),
          $req.params
        );
        $req.data =
          urlFlag === "permission"
            ? Object.assign(query, {
                permissionList: [
                  /*  */
                  { permissionId: 智能事件中心_项目_工单可读, permissionType: "View Tickets When Project Has View Tickets" },
                  { permissionId: 智能事件中心_联系人_工单可读, permissionType: "View Tickets When Contact Has View Tickets" },
                  { permissionId: 智能事件中心_设备_工单可读, permissionType: "View Tickets When Device Has View Tickets" },
                ],
              })
            : void 0;
        return $req;
      })
      .then(($req) => request<never, Response<ChangeItem[]>>($req)),
    { controller }
  );
}

export function getChangeStateCount(data: {} & RequestBase) {
  return request<unknown, Response<{ state: changeState; count: number }[]>>({
    url: `${SERVER.EVENT_CENTER}/change/count_state/current`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

export async function getChangeDetailById(data: { id: string } & RequestBase) {
  const {
    /*  */
    智能事件中心_变更工单_可读,
    智能事件中心_变更工单_更新,
    智能事件中心_变更工单_编辑小记,
    智能事件中心_变更工单_分配设备,
    智能事件中心_变更工单_分配联系人,
    智能事件中心_变更工单_关联工单,
  } = await import("@/views/pages/permission");

  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/details`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id, permissionId: "612917524815675392", queryPermissionId: [智能事件中心_变更工单_可读].join(), verifyPermissionIds: [智能事件中心_变更工单_更新, 智能事件中心_变更工单_编辑小记, 智能事件中心_变更工单_分配设备, 智能事件中心_变更工单_分配联系人, 智能事件中心_变更工单_关联工单].join() }),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

/**
 * @name 修改优先级
 * @export
 * @param {({ id: string; priority: priority } & RequestBase)} data
 * @return {*}
 */
export function setChangePriority(data: { id: string; priority: priority } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/priority/${data.priority}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

/**
 * @name 修改重要性
 * @export
 * @param {({ id: string; importance: deviceImportance } & RequestBase)} data
 * @return {*}
 */
export function setChangeImportance(data: { id: string; importance: deviceImportance } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/importance/${data.importance}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

/**
 * @name 修改紧急性
 * @export
 * @param {({ id: string; severity: eventSeverity } & RequestBase)} data
 * @return {*}
 */
export function setChangeSeverity(data: { id: string; severity: eventSeverity } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/severity/${data.severity}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

export function setChangeDesc(data: { id: string; desc: string; externalId: string } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/desc`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["desc", "externalId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function setChangeTime(data: { id: string; startTime: string; endTime: string } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/time`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["startTime", "endTime"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export interface ChangeNote {
  noteId: string;
  eventId: string;
  noteContent: string;
  attachmentList: {
    attachmentId: string;
    attachmentName: string;
    attachmentUrl: string;
    attachmentKey: string;
  }[];
  noteCreateUserId: string;
  tenantId: string;
  noteCreateUserName: string;
  noteCreateUserProfilePicture: string;
  noteCreateTime: string;
}

export function getChangeNotes(data: { id: string } & RequestBase) {
  return request<unknown, Response<ChangeNote[]>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/note`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function addChangeNote(data: { eventId: string; content: string; attachmentList: { attachmentId: string; attachmentName: string; attachmentUrl: string; attachmentKey: string }[] } & RequestBase) {
  return request<unknown, Response<ChangeNote>>({
    url: `${SERVER.EVENT_CENTER}/change/note`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["eventId", "content", "attachmentList", "privateAble"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function setChangeNote(data: { eventId: string; noteId: string; content: string; attachmentList: { attachmentId: string; attachmentName: string; attachmentUrl: string; attachmentKey: string }[] } & RequestBase) {
  return request<unknown, Response<ChangeNote>>({
    url: `${SERVER.EVENT_CENTER}/change/note`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["eventId", "content", "attachmentList", "noteId", "privateAble"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function delChangeNote(data: { id: string; noteId: string } & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/note/${data.noteId}/612915797014085632`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function addChangeDevices(data: { id: string; deviceIds: string; autoAllocateContact: boolean } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/device/${data.deviceIds}/add/612817881175949312`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["autoAllocateContact"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function delChangeDevice(data: { id: string; deviceId: string } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/device/${data.deviceId}/remove/${data.permissionId || 智能事件中心_变更工单_分配设备}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function addChangeContact(data: { id: string; contactType: string; contactIds: string[] } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/contact/add/612859932017950720`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["contactType", "contactIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function delChangeContact(data: { id: string; contactType: string; contactIds: Array<[]> } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/contact/remove/612859932017950720`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["contactType", "contactIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function setChangeCollectAlertDisable(data: { id: string } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/collect_alert/disable`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { orderId: data.id }),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export interface ApproveItem {
  id: string;
  tenantId: string;
  orderId: string;
  teamId: string;
  teamName: string;
  approveState: string;
  hasAuthority: boolean;
  createdBy: string;
  updatedBy: string;
  createdTime: string;
  updatedTime: string;
}

export enum ApproveState {
  SUSPEND = "SUSPEND",
  REFUSED = "REFUSED",
  PASSED = "PASSED",
}

export const approveStateOption = [
  { value: ApproveState.SUSPEND, label: "未审批", type: "warning" },
  { value: ApproveState.REFUSED, label: "已拒绝", type: "danger" },
  { value: ApproveState.PASSED, label: "已通过", type: "success" },
];

export function getApproves(data: { id: string; pageNumber: number; pageSize: number } & RequestBase) {
  return request<unknown, Response<ApproveItem[]>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/approve`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["pageNumber", "pageSize"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { permissionId: "612917524815675392" }),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function addChangeApprove(data: { id: string; teamId: string } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/approve/${data.teamId}/${"612917524815675392"}`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function delChangeApprove(data: { id: string; approveId: string } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/approve/${data.approveId}/${"612917524815675392"}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function changeApproveRefuse(data: { id: string; approveId: string } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/approve/${data.approveId}/refuse/${"612917524815675392"}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function changeApprovePass(data: { id: string; approveId: string } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/approve/${data.approveId}/pass/${"612917524815675392"}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function changeStateDispose(data: { id: string } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/dispose`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

// 舍弃
export function changeStateWithdraw(data: { id: string; code: { name: string; desc: string }; content: string } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/withdraw`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["code", "content"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

// 舍弃
export function changeStateAbandon(data: { id: string; code: { name: string; desc: string }; content: string } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/abandon`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["code", "content"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

// 完成
export function changeStateComplete(data: { id: string; code: { name: string; desc: string }; content: string; closeAlert: boolean } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/complete`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { orderId: data.id }),
    data: ["code", "content", "closeAlert"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

// 关闭
export function changeStateClose(data: { id: string; code: { name: string; desc: string }; content: string } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/close`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["code", "content"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function changeUser(data: { id: string; userId: string } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/user/${data.userId}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function changeUserGroup(data: { id: string; teamId: string } & RequestBase) {
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/team/${data.teamId}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export interface DeviceAllocateRule {
  id: string;
  tenantId: string;
  orderId: string;
  regionId: string;
  locationId: string;
  deviceGroupId: string;
  deviceTypeId: string;
  autoAllocateContact: boolean;
  createdBy: string;
  updatedBy: string;
  createdTime: string;
  updatedTime: string;
}

export function deviceAllocateRule(data: { orderId: string } & RequestBase) {
  return request<unknown, Response<DeviceAllocateRule[]>>({
    url: `${SERVER.EVENT_CENTER}/device_allocate_rule/list`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["orderId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @description 添加设备字段分配规则
 * @url http://*************:3000/project/17/interface/api/663
 */
export function /* 添加设备字段分配规则 */ addDeviceAllocateRule(req: Partial<Record<"orderId" | "regionId" | "locationId" | "deviceGroupId" | "deviceTypeId" | "autoAllocateContact", string>> & RequestBase) {
  const params = {};
  const data = req;
  return request<never, Response<DeviceAllocateRule>>({ url: `${SERVER.EVENT_CENTER}/device_allocate_rule`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function /* 更新摘要信息 */ setChangeDigest(req: { id: string; digest: string } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { digest: req.digest /* 摘要信息 */ };
  return request<never, Response<Change>>({ url: `${SERVER.EVENT_CENTER}/change/${req.id /* 变更id */}/digest`, method: Method.Patch, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function delDeviceAllocateRule(data: { id: string } & RequestBase) {
  return request<unknown, Response<DeviceAllocateRule[]>>({
    url: `${SERVER.EVENT_CENTER}/device_allocate_rule/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function setChangeEditable(req: {} & RequestBase) {
  const params = { orderId: req.id };
  return request<unknown, Response<Change>>({ url: `${SERVER.EVENT_CENTER}/change/${req.id}/selective`, method: Method.Put, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data: Object.keys(req).reduce((p, key) => Object.assign(p, req[key] === undefined ? {} : { [key]: req[key] }), {}) });
}

export interface ChangeTabCount {
  noteCount: string /* 小记数量 */;
  approveCount: string /* 审批数量 */;
  deviceCount: string /* 设备数量 */;
  contactCount: string /* 联系人数量 */;
  relationCount: string /* 关联数量 */;
  fileCount: string /* 文件数量 */;
  unconfirmed: string /* 未确认告警 */;
  confirmed: string /* 已确认告警 */;
  actionCount: string /* 行动策略数量 */;
}
export function getChangeTabCount(req: { id: string } & RequestBase) {
  return request<never, Response<ChangeTabCount>>({ url: `${SERVER.EVENT_CENTER}/change/${req.id /* 变更id */}/count/tab`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params: { permissionId: "612917524815675392" }, data: {} });
}

export function getChangeContacts(data: { id: string } & RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/${data.id}/getContact`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: {},
  });
}
export function publishCreate(data: Partial<{ ticketTemplateId: string; changeId: string; publishType: string; startTime: string; endTime: string; digest: string; priority: string; desc: string }> & RequestBase) {
  return request<unknown, Response<Record<string, any>>>({
    url: `${SERVER.EVENT_CENTER}/publish/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
    data: ["changeId", "publishType", "startTime", "endTime", "digest", "priority", "desc", "ticketTemplateId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}
