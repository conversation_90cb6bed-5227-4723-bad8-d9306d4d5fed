import { SERVER, Method, type Response, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

// export interface clientResourceTypeItem {
//   id: string /* 主键 */;
// }
// // 查询政支租户列表
// export function getClientResourceTypeList(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
//   return request<never, Response<clientResourceTypeItem[]>>({
//     url: `${SERVER.IAM}/tenantContainer/list`,
//     method: Method.Get,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: {},
//   });
// }
// // 新增政支租户
// export function addClientResources(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
//   return request<never, Response<clientResourceTypeItem[]>>({
//     url: `${SERVER.IAM}/tenantContainer/create`,
//     method: Method.Post,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: data,
//   });
// }
// // 更新政支租户
// export function editClientResources(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
//   return request<never, Response<clientResourceTypeItem[]>>({
//     url: `${SERVER.IAM}/tenantContainer/update`,
//     method: Method.Post,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: data,
//   });
// }
// //分页查询政支设备列表
// export function getClientDeviceList(data: object & RequestBase) {
//   return request<never, Response<clientResourceTypeItem[]>>({
//     url: `${SERVER.CMDB}/political/deviceList`,
//     method: Method.Get,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: {},
//   });
// }
// // 新建政支设备
// export function createClientDevice(data: object & RequestBase) {
//   return request<never, Response<clientResourceTypeItem[]>>({
//     url: `${SERVER.CMDB}/political/createDevice`,
//     method: Method.Post,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: data,
//   });
// }
// //更新政支设备
// export function updateClientDevice(data: object & RequestBase) {
//   return request<never, Response<clientResourceTypeItem[]>>({
//     url: `${SERVER.CMDB}/political/updateDevice`,
//     method: Method.Post,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: data,
//   });
// }
// //获取最新设备安装地址
// export function getLatestDeviceAddress(data: object & RequestBase) {
//   return request<never, Response<clientResourceTypeItem[]>>({
//     url: `${SERVER.CMDB}/political/latestDeviceAddress`,
//     method: Method.Get,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: {},
//   });
// }
// //返回当前设备的信息，未脱敏，用于编辑展示
// export function getDetailWithoutDesensitize(data: { deviceId: string } & RequestBase) {
//   return request<never, Response<clientResourceTypeItem[]>>({
//     url: `${SERVER.CMDB}/political/${data.deviceId}/detailWithoutDesensitize`,
//     method: Method.Get,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: {},
//   });
// }
// //设备详情
// export function getClientDevice(data: { deviceId: string } & RequestBase) {
//   return request<never, Response<clientResourceTypeItem[]>>({
//     url: `${SERVER.CMDB}/political/${data.deviceId}/detail`,
//     method: Method.Get,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: {},
//   });
// }

/**
 * @description 政支租户
 */
export interface GovTenantItem {
  /** 客户ID */
  id: string;
  /** 客户名称 */
  tenantName: string;
  /** 客户标识 */
  tenantIdent: string;
}

/**
 * @description 政支租户列表
 * @url http://*************:3000/project/11/interface/api/27023
 * @description 所属客户列表
 * @url http://*************:3000/project/47/interface/api/27182
 */
export function getGovTenantList(req: {}) {
  const controller = new AbortController();
  return Object.assign(
    // Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/tenantContainer/list`, method: Method.Get, responseType: "json", signal: controller.signal })
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/political/customerList`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj(req, $req.params);
        return $req;
      })
      .then(($req) => request<never, Response<GovTenantItem[]>>($req)),
    { controller }
  );
}

/**
 * @description 创建政支租户，同时创建安全容器
 * @url http://*************:3000/project/11/interface/api/27017
 * @description 新增政支设备所属客户
 * @url http://*************:3000/project/47/interface/api/27176
 */
export function addGovTenantData(req: { tenantName: /** 租户名称 */ string; tenantIdent: /* 客户标识 */ string; containerId: /** 当前租户安全容器ID(父容器ID) */ string }) {
  const controller = new AbortController();
  return Object.assign(
    // Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/tenantContainer/create`, method: Method.Post, responseType: "json", signal: controller.signal })
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/political/createCustomer`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        $req.data = { tenantName: req.tenantName, tenantIdent: req.tenantIdent, containerId: req.containerId };
        return $req;
      })
      .then(($req) => request<never, Response<GovTenantItem>>($req)),
    { controller }
  );
}

/**
 * @description 更新政支租户
 * @url http://*************:3000/project/11/interface/api/27020
 * @description 编辑所属客户
 * @url http://*************:3000/project/47/interface/api/27179
 */
export function modGovTenantData(req: { tenantName: /** 租户名称 */ string; tenantIdent: /* 客户标识 */ string; containerId: /** 当前租户安全容器ID(父容器ID) */ string; id: /** 客户ID */ string }) {
  const controller = new AbortController();
  return Object.assign(
    // Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/tenantContainer/update`, method: Method.Post, responseType: "json", signal: controller.signal })
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/political/updateCustomer`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = { tenantName: req.tenantName, tenantIdent: req.tenantIdent, containerId: req.containerId, id: req.id };
        return $req;
      })
      .then(($req) => request<never, Response<GovTenantItem>>($req)),
    { controller }
  );
}

/*  */

/**
 * @description 设备列表
 */
export interface GovResourceItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 安全容器 */
  containerId: /* Integer */ string;
  /** 资源池id */
  resourcePoolId: /* Integer */ string;
  /** 管理区编码 */
  precinctCode?: string;
  /** 设备型号 */
  deviceModel?: string;
  /** 模型标识 */
  modelIdent: string;
  /** 所在区域ID */
  regionId: /* Integer */ string;
  /** 所在场所ID */
  locationId: /* Integer */ string;
  /** 资源类型ID列表 */
  typeIds: /* Integer */ string[];
  /** 资源关联设备组ID列表 */
  groupIds: /* Integer */ string[];
  /** 服务商ID列表 */
  vendorIds: /* Integer */ string[];
  /** 行动策略列表 */
  supportNoteIds: /* Integer */ string[];
  /** 告警分类ID列表 */
  alertClassificationIds: /* Integer */ string[];
  /** 资产编号 */
  assetNumber?: string;
  /** 外部ID */
  externalId?: string;
  /** 资源名称 */
  name: string;
  /** 别名 */
  alias?: string;
  /** 资源上线时间 */
  resourceOnlineTime?: /* Integer */ string;
  /** 资源下线时间 */
  resourceOfflineTime?: /* Integer */ string;
  /** 是否交付 */
  delivery: boolean;
  /** 监控源 */
  monitorSources: /* 枚举: NIGHTINGALE_V6 :夜莺V6 | NETCARE_V6 :NetCareV6 | OTHER :未知 */ ("NIGHTINGALE_V6" | "NETCARE_V6" | "OTHER")[];
  /** 业务单位 */
  unit?: string;
  /** 备注|描述信息 */
  description?: string;
  /** 时区 */
  timeZone?: string;
  /** 资源重要性 */
  importance: /* 枚举: High :至关重要的 | Medium :中 | Low :低 | None :无 | Unknown :未知的 */ "High" | "Medium" | "Low" | "None" | "Unknown";
  /** 标签列表 */
  tags: string[];
  /** 资源配置信息 */
  config: {
    key?: string;
  };
  /** 是否激活 */
  active: boolean;
  /** 最后上线时间 */
  onlineTime: /* Integer */ string;
  /** 最后离线时间 */
  offlineTime: /* Integer */ string;
  /** 服务包 */
  servicePackage?: string;
  /** 协议名 */
  protocolName?: string;
  /** 逻辑删除字段 */
  deleted: boolean;
  /** 用于解决唯一索引与逻辑删除冲突的字段 没有删除的都是0，删除后改为id的值 */
  deletedUniqueKey: /* Integer */ string;
  /** 变更版本号 */
  version: /* Integer */ string;
  /** 录入时间 */
  createdTime: /* Integer */ string;
  /** 最近变更时间 */
  updatedTime: /* Integer */ string;
  /** 录入人 */
  createdBy?: string;
  /** 最近变更人 */
  updatedBy?: string;
  /** SN序列号 */
  sn?: string;
  /** 链路编号 */
  linkNumber?: string;
  /** 所属客户id */
  customerId: /* Integer */ string;
  /** 所属客户名称 */
  customerName?: string;
  /** 所属客户标识 */
  customerIdent?: string;
  /** 安装地址（省） */
  province?: string;
  /** 安装地址（市） */
  city?: string;
  /** 安装地址（区域） */
  district?: string;
  /** 安装地址（详细地址） */
  installAddress?: string;
  /** 安装人姓名 */
  installerName?: string;
  /** 安装人电话 */
  installerPhone?: string;
  /** 设备联系人 */
  contactPerson?: string;
  /** 联系人电话 */
  contactPhone?: string;
  /** 厂商名称 */
  vendorName?: string;

  mac?: string;

  verifyPermissionIds: string[];
}
/**
 * @description 分页查询设备列表
 * @url http://*************:3000/project/47/interface/api/27038
 */
export function getGovResourceList(req: { paging: { pageNumber: number; pageSize: number }; customerId: /* 所属客户ID */ string | null } & Record<string, any>) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/political/deviceList`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, customerId: req.customerId }, $req.params);
        try {
          const [{ default: getUserInfo }, { 资产管理中心_客户设备By政支_可读, 资产管理中心_客户设备By政支_新建设备, 资产管理中心_客户设备By政支_编辑设备, 资产管理中心_客户设备By政支_安全 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [资产管理中心_客户设备By政支_可读].join(","), verifyPermissionIds: [资产管理中心_客户设备By政支_新建设备, 资产管理中心_客户设备By政支_编辑设备, 资产管理中心_客户设备By政支_安全].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }

        if (!req.fullQuery) {
          bindParamByObj(
            {
              ...([...(req.includeName instanceof Array ? req.includeName : []), ...(req.excludeName instanceof Array ? req.excludeName : []), ...(req.eqName instanceof Array ? req.eqName : []), ...(req.neName instanceof Array ? req.neName : [])].filter((v) => v).length ? { nameFilterRelation: req.nameFilterRelation === "OR" ? "OR" : "AND", includeName: req.includeName instanceof Array && req.includeName.length ? req.includeName.join(",") : void 0, excludeName: req.excludeName instanceof Array && req.excludeName.length ? req.excludeName.join(",") : void 0, eqName: req.eqName instanceof Array && req.eqName.length ? req.eqName.join(",") : void 0, neName: req.neName instanceof Array && req.neName.length ? req.neName.join(",") : void 0 } : {}),

              ...([...(req.includeAddress instanceof Array ? req.includeAddress : []), ...(req.excludeAddress instanceof Array ? req.excludeAddress : []), ...(req.eqAddress instanceof Array ? req.eqAddress : []), ...(req.neAddress instanceof Array ? req.neAddress : [])].filter((v) => v).length ? { addressFilterRelation: req.addressFilterRelation === "OR" ? "OR" : "AND", includeAddress: req.includeAddress instanceof Array && req.includeAddress.length ? req.includeAddress.join(",") : void 0, excludeAddress: req.excludeAddress instanceof Array && req.excludeAddress.length ? req.excludeAddress.join(",") : void 0, eqAddress: req.eqAddress instanceof Array && req.eqAddress.length ? req.eqAddress.join(",") : void 0, neAddress: req.neAddress instanceof Array && req.neAddress.length ? req.neAddress.join(",") : void 0 } : {}),

              ...([...(req.includeBasicInfo instanceof Array ? req.includeBasicInfo : []), ...(req.excludeBasicInfo instanceof Array ? req.excludeBasicInfo : []), ...(req.eqBasicInfo instanceof Array ? req.eqBasicInfo : []), ...(req.neBasicInfo instanceof Array ? req.neBasicInfo : [])].filter((v) => v).length ? { basicInfoFilterRelation: req.basicInfoFilterRelation === "OR" ? "OR" : "AND", includeBasicInfo: req.includeBasicInfo instanceof Array && req.includeBasicInfo.length ? req.includeBasicInfo.join(",") : void 0, excludeBasicInfo: req.excludeBasicInfo instanceof Array && req.excludeBasicInfo.length ? req.excludeBasicInfo.join(",") : void 0, eqBasicInfo: req.eqBasicInfo instanceof Array && req.eqBasicInfo.length ? req.eqBasicInfo.join(",") : void 0, neBasicInfo: req.neBasicInfo instanceof Array && req.neBasicInfo.length ? req.neBasicInfo.join(",") : void 0 } : {}),

              ...([...(req.includeContact instanceof Array ? req.includeContact : []), ...(req.excludeContact instanceof Array ? req.excludeContact : []), ...(req.eqContact instanceof Array ? req.eqContact : []), ...(req.neContact instanceof Array ? req.neContact : [])].filter((v) => v).length ? { contactFilterRelation: req.contactFilterRelation === "OR" ? "OR" : "AND", includeContact: req.includeContact instanceof Array && req.includeContact.length ? req.includeContact.join(",") : void 0, excludeContact: req.excludeContact instanceof Array && req.excludeContact.length ? req.excludeContact.join(",") : void 0, eqContact: req.eqContact instanceof Array && req.eqContact.length ? req.eqContact.join(",") : void 0, neContact: req.neContact instanceof Array && req.neContact.length ? req.neContact.join(",") : void 0 } : {}),
            },
            $req.params
          );
        } else {
          bindParamByObj({ fullQuery: req.fullQuery }, $req.params);
        }

        bindParamByObj({ active: req.active || void 0 }, $req.params);

        return $req;
      })
      .then(($req) => request<never, Response<GovResourceItem[]>>($req)),
    { controller }
  );
}

/**
 * @description exportReport
 * @url http://*************:3000/project/17/interface/api/37634
 */
export function getGovResourceExport(req: {} & Record<string, any>) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.REPORT_CMDB}/political/resources/export`, method: Method.Get, responseType: "blob", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        // bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, customerId: req.customerId }, $req.params);
        try {
          const [{ default: getUserInfo }, { 资产管理中心_客户设备By政支_可读, 资产管理中心_客户设备By政支_新建设备, 资产管理中心_客户设备By政支_编辑设备 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [资产管理中心_客户设备By政支_可读].join(","), verifyPermissionIds: [资产管理中心_客户设备By政支_新建设备, 资产管理中心_客户设备By政支_编辑设备].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }

        if (!req.fullQuery) {
          bindParamByObj(
            {
              ...([...(req.includeName instanceof Array ? req.includeName : []), ...(req.excludeName instanceof Array ? req.excludeName : []), ...(req.eqName instanceof Array ? req.eqName : []), ...(req.neName instanceof Array ? req.neName : [])].filter((v) => v).length ? { nameFilterRelation: req.nameFilterRelation === "OR" ? "OR" : "AND", includeName: req.includeName instanceof Array && req.includeName.length ? req.includeName.join(",") : void 0, excludeName: req.excludeName instanceof Array && req.excludeName.length ? req.excludeName.join(",") : void 0, eqName: req.eqName instanceof Array && req.eqName.length ? req.eqName.join(",") : void 0, neName: req.neName instanceof Array && req.neName.length ? req.neName.join(",") : void 0 } : {}),

              ...([...(req.includeAddress instanceof Array ? req.includeAddress : []), ...(req.excludeAddress instanceof Array ? req.excludeAddress : []), ...(req.eqAddress instanceof Array ? req.eqAddress : []), ...(req.neAddress instanceof Array ? req.neAddress : [])].filter((v) => v).length ? { addressFilterRelation: req.addressFilterRelation === "OR" ? "OR" : "AND", includeAddress: req.includeAddress instanceof Array && req.includeAddress.length ? req.includeAddress.join(",") : void 0, excludeAddress: req.excludeAddress instanceof Array && req.excludeAddress.length ? req.excludeAddress.join(",") : void 0, eqAddress: req.eqAddress instanceof Array && req.eqAddress.length ? req.eqAddress.join(",") : void 0, neAddress: req.neAddress instanceof Array && req.neAddress.length ? req.neAddress.join(",") : void 0 } : {}),

              ...([...(req.includeBasicInfo instanceof Array ? req.includeBasicInfo : []), ...(req.excludeBasicInfo instanceof Array ? req.excludeBasicInfo : []), ...(req.eqBasicInfo instanceof Array ? req.eqBasicInfo : []), ...(req.neBasicInfo instanceof Array ? req.neBasicInfo : [])].filter((v) => v).length ? { basicInfoFilterRelation: req.basicInfoFilterRelation === "OR" ? "OR" : "AND", includeBasicInfo: req.includeBasicInfo instanceof Array && req.includeBasicInfo.length ? req.includeBasicInfo.join(",") : void 0, excludeBasicInfo: req.excludeBasicInfo instanceof Array && req.excludeBasicInfo.length ? req.excludeBasicInfo.join(",") : void 0, eqBasicInfo: req.eqBasicInfo instanceof Array && req.eqBasicInfo.length ? req.eqBasicInfo.join(",") : void 0, neBasicInfo: req.neBasicInfo instanceof Array && req.neBasicInfo.length ? req.neBasicInfo.join(",") : void 0 } : {}),

              ...([...(req.includeContact instanceof Array ? req.includeContact : []), ...(req.excludeContact instanceof Array ? req.excludeContact : []), ...(req.eqContact instanceof Array ? req.eqContact : []), ...(req.neContact instanceof Array ? req.neContact : [])].filter((v) => v).length ? { contactFilterRelation: req.contactFilterRelation === "OR" ? "OR" : "AND", includeContact: req.includeContact instanceof Array && req.includeContact.length ? req.includeContact.join(",") : void 0, excludeContact: req.excludeContact instanceof Array && req.excludeContact.length ? req.excludeContact.join(",") : void 0, eqContact: req.eqContact instanceof Array && req.eqContact.length ? req.eqContact.join(",") : void 0, neContact: req.neContact instanceof Array && req.neContact.length ? req.neContact.join(",") : void 0 } : {}),
            },
            $req.params
          );
        } else {
          bindParamByObj({ fullQuery: req.fullQuery }, $req.params);
        }

        bindParamByObj({ active: req.active || void 0 }, $req.params);

        return $req;
      })
      .then(($req) => request<never, Response<Blob>>($req)),
    { controller }
  );
}

/**
 * @description 设备详情
 * @url http://*************:3000/project/47/interface/api/27041
 */
export function getGovResourceData(req: { deviceId: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/political/${req.deviceId}/detail`, method: Method.Get, responseType: "json", signal: controller.signal }).then(($req) => request<never, Response<GovResourceItem>>($req)),
    { controller }
  );
}
/**
 * @description 返回当前设备的信息，未脱敏，用于编辑展示
 * @url http://*************:3000/project/47/interface/api/27065
 */
export function catGovResourceData(req: { deviceId: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/political/${req.deviceId}/detailWithoutDesensitize`, method: Method.Get, responseType: "json", signal: controller.signal }).then(($req) => request<never, Response<GovResourceItem>>($req)),
    { controller }
  );
}
/**
 * @description 新建设备
 * @url http://*************:3000/project/47/interface/api/27032
 */
export function addGovResourceData(req: { userId?: /** 当前用户ID(用来获取当前安装人的姓名和手机号) */ string; customerId: /** 所属客户ID */ string; containerId: /** 安全容器id */ string; deviceName: /** 设备名称 */ string; modelIdent: /** 模型标识 */ string; vendorId: /** 设备厂商ID */ string; sn: /** SN序列号 */ string; deviceModel: /** 设备型号 */ string; linkNumber: /** 链路编号 */ string; province: /** 安装地址（省） */ string; city: /** 安装地址（市） */ string; district: /** 安装地址（区域） */ string; installAddress: /** 安装地址（详细地址） */ string; installerName: /** 安装人姓名 */ string; installerPhone: /** 安装人电话 */ string; contactPerson: /** 设备联系人 */ string; contactPhone: /** 联系人电话 */ string; desc: /** 描述 */ string; active: string; mac: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/political/createDevice`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = { userId: req.userId || null, customerId: req.customerId, containerId: req.containerId, deviceName: req.deviceName, modelIdent: req.modelIdent, vendorId: req.vendorId, sn: req.sn, deviceModel: req.deviceModel, linkNumber: req.linkNumber, province: req.province, city: req.city, district: req.district, installAddress: req.installAddress, installerName: req.installerName, installerPhone: req.installerPhone, contactPerson: req.contactPerson, contactPhone: req.contactPhone, desc: req.desc, active: req.active, mac: req.mac };
        return $req;
      })
      .then(($req) => request<never, Response<GovResourceItem>>($req)),
    { controller }
  );
}
/**
 * @description 更新设备
 * @url http://*************:3000/project/47/interface/api/27035
 */
export function modGovResourceData(req: { userId?: /** 当前用户ID(用来获取当前安装人的姓名和手机号) */ string; containerId: /** 安全容器id */ string; deviceName: /** 设备名称 */ string; modelIdent: /** 模型标识 */ string; vendorId: /** 设备厂商ID */ string; sn: /** SN序列号 */ string; deviceModel: /** 设备型号 */ string; linkNumber: /** 链路编号 */ string; province: /** 安装地址（省） */ string; city: /** 安装地址（市） */ string; district: /** 安装地址（区域） */ string; installAddress: /** 安装地址（详细地址） */ string; installerName: /** 安装人姓名 */ string; installerPhone: /** 安装人电话 */ string; contactPerson: /** 设备联系人 */ string; contactPhone: /** 联系人电话 */ string; desc: /** 描述 */ string; id: /** 设备ID */ /* Integer */ string; active: string; mac: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/political/updateDevice`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = { id: req.id, userId: req.userId || null, containerId: req.containerId, deviceName: req.deviceName, modelIdent: req.modelIdent, vendorId: req.vendorId, sn: req.sn, deviceModel: req.deviceModel, linkNumber: req.linkNumber, province: req.province, city: req.city, district: req.district, installAddress: req.installAddress, installerName: req.installerName, installerPhone: req.installerPhone, contactPerson: req.contactPerson, contactPhone: req.contactPhone, desc: req.desc, active: req.active, mac: req.mac };
        return $req;
      })
      .then(($req) => request<never, Response<GovResourceItem>>($req)),
    { controller }
  );
}

/**
 * @description 获取最新设备的安装地址-响应体
 * @url http://*************:3000/project/47/interface/api/27044
 */
export interface PoliticalLatestDeviceAddressResData {
  /** 安装地址（省） */
  province: string;
  /** 安装地址（市） */
  city: string;
  /** 安装地址（区域） */
  district: string;
  /** 安装地址（详细地址） */
  installAddress: string;
}
/**
 * @description 获取最新设备的安装地址
 * @url http://*************:3000/project/47/interface/api/27044
 */
export function getInstallAddress(req: {}) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/political/latestDeviceAddress`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj(req, $req.params);
        return $req;
      })
      .then(($req) => request<never, Response<PoliticalLatestDeviceAddressResData>>($req)),
    { controller }
  );
}

export interface DeviceInfo {
  /** 设备id */
  id: /* Integer */ string;
  /** 设备序列号 */
  serialNum?: string;
  /** 设备名称 */
  name?: string;
  /** 设备状态 1：在线  0：离线 -1：从未在线 */
  status: /* Integer */ string;
  mac?: string;
  /** 设备vlan1地址 */
  manageIp?: string;
  /** 软件版本 */
  softVersion?: string;
  /** 软件小版本号 */
  versionNumber?: string;
  /** 设备型号 */
  model?: string;
  /** 所属项目id */
  groupId: /* Integer */ string;
  /** 创建时间 */
  createDate?: string;
  /** 上次主动上报时间 */
  updateDate?: string;
  /** 与云管连接类型，默认1 */
  commType: /* Integer */ string;
  /** 设备运行时间（开始时间点的毫秒时间戳） */
  uptime?: /* Integer */ string;
  /** 设备上已经创建的vlan id字符串 */
  standby3?: string;
  /** 设备上已经创建的vlan id数组 */
  vlanIdList?: /* Integer */ string[];
  mqtt?: boolean;
  mqttLinux?: boolean;
}

export interface SionPortInfo {
  /** "id": 11088,					//端口id */
  id: /* Integer */ string;
  serialNum?: string;
  /** 端口状态0: UP  1: DOWN   2:LINKDOWN（启用但未插线） */
  status: /* Integer */ string;
  mac?: string;
  manageIp?: string;
  exitIp?: string;
  softVersion?: string;
  versionNumber?: string;
  /** // 端口在设备上的index */
  ifIndex: /* Integer */ string;
  /** 端口描述（不变） */
  portDesc?: string;
  /** 速率类型 0-10M 1-100M 2-1000M 3-10000M(10G) 4-21000M(21G) 5-40G 6-100G */
  speedType: /* Integer */ string;
  /** 当前速率  1: auto，2: 10M,3: 100M,4: 1000M, 5: 10000M  6: 21000M, 7: 40000M */
  speed: /* Integer */ string;
  /** 端口物理类型 1-copper 2-fiber 3-combo 4-other 	5-poe(电口供电) */
  phyIfType: /* Integer */ string;
  /** 双工模式 1: auto，2: full，3: half */
  duplex: /* Integer */ string;
  flowControl: /* Integer */ string;
  /** 机框号 */
  frame: /* Integer */ string;
  /** 槽位号 */
  slotNum: /* Integer */ string;
  /** 标签编号 */
  labelNum?: string;
  /** combo口工作状态",0: 无，1: 电口在工作，2: 光口在工作 */
  current?: string;
  /** 环路检测状态 0: 关闭, 1: 开启, 2: eaps环端口 */
  keepaliveStatus: /* Integer */ string;
  pvid: /* Integer */ string;
  /** 0: access 1: trunk */
  vlanMode: /* Integer */ string;
  mqtt?: boolean;
  mqttLinux?: boolean;
}

export enum PortState {
  UP = "UP",
  DOWN = "DOWN",
  LINKDOWN = "LINKDOWN",
}

export const speedTypeOption = [
  { label: "10M", value: "0" },
  { label: "100M", value: "1" },
  { label: "1000M", value: "2" },
  { label: "10000(10G)", value: "3" },
  { label: "21000M(21G)", value: "4" },
  { label: "40G", value: "5" },
  { label: "100G", value: "6" },
];

export const speedOption = [
  { label: "auto", value: "1" },
  { label: "10M", value: "2" },
  { label: "100M", value: "3" },
  { label: "1000M", value: "4" },
  { label: "10000M", value: "5" },
  { label: "21000M", value: "6" },
  { label: "40000M", value: "7" },
];

export const duplexOption = [
  { label: "auto", value: "1" },
  { label: "full", value: "2" },
  { label: "half", value: "3" },
];
//
export const keepaliveStatusOption = [
  { label: "关闭", value: "0" },
  { label: "开启", value: "1" },
  { label: "eaps环端口", value: "2" },
];

export const vlanModeOption = [
  { label: "access", value: "0" },
  { label: "trunk", value: "1" },
];
export function getDevicePort(req: {} & Record<string, any>) {
  return request<
    never,
    Response<{
      deviceInfo: DeviceInfo;
      sionPortInfo: SionPortInfo[];
    }>
  >({
    url: `${SERVER.CMDB}/political/sionDeviceInfo`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params: { mac: `${req.mac}` },
    data: {},
  });
}

export function setSionPortStatus(req: {} & Record<string, any>) {
  return request<never, Response<null>>({
    url: `${SERVER.CMDB}/political/setSionPortStatus`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params: req,
    data: {},
  });
}

export function addSionVlan(req: {} & Record<string, any>) {
  return request<never, Response<null>>({
    url: `${SERVER.CMDB}/political/operDevVlan`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params: {},
    data: req,
  });
}

export function delSionVlan(req: {} & Record<string, any>) {
  return request<never, Response<null>>({
    url: `${SERVER.CMDB}/political/deleteSionVlan`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params: req,
    data: {},
  });
}

export function setSionVlanPort(req: {} & Record<string, any>) {
  return request<never, Response<null>>({
    url: `${SERVER.CMDB}/political/operPortVlan`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params: {},
    data: req,
  });
}

export function setSionPortDesc(req: {} & Record<string, any>) {
  return request<never, Response<null>>({
    url: `${SERVER.CMDB}/political/modifyDeviceName`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params: req,
    data: {},
  });
}

export function restartDevice(req: {} & Record<string, any>) {
  return request<never, Response<null>>({
    url: `${SERVER.CMDB}/political/rebootSwitchById`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params: req,
    data: {},
  });
}

export function remoteSshAc(req: {} & Record<string, any>) {
  return request<never, Response<null>>({
    url: `${SERVER.CMDB}/political/oper/sshac`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params: req,
    data: {},
  });
}

export function backupDevices(req: {} & Record<string, any>) {
  return request<never, Response<null>>({
    url: `${SERVER.CMDB}/political/backupDevices`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params: req,
    data: {},
  });
}

export function uploadSwitchConfig(req: {} & Record<string, any>, formData) {
  return request<never, Response<null>>({
    url: `${SERVER.CMDB}/political/uploadSwitchConfig`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params: req,
    data: formData,
  });
}
