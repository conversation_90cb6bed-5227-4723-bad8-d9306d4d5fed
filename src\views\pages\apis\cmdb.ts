/* eslint-disable @typescript-eslint/no-unused-vars */
import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";

export enum monitorSource {
  ZIBBX = "ZIBBX",
  ARTHAS = "ARTHAS",
  UNKNOWN = "UNKNOWN",
}

export const monitorSourceOption: { label: string; value: keyof typeof monitorSource }[] = [
  { label: "ZIBBX", value: "ZIBBX" },
  { label: "ARTHAS", value: "ARTHAS" },
  { label: "UNKNOWN", value: "UNKNOWN" },
];
export enum importance {
  High = "High",
  Medium = "Medium",
  Low = "Low",
  None = "None",
  Unknown = "Unknown",
}

export const importanceOption: { label: string; value: keyof typeof importance }[] = [
  { label: "至关重要", value: "High" },
  { label: "中", value: "Medium" },
  { label: "低", value: "Low" },
  { label: "无", value: "None" },
  { label: "未知", value: "Unknown" },
];
interface Resources {
  id: string /* 主键 */;
  tenantId: string /* 租户ID */;
  externalId: string /* 外部ID（仅展示） */;
  assetNumber: string /* 资产编号（仅展示） */;
  name: string /* 资源名称 */;
  unit: string /* 业务单位 */;
  description: string /* 描述信息 */;
  timeZone: string /* 时区 */;
  importance: importance /* 资源重要性 */;
  tags: string[] /* 标签列表 */;
  config: Record<string, string> /* 资源配置信息 */;
  active: boolean /* 是否激活 */;

  version: string /* 变更版本号 */;
  createdTime: string /* 录入时间 */;
  updatedTime: string /* 最近变更时间 */;
  createdBy: string /* 录入人 */;
  updatedBy: string /* 最近变更人 */;
}

interface ResourceTypes {
  id: string /* 主键 */;
  name: string /* 类型名称 */;
  description: string /* 描述 */;
  alertClassificationIds: string[] /* 告警分类ID列表 */;
  version: string /* 版本号 */;
  createdTime: string /* 创建时间 */;
  updatedTime: string /* 最近更新时间 */;
  createdBy: string /* 创建人 */;
  updatedBy: string /* 最后更新人 */;
}
interface Vendors {
  id: string /* 主键 */;
  name: string /* 名称 */;
  description: string /* 描述 */;
  landlinePhone: string /* 固定电话 */;
  supportPhone: string /* 支持电话 */;
  contactName: string /* 联系人姓名 */;
  email: string /* 电子邮箱 */;
  version: string /* 版本号 */;
  createdTime: string /* 创建时间 */;
  updatedTime: string /* 最近更新时间 */;
  createdBy: string /* 创建人 */;
  updatedBy: string /* 最后更新人 */;
}
interface AlertClassifications {
  id: string /* 主键 */;
  name: string /* 分类名称 */;
  description: string /* 描述信息 */;
  version: string /* 版本号 */;
  createdTime: string /* 创建时间 */;
  updatedTime: string /* 最近更新时间 */;
  createdBy: string /* 创建人 */;
  updatedBy: string /* 最后更新人 */;
}
interface Groups {
  id: string /* 主键 */;
  tenantId: string /* 租户ID */;
  name: string /* 名称 */;
  description: string /* 描述信息 */;
  report: boolean /* 是否为报告分组 */;
  alertClassificationIds: string[] /* 告警分类ID列表 */;
  version: string /* 版本号 */;
  createdTime: string /* 创建时间 */;
  updatedTime: string /* 最近更新时间 */;
  createdBy: string /* 创建人 */;
  updatedBy: string /* 最后更新人 */;
}
interface ServiceNumbers {
  id: string /* 主键 */;
  resourceId: string /* 资源ID */;
  number: string /* 服务编号 */;
  vendorIds: string[] /* 供应商列表 */;
  type: string /* 类型 */;
  progress: string /* 进度 */;
  product: string /* 产品 */;
  description: string /* 描述信息 */;
}

export interface ResourcesItem extends Resources {
  modelIdent: string /* 模型标识 */;
  supportNoteIds: string[] /* 行动策略列表 */;

  regionId: string /* 所在区域ID */;
  regionDesc: string /* 所在区域名称 */;

  locationId: string /* 所在场所ID */;
  locationDesc: string /* 场所名称 */;

  typeIds: ResourceTypes["id"][] /* 资源类型ID列表 */;
  resourceTypes: ResourceTypes[] /* 资源类型描述 */;
  vendorIds: Vendors["id"][] /* 服务商ID列表 */;
  vendors: Vendors[] /* 服务商名称列表 */;
  alertClassificationIds: AlertClassifications["id"][] /* 告警分类ID列表 */;
  alertClassifications: AlertClassifications[] /* 告警分类列表 */;
  groupIds: Groups["id"][] /* 资源关联设备组ID列表 */;
  groups: Groups[] /* 设备分组列表 */;

  monitorSource: monitorSource /* 监控源 */;

  serviceNumbers: ServiceNumbers[] /* 服务编号列表 */;
}

export function getResourcesList /* 获取模块 */(data: { ids: string[] } & RequestBase) {
  const params = new URLSearchParams({ pageNumber: String(data.paging?.pageNumber || 1), pageSize: String(data.paging?.pageSize || data.ids.length) });
  (data.sort instanceof Array ? data.sort : []).forEach((v) => params.append("sort", v));
  bindSearchParams({ tenantId: data.tenantId, serviceRequestId: data.serviceRequestId, orderId: data.orderId, deviceId: data.deviceId, alertCreateTimeRange: data.alertCreateTimeRange, eventSeverity: data.eventSeverity, alert: data.alert, title: data.title, desc: data.desc, alarmBoardConfirmedTime: data.alarmBoardConfirmedTime, alarmBoardConfirmedUsername: data.alarmBoardConfirmedUsername }, params);

  return request<unknown, Response<ResourcesItem[]>>({
    url: `${SERVER.CMDB}/resources/${data.ids.join(",")}/batch/detail/desensitized`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function addResourcesData /* 添加模块 */(data: Partial<ResourcesItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<ResourcesItem[]>);
  // return request<unknown, Response<ResourcesItem[]>>({
  //   url: `${SERVER.EVENT_CENTER}/module`,
  //   method: Method.Post,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  // });
}
export function setResourcesData /* 更新模块 */(data: Partial<ResourcesItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<ResourcesItem[]>);
  // return request<unknown, Response<ResourcesItem[]>>({
  //   url: `${SERVER.EVENT_CENTER}/module/${data.id}`,
  //   method: Method.Put,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  // });
}
export function modResourcesData /* 修改模块 */(data: Partial<ResourcesItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<ResourcesItem[]>);
  // return request<unknown, Response<ResourcesItem[]>>({
  //   url: `${SERVER.EVENT_CENTER}/module/${data.id}`,
  //   method: Method.Patch,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  // });
}
export function delResourcesData /* 删除模块 */(data: Partial<ResourcesItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<ResourcesItem[]>);
  // return request<unknown, Response<ResourcesItem[]>>({
  //   url: `${SERVER.EVENT_CENTER}/module/${data.id}`,
  //   method: Method.Delete,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  // });
}
