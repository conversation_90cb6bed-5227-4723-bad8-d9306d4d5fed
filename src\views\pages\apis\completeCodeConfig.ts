import { SERVER, Method, bindSearchParams, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";
import getUserInfo from "@/utils/getUserInfo";

// export function codeConfigCreate(
//   data: {
//     parentId?: string | number;
//     codeName: string;
//     codeDesc: string;
//   } & RequestBase
// ) {
//   return request<unknown, unknown>({
//     url: `${SERVER.EVENT_CENTER}/codeConfig/create`,
//     method: Method.Post,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: data,
//   });
// }

// export function codeConfigUpdate(
//   data: {
//     id: string | number;
//     parentId?: string | number;
//     codeName: string;
//     codeDesc: string;
//   } & RequestBase
// ) {
//   return request<unknown, unknown>({
//     url: `${SERVER.EVENT_CENTER}/codeConfig/update`,
//     method: Method.Post,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: data,
//   });
// }

export enum codeConfigType {
  EVENT = "EVENT",
  SERVICE = "SERVICE",
  QUESTION = "QUESTION",
  CHANGE = "CHANGE",
  PUBLISH = "PUBLISH",
}

export const codeConfigTypeOption: {
  label: string;
  value: keyof typeof codeConfigType;
  color?: string;
}[] = [
  { label: "事件", value: codeConfigType.EVENT, color: "#2CB6F4" },
  { label: "服务请求", value: codeConfigType.SERVICE, color: "#FF7D00" },
  { label: "问题", value: codeConfigType.QUESTION, color: "#ED4013" },
  { label: "变更", value: codeConfigType.CHANGE, color: "#ED4013" },
];
export interface CodeConfigItem {
  id: string /* ID */;
  parentId?: string /* 上级节点ID */;
  codeName: string /* 完成代码名称 */;
  codeDesc: string /* 完成代码描述 */;
  status: boolean /* 状态 */;
  codeConfigType: codeConfigType /* 完成代码类型 EVENT :事件类型 SERVICE :服务请求类型 QUESTION :问题 CHANGE :变更 */;
  children: CodeConfigItem[];
  createTime: string /* 创建时间 */;
}

export function getCodeConfigList(
  req: {
    codeName?: string;
    codeType?: CodeConfigItem["codeConfigType"];
  } & RequestBase
) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = {
    codeName: req.codeName,
    codeType: req.codeType,
    paging: req.paging,
  };

  return request<never, Response<CodeConfigItem[]>>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/list`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    params,
    data,
  });
}
export function addCodeConfigData(
  req: Partial<{
    parentId: string;
    codeName: string;
    codeDesc: string;
    codeType: CodeConfigItem["codeConfigType"];
  }> &
    RequestBase
) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = {
    parentId: req.parentId || null,
    codeName: req.codeName,
    codeDesc: req.codeDesc,
    codeType: req.codeType,
  };

  return request<never, Response<CodeConfigItem[]>>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/create`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    params,
    data,
  });
}
export function setCodeConfigData(
  req: Partial<{
    id: string;
    parentId: string;
    codeName: string;
    codeDesc: string;
    codeType: CodeConfigItem["codeConfigType"];
  }> &
    RequestBase
) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = {
    id: req.id,
    parentId: req.parentId || null,
    codeName: req.codeName,
    codeDesc: req.codeDesc,
    codeType: req.codeType,
  };

  return request<never, Response<CodeConfigItem[]>>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/update`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    params,
    data,
  });
}
export function modCodeConfigData(
  req: Partial<{
    id: string;
    parentId: string;
    codeName: string;
    codeDesc: string;
    codeType: CodeConfigItem["codeConfigType"];
  }> &
    RequestBase
) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = {
    id: req.id,
    parentId: req.parentId || null,
    codeName: req.codeName,
    codeDesc: req.codeDesc,
    codeType: req.codeType,
  };

  return request<never, Response<CodeConfigItem[]>>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/update`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    params,
    data,
  });
}
export function delCodeConfigData(
  req: Partial<{
    id: string;
    parentId: string;
    codeName: string;
    codeDesc: string;
    codeType: CodeConfigItem["codeConfigType"];
  }> &
    RequestBase
) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = new URLSearchParams();

  return request<never, Response<CodeConfigItem[]>>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/delete/${req.id}`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    params,
    data,
  });
}
export function setCodeConfigDataByStatus(req: Partial<{ id: string; ruleStatus: boolean }> & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({ ruleStatus: req.ruleStatus }, params);
  const data = new URLSearchParams();

  return request<never, Response<CodeConfigItem[]>>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/changeStatus/${req.id}`,
    method: Method.Patch,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    params,
    data,
  });
}

export function codeConfigChangeStatus(data: { id: string | number; ruleStatus: boolean } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/changeStatus/${data.id}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      ruleStatus: data.ruleStatus,
    },
    data: {},
  });
}

export function codeConfigAllName(data: object & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/allConfigName`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function codeConfigDelete(data: { id: string | number } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/delete/${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//新增完结代码----------------

export interface CodeConfigItemNew {
  id: string /* ID */;
  parentId?: string /* 上级节点ID */;
  codeName: string /* 完成代码名称 */;
  codeDesc: string /* 完成代码描述 */;
  defaultable: boolean;
}

//新增完结代码
export function addNewContacts(data: Partial<CodeConfigItemNew> & RequestBase) {
  return request<CodeConfigItemNew>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//编辑完结代码
export function editNewContacts(data: Partial<CodeConfigItemNew> & RequestBase) {
  return request<never, CodeConfigItemNew>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/update`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//查询完结代码的列表
export function getnewCodeConfigList(data: {} & RequestBase) {
  const dataQuery = {
    containerId: data.containerId,
    queryPermissionId: data.queryPermissionId,
    verifyPermissionIds: data.verifyPermissionIds,

    ...([...(data.includeName instanceof Array ? data.includeName : []), ...(data.excludeName instanceof Array ? data.excludeName : []), ...(data.eqName instanceof Array ? data.eqName : []), ...(data.neName instanceof Array ? data.neName : [])].filter((v) => v).length ? { nameFilterRelation: data.nameFilterRelation === "OR" ? "OR" : "AND", includeName: data.includeName instanceof Array && data.includeName.length ? data.includeName.join(",") : void 0, excludeName: data.excludeName instanceof Array && data.excludeName.length ? data.excludeName.join(",") : void 0, eqName: data.eqName instanceof Array && data.eqName.length ? data.eqName.join(",") : void 0, neName: data.neName instanceof Array && data.neName.length ? data.neName.join(",") : void 0 } : {}),

    ...([...(data.includeDescription instanceof Array ? data.includeDescription : []), ...(data.excludeDescription instanceof Array ? data.excludeDescription : []), ...(data.eqDescription instanceof Array ? data.eqDescription : []), ...(data.neDescription instanceof Array ? data.neDescription : [])].filter((v) => v).length ? { descriptionFilterRelation: data.descriptionFilterRelation === "OR" ? "OR" : "AND", includeDescription: data.includeDescription instanceof Array && data.includeDescription.length ? data.includeDescription.join(",") : void 0, excludeDescription: data.excludeDescription instanceof Array && data.excludeDescription.length ? data.excludeDescription.join(",") : void 0, eqDescription: data.eqDescription instanceof Array && data.eqDescription.length ? data.eqDescription.join(",") : void 0, neDescription: data.neDescription instanceof Array && data.neDescription.length ? data.neDescription.join(",") : void 0 } : {}),
  };

  return request<never, Response<CodeConfigItemNew[]>>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/2.0/list/filter`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: dataQuery,
  });
}

//删除完结代码
export function DelCodeConfig(data: {} & RequestBase) {
  return request<never, CodeConfigItemNew[]>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/delete/${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//根据当前关闭代码id和关闭代码类型获取展开后的配置
export function getexpandConfig(
  data: {
    id: string;
    codeType?: CodeConfigItem["codeConfigType"];
  } & RequestBase
) {
  return request<never, CodeConfigItemNew[]>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/${data.codeType}/expandConfig/${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//新增完结代码配置
export function addNewCodeconfig(data: Partial<CodeConfigItemNew> & RequestBase) {
  return request<CodeConfigItemNew>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/${data.id}/createConfig`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["parentId", "codeName", "codeDesc", "codeType", "containerId", "removeFromReport"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

//编辑完结代码配置
export function editNewCodeconfig(data: Partial<CodeConfigItemNew> & RequestBase) {
  return request<CodeConfigItemNew>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/updateConfig`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//删除完结代码配置
export function deleteNewCodeconfig(data: Partial<CodeConfigItemNew> & RequestBase) {
  return request<CodeConfigItemNew>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/deleteConfig/${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//判断除当前完结代码之外是否有默认完结代码
export function hasDefaultCloseCode(data: Partial<CodeConfigItemNew> & RequestBase) {
  return request<CodeConfigItemNew>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/hasDefaultCloseCode/${data.id}`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//查询完结代码的列表tree
export function getCodelistByOrderList(data: {} & RequestBase) {
  return request<never, CodeConfigItemNew[]>({
    url: `${SERVER.EVENT_CENTER}/codeConfig/2.0/listByOrder`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: Object.assign(data, { tenantId: (getUserInfo().currentTenant || {}).id }),
  });
}

// export function getCloseReason(data: {} & RequestBase) {
//   return request<never, CodeConfigItemNew[]>({
//     url: `${SERVER.EVENT_CENTER}//close_code/queryAllCode`,
//     method: Method.Get,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: data,
//     data: {},
//   });
// }
