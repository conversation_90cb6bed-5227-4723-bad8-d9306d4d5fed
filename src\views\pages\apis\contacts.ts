import { SERVER, Method, type Response, type RequestBase, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

/**
 * @description queryDesensitizedV2-响应体
 * @url http://*************:3000/project/47/interface/api/23129
 */
export type ContactsItem = {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 头衔 */
  title?: string;
  /** 姓名 */
  name: string;
  /** 语言 */
  language?: string;
  /** 邮箱地址 */
  email?: string;
  /** 固定电话 */
  landlinePhone?: string;
  /** 移动电话 */
  mobilePhone?: string;
  /** 下班后联系电话 */
  afterWorkPhone?: string;
  /** 短信号码 */
  smsPhone?: string;
  /** 传真 */
  fax?: string;
  /** 是否启用短信 */
  smsEnabled: boolean;
  /** 是否VIP */
  vip: boolean;
  /** 备注 */
  note?: string;
  /** 时区ID */
  zoneId?: string;
  /** 外部ID */
  externalId?: string;
  /** 是否激活 */
  active: boolean;
  /** 是否国际电话 */
  internationalPhone: boolean;
  /** 是否接受邮件 */
  acceptEmail: boolean;
  /** 职位 */
  position?: string;
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
  /** 拥有的权限 */
  hasPermissionIds: /* Integer */ string[];
  tenantName: string;
  tenantAbbreviation: string;
};

export async function getContactsQuery(req: Record<string, any> & { pageNumber: string; pageSize: string /* 页大小, 默认10 */; sort?: string[]; queryPermissionId?: string; filter?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/contacts/desensitized/queryContactList`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();

        // bindParamByObj({ pageNumber: req.pageNumber /* 页码, 默认第一页 */, pageSize: req.pageSize /* 页大小, 默认10 */, sort: req.sort, filter: req.filter }, $req.params);

        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_联系人_可读, 资产管理中心_联系人_新增, 资产管理中心_联系人_编辑, 资产管理中心_联系人_删除 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [资产管理中心_联系人_可读].join(","), verifyPermissionIds: [资产管理中心_联系人_新增, 资产管理中心_联系人_编辑, 资产管理中心_联系人_删除].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<ContactsItem[]>>($req)),
    { controller }
  );
}

/**
 * @description queryDesensitizedV2
 * @url http://*************:3000/project/47/interface/api/23129
 */
export async function getContacts(req: Record<string, any> & { pageNumber: string; pageSize: string /* 页大小, 默认10 */; sort?: string[]; queryPermissionId?: string; filter?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/contacts/2.0/tenant/current/desensitized/filter`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        bindParamByObj(
          Object.assign(
            {
              /* 联系人姓名 */
              ...([...(req.includeName instanceof Array ? req.includeName : []), ...(req.excludeName instanceof Array ? req.excludeName : []), ...(req.eqName instanceof Array ? req.eqName : []), ...(req.neName instanceof Array ? req.neName : [])].filter((v) => v).length ? { nameFilterRelation: req.nameFilterRelation === "OR" ? "OR" : "AND", includeName: req.includeName instanceof Array && req.includeName.length ? req.includeName.join(",") : void 0, excludeName: req.excludeName instanceof Array && req.excludeName.length ? req.excludeName.join(",") : void 0, eqName: req.eqName instanceof Array && req.eqName.length ? req.eqName.join(",") : void 0, neName: req.neName instanceof Array && req.neName.length ? req.neName.join(",") : void 0 } : {}),

              /* 联系人邮箱 */
              ...([...(req.includeEmail instanceof Array ? req.includeEmail : []), ...(req.excludeEmail instanceof Array ? req.excludeEmail : []), ...(req.eqEmail instanceof Array ? req.eqEmail : []), ...(req.neEmail instanceof Array ? req.neEmail : [])].filter((v) => v).length ? { emailFilterRelation: req.emailFilterRelation === "OR" ? "OR" : "AND", includeEmail: req.includeEmail instanceof Array && req.includeEmail.length ? req.includeEmail.join(",") : void 0, excludeEmail: req.excludeEmail instanceof Array && req.excludeEmail.length ? req.excludeEmail.join(",") : void 0, eqEmail: req.eqEmail instanceof Array && req.eqEmail.length ? req.eqEmail.join(",") : void 0, neEmail: req.neEmail instanceof Array && req.neEmail.length ? req.neEmail.join(",") : void 0 } : {}),

              /* 联系人固定电话 */
              ...([...(req.includeLandlinePhone instanceof Array ? req.includeLandlinePhone : []), ...(req.excludeLandlinePhone instanceof Array ? req.excludeLandlinePhone : []), ...(req.eqLandlinePhone instanceof Array ? req.eqLandlinePhone : []), ...(req.neLandlinePhone instanceof Array ? req.neLandlinePhone : [])].filter((v) => v).length ? { landlinePhoneFilterRelation: req.landlinePhoneFilterRelation === "OR" ? "OR" : "AND", includeLandlinePhone: req.includeLandlinePhone instanceof Array && req.includeLandlinePhone.length ? req.includeLandlinePhone.join(",") : void 0, excludeLandlinePhone: req.excludeLandlinePhone instanceof Array && req.excludeLandlinePhone.length ? req.excludeLandlinePhone.join(",") : void 0, eqLandlinePhone: req.eqLandlinePhone instanceof Array && req.eqLandlinePhone.length ? req.eqLandlinePhone.join(",") : void 0, neLandlinePhone: req.neLandlinePhone instanceof Array && req.neLandlinePhone.length ? req.neLandlinePhone.join(",") : void 0 } : {}),

              /* 联系人移动电话 */
              ...([...(req.includeMobilePhone instanceof Array ? req.includeMobilePhone : []), ...(req.excludeMobilePhone instanceof Array ? req.excludeMobilePhone : []), ...(req.eqMobilePhone instanceof Array ? req.eqMobilePhone : []), ...(req.neMobilePhone instanceof Array ? req.neMobilePhone : [])].filter((v) => v).length ? { mobilePhoneFilterRelation: req.mobilePhoneFilterRelation === "OR" ? "OR" : "AND", includeMobilePhone: req.includeMobilePhone instanceof Array && req.includeMobilePhone.length ? req.includeMobilePhone.join(",") : void 0, excludeMobilePhone: req.excludeMobilePhone instanceof Array && req.excludeMobilePhone.length ? req.excludeMobilePhone.join(",") : void 0, eqMobilePhone: req.eqMobilePhone instanceof Array && req.eqMobilePhone.length ? req.eqMobilePhone.join(",") : void 0, neMobilePhone: req.neMobilePhone instanceof Array && req.neMobilePhone.length ? req.neMobilePhone.join(",") : void 0 } : {}),

              //             eqSmsPhone: [],
              // includeSmsPhone: [],
              // smsPhoneFilterRelation: "AND",
              // neSmsPhone: [],
              // excludeSmsPhone: [],

              ...([...(req.includeSmsPhone instanceof Array ? req.includeSmsPhone : []), ...(req.excludeSmsPhone instanceof Array ? req.excludeSmsPhone : []), ...(req.eqSmsPhone instanceof Array ? req.eqSmsPhone : []), ...(req.neSmsPhone instanceof Array ? req.neSmsPhone : [])].filter((v) => v).length ? { smsPhoneFilterRelation: req.smsPhoneFilterRelation === "OR" ? "OR" : "AND", includeSmsPhone: req.includeSmsPhone instanceof Array && req.includeSmsPhone.length ? req.includeSmsPhone.join(",") : void 0, excludeSmsPhone: req.excludeSmsPhone instanceof Array && req.excludeSmsPhone.length ? req.excludeSmsPhone.join(",") : void 0, eqSmsPhone: req.eqSmsPhone instanceof Array && req.eqSmsPhone.length ? req.eqSmsPhone.join(",") : void 0, neSmsPhone: req.neSmsPhone instanceof Array && req.neSmsPhone.length ? req.neSmsPhone.join(",") : void 0 } : {}),

              /* 联系人描述 */
              ...([...(req.includeNote instanceof Array ? req.includeNote : []), ...(req.excludeNote instanceof Array ? req.excludeNote : []), ...(req.eqNote instanceof Array ? req.eqNote : []), ...(req.neNote instanceof Array ? req.neNote : [])].filter((v) => v).length ? { noteFilterRelation: req.noteFilterRelation === "OR" ? "OR" : "AND", includeNote: req.includeNote instanceof Array && req.includeNote.length ? req.includeNote.join(",") : void 0, excludeNote: req.excludeNote instanceof Array && req.excludeNote.length ? req.excludeNote.join(",") : void 0, eqNote: req.eqNote instanceof Array && req.eqNote.length ? req.eqNote.join(",") : void 0, neNote: req.neNote instanceof Array && req.neNote.length ? req.neNote.join(",") : void 0 } : {}),

              /* 联系人时区 */
              ...([...(req.includeZoneId instanceof Array ? req.includeZoneId : []), ...(req.excludeZoneId instanceof Array ? req.excludeZoneId : []), ...(req.eqZoneId instanceof Array ? req.eqZoneId : []), ...(req.neZoneId instanceof Array ? req.neZoneId : [])].filter((v) => v).length ? { zoneIdFilterRelation: req.zoneIdFilterRelation === "OR" ? "OR" : "AND", includeZoneId: req.includeZoneId instanceof Array && req.includeZoneId.length ? req.includeZoneId.join(",") : void 0, excludeZoneId: req.excludeZoneId instanceof Array && req.excludeZoneId.length ? req.excludeZoneId.join(",") : void 0, eqZoneId: req.eqZoneId instanceof Array && req.eqZoneId.length ? req.eqZoneId.join(",") : void 0, neZoneId: req.neZoneId instanceof Array && req.neZoneId.length ? req.neZoneId.join(",") : void 0 } : {}),

              /* 联系人外部ID */
              ...([...(req.includeExternalId instanceof Array ? req.includeExternalId : []), ...(req.excludeExternalId instanceof Array ? req.excludeExternalId : []), ...(req.eqExternalId instanceof Array ? req.eqExternalId : []), ...(req.neExternalId instanceof Array ? req.neExternalId : [])].filter((v) => v).length ? { externalIdFilterRelation: req.externalIdFilterRelation === "OR" ? "OR" : "AND", includeExternalId: req.includeExternalId instanceof Array && req.includeExternalId.length ? req.includeExternalId.join(",") : void 0, excludeExternalId: req.excludeExternalId instanceof Array && req.excludeExternalId.length ? req.excludeExternalId.join(",") : void 0, eqExternalId: req.eqExternalId instanceof Array && req.eqExternalId.length ? req.eqExternalId.join(",") : void 0, neExternalId: req.neExternalId instanceof Array && req.neExternalId.length ? req.neExternalId.join(",") : void 0 } : {}),
            },
            req.smsEnabled ? { smsEnabled: req.smsEnabled } : {},
            req.vip ? { vip: req.vip } : {},
            req.active ? { active: req.active } : {}
          ),
          $req.params
        );

        bindParamByObj({ pageNumber: req.pageNumber /* 页码, 默认第一页 */, pageSize: req.pageSize /* 页大小, 默认10 */, sort: req.sort, filter: req.filter }, $req.params);

        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_联系人_可读, 资产管理中心_联系人_新增, 资产管理中心_联系人_编辑, 资产管理中心_联系人_删除, 资产管理中心_联系人_安全, 资产管理中心_联系人_查看联系人 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [req.queryPermissionId || 资产管理中心_联系人_可读].join(","), verifyPermissionIds: [资产管理中心_联系人_新增, 资产管理中心_联系人_编辑, 资产管理中心_联系人_删除, 资产管理中心_联系人_安全, 资产管理中心_联系人_查看联系人].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<ContactsItem[]>>($req)),
    { controller }
  );
}

export async function getContactsquery(req: { pageNumber: string; pageSize: string /* 页大小, 默认10 */; sort?: string[]; queryPermissionId?: string; filter?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/contacts/2.0/tenant/current/desensitized`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.pageNumber /* 页码, 默认第一页 */, pageSize: req.pageSize /* 页大小, 默认10 */, sort: req.sort, filter: req.filter }, $req.params);
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_联系人_可读, 资产管理中心_联系人_新增, 资产管理中心_联系人_编辑, 资产管理中心_联系人_删除, 资产管理中心_联系人_查看联系人 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [req.queryPermissionId || 资产管理中心_联系人_可读].join(","), verifyPermissionIds: [资产管理中心_联系人_新增, 资产管理中心_联系人_编辑, 资产管理中心_联系人_删除, 资产管理中心_联系人_查看联系人].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<ContactsItem[]>>($req)),
    { controller }
  );
}
// export function getContacts(data: { pageNumber: string | number; pageSize: string | number } & RequestBase) {
//   return request<unknown, Response<ContactsItem[]>>({
//     url: `${SERVER.CMDB}/contacts/tenant/current/desensitized`,
//     method: Method.Get,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: data,
//     data: {},
//   });
// }

export function getContactInfoById(data: { id: string } & RequestBase) {
  return request<ContactsItem>({
    url: `${SERVER.CMDB}/contacts/${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function addContacts(data: Partial<ContactsItem> & RequestBase) {
  return request<ContactsItem>({
    url: `${SERVER.CMDB}/contacts/tenant/current`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function modContacts(data: Partial<ContactsItem> & RequestBase, id: string) {
  return request<ContactsItem>({
    url: `${SERVER.CMDB}/contacts/${id}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function delContacts(data: Partial<ContactsItem> & RequestBase) {
  return request<ContactsItem>({
    url: `${SERVER.CMDB}/contacts/${data.id}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export interface ContactsTypeItem {
  code: string;
  cnName: string;
  enName: string;
}

export function getContactTypes(data: object & RequestBase) {
  return request<never, Response<ContactsTypeItem[]>>({
    url: `${SERVER.CMDB}/contact_types`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

/**
 * @name 导出联系人列表
 * @export
 * @param {(object & RequestBase)} data
 * @return {*}
 */
export function contactsExport(data: object & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.REPORT_CMDB}/contacts/export`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

export function getContactByIds(data: { ids: string[] } & RequestBase) {
  return request<unknown, Response<Record<string, unknown>[]>>({
    url: `${SERVER.CMDB}/contacts/batch_get/${data.ids}/desensitized`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
export function eventBatchdesensitized(data: { ids: string[] } & RequestBase) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.CMDB}/contacts/batch_get/desensitized`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {
      ids: data.ids,
      permissionId: "513148893207199744",
    },
  });
}
//根据租户ID来查询联系人数据
export function getTenantIdContact(data: { id: string } & RequestBase) {
  return request<unknown, Response<Record<string, unknown>[]>>({
    url: `${SERVER.CMDB}/contacts/desensitized/queryContactListByTenantId?tenantId=${data.id}&permissionId=513148893207199744`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
