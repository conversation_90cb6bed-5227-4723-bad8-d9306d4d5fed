import { SERVER, Method, type Response, type RequestBase, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

export interface CustomerContactItem {
  id: /* Integer */ string;
  /** 姓名 */
  name: string;
  /** 手机号 */
  phone: string;
  /** 短信验证码 */
  verificationCode: string;
  /** 邮箱 */
  email?: string;
  /** 公司/单位名称 */
  company?: string;
  /** 所在行业 */
  industry?: string;
  /** 职位 */
  position?: string;
  /** 咨询问题 */
  question?: string;
  /** 更新时间 */
  updatedTime?: /* Integer */ string;
}

export enum Industry {
  INTERNET_SERVICES = "互联网和相关服务",
  ICT = "ICT",
  IOT_HARDWARE = "物联网和智能硬件",
  FINANCE = "金融",
  GOVERNMENT = "政府",
  PUBLIC_UTILITIES = "公共事业",
  EDUCATION_RESEARCH = "教育及科研",
  MEDICAL_HEALTH = "医疗卫生服务",
  TRANSPORTATION = "交通",
  MANUFACTURING = "制造业",
  HOSPITALITY_TOURISM = "餐饮、酒店、旅游服务业",
  REAL_ESTATE_CONSTRUCTION = "地产、建筑、房屋租售",
  WHOLESALE_RETAIL = "批发和零售业",
  ENERGY_MINING = "能源及采矿业",
  MEDIA_ENTERTAINMENT = "广电、新闻出版及文化娱乐业",
  WATER_ENVIRONMENT_FACILITIES = "水利、环境和公共设施管理业",
  MILITARY = "军工",
  SOCIAL_INTERNATIONAL_ORGS = "社会组织和国际组织",
  CENTRAL_ENTERPRISES = "央企",
  OTHER = "其他",
}

export enum Position {
  OPERATIONS_ENGINEER = "运维工程师",
  DEVELOPMENT_ENGINEER = "开发工程师",
  ARCHITECT = "架构师",
  TEST_ENGINEER = "测试工程师",
  IT_MANAGER = "IT经理/技术经理",
  PRODUCT_MANAGER = "产品经理",
  TECHNICAL_LEADER = "技术负责人",
  BUSINESS_LINE_LEADER = "业务线负责人",
  EXECUTIVE_MANAGER = "CIO/CTO/CEO等管理者",
  OTHER_PROFESSIONAL = "其他(运营/市场/媒体/学生等",
  OTHER = "其他",
}

export async function getCustomerContacts(req: { pageNumber: string; pageSize: string /* 页大小, 默认10 */; sort?: string[]; filter?: string }, data: Record<string, any>) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/expert/find_all`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        $req.data = data;
        bindParamByObj({ pageNumber: req.pageNumber /* 页码, 默认第一页 */, pageSize: req.pageSize /* 页大小, 默认10 */, sort: req.sort }, $req.params);
        return $req;
      })
      .then(($req) => request<never, Response<CustomerContactItem[]>>($req)),
    { controller }
  );
}

export function getCustomerContactItem(data: RequestBase) {
  return request<never, Response<CustomerContactItem>>({
    url: `${SERVER.EVENT_CENTER}/expert/find_one`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { id: data.id },
    data: {},
  });
}
