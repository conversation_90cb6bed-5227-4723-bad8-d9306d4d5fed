import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/aws";
import pingRequest from "@/api/service/ping";

export interface DelayList {
  cloudSiteId: string /*  云站点Id */;
  count: string /** 发包数量 */;
  durationSeconds: string /** 持续ping时间 */;
}
//线路列表
export function getDelayLineList(data: RequestBase) {
  return request<DelayList[]>({
    url: `/ping_tool/line/list`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//查看ping
export function PingMessgae(data: RequestBase) {
  return pingRequest<DelayList[]>({
    url: `/ping_tool/ping/sse`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    onDownloadProgress(e) {
      // // console.log(e);
    },
    onUploadProgress(progressEvent) {
      // // console.log(progressEvent);
    },
    headers: {},
    params: data,
    data: {},
  });
}

//新增线路
export function addDelay(data: RequestBase) {
  return request<DelayList[]>({
    url: `/ping_tool/line`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//删除线路
export function delayDel(data: RequestBase) {
  return request<DelayList[]>({
    url: `/ping_tool/line/${data.id}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function getTypeList(data: RequestBase) {
  return request<DelayList[]>({
    url: `/ping_tool/line/types`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
