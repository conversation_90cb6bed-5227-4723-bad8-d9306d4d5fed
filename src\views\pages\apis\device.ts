/* eslint-disable indent */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { SERVER, Method, type Response, type RequestBase, bindSearchParams, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

import { priority, priorityOption } from "./eventPriority";
import { i18n } from "@/lang";
export { priority, priorityOption };

export enum LoginMode {
  DEFAULT = "DEFAULT",
  ON_CONNECT = "ON_CONNECT",
  REQUIRE_TFA = "REQUIRE_TFA",
}

export const loginModeOption: { value: keyof typeof LoginMode; label: string; desc: string[] }[] = [
  { value: LoginMode.DEFAULT, label: i18n.global.t("devicesAdd.Default"), desc: [i18n.global.t("devicesAdd.direct")] },
  { value: LoginMode.ON_CONNECT, label: i18n.global.t("devicesAdd.through Connector"), desc: [i18n.global.t("devicesAdd.Input")] },
  { value: LoginMode.REQUIRE_TFA, label: i18n.global.t("devicesAdd.require TFA"), desc: [i18n.global.t("devicesAdd.Input"), i18n.global.t("devicesAdd.Two-factor")] },
];

export enum MonitorSources {
  NIGHTINGALE_V6 = "NIGHTINGALE_V6",
  NETCARE_V6 = "NETCARE_V6",
  OTHER = "OTHER",
}

export const monitorSourcesOption: { value: string; label: string }[] = [
  { label: "Nightingale V6", value: "NIGHTINGALE_V6" },
  { label: "NetCare V6", value: "NETCARE_V6" },
  { label: "Other", value: "OTHER" },
];

/* TODO: 资源类型 */
export interface ResourceTypeItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 类型名称 */
  name: string;
  /** 类型英文名称 */
  enName?: string;
  /** 描述 */
  description?: string;
  /** 告警分类ID列表 */
  alertClassificationIds: /* Integer */ string[];
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
  /** 拥有的权限 */
  hasPermissionIds: /* Integer */ string[];
}
// /**
//  * @description 获取资源类型列表
//  * @url http://*************:3000/project/47/interface/api/1249
//  */
// export function /* 获取资源类型列表 */ getResourceTypeList(req: Partial<Record<"alertClassificationId", string>> & RequestBase) {
//   // const header = new Headers();
//   const params = new URLSearchParams({});
//   bindSearchParams({ alertClassificationId: req.alertClassificationId }, params);
//   const data = new URLSearchParams({});
//   return request<never, Response<ResourceTypeItem[]>>({ url: `${SERVER.CMDB}/resource_types`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
// }
/**
 * @description 获取资源类型列表
 * @url http://*************:3000/project/47/interface/api/23150
 */
export async function getResourceTypeList(req: { alertClassificationId?: string; queryPermissionId?: string; verifyPermissionIds?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/resource_types/2.0`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        bindParamByObj({ alertClassificationId: req.alertClassificationId }, $req.params);
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_设备类型_可读, 资产管理中心_设备类型_新增, 资产管理中心_设备类型_编辑, 资产管理中心_设备类型_删除 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [req.queryPermissionId || 资产管理中心_设备类型_可读].join(","), verifyPermissionIds: req.verifyPermissionIds || [资产管理中心_设备类型_新增, 资产管理中心_设备类型_编辑, 资产管理中心_设备类型_删除].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<ResourceTypeItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 批量添加设备类型告警分类
 * @url http://*************:3000/project/47/interface/api/1243
 */
export function /* 批量添加设备类型告警分类 */ addTypeByClassifications(req: { ids: string[]; alertClassificationIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { ids: req.ids /* ID列表 */, alertClassificationIds: req.alertClassificationIds /* 告警分类ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/resource_types/alert_classification/add/batch`, method: Method.Put, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 批量移除设备类型告警分类
 * @url http://*************:3000/project/47/interface/api/1245
 */
export function /* 批量移除设备类型告警分类 */ delTypeByClassifications(req: { ids: string[]; alertClassificationIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { ids: req.ids /* ID列表 */, alertClassificationIds: req.alertClassificationIds /* 告警分类ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/resource_types/alert_classification/remove/batch`, method: Method.Put, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/* TODO: 服务商名称 */
// export enum vendorsProtocol {
//   GENERIC_OAUTH = "GENERIC_OAUTH",
// }
// export const vendorsProtocolOption: { label: string; value: keyof typeof vendorsProtocol; color?: string }[] = [
//   /*  */
//   { label: "OAuth2协议", value: vendorsProtocol.GENERIC_OAUTH },
// ];
// export interface VendorsType {
//   id?: string /* 主键 */;
//   platform?: string /* 平台编码 */;
//   tenantId?: string /* 所属租户ID */;
//   protocol?: vendorsProtocol /* 认证协议 枚举类型: GENERIC_OAUTH :OAuth2协议 */;
//   name?: string /* 名称 */;
//   note?: string /* 备注信息 */;
//   allowSignUp: boolean /* 是否自动注册账号 */;
//   autoMerge: boolean /* 创建用户时自动合并账号 */;
//   supportMfa: boolean /* 是否需要多因素登录 */;
//   enabled: boolean /* 是否启用 */;
//   config?: string /* 配置信息 */;
//   version: string /* 乐观锁版本号 */;
//   createdTime: string /* 创建时间 */;
//   updatedTime: string /* 更新时间 */;
//   createdBy?: string /* 创建人信息 */;
//   updatedBy?: string /* 最后更新人信息 */;
// }
export interface VendorsItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 名称 */
  name: string;
  /** 描述 */
  description?: string;
  /** 地址 */
  address?: string;
  /** 固定电话 */
  landlinePhone?: string;
  /** 支持电话 */
  supportPhone?: string;
  /** 联系人姓名 */
  contactName?: string;
  /** 电子邮箱 */
  email?: string;
  /** 供应商类别 */
  vendorType?: string;
  /** 供应商类别名称 */
  vendorTypeName?: string;
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}
// /**
//  * @description 查询供应商-脱敏
//  * @url http://*************:3000/project/47/interface/api/1299
//  */
// export function /* 查询供应商-脱敏 */ getVendorsList(req: RequestBase) {
//   // const header = new Headers();
//   const params = new URLSearchParams({});
//   bindSearchParams({}, params);
//   const data = new URLSearchParams({});
//   return request<never, Response<VendorsItem[]>>({ url: `${SERVER.CMDB}/vendors/DEVICE/desensitized`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
// }

/**
 * @description 获取供应商2.0
 * @url http://*************:3000/project/47/interface/api/24877
 */
export async function getVendorsList(req: { vendorType: "DEVICE" | "LINE" }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/vendors/${req.vendorType}/desensitized/2.0`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_设备供应商_可读, 资产管理中心_设备供应商_新增, 资产管理中心_设备供应商_编辑, 资产管理中心_设备供应商_删除, 资产管理中心_线路供应商_可读, 资产管理中心_线路供应商_新增, 资产管理中心_线路供应商_编辑, 资产管理中心_线路供应商_删除 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          switch (req.vendorType) {
            case "DEVICE":
              bindParamByObj({ queryPermissionId: [资产管理中心_设备供应商_可读].join(","), verifyPermissionIds: [资产管理中心_设备供应商_新增, 资产管理中心_设备供应商_编辑, 资产管理中心_设备供应商_删除].join(",") }, $req.params);
              break;
            case "LINE":
              bindParamByObj({ queryPermissionId: [资产管理中心_线路供应商_可读].join(","), verifyPermissionIds: [资产管理中心_线路供应商_新增, 资产管理中心_线路供应商_编辑, 资产管理中心_线路供应商_删除].join(",") }, $req.params);
              break;
            default:
              throw new Error("Web Error: 无效供应商类型");
          }
        } catch (error) {
          if (error instanceof Error) throw error;
        }
        return $req;
      })
      .then(($req) => request<never, Response<VendorsItem[]>>($req)),
    { controller }
  );
}

/* TODO: 告警分类 */
export interface AlertClassificationsItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 分类名称 */
  name: string;
  /** 描述信息 */
  description?: string;
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}
/**
 * @description 获取所有告警分类
 * @url http://*************:3000/project/47/interface/api/1069
 */
export function /* 获取所有告警分类 */ getAlertClassificationsList(req: RequestBase) {
  // const header = new Headers();
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = new URLSearchParams({});
  return request<never, Response<AlertClassificationsItem[]>>({ url: `${SERVER.CMDB}/alert_classifications`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/* TODO: 设备分组 */
export interface DeviceGroupItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 名称 */
  name: string;
  /** 描述信息 */
  description?: string;
  /** 是否为报告分组 */
  report: boolean;
  /** 告警分类ID列表 */
  alertClassificationIds: /* Integer */ string[];
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}
// /**
//  * @description 获取当前租户下所有设备组
//  * @url http://*************:3000/project/47/interface/api/1103
//  */
// export function /* 获取当前租户下所有设备组 */ getDeviceGroupList(req: Partial<Record<"alertClassificationId", string>> & RequestBase) {
//   // const header = new Headers();
//   const params = new URLSearchParams({});
//   bindSearchParams({ alertClassificationId: req.alertClassificationId /* undefined */ }, params);
//   const data = new URLSearchParams({});
//   return request<never, Response<DeviceGroupItem[]>>({ url: `${SERVER.CMDB}/groups/tenant/current`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
// }
/**
 * @description 获取当前租户下所有设备组
 * @url http://*************:3000/project/47/interface/api/23136
 */
export async function getDeviceGroupList(req: { alertClassificationId?: string; queryPermissionId?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/groups/2.0/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        bindParamByObj({ alertClassificationId: req.alertClassificationId }, $req.params);
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_设备分组_可读, 资产管理中心_设备分组_新增, 资产管理中心_设备分组_编辑, 资产管理中心_设备分组_删除 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [req.queryPermissionId || 资产管理中心_设备分组_可读].join(","), verifyPermissionIds: [资产管理中心_设备分组_新增, 资产管理中心_设备分组_编辑, 资产管理中心_设备分组_删除].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<DeviceGroupItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 批量添加设备组告警分类
 * @url http://*************:3000/project/47/interface/api/1097
 */
export function /* 批量添加设备组告警分类 */ addDeviceGroupByClassifications(req: { ids: string[]; alertClassificationIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { ids: req.ids /* ID列表 */, alertClassificationIds: req.alertClassificationIds /* 告警分类ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/groups/alert_classification/add/batch`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 批量移除设备组告警分类
 * @url http://*************:3000/project/47/interface/api/1099
 */
export function /* 批量移除设备组告警分类 */ delDeviceGroupByClassifications(req: { ids: string[]; alertClassificationIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { ids: req.ids /* ID列表 */, alertClassificationIds: req.alertClassificationIds /* 告警分类ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/groups/alert_classification/remove/batch`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/* TODO: 服务编号 */
export interface ServiceNumberItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 资源ID */
  resourceId: /* Integer */ string;
  /** 服务编号 */
  number: string;
  /** 供应商列表 */
  vendorIds?: /* Integer */ string[];
  /** 类型 */
  type?: string;
  /** 线路带宽 */
  progress: /* Integer */ string;
  /** 线路带宽单位 */
  progressUnit?: string;
  /** 产品 */
  product?: string;
  /** 描述信息 */
  description?: string;
  /** 端口名称 */
  portName?: string;
  /** 服务等级 */
  serviceLevel?: string;
}

/* TODO: 联系人 */
export interface ContactItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 头衔 */
  title?: string;
  /** 姓名 */
  name: string;
  /** 语言 */
  language?: string;
  /** 邮箱地址 */
  email?: string;
  /** 固定电话 */
  landlinePhone?: string;
  /** 移动电话 */
  mobilePhone?: string;
  /** 下班后联系电话 */
  afterWorkPhone?: string;
  /** 短信号码 */
  smsPhone?: string;
  /** 传真 */
  fax?: string;
  /** 是否启用短信 */
  smsEnabled: boolean;
  /** 是否VIP */
  vip: boolean;
  /** 备注 */
  note?: string;
  /** 时区ID */
  zoneId?: string;
  /** 外部ID */
  externalId?: string;
  /** 是否激活 */
  active: boolean;
  /** 是否国际电话 */
  internationalPhone: boolean;
  /** 是否接受邮件 */
  acceptEmail: boolean;
  /** 职位 */
  position?: string;
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}
/**
 * @description 查询联系人-脱敏
 * @url http://*************:3000/project/47/interface/api/1083
 */
export function /* 查询联系人-脱敏 */ getContactList(req: Partial<Record<"sort" | "dataPermissionType", string>> & RequestBase) {
  // const header = new Headers();

  const params = new URLSearchParams({ pageNumber: `${(req.paging || {}).pageNumber || 1}`, pageSize: `${(req.paging || {}).pageSize || 30}` });
  bindSearchParams({ sort: req.sort, dataPermissionType: req.dataPermissionType }, params);

  const data = new URLSearchParams({});

  return request<never, Response<ContactItem[]>>({ url: `${SERVER.CMDB}/contacts/tenant/current/desensitized`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 创建联系人
 * @url http://*************:3000/project/47/interface/api/1075
 */
export function /* 创建联系人 */ addContactData(req: { title?: string; name?: string; language?: string; email?: string; landlinePhone?: string; mobilePhone?: string; smsPhone?: string; smsEnabled: boolean; vip: boolean; note?: string; zoneId?: string; externalId?: string } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { title: req.title /* 头衔 */, name: req.name /* 姓名 */, language: req.language /* 语言 */, email: req.email /* 联系邮箱 */, landlinePhone: req.landlinePhone /* 固定电话 */, mobilePhone: req.mobilePhone /* 移动电话 */, smsPhone: req.smsPhone /* 短信号码 */, smsEnabled: req.smsEnabled /* 是否启用短信 */, vip: req.vip /* 是否VIP */, note: req.note /* 备注信息 */, zoneId: req.zoneId /* 时区ID */, externalId: req.externalId /* 外部ID */ };
  return request<never, Response<ContactItem>>({ url: `${SERVER.CMDB}/contacts/tenant/current`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 选择性更新联系人
 * @url http://*************:3000/project/47/interface/api/1077
 */
export function /* 选择性更新联系人 */ modContactData(req: Required<Record<"id", string>> & { title?: string; name?: string; language?: string; email?: string; landlinePhone?: string; mobilePhone?: string; smsPhone?: string; smsEnabled?: boolean; vip?: boolean; note?: string; zoneId?: string; externalId?: string } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { title: req.title /* 头衔 */, name: req.name /* 姓名 */, language: req.language /* 语言 */, email: req.email /* 联系邮箱 */, landlinePhone: req.landlinePhone /* 固定电话 */, mobilePhone: req.mobilePhone /* 移动电话 */, smsPhone: req.smsPhone /* 短信号码 */, smsEnabled: req.smsEnabled /* 是否启用短信 */, vip: req.vip /* 是否VIP */, note: req.note /* 备注信息 */, zoneId: req.zoneId /* 时区ID */, externalId: req.externalId /* 外部ID */ };
  return request<never, Response<ContactItem>>({ url: `${SERVER.CMDB}/contacts/${req.id /* 联系人ID */}`, method: Method.Patch, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 删除联系人
 * @url http://*************:3000/project/47/interface/api/1079
 */
export function /* 删除联系人 */ delContactData(req: Required<Record<"id", string>> & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/x-www-form-urlencoded");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = new URLSearchParams({});
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/contacts/${req.id /* 联系人ID */}`, method: Method.Delete, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 根据id查询联系人-非脱敏
 * @url http://*************:3000/project/47/interface/api/1085
 */
export function /* 根据id查询联系人-非脱敏 */ getContactById(req: Required<Record<"id", string>> & RequestBase) {
  // const header = new Headers();
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = new URLSearchParams({});
  return request<never, Response<ContactItem>>({ url: `${SERVER.CMDB}/contacts/${req.id /* 联系人ID */}`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 获取所有联系人类型
 * @url http://*************:3000/project/47/interface/api/1089
 */
export function /* 获取所有联系人类型 */ getContactGroup(req: RequestBase) {
  // const header = new Headers();
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = new URLSearchParams({});
  return request<never, Response<Record<"code" | "cnName" | "enName", string>[]>>({ url: `${SERVER.CMDB}/contact_types`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 根据联系人类型分组获取联系人列表
 * @url http://*************:3000/project/47/interface/api/2369
 */
export function /* 根据联系人类型分组获取联系人列表 */ getContactByContactGroup(req: Partial<Record<"contactType", string>> & RequestBase) {
  // const header = new Headers();
  const params = new URLSearchParams({});
  bindSearchParams({ contactType: req.contactType }, params);
  const data = new URLSearchParams({});
  return request<never, Response<{ contactType: Record<"code" | "cnName" | "enName", string>; contact: ContactItem[] }[]>>({ url: `${SERVER.CMDB}/contacts/contact_type/group_by`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 获取资源联系人
 * @url http://*************:3000/project/47/interface/api/2447
 */
export function /* 获取资源联系人 */ getContactByResource(req: Required<Record<"id", string>> & RequestBase) {
  // const header = new Headers();
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = new URLSearchParams({});
  return request<never, Response<{ contactType: string; contact: ContactItem }[]>>({ url: `${SERVER.CMDB}/resources/${req.id /* 资源ID */}/contacts`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function /* 获取资源联系人 */ getContactByResourceDetails(req: Required<Record<"id", string>> & RequestBase) {
  // const header = new Headers();
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = new URLSearchParams({});
  return request<never, Response<{ contactType: string; contact: ContactItem }[]>>({ url: `${SERVER.CMDB}/resources/${req.id /* 资源ID */}/findAllContacts`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 为资源分配联系人
 * @url http://*************:3000/project/47/interface/api/2443
 */
export function /* 为资源分配联系人 */ addContactByResource(req: Required<Record<"id", string>> & { contactType: string; contactIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { contactType: req.contactType /* 联系人类型 */, contactIds: req.contactIds /* 联系人ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/resources/${req.id /* 资源ID */}/contacts`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 取消资源联系人
 * @url http://*************:3000/project/47/interface/api/2445
 */
export function /* 取消资源联系人 */ delContactByResource(req: Required<Record<"id", string>> & Partial<Record<"contactId" | "contactType", string>> & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/x-www-form-urlencoded");
  const params = new URLSearchParams({});
  bindSearchParams({ contactId: req.contactId /* 联系人ID */, contactType: req.contactType /* 联系人类型 */ }, params);
  const data = new URLSearchParams({});
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/resources/${req.id /* 资源ID */}/contacts`, method: Method.Delete, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 获取区域联系人
 * @url http://*************:3000/project/47/interface/api/1179
 */
export function /* 获取区域联系人 */ getContactByRegions(req: Required<Record<"id", string>> & RequestBase) {
  // const header = new Headers();
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = new URLSearchParams({});
  return request<never, Response<{ contactType: string; contact: ContactItem }[]>>({ url: `${SERVER.CMDB}/regions/${req.id /* 区域ID */}/contacts`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 为区域分配联系人
 * @url http://*************:3000/project/47/interface/api/1175
 */
export function /* 为区域分配联系人 */ addContactByRegions(req: Required<Record<"id", string>> & { contactType: string; contactIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { contactType: req.contactType /* 联系人类型 */, contactIds: req.contactIds /* 联系人ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/regions/${req.id /* 区域ID */}/contacts`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

/**
 * @description 取消区域联系人
 * @url http://*************:3000/project/47/interface/api/1177
 */
export function /* 取消区域联系人 */ delContactByRegions(req: Required<Record<"id", string>> & Partial<Record<"contactId" | "contactType", string>> & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/x-www-form-urlencoded");
  const params = new URLSearchParams({});
  bindSearchParams({ contactId: req.contactId /* 联系人ID */, contactType: req.contactType /* 联系人类型 */ }, params);
  const data = new URLSearchParams({});
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/regions/${req.id /* 区域ID */}/contacts`, method: Method.Delete, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 获取场所联系人
 * @url http://*************:3000/project/47/interface/api/3747
 */
export function /* 获取场所联系人 */ getContactByLocation(req: Required<Record<"id", string>> & RequestBase) {
  // const header = new Headers();
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = new URLSearchParams({});
  return request<never, Response<{ contactType: string; contact: ContactItem }[]>>({ url: `${SERVER.CMDB}/locations/${req.id /* 场所ID */}/contacts`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 为场所分配联系人
 * @url http://*************:3000/project/47/interface/api/3743
 */
export function /* 为场所分配联系人 */ addContactByLocation(req: Required<Record<"id", string>> & { contactType: string; contactIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { contactType: req.contactType /* 联系人类型 */, contactIds: req.contactIds /* 联系人ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/locations/${req.id /* 场所ID */}/contacts`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 取消场所联系人
 * @url http://*************:3000/project/47/interface/api/3745
 */
export function /* 取消场所联系人 */ delContactByLocation(req: Required<Record<"id", string>> & Partial<Record<"contactId" | "contactType", string>> & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/x-www-form-urlencoded");
  const params = new URLSearchParams({});
  bindSearchParams({ contactId: req.contactId /* 联系人ID */, contactType: req.contactType /* 联系人类型 */ }, params);
  const data = new URLSearchParams({});
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/locations/${req.id /* 场所ID */}/contacts`, method: Method.Delete, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
export interface strategyItem {
  id: string /* 主键 */;
  tenantId: string /* 所属租户, 全局行动策略租户ID为 -999 */;
  name: string /* 名称 */;
  description?: string /* 描述信息 */;
  activeConfig: /* 生效配置 */ {
    useAutoTimeZone: boolean /* 是否使用自动时区 */;
    timeZone?: string /* 时区ID, 非自动时区必填 */;
    activeHours: /* 生效时间段 */ { weekDay: string /* 星期标识，用于排序(星期一传1，星期二传2，以此类推) */; hours: /* 生效时间 */ string[] }[];
  };
  activeNote?: string /* Notes for Active Hours */;
  inactiveNote?: string /* Notes for Inactive Hours */;
  active: boolean /* 是否激活 */;
  defaultNote: boolean /* 是否默认策略 */;
  version: string /* 版本号 */;
  createdTime: string /* 创建时间 */;
  updatedTime: string /* 最近更新时间 */;
  createdBy?: string /* 创建人 */;
  updatedBy?: string /* 最后更新人 */;
}

/* TODO: 设备 */
export enum deviceImportance {
  High = "High",
  Medium = "Medium",
  Low = "Low",
  None = "None",
  Unknown = "Unknown",
}

export enum mappingItems {
  Critical = "Critical",
  Major = "Major",
  Minor = "Minor",
  Warning = "Warning",
  Unknown = "Unknown",
  Normal = "Normal",
  Informational = "Informational",
  Calculating = "Calculating",
  Symptom = "Symptom",
  Monitoring = "Monitoring",
}

export const deviceImportanceOption: { label: string; value: keyof typeof deviceImportance; color?: string }[] = [
  { label: "Unknown", value: deviceImportance.Unknown },
  { label: "None", value: deviceImportance.None },
  { label: "Low", value: deviceImportance.Low },
  { label: "Medium", value: deviceImportance.Medium },
  { label: "High", value: deviceImportance.High },
];

export const alertOrderMappingItems: { label: string; value: keyof typeof mappingItems; color?: string }[] = [
  { label: "Monitoring", value: mappingItems.Monitoring },
  { label: "Symptom", value: mappingItems.Symptom },
  { label: "Calculating", value: mappingItems.Calculating },
  { label: "Informational", value: mappingItems.Informational },
  { label: "Critical", value: mappingItems.Critical },
  { label: "Normal", value: mappingItems.Normal },
  { label: "Unknown", value: mappingItems.Unknown },
  { label: "Warning", value: mappingItems.Warning },
  { label: "Minor", value: mappingItems.Minor },
  { label: "Major", value: mappingItems.Major },
  { label: "Critical", value: mappingItems.Critical },
];
// export enum deviceImportance {
//   High = "High",
//   Medium = "Medium",
//   Low = "Low",
//   None = "None",
//   Unknown = "Unknown",
// }
// export const deviceImportanceOption: { label: string; value: keyof typeof deviceImportance }[] = [
//   { label: "High", value: deviceImportance.High },
//   { label: "Medium", value: deviceImportance.Medium },
//   { label: "Low", value: deviceImportance.Low },
//   { label: "None", value: deviceImportance.None },
//   { label: "Unknown", value: deviceImportance.Unknown },
// ];

interface PasswordWalletsSystemItem {
  id: /* Integer */ string;
  /** 租户id */
  tenantId: /* Integer */ string;
  /** 用途 SYSTEM - 系统账号     DB - 数据库账号 */
  purpose: "SYSTEM";
  /** 类型 系统 Linux、Windows 数据库 Mysql、Mongodb、Prometheus、Redis */
  type: "Linux" | "Windows";
  /** 别名 */
  alias: string;
  /** 用户名|账号|名称 */
  username: string;
  /** 密码 */
  password: string;
  /** 描述 */
  desc: string;
  /** 资产ID */
  resourceIds: /* Integer */ string[];
  /** 创建人信息 */
  createdBy?: string;
  /** 更新人信息 */
  updatedBy?: string;
  createdTime: /* Integer */ string;
  updatedTime: /* Integer */ string;
}
interface PasswordWalletsDataBaseItem {
  id: /* Integer */ string;
  /** 租户id */
  tenantId: /* Integer */ string;
  /** 用途 SYSTEM - 系统账号     DB - 数据库账号 */
  purpose: "DB";
  /** 类型 系统 Linux、Windows 数据库 Mysql、Mongodb、Prometheus、Redis */
  type: "Mysql" | "Mongodb" | "Prometheus" | "Redis";
  /** 别名 */
  alias: string;
  /** 用户名|账号|名称 */
  username: string;
  /** 密码 */
  password: string;
  /** 描述 */
  desc: string;
  /** 资产ID */
  resourceIds: /* Integer */ string[];
  /** 创建人信息 */
  createdBy?: string;
  /** 更新人信息 */
  updatedBy?: string;
  createdTime: /* Integer */ string;
  updatedTime: /* Integer */ string;
}
/**
 * @description 当前租户分页查询资源-响应体
 * @url http://*************:3000/project/47/interface/api/23073
 */
export interface ResourcesItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 安全容器 */
  containerId: /* Integer */ string;
  /** 资源池id */
  resourcePoolId: /* Integer */ string;
  /** 管理区编码 */
  precinctCode?: string;
  /** 模型标识 */
  modelIdent: string;
  /** 所在区域ID */
  regionId: /* Integer */ string;
  /** 所在场所ID */
  locationId: /* Integer */ string;
  /** 资源类型ID列表 */
  typeIds: /* Integer */ string[];
  /** 资源关联设备组ID列表 */
  groupIds: /* Integer */ string[];
  /** 服务商ID列表 */
  vendorIds: /* Integer */ string[];
  /** 行动策略列表 */
  supportNoteIds: /* Integer */ string[];
  /** 告警分类ID列表 */
  alertClassificationIds: /* Integer */ string[];
  /** 资产编号 */
  assetNumber?: string;
  /** 外部ID */
  externalId?: string;
  /** 资源名称 */
  name: string;
  /** 别名 */
  alias?: string;
  /** 资源上线时间 */
  resourceOnlineTime?: /* Integer */ string;
  /** 资源下线时间 */
  resourceOfflineTime?: /* Integer */ string;
  /** 是否交付 */
  delivery: boolean;
  /** 监控源 */
  monitorSources: string[];
  /** 业务单位 */
  unit?: string;
  /** 备注|描述信息 */
  description?: string;
  /** 时区 */
  timeZone?: string;
  /** 资源重要性 */
  importance: /* 枚举: High :至关重要的 | Medium :中 | Low :低 | None :无 | Unknown :未知的 */ deviceImportance;
  /** 标签列表 */
  tags: string[];
  /** 资源配置信息 */
  config: { [key: string]: string };
  /** 是否激活 */
  active: boolean;
  /** 最后上线时间 */
  onlineTime: /* Integer */ string;
  /** 最后离线时间 */
  offlineTime: /* Integer */ string;
  /** 服务包 */
  servicePackage?: string;
  /** 协议名 */
  protocolName?: string;
  /** 变更版本号 */
  version: /* Integer */ string;
  /** 录入时间 */
  createdTime: /* Integer */ string;
  /** 最近变更时间 */
  updatedTime: /* Integer */ string;
  /** 录入人 */
  createdBy?: string;
  /** 最近变更人 */
  updatedBy?: string;
  /** 所在区域名称 */
  regionDesc?: string;
  /** 地点名称 */
  locationDesc?: string;
  /** 资源池名称 */
  resourcePoolName: string;
  /** 资源类型 描述 */
  resourceTypes: ResourceTypeItem[];
  /** 服务商名称列表 */
  vendors: VendorsItem[];
  /** 告警分类列表 */
  alertClassifications: AlertClassificationsItem[];
  /** 设备分组列表 */
  groups: DeviceGroupItem[];
  /** 服务编号列表 */
  serviceNumbers: ServiceNumberItem[];
  /** 联系人列表 */
  contacts: ContactItem[];
  /** 登录认证详情 */
  loginAuths?: string;
  /** 拥有的权限 */
  hasPermissionIds: /* Integer */ string[];
  /** 密码钱包列表 */
  passwordWallets: (PasswordWalletsSystemItem | PasswordWalletsDataBaseItem)[];
}

/**
 * @description 当前租户查询资源
 * @url http://*************:3000/project/47/interface/api/34818
 */

export async function getDeviceQuery(req: {
  /*  */
  paging: { pageNumber: number /* 页码, 默认第一页 */; pageSize: number /* 页大小, 默认10 */ };
  resourcePoolId?: string /* 资源池ID */;
  name?: string /* 资源名称 */;
  modelIdent?: string /* 模型标识 */;
  modelIdents?: string[] /* 模型标识列表<pre>    请求示例:modelIdents=physical,router,switch</pre> */;
  regionId?: string /* 区域ID */;
  regionName?: string /* 区域名称 */;
  regionIds?: string[] /* 区域id列表 */;
  locationId?: string /* 场所ID */;
  groupId?: string /* 设备组ID */;
  vendorId?: string /* 供应商ID */;
  supportNoteId?: string /* 行动策略ID */;
  resourceTypeId?: string /* 资源类型ID */;
  alertClassificationId?: string /* 告警分类ID */;
  active?: boolean /* 是否激活 */;
  delivery?: boolean /* 是否交付 */;
  ipAddress?: string /* IP地址 */;
  includeLocations?: string[] /* 包含的场所名称 */;
  locationFilterRelation?: "AND" | "OR" /* 场所过滤关系(AND,OR) */;
  excludeLocations?: string[] /* 不包含的场所名称 */;
  includeDevicesOrIps?: string[] /* 包含的设备名称或IP */;
  deviceOrIpFilterRelation?: "AND" | "OR" /* 设备名称或IP过滤关系(AND,OR) */;
  excludeDevicesOrIps?: string[] /* 不包含的设备名称或IP */;
  includeNmsSettings?: string[] /* 包含的网络监控系统设置 */;
  nmsSettingsFilterRelation?: "AND" | "OR" /* 网络监控系统设置过滤关系(AND,OR) */;
  excludeNmsSettings?: string[] /* 不包含的网络监控系统设置 */;
  includeDeviceBasicInfo?: string[] /* 包含的设备基本信息 */;
  deviceBasicInfoFilterRelation?: "AND" | "OR" /* 设备基本信息过滤关系(AND,OR) */;
  excludeDeviceBasicInfo?: string[] /* 不包含的设备基本信息 */;
  fullQuery?: string /* 全量查询参数，支持全文搜索 */;
  queryPermissionId?: string;
}) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/resources/queryResourceList`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        try {
          const [{ default: getUserInfo }, { 资产管理中心_设备_可读, 资产管理中心_设备_新增, 资产管理中心_设备_编辑 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ active: req.active, queryPermissionId: [req.queryPermissionId || 资产管理中心_设备_可读].join(","), verifyPermissionIds: [资产管理中心_设备_新增, 资产管理中心_设备_编辑].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<ResourcesItem[]>>($req)),
    { controller }
  );
}

/**
 * @description 当前租户分页查询资源
 * @url http://*************:3000/project/47/interface/api/23073
 */

export async function getDeviceList(req: {
  /*  */
  paging: { pageNumber: number /* 页码, 默认第一页 */; pageSize: number /* 页大小, 默认10 */ };
  resourcePoolId?: string /* 资源池ID */;
  name?: string /* 资源名称 */;
  modelIdent?: string /* 模型标识 */;
  modelIdents?: string[] /* 模型标识列表<pre>    请求示例:modelIdents=physical,router,switch</pre> */;
  regionId?: string /* 区域ID */;
  regionName?: string /* 区域名称 */;
  regionIds?: string[] /* 区域id列表 */;
  locationId?: string /* 场所ID */;
  groupId?: string /* 设备组ID */;
  vendorId?: string /* 供应商ID */;
  supportNoteId?: string /* 行动策略ID */;
  resourceTypeId?: string /* 资源类型ID */;
  alertClassificationId?: string /* 告警分类ID */;
  active?: boolean /* 是否激活 */;
  delivery?: boolean /* 是否交付 */;
  ipAddress?: string /* IP地址 */;
  includeLocations?: string[] /* 包含的场所名称 */;
  locationFilterRelation?: "AND" | "OR" /* 场所过滤关系(AND,OR) */;
  excludeLocations?: string[] /* 不包含的场所名称 */;
  includeDevicesOrIps?: string[] /* 包含的设备名称或IP */;
  deviceOrIpFilterRelation?: "AND" | "OR" /* 设备名称或IP过滤关系(AND,OR) */;
  excludeDevicesOrIps?: string[] /* 不包含的设备名称或IP */;
  includeNmsSettings?: string[] /* 包含的网络监控系统设置 */;
  nmsSettingsFilterRelation?: "AND" | "OR" /* 网络监控系统设置过滤关系(AND,OR) */;
  excludeNmsSettings?: string[] /* 不包含的网络监控系统设置 */;
  eqNmsSettings?: string[] /* 等于的网络监控系统设置 */;
  neNmsSettings?: string[] /* 不等于的网络监控系统设置 */;
  includeDeviceBasicInfo?: string[] /* 包含的设备基本信息 */;
  deviceBasicInfoFilterRelation?: "AND" | "OR" /* 设备基本信息过滤关系(AND,OR) */;
  excludeDeviceBasicInfo?: string[] /* 不包含的设备基本信息 */;
  fullQuery?: string /* 全量查询参数，支持全文搜索 */;
  queryPermissionId?: string;
}) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/resources/2.0/tenant/current/desensitized`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams({ pageNumber: `${(req.paging || {}).pageNumber || 1}`, pageSize: `${(req.paging || {}).pageSize || 30}` });
        bindParamByObj(
          {
            /*  */
            resourcePoolId: req.resourcePoolId ? req.resourcePoolId : void 0 /* 资源池ID */,
            name: req.name ? req.name : void 0 /* 资源名称 */,
            modelIdent: req.modelIdent ? req.modelIdent : void 0 /* 模型标识 */,
            modelIdents: req.modelIdents instanceof Array && req.modelIdents.length ? req.modelIdents.join(",") : void 0 /* 模型标识列表 请求示例:modelIdents=physical,router,switch */,
            regionId: req.regionId ? req.regionId : void 0 /* 区域ID */,
            regionName: req.regionName ? req.regionName : void 0 /* 区域名称 */,
            regionIds: req.regionIds instanceof Array && req.regionIds.length ? req.regionIds.join(",") : void 0 /* 区域id列表 */,
            locationId: req.locationId ? req.locationId : void 0 /* 场所ID */,
            groupId: req.groupId ? req.groupId : void 0 /* 设备组ID */,
            vendorId: req.vendorId ? req.vendorId : void 0 /* 供应商ID */,
            supportNoteId: req.supportNoteId ? req.supportNoteId : void 0 /* 行动策略ID */,
            resourceTypeId: req.resourceTypeId ? req.resourceTypeId : void 0 /* 资源类型ID */,
            alertClassificationId: req.alertClassificationId ? req.alertClassificationId : void 0 /* 告警分类ID */,
            active: req.active ? "true" : void 0 /* 是否激活 */,
            delivery: req.delivery ? "true" : void 0 /* 是否交付 */,
            ipAddress: req.ipAddress ? req.ipAddress : void 0 /* IP地址 */,
            fullQuery: req.fullQuery ? req.fullQuery : void 0,

            /* 场所名称 */
            ...([...(req.includeLocations instanceof Array ? req.includeLocations : []), ...(req.excludeLocations instanceof Array ? req.excludeLocations : [])].filter((v) => v).length ? { locationFilterRelation: req.locationFilterRelation === "OR" ? "OR" : "AND", includeLocations: req.includeLocations instanceof Array && req.includeLocations.length ? req.includeLocations.join(",") : void 0, excludeLocations: req.excludeLocations instanceof Array && req.excludeLocations.length ? req.excludeLocations.join(",") : void 0 } : {}),
            /* 设备名称或IP */
            ...([...(req.includeDevicesOrIps instanceof Array ? req.includeDevicesOrIps : []), ...(req.excludeDevicesOrIps instanceof Array ? req.excludeDevicesOrIps : [])].filter((v) => v).length ? { deviceOrIpFilterRelation: req.deviceOrIpFilterRelation === "OR" ? "OR" : "AND", includeDevicesOrIps: req.includeDevicesOrIps instanceof Array && req.includeDevicesOrIps.length ? req.includeDevicesOrIps.join(",") : void 0, excludeDevicesOrIps: req.excludeDevicesOrIps instanceof Array && req.excludeDevicesOrIps.length ? req.excludeDevicesOrIps.join(",") : void 0 } : {}),
            /* 网络监控系统设置 */
            ...([...(req.includeNmsSettings instanceof Array ? req.includeNmsSettings : []), ...(req.excludeNmsSettings instanceof Array ? req.excludeNmsSettings : []), ...(req.eqNmsSettings instanceof Array ? req.eqNmsSettings : []), ...(req.neNmsSettings instanceof Array ? req.neNmsSettings : [])].filter((v) => v).length
              ? {
                  nmsSettingsFilterRelation: req.nmsSettingsFilterRelation === "OR" ? "OR" : "AND",
                  includeNmsSettings: req.includeNmsSettings instanceof Array && req.includeNmsSettings.length ? req.includeNmsSettings.join(",") : void 0,
                  excludeNmsSettings: req.excludeNmsSettings instanceof Array && req.excludeNmsSettings.length ? req.excludeNmsSettings.join(",") : void 0,
                  eqNmsSettings: req.eqNmsSettings instanceof Array && req.eqNmsSettings.length ? req.eqNmsSettings.join(",") : void 0,
                  neNmsSettings: req.neNmsSettings instanceof Array && req.neNmsSettings.length ? req.neNmsSettings.join(",") : void 0,
                }
              : {}),
            /* 设备基本信息 */
            ...([...(req.includeDeviceBasicInfo instanceof Array ? req.includeDeviceBasicInfo : []), ...(req.excludeDeviceBasicInfo instanceof Array ? req.excludeDeviceBasicInfo : [])].filter((v) => v).length ? { deviceBasicInfoFilterRelation: req.deviceBasicInfoFilterRelation === "OR" ? "OR" : "AND", includeDeviceBasicInfo: req.includeDeviceBasicInfo instanceof Array && req.includeDeviceBasicInfo.length ? req.includeDeviceBasicInfo.join(",") : void 0, excludeDeviceBasicInfo: req.excludeDeviceBasicInfo instanceof Array && req.excludeDeviceBasicInfo.length ? req.excludeDeviceBasicInfo.join(",") : void 0 } : {}),
          },
          $req.params
        );
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_设备_可读, 资产管理中心_设备_新增, 资产管理中心_设备_编辑, 资产管理中心_设备_安全 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [req.queryPermissionId || 资产管理中心_设备_可读].join(","), verifyPermissionIds: [资产管理中心_设备_新增, 资产管理中心_设备_编辑, 资产管理中心_设备_安全].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<ResourcesItem[]>>($req)),
    { controller }
  );
}

export interface DeviceItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 安全容器 */
  containerId: /* Integer */ string;
  /** 资源池id */
  resourcePoolId: /* Integer */ string;
  /** 管理区编码 */
  precinctCode?: string;
  /** 设备型号 */
  deviceModel?: string;
  /** 模型标识 */
  modelIdent: string;
  /** 所在区域ID */
  regionId: /* Integer */ string;
  /** 所在场所ID */
  locationId: /* Integer */ string;
  /** 资源类型ID列表 */
  typeIds: /* Integer */ string[];
  /** 资源关联设备组ID列表 */
  groupIds: /* Integer */ string[];
  /** 服务商ID列表 */
  vendorIds: /* Integer */ string[];
  /** 行动策略列表 */
  supportNoteIds: /* Integer */ string[];
  /** 告警分类ID列表 */
  alertClassificationIds: /* Integer */ string[];
  /** 资产编号 */
  assetNumber?: string;
  /** 外部ID */
  externalId?: string;
  /** 资源名称 */
  name: string;
  /** 别名 */
  alias?: string;
  /** 资源上线时间 */
  resourceOnlineTime?: /* Integer */ string;
  /** 资源下线时间 */
  resourceOfflineTime?: /* Integer */ string;
  /** 是否交付 */
  delivery: boolean;
  /** 监控源 */
  monitorSources: string[];
  /** 业务单位 */
  unit?: string;
  /** 备注|描述信息 */
  description?: string;
  /** 时区 */
  timeZone?: string;
  /** 资源重要性 */
  importance: /* 枚举: High :至关重要的 | Medium :中 | Low :低 | None :无 | Unknown :未知的 */ "High" | "Medium" | "Low" | "None" | "Unknown";
  /** 标签列表 */
  tags: string[];
  /** 资源配置信息 */
  config: { [key: string]: string };
  /** 是否激活 */
  active: boolean;
  /** 最后上线时间 */
  onlineTime: /* Integer */ string;
  /** 最后离线时间 */
  offlineTime: /* Integer */ string;
  /** 服务包 */
  servicePackage?: string;
  /** 协议名 */
  protocolName?: string;
  /** 逻辑删除字段 */
  deleted: boolean;
  /** 用于解决唯一索引与逻辑删除冲突的字段 没有删除的都是0，删除后改为id的值 */
  deletedUniqueKey: /* Integer */ string;
  /** 变更版本号 */
  version: /* Integer */ string;
  /** 录入时间 */
  createdTime: /* Integer */ string;
  /** 最近变更时间 */
  updatedTime: /* Integer */ string;
  /** 录入人 */
  createdBy?: string;
  /** 最近变更人 */
  updatedBy?: string;
  /** SN序列号 */
  sn?: string;
  /** 链路编号 */
  linkNumber?: string;
  /** 所属客户id */
  customerId: /* Integer */ string;
  /** 所属客户名称 */
  customerName?: string;
  /** 安装地址（省） */
  province?: string;
  /** 安装地址（市） */
  city?: string;
  /** 安装地址（区域） */
  district?: string;
  /** 安装地址（详细地址） */
  installAddress?: string;
  /** 安装人姓名 */
  installerName?: string;
  /** 安装人电话 */
  installerPhone?: string;
  /** 设备联系人 */
  contactPerson?: string;
  /** 联系人电话 */
  contactPhone?: string;
  /** 所在区域名称 */
  regionDesc?: string;
  /** 地点名称 */
  locationDesc?: string;
  /** 资源池名称 */
  resourcePoolName: string;
  /** 资源类型 描述 */
  resourceTypes: ResourceTypeItem[];
  /** 服务商名称列表 */
  vendors: VendorsItem[];
  /** 告警分类列表 */
  alertClassifications: AlertClassificationsItem[];
  /** 设备分组列表 */
  groups: DeviceGroupItem[];
  /** 服务编号列表 */
  serviceNumbers: ServiceNumberItem[];
  /** 联系人列表 */
  contacts: ContactItem[];
  /** 登录认证详情 */
  loginAuths?: string;
  /** 拥有的权限 */
  hasPermissionIds: string[];
  /** 密码钱包列表 */
  passwordWallets: (PasswordWalletsSystemItem | PasswordWalletsDataBaseItem)[];
}
/**
 * @description 为当前租户创建资源
 * @url http://*************:3000/project/47/interface/api/2421
 */
export function /* 为当前租户创建资源 */ addDeviceData(req: { delivery: boolean; alias: string; resourceOnlineTime: string; resourceOfflineTime: string; modelIdent: string; regionId?: string; locationId?: string; typeIds?: string[]; groupIds?: string[]; vendorIds?: string[]; alertClassificationIds?: string[]; supportNoteIds?: string[]; assetNumber?: string; externalId?: string; name?: string; monitorSources?: DeviceItem["monitorSources"]; unit?: string; description?: string; timeZone?: string; importance?: DeviceItem["importance"]; tags?: string[]; config?: DeviceItem["config"]; active: boolean; containerId?: String } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { delivery: req.delivery, alias: req.alias, resourceOnlineTime: req.resourceOnlineTime, resourceOfflineTime: req.resourceOfflineTime, modelIdent: req.modelIdent /* 模型标识 */, regionId: req.regionId /* 所在区域ID */, locationId: req.locationId /* 所在场所ID */, typeIds: req.typeIds /* 资源类型ID列表 */, groupIds: req.groupIds /* 设备组ID列表 */, vendorIds: req.vendorIds /* 服务商ID列表 */, alertClassificationIds: req.alertClassificationIds /* 告警分类ID列表 */, supportNoteIds: req.supportNoteIds /* 行动策略ID列表 */, assetNumber: req.assetNumber /* 资产编号 */, externalId: req.externalId /* 外部ID */, name: req.name /* 资源名称 */, monitorSources: req.monitorSources /* 监控源 */, unit: req.unit /* 业务单位 */, description: req.description /* 备注|描述信息 */, timeZone: req.timeZone /* 时区 */, importance: req.importance /* 资源重要性枚举：High :至关重要的、Medium :中、Low :低、None :无、Unknown :未知的 */, tags: req.tags /* 标签列表 */, config: req.config /* 配置信息 */, active: req.active /* 是否激活 */, containerId: req.containerId };
  return request<never, Response<DeviceItem>>({ url: `${SERVER.CMDB}/resources/tenant/current`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 选择性更新资源信息
 * @url http://*************:3000/project/47/interface/api/2423
 */
export function /* 选择性更新资源信息 */ modDeviceData(req: Required<Record<"id", string>> & { delivery: boolean; alias: string; resourceOnlineTime: string; resourceOfflineTime: string; regionId?: string; locationId?: string; typeIds?: string[]; groupIds?: string[]; vendorIds?: string[]; alertClassificationIds?: string[]; supportNoteIds?: string[]; assetNumber?: string; externalId?: string; name?: string; monitorSources?: DeviceItem["monitorSources"]; unit?: string; description?: string; timeZone?: string; importance?: DeviceItem["importance"]; tags?: string[]; active?: boolean; config?: DeviceItem["config"] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = {
    /*  */
    delivery: req.delivery,
    alias: req.alias,
    resourceOnlineTime: req.resourceOnlineTime,
    resourceOfflineTime: req.resourceOfflineTime,
    regionId: req.regionId /* 区域ID, 选择性更新时如果想置空, 则传0 */,
    locationId: req.locationId /* 场所ID, 选择性更新时如果想置空, 则传0 */,
    typeIds: req.typeIds /* 资源类型ID列表 */,
    groupIds: (req.groupIds || []).concat((req as any).notPermissionGroupIds) /* 设备组ID列表 */,
    vendorIds: (req.vendorIds || []).concat((req as any).notPermissionVendorIds) /* 服务商ID列表 */,
    alertClassificationIds: (req.alertClassificationIds || []).concat((req as any).notPermissionAlertClassificationIds) /* 告警分类ID列表 */,
    supportNoteIds: req.supportNoteIds /* 行动策略ID列表 */,
    assetNumber: req.assetNumber /* 资产编号 */,
    externalId: req.externalId /* 外部ID */,
    name: req.name /* 资源名称 */,
    monitorSources: req.monitorSources /* 监控源 */,
    unit: req.unit /* 业务单位 */,
    description: req.description /* 备注|描述信息 */,
    timeZone: req.timeZone /* 时区 */,
    importance: req.importance /* 资源重要性枚举：High :至关重要的、Medium :中、Low :低、None :无、Unknown :未知的 */,
    tags: req.tags /* 标签列表 */,
    active: req.active /* 是否激活 */,
    config: req.config /* 选择性更新时, config中的属性也是逐个更新 */,
  };
  return request<never, Response<DeviceItem>>({ url: `${SERVER.CMDB}/resources/${req.id /* 资源ID */}`, method: Method.Patch, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 批量选择性更新资源信息
 * @url http://*************:3000/project/47/interface/api/2425
 */
export function /* 批量选择性更新资源信息 */ modDeviceList(req: Required<Record<"ids", string>> & { delivery: boolean; alias: string; resourceOnlineTime: string; resourceOfflineTime: string; regionId?: string; locationId?: string; typeIds?: string[]; groupIds?: string[]; vendorIds?: string[]; alertClassificationIds?: string[]; supportNoteIds?: string[]; assetNumber?: string; externalId?: string; name?: string; monitorSources?: DeviceItem["monitorSources"]; unit?: string; description?: string; timeZone?: string; importance?: DeviceItem["importance"]; tags?: string[]; active?: boolean; config?: DeviceItem["config"] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { delivery: req.delivery, alias: req.alias, resourceOnlineTime: req.resourceOnlineTime, resourceOfflineTime: req.resourceOfflineTime, regionId: req.regionId /* 区域ID, 选择性更新时如果想置空, 则传0 */, locationId: req.locationId /* 场所ID, 选择性更新时如果想置空, 则传0 */, typeIds: req.typeIds /* 资源类型ID列表 */, groupIds: req.groupIds /* 设备组ID列表 */, vendorIds: req.vendorIds /* 服务商ID列表 */, alertClassificationIds: req.alertClassificationIds /* 告警分类ID列表 */, supportNoteIds: req.supportNoteIds /* 行动策略ID列表 */, assetNumber: req.assetNumber /* 资产编号 */, externalId: req.externalId /* 外部ID */, name: req.name /* 资源名称 */, monitorSources: req.monitorSources /* 监控源 */, unit: req.unit /* 业务单位 */, description: req.description /* 备注|描述信息 */, timeZone: req.timeZone /* 时区 */, importance: req.importance /* 资源重要性枚举：High :至关重要的、Medium :中、Low :低、None :无、Unknown :未知的 */, tags: req.tags /* 标签列表 */, active: req.active /* 是否激活 */, config: req.config /* 选择性更新时, config中的属性也是逐个更新 */ };
  return request<never, Response<DeviceItem[]>>({ url: `${SERVER.CMDB}/resources/${req.ids /* 资源ID列表 使用,分割 */}/batch`, method: Method.Patch, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 删除资源
 * @url http://*************:3000/project/47/interface/api/2427
 */
export function /* 删除资源 */ delDeviceData(req: Required<Record<"id", string>> & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/x-www-form-urlencoded");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = new URLSearchParams({});
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/resources/${req.id /* 资源ID */}`, method: Method.Delete, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 批量删除资源
 * @url http://*************:3000/project/47/interface/api/2429
 */
export function /* 批量删除资源 */ delDeviceList(req: Required<Record<"ids", string>> & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/x-www-form-urlencoded");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = new URLSearchParams({});
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/resources/${req.ids /* 资源列表, 多个之间使用,分割 */}/batch`, method: Method.Delete, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 分页获取要导出的资源列表
 * @url http://*************:3000/project/47/interface/api/2453
 */
export function /* 分页获取要导出的资源列表 */ extDeviceList(
  req: {
    includeLocations: string[];
    excludeLocations: string[];
    includeDevicesOrIps: string[];
    excludeDevicesOrIps: string[];
    includeNmsSettings: string[];
    excludeNmsSettings: string[];
    includeDeviceBasicInfo: string[];
    excludeDeviceBasicInfo: string[];
  } & RequestBase
) {
  // const header = new Headers();
  const params = new URLSearchParams({ pageNumber: `${(req.paging || {}).pageNumber || 1}`, pageSize: `${(req.paging || {}).pageSize || 20}` });
  bindSearchParams(
    {
      name: req.name /* undefined */,
      modelIdent: req.modelIdent /* 模型标识 */,
      regionId: req.regionId /* 区域ID */,
      locationId: req.locationId /* 场所ID */,
      groupId: req.groupId /* 设备组ID */,
      vendorId: req.vendorId /* 供应商ID */,
      supportNoteId: req.supportNoteId /* 行动策略ID */,
      resourceTypeId: req.resourceTypeId /* 资源类型ID */,
      alertClassificationId: req.alertClassificationId /* 告警分类ID */,
      active: req.active /* 是否激活 */,
      locationFilterRelation: req.locationFilterRelation,
      deviceOrIpFilterRelation: req.deviceOrIpFilterRelation,
      nmsSettingsFilterRelation: req.nmsSettingsFilterRelation,
      deviceBasicInfoFilterRelation: req.deviceBasicInfoFilterRelation,
      includeLocations: req.includeLocations != undefined ? req.includeLocations.join() : [],
      excludeLocations: req.excludeLocations != undefined ? req.excludeLocations.join() : [],
      includeDevicesOrIps: req.includeDevicesOrIps != undefined ? req.includeDevicesOrIps.join() : [],
      excludeDevicesOrIps: req.excludeDevicesOrIps != undefined ? req.excludeDevicesOrIps.join() : [],
      includeNmsSettings: req.includeNmsSettings != undefined ? req.includeNmsSettings.join() : [],
      excludeNmsSettings: req.excludeNmsSettings != undefined ? req.excludeNmsSettings.join() : [],
      includeDeviceBasicInfo: req.includeDeviceBasicInfo != undefined ? req.includeDeviceBasicInfo.join() : [],
      excludeDeviceBasicInfo: req.excludeDeviceBasicInfo != undefined ? req.excludeDeviceBasicInfo.join() : [],
    },
    params
  );
  const data = new URLSearchParams({});
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_CMDB}/resources/export`,
    method: Method.Get,
    responseType: "blob",
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
    params,
    data,
  });
}
/**
 * @description 添加关联资源
 * @url http://*************:3000/project/47/interface/api/1107
 */
export function /* 添加关联资源 */ addResourcesByGroup(req: Required<Record<"id", string>> & { resourceIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { resourceIds: req.resourceIds /* 资源类型ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/groups/${req.id}/add/resources`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 删除关联资源
 * @url http://*************:3000/project/47/interface/api/1109
 */
export function /* 删除关联资源 */ delResourcesByGroup(req: Required<Record<"id", string>> & { resourceIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { resourceIds: req.resourceIds /* 资源类型ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/groups/${req.id}/remove/resources`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 添加关联资源
 * @url http://*************:3000/project/47/interface/api/1301
 */
export function /* 添加关联资源 */ addResourcesByVendor(req: Required<Record<"id", string>> & { resourceIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { resourceIds: req.resourceIds /* 资源类型ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/vendors/${req.id}/add/resources`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 删除关联资源
 * @url http://*************:3000/project/47/interface/api/1303
 */
export function /* 删除关联资源 */ delResourcesByVendor(req: Required<Record<"id", string>> & { resourceIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { resourceIds: req.resourceIds /* 资源类型ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/vendors/${req.id}/remove/resources`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 添加关联资源
 * @url http://*************:3000/project/47/interface/api/1251
 */
export function /* 添加关联资源 */ addResourcesByType(req: Required<Record<"id", string>> & { resourceIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { resourceIds: req.resourceIds /* 资源类型ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/resource_types/${req.id}/add/resources`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 删除关联资源
 * @url http://*************:3000/project/47/interface/api/1253
 */
export function /* 删除关联资源 */ delResourcesByType(req: Required<Record<"id", string>> & { resourceIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { resourceIds: req.resourceIds /* 资源类型ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/resource_types/${req.id}/remove/resources`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 添加关联资源
 * @url http://*************:3000/project/47/interface/api/1071
 */
export function /* 添加关联资源 */ addResourcesByClassifications(req: Required<Record<"id", string>> & { resourceIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { resourceIds: req.resourceIds /* 资源类型ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/alert_classifications/${req.id}/add/resources`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 删除关联资源
 * @url http://*************:3000/project/47/interface/api/1073
 */
export function /* 删除关联资源 */ delResourcesByClassifications(req: Required<Record<"id", string>> & { resourceIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { resourceIds: req.resourceIds /* 资源类型ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/alert_classifications/${req.id}/remove/resources`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 添加关联资源
 * @url http://*************:3000/project/47/interface/api/1281
 */
export function /* 添加关联资源 */ addResourcesByStrategy(req: Required<Record<"id", string>> & { resourceIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { resourceIds: req.resourceIds /* 资源类型ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/support_notes/${req.id}/add/resources`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 删除关联资源
 * @url http://*************:3000/project/47/interface/api/1283
 */
export function /* 删除关联资源 */ delResourcesByStrategy(req: Required<Record<"id", string>> & { resourceIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { resourceIds: req.resourceIds /* 资源类型ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/support_notes/${req.id}/remove/resources`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

/* TODO: 区域 */
export interface LocationItem {
  /** 主键 */
  id: string;
  /** 租户ID */
  tenantId: string;
  /** 安全容器id */
  containerId: string;
  /** 区域ID */
  regionId: string;
  /** 行动策略列表 */
  supportNoteIds: string[];
  /** 场所名称 */
  name: string;
  /** 描述 */
  description?: string;
  /** 时区Id */
  zoneId?: string;
  /** 外部ID */
  externalId?: string;
  /** 国家编码 */
  country?: string;
  /** 省级编码 */
  province?: string;
  /** 市级编码 */
  city?: string;
  /** 邮编代码 */
  postcode?: string;
  /** 地址 */
  address: string[];
  /** 是否激活 */
  active: boolean;
  /** 版本号 */
  version: string;
  /** 创建时间 */
  createdTime: string;
  /** 最近更新时间 */
  updatedTime: string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
  /** 拥有的权限 */
  hasPermissionIds: string[];
}

export function getQueryLocation(req: { containerId: string; queryPermissionId?: string; active: boolean; regionId?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/locations/queryLocationList`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ active: req.active }, $req.params);
        const [{ default: getUserInfo }, { 资产管理中心_场所_可读, 资产管理中心_场所_新增, 资产管理中心_场所_编辑, 资产管理中心_场所_删除 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
        const user = getUserInfo();
        for (let i = 0; i < user.tenants.length; i++) {
          if (user.currentTenantId === user.tenants[i].id) {
            bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
            break;
          }
        }
        bindParamByObj({ queryPermissionId: [req.queryPermissionId || 资产管理中心_场所_可读].join(","), verifyPermissionIds: [资产管理中心_场所_新增, 资产管理中心_场所_编辑, 资产管理中心_场所_删除].join(",") }, $req.params);

        bindParamByObj({ regionId: req.regionId }, $req.params);
        return $req;
      })
      .then(($req) => request<never, Response<{ online: boolean; resourceId: string }[]>>($req)),
    { controller }
  );
}

/**
 * @description 分页查询场所列表
 * @url http://*************:3000/project/47/interface/api/23115
 */
export function getLocationList(req: { paging: { pageNumber: number /* 页码, 默认第一页 */; pageSize: number /* 页大小, 默认10 */ }; sort?: string[]; queryPermissionId?: string /* 查询权限ID */; keyword?: string /* 模糊查询关键字, 名称/描述 */; regionId?: string /* 区域ID */; externalId?: string /* 外部ID */; supportNoteId?: string /* 行动策略ID */; active?: string /* 是否激活 */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/locations/2.0/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber /* 页码, 默认第一页 */, pageSize: req.paging.pageSize /* 页大小, 默认10 */, sort: req.sort, keyword: req.keyword /* 模糊查询关键字, 名称/描述 */, regionId: req.regionId /* 区域ID */, externalId: req.externalId /* 外部ID */, supportNoteId: req.supportNoteId /* 行动策略ID */, active: req.active /* 是否激活 */ }, $req.params);
        try {
          const [{ default: getUserInfo }, { 资产管理中心_场所_可读, 资产管理中心_场所_新增, 资产管理中心_场所_编辑, 资产管理中心_场所_删除 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [req.queryPermissionId || 资产管理中心_场所_可读].join(","), verifyPermissionIds: [资产管理中心_场所_新增, 资产管理中心_场所_编辑, 资产管理中心_场所_删除].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<LocationItem[]>>($req)),
    { controller }
  );
}

// /**
//  * @description 获取当前租户下场所列表
//  * @url http://*************:3000/project/47/interface/api/3741
//  */
// export function /* 获取当前租户下场所列表 */ getLocationList(req: Partial<Record<"keyword" | "regionId" | "externalId" | "supportNoteId", string>> & RequestBase) {
//   // const header = new Headers();
//   const params = new URLSearchParams({});
//   bindSearchParams({ keyword: req.keyword /* 模糊查询关键字, 名称/描述 */, regionId: req.regionId /* 区域ID */, externalId: req.externalId /* 外部ID */, supportNoteId: req.supportNoteId /* 行动策略ID */ }, params);
//   const data = new URLSearchParams({});
//   return request<never, Response<LocationItem[]>>({ url: `${SERVER.CMDB}/locations/tenant/current/list`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
// }
/**
 * @description 批量添加行动策略
 * @url http://*************:3000/project/47/interface/api/3749
 */
export function /* 批量添加行动策略 */ addLocationByStrategy(req: { ids: string[]; supportNoteIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { ids: req.ids /* ID列表 */, supportNoteIds: req.supportNoteIds /* 行动策略 ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/locations/support_note/add/batch`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 批量移除行动策略
 * @url http://*************:3000/project/47/interface/api/3751
 */
export function /* 批量移除行动策略 */ delLocationByStrategy(req: { ids: string[]; supportNoteIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { ids: req.ids /* ID列表 */, supportNoteIds: req.supportNoteIds /* 行动策略 ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/locations/support_note/remove/batch`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/* TODO: 场所 */
export interface RegionsItem {
  /** 主键 */
  id: string;
  /** 租户ID */
  tenantId: string;
  /** 安全容器 */
  containerId: string;
  /** 父区域Id */
  parentId: string;
  /** 子集 */
  children: RegionsItem[];
  /** 选择 */
  disabled?: boolean;
  /** 行动策略列表 */
  supportNoteIds: string[];
  label?: string;
  /** 区域名称 */
  name: string;
  /** 区域标识 */
  ident?: string;
  /** 描述信息 */
  description?: string;
  /** 外部ID */
  externalId?: string;
  /** 纬度 */
  latitude?: string;
  /** 经度 */
  longitude?: string;
  /** 是否激活 */
  active: boolean;
  /** 版本号 */
  version: string;
  /** 创建时间 */
  createdTime: string;
  /** 最近更新时间 */
  updatedTime: string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}
/**
 * @description 获取当前租户的所有区域
 * @url http://*************:3000/project/47/interface/api/1173
 */
export function /* 获取当前租户的所有区域 */ getRegionsList(req: { supportNoteId?: string; queryPermissionId?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/regions/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ supportNoteId: req.supportNoteId, active: true }, $req.params);
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_区域_可读, 资产管理中心_区域_新增, 资产管理中心_区域_编辑, 资产管理中心_区域_删除 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [req.queryPermissionId || 资产管理中心_区域_可读].join(","), verifyPermissionIds: [资产管理中心_区域_新增, 资产管理中心_区域_编辑, 资产管理中心_区域_删除].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<RegionsItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 批量添加行动策略
 * @url http://*************:3000/project/47/interface/api/1181
 */
export function /* 批量添加行动策略 */ addRegionByStrategy(req: { ids: string[]; supportNoteIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { ids: req.ids /* ID列表 */, supportNoteIds: req.supportNoteIds /* 行动策略 ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/regions/support_note/add/batch`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
/**
 * @description 批量移除行动策略
 * @url http://*************:3000/project/47/interface/api/1183
 */
export function /* 批量移除行动策略 */ delRegionByStrategy(req: { ids: string[]; supportNoteIds: string[] } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { ids: req.ids /* ID列表 */, supportNoteIds: req.supportNoteIds /* 行动策略 ID列表 */ };
  return request<never, Response<null>>({ url: `${SERVER.CMDB}/regions/support_note/remove/batch`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function addModelAlram(req: {} & RequestBase) {
  const params = new URLSearchParams({});

  return request<never, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/integration/net_care/alert/mock`,
    method: Method.Post,

    signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
    params,
    data: req,
  });
}

export function getDeviceDiscovery(req: {} & RequestBase) {
  const params = new URLSearchParams({});

  return request<never, Response<object[]>>({
    url: `${SERVER.CMDB}/resources/${req.id}/batch_get/discovery`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
    params,
    data: {},
  });
}

export function getTuopu(req: { containerId: string; queryPermissionId: string } & RequestBase) {
  const params = new URLSearchParams({});
  return request<never, Response<object[]>>({
    url: `${SERVER.CMDB}/topology/show`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
    params: {
      queryPermissionId: req.queryPermissionId,
      containerId: req.containerId,
    },
    data: {},
  });
}
export function desktop(req: { deviceIds: string } & RequestBase) {
  const params = new URLSearchParams({});

  return request<never, Response<{ resourceId: /** 资源id/设备id */ string; icoShow: /** 是否显示图标 */ boolean; success: /** 设备映射关系是否找到 */ boolean; allowTypes: /** 支持的远程桌面类型 */ string[] }[]>>({
    url: `${SERVER.CMDB}/netCare6/remote_desktop`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
    params: {
      deviceIds: req.deviceIds,
    },
    data: {},
  });
}
export function desktopbatch(req: { deviceIds: string; ids: string[] } & RequestBase) {
  const params = new URLSearchParams({});

  return request<never, Response<{ resourceId: /** 资源id/设备id */ string; icoShow: /** 是否显示图标 */ boolean; success: /** 设备映射关系是否找到 */ boolean; allowTypes: /** 支持的远程桌面类型 */ string[] }[]>>({
    url: `${SERVER.CMDB}/netCare6/remote_desktop/batch`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
    params: {},
    data: {
      stringIds: req.deviceIds,
      ids: req.ids,
    },
  });
}
/**
 * @description 批量获取设备属性发现发现的设备型号、序列号
 * @url http://*************:3000/project/47/interface/api/7883
 */
export function getDiscoveryByResources(req: { ids: string[] }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/resources/${req.ids.join(",")}/batch_get/discovery`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => $req)
      .then(($req) => request<never, Response<{ modelName: string; resourceId: string; serialNumber: string }[]>>($req)),
    { controller }
  );
}

/**
 * @description 远程桌面对接：列表上小电脑是否展示、展示哪些
 * @url http://*************:3000/project/47/interface/api/9935
 */
export function getRemoteDesktopByResources(req: { deviceIds: string[] /* 设备id，多个用逗号隔开 */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/netCare6/remote_desktop`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ deviceIds: req.deviceIds.join(",") }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, Response<{ resourceId: string; /** 是否显示图标 */ icoShow: boolean; /** 设备映射关系是否找到 */ success: boolean; /** 支持的远程桌面类型 */ allowTypes: string[] }[]>>($req)),
    { controller }
  );
}

/**
 * @description 批量获取NetCare设备是否在线信息-响应体
 * @url http://*************:3000/project/47/interface/api/25177
 */
export type ResourcesOnlineResData = [];

/**
 * @description 批量获取NetCare设备是否在线信息
 * @url http://*************:3000/project/47/interface/api/25177
 */
export function getRemoteOnlineByResources(req: { ids: string[] }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/netCare6/resources/online`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = { ids: req.ids instanceof Array ? req.ids.join(",") : "" };
        return $req;
      })
      .then(($req) => request<never, Response<{ online: boolean; resourceId: string }[]>>($req)),
    { controller }
  );
}

// 设备管理用于查询区域和场所
export function /* 获取当前租户的所有区域 */ getNewRegionsList(req: { supportNoteId?: string; queryPermissionId?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/regions/tenant/resource`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ supportNoteId: req.supportNoteId, active: true }, $req.params);
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_区域_可读, 资产管理中心_区域_新增, 资产管理中心_区域_编辑, 资产管理中心_区域_删除 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [req.queryPermissionId || 资产管理中心_区域_可读].join(","), verifyPermissionIds: [资产管理中心_区域_新增, 资产管理中心_区域_编辑, 资产管理中心_区域_删除].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<RegionsItem[]>>($req)),
    { controller }
  );
}

export function getNewQueryLocation(req: { containerId: string; queryPermissionId?: string; active: boolean; regionId?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/locations/queryResourceLocationList`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ active: req.active }, $req.params);
        const [{ default: getUserInfo }, { 资产管理中心_场所_可读, 资产管理中心_场所_新增, 资产管理中心_场所_编辑, 资产管理中心_场所_删除 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
        const user = getUserInfo();
        for (let i = 0; i < user.tenants.length; i++) {
          if (user.currentTenantId === user.tenants[i].id) {
            bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
            break;
          }
        }
        bindParamByObj({ queryPermissionId: [req.queryPermissionId || 资产管理中心_场所_可读].join(","), verifyPermissionIds: [资产管理中心_场所_新增, 资产管理中心_场所_编辑, 资产管理中心_场所_删除].join(",") }, $req.params);

        bindParamByObj({ regionId: req.regionId }, $req.params);
        return $req;
      })
      .then(($req) => request<never, Response<{ online: boolean; resourceId: string }[]>>($req)),
    { controller }
  );
}

export function setRemoteLoginLog(req: {} & RequestBase) {
  const params = new URLSearchParams({});
  bindParamByObj({ connectId: req.connectId }, params);
  return request<never, Response<object[]>>({
    url: `${SERVER.CMDB}/resources/business/seletResourceWsLog`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
    params,
    data: {},
  });
}
