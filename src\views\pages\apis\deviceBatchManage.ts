import { SERVER, Method, type Response, type RequestBase, bindSearchParams, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";
import axios from "axios"; // 关键代码
const CancelToken = axios.CancelToken; // 关键代码
export interface SlaConfigList {
  id: string;
  name: string;
  config: {
    ipAddress: string;
  };

  tenantId: string;
}

//下载模板
export function downloadDeviceBatchModelFile(data: { file: File } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/templates/customer-template.xlsx`,
    method: Method.Get,
    signal: undefined,
    headers: {},
    params: {},
    data: {},
    responseType: "blob",
  });
}

/**
 * @description 导入客户信息列表-响应体
 * @url http://*************:3000/project/71/interface/api/2469
 */
export interface ImportResourceResultItem {
  sheetMapHeadIndex: Record<number, number[]>;
  tenantId?: string;
  modelIdent?: string;
  tenantArgs?: { row: number; name: /* 客户名称 */ string; address?: /* 客户地址 */ string; contactAndMobile?: /* 联系人/电话 */ string; platformVersion?: /* 平台版本 */ string; abbreviation: /* 辅助 客户缩写 */ string; language?: /* 语言 */ string; tenantNature?: /* 客户性质 */ string; tenantIndustry?: /* 客户行业 */ string; tenantSignAddress?: /* 客户签约地 */ string };
  /** 网络基本信息 */
  basicNetworkArgs?: { row: number; networkSegment?: /* 客户网管地址段 */ string; networkAddress?: /* 客户网络地址段 */ string }[];
  /** 响应策略信息 */
  supportNoteArgs?: { row: number; supportNoteName: /* 响应策略名称(500个字内） */ string; standardSupportNote?: /* 标准（7*24）响应策略（1800个字内） */ string; workTimeSupportNote?: /* 工作时段响应策略（1800个字内） */ string; nonWorkTimeSupportNote?: /* 非工作时段响应策略（1800个字内） */ string; weekendSupportNote?: /* 周末响应策略（1800个字内） */ string }[];
  /** 资源和地址信息 */
  resourceAndAddressArgs?: { row: number; resourceName: /* 设备名称 */ string; location?: /* 所在地名称 */ string; region?: /* 区域 */ string; secondRegion?: /* 二级区域 */ string; thirdRegion?: /* 三级区域 */ string; unit?: /* 业务单位 */ string; supportNote?: /* 响应策略 */ string; deviceType?: /* 设备类型 */ string; importantLevel?: /* 重要级别 */ string; deviceConfig?: /* 设备配置 */ string; deviceVendor?: /* 设备商 */ string; deviceIp?: /* 设备网管IP */ string; managePort?: /* 被管理端口 */ string; deviceModel?: /* 设备型号 */ string; deviceSerialNumber?: /* 设备序列号 */ string; iosVersion?: /* IOS版本 */ string; loginUserNameAndPassword?: /* 登录用户名和密码 */ string; enableUserNameAndPassword?: /* 使能用户名和密码 */ string; superUserNameAndPassword?: /* 超级用户名和密码 */ string; operatingSystem?: /* 操作系统 */ string; serviceNumber?: /* 线路编号 */ string; lineBandwidth?: /* 线路带宽 */ string; lineType?: /* 线路类型 */ string; operatorPhone?: /* 运营商（报障电话） */ string; servicePackage?: /* 服务包 */ string; protocolName?: /* 协议名 */ string }[];
  /** 联系人信息 */
  contactArgs?: { row: number; locationName: /* 所在地名称 */ string; detailAddress?: /* 详细地址 */ string; localContact?: /* 当地联系人 */ string; position?: /* 职务 */ string; fixedPhone?: /* 固定电话 */ string; mobilePhone?: /* 移动电话 */ string; afterWorkPhone?: /* 下班后联系电话 */ string; fax?: /* 传真 */ string; email?: /* 邮件 */ string; remark?: /* 备注 */ string }[];
  /** 升级联系人信息 */
  contactUpdateArgs?: { row: number; resourceName: /* 设备名称 */ string; updateContact: /* 升级联系人 */ string; position?: /* 职务 */ string; updateContactAddress?: /* 升级联系人地址 */ string; fixedPhone?: /* 固定电话 */ string; mobilePhone?: /* 移动电话 */ string; afterWorkPhone?: /* 下班后联系电话 */ string; fax?: /* 传真 */ string; email?: /* 邮件 */ string; remark?: /* 备注 */ string }[];
  /** 简要协议信息 */
  protocolSummaryArgs?: { row: number; name: /* 名称 */ string; protocol?: /* 协议 */ string; description?: /* 描述 */ string }[];
  /** 校验错误信息 */
  errors: { commentsMsg?: /* 批注信息 */ string; value?: /* 字段值 */ Record<string, string>; sheetNo?: /* sheet页号 */ number; row: /* 行号 */ number; col: /* 列号 */ number }[];
  /** 错误文件路径 */
  errFilePath: string;
  /** 租户信息 */
  tenant: { id?: string /* 租户id */; containerId?: string /* 安全容器id */; name?: string /* 租户名称 */; abbreviation?: string /* 租户缩写 */; language?: string /* 语言 */; address?: string /* 地址 */; note?: string /* 备注信息 */; zoneId?: string /* 时区ID */; blocked?: boolean /* 是否已被冻结 */; signAddress?: string /* 客户签约地 */; tenantNature?: string /* 客户性质 */; tenantIndustry?: string /* 客户行业 */; systemEdition?: string /* 系统版本 */; tenantType?: string /* 客户类型 */ };
  /** 是否是新客户,默认: false */
  newTenant?: boolean;
}

/**
 * @description 导入客户信息列表
 * @url http://*************:3000/project/71/interface/api/2469
 */
export function uploadResourceByFile(req: { file: File[] } & { abbreviation: string } & { onUploadProgress?: (progress: import("axios").AxiosProgressEvent) => void }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CC_REPORT}/cmdb/tenants/import`, method: Method.Post, responseType: "json", timeout: 0, signal: controller.signal, onUploadProgress: req.onUploadProgress })
      .then(async ($req) => {
        $req.params = new URLSearchParams({ abbreviation: req.abbreviation });
        $req.data = new FormData();
        bindParamByObj({ file: req.file }, $req.data);
        return $req;
      })
      .then(($req) => request<never, Response<ImportResourceResultItem>>($req)),
    { controller }
  );
}

//批量设备列表
export function deviceBatchList(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/tenant_import_his/page`,
    method: Method.Get,
    signal: undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//删除导入记录
export function deleteBatchItem(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/tenant_import_his/${data.id}`,
    method: Method.Delete,
    signal: undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//下载导入错误信息文件
export function downloadFailFileMessage(data: { file: File } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/files/download`,
    method: Method.Get,
    signal: undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

//获取租户列表
export function getTenantList(data: RequestBase & RequestBase) {
  const params = new URLSearchParams();
  bindSearchParams(data, params);
  bindSearchParams({ permissionId: ["513160392680144896"].join(",") }, params);
  return request<unknown, unknown>({
    url: `${SERVER.IAM}/tenants/platform/current`,
    method: Method.Get,
    signal: undefined,
    headers: {},
    params,
    data: {},
  });
}

//获取租户下的用户组
export function getUserGroups(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.IAM}/user_groups/findByTenantIdAndPermissionId?tenantId=${data.tenantId}&permissionId=512890964822458368`,
    // url: `${SERVER.IAM}/tenants/${data.tenantId}/user_groups`,
    method: Method.Get,
    signal: undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//邀请租户成员
export function jionUserGroups(data: RequestBase & RequestBase, id: string) {
  return request<unknown, unknown>({
    url: `${SERVER.IAM}/tenants/${id}/invite`,
    method: Method.Post,
    signal: undefined,
    headers: {},
    params: {},
    data: data,
  });
}
