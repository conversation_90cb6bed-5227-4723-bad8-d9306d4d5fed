import { SERVER, Method, type Response, type RequestBase, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";
import { deviceImportance } from "./device";
import { 资产管理中心_设备_可读 } from "@/views/pages/permission";
export interface deviceGroupItem {
  id: string /* 主键 */;
  tenantId: string /* 租户ID */;
  name: string /* 名称 */;
  description?: string /* 描述信息 */;
  report: boolean /* 是否为报告分组 */;
  alertClassificationIds: /* 告警分类ID列表 */ string[];
  version: string /* 版本号 */;
  createdTime: string /* 创建时间 */;
  updatedTime: string /* 最近更新时间 */;
  createdBy?: string /* 创建人 */;
  updatedBy?: string /* 最后更新人 */;
}
export enum OrderType {
  EVENT_ORDER = "事件",
  SERVICE_REQUEST = "服务请求",
  CHANGE = "变更",
  QUESTION = "问题",
  PUBLISH = "发布",
  DICT_EVENT_ORDER = "DICT事件",
  DICT_SERVICE_REQUEST = "DICT服务请求",
}
export enum OrderTypes {
  EVENT_ORDER = "事件",
  SERVICE_REQUEST = "服务请求",
  CHANGE = "变更",
  QUESTION = "问题",
  PUBLISH = "发布",
  // DICT_EVENT_ORDER = "DICT事件",
  // DICT_SERVICE_REQUEST = "DICT服务请求",
}
export enum DataType {
  EVENT_ORDER = "EVENT_ORDER",
  SERVICE_REQUEST = "SERVICE_REQUEST",
  DICT_EVENT_ORDER = "DICT_EVENT_ORDER",
  DICT_SERVICE_REQUEST = "DICT_SERVICE_REQUEST",
  CHANGE = "CHANGE",
  QUESTION = "QUESTION",
  PUBLISH = "PUBLISH",
}

/**
 * @description 获取当前租户下所有设备组-query请求参数
 * @url http://*************:3000/project/47/interface/api/23136
 */
export interface TenantCurrentReqQuery {
  alertClassificationId?: string;
  containerId?: string;
  queryPermissionId?: string;
  verifyPermissionIds?: string;
}

/**
 * @description 设备组
 */
export interface DeviceGroupItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 名称 */
  name: string;
  /** 描述信息 */
  description?: string;
  /** 是否为报告分组 */
  report: boolean;
  /** 告警分类ID列表 */
  alertClassificationIds: /* Integer */ string[];
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}

/**
 * @description 获取当前租户下所有设备组
 * @url http://*************:3000/project/47/interface/api/23136
 */
export async function getdeviceGroupList(req: { alertClassificationId: string; containerId: string; queryPermissionId: string; verifyPermissionIds: string } & Record<string, any>) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/groups/2.0/tenant/current/filter`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        bindParamByObj({ alertClassificationId: req.alertClassificationId }, $req.params);

        bindParamByObj(
          {
            ...([...(req.includeName instanceof Array ? req.includeName : []), ...(req.excludeName instanceof Array ? req.excludeName : []), ...(req.eqName instanceof Array ? req.eqName : []), ...(req.neName instanceof Array ? req.neName : [])].filter((v) => v).length ? { nameFilterRelation: req.nameFilterRelation === "OR" ? "OR" : "AND", includeName: req.includeName instanceof Array && req.includeName.length ? req.includeName.join(",") : void 0, excludeName: req.excludeName instanceof Array && req.excludeName.length ? req.excludeName.join(",") : void 0, eqName: req.eqName instanceof Array && req.eqName.length ? req.eqName.join(",") : void 0, neName: req.neName instanceof Array && req.neName.length ? req.neName.join(",") : void 0 } : {}),

            ...([...(req.includeDescription instanceof Array ? req.includeDescription : []), ...(req.excludeDescription instanceof Array ? req.excludeDescription : []), ...(req.eqDescription instanceof Array ? req.eqDescription : []), ...(req.neDescription instanceof Array ? req.neDescription : [])].filter((v) => v).length ? { descriptionFilterRelation: req.descriptionFilterRelation === "OR" ? "OR" : "AND", includeDescription: req.includeDescription instanceof Array && req.includeDescription.length ? req.includeDescription.join(",") : void 0, excludeDescription: req.excludeDescription instanceof Array && req.excludeDescription.length ? req.excludeDescription.join(",") : void 0, eqDescription: req.eqDescription instanceof Array && req.eqDescription.length ? req.eqDescription.join(",") : void 0, neDescription: req.neDescription instanceof Array && req.neDescription.length ? req.neDescription.join(",") : void 0 } : {}),

            ...([...(req.includeAlertClassificationName instanceof Array ? req.includeAlertClassificationName : []), ...(req.excludeAlertClassificationName instanceof Array ? req.excludeAlertClassificationName : []), ...(req.eqAlertClassificationName instanceof Array ? req.eqAlertClassificationName : []), ...(req.neAlertClassificationName instanceof Array ? req.neAlertClassificationName : [])].filter((v) => v).length ? { alertClassificationNameFilterRelation: req.alertClassificationNameFilterRelation === "OR" ? "OR" : "AND", includeAlertClassificationName: req.includeAlertClassificationName instanceof Array && req.includeAlertClassificationName.length ? req.includeAlertClassificationName.join(",") : void 0, excludeAlertClassificationName: req.excludeAlertClassificationName instanceof Array && req.excludeAlertClassificationName.length ? req.excludeAlertClassificationName.join(",") : void 0, eqAlertClassificationName: req.eqAlertClassificationName instanceof Array && req.eqAlertClassificationName.length ? req.eqAlertClassificationName.join(",") : void 0, neAlertClassificationName: req.neAlertClassificationName instanceof Array && req.neAlertClassificationName.length ? req.neAlertClassificationName.join(",") : void 0 } : {}),

            report: req.report,
          },
          $req.params
        );

        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_设备分组_可读, 资产管理中心_设备分组_新增, 资产管理中心_设备分组_编辑, 资产管理中心_设备分组_删除, 资产管理中心_设备分组_安全 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [资产管理中心_设备分组_可读].join(","), verifyPermissionIds: [资产管理中心_设备分组_新增, 资产管理中心_设备分组_编辑, 资产管理中心_设备分组_删除, 资产管理中心_设备分组_安全].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<DeviceGroupItem[]>>($req)),
    { controller }
  );
}

//新增设备分组
export function adddeviceGroup(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<deviceGroupItem[]>>({
    url: `${SERVER.CMDB}/groups/tenant/current`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//编辑设备分组
export function editdeviceGroup(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<deviceGroupItem[]>>({
    url: `${SERVER.CMDB}/groups/${data.id}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//删除设备分组
export function deletedeviceGroup(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<deviceGroupItem[]>>({
    url: `${SERVER.CMDB}/groups/${data.id}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//
//
//
//
//
//
//

/**
 * @description 获取资源类型列表-响应体
 * @url http://*************:3000/project/47/interface/api/23150
 */
export interface DeviceTypeItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 类型名称 */
  name: string;
  /** 类型英文名称 */
  enName?: string;
  /** 描述 */
  description?: string;
  /** 告警分类ID列表 */
  alertClassificationIds: /* Integer */ string[];
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
  /** 拥有的权限 */
  hasPermissionIds: /* Integer */ string[];
}

/**
 * @description 获取资源类型列表
 * @url http://*************:3000/project/47/interface/api/23150
 */
export async function getdeviceTypeList(req: { alertClassificationId: string } & Record<string, any>) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/resource_types/2.0/filter`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        bindParamByObj({ alertClassificationId: req.alertClassificationId }, $req.params);

        bindParamByObj(
          {
            ...([...(req.includeName instanceof Array ? req.includeName : []), ...(req.excludeName instanceof Array ? req.excludeName : []), ...(req.eqName instanceof Array ? req.eqName : []), ...(req.neName instanceof Array ? req.neName : [])].filter((v) => v).length ? { nameFilterRelation: req.nameFilterRelation === "OR" ? "OR" : "AND", includeName: req.includeName instanceof Array && req.includeName.length ? req.includeName.join(",") : void 0, excludeName: req.excludeName instanceof Array && req.excludeName.length ? req.excludeName.join(",") : void 0, eqName: req.eqName instanceof Array && req.eqName.length ? req.eqName.join(",") : void 0, neName: req.neName instanceof Array && req.neName.length ? req.neName.join(",") : void 0 } : {}),

            ...([...(req.includeEnName instanceof Array ? req.includeEnName : []), ...(req.excludeEnName instanceof Array ? req.excludeEnName : []), ...(req.eqEnName instanceof Array ? req.eqEnName : []), ...(req.neEnName instanceof Array ? req.neEnName : [])].filter((v) => v).length ? { enNameFilterRelation: req.enNameFilterRelation === "OR" ? "OR" : "AND", includeEnName: req.includeEnName instanceof Array && req.includeEnName.length ? req.includeEnName.join(",") : void 0, excludeEnName: req.excludeEnName instanceof Array && req.excludeEnName.length ? req.excludeEnName.join(",") : void 0, eqEnName: req.eqEnName instanceof Array && req.eqEnName.length ? req.eqEnName.join(",") : void 0, neEnName: req.neEnName instanceof Array && req.neEnName.length ? req.neEnName.join(",") : void 0 } : {}),

            ...([...(req.includeDescription instanceof Array ? req.includeDescription : []), ...(req.excludeDescription instanceof Array ? req.excludeDescription : []), ...(req.eqDescription instanceof Array ? req.eqDescription : []), ...(req.neDescription instanceof Array ? req.neDescription : [])].filter((v) => v).length ? { descriptionFilterRelation: req.descriptionFilterRelation === "OR" ? "OR" : "AND", includeDescription: req.includeDescription instanceof Array && req.includeDescription.length ? req.includeDescription.join(",") : void 0, excludeDescription: req.excludeDescription instanceof Array && req.excludeDescription.length ? req.excludeDescription.join(",") : void 0, eqDescription: req.eqDescription instanceof Array && req.eqDescription.length ? req.eqDescription.join(",") : void 0, neDescription: req.neDescription instanceof Array && req.neDescription.length ? req.neDescription.join(",") : void 0 } : {}),

            ...([...(req.includeAlertClassificationName instanceof Array ? req.includeAlertClassificationName : []), ...(req.excludeAlertClassificationName instanceof Array ? req.excludeAlertClassificationName : []), ...(req.eqAlertClassificationName instanceof Array ? req.eqAlertClassificationName : []), ...(req.neAlertClassificationName instanceof Array ? req.neAlertClassificationName : [])].filter((v) => v).length ? { alertClassificationNameFilterRelation: req.alertClassificationNameFilterRelation === "OR" ? "OR" : "AND", includeAlertClassificationName: req.includeAlertClassificationName instanceof Array && req.includeAlertClassificationName.length ? req.includeAlertClassificationName.join(",") : void 0, excludeAlertClassificationName: req.excludeAlertClassificationName instanceof Array && req.excludeAlertClassificationName.length ? req.excludeAlertClassificationName.join(",") : void 0, eqAlertClassificationName: req.eqAlertClassificationName instanceof Array && req.eqAlertClassificationName.length ? req.eqAlertClassificationName.join(",") : void 0, neAlertClassificationName: req.neAlertClassificationName instanceof Array && req.neAlertClassificationName.length ? req.neAlertClassificationName.join(",") : void 0 } : {}),
          },
          $req.params
        );

        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_设备类型_可读, 资产管理中心_设备类型_新增, 资产管理中心_设备类型_编辑, 资产管理中心_设备类型_删除, 资产管理中心_设备类型_安全 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [资产管理中心_设备类型_可读].join(","), verifyPermissionIds: [资产管理中心_设备类型_新增, 资产管理中心_设备类型_编辑, 资产管理中心_设备类型_删除, 资产管理中心_设备类型_安全].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<DeviceTypeItem[]>>($req)),
    { controller }
  );
}

//设备类型列表
// export function getdeviceTypeList(data: { pageNumber: number; pageSize: number } & RequestBase) {
//   return request<never, Response<DeviceTypeItem[]>>({
//     url: `${SERVER.CMDB}/resource_types`,
//     method: Method.Get,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: data,
//     data: {},
//   });
// }

//新增设备类型
export function adddeviceType(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<deviceGroupItem[]>>({
    url: `${SERVER.CMDB}/resource_types`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//编辑设备类型
export function editdeviceType(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<deviceGroupItem[]>>({
    url: `${SERVER.CMDB}/resource_types/${data.id}`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//删除设备类型
export function deletedeviceType(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<deviceGroupItem[]>>({
    url: `${SERVER.CMDB}/resource_types/${data.id}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//设备管理

//添加设备
export function addDevice(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<deviceGroupItem[]>>({
    url: `${SERVER.CMDB}/resources/tenant/current`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//编辑设备
export function editDevice(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<deviceGroupItem[]>>({
    url: `${SERVER.CMDB}/resources/${data.id}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//删除设备
export function deleteDevice(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<deviceGroupItem[]>>({
    url: `${SERVER.CMDB}/resources/${data.id}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export interface DeviceList {
  id: string /* 主键 */;
  tenantId: string /* 租户ID */;
  modelIdent: string /* 模型标识 */;
  regionId: string /* 所在区域ID */;
  locationId: string /* 所在场所ID */;
  typeIds: /* 资源类型ID列表 */ string[];
  groupIds: /* 资源关联设备组ID列表 */ string[];
  vendorIds: /* 服务商ID列表 */ string[];
  supportNoteIds: /* 行动策略列表 */ string[];
  alertClassificationIds: /* 告警分类ID列表 */ string[];
  assetNumber?: string /* 资产编号 */;
  externalId?: string /* 外部ID */;
  name: string /* 资源名称 */;
  monitorSources: /* 监控源 */ string[];
  unit?: string /* 业务单位 */;
  description?: string /* 备注|描述信息 */;
  timeZone?: string /* 时区 */;
  importance: deviceImportance /* 资源重要性 枚举类型: High :至关重要的 | Medium :中 | Low :低 | None :无 | Unknown :未知的 */;
  tags: /* 标签列表 */ string[];
  config: /* 资源配置信息 */ { [key: string]: string };
  active: boolean /* 是否激活 */;
  onlineTime: string /* 最后上线时间 */;
  offlineTime: string /* 最后离线时间 */;
  version: string /* 变更版本号 */;
  createdTime: string /* 录入时间 */;
  updatedTime: string /* 最近变更时间 */;
  createdBy?: string /* 录入人 */;
  updatedBy?: string /* 最近变更人 */;
  regionDesc?: string /* 所在区域名称 */;
  locationDesc?: string /* 场所名称 */;
  resourceTypes: /* 资源类型 描述 */ {
    id: string /* 主键 */;
    name: string /* 类型名称 */;
    description?: string /* 描述 */;
    alertClassificationIds: /* 告警分类ID列表 */ string[];
    version: string /* 版本号 */;
    createdTime: string /* 创建时间 */;
    updatedTime: string /* 最近更新时间 */;
    createdBy?: string /* 创建人 */;
    updatedBy?: string /* 最后更新人 */;
  }[];
  vendors: /* 服务商名称列表 */ {
    id: string /* 主键 */;
    name: string /* 名称 */;
    description?: string /* 描述 */;
    landlinePhone?: string /* 固定电话 */;
    supportPhone?: string /* 支持电话 */;
    contactName?: string /* 联系人姓名 */;
    email?: string /* 电子邮箱 */;
    version: string /* 版本号 */;
    createdTime: string /* 创建时间 */;
    updatedTime: string /* 最近更新时间 */;
    createdBy?: string /* 创建人 */;
    updatedBy?: string /* 最后更新人 */;
  }[];
  alertClassifications: /* 告警分类列表 */ {
    id: string /* 主键 */;
    name: string /* 分类名称 */;
    description?: string /* 描述信息 */;
    version: string /* 版本号 */;
    createdTime: string /* 创建时间 */;
    updatedTime: string /* 最近更新时间 */;
    createdBy?: string /* 创建人 */;
    updatedBy?: string /* 最后更新人 */;
  }[];
  groups: /* 设备分组列表 */ deviceGroupItem[];
  serviceNumbers: /* 服务编号列表 */ {
    id: string /* 主键 */;
    resourceId: string /* 资源ID */;
    number: string /* 服务编号 */;
    vendorIds?: /* 供应商列表 */ string[];
    type?: string /* 类型 */;
    progress?: string /* 进度 */;
    product?: string /* 产品 */;
    description?: string /* 描述信息 */;
  }[];
  contacts: /* 联系人列表 */ {
    id: string /* 主键 */;
    tenantId: string /* 租户ID */;
    title?: string /* 头衔 */;
    name: string /* 姓名 */;
    language?: string /* 语言 */;
    email?: string /* 邮箱地址 */;
    landlinePhone?: string /* 固定电话 */;
    mobilePhone?: string /* 移动电话 */;
    afterWorkPhone?: string /* 下班后联系电话 */;
    smsPhone?: string /* 短信号码 */;
    fax?: string /* 传真 */;
    smsEnabled: boolean /* 是否启用短信 */;
    vip: boolean /* 是否VIP */;
    note?: string /* 备注 */;
    zoneId?: string /* 时区ID */;
    externalId?: string /* 外部ID */;
    version: string /* 版本号 */;
    createdTime: string /* 创建时间 */;
    updatedTime: string /* 最近更新时间 */;
    createdBy?: string /* 创建人 */;
    updatedBy?: string /* 最后更新人 */;
  }[];
}
//获取设备
export function getDeviceList(data: { pageNumber: number; pageSize: number; name?: string } & RequestBase) {
  return request<never, Response<DeviceList[]>>({
    url: `${SERVER.CMDB}/resources/tenant/current/desensitized`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function getDictDeviceIdByProjectId(data: { id: string; projectId: string } & RequestBase) {
  return request<never, Response<DeviceList[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/prod/${data.projectId}/getDeviceIds`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//获取设备
export function getDeviceQuery(data: { pageNumber: number; pageSize: number; name?: string } & RequestBase) {
  return request<never, Response<DeviceList[]>>({
    url: `${SERVER.CMDB}/resources/queryResourceList`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: Object.assign(data, { queryPermissionId: data.queryPermissionId || 资产管理中心_设备_可读 }),
    data: {},
  });
}

//获取设备详情
export function getDeviceDetaile(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<deviceGroupItem[]>>({
    url: `${SERVER.CMDB}/resources/${data.id}/detail`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
export function getLocationsDetaile(data: { id: string } & RequestBase) {
  return request<never, Response<deviceGroupItem[]>>({
    url: `${SERVER.CMDB}/locations/${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//设置资源联系人

export function setRegionsContacts(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/resources/${id}/contacts`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//获取资源联系人
export function getRegionsContacts(data: { id: string } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/resources/${data.id}/contacts`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//删除资源联系人
export function delRegionsContacts(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/resources/${id}/contacts`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//获取服务编号
export function getServiceList(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<deviceGroupItem[]>>({
    url: `${SERVER.CMDB}/service_numbers/detail`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//新增服务编号
export function addService(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<deviceGroupItem[]>>({
    url: `${SERVER.CMDB}/service_numbers`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//修改服务编号

export function setService(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/service_numbers/${data.id}`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//查看服务编号详情
export function getServiceDetails(data: { id: string } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/service_numbers/${data.id}/detail`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//删除服务编号
export function delService(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/service_numbers/${data.id}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//导出设备列表
export function exportDevice(data: RequestBase, id?: string) {
  return request<unknown, Response<Blob>>({
    url: `${SERVER.REPORT_CMDB}/resources/export`,
    method: Method.Get,
    responseType: "blob",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//设备关联sla
export function deviceRelationSla(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/device/relation`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//查询当前设备下的sla列表
export function deviceRelationSlaList(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/device/${data.id}/list`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//设备分组详情
export function getDeviceGroupDetail(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/groups/${data.id}/detail`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//设备类型详情
export function getDeviceTypeDetail(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/resource_types/${data.id}/detail`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//设备分组关联告警分类
export function deviceGroupRelationAlarm(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/groups/alert_classification/add/batch`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//设备类型关联告警分类
export function deviceTypeRelationAlarm(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/resource_types/alert_classification/add/batch`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//设备分组移除关联告警分类
export function deviceGroupDeleteAlarm(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/groups/alert_classification/remove/batch`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//设备类型移除关联告警分类
export function deviceTypeDeleteAlarm(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/resource_types/alert_classification/remove/batch`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//设备详情工单列表

export function getDeviceWorkOrderList(
  data: {
    includeOrderType: string[];
    excludeOrderType: string[];
    includeOrderSummary: string[];
    excludeOrderSummary: string[];
    includeOrderIds: string[];
    excludeOrderIds: string[];
    inOrderId: string[];
    neInOrderId: string[];
    inOrderSummary: string[];
    neInOrderSummary: string[];
  } & RequestBase,
  id?: string
) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/device_associated_order/${data.id}/query`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      pageNumber: data.pageNumber,
      pageSize: data.pageSize,
      sort: data.sort,
      includeOrderType: data.includeOrderType != undefined ? data.includeOrderType.join(",") : [],
      excludeOrderType: data.excludeOrderType != undefined ? data.excludeOrderType.join(",") : [],
      orderTypeRelation: data.orderTypeRelation,
      includeOrderSummary: data.includeOrderSummary != undefined ? data.includeOrderSummary.join(",") : [],
      excludeOrderSummary: data.excludeOrderSummary != undefined ? data.excludeOrderSummary.join(",") : [],
      orderIdFilterRelation: data.orderIdFilterRelation,
      includeOrderIds: data.includeOrderIds != undefined ? data.includeOrderIds.join(",") : [],
      excludeOrderIds: data.excludeOrderIds != undefined ? data.excludeOrderIds.join(",") : [],

      inOrderId: data.inOrderId != undefined ? data.inOrderId.join(",") : [],
      neInOrderId: data.neInOrderId != undefined ? data.neInOrderId.join(",") : [],

      inOrderSummary: data.inOrderSummary != undefined ? data.inOrderSummary.join(",") : [],
      neInOrderSummary: data.neInOrderSummary != undefined ? data.neInOrderSummary.join(",") : [],
      orderSummaryFilterRelation: data.orderSummaryFilterRelation,
    },
    data: {},
  });
}

//设备详情服务请求列表

export function getDeviceRequestList(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/list`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//设备分组- 关联设备
export function deviceGroupRelationDevice(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/groups/${data.id}/add/resources`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//设备分组- 移除设备
export function deviceGroupDelRelationDevice(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/groups/${data.id}/remove/resources`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//服务商- 关联设备
export function vendorsRelationDevice(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/vendors/${data.id}/add/resources`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//服务商- 移除设备
export function vendorsDelRelationDevice(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/vendors/${data.id}/remove/resources`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//设备分类- 关联设备
export function deviceTypeRelationDevice(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/resource_types/${data.id}/add/resources`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//设备分类- 移除设备
export function deviceTypeDelRelationDevice(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/resource_types/${data.id}/remove/resources`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//告警分类-设备分组
export function alarmDeviceGroup(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/groups`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//设备管理关联到的行动策略类别
export function deviceRelationSupportList(data: RequestBase, id?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/resources/${data.id}/supportNotes`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//上传文件
export function uploadFile(data: { file: File } & RequestBase, ossDirectoryPrefix?: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/files/multi/upload`,
    method: Method.Post,
    signal: undefined,
    headers: {},
    params: {
      ossDirectoryPrefix,
    },
    data: data,
    timeout: 50000000000,
    // responseType: "blob",
  });
}

//获取设备文件列表
export function getDeviceFileList(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/resource_files/${data.id}/page`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},

    // responseType: "blob",
  });
}
//获取服务目录列表
export function getserviceCatalogList(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/serviceCatalog/device/${data.id}/list`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},

    // responseType: "blob",
  });
}
//设备解绑服务目录
export function removeServiceCatalog(data: RequestBase & RequestBase) {
  console.log("data :>> ", data);
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/serviceCatalog/device/${data.id}/remove/${data.serviceCatalogId}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},

    // responseType: "blob",
  });
}
// 获取可分配的服务目录
export function getnewServiceCatalogList(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/serviceCatalog/${data.resourceId}/list`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//设备绑定服务目录
export function bindServiceCatalog(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/serviceCatalog/device/relation`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,

    // responseType: "blob",
  });
}
// 获取所有当前服务目录下的服务包
export function getPackageList(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/serviceCatalog/${data.id}/packageList`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

// 获取所有当前服务目录下的sla
export function getSlaList(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/serviceCatalog/${data.id}/slaList`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
// 获取sla的覆盖时间
export function getSlaTime(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/sla/rule/${data.ruleId}/detail`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//下载文件
export function downloadDeviceFile(data: RequestBase & RequestBase) {
  return request<unknown, Response<Blob> & { contentDisposition: { filename: string } }>({
    url: `${SERVER.CMDB}/files/download`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}
//删除文件
export function deleteDeviceFile(data: RequestBase & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.CMDB}/resource_files/${data.id}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//保存设备文件
export function saveDeviceFile(data: { name: string /* 文件名 */; size: number /* 文件大小, 单位: kb */; filePath: string /* 文件路径 */; url: string /* 访问url */ }[], id: string) {
  return request<unknown, Response<{ id: string /* 主键 */; resourceId: string /* 资源ID */; containerId: string /* 安全容器id */; name: string /* 文件名称 */; filePath: string; size: number /* 文件大小，单位：kb */; version: string /* 版本号 */; createdTime: string /* 创建时间 */; updatedTime: string /* 最近更新时间 */; createdBy?: string /* 创建人 */; updatedBy?: string /* 最后更新人 */ }[]>>({
    url: `${SERVER.CMDB}/resource_files/${id}`,
    method: Method.Post,
    // signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//添加日志
export function addDeviceLog(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/resource_logs`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//获取日期列表
export function getDeviceLogList(data: { pageNumber: string /* 页码, 默认第一页 */; pageSize: string /* 页大小, 默认10 */; sort: string[]; keyword?: string /* 查询关键字 */; startTime?: string /* 起始时间, 毫秒时间戳 */; endTime?: string /* 截止时间, 毫秒时间戳 */ } & RequestBase) {
  return request<unknown, Response<{ id: string /** 主键 */; resourceId: string /** 资源ID */; containerId: string /** 安全容器id */; resourceName?: string /** 资源名称 */; logNote: string /** 日志描述 */; fileInfos: { name: string /* 文件名 */; size: /* 文件大小, 单位: kb */ number; filePath: string /* 文件路径 */; url: string /* 访问url */ }[] /** 日志附件 */; version: string /** 版本号 */; createdTime: string /** 创建时间 */; updatedTime: string /** 最近更新时间 */; createdBy?: string /** 创建人 */; updatedBy?: string /** 最后更新人 */ }[]>>({
    url: `${SERVER.CMDB}/resource_logs/${data.id}/page`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//添加日志
export function addDeviceLogFile(data: RequestBase & RequestBase, id: string) {
  return request<unknown, Response<null>>({
    url: `${SERVER.CMDB}/resource_logs/${id}`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//更新日志
export function editDeviceLog(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/resource_logs/${data.id}`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {
      logNote: data.logNote,
    },
  });
}

//删除日志
export function deleteDeviceLog(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/resource_logs/${data.id}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//下载日志附件
export function downloadDeviceLogFile(data: RequestBase & RequestBase) {
  return request<unknown, Response<Blob> & { contentDisposition: { filename: string } }>({
    url: `${SERVER.CMDB}/files/download`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

//删除日志附件
export function deleteDeviceDeviceLogFile(data: RequestBase & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.CMDB}/resource_logs/${data.id}/file`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//PING
export function pingTo(data: RequestBase & RequestBase) {
  return request<unknown, string[]>({
    url: `${SERVER.CMDB}/${data.id}/ping`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//发起traceRoute
export function routeSend(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/${data.id}/traceRoute`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//获取traceRoute
export function getDeviceRoute(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/${data.id}/getTraceRoute/${data.deviceId}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function getNetcareDevice(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/resources/${data.id}/netcare_device`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
