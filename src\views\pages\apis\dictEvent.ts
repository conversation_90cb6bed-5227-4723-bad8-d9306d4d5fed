/* eslint-disable @typescript-eslint/no-unused-vars */
import { SERVER, Method, type Response, type RequestBase, bindSearchParams, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

import { priority, priorityOption } from "./eventPriority";
export { priority, priorityOption };
import { deviceImportance, deviceImportanceOption } from "./device";
export { deviceImportance, deviceImportanceOption };

import { i18n } from "@/lang/index";

const { t } = i18n.global;

/**
 * 基本分页字段
 */
interface PageFilter {
  paging: { pageNumber: number; pageSize: number };
  sort: string[];
}
/**
 * 基本时间字段
 */
interface DateRangeFilter {
  /** 创建时间起始时间 */
  createTimeStart: string;
  /** 创建时间结束时间 */
  createTimeEnd: string;
  /** 修改时间起始时间 */
  updateTimeStart: string;
  /** 修改时间结束时间 */
  updateTimeEnd: string;
  /** 紧凑模式下时间筛选-起始时间 */
  compactTimeStart: string;
  /** 紧凑模式下时间筛选-结束时间 */
  compactTimeEnd: string;
}
/**
 * 类型校验合并
 */
type Merge<T1 extends object, T2 extends object, MT = T1 & T2> = { [MK in keyof MT]: MT[MK] };
/**
 * 条件过滤器方法
 */
type ConditionFilter<N extends string, TT extends string, F extends string, TE extends string, OT = { [P in N extends `${infer F}${infer E}` ? `${TT}${Uppercase<F>}${E}` : never]: string[] } & { [P in F extends `${infer F}${infer E}` ? `${N}${Uppercase<F>}${E}` : never]: TE }> = { [P in keyof OT]: OT[P] };
// 类型校对方法
// type HasConditionFilter<Diff0 extends object, Diff1 extends object> = Merge<Record<keyof Omit<Diff0, keyof Diff1>, 0>, Record<keyof Omit<Diff1, keyof Diff0>, 1>>;

/**
 * 模块Api示例
 */
// export enum eventState {
//   UNASSIGNED = "UNASSIGNED",
//   WAITING_FOR_RECEIVE = "WAITING_FOR_RECEIVE",
//   PROCESSING = "PROCESSING",
//   PENDING_APPROVAL = "PENDING_APPROVAL",
//   CUSTOMER_SUSPENDED = "CUSTOMER_SUSPENDED",
//   SERVICE_PROVIDER_SUSPENDED = "SERVICE_PROVIDER_SUSPENDED",
//   SUSPENDED = "SUSPENDED",
//   COMPLETED = "COMPLETED",
//   CLOSED = "CLOSED",
// }
// export const eventStateOption: { label: string; value: keyof typeof eventState; color?: string; type?: "" | "success" | "warning" | "info" | "danger" }[] = [
//   { label: "未分配", value: eventState.UNASSIGNED, color: "#ED4013", type: "" },
//   { label: "待接手", value: eventState.WAITING_FOR_RECEIVE, color: "#FF7D00", type: "danger" },
//   { label: "处理中", value: eventState.PROCESSING, color: "#2CB6F4", type: "success" },
//   { label: "挂起待审批中", value: eventState.PENDING_APPROVAL, color: "#3EBE6B", type: "" },
//   { label: "客户挂起", value: eventState.CUSTOMER_SUSPENDED, color: "#3EBE6B", type: "" },
//   { label: "供应商挂起", value: eventState.SERVICE_PROVIDER_SUSPENDED, color: "#3EBE6B", type: "" },
//   { label: "挂起中", value: eventState.SUSPENDED, color: "#3EBE6B", type: "warning" },
//   { label: "完成", value: eventState.COMPLETED, color: "#3EBE6B", type: "" },
//   { label: "关闭", value: eventState.CLOSED, color: "#3EBE6B", type: "" },
// ];
export enum eventState {
  /**@type {string} - 新建 */
  NEW = "NEW",
  /**@type {string} - 未分配 */
  UNASSIGNED = "UNASSIGNED",
  /**@type {string} - 待处理 */
  WAITING_FOR_RECEIVE = "WAITING_FOR_RECEIVE",
  /**@type {string} - 处理中 */
  PROCESSING = "PROCESSING",
  /**@type {string} - 挂起待审批中 */
  PENDING_APPROVAL = "PENDING_APPROVAL",
  /**@type {string} - 客户挂起 */
  CUSTOMER_SUSPENDED = "CUSTOMER_SUSPENDED",
  /**@type {string} - 供应商挂起 */
  SERVICE_PROVIDER_SUSPENDED = "SERVICE_PROVIDER_SUSPENDED",
  /**@type {string} - 挂起中 */
  SUSPENDED = "SUSPENDED",
  /**@type {string} - 完成 */
  COMPLETED = "COMPLETED",
  /**@type {string} - 关闭 */
  CLOSED = "CLOSED",
  /**@type {string} - 自动关闭 */
  AUTO_CLOSED = "AUTO_CLOSED",
}

export const eventStateOption: { label: string; value: keyof typeof eventState; color?: string; type?: undefined | "success" | "warning" | "info" | "danger" }[] = [
  // { label: "未分配", value: eventState.UNASSIGNED, color: "#ED4013", type: "danger" },
  { label: t("event.新建"), value: eventState.NEW, color: "#ED4013", type: "danger" },
  // { label: "待处理", value: eventState.WAITING_FOR_RECEIVE, color: "#FF7D00", type: "danger" },
  { label: t("event.处理中"), value: eventState.PROCESSING, color: "#2CB6F4", type: "success" },
  { label: t("event.挂起待审批中"), value: eventState.PENDING_APPROVAL, color: "#3EBE6B", type: void 0 },
  { label: t("event.挂起中"), value: eventState.SUSPENDED, color: "#3EBE6B", type: "warning" },
  // { label: "客户挂起", value: eventState.CUSTOMER_SUSPENDED, color: "#3EBE6B", type: "warning" },
  // { label: "供应商挂起", value: eventState.SERVICE_PROVIDER_SUSPENDED, color: "#3EBE6B", type: "warning" },
  { label: t("event.已解决"), value: eventState.COMPLETED, color: "#3EBE6B", type: void 0 },
  { label: t("event.关闭"), value: eventState.CLOSED, color: "#3EBE6B", type: void 0 },
  { label: t("event.自动关闭"), value: eventState.AUTO_CLOSED, color: "#3EBE6B", type: void 0 },
];

export enum eventSeverity {
  Critical = "Critical",
  Major = "Major",
  Minor = "Minor",
  Warning = "Warning",
  Normal = "Normal",
  Unknown = "Unknown",
  Informational = "Informational",
  Calculating = "Calculating",
  Symptom = "Symptom",
  Monitoring = "Monitoring",
  Others = "Others",
}
export const eventSeverityOption: { label: string; value: keyof typeof eventSeverity; color?: string }[] = [
  { label: "Critical", value: eventSeverity.Critical, color: "#db3328" },
  { label: "Major", value: eventSeverity.Major, color: "#f0ad4e" },
  { label: "Minor", value: eventSeverity.Minor, color: "#e9d310" },
  { label: "Warning", value: eventSeverity.Warning, color: "#31b0d5" },
  { label: "Normal", value: eventSeverity.Normal, color: "#5cb85c" },
  { label: "Unknown", value: eventSeverity.Unknown, color: "#d3d3d3" },
  { label: "Informational", value: eventSeverity.Informational, color: "#d3d3d3" },
  { label: "Calculating", value: eventSeverity.Calculating, color: "#bd9fd9" },
  { label: "Symptom", value: eventSeverity.Symptom, color: "#d3d3d3" },
  { label: "Monitoring", value: eventSeverity.Monitoring, color: "#6B95EB" },
  // { label: "Unknown", value: eventSeverity.Others },
];
export enum sourceType {
  STANDARD = "STANDARD" /* 标准集成 */,
  NET_CARE = "NET_CARE" /* NetCare */,
  PROMETHEUS = "PROMETHEUS" /* prometheus */,
  N9E = "N9E" /* 夜莺V6 */,
  IDEAL_METRIC = "IDEAL_METRIC" /* IdealMetric */,
  UNKNOWN = "UNKNOWN" /* Unknown */,
}
export const sourceTypeOption: { label: string; value: keyof typeof sourceType }[] = [
  { label: "标准集成", value: sourceType.STANDARD },
  { label: "NetCare", value: sourceType.NET_CARE },
  { label: "Prometheus", value: sourceType.PROMETHEUS },
  { label: "夜莺V6", value: sourceType.N9E },
  { label: "IdealMetric", value: sourceType.IDEAL_METRIC },
  { label: "Unknown", value: sourceType.UNKNOWN },
];
/* TODO: 事件 */
export interface NoteMeta {
  noteId: string /* 小记ID */;
  eventId: string /* 关联ID */;
  noteContent: string /* 小记内容 */;
  attachmentList: Attachment[] /* 小记附件 */;
  noteCreateUserId: string /* 小记创建用户ID */;
  tenantId: string /* 租户ID */;
  noteCreateUserName: string /* 小记创建用户名称 */;
  noteCreateUserProfilePicture: string /* 小记创建用户头像 */;
  noteCreateTime: string /* 小记创建时间 */;
}
export interface Attachment {
  attachmentId: string /* 附件ID */;
  attachmentName: string /* 附件名称 */;
  attachmentUrl: string /* 附件地址 */;
  attachmentKey: string /* 附件key */;
}
export interface EventDynamic {
  dynamicId: string /* 事件动态ID */;
  eventId: string /* 关联ID */;
  dynamicContent: string /* 事件动态内容 */;
  eventCreatorId: string /* 事件创造者ID */;
  eventTransferId: string /* 事件转交对象ID */;
  dynamicCreateTime: string /* 事件动态创建时间 */;
  tenantId: string /* 租户ID */;
}
export interface SuspendRecord {
  serial: string /* 序号 */;
  durationMinutes: string /* 挂起持续分钟 */;
  cause: string /* 挂起原因 */;
  suspendUserId: string /* 挂起人ID */;
  suspendUserName: string /* 挂起人名称 */;
  startTime: string /* 挂起开始时间戳 */;
  endTime: string /* 挂起结束时间 */;
}
export interface SLATimeLimit {
  slaId: string /* slaId */;
  priority: priority /* sla优先级 */;
  responseTimeLimits: TimeLimit[] /* 响应时限 */;
  completedTimeLimits: TimeLimit[] /* 解决时限 */;
}
export interface TimeLimit {
  urgencyType: urgencyType /* slaId */;
  tolerateMinutes: string /* sla优先级 */;
}
export enum urgencyType {
  WARNING = "WARNING" /* 警告 */,
  IMMINENT = "IMMINENT" /* 严重 */,
  BREACH = "BREACH" /* 致命 */,
  NORMAL = "NORMAL",
}
export const urgencyTypeOption: { label: string; value: keyof typeof urgencyType; color?: string }[] = [
  { label: "警告", value: urgencyType.WARNING, color: "#2CB6F4" },
  { label: "严重", value: urgencyType.IMMINENT, color: "#FF7D00" },
  { label: "致命", value: urgencyType.BREACH, color: "#ED4013" },
  { label: "默认", value: urgencyType.NORMAL, color: "#5cb85c" },
];

export enum EventOperation {
  TAKE_OVER = "TAKE_OVER",
  DELIVER_TO = "DELIVER_TO",
  UPGRADE = "UPGRADE",
  CUSTOMER_SUSPENDED = "CUSTOMER_SUSPENDED",
  SERVICE_PROVIDER_SUSPENDED = "SERVICE_PROVIDER_SUSPENDED",
  FINISHED = "FINISHED",
  CLOSE = "CLOSE",
}
/* 操作 枚举类型: TAKE_OVER :处理 | DELIVER_TO :转交 | UPGRADE :升级 | CUSTOMER_SUSPENDED :客户挂起 | SERVICE_PROVIDER_SUSPENDED :供应商挂起 | FINISHED :完成 | CLOSE :关闭 */

export const eventOperationOption = [
  { label: "处理", value: EventOperation.TAKE_OVER },
  { label: "转交", value: EventOperation.DELIVER_TO },
  { label: "升级", value: EventOperation.UPGRADE },
  { label: "客户挂起", value: EventOperation.CUSTOMER_SUSPENDED },
  { label: "供应商挂起", value: EventOperation.SERVICE_PROVIDER_SUSPENDED },
  { label: "已解决", value: EventOperation.FINISHED },
  { label: "关闭", value: EventOperation.CLOSE },
];

/* TODO: 事件 */
/**
 * @description 事件列表
 */
export interface EventItem {
  /** 事件ID */
  id: string;
  /** 工单 */
  identifier: string;
  /** 工单类型 */
  orderType: /* 枚举: EVENT_ORDER :事件单 | SERVICE_REQUEST :服务请求 | CHANGE :变更 | QUESTION :问题 | PUBLISH :发布 */ import("./association").OrderType.EVENT_ORDER;
  /** 是否手动创建 */
  manualCreate: boolean;
  /** 租户ID */
  tenantId: string;
  /** 租户名称 */
  tenantName: string;
  /** 租户缩写 */
  tenantAbbreviation?: string;
  /** 事件状态 */
  eventState: eventState;
  /** 事件优先级 */
  priority: priority;
  /** 当前挂起请求时间戳 */
  currentSuspendRequestTime: string;
  /** 当前挂起暂停时间戳 */
  currentSuspendPauseTime: string;
  /** 当前是否有需要审批的挂起 */
  pendingSuspend: boolean;
  /** 响应时间(目前已过时间) */
  responseTime: string;
  /** 响应时限(配置的响应时间) */
  responseLimit: string;
  /** 解决时间(目前已过时间) */
  resolveTime: string;
  /** 解决时限(配置的解决时间) */
  resolveLimit: string;
  /** 告警数量 */
  alarmNumber: string;
  /** 事件摘要 */
  summary: string;
  /** 持续时间 */
  durationTime: number;
  /** 监控源 */
  source: sourceType;
  /** 创建时间 */
  createTime: string;
  /** 修改时间 */
  updateTime: string;
  /** 负责人Id */
  responsibleId: string;
  /** 负责人名称 */
  responsibleName: string;
  /** 可见用户组ID */
  userGroupId: string;
  /** 可见用户组名称 */
  userGroupName: string;
  /** 处理人Id */
  actorId: string;
  /** 处理人名称 */
  actorName: string;
  /** 回显展示用户组id 当转交到用户时，需要保存用户所属用户组id，用于回显 */
  displayUserGroupId: string;
  /** 当前被指派的处理人（可以有多个） */
  currentActors: string[];
  /** 挂起人申请id */
  suspendId: string;
  /** 事件挂起原因 */
  suspendReason: string;
  /** 事件挂起时长(返回的是分钟) */
  hangUpTime: string;
  /** 完结代码信息 */
  completeInfo: { finishCodeName: string /* 完结代码名称 */; finishCodeDesc: string /* 完结代码描述 */; finishContent: string /* 完结内容 */ } /* 完结代码信息 */;
  /** 挂起记录 */
  suspendRecords: SuspendRecord[];
  /** SLA时限信息 */
  slaTimeLimit: SLATimeLimit;
  /** 收集告警，默认开启，关闭后不能启用 */
  collectAlert: boolean;
  /** 前段展示收集告警入参回显 */
  collectAlertEcho?: boolean;
  /** 外部ID */
  externalId: string;
  /** 采集机 */
  collector: string;
  /** 描述 */
  description: string;
  /** 修改人 */
  updatedBy?: string;
  /** 创建人 */
  createdBy?: string;
  /** 联系人列表 */
  contacts: { contactId: string; contactType: /* 枚举: Notification :通知联系人 | Technical :技术联系人 | OnSite :现场联系人 */ "Notification" | "Technical" | "OnSite" }[];
  /** 设备id列表 */
  deviceIds: string[];
  /** 生成事件时,对应的原始slaId */
  originSlaId: string;
  /** 生成事件时，对应的原始优先级 */
  originPriority: priority;
  /** SLA快照ID */
  slaSnapshotId: string;
  /** 重要性 */
  importance: deviceImportance;
  /** 紧急性 */
  severity: eventSeverity;
  /** 工单影响性 */
  influence: deviceImportance;
  /** 工单紧急性 */
  urgency: deviceImportance;
  /** 自动关闭时间标识,true:自动关闭，false:手动关闭 */
  autoCloseTimeFlag: boolean;
  /** 操作 */
  operation: EventOperation;
  /** 事件锁定结束时间 */
  lockEndTime: string;
  locked: boolean;

  verifyPermissionIds: string[];
}
// export function getEventList /* 获取模块 */(data: { type: "list" | "myList" } & RequestBase) {
//   const params = new URLSearchParams({ pageNumber: String(data.paging?.pageNumber || 0), pageSize: String(data.paging?.pageSize || 0) });
//   (data.sort instanceof Array ? data.sort : []).forEach((v) => params.append("sort", v));
//   bindSearchParams({ identifier: data.identifier, eventState: data.eventState, summary: data.summary, priority: data.priority, tenantId: data.tenantId, responsibleName: data.responsibleName, actorName: data.actorName, permissionId: "612917424815079424" }, params);
//   bindSearchParams({ createTimeStart: ((data.createTime as Record<string, string>) || {}).start, createTimeEnd: ((data.createTime as Record<string, string>) || {}).end }, params);
//   bindSearchParams({ updateTimeStart: ((data.updateTime as Record<string, string>) || {}).start, updateTimeEnd: ((data.updateTime as Record<string, string>) || {}).end }, params);

//   return request<unknown, Response<EventItem[]>>({
//     url: `${SERVER.EVENT_CENTER}/dict_event/${data.type}`,
//     method: Method.Get,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     params,
//     data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
//   });
// }
interface EventQuery {
  /** id/工单号 */
  identifier?: string;
  /** 租户名称 */
  tenantName?: string;
  /** 事件状态 NEW :新建 PROCESSING :处理中 PENDING_APPROVAL :挂起待审批中 SUSPENDED :挂起中 COMPLETED :完成 AUTO_CLOSED :自动关闭 CLOSED :关闭 */
  eventState?: string;
  /** 摘要 */
  summary?: string;
  /** 事件优先级(可以多选，多选用逗号分隔) */
  priority?: string;

  state?: string;
  /** 租户ID */
  tenantId?: string;
  /** 负责人 */
  responsibleName?: string;
  /** 当前处理人 */
  actorName?: string;

  boardOrNot?: string;
  permissionId?: string;
}
/**
 * @description 分页展示全部事件列表
 * @url http://*************:3000/project/17/interface/api/515
 */
export async function getEventList(req: { type: "myList" | "list" } & Record<string, any> & EventQuery & PageFilter & DateRangeFilter & Merge<ConditionFilter<"tenantName" | "orderId" | "state" | "actorName" | "responsibleName" | "orderSummary", "include" | "exclude" | "eq" | "ne", "FilterRelation", "AND" | "OR">, ConditionFilter<"alarmCount", "eq" | "ne" | "ge" | "gt" | "le" | "lt" | "isNull" | "isNotNull", "FilterRelation", "AND" | "OR">> & ConditionFilter<"compactActorName", "eq" | "ne" | "include" | "exclude", "FilterRelation", "AND" | "OR"> & Record<string, any>) {
  const controller = new AbortController();
  const userInfo = (await import("@/utils/getUserInfo")).default();
  const { 智能事件中心_客户_工单可读, 智能事件中心_项目_工单可读, 智能事件中心_联系人_工单可读, 智能事件中心_设备_工单可读 } = await import("@/views/pages/permission");

  let urlFlag: "all" | "permission" = "all";

  let url;
  let method;
  if (userInfo.hasPermission(智能事件中心_客户_工单可读)) {
    urlFlag = "all";
    url = `${SERVER.EVENT_CENTER}/dict_event/${req.type}`;
    method = Method.Get;
  } else if (
    /*  */
    (userInfo.hasPermission("756061441173225472" as any) && userInfo.hasPermission(智能事件中心_项目_工单可读)) ||
    (userInfo.hasPermission("756062477225033728" as any) && userInfo.hasPermission(智能事件中心_联系人_工单可读)) ||
    (userInfo.hasPermission("756062918394511360" as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))
  ) {
    urlFlag = "permission";
    url = `${SERVER.EVENT_CENTER}/dict_event/listDictEvent`;
    method = Method.Post;
  }

  if (!url) return { success: true, data: [], message: "暂无权限" };

  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url, method, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();

        const query = {
          identifier: req.identifier /* id/工单号 */,
          tenantName: req.tenantName /* 租户名称 */,
          eventState: req.eventState ? req.eventState : void 0 /* 事件状态NEW :新建PROCESSING :处理中PENDING_APPROVAL :挂起待审批中SUSPENDED :挂起中COMPLETED :完成AUTO_CLOSED :自动关闭CLOSED :关闭 */,
          summary: req.summary /* 摘要 */,
          priority: req.priority /* 事件优先级(可以多选，多选用逗号分隔) */,
          queryType: req.queryType /* 工单参数 */,
          state: req.state,
          tenantId: req.tenantId /* 租户ID */,
          // createTimeStart: req.createTimeStart /* 创建时间起始时间 */,
          // createTimeEnd: req.createTimeEnd /* 创建时间结束时间 */,
          // updateTimeStart: req.updateTimeStart /* 修改时间起始时间 */,
          // updateTimeEnd: req.updateTimeEnd /* 修改时间结束时间 */,
          responsibleName: req.responsibleName /* 负责人 */,
          actorName: req.actorName /* 当前处理人 */,

          boardOrNot: typeof req.boardOrNot === "boolean" ? req.boardOrNot : true,

          /* 创建时间 */
          ...(req.createTimeStart && req.createTimeEnd ? { createTimeStart: req.createTimeStart, createTimeEnd: req.createTimeEnd } : {}),
          /* 修改时间 */
          ...(req.updateTimeStart && req.updateTimeEnd ? { updateTimeStart: req.updateTimeStart, updateTimeEnd: req.updateTimeEnd } : {}),
          /* 创建时间AND修改时间 */
          ...(req.compactTimeStart && req.compactTimeEnd ? { compactTimeStart: req.compactTimeStart, compactTimeEnd: req.compactTimeEnd } : {}),

          /* 租户名称 */
          ...([...(req.includeTenantName instanceof Array ? req.includeTenantName : []), ...(req.excludeTenantName instanceof Array ? req.excludeTenantName : []), ...(req.eqTenantName instanceof Array ? req.eqTenantName : []), ...(req.neTenantName instanceof Array ? req.neTenantName : [])].filter((v) => v).length ? { tenantNameFilterRelation: req.tenantNameFilterRelation === "OR" ? "OR" : "AND", includeTenantName: req.includeTenantName instanceof Array && req.includeTenantName.length ? req.includeTenantName.join(",") : void 0, excludeTenantName: req.excludeTenantName instanceof Array && req.excludeTenantName.length ? req.excludeTenantName.join(",") : void 0, eqTenantName: req.eqTenantName instanceof Array && req.eqTenantName.length ? req.eqTenantName.join(",") : void 0, neTenantName: req.neTenantName instanceof Array && req.neTenantName.length ? req.neTenantName.join(",") : void 0 } : {}),

          /* 客保工单号 */
          ...([...(req.includeKbServiceCodes instanceof Array ? req.includeKbServiceCodes : []), ...(req.excludeKbServiceCodes instanceof Array ? req.excludeKbServiceCodes : []), ...(req.eqKbServiceCodes instanceof Array ? req.eqKbServiceCodes : []), ...(req.neKbServiceCodes instanceof Array ? req.neKbServiceCodes : [])].filter((v) => v).length ? { kbServiceCodesFilterRelation: req.kbServiceCodesFilterRelation === "OR" ? "OR" : "AND", includeKbServiceCodes: req.includeKbServiceCodes instanceof Array && req.includeKbServiceCodes.length ? req.includeKbServiceCodes.join(",") : void 0, excludeKbServiceCodes: req.excludeKbServiceCodes instanceof Array && req.excludeKbServiceCodes.length ? req.excludeKbServiceCodes.join(",") : void 0, eqKbServiceCodes: req.eqKbServiceCodes instanceof Array && req.eqKbServiceCodes.length ? req.eqKbServiceCodes.join(",") : void 0, neKbServiceCodes: req.neKbServiceCodes instanceof Array && req.neKbServiceCodes.length ? req.neKbServiceCodes.join(",") : void 0 } : {}),

          /* 工单号 */
          ...([...(req.includeOrderId instanceof Array ? req.includeOrderId : []), ...(req.excludeOrderId instanceof Array ? req.excludeOrderId : []), ...(req.eqOrderId instanceof Array ? req.eqOrderId : []), ...(req.neOrderId instanceof Array ? req.neOrderId : [])].filter((v) => v).length ? { orderIdFilterRelation: req.orderIdFilterRelation === "OR" ? "OR" : "AND", includeOrderIds: req.includeOrderId instanceof Array && req.includeOrderId.length ? req.includeOrderId.join(",") : void 0, excludeOrderIds: req.excludeOrderId instanceof Array && req.excludeOrderId.length ? req.excludeOrderId.join(",") : void 0, eqOrderIds: req.eqOrderId instanceof Array && req.eqOrderId.length ? req.eqOrderId.join(",") : void 0, neOrderIds: req.neOrderId instanceof Array && req.neOrderId.length ? req.neOrderId.join(",") : void 0 } : {}),

          /* 状态 */
          ...([...(req.includeState instanceof Array ? req.includeState : []), ...(req.excludeState instanceof Array ? req.excludeState : []), ...(req.eqState instanceof Array ? req.eqState : []), ...(req.neState instanceof Array ? req.neState : [])].filter((v) => v).length ? { stateFilterRelation: req.stateFilterRelation === "OR" ? "OR" : "AND", includeStates: req.includeState instanceof Array && req.includeState.length ? req.includeState.join(",") : void 0, excludeStates: req.excludeState instanceof Array && req.excludeState.length ? req.excludeState.join(",") : void 0, eqStates: req.eqState instanceof Array && req.eqState.length ? req.eqState.join(",") : void 0, neStates: req.neState instanceof Array && req.neState.length ? req.neState.join(",") : void 0 } : {}),

          /* 告警数 */
          ...([...(req.eqAlarmCount instanceof Array ? req.eqAlarmCount : []), ...(req.neAlarmCount instanceof Array ? req.neAlarmCount : []), ...(req.geAlarmCount instanceof Array ? req.geAlarmCount : []), ...(req.gtAlarmCount instanceof Array ? req.gtAlarmCount : []), ...(req.leAlarmCount instanceof Array ? req.leAlarmCount : []), ...(req.ltAlarmCount instanceof Array ? req.ltAlarmCount : []), ...(req.isNullAlarmCount instanceof Array ? req.isNullAlarmCount : []), ...(req.isNotNullAlarmCount instanceof Array ? req.isNotNullAlarmCount : [])].filter((v) => v).length ? { alarmCountFilterRelation: req.alarmCountFilterRelation === "OR" ? "OR" : "AND", eqAlarmCount: req.eqAlarmCount instanceof Array && req.eqAlarmCount.length ? req.eqAlarmCount.join(",") : void 0, neAlarmCount: req.neAlarmCount instanceof Array && req.neAlarmCount.length ? req.neAlarmCount.join(",") : void 0, geAlarmCount: req.geAlarmCount instanceof Array && req.geAlarmCount.length ? req.geAlarmCount.join(",") : void 0, gtAlarmCount: req.gtAlarmCount instanceof Array && req.gtAlarmCount.length ? req.gtAlarmCount.join(",") : void 0, leAlarmCount: req.leAlarmCount instanceof Array && req.leAlarmCount.length ? req.leAlarmCount.join(",") : void 0, ltAlarmCount: req.ltAlarmCount instanceof Array && req.ltAlarmCount.length ? req.ltAlarmCount.join(",") : void 0, isNullAlarmCount: req.isNullAlarmCount instanceof Array && req.isNullAlarmCount.length ? req.isNullAlarmCount.join(",") : void 0, isNotNullAlarmCount: req.isNotNullAlarmCount instanceof Array && req.isNotNullAlarmCount.length ? req.isNotNullAlarmCount.join(",") : void 0 } : {}),

          /* 处理人 */
          ...([...(req.includeActorName instanceof Array ? req.includeActorName : []), ...(req.excludeActorName instanceof Array ? req.excludeActorName : []), ...(req.eqActorName instanceof Array ? req.eqActorName : []), ...(req.neActorName instanceof Array ? req.neActorName : [])].filter((v) => v).length ? { actorNameFilterRelation: req.actorNameFilterRelation === "OR" ? "OR" : "AND", includeActorName: req.includeActorName instanceof Array && req.includeActorName.length ? req.includeActorName.join(",") : void 0, excludeActorName: req.excludeActorName instanceof Array && req.excludeActorName.length ? req.excludeActorName.join(",") : void 0, eqActorName: req.eqActorName instanceof Array && req.eqActorName.length ? req.eqActorName.join(",") : void 0, neActorName: req.neActorName instanceof Array && req.neActorName.length ? req.neActorName.join(",") : void 0 } : {}),

          /* 负责人 */
          ...([...(req.includeResponsibleName instanceof Array ? req.includeResponsibleName : []), ...(req.excludeResponsibleName instanceof Array ? req.excludeResponsibleName : []), ...(req.eqResponsibleName instanceof Array ? req.eqResponsibleName : []), ...(req.neResponsibleName instanceof Array ? req.neResponsibleName : [])].filter((v) => v).length ? { responsibleNameFilterRelation: req.responsibleNameFilterRelation === "OR" ? "OR" : "AND", includeResponsibleName: req.includeResponsibleName instanceof Array && req.includeResponsibleName.length ? req.includeResponsibleName.join(",") : void 0, excludeResponsibleName: req.excludeResponsibleName instanceof Array && req.excludeResponsibleName.length ? req.excludeResponsibleName.join(",") : void 0, eqResponsibleName: req.eqResponsibleName instanceof Array && req.eqResponsibleName.length ? req.eqResponsibleName.join(",") : void 0, neResponsibleName: req.neResponsibleName instanceof Array && req.neResponsibleName.length ? req.neResponsibleName.join(",") : void 0 } : {}),

          /* 工单摘要 */
          ...([...(req.includeOrderSummary instanceof Array ? req.includeOrderSummary : []), ...(req.excludeOrderSummary instanceof Array ? req.excludeOrderSummary : []), ...(req.eqOrderSummary instanceof Array ? req.eqOrderSummary : []), ...(req.neOrderSummary instanceof Array ? req.neOrderSummary : [])].filter((v) => v).length ? { orderSummaryFilterRelation: req.orderSummaryFilterRelation === "OR" ? "OR" : "AND", includeOrderSummary: req.includeOrderSummary instanceof Array && req.includeOrderSummary.length ? req.includeOrderSummary.join(",") : void 0, excludeOrderSummary: req.excludeOrderSummary instanceof Array && req.excludeOrderSummary.length ? req.excludeOrderSummary.join(",") : void 0, eqOrderSummary: req.eqOrderSummary instanceof Array && req.eqOrderSummary.length ? req.eqOrderSummary.join(",") : void 0, neOrderSummary: req.neOrderSummary instanceof Array && req.neOrderSummary.length ? req.neOrderSummary.join(",") : void 0 } : {}),

          /* 负责人AND处理人 */
          ...([...(req.eqCompactActorName instanceof Array ? req.eqCompactActorName : []), ...(req.neCompactActorName instanceof Array ? req.neCompactActorName : []), ...(req.includeCompactActorName instanceof Array ? req.includeCompactActorName : []), ...(req.excludeCompactActorName instanceof Array ? req.excludeCompactActorName : [])].filter((v) => v).length ? { compactActorNameFilterRelation: req.compactActorNameFilterRelation === "OR" ? "OR" : "AND", compactEqActorName: req.eqCompactActorName instanceof Array && req.eqCompactActorName.length ? req.eqCompactActorName.join(",") : void 0, compactNeActorName: req.neCompactActorName instanceof Array && req.neCompactActorName.length ? req.neCompactActorName.join(",") : void 0, compactIncludeActorName: req.includeCompactActorName instanceof Array && req.includeCompactActorName.length ? req.includeCompactActorName.join(",") : void 0, compactExcludeActorName: req.excludeCompactActorName instanceof Array && req.excludeCompactActorName.length ? req.excludeCompactActorName.join(",") : void 0 } : {}),

          ...([...(req.eqTicketGroupName instanceof Array ? req.eqTicketGroupName : []), ...(req.neTicketGroupName instanceof Array ? req.neTicketGroupName : []), ...(req.inTicketGroupName instanceof Array ? req.inTicketGroupName : []), ...(req.excludeTicketGroupName instanceof Array ? req.excludeTicketGroupName : [])].filter((v) => v).length ? { ticketGroupNameFilterRelation: req.ticketGroupNameFilterRelation === "OR" ? "OR" : "AND", eqTicketGroupName: req.eqTicketGroupName instanceof Array && req.eqTicketGroupName.length ? req.eqTicketGroupName.join(",") : void 0, neTicketGroupName: req.neTicketGroupName instanceof Array && req.neTicketGroupName.length ? req.neTicketGroupName.join(",") : void 0, inTicketGroupName: req.inTicketGroupName instanceof Array && req.inTicketGroupName.length ? req.inTicketGroupName.join(",") : void 0, excludeTicketGroupName: req.excludeTicketGroupName instanceof Array && req.excludeTicketGroupName.length ? req.excludeTicketGroupName.join(",") : void 0 } : {}),

          ...([...(req.eqUserGroupName instanceof Array ? req.eqUserGroupName : []), ...(req.neUserGroupName instanceof Array ? req.neUserGroupName : []), ...(req.inUserGroupName instanceof Array ? req.inUserGroupName : []), ...(req.excludeUserGroupName instanceof Array ? req.excludeUserGroupName : [])].filter((v) => v).length ? { userGroupNameFilterRelation: req.userGroupNameFilterRelation === "OR" ? "OR" : "AND", eqUserGroupName: req.eqUserGroupName instanceof Array && req.eqUserGroupName.length ? req.eqUserGroupName.join(",") : void 0, neUserGroupName: req.neUserGroupName instanceof Array && req.neUserGroupName.length ? req.neUserGroupName.join(",") : void 0, inUserGroupName: req.inUserGroupName instanceof Array && req.inUserGroupName.length ? req.inUserGroupName.join(",") : void 0, excludeUserGroupName: req.excludeUserGroupName instanceof Array && req.excludeUserGroupName.length ? req.excludeUserGroupName.join(",") : void 0 } : {}),

          ...([...(req.compactEqTicketName instanceof Array ? req.compactEqTicketName : []), ...(req.compactNeTicketName instanceof Array ? req.compactNeTicketName : []), ...(req.compactIncludeTicketName instanceof Array ? req.compactIncludeTicketName : []), ...(req.compactExcludeTicketName instanceof Array ? req.compactExcludeTicketName : [])].filter((v) => v).length ? { compactTicketNameFilterRelation: req.compactTicketNameFilterRelation === "OR" ? "OR" : "AND", compactEqTicketName: req.compactEqTicketName instanceof Array && req.compactEqTicketName.length ? req.compactEqTicketName.join(",") : void 0, compactNeTicketName: req.compactNeTicketName instanceof Array && req.compactNeTicketName.length ? req.compactNeTicketName.join(",") : void 0, compactIncludeTicketName: req.compactIncludeTicketName instanceof Array && req.compactIncludeTicketName.length ? req.compactIncludeTicketName.join(",") : void 0, compactExcludeTicketName: req.compactExcludeTicketName instanceof Array && req.compactExcludeTicketName.length ? req.compactExcludeTicketName.join(",") : void 0 } : {}),

          permissionId: req.permissionId || (await import("@/views/pages/permission")).智能事件中心_客户_工单可读,
        };

        bindParamByObj(Object.assign({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort, permissionId: req.permissionId || (await import("@/views/pages/permission")).智能事件中心_客户_工单可读 }, urlFlag === "all" ? query : {}), $req.params);
        $req.data =
          urlFlag === "permission"
            ? Object.assign(query, {
                permissionList: [
                  /*  */
                  { permissionId: 智能事件中心_项目_工单可读, permissionType: "View Tickets When Project Has View Tickets" },
                  { permissionId: 智能事件中心_联系人_工单可读, permissionType: "View Tickets When Contact Has View Tickets" },
                  { permissionId: 智能事件中心_设备_工单可读, permissionType: "View Tickets When Device Has View Tickets" },
                ],
              })
            : void 0;
        return $req;
      })
      .then(($req) => request<never, Response<EventItem[]>>($req)),
    { controller }
  );
}

export async function getEventData /* 获取模块 */(data: { id: string } & RequestBase) {
  const { 智能事件中心_DICT事件管理_可读, 智能事件中心_DICT事件管理_更新, 智能事件中心_DICT事件管理_编辑小记, 智能事件中心_DICT事件管理_分配设备, 智能事件中心_DICT事件管理_分配联系人 } = await import("@/views/pages/permission");

  const params = new URLSearchParams({});
  bindSearchParams(
    {
      queryPermissionId: data.queryPermissionId || [智能事件中心_DICT事件管理_可读].join(),
      verifyPermissionIds: data.verifyPermissionIds || [智能事件中心_DICT事件管理_更新, 智能事件中心_DICT事件管理_编辑小记, 智能事件中心_DICT事件管理_分配设备, 智能事件中心_DICT事件管理_分配联系人].join(),
    },
    params
  );

  return request<unknown, Response<EventItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/detail`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function addEventData /* 添加模块 */(data: Partial<EventItem> & RequestBase) {
  const params = new URLSearchParams({});
  return request<unknown, Response<EventItem[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/create`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: ["eventName", "eventDesc", "priority", "orderType", "projectName", "projectCode", "kbServiceCode", "serviceCode", "uniformServiceCode", "projectLevel", "range", "projectId", "ticketSubtype", "ticketTemplateId", "ticketClassificationId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

//获取事件统计数据
export function getEventCount(data: {} & RequestBase) {
  return request<unknown, Response<{ eventState: eventState; eventCount: number }[]>>({
    url: ["my", "myList"].includes(data.type as string) ? `${SERVER.EVENT_CENTER}/dict_event/myCount` : `${SERVER.EVENT_CENTER}/dict_event/allCount`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      boardOrNot: typeof data.boardOrNot === "boolean" ? data.boardOrNot : true,
      permissionId: "638966735935897600",
    },
    data: {},
  });
}

export function setEventData /* 更新模块 */(data: Partial<EventItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<EventItem[]>);
  // return request<unknown, Response<EventItem[]>>({
  //   url: `${SERVER.EVENT_CENTER}/module/${data.id}`,
  //   method: Method.Put,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  // });
}
export function setEventDataByTransferOrUpgrade /* 指派 */(data: { id: string; type: "transfer" | "eventUpgrade"; userGroupId: string; userId: string } & RequestBase) {
  const params = new URLSearchParams({ approve: String(data.approve) });
  return request<unknown, Response<EventItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.type}`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: ["userGroupId", "userId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}
export function setEventDataByImportance /* 修改紧急性 */(data: { id: string; importance: deviceImportance } & RequestBase) {
  const params = new URLSearchParams({ approve: String(data.approve) });
  return request<unknown, Response<EventItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/importance/${data.importance}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function setEventDataBySeverity /* 修改紧急性 */(data: { id: string; severity: eventSeverity } & RequestBase) {
  const params = new URLSearchParams({ approve: String(data.approve) });
  return request<unknown, Response<EventItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/severity/${data.severity}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function setEventDataByTakeOver /* 指派 */(data: { id: string | string[] } & RequestBase) {
  const params = new URLSearchParams({ approve: String(data.approve) });
  return request<unknown, Response<EventItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id instanceof Array ? "batchTakeOver" : "takeOver"}`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), data.id instanceof Array ? { eventIds: data.id } : { eventId: data.id }),
  });
}
export function setEventDataByDescription /* 更新模块 */(data: { id: string; desc: string; externalId: string } & RequestBase) {
  const params = new URLSearchParams({ approve: String(data.approve) });
  return request<unknown, Response<EventItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/EditDescription`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: ["desc"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { id: data.id, externalId: data.externalId }),
  });
}
export function setEventDataByKbcode /* 更新模块 */(data: { id: string; kbServiceCode: string } & RequestBase) {
  // const params = new URLSearchParams({ approve: String(data.approve) });
  return request<unknown, Response<EventItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/replace/kbServiceCode`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: { kbServiceCode: data.kbServiceCode },
    data: {},
    // data: ["desc"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { id: data.id, externalId: data.externalId }),
  });
}
export function setEventDataByAssign /* 更新模块 */(data: { id: string | string[]; userGroupId?: string; userId?: string } & RequestBase) {
  const params = new URLSearchParams({ approve: String(data.approve) });
  return request<unknown, Response<EventItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id instanceof Array ? "batchAssign" : "assign"}`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: ["userGroupId", "userId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), data.id instanceof Array ? { eventIds: data.id } : { eventId: data.id }),
  });
}
export function setEventDataByApprove /* 更新模块 */(data: { id: string; approve: boolean } & RequestBase) {
  const params = new URLSearchParams({ approve: String(data.approve) });
  return request<unknown, Response<EventItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/approveHangUp`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function setEventDataByPriority /* 更新模块 */(data: { id: string[] | string; priority: priority } & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  return request<unknown, Response<EventItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id instanceof Array ? "batchChangePriority" : "updatePriority"}`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: ["priority", "finishCodeName", "finishCodeDesc", "finishContent"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), data.id instanceof Array ? { id: data.id } : { eventId: data.id }),
  });
}
export function modEventData /* 修改模块 */(data: Partial<EventItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<EventItem[]>);
  // return request<unknown, Response<EventItem[]>>({
  //   url: `${SERVER.EVENT_CENTER}/module/${data.id}`,
  //   method: Method.Patch,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  // });
}
export function delEventData /* 删除模块 */(data: Partial<EventItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<EventItem[]>);
  // return request<unknown, Response<EventItem[]>>({
  //   url: `${SERVER.EVENT_CENTER}/module/${data.id}`,
  //   method: Method.Delete,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  // });
}
/*  */
export function getEventStat /* 统计模块 */(data: { id?: string[] } & RequestBase) {
  return request<unknown, Response<{ eventState: eventState; eventCount: number }[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id ? `${data.id}/myCount` : `allCount`}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: { permissionId: "612917424815079424", type: data.type },
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
/* TODO: 告警 */
export enum alertState {
  TRIGGER = "TRIGGER" /* 触发告警 */,
  RECOVER = "RECOVER" /* 告警恢复 */,
}
export const alertStateOption: { label: string; value: keyof typeof alertState }[] = [
  { label: "触发告警", value: alertState.TRIGGER },
  { label: "告警恢复", value: alertState.RECOVER },
];

/**
 * @description 告警
 */
export interface AlertItem {
  id: string;
  /** 租户ID */
  tenantId: string;
  /** 租户名称 */
  tenantName: string;
  /** 租户缩写 */
  tenantAbbreviation?: string;
  /** 事件紧急性 */
  eventSeverity: /* 枚举: Critical :Critical | Major :Major | Minor :Minor | Warning :Warning | Unknown :Unknown | Normal :Normal | Informational :Informational | Calculating :Calculating | Symptom :Symptom | Monitoring :Monitoring | Others :在映射中展示Critical -- Monitoring, */ eventSeverity;
  /** 告警标题 */
  title: string;
  desc: string;
  /** 设备id */
  deviceId: string;
  /** 设备名称 */
  deviceName: string;
  /** 设备ip */
  deviceIp: string;
  /** 告警源 */
  sourceType: /* 枚举: STANDARD :标准集成 | NET_CARE :NetCare | PROMETHEUS :prometheus | N9E :夜莺V6 | IDEAL_METRIC :IdealMetric | ZABBIX :Zabbix | UNKNOWN :Unknown */ sourceType;
  /** 告警状态 */
  state: /* 枚举: TRIGGER :触发告警 | RECOVER :告警恢复 */ alertState;
  /** 是否需要确认告警 */
  confirm: boolean;
  /** 告警版中的告警是否已被确认 */
  alarmBoardConfirmed: boolean;
  /** 告警版中的告警确认时间 */
  alarmBoardConfirmedTime: number;
  /** 告警板中的确认人 */
  alarmBoardConfirmedPerson: string;
  /** 事件中的告警记录是否已被确认 */
  eventConfirmed: boolean;
  /** 事件中的告警记录确认时间 */
  eventConfirmedTime: string;
  /** 事件中的确认人 */
  eventConfirmedPerson: string;
  /** 告警创建时间 */
  alertCreateTime: number;
  /** 工单列表 */
  orders: {
    /** 工单id */
    orderId: string;
    /** 租户id */
    tenantId: string;
    /** 工单类型 */
    orderType: /* 枚举: EVENT_ORDER :事件单 | SERVICE_REQUEST :服务请求 | CHANGE :变更 | QUESTION :问题 | PUBLISH :发布 */ "EVENT_ORDER" | "SERVICE_REQUEST" | "CHANGE" | "QUESTION" | "PUBLISH";
    /** 优先级 */
    priority: /* 枚举: P1 :P1~P7 优先级递减 | P2 :P2 | P3 :P3 | P4 :P4 | P5 :P5 | P6 :P6 | P7 :P7 | P8 :P8 手动 */ "P1" | "P2" | "P3" | "P4" | "P5" | "P6" | "P7" | "P8";
    /** 工单状态, 不同工单的状态不同，字符串类型 */
    state: import("./association").OrderState;
    /** 摘要 */
    digest?: string;
  }[];
}
// export function getAlertList /* 获取模块 */(data: { excludeTenantIds: string[]; includeTenantIds: string[]; excludeOrderIds: string[]; includeOrderIds: string[]; includeAlerts: string[]; excludeAlerts: string[]; eqTenantName: string[]; neTenantName: string[] } & RequestBase) {
//   const params = new URLSearchParams({ pageNumber: String(data.paging?.pageNumber || 0), pageSize: String(data.paging?.pageSize || 0) });
//   (data.sort instanceof Array ? data.sort : []).forEach((v) => params.append("sort", v));
//   // console.log(data.alarmBoardConfirmedTime);
//   // const alarmBoardConfirmedTime: any = data.alarmBoardConfirmedTime;
//   // if (alarmBoardConfirmedTime != undefined) {
//   //   alarmBoardConfirmedTime["end"] += 59999;
//   // }

//   bindSearchParams(
//     {
//       tenantId: data.tenantId,
//       serviceRequestId: data.serviceRequestId,
//       orderId: data.orderId,
//       deviceId: data.deviceId,
//       alertCreateTimeRange: data.alertCreateTimeRange,
//       eventSeverity: data.eventSeverity,
//       deviceNameOrIp: data.deviceNameOrIp,
//       title: data.title,
//       desc: data.desc,
//       alarmBoardConfirmedTime: data.alarmBoardConfirmedTime,
//       alarmBoardConfirmedUsername: data.alarmBoardConfirmedUsername,
//       excludeTenantName: data.excludeTenantIds != undefined ? data.excludeTenantIds.join() : [],
//       includeTenantName: data.includeTenantIds != undefined ? data.includeTenantIds.join() : [],

//       // excludeTenantIds: data.excludeTenantIds != undefined ? data.excludeTenantIds.join() : [],
//       // includeTenantIds: data.includeTenantIds != undefined ? data.includeTenantIds.join() : [],
//       eqTenantName: data.eqTenantName != undefined ? data.eqTenantName.join() : [],
//       neTenantName: data.neTenantName != undefined ? data.neTenantName.join() : [],
//       tenantNameFilterRelation: data.tenantIdFilterRelation,

//       excludeOrderIds: data.excludeOrderIds != undefined ? data.excludeOrderIds.join() : [],
//       includeOrderIds: data.includeOrderIds != undefined ? data.includeOrderIds.join() : [],
//       orderIdFilterRelation: data.orderIdFilterRelation,

//       excludeAlerts: data.excludeAlerts != undefined ? data.excludeAlerts.join() : [],
//       includeAlerts: data.includeAlerts != undefined ? data.includeAlerts.join() : [],
//       alertFilterRelation: data.alertFilterRelation,
//       permissionId: "612917424815079424",
//     },
//     params
//   );

//   return request<unknown, Response<AlertItem[]>>({
//     url: `${SERVER.EVENT_CENTER}/${data.loginTime}/alert`,
//     method: Method.Get,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     params,
//     data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
//   });
// }

/**
 * @description 根据告警中的工单ID列表和权限ID查询告警工单列表-响应体
 * @url http://*************:3000/project/17/interface/api/31178
 */
export interface AlertOrderItem {
  /** 工单id */
  orderId: /* Integer */ string;
  /** 租户id */
  tenantId: /* Integer */ string;
  /** 工单类型 */
  orderType?: /* 枚举: EVENT_ORDER :事件单 | SERVICE_REQUEST :服务请求 | CHANGE :变更 | QUESTION :问题 | PUBLISH :发布 */ "EVENT_ORDER" | "SERVICE_REQUEST" | "CHANGE" | "QUESTION" | "PUBLISH";
  /** 优先级 */
  priority?: /* 枚举: P1 :P1~P7 优先级递减 | P2 :P2 | P3 :P3 | P4 :P4 | P5 :P5 | P6 :P6 | P7 :P7 | P8 :P8 手动 */ "P1" | "P2" | "P3" | "P4" | "P5" | "P6" | "P7" | "P8";
  /** 工单状态, 不同工单的状态不同，字符串类型 事件单的状态{@link EventState} 服务请求的状态{@link cn.sh.ideal.cloudcare.ec.order.constants.ServiceState} 变更的状态{@link cn.sh.ideal.cloudcare.ec.core.constants.ChangeState} 问题的状态{@link cn.sh.ideal.cloudcare.ec.order.constants.QuestionState} */
  state?: {
    /** The name of this enum constant, as declared in the enum declaration. Most programmers should use the{@link #toString} method rather than accessing this field. */
    name?: string;
    /** The ordinal of this enumeration constant (its position in the enum declaration, where the initial constant is assigned an ordinal of zero).  Most programmers will have no use for this field.  It is designed for use by sophisticated enum-based data structures, such as {@link java.util.EnumSet} and{@link java.util.EnumMap}. */
    ordinal: /* Integer */ string;
    /** The hash code of this enumeration constant. */
    hash: /* Integer */ string;
  };
  /** 摘要 */
  digest?: string;
}

/**
 * @description 根据告警中的工单ID列表和权限ID查询告警工单列表
 * @url http://*************:3000/project/17/interface/api/31178
 */
export function hasAlertOrderByOrderPermission(req: { orderIds: string[] /* 告警中的工单ID列表 */; permissionId: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/alert/findAlertOrders`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ orderIds: req.orderIds.join(",") /* 告警中的工单ID列表 */ }, $req.params);
        try {
          bindParamByObj({ permissionId: req.permissionId || (await import("@/views/pages/permission")).监控管理中心_工单_可读 }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<AlertOrderItem[]>>($req)),
    { controller }
  );
}

interface AlertQuery {
  tenantId: /** 当前租户ID,如果为null,表示告警板查询;否则表示根据当前租户查询 */ string;
  deviceId?: /** 设备id */ string;
  alertCreateTimeRange?: { start: string; end: string };
  eventSeverity?: /** 紧急性（可以多选，多个用逗号分隔） */ string;
  alert?: /** 告警，包括标题和描述 */ string;
  title?: /** 告警标题 */ string;
  desc?: /** 告警描述 */ string;
  alarmBoardConfirmedTime?: { start: string; end: string };
  alarmBoardConfirmedUsername?: /** 告警板确认人用户名 */ string;
  deviceNameOrIp?: /** 设备名称或者ip */ string;
  orderId?: /** 工单id */ string;
  permissionId?: string;
  isAlertBoard: boolean;
}

/**
 * @description 分页查询告警列表
 * @url http://*************:3000/project/17/interface/api/8354
 */
export function getAlertList(req: AlertQuery & PageFilter & Merge<ConditionFilter<"tenantName", "include" | "exclude" | "eq" | "ne", "FilterRelation", "AND" | "OR">, ConditionFilter<"orderId" | "alert" | "deviceOrIp" | "responseUser", "include" | "exclude", "FilterRelation", "AND" | "OR">> & { loginTime: string; containerId: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/${req.loginTime}/alert`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj(
          {
            /*  */
            tenantId: req.tenantId /* 当前租户ID,如果为null,表示告警板查询;否则表示根据当前租户查询 */,
            deviceId: req.deviceId /* 设备id */,
            // ...(req.alertCreateTimeRange.start && req.alertCreateTimeRange.end ? { alertCreateTimeRange: req.alertCreateTimeRange } : {}),
            ...(req.alertCreateTimeRange ? { alertCreateTimeRange: req.alertCreateTimeRange } : {}),
            eventSeverity: req.eventSeverity /* 紧急性（可以多选，多个用逗号分隔） */,
            alert: req.alert /* 告警，包括标题和描述 */,
            title: req.title /* 告警标题 */,
            desc: req.desc /* 告警描述 */,
            // ...(req.alarmBoardConfirmedTime.start && req.alarmBoardConfirmedTime.end ? { alarmBoardConfirmedTime: req.alarmBoardConfirmedTime } : {}),
            ...(req.alarmBoardConfirmedTime ? { alarmBoardConfirmedTime: req.alarmBoardConfirmedTime } : {}),
            alarmBoardConfirmedUsername: req.alarmBoardConfirmedUsername /* 告警板确认人用户名 */,
            deviceNameOrIp: req.deviceNameOrIp /* 设备名称或者ip */,
            orderId: req.orderId /* 工单id */,

            /* 租户名称 */
            ...([...(req.includeTenantName instanceof Array ? req.includeTenantName : []), ...(req.excludeTenantName instanceof Array ? req.excludeTenantName : []), ...(req.eqTenantName instanceof Array ? req.eqTenantName : []), ...(req.neTenantName instanceof Array ? req.neTenantName : [])].filter((v) => v).length ? { tenantNameFilterRelation: req.tenantNameFilterRelation === "OR" ? "OR" : "AND", includeTenantName: req.includeTenantName instanceof Array && req.includeTenantName.length ? req.includeTenantName.join(",") : void 0, excludeTenantName: req.excludeTenantName instanceof Array && req.excludeTenantName.length ? req.excludeTenantName.join(",") : void 0, eqTenantName: req.eqTenantName instanceof Array && req.eqTenantName.length ? req.eqTenantName.join(",") : void 0, neTenantName: req.neTenantName instanceof Array && req.neTenantName.length ? req.neTenantName.join(",") : void 0 } : {}),

            /* 工单号 */
            ...([...(req.includeOrderId instanceof Array ? req.includeOrderId : []), ...(req.excludeOrderId instanceof Array ? req.excludeOrderId : [])].filter((v) => v).length ? { orderIdFilterRelation: req.orderIdFilterRelation === "OR" ? "OR" : "AND", includeOrderIds: req.includeOrderId instanceof Array && req.includeOrderId.length ? req.includeOrderId.join(",") : void 0, excludeOrderIds: req.excludeOrderId instanceof Array && req.excludeOrderId.length ? req.excludeOrderId.join(",") : void 0 } : {}),

            /* 告警内容 */
            ...([...(req.includeAlert instanceof Array ? req.includeAlert : []), ...(req.excludeAlert instanceof Array ? req.excludeAlert : [])].filter((v) => v).length ? { alertFilterRelation: req.alertFilterRelation === "OR" ? "OR" : "AND", includeAlerts: req.includeAlert instanceof Array && req.includeAlert.length ? req.includeAlert.join(",") : void 0, excludeAlerts: req.excludeAlert instanceof Array && req.excludeAlert.length ? req.excludeAlert.join(",") : void 0 } : {}),

            /* 设备或者ip */
            ...([...(req.includeDeviceOrIp instanceof Array ? req.includeDeviceOrIp : []), ...(req.excludeDeviceOrIp instanceof Array ? req.excludeDeviceOrIp : [])].filter((v) => v).length ? { deviceOrIpFilterRelation: req.deviceOrIpFilterRelation === "OR" ? "OR" : "AND", includeDevicesOrIps: req.includeDeviceOrIp instanceof Array && req.includeDeviceOrIp.length ? req.includeDeviceOrIp.join(",") : void 0, excludeDevicesOrIps: req.excludeDeviceOrIp instanceof Array && req.excludeDeviceOrIp.length ? req.excludeDeviceOrIp.join(",") : void 0 } : {}),

            /* 响应人 */
            ...([...(req.includeResponseUser instanceof Array ? req.includeResponseUser : []), ...(req.excludeResponseUser instanceof Array ? req.excludeResponseUser : [])].filter((v) => v).length ? { responseUserFilterRelation: req.responseUserFilterRelation === "OR" ? "OR" : "AND", includeResponseUsers: req.includeResponseUser instanceof Array && req.includeResponseUser.length ? req.includeResponseUser.join(",") : void 0, excludeResponseUsers: req.excludeResponseUser instanceof Array && req.excludeResponseUser.length ? req.excludeResponseUser.join(",") : void 0 } : {}),

            pageNumber: req.paging.pageNumber,
            pageSize: req.paging.pageSize,
            sort: req.sort,

            isAlertBoard: req.isAlertBoard,
            containerId: req.containerId,
          },
          $req.params
        );
        try {
          bindParamByObj({ permissionId: req.permissionId || (await import("@/views/pages/permission")).监控管理中心_告警_可读 }, $req.params);
        } catch (error) {
          /*  */
        }
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, Response<AlertItem[]>>($req)),
    { controller }
  );
}

export function addAlertData /* 添加模块 */(data: Partial<AlertItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<AlertItem[]>);
  // return request<unknown, Response<AlertItem[]>>({
  //   url: `${SERVER.EVENT_CENTER}/module`,
  //   method: Method.Post,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  // });
}
export function setAlertData /* 更新模块 */(data: Partial<AlertItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<AlertItem[]>);
  // return request<unknown, Response<AlertItem[]>>({
  //   url: `${SERVER.EVENT_CENTER}/module/${data.id}`,
  //   method: Method.Put,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  // });
}
export function modAlertData /* 修改模块 */(data: Partial<AlertItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<AlertItem[]>);
  // return request<unknown, Response<AlertItem[]>>({
  //   url: `${SERVER.EVENT_CENTER}/module/${data.id}`,
  //   method: Method.Patch,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  // });
}
export function delAlertData /* 删除模块 */(data: Partial<AlertItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<AlertItem[]>);
  // return request<unknown, Response<AlertItem[]>>({
  //   url: `${SERVER.EVENT_CENTER}/module/${data.id}`,
  //   method: Method.Delete,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  // });
}
/*  */
export function getAlertStat /* 统计模块 */(data: { ids: string[] } & RequestBase) {
  return request<unknown, Response<{ alertCount: number /* 告警数 */; deviceCount: number /* 设备数 */; tenantCount: number /* 租户数（客户数） */; processEventCount: number /* 处理中的事件数 */; waitingForReceiveCount: number /* 未接手的事件数 */; processEventConfirm: boolean /* 同步确认处理中事件下的告警 */; waitingForReceiveConfirm: boolean /* 同步确认未接手事件下的告警并将事件状态置为处理中 */; assignedToEvent: boolean /* 分配告警到事件 */; createEvent: boolean /* 创建事件 */ }>>({
    url: `${SERVER.EVENT_CENTER}/alert/count/${data.ids.join(",")}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function setAlertStat /* 统计模块 */(data: { ids: string[] } & RequestBase) {
  return request<unknown, Response<{ alertCount: number /* 告警数 */; deviceCount: number /* 设备数 */; tenantCount: number /* 租户数（客户数） */; processEventCount: number /* 处理中的事件数 */; waitingForReceiveCount: number /* 未接手的事件数 */; processEventConfirm: boolean /* 同步确认处理中事件下的告警 */; waitingForReceiveConfirm: boolean /* 同步确认未接手事件下的告警并将事件状态置为处理中 */; assignedToEvent: boolean /* 分配告警到事件 */; createEvent: boolean /* 创建事件 */ }>>({
    url: `${SERVER.EVENT_CENTER}/alert/alert_board/confirm`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["processEventConfirm", "waitingForReceiveConfirm", "assignedToEvent", "createEvent"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { alertIds: data.ids }),
  });
}
/* TODO: 服务请求 */
export enum serviceState {
  NOT_STARTED = "NOT_STARTED",
  PROCESSING = "PROCESSING",
  COMPLETED = "COMPLETED",
  CLOSED = "CLOSED",
  AUTO_CLOSED = "AUTO_CLOSED",
  SUSPENDED = "SUSPENDED",
  PENDING_APPROVAL = "PENDING_APPROVAL",
  NEW = "NEW",
  CUSTOMER_SUSPENDED = "CUSTOMER_SUSPENDED", //客户挂起
  SERVICE_PROVIDER_SUSPENDED = "SERVICE_PROVIDER_SUSPENDED",
}

export const serviceStateOption: { label: string; value: keyof typeof serviceState; color?: string; type?: undefined | "success" | "warning" | "info" | "danger" }[] = [
  // { label: "未分配", value: eventState.UNASSIGNED, color: "#ED4013", type: "danger" },
  { label: t("event.新建"), value: eventState.NEW, color: "#ED4013", type: "danger" },
  // { label: "待处理", value: eventState.WAITING_FOR_RECEIVE, color: "#FF7D00", type: "danger" },
  // { label: "未开始", value: serviceState.NOT_STARTED, color: "#3EBE6B", type: "danger" },
  { label: t("event.处理中"), value: eventState.PROCESSING, color: "#2CB6F4", type: "success" },
  { label: t("event.挂起待审批中"), value: eventState.PENDING_APPROVAL, color: "#3EBE6B", type: void 0 },
  { label: t("event.挂起中"), value: eventState.SUSPENDED, color: "#3EBE6B", type: "warning" },
  // { label: "客户挂起", value: eventState.CUSTOMER_SUSPENDED, color: "#3EBE6B", type: "warning" },
  // { label: "供应商挂起", value: eventState.SERVICE_PROVIDER_SUSPENDED, color: "#3EBE6B", type: "warning" },
  { label: t("event.完成"), value: eventState.COMPLETED, color: "#3EBE6B", type: void 0 },
  { label: t("event.关闭"), value: eventState.CLOSED, color: "#3EBE6B", type: void 0 },
  { label: t("event.自动关闭"), value: eventState.AUTO_CLOSED, color: "#3EBE6B", type: void 0 },
];

// TAKE_OVER :处理 DELIVER_TO :转交 FINISHED :完成 CLOSE :关闭

export enum serviceOperation {
  TAKE_OVER = "TAKE_OVER",
  DELIVER_TO = "DELIVER_TO",
  FINISHED = "FINISHED",
  CLOSE = "CLOSE",
  CUSTOMER_SUSPENDED = "CUSTOMER_SUSPENDED",
  SERVICE_PROVIDER_SUSPENDED = "SERVICE_PROVIDER_SUSPENDED",
}

export const serviceOperationOption = [
  { label: "处理", value: serviceOperation.TAKE_OVER },
  { label: "转交", value: serviceOperation.DELIVER_TO },
  { label: "完成", value: serviceOperation.FINISHED },
  { label: "关闭", value: serviceOperation.CLOSE },
  { label: "客户挂起", value: serviceOperation.CUSTOMER_SUSPENDED },
  { label: "供应商挂起", value: serviceOperation.SERVICE_PROVIDER_SUSPENDED },
];

/**
 * @description 服务请求
 */
export interface ServiceItem {
  /** id */
  id: /* Integer */ string;
  /** 工单 */
  identifier: string;
  /** 工单类型 */
  orderType: /* 枚举: EVENT_ORDER :事件单 | SERVICE_REQUEST :服务请求 | CHANGE :变更 | QUESTION :问题 | PUBLISH :发布 */ import("./association").OrderType.SERVICE_REQUEST;
  /** 服务状态 */
  serviceState: serviceState;
  /** 当前挂起请求时间戳 */
  currentSuspendRequestTime: string;
  /** 当前挂起暂停时间戳 */
  currentSuspendPauseTime: string;
  /** 当前是否有需要审批的挂起 */
  pendingSuspend: boolean;
  /** 响应时间(目前已过时间) */
  responseTime: /* Integer */ string;
  /** 响应时限(配置的响应时间) */
  responseLimit: /* Integer */ string;
  /** 解决时间(目前已过时间) */
  resolveTime: /* Integer */ string;
  /** 解决时限(配置的解决时间) */
  resolveLimit: /* Integer */ string;
  /** 事件优先级 */
  priority: priority;
  /** 摘要 */
  title: string;
  /** 服务请求摘要 */
  serviceRequestDesc: string;
  /** 告警数量 */
  alarmCount?: /* Integer */ string;
  /** 设备名称 */
  deviceId: string[];
  /** 自动关闭时间标识 */
  autoCloseTimeFlag: boolean;
  /** 外部ID */
  externalId: string;
  /** 描述 */
  description: string;
  /** 事件负责人id */
  responsibleId: /* Integer */ string;
  /** 事件负责人名称 */
  responsibleName: string;
  /** 当前处理人id */
  currentOwnerId: /* Integer */ string;
  /** 当前处理人名称 */
  currentOwnerName: string;
  /** 回显展示用户组id 当转交到用户时，需要保存用户所属用户组id，用于回显 */
  displayUserGroupId: /* Integer */ string;
  /** 完结代码名称 */
  finishCodeName: string;
  /** 完结代码描述 */
  finishCodeDesc: string;
  /** 完结内容 */
  finishContent: string;
  /** 日志 */
  serviceLogs: { desc: string; finishCode: string; operateTime: /* Integer */ string; operator: string }[];
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** sla id */
  slaId: /* Integer */ string;
  /** 租户名称 */
  tenantName: string;
  /** 租户缩写 */
  tenantAbbreviation: string;
  /** 联系人列表 */
  contacts: { contactId: string; contactType: /* 枚举: Notification :通知联系人 | Technical :技术联系人 | OnSite :现场联系人 */ "Notification" | "Technical" | "OnSite" }[];
  /** 重要性 */
  importance: deviceImportance;
  /** 紧急性 */
  severity: eventSeverity;
  /** 工单影响性 */
  influence: deviceImportance;
  /** 工单紧急性 */
  urgency: deviceImportance;
  /** 创建时间 */
  createTime: string;
  /** 修改时间 */
  updateTime: string;
  /** 创建人 */
  createdBy: string;
  /** 修改人 */
  updatedBy: string;
  /** 操作 */
  operation: serviceOperation;
}
// export function getServiceList /* 获取模块 */(data: { type: "list" | "myList" } & RequestBase) {
//   const params = new URLSearchParams({ pageNumber: String(data.paging?.pageNumber || 0), pageSize: String(data.paging?.pageSize || 0) });
//   (data.sort instanceof Array ? data.sort : []).forEach((v) => params.append("sort", v));

//   bindSearchParams({ identifier: data.identifier /* id/工单号 */, serviceState: data.serviceState /* 服务状态 */, priority: data.priority /* 优先级 */, deviceId: data.deviceId /* 设备id */, serviceRequestDesc: data.title /* 摘要 */, tenantId: data.tenantId /* 租户ID */, responsibleName: data.responsibleName /* 负责人 */, currentOwnerName: data.currentOwnerName /* 当前处理人 */, permissionId: "612917477428428800" }, params);
//   bindSearchParams({ createTimeStart: ((data.createTime as Record<string, string>) || {}).start, createTimeEnd: ((data.createTime as Record<string, string>) || {}).end }, params);
//   bindSearchParams({ updateTimeStart: ((data.updateTime as Record<string, string>) || {}).start, updateTimeEnd: ((data.updateTime as Record<string, string>) || {}).end }, params);
//   return request<unknown, Response<ServiceItem[]>>({
//     url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.type}`,
//     method: Method.Get,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     params,
//     data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
//   });
// }

// type Has = HasConditionFilter<{}, ConditionFilter<"tenantName" | "orderIds" | "states" | "actorName" | "responsibleName" | "orderSummary", "include" | "exclude" | "eq" | "ne", "FilterRelation", "AND" | "OR"> & ConditionFilter<"alarmCount", "eq" | "ne" | "ge" | "gt" | "le" | "lt" | "isNull" | "isNotNull", "FilterRelation", "AND" | "OR">>;
interface ServiceQuery {
  /** id/工单号 */
  identifier?: string;
  /** 服务状态 NEW :新建 PROCESSING :处理中 COMPLETED :完成 AUTO_CLOSED :自动关闭 CLOSED :关闭 */
  serviceState?: string;
  /** 优先级 */
  priority?: string;
  state?: string;
  /** 设备id */
  deviceId?: string;
  /** 摘要 */
  serviceRequestDesc?: string;
  /** 租户ID */
  tenantId?: string;
  /** 租户名称 */
  tenantName?: string;
  /** 负责人 */
  responsibleName?: string;
  /** 当前处理人 */
  currentOwnerName?: string;

  boardOrNot?: string;
  permissionId?: string;
}
/**
 * @description 分页展示全部服务请求列表
 * @url http://*************:3000/project/17/interface/api/619
 */
export function getServiceList(req: { type: "myList" | "list" } & Record<string, any> & ServiceQuery & PageFilter & DateRangeFilter & Merge<ConditionFilter<"tenantName" | "orderId" | "state" | "actorName" | "responsibleName" | "orderSummary", "include" | "exclude" | "eq" | "ne", "FilterRelation", "AND" | "OR">, ConditionFilter<"alarmCount", "eq" | "ne" | "ge" | "gt" | "le" | "lt" | "isNull" | "isNotNull", "FilterRelation", "AND" | "OR">> & ConditionFilter<"compactActorName", "eq" | "ne" | "include" | "exclude", "FilterRelation", "AND" | "OR"> & Record<string, any>) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${req.type}`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj(
          {
            pageNumber: req.paging.pageNumber,
            pageSize: req.paging.pageSize,
            sort: req.sort,
            /*  */
            identifier: req.identifier /* id/工单号 */,
            serviceState: !req.state ? req.serviceState : void 0 /* 服务状态NEW :新建PROCESSING :处理中COMPLETED :完成AUTO_CLOSED :自动关闭CLOSED :关闭 */,
            priority: req.priority /* 优先级 */,
            queryType: req.queryType /* 工单参数 */,
            state: req.state,
            deviceId: req.deviceId /* 设备id */,
            serviceRequestDesc: req.serviceRequestDesc /* 摘要 */,
            tenantId: req.tenantId /* 租户ID */,
            tenantName: req.tenantName /* 租户名称 */,
            responsibleName: req.responsibleName /* 负责人 */,
            currentOwnerName: req.currentOwnerName /* 当前处理人 */,

            /* 创建时间 */
            ...(req.createTimeStart && req.createTimeEnd ? { createTimeStart: req.createTimeStart, createTimeEnd: req.createTimeEnd } : {}),

            /* 修改时间 */
            ...(req.updateTimeStart && req.updateTimeEnd ? { updateTimeStart: req.updateTimeStart, updateTimeEnd: req.updateTimeEnd } : {}),

            /* 创建时间AND修改时间 */
            ...(req.compactTimeStart && req.compactTimeEnd ? { compactTimeStart: req.compactTimeStart, compactTimeEnd: req.compactTimeEnd } : {}),

            /* 租户名称 */
            ...([...(req.includeTenantName instanceof Array ? req.includeTenantName : []), ...(req.excludeTenantName instanceof Array ? req.excludeTenantName : []), ...(req.eqTenantName instanceof Array ? req.eqTenantName : []), ...(req.neTenantName instanceof Array ? req.neTenantName : [])].filter((v) => v).length ? { tenantNameFilterRelation: req.tenantNameFilterRelation === "OR" ? "OR" : "AND", includeTenantName: req.includeTenantName instanceof Array && req.includeTenantName.length ? req.includeTenantName.join(",") : void 0, excludeTenantName: req.excludeTenantName instanceof Array && req.excludeTenantName.length ? req.excludeTenantName.join(",") : void 0, eqTenantName: req.eqTenantName instanceof Array && req.eqTenantName.length ? req.eqTenantName.join(",") : void 0, neTenantName: req.neTenantName instanceof Array && req.neTenantName.length ? req.neTenantName.join(",") : void 0 } : {}),

            ...([...(req.includeServiceCodes instanceof Array ? req.includeServiceCodes : []), ...(req.excludeServiceCodes instanceof Array ? req.excludeServiceCodes : []), ...(req.eqServiceCodes instanceof Array ? req.eqServiceCodes : []), ...(req.neServiceCodes instanceof Array ? req.neServiceCodes : [])].filter((v) => v).length ? { serviceCodesFilterRelation: req.serviceCodesFilterRelation === "OR" ? "OR" : "AND", includeServiceCodes: req.includeServiceCodes instanceof Array && req.includeServiceCodes.length ? req.includeServiceCodes.join(",") : void 0, excludeServiceCodes: req.excludeServiceCodes instanceof Array && req.excludeServiceCodes.length ? req.excludeServiceCodes.join(",") : void 0, eqServiceCodes: req.eqServiceCodes instanceof Array && req.eqServiceCodes.length ? req.eqServiceCodes.join(",") : void 0, neServiceCodes: req.neServiceCodes instanceof Array && req.neServiceCodes.length ? req.neServiceCodes.join(",") : void 0 } : {}),

            /* 工单号 */
            ...([...(req.includeOrderId instanceof Array ? req.includeOrderId : []), ...(req.excludeOrderId instanceof Array ? req.excludeOrderId : []), ...(req.eqOrderId instanceof Array ? req.eqOrderId : []), ...(req.neOrderId instanceof Array ? req.neOrderId : [])].filter((v) => v).length ? { orderIdFilterRelation: req.orderIdFilterRelation === "OR" ? "OR" : "AND", includeOrderIds: req.includeOrderId instanceof Array && req.includeOrderId.length ? req.includeOrderId.join(",") : void 0, excludeOrderIds: req.excludeOrderId instanceof Array && req.excludeOrderId.length ? req.excludeOrderId.join(",") : void 0, eqOrderIds: req.eqOrderId instanceof Array && req.eqOrderId.length ? req.eqOrderId.join(",") : void 0, neOrderIds: req.neOrderId instanceof Array && req.neOrderId.length ? req.neOrderId.join(",") : void 0 } : {}),

            /* 工单摘要 */
            ...([...(req.includeOrderSummary instanceof Array ? req.includeOrderSummary : []), ...(req.excludeOrderSummary instanceof Array ? req.excludeOrderSummary : []), ...(req.eqOrderSummary instanceof Array ? req.eqOrderSummary : []), ...(req.neOrderSummary instanceof Array ? req.neOrderSummary : [])].filter((v) => v).length ? { orderSummaryFilterRelation: req.orderSummaryFilterRelation === "OR" ? "OR" : "AND", includeOrderSummary: req.includeOrderSummary instanceof Array && req.includeOrderSummary.length ? req.includeOrderSummary.join(",") : void 0, excludeOrderSummary: req.excludeOrderSummary instanceof Array && req.excludeOrderSummary.length ? req.excludeOrderSummary.join(",") : void 0, eqOrderSummary: req.eqOrderSummary instanceof Array && req.eqOrderSummary.length ? req.eqOrderSummary.join(",") : void 0, neOrderSummary: req.neOrderSummary instanceof Array && req.neOrderSummary.length ? req.neOrderSummary.join(",") : void 0 } : {}),

            /* 状态 */
            ...([...(req.includeState instanceof Array ? req.includeState : []), ...(req.excludeState instanceof Array ? req.excludeState : []), ...(req.eqState instanceof Array ? req.eqState : []), ...(req.neState instanceof Array ? req.neState : [])].filter((v) => v).length ? { stateFilterRelation: req.stateFilterRelation === "OR" ? "OR" : "AND", includeStates: req.includeState instanceof Array && req.includeState.length ? req.includeState.join(",") : void 0, excludeStates: req.excludeState instanceof Array && req.excludeState.length ? req.excludeState.join(",") : void 0, eqStates: req.eqState instanceof Array && req.eqState.length ? req.eqState.join(",") : void 0, neStates: req.neState instanceof Array && req.neState.length ? req.neState.join(",") : void 0 } : {}),

            /* 告警数 */
            ...([...(req.eqAlarmCount instanceof Array ? req.eqAlarmCount : []), ...(req.neAlarmCount instanceof Array ? req.neAlarmCount : []), ...(req.geAlarmCount instanceof Array ? req.geAlarmCount : []), ...(req.gtAlarmCount instanceof Array ? req.gtAlarmCount : []), ...(req.leAlarmCount instanceof Array ? req.leAlarmCount : []), ...(req.ltAlarmCount instanceof Array ? req.ltAlarmCount : []), ...(req.isNullAlarmCount instanceof Array ? req.isNullAlarmCount : []), ...(req.isNotNullAlarmCount instanceof Array ? req.isNotNullAlarmCount : [])].filter((v) => v).length ? { alarmCountFilterRelation: req.alarmCountFilterRelation === "OR" ? "OR" : "AND", eqAlarmCount: req.eqAlarmCount instanceof Array && req.eqAlarmCount.length ? req.eqAlarmCount.join(",") : void 0, neAlarmCount: req.neAlarmCount instanceof Array && req.neAlarmCount.length ? req.neAlarmCount.join(",") : void 0, geAlarmCount: req.geAlarmCount instanceof Array && req.geAlarmCount.length ? req.geAlarmCount.join(",") : void 0, gtAlarmCount: req.gtAlarmCount instanceof Array && req.gtAlarmCount.length ? req.gtAlarmCount.join(",") : void 0, leAlarmCount: req.leAlarmCount instanceof Array && req.leAlarmCount.length ? req.leAlarmCount.join(",") : void 0, ltAlarmCount: req.ltAlarmCount instanceof Array && req.ltAlarmCount.length ? req.ltAlarmCount.join(",") : void 0, isNullAlarmCount: req.isNullAlarmCount instanceof Array && req.isNullAlarmCount.length ? req.isNullAlarmCount.join(",") : void 0, isNotNullAlarmCount: req.isNotNullAlarmCount instanceof Array && req.isNotNullAlarmCount.length ? req.isNotNullAlarmCount.join(",") : void 0 } : {}),

            /* 处理人 */
            ...([...(req.includeActorName instanceof Array ? req.includeActorName : []), ...(req.excludeActorName instanceof Array ? req.excludeActorName : []), ...(req.eqActorName instanceof Array ? req.eqActorName : []), ...(req.neActorName instanceof Array ? req.neActorName : [])].filter((v) => v).length ? { actorNameFilterRelation: req.actorNameFilterRelation === "OR" ? "OR" : "AND", includeActorName: req.includeActorName instanceof Array && req.includeActorName.length ? req.includeActorName.join(",") : void 0, excludeActorName: req.excludeActorName instanceof Array && req.excludeActorName.length ? req.excludeActorName.join(",") : void 0, eqActorName: req.eqActorName instanceof Array && req.eqActorName.length ? req.eqActorName.join(",") : void 0, neActorName: req.neActorName instanceof Array && req.neActorName.length ? req.neActorName.join(",") : void 0 } : {}),

            /* 负责人 */
            ...([...(req.includeResponsibleName instanceof Array ? req.includeResponsibleName : []), ...(req.excludeResponsibleName instanceof Array ? req.excludeResponsibleName : []), ...(req.eqResponsibleName instanceof Array ? req.eqResponsibleName : []), ...(req.neResponsibleName instanceof Array ? req.neResponsibleName : [])].filter((v) => v).length ? { responsibleNameFilterRelation: req.responsibleNameFilterRelation === "OR" ? "OR" : "AND", includeResponsibleName: req.includeResponsibleName instanceof Array && req.includeResponsibleName.length ? req.includeResponsibleName.join(",") : void 0, excludeResponsibleName: req.excludeResponsibleName instanceof Array && req.excludeResponsibleName.length ? req.excludeResponsibleName.join(",") : void 0, eqResponsibleName: req.eqResponsibleName instanceof Array && req.eqResponsibleName.length ? req.eqResponsibleName.join(",") : void 0, neResponsibleName: req.neResponsibleName instanceof Array && req.neResponsibleName.length ? req.neResponsibleName.join(",") : void 0 } : {}),

            /* 负责人AND处理人 */
            ...([...(req.eqCompactActorName instanceof Array ? req.eqCompactActorName : []), ...(req.neCompactActorName instanceof Array ? req.neCompactActorName : []), ...(req.includeCompactActorName instanceof Array ? req.includeCompactActorName : []), ...(req.excludeCompactActorName instanceof Array ? req.excludeCompactActorName : [])].filter((v) => v).length ? { compactActorNameFilterRelation: req.compactActorNameFilterRelation === "OR" ? "OR" : "AND", compactEqActorName: req.eqCompactActorName instanceof Array && req.eqCompactActorName.length ? req.eqCompactActorName.join(",") : void 0, compactNeActorName: req.neCompactActorName instanceof Array && req.neCompactActorName.length ? req.neCompactActorName.join(",") : void 0, compactIncludeActorName: req.includeCompactActorName instanceof Array && req.includeCompactActorName.length ? req.includeCompactActorName.join(",") : void 0, compactExcludeActorName: req.excludeCompactActorName instanceof Array && req.excludeCompactActorName.length ? req.excludeCompactActorName.join(",") : void 0 } : {}),

            ...([...(req.eqTicketGroupName instanceof Array ? req.eqTicketGroupName : []), ...(req.neTicketGroupName instanceof Array ? req.neTicketGroupName : []), ...(req.inTicketGroupName instanceof Array ? req.inTicketGroupName : []), ...(req.excludeTicketGroupName instanceof Array ? req.excludeTicketGroupName : [])].filter((v) => v).length ? { ticketGroupNameFilterRelation: req.ticketGroupNameFilterRelation === "OR" ? "OR" : "AND", eqTicketGroupName: req.eqTicketGroupName instanceof Array && req.eqTicketGroupName.length ? req.eqTicketGroupName.join(",") : void 0, neTicketGroupName: req.neTicketGroupName instanceof Array && req.neTicketGroupName.length ? req.neTicketGroupName.join(",") : void 0, inTicketGroupName: req.inTicketGroupName instanceof Array && req.inTicketGroupName.length ? req.inTicketGroupName.join(",") : void 0, excludeTicketGroupName: req.excludeTicketGroupName instanceof Array && req.excludeTicketGroupName.length ? req.excludeTicketGroupName.join(",") : void 0 } : {}),

            ...([...(req.eqUserGroupName instanceof Array ? req.eqUserGroupName : []), ...(req.neUserGroupName instanceof Array ? req.neUserGroupName : []), ...(req.inUserGroupName instanceof Array ? req.inUserGroupName : []), ...(req.excludeUserGroupName instanceof Array ? req.excludeUserGroupName : [])].filter((v) => v).length ? { userGroupNameFilterRelation: req.userGroupNameFilterRelation === "OR" ? "OR" : "AND", eqUserGroupName: req.eqUserGroupName instanceof Array && req.eqUserGroupName.length ? req.eqUserGroupName.join(",") : void 0, neUserGroupName: req.neUserGroupName instanceof Array && req.neUserGroupName.length ? req.neUserGroupName.join(",") : void 0, inUserGroupName: req.inUserGroupName instanceof Array && req.inUserGroupName.length ? req.inUserGroupName.join(",") : void 0, excludeUserGroupName: req.excludeUserGroupName instanceof Array && req.excludeUserGroupName.length ? req.excludeUserGroupName.join(",") : void 0 } : {}),

            ...([...(req.compactEqTicketName instanceof Array ? req.compactEqTicketName : []), ...(req.compactNeTicketName instanceof Array ? req.compactNeTicketName : []), ...(req.compactIncludeTicketName instanceof Array ? req.compactIncludeTicketName : []), ...(req.compactExcludeTicketName instanceof Array ? req.compactExcludeTicketName : [])].filter((v) => v).length ? { compactTicketNameFilterRelation: req.compactTicketNameFilterRelation === "OR" ? "OR" : "AND", compactEqTicketName: req.compactEqTicketName instanceof Array && req.compactEqTicketName.length ? req.compactEqTicketName.join(",") : void 0, compactNeTicketName: req.compactNeTicketName instanceof Array && req.compactNeTicketName.length ? req.compactNeTicketName.join(",") : void 0, compactIncludeTicketName: req.compactIncludeTicketName instanceof Array && req.compactIncludeTicketName.length ? req.compactIncludeTicketName.join(",") : void 0, compactExcludeTicketName: req.compactExcludeTicketName instanceof Array && req.compactExcludeTicketName.length ? req.compactExcludeTicketName.join(",") : void 0 } : {}),

            boardOrNot: req.boardOrNot,
            permissionId: req.permissionId || (await import("@/views/pages/permission")).智能事件中心_客户_工单可读,
          },
          $req.params
        );
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, Response<ServiceItem[]>>($req)),
    { controller }
  );
}

export function getServiceData /* 获取模块 */(data: { id: string } & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.id}/detail`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function addServiceData /* 添加模块 */(data: Partial<{ tenantId: string; title: string; description: string; deviceId: string[]; priority: priority; time: { start: string; end: string } }> & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/create`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: ["title", "description", "deviceId", "priority", "expectStartTime", "expectEndTime", "projectId", "projectName", "projectCode", "projectLevel", "range", "serviceCode", "uniformServiceCode", "ticketSubtype", "ticketTemplateId", "ticketClassificationId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { tenantId: data.tenantId, ...((data.time || {}).start && (data.time || {}).end ? { startTime: (data.time || {}).start, endTime: (data.time || {}).end } : {}) }),
  });
}
export function setServiceData /* 更新模块 */(data: Partial<ServiceItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<ServiceItem[]>);
  // return request<unknown, Response<ServiceItem[]>>({
  //   url: `${SERVER.EVENT_CENTER}/module/${data.id}`,
  //   method: Method.Put,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  // });
}
export function modServiceData /* 修改模块 */(data: Partial<ServiceItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<ServiceItem[]>);
  // return request<unknown, Response<ServiceItem[]>>({
  //   url: `${SERVER.EVENT_CENTER}/module/${data.id}`,
  //   method: Method.Patch,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  // });
}
export function delServiceData /* 删除模块 */(data: Partial<ServiceItem> & RequestBase) {
  return Promise.resolve({ success: true, message: "", data: [], page: 1, size: 30, total: 0, req: data } as Response<ServiceItem[]>);
  // return request<unknown, Response<ServiceItem[]>>({
  //   url: `${SERVER.EVENT_CENTER}/module/${data.id}`,
  //   method: Method.Delete,
  //   responseType: "json",
  //   signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
  //   params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  //   data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  // });
}
/*  */
export function getServiceStat /* 统计模块 */(data: { tenantId?: string } & RequestBase) {
  return request<unknown, Response<{ serviceState: serviceState; serviceCount: number }[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.tenantId ? `countAll` : `countMy`}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: { permissionId: "612917477428428800" },
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function serviceApproveHangUp(data: { id: string; approve: boolean } & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.id}/approveHangUp`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { approve: data.approve },
    data: {},
  });
}

export interface AlertBoardConfigItem {
  id: string /* 主键 */;
  version: string;
  createdTime?: string /* 创建时间 */;
  updatedTime?: string /* 更新时间 */;
  tenantId: string /* 租户ID */;
  userId: string /* 用户ID */;
  queryHours: string /* 查询时间,小时数 */;
  eventSeveritys?: /* 包括的告警等级 */ string[];
}

export function getAlertBoardConfig(data: {} & RequestBase) {
  return request<unknown, Response<AlertBoardConfigItem>>({
    url: `${SERVER.EVENT_CENTER}/alert/alertBoardQuery`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function setAlertBoardConfig(data: {} & RequestBase) {
  return request<unknown, Response<AlertBoardConfigItem>>({
    url: `${SERVER.EVENT_CENTER}/alert/alertBoardSet`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { ...data },
  });
}
