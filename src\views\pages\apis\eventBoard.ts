/* eslint-disable @typescript-eslint/no-unused-vars */
import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";

import { priority, priorityOption } from "./eventPriority";
export { priority, priorityOption };
import { deviceImportance, deviceImportanceOption } from "./device";
export { deviceImportance, deviceImportanceOption };
import { EventItem, ServiceItem } from "./event";
import { QuestionItem } from "./question";

import axios from "axios";

import { i18n } from "@/lang/index";
// import { useI18n } from "vue-i18n";
// const { t } = useI18n();
// export { QuestionItem };

// export { EventItem, ServiceItem };

export enum eventState {
  UNASSIGNED = "UNASSIGNED",
  WAITING_FOR_RECEIVE = "WAITING_FOR_RECEIVE",
  PROCESSING = "PROCESSING",
  PENDING_APPROVAL = "PENDING_APPROVAL",
  SUSPENDED = "SUSPENDED",
  COMPLETED = "COMPLETED",
  CLOSED = "CLOSED",
}

export const eventStateOption: { label: string; value: keyof typeof eventState; color?: string; type?: "default" | "success" | "warning" | "info" | "text" | "primary" | "danger" }[] = [
  { label: "未分配", value: eventState.UNASSIGNED, color: "#ED4013", type: "default" },
  { label: "待处理", value: eventState.WAITING_FOR_RECEIVE, color: "#FF7D00", type: "danger" },
  { label: "处理中", value: eventState.PROCESSING, color: "#2CB6F4", type: "success" },
  { label: "挂起待审批中", value: eventState.PENDING_APPROVAL, color: "#3EBE6B", type: "default" },
  { label: "挂起中", value: eventState.SUSPENDED, color: "#3EBE6B", type: "warning" },
  { label: "完成", value: eventState.COMPLETED, color: "#3EBE6B", type: "default" },
  { label: "关闭", value: eventState.CLOSED, color: "#3EBE6B", type: "default" },
];
export enum eventSeverity {
  Critical = "Critical",
  Major = "Major",
  Minor = "Minor",
  Warning = "Warning",
  Normal = "Normal",
  Unknown = "Unknown",
  Informational = "Informational",
  Calculating = "Calculating",
  Symptom = "Symptom",
  Monitoring = "Monitoring",
  Others = "Others",
}
export const eventSeverityOption: { label: string; value: keyof typeof eventSeverity; color?: string }[] = [
  { label: "Critical", value: eventSeverity.Critical, color: "#db3328" },
  { label: "Major", value: eventSeverity.Major, color: "#f0ad4e" },
  { label: "Minor", value: eventSeverity.Minor, color: "#e9d310" },
  { label: "Warning", value: eventSeverity.Warning, color: "#31b0d5" },
  { label: "Normal", value: eventSeverity.Normal, color: "#5cb85c" },
  { label: "Unknown", value: eventSeverity.Unknown, color: "#d3d3d3" },
  { label: "Informational", value: eventSeverity.Informational, color: "#d3d3d3" },
  { label: "Calculating", value: eventSeverity.Calculating, color: "#bd9fd9" },
  { label: "Symptom", value: eventSeverity.Symptom, color: "#d3d3d3" },
  { label: "Monitoring", value: eventSeverity.Monitoring, color: "#d3d3d3" },
  { label: "Others", value: eventSeverity.Others },
];
export enum sourceType {
  STANDARD = "STANDARD" /* 标准集成 */,
  NET_CARE = "NET_CARE" /* NetCare */,
  PROMETHEUS = "PROMETHEUS" /* prometheus */,
  N9E = "N9E" /* 夜莺V6 */,
  IDEAL_METRIC = "IDEAL_METRIC" /* IdealMetric */,
  UNKNOWN = "UNKNOWN" /* Unknown */,
}
export const sourceTypeOption: { label: string; value: keyof typeof sourceType }[] = [
  { label: "标准集成", value: sourceType.STANDARD },
  { label: "NetCare", value: sourceType.NET_CARE },
  { label: "Prometheus", value: sourceType.PROMETHEUS },
  { label: "夜莺V6", value: sourceType.N9E },
  { label: "IdealMetric", value: sourceType.IDEAL_METRIC },
  { label: "Unknown", value: sourceType.UNKNOWN },
];
/* TODO: 服务请求 */
export enum serviceState {
  NOT_STARTED = "NOT_STARTED",
  PROCESSING = "PROCESSING",
  COMPLETED = "COMPLETED",
  CLOSED = "CLOSED",
}

export const serviceStateOption: { label: string; value: keyof typeof serviceState; color?: string; type?: string }[] = [
  { label: "未开始", value: serviceState.NOT_STARTED, color: "#", type: "danger" },
  { label: "处理中", value: serviceState.PROCESSING, color: "#", type: "warning" },
  { label: "完成", value: serviceState.COMPLETED, color: "#", type: "success" },
  { label: "结束", value: serviceState.CLOSED, color: "#", type: "info" },
];

export function getEventList /* 获取模块 */(data: { type: "list" | "myList"; excludeTenantIds: string[]; includeTenantIds: string[]; excludeOrderIds: string[]; includeOrderIds: string[]; eqTenantName: string[]; neTenantName: string[] } & RequestBase) {
  const params = new URLSearchParams({ pageNumber: String(data.paging?.pageNumber || 0), pageSize: String(data.paging?.pageSize || 0) });
  (data.sort instanceof Array ? data.sort : []).forEach((v) => params.append("sort", v));
  bindSearchParams({ identifier: data.identifier, eventState: data.eventState, summary: data.summary, priority: data.priority, tenantId: data.tenantId, responsibleName: data.responsibleName, actorName: data.actorName, boardOrNot: data.boardOrNot, permissionId: "612917424815079424" }, params);
  bindSearchParams({ createTimeStart: ((data.createTime as Record<string, string>) || {}).start, createTimeEnd: ((data.createTime as Record<string, string>) || {}).end }, params);
  bindSearchParams({ updateTimeStart: ((data.updateTime as Record<string, string>) || {}).start, updateTimeEnd: ((data.updateTime as Record<string, string>) || {}).end }, params);

  bindSearchParams(
    {
      excludeTenantName: data.excludeTenantIds != undefined ? data.excludeTenantIds.join(",") : [],
      includeTenantName: data.includeTenantIds != undefined ? data.includeTenantIds.join(",") : [],
      eqTenantName: data.eqTenantName != undefined ? data.eqTenantName.join() : [],
      neTenantName: data.neTenantName != undefined ? data.neTenantName.join() : [],
      tenantIdFilterRelation: data.tenantIdFilterRelation,
      excludeOrderIds: data.excludeOrderIds != undefined ? data.excludeOrderIds.join(",") : [],
      includeOrderIds: data.includeOrderIds != undefined ? data.includeOrderIds.join(",") : [],
      orderIdFilterRelation: data.orderIdFilterRelation,
    },
    params
  );

  return request<unknown, Response<EventItem[]>>({
    url: `${SERVER.EVENT_CENTER}/event/${data.type}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: {},
  });
}

export function getServiceList /* 获取模块 */(data: { type: "list" | "myList"; excludeTenantIds: string[]; includeTenantIds: string[]; excludeOrderIds: string[]; includeOrderIds: string[]; eqTenantName: string[]; neTenantName: string[] } & RequestBase) {
  const params = new URLSearchParams({ pageNumber: String(data.paging?.pageNumber || 0), pageSize: String(data.paging?.pageSize || 0) });
  (data.sort instanceof Array ? data.sort : []).forEach((v) => params.append("sort", v));
  type TimeType = { start: number; end: number };
  bindSearchParams(
    {
      identifier: data.identifier /* id/工单号 */,
      boardOrNot: data.boardOrNot /*是否事件看板 */,
      serviceState: data.serviceState /* 服务状态 */,
      priority: data.priority /* 优先级 */,
      deviceId: data.deviceId /* 设备id */,
      serviceRequestDesc: data.title /* 摘要 */,
      tenantId: data.tenantId /* 租户ID */,
      createTimeStart: ((data.createTime as Record<string, string>) || {}).start /* 创建时间起始时间 */,
      createTimeEnd: ((data.createTime as Record<string, string>) || {}).end /* 创建时间结束时间 */,
      updateTimeStart: ((data.updateTime as Record<string, string>) || {}).start /* 修改时间起始时间 */,
      updateTimeEnd: ((data.updateTime as Record<string, string>) || {}).end /* 修改时间结束时间 */,
      responsibleName: data.responsibleName /* 负责人 */,
      currentOwnerName: data.actorName /* 当前处理人 */,
      tenantName: data.tenantName,
      actorName: data.actorName,
      excludeTenantName: data.excludeTenantIds != undefined ? data.excludeTenantIds.join() : [],
      includeTenantName: data.includeTenantIds != undefined ? data.includeTenantIds.join() : [],
      eqTenantName: data.eqTenantName != undefined ? data.eqTenantName.join() : [],
      neTenantName: data.neTenantName != undefined ? data.neTenantName.join() : [],
      tenantIdFilterRelation: data.tenantIdFilterRelation,
      excludeOrderIds: data.excludeOrderIds != undefined ? data.excludeOrderIds.join() : [],
      includeOrderIds: data.includeOrderIds != undefined ? data.includeOrderIds.join() : [],
      orderIdFilterRelation: data.orderIdFilterRelation,
      permissionId: "612917477428428800",

      // title: data.title,
    },
    params
  );

  return request<unknown, Response<ServiceItem[]>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.type}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

export enum questionState {
  NOT_STARTED = "NOT_STARTED",
  PROCESSING = "PROCESSING",
  COMPLETED = "COMPLETED",
  CLOSED = "CLOSED",
}

export const questionStateOption: { label: string; value: keyof typeof questionState; color?: string; type?: "default" | "success" | "warning" | "info" | "text" | "primary" | "danger" }[] = [
  { label: "未开启", value: questionState.NOT_STARTED, color: "#ED4013", type: "default" },
  { label: "处理中", value: questionState.PROCESSING, color: "#2CB6F4", type: "success" },
  { label: "完成", value: questionState.COMPLETED, color: "#3EBE6B", type: "default" },
  { label: "关闭", value: questionState.CLOSED, color: "#3EBE6B", type: "default" },
];
export function getQuestionList(data: { pageNumber: number; pageSize: number; excludeTenantIds: string[]; includeTenantIds: string[]; excludeOrderIds: string[]; includeOrderIds: string[]; eqTenantName: string[]; neTenantName: string[] } & RequestBase) {
  const params = new URLSearchParams({ pageNumber: String(data.pageNumber || 0), pageSize: String(data.pageSize || 0) });
  (data.sort instanceof Array ? data.sort : []).forEach((v) => params.append("sort", v));
  bindSearchParams({ identifier: data.identifier, userId: data.userId, questionState: data.questionState, digest: data.digest, priority: data.priority, tenantId: data.tenantId, responsibleName: data.responsibleName, actorName: data.actorName, boardOrNot: data.boardOrNot, tenantName: data.tenantName, permissionId: "612917559993303040" }, params);
  bindSearchParams({ createTimeStart: ((data.createTime as Record<string, string>) || {}).start, createTimeEnd: ((data.createTime as Record<string, string>) || {}).end }, params);
  bindSearchParams({ updateTimeStart: ((data.updateTime as Record<string, string>) || {}).start, updateTimeEnd: ((data.updateTime as Record<string, string>) || {}).end }, params);
  bindSearchParams(
    {
      excludeTenantName: data.excludeTenantIds != undefined ? data.excludeTenantIds.join() : [],
      includeTenantName: data.includeTenantIds != undefined ? data.includeTenantIds.join() : [],
      eqTenantName: data.eqTenantName != undefined ? data.eqTenantName.join() : [],
      neTenantName: data.neTenantName != undefined ? data.neTenantName.join() : [],
      tenantIdFilterRelation: data.tenantIdFilterRelation,
      excludeOrderIds: data.excludeOrderIds != undefined ? data.excludeOrderIds.join() : [],
      includeOrderIds: data.includeOrderIds != undefined ? data.includeOrderIds.join() : [],
      orderIdFilterRelation: data.orderIdFilterRelation,
    },
    params
  );
  return request<unknown, Response<QuestionItem[]>>({
    url: `${SERVER.EVENT_CENTER}/question/allList`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

export function getMyQuestionList(data: { pageNumber: number; pageSize: number } & RequestBase) {
  const params = new URLSearchParams({ pageNumber: String(data.pageNumber || 0), pageSize: String(data.pageSize || 0) });
  (data.sort instanceof Array ? data.sort : []).forEach((v) => params.append("sort", v));
  bindSearchParams({ identifier: data.identifier, userId: data.userId, questionState: data.questionState, digest: data.digest, priority: data.priority, tenantId: data.tenantId, responsibleName: data.responsibleName, actorName: data.actorName, boardOrNot: data.boardOrNot, tenantName: data.tenantName, permissionId: "612917559993303040" }, params);
  bindSearchParams({ createTimeStart: ((data.createTime as Record<string, string>) || {}).start, createTimeEnd: ((data.createTime as Record<string, string>) || {}).end }, params);
  bindSearchParams({ updateTimeStart: ((data.updateTime as Record<string, string>) || {}).start, updateTimeEnd: ((data.updateTime as Record<string, string>) || {}).end }, params);

  return request<unknown, Response<QuestionItem[]>>({
    url: `${SERVER.EVENT_CENTER}/question/allList`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export enum ChangeType {
  ORDINARY = "ORDINARY",
  STANDARD = "STANDARD",
  URGENCY = "URGENCY",
  IMPORTANT = "IMPORTANT",
}

export const changeType = [
  { label: "一般变更", value: ChangeType.ORDINARY },
  { label: "标准变更", value: ChangeType.STANDARD },
  { label: "紧急变更 ", value: ChangeType.URGENCY },
  { label: "重大变更", value: ChangeType.IMPORTANT },
];

export enum changeState {
  UN_APPROVED = "UN_APPROVED",
  SUSPEND = "SUSPEND",
  PROCESSING = "PROCESSING",
  AUTO_CLOSED = "AUTO_CLOSED",
  CLOSED = "CLOSED",
}

export enum changeOperate {
  CLOSED = "CLOSED",
  WITHDRAW = "WITHDRAW",
  ABANDON = "ABANDON",
  COMPLETE = "COMPLETE",
  DISPOSE = "DISPOSE",
}
export const changeStateOption: { label: string; value: keyof typeof changeState; color?: string; type?: undefined | "success" | "warning" | "info" | "danger" | "primary" }[] = [
  // { label: i18n.global.t("event.未审批"), value: changeState.UN_APPROVED, color: "#ED4013", type: "danger" },
  { label: i18n.global.t("event.挂起"), value: changeState.SUSPEND, color: "#2CB6F4", type: "warning" },
  { label: i18n.global.t("event.处理中"), value: changeState.PROCESSING, color: "#3EBE6B", type: "success" },
  { label: i18n.global.t("event.自动关闭"), value: changeState.AUTO_CLOSED, color: "#3EBE6B", type: void 0 },
  { label: i18n.global.t("event.关闭"), value: changeState.CLOSED, color: "#3EBE6B", type: void 0 },
];
export interface Change {
  id: string;
  tenantId: string;
  identifier: string;
  changeType: string;
  digest: string;
  priority: string;
  changeState: string;
  teamId: string;
  userId: string;
  importance: string;
  severity: string;
  startTime: string;
  endTime: string;
  desc: string;
  collectAlert: boolean;
  alertNumber: string;
  contacts: {
    contactId: string;
    contactType: string;
  };
  deviceIds: string[];
  createdBy: string;
  updatedBy: string;
  createdTime: string;
  updatedTime: string;
  externalId: string;
}
export function getChangeItems(
  data: {
    excludeTenantIds: string[];
    includeTenantIds: string[];
    excludeOrderIds: string[];
    includeOrderIds: string[];
    eqTenantName: string[];
    neTenantName: string[];
  } & RequestBase
) {
  const params = new URLSearchParams({ pageNumber: String(data.pageNumber || 0), pageSize: String(data.pageSize || 0) });
  (data.sort instanceof Array ? data.sort : []).forEach((v) => params.append("sort", v));
  bindSearchParams(
    {
      identifier: data.identifier,
      questionState: data.questionState,
      digest: data.digest,
      priority: data.priority,
      tenantId: data.tenantId,
      responsibleName: data.responsibleName,
      actorName: data.actorName,
      tenantName: data.tenantName,
      changeState: data.changeState,
      excludeTenantName: data.excludeTenantIds != undefined ? data.excludeTenantIds.join() : [],
      includeTenantName: data.includeTenantIds != undefined ? data.includeTenantIds.join() : [],
      eqTenantName: data.eqTenantName != undefined ? data.eqTenantName.join() : [],
      neTenantName: data.neTenantName != undefined ? data.neTenantName.join() : [],
      tenantIdFilterRelation: data.tenantIdFilterRelation,
      excludeOrderIds: data.excludeOrderIds != undefined ? data.excludeOrderIds.join() : [],
      includeOrderIds: data.includeOrderIds != undefined ? data.includeOrderIds.join() : [],
      orderIdFilterRelation: data.orderIdFilterRelation,
      boardOrNot: true,
      permissionId: "612917524815675392",
    },
    params
  );
  bindSearchParams({ "createdTimeRange.start": ((data.startTime as Record<string, string>) || {}).start, "createdTimeRange.end": ((data.startTime as Record<string, string>) || {}).end }, params);
  bindSearchParams({ "updatedTimeRange.start": ((data.updatedTime as Record<string, string>) || {}).start, "updatedTimeRange.end": ((data.updatedTime as Record<string, string>) || {}).end }, params);
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    // params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
    params,
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

export function getChangeItemsOwner(data: { excludeTenantIds: string[]; includeTenantIds: string[]; excludeOrderIds: string[]; includeOrderIds: string[] } & RequestBase) {
  const params = new URLSearchParams({ pageNumber: String(data.pageNumber || 0), pageSize: String(data.pageSize || 0) });
  (data.sort instanceof Array ? data.sort : []).forEach((v) => params.append("sort", v));

  bindSearchParams({ identifier: data.identifier, questionState: data.questionState, digest: data.digest, priority: data.priority, tenantId: data.tenantId, responsibleName: data.responsibleName, actorName: data.actorName, tenantName: data.tenantName, changeState: data.changeState, permissionId: "612917524815675392" }, params);
  bindSearchParams({ "createdTimeRange.start": ((data.startTime as Record<string, string>) || {}).start, "createdTimeRange.end": ((data.startTime as Record<string, string>) || {}).end }, params);
  bindSearchParams({ "updatedTimeRange.start": ((data.updatedTime as Record<string, string>) || {}).start, "updatedTimeRange.end": ((data.updatedTime as Record<string, string>) || {}).end }, params);
  bindSearchParams(
    {
      excludeTenantName: data.excludeTenantIds != undefined ? data.excludeTenantIds.join() : [],
      includeTenantName: data.includeTenantIds != undefined ? data.includeTenantIds.join() : [],
      tenantIdFilterRelation: data.tenantIdFilterRelation,
      excludeOrderIds: data.excludeOrderIds != undefined ? data.excludeOrderIds.join() : [],
      includeOrderIds: data.includeOrderIds != undefined ? data.includeOrderIds.join() : [],
      orderIdFilterRelation: data.orderIdFilterRelation,
    },
    params
  );
  return request<unknown, Response<Change>>({
    url: `${SERVER.EVENT_CENTER}/change/owner`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    // params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
    params,
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

export enum eventCount {
  PROCESSING = "PROCESSING",
  SUSPENDED = "SUSPENDED",
  NOT_STARTED = "NOT_STARTED ",
  SUSPEND = "SUSPEND ",
}

export const eventCountSate = {
  PROCESSING: i18n.global.t("eventBoard.Open"),
  SUSPENDED: i18n.global.t("eventBoard.Suspended"),
  SUSPEND: i18n.global.t("eventBoard.Suspended"),
  NOT_STARTED: i18n.global.t("eventBoard.Unopened"),
};
//获取事件统计数据
export async function getEventCount(data: {} & RequestBase) {
  const userInfo = (await import("@/utils/getUserInfo")).default();
  const { 智能事件中心_客户_工单可读, 智能事件中心_项目_工单可读, 智能事件中心_联系人_工单可读, 智能事件中心_设备_工单可读 } = await import("@/views/pages/permission");
  if (userInfo.hasPermission(智能事件中心_客户_工单可读)) {
    return request<unknown, Response<{ eventState: eventState; eventCount: number }[]>>({
      url: ["my", "myList"].includes(data.type as string) ? `${SERVER.EVENT_CENTER}/event/myCount` : `${SERVER.EVENT_CENTER}/event/allCount`,
      method: Method.Get,
      signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
      headers: {},
      params: {
        boardOrNot: typeof data.boardOrNot === "boolean" ? data.boardOrNot : true,
        permissionId: "638966735935897600",
      },
      data: {},
    });
  } else if (
    /*  */
    (userInfo.hasPermission("756061441173225472" as any) && userInfo.hasPermission(智能事件中心_项目_工单可读)) ||
    (userInfo.hasPermission("756062477225033728" as any) && userInfo.hasPermission(智能事件中心_联系人_工单可读)) ||
    (userInfo.hasPermission("756062918394511360" as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))
  ) {
    return request<unknown, Response<{ eventState: eventState; eventCount: number }[]>>({
      url: ["my", "myList"].includes(data.type as string) ? `${SERVER.EVENT_CENTER}/event/myEventCount` : `${SERVER.EVENT_CENTER}/event/allEventCount`,
      method: Method.Post,
      signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
      headers: {},
      params: {},
      data: {
        boardOrNot: typeof data.boardOrNot === "boolean" ? data.boardOrNot : true,
        permissionList: [
          /*  */
          { permissionId: 智能事件中心_项目_工单可读, permissionType: "View Tickets When Project Has View Tickets" },
          { permissionId: 智能事件中心_联系人_工单可读, permissionType: "View Tickets When Contact Has View Tickets" },
          { permissionId: 智能事件中心_设备_工单可读, permissionType: "View Tickets When Device Has View Tickets" },
        ],
      },
    });
  } else return Promise.resolve({ success: true, data: [], message: "没有匹配到url" });
}

//获取事件统计数据
export async function getDictEventCount(req: {} & RequestBase) {
  const userInfo = (await import("@/utils/getUserInfo")).default();
  const { 智能事件中心_客户_工单可读, 智能事件中心_项目_工单可读, 智能事件中心_联系人_工单可读, 智能事件中心_设备_工单可读 } = await import("@/views/pages/permission");
  let url;
  let method;
  let params = {};
  let data = {};
  if (userInfo.hasPermission(智能事件中心_客户_工单可读)) {
    url = ["my", "myList"].includes(req.type as string) ? `${SERVER.EVENT_CENTER}/dict_event/myCount` : `${SERVER.EVENT_CENTER}/dict_event/allCount`;
    method = Method.Get;
    params = {
      boardOrNot: typeof req.boardOrNot === "boolean" ? req.boardOrNot : true,
      permissionId: "638966735935897600",
    };
  } else if (
    /*  */
    (userInfo.hasPermission("756061441173225472" as any) && userInfo.hasPermission(智能事件中心_项目_工单可读)) ||
    (userInfo.hasPermission("756062477225033728" as any) && userInfo.hasPermission(智能事件中心_联系人_工单可读)) ||
    (userInfo.hasPermission("756062918394511360" as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))
  ) {
    url = `${SERVER.EVENT_CENTER}/dict_event/listEventCount`;
    method = Method.Post;
    data = {
      boardOrNot: typeof req.boardOrNot === "boolean" ? req.boardOrNot : true,
      permissionId: "638966735935897600",
      permissionList: [
        /*  */
        { permissionId: 智能事件中心_项目_工单可读, permissionType: "View Tickets When Project Has View Tickets" },
        { permissionId: 智能事件中心_联系人_工单可读, permissionType: "View Tickets When Contact Has View Tickets" },
        { permissionId: 智能事件中心_设备_工单可读, permissionType: "View Tickets When Device Has View Tickets" },
      ],
    };
  }

  if (!url) return { success: true, data: [], message: "" };

  return request<unknown, Response<{ eventState: eventState; eventCount: number }[]>>({
    url,
    method,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}

export async function getRequestCount(req: {} & RequestBase) {
  const userInfo = (await import("@/utils/getUserInfo")).default();
  const { 智能事件中心_客户_工单可读, 智能事件中心_项目_工单可读, 智能事件中心_联系人_工单可读, 智能事件中心_设备_工单可读 } = await import("@/views/pages/permission");
  let url;
  let method;
  let params = {};
  let data = {};

  if (userInfo.hasPermission(智能事件中心_客户_工单可读)) {
    url = ["my", "myList"].includes(req.type as string) ? `${SERVER.EVENT_CENTER}/serviceRequest/countMy` : `${SERVER.EVENT_CENTER}/serviceRequest/countAll`;
    method = Method.Get;
    params = {
      tenantId: req.tenantId,
      boardOrNot: "boardOrNot" in req ? req.boardOrNot : true,
      permissionId: "638966735935897600",
    };
  } else if (
    /*  */
    (userInfo.hasPermission("756061441173225472" as any) && userInfo.hasPermission(智能事件中心_项目_工单可读)) ||
    (userInfo.hasPermission("756062477225033728" as any) && userInfo.hasPermission(智能事件中心_联系人_工单可读)) ||
    (userInfo.hasPermission("756062918394511360" as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))
  ) {
    url = ["my", "myList"].includes(req.type as string) ? `${SERVER.EVENT_CENTER}/serviceRequest/myRequestCount` : `${SERVER.EVENT_CENTER}/serviceRequest/allRequestCount`;
    method = Method.Post;
    data = {
      tenantId: req.tenantId,
      boardOrNot: "boardOrNot" in req ? req.boardOrNot : true,
      permissionId: "638966735935897600",
      permissionList: [
        /*  */
        { permissionId: 智能事件中心_项目_工单可读, permissionType: "View Tickets When Project Has View Tickets" },
        { permissionId: 智能事件中心_联系人_工单可读, permissionType: "View Tickets When Contact Has View Tickets" },
        { permissionId: 智能事件中心_设备_工单可读, permissionType: "View Tickets When Device Has View Tickets" },
      ],
    };
  }

  if (!url) return { success: true, data: [], message: "" };

  return request<unknown, Response<{ serviceState: serviceState; serviceCount: number }[]>>({
    url,
    method,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}

export function getDictRequestCount(data: {} & RequestBase) {
  return request<unknown, Response<{ serviceState: serviceState; serviceCount: number }[]>>({
    url: ["my", "myList"].includes(data.type as string) ? `${SERVER.EVENT_CENTER}/dict_serviceRequest/countMy` : `${SERVER.EVENT_CENTER}/dict_serviceRequest/countAll`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      tenantId: data.tenantId,
      boardOrNot: "boardOrNot" in data ? data.boardOrNot : true,
      permissionId: "638966735935897600",
    },
    data: {},
  });
}

export async function getQuestionCount(req: {} & RequestBase) {
  const userInfo = (await import("@/utils/getUserInfo")).default();
  const { 智能事件中心_客户_工单可读, 智能事件中心_项目_工单可读, 智能事件中心_联系人_工单可读, 智能事件中心_设备_工单可读 } = await import("@/views/pages/permission");
  let url;
  let method;
  let params = {};
  let data = {};

  if (userInfo.hasPermission(智能事件中心_客户_工单可读)) {
    url = ["my", "myList"].includes(req.type as string) ? `${SERVER.EVENT_CENTER}/question/state/myCount` : `${SERVER.EVENT_CENTER}/question/state/allCount`;
    method = Method.Get;
    params = {
      boardOrNot: "boardOrNot" in req ? req.boardOrNot : true,
      permissionId: "612917559993303040",
    };
  } else if (
    /*  */
    (userInfo.hasPermission("756061441173225472" as any) && userInfo.hasPermission(智能事件中心_项目_工单可读)) ||
    (userInfo.hasPermission("756062477225033728" as any) && userInfo.hasPermission(智能事件中心_联系人_工单可读)) ||
    (userInfo.hasPermission("756062918394511360" as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))
  ) {
    url = ["my", "myList"].includes(req.type as string) ? `${SERVER.EVENT_CENTER}/question/state/myQuestCount` : `${SERVER.EVENT_CENTER}/question/state/allQuestCount`;
    method = Method.Post;
    data = {
      boardOrNot: "boardOrNot" in req ? req.boardOrNot : true,
      permissionId: "612917559993303040",
      permissionList: [
        /*  */
        { permissionId: 智能事件中心_项目_工单可读, permissionType: "View Tickets When Project Has View Tickets" },
        { permissionId: 智能事件中心_联系人_工单可读, permissionType: "View Tickets When Contact Has View Tickets" },
        { permissionId: 智能事件中心_设备_工单可读, permissionType: "View Tickets When Device Has View Tickets" },
      ],
    };
  }

  if (!url) return { success: true, data: [], message: "" };

  return request<unknown, Response<{ questionState: questionState; count: number }>>({
    // url: `${SERVER.EVENT_CENTER}/question${data.id ? "/" + data.id : ""}/${data.type}Count`,
    url,
    method,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}
export async function getChangeCount(req: { type: "" | "owner" | "current" } & RequestBase) {
  const userInfo = (await import("@/utils/getUserInfo")).default();
  const { 智能事件中心_客户_工单可读, 智能事件中心_项目_工单可读, 智能事件中心_联系人_工单可读, 智能事件中心_设备_工单可读 } = await import("@/views/pages/permission");
  let url;
  let method;
  let params = {};
  let data = {};

  if (userInfo.hasPermission(智能事件中心_客户_工单可读)) {
    url = `${SERVER.EVENT_CENTER}/change/count_state${req.type ? `/${req.type}` : ""}`;
    method = Method.Get;
    params = {
      boardOrNot: "boardOrNot" in req ? req.boardOrNot : true,
      permissionId: "612917524815675392",
    };
  } else if (
    /*  */
    (userInfo.hasPermission("756061441173225472" as any) && userInfo.hasPermission(智能事件中心_项目_工单可读)) ||
    (userInfo.hasPermission("756062477225033728" as any) && userInfo.hasPermission(智能事件中心_联系人_工单可读)) ||
    (userInfo.hasPermission("756062918394511360" as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))
  ) {
    url = {
      "": `${SERVER.EVENT_CENTER}/change/allChangeCount`,
      "owner": `${SERVER.EVENT_CENTER}/change/myChangeCount`,
    }[req.type];
    method = Method.Post;
    data = {
      boardOrNot: "boardOrNot" in req ? req.boardOrNot : true,
      permissionId: "612917524815675392",
      permissionList: [
        /*  */
        { permissionId: 智能事件中心_项目_工单可读, permissionType: "View Tickets When Project Has View Tickets" },
        { permissionId: 智能事件中心_联系人_工单可读, permissionType: "View Tickets When Contact Has View Tickets" },
        { permissionId: 智能事件中心_设备_工单可读, permissionType: "View Tickets When Device Has View Tickets" },
      ],
    };
  }

  if (!url) return { success: true, data: [], message: "" };

  return request<unknown, Response<{ state: changeState; count: number }[]>>({
    // url: `${SERVER.EVENT_CENTER}/change/count_state${data.type ? "/" + data.type : ""}`,
    url,
    method,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}
export function getOrderType(data: {} & RequestBase) {
  return request<unknown, Response<import("./deviceManage").DataType>>({
    // url: `${SERVER.EVENT_CENTER}/change/count_state${data.type ? "/" + data.type : ""}`,
    url: `${SERVER.EVENT_CENTER}/event/${data.id}/type`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

const source = axios.CancelToken.source();
export function batchCloseEvent(data: {} & RequestBase) {
  return request<unknown, Response<null>>({
    // url: `${SERVER.EVENT_CENTER}/change/count_state${data.type ? "/" + data.type : ""}`,
    url: `${SERVER.EVENT_CENTER}/event/batchClose`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {
      orderIds: data.orderIds,
      completeInfo: data.completeInfo,
    },
    cancelToken: source.token,
  });
}

export function batchCloseService(data: {} & RequestBase) {
  return request<unknown, Response<null>>({
    // url: `${SERVER.EVENT_CENTER}/change/count_state${data.type ? "/" + data.type : ""}`,
    url: `${SERVER.EVENT_CENTER}/serviceRequest/board/batchClose`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {
      orderIds: data.orderIds,
      completeInfo: data.completeInfo,
    },
    cancelToken: source.token,
  });
}

export function batchCloseDictService(data: {} & RequestBase) {
  return request<unknown, Response<null>>({
    // url: `${SERVER.EVENT_CENTER}/change/count_state${data.type ? "/" + data.type : ""}`,
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/board/batchClose`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {
      orderIds: data.orderIds,
      completeInfo: data.completeInfo,
    },
    cancelToken: source.token,
  });
}

export function batchCloseDictEvent(data: {} & RequestBase) {
  return request<unknown, Response<null>>({
    // url: `${SERVER.EVENT_CENTER}/change/count_state${data.type ? "/" + data.type : ""}`,
    url: `${SERVER.EVENT_CENTER}/dict_event/batchClose`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {
      orderIds: data.orderIds,
      completeInfo: data.completeInfo,
    },
    cancelToken: source.token,
  });
}

export function axiosCancel() {
  // 取消之前的请求
  source.cancel("取消之前的请求");
}

export function setEventNotes(data: {} & RequestBase) {
  return request<unknown, Response<Change>>({
    // url: `${SERVER.EVENT_CENTER}/change/count_state${data.type ? "/" + data.type : ""}`,
    url: `${SERVER.EVENT_CENTER}/event/board/batchAddNote`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function setDictEventNotes(data: {} & RequestBase) {
  return request<unknown, Response<Change>>({
    // url: `${SERVER.EVENT_CENTER}/change/count_state${data.type ? "/" + data.type : ""}`,
    url: `${SERVER.EVENT_CENTER}/dict_event/board/batchAddNote`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function setServiceRequestNotes(data: {} & RequestBase) {
  return request<unknown, Response<Change>>({
    // url: `${SERVER.EVENT_CENTER}/change/count_state${data.type ? "/" + data.type : ""}`,
    url: `${SERVER.EVENT_CENTER}/serviceRequest/board/batchAddNote`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function setDictServiceRequestNotes(data: {} & RequestBase) {
  return request<unknown, Response<Change>>({
    // url: `${SERVER.EVENT_CENTER}/change/count_state${data.type ? "/" + data.type : ""}`,
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/board/batchAddNote`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
