import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";

export interface SlaConfigList {
  id: string;

  tenantId: string;
}
//事件处理配置
export function eventHandler(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.EVENT_CENTER}/system_config/create/event_handler`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//事件关闭配置

export function eventClose(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.EVENT_CENTER}/system_config/create/event_close`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//获取默认配置

export function getEventDefault(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.EVENT_CENTER}/system_config/getDefault`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//获取全部配置

export function getAllEventDefault(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.EVENT_CENTER}/system_config/getAll`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
