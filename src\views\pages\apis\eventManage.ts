import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";

import { priority, eventSeverity, deviceImportance, EventOperation } from "./event";

import type { ContactsItem } from "./contacts";
import { DateRange } from "moment-range";
export interface EventCount {
  finishedEventCount: number | string;
  unfinishedEventCount: number | string;
  unassignedEventCount: number | string;
}
/**
 * @name 我的事件统计
 * @export
 * @param {({ userId: string } & RequestBase)} data
 * @return {*}
 */
export function getCurrentUseEventCount(data: { userId: string } & RequestBase) {
  return request<EventCount[]>({
    url: `${SERVER.EVENT_CENTER}/event/${data.userId}/myCount`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

/**
 * @name 全部事件统计
 * @export
 * @param {(object & RequestBase)} data
 * @return {*}
 */
export function getAllEventCount(data: object & RequestBase) {
  return request<EventCount[]>({
    url: `${SERVER.EVENT_CENTER}/event/allCount`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { ...data, permissionId: "612917424815079424" },
    data: {},
  });
}

export interface EventByPage {
  id: number;
  tenantName: number;
  eventType: string;
  responseTime?: string;
  responseLimit?: string;
  resolveTime?: string;
  resolveLimit?: string;
  priority?: string;
  alarmNumber?: string;
  eventName?: string;
  deNoiseName?: string;
  deviceOrServiceName?: string;
  dataSource?: string;
  createTime?: string;
  updateTime?: string;
  currentOwnerId: string;
  tenantId: string;
  currentUserId: string;
  alarmStatus: string;
  notesList?: object[];
  eventDynamicList?: object[];
  alarmRecordList?: object[];
  deviceInfoList?: object[];
  deNoiseDetails?: object[];
  currentHandle?: object[];
}

/**
 * @name 分页展示我的事件列表
 * @export
 * @param {(object & RequestBase)} data
 * @return {*}
 */
export function getCurrentUseEventByPage(data: object & RequestBase) {
  return request<EventByPage[]>({
    url: `${SERVER.EVENT_CENTER}/event/myList`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: data,
  });
}

/**
 * @name 分页展示全部事件列表
 * @export
 * @param {(object & RequestBase)} data
 * @return {*}
 */
export function getAllEventByPage(data: object & RequestBase) {
  return request<EventByPage[]>({
    url: `${SERVER.EVENT_CENTER}/event/list`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: data,
  });
}

/**
 * @name 事件详情
 * @export
 * @param {({ id: string | number } & RequestBase)} data
 * @return {*}
 */
export function getEventDeteil(data: { id: string | number } & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/${data.id}/detail`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

/**
 *
 * @name 新增小记
 * @export
 * @param {({ eventId: number | string; content: string } & RequestBase)} data
 * @return {*}
 */
export function setEventNote(data: { eventId: number | string; content: string } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/event/note`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data,
  });
}

export function setDictEventNote(data: { eventId: number | string; content: string } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/dict_event/note`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data,
  });
}

/**
 *
 * @name 编辑小记
 * @export
 * @param {({ eventId: number | string; content: string } & RequestBase)} data
 * @return {*}
 */
export function editEventNote(
  data: {
    eventId?: number | string;
    noteId?: number | string;
    content: string;
  } & RequestBase
) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/event/editNote`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data,
  });
}

export function editDictEventNote(
  data: {
    eventId?: number | string;
    noteId?: number | string;
    content: string;
  } & RequestBase
) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/dict_event/editNote`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data,
  });
}

/**
 * @name 获取小记
 * @export
 * @param {({ id: string | number } & RequestBase)} data
 * @return {*}
 */
export function getEventQueryNote(data: { id: string | number } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/event/${data.id}/queryNote`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function getDictEventQueryNote(data: { id: string | number } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/queryNote`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

/**
 * @name 删除附件
 * @export
 * @param {({ attachmentUrl: string } & RequestBase)} data
 * @return {*}
 */
export function delNoteFile(data: { attachmentUrl: string } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/file/deleteAttachment`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { ...data },
  });
}

interface Dynamic {
  dynamicTime: string;
  dynamicContent: string;
}
/**
 * @name 事件动态
 * @export
 * @param {({ id: string | number } & RequestBase)} data
 * @return {*}
 */
export function getEventDynamic(data: { id: string | number } & RequestBase) {
  return request<Dynamic[]>({
    url: `${SERVER.EVENT_CENTER}/event/${data.id}/eventDynamic`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

/**
 * @name 接手事件
 * @export
 * @param {({ eventId: string | number; priorityLevel?: string } & RequestBase)} data
 * @return {*}
 */
export function eventTaskOver(data: { eventId: string | number; priorityLevel?: string } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/event/takeOver`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * @name 批量接手
 * @export
 * @param {({ eventId: string[] | number[] } & RequestBase)} data
 * @return {*}
 */
export function eventBatchTakeOver(data: { eventId: string[] | number[] } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/event/batchTakeOver`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * @name 转交事件
 * @export
 * @param {({ eventId: string | number; transferId: string | number; priorityLevel: string } & RequestBase)} data
 * @return {*}
 */
export function eventTransfer(
  data: {
    eventId: string | number;
    transferId: string | number;
    priorityLevel: string;
  } & RequestBase
) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/transfer`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * @name 批量转交
 * @export
 * @param {({ eventId: string | number; transferId: string | number; priorityLevel: string } & RequestBase)} data
 * @return {*}
 */
export function eventBatchDeliver(
  data: {
    eventId: string | number;
    transferId: string | number;
    priorityLevel: string;
  } & RequestBase
) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/batchDeliver`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

interface Pend {
  eventId: string | number;
  hangUpDay: string | number;
  hangUpHour: string | number;
  hangUpMinute: string | number;
  reason: string | number;
  priorityLevel?: string | number;
}

/**
 *
 * @name 挂起事件
 * @export
 * @param {(Pend & RequestBase)} data
 * @return {*}
 */
export function eventPend(data: Pend & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/hangUp`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

interface Upgrade {
  id: string | number;
  userFlag: string | number;
  userGroupId: string | number;
  iuserIdd: string | number;
}
/**
 * @name 事件升级
 * @export
 * @param {(Upgrade & RequestBase)} data
 * @return {*}
 */
export function eventUpgrade(data: Upgrade & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/eventUpgrade`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

interface UpdatePriority {
  eventId: string | number;
  priority: string;
}
/**
 * @name 修改事件优先级
 * @export
 * @param {(UpdatePriority & RequestBase)} data
 * @return {*}
 */
export function eventUpdatePriority(data: UpdatePriority & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/updatePriority`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 *
 * @name 单独指派
 * @export
 * @param {(Upgrade & RequestBase)} data
 * @return {*}
 */
export function eventAssign(data: Upgrade & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/assign`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * @name 批量指派
 * @export
 * @param {(Upgrade & RequestBase)} data
 * @return {*}
 */
export function eventBatchAssign(data: Upgrade & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/batchAssign`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * @name 批量P8
 * @export
 * @param {({ id: string[] | number[] } & RequestBase)} data
 * @return {*}
 */
export function eventBatchP8(data: { id: string[] | number[] } & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/batchChangePriority`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 *
 * @name 审批挂起超过规定时间的事件(目前规定时间是超过8小时需要审批，且审批需要管理员权限)
 * @export
 * @param {(object & RequestBase)} data
 * @return {*}
 */
export function eventApproveHangUp(data: { id: string; approve: boolean } & RequestBase) {
  return request<unknown, Response<EventByPage>>({
    url: `${SERVER.EVENT_CENTER}/event/${data.id}/approveHangUp`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { approve: data.approve },
    data: {},
  });
}

export function dictEventApproveHangUp(data: { id: string; approve: boolean } & RequestBase) {
  return request<unknown, Response<EventByPage>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/approveHangUp`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { approve: data.approve },
    data: {},
  });
}

/**
 * @name 删除小记
 * @export
 * @param {(object & RequestBase)} data
 * @return {*}
 */
export function eventDelNote(data: object & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/${data.id}/deleteNote`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function dictEventDelNote(data: object & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/deleteNote`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

interface FinishQuery {
  eventId: string | number;
  finishCodeName: string;
  finishCodeDesc: string;
  finishContent: string;
}

/**
 * @name 完成事件
 * @export
 * @param {(FinishQuery & RequestBase)} data
 * @return {*}
 */
export function eventFinish(data: FinishQuery & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/finish`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { orderId: data.eventId },
    data: { ...data },
  });
}

export function dictEventFinish(data: FinishQuery & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/dict_event/finish`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { orderId: data.eventId },
    data: { ...data },
  });
}

/**
 * @name 关闭事件
 * @export
 * @param {(FinishQuery & RequestBase)} data
 * @return {*}
 */
export function eventClose(data: FinishQuery & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/close`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { ...data },
  });
}
/**
 * @name 关闭收集告警
 * @export
 * @param {({ id: string } & RequestBase)} data
 * @return {*}
 */
export function eventCollectAlertDisable(data: { id: string } & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/${data.id}/collect_alert/disable`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { orderId: data.id },
    data: {},
  });
}

/**
 * @name 模糊查询工单号
 * @export
 * @param {({ id: string } & RequestBase)} data
 * @return {*}
 */
export function eventSearchByOrderNo(data: { id: string } & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/${data.id}/searchByOrderNo`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

/**
 * @name 新增关联
 * @export
 * @param {({ associateId: string; orderNo: string } & RequestBase)} data
 * @return {*}
 */
export function eventAddAssociation(data: { associateId: string; orderNo: string } & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/addAssociation`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * @name 获取关联信息列表
 *
 * @export
 * @param {({ eventId: string } & RequestBase)} data
 * @return {*}
 */
export function eventGetAssociationList(data: { eventId: string } & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/getAssociationList`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { ...data },
    data: {},
  });
}

export function eventRemoveAssociation(data: { id: string } & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/${data.id}/removeAssociation`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { ...data },
    data: {},
  });
}

export function eventEditDescription(data: { id: string; desc: string } & RequestBase) {
  return request<EventByPage>({
    url: `${SERVER.EVENT_CENTER}/event/EditDescription`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { ...data },
  });
}

export function eventEditSummary(data: { id: string; desc: string } & RequestBase) {
  return request<unknown, Response<EventByPage>>({
    url: `${SERVER.EVENT_CENTER}/event/EditSummary`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { ...data },
  });
}

export interface Resp {
  day: string;
  hour: string;
  minute: string;
  urgencyType: string;
  name: string;
  description: string;
  definition: string;
  toleranceTime?: string;
  createTime?: string;
}

// interface Resolve extends Resp {
// }
export interface EventSla {
  resp: Resp[];
  resolve: Resp[];
  createTime: string;
}

export function eventSla(data: { slaSnapshotId: string; priority: keyof typeof priority } & RequestBase) {
  return request<unknown, Response<EventSla>>({
    url: `${SERVER.EVENT_CENTER}/event/${data.slaSnapshotId}/rule/${data.priority}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function dictEventSla(data: { slaSnapshotId: string; priority: keyof typeof priority } & RequestBase) {
  return request<unknown, Response<EventSla>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.slaSnapshotId}/rule/${data.priority}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function serviceRequestSla(data: { slaSnapshotId: string; priority: keyof typeof priority } & RequestBase) {
  return request<unknown, Response<EventSla>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.slaSnapshotId}/rule/${data.priority}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function eventBatchContact(data: { ids: string[] } & RequestBase) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.CMDB}/contacts/batch_get/${data.ids}/desensitized`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function eventBatchdesensitized(data: { deviceIds: string[] } & RequestBase) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.CMDB}/contacts/batch_get/desensitized`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {
      ids: data.deviceIds,
      permissionId: "513148893207199744",
    },
  });
}
//
export function eventAddContact(data: { id: string; contactType: string; contactIds: string[] } & RequestBase) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.EVENT_CENTER}/event/${data.id}/addContact`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["contactType", "contactIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export function eventDelContact(data: { id: string; contactType: string; contactIds: string[] } & RequestBase) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.EVENT_CENTER}/event/${data.id}/removeContact`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["contactType", "contactIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export function dictEventAddContact(data: { id: string; contactType: string; contactIds: string[] } & RequestBase) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/addContact`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["contactType", "contactIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export function dictEventDelContact(data: { id: string; contactType: string; contactIds: string[] } & RequestBase) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/removeContact`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["contactType", "contactIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export interface SupportNotes {
  id: string;
  tenantId: string;
  name: string;
  description: string;
  activeConfig: {
    useAutoTimeZone: boolean;
    timeZone: string;
    activeHours: {
      weekDay: string;
      hours: string[];
    }[];
  };
  activeNote: string;
  inactiveNote: string;
  active: boolean;
  defaultNote: boolean;
  version: string;
  createdTime: string;
  updatedTime: string;
  createdBy: string;
  updatedBy: string;
  dateTime: string;
  showWorkTime: Boolean;
}

// export interface ResourcesSupportNotes {
//   resourceSupportNotes: SupportNotes[];
//   locationSupportNotes: SupportNotes[];
//   regionSupportNotes: SupportNotes[];
//   globalSupportNotes: SupportNotes[];
//   tenantSupportNotes: SupportNotes[];
// }
export function getResourcesSupportNotes(data: { ids: string; containsGlobal: boolean } & RequestBase) {
  return request<unknown, Response<SupportNotes[]>>({
    url: `${SERVER.CMDB}/resources/batch_get/supportNotes`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    // params: ["ids", "containsGlobal"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
    // data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
    data: {
      ids: data.ids?.split(",") ?? [],
      containsGlobal: data.containsGlobal,
    },
  });
}

export function eventAddDevices(data: { id: string; deviceId: string; autoAllocateContact: boolean } & RequestBase) {
  return request<unknown, Response<EventByPage>>({
    url: `${SERVER.EVENT_CENTER}/event/${data.id}/addDevice/${data.deviceId}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: ["autoAllocateContact"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export function eventDelDevices(data: { id: string; deviceId: string } & RequestBase) {
  return request<unknown, Response<EventByPage>>({
    url: `${SERVER.EVENT_CENTER}/event/${data.id}/removeDevice/${data.deviceId}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export function dictEventAddDevices(data: { id: string; deviceId: string; autoAllocateContact: boolean } & RequestBase) {
  return request<unknown, Response<EventByPage>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/addDevice/${data.deviceId}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: ["autoAllocateContact"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export function dictEventDelDevices(data: { id: string; deviceId: string } & RequestBase) {
  return request<unknown, Response<EventByPage>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/removeDevice/${data.deviceId}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export interface TabCount {
  noteCount: string /* 小记数量 */;
  deviceCount: string /* 设备数量 */;
  contactCount: string /* 联系人数量 */;
  relationCount: string /* 关联数量 */;
  fileCount: string /* 文件数量 */;
  unconfirmed: string;
  confirmed: string;
  actionCount: string;
}
export function /* 返回tap统计数量 */ getDetailTapCountById(req: { id: string } & RequestBase) {
  const params = [].reduce((p, key) => Object.assign(p, req[key] === undefined ? {} : { [key]: req[key] }), {});
  const data = [].reduce((p, key) => Object.assign(p, req[key] === undefined ? {} : { [key]: req[key] }), {});
  return request<never, Response<TabCount>>({ url: `${SERVER.EVENT_CENTER}/event/tap/${req.id}/count`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function /* 返回tap统计数量 */ getDictDetailTapCountById(req: { id: string } & RequestBase) {
  const params = [].reduce((p, key) => Object.assign(p, req[key] === undefined ? {} : { [key]: req[key] }), {});
  const data = [].reduce((p, key) => Object.assign(p, req[key] === undefined ? {} : { [key]: req[key] }), {});
  return request<never, Response<TabCount>>({ url: `${SERVER.EVENT_CENTER}/dict_event/tap/${req.id}/count`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}
// eventSeverity, deviceImportance

export function setEventEditable(data: {} & RequestBase) {
  return request<unknown, Response<EventByPage>>({
    url: `${SERVER.EVENT_CENTER}/event/${data.id}/updateEditable`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { orderId: data.id }),
    data: Object.keys(data).reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function setDictEventEditable(data: {} & RequestBase) {
  return request<unknown, Response<EventByPage>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/updateEditable`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { orderId: data.id }),
    data: Object.keys(data).reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export interface SlaTimeLimit {
  responseTime?: string /* 响应时间(目前已过时间) */;
  resolveTime?: string /* 解决时间(目前已过时间) */;
  responseLimit?: string /* 响应时限(配置的响应时间) */;
  resolveLimit?: string /* 解决时限(配置的解决时间) */;
}

export function /* 详情页面返回事件时限信息 */ getEventSlaTimeLimitById(req: { id: string } & RequestBase) {
  const params = {};
  const data = {};
  return request<never, Response<SlaTimeLimit>>({ url: `${SERVER.EVENT_CENTER}/event/${req.id /*  */}/timeLimit`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function /* 详情页面返回事件时限信息 */ getDictEventSlaTimeLimitById(req: { id: string } & RequestBase) {
  const params = {};
  const data = {};
  return request<never, Response<SlaTimeLimit>>({ url: `${SERVER.EVENT_CENTER}/dict_event/${req.id /*  */}/timeLimit`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function /* 详情页面返回事件时限信息 */ getServiceRequestSlaTimeLimitById(req: { id: string } & RequestBase) {
  const params = {};
  const data = {};
  return request<never, Response<SlaTimeLimit>>({ url: `${SERVER.EVENT_CENTER}/serviceRequest/${req.id /*  */}/timeLimit`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function /* 详情页面返回事件时限信息 */ getDictServiceRequestSlaTimeLimitById(req: { id: string } & RequestBase) {
  const params = {};
  const data = {};
  return request<never, Response<SlaTimeLimit>>({ url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${req.id /*  */}/timeLimit`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function getChangeContacts(data: { id: string } & RequestBase) {
  return request<unknown, Response<{ contactId: string; contactType: string }[]>>({
    url: `${SERVER.EVENT_CENTER}/event/${data.id}/getContact`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: {},
  });
}
