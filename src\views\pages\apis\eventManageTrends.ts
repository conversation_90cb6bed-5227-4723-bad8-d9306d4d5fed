import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";

export interface Dynamic {
  dynamicId: string /* 事件动态ID */;
  eventId: string /* 关联事件ID */;
  dynamicContent?: string /* 事件动态内容 */;
  eventCreatorId: string /* 事件创造者ID */;
  eventCreatorName?: string /* 事件创造者名称 */;
  eventTransferId: string /* 事件转交对象ID */;
  eventTransferName?: string /* 事件转交对象名称 */;
  dynamicCreateTime: string /* 事件动态创建时间 */;
}

/**
 * @name 服务请求动态信息列表
 * @export
 * @param {(object & RequestBase)} data
 * @return {*}
 */
export function getRequestTrendList(data: object & RequestBase) {
  return request<unknown, Response<Dynamic[]>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.id}/dynamicInfo`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      tenantId: data.tenantId,
    },
    data: {},
  });
}

export function getDictRequestTrendList(data: object & RequestBase) {
  return request<unknown, Response<Dynamic[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.id}/dynamicInfo`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      tenantId: data.tenantId,
    },
    data: {},
  });
}

/**
 * @name 问题动态信息列表
 * @export
 * @param {(object & RequestBase)} data
 * @return {*}
 */
export function getQuestionTrendList(data: object & RequestBase) {
  return request<unknown, Response<Dynamic[]>>({
    url: `${SERVER.EVENT_CENTER}/question/${data.id}/eventDynamic`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      tenantId: data.tenantId,
    },
    data: {},
  });
}

/**
 * @name 变更动态信息列表
 * @export
 * @param {(object & RequestBase)} data
 * @return {*}
 */

export function getChangeTrendList(data: object & RequestBase) {
  return request<unknown, Response<Dynamic[]>>({
    url: `${SERVER.EVENT_CENTER}/change/${data.id}/dynamic`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      tenantId: data.tenantId,
      permissionId: "612917524815675392",
    },
    data: {},
  });
}

/**
 * @name 发布动态信息列表
 * @export
 * @param {(object & RequestBase)} data
 * @return {*}
 */

export interface Dynamic {
  dynamicId: string /* 事件动态ID */;
  eventId: string /* 关联事件ID */;
  dynamicContent?: string /* 事件动态内容 */;
  eventCreatorId: string /* 事件创造者ID */;
  eventCreatorName?: string /* 事件创造者名称 */;
  eventTransferId: string /* 事件转交对象ID */;
  eventTransferName?: string /* 事件转交对象名称 */;
  dynamicCreateTime: string /* 事件动态创建时间 */;
}
export function getPublishTrendList(data: object & RequestBase) {
  return request<unknown, Response<Dynamic[]>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/eventDynamic`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      tenantId: data.tenantId,
    },
    data: {},
  });
}

/**
 * @name 事件动态信息列表
 * @export
 * @param {(object & RequestBase)} data
 * @return {*}
 */
export function getEventTrendList(data: object & RequestBase) {
  return request<unknown, Response<Dynamic[]>>({
    url: `${SERVER.EVENT_CENTER}/event/${data.id}/eventDynamic`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      tenantId: data.tenantId,
    },
    data: {},
  });
}
