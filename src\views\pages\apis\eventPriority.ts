import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";
import { eventState, eventStateOption, eventSeverity, eventSeverityOption } from "./event";
export { eventState, eventStateOption, eventSeverity, eventSeverityOption };
import { deviceImportance, deviceImportanceOption } from "./device";
export { deviceImportance, deviceImportanceOption };

export enum priority {
  P1 = "P1",
  P2 = "P2",
  P3 = "P3",
  P4 = "P4",
  P5 = "P5",
  P6 = "P6",
  P7 = "P7",
  P8 = "P8",
  // DEFAULT = "DEFAULT",
}

// P1: { color: "#ED4013", value: "P1" },
// P2: { color: "#FF7D00", value: "P2" },
// P3: { color: "#2CB6F4", value: "P3" },
// P4: { color: "#3EBE6B", value: "P4" },
// P5: { color: "#3EBE6B", value: "P5" },
// P6: { color: "#3EBE6B", value: "P6" },
// P7: { color: "#3EBE6B", value: "P7" },
// P8: { color: "#BBBCBE", value: "P8" },
export const priorityOption: { label: string; value: keyof typeof priority; color?: string }[] = [
  { label: "P1", value: priority.P1, color: "#ED4013" },
  { label: "P2", value: priority.P2, color: "#FF7D00" },
  { label: "P3", value: priority.P3, color: "#2CB6F4" },
  { label: "P4", value: priority.P4, color: "#3EBE6B" },
  { label: "P5", value: priority.P5, color: "#3EBE6B" },
  { label: "P6", value: priority.P6, color: "#3EBE6B" },
  { label: "P7", value: priority.P7, color: "#3EBE6B" },
  { label: "P8", value: priority.P8, color: "#BBBCBE" },
];

interface monitorSourceMappingFindAll {
  id: string | number;
  tenantId: string | number;
  sourceId: string;
  sourceName: string;
  mappings: { alarmSeverity: string; alarmSeverityName: string; eventSeverity: eventSeverity }[];
  createdBy: string;
  updatedBy: string;
  createdTime: string | number;
  updatedTime: string | number;
}

//监控源映射关系
export function getMointerSourceMappingList(data: {} & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  return request<never, Response<monitorSourceMappingFindAll[]>>({
    url: `${SERVER.EVENT_CENTER}/monitor_source_mapping/find_all`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
//修改监控源映射关系
export function setMointerSourceMapping(data: Partial<{ sourceId: monitorSourceMappingFindAll["sourceId"]; mappings: monitorSourceMappingFindAll["mappings"] }> & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  return request<never, Response<monitorSourceMappingFindAll>>({
    url: `${SERVER.EVENT_CENTER}/monitor_source_mapping`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: ["sourceId", "mappings"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//修改监控源映射关系
export function editMointerSourceMapping(data: Required<{ sourceId: monitorSourceMappingFindAll["sourceId"]; mappings: monitorSourceMappingFindAll["mappings"] }> & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  return request<never, Response<monitorSourceMappingFindAll>>({
    url: `${SERVER.EVENT_CENTER}/monitor_source_mapping`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: ["sourceId", "mappings"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export interface PriorityMatrix {
  id: string | number;
  tenantId: string | number;
  priorityMatrixItems: { influence: string; urgency: string; priority: string }[];
  alertOrderMappingItems: { severity: string; urgency: string }[];
  deviceOrderMappingItems: { importance: string; influence: string }[];
  createdBy: string;
  updatedBy: string;
  createdTime: string | number;
  updatedTime: string | number;
}

//查询事件优先级矩阵
export function getPriorityMatrixList(data: {} & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  return request<never, Response<PriorityMatrix>>({
    url: `${SERVER.EVENT_CENTER}/priority_matrix`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

//编辑事件优先级矩阵
export function setPriorityMatrix(data: { priorityMatrixItems: PriorityMatrix["priorityMatrixItems"] } & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  return request<never, Response<PriorityMatrix>>({
    url: `${SERVER.EVENT_CENTER}/priority_matrix`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: ["priorityMatrixItems"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//编辑事件优先级矩阵
export function editPriorityMatrix(data: { priorityMatrixItems?: PriorityMatrix["priorityMatrixItems"]; deviceOrderMappingItems?: PriorityMatrix["deviceOrderMappingItems"]; alertOrderMappingItems?: PriorityMatrix["alertOrderMappingItems"] } & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  return request<never, Response<PriorityMatrix>>({
    url: `${SERVER.EVENT_CENTER}/2.0/priority_matrix`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: ["priorityMatrixItems", "deviceOrderMappingItems", "alertOrderMappingItems"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

//2.0 查询优先级矩阵

export function getNewPriorityMatrixList(data: {} & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  return request<never, Response<PriorityMatrix>>({
    url: `${SERVER.EVENT_CENTER}/priority_matrix/page`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

//新增优先级矩阵

export function addNewPriorityMatrix(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<PriorityMatrix[]>>({
    url: `${SERVER.EVENT_CENTER}/priority_matrix`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
export function EditNewSlaConfig(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<PriorityMatrix[]>>({
    url: `${SERVER.EVENT_CENTER}/priority_matrix`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
export function DelNewSlaConfig(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<PriorityMatrix[]>>({
    url: `${SERVER.EVENT_CENTER}/priority_matrix`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//根据ID查询优先级矩阵
export function getNewSlaConfig(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<PriorityMatrix[]>>({
    url: `${SERVER.EVENT_CENTER}/priority_matrix/${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//更新优先级矩阵基础信息

export function updateBasicInfo(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<PriorityMatrix[]>>({
    url: `${SERVER.EVENT_CENTER}/priority_matrix/${data.id}/basic`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//删除优先级矩阵
export function deletePriorityMatrix(data: {} & RequestBase) {
  return request<never, Response<PriorityMatrix[]>>({
    url: `${SERVER.EVENT_CENTER}/priority_matrix/${data.id}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

// 更新优先级矩阵配置信息(选择性更新)
export function updatePriorityMatrixInfoConfig(data: { priorityMatrixItems?: PriorityMatrix["priorityMatrixItems"]; deviceOrderMappingItems?: PriorityMatrix["deviceOrderMappingItems"]; alertOrderMappingItems?: PriorityMatrix["alertOrderMappingItems"] } & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);

  return request<never, Response<PriorityMatrix>>({
    url: `${SERVER.EVENT_CENTER}/priority_matrix/${data.id}/config/selectivity`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: ["priorityMatrixItems", "deviceOrderMappingItems", "alertOrderMappingItems"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

//查询优先级矩阵 根据数据权限
export async function getNewPriorityMatrixList2(data: { containerId: string; queryPermissionId: string } & RequestBase) {
  const params = new URLSearchParams({});
  // bindSearchParams(
  //   {
  //     containerId: data.containerId,
  //     queryPermissionId: data.queryPermissionId,
  //   },
  //   params
  // );

  bindSearchParams(
    {
      ...([...(data.includeName instanceof Array ? data.includeName : []), ...(data.excludeName instanceof Array ? data.excludeName : []), ...(data.eqName instanceof Array ? data.eqName : []), ...(data.neName instanceof Array ? data.neName : [])].filter((v) => v).length ? { nameFilterRelation: data.nameFilterRelation === "OR" ? "OR" : "AND", includeName: data.includeName instanceof Array && data.includeName.length ? data.includeName.join(",") : void 0, excludeName: data.excludeName instanceof Array && data.excludeName.length ? data.excludeName.join(",") : void 0, eqName: data.eqName instanceof Array && data.eqName.length ? data.eqName.join(",") : void 0, neName: data.neName instanceof Array && data.neName.length ? data.neName.join(",") : void 0 } : {}),

      ...([...(data.includeDescription instanceof Array ? data.includeDescription : []), ...(data.excludeDescription instanceof Array ? data.excludeDescription : []), ...(data.eqDescription instanceof Array ? data.eqDescription : []), ...(data.neDescription instanceof Array ? data.neDescription : [])].filter((v) => v).length ? { descriptionFilterRelation: data.descriptionFilterRelation === "OR" ? "OR" : "AND", includeDescription: data.includeDescription instanceof Array && data.includeDescription.length ? data.includeDescription.join(",") : void 0, excludeDescription: data.excludeDescription instanceof Array && data.excludeDescription.length ? data.excludeDescription.join(",") : void 0, eqDescription: data.eqDescription instanceof Array && data.eqDescription.length ? data.eqDescription.join(",") : void 0, neDescription: data.neDescription instanceof Array && data.neDescription.length ? data.neDescription.join(",") : void 0 } : {}),

      defaultMatrix: data.defaultMatrix,
    },
    params
  );

  const userInfo = (await import("@/utils/getUserInfo")).default();
  const { 服务管理中心_优先级矩阵模板_可读, 服务管理中心_优先级矩阵模板_编辑, 服务管理中心_优先级矩阵模板_删除, 服务管理中心_优先级矩阵模板_安全 } = await import("@/views/pages/permission");

  bindSearchParams(
    {
      containerId: data.containerId || (userInfo.currentTenant || {}).containerId,
      queryPermissionId: data.queryPermissionId || 服务管理中心_优先级矩阵模板_可读,
      verifyPermissionIds: data.verifyPermissionIds || [服务管理中心_优先级矩阵模板_编辑, 服务管理中心_优先级矩阵模板_删除, 服务管理中心_优先级矩阵模板_安全].join(),
    },
    params
  );

  return request<never, Response<PriorityMatrix>>({
    url: `${SERVER.EVENT_CENTER}/priority_matrix/2.0/filter`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: {},
  });
}
// 验证租户是否已有优先级矩阵
export function hasPriorityMatrixcheckDefault(data: { tenantId: string } & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  return request<PriorityMatrix>({
    url: `${SERVER.EVENT_CENTER}/priority_matrix/checkDefault?tenantId=${data.tenantId}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params,
    data: {},
  });
}
