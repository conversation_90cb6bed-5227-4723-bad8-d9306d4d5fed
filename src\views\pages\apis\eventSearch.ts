import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";

export function getRetrievalResult /* 获取检索结果 */(data: Record<string, unknown> & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/retrieval/list`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["id", "orderType", "state", "priority", "createTimeStart", "createTimeEnd", "finishTimeStart", "finishTimeEnd", "closeTimeStart", "closeTimeEnd", "field", "text", "pageNumber", "pageSize", "limit"].reduce((p, key) => Object.assign(p, !data[key] ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

export function getRetrievalResultByOrderId /* 通过工单id进行检索 */(data: Record<string, unknown> & RequestBase) {
  return request<unknown, Response<Record<string, any>>>({
    url: `${SERVER.EVENT_CENTER}/retrieval/orderId`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["ordreId"].reduce((p, key) => Object.assign(p, !data[key] ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

export function exportRetrievalResult /* 导出检索结果 */(data: Record<string, unknown> & RequestBase) {
  return request<unknown, Response<Blob>>({
    url: `${SERVER.CC_REPORT}/event_center/retrieval/excel`,
    method: Method.Get,
    responseType: "blob",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["id", "orderType", "state", "priority", "createTimeStart", "createTimeEnd", "finishTimeStart", "finishTimeEnd", "closeTimeStart", "closeTimeEnd", "field", "text", "pageNumber", "pageSize", "userId"].reduce((p, key) => Object.assign(p, !data[key] ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

export enum CustomKey {
  priority = "priority",
  orderType = "orderType",
  orderStatus = "orderStatus",
  alarmNumber = "alarmNumber",
  summary = "summary",
  desc = "desc",
  summaryAndDesc = "summaryAndDesc",
  createUser = "createUser",
  updateUser = "updateUser",
  responsibleName = "responsibleName",
  actorName = "actorName",
  id = "id",
  order = "order",
  finishTime = "finishTime",
  closeTime = "closeTime",
  createTime = "createTime",
  updateTime = "updateTime",
}

export const customKeyOption: { key: keyof typeof CustomKey; label: string }[] = [
  { key: CustomKey.priority, label: "优先级" },
  // { key: CustomKey.orderType, label: "工单类型" },
  { key: CustomKey.id, label: "工单ID" },
  { key: CustomKey.order, label: "工单" },
  // { key: CustomKey.orderStatus, label: "事件状态" },
  { key: CustomKey.alarmNumber, label: "告警" },
  { key: CustomKey.summary, label: "摘要" },
  { key: CustomKey.desc, label: "描述" },
  { key: CustomKey.summaryAndDesc, label: "摘要描述" },
  { key: CustomKey.createUser, label: "创建人" },
  { key: CustomKey.updateUser, label: "修改人" },
  { key: CustomKey.responsibleName, label: "负责人" },
  { key: CustomKey.actorName, label: "处理人" },
  { key: CustomKey.finishTime, label: "完成时间" },
  { key: CustomKey.closeTime, label: "关闭时间" },
  { key: CustomKey.createTime, label: "创建时间" },
  { key: CustomKey.updateTime, label: "更新时间" },
];

export interface CustomFieldItem {
  /** 主键 */
  id: /* Integer */ string;
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime?: /* Integer */ string;
  /** 更新时间 */
  updatedTime?: /* Integer */ string;
  /** 用户id */
  userId: /* Integer */ string;
  /** 自定义字段 */
  fields: string[];
}

export function getCustomKey(data: Record<string, unknown> & RequestBase) {
  return request<unknown, Response<CustomFieldItem>>({
    url: `${SERVER.EVENT_CENTER}/custom/findFields`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["userId"].reduce((p, key) => Object.assign(p, !data[key] ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

export function setCustomKey(data: Record<string, unknown> & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/custom/createFields`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["userId", "fields"].reduce((p, key) => Object.assign(p, !data[key] ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
