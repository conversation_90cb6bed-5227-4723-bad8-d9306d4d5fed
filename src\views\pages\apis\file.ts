import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";

export enum FileType {
  EVENT = "EVENT",
  QUESTION = "QUESTION",
  CHANGE = "CHANGE",
  SERVICE = "SERVICE",
  PROJECT = "PROJECT",
  DICT_ENENT = "DICT_ENENT",
}

export interface FileUpload {
  fileId: string;
  bucketName: string;
  keyName: string;
  fileName: string;
}

export function uploadFile(data: { file?: File; files?: File[] } & RequestBase, params: { fileType: keyof typeof FileType; noteCreated: boolean; id: string; ids: string }) {
  const formData = new FormData();
  // data.file && formData.append("file", data.file);

  if (!(data instanceof FormData) && data.files) {
    data.files.forEach((el, index) => {
      data.files && formData.append("files", data.files[index]);
    });
  }
  return request<unknown, Response<FileUpload[]>>({
    url: `${SERVER.EVENT_CENTER}/file/eventUpload`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: params,
    data: data instanceof FormData ? data : formData,
  });
}

export function dictUploadFile(data: { file?: File; files?: File[] } & RequestBase, params: { fileType: keyof typeof FileType; noteCreated: boolean; id: string; ids: string }) {
  const formData = new FormData();
  // data.file && formData.append("file", data.file);

  if (!(data instanceof FormData) && data.files) {
    data.files.forEach((el, index) => {
      data.files && formData.append("files", data.files[index]);
    });
  }
  return request<unknown, Response<FileUpload[]>>({
    url: `${SERVER.EVENT_CENTER}/file/dict/eventUpload`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: params,
    data: data instanceof FormData ? data : formData,
  });
}

export function serviceRequsestUploadFile(data: { file?: File; files?: File[] } & RequestBase, params: { fileType: keyof typeof FileType; noteCreated: boolean; id: string }) {
  const formData = new FormData();
  // data.file && formData.append("file", data.file);

  if (!(data instanceof FormData) && data.files) {
    data.files.forEach((el, index) => {
      data.files && formData.append("files", data.files[index]);
    });
  }
  return request<unknown, Response<FileUpload[]>>({
    url: `${SERVER.EVENT_CENTER}/file/serviceUpload`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: params,
    data: data instanceof FormData ? data : formData,
  });
}

export function dictServiceRequsestUploadFile(data: { file?: File; files?: File[] } & RequestBase, params: { fileType: keyof typeof FileType; noteCreated: boolean; id: string }) {
  const formData = new FormData();
  // data.file && formData.append("file", data.file);

  if (!(data instanceof FormData) && data.files) {
    data.files.forEach((el, index) => {
      data.files && formData.append("files", data.files[index]);
    });
  }
  return request<unknown, Response<FileUpload[]>>({
    url: `${SERVER.EVENT_CENTER}/file/dict/serviceUpload`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: params,
    data: data instanceof FormData ? data : formData,
  });
}

export function questionUploadFile(data: { file?: File; files?: File[] } & RequestBase, params: { fileType: keyof typeof FileType; noteCreated: boolean; id: string }) {
  const formData = new FormData();
  // data.file && formData.append("file", data.file);

  if (!(data instanceof FormData) && data.files) {
    data.files.forEach((el, index) => {
      data.files && formData.append("files", data.files[index]);
    });
  }
  return request<unknown, Response<FileUpload[]>>({
    url: `${SERVER.EVENT_CENTER}/file/questionUpload`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: params,
    data: data instanceof FormData ? data : formData,
  });
}

export function changeUploadFile(data: { file?: File; files?: File[] } & RequestBase, params: { fileType: keyof typeof FileType; noteCreated: boolean; id: string }) {
  const formData = new FormData();
  if (!(data instanceof FormData) && data.files) {
    data.files.forEach((el, index) => {
      data.files && formData.append("files", data.files[index]);
    });
  }
  return request<unknown, Response<FileUpload[]>>({
    url: `${SERVER.EVENT_CENTER}/file/changeUpload`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: params,
    data: data instanceof FormData ? data : formData,
  });
}

export function publishUploadFile(data: { file?: File; files?: File[] } & RequestBase, params: { fileType: keyof typeof FileType; noteCreated: boolean; id: string }) {
  const formData = new FormData();
  if (!(data instanceof FormData) && data.files) {
    data.files.forEach((el, index) => {
      data.files && formData.append("files", data.files[index]);
    });
  }
  return request<unknown, Response<FileUpload[]>>({
    url: `${SERVER.EVENT_CENTER}/file/publishUpload`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: params,
    data: data instanceof FormData ? data : formData,
  });
}

export function projectUploadFile(data: { file?: File; files?: File[] } & RequestBase, params: { fileType: keyof typeof FileType; noteCreated: boolean; id: string; ids: string }) {
  const formData = new FormData();
  // data.file && formData.append("file", data.file);

  if (!(data instanceof FormData) && data.files) {
    data.files.forEach((el, index) => {
      data.files && formData.append("files", data.files[index]);
    });
  }
  return request<unknown, Response<FileUpload[]>>({
    url: `${SERVER.EVENT_CENTER}/file/projectUpload`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: params,
    data: data instanceof FormData ? data : formData,
  });
}

export function getFile(data: { bucket?: string; path?: string; fileUrl?: string } & RequestBase) {
  let url = `${SERVER.EVENT_CENTER}/oss/show/`;
  if (data.bucket && data.path) url += `${data.bucket}/${data.path}`;
  else url += data.fileUrl;
  return request<unknown, Response<Blob>>({
    url,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
    responseType: "blob",
  });
}

export interface flieList {
  id: string;
  fileName: string;
  fileSize: string;
  createdBy: string;
  createdTime: string;
  uploadedByNote: boolean;
  bucketName: string;
  keyName: string;
}

// 获取模块文件列表
export function getModelFiles(data: { id: string } & RequestBase) {
  return request<unknown, Response<flieList[]>>({
    url: `${SERVER.EVENT_CENTER}/file/${data.id}/fileList`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function delModelFile(data: { id: string } & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/file/${data.id}/deleteFile`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

// 下载文件
export function download(fileName: string, blob: Blob) {
  const link = document.createElement("a");
  link.href = window.URL.createObjectURL(blob);
  link.style.display = "none";
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  window.URL.revokeObjectURL(link.href);
  document.body.removeChild(link);
}
