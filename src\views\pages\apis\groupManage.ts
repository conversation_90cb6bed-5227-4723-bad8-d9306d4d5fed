import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";

/**
 * 模块Api示例
 */
export interface ModuleItem {
  [key: string]: unknown;
  id: string;
  name: string;
}
// current_org/user_group_page
export function getUserGroupsList /* 用户组获取模块 */(data: { pageNumber: Number; pageSize: Number } & RequestBase) {
  const params = new URLSearchParams({ permissionId: ["512890964822458368"].join(","), pageNumber: String(data.pageNumber || 1), pageSize: String(data.pageSize || 10) });

  bindSearchParams(
    {
      mfaEnabled: data.mfaEnabled,
      active: data.active,

      ...([...(data.includeName instanceof Array ? data.includeName : []), ...(data.excludeName instanceof Array ? data.excludeName : []), ...(data.eqName instanceof Array ? data.eqName : []), ...(data.neName instanceof Array ? data.neName : [])].filter((v) => v).length ? { nameFilterRelation: data.nameFilterRelation === "OR" ? "OR" : "AND", includeName: data.includeName instanceof Array && data.includeName.length ? data.includeName.join(",") : void 0, excludeName: data.excludeName instanceof Array && data.excludeName.length ? data.excludeName.join(",") : void 0, eqName: data.eqName instanceof Array && data.eqName.length ? data.eqName.join(",") : void 0, neName: data.neName instanceof Array && data.neName.length ? data.neName.join(",") : void 0 } : {}),

      ...([...(data.includeEmail instanceof Array ? data.includeEmail : []), ...(data.excludeEmail instanceof Array ? data.excludeEmail : []), ...(data.eqEmail instanceof Array ? data.eqEmail : []), ...(data.neEmail instanceof Array ? data.neEmail : [])].filter((v) => v).length ? { emailFilterRelation: data.emailFilterRelation === "OR" ? "OR" : "AND", includeEmail: data.includeEmail instanceof Array && data.includeEmail.length ? data.includeEmail.join(",") : void 0, excludeEmail: data.excludeEmail instanceof Array && data.excludeEmail.length ? data.excludeEmail.join(",") : void 0, eqEmail: data.eqEmail instanceof Array && data.eqEmail.length ? data.eqEmail.join(",") : void 0, neEmail: data.neEmail instanceof Array && data.neEmail.length ? data.neEmail.join(",") : void 0 } : {}),
    },
    params
  );

  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/current_org/user_group_page/filter`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params,
    data: {},
  });
}
export function addUserGroups /* 用户组添加模块 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/current_org/user_groups`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "note", "email", "zoneId", "language", "active", "enabelMfa"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function editUserGroups /* 用户组修改模块 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/user_groups/${data.id}`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "note", "email", "zoneId", "language", "active", "enabelMfa"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function delUserGroups /* 用户组删除模块 */(data: Record<"id", string> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/user_groups/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
// 获取用户组下面用户列表
export function getUserList /* 用户获取模块 */(data: Partial<ModuleItem> & RequestBase) {
  if (!data.id) return Promise.resolve({ success: true, data: [], message: "true" });
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/user_groups/${data.id}/user_infos`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
// 给用户组添加用户
export function addUsers /* 用户组添加模块 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/user_groups/${data.id}/add_users`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["userIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
// 给用户组删除用户
export function delUsers /* 用户组添加模块 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/user_groups/${data.id}/remove_users`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["userIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
// 获取用户组下面用户组列表
export function getGroupList /* 用户获取模块 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/user_groups/${data.id}/children`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

// 获取用户组下面可分配用户列表
export function getAllotUserList /* 用户获取模块 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    // url: `${SERVER.IAM}/users/current_org/info`,
    url: `${SERVER.IAM}/user_groups/${data.id}/assignable_users`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
// 获取用户组下面可分配用户组列表
export function getAllotGroupList /* 用户获取模块 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    // url: `${SERVER.IAM}/current_org/user_groups`,
    url: `${SERVER.IAM}/user_groups/${data.id}/assignable_groups`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
// 给用户组添加用户组
export function addGroups /* 用户组添加模块 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/current_org/user_groups/${data.id}/add_children`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: data.userGroupIds,
  });
}
// 给用户组删除用户组
export function delGroups /* 用户组添加模块 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/current_org/user_groups/${data.id}/remove_child?childId=${data.childId}`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: {},
  });
}
