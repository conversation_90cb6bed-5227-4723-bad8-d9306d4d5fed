import { SERVER, Method, type Response, type RequestBase, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

/**
 * 模块Api示例
 */
export interface ModuleItem {
  [key: string]: unknown;
  id: string;
  name: string;
}
export function getModuleList /* 获取模块 */(data: Record<string, unknown> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/module`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
export function addModuleData /* 添加模块 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/module`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function setModuleData /* 更新模块 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/module/${data.id}`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function modModuleData /* 修改模块 */(data: Partial<ModuleItem> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/module/${data.id}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function delModuleData /* 删除模块 */(data: Record<"id", string> & RequestBase) {
  return request<unknown, Response<ModuleItem[]>>({
    url: `${SERVER.IAM}/module/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

export interface LocationItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 区域ID */
  regionId: /* Integer */ string;
  /** 行动策略列表 */
  supportNoteIds: /* Integer */ string[];
  /** 场所名称 */
  name: string;
  /** 描述 */
  description?: string;
  /** 时区Id */
  zoneId?: string;
  /** 外部ID */
  externalId?: string;
  /** 国家编码 */
  country?: string;
  /** 省级编码 */
  province?: string;
  /** 市级编码 */
  city?: string;
  /** 邮编代码 */
  postcode?: string;
  /** 地址 */
  address: string[];
  /** 是否激活 */
  active: boolean;
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
  /** 拥有的权限 */
  hasPermissionIds: /* Integer */ string[];
}

export interface RegionsTenant {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 安全容器 */
  containerId: /* Integer */ string;
  /** 父区域Id */
  parentId: /* Integer */ string;
  /** 行动策略列表 */
  supportNoteIds: /* Integer */ string[];
  label?: string;
  /** 区域名称 */
  name: string;
  /** 描述信息 */
  description?: string;
  /** 外部ID */
  externalId?: string;
  /** 纬度 */
  latitude?: string;
  /** 经度 */
  longitude?: string;
  /** 是否激活 */
  active: boolean;
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
  /** 拥有的权限 */
  hasPermissionIds: /* Integer */ string[];
  children: RegionsTenant[];
}
/**
 * @description 获取当前租户的所有场所
 */
export async function queryLocationList(req: { queryType: string /* 查询类型 */;queryId: string /* 查询主键ID */;pageNumber: string /* 页码, 默认第一页 */; pageSize: string /* 页大小, 默认10 */; sort: string[]; keyword: string /* 模糊查询关键字, 名称/描述 */; regionId: string /* 区域ID */; externalId: string /* 外部ID */; supportNoteId: string /* 行动策略ID */; active: string /* 是否激活 */; queryPermissionId?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/locations/queryLocationList`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        bindParamByObj({ queryType: req.queryType /* 查询类型 */,queryId: req.queryId /* 查询主键ID */,pageNumber: req.pageNumber /* 页码, 默认第一页 */, pageSize: req.pageSize /* 页大小, 默认10 */, sort: req.sort, keyword: req.keyword /* 模糊查询关键字, 名称/描述 */, regionId: req.regionId /* 区域ID */, externalId: req.externalId /* 外部ID */, supportNoteId: req.supportNoteId /* 行动策略ID */, active: req.active /* 是否激活 */ }, $req.params);
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_场所_可读, 资产管理中心_场所_新增, 资产管理中心_场所_编辑, 资产管理中心_场所_删除 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [req.queryPermissionId || 资产管理中心_场所_可读].join(","), verifyPermissionIds: [资产管理中心_场所_新增, 资产管理中心_场所_编辑, 资产管理中心_场所_删除].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<LocationItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 获取当前租户的所有设备
 */
export function queryResourceList(req: { queryType: string /* 查询类型 */;queryId: string /* 查询主键ID */;supportNoteId: string; active: string; queryPermissionId?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/resources/queryResourceList`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ queryType: req.queryType /* 查询类型 */,queryId: req.queryId /* 查询主键ID */, supportNoteId: req.supportNoteId, active: req.active }, $req.params);
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_设备_可读, 资产管理中心_设备_新增, 资产管理中心_设备_编辑 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [req.queryPermissionId || 资产管理中心_设备_可读].join(","), verifyPermissionIds: [资产管理中心_设备_新增, 资产管理中心_设备_编辑].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<RegionsTenant[]>>($req)),
    { controller }
  );
}

/**
 * @description 获取当前租户的所有区域
 */
export function queryRegionsList(req: { queryType: string /* 查询类型 */;queryId: string /* 查询主键ID */;supportNoteId: string; active: string; queryPermissionId?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/regions/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ queryType: req.queryType /* 查询类型 */,queryId: req.queryId /* 查询主键ID */, supportNoteId: req.supportNoteId, active: req.active }, $req.params);
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_区域_可读, 资产管理中心_区域_新增, 资产管理中心_区域_编辑, 资产管理中心_区域_删除 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [req.queryPermissionId || 资产管理中心_区域_可读].join(","), verifyPermissionIds: [资产管理中心_区域_新增, 资产管理中心_区域_编辑, 资产管理中心_区域_删除].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<RegionsTenant[]>>($req)),
    { controller }
  );
}

/**
 * @description 获取短信发送策略分配信息查询
 */
export async function querySMSassignedList(req: { queryType: string /* 查询类型 */;queryId: string /* 查询主键ID */;pageNumber: string /* 页码, 默认第一页 */; pageSize: string /* 页大小, 默认10 */; sort: string[]; keyword: string /* 模糊查询关键字, 名称/描述 */; regionId: string /* 区域ID */; externalId: string /* 外部ID */; supportNoteId: string /* 行动策略ID */; active: string /* 是否激活 */; queryPermissionId?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/message_strategy/2.0/distribution`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        bindParamByObj({ queryType: req.queryType /* 查询类型 */,queryId: req.queryId /* 查询主键ID */,pageNumber: req.pageNumber /* 页码, 默认第一页 */, pageSize: req.pageSize /* 页大小, 默认10 */, sort: req.sort, keyword: req.keyword /* 模糊查询关键字, 名称/描述 */, regionId: req.regionId /* 区域ID */, externalId: req.externalId /* 外部ID */, supportNoteId: req.supportNoteId /* 行动策略ID */, active: req.active /* 是否激活 */ }, $req.params);
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 系统管理中心_短信发送策略_可读, 系统管理中心_短信发送策略_新增, 系统管理中心_短信发送策略_编辑, 系统管理中心_短信发送策略_删除 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [req.queryPermissionId || 系统管理中心_短信发送策略_可读].join(","), verifyPermissionIds: [系统管理中心_短信发送策略_新增, 系统管理中心_短信发送策略_编辑, 系统管理中心_短信发送策略_删除].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<LocationItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 获取邮件发送策略分配信息查询
 */
export async function queryEmailAssignedList(req: { queryType: string /* 查询类型 */;queryId: string /* 查询主键ID */;pageNumber: string /* 页码, 默认第一页 */; pageSize: string /* 页大小, 默认10 */; sort: string[]; keyword: string /* 模糊查询关键字, 名称/描述 */; regionId: string /* 区域ID */; externalId: string /* 外部ID */; supportNoteId: string /* 行动策略ID */; active: string /* 是否激活 */; queryPermissionId?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/email_strategy/2.0/distribution`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        bindParamByObj({ queryType: req.queryType /* 查询类型 */,queryId: req.queryId /* 查询主键ID */,pageNumber: req.pageNumber /* 页码, 默认第一页 */, pageSize: req.pageSize /* 页大小, 默认10 */, sort: req.sort, keyword: req.keyword /* 模糊查询关键字, 名称/描述 */, regionId: req.regionId /* 区域ID */, externalId: req.externalId /* 外部ID */, supportNoteId: req.supportNoteId /* 行动策略ID */, active: req.active /* 是否激活 */ }, $req.params);
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 系统管理中心_邮件发送策略_可读, 系统管理中心_邮件发送策略_新增, 系统管理中心_邮件发送策略_编辑, 系统管理中心_邮件发送策略_删除 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [req.queryPermissionId || 系统管理中心_邮件发送策略_可读].join(","), verifyPermissionIds: [系统管理中心_邮件发送策略_新增, 系统管理中心_邮件发送策略_编辑, 系统管理中心_邮件发送策略_删除].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<LocationItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 获取行动策略分配信息查询
 */
export async function querySupportAssignedList(req: { queryType: string /* 查询类型 */;queryId: string /* 查询主键ID */;pageNumber: string /* 页码, 默认第一页 */; pageSize: string /* 页大小, 默认10 */; sort: string[]; keyword: string /* 模糊查询关键字, 名称/描述 */; regionId: string /* 区域ID */; externalId: string /* 外部ID */; supportNoteId: string /* 行动策略ID */; active: string /* 是否激活 */; queryPermissionId?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/support_notes/tenant/distribution`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        bindParamByObj({ queryType: req.queryType /* 查询类型 */,queryId: req.queryId /* 查询主键ID */,pageNumber: req.pageNumber /* 页码, 默认第一页 */, pageSize: req.pageSize /* 页大小, 默认10 */, sort: req.sort, keyword: req.keyword /* 模糊查询关键字, 名称/描述 */, regionId: req.regionId /* 区域ID */, externalId: req.externalId /* 外部ID */, supportNoteId: req.supportNoteId /* 行动策略ID */, active: req.active /* 是否激活 */ }, $req.params);
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_行动策略_可读, 资产管理中心_行动策略_新增, 资产管理中心_行动策略_编辑, 资产管理中心_行动策略_删除 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [req.queryPermissionId || 资产管理中心_行动策略_可读].join(","), verifyPermissionIds: [资产管理中心_行动策略_新增, 资产管理中心_行动策略_编辑, 资产管理中心_行动策略_删除].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<LocationItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 获取优先级分配信息查询
 */
export function queryDegradeAssignedList(data: RequestBase) {
  return request<never, any>({
    url: `${SERVER.EVENT_CENTER}/degrade/2.0/distribution`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}