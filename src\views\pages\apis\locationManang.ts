import { SERVER, Method, type Response, type RequestBase, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

export interface Locations {
  id: string;
  tenantId: string;
  regionId: string;
  name: string;
  description: string;
  zoneId: string;
  externalId: string;
  country: string;
  postcode: string;
  address: string[];
  version: string;
  createdTime: string;
  updatedTime: string;
  createdBy: string;
  updatedBy: string;
}

interface LocationsPostParams {
  id: string;
  regionId: string;
  name: string;
  description: string;
  externalId: string;
  country: string;
  zoneId: string;
  postcode: string;
  address: string[];
}

export function locationsTenantCurrent(data: Partial<LocationsPostParams> & RequestBase) {
  return request<Locations>({
    url: `${SERVER.CMDB}/locations/tenant/current`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function putLocationsTenantCurrent(data: Partial<LocationsPostParams> & RequestBase) {
  return request<Locations>({
    url: `${SERVER.CMDB}/locations/${data.id}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * @description 分页查询场所列表-响应体
 * @url http://*************:3000/project/47/interface/api/23115
 */
export interface LocationItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 区域ID */
  regionId: /* Integer */ string;
  /** 行动策略列表 */
  supportNoteIds: /* Integer */ string[];
  /** 场所名称 */
  name: string;
  /** 描述 */
  description?: string;
  /** 时区Id */
  zoneId?: string;
  /** 外部ID */
  externalId?: string;
  /** 国家编码 */
  country?: string;
  /** 省级编码 */
  province?: string;
  /** 市级编码 */
  city?: string;
  /** 邮编代码 */
  postcode?: string;
  /** 地址 */
  address: string[];
  /** 是否激活 */
  active: boolean;
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
  /** 拥有的权限 */
  hasPermissionIds: /* Integer */ string[];
}

/**
 * @description 分页查询场所列表
 * @url http://*************:3000/project/47/interface/api/23115
 */
export async function getLocationsTenantCurrent(req: Record<string, any> & { pageNumber: string /* 页码, 默认第一页 */; pageSize: string /* 页大小, 默认10 */; sort: string[]; keyword: string /* 模糊查询关键字, 名称/描述 */; regionId: string /* 区域ID */; externalId: string /* 外部ID */; supportNoteId: string /* 行动策略ID */; active: string /* 是否激活 */; queryPermissionId?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/locations/2.0/tenant/current/filter`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        bindParamByObj(
          {
            pageNumber: req.pageNumber /* 页码, 默认第一页 */,
            pageSize: req.pageSize /* 页大小, 默认10 */,
            sort: req.sort,
            keyword: req.keyword /* 模糊查询关键字, 名称/描述 */,
            regionId: req.regionId /* 区域ID */,
            externalId: req.externalId /* 外部ID */,
            supportNoteId: req.supportNoteId /* 行动策略ID */,
            active: req.active /* 是否激活 */,

            ...([...(req.includeName instanceof Array ? req.includeName : []), ...(req.excludeName instanceof Array ? req.excludeName : []), ...(req.eqName instanceof Array ? req.eqName : []), ...(req.neName instanceof Array ? req.neName : [])].filter((v) => v).length ? { nameFilterRelation: req.nameFilterRelation === "OR" ? "OR" : "AND", includeName: req.includeName instanceof Array && req.includeName.length ? req.includeName.join(",") : void 0, excludeName: req.excludeName instanceof Array && req.excludeName.length ? req.excludeName.join(",") : void 0, eqName: req.eqName instanceof Array && req.eqName.length ? req.eqName.join(",") : void 0, neName: req.neName instanceof Array && req.neName.length ? req.neName.join(",") : void 0 } : {}),

            ...([...(req.includeDescription instanceof Array ? req.includeDescription : []), ...(req.excludeDescription instanceof Array ? req.excludeDescription : []), ...(req.eqDescription instanceof Array ? req.eqDescription : []), ...(req.neDescription instanceof Array ? req.neDescription : [])].filter((v) => v).length ? { descriptionFilterRelation: req.descriptionFilterRelation === "OR" ? "OR" : "AND", includeDescription: req.includeDescription instanceof Array && req.includeDescription.length ? req.includeDescription.join(",") : void 0, excludeDescription: req.excludeDescription instanceof Array && req.excludeDescription.length ? req.excludeDescription.join(",") : void 0, eqDescription: req.eqDescription instanceof Array && req.eqDescription.length ? req.eqDescription.join(",") : void 0, neDescription: req.neDescription instanceof Array && req.neDescription.length ? req.neDescription.join(",") : void 0 } : {}),

            ...([...(req.includeArea instanceof Array ? req.includeArea : []), ...(req.excludeArea instanceof Array ? req.excludeArea : []), ...(req.eqArea instanceof Array ? req.eqArea : []), ...(req.neArea instanceof Array ? req.neArea : [])].filter((v) => v).length ? { areaFilterRelation: req.areaFilterRelation === "OR" ? "OR" : "AND", includeArea: req.includeArea instanceof Array && req.includeArea.length ? req.includeArea.join(",") : void 0, excludeArea: req.excludeArea instanceof Array && req.excludeArea.length ? req.excludeArea.join(",") : void 0, eqArea: req.eqArea instanceof Array && req.eqArea.length ? req.eqArea.join(",") : void 0, neArea: req.neArea instanceof Array && req.neArea.length ? req.neArea.join(",") : void 0 } : {}),

            ...([...(req.includeRegion instanceof Array ? req.includeRegion : []), ...(req.excludeRegion instanceof Array ? req.excludeRegion : []), ...(req.eqRegion instanceof Array ? req.eqRegion : []), ...(req.neRegion instanceof Array ? req.neRegion : [])].filter((v) => v).length ? { regionFilterRelation: req.regionFilterRelation === "OR" ? "OR" : "AND", includeRegion: req.includeRegion instanceof Array && req.includeRegion.length ? req.includeRegion.join(",") : void 0, excludeRegion: req.excludeRegion instanceof Array && req.excludeRegion.length ? req.excludeRegion.join(",") : void 0, eqRegion: req.eqRegion instanceof Array && req.eqRegion.length ? req.eqRegion.join(",") : void 0, neRegion: req.neRegion instanceof Array && req.neRegion.length ? req.neRegion.join(",") : void 0 } : {}),

            ...([...(req.includeZoneId instanceof Array ? req.includeZoneId : []), ...(req.excludeZoneId instanceof Array ? req.excludeZoneId : []), ...(req.eqZoneId instanceof Array ? req.eqZoneId : []), ...(req.neZoneId instanceof Array ? req.neZoneId : [])].filter((v) => v).length ? { zoneIdFilterRelation: req.zoneIdFilterRelation === "OR" ? "OR" : "AND", includeZoneId: req.includeZoneId instanceof Array && req.includeZoneId.length ? req.includeZoneId.join(",") : void 0, excludeZoneId: req.excludeZoneId instanceof Array && req.excludeZoneId.length ? req.excludeZoneId.join(",") : void 0, eqZoneId: req.eqZoneId instanceof Array && req.eqZoneId.length ? req.eqZoneId.join(",") : void 0, neZoneId: req.neZoneId instanceof Array && req.neZoneId.length ? req.neZoneId.join(",") : void 0 } : {}),

            ...([...(req.includeExternalId instanceof Array ? req.includeExternalId : []), ...(req.excludeExternalId instanceof Array ? req.excludeExternalId : []), ...(req.eqExternalId instanceof Array ? req.eqExternalId : []), ...(req.neExternalId instanceof Array ? req.neExternalId : [])].filter((v) => v).length ? { externalIdFilterRelation: req.externalIdFilterRelation === "OR" ? "OR" : "AND", includeExternalId: req.includeExternalId instanceof Array && req.includeExternalId.length ? req.includeExternalId.join(",") : void 0, excludeExternalId: req.excludeExternalId instanceof Array && req.excludeExternalId.length ? req.excludeExternalId.join(",") : void 0, eqExternalId: req.eqExternalId instanceof Array && req.eqExternalId.length ? req.eqExternalId.join(",") : void 0, neExternalId: req.neExternalId instanceof Array && req.neExternalId.length ? req.neExternalId.join(",") : void 0 } : {}),
          },
          $req.params
        );

        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_场所_可读, 资产管理中心_场所_新增, 资产管理中心_场所_编辑, 资产管理中心_场所_删除, 资产管理中心_场所_安全 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [req.queryPermissionId || 资产管理中心_场所_可读].join(","), verifyPermissionIds: [资产管理中心_场所_新增, 资产管理中心_场所_编辑, 资产管理中心_场所_删除, 资产管理中心_场所_安全].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<LocationItem[]>>($req)),
    { controller }
  );
}

// export function getLocationsTenantCurrent(data: { pageNumber?: number; pageSize?: number; keyword?: string; active?: string } & RequestBase) {
//   return request<never, Response<Locations[]>>({
//     url: `${SERVER.CMDB}/locations/tenant/current`,
//     method: Method.Get,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: data,
//     data: {},
//   });
// }

export function delLocationsTenantCurrent(data: { id: string } & RequestBase) {
  return request<never, Response<Locations[]>>({
    url: `${SERVER.CMDB}/locations/${data.id}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function setLocationsContacts(data: object & RequestBase, id: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/locations/${id}/contacts`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function getLocationsContacts(data: { id: string } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/locations/${data.id}/contacts`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function delLocationsContacts(data: object & RequestBase, id: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/locations/${id}/contacts`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
// 根据父节点查询地区

export function getRegion(data: { parentIndexCode: string } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/area/getAreaListByParentIndexCode?parentIndexCode=${data.parentIndexCode}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
