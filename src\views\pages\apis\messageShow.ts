import { SERVER, Method, type Response, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

/**
 * @description 短信发送日志分页列表-query请求参数
 * @url http://*************:3000/project/178/interface/api/29410
 */
export interface MessageshowReqQuery {
  /** 发送起始时间 */
  sendTimeStart?: string;
  /** 发送结束时间 */
  sendTimeEnd?: string;
  /** 发送状态 */
  success?: string;
  /** 页码, 默认第一页 */
  pageNumber: string;
  /** 页大小, 默认10 */
  pageSize: string;
}

/**
 * @description 短信发送日志分页列表-响应体
 * @url http://*************:3000/project/178/interface/api/29410
 */
export interface CurrentListResBody {
  /** 请求trace id  「已废弃」转移到响应头中, 为了保持向后兼容, 暂时保留此字段 「已废弃」 */
  traceId?: string;
  /** 是否成功, 正常响应该值不会为空 */
  success?: boolean;
  /** 响应编码 <p> 为了向后兼容暂时保留 「已废弃」 */
  code: /* Integer */ string;
  /** 引导编码, 当出现错误时引导客户端如何处理, 对于相同类型的错误, 这个值始终不会改变 */
  guide?: string;
  message: string;
  /** 返回的数据 */
  data: {
    id: /* Integer */ string;
    /** 发送租户ID */
    tenantId?: /* Integer */ string;
    /** 手机号列表 */
    phones?: string[];
    /** 短信内容 */
    content?: string;
    /** 短信是否发送成功 */
    success?: boolean;
    /** 发送失败的错误信息 */
    errorMessage?: string;
    /** 短信发送时间 */
    sendTime: /* Integer */ string;
  }[];
  /** 页码 */
  page: /* Integer */ string;
  /** 页大小 */
  size: /* Integer */ string;
  /** 总数量 */
  total: /* Integer */ string;
  /** 总页数 */
  totalPages: /* Integer */ string;
}

/**
 * @description 短信发送日志分页列表
 * @url http://*************:3000/project/178/interface/api/29410
 */
export function getMessageList(req: { sendTimeStart?: string /* 发送起始时间 */; sendTimeEnd?: string /* 发送结束时间 */; success?: string /* 发送状态 */; pageNumber: string /* 页码, 默认第一页 */; pageSize: string /* 页大小, 默认10 */ } & Record<string, any>) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CS}/sms/logs/current/filter`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.pageNumber /* 页码, 默认第一页 */, pageSize: req.pageSize /* 页大小, 默认10 */ }, $req.params);

        bindParamByObj(
          {
            sendTimeStart: req.sendTimeStart || void 0 /* 发送起始时间 */,
            sendTimeEnd: req.sendTimeEnd || void 0 /* 发送结束时间 */,
            success: req.success || void 0 /* 发送状态 */,

            // ...([...(req.eqPhone instanceof Array ? req.eqPhone : []), ...(req.nePhone instanceof Array ? req.nePhone : [])].filter((v) => v).length ? { phoneFilterRelation: req.phoneFilterRelation === "OR" ? "OR" : "AND", eqPhone: req.eqPhone instanceof Array && req.eqPhone.length ? req.eqPhone.join(",") : void 0, nePhone: req.nePhone instanceof Array && req.nePhone.length ? req.nePhone.join(",") : void 0 } : {}),

            ...([...(req.includePhone instanceof Array ? req.includePhone : []), ...(req.excludePhone instanceof Array ? req.excludePhone : []), ...(req.eqPhone instanceof Array ? req.eqPhone : []), ...(req.nePhone instanceof Array ? req.nePhone : [])].filter((v) => v).length ? { phoneFilterRelation: req.phoneFilterRelation === "OR" ? "OR" : "AND", includePhone: req.includePhone instanceof Array && req.includePhone.length ? req.includePhone.join(",") : void 0, excludePhone: req.excludePhone instanceof Array && req.excludePhone.length ? req.excludePhone.join(",") : void 0, eqPhone: req.eqPhone instanceof Array && req.eqPhone.length ? req.eqPhone.join(",") : void 0, nePhone: req.nePhone instanceof Array && req.nePhone.length ? req.nePhone.join(",") : void 0 } : {}),
            queryType: req.queryType,

            ...([
              ...(req.includeContent instanceof Array ? req.includeContent : []),
              ...(req.excludeContent instanceof Array ? req.excludeContent : []),
              /* ...(req.eqPhone instanceof Array ? req.eqPhone : []), */
              /* ...(req.nePhone instanceof Array ? req.nePhone : []), */
            ].filter((v) => v).length
              ? {
                  contentFilterRelation: req.contentFilterRelation === "OR" ? "OR" : "AND",
                  includeContent: req.includeContent instanceof Array && req.includeContent.length ? req.includeContent.join(",") : void 0,
                  excludeContent: req.excludeContent instanceof Array && req.excludeContent.length ? req.excludeContent.join(",") : void 0,
                  /* eqPhone: req.eqPhone instanceof Array && req.eqPhone.length ? req.eqPhone.join(",") : void 0, */
                  /* nePhone: req.nePhone instanceof Array && req.nePhone.length ? req.nePhone.join(",") : void 0, */
                }
              : {}),
          },
          $req.params
        );

        $req.data = void 0;
        return $req;
      })
      .then(($req) => request($req)),
    { controller }
  );
}

/**
 * @description 邮件发送日志分页列表
 * @url http://*************:3000/project/178/interface/api/29418
 */
export function getMailList(req: { sendTimeStart: string /* 发送起始时间 */; sendTimeEnd: string /* 发送结束时间 */; success: string /* 发送状态 */; pageNumber: string /* 页码, 默认第一页 */; pageSize: string /* 页大小, 默认10 */ } & Record<string, any>) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CS}/mail/logs/current/filter`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.pageNumber /* 页码, 默认第一页 */, pageSize: req.pageSize /* 页大小, 默认10 */, queryType: req.queryType }, $req.params);

        bindParamByObj(
          {
            sendTimeStart: req.sendTimeStart ? req.sendTimeStart : void 0 /* 发送起始时间 */,
            sendTimeEnd: req.sendTimeEnd ? req.sendTimeEnd : void 0 /* 发送结束时间 */,

            success: req.success || void 0 /* 发送状态 */,

            // ...([...(req.eqTo instanceof Array ? req.eqTo : []), ...(req.neTo instanceof Array ? req.neTo : [])].filter((v) => v).length ? { toFilterRelation: req.toFilterRelation === "OR" ? "OR" : "AND", eqTo: req.eqTo instanceof Array && req.eqTo.length ? req.eqTo.join(",") : void 0, neTo: req.neTo instanceof Array && req.neTo.length ? req.neTo.join(",") : void 0 } : {}),

            ...([...(req.includeTo instanceof Array ? req.includeTo : []), ...(req.excludeTo instanceof Array ? req.excludeTo : []), ...(req.eqTo instanceof Array ? req.eqTo : []), ...(req.neTo instanceof Array ? req.neTo : [])].filter((v) => v).length ? { toFilterRelation: req.toFilterRelation === "OR" ? "OR" : "AND", includeTo: req.includeTo instanceof Array && req.includeTo.length ? req.includeTo.join(",") : void 0, excludeTo: req.excludeTo instanceof Array && req.excludeTo.length ? req.excludeTo.join(",") : void 0, eqTo: req.eqTo instanceof Array && req.eqTo.length ? req.eqTo.join(",") : void 0, neTo: req.neTo instanceof Array && req.neTo.length ? req.neTo.join(",") : void 0 } : {}),

            // ...([...(req.eqFrom instanceof Array ? req.eqFrom : []), ...(req.neFrom instanceof Array ? req.neFrom : [])].filter((v) => v).length ? { fromFilterRelation: req.fromFilterRelation === "OR" ? "OR" : "AND", eqFrom: req.eqFrom instanceof Array && req.eqFrom.length ? req.eqFrom.join(",") : void 0, neFrom: req.neFrom instanceof Array && req.neFrom.length ? req.neFrom.join(",") : void 0 } : {}),
            ...([...(req.includeFrom instanceof Array ? req.includeFrom : []), ...(req.excludeFrom instanceof Array ? req.excludeFrom : []), ...(req.eqFrom instanceof Array ? req.eqFrom : []), ...(req.neFrom instanceof Array ? req.neFrom : [])].filter((v) => v).length ? { fromFilterRelation: req.fromFilterRelation === "OR" ? "OR" : "AND", includeFrom: req.includeFrom instanceof Array && req.includeFrom.length ? req.includeFrom.join(",") : void 0, excludeFrom: req.excludeFrom instanceof Array && req.excludeFrom.length ? req.excludeFrom.join(",") : void 0, eqFrom: req.eqFrom instanceof Array && req.eqFrom.length ? req.eqFrom.join(",") : void 0, neFrom: req.neFrom instanceof Array && req.neFrom.length ? req.neFrom.join(",") : void 0 } : {}),

            ...([
              ...(req.includeContent instanceof Array ? req.includeContent : []),
              ...(req.excludeContent instanceof Array ? req.excludeContent : []),
              /* ...(req.eqPhone instanceof Array ? req.eqPhone : []), */
              /* ...(req.nePhone instanceof Array ? req.nePhone : []), */
            ].filter((v) => v).length
              ? {
                  contentFilterRelation: req.contentFilterRelation === "OR" ? "OR" : "AND",
                  includeContent: req.includeContent instanceof Array && req.includeContent.length ? req.includeContent.join(",") : void 0,
                  excludeContent: req.excludeContent instanceof Array && req.excludeContent.length ? req.excludeContent.join(",") : void 0,
                  /* eqPhone: req.eqPhone instanceof Array && req.eqPhone.length ? req.eqPhone.join(",") : void 0, */
                  /* nePhone: req.nePhone instanceof Array && req.nePhone.length ? req.nePhone.join(",") : void 0, */
                }
              : {}),
          },
          $req.params
        );

        $req.data = void 0;
        return $req;
      })
      .then(($req) => request($req)),
    { controller }
  );
}
