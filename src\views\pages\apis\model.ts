import { SERVER, Method, type Response, type RequestBase, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

export interface SlaConfigList {
  id: string;

  tenantId: string;
}

/**
 * @description 当前组织下的用户
 */
export interface UsersInfoItem {
  /** 用户ID */
  id: /* Integer */ string;
  /** 所属平台 */
  platform: string;
  /** 所属租户 */
  tenantId: /* Integer */ string;
  /** 安全容器ID */
  containerId: /* Integer */ string;
  /** 姓名 */
  name: string;
  /** 昵称 */
  nickname?: string;
  /** 账号 */
  account?: string;
  /** 手机号 */
  phone?: string;
  /** 邮箱 */
  email?: string;
  /** 语言 */
  language?: string;
  /** 时区 */
  zoneId?: string;
  /** 性别 */
  gender?: /* 枚举: SECRET :保密 | MALE :男性 | FEMALE :女性 */ "SECRET" | "MALE" | "FEMALE";
  /** 头像 */
  profilePicture?: string;
  /** 双因素认证启用状态 */
  mfaState?: /* 枚举: ENABLED :启用MFA认证 | DISABLED :禁用MFA认证 | DEFAULT :使用系统默认配置 */ "ENABLED" | "DISABLED" | "DEFAULT";
  /** 密码修改时间戳 */
  passwordTime?: /* Integer */ string;
  /** 是否忙碌 */
  busy: boolean;
  /** 进入忙碌状态的时间戳 */
  busyTime?: /* Integer */ string;
  /** 是否已完善个人信息 */
  improved: boolean;
  /** 是否被锁定 */
  blocked: boolean;
  /** 最近登录时间 */
  lastLoginTime?: /* Integer */ string;
  /** 最近访问时间 */
  lastActivity?: /* Integer */ string;
  /** 创建时间 */
  createdTime?: /* Integer */ string;
  /** 所属租户名称 */
  tenantName?: string;
  /** 所属租户的缩写 */
  tenantAbbreviation?: string;
  /** 是否默认启用了MFA */
  enableMfaDefault: boolean;
  /** 是否已脱敏 */
  desensitized: boolean;
  /** 加入租户的事件, 多租户平台有效 */
  joinTenantTime?: /* Integer */ string;
  /** 用户是否被租户冻结, 多租户平台有效 */
  blockedInTenant?: boolean;
  groups?: {
    /** 用户组ID */
    id?: /* Integer */ string;
    /** 用户组名称 */
    name?: string;
  }[];
}

/**
 * @description 查询当前组织下的用户列表(脱敏)
 * @url http://*************:3000/project/11/interface/api/2637
 */
export function getUserList(req: { userId?: string /* 用户ID, 最高优先级 */; userIds?: string[] /* 用户ID列表 */; keyword?: string /* 查询关键字, 支持: 账号/邮箱/手机号/姓名/昵称 */; ident?: string /* 唯一标识, 如果传了keyword则该条件不生效. 支持: 账号/邮箱/手机号 */; blocked?: string /* 锁定状态 */; scope?: string /* 查询范围, 默认全部 ALL :所有的用户, 默认INTERNAL :仅租户内部的用户EXTERNAL :仅租户外部的用户 */; pageNumber: number /* 页码, 默认第一页 */; pageSize: number /* 页大小, 默认10 */; sort?: string[]; includeGroups?: string; permissionId?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/users/current_org/info`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ userId: req.userId /* 用户ID, 最高优先级 */, userIds: req.userIds /* 用户ID列表 */, keyword: req.keyword /* 查询关键字, 支持: 账号/邮箱/手机号/姓名/昵称 */, ident: req.ident /* 唯一标识, 如果传了keyword则该条件不生效. 支持: 账号/邮箱/手机号 */, blocked: req.blocked /* 锁定状态 */, scope: req.scope /* 查询范围, 默认全部 ALL :所有的用户, 默认INTERNAL :仅租户内部的用户EXTERNAL :仅租户外部的用户 */, pageNumber: req.pageNumber /* 页码, 默认第一页 */, pageSize: req.pageSize /* 页大小, 默认10 */, sort: req.sort, includeGroups: req.includeGroups }, $req.params);
        try {
          const { default: getUserInfo } = await import("@/utils/getUserInfo");
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ permissionId: req.permissionId || (await import("@/views/pages/permission")).安全管理中心_用户管理_可读 }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<UsersInfoItem[]>>($req)),
    { controller }
  );
}

// export function getUserList(data: { pageNumber: number; pageSize: number } & RequestBase) {
//   return request<SlaConfigList[]>({
//     url: `${SERVER.IAM}/users/current_org/info`,
//     method: Method.Get,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: { ...data, permissionId: ["515414964874248192"].join(",") },
//     data: {},
//   });
// }
//关联类型列表
export function getModelList(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/model_relation_types/page`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//新增关联类型
export function addModel(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/model_relation_types`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//编辑关联类型
export function editModel(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/model_relation_types/${data.ident}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//删除关联类型
export function deleteModel(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/model_relation_types/${data.ident}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//模型列表
export function getModelManageList(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/model_groups/details?key=${data.key}&enabled=${data.enabled}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//模型详情
export function modelResources(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/resources/${data.ident}/count`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: data,
  });
}
//获取所有模型
export function getAllModel(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/models?key=${data.key}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//新增模型
export function addModelManage(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/models`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//编辑模型
export function editModelManage(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/models/${data.ident}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//操作模型
export function editModelManageOther(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/models/${data.id}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//模型详情
export function modelManageDetail(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/models/${data.ident}/details`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: data,
  });
}
//删除模型
export function delModelManage(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/models/${data.ident}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//模型详情
export function getModelManageDetail(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/vendors/${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//模型分组列表
export function getGroupsList(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/model_groups`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//新增模型分组
export function addGroup(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/model_groups`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//编辑模型分组
export function editGroup(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/model_groups/${data.ident}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//删除模型分组
export function delGroup(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/model_groups/${data.ident}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//模型关联列表
export function getmodelRelationsList(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/model_relations/page?modelIdent=${data.ident}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//新增模型关联
export function addmodelRelations(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/model_relations`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//编辑模型关联
export function editmodelRelations(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/model_relations/${data.ident}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//删除模型关联
export function delmodelRelations(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/model_relations/${data.ident}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//获取模型通用字段列表
export function getGeneralFieldsList(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/table_sort_configs/general_fields/list`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: data,
  });
}
//获取表格排序配置
export function getGeneralFields(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/table_sort_configs/${data.ident}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: data,
  });
}
//保存表格排序配置
export function editGeneralFields(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/table_sort_configs/${data.ident}`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//获取当前租户的表格排序配置，资源列表中使用
export function getGeneralFieldsCurrentList(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/table_sort_configs/${data.ident}/current`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: data,
  });
}

//保存当前租户的表格排序配置，资源列表中使用
export function editGeneralCurrentFields(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/table_sort_configs/${data.ident}/current`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//重置当前租户的表格排序配置，恢复默认
export function editGeneralCurrentResetFields(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/table_sort_configs/${data.ident}/current/reset`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//当前租户分页查询资源
export function getResourcesList(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/resources/tenant/current/desensitized`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//获取当前租户的表格排序配置，资源列表中使用
export function getResourcesHeader(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/table_sort_configs/${data.modelIdent}/current`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: data,
  });
}
//分页获取要导出的资源列表
// export function getResourcesExport(data: { pageNumber: number; pageSize: number } & RequestBase) {
//   return request<SlaConfigList[]>({
//     url: `${SERVER.CMDB}/resources/export`,
//     method: Method.Get,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: data,
//     data: data,
//   });
// }
export function getResourcesExport(data: { file: File } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/resources/export`,
    method: Method.Get,
    signal: undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}
//为当前租户创建资源
export function addResource(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/resources/tenant/current`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//选择性更新资源信息
export function editResource(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/resources/${data.modelIdent}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
