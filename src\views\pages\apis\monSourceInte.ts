import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";

export interface ServiceCenterList {
  id: string;
  sourceType: string; //数据源类型
  enabled: boolean; //是否启用
}
//监控源集成
export function getMointerSourceList(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<ServiceCenterList[]>({
    url: `${SERVER.EVENT_CENTER}/integration/configs/tenant/current`,
    method: Method.Get,
    signal: undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//新增监控源集成
export function addtMointerSource(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<ServiceCenterList[]>({
    url: `${SERVER.EVENT_CENTER}/integration/configs/tenant/current`,
    method: Method.Post,
    signal: undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//监控源集成启用/禁用
export function updateMointerSource(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<ServiceCenterList[]>({
    url: `${SERVER.EVENT_CENTER}/integration/configs/${data.id}/enabled`,
    method: Method.Patch,
    signal: undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//监控源集成详情
export function MointerSourceDetails(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<ServiceCenterList[]>({
    url: `${SERVER.EVENT_CENTER}/integration/configs/${data.id}`,
    method: Method.Get,
    signal: undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//解除集成
export function DeleteMointerSource(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<ServiceCenterList[]>({
    url: `${SERVER.EVENT_CENTER}/integration/configs/${data.id}`,
    method: Method.Delete,
    signal: undefined,
    headers: {},
    params: {},
    data: {},
  });
}
