import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";

export function exportLoginLogs(data: {} & RequestBase) {
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_IAM}/loginRecord/exportLoginLogs`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

export function exportLastLoginLogs(data: {} & RequestBase) {
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_IAM}/loginRecord/exportLastLoginLogs`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

export function exportBreachedIncidentTicket(data: {} & RequestBase) {
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_EVENT}/timeout_event_report`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

export function exportTicketsClosedInCertainHours(data: {} & RequestBase) {
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_EVENT}/ticketsClosedInCertainHours`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

export function exportCustomerTicketCount(data: {} & RequestBase) {
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_EVENT}/customerTicketCount`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

export function exportQrcodeDetail(data: {} & RequestBase) {
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_EVENT}/qrCodeFaultReport`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

export function exportDictEvent(data: {} & RequestBase) {
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_EVENT}/dictEventDownLoad`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

export function exportDictService(data: {} & RequestBase) {
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_EVENT}/dictServiceDownLoad`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

export function exportTicketsCreatedByHourly(data: {} & RequestBase) {
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_EVENT}/ticketsCreatedByHourly`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

export function exportTicketsPriorityAnalysis(data: {} & RequestBase) {
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_EVENT}/ticketsPriorityAnalysis`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

export function exportTicketResolutionStatistics(data: {} & RequestBase) {
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_EVENT}/ticketResolutionStatistics`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

export function exportTicketClosureCodeStatistics(data: {} & RequestBase) {
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_EVENT}/ticketClosureCodeStatistics`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

export function exportTicketsClosedByUsers(data: {} & RequestBase) {
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_EVENT}/ticketsClosedByUsers`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

export function exportTicketsCreatedByUsers(data: {} & RequestBase) {
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_EVENT}/ticketsCreatedByUsers`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

export function exportTicketsDetailsStatistics(data: {} & RequestBase) {
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_EVENT}/dictEventDownLoad`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}

export function exportDeviceAlerts(data: {} & RequestBase) {
  return request<never, Response<Blob>>({
    url: `${SERVER.REPORT_EVENT}/deviceAlerts`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}
