import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";

import { priority, priorityOption } from "./eventPriority";
export { priority, priorityOption };
import { eventSeverityOption, eventSeverity } from "./event";
export { eventSeverityOption, eventSeverity };
import { deviceImportance, deviceImportanceOption } from "./device";
export { deviceImportance, deviceImportanceOption };

export enum opinionState {
  SUGGESTION = "SUGGESTION",
  SYSTEM_BUG = "SYSTEM_BUG",
  OTHER = "OTHER",
}

export interface opinionItem {
  type: opinionState;
  content: string;
  status: string;
  keyword: string;
  sort: string;
}

export function opinionCreate(data: {} & RequestBase) {
  return request<unknown, Response<opinionItem>>({
    url: `${SERVER.IAM}/feedbacks`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function getOpinionList(data: {} & RequestBase) {
  const params = new URLSearchParams();
  bindSearchParams(data, params);
  return request<unknown, Response<opinionItem>>({
    url: `${SERVER.IAM}/feedbacks/desensitize`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params,
    data: {},
  });
}

export function getOpinionDetail(data: {} & RequestBase) {
  // const params = new URLSearchParams();
  // bindSearchParams(data, params);
  return request<unknown, Response<opinionItem>>({
    url: `${SERVER.IAM}/feedbacks/${data.id}/detail`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
