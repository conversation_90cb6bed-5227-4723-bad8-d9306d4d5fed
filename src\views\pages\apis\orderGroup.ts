import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";
import { computed } from "vue";
import getUserInfo from "@/utils/getUserInfo";
import { 服务管理中心_工单组_新建, 服务管理中心_工单组_更新, 服务管理中心_工单组_删除, 服务管理中心_工单组_可读, 服务管理中心_工单组_安全 } from "@/views/pages/permission";

export interface OrderGroup {
  /** 主键Id */
  id: /* Integer */ string;
  /** 租户 Id */
  tenantId: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 工单组名称 */
  ticketGroupName: string;
  /** 描述 */
  description?: string;
  /** 发送邮箱 */
  sendEmail?: string;
  /** 是否直接关闭 */
  close: boolean;
  /** 事件自动关闭时间 */
  autoCloseTime: /* Integer */ string;
  /** 告警分类处理顺序(预留字段，暂不使用) */
  alarmClassification?: string[];
}

export const autoCloseTimeOption = computed(() => [
  /*  */
  // { label: "测试时间1分钟", value: (() => 1000 * 60 * 1)() },
  // { label: "测试时间5分钟", value: (() => 1000 * 60 * 5)() },
  { label: "不设置", value: 0 },
  { label: "4 h", value: (() => 1000 * 60 * 60 * 4)().toString() },
  { label: "12 h", value: (() => 1000 * 60 * 60 * 12)().toString() },
  { label: "1 Day", value: (() => 1000 * 60 * 60 * 24)().toString() },
  { label: "2 Days", value: (() => 1000 * 60 * 60 * 24 * 2)().toString(), default: true },
  { label: "3 Days", value: (() => 1000 * 60 * 60 * 24 * 3)().toString() },
  { label: "1 Week", value: (() => 1000 * 60 * 60 * 24 * 7)().toString() },
  { label: "2 Weeks", value: (() => 1000 * 60 * 60 * 24 * 7 * 2)().toString() },
  { label: "4 Weeks", value: (() => 1000 * 60 * 60 * 24 * 7 * 4)().toString() },
  { label: "8 Weeks", value: (() => 1000 * 60 * 60 * 24 * 7 * 8)().toString() },
]);

export function addOrderGroup(data: {} & RequestBase) {
  return request<never, Response<OrderGroup>>({
    url: `${SERVER.EVENT_CENTER}/ticket_group/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function getOrderGroup(data: {} & RequestBase) {
  return request<never, Response<OrderGroup[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_group/query`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { pageNumber: data.pageNumber, pageSize: data.pageSize },
    data: {
      containerId: (getUserInfo().currentTenant || {}).containerId,
      queryPermissionId: data.queryPermissionId || 服务管理中心_工单组_可读,
      verifyPermissionIds: data.verifyPermissionIds || [服务管理中心_工单组_新建, 服务管理中心_工单组_更新, 服务管理中心_工单组_删除, 服务管理中心_工单组_安全].join(),
      ...([...(data.includeTicketGroupName instanceof Array ? data.includeTicketGroupName : []), ...(data.excludeTicketGroupName instanceof Array ? data.excludeTicketGroupName : []), ...(data.eqTicketGroupName instanceof Array ? data.eqTicketGroupName : []), ...(data.neTicketGroupName instanceof Array ? data.neTicketGroupName : [])].filter((v) => v).length ? { ticketGroupNameFilterRelation: data.ticketGroupNameFilterRelation === "OR" ? "OR" : "AND", includeTicketGroupName: data.includeTicketGroupName instanceof Array && data.includeTicketGroupName.length ? data.includeTicketGroupName.join(",") : void 0, excludeTicketGroupName: data.excludeTicketGroupName instanceof Array && data.excludeTicketGroupName.length ? data.excludeTicketGroupName.join(",") : void 0, eqTicketGroupName: data.eqTicketGroupName instanceof Array && data.eqTicketGroupName.length ? data.eqTicketGroupName.join(",") : void 0, neTicketGroupName: data.neTicketGroupName instanceof Array && data.neTicketGroupName.length ? data.neTicketGroupName.join(",") : void 0 } : {}),
      ...([...(data.includeDesc instanceof Array ? data.includeDesc : []), ...(data.excludeDesc instanceof Array ? data.excludeDesc : []), ...(data.eqDesc instanceof Array ? data.eqDesc : []), ...(data.neDesc instanceof Array ? data.neDesc : [])].filter((v) => v).length ? { descFilterRelation: data.descFilterRelation === "OR" ? "OR" : "AND", includeDesc: data.includeDesc instanceof Array && data.includeDesc.length ? data.includeDesc.join(",") : void 0, excludeDesc: data.excludeDesc instanceof Array && data.excludeDesc.length ? data.excludeDesc.join(",") : void 0, eqDesc: data.eqDesc instanceof Array && data.eqDesc.length ? data.eqDesc.join(",") : void 0, neDesc: data.neDesc instanceof Array && data.neDesc.length ? data.neDesc.join(",") : void 0 } : {}),
      ...([...(data.includeSendEmail instanceof Array ? data.includeSendEmail : []), ...(data.excludeSendEmail instanceof Array ? data.excludeSendEmail : []), ...(data.eqSendEmail instanceof Array ? data.eqSendEmail : []), ...(data.neSendEmail instanceof Array ? data.neSendEmail : [])].filter((v) => v).length ? { sendEmailFilterRelation: data.sendEmailFilterRelation === "OR" ? "OR" : "AND", includeSendEmail: data.includeSendEmail instanceof Array && data.includeSendEmail.length ? data.includeSendEmail.join(",") : void 0, excludeSendEmail: data.excludeSendEmail instanceof Array && data.excludeSendEmail.length ? data.excludeSendEmail.join(",") : void 0, eqSendEmail: data.eqSendEmail instanceof Array && data.eqSendEmail.length ? data.eqSendEmail.join(",") : void 0, neSendEmail: data.neSendEmail instanceof Array && data.neSendEmail.length ? data.neSendEmail.join(",") : void 0 } : {}),
      autoCloseTime: data.autoCloseTime,
    },
  });
}

export function setOrderGroup(data: {} & RequestBase) {
  return request<never, Response<OrderGroup>>({
    url: `${SERVER.EVENT_CENTER}/ticket_group/update`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function delOrderGroup(data: { id: string } & RequestBase) {
  return request<never, Response<OrderGroup>>({
    url: `${SERVER.EVENT_CENTER}/ticket_group/${data.id}/delete`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//获取租户下的用户组
export function getUserGroups(data: { tenantId: string } & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.IAM}/user_groups/findByTenantIdAndPermissionId?tenantId=${data.tenantId}&permissionId=512890964822458368`,
    // url: `${SERVER.IAM}/tenants/${data.tenantId}/user_groups`,
    method: Method.Get,
    signal: undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function addUserGroupConfiguration(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/user_group_configuration/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data,
  });
}

export interface UserGroupConfigurationItem {
  /** 用户组配置ID */
  id: /* Integer */ string;
  /** 跨客户的ID */
  tenantId: /* Integer */ string;
  /** 跨客户的名称 */
  tenantName: string;
  /** 工单组ID */
  ticketGroupId: /* Integer */ string;
  /** 工单组名称 */
  ticketGroupName: string;
  /** 用户组Id */
  userGrouptId: /* Integer */ string;
  /** 用户组名称 */
  userGroupName: string;
  /** 可分配工单类型，支持多选，事件，服务，问题，变更 ，发布 */
  assignableTicketType?: string[];
  /** 变更审批类型,可支持多选，一般，标准，紧急，重大 */
  changeApprovalType?: string[];
  /** 发布审批类型,可支持多选，一般，标准，紧急，重大 */
  releaseApprovalType?: string[];
  /** 是否关闭工单(默认有工单关闭权限) */
  closeTicket: boolean;
  /** 最长挂起时间(默认最长挂起时间是1天) */
  maxSuspensionTime: /* Integer */ string;
  /** 默认工单分派组 */
  defaultTicketGroup?: boolean;
  /** 处理的告警类型 */
  handleAlarmType?: string[];
  abbreviation: string;
  closeTicketType: string[];
}

export enum AssignableTicketType {
  event = "event",
  service = "service",
  question = "question",
  change = "change",
  publish = "publish",
  dictevent = "dictevent",
  dictservice = "dictservice",
}

export const assignableTicketTypeOption: { label: string; value: keyof typeof AssignableTicketType }[] = [
  { label: "事件", value: AssignableTicketType.event },
  { label: "服务请求", value: AssignableTicketType.service },
  { label: "问题", value: AssignableTicketType.question },
  { label: "变更", value: AssignableTicketType.change },
  { label: "发布", value: AssignableTicketType.publish },
  // { label: "DICT事件", value: AssignableTicketType.dictevent },
  // { label: "DICT服务请求", value: AssignableTicketType.dictservice },
];

export enum ChangeApprovalType {
  ORDINARY = "ORDINARY",
  STANDARD = "STANDARD",
  URGENCY = "URGENCY",
  IMPORTANT = "IMPORTANT",
}

export const changeApprovalTypeOption: { label: string; value: keyof typeof ChangeApprovalType; type: string }[] = [
  { label: "一般", value: ChangeApprovalType.ORDINARY, type: "success" },
  { label: "标准", value: ChangeApprovalType.STANDARD, type: "primary" },
  { label: "紧急", value: ChangeApprovalType.URGENCY, type: "warning" },
  { label: "重大", value: ChangeApprovalType.IMPORTANT, type: "danger" },
];

export enum ReleaseApprovalType {
  ORDINARY = "ORDINARY",
  STANDARD = "STANDARD",
  URGENCY = "URGENCY",
  IMPORTANT = "IMPORTANT",
}

export const releaseApprovalTypeOption: { label: string; value: keyof typeof ReleaseApprovalType; type: string }[] = [
  { label: "一般", value: ReleaseApprovalType.ORDINARY, type: "success" },
  { label: "标准", value: ReleaseApprovalType.STANDARD, type: "primary" },
  { label: "紧急", value: ReleaseApprovalType.URGENCY, type: "warning" },
  { label: "重大", value: ReleaseApprovalType.IMPORTANT, type: "danger" },
];

export const maxSuspensionTimeOption = [
  { label: "1 Day", value: `${60 * 24}` },
  { label: "0 Hour", value: `${0}` },
  { label: "1 Hour", value: `${60}` },
  { label: "4 Hours", value: `${60 * 4}` },
  { label: "8 Hours", value: `${60 * 8}` },
  { label: "12 Hours", value: `${60 * 12}` },
  { label: "2 Days", value: `${60 * 24 * 2}` },
  { label: "3 Days", value: `${60 * 24 * 3}` },
  { label: "1 Week", value: `${60 * 24 * 7}` },
  { label: "2 Weeks", value: `${60 * 24 * 7 * 2}` },
  { label: "4 Weeks", value: `${60 * 24 * 7 * 4}` },
  { label: "6 Weeks", value: `${60 * 24 * 7 * 6}` },
  { label: "8 Weeks", value: `${60 * 24 * 7 * 8}` },
];

export function getUserGroupConfiguration(data: {} & RequestBase) {
  return request<unknown, Response<UserGroupConfigurationItem[]>>({
    url: `${SERVER.EVENT_CENTER}/user_group_configuration/queryAll`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function setUserGroupConfiguration(data: {} & RequestBase) {
  return request<unknown, Response<UserGroupConfigurationItem>>({
    url: `${SERVER.EVENT_CENTER}/user_group_configuration/update`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data,
  });
}

export function delUserGroupConfiguration(data: { id: string } & RequestBase) {
  return request<unknown, Response<UserGroupConfigurationItem>>({
    url: `${SERVER.EVENT_CENTER}/user_group_configuration/${data.id}/delete`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export interface SendPolicyItem {
  /** 主键ID */
  id: /* Integer */ string;
  /** 工单组id */
  ticketGroupId: /* Integer */ string;
  /** 发送策略ID */
  sendPolicyId: /* Integer */ string;
  /** 发送策略名称 */
  sendPolicyName: string;
  /** 发送策略类型---短信:sms  邮件:mail */
  sendPolicyType: string;
  index: /* Integer */ string;
}

export function addSendPolicy(data: {} & RequestBase) {
  return request<unknown, Response<SendPolicyItem>>({
    url: `${SERVER.EVENT_CENTER}/send_policy/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function getSendPolicy(data: {} & RequestBase) {
  return request<unknown, Response<SendPolicyItem[]>>({
    url: `${SERVER.EVENT_CENTER}/send_policy/queryAll`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function setSendPolicy(data: {} & RequestBase) {
  return request<unknown, Response<SendPolicyItem>>({
    url: `${SERVER.EVENT_CENTER}/send_policy/update`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function delSendPolicy(data: {} & RequestBase) {
  return request<unknown, Response<SendPolicyItem>>({
    url: `${SERVER.EVENT_CENTER}/send_policy/${data.id}/delete`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export interface CloseCodeItem {
  id: /* Integer */ string;
  /** 工单组id */
  ticketGroupId: /* Integer */ string;
  /** 关闭代码ID */
  closeCodeId: /* Integer */ string;
  /** 关闭代码名称 */
  closeCodeName: string;
}

export function addCloseCode(data: {} & RequestBase) {
  return request<unknown, Response<CloseCodeItem>>({
    url: `${SERVER.EVENT_CENTER}/close_code/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data,
  });
}

export function getCloseCode(data: {} & RequestBase) {
  return request<unknown, Response<CloseCodeItem[]>>({
    url: `${SERVER.EVENT_CENTER}/close_code/queryAll`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function delCloseCode(data: {} & RequestBase) {
  return request<unknown, Response<CloseCodeItem[]>>({
    url: `${SERVER.EVENT_CENTER}/close_code/${data.id}/delete`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export interface TicketDegrade {
  /** 降级策略ID */
  id: /* Integer */ string;
  /** 工单组id */
  ticketGroupId: /* Integer */ string;
  /** 生效时间配置 */
  effectTimeCfg: {
    /** 时区 */
    timeZone: string;
    /** 是否使用客户时区 */
    useCustomerTimeZone: boolean;
    /** 是否使用设备时区 */
    useDeviceTimeZone: boolean;
    /** 覆盖时间 */
    coverWorkTime: {
      /** 星期标识，用于排序(星期一传1，星期二传2，以此类推) */
      weekDay: /* Integer */ string;
      /** 生效时间 */
      workTime: /* Integer */ string[];
    }[];
    /** 降级配置列表 */
    degradeConfigs: {
      /** 工作时间内优先降级级数 */
      degradeInWorkTime: /* Integer */ string;
      /** 工作时间内优先降级级数是否生效 */
      degradeInWorkTimeEnable: boolean;
      /** 禁止下降的优先级(在这个范围内的优先级不降级) */
      forbidInWorkTime: /* Integer */ string;
      /** 工作时间内优先降级级数是否生效 */
      forbidInWorkTimeEnable: boolean;
      /** 非工作时间内优先降级级数 */
      degradeOutWorkTime: /* Integer */ string;
      /** 非工作时间内优先降级级数是否生效 */
      degradeOutWorkTimeEnable: boolean;
      /** 非工作时间禁止下降的优先级(在这个范围内的优先级不降级) */
      forbidOutWorkTime: /* Integer */ string;
      /** 非工作时间禁止下降的优先级是否生效 */
      forbidOutWorkTimeEnable: boolean;
      /** 配置时间标识(WORKDAY-工作日、SATURDAY-周六、SUNDAY-周日) */
      weekType: /* 枚举: WORKDAY :工作日 | SATURDAY :周六 | SUNDAY :周日 */ "WORKDAY" | "SATURDAY" | "SUNDAY";
    }[];
  };
}

export function getTicketDegrade(data: {} & RequestBase) {
  return request<unknown, Response<TicketDegrade>>({
    url: `${SERVER.EVENT_CENTER}/ticket_degrade/find`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function setTicketDegrade(data: {} & RequestBase) {
  return request<unknown, Response<TicketDegrade>>({
    url: `${SERVER.EVENT_CENTER}/ticket_degrade/setup`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data,
  });
}

export function getOrderGroupFindConfiguration(data: {} & RequestBase) {
  return request<unknown, Response<string>>({
    url: `${SERVER.EVENT_CENTER}/user_group_configuration/findconfiguration`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function getCloseDirectly(data: {} & RequestBase) {
  return request<unknown, Response<boolean>>({
    url: `${SERVER.EVENT_CENTER}/ticker_group/findconfiguration`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function getOrderUserGroup(data: {} & RequestBase) {
  return request<unknown, Response<UserGroupConfigurationItem[]>>({
    url: `${SERVER.EVENT_CENTER}/user_group_configuration/findByticketGroup`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function getOrderUserGroupIsClose(data: {} & RequestBase) {
  return request<unknown, Response<boolean>>({
    url: `${SERVER.EVENT_CENTER}/ticker_group/findconfigurationAndClose`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function getOrderApproveUserGroup(data: {} & RequestBase) {
  return request<unknown, Response<UserGroupConfigurationItem[]>>({
    url: `${SERVER.EVENT_CENTER}/user_group_configuration/findByticketGroupAndticketType`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export interface TicketClassificationItem {
  id: /* Integer */ string;
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime?: /* Integer */ string;
  /** 更新时间 */
  updatedTime?: /* Integer */ string;
  /** 创建人信息 */
  createdBy?: string;
  /** 更新人信息 */
  updatedBy?: string;
  /** 工单组id */
  ticketGroupId: /* Integer */ string;
  /** 父类id */
  parentId: /* Integer */ string;
  /** 工单分类名称 */
  classificationName: string;
  /** 描述 */
  description?: string;
  /** 性质 */
  quality?: string;
  /** 分类类型 */
  type: string;
  /** 是否激活 */
  active: boolean;
  /** 已有工单 */
  orderByuse?: boolean;
}

export function addTicketClassification(data: {} & RequestBase) {
  return request<unknown, Response<TicketClassificationItem>>({
    url: `${SERVER.EVENT_CENTER}/ticket_classification/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data,
  });
}

export function getTicketClassification(data: {} & RequestBase) {
  return request<unknown, Response<TicketClassificationItem[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_classification/queryByType`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function setTicketClassification(data: {} & RequestBase) {
  return request<unknown, Response<TicketClassificationItem[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_classification/update`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data,
  });
}

export function delTicketClassification(data: {} & RequestBase) {
  return request<unknown, Response<TicketClassificationItem[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_classification/delete`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

/**
 * @description 创建工单时获取当前客户的工单分类
 */
export function getTicketClassificationByTenant(data: {} & RequestBase) {
  return request<unknown, Response<TicketClassificationItem[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_classification/queryByTypeList`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

/**
 * @description 判断当前用户B是否可以审批其他用户A的工单
 * @link http://*************:3000/project/17/interface/api/50973
 * @param data
 * @returns
 */
export function getOrderApprovalPermission(data: {} & RequestBase) {
  return request<unknown, Response<boolean>>({
    url: `${SERVER.EVENT_CENTER}/user_group_configuration/findOtherUserApproval`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export async function getTicketClassificationNames(type, id, ticketTemplateId) {
  if (!id || !ticketTemplateId) return [];
  const { data: ticketClassificationData, message, success } = await getTicketClassificationByTenant({ type, ticketTemplateId });
  if (!success) throw new Error(message);
  if (!ticketClassificationData.map((v) => v.id).includes(id)) return [];
  const names: string[] = [];
  function setName(id, data, names) {
    const queryRow = data.find((v) => v.id === id);
    if (!queryRow) return names;
    names.unshift(queryRow.classificationName);
    queryRow.parentId && queryRow.parentId !== "-1" ? setName(queryRow.parentId, data, names) : void 0;
    return names;
  }
  return setName(id, ticketClassificationData, names);
}
