import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";

export const timeOptions = [
  {
    label: "24小时",
    value: "TODAY",
  },
  {
    label: "近7天",
    value: "WEEK",
  },
  // {
  //   label: "近2周",
  //   value: "MONTH",
  // },
  {
    label: "近一个月",
    value: "MONTH",
  },
  {
    label: "近3个月",
    value: "THREE_MONTH",
  },
  {
    label: "近半年",
    value: "SIX_MONTH",
  },
  {
    label: "全部",
    value: "All",
  },
];

export async function getOrderSituation(req: {} & RequestBase) {
  const userInfo = (await import("@/utils/getUserInfo")).default();
  const { 智能事件中心_客户_工单可读, 智能事件中心_二维码报障_可读, 智能事件中心_项目_工单可读, 智能事件中心_联系人_工单可读, 智能事件中心_设备_工单可读 } = await import("@/views/pages/permission");
  const data = Object.assign(
    {
      customerTicketRead: userInfo.hasPermission(智能事件中心_客户_工单可读),
      qrRead: userInfo.hasPermission(智能事件中心_二维码报障_可读),
      permissionList: [
        /*  */
        { permissionId: 智能事件中心_项目_工单可读, permissionType: "View Tickets When Project Has View Tickets" },
        { permissionId: 智能事件中心_联系人_工单可读, permissionType: "View Tickets When Contact Has View Tickets" },
        { permissionId: 智能事件中心_设备_工单可读, permissionType: "View Tickets When Device Has View Tickets" },
      ],
    },
    req
  );

  return request<never, Response<Record<string, any>>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/order/homeList`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export async function getAlarmSituation(req: {} & RequestBase) {
  const { 资产管理中心_设备_可读 } = await import("@/views/pages/permission");

  const params = Object.assign(
    {
      permissionId: 资产管理中心_设备_可读,
    },
    req
  );

  return request<never, Response<Record<string, any>>>({
    url: `${SERVER.EVENT_CENTER}/alert/homeCount`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data: {},
  });
}

export async function getOrderCloseReasons(req: {} & RequestBase) {
  // const { 服务管理中心_关闭代码配置_可读 } = await import("@/views/pages/permission");

  const userInfo = (await import("@/utils/getUserInfo")).default();
  const { 智能事件中心_客户_工单可读, 智能事件中心_二维码报障_可读, 智能事件中心_项目_工单可读, 智能事件中心_联系人_工单可读, 智能事件中心_设备_工单可读 } = await import("@/views/pages/permission");
  const data = Object.assign(
    {
      customerTicketRead: userInfo.hasPermission(智能事件中心_客户_工单可读),
      qrRead: userInfo.hasPermission(智能事件中心_二维码报障_可读),
      permissionList: [
        /*  */
        { permissionId: 智能事件中心_项目_工单可读, permissionType: "View Tickets When Project Has View Tickets" },
        { permissionId: 智能事件中心_联系人_工单可读, permissionType: "View Tickets When Contact Has View Tickets" },
        { permissionId: 智能事件中心_设备_工单可读, permissionType: "View Tickets When Device Has View Tickets" },
      ],
    },
    req
  );

  const params = {};

  return request<never, Response<Record<string, any>>>({
    url: `${SERVER.EVENT_CENTER}/home_page/order/homeCodeCount`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}

export async function getRegion(req: {} & RequestBase) {
  const { 资产管理中心_区域_可读 } = await import("@/views/pages/permission");
  const data = {};

  const params = Object.assign(
    {
      permissionId: 资产管理中心_区域_可读,
    },
    req
  );

  return request<never, Response<Record<string, any>>>({
    url: `${SERVER.EVENT_CENTER}/home_page/orerder/homeRegion`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}

// https://172.31.26.203:20082/gw_proxy/ops/event_center/home_page/order/homeType?permissionId=509623239069138944&regionId&allGlobal=true&homeTime=All&typePermissionId=512862869692350464
export async function getDeviceType(req: {} & RequestBase) {
  const { 资产管理中心_设备类型_可读, 资产管理中心_设备_可读 } = await import("@/views/pages/permission");
  const data = {};

  const params = Object.assign(
    {
      typePermissionId: 资产管理中心_设备类型_可读,
      permissionId: 资产管理中心_设备_可读,
    },
    req
  );

  return request<never, Response<Record<string, any>>>({
    url: `${SERVER.EVENT_CENTER}/home_page/order/homeType`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}

export async function getLineType(req: {} & RequestBase) {
  const { 资产管理中心_设备_可读 } = await import("@/views/pages/permission");
  const data = Object.assign(
    {
      permissionId: 资产管理中心_设备_可读,
    },
    req
  );

  const params = {};

  return request<never, Response<Record<string, any>>>({
    url: `${SERVER.EVENT_CENTER}/home_page/order/homeDevice`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}

export async function getTop5CoreIndicators(req: {} & RequestBase) {
  const { 资产管理中心_设备_可读 } = await import("@/views/pages/permission");
  const data = {};

  const params = Object.assign(
    {
      permissionId: 资产管理中心_设备_可读,
    },
    req
  );

  return request<never, Response<Record<string, any>>>({
    url: `${SERVER.EVENT_CENTER}/home_page/order/homeTop`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}
