import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";

import { 安全管理中心_密码钱包_可读密码凭证 } from "@/views/pages/permission";
import { active } from "sortablejs";

export interface AddReqBody {
  /** 钱包名称 */
  name: string;
  /** 描述 */
  description?: string;
  /** 容器ID */
  containerIds: /* Integer */ string[];
}

export function addPasswordWallet(data: {} & AddReqBody & RequestBase) {
  return request<never, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/cryptow_wallets/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export interface SetReqBody {
  /** 主键 */
  id: /* Integer */ string;
  /** 名称 */
  name: string;
  /** 所属租户 */
  tenantId: /* Integer */ string;
  /** 描述信息 */
  description?: string;
  /** 可读权限时间(分钟) */
  readTime: /* Integer */ string;
  /** 设置权限时间(分钟) */
  setTime: /* Integer */ string;
  /** 是否激活(状态：true-启用，false-禁用) */
  enable: boolean;
}

export function setPasswordWallet(data: {} & Partial<SetReqBody> & RequestBase) {
  return request<never, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/cryptow_wallets/update`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function delPasswordWallet(data: {} & Partial<SetReqBody> & RequestBase) {
  return request<never, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/cryptow_wallets/${data.id}/delete`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export interface PasswordWalletItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 所属租户 */
  tenantId: /* Integer */ string;
  /** 安全容器id */
  containerIds?: /* Integer */ string[];
  /** 名称 */
  name: string;
  /** 描述信息 */
  description?: string;
  /** 可读权限时间(分钟) */
  readTime: /* Integer */ string;
  /** 设置权限时间(分钟) */
  setTime: /* Integer */ string;
  /** 是否激活(状态：true-启用，false-禁用) */
  enable: boolean;
}

export async function getPasswordWallet(data: {} & RequestBase) {
  const userInfo = (await import("@/utils/getUserInfo")).default();
  const { 安全管理中心_密码钱包_可读, 安全管理中心_密码钱包_安全, 安全管理中心_密码钱包_新增, 安全管理中心_密码钱包_编辑, 安全管理中心_密码钱包_删除, 安全管理中心_密码钱包_查看日志 } = await import("@/views/pages/permission");
  return request<never, Response<PasswordWalletItem[]>>({
    url: `${SERVER.EVENT_CENTER}/cryptow_wallets/query`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { pageNumber: data.pageNumber, pageSize: data.pageSize },
    data: {
      active: data.active,
      containerId: data.containerId || (userInfo.currentTenant || {}).containerId,
      queryPermissionId: data.queryPermissionId || [安全管理中心_密码钱包_可读].join(),
      verifyPermissionIds: data.verifyPermissionIds || [安全管理中心_密码钱包_安全, 安全管理中心_密码钱包_新增, 安全管理中心_密码钱包_编辑, 安全管理中心_密码钱包_删除, 安全管理中心_密码钱包_查看日志].join(),
      ...([...(data.inName instanceof Array ? data.inName : []), ...(data.excludeName instanceof Array ? data.excludeName : []), ...(data.eqName instanceof Array ? data.eqName : []), ...(data.neName instanceof Array ? data.neName : [])].filter((v) => v).length ? { nameFilterRelation: data.nameFilterRelation === "OR" ? "OR" : "AND", inName: data.inName instanceof Array && data.inName.length ? data.inName.join(",") : void 0, excludeName: data.excludeName instanceof Array && data.excludeName.length ? data.excludeName.join(",") : void 0, eqName: data.eqName instanceof Array && data.eqName.length ? data.eqName.join(",") : void 0, neName: data.neName instanceof Array && data.neName.length ? data.neName.join(",") : void 0 } : {}),
      ...([...(data.inDesc instanceof Array ? data.inDesc : []), ...(data.excludeDesc instanceof Array ? data.excludeDesc : []), ...(data.eqDesc instanceof Array ? data.eqDesc : []), ...(data.neDesc instanceof Array ? data.neDesc : [])].filter((v) => v).length ? { descFilterRelation: data.descFilterRelation === "OR" ? "OR" : "AND", inDesc: data.inDesc instanceof Array && data.inDesc.length ? data.inDesc.join(",") : void 0, excludeDesc: data.excludeDesc instanceof Array && data.excludeDesc.length ? data.excludeDesc.join(",") : void 0, eqDesc: data.eqDesc instanceof Array && data.eqDesc.length ? data.eqDesc.join(",") : void 0, neDesc: data.neDesc instanceof Array && data.neDesc.length ? data.neDesc.join(",") : void 0 } : {}),
      ...([...(data.eqReadTime instanceof Array ? data.eqReadTime : []), ...(data.neReadTime instanceof Array ? data.neReadTime : [])].filter((v) => v).length ? { readTimeFilterRelation: data.readTimeFilterRelation === "OR" ? "OR" : "AND", eqReadTime: data.eqReadTime instanceof Array && data.eqReadTime.length ? data.eqReadTime.join(",") : void 0, neReadTime: data.neReadTime instanceof Array && data.neReadTime.length ? data.neReadTime.join(",") : void 0 } : {}),
      ...([...(data.eqSetTime instanceof Array ? data.eqSetTime : []), ...(data.neSetTime instanceof Array ? data.neSetTime : [])].filter((v) => v).length ? { setTimeFilterRelation: data.setTimeFilterRelation === "OR" ? "OR" : "AND", eqSetTime: data.eqSetTime instanceof Array && data.eqSetTime.length ? data.eqSetTime.join(",") : void 0, neSetTime: data.neSetTime instanceof Array && data.neSetTime.length ? data.neSetTime.join(",") : void 0 } : {}),
    },
  });
}

export function addLoginCredentials(data: {} & RequestBase) {
  return request<never, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/login_credentials/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function setLoginCredentials(data: {} & RequestBase) {
  return request<never, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/login_credentials/update`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function delLoginCredentials(data: {} & RequestBase) {
  return request<never, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/login_credentials/delete`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function getAccountPassword(data: { containerId: string } & RequestBase) {
  return request<never, Response<LoginCredentialsItem>>({
    url: `${SERVER.EVENT_CENTER}/login_credentials/queryPassWord`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: { "x-permission-id": 安全管理中心_密码钱包_可读密码凭证, "x-container-id": data.containerId },
    params: data,
    data: {},
  });
}

export interface LoginCredentialsItem {
  id?: /* Integer */ string;
  /** 钱包ID */
  parentId?: /* Integer */ string;
  /** 名称 */
  name: string;
  /** 描述 */
  description?: string;
  /** connectors */
  connectors: string[];
  /** 用户名 */
  userName: string;
  /** 密码 */
  passWord: string;
  /** 生效开始时间 */
  startTime: string;
  /** 生效结束时间 */
  endTime: string;
}

export function getLoginCredentials(data: {} & RequestBase) {
  return request<never, Response<LoginCredentialsItem[]>>({
    url: `${SERVER.EVENT_CENTER}/login_credentials/query`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export interface ResourceItem {
  /** 设备ID */
  id: /* Integer */ string;
  /** 设备名称 */
  name?: string;
  /** 场所 */
  locationDesc?: string;
  tags?: string;
  ipAddress?: string;
}

export function getResourceNotContain(data: {} & RequestBase) {
  return request<never, Response<ResourceItem[]>>({
    url: `${SERVER.EVENT_CENTER}/equipment/resourceNotContain`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export interface EquipmentQueryItem {
  id?: /* Integer */ string;
  /** 钱包ID */
  parentId: /* Integer */ string;
  /** 设备名称 */
  name: string;
  /** 设备ip */
  ipAddress?: string;
  /** 描述 */
  tags?: string;
  /** 场所 */
  locationDesc?: string;
}

export function getEquipmentQuery(data: {} & RequestBase) {
  return request<never, Response<EquipmentQueryItem[]>>({
    url: `${SERVER.EVENT_CENTER}/equipment/query`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function addEquipment(data: {} & RequestBase, deviceId) {
  return request<never, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/equipment/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: deviceId,
  });
}

export function delEquipment(data: {} & RequestBase) {
  return request<never, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/equipment/delete`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function addLoginCredentialsGroup(data: {} & RequestBase) {
  return request<never, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/loginCredentialsGroup/create`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function setLoginCredentialsGroup(data: {} & RequestBase) {
  return request<never, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/loginCredentialsGroup/update`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function delLoginCredentialsGroup(data: {} & RequestBase) {
  return request<never, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/loginCredentialsGroup/delete`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export interface LoginCredentialsGroupItem {
  id?: /* Integer */ string;
  /** 钱包ID */
  parentId: /* Integer */ string;
  /** 登录凭证组名称 */
  groupName: string;
}

export function getLoginCredentialsGroup(data: {} & RequestBase) {
  return request<never, Response<LoginCredentialsGroupItem[]>>({
    url: `${SERVER.EVENT_CENTER}/loginCredentialsGroup/query`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export interface PasswordWalletsLogItem {
  id?: /* Integer */ string;
  /** 钱包ID */
  parentId: /* Integer */ string;
  /** 用户名称 */
  userName: string;
  /** 操作 */
  operate: string;
  /** 登录凭证名称 */
  loginCredentialsName: string;
  /** 设备 */
  equipment?: string;
  /** ip时间 */
  ipTime: string;
  /** ip地址 */
  ipAddress?: string;
}

export function getPasswordWalletsLog(data: {} & RequestBase) {
  return request<never, Response<PasswordWalletsLogItem[]>>({
    url: `${SERVER.EVENT_CENTER}/cryptow_wallets_log/queryPage`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function getLoginCredentialQueryAllLognins(data: {} & RequestBase) {
  return request<never, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/login_credentials/queryAllLognin`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function verifyPwd(data: {} & RequestBase) {
  return request<never, Response<Record<string, any>[]>>({
    url: `${SERVER.IAM}/current_user/verify/password`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["secretCode"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function verifyCode(data: {} & RequestBase) {
  return request<never, Response<Record<string, any>[]>>({
    url: `${SERVER.IAM}/current_user/verify/code`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["userId", "code"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
