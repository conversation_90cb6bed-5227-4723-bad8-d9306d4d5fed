import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";

export interface ProjectItem {
  id: /* Integer */ string;
  /** 项目名称 */
  projectName?: string;
  /** 项目编号 */
  projectCode?: string;
  /** 统一服务编码 */
  uniformServiceCode?: string;
  /** 租户id */
  tenantId: /* Integer */ string;
  /** 租户缩写 */
  abbreviation?: string;
  /** 项目级别 */
  projectLevel?: string;
  /** 项目有效期 */
  range?: {
    /** 起始 */
    start?: /* Integer */ string;
    /** 结束 */
    end?: /* Integer */ string;
  };
  /** SLA规则ID */
  slaId?: /* Integer */ string;
  /** SLA规则名称 */
  slaName?: string;
  /** 是否激活 */
  active?: boolean;
  /** 设备id列表 */
  deviceIds: /* Integer */ string[];
}

export function getAllProjects(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/event/getAllProjects`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function getAllUniformServiceCodes(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/event/getAllUniformServiceCodes`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function getProjectsByEventId(data: {} & RequestBase) {
  return request<unknown, Response<ProjectItem[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/prod/getProjects`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { id: data.id },
    data: {},
  });
}

export function replaceProjectByEventId(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    // url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/replace/project`,
    url: `${SERVER.EVENT_CENTER}/event/replaceProject`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    // data: ["projectId", "projectName", "projectCode", "uniformServiceCode", "projectLevel", "range"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["eventId", "projectId", "orderType"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function removeProjectByEventId(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    // url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/replace/project`,
    url: `${SERVER.EVENT_CENTER}/event/removeProject`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    // data: ["projectId", "projectName", "projectCode", "uniformServiceCode", "projectLevel", "range"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["eventId", "projectId", "orderType"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function replaceProjectByServiceRequestId(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    // url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.id}/replace/project`,
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.id}/replace/${data.projectId}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    // data: ["projectId", "projectName", "projectCode", "uniformServiceCode", "projectLevel", "range"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function getDictOrderProject(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/${data.id}/prod/projectDetail`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function getProjects(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/prod/getProjects`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
