import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";

export interface AddReqBody {
  projectName: string;
  projectCode: string;
  containerId: string;
  uniformServiceCode: string;
  active: boolean;
  tenantId?: string;
}

export interface SetReqBody {
  id: string;
  projectName: string;
  projectCode: string;
  containerId: string;
  uniformServiceCode: string;
  active: boolean;
  tenantId?: string;
  range?: object;
  slaId?: string;
  slaName?: string;
  projectLevel?: string;
}

/**
 * 获取所有项目计划列表
 */
export async function getAllProjectPlans(data: {} & RequestBase) {
  const userInfo = (await import("@/utils/getUserInfo")).default();
  const { 配置管理中心_项目管理_可读, 配置管理中心_项目管理_安全, 配置管理中心_项目管理_新增, 配置管理中心_项目管理_编辑, 配置管理中心_项目管理_删除 } = await import("@/views/pages/permission");
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/prod/getAllProjects`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {
      containerId: data.containerId || (userInfo.currentTenant || {}).containerId,
      queryPermissionId: data.queryPermissionId || [配置管理中心_项目管理_可读].join(),
      active: data.active,
      verifyPermissionIds: data.verifyPermissionIds || [配置管理中心_项目管理_安全, 配置管理中心_项目管理_新增, 配置管理中心_项目管理_编辑, 配置管理中心_项目管理_删除].join(),
      ...([...(data.includeProjectName instanceof Array ? data.includeProjectName : []), ...(data.excludeProjectName instanceof Array ? data.excludeProjectName : []), ...(data.eqProjectName instanceof Array ? data.eqProjectName : []), ...(data.neProjectName instanceof Array ? data.neProjectName : [])].filter((v) => v).length ? { projectNameFilterRelation: data.projectNameFilterRelation === "OR" ? "OR" : "AND", includeProjectName: data.includeProjectName instanceof Array && data.includeProjectName.length ? data.includeProjectName.join(",") : void 0, excludeProjectName: data.excludeProjectName instanceof Array && data.excludeProjectName.length ? data.excludeProjectName.join(",") : void 0, eqProjectName: data.eqProjectName instanceof Array && data.eqProjectName.length ? data.eqProjectName.join(",") : void 0, neProjectName: data.neProjectName instanceof Array && data.neProjectName.length ? data.neProjectName.join(",") : void 0 } : {}),
      ...([...(data.includeProjectCode instanceof Array ? data.includeProjectCode : []), ...(data.excludeProjectCode instanceof Array ? data.excludeProjectCode : []), ...(data.eqProjectCode instanceof Array ? data.eqProjectCode : []), ...(data.neProjectCode instanceof Array ? data.neProjectCode : [])].filter((v) => v).length ? { projectCodeFilterRelation: data.projectCodeFilterRelation === "OR" ? "OR" : "AND", includeProjectCode: data.includeProjectCode instanceof Array && data.includeProjectCode.length ? data.includeProjectCode.join(",") : void 0, excludeProjectCode: data.excludeProjectCode instanceof Array && data.excludeProjectCode.length ? data.excludeProjectCode.join(",") : void 0, eqProjectCode: data.eqProjectCode instanceof Array && data.eqProjectCode.length ? data.eqProjectCode.join(",") : void 0, neProjectCode: data.neProjectCode instanceof Array && data.neProjectCode.length ? data.neProjectCode.join(",") : void 0 } : {}),
      ...([...(data.includeUniformServiceCode instanceof Array ? data.includeUniformServiceCode : []), ...(data.excludeUniformServiceCode instanceof Array ? data.excludeUniformServiceCode : []), ...(data.eqUniformServiceCode instanceof Array ? data.eqUniformServiceCode : []), ...(data.neUniformServiceCode instanceof Array ? data.neUniformServiceCode : [])].filter((v) => v).length ? { uniformServiceCodeFilterRelation: data.uniformServiceCodeFilterRelation === "OR" ? "OR" : "AND", includeUniformServiceCode: data.includeUniformServiceCode instanceof Array && data.includeUniformServiceCode.length ? data.includeUniformServiceCode.join(",") : void 0, excludeUniformServiceCode: data.excludeUniformServiceCode instanceof Array && data.excludeUniformServiceCode.length ? data.excludeUniformServiceCode.join(",") : void 0, eqUniformServiceCode: data.eqUniformServiceCode instanceof Array && data.eqUniformServiceCode.length ? data.eqUniformServiceCode.join(",") : void 0, neUniformServiceCode: data.neUniformServiceCode instanceof Array && data.neUniformServiceCode.length ? data.neUniformServiceCode.join(",") : void 0 } : {}),
      ...([...(data.eqProjectLevel instanceof Array ? data.eqProjectLevel : []), ...(data.neProjectLevel instanceof Array ? data.neProjectLevel : [])].filter((v) => v).length ? { projectLevelFilterRelation: data.projectLevelFilterRelation === "OR" ? "OR" : "AND", eqProjectLevel: data.eqProjectLevel instanceof Array && data.eqProjectLevel.length ? data.eqProjectLevel.join(",") : void 0, neProjectLevel: data.neProjectLevel instanceof Array && data.neProjectLevel.length ? data.neProjectLevel.join(",") : void 0 } : {}),
      ...([...(data.eqDuration instanceof Array ? data.eqDuration : []), ...(data.neDuration instanceof Array ? data.neDuration : [])].filter((v) => v).length ? { durationFilterRelation: data.durationFilterRelation === "OR" ? "OR" : "AND", eqDuration: data.eqDuration instanceof Array && data.eqDuration.length ? data.eqDuration.join(",") : void 0, neDuration: data.neDuration instanceof Array && data.neDuration.length ? data.neDuration.join(",") : void 0 } : {}),
      ...([...(data.includeSlaName instanceof Array ? data.includeSlaName : []), ...(data.excludeSlaName instanceof Array ? data.excludeSlaName : []), ...(data.eqSlaName instanceof Array ? data.eqSlaName : []), ...(data.neSlaName instanceof Array ? data.neSlaName : [])].filter((v) => v).length ? { slaNameFilterRelation: data.slaNameFilterRelation === "OR" ? "OR" : "AND", includeSlaName: data.includeSlaName instanceof Array && data.includeSlaName.length ? data.includeSlaName.join(",") : void 0, excludeSlaName: data.excludeSlaName instanceof Array && data.excludeSlaName.length ? data.excludeSlaName.join(",") : void 0, eqSlaName: data.eqSlaName instanceof Array && data.eqSlaName.length ? data.eqSlaName.join(",") : void 0, neSlaName: data.neSlaName instanceof Array && data.neSlaName.length ? data.neSlaName.join(",") : void 0 } : {}),
    },
  });
}
/**
 * 新增项目计划
 */
export function addAllProjectPlans(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/prod/createProject`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
/**
 * 编辑项目计划
 */
export function setAllProjectPlans(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/prod/updateProject`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
/**
 * 删除项目计划
 */
export function delAllProjectPlans(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/prod/deleteProject?prodId=${data.prodId}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * 项目添加设备
 */
export function ProjectaddProdDevice(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/prod/${data.id}/addProdDevice/${data.deviceIds}/${data.deviceNames}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
/**
 * 项目移除设备
 */
export function ProjectdelProdDevice(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_event/prod/${data.id}/removeProdDevice/${data.deviceId}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
