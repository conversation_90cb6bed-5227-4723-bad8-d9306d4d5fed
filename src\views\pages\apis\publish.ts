import { SERVER, Method, type Response, type RequestBase, bindSearchParams, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

import { priority, priorityOption } from "./eventPriority";
export { priority, priorityOption };
import { eventSeverityOption, eventSeverity } from "./event";
export { eventSeverityOption, eventSeverity };
import { deviceImportance, deviceImportanceOption } from "./device";
export { deviceImportance, deviceImportanceOption };

import { 智能事件中心_发布管理_创建审批, 智能事件中心_发布管理_审批 } from "@/views/pages/permission";

import { i18n } from "@/lang/index";

const { t } = i18n.global;

/**
 * 基本分页字段
 */
interface PageFilter {
  paging: { pageNumber: number; pageSize: number };
  sort: string[];
}
/**
 * 基本时间字段
 */
interface DateRangeFilter {
  /** 创建时间起始时间 */
  createTimeStart: string;
  /** 创建时间结束时间 */
  createTimeEnd: string;
  /** 修改时间起始时间 */
  updateTimeStart: string;
  /** 修改时间结束时间 */
  updateTimeEnd: string;
  /** 紧凑模式下时间筛选-起始时间 */
  compactTimeStart: string;
  /** 紧凑模式下时间筛选-结束时间 */
  compactTimeEnd: string;
}
/**
 * 类型校验合并
 */
type Merge<T1 extends object, T2 extends object, MT = T1 & T2> = { [MK in keyof MT]: MT[MK] };
/**
 * 条件过滤器方法
 */
type ConditionFilter<N extends string, TT extends string, F extends string, TE extends string, OT = { [P in N extends `${infer F}${infer E}` ? `${TT}${Uppercase<F>}${E}` : never]: string[] } & { [P in F extends `${infer F}${infer E}` ? `${N}${Uppercase<F>}${E}` : never]: TE }> = { [P in keyof OT]: OT[P] };
// /**
//  * 类型校对方法
//  */
// type HasConditionFilter<Diff0 extends object, Diff1 extends object> = Merge<Record<keyof Omit<Diff0, keyof Diff1>, 0>, Record<keyof Omit<Diff1, keyof Diff0>, 1>>;

export enum publishState {
  PROCESSING = "PROCESSING",
  AUTO_CLOSED = "AUTO_CLOSED",
  CLOSED = "CLOSED",
  NEW = "NEW",
}

export enum publishType {
  ORDINARY = "ORDINARY",
  STANDARD = "STANDARD",
  URGENCY = "URGENCY",
  IMPORTANT = "IMPORTANT",
}

export enum publishOperate {
  DISPOSE = "DISPOSE",
  TRANSFER = "TRANSFER",
  WITHDRAW = "WITHDRAW",
  COMPLETE = "COMPLETE",
  CLOSE = "CLOSE",
}

export const publishOperateOption: { label: string; value: keyof typeof publishOperate }[] = [
  { label: "关闭", value: publishOperate.CLOSE },
  { label: "撤回", value: publishOperate.WITHDRAW },
  { label: "完成", value: publishOperate.COMPLETE },
  { label: "处理", value: publishOperate.DISPOSE },
  { label: "转交", value: publishOperate.TRANSFER },
];

export const publishStateOption: { label: string; value: keyof typeof publishState; color?: string; type?: undefined | "success" | "warning" | "info" | "danger" }[] = [
  { label: t("event.新建"), value: publishState.NEW, color: "#ED4013", type: "danger" },
  { label: t("event.处理中"), value: publishState.PROCESSING, color: "#2CB6F4", type: "success" },
  { label: t("event.自动关闭"), value: publishState.AUTO_CLOSED, color: "#3EBE6B", type: void 0 },
  { label: t("event.关闭"), value: publishState.CLOSED, color: "#3EBE6B", type: void 0 },
];
export const publishTypeOption: { label: string; value: keyof typeof publishType }[] = [
  { label: "一般发布", value: publishType.ORDINARY },
  { label: "标准发布", value: publishType.STANDARD },
  { label: "紧急发布", value: publishType.URGENCY },
  { label: "重大发布", value: publishType.IMPORTANT },
];

export interface publishItem {
  teamId: string;
  id: string /* 主键Id */;
  tenantId: string /* 租户id */;
  identifier: string /* 工单编号 */;
  publishType: publishType /* 类型 枚举类型: ORDINARY :一般 | STANDARD :标准 | URGENCY :紧急 | IMPORTANT :重大 */;
  digest: string /* 摘要 */;
  priority: priority /* 优先级 枚举类型: P1 :P1~P7 优先级递减 | P2 :P2 | P3 :P3 | P4 :P4 | P5 :P5 | P6 :P6 | P7 :P7 | P8 :P8 手动 */;
  publishState: publishState /* 状态 枚举类型: NEW :新建 | PROCESSING :处理中 | AUTO_CLOSED :自动关闭 | CLOSED :关闭 */;
  approveState: "UN_APPROVE" | "APPROVED" /* 审批状态 枚举类型: UN_APPROVE :未审批 | APPROVED :已审批 */;
  operation: publishOperate /* 操作 枚举类型: DISPOSE :处理 | TRANSFER :转交 | WITHDRAW :撤回 | COMPLETE :完成 | CLOSE :关闭 */;
  responsibleId?: string /* 负责人Id */;
  responsibleName?: string /* 负责人名称 */;
  userGroupId?: string /* 用户组ID */;
  userGroupName?: string /* 用户组名称 */;
  userId?: string /* 用户ID */;
  userName?: string /* 用户名称 */;
  actorId?: string /* 处理人Id */;
  actorName?: string /* 处理人名称 */;
  displayUserGroupId?: string /* 回显展示用户组id */;
  importance?: deviceImportance /* 重要性 枚举类型: HIGH :高 | MIDDLE :中 | LOW :低 | NORMAL :无 | UNKNOWN :未知 */;
  severity?: eventSeverity /* 紧急性 枚举类型: Critical :Critical | Major :Major | Minor :Minor | Warning :Warning | Unknown :Unknown | Normal :Normal | Informational :Informational | Calculating :Calculating | Symptom :Symptom | Monitoring :Monitoring | Others :在映射中展示Critical -- Monitoring, */;
  startTime?: string /* 开始时间 */;
  endTime?: string /* 结束时间 */;
  desc: string /* 描述 */;
  externalId: string /* 外部id */;
  contacts: /* 联系人列表 */ {
    contactId: string /* 联系人id */;
    contactType: string /* 联系人类型 枚举类型: Notification :通知联系人 | Technical :技术联系人 | OnSite :现场联系人 */;
  }[];
  deviceIds: /* 设备id列表 */ string[];
  completeInfo?: /* 完成代码 */ {
    finishCodeName?: string /* 完成代码名称 */;
    finishCodeDesc?: string /* 完成代码描述 */;
    finishContent?: string /* 完成内容 */;
  };
  autoClose?: boolean /* 自动关闭 */;
  autoCloseTime: string /* 自动关闭时间 */;
  createdBy?: string /* 创建人信息 */;
  updatedBy?: string /* 更新人信息 */;
  createdTime?: string /* 创建时间 */;
  updatedTime?: string /* 更新时间 */;

  rejectAble?: boolean;
  draft: boolean;

  verifyPermissionIds: string[];
}

export function publishCreate(data: { name: string; description: string; priority: keyof typeof priority } & RequestBase) {
  return request<unknown, Response<publishItem>>({
    url: `${SERVER.EVENT_CENTER}/publish/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function setPublishItem /* 统计模块 */(data: { id?: string[] } & RequestBase) {
  return request<unknown, Response<publishItem>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/submit/${data.type}`,
    method: Method.Patch,
    // responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: {},
  });
}

// export function getPublishList(data: { pageNumber: number; pageSize: number } & RequestBase) {
//   const params = new URLSearchParams({ pageNumber: String(data.pageNumber || 0), pageSize: String(data.pageSize || 0) });
//   (data.sort instanceof Array ? data.sort : []).forEach((v) => params.append("sort", v));
//   bindSearchParams({ identifier: data.identifier, publishState: data.publishState, digest: data.digest, priority: data.priority, tenantId: data.tenantId, responsibleName: data.responsibleName, actorName: data.actorName, permissionId: "612917583569485824" }, params);
//   bindSearchParams({ createTimeStart: ((data.createTime as Record<string, string>) || {}).start, createTimeEnd: ((data.createTime as Record<string, string>) || {}).end }, params);
//   bindSearchParams({ updateTimeStart: ((data.updateTime as Record<string, string>) || {}).start, updateTimeEnd: ((data.updateTime as Record<string, string>) || {}).end }, params);

//   return request<unknown, Response<publishItem[]>>({
//     url: `${SERVER.EVENT_CENTER}/publish/query`,
//     method: Method.Get,
//     responseType: "json",
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     params,
//     data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
//   });
// }

/**
 * @description 发布
 */
export interface PublishItem {
  /** 主键Id */
  id: /* Integer */ string;
  /** 租户id */
  tenantId: /* Integer */ string;
  /** 审批是否拒绝(如果审批拒绝则为true，默认为false) */
  rejectAble?: boolean;
  /** 租户名称 */
  tenantName?: string;
  /** 工单编号 */
  identifier: string;
  /** 工单类型 */
  orderType: /* 枚举: EVENT_ORDER :事件单 | SERVICE_REQUEST :服务请求 | CHANGE :变更 | QUESTION :问题 | PUBLISH :发布 */ import("./association").OrderType.PUBLISH;
  /** 变更 Id */
  changeId: /* Integer */ string;
  /** 类型 */
  publishType: /* 枚举: ORDINARY :一般 | STANDARD :标准 | URGENCY :紧急 | IMPORTANT :重大 */ publishType;
  /** 摘要 */
  digest: string;
  /** 优先级 */
  priority: /* 枚举: P1 :P1~P7 优先级递减 | P2 :P2 | P3 :P3 | P4 :P4 | P5 :P5 | P6 :P6 | P7 :P7 | P8 :P8 手动 */ priority;
  /** 状态 */
  publishState: /* 枚举: NEW :新建 | PROCESSING :处理中 | AUTO_CLOSED :自动关闭 | CLOSED :关闭 */ publishState;
  /** 审批状态 */
  approveState: /* 枚举: UN_APPROVE :未审批 | APPROVED :已审批 */ "UN_APPROVE" | "APPROVED";
  /** 操作 */
  operation?: /* 枚举: DISPOSE :处理 | TRANSFER :转交 | WITHDRAW :撤回 | COMPLETE :完成 | CLOSE :关闭 */ publishOperate;
  /** 负责人Id */
  responsibleId?: /* Integer */ string;
  /** 负责人名称 */
  responsibleName?: string;
  /** 用户组ID */
  userGroupId?: /* Integer */ string;
  /** 用户组名称 */
  userGroupName?: string;
  /** 处理人Id */
  actorId?: /* Integer */ string;
  /** 处理人名称 */
  actorName?: string;
  /** 告警数量 */
  alarmNumber: string;
  /** 回显展示用户组id 当转交到用户时，需要保存用户所属用户组id，用于回显 */
  displayUserGroupId?: /* Integer */ string;
  /** 重要性 */
  importance?: /* 枚举: High :高 | Medium :中 | Low :低 | None :无 | Unknown :未知 */ deviceImportance;
  /** 紧急性 */
  severity?: /* 枚举: Critical :Critical | Major :Major | Minor :Minor | Warning :Warning | Unknown :Unknown | Normal :Normal | Informational :Informational | Calculating :Calculating | Symptom :Symptom | Monitoring :Monitoring | Others :在映射中展示Critical -- Monitoring, */ eventSeverity;
  /** 工单影响性 */
  influence: /* 枚举: High :High | Medium :Medium | Low :Low | None :None | Unknown :Unknown */ deviceImportance;
  /** 工单紧急性 */
  urgency: /* 枚举: High :High | Medium :Medium | Low :Low | None :None | Unknown :Unknown */ deviceImportance;
  /** 开始时间 */
  startTime?: /* Integer */ string;
  /** 结束时间 */
  endTime?: /* Integer */ string;
  /** 描述 */
  desc?: string;
  /** 外部id */
  externalId?: string;
  /** 联系人列表 */
  contacts: { contactId: /** 联系人id */ string; contactType: /** 联系人类型 */ "Notification" | "Technical" | "OnSite" }[];
  /** 设备id列表 */
  deviceIds: /* Integer */ string[];
  /** 完结代码 */
  completeInfo: { finishCodeName: /** 完结代码名称 */ string; finishCodeDesc: /** 完结代码描述 */ string; finishContent: /** 完结内容 */ string };
  /** 自动关闭 */
  autoClose?: boolean;
  /** 自动关闭时间 */
  autoCloseTime: /* Integer */ string;
  /** 当前发布是否需要修改有效时间（true:需要修改；false:不要修改） */
  needModifyTime?: boolean;
  /** 创建人信息 */
  createdBy?: string;
  /** 更新人信息 */
  updatedBy?: string;
  /** 创建时间 */
  createdTime?: /* Integer */ string;
  /** 更新时间 */
  updatedTime?: /* Integer */ string;
}
interface PublishQuery {
  /** 工单号 */
  identifier?: string;
  /** 租户名称 */
  tenantName?: string;
  /** 状态 NEW :新建 PROCESSING :处理中 AUTO_CLOSED :自动关闭 CLOSED :关闭 */
  publishState?: string;
  /** 摘要 */
  digest?: string;
  /** 优先级 */
  priority?: string;

  state?: string;
  /** 负责人 */
  responsibleName?: string;
  /** 当前处理人 */
  actorName?: string;

  boardOrNot?: string;
  userId?: string;

  permissionId?: string;
}
/**
 * @description 分页查询
 * @url http://*************:3000/project/17/interface/api/3543
 */
export async function getPublishList(req: Record<string, any> & PublishQuery & PageFilter & DateRangeFilter & Merge<ConditionFilter<"tenantName" | "orderId" | "state" | "actorName" | "responsibleName" | "orderSummary", "include" | "exclude" | "eq" | "ne", "FilterRelation", "AND" | "OR">, ConditionFilter<"alarmCount", "eq" | "ne" | "ge" | "gt" | "le" | "lt" | "isNull" | "isNotNull", "FilterRelation", "AND" | "OR">> & ConditionFilter<"compactActorName", "eq" | "ne" | "include" | "exclude", "FilterRelation", "AND" | "OR">) {
  const controller = new AbortController();

  const userInfo = (await import("@/utils/getUserInfo")).default();
  const { 智能事件中心_客户_工单可读, 智能事件中心_项目_工单可读, 智能事件中心_联系人_工单可读, 智能事件中心_设备_工单可读 } = await import("@/views/pages/permission");

  let urlFlag: "all" | "permission" = "all";
  let url;
  let method;

  if (userInfo.hasPermission(智能事件中心_客户_工单可读)) {
    urlFlag = "all";
    url = `${SERVER.EVENT_CENTER}/publish/query`;
    method = Method.Get;
  } else if (
    /*  */
    (userInfo.hasPermission("756061441173225472" as any) && userInfo.hasPermission(智能事件中心_项目_工单可读)) ||
    (userInfo.hasPermission("756062477225033728" as any) && userInfo.hasPermission(智能事件中心_联系人_工单可读)) ||
    (userInfo.hasPermission("756062918394511360" as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))
  ) {
    urlFlag = "permission";
    url = `${SERVER.EVENT_CENTER}/publish/allPublishList`;
    method = Method.Post;
  }

  if (!url) return { success: true, data: [], message: "" };

  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url, method, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();

        const query = {
          identifier: req.identifier /* 工单号 */,
          tenantName: req.tenantName /* 租户名称 */,
          publishState: !req.state ? req.publishState : void 0 /* 状态NEW :新建PROCESSING :处理中AUTO_CLOSED :自动关闭CLOSED :关闭 */,
          state: req.state || void 0,
          digest: req.digest /* 摘要 */,
          priority: req.priority /* 优先级 */,
          responsibleName: req.responsibleName /* 负责人 */,
          actorName: req.actorName /* 当前处理人 */,

          /* 创建时间 */
          ...(req.createTimeStart && req.createTimeEnd ? { createTimeStart: req.createTimeStart, createTimeEnd: req.createTimeEnd } : {}),

          /* 修改时间 */
          ...(req.updateTimeStart && req.updateTimeEnd ? { updateTimeStart: req.updateTimeStart, updateTimeEnd: req.updateTimeEnd } : {}),

          /* 创建时间AND修改时间 */
          ...(req.compactTimeStart && req.compactTimeEnd ? { compactTimeStart: req.compactTimeStart, compactTimeEnd: req.compactTimeEnd } : {}),

          /* 租户名称 */
          ...([...(req.includeTenantName instanceof Array ? req.includeTenantName : []), ...(req.excludeTenantName instanceof Array ? req.excludeTenantName : []), ...(req.eqTenantName instanceof Array ? req.eqTenantName : []), ...(req.neTenantName instanceof Array ? req.neTenantName : [])].filter((v) => v).length ? { tenantNameFilterRelation: req.tenantNameFilterRelation === "OR" ? "OR" : "AND", includeTenantName: req.includeTenantName instanceof Array && req.includeTenantName.length ? req.includeTenantName.join(",") : void 0, excludeTenantName: req.excludeTenantName instanceof Array && req.excludeTenantName.length ? req.excludeTenantName.join(",") : void 0, eqTenantName: req.eqTenantName instanceof Array && req.eqTenantName.length ? req.eqTenantName.join(",") : void 0, neTenantName: req.neTenantName instanceof Array && req.neTenantName.length ? req.neTenantName.join(",") : void 0 } : {}),

          /* 工单号 */
          ...([...(req.includeOrderId instanceof Array ? req.includeOrderId : []), ...(req.excludeOrderId instanceof Array ? req.excludeOrderId : []), ...(req.eqOrderId instanceof Array ? req.eqOrderId : []), ...(req.neOrderId instanceof Array ? req.neOrderId : [])].filter((v) => v).length ? { orderIdFilterRelation: req.orderIdFilterRelation === "OR" ? "OR" : "AND", includeOrderIds: req.includeOrderId instanceof Array && req.includeOrderId.length ? req.includeOrderId.join(",") : void 0, excludeOrderIds: req.excludeOrderId instanceof Array && req.excludeOrderId.length ? req.excludeOrderId.join(",") : void 0, eqOrderIds: req.eqOrderId instanceof Array && req.eqOrderId.length ? req.eqOrderId.join(",") : void 0, neOrderIds: req.neOrderId instanceof Array && req.neOrderId.length ? req.neOrderId.join(",") : void 0 } : {}),

          /* 工单摘要 */
          ...([...(req.includeOrderSummary instanceof Array ? req.includeOrderSummary : []), ...(req.excludeOrderSummary instanceof Array ? req.excludeOrderSummary : []), ...(req.eqOrderSummary instanceof Array ? req.eqOrderSummary : []), ...(req.neOrderSummary instanceof Array ? req.neOrderSummary : [])].filter((v) => v).length ? { orderSummaryFilterRelation: req.orderSummaryFilterRelation === "OR" ? "OR" : "AND", includeOrderSummary: req.includeOrderSummary instanceof Array && req.includeOrderSummary.length ? req.includeOrderSummary.join(",") : void 0, excludeOrderSummary: req.excludeOrderSummary instanceof Array && req.excludeOrderSummary.length ? req.excludeOrderSummary.join(",") : void 0, eqOrderSummary: req.eqOrderSummary instanceof Array && req.eqOrderSummary.length ? req.eqOrderSummary.join(",") : void 0, neOrderSummary: req.neOrderSummary instanceof Array && req.neOrderSummary.length ? req.neOrderSummary.join(",") : void 0 } : {}),
          // 外部id
          //...([...(req.includeExternalId instanceof Array ? req.includeExternalId : []), ...(req.excludeExternalId instanceof Array ? req.excludeExternalId : []), ...(req.eqExternalId instanceof Array ? req.eqExternalId : []), ...(req.neExternalId instanceof Array ? req.neExternalId : [])].filter((v) => v).length ? { externalIdFilterRelation: req.externalIdFilterRelation === "OR" ? "OR" : "AND", includeExternalId: req.includeExternalId instanceof Array && req.includeExternalId.length ? req.includeExternalId.join(",") : void 0, excludeExternalId: req.excludeExternalId instanceof Array && req.excludeExternalId.length ? req.excludeExternalId.join(",") : void 0, eqExternalId: req.eqExternalId instanceof Array && req.eqExternalId.length ? req.eqExternalId.join(",") : void 0, neExternalId: req.neExternalId instanceof Array && req.neExternalId.length ? req.neExternalId.join(",") : void 0 } : {}),

          /* 状态 */
          ...([...(req.includeState instanceof Array ? req.includeState : []), ...(req.excludeState instanceof Array ? req.excludeState : []), ...(req.eqState instanceof Array ? req.eqState : []), ...(req.neState instanceof Array ? req.neState : [])].filter((v) => v).length ? { stateFilterRelation: req.stateFilterRelation === "OR" ? "OR" : "AND", includeStates: req.includeState instanceof Array && req.includeState.length ? req.includeState.join(",") : void 0, excludeStates: req.excludeState instanceof Array && req.excludeState.length ? req.excludeState.join(",") : void 0, eqStates: req.eqState instanceof Array && req.eqState.length ? req.eqState.join(",") : void 0, neStates: req.neState instanceof Array && req.neState.length ? req.neState.join(",") : void 0 } : {}),

          /* 告警数 */
          ...([...(req.eqAlarmCount instanceof Array ? req.eqAlarmCount : []), ...(req.neAlarmCount instanceof Array ? req.neAlarmCount : []), ...(req.geAlarmCount instanceof Array ? req.geAlarmCount : []), ...(req.gtAlarmCount instanceof Array ? req.gtAlarmCount : []), ...(req.leAlarmCount instanceof Array ? req.leAlarmCount : []), ...(req.ltAlarmCount instanceof Array ? req.ltAlarmCount : []), ...(req.isNullAlarmCount instanceof Array ? req.isNullAlarmCount : []), ...(req.isNotNullAlarmCount instanceof Array ? req.isNotNullAlarmCount : [])].filter((v) => v).length ? { alarmCountFilterRelation: req.alarmCountFilterRelation === "OR" ? "OR" : "AND", eqAlarmCount: req.eqAlarmCount instanceof Array && req.eqAlarmCount.length ? req.eqAlarmCount.join(",") : void 0, neAlarmCount: req.neAlarmCount instanceof Array && req.neAlarmCount.length ? req.neAlarmCount.join(",") : void 0, geAlarmCount: req.geAlarmCount instanceof Array && req.geAlarmCount.length ? req.geAlarmCount.join(",") : void 0, gtAlarmCount: req.gtAlarmCount instanceof Array && req.gtAlarmCount.length ? req.gtAlarmCount.join(",") : void 0, leAlarmCount: req.leAlarmCount instanceof Array && req.leAlarmCount.length ? req.leAlarmCount.join(",") : void 0, ltAlarmCount: req.ltAlarmCount instanceof Array && req.ltAlarmCount.length ? req.ltAlarmCount.join(",") : void 0, isNullAlarmCount: req.isNullAlarmCount instanceof Array && req.isNullAlarmCount.length ? req.isNullAlarmCount.join(",") : void 0, isNotNullAlarmCount: req.isNotNullAlarmCount instanceof Array && req.isNotNullAlarmCount.length ? req.isNotNullAlarmCount.join(",") : void 0 } : {}),

          /* 处理人 */
          ...([...(req.includeActorName instanceof Array ? req.includeActorName : []), ...(req.excludeActorName instanceof Array ? req.excludeActorName : []), ...(req.eqActorName instanceof Array ? req.eqActorName : []), ...(req.neActorName instanceof Array ? req.neActorName : [])].filter((v) => v).length ? { actorNameFilterRelation: req.actorNameFilterRelation === "OR" ? "OR" : "AND", includeActorName: req.includeActorName instanceof Array && req.includeActorName.length ? req.includeActorName.join(",") : void 0, excludeActorName: req.excludeActorName instanceof Array && req.excludeActorName.length ? req.excludeActorName.join(",") : void 0, eqActorName: req.eqActorName instanceof Array && req.eqActorName.length ? req.eqActorName.join(",") : void 0, neActorName: req.neActorName instanceof Array && req.neActorName.length ? req.neActorName.join(",") : void 0 } : {}),

          /* 负责人 */
          ...([...(req.includeResponsibleName instanceof Array ? req.includeResponsibleName : []), ...(req.excludeResponsibleName instanceof Array ? req.excludeResponsibleName : []), ...(req.eqResponsibleName instanceof Array ? req.eqResponsibleName : []), ...(req.neResponsibleName instanceof Array ? req.neResponsibleName : [])].filter((v) => v).length ? { responsibleNameFilterRelation: req.responsibleNameFilterRelation === "OR" ? "OR" : "AND", includeResponsibleName: req.includeResponsibleName instanceof Array && req.includeResponsibleName.length ? req.includeResponsibleName.join(",") : void 0, excludeResponsibleName: req.excludeResponsibleName instanceof Array && req.excludeResponsibleName.length ? req.excludeResponsibleName.join(",") : void 0, eqResponsibleName: req.eqResponsibleName instanceof Array && req.eqResponsibleName.length ? req.eqResponsibleName.join(",") : void 0, neResponsibleName: req.neResponsibleName instanceof Array && req.neResponsibleName.length ? req.neResponsibleName.join(",") : void 0 } : {}),

          /* 负责人AND处理人 */
          ...([...(req.eqCompactActorName instanceof Array ? req.eqCompactActorName : []), ...(req.neCompactActorName instanceof Array ? req.neCompactActorName : []), ...(req.includeCompactActorName instanceof Array ? req.includeCompactActorName : []), ...(req.excludeCompactActorName instanceof Array ? req.excludeCompactActorName : [])].filter((v) => v).length ? { compactActorNameFilterRelation: req.compactActorNameFilterRelation === "OR" ? "OR" : "AND", compactEqActorName: req.eqCompactActorName instanceof Array && req.eqCompactActorName.length ? req.eqCompactActorName.join(",") : void 0, compactNeActorName: req.neCompactActorName instanceof Array && req.neCompactActorName.length ? req.neCompactActorName.join(",") : void 0, compactIncludeActorName: req.includeCompactActorName instanceof Array && req.includeCompactActorName.length ? req.includeCompactActorName.join(",") : void 0, compactExcludeActorName: req.excludeCompactActorName instanceof Array && req.excludeCompactActorName.length ? req.excludeCompactActorName.join(",") : void 0 } : {}),

          ...([...(req.eqTicketGroupName instanceof Array ? req.eqTicketGroupName : []), ...(req.neTicketGroupName instanceof Array ? req.neTicketGroupName : []), ...(req.inTicketGroupName instanceof Array ? req.inTicketGroupName : []), ...(req.excludeTicketGroupName instanceof Array ? req.excludeTicketGroupName : [])].filter((v) => v).length ? { ticketGroupNameFilterRelation: req.ticketGroupNameFilterRelation === "OR" ? "OR" : "AND", eqTicketGroupName: req.eqTicketGroupName instanceof Array && req.eqTicketGroupName.length ? req.eqTicketGroupName.join(",") : void 0, neTicketGroupName: req.neTicketGroupName instanceof Array && req.neTicketGroupName.length ? req.neTicketGroupName.join(",") : void 0, inTicketGroupName: req.inTicketGroupName instanceof Array && req.inTicketGroupName.length ? req.inTicketGroupName.join(",") : void 0, excludeTicketGroupName: req.excludeTicketGroupName instanceof Array && req.excludeTicketGroupName.length ? req.excludeTicketGroupName.join(",") : void 0 } : {}),

          ...([...(req.eqUserGroupName instanceof Array ? req.eqUserGroupName : []), ...(req.neUserGroupName instanceof Array ? req.neUserGroupName : []), ...(req.inUserGroupName instanceof Array ? req.inUserGroupName : []), ...(req.excludeUserGroupName instanceof Array ? req.excludeUserGroupName : [])].filter((v) => v).length ? { userGroupNameFilterRelation: req.userGroupNameFilterRelation === "OR" ? "OR" : "AND", eqUserGroupName: req.eqUserGroupName instanceof Array && req.eqUserGroupName.length ? req.eqUserGroupName.join(",") : void 0, neUserGroupName: req.neUserGroupName instanceof Array && req.neUserGroupName.length ? req.neUserGroupName.join(",") : void 0, inUserGroupName: req.inUserGroupName instanceof Array && req.inUserGroupName.length ? req.inUserGroupName.join(",") : void 0, excludeUserGroupName: req.excludeUserGroupName instanceof Array && req.excludeUserGroupName.length ? req.excludeUserGroupName.join(",") : void 0 } : {}),

          ...([...(req.compactEqTicketName instanceof Array ? req.compactEqTicketName : []), ...(req.compactNeTicketName instanceof Array ? req.compactNeTicketName : []), ...(req.compactIncludeTicketName instanceof Array ? req.compactIncludeTicketName : []), ...(req.compactExcludeTicketName instanceof Array ? req.compactExcludeTicketName : [])].filter((v) => v).length ? { compactTicketNameFilterRelation: req.compactTicketNameFilterRelation === "OR" ? "OR" : "AND", compactEqTicketName: req.compactEqTicketName instanceof Array && req.compactEqTicketName.length ? req.compactEqTicketName.join(",") : void 0, compactNeTicketName: req.compactNeTicketName instanceof Array && req.compactNeTicketName.length ? req.compactNeTicketName.join(",") : void 0, compactIncludeTicketName: req.compactIncludeTicketName instanceof Array && req.compactIncludeTicketName.length ? req.compactIncludeTicketName.join(",") : void 0, compactExcludeTicketName: req.compactExcludeTicketName instanceof Array && req.compactExcludeTicketName.length ? req.compactExcludeTicketName.join(",") : void 0 } : {}),

          boardOrNot: req.boardOrNot,
          userId: req.userId,
        };

        bindParamByObj(
          Object.assign(
            {
              pageNumber: req.paging.pageNumber /* 页码, 默认第一页 */,
              pageSize: req.paging.pageSize /* 页大小, 默认10 */,
              sort: req.sort,
              queryType: req.queryType /* 工单参数 */,
            },
            urlFlag === "all" ? query : {}
          ),
          $req.params
        );
        try {
          bindParamByObj({ permissionId: req.permissionId || (await import("@/views/pages/permission")).监控管理中心_告警_可读 }, $req.params);
        } catch (error) {
          /*  */
        }

        $req.data =
          urlFlag === "permission"
            ? Object.assign(query, {
                permissionList: [
                  /*  */
                  { permissionId: 智能事件中心_项目_工单可读, permissionType: "View Tickets When Project Has View Tickets" },
                  { permissionId: 智能事件中心_联系人_工单可读, permissionType: "View Tickets When Contact Has View Tickets" },
                  { permissionId: 智能事件中心_设备_工单可读, permissionType: "View Tickets When Device Has View Tickets" },
                ],
              })
            : void 0;
        return $req;
      })
      .then(($req) => request<never, Response<PublishItem[]>>($req)),
    { controller }
  );
}

export async function getPublishStateCount(req: {} & RequestBase) {
  const userInfo = (await import("@/utils/getUserInfo")).default();
  const { 智能事件中心_客户_工单可读, 智能事件中心_项目_工单可读, 智能事件中心_联系人_工单可读, 智能事件中心_设备_工单可读 } = await import("@/views/pages/permission");
  let url;
  let method;
  let params = {};
  let data = {};

  if (userInfo.hasPermission(智能事件中心_客户_工单可读)) {
    url = `${SERVER.EVENT_CENTER}/publish/state/allCount`;
    method = Method.Get;
    params = {
      eventId: req.id,
      permissionId: "612917583569485824",
    };
  } else if (
    /*  */
    (userInfo.hasPermission("756061441173225472" as any) && userInfo.hasPermission(智能事件中心_项目_工单可读)) ||
    (userInfo.hasPermission("756062477225033728" as any) && userInfo.hasPermission(智能事件中心_联系人_工单可读)) ||
    (userInfo.hasPermission("756062918394511360" as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))
  ) {
    url = `${SERVER.EVENT_CENTER}/publish/state/allPublishCount`;
    method = Method.Post;
    data = {
      eventId: req.id,
      permissionId: "612917583569485824",
      permissionList: [
        /*  */
        { permissionId: 智能事件中心_项目_工单可读, permissionType: "View Tickets When Project Has View Tickets" },
        { permissionId: 智能事件中心_联系人_工单可读, permissionType: "View Tickets When Contact Has View Tickets" },
        { permissionId: 智能事件中心_设备_工单可读, permissionType: "View Tickets When Device Has View Tickets" },
      ],
      boardOrNot: false,
    };
  }

  if (!url) return { success: true, data: [], message: "" };

  return request<unknown, Response<{ publishState: publishState; count: number }[]>>({
    url,
    method,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}

export async function getPublishDetail(data: { id: string } & RequestBase) {
  const { 智能事件中心_发布管理_可读, 智能事件中心_发布管理_更新, 智能事件中心_发布管理_创建审批, 智能事件中心_发布管理_编辑小记, 智能事件中心_发布管理_分配设备, 智能事件中心_发布管理_分配联系人, 智能事件中心_发布管理_关联工单 } = await import("@/views/pages/permission");
  return request<unknown, Response<publishItem>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/detail`,
    method: Method.Patch,
    // responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {
      queryPermissionId: [智能事件中心_发布管理_可读].join(),
      verifyPermissionIds: [智能事件中心_发布管理_更新, 智能事件中心_发布管理_创建审批, 智能事件中心_发布管理_编辑小记, 智能事件中心_发布管理_分配设备, 智能事件中心_发布管理_分配联系人, 智能事件中心_发布管理_关联工单].join(),
    },
    data: {},
  });
}

/**
 * @name 编辑发布详情
 * @export
 * @param {({ id: string;  } & RequestBase)} data
 * @return {*}
 */
export function setPublishItemDetail(req: {} & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/publish/${req.id}/update`,
    method: Method.Put,
    responseType: "json",
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
    params: {},
    data: Object.keys(req).reduce((p, key) => Object.assign(p, req[key] === undefined ? {} : { [key]: req[key] }), {}),
  });
}

/**
 * @name 编辑发布描述
 * @param req
 * @returns
 */
export function setPublishItemDesc(req: {} & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/publish/updateDesc`,
    method: Method.Post,
    responseType: "json",
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */,
    params: {},
    data: Object.keys(req).reduce((p, key) => Object.assign(p, req[key] === undefined ? {} : { [key]: req[key] }), {}),
  });
}

export interface publishNote {
  noteId: string;
  eventId: string;
  noteContent: string;
  attachmentList: {
    attachmentId: string;
    attachmentName: string;
    attachmentUrl: string;
    attachmentKey: string;
  }[];
  noteCreateTime: string;
  noteCreateUserId: string;
  noteCreateUserName: string;
  noteCreateUserProfilePicture: string;
}

/**
 * @name 获取小记
 * @export
 * @param {({ id: string } & RequestBase)} data
 * @return {*}
 */
export function getPublishNotes(data: { id: string } & RequestBase) {
  return request<unknown, Response<publishNote[]>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/queryNote`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 保存小记
 * @export
 * @param {({ eventId: string; content: string; attachmentList: { attachmentId: string; attachmentName: string; attachmentUrl: string; attachmentKey: string } } & RequestBase)} data
 */
export function addPublishNote(data: { eventId: string; content: string; attachmentList: { attachmentId: string; attachmentName: string; attachmentUrl: string; attachmentKey: string }[] } & RequestBase) {
  return request<unknown, Response<publishNote>>({
    url: `${SERVER.EVENT_CENTER}/publish/saveNote`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["eventId", "content", "attachmentList", "privateAble"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function setPublishNote(data: { eventId: string; noteId: string; content: string; attachmentList: { attachmentId: string; attachmentName: string; attachmentUrl: string; attachmentKey: string }[] } & RequestBase) {
  return request<unknown, Response<publishNote>>({
    url: `${SERVER.EVENT_CENTER}/publish/editNote`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["eventId", "content", "attachmentList", "noteId", "privateAble"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 删除小记
 * @export
 * @param {({ id: string } & RequestBase)} data
 * @return {*}
 */
export function delPublishNote(data: { id: string } & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/deleteNote`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export interface PublishNote {
  noteId: string;
  eventId: string;
  noteContent: string;
  attachmentList: {
    attachmentId: string;
    attachmentName: string;
    attachmentUrl: string;
    attachmentKey: string;
  }[];
  noteCreateUserId: string;
  tenantId: string;
  noteCreateUserName: string;
  noteCreateUserProfilePicture: string;
  noteCreateTime: string;
}

export function getChangeNotes(data: { id: string } & RequestBase) {
  return request<unknown, Response<PublishNote[]>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/queryNote`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function addChangeNote(data: { eventId: string; content: string; attachmentList: { attachmentId: string; attachmentName: string; attachmentUrl: string; attachmentKey: string }[] } & RequestBase) {
  return request<unknown, Response<PublishNote>>({
    url: `${SERVER.EVENT_CENTER}/publish/editNote`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["eventId", "content", "attachmentList"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function setChangeNote(data: { eventId: string; noteId: string; content: string; attachmentList: { attachmentId: string; attachmentName: string; attachmentUrl: string; attachmentKey: string }[] } & RequestBase) {
  return request<unknown, Response<PublishNote>>({
    url: `${SERVER.EVENT_CENTER}/publish/editNote`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["eventId", "content", "attachmentList", "noteId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function delChangeNote(data: { id: string; noteId: string } & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/deleteNote`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 添加设备
 * @export
 * @param {({ id: string; deviceIds: string } & RequestBase)} data
 * @return {*}
 */
export function addPublishDevices(data: { id: string; deviceIds: string; autoAllocateContact: boolean } & RequestBase) {
  return request<unknown, Response<publishItem>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/addDevice/${data.deviceIds}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["autoAllocateContact"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 移除设备
f * @export
 * @param {({ id: string; deviceId: string } & RequestBase)} data
 * @return {*}
 */
export function delPublishDevice(data: { id: string; deviceId: string } & RequestBase) {
  return request<unknown, Response<publishItem>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/removeDevice/${data.deviceId}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 添加联系人
 * @export
 * @param {({ id: string; contactType: string; contactId: string } & RequestBase)} data
 * @return {*}
 */
export function addPublishContact(data: { id: string; contactType: string; contactIds: string[] } & RequestBase) {
  return request<unknown, Response<publishItem>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/addContact`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["contactType", "contactIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 移除联系人
 * @export
 * @param {({ id: string; contactType: string; contactId: string } & RequestBase)} data
 * @return {*}
 */
export function delPublishContact(data: { id: string; contactType: string; contactIds: string[] } & RequestBase) {
  return request<unknown, Response<publishItem>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/removeContact`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["contactType", "contactIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 处理
 * @export
 * @param {({id: string} & RequestBase)} data
 * @return {*}
 */
export function setpublishStateProcessing(data: { id: string } & RequestBase) {
  return request<unknown, Response<publishItem>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/handle`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function setpublishStateCompleted(data: { id: string; completeInfo: { finishCodeName: string; finishCodeDesc: string; finishContent: string } } & RequestBase) {
  return request<unknown, Response<publishItem>>({
    url: `${SERVER.EVENT_CENTER}/publish/complete`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["id", "completeInfo"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function setpublishStateClose(data: { id: string; completeInfo: { finishCodeName: string; finishCodeDesc: string; finishContent: string } } & RequestBase) {
  return request<unknown, Response<publishItem>>({
    url: `${SERVER.EVENT_CENTER}/publish/close`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["id", "completeInfo"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function publishTransfer(data: { id: string; userGroupId: string; userId: string } & RequestBase) {
  return request<unknown, Response<publishItem>>({
    url: `${SERVER.EVENT_CENTER}/publish/transfer`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["id", "userGroupId", "userId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export interface TabCount {
  noteCount: string /* 小记数量 */;
  deviceCount: string /* 设备数量 */;
  contactCount: string /* 联系人数量 */;
  relationCount: string /* 关联数量 */;
  fileCount: string /* 文件数量 */;
  unconfirmed: string;
  confirmed: string;
  actionCount: string;
}
export function /* 返回tap统计数量 */ getDetailTapCountById(req: { id: string } & RequestBase) {
  const params = [].reduce((p, key) => Object.assign(p, req[key] === undefined ? {} : { [key]: req[key] }), {});
  const data = [].reduce((p, key) => Object.assign(p, req[key] === undefined ? {} : { [key]: req[key] }), {});
  return request<never, Response<TabCount>>({ url: `${SERVER.EVENT_CENTER}/publish/tap/${req.id}/count`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export interface ApproveItem {
  id: string;
  tenantId: string;
  orderId: string;
  teamId: string;
  teamName: string;
  approveState: string;
  hasAuthority: boolean;
  createdBy: string;
  updatedBy: string;
  createdTime: string;
  updatedTime: string;
}

export enum ApproveState {
  SUSPEND = "SUSPEND",
  REFUSED = "REFUSED",
  PASSED = "PASSED",
}
export const approveStateOption = [
  { value: ApproveState.SUSPEND, label: "未审批", type: "warning" },
  { value: ApproveState.REFUSED, label: "已拒绝", type: "danger" },
  { value: ApproveState.PASSED, label: "已通过", type: "success" },
];

export function getApproves(data: { id: string; pageNumber: number; pageSize: number } & RequestBase) {
  return request<unknown, Response<ApproveItem[]>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/approve/query`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["pageNumber", "pageSize"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function addPublishApprove(data: { id: string; teamId: string } & RequestBase) {
  return request<unknown, Response<publishItem>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/approval/${data.teamId}/${智能事件中心_发布管理_创建审批}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function delPublishApprove(data: { id: string; approveId: string } & RequestBase) {
  return request<unknown, Response<publishItem>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/approve/${data.approveId}/delete/612916552185937920`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function publishApproveRefuse(data: { id: string; approveId: string } & RequestBase) {
  return request<unknown, Response<publishItem>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/approve/${data.approveId}/refuse`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function publishApprovePass(data: { id: string; approveId: string } & RequestBase) {
  return request<unknown, Response<publishItem>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/approve/${data.approveId}/pass/${智能事件中心_发布管理_审批}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
//根据当前年月查询那天是否存在发布
export function getDatePublishList(data: {} & RequestBase) {
  return request<unknown, Response<string[]>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.date}/queryByYearMonth`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: {},
  });
}
//根据日期查询发布信息列表
export function getPublishDateList(data: {} & RequestBase) {
  return request<unknown, Response<publishItem[]>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.date}/queryByDate`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: {},
  });
}

//根据重要性发布信息
export function setPublishItemImportance(data: {} & RequestBase) {
  return request<unknown, Response<publishItem[]>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/importance/${data.importance}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: {},
  });
}

//根据紧急性发布信息
export function setPublishItemEmergent(data: {} & RequestBase) {
  return request<unknown, Response<publishItem[]>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/severity/${data.severity}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: {},
  });
}

export function getChangeContacts(data: { id: string } & RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/publish/${data.id}/getContact`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: {},
  });
}

// /event_center/bhilpsu / { id } / importance / { importance };
