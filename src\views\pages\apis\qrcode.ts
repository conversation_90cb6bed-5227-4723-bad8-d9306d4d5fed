import { SERVER, Method, bindSearchParams, type Response, type RequestBase, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";
import { i18n } from "@/lang/index";

const { t } = i18n.global;

export enum Urgency {
  VERY_URGENT = "VERY_URGENT",
  URGENT = "URGENT",
  NORMAL = "NORMAL",
  NOT_URGENT = "NOT_URGENT",
}

export const urgencyOption: { value: keyof typeof Urgency; label: string; color: string }[] = [
  { value: Urgency.VERY_URGENT, label: t("qrcode.Critical"), color: "#ED4013" },
  { value: Urgency.URGENT, label: t("qrcode.Urgent"), color: "#FF7D00" },
  { value: Urgency.NORMAL, label: t("qrcode.Normal"), color: "#2CB6F4" },
  { value: Urgency.NOT_URGENT, label: t("qrcode.Low Priority"), color: "#3EBE6B" },
];

export enum Status {
  NEW = "NEW",
  REPORTED = "REPORTED",
  DEPRECATED = "DEPRECATED",
  FIXED = "FIXED",
}
export interface QrcodeCountData {
  createCount?: string;
  deleteCount?: string;
  makeCount?: string;
  disposeCount?: string;
}

export const statusOption: { value: keyof typeof Status; label: string; type: string }[] = [
  { value: Status.NEW, label: t("qrcode.New"), type: "danger" },
  { value: Status.REPORTED, label: t("qrcode.Reported"), type: "primary" },
  { value: Status.DEPRECATED, label: t("qrcode.Deprecated"), type: "info" },
  { value: Status.FIXED, label: t("qrcode.Fixed"), type: "success" },
];

export interface QrcodeItem {
  failureTitle: string;
  /** 故障描述 */
  failureDescription: string;
  /** 统一服务编码 */
  unificationCode: string;
  /** 项目名称 */
  projectName?: string;
  /** 紧急程度枚举 非常紧急 - VERY_URGENT 紧急 - URGENT 一般 - NORMAL 不紧急 - NOT_URGENT */
  urgency?: string;
  /** 联系人姓名 */
  contactName: string;
  /** 联系人电话 */
  contactPhone: string;
  /** 联系人2姓名 */
  spareContactName?: string;
  /** 联系人2电话 */
  spareContactPhone?: string;
  /** 报障登录联系人电话 */
  reportFaultPhone?: string;
  /** 状态枚举 新增 - NEW 已报障 - REPORTED 废弃 - DEPRECATED */
  status?: string;
  /** 客保工单号 */
  kebaoCode?: string;
  /** 负责人 */
  chargePerson?: string;
  /** 处理人 */
  processors?: string;
  createTime?: /* Integer */ string;
  /** 原因 */
  reason?: string;
}

export function getQrcode(data: {} & RequestBase) {
  return request<unknown, Response<QrcodeItem[]>>({
    url: `${SERVER.EVENT_CENTER}/qrcode/queryAll`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["pageNumber", "pageSize", "code", "status"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export async function getQrcodeByFilter(req: Record<string, any> & { pageNumber: string /* 页码, 默认第一页 */; pageSize: string /* 页大小, 默认10 */; sort: string[] } & Record<string, any>) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/qrcode/queryList`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();

        // $req.params = new bindParamByObj();

        bindParamByObj({ pageNumber: req.pageNumber /* 页码, 默认第一页 */, pageSize: req.pageSize /* 页大小, 默认10 */, queryType: req.queryType }, $req.params);

        const { 系统管理中心_客户管理_可读 } = await import("@/views/pages/permission");

        $req.data = {
          /* */ type: req.type,
          tenantPermissionId: req.tenantPermissionId || 系统管理中心_客户管理_可读,
          status: req.status ? req.status.split(",") : void 0,
          urgency: req.urgency ? req.urgency.split(",") : void 0,
          code: req.code ? req.code : void 0,
          startTime: req.startTime,
          endTime: req.endTime,

          ...([...(req.includeAbbreviation instanceof Array ? req.includeAbbreviation : []), ...(req.excludeAbbreviation instanceof Array ? req.excludeAbbreviation : []), ...(req.eqAbbreviation instanceof Array ? req.eqAbbreviation : []), ...(req.neAbbreviation instanceof Array ? req.neAbbreviation : [])].filter((v) => v).length ? { abbreviationFilterRelation: req.abbreviationFilterRelation === "OR" ? "OR" : "AND", includeAbbreviation: req.includeAbbreviation instanceof Array && req.includeAbbreviation.length ? req.includeAbbreviation.join(",") : void 0, excludeAbbreviation: req.excludeAbbreviation instanceof Array && req.excludeAbbreviation.length ? req.excludeAbbreviation.join(",") : void 0, eqAbbreviation: req.eqAbbreviation instanceof Array && req.eqAbbreviation.length ? req.eqAbbreviation.join(",") : void 0, neAbbreviation: req.neAbbreviation instanceof Array && req.neAbbreviation.length ? req.neAbbreviation.join(",") : void 0 } : {}),

          ...([...(req.includeName instanceof Array ? req.includeName : []), ...(req.excludeName instanceof Array ? req.excludeName : []), ...(req.eqName instanceof Array ? req.eqName : []), ...(req.neName instanceof Array ? req.neName : [])].filter((v) => v).length ? { nameFilterRelation: req.nameFilterRelation === "OR" ? "OR" : "AND", includeName: req.includeName instanceof Array && req.includeName.length ? req.includeName.join(",") : void 0, excludeName: req.excludeName instanceof Array && req.excludeName.length ? req.excludeName.join(",") : void 0, eqName: req.eqName instanceof Array && req.eqName.length ? req.eqName.join(",") : void 0, neName: req.neName instanceof Array && req.neName.length ? req.neName.join(",") : void 0 } : {}),

          ...([...(req.includeFailureTitle instanceof Array ? req.includeFailureTitle : []), ...(req.excludeFailureTitle instanceof Array ? req.excludeFailureTitle : []), ...(req.eqFailureTitle instanceof Array ? req.eqFailureTitle : []), ...(req.neFailureTitle instanceof Array ? req.neFailureTitle : [])].filter((v) => v).length ? { failureTitleFilterRelation: req.failureTitleFilterRelation === "OR" ? "OR" : "AND", includeFailureTitle: req.includeFailureTitle instanceof Array && req.includeFailureTitle.length ? req.includeFailureTitle.join(",") : void 0, excludeFailureTitle: req.excludeFailureTitle instanceof Array && req.excludeFailureTitle.length ? req.excludeFailureTitle.join(",") : void 0, eqFailureTitle: req.eqFailureTitle instanceof Array && req.eqFailureTitle.length ? req.eqFailureTitle.join(",") : void 0, neFailureTitle: req.neFailureTitle instanceof Array && req.neFailureTitle.length ? req.neFailureTitle.join(",") : void 0 } : {}),

          ...([...(req.includeFailureDescription instanceof Array ? req.includeFailureDescription : []), ...(req.excludeFailureDescription instanceof Array ? req.excludeFailureDescription : []), ...(req.eqFailureDescription instanceof Array ? req.eqFailureDescription : []), ...(req.neFailureDescription instanceof Array ? req.neFailureDescription : [])].filter((v) => v).length ? { failureDescriptionFilterRelation: req.failureDescriptionFilterRelation === "OR" ? "OR" : "AND", includeFailureDescription: req.includeFailureDescription instanceof Array && req.includeFailureDescription.length ? req.includeFailureDescription.join(",") : void 0, excludeFailureDescription: req.excludeFailureDescription instanceof Array && req.excludeFailureDescription.length ? req.excludeFailureDescription.join(",") : void 0, eqFailureDescription: req.eqFailureDescription instanceof Array && req.eqFailureDescription.length ? req.eqFailureDescription.join(",") : void 0, neFailureDescription: req.neFailureDescription instanceof Array && req.neFailureDescription.length ? req.neFailureDescription.join(",") : void 0 } : {}),

          ...([...(req.includeUnificationCode instanceof Array ? req.includeUnificationCode : []), ...(req.excludeUnificationCode instanceof Array ? req.excludeUnificationCode : []), ...(req.eqUnificationCode instanceof Array ? req.eqUnificationCode : []), ...(req.neUnificationCode instanceof Array ? req.neUnificationCode : [])].filter((v) => v).length ? { unificationCodeFilterRelation: req.unificationCodeFilterRelation === "OR" ? "OR" : "AND", includeUnificationCode: req.includeUnificationCode instanceof Array && req.includeUnificationCode.length ? req.includeUnificationCode.join(",") : void 0, excludeUnificationCode: req.excludeUnificationCode instanceof Array && req.excludeUnificationCode.length ? req.excludeUnificationCode.join(",") : void 0, eqUnificationCode: req.eqUnificationCode instanceof Array && req.eqUnificationCode.length ? req.eqUnificationCode.join(",") : void 0, neUnificationCode: req.neUnificationCode instanceof Array && req.neUnificationCode.length ? req.neUnificationCode.join(",") : void 0 } : {}),

          ...([...(req.includeKebaoCode instanceof Array ? req.includeKebaoCode : []), ...(req.excludeKebaoCode instanceof Array ? req.excludeKebaoCode : []), ...(req.eqKebaoCode instanceof Array ? req.eqKebaoCode : []), ...(req.neKebaoCode instanceof Array ? req.neKebaoCode : [])].filter((v) => v).length ? { kebaoCodeFilterRelation: req.kebaoCodeFilterRelation === "OR" ? "OR" : "AND", includeKebaoCode: req.includeKebaoCode instanceof Array && req.includeKebaoCode.length ? req.includeKebaoCode.join(",") : void 0, excludeKebaoCode: req.excludeKebaoCode instanceof Array && req.excludeKebaoCode.length ? req.excludeKebaoCode.join(",") : void 0, eqKebaoCode: req.eqKebaoCode instanceof Array && req.eqKebaoCode.length ? req.eqKebaoCode.join(",") : void 0, neKebaoCode: req.neKebaoCode instanceof Array && req.neKebaoCode.length ? req.neKebaoCode.join(",") : void 0 } : {}),

          chargePerson: req.chargePerson || void 0,
          processors: req.processors || void 0,

          ...([...(req.includeChargePerson instanceof Array ? req.includeChargePerson : []), ...(req.excludeChargePerson instanceof Array ? req.excludeChargePerson : []), ...(req.eqChargePerson instanceof Array ? req.eqChargePerson : []), ...(req.neChargePerson instanceof Array ? req.neChargePerson : [])].filter((v) => v).length ? { chargePersonFilterRelation: req.chargePersonFilterRelation === "OR" ? "OR" : "AND", includeChargePerson: req.includeChargePerson instanceof Array && req.includeChargePerson.length ? req.includeChargePerson.join(",") : void 0, excludeChargePerson: req.excludeChargePerson instanceof Array && req.excludeChargePerson.length ? req.excludeChargePerson.join(",") : void 0, eqChargePerson: req.eqChargePerson instanceof Array && req.eqChargePerson.length ? req.eqChargePerson.join(",") : void 0, neChargePerson: req.neChargePerson instanceof Array && req.neChargePerson.length ? req.neChargePerson.join(",") : void 0 } : {}),

          ...([...(req.includeProcessors instanceof Array ? req.includeProcessors : []), ...(req.excludeProcessors instanceof Array ? req.excludeProcessors : []), ...(req.eqProcessors instanceof Array ? req.eqProcessors : []), ...(req.neProcessors instanceof Array ? req.neProcessors : [])].filter((v) => v).length ? { processorsFilterRelation: req.processorsFilterRelation === "OR" ? "OR" : "AND", includeProcessors: req.includeProcessors instanceof Array && req.includeProcessors.length ? req.includeProcessors.join(",") : void 0, excludeProcessors: req.excludeProcessors instanceof Array && req.excludeProcessors.length ? req.excludeProcessors.join(",") : void 0, eqProcessors: req.eqProcessors instanceof Array && req.eqProcessors.length ? req.eqProcessors.join(",") : void 0, neProcessors: req.neProcessors instanceof Array && req.neProcessors.length ? req.neProcessors.join(",") : void 0 } : {}),
        };

        return $req;
      })
      .then(($req) => request<never, Response<QrcodeItem[]>>($req)),
    { controller }
  );
}

export async function getQrcodeList(req: Record<string, any> & { pageNumber: string /* 页码, 默认第一页 */; pageSize: string /* 页大小, 默认10 */; sort: string[] } & Record<string, any>) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/qrcode/queryList`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();

        // $req.params = new bindParamByObj();

        bindParamByObj({ pageNumber: req.pageNumber /* 页码, 默认第一页 */, pageSize: req.pageSize /* 页大小, 默认10 */ }, $req.params);

        $req.data = {
          /* */ type: req.type,
          status: req.status ? req.status.split(",") : void 0,
          urgency: req.urgency ? req.urgency.split(",") : void 0,

          code: req.code ? req.code : void 0,

          ...([...(req.includeAbbreviation instanceof Array ? req.includeAbbreviation : []), ...(req.excludeAbbreviation instanceof Array ? req.excludeAbbreviation : []), ...(req.eqAbbreviation instanceof Array ? req.eqAbbreviation : []), ...(req.neAbbreviation instanceof Array ? req.neAbbreviation : [])].filter((v) => v).length ? { abbreviationFilterRelation: req.abbreviationFilterRelation === "OR" ? "OR" : "AND", includeAbbreviation: req.includeAbbreviation instanceof Array && req.includeAbbreviation.length ? req.includeAbbreviation.join(",") : void 0, excludeAbbreviation: req.excludeAbbreviation instanceof Array && req.excludeAbbreviation.length ? req.excludeAbbreviation.join(",") : void 0, eqAbbreviation: req.eqAbbreviation instanceof Array && req.eqAbbreviation.length ? req.eqAbbreviation.join(",") : void 0, neAbbreviation: req.neAbbreviation instanceof Array && req.neAbbreviation.length ? req.neAbbreviation.join(",") : void 0 } : {}),

          ...([...(req.includeName instanceof Array ? req.includeName : []), ...(req.excludeName instanceof Array ? req.excludeName : []), ...(req.eqName instanceof Array ? req.eqName : []), ...(req.neName instanceof Array ? req.neName : [])].filter((v) => v).length ? { nameFilterRelation: req.nameFilterRelation === "OR" ? "OR" : "AND", includeName: req.includeName instanceof Array && req.includeName.length ? req.includeName.join(",") : void 0, excludeName: req.excludeName instanceof Array && req.excludeName.length ? req.excludeName.join(",") : void 0, eqName: req.eqName instanceof Array && req.eqName.length ? req.eqName.join(",") : void 0, neName: req.neName instanceof Array && req.neName.length ? req.neName.join(",") : void 0 } : {}),

          ...([...(req.includeFailureTitle instanceof Array ? req.includeFailureTitle : []), ...(req.excludeFailureTitle instanceof Array ? req.excludeFailureTitle : []), ...(req.eqFailureTitle instanceof Array ? req.eqFailureTitle : []), ...(req.neFailureTitle instanceof Array ? req.neFailureTitle : [])].filter((v) => v).length ? { failureTitleFilterRelation: req.failureTitleFilterRelation === "OR" ? "OR" : "AND", includeFailureTitle: req.includeFailureTitle instanceof Array && req.includeFailureTitle.length ? req.includeFailureTitle.join(",") : void 0, excludeFailureTitle: req.excludeFailureTitle instanceof Array && req.excludeFailureTitle.length ? req.excludeFailureTitle.join(",") : void 0, eqFailureTitle: req.eqFailureTitle instanceof Array && req.eqFailureTitle.length ? req.eqFailureTitle.join(",") : void 0, neFailureTitle: req.neFailureTitle instanceof Array && req.neFailureTitle.length ? req.neFailureTitle.join(",") : void 0 } : {}),

          ...([...(req.includeFailureDescription instanceof Array ? req.includeFailureDescription : []), ...(req.excludeFailureDescription instanceof Array ? req.excludeFailureDescription : []), ...(req.eqFailureDescription instanceof Array ? req.eqFailureDescription : []), ...(req.neFailureDescription instanceof Array ? req.neFailureDescription : [])].filter((v) => v).length ? { failureDescriptionFilterRelation: req.failureDescriptionFilterRelation === "OR" ? "OR" : "AND", includeFailureDescription: req.includeFailureDescription instanceof Array && req.includeFailureDescription.length ? req.includeFailureDescription.join(",") : void 0, excludeFailureDescription: req.excludeFailureDescription instanceof Array && req.excludeFailureDescription.length ? req.excludeFailureDescription.join(",") : void 0, eqFailureDescription: req.eqFailureDescription instanceof Array && req.eqFailureDescription.length ? req.eqFailureDescription.join(",") : void 0, neFailureDescription: req.neFailureDescription instanceof Array && req.neFailureDescription.length ? req.neFailureDescription.join(",") : void 0 } : {}),

          ...([...(req.includeUnificationCode instanceof Array ? req.includeUnificationCode : []), ...(req.excludeUnificationCode instanceof Array ? req.excludeUnificationCode : []), ...(req.eqUnificationCode instanceof Array ? req.eqUnificationCode : []), ...(req.neUnificationCode instanceof Array ? req.neUnificationCode : [])].filter((v) => v).length ? { unificationCodeFilterRelation: req.unificationCodeFilterRelation === "OR" ? "OR" : "AND", includeUnificationCode: req.includeUnificationCode instanceof Array && req.includeUnificationCode.length ? req.includeUnificationCode.join(",") : void 0, excludeUnificationCode: req.excludeUnificationCode instanceof Array && req.excludeUnificationCode.length ? req.excludeUnificationCode.join(",") : void 0, eqUnificationCode: req.eqUnificationCode instanceof Array && req.eqUnificationCode.length ? req.eqUnificationCode.join(",") : void 0, neUnificationCode: req.neUnificationCode instanceof Array && req.neUnificationCode.length ? req.neUnificationCode.join(",") : void 0 } : {}),

          ...([...(req.includeKebaoCode instanceof Array ? req.includeKebaoCode : []), ...(req.excludeKebaoCode instanceof Array ? req.excludeKebaoCode : []), ...(req.eqKebaoCode instanceof Array ? req.eqKebaoCode : []), ...(req.neKebaoCode instanceof Array ? req.neKebaoCode : [])].filter((v) => v).length ? { kebaoCodeFilterRelation: req.kebaoCodeFilterRelation === "OR" ? "OR" : "AND", includeKebaoCode: req.includeKebaoCode instanceof Array && req.includeKebaoCode.length ? req.includeKebaoCode.join(",") : void 0, excludeKebaoCode: req.excludeKebaoCode instanceof Array && req.excludeKebaoCode.length ? req.excludeKebaoCode.join(",") : void 0, eqKebaoCode: req.eqKebaoCode instanceof Array && req.eqKebaoCode.length ? req.eqKebaoCode.join(",") : void 0, neKebaoCode: req.neKebaoCode instanceof Array && req.neKebaoCode.length ? req.neKebaoCode.join(",") : void 0 } : {}),

          chargePerson: req.chargePerson || void 0,
          processors: req.processors || void 0,

          ...([...(req.includeChargePerson instanceof Array ? req.includeChargePerson : []), ...(req.excludeChargePerson instanceof Array ? req.excludeChargePerson : []), ...(req.eqChargePerson instanceof Array ? req.eqChargePerson : []), ...(req.neChargePerson instanceof Array ? req.neChargePerson : [])].filter((v) => v).length ? { chargePersonFilterRelation: req.chargePersonFilterRelation === "OR" ? "OR" : "AND", includeChargePerson: req.includeChargePerson instanceof Array && req.includeChargePerson.length ? req.includeChargePerson.join(",") : void 0, excludeChargePerson: req.excludeChargePerson instanceof Array && req.excludeChargePerson.length ? req.excludeChargePerson.join(",") : void 0, eqChargePerson: req.eqChargePerson instanceof Array && req.eqChargePerson.length ? req.eqChargePerson.join(",") : void 0, neChargePerson: req.neChargePerson instanceof Array && req.neChargePerson.length ? req.neChargePerson.join(",") : void 0 } : {}),

          ...([...(req.includeProcessors instanceof Array ? req.includeProcessors : []), ...(req.excludeProcessors instanceof Array ? req.excludeProcessors : []), ...(req.eqProcessors instanceof Array ? req.eqProcessors : []), ...(req.neProcessors instanceof Array ? req.neProcessors : [])].filter((v) => v).length ? { processorsFilterRelation: req.processorsFilterRelation === "OR" ? "OR" : "AND", includeProcessors: req.includeProcessors instanceof Array && req.includeProcessors.length ? req.includeProcessors.join(",") : void 0, excludeProcessors: req.excludeProcessors instanceof Array && req.excludeProcessors.length ? req.excludeProcessors.join(",") : void 0, eqProcessors: req.eqProcessors instanceof Array && req.eqProcessors.length ? req.eqProcessors.join(",") : void 0, neProcessors: req.neProcessors instanceof Array && req.neProcessors.length ? req.neProcessors.join(",") : void 0 } : {}),
        };

        return $req;
      })
      .then(($req) => request<never, Response<QrcodeItem[]>>($req)),
    { controller }
  );
}

export function setQrcode(data: {} & RequestBase) {
  return request<unknown, Response<QrcodeItem>>({
    url: `${SERVER.EVENT_CENTER}/qrcode/updata`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["id", "status", "kebaoCode", "reason", "dispose"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function getQrcodeItem(data: {} & RequestBase) {
  return request<unknown, Response<QrcodeItem>>({
    url: `${SERVER.EVENT_CENTER}/qrcode/queryOne`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["id"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function getQrcodeCount(data: {} & RequestBase) {
  return request<unknown, Response<QrcodeCountData>>({
    url: `${SERVER.EVENT_CENTER}/qrcode/queryCount?type=${data.type}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
