import { SERVER, Method, type Response, type RequestBase, bindSearchParams, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

import { priority, priorityOption } from "./eventPriority";
export { priority, priorityOption };
import { eventSeverityOption, eventSeverity } from "./event";
export { eventSeverityOption, eventSeverity };
import { deviceImportance, deviceImportanceOption } from "./device";
export { deviceImportance, deviceImportanceOption };

import { i18n } from "@/lang/index";

const { t } = i18n.global;

/**
 * 基本分页字段
 */
interface PageFilter {
  paging: { pageNumber: number; pageSize: number };
  sort: string[];
}
/**
 * 基本时间字段
 */
interface DateRangeFilter {
  /** 创建时间起始时间 */
  createTimeStart: string;
  /** 创建时间结束时间 */
  createTimeEnd: string;
  /** 修改时间起始时间 */
  updateTimeStart: string;
  /** 修改时间结束时间 */
  updateTimeEnd: string;
  /** 紧凑模式下时间筛选-起始时间 */
  compactTimeStart: string;
  /** 紧凑模式下时间筛选-结束时间 */
  compactTimeEnd: string;
}
/**
 * 类型校验合并
 */
type Merge<T1 extends object, T2 extends object, MT = T1 & T2> = { [MK in keyof MT]: MT[MK] };
/**
 * 条件过滤器方法
 */
type ConditionFilter<N extends string, TT extends string, F extends string, TE extends string, OT = { [P in N extends `${infer F}${infer E}` ? `${TT}${Uppercase<F>}${E}` : never]: string[] } & { [P in F extends `${infer F}${infer E}` ? `${N}${Uppercase<F>}${E}` : never]: TE }> = { [P in keyof OT]: OT[P] };
// /**
//  * 类型校对方法
//  */
// type HasConditionFilter<Diff0 extends object, Diff1 extends object> = Merge<Record<keyof Omit<Diff0, keyof Diff1>, 0>, Record<keyof Omit<Diff1, keyof Diff0>, 1>>;

export enum questionState {
  NOT_STARTED = "NOT_STARTED",
  /**处理中 */
  PROCESSING = "PROCESSING",
  /**完成 */
  COMPLETED = "COMPLETED",
  /**自动关闭 */
  AUTO_CLOSED = "AUTO_CLOSED",
  /**关闭 */
  CLOSED = "CLOSED",
  /**挂起 */
  SUSPENDED = "SUSPENDED",
  /**待审批 */
  PENDING_APPROVAL = "PENDING_APPROVAL",
  /**新建 */
  NEW = "NEW",
}

export const questionStateOption: { label: string; value: keyof typeof questionState; color?: string; type?: undefined | "success" | "warning" | "info" | "danger" }[] = [
  { label: t("event.新建"), value: questionState.NEW, color: "#ED4013", type: "danger" },
  // { label: "未开启", value: questionState.NOT_STARTED, color: "#ED4013", type: void 0 },
  { label: t("event.处理中"), value: questionState.PROCESSING, color: "#2CB6F4", type: "success" },
  { label: t("event.完成"), value: questionState.COMPLETED, color: "#3EBE6B", type: void 0 },
  { label: t("event.自动关闭"), value: questionState.AUTO_CLOSED, color: "#3EBE6B", type: void 0 },
  { label: t("event.关闭"), value: questionState.CLOSED, color: "#3EBE6B", type: void 0 },

  // { label: "挂起", value: questionState.SUSPENDED, color: "#", type: "warning" },
  // { label: "待审批", value: questionState.PENDING_APPROVAL, color: "#", type: "warning" },
];

export enum questionOperation {
  TAKE_OVER = "TAKE_OVER",
  DELIVER_TO = "DELIVER_TO",
  FINISHED = "FINISHED",
  CLOSE = "CLOSE",
}

export const questionOperationOption = [
  { label: "处理", value: questionOperation.TAKE_OVER },
  { label: "转交", value: questionOperation.DELIVER_TO },
  { label: "完成", value: questionOperation.FINISHED },
  { label: "关闭", value: questionOperation.CLOSE },
];

/**
 * @description 问题
 */
export interface QuestionItem {
  /** ID */
  id: /* Integer */ string;
  /** 工单编号 */
  identifier: string;
  /** 工单类型 */
  orderType: /* 枚举: EVENT_ORDER :事件单 | SERVICE_REQUEST :服务请求 | CHANGE :变更 | QUESTION :问题 | PUBLISH :发布 */ import("./association").OrderType.QUESTION;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 租户名称 */
  tenantName?: string;
  /** 租户缩写 */
  tenantAbbreviation?: string;
  /** 问题类型 */
  questionState: /* 枚举: NEW :新建 | PROCESSING :处理中 | COMPLETED :已完成 | AUTO_CLOSED :自动关闭 | CLOSED :关闭 */ questionState;
  /** 优先级 */
  priority: string;
  /** 告警数量 */
  alertNumber: /* Integer */ string;
  /** 摘要 */
  digest: string;
  /** 负责人id */
  responsibleId: /* Integer */ string;
  /** 负责人 */
  responsibleName: string;
  /** 处理人 id */
  actorId: /* Integer */ string;
  /** 处理人 */
  actorName: string;
  /** 用户组ID */
  userGroupId: /* Integer */ string;
  /** 用户组名称 */
  userGroupName: string;
  /** 回显展示用户组id 当转交到用户时，需要保存用户所属用户组id，用于回显 */
  displayUserGroupId: /* Integer */ string;
  /** 自动关闭时间标识 */
  autoCloseTimeFlag: boolean;
  /** 外部ID */
  externalId: string;
  /** 详述 */
  description: string;
  /** 完结代码信息 */
  completeInfo: { finishCodeName: /** 完结代码名称 */ string; finishCodeDesc: /** 完结代码描述 */ string; finishContent: /** 完结内容 */ string };
  /** 重要性 */
  importance: /* 枚举: High :高 | Medium :中 | Low :低 | None :无 | Unknown :未知 */ deviceImportance;
  /** 紧急性 */
  severity: /* 枚举: Critical :Critical | Major :Major | Minor :Minor | Warning :Warning | Unknown :Unknown | Normal :Normal | Informational :Informational | Calculating :Calculating | Symptom :Symptom | Monitoring :Monitoring | Others :在映射中展示Critical -- Monitoring, */ eventSeverity;
  /** 工单影响性 */
  influence: /* 枚举: High :High | Medium :Medium | Low :Low | None :None | Unknown :Unknown */ deviceImportance;
  /** 工单紧急性 */
  urgency: /* 枚举: High :High | Medium :Medium | Low :Low | None :None | Unknown :Unknown */ deviceImportance;
  /** 联系人列表 */
  contacts: { contactId: /** 联系人id */ string; contactType: /** 联系人类型 */ "Notification" | "Technical" | "OnSite" }[];
  /** 设备id列表 */
  deviceIds: string[];
  /** 创建时间 */
  createTime: /* Integer */ string;
  /** 修改时间 */
  updateTime: /* Integer */ string;
  /** 创建人 */
  creatorName: string;
  /** 更新人 */
  updaterName: string;
  /** 操作 */
  operation: /* 枚举: TAKE_OVER :处理 | DELIVER_TO :转交 | FINISHED :完结 | CLOSE :关闭 */ questionOperation;

  draft: boolean;

  verifyPermissionIds: string[];
}

export function questionCreate(data: { name: string; description: string; priority: keyof typeof priority } & RequestBase) {
  return request<unknown, Response<QuestionItem>>({
    url: `${SERVER.EVENT_CENTER}/question/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
    data: ["name", "description", "priority", "ticketSubtype", "ticketTemplateId", "ticketClassificationId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id, permissionId: "612917559993303040" }),
  });
}

export function getQuestionState /* 统计模块 */(data: { id?: string[] } & RequestBase) {
  return request<unknown, Response<{ questionState: questionState; count: number }[]>>({
    url: `${SERVER.EVENT_CENTER}/question/state/allCount`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { permissionId: "612917559993303040" }),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function getQuestionItems(data: { pageNumber: number; pageSize: number } & RequestBase) {
  const params = new URLSearchParams({ pageNumber: String(data.pageNumber || 0), pageSize: String(data.pageSize || 0) });
  (data.sort instanceof Array ? data.sort : []).forEach((v) => params.append("sort", v));
  bindSearchParams({ identifier: data.identifier, questionState: data.questionState, digest: data.digest, priority: data.priority, tenantId: data.tenantId, responsibleName: data.responsibleName, actorName: data.actorName, permissionId: "612917559993303040" }, params);
  bindSearchParams({ createTimeStart: ((data.createTime as Record<string, string>) || {}).start, createTimeEnd: ((data.createTime as Record<string, string>) || {}).end }, params);
  bindSearchParams({ updateTimeStart: ((data.updateTime as Record<string, string>) || {}).start, updateTimeEnd: ((data.updateTime as Record<string, string>) || {}).end }, params);

  return request<unknown, Response<QuestionItem[]>>({
    url: `${SERVER.EVENT_CENTER}/question/allList`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params,
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

interface QuestionQuery {
  /** 工单号 */
  identifier?: string;
  /** 状态NEW :新建PROCESSING :处理中COMPLETED :已完成AUTO_CLOSED :自动关闭CLOSED :关闭 */
  questionState?: string;
  /** 摘要 */
  digest?: string;
  /** 优先级（可以多选，多个用逗号分隔） */
  priority?: string;
  state?: string;
  /** 租户ID */
  tenantId?: string;
  /** 租户名称 */
  tenantName?: string;
  /** 负责人 */
  responsibleName?: string;
  /** 当前处理人 */
  actorName?: string;

  boardOrNot?: boolean;
  userId?: string;

  permissionId?: string;

  type: string;
}
/**
 * @description 分页展示问题列表
 * @url http://*************:3000/project/17/interface/api/2993
 */
export async function getQuestionList(req: Record<string, any> & QuestionQuery & PageFilter & DateRangeFilter & Merge<ConditionFilter<"tenantName" | "orderId" | "orderSummary" | "state" | "actorName" | "responsibleName", "include" | "exclude" | "eq" | "ne", "FilterRelation", "AND" | "OR">, ConditionFilter<"alarmCount", "eq" | "ne" | "ge" | "gt" | "le" | "lt" | "isNull" | "isNotNull", "FilterRelation", "AND" | "OR">> & ConditionFilter<"compactActorName", "eq" | "ne" | "include" | "exclude", "FilterRelation", "AND" | "OR">) {
  const controller = new AbortController();

  const userInfo = (await import("@/utils/getUserInfo")).default();
  const { 智能事件中心_客户_工单可读, 智能事件中心_项目_工单可读, 智能事件中心_联系人_工单可读, 智能事件中心_设备_工单可读 } = await import("@/views/pages/permission");

  let urlFlag: "all" | "permission" = "all";
  let url;
  let method;

  if (userInfo.hasPermission(智能事件中心_客户_工单可读)) {
    urlFlag = "all";
    const type = req.type === "allList" ? "allList" : "myList";
    url = `${SERVER.EVENT_CENTER}/question/${type}`;
    method = Method.Get;
  } else if (
    /*  */
    (userInfo.hasPermission("756061441173225472" as any) && userInfo.hasPermission(智能事件中心_项目_工单可读)) ||
    (userInfo.hasPermission("756062477225033728" as any) && userInfo.hasPermission(智能事件中心_联系人_工单可读)) ||
    (userInfo.hasPermission("756062918394511360" as any) && userInfo.hasPermission(智能事件中心_设备_工单可读))
  ) {
    urlFlag = "permission";
    const type = req.type === "allList" ? "allQuestList" : "myQuestList";
    url = `${SERVER.EVENT_CENTER}/question/${type}`;
    method = Method.Post;
  }

  if (!url) return { success: true, data: [], message: "" };

  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url, method, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();

        const query = {
          identifier: req.identifier /* 工单号 */,
          questionState: !req.state ? req.questionState : void 0 /* 状态NEW :新建PROCESSING :处理中COMPLETED :已完成AUTO_CLOSED :自动关闭CLOSED :关闭 */,
          digest: req.digest /* 摘要 */,
          priority: req.priority /* 优先级（可以多选，多个用逗号分隔） */,
          queryType: req.queryType /* 工单参数 */,
          state: req.state,
          tenantId: req.tenantId /* 租户ID */,
          tenantName: req.tenantName /* 租户名称 */,
          createTimeStart: req.createTimeStart /* 创建时间起始时间 */,
          createTimeEnd: req.createTimeEnd /* 创建时间结束时间 */,
          updateTimeStart: req.updateTimeStart /* 修改时间起始时间 */,
          updateTimeEnd: req.updateTimeEnd /* 修改时间结束时间 */,
          responsibleName: req.responsibleName /* 负责人 */,
          actorName: req.actorName /* 当前处理人 */,

          /* 创建时间 */
          ...(req.createTimeStart && req.createTimeEnd ? { createTimeStart: req.createTimeStart, createTimeEnd: req.createTimeEnd } : {}),

          /* 修改时间 */
          ...(req.updateTimeStart && req.updateTimeEnd ? { updateTimeStart: req.updateTimeStart, updateTimeEnd: req.updateTimeEnd } : {}),

          /* 创建时间AND修改时间 */
          ...(req.compactTimeStart && req.compactTimeEnd ? { compactTimeStart: req.compactTimeStart, compactTimeEnd: req.compactTimeEnd } : {}),

          /* 租户名称 */
          ...([...(req.includeTenantName instanceof Array ? req.includeTenantName : []), ...(req.excludeTenantName instanceof Array ? req.excludeTenantName : []), ...(req.eqTenantName instanceof Array ? req.eqTenantName : []), ...(req.neTenantName instanceof Array ? req.neTenantName : [])].filter((v) => v).length ? { tenantNameFilterRelation: req.tenantNameFilterRelation === "OR" ? "OR" : "AND", includeTenantName: req.includeTenantName instanceof Array && req.includeTenantName.length ? req.includeTenantName.join(",") : void 0, excludeTenantName: req.excludeTenantName instanceof Array && req.excludeTenantName.length ? req.excludeTenantName.join(",") : void 0, eqTenantName: req.eqTenantName instanceof Array && req.eqTenantName.length ? req.eqTenantName.join(",") : void 0, neTenantName: req.neTenantName instanceof Array && req.neTenantName.length ? req.neTenantName.join(",") : void 0 } : {}),

          /* 工单号 */
          ...([...(req.includeOrderId instanceof Array ? req.includeOrderId : []), ...(req.excludeOrderId instanceof Array ? req.excludeOrderId : []), ...(req.eqOrderId instanceof Array ? req.eqOrderId : []), ...(req.neOrderId instanceof Array ? req.neOrderId : [])].filter((v) => v).length ? { orderIdFilterRelation: req.orderIdFilterRelation === "OR" ? "OR" : "AND", includeOrderIds: req.includeOrderId instanceof Array && req.includeOrderId.length ? req.includeOrderId.join(",") : void 0, excludeOrderIds: req.excludeOrderId instanceof Array && req.excludeOrderId.length ? req.excludeOrderId.join(",") : void 0, eqOrderIds: req.eqOrderId instanceof Array && req.eqOrderId.length ? req.eqOrderId.join(",") : void 0, neOrderIds: req.neOrderId instanceof Array && req.neOrderId.length ? req.neOrderId.join(",") : void 0 } : {}),

          /* 工单摘要 */
          ...([...(req.includeOrderSummary instanceof Array ? req.includeOrderSummary : []), ...(req.excludeOrderSummary instanceof Array ? req.excludeOrderSummary : []), ...(req.eqOrderSummary instanceof Array ? req.eqOrderSummary : []), ...(req.neOrderSummary instanceof Array ? req.neOrderSummary : [])].filter((v) => v).length ? { orderSummaryFilterRelation: req.orderSummaryFilterRelation === "OR" ? "OR" : "AND", includeOrderSummary: req.includeOrderSummary instanceof Array && req.includeOrderSummary.length ? req.includeOrderSummary.join(",") : void 0, excludeOrderSummary: req.excludeOrderSummary instanceof Array && req.excludeOrderSummary.length ? req.excludeOrderSummary.join(",") : void 0, eqOrderSummary: req.eqOrderSummary instanceof Array && req.eqOrderSummary.length ? req.eqOrderSummary.join(",") : void 0, neOrderSummary: req.neOrderSummary instanceof Array && req.neOrderSummary.length ? req.neOrderSummary.join(",") : void 0 } : {}),
          // 外部id
          ...([...(req.includeExternalId instanceof Array ? req.includeExternalId : []), ...(req.excludeExternalId instanceof Array ? req.excludeExternalId : []), ...(req.eqExternalId instanceof Array ? req.eqExternalId : []), ...(req.neExternalId instanceof Array ? req.neExternalId : [])].filter((v) => v).length ? { externalIdFilterRelation: req.externalIdFilterRelation === "OR" ? "OR" : "AND", includeExternalId: req.includeExternalId instanceof Array && req.includeExternalId.length ? req.includeExternalId.join(",") : void 0, excludeExternalId: req.excludeExternalId instanceof Array && req.excludeExternalId.length ? req.excludeExternalId.join(",") : void 0, eqExternalId: req.eqExternalId instanceof Array && req.eqExternalId.length ? req.eqExternalId.join(",") : void 0, neExternalId: req.neExternalId instanceof Array && req.neExternalId.length ? req.neExternalId.join(",") : void 0 } : {}),

          /* 状态 */
          ...([...(req.includeState instanceof Array ? req.includeState : []), ...(req.excludeState instanceof Array ? req.excludeState : []), ...(req.eqState instanceof Array ? req.eqState : []), ...(req.neState instanceof Array ? req.neState : [])].filter((v) => v).length ? { stateFilterRelation: req.stateFilterRelation === "OR" ? "OR" : "AND", includeStates: req.includeState instanceof Array && req.includeState.length ? req.includeState.join(",") : void 0, excludeStates: req.excludeState instanceof Array && req.excludeState.length ? req.excludeState.join(",") : void 0, eqStates: req.eqState instanceof Array && req.eqState.length ? req.eqState.join(",") : void 0, neStates: req.neState instanceof Array && req.neState.length ? req.neState.join(",") : void 0 } : {}),

          /* 告警数 */
          ...([...(req.eqAlarmCount instanceof Array ? req.eqAlarmCount : []), ...(req.neAlarmCount instanceof Array ? req.neAlarmCount : []), ...(req.geAlarmCount instanceof Array ? req.geAlarmCount : []), ...(req.gtAlarmCount instanceof Array ? req.gtAlarmCount : []), ...(req.leAlarmCount instanceof Array ? req.leAlarmCount : []), ...(req.ltAlarmCount instanceof Array ? req.ltAlarmCount : []), ...(req.isNullAlarmCount instanceof Array ? req.isNullAlarmCount : []), ...(req.isNotNullAlarmCount instanceof Array ? req.isNotNullAlarmCount : [])].filter((v) => v).length ? { alarmCountFilterRelation: req.alarmCountFilterRelation === "OR" ? "OR" : "AND", eqAlarmCount: req.eqAlarmCount instanceof Array && req.eqAlarmCount.length ? req.eqAlarmCount.join(",") : void 0, neAlarmCount: req.neAlarmCount instanceof Array && req.neAlarmCount.length ? req.neAlarmCount.join(",") : void 0, geAlarmCount: req.geAlarmCount instanceof Array && req.geAlarmCount.length ? req.geAlarmCount.join(",") : void 0, gtAlarmCount: req.gtAlarmCount instanceof Array && req.gtAlarmCount.length ? req.gtAlarmCount.join(",") : void 0, leAlarmCount: req.leAlarmCount instanceof Array && req.leAlarmCount.length ? req.leAlarmCount.join(",") : void 0, ltAlarmCount: req.ltAlarmCount instanceof Array && req.ltAlarmCount.length ? req.ltAlarmCount.join(",") : void 0, isNullAlarmCount: req.isNullAlarmCount instanceof Array && req.isNullAlarmCount.length ? req.isNullAlarmCount.join(",") : void 0, isNotNullAlarmCount: req.isNotNullAlarmCount instanceof Array && req.isNotNullAlarmCount.length ? req.isNotNullAlarmCount.join(",") : void 0 } : {}),

          /* 处理人 */
          ...([...(req.includeActorName instanceof Array ? req.includeActorName : []), ...(req.excludeActorName instanceof Array ? req.excludeActorName : []), ...(req.eqActorName instanceof Array ? req.eqActorName : []), ...(req.neActorName instanceof Array ? req.neActorName : [])].filter((v) => v).length ? { actorNameFilterRelation: req.actorNameFilterRelation === "OR" ? "OR" : "AND", includeActorName: req.includeActorName instanceof Array && req.includeActorName.length ? req.includeActorName.join(",") : void 0, excludeActorName: req.excludeActorName instanceof Array && req.excludeActorName.length ? req.excludeActorName.join(",") : void 0, eqActorName: req.eqActorName instanceof Array && req.eqActorName.length ? req.eqActorName.join(",") : void 0, neActorName: req.neActorName instanceof Array && req.neActorName.length ? req.neActorName.join(",") : void 0 } : {}),

          /* 负责人 */
          ...([...(req.includeResponsibleName instanceof Array ? req.includeResponsibleName : []), ...(req.excludeResponsibleName instanceof Array ? req.excludeResponsibleName : []), ...(req.eqResponsibleName instanceof Array ? req.eqResponsibleName : []), ...(req.neResponsibleName instanceof Array ? req.neResponsibleName : [])].filter((v) => v).length ? { responsibleNameFilterRelation: req.responsibleNameFilterRelation === "OR" ? "OR" : "AND", includeResponsibleName: req.includeResponsibleName instanceof Array && req.includeResponsibleName.length ? req.includeResponsibleName.join(",") : void 0, excludeResponsibleName: req.excludeResponsibleName instanceof Array && req.excludeResponsibleName.length ? req.excludeResponsibleName.join(",") : void 0, eqResponsibleName: req.eqResponsibleName instanceof Array && req.eqResponsibleName.length ? req.eqResponsibleName.join(",") : void 0, neResponsibleName: req.neResponsibleName instanceof Array && req.neResponsibleName.length ? req.neResponsibleName.join(",") : void 0 } : {}),

          /* 负责人AND处理人 */
          ...([...(req.eqCompactActorName instanceof Array ? req.eqCompactActorName : []), ...(req.neCompactActorName instanceof Array ? req.neCompactActorName : []), ...(req.includeCompactActorName instanceof Array ? req.includeCompactActorName : []), ...(req.excludeCompactActorName instanceof Array ? req.excludeCompactActorName : [])].filter((v) => v).length ? { compactActorNameFilterRelation: req.compactActorNameFilterRelation === "OR" ? "OR" : "AND", compactEqActorName: req.eqCompactActorName instanceof Array && req.eqCompactActorName.length ? req.eqCompactActorName.join(",") : void 0, compactNeActorName: req.neCompactActorName instanceof Array && req.neCompactActorName.length ? req.neCompactActorName.join(",") : void 0, compactIncludeActorName: req.includeCompactActorName instanceof Array && req.includeCompactActorName.length ? req.includeCompactActorName.join(",") : void 0, compactExcludeActorName: req.excludeCompactActorName instanceof Array && req.excludeCompactActorName.length ? req.excludeCompactActorName.join(",") : void 0 } : {}),

          ...([...(req.eqTicketGroupName instanceof Array ? req.eqTicketGroupName : []), ...(req.neTicketGroupName instanceof Array ? req.neTicketGroupName : []), ...(req.inTicketGroupName instanceof Array ? req.inTicketGroupName : []), ...(req.excludeTicketGroupName instanceof Array ? req.excludeTicketGroupName : [])].filter((v) => v).length ? { ticketGroupNameFilterRelation: req.ticketGroupNameFilterRelation === "OR" ? "OR" : "AND", eqTicketGroupName: req.eqTicketGroupName instanceof Array && req.eqTicketGroupName.length ? req.eqTicketGroupName.join(",") : void 0, neTicketGroupName: req.neTicketGroupName instanceof Array && req.neTicketGroupName.length ? req.neTicketGroupName.join(",") : void 0, inTicketGroupName: req.inTicketGroupName instanceof Array && req.inTicketGroupName.length ? req.inTicketGroupName.join(",") : void 0, excludeTicketGroupName: req.excludeTicketGroupName instanceof Array && req.excludeTicketGroupName.length ? req.excludeTicketGroupName.join(",") : void 0 } : {}),

          ...([...(req.eqUserGroupName instanceof Array ? req.eqUserGroupName : []), ...(req.neUserGroupName instanceof Array ? req.neUserGroupName : []), ...(req.inUserGroupName instanceof Array ? req.inUserGroupName : []), ...(req.excludeUserGroupName instanceof Array ? req.excludeUserGroupName : [])].filter((v) => v).length ? { userGroupNameFilterRelation: req.userGroupNameFilterRelation === "OR" ? "OR" : "AND", eqUserGroupName: req.eqUserGroupName instanceof Array && req.eqUserGroupName.length ? req.eqUserGroupName.join(",") : void 0, neUserGroupName: req.neUserGroupName instanceof Array && req.neUserGroupName.length ? req.neUserGroupName.join(",") : void 0, inUserGroupName: req.inUserGroupName instanceof Array && req.inUserGroupName.length ? req.inUserGroupName.join(",") : void 0, excludeUserGroupName: req.excludeUserGroupName instanceof Array && req.excludeUserGroupName.length ? req.excludeUserGroupName.join(",") : void 0 } : {}),

          ...([...(req.compactEqTicketName instanceof Array ? req.compactEqTicketName : []), ...(req.compactNeTicketName instanceof Array ? req.compactNeTicketName : []), ...(req.compactIncludeTicketName instanceof Array ? req.compactIncludeTicketName : []), ...(req.compactExcludeTicketName instanceof Array ? req.compactExcludeTicketName : [])].filter((v) => v).length ? { compactTicketNameFilterRelation: req.compactTicketNameFilterRelation === "OR" ? "OR" : "AND", compactEqTicketName: req.compactEqTicketName instanceof Array && req.compactEqTicketName.length ? req.compactEqTicketName.join(",") : void 0, compactNeTicketName: req.compactNeTicketName instanceof Array && req.compactNeTicketName.length ? req.compactNeTicketName.join(",") : void 0, compactIncludeTicketName: req.compactIncludeTicketName instanceof Array && req.compactIncludeTicketName.length ? req.compactIncludeTicketName.join(",") : void 0, compactExcludeTicketName: req.compactExcludeTicketName instanceof Array && req.compactExcludeTicketName.length ? req.compactExcludeTicketName.join(",") : void 0 } : {}),

          boardOrNot: req.boardOrNot,
          userId: req.userId,
          permissionId: req.permissionId || (await import("@/views/pages/permission")).智能事件中心_客户_工单可读,
        };

        bindParamByObj(
          Object.assign(
            {
              pageNumber: req.paging.pageNumber /* 页码, 默认第一页 */,
              pageSize: req.paging.pageSize /* 页大小, 默认10 */,
              sort: req.sort,
            },
            urlFlag === "all" ? query : {}
          ),
          $req.params
        );
        $req.data =
          urlFlag === "permission"
            ? Object.assign(query, {
                permissionList: [
                  /*  */
                  { permissionId: 智能事件中心_项目_工单可读, permissionType: "View Tickets When Project Has View Tickets" },
                  { permissionId: 智能事件中心_联系人_工单可读, permissionType: "View Tickets When Contact Has View Tickets" },
                  { permissionId: 智能事件中心_设备_工单可读, permissionType: "View Tickets When Device Has View Tickets" },
                ],
              })
            : void 0;
        return $req;
      })
      .then(($req) => request<never, Response<QuestionItem[]>>($req)),
    { controller }
  );
}

export async function getQuestionDetail(data: { id: string } & RequestBase) {
  const {
    /*  */
    智能事件中心_问题工单_可读,
    智能事件中心_问题工单_更新,
    智能事件中心_问题工单_编辑小记,
    智能事件中心_问题工单_分配设备,
    智能事件中心_问题工单_分配联系人,
    智能事件中心_问题工单_关联工单,
  } = await import("@/views/pages/permission");

  return request<unknown, Response<QuestionItem>>({
    url: `${SERVER.EVENT_CENTER}/question/${data.id}/detail`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: Object.assign(
      [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
      {
        queryPermissionId: [智能事件中心_问题工单_可读].join(),
        verifyPermissionIds: [智能事件中心_问题工单_更新, 智能事件中心_问题工单_编辑小记, 智能事件中心_问题工单_分配设备, 智能事件中心_问题工单_分配联系人, 智能事件中心_问题工单_关联工单].join(),
      }
    ),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

/**
 * @name 修改优先级
 * @export
 * @param {({ id: string; priority: priority } & RequestBase)} data
 * @return {*}
 */
export function setQuestionPriority(data: { id: string; priority: priority } & RequestBase) {
  return request<unknown, Response<QuestionItem>>({
    url: `${SERVER.EVENT_CENTER}/question/${data.id}/priority/${data.priority}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}
/**
 * @name 修改重要性
 * @export
 * @param {({ id: string; importance: deviceImportance } & RequestBase)} data
 * @return {*}
 */
export function setQuestionImportance(data: { id: string; importance: deviceImportance } & RequestBase) {
  return request<unknown, Response<QuestionItem>>({
    url: `${SERVER.EVENT_CENTER}/question/${data.id}/importance/${data.importance}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

/**
 * @name 修改紧急性
 * @export
 * @param {({ id: string; severity: eventSeverity } & RequestBase)} data
 * @return {*}
 */
export function setQuestionSeverity(data: { id: string; severity: eventSeverity } & RequestBase) {
  return request<unknown, Response<QuestionItem>>({
    url: `${SERVER.EVENT_CENTER}/question/${data.id}/severity/${data.severity}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: new URLSearchParams([].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {})),
  });
}

/**
 * @name 编辑详述
 * @export
 * @param {({ id: string; description: string; externalId: string } & RequestBase)} data
 * @return {*}
 */
export function setQuestionDataByDescription(data: { id: string; description: string; externalId: string } & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/question/updateDesc`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["id", "description", "externalId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export interface QuestionNote {
  noteId: string;
  eventId: string;
  noteContent: string;
  attachmentList: {
    attachmentId: string;
    attachmentName: string;
    attachmentUrl: string;
    attachmentKey: string;
  }[];
  noteCreateTime: string;
  noteCreateUserId: string;
  noteCreateUserName: string;
  noteCreateUserProfilePicture: string;
}

/**
 * @name 获取小记
 * @export
 * @param {({ id: string } & RequestBase)} data
 * @return {*}
 */
export function getQuestionNotes(data: { id: string } & RequestBase) {
  return request<unknown, Response<QuestionNote[]>>({
    url: `${SERVER.EVENT_CENTER}/question/${data.id}/queryNote`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 保存小记
 * @export
 * @param {({ eventId: string; content: string; attachmentList: { attachmentId: string; attachmentName: string; attachmentUrl: string; attachmentKey: string } } & RequestBase)} data
 */
export function addQuestionNote(data: { eventId: string; tenantId: string; privateAble?: string; privateCustomerId: string; content: string; attachmentList: { attachmentId: string; attachmentName: string; attachmentUrl: string; attachmentKey: string }[] } & RequestBase) {
  return request<unknown, Response<QuestionNote>>({
    url: `${SERVER.EVENT_CENTER}/question/note`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["eventId", "content", "attachmentList", "tenantId", "privateAble", "privateCustomerId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function setQuestionNote(data: { eventId: string; tenantId: string; privateAble?: string; privateCustomerId: string; noteId: string; content: string; attachmentList: { attachmentId: string; attachmentName: string; attachmentUrl: string; attachmentKey: string }[] } & RequestBase) {
  return request<unknown, Response<QuestionNote>>({
    url: `${SERVER.EVENT_CENTER}/question/editNote`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["eventId", "content", "attachmentList", "noteId", "tenantId", "privateAble", "privateCustomerId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 删除小记
 * @export
 * @param {({ id: string } & RequestBase)} data
 * @return {*}
 */
export function delQuestionNote(data: { id: string } & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/question/${data.id}/deleteNote`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 添加设备
 * @export
 * @param {({ id: string; deviceIds: string } & RequestBase)} data
 * @return {*}
 */
export function addQuestionDevice(data: { id: string; deviceIds: string; autoAllocateContact: boolean } & RequestBase) {
  return request<unknown, Response<QuestionItem>>({
    url: `${SERVER.EVENT_CENTER}/question/${data.id}/addDevice/${data.deviceIds}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["autoAllocateContact"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 移除设备
f * @export
 * @param {({ id: string; deviceId: string } & RequestBase)} data
 * @return {*}
 */
export function delQuestionDevice(data: { id: string; deviceId: string } & RequestBase) {
  return request<unknown, Response<QuestionItem>>({
    url: `${SERVER.EVENT_CENTER}/question/${data.id}/removeDevice/${data.deviceId}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 添加联系人
 * @export
 * @param {({ id: string; contactType: string; contactId: string } & RequestBase)} data
 * @return {*}
 */
export function addQuestionContact(data: { id: string; contactType: string; contactIds: string[] } & RequestBase) {
  return request<unknown, Response<QuestionItem>>({
    url: `${SERVER.EVENT_CENTER}/question/${data.id}/addContact`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["contactType", "contactIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 移除联系人
 * @export
 * @param {({ id: string; contactType: string; contactId: string } & RequestBase)} data
 * @return {*}
 */
export function delQuestionContact(data: { id: string; contactType: string; contactIds: string[] } & RequestBase) {
  return request<unknown, Response<QuestionItem>>({
    url: `${SERVER.EVENT_CENTER}/question/${data.id}/removeContact`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["contactType", "contactIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 处理
 * @export
 * @param {({id: string} & RequestBase)} data
 * @return {*}
 */
export function setQuestionStateProcessing(data: { id: string } & RequestBase) {
  return request<unknown, Response<QuestionItem>>({
    url: `${SERVER.EVENT_CENTER}/question/${data.id}/handle`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function setQuestionStateCompleted(data: { id: string; completeInfo: { finishCodeName: string; finishCodeDesc: string; finishContent: string } } & RequestBase) {
  return request<unknown, Response<QuestionItem>>({
    url: `${SERVER.EVENT_CENTER}/question/complete`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["id", "completeInfo"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function setQuestionStateClose(data: { id: string; completeInfo: { finishCodeName: string; finishCodeDesc: string; finishContent: string } } & RequestBase) {
  return request<unknown, Response<QuestionItem>>({
    url: `${SERVER.EVENT_CENTER}/question/close`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["id", "completeInfo"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function questionTransfer(data: { id: string; userGroupId: string; userId: string } & RequestBase) {
  return request<unknown, Response<QuestionItem>>({
    url: `${SERVER.EVENT_CENTER}/question/transfer`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["id", "userGroupId", "userId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export interface TabCount {
  noteCount: string /* 小记数量 */;
  deviceCount: string /* 设备数量 */;
  contactCount: string /* 联系人数量 */;
  relationCount: string /* 关联数量 */;
  fileCount: string /* 文件数量 */;
  actionCount: string;
}
export function /* 返回tap统计数量 */ getDetailTapCountById(req: { id: string } & RequestBase) {
  const params = [].reduce((p, key) => Object.assign(p, req[key] === undefined ? {} : { [key]: req[key] }), {});
  const data = [].reduce((p, key) => Object.assign(p, req[key] === undefined ? {} : { [key]: req[key] }), {});
  return request<never, Response<TabCount>>({ url: `${SERVER.EVENT_CENTER}/question/tap/${req.id}/count`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function /* 全量更新问题 */ setQuestionEditable(req: {} & RequestBase) {
  const params = new URLSearchParams({});
  bindSearchParams({}, params);
  const data = { urgency: req.urgency, influence: req.influence, priority: req.priority /* 优先级 */, severity: req.severity /* 紧急性枚举：Critical :Critical、Major :Major、Minor :Minor、Warning :Warning、Unknown :Unknown、Normal :Normal、Informational :Informational、Calculating :Calculating、Symptom :Symptom、Monitoring :Monitoring、Others :在映射中展示Critical -- Monitoring, */, importance: req.importance /* 重要性枚举：HIGH :高、MIDDLE :中、LOW :低、NORMAL :无、UNKNOWN :未知 */, userGroupId: req.userGroupId /* 用户组ID */, userId: req.userId /* 用户ID */, desc: req.desc /* 问题描述 */, externalId: req.externalId /* 外部ID */, digest: req.digest /* 问题摘要 */, operation: req.operation /* 问题操作枚举：TAKE_OVER :处理、DELIVER_TO :转交、FINISHED :完成、CLOSE :关闭 */, completeInfo: req.completeInfo /* 完成代码信息 */ };

  return request<never, Response<QuestionItem>>({ url: `${SERVER.EVENT_CENTER}/question/${req.id /*  */}/updateEditable`, method: Method.Patch, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function getChangeContacts(data: { id: string } & RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/question/${data.id}/getContact`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: {},
  });
}
