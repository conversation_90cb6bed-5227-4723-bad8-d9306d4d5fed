import { SERVER, Method, type Response, type RequestBase, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

/**
 * @description 获取区域树-响应体
 * @url http://*************:3000/project/47/interface/api/23108
 */
export interface RegionsTenant {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 安全容器 */
  containerId: /* Integer */ string;
  /** 父区域Id */
  parentId: /* Integer */ string;
  /** 行动策略列表 */
  supportNoteIds: /* Integer */ string[];
  label?: string;
  /** 区域名称 */
  name: string;
  /** 描述信息 */
  description?: string;
  /** 外部ID */
  externalId?: string;
  /** 纬度 */
  latitude?: string;
  /** 经度 */
  longitude?: string;
  /** 是否激活 */
  active: boolean;
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
  /** 拥有的权限 */
  hasPermissionIds: /* Integer */ string[];
  children: RegionsTenant[];
}

interface RegionsTenantQuery {
  parentId: string;
  label: string;
  name: string;
  description: string;
  externalId: string;
  latitude: string;
  Longitude: string;
  active: boolean;
}
/**
 * @description 为当前租户创建区域
 * @url http://*************:3000/project/47/interface/api/1165
 */
export function /* 为当前租户创建区域 */ createRegionsTenantCurrent(req: { parentId?: string; label?: string; name: string; description?: string; externalId?: string; latitude?: string; longitude?: string; active?: string; containerId?: string } & RequestBase) {
  // const header = new Headers();
  // header.set("Content-Type", "application/json");
  const params = new URLSearchParams({});
  const data = { parentId: req.parentId /* 父区域ID */, label: req.label /*  */, name: req.name /* 区域名 */, description: req.description /* 描述信息 */, externalId: req.externalId /* 外部ID */, latitude: req.latitude /* 纬度 */, longitude: req.longitude /* 经度 */, active: req.active /* 是否激活 */, containerId: req.containerId /* 安全容器 */ };
  return request<never, Response<RegionsTenant>>({ url: `${SERVER.CMDB}/regions/tenant/current`, method: Method.Post, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function editRegionsById(data: RegionsTenantQuery & RequestBase, id: string) {
  return request<never, Response<RegionsTenant>>({
    url: `${SERVER.CMDB}/regions/${id}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * @description 获取当前租户的所有区域
 * @url http://*************:3000/project/47/interface/api/1173
 */
export function getRegionsTenantCurrent(req: { supportNoteId: string; active: string; queryPermissionId?: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/regions/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ supportNoteId: req.supportNoteId, active: req.active }, $req.params);
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_区域_可读, 资产管理中心_区域_新增, 资产管理中心_区域_编辑, 资产管理中心_区域_删除 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [req.queryPermissionId || 资产管理中心_区域_可读].join(","), verifyPermissionIds: [资产管理中心_区域_新增, 资产管理中心_区域_编辑, 资产管理中心_区域_删除].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<RegionsTenant[]>>($req)),
    { controller }
  );
}

export function delRegionsById(data: { id: string } & RequestBase) {
  return request<never, Response<RegionsTenant>>({
    url: `${SERVER.CMDB}/regions/${data.id}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function setRegionsContacts(data: object & RequestBase, id: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/regions/${id}/contacts`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function getRegionsContacts(data: { id: string } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/regions/${data.id}/contacts`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function delRegionsContacts(data: object & RequestBase, id: string) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/regions/${id}/contacts`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

/**
 * @description 获取区域树
 * @url http://*************:3000/project/47/interface/api/23108
 */
export async function getRegionTree(req: Record<string, any> & { pageNumber: string /* 页码, 默认第一页 */; pageSize: string /* 页大小, 默认10 */; sort: string[]; parentId: string; active: string; containerId: string; queryPermissionId: string; verifyPermissionIds: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/regions/2.0/tree/filter`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.pageNumber /* 页码, 默认第一页 */, pageSize: req.pageSize /* 页大小, 默认10 */, sort: req.sort, parentId: req.parentId, active: req.active }, $req.params);

        bindParamByObj(
          {
            ...([...(req.includeLabel instanceof Array ? req.includeLabel : []), ...(req.excludeLabel instanceof Array ? req.excludeLabel : []), ...(req.eqLabel instanceof Array ? req.eqLabel : []), ...(req.neLabel instanceof Array ? req.neLabel : [])].filter((v) => v).length ? { labelFilterRelation: req.labelFilterRelation === "OR" ? "OR" : "AND", includeLabel: req.includeLabel instanceof Array && req.includeLabel.length ? req.includeLabel.join(",") : void 0, excludeLabel: req.excludeLabel instanceof Array && req.excludeLabel.length ? req.excludeLabel.join(",") : void 0, eqLabel: req.eqLabel instanceof Array && req.eqLabel.length ? req.eqLabel.join(",") : void 0, neLabel: req.neLabel instanceof Array && req.neLabel.length ? req.neLabel.join(",") : void 0 } : {}),

            ...([...(req.includeName instanceof Array ? req.includeName : []), ...(req.excludeName instanceof Array ? req.excludeName : []), ...(req.eqName instanceof Array ? req.eqName : []), ...(req.neName instanceof Array ? req.neName : [])].filter((v) => v).length ? { nameFilterRelation: req.nameFilterRelation === "OR" ? "OR" : "AND", includeName: req.includeName instanceof Array && req.includeName.length ? req.includeName.join(",") : void 0, excludeName: req.excludeName instanceof Array && req.excludeName.length ? req.excludeName.join(",") : void 0, eqName: req.eqName instanceof Array && req.eqName.length ? req.eqName.join(",") : void 0, neName: req.neName instanceof Array && req.neName.length ? req.neName.join(",") : void 0 } : {}),

            ...([...(req.includeDescription instanceof Array ? req.includeDescription : []), ...(req.excludeDescription instanceof Array ? req.excludeDescription : []), ...(req.eqDescription instanceof Array ? req.eqDescription : []), ...(req.neDescription instanceof Array ? req.neDescription : [])].filter((v) => v).length ? { descriptionFilterRelation: req.descriptionFilterRelation === "OR" ? "OR" : "AND", includeDescription: req.includeDescription instanceof Array && req.includeDescription.length ? req.includeDescription.join(",") : void 0, excludeDescription: req.excludeDescription instanceof Array && req.excludeDescription.length ? req.excludeDescription.join(",") : void 0, eqDescription: req.eqDescription instanceof Array && req.eqDescription.length ? req.eqDescription.join(",") : void 0, neDescription: req.neDescription instanceof Array && req.neDescription.length ? req.neDescription.join(",") : void 0 } : {}),

            ...([...(req.includeExternalId instanceof Array ? req.includeExternalId : []), ...(req.excludeExternalId instanceof Array ? req.excludeExternalId : []), ...(req.eqExternalId instanceof Array ? req.eqExternalId : []), ...(req.neExternalId instanceof Array ? req.neExternalId : [])].filter((v) => v).length ? { externalIdFilterRelation: req.externalIdFilterRelation === "OR" ? "OR" : "AND", includeExternalId: req.includeExternalId instanceof Array && req.includeExternalId.length ? req.includeExternalId.join(",") : void 0, excludeExternalId: req.excludeExternalId instanceof Array && req.excludeExternalId.length ? req.excludeExternalId.join(",") : void 0, eqExternalId: req.eqExternalId instanceof Array && req.eqExternalId.length ? req.eqExternalId.join(",") : void 0, neExternalId: req.neExternalId instanceof Array && req.neExternalId.length ? req.neExternalId.join(",") : void 0 } : {}),
          },
          $req.params
        );

        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_区域_可读, 资产管理中心_区域_新增, 资产管理中心_区域_编辑, 资产管理中心_区域_删除, 资产管理中心_区域_安全 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          bindParamByObj({ queryPermissionId: [资产管理中心_区域_可读].join(","), verifyPermissionIds: [资产管理中心_区域_新增, 资产管理中心_区域_编辑, 资产管理中心_区域_删除, 资产管理中心_区域_安全].join(",") }, $req.params);
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<RegionsTenant[]>>($req)),
    { controller }
  );
}
