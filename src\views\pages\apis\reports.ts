import { SERVER, Method, bindSearchParams, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";
import type { Zone } from "@/utils/zone";

export interface Reports {
  id: number;
  /** 租户id */
  tenantId: number;
  /** 模板中添加的客户id */
  tenantIds: number[];
  /** 报表名称 */
  name: string;
  /** 报表类型 */
  type: string;
  /** 报表模板 */
  template: string;
  /** 是否启用(状态：true-启用，false-禁用) */
  enable: boolean;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 创建人信息 */
  createdBy?: string;
  /** 更新人信息 */
  updatedBy?: string;
  /** 创建时间 */
  createdTime?: number;
  /** 更新时间 */
  updatedTime?: number;
}
/* TODO: 报表类型 */
export enum reportState {
  MONTH = "MONTH" /* 月报 */,
  WEEK = "WEEK" /* 周报 */,
  CUSTOM = "CUSTOM" /*自定义报表 */,
}
export const reportStateOption: { label: string; value: keyof typeof reportState }[] = [
  { label: "月报", value: reportState.MONTH },
  { label: "周报", value: reportState.WEEK },
  { label: "自定义", value: reportState.CUSTOM },
];

/* TODO: 报表模板 */
export enum reportModel {
  CUSTOMER_MONTH = "CUSTOMER_MONTH" /* 月报 */,
  OPERATING_STATEMENT = "OPERATING_STATEMENT" /* 周报 */,
}
export const reportModelOption: { label: string; value: keyof typeof reportModel }[] = [
  { label: "客户月报", value: reportModel.CUSTOMER_MONTH },
  { label: "运营报表", value: reportModel.OPERATING_STATEMENT },
];
export function getReportList(data: { tenantId?: string; type?: string; template?: string; start?: string; end?: string; pageNumber: number; pageSize: number } & RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/customer_statement`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: ["tenantId", "template", "type", "start", "end", "pageNumber", "pageSize", "sort"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function addReport(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/customer_statement`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: Object.keys(data).reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function editReport(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/customer_statement/${data.id}`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: Object.keys(data).reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function enableReport(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/customer_statement/${data.id}/${data.type}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {},
  });
}

export function delReport(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/customer_statement/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {},
  });
}

export function getTenantReportList(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.IAM}/tenants/batch_get`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {
      tenantIds: data.tenantIds,
    },
  });
}

export function addTenantReport(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/customer_statement/${data.id}/tenant/${data.tenantIds}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {},
  });
}
export function delTenantReport(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/customer_statement/${data.id}/tenant/${data.tenantIds}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {},
  });
}

export function downloadReportFile(data: { file: File } & RequestBase) {
  return request<unknown, unknown>({
    url: `/cc_report/event_center/customer_statement/download_template`,
    method: Method.Get,
    signal: undefined,
    headers: {},
    params: {},
    data: {},
    responseType: "blob",
  });
}

export function downloadTenantReportFile(data: { id: string } & RequestBase) {
  return request<unknown, unknown>({
    url: `/cc_report/event_center/customer_statement/export`,
    method: Method.Get,
    signal: undefined,
    headers: {},
    params: {
      id: data.id,
    },
    data: {},
    responseType: "blob",
  });
}

export function downloadTenantReportFileXlsx(data: { start: string | number; end: string | number } & RequestBase) {
  return request<unknown, unknown>({
    url: `/cc_report/event_center/customer_statement/export_operation_report`,
    method: Method.Get,
    signal: undefined,
    headers: {},
    params: {
      start: data.start,
      end: data.end,
    },
    data: {},
    responseType: "blob",
  });
}

//生成报告
export function generateReport(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/customer_statement/${data.statementId}/generate/${data.tenantId}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {},
  });
}

// 生成报告
// export function generateReport(data: RequestBase) {
//   return new Promise((resolve, reject) => {
//     setTimeout(() => {
//       request<unknown, Response<[]>>({
//         url: `${SERVER.EVENT_CENTER}/customer_statement/${data.statementId}/generate/${data.tenantId}`,
//         method: Method.Patch,
//         responseType: "json",
//         signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//         headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
//         params: {},
//         data: {},
//       })
//         .then((response) => {
//           resolve(response);
//         })
//         .catch((error) => {
//           reject(error);
//         });
//     }, 10000); // 5秒延迟
//   });
// }

//取消报告
export function cancelGenerateReport(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/customer_statement/${data.statementId}/cancel/${data.tenantId}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {},
  });
}
//下载客户报表
export function downloadGenerateReportFile(data: RequestBase) {
  return request<unknown, unknown>({
    url: `/cc_report/event_center/customer_statement/${data.statementId}/export/${data.tenantId}`,
    method: Method.Get,
    signal: undefined,
    headers: {},
    params: {},
    data: {},
    responseType: "blob",
  });
}
