import { SERVER, Method, bindSearchParams, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";
import type { Zone } from "@/utils/zone";
import getUserInfo from "@/utils/getUserInfo";

export interface Reports {
  id: number;
  /** 租户id */
  tenantId: number;
  /** 模板中添加的客户id */
  tenantIds: number[];
  /** 报表名称 */
  name: string;
  /** 报表类型 */
  type: string;
  /** 报表模板 */
  template: string;
  /** 是否启用(状态：true-启用，false-禁用) */
  active: boolean;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 创建人信息 */
  createdBy?: string;
  /** 更新人信息 */
  updatedBy?: string;
  /** 创建时间 */
  createdTime?: number;
  /** 更新时间 */
  updatedTime?: number;
}

// ----------------------------------------------------------Start----- -------------------------------------------------------------------

// 创建固定模板
export function getTemplateReportList(data: { permissionId?: string } & RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/fixed_templates/list`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {},
  });
}
// 激活固定模版
export function getActiveTemplateReport(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/fixed_templates/${data.type}/active/${data.active}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {},
  });
}
//生成模版
export function getFindTemplate(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/customer_statement/find_downloadList`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {
      id: data.id,
      containerId: (getUserInfo().currentTenant || {}).containerId,
      queryPermissionId: "509623239069138944",
      verifyPermissionIds: "509623262574018560,512856142523662336,612810505014214656",
      permissionList: [
        {
          permissionId: "734941934316945408",
          permissionType: "View Tickets When Project Has View Tickets",
        },
        {
          permissionId: "734941679332622336",
          permissionType: "View Tickets When Contact Has View Tickets",
        },
        {
          permissionId: "734941620490731520",
          permissionType: "View Tickets When Device Has View Tickets",
        },
      ],
      hasCustomerReadPermission: data.hasCustomerReadPermission,
    },
  });
}
//生成报告
export function getMarkFindTemplate(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/customer_statement/make_downloadList/${data.id}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {},
  });
}
// 生成固定模板
export function generateTemplateReport(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/customer_statement/create_downloadList`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: Object.keys(data).reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
// new下载列表
export function downloadListTemplateReportList(data: { statementId?: string; tenantIds?: string } & RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/customer_statement/find_downloadAll`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {},
  });
}
//删除下载列表
export function delTemplateReportList(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/customer_statement/delete?id=${data.id}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {},
  });
}
// 下载 ----下载列表
export function downTemplateReportFile(data: {} & RequestBase) {
  return request<unknown, unknown>({
    url: `/cc_report/event_center/customer_statement/${data.statementId}/exportValue/${data.tenantId}/${data.reportId}/${data.reportName}`,
    method: Method.Get,
    signal: undefined,
    headers: {},
    params: {},
    data: {},
    responseType: "blob",
  });
}
//分页查询下载列表
export function getdownloadListTemplate(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/customer_statement/find_downloadPage`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
// 分页查询自定义报告

export function getReportsCustomerList(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<never, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/report/customer_report/query`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//新增自定义报告
export function AddReportsCustomer(data: { name: string; desc?: string } & RequestBase) {
  return request<never, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/report/customer_report/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//编辑自定义报告
export function editReportsCustomer(data: { id: string } & RequestBase) {
  return request<never, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/report/customer_report/${data.id}/update`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//激活自定义模板

export function enableCustomerReport(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/report/customer_report/${data.id}/${data.type}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {},
  });
}

//删除自定义报告
export function delReportsCustomerList(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/report/customer_report/${data.id}/delete`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {},
  });
}
//查询父类
export function getReportsCustomerServiceList(data: RequestBase) {
  return request<never, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/report/customer_report/service/list`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//报告业务类型查询子类
export function getReportsCustomerTypeList(data: RequestBase) {
  return request<never, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/report/customer_report/serviceType/list`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//报告业务类型查询子类
export function getReportsCustomerchartFromList(data: RequestBase) {
  return request<never, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/report/customer_report/chartForm/list`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//查询自定义报表详情
export function getReportsCustomerdetails(data: RequestBase) {
  return request<never, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/report/customer_report/${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//插入自定义图表
export function getReportsInsert(data: RequestBase) {
  return request<never, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/report/customer_report/insertReportChartForm/${data.reportId}`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//取消自定义图表
export function getReportsDel(data: RequestBase) {
  return request<never, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/report/customer_report/cancelReportChartForm?chartFormInsertId=${data.chartFormInsertId}&reportId=${data.reportId}`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//一键清除图表
export function getReportsALLDel(data: RequestBase) {
  return request<never, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/report/customer_report/clearReportChartForm/${data.reportId}`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

// 图片预览
export function getImage(data: RequestBase) {
  return request<never, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/${data.url}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
    responseType: "blob",
  });
}

export function getTenantNotesAll(data: { ids: string[] } & RequestBase) {
  return request<never, Response<[]>>({
    url: `${SERVER.CMDB}/tenant_notes/ids?tenantIds=${data.ids}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

// ----------------------------------------------------------END------------------------------------------------------------------------
