import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";
//获取设备服务目录列表
export function getDeviceCatalogList(data: RequestBase & RequestBase) {
  const params = {
    ...([
      /*  */
      ...(data.includeName instanceof Array ? data.includeName : []),
      ...(data.excludeName instanceof Array ? data.excludeName : []),
      ...(data.eqName instanceof Array ? data.eqName : []),
      ...(data.neName instanceof Array ? data.neName : []),
    ].filter((v) => v).length
      ? {
          /*  */
          nameFilterRelation: data.nameFilterRelation === "OR" ? "OR" : "AND",
          includeName: data.includeName instanceof Array && data.includeName.length ? data.includeName.join(",") : void 0,
          excludeName: data.excludeName instanceof Array && data.excludeName.length ? data.excludeName.join(",") : void 0,
          eqName: data.eqName instanceof Array && data.eqName.length ? data.eqName.join(",") : void 0,
          schemas: data.schemas,
          scope: data.scope,
          neName: data.neName instanceof Array && data.neName.length ? data.neName.join(",") : void 0,
        }
      : {}),
    serviceCatalogStatus: data.serviceCatalogStatus,
  };
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/serviceCatalog/serviceCatalogReport?containerId=${data.containerId}&queryPermissionId=${data.queryPermissionId}`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    responseType: "json",
    params: {},
    data: params,
  });
}
export function downloadDeviceCatalogFile(data: RequestBase & RequestBase) {
  return request<unknown, unknown>({
    url: `/cc_report/event_center/serviceCatalog/exportServiceCatalogReport`,
    method: Method.Post,
    signal: undefined,
    headers: {},
    params: data,
    data: {},
    responseType: "blob",
  });
}
