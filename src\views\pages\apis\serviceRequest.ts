import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";

import { eventSeverity, ServiceItem } from "./event";
export { eventSeverity, type ServiceItem };

import { deviceImportance, deviceImportanceOption } from "./device";
export { deviceImportance, deviceImportanceOption };

import type { ContactsItem } from "./contacts";

export interface EventCount {
  finishedEventCount: number | string;
  unfinishedEventCount: number | string;
  unassignedEventCount: number | string;
}
export function getCurrentUseServiceRequestCount(data: { userId: string } & RequestBase) {
  return request<EventCount[]>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/countMy`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function getAllServiceRequestCount(data: object & RequestBase) {
  return request<EventCount[]>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.tenantId}/countAll`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

// export interface ServiceItem {
//   id: string | number;
//   tenantName: string;
//   responseTime?: string;
//   responseLimit?: string;
//   resolveTime?: string;
//   resolveLimit?: string;
//   serviceType: string;
//   priority?: string;
//   alarmNumber?: string;
//   serviceRequestNumber: string;
//   title: string;
//   serviceRequestDesc: string;
//   deviceName: string;
//   monitorSource: string;
//   createTime: string;
//   updateTime: string;
//   responsibleName: string;
//   finishCodeName: string;
//   finishCodeDesc: string;
//   finishContent: string;
//   deviceId: string[];
// }

export function getCurrentUserServiceByPage(data: object & RequestBase) {
  return request<unknown, Response<ServiceItem[]>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/myList`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { ...data },
    data: {},
  });
}

export function getAllServiceByPage(data: object & RequestBase) {
  return request<unknown, Response<ServiceItem[]>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/list`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { ...data },
    data: {},
  });
}

interface CreateParams {
  tenantName: string;
  title: string;
  description: string;
  deviceName: string[] | [];
  priority: string;
  startTime: string;
  endTime: string;
}

export function serviceRequestCreate(data: CreateParams & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { ...data },
  });
}

export function serviceRequestStart(
  data: {
    serviceRequestId: string | number;
    tenantId: string | number;
  } & RequestBase
) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/start`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { ...data },
  });
}

interface FinishQuery {
  serviceRequestId: string | number;
  finishCodeName: string;
  finishCodeDesc: string;
  finishContent: string;
  tenantId?: string | number;
}

export function serviceRequestFinish(data: FinishQuery & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/finish`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { ...data },
  });
}

export function serviceRequestClose(data: FinishQuery & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/close`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: { ...data },
  });
}

export async function getServiceRequestDeteil(data: { id: string } & RequestBase) {
  const {
    /*  */
    智能事件中心_服务请求工单_可读,
    /* 智能事件中心_服务请求工单_新增, */
    智能事件中心_服务请求工单_更新,
    智能事件中心_服务请求工单_编辑小记,
    智能事件中心_服务请求工单_分配设备,
    智能事件中心_服务请求工单_分配联系人,
    智能事件中心_服务请求工单_关联工单,
  } = await import("@/views/pages/permission");
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.id}/detail`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      queryPermissionId: [智能事件中心_服务请求工单_可读].join(),
      verifyPermissionIds: [智能事件中心_服务请求工单_更新, 智能事件中心_服务请求工单_编辑小记, 智能事件中心_服务请求工单_分配设备, 智能事件中心_服务请求工单_分配联系人, 智能事件中心_服务请求工单_关联工单].join(),
    },
    data: {},
  });
}

export async function getDictServiceRequestDeteil(data: { id: string } & RequestBase) {
  const { 智能事件中心_DICT服务请求_可读, 智能事件中心_DICT服务请求_更新, 智能事件中心_DICT服务请求_编辑小记, 智能事件中心_DICT服务请求_分配设备, 智能事件中心_DICT服务请求_分配联系人 } = await import("@/views/pages/permission");
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.id}/detail`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      queryPermissionId: [智能事件中心_DICT服务请求_可读].join(),
      verifyPermissionIds: [智能事件中心_DICT服务请求_更新, 智能事件中心_DICT服务请求_编辑小记, 智能事件中心_DICT服务请求_分配设备, 智能事件中心_DICT服务请求_分配联系人].join(),
    },
    data: {},
  });
}

interface Dynamic {
  dynamicId: string;
  eventId: string;
  dynamicContent: string;
  eventCreatorId: string;
  eventTransferId: string;
  dynamicCreateTime: string;
}
export function serviceRequestDynamic(data: { id: string | number } & RequestBase) {
  return request<Dynamic[]>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.id}/dynamicInfo`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function setServiceRequestNote(data: { eventId: number | string; content: string } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/saveNote`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data,
  });
}

export function setDictServiceRequestNote(data: { eventId: number | string; content: string } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/saveNote`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data,
  });
}

/**
 *
 * @description 编辑小记
 * @export
 * @param {({ eventId: number | string; content: string } & RequestBase)} data
 * @return {*}
 */
export function editServiceRequestNote(
  data: {
    eventId?: number | string;
    noteId?: number | string;
    content: string;
  } & RequestBase
) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/updateNote/${data.noteId}`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data,
  });
}

export function editDictServiceRequestNote(
  data: {
    eventId?: number | string;
    noteId?: number | string;
    content: string;
  } & RequestBase
) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/updateNote/${data.noteId}`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data,
  });
}

export function getServiceRequestQueryNote(data: { id: string | number } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.id}/queryNote`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function getDictServiceRequestQueryNote(data: { id: string | number } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.id}/queryNote`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function serviceRequestDelNote(data: object & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.id}/deleteNote`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function dictServiceRequestDelNote(data: object & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.id}/deleteNote`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function serviceRequestTransfer(
  data: {
    serviceRequestId: string;
    userGroupId: string;
    userId: string;
    tenantId: string;
  } & RequestBase
) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/transfer`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

interface UpdatePriority {
  eventId: string | number;
  priority: string;
}
export function serviceRequestUpdatePriority(data: UpdatePriority & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/updatePriority`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function serviceRequestAddDevice(data: { id: string; deviceIds: string } & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.id}/add_device/${data.deviceIds}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function serviceRequestRemoveDevice(data: { id: string; deviceIds: string } & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.id}/remove_device/${data.deviceIds}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function setServiceRequestDataByDescription(data: { id: string; description: string; externalId: string } & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/updateDetail`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: ["id", "description", "externalId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { id: data.id, serviceCode: data.serviceCode }),
  });
}

export function setDictServiceRequestDataByDescription(data: { id: string; description: string; externalId: string } & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/updateDetail`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: ["id", "description", "externalId"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { id: data.id, externalId: data.externalId }),
  });
}

export function setServiceRequestDataByServiceCode(data: { id: string; serviceCode: string } & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.id}/replace/serviceCode`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["serviceCode"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: {},
  });
}

export function setServiceRequestDataByImportance /* 修改紧急性 */(data: { id: string; importance: deviceImportance } & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.id}/importance/${data.importance}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
export function setServiceRequestDataBySeverity /* 修改紧急性 */(data: { id: string; severity: eventSeverity } & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.id}/severity/${data.severity}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function addServiceRequestDevices(data: { id: string; deviceId: string; autoAllocateContact: boolean } & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.id}/add_device/${data.deviceId}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: ["autoAllocateContact"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export function addDictServiceRequestDevices(data: { id: string; deviceId: string; autoAllocateContact: boolean } & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.id}/add_device/${data.deviceId}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: ["autoAllocateContact"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export function delServiceRequestDevices(data: { id: string; deviceId: string } & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.id}/remove_device/${data.deviceId}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export function delDictServiceRequestDevices(data: { id: string; deviceId: string } & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.id}/remove_device/${data.deviceId}`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export function getServiceRequestContact(data: { ids: string[] } & RequestBase) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.CMDB}/contacts/batch_get/${data.ids}/desensitized`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
export function eventBatchdesensitized(data: { ids: string[] } & RequestBase) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.CMDB}/contacts/batch_get/desensitized`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {
      ids: data.ids,
      permissionId: "513148893207199744",
    },
  });
}

export function addServiceRequestContact(data: { id: string; contactType: string; contactIds: string[] } & RequestBase) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.id}/addContact`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["contactType", "contactIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export function addDictServiceRequestContact(data: { id: string; contactType: string; contactIds: string[] } & RequestBase) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.id}/addContact`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["contactType", "contactIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export function delServiceRequestContact(data: { id: string; contactType: string; contactIds: string[] } & RequestBase) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.id}/removeContact`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["contactType", "contactIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export function delDictServiceRequestContact(data: { id: string; contactType: string; contactIds: string[] } & RequestBase) {
  return request<unknown, Response<ContactsItem[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.id}/removeContact`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: ["contactType", "contactIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export interface TabCount {
  noteCount: string /* 小记数量 */;
  deviceCount: string /* 设备数量 */;
  contactCount: string /* 联系人数量 */;
  relationCount: string /* 关联数量 */;
  fileCount: string /* 文件数量 */;
  actionCount: string;
}
export function /* 返回tap统计数量 */ getDetailTapCountById(req: { id: string } & RequestBase) {
  const params = [].reduce((p, key) => Object.assign(p, req[key] === undefined ? {} : { [key]: req[key] }), {});
  const data = [].reduce((p, key) => Object.assign(p, req[key] === undefined ? {} : { [key]: req[key] }), {});
  return request<never, Response<TabCount>>({ url: `${SERVER.EVENT_CENTER}/serviceRequest/tap/${req.id}/count`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function /* 返回tap统计数量 */ getDictDetailTapCountById(req: { id: string } & RequestBase) {
  const params = [].reduce((p, key) => Object.assign(p, req[key] === undefined ? {} : { [key]: req[key] }), {});
  const data = [].reduce((p, key) => Object.assign(p, req[key] === undefined ? {} : { [key]: req[key] }), {});
  return request<never, Response<TabCount>>({ url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/tap/${req.id}/count`, method: Method.Get, responseType: "json", signal: req.controller instanceof AbortController ? req.controller.signal : undefined /* , header */, params, data });
}

export function setServiceUpdateEditable(data: {} & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.id}/updateEditable`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: Object.keys(data).reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export function setDictServiceUpdateEditable(data: {} & RequestBase) {
  return request<unknown, Response<ServiceItem>>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.id}/updateEditable`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: Object.keys(data).reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), { eventId: data.id }),
  });
}

export function getChangeContacts(data: { id: string } & RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/serviceRequest/${data.id}/getContact`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: {},
  });
}

export function getDictChangeContacts(data: { id: string } & RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.id}/getContact`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: {},
    data: {},
  });
}

export function setExpectTimeByServiceId(data: {} & RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.EVENT_CENTER}/dict_serviceRequest/${data.id}/replace/expectTime`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    params: ["expectStartTime", "expectEndTime"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: {},
  });
}
