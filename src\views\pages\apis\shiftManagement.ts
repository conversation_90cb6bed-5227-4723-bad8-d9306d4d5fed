import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";

export enum CycleTime {
  everyDay = "everyDay",
  everyWorkDay = "everyWorkDay",
  oneTime = "oneTime",
  customize = "customize",
}

export const cycleTimeOptions: { value: keyof typeof CycleTime; label: string }[] = [
  {
    label: "每天",
    value: CycleTime.everyDay,
  },
  {
    label: "每个工作日",
    value: CycleTime.everyWorkDay,
  },
  {
    label: "不重复",
    value: CycleTime.oneTime,
  },
  {
    label: "自定义",
    value: CycleTime.customize,
  },
];

export enum FrequencyType {
  day = "day",
  week = "week",
  month = "month",
}

export const frequencyTypeOptions: { value: keyof typeof FrequencyType; label: string }[] = [
  {
    label: "天",
    value: FrequencyType.day,
  },
  {
    label: "周",
    value: FrequencyType.week,
  },
  {
    label: "月",
    value: FrequencyType.month,
  },
];

export enum FrequencyRange {
  workDay = "workDay",
  naturalDay = "naturalDay",
}

export const frequencyRangeOptions: { value: keyof typeof FrequencyRange; label: string }[] = [
  {
    label: "工作日",
    value: FrequencyRange.workDay,
  },
  {
    label: "自然日",
    value: FrequencyRange.naturalDay,
  },
];

export interface ScheduleItem {
  id: string;
  /** 容器ID */
  containerId: string;
  /** 班次名称 */
  name: string;
  /** 值班开始时间 */
  dutyStartTime: string;
  /** 值班结束时间 */
  dutyEndTime: string;
  /** 是否自动排班 */
  autoScheduling: boolean;
  /** 主题色 */
  accentColor: string;
  /** 班次生效时间 */
  takeEffectStartTime?: string | number;
  /** 循环周期
   * 每天 everyDay
   * 每个工作日 everyWorkDay
   * 不重复 oneTime
   * 自定义 customize */
  cycleTime?: string;
  /** 自定义频率次数 */
  frequency?: number;
  /** 自定义频率类型
   * day 天
   * week 周
   * month  月 */
  frequencyType?: string;
  /** 频率范围
   * workDay 工作日
   * naturalDay 自然日 */
  frequencyRange?: string;
  /** 班次结束时间 */
  takeEffectyEndTime?: string | number;
}

export function addSchedule(data: {} & RequestBase) {
  return request<unknown, Response<ScheduleItem>>({
    url: `${SERVER.EVENT_CENTER}/schedule/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function setSchedule(data: {} & RequestBase) {
  return request<unknown, Response<ScheduleItem>>({
    url: `${SERVER.EVENT_CENTER}/schedule/update`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export function delSchedule(req: {} & RequestBase) {
  const params = new URLSearchParams();
  bindSearchParams({ id: req.id }, params);
  const data = {};
  return request<unknown, Response<ScheduleItem>>({
    url: `${SERVER.EVENT_CENTER}/schedule/deleteById`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}

export async function getSchedule(req: {} & RequestBase) {
  const params = new URLSearchParams();

  bindSearchParams(
    {
      /*  */
      pageNumber: req.pageNumber,
      pageSize: req.pageSize,
    },
    params
  );

  // const data = new URLSearchParams();

  const { 服务管理中心_班次管理_可读, 服务管理中心_班次管理_新建, 服务管理中心_班次管理_编辑, 服务管理中心_班次管理_删除, 服务管理中心_班次管理_安全 } = await import("@/views/pages/permission");
  const userInfo = (await import("@/utils/getUserInfo")).default();

  // bindSearchParams(
  const data = {
    /*  */
    containerId: req.containerId || (userInfo.currentTenant || {}).containerId,
    queryPermissionId: req.queryPermissionId || 服务管理中心_班次管理_可读,
    verifyPermissionIds: req.verifyPermissionIds || [服务管理中心_班次管理_新建, 服务管理中心_班次管理_编辑, 服务管理中心_班次管理_删除, 服务管理中心_班次管理_安全].join(),
    name: req.name,
  };
  //   ,
  //   data
  // );

  return request<unknown, Response<ScheduleItem[]>>({
    url: `${SERVER.EVENT_CENTER}/schedule/queryBypage`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}

export interface DutyItem {
  /** 主键 */
  id: number;
  version: number;

  accentColor: string;
  /** 创建时间 */
  createdTime?: number;
  /** 更新时间 */
  updatedTime?: number;
  /** 创建人信息 */
  createdBy?: string;
  /** 更新人信息 */
  updatedBy?: string;
  /** 容器ID */
  containerId: number;
  /** 租户ID */
  tenantId: number;
  /** 班次ID */
  scheduleId: number;
  /** 班次名称 */
  scheduleName: string;
  /** 值班开始小时 */
  dutyStartTime?: string;
  /** 值班结束小时 */
  dutyEndTime?: string;
  /** 排班日期 */
  dutyDate: number;
  /** 值班人员ID */
  userIdList?: number[];
  /** 值班人员名称 */
  userNameList?: Record<string, any>[];
}

export function addDuty(data: [] & RequestBase) {
  return request<unknown, Response<DutyItem>>({
    url: `${SERVER.EVENT_CENTER}/schedule/createDuty`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

export async function getDuty(req: {} & RequestBase) {
  const { 服务管理中心_排班日历_安全, 服务管理中心_排班日历_可读, 服务管理中心_排班日历_新建, 服务管理中心_排班日历_编辑, 服务管理中心_排班日历_删除 } = await import("@/views/pages/permission");
  const userInfo = (await import("@/utils/getUserInfo")).default();

  const params = new URLSearchParams();
  bindSearchParams(
    {
      dutyStartDate: req.dutyStartDate,
      dutyEndDate: req.dutyEndDate,
      scheduleName: req.scheduleName,
      userName: req.userName,
    },
    params
  );

  const data = {
    containerId: req.containerId || (userInfo.currentTenant || {}).containerId,
    queryPermissionId: req.queryPermissionId || 服务管理中心_排班日历_可读,
    verifyPermissionIds: req.verifyPermissionIds || [服务管理中心_排班日历_新建, 服务管理中心_排班日历_编辑, 服务管理中心_排班日历_删除, 服务管理中心_排班日历_安全].join(),
  };

  return request<unknown, Response<DutyItem[]>>({
    url: `${SERVER.EVENT_CENTER}/schedule/findDuty`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}

export async function exportDuty(req: {} & RequestBase) {
  const { 服务管理中心_排班日历_安全, 服务管理中心_排班日历_可读, 服务管理中心_排班日历_新建, 服务管理中心_排班日历_编辑, 服务管理中心_排班日历_删除 } = await import("@/views/pages/permission");
  const userInfo = (await import("@/utils/getUserInfo")).default();

  const params = new URLSearchParams();
  bindSearchParams(
    {
      dutyStartDate: req.dutyStartDate,
      dutyEndDate: req.dutyEndDate,
      scheduleName: req.scheduleName,
      userName: req.userName,
    },
    params
  );

  const data = {
    containerId: req.containerId || (userInfo.currentTenant || {}).containerId,
    queryPermissionId: req.queryPermissionId || 服务管理中心_排班日历_可读,
    verifyPermissionIds: req.verifyPermissionIds || [服务管理中心_排班日历_新建, 服务管理中心_排班日历_编辑, 服务管理中心_排班日历_删除, 服务管理中心_排班日历_安全].join(),
  };

  return request<unknown, Response<Blob>>({
    url: `${SERVER.CC_REPORT}/duty/findDuty`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
    responseType: "blob",
  });
}

export async function setDuty(req: {} & RequestBase) {
  const params = new URLSearchParams();
  bindSearchParams({}, params);

  const data = {
    id: req.id,
    userNameList: req.userNameList,
    dutyStartTime: req.dutyStartTime,
    dutyEndTime: req.dutyEndTime,
  };

  return request<unknown, Response<DutyItem>>({
    url: `${SERVER.EVENT_CENTER}/schedule/updateDuty`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}

export async function delDuty(req: {} & RequestBase) {
  const params = new URLSearchParams();
  bindSearchParams({ id: req.id }, params);

  const data = {};

  return request<unknown, Response<DutyItem[]>>({
    url: `${SERVER.EVENT_CENTER}/schedule/deleteDuty`,
    method: Method.Get,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}

export async function importDuty(req: {} & FormData & RequestBase) {
  const userInfo = (await import("@/utils/getUserInfo")).default();

  const params = new URLSearchParams();
  bindSearchParams({ containerId: req.containerId || (userInfo.currentTenant || {}).containerId, tenantId: req.tenantId || (userInfo.currentTenant || {}).id }, params);

  const data = req;

  return request<unknown, Response<DutyItem[]>>({
    url: `${SERVER.EVENT_CENTER}/schedule/import`,
    method: Method.Post,
    signal: req.controller instanceof AbortController ? req.controller.signal : undefined,
    headers: {},
    params,
    data,
  });
}

export function downloadDutyUploadTemplate() {
  return request<unknown, Response<Blob>>({
    url: `${SERVER.CMDB}/templates/schedule-template.xlsx`,
    method: Method.Get,
    signal: undefined,
    headers: {},
    params: {},
    data: {},
    responseType: "blob",
  });
}

export function getUserByUserGroupIds(req: string[]) {
  return request<unknown, Response<Record<string, any>>>({
    url: `${SERVER.EVENT_CENTER}/schedule/findUsers`,
    method: Method.Post,
    signal: undefined,
    headers: {},
    params: {},
    data: req,
  });
}
