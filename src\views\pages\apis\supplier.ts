import { SERVER, Method, type Response, type RequestBase, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";
import { ca } from "element-plus/es/locale";

/**
 * @description 获取供应商2.0-响应体
 * @url http://*************:3000/project/47/interface/api/24877
 */
export interface VendorsItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 名称 */
  name: string;
  /** 描述 */
  description?: string;
  /** 地址 */
  address?: string;
  /** 固定电话 */
  landlinePhone?: string;
  /** 支持电话 */
  supportPhone?: string;
  /** 联系人姓名 */
  contactName?: string;
  /** 电子邮箱 */
  email?: string;
  /** 供应商类别 */
  vendorType?: string;
  /** 供应商类别名称 */
  vendorTypeName?: string;
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}

/**
 * @description 获取供应商2.0
 * @url http://*************:3000/project/47/interface/api/24877
 */
export function getVendorsList(req: { vendorType: "DEVICE" | "LINE" } & Record<string, any>) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/vendors/${req.vendorType}/desensitized/2.0/filter`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = {};
        $req.params = new URLSearchParams();
        $req.data = void 0;
        try {
          const [{ default: getUserInfo }, { 资产管理中心_设备供应商_可读, 资产管理中心_设备供应商_新增, 资产管理中心_设备供应商_编辑, 资产管理中心_设备供应商_删除, 资产管理中心_设备供应商_安全, 资产管理中心_线路供应商_可读, 资产管理中心_线路供应商_新增, 资产管理中心_线路供应商_编辑, 资产管理中心_线路供应商_删除, 资产管理中心_线路供应商_安全 }] = await Promise.all([import("@/utils/getUserInfo"), import("@/views/pages/permission")]);
          const user = getUserInfo();
          for (let i = 0; i < user.tenants.length; i++) {
            if (user.currentTenantId === user.tenants[i].id) {
              bindParamByObj({ containerId: user.tenants[i].containerId }, $req.params);
              break;
            }
          }
          switch (req.vendorType) {
            case "DEVICE":
              bindParamByObj({ queryPermissionId: [资产管理中心_设备供应商_可读].join(","), verifyPermissionIds: [资产管理中心_设备供应商_新增, 资产管理中心_设备供应商_编辑, 资产管理中心_设备供应商_删除, 资产管理中心_设备供应商_安全].join(",") }, $req.params);
              break;
            case "LINE":
              bindParamByObj({ queryPermissionId: [资产管理中心_线路供应商_可读].join(","), verifyPermissionIds: [资产管理中心_线路供应商_新增, 资产管理中心_线路供应商_编辑, 资产管理中心_线路供应商_删除, 资产管理中心_线路供应商_安全].join(",") }, $req.params);
              break;
            default:
              throw new Error("Web Error: 无效供应商类型");
          }
        } catch (error) {
          if (error instanceof Error) throw error;
        }

        bindParamByObj(
          {
            ...([...(req.includeName instanceof Array ? req.includeName : []), ...(req.excludeName instanceof Array ? req.excludeName : []), ...(req.eqName instanceof Array ? req.eqName : []), ...(req.neName instanceof Array ? req.neName : [])].filter((v) => v).length ? { nameFilterRelation: req.nameFilterRelation === "OR" ? "OR" : "AND", includeName: req.includeName instanceof Array && req.includeName.length ? req.includeName.join(",") : void 0, excludeName: req.excludeName instanceof Array && req.excludeName.length ? req.excludeName.join(",") : void 0, eqName: req.eqName instanceof Array && req.eqName.length ? req.eqName.join(",") : void 0, neName: req.neName instanceof Array && req.neName.length ? req.neName.join(",") : void 0 } : {}),

            ...([...(req.includeDescription instanceof Array ? req.includeDescription : []), ...(req.excludeDescription instanceof Array ? req.excludeDescription : []), ...(req.eqDescription instanceof Array ? req.eqDescription : []), ...(req.neDescription instanceof Array ? req.neDescription : [])].filter((v) => v).length ? { descriptionFilterRelation: req.descriptionFilterRelation === "OR" ? "OR" : "AND", includeDescription: req.includeDescription instanceof Array && req.includeDescription.length ? req.includeDescription.join(",") : void 0, excludeDescription: req.excludeDescription instanceof Array && req.excludeDescription.length ? req.excludeDescription.join(",") : void 0, eqDescription: req.eqDescription instanceof Array && req.eqDescription.length ? req.eqDescription.join(",") : void 0, neDescription: req.neDescription instanceof Array && req.neDescription.length ? req.neDescription.join(",") : void 0 } : {}),

            ...([...(req.includeLandlinePhone instanceof Array ? req.includeLandlinePhone : []), ...(req.excludeLandlinePhone instanceof Array ? req.excludeLandlinePhone : []), ...(req.eqLandlinePhone instanceof Array ? req.eqLandlinePhone : []), ...(req.neLandlinePhone instanceof Array ? req.neLandlinePhone : [])].filter((v) => v).length ? { landlinePhoneFilterRelation: req.landlinePhoneFilterRelation === "OR" ? "OR" : "AND", includeLandlinePhone: req.includeLandlinePhone instanceof Array && req.includeLandlinePhone.length ? req.includeLandlinePhone.join(",") : void 0, excludeLandlinePhone: req.excludeLandlinePhone instanceof Array && req.excludeLandlinePhone.length ? req.excludeLandlinePhone.join(",") : void 0, eqLandlinePhone: req.eqLandlinePhone instanceof Array && req.eqLandlinePhone.length ? req.eqLandlinePhone.join(",") : void 0, neLandlinePhone: req.neLandlinePhone instanceof Array && req.neLandlinePhone.length ? req.neLandlinePhone.join(",") : void 0 } : {}),

            ...([...(req.includeSupportPhone instanceof Array ? req.includeSupportPhone : []), ...(req.excludeSupportPhone instanceof Array ? req.excludeSupportPhone : []), ...(req.eqSupportPhone instanceof Array ? req.eqSupportPhone : []), ...(req.neSupportPhone instanceof Array ? req.neSupportPhone : [])].filter((v) => v).length ? { supportPhoneFilterRelation: req.supportPhoneFilterRelation === "OR" ? "OR" : "AND", includeSupportPhone: req.includeSupportPhone instanceof Array && req.includeSupportPhone.length ? req.includeSupportPhone.join(",") : void 0, excludeSupportPhone: req.excludeSupportPhone instanceof Array && req.excludeSupportPhone.length ? req.excludeSupportPhone.join(",") : void 0, eqSupportPhone: req.eqSupportPhone instanceof Array && req.eqSupportPhone.length ? req.eqSupportPhone.join(",") : void 0, neSupportPhone: req.neSupportPhone instanceof Array && req.neSupportPhone.length ? req.neSupportPhone.join(",") : void 0 } : {}),

            ...([...(req.includeContactName instanceof Array ? req.includeContactName : []), ...(req.excludeContactName instanceof Array ? req.excludeContactName : []), ...(req.eqContactName instanceof Array ? req.eqContactName : []), ...(req.neContactName instanceof Array ? req.neContactName : [])].filter((v) => v).length ? { contactNameFilterRelation: req.contactNameFilterRelation === "OR" ? "OR" : "AND", includeContactName: req.includeContactName instanceof Array && req.includeContactName.length ? req.includeContactName.join(",") : void 0, excludeContactName: req.excludeContactName instanceof Array && req.excludeContactName.length ? req.excludeContactName.join(",") : void 0, eqContactName: req.eqContactName instanceof Array && req.eqContactName.length ? req.eqContactName.join(",") : void 0, neContactName: req.neContactName instanceof Array && req.neContactName.length ? req.neContactName.join(",") : void 0 } : {}),

            ...([...(req.includeEmail instanceof Array ? req.includeEmail : []), ...(req.excludeEmail instanceof Array ? req.excludeEmail : []), ...(req.eqEmail instanceof Array ? req.eqEmail : []), ...(req.neEmail instanceof Array ? req.neEmail : [])].filter((v) => v).length ? { emailFilterRelation: req.emailFilterRelation === "OR" ? "OR" : "AND", includeEmail: req.includeEmail instanceof Array && req.includeEmail.length ? req.includeEmail.join(",") : void 0, excludeEmail: req.excludeEmail instanceof Array && req.excludeEmail.length ? req.excludeEmail.join(",") : void 0, eqEmail: req.eqEmail instanceof Array && req.eqEmail.length ? req.eqEmail.join(",") : void 0, neEmail: req.neEmail instanceof Array && req.neEmail.length ? req.neEmail.join(",") : void 0 } : {}),
          },
          $req.params
        );

        return $req;
      })
      .then(($req) => request<never, Response<VendorsItem[]>>($req)),
    { controller }
  );
}

export interface SlaConfigList {
  id: string;

  tenantId: string;
}
//设备供应商列表
export function getSupplierList(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return getVendorsList({ ...data, vendorType: "DEVICE" });
}
//线路供应商列表
export function getLineSupplierList(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return getVendorsList({ ...data, vendorType: "LINE" });
}

//新增供应商
export function addSupplier(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/vendors`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//编辑供应商
export function editSupplier(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/vendors/${data.id}`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//删除商列表
export function deleteSupplier(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<SlaConfigList[]>({
    url: `${SERVER.CMDB}/vendors/${data.id}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//供应商详情-脱敏
export function getSupplierDetailDesensitized(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/vendors/${data.id}/detail/desensitized`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//供应商详情-非脱敏
export function getSupplierDetail(data: { pageNumber: number; pageSize: number } & RequestBase) {
  return request<unknown, unknown>({
    url: `${SERVER.CMDB}/vendors/${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
