import { SERVER, Method, type Response, type RequestBase, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";

export interface SlaConfigList {
  id: string;

  tenantId: string;
}
//行动策略列表
export function getSupport_notesList(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/tenant/current`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

export function getnewSupport_notesList(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  const params = new URLSearchParams();
  bindParamByObj(
    {
      // active: data.active,
      containerId: data.containerId,
      queryPermissionId: data.queryPermissionId,
      verifyPermissionIds: data.verifyPermissionIds,
      pageNumber: data.pageNumber,
      pageSize: data.pageSize,
    },
    params
  );

  bindParamByObj(
    Object.assign(
      {
        ...([...(data.includeName instanceof Array ? data.includeName : []), ...(data.excludeName instanceof Array ? data.excludeName : []), ...(data.eqName instanceof Array ? data.eqName : []), ...(data.neName instanceof Array ? data.neName : [])].filter((v) => v).length ? { nameFilterRelation: data.nameFilterRelation === "OR" ? "OR" : "AND", includeName: data.includeName instanceof Array && data.includeName.length ? data.includeName.join(",") : void 0, excludeName: data.excludeName instanceof Array && data.excludeName.length ? data.excludeName.join(",") : void 0, eqName: data.eqName instanceof Array && data.eqName.length ? data.eqName.join(",") : void 0, neName: data.neName instanceof Array && data.neName.length ? data.neName.join(",") : void 0 } : {}),

        ...([...(data.includeDescription instanceof Array ? data.includeDescription : []), ...(data.excludeDescription instanceof Array ? data.excludeDescription : []), ...(data.eqDescription instanceof Array ? data.eqDescription : []), ...(data.neDescription instanceof Array ? data.neDescription : [])].filter((v) => v).length ? { descriptionFilterRelation: data.descriptionFilterRelation === "OR" ? "OR" : "AND", includeDescription: data.includeDescription instanceof Array && data.includeDescription.length ? data.includeDescription.join(",") : void 0, excludeDescription: data.excludeDescription instanceof Array && data.excludeDescription.length ? data.excludeDescription.join(",") : void 0, eqDescription: data.eqDescription instanceof Array && data.eqDescription.length ? data.eqDescription.join(",") : void 0, neDescription: data.neDescription instanceof Array && data.neDescription.length ? data.neDescription.join(",") : void 0 } : {}),

        containerList: data.containerList,
      },
      data.active ? { active: data.active } : {} /* 是否激活 */
    ),
    params
  );
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/2.0/tenant/filter`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params,
    data: {},
  });
}

//获取全局行动策略

export function getSupport_notes(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/global`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//更新全局行动策略

export function updateSupport_notes(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/global`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//新增行动策略
export function addSupport_notes(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/tenant/current`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//编辑行动策略
export function editSupport_notes(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/${data.id}`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//删除行动策略
export function deleteSupport_notes(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/${data.id}`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//根据id查询行动策略
export function getSupportNoteDetailById(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/getSupportNoteDetailById/${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//修改行动策略激活状态
export function updateSupport_notesActive(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/${data.id}/active`,
    method: Method.Patch,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
//根据id获取行动策略详情
export function getSupport_notesDetaile(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/${data.id}/detail`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//行动策略关联区域列表
export function Support_notesRelationArea(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/regions/support_note/add/batch`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//行动策略关联场所列表
export function Support_notesRelationLocation(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/locations/support_note/add/batch`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//行动策略关联设备列表
export function Support_notesRelationDevice(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/${data.id}/add/resources`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//行动策略取消关联设备
export function Support_notesDelRelationDevice(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/${data.id}/remove/resources`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//行动策略取消关联区域
export function Support_notesDelRelationArea(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/regions/support_note/remove/batch`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//行动策略取消关联场所
export function Support_notesDelRelationLocation(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/locations/support_note/remove/batch`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//行动策略保存
export function Support_notesSetHours(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/${data.id}/active_hours`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {
      autoTimeZone: data.autoTimeZone,
    },
  });
}

//全局行动策略列表
export function getSupport_notesListGlobal(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/findGlobalList`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}

//新增全局行动策略
export function addSupport_notesGlobal(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/createGlobal`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
//编辑全局行动策略
export function editSupport_notesGlobal(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/${data.id}/updateGlobal`,
    method: Method.Put,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//删除全局行动策略
export function deleteSupport_notesGlobal(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/${data.id}/deleteGlobal`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//行动策略关联租户
export function Support_notesGlobalRelationTenant(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/${data.id}/assign_tenants`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//行动策略移除关联租户

export function Support_notesDelGlobalRelationTenant(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/${data.id}/remove_tenants`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//根据行动策略id获取对应的租户信息
export function getSupport_noteslGlobalTenant(data: Partial<{ pageNumber: number; pageSize: number }> & RequestBase) {
  return request<never, Response<SlaConfigList[]>>({
    url: `${SERVER.CMDB}/support_notes/${data.id}/global_find_tenants`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

export function getAvailableTreeLine(data: {} & RequestBase) {
  return request<never, Response<string[]>>({
    url: `${SERVER.IAM}/current_org/security_containers/current_user/available_tree/line`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
