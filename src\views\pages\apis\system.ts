import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";

export interface MenuMeta {
  id: string;
  parentId: string /* 父菜单id */;
  appCode: string /* 应用编码 */;
  name: string /* 菜单名称 */;
  ident: string /* 唯一标识，同应用下唯一 */;
  enabled: boolean /* 是否启用 */;
  order: number /* 排序 */;
  config: string;
  permissions: string[] /* 权限列表 */;

  createdTime?: string;
  updatedTime?: string;
}

export interface MenuItem {
  id: MenuMeta["id"];
  parentId: MenuMeta["parentId"];
  order: MenuMeta["order"];
  permissions: MenuMeta["permissions"];
  title: MenuMeta["name"];
  name: <PERSON>uMeta["ident"];
  enabled: MenuMeta["enabled"];
  type: keyof typeof menuType;
  note: string;
  children: MenuItem[];

  createdTime?: MenuMeta["createdTime"];
  updatedTime?: MenuMeta["updatedTime"];
}
export interface MenuItem {
  /* 目录: MenuType.dir */
  path: string;
  icon: string;
  extend: keyof typeof menuExtendType;
}
export interface MenuItem {
  /* 菜单项: MenuType.menu */
  path: string;
  icon: string;
  component: string;
  keepalive: boolean;
  extend: keyof typeof menuExtendType;
}
export interface MenuItem {
  /* 链接: MenuType.link */
  url: string;
  icon: string;
  extend: keyof typeof menuExtendType;
}
export interface MenuItem {
  /* 微应用: MenuType.micro */
  micro: string;
  path: string;
  url: string;
  icon: string;
  keepalive: boolean;
  extend: keyof typeof menuExtendType;
}
export enum menuType {
  dir = "dir",
  menu = "menu",
  link = "link",
  micro = "micro",
}
export const menuTypeOption: { label: string; value: keyof typeof menuType }[] = [
  { label: "目录", value: "dir" },
  { label: "菜单项", value: "menu" },
  { label: "链接", value: "link" },
  { label: "微应用", value: "micro" },
];
export enum menuExtendType {
  none = "none",
  add_rules_only = "add_rules_only",
  add_menu_only = "add_menu_only",
}
export const menuExtendTypeOption: {
  label: string;
  value: keyof typeof menuExtendType;
}[] = [
  { label: "无", value: "none" },
  { label: "只添加为路由", value: "add_rules_only" },
  { label: "只添加为菜单", value: "add_menu_only" },
];
// export function getMenuList(data: { app: string } & RequestBase): Promise<Response<MenuItem[]>> {
//   return request<MenuMeta, Response<(Omit<MenuMeta, "config"> & { config: string })[]>>({
//     url: `${SERVER.IAM}/user/current/front_apps/${data.app}/menus`,
//     method: Method.Get,
//     signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
//     headers: {},
//     params: {},
//     data: new URLSearchParams(),
//   }).then((res) => {
//     return {
//       success: res.success,
//       message: res.message,
//       page: res.page,
//       size: res.size,
//       total: res.total,
//       data: Array.from(
//         res.data
//           .map((menu): MenuItem => {
//             const config: Required<MenuItem> = {
//               id: menu.id,
//               parentId: menu.parentId,
//               order: Number(menu.order),
//               enabled: menu.enabled,
//               name: menu.ident,
//               title: menu.name,
//               permissions: menu.permissions,
//               type: menuType.dir,
//               path: "",
//               micro: "",
//               icon: "el-icon-Grid",
//               url: "",
//               component: "",
//               keepalive: false,
//               extend: menuExtendType.none,
//               note: "",
//               children: [],
//               createdTime: menu.createdTime as string,
//               updatedTime: menu.updatedTime as string,
//             };

//             try {
//               const {
//                 /*  */
//                 type: type = config.type,
//                 path: path = config.path,
//                 micro: micro = config.micro,
//                 icon: icon = config.icon,
//                 url: url = config.url,
//                 component: component = config.component,
//                 keepalive: keepalive = config.keepalive,
//                 extend: extend = config.extend,
//                 note: note = config.note,
//               } = JSON.parse(menu.config);
//               return Object.assign(config, {
//                 /*  */
//                 type,
//                 path,
//                 micro,
//                 icon,
//                 url,
//                 component,
//                 keepalive,
//                 extend,
//                 note,
//               });
//             } catch (error) {
//               return config;
//             }
//           })
//           .map((value, _index, full) => {
//             if (value.id === value.parentId) return value;
//             else {
//               return Object.assign(value, {
//                 children: full
//                   .filter(({ parentId }) => parentId === value.id)
//                   .map((v) => Object.assign(v, { consume: true }))
//                   .sort((a, b) => Number(a.order) - Number(b.order)),
//               });
//             }
//           })
//           .filter((v: MenuItem & { consume?: boolean }) => {
//             const consume = v.consume;
//             if (consume) delete v.consume;
//             return !consume;
//           })
//           .sort((a, b) => Number(a.order) - Number(b.order))
//       ),
//     };
//   });
// }
