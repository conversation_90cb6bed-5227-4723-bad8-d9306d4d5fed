import { SERVER, Method, bindSearchParams, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";
import type { Zone } from "@/utils/zone";

export interface Tenants {
  id: string /* 租户ID */;
  containerId: string /* 安全容器ID */;
  name: string /* 租户名称 */;
  abbreviation: string /* 租户缩写 */;
  systemEdition?: string /* 系统版本 */;
  zoneId?: string /* 时区ID */;
  language?: string /* 语言 */;
  address?: string /* 地址 */;
  note?: string /* 备注 */;
  fullPermission: boolean /* 是否拥有完整权限 */;
  mfaState: "ENABLED" | "DISABLED" | "DEFAULT" /* 双因素认证启用状态 枚举类型: ENABLED :启用MFA认证 | DISABLED :禁用MFA认证 | DEFAULT :使用系统默认配置 */;
  ownerId?: string /* 拥有人用户ID */;
  ownerName?: string /* 拥有人姓名 */;
  ownerNickname?: string /* 拥有人昵称 */;
  ownerAccount?: string /* 拥有人账号 */;
  ownerPhone?: string /* 拥有人手机号 */;
  ownerEmail?: string /* 拥有人邮箱 */;
  blocked: boolean /* 租户是否已被锁定 */;
  activated: boolean /* 租户是否已激活 */;
  version: string /* 乐观锁版本 */;
  createdTime: string /* 创建时间 */;
  updatedTime: string /* 更新时间 */;
  createdBy?: string /* 创建人信息 */;
  updatedBy?: string /* 更新人信息 */;
  tenantPostcode?: string /* 租户邮编 */;
  tenantEmail?: string /* 租户邮箱 */;
  tenantFax?: string /* 租户传真 */;
  tenantPhone?: string /* 租户电话 */;
  tenantType?: string /* 租户类型 */;
  tenantSigningPlace?: string /* 租户签约地 */;
  tenantSigningPlace2?: string /* 租户签约地2 */;
  tenantIndustry?: string /* 租户行业 */;
  tenantNature?: string /* 租户性质 */;
  tenantChannel?: string /* 租户渠道 */;
  tenantOperator: boolean /* 租户是否运营商 */;
  tenantForeign: boolean /* 租户是否运营商 */;
  enableMfaDefault: boolean /* 是否默认启用了MFA */;
}
// {
//   id: string;
//   platform: string;
//   baileeTenantId: string;
//   name: string;
//   abbreviation: string;
//   systemEdition: string;
//   zoneId: keyof typeof Zone;
//   language: string;
//   address: string;
//   note: string;
//   fullPermission: boolean;
//   securityConfig: TenantSecurityConfig[];
//   ownerId: string;
//   ownerName: string;
//   ownerNickname: string;
//   ownerAccount: string;
//   ownerPhone: string;
//   ownerEmail: string;
//   blocked: boolean;
//   version: string;
//   createdTime: string;
//   updatedTime: string;
//   createdBy: string;
//   updatedBy: string;
//   desensitized: string;
// }

export interface TenantSecurityConfig {
  enableMfa: boolean;
  repeatLogin: boolean;
  loginFailureLimit: string;
  histPasswordLimit: string;
  passwordExpireDays: string;
}

export function getCurrentTenants(data: { id?: string; keyword?: string; pageNumber: number; pageSize: number } & RequestBase) {
  const params = new URLSearchParams();
  bindSearchParams(
    ["id", "keyword", "pageNumber", "pageSize", "sort"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params
  );
  bindSearchParams({ permissionId: ["513160392680144896"].join(",") }, params);
  return request<unknown, Response<Tenants[]>>({
    url: `${SERVER.IAM}/tenants/platform/current`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params,
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function addTenant(data: Partial<Record<keyof Tenants | "owner", string | object>> & RequestBase) {
  return request<unknown, Response<Tenants>>({
    url: `${SERVER.IAM}/tenants/platform/current`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: Object.keys(data).reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function modTenant(data: Partial<Tenants> & RequestBase) {
  return request<unknown, Response<Tenants>>({
    url: `${SERVER.IAM}/tenants/${data.id}`,
    method: Method.Patch,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "abbreviation", "zoneId", "language", "address", "note"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export interface SystemEdition {
  code: string;
  name: string;
}

/**
 * @name 获取当前平台下所有的系统版本
 * @param data
 * @returns
 */
export function getSystemEditions(data: {} & RequestBase) {
  return request<unknown, Response<SystemEdition[]>>({
    url: `${SERVER.IAM}/system_editions/platform/current`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: Object.keys(data).reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
// 获取当前客户信息
export function getTenantInfo(data: { [key: string]: unknown }) {
  return request<unknown, Response<Tenants>>({
    url: `${SERVER.IAM}/current_tenant/tenant_info`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}
//查询客户的工作时区
export function getTenantNotes(data: { [tenantId: string]: unknown }) {
  return request<unknown, Response<Tenants>>({
    url: `${SERVER.CMDB}/tenant_notes?tenantId=${data.tenantId}`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

//更新客户的工作时区
export function updateTenantNotes(data: { [tenantId: string]: unknown }) {
  return request<unknown, Response<Tenants>>({
    url: `${SERVER.CMDB}/tenant_notes`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
