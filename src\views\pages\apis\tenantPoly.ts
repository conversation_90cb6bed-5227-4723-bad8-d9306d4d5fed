import { SERVER, Method, bindSearchParams, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";
import type { Zone } from "@/utils/zone";

export interface Strategys {
  id: number;
  /** 租户id */
  tenantId: number;
  /** 模板中添加的客户id */
  tenantIds: number[];
  /** 报表名称 */
  name: string;
  /** 报表类型 */
  type: string;
  /** 报表模板 */
  template: string;
  /** 是否启用(状态：true-启用，false-禁用) */
  enable: boolean;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 创建人信息 */
  createdBy?: string;
  /** 更新人信息 */
  updatedBy?: string;
  /** 创建时间 */
  createdTime?: number;
  /** 更新时间 */
  updatedTime?: number;
}

export function getTenantPolyList(data: { tenantId?: string; type?: string; pageNumber: number; pageSize: number } & RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.IAM}/current_org/tenant_strategy`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: ["tenantId", "type", "pageNumber", "pageSize", "sort"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function addTenantPoly(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.IAM}/current_org/tenant_strategy`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: Object.keys(data).reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function editTenantPoly(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.IAM}/current_org/tenant_strategy/${data.id}`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: Object.keys(data).reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function delTenantPoly(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.IAM}/current_org/tenant_strategy/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {},
  });
}

export function getTenantStrategyPolyList(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.IAM}/tenant_strategy/${data.id}/tenants`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {
      tenantIds: data.tenantIds,
    },
  });
}

export function addTenantPolyStrategy(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.IAM}/tenant_strategy/${data.id}/assign_tenant`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {
      tenantIds: data.tenantIds,
    },
  });
}
export function delTenantPolyStrategy(data: RequestBase) {
  return request<unknown, Response<[]>>({
    url: `${SERVER.IAM}/tenant_strategy/${data.id}/remove_tenant`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: {},
    data: {
      tenantIds: data.tenantIds,
    },
  });
}
