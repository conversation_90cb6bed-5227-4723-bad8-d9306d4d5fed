import { SERVER, Method, type Response, type RequestBase, bindSearchParams } from "@/api/service/common";
import request from "@/api/service/index";

export interface AddReqBody {
  ticketTemplatesName: string;
  description: string;
  ticketType: string;
  ticketGroupId: string;
  ticketGroupName: string;
  containerId: string;
  orderSubclass: string;
  relevancy: string;
}

export interface SetReqBody {
  id: string;
  tenantId: string;
  containerId: string;
  ticketTemplatesName: string;
  description: string;
  category: string;
  ticketType: string;
  ticketGroupId: string;
  ticketGroupName: string;
  assignUserGroups: string;
  levelService: string;
  active: boolean;
  orderSubclass: string;
}

export enum TicketType {
  event = "event",
  service = "service",
  question = "question",
  change = "change",
  publish = "publish",
  dictevent = "dictevent",
  dictservice = "dictservice",
}

export const ticketTypes: { ticketType: keyof typeof TicketType; ticketName: string; value: keyof typeof TicketType; label: string }[] = [
  { ticketType: TicketType.event, ticketName: "事件管理", value: TicketType.event, label: "事件管理" },
  { ticketType: TicketType.service, ticketName: "服务请求", value: TicketType.service, label: "服务请求" },
  { ticketType: TicketType.question, ticketName: "问题管理", value: TicketType.question, label: "问题管理" },
  { ticketType: TicketType.change, ticketName: "变更管理", value: TicketType.change, label: "变更管理" },
  { ticketType: TicketType.publish, ticketName: "发布管理", value: TicketType.publish, label: "发布管理" },
  // { ticketType: TicketType.dictevent, ticketName: "DICT事件管理" },
  // { ticketType: TicketType.dictservice, ticketName: "DICT服务请求" },
];
export const relevancyTypes: { value: keyof typeof TicketType; label: string }[] = [
  { value: TicketType.event, label: "事件" },
  { value: TicketType.service, label: "服务请求" },
  { value: TicketType.question, label: "问题" },
  { value: TicketType.change, label: "变更管理" },
  { value: TicketType.publish, label: "发布管理" },
  { value: TicketType.dictevent, label: "DICT事件" },
  { value: TicketType.dictservice, label: "DICT服务请求" },
];
export const relevancyOptions = {
  [TicketType.event]: [
    { value: TicketType.event, label: "事件" },
    { value: TicketType.dictevent, label: "DICT事件" },
  ],
  [TicketType.service]: [
    { value: TicketType.service, label: "服务请求" },
    { value: TicketType.dictservice, label: "DICT服务请求" },
  ],
  [TicketType.question]: [{ value: TicketType.question, label: "问题" }],
  [TicketType.change]: [{ value: TicketType.change, label: "变更管理" }],
  [TicketType.publish]: [{ value: TicketType.publish, label: "发布管理" }],
};

export enum ChangeType {
  ORDINARY = "ORDINARY",
  STANDARD = "STANDARD",
  URGENCY = "URGENCY",
  IMPORTANT = "IMPORTANT",
}

export const changeType = [
  { label: "一般变更", value: ChangeType.ORDINARY },
  { label: "标准变更", value: ChangeType.STANDARD },
  { label: "紧急变更 ", value: ChangeType.URGENCY },
  { label: "重大变更", value: ChangeType.IMPORTANT },
];

export enum PublishType {
  ORDINARY = "ORDINARY",
  STANDARD = "STANDARD",
  URGENCY = "URGENCY",
  IMPORTANT = "IMPORTANT",
}

export const publishType = [
  { label: "一般发布", value: ChangeType.ORDINARY },
  { label: "标准发布", value: ChangeType.STANDARD },
  { label: "紧急发布 ", value: ChangeType.URGENCY },
  { label: "重大发布", value: ChangeType.IMPORTANT },
];
export const levelServicelist = [
  { label: "不设置", value: "" },
  { label: "客保SLA", value: "" },
  { label: "默认SLA ", value: "" },
];

export enum eventState {
  /**@type {string} - 新建 */
  NEW = "NEW",
  /**@type {string} - 未分配 */
  UNASSIGNED = "UNASSIGNED",
  /**@type {string} - 待处理 */
  WAITING_FOR_RECEIVE = "WAITING_FOR_RECEIVE",
  /**@type {string} - 处理中 */
  PROCESSING = "PROCESSING",
  /**@type {string} - 挂起待审批中 */
  PENDING_APPROVAL = "PENDING_APPROVAL",
  /**@type {string} - 客户挂起 */
  CUSTOMER_SUSPENDED = "CUSTOMER_SUSPENDED",
  /**@type {string} - 供应商挂起 */
  SERVICE_PROVIDER_SUSPENDED = "SERVICE_PROVIDER_SUSPENDED",
  /**@type {string} - 挂起中 */
  SUSPENDED = "SUSPENDED",
  /**@type {string} - 完成 */
  COMPLETED = "COMPLETED",
  /**@type {string} - 关闭 */
  CLOSED = "CLOSED",
  /**@type {string} - 自动关闭 */
  AUTO_CLOSED = "AUTO_CLOSED",
}

export const StateOption: { label: string; value: keyof typeof eventState }[] = [
  { label: "新建", value: eventState.NEW },
  { label: "处理中", value: eventState.PROCESSING },
  // { label: "客户挂起", value: eventState.CUSTOMER_SUSPENDED },
  // { label: "供应商挂起", value: eventState.SERVICE_PROVIDER_SUSPENDED },
  // { label: "解决", value: eventState.COMPLETED },
  // { label: "关闭", value: eventState.CLOSED },
];
export const dictStateOption: { label: string; value: keyof typeof eventState }[] = [{ label: "新建", value: eventState.NEW }];
/**
 * 获取工单模板列表数据
 */
export function getAllticketTemplates(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_templates/query`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: { pageNumber: data.pageNumber, pageSize: data.pageSize },
    data: {
      containerId: data.containerId,
      queryPermissionId: data.queryPermissionId,
      verifyPermissionIds: data.verifyPermissionIds,
      ticketTypeList: data.ticketTypeList,
      relevancyList: data.relevancyList,
      active: data.active,
      createSubmit: data.createSubmit,
      ...([...(data.includeAssignUserGroupName instanceof Array ? data.includeAssignUserGroupName : []), ...(data.excludeAssignUserGroupName instanceof Array ? data.excludeAssignUserGroupName : []), ...(data.eqAssignUserGroupName instanceof Array ? data.eqAssignUserGroupName : []), ...(data.neAssignUserGroupName instanceof Array ? data.neAssignUserGroupName : [])].filter((v) => v).length ? { assignUserGroupNameFilterRelation: data.assignUserGroupNameFilterRelation === "OR" ? "OR" : "AND", includeAssignUserGroupName: data.includeAssignUserGroupName instanceof Array && data.includeAssignUserGroupName.length ? data.includeAssignUserGroupName.join(",") : void 0, excludeAssignUserGroupName: data.excludeAssignUserGroupName instanceof Array && data.excludeAssignUserGroupName.length ? data.excludeAssignUserGroupName.join(",") : void 0, eqAssignUserGroupName: data.eqAssignUserGroupName instanceof Array && data.eqAssignUserGroupName.length ? data.eqAssignUserGroupName.join(",") : void 0, neAssignUserGroupName: data.neAssignUserGroupName instanceof Array && data.neAssignUserGroupName.length ? data.neAssignUserGroupName.join(",") : void 0 } : {}),
      ...([...(data.includeLevelServiceName instanceof Array ? data.includeLevelServiceName : []), ...(data.excludeLevelServiceName instanceof Array ? data.excludeLevelServiceName : []), ...(data.eqLevelServiceName instanceof Array ? data.eqLevelServiceName : []), ...(data.neLevelServiceName instanceof Array ? data.neLevelServiceName : [])].filter((v) => v).length ? { levelServiceNameFilterRelation: data.levelServiceNameFilterRelation === "OR" ? "OR" : "AND", includeLevelServiceName: data.includeLevelServiceName instanceof Array && data.includeLevelServiceName.length ? data.includeLevelServiceName.join(",") : void 0, excludeLevelServiceName: data.excludeLevelServiceName instanceof Array && data.excludeLevelServiceName.length ? data.excludeLevelServiceName.join(",") : void 0, eqLevelServiceName: data.eqLevelServiceName instanceof Array && data.eqLevelServiceName.length ? data.eqLevelServiceName.join(",") : void 0, neLevelServiceName: data.neLevelServiceName instanceof Array && data.neLevelServiceName.length ? data.neLevelServiceName.join(",") : void 0 } : {}),
      ...([...(data.includeTicketGroupName instanceof Array ? data.includeTicketGroupName : []), ...(data.excludeTicketGroupName instanceof Array ? data.excludeTicketGroupName : []), ...(data.eqTicketGroupName instanceof Array ? data.eqTicketGroupName : []), ...(data.neTicketGroupName instanceof Array ? data.neTicketGroupName : [])].filter((v) => v).length ? { ticketGroupNameFilterRelation: data.ticketGroupNameFilterRelation === "OR" ? "OR" : "AND", includeTicketGroupName: data.includeTicketGroupName instanceof Array && data.includeTicketGroupName.length ? data.includeTicketGroupName.join(",") : void 0, excludeTicketGroupName: data.excludeTicketGroupName instanceof Array && data.excludeTicketGroupName.length ? data.excludeTicketGroupName.join(",") : void 0, eqTicketGroupName: data.eqTicketGroupName instanceof Array && data.eqTicketGroupName.length ? data.eqTicketGroupName.join(",") : void 0, neTicketGroupName: data.neTicketGroupName instanceof Array && data.neTicketGroupName.length ? data.neTicketGroupName.join(",") : void 0 } : {}),
      ...([...(data.includeOrderSubclass instanceof Array ? data.includeOrderSubclass : []), ...(data.excludeOrderSubclass instanceof Array ? data.excludeOrderSubclass : []), ...(data.eqOrderSubclass instanceof Array ? data.eqOrderSubclass : []), ...(data.neOrderSubclass instanceof Array ? data.neOrderSubclass : [])].filter((v) => v).length ? { orderSubclassFilterRelation: data.orderSubclassFilterRelation === "OR" ? "OR" : "AND", includeOrderSubclass: data.includeOrderSubclass instanceof Array && data.includeOrderSubclass.length ? data.includeOrderSubclass.join(",") : void 0, excludeOrderSubclass: data.excludeOrderSubclass instanceof Array && data.excludeOrderSubclass.length ? data.excludeOrderSubclass.join(",") : void 0, eqOrderSubclass: data.eqOrderSubclass instanceof Array && data.eqOrderSubclass.length ? data.eqOrderSubclass.join(",") : void 0, neOrderSubclass: data.neOrderSubclass instanceof Array && data.neOrderSubclass.length ? data.neOrderSubclass.join(",") : void 0 } : {}),
      ...([...(data.includeCategory instanceof Array ? data.includeCategory : []), ...(data.excludeCategory instanceof Array ? data.excludeCategory : []), ...(data.eqCategory instanceof Array ? data.eqCategory : []), ...(data.neCategory instanceof Array ? data.neCategory : [])].filter((v) => v).length ? { categoryFilterRelation: data.categoryFilterRelation === "OR" ? "OR" : "AND", includeCategory: data.includeCategory instanceof Array && data.includeCategory.length ? data.includeCategory.join(",") : void 0, excludeCategory: data.excludeCategory instanceof Array && data.excludeCategory.length ? data.excludeCategory.join(",") : void 0, eqCategory: data.eqCategory instanceof Array && data.eqCategory.length ? data.eqCategory.join(",") : void 0, neCategory: data.neCategory instanceof Array && data.neCategory.length ? data.neCategory.join(",") : void 0 } : {}),
      ...([...(data.includeDesc instanceof Array ? data.includeDesc : []), ...(data.excludeDesc instanceof Array ? data.excludeDesc : []), ...(data.eqDesc instanceof Array ? data.eqDesc : []), ...(data.neDesc instanceof Array ? data.neDesc : [])].filter((v) => v).length ? { descFilterRelation: data.descFilterRelation === "OR" ? "OR" : "AND", includeDesc: data.includeDesc instanceof Array && data.includeDesc.length ? data.includeDesc.join(",") : void 0, excludeDesc: data.excludeDesc instanceof Array && data.excludeDesc.length ? data.excludeDesc.join(",") : void 0, eqDesc: data.eqDesc instanceof Array && data.eqDesc.length ? data.eqDesc.join(",") : void 0, neDesc: data.neDesc instanceof Array && data.neDesc.length ? data.neDesc.join(",") : void 0 } : {}),
      ...([...(data.includeName instanceof Array ? data.includeName : []), ...(data.excludeName instanceof Array ? data.excludeName : []), ...(data.eqName instanceof Array ? data.eqName : []), ...(data.neName instanceof Array ? data.neName : [])].filter((v) => v).length ? { nameFilterRelation: data.nameFilterRelation === "OR" ? "OR" : "AND", includeName: data.includeName instanceof Array && data.includeName.length ? data.includeName.join(",") : void 0, excludeName: data.excludeName instanceof Array && data.excludeName.length ? data.excludeName.join(",") : void 0, eqName: data.eqName instanceof Array && data.eqName.length ? data.eqName.join(",") : void 0, neName: data.neName instanceof Array && data.neName.length ? data.neName.join(",") : void 0 } : {}),
      ...([...(data.includeZoneId instanceof Array ? data.includeZoneId : []), ...(data.excludeZoneId instanceof Array ? data.excludeZoneId : []), ...(data.eqZoneId instanceof Array ? data.eqZoneId : []), ...(data.neZoneId instanceof Array ? data.neZoneId : [])].filter((v) => v).length ? { zoneIdFilterRelation: data.zoneIdFilterRelation === "OR" ? "OR" : "AND", includeZoneId: data.includeZoneId instanceof Array && data.includeZoneId.length ? data.includeZoneId.join(",") : void 0, excludeZoneId: data.excludeZoneId instanceof Array && data.excludeZoneId.length ? data.excludeZoneId.join(",") : void 0, eqZoneId: data.eqZoneId instanceof Array && data.eqZoneId.length ? data.eqZoneId.join(",") : void 0, neZoneId: data.neZoneId instanceof Array && data.neZoneId.length ? data.neZoneId.join(",") : void 0 } : {}),
    },
  });
}
/**
 * 新增工单模版
 */
export function addAllticketTemplates(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_templates/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
/**
 * 编辑工单模版
 */
export function setAllticketTemplates(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_templates/update`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
/**
 * 删除工单模版
 */
export function delAllticketTemplates(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_templates/${data.prodId}/delete`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * 工单模版添加设备
 */
export function ticketTemplateAddDevice(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_resource/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
/**
 * 工单模版删除设备
 */
export function ticketTemplateDelDevice(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_resource/delete`,
    method: Method.Delete,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * 获取工单模板配置
 */
export function getticketConfigurations(data: {} & RequestBase) {
  if (!data.ticketTemplatesId) return { success: true, data: { approval: true }, message: "success" }; // 发布详情调用，自动工单没有工单模板需要审批，默认true

  return request<unknown, Response<Record<string, any>>>({
    url: `${SERVER.EVENT_CENTER}/ticket_configuration/find?ticketTemplatesId=${data.ticketTemplatesId}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: {},
  });
}

/**
 * 更新工单模板配置
 */
export function setticketConfigurations(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_configuration/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * 删除工单模板配置
 */
export function delticketConfigurations(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_configuration/update`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * 工单模版下来添加联系人
 */
export function addticketcreate(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_contacts/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * 工单模版下来查询联系人
 */
export function getticketcontacts(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_contacts/findAll?ticketTemplatesId=${data.ticketTemplatesId}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * 工单模版下来删除联系人
 */
export function delticketcontacts(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_contacts/delete?id=${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * 工单模版下添加设备
 */
export function addticketresource(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_resource/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * 工单模版下来查询设备
 */
export function getticketresource(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_resource/findAll?ticketTemplatesId=${data.ticketTemplatesId}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * 工单模版下来删除设备
 */
export function delticketresource(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_contacts/delete?id=${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * 工单模版下添加设备规则
 */
export function addticketrule(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_resource_rule/create`,
    method: Method.Post,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}
/**
 * 工单模版下查询设备规则
 */
export function getticketrule(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_resource_rule/findAll?ticketTemplatesId=${data.ticketTemplatesId}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * 工单模版下来删除规则
 */
export function delticketrule(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_resource_rule/delete?id=${data.id}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * 查询工单组下面,用户组归属的不同客户
 */
export function getticketfindTenant(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_configuration/findTenant?ticketGroupId=${data.ticketGroupId}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * 查询工单组下面的用户组
 */
export function getticketfindUserGroup(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_configuration/findUserGroup?ticketGroupId=${data.ticketGroupId}&tenantId=${data.tenantId}&type=${data.type}&ticketType=${data.ticketType}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

/**
 * 移除工单组下面的用户组
 */
export function delticketfindUserGroup(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}//ticket_configuration/delete?id=${data.id}&userGroupsId=${data.userGroupsId}&userGroupsName=${data.userGroupsName}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

//查询变更管理下可选配置
export function findconfiguration(data: {} & RequestBase) {
  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/ticket_configuration/findconfiguration?tenantId=${data.tenantId}&ticketType=${data.ticketType}`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: {},
    data: data,
  });
}

// 获取模板下的用户组
export async function getconfigurationGroup(data: {} & RequestBase) {
  if (!data.ticketGroupId) {
    return Promise.resolve({ success: true, data: [], message: "true" });
  }

  return request<unknown, Response<Record<string, any>[]>>({
    url: `${SERVER.EVENT_CENTER}/user_group_configuration/queryAll`,
    method: Method.Get,
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: {},
    params: data,
    data: {},
  });
}
