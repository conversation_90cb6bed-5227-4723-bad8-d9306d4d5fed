import { SERVER, Method, type Response, type RequestBase } from "@/api/service/common";
import request from "@/api/service/index";

import { type UserItem } from "@/api/personnel";

import type { Tenants, TenantSecurityConfig } from "./tenant";

export interface UserGroupsOrgCurrent {
  id: string;
  tenantId: string;
  name: string;
  note: string;
  version: string;
  createdTime: string | number;
  updatedTime: string | number;
  createdBy: string;
  updatedBy: string;
}

export function getUserGroupByPage(data: { external?: boolean } & RequestBase) {
  return request<unknown, Response<UserGroupsOrgCurrent[]>>({
    url: `${SERVER.IAM}/current_org/user_groups`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: ["external"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function addUserGroup(data: Partial<UserGroupsOrgCurrent> & RequestBase) {
  return request<unknown, Response<UserGroupsOrgCurrent>>({
    url: `${SERVER.IAM}/current_org/user_groups`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "note"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function modUserGroup(data: Partial<UserGroupsOrgCurrent> & RequestBase) {
  return request<unknown, Response<UserGroupsOrgCurrent>>({
    url: `${SERVER.IAM}/user_groups/${data.id}`,
    method: Method.Put,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["name", "note"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function delUserGroup(data: Partial<UserGroupsOrgCurrent> & RequestBase) {
  return request<unknown, Response<UserGroupsOrgCurrent>>({
    url: `${SERVER.IAM}/user_groups/${data.id}`,
    method: Method.Delete,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 用户组添加用户
 * @export
 * @param {({ id: string; userIds: string[] } & RequestBase)} data
 * @return {*}
 */
export function addUserGroupMember(data: { id: string; userIds: string[] } & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.IAM}/user_groups/${data.id}/add_users`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["userIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 获取组下用户列表
 * @export
 * @param {({ id: string } & RequestBase)} data
 * @return {*}
 */
export function getGroupUsersByPage(data: { id: string } & RequestBase) {
  if (!data.id) return Promise.resolve({ success: true, data: [], message: "true" });
  return request<unknown, Response<UserItem>>({
    url: `${SERVER.IAM}/user_groups/${data.id}/user_infos`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["userIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function delUserGroupMember(data: { id: string; userIds: string[] } & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.IAM}/user_groups/${data.id}/remove_users`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["userIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 为用户组分配角色
 * @export
 * @param {({ userGroupIds: string[]; roleIds: string[] } & RequestBase)} data
 * @return {*}
 */
export function addUserGroupRole(data: { userGroupIds: string[]; roleIds: string[] } & RequestBase) {
  return request<unknown, Response<null>>({
    url: `${SERVER.IAM}/roles/current_org/assign_user_group_role`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["userGroupIds", "roleIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 获取用户组角色列表
 * @export
 * @param {({ id: string } & RequestBase)} data
 * @return {*}
 */
export function getUserGroupRole(data: Partial<UserItem> & RequestBase) {
  return request<unknown, Response<UserItem>>({
    url: `${SERVER.IAM}/user_groups/id/${data.id}/roles`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 用户组分配租户
 * @export
 * @param {({ groupIds: string[]; tenantIds: string } & RequestBase)} data
 * @return {*}
 */
export function addUserGroupTenant(data: { groupIds: string[]; tenantIds: string[] } & RequestBase) {
  return request<unknown, Response<Tenants>>({
    url: `${SERVER.IAM}/user_group/assign_tenant`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["groupIds", "tenantIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

/**
 * @name 解除用户组和租户的分配关系
 * @export
 * @param {({ groupIds: string[]; tenantIds: string } & RequestBase)} data
 * @return {*}
 */
export function userGroupUnassignTenant(data: { groupIds: string[]; tenantIds: string[] } & RequestBase) {
  return request<unknown, Response<Tenants>>({
    url: `${SERVER.IAM}/user_group/unassign_tenant`,
    method: Method.Post,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: ["groupIds", "tenantIds"].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}

export function userGroupAssignTenants(data: { id: string } & RequestBase) {
  return request<unknown, Response<Tenants>>({
    url: `${SERVER.IAM}/user_groups/${data.id}/assign_tenants`,
    method: Method.Get,
    responseType: "json",
    signal: data.controller instanceof AbortController ? data.controller.signal : undefined,
    headers: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    params: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
    data: [].reduce((p, key) => Object.assign(p, data[key] === undefined ? {} : { [key]: data[key] }), {}),
  });
}
