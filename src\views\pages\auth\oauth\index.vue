<template>
  <div></div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { onMounted } from "vue";
import { useRoute /* , useRouter */ } from "vue-router";
import { /* Method, */ SERVER } from "@/api/service/common";
// import axios, { HttpStatusCode } from "axios";

import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";

import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";

// const router = useRouter();
const route = useRoute();
const siteConfig = useSiteConfig();
const info = getInfo();

function getInfo() {
  switch (siteConfig.current) {
    case superBaseRoute.name:
      return useSuperInfo();
    case adminBaseRoute.name:
      return useAdminInfo();
    case usersBaseRoute.name:
      return useUsersInfo();
    default:
      return useUsersInfo();
  }
}

onMounted(async () => {
  const url = new URL(`${process.env["APP_AXIOS_BASE_URL"] || ""}${SERVER.IAM}/oauth2/server/authorize`, location.origin);
  Object.entries(route.query || {}).forEach(([key, value]) => {
    if (value instanceof Array) {
      for (let i = 0; i < value.length; i++) {
        url.searchParams.append(key, value[i] || "");
      }
    } else url.searchParams.append(key, value || "");
  });
  try {
    if (!info.userId) await info.updateInfo();
    url.searchParams.append("Authorization", info.token);
    window.location.replace(url);
  } catch (error) {
    await new Promise((resolve) => setTimeout(resolve, 10000));
    await info.logout();
  }
});
</script>

<style scoped></style>
