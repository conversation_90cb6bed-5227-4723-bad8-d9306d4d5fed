const formatDate = function (value: any, format: any) {
  if (!value) return false
  const date = new Date(Number(value))

  function addZero(date: any) {
    if (date < 10) {
      return '0' + date
    }
    return date
  }
  const getTime = {
    yyyy: date.getFullYear(),
    yy: date.getFullYear() % 100,
    MM: addZero(date.getMonth() + 1),
    M: date.getMonth() + 1,
    dd: addZero(date.getDate()),
    d: date.getDate(),
    HH: addZero(date.getHours()),
    H: date.getHours(),
    hh: addZero(date.getHours() % 12),
    h: date.getHours() % 12,
    mm: addZero(date.getMinutes()),
    m: date.getMinutes(),
    ss: addZero(date.getSeconds()),
    s: date.getSeconds(),
  }
  for (const i in getTime) {
    format = format.replace(i, getTime[i as keyof typeof getTime])
  }
  return format
}
export {
  formatDate, //通过此处导出方法
}
