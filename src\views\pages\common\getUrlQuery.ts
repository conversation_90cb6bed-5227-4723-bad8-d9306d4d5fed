export function getQueryString(name: string) {
  const query_string = window.location.href; // window.location.search
  if (!query_string) return null; // 如果无参，返回null
  const re = /[?&]?([^=]+)=([^&]*)/g;
  let tokens;
  // console.log('re.exec(query_string)', re.exec(query_string))

  // return re.exec(query_string) ? re.exec(query_string) || null
  while ((tokens = re.exec(query_string))) {
    // console.log('decodeURIComponent(tokens[1])', decodeURIComponent(tokens[1]))
    if (decodeURIComponent(tokens[1]) === name) {
      return decodeURIComponent(tokens[2]);
    }
  }
  return "";
}
