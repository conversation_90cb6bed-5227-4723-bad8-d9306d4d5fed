/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";
import { useRoute, useRouter } from "vue-router";
import { templateRef } from "@vueuse/core";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox } from "element-plus";
import { filter, find } from "lodash-es";
import moment from "moment";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
import { type ModuleItem as DataItem } from "@/views/pages/apis/index";
import { getModuleList as getItemList } from "@/views/pages/apis/index";
import { addModuleData as addItemData, setModuleData as setItemData, modModuleData as modItemData, delModuleData as delItemData } from "@/views/pages/apis/index";
/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */

interface Props {
  title?: string;
  pagination?: boolean;
}
export const props = withDefaults(defineProps<Props>(), { pagination: true, title: "告警" });

export const editorRef = templateRef("editorRef");
// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
export interface BaseItem {
  id: string;
}

export type Item = DataItem & BaseItem;

interface State<T> {
  loading: boolean;
  expand: string[];
  select: string[];
  current: string | undefined;
  search: Record<string, any>;
  list: T[];
  page: number;
  size: number;
  total: number;
}
export enum editCommand {
  Refresh,
  Request,
  Preview,
  Create,
  Update,
  Modify,
  Delete,
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
// const loading = ref<boolean>(false);
export const state = reactive<State<Item>>({
  loading: false,
  expand: [],
  select: [],
  current: undefined,
  search: {},
  list: [],
  page: 1,
  size: 30,
  total: 0,
});
export const dataList = computed(() => (props.pagination ? state.list.slice((state.page - 1) * state.size, state.page * state.size) : state.list));
export const expand = computed(() => filter<Item>(state.list, (row) => state.expand.includes(row.id)));
export const select = computed(() => filter<Item>(state.list, (row) => state.select.includes(row.id)));
export const current = computed(() => find<Item>(state.list, (row) => row.id === state.current));
// const name = computed(() => state.name);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

export function handleCommand(type: editCommand.Refresh): Promise<void>;
export function handleCommand(type: editCommand.Request): Promise<void>;
export function handleCommand(type: editCommand.Preview, data: BaseItem & Partial<Item>): Promise<void>;
export function handleCommand(type: editCommand.Create, data: BaseItem & Partial<Item>): Promise<void>;
export function handleCommand(type: editCommand.Update, data: BaseItem & Partial<Item>): Promise<void>;
export function handleCommand(type: editCommand.Modify, data: BaseItem & Partial<Item>): Promise<void>;
export function handleCommand(type: editCommand.Delete, data: BaseItem & Partial<Item>): Promise<void>;
export async function handleCommand(type: editCommand, data?: BaseItem & Partial<Item>) {
  try {
    state.loading = true;
    await nextTick();
    switch (type) {
      case editCommand.Refresh:
        await resetData();
        await queryData();
        break;
      case editCommand.Request:
        await queryData();
        break;
      case editCommand.Preview:
        break;
      case editCommand.Create:
        if (data) await createItem(data);
        break;
      case editCommand.Update:
        if (data) await rewriteItem(data);
        break;
      case editCommand.Modify:
        if (data) await modifyItem(data);
        break;
      case editCommand.Delete:
        if (data) await deleteItem(data);
        break;
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    state.loading = false;
  }
}

export async function handleRefresh() {
  try {
    state.loading = true;
    await nextTick();
    await resetData();
    await queryData();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    state.loading = false;
  }
}
export async function handleQuery() {
  try {
    state.loading = true;
    await nextTick();
    await queryData();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    state.loading = false;
  }
}
export async function resetData() {
  state.list = [];
  state.page = 1;
  state.size = 30;
  state.total = 0;
  for (const key in state.search) {
    if (Object.prototype.hasOwnProperty.call(state.search, key)) {
      delete state.search[key];
    }
  }
  await nextTick();
}
async function queryData() {
  const { success, message, data, page, size, total } = await getItemList({ ...state.search, paging: { pageNumber: state.page, pageSize: state.size } });
  if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
  state.list.splice(0, Infinity, ...(data instanceof Array ? data : []));
}
async function createItem(row: BaseItem & Partial<Item>) {
  if (!editorRef.value) return row;
}
async function rewriteItem(row: BaseItem & Partial<Item>) {
  if (!editorRef.value) return row;
}
async function modifyItem(row: BaseItem & Partial<Item>) {
  if (!editorRef.value) return row;
}
async function deleteItem(row: BaseItem & Partial<Item>) {
  if (!editorRef.value) return row;
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */
