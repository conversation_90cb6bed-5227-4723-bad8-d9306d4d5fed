import getUserInfo from "@/utils/getUserInfo";
import { Buffer } from "buffer";
const userInfo = getUserInfo();

const v6AuthorizeUrl = (function (url) {
  switch (url) {
    case "localhost:8080":
    case "*************:20082": // B4环境
      return {
        authorizeHost: "https://*************:20082",
        callbackHost: "http://*************:8080",
      };
    case "************:20082": // 155环境
      return {
        authorizeHost: "https://************:20082",
        callbackHost: "http://*************:8080",
      };
    case "*************": // 验收环境
      return {
        authorizeHost: "https://*************",
        callbackHost: "http://************:8080",
      };
    case "************": // 生产内网环境
      return {
        authorizeHost: "https://************",
        callbackHost: "http://************:8080",
      };
    case "www.ainoc.cn": // 生产环境
    case "unms.sst.net.cn": // 信天域名
      return {
        authorizeHost: "https://www.ainoc.cn",
        callbackHost: "https://netview.ctnetcare.com",
      };
    default:
      return {};
  }
})(location.host);

//采集机
export const routeJsonDCS_MANAGER = Object.assign(
  {
    redirectUrl: "/static/index.html#customerManager",
    currentTenant: userInfo.currentTenantId,
    type: "DCS_MANAGER",
    select: {
      tenant: "",
    },
  },
  v6AuthorizeUrl
);
//服务包

export const routeJsonNE_SRP_MANAGER = Object.assign(
  {
    redirectUrl: "/static/index.html#neServicePackageManager",
    currentTenant: userInfo.currentTenantId,
    type: "NE_SRP_MANAGER",
    select: {
      resource: "",
    },
  },
  v6AuthorizeUrl
);
//生产环境
// export const openUrl = "https://netview.ctnetcare.com/netcare/cloudcare/callback/sso";
//验收环境0
// export const openUrl = "http://************:8080/netcare/cloudcare/callback/sso";
// B4环境
// export const openUrl = "http://*************:8080/netcare/cloudcare/callback/sso";
// 155环境
// export const openUrl = "http://*************:8080/netcare/cloudcare/callback/sso";

export const openUrl = (function (url) {
  switch (url) {
    case "localhost:8080":
    case "*************:20082": // B4环境
      return "http://*************:8080/netcare/cloudcare/callback/sso";
    case "************:20082": // 155环境
      return "http://*************:8080/netcare/cloudcare/callback/sso";
    case "*************": // 验收环境
      return "http://************:8080/netcare/cloudcare/callback/sso";
    case "************": // 生产内网环境
      return "http://************:8080/netcare/cloudcare/callback/sso";
    case "www.ainoc.cn": // 生产环境
    case "unms.sst.net.cn": // 信天域名
      return "https://netview.ctnetcare.com/netcare/cloudcare/callback/sso";
    default:
      return "";
  }
})(location.host);

export function routerV6(id: string) {
  routeJsonNE_SRP_MANAGER.select.resource = id;
  routeJsonNE_SRP_MANAGER.currentTenant = userInfo.currentTenantId;

  // // console.log(process.env);
  const link = document.createElement("a");

  link.style.display = "none";
  link.target = "_blank";
  link.href = openUrl + "?redirectUrl=" + new Buffer(JSON.stringify(routeJsonNE_SRP_MANAGER), "utf8").toString("base64");
  // // console.log(openUrl + "?redirectUrl=" + new Buffer(JSON.stringify(routeJsonNE_SRP_MANAGER), "utf8").toString("base64"));
  link.setAttribute("download", "customer-template.xlsx");
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

export function routerV6Service(id: string) {
  routeJsonDCS_MANAGER.currentTenant = userInfo.currentTenantId;

  routeJsonDCS_MANAGER.select.tenant = id;

  const link = document.createElement("a");

  link.style.display = "none";
  link.target = "_blank";
  link.href = openUrl + "?redirectUrl=" + new Buffer(JSON.stringify(routeJsonDCS_MANAGER), "utf8").toString("base64");

  // link.setAttribute("download", "customer-template.xlsx");
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
const routeJsonNE_STATISTICS = Object.assign(
  {
    redirectUrl: "/static/index.html#statistics",
    currentTenant: userInfo.currentTenantId,
    type: "NE_STATISTICS",
    select: {
      resourceName: "", //设备名
    },
  },
  v6AuthorizeUrl
);

export function routerV6Busines(name: string, currentTenantId?: string) {
  routeJsonNE_STATISTICS.currentTenant = currentTenantId || userInfo.currentTenantId;
  routeJsonNE_STATISTICS.select.resourceName = encodeURIComponent(name);
  // currentTenantId
  const link = document.createElement("a");

  link.style.display = "none";
  link.target = "_blank";
  link.href = openUrl + "?redirectUrl=" + new Buffer(JSON.stringify(routeJsonNE_STATISTICS), "utf8").toString("base64");

  // link.setAttribute("download", "customer-template.xlsx");
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

const routeJsonNE_NETWORK = Object.assign(
  {
    redirectUrl: "/static/neBusinessCont.html",
    currentTenant: userInfo.currentTenantId,
    type: "NE_SERVER_OR_NETWORK",
    select: {
      resource: "", //设备id,
      businessId: "",
    },
  },
  v6AuthorizeUrl
);

export function routerV6Network(id: string, businessId: string) {
  routeJsonNE_NETWORK.currentTenant = userInfo.currentTenantId;
  routeJsonNE_NETWORK.select.resource = id;
  routeJsonNE_NETWORK.select.businessId = businessId;
  // currentTenantId
  const link = document.createElement("a");

  link.style.display = "none";
  link.target = "_blank";
  link.href = openUrl + "?redirectUrl=" + new Buffer(JSON.stringify(routeJsonNE_NETWORK), "utf8").toString("base64");

  // link.setAttribute("download", "customer-template.xlsx");
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

const routeJsonALLNE_NETWORK = Object.assign(
  {
    redirectUrl: "/static/index.html#neBusinessNetWork",
    currentTenant: userInfo.currentTenantId,
    type: "NE_NET_WORK",
    select: {},
  },
  v6AuthorizeUrl
);

export function routerV6NetworkAll() {
  routeJsonALLNE_NETWORK.currentTenant = userInfo.currentTenantId;

  // currentTenantId
  const link = document.createElement("a");

  link.style.display = "none";
  link.target = "_blank";
  link.href = openUrl + "?redirectUrl=" + new Buffer(JSON.stringify(routeJsonALLNE_NETWORK), "utf8").toString("base64");

  // link.setAttribute("download", "customer-template.xlsx");
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

//告警页面跳转v6查看全部历史告警
const alarmHistoryAll = Object.assign(
  {
    redirectUrl: "/static/index.html#eventHistoryAll",
    currentTenant: userInfo.currentTenantId,
    type: "CUS_HIS_ALERT", //枚举
    select: {
      tenant: "", //客户id
    },
  },
  v6AuthorizeUrl
);
// routeJsonDCS_MANAGER.select.tenant = id;

export function routerV6alarmHistoryAll(id: string) {
  alarmHistoryAll.currentTenant = userInfo.currentTenantId;
  alarmHistoryAll.select.tenant = id;

  // currentTenantId
  const link = document.createElement("a");

  link.style.display = "none";
  link.target = "_blank";
  link.href = openUrl + "?redirectUrl=" + new Buffer(JSON.stringify(alarmHistoryAll), "utf8").toString("base64");

  // link.setAttribute("download", "customer-template.xlsx");
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

//告警页面跳转v6查看设备历史告警
const deviceHistoryAll = Object.assign(
  {
    redirectUrl: "/static/index.html#neRootList",
    currentTenant: userInfo.currentTenantId,
    type: "NE_HIS_ALERT", //枚举
    select: {
      tenant: "", //客户id
      resource: "", //设备id
    },
  },
  v6AuthorizeUrl
);
// routeJsonDCS_MANAGER.select.tenant = id;

export function routerV6deviceHistoryAll(id: string) {
  deviceHistoryAll.currentTenant = userInfo.currentTenantId;
  deviceHistoryAll.select.tenant = userInfo.currentTenantId;
  deviceHistoryAll.select.resource = id;

  // currentTenantId
  const link = document.createElement("a");

  link.style.display = "none";
  link.target = "_blank";
  link.href = openUrl + "?redirectUrl=" + new Buffer(JSON.stringify(deviceHistoryAll), "utf8").toString("base64");

  // link.setAttribute("download", "customer-template.xlsx");
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

//工单页面跳转v6查看全部工单
const orderHistoryAll = Object.assign(
  {
    redirectUrl: "/static/index.html#homePage",
    currentTenant: userInfo.currentTenantId,
    type: "EVENT_CONSOLENE", //枚举
    select: {
      tenant: "", //客户id
    },
  },
  v6AuthorizeUrl
);

export function routerV6orderHistoryAll(id?: string) {
  orderHistoryAll.currentTenant = userInfo.currentTenantId;
  orderHistoryAll.select.tenant = userInfo.currentTenantId;

  // currentTenantId
  const link = document.createElement("a");

  link.style.display = "none";
  link.target = "_blank";
  link.href = openUrl + "?redirectUrl=" + new Buffer(JSON.stringify(orderHistoryAll), "utf8").toString("base64");

  // link.setAttribute("download", "customer-template.xlsx");
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

//事件管理页面跳转v6查看全部事件
const eventHistoryAll = Object.assign(
  {
    redirectUrl: "/static/index.html#incidentSearch",
    currentTenant: userInfo.currentTenantId,
    type: "EVENT_MANAGER", //枚举
    select: {
      tenant: "", //客户id
    },
  },
  v6AuthorizeUrl
);

export function routerV6eventHistoryAll(id?: string) {
  eventHistoryAll.currentTenant = userInfo.currentTenantId;
  eventHistoryAll.select.tenant = userInfo.currentTenantId;

  // currentTenantId
  const link = document.createElement("a");

  link.style.display = "none";
  link.target = "_blank";
  link.href = openUrl + "?redirectUrl=" + new Buffer(JSON.stringify(eventHistoryAll), "utf8").toString("base64");

  // link.setAttribute("download", "customer-template.xlsx");
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
