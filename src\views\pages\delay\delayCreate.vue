<template>
  <div>
    <el-dialog :title="title" v-model="dialogFormVisible" :before-close="cancel" width="35%">
      <el-form :model="form" :label-position="labelPosition" ref="ruleForm" :rules="rules">
        <el-form-item label="城市名称" :label-width="formLabelWidth" prop="name">
          <el-input v-model="form.name" style="width: 200px" autocomplete="off" show-word-limit clearable placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="接入方式" :label-width="formLabelWidth" prop="type">
          <el-select v-model="form.type" clearable placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.name" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="IP地址" :label-width="formLabelWidth" prop="ipAddress" :rules="[buildValidatorData({ name: 'required', title: 'IP地址' }), buildValidatorData({ name: 'address', title: 'IP地址' })]">
          <el-input v-model="form.ipAddress" style="width: 200px" autocomplete="off" show-word-limit clearable placeholder=""></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel()">取 消</el-button>
          <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { defineComponent } from "vue";
import { ElMessage } from "element-plus";
import { addDelay } from "@/views/pages/apis/delay";
import { buildValidatorData } from "@/utils/validate";

import { getTypeList } from "@/views/pages/apis/delay";
export default defineComponent({
  name: "supplierCreate",
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      buildValidatorData,
      form: {
        type: "",
        name: "",
        ipAddress: "",
      },
      rules: {
        ipAddress: [{ required: true, message: "请输入IP地址", trigger: "blur" }],
        name: [{ required: true, message: "请输入城市名称", trigger: "blur" }],
        type: [{ required: true, message: "请选择接入方式", trigger: "change" }],
      },
      options: [],
      dialogFormVisible: false,
      formLabelWidth: "130px",
      labelPosition: "left",
    };
  },
  watch: {
    // "dialog"(val) {
    //   this.dialogFormVisible = val;
    // },
    "form.report"(val) {
      this.form.report = val;
    },
  },
  created() {
    this.getGroups();
  },
  methods: {
    getGroups() {
      getTypeList({}).then((res) => {
        if (res.success) {
          res.data.forEach((v, i) => {
            this.options.push({
              name: v,
              value: v,
            });
          });
        }
      });
    },

    open(type, row) {
      // console.log(type);
      this.dialogFormVisible = true;
      // // console.log(type, row);
      this.type = type;

      this.title = "新增线路";
    },

    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          addDelay(this.form)
            .then((res) => {
              if (res.success) {
                ElMessage.success("新增成功");
                this.dialogFormVisible = false;
                this.$emit("dialogClose", false);
                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
              } else {
                this.dialogFormVisible = false;
                this.$emit("dialogClose", false);
                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
                ElMessage.error(JSON.parse(res.data)?.message);
              }
            })
            .catch((err) => {
              this.$message.error(err?.message);
            });

          // this.dialogFormVisible = false;
        } else {
          return false;
        }
      });
    },
    cancel() {
      this.dialogFormVisible = false;
      // this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      this.$refs["ruleForm"].clearValidate();
    },
    blurChange(data) {
      this.tags.push(data);
    },
    tagClose(index) {
      this.tags.splice(index, 1);
    },
  },
  expose: ["type", "disabled", "title", "form", "open"],
});
</script>
