<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }" class="delay">
    <el-row :gutter="24" style="margin-bottom: 15px">
      <el-col :span="5">
        <el-select v-model="form.cloudSiteId" style="width: 100%" @change="cloudSiteChange">
          <el-option label="香港阿里云:香港CTVPN58063B" value="1"></el-option>
          <el-option label="东京 AWS:东京CTVPN58063A" value="2"></el-option>
        </el-select>
      </el-col>
      <el-col :span="7">
        单次测试
        <el-select style="width: 100px" v-model="form.count">
          <el-option label="5" value="5"></el-option>
          <el-option label="100" value="100"></el-option>
          <el-option label="200" value="200"></el-option>
        </el-select>
        个包
        <el-button type="primary" style="margin-left: 5px" @click="PingMessgae('odd')">测试</el-button>
      </el-col>
      <el-col :span="9">
        连续测试
        <el-select style="width: 100px" v-model="continua.order">
          <el-option label="1" value="1"></el-option>
          <el-option v-if="continua.time && continua.time != 60" label="5" value="5"></el-option>
          <el-option v-if="continua.time && continua.time != 60" label="10" value="10"></el-option>
          <el-option v-if="continua.time && continua.time != 60" label="15" value="15"></el-option>
          <el-option v-if="continua.time && continua.time != 60" label="30" value="30"></el-option>
        </el-select>
        <el-select style="width: 100px; margin-left: 5px" v-model="continua.time" @change="timeChange">
          <el-option label="Second" value="1"></el-option>
          <el-option label="Min" value="60"></el-option>
        </el-select>
        <el-button style="margin-left: 5px" type="primary" @click="PingMessgae('even')">测试</el-button>
      </el-col>
      <el-col :span="3" style="text-align: right">
        <span class="tw-h-fit">
          <el-button v-if="userInfo.hasPermission(系统管理中心_延时列表_新建线路)" type="primary" :icon="Plus" @click="handleCreate('add')">{{ $t("glob.add") }}线路</el-button>
        </span>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="13">
        <template #default="{ height: tableHeight }">
          <el-scrollbar :height="`${height - 40 - 55}px`">
            <el-table :data="tableData" :height="tableHeight" :default-sort="state.sort" style="width: 100%" class="delay-table">
              <el-table-column align="left" width="80" prop="name" :label="labelFn('城市City')">
                <template #default="{ row }">
                  <el-popover trigger="hover" placement="top">
                    <div>{{ row.address }}</div>
                    <template #reference>
                      <span>
                        {{ row.name }}
                      </span>
                    </template>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column align="left" prop="code" :label="labelFn('接入方式', 'Circuit Type')" width="100" :formatter="formatterTable"> </el-table-column>
              <el-table-column align="left" prop="pingNum" :label="labelFn('Ping包数', 'Ping Test', 'Package Count')" :formatter="formatterTable"> </el-table-column>
              <el-table-column align="left" prop="min" :label="labelFn('最小时延', 'Minimum', 'Latency')" width="85" :formatter="formatterTable"> </el-table-column>
              <el-table-column align="left" prop="avg" :label="labelFn('平均时延', 'Average', 'Latency')" width="85" :formatter="formatterTable"> </el-table-column>
              <el-table-column align="left" prop="max" :label="labelFn('最大时延', 'Maximum', 'Latency')" width="85" :formatter="formatterTable"> </el-table-column>
              <el-table-column align="left" prop="stddev" :label="labelFn('抖动', 'Jitter')" width="60" :formatter="formatterTable"> </el-table-column>
              <el-table-column align="left" prop="loss" :label="labelFn('丢包率', 'Lost', 'Rate')" width="70" :formatter="formatterTable"></el-table-column>
              <el-table-column align="left" prop="operation" :label="labelFn('操作')" width="60">
                <template #default="{ row }">
                  <span>
                    <el-link v-if="userInfo.hasPermission(系统管理中心_延时列表_删除线路)" type="danger" style="font-size: 12px" :underline="false" @click="delDelay(row)"> 删除 </el-link>
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </el-scrollbar>
        </template>
      </el-col>
      <el-col :span="11">
        <div class="second" :style="{ height: `${height - 40 - 50}px` }">
          <img src="../assets/delay/delayImg.jpg" alt="" />
        </div>
      </el-col>
    </el-row>
  </el-card>
  <delayCreate :dialog="dialog" ref="delayRef" @dialogClose="dialogClose"></delayCreate>
</template>

<script setup lang="ts" generic="T extends object">
/* eslint-disable @typescript-eslint/no-unused-vars */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useStorage } from "@vueuse/core";
import { useI18n } from "vue-i18n";

import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete, Message } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMessageBox, ElText } from "element-plus";
import pageTemplate from "@/components/pageTemplate.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import delayCreate from "./delayCreate.vue";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";
import { slaDownStatus } from "./common";
import { AddSlaDownConfig, SlaDownConfigEdit } from "@/views/pages/apis/SlaConfig";
import { state, dataList, expand, select, current } from "./helper";
import { resetData, handleExpand, handleSort, command } from "./helper";

import { getDelayLineList, type DelayList as DataItem, delayDel } from "@/views/pages/apis/delay";
import { faBullseye } from "@fortawesome/free-solid-svg-icons";
import querystring from "querystring";
import requestEv from "./eventSrouce";
import axios from "axios";
import { 系统管理中心_延时列表_新建线路, 系统管理中心_延时列表_删除线路 } from "@/views/pages/permission";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "SlaDownConfig" });
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));
const activeName = ref("first");
const dialog = ref(false);

const siteConfig = useSiteConfig();
const userInfo = getUserInfo();
interface Item {
  cloudSiteId: string /*  云站点Id */;
  count: string /** 发包数量 */;
  durationSeconds: string /** 持续ping时间 */;
}
const form = ref<Item>({
  cloudSiteId: "1" /*  云站点Id */,
  count: "5" /** 发包数量 */,
  durationSeconds: "" /** 持续ping时间 */,
});
const continua = ref({
  order: "10",
  time: "1",
});

const CancelToken = axios.CancelToken;
const source = CancelToken.source();
// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
// const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);
interface storageDelayList {
  cloudSiteId: string /*  云站点Id */;
  date: string /** 缓存时间 */;
  arr1: string /** 缓存数据 */;
  arr2: string /** 缓存数据 */;
}
const delayObj = ref<storageDelayList>({
  cloudSiteId: "" /*  云站点Id */,
  date: "" /** 缓存时间 */,
  arr1: [] /** 缓存数据 */,
  arr2: [] /** 缓存数据 */,
});
const tableLoading = ref(false);

// 搜索关键字
const ServiceSearch = ref("");
const degradeStatus = ref(null);
const tableData = ref<DataItem[]>([]);
const delayList = useStorage<storageDelayList[]>("storageDelayList", () => {});
const seconDdelayList = useStorage<storageDelayList[]>("seconDdelayList", () => {});

const paging = reactive({
  pageNumber: 1,
  pageSize: 50,
  total: 0,
});

const defaultList = ref([]);
// if (delayList.value.length == 0) {

// }

const date = ref(new Date().getTime());

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  getList("");
}

function labelFn(text, text2, text3) {
  // 在需要换行的地方加入换行符 \n  ，在搭配最底下的white-space样式设置
  return `${text}\n${text2 || ""}\n${text3 || ""}`;
}

function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
//搜索
function getList(type) {
  getSlaDownList(type);
}

const controller = ref(new AbortController());
function formatterTable(_row, _col, v) {
  // // console.log(_row, _col, v, 55555);
  switch (_col.property) {
    default:
      return v || "--";
    case "loss":
      // // console.log(v);
      return v != undefined ? v + "%" : "--";
    case "name":
      return v + "   " + _row.address;
  }
}
// //默认值更改
// function statusChange(row: Partial<Record<string, any>>) {
//   SlaDownConfigStatus({
//     id: row.id,
//     defaultable: row.defaultRule,
//   })
//     .then((res) => {
//       if (res.success) {
//         ElMessage.success("默认状态修改成功");
//         getSlaDownList();
//       }
//     })
//     .catch((e) => {
//       ElMessage.error(e.message);
//     });
// }
//启禁用服务

function timeChange(val) {
  if (Number(val) === 60) continua.value.order = "1";
}

function getSlaDownList(type) {
  tableLoading.value = true;

  getDelayLineList({})
    .then(async ({ success, data }) => {
      if (success) {
        tableData.value = data.map((v) => ({ ...v, controller: new AbortController() }));
        tableLoading.value = false;
        defaultList.value = data.map((v) => ({ ...v, controller: new AbortController() }));

        setShowList();

        // PingMessgae(type);
      }
    })
    .catch(() => {
      tableLoading.value = false;
    });
}

function cloudSiteChange() {
  source.cancel("");

  controller.value.abort();

  setShowList();
}

function setShowList() {
  tableData.value.forEach((v, i) => {
    if (form.value.cloudSiteId === "1") {
      if (delayList.value != undefined && delayList.value != "undefined") {
        JSON.parse(delayList.value).arr.forEach((item, index) => {
          var difference = Math.abs(date.value - JSON.parse(delayList.value).date);
          var hours = Math.floor(difference / (1000 * 60 * 60));
          if (hours < 24) {
            if (v.id === item.id) {
              tableData.value[i] = { ...item, controller: new AbortController() };
            }
          }
        });
      } else {
        tableData.value = [...defaultList.value];
      }
    } else {
      if (seconDdelayList.value != undefined && seconDdelayList.value != "undefined") {
        JSON.parse(seconDdelayList.value).arr.forEach((item, index) => {
          var difference = Math.abs(date.value - JSON.parse(seconDdelayList.value).date);
          var hours = Math.floor(difference / (1000 * 60 * 60));
          if (hours < 24) {
            if (v.id === item.id) {
              tableData.value[i] = { ...item, controller: new AbortController() };
            }
          } else {
            // getList("");
          }
        });
      } else {
        tableData.value = [...defaultList.value];
      }
    }
  });
}

async function PingMessgae(type) {
  controller.value.abort();
  controller.value = new AbortController();
  if (form.value.cloudSiteId == "") {
    return ElMessage.warning("请选择云站点");
  }

  let ids = "";
  tableData.value.forEach(async (item, index) => {
    if (((item.controller || {}).signal || {}).aborted) return;
    item.controller.abort();

    Object.assign(item, { controller: new AbortController() });
    ids += item.id + ",";
  });

  try {
    const url = new URL("/gw_ping/ping_tool/ping/sse/batch", location.origin);
    const headers = new Headers();
    url.searchParams.set("lineIds", ids);
    url.searchParams.set("cloudSiteId", form.value.cloudSiteId);
    if (type === "odd") {
      url.searchParams.set("count", form.value.count);
    } else {
      url.searchParams.set("durationSeconds", continua.value.order * continua.value.time);
    }
    headers.set("Authorization", userInfo.token);
    // if (((continua.value.order && continua.value.time) || form.value.count) && form.value.cloudSiteId) {
    console.log("continua.value.order", continua.value.order, "&&", "continua.value.time", continua.value.time);
    console.log("form.value.count", form.value.count);
    console.log("form.value.cloudSiteId", form.value.cloudSiteId);

    const res = await fetch(url, { method: "get", headers, cache: "no-cache", signal: controller.value.signal, cancelToken: source.token });
    if (res.body) {
      const decoder = new TextDecoderStream("utf-8");
      const stream = res.body.pipeThrough(decoder);
      const reader = stream.getReader();
      let read: ReadableStreamReadResult<string>;
      const textStream = [];
      while (!(read = await reader.read()).done) {
        textStream.push(read.value);
        const data: Record<string, string>[] = textStream
          .join("")
          .split("\n\n")
          .filter((v) => v)
          .map((v) => {
            const item = v
              .split("\n")
              .filter((v) => v)
              .reduce<Record<string, string>>((p, c) => {
                const { groups } = /^(?<name>[a-z]*)\:(?<data>.*)$/g.exec(c) || {};
                if (groups) {
                  const { name, data } = groups;
                  if (name) {
                    if (name in p && p[name]) p[name] = `${p[name]}\n${data}`;
                    else Object.assign(p, { [name]: data });
                  }
                }
                return p;
              }, {});
            return item;
          });
        console.log(data);
        data.forEach((v, i) => {
          if (v.data) {
            const json = JSON.parse(v.data || "");

            const arr = JSON.parse(json.data);

            tableData.value.forEach(async (item, index) => {
              if (item.id == json.lineId) {
                Object.assign(item, {
                  pingNum: arr.statistics.transmitted,
                  min: arr.statistics.min,
                  avg: arr.statistics.avg,
                  max: arr.statistics.max,
                  stddev: arr.statistics.stddev,
                  loss: arr.statistics.loss,
                  cloudSiteId: form.value.cloudSiteId,
                  received: arr.statistics.received,
                });
              }
            });
          }
        });
        // for (let i = data.length - 1; i > -1; i--) {
        //   // console.log(i);

        //   if (!raw) continue;
        //   try {

        //     break;
        //   } catch (error) {
        //     continue;
        //   }
        // }
        if (form.value.cloudSiteId === "1") {
          delayList.value = JSON.stringify({
            date: new Date().getTime(),
            arr: tableData.value.map((v) => ({ ...v, controller: undefined })),
            cloudSiteId: form.value.cloudSiteId,
          });
          // delayList.value = JSON.stringify(delayObj.value);
        } else {
          seconDdelayList.value = JSON.stringify({
            date: new Date().getTime(),
            arr: tableData.value.map((v) => ({ ...v, controller: undefined })),
            cloudSiteId: form.value.cloudSiteId,
          });
        }

        // delayList.value = tableData.value.map((v) => ({ ...v, controller: undefined, date: new Date().getTime() }));
      }

      reader.cancel();

      // textStream = [];
    }
    // }
  } catch (error) {
    console.error(error);
  }
}

function delDelay(item) {
  ElMessageBox.confirm(`确定删除线路"${item.name}"?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        delayDel({
          id: item.id,
        }).then((res) => {
          if (res.success) {
            ElMessage.success("删除成功");
            getSlaDownList("");
          }
        });
      } catch (error) {
        error instanceof Error && ElMessage.error(error.message);
      }
    })
    .catch((error) => {
      //
    });
}

async function handleCreate(type, row) {
  // ctx.refs.delayRef.form.report = false;
  setTimeout(() => {
    ctx.refs.delayRef.open(type, row);
  }, 200);
  dialog.value = true;
}
function dialogClose(bool) {
  dialog.value = bool;
  getSlaDownList();
  // ctx.refs.delayRef.form.report = false;
}
//删除关联类型

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.delay-table {
  :deep(.cell) {
    white-space: pre;
    font-size: 12px !important;
    padding-right: 0 !important;
  }
}
.delay {
  :deep(.el-table th.el-table__cell) {
    > .cell {
      white-space: pre;
      // white-space: pre-wrap; // 也行。
    }
  }
}
.delay {
  :deep(.el-scrollbar) {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}

.second {
  width: 100%;
  height: 100%;
  // display: flex;
  // flex-direction: column;
  // justify-content: center;
  img {
    width: 100%;
    height: auto;
  }
}
</style>
