<template>
  <div id="desktop">
    <div class="desk">
      <div class="search-bar">
        <el-input v-model="state.search.name" placeholder="搜索应用..." clearable @keyup.enter="handleSearch()">
          <template #append>
            <el-button :icon="Search" @click="handleSearch()" />
          </template>
        </el-input>
      </div>
      <div class="desktop-container">
        <div class="appBtn" v-for="(item, i) in state.searchList" :key="i" @click="toApp(item)">
          <el-avatar>
            <Icon size="28" :name="JSON.parse(item.config).icon" color="inherit" />
          </el-avatar>
          <span class="appTitle">{{ item.title }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts" name="desktop">
import { Search } from "@element-plus/icons-vue";
import { getCurrentInstance, nextTick, computed, onActivated, onBeforeMount, onBeforeUnmount, onBeforeUpdate, onDeactivated, onMounted, onUnmounted, onUpdated, ref, reactive } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { logout } from "@/api/system";
import router from "@/router";
import { useI18n } from "vue-i18n";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
import { handleRoute, getFirstRoute } from "@/utils/router";
import { useNavTabs } from "@/stores/navTabs";
import { useSiteConfig } from "@/stores/siteConfig";
import { useConfig } from "@/stores/config";
import { adminBaseRoute, superBaseRoute, usersBaseRoute } from "@/router/common";
import { getUserAvailableApps, type NavItem } from "@/api/system";
const { t } = useI18n();

const superInfo = useSuperInfo();
const adminInfo = useAdminInfo();
const usersInfo = useUsersInfo();

const siteConfig = useSiteConfig();
const configStore = useConfig();
// const { proxy } = useCurrentInstance();

const userInfo = computed(() => {
  switch (siteConfig.current) {
    case superBaseRoute.name:
      return superInfo;
    case adminBaseRoute.name:
      return adminInfo;
    case usersBaseRoute.name:
      return usersInfo;
    default:
      return usersInfo;
  }
});

const navTabs = useNavTabs();

interface StateData<T> {
  loading: boolean;
  search: { [key: string]: string };
  appList: T[];
  searchList: T[];
  leftList: T[];
}
const state = reactive<StateData<NavItem>>({
  loading: false,
  search: {
    name: "",
  },
  appList: [],
  searchList: [],
  leftList: [],
});

onMounted(() => {
  getAppList();
});
async function getAppList() {
  let left = ["个人中心", "用户权限", "租户管理"],
    appList: any = [],
    leftList: any = [];
  if (state.loading) return;
  state.loading = true;
  await nextTick();
  const res = await getUserAvailableApps({});
  res.data.map((it) => {
    if (left.includes(it.title)) {
      leftList.push(it);
    } else {
      appList.push(it);
    }
  });
  state.leftList = leftList;
  state.appList = appList;
  state.searchList = appList;
  state.loading = false;
}
function handleSearch() {
  let searchList: any = [];
  if (state.search.name.trim().length > 0) {
    searchList = state.appList.filter((it: any) => it.title.includes(state.search.name.trim()));
  } else {
    searchList = state.appList;
  }
  state.searchList = searchList;
}
function toApp(item: any) {
  let href = "";
  if (/^(http:|https:|\/\/)/.test(item.path)) {
    href = item.path;
  } else if (item.path) {
    const $route = router.resolve(item.path);
    href = $route.href;
  }
  const link = document.createElement("a");
  link.href = href;
  link.target = "_blank";
  link.style.setProperty("display", "none");
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
function onLogout() {
  ElMessageBox.confirm(`确定退出登录?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      nextTick(async () => {
        try {
          const res = await logout({});
          if (res.success) {
            router.go(0);
          } else throw Object.assign(new Error(res.message), res);
        } catch (error) {
          router.replace({ path: "/" });
          if (error instanceof Error) ElMessage.error(error.message);
          return error;
        } finally {
          userInfo.value.handleLogout();
        }
      });
    })
    .catch(() => {
      // console.log("取消登录");
    });
}
</script>

<style scoped lang="scss">
#desktop {
  width: 100%;
  height: 100%;
  background: url("@/assets/bg.png") no-repeat center / cover;
}
.desk {
  z-index: 1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  .search-bar {
    position: absolute;
    height: 33px;
    width: 212px;
    top: 15px;
    right: 90px;
    z-index: 20;
    :deep(.el-input__wrapper),
    :deep(.el-input-group__append) {
      background: #ffffff88;
    }
    :deep(.el-input__wrapper.is-focus) {
      box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
    }
  }
  .desk-bar {
    position: absolute;
    width: 80px;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 1;
    .dock-container {
      position: absolute;
      width: 80px;
      height: 620px;
      top: 50%;
      left: 0;
      margin: -310px 0 0;
      z-index: 0;
      background: #ffffff88;
      padding: 14px 11px;
      display: flex;
      flex-direction: column;
      align-items: center;
      color: var(--el-text-color-primary);
      justify-content: space-between;
      .dock-appList {
        .appBtn {
          width: 68px;
          height: 90px;
          text-align: center;
          cursor: pointer;
          :deep(.el-avatar) {
            display: block;
            width: 48px;
            height: 48px;
            line-height: 43px;
            margin: 10px;
          }
          .appTitle {
            line-height: 22px;
            font-size: 12px;
          }
        }
      }
      .exit {
        cursor: pointer;
      }
    }
  }
}
.desk,
.desk .desktop-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.desktop-container {
  padding: 72px 20px 20px 100px;
  .appBtn {
    display: inline-block;
    width: 86px;
    height: 88px;
    text-align: center;
    cursor: pointer;
    :deep(.el-avatar) {
      display: block;
      width: 48px;
      height: 48px;
      margin: 8px auto 10px;
      line-height: 43px;
      background: var(--el-bg-color);
      font-size: 20px;
      color: var(--el-color-primary);
    }
    .appTitle {
      display: inline-block;
      max-width: 88px;
      line-height: 20px;
      font-size: 12px;
      background: rgba(0, 0, 0, 0.6);
      padding: 0 8px;
      border-radius: 10px;
      color: var(--el-bg-color);
    }
  }
}
</style>
