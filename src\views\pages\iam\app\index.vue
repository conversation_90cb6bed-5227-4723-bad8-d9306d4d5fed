<template>
  <el-container>
    <el-main :key="`active_view-${state.type}`" class="view-main" :style="{ height: `${height}px`, display: 'flex' }">
      <el-card :style="{ width: '260px', marginRight: '4px', border: '0', borderTopRightRadius: '0', borderBottomRightRadius: '0' }" :body-style="{ padding: '0px' }">
        <template #header>
          <div class="main-header">
            <div class="left">应用</div>
            <div class="center"></div>
            <div class="right">
              <el-tooltip :content="`${$t('glob.refresh')}应用列表`" placement="top">
                <el-link v-blur :underline="false" type="info" :icon="Refresh" @click="refreshState()"></el-link>
              </el-tooltip>
              <el-tooltip :content="`${$t('glob.add')}应用`" placement="top">
                <el-link v-blur :underline="false" type="primary" :icon="Plus" @click="createState({})"></el-link>
              </el-tooltip>
            </div>
          </div>
        </template>
        <template #default>
          <el-scrollbar v-loading="state.loading" :height="height - 91" :view-style="{ padding: '6px 3px' }">
            <draggable class="main-list" handle=".move_bar" group="main-list" tag="ul" v-model="state.data" animation="300" ghostClass="ghost" chosenClass="chosen" item-key="name" @change="reSlot">
              <template #item="{ element: row }">
                <li :key="row.name" :class="['main-item', { active: isEqual(row.name, state.active) }]" @click="state.active = row.name">
                  <el-avatar>
                    <Icon :name="row.icon" color="inherit" />
                  </el-avatar>
                  <el-tooltip :show-after="1000" :disabled="!row.note">
                    <template #content>
                      <pre>{{ row.note }}</pre>
                    </template>
                    <template #default>
                      <div class="main-item-body" style="flex-shrink: 1">
                        <div class="main-item-title">{{ row.title }}</div>
                        <div class="main-item-code">{{ row.name }}</div>
                      </div>
                    </template>
                  </el-tooltip>
                  <div class="tw-mr-[6px] tw-flex tw-w-[60px] tw-flex-col tw-items-center tw-justify-between">
                    <el-tag v-show="row.type === appType.DIR" :type="'success'" size="small" effect="dark">目录</el-tag>
                    <el-tag v-show="row.type === appType.MENU" :type="void 0" size="small" effect="dark">菜单项</el-tag>
                    <el-tag v-show="row.type === appType.ROUTE" :type="void 0" size="small" effect="dark">路由</el-tag>
                    <el-tag v-show="row.type === appType.MICRO" :type="void 0" size="small" effect="dark">微应用</el-tag>
                    <el-tag v-show="row.type === appType.LINK" :type="'info'" size="small" effect="dark">链接</el-tag>
                    <el-tag v-show="row.type === appType.BUTTON" :type="'warning'" size="small" effect="dark">按钮</el-tag>
                  </div>
                  <div class="main-item-btn-group" @click.stop>
                    <el-tooltip :content="$t('glob.edit')" placement="top">
                      <el-link :underline="false" type="primary" :icon="Edit" @click="editorState(row)"></el-link>
                    </el-tooltip>
                    <el-tooltip :content="$t('glob.delete')" placement="top">
                      <el-link :underline="false" type="danger" :icon="Delete" @click="deleteState(row)"></el-link>
                    </el-tooltip>
                    <el-link type="info" :underline="false" class="move_bar" :icon="Sort" @click.stop></el-link>
                  </div>
                </li>
              </template>
            </draggable>
          </el-scrollbar>
        </template>
      </el-card>
      <el-container>
        <el-header class="el-card" height="fit-content" :style="{ marginBottom: '4px', padding: '0', border: '0', borderBottomLeftRadius: '0', borderBottomRightRadius: '0' }">
          <el-menu :key="`active_type-${state.type}`" mode="horizontal" class="tab-menu" :default-active="state.type" style="width: 100%; --el-menu-item-height: 42px">
            <el-menu-item index="menu" @click="state.type = ($event.index as MenuType) || ''">
              <template #default>
                <el-icon>
                  <Menu />
                </el-icon>
              </template>
              <template #title>菜单</template>
            </el-menu-item>
            <el-menu-item index="auth" @click="state.type = ($event.index as MenuType) || ''">
              <template #default>
                <el-icon>
                  <Lock />
                </el-icon>
              </template>
              <template #title>权限-目录</template>
            </el-menu-item>
            <el-menu-item index="group" @click="state.type = ($event.index as MenuType) || ''">
              <template #default>
                <el-icon>
                  <Lock />
                </el-icon>
              </template>
              <template #title>权限-分组/配置项</template>
            </el-menu-item>
            <el-menu-item index="template" @click="state.type = ($event.index as MenuType) || ''">
              <template #default>
                <el-icon>
                  <Lock />
                </el-icon>
              </template>
              <template #title>模板</template>
            </el-menu-item>
          </el-menu>
        </el-header>
        <el-card :style="{ width: `${width - 264}px`, border: '0', borderTopLeftRadius: '0', borderTopRightRadius: '0', borderBottomLeftRadius: '0' }" :body-style="{ padding: '10px 0' }">
          <template v-if="!currentActive">
            <el-scrollbar>
              <div class="flex-search" :style="{ minWidth: `${width - 266}px` }">
                <div class="left">
                  <el-button type="primary" @click="open({ multiple: false, accept: 'application/json', reset: true })"> {{ $t("glob.Import") }}应用 </el-button>
                </div>
                <div class="center"></div>
                <div class="right"></div>
              </div>
            </el-scrollbar>
            <el-empty description="未找到相关信息" />
          </template>
          <template v-else-if="state.type === 'menu'">
            <DetailAppMenu key="active_menu" :data="currentActive" :width="width - 264" :height="height - 48">
              <template #left="{ refresh }">
                <el-tooltip :content="$t('glob.refresh')" placement="top">
                  <el-button v-blur color="#40485b" class="table-header-operate" type="info" :icon="Refresh" @click="refresh()"></el-button>
                </el-tooltip>
                <el-button type="primary" @click="open({ multiple: false, accept: 'application/json', reset: true })"> {{ $t("glob.Import") }}应用 </el-button>
                <el-button type="primary" @click="exportApp({ rootId: currentActive.name })"> {{ $t("glob.Export") }}应用 </el-button>
              </template>
              <template #right="{ create }">
                <el-button type="primary" :icon="Plus" @click="create({ rootId: currentActive.name })"> {{ $t("glob.add") }}菜单 </el-button>
                <el-button type="primary" :icon="Refresh" @click="$router.replace({ name: `${siteConfig.baseInfo?.name}Loading`, query: $route.query, params: { path: $route.path.split('/').filter((v) => `/${v}` !== siteConfig.baseInfo?.path) } })">
                  {{ $t("glob.ReStart Router") }}
                </el-button>
                <el-tooltip content="权限规则修改后Web不会立即生效，只有后台功能会生效，为了同步，请重启路由同步">
                  <el-icon :size="18" color="var(--el-color-info-light-3)" style="vertical-align: middle">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </template>
            </DetailAppMenu>
          </template>
          <template v-else-if="state.type === 'auth'">
            <ServeAuth key="active_auth" :data="currentActive" :width="width - 264" :height="height - 48">
              <template #left="{ refresh }">
                <el-tooltip :content="$t('glob.refresh')" placement="top">
                  <el-button v-blur color="#40485b" class="table-header-operate" type="info" :icon="Refresh" @click="refresh()"></el-button>
                </el-tooltip>
                <el-button type="primary" @click="open({ multiple: false, accept: 'application/json', reset: true })"> {{ $t("glob.Import") }}应用 </el-button>
                <el-button type="primary" @click="exportApp({ rootId: currentActive.name })"> {{ $t("glob.Export") }}应用 </el-button>
              </template>
              <template #right="{ create }">
                <el-button type="primary" :icon="Plus" @click="create({ appId: currentActive.name })">{{ $t("glob.add") }}权限分组</el-button>
              </template>
            </ServeAuth>
          </template>
          <template v-else-if="state.type === 'group'">
            <!-- <allocation key="active_auth" :data="currentActive" :width="width - 264" :height="height - 48">
              <template #left="{ refresh }">
                <el-tooltip :content="$t('glob.refresh')" placement="top">
                  <el-button v-blur color="#40485b" class="table-header-operate" type="info" :icon="Refresh" @click="refresh()"></el-button>
                </el-tooltip>
                <el-button type="primary" @click="open({ multiple: false, accept: 'application/json', reset: true })"> {{ $t("glob.Import") }}应用 </el-button>
                <el-button type="primary" @click="exportApp({ rootId: currentActive.name })"> {{ $t("glob.Export") }}应用 </el-button>
              </template>
              <template #right="{ create }">
                <el-button type="primary" :icon="Plus" @click="create({ appId: currentActive.name })">{{ $t("glob.add") }}权限分组</el-button>
              </template>
            </allocation> -->
            <AppAuthGroup key="active_group" :data="currentActive" :width="width - 264" :height="height - 48">
              <template #left="{ refresh }">
                <el-tooltip :content="$t('glob.refresh')" placement="top">
                  <el-button v-blur color="#40485b" class="table-header-operate" type="info" :icon="Refresh" @click="refresh()"></el-button>
                </el-tooltip>
                <el-button type="primary" @click="open({ multiple: false, accept: 'application/json', reset: true })"> {{ $t("glob.Import") }}应用 </el-button>
                <el-button type="primary" @click="exportApp({ rootId: currentActive.name })"> {{ $t("glob.Export") }}应用 </el-button>
              </template>
              <template #right="{ create }">
                <el-button type="primary" :icon="Plus" @click="create({ appId: currentActive.name })">{{ $t("glob.add") }}权限分组</el-button>
              </template>
            </AppAuthGroup>
          </template>
          <template v-else-if="state.type === 'template'">
            <AppAuthTemplate key="active_template" :data="currentActive" :width="width - 264" :height="height - 48">
              <template #left="{ refresh }">
                <el-tooltip :content="$t('glob.refresh')" placement="top">
                  <el-button v-blur color="#40485b" class="table-header-operate" type="info" :icon="Refresh" @click="refresh()"></el-button>
                </el-tooltip>
                <el-button type="primary" @click="open({ multiple: false, accept: 'application/json', reset: true })"> {{ $t("glob.Import") }}应用 </el-button>
                <el-button type="primary" @click="exportApp({ rootId: currentActive.name })"> {{ $t("glob.Export") }}应用 </el-button>
              </template>
              <template #right="{}"></template>
            </AppAuthTemplate>
          </template>
        </el-card>
      </el-container>
    </el-main>
  </el-container>
  <EditorForm ref="editorRef" title="应用" />
</template>

<script lang="ts" setup name="iam/app">
/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */
import { ref, reactive, readonly, computed, nextTick, onMounted, onActivated, onDeactivated, onBeforeUnmount, watch, provide, inject, h } from "vue";
import { useRouter, useRoute, onBeforeRouteUpdate } from "vue-router";
import draggable from "vuedraggable";
import { useFileDialog } from "@vueuse/core";
// Utils
import { cloneDeep, isEqual, isNil } from "lodash-es";
import { sizes } from "@/utils/common";
import { useSiteConfig } from "@/stores/siteConfig";
import { useConfig } from "@/stores/config";
// UI
import { ElMessage, ElContainer, ElAside, ElMain, ElButton, ElLink, ElTableV2, ElTooltip } from "element-plus";
import { Refresh, Plus, Edit, Delete, Sort, Setting, Tickets, Menu, Lock, QuestionFilled } from "@element-plus/icons-vue";
// Api
import { getApp as getItem, addApp as addItem, modApp as modItem, delApp as delItem, type NavItem as DataItem, modAppByOrder, appTerminal, appTerminalOption, appType, appTypeOption, exportApp, importApp } from "@/api/application";
// 组件
import DetailAppMenu from "./model/DetailAppMenu.vue";
import ServeAuth from "./model/ServeAuth.vue";
import allocation from "./model/allocation.vue";
import AppAuthGroup from "./model/AppAuthGroup.vue";
import AppAuthTemplate from "./model/AppAuthTemplate.vue";
import model from "./model/model.vue";

// 编辑器
import EditorForm from "./Editor.vue";
import { editorType, EditorType } from "@/views/common/interface";

const editorRef = ref<InstanceType<typeof EditorForm>>();

const width = inject<import("vue").Ref<number>>("width", ref(0));
const height = inject<import("vue").Ref<number>>("height", ref(0));

const route = useRoute();
const router = useRouter();

const siteConfig = useSiteConfig();
const { open, onChange } = useFileDialog({ reset: true });

onChange(async (files) => {
  if (files) {
    const file = files.item(0);
    if (file instanceof File) {
      try {
        const { success, message, data } = await importApp({ file });
        if (!success) throw Object.assign(new Error(message), { data, success });
        ElMessage.success("成功上传！");
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      }
    }
  }
});

type MenuType = "" | "menu" | "auth" | "group" | "template";

// 数据
interface StateData<T, K> {
  loading: boolean;
  search: Record<string, unknown>;
  data: T[];
  page: number;
  size: number;
  total: number;
  sizes: typeof sizes;
  active: import("vue").WritableComputedRef<T[K]>;
  type: import("vue").WritableComputedRef<MenuType>;
  // create: (params: Partial<T> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) => Promise<void>;
  // editor: (params: Partial<T> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) => Promise<void>;
  // delete: (params: Partial<T> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) => Promise<void>;
  // source: (params: Partial<T> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) => Promise<void>;
  // refresh: () => void;
}

const state = reactive<StateData<DataItem, "id">>({
  loading: false,
  search: {},
  data: [],
  page: 1,
  size: 20,
  total: 0,
  sizes,
  active: computed({
    get: () => (route.query.app as string) || "",
    set: (v: string) => router.push({ query: { ...route.query, app: v } }),
  }),
  type: computed({
    get: () => (route.query.type as MenuType) || "menu",
    set: (v: MenuType) => router.push({ query: { ...route.query, type: v } }),
  }),
});

// const active = computed({
//   get: () => (route.query.app as string) || "",
//   set: (v: string) => router.push({ query: { ...route.query, app: v } }),
// });
// const type = computed({
//   get: () => (route.query.type as MenuType) || "",
//   set: (v: MenuType) => router.push({ query: { ...route.query, type: v } }),
// });
async function createState(params: Partial<DataItem> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, "#TYPE": EditorType.Add }, async (req) => {
      try {
        const { success, message, data } = await addItem({ ...req, order: state.data.length });
        if (success) {
          ElMessage.success(`${editorType[EditorType.Add]}成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
  await refreshState();
}
async function editorState(params: Partial<DataItem> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;

  try {
    await editorRef.value.open({ ...params, "#TYPE": EditorType.Mod }, async (req) => {
      try {
        const { success, message, data } = await modItem({ ...req, order: params.order });
        if (success) {
          ElMessage.success(`${editorType[EditorType.Mod]}成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
  await refreshState();
}
async function deleteState(params: Partial<DataItem> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, "#TYPE": EditorType.Del }, async (req) => {
      try {
        const { success, message, data } = await delItem(req);
        if (success) {
          ElMessage.success(`${editorType[EditorType.Del]}成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
  await refreshState();
}
async function sourceState(params: Partial<DataItem> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  const controller = new AbortController();
  const response = getItem({ ...params, paging: { pageNumber: state.page, pageSize: state.size }, controller });
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    state.loading = true;
    await nextTick();
    const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await response;
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);
      state.data.splice(0, state.data.length, ...(data instanceof Array ? data : []));
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.total = Number(0);
    state.data.splice(0, state.data.length);
  } finally {
    await nextTick();
    if (!state.data.map(({ id }) => id).includes(state.active)) state.active = (state.data[0] || {}).id || "";
    state.loading = false;
  }
}
function refreshState() {
  sourceState(Object.entries(state.search).reduce<Record<string, unknown>>((p, [key, value]) => ({ ...p, ...(isNil(value) ? {} : { [key]: value }) }), {}));
}

if (!["menu", "auth", "group", "template"].includes(state.type)) state.type = "menu";

async function reSlot({ moved: _moved }: { moved: { newIndex: number; oldIndex: number; element: DataItem }; removed?: { oldIndex: number; element: DataItem }; added?: { newIndex: number; element: DataItem } }) {
  try {
    const { success, message, data } = await modAppByOrder({ orders: state.data.map((v, i) => ({ appCode: v.rootId, order: i })) });
    if (success) {
      /*  */
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
  refreshState();
}

onMounted(refreshState);

const currentActive = computed(() => state.data.find((item) => item.name === state.active));
</script>

<style lang="scss" scoped>
.default-alert {
  position: absolute;
  top: -14px;
  z-index: 1;
}

.tab-pane-label {
  display: inline-block;
  line-height: 1;
  // width: 120px;
  width: fit-content;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.content {
  height: calc(100vh - 70px - (var(--ba-main-space, 0px) * 2));
}

.menu-header {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;

  .title {
    flex-shrink: 0;
  }

  .btn-group {
    width: 100%;
    flex-shrink: 1;
  }
}

.card-header {
  background: var(--ba-bg-color-overlay);
  margin: 16px 0 16px 16px;
  height: calc(100vh - 32px);
  box-shadow: var(--el-box-shadow-light);
  border-radius: var(--el-border-radius-base);
  overflow: hidden;
  transition: width 0.3s ease;
  width: var(--8aea564f-menuWidth);
}

.view-main {
  padding: 0px;
  overflow: visible;
}

.root-item-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &::after {
    content: attr(title);
    font-size: 12px;
  }
}

.main-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  line-height: 18px;

  > :deep(.left),
  > :deep(.center),
  > :deep(.right) {
    > * {
      &:not(:last-child) {
        margin-right: 4px;
      }

      &:not(:first-child) {
        margin-left: 4px;
      }
    }
  }

  .center {
    margin: 0 8px;
  }
}

.main-list {
  margin: 0;
  padding: 0;

  .main-item {
    display: flex;
    padding: 4px 6px;
    border-color: var(--el-border-color);
    border-style: solid;
    border-width: 0px;
    cursor: pointer;

    &:not(:last-child) {
      // margin-bottom: 3px;
      border-bottom-width: 1px;
    }

    // &:not(:first-child) {
    //   margin-top: 3px;
    // }
    &.active,
    &:hover {
      background-color: var(--el-fill-color-light);
    }

    &.active {
      color: var(--el-color-primary);

      .main-item-code {
        color: var(--el-text-color-primary);
      }
    }

    > * {
      flex-shrink: 0;
    }

    .main-item-body {
      display: flex;
      flex-direction: column;
      width: calc(100% - 160px);
    }

    .main-item-code,
    .main-item-title {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .main-item-title {
      display: inline-block;
      max-width: 100px;
      margin-left: 6px;
      font-weight: bold;
      flex-shrink: 1;
      height: 100%;
    }

    .main-item-code {
      display: inline-block;
      max-width: 100px;
      margin-left: 6px;
      font-size: 12px;
      flex-shrink: 0;
      color: var(--el-text-color-placeholder);
    }

    .main-item-btn-group {
      position: relative;

      > * {
        &:not(:last-child) {
          margin-right: 3px;
        }

        &:not(:first-child) {
          margin-left: 3px;
        }
      }
    }
  }
}
</style>
