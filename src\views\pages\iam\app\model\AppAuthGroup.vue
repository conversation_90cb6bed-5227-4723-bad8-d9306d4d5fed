<template>
  <el-scrollbar>
    <div class="flex-search" :style="{ minWidth: `${props.width - 2}px` }">
      <div class="left"><slot name="left" :create="createPermissionGroup" :delete="deletePermissionGroup" :editor="editorPermissionGroup" :refresh="handleStateRefresh"></slot></div>
      <div class="center">
        <slot name="center" :create="createPermissionGroup" :delete="deletePermissionGroup" :editor="editorPermissionGroup" :refresh="handleStateRefresh">
          <CopyRender :value="`module.exports = ${logTreeData()};\n`">Data0</CopyRender>
          <CopyRender :value="logTreeType()">Data1</CopyRender>
        </slot>
      </div>
      <div class="right"><slot name="right" :create="createPermissionGroup" :delete="deletePermissionGroup" :editor="editorPermissionGroup" :refresh="handleStateRefresh"></slot></div>
    </div>
  </el-scrollbar>
  <el-scrollbar ref="scrollbarRef" :height="props.height - 64 - 20" :style="{ width: `${props.width - 40}px`, margin: '0 auto 20px auto' }" @scroll="scrollPosition = $event">
    <!-- <pre>{{ { listPermissionCatalog, listPermissionGroup, listPermissionOption, listPermissionAuth, listPermissionTemplate } }}</pre> -->
    <!-- <el-tree ref="treeRef" :allow-drop="allowDrop" :allow-drag="(draggingNode: Node) => draggingNode.data.type === appType.DIR" :data="treeList0" :default-expanded-keys="[...expandPermissionCatalog]" :default-checked-keys="selectPermissionCatalog" node-key="id" :props="{ label: 'name', children: 'children', disabled: 'disabled', isLeaf: 'isLeaf', class: (data) => `tree_context tree_data_dir ${data.id}` }" draggable show-checkbox :expand-on-click-node="false" :default-expand-all="false" :render-after-expand="false" @node-drop="handleDrop0" @node-expand="(data) => expandPermissionCatalog.add(data.id)" @node-collapse="(data) => expandPermissionCatalog.delete(data.id)" @check="handleCheck0">
      <template #default="{ node, data: authCatalog }: { node: Node; data: PermissionCatalogItem & { permission: PermissionAuthItem[] } }">
        <div class="node_context_view tw-w-full">
          <div class="el-tree-node__label tw-flex tw-items-center">
            <el-icon class="tw-mr-[3px]"><Folder></Folder></el-icon>
            <span style="color: var(--text-color)">{{ node.label }}</span>
            <div class="tw-ml-auto">
              <el-link type="primary" :underline="false" :icon="Plus" class="tw-mx-[3px]" @click.stop="createPermissionCatalog({ appId: props.data.id, parentId: authCatalog.id, orderNum: authCatalog.children.length })">{{ $t("glob.add") }}分组</el-link>
              <el-link type="primary" :underline="false" :icon="Delete" class="tw-mx-[3px]" @click.stop="deletePermissionCatalog(authCatalog)">{{ $t("glob.delete") }}分组</el-link>
              <el-link type="primary" :underline="false" :icon="Edit" class="tw-mx-[3px]" @click.stop="editorPermissionCatalog(authCatalog)">{{ $t("glob.edit") }}分组</el-link>
            </div>
          </div>
          <div @dragstart.stop @dragover.stop class="tw-cursor-default">
            <draggable class="el-row tree_item_group" handle=".move_bar" group="main-list" tag="div" v-model="authCatalog.permission" animation="300" ghostClass="ghost" chosenClass="chosen" item-key="name" @change="($event) => handleStateOrder0($event, authCatalog)">
              <template #header>
                <el-col :span="24" class="tw-flex">
                  <el-checkbox :model-value="authCatalog.permission.every((v) => v.enabled)" :disabled="!authCatalog.permission.length" :indeterminate="authCatalog.permission.every((v) => v.enabled) ? false : authCatalog.permission.some((v) => v.enabled)" @change="() => setAllEnabled(authCatalog)">
                    <el-icon class="tw-align-middle"><Finished /></el-icon>
                    {{ $t("glob.All Select") }}
                  </el-checkbox>
                  <el-link type="primary" :underline="false" :icon="Plus" class="tw-ml-auto" @click.stop="createPermissionAuth({ appId: props.data.id, catalogId: authCatalog.id, orderNum: authCatalog.permission.length })">{{ $t("glob.add") }}权限</el-link>
                </el-col>
              </template>
              <template #item="{ element: authoritie }: { element: PermissionAuthItem }">
                <el-col :key="`${authCatalog.id}_${authoritie.id}`" :span="24" :xs="24" :sm="12" :md="8" :lg="4" :xl="3" :offset="0" draggable="true" class="tw-p-[12px]">
                  <div :id="authoritie.id" style="border: 1px solid var(--el-border-color); border-radius: 3px; padding: 0 8px 0 16px" class="list-item tw-relative tw-flex tw-items-center">
                    <div class="move_bar tw-m-0 tw-mr-[3px] tw-h-full tw-w-[16px]"></div>
                    <el-checkbox v-model="authoritie.enabled" class="tw-w-[calc(100%-32px)] tw-overflow-hidden tw-text-ellipsis" :indeterminate="false" :title="authoritie.name" @change="($event) => setAllEnabled({ ...authCatalog, permission: [{ ...authoritie, enabled: !$event as boolean }] })">{{ authoritie.name }}</el-checkbox>
                    <div class="tw-ml-auto">
                      <CopyRender :value="authoritie.id"></CopyRender>
                      <el-link class="tw-ml-[3px] tw-align-middle" type="primary" :underline="false" :icon="Edit" @click.stop="editorPermissionAuth({ ...authoritie, catalogId: authCatalog.id })"></el-link>
                      <el-link class="tw-ml-[3px] tw-align-middle" type="primary" :underline="false" :icon="Delete" @click.stop="deletePermissionAuth({ id: authoritie.id })"></el-link>
                    </div>
                    <div class="tw-absolute tw-left-[12px] tw-top-0 tw-translate-y-[-50%] tw-bg-[var(--el-fill-color-blank)] tw-px-[0.5em] tw-text-[12px]">{{ authoritie.id }}</div>
                  </div>
                </el-col>
              </template>
            </draggable>
          </div>
        </div>
      </template>
    </el-tree> -->
    <el-tree ref="treeRef" class="custom-tree" :allow-drop="allowDrop" :allow-drag="(draggingNode: Node) => draggingNode.data.type === 'GROUP' || draggingNode.data.type === 'OPTION'" :data="treeList1" :default-expanded-keys="[...expandPermissionGroup]" :default-checked-keys="[...selectPermissionGroup, ...selectPermissionOption]" node-key="id" :props="{ label: 'name', children: 'children', disabled: 'disabled', isLeaf: 'isLeaf', class: (data) => `${data.permission.length ? 'tree_context ' : ''}tree_data_dir ${data.id}` }" :draggable="false" show-checkbox :expand-on-click-node="false" :default-expand-all="false" :render-after-expand="false" @node-drop="handleDrop1" @node-expand="(data) => expandPermissionGroup.add(data.id)" @node-collapse="(data) => expandPermissionGroup.delete(data.id)" @check="handleCheck1">
      <template #default="{ node, data: authGroup }: { node: Node; data: (PermissionGroupItem & { type: 'GROUP'; children: (PermissionOptionItem & { type: 'OPTION'; permission: PermissionAuthItem[] })[]; permission: PermissionAuthItem[] }) | (PermissionOptionItem & { type: 'OPTION'; permission: PermissionAuthItem[] }) }">
        <div class="node_context_view tw-w-full">
          <div class="el-tree-node__label tw-flex tw-items-center">
            <el-icon class="tw-mr-[3px]">
              <Folder v-if="authGroup.type === 'GROUP'"></Folder>
              <Setting v-else></Setting>
            </el-icon>
            <span style="color: var(--text-color)">{{ node.label }}</span>
            <div class="tw-ml-auto" v-if="authGroup.type === 'GROUP'">
              <el-link type="primary" :underline="false" :icon="Plus" class="tw-mx-[3px]" @click.stop="createPermissionOption({ appId: props.data.id, groupId: authGroup.id, orderNum: authGroup.children.length })">{{ $t("glob.add") }}配置项</el-link>
              <el-link type="primary" :underline="false" :icon="Delete" class="tw-mx-[3px]" @click.stop="deletePermissionGroup(authGroup)">{{ $t("glob.delete") }}分组</el-link>
              <el-link type="primary" :underline="false" :icon="Edit" class="tw-mx-[3px]" @click.stop="editorPermissionGroup(authGroup)">{{ $t("glob.edit") }}分组</el-link>
            </div>
            <div class="tw-ml-auto" v-else>
              <el-link type="primary" :underline="false" :icon="Plus" class="tw-mx-[3px]" @click.stop="createPermissionAuth({ appId: props.data.id, itemId: authGroup.id, orderNum: authGroup.permission.length })">{{ $t("glob.add") }}权限</el-link>
              <el-link type="primary" :underline="false" :icon="Delete" class="tw-mx-[3px]" @click.stop="deletePermissionOption(authGroup)">{{ $t("glob.delete") }}配置项</el-link>
              <el-link type="primary" :underline="false" :icon="Edit" class="tw-mx-[3px]" @click.stop="editorPermissionOption(authGroup)">{{ $t("glob.edit") }}配置项</el-link>
            </div>
          </div>
          <div v-if="authGroup.type === 'OPTION' && authGroup.permission.length" @dragstart.stop @dragover.stop class="tw-cursor-default">
            <div :style="{ borderTop: 'var(--el-border)', paddingLeft: '12px' }">
              <el-checkbox :model-value="authGroup.permission.every((v) => v.enabled)" :disabled="!authGroup.permission.length" :indeterminate="authGroup.permission.every((v) => v.enabled) ? false : authGroup.permission.some((v) => v.enabled)" @change="() => setAllEnabled(authGroup)">
                <el-icon class="tw-align-middle"><Finished /></el-icon>
                {{ $t("glob.All Select") }}
              </el-checkbox>
            </div>
            <!-- <el-scrollbar :max-height="140"> -->
            <draggable class="el-row" handle=".move_bar" group="main-list" tag="div" v-model="authGroup.permission" animation="300" ghostClass="ghost" chosenClass="chosen" item-key="name" @change="($event) => handleStateOrder1($event, authGroup)">
              <template #item="{ element: authoritie }: { element: PermissionAuthItem }">
                <el-col :key="`${authGroup.id}_${authoritie.id}`" :span="24" :xs="24" :sm="12" :md="8" :lg="4" :xl="3" :offset="0" draggable="true" class="tw-p-[12px]">
                  <div :id="authoritie.id" style="border: 1px solid var(--el-border-color); border-radius: 3px; padding: 0 8px 0 16px" class="list-item tw-relative tw-flex tw-items-center">
                    <div class="move_bar tw-m-0 tw-mr-[3px] tw-h-full tw-w-[16px]"></div>
                    <el-checkbox v-model="authoritie.enabled" class="tw-w-[calc(100%-32px)] tw-overflow-hidden tw-text-ellipsis" :indeterminate="false" :title="authoritie.name" @change="($event) => modPermissionAuth({ id: authoritie.id, enabled: $event as boolean })">{{ authoritie.name }}</el-checkbox>
                    <div class="tw-ml-auto">
                      <CopyRender :value="authoritie.id"></CopyRender>
                      <el-link class="tw-ml-[3px] tw-align-middle" type="primary" :underline="false" :icon="Edit" @click.stop="editorPermissionAuth({ ...authoritie, itemId: authGroup.id })"></el-link>
                      <el-link class="tw-ml-[3px] tw-align-middle" type="primary" :underline="false" :icon="Delete" @click.stop="deletePermissionAuth({ name: authoritie.name, id: authoritie.id })"></el-link>
                    </div>
                    <div class="tw-absolute tw-left-[12px] tw-top-0 tw-translate-y-[-50%] tw-bg-[var(--el-fill-color-blank)] tw-px-[0.5em] tw-text-[12px] tw-leading-[12px]">{{ authoritie.orderNum }}.{{ authoritie.id }}</div>
                  </div>
                </el-col>
              </template>
            </draggable>
            <!-- </el-scrollbar> -->
          </div>
        </div>
      </template>
    </el-tree>
    <EditorPermissionCatalog ref="editorPermissionCatalogRef" title="权限目录" :width="$width" :height="$height - 180 - $height * 0.15"></EditorPermissionCatalog>
    <EditorPermissionGroup ref="editorPermissionGroupRef" title="权限分组" :width="$width" :height="$height - 180 - $height * 0.15"></EditorPermissionGroup>
    <EditorPermissionOption ref="editorPermissionOptionRef" title="配置项" :width="$width" :height="$height - 180 - $height * 0.15"></EditorPermissionOption>
    <EditorPermissionAuth ref="editorPermissionAuthRef" title="权限" :width="$width" :height="$height - 180 - $height * 0.15"></EditorPermissionAuth>
    <EditorPermissionTemplate ref="editorPermissionTemplateRef" title="权限模板" :width="$width" :height="$height - 180 - $height * 0.15"></EditorPermissionTemplate>
  </el-scrollbar>
</template>

<script setup lang="ts" name="ServeAuth">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, nextTick, watch, onMounted, onBeforeUnmount, h, defineComponent, renderSlot, computed, toValue, inject } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useClipboard } from "@vueuse/core";
import { useI18n } from "vue-i18n";
import { ElLink, ElMessage, ElMessageBox } from "element-plus";
import { Plus, Edit, Delete, Folder, Setting, Pointer, Finished, CopyDocument, Checked } from "@element-plus/icons-vue";
import type Node from "element-plus/es/components/tree/src/model/node";
import type { AllowDropType, NodeDropType } from "element-plus/es/components/tree/src/tree.type";
import draggable from "vuedraggable";
import generateQueueHook from "@/utils/queue_hook";

import { getMenu, appTerminal, appType, appTheme, type NavItem } from "@/api/application";

import { type PermissionCatalogItem, getPermissionCatalog, addPermissionCatalog, modPermissionCatalog, delPermissionCatalog, setPermissionCatalogBySlot } from "@/api/permission";
import EditorPermissionCatalog from "./EditorByPermissionCatalog.vue";
import { type PermissionGroupItem, getPermissionGroup, addPermissionGroup, modPermissionGroup, delPermissionGroup, setPermissionGroupBySlot } from "@/api/permission";
import EditorPermissionGroup from "./EditorByPermissionGroup.vue";
import { type PermissionOptionItem, getPermissionOption, addPermissionOption, modPermissionOption, delPermissionOption, setPermissionOptionBySlot } from "@/api/permission";
import EditorPermissionOption from "./EditorByPermissionOption.vue";
import { type PermissionAuthItem, getPermissionAuth, addPermissionAuth, modPermissionAuth, delPermissionAuth, setPermissionAuthBySlot } from "@/api/permission";
import EditorPermissionAuth from "./EditorByPermissionAuth.vue";
import { type PermissionTemplateItem, getPermissionTemplate, addPermissionTemplate, modPermissionTemplate, delPermissionTemplate, setPermissionTemplateBySlot } from "@/api/permission";
import EditorPermissionTemplate from "./EditorByPermissionTemplate.vue";

function logTreeData(): string {
  const list: string[] = [];
  const $list = toValue(listPermissionGroup).sort((a, b) => (Number(a.orderNum) || 0) - (Number(b.orderNum) || 0));
  const $item = toValue(listPermissionOption).sort((a, b) => (Number(a.orderNum) || 0) - (Number(b.orderNum) || 0));
  const $auth = toValue(listPermissionAuth)
    .sort((a, b) => (Number(a.orderNum) || 0) - (Number(b.orderNum) || 0))
    .map((v) => Object.assign({ navigation: toValue(navigation).filter(({ permissionGroups }) => permissionGroups.some((auth) => auth.includes(v.id))) }, v));
  for (let index0 = 0; index0 < $list.length; index0++) {
    if ($list[index0].enabled) {
      list.push(`  /* TODO: ${$list[index0].name} */\n`);
      for (let index1 = 0; index1 < $item.length; index1++) {
        if ($item[index1].enabled && $item[index1].groupId === $list[index0].id) {
          const $link_list: string[] = [];
          const $item_list: string[] = [];
          for (let index2 = 0; index2 < $auth.length; index2++) {
            if ($auth[index2].enabled && $auth[index2].itemId === $item[index1].id) {
              /* \n *  */
              if ($auth[index2].navigation) $link_list.push(...$auth[index2].navigation.map((v) => `\n * @file [${v.title}_${v.name}](file://./views/pages/${v.component}) - ${$list[index0].name} -> ${$item[index1].name} - ${$auth[index2].name}`));
              $item_list.push(`  ${$auth[index2].ident || `${$list[index0].name}_${$item[index1].name}_${$auth[index2].name}`.replaceAll("/", "Or").replaceAll("-", "By")}: /* ${$list[index0].name} -> ${$item[index1].name} - ${$auth[index2].name} */ "${$auth[index2].id}",\n`);
            }
          }
          list.push(`  /**${$link_list.length ? `${$link_list.join("")}` : ""}\n * ${$list[index0].name} ----> ${$item[index1].name}\n */\n`);
          list.push(...$item_list);
        }
      }
      list.push(`\n`);
    }
  }

  return `{\n${list.join("")}} as const`;
}
function logTreeType(): string {
  const list: string[] = [];
  const $list = toValue(listPermissionGroup).sort((a, b) => (Number(a.orderNum) || 0) - (Number(b.orderNum) || 0));
  const $item = toValue(listPermissionOption).sort((a, b) => (Number(a.orderNum) || 0) - (Number(b.orderNum) || 0));
  const $auth = toValue(listPermissionAuth)
    .sort((a, b) => (Number(a.orderNum) || 0) - (Number(b.orderNum) || 0))
    .map((v) => Object.assign({ navigation: toValue(navigation).filter(({ permission, permissionGroups }) => [permission instanceof Array ? permission : [], ...permissionGroups].some((auth) => auth.includes(v.id))) }, v));
  for (let index0 = 0; index0 < $list.length; index0++) {
    // if ($list[index0].enabled) {
    list.push(`/* TODO: ${$list[index0].name} */\n`);
    for (let index1 = 0; index1 < $item.length; index1++) {
      if ($item[index1].groupId === $list[index0].id) {
        const $link_list: string[] = [];
        const $item_list: string[] = [];
        for (let index2 = 0; index2 < $auth.length; index2++) {
          if ($auth[index2].itemId === $item[index1].id) {
            if ($auth[index2].navigation) $link_list.push(...$auth[index2].navigation.map((v) => `\n * @file [${v.title}_${v.name}](file://./views/pages/${v.component}) - ${$list[index0].name} -> ${$item[index1].name} - ${$auth[index2].name}`));
            $item_list.push(`${$list[index0].enabled && $item[index1].enabled && $auth[index2].enabled ? "\n" : `\n// /**@deprecated - ${[...($list[index0].enabled ? [] : ['"🚫分组"']), ...($item[index1].enabled ? [] : ['"🚫配置"']), ...($auth[index2].enabled ? [] : ['"🚫权限"'])].join("+")} */\n`}${$list[index0].enabled && $item[index1].enabled && $auth[index2].enabled ? "" : "// "}export const ${`${$list[index0].name}_${$item[index1].name}_${$auth[index2].name}`.replaceAll("/", "Or").replaceAll("-", "By")} = /* ${$list[index0].name} -> ${$item[index1].name} - ${$auth[index2].name} */ "${$auth[index2].id}";`);
          }
        }
        list.push(`\n/**${$link_list.length ? `${$link_list.join("")}` : ""}\n * ${$list[index0].name} ----> ${$item[index1].name}\n */`, ...$item_list, $item_list.length ? "\n" : "");
      }
    }
    if (index0 !== $list.length - 1) list.push(`\n`);
  }

  return list.join("");
}

const route = useRoute();
const router = useRouter();
const { t } = useI18n({ useScope: "global" });
const $width = inject<import("vue").Ref<number>>("width", ref(0));
const $height = inject<import("vue").Ref<number>>("height", ref(0));

const scrollbarRef = ref<InstanceType<typeof import("element-plus").ElScrollbar>>();
const scrollPosition = ref({ scrollLeft: 0, scrollTop: 0 });

interface Props {
  data: NavItem;
  width: number;
  height: number;
}
const props = withDefaults(defineProps<Props>(), {
  data: () => ({ id: "", rootId: "", parentId: "", path: "", terminal: appTerminal.WEB, title: "", name: "", order: 0, icon: "local-SystemApps-line", type: appType.ROUTE, theme: appTheme.BASE, url: "", component: "", keepalive: false, enabled: true, permission: [], permissionGroups: [], note: "", version: "", config: "{}", hash: "", query: {}, params: {}, pattern: /(?:)/, names: [], children: [] }),
  width: 100,
  height: 300,
});

const CopyRender = defineComponent({
  props: {
    value: {
      type: String,
      default: "",
    },
  },
  setup(props, ctx) {
    const { copy, copied, isSupported } = useClipboard({ read: false, legacy: true });
    function copyData() {
      copy(props.value)
        .then(() => ElMessage.success("成功复制！"))
        .catch((error) => ElMessage.error(error instanceof Error ? error.message : "复制失败！"));
    }
    return () => (isSupported.value ? h(ElLink, { style: { marginLeft: "3px", verticalAlign: "middle" }, type: "primary", underline: false, icon: copied.value ? Checked : CopyDocument, title: t("glob.Copy"), onClick: (e) => (e.stopPropagation(), copyData()) }, () => renderSlot(ctx.slots, "default", {})) : null);
  },
});

const navigation = ref<NavItem[]>([]);
async function handleStateRefresh() {
  await Promise.all([
    (async () => {
      const { success, message, data } = await getMenu({ appId: props.data.id });
      if (!success) throw Object.assign(new Error(message), { success, data });
      const unTree = (tree: NavItem[]) => tree.reduce<NavItem[]>((p, c) => p.concat({ ...c, children: [] }, ...unTree(c.children)), []);
      navigation.value = unTree(data instanceof Array ? data : []);
    })(),
    // querysPermissionCatalog(),
    querysPermissionGroup(),
    querysPermissionOption(),
    querysPermissionAuth(),
    // querysPermissionTemplate(),
  ]);
}
/* ======================================================================================================================================= */
// const treeList0 = computed(() => {
//   const $auth = toValue(listPermissionAuth);
//   const $list = toValue(listPermissionCatalog).map((v) => Object.assign({ $isRoot: true, permission: $auth.filter((auth) => auth.catalogId === v.id).sort((a, b) => (Number(a.orderNum) || 0) - (Number(b.orderNum) || 0)) } as { $isRoot?: boolean; permission: PermissionAuthItem[] }, v));
//   for (const self of $list) {
//     const children = $list.filter((v) => v.parentId === self.id).sort((a, b) => (Number(a.orderNum) || 0) - (Number(b.orderNum) || 0));
//     for (let i = 0; i < children.length; i++) delete children[i].$isRoot;
//     Object.assign(self, { children });
//   }
//   for (let i = $list.length - 1; i >= 0; i--) {
//     if ($list[i].$isRoot) delete $list[i].$isRoot;
//     else $list.splice(i, 1);
//   }
//   return $list.sort((a, b) => (Number(a.orderNum) || 0) - (Number(b.orderNum) || 0)) as (PermissionCatalogItem & { permission: PermissionAuthItem[] })[];
// });
// async function handleStateOrder0({ moved, removed, added }: { moved?: { newIndex: number; oldIndex: number; element: PermissionAuthItem }; removed?: { oldIndex: number; element: PermissionAuthItem }; added?: { newIndex: number; element: PermissionAuthItem } }, catalog: PermissionCatalogItem & { permission: PermissionAuthItem[] }) {
//   await nextTick();
//   if (moved) {
//     try {
//       if (catalog.permission.length) {
//         const { success, message, data } = await setPermissionAuthBySlot({ slot: catalog.permission.map((v, i) => ({ id: v.id, order: i })) });
//         if (!success) throw Object.assign(new Error(message), { success, data });
//       }
//     } catch (error) {
//       await querysPermissionAuth();
//       if (error instanceof Error) ElMessage.error(error.message);
//     }
//   }
//   if (removed) {
//     try {
//       if (catalog.permission.length) {
//         const { success, message, data } = await setPermissionAuthBySlot({ slot: catalog.permission.map((v, i) => ({ id: v.id, order: i })) });
//         if (!success) throw Object.assign(new Error(message), { success, data });
//       }
//     } catch (error) {
//       await querysPermissionAuth();
//       if (error instanceof Error) ElMessage.error(error.message);
//     }
//   }
//   if (added) {
//     try {
//       const { success, message, data } = await modPermissionAuth({ id: added.element.id, orderNum: added.newIndex, catalogId: catalog.id });
//       if (!success) throw Object.assign(new Error(message), { success, data });
//     } catch (error) {
//       await querysPermissionAuth();
//       if (error instanceof Error) ElMessage.error(error.message);
//     }
//     try {
//       if (catalog.permission.length) {
//         const { success, message, data } = await setPermissionAuthBySlot({ slot: catalog.permission.map((v, i) => ({ id: v.id, order: i })) });
//         if (!success) throw Object.assign(new Error(message), { success, data });
//       }
//     } catch (error) {
//       await querysPermissionAuth();
//       if (error instanceof Error) ElMessage.error(error.message);
//     }
//   }
// }
// async function handleDrop0(draggingNode: Node, dropNode: Node, dropType: NodeDropType) {
//   await nextTick();
//   const req: { id: string; order: number }[] = [];
//   let result = Promise.resolve({ success: true, message: "", data: {} });

//   try {
//     switch (dropType) {
//       case "before": {
//         let currentNode: null | Node = dropNode.previousSibling as Node;
//         let index = dropNode.parent.childNodes.indexOf(currentNode);
//         while (currentNode) {
//           req.push({ id: currentNode.data.id, order: index });
//           index++;
//           currentNode = currentNode.nextSibling;
//         }
//         if (draggingNode.data.parentId !== dropNode.data.parentId) {
//           const { success, message, data } = await modPermissionCatalog({ id: draggingNode.data.id, parentId: dropNode.data.parentId || "-1" });
//           if (!success) throw Object.assign(new Error(message), { success, data });
//           req.push(...(getTreeDataChildren<PermissionCatalogItem>(toValue(treeList0), draggingNode.data.parentId, { key: "id", children: "children" }) || []).map((v, i) => ({ id: v.id, order: i, name: v.name })));
//         }
//         break;
//       }
//       case "inner": {
//         req.push({ id: draggingNode.data.id, order: dropNode.childNodes.length - 1 });
//         if (draggingNode.data.parentId === dropNode.data.id) {
//           for (let i = 0; i < dropNode.childNodes.length; i++) {
//             if (draggingNode.data.id === dropNode.childNodes[i].data.id) continue;
//             req.push({ id: dropNode.childNodes[i].data.id, order: i });
//           }
//         } else {
//           const { success, message, data } = await modPermissionCatalog({ id: draggingNode.data.id, parentId: dropNode.data.id || "-1" });
//           if (!success) throw Object.assign(new Error(message), { success, data });
//           req.push(...(getTreeDataChildren<PermissionCatalogItem>(toValue(treeList0), draggingNode.data.parentId, { key: "id", children: "children" }) || []).map((v, i) => ({ id: v.id, order: i, name: v.name })));
//         }
//         break;
//       }
//       case "after": {
//         let currentNode: null | Node = dropNode.nextSibling as Node;
//         let index = dropNode.parent.childNodes.indexOf(currentNode);
//         while (currentNode) {
//           req.push({ id: currentNode.data.id, order: index });
//           index++;
//           currentNode = currentNode.nextSibling;
//         }
//         if (draggingNode.data.parentId !== dropNode.data.parentId) {
//           const { success, message, data } = await modPermissionCatalog({ id: draggingNode.data.id, parentId: dropNode.data.parentId || "-1" });
//           if (!success) throw Object.assign(new Error(message), { success, data });
//           req.push(...(getTreeDataChildren<PermissionCatalogItem>(toValue(treeList0), draggingNode.data.parentId, { key: "id", children: "children" }) || []).map((v, i) => ({ id: v.id, order: i, name: v.name })));
//         }
//         break;
//       }
//       default: {
//         break;
//       }
//     }
//   } catch (error) {
//     if (error instanceof Error) ElMessage.error(error.message);
//     await handleStateRefresh();
//   }
//   try {
//     const { success, message, data } = await setPermissionCatalogBySlot({ slot: req });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//   } catch (error) {
//     if (error instanceof Error) ElMessage.error(error.message);
//     await handleStateRefresh();
//   }
//   treeRef.value && treeRef.value.setCheckedKeys(Array.from(toValue(selectPermissionCatalog)));
// }
// async function handleCheck0(data: { id: string; children: (typeof data)[] }, checked: { checkedNodes: Node[]; checkedKeys: string[]; halfCheckedNodes: Node[]; halfCheckedKeys: string[] }) {
//   try {
//     const allCheckeds = [...checked.checkedKeys, ...checked.halfCheckedKeys];
//     const result: Promise<import("@/api/service/common").Response<unknown>>[] = [];
//     const $list = toValue(listPermissionCatalog);
//     for (let i = 0; i < $list.length; i++) {
//       const check = allCheckeds.includes($list[i].id);
//       if ($list[i].enabled === check) continue;
//       Object.assign($list[i], { enabled: check });
//       result.push(modPermissionCatalog({ id: $list[i].id, enabled: check }));
//     }

//     const res = await Promise.all(result);
//     for (let i = 0; i < res.length; i++) {
//       const { success, message, data: resData } = res[i];
//       if (!success) throw Object.assign(new Error(message), { success, data: resData });
//     }
//   } catch (error) {
//     if (error instanceof Error) ElMessage.error(error.message);
//     await querysPermissionCatalog();
//   }
// }
const treeList1 = computed(() => {
  const $auth = toValue(listPermissionAuth);
  const $item = toValue(listPermissionOption).map((v) => Object.assign({ type: "OPTION", permission: $auth.filter((auth) => auth.itemId === v.id).sort((a, b) => (Number(a.orderNum) || 0) - (Number(b.orderNum) || 0)) } as { type: "OPTION"; permission: PermissionAuthItem[] }, v));
  const $list = toValue(listPermissionGroup).map((v) => Object.assign({ type: "GROUP", children: $item.filter((auth) => auth.groupId === v.id).sort((a, b) => (Number(a.orderNum) || 0) - (Number(b.orderNum) || 0)), permission: [] } as { type: "GROUP"; children: (PermissionOptionItem & { type: "OPTION"; permission: PermissionAuthItem[] })[]; permission: PermissionAuthItem[] }, v));
  return $list.sort((a, b) => (Number(a.orderNum) || 0) - (Number(b.orderNum) || 0)) as (PermissionGroupItem & { type: "GROUP"; children: (PermissionOptionItem & { type: "OPTION"; permission: PermissionAuthItem[] })[]; permission: PermissionAuthItem[] })[];
});
async function handleStateOrder1({ moved, removed, added }: { moved?: { newIndex: number; oldIndex: number; element: PermissionAuthItem }; removed?: { oldIndex: number; element: PermissionAuthItem }; added?: { newIndex: number; element: PermissionAuthItem } }, items: PermissionOptionItem & { type: "OPTION"; permission: PermissionAuthItem[] }) {
  await nextTick();
  if (moved) {
    try {
      if (items.permission.length) {
        const { success, message, data } = await setPermissionAuthBySlot({ slot: items.permission.map((v, i) => ({ id: v.id, order: i })) });
        if (!success) throw Object.assign(new Error(message), { success, data });
      }
    } catch (error) {
      await handleStateRefresh();
      if (error instanceof Error) ElMessage.error(error.message);
    }
  }
  if (removed) {
    try {
      if (items.permission.length) {
        const { success, message, data } = await setPermissionAuthBySlot({ slot: items.permission.map((v, i) => ({ id: v.id, order: i })) });
        if (!success) throw Object.assign(new Error(message), { success, data });
      }
    } catch (error) {
      await handleStateRefresh();
      if (error instanceof Error) ElMessage.error(error.message);
    }
  }
  if (added) {
    try {
      const { success, message, data } = await modPermissionAuth({ id: added.element.id, orderNum: added.newIndex, itemId: items.id });
      if (!success) throw Object.assign(new Error(message), { success, data });
    } catch (error) {
      await handleStateRefresh();
      if (error instanceof Error) ElMessage.error(error.message);
    }
    try {
      if (items.permission.length) {
        const { success, message, data } = await setPermissionAuthBySlot({ slot: items.permission.map((v, i) => ({ id: v.id, order: i })) });
        if (!success) throw Object.assign(new Error(message), { success, data });
      }
    } catch (error) {
      await handleStateRefresh();
      if (error instanceof Error) ElMessage.error(error.message);
    }
  }
}
async function handleDrop1(draggingNode: Node, dropNode: Node, dropType: NodeDropType) {
  // await nextTick();
  // const req: { id: string; order: number }[] = [];

  // try {
  //   switch (dropType) {
  //     case "before": {
  //       if (draggingNode.data.type !== dropNode.data.type) return await handleStateRefresh();
  //       let currentNode: null | Node = dropNode.previousSibling as Node;
  //       let index = dropNode.parent.childNodes.indexOf(currentNode);
  //       while (currentNode) {
  //         req.push({ id: currentNode.data.id, order: index });
  //         index++;
  //         currentNode = currentNode.nextSibling;
  //       }
  //       if (draggingNode.data.parentId !== dropNode.data.parentId) {
  //         if (draggingNode.data.type === "GROUP") {
  //           const { success, message, data } = await modPermissionGroup({ id: draggingNode.data.id, appId: dropNode.data.appId || "-1" });
  //           if (!success) throw Object.assign(new Error(message), { success, data });
  //         }
  //         if (draggingNode.data.type === "OPTION") {
  //           const { success, message, data } = await modPermissionOption({ id: draggingNode.data.id, groupId: dropNode.data.groupId || "-1" });
  //           if (!success) throw Object.assign(new Error(message), { success, data });
  //         }
  //         req.push(...(getTreeDataChildren<(typeof treeList1.value)[number]>(toValue(treeList1), draggingNode.data.parentId, { key: "id", children: "children" }) || []).map((v, i) => ({ id: v.id, order: i, name: v.name })));
  //       }
  //       break;
  //     }
  //     case "inner": {
  //       if (draggingNode.data.type !== "OPTION" || dropNode.data.type !== "GROUP") return await handleStateRefresh();
  //       req.push({ id: draggingNode.data.id, order: dropNode.childNodes.length - 1 });
  //       if (draggingNode.data.parentId === dropNode.data.id) {
  //         for (let i = 0; i < dropNode.childNodes.length; i++) {
  //           if (draggingNode.data.id === dropNode.childNodes[i].data.id) continue;
  //           req.push({ id: dropNode.childNodes[i].data.id, order: i });
  //         }
  //       } else {
  //         const { success, message, data } = await modPermissionOption({ id: draggingNode.data.id, groupId: dropNode.data.id || "-1" });
  //         if (!success) throw Object.assign(new Error(message), { success, data });
  //         req.push(...(getTreeDataChildren<(typeof treeList1.value)[number]>(toValue(treeList1), draggingNode.data.parentId, { key: "id", children: "children" }) || []).map((v, i) => ({ id: v.id, order: i, name: v.name })));
  //       }
  //       break;
  //     }
  //     case "after": {
  //       if (draggingNode.data.type !== dropNode.data.type) return await handleStateRefresh();
  //       let currentNode: null | Node = dropNode.nextSibling as Node;
  //       let index = dropNode.parent.childNodes.indexOf(currentNode);
  //       while (currentNode) {
  //         req.push({ id: currentNode.data.id, order: index });
  //         index++;
  //         currentNode = currentNode.nextSibling;
  //       }
  //       if (draggingNode.data.parentId !== dropNode.data.parentId) {
  //         if (draggingNode.data.type === "GROUP") {
  //           const { success, message, data } = await modPermissionGroup({ id: draggingNode.data.id, appId: dropNode.data.appId || "-1" });
  //           if (!success) throw Object.assign(new Error(message), { success, data });
  //         }
  //         if (draggingNode.data.type === "OPTION") {
  //           const { success, message, data } = await modPermissionOption({ id: draggingNode.data.id, groupId: dropNode.data.groupId || "-1" });
  //           if (!success) throw Object.assign(new Error(message), { success, data });
  //         }
  //         req.push(...(getTreeDataChildren<(typeof treeList1.value)[number]>(toValue(treeList1), draggingNode.data.parentId, { key: "id", children: "children" }) || []).map((v, i) => ({ id: v.id, order: i, name: v.name })));
  //       }
  //       break;
  //     }
  //     default: {
  //       break;
  //     }
  //   }
  // } catch (error) {
  //   if (error instanceof Error) ElMessage.error(error.message);
  //   await handleStateRefresh();
  // }
  // try {
  //   const [{ success: success0, message: message0, data: data0 }, { success: success1, message: message1, data: data1 }] = await Promise.all([setPermissionGroupBySlot({ slot: req.filter((v) => toValue(listPermissionGroup).some(({ id }) => id === v.id)) }), setPermissionOptionBySlot({ slot: req.filter((v) => toValue(listPermissionOption).some(({ id }) => id === v.id)) })]);
  //   if (!success0) throw Object.assign(new Error(message0), { success: success0, data: data0 });
  //   if (!success1) throw Object.assign(new Error(message1), { success: success1, data: data1 });
  // } catch (error) {
  //   if (error instanceof Error) ElMessage.error(error.message);
  //   await handleStateRefresh();
  // }
  treeRef.value && treeRef.value.setCheckedKeys(Array.from([...toValue(selectPermissionGroup), ...toValue(selectPermissionOption)]));
}
async function handleCheck1(data: { id: string; children: (typeof data)[] }, checked: { checkedNodes: Node[]; checkedKeys: string[]; halfCheckedNodes: Node[]; halfCheckedKeys: string[] }) {
  try {
    const allCheckeds = [...checked.checkedKeys, ...checked.halfCheckedKeys];
    const result: Promise<import("@/api/service/common").Response<unknown>>[] = [];
    const $list0 = toValue(listPermissionGroup);
    const $list1 = toValue(listPermissionOption);
    for (let i = 0; i < $list0.length; i++) {
      const check = allCheckeds.includes($list0[i].id);
      if ($list0[i].enabled === check) continue;
      Object.assign($list0[i], { enabled: check });
      result.push(modPermissionGroup({ id: $list0[i].id, enabled: check }));
    }
    for (let i = 0; i < $list1.length; i++) {
      const check = allCheckeds.includes($list1[i].id);
      if ($list1[i].enabled === check) continue;
      Object.assign($list1[i], { enabled: check });
      result.push(modPermissionOption({ id: $list1[i].id, enabled: check }));
    }

    const res = await Promise.all(result);
    for (let i = 0; i < res.length; i++) {
      const { success, message, data: resData } = res[i];
      if (!success) throw Object.assign(new Error(message), { success, data: resData });
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    await querysPermissionGroup();
    await querysPermissionOption();
  }
}
// const treeList2 = computed(() => {
//   const $auth = toValue(listPermissionAuth);
//   const $item = toValue(listPermissionTemplate).map((v) => Object.assign({ type: "TEMPLATE", permission: $auth.filter((auth) => (v.permissionIds instanceof Array ? v.permissionIds : []).includes(auth.id)).sort((a, b) => (Number(a.orderNum) || 0) - (Number(b.orderNum) || 0)) } as { type: "TEMPLATE"; permission: PermissionAuthItem[] }, v));
//   const $list = toValue(listPermissionGroup).map((v) => Object.assign({ type: "GROUP", children: $item.filter((auth) => auth.groupId === v.id).sort((a, b) => (Number(a.orderNum) || 0) - (Number(b.orderNum) || 0)), permission: [] } as { type: "GROUP"; children: (PermissionTemplateItem & { type: "TEMPLATE"; permission: PermissionAuthItem[] })[]; permission: PermissionAuthItem[] }, v));
//   return $list.sort((a, b) => (Number(a.orderNum) || 0) - (Number(b.orderNum) || 0)) as (PermissionGroupItem & { type: "GROUP"; children: (PermissionTemplateItem & { type: "TEMPLATE"; permission: PermissionAuthItem[] })[]; permission: PermissionAuthItem[] })[];
// });

// async function handleStateOrder2({ moved, removed, added }: { moved?: { newIndex: number; oldIndex: number; element: PermissionAuthItem }; removed?: { oldIndex: number; element: PermissionAuthItem }; added?: { newIndex: number; element: PermissionAuthItem } }, items: PermissionTemplateItem & { type: "TEMPLATE"; permission: PermissionAuthItem[] }) {
//   await nextTick();
//   if (moved) {
//     try {
//       if (items.permission.length) {
//         const { success, message, data } = await setPermissionAuthBySlot({ slot: items.permission.map((v, i) => ({ id: v.id, order: i })) });
//         if (!success) throw Object.assign(new Error(message), { success, data });
//       }
//     } catch (error) {
//       await handleStateRefresh();
//       if (error instanceof Error) ElMessage.error(error.message);
//     }
//   }
//   if (removed) {
//     try {
//       if (items.permission.length) {
//         const { success, message, data } = await setPermissionAuthBySlot({ slot: items.permission.map((v, i) => ({ id: v.id, order: i })) });
//         if (!success) throw Object.assign(new Error(message), { success, data });
//       }
//     } catch (error) {
//       await handleStateRefresh();
//       if (error instanceof Error) ElMessage.error(error.message);
//     }
//   }
//   if (added) {
//     try {
//       const { success, message, data } = await modPermissionAuth({ id: added.element.id, orderNum: added.newIndex, itemId: items.id });
//       if (!success) throw Object.assign(new Error(message), { success, data });
//     } catch (error) {
//       await handleStateRefresh();
//       if (error instanceof Error) ElMessage.error(error.message);
//     }
//     try {
//       if (items.permission.length) {
//         const { success, message, data } = await setPermissionAuthBySlot({ slot: items.permission.map((v, i) => ({ id: v.id, order: i })) });
//         if (!success) throw Object.assign(new Error(message), { success, data });
//       }
//     } catch (error) {
//       await handleStateRefresh();
//       if (error instanceof Error) ElMessage.error(error.message);
//     }
//   }
// }
// async function handleDrop2(draggingNode: Node, dropNode: Node, dropType: NodeDropType) {
//   await nextTick();
//   const req: { id: string; order: number }[] = [];
//   let result = Promise.resolve({ success: true, message: "", data: {} });

//   try {
//     switch (dropType) {
//       case "before": {
//         let currentNode: null | Node = dropNode.previousSibling as Node;
//         let index = dropNode.parent.childNodes.indexOf(currentNode);
//         while (currentNode) {
//           req.push({ id: currentNode.data.id, order: index });
//           index++;
//           currentNode = currentNode.nextSibling;
//         }
//         if (draggingNode.data.parentId !== dropNode.data.parentId) {
//           switch (draggingNode.data.type) {
//             case "TEMPLATE": {
//               break;
//             }
//             case "GROUP": {
//               break;
//             }
//           }
//           const { success, message, data } = await modPermissionCatalog({ id: draggingNode.data.id, parentId: dropNode.data.parentId || "-1" });
//           if (!success) throw Object.assign(new Error(message), { success, data });
//           req.push(...(getTreeDataChildren<PermissionCatalogItem>(toValue(treeList0), draggingNode.data.parentId, { key: "id", children: "children" }) || []).map((v, i) => ({ id: v.id, order: i, name: v.name })));
//         }
//         break;
//       }
//       case "inner": {
//         break;
//       }
//       case "after": {
//         let currentNode: null | Node = dropNode.nextSibling as Node;
//         let index = dropNode.parent.childNodes.indexOf(currentNode);
//         while (currentNode) {
//           req.push({ id: currentNode.data.id, order: index });
//           index++;
//           currentNode = currentNode.nextSibling;
//         }
//         if (draggingNode.data.parentId !== dropNode.data.parentId) {
//           const { success, message, data } = await modPermissionCatalog({ id: draggingNode.data.id, parentId: dropNode.data.parentId || "-1" });
//           if (!success) throw Object.assign(new Error(message), { success, data });
//           req.push(...(getTreeDataChildren<PermissionCatalogItem>(toValue(treeList0), draggingNode.data.parentId, { key: "id", children: "children" }) || []).map((v, i) => ({ id: v.id, order: i, name: v.name })));
//         }
//         break;
//       }
//       default: {
//         break;
//       }
//     }
//   } catch (error) {
//     if (error instanceof Error) ElMessage.error(error.message);
//     await handleStateRefresh();
//   }
//   try {
//     const { success, message, data } = await setPermissionCatalogBySlot({ slot: req });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//   } catch (error) {
//     if (error instanceof Error) ElMessage.error(error.message);
//     await handleStateRefresh();
//   }
//   treeRef.value && treeRef.value.setCheckedKeys(Array.from(toValue(selectPermissionCatalog)));
// }
/* ======================================================================================================================================= */
/* ======================================================================================================================================= */
// const loadingPermissionCatalog = ref(false);
// const AppendQueuePermissionCatalog = generateQueueHook(loadingPermissionCatalog, (loading) => !loading);
// let controllerPermissionCatalog: AbortController | undefined = void 0;
// const listPermissionCatalog = ref<PermissionCatalogItem[]>([]);
// const expandPermissionCatalog = ref(new Set<PermissionCatalogItem["id"]>());
// const selectPermissionCatalog = computed(() => toValue(listPermissionCatalog).reduce((p, c) => (c.enabled ? p.concat(c.id) : p), [] as string[]));
// // const currentPermissionCatalog = computed<null | PermissionCatalogItem>(() => {
// //   const $list = toValue(listPermissionCatalog);
// //   for (let i = 0; i < $list.length; i++) if ($list[i].id === toValue(selectPermissionCatalog)) return $list[i];
// //   return null;
// // });
// const editorPermissionCatalogRef = ref<InstanceType<typeof EditorPermissionCatalog>>();
// async function querysPermissionCatalog() {
//   if (controllerPermissionCatalog) controllerPermissionCatalog.abort();
//   if (toValue(loadingPermissionCatalog)) await new AppendQueuePermissionCatalog(true);
//   const result = getPermissionCatalog({ appId: props.data.id });
//   controllerPermissionCatalog = result.controller;
//   try {
//     loadingPermissionCatalog.value = true;
//     const { success, message, data } = await result;
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     listPermissionCatalog.value = data instanceof Array ? data : [];
//   } catch (error) {
//     if (error instanceof Error) ElMessage.error(error.message);
//   } finally {
//     controllerPermissionCatalog = void 0;
//     loadingPermissionCatalog.value = false;
//   }
// }
// async function createPermissionCatalog(params: Partial<PermissionCatalogItem>) {
//   const $dialog = toValue(editorPermissionCatalogRef);
//   if (!$dialog) return;
//   await $dialog
//     .opener({ ...params }, async (req) => {
//       const { success, message, data } = await addPermissionCatalog({ ...req });
//       if (!success) throw Object.assign(new Error(message), { success, data });
//       ElMessage.success(`${t("axios.Operation successful")}`);
//     })
//     .catch(() => Promise.resolve(void 0))
//     .finally(() => querysPermissionCatalog());
// }
// async function editorPermissionCatalog(params: Partial<PermissionCatalogItem>) {
//   const $dialog = toValue(editorPermissionCatalogRef);
//   if (!$dialog) return;
//   await $dialog
//     .opener({ ...params }, async (req) => {
//       const { success, message, data } = await modPermissionCatalog({ ...req });
//       if (!success) throw Object.assign(new Error(message), { success, data });
//       ElMessage.success(`${t("axios.Operation successful")}`);
//     })
//     .catch(() => Promise.resolve(void 0))
//     .finally(() => querysPermissionCatalog());
// }
// async function deletePermissionCatalog(params: Partial<PermissionCatalogItem>) {
//   ElMessageBox.confirm(`确定删除${params.name}?`, "删除权限目录", {
//     confirmButtonText: "确定",
//     cancelButtonText: "取消",
//     draggable: true,
//     async beforeClose(action, instance, done) {
//       switch (action) {
//         case "confirm": {
//           const confirmButtonText = instance.confirmButtonText;
//           try {
//             instance.confirmButtonLoading = true;
//             instance.confirmButtonText = "Loading...";
//             const { success, data, message } = await delPermissionCatalog({ ids: [params.id!] });
//             if (!success) throw Object.assign(new Error(message), { success, data });
//             ElMessage.success(`${t("axios.Operation successful")}`);
//             expandPermissionCatalog.value.clear();
//             await router.isReady();
//             await nextTick();
//             done();
//           } catch (error) {
//             if (error instanceof Error) ElMessage.error(error.message);
//           } finally {
//             instance.confirmButtonLoading = false;
//             instance.confirmButtonText = confirmButtonText;
//           }
//           break;
//         }
//         default: {
//           done();
//           break;
//         }
//       }
//     },
//   })
//     .catch(() => Promise.resolve())
//     .finally(() => querysPermissionCatalog());
// }
/* ======================================================================================================================================= */
const loadingPermissionGroup = ref(false);
const AppendQueuePermissionGroup = generateQueueHook(loadingPermissionGroup, (loading) => !loading);
let controllerPermissionGroup: AbortController | undefined = void 0;
const listPermissionGroup = ref<PermissionGroupItem[]>([]);
const expandPermissionGroup = ref(new Set<PermissionGroupItem["id"]>());
const selectPermissionGroup = computed(() => {
  return toValue(listPermissionGroup).reduce((p, c) => {
    if (!c.enabled) return p;
    const option = toValue(listPermissionOption).filter((v) => v.groupId === c.id);
    return option.every((v) => v.enabled) ? p.concat(c.id) : p;
  }, [] as string[]);
});
// const currentPermissionGroup = computed<null | PermissionGroupItem>(() => {
//   const $list = toValue(listPermissionGroup);
//   for (let i = 0; i < $list.length; i++) if ($list[i].id === toValue(selectPermissionGroup)) return $list[i];
//   return null;
// });
const editorPermissionGroupRef = ref<InstanceType<typeof EditorPermissionGroup>>();
async function querysPermissionGroup() {
  if (controllerPermissionGroup) controllerPermissionGroup.abort();
  if (toValue(loadingPermissionGroup)) await new AppendQueuePermissionGroup(true);
  const result = getPermissionGroup({ appId: props.data.id });
  controllerPermissionGroup = result.controller;
  try {
    loadingPermissionGroup.value = true;
    const { success, message, data } = await result;
    if (!success) throw Object.assign(new Error(message), { success, data });
    listPermissionGroup.value = data instanceof Array ? data : [];
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    controllerPermissionGroup = void 0;
    loadingPermissionGroup.value = false;
  }
}
async function createPermissionGroup(params: Partial<PermissionGroupItem>) {
  const $dialog = toValue(editorPermissionGroupRef);
  if (!$dialog) return;
  await $dialog
    .opener({ ...params, orderNum: toValue(listPermissionGroup).length }, async (req) => {
      const { success, message, data } = await addPermissionGroup({ ...req });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`${t("axios.Operation successful")}`);
    })
    .catch(() => Promise.resolve(void 0))
    .finally(() => querysPermissionGroup());
}
async function editorPermissionGroup(params: Partial<PermissionGroupItem>) {
  const $dialog = toValue(editorPermissionGroupRef);
  if (!$dialog) return;
  await $dialog
    .opener({ ...params }, async (req) => {
      const { success, message, data } = await modPermissionGroup({ ...req });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`${t("axios.Operation successful")}`);
    })
    .catch(() => Promise.resolve(void 0))
    .finally(() => querysPermissionGroup());
}
async function deletePermissionGroup(params: Partial<PermissionGroupItem>) {
  ElMessageBox.confirm(`确定删除${params.name}?`, "删除权限分组", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true,
    async beforeClose(action, instance, done) {
      switch (action) {
        case "confirm": {
          const confirmButtonText = instance.confirmButtonText;
          try {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "Loading...";
            const { success, data, message } = await delPermissionGroup({ id: params.id! });
            if (!success) throw Object.assign(new Error(message), { success, data });
            ElMessage.success(`${t("axios.Operation successful")}`);
            expandPermissionGroup.value.clear();
            await router.isReady();
            await nextTick();
            done();
          } catch (error) {
            if (error instanceof Error) ElMessage.error(error.message);
          } finally {
            instance.confirmButtonLoading = false;
            instance.confirmButtonText = confirmButtonText;
          }
          break;
        }
        default: {
          done();
          break;
        }
      }
    },
  })
    .catch(() => Promise.resolve())
    .finally(() => querysPermissionGroup());
}
/* ======================================================================================================================================= */
const loadingPermissionOption = ref(false);
const AppendQueuePermissionOption = generateQueueHook(loadingPermissionOption, (loading) => !loading);
let controllerPermissionOption: AbortController | undefined = void 0;
const listPermissionOption = ref<PermissionOptionItem[]>([]);
const expandPermissionOption = ref(new Set<PermissionOptionItem["id"]>());
const selectPermissionOption = computed(() => {
  return toValue(listPermissionOption).reduce((p, c) => (c.enabled ? p.concat(c.id) : p), [] as string[]);
});
// const currentPermissionOption = computed<null | PermissionOptionItem>(() => {
//   const $list = toValue(listPermissionOption);
//   for (let i = 0; i < $list.length; i++) if ($list[i].id === toValue(selectPermissionOption)) return $list[i];
//   return null;
// });
const editorPermissionOptionRef = ref<InstanceType<typeof EditorPermissionOption>>();
async function querysPermissionOption() {
  if (controllerPermissionOption) controllerPermissionOption.abort();
  if (toValue(loadingPermissionOption)) await new AppendQueuePermissionOption(true);
  const result = getPermissionOption({ appId: props.data.id });
  controllerPermissionOption = result.controller;
  try {
    loadingPermissionOption.value = true;
    const { success, message, data } = await result;
    if (!success) throw Object.assign(new Error(message), { success, data });
    listPermissionOption.value = data instanceof Array ? data : [];
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    controllerPermissionOption = void 0;
    loadingPermissionOption.value = false;
  }
}
async function createPermissionOption(params: Partial<PermissionOptionItem>) {
  const $dialog = toValue(editorPermissionOptionRef);
  if (!$dialog) return;
  await $dialog
    .opener({ ...params, appId: props.data.id }, async (req) => {
      const { success, message, data } = await addPermissionOption({ ...req });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`${t("axios.Operation successful")}`);
    })
    .catch(() => Promise.resolve(void 0))
    .finally(() => querysPermissionOption());
}
async function editorPermissionOption(params: Partial<PermissionOptionItem>) {
  const $dialog = toValue(editorPermissionOptionRef);
  if (!$dialog) return;
  await $dialog
    .opener({ ...params, appId: props.data.id }, async (req) => {
      const { success, message, data } = await modPermissionOption({ ...req });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`${t("axios.Operation successful")}`);
    })
    .catch(() => Promise.resolve(void 0))
    .finally(() => querysPermissionOption());
}
async function deletePermissionOption(params: Partial<PermissionOptionItem>) {
  ElMessageBox.confirm(`确定删除${params.name}?`, "删除配置项", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true,
    async beforeClose(action, instance, done) {
      switch (action) {
        case "confirm": {
          const confirmButtonText = instance.confirmButtonText;
          try {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "Loading...";
            const { success, data, message } = await delPermissionOption({ id: params.id! });
            if (!success) throw Object.assign(new Error(message), { success, data });
            ElMessage.success(`${t("axios.Operation successful")}`);
            expandPermissionOption.value.clear();
            await router.isReady();
            await nextTick();
            done();
          } catch (error) {
            if (error instanceof Error) ElMessage.error(error.message);
          } finally {
            instance.confirmButtonLoading = false;
            instance.confirmButtonText = confirmButtonText;
          }
          break;
        }
        default: {
          done();
          break;
        }
      }
    },
  })
    .catch(() => Promise.resolve())
    .finally(() => querysPermissionOption());
}
/* ======================================================================================================================================= */
const loadingPermissionAuth = ref(false);
const AppendQueuePermissionAuth = generateQueueHook(loadingPermissionAuth, (loading) => !loading);
let controllerPermissionAuth: AbortController | undefined = void 0;
const listPermissionAuth = ref<PermissionAuthItem[]>([]);
const expandPermissionAuth = ref(new Set<PermissionAuthItem["id"]>());
const selectPermissionAuth = computed(() => toValue(listPermissionAuth).reduce((p, c) => (c.enabled ? p.concat(c.id) : p), [] as string[]));
// const currentPermissionAuth = computed<null | PermissionAuthItem>(() => {
//   const $list = toValue(listPermissionAuth);
//   for (let i = 0; i < $list.length; i++) if ($list[i].id === toValue(selectPermissionAuth)) return $list[i];
//   return null;
// });
const editorPermissionAuthRef = ref<InstanceType<typeof EditorPermissionAuth>>();
async function querysPermissionAuth(groupId?: string, catalogId?: string) {
  if (controllerPermissionAuth) controllerPermissionAuth.abort();
  if (toValue(loadingPermissionAuth)) await new AppendQueuePermissionAuth(true);
  const result = getPermissionAuth({ appId: props.data.id, catalogId, groupId });
  controllerPermissionAuth = result.controller;
  try {
    loadingPermissionAuth.value = true;
    const { success, message, data } = await result;
    if (!success) throw Object.assign(new Error(message), { success, data });
    listPermissionAuth.value = data instanceof Array ? data : [];
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    controllerPermissionAuth = void 0;
    loadingPermissionAuth.value = false;
  }
}
async function createPermissionAuth(params: Partial<PermissionAuthItem>) {
  const $dialog = toValue(editorPermissionAuthRef);
  if (!$dialog) return;
  await $dialog
    .opener({ ...params, appId: props.data.id }, async (req) => {
      const { success, message, data } = await addPermissionAuth({ ...req });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`${t("axios.Operation successful")}`);
    })
    .catch(() => Promise.resolve(void 0))
    .finally(() => querysPermissionAuth());
}
async function editorPermissionAuth(params: Partial<PermissionAuthItem>) {
  const $dialog = toValue(editorPermissionAuthRef);
  if (!$dialog) return;
  await $dialog
    .opener({ ...params, appId: props.data.id }, async (req) => {
      const { success, message, data } = await modPermissionAuth({ ...req });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`${t("axios.Operation successful")}`);
    })
    .catch(() => Promise.resolve(void 0))
    .finally(() => querysPermissionAuth());
}
async function deletePermissionAuth(params: Partial<PermissionAuthItem>) {
  ElMessageBox.confirm(`确定删除${params.name}?`, "删除权限", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true,
    async beforeClose(action, instance, done) {
      switch (action) {
        case "confirm": {
          const confirmButtonText = instance.confirmButtonText;
          try {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "Loading...";
            const { success, data, message } = await delPermissionAuth({ ids: [params.id!] });
            if (!success) throw Object.assign(new Error(message), { success, data });
            ElMessage.success(`${t("axios.Operation successful")}`);
            expandPermissionAuth.value.clear();
            await router.isReady();
            await nextTick();
            done();
          } catch (error) {
            if (error instanceof Error) ElMessage.error(error.message);
          } finally {
            instance.confirmButtonLoading = false;
            instance.confirmButtonText = confirmButtonText;
          }
          break;
        }
        default: {
          done();
          break;
        }
      }
    },
  })
    .catch(() => Promise.resolve())
    .finally(() => querysPermissionAuth());
}
/* ======================================================================================================================================= */
// const loadingPermissionTemplate = ref(false);
// const AppendQueuePermissionTemplate = generateQueueHook(loadingPermissionTemplate, (loading) => !loading);
// let controllerPermissionTemplate: AbortController | undefined = void 0;
// const listPermissionTemplate = ref<PermissionTemplateItem[]>([]);
// const expandPermissionTemplate = ref(new Set<PermissionTemplateItem["id"]>());
// // const selectPermissionTemplate = computed(() => toValue(listPermissionTemplate).reduce((p, c) => (c.enabled ? p.concat(c.id) : p), [] as string[]));
// // const currentPermissionTemplate = computed<null | PermissionTemplateItem>(() => {
// //   const $list = toValue(listPermissionTemplate);
// //   for (let i = 0; i < $list.length; i++) if ($list[i].id === toValue(selectPermissionTemplate)) return $list[i];
// //   return null;
// // });
// const editorPermissionTemplateRef = ref<InstanceType<typeof EditorPermissionTemplate>>();
// async function querysPermissionTemplate(groupId?: string) {
//   if (controllerPermissionTemplate) controllerPermissionTemplate.abort();
//   if (toValue(loadingPermissionTemplate)) await new AppendQueuePermissionTemplate(true);
//   const result = getPermissionTemplate({ appId: props.data.id, groupId });
//   controllerPermissionTemplate = result.controller;
//   try {
//     loadingPermissionTemplate.value = true;
//     const { success, message, data } = await result;
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     listPermissionTemplate.value = data instanceof Array ? data : [];
//   } catch (error) {
//     if (error instanceof Error) ElMessage.error(error.message);
//   } finally {
//     controllerPermissionTemplate = void 0;
//     loadingPermissionTemplate.value = false;
//   }
// }
// async function createPermissionTemplate(params: Partial<PermissionTemplateItem>) {
//   const $dialog = toValue(editorPermissionTemplateRef);
//   if (!$dialog) return;
//   await $dialog
//     .opener({ ...params, rootId: props.data.id }, async (req) => {
//       const { success, message, data } = await addPermissionTemplate({ ...req });
//       if (!success) throw Object.assign(new Error(message), { success, data });
//       ElMessage.success(`${t("axios.Operation successful")}`);
//     })
//     .catch(() => Promise.resolve(void 0))
//     .finally(() => querysPermissionTemplate());
// }
// async function editorPermissionTemplate(params: Partial<PermissionTemplateItem>) {
//   const $dialog = toValue(editorPermissionTemplateRef);
//   if (!$dialog) return;
//   await $dialog
//     .opener({ ...params, rootId: props.data.id }, async (req) => {
//       const { success, message, data } = await modPermissionTemplate({ ...req });
//       if (!success) throw Object.assign(new Error(message), { success, data });
//       ElMessage.success(`${t("axios.Operation successful")}`);
//     })
//     .catch(() => Promise.resolve(void 0))
//     .finally(() => querysPermissionTemplate());
// }
// async function deletePermissionTemplate(params: Partial<PermissionTemplateItem>) {
//   ElMessageBox.confirm(`确定删除${params.name}?`, "删除权限模板", {
//     confirmButtonText: "确定",
//     cancelButtonText: "取消",
//     draggable: true,
//     async beforeClose(action, instance, done) {
//       switch (action) {
//         case "confirm": {
//           const confirmButtonText = instance.confirmButtonText;
//           try {
//             instance.confirmButtonLoading = true;
//             instance.confirmButtonText = "Loading...";
//             const { success, data, message } = await delPermissionTemplate({ id: params.id! });
//             if (!success) throw Object.assign(new Error(message), { success, data });
//             ElMessage.success(`${t("axios.Operation successful")}`);
//             expandPermissionTemplate.value.clear();
//             await router.isReady();
//             await nextTick();
//             done();
//           } catch (error) {
//             if (error instanceof Error) ElMessage.error(error.message);
//           } finally {
//             instance.confirmButtonLoading = false;
//             instance.confirmButtonText = confirmButtonText;
//           }
//           break;
//         }
//         default: {
//           done();
//           break;
//         }
//       }
//     },
//   })
//     .catch(() => Promise.resolve())
//     .finally(() => querysPermissionTemplate());
// }
/* ======================================================================================================================================= */
watch<string, true>(
  () => props.data.id,
  async function () {
    await handleStateRefresh();
  },
  { immediate: true, flush: "post" }
);
/* ================================================================================================== */

const treeRef = ref<InstanceType<typeof import("element-plus").ElTree>>();

async function setAllEnabled(auths: { permission: PermissionAuthItem[] }) {
  const dataList = auths.permission.every((v) => v.enabled) ? auths.permission.map((v) => Object.assign(v, { enabled: false })) : auths.permission.map((v) => Object.assign(v, { enabled: true }));
  await Promise.all(dataList.map((v) => modPermissionAuth({ id: v.id, enabled: v.enabled })));
  await querysPermissionAuth();
}

// const editorCatalogRef = ref<InstanceType<typeof EditorCatalogData>>();

// async function createCatalogItem(params: Partial<DataItem>, onCleanup?: (cleanupFn: () => void) => void) {
//   await nextTick();
//   if (typeof onCleanup === "function") onCleanup(() => editorCatalogRef.value?.close());
//   if (!editorCatalogRef.value) return;
//   try {
//     await editorCatalogRef.value.open({ ...params, "#TYPE": EditorType.Add }, async (req) => {
//       try {
//         const { success, message, data } = await addItem({ ...req, appId: props.data.name, orderNum: params.parentId ? params.orderNum || 0 : state.data.length });
//         if (success) {
//           ElMessage.success(`添加成功！`);
//           if (!params.parentId) scrollbarRef.value && (scrollPosition.value.scrollTop = (<HTMLDivElement>scrollbarRef.value?.wrapRef).scrollHeight);
//           else {
//             if (treeRef.value) {
//               const element = (<HTMLDivElement>treeRef.value.$el).getElementsByClassName(params.parentId).item(0) as HTMLDivElement | null;
//               element && (scrollPosition.value.scrollTop = element.offsetTop);
//             }
//           }
//           return true;
//         } else throw Object.assign(new Error(message), { success, data });
//       } catch (error) {
//         if (error instanceof Error) ElMessage.error(error.message);
//         return false;
//       }
//     });
//   } catch (error) {
//     /*  */
//   }
// }
// async function editorCatalogItem(params: Partial<DataItem>, onCleanup?: (cleanupFn: () => void) => void) {
//   await nextTick();
//   if (typeof onCleanup === "function") onCleanup(() => editorCatalogRef.value?.close());
//   if (!editorCatalogRef.value) return;
//   try {
//     await editorCatalogRef.value.open({ ...params, "#TYPE": EditorType.Mod }, async (req) => {
//       try {
//         const { success, message, data } = await modItem({ ...req, orderNum: params.orderNum });
//         if (success) {
//           ElMessage.success(`编辑成功！`);
//           if (!params.parentId) scrollbarRef.value && (scrollPosition.value.scrollTop = (<HTMLDivElement>scrollbarRef.value?.wrapRef).scrollHeight);
//           else {
//             if (treeRef.value) {
//               const element = (<HTMLDivElement>treeRef.value.$el).getElementsByClassName(params.parentId).item(0) as HTMLDivElement | null;
//               element && (scrollPosition.value.scrollTop = element.offsetTop);
//             }
//           }
//           return true;
//         } else throw Object.assign(new Error(message), { success, data });
//       } catch (error) {
//         if (error instanceof Error) ElMessage.error(error.message);
//         return false;
//       }
//     });
//   } catch (error) {
//     /*  */
//   }
// }
// async function deleteCatalogItem(params: Partial<DataItem>, onCleanup?: (cleanupFn: () => void) => void) {
//   await nextTick();
//   if (typeof onCleanup === "function") onCleanup(() => editorCatalogRef.value?.close());
//   if (!editorCatalogRef.value) return;
//   try {
//     await editorCatalogRef.value.open({ ...params, "#TYPE": EditorType.Del }, async (req) => {
//       try {
//         const { success, message, data } = await delItem({ id: req.id as string });
//         if (success) {
//           ElMessage.success(`删除成功！`);
//           if (!params.parentId) scrollbarRef.value && (scrollPosition.value.scrollTop = (<HTMLDivElement>scrollbarRef.value?.wrapRef).scrollHeight);
//           else {
//             if (treeRef.value) {
//               const element = (<HTMLDivElement>treeRef.value.$el).getElementsByClassName(params.parentId).item(0) as HTMLDivElement | null;
//               element && (scrollPosition.value.scrollTop = element.offsetTop);
//             }
//           }
//           return true;
//         } else throw Object.assign(new Error(message), { success, data });
//       } catch (error) {
//         if (error instanceof Error) ElMessage.error(error.message);
//         return false;
//       }
//     });
//   } catch (error) {
//     /*  */
//   }
// }
// async function querysCatalogItem(params: Record<"appId", string>, onCleanup?: (cleanupFn: () => void) => void) {
//   if (!params.appId) return [];
//   let controller = new AbortController();
//   const response = getItem({ appId: params.appId as string, controller });
//   if (typeof onCleanup === "function") onCleanup(() => controller.abort());
//   try {
//     const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await response;
//     if (success) {
//       state.page = Number(page);
//       state.size = Number(size);
//       state.total = Number(total);
//       return (data instanceof Array ? data : [])
//         .map((value, _index, full) => {
//           if (value.id === value.parentId) return value;
//           else {
//             return Object.assign(value, {
//               children: full
//                 .filter(({ parentId }) => parentId === value.id)
//                 .map((v) => Object.assign(v, { consume: true }))
//                 .sort((a, b) => Number(a.orderNum) - Number(b.orderNum)),
//             });
//           }
//         })
//         .filter((v: import("@/api/application").AuthCatalogItem & { consume?: boolean }) => {
//           const consume = v.consume;
//           if (consume) delete v.consume;
//           return !consume;
//         })
//         .sort((a, b) => Number(a.orderNum) - Number(b.orderNum));
//     } else throw Object.assign(new Error(message), { success, data });
//   } catch (error) {
//     if (error instanceof Error) ElMessage.error(error.message);
//     state.page = Number(1);
//     state.size = Number(20);
//     state.total = Number(0);
//     return [];
//   } finally {
//     controller = new AbortController();
//   }
// }

/*********************************************************/

// interface StateData<T> {
//   loading: boolean;
//   move: boolean;
//   select: string[];
//   current: T | null;
//   filter: { [key: string]: unknown };
//   expand: string[];
//   search: { [key: string]: string };
//   column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode }[];
//   data: T[];
//   page: number;
//   size: number;
//   sizes: typeof sizes;
//   total: number;
// }
// const state = reactive<StateData<DataItem>>({
//   loading: false,
//   move: false,
//   select: [],
//   current: null,
//   filter: {},
//   expand: [],
//   search: {
//     keyword: "",
//   },
//   column: [
//     /* 列 */
//     // { key: "id", label: "ID", width: 220 },
//     // { key: "title", label: "标题", width: 220 },
//     { key: "name", label: "路由名称", width: 160 },
//     // { key: "createdTime", label: "创建日期", formatter: (_row, _col, v) => formatterDate(v as string) },
//     // { key: "updatedTime", label: "修改日期", formatter: (_row, _col, v) => formatterDate(v as string) },
//   ],
//   data: [],
//   page: 1,
//   size: 20,
//   sizes,
//   total: 0,
// });

// async function handleStateCreate(params: Partial<DataItem>) {
//   await createCatalogItem(params);
//   await handleStateRefresh();
// }
// async function handleStateEditor(params: Partial<DataItem>) {
//   await editorCatalogItem(params);
//   await handleStateRefresh();
// }
// async function handleStateDelete(params: Partial<DataItem>) {
//   if (state.expand.includes(params.id!)) state.expand.splice(state.expand.indexOf(params.id!));
//   await deleteCatalogItem(params);
//   await handleStateRefresh();
// }
// async function handleStateRefresh() {
//   if (state.loading) return;
//   state.loading = true;
//   // state.data = [];
//   await nextTick();
//   const $data = await querysCatalogItem({ appId: props.data.name });
//   try {
//     const { success, message, data } = await getAppAuth({ appId: props.data.id });
//     if (!success) throw Object.assign(new Error(message), { success, data });
//     if (data instanceof Array) {
//       getTreAutheData(
//         $data,
//         data
//           .map((value, _index, full) => {
//             if (value.id === value.catalogId) return value;
//             else {
//               return Object.assign(value, {
//                 children: full
//                   .filter(({ catalogId }) => catalogId === value.id)
//                   .map((v) => Object.assign(v, { consume: true }))
//                   .sort((a, b) => Number(a.orderNum) - Number(b.orderNum)),
//               });
//             }
//           })
//           .filter((v: AuthorityItem & { consume?: boolean }) => {
//             const consume = v.consume;
//             if (consume) delete v.consume;
//             return !consume;
//           })
//           .sort((a, b) => Number(a.orderNum) - Number(b.orderNum))
//           .reduce((p, c) => {
//             const item = p.get(c.catalogId);
//             if (item) item.push(c);
//             else p.set(c.catalogId, [c]);
//             return p;
//           }, new Map<string, AuthorityItem[]>())
//       );
//     }
//   } catch (error) {
//     if (error instanceof Error) ElMessage.error(error.message);
//   }
//   /* 处理选中 */
//   const select: string[] = [];
//   state.select = [];
//   const treeData = treeIterators($data);
//   let treeResult: IteratorResult<DataItem>;
//   while (!(treeResult = treeData.next()).done) {
//     if (treeResult.value.enabled && treeResult.value.children.every((v) => v.enabled)) select.push(treeResult.value.id);
//   }
//   state.data.splice(0, state.data.length, ...$data);
//   await nextTick();
//   state.select.push(...select);
//   treeRef.value && treeRef.value.setCheckedKeys(state.select);
//   state.loading = false;
//   await new Promise((resolve) => setTimeout(resolve));
//   await nextTick();
//   scrollbarRef.value && scrollbarRef.value.scrollTo({ top: scrollPosition.value.scrollTop, left: scrollPosition.value.scrollLeft, behavior: "smooth" });
// }

// function getTreAutheData(dataList: DataItem[], mergeList: Map<string, AuthorityItem[]>) {
//   for (let i = 0; i < dataList.length; i++) {
//     if (!mergeList.size) break;
//     const item = mergeList.get(dataList[i].id);
//     if (item) {
//       mergeList.delete(dataList[i].id);
//       Object.assign(dataList[i], { permission: item });
//     }
//     if (dataList[i].children instanceof Array) getTreAutheData(dataList[i].children, mergeList);
//   }
// }

function allowDrop(draggingNode: Node, dropNode: Node, dropType: AllowDropType) {
  // console.log("allowDrop", draggingNode.data.name, dropNode.data.name, dropType);
  switch (dropType) {
    case "prev":
      // // console.log(`准备拖拽\`${draggingNode.label}\` 插入 \`${dropNode.label}\`之前，父级ID：${dropNode.parent.key}，排序：${dropNode.parent.childNodes.indexOf(dropNode) - 1}`);
      return draggingNode.data.type === dropNode.data.type;
    case "inner":
      // // console.log(`准备拖拽\`${draggingNode.label}\` 插入 \`${dropNode.label}\`之内，父级ID：${dropNode.key}，排序：${dropNode.childNodes.length}`);
      return draggingNode.data.id !== dropNode.data.id && draggingNode.data.type === "OPTION" && dropNode.data.type === "GROUP";
    case "next":
      // // console.log(`准备拖拽\`${draggingNode.label}\` 插入 \`${dropNode.label}\`之后，父级ID：${dropNode.parent.key}，排序：${dropNode.parent.childNodes.indexOf(dropNode) - 1}`);
      return draggingNode.data.type === dropNode.data.type;
    default:
      return false;
  }
}
// function handleDragStart(node: Node) {
//   // console.log(`开始拖拽 \`${node.label}\` 节点`);
// }
// function handleDragEnter(draggingNode: Node, dropNode: Node, ev: DragEvents) {
//   // console.log(`拖拽 \`${draggingNode.label}\` 进入 \`${dropNode.label}\` 节点`);
// }
// function handleDragLeave(draggingNode: Node, dropNode: Node, ev: DragEvents) {
//   // console.log(`拖拽 \`${draggingNode.label}\` 离开 \`${dropNode.label}\` 节点`);
// }
// function handleDragOver(draggingNode: Node, dropNode: Node, ev: DragEvents) {
//   // console.log(`拖拽节点：\`${draggingNode.label}\` 拖拽至 \`${dropNode.label}\``);
// }
// async function handleDragEnd(draggingNode: Node, dropNode: Node, dropType: NodeDropType) {
//   // // console.log(draggingNode, dropNode);
//   // await nextTick();
//   // switch (dropType) {
//   //   case "before":
//   //     // console.log(`拖拽结束\`${draggingNode.label}\` 插入 \`${dropNode.label}\`之前，父级ID：${dropNode.parent.key}，排序：${dropNode.parent.childNodes.indexOf(dropNode) - 1}`);
//   //     break;
//   //   case "inner":
//   //     // console.log(`拖拽结束\`${draggingNode.label}\` 插入 \`${dropNode.label}\`之内，父级ID：${dropNode.key}，排序：${dropNode.childNodes.length}`);
//   //     break;
//   //   case "after":
//   //     // console.log(`拖拽结束\`${draggingNode.label}\` 插入 \`${dropNode.label}\`之后，父级ID：${dropNode.parent.key}，排序：${dropNode.parent.childNodes.indexOf(dropNode) - 1}`);
//   //     break;
//   //   default:
//   //     // console.log(`节点 \`${draggingNode.label}\` 拖拽至 \`${dropNode.label}\` 无变化，父级ID：${draggingNode.key}，排序：${draggingNode.data.order}`);
//   //     break;
//   // }
// }
function getTreeDataChildren<T extends object>(data: T[], getKey: T[keyof T], option?: Partial<{ key: keyof T; children: keyof T }>): T[] | null {
  const key = ((option || {}).key || "id") as keyof T;
  const children = ((option || {}).children || "children") as keyof T;
  if (!getKey) return data;
  for (let i = 0; i < data.length; i++) {
    if (data[i][key] === getKey) {
      return data[i][children] as unknown as T[];
    }
    if (<any>data[i][children] instanceof Array) {
      const find = getTreeDataChildren(data[i][children] as unknown as T[], getKey, { key, children });
      if (find) {
        return find;
      }
    }
  }
  return null;
}
function getTreeDataItem<T extends object>(data: T[], getKey: T[keyof T], option?: Partial<{ key: keyof T; children: keyof T }>): T | null {
  const key = ((option || {}).key || "id") as keyof T;
  const children = ((option || {}).children || "children") as keyof T;
  if (!getKey) return null;
  for (let i = 0; i < data.length; i++) {
    if (data[i][key] === getKey) {
      return data[i];
    }
    if (<any>data[i][children] instanceof Array) {
      const find = getTreeDataItem(data[i][children] as unknown as T[], getKey, { key, children });
      if (find) {
        return find;
      }
    }
  }
  return null;
}
function* treeIterators<T extends object>(data: T[], option?: Partial<{ children: keyof T }>): Generator<T> {
  const children = ((option || {}).children || "children") as keyof T;
  for (let i = 0; i < data.length; i++) {
    yield data[i];
    if (<any>data[i][children] instanceof Array && (data[i][children] as unknown as T[]).length) yield* treeIterators(data[i][children] as unknown as T[], { children });
  }
}
</script>

<style lang="scss" scoped>
.list-item {
  position: relative;

  .move_bar {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    cursor: move;
  }
  &:hover {
    &::before {
      display: block;
    }
  }
  &::before {
    background-color: #666;
    border-radius: 50%;
    box-shadow:
      0 6px 0 0 #666,
      0 12px 0 0 #666,
      0 -6px 0 0 #666,
      6px 0 0 0 #3b3b3b,
      6px 6px 0 0 #3b3b3b,
      6px -6px 0 0 #3b3b3b,
      6px 12px 0 0 #3b3b3b;
    content: "";
    display: none;
    flex-shrink: 0;
    width: 2px;
    height: 2px;
    position: absolute;
    top: calc(50% - 4px);
    left: 5px;
  }
}
</style>
<style lang="scss" scoped>
.tree_context {
  margin-top: 1em;
  .el-tree-node__content {
    height: fit-content;
    align-items: flex-start;
  }
  &.tree_data_dir {
    --text-color: var(--el-text-color-primary);
  }
  &.tree_data_button {
    --text-color: var(--el-color-primary);
  }
}
.tree_item_group {
  border-top: var(--el-border);
}
// .custom-tree {
//   :deep(.tree_context) {
//     margin-top: 1em;
//     .el-tree-node__content {
//       height: fit-content;
//       align-items: flex-start;
//     }
//     &.tree_data_dir {
//       --text-color: var(--el-text-color-primary);
//     }
//     &.tree_data_button {
//       --text-color: var(--el-color-primary);
//     }
//   }
// }
</style>
