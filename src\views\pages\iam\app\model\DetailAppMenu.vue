<template>
  <el-scrollbar>
    <div class="flex-search" :style="{ minWidth: `${props.width - 2}px` }">
      <div class="left">
        <!-- <el-button type="primary" :icon="Plus" @click="exportMenuFile()">导出菜单</el-button> -->
        <!-- <el-button type="primary" :icon="Plus" @click="open()">导入菜单</el-button> -->
        <slot name="left" :create="handleStateCreate" :delete="handleStateDelete" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot>
      </div>
      <div class="center"><slot name="center" :create="handleStateCreate" :delete="handleStateDelete" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot></div>
      <div class="right"><slot name="right" :create="handleStateCreate" :delete="handleStateDelete" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot></div>
    </div>
  </el-scrollbar>
  <el-table ref="table" class="table" v-loading="state.loading" :border="true" stripe row-key="name" :expand-row-keys="state.expand" :row-class-name="(data) => `[${(data.row as NavItem).name || ''}]`" :data="state.data" :height="props.height - 64 - 20 - (state.total ? 32 : 0)" :style="{ width: `${props.width - 40}px`, margin: '0 auto' }" @expand-change="(expandedRows, expanded) => (expanded ? state.expand.push(expandedRows.name) : state.expand.splice(state.expand.indexOf(expandedRows.name)))">
    <el-table-column prop="title" label="标题" :width="300" fixed="left">
      <template #default="{ row, column }">
        <Icon :name="row.icon" size="18px" style="margin-right: 4px" />
        <span>{{ row[column.property] }}</span>
        <div class="tw-float-right tw-flex">
          <div class="checkbox-virtually tw-mx-[6px]">
            <label class="toggle-button">
              <input type="checkbox" v-model="row.enabled" style="display: none; visibility: hidden" @change="handleStateEnabled(row)" />
              <div class="virtually">
                <svg viewBox="0 0 44 44">
                  <path d="M14,24 L21,31 L39.7428882,11.5937758 C35.2809627,6.53125861 30.0333333,4 24,4 C12.95,4 4,12.95 4,24 C4,35.05 12.95,44 24,44 C35.05,44 44,35.05 44,24 C44,19.3 42.5809627,15.1645919 39.7428882,11.5937758" transform="translate(-2.000000, -2.000000)"></path>
                </svg>
              </div>
            </label>
          </div>
          <el-button class="move-column" :icon="Rank" size="small" style="padding: 5px"></el-button>
        </div>
      </template>
    </el-table-column>
    <el-table-column v-for="column in state.column" show-overflow-tooltip :key="column.key" :prop="column.key" :label="column.label" :min-width="column.width || 120" :formatter="column.formatter" />
    <el-table-column :label="$t('glob.operate')" align="right" header-align="left" :width="110" fixed="right">
      <template #header="{ column }">
        <div style="display: flex; justify-content: center">
          <span style="margin: 0 10px">{{ column.label }}</span>
          <el-link type="primary" :underline="false" :icon="Refresh" @click="handleStateRefresh()"></el-link>
        </div>
      </template>
      <template #default="{ row }">
        <!-- {{ row }} -->
        <el-button v-if="![appType.BUTTON].includes(row.type)" link type="primary" :icon="Plus" :disabled="state.move" @click="handleStateCreate(row)" :title="$t('glob.add')"></el-button>
        <el-button link type="primary" :icon="Edit" :disabled="state.move" @click="handleStateEditor(row)" :title="$t('glob.edit')"></el-button>
        <el-button link type="danger" :icon="Delete" :disabled="state.move || (row.children instanceof Array ? row.children : []).length !== 0" @click="handleStateDelete(row)" :title="$t('glob.delete')"></el-button>
      </template>
    </el-table-column>
  </el-table>
  <div :style="{ margin: '8px 20px 20px' }">
    <el-pagination v-show="state.total" v-model:current-page="state.page" v-model:page-size="state.size" :page-sizes="state.sizes" :total="state.total" layout="->, total, sizes, prev, pager, next, jumper" small @size-change="handleStateRefresh()" @current-change="handleStateRefresh()" />
  </div>
  <EditorDetailsByMenu ref="editorRef" title="菜单"></EditorDetailsByMenu>
</template>

<script setup lang="ts" name="WebMenu">
import { ref, reactive, nextTick, watch, onMounted, onBeforeUnmount, h, defineComponent, renderSlot } from "vue";
import { useClipboard } from "@vueuse/core";
import { useI18n } from "vue-i18n";
import { sizes } from "@/utils/common";
// import { formatterDate } from "@/utils/date";
import Sortable from "sortablejs";
import { cloneDeep } from "lodash-es";
import { getTreeItemById, spliceTreeItemById, appendTreeItemById } from "@/utils/table";
// import { useFileDialog } from "@vueuse/core";

// Ui
import { ElMessage, ElButton, ElTag, ElLink } from "element-plus";
import type { TableInstance } from "element-plus";
// eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
import { Checked, CopyDocument, Refresh, Plus, Edit, Delete, Rank } from "@element-plus/icons-vue";

// Api
import { getMenu, addMenu, modMenu, delMenu, appType, modMenuByOrder, type NavItem } from "@/api/application";
// import { getMenu, addMenu, modMenu, delMenu, modMenuByParent, modMenuByOrder, exportMenu, importMenu, appType, type AppItem, type MenuItem, type MenuMeta } from "@/api/application";

// Editor
import { EditorType } from "@/views/common/interface";
import EditorDetailsByMenu from "./EditorDetailsByMenu.vue";
// import moment from "moment";

// const { files, open, reset } = useFileDialog({ multiple: false, accept: "application/json" });
// watch(files, (files) => {
//   if (files instanceof FileList) {
//     const _file = files.item(0);
//     if (_file instanceof File) {
//       const file = _file;
//       reset();
//       importMenuFile(file);
//     }
//   }
// });
const { t } = useI18n();

const CopyRender = defineComponent({
  props: { value: { type: String, default: "" } },
  setup(props, ctx) {
    const { copy, copied, isSupported } = useClipboard({ read: false, legacy: true });
    function copyData() {
      copy(props.value)
        .then(() => ElMessage.success("成功复制！"))
        .catch((error) => ElMessage.error(error instanceof Error ? error.message : "复制失败！"));
    }
    return () => (isSupported.value ? h(ElLink, { style: { marginLeft: "3px", verticalAlign: "middle", fontSize: "12px" }, type: "primary", underline: false, icon: copied.value ? Checked : CopyDocument, title: t("glob.Copy"), onClick: (e) => (e.stopPropagation(), copyData()) }, () => renderSlot(ctx.slots, "default", {})) : null);
  },
});

const table = ref<TableInstance>();
let sortable: Sortable;
onMounted(async () => {
  const tbody = ((table.value as TableInstance).$refs["tableBody"] as HTMLTableElement).getElementsByTagName("tbody").item(0) as HTMLTableSectionElement;
  sortable = new Sortable(tbody, {
    animation: 150,
    handle: ".move-column",
    ghostClass: "table-row",
    onStart(_event: Sortable.SortableEvent) {
      state.move = true;
      const id = String(Array.from(_event.item.classList).find((v) => /^\[.*\]$/g.test(v)) || "[]").replace(/^\[(.*)\]$/g, "$1");
      if (state.expand.includes(id)) table.value && table.value.toggleRowExpansion(getTreeItemById(state.data, id, { key: "name" }), false);
    },
    async onEnd(_event: Sortable.SortableEvent) {
      (({ from, oldIndex, to, item }) => {
        from.removeChild(item);
        to.insertBefore(item, from.children[oldIndex!]);
      })(_event);

      function unTree<T extends (typeof state.data)[number]>(p: string[], c: T): string[] {
        p.push(c.name as string);
        if (c.children instanceof Array) c.children.forEach((v) => unTree(p, v));
        return p;
      }

      if (_event.oldIndex !== _event.newIndex) {
        const tableData = cloneDeep(state.data);
        const previewDataKeys = cloneDeep(state.data).reduce<string[]>(unTree, [] as string[]);
        state.data = [];
        const orders: { menuId: string; order: number }[] = [];
        const groupId = { from: "", to: "" };
        await nextTick();

        const target = await (async () => {
          const { success, parentId, target, orders: orderOutList = [] } = spliceTreeItemById<(typeof tableData)[number]>(tableData, previewDataKeys[_event.oldIndex!], { key: "name" });
          if (success) {
            groupId.from = parentId as string;
            orderOutList.forEach((order) => {
              const item = orders.find((v) => v.menuId === order.id);
              if (item) orders.splice(orders.indexOf(item), 1, { menuId: order.id as string, order: order.order });
              else orders.push({ menuId: order.id as string, order: order.order });
            });
          } else {
            await handleStateRefresh();
            ElMessage.error("菜单源位置错误！");
            throw new Error("Error");
          }
          return target;
        })();

        await (async () => {
          const { success, parentId, orders: orderIntList = [] } = appendTreeItemById<(typeof tableData)[number]>(tableData, previewDataKeys[_event.newIndex!], target!, { key: "name", append: (_event.oldIndex as number) < (_event.newIndex as number), toChildren: state.expand.includes(previewDataKeys[_event.newIndex!]) });
          if (success && parentId !== previewDataKeys[_event.oldIndex!]) {
            groupId.to = parentId as string;
            (orderIntList instanceof Array ? orderIntList : []).forEach((order) => {
              const item = orders.find((v) => v.menuId === order.id);
              if (item) orders.splice(orders.indexOf(item), 1, { menuId: order.id as string, order: order.order });
              else orders.push({ menuId: order.id as string, order: order.order });
            });
          } else {
            await handleStateRefresh();
            ElMessage.error("菜单目标位置错误！");
            throw new Error("Error");
          }
        })();

        await nextTick();
        await table.value?.$nextTick();
        state.data = tableData;

        if (groupId.from !== groupId.to) {
          try {
            const { success, message, data } = await modMenu({ name: previewDataKeys[_event.oldIndex!], parentId: groupId.to });
            if (success) {
              /*  */
            } else throw Object.assign(new Error(message), { success, message, data });
          } catch (error) {
            if (error instanceof Error) ElMessage.error(error.message);
          }
        }
        if (orders.length) {
          try {
            const { success, message, data } = await modMenuByOrder({ orders });
            if (success) {
              /*  */
            } else throw Object.assign(new Error(message), { success, message, data });
          } catch (error) {
            if (error instanceof Error) ElMessage.error(error.message);
          }
        }
        state.data.splice(0, state.data.length, ...(await querysItem({ appId: props.data.name })));
        await nextTick();
        await table.value?.$nextTick();
        state.expand.forEach((id) => table.value && table.value.toggleRowExpansion(getTreeItemById(state.data, id), true));
      }
      state.move = false;
    },
  });
});

onBeforeUnmount(() => {
  if (sortable) sortable.destroy();
});

const editorRef = ref<InstanceType<typeof EditorDetailsByMenu>>();
async function createItem(params: Partial<NavItem>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  let type = appType.DIR;
  switch (params.type || props.data.type) {
    case appType.DIR:
    case appType.MICRO:
      type = appType.MENU;
      break;
    case appType.MENU:
    case appType.ROUTE:
    case appType.LINK:
      type = appType.BUTTON;
      break;
    default:
      ElMessage.error("此类型菜单，不该有子集");
      return;
  }
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;

  try {
    await editorRef.value.open({ "parentId": params.id, "path": params.path ? `${params.path}/` : props.data.path ? `${props.data.path}/` : "", type, "name": params.name ? `${params.name}/` : props.data.path ? `${props.data.path}/` : "", "icon": "local-DocumentBill-line", "rootId": props.data.name, "#TYPE": EditorType.Add }, async (req) => {
      try {
        const { success, message, data } = await addMenu({ ...req, parentId: params.id, rootId: props.data.name, order: params.children instanceof Array ? params.children.length : state.data.length });
        if (success) {
          ElMessage.success(`添加成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
async function editorItem(params: Partial<NavItem>, onCleanup?: (cleanupFn: () => void) => void) {
  // console.log(params);
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, "rootId": props.data.name, "#TYPE": EditorType.Mod, "permissionGroups": params.permissionGroups }, async (req) => {
      // console.log(req);
      try {
        const { success, message, data } = await modMenu({ ...req, id: params.id, order: params.order });
        if (success) {
          ElMessage.success(`编辑成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
async function deleteItem(params: Partial<NavItem>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, "rootId": props.data.name, "#TYPE": EditorType.Del }, async (req) => {
      try {
        const { success, message, data } = await delMenu({ ...req });
        if (success) {
          ElMessage.success(`删除成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  if (!params.appId) return [];
  let controller = new AbortController();
  const response = getMenu({
    appId: params.appId as string,
    ...(typeof state.search.name === "string" && state.search.name ? { name: state.search.name } : {}),
    paging: {
      pageNumber: state.page,
      pageSize: state.size,
    },
    controller,
  });
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await response;
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);
      console.log(data);
      return data instanceof Array ? data : [];
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(20);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}

/*********************************************************/

interface Props {
  data: Pick<NavItem, "title" | "name" | "type" | "order" | "note" | "path">;
  width: number;
  height: number;
}
const props = withDefaults(defineProps<Props>(), {
  data: () => ({
    title: "",
    name: "",
    type: appType.BUTTON,
    order: 0,
    note: "",
    path: "",
  }),
  width: 100,
  height: 300,
});

interface StateData<T> {
  loading: boolean;
  move: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<NavItem>>({
  loading: false,
  move: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {
    keyword: "",
  },
  column: [
    /* 列 */
    // { key: "id", label: "ID", width: 220 },
    // { key: "title", label: "标题", width: 220 },
    { key: "enTitle", label: "英文标题", width: 160 },
    { key: "name", label: "路由名称" },
    {
      key: "type",
      label: "类型",
      formatter: (row, _col, v) => {
        switch (v) {
          case appType.MENU:
            return h(ElTag, { type: void 0, effect: "light" }, () => ["菜单项", h(CopyRender, { value: row.component })]);
          case appType.ROUTE:
            return h(ElTag, { type: void 0, effect: "light" }, () => ["路由", h(CopyRender, { value: row.component })]);
          case appType.DIR:
            return h(ElTag, { type: "success", effect: "light" }, () => ["目录"]);
          case appType.LINK:
            return h(ElTag, { type: "info", effect: "light" }, () => ["链接"]);
          case appType.MICRO:
            return h(ElTag, { type: void 0, effect: "light" }, () => ["微应用"]);
          case appType.BUTTON:
            return h(ElTag, { type: void 0, effect: "light" }, () => ["按钮"]);
          default:
            return h(ElTag, { type: "danger", effect: "light" }, () => ["未知"]);
        }
      },
    },
    { key: "path", label: "路径" },
    { key: "component", label: "组件" },
    { key: "note", label: "描述" },
    // { key: "createdTime", label: "创建日期", formatter: (_row, _col, v) => formatterDate(v as string) },
    // { key: "updatedTime", label: "修改日期", formatter: (_row, _col, v) => formatterDate(v as string) },
  ],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

watch<string, true>(
  () => props.data.name,
  async function () {
    if (state.loading) return;
    handleStateRefresh();
  },
  { immediate: true, flush: "post" }
);

async function handleStateCreate(params: Partial<NavItem>) {
  await createItem(params);
  await handleStateRefresh();
}
async function handleStateEditor(params: any) {
  // console.log(params);
  await editorItem(params);
  await handleStateRefresh();
}
async function handleStateDelete(params: Partial<NavItem>) {
  if (state.expand.includes(params.id!)) state.expand.splice(state.expand.indexOf(params.id!));
  await deleteItem(params);
  await handleStateRefresh();
}
async function handleStateRefresh() {
  if (state.loading) return;
  state.loading = true;
  state.data.splice(0, state.data.length, ...(await querysItem({ appId: props.data.name })));
  // // console.log(JSON.stringify(state.data));
  await nextTick();
  await table.value?.$nextTick();
  state.expand.forEach((id) => table.value && table.value.toggleRowExpansion(getTreeItemById(state.data, id), true));
  state.loading = false;
}
async function handleStateEnabled(req: Partial<NavItem>) {
  try {
    const { success, message, data } = await modMenu({ ...req });
    if (!success) throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
}
// async function exportMenuFile() {
//   // const unTree = (datas: MenuItem[]) => {
//   //   const dataList: MenuMeta[] = [];
//   //   for (let index = 0; index < datas.length; index++) {
//   //     const data = datas[index];
//   //     dataList.push(
//   //       {
//   //         id: data.id,
//   //         parentId: data.parentId,
//   //         appCode: props.data.code,
//   //         ident: data.name,
//   //         name: data.title,
//   //         order: Number(data.order),
//   //         enabled: Boolean(data.enabled),
//   //         permissions: data.permissions instanceof Array ? data.permissions : [],
//   //         config: JSON.stringify({ type: data.type, path: data.path, icon: data.icon, url: data.url, micro: data.micro, component: data.component, keepalive: data.keepalive, extend: data.extend, note: data.note }),
//   //       },
//   //       ...unTree(data.children)
//   //     );
//   //   }
//   //   return dataList;
//   // };
//   try {
//     const { success, message, data, contentDisposition } = await exportMenu({ appCode: props.data.code });
//     if (success) {
//       const fileName = contentDisposition?.fileName || `MENU_Data-${moment().format("YYYYMMDDHHmmssSSS")}.json`;
//       const file = new File([new Blob([data], { type: "application/json" })], fileName);
//       const link = document.createElement("a");
//       link.href = URL.createObjectURL(file);
//       link.style.visibility = "hidden";
//       link.download = file.name;
//       document.body.appendChild(link);
//       link.click();
//       document.body.removeChild(link);
//       URL.revokeObjectURL(link.href);
//     } else throw Object.assign(new Error(message), { success, data });
//   } catch (error) {
//     if (error instanceof Error) ElMessage.error(error.message);
//   }
// }
// async function importMenuFile(file: File) {
//   let menuDataList: MenuMeta[] = [];
//   try {
//     menuDataList = JSON.parse(await file.text()) as MenuMeta[];
//     if (!(menuDataList instanceof Array)) throw new Error("菜单格式错误");
//     for (let index = 0; index < menuDataList.length; index++) {
//       const menu = menuDataList[index];
//       let config: MenuItem = {
//         id: menu.id,
//         parentId: menu.parentId,
//         order: Number(menu.order),
//         enabled: menu.enabled,
//         name: menu.ident,
//         title: menu.name,
//         permissions: menu.permissions,
//         type: appType.dir,
//         path: "",
//         icon: "el-icon-Grid",
//         url: "",
//         component: "",
//         keepalive: false,
//         extend: "none",
//         note: "",
//         children: [],
//         createdTime: menu.createdTime as string,
//         updatedTime: menu.updatedTime as string,
//       };
//       try {
//         const { type: type = config.type, path: path = config.path, icon: icon = config.icon, url: url = config.url, component: component = config.component, keepalive: keepalive = config.keepalive, extend: extend = config.extend, note: note = config.note } = JSON.parse(menu.config);
//         Object.assign(config, { type, path, icon, url, component, keepalive, extend, note });
//       } catch (error) {
//         throw new Error("菜单配置项错误");
//       }
//       if (!config.title) throw new Error(`[ Menu Options Error ] 第${index + 1}项: 缺少菜单名称`);
//       if (!config.id) throw new Error(`[ Menu Options Error ] 第${index + 1}项 ${config.title}: 缺少菜单主键`);
//       if (!config.name) throw new Error(`[ Menu Options Error ] 第${index + 1}项 ${config.title}: 缺少菜单标识`);
//       if (isNaN(config.order)) throw new Error(`[ Menu Options Error ] 第${index + 1}项 ${config.title}: 缺少菜单排序参数`);
//       if (!config.permissions) throw new Error(`[ Menu Options Error ] 第${index + 1}项 ${config.title}: 缺少菜单权限`);
//       if (!config.path) throw new Error(`[ Menu Options Error ] 第${index + 1}项 ${config.title}: 缺少菜单路径`);
//       if (["dir", "menu", "link", "micro"].indexOf(config.type) === -1) throw new Error(`[ Menu Options Error ] 第${index + 1}项 ${config.title}: 缺少菜单类型`);
//       if (["menu"].includes(config.type) && !config.component) throw new Error(`[ Menu Options Error ] 第${index + 1}项 ${config.title}: 缺少菜单组件`);
//       if (["menu", "link", "micro"].includes(config.type) && !config.path) throw new Error(`[ Menu Options Error ] 第${index + 1}项 ${config.title}: 缺少菜单路径`);
//       if (["link", "micro"].includes(config.type) && !config.url) throw new Error(`[ Menu Options Error ] 第${index + 1}项 ${config.title}: 缺少菜单URL`);
//     }
//   } catch (error) {
//     if (error instanceof Error) ElMessage.error(error.message);
//     return;
//   }
//   try {
//     const { success, message, data } = await importMenu({ appCode: props.data.code, menu: menuDataList });
//     if (success) {
//       ElMessage.success("菜单导入成功！");
//     } else throw Object.assign(new Error(message), { success, data });
//   } catch (error) {
//     if (error instanceof Error) ElMessage.error(error.message);
//   }

//   await handleStateRefresh();
// }

defineSlots<{
  left(props: { create: typeof handleStateCreate; delete: typeof handleStateDelete; editor: typeof handleStateEditor; refresh: typeof handleStateRefresh }): any;
  center(props: { create: typeof handleStateCreate; delete: typeof handleStateDelete; editor: typeof handleStateEditor; refresh: typeof handleStateRefresh }): any;
  right(props: { create: typeof handleStateCreate; delete: typeof handleStateDelete; editor: typeof handleStateEditor; refresh: typeof handleStateRefresh }): any;
}>();
</script>

<style lang="scss" scoped>
.table {
  :deep(.el-table__body-wrapper) {
    .el-table__row {
      .move-column {
        cursor: move;
      }
    }
  }
}

.checkbox-virtually {
  .virtually {
    &,
    &::before,
    &::after {
      box-sizing: border-box;
    }
  }
  .toggle-button {
    cursor: pointer;
    display: block;
    transform-origin: 50% 50%;
    transform-style: preserve-3d;
    transition: transform 0.14s ease;
    &:active {
      transform: rotateX(30deg);
    }
    input {
      + {
        .virtually {
          border: 3px solid var(--el-color-success-light-7);
          border-radius: 50%;
          position: relative;
          width: 24px;
          height: 24px;
          svg {
            fill: none;
            stroke-width: 3.6;
            stroke-linecap: round;
            stroke-linejoin: round;
            width: 24px;
            height: 24px;
            display: block;
            position: absolute;
            left: -3px;
            top: -3px;
            right: -3px;
            bottom: -3px;
            z-index: 1;
          }
          svg {
            stroke: var(--el-color-danger-light-7);
            stroke-dashoffset: 124.6;
            stroke-dasharray: 0 162.6 133 29.6;
            transition: all 0.4s ease 0s;
          }
          &:before,
          &:after {
            content: "";
            width: 3px;
            height: 16px;
            position: absolute;
            left: 50%;
            top: 50%;
            border-radius: 5px;
          }
          &:before {
            opacity: 0;
            transform: scale(0.3) translate(-50%, -50%) rotate(45deg);
            animation: bounceInBefore 0.3s linear forwards 0.3s;
            background: var(--el-color-danger-light-3);
          }
          &:after {
            opacity: 0;
            transform: scale(0.3) translate(-50%, -50%) rotate(-45deg);
            animation: bounceInAfter 0.3s linear forwards 0.3s;
            background: var(--el-color-danger-light-3);
          }
        }
      }
      &:checked + {
        .virtually {
          svg {
            stroke: var(--el-color-success-light-3);
            stroke-dashoffset: 162.6;
            stroke-dasharray: 0 162.6 28 134.6;
            transition: all 0.4s ease 0.2s;
          }
          &:before {
            opacity: 0;
            transform: scale(0.3) translate(-50%, -50%) rotate(45deg);
            animation: bounceInBeforeDont 0.3s linear forwards 0s;
            background: var(--el-color-success-light-3);
          }
          &:after {
            opacity: 0;
            transform: scale(0.3) translate(-50%, -50%) rotate(-45deg);
            animation: bounceInAfterDont 0.3s linear forwards 0s;
            background: var(--el-color-success-light-3);
          }
        }
      }
    }
  }
  @-webkit-keyframes bounceInBefore {
    0% {
      opacity: 0;
      transform: scale(0.3) translate(-50%, -50%) rotate(45deg);
    }
    50% {
      opacity: 0.9;
      transform: scale(1.1) translate(-50%, -50%) rotate(45deg);
    }
    80% {
      opacity: 1;
      transform: scale(0.89) translate(-50%, -50%) rotate(45deg);
    }
    100% {
      opacity: 1;
      transform: scale(1) translate(-50%, -50%) rotate(45deg);
    }
  }

  @keyframes bounceInBefore {
    0% {
      opacity: 0;
      transform: scale(0.3) translate(-50%, -50%) rotate(45deg);
    }
    50% {
      opacity: 0.9;
      transform: scale(1.1) translate(-50%, -50%) rotate(45deg);
    }
    80% {
      opacity: 1;
      transform: scale(0.89) translate(-50%, -50%) rotate(45deg);
    }
    100% {
      opacity: 1;
      transform: scale(1) translate(-50%, -50%) rotate(45deg);
    }
  }
  @-webkit-keyframes bounceInAfter {
    0% {
      opacity: 0;
      transform: scale(0.3) translate(-50%, -50%) rotate(-45deg);
    }
    50% {
      opacity: 0.9;
      transform: scale(1.1) translate(-50%, -50%) rotate(-45deg);
    }
    80% {
      opacity: 1;
      transform: scale(0.89) translate(-50%, -50%) rotate(-45deg);
    }
    100% {
      opacity: 1;
      transform: scale(1) translate(-50%, -50%) rotate(-45deg);
    }
  }
  @keyframes bounceInAfter {
    0% {
      opacity: 0;
      transform: scale(0.3) translate(-50%, -50%) rotate(-45deg);
    }
    50% {
      opacity: 0.9;
      transform: scale(1.1) translate(-50%, -50%) rotate(-45deg);
    }
    80% {
      opacity: 1;
      transform: scale(0.89) translate(-50%, -50%) rotate(-45deg);
    }
    100% {
      opacity: 1;
      transform: scale(1) translate(-50%, -50%) rotate(-45deg);
    }
  }
  @-webkit-keyframes bounceInBeforeDont {
    0% {
      opacity: 1;
      transform: scale(1) translate(-50%, -50%) rotate(45deg);
    }
    100% {
      opacity: 0;
      transform: scale(0.3) translate(-50%, -50%) rotate(45deg);
    }
  }
  @keyframes bounceInBeforeDont {
    0% {
      opacity: 1;
      transform: scale(1) translate(-50%, -50%) rotate(45deg);
    }
    100% {
      opacity: 0;
      transform: scale(0.3) translate(-50%, -50%) rotate(45deg);
    }
  }
  @-webkit-keyframes bounceInAfterDont {
    0% {
      opacity: 1;
      transform: scale(1) translate(-50%, -50%) rotate(-45deg);
    }
    100% {
      opacity: 0;
      transform: scale(0.3) translate(-50%, -50%) rotate(-45deg);
    }
  }
  @keyframes bounceInAfterDont {
    0% {
      opacity: 1;
      transform: scale(1) translate(-50%, -50%) rotate(-45deg);
    }
    100% {
      opacity: 0;
      transform: scale(0.3) translate(-50%, -50%) rotate(-45deg);
    }
  }
}
</style>
