<template>
  <!-- 对话框表单 -->
  <el-dialog v-model="data.visible" :close-on-click-modal="false" draggable :width="`${width}px`" :before-close="handleCancel">
    <template #header>
      <div class="title">{{ editorType[$params["#TYPE"]] }}{{ props.title }}</div>
    </template>
    <template #default>
      <el-scrollbar ref="contentRef" :height="height">
        <el-form v-loading="data.loading" ref="formRef" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" style="overflow: hidden; padding: 12px" @submit.prevent @keyup.enter="$params['#TYPE'] === EditorType.Cat ? handleCancel() : handleFinish()">
          <el-row :gutter="16">
            <FormItem :edit="true" v-model="form.name" prop="name" :title="`${props.title}名称`" :type="InputType.text" :rules="[]"></FormItem>
            <FormItem :edit="true" :span="() => 24" v-if="props.title != '配置组' && props.title != '配置项'" v-model="form.note" prop="note" :title="`${props.title}描述`" :type="InputType.textarea" :rules="[]"></FormItem>
            <FormItem v-if="props.type == 'template'" :span="width > 600 ? 12 : 24" :title="`权限`" prop="permission" :rules="[]">
              <el-tree-select v-model="permission" :data="treeData" multiple filterable clearable auto-expand-parent node-key="id" :default-expanded-keys="swap.expanded" @node-expand="(data: TreeData) => swap.expanded.push(data.id)" :render-after-expand="false" class="tw-w-full" />
            </FormItem>
          </el-row>
        </el-form>
      </el-scrollbar>
    </template>
    <template #footer>
      <div v-if="$params['#TYPE'] === EditorType.Cat">
        <el-button type="primary" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      </div>
      <div v-else>
        <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
        <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
        <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      </div>
      <div class="zoom-handle" @mousedown.self="handleZoom">
        <svg style="display: block; width: 60%; height: 60%; transform: translate(-25%, -25%); fill: currentColor; pointer-events: none" viewBox="0 0 1024 1024">
          <path d="M319.20128 974.56128L348.16 1003.52l655.36-655.36-28.95872-28.95872-655.36 655.36zM675.84 1003.52l327.68-327.68-28.95872-28.95872-327.68 327.68L675.84 1003.52z" fill="#000000"></path>
        </svg>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable no-unused-vars, @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, provide, h } from "vue";
import type { VNode } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { buildValidatorData } from "@/utils/validate";
import { ElForm, ElMessage, ElMessageBox, FormItemRule } from "element-plus";
import { QuestionFilled } from "@element-plus/icons-vue";
import { EditorType, editorType } from "@/views/common/interface";
import { TypeHelper } from "@/utils/type";
import FormItem from "@/components/formItem/index.vue";
import { InputType } from "@/components/formItem";
import type { AuthCatalogItem as ItemData } from "@/api/application";

type Item = Omit<ItemData, "version" | "createdTime" | "updatedTime" | "order">;

interface Props {
  title: string;
  labelWidth?: number;
  type: string;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  labelWidth: 116,
  type: "",
});
const permission=ref([])
const formRef = ref<InstanceType<typeof ElForm>>();

const { t } = useI18n();

// const sizeRef = ref<HTMLDivElement>();
const width = ref(0);
const height = ref(0);

interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve: ((value: Partial<Item>) => void) | undefined;
  reject: ((value: Partial<Item>) => void) | undefined;
  callback: ((form: Partial<Item>) => Promise<boolean>) | undefined;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item> & { "#TYPE": EditorType; [key: string]: unknown }>({ "#TYPE": EditorType.Cat });

type DefaultForm<T> = { [P in keyof T]?: { value: T[P]; test: (v: any) => boolean; transfer: (fv: any, ov: T[P]) => T[P] } };
const defaultForm = readonly<DefaultForm<Item>>({
  id: { value: "", ...TypeHelper.string },
  parentId: { value: "", ...TypeHelper.string },
  appId: { value: "", ...TypeHelper.string },
  name: { value: "", ...TypeHelper.string },
  note: { value: "", ...TypeHelper.string },
  config: { value: "", ...TypeHelper.string },
  enabled: { value: true, ...TypeHelper.boolean },

});
const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  form = cloneDeep(form);
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], util.value as never)) });
    },
    form as Required<Item>
  );
}
async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => {
      if (typeof formRef.value?.validate === "function") formRef.value.validate(resolve);
      else resolve(false);
    });
  } catch (error) {
    return false;
  }
}


interface TreeData {
  label: string;
  id: string;
  children: TreeData[];
  disabled: boolean;
  isLeaf: boolean;
  parentId?: string | null;
  appId: string;
}
const swap = reactive({ expanded: <string[]>[] });
const treeData = ref<TreeData[]>([]);



/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") {
      const valid = await data.callback($form);
      if (!valid) throw new Error("Error");
    }

    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  form.value = await getForm($params.value);
  data.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

function handleZoom($event: MouseEvent) {
  const w = width.value;
  const h = height.value;
  ($event.target as HTMLElement).ownerDocument.onmousemove = (e: MouseEvent) => {
    e.preventDefault();
    if (w + (e.clientX - $event.clientX) * 2 < document.body.clientWidth - 200) width.value = w + (e.clientX - $event.clientX) * 2 > 360 ? w + (e.clientX - $event.clientX) * 2 : 360;
    else width.value = document.body.clientWidth - 200;
    if (h + (e.clientY - $event.clientY) * 1 < document.body.clientHeight - 260 - document.body.clientHeight * 0.15) height.value = h + (e.clientY - $event.clientY) * 1 > 24 ? h + (e.clientY - $event.clientY) * 1 : 24;
    else document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  };
  ($event.target as HTMLElement).ownerDocument.onmouseup = (e: MouseEvent) => {
    (e.target as HTMLElement).ownerDocument.onmousemove = null;
    (e.target as HTMLElement).ownerDocument.onmouseup = null;
  };
}

provide("#PARAMS", $params);
provide("#WIDTH", width);

defineExpose({
  close: handleCancel,
  open(params: Partial<Item> & { "#TYPE": EditorType; [key: string]: unknown }, callback?: (form: Partial<Item>) => Promise<boolean>) {
    switch (params["#TYPE"]) {
      case EditorType.Cat:
      case EditorType.Add:
      case EditorType.Mod: {
        if (data.visible) {
          return new Promise((resolve) => {
            ElMessage.warning("先关闭其他弹窗再重试！");
            resolve(params);
          });
        } else {
          $params.value = cloneDeep(params);
          data.visible = true;
          data.loading = true;
          data.submitLoading = true;
          data.callback = callback;
          return new Promise((resolve, reject) => {
            data.resolve = resolve;
            data.reject = reject;
            nextTick(async () => {
              width.value = document.body.clientWidth / 2;
              await nextTick();
              const maxHeight = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
              height.value = (((formRef.value as InstanceType<typeof ElForm>).$el as HTMLFormElement).clientHeight || 24) < maxHeight ? ((formRef.value as InstanceType<typeof ElForm>).$el as HTMLFormElement).clientHeight || 24 : maxHeight;
              await handleReset();
              data.loading = false;
              data.submitLoading = false;
            });
          });
        }
      }
      case EditorType.Del: {
        const option = reactive<{ message: string; valid: boolean; [key: string]: unknown }>({
          message: (params["#MESSAGE"] || `确认${editorType[params["#TYPE"]]}`) as string,
          valid: true,
        });
        return new Promise((resolve, reject) => {
          ElMessageBox({
            title: `${editorType[params["#TYPE"]]}${props.title}`,
            message() {
              return h("span", {}, [h("span", {}, option.message), h("span", { style: { margin: "0 3px", color: "var(--el-color-danger)" } }, params.name || "此"), option.valid ? h("span", {}, `${props.title}？`) : h("span", {}, `${props.title}删除失败！`)]);
            },
            type: "info",
            showCancelButton: true,
            showConfirmButton: true,
            cancelButtonText: t("glob.Cancel"),
            confirmButtonText: t("glob.delete"),
            distinguishCancelAndClose: true,
            draggable: true,
            async beforeClose(action, instance, done) {
              if (action === "confirm") {
                instance.confirmButtonLoading = true;
                try {
                  if (typeof callback === "function") option.valid = await callback(await getForm(params));
                  if (!option.valid) throw new Error("Error");
                  resolve(params);
                  done();
                } catch (error) {
                  option.message = "";
                  option.valid = false;
                  instance.showConfirmButton = false;
                  instance.type = "error";
                } finally {
                  instance.confirmButtonLoading = false;
                }
              } else {
                reject(params);
                done();
              }
            },
          })
            .then(async () => {})
            .catch(() => {
              reject(params);
            });
        });
      }
    }
  },
});
</script>

<style scoped lang="scss"></style>
