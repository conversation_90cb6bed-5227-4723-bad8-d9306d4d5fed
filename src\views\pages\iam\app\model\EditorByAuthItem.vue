<template>
  <!-- 对话框表单 -->
  <el-dialog v-model="data.visible" :close-on-click-modal="false" draggable :width="`${width}px`" :before-close="handleCancel">
    <template #header>
      <div class="title">{{ editorType[$params["#TYPE"]] }}{{ props.title }}</div>
    </template>
    <template #default>
      <el-scrollbar ref="contentRef" :height="height">
        <el-form v-loading="data.loading" ref="formRef" :model="form" label-position="top" :label-width="`${props.labelWidth}px`" style="overflow: hidden; padding: 12px" @submit.prevent @keyup.enter="$params['#TYPE'] === EditorType.Cat ? handleCancel() : handleFinish()">
          <el-row :gutter="16">
            <FormItem :edit="true" :span="() => 24" v-model="form.name" prop="name" title="权限名称" :type="InputType.text" :rules="[]" required="required"></FormItem>
            <FormItem :edit="true" :span="() => 24" v-model="form.itemName" prop="itemName" title="权限配置项" :type="InputType.text" :rules="[]"></FormItem>
            <!-- <FormItem :edit="true" :span="() => 24" v-model="form.code" prop="code" title="权限编码" :type="InputType.text" :rules="[]"></FormItem> -->
            <FormItem :edit="true" :span="() => 24" v-model="form.apis" prop="apis" title="接口地址" :type="InputType.array" :rules="[]"></FormItem>
            <FormItem :edit="true" :span="() => 24" v-model="form.authorities" prop="authorities" title="权限标识" :type="InputType.array" :rules="[]"></FormItem>
            <!-- <FormItem :edit="true" :span="() => 24" v-model="form.datatypes" prop="datatypes" title="数据权限标识" :type="InputType.array" :rules="[]"></FormItem> -->
            <FormItem :edit="true" :span="() => 24" v-model="form.note" prop="note" title="描述" :type="InputType.textarea" :rules="[]"></FormItem>
            <FormItem :edit="true" :span="() => 24" v-model="form.enabled" prop="enabled" title="启用" :type="InputType.switch" :rules="[]"></FormItem>
            <FormItem :edit="true" :span="() => 24" v-model="form.allInItem" prop="allInItem" title="是否拥有配置项内所有权限" :type="InputType.switch" :rules="[]"></FormItem>
            <FormItem :edit="true" :span="() => 24" v-model="form.itemSecurity" prop="itemSecurity" title="是否拥有所在配置项安全权限" :type="InputType.switch" :rules="[]"></FormItem>
            <FormItem :edit="true" :span="() => 24" v-model="form.groupSecurity" prop="groupSecurity" title="是否拥有所在配置组安全权限" :type="InputType.switch" :rules="[]"></FormItem>
            <el-col :span="24">
              <el-form-item label="权限" prop="childIds">
                <el-select v-model="form.childIds" :data="treeData" multiple filterable clearable>
                  <el-option v-for="item in treeData" :key="item.id" :value="item.id" :label="item.name"></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <!-- <FormItem :edit="true" :span="() => 24" :title="`权限`" prop="childIds">
            </FormItem> -->
          </el-row>
        </el-form>
      </el-scrollbar>
    </template>
    <template #footer>
      <div v-if="$params['#TYPE'] === EditorType.Cat">
        <el-button type="primary" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      </div>
      <div v-else>
        <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
        <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
        <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      </div>
      <div class="zoom-handle" @mousedown.self="handleZoom">
        <svg style="display: block; width: 60%; height: 60%; transform: translate(-25%, -25%); fill: currentColor; pointer-events: none" viewBox="0 0 1024 1024">
          <path d="M319.20128 974.56128L348.16 1003.52l655.36-655.36-28.95872-28.95872-655.36 655.36zM675.84 1003.52l327.68-327.68-28.95872-28.95872-327.68 327.68L675.84 1003.52z" fill="#000000"></path>
        </svg>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable no-unused-vars, @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, provide, h } from "vue";
import type { VNode } from "vue";
import { useI18n } from "vue-i18n";

import { cloneDeep, find } from "lodash-es";
import { buildValidatorData } from "@/utils/validate";
import { ElForm, ElMessage, ElMessageBox, FormItemRule } from "element-plus";
import { QuestionFilled } from "@element-plus/icons-vue";
import { EditorType, editorType } from "@/views/common/interface";
import { TypeHelper } from "@/utils/type";
import FormItem from "@/components/formItem/index.vue";
import { InputType } from "@/components/formItem";
import type { AuthorityItem as ItemData } from "@/api/application";
import { getAuthCatalog, getAppAuth } from "@/api/application";

type Item = Omit<ItemData, "createdTime" | "updatedTime" | "type" | "children" | "version" | "orderNum">;

interface Props {
  title: string;
  labelWidth?: number;
  type: string;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  labelWidth: 116,
  type: "",
});

const formRef = ref<InstanceType<typeof ElForm>>();

const { t } = useI18n();

// const sizeRef = ref<HTMLDivElement>();
const width = ref(0);
const height = ref(0);

interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve: ((value: Partial<Item>) => void) | undefined;
  reject: ((value: Partial<Item>) => void) | undefined;
  callback: ((form: Partial<Item>) => Promise<boolean>) | undefined;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item> & { "#TYPE": EditorType; [key: string]: unknown }>({ "#TYPE": EditorType.Cat });

const swap = reactive({ expanded: <string[]>[] });
const treeData = ref([]);

type DefaultForm<T> = { [P in keyof T]: { value: T[P]; test: (v: any) => boolean; transfer: (fv: any, ov: T[P]) => T[P] } };
const defaultForm = readonly<DefaultForm<Item>>({
  id: { value: "", ...TypeHelper.string } /* 主键 */,
  appId: { value: "", ...TypeHelper.string } /* 所属应用ID */,
  catalogId: { value: "", ...TypeHelper.string } /* 所属目录ID */,
  name: { value: "", ...TypeHelper.string } /* 名称 */,
  note: { value: "", ...TypeHelper.string } /* 备注 */,
  apis: { value: [], ...TypeHelper.array } /* 关联API接口列表 */,
  authorities: { value: [], ...TypeHelper.array } /* 权限标识列表 */,
  enabled: { value: true, ...TypeHelper.boolean } /* 是否启用 */,
  itemId: { value: "", ...TypeHelper.string },
  itemName: { value: "", ...TypeHelper.string },
  code: { value: "", ...TypeHelper.string },
  datatypes: { value: [], ...TypeHelper.array },
  allInItem: { value: false, ...TypeHelper.boolean },
  itemSecurity: { value: false, ...TypeHelper.boolean },
  groupSecurity: { value: false, ...TypeHelper.boolean },
  childIds: { value: [], ...TypeHelper.array },
  // type: { value: "ITEEM", ...TypeHelper.string },
});
const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  form = cloneDeep(form);
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], util.value as never)) });
    },
    form as Required<Item>
  );
}
async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => {
      if (typeof formRef.value?.validate === "function") formRef.value.validate(resolve);
      else resolve(false);
    });
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") {
      const valid = await data.callback($form);
      if (!valid) throw new Error("Error");
    }

    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  form.value = await getForm($params.value);
  try {
    const [{ success: catalogSuccess, message: catalogMessage, data: catalogData }, { success: appSuccess, message: appMessage, data: appData }] = await Promise.all([getAuthCatalog({ appId: form.value.appId }), getAppAuth({ appId: form.value.appId })]);
    if (!catalogSuccess) throw Object.assign(new Error(catalogMessage), { success: catalogSuccess, data: catalogData });
    if (!appSuccess) throw Object.assign(new Error(appMessage), { success: appSuccess, data: appData });

    // console.log(form.value.itemId,appData)
    let arr = [];
    appData.map((v, i) => {
      if (v.itemId == form.value.itemId) {
        arr.push(v);
      }
    });
    treeData.value = arr || [];
    // console.log(arr)

    // const $swap = appData.reduce((p, c) => {
    //   const v = { id: c.id, label: c.name, parentId: c.catalogId, isLeaf: true, disabled: false, children: <TreeData[]>[], appId: form.value.appId || "" };
    //   if (p.has(c.catalogId)) (p.get(c.catalogId) as TreeData[]).push(v);
    //   else p.set(c.catalogId, [v]);
    //   return p;
    // }, new Map<string | null | undefined, TreeData[]>());
    // const catalog: TreeData[] = (catalogData instanceof Array ? catalogData : []).map((v) => ({ id: v.id, label: v.name, parentId: v.parentId, isLeaf: false, disabled: false, children: <TreeData[]>[], appId: form.value.appId || "" }));

    // while (catalog.filter((v) => Array.from($swap.keys()).includes(v.id)).length) {
    //   const itemKeysleIterator = $swap.keys();
    //   let key: IteratorResult<string | null | undefined>;
    //   while (!(key = itemKeysleIterator.next()).done) {
    //     const childrenItems = $swap.get(key.value);
    //     if (!childrenItems) continue;
    //     const $findCatalog = find(catalog, (v) => v.id === key.value);
    //     if ($findCatalog) {
    //       // $swap.delete(key.value);
    //       catalog.splice(catalog.indexOf($findCatalog), 1);
    //       $findCatalog.children = childrenItems;
    //       if ($swap.has($findCatalog.parentId)) {
    //         $swap.get($findCatalog.parentId)!.push($findCatalog);
    //       } else {
    //         const $parent = find(catalog, (v) => v.id === $findCatalog.parentId);
    //         if ($parent) {
    //           $parent.children.push($findCatalog);
    //           $swap.set($findCatalog.parentId, $parent.children);
    //         } else {
    //           $swap.set($findCatalog.parentId, [$findCatalog]);
    //         }
    //       }
    //     }
    //   }
    // }
    // const _getName = (data: TreeData[]): string[] => data.map((v) => `${v.label}(${v.id})[${v.isLeaf ? "X" : _getName(v.children).join("|")}]`);
    // // console.log(
    //   JSON.stringify(
    //     [...$swap].map(([k, v]) => ({ [String(k)]: v.map((v) => `${v.label}(${v.id})[${v.isLeaf ? "X" : _getName(v.children).join("|")}]`) })),
    //     null,
    //     2
    //   )
    // );

    // console.log(treeData.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
  data.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

function handleZoom($event: MouseEvent) {
  const w = width.value;
  const h = height.value;
  ($event.target as HTMLElement).ownerDocument.onmousemove = (e: MouseEvent) => {
    e.preventDefault();
    if (w + (e.clientX - $event.clientX) * 2 < document.body.clientWidth - 200) width.value = w + (e.clientX - $event.clientX) * 2 > 360 ? w + (e.clientX - $event.clientX) * 2 : 360;
    else width.value = document.body.clientWidth - 200;
    if (h + (e.clientY - $event.clientY) * 1 < document.body.clientHeight - 260 - document.body.clientHeight * 0.15) height.value = h + (e.clientY - $event.clientY) * 1 > 24 ? h + (e.clientY - $event.clientY) * 1 : 24;
    else document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  };
  ($event.target as HTMLElement).ownerDocument.onmouseup = (e: MouseEvent) => {
    (e.target as HTMLElement).ownerDocument.onmousemove = null;
    (e.target as HTMLElement).ownerDocument.onmouseup = null;
  };
}

provide("#PARAMS", $params);
provide("#WIDTH", width);

defineExpose({
  close: handleCancel,
  open(params: Partial<Item> & { "#TYPE": EditorType; [key: string]: unknown }, callback?: (form: Partial<Item>) => Promise<boolean>) {
    switch (params["#TYPE"]) {
      case EditorType.Cat:
      case EditorType.Add:
      case EditorType.Mod: {
        if (data.visible) {
          return new Promise((resolve) => {
            ElMessage.warning("先关闭其他弹窗再重试！");
            resolve(params);
          });
        } else {
          $params.value = cloneDeep(params);

          // form.value = getForm($params.value);

          data.visible = true;
          data.loading = true;
          data.submitLoading = true;
          data.callback = callback;
          return new Promise((resolve, reject) => {
            data.resolve = resolve;
            data.reject = reject;
            nextTick(async () => {
              width.value = document.body.clientWidth / 2;
              await nextTick();
              const maxHeight = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
              height.value = (((formRef.value as InstanceType<typeof ElForm>).$el as HTMLFormElement).clientHeight || 24) < maxHeight ? ((formRef.value as InstanceType<typeof ElForm>).$el as HTMLFormElement).clientHeight || 24 : maxHeight;
              await handleReset();
              data.loading = false;
              data.submitLoading = false;
            });
          });
        }
      }
      case EditorType.Del: {
        const option = reactive<{ message: string; valid: boolean; [key: string]: unknown }>({
          message: (params["#MESSAGE"] || `确认${editorType[params["#TYPE"]]}`) as string,
          valid: true,
        });
        return new Promise((resolve, reject) => {
          ElMessageBox({
            title: `${editorType[params["#TYPE"]]}${props.title}`,
            message() {
              return h("span", {}, [h("span", {}, option.message), h("span", { style: { margin: "0 3px", color: "var(--el-color-danger)" } }, params.name || "此"), option.valid ? h("span", {}, `${props.title}？`) : h("span", {}, `${props.title}删除失败！`)]);
            },
            type: "info",
            showCancelButton: true,
            showConfirmButton: true,
            cancelButtonText: t("glob.Cancel"),
            confirmButtonText: t("glob.delete"),
            distinguishCancelAndClose: true,
            draggable: true,
            async beforeClose(action, instance, done) {
              if (action === "confirm") {
                instance.confirmButtonLoading = true;
                try {
                  if (typeof callback === "function") option.valid = await callback(await getForm(params));
                  if (!option.valid) throw new Error("Error");
                  resolve(params);
                  done();
                } catch (error) {
                  option.message = "";
                  option.valid = false;
                  instance.showConfirmButton = false;
                  instance.type = "error";
                } finally {
                  instance.confirmButtonLoading = false;
                }
              } else {
                reject(params);
                done();
              }
            },
          })
            .then(async () => {})
            .catch(() => {
              reject(params);
            });
        });
      }
    }
  },
});
</script>

<style scoped lang="scss"></style>
