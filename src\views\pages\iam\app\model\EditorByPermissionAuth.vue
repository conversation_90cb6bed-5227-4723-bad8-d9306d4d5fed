<template>
  <el-dialog v-model="visible" :close-on-click-modal="false" append-to-body draggable :width="`${$width}px`" :before-close="handleCancel">
    <template #header>
      <div class="leading-[18px]">{{ props.title }}</div>
    </template>
    <template #default>
      <el-scrollbar ref="contentRef" :max-height="$height" :view-style="{ padding: '0 6px' }">
        <el-form ref="formRef" :model="form" label-position="top" :label-width="80" require-asterisk-position="right" :status-icon="true" @keyup.ctrl.exact.enter.prevent.stop="handleSubmit()" @keydown.meta.exact.enter.prevent.stop="handleSubmit()" @submit.prevent="handleSubmit()">
          <el-row :gutter="12">
            <el-col :span="$width > 260 ? 12 : 24">
              <el-form-item label="名称" prop="name" :rules="[{ required: true, message: '请输入', trigger: 'bulr' }]">
                <el-input v-model="form.name" :placeholder="$t('glob.Please input field', { field: '名称' })"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="$width > 260 ? 12 : 24">
              <el-form-item label="标识" prop="ident" :rules="[{ required: false, message: '请输入', trigger: 'bulr' }]">
                <el-input v-model="form.ident" :placeholder="$t('glob.Please input field', { field: '标识' })" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="AllowApi" prop="apis" :rules="[{ required: false, type: 'array', message: '请输入', trigger: 'bulr' }]">
                <el-input :model-value="(form.apis instanceof Array ? form.apis : []).map((v) => String(['string', 'number'].includes(typeof v) ? v : '')).join('\n')" type="textarea" :placeholder="$t('glob.Please input field', { field: 'AllowApi' }) + '，' + $t('glob.Division Rule')" @update:model-value="form.apis = $event.split('\n')" @change="form.apis = (form.apis instanceof Array ? form.apis : []).filter((v) => v)"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="权限标识" prop="authorities" :rules="[{ required: false, type: 'array', message: '请输入', trigger: 'bulr' }]">
                <el-input :model-value="(form.authorities instanceof Array ? form.authorities : []).map((v) => String(['string', 'number'].includes(typeof v) ? v : '')).join('\n')" type="textarea" :placeholder="$t('glob.Please input field', { field: '权限标识' }) + '，' + $t('glob.Division Rule')" @update:model-value="form.authorities = $event.split('\n')" @change="form.authorities = (form.authorities instanceof Array ? form.authorities : []).filter((v) => v)"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="$width > 260 ? 12 : 24">
              <el-form-item label="名称" prop="type" :rules="[{ required: false, message: '请输入', trigger: 'bulr' }]">
                <el-radio-group v-model="form.type">
                  <el-radio-button label="GROUP">权限组</el-radio-button>
                  <el-radio-button label="ITEM">权限项</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="$width > 260 ? 12 : 24">
              <el-form-item label="启用" prop="enabled" :rules="[{ required: false, message: '请选择', trigger: 'bulr' }]">
                <el-switch v-model="form.enabled" :active-value="true" :active-text="$t('glob.Enable')" :inactive-value="false" :inactive-text="$t('glob.Disable')"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="$width > 260 ? 12 : 24">
              <el-form-item label="配置项内所有权限" prop="allInItem" :rules="[{ required: false, message: '请选择', trigger: 'bulr' }]">
                <el-switch v-model="form.allInItem" :active-value="true" :active-text="'所有权限'" :inactive-value="false" :inactive-text="'不包含'" @change="($event) => $event && (form.childIds = [])"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="$width > 260 ? 12 : 24">
              <el-form-item label="配置项内包含权限" prop="childIds" :rules="[{ required: false, type: 'array', message: '请选择', trigger: 'bulr' }]">
                <el-select v-model="$childIds" :disabled="form.allInItem" placeholder="请选择此权限包含的权限" multiple collapse-tags clearable filterable style="width: 100%">
                  <el-option v-for="item in authList.filter((v) => v.id !== form.id && v.parentId === form.itemId)" :disabled="item.disabled" :key="item.id" :label="item.label" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="$width > 260 ? 12 : 24">
              <el-form-item label="配置项安全" prop="itemSecurity" :rules="[{ required: false, message: '请选择', trigger: 'bulr' }]">
                <el-switch v-model="form.itemSecurity" :active-value="true" :active-text="'配置项拥有安全'" :inactive-value="false" :inactive-text="'无'" @change="() => (form.groupSecurity = false)"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="$width > 260 ? 12 : 24">
              <el-form-item label="配置组安全" prop="groupSecurity" :rules="[{ required: false, message: '请选择', trigger: 'bulr' }]">
                <el-switch v-model="form.groupSecurity" :active-value="true" :active-text="'配置组拥有安全'" :inactive-value="false" :inactive-text="'无'" @change="($event) => (form.itemSecurity = $event as boolean)"></el-switch>
              </el-form-item>
            </el-col>

            <el-col :span="$width > 260 ? 12 : 24">
              <el-form-item label="权限级别" prop="tenantLevel" :rules="[{ required: false, message: '请选择', trigger: 'bulr' }]">
                <el-switch v-model="form.tenantLevel" :active-value="true" :active-text="'租户级'" :inactive-value="false" :inactive-text="'全局'"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-scrollbar>
    </template>
    <template #footer>
      <div :style="{ padding: '0 10px 10px' }">
        <!-- <el-button type="warning" :loading="loading" @click="handleResets()">{{ $t("Resets") }}</el-button> -->
        <el-button type="default" :loading="loading" @click="handleCancel()">取消</el-button>
        <!-- <el-button type="primary" :loading="loading" @click="handleFinish()">{{ $t("Finish") }}</el-button> -->
        <el-button type="primary" :loading="loading" @click="handleSubmit()">确定</el-button>
      </div>
      <div class="i-mdi-resize-bottom-right absolute bottom-0 right-0 h-20px w-[20px] cursor-se-resize" @mousedown.self="handleZoom"></div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, shallowReadonly, toValue, toRaw, nextTick, computed } from "vue";
import { cloneDeep } from "lodash-es";
import { ElMessage } from "element-plus";
import { addPermissionAuth as addItem, modPermissionAuth as modItem /* , delPermissionAuth as delItem */ } from "@/api/permission";
import { getPermissionAuth } from "@/api/permission";

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
type RequestBase = "controller" | "keyword" | "paging" | "slot";
type CreateItem = Omit<Required<typeof addItem extends (req: infer P) => any ? P : never>, RequestBase>;
type ModifyItem = Omit<Required<typeof modItem extends (req: infer P) => any ? P : never>, RequestBase>;
// type DeleteItem = Omit<Required<typeof delItem extends (req: infer P) => any ? P : never>, RequestBase>;
interface EditorItem extends CreateItem, ModifyItem {
  id: string;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const defaultForm = shallowReadonly<{ [Key in keyof EditorItem]: DefaultFormData<EditorItem[Key]> }>({
  /**
   * TODO: 此为表单初始默认数据和校验方法
   * 使用`buildDefaultType`方法构建默认值
   */
  id: buildDefaultType<EditorItem["id"]>(""),
  appId: buildDefaultType<EditorItem["appId"]>(""),
  catalogId: buildDefaultType<EditorItem["catalogId"]>(""),
  itemId: buildDefaultType<EditorItem["itemId"]>(""),

  type: buildDefaultType<EditorItem["type"]>("ITEM"),
  name: buildDefaultType<EditorItem["name"]>(""),
  ident: buildDefaultType<EditorItem["ident"]>(""),
  apis: buildDefaultType<EditorItem["apis"]>([]),
  authorities: buildDefaultType<EditorItem["authorities"]>([]),
  orderNum: buildDefaultType<EditorItem["orderNum"]>(0),
  enabled: buildDefaultType<EditorItem["enabled"]>(true),
  allInItem: buildDefaultType<EditorItem["allInItem"]>(false),
  itemSecurity: buildDefaultType<EditorItem["itemSecurity"]>(false),
  groupSecurity: buildDefaultType<EditorItem["groupSecurity"]>(false),
  tenantLevel: buildDefaultType<EditorItem["tenantLevel"]>(false),
  childIds: buildDefaultType<EditorItem["childIds"]>([]),
});

const form = ref<EditorItem>(Object.entries(defaultForm).reduce<Partial<EditorItem>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {} as Partial<EditorItem>) as EditorItem);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
const $childIds = computed({
  get: () => toValue(form).childIds,
  set: ($auths: string[]) => {
    const $list = toValue(authList)
      .filter((v) => !v.disabled && v.parentId === toValue(form).itemId && v.id !== toValue(form).id)
      .reduce((p, c, _i, a) => {
        const $key = c.id;
        const $bind: string[] = c.children.map((v) => v);
        const $hand: string[] = a.reduce<string[]>((p, c) => (c.id === $key ? p : c.children.includes($key) ? p.concat(c.id) : p), []);
        p.set($key, { bind: $bind, hand: $hand });
        return p;
      }, new Map<string, { bind: string[]; hand: string[] }>());
    const $addValue: string[] = ($auths instanceof Array ? $auths : []).map((v) => v);
    const $delValue: string[] = toValue(form).childIds.map((v) => v);
    for (let i = $addValue.length - 1; i >= 0; i--) {
      const $delInfex = $delValue.indexOf($addValue[i]);
      if ($delInfex !== -1) $delValue.splice($delInfex, 1);
    }
    const $result = new Set<string>();
    for (let i = 0; i < $addValue.length; i++) {
      const $affect = $list.get($addValue[i]);
      if (!$affect) $result.delete($addValue[i]);
      else {
        $result.add($addValue[i]);
        $affect.bind.forEach((v: string) => $result.add(v));
        $affect.hand.forEach((v: string) => $result.delete(v));
      }
    }
    for (let i = 0; i < $delValue.length; i++) {
      const $affect = $list.get($delValue[i]);
      if (!$affect) $result.delete($delValue[i]);
      else {
        $result.delete($delValue[i]);
        $affect.bind.forEach((v: string) => $result.delete(v));
        $affect.hand.forEach((v: string) => $result.delete(v));
      }
    }
    form.value.childIds = Array.from($result.values());
  },
});
interface AuthItemData {
  label: string;
  id: string;
  children: string[];
  disabled: boolean;
  parentId?: string | null;
  appId: string;
}
const authList = ref<AuthItemData[]>([]);
/**
 * TODO: 首次打开初始化时执行
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(): Promise<void> {}
/**
 * TODO: 每次重置表单时调用
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(): Promise<void> {
  const _form = toValue(form);
  try {
    const [
      /*  */
      { success: authSuccess, message: authMessage, data: authData },
    ] = await Promise.all([getPermissionAuth({ appId: _form.appId })]);
    if (!authSuccess) throw Object.assign(new Error(authMessage), { success: authSuccess, data: authData });
    authList.value = authData.map((v) => ({ id: v.id, label: v.name, parentId: v.itemId, disabled: !v.enabled, children: v.allInItem ? authData.filter(($v) => $v.enabled && $v.itemId === v.itemId).map((v) => v.id) : v.childIds instanceof Array ? v.childIds : [], appId: v.appId }));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
}

/**
 * TODO: 此处使用可对生成的数据操作
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: EditorItem): Promise<EditorItem> {
  return form;
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

const params = ref<Record<string, unknown>>({});
defineOptions({ name: "IndexEditor", inheritAttrs: false });
interface Props {
  title?: string;
  labelWidth?: number;
  width: number;
  height: number;
}
const props = withDefaults(defineProps<Props>(), { title: "", labelWidth: 80 });
const visible = ref(false);
const loading = ref(false);
const $width = ref(props.width / 2);
const $height = ref(props.height);
const handle = reactive({
  callback: undefined as ((form: EditorItem) => Promise<void>) | undefined,
  resolve: (form: EditorItem) => void form,
  reject: (err: Error) => void err,
});

async function getForm(form: Partial<Record<keyof EditorItem, unknown>>): Promise<EditorItem> {
  const $form: Partial<EditorItem> = {};
  await nextTick();
  for (const key of Reflect.ownKeys(defaultForm)) {
    const structure = Reflect.get(defaultForm, key);
    const $value = Reflect.get(form, key);
    if (!structure) continue;
    Reflect.set($form, key, cloneDeep(structure.test($value) ? $value : structure.transfer(Reflect.get($form, key), toRaw(structure.value))));
  }
  return await transformForm($form as Required<EditorItem>);
}

const formRef = ref<InstanceType<typeof import("element-plus").ElForm>>();
async function handleFinish() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  if (!(await new Promise((resolve) => $formRef.validate(resolve)))) return;
  try {
    loading.value = true;
    await nextTick();
    const $form = await getForm(toValue(form));
    if (handle.callback) {
      try {
        await handle.callback($form);
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return;
      }
    }
    close("fulfilled");
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}
async function handleSubmit() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  if (!(await new Promise((resolve) => $formRef.validate(resolve)))) return;
  try {
    loading.value = true;
    await nextTick();
    const $form = await getForm(toValue(form));
    if (handle.callback) {
      try {
        await handle.callback($form);
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return;
      }
    }
    close("fulfilled");
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}
async function handleCancel() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  try {
    loading.value = true;
    await nextTick();
    close("rejected");
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}
async function handleResets() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  try {
    loading.value = true;
    await nextTick();
    form.value = await getForm(toValue(params));
    await resetFormInit();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}
async function handleOpener() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  try {
    loading.value = true;
    await nextTick();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}

function close(status: "fulfilled" | "rejected") {
  switch (status) {
    case "fulfilled":
      handle.resolve(toValue(form));
      break;
    case "rejected":
      handle.reject(Object.assign(new Error("Cancel"), toValue(form)));
      break;
    default:
      handle.reject(Object.assign(new Error("Error"), toValue(form)));
      break;
  }
  params.value = {};
  visible.value = false;
  nextTick(() => {
    loading.value = false;
    window.setTimeout(() => {
      handle.resolve = (form: EditorItem) => void form;
      handle.reject = (err: Error) => void err;
      handle.callback = undefined;
    });
  });
}

async function operate($handle: () => Promise<void>, $params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>): Promise<EditorItem> {
  if (toValue(loading)) return toValue(form);
  $width.value = props.width / 2 > 360 ? props.width / 2 : 360;
  $height.value = props.height;
  params.value = $params;
  loading.value = true;
  visible.value = true;
  const result = new Promise<EditorItem>((resolve, reject) => Object.assign(handle, { resolve, reject, callback }));
  await nextTick();
  try {
    await runningInit();
    loading.value = false;
    await handleResets();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    loading.value = false;
    await handleCancel();
  }
  await $handle();
  try {
    return result;
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : "Error");
  }
}

const contentRef = ref<InstanceType<typeof import("element-plus").ElScrollbar>>();
function handleZoom($event: MouseEvent) {
  const { wrapRef } = toValue(contentRef) || {};
  if (wrapRef) $height.value = wrapRef.offsetHeight;
  const w = toValue($width);
  const h = toValue($height);

  const max_w = props.width;
  const min_w = 360;

  const max_h = props.height;
  const min_h = 62;

  const controller = new AbortController();
  window.document.addEventListener(
    "mousemove",
    (e) => {
      e.preventDefault();
      nextTick(() => {
        const _w = w + (e.clientX - $event.clientX) * 2;
        $width.value = _w < max_w ? (_w > min_w ? _w : min_w) : max_w;
        const _h = h + (e.clientY - $event.clientY) * 1;
        $height.value = _h < max_h ? (_h > min_h ? _h : min_h) : max_h;
      });
    },
    { signal: controller.signal }
  );
  window.document.addEventListener(
    "mouseup",
    () => {
      controller.abort();
    },
    { once: true, signal: controller.signal }
  );
}

defineExpose({
  async finish($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleFinish, $params, callback);
  },
  async submit($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleSubmit, $params, callback);
  },
  async cancel($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleCancel, $params, callback);
  },
  async resets($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleResets, $params, callback);
  },
  async opener($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleOpener, $params, callback);
  },
});

/*  */
interface ConstructorFunc<T> {
  new (value: unknown): T;
  (value: unknown): T;
}

interface DefaultFormData<T> {
  value: T;
  test: (v: unknown) => v is T;
  transfer: (fv: unknown, ov: T) => T;
  type: string;
}

function buildDefaultType<T>(value: T, pattern?: RegExp): DefaultFormData<T> {
  const objectConstructor: string = Object.prototype.toString.call(value);
  const ConstructorFunction = new Object(value).constructor as ConstructorFunc<T>;
  const type = (/^\[object\s(?<type>[a-zA-Z0-9]*)\]$/g.exec(objectConstructor)?.groups?.type as string).toLowerCase();

  switch (objectConstructor) {
    case "[object Undefined]":
    case "[object Null]": {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? (pattern instanceof RegExp ? pattern.test(String(v)) : true) : false);
      const transfer = (fv: unknown, ov: T): T => (test(fv) ? fv : ov);
      return { value, test, transfer, type };
    }
    case "[object Boolean]":
    case "[object Number]":
    case "[object String]": {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? (pattern instanceof RegExp ? pattern.test(String(v)) : true) : false);
      const transfer = (fv: unknown, ov: T): T => {
        if (test(fv)) return fv;
        if (pattern instanceof RegExp || fv === undefined || fv === null) return ov;
        try {
          return ConstructorFunction(fv);
        } catch (error) {
          return ov;
        }
      };
      return { value, test, transfer, type };
    }
    case "[object Object]": {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? true : false);
      const transfer = (fv: unknown, ov: T): T => {
        if (test(fv)) return fv;
        else return ov;
      };
      return { value, test, transfer, type };
    }
    case "[object Array]": {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? true : false);
      const transfer = (fv: unknown, ov: T): T => {
        if (test(fv)) return fv;
        try {
          return Array.from(fv as Iterable<T> | ArrayLike<T>) as unknown as T;
        } catch (error) {
          return ov;
        }
      };
      return { value, test, transfer, type };
    }
    default: {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? true : false);
      const transfer = (fv: unknown, ov: T): T => (test(fv) ? fv : ov);
      return { value, test, transfer, type };
    }
  }
}
</script>

<style scoped lang="scss">
.size-full {
  width: 100%;
  height: 100%;
}
</style>
