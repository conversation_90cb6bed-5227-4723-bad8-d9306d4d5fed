<template>
  <el-dialog v-model="visible" :close-on-click-modal="false" append-to-body draggable :width="`${$width}px`" :before-close="handleCancel">
    <template #header>
      <div class="leading-[18px]">{{ props.title }}</div>
    </template>
    <template #default>
      <el-scrollbar ref="contentRef" :max-height="$height" :view-style="{ padding: '0 6px' }">
        <el-form ref="formRef" :model="form" label-position="left" :label-width="80" require-asterisk-position="right" :status-icon="true" @keyup.ctrl.exact.enter.prevent.stop="handleSubmit()" @keydown.meta.exact.enter.prevent.stop="handleSubmit()" @submit.prevent="handleSubmit()">
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="名称" prop="name" :rules="[{ required: true, message: '请输入名称', trigger: 'bulr' }]" @change="handleResets">
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-scrollbar>
    </template>
    <template #footer>
      <div :style="{ padding: '0 10px 10px' }">
        <!-- <el-button type="warning" :loading="loading" @click="handleResets()">{{ $t("Resets") }}</el-button> -->
        <el-button type="default" :loading="loading" @click="handleCancel()">取消</el-button>
        <!-- <el-button type="primary" :loading="loading" @click="handleFinish()">{{ $t("Finish") }}</el-button> -->
        <el-button type="primary" :loading="loading" @click="handleSubmit()">确定</el-button>
      </div>
      <div class="i-mdi-resize-bottom-right absolute bottom-0 right-0 h-20px w-[20px] cursor-se-resize" @mousedown.self="handleZoom"></div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, shallowReadonly, toValue, toRaw, nextTick, computed } from "vue";
import { cloneDeep } from "lodash-es";
import { ElMessage } from "element-plus";
import { addPermissionCatalog as addItem, modPermissionCatalog as modItem /* , delPermissionCatalog as delItem */ } from "@/api/permission";
/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
type RequestBase = "controller" | "keyword" | "paging" | "slot";
type CreateItem = Omit<Required<typeof addItem extends (req: infer P) => any ? P : never>, RequestBase>;
type ModifyItem = Omit<Required<typeof modItem extends (req: infer P) => any ? P : never>, RequestBase>;
// type DeleteItem = Omit<Required<typeof delItem extends (req: infer P) => any ? P : never>, RequestBase>;
interface EditorItem extends CreateItem, ModifyItem {
  id: string;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const defaultForm = shallowReadonly<{ [Key in keyof EditorItem]: DefaultFormData<EditorItem[Key]> }>({
  /**
   * TODO: 此为表单初始默认数据和校验方法
   * 使用`buildDefaultType`方法构建默认值
   */
  id: buildDefaultType<EditorItem["id"]>(""),
  parentId: buildDefaultType<EditorItem["parentId"]>(""),
  appId: buildDefaultType<EditorItem["appId"]>(""),
  name: buildDefaultType<EditorItem["name"]>(""),
  orderNum: buildDefaultType<EditorItem["orderNum"]>(0),
  enabled: buildDefaultType<EditorItem["enabled"]>(true),
  config: buildDefaultType<EditorItem["config"]>(""),
});

const form = ref<EditorItem>(Object.entries(defaultForm).reduce<Partial<EditorItem>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {} as Partial<EditorItem>) as EditorItem);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(): Promise<void> {
  // const $params = toValue(params);
  // await Promise.all([
  //   (async () => {
  //     try {
  //       templatePermission.value = [];
  //       loadingTemplatePermission.value = true;
  //       const { success, message, data } = await getPermissionTemplate({ appId: (siteConfig.baseInfo || {}).app || "", groupId: $params.permissionGroupId as string });
  //       if (!success) throw Object.assign(new Error(message), { success, data });
  //       templatePermission.value = data instanceof Array ? data : [];
  //     } catch (error) {
  //       if (error instanceof Error) ElMessage.error(error.message);
  //     } finally {
  //       loadingTemplatePermission.value = false;
  //     }
  //   })(),
  //   (async () => {
  //     try {
  //       permissionItems.value = [];
  //       loadingPermissionItems.value = true;
  //       const { success, message, data } = await getPermissionUsable({ appId: (siteConfig.baseInfo || {}).app || "", containerId: $params.containerId as string, permissionGroupId: $params.permissionGroupId as string });
  //       if (!success) throw Object.assign(new Error(message), { success, data });
  //       permissionItems.value = data instanceof Array ? data : [];
  //     } catch (error) {
  //       if (error instanceof Error) ElMessage.error(error.message);
  //     } finally {
  //       loadingPermissionItems.value = false;
  //     }
  //   })(),
  //   (async () => {
  //     try {
  //       appAuth.value = [];
  //       loadingAppAuth.value = true;
  //       const { success, message, data } = await getAppAuth({ appId: (siteConfig.baseInfo || {}).app || "" });
  //       if (!success) throw Object.assign(new Error(message), { success, data });
  //       appAuth.value = data instanceof Array ? data : [];
  //     } catch (error) {
  //       if (error instanceof Error) ElMessage.error(error.message);
  //     } finally {
  //       loadingAppAuth.value = false;
  //     }
  //   })(),
  // ]);
}
/**
 * TODO: 每次重置表单时调用
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(): Promise<void> {}

/**
 * TODO: 此处使用可对生成的数据操作
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: EditorItem): Promise<EditorItem> {
  return form;
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

const params = ref<Record<string, unknown>>({});
defineOptions({ name: "IndexEditor", inheritAttrs: false });
interface Props {
  title?: string;
  labelWidth?: number;
  width: number;
  height: number;
}
const props = withDefaults(defineProps<Props>(), { title: "", labelWidth: 80 });
const visible = ref(false);
const loading = ref(false);
const $width = ref(props.width / 2);
const $height = ref(props.height);
const handle = reactive({
  callback: undefined as ((form: EditorItem) => Promise<void>) | undefined,
  resolve: (form: EditorItem) => void form,
  reject: (err: Error) => void err,
});

async function getForm(form: Partial<Record<keyof EditorItem, unknown>>): Promise<EditorItem> {
  const $form: Partial<EditorItem> = {};
  await nextTick();
  for (const key of Reflect.ownKeys(defaultForm)) {
    const structure = Reflect.get(defaultForm, key);
    const $value = Reflect.get(form, key);
    if (!structure) continue;
    Reflect.set($form, key, cloneDeep(structure.test($value) ? $value : structure.transfer(Reflect.get($form, key), toRaw(structure.value))));
  }
  return await transformForm($form as Required<EditorItem>);
}

const formRef = ref<InstanceType<typeof import("element-plus").ElForm>>();
async function handleFinish() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  if (!(await new Promise((resolve) => $formRef.validate(resolve)))) return;
  try {
    loading.value = true;
    await nextTick();
    const $form = await getForm(toValue(form));
    if (handle.callback) {
      try {
        await handle.callback($form);
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return;
      }
    }
    close("fulfilled");
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}
async function handleSubmit() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  if (!(await new Promise((resolve) => $formRef.validate(resolve)))) return;
  try {
    loading.value = true;
    await nextTick();
    const $form = await getForm(toValue(form));
    if (handle.callback) {
      try {
        await handle.callback($form);
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return;
      }
    }
    close("fulfilled");
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}
async function handleCancel() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  try {
    loading.value = true;
    await nextTick();
    close("rejected");
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}
async function handleResets() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  try {
    loading.value = true;
    await nextTick();
    form.value = await getForm(toValue(params));
    await resetFormInit();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}
async function handleOpener() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  try {
    loading.value = true;
    await nextTick();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}

function close(status: "fulfilled" | "rejected") {
  switch (status) {
    case "fulfilled":
      handle.resolve(toValue(form));
      break;
    case "rejected":
      handle.reject(Object.assign(new Error("Cancel"), toValue(form)));
      break;
    default:
      handle.reject(Object.assign(new Error("Error"), toValue(form)));
      break;
  }
  params.value = {};
  visible.value = false;
  nextTick(() => {
    loading.value = false;
    window.setTimeout(() => {
      handle.resolve = (form: EditorItem) => void form;
      handle.reject = (err: Error) => void err;
      handle.callback = undefined;
    });
  });
}

async function operate($handle: () => Promise<void>, $params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>): Promise<EditorItem> {
  if (toValue(loading)) return toValue(form);
  $width.value = props.width / 2 > 360 ? props.width / 2 : 360;
  $height.value = props.height;
  params.value = $params;
  loading.value = true;
  visible.value = true;
  const result = new Promise<EditorItem>((resolve, reject) => Object.assign(handle, { resolve, reject, callback }));
  await nextTick();
  try {
    await runningInit();
    loading.value = false;
    await handleResets();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    loading.value = false;
    await handleCancel();
  }
  await $handle();
  try {
    return result;
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : "Error");
  }
}

const contentRef = ref<InstanceType<typeof import("element-plus").ElScrollbar>>();
function handleZoom($event: MouseEvent) {
  const { wrapRef } = toValue(contentRef) || {};
  if (wrapRef) $height.value = wrapRef.offsetHeight;
  const w = toValue($width);
  const h = toValue($height);

  const max_w = props.width;
  const min_w = 360;

  const max_h = props.height;
  const min_h = 62;

  const controller = new AbortController();
  window.document.addEventListener(
    "mousemove",
    (e) => {
      e.preventDefault();
      nextTick(() => {
        const _w = w + (e.clientX - $event.clientX) * 2;
        $width.value = _w < max_w ? (_w > min_w ? _w : min_w) : max_w;
        const _h = h + (e.clientY - $event.clientY) * 1;
        $height.value = _h < max_h ? (_h > min_h ? _h : min_h) : max_h;
      });
    },
    { signal: controller.signal }
  );
  window.document.addEventListener(
    "mouseup",
    () => {
      controller.abort();
    },
    { once: true, signal: controller.signal }
  );
}

defineExpose({
  async finish($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleFinish, $params, callback);
  },
  async submit($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleSubmit, $params, callback);
  },
  async cancel($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleCancel, $params, callback);
  },
  async resets($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleResets, $params, callback);
  },
  async opener($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleOpener, $params, callback);
  },
});

/*  */
interface ConstructorFunc<T> {
  new (value: unknown): T;
  (value: unknown): T;
}

interface DefaultFormData<T> {
  value: T;
  test: (v: unknown) => v is T;
  transfer: (fv: unknown, ov: T) => T;
  type: string;
}

function buildDefaultType<T>(value: T, pattern?: RegExp): DefaultFormData<T> {
  const objectConstructor: string = Object.prototype.toString.call(value);
  const ConstructorFunction = new Object(value).constructor as ConstructorFunc<T>;
  const type = (/^\[object\s(?<type>[a-zA-Z0-9]*)\]$/g.exec(objectConstructor)?.groups?.type as string).toLowerCase();

  switch (objectConstructor) {
    case "[object Undefined]":
    case "[object Null]": {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? (pattern instanceof RegExp ? pattern.test(String(v)) : true) : false);
      const transfer = (fv: unknown, ov: T): T => (test(fv) ? fv : ov);
      return { value, test, transfer, type };
    }
    case "[object Boolean]":
    case "[object Number]":
    case "[object String]": {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? (pattern instanceof RegExp ? pattern.test(String(v)) : true) : false);
      const transfer = (fv: unknown, ov: T): T => {
        if (test(fv)) return fv;
        if (pattern instanceof RegExp || fv === undefined || fv === null) return ov;
        try {
          return ConstructorFunction(fv);
        } catch (error) {
          return ov;
        }
      };
      return { value, test, transfer, type };
    }
    case "[object Object]": {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? true : false);
      const transfer = (fv: unknown, ov: T): T => {
        if (test(fv)) return fv;
        else return ov;
      };
      return { value, test, transfer, type };
    }
    case "[object Array]": {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? true : false);
      const transfer = (fv: unknown, ov: T): T => {
        if (test(fv)) return fv;
        try {
          return Array.from(fv as Iterable<T> | ArrayLike<T>) as unknown as T;
        } catch (error) {
          return ov;
        }
      };
      return { value, test, transfer, type };
    }
    default: {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? true : false);
      const transfer = (fv: unknown, ov: T): T => (test(fv) ? fv : ov);
      return { value, test, transfer, type };
    }
  }
}
</script>

<style scoped lang="scss">
.size-full {
  width: 100%;
  height: 100%;
}
</style>
