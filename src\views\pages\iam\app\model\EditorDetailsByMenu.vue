<template>
  <!-- 对话框表单 -->
  <el-dialog v-model="data.visible" :close-on-click-modal="false" draggable :width="`${width}px`" :before-close="handleCancel">
    <template #header>
      <div class="title">{{ editorType[$params["#TYPE"]] }}{{ props.title }}</div>
      <!-- {{ appType }}
      {{ form.type }} -->
    </template>
    <template #default>
      <el-scrollbar ref="contentRef" :height="height">
        <FormModel ref="formRef" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @submit="handleFinish">
          <template v-if="[EditorType.Add, EditorType.Mod].includes($params['#TYPE'] || '')">
            <!--  -->
            <FormItem :span="24" :label="`${props.title}名称`" tooltip="" prop="title" :rules="[buildValidatorData({ name: 'required', title: `${props.title}名称` })]">
              <el-input v-model="form.title" type="text" clearable :placeholder="t('glob.Please input field', { field: `${props.title}名称` })"></el-input>
            </FormItem>

            <FormItem :span="24" :label="`${props.title}英文名称`" tooltip="" prop="enTitle">
              <el-input v-model="form.enTitle" type="text" clearable :placeholder="t('glob.Please input field', { field: `${props.title}英文名称` })"></el-input>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="24" :label="`${props.title}类型`" prop="type" :tooltip="`${props.title}类型，创建后不可更改！`" :rules="[buildValidatorData({ name: 'required', title: `${props.title}类型` })]">
              <el-radio-group v-model="form.type" :disabled="$params['#TYPE'] !== EditorType.Add">
                <el-radio v-for="item in appTypeOption" :key="item.value" :value="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="24" :label="`${props.title}终端类型`" prop="terminal" :tooltip="``" :rules="[buildValidatorData({ name: 'required', title: `${props.title}终端类型` })]">
              <el-radio-group v-model="form.terminal" :disabled="$params['#TYPE'] !== EditorType.Add">
                <el-radio v-for="item in appTerminalOption" :key="item.value" :value="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="24" :label="`${props.title}备注`" tooltip="" prop="note" :rules="[]">
              <el-input v-model="form.note" type="text" clearable :placeholder="t('glob.Please input field', { field: `${props.title}备注` })"></el-input>
            </FormItem>
            <!--  -->
            <FormGroup :span="24" :label="(appTypeOption.find(({ value }) => value === form.type) || {}).label || '未选择类型'" tooltip="">
              <template #default="{ label }">
                <template v-if="!form.type">
                  <el-col :span="24"><el-empty description="请先选择菜单类型"></el-empty></el-col>
                </template>
                <template v-else-if="form.type === appType.DIR">
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}图标`" tooltip="" prop="icon" :rules="[]">
                    <IconSelector v-model="form.icon"></IconSelector>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}路径`" tooltip="" prop="path" :rules="[buildValidatorData({ name: 'required', title: `${label}路径` })]">
                    <el-input v-model="form.path" type="text" clearable :placeholder="t('glob.Please input field', { field: `${label}路径` })"></el-input>
                  </FormItem>
                </template>
                <template v-else-if="form.type === appType.MENU">
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}图标`" tooltip="" prop="icon" :rules="[]">
                    <IconSelector v-model="form.icon"></IconSelector>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}路径`" tooltip="" prop="path" :rules="[buildValidatorData({ name: 'required', title: `${label}路径` })]">
                    <el-input v-model="form.path" type="text" clearable :placeholder="t('glob.Please input field', { field: `${label}路径` })"></el-input>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}页面`" tooltip="页面文件位置，基于`views/pages`的绝对路径" prop="component" :rules="[buildValidatorData({ name: 'required', title: `${label}页面` })]">
                    <el-input v-model="form.component" type="text" clearable :placeholder="t('glob.Please input field', { field: `${label}页面` })"></el-input>
                  </FormItem>
                  <!-- <FormItem :span="width > 600 ? 12 : 24" :label="`${label}缓存`" :tooltip="`${props.title}缓存部分频繁切换的页面，可有效提高加载速度`" prop="keepalive" :rules="[]">
                    <el-switch v-model="form.keepalive" :active-value="true" :active-text="t('glob.Enable')" :inactive-value="false" :inactive-text="t('glob.Disable')"></el-switch>
                  </FormItem> -->
                  <!-- <FormItem :span="24" :label="`${label}权限`" :tooltip="`${props.title}需要关联的权限，可在应用权限内添加后进行关联`" prop="permission" :rules="[]">
                    <el-tree-select v-model="form.permission" :data="authGroupList" multiple filterable clearable auto-expand-parent node-key="id" :default-expanded-keys="swap.expanded" @node-expand="(data: TreeData) => swap.expanded.push(data.id)" :render-after-expand="false" class="tw-w-full" />
                  </FormItem> -->
                  <!-- <p style="padding-left: 10px; box-sizing: border-box">或者</p> -->
                  <!-- <FormItem :span="24" :label="`${label}权限`" :tooltip="`${props.title}需要关联的权限，可在应用权限内添加后进行关联`" :rules="[]">
                    <el-tree-select v-model="otherPermission" :data="authGroupList" multiple filterable clearable auto-expand-parent node-key="id" :default-expanded-keys="swap.expanded" @node-expand="(data: TreeData) => swap.expanded.push(data.id)" :render-after-expand="false" class="tw-w-full" />
                  </FormItem> -->
                </template>
                <template v-else-if="form.type === appType.MICRO">
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}图标`" tooltip="" prop="icon" :rules="[]">
                    <IconSelector v-model="form.icon"></IconSelector>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}路径`" tooltip="" prop="path" :rules="[buildValidatorData({ name: 'required', title: `${label}路径` })]">
                    <el-input v-model="form.path" type="text" clearable :placeholder="t('glob.Please input field', { field: `${label}路径` })"></el-input>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label} URL`" tooltip="" prop="url" :rules="[buildValidatorData({ name: 'required', title: `${label} URL` })]">
                    <el-input v-model="form.url" type="text" clearable :placeholder="t('glob.Please input field', { field: `${label} URL` })"></el-input>
                  </FormItem>
                  <!-- <FormItem :span="24" :label="`${label}权限`" :tooltip="`${props.title}需要关联的权限，可在应用权限内添加后进行关联`" prop="permission" :rules="[]">
                    <el-tree-select v-model="form.permission" :data="authGroupList" multiple filterable clearable auto-expand-parent node-key="id" :default-expanded-keys="swap.expanded" @node-expand="(data: TreeData) => swap.expanded.push(data.id)" :render-after-expand="false" class="tw-w-full" />
                  </FormItem> -->
                  <!-- <p style="padding-left: 10px; box-sizing: border-box">或者</p> -->
                  <!-- <FormItem :span="24" :label="`${label}权限`" :tooltip="`${props.title}需要关联的权限，可在应用权限内添加后进行关联`" :rules="[]">
                    <el-tree-select v-model="otherPermission" :data="authGroupList" multiple filterable clearable auto-expand-parent node-key="id" :default-expanded-keys="swap.expanded" @node-expand="(data: TreeData) => swap.expanded.push(data.id)" :render-after-expand="false" class="tw-w-full" />
                  </FormItem> -->
                </template>
                <template v-else-if="form.type === appType.LINK">
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}图标`" tooltip="" prop="icon" :rules="[]">
                    <IconSelector v-model="form.icon"></IconSelector>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label} URL`" tooltip="可以是菜单或路由ID，也可以是目标路径" prop="url" :rules="[buildValidatorData({ name: 'required', title: `${label} URL` })]">
                    <el-input v-model="form.url" type="text" clearable :placeholder="t('glob.Please input field', { field: `${label} URL` })"></el-input>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}打开方式`" :tooltip="``" prop="keepalive" :rules="[]">
                    <el-switch v-model="form.keepalive" :active-value="true" active-text="Blank" :inactive-value="false" inactive-text="Self"></el-switch>
                  </FormItem>
                  <!-- <FormItem :span="24" :label="`${label}权限`" :tooltip="`${props.title}需要关联的权限，可在应用权限内添加后进行关联`" prop="permission" :rules="[]">
                    <el-tree-select v-model="form.permission" :data="authGroupList" multiple filterable clearable auto-expand-parent node-key="id" :default-expanded-keys="swap.expanded" @node-expand="(data: TreeData) => swap.expanded.push(data.id)" :render-after-expand="false" class="tw-w-full" />
                    <p style="padding-left: 10px; box-sizing: border-box">或者</p>
                  </FormItem> -->
                  <!-- <p style="padding-left: 10px; box-sizing: border-box">或者</p> -->
                  <!-- <FormItem :span="24" :label="`${label}权限`" :tooltip="`${props.title}需要关联的权限，可在应用权限内添加后进行关联`" :rules="[]">
                    <el-tree-select v-model="otherPermission" :data="authGroupList" multiple filterable clearable auto-expand-parent node-key="id" :default-expanded-keys="swap.expanded" @node-expand="(data: TreeData) => swap.expanded.push(data.id)" :render-after-expand="false" class="tw-w-full" />
                  </FormItem> -->
                </template>
                <template v-else-if="form.type === appType.ROUTE">
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}图标`" tooltip="" prop="icon" :rules="[]">
                    <IconSelector v-model="form.icon"></IconSelector>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}路径`" tooltip="" prop="path" :rules="[buildValidatorData({ name: 'required', title: `${label}路径` })]">
                    <el-input v-model="form.path" type="text" clearable :placeholder="t('glob.Please input field', { field: `${label}路径` })"></el-input>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}页面`" tooltip="页面文件位置，基于`views/pages`的绝对路径" prop="component" :rules="[buildValidatorData({ name: 'required', title: `${label}页面` })]">
                    <el-input v-model="form.component" type="text" clearable :placeholder="t('glob.Please input field', { field: `${label}页面` })"></el-input>
                  </FormItem>
                  <!-- <FormItem :span="width > 600 ? 12 : 24" :label="`${label}缓存`" :tooltip="`${props.title}缓存部分频繁切换的页面，可有效提高加载速度`" prop="keepalive" :rules="[]">
                    <el-switch v-model="form.keepalive" :active-value="true" :active-text="t('glob.Enable')" :inactive-value="false" :inactive-text="t('glob.Disable')"></el-switch>
                  </FormItem> -->
                  <!-- <FormItem :span="24" :label="`${label}权限`" :tooltip="`${props.title}需要关联的权限，可在应用权限内添加后进行关联`" prop="permission" :rules="[]">
                    <el-tree-select v-model="form.permission" :data="authGroupList" multiple filterable clearable auto-expand-parent node-key="id" :default-expanded-keys="swap.expanded" @node-expand="(data: TreeData) => swap.expanded.push(data.id)" :render-after-expand="false" class="tw-w-full" />
                  </FormItem> -->
                  <!-- <p style="padding-left: 10px; box-sizing: border-box">或者</p> -->
                  <!-- <FormItem :span="24" :label="`${label}权限`" :tooltip="`${props.title}需要关联的权限，可在应用权限内添加后进行关联`" :rules="[]">
                    <el-tree-select v-model="otherPermission" :data="authGroupList" multiple filterable clearable auto-expand-parent node-key="id" :default-expanded-keys="swap.expanded" @node-expand="(data: TreeData) => swap.expanded.push(data.id)" :render-after-expand="false" class="tw-w-full" />
                  </FormItem> -->
                </template>
                <template v-else-if="form.type === appType.BUTTON">
                  <el-col :span="24">
                    <el-empty description="未开发！！！"></el-empty>
                  </el-col>
                </template>
                <template v-if="[appType.MENU, appType.MICRO, appType.LINK, appType.ROUTE].includes(form.type as appType)">
                  <el-col :span="24">
                    <el-form-item prop="permissionGroups">
                      <template #label>
                        <span>
                          权限
                          <el-tooltip :content="`${props.title}需要关联的权限，可在应用权限内添加后进行关联，同组为 AND 异组为或`">
                            <el-icon :size="16" style="vertical-align: middle; margin-left: 0.5em"><QuestionFilled /></el-icon>
                          </el-tooltip>
                          <!--  -->
                        </span>
                      </template>
                      <template #default>
                        <template v-for="(permission, index) in form.permissionGroups" :key="`${form.type}_permission-${index}`">
                          <el-divider v-if="index" direction="horizontal" content-position="left" style="margin: 10px 0; line-height: 12px"> OR </el-divider>
                          <div style="display: flex; width: 100%">
                            <div style="width: calc(100% - 58px)">
                              <el-tree-select :model-value="permission" @update:model-value="($event: string[]) => form.permissionGroups.splice(index, 1, isArray($event) ? $event : [])" :props="{ label: (data: TreeData) => data.name }" :data="treeSelect" multiple filterable :loading="selectLoad" remote :remote-method="filterMethod" clearable auto-expand-parent node-key="id" collapse-tags collapse-tags-tooltip :default-expanded-keys="[...swap.expanded]" @node-expand="(data: TreeData) => swap.expanded.add(data.id)" @node-collapse="(data: TreeData) => swap.expanded.delete(data.id)" :render-after-expand="false" style="width: 100%">
                                <template #default="{ data: $data }">
                                  <template v-if="select && $data.label.includes(select)">
                                    <span>{{ $data.label.substring(0, `${$data.label || ""}`.indexOf(select)) }}</span>
                                    <el-text type="success">{{ select }}</el-text>
                                    <span>{{ $data.label.replace(`${$data.label.substring(0, $data.label.indexOf(select))}${select}`, "") }}</span>
                                  </template>
                                  <template v-else>
                                    <span>{{ $data.label }}</span>
                                  </template>
                                </template>
                              </el-tree-select>
                            </div>
                            <el-button type="danger" :icon="Delete" :loading="selectLoad" style="margin-left: auto" @click="() => ((selectLoad = true), form.permissionGroups.splice(index, 1), nextTick(() => (selectLoad = false)))"></el-button>
                          </div>
                        </template>
                        <el-button type="primary" :icon="Plus" :loading="selectLoad" :style="{ width: '100%', marginTop: form.permissionGroups.length ? '12px' : '0' }" @click="() => ((selectLoad = true), form.permissionGroups.push([]), nextTick(() => (selectLoad = false)))">添加 OR 权限关系</el-button>
                      </template>
                    </el-form-item>
                  </el-col>
                </template>
              </template>
            </FormGroup>
            <!--  -->
          </template>
        </FormModel>
      </el-scrollbar>
    </template>
    <template #footer>
      <div>
        <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
        <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
        <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      </div>
      <div class="zoom-handle" @mousedown.self="handleZoom">
        <svg style="display: block; width: 60%; height: 60%; transform: translate(-25%, -25%); fill: currentColor; pointer-events: none" viewBox="0 0 1024 1024">
          <path d="M319.20128 974.56128L348.16 1003.52l655.36-655.36-28.95872-28.95872-655.36 655.36zM675.84 1003.52l327.68-327.68-28.95872-28.95872-327.68 327.68L675.84 1003.52z" fill="#000000"></path>
        </svg>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="PlatformEditor">
import { reactive, ref, nextTick, h, readonly, provide, toRefs, toRaw, computed, toValue } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep, isArray } from "lodash-es";
import { buildValidatorData } from "@/utils/validate";
import { ElForm, ElMessage, ElMessageBox } from "element-plus";
import { QuestionFilled, Plus, Delete } from "@element-plus/icons-vue";
import { EditorType, editorType } from "@/views/common/interface";
import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import IconSelector from "@/components/formItem/iconSelector.vue";
import { type NavItem as ItemData, appType, appTypeOption, appTerminal, appTerminalOption } from "@/api/application";
import { TypeHelper } from "@/utils/type";

import { getAuthCatalog, getAppAuth } from "@/api/application";
import { getPermissionGroups, getPermissionItems } from "@/api/application";
import type Node from "element-plus/es/components/tree/src/model/node";

type Item = Omit<ItemData, "createdTime" | "updatedTime" | "children" | "theme" | "query" | "params" | "hash" | "pattern" | "names" | "config" | "version">;

interface Props {
  title: string;
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  labelWidth: 130,
});

interface TreeData {
  label: string;
  name: string;
  id: string;
  children: TreeData[];
  disabled: boolean;
  isLeaf: boolean;
  parentId?: string | null;
  appId: string;
}
// const labelGetter = (data: TreeData): string => {
//   if (data.isLeaf) {
//     const $list: string[] = [];
//     const getItemById = (tree: TreeData[], id: string): TreeData | undefined => {
//       for (let i = 0; i < tree.length; i++) {
//         if (tree[i].id === id) return tree[i];
//         else {
//           const $find = getItemById(tree[i].children instanceof Array ? tree[i].children : [], id);
//           if ($find) return $find;
//         }
//       }
//     };
//     let $id = data.parentId;
//     while ($id) {
//       const $item = getItemById(toValue(treeData), $id);
//       $id = ($item || {}).parentId;
//       if ($item) $list.unshift($item.label);
//     }
//     return `${$list.join("/")} => ${data.label}`;
//   } else return data.label;
// };
const swap = reactive({ expanded: new Set<string>() });
const select = ref("");
const selectLoad = ref(false);
const treeData = ref<TreeData[]>([]);
const treeSelect = computed(() => {
  if (!toValue(select)) return toValue(treeData);
  const findMethod = (tree: TreeData): boolean => {
    const findRoot = tree.label.includes(toValue(select));
    if (findRoot) return findRoot;
    const findChild = tree.children.some(findMethod);
    if (findChild) return findChild;
    return false;
  };
  return toValue(treeData).filter(findMethod);
});
function filterMethod(value: string) {
  selectLoad.value = true;
  select.value = `${value || ""}`.trim();
  nextTick(() => (selectLoad.value = false));
}

const formRef = ref<InstanceType<typeof ElForm>>();
const contentRef = ref<InstanceType<typeof import("element-plus").ElScrollbar>>();

const { t } = useI18n();

// const sizeRef = ref<HTMLDivElement>();
const width = ref(document.body.clientWidth - 200);
const height = ref(document.body.clientHeight - 260 - document.body.clientHeight * 0.15);

interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve: ((value: Item) => void) | undefined;
  reject: ((value: Item) => void) | undefined;
  callback: ((form: Item) => Promise<boolean>) | undefined;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item> & { "#TYPE": EditorType; [key: string]: unknown }>({ "#TYPE": EditorType.Cat });
// const otherPermission = ref([]);
type DefaultForm<T> = { [P in keyof T]: { value: T[P]; test: (v: any) => v is T[P]; transfer: (fv: any, ov: T[P]) => T[P] } };
const defaultForm = readonly<DefaultForm<Required<Item>>>({
  id: { value: "", ...TypeHelper.string },
  rootId: { value: "", ...TypeHelper.string },
  parentId: { value: "", ...TypeHelper.string },
  name: { value: "", ...TypeHelper.string },
  title: { value: "", ...TypeHelper.string },
  enTitle: { value: "", ...TypeHelper.string },
  path: { value: "", ...TypeHelper.string },
  url: { value: "", ...TypeHelper.string },
  order: { value: 0, ...TypeHelper.number },
  icon: { value: "", ...TypeHelper.string },
  type: { value: appType.DIR, test: (v: any): v is appType => new RegExp(`^${Object.keys(appType).join("|")}$`, "g").test(v), transfer: (_, v) => v },
  enabled: { value: true, ...TypeHelper.boolean },
  permission: { value: [], ...TypeHelper.array },
  component: { value: "", ...TypeHelper.string },
  keepalive: { value: false, ...TypeHelper.boolean },
  terminal: { value: appTerminal.WEB, test: (v: any): v is appTerminal => new RegExp(`^${Object.keys(appTerminal).join("|")}$`, "g").test(v), transfer: (_, v) => v },
  note: { value: "", ...TypeHelper.string },
  permissionGroups: { value: [[]], ...TypeHelper.array },
});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  type DefaultForm = typeof defaultForm;
  const _form = (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], toRaw(util.value as never))) });
    },
    cloneDeep(form) as Required<Item>
  );
  return _form;
}
async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => {
      if (typeof formRef.value?.validate === "function") formRef.value.validate(resolve);
      else resolve(false);
    });
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") {
      const valid = await data.callback({ ...$form });
      if (!valid) throw new Error("Error");
    }

    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  formRef.value && formRef.value.clearValidate();
  const _form = await getForm($params.value);
  form.value = { ..._form };
  swap.expanded.clear();
  select.value = "";
  try {
    const [
      /*  */
      { success: groupsSuccess, message: groupsMessage, data: groupsData },
      { success: configSuccess, message: configMessage, data: configData },
      { success: authSuccess, message: authMessage, data: authData },
    ] = await Promise.all([
      /*  */
      getPermissionGroups({ appId: $params.value.rootId as string }),
      getPermissionItems({ appId: $params.value.rootId as string }),
      getAppAuth({ appId: $params.value.rootId as string }),
    ]);
    if (!groupsSuccess) throw Object.assign(new Error(groupsMessage), { success: groupsSuccess, data: groupsData });
    if (!configSuccess) throw Object.assign(new Error(configMessage), { success: configSuccess, data: configData });
    if (!authSuccess) throw Object.assign(new Error(authMessage), { success: authSuccess, data: authData });

    // const $swap = appData.reduce((p, c) => {
    //   const v = { id: c.id, label: c.name, parentId: c.catalogId, isLeaf: true, disabled: false, children: <TreeData[]>[], appId: $params.value.rootId || "" };
    //   if (p.has(c.catalogId)) (p.get(c.catalogId) as TreeData[]).push(v);
    //   else p.set(c.catalogId, [v]);
    //   return p;
    // }, new Map<string | null | undefined, TreeData[]>());
    // const catalog: TreeData[] = (catalogData instanceof Array ? catalogData : []).map((v) => ({ id: v.id, label: v.name, parentId: v.parentId, isLeaf: false, disabled: false, children: <TreeData[]>[], appId: $params.value.rootId || "" }));
    // while (catalog.filter((v) => Array.from($swap.keys()).includes(v.id)).length) {
    //   const itemKeysleIterator = $swap.keys();
    //   let key: IteratorResult<string | null | undefined>;
    //   while (!(key = itemKeysleIterator.next()).done) {
    //     const childrenItems = $swap.get(key.value);
    //     if (!childrenItems) continue;
    //     const $findCatalog = find(catalog, (v) => v.id === key.value);
    //     if ($findCatalog) {
    //       // $swap.delete(key.value);
    //       catalog.splice(catalog.indexOf($findCatalog), 1);
    //       $findCatalog.children = childrenItems;
    //       if ($swap.has($findCatalog.parentId)) {
    //         $swap.get($findCatalog.parentId)!.push($findCatalog);
    //       } else {
    //         const $parent = find(catalog, (v) => v.id === $findCatalog.parentId);
    //         if ($parent) {
    //           $parent.children.push($findCatalog);
    //           $swap.set($findCatalog.parentId, $parent.children);
    //         } else {
    //           $swap.set($findCatalog.parentId, [$findCatalog]);
    //         }
    //       }
    //     }
    //   }
    // }
    // treeData.value = $swap.get(null) || [];
    const expanded = _form.permissionGroups.reduce((p, c) => p.concat(c), []);
    treeData.value = groupsData.map((v) => {
      const config = configData
        .filter(({ groupId }) => groupId === v.id)
        .map((w): TreeData => {
          const auth = authData
            .filter(({ itemId }) => itemId === w.id)
            .map((x): TreeData => {
              if (expanded.includes(x.id)) {
                swap.expanded.add(v.id);
                swap.expanded.add(w.id);
              }
              return { id: x.id, label: x.name, name: `${v.name}/${w.name} => ${x.name || "--"}`, parentId: w.id, isLeaf: true, disabled: !x.enabled, children: [] as TreeData[], appId: $params.value.rootId || "" };
            });
          return { id: w.id, label: w.name, name: `${w.name || "--"}`, parentId: v.id, isLeaf: false, disabled: !w.enabled, children: auth, appId: $params.value.rootId || "" };
        });
      return { id: v.id, label: v.name, name: `${v.name || "--"}`, parentId: null, isLeaf: false, disabled: !v.enabled, children: config, appId: $params.value.rootId || "" };
    });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
  data.loading = false;
}

/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {}) as Item;
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

function handleZoom($event: MouseEvent) {
  const w = width.value;
  const h = ((toValue(contentRef) || {}).wrapRef || document.createElement("div")).offsetHeight || height.value;
  ($event.target as HTMLElement).ownerDocument.onmousemove = (e: MouseEvent) => {
    e.preventDefault();
    if (w + (e.clientX - $event.clientX) * 2 < document.body.clientWidth - 200) width.value = w + (e.clientX - $event.clientX) * 2 > 360 ? w + (e.clientX - $event.clientX) * 2 : 360;
    else width.value = document.body.clientWidth - 200;
    if (h + (e.clientY - $event.clientY) * 1 < document.body.clientHeight - 260 - document.body.clientHeight * 0.15) height.value = h + (e.clientY - $event.clientY) * 1 > 24 ? h + (e.clientY - $event.clientY) * 1 : 24;
    else height.value = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  };
  ($event.target as HTMLElement).ownerDocument.onmouseup = (e: MouseEvent) => {
    (e.target as HTMLElement).ownerDocument.onmousemove = null;
    (e.target as HTMLElement).ownerDocument.onmouseup = null;
  };
}

provide("#PARAMS", $params);
provide("#WIDTH", width);

defineExpose({
  close: handleCancel,
  open(params: Partial<ItemData> & { "#TYPE": EditorType; [key: string]: unknown }, callback?: (form: Item) => Promise<boolean>) {
    switch (params["#TYPE"]) {
      case EditorType.Cat:
      case EditorType.Add:
      case EditorType.Mod: {
        if (data.visible) {
          return new Promise((resolve) => {
            ElMessage.warning("先关闭其他弹窗再重试！");
            resolve(params);
          });
        } else {
          $params.value = {
            /*  */
            ...params,
            config: JSON.parse(params.config || "{}"),
            permission: [],
            permissionGroups: (params.permissionGroups instanceof Array && params.permissionGroups.length ? params.permissionGroups : params.permission instanceof Array && params.permission.length ? params.permission.map((v) => [v].filter((v) => typeof v === "string" || v)) : [[]]).map((v) => (v instanceof Array ? v : [])),
          };
          data.visible = true;
          data.loading = true;
          data.submitLoading = true;
          data.callback = callback;
          console.log(params, $params.value);
          return new Promise((resolve, reject) => {
            data.resolve = resolve;
            data.reject = reject;
            nextTick(async () => {
              width.value = document.body.clientWidth / 2;
              await nextTick();
              // if (formRef.value) {
              //   const maxHeight = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
              //   const formHeight = (formRef.value.$el as HTMLFormElement).clientHeight || 24;
              //   height.value = Math.max(formHeight, maxHeight);
              // }
              // if ([EditorType.Cat, EditorType.Mod].includes(params["#TYPE"])) {
              //   try {
              //     if (!params.id) throw new Error("找不到用户信息！");
              //     const { success, message, data } = await getUserById({ id: params.id });
              //     if (success) {
              //       Object.assign($params.value, data);
              //     } else Object.assign(new Error(message), { success, data });
              //   } catch (error) {
              //     await nextTick();
              //     if (error instanceof Error) ElMessage.error(error.message);
              //     return handleCancel();
              //     /*  */
              //   }
              // }
              await handleReset();
              data.loading = false;
              data.submitLoading = false;
            });
          });
        }
      }
      case EditorType.Del: {
        const option = reactive<{ message: string; valid: boolean; [key: string]: unknown }>({
          message: (params["#MESSAGE"] || `确认${editorType[params["#TYPE"]]}`) as string,
          valid: true,
        });
        return new Promise((resolve, reject) => {
          ElMessageBox({
            title: `${editorType[params["#TYPE"]]}${props.title}`,
            message() {
              return h("span", {}, [h("span", {}, option.message), h("span", { style: { margin: "0 3px", color: "var(--el-color-danger)" } }, params.name || "此"), option.valid ? h("span", {}, `${props.title}？`) : h("span", {}, `${props.title}删除失败！`)]);
            },
            type: "info",
            showCancelButton: true,
            showConfirmButton: true,
            cancelButtonText: t("glob.Cancel"),
            confirmButtonText: t("glob.delete"),
            distinguishCancelAndClose: true,
            draggable: true,
            async beforeClose(action, instance, done) {
              if (action === "confirm") {
                instance.confirmButtonLoading = true;
                try {
                  if (typeof callback === "function") option.valid = await callback(await getForm(params));
                  if (!option.valid) throw new Error("Error");
                  resolve(params);
                  done();
                } catch (error) {
                  option.message = "";
                  option.valid = false;
                  instance.showConfirmButton = false;
                  instance.type = "error";
                } finally {
                  instance.confirmButtonLoading = false;
                }
              } else {
                reject(params);
                done();
              }
            },
          })
            .then(async () => {})
            .catch(() => {
              reject(params);
            });
        });
      }
    }
  },
});
</script>

<style scoped lang="scss"></style>
