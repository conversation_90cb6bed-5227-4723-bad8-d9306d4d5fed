<template>
  <el-scrollbar>
    <div class="flex-search" :style="{ minWidth: `${props.width - 2}px` }">
      <div class="left"><slot name="left" :create="handleStateCreate" :delete="handleStateDelete" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot></div>
      <div class="center">
        <slot name="center" :create="handleStateCreate" :delete="handleStateDelete" :editor="handleStateEditor" :refresh="handleStateRefresh">
          <CopyRender :value="`module.exports = ${logTreeData(state.data)};\n`">Data</CopyRender>
          <CopyRender :value="`interface PermissionAttr ${logTreeType(state.data)}\n`">Type</CopyRender>
        </slot>
      </div>
      <!-- <slot name="right" :create="handleStateCreate" :delete="handleStateDelete" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot> -->
      <div class="right">
        <el-button type="primary" @click="createAuthGroups(), (dialogTitle = '配置组')">新建权限配置组</el-button>
      </div>
    </div>
  </el-scrollbar>
  <el-scrollbar ref="scrollbarRef" :height="props.height - 64 - 20" :style="{ width: `${props.width - 40}px`, margin: '0 auto 20px auto' }" @scroll="scrollPosition = $event">
    <!-- <el-tree ref="treeRef" v-loading="state.loading" :allow-drop="allowDrop" :allow-drag="(draggingNode: Node) => draggingNode.data.type === appType.DIR" :data="state.data" :default-expanded-keys="state.expand" :default-checked-keys="state.select" node-key="id" :props="{ label: 'name', children: 'children', disabled: 'disabled', isLeaf: 'isLeaf', class: (data) => `tree_context tree_data_${data.type.toLowerCase()} ${data.id}` }" draggable show-checkbox :expand-on-click-node="false" :default-expand-all="false" :render-after-expand="false" @node-drop="handleDrop" @node-expand="(data) => state.expand.push(data.id)" @node-collapse="state.expand.splice(state.expand.indexOf(data.id), 1)" @check="handleCheck">
      <template #default="{ node, data: authCatalog }: { node: Node, data: DataItem }">
        <div class="node_context_view tw-w-full">
          <div class="el-tree-node__label tw-flex tw-items-center">
            <el-icon class="tw-mr-[3px]">
              <Pointer v-if="authCatalog.type === appType.BUTTON" />
              <Folder v-else />
            </el-icon>
            <el-tooltip :content="authCatalog.note" :disabled="!authCatalog.note" placement="top" effect="dark">
              <span style="color: var(--text-color)">{{ node.label }}</span>
            </el-tooltip>
            <div class="tw-ml-auto">
              <el-link type="primary" :underline="false" :icon="Plus" class="tw-mx-[3px]" @click.stop="handleStateCreate({ parentId: authCatalog.id, orderNum: authCatalog.children.length }), (dialogTitle = '配置项')">{{ $t("glob.add") }}配置项</el-link>
              <el-link type="primary" :underline="false" :icon="Delete" class="tw-mx-[3px]" @click.stop="handleStateDelete(authCatalog), (dialogTitle = '配置项')">{{ $t("glob.delete") }}配置项</el-link>
              <el-link type="primary" :underline="false" :icon="Edit" class="tw-mx-[3px]" @click.stop="handleStateEditor(authCatalog), (dialogTitle = '配置项')">{{ $t("glob.edit") }}配置项</el-link>
            </div>
          </div>
          <div @dragstart.stop @dragover.stop class="tw-cursor-default">
            <draggable class="el-row tree_item_group" handle=".move_bar" group="main-list" tag="div" v-model="authCatalog.authorities" animation="300" ghostClass="ghost" chosenClass="chosen" item-key="name" @change="($event) => handleStateOrder($event, authCatalog)">
              <template #header>
                <el-col :span="24" class="tw-flex">
                  <el-checkbox :model-value="authCatalog.authorities.every((v) => v.enabled)" :disabled="!authCatalog.authorities.length" :indeterminate="authCatalog.authorities.every((v) => v.enabled) ? false : authCatalog.authorities.some((v) => v.enabled)" @change="() => setAllEnabled(authCatalog)">
                    <el-icon class="tw-align-middle"><Finished /></el-icon>
                    {{ $t("glob.All Select") }}
                  </el-checkbox>
                  <el-link type="primary" :underline="false" :icon="Plus" class="tw-ml-auto" @click.stop="createItem({ appId: props.data.name, catalogId: authCatalog.id, orderNum: authCatalog.authorities.length }).then(() => handleStateRefresh())">{{ $t("glob.add") }}权限</el-link>
                </el-col>
              </template>
              <template #item="{ element: authoritie }: { element: AuthorityItem }">
                <el-col :key="`${authCatalog.id}_${authoritie.id}`" :span="24" :xs="24" :sm="12" :md="12" :lg="6" :xl="3" :offset="0" draggable="true" class="tw-p-[12px]">
                  <div :id="authoritie.id" style="border: 1px solid var(--el-border-color); border-radius: 3px; padding: 0 8px 0 16px" class="list-item tw-relative tw-flex tw-items-center">
                    <div class="move_bar tw-m-0 tw-mr-[3px] tw-h-full tw-w-[16px]"></div>
                    <el-checkbox v-model="authoritie.enabled" class="tw-w-[calc(100%-32px)] tw-overflow-hidden tw-text-ellipsis" :indeterminate="false" :title="authoritie.name" @change="($event) => modAppAuth({ id: authoritie.id, enabled: $event as boolean })">{{ authoritie.name }}</el-checkbox>
                    <div class="tw-ml-auto">
                      <CopyRender :value="authoritie.id"></CopyRender>
                      <el-link class="tw-ml-[3px] tw-align-middle" type="primary" :underline="false" :icon="Edit" @click.stop="editorItem({ ...authoritie, catalogId: authCatalog.id }).then(() => handleStateRefresh())"></el-link>
                      <el-link class="tw-ml-[3px] tw-align-middle" type="primary" :underline="false" :icon="Delete" @click.stop="deleteItem({ id: authoritie.id }).then(() => handleStateRefresh())"></el-link>
                    </div>
                    <div class="tw-absolute tw-left-[12px] tw-top-0 tw-translate-y-[-50%] tw-bg-[var(--el-fill-color-blank)] tw-px-[0.5em] tw-text-[12px]">{{ authoritie.id }}</div>
                  </div>
                </el-col>
              </template>
            </draggable>
          </div>
        </div>
      </template>
    </el-tree> -->

    <el-tree ref="treeRef" v-loading="state.loading" :allow-drop="allowDrop" :allow-drag="(draggingNode: Node) => draggingNode.data.type === appType.DIR" :data="authGroupList" :default-expanded-keys="state.expand" :default-checked-keys="state.select" node-key="id" :props="{ label: 'name', children: 'children', disabled: 'disabled', isLeaf: 'isLeaf', class: (data) => `tree_context tree_data_ ${data.id}` }" draggable show-checkbox :expand-on-click-node="false" :default-expand-all="false" :render-after-expand="false" @node-drop="handleDrop" @node-expand="(data) => state.expand.push(data.id)" @node-collapse="state.expand.splice(state.expand.indexOf(data.id), 1)" @check="handleCheck">
      <template #default="{ node, data }: { node: Node, data: DataItem }">
        <div class="node_context_view tw-w-full">
          <div class="el-tree-node__label tw-flex tw-items-center">
            <el-icon class="tw-mr-[3px]">
              <Pointer v-if="true" />
              <Folder v-else />
            </el-icon>
            <!-- authCatalog.type === appType.BUTTON -->
            <!-- :content="authCatalog.note"  -->
            <!-- :disabled="!authCatalog.note" -->

            <el-tooltip placement="top" effect="dark">
              <span style="color: var(--text-color)">{{ node.label }}</span>
            </el-tooltip>
            <div class="tw-ml-auto" v-if="node.level == 1">
              <el-link type="primary" :underline="false" :icon="Plus" class="tw-mx-[3px]" @click.stop="handleStateCreate({ groupId: data.id, orderNum: 0 }), (dialogTitle = '配置模板')">{{ $t("glob.add") }}配置模板</el-link>
              <!-- <el-link type="primary" :underline="false" :icon="Delete" class="tw-mx-[3px]" @click.stop="handleStateDelete(authCatalog), (dialogTitle = '配置项')">{{ $t("glob.delete") }}配置项</el-link> -->
            </div>
            <div class="tw-ml-auto" v-if="node.level == 2">
              <el-link type="primary" :underline="false" :icon="Edit" class="tw-mx-[3px]" @click.stop="handleStateEditor(node.data), (dialogTitle = '配置项')">{{ $t("glob.edit") }}配置模板</el-link>
              <!-- <el-link type="primary" :underline="false" :icon="Delete" class="tw-mx-[3px]" @click.stop="handleStateDelete(authCatalog), (dialogTitle = '配置项')">{{ $t("glob.delete") }}配置项</el-link> -->
            </div>
          </div>
          <div @dragstart.stop @dragover.stop class="tw-cursor-default">
            <!-- <draggable class="el-row tree_item_group" handle=".move_bar" group="main-list" tag="div" v-model="authCatalog.authorities" animation="300" ghostClass="ghost" chosenClass="chosen" item-key="name" @change="($event) => handleStateOrder($event, authCatalog)">
              <template #header>
                <el-col :span="24" class="tw-flex">
                  <el-checkbox :model-value="authCatalog.authorities.every((v) => v.enabled)" :disabled="!authCatalog.authorities.length" :indeterminate="authCatalog.authorities.every((v) => v.enabled) ? false : authCatalog.authorities.some((v) => v.enabled)" @change="() => setAllEnabled(authCatalog)">
                    <el-icon class="tw-align-middle"><Finished /></el-icon>
                    {{ $t("glob.All Select") }}
                  </el-checkbox>
                  <el-link type="primary" :underline="false" :icon="Plus" class="tw-ml-auto" @click.stop="createItem({ appId: props.data.name, catalogId: authCatalog.id, orderNum: authCatalog.authorities.length }).then(() => handleStateRefresh())">{{ $t("glob.add") }}权限</el-link>
                </el-col>
              </template>
              <template #item="{ element: authoritie }: { element: AuthorityItem }">
                <el-col :key="`${authCatalog.id}_${authoritie.id}`" :span="24" :xs="24" :sm="12" :md="12" :lg="6" :xl="3" :offset="0" draggable="true" class="tw-p-[12px]">
                  <div :id="authoritie.id" style="border: 1px solid var(--el-border-color); border-radius: 3px; padding: 0 8px 0 16px" class="list-item tw-relative tw-flex tw-items-center">
                    <div class="move_bar tw-m-0 tw-mr-[3px] tw-h-full tw-w-[16px]"></div>
                    <el-checkbox v-model="authoritie.enabled" class="tw-w-[calc(100%-32px)] tw-overflow-hidden tw-text-ellipsis" :indeterminate="false" :title="authoritie.name" @change="($event) => modAppAuth({ id: authoritie.id, enabled: $event as boolean })">{{ authoritie.name }}</el-checkbox>
                    <div class="tw-ml-auto">
                      <CopyRender :value="authoritie.id"></CopyRender>
                      <el-link class="tw-ml-[3px] tw-align-middle" type="primary" :underline="false" :icon="Edit" @click.stop="editorItem({ ...authoritie, catalogId: authCatalog.id }).then(() => handleStateRefresh())"></el-link>
                      <el-link class="tw-ml-[3px] tw-align-middle" type="primary" :underline="false" :icon="Delete" @click.stop="deleteItem({ id: authoritie.id }).then(() => handleStateRefresh())"></el-link>
                    </div>
                    <div class="tw-absolute tw-left-[12px] tw-top-0 tw-translate-y-[-50%] tw-bg-[var(--el-fill-color-blank)] tw-px-[0.5em] tw-text-[12px]">{{ authoritie.id }}</div>
                  </div>
                </el-col>
              </template>
            </draggable> -->
          </div>
        </div>
      </template>
    </el-tree>
  </el-scrollbar>
  <EditorCatalogData ref="editorCatalogRef" :title="dialogTitle" :type="'template'"></EditorCatalogData>
  <EditorData ref="editorRef" title="权限"></EditorData>
</template>

<script setup lang="ts" name="ServeAuth">
import type Node from "element-plus/es/components/tree/src/model/node";
import type { AllowDropType, NodeDropType } from "element-plus/es/components/tree/src/tree.type";
import { useClipboard } from "@vueuse/core";

/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, nextTick, watch, onMounted, onBeforeUnmount, h, defineComponent, renderSlot, toRefs } from "vue";
import { useI18n } from "vue-i18n";
import { sizes } from "@/utils/common";
import draggable from "vuedraggable";
// import { formatterDate } from "@/utils/date";
import Sortable from "sortablejs";
import { cloneDeep, findIndex } from "lodash-es";
import { getTreeItemById, spliceTreeItemById, appendTreeItemById } from "@/utils/table";
import AuthItem from "./AuthItem.vue";

// Ui
import { ElMessage, ElButton, ElTag, ElLink, ElScrollbar, ElTree } from "element-plus";
import type { TableInstance } from "element-plus";
// eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
import { Plus, Edit, Delete, Folder, Pointer, Finished, CopyDocument, Checked } from "@element-plus/icons-vue";

// Api
// import { getAuthGroup as getItem, addAuthGroup as addItem, modAuthGroup as modItem, delAuthGroup as delItem, modAuthGroupByOrder } from "@/api/iam";
// import type { ModelItem, AuthGroupItem as DataItem } from "@/api/iam";
import { getAuthCatalog as getItem, addAuthCatalog as addItem, modAuthCatalog as modItem, delAuthCatalog as delItem, setAuthCatalogByOrder } from "@/api/application";
import { createPermissionGroups, getPermissionGroups, createPermissionModel, getPermissionModel, editorPermissionModel } from "@/api/application";

import { getAppAuth, addAppAuth, modAppAuth, delAppAuth, setAuthByOrder } from "@/api/application";
import { type AuthorityItem, type NavItem, type AuthCatalogItem as DataItem, appTerminal, appType, appTheme } from "@/api/application";

// Editor
import { EditorType } from "@/views/common/interface";
import EditorCatalogData from "./editorAuthModel.vue";
import EditorData from "./EditorByAuthItem.vue";

function logTreeData(data: DataItem[], prefix = "", depth = 1): string {
  return `{${data.map((v) => `\n${new Array(depth).fill("  ").join("")}${getName(v.name) ? `${getName(v.name)}:` : `${prefix ? `${prefix}_` : v.type === appType.BUTTON ? "auth" : "group"}${String(v.id).padStart(3, "0")}: /* ${v.name} */`} ${v.type === appType.BUTTON ? `"${v.id}"` : `${logTreeData(v.authorities.map((v): DataItem => ({ parentId: "", config: "", ...v, authorities: [], children: [] })).concat(v.children), "", depth + 1)}`},`).join("")}\n${new Array(depth - 1).fill("  ").join("")}}`;
}
function logTreeType(data: DataItem[], prefix = "", depth = 1): string {
  return `{${data.map((v) => `\n${new Array(depth).fill("  ").join("")}${getName(v.name) ? `${getName(v.name)}:` : `${prefix ? `${prefix}_` : v.type === appType.BUTTON ? "auth" : "group"}${String(v.id).padStart(3, "0")}:`} ${v.type === appType.BUTTON ? `string` : `${logTreeType(v.authorities.map((v): DataItem => ({ parentId: "", config: "", ...v, authorities: [], children: [] })).concat(v.children), "", depth + 1)}`};`).join("")}\n${new Array(depth - 1).fill("  ").join("")}}`;
}
function getName(name: string) {
  switch (name) {
    case "可读":
      return "preview";
    case "新增":
      return "create";
    case "编辑":
      return "editor";
    case "模版下载":
    case "下载模板":
      return "download_template";
    case "模版上传":
    case "上传模板":
      return "upload_template";
    case "下载":
      return "download";
    case "上传":
      return "upload";
    case "导入":
      return "import";
    case "导出":
      return "export";
    case "删除":
      return "remove";

    case "用户管理":
      return "user";
    case "重置密码":
      return "re_password";
    case "冻结/解冻":
    case "冻结 / 解冻":
      return "freeze_or_thawing";
    case "分配角色":
      return "assigning_roles";
    case "角色管理":
      return "role";
    case "设置角色类型":
      return "set_role_type";
    case "分配角色权限":
      return "assigning_role_permission";
    case "权限配置":
      return "permission_configuration";
    case "用户组":
      return "group";
    case "分配客户":
      return "assigning_tenant";
    case "变更托管客户":
      return "set_managed_tenant";
    case "添加托管客户":
      return "add_managed_tenant";
    case "移除托管客户":
      return "del_managed_tenant";
    case "查看托管客户":
      return "cat_managed_tenant";
    case "客户管理":
      return "tenant";
    case "日志中心":
      return "logger";
    case "添加成员":
      return "add_personnel";
    case "移除成员":
      return "del_personnel";
    default:
      return "";
  }
}
const CopyRender = defineComponent({
  props: {
    value: {
      type: String,
      default: "",
    },
  },
  setup(props, ctx) {
    const { copy, copied, isSupported } = useClipboard({ read: false, legacy: true });
    function copyData() {
      copy(props.value)
        .then(() => ElMessage.success("成功复制！"))
        .catch((error) => ElMessage.error(error instanceof Error ? error.message : "复制失败！"));
    }
    return () => (isSupported.value ? h(ElLink, { style: { marginLeft: "3px", verticalAlign: "middle" }, type: "primary", underline: false, icon: copied.value ? Checked : CopyDocument, title: t("glob.Copy"), onClick: (e) => (e.stopPropagation(), copyData()) }, () => renderSlot(ctx.slots, "default", {})) : null);
  },
});

const scrollbarRef = ref<InstanceType<typeof ElScrollbar>>();
const treeRef = ref<InstanceType<typeof ElTree>>();
const scrollPosition = ref({ scrollLeft: 0, scrollTop: 0 });

async function setAllEnabled(authCatalog: DataItem) {
  const dataList = authCatalog.authorities.every((v) => v.enabled) ? authCatalog.authorities.map((v) => Object.assign(v, { enabled: false })) : authCatalog.authorities.map((v) => Object.assign(v, { enabled: true }));
  await Promise.all(dataList.map((v) => modAppAuth({ id: v.id, enabled: v.enabled })));
  await handleStateRefresh();
}
const dialogTitle = ref("");
const editorCatalogRef = ref<InstanceType<typeof EditorCatalogData>>();
const editorRef = ref<InstanceType<typeof EditorData>>();

async function createCatalogItem(params: Partial<DataItem>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  // console.log(props.data);
  if (typeof onCleanup === "function") onCleanup(() => editorCatalogRef.value?.close());
  if (!editorCatalogRef.value) return;
  try {
    await editorCatalogRef.value.open({ ...params, "appId": props.data.rootId, "#TYPE": EditorType.Add }, async (req) => {
      // console.log(req);
      //permission  name==title

      try {
        const { success, message, data } = await createPermissionModel({ name: req.title, groupId: params.groupId, orderNum: 0, permissionIds: req.permissionIds });
        if (success) {
          ElMessage.success(`添加成功！`);
          if (!params.parentId) scrollbarRef.value && (scrollPosition.value.scrollTop = (<HTMLDivElement>scrollbarRef.value?.wrapRef).scrollHeight);
          else {
            if (treeRef.value) {
              const element = (<HTMLDivElement>treeRef.value.$el).getElementsByClassName(params.parentId).item(0) as HTMLDivElement | null;
              element && (scrollPosition.value.scrollTop = element.offsetTop);
            }
          }
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
async function editorCatalogItem(params: Partial<DataItem>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorCatalogRef.value?.close());
  if (!editorCatalogRef.value) return;
  try {
    await editorCatalogRef.value.open({ ...params, "#TYPE": EditorType.Mod }, async (req) => {
      try {
        const { success, message, data } = await editorPermissionModel({ ...req, orderNum: params.orderNum });
        if (success) {
          ElMessage.success(`编辑成功！`);
          if (!params.parentId) scrollbarRef.value && (scrollPosition.value.scrollTop = (<HTMLDivElement>scrollbarRef.value?.wrapRef).scrollHeight);
          else {
            if (treeRef.value) {
              const element = (<HTMLDivElement>treeRef.value.$el).getElementsByClassName(params.parentId).item(0) as HTMLDivElement | null;
              element && (scrollPosition.value.scrollTop = element.offsetTop);
            }
          }
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
async function deleteCatalogItem(params: Partial<DataItem>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorCatalogRef.value?.close());
  if (!editorCatalogRef.value) return;
  try {
    await editorCatalogRef.value.open({ ...params, "#TYPE": EditorType.Del }, async (req) => {
      try {
        const { success, message, data } = await delItem({ id: req.id as string });
        if (success) {
          ElMessage.success(`删除成功！`);
          if (!params.parentId) scrollbarRef.value && (scrollPosition.value.scrollTop = (<HTMLDivElement>scrollbarRef.value?.wrapRef).scrollHeight);
          else {
            if (treeRef.value) {
              const element = (<HTMLDivElement>treeRef.value.$el).getElementsByClassName(params.parentId).item(0) as HTMLDivElement | null;
              element && (scrollPosition.value.scrollTop = element.offsetTop);
            }
          }
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
async function querysCatalogItem(params: Record<"appId", string>, onCleanup?: (cleanupFn: () => void) => void) {
  if (!params.appId) return [];
  let controller = new AbortController();
  const response = getItem({ appId: params.appId as string, controller });
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await response;
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);
      return (data instanceof Array ? data : [])
        .map((value, _index, full) => {
          if (value.id === value.parentId) return value;
          else {
            return Object.assign(value, {
              children: full
                .filter(({ parentId }) => parentId === value.id)
                .map((v) => Object.assign(v, { consume: true }))
                .sort((a, b) => Number(a.orderNum) - Number(b.orderNum)),
            });
          }
        })
        .filter((v: import("@/api/application").AuthCatalogItem & { consume?: boolean }) => {
          const consume = v.consume;
          if (consume) delete v.consume;
          return !consume;
        })
        .sort((a, b) => Number(a.orderNum) - Number(b.orderNum));
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(20);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}

/*********************************************************/

const { t } = useI18n();

interface Props {
  data: NavItem;
  width: number;
  height: number;
}
const props = withDefaults(defineProps<Props>(), {
  data: () => ({ id: "", rootId: "", parentId: "", path: "", terminal: appTerminal.WEB, title: "", name: "", order: 0, icon: "local-SystemApps-line", type: appType.ROUTE, theme: appTheme.BASE, url: "", component: "", keepalive: false, enabled: true, permission: [], note: "", version: "", config: "{}", hash: "", query: {}, params: {}, pattern: /(?:)/, names: [], children: [] }),
  width: 100,
  height: 300,
});

interface StateData<T> {
  loading: boolean;
  move: boolean;
  select: string[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<DataItem>>({
  loading: false,
  move: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {
    keyword: "",
  },
  column: [
    /* 列 */
    // { key: "id", label: "ID", width: 220 },
    // { key: "title", label: "标题", width: 220 },
    { key: "name", label: "路由名称", width: 160 },
    // { key: "createdTime", label: "创建日期", formatter: (_row, _col, v) => formatterDate(v as string) },
    // { key: "updatedTime", label: "修改日期", formatter: (_row, _col, v) => formatterDate(v as string) },
  ],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

watch<string, true>(
  () => props.data.id,
  async function () {
    if (state.loading) return;
    await getAuthGroupList();

    await getPermissionList();

    await handleStateRefresh();

    // getTreAutheData();
  },
  { immediate: true, flush: "post" }
);

async function handleStateCreate(params: Partial<DataItem>) {
  await createCatalogItem(params);

  await getAuthGroupList();
  await getPermissionList();
}
async function createAuthGroups() {
  await createAuthItem({});

  await getAuthGroupList();
}

async function createAuthItem(params: Partial<DataItem>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorCatalogRef.value?.close());
  if (!editorCatalogRef.value) return;
  try {
    await editorCatalogRef.value.open({ ...params, "#TYPE": EditorType.Add }, async (req) => {
      try {
        const { success, message, data } = await createPermissionGroups({ ...req, appId: props.data.name, orderNum: 0 });
        if (success) {
          ElMessage.success(`添加成功！`);
          if (!params.parentId) scrollbarRef.value && (scrollPosition.value.scrollTop = (<HTMLDivElement>scrollbarRef.value?.wrapRef).scrollHeight);
          else {
            if (treeRef.value) {
              const element = (<HTMLDivElement>treeRef.value.$el).getElementsByClassName(params.parentId).item(0) as HTMLDivElement | null;
              element && (scrollPosition.value.scrollTop = element.offsetTop);
            }
          }
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}

async function handleStateEditor(params: Partial<DataItem>) {
  await editorCatalogItem(params);
  await handleStateRefresh();
}
async function handleStateDelete(params: Partial<DataItem>) {
  if (state.expand.includes(params.id!)) state.expand.splice(state.expand.indexOf(params.id!));
  await deleteCatalogItem(params);
  await handleStateRefresh();
}
const authGroupList = ref([]);
async function getAuthGroupList() {
  const { success, message, data } = await getPermissionGroups({ appId: props.data.id });
  if (!success) throw Object.assign(new Error(message), { success, data });
  // console.log(data);
  authGroupList.value = data;

  // return data;
}
async function getPermissionList() {
  const { success, message, data } = await getPermissionModel({ appId: props.data.id });
  if (!success) throw Object.assign(new Error(message), { success, data });
  // console.log(data);
  authGroupList.value.map((item) => {
    item.children = [];
    data.map((v) => {
      if (item.id === v.groupId) {
        item.children.push(v);
      }
    });
  });
  toRefs(authGroupList.value);
}
async function handleStateRefresh() {
  if (state.loading) return;
  state.loading = true;
  // state.data = [];
  await nextTick();
  const $data = await querysCatalogItem({ appId: props.data.name });
  try {
    const { success, message, data } = await getAppAuth({ appId: props.data.id });
    if (!success) throw Object.assign(new Error(message), { success, data });
    // console.log(data);
    // console.log(authGroupList.value);
    authGroupList.value.map((item) => {
      item.children.map((v) => {
        v.children = [];
        data.map((cv) => {
          if (cv.itemId === v.id) {
            v.children.push(cv);
          }
        });
      });
    });
    toRefs(authGroupList.value);
    // if (data instanceof Array) {
    //   getTreAutheData(
    //     $data,
    //     data
    //       .map((value, _index, full) => {
    //         if (value.id === value.catalogId) return value;
    //         else {
    //           return Object.assign(value, {
    //             children: full
    //               .filter(({ catalogId }) => catalogId === value.id)
    //               .map((v) => Object.assign(v, { consume: true }))
    //               .sort((a, b) => Number(a.orderNum) - Number(b.orderNum)),
    //           });
    //         }
    //       })
    //       .filter((v: AuthorityItem & { consume?: boolean }) => {
    //         const consume = v.consume;
    //         if (consume) delete v.consume;
    //         return !consume;
    //       })
    //       .sort((a, b) => Number(a.orderNum) - Number(b.orderNum))
    //       .reduce((p, c) => {
    //         const item = p.get(c.catalogId);
    //         if (item) item.push(c);
    //         else p.set(c.catalogId, [c]);
    //         return p;
    //       }, new Map<string, AuthorityItem[]>())
    //   );
    // }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
  /* 处理选中 */
  const select = [];
  state.select = [];
  const treeData = treeIterators($data);
  let treeResult: IteratorResult<DataItem>;
  while (!(treeResult = treeData.next()).done) {
    if (treeResult.value.enabled && treeResult.value.children.every((v) => v.enabled)) select.push(treeResult.value.id);
  }
  state.data.splice(0, state.data.length, ...$data);
  await nextTick();
  state.select.push(...select);
  treeRef.value && treeRef.value.setCheckedKeys(state.select);
  state.loading = false;
  await new Promise((resolve) => setTimeout(resolve));
  await nextTick();
  scrollbarRef.value && scrollbarRef.value.scrollTo({ top: scrollPosition.value.scrollTop, left: scrollPosition.value.scrollLeft, behavior: "smooth" });
}

function getTreAutheData(dataList: DataItem[], mergeList: Map<string, AuthorityItem[]>) {
  for (let i = 0; i < dataList.length; i++) {
    if (!mergeList.size) break;
    const item = mergeList.get(dataList[i].id);
    if (item) {
      mergeList.delete(dataList[i].id);
      Object.assign(dataList[i], { authorities: item });
    }
    if (dataList[i].children instanceof Array) getTreAutheData(dataList[i].children, mergeList);
  }
}

function allowDrop(draggingNode: Node, dropNode: Node, dropType: AllowDropType) {
  // // console.log("allowDrop", draggingNode, dropNode, dropType);
  switch (dropType) {
    case "prev":
      // // console.log(`准备拖拽\`${draggingNode.label}\` 插入 \`${dropNode.label}\`之前，父级ID：${dropNode.parent.key}，排序：${dropNode.parent.childNodes.indexOf(dropNode) - 1}`);
      return draggingNode.data.type === dropNode.data.type;
    case "inner":
      // // console.log(`准备拖拽\`${draggingNode.label}\` 插入 \`${dropNode.label}\`之内，父级ID：${dropNode.key}，排序：${dropNode.childNodes.length}`);
      return draggingNode.data.id !== dropNode.data.id;
    case "next":
      // // console.log(`准备拖拽\`${draggingNode.label}\` 插入 \`${dropNode.label}\`之后，父级ID：${dropNode.parent.key}，排序：${dropNode.parent.childNodes.indexOf(dropNode) - 1}`);
      return draggingNode.data.type === dropNode.data.type;
    default:
      return false;
  }
}
// 传递给 data 属性的数组中该节点所对应的对象
// 树目前的选中状态对象，包含 checkedNodes、checkedKeys、halfCheckedNodes、halfCheckedKeys 四个属性

async function handleCheck(data: DataItem, checked: { checkedNodes: Node[]; checkedKeys: string[]; halfCheckedNodes: Node[]; halfCheckedKeys: string[] }) {
  state.select = checked.checkedKeys;

  const dataItem = getTreeDataItem(state.data, data.id, { key: "id", children: "children" });
  if (!dataItem) return;

  // const isChecked = checked.checkedKeys.includes(data.id);
  // const isIndeterminate = checked.halfCheckedKeys.includes(data.id);
  // try {
  //   if (isIndeterminate) {
  //     const { success, message, data: resData } = await modItem({ id: data.id, enabled: true });
  //     if (!success) throw Object.assign(new Error(message), { success, data: resData });
  //   } else if (isChecked) {
  //     const { success, message, data: resData } = await modItem({ id: data.id, enabled: true });
  //     if (!success) throw Object.assign(new Error(message), { success, data: resData });
  //   } else {
  //     const { success, message, data: resData } = await modItem({ id: data.id, enabled: false });
  //     if (!success) throw Object.assign(new Error(message), { success, data: resData });
  //   }
  // } catch (error) {
  //   if (error instanceof Error) ElMessage.error(error.message);
  // }
  state.loading = true;

  try {
    const allCheckeds = [...checked.checkedKeys, ...checked.halfCheckedKeys];
    const result: Promise<import("@/api/service/common").Response<unknown>>[] = [];
    const treeData = treeIterators(state.data);
    let treeResult: IteratorResult<DataItem>;
    while (!(treeResult = treeData.next()).done) {
      const check = allCheckeds.includes(treeResult.value.id);
      if (treeResult.value.enabled === check) continue;
      Object.assign(treeResult.value, { enabled: check });
      // // console.log(treeResult.value.id, treeResult.value.name, treeResult.value.enabled);
      result.push(modItem({ id: treeResult.value.id, enabled: check }));
    }

    const res = await Promise.all(result);
    for (let i = 0; i < res.length; i++) {
      const { success, message, data: resData } = res[i];
      if (!success) throw Object.assign(new Error(message), { success, data: resData });
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    await handleStateRefresh();
  }
  state.loading = false;
}
// function handleDragStart(node: Node) {
//   // console.log(`开始拖拽 \`${node.label}\` 节点`);
// }
// function handleDragEnter(draggingNode: Node, dropNode: Node, ev: DragEvents) {
//   // console.log(`拖拽 \`${draggingNode.label}\` 进入 \`${dropNode.label}\` 节点`);
// }
// function handleDragLeave(draggingNode: Node, dropNode: Node, ev: DragEvents) {
//   // console.log(`拖拽 \`${draggingNode.label}\` 离开 \`${dropNode.label}\` 节点`);
// }
// function handleDragOver(draggingNode: Node, dropNode: Node, ev: DragEvents) {
//   // console.log(`拖拽节点：\`${draggingNode.label}\` 拖拽至 \`${dropNode.label}\``);
// }
// async function handleDragEnd(draggingNode: Node, dropNode: Node, dropType: NodeDropType) {
//   // // console.log(draggingNode, dropNode);
//   // await nextTick();
//   // switch (dropType) {
//   //   case "before":
//   //     // console.log(`拖拽结束\`${draggingNode.label}\` 插入 \`${dropNode.label}\`之前，父级ID：${dropNode.parent.key}，排序：${dropNode.parent.childNodes.indexOf(dropNode) - 1}`);
//   //     break;
//   //   case "inner":
//   //     // console.log(`拖拽结束\`${draggingNode.label}\` 插入 \`${dropNode.label}\`之内，父级ID：${dropNode.key}，排序：${dropNode.childNodes.length}`);
//   //     break;
//   //   case "after":
//   //     // console.log(`拖拽结束\`${draggingNode.label}\` 插入 \`${dropNode.label}\`之后，父级ID：${dropNode.parent.key}，排序：${dropNode.parent.childNodes.indexOf(dropNode) - 1}`);
//   //     break;
//   //   default:
//   //     // console.log(`节点 \`${draggingNode.label}\` 拖拽至 \`${dropNode.label}\` 无变化，父级ID：${draggingNode.key}，排序：${draggingNode.data.order}`);
//   //     break;
//   // }
// }
async function handleDrop(draggingNode: Node, dropNode: Node, dropType: NodeDropType) {
  await nextTick();
  const req: { id: string; order: number }[] = [];
  let result = Promise.resolve({ success: true, message: "", data: {} });

  try {
    switch (dropType) {
      case "before": {
        let currentNode: null | Node = dropNode.previousSibling as Node;
        let index = dropNode.parent.childNodes.indexOf(currentNode);
        while (currentNode) {
          req.push({ id: currentNode.data.id, order: index });
          index++;
          currentNode = currentNode.nextSibling;
        }
        if (draggingNode.data.parentId !== dropNode.data.parentId) {
          const { success, message, data } = await modItem({ id: draggingNode.data.id, parentId: dropNode.data.parentId || "-1" });
          if (!success) throw Object.assign(new Error(message), { success, data });
          req.push(...(getTreeDataChildren<DataItem>(state.data, draggingNode.data.parentId, { key: "id", children: "children" }) || []).map((v, i) => ({ id: v.id, order: i, name: v.name })));
        }
        break;
      }
      case "inner": {
        req.push({ id: draggingNode.data.id, order: dropNode.childNodes.length - 1 });
        if (draggingNode.data.parentId === dropNode.data.id) {
          for (let i = 0; i < dropNode.childNodes.length; i++) {
            if (draggingNode.data.id === dropNode.childNodes[i].data.id) continue;
            req.push({ id: dropNode.childNodes[i].data.id, order: i });
          }
        } else {
          const { success, message, data } = await modItem({ id: draggingNode.data.id, parentId: dropNode.data.id || "-1" });
          if (!success) throw Object.assign(new Error(message), { success, data });
          req.push(...(getTreeDataChildren<DataItem>(state.data, draggingNode.data.parentId, { key: "id", children: "children" }) || []).map((v, i) => ({ id: v.id, order: i, name: v.name })));
        }
        break;
      }
      case "after": {
        let currentNode: null | Node = dropNode.nextSibling as Node;
        let index = dropNode.parent.childNodes.indexOf(currentNode);
        while (currentNode) {
          req.push({ id: currentNode.data.id, order: index });
          index++;
          currentNode = currentNode.nextSibling;
        }
        if (draggingNode.data.parentId !== dropNode.data.parentId) {
          const { success, message, data } = await modItem({ id: draggingNode.data.id, parentId: dropNode.data.parentId || "-1" });
          if (!success) throw Object.assign(new Error(message), { success, data });
          req.push(...(getTreeDataChildren<DataItem>(state.data, draggingNode.data.parentId, { key: "id", children: "children" }) || []).map((v, i) => ({ id: v.id, order: i, name: v.name })));
        }
        break;
      }
      default: {
        break;
      }
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    await handleStateRefresh();
  }
  try {
    const { success, message, data } = await setAuthCatalogByOrder({ order: req });
    if (!success) throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    await handleStateRefresh();
  }
  treeRef.value && treeRef.value.setCheckedKeys(state.select);
}
function getTreeDataChildren<T extends object>(data: T[], getKey: T[keyof T], option?: Partial<{ key: keyof T; children: keyof T }>): T[] | null {
  const key = ((option || {}).key || "id") as keyof T;
  const children = ((option || {}).children || "children") as keyof T;
  if (!getKey) return data;
  for (let i = 0; i < data.length; i++) {
    if (data[i][key] === getKey) {
      return data[i][children] as unknown as T[];
    }
    if (<any>data[i][children] instanceof Array) {
      const find = getTreeDataChildren(data[i][children] as unknown as T[], getKey, { key, children });
      if (find) {
        return find;
      }
    }
  }
  return null;
}
function getTreeDataItem<T extends object>(data: T[], getKey: T[keyof T], option?: Partial<{ key: keyof T; children: keyof T }>): T | null {
  const key = ((option || {}).key || "id") as keyof T;
  const children = ((option || {}).children || "children") as keyof T;
  if (!getKey) return null;
  for (let i = 0; i < data.length; i++) {
    if (data[i][key] === getKey) {
      return data[i];
    }
    if (<any>data[i][children] instanceof Array) {
      const find = getTreeDataItem(data[i][children] as unknown as T[], getKey, { key, children });
      if (find) {
        return find;
      }
    }
  }
  return null;
}
function* treeIterators<T extends object>(data: T[], option?: Partial<{ children: keyof T }>): Generator<T> {
  const children = ((option || {}).children || "children") as keyof T;
  for (let i = 0; i < data.length; i++) {
    yield data[i];
    if (<any>data[i][children] instanceof Array && (data[i][children] as unknown as T[]).length) yield* treeIterators(data[i][children] as unknown as T[], { children });
  }
}

async function handleStateOrder({ moved, removed, added }: { moved?: { newIndex: number; oldIndex: number; element: AuthorityItem }; removed?: { oldIndex: number; element: AuthorityItem }; added?: { newIndex: number; element: AuthorityItem } }, catalog: DataItem) {
  state.loading = true;
  await nextTick();
  if (moved) {
    try {
      if (catalog.authorities.length) {
        const { success, message, data } = await setAuthByOrder({ order: catalog.authorities.map((v, i) => ({ id: v.id, order: i })) });
        if (!success) throw Object.assign(new Error(message), { success, data });
      }
    } catch (error) {
      await handleStateRefresh();
      if (error instanceof Error) ElMessage.error(error.message);
    }
  }
  if (removed) {
    try {
      if (catalog.authorities.length) {
        const { success, message, data } = await setAuthByOrder({ order: catalog.authorities.map((v, i) => ({ id: v.id, order: i })) });
        if (!success) throw Object.assign(new Error(message), { success, data });
      }
    } catch (error) {
      await handleStateRefresh();
      if (error instanceof Error) ElMessage.error(error.message);
    }
  }
  if (added) {
    try {
      const { success, message, data } = await modAppAuth({ id: added.element.id, orderNum: added.newIndex, catalogId: catalog.id });
      if (!success) throw Object.assign(new Error(message), { success, data });
    } catch (error) {
      await handleStateRefresh();
      if (error instanceof Error) ElMessage.error(error.message);
    }
    try {
      if (catalog.authorities.length) {
        const { success, message, data } = await setAuthByOrder({ order: catalog.authorities.map((v, i) => ({ id: v.id, order: i })) });
        if (!success) throw Object.assign(new Error(message), { success, data });
      }
    } catch (error) {
      await handleStateRefresh();
      if (error instanceof Error) ElMessage.error(error.message);
    }
  }
  state.loading = false;
}
</script>

<style lang="scss" scoped>
.list-item {
  position: relative;

  .move_bar {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    cursor: move;
  }
  &:hover {
    &::before {
      display: block;
    }
  }
  &::before {
    background-color: #666;
    border-radius: 50%;
    box-shadow: 0 6px 0 0 #666, 0 12px 0 0 #666, 0 -6px 0 0 #666, 6px 0 0 0 #3b3b3b, 6px 6px 0 0 #3b3b3b, 6px -6px 0 0 #3b3b3b, 6px 12px 0 0 #3b3b3b;
    content: "";
    display: none;
    flex-shrink: 0;
    width: 2px;
    height: 2px;
    position: absolute;
    top: calc(50% - 4px);
    left: 5px;
  }
}
</style>
<style lang="scss">
.tree_context {
  // .node_context_view {
  //   outline: var(--el-border);
  // }
  margin-top: 1em;
  .el-tree-node__content {
    height: fit-content;
    align-items: flex-start;
  }
  &.tree_data_dir {
    --text-color: var(--el-text-color-primary);
  }
  &.tree_data_button {
    --text-color: var(--el-color-primary);
  }
}
.tree_item_group {
  border-top: var(--el-border);
}
</style>
