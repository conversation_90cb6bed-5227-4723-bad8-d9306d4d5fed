<template>
  <!-- 对话框表单 -->
  <component :is="ComponentRender" v-model:visible="data.visible" :handle-cancel="handleCancel">
    <template #header>{{ editorType[$params["#TYPE"] as EditorType] }}{{ props.title }}</template>
    <template #default="{ width }">
      <!-- width -->
      <FormModel ref="formRef" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @submit="handleFinish">
        <template v-if="[EditorType.Add, EditorType.Mod].includes($params['#TYPE'] as EditorType || '')">
          <!--  -->
          <!--  -->
          <!--  -->
          <FormItem :span="width > 600 ? 12 : 24" :label="`${props.title}编码`" prop="code" :tooltip="`${props.title}平台编码为平台唯一标识符，全局唯一且不可修改`" :rules="[buildValidatorData({ name: 'required', title: `${props.title}编码` })]">
            <el-input v-model="form.code" type="text" :disabled="$params['#TYPE'] !== EditorType.Add" :placeholder="t('glob.Please input field', { field: `${props.title}编码` })"></el-input>
          </FormItem>
          <!--  -->
          <!--  -->
          <!--  -->
          <FormItem :span="width > 600 ? 12 : 24" label="租户" :tooltip="`${props.title}是否启用多租户，创建后不可修改`" prop="multiTenant" :rules="[]">
            <el-switch v-model="form.multiTenant" :disabled="$params['#TYPE'] !== EditorType.Add" :active-value="true" active-text="多租户" :inactive-value="false" inactive-text="非租户"></el-switch>
          </FormItem>
          <!--  -->
          <!--  -->
          <!--  -->
          <FormItem :span="width > 600 ? 12 : 24" :label="`${props.title}名称`" tooltip="" prop="name" :rules="[buildValidatorData({ name: 'required', title: `${props.title}名称` })]">
            <el-input v-model="form.name" type="text" clearable :placeholder="t('glob.Please input field', { field: `${props.title}名称` })"></el-input>
          </FormItem>
          <!--  -->
          <!--  -->
          <!--  -->
          <FormItem :span="width > 600 ? 12 : 24" :label="`显示名称`" tooltip="" prop="openName" :rules="[buildValidatorData({ name: 'required', title: `${props.title}名称` })]">
            <el-input v-model="form.openName" type="text" clearable :placeholder="t('glob.Please input field', { field: `${props.title}名称` })"></el-input>
          </FormItem>
          <!--  -->
          <!--  -->
          <!--  -->
          <FormItem :span="24" :label="`${props.title}备注`" tooltip="" prop="note" :rules="[]">
            <el-input v-model="form.note" type="text" clearable :placeholder="t('glob.Please input field', { field: `${props.title}备注` })"></el-input>
          </FormItem>
          <!--  -->
          <FormGroup :span="24" label="安全配置" tooltip="包含平台登录授权限制和密码有效期配置">
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" label="多因素登录" tooltip="" prop="securityConfig.enableMfa" :rules="[]">
              <el-switch v-model="form.securityConfig.enableMfa" :active-value="true" :active-text="t('glob.Enable')" :inactive-value="false" :inactive-text="t('glob.Disable')"></el-switch>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" label="重复登录" tooltip="开启重复登录后可多台设备同时登录，异地登录不会产生冲突" prop="securityConfig.repeatLogin" :rules="[]">
              <el-switch v-model="form.securityConfig.repeatLogin" :active-value="true" :active-text="t('glob.Enable')" :inactive-value="false" :inactive-text="t('glob.Disable')"></el-switch>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" label="登录失败限制" tooltip="登录失败次数限制, 大于0时生效, 连续多次登录失败则暂时冻结账号" prop="securityConfig.loginFailureLimit" :rules="[buildValidatorData({ name: 'required', title: '登录失败次数限制' })]">
              <el-input-number v-model="form.securityConfig.loginFailureLimit" label="" :min="0" :max="Number.MAX_SAFE_INTEGER" :step="1" :precision="0" :step-strictly="true" :controls="false" :placeholder="t('glob.Please input field', { field: '登录失败次数限制' })" class="text-left_input__inner tw-w-full"></el-input-number>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" label="密码复用次数" tooltip="最近几次使用过的密码不能重复使用, 大于0生效" prop="securityConfig.histPasswordLimit" :rules="[buildValidatorData({ name: 'required', title: '密码重复复用次数' })]">
              <el-input-number v-model="form.securityConfig.histPasswordLimit" label="" :min="0" :max="Number.MAX_SAFE_INTEGER" :step="1" :precision="0" :step-strictly="true" :controls="false" :placeholder="t('glob.Please input field', { field: '密码重复复用次数' })" class="text-left_input__inner tw-w-full"></el-input-number>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" label="密码过期天数" tooltip="密码过期天数, 大于0生效" prop="securityConfig.passwordExpireDays" :rules="[buildValidatorData({ name: 'required', title: '密码过期天数' })]">
              <el-input-number v-model="form.securityConfig.passwordExpireDays" label="" :min="0" :max="Number.MAX_SAFE_INTEGER" :step="1" :precision="0" :step-strictly="true" :controls="false" :placeholder="t('glob.Please input field', { field: '密码过期天数' })" class="text-left_input__inner tw-w-full"></el-input-number>
            </FormItem>
            <!--  -->
          </FormGroup>
          <!--  -->
          <FormItem :span="width > 600 ? 12 : 24" label="权限校验" tooltip="" prop="enablePermissionCheck" :rules="[]">
            <el-switch v-model="form.enablePermissionCheck" :active-value="true" :active-text="t('glob.Enable')" :inactive-value="false" :inactive-text="t('glob.Disable')"></el-switch>
          </FormItem>
          <!--  -->
          <!--  -->
          <!--  -->
          <FormItem :span="width > 600 ? 12 : 24" label="允许注册" tooltip="是否允许用户注册" prop="registrable" :rules="[]">
            <el-switch v-model="form.registrable" :active-value="true" :active-text="t('glob.Enable')" :inactive-value="false" :inactive-text="t('glob.Disable')"></el-switch>
          </FormItem>
          <!--  -->
          <!--  -->
          <!--  -->
          <FormItem :span="width > 600 ? 12 : 24" label="信息脱敏" tooltip="关键信息是否脱敏" prop="desensitize" :rules="[]">
            <el-switch v-model="form.desensitize" :active-value="true" :active-text="t('glob.Enable')" :inactive-value="false" :inactive-text="t('glob.Disable')"></el-switch>
          </FormItem>
          <!--  -->
          <!--  -->
          <!--  -->
          <FormItem :span="width > 600 ? 12 : 24" label="初始密码" tooltip="平台用户的初始密码" prop="initialUserPassword" :rules="[buildValidatorData({ name: 'password', title: '初始密码' })]">
            <el-input v-model="form.initialUserPassword" type="password" show-password clearable :placeholder="t('glob.Please input field', { field: `${props.title}初始密码` })"></el-input>
          </FormItem>
          <!--  -->
          <!--  -->
          <!--  -->
          <FormItem :span="24" label="登录地址" prop="loginPage" :rules="[buildValidatorData({ name: 'url', title: '登录地址' })]">
            <el-input v-model="form.loginPage" clearable :placeholder="t('glob.Please input field', { field: `${props.title}登录地址` })"></el-input>
          </FormItem>
          <!--  -->
          <!--  -->
          <!--  -->
          <FormGroup :span="24" label="平台配置信息" tooltip="">
            <FormItem :span="24" label="页脚" tooltip="" prop="config.footer" :rules="[buildValidatorData({ name: 'required', title: '平台页脚' })]">
              <CodemirrorEditor v-model="form.config.footer" :extensions="[html({ matchClosingTags: true, autoCloseTags: true })]"></CodemirrorEditor>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="24" label="主色" tooltip="" prop="config.primary" :rules="[buildValidatorData({ name: 'required', title: '主色' })]">
              <el-color-picker v-model="form.config.primary" show-alpha />
            </FormItem>
          </FormGroup>
          <!--  -->
          <!--  -->
          <!--  -->
          <FormGroup v-if="!form.multiTenant && $params['#TYPE'] === EditorType.Add" :span="24" label="平台拥有人" tooltip="填写后将在平台下自动创建一个用户，作为平台拥有人">
            <OwnerSelect v-model="form.owner" prefix="owner" label="拥有人" :required="false" :platform="''" :width="width"></OwnerSelect>
            <!-- <FormItem :span="width > 600 ? 12 : 24" label="页脚" tooltip="" prop="config.footer" :rules="[buildValidatorData({ name: 'required', title: '平台页脚' })]">
              <CodemirrorEditor v-model="form.config.footer" :extensions="[html({ matchClosingTags: true, autoCloseTags: true })]"></CodemirrorEditor>
            </FormItem> -->
          </FormGroup>
          <!--  -->
        </template>
      </FormModel>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </component>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, computed, h, renderSlot, getCurrentInstance, createVNode } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep, find, findIndex } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { buildTypeHelper } from "@/utils/type";
import { useConfig } from "@/stores/config";
import moment from "moment";
import { EditorType, editorType } from "@/views/common/interface";

import { buildValidatorData } from "@/utils/validate";

import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import { type PlatformItem as ItemData, gender, language, UserBaseItem } from "@/api/iam";

import getUserInfo from "@/utils/getUserInfo";

import CodemirrorEditor from "@/components/formItem/codemirror/index.vue";
import { html } from "@codemirror/lang-html";

import OwnerSelect from "@/components/formItem/OwnerSelect.vue";

const userInfo = getUserInfo();
const config = useConfig();
const { t } = useI18n({ useScope: "global" });
const formRef = ref<InstanceType<typeof FormModel>>();
const ctx = getCurrentInstance();
if (!ctx) throw new Error("Component context initialization failed!");
const { appContext } = ctx;

interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 120,
});

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
type Item = Omit<ItemData, "createdTime" | "updatedTime" | "config" | "ownerId" | "owner"> & { config: Record<string, string>; owner: UserBaseItem };
// interface Item {
//   baileeTenantId: string; // 受托租户ID
//   name: string;
//   abbreviation: string;
//   systemEdition: string;
//   zoneId: string;
//   language: string;
//   note: string;
//   address: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  code: buildTypeHelper<Required<Item>["code"]>(""),
  name: buildTypeHelper<Required<Item>["name"]>(""),
  openName: buildTypeHelper<Required<Item>["openName"]>(""),
  note: buildTypeHelper<Required<Item>["note"]>(""),
  multiTenant: buildTypeHelper<Required<Item>["multiTenant"]>(false),
  securityConfig: buildTypeHelper<Required<Item>["securityConfig"]>({ enableMfa: false /* 是否开启多因素登录 */, repeatLogin: false /* 是否允许重复登录 */, loginFailureLimit: 0 /* 登录失败次数限制, 大于0时生效, 连续多次登录失败则暂时冻结账号 */, histPasswordLimit: 0 /* 最近几次使用过的密码不能重复使用, 大于0生效, 默认不限制 */, passwordExpireDays: 0 /* 密码过期天数, 大于0生效, 默认不过期 */ }),
  enablePermissionCheck: buildTypeHelper<Required<Item>["enablePermissionCheck"]>(true),
  registrable: buildTypeHelper<Required<Item>["registrable"]>(true),
  desensitize: buildTypeHelper<Required<Item>["desensitize"]>(false),
  initialUserPassword: buildTypeHelper<Required<Item>["initialUserPassword"]>(""),
  loginPage: buildTypeHelper<Required<Item>["loginPage"]>(""),
  config: buildTypeHelper<Required<Item>["config"]>({}),
  owner: buildTypeHelper<UserBaseItem>({ name: "" /* 姓名 */, account: "" /* 账号 */, phone: "" /* 手机号 */, email: "" /* 邮箱 */, language: "" /* 语言 */, gender: gender.SECRET /* 性别 */, password: "" /* 密码 */ }),
});

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  // await (async () => {
  // })();
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  return form;
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 窗口方法
 */
interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<Item>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Record<string, unknown>) => void;
  reject?: (value: Error) => void;
  callback?: (form: Record<string, unknown>) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Record<string, unknown>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(function (formResult, [key, util]) {
    if (!util) return formResult;
    else if (util.test(formResult[key])) return formResult;
    else return Object.assign(formResult, { [key]: util.transfer(formResult[key], util.value as never) });
  }, form as Required<Item>);
}

async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => (formRef.value ? formRef.value.validate(resolve) : resolve(false)));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();

defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, callback?: (form: Record<string, unknown>) => Promise<void>): Promise<unknown> {
    switch (params["#TYPE"]) {
      case EditorType.Cat:
      case EditorType.Add:
      case EditorType.Mod: {
        if (data.visible) handleCancel();
        const wait = new Promise<Record<string, unknown>>((resolve, reject) => ((data.resolve = resolve), (data.reject = reject)));
        $params.value = { ...params, config: JSON.parse(<string>params.config || "{}") };
        data.visible = true;
        data.loading = true;
        data.submitLoading = true;
        data.callback = async (form) => {
          callback && (await callback({ ...form, config: JSON.stringify(form.config) }));
        };
        await nextTick();
        try {
          await runningInit($params.value);
        } catch (error) {
          if (error instanceof Error) ElMessage.error(error.message);
          handleCancel();
        }
        handleReset();
        data.loading = false;
        data.submitLoading = false;

        try {
          return await wait;
        } catch (error) {
          return error;
        }
      }
      case EditorType.Del: {
        const option = reactive<{ message: string; valid: boolean; [key: string]: unknown }>({
          message: (params["#MESSAGE"] || `确认${editorType[params["#TYPE"]]}`) as string,
          valid: true,
        });
        return new Promise((resolve, reject) => {
          ElMessageBox({
            title: `${editorType[<EditorType>params["#TYPE"]]}${props.title}`,
            message() {
              return h("span", {}, [h("span", {}, option.message), h("span", { style: { margin: "0 3px", color: "var(--el-color-danger)" } }, <string>params.name || "此"), option.valid ? h("span", {}, `${props.title}？`) : h("span", {}, `${props.title}删除失败！`)]);
            },
            type: "info",
            showCancelButton: true,
            showConfirmButton: true,
            cancelButtonText: t("glob.Cancel"),
            confirmButtonText: t("glob.delete"),
            distinguishCancelAndClose: true,
            draggable: true,
            async beforeClose(action, instance, done) {
              if (action === "confirm") {
                instance.confirmButtonLoading = true;
                try {
                  if (typeof callback === "function") await callback(await getForm(params));
                  if (!option.valid) throw new Error("Error");
                  resolve(params);
                  done();
                } catch (error) {
                  option.message = "";
                  option.valid = false;
                  instance.showConfirmButton = false;
                  instance.type = "error";
                } finally {
                  instance.confirmButtonLoading = false;
                }
              } else {
                reject(params);
                done();
              }
            },
          })
            .then(async () => {})
            .catch(() => {
              reject(params);
            });
        });
      }
    }
  },
  async confirm<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.confirm(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async alert<T extends { $title: string; $type: "" | "success" | "warning" | "info" | "error"; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.alert(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
});
</script>

<style scoped lang="scss"></style>
