<template>
  <!-- 对话框表单 -->
  <el-dialog v-model="data.visible" :close-on-click-modal="false" draggable :width="`${width}px`" :before-close="handleCancel">
    <template #header>
      <div class="title">{{ editorType[$params["#TYPE"]] }}{{ props.title }}</div>
    </template>
    <template #default>
      <el-scrollbar ref="contentRef" :height="height">
        <FormModel ref="formRef" :height="height" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @resize.once="handleSize" @submit="handleFinish">
          <template v-if="[EditorType.Add, EditorType.Mod].includes($params['#TYPE'] || '')">
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" :label="`${props.title}编码`" prop="code" :tooltip="`${props.title}平台编码为平台唯一标识符，全局唯一且不可修改`" :rules="[buildValidatorData({ name: 'required', title: `${props.title}编码` })]">
              <el-input v-model="form.code" type="text" :disabled="$params['#TYPE'] !== EditorType.Add" :placeholder="t('glob.Please input field', { field: `${props.title}编码` })"></el-input>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" label="租户" :tooltip="`${props.title}是否启用多租户，创建后不可修改`" prop="multiTenant" :rules="[]">
              <el-switch v-model="form.multiTenant" :disabled="$params['#TYPE'] !== EditorType.Add" :active-value="true" active-text="多租户" :inactive-value="false" inactive-text="非租户"></el-switch>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" :label="`${props.title}名称`" tooltip="" prop="name" :rules="[buildValidatorData({ name: 'required', title: `${props.title}名称` })]">
              <el-input v-model="form.name" type="text" clearable :placeholder="t('glob.Please input field', { field: `${props.title}名称` })"></el-input>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" :label="`显示名称`" tooltip="" prop="openName" :rules="[buildValidatorData({ name: 'required', title: `${props.title}名称` })]">
              <el-input v-model="form.openName" type="text" clearable :placeholder="t('glob.Please input field', { field: `${props.title}名称` })"></el-input>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="24" :label="`${props.title}备注`" tooltip="" prop="note" :rules="[]">
              <el-input v-model="form.note" type="text" clearable :placeholder="t('glob.Please input field', { field: `${props.title}备注` })"></el-input>
            </FormItem>
            <!--  -->
            <FormGroup :span="24" label="安全配置" tooltip="包含平台登录授权限制和密码有效期配置">
              <!--  -->
              <FormItem :span="width > 600 ? 12 : 24" label="多因素登录" tooltip="" prop="securityConfig.enableMfa" :rules="[]">
                <el-switch v-model="form.securityConfig.enableMfa" :active-value="true" :active-text="t('glob.Enable')" :inactive-value="false" :inactive-text="t('glob.Disable')"></el-switch>
              </FormItem>
              <!--  -->
              <!--  -->
              <!--  -->
              <FormItem :span="width > 600 ? 12 : 24" label="重复登录" tooltip="开启重复登录后可多台设备同时登录，异地登录不会产生冲突" prop="securityConfig.repeatLogin" :rules="[]">
                <el-switch v-model="form.securityConfig.repeatLogin" :active-value="true" :active-text="t('glob.Enable')" :inactive-value="false" :inactive-text="t('glob.Disable')"></el-switch>
              </FormItem>
              <!--  -->
              <!--  -->
              <!--  -->
              <FormItem :span="width > 600 ? 12 : 24" label="登录失败限制" tooltip="登录失败次数限制, 大于0时生效, 连续多次登录失败则暂时冻结账号" prop="securityConfig.loginFailureLimit" :rules="[buildValidatorData({ name: 'required', title: '登录失败次数限制' })]">
                <el-input-number v-model="form.securityConfig.loginFailureLimit" label="" :min="0" :max="Number.MAX_SAFE_INTEGER" :step="1" :precision="0" :step-strictly="true" :controls="false" :placeholder="t('glob.Please input field', { field: '登录失败次数限制' })" class="text-left_input__inner tw-w-full"></el-input-number>
              </FormItem>
              <!--  -->
              <!--  -->
              <!--  -->
              <FormItem :span="width > 600 ? 12 : 24" label="密码复用次数" tooltip="最近几次使用过的密码不能重复使用, 大于0生效" prop="securityConfig.histPasswordLimit" :rules="[buildValidatorData({ name: 'required', title: '密码重复复用次数' })]">
                <el-input-number v-model="form.securityConfig.histPasswordLimit" label="" :min="0" :max="Number.MAX_SAFE_INTEGER" :step="1" :precision="0" :step-strictly="true" :controls="false" :placeholder="t('glob.Please input field', { field: '密码重复复用次数' })" class="text-left_input__inner tw-w-full"></el-input-number>
              </FormItem>
              <!--  -->
              <!--  -->
              <!--  -->
              <FormItem :span="width > 600 ? 12 : 24" label="密码过期天数" tooltip="密码过期天数, 大于0生效" prop="securityConfig.passwordExpireDays" :rules="[buildValidatorData({ name: 'required', title: '密码过期天数' })]">
                <el-input-number v-model="form.securityConfig.passwordExpireDays" label="" :min="0" :max="Number.MAX_SAFE_INTEGER" :step="1" :precision="0" :step-strictly="true" :controls="false" :placeholder="t('glob.Please input field', { field: '密码过期天数' })" class="text-left_input__inner tw-w-full"></el-input-number>
              </FormItem>
              <!--  -->
            </FormGroup>
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" label="权限校验" tooltip="" prop="enablePermissionCheck" :rules="[]">
              <el-switch v-model="form.enablePermissionCheck" :active-value="true" :active-text="t('glob.Enable')" :inactive-value="false" :inactive-text="t('glob.Disable')"></el-switch>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" label="允许注册" tooltip="是否允许用户注册" prop="registrable" :rules="[]">
              <el-switch v-model="form.registrable" :active-value="true" :active-text="t('glob.Enable')" :inactive-value="false" :inactive-text="t('glob.Disable')"></el-switch>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" label="信息脱敏" tooltip="关键信息是否脱敏" prop="desensitize" :rules="[]">
              <el-switch v-model="form.desensitize" :active-value="true" :active-text="t('glob.Enable')" :inactive-value="false" :inactive-text="t('glob.Disable')"></el-switch>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" label="初始密码" tooltip="平台用户的初始密码" prop="initialUserPassword" :rules="[buildValidatorData({ name: 'password', title: '初始密码' })]">
              <el-input v-model="form.initialUserPassword" type="password" show-password clearable :placeholder="t('glob.Please input field', { field: `${props.title}初始密码` })"></el-input>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="24" label="登录地址" prop="loginPage" :rules="[buildValidatorData({ name: 'url', title: '登录地址' })]">
              <el-input v-model="form.loginPage" clearable :placeholder="t('glob.Please input field', { field: `${props.title}登录地址` })"></el-input>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormGroup :span="24" label="平台配置信息" tooltip="">
              <FormItem :span="24" label="页脚" tooltip="" prop="config.footer" :rules="[buildValidatorData({ name: 'required', title: '平台页脚' })]">
                <CodemirrorEditor v-model="form.config.footer" :extensions="[html({ matchClosingTags: true, autoCloseTags: true })]"></CodemirrorEditor>
              </FormItem>
              <!--  -->
              <!--  -->
              <!--  -->
              <FormItem :span="24" label="主色" tooltip="" prop="config.primary" :rules="[buildValidatorData({ name: 'required', title: '主色' })]">
                <el-color-picker v-model="form.config.primary" show-alpha />
              </FormItem>
            </FormGroup>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormGroup v-if="$params['#TYPE'] === EditorType.Add" :span="24" label="平台拥有人" tooltip="填写后将在平台下自动创建一个用户，作为平台拥有人">
              <OwnerSelect v-model="form.owner" prefix="owner" label="拥有人" :required="false" :platform="''" :width="width"></OwnerSelect>
              <!-- <FormItem :span="width > 600 ? 12 : 24" label="页脚" tooltip="" prop="config.footer" :rules="[buildValidatorData({ name: 'required', title: '平台页脚' })]">
              <CodemirrorEditor v-model="form.config.footer" :extensions="[html({ matchClosingTags: true, autoCloseTags: true })]"></CodemirrorEditor>
            </FormItem> -->
            </FormGroup>
            <!--  -->
          </template>
        </FormModel>
      </el-scrollbar>
    </template>
    <template #footer>
      <div>
        <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
        <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
        <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      </div>
      <div class="zoom-handle" @mousedown.self="handleZoom">
        <svg style="display: block; width: 60%; height: 60%; transform: translate(-25%, -25%); fill: currentColor; pointer-events: none" viewBox="0 0 1024 1024">
          <path d="M319.20128 974.56128L348.16 1003.52l655.36-655.36-28.95872-28.95872-655.36 655.36zM675.84 1003.52l327.68-327.68-28.95872-28.95872-327.68 327.68L675.84 1003.52z" fill="#000000"></path>
        </svg>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="PlatformEditor">
import { reactive, ref, nextTick, h, readonly, provide } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { buildValidatorData } from "@/utils/validate";
import { ElForm, ElMessage, ElMessageBox } from "element-plus";
import { EditorType, editorType } from "@/views/common/interface";
import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import OwnerSelect from "@/components/formItem/OwnerSelect.vue";
import { type PlatformItem as ItemData, gender, language } from "@/api/iam";
import { TypeHelper } from "@/utils/type";

import CodemirrorEditor from "@/components/formItem/codemirror/index.vue";
import { html } from "@codemirror/lang-html";

type Item = Omit<ItemData, "createdTime" | "updatedTime" | "config" | "ownerId"> & { config: Record<string, string> };

interface Props {
  title: string;
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  labelWidth: 130,
});

const formRef = ref<InstanceType<typeof ElForm>>();

const { t } = useI18n();

// const sizeRef = ref<HTMLDivElement>();
const width = ref(0);
const height = ref(0);

interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve: ((value: Partial<ItemData>) => void) | undefined;
  reject: ((value: Partial<ItemData>) => void) | undefined;
  callback: ((form: Partial<ItemData>) => Promise<boolean>) | undefined;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item> & { "#TYPE": EditorType; [key: string]: unknown }>({ "#TYPE": EditorType.Cat });

type DefaultForm<T> = { [P in keyof T]: { value: T[P]; test: (v: any) => v is T[P]; transfer: (fv: any, ov: T[P]) => T[P] } };
const defaultForm = readonly<DefaultForm<Required<Item>>>({
  code: { value: "", ...TypeHelper.string },
  name: { value: "", ...TypeHelper.string },
  openName: { value: "", ...TypeHelper.string },
  note: { value: "", ...TypeHelper.string },
  multiTenant: { value: false, ...TypeHelper.boolean },
  securityConfig: {
    value: { enableMfa: false /* 是否开启多因素登录 */, repeatLogin: false /* 是否允许重复登录 */, loginFailureLimit: 0 /* 登录失败次数限制, 大于0时生效, 连续多次登录失败则暂时冻结账号 */, histPasswordLimit: 0 /* 最近几次使用过的密码不能重复使用, 大于0生效, 默认不限制 */, passwordExpireDays: 0 /* 密码过期天数, 大于0生效, 默认不过期 */ },
    test: (v: unknown): v is Required<Item>["securityConfig"] => /^\[object\sObject\]$/g.test(Object.prototype.toString.call(v)),
    transfer: (v: unknown, f: Required<Item>["securityConfig"]): typeof f => (/^\[object\sObject\]$/g.test(Object.prototype.toString.call(v)) ? (v as typeof f) : f),
  },
  enablePermissionCheck: { value: true, ...TypeHelper.boolean },
  registrable: { value: true, ...TypeHelper.boolean },
  desensitize: { value: false, ...TypeHelper.boolean },
  initialUserPassword: { value: "", ...TypeHelper.string },
  loginPage: { value: "", ...TypeHelper.string },
  config: {
    value: {},
    test: (v: unknown): v is Required<Item>["config"] => /^\[object\sObject\]$/g.test(Object.prototype.toString.call(v)),
    transfer: (v: unknown, f: Required<Item>["config"]): typeof f => (/^\[object\sObject\]$/g.test(Object.prototype.toString.call(v)) ? (v as typeof f) : f),
  },
  owner: {
    value: {
      name: "" /* 姓名 */,
      nickname: "" /* 昵称 */,
      account: "" /* 账号 */,
      phone: "" /* 手机号 */,
      email: "" /* 邮箱 */,
      language: language.none /* 语言 */,
      gender: gender.SECRET /* 性别 */,
      password: "" /* 密码 */,
    },
    test: (v: unknown): v is Required<Item>["owner"] => /^\[object\sObject\]$/g.test(Object.prototype.toString.call(v)),
    transfer: (v: unknown, f: Required<Item>["owner"]): typeof f => (/^\[object\sObject\]$/g.test(Object.prototype.toString.call(v)) ? (v as typeof f) : f),
  },
});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<ItemData>> {
  type DefaultForm = typeof defaultForm;
  const _form = (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<ItemData>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], util.value as never)) });
    },
    cloneDeep(form) as Required<ItemData>
  );
  return { ..._form, config: JSON.stringify(_form.config) };
}
async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => {
      if (typeof formRef.value?.validate === "function") formRef.value.validate(resolve);
      else resolve(false);
    });
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") {
      const valid = await data.callback($form);
      if (!valid) throw new Error("Error");
    }

    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  formRef.value && formRef.value.clearValidate();
  const _form = await getForm($params.value);
  form.value = { ..._form, config: JSON.parse(_form.config) };
  data.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

function handleZoom($event: MouseEvent) {
  const w = width.value;
  const h = height.value;
  ($event.target as HTMLElement).ownerDocument.onmousemove = (e: MouseEvent) => {
    e.preventDefault();
    if (w + (e.clientX - $event.clientX) * 2 < document.body.clientWidth - 200) width.value = w + (e.clientX - $event.clientX) * 2 > 360 ? w + (e.clientX - $event.clientX) * 2 : 360;
    else width.value = document.body.clientWidth - 200;
    if (h + (e.clientY - $event.clientY) * 1 < document.body.clientHeight - 260 - document.body.clientHeight * 0.15) height.value = h + (e.clientY - $event.clientY) * 1 > 24 ? h + (e.clientY - $event.clientY) * 1 : 24;
    else document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  };
  ($event.target as HTMLElement).ownerDocument.onmouseup = (e: MouseEvent) => {
    (e.target as HTMLElement).ownerDocument.onmousemove = null;
    (e.target as HTMLElement).ownerDocument.onmouseup = null;
  };
}

function handleSize(size: { width: number; height: number }) {
  const maxHeight = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  const formHeight = size.height || 24;
  height.value = Math.min(formHeight, maxHeight);
}

provide("#PARAMS", $params);
provide("#WIDTH", width);

defineExpose({
  close: handleCancel,
  open(params: Partial<ItemData> & { "#TYPE": EditorType; [key: string]: unknown }, callback?: (form: Partial<ItemData>) => Promise<boolean>) {
    switch (params["#TYPE"]) {
      case EditorType.Cat:
      case EditorType.Add:
      case EditorType.Mod: {
        if (data.visible) {
          return new Promise((resolve) => {
            ElMessage.warning("先关闭其他弹窗再重试！");
            resolve(params);
          });
        } else {
          $params.value = { ...params, config: JSON.parse(params.config || "{}") };
          data.visible = true;
          data.loading = true;
          data.submitLoading = true;
          data.callback = callback;
          return new Promise((resolve, reject) => {
            data.resolve = resolve;
            data.reject = reject;
            nextTick(async () => {
              width.value = document.body.clientWidth / 2;
              await nextTick();
              // if (formRef.value) {
              //   const maxHeight = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
              //   const formHeight = (formRef.value.$el as HTMLFormElement).clientHeight || 24;
              //   height.value = Math.max(formHeight, maxHeight);
              // }
              // if ([EditorType.Cat, EditorType.Mod].includes(params["#TYPE"])) {
              //   try {
              //     if (!params.id) throw new Error("找不到用户信息！");
              //     const { success, message, data } = await getUserById({ id: params.id });
              //     if (success) {
              //       Object.assign($params.value, data);
              //     } else Object.assign(new Error(message), { success, data });
              //   } catch (error) {
              //     await nextTick();
              //     if (error instanceof Error) ElMessage.error(error.message);
              //     return handleCancel();
              //     /*  */
              //   }
              // }
              await handleReset();
              data.loading = false;
              data.submitLoading = false;
            });
          });
        }
      }
      case EditorType.Del: {
        const option = reactive<{ message: string; valid: boolean; [key: string]: unknown }>({
          message: (params["#MESSAGE"] || `确认${editorType[params["#TYPE"]]}`) as string,
          valid: true,
        });
        return new Promise((resolve, reject) => {
          ElMessageBox({
            title: `${editorType[params["#TYPE"]]}${props.title}`,
            message() {
              return h("span", {}, [h("span", {}, option.message), h("span", { style: { margin: "0 3px", color: "var(--el-color-danger)" } }, params.name || "此"), option.valid ? h("span", {}, `${props.title}？`) : h("span", {}, `${props.title}删除失败！`)]);
            },
            type: "info",
            showCancelButton: true,
            showConfirmButton: true,
            cancelButtonText: t("glob.Cancel"),
            confirmButtonText: t("glob.delete"),
            distinguishCancelAndClose: true,
            draggable: true,
            async beforeClose(action, instance, done) {
              if (action === "confirm") {
                instance.confirmButtonLoading = true;
                try {
                  if (typeof callback === "function") option.valid = await callback(await getForm(params));
                  if (!option.valid) throw new Error("Error");
                  resolve(params);
                  done();
                } catch (error) {
                  option.message = "";
                  option.valid = false;
                  instance.showConfirmButton = false;
                  instance.type = "error";
                } finally {
                  instance.confirmButtonLoading = false;
                }
              } else {
                reject(params);
                done();
              }
            },
          })
            .then(async () => {})
            .catch(() => {
              reject(params);
            });
        });
      }
    }
  },
});
</script>

<style scoped lang="scss"></style>
