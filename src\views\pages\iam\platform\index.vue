<template>
  <el-container>
    <el-main :key="`active_view-${type}`" class="view-main" :style="{ height: `${height}px`, display: 'flex' }">
      <!-- 左侧 -->
      <el-card :style="{ width: '200px', marginRight: '4px', border: '0', borderTopLeftRadius: '0', borderTopRightRadius: '0', borderBottomRightRadius: '0' }" :body-style="{ padding: '0px' }">
        <template #header>
          <div class="main-header">
            <div class="left">平台</div>
            <div class="center"></div>
            <div class="right">
              <el-tooltip :content="`${$t('glob.refresh')}终端列表`" placement="top">
                <el-link v-blur :underline="false" type="info" :icon="Refresh" @click="state.refresh()"></el-link>
              </el-tooltip>
              <el-tooltip :content="`${$t('glob.add')}终端`" placement="top">
                <el-link v-blur :underline="false" type="primary" :icon="Plus" @click="state.create({})"></el-link>
              </el-tooltip>
            </div>
          </div>
        </template>
        <template #default>
          <el-scrollbar v-loading="state.loading" :height="height - 91" :view-style="{ padding: '6px 3px' }">
            <ul class="main-list">
              <li v-for="row in state.data" :key="row.code" :class="['main-item', { active: isEqual(row.code, active) }]" @click="active = row.code">
                <el-avatar :icon="Monitor"></el-avatar>
                <el-tooltip :show-after="1000" :disabled="!row.note">
                  <template #content>
                    <pre>{{ row.note }}</pre>
                  </template>
                  <template #default>
                    <div class="main-item-body" style="flex-shrink: 1">
                      <div class="main-item-title">{{ row.name }}</div>
                      <div class="main-item-code">{{ row.code }}</div>
                    </div>
                  </template>
                </el-tooltip>
                <div class="main-item-btn-group" @click.stop>
                  <el-tooltip :content="$t('glob.edit')">
                    <el-link :underline="false" type="primary" :icon="Edit" @click="state.editor(row)"></el-link>
                  </el-tooltip>
                  <el-tooltip :content="$t('glob.delete')">
                    <el-link :underline="false" type="danger" :icon="Delete" @click="state.delete(row)"></el-link>
                  </el-tooltip>
                </div>
              </li>
            </ul>
          </el-scrollbar>
        </template>
      </el-card>
      <!-- 右侧 -->
      <el-container>
        <el-header class="el-card" height="fit-content" :style="{ marginBottom: '4px', padding: '0', border: '0', borderBottomLeftRadius: '0', borderBottomRightRadius: '0' }">
          <el-menu :key="`active_type-${type}`" mode="horizontal" class="tab-menu" :default-active="type" style="width: 100%; --el-menu-item-height: 42px">
            <el-menu-item index="" @click="type = ($event.index as MenuType) || ''">
              <template #default>
                <el-icon>
                  <Tickets />
                </el-icon>
              </template>
              <template #title>平台详情</template>
            </el-menu-item>
            <el-menu-item index="auth" @click="type = ($event.index as MenuType) || ''">
              <template #default>
                <el-icon>
                  <Iphone />
                </el-icon>
              </template>
              <template #title>授权端</template>
            </el-menu-item>
            <el-menu-item index="tenant" @click="type = ($event.index as MenuType) || ''" :disabled="!currentActive?.multiTenant">
              <template #default>
                <el-icon>
                  <Postcard />
                </el-icon>
              </template>
              <template #title>租户</template>
            </el-menu-item>
            <el-menu-item index="user" @click="type = ($event.index as MenuType) || ''">
              <template #default>
                <el-icon>
                  <User />
                </el-icon>
              </template>
              <template #title>用户</template>
            </el-menu-item>
            <el-menu-item index="power" @click="type = ($event.index as MenuType) || ''">
              <template #default>
                <el-icon>
                  <Grid />
                </el-icon>
              </template>
              <template #title>应用管理</template>
            </el-menu-item>
            <!-- <el-menu-item index="third" @click="type = ($event.index as MenuType) || ''">
              <template #default>
                <el-icon>
                  <Pointer />
                </el-icon>
              </template>
              <template #title>第三方配置</template>
            </el-menu-item> -->
          </el-menu>
        </el-header>
        <el-card v-loading="state.loading" :style="{ width: `${width - 204}px`, border: '0', borderTopLeftRadius: '0', borderTopRightRadius: '0', borderBottomLeftRadius: '0' }" :body-style="{ padding: '0px' }">
          <template v-if="!currentActive">
            <el-scrollbar>
              <div class="flex-search" :style="{ minWidth: `${width - 266}px` }">
                <div class="left">
                  <el-button type="primary" @click="open({ multiple: false, accept: 'application/json', reset: true })"> {{ $t("glob.Import") }}平台 </el-button>
                </div>
                <div class="center"></div>
                <div class="right"></div>
              </div>
            </el-scrollbar>
            <el-empty description="未找到相关信息" />
          </template>
          <template v-else-if="type === ''">
            <el-scrollbar>
              <div class="flex-search" :style="{ minWidth: `${width - 206}px` }">
                <div class="left">
                  <el-button type="primary" @click="open({ multiple: false, accept: 'application/json', reset: true })"> {{ $t("glob.Import") }}平台 </el-button>
                  <el-button type="primary" @click="exportPlatform({ code: currentActive.code })"> {{ $t("glob.Export") }}平台 </el-button>

                  <el-tooltip :content="$t('glob.refresh')" placement="top">
                    <el-button v-blur color="#40485b" class="table-header-operate" type="info" :icon="Refresh" @click="state.refresh()"></el-button>
                  </el-tooltip>
                </div>
                <div class="center"></div>
                <div class="right"></div>
              </div>
            </el-scrollbar>
            <DetailPlatform key="active_platform" :data="currentActive" :width="width - 204" :height="height - 48" @patch="($event) => modItem({ ...$event, code: currentActive?.code }).then(() => state.refresh())"> </DetailPlatform>
          </template>
          <template v-else-if="type === 'auth'">
            <DetailAuth key="active_auth" :data="currentActive" :width="width - 204" :height="height - 48">
              <template #left>
                <el-button type="primary" @click="open({ multiple: false, accept: 'application/json', reset: true })"> {{ $t("glob.Import") }}平台 </el-button>
                <el-button type="primary" @click="exportPlatform({ code: currentActive.code })"> {{ $t("glob.Export") }}平台 </el-button>
              </template>
              <template #right="{ create }">
                <el-button type="primary" :icon="Plus" @click="create({ platform: currentActive?.code })"> {{ $t("glob.add") }}授权端 </el-button>
              </template>
            </DetailAuth>
          </template>
          <template v-else-if="type === 'tenant'">
            <DetailTenant v-if="currentActive.multiTenant" key="active_tenant" :data="currentActive" :width="width - 204" :height="height - 48">
              <template #left>
                <el-button type="primary" @click="open({ multiple: false, accept: 'application/json', reset: true })"> {{ $t("glob.Import") }}平台 </el-button>
                <el-button type="primary" @click="exportPlatform({ code: currentActive.code })"> {{ $t("glob.Export") }}平台 </el-button>
              </template>
              <template #right="{ create }">
                <el-button type="primary" :icon="Plus" @click="create({ platform: currentActive?.code })"> {{ $t("glob.add") }}租户 </el-button>
              </template>
            </DetailTenant>
            <template v-else>
              <el-scrollbar>
                <div class="flex-search" :style="{ minWidth: `${width - 266}px` }">
                  <div class="left">
                    <el-button type="primary" @click="open({ multiple: false, accept: 'application/json', reset: true })"> {{ $t("glob.Import") }}平台 </el-button>
                    <el-button type="primary" @click="exportPlatform({ code: currentActive.code })"> {{ $t("glob.Export") }}平台 </el-button>
                  </div>
                  <div class="center"></div>
                  <div class="right"></div>
                </div>
              </el-scrollbar>
              <el-empty description="非多租户平台" />
            </template>
          </template>
          <template v-else-if="type === 'user'">
            <DetailUser key="active_user" :data="currentActive" :width="width - 204" :height="height - 48">
              <template #left>
                <el-button type="primary" @click="open({ multiple: false, accept: 'application/json', reset: true })"> {{ $t("glob.Import") }}平台 </el-button>
                <el-button type="primary" @click="exportPlatform({ code: currentActive.code })"> {{ $t("glob.Export") }}平台 </el-button>
              </template>
              <template #right="{ create }">
                <el-button type="primary" :icon="Plus" @click="create({ platform: currentActive?.code })"> {{ $t("glob.add") }}用户 </el-button>
              </template>
            </DetailUser>
          </template>
          <template v-else-if="type === 'power'">
            <DetailPower key="active_power" :data="currentActive" :width="width - 204" :height="height - 48">
              <template #left="{ refresh }">
                <el-button type="primary" @click="open({ multiple: false, accept: 'application/json', reset: true })"> {{ $t("glob.Import") }}平台 </el-button>
                <el-button type="primary" @click="exportPlatform({ code: currentActive.code })"> {{ $t("glob.Export") }}平台 </el-button>
                <el-tooltip :content="$t('glob.refresh')" placement="top">
                  <el-button v-blur color="#40485b" class="table-header-operate" type="info" :icon="Refresh" @click="refresh()"></el-button>
                </el-tooltip>
              </template>
            </DetailPower>
          </template>
          <template v-else-if="type === 'third'">
            <DetailThirdParty key="active_third" :data="currentActive" :width="width - 204" :height="height - 48">
              <template #left="{ refresh }">
                <el-button type="primary" @click="open({ multiple: false, accept: 'application/json', reset: true })"> {{ $t("glob.Import") }}平台 </el-button>
                <el-button type="primary" @click="exportPlatform({ code: currentActive.code })"> {{ $t("glob.Export") }}平台 </el-button>
                <el-tooltip :content="$t('glob.refresh')" placement="top">
                  <el-button v-blur color="#40485b" class="table-header-operate" type="info" :icon="Refresh" @click="refresh()"></el-button>
                </el-tooltip>
              </template>
            </DetailThirdParty>
          </template>
        </el-card>
      </el-container>
    </el-main>
    <EditorForm ref="editorRef" title="平台" />
  </el-container>
</template>

<script lang="ts" setup name="iam/platform">
/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */
import { ref, reactive, computed, nextTick, watch, inject } from "vue";
import { useRouter, useRoute } from "vue-router";
// Utils
import { useFileDialog } from "@vueuse/core";
import { cloneDeep, isEqual, isNil } from "lodash-es";
import { sizes } from "@/utils/common";
// UI
import { ElMessage } from "element-plus";
import { Refresh, Plus, Edit, Delete, Tickets, Iphone, Grid, Postcard, User, Monitor, Pointer } from "@element-plus/icons-vue";
// Api
import { getPlatform as getItem, addPlatform as addItem, modPlatform as modItem, delPlatform as delItem, getUserById, exportPlatform, importPlatform } from "@/api/iam";
import type { PlatformItem as DataItem, UserBaseItem } from "@/api/iam";
// 组件
import DetailAuth from "./model/DetailAuth.vue";
import DetailPlatform from "./model/DetailPlatform.vue";
import DetailTenant from "./model/DetailTenant.vue";
import DetailUser from "./model/DetailUser.vue";
import DetailPower from "./model/DetailPower.vue";
import DetailThirdParty from "./model/DetailThirdParty.vue";
// 编辑器
import EditorForm from "./Editor.vue";
import { editorType, EditorType } from "@/views/common/interface";

const editorRef = ref<InstanceType<typeof EditorForm>>();

const width = inject<import("vue").Ref<number>>("width", ref(0));
const height = inject<import("vue").Ref<number>>("height", ref(0));

const route = useRoute();
const router = useRouter();
type MenuType = "" | "auth" | "tenant" | "user" | "power" | "third";
const { open, onChange } = useFileDialog({ reset: true });
onChange(async (files) => {
  if (files) {
    const file = files.item(0);
    if (file instanceof File) {
      try {
        const { success, message, data } = await importPlatform({ file });
        if (!success) throw Object.assign(new Error(message), { data, success });
        ElMessage.success("成功上传！");
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      }
    }
  }
});

// 数据
interface StateData<T, K> {
  loading: boolean;
  search: Record<string, unknown>;
  data: T[];
  page: number;
  size: number;
  total: number;
  sizes: typeof sizes;
  // active: import("vue").WritableComputedRef<T[K]>;
  // type: import("vue").WritableComputedRef<MenuType>;
  create: (params: Partial<T>, onCleanup?: (cleanupFn: () => void) => void) => Promise<void>;
  editor: (params: Partial<T>, onCleanup?: (cleanupFn: () => void) => void) => Promise<void>;
  delete: (params: Partial<T>, onCleanup?: (cleanupFn: () => void) => void) => Promise<void>;
  source: (params: Partial<T>, onCleanup?: (cleanupFn: () => void) => void) => Promise<void>;
  refresh: () => void;
}

const active = computed({
  get: () => (route.query.platform as string) || "",
  set: (v: string) => router.push({ query: { ...route.query, platform: v } }),
});
const type = computed({
  get: () => (route.query.type as MenuType) || "",
  set: (v: MenuType) => router.push({ query: { ...route.query, type: v } }),
});

const state = reactive<StateData<DataItem, "code">>({
  loading: false,
  search: {},
  data: [],
  page: 1,
  size: 20,
  total: 0,
  sizes,
  // active: computed({
  //   get: () => (route.query.platform as string) || "",
  //   set: (v: string) => router.push({ query: { ...route.query, platform: v } }),
  // }),
  // type: computed({
  //   get: () => (route.query.type as MenuType) || "",
  //   set: (v: MenuType) => router.push({ query: { ...route.query, type: v } }),
  // }),
  async create(params, onCleanup) {
    await nextTick();
    if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
    if (!editorRef.value) return;
    try {
      await editorRef.value.open({ ...params, "#TYPE": EditorType.Add }, async (req) => {
        const { success, message, data } = await addItem(req);
        if (!success) throw Object.assign(new Error(message), { success, data });
        ElMessage.success(`${editorType[EditorType.Add]}成功！`);
      });
    } catch (error) {
      /*  */
    }
    await state.refresh();
  },
  async editor(params, onCleanup) {
    await nextTick();
    if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
    if (!editorRef.value) return;
    try {
      const owner: UserBaseItem = { name: "", account: "", phone: "", email: "", language: "none", gender: "SECRET", password: "" };

      await editorRef.value.open({ ...params, owner, "#TYPE": EditorType.Mod }, async (req) => {
        const { success, message, data } = await modItem(req);
        if (!success) throw Object.assign(new Error(message), { success, data });
        ElMessage.success(`${editorType[EditorType.Mod]}成功！`);
      });
    } catch (error) {
      /*  */
    }
    await state.refresh();
  },
  async delete(params, onCleanup) {
    await nextTick();
    if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
    if (!editorRef.value) return;
    try {
      await editorRef.value.open({ ...params, "#TYPE": EditorType.Del }, async (req) => {
        const { success, message, data } = await delItem(req);
        if (!success) throw Object.assign(new Error(message), { success, data });
        ElMessage.success(`${editorType[EditorType.Del]}成功！`);
      });
    } catch (error) {
      /*  */
    }
    await state.refresh();
  },
  async source(params, onCleanup) {
    const controller = new AbortController();
    const response = getItem({ ...params, paging: { pageNumber: state.page, pageSize: state.size }, controller });
    if (typeof onCleanup === "function") onCleanup(() => controller.abort());
    try {
      if (state.loading) return;
      state.loading = true;
      await nextTick();
      const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await response;
      if (success) {
        state.page = Number(page);
        state.size = Number(size);
        state.total = Number(total);
        state.data.splice(0, state.data.length, ...(data instanceof Array ? data : []));
      } else throw Object.assign(new Error(message), { success, data });
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      state.total = Number(0);
      state.data.splice(0, state.data.length);
    } finally {
      await nextTick();
      if (!state.data.map(({ code }) => code).includes(active.value)) active.value = (state.data[0] || {}).code || "";
      state.loading = false;
    }
  },
  refresh() {
    state.source(Object.entries(state.search).reduce<Record<string, unknown>>((p, [key, value]) => ({ ...p, ...(isNil(value) ? {} : { [key]: value }) }), {}));
  },
});

if (!["", "auth", "tenant", "user", "power", "third"].includes(type.value)) type.value = "";

// watch(
//   () => state.search,
//   function (search) {
//     state.source(Object.entries(search).reduce<Record<string, unknown>>((p, [key, value]) => ({ ...p, ...(isNil(value) ? {} : { [key]: value }) }), {}));
//   },
//   { immediate: true, flush: "post" }
// );

state.refresh();

const currentActive = computed(() => state.data.find((item) => item.code === active.value));
</script>

<style lang="scss" scoped>
.default-alert {
  position: absolute;
  top: -14px;
  z-index: 1;
}

.tab-pane-label {
  display: inline-block;
  line-height: 1;
  // width: 120px;
  width: fit-content;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.content {
  height: calc(100vh - 70px - (var(--ba-main-space, 0px) * 2));
}

.menu-header {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;

  .title {
    flex-shrink: 0;
  }

  .btn-group {
    width: 100%;
    flex-shrink: 1;
  }
}

.card-header {
  background: var(--ba-bg-color-overlay);
  margin: 16px 0 16px 16px;
  height: calc(100vh - 32px);
  box-shadow: var(--el-box-shadow-light);
  border-radius: var(--el-border-radius-base);
  overflow: hidden;
  transition: width 0.3s ease;
  width: var(--8aea564f-menuWidth);
}

.view-main {
  padding: 0px;
  overflow: visible;
}

.root-item-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &::after {
    content: attr(title);
    font-size: 12px;
  }
}

.main-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  line-height: 18px;

  > :deep(.left),
  > :deep(.center),
  > :deep(.right) {
    > * {
      &:not(:last-child) {
        margin-right: 4px;
      }

      &:not(:first-child) {
        margin-left: 4px;
      }
    }
  }

  .center {
    margin: 0 8px;
  }
}

.main-list {
  margin: 0;
  padding: 0;

  .main-item {
    display: flex;
    padding: 4px 6px;
    border-color: var(--el-border-color);
    border-style: solid;
    border-width: 0px;
    cursor: pointer;

    &:not(:last-child) {
      // margin-bottom: 3px;
      border-bottom-width: 1px;
    }

    // &:not(:first-child) {
    //   margin-top: 3px;
    // }
    &.active,
    &:hover {
      background-color: var(--el-fill-color-light);
    }

    &.active {
      color: var(--el-color-primary);

      .main-item-code {
        color: var(--el-text-color-primary);
      }
    }

    > * {
      flex-shrink: 0;
    }

    .main-item-body {
      display: flex;
      flex-direction: column;
      width: 100%;
    }

    .main-item-code,
    .main-item-title {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .main-item-title {
      display: inline-block;
      max-width: 100px;
      margin-left: 6px;
      font-weight: bold;
      flex-shrink: 1;
      height: 100%;
    }

    .main-item-code {
      display: inline-block;
      max-width: 100px;
      margin-left: 6px;
      font-size: 12px;
      flex-shrink: 0;
      color: var(--el-text-color-placeholder);
    }

    .main-item-btn-group {
      > * {
        &:not(:last-child) {
          margin-right: 3px;
        }

        &:not(:first-child) {
          margin-left: 3px;
        }
      }
    }
  }
}
</style>
