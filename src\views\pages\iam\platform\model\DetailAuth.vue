<template>
  <el-scrollbar>
    <div class="flex-search" :style="{ minWidth: `${props.width - 2}px` }">
      <div class="left"><slot name="left" :create="handleStateCreate" :delete="handleStateDelete" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot></div>
      <div class="center"><slot name="center" :create="handleStateCreate" :delete="handleStateDelete" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot></div>
      <div class="right"><slot name="right" :create="handleStateCreate" :delete="handleStateDelete" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot></div>
    </div>
  </el-scrollbar>
  <el-table v-loading="state.loading" :data="state.data" :height="props.height - 64 - 20 - (state.total ? 32 : 0)" :style="{ width: `${props.width - 40}px`, margin: '0 auto' }">
    <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :min-width="column.width || 120" :="typeof column.showOverflowTooltip === 'boolean' ? column.showOverflowTooltip : true" :formatter="column.formatter" />
    <el-table-column :label="t('glob.operate')" align="left" header-align="left" :width="110" fixed="right">
      <template #header="{ column }">
        <div style="display: flex; justify-content: center">
          <span style="margin: 0 10px">{{ column.label }}</span>
          <el-link type="primary" :underline="false" :icon="Refresh" @click="handleStateRefresh()"></el-link>
        </div>
      </template>
      <template #default="{ row }">
        <!-- <el-button link type="default" :icon="View" @click="editorRef?.open({ '#TYPE': EditorType.Cat, ...row })">{{ t("glob.Cat") }}</el-button> -->
        <el-button link type="primary" :icon="Edit" :disabled="false" @click="handleStateEditor(row)" :title="t('glob.edit')"></el-button>
        <el-button link type="danger" :icon="Delete" :disabled="false" @click="handleStateDelete(row)" :title="t('glob.delete')"></el-button>
        <!-- <el-dropdown @command="$event.callback(row)" class="el-button el-button--default is-link" style="vertical-align: middle">
          <span style="font-size: var(--el-font-size-base)">
            <el-icon :title="t('glob.More')"><More /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item :command="{ callback: () => {} }" :disabled="false">未开发...</el-dropdown-item>
              <el-dropdown-item :command="{ callback: () => {} }" :disabled="false">未开发...</el-dropdown-item>
              <el-dropdown-item :command="{ callback: () => {} }" :disabled="false">未开发...</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown> -->
      </template>
    </el-table-column>
  </el-table>
  <div :style="{ margin: '8px 20px 20px' }">
    <el-pagination v-show="state.total" v-model:current-page="state.page" v-model:page-size="state.size" :page-sizes="state.sizes" :total="state.total" layout="->, total, sizes, prev, pager, next, jumper" small @size-change="handleStateRefresh()" @current-change="handleStateRefresh()" />
  </div>
  <EditorDetailsByClient ref="editorRef" title="客户端"></EditorDetailsByClient>
</template>

<script setup lang="ts" name="PlatformDetailClient">
import { ref, reactive, onMounted, nextTick, watch, h } from "vue";
import { useI18n } from "vue-i18n";
import { sizes } from "@/utils/common";
import { useClipboard } from "@vueuse/core";
import _ from "lodash-es";

// Ui
import { ElMessage, ElButton, ElTag } from "element-plus";
import { Refresh, Edit, Delete } from "@element-plus/icons-vue";

// Api
import { getAuth as getItem, addAuth as addItem, modAuth as modItem, delAuth as delItem, terminalTypeOption } from "@/api/iam";
import type { PlatformItem, AuthItem } from "@/api/iam";

// Editor
import { EditorType } from "@/views/common/interface";
import EditorDetailsByClient from "./EditorDetailsByAuth.vue";

const { copy, copied, isSupported } = useClipboard({ read: false, legacy: true });

const editorRef = ref<InstanceType<typeof EditorDetailsByClient>>();
async function createItem(params: Partial<AuthItem>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, "#TYPE": EditorType.Add }, async (req) => {
      try {
        const { success, message, data } = await addItem(req);
        if (success) {
          ElMessage.success(`添加成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
async function editorItem(params: Partial<AuthItem>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, "#TYPE": EditorType.Mod }, async (req) => {
      try {
        const { success, message, data } = await modItem(req);
        if (success) {
          ElMessage.success(`编辑成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
async function deleteItem(params: Partial<AuthItem>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, "#TYPE": EditorType.Del }, async (req) => {
      try {
        const { success, message, data } = await delItem(req);
        if (success) {
          ElMessage.success(`删除成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  let controller = new AbortController();
  const response = getItem({ platform: params.platform as string, controller });
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await response;
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);
      return data instanceof Array ? data : [];
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(20);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}

/*********************************************************/

const { t } = useI18n();

interface Props {
  data: Pick<PlatformItem, "code" | "name" | "note" | "multiTenant"> & Record<string, unknown>;
  width: number;
  height: number;
}
const props = withDefaults(defineProps<Props>(), {
  data: () => ({
    code: "",
    name: "",
    note: "",
    multiTenant: false,
  }),
  width: 100,
  height: 300,
});

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; showOverflowTooltip?: boolean; formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<AuthItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {
    keyword: "",
  },
  column: [],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

onMounted(async () => {
  await nextTick();
  state.column = [
    /* 表格列 */
    // { key: "id", label: "客户端 ID" },
    { key: "name", label: "名称" },
    {
      key: "token",
      label: "BasicToken",
      width: 140,
      showOverflowTooltip: false,
      formatter: (_row, _col, v) => {
        async function onClick(value: string) {
          if (!isSupported.value) ElMessage.error("浏览器不支持");
          try {
            await copy(value);
            ElMessage.success("复制成功");
          } catch (error: unknown) {
            if (error instanceof Error) ElMessage.error(error.message);
          }
        }
        // return h(ElButton, { size: "small", class: ["copy"], onClick: () => onClick(v as string) }, () => h("span", { style: { maxWidth: "80px", display: "" }, class: ["no-wrap"] }, copied.value ? t("glob.Copied") : t("glob.Copy")));
        return h(ElButton, { size: "small", class: ["overflow-ellipsis", "overflow-hidden", "border-dashed"], onClick: () => onClick(v as string) }, () => (isSupported.value ? h("span", { style: { maxWidth: "80px", display: "" }, class: ["no-wrap"] }, copied.value ? t("glob.Copied") : t("glob.Copy")) : h("span", { style: { maxWidth: "80px", display: "" }, class: ["no-wrap"] }, v)));
      },
    },
    { key: "terminal", label: "终端类型", formatter: (_row, _col, v) => h(ElTag, { effect: "dark" }, () => _.find(terminalTypeOption, ({ value }) => value === v)?.label || "未知") },
    { key: "accessTokenValidity", label: "AccessToken", formatter: (_row, _col, v) => h(ElTag, { effect: "dark" }, () => `${v || 0}秒`) },
    { key: "refreshTokenValidity", label: "RefreshToken", formatter: (_row, _col, v) => h(ElTag, { effect: "dark" }, () => `${v || 0}秒`) },
    { key: "version", label: "锁版本", formatter: (_row, _col, v) => h(ElTag, { effect: "dark" }, () => v) },
    // { key: "accessTokenAutoRenewal", label: "自动续期", formatter: (_row, _col, v) => (v ? h(ElTag, { type: "success", effect: "dark" }, () => t("glob.Enable")) : h(ElTag, { type: "info", effect: "dark" }, () => t("glob.Disable"))) },
    { key: "note", label: "描述信息" },
    // { key: "createdTime", label: "创建日期", formatter: (_row, _col, v) => formatterDate(v as string) },
    // { key: "updatedTime", label: "修改日期", formatter: (_row, _col, v) => formatterDate(v as string) },
  ];
});

watch<string, true>(
  () => props.data.code,
  async function () {
    if (state.loading) return;
    handleStateRefresh();
  },
  { immediate: true, flush: "post" }
);

async function handleStateCreate(params: Partial<AuthItem>) {
  await createItem(params);
  await handleStateRefresh();
}
async function handleStateEditor(params: Partial<AuthItem>) {
  await editorItem(params);
  await handleStateRefresh();
}
async function handleStateDelete(params: Partial<AuthItem>) {
  await deleteItem(params);
  await handleStateRefresh();
}
async function handleStateRefresh() {
  if (state.loading) return;
  state.loading = true;
  state.data.splice(0, state.data.length, ...(await querysItem({ platform: props.data.code })));
  state.loading = false;
}
defineSlots<{
  left(props: { create: typeof handleStateCreate; delete: typeof handleStateDelete; editor: typeof handleStateEditor; refresh: typeof handleStateRefresh }): any;
  center(props: { create: typeof handleStateCreate; delete: typeof handleStateDelete; editor: typeof handleStateEditor; refresh: typeof handleStateRefresh }): any;
  right(props: { create: typeof handleStateCreate; delete: typeof handleStateDelete; editor: typeof handleStateEditor; refresh: typeof handleStateRefresh }): any;
}>();
</script>

<style lang="scss" scoped></style>
