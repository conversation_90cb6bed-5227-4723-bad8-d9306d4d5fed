<template>
  <el-scrollbar :height="props.height - 64" :view-style="{ padding: '0 20px 20px 20px' }">
    <el-descriptions :title="item.name" :column="3" :border="true" class="tw-mb-[12px]">
      <el-descriptions-item label="平台编码">{{ item.code }}</el-descriptions-item>
      <el-descriptions-item label="平台名称">
        <template v-if="!(form.name || {}).editor">
          {{ item.name }}
          <el-link type="primary" :underline="false" :icon="Edit" @click="form.name = { value: item.name, editor: true }"></el-link>
        </template>
        <el-input v-else v-model="form.name.value" size="small">
          <template #suffix>
            <el-link type="primary" :underline="false" :icon="Select" @click="() => patch({ name: form.name.value })"></el-link>
          </template>
        </el-input>
      </el-descriptions-item>
      <el-descriptions-item label="平台描述">
        <template v-if="!(form.note || {}).editor">
          <pre style="display: inline">{{ item.note || "--" }}</pre>
          <el-link type="primary" :underline="false" :icon="Edit" @click="form.note = { value: item.note, editor: true }"></el-link>
        </template>
        <el-input v-else v-model="form.note.value" size="small">
          <template #suffix>
            <el-link type="primary" :underline="false" :icon="Select" @click="() => patch({ note: form.note.value })"></el-link>
          </template>
        </el-input>
      </el-descriptions-item>
      <el-descriptions-item :span="3" label="平台登录地址">{{ item.loginPage }}</el-descriptions-item>
      <el-descriptions-item label="多租户">
        <el-tag size="small" effect="dark" :type="item.multiTenant ? 'success' : 'info'">{{ item.multiTenant ? $t("glob.Enable") : $t("glob.Disable") }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        <el-tag type="" size="small" effect="dark">{{ moment(item.createdTime, "x").format("YYYY-MM-DD HH:mm:ss") }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="修改时间">
        <el-tag type="warning" size="small" effect="dark">{{ moment(item.updatedTime, "x").format("YYYY-MM-DD HH:mm:ss") }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="平台所属人">
        {{ owner.name || owner.nickname || "--" }}
        <el-tag type="info" size="small" effect="dark">{{ owner.account || "--" }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="所属人手机号">{{ owner.phone || "--" }}</el-descriptions-item>
      <el-descriptions-item label="所属人邮箱">{{ owner.email || "--" }}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions v-if="item.securityConfig" title="平台安全配置" :column="3" class="tw-mb-[12px]">
      <el-descriptions-item label="多因素登录">
        <el-switch :model-value="item.securityConfig.enableMfa" :active-text="$t('glob.Enable')" :active-value="true" :inactive-text="$t('glob.Disable')" :inactive-value="false" @change="($event) => patch({ securityConfig: { ...item.securityConfig, enableMfa: $event as boolean } })"></el-switch>
      </el-descriptions-item>
      <el-descriptions-item :span="2" label="重复登录">
        <el-switch :model-value="item.securityConfig.repeatLogin" :active-text="$t('glob.Enable')" :active-value="true" :inactive-text="$t('glob.Disable')" :inactive-value="false" @change="($event) => patch({ securityConfig: { ...item.securityConfig, repeatLogin: $event as boolean } })"></el-switch>
      </el-descriptions-item>
      <el-descriptions-item label="登录失败次数限制">
        <EditTemplate @submit="patch({ securityConfig: item.securityConfig })">
          <template #default>
            {{ item.securityConfig.loginFailureLimit ? `${item.securityConfig.loginFailureLimit}次` : `不限制` }}
          </template>
          <template #editor>
            <el-input-number v-model="item.securityConfig.loginFailureLimit" size="small" :min="0" :max="Number.MAX_SAFE_INTEGER" :step="1" :precision="0" :step-strictly="true" :controls="false"></el-input-number>
            天
          </template>
        </EditTemplate>
      </el-descriptions-item>
      <el-descriptions-item label="密码复用次数">
        <EditTemplate @submit="patch({ securityConfig: item.securityConfig })">
          <template #default>
            {{ item.securityConfig.histPasswordLimit ? `${item.securityConfig.histPasswordLimit}次` : `不限制` }}
          </template>
          <template #editor>
            <el-input-number v-model="item.securityConfig.histPasswordLimit" size="small" :min="0" :max="Number.MAX_SAFE_INTEGER" :step="1" :precision="0" :step-strictly="true" :controls="false"></el-input-number>
            天
          </template>
        </EditTemplate>
      </el-descriptions-item>
      <el-descriptions-item label="密码过期天数">
        <EditTemplate @submit="patch({ securityConfig: item.securityConfig })">
          <template #default>
            {{ item.securityConfig.passwordExpireDays ? `${item.securityConfig.passwordExpireDays}天` : `不过期` }}
          </template>
          <template #editor>
            <el-input-number v-model="item.securityConfig.passwordExpireDays" size="small" :min="0" :max="Number.MAX_SAFE_INTEGER" :step="1" :precision="0" :step-strictly="true" :controls="false"></el-input-number>
            天
          </template>
        </EditTemplate>
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions title="平台设置" :column="3" class="tw-mb-[12px]">
      <el-descriptions-item label="权限校验">
        <el-switch :model-value="item.enablePermissionCheck" :active-text="$t('glob.Enable')" :active-value="true" :inactive-text="$t('glob.Disable')" :inactive-value="false" @change="($event) => patch({ enablePermissionCheck: $event as boolean })"></el-switch>
      </el-descriptions-item>
      <el-descriptions-item label="用户注册">
        <el-switch :model-value="item.registrable" :active-text="$t('glob.Enable')" :active-value="true" :inactive-text="$t('glob.Disable')" :inactive-value="false" @change="($event) => patch({ registrable: $event as boolean })"></el-switch>
      </el-descriptions-item>
      <el-descriptions-item label="信息脱敏">
        <el-switch :model-value="item.desensitize" :active-text="$t('glob.Enable')" :active-value="true" :inactive-text="$t('glob.Disable')" :inactive-value="false" @change="($event) => patch({ desensitize: $event as boolean })"></el-switch>
      </el-descriptions-item>
      <el-descriptions-item label="初始密码">{{ item.initialUserPassword || "自动" }}</el-descriptions-item>
    </el-descriptions>
  </el-scrollbar>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */
import { ref, nextTick, computed, watch, createVNode, renderSlot } from "vue";
import { cloneDeep } from "lodash-es";
import moment from "moment";
import { Edit, Select } from "@element-plus/icons-vue";

// Api
import { getUserById } from "@/api/iam";
import type { PlatformItem } from "@/api/iam";
import { ElLink } from "element-plus";

interface Props {
  data: PlatformItem;
  width: number;
  height: number;
}
const props = withDefaults(defineProps<Props>(), {
  data: () => ({
    code: "" /* 平台编码 */,
    name: "" /* 平台名称 */,
    openName: "" /* 平台对外显示名称 */,
    note: "" /* 平台备注 */,
    multiTenant: false /* 是否多租户平台, 默认否 */,
    securityConfig: /* 安全配置 */ {
      enableMfa: false /* 是否开启多因素登录, 默认否 */,
      repeatLogin: false /* 是否允许重复登录, 默认否 */,
      loginFailureLimit: 0 /* 登录失败次数限制, 大于0时生效, 连续多次登录失败则暂时冻结账号, 默认不限制 */,
      histPasswordLimit: 0 /* 最近几次使用过的密码不能重复使用, 大于0生效, 默认不限制 */,
      passwordExpireDays: 0 /* 密码过期天数, 大于0生效, 默认不过期 */,
    },
    enablePermissionCheck: false /* 是否启用权限校验, 默认是 */,
    registrable: false /* 是否允许用户注册 */,
    desensitize: false /* 关键信息是否脱敏 */,
    initialUserPassword: "" /* 平台用户的初始密码, 默认无 */,
    config: "{}" /* 配置信息JSON字符串 */,
    ownerId: "" /* 平台拥有人用户 */,
    owner: /* 平台拥有人信息, 可选 */ {
      platform: "",
      keyword: "",
      id: "",
      name: "" /* 姓名 */,
      nickname: "" /* 昵称 */,
      account: "" /* 账号 */,
      phone: "" /* 手机号码 */,
      email: "" /* 邮箱 */,
      language: "none" /* 语言 */,
      gender: "SECRET" /* 性别 */,
      password: "" /* 密码 */,
    },
    createdTime: "",
    updatedTime: "",
  }),
  width: 100,
  height: 300,
});

interface Emits {
  (e: "patch", data: Partial<PlatformItem>): void;
}
const emits = defineEmits<Emits>();

function patch(data: Partial<PlatformItem>) {
  (Object.keys(data) as (keyof PlatformItem)[]).forEach((key) => {
    if (Object.prototype.hasOwnProperty.call(form.value, key)) (form.value[key] || { editor: false }).editor = false;
  });
  emits("patch", data);
}

const EditTemplate = createVNode({
  emits: ["submit"],
  slots: {},
  setup(props, ctx) {
    const editor = ref(false);
    return () => {
      if (!editor.value) return [renderSlot(ctx.slots, "default", {}), createVNode(ElLink, { type: "primary", underline: false, icon: Edit, style: { marginLeft: "12px" }, onClick: () => (editor.value = true) })];
      else return [renderSlot(ctx.slots, "editor", {}), createVNode(ElLink, { type: "primary", underline: false, icon: Select, style: { marginLeft: "12px" }, onClick: () => ((editor.value = false), ctx.emit("submit")) })];
    };
  },
});

const item = computed<PlatformItem>(() => props.data);

type DefaultForm<T> = { [P in keyof T]: { value: T[P]; editor: boolean } };
const form = ref<DefaultForm<PlatformItem>>(cloneDeep(Object.entries(item.value).reduce<DefaultForm<PlatformItem>>((p, [key, value]) => ({ ...p, [key]: { value, editor: false } }), {} as DefaultForm<PlatformItem>)));

const owner = ref<Required<PlatformItem>["owner"]>({ name: "", nickname: "", account: "", phone: "", email: "", language: "none", gender: "SECRET", password: "" });

watch(
  item,
  async (value, oldValue) => {
    form.value = cloneDeep(Object.entries(value).reduce<DefaultForm<PlatformItem>>((p, [key, value]) => ({ ...p, [key]: { value, editor: false } }), {} as DefaultForm<PlatformItem>));
    await nextTick();
    if (value.ownerId === oldValue?.ownerId) return;
    if (value.ownerId) {
      const { success, message, data } = await getUserById({ id: value.ownerId });
      if (success) {
        owner.value = data;
      } else throw Object.assign(new Error(message), { success, data });
    } else {
      owner.value = { name: "", nickname: "", account: "", phone: "", email: "", language: "none", gender: "SECRET", password: "" };
    }
  },
  { immediate: true, flush: "post" }
);
</script>

<style lang="scss" scoped>
.title-content {
  &::before {
    content: attr(title);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    max-width: 50px;
    vertical-align: middle;
  }
}
</style>
