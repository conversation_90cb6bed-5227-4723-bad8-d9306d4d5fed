<template>
  <el-scrollbar>
    <div class="flex-search" :style="{ minWidth: `${props.width - 2}px` }">
      <div class="left">
        <slot name="left" :refresh="handleStateRefresh"></slot>
      </div>
      <div class="center">
        <slot name="center" :refresh="handleStateRefresh"></slot>
      </div>
      <div class="right">
        <slot name="right" :refresh="handleStateRefresh"></slot>
      </div>
    </div>
  </el-scrollbar>
  <div class="tw-flex" v-loading="state.loading">
    <el-scrollbar :style="{ width: '360px' }" :component-data="{ type: 'APP' }" :height="height - 64"
      :view-style="{ padding: '20px', minWidth: '100%', width: 'fit-content' }">
      <draggable class="source-tree tw-min-h-[24px]" tag="ul" v-model="state.data" fallback-on-body handle=".move-item"
        :sort="false" :group="{ name: 'group-item', pull: 'clone', put: true }" @start="() => (moveLiading = true)"
        @end="() => (moveLiading = false)" item-key="id">
        <!--  :move="($event: MoveEvent) => ($event.relatedContext.element ? handleMove($event) : true)" -->
        <template #item="{ element, index }">
          <li class="tree-item tw-pl-[18px]" :class="[{ first: index === 0, final: index === state.data.length - 1 }]">
            <div class="move-item tw-ml-[-8px] tw-flex tw-leading-[24px]" draggable="true">
              <div class="tw-mr-[6px] tw-flex tw-flex-shrink-0 tw-items-center">
                <div class="checkbox-virtually">
                  <label class="toggle-button">
                    <input type="checkbox" v-model="element.enabled" style="display: none; visibility: hidden"
                      @change="($event: Event) => handleStateCutSelect($event, element)" />
                    <div class="virtually">
                      <svg viewBox="0 0 44 44">
                        <path
                          d="M14,24 L21,31 L39.7428882,11.5937758 C35.2809627,6.53125861 30.0333333,4 24,4 C12.95,4 4,12.95 4,24 C4,35.05 12.95,44 24,44 C35.05,44 44,35.05 44,24 C44,19.3 42.5809627,15.1645919 39.7428882,11.5937758"
                          transform="translate(-2.000000, -2.000000)"></path>
                      </svg>
                    </div>
                  </label>
                </div>
              </div>
              <div class="tw-overflow-hidden tw-text-ellipsis tw-whitespace-nowrap">
                <span class="tw-mr-[6px] tw-overflow-hidden tw-text-ellipsis tw-whitespace-nowrap">
                  {{ element.title }}
                </span>
                <span class="tw-overflow-hidden tw-text-ellipsis tw-whitespace-nowrap"
                  style="color: var(--el-text-color-disabled)">
                  {{ element.name }}
                </span>
              </div>
            </div>
            <div v-show="!moveLiading" v-if="element.children instanceof Array && element.children.length" class="subset">
              <NestedDraggable v-model="element.children"></NestedDraggable>
            </div>
          </li>
        </template>
      </draggable>
    </el-scrollbar>
    <el-scrollbar :style="{ width: 'calc(100% - 360px)' }" :height="height - 64"
      :view-style="{ padding: '20px', minWidth: '100%', width: 'fit-content' }">
      <div class="tw-flex tw-flex-wrap tw-justify-end">
        <!-- <el-button type="primary" @click="() => navTabs.updateNavigation()">{{ $t("glob.refresh") }}导航目录</el-button>
        <el-button type="primary" @click="() => handleStateCreate({})">{{ $t("glob.add") }}导航目录</el-button> -->
      </div>
      <draggable v-model="navigation" handle=".move-group" group="group" tag="div"
        class="tw-flex tw-flex-wrap tw-items-start tw-justify-start" item-key="id" @change="handleNavigationChange">
        <template #item="{ element: nav }">
          <div class="tw-m-[6px] tw-w-[260px] tw-overflow-hidden tw-p-[6px] tw-outline-dashed tw-outline-1" :key="nav.id">
            <div class="move-group tw-flex tw-items-center">
              <svg style="width: 1em; height: 1em; vertical-align: middle; fill: currentColor; overflow: hidden"
                viewBox="0 0 1024 1024">
                <path
                  d="M320 64a64 64 0 1 0 0 128 64 64 0 0 0 0-128zM704 64a64 64 0 1 0 0 128 64 64 0 0 0 0-128zM256 384a64 64 0 1 1 128 0 64 64 0 0 1-128 0zM704 320a64 64 0 1 0 0 128 64 64 0 0 0 0-128zM256 640a64 64 0 1 1 128 0 64 64 0 0 1-128 0zM320 832a64 64 0 1 0 0 128 64 64 0 0 0 0-128zM640 640a64 64 0 1 1 128 0 64 64 0 0 1-128 0zM704 832a64 64 0 1 0 0 128 64 64 0 0 0 0-128z">
                </path>
              </svg>
              <h3 class="tw-mr-[12px] tw-font-semibold">{{ nav.name }}</h3>
            </div>
            <draggable v-model="nav.items" handle=".move-item" group="group-item" tag="ul" item-key="id"
              :move="handleMove"
              @change="($event: DraggableNavigationDataItem) => handleNavigationItemChange($event, nav)">
              <!--
                  添加 => 排序
                  更新 => 排序
                -->
              <template #header>
                <li>
                  <div>
                    <el-link class="tw-mr-[6px]" type="primary" :underline="false"
                      @click="() => handleStateEditor(nav)">{{ $t("glob.edit") }}导航目录</el-link>
                    <el-link class="tw-mr-[6px]" type="danger" :underline="false" @click="() => handleStateDelete(nav)">{{
                      $t("glob.delete") }}导航目录</el-link>
                  </div>
                  <!-- <el-link type="primary" :underline="false" @click="() => ">{{ $t("glob.add") }}导航项</el-link> -->
                </li>
              </template>
              <template #item="{ element }">
                <div class="list-group-item">
                  <div class="tw-flex tw-w-full tw-leading-[18px]">
                    <div class="move-item tw-mr-[6px] tw-flex tw-w-[24px] tw-flex-shrink-0 tw-items-center">
                      <Icon :name="element.icon" size="24px" color="inherit"></Icon>
                    </div>
                    <div class="tw-overflow-hidden tw-text-ellipsis tw-whitespace-nowrap"
                      style="width: calc(100% - 30px)">
                      <p class="tw-mr-[6px] tw-overflow-hidden tw-text-ellipsis tw-whitespace-nowrap">{{ element.name }}
                      </p>
                    </div>
                    <div>
                      <el-button type="danger" :icon="Close" circle @click="removeElement(element, nav)"></el-button>
                    </div>
                  </div>
                </div>
              </template>
            </draggable>
          </div>
        </template>
      </draggable>
    </el-scrollbar>
    <EditorDetailsByPower ref="editorRef" title="导航目录"></EditorDetailsByPower>
  </div>
  <div :style="{ margin: '8px 20px 20px' }">
    <el-pagination v-show="state.total" v-model:current-page="state.page" v-model:page-size="state.size"
      :page-sizes="state.sizes" :total="state.total" layout="->, total, sizes, prev, pager, next, jumper" small
      @size-change="handleStateRefresh()" @current-change="handleStateRefresh()" />
  </div>
</template>

<script setup lang="ts" name="PlatformDetailClient">
import { ref, reactive, watch, nextTick } from "vue";
import { sizes } from "@/utils/common";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import draggable from "vuedraggable";
import NestedDraggable from "@/components/nestedDraggable/index.vue";
import { useNavTabs } from "@/stores/navTabs";

// Ui
import { ElMessage } from "element-plus";
import { Close } from "@element-plus/icons-vue";

// Api
// import { addNavigationGroups as addItem, modNavigationGroups as modItem, delNavigationGroups as delItem, modModelAuthItemByPlatform, getModelAuthItemByPlatform, setNavigationGroupsByOrders, getNavigationData, addNavigationItem, setNavigationItemByOrders, delNavigationItem, type PlatformItem } from "@/api/iam";
import { type PlatformItem } from "@/api/iam";
import { getApp, getMenu, type AppItem, type MenuItem, type NavItem, getModelAuthItemByPlatform, setModelAuthItemByPlatform } from "@/api/application";
// Editor
import { EditorType } from "@/views/common/interface";
import EditorDetailsByPower from "./EditorDetailsByPower.vue";

type DraggableNavigationDataItem = DraggableEvent<import("@/api/iam").NavigationDataItem>;

type NavigationGroupItem = Omit<import("@/api/iam").NavigationGroupItem, "items"> & { items: import("@/api/iam").NavigationDataItem[] };

const editorRef = ref<InstanceType<typeof EditorDetailsByPower>>();

type ItemData = (Omit<AppItem, "type" | "children"> | Omit<MenuItem, "type" | "children">) & { type: "APP" | "MENU" | "LINK"; select?: boolean; children?: ItemData[] };

type MoveEvent = import("sortablejs").MoveEvent & { draggedContext: any; relatedContext: any };

async function createItem(params: Partial<ItemData>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, "platform": props.data.code, "#TYPE": EditorType.Add }, async (req) => {
      try {
        const { success, message, data } = await addItem({ ...req, platform: props.data.code });
        if (success) {
          ElMessage.success(`添加成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
async function editorItem(params: Partial<ItemData>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, "platform": props.data.code, "#TYPE": EditorType.Mod }, async (req) => {
      try {
        const { success, message, data } = await modItem({ ...req, platform: props.data.code });
        if (success) {
          ElMessage.success(`编辑成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
async function deleteItem(params: Partial<ItemData>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, "platform": props.data.code, "#TYPE": EditorType.Del }, async (req) => {
      try {
        const { success, message, data } = await delItem({ ...req, platform: props.data.code });
        if (success) {
          ElMessage.success(`删除成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
function bindTreeName(tree?: ItemData[]): ItemData[] {
  if (tree instanceof Array) {
    for (let index = tree.length - 1; index >= 0; index--) {
      const { title, name, type } = tree[index];
      if ((type as unknown) === "button") tree.splice(index, 1);
      else Object.assign(tree[index], { code: name, name: title, type: "MENU", children: bindTreeName(tree[index].children) });
    }
    return tree;
  } else {
    return [];
  }
}
async function querysItem(params: Record<"platform", string>, onCleanup?: (cleanupFn: () => void) => void) {
  let controller = new AbortController();
  const response = Promise.all([getApp({ controller }), getModelAuthItemByPlatform({ ...params, controller }) /* , getNavigationData({ ...params, controller }) */]);
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const [{ success, message, data }, { success: authSuccess, message: authMessage, data: authData } /* , { success: navigationSuccess, message: navigationMessage, data: navigationData } */] = await response;
    if (!success) throw Object.assign(new Error(message), { success, data });
    if (!authSuccess) throw Object.assign(new Error(authMessage), { success: authSuccess, data: authData });
    // if (!navigationSuccess) throw Object.assign(new Error(navigationMessage), { success: navigationSuccess, data: navigationData });

    const enabledApps = authData.map((v) => v.name);
    return {
      data: await Promise.all(
        (data instanceof Array ? data : []).map(async (v) => {
          try {
            const { success, message, data } = await getMenu({ appId: v.id, controller });
            if (!success) throw Object.assign(new Error(message), { success, data });
            // res.children = bindTreeName(data as unknown as ItemData[] | undefined);
            Object.assign(v, { children: data instanceof Array ? data : [] });
          } catch (error) {
            /*  */
          }
          return Object.assign(v, { enabled: enabledApps.includes(v.name) });
        })
      ),
      // navigation: (navigationData.groups instanceof Array ? navigationData.groups : []) as NavigationGroupItem[],
    };
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    return {
      data: [],
      // navigation: [] as NavigationGroupItem[],
    };
  } finally {
    controller = new AbortController();
  }
}

/*********************************************************/
interface DraggableEvent<T> {
  moved?: { newIndex: number; oldIndex: number; element: T };
  removed?: { oldIndex: number; element: T };
  added?: { newIndex: number; element: T };
}

const navTabs = useNavTabs();

interface Props {
  data: Pick<PlatformItem, "code" | "name" | "note" | "multiTenant"> & Record<string, unknown>;
  width: number;
  height: number;
}
const props = withDefaults(defineProps<Props>(), {
  data: () => ({
    code: "",
    name: "",
    note: "",
    multiTenant: false,
  }),
  width: 100,
  height: 300,
});

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<NavItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {
    keyword: "",
    schemas: [].join(","),
  },
  column: [
    /* 列 */
    // { key: "code", label: "ID" },
    { key: "name", label: "名称" },
    // { key: "createdTime", label: "创建日期", formatter: (_row, _col, v) => formatterDate(v as string) },
    // { key: "updatedTime", label: "修改日期", formatter: (_row, _col, v) => formatterDate(v as string) },
  ],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

// const tree = ref<InstanceType<typeof ElTree>>();

watch<string, true>(
  () => props.data.code,
  async function () {
    if (state.loading) return;
    handleStateRefresh();
  },
  { immediate: true, flush: "post" }
);

async function handleStateCutSelect({ target }: Event, row: ItemData) {
  for (let index = 0; index < state.data.length; index++) {
    if (state.data[index].id === row.id) {
      state.data[index].enabled = (target as HTMLInputElement).checked;
      break;
    }
  }
  const node = state.data.filter(({ enabled }) => enabled);
  try {
    const { success, data, message } = await setModelAuthItemByPlatform({ platform: props.data.code, apps: node.map((v) => v.name) });
    if (!success) throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    await handleStateRefresh();
  }
  // await nextTick();
  // if (tree.value) {
  //   const select = getTreeSelect(state.data, "select");
  //   const nodes = tree.value.getCheckedNodes(true) as ItemData[];
  //   const everyNodesByAdd = nodes.filter((v) => select.filter(({ id }) => id === v.id).length === 0);
  //   const everyNodesByDel = select.filter((v) => nodes.filter(({ id }) => id === v.id).length === 0);
  //   if (everyNodesByAdd.length) for (let index = 0; index < everyNodesByAdd.length; index++) Object.assign(everyNodesByAdd[index], { select: true });
  //   if (everyNodesByDel.length) for (let index = 0; index < everyNodesByDel.length; index++) Object.assign(everyNodesByDel[index], { select: false });
  //   await nextTick();
  //   state.select = nodes;
  //   if (!everyNodesByAdd.length && !everyNodesByDel.length) return;
  //   try {
  //     const { success, data, message } = await modModelAuthItemByPlatform({ platform: props.data.code, select: nodes.map((v) => v.id) });
  //     if (success) {
  //       /*  */
  //     } else throw Object.assign(new Error(message), { success, data });
  //   } catch (error) {
  //     if (error instanceof Error) ElMessage.error(error.message);
  //   } finally {
  //     await handleStateRefresh();
  //   }
  // }
}
const navigation = ref<NavigationGroupItem[]>([]);

async function handleStateCreate(params: Partial<ItemData>) {
  await createItem(params);
  await handleStateRefresh();
}
async function handleStateEditor(params: Partial<ItemData>) {
  await editorItem(params);
  await handleStateRefresh();
}
async function handleStateDelete(params: Partial<ItemData>) {
  await deleteItem(params);
  await handleStateRefresh();
}
async function handleStateRefresh() {
  if (state.loading) return;
  state.loading = true;
  const res = await querysItem({ platform: props.data.code });
  state.data.splice(0, state.data.length, ...res.data);
  // navigation.value = [];
  // await nextTick();
  // navigation.value = res.navigation;
  await nextTick();
  state.select = getTreeSelect(state.data, "enabled") as NavItem[];
  if (!state.expand.length) {
    state.expand = [];
    appendExpandKetToFirst(state.data);
  }
  state.loading = false;
}
function getTreeSelect(tree: NavItem[], selectKey: keyof NavItem): typeof tree {
  const select: NavItem[] = [];
  for (let index = 0; index < tree.length; index++) {
    if (tree[index][selectKey]) select.push(tree[index]);
    if (Object.prototype.hasOwnProperty.call(tree[index], "children") && tree[index].children instanceof Array) select.push(...getTreeSelect(tree[index].children as enabled[], selectKey));
  }
  return select;
}

function appendExpandKetToFirst(data: NavItem[]) {
  for (let index = 0; index < data.length; index++) {
    const item = data[index];
    state.expand.push(item.id);
    if (item.children instanceof Array) appendExpandKetToFirst(item.children);
    break;
  }
}

const moveLiading = ref(false);
function handleMove({ to }: MoveEvent) {
  return !Array.from(to.classList).includes("source-tree");
}

async function handleNavigationChange(event: DraggableEvent<NavigationGroupItem>) {
  state.loading = true;
  if (event.moved) {
    // const { moved } = event;
    // const index = navigation.value.findIndex((v) => v.id === moved.element.id);
    // if (index !== -1) {
    //   const item = navigation.value.splice(index, 1);
    //   navigation.value.splice(moved.newIndex, 0, ...item);
    // }
    try {
      const { success, message, data } = await setNavigationGroupsByOrders({ orders: navigation.value.map((nav, i) => ({ id: nav.id, order: i })) });
      if (!success) throw Object.assign(new Error(message), { success, data });
    } catch (error) {
      /*  */
    }
  }
  state.loading = false;
  await handleStateRefresh();
}

async function handleNavigationItemChange(event: DraggableNavigationDataItem, group: NavigationGroupItem) {
  try {
    if (event.added) {
      /* 添加到数组 */
      const {
        added: { newIndex, element },
      } = event;
      await (async (item) => {
        const { success, message, data } = await addNavigationItem({ groupId: group.id, name: item.name, note: item.note, type: item.type, targetId: item.targetId || item.id, order: newIndex, config: JSON.stringify({ path: item.path, url: item.url, icon: item.icon }) });
        group.items.splice(newIndex, 1, { ...data, path: item.path, url: item.url, icon: item.icon });
        if (!success) throw Object.assign(new Error(message), { success, data });
      })(element);

      const items = group.items.slice(newIndex + 1);
      await (async (start) => {
        if (!items.length) return;
        const { success, message, data } = await setNavigationItemByOrders({ orders: items.map((nav, i) => ({ id: nav.id, order: start + i })) });
        if (!success) throw Object.assign(new Error(message), { success, data });
      })(newIndex + 1);
    }
    if (event.removed) {
      /* 添加到数组 */
      const {
        removed: { oldIndex, element },
      } = event;

      await (async (item) => {
        const { success, message, data } = await delNavigationItem({ id: item.id });
        if (!success) throw Object.assign(new Error(message), { success, data });
      })(element);

      const items = group.items.slice(oldIndex);
      await (async (start) => {
        if (!items.length) return;
        const { success, message, data } = await setNavigationItemByOrders({ orders: items.map((nav, i) => ({ id: nav.id, order: start + i })) });
        if (!success) throw Object.assign(new Error(message), { success, data });
      })(oldIndex);
    }
    // eslint-disable-next-line no-unreachable
    if (event.moved) {
      /* 在数组中移动 */
      const {
        moved: { oldIndex, newIndex, element },
      } = event;
      if (group.items.findIndex((v) => v.id === element.id) !== newIndex) throw new Error("Refresh");

      const items = group.items.slice(Math.min(newIndex, oldIndex), Math.max(newIndex, oldIndex) + 1);
      await (async (start) => {
        if (!items.length) return;
        const { success, message, data } = await setNavigationItemByOrders({ orders: items.map((nav, i) => ({ id: nav.id, order: start + i })) });
        if (!success) throw Object.assign(new Error(message), { success, data });
      })(Math.min(newIndex, oldIndex));
    }
    // eslint-disable-next-line no-unreachable
    for (let index = 0; index < group.items.length; index++) {
      group.items[index].order = index;
    }
    // eslint-disable-next-line no-unreachable
  } catch (error) {
    await handleStateRefresh();
  }
}
async function removeElement(row: import("@/api/iam").NavigationDataItem, group: NavigationGroupItem) {
  const rawGroup = navigation.value.find((v) => v.id === group.id);
  if (!rawGroup) return;
  try {
    const index = rawGroup.items.findIndex((v) => v.id === row.id);
    if (index === -1) throw new Error("Not Find");
    rawGroup.items.splice(index, 1);

    const { success, message, data } = await delNavigationItem({ id: row.id });
    if (!success) throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    await handleStateRefresh();
  }
}
</script>

<style lang="scss" scoped>
.move-group,
.move-item {
  cursor: move;
}

.list-group-item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 12px;
}

.tree-item {
  position: relative;

  &::before,
  &::after {
    content: "";
    display: block;
    position: absolute;
    top: 12px;
    left: 0px;
  }

  &::before {
    width: 12px;
    height: 100%;
  }

  &:not(.final, .first) {
    border-left: var(--el-border);

    &::before {
      left: 0px;
      border-top: var(--el-border);
    }
  }

  &.first {
    &::before {
      top: 12px;
      border-top: var(--el-border);
      border-left: var(--el-border);
    }

    &::after {
      top: 0;
      left: -5px;
      width: 6px;
      height: 100%;
      border-top: var(--el-border);
      border-right: var(--el-border);
    }
  }

  &.final {
    &::before {
      top: 12px;
      height: calc(100% - 12px);
      border-top: var(--el-border);
    }

    &:not(.first) {
      &::after {
        top: 0;
        height: calc(100% - 12px);
        border-left: var(--el-border);
      }
    }
  }
}

.move-process {
  background-color: var(--el-color-primary-light-7);
}

.checkbox-virtually {
  .virtually {

    &,
    &::before,
    &::after {
      box-sizing: border-box;
    }
  }

  .toggle-button {
    cursor: pointer;
    display: block;
    transform-origin: 50% 50%;
    transform-style: preserve-3d;
    transition: transform 0.14s ease;

    &:active {
      transform: rotateX(30deg);
    }

    input {
      + {
        .virtually {
          border: 3px solid var(--el-color-success-light-7);
          border-radius: 50%;
          position: relative;
          width: 18px;
          height: 18px;

          svg {
            fill: none;
            stroke-width: 3.6;
            stroke-linecap: round;
            stroke-linejoin: round;
            width: 18px;
            height: 18px;
            display: block;
            position: absolute;
            left: -3px;
            top: -3px;
            right: -3px;
            bottom: -3px;
            z-index: 1;
          }

          svg {
            stroke: var(--el-color-danger-light-7);
            stroke-dashoffset: 124.6;
            stroke-dasharray: 0 162.6 133 29.6;
            transition: all 0.4s ease 0s;
          }

          &:before,
          &:after {
            content: "";
            width: 3px;
            height: 16px;
            position: absolute;
            left: 50%;
            top: 50%;
            border-radius: 5px;
          }

          &:before {
            opacity: 0;
            transform: scale(0.3) translate(-50%, -50%) rotate(45deg);
            animation: bounceInBefore 0.3s linear forwards 0.3s;
            background: var(--el-color-danger-light-3);
          }

          &:after {
            opacity: 0;
            transform: scale(0.3) translate(-50%, -50%) rotate(-45deg);
            animation: bounceInAfter 0.3s linear forwards 0.3s;
            background: var(--el-color-danger-light-3);
          }
        }
      }

      &:checked+ {
        .virtually {
          svg {
            stroke: var(--el-color-success-light-3);
            stroke-dashoffset: 162.6;
            stroke-dasharray: 0 162.6 28 134.6;
            transition: all 0.4s ease 0.2s;
          }

          &:before {
            opacity: 0;
            transform: scale(0.3) translate(-50%, -50%) rotate(45deg);
            animation: bounceInBeforeDont 0.3s linear forwards 0s;
            background: var(--el-color-success-light-3);
          }

          &:after {
            opacity: 0;
            transform: scale(0.3) translate(-50%, -50%) rotate(-45deg);
            animation: bounceInAfterDont 0.3s linear forwards 0s;
            background: var(--el-color-success-light-3);
          }
        }
      }
    }
  }

  @-webkit-keyframes bounceInBefore {
    0% {
      opacity: 0;
      transform: scale(0.3) translate(-50%, -50%) rotate(45deg);
    }

    50% {
      opacity: 0.9;
      transform: scale(1.1) translate(-50%, -50%) rotate(45deg);
    }

    80% {
      opacity: 1;
      transform: scale(0.89) translate(-50%, -50%) rotate(45deg);
    }

    100% {
      opacity: 1;
      transform: scale(1) translate(-50%, -50%) rotate(45deg);
    }
  }

  @keyframes bounceInBefore {
    0% {
      opacity: 0;
      transform: scale(0.3) translate(-50%, -50%) rotate(45deg);
    }

    50% {
      opacity: 0.9;
      transform: scale(1.1) translate(-50%, -50%) rotate(45deg);
    }

    80% {
      opacity: 1;
      transform: scale(0.89) translate(-50%, -50%) rotate(45deg);
    }

    100% {
      opacity: 1;
      transform: scale(1) translate(-50%, -50%) rotate(45deg);
    }
  }

  @-webkit-keyframes bounceInAfter {
    0% {
      opacity: 0;
      transform: scale(0.3) translate(-50%, -50%) rotate(-45deg);
    }

    50% {
      opacity: 0.9;
      transform: scale(1.1) translate(-50%, -50%) rotate(-45deg);
    }

    80% {
      opacity: 1;
      transform: scale(0.89) translate(-50%, -50%) rotate(-45deg);
    }

    100% {
      opacity: 1;
      transform: scale(1) translate(-50%, -50%) rotate(-45deg);
    }
  }

  @keyframes bounceInAfter {
    0% {
      opacity: 0;
      transform: scale(0.3) translate(-50%, -50%) rotate(-45deg);
    }

    50% {
      opacity: 0.9;
      transform: scale(1.1) translate(-50%, -50%) rotate(-45deg);
    }

    80% {
      opacity: 1;
      transform: scale(0.89) translate(-50%, -50%) rotate(-45deg);
    }

    100% {
      opacity: 1;
      transform: scale(1) translate(-50%, -50%) rotate(-45deg);
    }
  }

  @-webkit-keyframes bounceInBeforeDont {
    0% {
      opacity: 1;
      transform: scale(1) translate(-50%, -50%) rotate(45deg);
    }

    100% {
      opacity: 0;
      transform: scale(0.3) translate(-50%, -50%) rotate(45deg);
    }
  }

  @keyframes bounceInBeforeDont {
    0% {
      opacity: 1;
      transform: scale(1) translate(-50%, -50%) rotate(45deg);
    }

    100% {
      opacity: 0;
      transform: scale(0.3) translate(-50%, -50%) rotate(45deg);
    }
  }

  @-webkit-keyframes bounceInAfterDont {
    0% {
      opacity: 1;
      transform: scale(1) translate(-50%, -50%) rotate(-45deg);
    }

    100% {
      opacity: 0;
      transform: scale(0.3) translate(-50%, -50%) rotate(-45deg);
    }
  }

  @keyframes bounceInAfterDont {
    0% {
      opacity: 1;
      transform: scale(1) translate(-50%, -50%) rotate(-45deg);
    }

    100% {
      opacity: 0;
      transform: scale(0.3) translate(-50%, -50%) rotate(-45deg);
    }
  }
}
</style>
