<template>
  <el-scrollbar>
    <div class="flex-search" :style="{ minWidth: `${props.width - 2}px` }">
      <div class="left"><slot name="left" :create="handleStateCreate" :delete="handleStateDelete" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot></div>
      <div class="center"><slot name="center" :create="handleStateCreate" :delete="handleStateDelete" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot></div>
      <div class="right"><slot name="right" :create="handleStateCreate" :delete="handleStateDelete" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot></div>
    </div>
  </el-scrollbar>
  <el-table v-loading="state.loading" :data="state.data" :height="props.height - 64 - 20 - (state.total ? 32 : 0)" :style="{ width: `${props.width - 40}px`, margin: '0 auto' }">
    <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :min-width="column.width || 120" :formatter="column.formatter" />
    <el-table-column :label="t('glob.operate')" align="left" header-align="left" :width="110" fixed="right">
      <template #header="{ column }">
        <div style="display: flex; justify-content: center">
          <span style="margin: 0 10px">{{ column.label }}</span>
          <el-link type="primary" :underline="false" :icon="Refresh" @click="handleStateRefresh()"></el-link>
        </div>
      </template>
      <template #default="{ row }">
        <!-- <el-button link type="default" :icon="View" @click="editorRef?.open({ '#TYPE': EditorType.Cat, ...row })">{{ t("glob.Cat") }}</el-button> -->
        <el-button link type="primary" :icon="Edit" :disabled="false" @click="handleStateEditor(row)" :title="t('glob.edit')"></el-button>
        <el-button link type="danger" :icon="Delete" :disabled="false" @click="handleStateDelete(row)" :title="t('glob.delete')"></el-button>
        <el-dropdown trigger="click" @command="$event.callback(row)" class="el-button el-button--default is-link" style="vertical-align: middle">
          <span style="font-size: var(--el-font-size-base)">
            <el-icon :title="t('glob.More')"><More /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item :command="{ callback: handleStateCutUser }" :disabled="false">
                <el-icon class="el-icon--right"><Postcard /></el-icon>
                管理租户角色
              </el-dropdown-item>
              <el-dropdown-item :command="{ callback: handleStateCutUser }" :disabled="false">
                <el-icon class="el-icon--right"><User /></el-icon>
                管理租户用户
              </el-dropdown-item>
              <el-dropdown-item :command="{ callback: handleStateCutOwner }" :disabled="false">
                <el-icon class="el-icon--right"><Edit /></el-icon>
                变更拥有人
              </el-dropdown-item>
              <el-dropdown-item v-if="row.frozen === false" :command="{ callback: handleStateUserCutBlock }" :disabled="false">
                <el-icon class="el-icon--right"><Unlock /></el-icon>
                冻结租户
              </el-dropdown-item>
              <el-dropdown-item v-if="row.frozen === true" :command="{ callback: handleStateUserCutActive }" :disabled="false">
                <el-icon class="el-icon--right"><Lock /></el-icon>
                解冻租户
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </el-table-column>
  </el-table>
  <div :style="{ margin: '8px 20px 20px' }">
    <el-pagination v-show="state.total" v-model:current-page="state.page" v-model:page-size="state.size" :page-sizes="state.sizes" :total="state.total" layout="->, total, sizes, prev, pager, next, jumper" small @size-change="handleStateRefresh()" @current-change="handleStateRefresh()" />
  </div>
  <EditorDetailsByTenant ref="editorRef" title="租户" :systemEdition="systemEdition"></EditorDetailsByTenant>
  <EditorForPersonnel ref="editorForPersonnelRef" title="租户拥有人"></EditorForPersonnel>
</template>

<script setup lang="ts" name="PlatformDetailTenant">
/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */
import { ref, reactive, nextTick, watch, h, type CSSProperties } from "vue";
import { useI18n } from "vue-i18n";
import { sizes } from "@/utils/common";
import { bindFormBox } from "@/utils/bindFormBox";

// Ui
import { ElMessage, ElButton, ElTag, ElForm, ElFormItem, ElSelect, ElOption, ElAvatar, ElTable, ElTableColumn, ElMessageBox } from "element-plus";
import { Refresh, Edit, Delete, More, Unlock, Lock, Avatar, User, Postcard } from "@element-plus/icons-vue";

// Api
import { getTenant as getItem, addTenant as addItem, modTenant as modItem, delTenant as delItem, cutTenantByBlock, cutTenantByActive, changeTenantByOwner, getUser, languageOption } from "@/api/iam";
import type { PlatformItem, TenantItem as ItemData, UserItem, UserBaseItem } from "@/api/iam";
import { getSystemVersion } from "@/api/system";

// Editor
import { EditorType } from "@/views/common/interface";
import EditorDetailsByTenant from "./EditorDetailsByTenant.vue";
import EditorForPersonnel from "@/views/pages/tenant/govern/EditorForPersonnel.vue";
import { find } from "lodash-es";

const editorRef = ref<InstanceType<typeof EditorDetailsByTenant>>();
const editorForPersonnelRef = ref<InstanceType<typeof EditorForPersonnel>>();
async function createItem(params: Partial<ItemData>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, "platform": props.data.code, "#TYPE": EditorType.Add }, async (req) => {
      const action = await new Promise<"confirm" | "close" | "cancel">((resolve) => {
        ElMessageBox.confirm("是否为客户同时创建拥有人？", `${t("glob.add")}客户`, { type: "info", distinguishCancelAndClose: true, confirmButtonText: "是", cancelButtonText: "否" })
          .then(resolve)
          .catch(resolve);
      });
      switch (action) {
        case "confirm": {
          if (!editorForPersonnelRef.value) throw new Error("致命错误,请刷新后操作");
          try {
            await editorForPersonnelRef.value.open({ accountSuffix: req.abbreviation }, async (personnelForm: Record<string, unknown>) => {
              const { success, message, data } = await addItem({ ...req, owner: personnelForm as Partial<UserBaseItem>, platform: props.data.code });
              if (!success) throw Object.assign(new Error(message), { success, data });
              ElMessage.success(t("axios.Operation successful"));
            });
            return true;
          } catch (error) {
            // throw void 0;
            return false;
          }
        }
        case "cancel": {
          try {
            const { success, message, data } = await addItem({ ...req, platform: props.data.code });
            if (!success) throw Object.assign(new Error(message), { success, data });
            ElMessage.success(t("axios.Operation successful"));
            return true;
          } catch (error) {
            if (error instanceof Error) ElMessage.error(error.message);
            return false;
          }
        }
        default: {
          /* "close" */ return false;
        }
      }
    });
  } catch (error) {
    /*  */
  }
}
async function editorItem(params: Partial<ItemData>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    const owner: UserBaseItem = { id: params.ownerId || "", name: params.ownerName || "", /* nickname: params.ownerNickname || "", */ account: params.ownerAccount || "", phone: params.ownerPhone || "", email: params.ownerEmail || "", gender: "SECRET", language: "none", password: "" };
    await editorRef.value.open({ ...params, owner, "platform": props.data.code, "#TYPE": EditorType.Mod }, async (req) => {
      try {
        const { success, message, data } = await modItem({ ...req, platform: props.data.code });
        if (success) {
          ElMessage.success(`编辑成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
async function deleteItem(params: Partial<ItemData>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, "platform": props.data.code, "#TYPE": EditorType.Del }, async (req) => {
      try {
        const { success, message, data } = await delItem({ ...req, platform: props.data.code });
        if (success) {
          ElMessage.success(`删除成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  let controller = new AbortController();
  const response = Promise.all([
    getItem({
      platform: params.platform as string,
      ...(typeof state.search.keyword === "string" && state.search.keyword ? { keyword: state.search.keyword } : {}),
      paging: {
        pageNumber: state.page,
        pageSize: state.size,
      },
      controller,
    }),
    getSystemVersion({ controller }),
  ]).then(([res, { success, message, data }]) => {
    if (success) {
      systemEdition.value = data instanceof Array ? data : [];
    } else throw Object.assign(new Error(message), { success, data });
    return res;
  });
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await response;
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);
      return data instanceof Array ? data : [];
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(20);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}

/*********************************************************/

const { t } = useI18n();

interface Props {
  data: Pick<PlatformItem, "code" | "name" | "note" | "multiTenant"> & Record<string, unknown>;
  width: number;
  height: number;
}
const props = withDefaults(defineProps<Props>(), {
  data: () => ({
    code: "",
    name: "",
    note: "",
    multiTenant: false,
  }),
  width: 100,
  height: 300,
});

/* 数据部分 */
const systemEdition = ref<{ code: string; name: string }[]>([]);
interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<ItemData>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {
    keyword: "",
  },
  column: [
    /* 表格列 */
    // { key: "id", label: "ID" },
    { key: "name", label: "名称" },
    // { key: "parentId", label: "父级租户ID" },
    { key: "address", label: "地址" },
    { key: "systemEdition", label: "系统版本", formatter: (_row, _col, v) => ((find(systemEdition.value, { code: v }) as { name: string }) || { name: "--" }).name },
    { key: "zoneId", label: "时区", formatter: (_row, _col, v) => (v as string) || "--" },
    { key: "language", label: "语言", formatter: (_row, _col, v) => ((find(languageOption, { value: v }) as { label: string }) || { label: "--" }).label },
    { key: "blocked", label: "状态", formatter: (_row, _col, v) => (v ? h(ElTag, { type: "danger", effect: "dark" }, () => t("glob.Frozen")) : h(ElTag, { type: "success", effect: "dark" }, () => t("glob.Normal"))) },
    { key: "note", label: "描述信息" },
    // { key: "createdTime", label: "创建日期", formatter: (_row, _col, v) => formatterDate(v as string) },
    // { key: "updatedTime", label: "修改日期", formatter: (_row, _col, v) => formatterDate(v as string) },
  ],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

watch<string, true>(
  () => props.data.code,
  async function () {
    if (state.loading) return;
    handleStateRefresh();
  },
  { immediate: true, flush: "post" }
);

async function handleStateCreate(params: Partial<ItemData>) {
  await createItem(params);
  await handleStateRefresh();
}
async function handleStateEditor(params: Partial<ItemData>) {
  await editorItem(params);
  await handleStateRefresh();
}
async function handleStateDelete(params: Partial<ItemData>) {
  await deleteItem(params);
  await handleStateRefresh();
}
async function handleStateRefresh(onCleanup?: (cleanupFn: () => void) => void) {
  if (state.loading) return;
  state.loading = true;
  state.data.splice(0, state.data.length, ...(await querysItem({ platform: props.data.code }, onCleanup)));
  state.loading = false;
}

async function handleStateCutUser(params: Partial<ItemData>) {
  const form = reactive<Record<"title" | "ownerId", string> & { customStyle: CSSProperties; userList: UserItem[]; loading: boolean }>({
    title: "管理租户用户",
    customStyle: { "--el-messagebox-width": "fit-content" },

    ownerId: "",

    userList: [],
    loading: false,
  });
  const remoteMethod = async (req: Record<string, string>) => {
    form.userList = [];
    try {
      form.loading = true;
      await nextTick();
      const { success, message, data: userList } = await getUser({ platform: props.data.code, ...req, paging: { pageNumber: 1, pageSize: 10 } });
      if (!success) throw Object.assign(new Error(message), { success, data: userList });
      form.userList = (userList instanceof Array ? userList : []).map((user) => ({ ...user, ...req }));
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      form.loading = false;
    }
  };
  const messageRender = h(ElTable, { data: form.userList }, () => [
    /*  */
    h(ElTableColumn, { type: "selection", width: 55 }),
    h(ElTableColumn, { label: "姓名", prop: "name", width: 55 }),
    h(ElTableColumn, { label: "昵称", prop: "nickname", width: 55 }),
    h(ElTableColumn, { label: "账号", prop: "account", width: 55 }),
    h(ElTableColumn, { label: "手机号码", prop: "phone", width: 55 }),
    h(ElTableColumn, { label: "邮箱", prop: "email", width: 55 }),
  ]);
  try {
    await bindFormBox([h(ElForm, { model: form }, () => [messageRender])], form, async () => {
      const { success, message, data } = await changeTenantByOwner({ id: params.id, ownerId: form.ownerId });
      if (success) {
        ElMessage.success("变更成功");
      } else throw Object.assign(new Error(message), { success, data });
      return { success, message };
    });
  } catch (error) {
    /*  */
  }
  await handleStateRefresh();
}
async function handleStateUserCutBlock(params: Partial<ItemData>) {
  const form = reactive<Record<"title", string>>({
    title: "冻结租户",
  });
  try {
    await bindFormBox([h("p", {}, "确定冻结租户吗？")], form, async () => {
      const { success, message, data } = await cutTenantByBlock({ id: params.id as string });
      if (success) {
        ElMessage.success("租户冻结成功");
      } else throw Object.assign(new Error(message), { success, data });
      return { success, message };
    });
  } catch (error) {
    /*  */
  }
  await handleStateRefresh();
}
async function handleStateUserCutActive(params: Partial<ItemData>) {
  const form = reactive<Record<"title", string>>({
    title: "解冻租户",
  });
  try {
    await bindFormBox([h("p", {}, "确定解冻租户吗？")], form, async () => {
      const { success, message, data } = await cutTenantByActive({ id: params.id });
      if (success) {
        ElMessage.success("租户冻结成功");
      } else throw Object.assign(new Error(message), { success, data });
      return { success, message };
    });
  } catch (error) {
    /*  */
  }
  await handleStateRefresh();
}
async function handleStateCutOwner(params: Partial<ItemData>) {
  const form = reactive<Record<"title" | "ownerId", string> & { userList: UserBaseItem[]; loading: boolean }>({
    title: "变更租户拥有人",
    ownerId: params.ownerId || "",
    userList: [{ id: params.ownerId || "", keyword: "", name: params.ownerName!, /* nickname: params.ownerNickname!, */ account: params.ownerAccount!, phone: params.ownerPhone!, email: params.ownerEmail!, gender: "SECRET", language: "none", password: "" }],
    loading: false,
  });
  const remoteMethod = async (query: string) => {
    form.userList = [];
    if (!query) return;
    const base: UserBaseItem = { id: "new", keyword: "", name: "", /* nickname: "", */ account: "", phone: "", email: "", gender: "SECRET", language: "none", password: "" };
    if (/^(1[3-9])\d{9}$/.test(query)) {
      (base.phone = query), (base.keyword = "phone");
    } else if (/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/.test(query)) {
      (base.email = query), (base.keyword = "email");
    } else return;
    try {
      form.loading = true;
      await nextTick();
      await (async (req) => {
        const { success, message, data: userList } = await getUser({ platform: props.data.code, ...req, paging: { pageNumber: 1, pageSize: 10 } });
        if (!success) throw Object.assign(new Error(message), { success, data: userList });
        form.userList = (userList instanceof Array ? userList : []).map((user) => ({ ...user, ...req, keyword: base.keyword }));
      })({
        ...(base.phone ? { phone: base.phone } : {}),
        ...(base.email ? { email: base.email } : {}),
      });
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      form.loading = false;
    }
  };
  const messageRender = h(ElFormItem, { label: "用户手机号 或 邮箱", prop: "ownerId" }, () => [
    h(ElSelect, { "modelValue": form.ownerId, "onUpdate:modelValue": (v) => (form.ownerId = v), "class": ["tw-w-full"], "placeholder": t("glob.Please input field", { field: `用户 手机号 或 邮箱` }), "filterable": true, "remote": true, remoteMethod, "loading": form.loading }, () => {
      return form.userList.map((v) =>
        h(ElOption, { value: v.id || "", label: v.name, key: v.id || v.name, class: ["tw-h-fit"] }, () => {
          return h("div", { class: ["tw-flex", "tw-w-full", "tw-flex-row", "tw-items-center"] }, [
            /*  */
            h(ElAvatar, { shape: "circle", icon: v.name ? undefined : Avatar, fit: "fill" }, () => v.name[0]),
            h("div", { class: "tw-ml-4" }, [
              /*  */
              h("div", h("div", [h("span", `${v.name}${v.account ? `（${v.account}）` : ""}`), h("span", { style: { color: "var(--el-text-color-placeholder)" } }, `手机号：${v.phone || "--"}`)])),
              h("p", h("span", { style: { color: "var(--el-text-color-placeholder)" } }, `邮箱：${v.email || "--"}`)),
            ]),
          ]);
        })
      );
    }),
  ]);
  try {
    await bindFormBox([h(ElForm, { model: form }, () => [messageRender])], form, async () => {
      const { success, message, data } = await changeTenantByOwner({ id: params.id, ownerId: form.ownerId });
      if (success) {
        ElMessage.success("变更成功");
      } else throw Object.assign(new Error(message), { success, data });
      return { success, message };
    });
  } catch (error) {
    /*  */
  }
  await handleStateRefresh();
}
</script>

<style lang="scss" scoped></style>
