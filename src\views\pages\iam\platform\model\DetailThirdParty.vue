<template>
  <el-scrollbar>
    <div class="flex-search" :style="{ minWidth: `${props.width - 2}px` }">
      <div class="left"><slot name="left" :create="handleStateCreate" :delete="handleStateDelete" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot></div>
      <div class="center"><slot name="center" :create="handleStateCreate" :delete="handleStateDelete" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot></div>
      <div class="right"><slot name="right" :create="handleStateCreate" :delete="handleStateDelete" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot></div>
    </div>
  </el-scrollbar>
  <el-scrollbar :height="props.height - 64 - 20" :style="{ width: `${props.width - 40}px`, margin: '0 auto' }">
    <div class="tw-p-[20px]">
      <div class="tw-w-full tw-overflow-hidden tw-bg-white tw-shadow dark:tw-bg-black sm:tw-rounded-lg">
        <div class="tw-flex tw-items-center tw-py-[12px]">
          <div class="tw-px-4 tw-py-5 sm:tw-px-6">
            <h3 class="tw-text-base tw-font-semibold tw-leading-6 dark:tw-text-white">平台第三方绑定</h3>
            <p class="tw-mt-1 tw-text-sm tw-text-gray-600 dark:tw-text-slate-300">平台绑定第三方账号可用作身份验证</p>
          </div>
          <div class="tw-px-4 sm:tw-px-0">
            <!--  -->
          </div>
        </div>

        <div class="tw-border-t tw-border-gray-200 dark:tw-border-gray-700">
          <dl>
            <div class="tw-px-4 tw-py-5 odd:tw-bg-gray-50 even:tw-bg-white dark:odd:tw-bg-gray-900 dark:even:tw-bg-black sm:tw-grid sm:tw-grid-cols-3 sm:tw-gap-4 sm:tw-px-6">
              <dt class="text-gray-500 tw-text-sm tw-font-medium">平台编码</dt>
              <dd class="text-sm tw-mt-1 tw-text-gray-900 dark:tw-text-gray-50 sm:tw-col-span-2 sm:tw-mt-0">{{ props.data.code }}</dd>
            </div>
            <div class="tw-px-4 tw-py-5 odd:tw-bg-gray-50 even:tw-bg-white dark:odd:tw-bg-gray-900 dark:even:tw-bg-black sm:tw-grid sm:tw-grid-cols-3 sm:tw-gap-4 sm:tw-px-6">
              <dt class="text-gray-500 tw-text-sm tw-font-medium">平台名称</dt>
              <dd class="text-sm tw-mt-1 tw-text-gray-900 dark:tw-text-gray-50 sm:tw-col-span-2 sm:tw-mt-0">{{ props.data.name }}</dd>
            </div>
            <div class="tw-px-4 tw-py-5 odd:tw-bg-gray-50 even:tw-bg-white dark:odd:tw-bg-gray-900 dark:even:tw-bg-black sm:tw-grid sm:tw-grid-cols-3 sm:tw-gap-4 sm:tw-px-6">
              <dt class="text-gray-500 tw-text-sm tw-font-medium">平台描述</dt>
              <dd class="text-sm tw-mt-1 tw-whitespace-pre tw-text-gray-900 dark:tw-text-gray-50 sm:tw-col-span-2 sm:tw-mt-0">{{ props.data.note }}</dd>
            </div>
            <div class="tw-px-4 tw-py-5 odd:tw-bg-gray-50 even:tw-bg-white dark:odd:tw-bg-gray-900 dark:even:tw-bg-black sm:tw-grid sm:tw-grid-cols-3 sm:tw-gap-4 sm:tw-px-6">
              <dt class="text-gray-500 tw-text-sm tw-font-medium">第三方平台</dt>
              <dd class="text-sm tw-mt-1 tw-text-gray-900 dark:tw-text-gray-50 sm:tw-col-span-2 sm:tw-mt-0">
                <!--  -->
                <ul role="list" class="tw-divide-y tw-divide-gray-200 tw-rounded-md tw-border tw-border-gray-200 dark:tw-divide-gray-700 dark:tw-border-gray-700">
                  <li v-for="third in thirdTypeOption" :key="third.value" class="tw-flex tw-items-center tw-justify-between tw-py-3 tw-pl-3 tw-pr-4 tw-text-sm">
                    <div class="tw-flex tw-w-0 tw-flex-1 tw-items-center">
                      <Icon v-if="third.value === thirdType.github" color="inherit" size="20" name="fab-faGithub" />
                      <Icon v-if="third.value === thirdType.gitee" color="inherit" size="20" name="fab-faGitSquare" />
                      <Icon v-if="third.value === thirdType.wechat" color="inherit" size="20" name="fab-faWeixin" />
                      <span class="tw-ml-2 tw-w-0 tw-flex-1 tw-truncate">{{ third.label }}</span>
                    </div>
                    <div class="tw-ml-4 tw-flex-shrink-0">
                      <template v-if="state.data.find(({ type }) => type === third.value)">
                        <el-link type="primary" :underline="false" @click="handleStateEditor(state.data.find(({ type }) => type === third.value) as ItemData)">修改</el-link>
                        <el-divider direction="vertical" />
                        <el-link type="danger" :underline="false" @click="handleStateDelete(state.data.find(({ type }) => type === third.value) as ItemData)">解绑</el-link>
                      </template>
                      <template v-else>
                        <el-link type="primary" :underline="false" @click="handleStateEditor({ type: third.value })">绑定</el-link>
                      </template>
                    </div>
                  </li>
                </ul>
                <!--  -->
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  </el-scrollbar>
  <EditorDetailsByThirdParty ref="editorRef" title="第三方平台"></EditorDetailsByThirdParty>
</template>

<script setup lang="ts" name="PlatformDetailClient">
/* eslint-disable @typescript-eslint/no-unused-vars, no-unused-vars */
import { ref, reactive, nextTick, watch, h } from "vue";
import { useI18n } from "vue-i18n";
import { sizes } from "@/utils/common";
import { bindFormBox } from "@/utils/bindFormBox";

// Ui
import { ElMessage, ElButton, ElTag } from "element-plus";
import { Refresh, Edit, Delete, More, Unlock, Lock } from "@element-plus/icons-vue";

// Api
import { getThirdItem as getItem, addThirdItem as addItem, modThirdItem as modItem, delThirdItem as delItem, thirdTypeOption, thirdType } from "@/api/iam";
import type { PlatformItem, ThirdItem as ItemData } from "@/api/iam";

// Editor
import { EditorType } from "@/views/common/interface";
import EditorDetailsByThirdParty from "./EditorDetailsByThirdParty.vue";
const editorRef = ref<InstanceType<typeof EditorDetailsByThirdParty>>();
async function createItem(params: Partial<ItemData>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, "#TYPE": EditorType.Add }, async (req) => {
      try {
        const { success, message, data } = await addItem({ ...req, platform: props.data.code });
        if (success) {
          ElMessage.success(`添加成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
async function editorItem(params: Partial<ItemData>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, "#TYPE": EditorType.Mod }, async (req) => {
      try {
        const { success, message, data } = await modItem({ ...req, platform: props.data.code });
        if (success) {
          ElMessage.success(`编辑成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
async function deleteItem(params: Partial<ItemData>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, "#TYPE": EditorType.Del }, async (req) => {
      try {
        const { success, message, data } = await delItem({ ...req, platform: props.data.code });
        if (success) {
          ElMessage.success(`删除成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}
async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  let controller = new AbortController();
  const response = getItem({
    platform: params.platform as string,
    ...(typeof state.search.name === "string" && state.search.name ? { name: state.search.name } : {}),
    paging: {
      pageNumber: state.page,
      pageSize: state.size,
    },
    controller,
  });
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await response;
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);
      return data instanceof Array ? data : [];
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(20);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}

/*********************************************************/

const { t } = useI18n();

interface Props {
  data: Pick<PlatformItem, "code" | "name" | "note" | "multiTenant"> & Record<string, unknown>;
  width: number;
  height: number;
}
const props = withDefaults(defineProps<Props>(), {
  data: () => ({
    code: "",
    name: "",
    note: "",
    multiTenant: false,
  }),
  width: 100,
  height: 300,
});

/* 数据部分 */
interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<ItemData>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {
    keyword: "",
  },
  column: [
    /* 表格列 */
    // { key: "id", label: "ID" },
  ],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

watch<string, true>(
  () => props.data.code,
  async function () {
    if (state.loading) return;
    handleStateRefresh();
  },
  { immediate: true, flush: "post" }
);

async function handleStateCreate(params: Partial<ItemData>) {
  await createItem(params);
  await handleStateRefresh();
}
async function handleStateEditor(params: Partial<ItemData>) {
  await editorItem(params);
  await handleStateRefresh();
}
async function handleStateDelete(params: Partial<ItemData>) {
  await deleteItem(params);
  await handleStateRefresh();
}
async function handleStateRefresh(onCleanup?: (cleanupFn: () => void) => void) {
  if (state.loading) return;
  state.loading = true;
  state.data.splice(0, state.data.length, ...(await querysItem({ platform: props.data.code }, onCleanup)));
  state.loading = false;
}
</script>

<style lang="scss" scoped></style>
