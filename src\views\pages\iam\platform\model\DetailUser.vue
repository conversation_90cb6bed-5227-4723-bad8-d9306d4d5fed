<template>
  <el-scrollbar>
    <div class="flex-search" :style="{ minWidth: `${props.width - 2}px` }">
      <div class="left"><slot name="left" :create="handleStateCreate" :delete="() => {}" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot></div>
      <div class="center"><slot name="center" :create="handleStateCreate" :delete="() => {}" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot></div>
      <div class="right"><slot name="right" :create="handleStateCreate" :delete="() => {}" :editor="handleStateEditor" :refresh="handleStateRefresh"></slot></div>
    </div>
  </el-scrollbar>
  <el-table v-loading="state.loading" :data="state.data" :height="props.height - 64 - 20 - (state.total ? 32 : 0)" :style="{ width: `${props.width - 40}px`, margin: '0 auto' }">
    <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :min-width="column.width || 120" :formatter="column.formatter" class-name="tw-text-gray-400" />
    <el-table-column :label="t('glob.operate')" align="left" header-align="left" :width="110" fixed="right">
      <template #header="{ column }">
        <div style="display: flex; justify-content: center">
          <span style="margin: 0 10px">{{ column.label }}</span>
          <el-link type="primary" :underline="false" :icon="Refresh" @click="handleStateRefresh()"></el-link>
        </div>
      </template>
      <template #default="{ row }">
        <!-- <el-button link type="default" :icon="View" @click="editorRef?.open({ '#TYPE': EditorType.Cat, ...row })">{{ t("glob.Cat") }}</el-button> -->
        <el-button link type="primary" :icon="Edit" :disabled="false" @click="handleStateEditor(row)" :title="t('glob.edit')"></el-button>
        <el-dropdown trigger="click" @command="$event.callback(row)" class="el-button el-button--default is-link" style="vertical-align: middle">
          <span style="font-size: var(--el-font-size-base)">
            <el-icon :title="t('glob.More')"><More /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item :command="{ callback: handleStateResetPassword }" :disabled="false">
                <el-icon class="el-icon--right"><Edit /></el-icon>
                重置密码
              </el-dropdown-item>
              <el-dropdown-item v-if="row.frozen === false" :command="{ callback: handleStateUserCutBlock }" :disabled="false">
                <el-icon class="el-icon--right"><Unlock /></el-icon>
                冻结用户
              </el-dropdown-item>
              <el-dropdown-item v-if="row.frozen === true" :command="{ callback: handleStateUserCutActive }" :disabled="false">
                <el-icon class="el-icon--right"><Lock /></el-icon>
                解冻用户
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </el-table-column>
  </el-table>
  <div :style="{ margin: '8px 20px 20px' }">
    <el-pagination v-show="state.total" v-model:current-page="state.page" v-model:page-size="state.size" :page-sizes="state.sizes" :total="state.total" layout="->, total, sizes, prev, pager, next, jumper" small @size-change="handleStateRefresh()" @current-change="handleStateRefresh()" />
  </div>
  <EditorDetailsByUser ref="editorRef" title="用户">
    <template #selectTenant="{ form }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">请选择一个租户</p>
      </div>
      <el-select v-model="form.tenantId" placeholder="请选择一个租户" class="tw-my-[20px] tw-w-full" clearable filterable>
        <el-option v-for="item in form.tenants" :key="item.id" :label="`${item.name}（${item.abbreviation}）`" :value="item.id"></el-option>
      </el-select>
    </template>
  </EditorDetailsByUser>
</template>

<script setup lang="ts" name="PlatformDetailClient">
import { ref, reactive, nextTick, watch, h } from "vue";
import { useI18n } from "vue-i18n";
import { sizes } from "@/utils/common";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { bindFormBox } from "@/utils/bindFormBox";
import { bufferToBase64, stringToBuffer } from "@/utils/base64";

// Ui
import { ElMessage, ElButton, ElMessageBox, ElTag, ElFormItem, ElInput, ElDatePicker, ElAvatar } from "element-plus";
// eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
import { Refresh, More, Edit, Unlock, Lock, InfoFilled } from "@element-plus/icons-vue";

// Api
import { getUser as getItem, addUserByTenant, addUser, modUser as modItem, cutUserByBlock, cutUserByActive } from "@/api/iam";
// import { genderOption } from "@/api/platform";
import { cutUserPasswordReset, getAvatar, getPublicKey } from "@/api/system";
import type { PlatformItem, UserItem as ItemData } from "@/api/iam";

// Editor
import { editorType, EditorType } from "@/views/common/interface";
import EditorDetailsByUser from "./EditorDetailsByUser.vue";

import moment from "@/lang/moment/zh-cn";
import { getTenant } from "@/api/iam";

interface Props {
  data: Pick<PlatformItem, "code" | "name" | "note" | "multiTenant"> & Record<string, unknown>;
  width: number;
  height: number;
}
const props = withDefaults(defineProps<Props>(), {
  data: () => ({
    code: "",
    name: "",
    note: "",
    multiTenant: false,
  }),
  width: 100,
  height: 300,
});

const addItem = props.data.multiTenant ? addUserByTenant : addUser;

const editorRef = ref<InstanceType<typeof EditorDetailsByUser>>();

async function createItem(params: Partial<ItemData>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  const baseParams = { tenantId: "" };
  try {
    if (props.data.multiTenant) {
      const { success, message, data } = await getTenant({ platform: props.data.code });
      if (!success) throw Object.assign(new Error(message), { success, data });
      await editorRef.value.confirm({ $slot: "selectTenant", $title: "选择一个租户", tenants: data instanceof Array ? data : [], ...baseParams }, async (form) => {
        if (!form.tenantId) throw new Error("请选择一个租户");
        Object.assign(baseParams, { tenantId: form.tenantId });
      });
      if (!baseParams.tenantId) throw void 0;
      // await addUserByTenant({ tenantId: "515400731272937472", name: "杨乾", ccount: "superhero", phone: "15287964970", email: "<EMAIL>", language: "", gender: "MALE", password: "I528796497O" });
    }
    await editorRef.value.open({ ...params, "platform": props.data.code, "#TYPE": EditorType.Add }, async (req) => {
      const { success, message, data } = await addItem({ ...req, ...baseParams, platform: props.data.code });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`${editorType[EditorType.Add]}成功！`);
    });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
}
async function editorItem(params: Partial<ItemData>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, "platform": props.data.code, "#TYPE": EditorType.Mod }, async (req) => {
      const { success, message, data } = await modItem({ id: params.id!, ...req });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`${editorType[EditorType.Mod]}成功！`);
    });
  } catch (error) {
    /*  */
  }
}
async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  let controller = new AbortController();
  const response = getItem({ platform: params.platform as string, ...state.search, controller, paging: { pageNumber: state.page, pageSize: state.size } });
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await response;
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);
      return data instanceof Array ? data : [];
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(20);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}

/*********************************************************/

const { t } = useI18n();

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode; showOverflowTooltip: boolean }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
// 设置table formater文字class
const state = reactive<StateData<ItemData>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {
    keyword: "",
    schemas: [].join(","),
  },
  column: [],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

watch<string, true>(
  () => props.data.code,
  async function () {
    if (state.loading) return;
    handleStateRefresh();
  },
  { immediate: true, flush: "post" }
);

async function handleStateCreate(params: Partial<ItemData>) {
  await createItem(params);
  await handleStateRefresh();
}
async function handleStateEditor(params: Partial<ItemData>) {
  await editorItem(params);
  await handleStateRefresh();
}
async function handleStateResetPassword(params: Partial<ItemData>) {
  try {
    await ElMessageBox({
      title: "重置密码",
      type: "info",
      message: "确定重置密码",
      showCancelButton: true,
      showConfirmButton: true,
      draggable: true,
      showInput: false,
      inputType: "password",
      inputValue: "",
      inputPlaceholder: "请输入新密码",
      inputPattern: /^((?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*?])|(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])|(?=.*[a-z])(?=.*[0-9])(?=.*[~!@#$%^&*?])|(?=.*[A-Z])(?=.*[0-9])(?=.*[~!@#$%^&*?])).{8,25}$/,
      inputErrorMessage: "密码为8-25位包含英文大小写、数字、特殊字符(~!@#$%^&*?)中的三种",
      async beforeClose(action, instance, done) {
        if (action === "confirm") {
          instance.confirmButtonLoading = true;
          instance.confirmButtonText = "Loading...";
          try {
            let _password: ArrayBuffer | null = null;
            if (instance.inputValue) {
              const importPublicKey = await getPublicKey();
              _password = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(instance.inputValue));
            }

            const { success, message, data } = await cutUserPasswordReset({ id: params.id as string, ...(_password ? { password: bufferToBase64(_password) } : {}), ptype: "RSA" });
            if (success) {
              done();
              ElMessage.success("密码重置成功");
            } else throw Object.assign(new Error(message), { success, data });
          } catch (error) {
            if (error instanceof Error) {
              instance.showConfirmButton = false;
              instance.showInput = false;
              instance.message = error.message;
              instance.type = "error";
            }
          } finally {
            instance.confirmButtonLoading = false;
          }
        } else {
          done();
        }
      },
    });
  } catch (error) {
    /*  */
  }
  await handleStateRefresh();
}
async function handleStateUserCutBlock(params: Partial<ItemData>) {
  const form = reactive<Record<"title" | "frozenNote" | "frozenExpire", string>>({
    title: "冻结用户",
    frozenNote: "",
    frozenExpire: "2099-01-01 00:00:00.000",
  });
  try {
    await bindFormBox(
      [
        /*  */
        h(ElFormItem, { rules: [{ required: true, message: "请选择冻结至时间", trigger: "blur" }], prop: "frozenExpire", label: "冻结至" }, () => h(ElDatePicker, { "modelValue": form.frozenExpire, "valueFormat": "YYYY-MM-DD HH:mm:ss.SSS", "onUpdate:modelValue": ($event) => (form.frozenExpire = $event) })),
        h(ElFormItem, { rules: [{ required: true, message: "请输入冻结备注", trigger: "blur" }], prop: "frozenNote", label: "冻结备注" }, () => h(ElInput, { "type": "textarea", "modelValue": form.frozenNote, "onUpdate:modelValue": ($event) => (form.frozenNote = $event) })),
      ],
      form,
      async () => {
        const { success, message, data } = await cutUserByBlock({ id: params.id as string, frozenNote: form.frozenNote, frozenExpire: form.frozenExpire });
        if (success) {
          ElMessage.success("用户冻结成功");
        } else throw Object.assign(new Error(message), { success, data });
        return { success, message };
      }
    );
  } catch (error) {
    /*  */
  }
  await handleStateRefresh();
}
async function handleStateUserCutActive(params: Partial<ItemData>) {
  const form = reactive<Record<"title", string>>({
    title: "解冻用户",
  });
  try {
    await bindFormBox([h("p", {}, "确定解冻用户吗？")], form, async () => {
      const { success, message, data } = await cutUserByActive({ id: params.id as string });
      if (success) {
        ElMessage.success("用户冻结成功");
      } else throw Object.assign(new Error(message), { success, data });
      return { success, message };
    });
  } catch (error) {
    /*  */
  }
  await handleStateRefresh();
}
async function handleStateRefresh() {
  if (props.data.multiTenant) {
    state.column = [
      {
        key: "name",
        label: "用户",
        width: 200,
        showOverflowTooltip: false,
        formatter: (row, _col, value) => h("div", { class: "tw-flex tw-items-center" }, [h(ElAvatar, { class: ["tw-flex-shrink-0", "tw-mr-[6px]"], size: 50 }, () => [h("img", { ref: ($el) => getAvatarSrc(row, $el as HTMLImageElement | undefined) })]), h("div", [h("p", { style: { color: "var(--el-text-color-primary)" } }, value as string), h("p", row.email)])]),
      },
      { key: "account", label: "账号", showOverflowTooltip: true },
      { key: "tenantName", label: "所属租户", showOverflowTooltip: true },
      { key: "phone", label: "手机号", showOverflowTooltip: true },
      { key: "email", label: "邮箱", showOverflowTooltip: true },
      { key: "blocked", label: "状态", formatter: (_row, _col, v) => (v ? h(ElTag, { type: "danger" /* , title: `冻结至${_row.frozenExpire}\n\r${_row.frozenNote}` */ }, () => t("glob.Frozen")) : _row.busy ? h(ElTag, { type: "warning" }, () => t("glob.Busy")) : h(ElTag, { type: "success" }, () => t("glob.Normal"))), showOverflowTooltip: true },
      { key: "lastLoginTime", label: "上次登录时间", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? moment(v as string, "x").fromNow() : "--") },
    ];
  } else {
    state.column = [
      {
        key: "name",
        label: "用户",
        width: 200,
        showOverflowTooltip: false,
        formatter: (row, _col, value) => h("div", { class: "tw-flex tw-items-center" }, [h(ElAvatar, { class: ["tw-flex-shrink-0", "tw-mr-[6px]"], size: 50 }, () => [h("img", { ref: ($el) => getAvatarSrc(row, $el as HTMLImageElement | undefined) })]), h("div", [h("p", { style: { color: "var(--el-text-color-primary)" } }, value as string), h("p", row.email)])]),
      },
      { key: "account", label: "账号", showOverflowTooltip: true },
      { key: "phone", label: "手机号", showOverflowTooltip: true },
      { key: "email", label: "邮箱", showOverflowTooltip: true },
      { key: "blocked", label: "状态", formatter: (_row, _col, v) => (v ? h(ElTag, { type: "danger" /* , title: `冻结至${_row.frozenExpire}\n\r${_row.frozenNote}` */ }, () => t("glob.Frozen")) : _row.busy ? h(ElTag, { type: "warning" }, () => t("glob.Busy")) : h(ElTag, { type: "success" }, () => t("glob.Normal"))), showOverflowTooltip: true },
      { key: "lastLoginTime", label: "上次登录时间", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? moment(v as string, "x").fromNow() : "--") },
    ];
  }
  if (state.loading) return;
  state.loading = true;
  state.data.splice(0, state.data.length, ...(await querysItem({ platform: props.data.code })));
  state.loading = false;
}

async function getAvatarSrc(row: ItemData, image?: HTMLImageElement) {
  if (image instanceof HTMLImageElement) {
    if (image.alt === row.profilePicture) return;
    image.alt = row.profilePicture as string;
    const src = await getAvatar({ filePath: row.profilePicture });
    image.src = src;
    image.onload = image.onerror = async () => URL.revokeObjectURL(src);
  }
}
</script>

<style lang="scss" scoped></style>
