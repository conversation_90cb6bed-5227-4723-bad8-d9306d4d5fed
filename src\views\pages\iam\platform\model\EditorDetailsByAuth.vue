<template>
  <!-- 对话框表单 -->
  <el-dialog v-model="data.visible" :close-on-click-modal="false" draggable :width="`${width}px`" :before-close="handleCancel">
    <template #header>
      <div class="title">{{ editorType[$params["#TYPE"]] }}{{ props.title }}</div>
    </template>
    <template #default>
      <FormModel ref="formRef" :height="height" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @resize.once="handleSize" @submit="handleFinish">
        <template v-if="[EditorType.Add, EditorType.Mod].includes($params['#TYPE'] || '')">
          <!--  -->
          <!--  -->
          <!--  -->
          <FormItem :span="24" label="平台编码" prop="platform" :rules="[buildValidatorData({ name: 'required', title: '平台编码' })]">
            <el-tag type="success" effect="dark">{{ form.platform }}</el-tag>
          </FormItem>
          <!--  -->
          <!--  -->
          <!--  -->
          <FormItem :span="width > 600 ? 12 : 24" label="名称" tooltip="" prop="name" :rules="[buildValidatorData({ name: 'required', title: '客户端名称' })]">
            <el-input v-model="form.name" type="text" :placeholder="t('glob.Please input field', { field: '客户端名称' })"></el-input>
          </FormItem>
          <!--  -->
          <!--  -->
          <!--  -->
          <FormItem :span="width > 600 ? 12 : 24" label="备注" tooltip="" prop="note" :rules="[]">
            <el-input v-model="form.note" type="text" :placeholder="t('glob.Please input field', { field: '客户端备注' })"></el-input>
          </FormItem>
          <!--  -->
          <!--  -->
          <!--  -->
          <FormItem :span="width > 600 ? 12 : 24" label="密钥" tooltip="客户端密钥，不输入则自动生成" prop="token" :rules="[]">
            <el-input v-model="form.token" :disabled="$params['#TYPE'] !== EditorType.Add" type="text" :placeholder="t('glob.Please input field', { field: '客户端密钥' })"></el-input>
          </FormItem>
          <!--  -->
          <!--  -->
          <!--  -->
          <FormItem :span="width > 600 ? 12 : 24" label="类型" tooltip="终端类型" prop="accessTokenValidity" :rules="[buildValidatorData({ name: 'required', title: '终端类型' })]">
            <el-select v-model="form.terminal" class="tw-w-full" value-key="" :placeholder="t('glob.Please select field', { field: '终端类型' })" filterable>
              <el-option v-for="item in terminalTypeOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </FormItem>
          <!--  -->
          <FormGroup :span="24" label="Token有效期" tooltip="授权端发放的Token有效期，过期Token则失效，单位（秒）">
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" label="Access" tooltip="Access Token 有效期，单位（秒）" prop="accessTokenValidity" :rules="[buildValidatorData({ name: 'required', title: 'Access Token 有效期' })]">
              <el-input-number v-model="form.accessTokenValidity" :min="0" :max="Number.MAX_SAFE_INTEGER" :step="1" :precision="0" :step-strictly="true" :controls="false" :placeholder="t('glob.Please input field', { field: 'AccessToken有效期' })"> </el-input-number>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" label="Refresh" tooltip="Refresh Token 有效期，单位（秒）" prop="refreshTokenValidity" :rules="[buildValidatorData({ name: 'required', title: 'Refresh Token 有效期' })]">
              <el-input-number v-model="form.refreshTokenValidity" :min="0" :max="Number.MAX_SAFE_INTEGER" :step="1" :precision="0" :step-strictly="true" :controls="false" :placeholder="t('glob.Please input field', { field: 'RefreshToken有效期' })"> </el-input-number>
            </FormItem>
            <!--  -->
          </FormGroup>
          <!--  -->
        </template>
      </FormModel>
    </template>
    <template #footer>
      <div>
        <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
        <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
        <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      </div>
      <div class="zoom-handle" @mousedown.self="handleZoom">
        <svg style="display: block; width: 60%; height: 60%; transform: translate(-25%, -25%); fill: currentColor; pointer-events: none" viewBox="0 0 1024 1024">
          <path d="M319.20128 974.56128L348.16 1003.52l655.36-655.36-28.95872-28.95872-655.36 655.36zM675.84 1003.52l327.68-327.68-28.95872-28.95872-327.68 327.68L675.84 1003.52z" fill="#000000"></path>
        </svg>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="PlatformEditor">
import { reactive, ref, nextTick, h, readonly, provide } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { buildValidatorData } from "@/utils/validate";
import { ElForm, ElMessage, ElMessageBox } from "element-plus";
import { EditorType, editorType } from "@/views/common/interface";
import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import { terminalType, terminalTypeOption, type AuthItem as ItemData } from "@/api/iam";
import { TypeHelper } from "@/utils/type";

type Item = Omit<ItemData, "createdTime" | "updatedTime" | "id" | "version" | "multiTenant">;

interface Props {
  title: string;
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  labelWidth: 116,
});

const formRef = ref<InstanceType<typeof ElForm>>();

const { t } = useI18n();

// const sizeRef = ref<HTMLDivElement>();
const width = ref(0);
const height = ref(0);

interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve: ((value: Partial<Item>) => void) | undefined;
  reject: ((value: Partial<Item>) => void) | undefined;
  callback: ((form: Partial<Item>) => Promise<boolean>) | undefined;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item> & { "#TYPE": EditorType; [key: string]: unknown }>({ "#TYPE": EditorType.Cat });

type DefaultForm<T> = { [P in keyof T]: { value: T[P]; test: (v: any) => v is T[P]; transfer: (fv: any, ov: T[P]) => T[P] } };
const defaultForm = readonly<DefaultForm<Required<Item>>>({
  platform: { value: "", ...TypeHelper.string },
  name: { value: "", ...TypeHelper.string },
  note: { value: "", ...TypeHelper.string },
  token: { value: "", ...TypeHelper.string },
  terminal: { value: "WEB", test: (v: any): v is terminalType => new RegExp(`^${Object.keys(terminalType).join("|")}$`, "g").test(v), transfer: (_, v) => v },
  accessTokenValidity: { value: 3600, ...TypeHelper.number },
  refreshTokenValidity: { value: 2592000, ...TypeHelper.number },
});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  form = cloneDeep(form);
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], util.value as never)) });
    },
    form as Required<Item>
  );
}
async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => {
      if (typeof formRef.value?.validate === "function") formRef.value.validate(resolve);
      else resolve(false);
    });
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") {
      const valid = await data.callback($form);
      if (!valid) throw new Error("Error");
    }

    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  formRef.value && formRef.value.clearValidate();
  form.value = await getForm($params.value);
  data.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

function handleZoom($event: MouseEvent) {
  const w = width.value;
  const h = height.value;
  ($event.target as HTMLElement).ownerDocument.onmousemove = (e: MouseEvent) => {
    e.preventDefault();
    if (w + (e.clientX - $event.clientX) * 2 < document.body.clientWidth - 200) width.value = w + (e.clientX - $event.clientX) * 2 > 360 ? w + (e.clientX - $event.clientX) * 2 : 360;
    else width.value = document.body.clientWidth - 200;
    if (h + (e.clientY - $event.clientY) * 1 < document.body.clientHeight - 260 - document.body.clientHeight * 0.15) height.value = h + (e.clientY - $event.clientY) * 1 > 24 ? h + (e.clientY - $event.clientY) * 1 : 24;
    else document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  };
  ($event.target as HTMLElement).ownerDocument.onmouseup = (e: MouseEvent) => {
    (e.target as HTMLElement).ownerDocument.onmousemove = null;
    (e.target as HTMLElement).ownerDocument.onmouseup = null;
  };
}

function handleSize(size: { width: number; height: number }) {
  const maxHeight = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  const formHeight = size.height || 24;
  height.value = Math.min(formHeight, maxHeight);
}

provide("#PARAMS", $params);
provide("#WIDTH", width);

defineExpose({
  close: handleCancel,
  open(params: Partial<Item> & { "#TYPE": EditorType; [key: string]: unknown }, callback?: (form: Partial<Item>) => Promise<boolean>) {
    switch (params["#TYPE"]) {
      case EditorType.Cat:
      case EditorType.Add:
      case EditorType.Mod: {
        if (data.visible) {
          return new Promise((resolve) => {
            ElMessage.warning("先关闭其他弹窗再重试！");
            resolve(params);
          });
        } else {
          $params.value = cloneDeep(params);
          data.visible = true;
          data.loading = true;
          data.submitLoading = true;
          data.callback = callback;
          return new Promise((resolve, reject) => {
            data.resolve = resolve;
            data.reject = reject;
            nextTick(async () => {
              width.value = document.body.clientWidth / 2;
              await nextTick();
              // if (formRef.value) {
              //   const maxHeight = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
              //   const formHeight = (formRef.value.$el as HTMLFormElement).clientHeight || 24;
              //   height.value = Math.max(formHeight, maxHeight);
              // }
              // if ([EditorType.Cat, EditorType.Mod].includes(params["#TYPE"])) {
              //   try {
              //     if (!params.id) throw new Error("找不到用户信息！");
              //     const { success, message, data } = await getUserById({ id: params.id });
              //     if (success) {
              //       Object.assign($params.value, data);
              //     } else Object.assign(new Error(message), { success, data });
              //   } catch (error) {
              //     await nextTick();
              //     if (error instanceof Error) ElMessage.error(error.message);
              //     return handleCancel();
              //     /*  */
              //   }
              // }
              await handleReset();
              data.loading = false;
              data.submitLoading = false;
            });
          });
        }
      }
      case EditorType.Del: {
        const option = reactive<{ message: string; valid: boolean; [key: string]: unknown }>({
          message: (params["#MESSAGE"] || `确认${editorType[params["#TYPE"]]}`) as string,
          valid: true,
        });
        return new Promise((resolve, reject) => {
          ElMessageBox({
            title: `${editorType[params["#TYPE"]]}${props.title}`,
            message() {
              return h("span", {}, [h("span", {}, option.message), h("span", { style: { margin: "0 3px", color: "var(--el-color-danger)" } }, params.name || "此"), option.valid ? h("span", {}, `${props.title}？`) : h("span", {}, `${props.title}删除失败！`)]);
            },
            type: "info",
            showCancelButton: true,
            showConfirmButton: true,
            cancelButtonText: t("glob.Cancel"),
            confirmButtonText: t("glob.delete"),
            distinguishCancelAndClose: true,
            draggable: true,
            async beforeClose(action, instance, done) {
              if (action === "confirm") {
                instance.confirmButtonLoading = true;
                try {
                  if (typeof callback === "function") option.valid = await callback(await getForm(params));
                  if (!option.valid) throw new Error("Error");
                  resolve(params);
                  done();
                } catch (error) {
                  option.message = "";
                  option.valid = false;
                  instance.showConfirmButton = false;
                  instance.type = "error";
                } finally {
                  instance.confirmButtonLoading = false;
                }
              } else {
                reject(params);
                done();
              }
            },
          })
            .then(async () => {})
            .catch(() => {
              reject(params);
            });
        });
      }
    }
  },
});
</script>

<style scoped lang="scss"></style>
