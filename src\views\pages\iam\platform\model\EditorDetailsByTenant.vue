<template>
  <!-- 对话框表单 -->
  <el-dialog v-model="data.visible" :close-on-click-modal="false" draggable :width="`${width}px`" :before-close="handleCancel">
    <template #header>
      <div class="title">{{ editorType[$params["#TYPE"]] }}{{ props.title }}</div>
    </template>
    <template #default>
      <el-scrollbar ref="contentRef" :height="height">
        <FormModel ref="formRef" :height="height" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @resize.once="handleSize" @submit="handleFinish">
          <template v-if="[EditorType.Add, EditorType.Mod].includes($params['#TYPE'] || '')">
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" label="平台编码" prop="platform" :rules="[buildValidatorData({ name: 'required', title: '平台编码' })]">
              <el-tag type="success" effect="dark">{{ form.platform }}</el-tag>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" :label="`受托租户`" tooltip="" prop="baileeTenantId" :rules="[]" @keyup.stop>
              <el-select v-model="form.baileeTenantId" :disabled="$params['#TYPE'] !== EditorType.Add" clearable filterable remote reserve-keyword default-first-option :placeholder="t('glob.Please input field', { field: '受托租户' })" :remote-method="tenant.remoteMethod" :loading="tenant.loading" class="tw-w-full">
                <el-option v-for="item in tenant.options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" :label="`${props.title}名称`" tooltip="" prop="name" :rules="[buildValidatorData({ name: 'required', title: `${props.title}名称` })]">
              <el-input v-model="form.name" type="text" clearable :placeholder="t('glob.Please input field', { field: `${props.title}名称` })"></el-input>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" :label="`${props.title}缩写`" tooltip="" prop="abbreviation" :rules="[buildValidatorData({ name: 'required', title: `${props.title}缩写` }), buildValidatorData({ name: 'ident', title: `${props.title}缩写` })]">
              <el-input v-model="form.abbreviation" type="text" clearable :placeholder="t('glob.Please input field', { field: `${props.title}缩写` })"></el-input>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" :label="`${props.title}系统版本`" tooltip="" prop="systemEdition" :rules="[]" @keyup.stop>
              <el-select v-model="form.systemEdition" :disabled="$params['#TYPE'] !== EditorType.Add" :placeholder="t('glob.Please select field', { field: '系统版本' })" filterable>
                <el-option v-for="item in props.systemEdition" :key="item.code" :label="item.name" :value="item.code"></el-option>
              </el-select>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="width > 600 ? 12 : 24" :label="`${props.title}语言`" tooltip="" prop="language" :rules="[]" @keyup.stop>
              <el-select v-model="form.language" :placeholder="t('glob.Please select field', { field: '语言' })" filterable>
                <el-option v-for="item in languageOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="24" :label="`${props.title}所在时区`" tooltip="" prop="zoneId" :rules="[]" @keyup.stop>
              <Timezone v-model="form.zoneId"></Timezone>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="24" :label="`${props.title}地址`" tooltip="" prop="address" :rules="[]">
              <el-input v-model="form.address" type="text" clearable :placeholder="t('glob.Please input field', { field: '地址' })"></el-input>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="24" :label="`${props.title}备注`" tooltip="" prop="note" :rules="[]">
              <el-input v-model="form.note" type="text" clearable :placeholder="t('glob.Please input field', { field: '备注' })"></el-input>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <!-- <FormGroup :span="24" :label="`${props.title}拥有人`" tooltip="填写后将在租户下自动创建一个用户，作为租户拥有人">
              <FormItem v-if="form.owner.id !== 'new'" :span="24" :label="`${props.title}拥有人`" tooltip="" prop="owner" :rules="[]" @keyup.stop>
                <el-select v-model="form.owner" :disabled="$params['#TYPE'] !== EditorType.Add" value-key="id" clearable filterable remote reserve-keyword default-first-option :placeholder="t('glob.Please input field', { field: '受托租户' })" :remote-method="owner.remoteMethod" :loading="owner.loading" class="tw-w-full">
                  <el-option v-for="item in owner.options" :key="item.id" :label="item.name || item.account || item.phone || item.email" :value="item" class="tw-h-fit">
                    <div class="tw-flex tw-items-center">
                      <div class="tw-mr-[24px] tw-h-[64px] tw-w-[40px] tw-flex-shrink-0 tw-py-[12px]">
                        <el-badge v-if="item.id === 'new'" value="new">
                          <el-avatar :icon="Plus" />
                        </el-badge>
                        <el-badge v-else :value="item.name || item.account || item.phone || item.email" type="primary">
                          <el-avatar :icon="UserFilled" />
                        </el-badge>
                      </div>
                      <el-row class="tw-w-full tw-flex-shrink tw-text-xs" :gutter="20">
                        <el-col :span="12" class="tw-py-[6px]">姓名：{{ item.name }}</el-col>
                        <el-col :span="12" class="tw-py-[6px]">账号：{{ item.account }}</el-col>
                        <el-col :span="12" class="tw-py-[6px]">手机号：{{ item.phone }}</el-col>
                        <el-col :span="12" class="tw-py-[6px]">邮箱：{{ item.email }}</el-col>
                        <el-col :span="12" class="tw-py-[6px]">性别：{{ (genderOption.find((v) => v.value === item.gender) || {}).label || "--" }}</el-col>
                      </el-row>
                    </div>
                  </el-option>
                </el-select>
              </FormItem>
              <OwnerSelect v-else v-model="form.owner" :disabled="$params['#TYPE'] !== EditorType.Add" prefix="owner" label="拥有人" :required="false" :platform="''" :width="width"></OwnerSelect>
            </FormGroup> -->
            <!--  -->
          </template>
        </FormModel>
      </el-scrollbar>
    </template>
    <template #footer>
      <div>
        <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
        <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
        <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      </div>
      <div class="zoom-handle" @mousedown.self="handleZoom">
        <svg style="display: block; width: 60%; height: 60%; transform: translate(-25%, -25%); fill: currentColor; pointer-events: none" viewBox="0 0 1024 1024">
          <path d="M319.20128 974.56128L348.16 1003.52l655.36-655.36-28.95872-28.95872-655.36 655.36zM675.84 1003.52l327.68-327.68-28.95872-28.95872-327.68 327.68L675.84 1003.52z" fill="#000000"></path>
        </svg>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="PlatformEditor">
import { reactive, ref, nextTick, h, readonly, provide } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { buildValidatorData } from "@/utils/validate";
import { ElForm, ElMessage, ElMessageBox } from "element-plus";
import { UserFilled, Plus } from "@element-plus/icons-vue";
import { EditorType, editorType } from "@/views/common/interface";
import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import OwnerSelect from "@/components/formItem/OwnerSelect.vue";
import { type TenantItem as ItemData, getTenant, getUser, gender, genderOption, language, languageOption } from "@/api/iam";
import { TypeHelper } from "@/utils/type";
import Timezone from "@/components/formItem/timezone/index.vue";
/* ============================================= */

interface TenantData {
  openerMethod: (id?: string) => void;
  remoteMethod: (keyword: string) => void;
  loading: boolean;
  default: { value: string; label: string; disabled?: boolean } | null;
  options: { value: string; label: string; disabled?: boolean }[];
}
const tenant = reactive<TenantData>({
  async openerMethod(id?: string) {
    if (tenant.loading) return;
    if (!$params.value.platform) return;
    try {
      tenant.loading = true;
      const { success, message, data } = await getTenant({ platform: $params.value.platform, ...(id ? { id } : {}), paging: { pageNumber: 1, pageSize: id ? 1 : 30 } });
      if (success && data instanceof Array) {
        const [item] = data;
        if (!item) return;
        const options = (data instanceof Array ? data : []).map((v) => ({ label: v.name, value: v.id }));
        tenant.default = { value: item.id, label: item.name };
        tenant.options = options.filter((v) => v.value !== $params.value.id);
      } else throw Object.assign(new Error(message), { success, data });
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      tenant.loading = false;
    }
  },
  async remoteMethod(keyword?: string) {
    if (tenant.loading) return;
    if (!keyword) {
      if (tenant.default) tenant.options = [tenant.default];
      return;
    }
    if (!$params.value.platform) return;
    try {
      tenant.loading = true;
      const { success, message, data } = await getTenant({ platform: $params.value.platform, keyword, paging: { pageNumber: 1, pageSize: 50 } });
      if (success) {
        const options = (data instanceof Array ? data : []).map((v) => ({ label: v.name, value: v.id }));
        if (tenant.default) options.push(tenant.default);
        tenant.options = options.filter((v) => v.value !== $params.value.id);
      } else throw Object.assign(new Error(message), { success, data });
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      tenant.loading = false;
    }
  },
  loading: false,
  default: null,
  options: [],
});
// interface OwnerData {
//   openerMethod: (id?: string) => void;
//   remoteMethod: (keyword: string) => void;
//   loading: boolean;
//   creater: boolean;
//   default: import("@/api/iam").UserBaseItem;
//   options: import("@/api/iam").UserBaseItem[];
// }
// const owner = reactive<OwnerData>({
//   async openerMethod(id?: string) {
//     if (owner.loading) return;
//     if (!id) return;
//     if (!$params.value.platform) return;
//     try {
//       owner.loading = true;
//       const { success, message, data } = await getUser({ platform: $params.value.platform, userId: id, paging: { pageNumber: 1, pageSize: 1 } });
//       if (success) {
//         if (data instanceof Array) {
//           const [item] = data;
//           if (item) owner.default = item;
//           owner.options = data instanceof Array && data.length ? data : [owner.default];
//         }
//       } else throw Object.assign(new Error(message), { success, data });
//     } catch (error) {
//       if (error instanceof Error) ElMessage.error(error.message);
//     } finally {
//       owner.loading = false;
//     }
//   },
//   async remoteMethod(keyword?: string) {
//     if (owner.loading) return;
//     if (!keyword) {
//       owner.options = [owner.default];
//       return;
//     }
//     if (!$params.value.platform) return;

//     const base: import("@/api/iam").UserBaseItem = { id: "new", keyword: "", name: "" /* , nickname: "" */, account: "", phone: "", email: "", gender: "SECRET", language: "none", password: "" };
//     if (/^(1[3-9])\d{9}$/.test(keyword)) {
//       base.phone = keyword;
//     } else if (/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/.test(keyword)) {
//       base.email = keyword;
//     } else {
//       base.account = keyword;
//     }

//     try {
//       owner.loading = true;
//       const { success, message, data } = await getUser({ platform: $params.value.platform, ident: keyword, paging: { pageNumber: 1, pageSize: 50 } });
//       if (success) {
//         if (data instanceof Array) {
//           owner.options = data;
//         }
//       } else throw Object.assign(new Error(message), { success, data });
//     } catch (error) {
//       if (error instanceof Error) ElMessage.error(error.message);
//     } finally {
//       owner.options.push(base);
//       owner.loading = false;
//     }
//   },
//   creater: false,
//   loading: false,
//   default: { platform: "", keyword: "", id: "", name: "" /* 姓名 */ /* , nickname: "" */ /* 昵称 */, account: "" /* 账号 */, phone: "" /* 手机号码 */, email: "" /* 邮箱 */, language: "none" /* 语言 */, gender: "SECRET" /* 性别 */, password: "" /* 密码 */ },
//   options: [],
// });

/* ============================================= */

type Item = Pick<ItemData, "platform" | "id" | "baileeTenantId" | "name" | "abbreviation" | "systemEdition" | "zoneId" | "language" | "address" | "note">;

interface Props {
  title: string;
  labelWidth?: number;
  systemEdition: { code: string; name: string }[];
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  labelWidth: 130,
  systemEdition: () => [],
});

const formRef = ref<InstanceType<typeof ElForm>>();

const { t } = useI18n();

// const sizeRef = ref<HTMLDivElement>();
const width = ref(0);
const height = ref(0);

interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve: ((value: Partial<ItemData>) => void) | undefined;
  reject: ((value: Partial<ItemData>) => void) | undefined;
  callback: ((form: Partial<ItemData>) => Promise<boolean>) | undefined;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item> & { "#TYPE": EditorType; [key: string]: unknown }>({ "#TYPE": EditorType.Cat });

type DefaultForm<T> = { [P in keyof T]: { value: T[P]; test: (v: any) => v is T[P]; transfer: (fv: any, ov: T[P]) => T[P] } };
const defaultForm = readonly<DefaultForm<Required<Item>>>({
  platform: { value: "", ...TypeHelper.string },
  id: { value: "", ...TypeHelper.string },
  name: { value: "", ...TypeHelper.string },
  baileeTenantId: { value: "", ...TypeHelper.string },
  note: { value: "", ...TypeHelper.string },
  abbreviation: { value: "", ...TypeHelper.string },
  systemEdition: { value: "", ...TypeHelper.string },
  zoneId: { value: "", ...TypeHelper.string },
  language: { value: "none", test: (v: any): v is Required<Item>["language"] => new RegExp(`^${Object.keys(language).join("|")}$`, "g").test(v), transfer: (_, v) => v },
  address: { value: "", ...TypeHelper.string },
  // owner: {
  //   value: {
  //     id: "",
  //     name: "" /* 姓名 */,
  //     // nickname: "" /* 昵称 */,
  //     account: "" /* 账号 */,
  //     phone: "" /* 手机号 */,
  //     email: "" /* 邮箱 */,
  //     language: language.none /* 语言 */,
  //     gender: gender.SECRET /* 性别 */,
  //     password: "" /* 密码 */,
  //   },
  //   test: (v: unknown): v is Required<Item>["owner"] => /^\[object\sObject\]$/g.test(Object.prototype.toString.call(v)),
  //   transfer: (v: unknown, f: Required<Item>["owner"]): typeof f => (/^\[object\sObject\]$/g.test(Object.prototype.toString.call(v)) ? (v as typeof f) : f),
  // },
});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<ItemData>> {
  type DefaultForm = typeof defaultForm;
  const _form = (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<ItemData>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], util.value as never)) });
    },
    cloneDeep(form) as Required<ItemData>
  );
  return { ..._form };
}
async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => {
      if (typeof formRef.value?.validate === "function") formRef.value.validate(resolve);
      else resolve(false);
    });
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") {
      const valid = await data.callback($form);
      if (!valid) throw new Error("Error");
    }

    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  formRef.value && formRef.value.clearValidate();
  const _form = await getForm($params.value);
  form.value = { ..._form };
  try {
    formRef.value && formRef.value.clearValidate();
    // await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }
  data.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

function handleZoom($event: MouseEvent) {
  const w = width.value;
  const h = height.value;
  ($event.target as HTMLElement).ownerDocument.onmousemove = (e: MouseEvent) => {
    e.preventDefault();
    if (w + (e.clientX - $event.clientX) * 2 < document.body.clientWidth - 200) width.value = w + (e.clientX - $event.clientX) * 2 > 360 ? w + (e.clientX - $event.clientX) * 2 : 360;
    else width.value = document.body.clientWidth - 200;
    if (h + (e.clientY - $event.clientY) * 1 < document.body.clientHeight - 260 - document.body.clientHeight * 0.15) height.value = h + (e.clientY - $event.clientY) * 1 > 24 ? h + (e.clientY - $event.clientY) * 1 : 24;
    else document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  };
  ($event.target as HTMLElement).ownerDocument.onmouseup = (e: MouseEvent) => {
    (e.target as HTMLElement).ownerDocument.onmousemove = null;
    (e.target as HTMLElement).ownerDocument.onmouseup = null;
  };
}

function handleSize(size: { width: number; height: number }) {
  const maxHeight = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  const formHeight = size.height || 24;
  height.value = Math.min(formHeight, maxHeight);
}

provide("#PARAMS", $params);
provide("#WIDTH", width);

defineExpose({
  close: handleCancel,
  open(params: Partial<ItemData> & { "#TYPE": EditorType; [key: string]: unknown }, callback?: (form: Partial<ItemData>) => Promise<boolean>) {
    switch (params["#TYPE"]) {
      case EditorType.Cat:
      case EditorType.Add:
      case EditorType.Mod: {
        if (data.visible) {
          return new Promise((resolve) => {
            ElMessage.warning("先关闭其他弹窗再重试！");
            resolve(params);
          });
        } else {
          $params.value = { ...params };
          data.visible = true;
          data.loading = true;
          data.submitLoading = true;
          data.callback = callback;
          return new Promise((resolve, reject) => {
            data.resolve = resolve;
            data.reject = reject;
            nextTick(async () => {
              width.value = document.body.clientWidth / 2;
              await nextTick();

              await Promise.all([/* owner.openerMethod(params.ownerId),  */ tenant.openerMethod(params.baileeTenantId)]);

              await handleReset();
              data.loading = false;
              data.submitLoading = false;
            });
          });
        }
      }
      case EditorType.Del: {
        const option = reactive<{ message: string; valid: boolean; [key: string]: unknown }>({
          message: (params["#MESSAGE"] || `确认${editorType[params["#TYPE"]]}`) as string,
          valid: true,
        });
        return new Promise((resolve, reject) => {
          ElMessageBox({
            title: `${editorType[params["#TYPE"]]}${props.title}`,
            message() {
              return h("span", {}, [h("span", {}, option.message), h("span", { style: { margin: "0 3px", color: "var(--el-color-danger)" } }, params.name || "此"), option.valid ? h("span", {}, `${props.title}？`) : h("span", {}, `${props.title}删除失败！`)]);
            },
            type: "info",
            showCancelButton: true,
            showConfirmButton: true,
            cancelButtonText: t("glob.Cancel"),
            confirmButtonText: t("glob.delete"),
            distinguishCancelAndClose: true,
            draggable: true,
            async beforeClose(action, instance, done) {
              if (action === "confirm") {
                instance.confirmButtonLoading = true;
                try {
                  if (typeof callback === "function") option.valid = await callback(await getForm(params));
                  if (!option.valid) throw new Error("Error");
                  resolve(params);
                  done();
                } catch (error) {
                  option.message = "";
                  option.valid = false;
                  instance.showConfirmButton = false;
                  instance.type = "error";
                } finally {
                  instance.confirmButtonLoading = false;
                }
              } else {
                reject(params);
                done();
              }
            },
          })
            .then(async () => {})
            .catch(() => {
              reject(params);
            });
        });
      }
    }
  },
});
</script>

<style scoped lang="scss"></style>
