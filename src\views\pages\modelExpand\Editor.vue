<template>
  <!-- 对话框表单 -->
  <component :is="ComponentRender" v-model:visible="data.visible" :handle-cancel="handleCancel">
    <template #header> {{ `${$params.id ? t("region.Assign", { value: $params.title }) : t("glob.add") + $params.title}` }} </template>
    <template #default="{}">
      <FormModel ref="formRef" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @submit="handleFinish">
        <FormItem :span="24" :label="t('devicesContacts.Contact Types')" tooltip="" prop="type" :rules="[]">
          <!-- <el-radio-group v-model="form.type" @change="resetFormInit($params), emits('change', $event as string)">
            <el-radio-button v-for="(item, i) in ((isArray($params.tabs) ? $params.tabs : []) as TabsType[])" :key="`${item.code}_${i}`" :label="item.code">{{ item.cnName }}</el-radio-button>
          </el-radio-group> -->
          <el-select v-model="form.type" style="width: 300px" disabled @change="nextTick(() => (resetFormInit($params), emits('change', $event as string)))">
            <el-option v-for="(item, i) in (isArray($params.tabs) ? $params.tabs : []) as TabsType[]" :key="`${item.code}_${i}`" :label="isEn ? item.enLabel : item.label" :value="item.code"> </el-option>
          </el-select>
        </FormItem>
        <FormItem v-if="form.type === 'Region'" :span="24" :label="$params.title as string" tooltip="" prop="itemIds" :rules="[]">
          <el-cascader style="width: 300px" :key="`cascader_${form.type}`" v-model="form.itemIds" :options="itemList" :props="{ multiple: true, checkStrictly: true, emitPath: false, value: 'id', label: 'name' }" :collapse-tags="false" collapse-tags-tooltip :show-all-levels="false"></el-cascader>
        </FormItem>
        <FormItem v-else-if="form.type != 'OnSite' && form.type != 'Technical' && form.type != 'Notification' && form.type != 'Resource'" :span="24" :label="$params.title as string" tooltip="" prop="itemIds" :rules="[]">
          <el-select :span="8" style="width: 300px" :key="`select_${form.type}`" v-model="form.itemIds" placeholder="" @change="$forceUpdate()" filterable multiple :collapse-tags="false" collapse-tags-tooltip default-first-option clearable>
            <el-option v-for="(item, i) in itemList" :key="`${form.type}_${item.id}_${i}`" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </FormItem>
        <FormItem v-else-if="form.type == 'Resource'" :span="24" :label="$params.title as string" tooltip="" prop="itemIds" :rules="[]">
          <el-select :span="8" style="width: 300px" :key="`select_${form.type}`" v-model="form.itemIds" placeholder="" @change="$forceUpdate()" filterable multiple :collapse-tags="false" collapse-tags-tooltip default-first-option clearable>
            <el-option v-for="(item, i) in itemList" v-show="item.show" :key="`${form.type}_${item.id}_${i}`" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </FormItem>

        <FormItem v-else :span="24" :label="$params.title as string" tooltip="" prop="itemIds" :rules="[]">
          <el-select :span="8" style="width: 300px" :key="`select_${form.type}`" v-model="form.itemIds" placeholder="" @change="$forceUpdate()" filterable multiple :collapse-tags="false" collapse-tags-tooltip default-first-option clearable>
            <el-option v-for="(item, i) in itemList" :key="`${form.type}_${item.id}_${i}`" :label="item.name" :value="item.id" :class="item.email && item.mobilePhone ? 'select_item' : item.email || item.mobilePhone ? 'select_item_once' : ''">
              <p>{{ item.name }}</p>
              <p v-if="item.email" style="color: #8492a6; font-size: 13px; display: block; margin-top: -10px; height: 20px">{{ item.email }}</p>
              <p v-if="item.mobilePhone && item.email" style="color: #8492a6; font-size: 13px; display: block; height: 20px">{{ item.mobilePhone }}</p>
              <p v-else style="color: #8492a6; font-size: 13px; display: block; margin-top: -10px; height: 20px">{{ item.mobilePhone }}</p>
            </el-option>
          </el-select>
        </FormItem>
      </FormModel>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </component>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, toRef, unref, nextTick, computed, h, inject, provide, createVNode, renderSlot, toRaw } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep, find, first, isArray, uniqBy } from "lodash-es";
import { UserFilled, Postcard, InfoFilled } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { templateRef } from "@vueuse/core";
import { buildTypeHelper } from "@/utils/type";
import { buildValidatorData } from "@/utils/validate";

import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import getUserInfo from "@/utils/getUserInfo";

import { getContactList, getDeviceQuery, getDeviceGroupList, getResourceTypeList, getLocationList, getRegionsList } from "@/views/pages/apis/device";
import { getContactsQuery } from "@/views/pages/apis/contacts";
import { getContactByResource } from "@/views/pages/apis/device";
import { 资产管理中心_设备_分配设备分组, 资产管理中心_设备_分配设备供应商, 资产管理中心_设备_分配设备类型, 服务管理中心_设备_分配告警分类, 资产管理中心_设备_分配行动策略, 智能事件中心_设备_分配工单 } from "@/views/pages/permission";
import { 资产管理中心_联系人_分配区域, 资产管理中心_联系人_分配场所, 资产管理中心_联系人_分配设备, 智能事件中心_联系人_分配工单 } from "@/views/pages/permission";
import { 服务管理中心_设备分组_分配告警分类 } from "@/views/pages/permission";
import { 服务管理中心_设备类型_分配告警分类 } from "@/views/pages/permission";
import { 资产管理中心_区域_分配行动策略 } from "@/views/pages/permission";
import { 资产管理中心_场所_分配行动策略 } from "@/views/pages/permission";
import { 智能事件中心_用户组_分配工单 } from "@/views/pages/permission";
import { 智能事件中心_用户_分配工单 } from "@/views/pages/permission";
import { 资产管理中心_客户_分配行动策略 } from "@/views/pages/permission";

// const $width = inject("width", ref(0));
// provide(
//   "width",
//   computed(() => $width.value * 0.6)
// );

const isEn = inject("isEn");

type TabsType = { [key: string]: unknown; code: string; label: string; type: string; items: Record<string, any>[] };

const userInfo = getUserInfo();

const formRef = templateRef<InstanceType<typeof FormModel>>("formRef");
/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Item {
  id: string;
  type: string;
  itemIds: string[];
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const itemList = ref<{ id: string; name: string; disabled: boolean; show?: boolean; [key: string]: any }[]>([]);
/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  id: buildTypeHelper<Required<Item>["id"]>(""),
  type: buildTypeHelper<Required<Item>["type"]>(""),
  itemIds: buildTypeHelper<Required<Item>["itemIds"]>([]),
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;

  const $active: Partial<TabsType> = find((isArray($params.value.tabs) ? $params.value.tabs : []) as TabsType[], (v) => v.code === form.value.type) || {};
  $params.value.title = isEn ? $active.enLabel : $active.label || "";
  $params.value.type = $active.code || "";
  $params.value.items = ($active.items || []) as { id: string; name: string; disabled: boolean }[];

  data.loading = true;
  itemList.value = [];

  form.value.itemIds = [];

  const $items = ($params.value.items as { id: string; name: string; disabled: boolean }[]).map((v) => v.id);
  switch ($params.value.type) {
    case "Location": {
      let pageNumber = 1;
      let pageSize = 300;
      let pageTotal = Infinity;
      const $data: { id: string; name: string; disabled: boolean }[] = [];
      while ((pageNumber - 1) * pageSize < pageTotal) {
        const { success, message, data, page, size, total } = await getLocationList({ paging: { pageNumber, pageSize }, queryPermissionId: props.type === "strategy" ? 资产管理中心_场所_分配行动策略 : "0" });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        pageNumber = Number(page) || 1;
        pageSize = Number(size) || 30;
        pageTotal = Number(total) || 0;
        if (data instanceof Array) $data.push(...data.map((v) => ({ id: v.id, name: v.name, disabled: $items.includes(v.id) })));
        pageNumber++;
      }
      itemList.value = $data;

      break;
    }
    case "Region": {
      const { success, message, data } = await getRegionsList({ queryPermissionId: props.type === "strategy" ? 资产管理中心_区域_分配行动策略 : "" });
      if (!success) throw Object.assign(new Error(message), { success, data });
      itemList.value = buildTree(data instanceof Array ? data : []) as { id: string; name: string; disabled: boolean }[];
      break;
    }
    case "Resource": {
      let pageNumber = 1;
      let pageSize = 300;
      let pageTotal = Infinity;
      const $data: { id: string; name: string; disabled: boolean }[] = [];
      while ((pageNumber - 1) * pageSize < pageTotal) {
        const { success, message, data, page, size, total } = await getDeviceQuery({ paging: { pageNumber, pageSize }, active: true, queryPermissionId: props.type === "group" ? 资产管理中心_设备_分配设备分组 : props.type === "vendor" ? 资产管理中心_设备_分配设备供应商 : props.type === "type" ? 资产管理中心_设备_分配设备类型 : props.type === "classifications" ? 服务管理中心_设备_分配告警分类 : props.type === "strategy" ? 资产管理中心_设备_分配行动策略 : "0" });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        pageNumber = Number(page) || 1;
        pageSize = Number(size) || 30;
        pageTotal = Number(total) || 0;
        if (data instanceof Array) $data.push(...data.map((v) => ({ id: v.id, name: v.name, disabled: $items.includes(v.id), show: v.active })));
        pageNumber++;
      }
      itemList.value = $data;

      break;
    }
    case "DeviceGroup": {
      let pageNumber = 1;
      let pageSize = 300;
      let pageTotal = Infinity;
      const $data: { id: string; name: string; disabled: boolean }[] = [];
      while ((pageNumber - 1) * pageSize < pageTotal) {
        const { success, message, data, page, size, total } = await getDeviceGroupList({ /* paging: { pageNumber, pageSize } */ queryPermissionId: props.type === "classifications" ? 服务管理中心_设备分组_分配告警分类 : "0" });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        pageNumber = Number(page) || 1;
        pageSize = Number(size) || 30;
        pageTotal = Number(total) || 0;
        if (data instanceof Array) $data.push(...data.map((v) => ({ id: v.id, name: v.name, disabled: $items.includes(v.id) })));
        pageNumber++;
      }
      itemList.value = $data;
      break;
    }
    case "DeviceType": {
      /*  */
      let pageNumber = 1;
      let pageSize = 300;
      let pageTotal = Infinity;
      const $data: { id: string; name: string; disabled: boolean }[] = [];
      while ((pageNumber - 1) * pageSize < pageTotal) {
        const { success, message, data, page, size, total } = await getResourceTypeList({ queryPermissionId: props.type === "classifications" ? 服务管理中心_设备类型_分配告警分类 : "0" });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        pageNumber = Number(page) || 1;
        pageSize = Number(size) || 30;
        pageTotal = Number(total) || 0;
        if (data instanceof Array) $data.push(...data.map((v) => ({ id: v.id, name: v.name, disabled: $items.includes(v.id) })));
        pageNumber++;
      }
      itemList.value = $data;
      break;
    }
    case "Tenant": {
      let pageNumber = 1;
      let pageSize = 300;
      let pageTotal = Infinity;
      const $data: { id: string; name: string; disabled: boolean }[] = [];

      const data = userInfo.tenants;

      if (data instanceof Array) $data.push(...data.map((v) => ({ id: v.id, name: `${v.name}[${v.abbreviation}]`, disabled: $items.includes(v.id) })));

      itemList.value = $data;
      break;
    }

    default: {
      let pageNumber = 1;
      let pageSize = 300;
      let pageTotal = Infinity;
      const $data: { id: string; name: string; disabled: boolean }[] = [];
      while ((pageNumber - 1) * pageSize < pageTotal) {
        const { success, message, data, page, size, total } = await getContactsQuery({ pageNumber: `${pageNumber}`, pageSize: `${pageSize}`, queryPermissionId: props.type === "regions" ? 资产管理中心_联系人_分配区域 : props.type === "location" ? 资产管理中心_联系人_分配场所 : props.type === "resource" || props.type === "resourceDetail" ? 资产管理中心_联系人_分配设备 : props.type === "event" ? 智能事件中心_联系人_分配工单 : "0" });
        if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
        pageNumber = Number(page) || 1;
        pageSize = Number(size) || 30;
        pageTotal = Number(total) || 0;
        if (data instanceof Array) $data.push(...data.map((v) => ({ id: v.id, name: v.name, disabled: $items.includes(v.id), email: v.email, mobilePhone: v.mobilePhone })));
        pageNumber++;
      }
      itemList.value = $data;
      break;
    }
  }
  itemList.value = itemList.value.filter((v) => $items.every((key) => key !== v.id));

  data.loading = false;
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  return form;
}
/* ---------------------------------------------------------‖ ↑↑ 钩子 Start ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE ACHIEVE  END  ========================================================= */
/**
 * TODO: 本地方法
 */
interface Tree {
  children: Tree[];
  id: string;
  parentId: string;
  disabled?: boolean;
  [key: string]: any;
}
function buildTree<T extends Tree>(data: T[]): T[] {
  for (let index = 0; index < data.length; index++) {
    Object.assign(data[index], { disabled: form.value.itemIds.includes(data[index].id) });
    if (!(data[index].children instanceof Array)) data[index].children = [];
    data[index].children.splice(0, data[index].children.length, ...data.filter((v) => v.parentId === data[index].id).map((v) => Object.assign(v, { isBuild: true })));
  }
  return data.filter((v) => {
    if ("isBuild" in v) {
      delete v.isBuild;
      return false;
    } else return true;
  });
}
/*  */
/**
 * TODO: 窗口方法
 */
interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
  type: "resource" | "regions" | "location" | "group" | "vendor" | "type" | "classifications" | "strategy" | "event" | "contact" | "resourceDetail" | "globalStrategy";
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 120,
});

interface Emits {
  ($event: "change", value: string): void;
}
const emits = defineEmits<Emits>();

const { t } = useI18n();

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<Item>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Partial<Item>) => void;
  reject?: (value: Partial<Item>) => void;
  callback?: (form: Item) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item & Record<string, unknown>>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }

  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], cloneDeep(toRaw(util.value)) as never)) });
    },
    form as Required<Item>
  );
}
async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => formRef.value.validate(resolve));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();

defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, callback?: (form: Item) => Promise<void>): Promise<unknown> {
    if (data.visible) handleCancel();
    const wait = new Promise<Record<string, unknown>>((resolve, reject) => ((data.resolve = resolve), (data.reject = reject)));
    $params.value = cloneDeep(params);
    data.visible = true;
    data.loading = true;
    data.submitLoading = true;
    data.callback = callback;
    await nextTick();
    try {
      await runningInit($params.value);
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      handleCancel();
    }
    handleReset();
    data.loading = false;
    data.submitLoading = false;

    try {
      return await wait;
    } catch (error) {
      return error;
    }
  },
  async confirm<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.confirm(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async alert<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.alert(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
});
</script>

<style scoped lang="scss">
.select_item {
  // min-height: 30px !important;
  height: 80px !important;
  // line-height: 25px;
  font-size: 12px;
}
.select_item_once {
  height: 60px !important;
}

// 防止表单标签换行
:deep(.el-form-item__label) {
  white-space: nowrap !important;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
