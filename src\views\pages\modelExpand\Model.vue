<template>
  <!-- <el-tabs v-model="active" type="card" class="tw-mx-[8px]" v-if="!showContact">
    <el-tab-pane v-for="tab in tabs" :key="tab.code" :label="tab.label" :name="tab.code" v-show="tab.type !== tabsTypeEnum.contact">
      <el-row v-loading="loading" :gutter="16" class="tw-mx-[2px]" v-if="tab.type !== tabsTypeEnum.contact">
        <el-col v-if="props.allocation" :span="24" class="tw-mb-[16px] tw-flex">
          <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.create[tab.type] || false)">
            <span class="tw-ml-auto tw-h-fit">
              <el-button type="primary" :disabled="props.create[tab.type] || false" @click="addData(tab.items)">分配{{ tab.label }}</el-button>
            </span>
          </el-tooltip>
        </el-col>
        <template v-if="!tab.items.length">
          <el-col :span="24">
            <el-empty class="tw-pt-0" :description="`没有找到与此相关的${tab.label}!`" :image-size="120" />
          </el-col>
        </template>
<template v-for="item in tab.items" :key="item.id">
          <el-col v-if="tab.type === tabsTypeEnum.contact" :xs="12" :sm="8" :md="8" :lg="6" :xl="4" class="tw-mb-[16px]">
            <el-card shadow="never" :body-style="{ padding: '6px 20px' }" :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
              <template #header>
                <div class="tw-flex tw-items-center">
                  <el-icon color="var(--el-color-primary)" :size="16" class="tw-mr-1"><Postcard /></el-icon>
                  <el-text type="primary" class="tw-mr-auto tw-w-[calc(100%_-_90px)] tw-text-[14px] tw-leading-[16px]" truncated :title="item.name">{{ item.name }}</el-text>

                  <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.viewer[tab.type] || false)">
                    <span class="tw-ml-auto tw-h-fit">
                      <el-link type="danger" :underline="false" :disabled="props.viewer[tab.type]" @click="previewRef && previewRef.open(item)">{{ $t("glob.Cat") }}</el-link>
                    </span>
                  </el-tooltip>
                  <template v-if="props.allocation">
                    <el-popconfirm :width="200" :disabled="props.remove[tab.type] || false" :title="`确定${$t('glob.remove')} ${item.name} ${tab.label}?`" :confirm-button-text="$t('glob.remove')" confirm-button-type="primary" @confirm="delData(item.id, tab.type)">
                      <template #reference>
                        <span>
                          <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.remove[tab.type] || false)">
                            <span class="tw-ml-auto tw-h-fit">
                              <el-link type="danger" :underline="false" :disabled="props.remove[tab.type] || false" class="tw-ml-[8px]">{{ $t("glob.remove") }}</el-link>
                            </span>
                          </el-tooltip>
                        </span>
                      </template>
</el-popconfirm>
</template>
</div>
</template>
<template #default>
                <div class="tw-flex tw-h-[24px] tw-items-center" title="固定电话">
                  <el-icon class="tw-mr-2"><Phone /></el-icon>
                  <el-text type="info" class="tw-text-[14px]">{{ item.landlinePhone || "--" }}</el-text>
                </div>
                <div class="tw-flex tw-h-[24px] tw-items-center" title="移动电话">
                  <el-icon class="tw-mr-2"><Iphone /></el-icon>
                  <el-text type="info" class="tw-text-[14px]">{{ item.mobilePhone || "--" }}</el-text>
                </div>
                <div class="tw-flex tw-h-[24px] tw-items-center" title="邮箱">
                  <el-icon class="tw-mr-2"><Message /></el-icon>
                  <el-text type="info" class="tw-text-[14px]">{{ item.email || "--" }}</el-text>
                </div>
              </template>
</el-card>
</el-col>
<el-col v-else-if="tab.type === tabsTypeEnum.resource" :xs="12" :sm="8" :md="8" :lg="6" :xl="4" class="tw-mb-[16px]">
  <el-card shadow="never" :body-style="{ padding: '6px 20px' }"
    :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
    <template #header>
                <div class="tw-flex tw-items-center">
                  <el-icon color="var(--el-color-primary)" :size="16" class="tw-mr-1"><Monitor /></el-icon>
                  <el-text type="primary" class="tw-mr-auto tw-w-[calc(100%_-_90px)] tw-text-[14px] tw-leading-[16px]" truncated :title="item.name">{{ item.name }}</el-text>

                  <template v-if="props.allocation">
                    <el-popconfirm :width="200" :disabled="props.remove[tab.type] || false" :title="`确定${$t('glob.remove')} ${item.name} ${tab.label}?`" :confirm-button-text="$t('glob.remove')" confirm-button-type="primary" @confirm="delData(item.id, tab.type)">
                      <template #reference>
                        <span>
                          <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.remove[tab.type] || false)">
                            <span class="tw-ml-auto tw-h-fit">
                              <el-link type="danger" :underline="false" :disabled="props.remove[tab.type] || false" class="tw-ml-[8px]">{{ $t("glob.remove") }}</el-link>
                            </span>
                          </el-tooltip>
                        </span>
                      </template>
    </el-popconfirm>
    </template>
    </div>
    </template>
    <template #default>
                <div class="tw-flex tw-h-[24px] tw-items-center">
                  <el-text type="info" class="tw-text-[14px]">IP: {{ (item.config || {}).ipAddress || "--" }}</el-text>
                </div>
                <div class="tw-flex tw-min-h-[24px] tw-items-center">
                  <el-text type="info" class="tw-text-[14px]">
                    <pre>{{ item.description || "--" }}</pre>
                  </el-text>
                </div>
              </template>
  </el-card>
</el-col>
<el-col v-else-if="tab.type === tabsTypeEnum.group" :xs="12" :sm="8" :md="8" :lg="6" :xl="4" class="tw-mb-[16px]">
  <el-card shadow="never" :body-style="{ padding: '6px 20px' }"
    :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
    <template #header>
                <div class="tw-flex tw-items-center">
                  <el-icon color="var(--el-color-primary)" :size="16" class="tw-mr-1"><Grid /></el-icon>
                  <el-text type="primary" class="tw-mr-auto tw-w-[calc(100%_-_90px)] tw-text-[14px] tw-leading-[16px]" truncated :title="item.name">{{ item.name }}</el-text>

                  <template v-if="props.allocation">
                    <el-popconfirm :width="200" :disabled="props.remove[tab.type] || false" :title="`确定${$t('glob.remove')} ${item.name} ${tab.label}?`" :confirm-button-text="$t('glob.remove')" confirm-button-type="primary" @confirm="delData(item.id, tab.type)">
                      <template #reference>
                        <span>
                          <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.remove[tab.type] || false)">
                            <span class="tw-ml-auto tw-h-fit">
                              <el-link type="danger" :underline="false" :disabled="props.remove[tab.type] || false" class="tw-ml-[8px]">{{ $t("glob.remove") }}</el-link>
                            </span>
                          </el-tooltip>
                        </span>
                      </template>
    </el-popconfirm>
    </template>
    </div>
    </template>
    <template #default>
                <div class="tw-flex tw-min-h-[24px] tw-items-center">
                  <el-text type="info" class="tw-text-[14px]">
                    <pre>{{ item.description || "--" }}</pre>
                  </el-text>
                </div>
              </template>
  </el-card>
</el-col>
<el-col v-else-if="tab.type === tabsTypeEnum.type" :xs="12" :sm="8" :md="8" :lg="6" :xl="4" class="tw-mb-[16px]">
  <el-card shadow="never" :body-style="{ padding: '6px 20px' }"
    :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
    <template #header>
                <div class="tw-flex tw-items-center">
                  <el-icon color="var(--el-color-primary)" :size="16" class="tw-mr-1"><Menu /></el-icon>
                  <el-text type="primary" class="tw-mr-auto tw-w-[calc(100%_-_90px)] tw-text-[14px] tw-leading-[16px]" truncated :title="item.name">{{ item.name }}</el-text>

                  <template v-if="props.allocation">
                    <el-popconfirm :width="200" :disabled="props.remove[tab.type] || false" :title="`确定${$t('glob.remove')} ${item.name} ${tab.label}?`" :confirm-button-text="$t('glob.remove')" confirm-button-type="primary" @confirm="delData(item.id, tab.type)">
                      <template #reference>
                        <span>
                          <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.remove[tab.type] || false)">
                            <span class="tw-ml-auto tw-h-fit">
                              <el-link type="danger" :underline="false" :disabled="props.remove[tab.type] || false" class="tw-ml-[8px]">{{ $t("glob.remove") }}</el-link>
                            </span>
                          </el-tooltip>
                        </span>
                      </template>
    </el-popconfirm>
    </template>
    </div>
    </template>
    <template #default>
                <div class="tw-flex tw-min-h-[24px] tw-items-center">
                  <el-text type="info" class="tw-text-[14px]">
                    <pre>{{ item.description || "--" }}</pre>
                  </el-text>
                </div>
              </template>
  </el-card>
</el-col>
<el-col v-else-if="tab.type === tabsTypeEnum.location" :xs="12" :sm="8" :md="8" :lg="6" :xl="4" class="tw-mb-[16px]">
  <el-card shadow="never" :body-style="{ padding: '6px 20px' }"
    :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
    <template #header>
                <div class="tw-flex tw-items-center">
                  <el-icon color="var(--el-color-primary)" :size="16" class="tw-mr-1"><Location /></el-icon>
                  <el-text type="primary" class="tw-mr-auto tw-w-[calc(100%_-_90px)] tw-text-[14px] tw-leading-[16px]" truncated :title="item.name">{{ item.name }}</el-text>

                  <template v-if="props.allocation">
                    <el-popconfirm :width="200" :disabled="props.remove[tab.type] || false" :title="`确定${$t('glob.remove')} ${item.name} ${tab.label}?`" :confirm-button-text="$t('glob.remove')" confirm-button-type="primary" @confirm="delData(item.id, tab.type)">
                      <template #reference>
                        <span>
                          <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.remove[tab.type] || false)">
                            <span class="tw-ml-auto tw-h-fit">
                              <el-link type="danger" :underline="false" :disabled="props.remove[tab.type] || false" class="tw-ml-[8px]">{{ $t("glob.remove") }}</el-link>
                            </span>
                          </el-tooltip>
                        </span>
                      </template>
    </el-popconfirm>
    </template>
    </div>
    </template>
    <template #default>
                <div class="tw-flex tw-min-h-[24px] tw-items-center">
                  <el-text type="info" class="tw-text-[14px]">
                    <pre>{{ item.description || "--" }}</pre>
                  </el-text>
                </div>
              </template>
  </el-card>
</el-col>
<el-col v-else-if="tab.type === tabsTypeEnum.region" :xs="12" :sm="8" :md="8" :lg="6" :xl="4" class="tw-mb-[16px]">
  <el-card shadow="never" :body-style="{ padding: '6px 20px' }"
    :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
    <template #header>
                <div class="tw-flex tw-items-center">
                  <el-icon color="var(--el-color-primary)" :size="16" class="tw-mr-1"><MapLocation /></el-icon>
                  <el-text type="primary" class="tw-mr-auto tw-w-[calc(100%_-_90px)] tw-text-[14px] tw-leading-[16px]" truncated :title="item.name">{{ item.name }}</el-text>

                  <template v-if="props.allocation">
                    <el-popconfirm :width="200" :disabled="props.remove[tab.type] || false" :title="`确定${$t('glob.remove')} ${item.name} ${tab.label}?`" :confirm-button-text="$t('glob.remove')" confirm-button-type="primary" @confirm="delData(item.id, tab.type)">
                      <template #reference>
                        <span>
                          <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.remove[tab.type] || false)">
                            <span class="tw-ml-auto tw-h-fit">
                              <el-link type="danger" :underline="false" :disabled="props.remove[tab.type] || false" class="tw-ml-[8px]">{{ $t("glob.remove") }}</el-link>
                            </span>
                          </el-tooltip>
                        </span>
                      </template>
    </el-popconfirm>
    </template>
    </div>
    </template>
    <template #default>
                <div class="tw-flex tw-h-[24px] tw-items-center">
                  <el-text type="info" class="tw-text-[14px]">标签: {{ item.landlinePhone || "--" }}</el-text>
                </div>
                <div class="tw-flex tw-min-h-[24px] tw-items-center">
                  <el-text type="info" class="tw-text-[14px]">
                    <pre>{{ item.description || "--" }}</pre>
                  </el-text>
                </div>
              </template>
  </el-card>
</el-col>
</template>
</el-row>
</el-tab-pane>
</el-tabs> -->
  <div v-loading="loading">
    <div v-for="tab in tabs" :key="tab.code">
      <el-row :gutter="16" class="tw-mx-[2px]">
        <el-col :span="12" class="tw-mb-[16px] tw-flex">
          <h3 style="font-weight: 400; font-size: 14px; color: #000">
            {{ isEn ? tab.enLabel : tab.label }}
          </h3>
        </el-col>

        <el-col :span="12" class="tw-mb-[16px] tw-flex">
          <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.create[tab.type] || false)">
            <span class="tw-ml-auto tw-h-fit">
              <el-button v-if="props.showAdd" type="primary" :disabled="props.create[tab.type] || false" @click="addData(tab.items, tab.code, tab.type)">{{ $t("glob.Allocate the", { value: isEn ? tab.enLabel : tab.label }) }}</el-button>
            </span>
          </el-tooltip>
        </el-col>
        <template v-for="item in tab.items" :key="item.id">
          <el-col v-if="tab.type === tabsTypeEnum.contact" :xs="12" :sm="12" :md="12" :lg="8" :xl="6" class="tw-mb-[16px]" :span="8">
            <el-card shadow="never" class="" :body-style="{ padding: '6px 20px' }" :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
              <template #header>
                <div class="tw-flex tw-items-center">
                  <el-icon color="var(--el-color-primary)" :size="16" class="tw-mr-1">
                    <Postcard />
                  </el-icon>
                  <el-text type="primary" class="tw-mr-auto tw-w-[calc(100%_-_265px)] tw-text-[14px] tw-leading-[16px]" truncated :title="item.name">{{ item.name }}</el-text>
                  <div style="display: flex; align-items: center">
                    <div style="margin-right: 2px" v-for="itemA in localesOption" :key="itemA.value">
                      <div v-if="itemA.value == item.language" :style="{ background: `url(${itemA.icon}) no-repeat left / auto`, paddingLeft: '30px' }">{{ itemA.label }}</div>
                    </div>
                    <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.viewer[tab.type] || false)">
                      <span class="tw-ml-auto tw-h-fit">
                        <el-link type="danger" :underline="false" :disabled="props.viewer[tab.type]" @click="previewRef && previewRef.open(item)">{{ $t("glob.Cat") }}</el-link>
                      </span>
                    </el-tooltip>
                    <template v-if="props.allocation && props.showRemove">
                      <el-popconfirm :width="200" :disabled="props.remove[tab.type] || false" :title="`${$t('glob.Confirm')} ${$t('glob.remove')} ${item.name} ${isEn ? tab.enLabel : tab.label}?`" :confirm-button-text="$t('glob.remove')" confirm-button-type="primary" @confirm="delData(item.id, tab.type, tab.code)">
                        <template #reference>
                          <span>
                            <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.remove[tab.type] || false)">
                              <span class="tw-ml-auto tw-h-fit">
                                <el-link type="danger" :underline="false" :disabled="props.remove[tab.type] || false" class="tw-ml-[8px]">{{ $t("glob.remove") }}</el-link>
                              </span>
                            </el-tooltip>
                          </span>
                        </template>
                      </el-popconfirm>
                    </template>
                  </div>
                </div>
              </template>
              <template #default>
                <div class="tw-flex tw-h-[24px] tw-items-center" title="固定电话">
                  <el-icon class="tw-mr-2">
                    <Phone />
                  </el-icon>
                  <el-text type="info" class="tw-text-[14px]">{{ item.landlinePhone || "--" }}</el-text>
                </div>
                <div class="tw-flex tw-h-[24px] tw-items-center" title="移动电话">
                  <el-icon class="tw-mr-2">
                    <Iphone />
                  </el-icon>
                  <el-text type="info" class="tw-text-[14px]">{{ item.smsPhone || "--" }}</el-text>
                </div>
                <div class="tw-flex tw-h-[24px] tw-items-center" title="邮箱">
                  <el-icon class="tw-mr-2">
                    <Message />
                  </el-icon>
                  <el-text type="info" class="tw-text-[14px]">{{ item.email || "--" }}</el-text>
                </div>
              </template>
            </el-card>
          </el-col>
          <el-col v-else-if="tab.type === tabsTypeEnum.resource" :xs="12" :sm="8" :md="8" :lg="6" :xl="4" class="tw-mb-[16px]">
            <el-card shadow="never" :body-style="{ padding: '6px 20px' }" :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
              <template #header>
                <div class="tw-flex tw-items-center">
                  <el-icon color="var(--el-color-primary)" :size="16" class="tw-mr-1">
                    <Monitor />
                  </el-icon>
                  <el-text type="primary" class="tw-mr-auto tw-w-[calc(100%_-_90px)] tw-text-[14px] tw-leading-[16px]" truncated :title="item.name">{{ item.name }}</el-text>
                  <template v-if="props.allocation">
                    <el-popconfirm :width="200" :disabled="props.remove[tab.type] || false" :title="`确定${$t('glob.remove')} ${item.name} ${tab.label}?`" :confirm-button-text="$t('glob.remove')" confirm-button-type="primary" @confirm="delData(item.id, tab.type)">
                      <template #reference>
                        <span>
                          <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.remove[tab.type] || false)">
                            <span class="tw-ml-auto tw-h-fit">
                              <el-link type="danger" :underline="false" :disabled="props.remove[tab.type] || false" class="tw-ml-[8px]">{{ $t("glob.remove") }}</el-link>
                            </span>
                          </el-tooltip>
                        </span>
                      </template>
                    </el-popconfirm>
                  </template>
                </div>
              </template>
              <template #default>
                <div class="tw-flex tw-h-[24px] tw-items-center">
                  <el-text type="info" class="tw-text-[14px]">IP: {{ (item.config || {}).ipAddress || "--" }}</el-text>
                </div>
                <div class="tw-flex tw-min-h-[24px] tw-items-center">
                  <el-text type="info" class="tw-text-[14px]">
                    <pre>{{ item.description || "--" }}</pre>
                  </el-text>
                </div>
              </template>
            </el-card>
          </el-col>
          <el-col v-else-if="tab.type === tabsTypeEnum.group" :xs="12" :sm="8" :md="8" :lg="6" :xl="4" class="tw-mb-[16px]">
            <el-card shadow="never" :body-style="{ padding: '6px 20px' }" :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
              <template #header>
                <div class="tw-flex tw-items-center">
                  <el-icon color="var(--el-color-primary)" :size="16" class="tw-mr-1">
                    <Grid />
                  </el-icon>
                  <el-text type="primary" class="tw-mr-auto tw-w-[calc(100%_-_90px)] tw-text-[14px] tw-leading-[16px]" truncated :title="item.name">{{ item.name }}</el-text>
                  <template v-if="props.allocation">
                    <el-popconfirm :width="200" :disabled="props.remove[tab.type] || false" :title="`确定${$t('glob.remove')} ${item.name} ${tab.label}?`" :confirm-button-text="$t('glob.remove')" confirm-button-type="primary" @confirm="delData(item.id, tab.type)">
                      <template #reference>
                        <span>
                          <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.remove[tab.type] || false)">
                            <span class="tw-ml-auto tw-h-fit">
                              <el-link type="danger" :underline="false" :disabled="props.remove[tab.type] || false" class="tw-ml-[8px]">{{ $t("glob.remove") }}</el-link>
                            </span>
                          </el-tooltip>
                        </span>
                      </template>
                    </el-popconfirm>
                  </template>
                </div>
              </template>
              <template #default>
                <div class="tw-flex tw-min-h-[24px] tw-items-center">
                  <el-text type="info" class="tw-text-[14px]">
                    <pre>{{ item.description || "--" }}</pre>
                  </el-text>
                </div>
              </template>
            </el-card>
          </el-col>
          <el-col v-else-if="tab.type === tabsTypeEnum.type" :xs="12" :sm="8" :md="8" :lg="6" :xl="4" class="tw-mb-[16px]">
            <el-card shadow="never" :body-style="{ padding: '6px 20px' }" :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
              <template #header>
                <div class="tw-flex tw-items-center">
                  <el-icon color="var(--el-color-primary)" :size="16" class="tw-mr-1">
                    <Menu />
                  </el-icon>
                  <el-text type="primary" class="tw-mr-auto tw-w-[calc(100%_-_90px)] tw-text-[14px] tw-leading-[16px]" truncated :title="item.name">{{ item.name }}</el-text>
                  <template v-if="props.allocation">
                    <el-popconfirm :width="200" :disabled="props.remove[tab.type] || false" :title="`确定${$t('glob.remove')} ${item.name} ${tab.label}?`" :confirm-button-text="$t('glob.remove')" confirm-button-type="primary" @confirm="delData(item.id, tab.type)">
                      <template #reference>
                        <span>
                          <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.remove[tab.type] || false)">
                            <span class="tw-ml-auto tw-h-fit">
                              <el-link type="danger" :underline="false" :disabled="props.remove[tab.type] || false" class="tw-ml-[8px]">{{ $t("glob.remove") }}</el-link>
                            </span>
                          </el-tooltip>
                        </span>
                      </template>
                    </el-popconfirm>
                  </template>
                </div>
              </template>
              <template #default>
                <div class="tw-flex tw-min-h-[24px] tw-items-center">
                  <el-text type="info" class="tw-text-[14px]">
                    <pre>{{ item.description || "--" }}</pre>
                  </el-text>
                </div>
              </template>
            </el-card>
          </el-col>
          <el-col v-else-if="tab.type === tabsTypeEnum.location" :xs="12" :sm="8" :md="8" :lg="6" :xl="4" class="tw-mb-[16px]">
            <el-card shadow="never" :body-style="{ padding: '6px 20px' }" :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
              <template #header>
                <div class="tw-flex tw-items-center">
                  <el-icon color="var(--el-color-primary)" :size="16" class="tw-mr-1">
                    <Location />
                  </el-icon>
                  <el-text type="primary" class="tw-mr-auto tw-w-[calc(100%_-_90px)] tw-text-[14px] tw-leading-[16px]" truncated :title="item.name">{{ item.name }}</el-text>
                  <template v-if="props.allocation">
                    <el-popconfirm :width="200" :disabled="props.remove[tab.type] || false" :title="`确定${$t('glob.remove')} ${item.name} ${tab.label}?`" :confirm-button-text="$t('glob.remove')" confirm-button-type="primary" @confirm="delData(item.id, tab.type)">
                      <template #reference>
                        <span>
                          <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.remove[tab.type] || false)">
                            <span class="tw-ml-auto tw-h-fit">
                              <el-link type="danger" :underline="false" :disabled="props.remove[tab.type] || false" class="tw-ml-[8px]">{{ $t("glob.remove") }}</el-link>
                            </span>
                          </el-tooltip>
                        </span>
                      </template>
                    </el-popconfirm>
                  </template>
                </div>
              </template>
              <template #default>
                <div class="tw-flex tw-min-h-[24px] tw-items-center">
                  <el-text type="info" class="tw-text-[14px]">
                    <pre>{{ item.address.length > 0 ? item.address.reduce((accumulator: string, currentValue: string) => accumulator + "-" + currentValue) || "--" : "--" }}</pre>
                  </el-text>
                </div>
              </template>
            </el-card>
          </el-col>
          <el-col v-else-if="tab.type === tabsTypeEnum.region" :xs="12" :sm="8" :md="8" :lg="6" :xl="4" class="tw-mb-[16px]">
            <el-card shadow="never" :body-style="{ padding: '6px 20px' }" :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
              <template #header>
                <div class="tw-flex tw-items-center">
                  <el-icon color="var(--el-color-primary)" :size="16" class="tw-mr-1">
                    <MapLocation />
                  </el-icon>
                  <el-text type="primary" class="tw-mr-auto tw-w-[calc(100%_-_90px)] tw-text-[14px] tw-leading-[16px]" truncated :title="item.name">{{ item.name }}</el-text>
                  <template v-if="props.allocation">
                    <el-popconfirm :width="200" :disabled="props.remove[tab.type] || false" :title="`确定${$t('glob.remove')} ${item.name} ${tab.label}?`" :confirm-button-text="$t('glob.remove')" confirm-button-type="primary" @confirm="delData(item.id, tab.type)">
                      <template #reference>
                        <span>
                          <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.remove[tab.type] || false)">
                            <span class="tw-ml-auto tw-h-fit">
                              <el-link type="danger" :underline="false" :disabled="props.remove[tab.type] || false" class="tw-ml-[8px]">{{ $t("glob.remove") }}</el-link>
                            </span>
                          </el-tooltip>
                        </span>
                      </template>
                    </el-popconfirm>
                  </template>
                </div>
              </template>
              <template #default>
                <div class="tw-flex tw-h-[24px] tw-items-center">
                  <el-text type="info" class="tw-text-[14px]">标签: {{ item.label || "--" }}</el-text>
                </div>
                <div class="tw-flex tw-min-h-[24px] tw-items-center">
                  <el-text type="info" class="tw-text-[14px]">
                    <pre>{{ item.description || "--" }}</pre>
                  </el-text>
                </div>
              </template>
            </el-card>
          </el-col>
          <el-col v-else-if="tab.type === tabsTypeEnum.tenant" :xs="12" :sm="8" :md="8" :lg="6" :xl="4" class="tw-mb-[16px]">
            <el-card shadow="never" :body-style="{ padding: '6px 20px' }" :style="{ '--el-card-bg-color': 'var(--el-fill-color-light)', '--el-card-padding': '14px' }">
              <template #header>
                <div class="tw-flex tw-items-center">
                  <el-icon color="var(--el-color-primary)" :size="16" class="tw-mr-1">
                    <Stamp />
                  </el-icon>
                  <el-text type="primary" class="tw-mr-auto tw-w-[calc(100%_-_90px)] tw-text-[14px] tw-leading-[16px]" truncated :title="item.name">{{ item.tenantName + `[${item.tenantAbbreviation}]` }}</el-text>
                  <template v-if="props.allocation">
                    <el-popconfirm :width="200" :disabled="props.remove[tab.type] || false" :title="`确定${$t('glob.remove')} ${item.tenantName} ${tab.label}?`" :confirm-button-text="$t('glob.remove')" confirm-button-type="primary" @confirm="delData(item.tenantId, tab.type)">
                      <template #reference>
                        <span>
                          <el-tooltip :content="$t('glob.noPower')" :disabled="!(props.remove[tab.type] || false)">
                            <span class="tw-ml-auto tw-h-fit">
                              <el-link type="danger" :underline="false" :disabled="props.remove[tab.type] || false" class="tw-ml-[8px]">{{ $t("glob.remove") }}</el-link>
                            </span>
                          </el-tooltip>
                        </span>
                      </template>
                    </el-popconfirm>
                  </template>
                </div>
              </template>
            </el-card>
          </el-col>
        </template>
      </el-row>
    </div>
  </div>

  <Editor ref="editorRef" title="联系人" display="dialog" :type="props.type" @change="active = $event"></Editor>
  <Preview ref="previewRef" title="联系人" display="dialog"></Preview>
</template>

<script setup lang="ts">
import { ref, shallowRef, watch, computed, provide } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
// import getUserInfo from "@/utils/getUserInfo";
import { Postcard, Monitor, Menu, Grid, Location, MapLocation, Iphone, Phone, Message, Stamp } from "@element-plus/icons-vue";
import { getContactByResource, addContactByResource, delContactByResource, getContactByResourceDetails } from "@/views/pages/apis/device";
import { getContactByRegions, addContactByRegions, delContactByRegions } from "@/views/pages/apis/device";
import { getContactByLocation, addContactByLocation, delContactByLocation } from "@/views/pages/apis/device";
import { getDeviceList, getDeviceGroupList, getResourceTypeList, getLocationList, getRegionsList } from "@/views/pages/apis/device";
// import {} from "@/views/pages/apis/deviceManage.ts"
import { addResourcesByGroup, delResourcesByGroup } from "@/views/pages/apis/device";
import { addResourcesByVendor, delResourcesByVendor } from "@/views/pages/apis/device";
import { addResourcesByType, delResourcesByType } from "@/views/pages/apis/device";
import { addResourcesByStrategy, delResourcesByStrategy, addLocationByStrategy, delLocationByStrategy, addRegionByStrategy, delRegionByStrategy } from "@/views/pages/apis/device";
import { addResourcesByClassifications, delResourcesByClassifications, addDeviceGroupByClassifications, delDeviceGroupByClassifications, addTypeByClassifications, delTypeByClassifications } from "@/views/pages/apis/device";
import { contactGroupState, deviceGroupState, groupGroupState, typeGroupState, locationState, regionState, tenantState, tabsTypeEnum } from "./store";
import { Support_notesGlobalRelationTenant, Support_notesDelGlobalRelationTenant, getSupport_noteslGlobalTenant } from "@/views/pages/apis/supportNotes";
import Editor from "./Editor.vue";
import Preview from "./Preview.vue";
import { find } from "lodash-es";
import getUserInfo from "@/utils/getUserInfo";
import { localesOption } from "@/api/locale.ts";

const { t, locale } = useI18n();
const isEn = computed(() => locale.value === "en");
provide("isEn", isEn.value);
const editorRef = shallowRef<InstanceType<typeof Editor>>();
const previewRef = shallowRef<InstanceType<typeof Preview>>();

type TabsType = { [key: string]: unknown; code: string; label: string; type: tabsTypeEnum; items: Record<string, any>[] };
const tabs = ref<TabsType[]>([]);
const active = ref("");
const loading = ref(false);

const showContact = ref(false);

const userInfo = getUserInfo();

interface Props {
  id: string;
  create?: Partial<Record<keyof typeof tabsTypeEnum, boolean>>;
  remove?: Partial<Record<keyof typeof tabsTypeEnum, boolean>>;
  viewer?: Partial<Record<keyof typeof tabsTypeEnum, boolean>>;
  allocation?: boolean;
  type: "resource" | "regions" | "location" | "group" | "vendor" | "type" | "classifications" | "strategy" | "event" | "contact" | "resourceDetail" | "globalStrategy";
  showAdd?: boolean;
  showRemove?: boolean;
  tenantIds?: string[];
}
const props = withDefaults(defineProps<Props>(), { id: "", create: () => ({}), remove: () => ({}), viewer: () => ({}), allocation: true, showAdd: true, showRemove: true, tenantIds: () => [] });

watch(() => props.id, getData, { immediate: true });

async function getData() {
  loading.value = true;
  try {
    switch (props.type) {
      case tabsTypeEnum.resource:
        await (async () => {
          if (!props.id) return;
          const [{ success, message, data }, { success: itemSuccess, message: itemMessage, data: itemData }] = await Promise.all([contactGroupState(), getContactByResource({ id: props.id })]);
          if (!success) throw Object.assign(new Error(message), { success, data });
          if (!data.map((v) => v.code).includes(active.value)) active.value = data.map((v) => v.code)[0] || "";
          if (!itemSuccess) throw Object.assign(new Error(itemMessage), { success: itemSuccess, data: itemData });
          tabs.value.splice(0, tabs.value.length, ...data.map((v) => ({ code: v.code, label: v.cnName, type: tabsTypeEnum.contact, items: itemData.filter(({ contactType }) => v.code === contactType).map((v) => v.contact), enLabel: v.enName }) as TabsType));
          showContact.value = true;
        })();
        break;

      case "resourceDetail":
        await (async () => {
          if (!props.id) return;
          const [{ success, message, data }, { success: itemSuccess, message: itemMessage, data: itemData }] = await Promise.all([contactGroupState(), getContactByResourceDetails({ id: props.id })]);
          if (!success) throw Object.assign(new Error(message), { success, data });
          if (!data.map((v) => v.code).includes(active.value)) active.value = data.map((v) => v.code)[0] || "";
          if (!itemSuccess) throw Object.assign(new Error(itemMessage), { success: itemSuccess, data: itemData });
          tabs.value.splice(0, tabs.value.length, ...data.map((v) => ({ code: v.code, label: v.cnName, type: tabsTypeEnum.contact, items: itemData.filter(({ contactType }) => v.code === contactType).map((v) => v.contact), enLabel: v.enName }) as TabsType));
          showContact.value = true;
        })();
        break;

      case "regions":
        await (async () => {
          if (!props.id) return;
          const [{ success, message, data }, { success: itemSuccess, message: itemMessage, data: itemData }] = await Promise.all([contactGroupState(), getContactByRegions({ id: props.id })]);
          if (!success) throw Object.assign(new Error(message), { success, data });
          if (!data.map((v) => v.code).includes(active.value)) active.value = data.map((v) => v.code)[0] || "";
          if (!itemSuccess) throw Object.assign(new Error(itemMessage), { success: itemSuccess, data: itemData });
          tabs.value.splice(0, tabs.value.length, ...data.map((v) => ({ code: v.code, label: v.cnName, type: tabsTypeEnum.contact, items: itemData.filter(({ contactType }) => v.code === contactType).map((v) => v.contact), enLabel: v.enName }) as TabsType));
          showContact.value = true;
        })();
        break;
      case tabsTypeEnum.location:
        await (async () => {
          if (!props.id) return;
          const [{ success, message, data }, { success: itemSuccess, message: itemMessage, data: itemData }] = await Promise.all([contactGroupState(), getContactByLocation({ id: props.id })]);
          if (!success) throw Object.assign(new Error(message), { success, data });
          if (!data.map((v) => v.code).includes(active.value)) active.value = data.map((v) => v.code)[0] || "";
          if (!itemSuccess) throw Object.assign(new Error(itemMessage), { success: itemSuccess, data: itemData });
          tabs.value.splice(0, tabs.value.length, ...data.map((v) => ({ code: v.code, label: v.cnName, type: tabsTypeEnum.contact, items: itemData.filter(({ contactType }) => v.code === contactType).map((v) => v.contact), enLabel: v.enName }) as TabsType));
          showContact.value = true;
        })();
        break;
      case tabsTypeEnum.group:
        await (async () => {
          if (!props.id) return;
          const [{ success, message, data }, itemData] = await Promise.all([deviceGroupState(), getAllPagingApi(getDeviceList, { groupId: props.id, active: true })]);
          if (!success) throw Object.assign(new Error(message), { success, data });
          if (!data.map((v) => v.code).includes(active.value)) active.value = data.map((v) => v.code)[0] || "";
          tabs.value.splice(0, tabs.value.length, ...data.map((v) => ({ code: v.code, label: v.label, type: v.type, items: itemData }) as TabsType));
        })();
        break;
      case "vendor":
        await (async () => {
          if (!props.id) return;
          const [{ success, message, data }, itemData] = await Promise.all([deviceGroupState(), getAllPagingApi(getDeviceList, { vendorId: props.id, active: true })]);
          if (!success) throw Object.assign(new Error(message), { success, data });
          if (!data.map((v) => v.code).includes(active.value)) active.value = data.map((v) => v.code)[0] || "";
          tabs.value.splice(0, tabs.value.length, ...data.map((v) => ({ code: v.code, label: v.label, type: v.type, items: itemData }) as TabsType));
        })();
        break;
      case tabsTypeEnum.type:
        await (async () => {
          if (!props.id) return;
          const [{ success, message, data }, itemData] = await Promise.all([deviceGroupState(), getAllPagingApi(getDeviceList, { resourceTypeId: props.id, active: true })]);
          if (!success) throw Object.assign(new Error(message), { success, data });
          if (!data.map((v) => v.code).includes(active.value)) active.value = data.map((v) => v.code)[0] || "";
          tabs.value.splice(0, tabs.value.length, ...data.map((v) => ({ code: v.code, label: v.label, type: v.type, items: itemData }) as TabsType));
        })();
        break;
      case "classifications" /* 告警分类 */:
        if (userInfo.hasPermission("509623239069138944")) {
          await (async () => {
            if (!props.id) return;

            const data = await Promise.all([
              deviceGroupState().then(async ({ success, message, data: [data] }) => {
                if (!success) throw Object.assign(new Error(message), { success, data });

                return { ...data, items: await getAllPagingApi(getDeviceList, { alertClassificationId: props.id, active: true }) };
              }),
              groupGroupState().then(async ({ success, message, data: [data] }) => {
                if (!success) throw Object.assign(new Error(message), { success, data });
                return { ...data, items: await getAllPagingApi(getDeviceGroupList, { alertClassificationId: props.id }) };
              }),
              typeGroupState().then(async ({ success, message, data: [data] }) => {
                if (!success) throw Object.assign(new Error(message), { success, data });
                return { ...data, items: await getAllPagingApi(getResourceTypeList, { alertClassificationId: props.id }) };
              }),
            ]);
            if (!data.map((v) => v.code).includes(active.value)) active.value = data.map((v) => v.code)[0] || "";
            tabs.value.splice(0, tabs.value.length, ...data.map((v) => ({ code: v.code, label: v.label, type: v.type, items: v.items }) as TabsType));
          })();
        }

        break;
      case "strategy":
        await (async () => {
          if (!props.id) return;
          const data = await Promise.all([
            deviceGroupState().then(async ({ success, message, data: [data] }) => {
              if (!success) throw Object.assign(new Error(message), { success, data });
              return { ...data, items: await getAllPagingApi(getDeviceList, { supportNoteId: props.id, active: true }) };
            }),
            locationState().then(async ({ success, message, data: [data] }) => {
              if (!success) throw Object.assign(new Error(message), { success, data });
              return { ...data, items: await getAllPagingApi(getLocationList, { supportNoteId: props.id }) };
            }),
            regionState().then(async ({ success, message, data: [data] }) => {
              if (!success) throw Object.assign(new Error(message), { success, data });
              return { ...data, items: await getAllPagingApi(getRegionsList, { supportNoteId: props.id }) };
            }),
          ]);
          if (!data.map((v) => v.code).includes(active.value)) active.value = data.map((v) => v.code)[0] || "";
          tabs.value.splice(0, tabs.value.length, ...data.map((v) => ({ code: v.code, label: v.label, type: v.type, items: v.items }) as TabsType));
        })();
        break;
      case "event":
        await (async () => {
          if (!props.id) return;
          const [{ success, message, data }, { success: itemSuccess, message: itemMessage, data: itemData }] = await Promise.all([contactGroupState(), getContactByResource({ id: props.id })]);
          if (!success) throw Object.assign(new Error(message), { success, data });
          if (!data.map((v) => v.code).includes(active.value)) active.value = data.map((v) => v.code)[0] || "";
          if (!itemSuccess) throw Object.assign(new Error(itemMessage), { success: itemSuccess, data: itemData });
          tabs.value.splice(0, tabs.value.length, ...data.map((v) => ({ code: v.code, label: v.cnName, type: tabsTypeEnum.contact, items: itemData.filter(({ contactType }) => v.code === contactType).map((v) => v.contact), enLabel: v.enName }) as TabsType));
          showContact.value = true;
        })();
        break;

      // case "globalStrategy":
      //   await (async () => {
      //     if (!props.id) return;
      //     const [{ success, message, data }, { success: itemSuccess, message: itemMessage, data: itemData }] = await Promise.all([tenantState(), getSupport_noteslGlobalTenant({ id: props.id })]);
      //     if (!success) throw Object.assign(new Error(message), { success, data });
      //     if (!data.map((v) => v.code).includes(active.value)) active.value = data.map((v) => v.code)[0] || "";
      //     if (!itemSuccess) throw Object.assign(new Error(itemMessage), { success: itemSuccess, data: itemData });
      //     tabs.value.splice(0, tabs.value.length, ...data.map((v) => ({ code: v.code, label: v.label, type: tabsTypeEnum.contact, items: itemData }) as TabsType));
      //     showContact.value = true;
      //   })();
      //   break;
    }
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : `${error}`);
  }
  loading.value = false;

  return;

  async function getAllPagingApi<T, P extends { paging?: { pageNumber: number; pageSize: number }; [key: string]: any }>(api: (req: P) => Promise<import("@/api/service/common").Response<T[]>>, req: Omit<P, "paging">) {
    let pageNumber = 1;
    let pageSize = 300;
    let pageTotal = Infinity;
    const $data: T[] = [];
    while ((pageNumber - 1) * pageSize < pageTotal) {
      const { success, message, data, page, size, total } = await api({ ...req, paging: { pageNumber, pageSize } } as P);
      if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
      pageNumber = Number(page) || 1;
      pageSize = Number(size) || data.length;
      pageTotal = Number(total) || data.length;
      if (data instanceof Array) $data.push(...data);
      pageNumber++;
    }
    return $data;
  }
}
async function addData(items: Record<string, any>[], code: string, type: string) {
  if (!editorRef.value) return;
  try {
    const $active = find(tabs.value, (v) => v.code === active.value)!;
    if (!$active) return;
    await editorRef.value.open({ id: props.id, tabs: tabs.value, title: isEn.value ? $active.enLabel || $active.label : $active.label, type: code ? code : active.value, items }, async (form) => {
      const $items = tabs.value.filter((v) => v.code === form.type).reduce((p, c) => p.concat(c.items.map((v) => v.id)), <string[]>[]);
      for (let index = 0; index < $items.length; index++) {
        const $i = form.itemIds.indexOf($items[index]);
        if ($i !== -1) form.itemIds.splice($i, 1);
      }
      if (form.itemIds.length) {
        switch (props.type) {
          case "resource": {
            if (type === tabsTypeEnum.contact) {
              const { success, message, data } = await addContactByResource({ id: props.id, contactType: form.type, contactIds: form.itemIds });
              if (!success) throw Object.assign(new Error(message), { success, data });
            }
            break;
          }
          case "regions": {
            if (type === tabsTypeEnum.contact) {
              const { success, message, data } = await addContactByRegions({ id: props.id, contactType: form.type, contactIds: form.itemIds });
              if (!success) throw Object.assign(new Error(message), { success, data });
            }
            break;
          }
          case "location": {
            if (type === tabsTypeEnum.contact) {
              const { success, message, data } = await addContactByLocation({ id: props.id, contactType: form.type, contactIds: form.itemIds });
              if (!success) throw Object.assign(new Error(message), { success, data });
            }
            break;
          }
          case "group": {
            if (type === tabsTypeEnum.resource) {
              const { success, message, data } = await addResourcesByGroup({ id: props.id, resourceIds: form.itemIds });
              if (!success) throw Object.assign(new Error(message), { success, data });
            }
            break;
          }
          case "vendor": {
            if (type === tabsTypeEnum.resource) {
              const { success, message, data } = await addResourcesByVendor({ id: props.id, resourceIds: form.itemIds });
              if (!success) throw Object.assign(new Error(message), { success, data });
            }
            break;
          }
          case "type": {
            if (type === tabsTypeEnum.resource) {
              const { success, message, data } = await addResourcesByType({ id: props.id, resourceIds: form.itemIds });
              if (!success) throw Object.assign(new Error(message), { success, data });
            }
            break;
          }
          case "classifications": {
            if (type === tabsTypeEnum.resource) {
              const { success, message, data } = await addResourcesByClassifications({ id: props.id, resourceIds: form.itemIds });
              if (!success) throw Object.assign(new Error(message), { success, data });
            }
            if (type === tabsTypeEnum.group) {
              const { success, message, data } = await addDeviceGroupByClassifications({ alertClassificationIds: [props.id], ids: form.itemIds });
              if (!success) throw Object.assign(new Error(message), { success, data });
            }
            if (type === tabsTypeEnum.type) {
              const { success, message, data } = await addTypeByClassifications({ alertClassificationIds: [props.id], ids: form.itemIds });
              if (!success) throw Object.assign(new Error(message), { success, data });
            }
            break;
          }
          case "strategy": {
            if (type === tabsTypeEnum.resource) {
              const { success, message, data } = await addResourcesByStrategy({ id: props.id, resourceIds: form.itemIds });
              if (!success) throw Object.assign(new Error(message), { success, data });
            }
            if (type === tabsTypeEnum.location) {
              const { success, message, data } = await addLocationByStrategy({ supportNoteIds: [props.id], ids: form.itemIds });
              if (!success) throw Object.assign(new Error(message), { success, data });
            }
            if (type === tabsTypeEnum.region) {
              const { success, message, data } = await addRegionByStrategy({ supportNoteIds: [props.id], ids: form.itemIds });
              if (!success) throw Object.assign(new Error(message), { success, data });
            }
            break;
          }
          case "globalStrategy": {
            const { success, message, data } = await Support_notesGlobalRelationTenant({ id: props.id, tenantIds: form.itemIds });
            if (!success) throw Object.assign(new Error(message), { success, data });
            break;
          }
        }
      }
    });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    await getData();
  }
}
async function delData(id: string, type: tabsTypeEnum, contactType?: string) {
  loading.value = true;
  try {
    switch (props.type) {
      case "resource": {
        if (type === tabsTypeEnum.contact) {
          const { success, message, data } = await delContactByResource({ id: props.id, contactType: contactType ? contactType : active.value, contactId: id });
          if (!success) throw Object.assign(new Error(message), { success, data });
        }
        break;
      }
      case "regions": {
        if (type === tabsTypeEnum.contact) {
          const { success, message, data } = await delContactByRegions({ id: props.id, contactType: contactType ? contactType : active.value, contactId: id });
          if (!success) throw Object.assign(new Error(message), { success, data });
        }
        break;
      }
      case "location": {
        if (type === tabsTypeEnum.contact) {
          const { success, message, data } = await delContactByLocation({ id: props.id, contactType: contactType ? contactType : active.value, contactId: id });
          if (!success) throw Object.assign(new Error(message), { success, data });
        }
        break;
      }
      case "group": {
        if (type === tabsTypeEnum.resource) {
          const { success, message, data } = await delResourcesByGroup({ id: props.id, resourceIds: [id] });
          if (!success) throw Object.assign(new Error(message), { success, data });
        }
        break;
      }
      case "vendor": {
        if (type === tabsTypeEnum.resource) {
          const { success, message, data } = await delResourcesByVendor({ id: props.id, resourceIds: [id] });
          if (!success) throw Object.assign(new Error(message), { success, data });
        }
        break;
      }
      case "type": {
        if (type === tabsTypeEnum.resource) {
          const { success, message, data } = await delResourcesByType({ id: props.id, resourceIds: [id] });
          if (!success) throw Object.assign(new Error(message), { success, data });
        }
        break;
      }
      case "classifications": {
        if (type === tabsTypeEnum.resource) {
          const { success, message, data } = await delResourcesByClassifications({ id: props.id, resourceIds: [id] });
          if (!success) throw Object.assign(new Error(message), { success, data });
        }
        if (type === tabsTypeEnum.group) {
          const { success, message, data } = await delDeviceGroupByClassifications({ alertClassificationIds: [props.id], ids: [id] });
          if (!success) throw Object.assign(new Error(message), { success, data });
        }
        if (type === tabsTypeEnum.type) {
          const { success, message, data } = await delTypeByClassifications({ alertClassificationIds: [props.id], ids: [id] });
          if (!success) throw Object.assign(new Error(message), { success, data });
        }
        break;
      }
      case "strategy": {
        if (type === tabsTypeEnum.resource) {
          const { success, message, data } = await delResourcesByStrategy({ id: props.id, resourceIds: [id] });
          if (!success) throw Object.assign(new Error(message), { success, data });
        }
        if (type === tabsTypeEnum.location) {
          const { success, message, data } = await delLocationByStrategy({ supportNoteIds: [props.id], ids: [id] });
          if (!success) throw Object.assign(new Error(message), { success, data });
        }
        if (type === tabsTypeEnum.region) {
          const { success, message, data } = await delRegionByStrategy({ supportNoteIds: [props.id], ids: [id] });
          if (!success) throw Object.assign(new Error(message), { success, data });
        }
        break;
      }
      case "globalStrategy": {
        const { success, message, data } = await Support_notesDelGlobalRelationTenant({ id: props.id, tenantIds: [id] });
        if (!success) throw Object.assign(new Error(message), { success, data });
        break;
      }
    }
    ElMessage.success(`${t("axios.Operation successful")}`);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loading.value = false;

    await getData();
  }
}
</script>

<style scoped lang="scss">
.card_contact {
  width: 128%;
}
</style>
