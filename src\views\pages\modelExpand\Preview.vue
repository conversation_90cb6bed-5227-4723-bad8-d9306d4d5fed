<template>
  <!-- 对话框表单 -->
  <component :is="ComponentRender" v-model:visible="data.visible" :handle-cancel="handleCancel">
    <template #header>
      {{ `${$params.id ? t("glob.Cat") : t("glob.add")}${t("devicesContacts.Contacts")}` }}
    </template>
    <template #default="{ width }">
      <FormModel ref="formRef" :loading="data.loading" :model="form" label-position="left" disabled :label-width="`${props.labelWidth}px`" @submit="handleFinish" class="form-item">
        <FormItem :span="width > 600 ? 12 : 24" :label="t('devicesContacts.Name')" tooltip="" prop="name" :rules="[]">
          <el-input v-model="form.name" placeholder="--"></el-input>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="t('devicesContacts.Email')" tooltip="" prop="email" :rules="[]">
          <el-input v-model="form.email" placeholder="--"></el-input>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="t('devicesContacts.Fixed telephone')" tooltip="" prop="landlinePhone" :rules="[]">
          <el-input v-model="form.landlinePhone" placeholder="--"> </el-input>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="t('devicesContacts.Phone')" tooltip="" prop="smsPhone" :rules="[]">
          <el-input v-model="form.smsPhone" placeholder="--"></el-input>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="t('devicesContacts.Sms number')" tooltip="" prop="mobilePhone" :rules="[]">
          <el-input v-model="form.mobilePhone" placeholder="--"></el-input>
        </FormItem>

        <FormItem :span="width > 600 ? 12 : 24" :label="t('devicesAdd.Time Zone')" tooltip="" prop="zoneId" :rules="[]">
          <el-select v-model="form.zoneId" placeholder="--" :style="{ width: '100%' }" filterable>
            <el-option v-for="(key, value) in Zone" :key="key" :label="key" :value="value" />
          </el-select>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="t('devicesContacts.Language')" tooltip="" prop="language" :rules="[]">
          <el-select v-model="form.language" placeholder="--" :style="{ width: '100%' }" filterable>
            <el-option v-for="item in localesOption" :key="item.value" :label="item.label" :value="item.value">
              <div :style="{ background: `url(${item.icon}) no-repeat left / auto calc(100% - 12px)`, paddingLeft: '30px' }">{{ item.label }}</div>
            </el-option>
          </el-select>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="`vip`" tooltip="" prop="vip" :rules="[]">
          <el-switch v-model="form.vip" :active-text="t('devicesContacts.Yes')" :inactive-text="t('devicesContacts.No')" />
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="t('devicesContacts.Sms enabled')" tooltip="" prop="smsEnabled" :rules="[]">
          <el-switch v-model="form.smsEnabled" :active-text="t('devicesContacts.Yes')" :inactive-text="t('devicesContacts.No')" />
        </FormItem>
        <FormItem :span="24" :label="t('devicesContacts.Description')" tooltip="" prop="note" :rules="[]">
          <el-input type="textarea" v-model="form.note" placeholder="--"></el-input>
        </FormItem>
        <FormItem :span="24" :label="t('devicesContacts.External Id')" tooltip="" prop="externalId" :rules="[]">
          <el-input v-model="form.externalId" placeholder="--"></el-input>
        </FormItem>
        <el-col v-if="!1" :span="24">
          <el-descriptions :column="24" class="model_preview">
            <!-- <el-descriptions-item :span="width > 600 ? 12 : 24" label="主键">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">{{ form.id }}</div>
            </el-descriptions-item> -->
            <!-- <el-descriptions-item :span="width > 600 ? 12 : 24" label="租户ID">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">{{ form.tenantId }}</div>
            </el-descriptions-item> -->
            <!-- <el-descriptions-item :span="width > 600 ? 12 : 24" label="头衔">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">{{ form.title }}</div>
            </el-descriptions-item> -->
            <el-descriptions-item :span="24" :label="t('devicesContacts.Name')">
              <div :style="{ width: `${width - 88 - props.labelWidth}px` }">{{ form.name }}</div>
            </el-descriptions-item>
            <el-descriptions-item :span="width > 600 ? 12 : 24" :label="t('devicesContacts.Email')">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">{{ form.email }}</div>
            </el-descriptions-item>
            <el-descriptions-item :span="width > 600 ? 12 : 24" :label="t('devicesContacts.Fixed telephone')">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">{{ form.landlinePhone }}</div>
            </el-descriptions-item>
            <el-descriptions-item :span="width > 600 ? 12 : 24" :label="t('devicesContacts.Phone')">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">{{ form.smsPhone }}</div>
            </el-descriptions-item>
            <!-- <el-descriptions-item :span="width > 600 ? 12 : 24" label="下班后联系电话">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">{{ form.afterWorkPhone }}</div>
            </el-descriptions-item> -->
            <el-descriptions-item :span="width > 600 ? 12 : 24" :label="t('devicesContacts.Sms number')">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">{{ form.mobilePhone }}</div>
            </el-descriptions-item>
            <!-- <el-descriptions-item :span="width > 600 ? 12 : 24" label="传真">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">{{ form.fax }}</div>
            </el-descriptions-item> -->
            <el-descriptions-item :span="width > 600 ? 12 : 24" :label="t('devicesAdd.Time Zone')">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">{{ Zone[form.zoneId as keyof typeof Zone] || form.zoneId }}</div>
            </el-descriptions-item>
            <el-descriptions-item :span="width > 600 ? 12 : 24" :label="t('devicesContacts.Language')">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px`, paddingLeft: '30px', background: language.icon ? `url(${language.icon}) no-repeat left / 22px auto` : 'none' }">{{ language.label }}</div>
            </el-descriptions-item>
            <el-descriptions-item :span="width > 600 ? 12 : 24" :label="t('devicesContacts.Is it VIP')">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">
                <el-tag :type="form.vip ? '' : 'danger'" size="small">{{ form.vip ? $t("glob.Yes") : $t("glob.Not") }}</el-tag>
              </div>
            </el-descriptions-item>
            <el-descriptions-item :span="width > 600 ? 12 : 24" :label="t('devicesContacts.Sms enabled')">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">
                <el-tag :type="form.smsEnabled ? '' : 'danger'" size="small">{{ form.smsEnabled ? $t("glob.Yes") : $t("glob.Not") }}</el-tag>
              </div>
            </el-descriptions-item>
            <el-descriptions-item :span="24" :label="t('devicesContacts.Description')">
              <div :style="{ width: `${width - 88 - props.labelWidth}px` }">
                <pre class="tw-m-0 tw-p-0">{{ form.note }}</pre>
              </div>
            </el-descriptions-item>
            <el-descriptions-item :span="width > 600 ? 12 : 24" :label="t('devicesContacts.External Id')">
              <div :style="{ width: `${width - 88 - props.labelWidth}px` }">{{ form.externalId }}</div>
            </el-descriptions-item>
            <!-- <el-descriptions-item :span="width > 600 ? 12 : 24" label="版本号">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">{{ form.version }}</div>
            </el-descriptions-item> -->
            <!-- <el-descriptions-item :span="width > 600 ? 12 : 24" label="创建时间">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">{{ form.createdTime ? moment(form.createdTime, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</div>
            </el-descriptions-item> -->
            <!-- <el-descriptions-item :span="width > 600 ? 12 : 24" label="更新时间">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">{{ form.updatedTime ? moment(form.updatedTime, "x").format("YYYY-MM-DD HH:mm:ss") : "--" }}</div>
            </el-descriptions-item> -->
            <!-- <el-descriptions-item :span="width > 600 ? 12 : 24" label="创建人">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">{{ form.createdBy.username }}</div>
            </el-descriptions-item> -->
            <!-- <el-descriptions-item :span="width > 600 ? 12 : 24" label="更新人">
              <div :style="{ width: `${(width > 600 ? width / 2 : width) - 88 - props.labelWidth}px` }">{{ form.updatedBy.username }}</div>
            </el-descriptions-item> -->
          </el-descriptions>
        </el-col>
      </FormModel>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.back") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button> -->
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </component>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, computed, h, createVNode, renderSlot, toRaw } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep, find, first, uniqBy, isArray } from "lodash-es";
import { UserFilled, Postcard, InfoFilled } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { templateRef } from "@vueuse/core";
import { buildTypeHelper } from "@/utils/type";
import { buildValidatorData } from "@/utils/validate";
import moment from "moment";

import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import getUserInfo from "@/utils/getUserInfo";

import { Zone } from "@/utils/zone";
import { localesOption } from "@/api/locale";
import { getContactById } from "@/views/pages/apis/device";

const userInfo = getUserInfo();

const formRef = templateRef<InstanceType<typeof FormModel>>("formRef");
/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Item {
  id: string /* 主键 */;
  tenantId: string /* 租户ID */;
  title: string /* 头衔 */;
  name: string /* 姓名 */;
  language: string /* 语言 */;
  email: string /* 邮箱地址 */;
  landlinePhone: string /* 固定电话 */;
  mobilePhone: string /* 移动电话 */;
  afterWorkPhone: string /* 下班后联系电话 */;
  smsPhone: string /* 短信号码 */;
  fax: string /* 传真 */;
  smsEnabled: boolean /* 是否启用短信 */;
  vip: boolean /* 是否VIP */;
  note: string /* 备注 */;
  zoneId: string /* 时区ID */;
  externalId: string /* 外部ID */;
  version: string /* 版本号 */;
  createdTime: string /* 创建时间 */;
  updatedTime: string /* 最近更新时间 */;
  createdBy: Record<string, string> /* 创建人 */;
  updatedBy: Record<string, string> /* 最后更新人 */;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const language = computed(() => find(localesOption, (v) => v.value === form.value.language) || { label: form.value.language, icon: "" });
/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  id: buildTypeHelper<Required<Item>["id"]>(""),
  tenantId: buildTypeHelper<Required<Item>["tenantId"]>(""),
  title: buildTypeHelper<Required<Item>["title"]>(""),
  name: buildTypeHelper<Required<Item>["name"]>(""),
  language: buildTypeHelper<Required<Item>["language"]>(""),
  email: buildTypeHelper<Required<Item>["email"]>(""),
  landlinePhone: buildTypeHelper<Required<Item>["landlinePhone"]>(""),
  mobilePhone: buildTypeHelper<Required<Item>["mobilePhone"]>(""),
  afterWorkPhone: buildTypeHelper<Required<Item>["afterWorkPhone"]>(""),
  smsPhone: buildTypeHelper<Required<Item>["smsPhone"]>(""),
  fax: buildTypeHelper<Required<Item>["fax"]>(""),
  smsEnabled: buildTypeHelper<Required<Item>["smsEnabled"]>(false),
  vip: buildTypeHelper<Required<Item>["vip"]>(false),
  note: buildTypeHelper<Required<Item>["note"]>(""),
  zoneId: buildTypeHelper<Required<Item>["zoneId"]>(""),
  externalId: buildTypeHelper<Required<Item>["externalId"]>(""),
  version: buildTypeHelper<Required<Item>["version"]>(""),
  createdTime: buildTypeHelper<Required<Item>["createdTime"]>(""),
  updatedTime: buildTypeHelper<Required<Item>["updatedTime"]>(""),
  createdBy: buildTypeHelper<Required<Item>["createdBy"]>({}),
  updatedBy: buildTypeHelper<Required<Item>["updatedBy"]>({}),
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function tryJSON<T>(str: string): null | T {
  try {
    return JSON.parse(str);
  } catch (error) {
    return null;
  }
}
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  data.loading = true;

  await (async () => {
    const { success, message, data } = await getContactById({ id: <string>params.id });
    if (!success) throw Object.assign(new Error(message), { success, data });
    $params.value = { ...data, createdBy: tryJSON<Record<string, string>>(data.createdBy || "") || {}, updatedBy: tryJSON<Record<string, string>>(data.updatedBy || "") || {} };
  })();

  data.loading = false;
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  return form;
}
/* ---------------------------------------------------------‖ ↑↑ 钩子 Start ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE ACHIEVE  END  ========================================================= */
/**
 * TODO: 本地方法
 */
interface Tree {
  children: Tree[];
  id: string;
  parentId: string;
  [key: string]: any;
}
function buildTree<T extends Tree>(data: T[]): T[] {
  for (let index = 0; index < data.length; index++) {
    if (!(data[index].children instanceof Array)) data[index].children = [];
    data[index].children.splice(0, data[index].children.length, ...data.filter((v) => v.parentId === data[index].id).map((v) => Object.assign(v, { isBuild: true })));
  }
  return data.filter((v) => {
    if ("isBuild" in v) {
      delete v.isBuild;
      return false;
    } else return true;
  });
}
/*  */
/**
 * TODO: 窗口方法
 */
interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 100,
});

const { t } = useI18n();

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<Item>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Partial<Item>) => void;
  reject?: (value: Partial<Item>) => void;
  callback?: (form: Item) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }

  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], cloneDeep(toRaw(util.value)) as never)) });
    },
    form as Required<Item>
  );
}
async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => formRef.value.validate(resolve));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();

defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, callback?: (form: Item) => Promise<void>): Promise<unknown> {
    if (data.visible) handleCancel();
    const wait = new Promise<Record<string, unknown>>((resolve, reject) => ((data.resolve = resolve), (data.reject = reject)));
    $params.value = cloneDeep(params);
    data.visible = true;
    data.loading = true;
    data.submitLoading = true;
    data.callback = callback;
    await nextTick();
    try {
      await runningInit($params.value);
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      handleCancel();
    }
    handleReset();
    data.loading = false;
    data.submitLoading = false;

    try {
      return await wait;
    } catch (error) {
      return error;
    }
  },
  async confirm<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.confirm(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async alert<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.alert(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
});
</script>

<style scoped lang="scss">
.form-item {
  :deep(.el-input.is-disabled .el-input__inner) {
    color: #000;
    -webkit-text-fill-color: #000;
  }
  :deep(.el-input__inner)::placeholder {
    color: #000;
    -webkit-text-fill-color: #000;
  }
  :deep(.el-textarea.is-disabled .el-textarea__inner) {
    color: #000;
    -webkit-text-fill-color: #000;
  }
  :deep(.el-textarea__inner)::placeholder {
    color: #000;
    -webkit-text-fill-color: #000;
  }
}

.model_preview {
  :deep(.el-descriptions__cell) {
    .el-descriptions__label {
      display: inline-block;
      vertical-align: middle;
      width: 90px;
    }
    .el-descriptions__content {
      > div {
        display: inline-block;
        vertical-align: middle;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
