import { shallowReadonly } from "vue";
import { createGlobalState } from "@vueuse/core";
import { getContactGroup } from "@/views/pages/apis/device";

export enum tabsTypeEnum {
  /**@type {string} - 联系人 */
  contact = "contact",
  /**@type {string} - 资源 */
  resource = "resource",
  /**@type {string} - 资源组 */
  group = "group",
  /**@type {string} - 资源类型 */
  type = "type",
  /**@type {string} - 场所 */
  location = "location",
  /**@type {string} - 客户 */
  tenant = "tenant",
  /**@type {string} - 区域 */
  region = "region",
  removeResoure = "removeResoure",
  removeType = "removeType",
  removeGroup = "removeGroup",
  removeLocation = "removeLocation",
  removeRegion = "removeRegion",
  removeTenant = "removeTenant",
}

export const contactGroupState = createGlobalState(() => {
  const state = shallowReadonly(getContactGroup({}));
  return state;
});
export const deviceGroupState = createGlobalState(() => {
  const state = shallowReadonly(Promise.resolve({ success: true, message: "", data: <{ code: string; type: tabsTypeEnum; label: string }[]>[{ code: "Resource", type: tabsTypeEnum.resource, label: "设备" }] }));
  return state;
});
export const groupGroupState = createGlobalState(() => {
  const state = shallowReadonly(Promise.resolve({ success: true, message: "", data: <{ code: string; type: tabsTypeEnum; label: string }[]>[{ code: "DeviceGroup", type: tabsTypeEnum.group, label: "设备分组" }] }));
  return state;
});
export const typeGroupState = createGlobalState(() => {
  const state = shallowReadonly(Promise.resolve({ success: true, message: "", data: <{ code: string; type: tabsTypeEnum; label: string }[]>[{ code: "DeviceType", type: tabsTypeEnum.type, label: "设备类型" }] }));
  return state;
});
export const locationState = createGlobalState(() => {
  const state = shallowReadonly(Promise.resolve({ success: true, message: "", data: <{ code: string; type: tabsTypeEnum; label: string }[]>[{ code: "Location", type: tabsTypeEnum.location, label: "场所" }] }));
  return state;
});
export const regionState = createGlobalState(() => {
  const state = shallowReadonly(Promise.resolve({ success: true, message: "", data: <{ code: string; type: tabsTypeEnum; label: string }[]>[{ code: "Region", type: tabsTypeEnum.region, label: "区域" }] }));
  return state;
});
export const tenantState = createGlobalState(() => {
  const state = shallowReadonly(Promise.resolve({ success: true, message: "", data: <{ code: string; type: tabsTypeEnum; label: string }[]>[{ code: "Tenant", type: tabsTypeEnum.tenant, label: "客户" }] }));
  return state;
});
