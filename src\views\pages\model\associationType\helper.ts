/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { reactive, readonly, computed, nextTick } from "vue";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { filter, find } from "lodash-es";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 接口 Start ↓↓ ‖-------------------------------------------- */
// import { type ServiceItem as DataItem } from "@/views/pages/apis/event";
import { type publishItem as DataItem } from "@/views/pages/apis/publish";
/* --------------------------------------------‖ ↑↑  接口 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */

/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
export interface BaseItem {
  id: string;
}
export { type DataItem };
// type ItemKeys = ;
export type Item = DataItem;

interface State<T> {
  loading: boolean;
  expand: string[];
  select: string[];
  current: string | undefined;
  search: Record<string, any>;
  sort?: { prop: string; order: "ascending" | "descending" };
  list: T[];
  page: number;
  size: number;
  total: number;
}
export enum command {
  Refresh = "Refresh",
  Request = "Request",
  Preview = "Preview",
  Create = "Create",
  Update = "Update",
  Modify = "Modify",
  Delete = "Delete",
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const final = readonly({
  pagination: false,
});

export const state = reactive<State<DataItem>>({
  loading: false,
  expand: [],
  select: [],
  current: undefined,
  search: {
    changeType: null,
  },
  sort: undefined,
  list: [],
  page: 1,
  size: 20,
  total: 0,
});
export const dataList = computed(() => (final.pagination ? state.list.slice((state.page - 1) * state.size, state.page * state.size) : state.list));
export const expand = computed(() => filter<DataItem>(state.list, (row: DataItem) => state.expand.includes(row.id)));
export const select = computed(() => filter<DataItem>(state.list, (row: DataItem) => state.select.includes(row.id)));
export const current = computed(() => find<DataItem>(state.list, (row: DataItem) => row.id === state.current));
// const name = computed(() => state.name);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
export function handleExpand(row: Item, expandedRows: Item[]) {
  state.expand = expandedRows.filter((v) => v).map(({ id }) => id);
  if (find(expandedRows, ({ id }) => row.id === id)) {
    /*  */
  } else {
    /*  */
  }
}
export function handleSort(sort: { prop: string; order: "ascending" | "descending" }) {
  state.sort = sort.prop ? sort : undefined;
}
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
export async function resetData() {
  state.list = [];
  state.page = 1;
  state.size = 20;
  state.total = 0;
  for (const key in state.search) {
    if (Object.prototype.hasOwnProperty.call(state.search, key)) {
      delete state.search[key];
    }
  }
  await nextTick();
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */
