<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="getSlaDownList()" @current-change="getSlaDownList()">
      <template #left>
        <el-input v-model="state.search.keyword" @clear="getSlaDownList" clearable style="width: 220px" placeholder="请输入名称" @keyup.enter="getSlaList()">
          <template #append>
            <el-button :icon="Search" @click="getSlaList()" />
          </template>
        </el-input>
      </template>
      <template #right>
        <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811748109549568.create)">
          <span class="tw-h-fit">
            <el-button :disabled="!userInfo.hasPermission(PERMISSION.group533811748109549568.create)" type="primary" :icon="Plus" @click="handleCreate('add')">{{ $t("glob.add") }}关联类型</el-button>
          </span>
        </el-tooltip>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table stripe :data="tableData" :height="tableHeight" @sort-change="(sort) => ((state.sort = sort.prop ? sort : undefined), handleCommand(command.Request))" :default-sort="state.sort" style="width: 100%">
          <!-- <el-table-column type="index" align="left" prop="date" label="序号" :width="56"> </el-table-column> -->
          <!-- <el-table-column  align="left" prop="degradeName" label="告警降级配置名称" :formatter="formatterTable" width="180"> </el-table-column> -->
          <!-- <TableColumn type="default" show-filter v-model:filtered-value="ServiceSearch" @filter-change="getSlaDownList()"  prop="degradeName" label="告警降级配置名称" :width="160"> </TableColumn> -->
          <!-- <el-table-column  align="left" prop="ruleDesc" label="SLA描述" :formatter="formatterTable"> </el-table-column> -->
          <!-- <TableColumn type="enum" show-filter v-model:filtered-value="degradeStatus" @filter-change="getSlaDownList()" prop="degradeStatus" label="使用状态" :width="100" :filters="slaDownStatus.map((v) => ({ ...v, text: v.label }))">
            <template #default="{ row }">
              <el-tag class="ml-2" :type="row.degradeStatus ? 'success' : 'danger'">
                {{ row.degradeStatus ? "启用" : "禁用" }}
              </el-tag>
            </template>
          </TableColumn> -->
          <TableColumn type="default" prop="ident" label="唯一标识"> </TableColumn>
          <!-- <el-table-column  align="left" prop="degradeName" label="唯一标识" :formatter="formatterTable" sortable="custom"> </el-table-column> -->
          <el-table-column align="left" prop="name" label="名称" :formatter="formatterTable"> </el-table-column>
          <el-table-column align="left" prop="sourceDesc" label="源=>目标 描述" :formatter="formatterTable"> </el-table-column>
          <el-table-column align="left" prop="targetDesc" label="目标=>源 描述" :formatter="formatterTable"> </el-table-column>
          <el-table-column align="left" prop="direction" label="方向" :formatter="formatterTableDirection"> </el-table-column>
          <!-- <el-table-column  align="left" prop="degradeDesc" label="告警降级配置描述" :formatter="formatterTable"> </el-table-column> -->
          <!-- <el-table-column align="left" label="是否默认" :width="90">
            <template #default="{ row }">
              <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811748109549568.editor)">
                <span class="tw-h-fit tw-align-middle">
                  <el-switch v-model="row.defaultRule" :disabled="!userInfo.hasPermission(PERMISSION.group533811748109549568.editor)" @change="statusChange(row)" active-color="#13ce66" inactive-color="#D3D3D3"></el-switch>
                </span>
              </el-tooltip>
            </template>
          </el-table-column> -->
          <!-- <el-table-column align="left" prop="degradeStatus" label="使用状态" :width="80" :formatter="(_row, _col, v) => h(ElText, { type: v ? 'success' : 'danger' }, () => (v ? $t('glob.Enable') : $t('glob.Disable')))"></el-table-column> -->
          <!-- <el-table-column  align="left" prop="degradeCreateTime" label="创建时间" :formatter="(_row, _col, v) => (v ? moment(v, 'x').format('yyyy-MM-DD HH:mm:ss') : '--')"></el-table-column> -->
          <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="126">
            <template #default="{ row }">
              <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811748109549568.editor)">
                <span class="tw-h-fit tw-align-middle">
                  <el-link :disabled="!userInfo.hasPermission(PERMISSION.group533811748109549568.editor)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleCreate('edit', row)">{{ $t("glob.edit") }}</el-link>
                </span>
              </el-tooltip>
              <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811748109549568.remove)">
                <span class="tw-h-fit tw-align-middle">
                  <el-link :disabled="!userInfo.hasPermission(PERMISSION.group533811748109549568.remove)" type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="modelDelete(row as DataItem)">{{ $t("glob.delete") }}</el-link>
                </span>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
  <modelConCreate :dialog="dialog" ref="supplier" @dialogClose="dialogClose"></modelConCreate>
</template>

<script setup lang="ts" generic="T extends object">
/* eslint-disable @typescript-eslint/no-unused-vars */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMessageBox, ElText } from "element-plus";
import pageTemplate from "@/components/pageTemplate.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import modelConCreate from "./modelConCreate.vue";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";
import { slaDownStatus } from "./common";
import { AddSlaDownConfig, SlaDownConfigEdit } from "@/views/pages/apis/SlaConfig";
import { state, dataList, expand, select, current } from "./helper";
import { resetData, handleExpand, handleSort, command } from "./helper";

import { getModelList, deleteModel, EnableSlaDownConfig, DisableSlaDownConfig, SlaDownConfigStatus, type SlaConfigList as DataItem } from "@/views/pages/apis/model";
import { faBullseye } from "@fortawesome/free-solid-svg-icons";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "SlaDownConfig" });
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const dialog = ref(false);

const siteConfig = useSiteConfig();
const userInfo = getUserInfo();

// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
// const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);

const tableLoading = ref(false);

// 搜索关键字
const ServiceSearch = ref("");
const degradeStatus = ref(null);
const tableData = ref<DataItem[]>([]);
const paging = reactive({
  pageNumber: 1,
  pageSize: 50,
  total: 0,
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  getSlaList();
}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
//搜索
function getSlaList() {
  paging.pageNumber = 1;

  getSlaDownList();
}
function formatterTableDirection(_row, _col, v) {
  switch (v) {
    case "SOURCE_TO_TARGET":
      return "源指向目标";
    case "TARGET_TO_SOURCE":
      return "目标指向源";
    case "BOTH_WAY":
      return "双向";
  }
}
function formatterTable(_row, _col, v) {
  switch (_col.property) {
    default:
      return v || "--";
  }
}
// //默认值更改
// function statusChange(row: Partial<Record<string, any>>) {
//   SlaDownConfigStatus({
//     id: row.id,
//     defaultable: row.defaultRule,
//   })
//     .then((res) => {
//       if (res.success) {
//         ElMessage.success("默认状态修改成功");
//         getSlaDownList();
//       }
//     })
//     .catch((e) => {
//       ElMessage.error(e.message);
//     });
// }
//启禁用服务

function getSlaDownList() {
  let sort: string[] = [];
  switch ((state.sort || {}).order) {
    case "ascending":
      sort.push(`${String(state.sort?.prop)},asc`);
      break;
    case "descending":
      sort.push(`${String(state.sort?.prop)},desc`);
      break;
  }
  let params = {
    ...paging,
    ...sort,
    name: state.search.keyword,
  };
  tableLoading.value = true;
  getModelList(params)
    .then(({ success, data, page, size, total }) => {
      if (success) {
        let arr = [];
        data.forEach((v, i) => {
          if (v.defaultRule == 1 || v.defaultRule == null) {
            arr.push({ ...v, defaultRule: true });
          } else {
            arr.push({ ...v, defaultRule: false });
          }
        });
        //  let arr = [...data];
        let newArr = [];
        // // console.log()

        if (degradeStatus.value == null || degradeStatus.value === "") {
          tableData.value = arr;
        } else {
          // console.log(degradeStatus.value);
          if (degradeStatus.value) {
            arr.forEach((v, i) => {
              if (v.degradeStatus) {
                newArr.push(v);
              }
            });
            tableData.value = newArr;
          }
          if (degradeStatus.value == false) {
            arr.forEach((v, i) => {
              // console.log(v);
              if (v.degradeStatus == degradeStatus.value) {
                newArr.push(v);
              }
            });
            tableData.value = newArr;
          }
        }
        // // console.log(arr, 55555);

        tableLoading.value = false;
        paging.total = Number(total);
        paging.pageNumber = page;
        paging.pageSize = size;
      }
    })
    .catch(() => {
      tableLoading.value = false;
    });
}
async function handleCommand(type: command, data?: Record<string, unknown>) {
  try {
    await nextTick();
    switch (type) {
      case command.Refresh:
        await getSlaDownList();
        break;
      case command.Request:
        await getSlaDownList();
        break;
      case command.Preview:
        // await previewItem(data as Record<string, unknown>);
        break;
      case command.Create:
        // await createItem(data as Record<string, unknown>);
        await getSlaDownList();
        break;
      case command.Update:
        // await rewriteItem(data as Record<string, unknown>);
        await getSlaDownList();
        break;
      case command.Modify:
        // await modifyItem(data as Record<string, unknown>);
        await getSlaDownList();
        break;
      case command.Delete:
        // await deleteItem(data as Record<string, unknown>);
        await getSlaDownList();
        break;
    }
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
      await getSlaDownList();
    }
  } finally {
  }
}
async function handleCreate(type, row) {
  ctx.refs.supplier.form.report = false;
  setTimeout(() => {
    ctx.refs.supplier.open(type, row);
  }, 200);
  dialog.value = true;
}
function dialogClose(bool) {
  dialog.value = bool;
  getSlaDownList();
  ctx.refs.supplier.form.report = false;
}
//删除关联类型
function modelDelete(row: Partial<Record<string, any>>) {
  ElMessageBox.confirm("确定删除此关联类型吗?", "删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // // tableData.value.length = tableData.value.length - 1;
      let params = {
        ident: row.ident,
      };
      deleteModel(params)
        .then(({ success, data, message }) => {
          if (!success) throw new Error(message);
          ElMessage.success("操作成功");

          if (tableData.value.slice((paging.pageNumber - 1) * paging.pageSize, paging.pageNumber * paging.pageSize).length == 0) {
            paging.pageNumber = 1;
          }
          getSlaDownList();
        })
        .catch((e) => {
          ElMessage.error(e.message);
          tableLoading.value = false;
        });
    })
    .catch(() => {
      // ElMessage({
      //   type: "info",
      //   message: "已取消删除",
      // });
    });
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
