<template>
  <div>
    <el-dialog :title="title" v-model="dialogFormVisible" :before-close="cancel" width="45%">
      <el-form :model="form" :rules="rules" :label-position="labelPosition" ref="ruleForm">
        <el-form-item label="唯一标识" :label-width="formLabelWidth" prop="ident">
          <el-input :disabled="type !== 'add'" v-model="form.ident" autocomplete="off" maxlength="150" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="名称" :label-width="formLabelWidth" prop="name">
          <el-input v-model="form.name" autocomplete="off" maxlength="150" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="源->目标 描述" :label-width="formLabelWidth" prop="sourceDesc">
          <el-input v-model="form.sourceDesc" autocomplete="off" maxlength="150" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="目标->源 描述" :label-width="formLabelWidth" prop="targetDesc">
          <el-input v-model="form.targetDesc" autocomplete="off" maxlength="150" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="是否有方向" :label-width="formLabelWidth" prop="direction">
          <el-radio-group v-model="form.direction">
            <el-radio label="SOURCE_TO_TARGET">源指向目标</el-radio>
            <el-radio label="TARGET_TO_SOURCE">目标指向源</el-radio>
            <!-- <el-radio label="NONE">无方向</el-radio> -->
            <el-radio label="BOTH_WAY">双向</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel()">取 消</el-button>
          <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { defineComponent } from "vue";
import { ElMessage } from "element-plus";
import { addModel, editModel } from "@/views/pages/apis/model";
import { getAlarmClassificationList } from "@/views/pages/apis/alarmClassification";

export default defineComponent({
  name: "supplierCreate",
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      form: {
        ident: "",
        name: "",
        sourceDesc: "",
        targetDesc: "",
        direction: "SOURCE_TO_TARGET",
      },
      rules: {
        ident: [{ required: true, message: "请输入", trigger: "blur" }],
        name: [{ required: true, message: "请输入", trigger: "blur" }],
        sourceDesc: [{ required: true, message: "请输入", trigger: "blur" }],
        targetDesc: [{ required: true, message: "请输入", trigger: "blur" }],
        direction: [{ required: true, message: "请选择", trigger: "change" }],
      },
      disabled: false,
      title: "",
      checked: false,
      dialogFormVisible: false,
      formLabelWidth: "130px",
      labelPosition: "left",
      type: "",
      options: [],
      value: "",
    };
  },
  watch: {
    // "dialog"(val) {
    //   this.dialogFormVisible = val;
    // },
    "form.report"(val) {
      this.form.report = val;
    },
  },
  created() {
    this.getAlarmList();
  },
  methods: {
    open(type, row) {
      this.dialogFormVisible = true;
      // // console.log(type, row);
      this.type = type;
      this.form.report = false;
      if (type === "add") {
        this.form = {
          ident: "",
          name: "",
          sourceDesc: "",
          targetDesc: "",
          direction: "SOURCE_TO_TARGET",
        };
        this.title = "新增关联类型";
        // // console.log(this.form.report);
      } else {
        this.form = { ...row };
        this.title = "修改关联类型";
      }
    },
    getAlarmList() {
      getAlarmClassificationList({}).then((res) => {
        if (res.success) {
          this.options = [...res.data];
        } else {
          ElMessage.error(JSON.parse(res.data)?.message);
        }
      });
    },
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.type === "add") {
            addModel(this.form).then((res) => {
              if (res.success) {
                ElMessage.success("新增成功");
                this.dialogFormVisible = false;

                this.$emit("dialogClose", false);

                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
              } else {
                this.dialogFormVisible = false;

                this.$emit("dialogClose", false);
                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
                ElMessage.error(JSON.parse(res.data)?.message);
              }
            });
          } else {
            editModel(this.form).then((res) => {
              if (res.success) {
                this.dialogFormVisible = false;

                ElMessage.success("修改成功");
                this.$emit("dialogClose", false);

                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
              } else {
                this.dialogFormVisible = false;

                this.$emit("dialogClose", false);
                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
                ElMessage.error(JSON.parse(res.data)?.message);
              }
            });
          }
          // this.dialogFormVisible = false;
        } else {
          return false;
        }
      });
    },
    cancel() {
      this.dialogFormVisible = false;
      // this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      this.$refs["ruleForm"].clearValidate();
    },
    blurChange(data) {
      this.tags.push(data);
    },
    tagClose(index) {
      this.tags.splice(index, 1);
    },
  },
  expose: ["type", "disabled", "title", "form", "open"],
});
</script>
