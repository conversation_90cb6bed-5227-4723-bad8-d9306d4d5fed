<template>
  <div>
    <el-dialog :title="title" v-model="dialogFormVisible" :before-close="handleClose" width="45%">
      <el-form :model="form" :rules="rules" :label-position="labelPosition" ref="ruleForm">
        <el-form-item label="唯一标识" :label-width="formLabelWidth" prop="ident">
          <el-input :disabled="type !== 'add'" v-model="form.ident" autocomplete="off" maxlength="150" show-word-limit clearable placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="分组名称" :label-width="formLabelWidth" prop="name">
          <el-input v-model="form.name" autocomplete="off" maxlength="150" show-word-limit clearable placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="分组备注" :label-width="formLabelWidth" prop="destDes">
          <el-input v-model="form.destDes" type="textarea" autocomplete="off" maxlength="500" show-word-limit clearable placeholder=""></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel()">取 消</el-button>
          <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { defineComponent } from "vue";
import { ElMessage } from "element-plus";
import { addGroup, editGroup } from "@/views/pages/apis/model";

export default defineComponent({
  name: "supplierCreate",
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      form: {
        ident: "",
        name: "",
        destDes: "",
      },
      rules: {
        ident: [{ required: true, message: "请输入", trigger: "blur" }],
        name: [{ required: true, message: "请输入", trigger: "blur" }],
      },
      disabled: false,
      title: "",
      checked: false,
      dialogFormVisible: false,
      formLabelWidth: "130px",
      labelPosition: "left",
      type: "",
      value: "",
    };
  },
  watch: {
    // "dialog"(val) {
    //   this.dialogFormVisible = val;
    // },
    "form.report"(val) {
      this.form.report = val;
    },
  },
  created() {

  },
  methods: {
    open(type, row) {
      this.dialogFormVisible = true;
      // // console.log(type, row);
      this.type = type;
      this.form.report = false;
      if (type === "add") {
        this.form = {
          ident: "",
          name: "",
          destDes: "",
        };
        this.title = "新建分组";
        // // console.log(this.form.report);
      } else {
        this.form = { ...row };
        this.title = "修改分组";
      }
    },
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.type === "add") {
            addGroup(this.form).then((res) => {
              if (res.success) {
                ElMessage.success("新增成功");
                this.dialogFormVisible = false;

                this.$emit("dialogClose", false);

                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
              } else {
                this.dialogFormVisible = false;

                this.$emit("dialogClose", false);
                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
                ElMessage.error(JSON.parse(res.data)?.message);
              }
            });
          } else {
            editGroup(this.form).then((res) => {
              if (res.success) {
                this.dialogFormVisible = false;

                ElMessage.success("修改成功");
                this.$emit("dialogClose", false);

                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
              } else {
                this.dialogFormVisible = false;

                this.$emit("dialogClose", false);
                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
                ElMessage.error(JSON.parse(res.data)?.message);
              }
            });
          }
          // this.dialogFormVisible = false;
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.dialogFormVisible = false;
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      this.$refs["ruleForm"].clearValidate();

      // this.dialogFormVisible = false;
    },
    cancel() {
      this.dialogFormVisible = false;
      // this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      this.$refs["ruleForm"].clearValidate();
    },
    blurChange(data) {
      this.tags.push(data);
    },
    tagClose(index) {
      this.tags.splice(index, 1);
    },
  },
  expose: ["type", "disabled", "title", "form", "open"],
});
</script>
