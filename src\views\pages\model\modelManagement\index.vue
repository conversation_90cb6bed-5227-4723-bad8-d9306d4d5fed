<template>
  <el-container :style="{ margin: '0 -16px' }">
    <el-header height="56px">
      <el-card :body-style="{ padding: '0 20px', height: '56px' }">
        <el-input v-model="state.search.keyword" clearable @clear="getSlaDownList" style="width: 220px; float: left; top: 13px" placeholder="请输入关键字" @keyup.enter="getSlaList()">
          <template #append>
            <el-button :icon="Search" @click="getSlaList()" />
          </template>
        </el-input>
        <el-radio-group v-model="radio" @change="selectRadio" size="medium" style="margin-left: 10px; float: left; margin-top: 13px">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="start">启用中</el-radio-button>
          <el-radio-button label="stop">停用</el-radio-button>
        </el-radio-group>
        <div style="float: right; margin-top: 13px">
          <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.create)">
            <el-button size="medium" type="primary" @click="modelCreateClick('add')" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.create)">新建模型</el-button>
          </el-tooltip>

          <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.auth542939509373796352)">
            <el-button size="medium" @click="groupCreateClick('add')" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.auth542939509373796352)">新建分组</el-button>
          </el-tooltip>
        </div>
      </el-card>
    </el-header>
    <el-main>
      <el-card :body-style="{ padding: '0px', height: `${height - 72}px`, width: `${width}px` }">
        <SmsTemplate :dataBox="dataBox" @delGroup="getSlaDownList" :height="height - 66" :width="width"></SmsTemplate>
      </el-card>
    </el-main>
  </el-container>
  <modelCreate :dialog="dialog" ref="supplier" @dialogClose="dialogClose"></modelCreate>
  <groupCreate :dialog="dialogGroup" ref="supplierGroup" @dialogClose="dialogCloseGroup"></groupCreate>
</template>

<script setup lang="ts" generic="T extends object">
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
import modelCreate from "./modelCreate.vue";
import groupCreate from "./groupCreate.vue";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox } from "element-plus";
import pageTemplate from "@/components/pageTemplate.vue";

import SmsTemplate from "./smsTemplate.vue";
import { state, dataList, expand, select, current } from "../associationType/helper";

/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";
import getUserInfo from "@/utils/getUserInfo";

import { getModelManageList, DisableSlaDownConfig, SlaDownConfigStatus, type SlaConfigList as DataItem } from "@/views/pages/apis/model";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "NoticeSub" });
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const siteConfig = useSiteConfig();
const userInfo = getUserInfo();
const dialog = ref(false);
const dialogGroup = ref(false);
const enabled = ref("");

const ServiceSearch = ref("");
const radio = ref("all");

const dataBox = ref([]);

// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);
const active = ref("sms");

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
//搜索
function getSlaList() {
  getSlaDownList();
}
function beforeCreate() {}
function created() {
  getSlaList();
}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
async function modelCreateClick(type, row) {
  ctx.refs.supplier.form.report = false;
  getSlaDownList();
  setTimeout(() => {
    ctx.refs.supplier.open(type, row);
  }, 200);
  dialog.value = true;
}
function dialogClose(bool) {
  dialog.value = bool;
  getSlaDownList();
  ctx.refs.supplier.form.report = false;
}
async function groupCreateClick(type, row) {
  ctx.refs.supplierGroup.form.report = false;
  getSlaDownList();
  setTimeout(() => {
    ctx.refs.supplierGroup.open(type, row);
  }, 200);
  dialogGroup.value = true;
}
function dialogCloseGroup(bool) {
  dialogGroup.value = bool;
  getSlaDownList();
  ctx.refs.supplierGroup.form.report = false;
}
function selectRadio(val) {
  if (val === "all") {
    enabled.value = "";
    getSlaDownList();
  } else if (val === "start") {
    enabled.value = true;
    getSlaDownList();
  } else {
    enabled.value = false;
    getSlaDownList();
  }
}
function getSlaDownList() {
  let params = {
    key: state.search.keyword || "",
    enabled: enabled.value,
  };
  getModelManageList(params)
    .then(({ success, message, data, total }) => {
      if (!success) throw new Error(message);
      dataBox.value = data;
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {
      loading.value = false;
    });
}
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
