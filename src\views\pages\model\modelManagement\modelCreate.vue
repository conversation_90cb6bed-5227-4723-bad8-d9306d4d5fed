<template>
  <div>
    <el-dialog :title="title" v-model="dialogFormVisible" :before-close="cancel" width="45%">
      <el-form :model="form" :rules="rules" :label-position="labelPosition" ref="ruleForm">
        <el-form-item label="所属分组" :label-width="formLabelWidth" prop="modelGroup">
          <el-select v-model="form.modelGroup" clearable placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.ident"
              :label="item.name"
              :value="item.ident">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="唯一标识" :label-width="formLabelWidth" prop="ident">
          <el-input v-model="form.ident" autocomplete="off" maxlength="150" show-word-limit clearable placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="模型名称" :label-width="formLabelWidth" prop="name">
          <el-input v-model="form.name" autocomplete="off" maxlength="150" show-word-limit clearable placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="模型图标" :label-width="formLabelWidth" prop="icon">
          <el-select v-model="form.icon" placeholder="请选择">
            <el-option-group
              v-for="group in optionsTwo"
              :key="group.label"
              :label="group.label">
              <el-option
                v-for="item in group.options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" :label-width="formLabelWidth" prop="description">
          <el-input v-model="form.description" type="textarea" autocomplete="off" maxlength="500" show-word-limit clearable placeholder=""></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel()">取 消</el-button>
          <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { defineComponent } from "vue";
import { ElMessage } from "element-plus";
import { getGroupsList, addModelManage, editModelManage} from "@/views/pages/apis/model";

export default defineComponent({
  name: "supplierCreate",
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      form: {
        ident: "",
        name: "",
        icon: "",
        description: "",
        modelGroup: "",
      },
      rules: {
        ident: [{ required: true, message: "请输入", trigger: "blur" }],
        name: [{ required: true, message: "请输入", trigger: "blur" }],
        modelGroup: [{ required: true, message: "请选择", trigger: "change" }],
      },
      disabled: false,
      title: "",
      dialogFormVisible: false,
      formLabelWidth: "130px",
      labelPosition: "left",
      type: "",
      options: [],
      optionsTwo: [{
          label: '热门城市',
          options: [{
            value: 'Shanghai',
            label: '上海'
          }, {
            value: 'Beijing',
            label: '北京'
          }]
        }, {
          label: '城市名',
          options: [{
            value: 'Chengdu',
            label: '成都'
          }, {
            value: 'Shenzhen',
            label: '深圳'
          }, {
            value: 'Guangzhou',
            label: '广州'
          }, {
            value: 'Dalian',
            label: '大连'
          }]
        }],
      value: "",
    };
  },
  watch: {
    // "dialog"(val) {
    //   this.dialogFormVisible = val;
    // },
    "form.report"(val) {
      this.form.report = val;
    },
  },
  created() {
    // this.getGroups();
  },
  methods: {
    open(type, row) {
      this.getGroups();
      this.dialogFormVisible = true;
      // // console.log(type, row);
      this.type = type;
      this.form.report = false;
      if (type === "add") {
        this.form = {
          ident: "",
          name: "",
          icon: "",
          description: "",
          modelGroup: "",
        };
        this.title = "新增模型";
        // // console.log(this.form.report);
      } else if (type === "addTwo") {
        this.form = {
          ident: "",
          name: "",
          icon: "",
          description: "",
          modelGroup: row.ident,
        };
        this.title = "新增模型";
        // // console.log(this.form.report);
      } else {
        this.form = { ...row };
        this.title = "修改模型";
      }
    },
    getGroups() {
      getGroupsList({}).then((res) => {
        if (res.success) {
          this.options = [...res.data];
        } else {
          ElMessage.error(JSON.parse(res.data)?.message);
        }
      });
    },
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.type === "add" || this.type === "addTwo") {
            addModelManage(this.form).then((res) => {
              // console.log(res,'00000000')
              if (res.success) {
                ElMessage.success("新增成功");
                this.dialogFormVisible = false;
                this.$emit("dialogClose", false);
                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
              } else {
                // console.log('00000000')
                this.dialogFormVisible = false;
                this.$emit("dialogClose", false);
                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
                ElMessage.error(JSON.parse(res.data)?.message);
              }
            });
          } else {
            editModelManage(this.form).then((res) => {
              if (res.success) {
                this.dialogFormVisible = false;

                ElMessage.success("修改成功");
                this.$emit("dialogClose", false);

                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
              } else {
                this.dialogFormVisible = false;

                this.$emit("dialogClose", false);
                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
                ElMessage.error(JSON.parse(res.data)?.message);
              }
            });
          }
          // this.dialogFormVisible = false;
        } else {
          return false;
        }
      });
    },
    cancel() {
      this.dialogFormVisible = false;
      // this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      this.$refs["ruleForm"].clearValidate();
    },
    blurChange(data) {
      this.tags.push(data);
    },
    tagClose(index) {
      this.tags.splice(index, 1);
    },
  },
  expose: ["type", "disabled", "title", "form", "open"],
});
</script>
