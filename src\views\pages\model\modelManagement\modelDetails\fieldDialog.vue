<!--  -->
<template>
  <div>
    <el-dialog :title="title" v-model="dialogVisible" :before-close="cancel" width="45%">
      <el-form :model="form" :rules="rules" label-position="left" ref="serviceForm" style="max-height: calc(70vh - 116px);overflow: auto;">
        <el-form-item label="唯一标识" :label-width="formLabelWidth" prop="ident">
          <el-input v-model="form.ident" :disabled="isAdd == 'edit'" autocomplete="off" maxlength="150" show-word-limit clearable placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="字段名称" :label-width="formLabelWidth" prop="name">
          <el-input v-model="form.name" :disabled="isAdd == 'edit'" autocomplete="off" maxlength="150" show-word-limit clearable placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="字段类型" :label-width="formLabelWidth" prop="type">
          <el-select v-model="form.type" :disabled="isAdd == 'edit'" clearable placeholder="请选择">
            <el-option
              v-for="item in optionsT"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <div style="width: 100%;background: rgb(243, 248, 255);padding: 28px;margin-bottom: 28px;" v-if="form.type">
          <el-form-item label="字段设置" :label-width="labelWidth" v-if="form.type == 'SHORT_CHARACTER' || form.type == 'LONG_CHARACTER'">
            <el-checkbox-group v-model="checkList">
              <el-checkbox label="可编辑"></el-checkbox>
              <el-checkbox label="必填"></el-checkbox>
              <el-checkbox label="可录入"></el-checkbox>
              <el-checkbox label="敏感字段"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="字段设置" :label-width="labelWidth" v-else>
            <el-checkbox-group v-model="checkList">
              <el-checkbox label="可编辑"></el-checkbox>
              <el-checkbox label="必填"></el-checkbox>
              <el-checkbox label="可录入"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="正则校验" :label-width="labelWidth" v-if="form.type == 'SHORT_CHARACTER' || form.type == 'LONG_CHARACTER'">
            <el-input v-model="verification" type="textarea" autocomplete="off" maxlength="500" show-word-limit clearable placeholder=""></el-input>
          </el-form-item>
          <el-form-item label="最大值" :label-width="labelWidth" v-if="form.type == 'DIGITAL' || form.type == 'FLOAT'">
            <el-input-number v-model="numMax" :min="numMin" @change="handleChange" label="描述文字"></el-input-number>
          </el-form-item>
          <el-form-item label="最小值" :label-width="labelWidth" v-if="form.type == 'DIGITAL' || form.type == 'FLOAT'">
            <el-input-number v-model="numMin" :max="numMax" @change="handleChange" label="描述文字"></el-input-number>
          </el-form-item>
          <el-form-item label="单位" :label-width="labelWidth" v-if="form.type == 'DIGITAL' || form.type == 'FLOAT'">
            <el-input
              placeholder="请输入单位"
              v-model="form.unit"
              maxlength="6"
              show-word-limit
            >
            </el-input>
          </el-form-item>
          <el-form-item label="枚举值" :label-width="labelWidth" v-if="form.type == 'ENUM'">
            <div style="width: 100%;margin-bottom: 10px;" v-for="(item,index) in meijuList" :key="index">
              <el-input
                placeholder="请输入ID"
                v-model="item.id"
                style="width: 150px;"
              >
              </el-input>
              <el-input
                placeholder="请输入值"
                v-model="item.name"
                style="margin-left: 10px;width: calc(100% - 230px)"
              >
              </el-input>
              <el-button type="danger" style="margin-left: 10px;" @click="delMeiju(index)">删除</el-button>
            </div>
            <div style="width: 100%;">
              <el-button type="primary" style="width: 100%;" @click="addMeiju">添加</el-button>
            </div>
          </el-form-item>
          <!-- <el-form-item label="默认设置" :label-width="labelWidth" v-if="form.type == 'ENUM'">
            <el-select v-model="value" placeholder="请选择">
              <el-option
                v-for="item in optionsF"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="默认状态" :label-width="labelWidth" v-if="form.type == 'BOOL'">
            <el-switch
              v-model="status"
              active-color="#1890ff"
              inactive-color="#dcdfe6">
            </el-switch>
          </el-form-item>
          <el-form-item label="列表值" :label-width="labelWidth" v-if="form.type == 'LIST'">
            <div style="width: 100%;margin-bottom: 10px;" v-for="(item,index) in tableList" :key="index">
              <el-input
                placeholder="请输入列表值"
                v-model="item.name"
                style="width: calc(100% - 70px)"
              >
              </el-input>
              <el-button type="danger" style="margin-left: 10px;" @click="delTable(index)">删除</el-button>
            </div>
            <div style="width: 100%;">
              <el-button type="primary" style="width: 100%;" @click="addTable">添加</el-button>
            </div>
          </el-form-item>
        </div>
        <el-form-item label="用户提示" :label-width="formLabelWidth" prop="helpNote">
          <el-input v-model="form.helpNote" type="textarea" autocomplete="off" maxlength="500" show-word-limit clearable placeholder=""></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { editModelManageOther } from "@/views/pages/apis/model";

export default {
  props: {
    isAdd: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
    fieldDetail: {
      type: Object,
      default: {},
    },
    fieldDetailGroup: {
      type: Object,
      default: {},
    },
    modelDetail: {
      type: Object,
      default: {},
    },
  },
  emits: ["confirm"],
  data() {
    return {
      form: {
        ident: "",
        groupIdent: "",
        name: "",
        type: "",
        helpNote: "",
        readonly: true,
        required: false,
        inputable: false,
        sensitive: false,
        option: "",
        unit: "",
      },
      rules: {
        ident: [{ required: true, message: "请输入", trigger: "blur" }],
        name: [{ required: true, message: "请输入", trigger: "blur" }],
        type: [{ required: true, message: "请选择", trigger: "change" }],
      },
      verification: '',
      status: false,
      numMax: '',
      numMin: '',
      labelWidth: "80px",
      formLabelWidth: "120px",
      dialogVisible: false,
      checkList: ['可编辑'],
      meijuList: [],
      tableList: [],
      title: "",
      optionsF: [{
        value: '1',
        label: '1'
      }, {
        value: '2',
        label: '2'
      }],
      optionsS: [{
        value: '1',
        label: '子系统 属于 子系统'
      }],
      optionsT: [{
        value: 'SHORT_CHARACTER',
        label: '短字符'
      }, {
        value: 'LONG_CHARACTER',
        label: '长字符'
      }, {
        value: 'DIGITAL',
        label: '数字'
      }, {
        value: 'FLOAT',
        label: '浮点型'
      }, {
        value: 'ENUM',
        label: '枚举型'
      }, {
        value: 'DATE_TIME',
        label: '日期'
      }, {
        value: 'TIME_ZONE',
        label: '时区'
      }, {
        value: 'USER',
        label: '用户'
      }, {
        value: 'BOOL',
        label: '布尔型'
      }, {
        value: 'LIST',
        label: '列表'
      }],
      resourceId: "",
      disabledList: [],
    };
  },
  watch: {
    isAdd(val) {
      this.title = val == "add" ? "新建字段" : "修改字段";
      this.form.groupIdent = this.fieldDetailGroup.ident
      if (val === "edit") {
        this.form = { ...this.fieldDetail };
        switch (this.form.type) {
          case 'SHORT_CHARACTER':
            this.verification = this.form.option
            break;
          case 'LONG_CHARACTER':
            this.verification = this.form.option
            break;
          case 'DIGITAL':
            this.numMax = JSON.parse(this.form.option).max
            this.numMin = JSON.parse(this.form.option).min
            break;
          case 'FLOAT':
            this.numMax = JSON.parse(this.form.option).max
            this.numMin = JSON.parse(this.form.option).min
            break;
          case 'ENUM':
            this.meijuList = JSON.parse(this.form.option).nodes
            break;
          case 'BOOL':
            this.status = JSON.parse(this.form.option)
            break;
          case 'LIST':
            let arr = JSON.parse(this.form.option).values
            arr.forEach(item => {
              this.tableList.push({name: item})
            })
            break;
          default:
            break;
        }
        this.checkList = []
        if(!this.form.readonly) {
          this.checkList.push("可编辑")
        }
        if(this.form.required) {
          this.checkList.push("必填")
        }
        if(this.form.inputable) {
          this.checkList.push("可录入")
        }
        if(this.form.sensitive) {
          this.checkList.push("敏感字段")
        }
      } else {
        this.form = {
          ident: "",
          groupIdent: this.fieldDetailGroup.ident,
          name: "",
          type: "",
          helpNote: "",
          readonly: true,
          required: false,
          inputable: false,
          sensitive: false,
          option: "",
          unit: "",
        },
        this.checkList = ['可编辑'],
        this.verification = ''
        this.status = false
        this.numMax = ''
        this.numMin = ''
        this.meijuList = []
        this.tableList = []
      }
    },
  },

  // created() {
  // },
  mounted() {

  },

  methods: {
    addMeiju() {
      this.meijuList.push(
        {id:'',name:'',description:''}
      )
    },
    delMeiju(val) {
      this.meijuList.splice(val, 1)
    },
    addTable() {
      this.tableList.push(
        {name:''}
      )
    },
    delTable(val) {
      this.tableList.splice(val, 1)
    },
    cancel() {
      this.dialogVisible = false;
      // this.$emit("confirm", false);
      this.$refs["serviceForm"].resetFields();
      this.$refs["serviceForm"].clearValidate();
    },
    submit() {
      this.$refs["serviceForm"].validate((valid) => {
        if (valid) {
          if(this.checkList.includes("可编辑")) {
            this.form.readonly = false
          }else{
            this.form.readonly = true
          }
          if(this.checkList.includes("必填")) {
            this.form.required = true
          }else{
            this.form.required = false
          }
          if(this.checkList.includes("可录入")) {
            this.form.inputable = true
          }else{
            this.form.inputable = false
          }
          if(this.checkList.includes("敏感字段")) {
            this.form.sensitive = true
          }else{
            this.form.sensitive = false
          }
          switch (this.form.type) {
            case 'SHORT_CHARACTER':
              this.form.option = this.verification
              break;
            case 'LONG_CHARACTER':
              this.form.option = this.verification
              break;
            case 'DIGITAL':
              this.form.option = JSON.stringify({max: this.numMax, min: this.numMin})
              break;
            case 'FLOAT':
              this.form.option = JSON.stringify({max: this.numMax, min: this.numMin})
              break;
            case 'ENUM':
              this.form.option = JSON.stringify({nodes: this.meijuList})
              break;
            case 'BOOL':
              this.form.option = this.status
              break;
            case 'LIST':
              let arr = []
              this.tableList.forEach(item => {
                arr.push(item.name)
              })
              this.form.option = JSON.stringify({values: arr})
              break;
            default:
              break;
          }
          let arr = this.modelDetail.fields
          if (this.isAdd === "add") {
            arr.push(this.form)
            let params = {
              fields: arr,
              id: this.id,
            }
            editModelManageOther({ ...params })
              .then((res) => {
                // console.log(res);
                if (res.success) {
                  this.$message.success("操作成功");
                  this.$refs["serviceForm"].resetFields();
                  this.dialogVisible = false;
                  this.$emit("confirm", true);
                } else this.$message.error(JSON.parse(res.data)?.message);
              })
              .catch((err) => {
                this.$message.error(err?.message);
              });
          } else {
            arr.forEach(element => {
              if(element.ident==this.form.ident) {
                element.name = this.form.name
                element.type = this.form.type
                element.helpNote = this.form.helpNote
                element.required = this.form.required
                element.inputable = this.form.inputable
                element.readonly = this.form.readonly
                element.sensitive = this.form.sensitive
                element.unit = this.form.unit
                switch (element.type) {
                  case 'SHORT_CHARACTER':
                    element.option = this.verification
                    break;
                  case 'LONG_CHARACTER':
                    element.option = this.verification
                    break;
                  case 'DIGITAL':
                    element.option = JSON.stringify({max: this.numMax, min: this.numMin})
                    break;
                  case 'FLOAT':
                    element.option = JSON.stringify({max: this.numMax, min: this.numMin})
                    break;
                  case 'ENUM':
                    element.option = JSON.stringify({nodes: this.meijuList})
                    break;
                  case 'BOOL':
                    element.option = this.status
                    break;
                  case 'LIST':
                    let arr = []
                    this.tableList.forEach(item => {
                      arr.push(item.name)
                    })
                    element.option = JSON.stringify({values: arr})
                    break;
                  default:
                    break;
                }
              }
            });
            let params = {
              fields: arr,
              id: this.id,
            }
            editModelManageOther({ ...params })
              .then((res) => {
                if (res.success) {
                  this.$message.success("操作成功");
                  this.$refs["serviceForm"].resetFields();
                  this.dialogVisible = false;
                  this.$emit("confirm", true);
                } else this.$message.error(JSON.parse(res.data)?.message);
              })
              .catch((err) => {
                this.$message.error(err?.message);
              });
          }
        }
      });
    },
  },
  expose: ["dialogVisible", "ident", "resourceId", "disabledList", "title", "form"],
};
</script>
<style scoped lang="scss"></style>
