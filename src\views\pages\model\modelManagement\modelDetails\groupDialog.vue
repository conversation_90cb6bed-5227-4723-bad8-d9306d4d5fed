<!--  -->
<template>
  <div>
    <el-dialog :title="title" v-model="dialogVisible" :before-close="cancel" width="45%">
      <el-form :model="form" :rules="rules" label-position="left" ref="serviceForm">
        <el-form-item label="唯一标识" :label-width="formLabelWidth" prop="ident">
          <el-input v-model="form.ident" :disabled="isAdd == 'edit'" autocomplete="off" maxlength="150" show-word-limit clearable placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="分组名称" :label-width="formLabelWidth" prop="name">
          <template #label>
            <div>
              <span>分组名称</span>
              <el-tooltip
                class="box-item"
                effect="dark"
                content="分组名称不可重复"
                placement="top-start"
              >
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-input v-model="form.name" autocomplete="off" maxlength="150" show-word-limit clearable placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="默认状态" :label-width="formLabelWidth" prop="defaultFolding">
          <el-radio-group v-model="defaultFoldingText" @change="changeRadio">
            <el-radio label="separate">折叠</el-radio>
            <el-radio label="joint">展开</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { editModelManageOther } from "@/views/pages/apis/model";
import { QuestionFilled } from "@element-plus/icons-vue";
export default {
  props: {
    isAdd: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
    groupDetail: {
      type: Object,
      default: {},
    },
    modelDetail: {
      type: Object,
      default: {},
    },
  },
  emits: ["confirm"],
  components: {
    QuestionFilled
  },
  data() {
    return {
      form: {
        defaultFolding: false,
        name: "",
        ident: "",
        order: 0,
      },
      defaultFoldingText: 'separate',
      rules: {
        defaultFolding: [{ required: true, message: "请选择", trigger: "change" }],
        name: [{ required: true, message: "请输入", trigger: "blur" }],
        ident: [{ required: true, message: "请输入", trigger: "blur" }],
      },
      formLabelWidth: "120px",
      dialogVisible: false,

      title: "",
      options: [],
      resourceId: "",
      disabledList: [],
    };
  },
  watch: {
    isAdd(val) {
      this.title = val == "add" ? "新建分组" : "修改分组";
      if (val === "edit") {
        this.form = { ...this.groupDetail };
        if(this.form.defaultFolding) {
          this.defaultFoldingText = 'joint'
        } else {
          this.defaultFoldingText = 'separate'
        }
      } else {
        this.defaultFoldingText = 'separate'
        this.form = {
          defaultFolding: false,
          name: "",
          ident: "",
          order: 0,
        }
      }
    },
  },

  // created() {
  // },
  mounted() {

  },

  methods: {
    changeRadio(val) {
      if(val=='separate') {
        this.form.defaultFolding = false
      } else {
        this.form.defaultFolding = true
      }
    },
    cancel() {
      this.dialogVisible = false;
      // this.$emit("confirm", false);
      this.$refs["serviceForm"].resetFields();
      this.$refs["serviceForm"].clearValidate();
    },
    submit() {
      this.$refs["serviceForm"].validate((valid) => {
        if (valid) {
          let arr = this.modelDetail.fieldGroups
          if (this.isAdd === "add") {
            if(arr.length>0) {
              this.form.order = arr[arr.length - 1].order + 1
            }
            arr.push(this.form)
            let params = {
              fieldGroups: arr,
              id: this.id,
            }
            editModelManageOther({ ...params})
              .then((res) => {
                // console.log(res);
                if (res.success) {
                  this.$message.success("操作成功");
                  this.$refs["serviceForm"].resetFields();
                  this.dialogVisible = false;
                  this.$emit("confirm", true);
                } else this.$message.error(JSON.parse(res.data)?.message);
              })
              .catch((err) => {
                this.$message.error(err?.message);
              });
          } else {
            arr.forEach(element => {
              if(element.ident==this.form.ident) {
                element.name = this.form.name
                element.defaultFolding = this.form.defaultFolding
              }
            });
            let params = {
              fieldGroups: arr,
              id: this.id,
            }
            editModelManageOther({ ...params })
              .then((res) => {
                if (res.success) {
                  this.$message.success("操作成功");
                  this.$refs["serviceForm"].resetFields();
                  this.dialogVisible = false;
                  this.$emit("confirm", true);
                } else this.$message.error(JSON.parse(res.data)?.message);
              })
              .catch((err) => {
                this.$message.error(err?.message);
              });
          }
        }
      });
    },
  },
  expose: ["dialogVisible", "name", "resourceId", "disabledList", "title", "form"],
};
</script>
<style scoped lang="scss"></style>
