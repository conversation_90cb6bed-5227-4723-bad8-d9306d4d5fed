<template>
  <div :style="{ height: 'calc(100% - 28px)' }" class="device-detail">
    <el-page-header @back="backRouter" :content="'模型详情'"></el-page-header>
    <div class="details" :style="{ height: 'calc(100% - 38px)' }">
      <el-row class="title">
        <el-col :span="12">
          <div class="device-img">
            <img :src="deviceImgList[0].normalImg" alt="" />
            <span style="margin-left: 10px; color: #737987">唯一标识：</span>
            <span>{{ modelDetail.ident }}</span>
            <span style="margin-left: 10px; color: #737987">名称：</span>
            <span>{{ modelDetail.name }}</span>
            <span style="margin-left: 10px; color: #737987">实例数量：</span>
            <span>{{ modelDetail.resourceCount }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div style="text-align: right">
            <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.editor)">
              <el-link :underline="false" type="primary" icon="el-icon-plus" @click="modelCreateClick('edit')" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.editor)">编辑模型</el-link>
            </el-tooltip>
            <el-tooltip :content="$t('glob.noPower')" v-if="!modelDetail.enabled" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.auth543656566851633152)">
              <el-link :underline="false" type="primary" icon="el-icon-edit" @click="modelStopClick()" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.auth543656566851633152)">{{ "启用模型" }}</el-link>
            </el-tooltip>
            <el-tooltip :content="$t('glob.noPower')" v-if="modelDetail.enabled" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.auth542949985486897152)">
              <el-link :underline="false" type="primary" icon="el-icon-edit" @click="modelStopClick()" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.auth542949985486897152)">{{ "停用模型" }}</el-link>
            </el-tooltip>
            <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.remove)">
              <el-link :underline="false" type="danger" icon="el-icon-delete" @click="modelDelete()" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.remove)">删除模型</el-link>
            </el-tooltip>
          </div>
        </el-col>
      </el-row>
      <div class="device-detail-tab-list">
        <el-tabs v-model="activeName">
          <el-tab-pane label="模型字段" name="模型字段">
            <div style="height: 48px">
              <el-input v-model="keyword" @clear="getDetail()" clearable style="width: 220px; float: left" placeholder="请输入关键字" @keyup.enter="getDetail()">
                <template #append>
                  <el-button :icon="Search" @click="getDetail()" />
                </template>
              </el-input>
              <div style="float: right">
                <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.auth542951079487209472)">
                  <el-button size="medium" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.auth542951079487209472)" @click="modelViewClick('add')">字段预览</el-button>
                </el-tooltip>

                <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.auth542950973820108800)">
                  <el-button size="medium" type="primary" @click="sortClick('add')" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.auth542950973820108800)">表格排序设置</el-button>
                </el-tooltip>
              </div>
            </div>
            <div style="height: 500px; width: 100%">
              <el-collapse v-model="activeNames" @change="handleChange">
                <el-collapse-item :name="item.ident" v-for="(item, index) in modelDetail.fieldGroups" :key="item.ident">
                  <template v-slot:title>
                    <div @mouseenter="enter(item.ident)" @mouseleave="out" style="width: 100%; display: flex">
                      <div style="width: 50%">
                        <el-divider direction="vertical"></el-divider>
                        {{ item.name }}
                      </div>
                      <div style="width: 50%; text-align: right; padding-right: 20px; visibility: hidden" :class="{ showBtn: item.ident === isShowBtn }">
                        <el-link :underline="false" type="primary" icon="el-icon-plus" @click.stop="topGroupClick(item, index)" :disabled="index == 0">上移分组</el-link>
                        <el-link :underline="false" type="primary" icon="el-icon-plus" @click.stop="bottomGroupClick(item, index)" :disabled="index == modelDetail.fieldGroups.length - 1">下移分组</el-link>
                        <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.auth542939547625848832)">
                          <el-link :underline="false" type="primary" icon="el-icon-edit" @click.stop="groupCreateClick('edit', item)" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.auth542939547625848832)">编辑分组</el-link>
                        </el-tooltip>
                        <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.auth542939584871268352)">
                          <el-link :underline="false" type="danger" icon="el-icon-delete" @click.stop="groupDelete(item)" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.auth542939584871268352)">删除分组</el-link>
                        </el-tooltip>
                      </div>
                    </div>
                  </template>
                  <div style="width: 100%; padding: 16px; display: flex; flex-wrap: wrap; justify-content: flex-start; align-content: space-between">
                    <div v-for="val in item.fields" :key="val.ident" @mouseenter="enterBox(val.ident + item.ident)" @mouseleave="outBox" style="width: 19%; height: 68px; border: 1px solid #dde4eb; display: flex; cursor: pointer; border-radius: 4px; margin: 0 1% 10px 0">
                      <div style="width: 66%; height: 100%; display: flex">
                        <div style="width: 20px; height: 100%">
                          <div style="width: 100%; height: 50%; margin-left: 5px; margin-top: 9px; display: flex; justify-content: space-between; flex-wrap: wrap">
                            <div style="width: 50%; height: 10%">.</div>
                            <div style="width: 50%; height: 10%">.</div>
                            <div style="width: 50%; height: 10%">.</div>
                            <div style="width: 50%; height: 10%">.</div>
                            <div style="width: 50%; height: 10%">.</div>
                            <div style="width: 50%; height: 10%">.</div>
                            <div style="width: 50%; height: 10%">.</div>
                            <div style="width: 50%; height: 10%">.</div>
                          </div>
                        </div>
                        <div style="width: calc(100% - 20px); height: 100%; padding-left: 5px">
                          <p style="margin-top: 13px; text-overflow: ellipsis; white-space: nowrap; overflow: hidden">{{ val.name }}</p>
                          <p style="color: #bfc7d2; text-overflow: ellipsis; white-space: nowrap; overflow: hidden">{{ optionsFiled[val.type] }}：{{ val.ident }}</p>
                        </div>
                      </div>
                      <div :class="{ activeTwoShow: val.ident + item.ident === isActiveShow }" style="width: 34%; height: 100%; visibility: hidden; line-height: 75px; text-align: center">
                        <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.editor)">
                          <el-icon :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.editor)" color="#3a84ff" :size="16">
                            <el-button type="primary" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.editor)" size="small" circle @click="fieldCreate('edit', item, val)">
                              <Edit />
                            </el-button>
                          </el-icon>
                        </el-tooltip>
                        <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.remove)">
                          <el-icon :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.remove)" style="margin-left: 10px" color="#ff4949" :size="16">
                            <el-button type="danger" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.remove)" size="small" circle @click="deleteField(val)">
                              <Delete />
                            </el-button>
                          </el-icon>
                        </el-tooltip>
                      </div>
                    </div>
                    <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.create)">
                      <el-button @click="fieldcreate('add', item, modelDetail.fields)" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.create)" style="width: 19%; height: 68px">添加</el-button>
                    </el-tooltip>
                  </div>
                </el-collapse-item>
              </el-collapse>
              <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.auth542939509373796352)">
                <el-button size="medium" @click="groupCreateClick('add')" style="margin-top: 20px" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.auth542939509373796352)">新建分组</el-button>
              </el-tooltip>
            </div>
          </el-tab-pane>
          <el-tab-pane label="模型关联" name="模型关联">
            <page-template :search-span="24" :showPaging="true" v-model:current-page="paging.pageNumber" v-model:page-size="paging.pageSize" :total="paging.total" @current-change="getServiceNumberList()" @size-change="getServiceNumberList()">
              <template #right>
                <div class="work">
                  <h3>
                    <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.auth543231828845133824)">
                      <el-button type="primary" :icon="Plus" @click="serviceSize('add')" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.auth543231828845133824)"> 新建关联 </el-button>
                    </el-tooltip>
                  </h3>
                </div>
              </template>
              <template #default="{}">
                <el-table :data="serviceList" stripe style="width: 100%" height="450" border>
                  <el-table-column prop="ident" label="唯一标识" width="180"> </el-table-column>
                  <el-table-column prop="sourceModel" label="源模型" width="180"> </el-table-column>
                  <el-table-column prop="type" label="关联类型" :formatter="formatterType"> </el-table-column>
                  <el-table-column prop="targetModel" label="目标类型"> </el-table-column>
                  <el-table-column prop="constraint" label="源-目标约束" :formatter="formatterConstraint"> </el-table-column>
                  <el-table-column prop="address" label="操作" width="180">
                    <template #default="{ row }">
                      <div>
                        <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.auth543231870863671296)">
                          <el-button type="text" @click="serviceSize('edit', row)" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.auth543231870863671296)">编辑</el-button>
                        </el-tooltip>
                        <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.auth543231906859188224)">
                          <el-button type="text" textColor="danger" @click="deleteService(row)" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.auth543231870863671296)">删除</el-button>
                        </el-tooltip>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </page-template>
          </el-tab-pane>
          <el-tab-pane label="唯一校验" name="唯一校验">
            <page-template :search-span="24" :showPaging="false" v-model:current-page="slaPaging.pageNumber" v-model:page-size="slaPaging.pageSize" :total="slaPaging.total" @current-change="getDetail()" @size-change="getDetail()">
              <template #right>
                <h3>
                  <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.create)">
                    <el-button type="primary" :icon="Plus" @click="bindSla('add')" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.create)"> 新建校验 </el-button>
                  </el-tooltip>
                </h3>
              </template>
              <template #default="{}">
                <el-table :data="modelDetail.uniques" height="450" stripe style="width: 100%" border>
                  <el-table-column prop="fieldsName" label="检验规则">
                    <!-- <template #default="{ row }">
                      <div>
                        {{ row.fields.includes() }}
                      </div>
                    </template> -->
                  </el-table-column>

                  <el-table-column prop="address" label="操作" width="180">
                    <template #default="{ row }">
                      <div>
                        <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.editor)">
                          <el-button type="text" @click="bindSla('edit', row)" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.editor)">编辑</el-button>
                        </el-tooltip>
                        <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.remove)">
                          <el-button type="text" textColor="danger" @click="deleteSla(row)" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.remove)">删除</el-button>
                        </el-tooltip>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </page-template>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <serviceDialog :dataBox="dataBox" :modelDetail="modelDetail" :isAdd="serviceType" :id="id" ref="serviceRef" @confirm="confirmService"></serviceDialog>
    <slaDialog :isAdd="ruleType" :modelDetail="modelDetail" :slaDetail="slaDetail" :id="id" @confirm="confirmSla" ref="slaRef"> </slaDialog>
    <modelEdit :isAdd="modelType" :modelDetail="modelDetail" :id="id" @confirm="confirmModel" ref="modelRef"></modelEdit>
    <viewModel :isAdd="modelviewType" :modelDetail="modelDetail" :id="id" @confirm="confirmModelview" ref="modelviewRef"></viewModel>
    <groupDialog :isAdd="groupType" :modelDetail="modelDetail" :groupDetail="groupDetail" :id="id" @confirm="confirmGroup" ref="groupRef"></groupDialog>
    <fieldDialog :isAdd="fieldType" :modelDetail="modelDetail" :fieldDetail="fieldDetail" :fieldDetailGroup="fieldDetailGroup" :id="id" @confirm="confirmField" ref="fieldRef"></fieldDialog>
    <sortDialog :isAdd="sortType" :modelDetail="modelDetail" :id="id" @confirm="confirmSort" ref="sortRef"></sortDialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import ModelExpand from "@/views/pages/modelExpand/Model.vue";

import pageTemplate from "@/components/pageTemplate";
import serviceDialog from "./serviceDialog.vue";
import { Download, DeleteFilled, Plus } from "@element-plus/icons-vue";
import { getServiceList, delService } from "@/views/pages/apis/deviceManage";
import slaDialog from "./slaDialog.vue";
import modelEdit from "./modelEdit.vue";
import viewModel from "./viewModel.vue";
import groupDialog from "./groupDialog.vue";
import fieldDialog from "./fieldDialog.vue";
import sortDialog from "./sortDialog.vue";
import bindContacts from "@/components/bindContacts/vIndex.vue";
import moment from "moment";
import { state, dataList, expand, select, current } from "../associationType/helper";

import { deviceRelationSlaList, getDeviceLogList, deleteDeviceDeviceLogFile, downloadDeviceLogFile, addDeviceLogFile, deleteDeviceLog, editDeviceLog, getDeviceWorkOrderList, getDeviceRequestList, deviceRelationSupportList, uploadFile, deleteDeviceFile, getDeviceFileList, downloadDeviceFile, addDeviceLog, saveDeviceFile } from "@/views/pages/apis/deviceManage";
import { deleteDeviceRelation } from "@/views/pages/apis/SlaConfig";
import { delModelManage, getmodelRelationsList, getModelManageList, editModelManage, delmodelRelations, editModelManageOther, modelManageDetail, addmodelRelations, editmodelRelations } from "@/views/pages/apis/model";
import { getAlarmBoardDetail } from "@/views/pages/apis/alarmBoard";

import { h } from "vue";
import getUserInfo from "@/utils/getUserInfo";
const userInfo = getUserInfo();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */

// const refs = ref({});
export default {
  name: "deviceManageDetails",
  components: {
    pageTemplate,
    serviceDialog,
    slaDialog,
    modelEdit,
    viewModel,
    groupDialog,
    fieldDialog,
    sortDialog,
    // bindContacts,
    Download,
    DeleteFilled,
    ModelExpand,
  },
  data() {
    return {
      userInfo,
      paging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      activeNames: [],
      uploadDialog: false,
      activeName: "模型字段",
      content: "设备详情",
      isShowBtn: false,
      isActiveShow: false,
      showCancel: false, //是否展示取消按钮
      showPercentage: false, //是否展示上传进度
      percentage: 0, //上传进度
      showFile: false, //展示上传后的文件列表
      uploadTitle: "",
      showUploadFial: false,
      fileList: [],
      id: this.$route.params.id,
      modelName: this.$route.query.name,
      keyword: "",
      modelDetail: "",
      groupFileds: [],
      optionsFiled: {
        SHORT_CHARACTER: "短字符",
        LONG_CHARACTER: "长字符",
        DIGITAL: "数字",
        FLOAT: "浮点型",
        ENUM: "枚举型",
        DATE_TIME: "日期",
        TIME_ZONE: "时间",
        USER: "用户",
        BOOL: "布尔型",
        LIST: "列表",
      },
      tableHeight: "500px",
      form: {
        modelIdent: null, //模型标识
        regionId: null, //所在区域ID
        locationId: null, //所在场所ID
        typeIds: [], //设备类型ID列表
        groupIds: [], //设备组ID列表
        vendorIds: [], //服务商ID列表
        alertClassificationIds: [], //告警分类ID列表
        externalId: null, //外部ID
        name: null, //设备名称
        description: null, //设备描述
        timeZone: null, //时区
        importance: null, //资源重要性
        tags: [], //标签
        active: true, //是否激活
        serviceNumbers: [],
        config: {
          ipAddress: "", //ip地址
          dynamicIp: "", //是否默认IP
          ackRequired: "", //确认告警

          nmsTicketing: "", //自动事件
          connectAuthType: "", //远程登录认证
          modelNumbers: [], //型号
          serialNumbers: [], //序列号
          assetNumbers: [], //资产编号
        },
      },
      deviceImgList: [
        {
          normalImg: require("@/views/pages/assets/device/desktop-normal.png"),
          offlineImg: require("@/views/pages/assets/device/desktop-offline.png"),
          warningImg: require("@/views/pages/assets/device/desktop-warning.png"),
        },
        {
          normalImg: require("@/views/pages/assets/device/wifi-normal.png"),
          offlineImg: require("@/views/pages/assets/device/wifi-offline.png"),
          warningImg: require("@/views/pages/assets/device/wifi-warning.png"),
        },
        {
          normalImg: require("@/views/pages/assets/device/share-normal.png"),
          offlineImg: require("@/views/pages/assets/device/share-offline.png"),
          warningImg: require("@/views/pages/assets/device/share-warning.png"),
        },
        {
          normalImg: require("@/views/pages/assets/device/vertical-normal.png"),
          offlineImg: require("@/views/pages/assets/device/vertical-offline.png"),
          warningImg: require("@/views/pages/assets/device/vertical-warning.png"),
        },
      ],
      weekDay: 1, //判断当前日期是周几
      serviceType: "",
      ruleType: "",
      modelType: "",
      modelviewType: "",
      groupType: "",
      fieldType: "",
      sortType: "",
      serviceList: [],
      groupDetail: {},
      fieldDetail: {},
      fieldDetailGroup: {},
      slaDetail: {},
      slaList: [],
      slaPaging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      workOrderList: [],
      workOrderPaging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      requestList: [],
      requestPaging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      alarmList: [],
      alarmPaging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      supportList: [],
      deviceFileList: [],
      filePaging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      logList: [],
      logPaging: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      logId: "",
      dataBox: [],
    };
  },
  computed: {
    routeName() {
      return this.$route.name;
    },
  },

  mounted() {
    this.id = this.$route.params.id;
    this.getDetail(); //模型详情
    this.getSlaDownList(); //所有模型
    this.getServiceNumberList(); //模型关联
    // this.getSlaList(); //sla配置
    // this.getRequestList(); //服务请求
  },
  methods: {
    moment,
    enter(key) {
      this.isShowBtn = key;
    },
    out() {
      this.isShowBtn = false;
    },
    enterBox(index) {
      this.isActiveShow = index;
    },
    outBox() {
      this.isActiveShow = false;
    },
    formatterType(_row, _col, v) {
      switch (v) {
        case "cc_belong":
          return "属于";
        case "cc_contain":
          return "包含";
      }
    },
    formatterConstraint(_row, _col, v) {
      switch (v) {
        case "O2O":
          return "一对一";
        case "O2N":
          return "一对多";
        case "N2O":
          return "多对一";
        case "N2N":
          return "多对多";
      }
    },
    getRequestList() {
      // // console.log(getUserInfo().currentTenant, 555555);
      //getUserInfo()
      getDeviceRequestList({ deviceId: this.id, ...this.requestPaging, tenantId: getUserInfo().currentTenant.tenantId }).then((res) => {
        if (res.success) {
          this.requestList = [...res.data];
          this.requestPaging.total = res.total * 1;
        }
      });
    },
    handleChange(val) {
      // console.log(val);
    },
    topGroupClick(val, index) {
      let arr = this.modelDetail.fieldGroups;
      // arr.splice(index-1, 1, ...arr.splice(index, 1, arr[index-1]))
      let number;
      number = arr[index - 1].order;
      arr[index - 1].order = arr[index].order;
      arr[index].order = number;
      let params = {
        fieldGroups: arr,
        id: this.id,
      };
      editModelManageOther({ ...params })
        .then((res) => {
          if (res.success) {
            this.$message.success("操作成功");
            this.getDetail();
          } else this.$message.error(JSON.parse(res.data)?.message);
        })
        .catch((err) => {
          this.$message.error(err?.message);
        });
    },
    bottomGroupClick(val, index) {
      let arr = this.modelDetail.fieldGroups;
      // arr.splice(index, 1, ...arr.splice(index+1, 1, arr[index]))
      let number;
      number = arr[index].order;
      arr[index].order = arr[index + 1].order;
      arr[index + 1].order = number;
      let params = {
        fieldGroups: arr,
        id: this.id,
      };
      editModelManageOther({ ...params })
        .then((res) => {
          if (res.success) {
            this.$message.success("操作成功");
            this.getDetail();
          } else this.$message.error(JSON.parse(res.data)?.message);
        })
        .catch((err) => {
          this.$message.error(err?.message);
        });
    },
    getSlaDownList() {
      const params = {
        key: "",
        enabled: "",
      };
      getModelManageList(params as never).then(({ success, message, data, total }) => {
        if (!success) throw new Error(message);
        this.dataBox = data;
      });
    },
    getSlaList() {
      // console.log(this.keyword, "888888");
    },
    //模型关联 新增/编辑事件操作
    confirmService(bool) {
      this.serviceType = "";
      if (bool) {
        this.getServiceNumberList();
      }
    },
    //编辑模型
    modelCreateClick(type, id) {
      this.modelType = type;
      this.$refs.modelRef.dialogVisible = true;
    },
    //预览模型
    modelViewClick(type, id) {
      this.modelviewType = type;
      this.$refs.modelviewRef.dialogVisible = true;
    },
    //表格排序设置
    sortClick(type, id) {
      this.sortType = type;
      this.$refs.sortRef.dialogVisible = true;
    },
    //停用启用模型
    modelStopClick(val) {
      let params = {
        ident: this.modelDetail.ident,
        enabled: this.modelDetail.enabled ? false : true,
      };
      editModelManage(params).then((res) => {
        if (res.success) {
          this.$message.success("操作成功");
          this.getDetail();
        } else {
          this.$message.error(JSON.parse(res.data)?.message);
        }
      });
    },
    //新建唯一检验
    bindSla(type, row) {
      this.ruleType = type;
      this.$refs.slaRef.dialogVisible = true;
      if (row) {
        this.slaDetail = row;
      }
    },
    //新建字段
    fieldCreate(type, row, val) {
      // console.log(row, val, "88888");
      this.fieldType = type;
      this.$refs.fieldRef.dialogVisible = true;
      this.fieldDetailGroup = row;
      this.fieldDetail = val;
    },
    //新建分组
    groupCreateClick(type, row) {
      this.groupType = type;
      this.$refs.groupRef.dialogVisible = true;
      if (row) {
        this.groupDetail = row;
      }
    },
    //删除分组
    groupDelete(row) {
      this.$confirm(`确定删除该分组吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let arr = this.modelDetail.fieldGroups;
          let arrFile = this.modelDetail.fields;
          arr.forEach((element, index) => {
            if (element.ident == row.ident) {
              arr.splice(index, 1);
            }
          });
          arrFile.forEach((element, index) => {
            if (element.groupIdent == row.ident) {
              arrFile.splice(index, 1);
            }
          });
          let params = {
            fieldGroups: arr,
            fields: arrFile,
            id: this.id,
          };
          editModelManageOther({ ...params }).then((res) => {
            if (res.success) {
              this.$message.success("操作成功");
              this.getDetail();
            } else this.$message.error(JSON.parse(res.data)?.message);
          });
        })
        .catch(() => {
          //
        });
    },
    //删除字段
    deleteField(row) {
      this.$confirm(`确定删除该字段吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let arr = this.modelDetail.fields;
          arr.forEach((element, index) => {
            if (element.ident == row.ident) {
              arr.splice(index, 1);
            }
          });
          let params = {
            fields: arr,
            id: this.id,
          };
          editModelManageOther({ ...params }).then((res) => {
            if (res.success) {
              this.$message.success("操作成功");
              this.getDetail();
            } else this.$message.error(JSON.parse(res.data)?.message);
          });
        })
        .catch(() => {
          //
        });
    },
    //删除模型
    modelDelete(row) {
      this.$confirm(`确定删除该模型吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delModelManage({ ident: this.id }).then((res) => {
            if (res.success) {
              this.$message.success("操作成功");
              this.backRouter();
            } else this.$message.error(JSON.parse(res.data)?.message);
          });
        })
        .catch(() => {
          //
        });
    },
    //删除检验规则
    deleteSla(row) {
      this.$confirm(`确定删除该校验规则吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let arr = this.modelDetail.uniques;
          arr.forEach((element, index) => {
            if (element.fields == row.fields) {
              arr.splice(index, 1);
            }
          });
          let params = {
            uniques: arr,
            id: this.id,
          };
          editModelManageOther({ ...params }).then((res) => {
            if (res.success) {
              this.$message.success("操作成功");
              this.getDetail();
            } else this.$message.error(JSON.parse(res.data)?.message);
          });
        })
        .catch(() => {
          //
        });
    },
    confirmSla() {
      this.ruleType = "";
      this.getDetail();
    },
    confirmGroup() {
      this.groupType = "";
      this.getDetail();
    },
    confirmField() {
      this.fieldType = "";
      this.getDetail();
    },
    confirmSort() {
      this.sortType = "";
      this.getDetail();
    },
    confirmModel() {
      this.modelType = "";
      this.getDetail();
    },
    confirmModelview() {
      this.modelviewType = "";
    },

    //模型关联列表
    getServiceNumberList() {
      getmodelRelationsList({
        pageNumber: this.paging.pageNumber,
        pageSize: this.paging.pageSize,
        ident: this.id,
      }).then((res) => {
        if (res.success) {
          this.serviceList = res.data;
          this.paging.total = Number(res.total);
        }
      });
    },
    //删除模型关联
    deleteService(row) {
      this.$confirm(`确定删除${row.number}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delmodelRelations({ ident: row.ident }).then((res) => {
            if (res.success) {
              this.$message.success("操作成功");
              this.getServiceNumberList();
            } else this.$message.error(JSON.parse(res.data)?.message);
          });
        })
        .catch(() => {
          //
        });
    },
    //编辑/新增服务编号
    serviceSize(type, row) {
      this.serviceType = type;
      (this.$refs.serviceRef as any).dialogVisible = true;
      if (row) {
        (this.$refs.serviceRef as any).form = row;
      }
    },
    backRouter /* 返回上一页 */() {
      if ("fallback" in this.$route.query && typeof this.$route.query.fallback === "string") this.$router.push({ name: this.$route.query.fallback });
      else this.$router.back();
    },
    //模型详情
    getDetail() {
      this.activeNames = [];
      modelManageDetail({ ident: this.id, key: this.keyword }).then((res) => {
        if (res.success) {
          res.data.fieldGroups.forEach((item) => {
            if (item.defaultFolding) {
              this.activeNames.push(item.ident);
            }
            item.fields = [];
            res.data.fields.forEach((element) => {
              if (item.ident === element.groupIdent) {
                item.fields.push(element);
              }
            });
          });
          let labelList = {};
          res.data.fields.forEach((item) => {
            if (item.type === "BOOL") {
              item.value = false;
            } else {
              item.value = "";
            }
            labelList[item.ident] = item.name;
          });
          res.data.uniques.forEach((element) => {
            element.fieldsName = [];
            element.fields.forEach((f_item) => {
              element.fieldsName.push(labelList[f_item]);
            });
          });
          this.modelDetail = res.data;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.el-divider--vertical {
  width: 4px;
  height: 14px;
  background: #c3cdd7;
  margin-left: 0;
}
.device-log-list {
  width: 100%;
  height: auto;
  // border: 1px solid #e4eced;
  padding: 20px;
  box-sizing: border-box;
  // min-height: 550px;
  > li {
    width: 100%;
    height: auto;
    border: 1px solid #e4eced;
    padding: 10px 20px;
    box-sizing: border-box;
    // min-height: 200px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    margin-bottom: 10px;
    // justify-content: space-between;
    > .log-content {
      // flex: 1;
      border-bottom: 1px solid #e4eced;
      height: auto;
      margin-bottom: 10px;
    }
    > .log-file {
      flex: none;
      // border-bottom: 1px solid #e4eced;
      height: auto;
      // padding: 8px 0;
      box-sizing: border-box;
      display: flex;
      flex-wrap: wrap;
      span {
        display: block;
        flex: none;
        height: 35px;
        padding: 8px;
        box-sizing: border-box;
        background: rgb(250, 250, 250);
        color: rgb(72, 137, 237);
        border: 1px solid #e4eced;
        margin: 0 10px 10px 0;
      }
    }
    > .log-operate {
      flex: none;
      border-top: 1px solid #e4eced;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: auto;
    }
  }
}
.imgList {
  display: flex;
  flex-direction: column;
  > li {
    display: flex;
    align-items: center;
    padding: 8px 0;
    box-sizing: border-box;

    .file-img {
      width: 42px;
      height: 42px;
      margin-right: 10px;
    }
  }
}
.upload-fail {
  display: flex;
  align-items: center;
}
.device-detail {
  overflow-y: auto;
}
.device-detail-tab-list {
  padding: 0 20px;
  box-sizing: border-box;
  background: #fff;
  :deep(.el-tabs__content) {
    overflow-y: auto;
  }
}
.priority {
  color: #fff;
  padding: 5px 10px;
  box-sizing: border-box;
  border-radius: 20px;
}
.details {
  background: #fff;
  margin-top: 10px;
}
.title {
  padding: 26px 20px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  p {
    line-height: 30px;
    font-size: 18px;
  }
  border-bottom: 3px solid rgb(241, 241, 241);
}
.device-img {
  display: flex;
  > img {
    margin-right: 8px;
    cursor: pointer;
    width: 25px;
    height: auto;
  }
  > span {
    line-height: 25px;
  }
}
// ::v-deep .elstyle-tabs__nav-wrap {
//   padding: 0 20px;
//   box-sizing: border-box;
// }
// ::v-deep .elstyle-tab-pane {
//   padding: 0 20px;
//   box-sizing: border-box;
// }
.message {
  width: 100%;
  height: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  > div {
    flex: none;
    width: 48%;
    border: 1px solid #ddd;
    padding: 20px;
    box-sizing: border-box;
  }
  ul > li {
    display: flex;
    justify-content: space-between;
    height: 40px;
    align-items: center;
    padding: 10px 15px 0;
    box-sizing: border-box;
  }
  ul > li:nth-child(odd) {
    border-bottom: 1px solid #ddd;
    background: rgb(241, 241, 241);
  }
  .monitor {
    display: flex;
    flex-direction: column;
    > div {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ddd;
    }
  }
}
.work {
  width: 100%;
  height: auto;
  > div {
    margin-bottom: 30px;
    border: 1px solid #ddd;
    padding: 20px;
    box-sizing: border-box;

    // height: 300px;
    // overflow-y: auto;
  }
  h3 {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.strategy-title {
  padding: 10px;
  box-sizing: border-box;
}
.strategy {
  width: 100%;
  height: auto;
  display: flex;
  // justify-content: space-between;
  margin-bottom: 20px;

  > div {
    margin-right: 10px;
    width: 40%;
    min-height: 200px;
    border: 1px solid #ddd;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    // align-items: center;
    > h2 {
      flex: none;
      width: 100%;
      height: 50px;
      line-height: 50px;
      padding-left: 15px;
      background: #ddd;
      // border-bottom: 1px solid #ddd;
    }
    div {
      width: 100%;
      min-height: 100px;
      flex: 1;
      // line-height: 100px;
      padding-left: 15px;
    }
    h4 {
      border-top: 1px solid #ddd;
      width: 100%;
      height: 50px;
      line-height: 50px;
      padding-left: 15px;
    }
  }
  > div.week {
    border-color: #3e97ff;
    > h2 {
      background: #3e97ff;
      color: #fff;
    }
  }
}
.showBtn {
  visibility: visible !important;
}
.activeTwoShow {
  visibility: visible !important;
}
</style>
