<!--  -->
<template>
  <div>
    <el-dialog :title="title" v-model="dialogVisible" width="45%" :before-close="cancel">
      <el-form :model="form" :rules="rules" label-position="left" ref="serviceFormRef">
        <el-form-item label="所属分组" :label-width="formLabelWidth" prop="modelGroup">
          <el-select :disabled="isAdd !== 'add'" v-model="form.modelGroup" clearable placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.ident"
              :label="item.name"
              :value="item.ident">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="唯一标识" :label-width="formLabelWidth" prop="ident">
          <el-input :disabled="isAdd !== 'add'" v-model="form.ident" autocomplete="off" maxlength="150" show-word-limit clearable placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="模型名称" :label-width="formLabelWidth" prop="name">
          <el-input v-model="form.name" autocomplete="off" maxlength="150" show-word-limit clearable placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="模型图标" :label-width="formLabelWidth" prop="icon">
          <el-select v-model="form.icon" placeholder="请选择">
            <el-option-group
              v-for="group in optionsTwo"
              :key="group.label"
              :label="group.label">
              <el-option
                v-for="item in group.options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" :label-width="formLabelWidth" prop="description">
          <el-input v-model="form.description" type="textarea" autocomplete="off" maxlength="500" show-word-limit clearable placeholder=""></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import mixin from "./mixin";
import { addModelManage, editModelManage } from "@/views/pages/apis/model";
export default {
  mixins: [mixin],
  props: {
    isAdd: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
    modelDetail: {
      type: Object,
      default: {},
    },
  },
  emits: ["confirm"],
  data() {
    return {
      form: {
        ident: "",
        name: "",
        icon: "",
        description: "",
        modelGroup: "",
        id: "",
      },
      formLabelWidth: "120px",
      dialogVisible: false,
      rules: {
        ident: [{ required: true, message: "请输入", trigger: "blur" }],
        name: [{ required: true, message: "请输入", trigger: "blur" }],
        modelGroup: [{ required: true, message: "请选择", trigger: "change" }],
      },
      title: "",
    };
  },
  watch: {
    isAdd(val) {
      this.title = val == "add" ? "新建模型" : "修改模型";
      if (val === "edit") {
        this.form = { ...this.modelDetail };
      }
    },
  },
  // created() {
  // },

  methods: {
    cancel() {
      this.dialogVisible = false;
      // this.$emit("confirm", false);
      this.$refs["serviceFormRef"].resetFields();
      this.$refs["serviceFormRef"].clearValidate();
    },
    submit() {
      this.$refs["serviceFormRef"].validate((valid) => {
        if (valid) {
          // alert("submit!");
          if (this.isAdd === "add") {
            delete this.form.id;
            addModelManage({ ...this.form })
              .then((res) => {
                // console.log(res);
                if (res.success) {
                  this.$message.success("操作成功");
                  this.$refs["serviceFormRef"].resetFields();
                  this.dialogVisible = false;
                  this.$emit("confirm", true);
                } else this.$message.error(JSON.parse(res.data)?.message);
              })
              .catch((err) => {
                this.$message.error(err?.message);
              });
          } else {
            editModelManage({ ...this.form })
              .then((res) => {
                if (res.success) {
                  this.$message.success("操作成功");
                  this.$refs["serviceFormRef"].resetFields();
                  this.dialogVisible = false;
                  this.$emit("confirm", true);
                } else this.$message.error(JSON.parse(res.data)?.message);
              })
              .catch((err) => {
                this.$message.error(err?.message);
              });
          }
        }
      });
    },
  },
  expose: ["dialogVisible", "title", "form"],
};
</script>
<style scoped lang="scss"></style>
