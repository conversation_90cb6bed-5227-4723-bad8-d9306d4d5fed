<!--  -->
<template>
  <div>
    <el-dialog :title="title" v-model="dialogVisible" width="45%" :before-close="cancel">
      <el-form :model="form" label-position="left" :rules="rules" ref="serviceFormRef">
        <el-form-item label="源模型" :label-width="formLabelWidth" prop="sourceModel">
          <el-select :disabled="isAdd !== 'add'" v-model="form.sourceModel" placeholder="请选择" clearable style="width: 100%">
            <el-option-group
              v-for="group in dataBox"
              :key="group.ident"
              :label="group.name">
              <el-option
                v-for="item in group.models"
                :key="item.ident"
                :label="item.name"
                :value="item.ident">
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="目标模型" :label-width="formLabelWidth" prop="targetModel">
          <el-select :disabled="isAdd !== 'add'" v-model="form.targetModel" placeholder="请选择" clearable style="width: 100%">
            <el-option-group
              v-for="group in dataBox"
              :key="group.ident"
              :label="group.name">
              <el-option
                v-for="item in group.models"
                :key="item.ident"
                :label="item.name"
                :value="item.ident">
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="关联类型" :label-width="formLabelWidth" prop="type">
          <el-select :disabled="isAdd !== 'add'" v-model="form.type" placeholder="请选择" clearable style="width: 100%">
            <el-option v-for="item in typeList" :key="item.ident" :label="item.name" :value="item.ident"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="源-目标约束" :label-width="formLabelWidth" prop="constraint">
          <el-select :disabled="isAdd !== 'add'" v-model="form.constraint" placeholder="请选择" clearable style="width: 100%">
            <el-option v-for="item in constraintList" :key="item.ident" :label="item.name" :value="item.ident"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关联描述" :label-width="formLabelWidth" prop="description">
          <el-input v-model="form.description" type="textarea" maxlength="500" show-word-limit clearable autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import mixin from "./mixin";
import { addmodelRelations, editmodelRelations } from "@/views/pages/apis/model";
export default {
  mixins: [mixin],
  props: {
    isAdd: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
    dataBox: {
      type: Array,
      default: [],
    },
    modelDetail: {
      type: Object,
      default: {},
    },
  },
  emits: ["confirm"],
  data() {
    return {
      form: {
        sourceModel: "",
        constraint: "",
        targetModel: "",
        type: "",
        description: "",
        ident: "",
      },
      typeList: [
        {ident: 'cc_belong',name:'属于'},
        {ident: 'cc_contain',name:'包含'},
      ],
      constraintList: [
        {ident: 'O2O',name:'一对一'},
        {ident: 'O2N',name:'一对多'},
        {ident: 'N2O',name:'多对一'},
        {ident: 'N2N',name:'多对多'},
      ],
      formLabelWidth: "120px",
      dialogVisible: false,
      rules: {
        sourceModel: [{ required: true, message: "请选择", trigger: "change" }],
        targetModel: [{ required: true, message: "请选择", trigger: "change" }],
        type: [{ required: true, message: "请选择", trigger: "change" }],
        constraint: [{ required: true, message: "请选择", trigger: "change" }],
      },
      title: "",
    };
  },
  watch: {
    isAdd(val) {
      this.title = val == "add" ? "新建关联" : "修改关联";
    },
  },
  // created() {
  // },

  methods: {
    cancel() {
      this.dialogVisible = false;
      // this.$emit("confirm", false);
      this.$refs["serviceFormRef"].resetFields();
      this.$refs["serviceFormRef"].clearValidate();
    },
    submit() {
      this.$refs["serviceFormRef"].validate((valid) => {
        if (valid) {
          // alert("submit!");
          if (this.isAdd === "add") {
            delete this.form.ident;
            addmodelRelations({ ...this.form })
              .then((res) => {
                // console.log(res);
                if (res.success) {
                  this.$message.success("操作成功");
                  this.$refs["serviceFormRef"].resetFields();
                  this.dialogVisible = false;
                  this.$emit("confirm", true);
                } else this.$message.error(JSON.parse(res.data)?.message);
              })
              .catch((err) => {
                this.$message.error(err?.message);
              });
          } else {
            editmodelRelations({ ...this.form })
              .then((res) => {
                if (res.success) {
                  this.$message.success("操作成功");
                  this.$refs["serviceFormRef"].resetFields();
                  this.dialogVisible = false;
                  this.$emit("confirm", true);
                } else this.$message.error(JSON.parse(res.data)?.message);
              })
              .catch((err) => {
                this.$message.error(err?.message);
              });
          }
        }
      });
    },
  },
  expose: ["dialogVisible", "title", "form"],
};
</script>
<style scoped lang="scss"></style>
