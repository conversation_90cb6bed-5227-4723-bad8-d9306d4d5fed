<!--  -->
<template>
  <div>
    <el-dialog :title="title" v-model="dialogVisible" :before-close="cancel" width="45%">
      <el-form :model="form" label-position="left" :rules="rules" ref="serviceForm">
        <el-form-item label="规则类型" :label-width="formLabelWidth" prop="radio">
          <el-radio-group v-model="form.radio" @change="changeRadio">
            <el-radio label="separate">单独唯一</el-radio>
            <el-radio label="joint">联合唯一</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="校验规则" v-if="form.radio=='separate'" :label-width="formLabelWidth" prop="fields">
          <el-select v-model="form.fields" clearable style="width: 100%" filterable placeholder="请选择">
            <el-option v-for="item in options" :key="item.ident" :label="item.name" :value="item.ident"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="校验规则" v-else :label-width="formLabelWidth" prop="fields">
          <el-select v-model="form.fields" multiple clearable style="width: 100%" filterable placeholder="请选择">
            <el-option v-for="item in options" :key="item.ident" :label="item.name" :value="item.ident"> </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { editModelManageOther } from "@/views/pages/apis/model";

export default {
  props: {
    isAdd: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
    modelDetail: {
      type: Object,
      default: {},
    },
    slaDetail: {
      type: Object,
      default: {},
    },
  },
  emits: ["confirm"],
  data() {
    return {
      form: {
        radio: "separate",
        fields: '',
      },
      rules: {
        radio: [{ required: true, message: "请选择", trigger: "change" }],
        fields: [{ required: true, message: "请选择", trigger: "change" }],
      },
      formLabelWidth: "120px",
      dialogVisible: false,

      title: "",
      options: [],
      resourceId: "",
      disabledList: [],
    };
  },
  watch: {
    modelDetail(val) {
      this.options = val.fields
    },
    isAdd(val) {
      this.title = val == "add" ? "新建校验" : "修改校验";
      if (val === "edit") {
        if(this.slaDetail.fields.length>1) {
          this.form.radio = 'joint'
          this.form.fields = this.slaDetail.fields
        } else {
          this.form.radio = 'separate'
          this.form.fields = this.slaDetail.fields+''
        }
      } else {
        this.form = {
          radio: "separate",
          fields: '',
        }
      }
    },
  },

  // created() {
  // },
  mounted() {

  },

  methods: {
    changeRadio() {
      this.form.fields = ''
    },
    cancel() {
      this.dialogVisible = false;
      // this.$emit("confirm", false);
      this.$refs["serviceForm"].resetFields();
      this.$refs["serviceForm"].clearValidate();
    },
    submit() {
      this.$refs["serviceForm"].validate((valid) => {
        if (valid) {
          if(typeof this.form.fields =='string') {
            this.form.fields = [this.form.fields]
          }
          let arr = this.modelDetail.uniques
          if (this.isAdd === "add") {
            arr.push(this.form)
            let params = {
              uniques: arr,
              id: this.id,
            }
            editModelManageOther({ ...params })
              .then((res) => {
                // console.log(res);
                if (res.success) {
                  this.$message.success("操作成功");
                  this.$refs["serviceForm"].resetFields();
                  this.dialogVisible = false;
                  this.$emit("confirm", true);
                } else {
                  if(this.form.radio == 'joint') {
                    this.form.fields = this.slaDetail.fields
                  } else {
                    this.form.fields = this.slaDetail.fields+''
                  }
                  this.$message.error(JSON.parse(res.data)?.message);
                }
              })
              .catch((err) => {
                if(this.form.radio == 'joint') {
                  this.form.fields = this.slaDetail.fields
                } else {
                  this.form.fields = this.slaDetail.fields+''
                }
                this.$message.error(err?.message);
              });
          } else {
            arr.forEach(element => {
              if(element.fields == this.slaDetail.fields) {
                element.fields = this.form.fields
              }
            });
            let params = {
              uniques: arr,
              id: this.id,
            }
            editModelManageOther({ ...params })
              .then((res) => {
                if (res.success) {
                  this.$message.success("操作成功");
                  this.$refs["serviceForm"].resetFields();
                  this.dialogVisible = false;
                  this.$emit("confirm", true);
                }  else {
                  if(this.form.radio == 'joint') {
                    this.form.fields = this.slaDetail.fields
                  } else {
                    this.form.fields = this.slaDetail.fields+''
                  }
                  this.$message.error(JSON.parse(res.data)?.message);
                }
              })
              .catch((err) => {
                if(this.form.radio == 'joint') {
                  this.form.fields = this.slaDetail.fields
                } else {
                  this.form.fields = this.slaDetail.fields+''
                }
                this.$message.error(err?.message);
              });
          }
        }
      });
    },
  },
  expose: ["dialogVisible", "fields", "resourceId", "disabledList", "title", "form"],
};
</script>
<style scoped lang="scss"></style>
