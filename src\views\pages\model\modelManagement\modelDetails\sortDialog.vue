<!--  -->
<template>
  <div>
    <el-dialog :title="title" v-model="dialogVisible" width="45%" :before-close="cancel">
      <el-form :model="form" :rules="rules" label-position="left" ref="serviceFormRef" style="text-align: center;">
        <el-transfer
          style="text-align: left; display: inline-block"
          v-model="value4"
          filterable
          :left-default-checked="leftCheck"
          :right-default-checked="rightCheck"
          :titles="['待选属性', '已选属性']"
          :button-texts="['', '']"
          :format="{
            noChecked: '${total}',
            hasChecked: '${checked}/${total}'
          }"
          @change="handleChange"
          :data="dataBox">
          <template #default="{ option }">
            {{ option.name }}
          </template>
        </el-transfer>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import mixin from "./mixin";
import { getGeneralFieldsList,getGeneralFields,editGeneralFields } from "@/views/pages/apis/model";
import { Console } from 'console';
export default {
  mixins: [mixin],
  props: {
    isAdd: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
    modelDetail: {
      type: Object,
      default: {},
    },
  },
  emits: ["confirm"],
  data() {
    return {
      dataBox: [],
      leftCheck: [],
      rightCheck: [],
      value4: [],
      fields: [],
      form: {
        ident: "",
        name: "",
        srcDes: "",
        destDes: "",
        direction: "SRC_TO_DEST",
        group: "",
        checked: false,
        id: "",
      },
      formLabelWidth: "120px",
      dialogVisible: false,
      rules: {
        ident: [{ required: true, message: "请输入", trigger: "blur" }],
        name: [{ required: true, message: "请输入", trigger: "blur" }],
        direction: [{ required: true, message: "请选择", trigger: "change" }],
        group: [{ required: true, message: "请选择", trigger: "change" }],
      },
      title: "",
    };
  },
  watch: {
    isAdd(val) {
      this.title = val == "add" ? "列表显示属性配置" : "修改模型";
      if (val === "edit") {

      } else {
        this.dataBox = []
        this.rightCheck = []
        this.fields = []
        this.value4 = []
        this.modelDetail.fields.forEach(element => {
          this.dataBox.push({key:element.ident, name:element.name, general:false})
        });
        this.getServiceDetail();
      }
    },
  },
  // created() {
  // },
  mounted() {
    // this.getServiceDetail();
  },
  methods: {
    handleChange(value, direction, movedKeys) {
      // console.log(value, direction, movedKeys);
      this.fields = []
      this.dataBox.forEach(val => {
        value.forEach(element => {
          if(element==val.key) {
            this.fields.push({ident:element,general:val.general})
          }
        });
      });
    },
    getServiceDetail() {
      getGeneralFieldsList({
        pageNumber: 10,
        pageSize: 1,
      }).then((res) => {
        if (res.success) {
          if(res.data.length>0) {
            res.data.forEach(element => {
              this.dataBox.push({key:element.ident,name:element.name,general:true})
              this.fields.push({ident:element.ident,general:true})
            });
          }
        }
      });
      getGeneralFields({
        pageNumber: 10,
        pageSize: 1,
        ident: this.id
      }).then((res) => {
        if (res.success) {
          if(res.data.fields.length>0) {
            res.data.fields.forEach(element => {
              this.value4.push(element.ident)
            });
            // this.value4 = this.rightCheck
          }
        }
      });
    },

    cancel() {
      this.dialogVisible = false;
      // this.$emit("confirm", false);
      this.$refs["serviceFormRef"].resetFields();
      this.$refs["serviceFormRef"].clearValidate();
    },
    submit() {
      this.$refs["serviceFormRef"].validate((valid) => {
        if (valid) {
          if (this.isAdd === "add") {
            editGeneralFields({
              ident: this.id,
              fields: this.fields
            })
              .then((res) => {
                // console.log(res);
                if (res.success) {
                  this.$message.success("操作成功");
                  this.$refs["serviceFormRef"].resetFields();
                  this.dialogVisible = false;
                  this.$emit("confirm", true);
                } else this.$message.error(JSON.parse(res.data)?.message);
              })
              .catch((err) => {
                this.$message.error(err?.message);
              });
          }
        }
      });
    },
  },
  expose: ["dialogVisible", "title", "form"],
};
</script>
<style scoped lang="scss">
.el-divider--vertical{
    width: 4px;
    height: 14px;
    background: #c3cdd7;
    margin-left: 0;
  }
</style>
