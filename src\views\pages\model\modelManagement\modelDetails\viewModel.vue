<!--  -->
<template>
  <div>
    <el-dialog :title="title" v-model="dialogVisible" width="45%" :before-close="cancel">
      <el-form :model="form" :rules="rules" label-position="left" ref="serviceFormRef" style="max-height: calc(70vh - 116px);overflow: auto;">
        <el-collapse v-model="activeNames" @change="handleChange">
          <el-collapse-item :name="item.ident" v-for="(item) in modelDetail.fieldGroups" :key="item.ident">
            <template v-slot:title>
              <el-divider direction="vertical"></el-divider>
              {{ item.name }}
            </template>
            <div v-for="(val) in item.fields" :key="val.ident">
              <div style="text-align: left;margin-top: 15px;">{{ val.name }}
                <span v-if="val.type=='DIGITAL'">
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    v-if="val.helpNote"
                    :content="val.helpNote"
                    placement="top-start"
                  >
                  <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </span>
                <span v-if="val.type=='FLOAT'">
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    v-if="val.helpNote"
                    :content="val.helpNote"
                    placement="top-start"
                  >
                  <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </span>
              </div>
              <div style="margin-top: 10px;">
                <el-input
                  v-if="val.type=='SHORT_CHARACTER'"
                  style="width: 100%;"
                  placeholder=""
                  v-model="val.value"
                >
                </el-input>
                <el-input
                  v-if="val.type=='LONG_CHARACTER'"
                  type="textarea"
                  style="width: 100%;"
                  :rows="2"
                  placeholder="请输入内容"
                  v-model="val.value">
                </el-input>
                <div v-if="val.type=='DIGITAL'">
                  <el-input-number :min="min" :max="max" v-model="val.value" label="描述文字"></el-input-number>
                  <span style="margin-left: 10px;">{{val.unit}}</span>
                </div>
                <div v-if="val.type=='FLOAT'">
                  <el-input-number :min="minF" :max="maxF" v-model="val.value" :step="0.1" :precision="2" label="描述文字"></el-input-number>
                  <span style="margin-left: 10px;">{{val.unit}}</span>
                </div>
                <el-select v-if="val.type=='ENUM'" style="width: 100%;" v-model="val.value" placeholder="请选择">
                  <el-option
                    v-for="item in meijuList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                  </el-option>
                </el-select>
                <el-date-picker
                  v-if="val.type=='DATE_TIME'"
                  v-model="val.value"
                  style="width: 100%;"
                  type="date"
                  placeholder="选择日期">
                </el-date-picker>
                <el-select v-if="val.type=='TIME_ZONE'" style="width: 100%;" v-model="val.value" placeholder="请选择">
                  <el-option
                    v-for="item in timeZoneList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                  </el-option>
                </el-select>
                <el-select v-if="val.type=='USER'" style="width: 100%;" v-model="val.value" multiple placeholder="请选择">
                  <el-option
                    v-for="item in options2"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                  </el-option>
                </el-select>
                <el-switch
                  v-if="val.type=='BOOL'"
                  v-model="val.value"
                  active-color="#13ce66"
                  inactive-color="#ff4949">
                </el-switch>
                <el-select v-if="val.type=='LIST'" style="width: 100%;" v-model="val.value" placeholder="请选择">
                  <el-option
                    v-for="item in tableList"
                    :key="item"
                    :label="item"
                    :value="item">
                  </el-option>
                </el-select>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">返 回</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import mixin from "./mixin";
import { Zone } from "@/utils/zone";
import { getUserList } from "@/views/pages/apis/model";
import { QuestionFilled } from "@element-plus/icons-vue";
export default {
  mixins: [mixin],
  props: {
    isAdd: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
    modelDetail: {
      type: Object,
      default: {},
    },
  },
  emits: ["confirm"],
  components: {
    QuestionFilled
  },
  data() {
    return {
      form: {
        ident: "",
        name: "",
        srcDes: "",
        destDes: "",
        direction: "SRC_TO_DEST",
        group: "",
        checked: false,
        id: "",
      },
      modelList: [],
      activeNames: [],
      formLabelWidth: "120px",
      dialogVisible: false,
      rules: {
        ident: [{ required: true, message: "请输入", trigger: "blur" }],
        name: [{ required: true, message: "请输入", trigger: "blur" }],
        direction: [{ required: true, message: "请选择", trigger: "change" }],
        group: [{ required: true, message: "请选择", trigger: "change" }],
      },
      value: "",
      numMax: "",
      meijuList: [],
      timeZoneList: [],
      options2: [],
      tableList: [],
      min: '',
      minF: '',
      max: '',
      maxF: '',
      title: "",
    };
  },
  watch: {
    isAdd(val) {
      this.title = val == "add" ? "预览模型" : "修改模型";
      if (val === "edit") {
        
      } else {
        getUserList({
          pageNumber: 1,
          pageSize: 1000,
        }).then((res) => {
          if (res.success) {
            this.options2 = res.data
          }
        });
        this.meijuList = []
        this.tableList = []
        this.timeZoneList = []
        for (let key in Zone) {
          this.timeZoneList.push({id:key,name:Zone[key]})
        }
        this.modelDetail.fieldGroups.forEach(item => {
          this.activeNames.push(item.ident)
          item.fields = []
          this.modelDetail.fields.forEach(element => {
            if(item.ident === element.groupIdent) {
              item.fields.push(element)
            }
            switch (element.type) {
              case 'SHORT_CHARACTER':
                element.value = ''
                break;
              case 'LONG_CHARACTER':
                element.value = ''
                break;
              case 'DIGITAL':
                this.max = JSON.parse(element.option).max
                this.min = JSON.parse(element.option).min
                break;
              case 'FLOAT':
                this.maxF = JSON.parse(element.option).max
                this.minF = JSON.parse(element.option).min
                break;
              case 'ENUM':
                this.meijuList = JSON.parse(element.option).nodes
                break;
              case 'DATE_TIME':
                element.value = element.option
                break;
              case 'TIME_ZONE':
                element.value = element.option
                break;
              case 'USER':
                element.value = element.option
                break;
              case 'BOOL':
                element.value = JSON.parse(element.option)
                break;
              case 'LIST':
                this.tableList = JSON.parse(element.option).values
                break;
              default:
                break;
            }
          })
        })
      }
    },
  },
  // created() {
  // },
  mounted() {
    
  },
  methods: {

    cancel() {
      this.dialogVisible = false;
      // this.$emit("confirm", false);
      this.$refs["serviceFormRef"].resetFields();
      this.$refs["serviceFormRef"].clearValidate();
    },
  },
  expose: ["dialogVisible", "title", "form"],
};
</script>
<style scoped lang="scss">
.el-divider--vertical{
    width: 4px;
    height: 14px;
    background: #c3cdd7;
    margin-left: 0;
  }
</style>
