<template>
  <el-card :body-style="{ padding: '20px', height: `${props.height}px`, width: `${width}px`, overflow: 'auto' }">
    <div class="box" v-for="val in dataBox" :key="val.ident">
      <div @mouseenter="enter(val.ident)" @mouseleave="out" style="width: 100%; height: 30px; line-height: 30px; border-bottom: 1px solid rgba(195, 205, 215, 0.26666666666666666)">
        <el-divider direction="vertical"></el-divider>
        <span>{{ val.name }}</span>
        <b>({{ val.models.length }})</b>
        <div style="float: right; margin-top: -2px; visibility: hidden" :class="{ showBtn: val.ident === isShowBtn }">
          <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.create)">
            <el-link :underline="false" type="primary" icon="el-icon-plus" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.create)" @click="modelCreateClick('addTwo', val)">添加模型</el-link>
          </el-tooltip>
          <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.auth542939547625848832)">
            <el-link :underline="false" type="primary" icon="el-icon-edit" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.auth542939547625848832)" @click="groupCreateClick('edit', val)">编辑分组</el-link>
          </el-tooltip>
          <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group533811416109416448.auth542939584871268352)">
            <el-link :underline="false" type="danger" icon="el-icon-delete" :disabled="!userInfo.hasPermission(PERMISSION.group533811416109416448.auth542939584871268352)" @click="groupDelete(val)">删除分组</el-link>
          </el-tooltip>
        </div>
      </div>
      <div style="width: 100%; padding: 16px; display: flex; flex-wrap: wrap; justify-content: flex-start; align-content: space-between">
        <div v-for="item in val.models" :key="item.ident" @mouseenter="enterBox(item.ident)" @mouseleave="outBox" style="width: 19%; height: 68px; border: 1px solid #dde4eb; display: flex; cursor: pointer; border-radius: 4px; margin: 0 1% 10px 0">
          <div @mouseenter="enterBoxOne(item.ident)" @click="toDetail(item)" :class="{ active: item.ident === isActive }" style="width: 66%; height: 100%; display: flex">
            <div style="width: 66px; height: 100%">
              <!-- <el-icon color="#3a84ff" :size="20"><Monitor /></el-icon> -->
              <div style="width: 50%; height: 50%; margin: auto; margin-top: 17px">
                <img style="width: 100%; height: 100%" src="@/views/pages/assets/device/desktop-normal.png" alt="" />
              </div>
            </div>
            <div style="width: calc(100% - 66px); height: 100%">
              <p style="margin-top: 13px; text-overflow: ellipsis; white-space: nowrap; overflow: hidden">{{ item.name }}</p>
              <p style="color: #bfc7d2; text-overflow: ellipsis; white-space: nowrap; overflow: hidden">{{ item.ident }}</p>
            </div>
          </div>
          <div @mouseenter="enterBoxTwo(item.ident)" @click="toResource(item)" :class="{ activeTwo: item.ident === isActiveTwo, activeTwoShow: item.ident === isActiveShow }" style="width: 34%; height: 100%; visibility: hidden">
            <div style="width: 20px; height: 20px; margin: auto; margin-top: 17px">
              <!-- <el-icon color="#3a84ff" :size="20">
                <TopRight />
              </el-icon> -->
              <img style="width: 100%; height: 100%" src="@/views/pages/assets/device/share-normal.png" alt="" />
            </div>
            <div style="width: 100%; text-align: center">（{{ dataNumber }}）</div>
          </div>
        </div>
      </div>
    </div>
  </el-card>
  <modelCreate :dialog="dialog" ref="supplier" @dialogClose="dialogClose"></modelCreate>
  <groupCreate :dialog="dialogGroup" ref="supplierGroup" @dialogClose="dialogCloseGroup"></groupCreate>
</template>

<script setup lang="ts" generic="T extends object">
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

import { useSiteConfig } from "@/stores/siteConfig";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMessageBox, type TableColumnCtx, ElText } from "element-plus";
import pageTemplate from "@/components/pageTemplate.vue";
import modelCreate from "./modelCreate.vue";
import groupCreate from "./groupCreate.vue";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";

import { getModelManageList, delGroup, modelResources } from "@/views/pages/apis/model";
import { column } from "element-plus/es/components/table-v2/src/common";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "SlaDownConfig" });
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */

const siteConfig = useSiteConfig();
const userInfo = getUserInfo();

interface Props {
  height: number;
  width: number;
  dataBox: Array;
}
const props = withDefaults(defineProps<Props>(), {
  height: 0,
  width: 0,
  dataBox: {
    default: "",
  },
});

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);
const dialog = ref(false);
const dialogGroup = ref(false);

const emit = defineEmits(["delGroup"]);

const tableLoading = ref(false);
const isShowBtn = ref(false);
const shareBox = ref(false);
const isActive = ref(false);
const isActiveTwo = ref(false);
const isActiveShow = ref(false);
const dataNumber = ref("");

// 搜索关键字
const searchForm = ref<Record<string, string>>({ name: "" });

const tableData = ref<DataItem[]>([]);
const paging = reactive({
  pageNumber: 1,
  pageSize: 10,
  total: 0,
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  getSearchData();
}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

function toDetail(row) {
  router.push({
    name: "535255871177887744",
    params: {
      id: row.ident,
    },
    query: { fallback: route.name as string, tenant: row.tenantId },
    // query: { fallback: route.name as string,}
  });
}

function toResource(row) {
  router.push({
    name: "537885027082440704",
    query: { fallback: route.name as string, id: row.ident, name: row.name, tenant: row.tenantId },
    // query: { fallback: route.name as string,tenant:row.tenantId }
  });
}
function enter(key) {
  isShowBtn.value = key;
}
function out() {
  isShowBtn.value = false;
}
function enterBox(index) {
  isActiveShow.value = index;
}
function outBox() {
  isActiveShow.value = false;
  isActive.value = false;
  isActiveTwo.value = false;
}
function enterBoxOne(index) {
  isActive.value = index;
  isActiveTwo.value = false;
  resources(index);
}
function resources(index) {
  let params = {
    ident: index,
  };
  modelResources(params)
    .then(({ success, message, data, total }) => {
      if (!success) throw new Error(message);
      dataNumber.value = data;
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {
      loading.value = false;
    });
}
function enterBoxTwo(index) {
  isActiveTwo.value = index;
  isActive.value = false;
  resources(index);
}
async function modelCreateClick(type, row) {
  ctx.refs.supplier.form.report = false;
  setTimeout(() => {
    ctx.refs.supplier.open(type, row);
  }, 200);
  dialog.value = true;
}
function dialogClose(bool) {
  dialog.value = bool;
  getRefreshTable();
  ctx.refs.supplier.form.report = false;
}
async function groupCreateClick(type, row) {
  ctx.refs.supplierGroup.form.report = false;
  setTimeout(() => {
    ctx.refs.supplierGroup.open(type, row);
  }, 200);
  dialogGroup.value = true;
}
function dialogCloseGroup(bool) {
  dialogGroup.value = bool;
  getRefreshTable();
  ctx.refs.supplierGroup.form.report = false;
}
//删除分组
function groupDelete(row: Partial<Record<string, any>>) {
  ElMessageBox.confirm("确定删除此分组吗?", "删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // // tableData.value.length = tableData.value.length - 1;
      let params = {
        ident: row.ident,
      };
      delGroup(params)
        .then(({ success, data, message }) => {
          if (!success) throw new Error(message);
          ElMessage.success("操作成功");

          if (tableData.value.slice((paging.pageNumber - 1) * paging.pageSize, paging.pageNumber * paging.pageSize).length == 0) {
            paging.pageNumber = 1;
          }
          getRefreshTable();
        })
        .catch((e) => {
          ElMessage.error(e.message);
          tableLoading.value = false;
        });
    })
    .catch(() => {
      // ElMessage({
      //   type: "info",
      //   message: "已取消删除",
      // });
    });
}
//搜索
function getSearchData() {
  paging.pageNumber = 1;

  // getRefreshTable();
}
function getRefreshTable() {
  emit("delGroup");
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.el-divider--vertical {
  width: 4px;
  height: 14px;
  background: #c3cdd7;
  margin-left: 0;
}
.box {
  span {
    font-size: 16px;
    font-weight: 700;
    color: #303133;
  }
  b {
    margin-left: 5px;
    font-size: 16px;
    color: #c3cdd7;
  }
}
.showBtn {
  visibility: visible !important;
}
.active {
  background: #f0f5ff;
}
.activeTwo {
  background: #f0f5ff;
}
.activeTwoShow {
  visibility: visible !important;
}
</style>
