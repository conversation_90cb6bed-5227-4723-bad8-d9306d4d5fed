<template>
  <el-card>
    <pageTemplate v-model:current-page="state.page" v-model:page-size="state.size" :total="state.total" :height="height - 40" :show-paging="true" @size-change="handleStateRefresh()" @current-change="handleStateRefresh()">
      <template #left></template>
      <template #center></template>
      <template #right>
        <el-button type="primary" :icon="Plus" @click="handleStateCreate({ ...publicParams })">{{ t("glob.add") }}</el-button>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table v-loading="state.loading" :data="state.data" :height="tableHeight" :style="{ width: `${width - 40}px`, margin: '0 auto' }">
          <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :min-width="column.width || 120" :="column.showOverflowTooltip" :formatter="column.formatter" />
          <el-table-column :label="t('glob.operate')" align="left" header-align="left" :width="110" fixed="right">
            <template #header="{ column }">
              <div class="tw-flex tw-justify-center">
                <span>{{ column.label }}</span>
                <el-link type="primary" :underline="false" :icon="Refresh" @click="handleStateRefresh()"></el-link>
              </div>
            </template>
            <template #default="{ row }">
              <el-button link type="primary" :icon="Edit" :title="t('glob.edit')" @click="handleStateEditor(row)"></el-button>
              <el-button link type="danger" :icon="Delete" :title="t('glob.delete')" @click="handleStateDelete(row)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </pageTemplate>
    <EditorForm ref="editorRef" title="联系人"></EditorForm>
  </el-card>
</template>

<script setup lang="ts" name="contacts">
import { inject, ref, reactive, nextTick, computed, watch, h } from "vue";
import type { Ref } from "vue";
import { useI18n } from "vue-i18n";
import { Plus, Refresh, Edit, Delete } from "@element-plus/icons-vue";
import { sizes } from "@/utils/common";
import { ElMessage, ElTag } from "element-plus";
import zone from "@/utils/zone.json";
// -------api-------
import { getContacts as getItem, addContacts as addData, modContacts as modItem, delContacts as delItem } from "@/api/personnel";
import type { ContactsItem as ItemData } from "@/api/personnel";

// -------components-------
import pageTemplate from "@/components/pageTemplate.vue";
import { EditorType } from "@/views/common/interface";
import EditorForm from "./EditForm.vue";

const editorRef = ref<InstanceType<typeof EditorForm>>();
const width = inject<Ref<number>>("width", ref(100));
const height = inject<Ref<number>>("height", ref(100));
const publicParams = computed<Record<string, unknown>>(() => ({}));
const { t } = useI18n();

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode; showOverflowTooltip: boolean }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}

const state = reactive<StateData<ItemData>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {
    keyword: "",
    schemas: ["ROLE"].join(","),
  },
  column: [
    {
      key: "name",
      label: "姓名",
      showOverflowTooltip: true,
      formatter: (row, col, value) => {
        return (value as string) || "--";
      },
    },
    {
      key: "email",
      label: "联系邮箱",
      showOverflowTooltip: true,
      formatter: (row, col, value) => {
        return (value as string) || "--";
      },
    },
    {
      key: "landlinePhone",
      label: "固定电话",
      showOverflowTooltip: true,
      formatter: (row, col, value) => {
        return (value as string) || "--";
      },
    },
    {
      key: "mobilePhone",
      label: "移动电话",
      showOverflowTooltip: true,
      formatter: (row, col, value) => {
        return (value as string) || "--";
      },
    },
    {
      key: "smsPhone",
      label: "短信号码	",
      showOverflowTooltip: true,
      formatter: (row, col, value) => {
        return (value as string) || "--";
      },
    },
    {
      key: "smsEnabled",
      label: "已启用短信",
      showOverflowTooltip: true,
      formatter: (row, col, value) => {
        const enable = h(ElTag, { type: "success" }, () => t("module.yes"));
        const disable = h(ElTag, { type: "danger" }, () => t("module.no"));
        return value ? enable : disable || "--";
      },
    },
    {
      key: "vip",
      label: "VIP",
      showOverflowTooltip: true,
      formatter: (row, col, value) => {
        const enable = h(ElTag, { type: "success" }, () => t("module.yes"));
        const disable = h(ElTag, { type: "danger" }, () => t("module.no"));
        return value ? enable : disable || "--";
      },
    },
    {
      key: "note",
      label: "备注",
      showOverflowTooltip: true,
      formatter: (row, col, value) => {
        return (value as string) || "--";
      },
    },
    {
      key: "zoneId",
      label: "时区",
      showOverflowTooltip: true,
      formatter: (row, col, value) => {
        return zone.find((v: Record<string, string>) => value === v.zoneId)?.displayName || "--";
      },
    },
    {
      key: "externalId",
      label: "外部ID",
      showOverflowTooltip: true,
      formatter: (row, col, value) => {
        return (value as string) || "--";
      },
    },
  ],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

async function handleStateEditor(params: Partial<ItemData> & Record<string, unknown>) {
  await editorItem(params);
  await handleStateRefresh();
}
async function handleStateDelete(params: Partial<ItemData> & Record<string, unknown>) {
  await deleteItem(params);
  await handleStateRefresh();
}

async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  let controller = new AbortController();
  const response = getItem({ pageNumber: state.page, pageSize: state.size });
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await response;
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);
      return data instanceof Array ? data : [];
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(20);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}

async function createItem(params: Partial<ItemData> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...publicParams.value, ...params, "#TYPE": EditorType.Add }, async (req) => {
      try {
        const { success, message, data } = await addData({ ...req });
        if (success) {
          ElMessage.success(`添加成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}

async function editorItem(params: Partial<ItemData> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...publicParams.value, ...params, "#TYPE": EditorType.Mod }, async (req) => {
      try {
        const { success, message, data } = await modItem({ ...req });
        if (success) {
          ElMessage.success(`编辑成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}

async function deleteItem(params: Partial<ItemData> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...publicParams.value, ...params, "#TYPE": EditorType.Del }, async (req) => {
      try {
        const { success, message, data } = await delItem({ ...req });
        if (success) {
          ElMessage.success(`删除成功！`);
          return true;
        } else throw Object.assign(new Error(message), { success, data });
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return false;
      }
    });
  } catch (error) {
    /*  */
  }
}

async function handleStateCreate(params: Partial<ItemData> & Record<string, unknown>) {
  await createItem(params);
  await handleStateRefresh();
}

watch<typeof publicParams, true>(
  publicParams,
  async function () {
    if (state.loading) return;
    handleStateRefresh();
  },
  { immediate: true, flush: "post" }
);

async function handleStateRefresh() {
  if (state.loading) return;
  state.loading = true;
  state.data.splice(0, state.data.length, ...(await querysItem({})));
  state.loading = false;
}
handleStateRefresh();
</script>
