<!-- eslint-disable vue/no-unused-vars -->
<template>
  <!-- 对话框表单 -->
  <el-dialog v-model="visible" title="排班预览" :close-on-click-modal="false" append-to-body draggable :width="`${1500}px`" :before-close="handleClose">
    <template #header>
      <div class="title">
        <slot name="header" :width="width" :height="height"></slot>
      </div>
    </template>
    <template #default>
      <el-scrollbar ref="contentRef" :height="height || undefined" :style="{ maxHeight: `${$height / 1.5}px` }">
        <el-calendar v-model="dateValue" ref="calendar">
          <template #header="{ date }">
            <!-- <span>Custom header content</span> -->
            <span>{{ date }}</span>
            <el-button-group>
              <el-button size="small" @click="selectDate('prev-year')">上一年 </el-button>
              <el-button size="small" @click="selectDate('prev-month')"> 上个月 </el-button>
              <el-button size="small" @click="selectDate('today')">今天</el-button>
              <el-button size="small" @click="selectDate('next-month')"> 下个月 </el-button>
              <el-button size="small" @click="selectDate('next-year')"> 下一年 </el-button>
            </el-button-group>
          </template>
          <template #date-cell="{ data }">
            <!-- {{ data }} -->
            <div class="calendar-div">
              <div :class="data.isSelected ? 'is-selected' : ''">
                {{ data.day.split("-")[2] }}
                <div v-for="(value, name) in props.dateList" class="on" :key="name">
                  <!-- {{ name }} -->
                  <template v-if="data.day == name">
                    <p v-for="item in value" :key="item">
                      {{ item.startTime }}-
                      {{ item.endTime }}
                      <span v-for="v in item.staffNames" :ke="v">
                        {{ v }}
                      </span>
                    </p>
                  </template>

                  <!-- {{ data.day === item ? "Δ" : "" }} -->
                </div>
              </div>
            </div>
          </template>
        </el-calendar>
      </el-scrollbar>
    </template>
    <template #footer>
      <div>
        <slot name="footer" :width="width" :height="height"></slot>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="EditorForm">
import moment from "moment";
import { ElScrollbar, CalendarDateType, CalendarInstance } from "element-plus";
import { useModel, ref, inject, watch, nextTick } from "vue";
const emits = defineEmits(["handleCancel"]);
interface Props {
  visible: boolean;
  dateList: Array<[]>;
}
const props = withDefaults(defineProps<Props>(), { visible: false, dateList: [] });

const visible = useModel(props, "visible");

const contentRef = ref<InstanceType<typeof ElScrollbar>>();

const dateValue = ref(new Date());

const $width = inject<import("vue").Ref<number>>("width", ref(document.body.clientWidth - 200));
const $height = inject<import("vue").Ref<number>>("height", ref(document.body.clientHeight - 260 - document.body.clientHeight * 0.15));
const width = ref($width.value / 1.75);
const height = ref(0);

// const dateList = ref<string[]>([] as string[]);

watch(visible, async (visible) => {
  await nextTick();
  height.value = 0;
  const w = $width.value / 1.75;
  if (visible) {
    await nextTick();
    const h = contentRef.value ? contentRef.value.$el.clientHeight : $height.value / 2.5;
    if (h) height.value = contentRef.value ? contentRef.value.$el.clientHeight : $height.value / 2.5;
    width.value = w;
    height.value = h < document.body.clientHeight - 260 - document.body.clientHeight * 0.15 ? h : document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  }
});

const handleClose = (done: () => void) => {
  emits("handleCancel", false);
};
const calendar = ref<CalendarInstance>();
const selectDate = (val: CalendarDateType) => {
  if (!calendar.value) return;
  calendar.value.selectDate(val);
};
interface Slots {
  header(size: { width: number; height: number }): any;
  default(size: { width: number; height: number }): any;
  footer(size: { width: number; height: number }): any;
}

setcalendar();
function setcalendar() {
  // // console.log(props.dateList);
  // const date = moment().format("YYYY-MM-DD"); //当前时间
  // const newDate = moment()
  //   .add(Number(props.dateNum) - 1, "days")
  //   .format("YYYY-MM-DD");
  // dateList.value = enumerateDaysBetweenDates(date, newDate);
  // // console.log(dateList.value);
  // // console.log(enumerateDaysBetweenDates('2021-06-09', '2022-06-12');
}

function enumerateDaysBetweenDates(startDate: any, endDate: any) {
  // 假定你已经保证了startDate 小于endDate，且二者不相等
  let daysList = [];
  let SDate = moment(startDate);
  let EDate = moment(endDate);
  daysList.push(SDate.format("YYYY-MM-DD"));
  while (SDate.add(1, "day").isBefore(EDate)) {
    // 注意这里add方法处理后SDate对象已经改变。
    daysList.push(SDate.format("YYYY-MM-DD"));
  }
  daysList.push(EDate.format("YYYY-MM-DD"));
  return Array.from(new Set(daysList));
  // // console.log();
}

defineSlots<Slots>();
</script>

<style scoped lang="scss"></style>
