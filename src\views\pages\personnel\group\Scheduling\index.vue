<template>
  <div :style="{ height: 'calc(100% - 28px)' }" class="device-detail">
    <el-page-header @back="backRouter" :content="'排班详情'"></el-page-header>
    <div class="details" :style="{ height: 'calc(100% - 28px)' }">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <p>
              排班管理

              <span
                ><el-icon><WarningFilled /></el-icon> 若修改排班方式，则先前修改的单日值班内容不予保存，且根据值班模板批量覆盖。
              </span>
            </p>
          </div>
        </template>
        <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="120px" class="demo-ruleForm" :size="formSize" status-icon>
          <el-form-item label="排班名称：">
            <el-input v-model="ruleForm.name" clearable placeholder="请输入排班名称" style="width: 300px" />
          </el-form-item>
          <el-form-item label="排班方式：">
            <el-radio-group v-model="ruleForm.radio1" class="ml-4">
              <el-radio label="QUICK" size="large">快速排班</el-radio>
              <el-radio label="FINE" size="large">精细排班</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="循环周期：" prop="day" v-if="ruleForm.radio1 == 'FINE'">
            <el-input v-model="ruleForm.day" placeholder="请输入需要循环的天数" style="width: 300px" clearable />
            <!-- <el-input v-model="ruleForm.day" clearable placeholder="请输入需要循环的天数" style="width: 300px" /> -->
            <span style="padding-left: 10px">日</span>
          </el-form-item>
          <el-form-item label="值班方案：" prop="day" v-if="ruleForm.radio1 == 'QUICK'">
            <el-select v-model="ruleForm.day" class="m-2" placeholder="Select">
              <el-option v-for="item in dutyTime" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <!-- <el-input v-model="ruleForm.num" clearable placeholder="请输入值班时间" style="width: 300px" /> -->

            <span style="padding-left: 10px">每人</span>
          </el-form-item>

          <div class="people-duty" v-if="ruleForm.radio1 == 'QUICK'">
            <h3>人员选择</h3>
            <p>值班人支持每轮选择一人值班，根据轮班次序轮番排班</p>
            <el-row :gutter="24">
              <el-col :span="4">
                <div class="operator">
                  <div class="operator-head">
                    <el-checkbox v-model="checked" size="large" @change="allCheck"> {{ checkUserList.length + "/" + userGroup.length }}条 </el-checkbox>
                  </div>
                  <div class="operator-main">
                    <el-input></el-input>
                    <el-tree :data="userGroup" :props="defaultProps" show-checkbox @check="treeCheck" ref="groupUserRef" />
                  </div>
                </div>
              </el-col>
              <el-col :span="2">
                <div class="operator-button">
                  <el-button type="primary" style="width: 110px" :disabled="checkUserList.length < 1" @click="shift('odd')"
                    >轮班
                    <el-icon class="el-icon--right"><ArrowRight /></el-icon>
                  </el-button>
                  <el-button type="primary" style="width: 110px" :disabled="checkUserList.length < 1" @click="shift('more')"
                    >共同值班
                    <el-icon class="el-icon--right"><ArrowRight /></el-icon>
                  </el-button>
                </div>
              </el-col>
              <el-col :span="4">
                <div class="operator">
                  <div class="operator-head">轮班列表</div>
                  <div class="operator-main">
                    <!-- <el-input></el-input> -->
                    <el-tree :data="useCheckUserList" :props="defaultProps">
                      <template #default="{ node, data }">
                        <span class="custom-tree-node">
                          <span>{{ node.label }}</span>
                          <span>
                            <el-icon class="el-icon--right" @click="remove(node, data)"><Delete /></el-icon>
                          </span>
                        </span>
                      </template>
                    </el-tree>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
          <!---->
          <div class="people-duty" v-if="ruleForm.radio1 == 'FINE'">
            <h3>自定义班次</h3>
            <p>请保证编辑的每日排班时效连续且达到24h，结束时间不包含最后一分钟，信息全部配置完成后会自动生成循环值班表。</p>

            <el-radio-group v-model="ruleForm.radio2" class="ml-4">
              <el-radio label="1" size="large">早晚班</el-radio>
              <el-radio label="2" size="large">早中晚班</el-radio>
              <el-radio label="3" size="large">自定义</el-radio>
            </el-radio-group>
            <div v-if="ruleForm.radio2 == 1">
              <div class="edit-form-wrap" v-for="(item, index) in morningList" :key="item.name">
                <el-input :value="item.name" disabled style="width: 300px; margin-right: 10px"></el-input>
                <el-time-picker
                  v-model="item.startTime"
                  placeholder="起始时间"
                  :value-format="'HH:mm'"
                  :format="'HH:mm'"
                  :picker-options="{
                    selectableRange: '08:00:00 - 23:59:00',
                    format: 'HH:mm',
                  }"
                  @change="changeTime(item.startTime, index, 'start', morningList)"
                  style="margin-right: 5px"
                >
                </el-time-picker>
                <span>-</span>
                <el-time-picker
                  v-model="item.endTime"
                  placeholder="结束时间"
                  :value-format="'HH:mm'"
                  :format="'HH:mm'"
                  :picker-options="{
                    selectableRange: '08:00:00 - 23:59:00',
                    format: 'HH:mm',
                  }"
                  @change="changeTime(item.endTime, index, 'end', morningList)"
                  style="margin: 0px 5px"
                >
                </el-time-picker>
                <el-select v-model="item.user" multiple>
                  <el-option v-for="v in userGroup" :key="v.id" :label="v.name" :value="v.id"></el-option>
                </el-select>
              </div>
              <p class="custom-error-message" v-if="showErrorMsg">编辑的排班时间重复、空缺或总和不等于24小时，请修改。</p>
            </div>
            <div v-if="ruleForm.radio2 == 2">
              <div class="edit-form-wrap" v-for="(item, index) in afterList" :key="item.name">
                <el-input :value="item.name" disabled style="width: 300px; margin-right: 10px"></el-input>
                <el-time-picker
                  v-model="item.startTime"
                  placeholder="起始时间"
                  :value-format="'HH:mm'"
                  :format="'HH:mm'"
                  :picker-options="{
                    selectableRange: '08:00:00 - 23:59:00',
                    format: 'HH:mm',
                  }"
                  @change="changeTime(item.startTime, index, 'start', afterList)"
                  style="margin-right: 5px"
                >
                </el-time-picker>
                <span>-</span>
                <el-time-picker
                  v-model="item.endTime"
                  placeholder="结束时间"
                  :value-format="'HH:mm'"
                  :format="'HH:mm'"
                  :picker-options="{
                    selectableRange: '08:00:00 - 23:59:00',
                    format: 'HH:mm',
                  }"
                  @change="changeTime(item.endTime, index, 'end', afterList)"
                  style="margin: 0px 5px"
                >
                </el-time-picker>
                <el-select v-model="item.user" multiple>
                  <el-option v-for="v in userGroup" :key="v.id" :label="v.name" :value="v.id"></el-option>
                </el-select>
              </div>
              <p class="custom-error-message" v-if="showErrorMsg">编辑的排班时间重复、空缺或总和不等于24小时，请修改。</p>
            </div>
            <div v-if="ruleForm.radio2 == 3">
              <div class="edit-form-wrap" v-for="(item, index) in customList" :key="item">
                <el-input v-model="item.name" clearable placeholder="请输入" style="width: 300px; margin-right: 10px"></el-input>
                <el-time-picker
                  v-model="item.startTime"
                  placeholder="起始时间"
                  :value-format="'HH:mm'"
                  :format="'HH:mm'"
                  :picker-options="{
                    selectableRange: '08:00:00 - 23:59:00',
                    format: 'HH:mm',
                  }"
                  @change="changeTime(item.startTime, index, 'start', customList)"
                  style="margin-right: 5px"
                >
                </el-time-picker>
                <span>-</span>
                <el-time-picker
                  v-model="item.endTime"
                  placeholder="结束时间"
                  :value-format="'HH:mm'"
                  :format="'HH:mm'"
                  :picker-options="{
                    selectableRange: '08:00:00 - 23:59:00',
                    format: 'HH:mm',
                  }"
                  @change="changeTime(item.endTime, index, 'end', customList)"
                  style="margin: 0px 5px"
                >
                </el-time-picker>
                <el-select v-model="item.user" multiple>
                  <el-option v-for="v in userGroup" :key="v.id" :label="v.name" :value="v.id"></el-option>
                </el-select>

                <el-icon class="customIcon" style="color: #db3328" v-if="customList.length > 1" @click="deleteCustomItem(index)"><Delete /></el-icon>

                <el-icon v-if="index == customList.length - 1" class="customIcon" style="color: rgb(32, 128, 255)" @click="addCustomItem"><CirclePlus /></el-icon>
              </div>
              <p class="custom-error-message" v-if="showErrorMsg">编辑的排班时间重复、空缺或总和不等于24小时，请修改。</p>
            </div>
          </div>
        </el-form>
        <div>
          <el-button @click="visible = true">提交</el-button>
          <el-button @click="watchScheduling(ruleFormRef)">预览</el-button>
          <el-button @click="visible = true">取消</el-button>
        </div>
      </el-card>
    </div>
  </div>
  <dialogVue v-if="visible" :visible="visible" :dateList="previewList" @handleCancel="handleCancel"></dialogVue>
</template>
<script setup lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { getUserByGroup as getData, schedulingPreview } from "@/api/personnel";
import { useSiteConfig } from "@/stores/siteConfig";
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h } from "vue";
import { ElMessage, ElMessageBox, ElText, FormInstance, FormRules } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { WarningFilled, Delete, CirclePlus, ArrowRight } from "@element-plus/icons-vue";

import dialogVue from "./detailsDialog";

const siteConfig = useSiteConfig();

interface RuleForm {
  name: string;
  day: number;
  radio1: string;
  radio2: string;
}
interface Tree {
  id: number;
  label: string;
  children?: Tree[];
}

const formSize = ref("default");
const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive<RuleForm>({
  name: "Hello",
  day: null,
  radio1: "QUICK",
  radio2: "3",
});

const defaultProps = {
  children: "children",
  label: "name",
  id: "id",
};

const userGroup = ref([]);

const morningList = ref([
  {
    name: "早班",
    startTime: "08:00",
    endTime: "20:00",
    user: "",
  },
  {
    name: "晚班",
    startTime: "20:00",
    endTime: "08:00",
    user: "",
  },
]);

const afterList = ref([
  {
    name: "早班",
    startTime: "",
    endTime: "",
    user: "",
  },
  {
    name: "中班",
    startTime: "",
    endTime: "",
    user: "",
  },
  {
    name: "晚班",
    startTime: "",
    endTime: "",
    user: "",
  },
]);

const customList = ref([
  {
    name: "",
    startTime: "",
    endTime: "",
    user: "",
  },
]);

const previewList = ref([]);

const showErrorMsg = ref(false);

const visible = ref(false);
const rules = reactive<FormRules<RuleForm>>({
  day: [{ required: true, message: "请输入需循环的周期", trigger: "blur" }],
});

const router = useRouter();
const route = useRoute();
const ctx = getCurrentInstance()!;
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}

const dutyTime = ref([
  // { label: "2小时", value: 2 },
  { label: "4小时", value: 4 },
  { label: "6小时", value: 6 },
  { label: "8小时", value: 8 },
  { label: "12小时", value: 12 },
  { label: "24小时", value: 24 },
]);

const checkUserList = ref([]);

const useCheckUserList = ref([]);

function beforeCreate() {
  querysItem();
}
function created() {}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}

/* 返回上一页 */
function backRouter() {
  router.back();
}

const remove = (node: Node, data: Tree) => {
  const parent = node.parent;
  const children: Tree[] = parent.data.children || parent.data;
  const index = children.findIndex((d) => d.id === data.id);
  children.splice(index, 1);
  useCheckUserList.value = [...useCheckUserList.value];
};

function handleCancel(val) {
  // // console.log(val);
  visible.value = val;
}

function treeCheck(checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys) {
  // // console.log(checkedNodes,checkedKeys,halfCheckedNodes,halfCheckedKeys)
  checkUserList.value = checkedKeys.checkedNodes;
}

function shift(type: string) {
  if (type === "odd") {
    useCheckUserList.value = [...checkUserList.value, ...useCheckUserList.value];
  } else {
    let ids = "";
    let name = "";
    checkUserList.value.forEach((item) => {
      // useCheckUserList.value.push({
      //   id:item.
      // })
      ids += item.id + ",";
      name += item.name + ",";
    });
    useCheckUserList.value.push({
      id: ids,
      name,
    });
  }
}

//排班发生改变事件
function changeTime(value, index, type, list) {
  // if (list.length > 1) {
  if (type == "end") {
    if (index != list.length - 1) {
      return (list[index + 1].startTime = list[index].endTime);
    }
  } else {
    if (index > 0) {
      return (list[index - 1 < 0 ? 0 : index - 1].endTime = list[index].startTime);
    }
  }
  // }
  let arr = [];

  list.forEach((v, i) => {
    // // console.log(v, 5555);
    arr.push([v.startTime, v.endTime]);
  });
  arr.unshift(["00:00", list[0].startTime]);
  if (list[0].startTime === list[list.length - 1].endTime) {
    arr.push([list[list.length - 1].startTime, "24:00"]);
  }
  const timeArr = Array(24).fill("");

  arr.forEach((item) => {
    for (let index = Number(item[0].split(":")[0]); index < Number(item[1]?.split(":")[0]); index++) {
      // // console.log("--", index);

      timeArr[index] = "true";
    }
  });

  if (timeArr.some((i) => i === "")) {
    showErrorMsg.value = true;
  } else {
    showErrorMsg.value = false;
  }
}
//添加自定义排班
function addCustomItem() {
  customList.value.push({
    name: "",
    startTime: "",
    endTime: "",
    user: "",
  });
  showErrorMsg.value = true;
}
//移除自定义排班
function deleteCustomItem(index) {
  customList.value.splice(index, 1);
}

//获取用户组下用户列表
async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  // // console.log(route, router);
  getData({
    id: route.params.id,
  }).then((res) => {
    if (res.code === 200 && res.success) {
      userGroup.value = res.data;
    }
  });
}

async function watchScheduling(formEl: FormInstance | undefined) {
  let data = {
    cycle: ruleForm.day,
    fineSchedules: [],
  };
  let quickShifts = {
    schedulePlan: ruleForm.day,
    staffIds: [],
    staffNames: [],
  };
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      if (ruleForm.radio1 === "FINE") {
        switch (ruleForm.radio2) {
          case "1":
            setPreviewList(morningList.value, data);
            schedulingPreview({ scheduleType: ruleForm.radio1, fineShifts: data }).then((res) => {
              if (res.success) {
                visible.value = true;
                previewList.value = res.data.fineShifts[0];
              }
            });

            break;
          case "2":
            setPreviewList(afterList.value, data);
            schedulingPreview({ scheduleType: ruleForm.radio1, fineShifts: data }).then((res) => {
              if (res.success) {
                visible.value = true;
                previewList.value = res.data.fineShifts[0];
              }
            });

            // // console.log(afterList.value);

            break;
          case "3":
            setPreviewList(customList.value, data);
            schedulingPreview({ scheduleType: ruleForm.radio1, fineShifts: data }).then((res) => {
              if (res.success) {
                visible.value = true;
                previewList.value = res.data.fineShifts[0];
              }
            });

            // // console.log(customList.value);

            break;
        }
      } else {
        setPreviewList(useCheckUserList.value, quickShifts);
        schedulingPreview({ scheduleType: ruleForm.radio1, quickShifts }).then((res) => {
          if (res.success) {
            // // console.log(res);
            // visible.value = true;
            // previewList.value = res.data.quickShifts[0];
          }
        });
      }
    }
  });

  // // console.log(ruleForm.radio1, ruleForm.radio2);
}

function setPreviewList(list, data) {
  if (ruleForm.radio1 === "FINE") {
    list.forEach((item, index) => {
      let users = [];
      data.fineSchedules.push({
        name: item.name,
        startTime: item.startTime,
        endTime: item.endTime,
        staffIds: item.user,
        staffNames: [],
      });
      users = userGroup.value.filter((itemA) => {
        return item.user.includes(itemA.id);
      });
      users.forEach((v, i) => {
        data.fineSchedules[index].staffNames.push(v.name);
      });
    });
  } else {
    ///
    list.forEach((item, index) => {
      data.staffIds.push(item.id);
      data.staffNames.push(item.name);
      // data.fineSchedules.push({
      //   name: item.name,
      //   startTime: item.startTime,
      //   endTime: item.endTime,
      //   staffIds: item.user,
      //   staffNames: [],
      // });
    });
    //  // console.log(list, data);
  }
}

beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, '🚀[onErrorCaptured]'), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, '🚀[onRenderTracked]'), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, '🚀[onRenderTriggered]'), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
</script>
<style lang="scss" scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.operator {
  width: 100%;
  height: 300px;
  display: flex;
  flex-direction: column;
  border: 1px solid #ccc;
  > .operator-head {
    width: 100%;
    height: 40px;
    border-bottom: 1px solid #ccc;
    padding: 0 10px;
    box-sizing: border-box;
    line-height: 40px;
  }
  > .operator-main {
    flex: 1;
    padding: 10px 10px;
    box-sizing: border-box;
  }
}
.operator-button {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  > .el-button {
    margin-left: 0;
    margin-bottom: 10px;
  }
}
.card-header {
  color: rgb(32, 128, 255);
  font-size: 16px;
  font-weight: 500;
  > p {
    display: flex;
    align-items: center;
    > span {
      display: flex;
      align-items: center;

      font-size: 12px;
      padding-left: 30px;
      color: rgb(102, 102, 102);
      > i {
        color: rgb(252, 202, 0);
      }
    }
  }
}

.people-duty {
  margin-left: 38px;
  h3 {
    font-weight: 700;
    margin-bottom: 5px;
  }
  p {
    color: #606266;
    margin-bottom: 20px;
  }
  .custom-error-message {
    color: #db3328;
  }
}
.edit-form-wrap {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}
.customIcon {
  font-size: 20px;
  margin-left: 15px;
  cursor: pointer;
}
</style>
