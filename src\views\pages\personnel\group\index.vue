<template>
  <el-tabs v-model="active" class="demo-tabs">
    <!-- .filter((v) => (v.name === activeType.tenant ? !(userInfo.currentTenant || {}).baileeTenantId : true)) -->
    <template v-for="tab in tabs" :key="`tab-${tab.name}`">
      <el-tab-pane :label="tab.label" :name="tab.name" :disabled="tab.disabled">
        <!-- {{ tab }} -->
        <template v-if="tab.disabled">
          <el-empty :description="$t('glob.noPower')" />
        </template>
        <template v-else>
          <el-container>
            <el-aside :width="selectActive.showAside ? '240px' : '0px'" :style="{ transition: 'all 0.3s ease', marginRight: selectActive.showAside ? '20px' : '0' }">
              <el-card :body-style="{ padding: '12px 0' }">
                <template #header>
                  <div class="tw-flex tw-leading-[14px]">
                    <span class="tw-mr-auto tw-whitespace-nowrap">用户组</span>
                    <el-link type="primary" :icon="Refresh" :underline="false" @click.prevent="handleStateRefresh()"></el-link>
                  </div>
                </template>
                <template #default>
                  <div class="tw-mb-[10px] tw-h-[35px]">
                    <el-input class="tw-mx-[20px] tw-w-[calc(100%_-_40px)]" v-model="state.search.name" placeholder="输入名称搜索" :prefix-icon="Search" clearable></el-input>
                  </div>
                  <el-scrollbar v-loading="state.loading" :height="height - 164">
                    <div v-for="item in currentList.filter((v) => (state.search.name ? (v.name || '').indexOf(state.search.name) !== -1 : true))" :key="`list_${item.id}`" class="tw-cursor-pointer tw-px-[20px] hover:tw-bg-[var(--el-color-primary-light-9)]" :class="current === item.id ? ['tw-bg-[var(--el-color-primary-light-9)]'] : []" @click.stop="current = item.id">
                      <el-text :type="current === item.id ? 'primary' : ''" truncated class="tw-h-[32px] tw-whitespace-nowrap tw-align-middle tw-leading-[32px]">
                        <span>{{ item.name }}</span>
                        <span v-if="userInfo.currentTenantId !== item.tenantId" class="tw-mx-1" style="color: var(--el-color-danger)">{{ item.tenantName }} [{{ item.tenantAbbreviation }}]</span>
                      </el-text>
                      <!-- <el-text type="info" truncated class="tw-h-[32px] tw-whitespace-nowrap tw-align-middle tw-leading-[32px]">{{ item.tenantName }}[{{ item.tenantAbbreviation }}]</el-text> -->
                    </div>
                  </el-scrollbar>
                </template>
              </el-card>
            </el-aside>
            <el-main :style="{ width: `${width - (tab.showAside ? 260 : 0)}px`, margin: '0', padding: '0', transition: 'all 0.3s ease' }">
              <el-card :body-style="{ padding: '20px' }">
                <template v-if="selectCurrent || active === activeType.details">
                  <component v-loading="state.loading" v-if="active === tab.name" :is="tab.Component" :width="width - (tab.showAside ? 300 : 0)" :height="height - 95" :title="tab.label" :data="state.data" :cols="state.column" :paging="{ page: state.page, size: state.size }" :current="selectCurrent"></component>
                </template>
                <template v-else>
                  <el-empty description="请选择一个用户组" :style="{ height: `${height - 95}px` }"></el-empty>
                </template>
              </el-card>
            </el-main>
            <!-- 123456789 -->
          </el-container>
        </template>
      </el-tab-pane>
    </template>
  </el-tabs>
  <EditorForm ref="editorRef" title="用户组">
    <template #deleteItem="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          确定
          {{ t("glob.delete") }}
          <span :style="{ color: 'var(--el-color-danger)' }">{{ params.name }}</span>
          用户组吗？
        </p>
      </div>
      <!-- <p class="tw-mt-[20px]">删除后将无法恢复！</p> -->
    </template>
  </EditorForm>
</template>

<script lang="ts" setup name="SystemConfig">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, shallowRef, reactive, shallowReactive, readonly, shallowReadonly, computed, inject, nextTick, onMounted, getCurrentInstance, h, provide, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { sizes } from "@/utils/common";
import { find, first } from "lodash-es";
import DetailsConfig from "./models/DetailsConfig.vue";
import UserConfig from "./models/UserConfig.vue";
import RoleConfig from "./models/RoleConfig.vue";
import TenantConfig from "./models/TenantConfig.vue";
import { handleStateCreateKey, handleStateEditorKey, handleStateDeleteKey, handleStateCutBasicAuthorityKey, handleStateRefreshKey } from "./models/helper";
import { useSiteConfig } from "@/stores/siteConfig";

import { ElMessage, ElTag } from "element-plus";
import { Search, Plus, Edit, Delete, More, InfoFilled, Refresh } from "@element-plus/icons-vue";
import { getGroupList as getData, addGroupData as addData, modGroupData as modData, delGroupData as delData, cutRoleBasicAuthority } from "@/api/personnel";
import type { GroupItem as DataItem } from "@/api/personnel";
import getUserInfo from "@/utils/getUserInfo";
import { userGroupUnassignTenant } from "@/views/pages/apis/userGroup";

import EditorForm from "./Editor.vue";

const { proxy } = getCurrentInstance()!;
if (!proxy) throw new Error("Current Instance Not Proxy");
const { t } = useI18n();

const route = useRoute();
const router = useRouter();
const siteConfig = useSiteConfig();
const userInfo = getUserInfo();

enum activeType {
  details = "details",
  role = "role",
  user = "user",
  tenant = "tenant",
}
const currentList = computed(() => state.data.filter((v) => ([activeType.tenant].includes(active.value) ? userInfo.currentTenantId === v.tenantId : true)));
const active = computed({
  // route.query.type === activeType.tenant ? ((userInfo.currentTenant || {}).baileeTenantId ? activeType.details : (route.query.type as activeType) || activeType.details) :
  get: () => (route.query.type as activeType) || activeType.details,
  set: (v) => ((route.query.type as activeType) || activeType.details) !== v && router.push({ query: { ...route.query, type: v } }),
});
const current = computed({
  get: () => (route.query.current as string) || (first(currentList.value) || {}).id || null,
  set: (v) => ((route.query.current as string) || (first(currentList.value) || {}).id || null) !== v && router.push({ query: { ...route.query, current: v || "" } }),
});
const selectActive = computed<Partial<(typeof tabs)[number]>>(() => find(tabs, (v) => v.name === active.value) || {});
const selectCurrent = computed(() => find(currentList.value, (v) => v.id === current.value) || null);

watch(active, () => {
  if (!find(currentList.value, (v) => v.id === current.value)) {
    current.value = (first(currentList.value) || {}).id || null;
  }
});

const tabs = shallowReadonly([
  { label: "用户组列表", name: activeType.details, disabled: !userInfo.hasPermission(proxy.PERMISSION.group.preview), Component: DetailsConfig, showAside: false },
  { label: "成员", name: activeType.user, disabled: !userInfo.hasPermission(proxy.PERMISSION.group.preview), Component: UserConfig, showAside: true },
  { label: "角色", name: activeType.role, disabled: !userInfo.hasPermission(proxy.PERMISSION.group.preview), Component: RoleConfig, showAside: true },
  { label: "客户", name: activeType.tenant, disabled: !userInfo.hasPermission(proxy.PERMISSION.group.preview), Component: TenantConfig, showAside: true },
]);

const width = inject("width", ref(0));
const height = inject("height", ref(0));

const publicParams = computed<Record<string, unknown>>(() => ({}));

interface StateData<T> {
  loading: boolean;
  select: string[];
  current: string | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: import("./models/helper").Col<T>[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<DataItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {
    keyword: "",
  },
  column: [
    /* 列 */
    // { key: "id", label: "用户 ID" },
    { key: "name", label: "用户组名称" },
    { key: "tenantName", label: "所属客户", formatter: (_row, _col, v) => `${v} [${_row.tenantAbbreviation}]` },
    { key: "note", label: "备注" },
    // { key: "createdTime", label: "创建日期", formatter: (_row, _col, v) => formatterDate(v as string) },
    // { key: "updatedTime", label: "修改日期", formatter: (_row, _col, v) => formatterDate(v as string) },
  ],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

const editorRef = shallowRef<InstanceType<typeof EditorForm>>();

async function createItem(params: Partial<DataItem> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...publicParams.value, ...params }, async (req) => {
      const { success, message, data } = await addData({ name: <string>req.name, note: <string>req.note });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`添加成功！`);
    });
  } catch (error) {
    /*  */
  }
}
async function editorItem(params: Partial<DataItem> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...publicParams.value, ...params }, async (req) => {
      const { success, message, data } = await modData({ id: <string>params.id, name: <string>req.name, note: <string>req.note });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`编辑成功！`);
    });
  } catch (error) {
    /*  */
  }
}
async function deleteItem(params: Partial<DataItem> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.confirm({ $title: `${t("glob.delete")}用户组`, $slot: "deleteItem", ...publicParams.value, ...params }, async (req) => {
      if (params.tenantId != userInfo.currentTenantId) {
        const { success } = await userGroupUnassignTenant({ groupIds: [params.id], tenantIds: [userInfo.currentTenantId] });
        // // console.log(success)
        if (success) {
          ElMessage.success("操作成功");
          // getTenantUserGroups(userInfo.currentTenantId);
        }
      } else {
        const { success, message, data } = await delData({ id: <string>params.id });
        if (!success) throw Object.assign(new Error(message), { success, data });
        ElMessage.success(`删除成功！`);
      }
    });
  } catch (error) {
    /*  */
  }
}
async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  let controller = new AbortController();
  const response = getData({
    ...publicParams.value,
    ...params,
    ...(typeof state.search.name === "string" && state.search.name ? { name: state.search.name } : {}),
    external: true,
    appId: (siteConfig.baseInfo || {}).app,
    // paging: {
    //   pageNumber: state.page,
    //   pageSize: state.size,
    // },
    controller,
  });
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await response;
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);
      return data instanceof Array ? data : [];
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(20);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}

onMounted(() => {
  handleStateRefresh();
});

async function handleStateCreate(params: Partial<DataItem> & Record<string, unknown>) {
  await createItem(params);
  await handleStateRefresh();
}
async function handleStateEditor(params: Partial<DataItem> & Record<string, unknown>) {
  await editorItem(params);
  await handleStateRefresh();
}
async function handleStateDelete(params: Partial<DataItem> & Record<string, unknown>) {
  await deleteItem(params);
  await handleStateRefresh();
}
async function handleStateCutBasicAuthority(params: Partial<DataItem> & Record<string, unknown>) {
  try {
    const { success, message, data } = await cutRoleBasicAuthority(params);
    if (success) ElMessage.success(`成功${params.basic ? "设置为" : "取消"}基础角色`);
    else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
  await handleStateRefresh();
}
async function handleStateRefresh() {
  if (state.loading) return;
  state.loading = true;
  state.data.splice(0, state.data.length, ...(await querysItem({})));
  await nextTick();
  const $data = state.data;
  // .filter((v) => ([activeType.role, activeType.tenant].includes(active.value) ? userInfo.currentTenantId === v.tenantId : true));
  if (!find($data, (v) => v.id === current.value)) current.value = (first($data) || {}).id || null;
  state.loading = false;
}

provide(handleStateCreateKey, handleStateCreate);
provide(handleStateEditorKey, handleStateEditor);
provide(handleStateDeleteKey, handleStateDelete);
provide(handleStateCutBasicAuthorityKey, handleStateCutBasicAuthority);
provide(handleStateRefreshKey, handleStateRefresh);
</script>
