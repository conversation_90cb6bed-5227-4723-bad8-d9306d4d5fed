<template>
  <div>
    <div class="tw-flex tw-h-[50px] tw-flex-row tw-items-start">
      <div class="tw-mr-auto"></div>
      <div class="tw-mx-auto"></div>
      <div class="tw-ml-auto">
        <span v-if="userInfo.hasPermission(PERMISSION.group.create)" class="tw-h-fit">
          <el-button type="primary" :icon="Plus" @click="handleStateCreate({})">{{ $t("glob.add") }}用户组</el-button>
        </span>
      </div>
    </div>
    <el-table :data="data" :height="height - 50">
      <el-table-column v-for="_col in cols" :key="_col.key" :prop="_col.key as string" :label="_col.label" :min-width="_col.width || 120" :formatter="_col.formatter" />
      <el-table-column :label="t('glob.operate')" align="center" header-align="center" :width="180" fixed="right">
        <template #header="{ column }">
          <!-- <div style="display: flex; justify-content: center"> -->
          <span class="tw-mr-[2.5px]">{{ column.label }}</span>
          <el-link class="tw-ml-[2.5px] tw-align-middle" type="primary" :underline="false" :title="t('glob.refresh')" @click.prevent="handleStateRefresh()"></el-link>
          <!-- </div> -->
        </template>
        <template #default="{ row }">
          <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group.editor)">
            <span v-show="row.tenantId === userInfo.currentTenantId">
              <el-link :disabled="row.global || !userInfo.hasPermission(PERMISSION.group.editor)" type="primary" :underline="false" :title="t('glob.edit')" class="tw-mx-[5px]" @click.prevent="handleStateEditor(row)">{{ $t("glob.edit") }}</el-link>
            </span>
          </el-tooltip>
          <!-- <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group.remove)">
            <span v-show="row.tenantId === userInfo.currentTenantId">
              <router-link :to="{ name: '545105319160709120', params: { id: row.id || '' }, query: { fallback: $route.name as string,tenant:row.tenantId } }">
                <template #default="{ navigate }">
                  <el-link :disabled="row.global || !userInfo.hasPermission(PERMISSION.group.remove)" type="primary" :underline="false" @click="navigate" class="tw-mx-[5px]">排班详情</el-link>
                </template>
              </router-link>
            </span>
          </el-tooltip> -->
          <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group.remove)">
            <span>
              <el-link :disabled="row.global || !userInfo.hasPermission(PERMISSION.group.remove)" type="danger" :underline="false" :title="t('glob.delete')" class="tw-mx-[5px]" @click.prevent="handleStateDelete(row)">{{ row.tenantId === userInfo.currentTenantId ? $t("glob.delete") : "移除" }}</el-link>
            </span>
          </el-tooltip>

          <!-- <el-dropdown trigger="click" :disabled="false" @command="$event.callback(row)" class="tw-align-middle">
            <span style="font-size: var(--el-font-size-base)">
              <el-link type="default" :underline="false" :icon="More" :disabled="false" :title="t('glob.More')" class="tw-mx-[5px]" @click.prevent=""></el-link>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="row.basic" :command="{ callback: () => handleStateCutBasicAuthority({ ...row, basic: false }) }" class="tw-text-[var(--el-color-danger)]">取消基础角色</el-dropdown-item>
                <el-dropdown-item v-else :command="{ callback: () => handleStateCutBasicAuthority({ ...row, basic: true }) }" class="tw-text-[var(--el-color-primary)]">设置基础角色</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown> -->
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup generic="T extends import('@/api/personnel').RoleItem, C extends import('./helper').Col<T>">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, readonly, nextTick, inject, h, computed, onMounted, PropType } from "vue";
import { useI18n } from "vue-i18n";
import getUserInfo from "@/utils/getUserInfo";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import { ElMessage } from "element-plus";

import { Refresh, Plus, Edit, Delete, More } from "@element-plus/icons-vue";
import { userGroupUnassignTenant, delUserGroup } from "@/views/pages/apis/userGroup";

import { handleStateCreateKey, handleStateEditorKey, handleStateDeleteKey, handleStateCutBasicAuthorityKey, handleStateRefreshKey } from "./helper";

const { t } = useI18n();
const userInfo = getUserInfo();

interface Props {
  width?: number;
  height?: number;
  title?: string;
  data: T[];
  cols: C[];
  current?: Partial<T>;
  paging: Record<"page" | "size", number>;
}
const props = withDefaults(defineProps<Props>(), { title: "", width: 0, height: 0, current: () => ({}) as Partial<T> });
const width = computed(() => props.width || inject("width", ref(0)).value);
const height = computed(() => props.height || inject("height", ref(0)).value);
const data = computed(() => props.data);
const cols = computed(() => props.cols);
const current = computed(() => props.current);

interface Form {
  [key: string]: any;
}
const form = reactive<Form>({});

const handleStateCreate = inject(handleStateCreateKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateEditor = inject(handleStateEditorKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateDelete = inject(handleStateDeleteKey, async (_raw: Partial<T> & Record<string, unknown>) => {});

// const { success } = await userGroupUnassignTenant({ groupIds: [item.id], tenantIds: [$params.value.id] });
//   // // console.log(success)
//   if (success) {
//     ElMessage.success("操作成功");
//     getTenantUserGroups($params.value.id);
//   }
// async function handleStateDelete(item) {
//   if (item.tenantId != userInfo.currentTenantId) {
//     const { success } = await userGroupUnassignTenant({ groupIds: [item.id], tenantIds: [userInfo.currentTenantId] });
//     // // console.log(success)
//     if (success) {
//       ElMessage.success("操作成功");
//       // getTenantUserGroups(userInfo.currentTenantId);
//     }
//   } else {
//     const { success } = await delUserGroup({ id: item.id });
//     // // console.log(success)
//     if (success) {
//       ElMessage.success("操作成功");
//       // getTenantUserGroups(userInfo.currentTenantId);
//     }
//   }
// }
const handleStateCutBasicAuthority = inject(handleStateCutBasicAuthorityKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateRefresh = inject(handleStateRefreshKey, async () => {});
</script>

<style lang="scss" scoped></style>
