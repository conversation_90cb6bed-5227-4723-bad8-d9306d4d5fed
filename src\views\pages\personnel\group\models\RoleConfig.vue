<template>
  <div>
    <div class="tw-mb-[18px] tw-flex tw-h-[32px]">
      <div class="tw-ml-auto">
        <span v-if="userInfo.hasPermission(PERMISSION.group.assigning_roles)">
          <!-- <el-button type="primary" :icon="EditPen" size="default" @click="handleStateUserCutRole(current)">{{ t("glob.allocation") }}{{ title }}</el-button> -->
        </span>
      </div>
    </div>
    <el-table v-loading="loading" :data="dataList" :height="height - 50">
      <el-table-column prop="name" label="角色名称" :min-width="80" />
      <el-table-column prop="basic" label="特性" :min-width="80" :formatter="getBasicVnode" />
      <el-table-column prop="note" label="备注" :min-width="120" />
      <!-- <el-table-column :label="t('glob.operate')" align="left" header-align="left" :width="110" fixed="right">
        <template #header="{ column }">
          <span class="tw-mr-[2.5px]">{{ column.label }}</span>
          <el-link class="tw-ml-[2.5px] tw-align-middle" type="primary" :underline="false" :icon="Refresh" :title="t('glob.refresh')" @click.prevent="getUserList(current)"></el-link>
        </template>
        <template #default="{ row }">
          <span v-if="userInfo.hasPermission(PERMISSION.group.assigning_roles)">
            <el-link v-show="current.tenantId === userInfo.currentTenantId" class="tw-mx-[2.5px] tw-align-middle" type="primary" :underline="false" :title="t('glob.remove')" @click.prevent="delUserRole(row, current.id as string)">{{ t("glob.remove") }}</el-link>
          </span>
        </template>
      </el-table-column> -->
    </el-table>
    <EditorForm ref="editorRef" title="用户组">
      <template #deleteItem="{ params }">
        <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
          <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
          <p class="title">
            确定从
            <span :style="{ color: 'var(--el-color-danger)' }">{{ current.name }}</span>
            用户组中{{ t("glob.remove") }}
            <span :style="{ color: 'var(--el-color-danger)' }">{{ params.name }}</span>
            {{ title }}吗？
          </p>
        </div>
      </template>
    </EditorForm>
  </div>
</template>

<script lang="ts" setup generic="T extends import('@/api/personnel').GroupItem, C extends import('./helper').Col<T>">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, shallowRef, reactive, readonly, nextTick, inject, h, computed, onMounted, watch } from "vue";
import { useI18n } from "vue-i18n";
import { Col } from "./helper";
import moment from "moment";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import { ElFormItem, ElMessage, ElOption, ElSelect, ElTag } from "element-plus";

import { Refresh, SemiSelect, Edit, Delete, More, Plus, InfoFilled, EditPen } from "@element-plus/icons-vue";

import { handleStateCreateKey, handleStateEditorKey, handleStateDeleteKey, handleStateCutBasicAuthorityKey, handleStateRefreshKey } from "./helper";

import { getRoleByGroup, setGroupByRole, getRoleByUsable } from "@/api/personnel";
import EditorForm from "../Editor.vue";
import { bindFormBox } from "@/utils/bindFormBox";
import getUserInfo from "@/utils/getUserInfo";
import { useSiteConfig } from "@/stores/siteConfig";

const { t } = useI18n();
const userInfo = getUserInfo();
const siteConfig = useSiteConfig();
const editorRef = shallowRef<InstanceType<typeof EditorForm>>();

interface Props {
  width?: number;
  height?: number;
  title?: string;
  data: T[];
  cols: C[];
  current?: Partial<T>;
  paging: Record<"page" | "size", number>;
}
const props = withDefaults(defineProps<Props>(), { title: "", width: 0, height: 0, current: () => ({}) as Partial<T> });
const width = computed(() => props.width || inject("width", ref(0)).value);
const height = computed(() => props.height || inject("height", ref(0)).value);
const data = computed(() => props.data);
const cols = computed(() => props.cols);
const current = computed(() => props.current);
const title = computed(() => props.title);

interface Form {
  [key: string]: any;
}
const loading = ref(false);
const dataList = ref<import("@/api/personnel").RoleItem[]>([]);
const form = reactive<Form>({});

const getBasicVnode = (row: any) => {
  const characteristic: import("vue").VNode[] = [];
  if (row.superAdmin) characteristic.push(h(ElTag, { style: { margin: "0 4px" }, type: "" }, () => t("glob.Role By Admin")));
  if (row.dataAdmin) characteristic.push(h(ElTag, { style: { margin: "0 4px" }, type: "success" }, () => t("glob.Role By Admin")));
  if (row.basic) characteristic.push(h(ElTag, { style: { margin: "0 4px" }, type: "info" }, () => t("glob.Role By Users")));

  return characteristic.length ? h({ setup: () => () => characteristic }) : h(ElTag, { style: { margin: "0 4px" }, type: "info" }, () => t("glob.Role By Normal"));
};

onMounted(() => {
  watch(
    current,
    async (current) => {
      if (!current) return;
      await getUserList(current);
    },
    { immediate: true }
  );
});

async function getUserList(current: Partial<T>) {
  if (!current.id) return;
  try {
    loading.value = true;
    await nextTick();
    const { success, message, data } = await getRoleByGroup({ id: current.id as string, appId: (siteConfig.baseInfo || {}).app || "" });
    if (!success) throw Object.assign(new Error(message), { success, data });
    dataList.value = data instanceof Array ? data : [];
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}
async function delUserRole(params: import("@/api/personnel").RoleItem, id: string) {
  if (!editorRef.value) return;
  try {
    loading.value = true;
    await editorRef.value.confirm({ $title: `${t("glob.remove")}角色`, $slot: "deleteItem", ...params }, async (req) => {
      const { success: listSuccess, message: listMessage, data: listData } = await getRoleByGroup({ id, appId: (siteConfig.baseInfo || {}).app || "" });
      if (!listSuccess) throw Object.assign(new Error(listMessage), { success: listSuccess, data: listData });
      const roleIds: string[] = [];
      for (let i = 0; i < (listData instanceof Array ? listData : []).length; i++) {
        const item = (listData instanceof Array ? listData : [])[i];
        if (item.id !== params.id) roleIds.push(item.id || "");
      }
      const { app } = siteConfig.baseInfo!;
      const { success, message, data } = await setGroupByRole({ appId: app, roleIds, userGroupIds: [id] });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`成功从用户组中${t("glob.remove")}角色！`);
    });
  } catch (error) {
    /*  */
  } finally {
    loading.value = false;
    await getUserList(current.value);
  }
  try {
    loading.value = true;
    await nextTick();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loading.value = false;
    await getUserList(current.value);
  }
}

async function handleStateUserCutRole(current: Partial<T>) {
  const form = reactive<Record<"title", string> & { roleIds: string[] }>({
    title: `${t("glob.allocation")}${title.value}`,
    roleIds: [],
  });
  let roleIdList: any = [];
  try {
    const { success, message, data } = await getRoleByGroup({ id: <string>current.id, appId: (siteConfig.baseInfo || {}).app || "" });
    if (!success) throw Object.assign(new Error(message), { success, data });
    // form.roleIds = (data instanceof Array ? data : []).map((v) => v.id!);
    roleIdList = data;
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }

  let currentRole: import("@/api/personnel").RoleItem[] = [];

  try {
    const { success, message, data } = await getRoleByUsable({ appId: (siteConfig.baseInfo || {}).app });
    if (!success) throw Object.assign(new Error(message), { success, data });
    currentRole.push(...(data instanceof Array ? data : []));

    let arr = [];
    arr = currentRole.filter((itemA: any) => {
      return roleIdList.every((itemB: any) => {
        return itemB.id !== itemA.id;
      });
    });
    currentRole = arr;
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
  try {
    await bindFormBox([h(ElFormItem, { rules: [{ required: false, type: "array", message: "请选择角色", trigger: "blur" }], prop: "roleIds", label: `${current.name} 拥有的${title.value}` }, () => h(ElSelect, { "modelValue": form.roleIds, "onUpdate:modelValue": ($event) => (form.roleIds = $event), "multiple": true, "clearable": true, "collapseTags": false, "collapseTagsTooltip": true, "filterable": true, "style": { width: "100%" } }, () => currentRole.map((role) => h(ElOption, { label: role.name!, value: role.id! }))))], form, async () => {
      const { app } = siteConfig.baseInfo!;
      const { success, message, data } = await setGroupByRole({ appId: app, userGroupIds: [current.id as string], roleIds: form.roleIds });
      if (success) {
        ElMessage.success(t("axios.Operation successful"));
      } else throw Object.assign(new Error(message), { success, data });
      return { success, message };
    });
  } catch (error) {
    /*  */
  }
  await handleStateRefresh();
}
const handleStateCreate = inject(handleStateCreateKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateEditor = inject(handleStateEditorKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateDelete = inject(handleStateDeleteKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateCutBasicAuthority = inject(handleStateCutBasicAuthorityKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateRefresh = inject(handleStateRefreshKey, async () => {});
</script>

<style lang="scss" scoped></style>
