<template>
  <div>
    <div class="tw-mb-[18px] tw-flex tw-h-[32px]">
      <div class="tw-ml-auto">
        <span v-if="userInfo.hasPermission(PERMISSION.group.add_personnel)">
          <el-button type="primary" size="default" @click="handleStateUserCutRole(current)">{{ t("glob.allocation") }}{{ title }}</el-button>
        </span>
      </div>
    </div>
    <el-table v-loading="loading" :data="dataList" :height="height - 50">
      <el-table-column prop="name" label="姓名" :min-width="80" />
      <el-table-column prop="account" label="账号" :min-width="120">
        <template #default="{ row }"> {{ row.account }}@{{ row.tenantAbbreviation }} </template>
      </el-table-column>
      <el-table-column prop="phone" label="手机号" :min-width="120" />
      <el-table-column prop="email" label="邮箱" :min-width="120" />
      <el-table-column prop="createdTime" label="注册时间" :min-width="140" :formatter="(_row: unknown, _col: unknown, v: string) => (v ? moment(v, 'x').format('YYYY-MM-DD HH:mm') : '--')" />
      <el-table-column :label="t('glob.operate')" align="left" header-align="left" :width="110" fixed="right">
        <template #header="{ column }">
          <span class="tw-mr-[2.5px]">{{ column.label }}</span>
          <el-link class="tw-ml-[2.5px] tw-align-middle" type="primary" :underline="false" :title="t('glob.refresh')" @click.prevent="getUserList(current)"></el-link>
        </template>
        <template #default="{ row }">
          <span v-if="userInfo.hasPermission(PERMISSION.group.del_personnel)">
            <el-link v-show="current.tenantId === userInfo.currentTenantId" class="tw-mx-[2.5px] tw-align-middle" type="primary" :underline="false" :title="t('glob.remove')" @click.prevent="delUserRole(row, current.id as string)">{{ t("glob.remove") }}</el-link>
          </span>
        </template>
      </el-table-column>
    </el-table>
    <EditorForm ref="editorRef" title="用户组">
      <template #deleteItem="{ params }">
        <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
          <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
          <p class="title">
            确定从
            <span :style="{ color: 'var(--el-color-danger)' }">{{ current.name }}</span>
            用户组中{{ t("glob.remove") }}
            <span :style="{ color: 'var(--el-color-danger)' }">{{ params.name }}</span>
            {{ title }}吗？
          </p>
        </div>
      </template>
    </EditorForm>
  </div>
</template>

<script lang="ts" setup generic="T extends import('@/api/personnel').GroupItem, C extends import('./helper').Col<T>">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, shallowRef, reactive, readonly, nextTick, inject, h, computed, onMounted, watch } from "vue";
import { useI18n } from "vue-i18n";
import { Col } from "./helper";
import moment from "moment";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import { ElFormItem, ElMessage, ElOption, ElSelect, ElTag } from "element-plus";

import { Refresh, SemiSelect, Edit, Delete, More, Plus, InfoFilled, EditPen } from "@element-plus/icons-vue";

import { handleStateCreateKey, handleStateEditorKey, handleStateDeleteKey, handleStateCutBasicAuthorityKey, handleStateRefreshKey } from "./helper";

import { getUserByGroup, getUser, delUserByGroup, addUserByGroup } from "@/api/personnel";
import EditorForm from "../Editor.vue";
import { bindFormBox } from "@/utils/bindFormBox";
import getUserInfo from "@/utils/getUserInfo";

const { t } = useI18n();
const userInfo = getUserInfo();
const editorRef = shallowRef<InstanceType<typeof EditorForm>>();

interface Props {
  width?: number;
  height?: number;
  title?: string;
  data: T[];
  cols: C[];
  current?: Partial<T>;
  paging: Record<"page" | "size", number>;
}
const props = withDefaults(defineProps<Props>(), { title: "", width: 0, height: 0, current: () => ({}) as Partial<T> });
const width = computed(() => props.width || inject("width", ref(0)).value);
const height = computed(() => props.height || inject("height", ref(0)).value);
const data = computed(() => props.data);
const cols = computed(() => props.cols);
const current = computed(() => props.current);
const title = computed(() => props.title);

interface Form {
  [key: string]: any;
}
const loading = ref(false);
const dataList = ref<import("@/api/personnel").EntrustUserItem[]>([]);
const form = reactive<Form>({});

onMounted(() => {
  watch(
    current,
    async (current) => {
      if (!current) return;
      await getUserList(current);
    },
    { immediate: true }
  );
});

async function getUserList(current: Partial<T>) {
  if (!current.id) return;
  try {
    loading.value = true;
    await nextTick();
    const { success, message, data } = await getUserByGroup({ id: current.id as string });
    if (!success) throw Object.assign(new Error(message), { success, data });
    dataList.value = data instanceof Array ? data : [];
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}
async function delUserRole(params: import("@/api/personnel").EntrustUserItem, id: string) {
  if (!editorRef.value) return;
  try {
    loading.value = true;
    await editorRef.value.confirm({ $title: `${t("glob.remove")}用户组`, $slot: "deleteItem", ...params }, async (req) => {
      const { success, message, data } = await delUserByGroup({ id, userIds: [params.id] });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`成功从用户组中${t("glob.remove")}${title.value}！`);
    });
  } catch (error) {
    /*  */
  } finally {
    loading.value = false;
    await getUserList(current.value);
  }
}

async function handleStateUserCutRole(current: Partial<T>) {
  const form = reactive<Record<"title", string> & { userIds: string[] }>({
    title: `${t("glob.allocation")}${title.value}`,
    userIds: [],
  });
  let userIdList: any = [];
  try {
    const { success, message, data } = await getUserByGroup({ id: current.id as string });
    if (!success) throw Object.assign(new Error(message), { success, data });
    // form.userIds = (data instanceof Array ? data : []).map((v) => <string>v.userId);
    userIdList = data;
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
  form.userIds = [];

  let allList: (import("@/api/personnel").EntrustUserItem & { blocked?: boolean })[] = [];

  try {
    const { success, message, data } = await getUser({ paging: { pageNumber: 1, pageSize: 99999 } }); // 获取用户
    if (!success) throw Object.assign(new Error(message), { success, data });
    allList.push(...(data instanceof Array ? data : []).map((v) => Object.assign(v as unknown as import("@/api/personnel").EntrustUserItem, { blocked: false })));
    let arr = [];
    arr = allList.filter((itemA: any) => {
      return userIdList.every((itemB: any) => {
        return itemB.userId !== itemA.id;
      });
    });
    allList = arr;
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
  try {
    await bindFormBox([h(ElFormItem, { rules: [{ required: false, type: "array", message: `请选择${title.value}`, trigger: "blur" }], prop: "roleIds", label: `${current.name} 拥有的${title.value}` }, () => h(ElSelect, { "modelValue": form.userIds, "onUpdate:modelValue": ($event) => (form.userIds = $event), "multiple": true, "clearable": true, "collapseTags": false, "collapseTagsTooltip": true, "filterable": true, "style": { width: "100%" } }, () => allList.map((v) => h(ElOption, { label: v.name + ` (${v.account}@${v.tenantAbbreviation})` || "", value: v.id || "", disabled: v.blocked }))))], form, async () => {
      const { success, message, data } = await addUserByGroup({ id: <string>current.id, userIds: allList.filter((v) => !v.blocked && form.userIds.includes(<string>v.id)).map((v) => <string>v.id) });
      if (success) {
        ElMessage.success(t("axios.Operation successful"));
      } else throw Object.assign(new Error(message), { success, data });
      return { success, message };
    });
  } catch (error) {
    /*  */
  }
  await handleStateRefresh();
}

const handleStateCreate = inject(handleStateCreateKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateEditor = inject(handleStateEditorKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateDelete = inject(handleStateDeleteKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateCutBasicAuthority = inject(handleStateCutBasicAuthorityKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateRefresh = inject(handleStateRefreshKey, async () => {});
</script>

<style lang="scss" scoped></style>
