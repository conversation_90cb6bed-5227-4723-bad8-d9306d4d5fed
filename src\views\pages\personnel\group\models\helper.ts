export interface Col<T = Record<string, unknown>> {
  key: keyof T;
  label?: string;
  align?: "left" | "center" | "right";
  width?: number;
  formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode;
}

export const handleStateCreateKey = Symbol("handleStateCreate");
export const handleStateEditorKey = Symbol("handleStateEditor");
export const handleStateDeleteKey = Symbol("handleStateDelete");
export const handleStateCutBasicAuthorityKey = Symbol("handleStateCutBasicAuthority");
export const handleStateRefreshKey = Symbol("handleStateRefresh");
