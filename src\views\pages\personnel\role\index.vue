<template>
  <el-tabs v-model="active" class="demo-tabs">
    <el-tab-pane v-for="tab in tabs" :key="`tab-${tab.name}`" :label="tab.label" :name="tab.name" :disabled="tab.disabled">
      <template v-if="tab.disabled">
        <el-empty :description="$t('glob.noPower')" />
      </template>
      <template v-else>
        <el-container>
          <el-aside :width="selectActive.showAside ? '240px' : '0px'" :style="{ transition: 'all 0.3s ease', marginRight: selectActive.showAside ? '20px' : '0' }">
            <el-card :body-style="{ padding: '12px 0' }">
              <template #header>
                <div class="tw-flex tw-leading-[14px]">
                  <span class="tw-mr-auto tw-whitespace-nowrap">角色</span>
                  <el-link type="primary" :icon="Refresh" :underline="false" @click.prevent="handleStateRefresh()"></el-link>
                </div>
              </template>
              <template #default>
                <div class="tw-mb-[10px] tw-h-[35px]">
                  <el-input class="tw-mx-[20px] tw-w-[calc(100%_-_40px)]" v-model="state.search.name" placeholder="输入名称搜索" :prefix-icon="Search" clearable></el-input>
                </div>
                <el-scrollbar v-loading="state.loading" :height="height - 164">
                  <div v-for="item in state.data.filter((v) => (active === activeType.authority ? !v.global : true)).filter((v) => (state.search.name ? (v.name || '').indexOf(state.search.name) !== -1 : true))" :key="`list_${item.id}`" class="tw-cursor-pointer tw-px-[20px] hover:tw-bg-[var(--el-color-primary-light-9)]" :class="current === item.id ? ['tw-bg-[var(--el-color-primary-light-9)]'] : []" @click.stop="current = item.id || ''">
                    <el-text :type="current === item.id ? 'primary' : ''" truncated class="tw-h-[32px] tw-whitespace-nowrap tw-align-middle tw-leading-[32px]">{{ item.name }}</el-text>
                  </div>
                </el-scrollbar>
              </template>
            </el-card>
          </el-aside>
          <el-main :style="{ width: `${width - (tab.showAside ? 260 : 0)}px`, margin: '0', padding: '0', transition: 'all 0.3s ease' }">
            <el-card :body-style="{ padding: '20px' }">
              <template v-if="selectCurrent || active === activeType.details">
                <component v-loading="state.loading" v-if="active === tab.name" :is="tab.Component" :width="width - (tab.showAside ? 300 : 0)" :height="height - 95" :title="tab.label" :data="state.data" :cols="state.column" :paging="{ page: state.page, size: state.size }" :current="find(state.data, (v) => v.id === current)"></component>
              </template>
              <template v-else>
                <el-empty description="请选择一个用角色" :style="{ height: `${height - 95}px` }"></el-empty>
              </template>
            </el-card>
          </el-main>
        </el-container>
      </template>
    </el-tab-pane>
  </el-tabs>
  <EditorForm ref="editorRef" title="角色">
    <template #deleteItem="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title">
          确定
          {{ t("glob.delete") }}
          <span :style="{ color: 'var(--el-color-danger)' }">{{ params.name }}</span>
          角色吗？
        </p>
      </div>
      <!-- <p class="tw-mt-[20px]">删除后将无法恢复！</p> -->
    </template>
  </EditorForm>
</template>

<script lang="ts" setup name="SystemConfig">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, shallowRef, reactive, shallowReactive, readonly, shallowReadonly, computed, inject, nextTick, onMounted, getCurrentInstance, h, provide, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { sizes } from "@/utils/common";
import { find, first } from "lodash-es";
import DetailsConfig from "./models/DetailsConfig.vue";
import AuthorityConfig from "./models/AuthorityConfig.vue";
import UserConfig from "./models/UserConfig.vue";
import GroupConfig from "./models/GroupConfig.vue";
import { handleStateCreateKey, handleStateEditorKey, handleStateDeleteKey, handleStateCutBasicAuthorityKey, handleStateRefreshKey } from "./models/helper";
import { useSiteConfig } from "@/stores/siteConfig";
import getUserInfo from "@/utils/getUserInfo";

import { ElMessage, ElTag } from "element-plus";
import { Search, Plus, Edit, Delete, More, InfoFilled, Refresh } from "@element-plus/icons-vue";

import { getRole as getData, addRole as addData, modRole as modData, delRole as delData, cutRoleBasicAuthority } from "@/api/personnel";
import type { RoleItem as DataItem } from "@/api/personnel";

import EditorForm from "./Editor.vue";

const { proxy } = getCurrentInstance()!;
if (!proxy) throw new Error("Current Instance Not Proxy");
const { t } = useI18n();

const route = useRoute();
const router = useRouter();
const siteConfig = useSiteConfig();
const userInfo = getUserInfo();

enum activeType {
  details = "details",
  authority = "authority",
  user = "user",
  group = "group",
}
const active = computed({
  get: () => (route.query.type as activeType) || activeType.details,
  set: (v) => ((route.query.type as activeType) || activeType.details) !== v && router.push({ query: { ...route.query, type: v } }),
});
const current = computed({
  get: () => (route.query.current as string) || (first(state.data) || {}).id || null,
  set: (v) => ((route.query.current as string) || (first(state.data) || {}).id || null) !== v && router.push({ query: { ...route.query, current: v } }),
});
const selectActive = computed<Partial<(typeof tabs)[number]>>(() => find(tabs, (v) => v.name === active.value) || {});
const selectCurrent = computed(() => find(state.data, (v) => v.id === current.value) || {});

watch(active, (active) => {
  const $data = state.data.filter((v) => (active === activeType.authority ? !v.global : true));
  if (!find($data, (v) => v.id === current.value)) current.value = (first($data) || {}).id || null;
});

const tabs = shallowReadonly([
  { label: "角色列表", name: activeType.details, disabled: !userInfo.hasPermission(proxy.PERMISSION.role.preview), Component: DetailsConfig, showAside: false },
  { label: "权限", name: activeType.authority, disabled: !userInfo.hasPermission(proxy.PERMISSION.role.assigning_role_permission), Component: AuthorityConfig, showAside: true },
  { label: "用户", name: activeType.user, disabled: !userInfo.hasPermission(proxy.PERMISSION.user.preview), Component: UserConfig, showAside: true },
  { label: "用户组", name: activeType.group, disabled: !userInfo.hasPermission(proxy.PERMISSION.group.preview), Component: GroupConfig, showAside: true },
]);

const width = inject("width", ref(0));
const height = inject("height", ref(0));

const publicParams = computed<Record<string, unknown>>(() => ({}));

interface StateData<T> {
  loading: boolean;
  select: string[];
  current: string | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: import("./models/helper").Col<T>[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<DataItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {
    keyword: "",
  },
  column: [
    /* 列 */
    // { key: "id", label: "用户 ID" },
    { key: "name", label: "角色名称" },
    {
      key: "basic",
      label: "特性",
      formatter: (row) => {
        const characteristic: import("vue").VNode[] = [];
        if (row.superAdmin) characteristic.push(h(ElTag, { style: { margin: "0 4px" }, type: "" }, () => t("glob.Role By Admin")));
        // if (row.global) characteristic.push(h(ElTag, { style: { margin: "0 4px" }, type: "success" }, () => t("glob.Role By Gloub")));
        if (row.basic) characteristic.push(h(ElTag, { style: { margin: "0 4px" }, type: "success" }, () => t("glob.Role By Users")));

        return characteristic.length ? h({ setup: () => () => characteristic }) : h(ElTag, { style: { margin: "0 4px" }, type: "info" }, () => t("glob.Role By Normal"));
      },
    },
    { key: "note", label: "备注" },
    // { key: "createdTime", label: "创建日期", formatter: (_row, _col, v) => formatterDate(v as string) },
    // { key: "updatedTime", label: "修改日期", formatter: (_row, _col, v) => formatterDate(v as string) },
  ],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

const editorRef = shallowRef<InstanceType<typeof EditorForm>>();

async function createItem(params: Partial<DataItem> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    const { app } = siteConfig.baseInfo!;
    await editorRef.value.open({ ...publicParams.value, ...params }, async (req) => {
      const { success, message, data } = await addData({ name: <string>req.name, note: <string>req.note, appId: app });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`添加成功！`);
    });
  } catch (error) {
    /*  */
  }
}
async function editorItem(params: Partial<DataItem> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...publicParams.value, ...params }, async (req) => {
      const { success, message, data } = await modData({ ...req });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`编辑成功！`);
    });
  } catch (error) {
    /*  */
  }
}
async function deleteItem(params: Partial<DataItem> & Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.confirm({ $title: `${t("glob.delete")}角色`, $slot: "deleteItem", ...publicParams.value, ...params }, async (req) => {
      const { success, message, data } = await delData({ ...req });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`删除成功！`);
    });
  } catch (error) {
    /*  */
  }
}
async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  let controller = new AbortController();
  const response = getData({
    ...publicParams.value,
    appId: siteConfig.baseInfo?.app,
    ...params,
    ...(typeof state.search.name === "string" && state.search.name ? { name: state.search.name } : {}),
    // paging: {
    //   pageNumber: state.page,
    //   pageSize: state.size,
    // },
    controller,
  });
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await response;
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);
      return data instanceof Array ? data : [];
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(20);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}

onMounted(() => {
  handleStateRefresh();
});

async function handleStateCreate(params: Partial<DataItem> & Record<string, unknown>) {
  await createItem(params);
  await handleStateRefresh();
}
async function handleStateEditor(params: Partial<DataItem> & Record<string, unknown>) {
  await editorItem(params);
  await handleStateRefresh();
}
async function handleStateDelete(params: Partial<DataItem> & Record<string, unknown>) {
  await deleteItem(params);
  await handleStateRefresh();
}
async function handleStateCutBasicAuthority(params: Partial<DataItem> & Record<string, unknown>) {
  try {
    const { success, message, data } = await cutRoleBasicAuthority(params);
    if (success) ElMessage.success(`成功${params.basic ? "设置为" : "取消"}基础角色`);
    else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
  await handleStateRefresh();
}
async function handleStateRefresh() {
  if (state.loading) return;
  state.loading = true;
  state.data.splice(0, state.data.length, ...(await querysItem({})));
  await nextTick();
  const $data = state.data.filter((v) => (active.value === activeType.authority ? !v.global : true));
  if (!find($data, (v) => v.id === current.value)) current.value = (first($data) || {}).id || null;
  state.loading = false;
}

provide(handleStateCreateKey, handleStateCreate);
provide(handleStateEditorKey, handleStateEditor);
provide(handleStateDeleteKey, handleStateDelete);
provide(handleStateCutBasicAuthorityKey, handleStateCutBasicAuthority);
provide(handleStateRefreshKey, handleStateRefresh);
</script>
