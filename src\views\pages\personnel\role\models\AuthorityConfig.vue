<template>
  <div class="tw-mt-[-20px]">
    <el-tabs v-model="active" class="demo-tabs" @tab-change="(isEdit = false), (isAllAuth = false), (showSearch = false), ($paging = { page: 1, size: 30, total: 0 }), nextTick(() => getAuthTree($event))">
      <el-tab-pane v-for="(tab, index) in tabs" :key="`tab-${tab.name}`" :label="tab.label" :name="tab.name">
        <div class="tw-flex tw-items-center">
          <div v-if="[activeType.resource, activeType.region, activeType.location, activeType.contact, activeType.vendor, activeType.resource_group, activeType.resource_type, activeType.completion, activeType.alert_classification, activeType.support_note].includes(active)" class="tw-mb-[0px] tw-h-[40px]">
            <el-checkbox v-show="isEdit" v-model="isAllAuth" border>全部权限</el-checkbox>
          </div>
          <div class="tw-mb-[0px] tw-ml-auto tw-h-[40px]">
            <template v-if="!isEdit">
              <el-button type="primary" :disabled="current.global" @click.stop="resetTreeChecked(true, index)">配置权限</el-button>
            </template>
            <template v-else>
              <el-button type="default" :disabled="current.global" @click.stop="(isEdit = false), getAuthTree(active), (showSearch = false)">取消</el-button>
              <el-button type="primary" :disabled="current.global" @click.stop="resetTreeChecked(false, index)">保存</el-button>
            </template>
          </div>
        </div>
        <el-scrollbar :height="height - ([activeType.resource, activeType.location, activeType.contact].includes(active) ? 125 : 75)">
          <el-input v-model="filterText" v-if="active === 'ability' && showSearch" placeholder="请输入需要筛选的权限名称" style="width: 300px" />
          <div style="width: 100%">
            <el-checkbox size="mini" v-if="active === 'ability' && showSearch" :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange" style="padding: 0px; margin-right: 5px"> 全选</el-checkbox>
          </div>

          <!-- <component v-loading="loading" v-if="active === tab.name" :is="tab.Component"></component> -->
          <div v-if="isAllAuthList && !isEdit && active !== activeType.ability" style="text-align: center">
            {{ "已选择" + tab.label + "全部权限" }}
          </div>

          <component v-loading="loading" v-else-if="active === tab.name" :is="tab.Component" :ref="(vm: unknown) => (toRaw(tab).ref = vm)" :name="tab.name" :isEdit="isEdit" :isAllAuth="isAllAuth"></component>
          <!-- <component v-loading="loading" v-else-if="!isAllAuthList && isEdit && active == 'ability' && active != 'resource'" :is="tab.Component" :name="tab.name" :isEdit="isEdit" :ref="tab.ref" :isAllAuth="isAllAuth"></component> -->

          <!-- <devicePermission v-if="!isAllAuthList && !isEdit && active == 'resource'" :name="tab.name" :isEdit="isEdit" ref="deviceRef" :isAllAuth="isAllAuth"></devicePermission> -->
          <!-- <component v-loading="loading" v-if="active === 'ability'" :is="tab.Component"></component> -->

          <!-- <AuthorityConfig  ref="editorForPermissionRef" title="客户权限"/> -->
        </el-scrollbar>
        <el-pagination v-if="[activeType.location, activeType.contact].includes(active)" class="tw-mt-[18px]" @size-change="getAuthTree(active)" @current-change="getAuthTree(active)" v-model:currentPage="$paging.page" :page-sizes="sizes" v-model:page-size="$paging.size" layout="->, total, sizes, prev, pager, next, jumper" :total="$paging.total"></el-pagination>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup generic="T extends import('@/api/personnel').RoleItem, C extends import('./helper').Col<T>">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, shallowRef, reactive, readonly, shallowReadonly, toRaw, nextTick, inject, h, computed, onMounted, watch, createVNode, openBlock, createElementBlock } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { sizes } from "@/utils/common";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import { ElCheckbox, ElCol, ElEmpty, ElIcon, ElMessage, ElRow, ElTree } from "element-plus";

import { Refresh, Plus, Edit, Delete, More, Pointer, Folder } from "@element-plus/icons-vue";

import { handleStateCreateKey, handleStateEditorKey, handleStateDeleteKey, handleStateCutBasicAuthorityKey, handleStateRefreshKey } from "./helper";
import { getRolePermission, setRolePermission, getRoleDataAuth, setRoleDataAuth, setLoggerData } from "@/api/personnel";
import devicePermission from "./devicePermisssion.vue";
import { getResourceList, getRegionList, getLocationList, getContactList, getVendorList, getResourceGroupList, getResourceTypeList, getCompletionList, getAlertClassificationList, getSupportNoteList, getMainSupportNoteData } from "@/api/auth";

import type Node from "element-plus/es/components/tree/src/model/node";
// import AuthorityConfig  from "./AuthorityConfig.vue";

const { t } = useI18n();
const treeRef = shallowRef<InstanceType<typeof ElTree>>();

const route = useRoute();
const router = useRouter();

enum activeType {
  ability = "ability",
  resource = "resource",
  region = "region",
  location = "location",
  contact = "contact",
  vendor = "vendor",
  resource_group = "resource_group",
  resource_type = "resource_type",
  completion = "completion",
  alert_classification = "alert_classification",
  support_note = "support_note",
}

const active = computed({
  get: () => (route.query.auth as activeType) || activeType.ability,
  set: (v) => ((route.query.auth as activeType) || activeType.ability) !== v && router.push({ query: { ...route.query, auth: v } }),
});

const showSearch = ref(false);
const AuthTreeVNode = createVNode({
  setup: () => () => {
    if (current.value.global) return h(ElEmpty, { description: "无法为全局角色分配权限" });
    else {
      return createVNode(
        ElTree,
        { ref: (vm) => (treeRef.value = vm as InstanceType<typeof ElTree>), data: isEdit.value || isAllAuth.value ? authTree.value : previewTreeItem.value, nodeKey: "id", showCheckbox: false, props: { disabled: (v: TreeData) => (v.isLeaf ? v.disabled : v.disabled || !v.children.length), class: (data: TreeData) => `tree_context tree_data_${data.isLeaf ? "button" : "dir"}` }, defaultExpandAll: true, renderAfterExpand: false, filterNodeMethod: filterNode },
        {
          default: ({ node, data: authCatalog }: { node: Node; data: TreeData }) =>
            h("div", { class: ["node_context_view"], style: { width: "100%" } }, [
              h("div", { class: ["el-tree-node__label"], style: { display: "flex", alignItems: "center" } }, [h(ElIcon, { style: { marginRight: "3px" } }, () => h(authCatalog.isLeaf ? Pointer : Folder)), h("span", { style: { color: "var(--text-color)" } }, node.label)]),
              h("div", { class: [], style: { cursor: "default" }, onClick: (e: Event) => e.stopPropagation() }, [
                h(ElRow, { class: ["tree_item_group"] }, () => [
                  /* 全选按钮 */
                  ...(isEdit.value && !isAllAuth.value ? [h(ElCol, { class: [], style: { display: "flex" }, span: 24 }, () => h(ElCheckbox, { modelValue: authCatalog.items.every((v) => authActive.value.includes(v.id)), disabled: !authCatalog.items.length || authCatalog.disabled, indeterminate: authCatalog.items.every((v) => authActive.value.includes(v.id)) ? false : authCatalog.items.some((v) => authActive.value.includes(v.id)), onChange: () => (authActive.value = Array.from(authCatalog.items.every((v) => authActive.value.includes(v.id)) ? authCatalog.items.filter((v) => !v.disabled).reduce((p, c) => (p.delete(c.id), p), new Set(authActive.value)) : authCatalog.items.filter((v) => !v.disabled).reduce((p, c) => (p.add(c.id), p), new Set(authActive.value)))), style: { width: "100%", overflow: "hidden", textOverflow: "ellipsis" }, title: t("glob.All Select") }, () => t("glob.All Select")))] : []),
                  /* 权限按钮 */
                  ...authCatalog.items.map((v) => h(ElCol, { key: `${authCatalog.id}_${v.id}`, class: [], style: { display: "flex", padding: "12px" }, span: 24, xs: 12, sm: 12, md: 8, lg: 6, xl: 6 }, () => h("div", { class: ["list-item"], style: { display: "flex", width: "100%", alignItems: "center", border: "1px solid var(--el-border-color)", borderRadius: "3px", padding: "0 8px 0 16px" } }, [isEdit.value ? h(ElCheckbox, { modelValue: isAllAuth.value ? true : authActive.value.includes(v.id), disabled: isAllAuth.value || v.disabled || authCatalog.disabled, onChange: () => (authActive.value.includes(v.id) ? authActive.value.splice(authActive.value.indexOf(v.id), 1) : authActive.value.push(v.id)), style: { width: "100%", overflow: "hidden", textOverflow: "ellipsis" }, title: v.label }, () => v.label) : h("span", { style: { lineHeight: "32px", width: "100%", overflow: "hidden", textOverflow: "ellipsis" }, title: v.label }, v.label)]))),
                ]),
              ]),
            ]),
        }
      );
    }
  },
});
const AuthSelectTreeVNode = createVNode({
  setup: () => () => {
    if (current.value.global) return h(ElEmpty, { description: "无法为全局角色分配权限" });
    else {
      return createVNode(ElTree, { ref: (vm) => (treeRef.value = vm as InstanceType<typeof ElTree>), data: isEdit.value || isAllAuth.value ? authTree.value : previewTree.value, nodeKey: "id", showCheckbox: !isAllAuth.value && isEdit.value, props: { disabled: (v: TreeData) => (v.isLeaf ? v.disabled : v.disabled || !v.children.length) }, defaultExpandAll: true, renderAfterExpand: false, onCheck: (_raw: never, { checkedNodes }: { checkedNodes: TreeData[] }) => (authActive.value = checkedNodes.filter((v) => v.isLeaf).map((v) => v.id)) });
    }
  },
});
const deviceRef = ref<InstanceType<typeof devicePermission>>();
const tabs = shallowReadonly([
  { label: "功能", name: activeType.ability, Component: AuthTreeVNode, ref: <unknown>null },
  { label: "设备", name: activeType.resource, Component: devicePermission, ref: <unknown>null },
  { label: "区域", name: activeType.region, Component: AuthSelectTreeVNode, ref: <unknown>null },
  { label: "场所", name: activeType.location, Component: AuthTreeVNode, ref: <unknown>null },
  { label: "联系人", name: activeType.contact, Component: AuthTreeVNode, ref: <unknown>null },
  { label: "供应商", name: activeType.vendor, Component: AuthTreeVNode, ref: <unknown>null },
  { label: "设备分组", name: activeType.resource_group, Component: AuthTreeVNode, ref: <unknown>null },
  { label: "设备类型", name: activeType.resource_type, Component: AuthTreeVNode, ref: <unknown>null },
  // { label: "代码完成配置", name: activeType.completion, Component: AuthSelectTreeVNode, ref: <unknown>null },
  { label: "告警分类", name: activeType.alert_classification, Component: AuthTreeVNode, ref: <unknown>null },
  // { label: "行动策略", name: activeType.support_note, Component: AuthTreeVNode, ref: <unknown>null },
]);
const isAllAuthList = ref(false);

// resource("cmdb.resource");
// region("cmdb.region");
// location("cmdb.location");
// contact("cmdb.contact");
// resource_group("cmdb.resource.group");
// resource_type("cmdb.resource.type");
// vendor("cmdb.vendor");
// completion("ec.completeCode");
// alert_classification("cmdb.alert.classification");
// support_note("cmdb.support.note")

interface TreeData {
  label: string;
  id: string;
  items: TreeData[];
  children: TreeData[];
  disabled: boolean;
  order: number;
  isLeaf: boolean;
  parentId?: string | null;
  appId: string;
}

interface Props {
  width?: number;
  height?: number;
  title?: string;
  data: T[];
  cols: C[];
  current?: Partial<T>;
  paging: Record<"page" | "size", number>;
}
const props = withDefaults(defineProps<Props>(), { title: "", width: 0, height: 0, current: () => ({} as Partial<T>) });
const width = computed(() => props.width || inject("width", ref(0)).value);
const height = computed(() => props.height || inject("height", ref(0)).value);
const data = computed(() => props.data);
const cols = computed(() => props.cols);
const current = computed(() => props.current);
const $paging = ref({
  page: 1,
  size: 30,
  total: 0,
});
const isAllAuth = ref(false);

interface Form {
  [key: string]: any;
}
const loading = ref(false);
const isEdit = ref(false);
const authActive = ref<string[]>([]);
const authTree = ref<TreeData[]>([]);
const allPreviewTree = ref([]);
const previewTreeItem = computed((): TreeData[] => {
  const hasTreeEffectiveItem = (items: TreeData[]) => {
    for (let i = 0; i < items.length; i++) {
      if (items[i].items.length) return true;
      else if (hasTreeEffectiveItem(items[i].children)) return true;
    }
    return false;
  };
  const subHasTreeItem = (parent: TreeData[], item: TreeData) => {
    const childrenItem: TreeData = { label: item.label, id: item.id, children: [], items: item.items.filter((v) => authActive.value.includes(v.id)), disabled: item.disabled, order: item.order, isLeaf: item.isLeaf, parentId: item.parentId, appId: item.appId };
    for (let i = 0; i < item.children.length; i++) {
      subHasTreeItem(childrenItem.children, item.children[i]);
    }
    if (hasTreeEffectiveItem([childrenItem])) parent.push(childrenItem);
    return parent;
  };
  return authTree.value.reduce(subHasTreeItem, [] as TreeData[]);
});
const previewTree = computed((): TreeData[] => {
  const subHasTreeItem = (parent: TreeData[], item: TreeData) => {
    if (authActive.value.includes(item.id)) return [...parent, item];
    const childrenItem: TreeData = { label: item.label, id: item.id, children: [], items: [], disabled: item.disabled, order: item.order, isLeaf: item.isLeaf, parentId: item.parentId, appId: item.appId };
    for (let i = 0; i < item.children.length; i++) {
      if (authActive.value.includes(item.children[i].id)) {
        childrenItem.children.push(item.children[i]);
      } else {
        childrenItem.children.push(...subHasTreeItem([], item.children[i]));
      }
    }
    if (childrenItem.children.length) return [...parent, childrenItem];
    else return [...parent];
  };
  return authTree.value.reduce(subHasTreeItem, [] as TreeData[]);
});
const form = reactive<Form>({});

let checkAll = ref(false); //全选按钮的绑定值
let isIndeterminate = ref(false); //全选按钮的全选，半选样式
interface Tree {
  [key: string]: any;
}
const filterText = ref("");
watch(filterText, (val) => {
  if (val) {
    treeRef.value?.filter(val);
  } else {
    treeRef.value?.filter("");
  }
});

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.label.includes(value);
};
// //全选按钮勾上的方法事件
function handleCheckAllChange(val) {
  if (checkAll.value == true) {
    //如果是当前值是全选，依次遍历节点设置勾选，同时过滤的disabled为true的

    authActive.value = allPreviewTree.value.map(function (item, index, array) {
      // 处理函数作为参数

      return item.id;
    });
  } else {
    //取消全选时置空
    authActive.value = [];
  }
}

onMounted(() => {
  watch(
    current,
    async (current) => {
      isEdit.value = false;
      if (!current) return;
      $paging.value = { page: 1, size: 30, total: 0 };
      await getAuthTree(active.value);
    },
    { immediate: true }
  );
});
const deviceRegionIds = ref([]);
const deviceLocationIds = ref([]);
const regionIdList = ref([]);
const locationIdList = ref([]);
const contactIdList = ref([]);
const vendorIdList = ref([]);
const resourceGroupIdList = ref([]);
const resourceTypeIdList = ref([]);
const alarmTypeIdList = ref([]);

async function getAuthTree(type: activeType) {
  authTree.value = [];
  authActive.value = [];
  deviceRegionIds.value = [];
  deviceLocationIds.value = [];
  regionIdList.value = [];
  locationIdList.value = [];
  contactIdList.value = [];
  vendorIdList.value = [];
  resourceGroupIdList.value = [];
  resourceTypeIdList.value = [];
  alarmTypeIdList.value = [];
  if (!current.value.id) return;
  try {
    loading.value = true;
    await nextTick();
    await router.isReady();

    switch (type) {
      case activeType.ability: {
        if (current.value.global) return (authTree.value = []);
        /* TODO: 功能权限配置 */
        const { success, message, data } = await getRolePermission({ id: current.value.id as string });
        if (!success) throw Object.assign(new Error(message), { success, data });

        const catalogs = (data.catalogs instanceof Array ? data.catalogs : []).filter((v) => v.enabled);
        const permissions = (data.permissions instanceof Array ? data.permissions : []).filter((v) => v.enabled);
        allPreviewTree.value = data.permissions;
        const helperCatalogs = new Map<string | null, TreeData>();
        for (let i = 0; i < catalogs.length; i++) {
          helperCatalogs.set(catalogs[i].id || null, { label: catalogs[i]?.name, id: catalogs[i].id, children: [], items: [], order: Number(catalogs[i].orderNum) || 0, disabled: !catalogs[i].enabled, isLeaf: false, parentId: catalogs[i].parentId || null, appId: catalogs[i].appId });
        }
        const helperPermissions = new Map<string | null, TreeData[]>();
        for (let i = 0; i < permissions.length; i++) {
          if (!helperPermissions.has(permissions[i].catalogId || null)) helperPermissions.set(permissions[i].catalogId || null, []);
          const item = helperPermissions.get(permissions[i].catalogId || null)!;
          item.push({ label: permissions[i]?.name, id: permissions[i].id, children: [], items: [], order: Number(permissions[i].orderNum) || 0, disabled: !permissions[i].enabled, isLeaf: true, appId: permissions[i].appId });
        }

        const iterator = helperCatalogs.keys();
        let index: IteratorResult<string | null>;
        while (!(index = iterator.next()).done) {
          const item = helperCatalogs.get(index.value);
          if (!item) continue;
          if (helperPermissions.has(index.value)) {
            item.items.splice(0, item.items.length, ...(helperPermissions.get(index.value) || []));
            item.items.sort((a, b) => a.order - b.order);
          }
          if (item.parentId) {
            const parent = helperCatalogs.get(item.parentId);
            if (parent) {
              if ("parentId" in item) delete item.parentId;
              parent.children.push(item);
              parent.children.sort((a, b) => a.order - b.order);
            }
          }
        }
        helperCatalogs.forEach((v) => {
          if ("parentId" in v) {
            delete v.parentId;
            authTree.value.push(v);
            authTree.value.sort((a, b) => a.order - b.order);
          }
        });
        await nextTick();
        authActive.value = data.assignedPermissionIds instanceof Array ? data.assignedPermissionIds : [];
        break;
      }
      case activeType.resource: {
        const [{ success: activeSuccess, message: activeMessage, data: activeData }, { success, message, data, page, size, total }] = await Promise.all([getRoleDataAuth({ id: current.value.id as string, dataTypes: ["cmdb.resource"] }), getResourceList({ paging: { pageNumber: $paging.value.page, pageSize: $paging.value.size } })]);
        if (!activeSuccess) throw Object.assign(new Error(activeMessage), { success: activeSuccess, data: activeData });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const [auth] = [...(activeData instanceof Array ? activeData : []), { authValue: "", dataType: "cmdb.resource" }];

        $paging.value = { page: Number(page), size: Number(size), total: Number(total) };
        authTree.value = [
          {
            label: "设备权限",
            id: auth.dataType,
            children: [],
            items: (data instanceof Array ? data : []).map((v): TreeData => ({ label: `${<string>v?.name}（${(<Record<string, unknown>>v.config || {}).ipAddress}）`, id: v.id, children: [], items: [], order: 0, disabled: false, isLeaf: true, parentId: null, appId: "" })),
            disabled: false,
            order: 0,
            isLeaf: false,
            parentId: null,
            appId: "",
          },
        ];
        try {
          // // console.log(activeData, 77777777777);
          const _v = JSON.parse(auth.authValue);

          tabs.forEach((v, i) => {
            if (v.label == "设备") {
              setTimeout(() => {
                if (v.ref) v.ref.open(JSON.parse(activeData[0].authValue).ids, JSON.parse(activeData[0].authValue).regionIds, JSON.parse(activeData[0].authValue).locationIds);
              }, 500);
            }
          });

          // deviceRef.value[0].
          isAllAuth.value = Boolean(auth.hasAllAuth);
          isAllAuthList.value = Boolean(auth.hasAllAuth);
          //           const deviceRegionIds=ref([])
          // const deviceLocationIds=ref([])
          authActive.value = _v.ids instanceof Array ? [..._v.ids] : [];
          deviceRegionIds.value = _v.regionIds instanceof Array ? [..._v.regionIds] : [];
          deviceLocationIds.value = _v.locationIds instanceof Array ? [..._v.locationIds] : [];
          // console.log(regionIds.value, locationIds.value,)
        } catch (error) {
          isAllAuth.value = false;
          isAllAuthList.value = false;
          authActive.value = [];
          tabs.forEach((v, i) => {
            if (v.label == "设备") {
              setTimeout(() => {
                if (v.ref) v.ref.open([], [], []);
              }, 500);
            }
          });
        }

        break;
      }
      case activeType.region: {
        const [{ success: activeSuccess, message: activeMessage, data: activeData }, { success, message, data, page, size, total }] = await Promise.all([getRoleDataAuth({ id: current.value.id as string, dataTypes: ["cmdb.region"] }), getRegionList({})]);
        if (!activeSuccess) throw Object.assign(new Error(activeMessage), { success: activeSuccess, data: activeData });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const [auth] = [...(activeData instanceof Array ? activeData : []), { authValue: "", dataType: "cmdb.region" }];

        const helper = (data instanceof Array ? data : []).reduce((p, c) => (p.set(c.id, { label: c?.name, id: c.id, children: [], items: [], disabled: false, order: 0, isLeaf: true, parentId: <string>c.parentId || null, appId: "" }), p), new Map<string, TreeData>());
        const iterator = helper.keys();
        let index: IteratorResult<string | null>;
        while (!(index = iterator.next()).done) {
          const item = helper.get(index.value || "");
          if (!item) continue;
          if (item.parentId) {
            const parent = helper.get(<string>item.parentId);
            if (parent) {
              if ("parentId" in item) delete item.parentId;
              parent.children.push(item);
            }
          }
        }

        $paging.value = { page: Number(page), size: Number(size), total: Number(total) };
        authTree.value = Array.from(helper.values()).filter((v) => "parentId" in v);

        try {
          const _v = JSON.parse(auth.authValue);
          isAllAuth.value = Boolean(auth.hasAllAuth);
          isAllAuthList.value = Boolean(auth.hasAllAuth);
          authActive.value = _v.ids instanceof Array ? _v.ids : [];
          regionIdList.value = _v.ids instanceof Array ? [..._v.ids] : [];
        } catch (error) {
          isAllAuth.value = false;
          isAllAuthList.value = false;
          authActive.value = [];
        }

        break;
      }
      case activeType.location: {
        const [{ success: activeSuccess, message: activeMessage, data: activeData }, { success, message, data, page, size, total }] = await Promise.all([getRoleDataAuth({ id: current.value.id as string, dataTypes: ["cmdb.location"] }), getLocationList({ paging: { pageNumber: $paging.value.page, pageSize: $paging.value.size } })]);
        if (!activeSuccess) throw Object.assign(new Error(activeMessage), { success: activeSuccess, data: activeData });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const [auth] = [...(activeData instanceof Array ? activeData : []), { authValue: "", dataType: "cmdb.location" }];

        $paging.value = { page: Number(page), size: Number(size), total: Number(total) };
        authTree.value = [
          {
            label: "场所权限",
            id: auth.dataType,
            children: [],
            items: (data instanceof Array ? data : []).map((v): TreeData => ({ label: v?.name, id: v.id, children: [], items: [], disabled: false, order: 0, isLeaf: true, parentId: null, appId: "" })),
            disabled: false,
            order: 0,
            isLeaf: false,
            parentId: null,
            appId: "",
          },
        ];
        try {
          const _v = JSON.parse(auth.authValue);
          isAllAuth.value = Boolean(auth.hasAllAuth);
          isAllAuthList.value = Boolean(auth.hasAllAuth);
          authActive.value = _v.ids instanceof Array ? _v.ids : [];
          locationIdList.value = _v.ids instanceof Array ? [..._v.ids] : [];
        } catch (error) {
          isAllAuth.value = false;
          isAllAuthList.value = false;
          authActive.value = [];
        }

        break;
      }
      case activeType.contact: {
        const [{ success: activeSuccess, message: activeMessage, data: activeData }, { success, message, data, page, size, total }] = await Promise.all([getRoleDataAuth({ id: current.value.id as string, dataTypes: ["cmdb.contact"] }), getContactList({ paging: { pageNumber: $paging.value.page, pageSize: $paging.value.size } })]);
        if (!activeSuccess) throw Object.assign(new Error(activeMessage), { success: activeSuccess, data: activeData });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const [auth] = [...(activeData instanceof Array ? activeData : []), { authValue: "", dataType: "cmdb.contact" }];

        $paging.value = { page: Number(page), size: Number(size), total: Number(total) };
        authTree.value = [
          {
            label: "联系人权限",
            id: auth.dataType,
            children: [],
            items: (data instanceof Array ? data : []).map((v): TreeData => ({ label: v?.name, id: v.id, children: [], items: [], disabled: false, order: 0, isLeaf: true, parentId: null, appId: "" })),
            disabled: false,
            order: 0,
            isLeaf: false,
            parentId: null,
            appId: "",
          },
        ];
        try {
          const _v = JSON.parse(auth.authValue);
          isAllAuth.value = Boolean(auth.hasAllAuth);
          isAllAuthList.value = Boolean(auth.hasAllAuth);
          authActive.value = _v.ids instanceof Array ? _v.ids : [];
          contactIdList.value = _v.ids instanceof Array ? [..._v.ids] : [];
        } catch (error) {
          isAllAuth.value = false;
          isAllAuthList.value = false;
          authActive.value = [];
        }

        break;
      }
      case activeType.vendor: {
        const [{ success: activeSuccess, message: activeMessage, data: activeData }, { success, message, data, page, size, total }] = await Promise.all([getRoleDataAuth({ id: current.value.id as string, dataTypes: ["cmdb.vendor"] }), getVendorList({})]);
        if (!activeSuccess) throw Object.assign(new Error(activeMessage), { success: activeSuccess, data: activeData });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const [auth] = [...(activeData instanceof Array ? activeData : []), { authValue: "", dataType: "cmdb.vendor" }];

        $paging.value = { page: Number(page), size: Number(size), total: Number(total) };
        authTree.value = [
          {
            label: "供应商权限",
            id: auth.dataType,
            children: [],
            items: (data instanceof Array ? data : []).map((v): TreeData => ({ label: v?.name, id: v.id, children: [], items: [], disabled: false, order: 0, isLeaf: true, parentId: null, appId: "" })),
            disabled: false,
            order: 0,
            isLeaf: false,
            parentId: null,
            appId: "",
          },
        ];
        try {
          const _v = JSON.parse(auth.authValue);
          isAllAuth.value = Boolean(auth.hasAllAuth);
          isAllAuthList.value = Boolean(auth.hasAllAuth);
          authActive.value = _v.ids instanceof Array ? _v.ids : [];
          vendorIdList.value = _v.ids instanceof Array ? [..._v.ids] : [];
        } catch (error) {
          isAllAuth.value = false;
          isAllAuthList.value = false;
          authActive.value = [];
        }

        break;
      }
      case activeType.resource_group: {
        const [{ success: activeSuccess, message: activeMessage, data: activeData }, { success, message, data, page, size, total }] = await Promise.all([getRoleDataAuth({ id: current.value.id as string, dataTypes: ["cmdb.resource.group"] }), getResourceGroupList({})]);
        if (!activeSuccess) throw Object.assign(new Error(activeMessage), { success: activeSuccess, data: activeData });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const [auth] = [...(activeData instanceof Array ? activeData : []), { authValue: "", dataType: "cmdb.resource.group" }];

        $paging.value = { page: Number(page), size: Number(size), total: Number(total) };
        authTree.value = [
          {
            label: "设备分组权限",
            id: auth.dataType,
            children: [],
            items: (data instanceof Array ? data : []).map((v): TreeData => ({ label: v?.name, id: v.id, children: [], items: [], disabled: false, order: 0, isLeaf: true, parentId: null, appId: "" })),
            disabled: false,
            order: 0,
            isLeaf: false,
            parentId: null,
            appId: "",
          },
        ];
        try {
          const _v = JSON.parse(auth.authValue);
          isAllAuth.value = Boolean(auth.hasAllAuth);
          isAllAuthList.value = Boolean(auth.hasAllAuth);
          authActive.value = _v.ids instanceof Array ? _v.ids : [];
          resourceGroupIdList.value = _v.ids instanceof Array ? [..._v.ids] : [];
        } catch (error) {
          isAllAuth.value = false;
          isAllAuthList.value = false;
          authActive.value = [];
        }

        break;
      }
      case activeType.resource_type: {
        const [{ success: activeSuccess, message: activeMessage, data: activeData }, { success, message, data, page, size, total }] = await Promise.all([getRoleDataAuth({ id: current.value.id as string, dataTypes: ["cmdb.resource.type"] }), getResourceTypeList({})]);
        if (!activeSuccess) throw Object.assign(new Error(activeMessage), { success: activeSuccess, data: activeData });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const [auth] = [...(activeData instanceof Array ? activeData : []), { authValue: "", dataType: "cmdb.resource.type" }];

        $paging.value = { page: Number(page), size: Number(size), total: Number(total) };
        authTree.value = [
          {
            label: "设备类型权限",
            id: auth.dataType,
            children: [],
            items: (data instanceof Array ? data : []).map((v): TreeData => ({ label: v?.name, id: v.id, children: [], items: [], disabled: false, order: 0, isLeaf: true, parentId: null, appId: "" })),
            disabled: false,
            order: 0,
            isLeaf: false,
            parentId: null,
            appId: "",
          },
        ];
        try {
          const _v = JSON.parse(auth.authValue);
          isAllAuth.value = Boolean(auth.hasAllAuth);
          isAllAuthList.value = Boolean(auth.hasAllAuth);
          authActive.value = _v.ids instanceof Array ? _v.ids : [];
          resourceTypeIdList.value = _v.ids instanceof Array ? [..._v.ids] : [];
        } catch (error) {
          isAllAuth.value = false;
          isAllAuthList.value = false;
          authActive.value = [];
        }

        break;
      }
      case activeType.completion: {
        const [{ success: activeSuccess, message: activeMessage, data: activeData }, { success, message, data, page, size, total }] = await Promise.all([getRoleDataAuth({ id: current.value.id as string, dataTypes: ["ec.completeCode"] }), getCompletionList({})]);
        if (!activeSuccess) throw Object.assign(new Error(activeMessage), { success: activeSuccess, data: activeData });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const [auth] = [...(activeData instanceof Array ? activeData : []), { authValue: "", dataType: "ec.completeCode" }];

        const helper = (data instanceof Array ? data : []).reduce((p, c) => (p.set(c.id, { label: <string>c.codeName, id: c.id, children: [], items: [], disabled: false, order: 0, isLeaf: true, parentId: <string>c.parentId || null, appId: "" }), p), new Map<string, TreeData>());
        const iterator = helper.keys();
        let index: IteratorResult<string | null>;
        while (!(index = iterator.next()).done) {
          const item = helper.get(index.value || "");
          if (!item) continue;
          if (item.parentId) {
            const parent = helper.get(<string>item.parentId);
            if (parent) {
              if ("parentId" in item) delete item.parentId;
              parent.children.push(item);
            }
          }
        }

        $paging.value = { page: Number(page), size: Number(size), total: Number(total) };
        authTree.value = Array.from(helper.values()).filter((v) => "parentId" in v);

        try {
          const _v = JSON.parse(auth.authValue);
          isAllAuth.value = Boolean(auth.hasAllAuth);
          isAllAuthList.value = Boolean(auth.hasAllAuth);
          authActive.value = _v.ids instanceof Array ? _v.ids : [];
        } catch (error) {
          isAllAuth.value = false;
          isAllAuthList.value = false;
          authActive.value = [];
        }

        break;
      }
      case activeType.alert_classification: {
        const [{ success: activeSuccess, message: activeMessage, data: activeData }, { success, message, data, page, size, total }] = await Promise.all([getRoleDataAuth({ id: current.value.id as string, dataTypes: ["cmdb.alert.classification"] }), getAlertClassificationList({})]);
        if (!activeSuccess) throw Object.assign(new Error(activeMessage), { success: activeSuccess, data: activeData });
        if (!success) throw Object.assign(new Error(message), { success, data });
        const [auth] = [...(activeData instanceof Array ? activeData : []), { authValue: "", dataType: "cmdb.alert.classification" }];

        $paging.value = { page: Number(page), size: Number(size), total: Number(total) };
        authTree.value = [
          {
            label: "告警分类权限",
            id: auth.dataType,
            children: [],
            items: (data instanceof Array ? data : []).map((v): TreeData => ({ label: v?.name, id: v.id, children: [], items: [], disabled: false, order: 0, isLeaf: true, parentId: null, appId: "" })),
            disabled: false,
            order: 0,
            isLeaf: false,
            parentId: null,
            appId: "",
          },
        ];
        try {
          const _v = JSON.parse(auth.authValue);
          isAllAuth.value = Boolean(auth.hasAllAuth);
          isAllAuthList.value = Boolean(auth.hasAllAuth);
          authActive.value = _v.ids instanceof Array ? _v.ids : [];
          alarmTypeIdList.value = _v.ids instanceof Array ? [..._v.ids] : [];
        } catch (error) {
          isAllAuth.value = false;
          isAllAuthList.value = false;
          authActive.value = [];
        }

        break;
      }
      case activeType.support_note: {
        const [{ success: activeSuccess, message: activeMessage, data: activeData }, { success, message, data, page, size, total }, { success: mainSuccess, message: mainMessage, data: mainData }] = await Promise.all([getRoleDataAuth({ id: current.value.id as string, dataTypes: ["cmdb.support.note"] }), getSupportNoteList({}), getMainSupportNoteData({})]);
        if (!activeSuccess) throw Object.assign(new Error(activeMessage), { success: activeSuccess, data: activeData });
        if (!success) throw Object.assign(new Error(message), { success, data });
        if (!mainSuccess) throw Object.assign(new Error(mainMessage), { success: mainSuccess, data: mainData });
        const [auth] = [...(activeData instanceof Array ? activeData : []), { authValue: "", dataType: "cmdb.support.note" }];
        // console.log(mainData, 777777);
        $paging.value = { page: Number(page), size: Number(size), total: Number(total) };
        authTree.value = [
          {
            label: "全局行动策略权限",
            id: `${auth.dataType}.main`,
            children: [],
            items: (mainData instanceof Array ? mainData : []).map((v): TreeData => ({ label: v?.name, id: v.id, children: [], items: [], disabled: false, order: 0, isLeaf: true, parentId: null, appId: "" })),
            disabled: false,
            order: 0,
            isLeaf: false,
            parentId: null,
            appId: "",
          },
          {
            label: "行动策略权限",
            id: auth.dataType,
            children: [],
            items: (data instanceof Array ? data : []).map((v): TreeData => ({ label: v?.name, id: v.id, children: [], items: [], disabled: false, order: 0, isLeaf: true, parentId: null, appId: "" })),
            disabled: false,
            order: 0,
            isLeaf: false,
            parentId: null,
            appId: "",
          },
        ];
        try {
          const _v = JSON.parse(auth.authValue);
          isAllAuth.value = Boolean(auth.hasAllAuth);
          isAllAuthList.value = Boolean(auth.hasAllAuth);
          authActive.value = _v.ids instanceof Array ? _v.ids : [];
        } catch (error) {
          isAllAuth.value = false;
          isAllAuthList.value = false;
          authActive.value = [];
        }

        break;
      }
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    authTree.value = [];
  } finally {
    loading.value = false;
  }
}

async function resetTreeChecked(editor: boolean, index: number) {
  showSearch.value = editor;
  loading.value = true;
  filterText.value = "";
  // isAllAuthList.value[index] = isAllAuth.value;
  if (editor) {
    isEdit.value = true;
    await nextTick();
    await getAuthTree(active.value);
    if (treeRef.value) {
      const keys = treeRef.value.getCheckedKeys().filter((v) => !authActive.value.includes(v as string));
      for (let i = 0; i < keys.length; i++) {
        treeRef.value.setChecked(keys[i], false, false);
      }
      for (let i = 0; i < authActive.value.length; i++) {
        treeRef.value.setChecked(authActive.value[i], true, false);
      }
    }

    //
  } else {
    try {
      switch (active.value) {
        case activeType.ability: {
          /* TODO: 功能权限配置 */
          const { success, message, data } = await setRolePermission({ id: current.value.id as string, permissionIds: authActive.value });
          if (!success) throw Object.assign(new Error(message), { success, data });
          ElMessage.success("权限保存成功");
          break;
        }
        case activeType.resource: {
          // // console.log(, 4444);
          let ids: any = [];
          let regionIds: any = [];
          let locationIds: any = [];

          tabs.forEach((v, i) => {
            if (v.label == "设备") {
              // // console.log(v.ref);
              let data = [...v.ref.chooseDeviceList];
              let regions = [...v.ref.authTreeSelect];
              let locations = [...v.ref.chooseLocatinoList];

              ids = Array.from(new Set(data));
              regionIds = Array.from(new Set(regions));
              locationIds = Array.from(new Set(locations));
            }
          });

          await setLoggerData({ type: "resources", id: current.value.id as string, beforeIds: authActive.value, afterIds: ids, beforeRegionIds: deviceRegionIds.value, afterRegionIds: regionIds, beforeLocationIds: deviceLocationIds.value, afterLocationIds: locationIds }).then((res) => {
            const { success, message, data } = setRoleDataAuth({ id: current.value.id as string, dataType: "cmdb.resource", value: JSON.stringify({ hasAllAuth: isAllAuth.value, ids: ids, regionIds: regionIds, locationIds: locationIds }), hasAllAuth: isAllAuth.value, auditInfo: res.data });

            ElMessage.success("权限保存成功");
          });
          break;
        }
        case activeType.region: {
          // console.log(authActive.value, regionIdList.value);
          await setLoggerData({ type: "regions", id: current.value.id as string, beforeIds: regionIdList.value, afterIds: authActive.value }).then((res) => {
            const { success, message, data } = setRoleDataAuth({ id: current.value.id as string, dataType: "cmdb.region", value: JSON.stringify({ hasAllAuth: isAllAuth.value, ids: authActive.value }), hasAllAuth: isAllAuth.value, auditInfo: res.data });

            ElMessage.success("权限保存成功");
          });

          break;
        }
        case activeType.location: {
          //
          await setLoggerData({ type: "locations", id: current.value.id as string, beforeIds: locationIdList.value, afterIds: authActive.value }).then((res) => {
            const { success, message, data } = setRoleDataAuth({ id: current.value.id as string, dataType: "cmdb.location", value: JSON.stringify({ hasAllAuth: isAllAuth.value, ids: authActive.value }), hasAllAuth: isAllAuth.value, auditInfo: res.data });

            ElMessage.success("权限保存成功");
          });

          break;
        }
        case activeType.contact: {
          await setLoggerData({ type: "contacts", id: current.value.id as string, beforeIds: contactIdList.value, afterIds: authActive.value }).then((res) => {
            const { success, message, data } = setRoleDataAuth({ id: current.value.id as string, dataType: "cmdb.contact", value: JSON.stringify({ hasAllAuth: isAllAuth.value, ids: authActive.value }), hasAllAuth: isAllAuth.value, auditInfo: res.data });

            ElMessage.success("权限保存成功");
          });

          break;
        }

        case activeType.vendor: {
          // console.log(vendorIdList.value, authActive.value);
          await setLoggerData({ type: "vendors", id: current.value.id as string, beforeIds: vendorIdList.value, afterIds: authActive.value }).then((res) => {
            const { success, message, data } = setRoleDataAuth({ id: current.value.id as string, dataType: "cmdb.vendor", value: JSON.stringify({ hasAllAuth: isAllAuth.value, ids: authActive.value }), hasAllAuth: isAllAuth.value, auditInfo: res.data });

            ElMessage.success("权限保存成功");
          });

          break;
        }
        case activeType.resource_group: {
          await setLoggerData({ type: "groups", id: current.value.id as string, beforeIds: resourceGroupIdList.value, afterIds: authActive.value }).then((res) => {
            const { success, message, data } = setRoleDataAuth({ id: current.value.id as string, dataType: "cmdb.resource.group", value: JSON.stringify({ hasAllAuth: isAllAuth.value, ids: authActive.value }), hasAllAuth: isAllAuth.value, auditInfo: res.data });

            ElMessage.success("权限保存成功");
          });

          break;
        }
        case activeType.resource_type: {
          await setLoggerData({ type: "resource_types", id: current.value.id as string, beforeIds: resourceTypeIdList.value, afterIds: authActive.value }).then((res) => {
            const { success, message, data } = setRoleDataAuth({ id: current.value.id as string, dataType: "cmdb.resource.type", value: JSON.stringify({ hasAllAuth: isAllAuth.value, ids: authActive.value }), hasAllAuth: isAllAuth.value, auditInfo: res.data });

            ElMessage.success("权限保存成功");
          });
          break;
        }
        case activeType.completion: {
          const { success, message, data } = await setRoleDataAuth({ id: current.value.id as string, dataType: "ec.completeCode", value: JSON.stringify({ hasAllAuth: isAllAuth.value, ids: authActive.value }), hasAllAuth: isAllAuth.value });
          if (!success) throw Object.assign(new Error(message), { success, data });
          ElMessage.success("权限保存成功");

          break;
        }

        case activeType.alert_classification: {
          await setLoggerData({ type: "alert_classifications", id: current.value.id as string, beforeIds: alarmTypeIdList.value, afterIds: authActive.value }).then((res) => {
            const { success, message, data } = setRoleDataAuth({ id: current.value.id as string, dataType: "cmdb.alert.classification", value: JSON.stringify({ hasAllAuth: isAllAuth.value, ids: authActive.value }), hasAllAuth: isAllAuth.value, auditInfo: res.data });

            ElMessage.success("权限保存成功");
          });
          break;
        }
        case activeType.support_note: {
          const { success, message, data } = await setRoleDataAuth({ id: current.value.id as string, dataType: "cmdb.support.note", value: JSON.stringify({ hasAllAuth: isAllAuth.value, ids: authActive.value }), hasAllAuth: isAllAuth.value });
          if (!success) throw Object.assign(new Error(message), { success, data });
          ElMessage.success("权限保存成功");

          break;
        }
      }
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    } finally {
      await setTimeout(() => {
        getAuthTree(active.value);
      }, 1000);
      // await getAuthTree(active.value);
      isEdit.value = false;
    }
  }
}

const handleStateCreate = inject(handleStateCreateKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateEditor = inject(handleStateEditorKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateDelete = inject(handleStateDeleteKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateCutBasicAuthority = inject(handleStateCutBasicAuthorityKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateRefresh = inject(handleStateRefreshKey, async () => {});
</script>

<style lang="scss" scoped>
.list-item {
  position: relative;

  .move_bar {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    cursor: move;
  }
  &:hover {
    &::before {
      display: block;
    }
  }
  &::before {
    background-color: #666;
    border-radius: 50%;
    box-shadow: 0 6px 0 0 #666, 0 12px 0 0 #666, 0 -6px 0 0 #666, 6px 0 0 0 #3b3b3b, 6px 6px 0 0 #3b3b3b, 6px -6px 0 0 #3b3b3b, 6px 12px 0 0 #3b3b3b;
    content: "";
    display: none;
    flex-shrink: 0;
    width: 2px;
    height: 2px;
    position: absolute;
    top: calc(50% - 4px);
    left: 5px;
  }
}
</style>
<style lang="scss">
.tree_context {
  margin-top: 1em;
  .el-tree-node__content {
    height: fit-content;
    align-items: flex-start;
  }
  &.tree_data_dir {
    --text-color: var(--el-text-color-primary);
  }
  &.tree_data_button {
    --text-color: var(--el-color-primary);
  }
}
.tree_item_group {
  border-top: var(--el-border);
}
</style>
