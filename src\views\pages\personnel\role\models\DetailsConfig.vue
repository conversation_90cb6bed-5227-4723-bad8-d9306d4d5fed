<template>
  <div>
    <div class="tw-flex tw-h-[50px] tw-flex-row tw-items-start">
      <div class="tw-mr-auto"></div>
      <div class="tw-mx-auto"></div>
      <div class="tw-ml-auto">
        <span v-if="userInfo.hasPermission(PERMISSION.role.create)" class="tw-h-fit">
          <el-button type="primary" :icon="Plus" @click="handleStateCreate({})">{{ $t("glob.add") }}角色</el-button>
        </span>
      </div>
    </div>
    <el-table :data="data" :height="height - 50">
      <el-table-column v-for="_col in cols" :key="_col.key" :prop="_col.key as string" :label="_col.label" :min-width="_col.width || 120" :formatter="_col.formatter" />
      <el-table-column :label="t('glob.operate')" align="center" header-align="center" :width="182" fixed="right">
        <template #header="{ column }">
          <!-- <div style="display: flex; justify-content: center"> -->
          <span class="tw-mr-[2.5px]">{{ column.label }}</span>
          <el-link class="tw-ml-[2.5px] tw-align-middle" type="primary" :underline="false" :title="t('glob.refresh')" @click.prevent="handleStateRefresh()"></el-link>
          <!-- </div> -->
        </template>
        <template #default="{ row }">
          <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.role.editor)">
            <span>
              <el-link :disabled="row.global || !userInfo.hasPermission(PERMISSION.role.editor)" type="primary" :underline="false" :title="t('glob.edit')" class="tw-mx-[5px]" @click.prevent="handleStateEditor(row)">{{ $t("glob.edit") }}</el-link>
            </span>
          </el-tooltip>
          <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.role.remove)">
            <span>
              <el-link :disabled="row.global || !userInfo.hasPermission(PERMISSION.role.remove)" type="danger" :underline="false" :title="t('glob.delete')" class="tw-mx-[5px]" @click.prevent="handleStateDelete(row)">{{ $t("glob.delete") }}</el-link>
            </span>
          </el-tooltip>
          <!-- <el-dropdown trigger="click" :disabled="false" @command="$event.callback(row)" class="tw-align-middle">
            <span style="font-size: var(--el-font-size-base)">
              <el-link type="primary" :underline="false" :disabled="false" :title="t('glob.More')" class="tw-mx-[5px]" @click.prevent="">{{ $t("glob.More") }}</el-link>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="userInfo.hasPermission(PERMISSION.role.set_role_type) && row.basic" :command="{ callback: () => handleStateCutBasicAuthority({ ...row, basic: false }) }" class="tw-text-[var(--el-color-danger)]">
                  <span>
                    <el-link type="primary" :underline="false">取消基础角色</el-link>
                  </span>
                </el-dropdown-item>
                <el-dropdown-item v-if="userInfo.hasPermission(PERMISSION.role.set_role_type) && !row.basic" :command="{ callback: () => handleStateCutBasicAuthority({ ...row, basic: true }) }" class="tw-text-[var(--el-color-primary)]">
                  <span>
                    <el-link type="primary" :underline="false">设置基础角色</el-link>
                  </span>
                </el-dropdown-item>

              </el-dropdown-menu>
            </template>
          </el-dropdown> -->
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup generic="T extends import('@/api/personnel').RoleItem, C extends import('./helper').Col<T>">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, readonly, nextTick, inject, h, computed, onMounted, PropType } from "vue";
import { useI18n } from "vue-i18n";
import getUserInfo from "@/utils/getUserInfo";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import { ElMessage } from "element-plus";

import { Refresh, Plus, Edit, Delete, More } from "@element-plus/icons-vue";

import { handleStateCreateKey, handleStateEditorKey, handleStateDeleteKey, handleStateCutBasicAuthorityKey, handleStateRefreshKey } from "./helper";

const { t } = useI18n();
const userInfo = getUserInfo();

interface Props {
  width?: number;
  height?: number;
  title?: string;
  data: T[];
  cols: C[];
  current?: Partial<T>;
  paging: Record<"page" | "size", number>;
}
const props = withDefaults(defineProps<Props>(), { title: "", width: 0, height: 0, current: () => ({}) as Partial<T> });
const width = computed(() => props.width || inject("width", ref(0)).value);
const height = computed(() => props.height || inject("height", ref(0)).value);
const data = computed(() => props.data);
const cols = computed(() => props.cols);
const current = computed(() => props.current);

interface Form {
  [key: string]: any;
}
const form = reactive<Form>({});

const handleStateCreate = inject(handleStateCreateKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateEditor = inject(handleStateEditorKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateDelete = inject(handleStateDeleteKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateCutBasicAuthority = inject(handleStateCutBasicAuthorityKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateRefresh = inject(handleStateRefreshKey, async () => {});
</script>

<style lang="scss" scoped></style>
