<template>
  <!-- 对话框表单 -->

  <!-- width -->
  <FormModel class="editor-for-permission" ref="formRef" :loading="data.loading" :model="form" label-position="top" @submit="handleFinish">
    <FormItem :span="24" :label="`${props.title}`" tooltip="">
      <el-input v-model="filterText" placeholder="请输入需要筛选的权限名称" style="width: 300px" />

      <div style="width: 100%">
        <el-checkbox size="mini" :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange" style="padding: 0px; margin-right: 5px"> 全选</el-checkbox>
      </div>

      <el-tree ref="treeRef" :data="isEdit ? authTree : previewTreeItem" node-key="id" :props="{ class: (data) => `tree_context tree_data_${data.isLeaf ? 'button' : 'dir'}` }" empty-text="应用未配置权限" :show-checkbox="false" class="tw-w-full" :indent="30" default-expand-all :render-after-expand="false" :filter-node-method="filterNode" @check-change="testCheckChange">
        <template #default="{ node, data: authCatalog }: { node: Node, data: TreeData }">
          <div class="node_context_view tw-w-full">
            <div class="el-tree-node__label tw-flex tw-items-center">
              <el-icon class="tw-mr-[3px]"><Pointer v-if="authCatalog.isLeaf"></Pointer><Folder v-else></Folder></el-icon>
              <span :style="{ color: 'var(--text-color)' }">{{ node.label }}</span>
            </div>
            <div v-show="authCatalog.items.length " class="tw-cursor-default" @click.stop>
              <el-row>
                <el-col v-if="isEdit" :span="24" class="tw-flex">
                  <el-checkbox :model-value="authCatalog.items.every((v) => form.permissionIds.includes(v.id))" :disabled="!authCatalog.items.length || authCatalog.disabled" :indeterminate="authCatalog.items.every((v) => form.permissionIds.includes(v.id)) ? false : authCatalog.items.some((v) => form.permissionIds.includes(v.id))" @change="selectAll(authCatalog.items.filter((v) => !v.disabled))" :title="t('glob.All Select')">{{ t("glob.All Select") }}</el-checkbox>
                </el-col>
                <el-col v-for="v in authCatalog.items" :key="`${authCatalog.id}_${v.id}`" :span="24" :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="tw-flex tw-p-[12px]">
                  <div class="list-item tw-flex tw-w-full tw-items-center tw-rounded-[3px] tw-border-[1px] tw-border-solid tw-pl-[16px] tw-pr-[8px]" :color="form.permissionIds.includes(v.id) ? 'tw-border-[var(--el-border-color)]' : 'tw-border-[var(--el-color-primary)]'" :style="{ borderRadius: '3px', padding: '0 8px 0 16px' }">
                    <el-checkbox v-if="isEdit" :model-value="form.permissionIds.includes(v.id)" :disabled="v.disabled || authCatalog.disabled" @change="selectAll([v])" class="tw-w-full tw-overflow-hidden tw-text-ellipsis" :title="v.label">{{ v.label }}</el-checkbox>

                    <div v-else class="tw-w-full tw-overflow-hidden tw-text-ellipsis tw-leading-[32px]" :title="v.label">{{ v.label }}</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </template>
      </el-tree>
    </FormItem>
  </FormModel>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, computed, h, renderSlot, getCurrentInstance, createVNode, shallowRef, watch } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep, find, findIndex } from "lodash-es";
import { ElCheckbox, ElCol, ElEmpty, ElIcon, ElMessage, ElMessageBox, ElRow, ElTree } from "element-plus";
import { buildTypeHelper } from "@/utils/type";
import { useConfig } from "@/stores/config";
import moment from "moment";

import { buildValidatorData } from "@/utils/validate";

import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import Timezone from "@/components/formItem/timezone/index.vue";

import getUserInfo from "@/utils/getUserInfo";

import { getSystemVersion } from "@/api/system";

import { locales, localesOption } from "@/api/locale";
import { Zone as zones } from "@/utils/zone";

import { priority, priorityOption } from "@/views/pages/apis/event";
import { TenantItem, getTenantByPermission } from "@/api/personnel";
import type Node from "element-plus/es/components/tree/src/model/node";
import { Pointer, Folder } from "@element-plus/icons-vue";

const userInfo = getUserInfo();
const config = useConfig();
const { t } = useI18n({ useScope: "global" });
const formRef = ref<InstanceType<typeof FormModel>>();
const ctx = getCurrentInstance();
if (!ctx) throw new Error("Component context initialization failed!");
const { appContext } = ctx;

interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 120,
});

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Item {
  tenantId: string;
  appId: string;
  appAssigned: boolean;
  permissionIds: string[];
}

interface TreeData {
  label: string;
  id: string;
  items: TreeData[];
  children: TreeData[];
  disabled: boolean;
  order: number;
  isLeaf: boolean;
  parentId?: string | null;
  appId: string;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const treeRef = ref<InstanceType<typeof ElTree>>();
const isEdit = ref(true);
const authTree = ref<TreeData[]>([]);

const allPreviewTree = ref([]);
const previewTreeItem = computed((): TreeData[] => {
  const hasTreeEffectiveItem = (items: TreeData[]) => {
    for (let i = 0; i < items.length; i++) {
      if (items[i].items.length) return true;
      else if (hasTreeEffectiveItem(items[i].children)) return true;
    }
    return false;
  };
  const subHasTreeItem = (parent: TreeData[], item: TreeData) => {
    const childrenItem: TreeData = { label: item.label, id: item.id, order: item.order, children: [], items: item.items.filter((v) => form.value.permissionIds.includes(v.id)), disabled: item.disabled, isLeaf: item.isLeaf, parentId: item.parentId, appId: item.appId };
    for (let i = 0; i < item.children.length; i++) {
      subHasTreeItem(childrenItem.children, item.children[i]);
    }
    if (hasTreeEffectiveItem([childrenItem])) parent.push(childrenItem);
    return parent;
  };
  return authTree.value.reduce(subHasTreeItem, [] as TreeData[]);
});
/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  tenantId: buildTypeHelper<Required<Item>["tenantId"]>(""),
  appId: buildTypeHelper<Required<Item>["appId"]>(""),
  appAssigned: buildTypeHelper<Required<Item>["appAssigned"]>(true),
  permissionIds: buildTypeHelper<Required<Item>["permissionIds"]>([]),
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  // await (async () => {
  // })();
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  authTree.value = [];
  form.value.permissionIds = [];
  if (!params) return;
  if (!params.tenantId) throw new Error("未找到租户信息");
  if (!params.appId) throw new Error("未找到应用信息");

  const { success, message, data: permissions } = await getTenantByPermission({ tenantId: <string>params.tenantId, appIds: [params.appId].join(",") });
  if (!success) throw Object.assign(new Error(message), { success, data: permissions });
  const data = find(permissions instanceof Array ? permissions : [], (v) => v.app.id === params.appId);
  if (data) {
    const catalogs = (data.catalogs instanceof Array ? data.catalogs : []).filter((v) => v.enabled);
    const permissions = (data.permissions instanceof Array ? data.permissions : []).filter((v) => v.enabled);
    allPreviewTree.value = data.permissions;
    const helperCatalogs = new Map<string | null, TreeData>();
    for (let i = 0; i < catalogs.length; i++) {
      helperCatalogs.set(catalogs[i].id || null, { label: catalogs[i].name, id: catalogs[i].id, children: [], items: [], order: Number(catalogs[i].orderNum) || 0, disabled: !catalogs[i].enabled, isLeaf: false, parentId: catalogs[i].parentId || null, appId: catalogs[i].appId });
    }
    const helperPermissions = new Map<string | null, TreeData[]>();
    for (let i = 0; i < permissions.length; i++) {
      if (!helperPermissions.has(permissions[i].catalogId || null)) helperPermissions.set(permissions[i].catalogId || null, []);
      const item = helperPermissions.get(permissions[i].catalogId || null)!;
      item.push({ label: permissions[i].name, id: permissions[i].id, children: [], items: [], order: Number(permissions[i].orderNum) || 0, disabled: !permissions[i].enabled, isLeaf: true, appId: permissions[i].appId });
    }

    const iterator = helperCatalogs.keys();
    let index: IteratorResult<string | null>;
    while (!(index = iterator.next()).done) {
      const item = helperCatalogs.get(index.value);
      if (!item) continue;
      if (helperPermissions.has(index.value)) {
        item.items.splice(0, item.items.length, ...(helperPermissions.get(index.value) || []));
        item.items.sort((a, b) => a.order - b.order);
      }
      if (item.parentId) {
        const parent = helperCatalogs.get(item.parentId);
        if (parent) {
          if ("parentId" in item) delete item.parentId;
          parent.children.push(item);
          parent.children.sort((a, b) => a.order - b.order);
        }
      }
    }
    helperCatalogs.forEach((v) => {
      if ("parentId" in v) {
        delete v.parentId;
        authTree.value.push(v);
        authTree.value.sort((a, b) => a.order - b.order);
      }
    });

    await nextTick();
    form.value.permissionIds = data.assignedPermissionIds instanceof Array ? data.assignedPermissionIds : [];
  }
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  return form;
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
function selectAll(items: TreeData[]) {
  const select = items.every((v) => form.value.permissionIds.includes(v.id));
  if (select) form.value.permissionIds = Array.from(items.reduce((p, c) => (p.delete(c.id), p), new Set(form.value.permissionIds)));
  else form.value.permissionIds = Array.from(items.reduce((p, c) => (p.add(c.id), p), new Set(form.value.permissionIds)));
}
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 窗口方法
 */
interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<Item>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Record<string, unknown>) => void;
  reject?: (value: Error) => void;
  callback?: (form: Record<string, unknown>) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Record<string, unknown>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(function (formResult, [key, util]) {
    if (!util) return formResult;
    else if (util.test(formResult[key])) return formResult;
    else return Object.assign(formResult, { [key]: util.transfer(formResult[key], util.value as never) });
  }, form as Required<Item>);
}

async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => (formRef.value ? formRef.value.validate(resolve) : resolve(false)));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;

  form.value = await getForm($params.value);

  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
  checkAll.value == true;
}

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();

interface Tree {
  [key: string]: any;
}
const filterText = ref("");
watch(filterText, (val) => {
  treeRef.value!.filter(val);
});

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.label.includes(value);
};

let checkAll = ref(false); //全选按钮的绑定值
let isIndeterminate = ref(false); //全选按钮的全选，半选样式

// //全选按钮勾上的方法事件
function handleCheckAllChange(val) {
  if (checkAll.value == true) {
    //如果是当前值是全选，依次遍历节点设置勾选，同时过滤的disabled为true的

    form.value.permissionIds = allPreviewTree.value.map(function (item, index, array) {
      // 处理函数作为参数

      return item.id;
    });
  } else {
    //取消全选时置空
    form.value.permissionIds = [];
  }
}

defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, callback?: (form: Record<string, unknown>) => Promise<void>): Promise<unknown> {
    if (data.visible) handleCancel();
    const wait = new Promise<Record<string, unknown>>((resolve, reject) => ((data.resolve = resolve), (data.reject = reject)));
    $params.value = cloneDeep(params);
    data.visible = true;
    data.loading = true;
    data.submitLoading = true;
    data.callback = callback;
    await nextTick();
    try {
      await runningInit($params.value);
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      handleCancel();
    }
    handleReset();
    data.loading = false;
    data.submitLoading = false;

    try {
      return await wait;
    } catch (error) {
      return error;
    }
  },
  async confirm<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.confirm(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async alert<T extends { $title: string; $type: "" | "success" | "warning" | "info" | "error"; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.alert(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
});
</script>

<style lang="scss" scoped>
.list-item {
  position: relative;

  // .move_bar {
  //   position: absolute;
  //   top: 0px;
  //   left: 0px;
  //   right: 0px;
  //   cursor: move;
  // }
  // &:hover {
  //   &::before {
  //     display: block;
  //   }
  // }
  // &::before {
  //   background-color: #666;
  //   border-radius: 50%;
  //   box-shadow: 0 6px 0 0 #666, 0 12px 0 0 #666, 0 -6px 0 0 #666, 6px 0 0 0 #3b3b3b, 6px 6px 0 0 #3b3b3b, 6px -6px 0 0 #3b3b3b, 6px 12px 0 0 #3b3b3b;
  //   content: "";
  //   display: none;
  //   flex-shrink: 0;
  //   width: 2px;
  //   height: 2px;
  //   position: absolute;
  //   top: calc(50% - 4px);
  //   left: 5px;
  // }
}

.editor-for-permission {
  :deep(.tree_context) {
    margin-top: 1em;
    .el-tree-node__content {
      height: fit-content;
      align-items: flex-start;
    }
    &.tree_data_dir {
      --text-color: var(--el-text-color-primary);
    }
    &.tree_data_button {
      --text-color: var(--el-color-primary);
    }
  }
  :deep(.tree_item_group) {
    border-top: var(--el-border);
  }
}
</style>
