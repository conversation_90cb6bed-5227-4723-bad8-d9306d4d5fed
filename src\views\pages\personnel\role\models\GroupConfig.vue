<template>
  <div>
    <div class="tw-mb-[18px] tw-flex tw-h-[32px]">
      <div class="tw-ml-auto">
        <span v-if="userInfo.hasPermission(PERMISSION.group.assigning_roles)">
          <el-button type="primary" :icon="EditPen" size="default" @click="handleAddUserGroup">{{ t("glob.allocation") }}{{ title }}</el-button>
        </span>
      </div>
    </div>
    <el-table v-loading="loading" :data="rawList" :height="height">
      <el-table-column prop="name" label="用户组名称" :min-width="80" />
      <el-table-column prop="note" label="描述" :min-width="120" />
      <el-table-column prop="createdTime" label="创建时间" :min-width="140" :formatter="(_row: unknown, _col: unknown, v: string) => (v ? moment(v, 'x').format('YYYY-MM-DD HH:mm') : '--')" />
      <el-table-column :label="t('glob.operate')" align="left" header-align="left" :width="80" fixed="right">
        <template #header="{ column }">
          <!-- <div style="display: flex; justify-content: center"> -->
          <span class="tw-mr-[2.5px]">{{ column.label }}</span>
          <el-link class="tw-ml-[2.5px] tw-align-middle" type="primary" :underline="false" :title="t('glob.refresh')" @click.prevent="getRawList(current)"></el-link>
          <!-- </div> -->
        </template>
        <template #default="{ row }">
          <span v-if="userInfo.hasPermission(PERMISSION.group.assigning_roles)">
            <el-link class="tw-mx-[2.5px] tw-align-middle" type="primary" :underline="false" :title="t('glob.remove')" @click.prevent="delRawRole(row, current.id as string)">{{ t("glob.remove") }}</el-link>
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup generic="T extends import('@/api/personnel').RoleItem, C extends import('./helper').Col<T>">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, readonly, nextTick, inject, h, computed, onMounted, watch } from "vue";
import { useI18n } from "vue-i18n";
import { Col } from "./helper";
import moment from "moment";
import getUserInfo from "@/utils/getUserInfo";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import { bindFormBox } from "@/utils/bindFormBox";
import { ElMessage, ElFormItem, ElSelect, ElOption } from "element-plus";
import { useSiteConfig } from "@/stores/siteConfig";

import { Refresh, SemiSelect, Edit, Delete, More, EditPen } from "@element-plus/icons-vue";

import { handleStateCreateKey, handleStateEditorKey, handleStateDeleteKey, handleStateCutBasicAuthorityKey, handleStateRefreshKey } from "./helper";

import { getGroupByRole, getRoleByGroup, setGroupByRole, getGroupList, type GroupItem, setRoleUserGroup } from "@/api/personnel";

// import { type UserGroupsOrgCurrent, getUserGroupByPage } from "@/views/pages/apis/userGroup";

const { t } = useI18n();
const siteConfig = useSiteConfig();
const userInfo = getUserInfo();

interface Props {
  width?: number;
  height?: number;
  title?: string;
  data: T[];
  cols: C[];
  current?: Partial<T>;
  paging: Record<"page" | "size", number>;
}
const props = withDefaults(defineProps<Props>(), { title: "", width: 0, height: 0, current: () => ({}) as Partial<T> });
const width = computed(() => props.width || inject("width", ref(0)).value);
const height = computed(() => props.height || inject("height", ref(0)).value);
const data = computed(() => props.data);
const cols = computed(() => props.cols);
const current = computed(() => props.current);

interface Form {
  [key: string]: any;
}
const loading = ref(false);
const rawList = ref<import("@/api/personnel").GroupItem[]>([]);
const form = reactive<Form>({});

onMounted(() => {
  watch(
    current,
    async (current) => {
      if (!current) return;
      await getRawList(current);
    },
    { immediate: true }
  );
});

async function getRawList(current: Partial<T>) {
  if (!current.id) return;
  try {
    loading.value = true;
    await nextTick();
    const { success, message, data } = await getGroupByRole({ id: current.id as string });
    if (!success) throw Object.assign(new Error(message), { success, data });
    rawList.value = data instanceof Array ? data : [];
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}
async function delRawRole(raw: import("@/api/personnel").GroupItem, role: string) {
  try {
    loading.value = true;
    await nextTick();
    const { success: rawSuccess, message: rawMessage, data: rawData } = await getRoleByGroup({ id: raw.id || "", appId: (siteConfig.baseInfo || {}).app || "" });
    if (!rawSuccess) throw Object.assign(new Error(rawMessage), { success: rawSuccess, data: rawData });
    const roleList: string[] = [];
    for (let i = 0; i < (rawData instanceof Array ? rawData : []).length; i++) {
      const item = (rawData instanceof Array ? rawData : [])[i];
      if (item.id !== role) roleList.push(item.id || "");
    }
    const { app } = siteConfig.baseInfo!;
    const { success, message, data } = await setGroupByRole({ appId: app, userGroupIds: [raw.id || ""], roleIds: roleList });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`成功从角色中${t("glob.remove")}用户组！`);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loading.value = false;
    await getRawList(current.value);
  }
}

// 分配用户组
async function handleAddUserGroup() {
  const form = reactive<Record<"title", string> & { userGroupIds: string[] }>({
    title: "分配用户组",
    userGroupIds: [],
  });
  const { id: roleId, name: roleName } = current.value || "";
  const userGroups: GroupItem[] = [] as GroupItem[];
  try {
    const { success, data, message } = await getGroupList({ appId: (siteConfig.baseInfo || {}).app, external: true });
    if (!success) throw new Error(message);
    userGroups.push(...(data instanceof Array ? data : []));
    const existUserGroup: string[] = rawList.value.map((v) => v.id);
    await bindFormBox([h(ElFormItem, { rules: [{ required: false, type: "array", message: "请选择用户组", trigger: "blur" }], prop: "userGroupIds", label: roleName }, () => h(ElSelect, { "modelValue": form.userGroupIds, "onUpdate:modelValue": ($event) => (form.userGroupIds = $event), "multiple": true, "clearable": true, "collapseTags": false, "collapseTagsTooltip": true, "filterable": true, "style": { width: "100%" } }, () => userGroups.filter((v) => !existUserGroup.includes(v.id)).map((v) => h(ElOption, { label: v.name!, value: v.id! }))))], form, async () => {
      const { success: submitSuccess, data: submitData, message: submitMessage } = await setRoleUserGroup({ roleId, userGroupIds: form.userGroupIds });
      if (!submitSuccess) throw new Error(submitMessage);
      ElMessage.success(`角色${t("glob.allocation")}用户组成功！`);
      await getRawList(current.value);
      return { success: submitSuccess, message: submitMessage };
    });
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

const handleStateCreate = inject(handleStateCreateKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateEditor = inject(handleStateEditorKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateDelete = inject(handleStateDeleteKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateCutBasicAuthority = inject(handleStateCutBasicAuthorityKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateRefresh = inject(handleStateRefreshKey, async () => {});
</script>

<style lang="scss" scoped></style>
