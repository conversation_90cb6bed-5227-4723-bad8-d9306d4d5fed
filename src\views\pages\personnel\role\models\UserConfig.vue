<template>
  <div>
    <div class="tw-mb-[18px] tw-flex tw-h-[32px]">
      <div class="tw-ml-auto">
        <span v-if="userInfo.hasPermission(PERMISSION.group.assigning_roles)">
          <el-button type="primary" :icon="EditPen" size="default" @click="handleAddUser">{{ t("glob.allocation") }}{{ title }}</el-button>
        </span>
      </div>
    </div>
    <el-table v-loading="loading" :data="userList" :height="height">
      <el-table-column prop="name" label="姓名" :min-width="80" />
      <el-table-column prop="account" label="账号" :min-width="120">
        <template #default="{ row }">
          <span>
            {{ row.account + `@${row.tenantAbbreviation}` }}
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="phone" label="手机号" :min-width="120" />
      <el-table-column prop="email" label="邮箱" :min-width="120" />
      <el-table-column prop="createdTime" label="注册时间" :min-width="140" :formatter="(_row: unknown, _col: unknown, v: string) => (v ? moment(v, 'x').format('YYYY-MM-DD HH:mm') : '--')" />
      <el-table-column :label="t('glob.operate')" align="left" header-align="left" :width="80" fixed="right">
        <template #header="{ column }">
          <!-- <div style="display: flex; justify-content: center"> -->
          <span class="tw-mr-[2.5px]">{{ column.label }}</span>
          <el-link class="tw-ml-[2.5px] tw-align-middle" type="primary" :underline="false" :title="t('glob.refresh')" @click.prevent="getUserList(current)"></el-link>
          <!-- </div> -->
        </template>
        <template #default="{ row }">
          <span v-if="userInfo.hasPermission(PERMISSION.user.assigning_roles)">
            <el-link class="tw-mx-[2.5px] tw-align-middle" type="primary" :underline="false" :title="t('glob.remove')" @click.prevent="delUserRole(row, current.id as string)">{{ t("glob.remove") }}</el-link>
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup generic="T extends import('@/api/personnel').RoleItem, C extends import('./helper').Col<T>">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, readonly, nextTick, inject, h, computed, onMounted, watch } from "vue";
import { useI18n } from "vue-i18n";
import { Col } from "./helper";
import moment from "moment";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import { ElMessage, ElFormItem, ElSelect, ElOption } from "element-plus";
import { useSiteConfig } from "@/stores/siteConfig";
import { bindFormBox } from "@/utils/bindFormBox";
import getUserInfo from "@/utils/getUserInfo";

import { Refresh, SemiSelect, Edit, Delete, More, EditPen } from "@element-plus/icons-vue";

import { handleStateCreateKey, handleStateEditorKey, handleStateDeleteKey, handleStateCutBasicAuthorityKey, handleStateRefreshKey } from "./helper";

import { getUserByRole, getRoleByUser, setUserByRole, setRoleUser, getUser, UserItemScope } from "@/api/personnel";

const { t } = useI18n();
const siteConfig = useSiteConfig();
const userInfo = getUserInfo();

interface Props {
  width?: number;
  height?: number;
  title?: string;
  data: T[];
  cols: C[];
  current?: Partial<T>;
  paging: Record<"page" | "size", number>;
}
const props = withDefaults(defineProps<Props>(), { title: "", width: 0, height: 0, current: () => ({}) as Partial<T> });
const width = computed(() => props.width || inject("width", ref(0)).value);
const height = computed(() => props.height || inject("height", ref(0)).value);
const data = computed(() => props.data);
const cols = computed(() => props.cols);
const current = computed(() => props.current);

interface Form {
  [key: string]: any;
}
const loading = ref(false);
const userList = ref<import("@/api/personnel").UserItem[]>([]);
const form = reactive<Form>({});

onMounted(() => {
  watch(
    current,
    async (current) => {
      if (!current) return;
      await getUserList(current);
    },
    { immediate: true }
  );
});

async function getUserList(current: Partial<T>) {
  if (!current.id) return;
  try {
    loading.value = true;
    await nextTick();
    const { success, message, data } = await getUserByRole({ id: current.id as string });
    if (!success) throw Object.assign(new Error(message), { success, data });
    userList.value = data instanceof Array ? data : [];
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}
async function delUserRole(user: import("@/api/personnel").UserItem, role: string) {
  try {
    loading.value = true;
    await nextTick();
    const { success: userSuccess, message: userMessage, data: userData } = await getRoleByUser({ id: user.userId, appId: (siteConfig.baseInfo || {}).app || "" });
    if (!userSuccess) throw Object.assign(new Error(userMessage), { success: userSuccess, data: userData });
    const roleList: string[] = [];
    for (let i = 0; i < (userData instanceof Array ? userData : []).length; i++) {
      const item = (userData instanceof Array ? userData : [])[i];
      if (item.id !== role) roleList.push(item.id || "");
    }
    const { app } = siteConfig.baseInfo!;
    const { success, message, data } = await setUserByRole({ appId: app, userIds: [user.userId], roleIds: roleList });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`成功从角色中${t("glob.remove")}用户！`);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loading.value = false;
    await getUserList(current.value);
  }
}

// 分配用户组
async function handleAddUser() {
  const form = reactive<Record<"title", string> & { userIds: string[] }>({
    title: "分配用户",
    userIds: [],
  });
  const { id: roleId, name: roleName } = current.value || "";

  type UserItme = import("@/api/personnel").UserItem;
  const users: UserItme[] = [];
  try {
    const { success, data, message } = await getUser({ scope: "ALL", paging: { pageNumber: 1, pageSize: 999999 } });
    if (!success) throw new Error(message);
    users.push(...(data instanceof Array ? data : []));
    const existUser: string[] = userList.value.map((v) => v.userId);
    await bindFormBox([h(ElFormItem, { rules: [{ required: false, type: "array", message: "请选择用户", trigger: "blur" }], prop: "userIds", label: roleName }, () => h(ElSelect, { "modelValue": form.userIds, "onUpdate:modelValue": ($event) => (form.userIds = $event), "multiple": true, "clearable": true, "collapseTags": false, "collapseTagsTooltip": true, "filterable": true, "style": { width: "100%" } }, () => users.filter((v) => !existUser.includes(v.id)).map((v) => h(ElOption, { label: v.name + ` (${v.account}@${v.tenantAbbreviation})` || "", value: v.id! }))))], form, async () => {
      const { success: submitSuccess, data: submitData, message: submitMessage } = await setRoleUser({ roleId, userIds: form.userIds });
      if (!submitSuccess) throw new Error(submitMessage);
      ElMessage.success(`角色${t("glob.allocation")}用户成功！`);
      await getUserList(current.value);
      return { success: submitSuccess, message: submitMessage };
    });
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}

const handleStateCreate = inject(handleStateCreateKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateEditor = inject(handleStateEditorKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateDelete = inject(handleStateDeleteKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateCutBasicAuthority = inject(handleStateCutBasicAuthorityKey, async (_raw: Partial<T> & Record<string, unknown>) => {});
const handleStateRefresh = inject(handleStateRefreshKey, async () => {});
</script>

<style lang="scss" scoped></style>
