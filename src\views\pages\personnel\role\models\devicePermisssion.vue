<template>
  <div class="devicePermission">
    <!-- <el-checkbox v-model="isAllAuth" border>全部权限</el-checkbox> -->
    <!-- {{ props.isAllAuth }} -->
    <div class="devicePermission-list">
      <div class="devicePermission-list-area" v-if="props.isEdit">
        <el-scrollbar :height="height - 250">
          <h3>按区域分配</h3>
          <p>按区域分配设备，则该区域下子区域，场所下的设备将全部分配，且不单独可以取消分配；</p>
          <el-tree ref="treeRef" :data="authTree" :default-checked-keys="allTreeList" :props="{ children: 'children', label: 'label', id: 'id', disabled: (data) => data.path.some((v) => authTreeSelect.includes(v)) }" node-key="id" empty-text="应用未配置权限" :show-checkbox="!props.isAllAuth" class="tw-w-full" :indent="30" default-expand-all @check="handleChange"> </el-tree>
        </el-scrollbar>
      </div>
      <div class="devicePermission-list-location" v-if="props.isEdit">
        <el-scrollbar :height="height - 250">
          <h3>按场所分配</h3>
          <p>按照场所分配设备，则该场所下的设备将全部分配，且不单独可以取消分配；</p>
          <div class="location-list">
            <div v-for="(item, index) in tableLocationData" :key="item.id">
              <el-checkbox :disabled="item.disabled" v-model="item.check" :true-label="`true-${item.id}`" :false-label="`false-${item.id}`" @change="locationChange"></el-checkbox>
              {{ item.name }}
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div class="devicePermission-list-device" v-if="props.isEdit">
        <el-scrollbar :height="height - 250">
          <h3>设备单独分配</h3>
          <p>若对某设备有特殊分配需求，请单独分配设备且不可以对改设备所在区域和场所有其他分配设置</p>
          <div>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-input v-model="form.name" placeholder="请输入设备名称" @keyup.enter="queryData([], [], [])"></el-input>
              </el-col>
              <el-col :span="8">
                <el-cascader v-model="form.regionId" :options="regionTree" @change="queryData([], [], [])" placeholder="请选择区域" :props="{ checkStrictly: true, emitPath: false, multiple: false, value: 'id', label: 'name' }" separator=" > " clearable filterable class="tw-w-full"></el-cascader>
              </el-col>
              <el-col :span="8">
                <el-select v-model="form.locationId" placeholder="请选择场所" clearable filterable class="tw-w-full" @change="queryData([], [], [])">
                  <el-option v-for="item in locationList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
              </el-col>
            </el-row>
          </div>
          <div class="device-list">
            <div v-for="item in deviceList" :key="item.id">
              <el-checkbox :disabled="item.disabled" v-model="item.check"></el-checkbox>
              {{ item.name }} ({{ item.config?.ipAddress }})
            </div>
          </div>
          <!-- <el-pagination class="tw-mt-[18px]" @size-change="queryData([])" @current-change="queryData([])" v-model:currentPage="paging.pageNumber" :page-sizes="[10, 20, 30, 50, 100]" v-model:page-size="paging.pageSize" layout="->, total, sizes, prev, pager, next, jumper" :total="paging.total"></el-pagination> -->
        </el-scrollbar>
      </div>

      <div class="devicePermission-list-device-show" v-if="!props.isEdit">
        <el-scrollbar :height="height - 250">
          <h3 v-if="showDeivceList.length > 0">设备权限</h3>

          <div class="device-list">
            <div v-for="item in showDeivceList" :key="item.id">
              <el-checkbox :disabled="item.disabled" v-model="item.check"></el-checkbox>
              {{ item.name }} ({{ item.config?.ipAddress }})
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h } from "vue";
import { ElMessage, ElMessageBox, ElText, ElTree } from "element-plus";
import { type SlaConfigList as DataItem } from "@/views/pages/apis/deviceManage";
import { getRegionTree } from "@/views/pages/apis/regionManage";
import type Node from "element-plus/es/components/tree/src/model/node";
import { getLocationsTenantCurrent } from "@/views/pages/apis/locationManang";
import { getDeviceList as getItemList } from "@/views/pages/apis/device";

interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
}

const props = defineProps({
  isAllAuth: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});
interface TreeData {
  label: string;
  id: string;
  items: TreeData[];
  children: TreeData[];
  disabled: boolean;
  order: number;
  isLeaf: boolean;
  parentId?: string | null;
  appId: string;
}

const defaultProps = {
  children: "children",
  label: "label",
  id: "id",
};

const form = ref({
  name: "",
  locationId: "",
  regionId: "",
});

const regionTree = ref([]);
const locationList = ref([]);
const deviceList = ref([]);
const tableLocationData = ref([]);
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const treeRef = ref<InstanceType<typeof ElTree>>();
const height = inject("height", ref(0));
const authTree = ref<TreeData[]>([]);
const showDeivceList = ref([]);
const areaAndLocationList = ref([]);
function beforeCreate() {}
function created() {
  // handleRefreshRegionTable();
  // handleRefreshLocationTable();
  // queryData([]);
}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}

const paging = reactive({
  pageNumber: 1,
  pageSize: 30,
  total: 0,
});

const allTreeList = ref([]);
const locationIds = ref([]);
const chooseDeviceList = ref([]);
const chooseLocatinoList = ref([]);
const chooseRegionList = ref([]);
watch(
  () => props.isAllAuth,
  (newProps: any) => {
    if (newProps) {
      allTreeList.value = getAllIds(authTree.value, []);
      tableLocationData.value.map((v) => {
        v.check = true;
        v.disabled = true;
      });
      deviceList.value.map((v) => {
        v.check = true;
        v.disabled = true;
      });
    } else {
      const $treeRef = treeRef.value;
      if (!$treeRef) return;
      nextTick(async () => {
        await $treeRef.$nextTick;
        $treeRef.setCheckedKeys([]);
      });
      tableLocationData.value.map((v) => {
        v.check = false;
        v.disabled = false;
      });
      deviceList.value.map((v) => {
        v.check = false;
        v.disabled = false;
      });
    }
  },
  {
    deep: true,
  }
);

watch(
  deviceList,
  (newValue) => {
    chooseDeviceList.value = [];
    newValue.forEach((v, i) => {
      if (v.check) {
        chooseDeviceList.value.push(v.id);
      }
    });
  },
  {
    deep: true,
  }
);

function getAllIds(tree, result) {
  //遍历树  获取id数组
  for (const i in tree) {
    result.push(tree[i].id); // 遍历项目满足条件后的操作
    if (tree[i].children) {
      //存在子节点就递归
      getAllIds(tree[i].children, result);
    }
  }
  return result;
}
function locationChange(val, type) {
  // // console.log(type,6666)
  const selectId = val.substring(val.indexOf("-") + 1);
  if (type != "area") {
    chooseLocatinoList.value.push(selectId);
  }

  deviceList.value.forEach((data) => {
    if (data.locationId === selectId || data.regionId === selectId) {
      data.disabled = val.includes("true");
      data.check = val.includes("true");
    }
  });
}
function handleRefreshLocationTable(ids) {
  const params = {
    pageNumber: 1,
    pageSize: 300,
  };

  getLocationsTenantCurrent(params).then(({ success, data, total }) => {
    if (success) {
      tableLocationData.value = data.map((v) => Object.assign(v, { disabled: props.isAllAuth, check: props.isAllAuth }));
      locationList.value = data;
      tableLocationData.value.forEach((item) => {
        ids.forEach((v) => {
          if (item.id === v) {
            item.check = true;
            // disabled: true,
          }
        });
      });
    }
  });
}

const authTreeSelect = ref([]);
async function handleRefreshRegionTable(ids) {
  const { success, data, message, page, size, total } = await getRegionTree({ pageNumber: 1, pageSize: 10000, parentId: "-1", sort: "createdTime,desc" });

  if (!success) throw new Error(message);
  authTree.value = setTreeDepth(data instanceof Array ? data : []);
  if (props.isAllAuth) {
    allTreeList.value = getAllIds(authTree.value, []);
  } else {
    const $treeRef = treeRef.value;
    if (!$treeRef) return;
    nextTick(async () => {
      await $treeRef.$nextTick;
      allTreeList.value = [...ids];
      $treeRef.setCheckedKeys([...ids]);
    });
  }

  regionTree.value = data;
}
// function setTreeDepth(data: DataItem[], depth = 0) {
//   return data.map((v) => Object.assign(v, { label: v.name }, { depth, children: setTreeDepth(v.children instanceof Array ? v.children : [], depth + 1) }));
// }
function setTreeDepth(data: DataItem[], path = <string[]>[]) {
  return data.map((v) => Object.assign(v, { label: v.name, checked: true }, { depth: path.length, path, children: setTreeDepth(v.children instanceof Array ? v.children : [], path.concat(v.id)) }));
}

async function queryData(ids, locationIds, regionIds) {
  const [{ success, message, data, page, size, total } /* , { success: contactGroupSuccess, message: contactGroupMessage, data: contactGroupData } */] = await Promise.all([
    /* 获取设备列表 */
    getItemList({ ...form.value, paging: { pageNumber: paging.pageNumber, pageSize: 1000000 } }),
    /* 联系人 */
    // getContactByContactGroup({}),
  ]);

  if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
  // // console.log(data, 666666);
  deviceList.value = data.map((v) => Object.assign(v, { disabled: props.isAllAuth, check: props.isAllAuth }));
  showDeivceList.value = [];

  if (ids.length > 0 && !props.isAllAuth) {
    deviceList.value.forEach((item) => {
      ids.forEach((v) => {
        if (item.id === v) {
          showDeivceList.value.push({
            ...item,
            check: true,
            disabled: true,
          });

          item.check = true;
          // disabled: true,
        }
      });
      [...regionIds].forEach((v) => {
        if (item.regionId === v) {
          item.check = true;
          item.disabled = true;
          // disabled: true,
        }
      });
      [...locationIds].forEach((v) => {
        if (item.locationId === v) {
          item.check = true;
          item.disabled = true;
          // disabled: true,
        }
      });
    });
  } else {
    showDeivceList.value = [];
  }
  paging.total = total * 1;
}

function handleChange(data: any, chooseNodes: any) {
  authTreeSelect.value = chooseNodes.checkedNodes.map((v) => v.id);

  if (chooseNodes.checkedNodes.includes(data)) {
    /* OK */
  } else {
    const getChiden = (list) => list.reduce((p, c) => p.concat([c.id, ...getChiden(c.children)]), []);
    const checked = getChiden(data.children);
    for (const item of checked) {
      const index = authTreeSelect.value.indexOf(item);
      if (index !== -1) authTreeSelect.value.splice(index, 1);
    }
  }

  const $treeRef = treeRef.value;
  if (!$treeRef) return;
  nextTick(async () => {
    await $treeRef.$nextTick;
    $treeRef.setCheckedKeys(authTreeSelect.value);
  });
  areaAndLocationList.value = [...authTreeSelect.value];
  // // console.log(areaAndLocationList.value);
  authTreeSelect.value.forEach((v, i) => {
    locationList.value.forEach((item) => {
      if (v === item.regionId) {
        areaAndLocationList.value.push(item.id);
      }
    });
  });
  let ids = getAllIds(authTree.value, []);
  if (authTreeSelect.value.length > 0) {
    // locationChange()

    let arr = getArrDifference(ids, authTreeSelect.value);
    arr.forEach((v, i) => {
      locationChange("false-" + v, "area");
    });
    authTreeSelect.value.forEach((v, i) => {
      locationChange("true-" + v, "area");
    });
  } else {
    // // console.log(12346798, ids);
    ids.forEach((v, i) => {
      locationChange("false-" + v, "area");
    });
  }
}
function getArrDifference(arr1, arr2) {
  return arr1.concat(arr2).filter(function (v, i, arr) {
    return arr.indexOf(v) === arr.lastIndexOf(v);
  });
}

// 更新树形数据的状态
function updateTreeDataStatus(data) {
  let treeData = authTree.value;
  // 直接把所有节点置为可选，避免父节点取消选择了，子节点还不可选的情况
  setChildrenDisabled(treeData, false);
  // 根据上面选择的节点，把不可选的子节点及所有后代节点置为不可选
  data.map((item) => {
    let sub = filter(treeData, (node) => {
      return node.value == item;
    });
    if (sub.children) {
      const subChild = setChildrenDisabled(sub.children, true);
      sub.children = subChild;
    }
  });
}
//递归设置子级不可选择
function setChildrenDisabled(tree: any, status: any) {
  tree.map((item) => {
    //  $set(item, "disabled", status);
    item.disabled = status;
    if (item.children) {
      setChildrenDisabled(item.children, status);
    }
  });
  return tree;
}

function filter(treeList: any, callback: any) {
  const queue = [...treeList];
  while (queue.length > 0) {
    const cur = queue.shift();
    if (callback(cur)) {
      return cur;
    }
    if (cur.children && cur.children.length > 0) {
      for (const item of cur.children) {
        queue.push(item);
      }
    }
  }
}
function open(ids, regionIds, locationIds) {
  handleRefreshRegionTable(regionIds);
  handleRefreshLocationTable(locationIds);
  queryData(ids, locationIds, regionIds);
}

beforeCreate();
nextTick(created);

defineExpose({ chooseDeviceList, open, authTreeSelect, chooseLocatinoList });
</script>
<style lang="scss" scoped>
.devicePermission-list {
  width: 100%;
  height: 100%;
  display: flex;
  h3 {
    font-weight: 500;
    font-size: 16px;
  }
  p {
    color: #aaa;
  }
  > .devicePermission-list-area,
  .devicePermission-list-location {
    border: 1px solid #777;
    padding: 10px;
    box-sizing: border-box;
    width: 25%;
    margin-right: 10px;

    .location-list {
      display: flex;
      flex-wrap: wrap;
      > div {
        flex: none;
        width: 50%;
      }
    }
  }
  .devicePermission-list-device {
    flex: 1;
    padding: 10px;
    box-sizing: border-box;
    border: 1px solid #777;
    .device-list {
      display: flex;
      flex-wrap: wrap;
      > div {
        flex: none;
        width: 50%;
      }
    }
  }
  .devicePermission-list-device-show {
    flex: 1;
    padding: 10px;
    box-sizing: border-box;
    // border: 1px solid #777;
    .device-list {
      display: flex;
      flex-wrap: wrap;
      > div {
        flex: none;
        width: 33%;
      }
    }
  }
}
</style>
