<template>
  <el-card>
    <el-scrollbar>
      <div class="flex-search" :style="{ minWidth: `${width - 42}px`, padding: '0px' }">
        <div class="left">
          <el-tooltip :content="t('glob.refresh')" placement="top">
            <el-button v-blur color="#40485b" class="table-header-operate" type="info" :icon="Refresh" @click="handleStateRefresh()"></el-button>
          </el-tooltip>
        </div>
        <div class="center">
          <!--  -->
        </div>
        <div class="right">
          <el-button type="primary" :icon="Plus" @click="handleStateCreate({ ...publicParams })">{{ t("glob.add") }}</el-button>
        </div>
      </div>
    </el-scrollbar>
    <el-table v-loading="state.loading" :data="state.data" :height="height - 64 - 60 - (state.total ? 32 : 0)" :style="{ width: `${width - 40}px`, margin: '0 auto' }">
      <el-table-column v-for="column in state.column" :key="column.key" :prop="(column.key as string)" :label="column.label" :min-width="column.width || 120"  :formatter="column.formatter" />
      <el-table-column :label="t('glob.operate')" align="left" header-align="left" :width="110" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" :icon="Edit" @click="handleStateEditor(row)" :title="t('glob.edit')"></el-button>
          <el-button link type="danger" :icon="Delete" @click="handleStateDelete(row)" :title="t('glob.delete')"></el-button>
          <el-dropdown @command="$event.callback(row)" class="el-button el-button--default is-link" style="vertical-align: middle">
            <span style="font-size: var(--el-font-size-base)">
              <el-icon :title="t('glob.More')"><More /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="{ callback: () => {} }">未开发...</el-dropdown-item>
                <el-dropdown-item :command="{ callback: () => {} }">未开发...</el-dropdown-item>
                <el-dropdown-item :command="{ callback: () => {} }">未开发...</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <div :style="{ margin: '8px 20px 20px' }">
      <el-pagination v-show="state.total" v-model:current-page="state.page" v-model:page-size="state.size" :page-sizes="state.sizes" :total="state.total" layout="->, total, sizes, prev, pager, next, jumper" small @size-change="handleStateRefresh()" @current-change="handleStateRefresh()" />
    </div>
  </el-card>
  <EditorForm ref="editorRef" title="团队"></EditorForm>
</template>

<script setup lang="ts" name="personnel/team">
import { ref, reactive, nextTick, watch, computed, inject } from "vue";
import type { Ref, VNode } from "vue";
import { useI18n } from "vue-i18n";
import { sizes } from "@/utils/common";
import { formatterDate } from "@/utils/date";

// Ui
import { ElMessage, ElButton } from "element-plus";
// eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
import { More, Refresh, Plus, Edit, Delete } from "@element-plus/icons-vue";

// Api
import { getTeam as getData, addTeam as addData, modTeam as modData, delTeam as delData } from "@/api/personnel";
import type { TeamItem as DataItem } from "@/api/personnel";

// Editor
import { EditorType } from "@/views/common/interface";
import EditorForm from "./EditForm.vue";

const editorRef = ref<InstanceType<typeof EditorForm>>();

const publicParams = computed<Record<string, unknown>>(() => ({}));

async function createItem(params: Partial<DataItem>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  await editorRef.value.open({ ...params, "#TYPE": EditorType.Add }, async (req) => {
    try {
      const { success, message, data } = await addData({ ...req, ...publicParams });
      if (success) {
        ElMessage.success(`添加成功！`);
        return true;
      } else throw Object.assign(new Error(message), { success, data });
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      return false;
    }
  });
}
async function editorItem(params: Partial<DataItem>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  await editorRef.value.open({ ...params, "#TYPE": EditorType.Mod }, async (req) => {
    try {
      const { success, message, data } = await modData({ ...req });
      if (success) {
        ElMessage.success(`编辑成功！`);
        return true;
      } else throw Object.assign(new Error(message), { success, data });
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      return false;
    }
  });
}
async function deleteItem(params: Partial<DataItem>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  await editorRef.value.open({ ...params, "#TYPE": EditorType.Del }, async (req) => {
    try {
      const { success, message, data } = await delData({ ...req });
      if (success) {
        ElMessage.success(`删除成功！`);
        return true;
      } else throw Object.assign(new Error(message), { success, data });
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      return false;
    }
  });
}
async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  let controller = new AbortController();
  const response = getData({
    platform: params.platform as string,
    ...Object.entries(state.search).reduce((p, [key, value]) => (value !== null && value !== undefined && value !== "" ? Object.assign(p, { [key]: value }) : p), {}),
    ...publicParams,
    paging: {
      pageNumber: state.page,
      pageSize: state.size,
    },
    controller,
  });
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await response;
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);
      return data instanceof Array ? data : [];
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(20);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}

/*********************************************************/

const { t } = useI18n();

const width = inject<Ref<number>>("width", ref(100));
const height = inject<Ref<number>>("height", ref(100));

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: {}, value: T[keyof T]) => string | VNode }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<DataItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {
    keyword: "",
  },
  column: [
    /* 列 */
    // { key: "id", label: "ID" },
    { key: "name", label: "名称" },
    { key: "note", label: "备注" },
    { key: "createdTime", label: "创建日期", formatter: (_row, _col, v) => formatterDate(v as string) },
    { key: "updatedTime", label: "修改日期", formatter: (_row, _col, v) => formatterDate(v as string) },
  ],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

watch<Record<string, unknown>, true>(
  publicParams,
  async function () {
    if (state.loading) return;
    handleStateRefresh();
  },
  { immediate: true, flush: "post" }
);

async function handleStateCreate(params: Partial<DataItem>) {
  await createItem(params);
  await handleStateRefresh();
}
async function handleStateEditor(params: Partial<DataItem>) {
  await editorItem(params);
  await handleStateRefresh();
}
async function handleStateDelete(params: Partial<DataItem>) {
  await deleteItem(params);
  await handleStateRefresh();
}
async function handleStateRefresh() {
  if (state.loading) return;
  state.loading = true;
  state.data.splice(0, state.data.length, ...(await querysItem({ ...publicParams })));
  state.loading = false;
}
</script>

<style lang="scss" scoped></style>
