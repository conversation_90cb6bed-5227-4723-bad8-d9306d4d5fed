<template>
  <el-dialog v-model="dialogVisible" title="添加用户组" width="500" :before-close="handleClose">
    <div>
      <el-select style="width: 100%" v-model="userGroupId" placeholder="请选择用户组" clearable filterable>
        <el-option v-for="item in userGroupList" :key="item.id" :label="item.name" :value="item.id" />
        <!-- <el-option label="Zone two" value="beijing" /> -->
      </el-select>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <!-- <el-button @click="dialogVisible = false">other</el-button> -->
        <span></span>
        <div>
          <el-button type="primary" @click="next()"> 下一步</el-button>

          <el-button @click="dialogVisible = false">取消</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
  <el-dialog style="z-index: 10" v-model="authDialogVisible" title="权限配置" width="500" :before-close="handleClose" append-to-body>
    <div>
      <el-select style="width: 100%" v-model="authType" placeholder="请选择权限" clearable filterable @change="chooseAuthType">
        <!-- <el-option v-for="item in authModelList" :key="item.value" :label="item.label" :value="item.value" /> -->
        <el-option :disabled="!authTemplateList.some((v) => v.name === '可读')" label="可读" value="可读"></el-option>
        <el-option label="所有权限" :value="AssignType.FULL"></el-option>
        <el-option label="自定义" :value="AssignType.CUSTOM"></el-option>
        <el-option label="清空" :value="AssignType.CLEAR"></el-option>
      </el-select>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <!-- <el-button @click="authDialogVisible = false">other</el-button> -->
        <span></span>
        <div>
          <el-button type="primary" @click="bindAuth"> 确定</el-button>

          <el-button @click="authDialogVisible = false">取消</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
  <el-dialog style="z-index: 11" v-model="customDialogVisible" title="" width="50%" :before-close="handleClose" append-to-body>
    <div>
      <el-form :model="form" label-width="100">
        <el-form-item label="配置权限">
          <el-select v-model="form.name">
            <el-option label="自定义" value="custom"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="授权">
          <el-select v-model="form.assign">
            <!-- <el-option label="授权" value="授权"></el-option> -->
            <el-option label="授权（继承）" value="授权（继承）"></el-option>
            <el-option label="授权（不继承）" value="授权（不继承）"></el-option>
            <el-option label="禁止授权" value="禁止授权"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="配置项">
          <el-select v-model="form.authItem" @change="authItemChange">
            <el-option v-for="item in authItem" :key="item.id" :value="item.id" :label="item.name"></el-option>
            <!-- const authItem = ref([]);
const permissionList = ref([]); -->
          </el-select>
        </el-form-item>
        <el-form-item label="权限">
          <el-select v-model="form.permissionIds" multiple>
            <el-option v-for="item in permissionList" :key="item.id" :value="item.id" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <!-- <el-select v-model="form.permissionIds"> </el-select> -->
          <el-checkbox v-model="form.mfa" label="双因素认证" size="large" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <!-- <el-button @click="customDialogVisible = false">other</el-button> -->
        <span></span>
        <div>
          <el-button type="primary" @click="customBindAuth">确定</el-button>

          <el-button @click="customDialogVisible = false">取消</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h, toValue } from "vue";
import { ElMessage, ElMessageBox, ElText } from "element-plus";
import { getContainerGroup } from "@/api/personnel";
import { getUserGroupsAuth, relationUserGroupsAuth, getAuthGroupRelationUserGroup, setUserGroupAuth, customSetPermissions, getAssigned_permissions, cancelPermission, deleteUserGroupsAuth, setAllPermissions } from "@/api/authConfig";
import { useSiteConfig } from "@/stores/siteConfig";
import { getPermissionItems } from "@/api/application";
import { getAppAuth } from "@/api/application";

enum AssignType {
  FULL = "FULL",
  CUSTOM = "CUSTOM",
  CLEAR = "CLEAR",
}

const siteConfig = useSiteConfig();
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
interface Emits {
  (event: "confirm", item: any): void;
}
const emits = defineEmits<Emits>();
const form = ref({
  name: "custom",
  assign: "",
  authItem: "",
  permissionIds: [],
  mfa: false,
});
const dialogVisible = ref(false);
const userGroupId = ref("");
const userGroupList = ref([]);

async function getUserGroupList(containerId) {
  await getContainerGroup({ appId: (siteConfig.baseInfo || {}).app, containerId }).then((res) => {
    // console.log(res);
    if (res.success) {
      // console.log(haveUserGroupData.value);
      userGroupList.value = res.data.filter((item) => !haveUserGroupData.value.some((v) => v.userGroupId === item.id && v.extend == false));
    }
  });
}
const authType = ref("");
const authGroupInfo = ref({});
//权限类型弹框
const authDialogVisible = ref(false);
//自定义权限配置弹框
const customDialogVisible = ref(false);
//模板id
// const authTemplateId = ref("");
const authTemplateId = computed(() => toValue(authTemplateList).reduce((p, c) => (c.name === toValue(authType) ? c.id : p), ""));

//当前资源所拥有的用户组
const haveUserGroupData = ref([]);
//打开用户组弹框
async function open(row: {}) {
  // console.log(row);

  form.value = {
    name: "custom",
    assign: "",
    authItem: "",
    permissionIds: [],
    mfa: false,
  };
  // console.log(row);
  haveUserGroupData.value = row.userGroupData;
  // console.log(row.haveUserGroupData);
  userGroupId.value = "";
  authType.value = "";
  if (row) {
    authGroupInfo.value = { ...row };
    dialogVisible.value = true;

    await getPermissionList();
  }
  await getUserGroupList(row.containerId);
}
const tableItem = ref([]);
//打开自定义弹框
async function openAuth(row: { containerId: string; id: string; assignId: string }) {
  form.value = {
    name: "custom",
    assign: "",
    authItem: "",
    permissionIds: [],
    mfa: false,
  };
  authType.value = "";
  // console.log(row);
  if (row) {
    authGroupInfo.value.containerId = row.containerId;
    authGroupInfo.value.id = row.id;
    groupAssignId.value = row.assignId;
    authDialogVisible.value = true;
    tableItem.value = { ...row };

    await getPermissionList();
    try {
      const { success, message, data } = await getUserGroupsAuth({ appId: (siteConfig.baseInfo || {}).app, groupId: row.id });
      if (!success) throw Object.assign(new Error(message), { success, data });
      authTemplateList.value = data instanceof Array ? data : [];
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
    }
  }
}
//选择权限配置项
async function authItemChange(val) {
  // console.log(val);
  form.value.permissionIds = [];
  await handleStateRefresh(val);
}
//点击下一步获取 权限模板来配置权限
//获取权限配置组下的用户组分配列表
//获取权限模板组id
const authTemplateList = ref([]);
const groupAssignId = ref("");
async function next() {
  try {
    const { success, message, data } = await relationUserGroupsAuth({ containerId: authGroupInfo.value.containerId, userGroupId: userGroupId.value, permissionGroupId: authGroupInfo.value.id });
    if (!success) throw Object.assign(new Error(message), { success, data });
    // authTemplateList.value = data instanceof Array ? data : [];
    const { success: templateSuccess, message: templateMessage, data: templateData } = await getUserGroupsAuth({ appId: (siteConfig.baseInfo || {}).app, groupId: data.id });
    if (!templateSuccess) throw Object.assign(new Error(templateMessage), { success: templateSuccess, data: templateData });
    authTemplateList.value = templateData instanceof Array ? templateData : [];
    groupAssignId.value = data.assignId;
    authDialogVisible.value = true;
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }

  // await relationUserGroupsAuth({
  //   containerId: authGroupInfo.value.containerId,
  //   userGroupId: userGroupId.value,
  //   permissionGroupId: authGroupInfo.value.id,
  // })
  //   .then((res) => {
  //     if (res.success) {
  //       authDialogVisible.value = true;
  //       groupAssignId.value = res.data.assignId;
  //       // getAuthGroupRelationUserGroup({
  //       //   containerId: authGroupInfo.value.containerId,

  //       //   permissionGroupId: authGroupInfo.value.id,
  //       // })
  //       //   .then((res) => {
  //       //     if (res.success) {
  //       //       res.data.map((v) => {
  //       //         if (v.userGroupId == userGroupId.value) {
  //       //           groupAssignId.value = v.assignId;
  //       //         }
  //       //       });

  //       //       // authDialogVisible.value = true;
  //       //     }
  //       //   })
  //       //   .catch((err) => {
  //       //     err instanceof Error && ElMessage.error(err.message);
  //       //   });
  //     }
  //   })
  //   .catch((err) => {
  //     err instanceof Error && ElMessage.error(err.message);
  //   });

  // await getUserGroupsAuth({ appId: (siteConfig.baseInfo || {}).app, groupId: authGroupInfo.value.id }).then((res) => {
  //   // console.log(res);
  //   // authTemplateId.value = "";

  //   if (res.success) {
  //     authTemplateList.value = res.data;
  //   }
  // });
  // // await setUserGroupAuth
}

async function chooseAuthType() {
  try {
    switch (toValue(authType)) {
      case AssignType.CUSTOM: {
        customDialogVisible.value = true;
        break;
      }
      // case AssignType.CLEAR: {
      //   const { success, message, data } = await getAssigned_permissions({ groupAssignId: toValue(groupAssignId) });
      //   if (!success) throw Object.assign(new Error(message), { success, data });
      //   permissionItemId.value = Array.from(
      //     data
      //       .reduce((p, c) => {
      //         const permissions = c.permissions instanceof Array ? c.permissions : [];
      //         for (let i = 0; i < permissions.length; i++) {
      //           if (permissions[i].enabled) p.add(permissions[i].id);
      //         }
      //         return p;
      //       }, new Set<string>())
      //       .values()
      //   );
      //   break;
      // }
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
}

//绑定配置模板权限
async function bindAuth() {
  try {
    const $groupAssignId = toValue(groupAssignId);
    switch (toValue(authType)) {
      case AssignType.FULL: {
        const { success, message, data } = await setAllPermissions({ groupAssignId: $groupAssignId });
        if (!success) throw Object.assign(new Error(message), { success, data });
        ElMessage.success("操作成功");
        authDialogVisible.value = false;
        customDialogVisible.value = false;
        dialogVisible.value = false;
        emits("confirm", { groupAssignId: $groupAssignId });
        break;
      }
      case AssignType.CUSTOM: {
        break;
      }
      case AssignType.CLEAR: {
        const { success, message, data } = await deleteUserGroupsAuth({ assignId: $groupAssignId });
        if (!success) throw Object.assign(new Error(message), { success, data });
        ElMessage.success("操作成功");
        authDialogVisible.value = false;
        customDialogVisible.value = false;
        dialogVisible.value = false;
        emits("confirm", { groupAssignId: $groupAssignId });
        break;
      }
      default: {
        const $authTemplateId = toValue(authTemplateId);
        if (!$authTemplateId) throw new Error(`用户组下没有可配置的"${toValue(authType)}"权限`);
        const { success, message, data } = await setUserGroupAuth({ groupAssignId: $groupAssignId, templateId: $authTemplateId });
        if (!success) throw Object.assign(new Error(message), { success, data });
        ElMessage.success("操作成功");
        authDialogVisible.value = false;
        customDialogVisible.value = false;
        dialogVisible.value = false;
        emits("confirm", { groupAssignId: $groupAssignId });
        break;
      }
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
}
//自定义绑定权限
async function customBindAuth() {
  // console.log(form.value)
  const obj = { ...form.value };
  switch (form.value.assign) {
    case "授权（不继承）":
      obj.assign = true;
      obj.extend = false;
      break;
    case "授权（继承）":
      obj.assign = true;
      obj.extend = true;

      break;
    case "禁止授权":
      obj.assign = false;
      break;
  }
  console.log(authType.value);
  //
  await customSetPermissions({ ...obj, groupAssignId: groupAssignId.value })
    .then((res) => {
      // console.log(res);
      if (res.success) {
        authDialogVisible.value = false;
        customDialogVisible.value = false;
        dialogVisible.value = false;
        emits("confirm", { groupAssignId: groupAssignId.value });
      }
    })
    .catch((err) => {
      err instanceof Error && ElMessage.error(err.message);
    });
  // console.log(obj)
}
//获取用户组关联的所有权限
const permissionItemId = ref([]);
const authItem = ref([]);
const permissionList = ref([]);
//获取权限配置项
async function getPermissionList() {
  const { success, message, data } = await getPermissionItems({ appId: (siteConfig.baseInfo || {}).app });
  if (!success) throw Object.assign(new Error(message), { success, data });
  // console.log(data);
  if (success) {
    let arr = [];
    data.map((v) => {
      // console.log(v, authGroupInfo.value);
      if (v.groupId === authGroupInfo.value.id && v.enabled) {
        arr.push(v);
      }
    });
    authItem.value = [...arr];
    // console.log(authItem.value);
  }

  // authGroupList.value.map((item) => {
  //   item.children = [];
  //   data.map((v) => {
  //     if (item.id === v.groupId) {
  //       item.children.push(v);
  //     }
  //   });
  // });
  // toRefs(authGroupList.value);
}
async function handleStateRefresh(id) {
  // state.data = [];

  const { success, message, data } = await getAppAuth({ appId: (siteConfig.baseInfo || {}).app });
  if (!success) throw Object.assign(new Error(message), { success, data });
  // permissionList.value = data.map((v) => v.itemId == id);
  let arr = [];
  data.map((v, i) => {
    if (v.itemId == id && v.enabled) {
      arr.push(v);
    }
  });
  permissionList.value = [...arr];
}
function beforeCreate() {}
function created() {}
function beforeMount() {}
async function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}

beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ console.log.bind(null, '🚀[onErrorCaptured]'), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ console.log.bind(null, '🚀[onRenderTracked]'), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ console.log.bind(null, '🚀[onRenderTriggered]'), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
defineExpose({ open, openAuth });
</script>
<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.el-message {
  z-index: 9999 !important; /* 确保这个值高于其他元素 */
}
</style>
