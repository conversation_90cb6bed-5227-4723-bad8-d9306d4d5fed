<template>
  <el-dialog v-model="visible" :close-on-click-modal="false" append-to-body draggable :width="`${$width}px`" :before-close="handleCancel">
    <template #header>
      <div class="leading-[18px]">{{ props.title }}</div>
    </template>
    <template #default>
      <el-scrollbar ref="contentRef" :max-height="$height" :view-style="{ padding: '0 6px' }">
        <el-form ref="formRef" :model="form" label-position="left" :label-width="80" require-asterisk-position="right" :status-icon="true" @keyup.ctrl.exact.enter.prevent.stop="handleSubmit()" @keydown.meta.exact.enter.prevent.stop="handleSubmit()" @submit.prevent="handleSubmit()">
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="配置权限" prop="type" :rules="[{ required: true, message: '请选择用户组', trigger: 'bulr' }]">
                <el-select style="width: 100%" v-model="form.type" :loading="loadingTemplatePermission" placeholder="请选择配置方式" clearable filterable @change="() => ((assignMode = AssignMode.EXTEND), (form.itemId = ''), (form.permissionIds = []))">
                  <el-option v-for="item in templatePermission" :key="item.id" :label="item.name" :value="item.id"></el-option>
                  <el-option label="所有权限" :value="AssignType.FULL"></el-option>
                  <el-option v-if="params.isClear" label="清空" :value="AssignType.CLEAR"></el-option>
                  <el-option label="自定义" :value="AssignType.CUSTOM"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <template v-if="form.type === AssignType.CUSTOM">
              <el-col :span="24">
                <el-form-item label="授权" prop="assign">
                  <el-select style="width: 100%" v-model="assignMode" placeholder="请选择授权方式">
                    <el-option label="授权（继承）" :value="AssignMode.EXTEND"></el-option>
                    <el-option label="授权（不继承）" :value="AssignMode.ASSIGN"></el-option>
                    <el-option label="禁止授权" :value="AssignMode.NULL"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="配置项" prop="itemId">
                  <el-select style="width: 100%" v-model="form.itemId" :loading="loadingPermissionItems" placeholder="请选择配置项" @change="($event) => (form.permissionIds = assignedPermission.reduce<string[]>((p, c) => ((form.assign ? !!c.assign === !!form.assign && !!c.extend === !!form.extend : !!c.assign === !!form.assign) && c.itemId === $event ? p.concat(c.permissions.map((v) => v.id)) : p), []))">
                    <el-option v-for="item in selectablePermissionItems" :key="item.id" :label="item.name" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="权限" prop="permissionIds">
                  <el-select style="width: 100%" v-model="$permissionIds" :disabled="!form.itemId" :loading="loadingAppAuth" placeholder="请选择权限" multiple>
                    <el-option v-for="item in appAuth.filter((v) => v.enabled && v.itemId === form.itemId).sort((a, b) => a.orderNum - b.orderNum)" :key="item.id" :disabled="!!currentPermission.filter((v) => (form.assign ? v.assign === form.assign && v.extend === form.extend : v.assign === form.assign) && v.id === item.id).length" :label="item.name" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="认证">
                  <el-checkbox v-model="form.mfa" label="双因素认证"></el-checkbox>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
        </el-form>
      </el-scrollbar>
    </template>
    <template #footer>
      <div :style="{ padding: '0 10px 10px' }">
        <!-- <el-button type="warning" :loading="loading" @click="handleResets()">{{ $t("Resets") }}</el-button> -->
        <el-button type="default" :loading="loading" @click="handleCancel()">取消</el-button>
        <!-- <el-button type="primary" :loading="loading" @click="handleFinish()">{{ $t("Finish") }}</el-button> -->
        <el-button type="primary" :loading="loading" @click="handleSubmit()">确定</el-button>
      </div>
      <div class="i-mdi-resize-bottom-right absolute bottom-0 right-0 h-20px w-[20px] cursor-se-resize" @mousedown.self="handleZoom"></div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, shallowReadonly, toValue, toRaw, nextTick, computed } from "vue";
import { cloneDeep } from "lodash-es";
import { ElMessage } from "element-plus";
import { useSiteConfig } from "@/stores/siteConfig";
import { getPermissionTemplate } from "@/api/permission";
import { getPermissionUsable, getAssignedPermissionList, type AssignedItem } from "./security_container";
import { getAppAuth } from "@/api/application";
/* ========================================================= USE ACHIEVE START ========================================================= */
enum AssignType {
  FULL = "FULL",
  CUSTOM = "CUSTOM",
  CLEAR = "CLEAR",
}
enum AssignMode {
  EXTEND = "EXTEND",
  ASSIGN = "ASSIGN",
  NULL = "NULL",
}
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// type RequestBase = "controller" | "keyword" | "paging" | "slot";
// type CreateItem = Omit<Required<typeof addItem extends (req: infer P) => any ? P : never>, RequestBase>;
// type SetterItem = Omit<Required<typeof setItem extends (req: infer P) => any ? P : never>, RequestBase>;
// type ModifyItem = Omit<Required<typeof modItem extends (req: infer P) => any ? P : never>, RequestBase>;
// type DeleteItem = Omit<Required<typeof delItem extends (req: infer P) => any ? P : never>, RequestBase>;
interface EditorItem /* extends CreateItem, SetterItem, ModifyItem */ {
  containerId: string;
  userGroupId: string;
  permissionGroupId: string;
  type: string | AssignType;
  assign: boolean;
  extend: boolean;
  mfa: boolean;
  itemId: string;
  permissionIds: string[];
  appAuth: string[];
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const defaultForm = shallowReadonly<{ [Key in keyof EditorItem]: DefaultFormData<EditorItem[Key]> }>({
  /**
   * TODO: 此为表单初始默认数据和校验方法
   * 使用`buildDefaultType`方法构建默认值
   */
  containerId: buildDefaultType(""),
  userGroupId: buildDefaultType(""),
  permissionGroupId: buildDefaultType(""),
  type: buildDefaultType(""),
  assign: buildDefaultType(true as boolean),
  extend: buildDefaultType(true as boolean),
  mfa: buildDefaultType(false as boolean),
  itemId: buildDefaultType(""),
  permissionIds: buildDefaultType([] as string[]),
  appAuth: buildDefaultType([] as string[]),
});

const form = ref<EditorItem>(Object.entries(defaultForm).reduce<Partial<EditorItem>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {} as Partial<EditorItem>) as EditorItem);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
const currentPermission = computed(() => {
  const $form = toValue(form);
  return toValue(assignedPermission).reduce<AssignedItem[]>((p, c) => (($form.assign ? !!c.assign === !!$form.assign && !!c.extend === !!$form.extend : !!c.assign === !!$form.assign) && c.itemId === $form.itemId ? p.concat(c.permissions) : p), []);
});

const $permissionIds = computed({
  get: () => toValue(form).permissionIds,
  set: ($auths: string[]) => {
    const $list = toValue(appAuth).filter((v) => v.itemId === toValue(form).itemId && v.enabled);
    const $ids = $list.map((v) => v.id);
    const $map = $list.reduce((p, c) => {
      const $key = c.id;
      const $bind: string[] = c.allInItem ? $ids.filter((v) => v !== $key) : (c.childIds instanceof Array ? c.childIds : []).filter((v) => $ids.includes(v));
      const $hand: string[] = $list.reduce<string[]>((hand, item) => (item.id === $key ? hand : item.allInItem || (item.childIds instanceof Array ? item.childIds : []).includes($key) ? hand.concat(item.id) : hand), []);
      p.set($key, { bind: $bind, hand: $hand });
      return p;
    }, new Map<string, { /* 关联的权限 */ bind: string[]; /* 依赖的权限 */ hand: string[] }>());
    const $result = new Set<string>(toValue(form).permissionIds.filter((v) => $map.has(v) && ($auths instanceof Array ? $auths : []).includes(v)));
    const $addValue: string[] = ($auths instanceof Array ? $auths : []).filter((v) => $map.has(v) && !toValue(form).permissionIds.includes(v));
    const $delValue: string[] = toValue(form).permissionIds.filter((v) => $map.has(v) && !($auths instanceof Array ? $auths : []).includes(v));
    const $addAffect = /* 处理添加数据 */ (id: string, $affect: { bind: string[]; hand: string[] }) => {
      $result.add(id);
      for (let i = 0; i < $affect.bind.length; i++) $addAffect($affect.bind[i], $map.get($affect.bind[i])!);
      for (let i = 0; i < $affect.hand.length; i++) {
        if ($map.get($affect.hand[i])!.bind.every((v) => $result.has(v))) $result.add($affect.hand[i]);
      }
    };
    const $delAffect = /* 处理移除数据 */ (id: string, $affect: { bind: string[]; hand: string[] }) => {
      $result.delete(id);
      for (let i = 0; i < $affect.bind.length; i++) $delAffect($affect.bind[i], $map.get($affect.bind[i])!);
      for (let i = 0; i < $affect.hand.length; i++) {
        if ($result.has($affect.hand[i])) $result.delete($affect.hand[i]);
      }
    };
    for (let i = 0; i < $addValue.length; i++) $addAffect($addValue[i], $map.get($addValue[i])!);
    for (let i = 0; i < $delValue.length; i++) $delAffect($delValue[i], $map.get($delValue[i])!);
    form.value.permissionIds = Array.from($result.values());
  },
});
const siteConfig = useSiteConfig();
const loadingTemplatePermission = ref(false);
const templatePermission = ref<ReturnType<typeof getPermissionTemplate> extends Promise<{ data: infer P }> ? P : never>([]);
const loadingPermissionItems = ref(false);
const permissionItems = ref<ReturnType<typeof getPermissionUsable> extends Promise<{ data: infer P }> ? P : never>([]);
const loadingAppAuth = ref(false);
const appAuth = ref<ReturnType<typeof getAppAuth> extends Promise<{ data: infer P }> ? P : never>([]);
const loadingAssignedPermission = ref(false);
const assignedPermission = ref<ReturnType<typeof getAssignedPermissionList> extends Promise<{ data: infer P }> ? P : never>([]);
const assignMode = computed({
  get: () => {
    const $form = toValue(form);
    if ($form.assign) {
      return $form.extend ? AssignMode.EXTEND : AssignMode.ASSIGN;
    } else return AssignMode.NULL;
  },
  set: (v) => {
    switch (v) {
      case AssignMode.EXTEND:
        form.value.assign = true;
        form.value.extend = true;
        break;
      case AssignMode.ASSIGN:
        form.value.assign = true;
        form.value.extend = false;
        break;
      default:
        form.value.assign = false;
        form.value.extend = false;
        break;
    }
    const $form = toValue(form);
    form.value.itemId = toValue(selectablePermissionItems).some((v) => v.id === form.value.itemId) ? form.value.itemId : "";
    form.value.permissionIds = toValue(assignedPermission).reduce<string[]>((p, c) => (($form.assign ? !!c.assign === !!$form.assign && !!c.extend === !!$form.extend : !!c.assign === !!$form.assign) && c.itemId === $form.itemId ? p.concat(c.permissions.map((v) => v.id)) : p), []);
  },
});
const selectablePermissionItems = computed(() => {
  const $form = toValue(form);
  return toValue(permissionItems).filter((v) => v.groupId === $form.permissionGroupId && v.enabled && !toValue(assignedPermission).filter(({ itemId, assign, extend }) => v.id === itemId && (assign ? assign === $form.assign && extend === $form.extend : assign === $form.assign)).length);
});
/**
 * TODO: 首次打开初始化时执行
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(): Promise<void> {
  const $params = toValue(params);
  await Promise.all([
    (async () => {
      try {
        templatePermission.value = [];
        loadingTemplatePermission.value = true;
        const { success, message, data } = await getPermissionTemplate({ appId: (siteConfig.baseInfo || {}).app || "", groupId: $params.permissionGroupId as string });
        if (!success) throw Object.assign(new Error(message), { success, data });
        templatePermission.value = data instanceof Array ? data : [];
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      } finally {
        loadingTemplatePermission.value = false;
      }
    })(),
    (async () => {
      try {
        permissionItems.value = [];
        loadingPermissionItems.value = true;
        const { success, message, data } = await getPermissionUsable({ appId: (siteConfig.baseInfo || {}).app || "", containerId: $params.containerId as string, permissionGroupId: $params.permissionGroupId as string });
        if (!success) throw Object.assign(new Error(message), { success, data });
        permissionItems.value = data instanceof Array ? data : [];
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      } finally {
        loadingPermissionItems.value = false;
      }
    })(),
    (async () => {
      try {
        appAuth.value = [];
        loadingAppAuth.value = true;
        const { success, message, data } = await getAppAuth({ appId: (siteConfig.baseInfo || {}).app || "" });
        if (!success) throw Object.assign(new Error(message), { success, data });
        console.log(data, "data");
        appAuth.value = data instanceof Array ? data : [];
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      } finally {
        loadingAppAuth.value = false;
      }
    })(),
    (async () => {
      try {
        assignedPermission.value = [];
        if ($params.groupAssignId) {
          loadingAssignedPermission.value = true;
          const { success, message, data } = await getAssignedPermissionList({ extendView: false, groupAssignId: $params.groupAssignId as string, permissionGroupId: $params.permissionGroupId as string, userGroupId: $params.userGroupId as string, container: props.select as string });
          if (!success) throw Object.assign(new Error(message), { success, data });
          assignedPermission.value = data instanceof Array ? data : [];
        }
        const $form = toValue(form);
        form.value.itemId = toValue(selectablePermissionItems).some((v) => v.id === form.value.itemId) ? form.value.itemId : "";
        form.value.permissionIds = $form.assign ? toValue(assignedPermission).reduce<string[]>((p, c) => (($form.assign ? !!c.assign === !!$form.assign && !!c.extend === !!$form.extend : !!c.assign === !!$form.assign) && c.itemId === $form.itemId ? p.concat(c.permissions.map((v) => v.id)) : p), []) : [];
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      } finally {
        loadingAssignedPermission.value = false;
      }
    })(),
  ]);
}
/**
 * TODO: 每次重置表单时调用
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(): Promise<void> {
  const $form = toValue(form);
  form.value.itemId = toValue(selectablePermissionItems).some((v) => v.id === form.value.itemId) ? form.value.itemId : "";
  form.value.permissionIds = $form.assign ? toValue(assignedPermission).reduce<string[]>((p, c) => (($form.assign ? !!c.assign === !!$form.assign && !!c.extend === !!$form.extend : !!c.assign === !!$form.assign) && c.itemId === $form.itemId ? p.concat(c.permissions.map((v) => v.id)) : p), []) : [];
}

/**
 * TODO: 此处使用可对生成的数据操作
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: EditorItem): Promise<EditorItem> {
  return { ...form, appAuth: toValue(assignedPermission).reduce<string[]>((p, c) => ((form.assign ? !!c.assign === !!form.assign && !!c.extend === !!form.extend : !!c.assign === !!form.assign) && c.itemId === form.itemId ? p.concat(c.permissions.map((v) => v.id)) : p), []) };
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

const params = ref<Record<string, unknown>>({});
defineOptions({ name: "IndexEditor", inheritAttrs: false });
interface Props {
  title?: string;
  labelWidth?: number;
  width: number;
  height: number;
  select?: string;
}
const props = withDefaults(defineProps<Props>(), { title: "", labelWidth: 80 });
const visible = ref(false);
const loading = ref(false);
const $width = ref(props.width / 2);
const $height = ref(props.height);
const handle = reactive({
  callback: undefined as ((form: EditorItem) => Promise<void>) | undefined,
  resolve: (form: EditorItem) => void form,
  reject: (err: Error) => void err,
});

async function getForm(form: Partial<Record<keyof EditorItem, unknown>>): Promise<EditorItem> {
  const $form: Partial<EditorItem> = {};
  await nextTick();
  for (const key of Reflect.ownKeys(defaultForm)) {
    const structure = Reflect.get(defaultForm, key);
    const $value = Reflect.get(form, key);
    if (!structure) continue;
    Reflect.set($form, key, cloneDeep(structure.test($value) ? $value : structure.transfer(Reflect.get($form, key), toRaw(structure.value))));
  }
  return await transformForm($form as Required<EditorItem>);
}

const formRef = ref<InstanceType<typeof import("element-plus").ElForm>>();
async function handleFinish() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  if (!(await new Promise((resolve) => $formRef.validate(resolve)))) return;
  try {
    loading.value = true;
    await nextTick();
    const $form = await getForm(toValue(form));
    if (handle.callback) {
      try {
        await handle.callback($form);
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return;
      }
    }
    close("fulfilled");
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}
async function handleSubmit() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  if (!(await new Promise((resolve) => $formRef.validate(resolve)))) return;
  try {
    loading.value = true;
    await nextTick();
    const $form = await getForm(toValue(form));
    if (handle.callback) {
      try {
        await handle.callback($form);
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return;
      }
    }
    close("fulfilled");
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}
async function handleCancel() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  try {
    loading.value = true;
    await nextTick();
    close("rejected");
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}
async function handleResets() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  try {
    loading.value = true;
    await nextTick();
    form.value = await getForm(toValue(params));
    await resetFormInit();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}
async function handleOpener() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  try {
    loading.value = true;
    await nextTick();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}

function close(status: "fulfilled" | "rejected") {
  switch (status) {
    case "fulfilled":
      handle.resolve(toValue(form));
      break;
    case "rejected":
      handle.reject(Object.assign(new Error("Cancel"), toValue(form)));
      break;
    default:
      handle.reject(Object.assign(new Error("Error"), toValue(form)));
      break;
  }
  params.value = {};
  visible.value = false;
  nextTick(() => {
    loading.value = false;
    window.setTimeout(() => {
      handle.resolve = (form: EditorItem) => void form;
      handle.reject = (err: Error) => void err;
      handle.callback = undefined;
    });
  });
}

async function operate($handle: () => Promise<void>, $params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>): Promise<EditorItem> {
  if (toValue(loading)) return toValue(form);
  $width.value = props.width / 2 > 360 ? props.width / 2 : 360;
  $height.value = props.height;
  params.value = $params;
  loading.value = true;
  visible.value = true;
  const result = new Promise<EditorItem>((resolve, reject) => Object.assign(handle, { resolve, reject, callback }));
  await nextTick();
  try {
    await runningInit();
    loading.value = false;
    await handleResets();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    loading.value = false;
    await handleCancel();
  }
  await $handle();
  try {
    return result;
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : "Error");
  }
}

const contentRef = ref<InstanceType<typeof import("element-plus").ElScrollbar>>();
function handleZoom($event: MouseEvent) {
  const { wrapRef } = toValue(contentRef) || {};
  if (wrapRef) $height.value = wrapRef.offsetHeight;
  const w = toValue($width);
  const h = toValue($height);

  const max_w = props.width;
  const min_w = 360;

  const max_h = props.height;
  const min_h = 62;

  const controller = new AbortController();
  window.document.addEventListener(
    "mousemove",
    (e) => {
      e.preventDefault();
      nextTick(() => {
        const _w = w + (e.clientX - $event.clientX) * 2;
        $width.value = _w < max_w ? (_w > min_w ? _w : min_w) : max_w;
        const _h = h + (e.clientY - $event.clientY) * 1;
        $height.value = _h < max_h ? (_h > min_h ? _h : min_h) : max_h;
      });
    },
    { signal: controller.signal }
  );
  window.document.addEventListener(
    "mouseup",
    () => {
      controller.abort();
    },
    { once: true, signal: controller.signal }
  );
}

defineExpose({
  async finish($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleFinish, $params, callback);
  },
  async submit($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleSubmit, $params, callback);
  },
  async cancel($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleCancel, $params, callback);
  },
  async resets($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleResets, $params, callback);
  },
  async opener($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleOpener, $params, callback);
  },
});

/*  */
interface ConstructorFunc<T> {
  new (value: unknown): T;
  (value: unknown): T;
}

interface DefaultFormData<T> {
  value: T;
  test: (v: unknown) => v is T;
  transfer: (fv: unknown, ov: T) => T;
  type: string;
}

function buildDefaultType<T>(value: T, pattern?: RegExp): DefaultFormData<T> {
  const objectConstructor: string = Object.prototype.toString.call(value);
  const ConstructorFunction = new Object(value).constructor as ConstructorFunc<T>;
  const type = (/^\[object\s(?<type>[a-zA-Z0-9]*)\]$/g.exec(objectConstructor)?.groups?.type as string).toLowerCase();

  switch (objectConstructor) {
    case "[object Undefined]":
    case "[object Null]": {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? (pattern instanceof RegExp ? pattern.test(String(v)) : true) : false);
      const transfer = (fv: unknown, ov: T): T => (test(fv) ? fv : ov);
      return { value, test, transfer, type };
    }
    case "[object Boolean]":
    case "[object Number]":
    case "[object String]": {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? (pattern instanceof RegExp ? pattern.test(String(v)) : true) : false);
      const transfer = (fv: unknown, ov: T): T => {
        if (test(fv)) return fv;
        if (pattern instanceof RegExp || fv === undefined || fv === null) return ov;
        try {
          return ConstructorFunction(fv);
        } catch (error) {
          return ov;
        }
      };
      return { value, test, transfer, type };
    }
    case "[object Object]": {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? true : false);
      const transfer = (fv: unknown, ov: T): T => {
        if (test(fv)) return fv;
        else return ov;
      };
      return { value, test, transfer, type };
    }
    case "[object Array]": {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? true : false);
      const transfer = (fv: unknown, ov: T): T => {
        if (test(fv)) return fv;
        try {
          return Array.from(fv as Iterable<T> | ArrayLike<T>) as unknown as T;
        } catch (error) {
          return ov;
        }
      };
      return { value, test, transfer, type };
    }
    default: {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? true : false);
      const transfer = (fv: unknown, ov: T): T => (test(fv) ? fv : ov);
      return { value, test, transfer, type };
    }
  }
}
</script>

<style scoped lang="scss">
.size-full {
  width: 100%;
  height: 100%;
}
</style>
