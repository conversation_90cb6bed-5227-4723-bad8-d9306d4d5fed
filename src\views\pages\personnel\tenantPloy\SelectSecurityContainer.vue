<template>
  <el-dialog v-model="visible" :close-on-click-modal="false" append-to-body draggable :width="`${$width}px`" :before-close="handleCancel">
    <template #header>
      <div class="leading-[18px]">{{ props.title }}</div>
    </template>
    <template #default>
      <el-scrollbar ref="contentRef" :max-height="$height" :view-style="{ padding: '0 6px' }">
        <el-form ref="formRef" :model="form" label-position="top" require-asterisk-position="right" :status-icon="true" @keyup.ctrl.exact.enter.prevent.stop="handleSubmit()" @keydown.meta.exact.enter.prevent.stop="handleSubmit()" @submit.prevent="handleSubmit()">
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item prop="newContainerName" label="" style="margin-bottom: 0px">
                <el-scrollbar style="width: 100%; height: 300px" :height="300" v-loading="loadingTree">
                  <el-tree class="size-full tree-autosize tree-custom" :props="{ label: 'name', children: 'children', class: (data, node) => (!node.isLeaf && expandedNodes.has(data.id) ? `is-expanded_node` : '') }" :data="containerTree" node-key="id" :expand-on-click-node="false" :indent="0" :current-node-key="loadingTree ? '' : select" highlight-current :default-expanded-keys="loadingTree ? [] : [...expandedNodes]" @current-change="($event) => (select = $event.id)" @node-expand="($event) => expandedNodes.add($event.id)" @node-collapse="($event) => expandedNodes.delete($event.id)">
                    <template #default="{ node, data }: { node: Node; data: TreeItem }">
                      <div :class="['skeleton-line', { 'is-root': node.level === 1 }]" style="width: 100%">
                        <div v-if="!node.isLeaf" class="skeleton-icon">
                          <el-icon color="var(--el-text-color-placeholder)" @click.stop="() => expandNodes(!node.expanded, node)">
                            <SvgIconMinus v-if="node.expanded"></SvgIconMinus>
                            <SvgIconPlus v-else></SvgIconPlus>
                          </el-icon>
                        </div>
                        <el-button :type="node.isCurrent ? 'primary' : void 0">{{ data.name }}</el-button>
                      </div>
                    </template>
                  </el-tree>
                </el-scrollbar>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-scrollbar>
    </template>
    <template #footer>
      <div :style="{ padding: '0 10px 10px' }">
        <!-- <el-button type="warning" :loading="loading" @click="handleResets()">{{ $t("Resets") }}</el-button> -->
        <el-button type="default" :loading="loading" @click="handleCancel()">取消</el-button>
        <!-- <el-button type="primary" :loading="loading" @click="handleFinish()">{{ $t("Finish") }}</el-button> -->
        <el-button type="primary" :loading="loading" :disabled="params.containerId === form.containerId" @click="handleSubmit()">移动</el-button>
      </div>
      <div class="i-mdi-resize-bottom-right absolute bottom-0 right-0 h-20px w-[20px] cursor-se-resize" @mousedown.self="handleZoom"></div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, shallowReadonly, toValue, toRaw, nextTick, watch, computed } from "vue";
import { cloneDeep } from "lodash-es";
import { ElMessage } from "element-plus";
import SvgIconMinus from "@/assets/minus.vue";
import SvgIconPlus from "@/assets/plus.vue";
import type Node from "element-plus/es/components/tree/src/model/node";
import { getSafeContaineList } from "@/api/personnel";
/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// type RequestBase = "controller" | "keyword" | "paging" | "slot";
// type CreateItem = Omit<Required<typeof addItem extends (req: infer P) => any ? P : never>, RequestBase>;
// type SetterItem = Omit<Required<typeof setItem extends (req: infer P) => any ? P : never>, RequestBase>;
// type ModifyItem = Omit<Required<typeof modItem extends (req: infer P) => any ? P : never>, RequestBase>;
// type DeleteItem = Omit<Required<typeof delItem extends (req: infer P) => any ? P : never>, RequestBase>;
interface EditorItem /* extends CreateItem, SetterItem, ModifyItem */ {
  id: string;
  containerId: string;
  /** 旧的安全容器名称 */
  oldContainerName: string;
  /** 新的安全容器名称 */
  newContainerName: string;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const defaultForm = shallowReadonly<{ [Key in keyof EditorItem]: DefaultFormData<EditorItem[Key]> }>({
  /**
   * TODO: 此为表单初始默认数据和校验方法
   * 使用`buildDefaultType`方法构建默认值
   */
  id: buildDefaultType(""),
  containerId: buildDefaultType(""),
  oldContainerName: buildDefaultType(""),
  newContainerName: buildDefaultType(""),
});

const form = ref<EditorItem>(Object.entries(defaultForm).reduce<Partial<EditorItem>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {} as Partial<EditorItem>) as EditorItem);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
type TreeItem = (ReturnType<typeof getSafeContaineList> extends Promise<{ data: infer P }> ? P : never)[number];
const containerTree = ref<TreeItem[]>([]);
const loadingTree = ref(false);
const select = computed({ get: () => toValue(form).containerId, set: (v) => (form.value.containerId = v) });
watch(select, ($select) => {
  if (!$select) {
    if (loadingTree.value) return;
    form.value.newContainerName = "";
  } else {
    const getTreeById = (tree: typeof containerTree.value, id: string): TreeItem | void => {
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].id === id) return tree[i];
        if (tree[i].children instanceof Array && tree[i].children.length) {
          const find = getTreeById(tree[i].children, id);
          if (find) return find;
        }
      }
    };
    const find = getTreeById(toValue(containerTree), $select);
    let result = find ? find.name : "";
    if (form.value.newContainerName !== result) form.value.newContainerName = result;
  }
});
const expandedNodes = ref(new Set<string>());
async function expandNodes(expand: boolean, node: Node) {
  if (expand) {
    expandedNodes.value.add(node.key as string);
    nextTick(() => node.expand());
  } else {
    const unfoldChildren = async (children: Node[]) => {
      for (let i = 0; i < children.length; i++) {
        const item = children[i];
        const key = item.key as string;
        await unfoldChildren(item.childNodes);
        await nextTick();
        expandedNodes.value.delete(key);
        await nextTick();
        item.collapse();
      }
    };
    await unfoldChildren(node.childNodes);
    expandedNodes.value.delete(node.key as string);
    nextTick(() => node.collapse());
  }
}
/**
 * TODO: 首次打开初始化时执行
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(): Promise<void> {
  expandedNodes.value.clear();
}
/**
 * TODO: 每次重置表单时调用
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(): Promise<void> {
  try {
    containerTree.value = [];
    loadingTree.value = true;
    // select.value = "";
    const hasDisabled = (node: TreeItem[]) => {
      const $node = node.filter((v) => v.id !== toValue(params).disabled);
      for (let i = 0; i < $node.length; i++) {
        $node[i].children = hasDisabled($node[i].children instanceof Array ? $node[i].children : []);
      }
      return $node;
    };
    await nextTick();
    const { success, message, data } = await getSafeContaineList({});
    if (!success) throw Object.assign(new Error(message), { success, data });
    containerTree.value = hasDisabled(data instanceof Array ? data : []);
    await nextTick();
    // const $form = toValue(form);
    // if (!$form.newContainerName) {
    //   form.value.newContainerName = "";
    // } else {
    //   const getTreeByName = (tree: typeof containerTree.value, name: string): TreeItem | void => {
    //     for (let i = 0; i < tree.length; i++) {
    //       if (tree[i].name === name) return tree[i];
    //       if (tree[i].children instanceof Array && tree[i].children.length) {
    //         const find = getTreeByName(tree[i].children, name);
    //         if (find) return find;
    //       }
    //     }
    //   };

    //   const find = getTreeByName(toValue(containerTree), $form.newContainerName);
    //   let result = find ? find.id : "";
    //   if (select.value !== result) select.value = result;
    //   if (find && (find.children instanceof Array ? find.children : []).length) expandedNodes.value.add(find.id);
    // }
    const expanded = (list: TreeItem[], key: string, path: string[] = []): string[] => {
      for (let i = 0; i < list.length; i++) {
        if (list[i].id === key) return path;
        const find = expanded(list[i].children instanceof Array ? list[i].children : [], key, path.concat(list[i].id));
        if (find.length) return find;
      }
      return [];
    };
    const $expanded = expanded(containerTree.value, toValue(select));
    nextTick(() => {
      for (let i = 0; i < $expanded.length; i++) expandedNodes.value.add($expanded[i]);
    });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loadingTree.value = false;
  }
}

/**
 * TODO: 此处使用可对生成的数据操作
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: EditorItem): Promise<EditorItem> {
  return form;
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

const params = ref<Record<string, unknown>>({});
defineOptions({ name: "IndexEditor", inheritAttrs: false });
interface Props {
  title?: string;
  labelWidth?: number;
  width: number;
  height: number;
}
const props = withDefaults(defineProps<Props>(), { title: "", labelWidth: 80 });
const visible = ref(false);
const loading = ref(false);
const $width = ref(props.width / 2);
const $height = ref(props.height);
const handle = reactive({
  callback: undefined as ((form: EditorItem) => Promise<void>) | undefined,
  resolve: (form: EditorItem) => void form,
  reject: (err: Error) => void err,
});

async function getForm(form: Partial<Record<keyof EditorItem, unknown>>): Promise<EditorItem> {
  const $form: Partial<EditorItem> = {};
  await nextTick();
  for (const key of Reflect.ownKeys(defaultForm)) {
    const structure = Reflect.get(defaultForm, key);
    const $value = Reflect.get(form, key);
    if (!structure) continue;
    Reflect.set($form, key, cloneDeep(structure.test($value) ? $value : structure.transfer(Reflect.get($form, key), toRaw(structure.value))));
  }
  return await transformForm($form as Required<EditorItem>);
}

const formRef = ref<InstanceType<typeof import("element-plus").ElForm>>();
async function handleFinish() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  if (!(await new Promise((resolve) => $formRef.validate(resolve)))) return;
  try {
    loading.value = true;
    await nextTick();
    const $form = await getForm(toValue(form));
    if (handle.callback) {
      try {
        await handle.callback($form);
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return;
      }
    }
    close("fulfilled");
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}
async function handleSubmit() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  if (!(await new Promise((resolve) => $formRef.validate(resolve)))) return;
  try {
    loading.value = true;
    await nextTick();
    const $form = await getForm(toValue(form));
    if (handle.callback) {
      try {
        await handle.callback($form);
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
        return;
      }
    }
    close("fulfilled");
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}
async function handleCancel() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  try {
    loading.value = true;
    await nextTick();
    close("rejected");
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}
async function handleResets() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  try {
    loading.value = true;
    await nextTick();
    form.value = await getForm(toValue(params));
    await resetFormInit();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}
async function handleOpener() {
  const $formRef = toValue(formRef);
  if (!$formRef) return;
  if (toValue(loading)) return void 0;
  $formRef.clearValidate();
  try {
    loading.value = true;
    await nextTick();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    close("rejected");
  } finally {
    loading.value = false;
  }
}

function close(status: "fulfilled" | "rejected") {
  switch (status) {
    case "fulfilled":
      handle.resolve(toValue(form));
      break;
    case "rejected":
      handle.reject(Object.assign(new Error("Cancel"), toValue(form)));
      break;
    default:
      handle.reject(Object.assign(new Error("Error"), toValue(form)));
      break;
  }
  params.value = {};
  visible.value = false;
  nextTick(() => {
    loading.value = false;
    window.setTimeout(() => {
      handle.resolve = (form: EditorItem) => void form;
      handle.reject = (err: Error) => void err;
      handle.callback = undefined;
    });
  });
}

async function operate($handle: () => Promise<void>, $params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>): Promise<EditorItem> {
  if (toValue(loading)) return toValue(form);
  $width.value = props.width / 2 > 360 ? props.width / 2 : 360;
  $height.value = props.height;
  params.value = $params;
  loading.value = true;
  visible.value = true;
  const result = new Promise<EditorItem>((resolve, reject) => Object.assign(handle, { resolve, reject, callback }));
  await nextTick();
  try {
    await runningInit();
    loading.value = false;
    await handleResets();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    loading.value = false;
    await handleCancel();
  }
  await $handle();
  try {
    return result;
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : "Error");
  }
}

const contentRef = ref<InstanceType<typeof import("element-plus").ElScrollbar>>();
function handleZoom($event: MouseEvent) {
  const { wrapRef } = toValue(contentRef) || {};
  if (wrapRef) $height.value = wrapRef.offsetHeight;
  const w = toValue($width);
  const h = toValue($height);

  const max_w = props.width;
  const min_w = 360;

  const max_h = props.height;
  const min_h = 62;

  const controller = new AbortController();
  window.document.addEventListener(
    "mousemove",
    (e) => {
      e.preventDefault();
      nextTick(() => {
        const _w = w + (e.clientX - $event.clientX) * 2;
        $width.value = _w < max_w ? (_w > min_w ? _w : min_w) : max_w;
        const _h = h + (e.clientY - $event.clientY) * 1;
        $height.value = _h < max_h ? (_h > min_h ? _h : min_h) : max_h;
      });
    },
    { signal: controller.signal }
  );
  window.document.addEventListener(
    "mouseup",
    () => {
      controller.abort();
    },
    { once: true, signal: controller.signal }
  );
}

defineExpose({
  async finish($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleFinish, $params, callback);
  },
  async submit($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleSubmit, $params, callback);
  },
  async cancel($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleCancel, $params, callback);
  },
  async resets($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleResets, $params, callback);
  },
  async opener($params: Record<string, unknown>, callback?: (form: EditorItem) => Promise<void>) {
    return await operate(handleOpener, $params, callback);
  },
});

/*  */
interface ConstructorFunc<T> {
  new (value: unknown): T;
  (value: unknown): T;
}

interface DefaultFormData<T> {
  value: T;
  test: (v: unknown) => v is T;
  transfer: (fv: unknown, ov: T) => T;
  type: string;
}

function buildDefaultType<T>(value: T, pattern?: RegExp): DefaultFormData<T> {
  const objectConstructor: string = Object.prototype.toString.call(value);
  const ConstructorFunction = new Object(value).constructor as ConstructorFunc<T>;
  const type = (/^\[object\s(?<type>[a-zA-Z0-9]*)\]$/g.exec(objectConstructor)?.groups?.type as string).toLowerCase();

  switch (objectConstructor) {
    case "[object Undefined]":
    case "[object Null]": {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? (pattern instanceof RegExp ? pattern.test(String(v)) : true) : false);
      const transfer = (fv: unknown, ov: T): T => (test(fv) ? fv : ov);
      return { value, test, transfer, type };
    }
    case "[object Boolean]":
    case "[object Number]":
    case "[object String]": {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? (pattern instanceof RegExp ? pattern.test(String(v)) : true) : false);
      const transfer = (fv: unknown, ov: T): T => {
        if (test(fv)) return fv;
        if (pattern instanceof RegExp || fv === undefined || fv === null) return ov;
        try {
          return ConstructorFunction(fv);
        } catch (error) {
          return ov;
        }
      };
      return { value, test, transfer, type };
    }
    case "[object Object]": {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? true : false);
      const transfer = (fv: unknown, ov: T): T => {
        if (test(fv)) return fv;
        else return ov;
      };
      return { value, test, transfer, type };
    }
    case "[object Array]": {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? true : false);
      const transfer = (fv: unknown, ov: T): T => {
        if (test(fv)) return fv;
        try {
          return Array.from(fv as Iterable<T> | ArrayLike<T>) as unknown as T;
        } catch (error) {
          return ov;
        }
      };
      return { value, test, transfer, type };
    }
    default: {
      const test = (v: unknown): v is T => (Object.prototype.toString.call(v) === objectConstructor ? true : false);
      const transfer = (fv: unknown, ov: T): T => (test(fv) ? fv : ov);
      return { value, test, transfer, type };
    }
  }
}
</script>

<style scoped lang="scss">
.size-full {
  width: 100%;
  height: 100%;
}
.tree-autosize {
  :deep(.el-tree-node__content) {
    height: fit-content;
    padding: 3px 0;
    margin: 6px 0;
  }
}
.tree-custom {
  > :deep(.el-tree-node) {
    width: fit-content;
    > .el-tree-node__children::after {
      display: none !important;
    }
  }

  :deep(.el-tree-node) {
    margin-left: 12px;
    overflow: hidden;
    .skeleton-icon {
      overflow: hidden;
      cursor: pointer;
      position: absolute;
      z-index: 2;
      top: 0px;
      left: -24px;
      width: 24px;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      > * {
        background-color: var(--el-fill-color-blank);
      }
    }
    .el-tree-node__expand-icon {
      display: none;
    }
    > .el-tree-node__content {
      .skeleton-line {
        margin-left: 24px;
        position: relative;
        &::before {
          content: "";
          position: absolute;
          z-index: 1;
          top: -6px;
          left: -30px;
          display: block;
          width: 30px;
          pointer-events: none;
          height: calc(50% + 6px);
          border-bottom: 1px solid var(--el-text-color-placeholder);
        }
        &.is-root {
          &::before {
            left: -12px;
            width: 12px;
          }
        }
      }
    }
    &.is-expanded_node {
      > .el-tree-node__content {
        position: relative;
        &:not(:last-child) {
          &::before {
            content: "";
            position: absolute;
            z-index: 1;
            left: 12px;
            bottom: -10px;
            display: block;
            width: 18px;
            pointer-events: none;
            height: calc(50% + 6px);
            border-left: 1px solid var(--el-text-color-placeholder);
            // border-bottom: 1px solid var(--el-text-color-placeholder);
          }
        }
      }
      > .el-tree-node__children {
        position: relative;
        overflow: visible !important;

        &::before {
          content: "";
          position: absolute;
          z-index: 1;
          top: 0px;
          left: 12px;
          display: block;
          width: 18px;
          pointer-events: none;
          height: calc(100% - 25px);
          border-left: 1px solid var(--el-text-color-placeholder);
        }
        &::after {
          content: "";
          position: absolute;
          z-index: 1;
          top: -25px;
          left: 0px;
          display: block;
          width: 18px;
          pointer-events: none;
          height: 100%;
          border-left: 1px solid var(--el-fill-color-blank);
        }
      }
    }

    &:not(:last-child).is-expanded_node {
      > .el-tree-node__children::after {
        border-left: 1px solid var(--el-text-color-placeholder);
      }
    }
  }
}
</style>
