<template>
  <el-card class="auth">
    <el-scrollbar :height="height" width="100%">
      <div class="auth-top">
        <el-row :gutter="24" style="width: 100%">
          <el-col :span="12">
            <div>
              <h3>安全容器</h3>
              <div>
                <!-- <el-radio-group v-model="radio2" @click="handleClick"> -->
                <!-- :disabled="userInfo.hasPermission(PERMISSION.group509623135931203584.create)" -->
                <el-button @click="handleClick('重命名')" type="primary" :disabled="containerId && !userInfo.hasPermission('608444436526923776')">重命名</el-button>
                <el-button @click="handleClick('新增')" type="primary" :disabled="containerId && !userInfo.hasPermission('608444345678299136')">新增</el-button>
                <el-button @click="handleClick('移动')" type="primary" :disabled="containerId && !userInfo.hasPermission('608444345678299136')">移动</el-button>
                <el-button @click="handleClick('删除')" type="primary" :disabled="deleteDiasbled || !userInfo.hasPermission('608444610280161280')">删除</el-button>
                <!-- </el-radio-group> -->
                <!-- {{ deleteDiasbled }} -->
                <!-- {{!userInfo.hasPermission('608444610280161280')}} -->
                <treeAuth ref="treeAuthRef" :operationType="operationType" @clickItem="containerChange" :belong="'manage'"></treeAuth>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div>
              <h3>安全容器配置项</h3>
              <div>
                <el-radio-group v-model="containerItemTab">
                  <el-radio-button v-for="item in containerTabs" :label="containerTypes[item]" :key="item" @click.prevent="itemChange(containerTypes[item])" />
                </el-radio-group>
              </div>
              <div v-if="containerItemTab != '1'">
                <!-- {{ containerItemTab }} -->
                <el-table height="240" :data="tableData" style="width: 100%" :loading="authDataLoading">
                  <el-table-column label="名称" prop="name" />
                  <el-table-column label="客户" prop="tenantName" v-if="containerItemTab == '用户' || containerItemTab == '用户组'">
                    <template #default="{ row }">
                      <div m="4">
                        {{ row.tenantName + `[${row.tenantAbbreviation}]` }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="" prop="extend">
                    <template #default="{ row }">
                      <div m="4">
                        <el-button @click="dataMoveContainer('移动', row)">移动</el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination @size-change="itemChange(containerItemTab)" @current-change="itemChange(containerItemTab)" v-model:current-page="paging.pageNumber" v-model:page-size="paging.pageSize" layout="->, total, sizes, prev, pager, next, jumper" :total="paging.total" class="tw-w-full"></el-pagination>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="auth-main">
        <div v-for="item in authGroupList" :key="item.id">
          <h3>{{ item.name }} — {{ containerItem?.name }}</h3>
          <p style="text-align: right; padding: 10px 0; box-sizing: border-box"><el-button type="primary" :icon="Plus" :disabled="containerId && !userInfo.hasPermission('623053172327317504')" @click="addUserGroup(item)">添加用户组</el-button></p>
          <el-table :data="item.userGroupData" style="width: 100%" :loading="permissionsLoading" @expand-change="handleExpandChange" :row-key="getRowKeys" :expand-row-keys="expands">
            <el-table-column type="expand" width="300">
              <template #default="props">
                <div m="4">
                  <p style="text-align: right; padding: 10px 0; box-sizing: border-box">
                    <el-button type="primary" :disabled="props.row.extend || !userInfo.hasPermission('623053172327317504')" :icon="Plus" @click="setAuth({ ...props.row, ...item })">权限配置</el-button>
                  </p>

                  <el-table v-if="containerId === props.row.containerId || props.row.extend" :data="permissionsList" class="hide-table-header" border>
                    <el-table-column label="授权">
                      <template #default="{ row }"> {{ row.assign ? "授权" : "禁止授权" }} </template>
                    </el-table-column>
                    <el-table-column label="State" prop="state">
                      <template #default="{ row }">
                        {{ row.itemName }}
                      </template>
                    </el-table-column>
                    <el-table-column label="City" prop="city">
                      <template #default="{ row }">
                        <div class="flex gap-2" v-if="!row.isEdit">
                          <el-tag v-for="tag in row.permissions" :key="tag.id" type="info" style="margin: 5px 5px 0 0">
                            {{ tag.name }}
                          </el-tag>
                        </div>
                        <div class="flex gap-2" v-if="row.isEdit">
                          <el-select v-model="row.permissionIds" multiple>
                            <el-option v-for="tag in row.checkPermission" :key="tag.id" :value="tag.id" :label="tag.name"> </el-option>
                          </el-select>
                          <!-- <el-tag v-for="tag in row.permissions" :key="tag.id" type="info" style="margin: 5px 5px 0 0">
                            {{ tag.name }}
                          </el-tag> -->
                        </div>
                      </template>
                    </el-table-column>

                    <el-table-column label="Zip" prop="zip">
                      <template #default="{ row }">
                        <el-button v-if="!row.isEdit" @click="eidtorPermission(row)" :disabled="props.row.extend || !userInfo.hasPermission('608455496017379328')">编辑</el-button>
                        <el-button v-if="row.isEdit" @click="savePermission(row, props.row)" :disabled="props.row.extend || !userInfo.hasPermission('608455496017379328')">保存</el-button>
                        <el-button v-if="!row.isEdit" @click="deletePermission(row, props.row)" :disabled="props.row.extend || !userInfo.hasPermission('608455858757566464')">删除</el-button>
                        <el-button v-if="row.isEdit" @click="row.isEdit = false">取消</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="名称" prop="userGroupName" />
            <el-table-column label="客户" prop="tenantName">
              <template #default="{ row }">
                <div m="4">
                  {{ row.tenantName + `[${row.tenantAbbreviation}]` }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="继承" prop="extend">
              <template #default="{ row }">
                <div m="4">
                  <el-icon v-if="row.extend"><Check /></el-icon>
                  <!-- {{ row.extend + `[${row.tenantAbbreviation}]` }} -->
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-scrollbar>
    <Editor title="安全容器" ref="editorRef"></Editor>
    <EditorUser title="添加用户组" ref="EditorUserRef" @confirm="confirm"></EditorUser>
    <moveContainer ref="moveContainerRef" @moveContainerItem="moveContainerItem"></moveContainer>
    <containerDataMove ref="containerDataMoveRef" @containerDataMoveItem="containerDataMoveItem"></containerDataMove>
  </el-card>
</template>

<script setup lang="ts" generic="T extends object">
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox, ElTag } from "element-plus";
import { Plus, Check } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";
import { useSiteConfig } from "@/stores/siteConfig";
import getUserInfo from "@/utils/getUserInfo";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
import { createSafeContaineItem, deleteSafeContaineItem, editSafeContaine } from "@/api/personnel";
import { getPermissionGroups, getPermissionItems, getAppAuth } from "@/api/application";
import { getAuthGroupRelationUserGroup, getAssigned_permissions, deletable } from "@/api/authConfig";
import { sizes } from "@/utils/common";
import { faBan, faPlusSquare } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import { Zone } from "@/utils/zone";
import { bindFormBox } from "@/utils/bindFormBox";
import { EditorType } from "@/views/common/interface";
import type { TabsPaneContext } from "element-plus";
import containerDataMove from "./containerDataMove.vue";

// import { getDeviceList as getItemList } from "@/views/pages/apis/device";
// import { getRegionTree } from "@/views/pages/apis/regionManage";

import treeAuth from "@/components/treeAuth/authManageTree.vue";

import EditorUser from "./EditorUser.vue";
import Editor from "./Editor.vue";
import moveContainer from "./moveContainer.vue";
import { editorPermissions, cancelItemPermission, getAllPermissions, getContainersResource } from "@/api/authConfig";
import { getTenantList, getAllotUserList, getRegionTree, getUserGroupsList, getSupport_notesList, getSupplierList, getLineSupplierList, getAlarmClassificationList, getdeviceTypeList, getdeviceGroupList, getContacts, getLocationsTenantCurrent, getDeviceList, getSlaConfigByPage } from "@/api/componentsList";
import { moveRegionsList, moveDeviceGroupsList, moveContactsList, moveAlarmClassificationList, moveSlaList, moveLocationList, moveDeviceList, moveVendorsList, moveSupportNotesList, moveDeviceTypeList, moveUserGroupList, moveTenantsList, moveUserList } from "@/api/componentsList";

const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "global" });
const { t } = useI18n();

const route = useRoute();
const router = useRouter();
const userInfo = getUserInfo();
defineOptions({ name: "TenantGovern" });

const siteConfig = useSiteConfig();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));
const radio2 = ref("1");
const containerItemTab = ref("1");

const editorRef = ref<InstanceType<typeof Editor>>();
const EditorUserRef = ref<InstanceType<typeof EditorUser>>();
const treeAuthRef = ref<InstanceType<typeof treeAuth>>();
const moveContainerRef = ref<InstanceType<typeof moveContainer>>();
const containerDataMoveRef = ref<InstanceType<typeof containerDataMove>>();

const containerId = ref("");

/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
// const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */

// console.log(userInfo);
const deleteDiasbled = ref(true);
//容器点击事件
const containerItem = ref("");
async function containerChange(val) {
  // console.log(val);
  tableData.value = [];
  containerId.value = val.id;
  containerItem.value = val;
  containerItemTab.value = 1;
  await queryData();
  await deletable({ id: val.id }).then((res) => {
    // console.log(res);
    deleteDiasbled.value = !res.data;
  });
  authGroupList.value.forEach((v, i) => {
    v.userGroupData = [];
    getAuthGroupRelationUserGroup({
      containerId: containerId.value,
      permissionGroupId: v.id,
    }).then((res) => {
      if (res.success) {
        v.userGroupData = [...res.data];
      }
    });
  });
  //区域接口
  // await handleRefreshRegionTable();
  //设备列表
}
const operationType = ref("");
//tab按钮
async function handleClick(value) {
  // if(value==='c')
  // console.log(value);
  operationType.value = value;
  switch (value) {
    case "新增":
      let parentId = containerId.value;
      await editorRef.value.open({ name: "", operationType: "添加" }, async (form) => {
        // console.log(form);
        const { success, message, data } = await createSafeContaineItem({ name: form.name, parentId: parentId });
        if (!success) throw Object.assign(new Error(message), { success, data });
        ElMessage.success(`${i18n.t("axios.Operation successful")}`);
        treeAuthRef.value.getSafeContaine();
        treeAuthRef.value.treeId = containerId.value;
      });
      break;
    case "重命名":
      await editorRef.value.open({ name: containerItem.value?.name, operationType: "编辑" }, async (form) => {
        // console.log(form);
        const { success, message, data } = await editSafeContaine({ name: form.name, id: containerId.value });
        if (!success) throw Object.assign(new Error(message), { success, data });
        ElMessage.success(`${i18n.t("axios.Operation successful")}`);
        containerItem.value.name = data.name;
        treeAuthRef.value.getSafeContaine();
        treeAuthRef.value.treeId = containerId.value;
      });
      break;
    case "删除":
      let id = containerId.value;
      ElMessageBox.confirm(`确定删除${treeAuthRef.value.treeItem.name}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const { success, data, message } = await deleteSafeContaineItem({ id });
            if (!success) throw new Error(message);
            ElMessage.success("操作成功");
            treeAuthRef.value.getSafeContaine();
            treeAuthRef.value.treeId = containerItem.value.parentId || containerId.value;
          } catch (error) {
            error instanceof Error && ElMessage.error(error.message);
          }
        })
        .catch((err) => {
          //
        });
      break;
    case "移动":
      // console.log(moveContainerRef.value)
      moveContainerRef.value.open(containerId.value);
      break;
  }
  // console.log(radio2.value, value);
}
//打开移动数据安全容器弹框
const dataId = ref("");
async function dataMoveContainer(type: string, item: object) {
  // console.log(type, item);
  dataId.value = item.id;
  containerDataMoveRef.value.open(item.containerId);
}
//移动数据安全容器
async function containerDataMoveItem(val) {
  // console.log(containerItemTab, val);
  try {
    switch (containerItemTab.value) {
      case "客户":
        await moveTenantsList({
          id: dataId.value,
          oldContainerName: val.oldContainer.name,
          newContainerName: val.newContainer.name,
          containerId: val.newContainer.id,
        })
          .then((res) => {
            if (res.success) {
              ElMessage.success("操作成功");
            }
          })
          .catch((error) => {
            error instanceof Error && ElMessage.error(error.message);
          });
        break;
      case "用户":
        await moveUserList({
          id: dataId.value,
          oldContainerName: val.oldContainer.name,
          newContainerName: val.newContainer.name,
          containerId: val.newContainer.id,
        })
          .then((res) => {
            if (res.success) {
              ElMessage.success("操作成功");
            }
          })
          .catch((error) => {
            error instanceof Error && ElMessage.error(error.message);
          });
        break;
      case "用户组":
        await moveUserGroupList({
          id: dataId.value,
          oldContainerName: val.oldContainer.name,
          newContainerName: val.newContainer.name,
          containerId: val.newContainer.id,
        })
          .then((res) => {
            if (res.success) {
              ElMessage.success("操作成功");
            }
          })
          .catch((error) => {
            error instanceof Error && ElMessage.error(error.message);
          });
        break;
      case "优先级矩阵":
        break;
      case "SLA":
        await moveSlaList({
          id: dataId.value,
          oldContainerName: val.oldContainer.name,
          newContainerName: val.newContainer.name,
          containerId: val.newContainer.id,
        })
          .then((res) => {
            if (res.success) {
              ElMessage.success("操作成功");
            }
          })
          .catch((error) => {
            error instanceof Error && ElMessage.error(error.message);
          });
        break;
      case "设备":
        await moveDeviceList({
          id: dataId.value,
          oldContainerName: val.oldContainer.name,
          newContainerName: val.newContainer.name,
          containerId: val.newContainer.id,
        })
          .then((res) => {
            if (res.success) {
              ElMessage.success("操作成功");
            }
          })
          .catch((error) => {
            error instanceof Error && ElMessage.error(error.message);
          });
        break;
      case "区域":
        await moveRegionsList({
          id: dataId.value,
          oldContainerName: val.oldContainer.name,
          newContainerName: val.newContainer.name,
          containerId: val.newContainer.id,
        })
          .then((res) => {
            if (res.success) {
              ElMessage.success("操作成功");
            }
          })
          .catch((error) => {
            error instanceof Error && ElMessage.error(error.message);
          });
        break;
      case "场所":
        await moveLocationList({
          id: dataId.value,
          oldContainerName: val.oldContainer.name,
          newContainerName: val.newContainer.name,
          containerId: val.newContainer.id,
        })
          .then((res) => {
            if (res.success) {
              ElMessage.success("操作成功");
            }
          })
          .catch((error) => {
            error instanceof Error && ElMessage.error(error.message);
          });
        break;
      case "联系人":
        await moveContactsList({
          id: dataId.value,
          oldContainerName: val.oldContainer.name,
          newContainerName: val.newContainer.name,
          containerId: val.newContainer.id,
        })
          .then((res) => {
            if (res.success) {
              ElMessage.success("操作成功");
            }
          })
          .catch((error) => {
            error instanceof Error && ElMessage.error(error.message);
          });
        break;
      case "设备分组":
        await moveDeviceGroupsList({
          id: dataId.value,
          oldContainerName: val.oldContainer.name,
          newContainerName: val.newContainer.name,
          containerId: val.newContainer.id,
        })
          .then((res) => {
            if (res.success) {
              ElMessage.success("操作成功");
            }
          })
          .catch((error) => {
            error instanceof Error && ElMessage.error(error.message);
          });
        break;
      case "设备类型":
        await moveDeviceTypeList({
          id: dataId.value,
          oldContainerName: val.oldContainer.name,
          newContainerName: val.newContainer.name,
          containerId: val.newContainer.id,
        })
          .then((res) => {
            if (res.success) {
              ElMessage.success("操作成功");
            }
          })
          .catch((error) => {
            error instanceof Error && ElMessage.error(error.message);
          });
        break;
      case "告警分类":
        await moveAlarmClassificationList({
          id: dataId.value,
          oldContainerName: val.oldContainer.name,
          newContainerName: val.newContainer.name,
          containerId: val.newContainer.id,
        })
          .then((res) => {
            if (res.success) {
              ElMessage.success("操作成功");
            }
          })
          .catch((error) => {
            error instanceof Error && ElMessage.error(error.message);
          });
        break;
      case "供应商":
        await moveVendorsList({
          id: dataId.value,
          oldContainerName: val.oldContainer.name,
          newContainerName: val.newContainer.name,
          containerId: val.newContainer.id,
        })
          .then((res) => {
            if (res.success) {
              ElMessage.success("操作成功");
            }
          })
          .catch((error) => {
            error instanceof Error && ElMessage.error(error.message);
          });
        break;
      case "行动策略":
        await moveSupportNotesList({
          id: dataId.value,
          oldContainerName: val.oldContainer.name,
          newContainerName: val.newContainer.name,
          containerId: val.newContainer.id,
        })
          .then((res) => {
            if (res.success) {
              ElMessage.success("操作成功");
            }
          })
          .catch((error) => {
            error instanceof Error && ElMessage.error(error.message);
          });
        break;
    }
  } catch {
    // ElMessage.warning("");
  } finally {
    containerDataMoveRef.value.dialogVisible = false;
    await queryData();
    await itemChange(containerItemTab.value);
  }
}
//移动安全容器
function moveContainerItem() {
  treeAuthRef.value.getSafeContaine();
}
const authGroupList = ref([]);
//获取当前应用下的权限配置组
async function getAuthGroups() {
  await getPermissionGroups({ appId: (siteConfig.baseInfo || {}).app }).then((res) => {
    // console.log(res);
    if (res.success) {
      authGroupList.value = res.data;
    }
  });
}
//添加用户组弹框
function addUserGroup(row) {
  EditorUserRef.value.open({ ...row, containerId: containerId.value });
}
function setAuth(row) {
  EditorUserRef.value.openAuth({ ...row, containerId: containerId.value });
}
const getRowKeys = (row) => {
  //row是当前行的数据
  //给每行绑定唯一的标识
  return row.assignId;
};
const permissionsList = ref([]);
//用户组列表展开 查询所属权限
const expands = ref([]);
async function handleExpandChange(row, expandedRows) {
  getPermissions(row);
  if (expandedRows.length) {
    //展开
    expands.value = []; //先干掉之前展开的行
    if (row) {
      expands.value.push(row.assignId); //push新的行 (原理有点类似防抖)
    }
  } else {
    expands.value = []; //折叠 就清空expand-row-keys对应的数组
  }
}

const permissionsLoading = ref(true);
async function getPermissions(row) {
  permissionsList.value = [];
  permissionsLoading.value = true;
  // console.log("行展开状态变化:", row, expandedRows);
  await getAssigned_permissions({
    groupAssignId: row.assignId,
    extendView: row.extend,
  }).then((res) => {
    // console.log(res, 666);

    if (res.success) {
      permissionsLoading.value = false;

      let arr = [...res.data];

      arr.forEach((v, i) => {
        permissionsList.value.push({ ...v, permissionIds: [] });
      });
    }
  });
}
//子组件回调事件
async function confirm(row) {
  // console.log(123456, row);
  // getPermissions({ assignId: row.groupAssignId });

  await getAuthGroups();
  setTimeout(() => {
    authGroupList.value.forEach((v, i) => {
      v.userGroupData = [];
      getAuthGroupRelationUserGroup({
        containerId: containerId.value,
        permissionGroupId: v.id,
      }).then((res) => {
        if (res.success) {
          v.userGroupData = [...res.data];
        }
      });
    });
  }, 2000);
  await getPermissions({ assignId: expands.value[0] });
}
//编辑权限
async function eidtorPermission(row) {
  permissionsLoading.value = true;
  row.isEdit = true;
  row.permissionIds = [];
  row.checkPermission = [];
  // console.log(row.permissionIds);
  row.permissions.forEach((v, i) => {
    row.permissionIds.push(v.id);
  });
  await getAllPermissions({ appId: (siteConfig.baseInfo || {}).app }).then((res) => {
    // console.log(res);
    if (res.success) {
      let arr = [...res.data];
      permissionsLoading.value = false;
      arr.forEach((v, i) => {
        if (v.itemId === row.itemId && v.enabled) {
          row.checkPermission.push(v);
        }
      });

      // authGroupList.value = res.data;
    }
  });
}
//保存编辑后的内容
async function savePermission(row, item) {
  const list = [];
  row.permissionIds.forEach((v, i) => {
    row.permissions.forEach((cv) => {
      if (cv.id === v) {
        list.push({
          assign: cv.assign,
          mfa: cv.assign,
          extend: cv.extend,
          permissionId: cv.id,
        });
      }
    });
    list.push({
      assign: true,
      mfa: false,
      permissionId: v,
      extend: item.extend,
    });
  });
  for (let i = 0; i < list.length; i++) {
    for (let j = i + 1; j < list.length; j++) {
      if (list[i].permissionId == list[j].permissionId) {
        list.splice(j, 1);
        j--; // 这个别忘咯
      }
    }
  }

  // console.log(row);
  await editorPermissions({ list: list, itemId: row.itemId, groupAssignId: expands.value[0] })
    .then((res) => {
      // console.log(res);
      if (res.success) {
        ElMessage.success("操作成功");
        getPermissions({ assignId: expands.value[0] });
        row.isEdit = false;
      }
    })
    .catch((err) => {
      err instanceof Error && ElMessage.error(err.message);
    });
}
//删除权限
async function deletePermission(row, item) {
  console;
  ElMessageBox.confirm(`确定删除${row.itemName}所有权限吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      await cancelItemPermission({ permissionItemId: row.itemId, groupAssignId: expands.value[0], deleteAssigned: row.assign })
        .then((res) => {
          // console.log(res);
          if (res.success) {
            ElMessage.success("操作成功");
            // row.isEdit = false;
            permissionsLoading.value = true;

            permissionsList.value = [];
            getAssigned_permissions({
              groupAssignId: expands.value[0],
            }).then((res) => {
              // console.log(res, 666);

              if (res.success) {
                let arr = [...res.data];
                permissionsLoading.value = false;

                arr.forEach((v, i) => {
                  permissionsList.value.push({ ...v, permissionIds: [] });
                });
              }
            });
          }
        })
        .catch((err) => {
          err instanceof Error && ElMessage.error(err.message);
        });
    })
    .catch((err) => {
      //
    });
}
const tableData = ref([]);
const authDataLoading = ref(false);
const paging = reactive({
  pageNumber: 1,
  pageSize: 10,
  total: 0,
});
const containerTabs = ref([]);
const containerTypes = ref({
  "iam.tenant": "客户",
  "iam.user": "用户",
  "iam.user_group": "用户组",
  "cmdb.resource": "设备",
  "cmdb.region": "区域",
  "cmdb.location": "场所",
  "cmdb.contact": "联系人",
  "cmdb.group": "设备分组",
  "cmdb.resourceType": "设备类型",
  "cmdb.alertClassification": "告警分类",
  "cmdb.vendor": "供应商",
  "cmdb.supportNote": "行动策略",
  "ec.priorityMatrix": "优先级矩阵",
  "ec.sla": "SLA",
  //邮件
});
//获取安全容器关联的设备、区域、场所、联系人、设备分组、线路供应商、设备供应商、设备类型、告警分类、行动策略、客户管理、用户管理、用户组管理
async function itemChange(type) {
  // console.log(type);
  containerItemTab.value = type;

  // getSupport_notesList,
  // getSupplierList,
  // getLineSupplierList,
  // getAlarmClassificationList,
  // getdeviceTypeList,
  // getdeviceGroupList,
  // getContacts,
  // getLocationsTenantCurrent,

  tableData.value = [];
  authDataLoading.value = true;
  switch (type) {
    case "客户":
      getTenantList({ containerId: containerId.value, pageNumber: paging.pageNumber, pageSize: paging.pageSize }).then((res) => {
        if (res.success) {
          authDataLoading.value = false;
          tableData.value = res.data;
          paging.total = res.total * 1;
        }
      });
      break;
    case "用户":
      getAllotUserList({ containerId: containerId.value, pageNumber: paging.pageNumber, pageSize: paging.pageSize }).then((res) => {
        if (res.success) {
          authDataLoading.value = false;
          tableData.value = res.data;
          paging.total = res.total * 1;
        }
      });
      break;
    case "用户组":
      getUserGroupsList({ containerId: containerId.value, pageNumber: paging.pageNumber, pageSize: paging.pageSize }).then((res) => {
        if (res.success) {
          authDataLoading.value = false;
          tableData.value = res.data;
          paging.total = res.total * 1;
        }
      });
      break;
    case "优先级矩阵":
      break;
    case "SLA":
      getSlaConfigByPage({ containerId: containerId.value, pageNumber: paging.pageNumber, pageSize: paging.pageSize }).then((res) => {
        if (res.success) {
          authDataLoading.value = false;
          tableData.value = res.data;
          paging.total = res.total * 1;
        }
      });
      break;
    case "设备":
      getDeviceList({ containerId: containerId.value, pageNumber: paging.pageNumber, pageSize: paging.pageSize }).then((res) => {
        if (res.success) {
          authDataLoading.value = false;
          tableData.value = res.data;
          paging.total = res.total * 1;
        }
      });
      break;
    case "区域":
      const { success, data, message, page, size, total } = await getRegionTree({ pageNumber: paging.pageNumber, pageSize: paging.pageSize, parentId: "-1", sort: "createdTime,desc", containerId: containerId.value });

      if (!success) throw new Error(message);
      tableData.value = setTreeDepth(data instanceof Array ? data : []);
      paging.total = total * 1;
      break;
    case "场所":
      getLocationsTenantCurrent({ containerId: containerId.value, pageNumber: paging.pageNumber, pageSize: paging.pageSize }).then((res) => {
        if (res.success) {
          authDataLoading.value = false;
          tableData.value = res.data;
          paging.total = res.total * 1;
        }
      });
      break;
    case "联系人":
      getContacts({ containerId: containerId.value, pageNumber: paging.pageNumber, pageSize: paging.pageSize }).then((res) => {
        if (res.success) {
          authDataLoading.value = false;
          tableData.value = res.data;
          paging.total = res.total * 1;
        }
      });
      break;
    case "设备分组":
      getdeviceGroupList({ containerId: containerId.value, pageNumber: paging.pageNumber, pageSize: paging.pageSize }).then((res) => {
        if (res.success) {
          authDataLoading.value = false;
          tableData.value = res.data;
          paging.total = res.total * 1;
        }
      });
      break;
    case "设备类型":
      getdeviceTypeList({ containerId: containerId.value, pageNumber: paging.pageNumber, pageSize: paging.pageSize }).then((res) => {
        if (res.success) {
          authDataLoading.value = false;
          tableData.value = res.data;
          paging.total = res.total * 1;
        }
      });
      break;
    case "告警分类":
      getAlarmClassificationList({ containerId: containerId.value, pageNumber: paging.pageNumber, pageSize: paging.pageSize }).then((res) => {
        if (res.success) {
          authDataLoading.value = false;
          tableData.value = res.data;
          paging.total = res.total * 1;
        }
      });
      break;
    case "供应商":
      getSupplierList({ containerId: containerId.value, pageNumber: paging.pageNumber, pageSize: paging.pageSize }).then((res) => {
        if (res.success) {
          authDataLoading.value = false;
          tableData.value = res.data;
          paging.total = res.total * 1;
        }
      });
      getLineSupplierList({ containerId: containerId.value, pageNumber: paging.pageNumber, pageSize: paging.pageSize }).then((res) => {
        if (res.success) {
          authDataLoading.value = false;
          tableData.value = res.data;
          paging.total = res.total * 1;
        }
      });
      break;
    case "行动策略":
      getSupport_notesList({ containerId: containerId.value, pageNumber: paging.pageNumber, pageSize: paging.pageSize }).then((res) => {
        if (res.success) {
          authDataLoading.value = false;
          tableData.value = res.data;
          paging.total = res.total * 1;
        }
      });
      break;
  }
}
//获取安全容器关联的列表类型
async function queryData() {
  containerTabs.value = [];
  await getContainersResource({ id: containerId.value }).then((res) => {
    // console.log(res);
    if (res.success) {
      containerTabs.value = res.data;
      containerItemTab.value = containerTypes.value[res.data[0]];
      itemChange(containerItemTab.value);
    }
  });
  // const [{ success, message, data, page, size, total } /* , { success: contactGroupSuccess, message: contactGroupMessage, data: contactGroupData } */] = await Promise.all([
  //   /* 获取设备列表 */

  //   await getItemList({ active: true, name: "", paging: { pageNumber: paging.pageNumber, pageSize: paging.pageSize }, containerId: containerId.value }),

  //   /* 联系人 */
  //   // getContactByContactGroup({}),
  // ]);
  // // state.loading = false;
}
function setTreeDepth(data: [], depth = 0) {
  return data.map((v) => Object.assign(v, { depth, children: setTreeDepth(v.children instanceof Array ? v.children : [], depth + 1) }));
}
function beforeCreate() {}
function created() {}
function beforeMount() {}
async function mounted() {
  await getAuthGroups();
  // console.log(containerItem.value)
  // console.log(treeAuthRef.value)
  // treeAuthRef.value.getSafeContaine()
  // treeAuthRef.value.setTreeDepth()
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}

/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.el-message {
  z-index: 9999 !important; /* 确保这个值高于其他元素 */
}
.hide-table-header {
  :deep(.el-table__header-wrapper) {
    display: none;
  }
}
.auth-top {
  width: 100%;
  height: 400px;
  > .el-row {
    height: 100%;
    > .el-col > div {
      width: 100%;
      height: 100%;
      border: 1px solid #ddd;
      > h3 {
        font-size: 16px;
        border-bottom: 1px solid #ddd;
        padding: 5px 10px;
        box-sizing: border-box;
        background: rgb(245, 245, 245);
      }
      > div {
        padding: 10px 15px;
        box-sizing: border-box;
      }
    }
  }
}
.auth-main {
  padding: 20px 0 0 0;
  box-sizing: border-box;
  > div {
    padding: 15px;
    box-sizing: border-box;
    > h3 {
      padding: 5px 10px;
      box-sizing: border-box;
      background: rgb(245, 245, 245);
    }
  }
}
</style>

<style scoped lang="scss">
.auth {
  width: 100%;
  :deep(.el-tree-node__expand-icon) {
    color: #fff;
  }
  :deep(.el-tree) {
    /* 修改选中节点的背景颜色 */
    .el-tree-node__content:hover {
      background-color: #fff; /* 你想要的任何颜色 */
    }

    /* 如果你想要修改当前选中节点的背景颜色，而不仅仅是鼠标悬停时 */
    .el-tree-node__content.is-current {
      background-color: none; /* 你想要的任何颜色 */
    }
    .el-tree-node__content {
      height: 50px;
    }
  }
  .pageTree {
    height: 300px; /* 设置树的高度 */
    overflow-y: scroll; /* 添加垂直滚动条 */
  }
}
.tree-main {
  display: flex;
  align-items: center;
  > .tree-icon {
    width: 16px;
    height: 16px;
    margin-right: 5px;
  }
  > .tree-name {
    display: flex;
    border: 1px solid #ddd;
    padding: 5px;
    align-items: center;
    box-sizing: border-box;
    border-radius: 5px;
  }
  > .on {
    display: flex;
    border: 1px solid #ddd;
    padding: 5px;
    align-items: center;

    box-sizing: border-box;
    background: #409eff;
    color: #fff;
    border-radius: 5px;
  }
}
</style>
