<template>
  <div style="width: 100%; height: 100%; overflow: hidden">
    <el-scrollbar ref="scrollbarRef" height="100%" @scroll="scrollbar = $event">
      <el-row class="permission" :gutter="12">
        <!--  :style="{ height: `${height + 62 > 280 ? height + 62 : 280}px` }" -->
        <el-col :md="10" :span="24" class="toolbar">
          <el-card shadow="never" class="size-full" :body-style="{ padding: '6px 12px', height: 'calc(100% - 31px)' }">
            <template #header>
              <div class="flex-row">
                <span class="card-title">安全容器</span>
              </div>
            </template>
            <template #default>
              <el-button-group size="small" style="margin-bottom: 6px">
                <el-button @click="renameSafe<PERSON>ontaine({ name: selectNode.name, containerId: select || '', parentId: selectNode.parentId })" :loading="loading" :disabled="!select || !userInfo.hasPermission(安全管理中心_权限管理_编辑)" type="primary">重命名</el-button>
                <el-button @click="createSafeContaine({ name: selectNode.name, containerId: select || '', parentId: selectNode.parentId })" :loading="loading" :disabled="!select || !userInfo.hasPermission(安全管理中心_权限管理_新增)" type="primary">新增</el-button>
                <el-button @click="movingSafeContaine({ name: selectNode.name, containerId: select || '', parentId: selectNode.parentId })" :loading="loading" :disabled="!select || !userInfo.hasPermission(安全管理中心_权限管理_新增)" type="primary">移动</el-button>
                <el-button @click="deleteSafeContaine({ name: selectNode.name, containerId: select || '', parentId: selectNode.parentId })" :loading="loading" :disabled="!select || deleteDiasbled || !userInfo.hasPermission(安全管理中心_权限管理_删除)" type="primary">删除</el-button>
              </el-button-group>
              <div v-loading="loading">
                <el-scrollbar :height="height > 357 ? height : 357" max-height="calc(100vh - 260px)">
                  <el-tree v-if="userInfo.hasPermission(安全管理中心_权限管理_安全)" ref="containerRef" class="size-full tree-autosize tree-custom" :props="{ label: 'name', children: 'children', class: (data, node) => (!node.isLeaf && expandedNodes.has(data.id) ? `is-expanded_node` : '') }" :data="containerTree" node-key="id" :expand-on-click-node="false" :indent="0" :current-node-key="loading ? '' : select" highlight-current :default-expanded-keys="loading ? [] : [...expandedNodes]" @current-change="($event) => (select = $event.id)" @node-expand="($event) => expandedNodes.add($event.id)" @node-collapse="($event) => expandedNodes.delete($event.id)">
                    <template #default="{ node, data }: { node: Node; data: TreeItem }">
                      <div :class="['skeleton-line', { 'is-root': node.level === 1 }]" style="width: 100%">
                        <div v-if="!node.isLeaf" class="skeleton-icon">
                          <el-icon color="var(--el-text-color-placeholder)" @click.stop="() => expandNodes(!node.expanded, node)">
                            <SvgIconMinus v-if="node.expanded"></SvgIconMinus>
                            <SvgIconPlus v-else></SvgIconPlus>
                          </el-icon>
                        </div>
                        <el-button :type="node.isCurrent ? 'primary' : void 0">{{ data.name }}</el-button>
                      </div>
                    </template>
                  </el-tree>
                </el-scrollbar>
              </div>
            </template>
          </el-card>
        </el-col>
        <el-col :md="14" :span="24" class="toolbar">
          <el-card shadow="never" class="size-full" :body-style="{ padding: '0', height: 'calc(100% - 31px)' }">
            <template #header>
              <div class="flex-row" style="justify-content: flex-start">
                <span class="card-title" style="margin-right: 0">安全容器配置项{{ selectNode.name ? ` — ${selectNode.name}` : "" }}</span>
                <!-- <span class="card-extend">{{ selectNode.name }}</span> -->
              </div>
            </template>
            <template #default>
              <el-empty v-if="!select" description="未选择安全容器" :image-size="60" class="size-full"></el-empty>
              <el-empty v-else-if="!containerTypeOptions.length" v-loading="loadingContainerType" description="无配置项数据" :image-size="60" class="size-full"></el-empty>
              <!-- <el-tabs v-else v-model="active" type="card" :before-leave="refreshContactData" class="size-full" style="--el-tabs-header-height: 32px; overflow: hidden"> -->
              <el-tabs v-else v-model="active" type="card" :before-leave="refreshContactData" class="size-full" :style="{ '--el-tabs-header-height': `26px`, 'overflow': 'hidden' }">
                <el-tab-pane v-for="item in containerTypeOptions" :disabled="item.loading || item.value === ContactType.UNKNOWN" :key="item.value" :label="item.label" :name="item.value || item.label">
                  <template v-if="active === item.value">
                    <el-table v-loading="item.loading" :key="`table_${item.value}`" :data="item.list" :height="330" max-height="calc(100vh - 260px)" stripe border size="small" row-key="id" style="margin: 0 2px; width: calc(100% - 4px)">
                      <!-- <el-table-column :resizable="false" type="selection" :width="80"></el-table-column> -->
                      <el-table-column v-for="col in item.column" :key="col.value" :show-overflow-tooltip="true" :resizable="false" :prop="col.value" :label="col.label" :formatter="(r, _c, v, i) => col.formatter(v, r, i)"></el-table-column>
                      <el-table-column label="操作" :resizable="false" :width="100">
                        <template #default="{ row }: { row: BaseDataItem }">
                          <el-button :loading="item.loading" size="small" @click="setSelectContainer({ id: row.id, containerId: row.containerId, oldContainerName: selectNode.name, newContainerName: selectNode.name }, item.move)">移动</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-pagination v-model:current-page="item.page" v-model:page-size="item.size" :key="`pagination_${item.value}`" small layout="total, ->, sizes, prev, pager, next" :total="item.total" :pager-count="5" @size-change="refreshContactData(item.value)" @current-change="refreshContactData(item.value)" style="margin: 5px 12px 6px 12px"></el-pagination>
                  </template>
                </el-tab-pane>
              </el-tabs>
            </template>
          </el-card>
        </el-col>
      </el-row>
      <div style="height: calc(100% - 280px); margin-top: 12px">
        <el-row v-loading="loadingAuthGroup" class="permission" :gutter="12">
          <template v-for="authGroup in authGroupList" :key="authGroup.id">
            <el-col v-if="authGroup.enabled" :span="24" class="context">
              <el-card shadow="never" class="size-full" :body-style="{ padding: '1px', height: 'calc(100% - 31px)' }">
                <template #header>
                  <div class="flex-row">
                    <span class="card-title tw-text-lg tw-font-semibold" style="font-weight: normal">{{ authGroup.name }}{{ selectNode.name ? ` — ${selectNode.name}` : "" }}</span>
                    <el-button size="small" type="primary" :disabled="!select || !userInfo.hasPermission(安全管理中心_权限管理_安全)" :icon="Plus" :loading="loadingUserGroup" @click="setPermissionGroup({ id: authGroup.id })">添加用户组</el-button>
                    <!-- <el-button size="small" type="primary" :disabled="!select || !userInfo.hasPermission(安全管理中心_权限管理_安全)" :icon="Plus" :loading="loadingUserGroup" @click="addUserGroup({ ...authGroup, userGroupData: userGroupList.filter((v) => v.permissionGroupId === authGroup.id) })">添加用户组</el-button> -->
                  </div>
                </template>
                <template #default>
                  <el-table v-loading="loadingUserGroup" :key="`auth-group-table_${authGroup.id}`" :data="userGroupList.filter((v) => v.permissionGroupId === authGroup.id)" :expand-row-keys="[...expandGroupsAuth]" stripe row-key="assignId" @expand-change="(row: UserGroupItem, rows: UserGroupItem[]) => ((rowEditItems = []), (rowEdit = { groupAssignId: '', itemId: '', assign: null, extend: null }), rows.includes(row) ? nextTick(() => getGroupsAuth(row)) : nextTick(() => clsGroupsAuth(row)))">
                    <el-table-column :resizable="false" type="expand" :width="105">
                      <template #default="{ row: groupRow }: { row: UserGroupItem }">
                        <div style="width: 100%; display: flex; justify-content: flex-end; padding: 0 16px; margin-bottom: 4px">
                          <el-button size="small" type="primary" :icon="Plus" :disabled="groupRow.extend || !userInfo.hasPermission(安全管理中心_权限管理_安全)" :loading="loadingUserGroup" @click="setPermission({ permissionGroupId: groupRow.permissionGroupId, groupAssignId: groupRow.assignId, userGroupId: groupRow.userGroupId })">权限配置</el-button>
                        </div>
                        <el-table v-loading="loadingGroupsAuth" :key="`auth-table_${groupRow.assignId}-${authGroup.id}`" :data="groupsAuthList.filter((v) => v.groupAssignId === groupRow.assignId)" :show-header="false" border row-key="assignId" style="margin-bottom: -4px">
                          <el-table-column :resizable="false" prop="assign" :width="105" :formatter="(row: GroupsAuthItem) => (row.assign ? `授权${row.extend ? '' : '(不继承)'}` : '禁止授权')"></el-table-column>
                          <el-table-column show-overflow-tooltip :resizable="false" prop="itemName" :width="160"></el-table-column>
                          <el-table-column :show-overflow-tooltip="false" :resizable="false" prop="permissions">
                            <template #default="{ row: authRow }: { row: GroupsAuthItem }">
                              <div v-if="rowEdit.groupAssignId === authRow.groupAssignId && rowEdit.itemId === authRow.itemId && (authRow.assign ? rowEdit.assign === authRow.assign && rowEdit.extend === authRow.extend : rowEdit.assign === authRow.assign)" style="display: flex; gap: 2; flex-wrap: wrap">
                                <el-select v-model="$rowEditItems" :loading="loadingAuth" :disabled="loadingAuth" size="small" multiple style="width: 100%">
                                  <el-option v-for="tag in authList.filter((v) => v.enabled && v.itemId === authRow.itemId)" :disabled="!tag.enabled" :key="tag.id" :value="tag.id" :label="tag.name"></el-option>
                                </el-select>
                              </div>
                              <div v-else style="display: flex; gap: 2; flex-wrap: wrap">
                                <el-tooltip v-for="tag in authRow.permissions" :key="tag.id" :disabled="!tag.children.length" effect="light">
                                  <template #content>
                                    <el-scrollbar style="margin: -3px -8px" :max-height="180" :view-style="{ display: 'flex', flexDirection: 'column', padding: '3px 8px' }">
                                      <el-tag v-for="tagChild in tag.children" :key="tagChild.id" type="info" size="small" :style="{ margin: '3px 0' }">{{ tagChild.name }}</el-tag>
                                    </el-scrollbar>
                                  </template>
                                  <template #default>
                                    <el-tag type="info" size="small" :style="{ margin: '3px' }">{{ tag.name }}</el-tag>
                                  </template>
                                </el-tooltip>
                              </div>
                            </template>
                          </el-table-column>
                          <el-table-column :resizable="false" :width="160">
                            <template #default="{ row: authRow }: { row: GroupsAuthItem }">
                              <template v-if="rowEdit.groupAssignId === authRow.groupAssignId && rowEdit.itemId === authRow.itemId && (authRow.assign ? rowEdit.assign === authRow.assign && rowEdit.extend === authRow.extend : rowEdit.assign === authRow.assign)">
                                <el-button size="small" @click="savingPermission(authRow)" :loading="loadingAuth" :disabled="groupRow.extend || !userInfo.hasPermission(安全管理中心_权限管理_安全)">{{ loadingAuth ? "  " : "保存" }}</el-button>
                                <el-button size="small" @click="cancelPermission(authRow)" :loading="loadingAuth" :disabled="groupRow.extend || !userInfo.hasPermission(安全管理中心_权限管理_安全)">{{ loadingAuth ? "  " : "取消" }}</el-button>
                              </template>
                              <template v-else>
                                <el-button size="small" @click="editorPermission(authRow)" :loading="loadingAuth" :disabled="groupRow.extend || !userInfo.hasPermission(安全管理中心_权限管理_安全)">{{ loadingAuth ? "  " : "编辑" }}</el-button>
                                <el-button size="small" @click="deletePermission(authRow)" :loading="loadingAuth" :disabled="groupRow.extend || !userInfo.hasPermission(安全管理中心_权限管理_安全)">{{ loadingAuth ? "  " : "删除" }}</el-button>
                              </template>
                            </template>
                          </el-table-column>
                        </el-table>
                      </template>
                    </el-table-column>
                    <!-- :formatter="(row: UserGroupItem, col) => `${row[col.property]} - ${getTreeItemById(row.containerId).name}`" -->
                    <el-table-column show-overflow-tooltip :resizable="false" prop="userGroupName" label="用户组名称"></el-table-column>
                    <el-table-column show-overflow-tooltip :resizable="false" prop="tenantName" label="客户" :formatter="(row, _col, v) => `${v}[${row.tenantAbbreviation}]`"></el-table-column>
                    <el-table-column show-overflow-tooltip :resizable="false" prop="extend" label="继承" :width="160" :formatter="(_row, _col, v) => (v ? h(ElIcon, { color: 'var(--el-color-success-light-5)' }, () => h(Select)) : h(ElIcon, { color: 'var(--el-color-danger-light-5)' }, () => h(CloseBold)))"></el-table-column>
                  </el-table>
                </template>
              </el-card>
            </el-col>
          </template>
        </el-row>
      </div>
    </el-scrollbar>
    <SelectSecurityContainer ref="selectContainerRef" title="选择目标安全容器" :width="$width" :height="$height - 180 - $height * 0.15"></SelectSecurityContainer>
    <PermissionModify ref="permissionRef" title="权限配置" :width="$width" :height="$height - 180 - $height * 0.15" :select="select"></PermissionModify>
    <!-- <EditorUser title="添加用户组" ref="EditorUserRef" @confirm="confirm"></EditorUser> -->
  </div>
</template>

<script setup lang="ts">
import { computed, ref, toValue, nextTick, watch, onMounted, onBeforeUnmount, h, inject, type Ref, shallowRef } from "vue";
import { useElementSize } from "@vueuse/core";
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElIcon, ElMessageBox, ElForm, ElFormItem, ElSelect, ElOption } from "element-plus";
import { Plus, Select, CloseBold } from "@element-plus/icons-vue";
import SvgIconMinus from "@/assets/minus.vue";
import SvgIconPlus from "@/assets/plus.vue";
import type Node from "element-plus/es/components/tree/src/model/node";
import { useSiteConfig } from "@/stores/siteConfig";
import getUserInfo from "@/utils/getUserInfo";
import { getAppAuth } from "@/api/application";
/* 安全容器 */
import { getSecurityContainer, addSecurityContainer, modSecurityContainerByRename, modSecurityContainerByMove, delSecurityContainer, hasSecurityContainer } from "./security_container";
/* 数据权限配置 */
import { BaseDataItem, ContactType, type FatchQueryController, type FatchMoveController, catSecurityContainerResourceType, generateContactTypeMeta } from "./security_container";
/* 权限分配 */
import { getAvailablePermissionGroupsList, getAssignedUserGroupsList, getAssignedPermissionList, setAssignedByCover, clsAssignedByPermission, getUserGroupUsable, hasPermissionByContainer, setAssignedByGroupCustom, setAssignedByGroupFull, setAssignedByGroupTemplate, setAssignedByCustom, setAssignedByFull, setAssignedByTemplate, delAssignedUserGroups } from "./security_container";

import generateQueueHook from "@/utils/queue_hook";

import SelectSecurityContainer from "./SelectSecurityContainer.vue";
import PermissionModify from "./PermissionModify.vue";

import { 安全管理中心_权限管理_删除, 安全管理中心_权限管理_安全, 安全管理中心_权限管理_新增, 安全管理中心_权限管理_编辑 } from "@/views/pages/permission";

const scrollbarRef = ref<InstanceType<typeof import("element-plus").ElScrollbar>>();
const scrollbar = ref({
  scrollLeft: 0,
  scrollTop: 0,
});
const containerRef = ref<HTMLDivElement>();
const { height } = useElementSize(containerRef);

/* ==================== */

const route = useRoute();
const router = useRouter();
const siteConfig = useSiteConfig();
const { t } = useI18n({ useScope: "global" });
const userInfo = getUserInfo();
const $width = inject<Ref<number>>("width", ref(0));
const $height = inject<Ref<number>>("height", ref(0));
/* =========================================================================================================== */

const selectContainerRef = ref<InstanceType<typeof SelectSecurityContainer>>();
type ResourceDataMoveQuery = FatchMoveController extends (form: infer P) => any ? P : never;
async function setSelectContainer(form: ResourceDataMoveQuery, collback: FatchMoveController) {
  const $dialog = toValue(selectContainerRef);
  if (!$dialog) return;
  await $dialog
    .opener({ ...form }, async ($form) => {
      const { success, message, data } = await collback($form);
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success("操作成功");
    })
    .catch((v) => Promise.resolve(v))
    .finally(() => refreshContactType(toValue(select)!));
}
/* =========================================================================================================== */
async function setPermissionGroup(raw: { id: string }) {
  const $select = toValue(select);
  if (!$select) return;
  const id = ref("");
  const $messageFormRef = shallowRef<InstanceType<typeof ElForm>>();
  const message = h({
    setup() {
      const loading = ref(false);
      const form = computed(() => ({ id: toValue(id) }));
      const options = ref<{ label: string; value: string }[]>([]);
      nextTick(async () => {
        try {
          loading.value = true;
          const { success, data, message } = await getUserGroupUsable({ containerId: $select, permissionGroupId: raw.id });
          if (!success) throw Object.assign(new Error(message), { success, data });
          options.value = (data instanceof Array ? data : []).map((v) => ({ label: v.name, value: v.id }));
        } catch (error) {
          if (error instanceof Error) ElMessage.error(error.message);
        } finally {
          loading.value = false;
        }
      });

      return () => {
        return h(ElForm, { ref: (vm) => ($messageFormRef.value = vm as InstanceType<typeof ElForm>), labelPosition: "top", model: toValue(form), onSubmit: (e: SubmitEvent) => e.preventDefault() }, () => [
          /*  */
          h(ElFormItem, { prop: "id", label: "用户组", rules: [{ required: true, message: "请选择用户组", trigger: "bulr" }] }, () => h(ElSelect, { "modelValue": toValue(id), "onUpdate:modelValue": ($event: string) => (id.value = $event), "placeholder": "选择用户组", "loading": toValue(loading), "style": { width: "100%" }, "filterable": true }, () => toValue(options).map((v) => h(ElOption, { label: v.label, value: v.value })))),
        ]);
      };
    },
  });
  await ElMessageBox.confirm(message, "添加用户组", {
    confirmButtonText: "下一步",
    cancelButtonText: "取消",
    draggable: true,
    async beforeClose(action, instance, done) {
      switch (action) {
        case "confirm": {
          const $formRef = toValue($messageFormRef);
          if (!$formRef) return;
          if (await new Promise((resolve) => $formRef.validate((valid) => resolve(valid)))) {
            const confirmButtonText = instance.confirmButtonText;
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "Loading...";

            const $dialog = toValue(permissionRef);
            if (!$dialog) return;
            /* { permissionGroupId: string; groupAssignId: string; type?: string | AssignType; itemId?: string } */
            await $dialog
              .opener({ userGroupId: toValue(id), permissionGroupId: raw.id, assign: true, extend: true, mfa: false, containerId: toValue(select), permissionIds: [] as string[], appAuth: [] as string[], isClear: false }, async ($form) => {
                let result: null | Promise<{ success: boolean; message: string; data: { assignId: string } }> = null;
                switch ($form.type) {
                  case AssignType.CUSTOM: {
                    result = setAssignedByGroupCustom({ containerId: $form.containerId, userGroupId: $form.userGroupId, permissionGroupId: $form.permissionGroupId, assign: $form.assign, extend: $form.extend, mfa: $form.mfa, permissionIds: $form.permissionIds });
                    break;
                  }
                  case AssignType.FULL: {
                    result = setAssignedByGroupFull({ containerId: $form.containerId, userGroupId: $form.userGroupId, permissionGroupId: $form.permissionGroupId, extend: $form.extend });
                    break;
                  }
                  default: {
                    result = setAssignedByGroupTemplate({ containerId: $form.containerId, userGroupId: $form.userGroupId, permissionGroupId: $form.permissionGroupId, templateId: $form.type });
                    break;
                  }
                }
                if (result) {
                  const { success: success, message: message, data: data } = await result;
                  if (!success) throw Object.assign(new Error(message), { success, data });
                  ElMessage.success(`${t("axios.Operation successful")}`);
                  done();
                  /* 展开 */
                  for (const assignId of toValue(expandGroupsAuth).values()) {
                    if (assignId !== data.assignId) await clsGroupsAuth({ assignId });
                  }
                  expandGroupsAuth.value.add(data.assignId);
                } else throw new Error("配置无效");
              })
              .catch((v) => Promise.resolve(v))
              .finally(() => getAuthGroups());

            instance.confirmButtonLoading = false;
            instance.confirmButtonText = confirmButtonText;
          }
          break;
        }
        default: {
          done();
          break;
        }
      }
    },
  }).catch(() => Promise.resolve());
}
/* =========================================================================================================== */
const permissionRef = ref<InstanceType<typeof PermissionModify>>();
enum AssignType {
  FULL = "FULL",
  CUSTOM = "CUSTOM",
  CLEAR = "CLEAR",
}
async function setPermission(raw: { permissionGroupId: string; groupAssignId: string; userGroupId: string; type?: string | AssignType; itemId?: string }) {
  const $dialog = toValue(permissionRef);
  if (!$dialog) return;
  await $dialog
    .opener({ ...raw, assign: true, extend: true, mfa: false, containerId: toValue(select), permissionIds: [] as string[], appAuth: [] as string[], isClear: true }, async ($form) => {
      switch ($form.type) {
        case AssignType.CUSTOM: {
          const { success: success, message: message, data: data } = await setAssignedByCustom({ groupAssignId: raw.groupAssignId, assign: $form.assign, extend: $form.extend, mfa: $form.mfa, permissionIds: $form.permissionIds.filter((v) => !$form.appAuth.includes(v)) });
          if (!success) throw Object.assign(new Error(message), { success, data });
          break;
        }
        case AssignType.FULL: {
          const { success, message, data } = await setAssignedByFull({ groupAssignId: raw.groupAssignId, extend: $form.extend });
          if (!success) throw Object.assign(new Error(message), { success, data });
          break;
        }
        case AssignType.CLEAR: {
          const { success, message, data } = await delAssignedUserGroups({ assignId: raw.groupAssignId });
          if (!success) throw Object.assign(new Error(message), { success, data });
          break;
        }
        default: {
          const { success, message, data } = await setAssignedByTemplate({ groupAssignId: raw.groupAssignId, templateId: $form.type });
          if (!success) throw Object.assign(new Error(message), { success, data });
          break;
        }
      }
      ElMessage.success(`${t("axios.Operation successful")}`);
    })
    .catch((v) => Promise.resolve(v))
    .finally(() => getAuthGroups());
}
/* =========================================================================================================== */

interface OptionsItem {
  readonly label: string;
  readonly value: ContactType;
  readonly fatch: FatchQueryController;
  readonly move: FatchMoveController;
  readonly column: { label: string; value: string; formatter: (value: string, row: Record<string, unknown>, index: number) => string | import("vue").VNode }[];
  list: BaseDataItem[];
  loading: boolean;
  page: number;
  size: number;
  total: number;
}
const containerTypeOptions = ref<OptionsItem[]>([]);
const loadingContainerType = ref(false);
const active = computed<ContactType | "">({
  get: () => (route.query.active as ContactType) || toValue(containerTypeOptions).reduce<ContactType>((p, c) => (p ? p : c.value), ContactType.UNKNOWN),
  set: (v) => router.replace({ query: { ...route.query, active: v || undefined } }),
});
async function refreshContactType($select: string) {
  try {
    loadingContainerType.value = true;
    await nextTick();
    const [{ success, message, data }, { success: hasSuccess, message: hasMessage, data: hasData }] = await Promise.all([catSecurityContainerResourceType({ id: $select }), hasPermissionByContainer({ appId: (siteConfig.baseInfo || {}).app || "", containerId: $select })]);
    if (!success) throw Object.assign(new Error(message), { success, data });
    if (!hasSuccess) throw Object.assign(new Error(hasMessage), { success: hasData, data: hasSuccess });
    containerTypeOptions.value = (data instanceof Array ? data : [])
      .filter((v) => hasData.includes(v))
      .map((type): OptionsItem => {
        const meta = generateContactTypeMeta(type);
        return Object.defineProperties<OptionsItem>({ fatch: meta.fatch, move: meta.move, column: meta.column, label: meta.value === ContactType.UNKNOWN ? `${meta.label}(${type})` : meta.label, value: meta.value, list: [], page: 1, size: 50, total: 0, loading: false }, { fatch: { value: meta.fatch, writable: false }, move: { value: meta.move, writable: false }, column: { value: meta.column, writable: false }, label: { value: meta.label, writable: false }, value: { value: meta.value, writable: false } });
      });
    if (toValue(containerTypeOptions).filter((v) => v.value === toValue(active)).length === 0) active.value = toValue(containerTypeOptions).reduce<ContactType>((p, c) => (p ? p : c.value), ContactType.UNKNOWN);
    await router.isReady();
    await nextTick();
    const $active = toValue(active);
    if (!$active) return;
    await refreshContactData($active);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loadingContainerType.value = false;
  }
}
function refreshContactData($active: string | number) {
  const options = toValue(containerTypeOptions);
  const $select = toValue(select);
  if (!$select) return;
  return new Promise<boolean>((resolve) => {
    let $find = false;
    for (let i = 0; i < options.length; i++) {
      if (options[i].value === $active) {
        if (!$find) resolve(true);
        $find = true;
        nextTick(async () => {
          try {
            options[i].loading = true;
            await nextTick();
            const { success, message, data, page, size, total } = await options[i].fatch({ containerId: $select, paging: { pageNumber: options[i].page, pageSize: options[i].size } });
            if (!success) throw Object.assign(new Error(message), { success, data });
            options[i].list = data instanceof Array ? data : [];
            options[i].page = Number(page) || 1;
            options[i].size = Number(size) || 50;
            options[i].total = Number(total) || 0;
            options[i].loading = false;
          } catch (error) {
            options[i].list = [];
            options[i].page = 1;
            options[i].size = 50;
            options[i].total = 0;
            options[i].loading = false;
            if (error instanceof Error) ElMessage.error(error.message || `[Web Error] Not Find: API By "${$active}"`);
          }
        });
      } else {
        options[i].list = [];
        options[i].page = 1;
        options[i].size = 50;
        options[i].total = 0;
      }
    }
    if (!$find) resolve(false);
  });
}
/* =========================================================================================================== */
const authGroupList = ref<ReturnType<typeof getAvailablePermissionGroupsList> extends Promise<{ data: infer P }> ? P : never>([]);
const loadingAuthGroup = ref(false);
type UserGroupItem = ReturnType<typeof getAssignedUserGroupsList> extends Promise<{ data: (infer P)[] }> ? P : never;
const userGroupList = ref<UserGroupItem[]>([]);
const loadingUserGroup = ref(false);
type GroupsAuthItem = ReturnType<typeof getAssignedPermissionList> extends Promise<{ data: (infer P)[] }> ? P : never;
const groupsAuthList = ref<GroupsAuthItem[]>([]);
const loadingGroupsAuth = ref(false);
const AppendQueue = generateQueueHook(loadingGroupsAuth, (loading) => !loading);
const expandGroupsAuth = ref(new Set<string>());
const rowEdit = ref({ groupAssignId: "", itemId: "", assign: null as null | boolean, extend: null as null | boolean });
const rowEditItems = ref<string[]>([]);
const $rowEditItems = computed({
  get: () => toValue(rowEditItems),
  set: ($auths: string[]) => {
    const $list = toValue(authList).filter((v) => v.itemId === toValue(rowEdit).itemId && v.enabled);
    const $ids = $list.map((v) => v.id);
    const $map = $list.reduce((p, c) => {
      const $key = c.id;
      const $bind: string[] = c.allInItem ? $ids.filter((v) => v !== $key) : (c.childIds instanceof Array ? c.childIds : []).filter((v) => $ids.includes(v));
      const $hand: string[] = $list.reduce<string[]>((hand, item) => (item.id === $key ? hand : item.allInItem || (item.childIds instanceof Array ? item.childIds : []).includes($key) ? hand.concat(item.id) : hand), []);
      p.set($key, { bind: $bind, hand: $hand });
      return p;
    }, new Map<string, { /* 关联的权限 */ bind: string[]; /* 依赖的权限 */ hand: string[] }>());
    const $result = new Set<string>(toValue(rowEditItems).filter((v) => $map.has(v) && ($auths instanceof Array ? $auths : []).includes(v)));
    const $addValue: string[] = ($auths instanceof Array ? $auths : []).filter((v) => $map.has(v) && !toValue(rowEditItems).includes(v));
    const $delValue: string[] = toValue(rowEditItems).filter((v) => $map.has(v) && !($auths instanceof Array ? $auths : []).includes(v));
    const $addAffect = /* 处理添加数据 */ (id: string, $affect: { bind: string[]; hand: string[] }) => {
      $result.add(id);
      for (let i = 0; i < $affect.bind.length; i++) $addAffect($affect.bind[i], $map.get($affect.bind[i])!);
      for (let i = 0; i < $affect.hand.length; i++) {
        if ($map.get($affect.hand[i])!.bind.every((v) => $result.has(v))) $result.add($affect.hand[i]);
      }
    };
    const $delAffect = /* 处理移除数据 */ (id: string, $affect: { bind: string[]; hand: string[] }) => {
      $result.delete(id);
      for (let i = 0; i < $affect.bind.length; i++) $delAffect($affect.bind[i], $map.get($affect.bind[i])!);
      for (let i = 0; i < $affect.hand.length; i++) {
        if ($result.has($affect.hand[i])) $result.delete($affect.hand[i]);
      }
    };
    for (let i = 0; i < $addValue.length; i++) $addAffect($addValue[i], $map.get($addValue[i])!);
    for (let i = 0; i < $delValue.length; i++) $delAffect($delValue[i], $map.get($delValue[i])!);
    rowEditItems.value = Array.from($result.values());
  },
});
type AuthItem = ReturnType<typeof getAppAuth> extends Promise<{ data: (infer P)[] }> ? P : never;
const authList = ref<AuthItem[]>([]);
const loadingAuth = ref(false);

//获取当前应用下的权限配置组
async function getAuthGroups() {
  const { scrollLeft, scrollTop } = toValue(scrollbar);
  const $scrollbarRef = toValue(scrollbarRef);
  authGroupList.value = [];
  userGroupList.value = [];
  const $select = toValue(select);
  if (!$select) return;
  try {
    loadingAuthGroup.value = true;
    await nextTick();
    const [{ success, message, data }, { success: authSuccess, message: authMessage, data: authData }] = await Promise.all([getAvailablePermissionGroupsList({ appId: (siteConfig.baseInfo || {}).app || "", containerId: toValue(select) || "" }), getAppAuth({ appId: (siteConfig.baseInfo || {}).app })]);
    if (!success) throw Object.assign(new Error(message), { success, data });
    if (!authSuccess) throw Object.assign(new Error(authMessage), { success: authSuccess, data: authData });
    authGroupList.value = (data instanceof Array ? data : []).map((v) => ({ ...v, orderNum: Number(v.orderNum) || 0 })).sort((a, b) => a.orderNum - b.orderNum);
    authList.value = (authData instanceof Array ? authData : []).map((v, i, f) => ({ ...v, childIds: (v.childIds instanceof Array ? v.childIds : []).filter((id) => f.some((q) => q.enabled && q.id === id)) }));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loadingAuthGroup.value = false;
  }

  try {
    loadingUserGroup.value = true;
    await nextTick();
    const $authGroupList = toValue(authGroupList);
    if ($authGroupList.length) {
      const { success, message, data } = await getAssignedUserGroupsList({ containerId: $select, permissionGroupIds: toValue(authGroupList).map((v) => v.id) });
      if (!success) throw Object.assign(new Error(message), { success, data });
      userGroupList.value = data instanceof Array ? data : [];
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loadingUserGroup.value = false;
  }

  const $expandGroupsAuth = toValue(expandGroupsAuth);
  await Promise.all(
    toValue(userGroupList)
      .filter((v) => $expandGroupsAuth.has(v.assignId))
      .map((userGroup) => getGroupsAuth(userGroup))
  );

  if ($scrollbarRef) {
    await nextTick();
    try {
      $scrollbarRef.scrollTo({ top: scrollTop, left: scrollLeft, behavior: "smooth" });
    } catch (error) {
      /* */
    }
  }
}
/* 权限配置项 */
let getGroupsAuthController: AbortController | undefined;
/* 用户组 In 权限配置项 ===> OPEN */
async function getGroupsAuth($userGroup: UserGroupItem) {
  if (getGroupsAuthController) getGroupsAuthController.abort();
  const groupAssignId = $userGroup.assignId;
  const permissionGroupId = $userGroup.permissionGroupId;
  const userGroupId = $userGroup.userGroupId;
  if (loadingGroupsAuth.value) await new AppendQueue(true);
  for (let i = groupsAuthList.value.length - 1; i >= 0; i--) {
    if (groupsAuthList.value[i].groupAssignId === groupAssignId) groupsAuthList.value.splice(i, 1);
  }
  expandGroupsAuth.value.add(groupAssignId);
  try {
    loadingGroupsAuth.value = true;
    await nextTick();
    const result = getAssignedPermissionList({ extendView: $userGroup.extend, groupAssignId, permissionGroupId, userGroupId, container: select.value as string });
    getGroupsAuthController = result.controller;
    const { success, message, data } = await result;
    if (!success) throw Object.assign(new Error(message), { success, data });
    type ChildrenAuth = { id: string; name: string; sort: number };
    const $auth = toValue(authList).reduce((p, c) => (c.enabled ? p.set(c.id, c) : p), new Map<string, AuthItem>());
    const $reduceAuthById = (p: ChildrenAuth[], c: string): typeof p => {
      const v = $auth.get(c);
      return v ? p.concat({ id: v.id, name: v.name, sort: Number(v.orderNum) || 0 }) : p;
    };
    groupsAuthList.value.push(
      ...(data instanceof Array ? data : []).map((v) => {
        const $permissions = (v.permissions instanceof Array ? v.permissions : []).map((permission) => {
          /* eslint-disable indent */
          const $children: ChildrenAuth[] = [];
          const findAuth = $auth.get(permission.id);
          const $reduceAuthByItem = (p: ChildrenAuth[], c: AuthItem): typeof p => (c.id !== permission.id && c.itemId === v.itemId ? p.concat({ id: c.id, name: c.name, sort: Number(c.orderNum) || 0 }) : p);
          if (findAuth) {
            if (findAuth.allInItem) {
              const $value = Array.from($auth.values());
              $children.push(...$value.reduce<ChildrenAuth[]>($reduceAuthByItem, []));
            } else $children.push(...(findAuth.childIds instanceof Array ? findAuth.childIds : []).reduce<ChildrenAuth[]>($reduceAuthById, []));
          }
          return { ...permission, sort: findAuth ? Number(findAuth.orderNum) : NaN, children: $children.sort((a, b) => a.sort - b.sort) };
        });
        return { ...v, groupAssignId, permissions: $permissions.sort((a, b) => a.sort - b.sort) };
      })
    );
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    getGroupsAuthController = void 0;
    loadingGroupsAuth.value = false;
  }
}
/* 用户组 In 权限配置项 ===> CLOSE */
async function clsGroupsAuth($userGroup: Pick<UserGroupItem, "assignId">) {
  const groupAssignId = $userGroup.assignId;
  for (let i = groupsAuthList.value.length - 1; i >= 0; i--) {
    if (groupsAuthList.value[i].groupAssignId === groupAssignId) groupsAuthList.value.splice(i, 1);
  }
  expandGroupsAuth.value.delete(groupAssignId);
}
/* 权限操作 */
async function editorPermission(row: GroupsAuthItem) {
  const $userGroupList = toValue(userGroupList);
  for (let i = 0; i < $userGroupList.length; i++) {
    if ($userGroupList[i].assignId === row.groupAssignId) {
      try {
        loadingAuth.value = true;
        await nextTick();
        rowEditItems.value = row.permissions.map((v) => v.id);
        rowEdit.value = { groupAssignId: row.groupAssignId, itemId: row.itemId, assign: row.assign, extend: row.extend };
      } catch (error) {
        rowEditItems.value = [];
        rowEdit.value = { groupAssignId: "", itemId: "", assign: null, extend: null };
        if (error instanceof Error) ElMessage.error(error.message);
      } finally {
        loadingAuth.value = false;
      }
      break;
    }
  }
}
async function savingPermission(row: GroupsAuthItem) {
  const $userGroupList = toValue(userGroupList);
  for (let i = 0; i < $userGroupList.length; i++) {
    if ($userGroupList[i].assignId === row.groupAssignId) {
      const full = toValue(authList).filter((v) => v.itemId === row.itemId);
      const $permissions = toValue(groupsAuthList).reduce<import("./security_container").AssignedItem[]>((p, c) => {
        if (c.groupAssignId === row.groupAssignId && c.itemId === row.itemId) {
          for (let i = 0; i < c.permissions.length; i++) p.push({ id: c.permissions[i].id, name: c.permissions[i].name, assign: c.permissions[i].assign, extend: c.permissions[i].extend, mfa: c.permissions[i].mfa, sort: c.permissions[i].sort, children: c.permissions[i].children });
        }
        return p;
      }, []);
      row.permissions.splice(
        0,
        row.permissions.length,
        ...toValue(rowEditItems).reduce<import("./security_container").AssignedItem[]>(
          (permission, id) => {
            for (let i = permission.length - 1; i >= 0; i--) {
              if (permission[i].id === id) permission.splice(i, 1);
            }
            return permission.concat({ id, name: full.reduce((p, c) => (c.id === id ? c.name : p), ""), assign: row.assign, mfa: false, extend: row.extend, sort: Infinity, children: [] });
          },
          $permissions.filter((v) => !(v.assign ? row.assign === v.assign && row.extend === v.extend : row.assign === v.assign))
        )
      );
      try {
        loadingAuth.value = true;
        await nextTick();
        rowEditItems.value = [];
        rowEdit.value = { groupAssignId: "", itemId: "", assign: null, extend: null };
        const { success, message, data } = await setAssignedByCover({ permission: row.permissions.map((v) => ({ permissionId: v.id, name: v.name, extend: v.extend, assign: v.assign, mfa: v.mfa })), permissionItemId: row.itemId, groupAssignId: $userGroupList[i].assignId });
        if (!success) throw Object.assign(new Error(message), { success, data });
        ElMessage.success(`${t("axios.Operation successful")}`);
        const { scrollLeft, scrollTop } = toValue(scrollbar);
        const $scrollbarRef = toValue(scrollbarRef);
        await getGroupsAuth($userGroupList[i]);
        await nextTick();
        try {
          if ($scrollbarRef) $scrollbarRef.scrollTo({ top: scrollTop, left: scrollLeft, behavior: "smooth" });
        } catch (error) {
          /* */
        }
      } catch (error) {
        rowEditItems.value = row.permissions.filter((v) => (v.assign ? row.assign === v.assign && row.extend === v.extend : row.assign === v.assign)).map((v) => v.id);
        row.permissions.splice(0, row.permissions.length, ...$permissions.filter((v) => (v.assign ? row.assign === v.assign && row.extend === v.extend : row.assign === v.assign)));
        rowEdit.value = { groupAssignId: row.groupAssignId, itemId: row.itemId, assign: row.assign, extend: row.extend };
        if (error instanceof Error) ElMessage.error(error.message);
      } finally {
        loadingAuth.value = false;
      }
      break;
    }
  }
}
async function deletePermission(row: GroupsAuthItem) {
  const $userGroupList = toValue(userGroupList);
  for (let i = 0; i < $userGroupList.length; i++) {
    if ($userGroupList[i].assignId === row.groupAssignId) {
      try {
        loadingAuth.value = true;
        await nextTick();
        await ElMessageBox.confirm(`确定删除${row.itemName}所有权限吗?`, "删除权限", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          draggable: true,
          async beforeClose(action, instance, done) {
            switch (action) {
              case "confirm": {
                const confirmButtonText = instance.confirmButtonText;
                const $permissions = row.permissions.map((v) => ({ id: v.id, name: v.name, assign: v.assign, extend: v.extend, mfa: v.mfa, sort: v.sort, children: v.children }));
                row.permissions.splice(0, row.permissions.length);
                try {
                  instance.confirmButtonLoading = true;
                  instance.confirmButtonText = "Loading...";
                  const { success, data, message } = await clsAssignedByPermission({ permissionItemId: row.itemId, groupAssignId: $userGroupList[i].assignId, deleteAssigned: row.assign });
                  if (!success) throw Object.assign(new Error(message), { success, data });
                  ElMessage.success(`${t("axios.Operation successful")}`);
                  done();
                } catch (error) {
                  row.permissions.splice(0, row.permissions.length, ...$permissions);
                  if (error instanceof Error) ElMessage.error(error.message);
                } finally {
                  instance.confirmButtonLoading = false;
                  instance.confirmButtonText = confirmButtonText;
                  const { scrollLeft, scrollTop } = toValue(scrollbar);
                  const $scrollbarRef = toValue(scrollbarRef);
                  await getAuthGroups();
                  await getGroupsAuth($userGroupList[i]);
                  await nextTick();
                  try {
                    if ($scrollbarRef) $scrollbarRef.scrollTo({ top: scrollTop, left: scrollLeft, behavior: "smooth" });
                  } catch (error) {
                    /* */
                  }
                }
                break;
              }
              default: {
                done();
                break;
              }
            }
          },
        }).catch(() => Promise.resolve());
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      } finally {
        loadingAuth.value = false;
      }
      break;
    }
  }
}
async function cancelPermission(row: GroupsAuthItem) {
  const $userGroupList = toValue(userGroupList);
  for (let i = 0; i < $userGroupList.length; i++) {
    if ($userGroupList[i].assignId === row.groupAssignId) {
      try {
        loadingAuth.value = true;
        await nextTick();
        rowEditItems.value = [];
        rowEdit.value = { groupAssignId: "", itemId: "", assign: null, extend: null };
      } catch (error) {
        if (error instanceof Error) ElMessage.error(error.message);
      } finally {
        loadingAuth.value = false;
      }
      break;
    }
  }
}
/* =========================================================================================================== */
type TreeItem = (ReturnType<typeof getSecurityContainer> extends Promise<{ data: infer P }> ? P : never)[number];
const containerTree = ref<TreeItem[]>([]);
const loading = ref(false);
const select = computed<string | undefined>({
  get: () => (route.query.select as string) || toValue(containerTree).reduce<undefined | string>((p, c) => (p ? p : c.id), void 0),
  set: (v) => router.replace({ query: { ...route.query, select: v || undefined } }),
});
function getTreeItemById(id: string) {
  const getTreeById = (tree: typeof containerTree.value, id: string): TreeItem | void => {
    for (let i = 0; i < tree.length; i++) {
      if (tree[i].id === id) return tree[i];
      if (tree[i].children instanceof Array && tree[i].children.length) {
        const find = getTreeById(tree[i].children, id);
        if (find) return find;
      }
    }
  };
  const find = getTreeById(toValue(containerTree), id);
  if (find) return find;
  else return { id: "", name: "", children: [] };
}
const selectNode = computed<TreeItem>(() => {
  const $select = toValue(select);
  if (!$select) return { id: $select || "", name: "", children: [] };
  return getTreeItemById($select);
});
const expandedNodes = ref(new Set<string>());
async function expandNodes(expand: boolean, node: Node) {
  if (expand) {
    expandedNodes.value.add(node.key as string);
    nextTick(() => node.expand());
  } else {
    const unfoldChildren = async (children: Node[]) => {
      for (let i = 0; i < children.length; i++) {
        const item = children[i];
        const key = item.key as string;
        await unfoldChildren(item.childNodes);
        await nextTick();
        expandedNodes.value.delete(key);
        await nextTick();
        item.collapse();
      }
    };
    await unfoldChildren(node.childNodes);
    expandedNodes.value.delete(node.key as string);
    nextTick(() => node.collapse());
  }
}
onMounted(() => loadNode());
let unWatch = () => {};
onBeforeUnmount(() => unWatch());
const deleteDiasbled = ref(true);
async function loadNode() {
  deleteDiasbled.value = true;
  try {
    unWatch();
    unWatch = () => {};
    containerTree.value = [];
    loading.value = true;
    await nextTick();
    const { success, message, data } = await getSecurityContainer({ permissionId: "623053172327317504" });
    if (!success) throw Object.assign(new Error(message), { success, data });
    containerTree.value = data instanceof Array ? data : [];
    await nextTick();
    await router.isReady();
    await nextTick();
    containerTypeOptions.value = [];
    const $select = toValue(select);
    const expanded = (list: TreeItem[], key: string, path: string[] = []): string[] => {
      for (let i = 0; i < list.length; i++) {
        if (list[i].id === key) return path;
        const find = expanded(list[i].children instanceof Array ? list[i].children : [], key, path.concat(list[i].id));
        if (find.length) return find;
      }
      return [];
    };
    const $expanded = expanded(containerTree.value, $select || "");
    nextTick(() => {
      for (let i = 0; i < $expanded.length; i++) expandedNodes.value.add($expanded[i]);
    });
    if (!$select) return;
    refreshContactType($select);
    await getAuthGroups();
    await hasSecurityContainer({ id: $select })
      .then(({ success, message, data }) => {
        if (!success) return Promise.reject(Object.assign(new Error(message), { success, data }));
        deleteDiasbled.value = !data;
        return Promise.resolve();
      })
      .catch(() => {
        return Promise.resolve();
      });
    unWatch = watch(select, ($select) => {
      containerTypeOptions.value = [];
      deleteDiasbled.value = true;
      rowEditItems.value = [];
      rowEdit.value = { groupAssignId: "", itemId: "", assign: null, extend: null };
      if (!$select) return;
      refreshContactType($select);
      getAuthGroups();
      loading.value = true;
      hasSecurityContainer({ id: $select })
        .then(({ success, message, data }) => {
          if (!success) return Promise.reject(Object.assign(new Error(message), { success, data }));
          deleteDiasbled.value = !data;
          return Promise.resolve();
        })
        .catch(() => {
          return Promise.resolve();
        })
        .finally(() => {
          loading.value = false;
        });
    });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    loading.value = false;
  }
}

async function createSafeContaine(raw: { name: string; containerId: string; parentId?: string }) {
  ElMessageBox.prompt(`安全容器名称`, "新增安全容器", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    // inputValue: "",
    inputPattern: /[^\s]/,
    inputPlaceholder: "请输入安全容器名称",
    inputErrorMessage: "输入的容器名称不合法!",
    draggable: true,
    async beforeClose(action, instance, done) {
      switch (action) {
        case "confirm": {
          const confirmButtonText = instance.confirmButtonText;
          try {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "Loading...";
            const { success, message, data } = await addSecurityContainer({ name: instance.inputValue, parentId: raw.containerId });
            if (!success) throw Object.assign(new Error(message), { success, data });
            ElMessage.success(`${t("axios.Operation successful")}`);
            done();
          } catch (error) {
            if (error instanceof Error) ElMessage.error(error.message);
          } finally {
            instance.confirmButtonLoading = false;
            instance.confirmButtonText = confirmButtonText;
            await loadNode();
          }
          break;
        }
        default: {
          done();
          break;
        }
      }
    },
  }).catch(() => Promise.resolve());
}
async function renameSafeContaine(raw: { name: string; containerId: string; parentId?: string }) {
  ElMessageBox.prompt(`安全容器名称`, "重命名安全容器", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputValue: raw.name,
    inputPattern: /[^\s]/,
    inputPlaceholder: "请输入安全容器名称",
    inputErrorMessage: "输入的容器名称不合法!",
    draggable: true,
    async beforeClose(action, instance, done) {
      switch (action) {
        case "confirm": {
          const confirmButtonText = instance.confirmButtonText;
          try {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "Loading...";
            const { success, message, data } = await modSecurityContainerByRename({ name: instance.inputValue, id: raw.containerId });
            if (!success) throw Object.assign(new Error(message), { success, data });
            ElMessage.success(`${t("axios.Operation successful")}`);
            done();
          } catch (error) {
            if (error instanceof Error) ElMessage.error(error.message);
          } finally {
            instance.confirmButtonLoading = false;
            instance.confirmButtonText = confirmButtonText;
            await loadNode();
          }
          break;
        }
        default: {
          done();
          break;
        }
      }
    },
  }).catch(() => Promise.resolve());
}
async function movingSafeContaine(raw: { name: string; containerId: string; parentId?: string }) {
  const $dialog = toValue(selectContainerRef) as unknown as Record<"opener", (params: ResourceDataMoveQuery & { disabled: string }, collback: (form: ResourceDataMoveQuery) => Promise<void>) => Promise<void>>;
  if (!$dialog) return;
  $dialog
    .opener({ id: raw.containerId, containerId: raw.parentId || "", oldContainerName: "", newContainerName: "", disabled: raw.containerId }, async ($form) => {
      const { success, message, data } = await modSecurityContainerByMove({ id: raw.containerId, parentId: $form.containerId });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`${t("axios.Operation successful")}`);
    })
    .catch((v) => Promise.resolve(v))
    .finally(() => loadNode());
}
async function deleteSafeContaine(raw: { name: string; containerId: string; parentId?: string }) {
  ElMessageBox.confirm(`确定删除${raw.name}?`, "删除安全容器", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    draggable: true,
    async beforeClose(action, instance, done) {
      switch (action) {
        case "confirm": {
          const confirmButtonText = instance.confirmButtonText;
          try {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "Loading...";
            const { success, data, message } = await delSecurityContainer({ id: raw.containerId });
            if (!success) throw Object.assign(new Error(message), { success, data });
            ElMessage.success(`${t("axios.Operation successful")}`);
            select.value = undefined;
            await router.isReady();
            await nextTick();
            done();
          } catch (error) {
            if (error instanceof Error) ElMessage.error(error.message);
          } finally {
            instance.confirmButtonLoading = false;
            instance.confirmButtonText = confirmButtonText;
            await loadNode();
          }
          break;
        }
        default: {
          done();
          break;
        }
      }
    },
  }).catch(() => Promise.resolve());
}
</script>

<style scoped lang="scss">
.permission {
  width: 100%;
  margin: -6px;
  font-size: 14px;
  line-height: 22px;
  overflow: hidden;

  > * {
    padding: 6px;
  }

  .size-full {
    width: 100%;
    height: 100%;
  }
  .tree-autosize {
    :deep(.el-tree-node__content) {
      height: fit-content;
      padding: 3px 0;
      margin: 6px 0;
    }
  }
  .tree-custom {
    > :deep(.el-tree-node) {
      width: fit-content;
      > .el-tree-node__children::after {
        display: none !important;
      }
    }

    :deep(.el-tree-node) {
      margin-left: 12px;
      overflow: hidden;
      .skeleton-icon {
        overflow: hidden;
        cursor: pointer;
        position: absolute;
        z-index: 2;
        top: 0px;
        left: -24px;
        width: 24px;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        > * {
          background-color: var(--el-fill-color-blank);
        }
      }
      .el-tree-node__expand-icon {
        display: none;
      }
      > .el-tree-node__content {
        .skeleton-line {
          margin-left: 24px;
          position: relative;
          &::before {
            content: "";
            position: absolute;
            z-index: 1;
            top: -6px;
            left: -30px;
            display: block;
            width: 30px;
            pointer-events: none;
            height: calc(50% + 6px);
            border-bottom: 1px solid var(--el-text-color-placeholder);
          }
          &.is-root {
            &::before {
              left: -12px;
              width: 12px;
            }
          }
        }
      }
      &.is-expanded_node {
        > .el-tree-node__content {
          position: relative;
          &:not(:last-child) {
            &::before {
              content: "";
              position: absolute;
              z-index: 1;
              left: 12px;
              bottom: -10px;
              display: block;
              width: 18px;
              pointer-events: none;
              height: calc(50% + 6px);
              border-left: 1px solid var(--el-text-color-placeholder);
              // border-bottom: 1px solid var(--el-text-color-placeholder);
            }
          }
        }
        > .el-tree-node__children {
          position: relative;
          overflow: visible !important;

          &::before {
            content: "";
            position: absolute;
            z-index: 1;
            top: 0px;
            left: 12px;
            display: block;
            width: 18px;
            pointer-events: none;
            height: calc(100% - 25px);
            border-left: 1px solid var(--el-text-color-placeholder);
          }
          &::after {
            content: "";
            position: absolute;
            z-index: 1;
            top: -25px;
            left: 0px;
            display: block;
            width: 18px;
            pointer-events: none;
            height: 100%;
            border-left: 1px solid var(--el-fill-color-blank);
          }
        }
      }

      &:not(:last-child).is-expanded_node {
        > .el-tree-node__children::after {
          border-left: 1px solid var(--el-text-color-placeholder);
        }
      }
    }
  }
  .flex-row {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .card-title {
    font-weight: bold;
    margin-right: auto;
  }
  .card-extend {
    color: var(--el-text-color-placeholder);
  }
  .toolbar {
    height: fit-content;
    > :deep(.el-card) {
      box-shadow: 0 0 0 1px inset var(--el-border-color);

      .el-card__header {
        --el-card-padding: 12px;
      }
    }
  }
  .context {
    > :deep(.el-card) {
      box-shadow: 0 0 0 1px inset var(--el-border-color);

      .el-card__header {
        --el-card-padding: 12px;
        padding-top: 8px;
      }
    }
  }
  :deep(.el-tabs__header) {
    margin-top: 6px;
    margin-bottom: -1px;
  }
}
.permission-extend {
  position: relative;
  &::after {
    content: "";
    display: block;
    border-radius: 100%;
    position: absolute;
    bottom: 1px;
    right: 1px;
    background-color: var(--el-color-info-light-5);
    width: 1px;
    height: 1px;
  }
}
</style>
