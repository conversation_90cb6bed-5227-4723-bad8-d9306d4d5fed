<template>
  <el-dialog v-model="dialogVisible" title="选择目标安全容器" width="500" :before-close="handleClose">
    <div>
      <treeAuth ref="treeAuthRef" :operationType="'移动'" @clickItem="containerChange"></treeAuth>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">取消</el-button>

        <el-button @click="move">移动</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h } from "vue";
import { ElMessage, ElMessageBox, ElText } from "element-plus";

import treeAuth from "@/components/treeAuth/moveTree.vue";

import { moveContainer } from "@/api/authConfig";

const treeAuthRef = ref<InstanceType<typeof treeAuth>>();
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const dialogVisible = ref(false);
const container = ref({});
const containerId = ref("");

interface Emits {
  (event: "moveContainerItem", item: any): void;
}
const emits = defineEmits<Emits>();

//打开用户组弹框
async function open(val) {
  containerId.value = val;
  dialogVisible.value = true;
  // await console.log(treeAuthRef.value);
  setTimeout(() => {
    // console.log(treeAuthRef.value);
    treeAuthRef.value.getSafeContaine(val);
  }, 1000);
  //
}

async function containerChange(val) {
  // console.log(val);
  container.value = val;
}
async function move() {
  await moveContainer({
    id: containerId.value,
    parentId: container.value.id,
  }).then((res) => {
    // console.log(res);
    if (res.success) {
      ElMessage.success("操作成功");
      dialogVisible.value = false;

      emits("moveContainerItem");
    }
  });
}
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}

beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ console.log.bind(null, '🚀[onErrorCaptured]'), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ console.log.bind(null, '🚀[onRenderTracked]'), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ console.log.bind(null, '🚀[onRenderTriggered]'), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);

defineExpose({ open });
</script>
<style lang="scss" scoped></style>
