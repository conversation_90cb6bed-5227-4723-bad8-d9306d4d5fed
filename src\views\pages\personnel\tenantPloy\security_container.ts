import { SERVER, Method, type Response, bindParamByObj } from "@/api/service/common";
import request from "@/api/service/index";
import getUserInfo from "@/utils/getUserInfo";
import { 安全管理中心_用户管理_安全, 安全管理中心_用户组管理_安全, 服务管理中心_SLA配置_安全, 服务管理中心_优先级矩阵模板_安全, 服务管理中心_关闭代码配置_安全, 系统管理中心_客户管理_安全, 系统管理中心_短信发送策略_安全, 系统管理中心_短信模板_安全, 系统管理中心_触发条件模板_安全, 系统管理中心_邮件发送策略_安全, 系统管理中心_邮件模板_安全, 资产管理中心_区域_安全, 资产管理中心_告警分类_安全, 资产管理中心_场所_安全, 资产管理中心_线路供应商_安全, 资产管理中心_联系人_安全, 资产管理中心_行动策略_安全, 资产管理中心_设备_安全, 资产管理中心_设备供应商_安全, 资产管理中心_设备分组_安全, 资产管理中心_设备类型_安全, 服务管理中心_服务目录_安全, 安全管理中心_密码钱包_安全, 配置管理中心_项目管理_安全, 服务管理中心_工单组_安全, 服务管理中心_工单模版_安全, 服务管理中心_告警分类_安全 } from "@/views/pages/permission";

/**
 * @description 权限组
 */
export interface AvailablePermissionGroups {
  id: string;
  appId: string;
  name: string;
  orderNum: number;
  enabled: boolean;
}
/**
 * @description 用户组
 */
export interface AssignedUserGroupsItem {
  /** 用户组分配ID */
  assignId: string;
  /* 权限配置组ID */
  permissionGroupId: string;
  /** 安全容器ID */
  containerId: string;
  /** 用户组ID */
  userGroupId: AvailablePermissionGroups["id"];
  /** 用户组名称 */
  userGroupName?: string;
  /** 租户ID */
  tenantId?: string;
  /** 租户名称 */
  tenantName?: string;
  /** 租户缩写 */
  tenantAbbreviation?: string;
  /** 是不是继承过来的 */
  extend: boolean;
}
/**
 * @description 权限项
 */
export interface AssignedPermissionItem {
  groupAssignId: AssignedUserGroupsItem["assignId"];
  /** 配置项ID */
  itemId: string;
  /** 配置项名称 */
  itemName: string;
  /** 是否授权 */
  assign: boolean;
  /** 是否继承 */
  extend: boolean;
  /** 权限分配信息 */
  permissions: AssignedItem[];
}
/**
 * @description 权限
 */
export interface AssignedItem {
  /** 权限ID */
  id: string;
  /** 权限名称 */
  name: string;
  /** 是否授权 */
  assign: boolean;
  /** 是否继承，只有授权可继承 */
  extend: boolean;
  /** 是否需要双因素认证 */
  mfa: boolean;
  /** */
  sort: number;
  /** */
  children: { id: string; name: string; sort: number }[];
}
/**
 * @description 获取指定安全容器下的配置组
 * @url http://*************:3000/project/11/interface/api/16080
 */
export function getAvailablePermissionGroupsList(req: { appId: string /* 应用ID */; containerId: string /* 安全容器ID */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/available_permission_groups`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ appId: req.appId /* 应用ID */, containerId: req.containerId /* 安全容器ID */ }, $req.params);
        return $req;
      })
      .then(($req) => request<never, Response<AvailablePermissionGroups[]>>($req)),
    { controller }
  );
}
/**
 * @description 获取权限配置组下的用户组分配列表
 * @url http://*************:3000/project/11/interface/api/16087
 */
export function getAssignedUserGroupsList(req: { containerId: string /* 安全容器ID */; permissionGroupIds: string[] /* 权限配置组ID */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/assigned_user_groups/batch`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器ID */, permissionGroupIds: req.permissionGroupIds.join(",") /* 权限配置组ID */, permissionId: "512890964822458368" }, $req.params);
        return $req;
      })
      .then(($req) => request<never, Response<AssignedUserGroupsItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 获取分配关系下分配的权限
 * @url http://*************:3000/project/11/interface/api/16143
 */
export function getAssignedPermissionList(req: { extendView: boolean } & { groupAssignId: /* 用户组分配关系ID */ string; permissionGroupId: string; userGroupId: string; container: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/assigned_permissions/${req.groupAssignId /* 用户组分配关系ID */}`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ extendView: req.extendView /* 是否在继承的节点中查看分配关系, 如果为true, 则只能显示已分配且继承的权限 */, permissionGroupId: req.permissionGroupId, userGroupId: req.userGroupId, container: req.container || undefined }, $req.params);
        return $req;
      })
      .then(($req) => request<never, Response<AssignedPermissionItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 获取用户在指定安全容器下有分配权限的配置项
 * @url http://*************:3000/project/11/interface/api/26077
 */
export function getPermissionUsable(req: { appId: string /* 应用ID */; containerId: string /* 安全容器ID */; permissionGroupId: string /* 权限组ID */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/assignable_permission_items`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ appId: req.appId /* 应用ID */, containerId: req.containerId /* 安全容器ID */, permissionGroupId: req.permissionGroupId /* 权限组ID */ }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, Response<{ id: string; appId: string; groupId: string; name: string; orderNum: number; enabled: boolean }[]>>($req)),
    { controller }
  );
}

/**
 * @description 获取当前用户可分配的用户组列表
 * @url http://*************:3000/project/11/interface/api/19629
 */
export function getUserGroupUsable(req: { containerId: string /* 安全容器ID */; permissionGroupId: string /* 权限配置组ID */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/current_org/assignable_user_groups`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器ID */, permissionGroupId: req.permissionGroupId /* 权限配置组ID */, permissionId: "512890964822458368" }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, Response<{ id: /** 用户组ID */ string; containerId: /** 安全容器ID */ string; tenantId: /** 租户ID */ string; name: /** 用户组名称 */ string; note?: /** 用户组备注 */ string; email?: /** 邮箱 */ string; language?: /** 语言 */ string; zoneId?: /** 时区 */ string; active: /** 是否激活 */ boolean; mfaEnabled: /** 是否开启双因素认证 */ boolean; createdTime: /** 创建时间 */ string; updatedTime: /** 更新时间 */ string; createdBy?: /** 创建人 */ string; updatedBy?: /** 更新人 */ string; tenantName?: /** 租户名称 */ string; tenantAbbreviation?: /** 租户缩写 */ string }[]>>($req)),
    { controller }
  );
}
/* ============================================================================================================= */
/* ================================================    权  限    =============================================== */
/* ============================================================================================================= */

/**
 * @description 分配用户组
 * @url http://*************:3000/project/11/interface/api/16094
 */
export function addAssignedUserGroups(req: { containerId: /** 安全容器ID */ string; userGroupId: /** 用户组ID */ string; permissionGroupId: /** 权限配置组ID */ string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/assign_user_group`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        $req.data = { containerId: req.containerId, userGroupId: req.userGroupId, permissionGroupId: req.permissionGroupId };
        return $req;
      })
      .then(($req) => request<never, Response<AssignedUserGroupsItem>>($req)),
    { controller }
  );
}
/**
 * @description 移除用户组
 * @url http://*************:3000/project/11/interface/api/16101
 */
export function delAssignedUserGroups(req: { assignId: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/unassign_user_group/${req.assignId}`, method: Method.Delete, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, Response<AssignedUserGroupsItem>>($req)),
    { controller }
  );
}

/**
 * @description 自定义分配权限
 * @url http://*************:3000/project/11/interface/api/16108
 */
export function setAssignedByCustom(req: { groupAssignId: /** 用户组分配关系ID */ string; assign: /** 是否授权 */ boolean; extend?: /** 是否继承, assign为true时必填 */ boolean; mfa: /** 是否需要双因素认证 */ boolean; permissionIds: /** 权限ID列表 */ string[] }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/assign_permission`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        $req.data = { groupAssignId: req.groupAssignId, assign: req.assign, extend: req.extend === void 0 ? null : req.extend, mfa: req.mfa, permissionIds: req.permissionIds };
        return $req;
      })
      .then(($req) => request<never, Response<AssignedItem>>($req)),
    { controller }
  );
}
/**
 * @description 自定义分配权限v2
 * @url http://*************:3000/project/11/interface/api/31202
 */
export function setAssignedByGroupCustom(req: { containerId?: string /* 安全容器ID */; userGroupId: string /* 用户组ID */; permissionGroupId: string /* 权限组ID */; assign: boolean /* 是否授权 */; extend?: boolean /* 是否继承, assign为true时必填 */; mfa: boolean /* 是否需要双因素认证 */; permissionIds: string[] /* 权限ID列表 */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/assign_permission_v2`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = { "Content-Type": "application/json" };
        $req.params = new URLSearchParams();
        $req.data = { containerId: req.containerId, userGroupId: req.userGroupId, permissionGroupId: req.permissionGroupId, assign: req.assign, extend: req.extend === void 0 ? null : req.extend, mfa: req.mfa, permissionIds: req.permissionIds };
        return $req;
      })
      .then(($req) => request<never, Response<AssignedUserGroupsItem>>($req)),
    { controller }
  );
}
/**
 * @description 分配配置组下所有的权限
 * @url http://*************:3000/project/11/interface/api/21127
 */
export function setAssignedByFull(req: { extend: boolean /* 是否可继承, 默认否 */ } & { groupAssignId: /* 用户组分配关系ID */ string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/assign_group_all_permission/${req.groupAssignId /* 用户组分配关系ID */}`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ extend: req.extend /* 是否可继承, 默认否 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, Response<AssignedItem>>($req)),
    { controller }
  );
}
/**
 * @description 分配配置组下所有的权限v2
 * @url http://*************:3000/project/11/interface/api/31242
 */
export function setAssignedByGroupFull(req: { containerId: /* 安全容器ID */ string; userGroupId: /* 用户组ID */ string; permissionGroupId: /* 权限组ID */ string } & { extend: boolean /* 是否可继承, 默认否 */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/assign_group_all_permission_v2`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ extend: req.extend /* 是否可继承, 默认否 */ }, $req.params);
        $req.data = { containerId: req.containerId, userGroupId: req.userGroupId, permissionGroupId: req.permissionGroupId };
        return $req;
      })
      .then(($req) => request<never, Response<AssignedUserGroupsItem>>($req)),
    { controller }
  );
}
/**
 * @description 通过模板分配权限
 * @url http://*************:3000/project/11/interface/api/16115
 */
export function setAssignedByTemplate(req: { groupAssignId: /** 用户组分配关系ID */ string; templateId: /** 权限配置模板ID */ string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/assign_permission_by_template`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        $req.data = { groupAssignId: req.groupAssignId, templateId: req.templateId };
        return $req;
      })
      .then(($req) => request<never, Response<AssignedItem>>($req)),
    { controller }
  );
}
/**
 * @description 通过模板分配权限v2
 * @url http://*************:3000/project/11/interface/api/31234
 */
export function setAssignedByGroupTemplate(req: { containerId: string /* 安全容器ID */; userGroupId: string /* 用户组ID */; permissionGroupId: string /* 权限组ID */; templateId: string /* 权限配置模板ID */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/assign_permission_by_template_v2`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        $req.data = { containerId: req.containerId, userGroupId: req.userGroupId, permissionGroupId: req.permissionGroupId, templateId: req.templateId };
        return $req;
      })
      .then(($req) => request<never, Response<AssignedUserGroupsItem>>($req)),
    { controller }
  );
}
/**
 * @description 覆盖配置项权限
 * @url http://*************:3000/project/11/interface/api/17802
 */
export function setAssignedByCover(req: { permission: { assign: boolean; extend?: boolean; mfa: boolean; permissionId: string }[] } & { permissionItemId: string /* 权限配置项ID */ } & { groupAssignId: /* 用户组分配关系ID */ string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/cover_item_permission/${req.groupAssignId /* 用户组分配关系ID */}`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ permissionItemId: req.permissionItemId /* 权限配置项ID */ }, $req.params);
        $req.data = (req.permission instanceof Array ? req.permission : []).map((v) => v);
        return $req;
      })
      .then(($req) => request<never, Response<AssignedItem>>($req)),
    { controller }
  );
}
/**
 * @description 移除某几项权限
 * @url http://*************:3000/project/11/interface/api/16241
 */
export function delAssignedByCustom(req: { permissionIds: string[] /* 权限ID列表, 多个之间,分割(必填) */ } & { groupAssignId: /* 用户组分配关系ID */ string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/remove_permission/${req.groupAssignId /* 用户组分配关系ID */}`, method: Method.Delete, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ permissionIds: (req.permissionIds instanceof Array ? req.permissionIds : []).join(",") /* 权限ID列表, 多个之间,分割(必填) */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, Response<AssignedItem>>($req)),
    { controller }
  );
}
/**
 * @description 清空配置组权限
 * @url http://*************:3000/project/11/interface/api/16122
 */
export function clsAssignedByUserGroups(req: { groupAssignId: /** 用户组分配关系ID */ string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/empty_group_permission/${req.groupAssignId /* 用户组分配关系ID */}`, method: Method.Delete, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, Response<AssignedItem>>($req)),
    { controller }
  );
}
/**
 * @description 清空配置项权限
 * @url http://*************:3000/project/11/interface/api/16234
 */
export function clsAssignedByPermission(req: { permissionItemId: string /* 权限配置ID(必填) */; deleteAssigned: boolean /* 是否删除已分配的权限, 为空全部删除, true只删除分配的权限, false只删除禁止授权的权限 */ } & { groupAssignId: /* 用户组分配关系ID */ string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_container/empty_item_permission/${req.groupAssignId /* 用户组分配关系ID */}`, method: Method.Delete, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ permissionItemId: req.permissionItemId /* 权限配置ID(必填) */, deleteAssigned: req.deleteAssigned /* 是否删除已分配的权限, 为空全部删除, true只删除分配的权限, false只删除禁止授权的权限 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, Response<AssignedItem>>($req)),
    { controller }
  );
}

/* ============================================================================================================= */
/* ================================================安  全  容  器=============================================== */
/* ============================================================================================================= */
/**
 * @description 安全容器
 */
export interface SecurityContainer {
  id: string;
  parentId?: string;
  name: string;
  children: SecurityContainer[];
}

/**
 * @description 获取可访问的安全容器列表
 * @url http://*************:3000/project/11/interface/api/15849
 */
export function getSecurityContainer(req: { permissionId: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/current_org/security_containers/current_user/available_tree`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ permissionId: req.permissionId }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, Response<SecurityContainer[]>>($req)),
    { controller }
  );
}

/**
 * @description 创建安全容器
 * @url http://*************:3000/project/11/interface/api/15821
 */
export function addSecurityContainer(req: { parentId?: /** 父容器ID */ string; name: /** 容器名称 */ string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_containers`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        $req.data = { parentId: req.parentId === void 0 ? null : req.parentId, name: req.name };
        return $req;
      })
      .then(($req) => request<never, Response<SecurityContainer>>($req)),
    { controller }
  );
}
/**
 * @description 重命名安全容器
 * @url http://*************:3000/project/11/interface/api/15828
 */
export function modSecurityContainerByRename(req: { name: string /* 名称 */ } & { id: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_containers/${req.id /* 容器ID */}/rename`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ name: req.name /* 新名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, Response<SecurityContainer>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/11/interface/api/15835
 */
export function modSecurityContainerByMove(req: { parentId: string /* 要移动到的父容器ID */ } & { id: /* 容器ID */ string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_containers/${req.id /* 容器ID */}/move`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ parentId: req.parentId /* 要移动到的父容器ID */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, Response<SecurityContainer>>($req)),
    { controller }
  );
}

/**
 * @description 删除安全容器
 * @url http://*************:3000/project/11/interface/api/15842
 */
export function delSecurityContainer(req: { id: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_containers/${req.id /* 容器ID */}`, method: Method.Delete, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.data = new URLSearchParams();
        try {
          const { success, message, data } = await hasSecurityContainer(req);
          if (!success) throw new Error(message);
          if (!data) throw new Error("此安全容器不可删除");
        } catch (error) {
          /*  */
        }
        return $req;
      })
      .then(($req) => request<never, Response<SecurityContainer>>($req)),
    { controller }
  );
}

/**
 * @description 判断安全容器是否允许删除
 * @url http://*************:3000/project/11/interface/api/16318
 */
export function hasSecurityContainer(req: { id: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_containers/${req.id /* 安全容器ID */}/deletable`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        return $req;
      })
      .then(($req) => request<never, Response<boolean>>($req)),
    { controller }
  );
}

/* ============================================================================================================= */
/* ================================================ 容器资源数据 =============================================== */
/* ============================================================================================================= */

export enum ContactType {
  /**@type {string} - 客户 */
  TENANT = "iam.tenant",
  /**@type {string} - 用户 */
  USER = "iam.user",
  /**@type {string} - 用户组 */
  USER_GROUP = "iam.user_group",
  /**@type {string} - 设备 */
  RESOURCE = "cmdb.resource",
  /**@type {string} - 设备文件 */
  RESOURCE_FILE = "cmdb.resourceFile",
  /**@type {string} - 设备日志 */
  RESOURCE_LOG = "cmdb.resourceLog",
  /**@type {string} - 区域 */
  REGION = "cmdb.region",
  /**@type {string} - 场所 */
  LOCATION = "cmdb.location",
  /**@type {string} - 联系人 */
  CONTACT = "cmdb.contact",
  /**@type {string} - 设备分组 */
  GROUP = "cmdb.group",
  /**@type {string} - 设备类型 */
  RESOURCE_TYPE = "cmdb.resourceType",
  /**@type {string} - 告警分类 */
  ALERT_CLASSIFICATION = "cmdb.alertClassification",
  /**@type {string} - 供应商 */
  VENDOR = "cmdb.vendor",
  /**@type {string} - 线路供应商 */
  LINE_VENDOR = "cmdb.lineVendor",
  /**@type {string} - 设备供应商 */
  DEVICE_VENDOR = "cmdb.deviceVendor",
  /**@type {string} - 行动策略 */
  SUPPORT_NOTE = "cmdb.supportNote",
  /**@type {string} - 优先级矩阵 */
  PRIORITY_MATRIX = "ec.priorityMatrix",
  /**@type {string} - 关闭代码 */
  CODE_CONFIG = "ec.codeConfig",
  /**@type {string} - SLA */
  SLA = "ec.sla",
  /**@type {string} - 短信模版 */
  SMS_TEMPLATE = "ec.smsTemplate",
  /**@type {string} - 短信发送模版 */
  SMS_SEND_TEMPLATE = "ec.smsSendTemplate",
  /**@type {string} - 邮件模版 */
  EMAIL_TEMPLATE = "ec.emailTemplate",
  /**@type {string} - 邮件发送模版 */
  EMAIL_SEND_TEMPLATE = "ec.emailSendTemplate",
  /**@type {string} - 触发条件模版 */
  TRIGGER_CONDITION_TEMPLATE = "ec.triggerConditionTemplate",
  /**@type {string} - 服务目录 */
  SERVICE_CATALOG = "ec.serviceCatalog",
  /**@type {string} - 密码钱包 */
  CRYPTOW_WALLETS = "ec.cryptow_wallets",
  /**@type {string} - 项目管理 */
  PROJECT_MANAGEMENT = "ec.dictproject",
  /**@type {string} - 工单组 */
  TICKET_GROUP = "ec.ticket_group",
  /**@type {string} - 工单模版 */
  TICKET_TEMPLATE = "ec.ticket_templates",
  /**@type {string} - 未知 */
  UNKNOWN = "",
}
export const option = new Map<ContactType, { label: string; value: ContactType; permission: string[] }>([
  [ContactType.TENANT, { label: "客户", value: ContactType.TENANT, permission: [系统管理中心_客户管理_安全] }],
  [ContactType.USER, { label: "用户", value: ContactType.USER, permission: [安全管理中心_用户管理_安全] }],
  [ContactType.USER_GROUP, { label: "用户组", value: ContactType.USER_GROUP, permission: [安全管理中心_用户组管理_安全] }],
  [ContactType.RESOURCE, { label: "设备", value: ContactType.RESOURCE, permission: [资产管理中心_设备_安全] }],
  [ContactType.RESOURCE_FILE, { label: "设备文件", value: ContactType.RESOURCE_FILE, permission: [] }],
  [ContactType.RESOURCE_LOG, { label: "设备日志", value: ContactType.RESOURCE_LOG, permission: [] }],
  [ContactType.REGION, { label: "区域", value: ContactType.REGION, permission: [资产管理中心_区域_安全] }],
  [ContactType.LOCATION, { label: "场所", value: ContactType.LOCATION, permission: [资产管理中心_场所_安全] }],
  [ContactType.CONTACT, { label: "联系人", value: ContactType.CONTACT, permission: [资产管理中心_联系人_安全] }],
  [ContactType.GROUP, { label: "设备分组", value: ContactType.GROUP, permission: [资产管理中心_设备分组_安全] }],
  [ContactType.RESOURCE_TYPE, { label: "设备类型", value: ContactType.RESOURCE_TYPE, permission: [资产管理中心_设备类型_安全] }],
  [ContactType.ALERT_CLASSIFICATION, { label: "告警分类", value: ContactType.ALERT_CLASSIFICATION, permission: [服务管理中心_告警分类_安全] }],
  [ContactType.VENDOR, { label: "供应商", value: ContactType.VENDOR, permission: [] }],
  [ContactType.LINE_VENDOR, { label: "线路供应商", value: ContactType.LINE_VENDOR, permission: [资产管理中心_线路供应商_安全] }],
  [ContactType.DEVICE_VENDOR, { label: "设备供应商", value: ContactType.DEVICE_VENDOR, permission: [资产管理中心_设备供应商_安全] }],
  [ContactType.SUPPORT_NOTE, { label: "行动策略", value: ContactType.SUPPORT_NOTE, permission: [资产管理中心_行动策略_安全] }],
  [ContactType.PRIORITY_MATRIX, { label: "优先级矩阵", value: ContactType.PRIORITY_MATRIX, permission: [服务管理中心_优先级矩阵模板_安全] }],
  [ContactType.CODE_CONFIG, { label: "关闭代码", value: ContactType.CODE_CONFIG, permission: [服务管理中心_关闭代码配置_安全] }],
  [ContactType.SLA, { label: "SLA", value: ContactType.SLA, permission: [服务管理中心_SLA配置_安全] }],
  [ContactType.SMS_TEMPLATE, { label: "短信模版", value: ContactType.SMS_TEMPLATE, permission: [系统管理中心_短信模板_安全] }],
  [ContactType.SMS_SEND_TEMPLATE, { label: "短信发送模版", value: ContactType.SMS_SEND_TEMPLATE, permission: [系统管理中心_短信发送策略_安全] }],
  [ContactType.EMAIL_TEMPLATE, { label: "邮件模版", value: ContactType.EMAIL_TEMPLATE, permission: [系统管理中心_邮件模板_安全] }],
  [ContactType.EMAIL_SEND_TEMPLATE, { label: "邮件发送模版", value: ContactType.EMAIL_SEND_TEMPLATE, permission: [系统管理中心_邮件发送策略_安全] }],
  [ContactType.TRIGGER_CONDITION_TEMPLATE, { label: "触发条件模版", value: ContactType.TRIGGER_CONDITION_TEMPLATE, permission: [系统管理中心_触发条件模板_安全] }],

  [ContactType.SERVICE_CATALOG, { label: "服务包", value: ContactType.SERVICE_CATALOG, permission: [服务管理中心_服务目录_安全] }],
  [ContactType.CRYPTOW_WALLETS, { label: "密码钱包", value: ContactType.CRYPTOW_WALLETS, permission: [安全管理中心_密码钱包_安全] }],
  [ContactType.PROJECT_MANAGEMENT, { label: "项目管理", value: ContactType.PROJECT_MANAGEMENT, permission: [配置管理中心_项目管理_安全] }],
  [ContactType.TICKET_GROUP, { label: "工单组", value: ContactType.TICKET_GROUP, permission: [服务管理中心_工单组_安全] }],
  [ContactType.TICKET_TEMPLATE, { label: "工单模版", value: ContactType.TICKET_TEMPLATE, permission: [服务管理中心_工单模版_安全] }],
]);
export interface BaseDataItem {
  id: string;
  containerId: string;
  name?: string;
}
export type FatchQueryController = (req: ResourceDataBaseQuery) => Promise<ResourceDataBaseResponse<BaseDataItem[]>>;
export type FatchMoveController = (req: ResourceDataMoveQuery) => Promise<ResourceDataBaseResponse<null>>;
interface ColumnMeta {
  label: string;
  value: string;
  formatter: (value: string, row: Record<string, unknown>, index: number) => string | import("vue").VNode;
}
interface OptionsMeta {
  label: string;
  value: ContactType;
  fatch: FatchQueryController;
  move: FatchMoveController;
  column: ColumnMeta[];
  builtInTenant: boolean;
}

export function generateContactTypeMeta(type): OptionsMeta {
  switch (type) {
    case ContactType.TENANT: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value, row) => `${value || "--"}${row.abbreviation ? `[${row.abbreviation}]` : ""}` },
      ];
      return { column, label: "客户", builtInTenant: false, value: ContactType.TENANT, fatch: getTenantBySecurityContainer, move: modTenantBySecurityContainer };
    }
    case ContactType.USER: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
        { label: "客户", value: "tenantName", formatter: (value, row) => `${value || "--"}${row.tenantAbbreviation ? `[${row.tenantAbbreviation}]` : ""}` },
      ];
      return { column, label: "用户", builtInTenant: true, value: ContactType.USER, fatch: getUserBySecurityContainer, move: modUserBySecurityContainer };
    }
    case ContactType.USER_GROUP: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
        { label: "客户", value: "tenantName", formatter: (value, row) => `${value || "--"}${row.tenantAbbreviation ? `[${row.tenantAbbreviation}]` : ""}` },
      ];
      return { column, label: "用户组", builtInTenant: true, value: ContactType.USER_GROUP, fatch: getUserGroupBySecurityContainer, move: modUserGroupBySecurityContainer };
    }
    case ContactType.RESOURCE: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
        { label: "客户", value: "tenantName", formatter: (value, row) => `${value || "--"}${row.tenantAbbreviation ? `[${row.tenantAbbreviation}]` : ""}` },
      ];
      return { column, label: "设备", builtInTenant: true, value: ContactType.RESOURCE, fatch: getResourceBySecurityContainer, move: modResourceBySecurityContainer };
    }
    case ContactType.RESOURCE_FILE: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "设备文件", builtInTenant: false, value: ContactType.RESOURCE_FILE, fatch: getResourceFileBySecurityContainer, move: modResourceFileBySecurityContainer };
    }
    case ContactType.RESOURCE_LOG: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "设备日志", builtInTenant: false, value: ContactType.RESOURCE_LOG, fatch: getResourceLogBySecurityContainer, move: modResourceLogBySecurityContainer };
    }
    case ContactType.REGION: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
        { label: "客户", value: "tenantName", formatter: (value, row) => `${value || "--"}${row.tenantAbbreviation ? `[${row.tenantAbbreviation}]` : ""}` },
      ];
      return { column, label: "区域", builtInTenant: true, value: ContactType.REGION, fatch: getRegionsBySecurityContainer, move: modRegionsBySecurityContainer };
    }
    case ContactType.LOCATION: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
        { label: "客户", value: "tenantName", formatter: (value, row) => `${value || "--"}${row.tenantAbbreviation ? `[${row.tenantAbbreviation}]` : ""}` },
      ];
      return { column, label: "场所", builtInTenant: true, value: ContactType.LOCATION, fatch: getLocationBySecurityContainer, move: modLocationBySecurityContainer };
    }
    case ContactType.CONTACT: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
        { label: "客户", value: "tenantName", formatter: (value, row) => `${value || "--"}${row.tenantAbbreviation ? `[${row.tenantAbbreviation}]` : ""}` },
      ];
      return { column, label: "联系人", builtInTenant: true, value: ContactType.CONTACT, fatch: getContactsBySecurityContainer, move: modContactsBySecurityContainer };
    }
    case ContactType.GROUP: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
        { label: "客户", value: "tenantName", formatter: (value, row) => `${value || "--"}${row.tenantAbbreviation ? `[${row.tenantAbbreviation}]` : ""}` },
      ];
      return { column, label: "设备分组", builtInTenant: true, value: ContactType.GROUP, fatch: getDeviceGroupsBySecurityContainer, move: modDeviceGroupsBySecurityContainer };
    }
    case ContactType.RESOURCE_TYPE: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "设备类型", builtInTenant: false, value: ContactType.RESOURCE_TYPE, fatch: getResourceTypeBySecurityContainer, move: modResourceTypeBySecurityContainer };
    }
    case ContactType.ALERT_CLASSIFICATION: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "告警分类", builtInTenant: false, value: ContactType.ALERT_CLASSIFICATION, fatch: getAlertClassificationsBySecurityContainer, move: modAlertClassificationsBySecurityContainer };
    }
    case ContactType.VENDOR: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "供应商", builtInTenant: false, value: ContactType.VENDOR, fatch: async () => ({ success: false, message: "供应商已被弃用！", data: [], page: 1, size: 10, total: 0 }), move: modVendorBySecurityContainer };
    }
    case ContactType.LINE_VENDOR: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "线路供应商", builtInTenant: false, value: ContactType.LINE_VENDOR, fatch: getVendorLineBySecurityContainer, move: modVendorBySecurityContainer };
    }
    case ContactType.DEVICE_VENDOR: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "设备供应商", builtInTenant: false, value: ContactType.DEVICE_VENDOR, fatch: getVendorDeviceBySecurityContainer, move: modVendorBySecurityContainer };
    }
    case ContactType.SUPPORT_NOTE: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "行动策略", builtInTenant: false, value: ContactType.SUPPORT_NOTE, fatch: getSupportNoteBySecurityContainer, move: modSupportNoteBySecurityContainer };
    }
    case ContactType.PRIORITY_MATRIX: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "优先级矩阵", builtInTenant: false, value: ContactType.PRIORITY_MATRIX, fatch: getPriorityMatrixBySecurityContainer, move: modPriorityMatrixBySecurityContainer };
    }
    case ContactType.CODE_CONFIG: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "关闭代码", builtInTenant: false, value: ContactType.CODE_CONFIG, fatch: getCodeConfigBySecurityContainer, move: modCodeConfigBySecurityContainer };
    }
    case ContactType.SLA: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "SLA", builtInTenant: false, value: ContactType.SLA, fatch: getSlaRuleBySecurityContainer, move: modSlaRuleBySecurityContainer };
    }
    case ContactType.SMS_TEMPLATE: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "短信模版", builtInTenant: false, value: ContactType.SMS_TEMPLATE, fatch: getMessageTemplateBySecurityContainer, move: modMessageTemplateBySecurityContainer };
    }
    case ContactType.SMS_SEND_TEMPLATE: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "短信发送模版", builtInTenant: false, value: ContactType.SMS_SEND_TEMPLATE, fatch: getMessageStrategyBySecurityContainer, move: modMessageStrategyBySecurityContainer };
    }
    case ContactType.EMAIL_TEMPLATE: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "邮件模版", builtInTenant: false, value: ContactType.EMAIL_TEMPLATE, fatch: getEmailTemplateBySecurityContainer, move: modEmailTemplateBySecurityContainer };
    }
    case ContactType.EMAIL_SEND_TEMPLATE: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "邮件发送模版", builtInTenant: false, value: ContactType.EMAIL_SEND_TEMPLATE, fatch: getEmailStrategyBySecurityContainer, move: modEmailStrategyBySecurityContainer };
    }
    case ContactType.TRIGGER_CONDITION_TEMPLATE: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "触发条件模版", builtInTenant: false, value: ContactType.TRIGGER_CONDITION_TEMPLATE, fatch: getTriggerConditionBySecurityContainer, move: modTriggerConditionBySecurityContainer };
    }

    case ContactType.SERVICE_CATALOG: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "服务目录", builtInTenant: false, value: ContactType.SERVICE_CATALOG, fatch: getTriggerConditionByServiceCatalog, move: modTriggerConditionByServiceCatalog };
    }

    case ContactType.CRYPTOW_WALLETS: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "密码钱包", builtInTenant: false, value: ContactType.CRYPTOW_WALLETS, fatch: getTriggerConditionByCryptowWallets, move: modTriggerConditionByCryptowWallets };
    }

    case ContactType.PROJECT_MANAGEMENT: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "projectName", formatter: (value) => `${value || "--"}` },
        {
          label: "客户",
          value: "tenantId",
          formatter: (value) => {
            const userInfo = getUserInfo();
            const tenant = userInfo.tenants.find((item) => item.id === value);
            if (tenant) return `${tenant.name || "--"}${tenant.abbreviation ? `[${tenant.abbreviation}]` : ""}`;
            else return "--";
          },
        },
      ];
      return { column, label: "项目", builtInTenant: false, value: ContactType.PROJECT_MANAGEMENT, fatch: getTriggerConditionByProjectManagement, move: modTriggerConditionByProjectManagement };
    }

    case ContactType.TICKET_GROUP: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "ticketGroupName", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "工单组", builtInTenant: false, value: ContactType.TICKET_GROUP, fatch: getTriggerConditionByTicketGroup, move: modTriggerConditionByTicketGroup };
    }

    case ContactType.TICKET_TEMPLATE: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "ticketTemplatesName", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: "工单模版", builtInTenant: false, value: ContactType.TICKET_TEMPLATE, fatch: getTriggerConditionByTicketTemplate, move: modTriggerConditionByTicketTemplate };
    }

    default: {
      const column: ColumnMeta[] = [
        /*  */
        { label: "名称", value: "name", formatter: (value) => `${value || "--"}` },
      ];
      return { column, label: `UNKNOWN(${type})`, builtInTenant: false, value: ContactType.UNKNOWN, fatch: async () => ({ success: false, message: `[Web Error] Not Find: RESOURCE By "${type}"`, data: [], page: 1, size: 10, total: 0 }), move: async () => ({ success: false, message: `[Web Error] Not Supported: RESOURCE By "${type}"`, data: null }) };
    }
  }
}

/**
 * @description 过滤权限ID
 * @url http://*************:3000/project/11/interface/api/31210
 */
export function hasPermissionByContainer(req: { appId: string /* 应用ID */; containerId: string /* 容器ID */ }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/tbac/filter_permission_ids`, method: Method.Post, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ appId: req.appId /* 应用ID */, containerId: req.containerId /* 容器ID */ }, $req.params);
        $req.data = Array.from(option.values()).reduce<string[]>((p, c) => p.concat(c.permission), []);
        return $req;
      })
      .then(($req) => request<never, Response<string[]>>($req))
      .then(($res) => Object.assign($res, { data: Array.from(option.entries()).reduce<ContactType[]>((p, [k, { permission }]) => (!permission.length || permission.every((v) => ($res.data instanceof Array ? $res.data : []).includes(v)) ? p.concat(k) : p), []) })),
    { controller }
  );
}

/**
 * @description 获取安全容器节点下拥有的资源类型列表
 * @url http://*************:3000/project/11/interface/api/17753
 */
export function catSecurityContainerResourceType(req: { id: string }) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_containers/${req.id /* 安全容器ID */}/exists_resource_types`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        return $req;
      })
      .then(($req) => request<never, Response<ContactType[]>>($req)),
    { controller }
  );
}

interface ResourceDataBaseQuery {
  paging: { pageNumber: number; pageSize: number };
  sort?: string[];
  containerId: string;
}
interface ResourceDataMoveQuery {
  id: string;
  /** 安全容器id */
  containerId: string;
  /** 旧的安全容器名称 */
  oldContainerName: string;
  /** 新的安全容器名称 */
  newContainerName: string;
}
interface ResourceDataBaseResponse<T> {
  success: boolean;
  message: string;
  data: T;
  page?: number;
  size?: number;
  total?: number;
}
/* TODO: ==================================================== IAM ==================================================== */

/**
 * @description 安全容器下客户
 */
export interface TenantsItem {
  /** 租户ID */
  id: /* Integer */ string;
  /** 安全容器ID */
  containerId: /* Integer */ string;
  /** 租户名称 */
  name: string;
  /** 租户缩写 */
  abbreviation?: string;
  /** 系统版本 */
  systemEdition?: string;
  /** 时区ID */
  zoneId?: string;
  /** 语言 */
  language?: string;
  /** 地址 */
  address?: string;
  /** 备注 */
  note?: string;
  /** 是否拥有完整权限 */
  fullPermission: boolean;
  /** 双因素认证启用状态 */
  mfaState?: /* 枚举: ENABLED :启用MFA认证 | DISABLED :禁用MFA认证 | DEFAULT :使用系统默认配置 */ "ENABLED" | "DISABLED" | "DEFAULT";
  /** 拥有人用户ID */
  ownerId?: /* Integer */ string;
  /** 拥有人姓名 */
  ownerName?: string;
  /** 拥有人昵称 */
  ownerNickname?: string;
  /** 拥有人账号 */
  ownerAccount?: string;
  /** 拥有人手机号 */
  ownerPhone?: string;
  /** 拥有人邮箱 */
  ownerEmail?: string;
  /** 租户是否已被锁定 */
  blocked: boolean;
  /** 租户是否已激活 */
  activated: boolean;
  /** 乐观锁版本 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人信息 */
  createdBy?: string;
  /** 更新人信息 */
  updatedBy?: string;
  /** 租户邮编 */
  tenantPostcode?: string;
  /** 租户邮箱 */
  tenantEmail?: string;
  /** 租户传真 */
  tenantFax?: string;
  /** 租户电话 */
  tenantPhone?: string;
  /** 租户类型 */
  tenantType?: string;
  /** 租户签约地 */
  tenantSigningPlace?: string;
  /** 租户签约地2 */
  tenantSigningPlace2?: string;
  /** 租户行业 */
  tenantIndustry?: string;
  /** 租户性质 */
  tenantNature?: string;
  /** 租户渠道 */
  tenantChannel?: string;
  /** 租户是否运营商 */
  tenantOperator: boolean;
  /** 租户是否运营商 */
  tenantForeign: boolean;
  /** 是否默认启用了MFA */
  enableMfaDefault: boolean;
}
/**
 * 客户
 * TENANT
 * @description 获取安全容器下客户列表
 * @url http://*************:3000/project/11/interface/api/19867
 */
export function getTenantBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_containers/${req.containerId /* 安全容器ID */}/tenants`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<TenantsItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/11/interface/api/15954
 */
export function modTenantBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/tenants/${req.id /* 租户ID */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器ID */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

/**
 * @description 安全容器下用户组
 */
export interface UserGroupItem {
  /** 用户组ID */
  id: /* Integer */ string;
  /** 安全容器ID */
  containerId: /* Integer */ string;
  /** 租户ID */
  tenantId?: /* Integer */ string;
  /** 用户组名称 */
  name?: string;
  /** 用户组备注 */
  note?: string;
  /** 邮箱 */
  email?: string;
  /** 语言 */
  language?: string;
  /** 时区 */
  zoneId?: string;
  /** 是否激活 */
  active: boolean;
  /** 是否开启双因素认证 */
  mfaEnabled: boolean;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 更新人 */
  updatedBy?: string;
  /** 租户名称 */
  tenantName?: string;
  /** 租户缩写 */
  tenantAbbreviation?: string;
}
/**
 * 用户组
 * USER_GROUP
 * @description 分页获取安全容器下用户组列表
 * @url http://*************:3000/project/11/interface/api/19860
 */
export function getUserGroupBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_containers/${req.containerId /* 安全容器ID */}/user_groups`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<UserGroupItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动用户组安全容器
 * @url http://*************:3000/project/11/interface/api/15471
 */
export function modUserGroupBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/user_groups/${req.id /* 用户组ID */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器ID */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

/**
 * @description 安全容器下的用户
 */
export interface UserItem {
  /** 用户ID */
  id: /* Integer */ string;
  /** 所属平台 */
  platform?: string;
  /** 所属租户 */
  tenantId?: /* Integer */ string;
  /** 安全容器ID */
  containerId: /* Integer */ string;
  /** 姓名 */
  name?: string;
  /** 昵称 */
  nickname?: string;
  /** 账号 */
  account?: string;
  /** 手机号 */
  phone?: string;
  /** 邮箱 */
  email?: string;
  /** 语言 */
  language?: string;
  /** 时区 */
  zoneId?: string;
  /** 性别 */
  gender?: /* 枚举: SECRET :保密 | MALE :男性 | FEMALE :女性 */ "SECRET" | "MALE" | "FEMALE";
  /** 头像 */
  profilePicture?: string;
  /** 双因素认证启用状态 */
  mfaState?: /* 枚举: ENABLED :启用MFA认证 | DISABLED :禁用MFA认证 | DEFAULT :使用系统默认配置 */ "ENABLED" | "DISABLED" | "DEFAULT";
  /** 密码修改时间戳 */
  passwordTime?: /* Integer */ string;
  /** 是否忙碌 */
  busy: boolean;
  /** 进入忙碌状态的时间戳 */
  busyTime?: /* Integer */ string;
  /** 是否已完善个人信息 */
  improved: boolean;
  /** 是否被锁定 */
  blocked: boolean;
  /** 最近登录时间 */
  lastLoginTime?: /* Integer */ string;
  /** 最近访问时间 */
  lastActivity?: /* Integer */ string;
  /** 创建时间 */
  createdTime?: /* Integer */ string;
  /** 所属租户名称 */
  tenantName?: string;
  /** 所属租户的缩写 */
  tenantAbbreviation?: string;
  /** 是否默认启用了MFA */
  enableMfaDefault: boolean;
  /** 是否已脱敏 */
  desensitized: boolean;
}
/**
 * 用户
 * USER
 * @description 分页获取安全容器下的用户列表
 * @url http://*************:3000/project/11/interface/api/19853
 */
export function getUserBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/security_containers/${req.containerId /* 安全容器ID */}/users`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<UserItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动用户到指定安全容器
 * @url http://*************:3000/project/11/interface/api/17760
 */
export function modUserBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.IAM}/users/id/${req.id /* 用户ID */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器ID */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

/* TODO: ==================================================== CMDB =================================================== */
/**
 * @description 资源
 */
export interface ResourceItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 安全容器 */
  containerId: /* Integer */ string;
  /** 资源池id */
  resourcePoolId: /* Integer */ string;
  /** 管理区编码 */
  precinctCode?: string;
  /** 模型标识 */
  modelIdent: string;
  /** 所在区域ID */
  regionId: /* Integer */ string;
  /** 所在场所ID */
  locationId: /* Integer */ string;
  /** 资源类型ID列表 */
  typeIds: /* Integer */ string[];
  /** 资源关联设备组ID列表 */
  groupIds: /* Integer */ string[];
  /** 服务商ID列表 */
  vendorIds: /* Integer */ string[];
  /** 行动策略列表 */
  supportNoteIds: /* Integer */ string[];
  /** 告警分类ID列表 */
  alertClassificationIds: /* Integer */ string[];
  /** 资产编号 */
  assetNumber?: string;
  /** 外部ID */
  externalId?: string;
  /** 资源名称 */
  name: string;
  /** 别名 */
  alias?: string;
  /** 资源上线时间 */
  resourceOnlineTime?: /* Integer */ string;
  /** 资源下线时间 */
  resourceOfflineTime?: /* Integer */ string;
  /** 是否交付 */
  delivery: boolean;
  /** 监控源 */
  monitorSources: string[];
  /** 业务单位 */
  unit?: string;
  /** 备注|描述信息 */
  description?: string;
  /** 时区 */
  timeZone?: string;
  /** 资源重要性 */
  importance: /* 枚举: High :至关重要的 | Medium :中 | Low :低 | None :无 | Unknown :未知的 */ "High" | "Medium" | "Low" | "None" | "Unknown";
  /** 标签列表 */
  tags: string[];
  /** 资源配置信息 */
  config: { [key: string]: string | undefined | null };
  /** 是否激活 */
  active: boolean;
  /** 最后上线时间 */
  onlineTime: /* Integer */ string;
  /** 最后离线时间 */
  offlineTime: /* Integer */ string;
  /** 服务包 */
  servicePackage?: string;
  /** 协议名 */
  protocolName?: string;
  /** 变更版本号 */
  version: /* Integer */ string;
  /** 录入时间 */
  createdTime: /* Integer */ string;
  /** 最近变更时间 */
  updatedTime: /* Integer */ string;
  /** 录入人 */
  createdBy?: string;
  /** 最近变更人 */
  updatedBy?: string;
}
/**
 * 设备
 * RESOURCE
 * @description 根据安全容器ID分页查询资源列表
 * @url http://*************:3000/project/47/interface/api/19916
 */
export function getResourceBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/resources/${req.containerId /* 安全容器id */}/tenant/current/desensitized`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<ResourceItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/47/interface/api/15856
 */
export function modResourceBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/resources/${req.id /* 资源id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

/**
 * @description 资源文件
 */
export interface ResourceFileItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 资源ID */
  resourceId: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 文件名称 */
  name: string;
  filePath: string;
  /** 文件大小，单位：kb */
  size: /* Integer */ string;
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}
/**
 * 设备文件
 * RESOURCE_FILE
 * @description 根据安全容器id分页查询资源文件
 * @url http://*************:3000/project/47/interface/api/25969
 */
export function getResourceFileBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/resource_files/${req.containerId /* 安全容器id */}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<ResourceFileItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/47/interface/api/25975
 */
export function modResourceFileBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/resource_files/${req.id /* 资源文件id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

/**
 * @description 资源日志
 */
export interface ResourceLogItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 资源ID */
  resourceId: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 资源名称 */
  resourceName?: string;
  /** 日志描述 */
  logNote: string;
  /** 日志附件 */
  fileInfos: {
    /** 文件名 */
    name: string;
    /** 文件大小, 单位: kb */
    size: /* Integer */ string;
    /** 文件路径 */
    filePath: string;
    /** 访问url */
    url: string;
  }[];
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}
/**
 * 资源日志
 * RESOURCE_LOG
 * @description 根据安全容器id分页查询资源日志
 * @url http://*************:3000/project/47/interface/api/25981
 */
export function getResourceLogBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/resource_logs/${req.containerId /* 安全容器id */}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<ResourceLogItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/47/interface/api/25987
 */
export function modResourceLogBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/resource_logs/${req.id /* 资源日志id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = { "Content-Type": "application/x-www-form-urlencoded" };
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

/**
 * @description 区域
 */
export interface RegionsItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 安全容器 */
  containerId: /* Integer */ string;
  /** 父区域Id */
  parentId: /* Integer */ string;
  /** 行动策略列表 */
  supportNoteIds: string[];
  label?: string;
  /** 区域名称 */
  name: string;
  /** 描述信息 */
  description?: string;
  /** 外部ID */
  externalId?: string;
  /** 纬度 */
  latitude?: string;
  /** 经度 */
  longitude?: string;
  /** 是否激活 */
  active: boolean;
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}
/**
 * 区域
 * REGION
 * @description 根据安全容器ID分页查询区域
 * @url http://*************:3000/project/47/interface/api/19902
 */
export function getRegionsBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/regions/${req.containerId /* 安全容器id */}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<RegionsItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/47/interface/api/15863
 */
export function modRegionsBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/regions/${req.id /* 区域id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器Id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

/**
 * @description 场所
 */
export interface LocationItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 区域ID */
  regionId: /* Integer */ string;
  /** 行动策略列表 */
  supportNoteIds: string[];
  /** 场所名称 */
  name: string;
  /** 描述 */
  description?: string;
  /** 时区Id */
  zoneId?: string;
  /** 外部ID */
  externalId?: string;
  /** 国家编码 */
  country?: string;
  /** 省级编码 */
  province?: string;
  /** 市级编码 */
  city?: string;
  /** 邮编代码 */
  postcode?: string;
  /** 地址 */
  address: string[];
  /** 是否激活 */
  active: boolean;
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}
/**
 * 场所
 * LOCATION
 * @description 根据安全区域ID分页查询场所列表
 * @url http://*************:3000/project/47/interface/api/19909
 */
export function getLocationBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/locations/${req.containerId /* 安全区域ID */}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<LocationItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/47/interface/api/15870
 */
export function modLocationBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/locations/${req.id /* 场所id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

/**
 * @description 联系人
 */
export interface ContactsItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 头衔 */
  title?: string;
  /** 姓名 */
  name: string;
  /** 语言 */
  language?: string;
  /** 邮箱地址 */
  email?: string;
  /** 固定电话 */
  landlinePhone?: string;
  /** 移动电话 */
  mobilePhone?: string;
  /** 下班后联系电话 */
  afterWorkPhone?: string;
  /** 短信号码 */
  smsPhone?: string;
  /** 传真 */
  fax?: string;
  /** 是否启用短信 */
  smsEnabled: boolean;
  /** 是否VIP */
  vip: boolean;
  /** 备注 */
  note?: string;
  /** 时区ID */
  zoneId?: string;
  /** 外部ID */
  externalId?: string;
  /** 是否激活 */
  active: boolean;
  /** 是否国际电话 */
  internationalPhone: boolean;
  /** 是否接受邮件 */
  acceptEmail: boolean;
  /** 职位 */
  position?: string;
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}
/**
 * 联系人
 * CONTACT
 * @description 根据安全容器id分页查询联系人
 * @url http://*************:3000/project/47/interface/api/19923
 */
export function getContactsBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/contacts/${req.containerId /* 安全容器id */}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<ContactsItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/47/interface/api/15877
 */
export function modContactsBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/contacts/${req.id /* 联系人id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

/**
 * @description 设备组
 */
export interface DeviceGroupsItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 名称 */
  name: string;
  /** 描述信息 */
  description?: string;
  /** 是否为报告分组 */
  report: boolean;
  /** 告警分类ID列表 */
  alertClassificationIds: string[];
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}
/**
 * 设备分组
 * GROUP
 * @description 根据安全容器id分页查询设备组
 * @url http://*************:3000/project/47/interface/api/19944
 */
export function getDeviceGroupsBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/groups/${req.containerId /* 安全容器id */}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<DeviceGroupsItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/47/interface/api/15884
 */
export function modDeviceGroupsBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/groups/${req.id /* 组id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器ID */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

/**
 * @description 资源类型
 */
export interface ResourceTypeItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 类型名称 */
  name: string;
  /** 类型英文名称 */
  enName?: string;
  /** 描述 */
  description?: string;
  /** 告警分类ID列表 */
  alertClassificationIds: string[];
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}
/**
 * 设备类型
 * RESOURCE_TYPE
 * @description 根据安全容器id分页查询资源类型
 * @url http://*************:3000/project/47/interface/api/19951
 */
export function getResourceTypeBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/resource_types/${req.containerId /* 安全容器id */}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<ResourceTypeItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/47/interface/api/15891
 */
export function modResourceTypeBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/resource_types/${req.id /* 资源类型id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

/**
 * @description 告警分类
 */
export interface AlertClassificationsItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 分类名称 */
  name: string;
  /** 描述信息 */
  description?: string;
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}
/**
 * 告警分类
 * ALERT_CLASSIFICATION
 * @description 根据安全容器id分页查询告警分类
 * @url http://*************:3000/project/47/interface/api/19958
 */
export function getAlertClassificationsBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/alert_classifications/${req.containerId /* 安全容器id */}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<AlertClassificationsItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/47/interface/api/15898
 */
export function modAlertClassificationsBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/alert_classifications/${req.id /* 告警分类id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

/**
 * @description 供应商(VENDOR)
 */
export interface VendorItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 名称 */
  name: string;
  /** 描述 */
  description?: string;
  /** 地址 */
  address?: string;
  /** 固定电话 */
  landlinePhone?: string;
  /** 支持电话 */
  supportPhone?: string;
  /** 联系人姓名 */
  contactName?: string;
  /** 电子邮箱 */
  email?: string;
  /** 供应商类别 */
  vendorType?: string;
  /** 供应商类别名称 */
  vendorTypeName?: string;
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/47/interface/api/15905
 */
export function modVendorBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/vendors/${req.id /* 供应商id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}
/**
 * @description 线路供应商
 */
/**
 * 线路供应商
 * LINE_VENDOR
 * @description 根据安全容器id分页查询线路供应商
 * @url http://*************:3000/project/47/interface/api/19930
 */
export function getVendorLineBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/vendors/line/${req.containerId /* 安全容器id */}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<VendorItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 设备供应商
 */
/**
 * 设备供应商
 * DEVICE_VENDOR
 * @description 根据安全容器id分页查询设备供应商
 * @url http://*************:3000/project/47/interface/api/19937
 */
export function getVendorDeviceBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/vendors/device/${req.containerId /* 安全容器id */}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<VendorItem[]>>($req)),
    { controller }
  );
}

/**
 * @description 行动策略
 */
export interface SupportNotesItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 所属租户, 全局行动策略租户ID为 -999 */
  tenantId: /* Integer */ string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** 名称 */
  name: string;
  /** 描述信息 */
  description?: string;
  /** 生效配置 */
  activeConfig?: {
    /** 是否使用自动时区 */
    useAutoTimeZone: boolean;
    /** 时区ID, 非自动时区必填 */
    timeZone?: string;
    /** 生效时间段 */
    activeHours: {
      /** 星期标识，用于排序(星期一传1，星期二传2，以此类推) */
      weekDay: /* Integer */ string;
      /** 生效时间 */
      hours: /* Integer */ string[];
    }[];
  };
  /** Notes for Active Hours */
  activeNote?: string;
  /** Notes for Inactive Hours */
  inactiveNote?: string;
  /** 是否激活 */
  active: boolean;
  /** 是否默认策略 */
  defaultNote: boolean;
  /** 当前相应策略绑定设备id列表 */
  resourceIds?: /* Integer */ string[];
  /** 当前相应策略绑定区域id列表 */
  regionIds?: /* Integer */ string[];
  /** 当前相应策略绑定地点id列表 */
  locationIds?: /* Integer */ string[];
  /** 获取当前时间下的行动策略的使用时间 */
  activeTime?: /* Integer */ string;
  /** 是否全局行动策略 */
  global?: boolean;
  /** 分配的租户列表 */
  assignedTenantIds?: /* Integer */ string[];
  /** 版本号 */
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime: /* Integer */ string;
  /** 最近更新时间 */
  updatedTime: /* Integer */ string;
  /** 创建人 */
  createdBy?: string;
  /** 最后更新人 */
  updatedBy?: string;
}
/**
 * 行动策略
 * SUPPORT_NOTE
 * @description 根据安全容器id分页查询行动策略
 * @url http://*************:3000/project/47/interface/api/19993
 */
export function getSupportNoteBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/support_notes/${req.containerId /* 安全容器id */}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<SupportNotesItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/47/interface/api/15912
 */
export function modSupportNoteBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.CMDB}/support_notes/${req.id /* 行动策略id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

/* TODO: ================================================ EVENT_CENTRT =============================================== */
// 优先级矩阵 */
//   PRIORITY_MATRIX

/**
 * @description 根据安全容器id分页查询优先级矩阵-响应体
 * @url http://*************:3000/project/17/interface/api/19972
 */
export interface PriorityMatrixItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户id */
  tenantId: /* Integer */ string;
  /** 名称 */
  name: string;
  /** 描述 */
  desc: string;
  /** 是否默认 */
  defaultMatrix: boolean;
  /** 安全容器ID */
  containerId: /* Integer */ string;
  /** 优先级矩阵  「已废弃」使用 {@link PriorityMatrixVO#priorityMatrixItems} 「已废弃」 */
  items?: {
    /** 事件紧急性 */
    eventSeverity?: /* 枚举: Critical :Critical | Major :Major | Minor :Minor | Warning :Warning | Unknown :Unknown | Normal :Normal | Informational :Informational | Calculating :Calculating | Symptom :Symptom | Monitoring :Monitoring | Others :在映射中展示Critical -- Monitoring, */ "Critical" | "Major" | "Minor" | "Warning" | "Unknown" | "Normal" | "Informational" | "Calculating" | "Symptom" | "Monitoring" | "Others";
    /** 设备重要性 */
    deviceImportance?: /* 枚举: High :高 | Medium :中 | Low :低 | None :无 | Unknown :未知 */ "High" | "Medium" | "Low" | "None" | "Unknown";
    /** 优先级 */
    priority?: /* 枚举: P1 :P1~P7 优先级递减 | P2 :P2 | P3 :P3 | P4 :P4 | P5 :P5 | P6 :P6 | P7 :P7 | P8 :P8 手动 */ "P1" | "P2" | "P3" | "P4" | "P5" | "P6" | "P7" | "P8";
  }[];
  /** 设备重要性和工单影响的映射项 */
  deviceOrderMappingItems?: {
    /** 设备重要性 */
    importance?: /* 枚举: High :高 | Medium :中 | Low :低 | None :无 | Unknown :未知 */ "High" | "Medium" | "Low" | "None" | "Unknown";
    /** 工单的影响 */
    influence?: /* 枚举: High :High | Medium :Medium | Low :Low | None :None | Unknown :Unknown */ "High" | "Medium" | "Low" | "None" | "Unknown";
  }[];
  /** 告警严重性和工单紧急性的映射 */
  alertOrderMappingItems?: {
    /** 告警严重性 */
    severity?: /* 枚举: Critical :Critical | Major :Major | Minor :Minor | Warning :Warning | Unknown :Unknown | Normal :Normal | Informational :Informational | Calculating :Calculating | Symptom :Symptom | Monitoring :Monitoring | Others :在映射中展示Critical -- Monitoring, */ "Critical" | "Major" | "Minor" | "Warning" | "Unknown" | "Normal" | "Informational" | "Calculating" | "Symptom" | "Monitoring" | "Others";
    /** 工单紧急性 */
    urgency?: /* 枚举: High :High | Medium :Medium | Low :Low | None :None | Unknown :Unknown */ "High" | "Medium" | "Low" | "None" | "Unknown";
  }[];
  /** 优先级矩阵 */
  priorityMatrixItems?: {
    /** 工单影响性 */
    influence?: /* 枚举: High :High | Medium :Medium | Low :Low | None :None | Unknown :Unknown */ "High" | "Medium" | "Low" | "None" | "Unknown";
    /** 工单紧急性 */
    urgency?: /* 枚举: High :High | Medium :Medium | Low :Low | None :None | Unknown :Unknown */ "High" | "Medium" | "Low" | "None" | "Unknown";
    /** 优先级 */
    priority?: /* 枚举: P1 :P1~P7 优先级递减 | P2 :P2 | P3 :P3 | P4 :P4 | P5 :P5 | P6 :P6 | P7 :P7 | P8 :P8 手动 */ "P1" | "P2" | "P3" | "P4" | "P5" | "P6" | "P7" | "P8";
  }[];
  /** 优先级矩阵+告警严重性和工单紧急性的映射 */
  priorityMatrixItemsWithAlertOrderMapping?: {
    /** 告警严重性和工单紧急性的映射 */
    alertOrderMappingItems?: {
      /** 告警严重性 */
      severity?: /* 枚举: Critical :Critical | Major :Major | Minor :Minor | Warning :Warning | Unknown :Unknown | Normal :Normal | Informational :Informational | Calculating :Calculating | Symptom :Symptom | Monitoring :Monitoring | Others :在映射中展示Critical -- Monitoring, */ "Critical" | "Major" | "Minor" | "Warning" | "Unknown" | "Normal" | "Informational" | "Calculating" | "Symptom" | "Monitoring" | "Others";
      /** 工单紧急性 */
      urgency?: /* 枚举: High :High | Medium :Medium | Low :Low | None :None | Unknown :Unknown */ "High" | "Medium" | "Low" | "None" | "Unknown";
    }[];
    /** 优先级矩阵 */
    priorityMatrixItems?: {
      /** 工单影响性 */
      influence?: /* 枚举: High :High | Medium :Medium | Low :Low | None :None | Unknown :Unknown */ "High" | "Medium" | "Low" | "None" | "Unknown";
      /** 工单紧急性 */
      urgency?: /* 枚举: High :High | Medium :Medium | Low :Low | None :None | Unknown :Unknown */ "High" | "Medium" | "Low" | "None" | "Unknown";
      /** 优先级 */
      priority?: /* 枚举: P1 :P1~P7 优先级递减 | P2 :P2 | P3 :P3 | P4 :P4 | P5 :P5 | P6 :P6 | P7 :P7 | P8 :P8 手动 */ "P1" | "P2" | "P3" | "P4" | "P5" | "P6" | "P7" | "P8";
    }[];
  };
  /** 创建人信息 */
  createdBy?: string;
  /** 更新人信息 */
  updatedBy?: string;
  /** 创建时间 */
  createdTime?: /* Integer */ string;
  /** 更新时间 */
  updatedTime?: /* Integer */ string;
}
/**
 * 优先级矩阵
 * PRIORITY_MATRIX
 * @description 根据安全容器id分页查询优先级矩阵
 * @url http://*************:3000/project/17/interface/api/19972
 */
export function getPriorityMatrixBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/priority_matrix/${req.containerId /* 安全容器id */}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<PriorityMatrixItem[]>>($req)),
    { controller }
  );
}

export function modPriorityMatrixBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/priority_matrix/${req.id /* 模版id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = { "Content-Type": "application/x-www-form-urlencoded" };
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

// 关闭代码 */
//   CODE_CONFIG

/**
 * @description 关闭代码
 */
export interface CodeConfigItem {
  /** 拥有的权限 */
  hasPermissionIds: /* Integer */ string[];
  /** ID */
  id: string;
  /** 关闭代码名称 */
  codeName: string;
  name: string;
  /** 关闭代码描述 */
  codeDesc?: string;
  /** 是否默认 */
  defaultable: boolean;
  /** 安全容器ID */
  containerId: string;
  /** 创建时间 */
  createTime: /* Integer */ string;
}
/**
 * @description 根据安全容器ID分页查询完结代码，用于安全容器配置项展示
 * @url http://*************:3000/project/17/interface/api/25063
 */
export function getCodeConfigBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/codeConfig/${req.containerId}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<CodeConfigItem[]>>($req).then((res) => ({ ...res, data: (res.data instanceof Array ? res.data : []).map((v) => Object.assign(v, { id: v.id, name: v.codeName })) }))),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/17/interface/api/21225
 */
export function modCodeConfigBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/codeConfig/${req.id /* 模版id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.headers = { "Content-Type": "application/x-www-form-urlencoded" };
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

// SLA */
//   SLA
/**
 * @description SLA规则
 */
export interface SlaRuleItem {
  /** SLA规则ID */
  ruleId: string;
  id: string;
  /** 安全容器id */
  containerId: /* Integer */ string;
  /** SLA规则名称 */
  ruleName: string;
  name: string;
  /** SLA规则描述 */
  ruleDesc?: string;
  /** 创建时间 */
  createTime: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 是否为全局配置 */
  globalEnable: boolean;
}
/**
 * SLA
 * SLA
 * @description 根据安全容器id分页获取SLA规则
 * @url http://*************:3000/project/17/interface/api/19965
 */
export function getSlaRuleBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/sla/rule/${req.containerId /* 安全容器id */}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<SlaRuleItem[]>>($req).then((res) => ({ ...res, data: (res.data instanceof Array ? res.data : []).map((v) => Object.assign(v, { id: v.ruleId, name: v.ruleName })) }))),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/17/interface/api/16388
 */
export function modSlaRuleBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/sla/rule/${req.id /* SLA ID */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器ID */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

// 短信模版 */
//   SMS_TEMPLATE

/**
 * @description 短信模版
 */
export interface MessageTemplateItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 租户id */
  tenantId: /* Integer */ string;
  /** 模板名称 */
  name: string;
  /** 模板描述 */
  desc: string;
  active: boolean;
  /** 模版是否已被关联 */
  strategyIds?: /* Integer */ string[];
  /** 安全容器ID */
  containerId: /* Integer */ string;
}
/**
 * 短信模版
 * SMS_TEMPLATE
 * @description 根据安全容器ID分页查询短信模版，用于安全容器配置项展示
 * @url http://*************:3000/project/17/interface/api/24955
 */
export function getMessageTemplateBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/message_template/${req.containerId}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<MessageTemplateItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/17/interface/api/20476
 */
export function modMessageTemplateBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/message_template/${req.id /* 模版id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

// 短信发送模版 */
//   SMS_SEND_TEMPLATE

/**
 * @description 短信发送模版
 */
export interface MessageStrategyItem {
  /** id */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 策略名称 */
  name: string;
  /** 策略描述 */
  desc?: string;
  /** 关联短信模版ID */
  templateId?: /* Integer */ string;
  /** 关联触发条件模版ID */
  triggerId?: /* Integer */ string;
  /** 是否默认 */
  defaultable: boolean;
  /** 是否激活 */
  active: boolean;
  /** 安全容器ID */
  containerId: /* Integer */ string;
  /** 触发模版名称 */
  triggerTemplateName?: string;
  /** 短信模版名称 */
  templateName?: string;
  /** 分配给的客户ID */
  customerIds?: /* Integer */ string[];
  /** 分配给的区域ID */
  regionIds?: /* Integer */ string[];
  /** 分配给的场所ID */
  locationIds?: /* Integer */ string[];
  /** 分配给的设备ID */
  deviceIds?: /* Integer */ string[];
  /** 收件人类型 */
  recipientTypes?: /* 枚举: Notification :通知联系人 | Technical :技术联系人 | OnSite :现场联系人 */ ("Notification" | "Technical" | "OnSite")[];
  /** 指定联系人(输入手机号，多个用分号隔开) */
  recipients?: string;
  /** 发送给的用户组ID列表 */
  userGroupIds?: /* Integer */ string[];
  /** 发送给的用户ID列表 */
  userIds?: /* Integer */ string[];
}

/**
 * @description 根据安全容器ID分页查询，用于安全容器配置项展示
 * @url http://*************:3000/project/17/interface/api/24949
 */
export function getMessageStrategyBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/message_strategy/${req.containerId}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<MessageStrategyItem[]>>($req)),
    { controller }
  );
}

/**
 * @description 移动安全容器
 * @url http://*************:3000/project/17/interface/api/24973
 */
export function modMessageStrategyBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/message_strategy/${req.id /* 模版id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

// 邮件模版 */
//   EMAIL_TEMPLATE

/**
 * @description 邮件模版
 */
export interface EmailTemplateItem {
  id: /* Integer */ string;
  /** 租户id */
  tenantId: /* Integer */ string;
  /** 安全目录id */
  containerId: /* Integer */ string;
  /** 已经关联的发送策略ID集合 */
  strategyIds?: /* Integer */ string[];
  /** 模板名称 */
  name: string;
  /** 模板描述 */
  desc?: string;
  active: boolean;
  /** 收件人 */
  recipients?: /* 枚举: NOTIFY_CONTACT :通知联系人 | FIELD_CONTACT :现场联系人 | TECHNICAL_CONTACT :技术联系人 */ ("NOTIFY_CONTACT" | "FIELD_CONTACT" | "TECHNICAL_CONTACT")[];
  /** 抄送人 */
  carbonCopies?: string[];
  /** 抄送人(输入框) */
  cc?: string;
  /** 创建人信息 */
  createdBy?: string;
  /** 更新人信息 */
  updatedBy?: string;
  /** 创建时间 */
  createdTime?: /* Integer */ string;
  /** 更新时间 */
  updatedTime?: /* Integer */ string;
}
/**
 * 邮件模版
 * EMAIL_TEMPLATE
 * @description 根据安全容器ID分页查询邮件模版，用于安全容器配置项展示
 * @url http://*************:3000/project/17/interface/api/24943
 */
export function getEmailTemplateBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/email_template/${req.containerId}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<EmailTemplateItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/17/interface/api/20490
 */
export function modEmailTemplateBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/email_template/${req.id /* 模版id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

// 邮件发送模版 */
//   EMAIL_SEND_TEMPLATE
/**
 * @description 邮件发送模版
 */
export interface EmailStrategyItem {
  /** id */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 策略名称 */
  name: string;
  /** 策略描述 */
  desc?: string;
  /** 关联短信模版ID */
  templateId?: /* Integer */ string;
  /** 关联触发条件模版ID */
  triggerId?: /* Integer */ string;
  /** 是否默认 */
  defaultable: boolean;
  /** 是否激活 */
  active: boolean;
  /** 安全容器ID */
  containerId: string;
  /** 触发模版名称 */
  triggerTemplateName?: string;
  /** 邮件模版名称 */
  templateName?: string;
  /** 分配给的客户ID */
  customerIds?: /* Integer */ string[];
  /** 分配给的区域ID */
  regionIds?: /* Integer */ string[];
  /** 分配给的场所ID */
  locationIds?: /* Integer */ string[];
  /** 分配给的设备ID */
  deviceIds?: /* Integer */ string[];
  /** 收件人类型 */
  recipientTypes?: /* 枚举: Notification :通知联系人 | Technical :技术联系人 | OnSite :现场联系人 */ ("Notification" | "Technical" | "OnSite")[];
  /** 指定联系人(输入手机号，多个用分号隔开) */
  recipients?: string;
  /** 抄送人类型 */
  ccRecipientTypes?: string[];
  /** 指定抄送人(输入邮箱号，多个用分号隔开) */
  ccRecipients?: string;
  /** 发送给的用户组ID列表 */
  userGroupIds?: /* Integer */ string[];
  /** 发送给的用户ID列表 */
  userIds?: /* Integer */ string[];
}
/**
 * 邮件发送模版
 * EMAIL_SEND_TEMPLATE
 * @description 根据安全容器ID分页查询邮件模板，用于安全容器配置项展示
 * @url http://*************:3000/project/17/interface/api/24937
 */
export function getEmailStrategyBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/email_strategy/${req.containerId}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<EmailStrategyItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/17/interface/api/24967
 */
export function modEmailStrategyBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/email_strategy/${req.id /* 模版id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

// 触发条件模版 */
//   TRIGGER_CONDITION_TEMPLATE

/**
 * @description 触发条件模版
 */
export interface TriggerConditionItem {
  /** id */
  id: /* Integer */ string;
  /** 租户ID */
  tenantId: /* Integer */ string;
  /** 模版名称 */
  name?: string;
  /** 模版描述 */
  desc?: string;
  /** 是否激活 */
  active: boolean;
  /** 安全容器ID */
  containerId: /* Integer */ string;
  /** 模版是否已被关联 */
  strategyIds?: /* Integer */ string[];
}

/**
 * @description 根据安全容器ID分页查询，用于安全容器配置项展示
 * @url http://*************:3000/project/17/interface/api/24961
 */
export function getTriggerConditionBySecurityContainer(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/trigger_condition/${req.containerId}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<TriggerConditionItem[]>>($req)),
    { controller }
  );
}

/**
 * @description 移动安全容器
 * @url http://*************:3000/project/17/interface/api/24979
 */
export function modTriggerConditionBySecurityContainer(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/trigger_condition/${req.id /* 模版id */}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器id */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

export interface ServiceCatalogItem {
  /** 主键 */
  id: /* Integer */ string;
  version: /* Integer */ string;
  /** 创建时间 */
  createdTime?: /* Integer */ string;
  /** 更新时间 */
  updatedTime?: /* Integer */ string;
  /** 创建人信息 */
  createdBy?: string;
  /** 更新人信息 */
  updatedBy?: string;
  /** 租户id */
  tenantId: /* Integer */ string;
  /** 服务目录名称 */
  name?: string;
  /** 服务目录描述 */
  description?: string;
  /** 安全容器id */
  containerId: /* Integer */ string;
}

/**
 * 服务目录
 * TENANT
 * @description 获取安全容器下服务目录列表
 * @url http://*************:3000/project/17/interface/api/39106
 */
export function getTriggerConditionByServiceCatalog(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/serviceCatalog/query/${req.containerId}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<ServiceCatalogItem[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/17/interface/api/39110
 */
export function modTriggerConditionByServiceCatalog(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/serviceCatalog/query/${req.id}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器ID */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

export interface CryptowWalletItem {
  /** 主键 */
  id: /* Integer */ string;
  /** 所属租户 */
  tenantId: /* Integer */ string;
  /** 安全容器id */
  containerIds: /* Integer */ string[];
  /** 名称 */
  name: string;
  /** 描述信息 */
  description?: string;
  /** 可读权限时间(分钟) */
  readTime: /* Integer */ string;
  /** 设置权限时间(分钟) */
  setTime: /* Integer */ string;
  /** 是否激活(状态：true-启用，false-禁用) */
  enable: boolean;
}

/**
 * 密码钱包
 * TENANT
 * @description 获取安全容器下密码钱包列表
 * @url http://*************:3000/project/17/interface/api/39118
 */
export function getTriggerConditionByCryptowWallets(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/cryptow_wallets/${req.containerId}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<any[]>>($req)),
    { controller }
  );
}
/**
 * @description 移动安全容器
 * @url http://*************:3000/project/17/interface/api/39110
 */
export function modTriggerConditionByCryptowWallets(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/cryptow_wallets/${req.id}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器ID */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

/**
 * 项目管理
 * TENANT
 * @description 获取安全容器下项目管理列表
 * @url http://*************:3000/project/17/interface/api/41462
 */
export function getTriggerConditionByProjectManagement(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/dict_event/prod/${req.containerId}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<any[]>>($req)),
    { controller }
  );
}

export function modTriggerConditionByProjectManagement(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/dict_event/prod/${req.id}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器ID */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

export function getTriggerConditionByTicketGroup(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/ticket_group/${req.containerId}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<any[]>>($req)),
    { controller }
  );
}

export function modTriggerConditionByTicketGroup(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/ticket_group/${req.id}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器ID */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}

/**
 * 工单模版
 * TENANT
 * @description 获取安全容器下工单模版列表
 * @url
 */
export function getTriggerConditionByTicketTemplate(req: ResourceDataBaseQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/ticket_templates/${req.containerId}/tenant/current`, method: Method.Get, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ pageNumber: req.paging.pageNumber, pageSize: req.paging.pageSize, sort: req.sort }, $req.params);
        $req.data = void 0;
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<any[]>>($req)),
    { controller }
  );
}

export function modTriggerConditionByTicketTemplate(req: ResourceDataMoveQuery) {
  const controller = new AbortController();
  return Object.assign(
    Promise.resolve<import("axios").AxiosRequestConfig>({ url: `${SERVER.EVENT_CENTER}/ticket_templates/${req.id}/move_container`, method: Method.Patch, responseType: "json", signal: controller.signal })
      .then(async ($req) => {
        $req.params = new URLSearchParams();
        bindParamByObj({ containerId: req.containerId /* 安全容器ID */, oldContainerName: req.oldContainerName /* 旧的安全容器名称 */, newContainerName: req.newContainerName /* 新的安全容器名称 */ }, $req.params);
        $req.data = new URLSearchParams();
        return $req;
      })
      .then(($req) => request<never, ResourceDataBaseResponse<null>>($req)),
    { controller }
  );
}
