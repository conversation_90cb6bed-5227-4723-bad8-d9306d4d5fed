<template>
  <!-- 对话框表单 -->
  <component :is="ComponentRender" v-model:visible="data.visible" :handle-cancel="handleCancel">
    <template #header>
      {{ `${$params.id && data.disabled ? t("glob.Cat") : $params.id ? t("glob.edit") : t("glob.add")} ${props.title}` }}
    </template>
    <template #default="{ width }">
      <!-- width -->
      <FormModel ref="formRef" :disabled="data.disabled" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @submit="handleFinish">
        <FormItem :span="width > 600 ? 12 : 24" :label="t('userManagement.Full name')" tooltip="" prop="name" :rules="[buildValidatorData({ name: 'required', title: t('userManagement.PleaseEnterName') })]">
          <el-input v-model="form.name" :placeholder="t('userManagement.PleaseEnterName')" />
        </FormItem>
        <!-- <FormItem :span="width > 600 ? 12 : 24" :label="`昵称`" tooltip="" prop="nickname" :rules="[{ required: false, message: t('glob.Please input field', { field: `${props.title}昵称` }), trigger: 'blur' }]">
          <el-input v-model="form.nickname" :disabled="!!$params.id" :placeholder="`请输入${props.title}昵称`" />
        </FormItem> -->
        <FormItem :span="width > 600 ? 12 : 24" :label="t('userManagement.Username')" :tooltip="t('userManagement.Username login ID start with uppercase and lowercase letters, including 1 to 64 digits of uppercase and lowercase letters, numbers, underscores, question marks, exclamation marks, and pound signs')" prop="account" :rules="[...(!form.email ? [buildValidatorData({ name: 'required', title: t('userManagement.PleaseEnterUsername') })] : []), buildValidatorData({ name: 'account', title: t('userManagement.The account starts with a letter and contains 1 to 64 uppercase and lowercase letters, numbers, and _#') })]">
          <el-input v-model="form.account" :placeholder="t('userManagement.PleaseEnterUsername')">
            <template #append>@{{ $params.accountSuffix || (userInfo.currentTenant || {}).abbreviation }}</template>
          </el-input>
        </FormItem>
        <!-- :rules="[...(false ? [buildValidatorData({ name: 'required', title: `${props.title}手机号` })] : []), buildValidatorData({ name: 'mobile', title: `${props.title}手机号` })]" -->
        <FormItem style="margin-top: 20px" :span="width > 600 ? 12 : 24" :label="t('userManagement.Mobile')" tooltip="" prop="phone" :rules="[{ validator: handleCheckPhone, trigger: ['blur', 'change'] }]">
          <el-input v-model="form.phone" :placeholder="!data.disabled ? t('userManagement.PleaseEnterMobilePhoneNumber') : ''" />
        </FormItem>
        <FormItem style="margin-top: 20px" :span="width > 600 ? 12 : 24" :label="t('userManagement.Email')" tooltip="" prop="email">
          <el-input v-model="form.email" :placeholder="!data.disabled ? t('userManagement.PleaseEnterEmail') : ''" />
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="t('userManagement.TimeZone')" tooltip="" prop="zoneId" :rules="[buildValidatorData({ name: 'required', title: t('userManagement.TimeZone') })]">
          <el-select v-model="form.zoneId" filterable :placeholder="!data.disabled ? t('userManagement.PleaseChoose') : ''" class="tw-w-full">
            <el-option v-for="item in timeZone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"> </el-option>
          </el-select>
        </FormItem>
        <!-- <FormItem v-if="!$params.id" :span="width > 600 ? 12 : 24" :label="`登录密码`" tooltip="" prop="password" :rules="[...(form.account ? [buildValidatorData({ name: 'required', title: `${props.title}密码` })] : []), buildValidatorData({ name: 'password', title: `${props.title}密码`, min: pwdMinLength })]">
          <el-input v-model="form.password" type="password" :disabled="!!$params.id" :placeholder="`请输入${props.title}密码`" show-password />
        </FormItem> -->

        <FormItem :span="width > 600 ? 12 : 24" :label="t('userManagement.UserGroup')" tooltip="" prop="zoneId" :rules="[]">
          <el-select :disabled="!userInfo.hasPermission('669806141328330752')" v-model="form.userGroupIds" class="tw-w-full" multiple :placeholder="!data.disabled ? t('userManagement.PleaseChoose') : ''">
            <el-option v-for="item in userGroupOption" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="t('userManagement.NotificationLanguage')" tooltip="" prop="language" :rules="[buildValidatorData({ name: 'required', title: t('userManagement.PleaseChooseNotificationLanguage') })]">
          <el-select v-model="form.language" class="tw-w-full" :placeholder="t('userManagement.PleaseChooseNotificationLanguage')" filterable clearable>
            <el-option v-for="item in localesOption" :key="`locale-${item.value}`" :label="item.label" :value="item.value">
              <div :style="{ background: `url(${item.icon}) no-repeat left / auto calc(100% - 12px)`, paddingLeft: '30px' }">{{ item.label }}</div>
            </el-option>
          </el-select>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="t('userManagement.Password')" tooltip="" prop="password" :rules="[...(form.account ? [buildValidatorData({ name: (!$params.id ? 'required' : '') as any, title: t('userManagement.User password cannot be empty') })] : []), buildValidatorData({ name: 'password', title: t('userManagement.User password cannot be empty'), min: pwdMinLength })]">
          <!-- <el-input v-model="form.password" type="password" :disabled="!!$params.id" :placeholder="`请输入${props.title}密码`" show-password /> -->

          <el-button style="margin-top: -8px" @click="dialogVisible = true">{{ t("userManagement.Set Password") }}</el-button>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="`${t('userManagement.Account Expiration Date')}`" tooltip="" prop="expirationDate" :rules="[{ required: false, message: `${t('userManagement.Please select the account expiration date')}` }]">
          <el-date-picker
            class="tw-w-full"
            :model-value="form.expirationDate"
            @update:model-value="
              ($event: any) => {
                if ($event) {
                  const dateValue = new Date(Number($event)) as Date;
                  dateValue.setHours(23);
                  dateValue.setMinutes(59);
                  dateValue.setSeconds(59);
                  form.expirationDate = dateValue.getTime() as any;
                } else form.expirationDate = '' as any;
              }
            "
            type="date"
            value-format="x"
            :default-time="new Date(2024, 0, 1, 23, 59, 59)"
            :placeholder="t('userManagement.Please select the account expiration date')"
            :disabledDate="handleDisabledBeforeToday"
          />
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="`${t('userManagement.Active')}`" tooltip="" prop="" :rules="[]">
          <el-checkbox v-model="form.blocked" :label="t('userManagement.Active')" true-value="false" false-value="true" />
          <!-- <el-date-picker class="tw-w-full" v-model="form.expirationDate" type="date" value-format="x" placeholder="请选择账号有效期" /> -->
        </FormItem>
        <!-- <FormItem v-if="!$params.id" :span="width > 600 ? 12 : 24" :label="``" tooltip="" prop="changePwdDuringLogin">
          <el-checkbox v-model="form.changePwdDuringLogin" label="登录时是否需要修改密码" size="large" />
        </FormItem> -->
      </FormModel>
    </template>
    <template #footer>
      <!-- auth599449659865300992: /* 保存用户密码策略 */ "599449659865300992",
    auth599449835422089216: /* 保存客户密码策略 */ "599449835422089216", -->
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </component>
  <el-dialog v-model="dialogVisible" :title="t('userManagement.Set Password')" width="500" :before-close="handleClose" append-to-body>
    <FormModel ref="formPasswordRef" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @submit="handleFinish">
      <FormItem :span="24" :label="t('userManagement.Password')" tooltip="" prop="password" :rules="getPasswordRules">
        <el-input v-model="form.password" type="password" :placeholder="t('userManagement.PleaseEnterUserPassword')" show-password />
      </FormItem>
      <FormItem :span="24" :label="t('userManagement.Confirm Password')" style="margin-top: 25px" prop="rePassword" :rules="[buildValidatorData({ name: 'required', title: t('userManagement.Confirm Password') }), { validator: (_rule, value, callback) => callback(value === form.password ? undefined : t('userManagement.Two passwords need to be consistent')), trigger: 'blur' }]">
        <el-input v-model="form.rePassword" type="password" show-password autocomplete="new-password" :placeholder="t('userManagement.Confirm Password')" clearable></el-input>
      </FormItem>

      <FormItem :span="width > 600 ? 12 : 24" :label="``" tooltip="" prop="changePwdDuringLogin">
        <el-checkbox v-model="form.changePwdDuringLogin" :label="t('userManagement.Password needs to be changed when logging in')" size="large" />
      </FormItem>
    </FormModel>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="confrimPwd('cancel')">{{ t("glob.NO") }}</el-button>
        <el-button type="primary" @click="confrimPwd('confrim')"> {{ t("glob.OK") }} </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, computed, h, renderSlot, getCurrentInstance, createVNode, toRaw } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { buildTypeHelper } from "@/utils/type";
import { useConfig } from "@/stores/config";
import moment from "moment";
import _timeZone from "@/views/pages/common/contactsZone.json";

import { buildValidatorData } from "@/utils/validate";

import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import { getTenantInfo } from "@/views/pages/apis/tenant";

import getUserInfo from "@/utils/getUserInfo";

import { getTenantPasswordStrategyMsg, getAppointUserPasswordStrategyMsg, getTenantPasswordStrategy, getUserPasswordStrategy } from "@/api/personnel";

import { locales, localesOption } from "@/api/locale";
import { gender, genderOption, getGroup } from "@/api/personnel";
import { getUserSensitiveById } from "@/api/personnel";
const dialogVisible = ref(false);
const handleClose = (done: () => void) => {
  done();
};

const userInfo = getUserInfo();
const timeZone = ref(_timeZone);
const config = useConfig();
const { t } = useI18n({ useScope: "global" });
const formRef = ref<InstanceType<typeof FormModel>>();
const formPasswordRef = ref<InstanceType<typeof FormModel>>();
const ctx = getCurrentInstance();
if (!ctx) throw new Error("Component context initialization failed!");
const { appContext } = ctx;

interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 100,
});

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Item {
  name: string;
  // nickname: string;
  account: string;
  phone: string;
  email: string;
  language: locales;
  gender: gender;
  password: string;
  zoneId: string;
  changePwdDuringLogin: boolean;
  rePassword: string;
  userGroupIds: string[];
  expirationDate: number | string | null;
  blocked: string;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  name: buildTypeHelper<Required<Item>["name"]>(""),
  // nickname: buildTypeHelper<Required<Item>["nickname"]>(""),
  account: buildTypeHelper<Required<Item>["account"]>(""),
  phone: buildTypeHelper<Required<Item>["phone"]>(""),
  email: buildTypeHelper<Required<Item>["email"]>(""),
  language: buildTypeHelper<Required<Item>["language"]>(locales["zh-CN"]),
  gender: buildTypeHelper<Required<Item>["gender"]>(gender.SECRET),
  password: buildTypeHelper<Required<Item>["password"]>(""),
  zoneId: buildTypeHelper<Required<Item>["zoneId"]>(""),
  changePwdDuringLogin: buildTypeHelper<Required<Item>["changePwdDuringLogin"]>(false),
  rePassword: buildTypeHelper<Required<Item>["rePassword"]>(""),
  userGroupIds: buildTypeHelper<Required<Item>["userGroupIds"]>([]),
  expirationDate: buildTypeHelper<Required<Item>["expirationDate"]>(""),
  blocked: buildTypeHelper<Required<Item>["blocked"]>("true"),
});
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  if (params.id) {
    const { success, data: res, message } = await getUserSensitiveById({ id: params.id as string, dataMasking: data.disabled || false });
    if (!success) throw Object.assign(new Error(message), { success, res });
    if (userInfo.hasPermission("512890964822458368")) {
      Object.assign(params, {
        ...res,
        userGroupIds: res.groups instanceof Array ? res.groups.map((group) => group.id) : [],
      });
    }
    // Object.assign(params, { ...data, userGroupIds: data.groups instanceof Array ? data.groups.map((group) => group.id) : [] });
  }
  if (!params.id) {
    (async (req) => {
      const { success, message, data } = await getTenantInfo(req);
      if (!success) throw Object.assign(new Error(message), { success, data });

      form.value.zoneId = data.zoneId || "China Standard Time";
      form.value.language = data.language as locales;
    })({});
  }
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  await nextTick();
  form.value.blocked = params.blocked ? "true" : "false";
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  return form;
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 窗口方法
 */
interface StateData<T> {
  loading: boolean;
  disabled: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<Item>>({
  loading: false,
  disabled: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  disabled: boolean;
  submitLoading: boolean;
  resolve?: (value: Record<string, unknown>) => void;
  reject?: (value: Error) => void;
  callback?: (form: Record<string, unknown>) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  disabled: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Record<string, unknown>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {}) as Item);

const userGroupOption = ref<Record<"name" | "code", string>[]>([]);
// 获取组织下所有用户组
async function getGroupList() {
  await (async () => {
    const { success, message, data } = await getGroup({});
    if (!success) throw Object.assign(new Error(message), { success, data });
    userGroupOption.value = userInfo.hasPermission("512890964822458368") ? data : [];
  })();
}
async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(function (formResult, [key, util]) {
    if (!util) return formResult;
    else if (util.test(formResult[key])) return formResult;
    else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], toRaw(util.value as never))) });
  }, form as Required<Item>);
}

async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => (formRef.value ? formRef.value.validate(resolve) : resolve(false)));
  } catch (error) {
    return false;
  }
}

function handleCheckPhone(rule, value, callback) {
  const regex = /^(?:\+86)?\s?1[3-9]\d{9}$/;
  return !form.value.phone || regex.test(form.value.phone) ? callback() : callback(t("contact.Invalid phone"));
}

function handleDisabledBeforeToday(date) {
  // 禁用今天之前的日期
  return date < new Date(new Date().setHours(0, 0, 0, 0));
}

/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
    if (!$params.value.id) await handleGetTenantPasswordStrategy();
    else {
      form.value.expirationDate = form.value.expirationDate ? Number(form.value.expirationDate) : "";
      // handleGetUserPasswordStrategy();
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();
const pwdMinLength = ref(0);
const pwdExpire = ref(false);
function getPwdMinLength() {
  getTenantPasswordStrategyMsg({}).then((res) => {
    if (res.success) {
      pwdMinLength.value = res.data.minLength as any;
      pwdExpire.value = res.data.passwordExpire as any;
    }
  });
}

const isShowExpirationData = ref<boolean>(true);

// async function handleGetUserPasswordStrategy() {
//   const { data, message, success } = await getUserPasswordStrategy({ userId: $params.value.id });
//   if (!success) throw new Error(message);
//   isShowExpirationData.value = [data.accountExpirationDateConfigType].includes("CUSTOM");
// }

async function handleGetTenantPasswordStrategy() {
  const { data, message, success } = await getTenantPasswordStrategy({});
  if (!success) throw new Error(message);
  isShowExpirationData.value = ["CUSTOM"].includes(data.accountExpirationDateConfigType);

  if (isShowExpirationData.value && data.customAccountExpirationDate) {
    const date = new Date() as Date;
    date.setHours(23);
    date.setMinutes(59);
    date.setSeconds(59);
    form.value.expirationDate = date.getTime() + 1000 * 60 * 60 * 24 * Number(data.customAccountExpirationDate);
  }
}
const getPasswordRules = computed(() => {
  if (pwdExpire.value != true) {
    return [buildValidatorData({ name: "required", title: `${props.title}密码` })];
  } else {
    return [buildValidatorData({ name: "required", title: `${props.title}密码` }), buildValidatorData({ name: "password", title: `${props.title}密码`, min: pwdMinLength.value })];
  }
});

async function confrimPwd(type) {
  if (type === "confrim") {
    if (formPasswordRef.value) {
      formPasswordRef.value.validate(async (valid) => {
        if (valid) {
          dialogVisible.value = false;
          formPasswordRef.value && formPasswordRef.value.clearValidate();
        }
      });
    }
  } else {
    dialogVisible.value = false;
    formPasswordRef.value && formPasswordRef.value.clearValidate();
  }
}

defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, callback?: (form: Record<string, unknown>) => Promise<void>): Promise<unknown> {
    if (data.visible) handleCancel();
    const wait = new Promise<Record<string, unknown>>((resolve, reject) => ((data.resolve = resolve), (data.reject = reject)));
    $params.value = cloneDeep(params);
    data.visible = true;
    data.loading = true;
    data.disabled = params.watch ? true : false;
    data.submitLoading = true;
    data.callback = callback;
    getPwdMinLength();
    await getGroupList();
    await nextTick();
    try {
      await runningInit($params.value);
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      handleCancel();
    }
    handleReset();
    data.loading = false;
    data.submitLoading = false;

    try {
      return await wait;
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async confirm<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.confirm(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async alert<T extends { $title: string; $type: "" | "success" | "warning" | "info" | "error"; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.alert(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
});
</script>

<style scoped lang="scss">
/* 添加标签防换行样式 */
:deep(.el-form-item__label) {
  white-space: nowrap;
  overflow: visible;
  min-width: fit-content;
  margin-right: 12px;
}

/* 弹性布局保持标签与输入框同行 */
:deep(.el-form-item) {
  display: flex;
  align-items: center;
}
</style>
