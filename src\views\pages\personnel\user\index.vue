<template>
  <el-card>
    <el-scrollbar>
      <div class="flex-search" :style="{ minWidth: `${width - 42}px`, padding: '0px' }">
        <div class="left">
          <el-input v-model="state.search.keyword" :placeholder="$t('userManagement.SearchUser')" @keyup.enter="handleStateRefresh"> </el-input>
        </div>
        <div class="center">
          <!--  -->
        </div>
        <div class="right">
          <span class="el-button is-link">
            <el-button type="primary" @click="handleBatchChangeExpirationDate" :disabled="!(multipleSelection instanceof Array) || !multipleSelection.length" :loading="batchButLoading"> {{ t("userManagement.Batch Operation") }} </el-button>
          </span>

          <!-- <el-tooltip :content="$t('glob.noPower')" v-if="userInfo.hasPermission(安全管理中心_用户管理_客户密码策略)"> -->
          <span class="el-button is-link">
            <el-button type="primary" v-if="userInfo.hasPermission(安全管理中心_用户管理_客户密码策略)" @click="handlePwdStrategy({ type: 'tenant', id: '' })">{{ t("userManagement.Password Policy") }}</el-button>
          </span>
          <!-- </el-tooltip> -->
          <!-- <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.user.auth525177491799670784)">
            <span class="el-button is-link">
              <el-button type="primary" :disabled="!userInfo.hasPermission(PERMISSION.user.auth525177491799670784)" @click="handleStateInvite({})">{{ t("glob.invite") }}</el-button>
            </span>
          </el-tooltip> -->
          <!-- <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(安全管理中心_用户管理_新增)"> -->
          <span class="el-button is-link">
            <el-button type="primary" v-if="userInfo.hasPermission(安全管理中心_用户管理_新增)" :icon="Plus" @click="handleStateCreate({})">{{ t("userManagement.NewUser") }}</el-button>
          </span>
          <!-- </el-tooltip> -->
        </div>
      </div>
    </el-scrollbar>
    <el-table ref="tableRef" v-loading="state.loading" :data="state.data" :height="height - 104 - 20 - (state.total ? 32 : 0)" :style="{ width: `${width - 40}px`, margin: '0 auto' }" row-key="id" @selection-change="handleSelectionChange">
      <!-- <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :min-width="column.width || 120" :="column.showOverflowTooltip" :formatter="column.formatter" /> -->

      <el-table-column type="selection" width="55" :reserve-selection="true" />

      <TableColumn type="condition" :prop="`name`" :label="$t('userManagement.User')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="handleStateRefresh()" :formatter="(state.column.find((v) => v.key === 'name') || {}).formatter || void 0"></TableColumn>

      <TableColumn type="condition" :prop="`account`" :label="$t('userManagement.Username')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByAccount" :filters="$filter0" @filter-change="handleStateRefresh()" :formatter="(state.column.find((v) => v.key === 'account') || {}).formatter || void 0">
        <!-- 添加作用域插槽 -->
        <template #default="{ row }">
          <span style="cursor: pointer" @click="handleCopyAccount(row)"> {{ row.account }}@{{ row.tenantAbbreviation }} </span>
        </template>
      </TableColumn>

      <TableColumn type="condition" :prop="`phone`" :label="$t('userManagement.Mobile')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByPhone" :filters="$filter2" @filter-change="handleStateRefresh()" :formatter="(state.column.find((v) => v.key === 'phone') || {}).formatter || void 0"></TableColumn>

      <TableColumn type="condition" :prop="`email`" :label="$t('userManagement.Email')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByEmail" :filters="$filter2" @filter-change="handleStateRefresh()" :formatter="(state.column.find((v) => v.key === 'email') || {}).formatter || void 0"></TableColumn>

      <TableColumn type="default" :prop="`lastActivity`" :label="$t('userManagement.LatestLoginTime')" :min-width="120" :showOverflowTooltip="true" :formatter="(state.column.find((v) => v.key === 'lastActivity') || {}).formatter || void 0"></TableColumn>

      <!-- <el-table-column label="双因素认证" :width="94" prop="mfaState">
        <template #default="{ row }: { row: ItemData }">
          <div :key="row.id" style="display: flex; justify-content: center; align-items: center">
            <el-switch v-if="(row.mfaState || 'DEFAULT') !== 'DEFAULT'" v-model="row.mfaState" :disabled="!userInfo.hasPermission(安全管理中心_用户管理_编辑) || row.mfaState !== 'ENABLED'" :loading="loadingMfa.has(row.id)" :active-value="'ENABLED'" active-text="启用" :inactive-value="'DISABLED'" inactive-text="禁用" inline-prompt :before-change="() => mfaChange({ id: row.id, mfaState: row.mfaState === 'ENABLED' ? 'DISABLED' : 'ENABLED' })"> </el-switch>
          </div>
        </template>
      </el-table-column> -->

      <TableColumn
        type="enum"
        :prop="`mfaStateValue`"
        :label="$t('userManagement.TwoFactorAuthentication')"
        :width="114"
        :showOverflowTooltip="true"
        show-filter
        v-model:filtered-value="state.search.mfaState"
        :filters="[
          { value: 'ENABLED', text: $t('userManagement.Enable') },
          { value: 'DISABLED', text: $t('userManagement.Disabled') },
          // { value: 'DEFAULT', text: '默认' },
        ]"
        @filter-change="handleStateRefresh()"
      >
        <template #default="{ row }: { row: ItemData }">
          <div :key="row.id" style="display: flex; justify-content: center; align-items: center">
            <el-switch v-if="(row.mfaState || 'DEFAULT') !== 'DEFAULT'" v-model="row.mfaState" :disabled="!userInfo.hasPermission(安全管理中心_用户管理_编辑) || row.mfaState !== 'ENABLED'" :loading="loadingMfa.has(row.id)" :active-value="'ENABLED'" :active-text="$t('userManagement.Enable')" :inactive-value="'DISABLED'" :inactive-text="$t('userManagement.Disabled')" inline-prompt :before-change="() => mfaChange({ id: row.id, mfaState: row.mfaState === 'ENABLED' ? 'DISABLED' : 'ENABLED' })"> </el-switch>
          </div>
        </template>
      </TableColumn>

      <!-- {
      key: "blocked",
      label: "状态",
      formatter: (_row, _col, v) => (v ? h(ElTag, { type: "danger" }, () => t("glob.Frozen")) : _row.busy ? h(ElTag, { type: "warning" }, () => t("glob.Busy")) : h(ElTag, { type: "success" }, () => t("glob.Normal"))),
      showOverflowTooltip: true,
    }, -->
      <!-- <el-table-column prop="id" label="状态" :show-overflow-tooltip="false" :formatter="(row) => (row.blocked ? h(ElTag, { type: 'danger' }, () => t('glob.Frozen')) : row.improved ? (row.busy ? h(ElTag, { type: 'warning' }, () => t('glob.Busy')) : h(ElTag, { type: 'success' }, () => t('glob.Normal'))) : h(ElTag, { type: 'info' }, () => '未激活'))" :width="80"></el-table-column> -->

      <TableColumn type="enum" :prop="`blockedValue`" :label="$t('userManagement.State')" :width="114" :showOverflowTooltip="true" show-filter v-model:filtered-value="state.search.blocked" :filters="['false', 'true'].map((v) => ({ value: v, text: v === 'false' ? $t('userManagement.Active') : $t('userManagement.NotActive'), type: v === 'false' ? 'success' : 'info' }))" @filter-change="handleStateRefresh()"></TableColumn>
      <TableColumn type="condition" :prop="`expirationDate`" :label="`${$t('userManagement.Account Expiration Date')}`" :width="180" :showOverflowTooltip="true" :formatter="(state.column.find((v) => v.key === 'expirationDate') || {}).formatter || void 0"> </TableColumn>

      <!-- <TableColumn type="default" :prop="`accountOverdueValue`" :label="`冻结`" :width="114" :showOverflowTooltip="true">
        <template #default="{ row }">
          <el-tag type="success" v-if="!row.accountOverdue">未冻结</el-tag>
          <el-tag type="danger" v-else>冻结</el-tag>
        </template>
      </TableColumn> -->

      <el-table-column :label="t('glob.operate')" align="left" header-align="left" :width="228" fixed="right">
        <template #header="{ column }">
          <div style="display: flex; justify-content: center">
            <span style="margin: 0 10px">{{ column.label }}</span>
            <el-link type="primary" :underline="false" :icon="Refresh" @click="handleStateRefresh()" :title="t('glob.refresh')"></el-link>
          </div>
        </template>
        <template #default="{ row }">
          <!-- <el-button link type="default" :icon="View" @click="editorRef?.open({ '#TYPE': EditorType.Cat, ...row })">{{ t("glob.Cat") }}</el-button> -->
          <!-- <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(安全管理中心_用户管理_用户密码策略)"> -->
          <span class="el-button is-link">
            <el-link v-if="userInfo.hasPermission(安全管理中心_用户管理_用户密码策略)" @click="handlePwdStrategy({ type: 'user', id: row.id })" type="primary" :underline="false">{{ t("userManagement.Password Policy") }}</el-link>
          </span>
          <!-- </el-tooltip> -->
          <!-- <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(安全管理中心_用户管理_查看用户信息)"> -->
          <span>
            <el-link v-if="userInfo.hasPermission(安全管理中心_用户管理_查看用户信息)" type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="handleStateEditor({ ...row, watch: true })">{{ $t("glob.Cat") }}</el-link>
          </span>
          <!-- </el-tooltip> -->
          <!-- <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(安全管理中心_用户管理_编辑)"> -->
          <span class="el-button is-link">
            <el-button link type="primary" v-if="userInfo.hasPermission(安全管理中心_用户管理_编辑)" @click="handleStateEditor(row)">{{ t("glob.edit") }}</el-button>
          </span>

          <span>
            <el-button link type="primary" v-if="userInfo.hasPermission(安全管理中心_用户管理_编辑)" @click="handleThawUser(row)">{{ t("userManagement.thaw") }}</el-button>
          </span>
          <!-- v-if="userInfo?.currentTenant?.tenantId === row.tenantId"  -->
          <!-- </el-tooltip> -->
          <span>
            <!-- 用户管理('604221440782237696') -->
            <el-link :type="userInfo.hasPermission(安全管理中心_用户管理_安全) ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="userInfo.hasPermission(安全管理中心_用户管理_安全) ? state.loading : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
          </span>
          <!-- <el-dropdown trigger="click" @command="$event.callback(row)" class="el-button el-button--default is-link" style="vertical-align: middle; margin: 0">
            <span style="font-size: var(--el-font-size-base)">
              <el-button link type="primary">{{ t("glob.More") }}</el-button>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="{ callback: handleStateResetPassword }" v-if="userInfo.hasPermission(安全管理中心_用户管理_重置密码)">
                  <span>
                    <el-link v-if="userInfo.hasPermission(安全管理中心_用户管理_重置密码)" type="primary" :underline="false">重置密码</el-link>
                  </span>
                </el-dropdown-item>
                <el-dropdown-item :command="{ callback: handleStateUserCutBlock }" v-if="userInfo.hasPermission(安全管理中心_用户管理_冻结Or解冻)">
                  <span>
                    <el-link v-if="userInfo.hasPermission(安全管理中心_用户管理_冻结Or解冻) || row.blocked" type="primary" :underline="false">冻结用户</el-link>
                  </span>
                </el-dropdown-item>
                <el-dropdown-item :command="{ callback: handleStateUserCutActive }" v-if="userInfo.hasPermission(安全管理中心_用户管理_冻结Or解冻)">
                  <span>
                    <el-link v-if="userInfo.hasPermission(安全管理中心_用户管理_冻结Or解冻)" type="primary" :underline="false">解锁用户</el-link>
                  </span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown> -->
        </template>
      </el-table-column>
    </el-table>
    <div :style="{ margin: '8px 20px 20px' }">
      <el-pagination v-show="state.total" v-model:current-page="state.page" v-model:page-size="state.size" :page-sizes="state.sizes" :total="state.total" layout="->, total, sizes, prev, pager, next, jumper" small @size-change="handleStateRefresh()" @current-change="handleStateRefresh()" />
    </div>
    <EditForm ref="editorRef" :title="t('userManagement.User')"></EditForm>
    <EditInvite ref="editorByInviteRef" :title="t('userManagement.User')">
      <template #deleteItem="{ params }">
        <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
          <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
          <p class="title">
            {{ t("userManagement.Confirm removal") }}
            <span :style="{ color: 'var(--el-color-danger)' }">{{ params.name }}</span>
            {{ t("userManagement.Invited users?") }}
          </p>
        </div>
        <!-- <p class="tw-mt-[20px]">删除后将无法恢复！</p> -->
      </template>
    </EditInvite>
    <passwordStrategy ref="passwordStrategyRef" @refresh="handleStateRefresh"></passwordStrategy>
    <el-dialog v-model="dialogVisible" :title="t('userManagement.Set Password')" width="500" :before-close="handleClose" append-to-body>
      <!-- <el-alert title="密码生产" type="warning" description="不填写 新密码 则系统自动生成" show-icon /> -->
      <FormModel ref="formPasswordRef" :model="form" label-position="top" @submit="handleFinish">
        <FormItem
          :span="24"
          :label="t('userManagement.Password')"
          tooltip=""
          prop="password"
          :rules="[
            buildValidatorData({ name: 'required', title: t('userManagement.User Password') }),
            buildValidatorData({
              name: 'password',
              title: t('userManagement.User Password'),
              min: passwordMinLength,
            }),
          ]"
        >
          <el-input v-model="form.password" type="password" :placeholder="t('userManagement.PleaseEnterUserPassword')" show-password />
        </FormItem>
        <FormItem
          :span="24"
          :label="t('userManagement.Confirm Password')"
          style="margin-top: 25px"
          prop="rePassword"
          :rules="[
            buildValidatorData({ name: 'required', title: t('userManagement.Confirm Password') }),
            {
              validator: (_rule, value, callback) => callback(value === form.password ? undefined : t('userManagement.Two passwords need to be consistent')),
              trigger: 'blur',
            },
          ]"
        >
          <el-input v-model="form.rePassword" type="password" show-password autocomplete="new-password" :placeholder="t('userManagement.Confirm Password')" clearable></el-input>
        </FormItem>

        <FormItem :span="width > 600 ? 12 : 24" :label="``" tooltip="" prop="changePwdDuringLogin">
          <el-checkbox v-model="form.changePwdDuringLogin" :label="t('userManagement.Password needs to be changed when logging in')" size="large" />
        </FormItem>
      </FormModel>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="confrimPwd('cancel')">{{ t("glob.NO") }}</el-button>
          <el-button type="primary" @click="confrimPwd('confrim')"> {{ t("glob.OK") }} </el-button>
        </div>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts" name="PlatformDetailClient">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, nextTick, h, inject, getCurrentInstance, computed, toValue, defineComponent } from "vue";
import type { Ref } from "vue";
import { useI18n } from "vue-i18n";
import { sizes } from "@/utils/common";
import { formatterDate } from "@/utils/date";
import { bindFormBox } from "@/utils/bindFormBox";
import { bufferToBase64, stringToBuffer } from "@/utils/base64";
import moment from "@/lang/moment/zh-cn";
import treeAuth from "@/components/treeAuth/index.vue";
// Ui
import { ElMessage, ElButton, ElMessageBox, ElTag, ElFormItem, ElInput, ElDatePicker, ElSelect, ElOption, ElAvatar, ElCheckbox, ElAlert, ElText, ElForm, FormInstance, ElTable } from "element-plus";
import { Refresh, More, Plus, Edit, Unlock, Lock, User, InfoFilled, ArrowDown } from "@element-plus/icons-vue";
// Api
import { getUser as getItem, addUser as addItem, modUser as modItem, addUserListByInvite, delUserListByInvite, cutUserByBlock, cutUserByActive, releaseLoginLock, getRoleByUser, setUserByRole, cutUserPasswordReset, getRoleByUsable, UserItemScope as UserScope, setBatchChangeExpirationDate, setThawUser } from "@/api/personnel";
// import { genderOption } from "@/api/platform";
import { getTenantPasswordStrategy, saveTenantPasswordStrategy } from "@/api/personnel";
import type { UserItem as ItemData } from "@/api/personnel";
import { getAvatar, getPublicKey } from "@/api/system";
import getUserInfo from "@/utils/getUserInfo";
import passwordStrategy from "./passwordStrategy.vue";
import { getTenantPasswordStrategyMsg, getAppointUserPasswordStrategyMsg } from "@/api/personnel";

import TableColumn from "@/components/tableColumn/TableColumn.vue";

// Editor
import EditForm from "./EditForm.vue";
import EditInvite from "./EditInvite.vue";
import { buildValidatorData } from "@/utils/validate";
import { useSiteConfig } from "@/stores/siteConfig";
import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";
import {
  /*  */
  安全管理中心_用户管理_可读,
  安全管理中心_用户管理_编辑,
  安全管理中心_用户管理_新增,
  安全管理中心_用户管理_客户密码策略,
  安全管理中心_用户管理_查看用户信息,
  安全管理中心_用户管理_安全,
  安全管理中心_用户管理_所有权限,
  安全管理中心_用户管理_管理,
  安全管理中心_用户管理_查看审计日志,
  安全管理中心_用户管理_重置密码,
  安全管理中心_用户管理_冻结Or解冻,
  安全管理中心_用户管理_用户密码策略,
} from "@/views/pages/permission";
import { exoprtMatch1, exoprtMatch2, exoprtMatch3 } from "@/components/tableColumn/common";
const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);
const $filter2 = ref(exoprtMatch3);

const ctx = getCurrentInstance()!;
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", {
    type: "error",
  });
  throw new Error("Component context initialization failed!");
}
const userInfo = getUserInfo();
const siteConfig = useSiteConfig();
const dialogVisible = ref(false);
const form = ref({
  ptype: "RSA",
  password: "",
  rePassword: "",
  changePwdDuringLogin: false,
});
const handleClose = (done: () => void) => {
  done();
};
const editorRef = ref<InstanceType<typeof EditForm>>();
const editorByInviteRef = ref<InstanceType<typeof EditInvite>>();
const passwordStrategyRef = ref<InstanceType<typeof passwordStrategy>>();
const formPasswordRef = ref<InstanceType<typeof FormModel>>();
const dialogVisibleshow = ref(false);
const treeStyle = ref({
  pointerEvents: "none",
});
const containerId = ref("");

function handleThawUser(row) {
  ElMessageBox.confirm(t("userManagement.AreYouSureToThawUser"), t("userManagement.Unlock user"), {
    confirmButtonText: t("glob.Confirm"),
    cancelButtonText: t("glob.Cancel"),
    type: "warning",
    beforeClose: async (action, instance, done) => {
      if (action === "confirm") {
        const { message, success } = await setThawUser({ platform: row.platform, username: `${row.account}@${row.tenantAbbreviation}` });
        if (!success) throw new Error(message);
        ElMessage.success(t("axios.Operation successful"));
        handleStateRefresh();
        done();
      } else {
        done();
      }
    },
  })
    .then(() => {})
    .catch(() => {});
}

async function createItem(params: Partial<ItemData>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params }, async (req) => {
      const { success, message, data } = await addItem({ ...req });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(`添加成功！`);
    });
  } catch (error) {
    /*  */
  }
}

const loadingMfa = ref(new Set<string>());
async function mfaChange(row: { id: string; mfaState: "DISABLED" | "ENABLED" }) {
  try {
    loadingMfa.value.add(row.id);
    const { success, message, data } = await modItem({ id: row.id, mfaState: row.mfaState });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(`编辑成功！`);
    return await Promise.resolve(true);
  } catch (error) {
    return await Promise.reject(false);
  } finally {
    loadingMfa.value.delete(row.id);
  }
}
async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  let controller = new AbortController();
  const response = getItem({
    platform: params.platform as string,
    ...state.search,
    controller,
    paging: { pageNumber: state.page, pageSize: state.size },
  });
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const { success, message, date, data, page: page = 1, size: size = 50, total: total = 0 } = await response;
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);

      const difference = date && date.isValid() ? moment().valueOf() - date.valueOf() : 0;
      return (data instanceof Array ? data : []).map((v) => {
        if (v.lastActivity && Number(v.lastActivity)) return Object.assign(v, { mfaStateValue: String(v.mfaState), blockedValue: String(v.blocked), accountOverdueValue: String(v.accountOverdue), lastActivity: `${Number(v.lastActivity) - difference}` });
        else return Object.assign(v, { mfaStateValue: String(v.mfaState), blockedValue: String(v.blocked), accountOverdueValue: String(v.accountOverdue), lastActivity: null });
      });
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(50);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}

/*********************************************************/

const { t } = useI18n();
const passwordMinLength = ref<number>(0);

const width = inject<Ref<number>>("width", ref(100));
const height = inject<Ref<number>>("height", ref(100));

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: any };
  column: {
    key: keyof T;
    label?: string;
    align?: "left" | "center" | "right";
    width?: number;
    formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode;
    showOverflowTooltip: boolean;
  }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}

// 设置table formater文字class
const tableTextClass = "tw-text-gray-400";

const state = reactive<StateData<ItemData>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {
    scope: "INTERNAL",
    keyword: "",
    schemas: ["ROLE"].join(","),

    mfaState: "",

    eqName: [] /* 等于的用户名称 */,
    includeName: [] /* 包含的用户名称 */,
    nameFilterRelation: "AND" /* 用户名称过滤关系(AND,OR) */,
    neName: [] /* 不等于的用户名称 */,
    excludeName: [] /* 不包含的用户名称 */,

    eqAccount: [] /* 等于的用户账号 */,
    includeAccount: [] /* 包含的用户账号 */,
    accountFilterRelation: "AND" /* 用户账号过滤关系(AND,OR) */,
    neAccount: [] /* 不等于的用户账号 */,
    excludeAccount: [] /* 不包含的用户账号 */,

    eqPhone: [] /* 等于的手机号 */,
    includePhone: [] /* 包含的手机号 */,
    phoneFilterRelation: "AND" /* 手机号过滤关系(AND,OR) */,
    nePhone: [] /* 不等于的手机号 */,
    excludePhone: [] /* 不包含的手机号 */,

    eqEmail: [] /* 等于的邮箱 */,
    includeEmail: [] /* 包含的邮箱 */,
    emailFilterRelation: "AND" /* 邮箱过滤关系(AND,OR) */,
    neEmail: [] /* 不等于的邮箱 */,
    excludeEmail: [] /* 不包含的邮箱 */,
  },
  column: [
    /* 列 */
    // { key: "id", label: "用户 ID" },
    {
      key: "name",
      label: "用户",
      width: 200,
      showOverflowTooltip: false,
      formatter: (row, col, value) => {
        async function getAvatarSrc(image?: HTMLImageElement) {
          // // console.log(_dom)
          if (image instanceof HTMLImageElement) {
            if (image.alt === row.profilePicture) return;
            image.alt = row.profilePicture || "";
            const src = await getAvatar({ filePath: row.profilePicture });
            image.src = src;
            image.onload = image.onerror = async () => {
              URL.revokeObjectURL(src);
            };
          }
        }
        return h("div", { class: "tw-flex tw-items-center" }, [
          h(ElAvatar, { class: ["tw-flex-shrink-0", "tw-mr-[6px]"], size: 50 }, () => [
            h("img", {
              ref: ($el) => getAvatarSrc($el as HTMLImageElement | undefined),
            }),
          ]),
          h("div", [h("p", value as string) /* , h("p", { class: tableTextClass }, row.email) */]),
        ]);
      },
    },
    // { key: "nickname", label: "昵称" },
    {
      key: "account",
      label: "账号",
      showOverflowTooltip: true,
      formatter: (_row, _col, v) => h("p", { class: tableTextClass }, v ? `${v}@${_row.tenantAbbreviation}` : "--"),
    },
    {
      key: "phone",
      label: "手机号",
      showOverflowTooltip: true,
      formatter: (_row, _col, v) => h("p", { class: tableTextClass }, `${v}` || "--"),
    },
    {
      key: "email",
      label: "邮箱",
      showOverflowTooltip: true,
      formatter: (_row, _col, v) => h("p", { class: tableTextClass }, v ? `${v}` : "--"),
    },
    // eslint-disable-next-line prettier/prettier
    // {
    //   key: "roles",
    //   label: "角色",
    //   width: 160,
    //   showOverflowTooltip: true,
    //   formatter: (_row, _col, v) => {
    //     function setShowRole(roleList: import("@/api/personnel").RoleItem[]) {
    //       return roleList.map(({ superAdmin, dataAdmin, name }) => {
    //         const roleName = name || "--";
    //         const tagStyle = { margin: "0 3px" };
    //         if (superAdmin) return h(ElTag, { style: tagStyle }, () => roleName);
    //         else if (dataAdmin) return h(ElTag, { type: "success", style: tagStyle }, () => roleName);
    //         else return h(ElTag, { type: "info", style: tagStyle }, () => roleName);
    //       });
    //     }
    //     return h("span", { class: tableTextClass }, v instanceof Array ? setShowRole(v) : "--");
    //   },
    // },
    // 冻结 > 忙碌 > 正常
    // {
    //   key: "blocked",
    //   label: "状态",
    //   formatter: (_row, _col, v) => (v ? h(ElTag, { type: "danger" }, () => t("glob.Frozen")) : _row.busy ? h(ElTag, { type: "warning" }, () => t("glob.Busy")) : h(ElTag, { type: "success" }, () => t("glob.Normal"))),
    //   showOverflowTooltip: true,
    // },
    {
      key: "lastActivity",
      label: "最近访问时间",
      showOverflowTooltip: true,
      formatter: (_row, _col, v) => {
        const time = moment(String(v || ""), "x");
        return h("p", { class: tableTextClass }, time.isValid() ? time.fromNow() : "--");
      },
    },

    {
      key: "expirationDate",
      label: "账号有效期",
      showOverflowTooltip: true,
      formatter: (_row, _col, v) => {
        const _type = new Date(moment(Number(v)).format("YYYY-MM-DD") + " " + "23:59:59").getTime() < new Date().getTime() ? "info" : "";
        return v ? h(ElText, { type: _type }, v ? `${moment(Number(v)).format("YYYY-MM-DD")}${_type === "info" ? "(已过期)" : ""}` : "") : "";
      },
    },
    // { key: "gender", label: "性别", formatter: (_row, _col, v) => genderOption.find(({ value }) => v === value)?.label || "" },
    // { key: "id", label: "用户ID", width: 160, showOverflowTooltip: true, formatter: (_row, _col, v) => h("p", { class: tableTextClass }, `${v}` || "--") },
    // { key: "createdTime", label: "创建日期", formatter: (_row, _col, v) => formatterDate(v as string) },
    // { key: "updatedTime", label: "修改日期", formatter: (_row, _col, v) => formatterDate(v as string) },
  ],
  data: [],
  page: 1,
  size: 50,
  sizes,
  total: 0,
});

const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";

    // eqName: [] /* 等于的用户名称 */,
    // includeName: [] /* 包含的用户名称 */,
    // nameFilterRelation: "AND" /* 用户名称过滤关系(AND,OR) */,
    // neName: [] /* 不等于的用户名称 */,
    // excludeName: [] /* 不包含的用户名称 */,
    if (toValue(searchType0ByName) === "include") value0 = state.search.includeName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = state.search.excludeName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = state.search.eqName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = state.search.neName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = state.search.includeName[state.search.includeName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = state.search.excludeName[state.search.excludeName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = state.search.eqName[state.search.eqName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = state.search.neName[state.search.neName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: state.search.nameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    state.search.nameFilterRelation = v.relation;
    state.search.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByAccount = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByAccount = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByAccount = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByAccount) === "include") value0 = state.search.includeAccount[0] || "";
    if (toValue(searchType0ByAccount) === "exclude") value0 = state.search.excludeAccount[0] || "";
    if (toValue(searchType0ByAccount) === "eq") value0 = state.search.eqAccount[0] || "";
    if (toValue(searchType0ByAccount) === "ne") value0 = state.search.neAccount[0] || "";
    let value1 = "";
    if (toValue(searchType1ByAccount) === "include") value1 = state.search.includeAccount[state.search.includeAccount.length - 1] || "";
    if (toValue(searchType1ByAccount) === "exclude") value1 = state.search.excludeAccount[state.search.excludeAccount.length - 1] || "";
    if (toValue(searchType1ByAccount) === "eq") value1 = state.search.eqAccount[state.search.eqAccount.length - 1] || "";
    if (toValue(searchType1ByAccount) === "ne") value1 = state.search.neAccount[state.search.neAccount.length - 1] || "";
    return {
      type0: toValue(searchType0ByAccount),
      type1: toValue(searchType1ByAccount),
      relation: state.search.accountFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByAccount.value = v.type0 as typeof searchType0ByAccount extends import("vue").Ref<infer T> ? T : string;
    searchType1ByAccount.value = v.type1 as typeof searchType1ByAccount extends import("vue").Ref<infer T> ? T : string;
    state.search.accountFilterRelation = v.relation;
    state.search.includeAccount = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeAccount = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqAccount = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neAccount = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByPhone = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchType1ByPhone = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchByPhone = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByPhone) === "include") value0 = state.search.includePhone[0] || "";
    if (toValue(searchType0ByPhone) === "exclude") value0 = state.search.excludePhone[0] || "";
    if (toValue(searchType0ByPhone) === "eq") value0 = state.search.eqPhone[0] || "";
    if (toValue(searchType0ByPhone) === "ne") value0 = state.search.nePhone[0] || "";
    let value1 = "";
    if (toValue(searchType1ByPhone) === "include") value1 = state.search.includePhone[state.search.includePhone.length - 1] || "";
    if (toValue(searchType1ByPhone) === "exclude") value1 = state.search.excludePhone[state.search.excludePhone.length - 1] || "";
    if (toValue(searchType1ByPhone) === "eq") value1 = state.search.eqPhone[state.search.eqPhone.length - 1] || "";
    if (toValue(searchType1ByPhone) === "ne") value1 = state.search.nePhone[state.search.nePhone.length - 1] || "";
    return {
      type0: toValue(searchType0ByPhone),
      type1: toValue(searchType1ByPhone),
      relation: state.search.phoneFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByPhone.value = v.type0 as typeof searchType0ByPhone extends import("vue").Ref<infer T> ? T : string;
    searchType1ByPhone.value = v.type1 as typeof searchType1ByPhone extends import("vue").Ref<infer T> ? T : string;
    state.search.phoneFilterRelation = v.relation;
    state.search.includePhone = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludePhone = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqPhone = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.nePhone = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByEmail = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchType1ByEmail = ref<"include" | "exclude" | "eq" | "ne">("eq");
const searchByEmail = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    // eqEmail: [] /* 等于的邮箱 */,
    // includeEmail: [] /* 包含的邮箱 */,
    // emailFilterRelation: "AND" /* 邮箱过滤关系(AND,OR) */,
    // neEmail: [] /* 不等于的邮箱 */,
    // excludeEmail: [] /* 不包含的邮箱 */,
    if (toValue(searchType0ByEmail) === "include") value0 = state.search.includeEmail[0] || "";
    if (toValue(searchType0ByEmail) === "exclude") value0 = state.search.excludeEmail[0] || "";
    if (toValue(searchType0ByEmail) === "eq") value0 = state.search.eqEmail[0] || "";
    if (toValue(searchType0ByEmail) === "ne") value0 = state.search.neEmail[0] || "";
    let value1 = "";
    if (toValue(searchType1ByEmail) === "include") value1 = state.search.includeEmail[state.search.includeEmail.length - 1] || "";
    if (toValue(searchType1ByEmail) === "exclude") value1 = state.search.excludeEmail[state.search.excludeEmail.length - 1] || "";
    if (toValue(searchType1ByEmail) === "eq") value1 = state.search.eqEmail[state.search.eqEmail.length - 1] || "";
    if (toValue(searchType1ByEmail) === "ne") value1 = state.search.neEmail[state.search.neEmail.length - 1] || "";
    return {
      type0: toValue(searchType0ByEmail),
      type1: toValue(searchType1ByEmail),
      relation: state.search.emailFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByEmail.value = v.type0 as typeof searchType0ByEmail extends import("vue").Ref<infer T> ? T : string;
    searchType1ByEmail.value = v.type1 as typeof searchType1ByEmail extends import("vue").Ref<infer T> ? T : string;
    state.search.emailFilterRelation = v.relation;
    state.search.includeEmail = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeEmail = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqEmail = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neEmail = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

// function handleScopeChange(v: keyof typeof UserScope) {
//   // console.log(v);
//   handleStateRefresh()
// }
// 复制方法
const handleCopyAccount = (row) => {
  const textToCopy = `${row.account}@${row.tenantAbbreviation}`;
  navigator.clipboard
    .writeText(textToCopy)
    .then(() => {
      ElMessage.success("账号复制成功");
    })
    .catch(() => {
      // 降级方案
      const textarea = document.createElement("textarea");
      textarea.value = textToCopy;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand("copy");
      document.body.removeChild(textarea);
      ElMessage.success("已复制到剪贴板");
    });
};
async function handleStateCreate(params: Partial<ItemData>) {
  await createItem(params);
  await handleStateRefresh();
}
async function handleStateInvite(params: Partial<ItemData>) {
  await nextTick();
  if (!editorByInviteRef.value) return;
  try {
    await editorByInviteRef.value.open({ ...params }, async (req) => {
      const { success, message, data } = await addUserListByInvite({
        userIds: <string[]>req.userIds,
      });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(t("axios.Operation successful"));
    });
  } catch (error) {
    /*  */
  }
  await handleStateRefresh();
}

async function handlePwdStrategy(params: { type: "tenant" | "user"; id: string }) {
  await nextTick();
  if (!passwordStrategyRef.value) return;
  try {
    await passwordStrategyRef.value.open({ ...params });
  } catch (error) {
    /*  */
  }
  // await handleStateRefresh();
}

async function handleStateDelete(params: Partial<ItemData> & Record<string, unknown>) {
  await nextTick();
  if (!editorByInviteRef.value) return;
  try {
    await editorByInviteRef.value.confirm({ $title: `${t("glob.remove")}受邀用户`, $slot: "deleteItem", ...params }, async (req) => {
      const { success, message, data } = await delUserListByInvite({
        userIds: [<string>req.id],
      });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(t("axios.Operation successful"));
    });
  } catch (error) {
    /*  */
  }
  await handleStateRefresh();
}
async function handleStateEditor(params: Partial<ItemData>) {
  await editorItem(params);
  await handleStateRefresh();
}
async function editorItem(params: Partial<ItemData>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  if (typeof onCleanup === "function") onCleanup(() => editorRef.value?.close());
  if (!editorRef.value) return;
  try {
    await editorRef.value.open({ ...params, accountSuffix: params.tenantAbbreviation }, async (req) => {
      const { success, message, data } = await modItem({
        id: <string>params.id,
        ...req,
      });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(t("axios.Operation successful"));
    });
  } catch (error) {
    /*  */
  }
}
getPasswordMinLength(null);
async function getPasswordMinLength(id) {
  // passwordMinLength.value = 0;
  if (id == null) {
    await getTenantPasswordStrategyMsg({}).then((res) => {
      if (res.success) {
        passwordMinLength.value = Number(res.data.minLength) || 0;
      }
    });
  } else {
    await getAppointUserPasswordStrategyMsg({ userId: id }).then((res) => {
      if (res.success) {
        passwordMinLength.value = Number(res.data.minLength) || 0;
      }
    });
  }

  return passwordMinLength.value;
}
async function confrimPwd(type) {
  // console.log(formPasswordRef.value);
  if (type === "confrim") {
    if (formPasswordRef.value) {
      formPasswordRef.value.validate(async (valid) => {
        if (valid) {
          let _password: ArrayBuffer | null = null;
          if (form.value.password) {
            const importPublicKey = await getPublicKey();
            _password = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.value.password));
          }
          const { success, message, data } = await cutUserPasswordReset({
            id: userId.value as string,
            ...(_password ? { password: bufferToBase64(_password) } : {}),
            ptype: "RSA",
            changeDuringLogin: form.value.changePwdDuringLogin,
          });
          if (success) {
            ElMessage.success(t("axios.Operation successful"));
          } else throw Object.assign(new Error(message), { success, data });
          dialogVisible.value = false;
          formPasswordRef.value.clearValidate();
        }
      });
    }
  } else {
    dialogVisible.value = false;
    formPasswordRef.value.clearValidate();
  }
}
const userId = ref(null);

async function handleStateResetPassword(params: Partial<ItemData>) {
  // console.log(params,6666)
  userId.value = params.id;
  // const form = reactive<Record<"title" | "password" | "changeDuringLogin" | "rePassword", string>>({
  //   title: "重置密码",
  //   password: "",
  //   changeDuringLogin: "",
  //   rePassword: "",
  // });
  try {
    // let userPassword = 0;
    dialogVisible.value = true;
    form.value.password = "";
    form.value.rePassword = "";
    form.value.changePwdDuringLogin = false;

    await getPasswordMinLength(params.id);
    // await bindFormBox(
    //   [
    //     /*  */

    //     h(ElAlert, { type: "warning", showIcon: true, title: "密码生成", description: "不填写 新密码 则系统自动生成" }),
    //     h(ElFormItem, { rules: [buildValidatorData({ name: "password", title: "密码", min: await getPasswordMinLength(params.id) })], prop: "password", label: "新密码" }, () => h(ElInput, { "type": "password", "showPassword": true, "modelValue": form.password, "clearable": true, "onUpdate:modelValue": ($event) => (form.password = $event) })),
    //     h(ElFormItem, { rules: [buildValidatorData({ name: "required", title: "确认密码" }), { validator: (_rule, value, callback) => callback(console.log(value)), trigger: "blur" }], prop: "password", label: "确认密码" }, () => h(ElInput, { "type": "password", "showPassword": true, "modelValue": form.rePassword, "clearable": true, "onUpdate:modelValue": ($event) => (form.rePassword = $event) })),
    //     h(ElFormItem, { prop: "changeDuringLogin", label: "" }, () => h(ElCheckbox, { "label": "登录时是否需要修改密码", "modelValue": form.changeDuringLogin, "onUpdate:modelValue": ($event) => (form.changeDuringLogin = $event) })),
    //   ],
    //   form,
    //   async () => {
    //     let _password: ArrayBuffer | null = null;
    //     if (form.password) {
    //       const importPublicKey = await getPublicKey();
    //       _password = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(form.password));
    //     }

    //     const { success, message, data } = await cutUserPasswordReset({ id: params.id as string, ...(_password ? { password: bufferToBase64(_password) } : {}), ptype: "RSA", changeDuringLogin: form.changeDuringLogin });
    //     if (success) {
    //       ElMessage.success("密码重置成功");
    //     } else throw Object.assign(new Error(message), { success, data });
    //     return { success, message };
    //   }
    // );
  } catch (error) {
    /*  */
  }
  await handleStateRefresh();
}
async function handleStateUserCutBlock(params: Partial<ItemData>) {
  const form = reactive<Record<"title" | "frozenNote" | "frozenExpire", string>>({
    title: "冻结用户",
    frozenNote: "",
    frozenExpire: "2099-01-01 00:00:00.000",
  });
  try {
    await bindFormBox(
      [
        h("p", {}, "确定冻结用户吗？"),
        /*  */
        // h(ElFormItem, { rules: [{ required: true, message: "请选择冻结至时间", trigger: "blur" }], prop: "frozenExpire", label: "冻结至" }, () => h(ElDatePicker, { "modelValue": form.frozenExpire, "valueFormat": "YYYY-MM-DD HH:mm:ss.SSS", "onUpdate:modelValue": ($event) => (form.frozenExpire = $event) })),
        // h(ElFormItem, { rules: [{ required: true, message: "请输入冻结备注", trigger: "blur" }], prop: "frozenNote", label: "冻结备注" }, () => h(ElInput, { "type": "textarea", "modelValue": form.frozenNote, "onUpdate:modelValue": ($event) => (form.frozenNote = $event) })),
      ],
      form,
      async () => {
        const { success, message, data } = await cutUserByBlock({
          id: params.id as string /* , frozenNote: form.frozenNote, frozenExpire: form.frozenExpire */,
        });
        if (success) {
          ElMessage.success("用户冻结成功");
        } else throw Object.assign(new Error(message), { success, data });
        return { success, message };
      }
    );
  } catch (error) {
    /*  */
  }
  await handleStateRefresh();
}
async function handleStateUserCutActive(params: Partial<ItemData>) {
  const form = reactive<Record<"title", string>>({
    title: "解锁用户",
  });
  try {
    await bindFormBox([h("p", {}, "确定解锁用户吗？")], form, async () => {
      const { success, message, data } = await cutUserByActive({
        id: params.id as string,
      });
      const loginData = await releaseLoginLock({ id: params.id as string });
      // // console.log(success, message, data)
      if (success || loginData.success) {
        ElMessage.success(t("axios.Operation successful"));
      } else throw Object.assign(new Error(message), { success, data });
      return { success, message };
    });
  } catch (error) {
    /*  */
  }
  await handleStateRefresh();
}
async function handleStateUserCutRole(params: Partial<ItemData>) {
  const form = reactive<Record<"title", string> & { roleIds: string[] }>({
    title: "变更角色",
    roleIds: [],
  });
  try {
    const { success, data } = await getRoleByUser({
      id: params.id as string,
      appId: (siteConfig.baseInfo || {}).app || "",
    });
    if (!success) throw new Error("已有角色查询失败!");
    form.roleIds = (data instanceof Array ? data : []).map((v) => v.id!);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }

  const currentUserRole: import("@/api/personnel").RoleItem[] = [];

  try {
    const { success, message, data } = await getRoleByUsable({
      appId: (siteConfig.baseInfo || {}).app,
    });
    if (!success) throw Object.assign(new Error(message), { success, data });
    currentUserRole.push(...(data instanceof Array ? data : []));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
  try {
    await bindFormBox(
      [
        h(
          ElFormItem,
          {
            rules: [
              {
                required: false,
                type: "array",
                message: "请选择角色",
                trigger: "blur",
              },
            ],
            prop: "roleIds",
            label: `${params.name}拥有的角色`,
          },
          () =>
            h(
              ElSelect,
              {
                "modelValue": form.roleIds,
                "onUpdate:modelValue": ($event) => (form.roleIds = $event),
                "multiple": true,
                "clearable": true,
                "collapseTags": false,
                "collapseTagsTooltip": true,
                "filterable": true,
                "style": { width: "100%" },
              },
              () => currentUserRole.map((role) => h(ElOption, { label: role.name!, value: role.id! }))
            )
        ),
      ],
      form,
      async () => {
        const { app } = siteConfig.baseInfo!;
        const { success, message, data } = await setUserByRole({
          appId: app,
          userIds: [params.id as string],
          roleIds: form.roleIds,
        });
        if (success) {
          ElMessage.success(t("axios.Operation successful"));
        } else throw Object.assign(new Error(message), { success, data });
        return { success, message };
      }
    );
  } catch (error) {
    /*  */
  }
  await handleStateRefresh();
}
async function handleStateRefresh() {
  if (state.loading) return;
  state.loading = true;
  state.data.splice(0, state.data.length, ...(await querysItem({})));
  state.loading = false;
}
handleStateRefresh();

const multipleSelection = ref<ItemData[]>([]);

async function handleSelectionChange(v, row) {
  multipleSelection.value = v;
}

const batchChangeExpirationDateForm = ref({ expirationDate: "", blocked: "false" });
const batchChangeExpirationDateFormRef = ref<FormInstance>();
const batchButLoading = ref<boolean>(false);
const tableRef = ref<typeof ElTable>();
async function handleBatchChangeExpirationDate() {
  try {
    // batchButLoading.value = true;
    if (!(multipleSelection.value instanceof Array) || !multipleSelection.value.length) return;
    // const { data, message, success } = await getTenantPasswordStrategy({});
    // if (!success) throw Error(message);
    // const date = new Date() as Date;
    // batchChangeExpirationDateForm.value.expirationDate = [data.accountExpirationDateConfigType].includes("CUSTOM") ? ((new Date(date.getFullYear(), date.getMonth() + 1, date.getDate(), 23, 59, 59).getTime() + 1000 * 60 * 60 * 24 * Number(data.customAccountExpirationDate)) as any) : "";
    // batchButLoading.value = false;
    ElMessageBox({
      title: t("userManagement.Batch Operation"),
      showCancelButton: true,
      message: h(
        defineComponent({
          setup() {
            return () =>
              h(ElForm, { ref: (v) => (batchChangeExpirationDateFormRef.value = v as FormInstance), labelWidth: "90px", style: { width: "396px" }, model: batchChangeExpirationDateForm.value, labelPosition: "left" }, [
                /*  */
                h(ElFormItem, { labelWidth: "0" }, () => h(ElAlert, { type: "warning", showIcon: true, closable: false, title: t("userManagement.After setting the validity period in bulk, if there are users who have not enabled the account validity period, the system will automatically enable it, and the validity period will take effect according to the time configured on the current page") })),
                h(ElFormItem, { label: t("userManagement.Active"), prop: "blocked" }, () =>
                  h(ElCheckbox, {
                    /*  */
                    "modelValue": batchChangeExpirationDateForm.value.blocked,
                    "onUpdate:modelValue": ($event) => (batchChangeExpirationDateForm.value.blocked = $event as string),
                    "true-value": "false",
                    "false-value": "true",
                  })
                ),
                h(ElFormItem, { label: t("userManagement.Account Expiration Date"), prop: "expirationDate" }, () =>
                  h(ElDatePicker, {
                    /*  */
                    "type": "date",
                    "class": "tw-w-full",
                    "modelValue": batchChangeExpirationDateForm.value.expirationDate,
                    "onUpdate:modelValue": ($event) => {
                      if ($event) {
                        const dateValue = new Date(Number($event)) as Date;
                        dateValue.setHours(23);
                        dateValue.setMinutes(59);
                        dateValue.setSeconds(59);
                        batchChangeExpirationDateForm.value.expirationDate = dateValue.getTime() as any;
                      } else batchChangeExpirationDateForm.value.expirationDate = undefined as any;
                    },
                    "placeholder": t("userManagement.Please select the account expiration date"),
                    "value-format": "x",
                    "default-time": new Date(2024, 0, 1, 23, 59, 59),
                    "disabledDate": (date) => date < new Date(new Date().setHours(0, 0, 0, 0)),
                  })
                ),
              ]);
          },
        })
      ),
      beforeClose: async (action, instance, done) => {
        if (action === "confirm") {
          batchChangeExpirationDateFormRef.value &&
            batchChangeExpirationDateFormRef.value.validate(async (valid) => {
              if (!valid) return;
              try {
                const { data, message, success } = await setBatchChangeExpirationDate({ userIds: multipleSelection.value.map((v) => v.id), ...batchChangeExpirationDateForm.value });
                if (!success) throw new Error(message);
                ElMessage.success(t("axios.Operation successful"));
                handleStateRefresh();
                tableRef.value && tableRef.value.clearSelection();
                done();
              } catch (error) {
                error instanceof Error && ElMessage.error(error.message);
              }
            });
        } else {
          batchChangeExpirationDateFormRef.value && batchChangeExpirationDateFormRef.value.resetFields();
          await nextTick();
          done();
        }
      },
    })
      .then(() => {})
      .catch(() => {});
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}
</script>

<style lang="scss" scoped></style>
