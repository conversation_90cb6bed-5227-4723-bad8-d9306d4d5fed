<template>
  <!-- 对话框表单 -->
  <el-dialog v-model="visible" :handle-cancel="handleCancel" class="loading-class">
    <template #header>
      <div>
        {{ title }} <el-icon @click="oepnMessage"><QuestionFilled /></el-icon>
      </div>
    </template>
    <template #default="{ width }">
      <div class="main">
        <ul>
          <li>{{ t("userManagement.User Password Policy") }}</li>
          <li>{{ t("userManagement.Minimum password length") }}</li>
          <li>{{ t("userManagement.Enforce password complexity") }}</li>
          <li>{{ t("userManagement.UserCanChangePassword") }}</li>
          <li>{{ t("userManagement.Password expire") }}</li>
          <li>{{ t("userManagement.Password history") }}</li>
          <li>{{ t("userManagement.Maximum password age days") }}</li>
          <li>{{ t("userManagement.Account Expiration Date") }}</li>
        </ul>
        <ul>
          <li>{{ t("userManagement.DefaultValue") }}</li>
          <li>
            <div v-if="backstageObj.minLengthConfigType == 'DISABLE'"><FontAwesomeIcon class="tw-mx-[5px]" :icon="faBan"></FontAwesomeIcon></div>
            <div v-else>{{ form.defaultMinLength }}</div>
          </li>
          <li>
            <el-icon v-if="!Showhardeningicon()"><Select /></el-icon>
            <div v-else><FontAwesomeIcon class="tw-mx-[5px]" :icon="faBan"></FontAwesomeIcon></div>
          </li>
          <li>
            <el-icon v-if="!ShowAllowUserChangeicon()"><Select /></el-icon>
            <div v-else><FontAwesomeIcon class="tw-mx-[5px]" :icon="faBan"></FontAwesomeIcon></div>
          </li>
          <li>
            <!-- {{ form.defaultPasswordExpire }}
            {{ form.customPasswordExpire }} -->
            <el-icon v-if="!ShowPasswordExpireicon()"><Select /></el-icon>
            <div v-else><FontAwesomeIcon class="tw-mx-[5px]" :icon="faBan"></FontAwesomeIcon></div>
          </li>
          <li>
            <div v-if="backstageObj.histPasswordLimitConfigType == 'DISABLE'"><FontAwesomeIcon class="tw-mx-[5px]" :icon="faBan"></FontAwesomeIcon></div>
            <div v-else>{{ form.defaultHistPasswordLimit }}</div>
          </li>
          <li>
            {{ form.defaultPasswordExpireDays }}
          </li>
          <li>
            <!-- <FontAwesomeIcon class="tw-mx-[5px]" :icon="faBan"></FontAwesomeIcon> -->
            <span v-if="isTenant"><FontAwesomeIcon class="tw-mx-[5px]" :icon="faBan"></FontAwesomeIcon></span>
            <div v-else>
              <span v-if="backstageObj.defaultAccountExpirationDateConfigType === 'DISABLE'"><FontAwesomeIcon class="tw-mx-[5px]" :icon="faBan"></FontAwesomeIcon></span>
              <span v-else>{{ form.defaultAccountExpirationDate }}</span>
            </div>
          </li>
        </ul>
        <ul>
          <li>{{ t("userManagement.Feature Configuration") }}</li>
          <li>
            <el-select v-model="form.minLengthConfigType">
              <el-option value="DEFAULT" :label="t('userManagement.UserDefault')">{{ t("userManagement.UserDefault") }}</el-option>
              <el-option value="CUSTOM" :label="t('userManagement.Custom')">{{ t("userManagement.Custom") }}</el-option>
              <el-option value="DISABLE" :label="t('userManagement.Disabled')">{{ t("userManagement.Disabled") }}</el-option>
            </el-select>
          </li>
          <li>
            <el-select v-model="form.customHardening">
              <el-option value="null" :label="t('userManagement.UserDefault')">{{ t("userManagement.UserDefault") }}</el-option>
              <!-- <el-option :value="true" label="启用">启用</el-option> -->
              <el-option value="CUSTOM" :label="t('userManagement.Custom')">{{ t("userManagement.Custom") }}</el-option>
            </el-select>
          </li>
          <li>
            <el-select v-model="form.customAllowUserChange">
              <el-option value="null" :label="t('userManagement.UserDefault')">{{ t("userManagement.UserDefault") }}</el-option>
              <!-- <el-option :value="true" label="启用">启用</el-option> -->
              <el-option value="CUSTOM" :label="t('userManagement.Custom')">{{ t("userManagement.Custom") }}</el-option>
            </el-select>
          </li>
          <li>
            <el-select v-model="form.customPasswordExpire">
              <el-option value="null" :label="t('userManagement.UserDefault')">{{ t("userManagement.UserDefault") }}</el-option>
              <!-- <el-option :value="true" label="启用">启用</el-option> -->
              <el-option value="CUSTOM" :label="t('userManagement.Custom')">{{ t("userManagement.Custom") }}</el-option>
            </el-select>
          </li>
          <li>
            <el-select v-model="form.histPasswordLimitConfigType">
              <el-option value="DEFAULT" :label="t('userManagement.UserDefault')">{{ t("userManagement.UserDefault") }}</el-option>
              <el-option value="CUSTOM" :label="t('userManagement.Custom')">{{ t("userManagement.Custom") }}</el-option>
              <el-option value="DISABLE" :label="t('userManagement.Disabled')">{{ t("userManagement.Disabled") }}</el-option>
            </el-select>
          </li>
          <li>
            <el-select v-model="form.customPasswordExpireDays">
              <el-option value="null" :label="t('userManagement.Default')">{{ t("userManagement.Default") }}</el-option>
              <el-option value="CUSTOM" :label="t('userManagement.Custom')">{{ t("userManagement.Custom") }}</el-option>
            </el-select>
          </li>
          <li>
            <el-select v-model="form.accountExpirationDateConfigType">
              <el-option v-if="!isTenant" value="DEFAULT" :label="t('userManagement.UserDefault')">{{ t("userManagement.UserDefault") }}</el-option>
              <el-option value="DISABLE" :label="t('userManagement.Disabled')">{{ t("userManagement.Disabled") }}</el-option>
              <el-option value="CUSTOM" :label="t('userManagement.Custom')">{{ t("userManagement.Custom") }}</el-option>
            </el-select>
          </li>
        </ul>
        <ul>
          <li>{{ t("userManagement.Custom") }}</li>
          <li>
            <!-- 最小密码长度 -->
            <!-- <el-switch v-if="form.minLengthConfigType === 'DISABLE'" v-model="custom.isDisabledMin" /> -->

            <el-input
              v-if="form.minLengthConfigType === 'CUSTOM'"
              v-model="custom.customMinLength"
              @keyup="
                custom.customMinLength = custom.customMinLength.replace(/[^0-9]/g, '');
                if (custom.customMinLength > 16) {
                  custom.customMinLength = 16;
                }
                if (String(custom.customMinLength) === '0') {
                  custom.customMinLength = 1;
                }
              "
            ></el-input>
          </li>
          <li>
            <!-- 强化密码复杂度 -->
            <el-switch v-if="form.customHardening === 'CUSTOM'" v-model="custom.customHardening" />
          </li>
          <li>
            <!-- 用户可以更改密码 -->
            <el-switch v-if="form.customAllowUserChange === 'CUSTOM'" v-model="custom.customAllowUserChange" />
          </li>
          <li>
            <!-- 密码过期 -->
            <el-switch v-if="form.customPasswordExpire === 'CUSTOM'" v-model="custom.customPasswordExpire" />
          </li>
          <li>
            <!-- <el-switch v-if="form.histPasswordLimitConfigType === 'DISABLE'" v-model="custom.isHistPasswordLimit" /> -->
            <el-input
              v-if="form.histPasswordLimitConfigType === 'CUSTOM'"
              v-model="custom.customHistPasswordLimit"
              @keyup="
                custom.customHistPasswordLimit = custom.customHistPasswordLimit.replace(/[^0-9]/g, '');
                if (custom.customHistPasswordLimit > 16) {
                  custom.customHistPasswordLimit = 16;
                }
                if (String(custom.customHistPasswordLimit) === '0') {
                  custom.customHistPasswordLimit = 1;
                }
              "
            ></el-input>
          </li>
          <li>
            <el-input
              v-if="form.customPasswordExpireDays === 'CUSTOM'"
              v-model="custom.customPasswordExpireDays"
              @keyup="
                custom.customPasswordExpireDays = custom.customPasswordExpireDays.replace(/[^0-9]/g, '');
                if (custom.customPasswordExpireDays > 1000) {
                  custom.customPasswordExpireDays = 1000;
                }
                if (String(custom.customPasswordExpireDays) === '0') {
                  custom.customPasswordExpireDays = 1;
                }
              "
            ></el-input>
          </li>
          <li>
            <!-- <el-date-picker v-if="form.accountExpirationDateConfigType === 'CUSTOM'" v-model="custom.accountExpirationDate" type="date" placeholder="有效期至" value-format="x" :disabled-date="(time: Date) => time.getTime() < Date.now()" /> -->

            <el-input-number v-if="form.accountExpirationDateConfigType === 'CUSTOM'" v-model="custom.accountExpirationDate" :min="1" :max="1000" />
          </li>
        </ul>
      </div>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()">{{ t("glob.Cancel") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur>{{ t("glob.Confirm") }}</el-button> -->
      <!-- {{ userId }} -->
      <el-button type="primary" v-if="userId != null && userId != ''" :disabled="!userInfo.hasPermission(PERMISSION.user.auth598673031702777856)" @click="handleFinish()" v-blur>{{ t("glob.Confirm") }}</el-button>
      <el-button type="primary" v-else :disabled="!userInfo.hasPermission(PERMISSION.user.auth598672954271731712)" @click="handleFinish()" v-blur>{{ t("glob.Confirm") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </el-dialog>
  <el-dialog v-model="innerVisible" :title="title" append-to-body>
    <p>{{ t("userManagement.The User Account Policy applies only to a specific user") }}</p>
    <p>{{ t("userManagement.Any of the customer default policy settings may be changed for the user") }}</p>

    <h2>
      <strong>{{ t("userManagement.Minimum password length") }}</strong>
    </h2>

    <p>{{ t("userManagement.The minimum allowed length of a password") }}</p>

    <h2>
      <strong>{{ t("userManagement.Enforce password complexity") }}</strong>
    </h2>

    <p>{{ t("userManagement.Enforce password complexity tip") }}</p>

    <h2>
      <strong>{{ t("userManagement.Password Expiration") }}</strong>
    </h2>

    <p>{{ t("userManagement.After activation, the system will force users to change their passwords after a period of time") }}</p>

    <h2>
      <strong>{{ t("userManagement.Password history") }}</strong>
    </h2>

    <p>{{ t("userManagement.The number of new passwords that users must use before reusing old passwords to prevent users from reusing old passwords when changing them。") }}</p>

    <h2>
      <strong>{{ t("userManagement.Maximum password usage period") }}</strong>
    </h2>

    <p>{{ t("userManagement.The number of days after which the system will force users to change their password") }}</p>
  </el-dialog>
</template>
<script setup lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h } from "vue";
import { ElMessage, ElMessageBox, ElText } from "element-plus";
import { useI18n } from "vue-i18n";
import { QuestionFilled, Select } from "@element-plus/icons-vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";
import { getTenantPasswordStrategy, saveTenantPasswordStrategy, getUserPasswordStrategy, saveUserPasswordStrategy } from "@/api/personnel";
import { ElLoading } from "element-plus";
import getUserInfo from "@/utils/getUserInfo";
import { faBan } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const userInfo = getUserInfo();
const { t } = useI18n();
const ctx = getCurrentInstance();
if (!ctx) throw new Error("Component context initialization failed!");
const { appContext } = ctx;

const ComponentRender = computed(() => {
  return EditorDialog;
});

const emits = defineEmits(["refresh"]);

const title = ref("");
const visible = ref(false);
const innerVisible = ref(false);
const isTenant = ref(false);
const form = ref({
  defaultMinLength: 0, //默认最小密码长度
  customMinLength: 0, //自定义最小密码长度
  minLengthConfigType: "DISABLE", //最小密码长度配置类型
  defaultHardening: true, //默认强化密码复杂度
  customHardening: null, //自定义强化密码复杂度
  defaultAllowUserChange: true, //默认用户可以更改密码
  customAllowUserChange: null, //自定义用户可以更改密码
  defaultHistPasswordLimit: 0, //默认密码历史
  customHistPasswordLimit: 0, //自定义密码历史
  histPasswordLimitConfigType: "DEFAULT", //密码历史配置类型
  defaultPasswordExpire: false, //默认密码过期
  customPasswordExpire: null, //自定义密码过期
  defaultPasswordExpireDays: 0, //默认密码最长使用期限
  customPasswordExpireDays: null, //自定义密码最长使用期限
});
const custom = ref({
  customMinLength: "",
  isDisabledMin: true,
  customHardening: true,
  customAllowUserChange: true,
  customPasswordExpire: true,
  customHistPasswordLimit: "",
  isHistPasswordLimit: true,
  customPasswordExpireDays: null,
  histPasswordLimit: "",
  isPasswordExpireDays: true,
});
const backstageObj = ref({
  customHardening: null, //强化密码复杂度
  customAllowUserChange: null, //用户可以更改密码
  customPasswordExpire: null, //密码过期
  customPasswordExpireDays: null, //密码最长使用天数
  defaultHistPasswordLimit: null, // 默认密码历史
  customHistPasswordLimit: null, //自定义密码历史
  defaultMinLength: null, // 最小密码长度
  customMinLength: null, //最小密码长度
  histPasswordLimitConfigType: "DEFAULT", //历史类型
  minLengthConfigType: "DEFAULT", //长度类型
  defaultHardening: true,
  defaultAllowUserChange: true,
  defaultPasswordExpire: false,

  defaultAccountExpirationDate: 0, //默认账号有效期
  defaultAccountExpirationDateConfigType: "DISABLE",
});
function handleCancel() {
  custom.value = {
    customMinLength: "",
    isDisabledMin: true,
    customHardening: true,
    customAllowUserChange: true,
    customPasswordExpire: true,
    customHistPasswordLimit: "",
    isHistPasswordLimit: true,
    customPasswordExpireDays: null,
    histPasswordLimit: "",
    isPasswordExpireDays: true,
  };
  visible.value = false;
}

const userId = ref(null);
function open(data) {
  if (data) {
    if (data.type === "tenant") {
      title.value = t("userManagement.CustomerPolicy");
      isTenant.value = true;
    } else {
      title.value = t("userManagement.User Password Policy");
      isTenant.value = false;
    }
    getData(data);
    userId.value = data.id;
  }
}

function oepnMessage() {
  innerVisible.value = true;
}

function getData(data) {
  const loading = ElLoading.service({
    target: ".loading-class",
  });

  if (data.type === "tenant") {
    getTenantPasswordStrategy({}).then((res) => {
      if (res.success) {
        form.value = { ...res.data };
        form.value.customHardening = res.data.customHardening != null ? "CUSTOM" : "null";
        form.value.customAllowUserChange = res.data.customAllowUserChange != null ? "CUSTOM" : "null";
        form.value.customPasswordExpire = res.data.customPasswordExpire != null ? "CUSTOM" : "null";
        custom.value.customHardening = res.data.customHardening;
        custom.value.customAllowUserChange = res.data.customAllowUserChange;
        custom.value.customPasswordExpire = res.data.customPasswordExpire;
        custom.value.customMinLength = res.data.customMinLength;
        form.value.customPasswordExpireDays = res.data.customPasswordExpireDays != null ? "CUSTOM" : "null";
        custom.value.customPasswordExpireDays = res.data.customPasswordExpireDays;
        custom.value.customHistPasswordLimit = res.data.customHistPasswordLimit;

        form.value.accountExpirationDateConfigType = res.data.accountExpirationDateConfigType;
        custom.value.accountExpirationDate = Number(res.data.customAccountExpirationDate) || 365;

        backstageObj.value.customHardening = res.data.customHardening;
        backstageObj.value.customAllowUserChange = res.data.customAllowUserChange;
        backstageObj.value.customPasswordExpire = res.data.customPasswordExpire;
        backstageObj.value.customPasswordExpireDays = res.data.customPasswordExpireDays;
        backstageObj.value.defaultHistPasswordLimit = res.data.defaultHistPasswordLimit;
        backstageObj.value.customHistPasswordLimit = res.data.customHistPasswordLimit;
        backstageObj.value.defaultMinLength = res.data.defaultMinLength;
        backstageObj.value.customMinLength = res.data.customMinLength;
        backstageObj.value.histPasswordLimitConfigType = res.data.histPasswordLimitConfigType;
        backstageObj.value.minLengthConfigType = res.data.minLengthConfigType;
        backstageObj.value.defaultHardening = res.data.defaultHardening;
        backstageObj.value.defaultAllowUserChange = res.data.defaultAllowUserChange;
        backstageObj.value.defaultPasswordExpire = res.data.defaultPasswordExpire;
      }
    });
  } else {
    getUserPasswordStrategy({ userId: data.id }).then((res) => {
      if (res.success) {
        form.value = { ...res.data };
        form.value.customHardening = res.data.customHardening != null ? "CUSTOM" : "null";
        form.value.customAllowUserChange = res.data.customAllowUserChange != null ? "CUSTOM" : "null";
        form.value.customPasswordExpire = res.data.customPasswordExpire != null ? "CUSTOM" : "null";
        custom.value.customHardening = res.data.customHardening;
        custom.value.customAllowUserChange = res.data.customAllowUserChange;
        custom.value.customPasswordExpire = res.data.customPasswordExpire;
        custom.value.customMinLength = res.data.customMinLength;
        form.value.customPasswordExpireDays = res.data.customPasswordExpireDays != null ? "CUSTOM" : "null";
        custom.value.customPasswordExpireDays = res.data.customPasswordExpireDays;
        custom.value.customHistPasswordLimit = res.data.customHistPasswordLimit;

        form.value.accountExpirationDateConfigType = res.data.accountExpirationDateConfigType;
        custom.value.accountExpirationDate = Number(res.data.customAccountExpirationDate) || 365;

        backstageObj.value.customHardening = res.data.customHardening;
        backstageObj.value.customAllowUserChange = res.data.customAllowUserChange;
        backstageObj.value.customPasswordExpire = res.data.customPasswordExpire;
        backstageObj.value.customPasswordExpireDays = res.data.customPasswordExpireDays;
        backstageObj.value.defaultHistPasswordLimit = res.data.defaultHistPasswordLimit;
        backstageObj.value.customHistPasswordLimit = res.data.customHistPasswordLimit;
        backstageObj.value.defaultMinLength = res.data.defaultMinLength;
        backstageObj.value.customMinLength = res.data.customMinLength;
        backstageObj.value.histPasswordLimitConfigType = res.data.histPasswordLimitConfigType;
        backstageObj.value.minLengthConfigType = res.data.minLengthConfigType;
        backstageObj.value.defaultHardening = res.data.defaultHardening;
        backstageObj.value.defaultAllowUserChange = res.data.defaultAllowUserChange;
        backstageObj.value.defaultPasswordExpire = res.data.defaultPasswordExpire;

        backstageObj.value.defaultAccountExpirationDate = res.data.defaultAccountExpirationDate;
        backstageObj.value.defaultAccountExpirationDateConfigType = res.data.defaultAccountExpirationDateConfigType;
      }
    });
  }
  setTimeout(() => {
    loading.close();
  }, 1000);
  visible.value = true;
}
const computeIconVisibility = (defaultValue, customValue) => {
  // 处理都为false
  if (defaultValue === false && customValue === false) {
    return true;
  }
  if (defaultValue === true) {
    if (customValue === null) {
      return false;
    } else if (customValue === false) {
      return true;
    }
  } else if (defaultValue === false) {
    if (customValue === null) {
      return true;
    }
    if (customValue === true) {
      return false;
    }
  }
  return false;
};

const Showhardeningicon = () => {
  return computeIconVisibility(backstageObj.value.defaultHardening, backstageObj.value.customHardening);
};

const ShowAllowUserChangeicon = () => {
  return computeIconVisibility(backstageObj.value.defaultAllowUserChange, backstageObj.value.customAllowUserChange);
};
const ShowPasswordExpireicon = () => {
  return computeIconVisibility(backstageObj.value.defaultPasswordExpire, backstageObj.value.customPasswordExpire);
};
function handleFinish() {
  // console.log(form.value);
  // custom.value.customHardening
  let obj = {
    minLength: form.value.minLengthConfigType === "CUSTOM" ? custom.value.customMinLength : undefined,
    minLengthConfigType: form.value.minLengthConfigType,
    hardening: form.value.customHardening != "null" ? custom.value.customHardening : "null",
    allowUserChange: form.value.customAllowUserChange != null ? custom.value.customAllowUserChange : "null",
    passwordExpire: form.value.customPasswordExpire != null ? custom.value.customPasswordExpire : "null",

    histPasswordLimit: form.value.histPasswordLimitConfigType === "CUSTOM" ? custom.value.customHistPasswordLimit : undefined,
    histPasswordLimitConfigType: form.value.histPasswordLimitConfigType,
    // passwordExpireDays: custom.value.customPasswordExpire == false ? "null" : custom.value.customPasswordExpireDays,
    passwordExpireDays: form.value.customPasswordExpireDays === "CUSTOM" ? custom.value.customPasswordExpireDays : null,
    accountExpirationDateConfigType: form.value.accountExpirationDateConfigType,
    accountExpirationDate: form.value.accountExpirationDateConfigType === "CUSTOM" ? custom.value.accountExpirationDate : null,
  };
  if (isTenant.value) {
    saveTenantPasswordStrategy(obj)
      .then((res) => {
        if (res.success) {
          ElMessage.success(t("axios.Operation successful"));
          handleCancel();
          emits("refresh");
        }
      })
      .catch((err) => {
        ElMessage.error(err.message);
      });
  } else {
    saveUserPasswordStrategy({ ...obj, userId: userId.value })
      .then((res) => {
        if (res.success) {
          ElMessage.success(t("axios.Operation successful"));
          handleCancel();
          emits("refresh");
        }
      })
      .catch((err) => {
        ElMessage.error(err.message);
      });
  }
}
defineExpose({
  close: handleCancel,
  open,
});
</script>
<style lang="scss" scoped>
.main {
  width: 100%;
  display: flex;
  > ul {
    flex: 1;
    > li {
      height: 50px;
      padding: 0px 10px;
      box-sizing: border-box;
      color: #000;
      // text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      border-top: 1px solid #ebeef5;
    }
    > li:first-child {
      background-color: #f2f4f5;
    }
    border-right: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
  }
  > ul:first-child {
    border-left: 1px solid #ebeef5;
    > li {
      justify-content: flex-start;
    }
  }
}
h2 {
  font-size: 16px;
  font-weight: bold;
  padding: 5px 0;
  box-sizing: border-box;
  color: #000;
}
</style>
