<template>
  <el-row :gutter="20">
    <el-col :span="6">
      <el-card :body-style="{ height: `${height}px` }">
        <div class="tw-mb-4">
          <span class="tw-text-xl tw-font-semibold">选择报告</span>
        </div>

        <el-collapse v-model="activeNames" accordion>
          <el-collapse-item v-for="collapseItem in reportMenu" :key="collapseItem.name" :name="collapseItem.name">
            <template #title> {{ collapseItem.title }} </template>
            <div>
              <el-scrollbar :max-height="`${height - 60 - 38 - 48 * reportMenu.length}px`">
                <div class="tw-cursor-pointer tw-border tw-border-x-0 tw-border-b-0 tw-leading-[30px]" v-for="report in collapseItem.children" :key="`${collapseItem.name}-${report.name}`" @click="handleChangeReport(report.name)">
                  <p v-if="report.title">
                    <el-text :type="report.name === currentReport ? 'primary' : ''">{{ report.title }}</el-text>
                  </p>
                  <p v-if="report.cnTitle">
                    <el-text :type="report.name === currentReport ? 'primary' : ''">{{ report.cnTitle }}</el-text>
                  </p>
                </div>
              </el-scrollbar>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-card>
    </el-col>
    <el-col :span="18">
      <el-card :body-style="{ height: `${height}px` }" v-loading="loading">
        <component v-if="!loading && currentReport" :is="currentRecord.component" :record="currentRecord"></component>
        <div v-else class="tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center">
          <el-empty description="请选择运营报告" />
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import { ref, inject, computed, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";

import userLoginRecords from "./model/userLoginRecords.vue";
import GeneralTemplate from "./model/GeneralTemplate.vue";
import TicketsClosedin24Hours from "./model/TicketsClosedin24Hours.vue";
import CustomerTicketCount from "./model/CustomerTicketCount.vue";
import TicketsDetailsStatistics from "./model/TicketsDetailsStatistics.vue";
import qrcodeDetail from "./model/qrcodeDetail.vue";
import PersonnelWorkioadStatistics from "./model/PersonnelWorkioadStatistics.vue";

// import dictEvent from "./model/dictEvent.vue";
// import dictService from "./model/dictService.vue";
import TicketsCreatedByHourly from "./model/TicketsCreatedByHourly.vue";
import TicketsPriorityAnalysis from "./model/TicketsPriorityAnalysis.vue";
import TicketResolutionStatistics from "./model/TicketResolutionStatistics.vue";
import TicketClosureCodeStatistics from "./model/TicketClosureCodeStatistics.vue";
import TicketsClosedByUsers from "./model/TicketsClosedByUsers.vue";
import TicketsCreatedByUsers from "./model/TicketsCreatedByUsers.vue";
import DeviceAlerts from "./model/DeviceAlerts.vue";
import AllWorkOrderStatistics from "./model/AllWorkOrderStatistics.vue";
import EventResponseStatistics from "./model/EventResponseStatistics.vue";

import { /* 报表管理中心_运营报告_服务台相关报告, 报表管理中心_运营报告_DICT工单报告, 报表管理中心_运营报告_系统相关报告, */ 报表管理中心_客户_运营报告 } from "@/views/pages/permission";

import getUserInfo from "@/utils/getUserInfo";

const router = useRouter();
const route = useRoute();

const userInfo = getUserInfo();

const width = inject("width", ref(0));
const height = inject("height", ref(0));

const reportMenu = ref<Record<string, any>[]>(
  [
    {
      name: "informationDeskRelatedReports",
      title: "服务台相关报告",
      children: [
        { component: GeneralTemplate, title: "Breached Incident Ticket", cnTitle: "超时工单统计", name: "BreachedIncidentTicket", permissions: { view: userInfo.hasPermission(报表管理中心_客户_运营报告), export: userInfo.hasPermission(报表管理中心_客户_运营报告) } },
        { component: GeneralTemplate, title: "Tickets Closed in 24 Hours", cnTitle: "24小时内关闭工单统计", name: "TicketsClosedin24Hours", permissions: { view: userInfo.hasPermission(报表管理中心_客户_运营报告), export: userInfo.hasPermission(报表管理中心_客户_运营报告) } },
        { component: GeneralTemplate, title: "Customer Ticket Count", cnTitle: "客户工单统计", name: "CustomerTicketCount", permissions: { view: userInfo.hasPermission(报表管理中心_客户_运营报告), export: userInfo.hasPermission(报表管理中心_客户_运营报告) } },
        /* */
        // { component: TicketsCreatedByHourly, title: "Tickets Created by Hourly", cnTitle: "每小时创建的工单统计", name: "TicketsCreatedByHourly", permissions: { view: userInfo.hasPermission(报表管理中心_客户_运营报告), export: userInfo.hasPermission(报表管理中心_客户_运营报告) } },
        // { component: TicketsPriorityAnalysis, title: "Tickets Priority Analysis", cnTitle: "工单优先级统计", name: "TicketsPriorityAnalysis", permissions: { view: userInfo.hasPermission(报表管理中心_客户_运营报告), export: userInfo.hasPermission(报表管理中心_客户_运营报告) } },
        // { component: TicketResolutionStatistics, title: "Ticket Resolution Statistics", cnTitle: "工单解决方案统计信息", name: "TicketResolutionStatistics", permissions: { view: userInfo.hasPermission(报表管理中心_客户_运营报告), export: userInfo.hasPermission(报表管理中心_客户_运营报告) } },
        { component: GeneralTemplate, title: "Ticket ClosureCode Statistics", cnTitle: "工单关闭代码统计", name: "TicketClosureCodeStatistics", permissions: { view: userInfo.hasPermission(报表管理中心_客户_运营报告), export: userInfo.hasPermission(报表管理中心_客户_运营报告) } },
        // // /* */
        { component: GeneralTemplate, title: "Tickets Closed by Users", cnTitle: "人员关单数统计", name: "TicketsClosedByUsers", permissions: { view: userInfo.hasPermission(报表管理中心_客户_运营报告), export: userInfo.hasPermission(报表管理中心_客户_运营报告) } },
        { component: GeneralTemplate, title: "Tickets Created by Users", cnTitle: "人员开单数统计", name: "TicketsCreatedByUsers", permissions: { view: userInfo.hasPermission(报表管理中心_客户_运营报告), export: userInfo.hasPermission(报表管理中心_客户_运营报告) } },
        { component: GeneralTemplate, title: "Tickets Details Statistics", cnTitle: "工单详情统计", name: "TicketsDetailsStatistics", permissions: { view: userInfo.hasPermission(报表管理中心_客户_运营报告), export: userInfo.hasPermission(报表管理中心_客户_运营报告) } },
        { component: qrcodeDetail, title: "QR Code Fault Reports", cnTitle: "二维码报障单详情", name: "qrcodeDetail", permissions: { view: userInfo.hasPermission(报表管理中心_客户_运营报告), export: userInfo.hasPermission(报表管理中心_客户_运营报告) } },
        { component: GeneralTemplate, title: "Personnel Workioad Statistics", cnTitle: "人员工作量统计", name: "PersonnelWorkioadStatistics", permissions: { view: userInfo.hasPermission(报表管理中心_客户_运营报告), export: userInfo.hasPermission(报表管理中心_客户_运营报告) } },
        // { component: DeviceAlerts, title: "Device Alerts", cnTitle: "设备告警数", name: "DeviceAlerts", permissions: { view: userInfo.hasPermission(报表管理中心_运营报告_服务台相关报告), export: userInfo.hasPermission(报表管理中心_运营报告_服务台相关报告) } },

        /* 未联调 */
        // { component: AllWorkOrderStatistics, title: "", cnTitle: "全部工单统计", name: "AllWorkOrderStatistics", permissions: { view: userInfo.hasPermission(报表管理中心_运营报告_服务台相关报告), export: userInfo.hasPermission(报表管理中心_运营报告_服务台相关报告) } },
        // { component: EventResponseStatistics, title: "", cnTitle: "事件响应统计", name: "EventResponseStatistics", permissions: { view: userInfo.hasPermission(报表管理中心_运营报告_服务台相关报告), export: userInfo.hasPermission(报表管理中心_运营报告_服务台相关报告) } },
      ].filter((v) => v.permissions.view),
    },
    // {
    //   name: "dictWorkOrderReport",
    //   title: "DICT工单报告",
    //   children: [
    //     /* */
    //     // { component: dictEvent, title: "", cnTitle: "DICT事件管理详单统计", name: "dictEvent", permissions: { view: userInfo.hasPermission(报表管理中心_运营报告_DICT工单报告), export: userInfo.hasPermission(报表管理中心_运营报告_DICT工单报告) } },
    //     // { component: dictService, title: "", cnTitle: "DICT服务请求详单统计", name: "dictService", permissions: { view: userInfo.hasPermission(报表管理中心_运营报告_DICT工单报告), export: userInfo.hasPermission(报表管理中心_运营报告_DICT工单报告) } },
    //   ].filter((v) => v.permissions.view),
    // },
    {
      name: "systemRelatedReport",
      title: "系统相关报告",
      children: [
        { component: userLoginRecords, title: "User Log-in Records", cnTitle: "用户登录详情", name: "UserLogInRecords", permissions: { view: userInfo.hasPermission(报表管理中心_客户_运营报告), export: userInfo.hasPermission(报表管理中心_客户_运营报告) } },
        { component: userLoginRecords, title: "User Last Log-in Records", cnTitle: "用户最新一次登录记录", name: "UserLastLogInRecords", permissions: { view: userInfo.hasPermission(报表管理中心_客户_运营报告), export: userInfo.hasPermission(报表管理中心_客户_运营报告) } },
      ].filter((v) => v.permissions.view),
    },
  ].filter((v) => !!v.children.map((c) => c.permissions.view).length)
);

const activeNames = ref();

const currentReport = ref<string>((route.query.report as string) || "");

const loading = ref<boolean>(false);

const currentRecord = computed(() => {
  for (let i = 0; i < reportMenu.value.length; i++) {
    const record = reportMenu.value[i].children.find((v) => v.name === currentReport.value);
    if (record) return record;
  }
  return {};
});

async function handleChangeReport(name) {
  loading.value = true;

  currentReport.value = name;

  router.replace({ query: Object.assign({ ...route.query }, { report: name }) });

  await nextTick();

  loading.value = false;
}
</script>
