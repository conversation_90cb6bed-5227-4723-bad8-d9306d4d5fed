<template>
  <div>
    <div class="tw-border-b">
      <span class="tw-border-l-8 tw-border-[var(--el-color-primary)] tw-pl-2 tw-text-lg tw-font-semibold">{{ props.record.title }}</span>
    </div>

    <div class="tw-mt-4">
      <el-form :model="searchForm" :inline="true">
        <el-form-item :label="`开始时间`">
          <el-date-picker v-model="searchForm.start" type="datetime" placeholder="请选择开始时间" value-format="x" :clearable="false" />
        </el-form-item>
        <el-form-item :label="`结束时间`">
          <el-date-picker v-model="searchForm.end" type="datetime" placeholder="请选择结束时间" value-format="x" :clearable="false" />
        </el-form-item>
        <el-form-item :label="`重要性`">
          <el-select v-model="searchForm.priority" collapse-tags style="width: 150px">
            <el-option v-for="priority in priorityOption" :key="`priority-${priority.value}`" :value="priority.value" :label="`${priority.label}${priority.label === 'P1' ? '' : '以上'}`"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button :icon="Refresh"></el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Download" :loading="downloadLoading" @click="handleExport" v-if="(props.record.permissions || {}).export || false">下载</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

import { Refresh, Download } from "@element-plus/icons-vue";

import { exportTicketsCreatedByUsers } from "@/views/pages/apis/operationReport";
import { ElMessage } from "element-plus";
import { priorityOption } from "@/views/pages/apis/event";

import { 系统管理中心_客户管理_可读 } from "@/views/pages/permission";

interface Props {
  record: any;
}
const props = withDefaults(defineProps<Props>(), {
  record: {},
});

const downloadLoading = ref<boolean>(false);

var date = new Date();
date.setDate(1);
var dateStart = date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate();
const searchForm = ref<Record<string, any>>({
  start: new Date(dateStart).getTime(),
  end: new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59).getTime(),
  priority: (priorityOption.find((v) => v) || {}).value,
});

async function handleExport() {
  try {
    if (new Date(searchForm.value.start) >= new Date(searchForm.value.end)) return ElMessage.error("结束时间不能小于等于开始时间");

    downloadLoading.value = true;

    const api = {
      TicketsCreatedByUsers: exportTicketsCreatedByUsers,
    }[props.record.name];
    // eslint-disable-next-line no-console
    if (!api) return console.warn("没有匹配导出接口"); // 新增导出接口要添加到上面api对象中匹配根据index.vue设置的name值
    const priority: string[] = [];
    if (searchForm.value.priority)
      for (let i = 0; i < priorityOption.findIndex((v) => v.value === searchForm.value.priority) + 1; i++) {
        priority.push(priorityOption[i].value as string);
      }
    const { data, message, success } = await api({ startTime: searchForm.value.start, endTime: searchForm.value.end, priority: priority.join(), tenantPermissionId: 系统管理中心_客户管理_可读 });
    if (!success) throw new Error(message);
    const href = URL.createObjectURL(data);
    const link = document.createElement("a");
    link.download = `${props.record.title}.xlsx`;
    link.href = href;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(href); // 释放内存
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    downloadLoading.value = false;
  }
}
</script>
