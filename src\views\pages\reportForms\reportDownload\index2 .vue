<template>
  <el-card>
    <el-scrollbar>
      <div class="flex-search" :style="{ minWidth: `${width - 42}px`, padding: '0px' }">
        <div class="left"></div>
        <div class="center"></div>
        <!-- <div class="right">
          <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group582103048025800704.create)">
            <span>
              <el-button type="primary" :disabled="!userInfo.hasPermission(PERMISSION.group582103048025800704.create)" :icon="Plus" @click="createItem({}).finally(() => handleStateRefresh())">添加客户</el-button>
            </span>
          </el-tooltip>
        </div> -->
      </div>
      <div class="task-main">
        <div class="task-main-left">
          <h3>模板列表</h3>
          <el-select v-model="reporType" placeholder="">
            <!-- <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option> -->
            <el-option label="月报" value="MONTH">月报</el-option>
            <!-- <el-option label="周报" value="WEEK">周报</el-option> -->
            <!-- <el-option label="自定义" value="CUSTOM">自定义</el-option> -->
          </el-select>
          <el-menu :default-active="tenantId" class="el-menu-vertical-demo">
            <el-menu-item v-for="item in userInfo.tenants" :key="item.tenantId" :index="item.tenantId" @click="tenantChange(item)">
              <span>{{ item.tenantName }}</span>
            </el-menu-item>
          </el-menu>
        </div>
        <div class="task-main-table">
          <div style="margin-bottom: 15px; display: flex">
            <el-select v-model="template" style="margin-right: 15px" clearable @change="templateChange($event)">
              <el-option value="CUSTOMER_MONTH" label="客户报告"> </el-option>
              <el-option value="OPERATING_STATEMENT" label="运营报告"> </el-option>
            </el-select>
            <el-select v-model="reportType" v-if="template !== 'OPERATING_STATEMENT'" style="width: 300px" clearable>
              <el-option value="MONTH" label="月报"> </el-option>
              <!-- <el-option value="OPERATING_STATEMENT" label="运营报告"> </el-option> -->
            </el-select>
            <el-date-picker v-else :model-value="startEndTime" @update:model-value="($event) => handleChangeTime($event)" type="datetimerange" range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间" />
            <el-button type="primary" style="margin-left: 15px" @click="handleStateRefresh()"> 搜索 </el-button>
          </div>
          <el-table v-loading="state.loading" :data="state.data" :height="height - 104 - 20 - (state.total ? 32 : 0)" :style="{ width: `${width}px`, margin: '0 auto' }">
            <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :min-width="column.width || 120" :="column.showOverflowTooltip" :formatter="column.formatter" />
            <el-table-column label="操作" prop="name">
              <template #default="{ row }">
                <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group582102647406854144.auth583556637252386816)">
                  <span class="el-button is-link">
                    <el-button v-if="!generateReportStatus && !reportdownloaded && !generateloading" link type="primary" size="small" @click="generateReport" style="font-size: 14px"> 生成报告 </el-button>
                    <el-button v-if="generateReportStatus && !reportdownloaded && !generateloading" link type="primary" size="small" @click="downloadReport" style="font-size: 14px"> 下载报告 </el-button>
                    <el-button v-if="generateloading" link type="primary" size="small" @click="cancelReport" style="font-size: 14px"> 取消生成 </el-button>
                  </span>
                </el-tooltip>
                <!-- <el-button link type="danger" size="small" :icon="Delete"></el-button> -->
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-scrollbar>
    <div :style="{ margin: '2px 20px 20px' }">
      <el-pagination v-show="state.total" v-model:current-page="state.page" v-model:page-size="state.size" :page-sizes="state.sizes" :total="state.total" layout="->, total, sizes, prev, pager, next, jumper" @size-change="handleStateRefresh()" @current-change="handleStateRefresh()" />
    </div>
  </el-card>
</template>

<script setup lang="ts" generic="T extends object">
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete, Connection, More, Refresh, Lock, Unlock, InfoFilled, EditPen, Setting } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox, ElTag } from "element-plus";

import type { TableColumnCtx } from "element-plus";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";
import { useSiteConfig } from "@/stores/siteConfig";
import getUserInfo from "@/utils/getUserInfo";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
import { getReportList as getData, addReport as addData, editReport as setData, type Reports as DataItem, reportStateOption, reportModelOption, downloadTenantReportFileXlsx, downloadTenantReportFile } from "@/views/pages/apis/reports";

import { sizes } from "@/utils/common";

import { Zone } from "@/utils/zone";
import { bindFormBox } from "@/utils/bindFormBox";
import { timeFormat } from "@/utils/date";
import { EditorType } from "@/views/common/interface";

// import EditorForm from "./EditForm.vue";

const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
// const i18n = useI18n({ useScope: "local" });
const { t } = useI18n();

const route = useRoute();
const router = useRouter();
const userInfo = getUserInfo();
defineOptions({ name: "TenantGovern" });
const tenantId = ref(userInfo.tenants[0].tenantId);
const siteConfig = useSiteConfig();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));
const template = ref("");
const reportType = ref("");
const reporType = ref("MONTH");
const generateReportStatus = ref(false);
const reportdownloaded = ref(false);
const generateloading = ref(false);
const continueGenerate = ref(true); // 控制生成报告逻辑是否继续执行
// let generateTimer: number; // 生成报告定时器

const startEndTime = ref<[string | number, string | number]>(["", ""]);

const operateData = ref([
  {
    id: "599839269141348352",
    tenantId: "509621665546633216",
    tenantIds: ["509621665546633216"],
    name: "运营报告",
    type: "MONTH",
    template: "OPERATING_STATEMENT",
    enable: true,
    startTime: "15:33:34",
    endTime: "15:33:34",
    createdBy: '{"userId":509621665794097152,"username":"宋志宗1"}',
    updatedBy: '{"userId":509621665794097152,"username":"宋志宗1"}',
    createdTime: "1710488024129",
    updatedTime: "1710752299974",
  },
]);

// 测试

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: TableColumnCtx<DataItem>, value: T[keyof T]) => string | import("vue").VNode; showOverflowTooltip?: boolean }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<DataItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  column: [
    { key: "name", label: "报表名称", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? String(v) : "--") },
    { key: "type", label: "报表类型", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? reportStateOption.map((item) => (item.value == v ? item.label : "")) : "--") },
    // { key: "baileeTenantId", label: "是否托管", showOverflowTooltip: true, formatter: (_row, _col, v) => h(ElTag, { type: v ? "success" : "" }, () => (v ? t("glob.Yes") : t("glob.Not"))) },
    // { key: "baileeTenantName", label: "受托客户", showOverflowTooltip: true, formatter: (_row, _col, v) => (_row.baileeTenantId ? `${v || "--"}（${_row.baileeTenantAbbreviation || "--"}）` : "") },
    // { key: "zoneId", label: "时区", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? Zone[v as keyof typeof Zone] : "--") },
    { key: "template", label: "报表模板", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? reportModelOption.map((item) => (item.value == v ? item.label : "")) : "--") },

    { key: "enable", label: "状态", showOverflowTooltip: true, formatter: (_row, _col, v) => (v == false ? h(ElTag, { type: "danger" }, () => "禁用") : h(ElTag, { type: "success" }, () => "启用")) },
  ],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

const publicParams = computed<Record<string, unknown>>(() => ({}));

const isActive = ref(false);

async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  let controller = new AbortController();
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  let params1;
  if (template.value != "OPERATING_STATEMENT") {
    params1 = { tenantId: tenantId.value, type: reportType.value, template: template.value };
  } else {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    params1 = { tenantId: tenantId.value, template: template.value, start: startEndTime.value[0], end: startEndTime.value[1] };
  }
  try {
    const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await getData({ ...params1, paging: { pageNumber: state.page, pageSize: state.size } });
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);
      if (template.value !== "OPERATING_STATEMENT") {
        return data instanceof Array ? data : [];
      } else {
        return operateData.value;
      }
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(20);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}
function templateChange(status) {
  startEndTime.value = ["", ""];
}
function tenantChange(item: any) {
  tenantId.value = item.tenantId;
  // tenantIdList.value = item.tenantIds;
  handleStateRefresh();
}
async function handleChangeTime(v: Date[]) {
  try {
    startEndTime.value = [v[0].getTime(), v[1].getTime()];
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  }
}
function generateReport(item) {
  generateloading.value = true;
  generateReportStatus.value = true;
  ElMessage({
    message: "生成报告中,请耐心等待或从当前页面退出,待生成完成后,可下载。",
    duration: 1500,
  });
  // generateTimer = setTimeout(() => {
  //   // 检查是否继续生成报告逻辑
  //   // if (!continueGenerate.value) {
  //   //   generateloading.value = false; // 隐藏生成中状态
  //   //   return; // 中断生成报告逻辑
  //   // }
  //   // 生成完成后
  //   reportdownloaded.value = false; // 设置报表未下载状态为 false
  //   generateReportStatus.value = true; // 设置报表已生成状态为 true
  //   generateloading.value = false; // 隐藏生成中状态
  //   ElMessage({
  //     message: "报表生成成功。",
  //     grouping: true,
  //     type: "success",
  //   });
  // }, 5000); // 模拟3秒的等待时间
  // 隐藏生成报告按钮
}
function cancelReport() {
  // 设置变量来中断生成报告逻辑
  // continueGenerate.value = false;
  // 清除生成报告定时器
  // clearTimeout(generateTimer);
  //恢复初始状态
  generateReportStatus.value = false;
  reportdownloaded.value = false;
  generateloading.value = false;
  ElMessage({
    message: "报表生成失败。",
    grouping: true,
    type: "error",
  });
}
function downloadReport() {}
// function downLoad(item: any) {
//   if (template.value === "CUSTOMER_MONTH") {
//     downloadTenantReportFile({ id: item.id })
//       .then((res) => {
//         // console.log(res)
//         const link = document.createElement("a");
//         let blob = new Blob([res.data], {
//           type: "application/msword;charset=utf-8",
//         });
//         //  application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8
//         link.style.display = "none";
//         link.href = URL.createObjectURL(blob);
//         link.setAttribute("download", "customer-template.docx");
//         document.body.appendChild(link);
//         link.click();
//         document.body.removeChild(link);
//       })
//       .catch((err) => {
//         // console.log(err)

//         ElMessage.error(err.message);
//       });
//   } else
//     downloadTenantReportFileXlsx({ start: startEndTime.value[0], end: startEndTime.value[1] })
//       .then((res) => {
//         const link = document.createElement("a");
//         let blob = new Blob([res.data], {
//           type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
//         });

//         link.style.display = "none";
//         link.href = URL.createObjectURL(blob);
//         link.setAttribute("download", "customer-template.xlsx");
//         document.body.appendChild(link);
//         link.click();
//         document.body.removeChild(link);
//       })
//       .catch((err) => {
//         // console.log(err)

//         ElMessage.error(err.message);
//       });
// }
async function handleStateRefresh() {
  if (state.loading) return;
  state.loading = true;
  state.data.splice(0, state.data.length, ...(await querysItem({})));
  state.loading = false;
}

/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
// const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  handleStateRefresh();
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.task-main {
  display: flex;
  width: 100%;
  > .task-main-left {
    width: 20%;
    padding-right: 10px;
    box-sizing: border-box;
    :deep(.el-menu-item) {
      height: 30px !important;
    }
  }
  > .task-main-table {
    flex: none;
    width: 80%;
    :deep(.el-input__wrapper) {
      flex-grow: 0;
    }
  }
}
</style>
