<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <div style="margin-bottom: 30px">
      <h2 style="font-weight: 700; margin-bottom: 10px">服务目录总数-{{ userInfo.currentTenant.name }} [{{ userInfo.currentTenant.abbreviation }}]</h2>
      <el-table key="tableTotal" stripe border :data="tableTotal" row-key="idA" :height="tableHeight" style="width: 100%">
        <el-table-column :width="tableWidth" v-for="(item, key) in datalist" :key="key" :label="item.name">
          <template #default>
            <div>
              {{ item.active }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pageTemplate :height="height">
      <template #left>
        <div style="display: flex; align-items: center">
          <h2 style="font-weight: 700; margin-right: 10px">设备服务目录</h2>
          <el-button plain @click.stop="exportFailFile" :disabled="!userInfo.hasPermission(服务管理中心_服务目录_可读)">导出Excel</el-button>
        </div>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table v-loading="loading" key="tableB" border stripe :data="tableData" row-key="id" :height="tableHeight - 30 - 136" style="width: 100%" :summary-method="getSummaries" show-summary>
          <TableColumn type="condition" align="left" label="设备" prop="devName" width="100" fixed="left" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="getList()">
            <template #default="scope">
              <div style="color: #409eff; cursor: pointer" @click="openDevice(scope.row)">
                {{ scope.row.devName }}
              </div>
            </template>
          </TableColumn>
          <TableColumn type="enum" align="left" :width="tableWidth" v-for="(item, key) in packData" :key="key" :prop="item.name" :label="item.name" :min-width="100" @filter-change="getList(item.name, key)" :showOverflowTooltip="true" show-filter v-model:filtered-value="searchForm[key]" :filters="['1', '0'].map((v) => ({ value: v, text: v === '1' ? '√' : '×' }))">
            <template #default="{ row }">
              <span>{{ row.active[key] == 1 ? "✔" : "" }}</span>
            </template>
          </TableColumn>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, inject, computed, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, toValue, h } from "vue";

import type { VNode } from "vue";
import type { TableColumnCtx } from "element-plus";

/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import getUserInfo from "@/utils/getUserInfo";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import pageTemplate from "@/components/pageTemplate.vue";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import { getDeviceCatalogList, downloadDeviceCatalogFile } from "@/views/pages/apis/serviceCatalog";
import moment from "moment";
import { active } from "sortablejs";
import { Check } from "@element-plus/icons-vue";
import { 服务管理中心_服务目录_可读 } from "@/views/pages/permission";
import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";

/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance()!;
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
defineOptions({ name: "messageShow" });

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));
const route = useRoute();
const router = useRouter();
const userInfo = getUserInfo();
const userInfoS = getUserInfo();

/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const loading = ref<boolean>(false);
const errFilePath = ref<string>("");
const tableTotal = ref<Record<string, any>[]>([{}]);
const datalist = ref<Record<string, any>[]>([]);
const tableData = ref<Record<string, any>[]>([]);
const packData = ref<Record<string, any>[]>([]);
const searchData = ref<Record<string, any>[]>([]);

// 筛选开始--------------------------------------------------------------------------------//

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: any };
  column: {
    key: keyof T;
    label?: string;
    align?: "left" | "center" | "right";
    width?: number;
    formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode;
    showOverflowTooltip: boolean;
  }[];
  data: T[];
}

// 设置table formater文字class
// const tableTextClass = "tw-text-gray-400";

const state = reactive<StateData<Record<string, any>>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {
    scope: "INTERNAL",
    keyword: "",
    schemas: ["ROLE"].join(","),
    mfaState: "",
    eqName: "" /* 等于的用户名称 */,
    includeName: "" /* 包含的用户名称 */,
    nameFilterRelation: "AND" /* 用户名称过滤关系(AND,OR) */,
    neName: "" /* 不等于的用户名称 */,
    excludeName: "" /* 不包含的用户名称 */,
  },
});
const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";

    // eqName: [] /* 等于的用户名称 */,
    // includeName: [] /* 包含的用户名称 */,
    // nameFilterRelation: "AND" /* 用户名称过滤关系(AND,OR) */,
    // neName: [] /* 不等于的用户名称 */,
    // excludeName: [] /* 不包含的用户名称 */,
    if (toValue(searchType0ByName) === "include") value0 = state.search.includeName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = state.search.excludeName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = state.search.eqName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = state.search.neName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = state.search.includeName[state.search.includeName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = state.search.excludeName[state.search.excludeName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = state.search.eqName[state.search.eqName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = state.search.neName[state.search.neName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: state.search.nameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    state.search.nameFilterRelation = v.relation;
    state.search.includeName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
// const $filter0 = ref([
//   { text: "包含", value: "include" },
//   { text: "不包含", value: "exclude" },
//   { text: "等于", value: "eq" },
//   { text: "不等于", value: "ne" },
// ]);
const $filter0 = ref(exoprtMatch1);
const searchForm = reactive([]);

interface SummaryMethodProps<T = any> {
  columns: TableColumnCtx<T>[];
  data: T[];
}

function getSummaries(param: SummaryMethodProps) {
  const { columns, data } = param;
  const sums: (string | VNode)[] = [];
  // console.log(columns, data);
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = "总计";
      return;
    }
    const count = tableData.value.reduce((a, c) => a + c.active[index - 1], 0);
    return (sums[index] = count);
  });

  return sums;
}
// columns.forEach((column, index) => {
//   if (index === 0) {
//     sums[index] = h("div", { style: { textDecoration: "underline" } }, ["Total Cost"]);
//     return;
//   }
//   const values = data.map((item) => Number(item[column.property]));
//   if (!values.every((value) => Number.isNaN(value))) {
//     sums[index] = `$ ${values.reduce((prev, curr) => {
//       const value = Number(curr);
//       if (!Number.isNaN(value)) {
//         return prev + curr;
//       } else {
//         return prev;
//       }
//     }, 0)}`;
//   } else {
//     sums[index] = "N/A";
//   }
// });

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}

function beforeMount() {}
function mounted() {
  getList();
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

function formatterTable(_row, _col, v) {
  switch (_col.property) {
    default:
      return v || "--";
  }
}

function openDevice(props) {
  const { href } = router.resolve({
    name: "509596457372745728",
    params: { id: props.resourceId },
    query: {
      fallback: route.name,
      tenant: userInfo.currentTenantId,
    },
  });
  window.open(href, props.resourceId);
}
function exportFailFile() {
  const param = {
    containerId: userInfo.currentTenant.containerId,
    queryPermissionId: "659635446946463744",
  };
  downloadDeviceCatalogFile(param, {
    serviceCatalogStatus: searchData.value.filter((obj) => {
      return Object.values(obj).some((val) => val !== undefined);
    }),
  }).then((res) => {
    const link = document.createElement("a");
    let blob = new Blob([res.data], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
    });
    link.style.display = "none";
    link.href = URL.createObjectURL(blob);
    link.setAttribute("download", "设备服务目录报表" + getCurrentDate() + ".xlsx");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  });
}
function getCurrentDate() {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1; // 月份是从0开始的，所以需要加1
  const day = now.getDate();
  return `${year}-${month.toString().padStart(2, "0")}-${day.toString().padStart(2, "0")}`;
}
//获取列表
function getList(name, key) {
  if (searchForm.length > 0) {
    if (searchForm[key] === "") {
      // 如果 status 为空，删除对应的对象
      searchData.value = searchData.value.filter((item) => item.name !== name);
    } else {
      // 查找数组中是否已经存在相同 name 的对象
      let existingItem = searchData.value.find((item) => item.name === name);
      if (existingItem) {
        // 如果存在相同 name 的对象，更新对应 key 的 status
        existingItem.status = searchForm[key];
      } else {
        // 如果不存在相同 name 的对象，push 一个新的对象
        searchData.value.push({
          name: name,
          status: searchForm[key],
        });
      }
    }
  }
  loading.value = true;
  const param = {
    containerId: userInfo.currentTenant.containerId,
    queryPermissionId: "659635979308498944",
    ...state.search,
    serviceCatalogStatus: searchData.value.filter((obj) => {
      return Object.values(obj).some((val) => val !== undefined);
    }),
  };
  // return
  getDeviceCatalogList(param).then((res) => {
    if (res.success) {
      loading.value = false;
      tableData.value = res.data.resourceServiceDetails;
      packData.value = res.data.packName.map((item) => ({ name: item }));
      datalist.value = res.data.statistics;
      datalist.value.unshift({ name: "名称", active: "总计" });
    } else {
      ElMessage.error(JSON.parse(res.data)?.message);
    }
  });
}

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
