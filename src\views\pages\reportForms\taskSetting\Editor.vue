<template>
  <!-- 对话框表单 -->
  <component :is="ComponentRender" v-model:visible="data.visible" :handle-cancel="handleCancel">
    <template #header>
      {{ ` 添加${props.title}` }}
    </template>
    <template #default="{}">
      <!-- width -->
      <FormModel ref="formRef" :loading="data.loading" :model="form" label-position="top" :label-width="`${props.labelWidth}px`" @submit="handleFinish">
        <FormItem :span="24" :label="`选择客户 `" tooltip="" prop="" :rules="[]">
          <el-input v-model="form.ident" :placeholder="`输入${props.title}名称/缩写 查询`" clearable @keyup.enter="getDataList()">
            <template #append>
              <el-button :icon="Search" @click="getDataList()">搜索</el-button>
            </template>
          </el-input>
        </FormItem>
        <FormItem :span="24" :label="`(已选择${chooseList.length})`">
          <!-- <el-tag v-for="(tag, index) in chooseList" :key="tag" class="mx-1" closable :disable-transitions="false" @close="handleClose(tag, index)" style="margin-right: 5px">
            {{ tag.tenantName }}
          </el-tag> -->
        </FormItem>
        <FormItem style="height: 360px" :span="24" :label="``" tooltip="" prop="userIds" :rules="[{ required: true, type: 'array', message: $t('glob.Please select field', { field: `至少一个${props.title}` }) }]">
          <el-table ref="tableRef" v-if="form.ident && showTable" :data="form.list" border stripe :height="360" row-key="id" @select-all="selectChange" @select="selectChange">
            <!--  -->
            <el-table-column type="selection" width="50" :selectable="selectable" />

            <el-table-column prop="name" label="客户名称" :min-width="80" />
            <el-table-column prop="abbreviation" label="客户缩写" :min-width="120" />
            <el-table-column prop="address" label="地址" :min-width="120" />
          </el-table>
        </FormItem>
      </FormModel>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </component>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, computed, h, renderSlot, getCurrentInstance, createVNode, watch } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { ElMessage, ElMessageBox, ElTable } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { buildTypeHelper } from "@/utils/type";
import { useConfig } from "@/stores/config";
import moment from "moment";

import { buildValidatorData } from "@/utils/validate";

import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import getUserInfo from "@/utils/getUserInfo";

import { locales, localesOption } from "@/api/locale";
import { gender, genderOption } from "@/api/personnel";
import { getUserByPlatform, getUser as getItem } from "@/api/personnel";

const userInfo = getUserInfo();
const config = useConfig();
const { t } = useI18n({ useScope: "global" });
const formRef = ref<InstanceType<typeof FormModel>>();
const tableRef = ref<InstanceType<typeof ElTable>>();
const ctx = getCurrentInstance();
if (!ctx) throw new Error("Component context initialization failed!");
const { appContext } = ctx;

interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 80,
});

const showTable = ref(false);

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
type RemoteData = (ReturnType<typeof getUserByPlatform> extends Promise<infer U> ? U : never)["data"][number];
interface Item {
  ident: string;
  userIds: string[];
  page: number;
  size: number;
  total: number;
  list: RemoteData[];
}

const chooseList = ref([]);
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
async function getDataList() {
  console.log(userInfo, "99");

  try {
    // const { success, message, data, page, size, total } = await getUserByPlatform({ ident: form.value.ident, paging: { pageNumber: form.value.page, pageSize: form.value.size } });
    // if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
    // form.value.page = Number(page) || 1;
    // form.value.size = Number(size) || 20;
    // form.value.total = Number(total) || 0;
    // let list = data.filter((v, i) => {
    //   return allUserList.value.every((item: any) => {
    //     return item.id !== v.id;
    //   });
    // });
    const list: any = [];
    if (form.value.ident != "") {
      userInfo.tenants.forEach((v, i) => {
        console.log(form.value, "99000---88");

        if (v.name?.includes(form.value.ident) || v.tenantAbbreviation?.includes(form.value.ident)) {
          list.push(v);
        }
      });
    }
    const arr: any = [];
    // console.log(userInfo, list, 777777);
    // tenantIdList.value.forEach((v, i) => {
    //   list.forEach((item) => {
    //     if (item.tenantId != v) {
    //       arr.push(item);
    //     }
    //   });
    // });
    form.value.list = list instanceof Array ? [...list] : [];

    showTable.value = true;
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
  await nextTick(() => {
    if (!tableRef.value) return;
    for (let i = 0; i < form.value.list.length; i++) {
      tableRef.value.toggleRowSelection(form.value.list[i], form.value.userIds.includes(form.value.list[i].id));
    }
  });
}
function selectable(row, index) {
  // console.log(row, index);
  // tenantIdList.value.forEach((v) => {
  //   if (v === row.tenantId) {
  //     console.log(v);
  //     return true;
  //   } else {
  //     return false;
  //   }
  // });

  return tenantIdList.value.findIndex((item) => item == row.tenantId) === -1;
}

function selectChange(select: any, row?: any) {
  chooseList.value = select;

  if (row ? select.filter((v) => v.id === row.tenantId).length : select.length) {
    form.value.userIds = Array.from(new Set([...form.value.userIds, ...select.map((v) => v.id)]));
  } else {
    const invert: string[] = row ? [row.tenantId] : form.value.list.map((v) => v.id);
    for (let i = 0; i < invert.length; i++) {
      const index = form.value.userIds.indexOf(invert[i]);
      if (index >= 0) form.value.userIds.splice(index, 1);
    }
  }
}

/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  ident: buildTypeHelper<Required<Item>["ident"]>(""),
  userIds: buildTypeHelper<Required<Item>["userIds"]>([]),
  page: buildTypeHelper<Required<Item>["page"]>(1),
  size: buildTypeHelper<Required<Item>["size"]>(20),
  total: buildTypeHelper<Required<Item>["total"]>(0),
  list: buildTypeHelper<Required<Item>["list"]>([]),
});

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  // await getDataList();
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  return form;
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 窗口方法
 */
interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<Item>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Record<string, unknown>) => void;
  reject?: (value: Error) => void;
  callback?: (form: Record<string, unknown>) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Record<string, unknown>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(function (formResult, [key, util]) {
    if (!util) return formResult;
    else if (util.test(formResult[key])) return formResult;
    else return Object.assign(formResult, { [key]: util.transfer(formResult[key], util.value as never) });
  }, form as Required<Item>);
}

async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => (formRef.value ? formRef.value.validate(resolve) : resolve(false)));
  } catch (error) {
    return false;
  }
}

/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});
  console.log($form, "000");

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}
const tenantIdList = ref([]);

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();

defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, callback?: (form: Record<string, unknown>) => Promise<void>): Promise<unknown> {
    if (data.visible) handleCancel();
    const wait = new Promise<Record<string, unknown>>((resolve, reject) => ((data.resolve = resolve), (data.reject = reject)));
    $params.value = cloneDeep(params);
    // console.log(params,666666)
    tenantIdList.value = params.tenantIdList;
    data.visible = true;
    data.loading = true;
    data.submitLoading = true;
    chooseList.value = [];
    data.callback = callback;

    await nextTick();

    try {
      await runningInit($params.value);
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      handleCancel();
    }
    handleReset();
    data.loading = false;
    data.submitLoading = false;

    try {
      return await wait;
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async confirm<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.confirm(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async alert<T extends { $title: string; $type: "" | "success" | "warning" | "info" | "error"; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.alert(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
});

watch(
  () => form.value.ident,
  (newval, oldval) => {
    // // console.log("form.value.ident", newval);
    if (newval == "") {
      showTable.value = false;
    }
  }
);
</script>

<style scoped lang="scss"></style>
