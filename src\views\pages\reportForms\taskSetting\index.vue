<template>
  <el-card>
    <el-scrollbar>
      <div class="flex-search" :style="{ minWidth: `${width - 42}px`, padding: '0px' }">
        <div class="left"></div>
        <div class="center"></div>
        <div class="right">
          <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group582103048025800704.create)">
            <span>
              <el-button type="primary" :disabled="!userInfo.hasPermission(PERMISSION.group582103048025800704.create)" :icon="Plus" @click="createItem({}).finally(() => handleStateRefresh())">添加客户</el-button>
            </span>
          </el-tooltip>
        </div>
      </div>

      <div class="task-main">
        <div class="task-main-left">
          <h3>模板列表</h3>
          <el-select v-model="reporType" placeholder="">
            <!-- <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option> -->
            <el-option label="月报" value="MONTH">月报</el-option>
            <!-- <el-option label="周报" value="WEEK">周报</el-option> -->
            <!-- <el-option label="自定义" value="CUSTOM">自定义</el-option> -->
          </el-select>
          <el-menu :default-active="reportId" class="el-menu-vertical-demo">
            <el-menu-item v-for="item in reportList" :key="item.id" :index="item.id" @click="reportChange(item)">
              <span>{{ item.name }}</span>
            </el-menu-item>
          </el-menu>
        </div>
        <div class="seat"></div>
        <el-table v-loading="state.loading" :data="state.data" :height="height - 104 - 20 - (state.total ? 32 : 0)" :style="{ width: `${width}px`, margin: '0 auto' }">
          <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :min-width="column.width || 120" :="column.showOverflowTooltip" :formatter="column.formatter" />
          <el-table-column :label="t('glob.operate')" align="left" header-align="left" :width="220" fixed="right">
            <template #header="{ column }">
              <span class="tw-mr-[2.5px]">{{ column.label }}</span>
              <el-link class="tw-ml-[2.5px] tw-align-middle" type="primary" :underline="false" :icon="Refresh" :title="t('glob.refresh')" @click.prevent="handleStateRefresh()" style="font-size: 14px"></el-link>
            </template>
            <template #default="{ row }: { row: DataItem }">
              <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.group582103048025800704.remove)">
                <span class="el-button is-link">
                  <el-button link type="primary" :disabled="!userInfo.hasPermission(PERMISSION.group582103048025800704.remove)" size="small" @click="del(row as Record<string, any>).finally(() => handleStateRefresh())" style="font-size: 14px"> 移除 </el-button>
                </span>
              </el-tooltip>
              <!-- <el-button link type="danger" size="small" :icon="Delete"></el-button> -->
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-scrollbar>
    <div :style="{ margin: '2px 20px 20px' }">
      <el-pagination v-show="state.total" v-model:current-page="state.page" v-model:page-size="state.size" :page-sizes="state.sizes" :total="state.total" layout="->, total, sizes, prev, pager, next, jumper" @size-change="handleStateRefresh()" @current-change="handleStateRefresh()" />
    </div>
  </el-card>
  <!-- <EditorForm ref="editorRef" title="客户" /> -->
  <Editor ref="editorRef" title="客户"> </Editor>
</template>

<script setup lang="ts" generic="T extends object">
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete, Connection, More, Refresh, Lock, Unlock, InfoFilled, EditPen, Setting } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox, ElTag } from "element-plus";

import type { TableColumnCtx } from "element-plus";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";
import { useSiteConfig } from "@/stores/siteConfig";
import getUserInfo from "@/utils/getUserInfo";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
import { getReportList, addTenantReport as addData, editReport as setData, type Reports as DataItem, reportStateOption, reportModelOption, enableReport, delTenantReport, getTenantReportList as getData } from "@/views/pages/apis/reports";

import { sizes } from "@/utils/common";

import { Zone } from "@/utils/zone";
import { bindFormBox } from "@/utils/bindFormBox";
import { EditorType } from "@/views/common/interface";

// import EditorForm from "./EditForm.vue";
import Editor from "./Editor.vue";

const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
// const i18n = useI18n({ useScope: "local" });
const { t } = useI18n();

const route = useRoute();
const router = useRouter();
const userInfo = getUserInfo();
defineOptions({ name: "TenantGovern" });

const siteConfig = useSiteConfig();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const reporType = ref("MONTH");

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: TableColumnCtx<DataItem>, value: T[keyof T]) => string | import("vue").VNode; showOverflowTooltip?: boolean }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<DataItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  column: [
    { key: "name", label: "客户名称", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? String(v) : "--") },
    { key: "abbreviation", label: "客户缩写", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? String(v) : "--") },
    { key: "address", label: "客户地址", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? String(v) : "--") },
    { key: "tenantPhone", label: "电话", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? String(v) : "--") },
    { key: "tenantEmail", label: "邮箱", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? String(v) : "--") },
    // { key: "baileeTenantId", label: "是否托管", showOverflowTooltip: true, formatter: (_row, _col, v) => h(ElTag, { type: v ? "success" : "" }, () => (v ? t("glob.Yes") : t("glob.Not"))) },
    // { key: "baileeTenantName", label: "受托客户", showOverflowTooltip: true, formatter: (_row, _col, v) => (_row.baileeTenantId ? `${v || "--"}（${_row.baileeTenantAbbreviation || "--"}）` : "") },
    // { key: "zoneId", label: "时区", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? Zone[v as keyof typeof Zone] : "--") },
    // { key: "template", label: "报表模板", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? reportModelOption.map((item) => (item.value == v ? item.label : "")) : "--") },

    // { key: "enable", label: "状态", showOverflowTooltip: true, formatter: (_row, _col, v) => (v == false ? h(ElTag, { type: "danger" }, () => "禁用") : h(ElTag, { type: "success" }, () => "启用")) },
  ],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

const publicParams = computed<Record<string, unknown>>(() => ({}));
const editorRef = ref<InstanceType<typeof Editor>>();

const isActive = ref(false);
async function createItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  // if (!editorForPersonnelRef.value) return row;
  try {
    const $raw: Partial<(ReturnType<typeof addData> extends Promise<infer U> ? U : never)["data"]> = {};
    await editorRef.value.open({ tenantIdList: tenantIdList.value }, async (form: Record<string, unknown>) => {
      // if (form.ownerUserId) delete form.owner;
      // else if ((<Record<string, unknown>>form.owner || {}).name || (<Record<string, unknown>>form.owner || {}).account || (<Record<string, unknown>>form.owner || {}).phone || (<Record<string, unknown>>form.owner || {}).email) {
      //   delete form.ownerUserId;
      //   form.owner = { name: (<Record<string, unknown>>form.owner || {}).name, account: (<Record<string, unknown>>form.owner || {}).account, phone: (<Record<string, unknown>>form.owner || {}).phone, email: (<Record<string, unknown>>form.owner || {}).email };
      // } else {
      //   delete form.owner;
      //   delete form.ownerUserId;
      // }

      const { success, message, data } = await addData({ id: reportId.value, tenantIds: form.userIds.join() });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(t("axios.Operation successful"));
      getReport();

      // Object.assign($raw, data);
    });
  } catch (error) {
    /*  */
  }
}

async function del(row: Record<string, unknown>) {
  const form = reactive<Record<"title", string>>({
    title: "移除客户",
  });
  try {
    await bindFormBox([h("p", {}, `确定移除客户${row.name}吗？`)], form, async () => {
      const { success, message, data } = await delTenantReport({ id: reportId.value, tenantIds: row.id });
      // // console.log(success, message, data)
      if (success) {
        ElMessage.success("操作成功");
        tenantIdList.value = tenantIdList.value.filter((item) => item != row.id);
        handleStateRefresh();
      }
      return { success, message };
    });
  } catch (error) {
    /*  */
  }
}
const reportId = ref("");
const reportList = ref([]);
const tenantIdList = ref([]);
async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  let controller = new AbortController();
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const params = { keyword: state.search.keyword };

    if (tenantIdList.value.length > 0) {
      const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await getData({ tenantIds: tenantIdList.value });
      if (success) {
        state.page = Number(page);
        state.size = Number(size);
        state.total = Number(total);
        // reportId.value = data[0].id;
        return data instanceof Array ? data : [];
      } else throw Object.assign(new Error(message), { success, data });
    } else {
      return [];
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(20);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}
function reportChange(item: any) {
  reportId.value = item.id;
  tenantIdList.value = item.tenantIds;
  handleStateRefresh();
}
async function getReport() {
  const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await getReportList({ paging: { pageNumber: state.page, pageSize: 100000 }, type: reporType.value });
  if (success) {
    reportId.value = data[0].id;
    reportList.value = data;
    tenantIdList.value = data[0].tenantIds;
    handleStateRefresh();
  }
}

async function handleStateRefresh() {
  if (state.loading) return;
  state.loading = true;
  state.data.splice(0, state.data.length, ...(await querysItem({})));
  state.loading = false;
}

/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
// const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
function mounted() {
  getReport();
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss">
.task-main {
  display: flex;
  > .task-main-left {
    width: 25%;
    padding-right: 10px;
    box-sizing: border-box;
    :deep(.el-menu-item) {
      height: 30px !important;
    }
  }
  .seat {
    width: 15px;
    // height: 100%;
    background: #f5f5f5;
    margin-right: 10px;
  }
}
</style>
