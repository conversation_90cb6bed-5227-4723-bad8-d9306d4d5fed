<template>
  <!-- 对话框表单 -->
  <component :is="ComponentRender" v-model:visible="data.visible" :handle-cancel="handleCancel" style="width: 30%">
    <template #header>
      {{ `${$params.id ? t("glob.edit") : t("glob.add")}${props.title}` }}
    </template>
    <template #default="{ width }">
      <!-- width -->
      <FormModel ref="formRef" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @submit="handleFinish" class="editor-tenant">
        <FormItem :span="24" :label="`报表类型`" tooltip="" prop="type" :rules="[{ required: true, message: '请选择报表类型', trigger: 'change' }]">
          <el-select style="width: 100%" v-model="form.type">
            <el-option label="月报" value="MONTH">月报</el-option>
          </el-select>
        </FormItem>
        <FormItem :span="24" :label="`报表名称`" tooltip="" prop="name" :rules="[{ required: true, message: '请输入报表名称', trigger: 'blur' }]">
          <el-input v-model="form.name" :placeholder="`请输入报表名称`" />
        </FormItem>
        <FormItem :span="24" :label="`报表模板`" tooltip="" prop="template" :rules="[{ required: true, message: '请选择报表模板', trigger: 'change' }]">
          <el-select style="width: 90%" v-model="form.template">
            <el-option label="客户月报" value="CUSTOMER_MONTH">客户月报</el-option>
            <el-option label="运营报表" value="OPERATING_STATEMENT">运营报表</el-option>
          </el-select>
          <el-link type="primary" @click="downloadFile" :underline="false" :disabled="!userInfo.hasPermission(PERMISSION.group582102647406854144.download_template)"> 下载 </el-link>
          <!-- <el-checkbox label="" v-model="form.activated"> </el-checkbox> -->
        </FormItem>
        <FormItem :span="24" :label="`开始时间`" tooltip="" prop="startTime" :rules="[]">
          <!-- <el-input v-model="form.startTime" :placeholder="`请输入地址`" /> -->
          <el-time-picker v-model="form.startTime" placeholder="请选择开始时间" value-format="HH:mm:ss" />
        </FormItem>
        <FormItem :span="24" :label="`结束时间`" tooltip="" prop="endTime">
          <!-- <el-input v-model="form.endTime" :placeholder="`请输入电话`" /> -->
          <el-time-picker v-model="form.endTime" placeholder="请选择结束时间" value-format="HH:mm:ss" />
        </FormItem>
      </FormModel>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button> -->
      <el-button v-if="form.template == 'CUSTOMER_MONTH'" type="primary" @click="handleSubmit()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      <!-- <el-button v-if="!$params.id" type="primary" :disabled="!userInfo.hasPermission(PERMISSION.tenant.auth524820946712788992)" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}并配置采集机</el-button> -->
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </component>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { toRaw, readonly, reactive, ref, nextTick, computed, h, renderSlot, getCurrentInstance, createVNode } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep, find, findIndex } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { Back, UserFilled } from "@element-plus/icons-vue";
import { buildTypeHelper } from "@/utils/type";
import { useConfig } from "@/stores/config";
import moment from "moment";

import { buildValidatorData, validatorPattern } from "@/utils/validate";

import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";

import getUserInfo from "@/utils/getUserInfo";

import { getSystemVersion } from "@/api/system";

import { TenantItem, getUserByPlatform, getTenantList, type UserItem, getGroup } from "@/api/personnel";
import { downloadReportFile } from "@/views/pages/apis/reports";

const { log } = console;

const dataLoading = ref(false);
const dataList = ref<Partial<{ id: string; name: string; account: string; phone: string; email: string; [key: string]: unknown }>[]>([]);

const userInfo = getUserInfo();
const config = useConfig();
const { t } = useI18n({ useScope: "global" });
const formRef = ref<InstanceType<typeof FormModel>>();
const ctx = getCurrentInstance();
if (!ctx) throw new Error("Component context initialization failed!");
const { appContext } = ctx;

interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 120,
});

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Item {
  type: string;
  name: string;
  template: string;
  startTime: string;
  endTime: string;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  type: buildTypeHelper<Required<Item>["type"]>(""),
  name: buildTypeHelper<Required<Item>["name"]>(""),
  template: buildTypeHelper<Required<Item>["template"]>(""),
  startTime: buildTypeHelper<Required<Item>["startTime"]>(""),
  endTime: buildTypeHelper<Required<Item>["endTime"]>(""),
});

const systemEditionOption = ref<Record<"name" | "code", string>[]>([]);
const tenants = ref({
  loading: false,
  base: <TenantItem | null>null,
  option: <TenantItem[]>[],
});

function downloadFile() {
  downloadReportFile().then((res) => {
    const link = document.createElement("a");
    let blob = new Blob([res.data], {
      type: "application/msword;charset=utf-8",
    });
    //  application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8
    link.style.display = "none";
    link.href = URL.createObjectURL(blob);
    link.setAttribute("download", "customer-template.docx");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  });
}

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  await (async () => {
    const { success, message, data } = await getSystemVersion({});
    if (!success) throw Object.assign(new Error(message), { success, data });
    systemEditionOption.value = data instanceof Array ? data.reverse() : [];
    form.value.systemEdition = systemEditionOption.value[0].code;
    // // console.log(form.value, 66666666);
  })();
}
const userGroupOption = ref<Record<"name" | "code", string>[]>([]);
// 获取组织下所有用户组
async function getGroupList() {
  await (async () => {
    const { success, message, data } = await getGroup({});
    if (!success) throw Object.assign(new Error(message), { success, data });
    userGroupOption.value = data;
  })();
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  // if (!$params.value.id) {
  tenants.value.loading = true;
  let pageNumber = 1;
  let pageSize = 300;
  let pageTotal = Infinity;
  const $data: (ReturnType<typeof getTenantList> extends Promise<infer U> ? U : never)["data"] = [];
  while ((pageNumber - 1) * pageSize < pageTotal) {
    const { success, message, data, page, size, total } = await getTenantList({ paging: { pageNumber, pageSize } });
    if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
    pageNumber = Number(page) || 1;
    pageSize = Number(size) || 30;
    pageTotal = Number(total) || 0;
    if (data instanceof Array) $data.push(...(data instanceof Array ? data : []));
    pageNumber++;
  }
  // if (params.baileeTenantId) {
  //   const { success, message, data } = await getTenantList({ id: <string>params.baileeTenantId, paging: { pageNumber: 1, pageSize: 1 } });
  //   if (!success) throw Object.assign(new Error(message), { success, data });
  //   tenants.value.base = (data instanceof Array ? data : [])[0] || null;
  // } else {
  //   tenants.value.base = null;
  // }
  tenants.value.option = $data.filter((v) => (v.baileeTenantId ? v.baileeTenantId === $params.value.baileeTenantId && v.id !== $params.value.id : true));
  tenants.value.loading = false;

  // }
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  if (form.ownerUserId === "new") delete form.ownerUserId;
  else delete form.owner;
  return form;
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 窗口方法
 */
interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<Item>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Record<string, unknown>) => void;
  reject?: (value: Error) => void;
  callback?: (form: Record<string, unknown>) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Record<string, unknown>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(function (formResult, [key, util]) {
    if (!util) return formResult;
    else if (util.test(formResult[key])) return formResult;
    else return Object.assign(formResult, { [key]: util.transfer(formResult[key], cloneDeep(toRaw(util.value)) as never) });
  }, form as Required<Item>);
}

async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => (formRef.value ? formRef.value.validate(resolve) : resolve(false)));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleSubmit(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.reject === "function") await data.reject(Object.assign(new Error(), $form));
    // if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 下一步方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();

defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, callback?: (form: Record<string, unknown>) => Promise<void>): Promise<unknown> {
    if (data.visible) handleCancel();
    const wait = new Promise<Record<string, unknown>>((resolve, reject) => ((data.resolve = resolve), (data.reject = reject)));
    $params.value = cloneDeep(params);
    // if(JSON.stringify(params)=="{}"){
    //   form.value.systemEdition = systemEditionOption.value[0].code;
    // }

    data.visible = true;
    data.loading = true;
    data.submitLoading = true;
    data.callback = callback;

    // remoteTenantsMethod();
    await resetFormInit($params.value);
    await getGroupList();
    await nextTick();
    try {
      await runningInit($params.value);
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      handleCancel();
    }
    handleReset();
    data.loading = false;
    data.submitLoading = false;

    return await wait;
    // try {
    //   return await wait;
    // } catch (error) {
    //   return error;
    // }
  },
  async confirm<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.confirm(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async alert<T extends { $title: string; $type: "" | "success" | "warning" | "info" | "error"; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.alert(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
});
</script>

<style lang="scss">
.dissableFirstLevel {
  .el-scrollbar:first-child {
    .el-checkbox {
      display: none;
    }
  }
}
</style>
