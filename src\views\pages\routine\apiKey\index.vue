<template>
  <el-scrollbar :height="height">
    <el-card>
      <div class="md:tw-col-span-2">
        <div class="tw-w-full tw-px-4 sm:tw-px-0">
          <h3 class="tw-text-base tw-font-semibold tw-leading-6 dark:tw-text-white">API KEY</h3>
          <p class="tw-mt-1 tw-text-sm tw-text-gray-600 dark:tw-text-slate-300">API KEY</p>
        </div>
      </div>
      <el-row>
        <el-col class="tw-text-right">
          <el-button type="primary" @click="createItem">创建API KEY</el-button>
        </el-col>
        <el-col>
          <el-table v-loading="state.loading" :data="state.data" stripe style="width: 100%" :height="height - 104 - 20 - (state.total ? 32 : 0)">
            <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :width="column.width" :="column.showOverflowTooltip" :formatter="column.formatter" />
            <el-table-column v-slot="{ row }" width="100">
              <el-button v-if="!row.enabled" type="primary" link :icon="Select" :title="t('glob.Enable')" @click="handelUpdateApiKey(row)" />
              <el-button v-else type="primary" link :icon="CloseBold" :title="t('glob.Disable')" @click="handelUpdateApiKey(row)" />
              <el-button type="primary" link :icon="Delete" @click="handleRemoveApiKey(row)" />
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </el-card>
  </el-scrollbar>
  <EditorDetailsByClient ref="editorRef" title="API KEY"></EditorDetailsByClient>
</template>

<script setup lang="ts" name="routine/userInfo/apikey">
import { inject, ref, onMounted, reactive, h, nextTick } from "vue";
import type { Ref } from "vue";
import { ElMessage, ElMessageBox, ElTag, ElButton } from "element-plus";
import { Delete, Select, CloseBold, DocumentCopy } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";
import EditorDetailsByClient from "./EditorDetailsByAuth.vue";
import { getApiKey, ApiKeys, createApiKey, delApiKey, putApiKeyEnabled } from "@/api/system";
import { useClipboard } from "@vueuse/core";

const height = inject<Ref<number>>("height", ref(100));

const { t } = useI18n();

const { copy, isSupported } = useClipboard({ read: false, legacy: true });

const editorRef = ref<InstanceType<typeof EditorDetailsByClient>>();

interface ApiKeysList<T> {
  data: T[];
  loading: Boolean;
  column: {
    key: keyof T;
    label?: string;
    align?: "left" | "center" | "right";
    width?: number;
    showOverflowTooltip?: boolean;
    formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode;
  }[];
  total: Number;
}

const state = reactive<ApiKeysList<ApiKeys>>({
  loading: false,
  data: [],
  column: [],
  total: 0,
});

async function updateItem(params: Partial<ApiKeys>) {
  const title = !params.enabled ? "启用" : "禁用";
  await nextTick();
  try {
    await new Promise((resolve, reject) => {
      const option = reactive<{ message: string; valid: boolean; [key: string]: unknown }>({
        message: `确认更改${title}`,
        valid: true,
      });
      ElMessageBox({
        title: `${title}API KEY`,
        message() {
          return h("span", {}, [h("span", {}, option.message), h("span", { style: { margin: "0 3px", color: "var(--el-color-danger)" } }, params.label || "此"), option.valid ? h("span", {}, `${title}API KEY`) : h("span", {}, `${"API KEY"}${title}失败！`)]);
        },
        type: "info",
        showCancelButton: true,
        showConfirmButton: true,
        cancelButtonText: t("glob.Cancel"),
        confirmButtonText: t(!params.enabled ? "glob.Enable" : "glob.Disable"),
        distinguishCancelAndClose: true,
        draggable: true,
        async beforeClose(action, instance, done) {
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            try {
              const { success, message } = await putApiKeyEnabled({ id: params.id, enabled: !params.enabled });
              if (success) ElMessage.success(`${title}成功`);
              else ElMessage.error(message);
              if (!option.valid) throw new Error("Error");
              resolve(params);
              done();
            } catch (error) {
              option.message = "";
              option.valid = false;
              instance.showConfirmButton = false;
              instance.type = "error";
            } finally {
              instance.confirmButtonLoading = false;
            }
          } else {
            reject(params);
            done();
          }
        },
      });
    });
  } catch (error) {
    /*  */
  }
}

async function delItem(params: Partial<ApiKeys>) {
  await nextTick();
  try {
    await new Promise((resolve, reject) => {
      const option = reactive<{ message: string; valid: boolean; [key: string]: unknown }>({
        message: "确认删除",
        valid: true,
      });
      ElMessageBox({
        title: `删除API KEY`,
        message() {
          return h("span", {}, [h("span", {}, option.message), h("span", { style: { margin: "0 3px", color: "var(--el-color-danger)" } }, params.label || "此"), option.valid ? h("span", {}, "删除API KEY") : h("span", {}, `${"删除API KEY"}删除失败！`)]);
        },
        type: "info",
        showCancelButton: true,
        showConfirmButton: true,
        cancelButtonText: t("glob.Cancel"),
        confirmButtonText: t("glob.delete"),
        distinguishCancelAndClose: true,
        draggable: true,
        async beforeClose(action, instance, done) {
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            try {
              const { success, message } = await delApiKey({ id: params.id });
              if (success) ElMessage.success("删除成功");
              else ElMessage.error(message);
              if (!option.valid) throw new Error("Error");
              resolve(params);
              done();
            } catch (error) {
              option.message = "";
              option.valid = false;
              instance.showConfirmButton = false;
              instance.type = "error";
            } finally {
              instance.confirmButtonLoading = false;
            }
          } else {
            reject(params);
            done();
          }
        },
      });
    });
  } catch (error) {
    /*  */
  }
}
async function handelUpdateApiKey(params: Partial<ApiKeys>) {
  await updateItem(params);
  await handleStateRefresh();
}
async function handleRemoveApiKey(params: Partial<ApiKeys>) {
  await delItem(params);
  await handleStateRefresh();
}

async function createItem() {
  const title = "创建API KEY";
  ElMessageBox.prompt("请输入API KEY Label", title, {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputPattern: /[\S]+/,
    inputErrorMessage: "请输入API KEY Label",
  })
    .then(async ({ value }) => {
      try {
        const params = { label: value };
        const { success, message } = await createApiKey(params);
        if (success) {
          ElMessage.success(`${title}成功`);
          handleStateRefresh();
        } else ElMessage.error(message);
      } catch (error) {
        if (error instanceof Error) ElMessage.error(`${title}失败`);
      }
    })
    .catch(() => {});
}

async function handleStateRefresh() {
  state.loading = true;
  try {
    const { success, data } = await getApiKey({});
    if (success) {
      if (data instanceof Array) state.data = data;
      state.loading = false;
    }
  } catch (e) {
    state.loading = false;
  }
}

onMounted(async () => {
  state.column = [
    { key: "label", label: "Label", width: 200, showOverflowTooltip: true },
    {
      key: "apiKey",
      label: "API KEYS",
      showOverflowTooltip: true,
      formatter: (_row, _col, v) => {
        async function onClick(value: string) {
          if (!isSupported.value) ElMessage.error("浏览器不支持");
          try {
            await copy(value);
            ElMessage.success("复制成功");
          } catch (error: unknown) {
            if (error instanceof Error) ElMessage.error(error.message);
          }
        }
        return h("div", { class: "tw-flex" }, [h("span", { class: "tw-mr-2" }, v as string), h(DocumentCopy, { style: "width: 16.25px", onClick: () => onClick(v as string) })]);
      },
    },
    { key: "createdTime", label: "Created", width: 200, showOverflowTooltip: true },
    {
      key: "enabled",
      label: "Status",
      width: 100,
      showOverflowTooltip: true,
      formatter: (_row, _col, v) => {
        const tag = {
          success: h(ElTag, { type: "success", effect: "dark" }, () => t("glob.Enable")),
          info: h(ElTag, { type: "info", effect: "dark" }, () => t("glob.Disable")),
        };
        return tag[v ? "success" : "info"];
      },
    },
  ];
  await handleStateRefresh();
});
</script>

<style lang="scss" scoped></style>
