<template>
  <!-- 对话框表单 -->
  <el-dialog v-model="data.visible" :close-on-click-modal="false" :draggable="true" :width="`${width}px`" :before-close="handleBeforeClose">
    <template #header>
      <div class="title">{{ $t("personalInformation.Dynamic Key") }} {{ $t("personalInformation.Guide") }}</div>
    </template>
    <template #default>
      <el-steps :active="form.step" align-center finish-status="success"><el-step v-for="_step in props.step" :key="`step_${_step}`" /></el-steps>
      <el-scrollbar ref="contentRef" :height="height">
        <el-form v-loading="data.loading" ref="formRef" :model="form" size="large" label-position="left" :label-width="`${props.labelWidth}px`" style="overflow: hidden; padding: 12px" @submit.prevent @keyup.enter.stop>
          <el-row v-show="form.step !== props.step - 1" :gutter="16" class="tw-pt-10">
            <!-- <template v-if="form.step === 0">
              <el-col :span="24">
                <el-form-item label="验证方法" prop="mfaMethods" :rules="[]">
                  <el-radio-group v-model="form.mfaMethod" @change="(form.mfaCode = ''), nextTick(onChangeCaptcha)">
                    <el-radio v-for="method in props.mfaMethods" :key="`mfa_method-${method}`" :label="method">{{ MFAMethodOption.find(({ value }) => value === method)?.label || method }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <template v-if="form.mfaMethod === MFAMethod.PASSWORD">
                <el-col :span="24">
                  <el-form-item label="密码" prop="mfaCode" :rules="[buildValidatorData({ name: 'required', title: '密码' }), buildValidatorData({ name: 'password', title: '密码' })]">
                    <el-input v-model="form.mfaCode" :prefix-icon="Lock" type="password" placeholder="请输入密码" show-password clearable></el-input>
                  </el-form-item>
                </el-col>
              </template>
              <template v-else-if="form.mfaMethod === MFAMethod.SMS">
                <el-col :span="24">
                  <el-form-item label="短信验证码" prop="mfaCode" :rules="[buildValidatorData({ name: 'required', title: '短信验证码' }), { type: 'string', min: 4, max: 8, message: '短信验证码需要在4-8位字符', trigger: 'blur' }]">
                    <el-input v-model="form.mfaCode" :prefix-icon="ChatDotSquare" placeholder="短信验证码" clearable>
                      <template #suffix>
                        <el-link type="primary" :disabled="loadingCaptcha || coolingCaptcha !== 0" :underline="false" @click="sendCertificate()">获取验证码{{ coolingCaptcha ? `（${coolingCaptcha}秒）` : "" }}</el-link>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </template>
              <template v-else-if="form.mfaMethod === MFAMethod.EMAIL">
                <el-col :span="24">
                  <el-form-item label="邮箱验证码" prop="mfaCode" :rules="[buildValidatorData({ name: 'required', title: '邮箱验证码' }), { type: 'string', min: 4, max: 8, message: '邮箱验证码需要在4-8位字符', trigger: 'blur' }]">
                    <el-input v-model="form.mfaCode" :prefix-icon="ChatDotSquare" placeholder="邮箱验证码" clearable>
                      <template #suffix>
                        <el-link type="primary" :disabled="loadingCaptcha || coolingCaptcha !== 0" :underline="false" @click="sendCertificate()">获取验证码{{ coolingCaptcha ? `（${coolingCaptcha}秒）` : "" }}</el-link>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </template>
              <template v-else-if="form.mfaMethod === MFAMethod.TOTP">
                <el-col :span="24">
                  <el-form-item label="MFA动态码" prop="mfaCode" :rules="[buildValidatorData({ name: 'required', title: 'MFA动态码' }), { type: 'string', min: 4, max: 8, message: 'MFA动态码需要在4-8位字符', trigger: 'blur' }]">
                    <el-input v-model="form.mfaCode" :prefix-icon="Lock" placeholder="请输入MFA动态码" clearable></el-input>
                  </el-form-item>
                </el-col>
              </template>
            </template> -->
            <template v-if="form.step === 0">
              <el-col :span="24">
                <el-form-item :label="$t('personalInformation.Verification')" prop="code" :rules="[buildValidatorData({ name: 'required', title: 'TOTP动态码' }), { type: 'string', min: 4, max: 8, message: 'TOTP动态码需要在4-8位字符', trigger: 'blur' }]">
                  <el-input v-model="form.code" :prefix-icon="ChatDotSquare" :placeholder="$t('personalInformation.Please enter TOTP dynamic code')" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <canvas ref="qrCodeCanvasRef" width="160" height="160" class="tw-mx-auto tw-my-4" />
                <el-alert class="tw-my-4" type="info" :closable="false">
                  <template #default>
                    <div class="title-alert">
                      <div>{{ $t("personalInformation.enabling") }}</div>
                      <p>{{ $t("personalInformation.enabling1") }}</p>
                      <p><span>·</span>{{ $t("personalInformation.enabling2") }}</p>
                      <p><span>·</span> {{ $t("personalInformation.enabling3") }}</p>
                      <p><span>·</span> {{ $t("personalInformation.enabling4") }}</p>
                      <p><span>·</span> {{ $t("personalInformation.enabling5") }}</p>
                      <h3>{{ $t("personalInformation.enabling6") }}</h3>
                      <p>{{ $t("personalInformation.enabling7") }}</p>
                      <p>{{ $t("personalInformation.enabling8") }}</p>
                      <p>{{ $t("personalInformation.enabling9") }}</p>
                      <p>{{ $t("personalInformation.enabling10") }}</p>
                      <p>{{ $t("personalInformation.enabling11") }}</p>
                      <p>{{ $t("personalInformation.enabling12") }}</p>
                      <p>{{ $t("personalInformation.enabling13") }}</p>
                    </div>
                  </template>
                </el-alert>
              </el-col>
            </template>
          </el-row>
          <template v-if="form.step === props.step - 1">
            <el-result class="tw-m-auto" icon="success" :title="$t('personalInformation.enabling14')" :sub-title="$t('personalInformation.enabling15')"></el-result>
          </template>
        </el-form>
      </el-scrollbar>
    </template>
    <template #footer>
      <el-button type="default" @click="handleBeforeClose(null)" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <el-button type="primary" v-if="form.step < props.step - 1" @click="handleNext()" :disabled="data.submitLoading">{{ t("glob.Next") }}</el-button>
      <el-button type="primary" v-show="form.step === props.step - 1" @click="handleNext()" v-blur :loading="data.submitLoading">{{ t("glob.complete") }}</el-button>
      <div class="zoom-handle" @mousedown.self="handleZoom">
        <svg style="display: block; width: 60%; height: 60%; transform: translate(-25%, -25%); fill: currentColor; pointer-events: none" viewBox="0 0 1024 1024">
          <path d="M319.20128 974.56128L348.16 1003.52l655.36-655.36-28.95872-28.95872-655.36 655.36zM675.84 1003.52l327.68-327.68-28.95872-28.95872-327.68 327.68L675.84 1003.52z" fill="#000000"></path>
        </svg>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable no-unused-vars, @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, onBeforeUnmount, computed, watch, h, Ref } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { buildValidatorData } from "@/utils/validate";
import { ElForm, ElFormItem, ElInput, ElLink, ElMessage, ElMessageBox, ElRadio, ElRadioGroup } from "element-plus";
import { Lock, Phone, Message, ChatDotSquare } from "@element-plus/icons-vue";
import FormItem from "@/components/formItem/index.vue";
import { TypeHelper } from "@/utils/type";
import { bindCanvasImage } from "@/utils/image";
import { InputType } from "@/components/formItem";
import { captchaForImage, getTOTPGenerate, setTOTPGenerate, delTOTPGenerate, MFAMethod, MFAMethodOption } from "@/api/system";
import requestApi from "@/api/service/index";
import { SERVER, Method } from "@/api/service/common";
import type { Response } from "@/api/service/common";
import QRCode from "qrcode";

type Item = {
  step: number;
  mfaMethod: keyof typeof MFAMethod;
  mfaCode: string;
  certificate: string;
  captcha: string;
  code: string;
  totpQRCodeSrc: string;
};

interface Props {
  title: string;
  step?: number;
  mfaMethods: (keyof typeof MFAMethod)[];
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  step: 2,
  mfaMethods: () => [] as (keyof typeof MFAMethod)[],
  labelWidth: 116,
});

const emits = defineEmits<{
  (e: "done"): void;
}>();

const qrCodeCanvasRef = ref<HTMLCanvasElement>();
const formRef = ref<InstanceType<typeof ElForm>>();

const { t } = useI18n();

const width = ref(0);
const height = ref(0);

interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
});
const $params = ref<Partial<Item>>({});

const dialogPromise: any = {};
const dialogApiToken = ref<string>("");

type DefaultForm<T> = { [P in keyof T]: { value: T[P]; test: (v: any) => v is T[P]; transfer: (fv: any, ov: T[P]) => T[P] } };
const defaultForm = readonly<DefaultForm<Item>>({
  step: { value: 0, ...TypeHelper.number },
  mfaMethod: { value: props.mfaMethods[0], test: (v: any): v is MFAMethod => new RegExp(`^${Object.keys(MFAMethod).join("|")}$`, "g").test(v), transfer: (_, v) => props.mfaMethods[0] || v },
  mfaCode: { value: "", ...TypeHelper.string },
  code: { value: "", ...TypeHelper.string },
  certificate: { value: "", ...TypeHelper.string },
  captcha: { value: "", ...TypeHelper.string },
  totpQRCodeSrc: { value: "", ...TypeHelper.string },
});
const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

watch(
  () => props.mfaMethods,
  (mfaMethods) => {
    form.value.mfaMethod = mfaMethods[0];
  }
);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  form = cloneDeep(form);
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], util.value as never)) });
    },
    form as Required<Item>
  );
}
async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => {
      if (typeof formRef.value?.validate === "function") formRef.value.validate(resolve);
      else resolve(false);
    });
  } catch (error) {
    return false;
  }
}

/* TODO: 上一步 */
// async function handlePrev() {
//   data.submitLoading = true;
//   await nextTick();
//   if (!(await checkForm())) {
//     data.submitLoading = false;
//     return;
//   }
//   if (form.value.step > 0) --form.value.step;
//   data.submitLoading = false;
// }
/* TODO: 下一步 */
async function handleNext() {
  if (!formRef.value) return;
  data.submitLoading = true;
  await nextTick();
  formRef.value.clearValidate();
  try {
    switch (form.value.step) {
      // case 0:
      //   if (await new Promise((resolve) => formRef.value?.validateField(["mfaMethod", "mfaCode"], resolve))) {
      //     try {
      //       const { success, message, data } = await getTOTPGenerate({ mfaMethod: form.value.mfaMethod, mfaCode: form.value.mfaCode });
      //       if (!success) throw Object.assign(new Error(message), { success, data });
      //       form.value.step = 1;
      //       await nextTick();
      //       const maxHeight = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
      //       height.value = (((formRef.value as InstanceType<typeof ElForm>).$el as HTMLFormElement).clientHeight || 24) < maxHeight ? ((formRef.value as InstanceType<typeof ElForm>).$el as HTMLFormElement).clientHeight || 24 : maxHeight;
      //       if (!qrCodeCanvasRef.value) return;
      //       QRCode.toCanvas(qrCodeCanvasRef.value, data);
      //     } catch (error) {
      //       await nextTick(onChangeCaptcha);
      //       throw error;
      //     }
      //   }
      //   break;
      case 0:
        if (await new Promise((resolve) => formRef.value?.validateField(["code"], resolve))) {
          try {
            const { success, message, data } = await setTOTPGenerate({ code: form.value.code }, dialogApiToken.value);
            if (success) {
              form.value.step = 1;
              await nextTick();
              const maxHeight = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
              height.value = (((formRef.value as InstanceType<typeof ElForm>).$el as HTMLFormElement).clientHeight || 24) < maxHeight ? ((formRef.value as InstanceType<typeof ElForm>).$el as HTMLFormElement).clientHeight || 24 : maxHeight;
            } else Object.assign(new Error(message), { success, data });
          } catch (error) {
            if (error instanceof Error) ElMessage.error(error.message);
            throw error;
          }
        }
        break;
      case 1:
        dialogPromise.resolve(true);
        handleCancel();
        break;
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}

function handleBeforeClose(done) {
  dialogPromise.resject(false);
  handleCancel(done);
}

/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  try {
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  await onChangeCaptcha();
  form.value = await getForm($params.value);
  try {
    formRef.value && formRef.value.clearValidate();
    // await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }
  data.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
}
/**
 * TODO: 验证码
 */
const timer = ref<NodeJS.Timeout | undefined>();
const captchaImg = ref<HTMLCanvasElement>();
const loadingCaptcha = ref(false);
const coolingCaptcha = ref(0);
watch(coolingCaptcha, (cooling) => cooling !== 0 && setTimeout(() => coolingCaptcha.value !== 0 && (coolingCaptcha.value = cooling - 1), 1000));
async function onChangeCaptcha() {
  timer.value && clearTimeout(timer.value);
  timer.value = setTimeout(() => onChangeCaptcha(), 1000 * 60 * 5);
  form.value.captcha = "";
  form.value.certificate = String(`xxxxxxxx-xxxx-4xxx-yxxx-${Date.now().toString(16).padStart(12, "x")}`).replace(/[xy]/g, (c) => Number(c === "x" ? (Math.random() * 16) | 0 : (((Math.random() * 16) | 0) & 0x3) | 0x8).toString(16));
  await nextTick();
  if (!captchaImg.value) return;
  updateCertificate(captchaImg.value, form.value.certificate);
}
async function updateCertificate(canvasRef: HTMLCanvasElement, certificate: string) {
  try {
    const { success, data, message } = await captchaForImage({ certificate }, dialogApiToken.value);
    if (success) {
      await bindCanvasImage(canvasRef.getContext("2d") as CanvasRenderingContext2D, data);
    } else throw Object.assign(new Error(message), { success, data, message });
  } catch (error) {
    return;
  }
}
async function sendCertificate() {
  if (!formRef.value) return;
  formRef.value.clearValidate();
  loadingCaptcha.value = true;
  if (await send(form.value.mfaMethod)) coolingCaptcha.value = 60;
  loadingCaptcha.value = false;
}
async function send(method: keyof typeof MFAMethod) {
  let result: Promise<Response<unknown>> | undefined = undefined;
  switch (method) {
    case MFAMethod.PASSWORD:
      break;
    case MFAMethod.SMS:
      result = requestApi<unknown, Response<unknown>>({ url: `${SERVER.IAM}/current_user/mfa/code/sms`, method: Method.Post, responseType: "json", params: {}, data: {} });
      break;
    case MFAMethod.EMAIL:
      result = requestApi<unknown, Response<unknown>>({ url: `${SERVER.IAM}/current_user/mfa/code/email`, method: Method.Post, responseType: "json", params: {}, data: {} });
      break;
    case MFAMethod.TOTP:
      break;
  }
  try {
    if (!result) throw new Error("未知操作");
    const { success, data, message } = await result;
    if (success) {
      ElMessage.success("验证码发送成功！");
      return true;
    } else throw Object.assign(new Error(message), { success, data, message });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    return false;
  }
}

function close() {
  emits("done");
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  timer.value && clearTimeout(timer.value);
}
onBeforeUnmount(() => {
  close();
});

function handleZoom($event: MouseEvent) {
  const w = width.value;
  const h = height.value;
  ($event.target as HTMLElement).ownerDocument.onmousemove = (e: MouseEvent) => {
    e.preventDefault();
    if (w + (e.clientX - $event.clientX) * 2 < document.body.clientWidth - 200) width.value = w + (e.clientX - $event.clientX) * 2 > 360 ? w + (e.clientX - $event.clientX) * 2 : 360;
    else width.value = document.body.clientWidth - 200;
    if (h + (e.clientY - $event.clientY) * 1 < document.body.clientHeight - 260 - document.body.clientHeight * 0.15) height.value = h + (e.clientY - $event.clientY) * 1 > 24 ? h + (e.clientY - $event.clientY) * 1 : 24;
    else document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  };
  ($event.target as HTMLElement).ownerDocument.onmouseup = (e: MouseEvent) => {
    (e.target as HTMLElement).ownerDocument.onmousemove = null;
    (e.target as HTMLElement).ownerDocument.onmouseup = null;
  };
}

defineExpose({
  close: handleCancel,
  open: (token?: string) => {
    return new Promise((resolve, resject) => {
      nextTick(() => {
        dialogPromise.resolve = resolve;
        dialogPromise.resject = resject;

        dialogApiToken.value = token || "";
      });

      if (data.visible) {
        ElMessage.warning("先关闭其他弹窗再重试！");
      } else {
        data.visible = true;
        data.loading = true;
        data.submitLoading = true;
        nextTick(async () => {
          width.value = document.body.clientWidth / 2;
          await nextTick();
          const maxHeight = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
          height.value = (((formRef.value as InstanceType<typeof ElForm>).$el as HTMLFormElement).clientHeight || 24) < maxHeight ? ((formRef.value as InstanceType<typeof ElForm>).$el as HTMLFormElement).clientHeight || 24 : maxHeight;
          await handleReset();
          data.loading = false;
          data.submitLoading = false;
        });
      }

      nextTick(async () => {
        if (await new Promise((resolve) => formRef.value?.validateField(["mfaMethod", "mfaCode"], resolve))) {
          try {
            const { success, message, data } = await getTOTPGenerate({ mfaMethod: form.value.mfaMethod, mfaCode: form.value.mfaCode }, dialogApiToken.value);
            if (!success) throw Object.assign(new Error(message), { success, data });
            await nextTick();
            const maxHeight = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
            height.value = (((formRef.value as InstanceType<typeof ElForm>).$el as HTMLFormElement).clientHeight || 24) < maxHeight ? ((formRef.value as InstanceType<typeof ElForm>).$el as HTMLFormElement).clientHeight || 24 : maxHeight;
            if (!qrCodeCanvasRef.value) return;
            QRCode.toCanvas(qrCodeCanvasRef.value, data);
          } catch (error) {
            await nextTick(onChangeCaptcha);
            throw error;
          }
        }
      });
    });
  },
});
</script>

<style scoped lang="scss">
.title-alert {
  color: #000;
  p {
    display: flex;
    align-items: center;
    span {
      font-size: 20px;
      margin-right: 5px;
    }
  }
  h3 {
    color: #000;
  }
}
</style>
