/* eslint-disable no-console */
import { reactive, ref, nextTick, watch, h } from "vue";
import { <PERSON><PERSON><PERSON><PERSON>, ElButton, ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElLink, ElMessage, ElMessageBox, ElRadio, ElRadioGroup } from "element-plus";
import { User, Lock, Phone, Message, ChatDotSquare } from "@element-plus/icons-vue";
import { buildValidatorData } from "@/utils/validate";
import { bindFormBox } from "@/utils/bindFormBox";
import { bufferToBase64, stringToBuffer } from "@/utils/base64";
import { bindCanvasImage } from "@/utils/image";
import { setAccountGenerate, setPasswordGenerate, setPhoneGenerate, setEmailGenerate, delTOTPGenerate, MFAMethod, MFAMethodOption, setUserInfo, getPublicKey } from "@/api/system";
import requestApi from "@/api/service/index";
import { SERVER, Method } from "@/api/service/common";
import type { Response, RequestBase } from "@/api/service/common";
import { sendCommonSmsCode, sendCommonEmailCode, captchaForImage } from "@/api/system";
import { getTenantPasswordStrategyMsg, getUserPasswordStrategyMsg } from "@/api/personnel";
import _timeZone from "@/views/pages/common/contactsZone.json";
import { localesOption } from "@/api/locale";
import { i18n } from "@/lang/index";
const timeZone = ref(_timeZone);
const loadingCaptcha = ref(false);
const coolingCaptcha = ref(0);
watch(coolingCaptcha, (cooling) => cooling !== 0 && setTimeout(() => coolingCaptcha.value !== 0 && (coolingCaptcha.value = cooling - 1), 1000));

async function send(method: keyof typeof MFAMethod) {
  let result: Promise<Response<unknown>> | undefined = undefined;
  switch (method) {
    case MFAMethod.PASSWORD:
      break;
    case MFAMethod.SMS:
      result = requestApi<unknown, Response<unknown>>({ url: `${SERVER.IAM}/current_user/mfa/code/sms`, method: Method.Post, responseType: "json", params: {}, data: {} });
      break;
    case MFAMethod.EMAIL:
      result = requestApi<unknown, Response<unknown>>({ url: `${SERVER.IAM}/current_user/mfa/code/email`, method: Method.Post, responseType: "json", params: {}, data: {} });
      break;
    case MFAMethod.TOTP:
      break;
  }
  try {
    if (!result) throw new Error("未知操作");
    const { success, data, message } = await result;
    if (success) {
      ElMessage.success("验证码发送成功！");
      return true;
    } else throw Object.assign(new Error(message), { success, data, message });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    return false;
  }
}

export function putUsernameWizard(data: { id?: string; label?: string } & RequestBase) {
  // { title: `修改{data.label}`,
  console.log(data.label, "data.label");

  const $data = reactive({ title: i18n.global.t("personalInformation.Edit") + i18n.global.t(`personalInformation.${data.label}`), formRef: undefined as InstanceType<typeof ElForm> | undefined, message: "", checkWizard: false });
  const formWizard = ref({
    username: data.id || "",
  });
  if (data.label == "时区" || data.label == "姓名" || data.label == "语言") {
    return open(
      h({
        name: "MessageBoxWizard",
        setup: () => {
          return () =>
            h(
              ElForm,
              {
                ref: (vm) => ($data.formRef = vm as InstanceType<typeof ElForm>),
                model: formWizard.value,
                labelPosition: "top",
                onSubmit: (e: Event) => e.preventDefault(),
              },
              () => {
                if ($data.checkWizard) {
                  return [h("h3", { style: { fontSize: "var(--el-messagebox-font-size)", lineHeight: "1" } }, `${i18n.global.t("personalInformation.enabling21")} ${$data.title}`)];
                } else if ($data.message) {
                  return [h("h3", { style: { fontSize: "var(--el-messagebox-font-size)", lineHeight: "1", color: "var(--el-color-error)" } }, $data.message)];
                } else {
                  const rules = [buildValidatorData({ name: "required", title: data.label })];
                  const inputProps = {
                    "modelValue": formWizard.value.username,
                    "onUpdate:modelValue": ($event) => (formWizard.value.username = $event),
                    "placeholder": data.label === "时区" ? i18n.global.t("personalInformation.Time zone") : data.label === "姓名" ? i18n.global.t("personalInformation.The name") : i18n.global.t("personalInformation.A language"),
                    "prefixIcon": User,
                  };

                  if (data.label === "时区" || data.label === "语言") {
                    return [
                      h(ElFormItem, { key: "username", label: i18n.global.t(`personalInformation.${data.label}`), prop: "username", rules }, () =>
                        h(ElSelect, inputProps, () =>
                          data.label === "时区"
                            ? timeZone.value.map((zone) => h(ElOption, { key: zone.zoneId, label: zone.displayName, value: zone.zoneId }))
                            : localesOption.map(
                                (item) => h(ElOption, { key: item.value, label: item.label, value: item.value })
                                // h(ElOption, { key: item.value, value: item.value }, {
                                //   default: () => h('div', {
                                //     style: {
                                //       background: `url(${item.icon}) no-repeat left`,
                                //       paddingLeft: '35px',
                                //       height: '30px',
                                //       lineHeight: '30px'
                                //     }
                                //   }, item.label)
                                // })
                              )
                        )
                      ),
                    ];
                  } else {
                    // 姓名
                    return [h(ElFormItem, { key: "username", label: i18n.global.t(`personalInformation.${data.label}`), prop: "username", rules }, () => h(ElInput, inputProps))];
                  }
                }
              }
            );
        },
      }),
      $data,
      () => {
        const userInfo = data.label === "时区" ? { zoneId: formWizard.value.username } : data.label === "姓名" ? { name: formWizard.value.username } : { language: formWizard.value.username };
        return setUserInfo(userInfo);
      }
    );
  }
}
export function putNicknameWizard(defaultValue?: string) {
  const $data = reactive({ title: "修改昵称", formRef: undefined as InstanceType<typeof ElForm> | undefined, message: "", checkWizard: false });
  const formWizard = ref({
    nickname: defaultValue || "",
  });
  return open(
    h({
      name: "MessageBoxWizard",
      setup: () => {
        return () =>
          h(ElForm, { ref: (vm) => ($data.formRef = vm as InstanceType<typeof ElForm>), model: formWizard.value, labelPosition: "top", onSubmit: (e: Event) => e.preventDefault() }, () => {
            if ($data.checkWizard) return [h("h3", { style: { fontSize: "var(--el-messagebox-font-size)", lineHeight: "1" } }, `${i18n.global.t("personalInformation.enabling21")} ${$data.title}`)];
            else if ($data.message) return [h("h3", { style: { fontSize: "var(--el-messagebox-font-size)", lineHeight: "1", color: "var(--el-color-error)" } }, $data.message)];
            else return [h(ElFormItem, { key: "nickname", label: "昵称", prop: "nickname", rules: [buildValidatorData({ name: "required", title: "昵称" })] }, () => h(ElInput, { "modelValue": formWizard.value.nickname, "onUpdate:modelValue": ($event) => (formWizard.value.nickname = $event), "placeholder": "请输入昵称", "prefixIcon": User }))];
          });
      },
    }),
    $data,
    () => setUserInfo({ nickname: formWizard.value.nickname })
  );
}

export function bindAccountWizard(mfaMethods: (keyof typeof MFAMethod)[]) {
  const coolingCaptcha = ref(0);
  watch(coolingCaptcha, (cooling) => cooling !== 0 && setTimeout(() => coolingCaptcha.value !== 0 && (coolingCaptcha.value = cooling - 1), 1000));

  const coolingCaptcha2 = ref(0);
  watch(coolingCaptcha2, (cooling) => cooling !== 0 && setTimeout(() => coolingCaptcha2.value !== 0 && (coolingCaptcha2.value = cooling - 1), 1000));

  const $data = reactive({ title: i18n.global.t("personalInformation.Edit Username login ID"), formRef: undefined as InstanceType<typeof ElForm> | undefined, message: "", checkWizard: false });
  const formWizard = ref({
    mfaMethod: mfaMethods[0],
    mfaCode: "",
    newAccount: "",
  });
  return open(
    h({
      name: "MessageBoxWizard",
      setup: () => {
        async function sendCertificate() {
          if (!$data.formRef) return;
          $data.formRef.clearValidate();
          loadingCaptcha.value = true;
          if (await send(formWizard.value.mfaMethod)) {
            switch (formWizard.value.mfaMethod) {
              case MFAMethod.PASSWORD:
                break;
              case MFAMethod.SMS:
                coolingCaptcha.value = 60;
                break;
              case MFAMethod.EMAIL:
                coolingCaptcha2.value = 60;
                break;
              case MFAMethod.TOTP:
                break;
            }
          }
          loadingCaptcha.value = false;
        }
        return () => {
          const formMethod = h(ElFormItem, { label: i18n.global.t("personalInformation.yz"), prop: "mfaMethods" }, () => h(ElRadioGroup, { "modelValue": formWizard.value.mfaMethod, "onUpdate:modelValue": ($event) => ((formWizard.value.mfaMethod = $event as keyof typeof MFAMethod), (formWizard.value.mfaCode = "")) }, () => mfaMethods.map((method) => h(ElRadio, { key: `mfa_method_${method}`, label: method }, () => MFAMethodOption.find(({ value }) => value === method)?.label || method))));
          function formItemRender() {
            switch (formWizard.value.mfaMethod) {
              case MFAMethod.PASSWORD:
                return h(ElFormItem, { key: "PASSWORD_Method", label: i18n.global.t("personalInformation.Password"), prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.Password") }), buildValidatorData({ name: "password", title: i18n.global.t("personalInformation.Password") })] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": i18n.global.t("personalInformation.Please enter your password"), "prefixIcon": Lock, "type": "password", "showPassword": true, "clearable": true }));
              case MFAMethod.SMS:
                return h(ElFormItem, { key: "SMS_Method", label: "短信验证码", prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: "短信验证码" }), { type: "string", min: 4, max: 8, message: "短信验证码需要在4-8位字符", trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": "短信验证码", "prefixIcon": ChatDotSquare, "clearable": true }, { suffix: () => h(ElLink, { type: "primary", disabled: loadingCaptcha.value || coolingCaptcha.value !== 0, underline: false, onClick: () => sendCertificate() }, () => `获取验证码${coolingCaptcha.value ? `（${coolingCaptcha.value}秒）` : ""}`) }));
              case MFAMethod.EMAIL:
                return h(ElFormItem, { key: "EMAIL_Method", label: "邮箱验证码", prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: "邮箱验证码" }), { type: "string", min: 4, max: 8, message: "邮箱验证码需要在4-8位字符", trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": "邮箱验证码", "prefixIcon": ChatDotSquare, "clearable": true }, { suffix: () => h(ElLink, { type: "primary", disabled: loadingCaptcha.value || coolingCaptcha2.value !== 0, underline: false, onClick: () => sendCertificate() }, () => `获取验证码${coolingCaptcha2.value ? `（${coolingCaptcha2.value}秒）` : ""}`) }));
              case MFAMethod.TOTP:
                return h(ElFormItem, { key: "TOTP_Method", label: i18n.global.t("personalInformation.enabling17"), prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.enabling17") }), { type: "string", min: 4, max: 8, message: i18n.global.t("personalInformation.enabling18"), trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": i18n.global.t("personalInformation.enabling17"), "prefixIcon": ChatDotSquare, "clearable": true }));
            }
          }
          return h(ElForm, { ref: (vm) => ($data.formRef = vm as InstanceType<typeof ElForm>), model: formWizard.value, labelPosition: "top", onSubmit: (e: Event) => e.preventDefault() }, () => {
            if ($data.checkWizard) return [h("h3", { style: { fontSize: "var(--el-messagebox-font-size)", lineHeight: "1" } }, `${i18n.global.t("personalInformation.enabling21")} ${$data.title}`)];
            else if ($data.message) return [h("h3", { style: { fontSize: "var(--el-messagebox-font-size)", lineHeight: "1", color: "var(--el-color-error)" } }, $data.message)];
            else return [formMethod, formItemRender(), h(ElFormItem, { key: "newAccount", label: i18n.global.t("personalInformation.Username login ID"), prop: "newAccount", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.Username login ID") }), buildValidatorData({ name: "account", title: "" })] }, () => h(ElInput, { "modelValue": formWizard.value.newAccount, "onUpdate:modelValue": ($event) => (formWizard.value.newAccount = $event), "placeholder": i18n.global.t("personalInformation.Please enter your username"), "prefixIcon": User }))];
          });
        };
      },
    }),
    $data,
    async () => {
      const importPublicKey = await getPublicKey();
      const _mfaCode = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(formWizard.value.mfaCode));

      return await setAccountGenerate({ mfaMethod: formWizard.value.mfaMethod, mfaCode: bufferToBase64(_mfaCode), newAccount: formWizard.value.newAccount, ptype: "RSA" });
    }
  );
}
export async function bindPasswordWizard(mfaMethods: (keyof typeof MFAMethod)[]) {
  const passwordhardening = ref(false);
  const coolingCaptcha = ref(0);
  watch(coolingCaptcha, (cooling) => cooling !== 0 && setTimeout(() => coolingCaptcha.value !== 0 && (coolingCaptcha.value = cooling - 1), 1000));

  const coolingCaptcha2 = ref(0);
  watch(coolingCaptcha2, (cooling) => cooling !== 0 && setTimeout(() => coolingCaptcha2.value !== 0 && (coolingCaptcha2.value = cooling - 1), 1000));
  const $data = reactive({ title: i18n.global.t("personalInformation.Change pwd"), formRef: undefined as InstanceType<typeof ElForm> | undefined, message: "", checkWizard: false });
  const formWizard = ref({
    mfaMethod: MFAMethod.PASSWORD as keyof typeof MFAMethod,
    mfaCode: "",
    newPassword: "",
    confirmationPassword: "",
  });
  const { success, data } = await getUserPasswordStrategyMsg({});
  if (success) {
    passwordhardening.value = data.hardening;
  }

  return open(
    h({
      name: "MessageBoxWizard",
      setup: () => {
        async function sendCertificate() {
          if (!$data.formRef) return;
          $data.formRef.clearValidate();
          loadingCaptcha.value = true;
          if (await send(formWizard.value.mfaMethod)) {
            switch (formWizard.value.mfaMethod) {
              case MFAMethod.PASSWORD:
                break;
              case MFAMethod.SMS:
                coolingCaptcha.value = 60;
                break;
              case MFAMethod.EMAIL:
                coolingCaptcha2.value = 60;
                break;
              case MFAMethod.TOTP:
                break;
            }
          }
          loadingCaptcha.value = false;
        }
        return () => {
          const formMethod = h(ElFormItem, { label: i18n.global.t("personalInformation.yz"), prop: "mfaMethods" }, () => h(ElRadioGroup, { "modelValue": formWizard.value.mfaMethod, "onUpdate:modelValue": ($event) => ((formWizard.value.mfaMethod = $event as keyof typeof MFAMethod), (formWizard.value.mfaCode = "")) }, () => Array.from(new Set([MFAMethod.PASSWORD, ...mfaMethods])).map((method) => h(ElRadio, { key: `mfa_method_${method}`, label: method }, () => MFAMethodOption.find(({ value }) => value === method)?.label || method))));
          function formItemRender() {
            let hardeningType;
            if (passwordhardening.value) {
              hardeningType = h(ElFormItem, { key: "PASSWORD_Method", label: i18n.global.t("personalInformation.Password"), prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.Password") }), buildValidatorData({ name: "password", title: i18n.global.t("personalInformation.Password") })] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": i18n.global.t("personalInformation.Please enter your password"), "prefixIcon": Lock, "type": "password", "showPassword": true, "clearable": true }));
            } else {
              hardeningType = h(ElFormItem, { key: "PASSWORD_Method", label: i18n.global.t("personalInformation.Password"), prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.Password") })] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": i18n.global.t("personalInformation.Please enter your password"), "prefixIcon": Lock, "type": "password", "showPassword": true, "clearable": true }));
            }

            switch (formWizard.value.mfaMethod) {
              case MFAMethod.PASSWORD:
                return hardeningType;
              case MFAMethod.SMS:
                return h(ElFormItem, { key: "SMS_Method", label: "短信验证码", prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: "短信验证码" }), { type: "string", min: 4, max: 8, message: "短信验证码需要在4-8位字符", trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": "短信验证码", "prefixIcon": ChatDotSquare, "clearable": true }, { suffix: () => h(ElLink, { type: "primary", disabled: loadingCaptcha.value || coolingCaptcha.value !== 0, underline: false, onClick: () => sendCertificate() }, () => `获取验证码${coolingCaptcha.value ? `（${coolingCaptcha.value}秒）` : ""}`) }));
              case MFAMethod.EMAIL:
                return h(ElFormItem, { key: "EMAIL_Method", label: "邮箱验证码", prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: "邮箱验证码" }), { type: "string", min: 4, max: 8, message: "邮箱验证码需要在4-8位字符", trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": "邮箱验证码", "prefixIcon": ChatDotSquare, "clearable": true }, { suffix: () => h(ElLink, { type: "primary", disabled: loadingCaptcha.value || coolingCaptcha2.value !== 0, underline: false, onClick: () => sendCertificate() }, () => `获取验证码${coolingCaptcha2.value ? `（${coolingCaptcha2.value}秒）` : ""}`) }));
              case MFAMethod.TOTP:
                return h(ElFormItem, { key: "TOTP_Method", label: i18n.global.t("personalInformation.enabling17"), prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.enabling17") }), { type: "string", min: 4, max: 8, message: i18n.global.t("personalInformation.enabling18"), trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": i18n.global.t("personalInformation.enabling17"), "prefixIcon": ChatDotSquare, "clearable": true }));
            }
          }
          let verifyType;
          if (passwordhardening.value) {
            verifyType = [formMethod, formItemRender(), h(ElFormItem, { key: "newPassword", label: i18n.global.t("personalInformation.new password"), prop: "newPassword", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.Password") }), buildValidatorData({ name: "password", title: i18n.global.t("personalInformation.Password") })] }, () => h(ElInput, { "modelValue": formWizard.value.newPassword, "onUpdate:modelValue": ($event) => (formWizard.value.newPassword = $event), "placeholder": i18n.global.t("personalInformation.Please enter a new password"), "prefixIcon": Lock, "type": "password", "showPassword": true })), h(ElFormItem, { key: "confirmationPassword", label: i18n.global.t("personalInformation.confirmation password"), prop: "confirmationPassword", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.Password") }), { validator: (_rule, value, callback) => callback(value === formWizard.value.newPassword ? undefined : "两次密码需要一致"), trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.confirmationPassword, "onUpdate:modelValue": ($event) => (formWizard.value.confirmationPassword = $event), "placeholder": i18n.global.t("personalInformation.Please enter the confirmation password"), "prefixIcon": Lock, "type": "password", "showPassword": true }))];
          } else {
            verifyType = [formMethod, formItemRender(), h(ElFormItem, { key: "newPassword", label: i18n.global.t("personalInformation.new password"), prop: "newPassword", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.Password") })] }, () => h(ElInput, { "modelValue": formWizard.value.newPassword, "onUpdate:modelValue": ($event) => (formWizard.value.newPassword = $event), "placeholder": i18n.global.t("personalInformation.Please enter a new password"), "prefixIcon": Lock, "type": "password", "showPassword": true })), h(ElFormItem, { key: "confirmationPassword", label: i18n.global.t("personalInformation.confirmation password"), prop: "confirmationPassword", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.Password") }), { validator: (_rule, value, callback) => callback(value === formWizard.value.newPassword ? undefined : i18n.global.t("personalInformation.The two passwords must be the same")), trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.confirmationPassword, "onUpdate:modelValue": ($event) => (formWizard.value.confirmationPassword = $event), "placeholder": i18n.global.t("personalInformation.Please enter your password"), "prefixIcon": Lock, "type": "password", "showPassword": true }))];
          }
          return h(ElForm, { ref: (vm) => ($data.formRef = vm as InstanceType<typeof ElForm>), model: formWizard.value, labelPosition: "top", onSubmit: (e: Event) => e.preventDefault() }, () => {
            //  // console.log($data, 555555);
            if ($data.checkWizard) return [h("h3", { style: { fontSize: "var(--el-messagebox-font-size)", lineHeight: "1" } }, `${i18n.global.t("personalInformation.enabling21")} ${$data.title}`)];
            else if ($data.message) return [h("h3", { style: { fontSize: "var(--el-messagebox-font-size)", lineHeight: "1", color: "var(--el-color-error)" } }, $data.message)];
            else return verifyType;
          });
        };
      },
    }),
    $data,
    async () => {
      const importPublicKey = await getPublicKey();
      const _newPassword = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(formWizard.value.newPassword));
      const _confirmationPassword = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(formWizard.value.confirmationPassword));
      const _mfaCode = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(formWizard.value.mfaCode));

      return await setPasswordGenerate({ mfaMethod: formWizard.value.mfaMethod, mfaCode: bufferToBase64(_mfaCode), newPassword: bufferToBase64(_newPassword), confirmationPassword: bufferToBase64(_confirmationPassword), ptype: "RSA" });
    }
  );
}
export function bindPhoneWizard(mfaMethods: (keyof typeof MFAMethod)[]) {
  const coolingCaptcha = ref(0);
  watch(coolingCaptcha, (cooling) => cooling !== 0 && setTimeout(() => coolingCaptcha.value !== 0 && (coolingCaptcha.value = cooling - 1), 1000));

  const coolingCaptcha2 = ref(0);
  watch(coolingCaptcha2, (cooling) => cooling !== 0 && setTimeout(() => coolingCaptcha2.value !== 0 && (coolingCaptcha2.value = cooling - 1), 1000));

  const $data = reactive({ title: i18n.global.t("Edit Mobile"), formRef: undefined as InstanceType<typeof ElForm> | undefined, message: "", checkWizard: false });
  const formWizard = ref({
    mfaMethod: mfaMethods[0],
    mfaCode: "",
    newPhone: "",
    smsCode: "",
  });
  return open(
    h({
      name: "MessageBoxWizard",
      setup: () => {
        async function sendCertificate() {
          if (!$data.formRef) return;
          $data.formRef.clearValidate();
          loadingCaptcha.value = true;
          if (await send(formWizard.value.mfaMethod)) {
            switch (formWizard.value.mfaMethod) {
              case MFAMethod.PASSWORD:
                break;
              case MFAMethod.SMS:
                coolingCaptcha.value = 60;
                break;
              case MFAMethod.EMAIL:
                coolingCaptcha2.value = 60;
                break;
              case MFAMethod.TOTP:
                break;
            }
          }
          loadingCaptcha.value = false;
        }
        const getSmsCodeText = ref(i18n.global.t("personalInformation.Obtain verification code"));
        const getSmsCodeState = ref(false);
        return () => {
          const formMethod = h(ElFormItem, { label: i18n.global.t("personalInformation.yz"), prop: "mfaMethods" }, () => h(ElRadioGroup, { "modelValue": formWizard.value.mfaMethod, "onUpdate:modelValue": ($event) => ((formWizard.value.mfaMethod = $event as keyof typeof MFAMethod), (formWizard.value.mfaCode = "")) }, () => mfaMethods.map((method) => h(ElRadio, { key: `mfa_method_${method}`, label: method }, () => MFAMethodOption.find(({ value }) => value === method)?.label || method))));
          function formItemRender() {
            // // console.log(formWizard.value.mfaMethod);
            switch (formWizard.value.mfaMethod) {
              case MFAMethod.PASSWORD:
                return h(ElFormItem, { key: "PASSWORD_Method", label: i18n.global.t("personalInformation.Password"), prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.Password") }), buildValidatorData({ name: "password", title: i18n.global.t("personalInformation.Password") })] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": i18n.global.t("personalInformation.Please enter your password"), "prefixIcon": Lock, "type": "password", "showPassword": true, "clearable": true }));
              case MFAMethod.SMS:
                return h(ElFormItem, { key: "SMS_Method", label: "短信验证码", prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: "短信验证码" }), { type: "string", min: 4, max: 8, message: "短信验证码需要在4-8位字符", trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": "短信验证码", "prefixIcon": ChatDotSquare, "clearable": true }, { suffix: () => h(ElLink, { type: "primary", disabled: loadingCaptcha.value || coolingCaptcha.value !== 0, underline: false, onClick: () => sendCertificate() }, () => `获取验证码${coolingCaptcha.value ? `（${coolingCaptcha.value}秒）` : ""}`) }));
              case MFAMethod.EMAIL:
                return h(ElFormItem, { key: "EMAIL_Method", label: "邮箱验证码", prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: "邮箱验证码" }), { type: "string", min: 4, max: 8, message: "邮箱验证码需要在4-8位字符", trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": "邮箱验证码", "prefixIcon": ChatDotSquare, "clearable": true }, { suffix: () => h(ElLink, { type: "primary", disabled: loadingCaptcha.value || coolingCaptcha2.value !== 0, underline: false, onClick: () => sendCertificate() }, () => `获取验证码${coolingCaptcha2.value ? `（${coolingCaptcha2.value}秒）` : ""}`) }));
              case MFAMethod.TOTP:
                return h(ElFormItem, { key: "TOTP_Method", label: i18n.global.t("personalInformation.enabling17"), prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.enabling17") }), { type: "string", min: 4, max: 8, message: i18n.global.t("personalInformation.enabling18"), trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": i18n.global.t("personalInformation.enabling17"), "prefixIcon": ChatDotSquare, "clearable": true }));
            }
          }
          return h(ElForm, { ref: (vm) => ($data.formRef = vm as InstanceType<typeof ElForm>), model: formWizard.value, labelPosition: "top", onSubmit: (e: Event) => e.preventDefault() }, () => {
            if ($data.checkWizard) return [h("h3", { style: { fontSize: "var(--el-messagebox-font-size)", lineHeight: "1" } }, `${i18n.global.t("personalInformation.enabling21")} ${$data.title}`)];
            else if ($data.message) return [h("h3", { style: { fontSize: "var(--el-messagebox-font-size)", lineHeight: "1", color: "var(--el-color-error)" } }, $data.message)];
            else
              return [
                formMethod,
                formItemRender(),
                [
                  h(ElFormItem, { key: "newPhone", label: i18n.global.t("personalInformation.New phone number"), prop: "newPhone", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.New phone number") }), buildValidatorData({ name: "mobile", title: "" })] }, () =>
                    h("div", { style: "display:flex;width: 100%" }, [
                      h(ElInput, { "modelValue": formWizard.value.newPhone, "onUpdate:modelValue": ($event) => (formWizard.value.newPhone = $event), "placeholder": i18n.global.t("personalInformation.Please enter New phone number"), "prefixIcon": Phone }),
                      h(
                        ElButton,
                        {
                          type: "primary",
                          disabled: getSmsCodeState.value,
                          onClick: async () => {
                            // if (!formWizard.value.newPhone) return new Error("手机号不能为空");
                            if (!$data.formRef) return;
                            try {
                              $data.formRef.clearValidate();
                              if (await new Promise((resolve) => $data.formRef && $data.formRef.validateField("newPhone", resolve))) {
                                await inputCertificate(($form) => sendCommonSmsCode({ phone: formWizard.value.newPhone, ...$form }));
                                let num = 60;
                                getSmsCodeState.value = true;
                                const timer = setInterval(() => {
                                  num--;
                                  getSmsCodeText.value = `${num}秒后重新获取`;
                                  if (num === 0) {
                                    getSmsCodeText.value = "获取验证码";
                                    getSmsCodeState.value = false;
                                    window.clearInterval(timer);
                                  }
                                }, 1000);
                              }
                            } catch (error) {
                              return;
                            }
                          },
                        },
                        () => getSmsCodeText.value
                      ),
                    ])
                  ),
                  h(ElFormItem, { key: "smsCode", label: i18n.global.t("personalInformation.Verification code"), prop: "smsCode", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.Verification code") })] }, () => h(ElInput, { "modelValue": formWizard.value.smsCode, "onUpdate:modelValue": ($event) => (formWizard.value.smsCode = $event), "placeholder": i18n.global.t("personalInformation.Please enter the verification code"), "prefixIcon": Message })),
                ],
              ];
          });
        };
      },
    }),
    $data,
    async () => {
      const importPublicKey = await getPublicKey();
      const _mfaCode = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(formWizard.value.mfaCode));
      const _smsCode = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(formWizard.value.smsCode));

      return await setPhoneGenerate({ mfaMethod: formWizard.value.mfaMethod, mfaCode: bufferToBase64(_mfaCode), newPhone: formWizard.value.newPhone, smsCode: bufferToBase64(_smsCode), ptype: "RSA" });
    }
  );
}
export function bindEmailWizard(mfaMethods: (keyof typeof MFAMethod)[]) {
  const coolingCaptcha = ref(0);
  watch(coolingCaptcha, (cooling) => cooling !== 0 && setTimeout(() => coolingCaptcha.value !== 0 && (coolingCaptcha.value = cooling - 1), 1000));

  const coolingCaptcha2 = ref(0);
  watch(coolingCaptcha2, (cooling) => cooling !== 0 && setTimeout(() => coolingCaptcha2.value !== 0 && (coolingCaptcha2.value = cooling - 1), 1000));

  const $data = reactive({ title: i18n.global.t("personalInformation.Edit Email"), formRef: undefined as InstanceType<typeof ElForm> | undefined, message: "", checkWizard: false });
  const formWizard = ref({
    mfaMethod: mfaMethods[0],
    mfaCode: "",
    newEmail: "",
    emailCode: "",
  });
  return open(
    h({
      name: "MessageBoxWizard",
      setup: () => {
        async function sendCertificate() {
          if (!$data.formRef) return;
          $data.formRef.clearValidate();
          loadingCaptcha.value = true;
          if (await send(formWizard.value.mfaMethod)) {
            switch (formWizard.value.mfaMethod) {
              case MFAMethod.PASSWORD:
                break;
              case MFAMethod.SMS:
                coolingCaptcha.value = 60;
                break;
              case MFAMethod.EMAIL:
                coolingCaptcha2.value = 60;
                break;
              case MFAMethod.TOTP:
                break;
            }
          }
          loadingCaptcha.value = false;
        }
        const getEmailCodeText = ref(i18n.global.t("personalInformation.Obtain verification code"));
        const getEmailCodeState = ref(false);
        return () => {
          const formMethod = h(ElFormItem, { label: i18n.global.t("personalInformation.yz"), prop: "mfaMethods" }, () => h(ElRadioGroup, { "modelValue": formWizard.value.mfaMethod, "onUpdate:modelValue": ($event) => ((formWizard.value.mfaMethod = $event as keyof typeof MFAMethod), (formWizard.value.mfaCode = "")) }, () => mfaMethods.map((method) => h(ElRadio, { key: `mfa_method_${method}`, label: method }, () => MFAMethodOption.find(({ value }) => value === method)?.label || method))));
          function formItemRender() {
            switch (formWizard.value.mfaMethod) {
              case MFAMethod.PASSWORD:
                return h(ElFormItem, { key: "PASSWORD_Method", label: i18n.global.t("personalInformation.Password"), prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.Password") }), buildValidatorData({ name: "password", title: i18n.global.t("personalInformation.Password") })] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": i18n.global.t("personalInformation.Please enter your password"), "prefixIcon": Lock, "type": "password", "showPassword": true, "clearable": true }));
              case MFAMethod.SMS:
                return h(ElFormItem, { key: "SMS_Method", label: "短信验证码", prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: "短信验证码" }), { type: "string", min: 4, max: 8, message: "短信验证码需要在4-8位字符", trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": "短信验证码", "prefixIcon": ChatDotSquare, "clearable": true }, { suffix: () => h(ElLink, { type: "primary", disabled: loadingCaptcha.value || coolingCaptcha.value !== 0, underline: false, onClick: () => sendCertificate() }, () => `获取验证码${coolingCaptcha.value ? `（${coolingCaptcha.value}秒）` : ""}`) }));
              case MFAMethod.EMAIL:
                return h(ElFormItem, { key: "EMAIL_Method", label: "邮箱验证码", prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: "邮箱验证码" }), { type: "string", min: 4, max: 8, message: "邮箱验证码需要在4-8位字符", trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": "邮箱验证码", "prefixIcon": ChatDotSquare, "clearable": true }, { suffix: () => h(ElLink, { type: "primary", disabled: loadingCaptcha.value || coolingCaptcha2.value !== 0, underline: false, onClick: () => sendCertificate() }, () => `获取验证码${coolingCaptcha2.value ? `（${coolingCaptcha2.value}秒）` : ""}`) }));
              case MFAMethod.TOTP:
                return h(ElFormItem, { key: "TOTP_Method", label: i18n.global.t("personalInformation.enabling17"), prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.enabling17") }), { type: "string", min: 4, max: 8, message: i18n.global.t("personalInformation.enabling18"), trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": i18n.global.t("personalInformation.enabling17"), "prefixIcon": ChatDotSquare, "clearable": true }));
            }
          }
          return h(ElForm, { ref: (vm) => ($data.formRef = vm as InstanceType<typeof ElForm>), model: formWizard.value, labelPosition: "top", onSubmit: (e: Event) => e.preventDefault() }, () => {
            if ($data.checkWizard) return [h("h3", { style: { fontSize: "var(--el-messagebox-font-size)", lineHeight: "1" } }, `${i18n.global.t("personalInformation.enabling21")} ${$data.title}`)];
            else if ($data.message) return [h("h3", { style: { fontSize: "var(--el-messagebox-font-size)", lineHeight: "1", color: "var(--el-color-error)" } }, $data.message)];
            else
              return [
                formMethod,
                formItemRender(),
                [
                  h(ElFormItem, { key: "newEmail", label: i18n.global.t("personalInformation.New email address"), prop: "newEmail", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.Email") }), buildValidatorData({ name: "email", title: i18n.global.t("personalInformation.Email") })] }, () =>
                    h("div", { style: "display:flex;width: 100%" }, [
                      h(ElInput, { "modelValue": formWizard.value.newEmail, "onUpdate:modelValue": ($event) => (formWizard.value.newEmail = $event), "placeholder": i18n.global.t("personalInformation.Please address"), "prefixIcon": Message }),
                      h(
                        ElButton,
                        {
                          type: "primary",
                          disabled: getEmailCodeState.value,
                          onClick: async () => {
                            if (!$data.formRef) return;
                            try {
                              $data.formRef.clearValidate();
                              if (await new Promise((resolve) => $data.formRef && $data.formRef.validateField("newEmail", resolve))) {
                                await inputCertificate(($form) => sendCommonEmailCode({ email: formWizard.value.newEmail, ...$form }));
                                let num = 60;
                                getEmailCodeState.value = true;
                                const timer = setInterval(() => {
                                  num--;
                                  getEmailCodeText.value = `${num}${i18n.global.t("personalInformation.Retrieve")}`;
                                  if (num === 0) {
                                    getEmailCodeText.value = i18n.global.t("personalInformation.Obtain verification code");
                                    getEmailCodeState.value = false;
                                    window.clearInterval(timer);
                                  }
                                }, 1000);
                              }
                            } catch (error) {
                              //   // console.log(error);
                              return;
                            }
                          },
                        },
                        () => getEmailCodeText.value
                      ),
                    ])
                  ),
                  h(ElFormItem, { key: "emailCode", label: i18n.global.t("personalInformation.Verification code"), prop: "emailCode", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.Verification code") })] }, () => h(ElInput, { "modelValue": formWizard.value.emailCode, "onUpdate:modelValue": ($event) => (formWizard.value.emailCode = $event), "placeholder": i18n.global.t("personalInformation.Please enter the verification code"), "prefixIcon": ChatDotSquare })),
                ],
              ];
          });
        };
      },
    }),
    $data,
    async () => {
      const importPublicKey = await getPublicKey();
      const _mfaCode = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(formWizard.value.mfaCode));
      const _emailCode = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(formWizard.value.emailCode));

      return await setEmailGenerate({ mfaMethod: formWizard.value.mfaMethod, mfaCode: bufferToBase64(_mfaCode), newEmail: formWizard.value.newEmail, emailCode: bufferToBase64(_emailCode), ptype: "RSA" });
    }
  );
}
export function bindMFAWizard(mfaMethods: (keyof typeof MFAMethod)[]) {
  const coolingCaptcha = ref(0);
  watch(coolingCaptcha, (cooling) => cooling !== 0 && setTimeout(() => coolingCaptcha.value !== 0 && (coolingCaptcha.value = cooling - 1), 1000));

  const coolingCaptcha2 = ref(0);
  watch(coolingCaptcha2, (cooling) => cooling !== 0 && setTimeout(() => coolingCaptcha2.value !== 0 && (coolingCaptcha2.value = cooling - 1), 1000));

  const $data = reactive({ title: i18n.global.t("personalInformation.enabling16"), formRef: undefined as InstanceType<typeof ElForm> | undefined, message: "", checkWizard: false });
  const formWizard = ref({
    mfaMethod: mfaMethods[0],
    mfaCode: "",
  });
  return open(
    h({
      name: "MessageBoxWizard",
      setup: () => {
        async function sendCertificate() {
          if (!$data.formRef) return;
          $data.formRef.clearValidate();
          loadingCaptcha.value = true;
          if (await send(formWizard.value.mfaMethod)) {
            switch (formWizard.value.mfaMethod) {
              case MFAMethod.PASSWORD:
                break;
              case MFAMethod.SMS:
                coolingCaptcha.value = 60;
                break;
              case MFAMethod.EMAIL:
                coolingCaptcha2.value = 60;
                break;
              case MFAMethod.TOTP:
                break;
            }
          }
          loadingCaptcha.value = false;
        }
        return () => {
          const formMethod = h(ElFormItem, { label: i18n.global.t("personalInformation.yz"), prop: "mfaMethods" }, () => h(ElRadioGroup, { "modelValue": formWizard.value.mfaMethod, "onUpdate:modelValue": ($event) => ((formWizard.value.mfaMethod = $event as keyof typeof MFAMethod), (formWizard.value.mfaCode = "")) }, () => mfaMethods.map((method) => h(ElRadio, { key: `mfa_method_${method}`, label: method }, () => MFAMethodOption.find(({ value }) => value === method)?.label || method))));
          function formItemRender() {
            switch (formWizard.value.mfaMethod) {
              case MFAMethod.PASSWORD:
                return h(ElFormItem, { key: "PASSWORD_Method", label: i18n.global.t("personalInformation.Password"), prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.Password") }), buildValidatorData({ name: "password", title: i18n.global.t("personalInformation.Password") })] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": i18n.global.t("personalInformation.Please enter your password"), "prefixIcon": Lock, "type": "password", "showPassword": true, "clearable": true }));
              case MFAMethod.SMS:
                return h(ElFormItem, { key: "SMS_Method", label: "短信验证码", prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: "短信验证码" }), { type: "string", min: 4, max: 8, message: "短信验证码需要在4-8位字符", trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": "短信验证码", "prefixIcon": ChatDotSquare, "clearable": true }, { suffix: () => h(ElLink, { type: "primary", disabled: loadingCaptcha.value || coolingCaptcha.value !== 0, underline: false, onClick: () => sendCertificate() }, () => `获取验证码${coolingCaptcha.value ? `（${coolingCaptcha.value}秒）` : ""}`) }));
              case MFAMethod.EMAIL:
                return h(ElFormItem, { key: "EMAIL_Method", label: "邮箱验证码", prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: "邮箱验证码" }), { type: "string", min: 4, max: 8, message: "邮箱验证码需要在4-8位字符", trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": "邮箱验证码", "prefixIcon": ChatDotSquare, "clearable": true }, { suffix: () => h(ElLink, { type: "primary", disabled: loadingCaptcha.value || coolingCaptcha2.value !== 0, underline: false, onClick: () => sendCertificate() }, () => `获取验证码${coolingCaptcha2.value ? `（${coolingCaptcha2.value}秒）` : ""}`) }));
              case MFAMethod.TOTP:
                return h(ElFormItem, { key: "TOTP_Method", label: i18n.global.t("personalInformation.enabling17"), prop: "mfaCode", rules: [buildValidatorData({ name: "required", title: i18n.global.t("personalInformation.enabling17") }), { type: "string", min: 4, max: 8, message: i18n.global.t("personalInformation.enabling18"), trigger: "blur" }] }, () => h(ElInput, { "modelValue": formWizard.value.mfaCode, "onUpdate:modelValue": ($event) => (formWizard.value.mfaCode = $event), "placeholder": i18n.global.t("personalInformation.enabling17"), "prefixIcon": ChatDotSquare, "clearable": true }));
            }
          }
          return h(ElForm, { ref: (vm) => ($data.formRef = vm as InstanceType<typeof ElForm>), model: formWizard.value, labelPosition: "top", onSubmit: (e: Event) => e.preventDefault() }, () => {
            if ($data.checkWizard) return [h("h3", { style: { fontSize: "var(--el-messagebox-font-size)", lineHeight: "1" } }, `${i18n.global.t("personalInformation.enabling21")} ${$data.title}`)];
            else if ($data.message) return [h("h3", { style: { fontSize: "var(--el-messagebox-font-size)", lineHeight: "1", color: "var(--el-color-error)" } }, $data.message)];
            else return [formMethod, formItemRender()];
          });
        };
      },
    }),
    $data,
    async () => {
      const importPublicKey = await getPublicKey();
      const _mfaCode = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, importPublicKey, stringToBuffer(formWizard.value.mfaCode));

      return await delTOTPGenerate({ mfaMethod: formWizard.value.mfaMethod, mfaCode: bufferToBase64(_mfaCode), ptype: "RSA" });
    }
  );
}

interface WizardData {
  formRef?: InstanceType<typeof ElForm>;
  message: string;
  checkWizard: boolean;
  title: string;
}

/**
 * 打开弹窗方法
 *
 * @param {import("vue").VNode} message
 * @param {WizardData} $data
 * @param {() => Promise<{ success: boolean; message: string }>} callback
 */
async function open(message: import("vue").VNode, $data: WizardData, callback: () => Promise<{ success: boolean; message: string }>) {
  await nextTick();
  try {
    await ElMessageBox({
      title: $data.title,
      message,
      showClose: false,
      showCancelButton: true,
      showConfirmButton: true,
      closeOnClickModal: false,
      closeOnPressEscape: false,
      draggable: true,
      async beforeClose(action, instance, done) {
        if (action === "confirm") {
          await nextTick();
          if ($data.checkWizard) return done();
          if ($data.formRef) {
            const valid = await new Promise((resolve) => ($data.formRef as InstanceType<typeof ElForm>).validate(resolve));
            if (!valid) return;
          }
          instance.confirmButtonLoading = true;
          instance.confirmButtonText = "Loading...";
          try {
            const { success, message } = await callback();
            if (success) {
              $data.checkWizard = true;
              instance.showCancelButton = false;
              instance.confirmButtonLoading = false;
              instance.confirmButtonText = "完成";
              instance.type = "success";
            } else throw Object.assign(new Error(message), { success });
          } catch (error) {
            if (error instanceof Error) {
              instance.showConfirmButton = false;
              $data.message = error.message;
              instance.type = "error";
            }
          } finally {
            instance.confirmButtonLoading = false;
          }
        } else done();
      },
    });
  } catch (error) {
    /*  */
  }
}

async function inputCertificate(callback: (form: { certificate?: string; captcha?: string }) => Promise<{ success: boolean; message: string } & Record<string, unknown>>): Promise<void> {
  const $form = reactive({
    title: i18n.global.t("personalInformation.enabling20"),
    certificate: "",
    captcha: "",
  });
  const $input = ref<import("element-plus").InputInstance>();
  const $canvasRef = ref<HTMLCanvasElement>();
  const unWatch = watch($canvasRef, ($canvas) => {
    if ($canvas instanceof HTMLCanvasElement) {
      unWatch();
      $form.captcha = "";
      $form.certificate = String(`xxxxxxxx-xxxx-4xxx-yxxx-${Date.now().toString(16).padStart(12, "x")}`).replace(/[xy]/g, (c) => Number(c === "x" ? (Math.random() * 16) | 0 : (((Math.random() * 16) | 0) & 0x3) | 0x8).toString(16));
      updateCertificate($canvas, $form.certificate).then(() => nextTick(() => $input.value?.focus()));
    }
  });
  await bindFormBox(
    [
      h(ElAlert, { type: "info", title: i18n.global.t("personalInformation.Verification code"), description: i18n.global.t("personalInformation.enabling19"), showIcon: true, closable: false, style: { marginBottom: "22px" } }),
      h(ElFormItem, { rules: [{ required: true, message: i18n.global.t("personalInformation.Please enter the verification code"), trigger: "blur" }], prop: "captcha", label: "", size: "large" }, () => [
        h(ElInput, { "ref": (vm) => ($input.value = vm as import("element-plus").InputInstance), "prefixIcon": ChatDotSquare, "placeholder": i18n.global.t("personalInformation.Please enter the verification code"), "clearable": true, "modelValue": $form.captcha, "onUpdate:modelValue": (v) => ($form.captcha = v), "style": { verticalAlign: "top", width: "calc(100% - 118px)", marginRight: "12px" } }),
        h("canvas", {
          ref: (vm) => ($canvasRef.value = vm as HTMLCanvasElement),
          class: ["captcha-img"],
          height: "40",
          width: "100",
          title: "看不清，换一张",
          onClick({ target }: PointerEvent) {
            if (target instanceof HTMLCanvasElement) {
              $form.captcha = "";
              $form.certificate = String(`xxxxxxxx-xxxx-4xxx-yxxx-${Date.now().toString(16).padStart(12, "x")}`).replace(/[xy]/g, (c) => Number(c === "x" ? (Math.random() * 16) | 0 : (((Math.random() * 16) | 0) & 0x3) | 0x8).toString(16));
              updateCertificate(target, $form.certificate).then(() => nextTick(() => $input.value?.focus()));
            }
          },
        }),
      ]),
    ],
    $form,
    async () => {
      const { success, message, data } = await callback({ certificate: $form.certificate, captcha: $form.captcha });
      if (!success) throw Object.assign(new Error(message), { success, data });
      coolingCaptcha.value = 60;
      return { success, message };
    }
  );
}
async function updateCertificate(canvasRef: HTMLCanvasElement, certificate: string) {
  try {
    const { success, data, message } = await captchaForImage({ certificate });
    if (success) {
      await bindCanvasImage(canvasRef.getContext("2d") as CanvasRenderingContext2D, data);
    } else throw Object.assign(new Error(message), { success, data, message });
  } catch (error) {
    return;
  }
}
