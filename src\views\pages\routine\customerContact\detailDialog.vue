<template>
  <el-dialog v-model="dialogVisible" title="详情" width="500" :before-close="handleClose">
    <el-form :model="data" label-width="120">
      <el-form-item label="姓名">{{ data.name }}</el-form-item>
      <el-form-item label="手机号">{{ data.phone }}</el-form-item>
      <el-form-item label="邮箱">{{ data.email }}</el-form-item>
      <el-form-item label="公司名称">{{ data.company }}</el-form-item>
      <el-form-item label="所在行业">{{ data.industry ? Industry[data.industry as Industry] : "" }}</el-form-item>
      <el-form-item label="职位">{{ data.position ? Position[data.position as Position] : "" }}</el-form-item>
      <el-form-item label="咨询问题">{{ data.question }}</el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleClose">{{ t("glob.Cancel") }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { getCustomerContactItem, CustomerContactItem, Industry, Position } from "@/views/pages/apis/customerContact";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const dialogVisible = ref(false);

const data = ref<CustomerContactItem>({} as CustomerContactItem);

function handleClose(done: any) {
  if (done instanceof Function) done();
  else dialogVisible.value = false;
}

async function handleGetCustomerContactItem(id: string): Promise<CustomerContactItem> {
  try {
    const { data, message, success } = await getCustomerContactItem({ id });
    if (!success) throw new Error(message);
    return data;
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
    return {} as CustomerContactItem;
  }
}

defineExpose({
  open: async (id: string) => {
    data.value = await handleGetCustomerContactItem(id);

    dialogVisible.value = true;
  },
});
</script>
