<template>
  <el-card>
    <pageTemplate v-model:currentPage="state.page" v-model:pageSize="state.size" :total="state.total" :height="height - 40" :show-paging="true" @size-change="handleRefresh" @current-change="handleRefresh">
      <template #left>
        <el-form ref="searchFormRef" :model="state.search" :inline="true">
          <el-form-item>
            <el-select v-model="state.search.shortcuts" @change="handleSetOperationTime">
              <el-option v-for="item in shortcuts" :key="item.value" :label="item.text" :value="item.flag" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-date-picker
              :model-value="[state.search.operationTimeStart, state.search.operationTimeEnd]"
              @update:model-value="
                (v) => {
                  if (v.length) {
                    state.search.operationTimeStart = v[0];
                    state.search.operationTimeEnd = v[1];
                  }
                }
              "
              @change="handleRefresh"
              type="datetimerange"
              start-placeholder="Start date"
              value-format="x"
              format="YYYY-MM-DD HH:mm"
              placeholder="请选择开始时间"
              :clearable="false"
            />
          </el-form-item>
        </el-form>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table v-loading="state.loading" ref="tableRef" :data="state.list" :height="tableHeight" stripe>
          <TableColumn type="condition" :prop="`name`" :label="`姓名`" :showOverflowTooltip="true" />
          <TableColumn type="condition" :prop="`phone`" :label="`手机号`" :showOverflowTooltip="true" />
          <TableColumn type="condition" :prop="`email`" :label="`邮箱`" :showOverflowTooltip="true" />
          <TableColumn type="condition" :prop="`company`" :label="`公司`" :showOverflowTooltip="true" />
          <TableColumn type="condition" :prop="`industry`" :label="`所在行业`" :showOverflowTooltip="true" :formatter="(_row, _col, _v) => (_v ? Industry[_v as Industry] : '')" />
          <TableColumn type="condition" :prop="`position`" :label="`职位`" :showOverflowTooltip="true" :formatter="(_row, _col, _v) => (_v ? Position[_v as Industry] : '')" />
          <TableColumn type="condition" :prop="`question`" :label="`咨询问题`" :showOverflowTooltip="true" />
          <TableColumn type="date" :prop="`updatedTime`" :label="`更新时间`" :showOverflowTooltip="true" />
          <TableColumn type="default" :label="t('glob.operate')" :showOverflowTooltip="true">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleViewRow(row as DataItem)">{{ t("glob.view") }}</el-button>
            </template>
          </TableColumn>
        </el-table>
      </template>
    </pageTemplate>

    <detailDialog ref="detailDialogRef" />
  </el-card>
</template>

<script lang="ts" setup>
import { reactive, ref, inject, computed, onMounted, nextTick } from "vue";
import pageTemplate from "@/components/pageTemplate.vue";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { ElMessage } from "element-plus";
import { getCustomerContacts as getData, CustomerContactItem as DataItem, Industry, Position } from "@/views/pages/apis/customerContact";
import detailDialog from "./detailDialog.vue";

import { useI18n } from "vue-i18n";

const { t } = useI18n();

interface Props {
  width?: number;
  height?: number;
  title?: string;
}
const props = withDefaults(defineProps<Props>(), { title: "客户信息查看" });

const _width = inject("width", ref(0));
const _height = inject("height", ref(0));

const width = computed(() => props.width || _width.value);
const height = computed(() => props.height || _height.value);

const shortcuts = ref([
  { flag: "6hour", text: "最近6小时", value: () => [new Date().getTime() - 1000 * 60 * 60 * 6, new Date().getTime()] },
  { flag: "12hour", text: "最近12小时", value: () => [new Date().getTime() - 1000 * 60 * 60 * 12, new Date().getTime()] },
  { flag: "24hour", text: "最近24小时", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24, new Date().getTime()] },
  { flag: "2day", text: "最近2天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 2, new Date().getTime()] },
  { flag: "7day", text: "最近7天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 7, new Date().getTime()] },
  { flag: "30day", text: "最近30天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 30, new Date().getTime()] },
  { flag: "60day", text: "最近60天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 60, new Date().getTime()] },
  { flag: "90day", text: "最近90天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 90, new Date().getTime()] },
  { flag: "180day", text: "最近180天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 180, new Date().getTime()] },
  { flag: "365day", text: "最近365天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 365, new Date().getTime()] },
]);

interface State<T, P> {
  loading: boolean;
  search: P;
  sort?: { prop: string; order: "ascending" | "descending" };
  list: T[];
  page: number;
  size: number;
  total: number;
}

const state = reactive<State<DataItem, Record<string, any>>>({
  loading: false,
  search: {
    shortcuts: "30day",
    operationTimeStart: Date.parse(new Date((shortcuts.value.find((v) => v.flag === "30day") || ({} as unknown as never)).value()[0]) as unknown as never),
    operationTimeEnd: Date.parse(new Date((shortcuts.value.find((v) => v.flag === "30day") || ({} as unknown as never)).value()[1]) as unknown as never),
  },
  page: 1,
  size: 50,
  total: 0,
  list: [],
});

/**
 * @desc 时间选择器快捷方式
 */
function handleSetOperationTime(val) {
  const item = shortcuts.value.find((v) => v.flag === val);
  if (item) {
    const value = item.value();
    state.search.operationTimeStart = Math.floor(value[0]);
    state.search.operationTimeEnd = Math.floor(value[1]);

    nextTick(() => handleRefresh());
  }
}

const detailDialogRef = ref<InstanceType<typeof detailDialog>>();

async function handleViewRow(row: DataItem) {
  detailDialogRef.value && detailDialogRef.value.open(row.id);
}

async function handleRefresh() {
  try {
    state.loading = true;
    const params = {
      pageNumber: `${state.page}`,
      pageSize: `${state.size}`,
    };

    const { data, message, success, total } = await getData(params, {
      operationTimeStart: state.search.operationTimeStart,
      operationTimeEnd: state.search.operationTimeEnd,
    });
    if (!success) throw new Error(message);
    state.list = data;
    state.total = Number(total);
  } catch (error) {
    error instanceof Error && ElMessage.error(error.message);
  } finally {
    state.loading = false;
  }
}

onMounted(async () => {
  await handleRefresh();
});
</script>
