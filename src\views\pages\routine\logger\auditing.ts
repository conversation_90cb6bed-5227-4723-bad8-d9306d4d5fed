export const resourceType = {
  resource: [
    { label: "名称", key: "name", type: "text" },
    { label: "描述", key: "description", type: "text" },
    { label: "地区", key: "regionId", type: "text" },
    { label: "场所", key: "locationId", type: "text" },
    { label: "外部ID", key: "externalId", type: "text" },
    { label: "标签", key: "tag", type: "text" },
    { label: "IP地址", source: "config", key: "ipAddress", type: "text" },
    { label: "是否激活", key: "active", type: "text" },
    { label: "是否交付", key: "delivery", type: "text" },
    { label: "是否为动态IP", source: "config", key: "dynamicIp", type: "text" },
    { label: "告警分类", key: "alertClassificationIds", type: "text" },
    { label: "是否确认告警", source: "config", key: "ackRequired", type: "text" },
    { label: "是否自动事件", source: "config", key: "nmsTicketing", type: "text" },
    { label: "重要性", key: "importance", type: "text" },
    { label: "远程登录方式", source: "config", key: "connectAuthType", type: "text" },
    { label: "设备供应商", key: "vendorIds", type: "text" },
    { label: "设备型号", key: "modelNumbers", type: "text" },
    { label: "设备序列号", key: "serialNumbers", type: "text" },
    { label: "资产编号", source: "config", key: "assetNumbers", type: "text" },
    { label: "设备类型", key: "typeIdsp", type: "text" },
    { label: "设备分组", key: "grouIds", type: "text" },
  ],
};
