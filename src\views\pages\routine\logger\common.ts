export enum GroupType {
  /**@type {string} - 资产管理中心 */
  resource = "resource",
  /**@type {string} - 监控管理中心 */
  monitors = "monitors",
  /**@type {string} - 智能事件中心 */
  incident = "incident",
  /**@type {string} - 安全管理中心 */
  security = "security",
  /**@type {string} - 服务管理中心 */
  services = "services",
  /**@type {string} - 系统管理中心 */
  systemic = "systemic",
}

export const group = [
  /*  */
  { id: GroupType.resource, name: "资产管理中心" },
  { id: GroupType.monitors, name: "监控管理中心" },
  { id: GroupType.incident, name: "智能事件中心" },
  { id: GroupType.security, name: "安全管理中心" },
  { id: GroupType.services, name: "服务管理中心" },
  { id: GroupType.systemic, name: "系统管理中心" },
];

export enum ResourceType {
  /**@type {string} -  资产管理  */
  resource = "resource",
  /**@type {string} -  区    域  */
  /**@type {string} -  场    所  */
  /**@type {string} -  联 系 人  */
  /**@type {string} -  设备分组  */
  /**@type {string} -  设备类型  */
  /**@type {string} - 设备供应商 */
  /**@type {string} - 线路供应商 */
  /**@type {string} -  服务编号  */
  /**@type {string} -  绑定 SLA  */
  /**@type {string} -  设备日志  */
  /**@type {string} -  设备文件  */
  /**@type {string} -  行动策略  */
}
export enum MonitorsType {
  /**@type {string} -  告    警  */
  alarm = "alarm",
  /**@type {string} -  告 警 板  */
  alarm_board = "alarm_board",
}
export enum IncidentType {
  /**@type {string} -  事    件  */
  event = "event",
  /**@type {string} -  服务请求  */
  service_request = "service_request",
}
export enum SecurityType {
  /**@type {string} -  客    户  */
  tenant = "tenant",
  /**@type {string} -  用    户  */
  user = "user",
  /**@type {string} -  用 户 组  */
  user_group = "user_group",
  /**@type {string} -  权限管理  */
  permission = "permission",
}
export enum ServicesType {
  /**@type {string} -  告警映射  */
  alarm_mapping = "alarm_mapping",
  /**@type {string} -  关闭代码  */
  /**@type {string} - 优先级矩阵 */
  /**@type {string} -  SLA 配置  */
  /**@type {string} -  告警归并  */
  /**@type {string} - 优先级调整 */
}
export enum SystemicType {
  /**@type {string} -  个人中心  */
  personal = "personal",
  /**@type {string} -  邮件模板  */
  /**@type {string} -  短信模板  */
  /**@type {string} -邮件发送策略*/
  /**@type {string} -短信发送策略*/
  /**@type {string} -触发条件模板*/
}
export const audit = [
  /**
   * 资产管理中心
   */
  /**@type {string} -  资产管理  */
  { group: GroupType.resource, id: ResourceType, name: "  资产管理  " },
  /**@type {string} -  区    域  */
  { group: GroupType.resource, id: ResourceType, name: "  区    域  " },
  /**@type {string} -  场    所  */
  { group: GroupType.resource, id: ResourceType, name: "  场    所  " },
  /**@type {string} -  联 系 人  */
  { group: GroupType.resource, id: ResourceType, name: "  联 系 人  " },
  /**@type {string} -  设备分组  */
  { group: GroupType.resource, id: ResourceType, name: "  设备分组  " },
  /**@type {string} -  设备类型  */
  { group: GroupType.resource, id: ResourceType, name: "  设备类型  " },
  /**@type {string} - 设备供应商 */
  { group: GroupType.resource, id: ResourceType, name: " 设备供应商 " },
  /**@type {string} - 线路供应商 */
  { group: GroupType.resource, id: ResourceType, name: " 线路供应商 " },
  /**@type {string} -  服务编号  */
  { group: GroupType.resource, id: ResourceType, name: "  服务编号  " },
  /**@type {string} -  绑定 SLA  */
  { group: GroupType.resource, id: ResourceType, name: "  绑定 SLA  " },
  /**@type {string} -  设备日志  */
  { group: GroupType.resource, id: ResourceType, name: "  设备日志  " },
  /**@type {string} -  设备文件  */
  { group: GroupType.resource, id: ResourceType, name: "  设备文件  " },
  /**@type {string} -  行动策略  */
  { group: GroupType.resource, id: ResourceType, name: "  行动策略  " },
  /**
   * 监控管理中心
   */
  /**@type {string} -  告    警  */
  { group: GroupType.monitors, id: MonitorsType, name: "  告    警  " },
  /**@type {string} -  告 警 板  */
  { group: GroupType.monitors, id: MonitorsType, name: "  告 警 板  " },
  /**
   * 智能事件中心
   */
  /**@type {string} -  事    件  */
  { group: GroupType.incident, id: IncidentType, name: "  事    件  " },
  /**@type {string} -  服务请求  */
  { group: GroupType.incident, id: IncidentType, name: "  服务请求  " },
  /**
   * 安全管理中心
   */
  /**@type {string} -  客    户  */
  { group: GroupType.security, id: SecurityType, name: "  客    户  " },
  /**@type {string} -  用    户  */
  { group: GroupType.security, id: SecurityType, name: "  用    户  " },
  /**@type {string} -  用 户 组  */
  { group: GroupType.security, id: SecurityType, name: "  用 户 组  " },
  /**@type {string} -  权限管理  */
  { group: GroupType.security, id: SecurityType, name: "  权限管理  " },
  /**
   * 服务管理中心
   */
  /**@type {string} -  告警映射  */
  { group: GroupType.services, id: ServicesType, name: "  告警映射  " },
  /**@type {string} -  关闭代码  */
  { group: GroupType.services, id: ServicesType, name: "  关闭代码  " },
  /**@type {string} - 优先级矩阵 */
  { group: GroupType.services, id: ServicesType, name: " 优先级矩阵 " },
  /**@type {string} -  SLA 配置  */
  { group: GroupType.services, id: ServicesType, name: "  SLA 配置  " },
  /**@type {string} -  告警归并  */
  { group: GroupType.services, id: ServicesType, name: "  告警归并  " },
  /**@type {string} - 优先级调整 */
  { group: GroupType.services, id: ServicesType, name: " 优先级调整 " },
  /**
   * 系统管理中心
   */
  /**@type {string} -  个人中心  */
  { group: GroupType.systemic, id: SystemicType, name: "  个人中心  " },
  /**@type {string} -  邮件模板  */
  { group: GroupType.systemic, id: SystemicType, name: "  邮件模板  " },
  /**@type {string} -  短信模板  */
  { group: GroupType.systemic, id: SystemicType, name: "  短信模板  " },
  /**@type {string} -邮件发送策略*/
  { group: GroupType.systemic, id: SystemicType, name: "邮件发送策略" },
  /**@type {string} -短信发送策略*/
  { group: GroupType.systemic, id: SystemicType, name: "短信发送策略" },
  /**@type {string} -触发条件模板*/
  { group: GroupType.systemic, id: SystemicType, name: "触发条件模板" },
];
export const preview = [
  /**
   * 资产管理中心
   */
  /**@type {string} -  资产管理  */
  { group: GroupType.resource, audit: ResourceType, id: "", name: "  资产管理  ", render: () => {} },
  /**@type {string} -  区    域  */
  { group: GroupType.resource, audit: ResourceType, id: "", name: "  区    域  ", render: () => {} },
  /**@type {string} -  场    所  */
  { group: GroupType.resource, audit: ResourceType, id: "", name: "  场    所  ", render: () => {} },
  /**@type {string} -  联 系 人  */
  { group: GroupType.resource, audit: ResourceType, id: "", name: "  联 系 人  ", render: () => {} },
  /**@type {string} -  设备分组  */
  { group: GroupType.resource, audit: ResourceType, id: "", name: "  设备分组  ", render: () => {} },
  /**@type {string} -  设备类型  */
  { group: GroupType.resource, audit: ResourceType, id: "", name: "  设备类型  ", render: () => {} },
  /**@type {string} - 设备供应商 */
  { group: GroupType.resource, audit: ResourceType, id: "", name: " 设备供应商 ", render: () => {} },
  /**@type {string} - 线路供应商 */
  { group: GroupType.resource, audit: ResourceType, id: "", name: " 线路供应商 ", render: () => {} },
  /**@type {string} -  服务编号  */
  { group: GroupType.resource, audit: ResourceType, id: "", name: "  服务编号  ", render: () => {} },
  /**@type {string} -  绑定 SLA  */
  { group: GroupType.resource, audit: ResourceType, id: "", name: "  绑定 SLA  ", render: () => {} },
  /**@type {string} -  设备日志  */
  { group: GroupType.resource, audit: ResourceType, id: "", name: "  设备日志  ", render: () => {} },
  /**@type {string} -  设备文件  */
  { group: GroupType.resource, audit: ResourceType, id: "", name: "  设备文件  ", render: () => {} },
  /**@type {string} -  行动策略  */
  { group: GroupType.resource, audit: ResourceType, id: "", name: "  行动策略  ", render: () => {} },
  /**
   * 监控管理中心
   */
  /**@type {string} -  告    警  */
  { group: GroupType.monitors, audit: MonitorsType, id: "", name: "  告    警  ", render: () => {} },
  /**@type {string} -  告 警 板  */
  { group: GroupType.monitors, audit: MonitorsType, id: "", name: "  告 警 板  ", render: () => {} },
  /**
   * 智能事件中心
   */
  /**@type {string} -  事    件  */
  { group: GroupType.incident, audit: IncidentType, id: "", name: "  事    件  ", render: () => {} },
  /**@type {string} -  服务请求  */
  { group: GroupType.incident, audit: IncidentType, id: "", name: "  服务请求  ", render: () => {} },
  /**
   * 安全管理中心
   */
  /**@type {string} -  客    户  */
  { group: GroupType.security, audit: SecurityType, id: "", name: "  客    户  ", render: () => {} },
  /**@type {string} -  用    户  */
  { group: GroupType.security, audit: SecurityType, id: "", name: "  用    户  ", render: () => {} },
  /**@type {string} -  用 户 组  */
  { group: GroupType.security, audit: SecurityType, id: "", name: "  用 户 组  ", render: () => {} },
  /**@type {string} -  权限管理  */
  { group: GroupType.security, audit: SecurityType, id: "", name: "  权限管理  ", render: () => {} },
  /**
   * 服务管理中心
   */
  /**@type {string} -  告警映射  */
  { group: GroupType.services, audit: ServicesType, id: "", name: "  告警映射  ", render: () => {} },
  /**@type {string} -  关闭代码  */
  { group: GroupType.services, audit: ServicesType, id: "", name: "  关闭代码  ", render: () => {} },
  /**@type {string} - 优先级矩阵 */
  { group: GroupType.services, audit: ServicesType, id: "", name: " 优先级矩阵 ", render: () => {} },
  /**@type {string} -  SLA 配置  */
  { group: GroupType.services, audit: ServicesType, id: "", name: "  SLA 配置  ", render: () => {} },
  /**@type {string} -  告警归并  */
  { group: GroupType.services, audit: ServicesType, id: "", name: "  告警归并  ", render: () => {} },
  /**@type {string} - 优先级调整 */
  { group: GroupType.services, audit: ServicesType, id: "", name: " 优先级调整 ", render: () => {} },
  /**
   * 系统管理中心
   */
  /**@type {string} -  个人中心  */
  { group: GroupType.systemic, audit: SystemicType, id: "", name: "  个人中心  ", render: () => {} },
  /**@type {string} -  邮件模板  */
  { group: GroupType.systemic, audit: SystemicType, id: "", name: "  邮件模板  ", render: () => {} },
  /**@type {string} -  短信模板  */
  { group: GroupType.systemic, audit: SystemicType, id: "", name: "  短信模板  ", render: () => {} },
  /**@type {string} -邮件发送策略*/
  { group: GroupType.systemic, audit: SystemicType, id: "", name: "邮件发送策略", render: () => {} },
  /**@type {string} -短信发送策略*/
  { group: GroupType.systemic, audit: SystemicType, id: "", name: "短信发送策略", render: () => {} },
  /**@type {string} -触发条件模板*/
  { group: GroupType.systemic, audit: SystemicType, id: "", name: "触发条件模板", render: () => {} },
];
