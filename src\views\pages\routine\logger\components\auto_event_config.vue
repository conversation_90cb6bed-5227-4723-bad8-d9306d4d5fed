<template>
  <el-form :model="{}" label-position="left">
    <div style="font-weight: 600; color: #000">{{ operationType }}</div>
    <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <template v-if="item.type === 'text'">
        <div>
          <div class="changedValue" v-if="changedValue[item.key]">"{{ changedValue[item.key] }}"</div>
          <div class="originalValue" v-if="operationType != '新增'">"{{ originalValue[item.key] }}"</div>
        </div>
      </template>
      <template v-else-if="item.type === 'tag'">
        <div v-if="['alertCollect', 'autoEvent', 'defaultRule', 'mergeEvent'].includes(item.key)" class="tags">
          <el-tag :type="'success'" v-if="operationType != '删除' && changedValue[item.key]">{{ booleans[changedValue[item.key] + ""] }}</el-tag>
          <el-tag :type="'danger'" v-if="operationType != '新增' && originalValue[item.key]">{{ booleans[originalValue[item.key] + ""] }}</el-tag>
        </div>
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";
import { operationLogger, contactsType } from "@/api/loggerType";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };

const formOption: CurrentLogFormItems[] = [
  { label: "是否自动事件", key: "autoEvent", type: "tag" },
  { label: "是否采集告警", key: "alertCollect", type: "tag" },
  { label: "是否默认规则", key: "defaultRule", type: "tag" },
  { label: "事件锁定时间", key: "sustainTime", type: "text" },
  { label: "告警归并到", key: "alertMerge", type: "text" },
  { label: "告警是否生成", key: "mergeEvent", type: "tag" },
];
// "{"autoEvent\":true,\"alertCollect\":false,\"defaultRule\":true,\"sustainTime\":15,\"alertMerge\":\"ANY_ALERT\",\"mergeEvent\":true}"

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<any>({});

const changedValue = ref<any>({});

const operationType = ref<string>("");
function handleLoggerInfo() {
  operationLogger.forEach((v, i) => {
    if (v.auditCode == props.data.auditCode) {
      operationType.value = v.type;
    }
  });

  try {
    originalValue.value = new Function("return" + props.data.originalValue)() || {};
  } catch (error) {
    originalValue.value = {};
  }

  try {
    changedValue.value = new Function("return" + props.data.changedValue)() || {};
  } catch (error) {
    changedValue.value = {};
  }

  currentLogFormItems.value = formOption.filter((v) => {
    if (originalValue.value[v.key] == changedValue.value[v.key]) return false;
    else return true;
  });
}

onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
.tags {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  justify-content: space-between;
  height: 55px;
}
</style>
