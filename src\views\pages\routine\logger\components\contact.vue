<template>
  <el-form :model="{}" label-position="left">
    <div style="font-weight: 600; color: #000">{{ operationType }}</div>
    <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <template v-if="item.type === 'text'">
        <div>
          <div class="changedValue" v-if="changedValue[item.key]">"{{ changedValue[item.key] }}"</div>
          <div class="originalValue" v-if="!['新增', '查询'].includes(operationType)">"{{ originalValue[item.key] }}"</div>
        </div>
      </template>
      <template v-else-if="item.type === 'tag'">
        <div v-if="['vip', 'smsEnabled', 'active'].includes(item.key)" class="tags">
          <el-tag :type="'success'" v-if="operationType != '删除'">{{ booleans[changedValue[item.key] + ""] }}</el-tag>
          <el-tag :type="'danger'" v-if="!['新增', '查询'].includes(operationType)">{{ booleans[originalValue[item.key] + ""] }}</el-tag>
        </div>
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });
import { operationLogger, contactsType } from "@/api/loggerType";

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };

const formOption: CurrentLogFormItems[] = [
  { label: "姓名", key: "name", type: "text" },
  { label: "邮箱", key: "email", type: "text" },
  { label: "固定电话", key: "landlinePhone", type: "text" },
  { label: "移动电话", key: "mobilePhone", type: "text" },
  { label: "短信号码", key: "smsPhone", type: "text" },
  { label: "时区", key: "zoneId", type: "text" },
  { label: "语言", key: "language", type: "text" },
  { label: "VIP", key: "vip", type: "tag" },
  { label: "是否启用短信", key: "smsEnabled", type: "tag" },
  { label: "描述", key: "note", type: "text" },
  { label: "外部ID", key: "externalId", type: "text" },
  { label: "是否激活", key: "active", type: "tag" },
  { label: "职位", key: "position", type: "text" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<any>({});

const changedValue = ref<any>({});
const operationType = ref<string>("");
function handleLoggerInfo() {
  operationLogger.forEach((v, i) => {
    if (v.auditCode == props.data.auditCode) {
      operationType.value = v.type;
    }
  });
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  originalValue.value.zoneId = originalValue.value.zoneId ? Zone[originalValue.value.zoneId] : "";
  originalValue.value.language = originalValue.value.language ? Language[originalValue.value.language] : "";

  changedValue.value = new Function("return" + props.data.changedValue)() || {};
  changedValue.value.zoneId = changedValue.value.zoneId ? Zone[changedValue.value.zoneId] : "";
  changedValue.value.language = changedValue.value.language ? Language[changedValue.value.language] : "";

  if (operationType.value == "新增") {
    changedValue.value.email = changedValue.value.email || null;
    changedValue.value.landlinePhone = changedValue.value.landlinePhone || null;
    changedValue.value.mobilePhone = changedValue.value.mobilePhone || null;
    changedValue.value.smsPhone = changedValue.value.smsPhone || null;
  }
  if (operationType.value == "删除") {
    originalValue.value.email = originalValue.value.email || null;
    originalValue.value.landlinePhone = originalValue.value.landlinePhone || null;
    originalValue.value.mobilePhone = originalValue.value.mobilePhone || null;
    originalValue.value.smsPhone = originalValue.value.smsPhone || null;
  }

  currentLogFormItems.value = formOption.filter((v) => {
    if (originalValue.value[v.key] == changedValue.value[v.key]) {
      return false;
    } else {
      return true;
    }
  });
}

onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
.tags {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  justify-content: space-between;
  height: 55px;
}
</style>
