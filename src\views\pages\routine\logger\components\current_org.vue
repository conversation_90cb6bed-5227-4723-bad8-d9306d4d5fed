<template>
  <el-form :model="{}" label-position="left">
    <div style="font-weight: 600; color: #000">更新</div>
    <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <template v-if="item.type === 'text'">
        <div v-if="item.source">
          <template v-if="Object.keys(booleans).includes(changedValue[item.source][item.key])">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ booleans[changedValue[item.source][item.key]] }}"</div>
            <div class="originalValue">"{{ booleans[originalValue[item.source][item.key]] }}"</div>
          </template>
          <template v-else-if="[/*'vendorIds', 'modelNumbers', 'serialNumbers', */ 'assetNumbers' /* 'typeIdsp', 'grouIds' */].includes(item.key)">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ JSON.parse(changedValue[item.source][item.key]).join() }}"</div>
            <div class="originalValue">"{{ JSON.parse(changedValue[item.source][item.key]).join() }}"</div>
          </template>
          <template v-else>
            <div class="changedValue">"{{ changedValue[item.source][item.key] }}"</div>
            <div class="originalValue">"{{ originalValue[item.source][item.key] }}"</div>
          </template>
        </div>
        <div v-else>
          <template v-if="['report'].includes(item.key)">
            <!-- 处理 true | false -->
            <div :style="{ marginLeft: ['report'].includes(item.key) ? '50px' : '0' }">
              <div class="changedValue">"{{ booleans[changedValue[item.key] + ""] }}"</div>
              <div class="originalValue">"{{ booleans[originalValue[item.key] + ""] }}"</div>
            </div>
          </template>
          <template v-else-if="[/*'vendorIds', 'modelNumbers', 'serialNumbers', */ 'assetNumbers' /* 'typeIdsp', 'grouIds' */].includes(item.key)">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ JSON.parse(changedValue[item.key]).join() }}"</div>
            <div class="originalValue">"{{ JSON.parse(originalValue[item.key]).join() }}"</div>
          </template>
          <template v-else>
            <div class="changedValue">"{{ changedValue[item.key] }}"</div>
            <div class="originalValue">"{{ originalValue[item.key] }}"</div>
          </template>
        </div>
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };
// 告警分类字段待增加
const formOption: CurrentLogFormItems[] = [{ label: "名称", key: "name", type: "text" }];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<any>({});

const changedValue = ref<any>({});

function handleLoggerInfo() {
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  changedValue.value = new Function("return" + props.data.changedValue)() || {};
  currentLogFormItems.value = formOption.filter((v) => changedValue.value[v.key]);
}

onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
</style>
