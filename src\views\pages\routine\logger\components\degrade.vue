<template>
  <div v-if="isShow">
    <div style="font-weight: 600; color: #000">{{ operationType }}</div>

    <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <template v-if="item.type === 'text'">
        <div>
          <div class="changedValue" v-if="operationType != '删除' && operationType != '移除'">"{{ changedValue[item.key] }} "</div>
          <div class="originalValue" v-if="operationType != '新增'">"{{ originalValue[item.key] }}"</div>
        </div>
      </template>
      <template v-if="item.type === 'contacText'">
        <div>
          <div class="changedValue" v-if="operationType != '删除' && operationType != '移除'">{{ changedValue[item.key] }} ({{ contactsType[contacType] }})</div>
          <div class="originalValue" v-if="operationType != '新增'">{{ originalValue[item.key] }}({{ contactsType[contacType] }})</div>
        </div>
      </template>
      <template v-if="item.type === 'relation'">
        <div>
          <div v-if="operationType == '分配'">
            <div class="changedValue" v-for="v in changedValue[item.key]" :key="v">
              {{ v }}
            </div>
          </div>
          <div v-if="operationType == '移除'">
            <div class="originalValue" v-for="v in changedValue[item.key]" :key="v">
              {{ v }}
            </div>
          </div>
          <div>{{ props.data.tenant.name }}[{{ props.data.tenant.abbreviation }}]</div>
        </div>
      </template>
    </el-form-item>
    <el-form :model="{}" label-width="160px" label-position="left" v-for="item in weekList" :key="item.title">
      <div v-if="item.cwnDegradeInWorkTime != item.cwnForbidInWorkTime && item.cwnDegradeOutWorkTime != item.cwnForbidOutWorkTime">
        <div style="font-weight: 600; color: #000; margin: 5px 0">{{ item.title }}</div>
        <div style="display: flex">
          <div style="background: rgb(234, 245, 209); margin-right: 55px; padding: 10px 20px; min-width: 240px">
            <div>工作时间内</div>
            <el-form-item label="选择优先级降级数" v-if="item.cwnDegradeInWorkTime !== item.owyDegradeInWorkTime">
              <div>
                <div class="changedValue">"{{ item.cwnDegradeInWorkTime }}"</div>
                <div class="originalValue">"{{ item.owyDegradeInWorkTime }}"</div>
              </div>
            </el-form-item>
            <el-form-item label="禁止优先级低于" v-if="item.cwnForbidInWorkTime !== item.owyForbidInWorkTime">
              <div>
                <div class="changedValue">"{{ item.cwnForbidInWorkTime ? "P" + item.cwnForbidInWorkTime : "" }}"</div>
                <div class="originalValue">"{{ item.owyForbidInWorkTime ? "P" + item.owyForbidInWorkTime : "" }}"</div>
              </div>
            </el-form-item>
          </div>
          <div style="background: rgb(249, 247, 235); padding: 10px 20px; min-width: 240px">
            <div>非工作时间内</div>
            <el-form-item label="选择优先级降级数" v-if="item.cwnDegradeInWorkTime !== item.ownDegradeOutWorkTime">
              <div>
                <div class="changedValue">"{{ item.cwnDegradeOutWorkTime }}"</div>
                <div class="originalValue">"{{ item.ownDegradeOutWorkTime }}"</div>
              </div>
            </el-form-item>
            <el-form-item label="禁止优先级低于" v-if="item.cwnForbidOutWorkTime !== item.ownForbidOutWorkTime">
              <div>
                <div class="changedValue">"{{ item.cwnForbidOutWorkTime ? "P" + item.cwnForbidOutWorkTime : "" }}"</div>
                <div class="originalValue">"{{ item.ownForbidOutWorkTime ? "P" + item.ownForbidOutWorkTime : "" }}"</div>
              </div>
            </el-form-item>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});
import { operationLogger, contactsType } from "@/api/loggerType";

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };
// 告警分类字段待增加
const formOption: CurrentLogFormItems[] = [
  { label: "告警降级名称", key: "degradeName", type: "text" },
  { label: "描述", key: "degradeDesc", type: "text" },
  { label: "时区", key: "timeZone", type: "text" },

  // { label: "手机号", key: "phone", type: "text" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<any>({});

const changedValue = ref<any>({});
const WORKDAY = ref<any>({});
const SATURDAY = ref<any>({});
const SUNDAY = ref<any>({});
const weekList = ref<any>([]);

let isShow = ref<any>((new Function("return" + props.data.changedValue)() || {})?.effectTimeCfg ? true : false);
const operationLoggerItem = ref<any>({});
const operationType = ref<string>("");
function handleLoggerInfo() {
  operationLogger.forEach((v, i) => {
    if (v.auditCode == props.data.auditCode) {
      operationType.value = v.type;
      operationLoggerItem.value = v;
    }
  });
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  changedValue.value = new Function("return" + props.data.changedValue)() || {};

  formOption.push({ label: operationLoggerItem.value.name.split("分配")[1], key: "names", type: "relation" });

  originalValue.value.timeZone = originalValue.value.effectTimeCfg?.timeZone ? Zone[originalValue.value.effectTimeCfg.timeZone] : originalValue.value?.effectTimeCfg?.timeZone;

  changedValue.value.timeZone = changedValue.value.effectTimeCfg?.timeZone ? Zone[changedValue.value.effectTimeCfg.timeZone] : changedValue.value?.effectTimeCfg?.timeZone;
  currentLogFormItems.value = formOption.filter((v) => {
    // console.log(v.key);
    // console.log(changedValue.value[v.key]);
    // console.log(originalValue.value[v.key]);
    // if (originalValue.value[v.key].length > 0 || changedValue.value[v.key] > 0) {
    //   return true;
    // }

    if (!originalValue.value[v.key] && !changedValue.value[v.key]) return false;

    if (originalValue.value[v.key] == changedValue.value[v.key]) return false;
    else return true;
  });
  if (!isShow) {
    return;
  }
  let originalWORKDAY = originalValue.value.effectTimeCfg?.degradeConfigs.find((v) => v.weekType === "WORKDAY");
  let originalSATURDAY = originalValue.value.effectTimeCfg?.degradeConfigs.find((v) => v.weekType === "SATURDAY");
  let originalSUNDAY = originalValue.value.effectTimeCfg?.degradeConfigs.find((v) => v.weekType === "SUNDAY");
  let changedWORKDAY = changedValue.value.effectTimeCfg?.degradeConfigs.find((v) => v.weekType === "WORKDAY");
  let changedSATURDAY = changedValue.value.effectTimeCfg?.degradeConfigs.find((v) => v.weekType === "SATURDAY");
  let changedSUNDAY = changedValue.value.effectTimeCfg?.degradeConfigs.find((v) => v.weekType === "SUNDAY");
  WORKDAY.value = {
    title: "工作日降级策略配置",
    // 变更前工作时间选择优先级降级数
    owyDegradeInWorkTime: originalWORKDAY?.degradeInWorkTime,
    // 变更前工作时间选择禁止优先级低于
    owyForbidInWorkTime: originalWORKDAY?.forbidInWorkTime,
    // 变更前非工作时间时间选择优先级降级数
    ownDegradeOutWorkTime: originalWORKDAY?.degradeOutWorkTime,
    // 变更前非工作时间选择禁止优先级低于
    ownForbidOutWorkTime: originalWORKDAY?.forbidOutWorkTime,
    // 变更后工作时间选择优先级降级数
    cwnDegradeInWorkTime: changedWORKDAY?.degradeInWorkTime,
    // 变更后工作时间选择禁止优先级低于
    cwnForbidInWorkTime: changedWORKDAY?.forbidInWorkTime,
    // 变更后非工作时间时间选择优先级降级数
    cwnDegradeOutWorkTime: changedWORKDAY?.degradeOutWorkTime,
    // 变更后非工作时间选择禁止优先级低于
    cwnForbidOutWorkTime: changedWORKDAY?.forbidOutWorkTime,
  };
  SATURDAY.value = {
    title: "周六降级配置",
    // 变更前工作时间选择优先级降级数
    owyDegradeInWorkTime: originalSATURDAY?.degradeInWorkTime,
    // 变更前工作时间选择禁止优先级低于
    owyForbidInWorkTime: originalSATURDAY?.forbidInWorkTime,
    // 变更前非工作时间时间选择优先级降级数
    ownDegradeOutWorkTime: originalSATURDAY?.degradeOutWorkTime,
    // 变更前非工作时间选择禁止优先级低于
    ownForbidOutWorkTime: originalSATURDAY?.forbidOutWorkTime,
    // 变更后工作时间选择优先级降级数
    cwnDegradeInWorkTime: changedSATURDAY?.degradeInWorkTime,
    // 变更后工作时间选择禁止优先级低于
    cwnForbidInWorkTime: changedSATURDAY?.forbidInWorkTime,
    // 变更后非工作时间时间选择优先级降级数
    cwnDegradeOutWorkTime: changedSATURDAY?.degradeOutWorkTime,
    // 变更后非工作时间选择禁止优先级低于
    cwnForbidOutWorkTime: changedSATURDAY?.forbidOutWorkTime,
  };
  SUNDAY.value = {
    title: "周日降级配置",
    // 变更前工作时间选择优先级降级数
    owyDegradeInWorkTime: originalSUNDAY?.degradeInWorkTime,
    // 变更前工作时间选择禁止优先级低于
    owyForbidInWorkTime: originalSUNDAY?.forbidInWorkTime,
    // 变更前非工作时间时间选择优先级降级数
    ownDegradeOutWorkTime: originalSUNDAY?.degradeOutWorkTime,
    // 变更前非工作时间选择禁止优先级低于
    ownForbidOutWorkTime: originalSUNDAY?.forbidOutWorkTime,
    // 变更后工作时间选择优先级降级数
    cwnDegradeInWorkTime: changedSUNDAY?.degradeInWorkTime,
    // 变更后工作时间选择禁止优先级低于
    cwnForbidInWorkTime: changedSUNDAY?.forbidInWorkTime,
    // 变更后非工作时间时间选择优先级降级数
    cwnDegradeOutWorkTime: changedSUNDAY?.degradeOutWorkTime,
    // 变更后非工作时间选择禁止优先级低于
    cwnForbidOutWorkTime: changedSUNDAY?.forbidOutWorkTime,
  };
  weekList.value = [WORKDAY.value, SATURDAY.value, SUNDAY.value];
  // console.log(weekList.value);
}
// setup(() => {
//   isShow = ;
// });
onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
:deep.el-form-item {
  margin: 0;
}
</style>
