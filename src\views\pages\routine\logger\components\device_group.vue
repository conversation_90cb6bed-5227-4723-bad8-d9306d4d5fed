<template>
  <el-form :model="{}" label-position="left">
    <div style="font-weight: 600; color: #000">{{ operationType }}</div>
    <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <template v-if="item.type === 'text'">
        <div>
          <template v-if="['report'].includes(item.key)">
            <!-- 处理 true | false -->
            <div :style="{ marginLeft: ['report'].includes(item.key) ? '50px' : '0' }">
              <div class="changedValue">"{{ booleans[changedValue[item.key] + ""] }}"</div>
              <div class="originalValue">"{{ booleans[originalValue[item.key] + ""] }}"</div>
            </div>
          </template>

          <template v-else>
            <div class="changedValue" v-if="operationType != '删除' && operationType != '移除'">"{{ changedValue[item.key] }}"</div>
            <div class="originalValue" v-if="operationType != '新增' && originalValue[item.key]">"{{ originalValue[item.key] }}"</div>
          </template>
        </div>
      </template>
      <template v-if="item.type === 'relation'">
        <div>
          <div v-if="operationType == '分配'">
            <div class="changedValue" v-for="v in changedValue[item.key]" :key="v">
              {{ v }}
            </div>
          </div>
          <div v-if="operationType == '移除'">
            <div class="originalValue" v-for="v in changedValue[item.key]" :key="v">
              {{ v }}
            </div>
          </div>
          <div>{{ props.data.resourceTenantName }}</div>
        </div>
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});
import { operationLogger, contactsType } from "@/api/loggerType";

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };
// 告警分类字段待增加
const formOption: CurrentLogFormItems[] = [
  { label: "设备分组", key: "name", type: "text" },
  { label: "描述", key: "description", type: "text" },
  { label: "告警分类", key: "alertType", type: "text" },
  { label: "是否为一个报告组", key: "report", type: "text" },
  { label: "设备", key: "names", type: "relation" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<any>({});

const changedValue = ref<any>({});
const operationType = ref("");
function handleLoggerInfo() {
  operationLogger.forEach((v, i) => {
    if (v.auditCode == props.data.auditCode) {
      operationType.value = v.type;
    }
  });
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  originalValue.value.alertType = (originalValue.value.alertClassificationNames || []).join(",");
  changedValue.value = new Function("return" + props.data.changedValue)() || {};
  changedValue.value.alertType = (changedValue.value.alertClassificationNames || []).join(",");
  currentLogFormItems.value = formOption.filter((v) => {
    if (!originalValue.value[v.key] && !changedValue.value[v.key]) return false;
    if (originalValue.value[v.key] == changedValue.value[v.key]) return false;
    else return true;
  });
}

onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
</style>
