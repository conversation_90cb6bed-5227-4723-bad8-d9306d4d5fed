<template>
  <el-form :model="{}" label-position="left" v-if="props.data.success">
    <div style="font-weight: 600; color: #000">{{ operationType }}</div>
    <template v-if="props.data.name?.includes('启用') || props.data.name?.includes('禁用')">
      <el-form-item :label="changedValue.status ? '启用' : '禁用'">
        <div>
          <p class="tw-font-semibold" :class="!changedValue.status ? 'tw-text-slate-500' : 'changedValue'">{{ changedValue.codeName }} <FontAwesomeIcon v-if="!changedValue.status" class="tw-mx-[5px]" :icon="faBan"></FontAwesomeIcon></p>
          <p>{{ props.data.resourceTenantName }}</p>
        </div>
      </el-form-item>
    </template>
    <template v-else>
      <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
        <template v-if="item.type === 'text'">
          <div>
            <div class="changedValue" v-if="changedValue[item.key]">"{{ changedValue[item.key] }}"</div>
            <div class="originalValue" v-if="originalValue[item.key]">"{{ originalValue[item.key] }}"</div>
          </div>
        </template>
      </el-form-item>
    </template>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { faBan } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import { genderOption } from "@/api/personnel";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });
import { operationLogger, contactsType } from "@/api/loggerType";

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string; isBoolean?: boolean };

const formOption: CurrentLogFormItems[] = [
  { label: "完结代码", key: "codeName", type: "text" },
  { label: "描述", key: "codeDesc", type: "text" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<Record<string, any>>({});

const changedValue = ref<Record<string, any>>({});

const operationType = ref<string>("");
function handleLoggerInfo() {
  operationLogger.forEach((v, i) => {
    if (v.auditCode == props.data.auditCode) {
      operationType.value = v.type;
    }
  });
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  changedValue.value = new Function("return" + props.data.changedValue)() || {};

  // originalValue.value.genderLabel = genderOption.find((v) => v.value === originalValue.value.gender)?.label || "";
  // changedValue.value.genderLabel = genderOption.find((v) => v.value === changedValue.value.gender)?.label || "";

  // currentLogFormItems.value = formOption; //.filter((v) => originalValue.value[v.key] !== changedValue.value[v.key]);
  currentLogFormItems.value = formOption.filter((v) => {
    if (!originalValue.value[v.key] && !changedValue.value[v.key]) return false;

    if (originalValue.value[v.key] == changedValue.value[v.key]) return false;
    else return true;
  });
}
onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
</style>
