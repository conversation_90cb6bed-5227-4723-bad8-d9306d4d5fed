<template>
  <el-form :model="{}" label-position="left">
    <div style="font-weight: 600; color: #000">{{ operationType }}</div>
    <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <template v-if="item.type === 'text'">
        <div v-if="item.source">
          <template v-if="Object.keys(booleans).includes(changedValue[item.source][item.key])">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ booleans[changedValue[item.source][item.key]] }}"</div>
            <div class="originalValue">"{{ booleans[originalValue[item.source][item.key]] }}"</div>
          </template>
          <template v-else-if="[].includes(item.key)">
            <!-- 处理 true | false -->
            <div style="margin-left: 60px">
              <div class="changedValue">"{{ changedValue[item.source][item.key] }}"</div>
              <div class="originalValue">"{{ changedValue[item.source][item.key] }}"</div>
            </div>
          </template>
          <template v-else>
            <div class="changedValue">"{{ changedValue[item.source][item.key] }}"</div>
            <div class="originalValue">"{{ originalValue[item.source][item.key] }}"</div>
          </template>
        </div>
        <div v-else>
          <template v-if="['vip', 'smsEnabled'].includes(item.key)">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ booleans[changedValue[item.key] + ""] }}"</div>
            <div class="originalValue">"{{ booleans[originalValue[item.key] + ""] }}"</div>
          </template>
          <template v-else-if="['activeNote', 'inactiveNote'].includes(item.key)">
            <!-- 处理 true | false -->
            <div style="margin-left: 60px">
              <div class="changedValue">"{{ changedValue[item.key] }}"</div>
              <div class="originalValue">"{{ originalValue[item.key] }}"</div>
            </div>
          </template>
          <template v-else>
            <div class="changedValue">"{{ changedValue[item.key] }}"</div>
            <div class="originalValue">"{{ originalValue[item.key] }}"</div>
          </template>
        </div>
      </template>
      <template v-if="item.type === 'tag'">
        <div v-if="['active'].includes(item.key)" class="tags">
          <el-tag :type="'success'" v-if="operationType != '删除'">{{ booleans[changedValue[item.key] + ""] }}</el-tag>
          <el-tag :type="'danger'" v-if="operationType != '新增'">{{ booleans[originalValue[item.key] + ""] }}</el-tag>
        </div>
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";
import { operationLogger, contactsType } from "@/api/loggerType";
interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };
// 工作时间监控策略/非工作时间监控策略字段待增加
const formOption: CurrentLogFormItems[] = [
  { label: "行动策略名称", key: "name", type: "text" },
  { label: "描述", key: "description", type: "text" },
  { label: "时区", key: "timeZone", source: "activeConfig", type: "text" },
  { label: "工作时间监控策略", key: "activeNote", type: "text" },
  { label: "非工作时间监控策略", key: "inactiveNote", type: "text" },
  { label: "是否激活", key: "active", type: "tag" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<any>({});

const changedValue = ref<any>({});
const operationType = ref<string>("");
function handleLoggerInfo() {
  operationLogger.forEach((v, i) => {
    if (v.auditCode == props.data.auditCode) {
      operationType.value = v.type;
    }
  });
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  console.log(originalValue.value, changedValue.value.activeConfig);

  originalValue.value.activeConfig.timeZone = originalValue.value && originalValue.value.activeConfig && originalValue.value.activeConfig.timeZone ? Zone[originalValue.value.activeConfig.timeZone] : "";
  changedValue.value = new Function("return" + props.data.changedValue)() || {};
  changedValue.value.activeConfig.timeZone = changedValue.value && changedValue.value.activeConfig && changedValue.value.activeConfig.timeZone ? Zone[changedValue.value.activeConfig.timeZone] : "";
  currentLogFormItems.value = [...formOption.filter((v) => changedValue.value[v.key]), ...formOption.filter((v) => changedValue.value.activeConfig[v.key])];
}

onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
.tags {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  justify-content: space-between;
  height: 55px;
}
</style>
