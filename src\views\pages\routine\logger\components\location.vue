<template>
  <el-form :model="{}" label-position="left">
    <div style="font-weight: 600; color: #000">{{ operationType }}</div>
    <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <template v-if="item.type === 'text'">
        <div>
          <div class="changedValue" v-if="[AuditCode['cmdb.location.delete'], AuditCode['cmdb.location.unassignContact'], AuditCode['cmdb.location.selectiveUpdate']].includes(auditCode as AuditCode)">"{{ changedValue[item.key] }} "</div>
          <div class="originalValue" v-if="[AuditCode['cmdb.location.create'], AuditCode['cmdb.location.selectiveUpdate']].includes(auditCode as AuditCode)">"{{ originalValue[item.key] }}"</div>
        </div>
      </template>
      <template v-if="item.type === 'contacText'">
        <div>
          <div class="changedValue" v-if="[AuditCode['cmdb.location.delete'], AuditCode['cmdb.location.unassignContact']].includes(auditCode as AuditCode)">{{ changedValue[item.key] }} ({{ contactsType[contacType] }})</div>
          <div class="originalValue" v-if="[AuditCode['cmdb.location.create']].includes(auditCode as AuditCode) && originalValue[item.key]">{{ originalValue[item.key] }}({{ contactsType[contacType] }})</div>
        </div>
      </template>
      <div v-if="item.type === 'text1'">
        <div class="changedValue" v-for="v in changedValue[item.key]" :key="v">
          {{ '"' + v + '"' }}
        </div>
        <div class="originalValue" v-for="v in originalValue[item.key]" :key="v">
          {{ '"' + v + '"' }}
        </div>
      </div>
      <template v-else-if="item.type === 'tag'">
        <div class="tags">
          <el-tag :type="'success'" v-if="[AuditCode['cmdb.location.delete']].includes(auditCode as AuditCode)">{{ booleans[changedValue[item.key] + ""] }}</el-tag>
          <el-tag :type="'danger'" v-if="[AuditCode['cmdb.location.create']].includes(auditCode as AuditCode)">{{ booleans[originalValue[item.key] + ""] }}</el-tag>
        </div>
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });
import { operationLogger, contactsType } from "@/api/loggerType";

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };

const formOption: CurrentLogFormItems[] = [
  { label: "场所名称", key: "name", type: "text" },
  { label: "描述", key: "description", type: "text" },
  { label: "地区", key: "name", type: "text" },
  { label: "区域", key: "regionName", type: "text" },
  { label: "外部ID", key: "externalId", type: "text" },
  { label: "联系人", key: "contactName", type: "contacText" },
  { label: "时区", key: "zoneId", type: "text" },
  { label: "国家", key: "country", type: "text" },
  { label: "是否激活", key: "active", type: "tag" },
  { label: "地址", key: "address", type: "text1" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();
const contacType = ref<string>();
const originalValue = ref<any>({});

const changedValue = ref<any>({});
const operationType = ref<string>("");
const auditCode = ref<string>("");
enum AuditCode {
  "cmdb.location.create" = "cmdb.location.create",
  "cmdb.location.selectiveUpdate" = "cmdb.location.selectiveUpdate",
  "cmdb.location.delete" = "cmdb.location.delete",
  "cmdb.location.unassignContact" = "cmdb.location.unassignContact",
  "cmdb.location.assignContact" = "cmdb.location.assignContact",
}
function handleLoggerInfo() {
  operationLogger.forEach((v, i) => {
    if (v.auditCode == props.data.auditCode) {
      auditCode.value = v.auditCode;
      operationType.value = v.type;
    }
  });
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  changedValue.value = new Function("return" + props.data.changedValue)() || {};
  originalValue.value.zoneId = originalValue.value?.zoneId ? Zone[originalValue.value.zoneId] : originalValue.value?.zoneId;

  changedValue.value.zoneId = changedValue.value?.zoneId ? Zone[changedValue.value.zoneId] : changedValue.value?.zoneId;
  // contacType.value = changedValue.value.contactType || originalValue.value.contactType;
  if (changedValue.value.length > 0) {
    contacType.value = changedValue.value[0].contactType;
    changedValue.value.contactName = changedValue.value.map((v: any) => v.contactName).join(",");
  }
  if (JSON.stringify(originalValue.value) != "{}") {
    if (originalValue.value.length > 0) {
      contacType.value = originalValue.value[0].contactType;

      originalValue.value.contactName = originalValue.value.map((v: any) => v.contactName).join(",");
    } else {
      contacType.value = originalValue.value.contactType;
    }
  }

  currentLogFormItems.value = formOption.filter((v) => {
    if (!originalValue.value[v.key] && !changedValue.value[v.key]) return false;
    // console.log(, changedValue.value[v.key], 104);
    if (originalValue.value[v.key] instanceof Array && changedValue.value[v.key] instanceof Array) {
      return JSON.stringify(originalValue.value[v.key]) !== JSON.stringify(changedValue.value[v.key]);
    }
    if (originalValue.value[v.key] == changedValue.value[v.key]) return false;
    else return true;
  });
}

onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
.changedValueParent {
  font-weight: 600;
}
.originalValueParent {
  color: #777;
  font-size: 12px;
}
.update {
  font-weight: 600;
  color: #000;
  position: absolute;
  left: -90px;
  top: 28px;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  justify-content: space-between;
  height: 55px;
}
</style>
