<template>
  <el-form :model="{}" label-position="left">
    <div style="font-weight: 600; color: #000">更新</div>

    <el-form-item :label="label" v-for="item in changedValue" :key="item">
      <div>
        <!-- {{ item }} -->
        <div :style="{ marginLeft: '50px' }">
          <div style="display: flex; max-width: 350px; flex-wrap: wrap">
            <div class="icon-right">
              <el-tag :type="'danger'">{{ item.beforeEventSeverity }}</el-tag>

              <el-icon style="margin: 0 2px"><Right /></el-icon>
              <el-tag :type="'success'">{{ item.afterEventSeverity }}</el-tag>
            </div>
          </div>
        </div>

        <!-- <template v-else-if="[/*'vendorIds', 'modelNumbers', 'serialNumbers', */ 'assetNumbers' /* 'typeIdsp', 'grouIds' */].includes(item.key)">

            <div class="changedValue">"{{ JSON.parse(changedValue[item.key]).join() }}"</div>
            <div class="originalValue">"{{ JSON.parse(originalValue[item.key]).join() }}"</div>
          </template>
          <template v-else>
            <div class="changedValue">"{{ changedValue[item.key] }}"</div>
            <div class="originalValue">"{{ originalValue[item.key] }}"</div>
          </template> -->
      </div>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";
import { Right, BottomRight, TopRight } from "@element-plus/icons-vue";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };
// 告警分类字段待增加
const formOption: CurrentLogFormItems[] = [{ label: "sourceName", key: "mappings", type: "text" }];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<any>({});

const changedValue = ref<any>({});
const label = ref("");
function handleLoggerInfo() {
  // originalValue.value = new Function("return" + props.data.originalValue)() || {};
  changedValue.value = new Function("return" + props.data.auditInfo)() || [];
  console.log(changedValue.value);
  label.value = changedValue.value[0]?.sourceName + "映射关系";
  console.log(label);
  // formOption.map((v) => (v.label = changedValue.value[0].sourceName + "映射关系"));
  // currentLogFormItems.value = formOption.filter((v) => changedValue.value[v.key]);
}

onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
.icon-right {
  display: flex;
  align-items: center;
  margin-right: 5px;
  margin-bottom: 5px;
  // flex-wrap: nowrap;
}
</style>
