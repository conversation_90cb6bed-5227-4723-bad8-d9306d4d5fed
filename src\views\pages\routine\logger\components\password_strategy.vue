<template>
  <el-form :model="{}" label-position="left">
    <div style="font-weight: 600; color: #000">更新</div>
    <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <template v-if="item.type === 'text'">
        <!-- <div v-if="['afterAccountExpirationDate'].includes(item.key)">
          <div class="changedValue">"{{ moment(Number(changedValue[item.key])).format("YYYY-MM-DD") }}"</div>
          <div class="originalValue">"{{ moment(Number(originalValue[item.beforeKey])).format("YYYY-MM-DD") }}"</div>
        </div> -->
        <div>
          <!-- <template> -->
          <div class="changedValue">"{{ handleValueFormet(changedValue, item.key) }}"</div>
          <div class="originalValue">"{{ handleValueFormet(originalValue, item.beforeKey) }}"</div>
          <!-- </template> -->
        </div>
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";
import moment from "moment";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string; beforeKey: string };

const formOption: CurrentLogFormItems[] = [
  { label: "最小密码长度", key: "afterMinLength", type: "text", beforeKey: "beforeMinLength" },
  { label: "强化密码复杂度", key: "afterHardening", type: "text", beforeKey: "beforeHardening" },
  { label: "用户可以修改密码", key: "afterAllowUserChange", type: "text", beforeKey: "beforeAllowUserChange" },
  { label: "密码过期", key: "afterPasswordExpire", type: "text", beforeKey: "beforePasswordExpire" },
  { label: "密码历史", key: "afterHistPasswordLimit", type: "text", beforeKey: "beforeHistPasswordLimit" },
  { label: "密码最长使用期限", key: "afterPasswordExpireDays", type: "text", beforeKey: "beforePasswordExpireDays" },
  { label: "账号有效期", key: "afterAccountExpirationDate", type: "text", beforeKey: "beforeAccountExpirationDate" },
];
// {\"minLength\":null,\"minLengthConfigType\":\"DISABLE\",\"hardening\":null,\"allowUserChange\":true,\"histPasswordLimit\":5,\"histPasswordLimitConfigType\":\"CUSTOM\",\"passwordExpire\":true,\"passwordExpireDays\":96,\"accountExpirationDate\":*************,\"accountExpirationDateConfigType\":\"CUSTOM\",\"id\":599468286756782080,\"strategyKey\":\"TENANT_511378437676466176\",\"version\":175,\"createdTime\":*************,\"updatedTime\":*************}
// const OriginFormOption: CurrentLogFormItems[] = [
//   { label: "最小密码长度", key: "beforeMinLengh", type: "text" },
//   { label: "强化密码复杂度", key: "beforeHardenig", type: "text" },
//   { label: "用户可以修改密码", key: "beforeAllowUserChane", type: "text" },
//   { label: "密码过期", key: "beforePasswordExpire", type: "text" },
//   { label: "密码历史", key: "beforeHistPasswordLimt", type: "text" },
//   { label: "密码最长使用期限", key: "beforePasswordExpireDays", type: "text" },
// ];
// beforeHardening

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<any>({});

const changedValue = ref<any>({});

function handleLoggerInfo() {
  originalValue.value = new Function("return" + props.data.auditInfo)() || {};

  changedValue.value = new Function("return" + props.data.auditInfo)() || {};

  console.log(originalValue, changedValue);
  currentLogFormItems.value = formOption.filter((v) => {
    if (originalValue.value[v.beforeKey] && changedValue.value[v.key]) return true;
    else return false;
  });
}

function handleValueFormet(data, key) {
  try {
    if (["afterAccountExpirationDate", "beforeAccountExpirationDate"].includes(key) && !isNaN(Number(data[key]))) return moment(Number(data[key])).format("YYYY-MM-DD");
    return data[key];
  } catch (error) {
    return "";
  }
}

onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
.tags {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  justify-content: space-between;
  height: 55px;
}
</style>
