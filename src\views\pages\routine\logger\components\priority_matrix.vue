<template>
  <el-form :model="{}" label-position="left">
    <div style="font-weight: 600; color: #000">更新</div>
    <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <!-- {{ item }} -->
      <template v-if="item.type === 'text'">
        <div>
          <!-- <template> -->
          <!-- {{ changedValue }} -->
          <div :style="{ marginLeft: '50px' }">
            <div style="display: flex; align-items: center">
              <div>
                <div v-if="changedValue.urgency && changedValue.severity">
                  <span class="changedValue">告警严重性->工单紧急性 </span>

                  <el-tag style="width: 89px" :type="'success'">{{ changedValue.severity }}</el-tag>
                  <el-icon style="margin: 0 2px"><Right /></el-icon>
                  <el-tag style="width: 89px" :type="'success'">{{ changedValue.urgency }}</el-tag>
                  <!-- <el-icon style="margin: 0 2px"><BottomRight /></el-icon> -->
                  <!-- <el-icon style="margin: 0 2px"><Right /></el-icon> -->
                </div>
                <div v-if="changedValue.importance && changedValue.influence">
                  <span class="changedValue">设备重要性->工单影响性 </span>
                  <el-tag style="width: 89px" :type="'success'">{{ changedValue.importance }}</el-tag>
                  <el-icon style="margin: 0 2px"><Right /></el-icon>
                  <el-tag style="width: 89px" :type="'success'">{{ changedValue.influence }}</el-tag>
                  <!-- <el-icon style="margin: 0 2px"><TopRight /></el-icon> -->
                  <!-- <el-icon style="margin: 0 2px"><Right /></el-icon> -->
                </div>
                <div v-if="changedValue.priority">
                  <span class="changedValue">优先级-> </span>
                  <el-tag style="width: 89px" :type="'success'">{{ changedValue.priority }}</el-tag>
                </div>
              </div>
              <div>
                <!-- <el-tag style="width: 89px" :type="'success'">{{ 123456 }}</el-tag> -->
              </div>
            </div>
            <div style="display: flex; align-items: center">
              <div>
                <div v-if="changedValue.urgency && changedValue.severity">
                  <span class="originalValue">告警严重性->工单紧急性 </span>
                  <el-tag style="width: 89px" :type="'danger'">{{ changedValue.severity }}</el-tag>

                  <el-icon style="margin: 0 2px"><Right /></el-icon>
                  <el-tag style="width: 89px" :type="'danger'">{{ originalValue.urgency }}</el-tag>

                  <!-- <el-icon style="margin: 0 2px"><BottomRight /></el-icon> -->
                  <!-- <el-icon style="margin: 0 2px"><Right /></el-icon> -->
                </div>
                <div v-if="changedValue.importance && changedValue.influence">
                  <span class="originalValue">设备重要性->工单影响性 </span>
                  <el-tag style="width: 89px" :type="'danger'">{{ changedValue.importance }}</el-tag>
                  <el-icon style="margin: 0 2px"><Right /></el-icon>
                  <el-tag style="width: 89px" :type="'danger'">{{ originalValue.influence }}</el-tag>
                  <!-- <el-icon style="margin: 0 2px"><TopRight /></el-icon> -->
                  <!-- <el-icon style="margin: 0 2px"><Right /></el-icon> -->
                </div>
                <div v-if="originalValue.priority">
                  <span class="originalValue">优先级-> </span>
                  <el-tag style="width: 89px" :type="'danger'">{{ originalValue.priority }}</el-tag>
                </div>
              </div>
              <div>
                <!-- <el-tag style="width: 89px" :type="'danger'">{{ originalValue[item.key]?.priority }}</el-tag> -->
              </div>
            </div>
          </div>
        </div>
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";
import { Right, BottomRight, TopRight } from "@element-plus/icons-vue";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };
// 告警分类字段待增加
const formOption: CurrentLogFormItems[] = [{ label: "事件优先级", key: "matrix", type: "text" }];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<any>({});

const changedValue = ref<any>({});

function handleLoggerInfo() {
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  changedValue.value = new Function("return" + props.data.changedValue)() || {};

  if (changedValue.value.alertOrderMappingItems) {
    changedValue.value.alertOrderMappingItems.forEach((v: any) => {
      changedValue.value.urgency = v.urgency;
      changedValue.value.severity = v.severity;
    });
  }
  if (changedValue.value.deviceOrderMappingItems) {
    changedValue.value.deviceOrderMappingItems.forEach((v: any) => {
      changedValue.value.importance = v.importance;
      changedValue.value.influence = v.influence;
    });
  }
  if (changedValue.value.priorityMatrixItems) {
    changedValue.value.priorityMatrixItems.forEach((v: any) => {
      changedValue.value.priority = v.priority;
    });
    originalValue.value.priority = originalValue.value.priorityMatrixItems.filter((v: any) => v.influence === changedValue.value.priorityMatrixItems[0].influence && v.urgency === changedValue.value.priorityMatrixItems[0]?.urgency)[0]?.priority;
  }

  originalValue.value.urgency = originalValue.value.alertOrderMappingItems.filter((v: any) => v.severity === changedValue.value.severity)[0]?.urgency;
  originalValue.value.influence = originalValue.value.deviceOrderMappingItems.filter((v: any) => v.importance === changedValue.value.importance)[0]?.influence;

  // console.log(originalValue.value.urgency)
  // if (originalValue.value.items.length) originalValue.value.matrix = originalValue.value.items.filter((v: any) => v.eventSeverity === changedValue.value[0]?.eventSeverity && v.deviceImportance === changedValue.value[0]?.deviceImportance)[0];

  // if (changedValue.value.length)
  currentLogFormItems.value = formOption;
}

onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
.icon-right {
  display: flex;
  align-items: center;
  margin-right: 5px;
  margin-bottom: 5px;
  // flex-wrap: nowrap;
}
</style>
