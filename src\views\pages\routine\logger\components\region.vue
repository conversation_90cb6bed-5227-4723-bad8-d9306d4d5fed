<template>
  <el-form :model="{}" label-position="left">
    <!-- <div style="font-weight: 600; color: #000; margin-left: 90px">{{ changedValue.parentLabel }}</div> -->
    <div style="font-weight: 600; color: #000">{{ operationType }}</div>

    <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <template v-if="item.type === 'text'">
        <div>
          <template v-if="['parentName'].includes(item.key) && changedValue['parentId']">
            <div class="changedValueParent">{{ changedValue[item.key] }}</div>
            <div class="originalValueParent">{{ changedValue[item.tag] }}</div>
            <!-- <div class="update">更新</div> -->
          </template>

          <template v-else>
            <div class="changedValue">"{{ changedValue[item.key] }}"</div>
            <div class="originalValue" v-if="operationType != '新增'">"{{ originalValue[item.key] }}"</div>
          </template>
        </div>
      </template>
      <template v-else-if="item.type === 'tag'">
        <div class="tags">
          <el-tag style="margin-right: 10px" :type="'success'" v-if="operationType != '删除'">{{ booleans[changedValue[item.key] + ""] }}</el-tag>
          <el-tag :type="'danger'" v-if="operationType != '新增'">{{ booleans[originalValue[item.key] + ""] }}</el-tag>
        </div>
      </template>
      <template v-if="item.type === 'contacText'">
        <div>
          <div class="changedValue" v-if="operationType != '删除' && operationType != '移除'">{{ changedValue[item.key] }} ({{ contactsType[contacType] }})</div>
          <div class="originalValue" v-if="operationType != '新增'">{{ originalValue[item.key] }}({{ contactsType[contacType] }})</div>
        </div>
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });

type CurrentLogFormItems = { label: string; source?: string; parent?: string; key: string; type: string; tag?: string };
// 父区域名称/标签字段待增加
import { operationLogger, contactsType } from "@/api/loggerType";
import { log } from "console";

const formOption: CurrentLogFormItems[] = [
  { label: "父区域", key: "parentName", type: "text", tag: "parentLabel", parent: "parent" },
  { label: "子区域", key: "name", type: "text", parent: "parent" },
  { label: "标签", key: "label", type: "text" },
  { label: "描述", key: "description", type: "text" },
  { label: "外部ID", key: "externalId", type: "text" },
  { label: "是否激活", key: "active", type: "tag" },
  { label: "联系人", key: "contactName", type: "contacText" },
  { label: "经度", key: "longitude", type: "text" },
  { label: "纬度", key: "latitude", type: "text" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<any>({});

const changedValue = ref<any>({});
const contacType = ref<string>();
const operationType = ref<string>("");
function handleLoggerInfo() {
  operationLogger.forEach((v, i) => {
    if (v.auditCode == props.data.auditCode) {
      operationType.value = v.type;
    }
  });
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  changedValue.value = new Function("return" + props.data.changedValue)() || {};
  //   changedValue.value.parentId = 1;

  // contacType.value = changedValue.value.contactType || originalValue.value.contactType;
  // changedValue.value.parentName = "1";
  // console.log(changedValue.value);
  // if(changedValue.value.lenght>0){

  // }
  // console.log(changedValue.value.lenght);
  if (changedValue.value.length > 0) {
    contacType.value = changedValue.value[0].contactType;
    changedValue.value.contactName = changedValue.value.map((v: any) => v.contactName).join(",");
  }
  if (JSON.stringify(originalValue.value) != "{}") {
    if (originalValue.value.length > 0) {
      contacType.value = originalValue.value[0].contactType;

      originalValue.value.contactName = originalValue.value.map((v: any) => v.contactName).join(",");
    } else {
      contacType.value = originalValue.value.contactType;
    }
  }
  currentLogFormItems.value = formOption.filter((v) => {
    if (operationType.value == "新增") {
      if (changedValue.value.parentName == null) {
        if (v.key == "name") {
          v.label = "父区域";
        }
      }
    }
    if (originalValue.value[v.key] == changedValue.value[v.key]) return false;
    else return true;
  });
  // console.log(originalValue.value, changedValue.value[0], contacType.value);
}

onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
.changedValueParent {
  font-weight: 600;
}
.originalValueParent {
  color: #777;
  font-size: 12px;
}
.update {
  font-weight: 600;
  color: #000;
  position: absolute;
  left: -90px;
  bottom: -25px;
}
</style>
