<template>
  <el-form :model="{}" label-position="left" v-if="props.data.success">
    <p>{{ changedValue.resourceName }}</p>
    <div style="font-weight: 600; color: #000">{{ operationType }}</div>
    <div v-if="deviceRelationOther[props.data.auditCode] != undefined">
      <div v-for="(v, key) in deviceRelationOther" :key="v">
        <div v-if="props.data.auditCode == key">
          <el-form-item :label="v" :key="v">
            <div v-if="Array.isArray(changedValue)">
              <div class="changedValue" v-for="data in changedValue" :key="data">
                {{ data.name }}
              </div>
            </div>
            <div v-else>
              <div class="changedValue">
                {{ changedValue.name || changedValue.ruleName }}
              </div>
              <div class="changedValue" v-html="changedValue.logNote"></div>
            </div>
          </el-form-item>
        </div>
      </div>
    </div>

    <el-form-item v-else :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <template v-if="item.type === 'text'">
        <div v-if="item.source">
          <template v-if="Object.keys(booleans).includes(changedValue[item.source][item.key])">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ booleans[changedValue[item.source][item.key]] }}"</div>
            <div class="originalValue">"{{ booleans[originalValue[item.source][item.key]] }}"</div>
          </template>
          <template v-else-if="['modelNumbers', 'serialNumbers', 'assetNumbers'].includes(item.key)">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ JSON.parse(changedValue[item.source][item.key]).join() }}"</div>
            <div class="originalValue">"{{ JSON.parse(originalValue[item.source][item.key]).join() }}"</div>
          </template>
          <template v-else>
            <div class="changedValue">"{{ changedValue[item.source][item.key] }}"</div>
            <div class="originalValue">"{{ originalValue[item.source][item.key] }}"</div>
          </template>
        </div>
        <div v-else>
          <template v-if="['active', 'delivery'].includes(item.key)">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ booleans[changedValue[item.key] + ""] }}"</div>
            <div class="originalValue">"{{ booleans[originalValue[item.key] + ""] }}"</div>
          </template>
          <template v-else-if="['modelNumbers', 'serialNumbers', 'assetNumbers' /* 'typeIdsp', 'grouIds' */].includes(item.key)">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ JSON.parse(changedValue[item.source][item.key]).join() }}"</div>
            <div class="originalValue">"{{ JSON.parse(originalValue[item.source][item.key]).join() }}"</div>
          </template>
          <template v-else>
            <div class="changedValue">"{{ changedValue[item.key] }}"</div>
            <div class="originalValue">"{{ originalValue[item.key] }}"</div>
          </template>
        </div>
      </template>
      <template v-else-if="item.type === 'tags'">
        <div>
          <div>
            <el-tag class="tw-mr-2" type="success" v-for="tag in changedValue[item.key]" :key="`${props.data.resourceType}.${item.key}-${tag}`">{{ tag }}</el-tag>
          </div>
          <div>
            <el-tag class="tw-mr-2" type="danger" v-for="tag in originalValue[item.key]" :key="`${props.data.resourceType}.${item.key}-${tag}`">{{ tag }}</el-tag>
          </div>
        </div>
      </template>

      <template v-else-if="item.type === 'tag'">
        <div>
          <div>
            <el-tag class="tw-mr-2" type="success">{{ changedValue[item.key] }}</el-tag>
          </div>
          <div>
            <el-tag class="tw-mr-2" type="danger">{{ originalValue[item.key] }}</el-tag>
          </div>
        </div>
      </template>
      <template v-if="item.type === 'contacText'">
        <div>
          <div class="changedValue" v-if="operationType != '删除' && operationType != '移除'">{{ changedValue[item.key] }} ({{ contactsType[contacType] }})</div>
          <div class="originalValue" v-if="operationType != '新增' && originalValue[item.key]">{{ originalValue[item.key] }}({{ contactsType[contacType] }})</div>
        </div>
      </template>
      <template v-if="item.type === 'MonitorText'">
        <div>
          <div v-for="v in changedValue[item.key]" :key="v">
            <div class="changedValue" v-if="operationType != '删除' && operationType != '移除'">"{{ v }}"</div>
          </div>
          <div v-for="v in originalValue[item.key]" :key="v">
            <div class="originalValue" v-if="operationType != '新增'">"{{ v }}"</div>
          </div>
        </div>
      </template>
      <template v-if="item.type === 'timeText'">
        <div>
          <div>
            <div class="changedValue" v-if="operationType != '删除' && operationType != '移除'">"{{ Number(changedValue[item.key]) ? moment(Number(changedValue[item.key])).format("YYYY-MM-DD HH:mm:ss") : "" }}"</div>
          </div>
          <div>
            <div class="originalValue">"{{ Number(originalValue[item.key]) ? moment(Number(originalValue[item.key])).format("YYYY-MM-DD HH:mm:ss") : "" }}"</div>
          </div>
        </div>
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";

import { getdeviceTypeList, DeviceTypeItem } from "@/views/pages/apis/deviceManage";
import { getdeviceGroupList, deviceGroupItem } from "@/views/pages/apis/deviceManage";
import { getAlarmClassificationList, SlaConfigList } from "@/views/pages/apis/alarmClassification";
import { getRegionsTenantCurrent, RegionsTenant } from "@/views/pages/apis/regionManage";
import { getLocationsTenantCurrent, Locations } from "@/views/pages/apis/locationManang";
import { getSupplierList, getLineSupplierList } from "@/views/pages/apis/supplier";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});
import moment from "moment";

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };
import { operationLogger, contactsType } from "@/api/loggerType";
const auditLogFormItems = ref([
  { origin: "resource", operation: "设备文件", type: "上传", name: "上传文件", auditCode: "cmdb.file:upload" },
  { origin: "resource", operation: "设备文件", type: "上传", name: "多文件上传", auditCode: "cmdb.file:multi:upload" },
  { origin: "resource", operation: "设备文件", type: "删除", name: "删除文件", auditCode: "cmdb.resource_file:delete" },
  { origin: "resource", operation: "设备文件", type: "上传", name: "上传文件", auditCode: "cmdb.resource_file:create" },
  { origin: "resource", operation: "日志文件", type: "新增", name: "上传资源日志", auditCode: "cmdb.resource_log:create" },
  { origin: "resource", operation: "日志文件", type: "更新", name: "更新资源日志", auditCode: "cmdb.resource_log:update" },
  { origin: "resource", operation: "日志文件", type: "删除", name: "删除资源日志", auditCode: "cmdb.resource_log:delete" },
  { origin: "resource", operation: "日志文件", type: "删除", name: "删除资源日志附件", auditCode: "cmdb.resource_log:deleteAttachment" },
  { origin: "resource", operation: "日志文件", type: "更新", name: "保存资源日志附件", auditCode: "cmdb.resource_log:saveAttachment" },
  { origin: "resource", operation: "SLA服务", type: "绑定", name: "设备关联sla", auditCode: "ec.sla.device.relation" },
  { origin: "resource", operation: "SLA服务", type: "解绑", name: "移除设备关联sla", auditCode: "ec.sla.device.remove" },
  { origin: "resource", operation: "设备", type: "创建", name: "创建资源", auditCode: "cmdb.resource.create" },
  { origin: "resource", operation: "设备", type: "更新", name: "选择性更新资源信息", auditCode: "cmdb.resource.selectivityUpdate" },
  { origin: "resource", operation: "设备", type: "删除", name: "删除资源", auditCode: "cmdb.resource.delete" },
  { origin: "resource", operation: "设备", type: "移除", name: "取消资源联系人", auditCode: "cmdb.resource.unassignContact" },
  { origin: "resource", operation: "设备", type: "分配", name: "为资源分配联系人", auditCode: "cmdb.resource.assignContact" },
]);

const deviceRelationOther = ref({
  "cmdb.file:upload": "文件",
  "cmdb.file:multi:upload": "文件",
  "cmdb.resource_log:create": "日志",
  "cmdb.resource_log:update": "日志",
  "cmdb.resource_log:delete": "日志",
  "cmdb.resource_log:deleteAttachment": "日志文件",
  "cmdb.resource_log:saveAttachment": "日志文件",
  "ec.sla.device.relation": "sla服务",
  "ec.sla.device.remove": "sla服务",
  "cmdb.resource_file:create": "上传文件",
  "cmdb.resource_file:delete": "删除文件",
  // "cmdb.resource.selectivityUpdate": "更新",
  // "cmdb.resource.create": "创建",
  // "cmdb.resource.delete": "删除",
});

const formOption: CurrentLogFormItems[] = [
  { label: "设备名称", key: "name", type: "text" },
  { label: "描述", key: "description", type: "text" },
  { label: "地区", key: "regionName", type: "text" },
  { label: "场所", key: "locationName", type: "text" },
  { label: "外部ID", key: "externalId", type: "text" },
  { label: "标签", key: "tags", type: "tags" },
  { label: "IP地址", source: "config", key: "ipAddress", type: "text" },
  { label: "是否激活", key: "active", type: "text" },
  { label: "是否交付", key: "delivery", type: "text" },
  { label: "是否为动态IP", source: "config", key: "dynamicIp", type: "text" },
  { label: "告警分类", key: "alarmCategoryNames", type: "tags" },
  { label: "是否确认告警", source: "config", key: "ackRequired", type: "text" },
  { label: "是否自动事件", source: "config", key: "nmsTicketing", type: "text" },
  { label: "重要性", key: "importance", type: "tag" },
  { label: "远程登录方式", source: "config", key: "connectAuthType", type: "text" },

  { label: "设备供应商", key: "vendorNames", type: "tags" },
  { label: "设备型号", source: "config", key: "modelNumbers", type: "text" },
  { label: "设备序列号", source: "config", key: "serialNumbers", type: "text" },
  { label: "资产编号", source: "config", key: "assetNumbers", type: "text" },
  { label: "设备类型", key: "typeNames", type: "tags" },
  { label: "设备分组", key: "groupNames", type: "tags" },
  { label: "监控源", key: "monitorSources", type: "MonitorText" },
  { label: "业务单位", key: "unit", type: "text" },
  { label: "下线时间", key: "resourceOfflineTime", type: "timeText" },
  { label: "上线时间", key: "resourceOnlineTime", type: "timeText" },
  { label: "联系人", key: "contactName", type: "contacText" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();
const operationType = ref("");
const originalValue = ref<Record<string, any>>({});

const changedValue = ref<Record<string, any>>({});

const changeOther = ref<Record<string, any>>({});
const originalOther = ref<Record<string, any>>({});
const contacType = ref<string>();
function handleLoggerInfo() {
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  changedValue.value = new Function("return" + props.data.changedValue)() || {};

  // console.log(auditLogFormItems.value);
  // console.log(props.data.auditCode);
  // console.log(auditLogFormItems.value[props.data.auditCode]);
  // console.log(changedValue.value, originalValue.value);
  // if (auditLogFormItems.value[props.data.auditCode] != undefined) {
  if (changedValue.value.length > 0) {
    contacType.value = changedValue.value[0].contactType;
    changedValue.value.contactName = changedValue.value.map((v: any) => v.contactName).join(",");
  }
  if (JSON.stringify(originalValue.value) != "{}") {
    if (originalValue.value.length > 0) {
      contacType.value = originalValue.value[0].contactType;

      originalValue.value.contactName = originalValue.value.map((v: any) => v.contactName).join(",");
    } else {
      contacType.value = originalValue.value.contactType;
    }
  }
  auditLogFormItems.value.forEach((item) => {
    if (item.auditCode === props.data.auditCode) {
      operationType.value = item.type;
    }
  });

  if (Object.keys(originalValue.value).length > 0) {
    currentLogFormItems.value = formOption.filter((v) => {
      if (originalValue.value.config && changedValue.value.config) {
        if (v.source && JSON.stringify(originalValue.value[v.source][v.key]) != JSON.stringify(changedValue.value[v.source][v.key])) {
          return true;
        }
      }

      if (JSON.stringify(originalValue.value[v.key]) == JSON.stringify(changedValue.value[v.key])) {
        return false;
      } else if (isNullOrEmpty(originalValue.value[v.key]) && isNullOrEmpty(changedValue.value[v.key])) return false;
      else return true;
    });
  } else {
    currentLogFormItems.value = formOption.filter((v) => {
      if (JSON.stringify(originalValue.value[v.key]) == JSON.stringify(changedValue.value[v.key])) {
        return false;
      } else if (isNullOrEmpty(originalValue.value[v.key]) && isNullOrEmpty(changedValue.value[v.key])) return false;
      else return true;
    });
  }

  // console.log(currentLogFormItems);

  // currentLogFormItems.value= Array.from(new Set(currentLogFormItems.value.map(item => JSON.stringify(item)))).map(item => JSON.parse(item));

  // handleOhterInfo();
}

function isNullOrEmpty(value) {
  return value == null || value == "" || value == "null";
}

const deviceTypeOption = ref<DeviceTypeItem[]>([]); // 设备类型
const deviceGroupOption = ref<deviceGroupItem[]>([]); // 设备分组:
const alarmClassificationOption = ref<SlaConfigList[]>([]); // 告警分类
const regionsTenantOption = ref<RegionsTenant[]>([]); // 区域
const locationsTenantOption = ref<Locations[]>([]); // 地点

async function handleOhterInfo() {
  const [deviceTypeList, deviceGroupList, alarmClassificationList, regionsTenantCurrent, locationsTenantCurrent] = await Promise.all([getdeviceTypeList({} as any), getdeviceGroupList({} as any), getAlarmClassificationList({} as any), getRegionsTenantCurrent({}), getLocationsTenantCurrent({})]);

  (function (data) {
    deviceTypeOption.value = data.data;
    // console.log("设备类型", deviceTypeOption.value);
    originalValue.value.typeNames = deviceGroupOption.value.filter((v) => originalValue.value.typeIds.includes(v.id)).map((v) => v.name);
    changedValue.value.typeNames = deviceGroupOption.value.filter((v) => changedValue.value.typeIds.includes(v.id)).map((v) => v.name);
  })(deviceTypeList);

  (function (data) {
    deviceGroupOption.value = data.data;
    // console.log("设备分组", deviceGroupOption.value);
    originalValue.value.groupNames = deviceGroupOption.value.filter((v) => originalValue.value.groupIds.includes(v.id)).map((v) => v.name);
    changedValue.value.groupNames = deviceGroupOption.value.filter((v) => changedValue.value.groupIds.includes(v.id)).map((v) => v.name);
  })(deviceGroupList);

  (function (data) {
    alarmClassificationOption.value = data.data;
    // console.log("告警分类", deviceGroupOption.value);
    originalValue.value.alertClassificationNames = alarmClassificationOption.value.filter((v) => originalValue.value.alertClassificationIds.includes(v.id)).map((v) => v.name);
    changedValue.value.alertClassificationNames = alarmClassificationOption.value.filter((v) => changedValue.value.alertClassificationIds.includes(v.id)).map((v) => v.name);
  })(alarmClassificationList);

  (function (data) {
    regionsTenantOption.value = data.data;
    // console.log("区域", regionsTenantOption.value);
    originalValue.value.regionName = (regionsTenantOption.value.find((v) => v.id === originalValue.value.regionId) || {}).name || "";
    changedValue.value.regionName = (regionsTenantOption.value.find((v) => v.id === changedValue.value.regionId) || {}).name || "";
  })(regionsTenantCurrent);

  (function (data) {
    locationsTenantOption.value = data.data;
    // console.log("场所", locationsTenantOption.value);
    originalValue.value.locationsName = (locationsTenantOption.value.find((v) => v.id === originalValue.value.locationId) || {}).name || "";
    changedValue.value.locationsName = (locationsTenantOption.value.find((v) => v.id === changedValue.value.locationId) || {}).name || "";
  })(locationsTenantCurrent);
}

onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
</style>
