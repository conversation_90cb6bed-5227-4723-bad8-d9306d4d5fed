<template>
  <el-form :model="{}" label-position="left" v-if="props.data.success">
    <div style="font-weight: 600; color: #000">{{ operationType }}</div>

    <div v-if="Object.keys(authValue).length < 1">
      <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
        <template v-if="item.type === 'text'">
          <div>
            <div class="changedValue">"{{ changedValue[item.key] }}"</div>
            <div class="originalValue" v-if="operationType != '新增'">"{{ originalValue[item.key] }}"</div>
          </div>
        </template>
      </el-form-item>
      <el-form-item :label="label" v-if="auditInfo.length > 0">
        <div v-if="operationType == '分配'">
          <div class="changedValue" v-for="v in auditInfo" :key="v">
            {{ v.name }}
          </div>
          <!-- <div class="originalValue" v-if="operationType != '新增'">"{{ originalValue[item.key] }}"</div> -->
        </div>
        <div v-if="operationType == '移除'">
          <div class="originalValue" v-for="v in auditInfo" :key="v">
            {{ v.name }}
          </div>
          <!-- <div class="originalValue" v-if="operationType != '新增'">"{{ originalValue[item.key] }}"</div> -->
        </div>
      </el-form-item>
      <div v-if="authType.length > 0">
        <el-form-item v-for="item in authType" :label="item.name" :key="item.id">
          <div v-for="v in authDataValue" :key="v.id">
            <el-tag v-if="item.id === v.catalogId" type="success" style="margin-left: 10px">
              {{ v.name }}
            </el-tag>
          </div>
        </el-form-item>
      </div>
    </div>
    <!-- <div v-else>此处是更改数据权限内容</div> -->
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from "vue";
import { LoggerItem } from "@/api/system";

import { getdeviceTypeList, DeviceTypeItem } from "@/views/pages/apis/deviceManage";
import { getdeviceGroupList, deviceGroupItem } from "@/views/pages/apis/deviceManage";
import { getAlarmClassificationList, SlaConfigList } from "@/views/pages/apis/alarmClassification";
import { getRegionsTenantCurrent, RegionsTenant } from "@/views/pages/apis/regionManage";
import { getLocationsTenantCurrent, Locations } from "@/views/pages/apis/locationManang";
import { getSupplierList, getLineSupplierList } from "@/views/pages/apis/supplier";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

import { operationLogger, contactsType } from "@/api/loggerType";
const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };

const formOption: CurrentLogFormItems[] = [
  { label: "角色名称", key: "name", type: "text" },
  { label: "描述", key: "note", type: "text" },
  // { label: "描述", key: "note", type: "text" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

// const adds: any = computed(() => {
//   console.log();
//   return "";
// });

const roleAuth = ref([]);
const originalValue = ref<Record<string, any>>({});
const operationLoggerItem = ref<any>({});
const label = ref("");
const changedValue = ref<Record<string, any>>({});
const authValue = ref<Record<string, any>>({});
const auditInfo = ref([]);
const operationType = ref<string>("");
const authType = ref([]);
const authDataValue = ref([]);
function handleLoggerInfo() {
  operationLogger.forEach((v, i) => {
    if (v.auditCode == props.data.auditCode) {
      operationType.value = v.type;
      operationLoggerItem.value = v;
    }
  });
  label.value = operationLoggerItem.value.name.split("分配")[1];
  // formOption.push({ label: , key: "names", type: "relation" });

  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  changedValue.value = new Function("return" + props.data.changedValue)() || {};
  // authValue.value = new Function("return" + props.data.authValue)() || {};
  roleAuth.value = new Function("return" + props.data.changedValue)() || [];
  // roleAuth.value[0]
  if (props.data.auditCode === "iam.role_auth.save") {
    roleAuth.value.catalogs.forEach((v, i) => {
      if (i < 5) {
        authType.value.push(v);
      }
    });
    roleAuth.value.permissions.forEach((v, i) => {
      authType.value.forEach((item) => {
        if (item.id === v.catalogId) {
          authDataValue.value.push(v);
        }
      });
    });
    // console.log(authType.value, authDataValue.value);
  }

  // authType
  auditInfo.value = new Function("return" + props.data.auditInfo)() || new Function("return" + props.data.changedValue)() || [];

  currentLogFormItems.value = formOption.filter((v) => {
    if (!originalValue.value[v.key] && !changedValue.value[v.key]) return false;
    if (originalValue.value[v.key] === changedValue.value[v.key]) return false;
    else return true;
  });

  // console.log("角色 adds", originalValue.value.catalogs.map((v) => ({ ...v, children: originalValue.value.permissions.filter((f) => f.id === v.catalogId) })));

  // try {
  //   // console.log(
  //   //   originalValue.value.catalogs.map((v) => {
  //   //     console.log();
  //   //     return v;
  //   //   })
  //   // );
  //   const originalPermissions = originalValue.value.catalogs.map((v) => ({ ...v, children: originalValue.value.permissions.filter((f) => v.id === f.catalogId) }));
  //   const changedPermissions = changedValue.value.catalogs.map((v) => ({ ...v, children: changedValue.value.permissions.filter((f) => v.id === f.catalogId) }));
  //   console.log(originalPermissions, changedPermissions);
  //   changedPermissions.map(async (v) => ({
  //     ...v,
  //     adds: await new Promise((resolve) => {
  //       // 增加了
  //       const beforeIds = originalPermissions.find((f: any) => f.id === v.id).children.map(({ id }: any) => id);
  //       const afterIds = v.children.map(({ id }: any) => id);
  //       console.log(`${v.name}-beforeIds`, beforeIds);
  //       console.log(`${v.name}-afterIds`, afterIds);
  //       resolve([]);
  //     }),
  //     removes: await new Promise((resolve) => {
  //       // 删除了
  //     }),
  //   }));

  // console.log(
  //   "adds",
  // );
  // } catch (error) {
  //   console.log(error);
  // }
  // handleOhterInfo();
}
onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
</style>
