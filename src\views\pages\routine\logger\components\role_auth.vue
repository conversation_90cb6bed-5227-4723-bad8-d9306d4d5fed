<template>
  <el-form :model="{}" label-position="left" v-if="props.data.success">
    <div style="font-weight: 600; color: #000">权限配置</div>

    <div class="role-auth">
      <el-form-item :label="'分配给角色'" label-width="120">
        {{ props.data.resourceName }}
      </el-form-item>
      <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.id}.${item.key}`" label-width="120">
        <template v-if="item.type === 'tags'">
          <!-- <el-tag type="success" >{{ 1 }}</el-tag> -->
          <div>
            <el-tag class="tw-mr-2" type="success" v-for="tag in insertValue" :key="`${props.data.id}.${item.key}-${tag}`">{{ tag.name }}</el-tag>
          </div>

          <div>
            <el-tag class="tw-mr-2" type="danger" v-for="tag in deleteValue" :key="`${props.data.id}.${item.key}-${tag}`">{{ tag.name }}</el-tag>
          </div>
        </template>
      </el-form-item>
    </div>
    <!-- <div v-else>此处是更改数据权限内容</div> -->
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from "vue";
import { LoggerItem } from "@/api/system";

import { getdeviceTypeList, DeviceTypeItem } from "@/views/pages/apis/deviceManage";
import { getdeviceGroupList, deviceGroupItem } from "@/views/pages/apis/deviceManage";
import { getAlarmClassificationList, SlaConfigList } from "@/views/pages/apis/alarmClassification";
import { getRegionsTenantCurrent, RegionsTenant } from "@/views/pages/apis/regionManage";
import { getLocationsTenantCurrent, Locations } from "@/views/pages/apis/locationManang";
import { getSupplierList, getLineSupplierList } from "@/views/pages/apis/supplier";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };

const formOption: CurrentLogFormItems[] = [
  // { label: "设备类型", key: "typeNames", type: "tags" },
  // { label: "设备分组", key: "groupNames", type: "tags" },
  { label: "告警分类", key: "alert_classification", type: "tags" },
  { label: "设备类型", key: "resourceType", type: "tags" },
  { label: "设备分组", key: "group", type: "tags" },
  { label: "设备", key: "resource", type: "tags" },
  { label: "区域", key: "region", type: "tags" },
  { label: "场所", key: "location", type: "tags" },
  { label: "联系人", key: "contact", type: "tags" },
  { label: "供应商", key: "vendor", type: "tags" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

// const adds: any = computed(() => {
//   console.log();
//   return "";
// });

const insertValue = ref<Record<string, any>>([]);

const deleteValue = ref<Record<string, any>>([]);
const authValue = ref<Record<string, any>>({});
const allMessage = ref<Record<string, any>>({});

function handleLoggerInfo() {
  authValue.value = new Function("return" + props.data.auditInfo)() || {};
  allMessage.value = props.data;
  // deleteValue.value = new Function("return" + props.data.auditInfo)() || {};
  if (Object.keys(authValue.value).length > 0) {
    insertValue.value = authValue.value.insert;
    deleteValue.value = authValue.value.delete;
  }

  // insertValue.value.(v=> )
  const insertType = (insertValue.value.find((v) => v) || {}).type || "";
  const delType = (deleteValue.value.find((v) => v) || {}).type || "";
  currentLogFormItems.value = formOption.filter((v) => {
    return v.key === (insertType || delType);

    // if (v.key && JSON.stringify(originalValue.value[v.source][v.key]) === JSON.stringify(changedValue.value[v.source][v.key])) return false;
    // else if (JSON.stringify(originalValue.value[v.key]) === JSON.stringify(changedValue.value[v.key])) return false;
    // else return true;
  });

  // console.log("角色 adds", originalValue.value.catalogs.map((v) => ({ ...v, children: originalValue.value.permissions.filter((f) => f.id === v.catalogId) })));

  // try {
  //   // console.log(
  //   //   originalValue.value.catalogs.map((v) => {
  //   //     console.log();
  //   //     return v;
  //   //   })
  //   // );
  //   const originalPermissions = originalValue.value.catalogs.map((v) => ({ ...v, children: originalValue.value.permissions.filter((f) => v.id === f.catalogId) }));
  //   const changedPermissions = changedValue.value.catalogs.map((v) => ({ ...v, children: changedValue.value.permissions.filter((f) => v.id === f.catalogId) }));
  //   console.log(originalPermissions, changedPermissions);
  //   changedPermissions.map(async (v) => ({
  //     ...v,
  //     adds: await new Promise((resolve) => {
  //       // 增加了
  //       const beforeIds = originalPermissions.find((f: any) => f.id === v.id).children.map(({ id }: any) => id);
  //       const afterIds = v.children.map(({ id }: any) => id);
  //       console.log(`${v.name}-beforeIds`, beforeIds);
  //       console.log(`${v.name}-afterIds`, afterIds);
  //       resolve([]);
  //     }),
  //     removes: await new Promise((resolve) => {
  //       // 删除了
  //     }),
  //   }));

  // console.log(
  //   "adds",
  // );
  // } catch (error) {
  //   console.log(error);
  // }
  // handleOhterInfo();
}
onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
.role-auth {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}
</style>
