<template>
  <el-form :model="{}" label-position="left">
    <div style="font-weight: 600; color: #000">{{ operationType }}</div>

    <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <template v-if="item.type === 'text'">
        <div v-if="item.source">
          <template v-if="Object.keys(booleans).includes(changedValue[item.source][item.key])">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ booleans[changedValue[item.source][item.key]] }}"</div>
            <div v-if="props.data.originalValue" class="originalValue">"{{ booleans[originalValue[item.source][item.key]] }}"</div>
          </template>
          <template v-else-if="[/*'vendorIds', 'modelNumbers', 'serialNumbers', */ 'assetNumbers' /* 'typeIdsp', 'grouIds' */].includes(item.key)">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ JSON.parse(changedValue[item.source][item.key]).join() }}"</div>
            <div v-if="props.data.originalValue" class="originalValue">"{{ JSON.parse(changedValue[item.source][item.key]).join() }}"</div>
          </template>
          <template v-else>
            <div class="changedValue">"{{ changedValue[item.source][item.key] }}"</div>
            <div v-if="props.data.originalValue" class="originalValue">"{{ originalValue[item.source][item.key] }}"</div>
          </template>
        </div>
        <div v-else>
          <template v-if="['vip', 'smsEnabled'].includes(item.key)">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ booleans[changedValue[item.key] + ""] }}"</div>
            <div v-if="props.data.originalValue" class="originalValue">"{{ booleans[originalValue[item.key] + ""] }}"</div>
          </template>
          <template v-else-if="[/*'vendorIds', 'modelNumbers', 'serialNumbers', */ 'assetNumbers' /* 'typeIdsp', 'grouIds' */].includes(item.key)">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ JSON.parse(changedValue[item.key]).join() }}"</div>
            <div v-if="props.data.originalValue" class="originalValue">"{{ JSON.parse(originalValue[item.key]).join() }}"</div>
          </template>
          <template v-else>
            <div class="changedValue">"{{ changedValue[item.key] }}"</div>
            <div v-if="props.data.originalValue" class="originalValue">"{{ originalValue[item.key] }}"</div>
          </template>
        </div>
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });
import { operationLogger, contactsType } from "@/api/loggerType";
type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };
// 线路供应商字段待增加,进度就是线路带宽
const formOption: CurrentLogFormItems[] = [
  { label: "服务编号", key: "number", type: "text" },
  { label: "描述", key: "description", type: "text" },
  { label: "线路供应商", key: "vendors", type: "text" },
  { label: "设备名称", key: "resourceName", type: "text" },
  { label: "类型", key: "type", type: "text" },
  { label: "线路宽带", key: "progress", type: "text" },
  { label: "产品", key: "product", type: "text" },
  // { label: "端口", key: "product", type: "text" },
  { label: "端口名称", key: "portName", type: "text" },
  { label: "服务等级", key: "serviceLevel", type: "text" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<any>({});

const changedValue = ref<any>({});

const operationType = ref<string>("");
function handleLoggerInfo() {
  operationLogger.forEach((v, i) => {
    if (v.auditCode == props.data.auditCode) {
      operationType.value = v.type;
    }
  });
  originalValue.value = new Function("return " + props.data.originalValue)() || {};
  originalValue.value.progress = originalValue.value.progress ? originalValue.value.progress : "";
  originalValue.value.vendors = originalValue.value.vendorList instanceof Array ? originalValue.value.vendorList.map((v: any) => v.name).join(",") : "";
  changedValue.value = new Function("return " + props.data.changedValue)() || {};
  changedValue.value.progress = changedValue.value.progress ? changedValue.value.progress : "";
  changedValue.value.vendors = changedValue.value.vendorList instanceof Array ? changedValue.value.vendorList.map((v: any) => v.name).join(",") : "";
  // currentLogFormItems.value = formOption.filter((v) => changedValue.value[v.key]);

  currentLogFormItems.value = formOption.filter((v) => {
    if (!originalValue.value[v.key] && !changedValue.value[v.key]) return false;

    if (originalValue.value[v.key] == changedValue.value[v.key]) return false;
    else return true;
  });
}

onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
</style>
