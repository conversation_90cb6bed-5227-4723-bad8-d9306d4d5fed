<template>
  <el-form :model="{}" label-position="left">
    <div style="font-weight: 600; color: #000" v-if="operationType != '禁用/启用'">{{ operationType }}</div>
    <div style="font-weight: 600; color: #000" v-if="props.data.auditCode == 'ec.sla.disable'">
      {{ changedValue.status ? "启用" : "禁用" }}
    </div>

    <div style="font-weight: 600; color: #000" v-if="currentLogFormItems && currentLogFormItems.length">SLA规则</div>
    <div v-if="props.data.auditCode != 'ec.sla.disable'">
      <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
        <div>
          <div class="changedValue" v-if="changedValue[item.key]">"{{ changedValue[item.key] }}"</div>
          <div class="originalValue" v-if="operationType != '新增'">"{{ originalValue[item.key] }}"</div>
        </div>
      </el-form-item>
      <div style="font-weight: 600; color: #000" v-if="resolveFormItems && resolveFormItems.length">事件处理时间等级</div>
      <div v-for="item in resolveFormItems" :key="`${props.data.resourceType}.${item.key}`">
        <el-form-item :label="item.label" v-if="changedResolveValue[item.key]">
          <div>
            <div class="changedValue">"{{ changedResolveValue[item.key] }}"</div>
            <div class="originalValue">"{{ originalResolveValue[item.key] }}"</div>
          </div>
        </el-form-item>
      </div>
      <div style="font-weight: 600; color: #000" v-if="resolveFormItems && respFormItems.length">事件响应时间等级</div>
      <el-form-item :label="item.label" v-for="item in respFormItems" :key="`${props.data.resourceType}.${item.key}`">
        <div>
          <div class="changedValue">"{{ changedRespValue[item.key] }}"</div>
          <div class="originalValue">"{{ originalRespValue[item.key] }}"</div>
        </div>
      </el-form-item>
    </div>
    <div v-if="props.data.auditCode == 'ec.sla.disable'">
      <el-form-item label="SLA">
        <div v-show="changedValue.status" class="changedValue">
          {{ changedValue.ruleName }}
        </div>
        <div v-show="!changedValue.status">{{ originalValue.ruleName }} <FontAwesomeIcon class="tw-mx-[5px]" :icon="faBan"></FontAwesomeIcon></div>
      </el-form-item>
    </div>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";
import { Right, BottomRight, TopRight } from "@element-plus/icons-vue";
import { faBan, faSquarePlus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });
import { operationLogger, contactsType } from "@/api/loggerType";

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };
// 告警分类字段待增加
const coverFormOption: CurrentLogFormItems[] = [
  { label: "SLA名称", key: "ruleName", type: "text" },
  { label: "描述", key: "ruleDesc", type: "text" },
  { label: "覆盖时间", key: "time", type: "text" },
  { label: "时区", key: "timeZone", type: "text" },
];
const resolveFormOption: CurrentLogFormItems[] = [
  { label: "事件优先级", key: "priority", type: "text" },
  { label: "容忍时间", key: "time", type: "text" },
  { label: "SLA状态", key: "urgencyType", type: "text" },
  { label: "名称", key: "name", type: "text" },
  { label: "描述", key: "description", type: "text" },
  { label: "定义", key: "definition", type: "text" },
];
const respFormOption: CurrentLogFormItems[] = [
  { label: "事件优先级", key: "priority", type: "text" },
  { label: "容忍时间", key: "time", type: "text" },
  { label: "SLA状态", key: "urgencyType", type: "text" },
  { label: "名称", key: "name", type: "text" },
  { label: "描述", key: "description", type: "text" },
  { label: "定义", key: "definition", type: "text" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();
const resolveFormItems = ref<CurrentLogFormItems[]>();
const respFormItems = ref<CurrentLogFormItems[]>();
// SLA规则基本信息
const originalValue = ref<any>({});
const changedValue = ref<any>({});
// 事件响应时间等级
const changedResolveValue = ref<any>({});
const originalResolveValue = ref<any>({});
// 事件处理时间等级
const changedRespValue = ref<any>({});
const originalRespValue = ref<any>({});

const operationType = ref<string>("");
function handleLoggerInfo() {
  operationLogger.forEach((v, i) => {
    if (v.auditCode == props.data.auditCode) {
      operationType.value = v.type;
    }
  });
  /**
   * 1.SLA规则基本信息
   */
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  originalValue.value.timeZone = originalValue.value.coverTimeCfg?.timeZone ? Zone[originalValue.value.coverTimeCfg.timeZone] : originalValue.value?.coverTimeCfg?.timeZone;
  changedValue.value = new Function("return" + props.data.changedValue)() || {};
  changedValue.value.timeZone = changedValue.value.coverTimeCfg?.timeZone ? Zone[changedValue.value.coverTimeCfg.timeZone] : changedValue.value?.coverTimeCfg?.timeZone;
  // changedValue.value.time = "7*24";
  // currentLogFormItems.value = coverFormOption.filter((v) => changedValue.value[v.key]);
  currentLogFormItems.value = coverFormOption.filter((v) => {
    if (!originalValue.value[v.key] && !changedValue.value[v.key]) return false;

    if (originalValue.value[v.key] == changedValue.value[v.key]) return false;
    else return true;
  });

  /**
   * 2.事件响应时间等级
   * @originalResolveValue 改变前事件响应时间等级
   * @changedResolveValue 改变后事件响应时间等级
   */
  originalResolveValue.value = new Function("return" + props.data.originalValue)() || {};
  originalResolveValue.value.priority = [...(originalResolveValue.value.eventResolveTimeLevels?.map((v: any) => v.priority) || []), ...(originalResolveValue.value.eventResolveDefaultResolves?.map((v: any) => "Default") || [])].join(",");
  // 容忍时间
  originalResolveValue.value.defTime = [];
  originalResolveValue.value.resTime = [];
  // SLA状态
  originalResolveValue.value.defurgencyType = [];
  originalResolveValue.value.resurgencyType = [];
  // 名称
  originalResolveValue.value.defName = [];
  originalResolveValue.value.resName = [];
  // 描述
  originalResolveValue.value.defDescription = [];
  originalResolveValue.value.resDescription = [];
  // 定义
  originalResolveValue.value.defDefinition = [];
  originalResolveValue.value.resDefinition = [];
  // p1-p8等级
  originalResolveValue.value.eventResolveTimeLevels?.length &&
    originalResolveValue.value.eventResolveTimeLevels?.forEach((v) => {
      originalResolveValue.value.resTime.push(v?.resolves?.map((t) => `${t.day > 0 ? t.day + "day" : ""}${t.hour > 0 ? t.hour + "h" : ""}${t.minute > 0 ? t.minute + "min" : ""}`));
      originalResolveValue.value.resurgencyType.push(v?.resolves?.map((s) => s.urgencyType));
      originalResolveValue.value.resName.push(v?.resolves?.map((s) => s.name));
      originalResolveValue.value.resDescription.push(v?.resolves?.map((s) => s.description));
      originalResolveValue.value.resDefinition.push(v?.resolves?.map((s) => s.definition));
    });
  // 默认等级
  originalResolveValue.value.eventResolveDefaultResolves?.length &&
    originalResolveValue.value.eventResolveDefaultResolves?.forEach((v) => {
      originalResolveValue.value.defTime.push(`${v.day ? v.day + "day" : ""}${v.hour ? v.hour + "h" : ""}${v.minute ? v.minute + "min" : ""}`);
    });
  originalResolveValue.value.defurgencyType = originalResolveValue.value.eventResolveDefaultResolves?.map((s) => s.urgencyType);
  originalResolveValue.value.defName = originalResolveValue.value.eventResolveDefaultResolves?.map((s) => s.name);
  originalResolveValue.value.defDescription = originalResolveValue.value.eventResolveDefaultResolves?.map((s) => s.description);
  originalResolveValue.value.defDefinition = originalResolveValue.value.eventResolveDefaultResolves?.map((s) => s.definition);
  // 重新赋值
  originalResolveValue.value.time = [...(originalResolveValue.value?.resTime || []), ...(originalResolveValue.value?.defTime || [])].join(",");
  originalResolveValue.value.urgencyType = [...(originalResolveValue.value?.resurgencyType || []), ...(originalResolveValue.value?.defurgencyType || [])].join(",");
  originalResolveValue.value.name = [...(originalResolveValue.value?.resName || []), ...(originalResolveValue.value?.defName || [])].join(",");
  originalResolveValue.value.description = [...(originalResolveValue.value?.resDescription || []), ...(originalResolveValue.value?.defDescription || [])].join(",");
  originalResolveValue.value.definition = [...(originalResolveValue.value?.resDefinition || []), ...(originalResolveValue.value?.defDefinition || [])].join(",");

  changedResolveValue.value = new Function("return" + props.data.changedValue)() || {};
  changedResolveValue.value.priority = [...(changedResolveValue.value?.eventResolveTimeLevels?.map((v: any) => v.priority) || []), ...(changedResolveValue.value?.eventResolveDefaultResolves?.map((v: any) => "Default") || [])].join(",");
  // 容忍时间
  changedResolveValue.value.defTime = [];
  changedResolveValue.value.resTime = [];
  // SLA状态
  changedResolveValue.value.defurgencyType = [];
  changedResolveValue.value.resurgencyType = [];
  // 名称
  changedResolveValue.value.defName = [];
  changedResolveValue.value.resName = [];
  // 描述
  changedResolveValue.value.defDescription = [];
  changedResolveValue.value.resDescription = [];
  // 定义
  changedResolveValue.value.defDefinition = [];
  changedResolveValue.value.resDefinition = [];
  // p1-p8等级
  changedResolveValue.value.eventResolveTimeLevels?.length &&
    changedResolveValue.value.eventResolveTimeLevels?.forEach((v) => {
      changedResolveValue.value.resTime.push(v?.resolves?.map((t) => `${t.day > 0 ? t.day + "day" : ""}${t.hour > 0 ? t.hour + "h" : ""}${t.minute > 0 ? t.minute + "min" : ""}`));
      changedResolveValue.value.resurgencyType.push(v?.resolves?.map((s) => s.urgencyType));
      changedResolveValue.value.resName.push(v?.resolves?.map((s) => s.name));
      changedResolveValue.value.resDescription.push(v?.resolves?.map((s) => s.description));
      changedResolveValue.value.resDefinition.push(v?.resolves?.map((s) => s.definition));
    });
  // 默认等级
  changedResolveValue.value.eventResolveDefaultResolves?.length &&
    changedResolveValue.value.eventResolveDefaultResolves?.forEach((v) => {
      changedResolveValue.value.defTime.push(`${v.day ? v.day + "day" : ""}${v.hour ? v.hour + "h" : ""}${v.minute ? v.minute + "min" : ""}`);
    });
  changedResolveValue.value.defurgencyType = changedResolveValue.value.eventResolveDefaultResolves?.map((s) => s.urgencyType);
  changedResolveValue.value.defName = changedResolveValue.value.eventResolveDefaultResolves?.map((s) => s.name);
  changedResolveValue.value.defDescription = changedResolveValue.value.eventResolveDefaultResolves?.map((s) => s.description);
  changedResolveValue.value.defDefinition = changedResolveValue.value.eventResolveDefaultResolves?.map((s) => s.definition);
  // 重新赋值
  changedResolveValue.value.time = [...(changedResolveValue.value?.resTime || []), ...(changedResolveValue.value?.defTime || [])].join(",");
  changedResolveValue.value.urgencyType = [...(changedResolveValue.value?.resurgencyType || []), ...(changedResolveValue.value?.defurgencyType || [])].join(",");
  changedResolveValue.value.name = [...(changedResolveValue.value?.resName || []), ...(changedResolveValue.value?.defName || [])].join(",");
  changedResolveValue.value.description = [...(changedResolveValue.value?.resDescription || []), ...(changedResolveValue.value?.defDescription || [])].join(",");
  changedResolveValue.value.definition = [...(changedResolveValue.value?.resDefinition || []), ...(changedResolveValue.value?.defDefinition || [])].join(",");
  // resolveFormItems.value = resolveFormOption.filter((v) => changedResolveValue.value[v.key]);

  resolveFormItems.value = resolveFormOption.filter((v) => {
    if (changedResolveValue.value[v.key].split(",").sort().join("") == originalResolveValue.value[v.key].split(",").sort().join("")) return false;
    else return true;
  });
  /**
   * 3.事件处理时间等级
   * @originalRespValue 改变前事件处理时间等级
   * @changedRespValue 改变后事件处理时间等级
   */
  originalRespValue.value = new Function("return" + props.data.originalValue)() || {};
  originalRespValue.value.priority = [...(originalRespValue.value.eventRespTimeLevels?.map((v: any) => v.priority) || []), ...(originalRespValue.value.eventRespDefaultResolves?.map((v: any) => "Default") || [])].join(",");
  // 容忍时间
  originalRespValue.value.defTime = [];
  originalRespValue.value.resTime = [];
  // SLA状态
  originalRespValue.value.defurgencyType = [];
  originalRespValue.value.resurgencyType = [];
  // 名称
  originalRespValue.value.defName = [];
  originalRespValue.value.resName = [];
  // 描述
  originalRespValue.value.defDescription = [];
  originalRespValue.value.resDescription = [];
  // 定义
  originalRespValue.value.defDefinition = [];
  originalRespValue.value.resDefinition = [];
  // p1-p8等级
  originalRespValue.value.eventRespTimeLevels?.length &&
    originalRespValue.value.eventRespTimeLevels?.forEach((v) => {
      originalRespValue.value.resTime.push(v?.resolves?.map((t) => `${t.day > 0 ? t.day + "day" : ""}${t.hour > 0 ? t.hour + "h" : ""}${t.minute > 0 ? t.minute + "min" : ""}`));
      originalRespValue.value.resurgencyType.push(v?.resolves?.map((s) => s.urgencyType));
      originalRespValue.value.resName.push(v?.resolves?.map((s) => s.name));
      originalRespValue.value.resDescription.push(v?.resolves?.map((s) => s.description));
      originalRespValue.value.resDefinition.push(v?.resolves?.map((s) => s.definition));
    });
  // 默认等级
  originalRespValue.value.eventRespDefaultResolves?.length &&
    originalRespValue.value.eventRespDefaultResolves?.forEach((v) => {
      originalRespValue.value.defTime.push(`${v.day ? v.day + "day" : ""}${v.hour ? v.hour + "h" : ""}${v.minute ? v.minute + "min" : ""}`);
    });
  originalRespValue.value.defurgencyType = originalRespValue.value.eventRespDefaultResolves?.map((s) => s.urgencyType);
  originalRespValue.value.defName = originalRespValue.value.eventRespDefaultResolves?.map((s) => s.name);
  originalRespValue.value.defDescription = originalRespValue.value.eventRespDefaultResolves?.map((s) => s.description);
  originalRespValue.value.defDefinition = originalRespValue.value.eventRespDefaultResolves?.map((s) => s.definition);
  // 重新赋值
  originalRespValue.value.time = [...(originalRespValue.value?.resTime || []), ...(originalRespValue.value?.defTime || [])].join(",");
  originalRespValue.value.urgencyType = [...(originalRespValue.value?.resurgencyType || []), ...(originalRespValue.value?.defurgencyType || [])].join(",");
  originalRespValue.value.name = [...(originalRespValue.value?.resName || []), ...(originalRespValue.value?.defName || [])].join(",");
  originalRespValue.value.description = [...(originalRespValue.value?.resDescription || []), ...(originalRespValue.value?.defDescription || [])].join(",");
  originalRespValue.value.definition = [...(originalRespValue.value?.resDefinition || []), ...(originalRespValue.value?.defDefinition || [])].join(",");

  changedRespValue.value = new Function("return" + props.data.changedValue)() || {};
  changedRespValue.value.priority = [...(changedRespValue.value.eventRespTimeLevels?.map((v: any) => v.priority) || []), ...(changedRespValue.value.eventRespDefaultResolves?.map((v: any) => "Default") || [])].join(",");
  // 容忍时间
  changedRespValue.value.defTime = [];
  changedRespValue.value.resTime = [];
  // SLA状态
  changedRespValue.value.defurgencyType = [];
  changedRespValue.value.resurgencyType = [];
  // 名称
  changedRespValue.value.defName = [];
  changedRespValue.value.resName = [];
  // 描述
  changedRespValue.value.defDescription = [];
  changedRespValue.value.resDescription = [];
  // 定义
  changedRespValue.value.defDefinition = [];
  changedRespValue.value.resDefinition = [];
  // p1-p8等级
  changedRespValue.value.eventRespTimeLevels?.length &&
    changedRespValue.value.eventRespTimeLevels?.forEach((v) => {
      changedRespValue.value.resTime.push(v?.resolves?.map((t) => `${t.day > 0 ? t.day + "day" : ""}${t.hour > 0 ? t.hour + "h" : ""}${t.minute > 0 ? t.minute + "min" : ""}`));
      changedRespValue.value.resurgencyType.push(v?.resolves?.map((s) => s.urgencyType));
      changedRespValue.value.resName.push(v?.resolves?.map((s) => s.name));
      changedRespValue.value.resDescription.push(v?.resolves?.map((s) => s.description));
      changedRespValue.value.resDefinition.push(v?.resolves?.map((s) => s.definition));
    });
  // 默认等级
  changedRespValue.value.eventRespDefaultResolves?.length &&
    changedRespValue.value.eventRespDefaultResolves?.forEach((v) => {
      // let time = [];
      changedRespValue.value.defTime.push(`${v.day ? v.day + "day" : ""}${v.hour ? v.hour + "h" : ""}${v.minute ? v.minute + "min" : ""}`);
      // changedRespValue.value.defTime = time.length ? time : [];
    });
  changedRespValue.value.defurgencyType = changedRespValue.value.eventRespDefaultResolves?.map((s) => s.urgencyType);
  changedRespValue.value.defName = changedRespValue.value.eventRespDefaultResolves?.map((s) => s.name);
  changedRespValue.value.defDescription = changedRespValue.value.eventRespDefaultResolves?.map((s) => s.description);
  changedRespValue.value.defDefinition = changedRespValue.value.eventRespDefaultResolves?.map((s) => s.definition);
  // 重新赋值
  changedRespValue.value.time = [...(changedRespValue.value?.resTime || []), ...(changedRespValue.value?.defTime || [])].join(",");
  changedRespValue.value.urgencyType = [...(changedRespValue.value?.resurgencyType || []), ...(changedRespValue.value?.defurgencyType || [])].join(",");
  changedRespValue.value.name = [...(changedRespValue.value?.resName || []), ...(changedRespValue.value?.defName || [])].join(",");
  changedRespValue.value.description = [...(changedRespValue.value?.resDescription || []), ...(changedRespValue.value?.defDescription || [])].join(",");
  changedRespValue.value.definition = [...(changedRespValue.value?.resDefinition || []), ...(changedRespValue.value?.defDefinition || [])].join(",");

  // respFormItems.value = respFormOption.filter((v) => changedRespValue.value[v.key]);

  respFormItems.value = respFormOption.filter((v) => {
    if (changedRespValue.value[v.key].split(",").sort().join("") == originalRespValue.value[v.key].split(",").sort().join("")) return false;
    else return true;
  });
}

onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
.icon-right {
  display: flex;
  align-items: center;
  margin-right: 5px;
  margin-bottom: 5px;
  // flex-wrap: nowrap;
}
</style>
