<template>
  <div>
    <div style="font-weight: 600; color: #000">{{ operationType }}</div>

    <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <template v-if="item.type === 'relation'">
        <div>
          <div v-if="operationType == '分配'">
            <div class="changedValue" v-for="v in changedValue[item.key]" :key="v">
              {{ v }}
            </div>
          </div>
          <div v-if="operationType == '移除'">
            <div class="originalValue" v-for="v in changedValue[item.key]" :key="v">
              {{ v }}
            </div>
          </div>
          <div>{{ props.data.resourceTenantName }}</div>
        </div>
      </template>
    </el-form-item>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({} as LoggerItem),
});
import { operationLogger, contactsType } from "@/api/loggerType";

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };
// 告警分类字段待增加
const formOption: CurrentLogFormItems[] = [
  // { label: "手机号", key: "phone", type: "text" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<any>({});

const changedValue = ref<any>({});
const WORKDAY = ref<any>({});
const SATURDAY = ref<any>({});
const SUNDAY = ref<any>({});
const weekList = ref<any>([]);

let isShow = ref<any>((new Function("return" + props.data.changedValue)() || {})?.effectTimeCfg ? true : false);
const operationLoggerItem = ref<any>({});
const operationType = ref<string>("");
function handleLoggerInfo() {
  operationLogger.forEach((v, i) => {
    if (v.auditCode == props.data.auditCode) {
      operationType.value = v.type;
      operationLoggerItem.value = v;
    }
  });
  // console.log(operationType);
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  changedValue.value = new Function("return" + props.data.changedValue)() || {};

  formOption.push({ label: operationLoggerItem.value.name.split("分配")[1], key: "names", type: "relation" });

  currentLogFormItems.value = formOption.filter((v) => {
    return true;
  });

  // console.log(currentLogFormItems.value);
}
// setup(() => {
//   isShow = ;
// });
onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
:deep.el-form-item {
  margin: 0;
}
</style>
