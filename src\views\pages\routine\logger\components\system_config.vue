<template>
  <el-form :model="{}" label-position="left" v-if="props.data.success">
    <div style="font-weight: 600; color: #000">更新</div>
    <template v-if="props.data.name?.includes('启用') || props.data.name?.includes('禁用')">
      <el-form-item :label="changedValue.status ? '启用' : '禁用'">
        <div>
          <p class="tw-font-semibold" :class="!changedValue.status ? 'tw-text-slate-500' : 'changedValue'">{{ changedValue.codeName }} <FontAwesomeIcon v-if="!changedValue.status" class="tw-mx-[5px]" :icon="faBan"></FontAwesomeIcon></p>
          <p>{{ props.data.resourceTenantName }}</p>
        </div>
      </el-form-item>
    </template>
    <template v-else>
      <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
        <template v-if="item.type === 'text'">
          <div>
            <div class="changedValue">"{{ changedValue[item.key] }}"</div>
            <div class="originalValue">"{{ originalValue[item.key] }}"</div>
          </div>
        </template>
      </el-form-item>
    </template>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { faBan } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import { genderOption } from "@/api/personnel";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string; isBoolean?: boolean };

const formOption: CurrentLogFormItems[] = [
  { label: "默认处理对象", key: "name", type: "text" },
  { label: "是否自动关闭", key: "autoClose", type: "text" },
  { label: "自动关闭时限", key: "autoCloseTimeLimit", type: "text" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<Record<string, any>>({});

const changedValue = ref<Record<string, any>>({});

function handleLoggerInfo() {
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  changedValue.value = typeof new Function("return" + props.data.changedValue)() === "string" ? JSON.parse(new Function("return" + props.data.changedValue?.replace(RegExp("/", "g"), ""))() || {}) : new Function("return" + props.data.changedValue)();
  // console.log(changedValue.value, originalValue.value);
  changedValue.value.name = "";
  changedValue.value.name = changedValue.value?.tenantSystemConfig?.groupName || "";
  changedValue.value.autoClose = changedValue.value?.eventCloseTenantConfig?.autoClose ? "是" : "否" || "";
  changedValue.value.autoCloseTimeLimit = convertMinutesToDaysHoursAndMinutes(changedValue.value.eventCloseTenantConfig?.autoCloseTimeLimit) || "";
  originalValue.value.name = "";
  originalValue.value.name = originalValue.value?.tenantSystemConfig?.groupName || "";

  originalValue.value.autoClose = originalValue.value?.eventCloseTenantConfig?.autoClose ? "是" : "否" || "";

  originalValue.value.autoCloseTimeLimit = convertMinutesToDaysHoursAndMinutes(originalValue.value.eventCloseTenantConfig?.autoCloseTimeLimit) || "";

  currentLogFormItems.value = formOption.filter((v) => {
    if (!originalValue.value[v.key] && !changedValue.value[v.key]) return false;

    if (originalValue.value[v.key] == changedValue.value[v.key]) return false;
    else return true;
  }); //.filter((v) => originalValue.value[v.key] !== changedValue.value[v.key]);
}
onMounted(() => {
  handleLoggerInfo();
});

function convertMinutesToDaysHoursAndMinutes(minutes: number) {
  if (minutes === undefined) return "";
  var days = Math.floor(minutes / (24 * 60)); // 获取天数部分
  minutes %= 1440; // 去除已经计算过的天数后的余数（每天有1440分钟）

  var hours = Math.floor(minutes / 60); // 获取小时部分
  minutes %= 60; // 去除已经计算过的小时后的余数

  return `${days > 0 ? days + "天" : ""}${hours > 0 ? hours + "时" : ""}${minutes + "分"}`;
}
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
</style>
