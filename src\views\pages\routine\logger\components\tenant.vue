<template>
  <el-form :model="{}" label-position="left" v-if="props.data.success">
    <div style="font-weight: 600; color: #000">{{ operationType }}</div>
    <div v-if="operationType != '解锁' && operationType != '锁定'">
      <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
        <template v-if="item.type === 'text'">
          <div v-if="item.source">
            <template v-if="Object.keys(booleans).includes(changedValue[item.source][item.key])">
              <!-- 处理 true | false -->
              <div class="changedValue">"{{ booleans[changedValue[item.source][item.key]] }}"</div>
              <div class="originalValue" v-if="![AuditCode['iam.tenant.create']].includes(auditCode as AuditCode)">"{{ booleans[originalValue[item.source][item.key]] }}"</div>
            </template>
            <template v-else-if="item.isBoolean">
              <!-- 处理 true | false -->
              <div class="changedValue">"{{ JSON.parse(changedValue[item.source][item.key]).join() }}"</div>
              <div class="originalValue" v-if="![AuditCode['iam.tenant.create']].includes(auditCode as AuditCode)">"{{ JSON.parse(originalValue[item.source][item.key]).join() }}"</div>
            </template>
            <template v-else>
              <div class="changedValue">"{{ changedValue[item.source][item.key] }}"</div>
              <div class="originalValue" v-if="![AuditCode['iam.tenant.create']].includes(auditCode as AuditCode)">"{{ originalValue[item.source][item.key] }}"</div>
            </template>
          </div>
          <div v-else>
            <template v-if="item.isBoolean">
              <!-- 处理 true | false -->
              <div class="changedValue">"{{ booleans[changedValue[item.key] + ""] }}"</div>
              <div class="originalValue" v-if="![AuditCode['iam.tenant.create']].includes(auditCode as AuditCode)">"{{ booleans[originalValue[item.key] + ""] }}"</div>
            </template>
            <template v-else-if="[/*'vendorIds', 'modelNumbers', 'serialNumbers', */ 'assetNumbers' /* 'typeIdsp', 'grouIds' */].includes(item.key)">
              <!-- 处理 true | false -->
              <div class="changedValue">"{{ JSON.parse(changedValue[item.key]).join() }}"</div>
              <div class="originalValue" v-if="![AuditCode['iam.tenant.create']].includes(auditCode as AuditCode)">"{{ JSON.parse(originalValue[item.key]).join() }}"</div>
            </template>
            <template v-else>
              <div class="changedValue">"{{ changedValue[item.key] }}"</div>
              <div class="originalValue" v-if="![AuditCode['iam.tenant.create']].includes(auditCode as AuditCode)">"{{ originalValue[item.key] }}"</div>
            </template>
          </div>
        </template>
        <template v-else-if="item.type === 'tags'">
          <div>
            <div>
              <el-tag class="tw-mr-2" type="success" v-for="tag in changedValue[item.key]" :key="`${props.data.resourceType}.${item.key}-${tag}`">{{ tag }}</el-tag>
            </div>
            <div>
              <el-tag class="tw-mr-2" type="danger" v-for="tag in originalValue[item.key]" :key="`${props.data.resourceType}.${item.key}-${tag}`">{{ tag }}</el-tag>
            </div>
          </div>
        </template>

        <template v-else-if="item.type === 'tag'">
          <div>
            <div>
              <el-tag class="tw-mr-2" type="success">{{ changedValue[item.key] }}</el-tag>
            </div>
            <div>
              <el-tag class="tw-mr-2" type="danger">{{ originalValue[item.key] }}</el-tag>
            </div>
          </div>
        </template>
      </el-form-item>
    </div>
    <el-form-item label="客户" v-if="operationType == '解锁' || operationType == '锁定'">
      <div>
        <div v-show="!originalValue.blocked" class="changedValue">
          {{ originalValue.name }}
        </div>
        <div v-show="originalValue.blocked">{{ originalValue.name }} <FontAwesomeIcon class="tw-mx-[5px]" :icon="faBan"></FontAwesomeIcon></div>
        <div>{{ props.data.resourceTenantName }}</div>
      </div>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeMount } from "vue";
import { LoggerItem } from "@/api/system";

import { Clientsigningplace, industry, localesOption, OTCR } from "@/api/locale";
import { getSystemVersion } from "@/api/system";
import { faBan } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });
import { operationLogger, contactsType } from "@/api/loggerType";

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string; isBoolean?: boolean };

const formOption: CurrentLogFormItems[] = [
  { label: "客户名称", key: "name", type: "text" },
  { label: "客户名称缩写", key: "abbreviation", type: "text" },
  { label: "是否激活", isBoolean: true, key: "activated", type: "text" },
  { label: "地址", key: "address", type: "text" },
  { label: "电话", key: "tenantPhone", type: "text" },
  { label: "邮编", key: "tenantPostcode", type: "text" },
  { label: "电子邮件", key: "tenantEmail", type: "text" },
  { label: "客户签约地", key: "tenantSigningPlaceLabel", type: "text" },
  { label: "客户签约地2", key: "tenantSigningPlace2", type: "text" },
  { label: "服务语种", key: "languageLabel", type: "text" },
  { label: "行业", key: "tenantIndustryLabel", type: "text" },
  { label: "性质", key: "tenantNature", type: "text" },
  { label: "客户渠道", key: "tenantChannelLabel", type: "text" },
  { label: "是否运营商", isBoolean: true, key: "tenantOperator", type: "text" },
  { label: "是否国外客户", isBoolean: true, key: "tenantForeign", type: "text" },
  { label: "描述", key: "tenantDescription", type: "text" },
  { label: "平台版本", key: "version", type: "text" },
  { label: "传真", key: "tenantFax", type: "text" },
  { label: "客户类型", key: "tenantType", type: "text" },
  { label: "描述", key: "note", type: "text" },
  { label: "平台版本", key: "systemEdition", type: "text" },
  { label: "双因素认证", key: "mfaState", type: "text" },
  { label: "同步NetCare", isBoolean: true, key: "synetcare", type: "text" },

  // { label: "选择用户组", isBoolean: true, key: "tenantForeign", type: "text" },
  // { label: "是否加入该客户", isBoolean: true, key: "tenantForeign", type: "text" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<Record<string, any>>({});

const changedValue = ref<Record<string, any>>({});
const systemEditionOption = ref<Record<"name" | "code", string>[]>([]);
const operationType = ref<string>("");
const auditCode = ref<string>("");
enum AuditCode {
  "iam.tenant.create" = "iam.tenant.create",
}

function handleLoggerInfo() {
  operationLogger.forEach((v, i) => {
    if (v.auditCode == props.data.auditCode) {
      operationType.value = v.type;
      auditCode.value = v.auditCode;
    }
  });
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  changedValue.value = new Function("return" + props.data.changedValue)() || {};

  originalValue.value.tenantSigningPlaceLabel = Clientsigningplace.find((v) => v.value === originalValue.value.tenantSigningPlace)?.label || "";
  changedValue.value.tenantSigningPlaceLabel = Clientsigningplace.find((v) => v.value === changedValue.value.tenantSigningPlace)?.label || "";

  originalValue.value.tenantIndustryLabel = industry.find((v) => v.value === originalValue.value.tenantIndustry)?.label || "";
  changedValue.value.tenantIndustryLabel = industry.find((v) => v.value === changedValue.value.tenantIndustry)?.label || "";

  originalValue.value.languageLabel = localesOption.find((v) => v.value === originalValue.value.language)?.label || "";
  changedValue.value.languageLabel = localesOption.find((v) => v.value === changedValue.value.language)?.label || "";

  originalValue.value.tenantChannelLabel = OTCR.find((v) => v.value === originalValue.value.tenantChannel)?.label || "";
  changedValue.value.tenantChannelLabel = OTCR.find((v) => v.value === changedValue.value.tenantChannel)?.label || "";
  changedValue.value.mfaState = changedValue.value.mfaState == "DEFAULT" ? (changedValue.value.enableMfaDefault ? "开" : "关") : changedValue.value.mfaState == "ENABLED" ? "开" : "关";
  originalValue.value.mfaState = originalValue.value.mfaState == "DEFAULT" ? (originalValue.value.enableMfaDefault ? "开" : "关") : originalValue.value.mfaState == "ENABLED" ? "开" : "关";

  // currentLogFormItems.value = formOption.filter((v) => originalValue.value[v.key] !== changedValue.value[v.key]);
  // console.log(systemEditionOption.value);
  getSystemVersion({}).then((res) => {
    systemEditionOption.value = res.data instanceof Array ? res.data : [];
    systemEditionOption.value.forEach((v, i) => {
      originalValue.value.systemEdition = v.code == originalValue.value.systemEdition ? v.name : originalValue.value.systemEdition;
      changedValue.value.systemEdition = v.code == changedValue.value.systemEdition ? v.name : changedValue.value.systemEdition;
    });
  });

  currentLogFormItems.value = formOption.filter((v) => {
    if (!originalValue.value[v.key] && !changedValue.value[v.key]) return false;
    if (originalValue.value[v.key] == changedValue.value[v.key]) return false;
    else return true;
  });
}
// created() {
//
// }
onBeforeMount(() => {
  // getSystemVersion({}).then((res) => {
  //   systemEditionOption.value = res.data instanceof Array ? res.data : [];
  // });
});
onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
</style>
