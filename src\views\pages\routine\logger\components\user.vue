<template>
  <el-form :model="{}" label-position="left">
    <div style="font-weight: 600; color: #000">{{ operationType }}</div>

    <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <template v-if="item.type === 'text' && operationType !== '冻结' && operationType !== '解冻'">
        <div>
          <div class="changedValue" v-if="changedValue[item.key]">"{{ changedValue[item.key] }}"</div>
          <div class="originalValue" v-if="operationType != '新增'">"{{ originalValue[item.key] }}"</div>
        </div>
      </template>
      <template v-if="item.type === 'tag'">
        <div>
          <div v-if="operationType === '冻结'">{{ changedValue["name"] }} <FontAwesomeIcon class="tw-mx-[5px]" :icon="faBan"></FontAwesomeIcon></div>
          <div v-else class="changedValue">
            {{ changedValue["name"] }}
          </div>
          <div>{{ props.data.resourceTenantName }}</div>
        </div>
      </template>
      <template v-if="item.type === 'teext'">
        <div>
          <!-- <el-option value="ENABLED" label="开">开</el-option>
              <el-option value="DISABLED" label="关">关</el-option>
              <el-option value="DEFAULT" :label="`默认(${row.enableMfaDefault ? '开' : '关'})`">{{ `默认(${row.enableMfaDefault ? "开" : "关"})` }}</el-option> -->
          <div class="changedValue" v-if="changedValue[item.key]">"{{ changedValue[item.key] }}"</div>
          <div class="originalValue" v-if="operationType != '新增'">"{{ originalValue[item.key] }}"</div>
        </div>
      </template>
      <template v-if="item.type === 'password' && props.data.auditCode == 'iam.user.reset_pwd'">
        <div>
          <!-- <el-option value="ENABLED" label="开">开</el-option>
              <el-option value="DISABLED" label="关">关</el-option>
              <el-option value="DEFAULT" :label="`默认(${row.enableMfaDefault ? '开' : '关'})`">{{ `默认(${row.enableMfaDefault ? "开" : "关"})` }}</el-option> -->
          <div class="changedValue" v-if="changedValue[item.key]">"{{ changedValue[item.key] }}"</div>
          <div class="originalValue" v-if="operationType != '新增'">"{{ originalValue[item.key] }}"</div>
        </div>
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";
import { faBan } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { locales, localesOption } from "@/api/locale";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };
import { operationLogger, contactsType } from "@/api/loggerType";

// 告警分类字段待增加
const formOption: CurrentLogFormItems[] = [
  { label: "用户名称", key: "name", type: "text" },
  { label: "账号", key: "account", type: "text" },
  { label: "手机号", key: "phone", type: "text" },
  { label: "邮箱", key: "email", type: "text" },
  { label: "用户", key: "blocked", type: "tag" },
  { label: "双因素认证", key: "mfaState", type: "teext" },
  { label: "登录密码", key: "password", type: "password" },
  { label: "时区", key: "zoneId", type: "text" },
  { label: "语言", key: "language", type: "text" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<any>({});

const changedValue = ref<any>({});
const operationType = ref<string>("");
function handleLoggerInfo() {
  operationLogger.forEach((v, i) => {
    if (v.auditCode == props.data.auditCode) {
      operationType.value = v.type;
    }
  });
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  originalValue.value.account = originalValue.value.account ? originalValue.value.account + "@ideal" : "";
  originalValue.value.name = originalValue.value.name ? desensitationName(originalValue.value.name) : "";
  originalValue.value.mfaState = originalValue.value.mfaState == "ENABLED" ? "开" : originalValue.value.mfaState == "DISABLED" ? "关" : `默认${originalValue.value.enableMfaDefault ? "开" : "关"}`;
  changedValue.value = new Function("return" + props.data.changedValue)() || {};
  changedValue.value.account = changedValue.value.account ? changedValue.value.account + "@ideal" : "";
  changedValue.value.name = changedValue.value.name ? desensitationName(changedValue.value.name) : "";
  changedValue.value.mfaState = changedValue.value.mfaState == "ENABLED" ? "开" : changedValue.value.mfaState == "DISABLED" ? "关" : `默认${changedValue.value.enableMfaDefault ? "开" : "关"}`;
  originalValue.value.zoneId = originalValue.value.zoneId ? Zone[originalValue.value.zoneId] : originalValue.value?.zoneId;

  changedValue.value.zoneId = changedValue.value.zoneId ? Zone[changedValue.value.zoneId] : changedValue.value?.zoneId;
  // console.log(localesOption);
  localesOption.forEach((v, i) => {
    originalValue.value.language = v.value === originalValue.value.language ? v.label : originalValue.value.language;
    changedValue.value.language = v.value === changedValue.value.language ? v.label : changedValue.value.language;
  });

  if (props.data.auditCode == "iam.user.reset_pwd") {
    changedValue.value.password = "*********";
    originalValue.value.password = "**********";
  }

  // currentLogFormItems.value = formOption.filter((v) => changedValue.value[v.key]);
  if (operationType.value != "解冻") {
    currentLogFormItems.value = formOption.filter((v) => {
      if (!originalValue.value[v.key] || !changedValue.value[v.key]) return false;

      if (originalValue.value[v.key] == changedValue.value[v.key]) return false;
      else return true;
    });
  } else {
    formOption.shift();
    currentLogFormItems.value = formOption.filter((v) => {
      if (!originalValue.value[v.key] && !changedValue.value[v.key]) return false;

      if (originalValue.value[v.key] == changedValue.value[v.key]) return false;
      else return true;
    });
  }
}
function desensitationName(string: string) {
  /**
   * 名称脱敏处理
   * 脱敏规则：两个字的，脱第二个字，比如王*；三个字，保留第一个字和最后一个字，比如李*芳；四个字及以上，保留前两个字和最后一个字，比如上官**野
   */
  // if (string.length >= 4) {
  //   return string.slice(0, 2).padEnd(string.length - 1, "*") + string[string.length - 1];
  // }
  // if (string.length >= 3) {
  //   return string.slice(0, 1).padEnd(string.length - 1, "*") + string[string.length - 1];
  // }
  // if (string.length >= 2) {
  //   return string.slice(0, 1).padEnd(string.length, "*");
  // }
  return string;
}

onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
</style>
