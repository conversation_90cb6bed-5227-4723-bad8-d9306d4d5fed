<template>
  <el-form :model="{}" label-position="left" v-if="props.data.success">
    <div style="font-weight: 600; color: #000">更新</div>
    <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <template v-if="item.type === 'text'">
        <div v-if="item.source">
          <template v-if="Object.keys(booleans).includes(changedValue[item.source][item.key])">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ booleans[changedValue[item.source][item.key]] }}"</div>
            <div class="originalValue">"{{ booleans[originalValue[item.source][item.key]] }}"</div>
          </template>
          <template v-else-if="item.isBoolean">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ JSON.parse(changedValue[item.source][item.key]).join() }}"</div>
            <div class="originalValue">"{{ JSON.parse(originalValue[item.source][item.key]).join() }}"</div>
          </template>
          <template v-else>
            <div class="changedValue">"{{ changedValue[item.source][item.key] }}"</div>
            <div class="originalValue">"{{ originalValue[item.source][item.key] }}"</div>
          </template>
        </div>
        <div v-else>
          <template v-if="item.isBoolean">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ booleans[changedValue[item.key] + ""] }}"</div>
            <div class="originalValue">"{{ booleans[originalValue[item.key] + ""] }}"</div>
          </template>
          <template v-else-if="[/*'vendorIds', 'modelNumbers', 'serialNumbers', */ 'assetNumbers' /* 'typeIdsp', 'grouIds' */].includes(item.key)">
            <!-- 处理 true | false -->
            <div class="changedValue">"{{ JSON.parse(changedValue[item.key]).join() }}"</div>
            <div class="originalValue">"{{ JSON.parse(originalValue[item.key]).join() }}"</div>
          </template>
          <template v-else>
            <div class="changedValue">"{{ changedValue[item.key] }}"</div>
            <div class="originalValue">"{{ originalValue[item.key] }}"</div>
          </template>
        </div>
      </template>
      <template v-else-if="item.type === 'tags'">
        <div>
          <div>
            <el-tag class="tw-mr-2" type="success" v-for="tag in changedValue[item.key]" :key="`${props.data.resourceType}.${item.key}-${tag}`">{{ tag }}</el-tag>
          </div>
          <div>
            <el-tag class="tw-mr-2" type="danger" v-for="tag in originalValue[item.key]" :key="`${props.data.resourceType}.${item.key}-${tag}`">{{ tag }}</el-tag>
          </div>
        </div>
      </template>

      <template v-else-if="item.type === 'tag'">
        <div>
          <div>
            <el-tag class="tw-mr-2" type="success">{{ changedValue[item.key] }}</el-tag>
          </div>
          <div>
            <el-tag class="tw-mr-2" type="danger">{{ originalValue[item.key] }}</el-tag>
          </div>
        </div>
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";

import { genderOption } from "@/api/personnel";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string; isBoolean?: boolean };

const formOption: CurrentLogFormItems[] = [
  { label: "姓名", key: "name", type: "text" },
  { label: "账号", key: "account", type: "text" },
  { label: "移动电话", key: "phone", type: "text" },
  { label: "邮箱", key: "email", type: "text" },
  { label: "性别", key: "genderLabel", type: "text" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<Record<string, any>>({});

const changedValue = ref<Record<string, any>>({});

function handleLoggerInfo() {
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  changedValue.value = new Function("return" + props.data.changedValue)() || {};

  originalValue.value.genderLabel = genderOption.find((v) => v.value === originalValue.value.gender)?.label || "";
  changedValue.value.genderLabel = genderOption.find((v) => v.value === changedValue.value.gender)?.label || "";

  currentLogFormItems.value = formOption.filter((v) => originalValue.value[v.key] !== changedValue.value[v.key]);
}
onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
</style>
