<template>
  <el-form :model="{}" label-position="left">
    <div style="font-weight: 600; color: #000">{{ operationType }}</div>

    <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <template v-if="item.type === 'text'">
        <div>
          <!-- <template v-if="['report'].includes(item.key)">

            <div :style="{ marginLeft: ['report'].includes(item.key) ? '50px' : '0' }">
              <div class="changedValue">"{{ booleans[changedValue[item.key] + ""] }}"</div>
              <div class="originalValue">"{{ booleans[originalValue[item.key] + ""] }}"</div>
            </div>
          </template>
          <template v-else-if="[/*'vendorIds', 'modelNumbers', 'serialNumbers', */ 'assetNumbers' /* 'typeIdsp', 'grouIds' */].includes(item.key)">

            <div class="changedValue">"{{ JSON.parse(changedValue[item.key]).join() }}"</div>
            <div class="originalValue">"{{ JSON.parse(originalValue[item.key]).join() }}"</div>
          </template> -->
          <!-- <template> -->
          <div class="changedValue">"{{ changedValue[item.key] }}"</div>
          <div class="originalValue" v-if="operationType != '新增'">"{{ originalValue[item.key] }}"</div>
          <!-- </template> -->
        </div>
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });
import { operationLogger, contactsType } from "@/api/loggerType";

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };
// 告警分类字段待增加
const formOption: CurrentLogFormItems[] = [
  { label: "用户组名称", key: "name", type: "text" },
  { label: "描述", key: "note", type: "text" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<any>({});

const changedValue = ref<any>({});

const operationType = ref<string>("");
function handleLoggerInfo() {
  operationLogger.forEach((v, i) => {
    if (v.auditCode == props.data.auditCode) {
      operationType.value = v.type;
    }
  });
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  changedValue.value = new Function("return" + props.data.changedValue)() || {};

  currentLogFormItems.value = formOption.filter((v) => {
    if (!originalValue.value[v.key] && !changedValue.value[v.key]) return false;

    if (originalValue.value[v.key] == changedValue.value[v.key]) return false;
    else return true;
  });
}

onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
</style>
