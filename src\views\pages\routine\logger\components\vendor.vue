<template>
  <el-form :model="{}" label-position="left">
    <div style="font-weight: 600; color: #000">{{ operationType }}</div>
    {{ changedValue.vendorTypeName || originalValue.vendorTypeName }}
    <el-form-item :label="item.label" v-for="item in currentLogFormItems" :key="`${props.data.resourceType}.${item.key}`">
      <template v-if="item.type === 'text'">
        <div>
          <div class="changedValue" v-if="operationType != '删除' && operationType != '移除'">"{{ changedValue[item.key] }}"</div>
          <div class="originalValue" v-if="operationType != '新增'">"{{ originalValue[item.key] }}"</div>
        </div>
      </template>
      <template v-if="item.type === 'deviceText'">
        <div>
          <div class="changedValue" v-if="operationType != '删除' && operationType != '移除'">{{ changedValue[item.key] }}</div>
          <div class="originalValue" v-if="operationType != '新增'">{{ originalValue[item.key] }}</div>
        </div>
      </template>
      <template v-if="item.type === 'relation'">
        <div>
          <div v-if="operationType == '分配'">
            <div class="changedValue" v-for="v in changedValue[item.key]" :key="v">
              {{ v }}
            </div>
          </div>
          <div v-if="operationType == '移除'">
            <div class="originalValue" v-for="v in changedValue[item.key]" :key="v">
              {{ v }}
            </div>
          </div>
          <div>{{ props.data.resourceTenantName }}</div>
        </div>
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { LoggerItem } from "@/api/system";
import { Zone } from "@/utils/zone";
import { Language } from "@/utils/language";

interface Props {
  data: LoggerItem;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}) as LoggerItem,
});

const booleans = ref<{ [key: string]: string }>({ true: "是", false: "否" });
import { operationLogger, contactsType } from "@/api/loggerType";

type CurrentLogFormItems = { label: string; source?: string; key: string; type: string };
// 告警分类字段待增加
const formOption: CurrentLogFormItems[] = [
  { label: "名称", key: "name", type: "text" },
  { label: "描述", key: "description", type: "text" },
  { label: "固定电话", key: "landlinePhone", type: "text" },
  { label: "支撑电话", key: "supportPhone", type: "text" },
  { label: "联系人姓名", key: "contactName", type: "text" },
  { label: "邮箱", key: "email", type: "text" },
  { label: "设备", key: "names", type: "relation" },
  { label: "地址", key: "address", type: "text" },
  // { label: "供应商类型", key: "vendorTypeName", type: "text" },
];

const currentLogFormItems = ref<CurrentLogFormItems[]>();

const originalValue = ref<any>({});

const changedValue = ref<any>({});
const operationType = ref("");
function handleLoggerInfo() {
  operationLogger.forEach((v, i) => {
    if (v.auditCode == props.data.auditCode) {
      operationType.value = v.type;
    }
  });
  originalValue.value = new Function("return" + props.data.originalValue)() || {};
  originalValue.value.contactName = Object.keys(originalValue.value).length > 0 ? desensitationName(originalValue.value.contactName) : desensitationName("");
  changedValue.value = new Function("return" + props.data.changedValue)() || {};
  changedValue.value.contactName = Object.keys(changedValue.value).length > 0 ? desensitationName(changedValue.value.contactName) : desensitationName("");
  if (changedValue.value.length > 0) {
    changedValue.value.deviceName = changedValue.value.map((v: any) => v.name).join(",");
  }

  if (originalValue.value.length > 0) {
    originalValue.value.deviceName = originalValue.value.map((v: any) => v.name).join(",");
  }

  // originalValue.value.deviceName = originalValue.value.map((v: any) => v.name).join(",");
  currentLogFormItems.value = formOption.filter((v) => {
    if (!originalValue.value[v.key] && !changedValue.value[v.key]) return false;

    if (originalValue.value[v.key] == changedValue.value[v.key]) return false;
    else return true;
  });
  // console.log(props.data.resourceType, originalValue.value, changedValue.value, currentLogFormItems.value);
}
function desensitationName(string: string) {
  /**
   * 名称脱敏处理
   * 脱敏规则：两个字的，脱第二个字，比如王*；三个字，保留第一个字和最后一个字，比如李*芳；四个字及以上，保留前两个字和最后一个字，比如上官**野
   */
  if (string === null || string === undefined) return "";
  if (string.length >= 4) {
    return string.slice(0, 2).padEnd(string.length - 1, "*") + string[string.length - 1];
  }
  if (string.length >= 3) {
    return string.slice(0, 1).padEnd(string.length - 1, "*") + string[string.length - 1];
  }
  if (string.length >= 2) {
    return string.slice(0, 1).padEnd(string.length, "*");
  }
  return string;
}

onMounted(() => {
  handleLoggerInfo();
});
</script>

<style scoped lang="scss">
@import "@/styles/theme/common/var";
$changedValueColor: $color-success;
$originalValueColor: $color-danger;

.changedValue {
  color: $changedValueColor;
}
.originalValue {
  color: $originalValueColor;
}
</style>
