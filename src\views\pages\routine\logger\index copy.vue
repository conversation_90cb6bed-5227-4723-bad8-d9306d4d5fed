<template>
  <el-card>
    <el-scrollbar>
      <div class="flex-search" :style="{ minWidth: `${width - 42}px`, padding: '0px' }">
        <div class="left">
          <el-input v-model="state.search.keyword" placeholder="关键字搜索" clearable @change="handleStateRefresh"></el-input>
          <el-date-picker :model-value="[state.search.startTime, state.search.endTime]" @update:model-value="(v) => (v instanceof Array ? ((state.search.startTime = v[0]), (state.search.endTime = v[1])) : ((state.search.endTime = ''), (state.search.startTime = '')))" value-format="x" type="datetimerange" @change="handleStateRefresh" :disabled-date="(date: Date) => moment().isBefore(date)" />
        </div>
        <div class="center">
          <!--  -->
        </div>
        <div class="right">
          <el-button type="default" :icon="Refresh" @click="handleStateRefresh"></el-button>
        </div>
      </div>
    </el-scrollbar>
    <el-table v-loading="state.loading" border :data="state.data" :height="height - 64 - 60 - (state.total ? 32 : 0)" :style="{ width: `${width - 40}px`, margin: '0 auto' }">
      <!-- :row-style="({ row }) => (row.success ? { backgroundColor: 'var(--el-color-success-light-9)' } : { backgroundColor: 'var(--el-color-danger-light-9)' })" -->
      <!-- <el-table-column label="" :width="38">
        <template #default="{ row }">
          <el-tooltip :content="row.message" :disabled="!row.message" placement="top">
            <el-icon :size="14" class="tw-align-middle" :color="row.success ? 'var(--el-color-success)' : 'var(--el-color-error)'"><component :is="row.success ? CircleCheckFilled : CircleCloseFilled"></component></el-icon>
          </el-tooltip>
        </template>
      </el-table-column> -->
      <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :width="column.width" :formatter="column.formatter" />
    </el-table>
    <div :style="{ margin: '8px 20px 20px' }">
      <el-pagination v-show="state.total" v-model:current-page="state.page" v-model:page-size="state.size" :page-sizes="state.sizes" :total="state.total" layout="->, total, sizes, prev, pager, next, jumper" small @size-change="handleStateRefresh()" @current-change="handleStateRefresh()" />
    </div>
  </el-card>
</template>

<script setup lang="ts" name="personnel/business">
import { ref, reactive, computed, watch, inject, h } from "vue";
// import { useI18n } from "vue-i18n";
import { sizes } from "@/utils/common";
// import { formatterDate } from "@/utils/date";

import resource from "./components/resource.vue";
import contact from "./components/contact.vue";
import region from "./components/region.vue";
import vendor from "./components/vendor.vue";
import location from "./components/location.vue";
import service_number from "./components/service_number.vue";
import support_note from "./components/support_note.vue";
import device_group from "./components/device_group.vue";
import resource_type from "./components/resource_type.vue";
import alert_classification from "./components/alert_classification.vue";
import role from "./components/role.vue";
import role_auth from "./components/role_auth.vue";
import tenant from "./components/tenant.vue";
import user_center from "./components/user_center.vue";
import user from "./components/user.vue";
import user_group from "./components/user_group.vue";
import current_org from "./components/current_org.vue";
import monitor_source_mapping from "./components/monitor_source_mapping.vue";
import priority_matrix from "./components/priority_matrix.vue";
import global_config from "./components/global_config.vue";
import system_config from "./components/system_config.vue";
import sla from "./components/sla.vue";
import global_sla from "./components/global_sla.vue";
import global_support_note from "./components/global_support_note.vue";
import global_degrade from "./components/global_degrade.vue";
import tenant_pwd_strategy from "./components/password_strategy.vue";

import degrade from "./components/degrade.vue";
import auto_event_config from "./components/auto_event_config.vue";
import degrade_roletion from "./components/degradeRoletion.vue";
import support_roletion from "./components/supportRoletion.vue";
import secure_container from "./components/secure_container.vue";

// Ui
import { ElMessage, ElText, ElForm, ElFormItem } from "element-plus";
// eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
import { More, Refresh, Plus, Edit, Delete, CircleCloseFilled, CircleCheckFilled } from "@element-plus/icons-vue";

// Api
import { getLogger as getData, OperationType, operationTypeOption, resourceTypeOption } from "@/api/system";
import type { LoggerItem as DataItem } from "@/api/system";
import moment from "moment";

const publicParams = computed<Record<string, unknown>>(() => ({ schemas: ["USER", "TENANT"].join(",") }));

async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  let controller = new AbortController();
  const response = getData({
    ...publicParams.value,
    ...params,
    ...(typeof state.search.keyword === "string" && state.search.keyword ? { keyword: state.search.keyword } : {}),
    ...(state.search.endTime && state.search.startTime ? { startTime: state.search.startTime, endTime: state.search.endTime } : {}),
    paging: {
      pageNumber: state.page,
      pageSize: state.size,
    },
    controller,
    success: true,
  });
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await response;
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);
      return data instanceof Array ? data : [];
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(20);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}

/*********************************************************/

// const { t } = useI18n();

// interface Props {
//   width: number;
//   height: number;
// }
// const props = withDefaults(defineProps<Props>(), {});

const width = inject<import("vue").Ref<number>>("width", ref(100));
const height = inject<import("vue").Ref<number>>("height", ref(100));

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<DataItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {
    keyword: "",
    startTime: "",
    endTime: "",
  },
  column: [
    /* 列 */
    { key: "operationType", label: "操作类型", formatter: (_row, _col, value) => operationTypeOption.find((v) => v.value === ((_row.auditCode as string) || ""))?.type || "", width: 100 },
    {
      key: "resourceType",
      label: "配置项",
      formatter: (_row, _col, value) => {
        // role = "role",
        // user = "user",
        // user_center = "user_center",
        // system_config = "system_config",
        if (_row.auditCode === "ec.monitorSourceMapping.allUpdate" || _row.auditCode === "ec.monitorSourceMapping.selectivityUpdate") {
          let auditInfo = new Function("return" + _row.auditInfo)() || [];

          return h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: "映射关系" }, () => h("div", [h("p", { style: "font-weight:600;color:#000" }, auditInfo[0]?.sourceName + "映射关系")]))]);
        } else if (_row.auditCode === "ec.priorityMatrix.update" || _row.auditCode === "ec.priorityMatrix.selectivityUpdate") {
          return h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: "事件优先级" }, () => h("div", [h("p", { style: "font-weight:600;color:#000" }, resourceTypeOption.find((v) => v.value === value)?.label || "")]))]);
          //设备
        } else if (_row.auditCode === "cmdb.resource.create" || _row.auditCode === "cmdb.resource.selectivityUpdate" || _row.auditCode === "cmdb.resource.delete") {
          const changedValue = new Function("return" + _row.changedValue)() || {};
          const originalValue = new Function("return" + _row.originalValue)() || {};

          if (Object.keys(originalValue).length > 0) {
            return h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: operationTypeOption.find((v) => v.value == _row.auditCode)?.operation || "" }, () => h("div", [h("p", {}, changedValue.name), h("p", { style: "color:#db3328" }, "(" + originalValue.name + ")"), h("p", `${(_row.tenant || {}).name}[${(_row.tenant || {}).abbreviation}]`)]))]);
          } else {
            return h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: operationTypeOption.find((v) => v.value == _row.auditCode)?.operation || "" }, () => h("div", [h("p", {}, changedValue.name), h("p", `${(_row.tenant || {}).name}[${(_row.tenant || {}).abbreviation}]`)]))]);
          }
          //sla
        } else if (_row.auditCode === "ec.sla.create" || _row.auditCode === "ec.sla.update" || _row.auditCode === "ec.sla.delete" || _row.auditCode === "ec.sla.disable") {
          const changedValue = new Function("return" + (_row.changedValue || _row.originalValue))() || {};
          return h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: operationTypeOption.find((v) => v.value == _row.auditCode)?.operation || "" }, () => h("div", [h("p", {}, changedValue.ruleName), h("p", `${(_row.tenant || {}).name}[${(_row.tenant || {}).abbreviation}]`)]))]);
          //全局sla
        } else if (_row.auditCode === "ec.sla.updateGlobal" || _row.auditCode === "ec.sla.createGlobal") {
          return h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: operationTypeOption.find((v) => v.value == _row.auditCode)?.operation || "" }, () => h("div", [h("p", {}, _row.name), h("p", `${(_row.tenant || {}).name}[${(_row.tenant || {}).abbreviation}]`)]))]);
          //区域
        } else if (_row.auditCode === "cmdb.region.create" || _row.auditCode === "cmdb.region.selectiveUpdate" || _row.auditCode === "cmdb.region.changeParent" || _row.auditCode === "cmdb.region.delete") {
          const changedValue = new Function("return" + (_row.changedValue || _row.originalValue))() || {};
          const originalValue = new Function("return" + _row.originalValue)() || {};
          const label = changedValue.parentName ? "子区域" : "区域";
          return h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: label || operationTypeOption.find((v) => v.value == _row.auditCode)?.operation || "" }, () => h("div", [h("p", {}, changedValue.name || originalValue.name), h("p", `${(_row.tenant || {}).name}[${(_row.tenant || {}).abbreviation}]`)]))]);
          //服务编号
        } else if (_row.auditCode === "cmdb.serviceNumber.create" || _row.auditCode === "cmdb.serviceNumber.update" || _row.auditCode === "cmdb.serviceNumber.delete") {
          const changedValue = new Function("return" + (_row.changedValue || _row.originalValue))() || {};
          return h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: operationTypeOption.find((v) => v.value == _row.auditCode)?.operation || "" }, () => h("div", [h("p", {}, changedValue.number), h("p", `${(_row.tenant || {}).name}[${(_row.tenant || {}).abbreviation}]`)]))]);
          //权限配置
        } else if (_row.auditCode === "iam.role_data_auth.save.cmdb.alert.classification" || _row.auditCode === "iam.role_data_auth.save.cmdb.resource.type" || _row.auditCode === "iam.role_data_auth.save.cmdb.resource.group" || _row.auditCode === "iam.role_data_auth.save.cmdb.resource" || _row.auditCode === "iam.role_data_auth.save.cmdb.region" || _row.auditCode === "iam.role_data_auth.save.cmdb.location" || _row.auditCode === "iam.role_data_auth.save.cmdb.contact" || _row.auditCode === "iam.role_data_auth.save.cmdb.vendor") {
          // const changedValue = new Function("return" + _row.changedValue)() || {};
          return h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: "角色" }, () => h("div", [h("p", {}, _row.resourceName)]))]);
        } else if (_row.auditCode === "iam.tenant_pwd_strategy.save" || _row.auditCode === "iam.user_pwd_strategy.save") {
          const resourceName = _row.resourceName?.split("\n")[0];
          const resourceNameLenght = _row.resourceName?.split("\n").length;

          return h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: operationTypeOption.find((v) => v.value == _row.auditCode)?.operation || "" }, () => h("div", [h("p", {}, resourceNameLenght > 1 ? resourceName : ""), h("p", `${(_row.tenant || {}).name}[${(_row.tenant || {}).abbreviation}]`)]))]);
        } else if (_row.auditCode === "ec:auto_event_config:save") {
          // const resourceName = _row.resourceName?.split("\n")[0];

          return h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: "" || "" }, () => h("div", [h("p", {}, "告警归并配置")]))]);
        } else if (_row.auditCode === "cmdb.vendor.create" || _row.auditCode === "cmdb.vendor.update" || _row.auditCode === "cmdb.vendor.delete") {
          //供应商
          const changedValue = new Function("return" + (_row.changedValue || _row.originalValue))() || {};
          return h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: changedValue.vendorTypeName || "" }, () => h("div", [h("p", {}, changedValue.name), h("p", `${(_row.tenant || {}).name}[${(_row.tenant || {}).abbreviation}]`)]))]);
        } else {
          const changedValue = new Function("return" + _row.changedValue)() || {};
          const originalValue = new Function("return" + _row.originalValue)() || {};

          let name = changedValue.name ? changedValue.name : changedValue.tragetName ? changedValue.tragetName : originalValue.name ? originalValue.name : originalValue.number ? originalValue.number : _row.resourceName;

          return h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: operationTypeOption.find((v) => v.value == _row.auditCode)?.operation || "" }, () => h("div", [h("p", {}, name || _row.name || originalValue.number || _row.resourceName), h("p", `${(_row.tenant || {}).name}[${(_row.tenant || {}).abbreviation}]`)]))]);
        }
      },
      width: 300,
    },
    {
      key: "originalValue",
      label: "审计信息",
      formatter: (_row, _col, value) => {
        // console.log(_row.name, JSON.parse(value as string));
        // console.log(eval("(" + JSON.parse(JSON.stringify(value as string)) + ")"));
        if (!_row.success) return "";
        let resourceType = "";
        let operationType = "";
        operationTypeOption.map((v, i) => {
          if (v.value == _row.auditCode) {
            resourceType = v.resourceType;
            operationType = v.type;
          }
        });

        // console.log(operationType);
        switch (operationType) {
          case "删除":
            const changedValue = new Function("return" + (_row.changedValue || _row.originalValue))() || {};
            const _title = h("p", { style: "font-weight:600;color:#000" }, "删除");
            const label = _row.auditCode == "cmdb.region.delete" ? (changedValue.parentName ? "子区域" : "区域") : "";

            const parent_title = changedValue.parentName ? h("p", { style: "font-weight:400;" }, "父区域") : "";
            const parent_Name = changedValue.parentName ? h("p", { style: "margin-left:90px;font-weight:400;color:#000" }, changedValue.parentName) : "";

            const _content = h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: label || operationTypeOption.find((v) => v.value === _row.auditCode)?.operation || "" }, () => h("div", [h("p", { style: `font-weight:600;color:#FF0000` }, changedValue.name || changedValue.number || changedValue.ruleName || _row.name), h("p", `${(_row.tenant || {}).name}[${(_row.tenant || {}).abbreviation}]`)]))]);
            return h("div", {}, [_title, parent_title, parent_Name, _content]);
          default:
            switch (resourceType) {
              // 安全容器
              case "secure_container":
                const securityComponent = { secure_container }[resourceType || ""] || "";

                if (!securityComponent) return "";
                return h(securityComponent, { data: _row });
              // 设备
              case "resource":
                const auditingComponent = { resource }[resourceType || ""] || "";

                if (!auditingComponent) return "";
                return h(auditingComponent, { data: _row });
              // 联系人
              case "contact":
                const contactComponent = { contact }[resourceType || ""] || "";
                if (!contactComponent) return "";
                return h(contactComponent, { data: _row });
              // 区域
              case "region":
                const regionComponent = { region }[resourceType || ""] || "";
                if (!regionComponent) return "";
                return h(regionComponent, { data: _row });
              // 场所
              case "location":
                const locationComponent = { location }[resourceType || ""] || "";
                if (!locationComponent) return "";
                return h(locationComponent, { data: _row });
              // 供应商
              case "vendor":
                const vendorComponent = { vendor }[resourceType || ""] || "";
                if (!vendorComponent) return "";
                return h(vendorComponent, { data: _row });
              // 响应策略
              case "support_note":
                const supportNoteComponent = { support_note }[resourceType || ""] || "";
                if (!supportNoteComponent) return "";
                return h(supportNoteComponent, { data: _row });
              //行动策略关联
              case "support_roletion":
                const supportRoletionComponent = { support_roletion }[resourceType || ""] || "";
                if (!supportRoletionComponent) return "";
                return h(supportRoletionComponent, { data: _row });

              // 服务编号
              case "service_number":
                const serviceNumberComponent = { service_number }[resourceType || ""] || "";
                if (!serviceNumberComponent) return "";
                return h(serviceNumberComponent, { data: _row });
              // 设备分组
              case "device_group":
                const deviceGroupComponent = { device_group }[resourceType || ""] || "";
                if (!deviceGroupComponent) return "";
                return h(deviceGroupComponent, { data: _row });
              // 设备类型
              case "resource_type":
                const deviceTypeComponent = { resource_type }[resourceType || ""] || "";
                if (!deviceTypeComponent) return "";
                return h(deviceTypeComponent, { data: _row });
              // 告警类型
              case "alert_classification":
                const alertClassificationsComponent = { alert_classification }[resourceType || ""] || "";
                if (!alertClassificationsComponent) return "";
                return h(alertClassificationsComponent, { data: _row });
              // 角色
              case "role":
                const roleComponent = { role }[resourceType || ""] || "";
                if (!roleComponent) return "";
                return h(roleComponent, { data: _row });
              case "tenant":
                const tenantComponent = { tenant }[resourceType || ""] || "";
                if (!tenantComponent) return "";
                return h(tenantComponent, { data: _row });
              case "user_center":
                const userCenterComponent = { user_center }[resourceType || ""] || "";
                if (!userCenterComponent) return "";
                return h(userCenterComponent, { data: _row });
              // 用户
              case "user":
                const userComponent = { user }[resourceType || ""] || "";
                if (!userComponent) return "";
                return h(userComponent, { data: _row });
              // 用户组
              case "user_group":
                const userGroupComponent = { user_group }[resourceType || ""] || "";
                if (!userGroupComponent) return "";
                return h(userGroupComponent, { data: _row });

              // 映射关系
              case "monitor_source_mapping":
                const monitor_source_mappingComponent = { monitor_source_mapping }[resourceType || ""] || "";
                if (!monitor_source_mappingComponent) return "";
                return h(monitor_source_mappingComponent, { data: _row });
              // 事件优先级矩阵
              case "priority_matrix":
                const priority_matrixComponent = { priority_matrix }[resourceType || ""] || "";
                if (!priority_matrixComponent) return "";
                return h(priority_matrixComponent, { data: _row });
              // sla配置
              case "sla":
                const slaComponent = { sla }[resourceType || ""] || "";

                if (!slaComponent) return "";
                return h(slaComponent, { data: _row });
              // 告警降级配置
              case "degrade":
                const degradeComponent = { degrade }[resourceType || ""] || "";
                if (!degradeComponent) return "";
                return h(degradeComponent, { data: _row });

              //告警降级关联

              case "degrade_roletion":
                const degrade_roletionComponent = { degrade_roletion }[resourceType || ""] || "";
                if (!degrade_roletionComponent) return "";
                return h(degrade_roletionComponent, { data: _row });

              // 完结代码
              case "global_config":
              case "finish_code":
                const globalConfigComponent = global_config;
                if (!globalConfigComponent) return "";
                return h(globalConfigComponent, { data: _row });
              //事件处理配置
              case "system_config":
                const systemConfigComponent = system_config;
                if (!systemConfigComponent) return "";
                return h(systemConfigComponent, { data: _row });
              // 全局响应策略
              case "support_note_global":
                // const globalsupportNoteComponents = { global_support_note }[_row.resourceType || ""] || "";
                if (!global_support_note) return "";
                return h(global_support_note, { data: _row });
              // 全局sla配置
              case "sla_global":
                if (!global_sla) return "";
                return h(global_sla, { data: _row });

              // 全局告警降级配置
              case "degrade_global":
                if (!global_degrade) return "";
                return h(global_degrade, { data: _row });

              // 角色数据权限配置
              case "role_auth":
                const roleAuthComponent = { role_auth }[resourceType || ""] || "";
                if (!roleAuthComponent) return "";
                return h(roleAuthComponent, { data: _row });
              case "tenant_pwd_strategy":
                const pwdStrategyComponent = { tenant_pwd_strategy }[resourceType || ""] || "";
                if (!pwdStrategyComponent) return "";
                return h(pwdStrategyComponent, { data: _row });

              case "auto_event_config":
                const auto_event_configComponent = { auto_event_config }[resourceType || ""] || "";
                if (!auto_event_configComponent) return "";
                return h(auto_event_configComponent, { data: _row });

              default:
                return "";
            }
        }

        // if (_row.message != "")

        // console.log(resourceType);

        // switch (_row.operationType) {
        //   case OperationType.delete:
        //     const changedValue = new Function("return" + (_row.operationType === OperationType.create ? _row.changedValue : _row.originalValue))() || {};
        //     const _title = h("p", { style: "font-weight:600;color:#000" }, operationTypeOption.find((v) => v.value === ((_row.operationType as string) || ""))?.label || "");
        //     const _content = h(ElForm, { model: _row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: operationTypeOption.find((v) => v.value === _row.resourceType)?.label || "" }, () => h("div", [h("p", { style: `font-weight:600;color:${_row.operationType === OperationType.delete ? "red" : "#000"}` }, changedValue.name || _row.name), h("p", `${(_row.tenant || {}).name}[${(_row.tenant || {}).abbreviation}]`)]))]);
        //     return h("div", {}, [_title, _content]);
        //   default:
        //     return "";
        // }

        // return h(ElForm, { model: _row, labelWidth: "80px", labelPosition: "left" }, [h(ElFormItem, { label: operationTypeOption.find((v) => v.value === value)?.label || "" }, () => h("div", [h("p", {}, _row.name), h("p", `${(_row.tenant || {}).name}[${(_row.tenant || {}).abbreviation}]`)]))]);
      },
    },
    // { key: "name", label: "操作名称" },
    // { key: "details", label: "操作详情" },
    { key: "tenant", label: "客户", width: 160, formatter: (_row, _col, value) => (value ? `${(value as DataItem["tenant"])?.name || ""} [${(value as DataItem["tenant"])?.abbreviation || ""}]` : "--") },
    { key: "user", label: "用户", width: 160, formatter: (_row, _col, value) => (value ? `${(value as DataItem["user"])?.name || ""} (${(value as DataItem["user"])?.account || ""})` : "--") },
    // { key: "path", label: "请求路径" },
    // { key: "consuming", label: "耗时", formatter: (_row, _col, value) => h(ElText, { type: Number(value || 0) > 100 ? (Number(value || 0) > 150 ? "danger" : "warning") : "success" }, () => `${value} ms`) },
    { key: "operationTime", label: "操作时间", formatter: (_row, _col, value) => (Number(value) ? moment(Number(value)).format("YYYY-MM-DD HH:mm:ss") : "--"), width: 160 },
    // { key: "message", label: "执行结果" },
    // { key: "location", label: "所在位置" },
    // { key: "system", label: "服务名称", width: 100 },
    // { key: "platform", label: "平台" },
    { key: "originalIp", label: "IP", formatter: (_row, _col, value) => (value as string) || "--", width: 120 },
    // { key: "createdTime", label: "创建日期", formatter: (_row, _col, v) => formatterDate(v as string) },
    // { key: "updatedTime", label: "修改日期", formatter: (_row, _col, v) => formatterDate(v as string) },
  ],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

watch<typeof publicParams, true>(
  publicParams,
  async function () {
    if (state.loading) return;
    handleStateRefresh();
  },
  { immediate: true, flush: "post" }
);
function showComponents(resourceType, _row) {
  switch (resourceType) {
    // 安全容器
    case "secure_container":
      const securityComponent = { secure_container }[resourceType || ""] || "";

      if (!securityComponent) return "";
      return h(securityComponent, { data: _row });
    // 设备
    case "resource":
      const auditingComponent = { resource }[resourceType || ""] || "";

      if (!auditingComponent) return "";
      return h(auditingComponent, { data: _row });
    // 联系人
    case "contact":
      const contactComponent = { contact }[resourceType || ""] || "";
      if (!contactComponent) return "";
      return h(contactComponent, { data: _row });
    // 区域
    case "region":
      const regionComponent = { region }[resourceType || ""] || "";
      if (!regionComponent) return "";
      return h(regionComponent, { data: _row });
    // 场所
    case "location":
      const locationComponent = { location }[resourceType || ""] || "";
      if (!locationComponent) return "";
      return h(locationComponent, { data: _row });
    // 供应商
    case "vendor":
      const vendorComponent = { vendor }[resourceType || ""] || "";
      if (!vendorComponent) return "";
      return h(vendorComponent, { data: _row });
    // 响应策略
    case "support_note":
      const supportNoteComponent = { support_note }[resourceType || ""] || "";
      if (!supportNoteComponent) return "";
      return h(supportNoteComponent, { data: _row });
    // 服务编号
    case "service_number":
      const serviceNumberComponent = { service_number }[resourceType || ""] || "";
      if (!serviceNumberComponent) return "";
      return h(serviceNumberComponent, { data: _row });
    // 设备分组
    case "device_group":
      const deviceGroupComponent = { device_group }[resourceType || ""] || "";
      if (!deviceGroupComponent) return "";
      return h(deviceGroupComponent, { data: _row });
    // 设备类型
    case "resource_type":
      const deviceTypeComponent = { resource_type }[resourceType || ""] || "";
      if (!deviceTypeComponent) return "";
      return h(deviceTypeComponent, { data: _row });
    // 告警类型
    case "alert_classification":
      const alertClassificationsComponent = { alert_classification }[resourceType || ""] || "";
      if (!alertClassificationsComponent) return "";
      return h(alertClassificationsComponent, { data: _row });
    // 角色
    case "role":
      const roleComponent = { role }[resourceType || ""] || "";
      if (!roleComponent) return "";
      return h(roleComponent, { data: _row });
    case "tenant":
      const tenantComponent = { tenant }[resourceType || ""] || "";
      if (!tenantComponent) return "";
      return h(tenantComponent, { data: _row });
    case "user_center":
      const userCenterComponent = { user_center }[resourceType || ""] || "";
      if (!userCenterComponent) return "";
      return h(userCenterComponent, { data: _row });
    // 用户
    case "user":
      const userComponent = { user }[resourceType || ""] || "";
      if (!userComponent) return "";
      return h(userComponent, { data: _row });
    // 用户组
    case "user_group":
      const userGroupComponent = { user_group }[resourceType || ""] || "";
      if (!userGroupComponent) return "";
      return h(userGroupComponent, { data: _row });

    // 映射关系
    case "monitor_source_mapping":
      const monitor_source_mappingComponent = { monitor_source_mapping }[resourceType || ""] || "";
      if (!monitor_source_mappingComponent) return "";
      return h(monitor_source_mappingComponent, { data: _row });
    // 事件优先级矩阵
    case "priority_matrix":
      const priority_matrixComponent = { priority_matrix }[resourceType || ""] || "";
      if (!priority_matrixComponent) return "";
      return h(priority_matrixComponent, { data: _row });
    // sla配置
    case "sla":
      const slaComponent = { sla }[resourceType || ""] || "";

      if (!slaComponent) return "";
      return h(slaComponent, { data: _row });
    // 告警降级配置
    case "degrade":
      const degradeComponent = { degrade }[resourceType || ""] || "";
      if (!degradeComponent) return "";
      return h(degradeComponent, { data: _row });

    // 完结代码
    case "global_config":
    case "finish_code":
      const globalConfigComponent = global_config;
      if (!globalConfigComponent) return "";
      return h(globalConfigComponent, { data: _row });
    //事件处理配置
    case "system_config":
      const systemConfigComponent = system_config;
      if (!systemConfigComponent) return "";
      return h(systemConfigComponent, { data: _row });
    // 全局响应策略
    case "support_note_global":
      // const globalsupportNoteComponents = { global_support_note }[_row.resourceType || ""] || "";
      if (!global_support_note) return "";
      return h(global_support_note, { data: _row });
    // 全局sla配置
    case "sla_global":
      if (!global_sla) return "";
      return h(global_sla, { data: _row });

    // 全局告警降级配置
    case "degrade_global":
      if (!global_degrade) return "";
      return h(global_degrade, { data: _row });

    // 角色数据权限配置
    case "role_auth":
      const roleAuthComponent = { role_auth }[resourceType || ""] || "";
      if (!roleAuthComponent) return "";
      return h(roleAuthComponent, { data: _row });
    case "tenant_pwd_strategy":
      const pwdStrategyComponent = { tenant_pwd_strategy }[resourceType || ""] || "";
      if (!pwdStrategyComponent) return "";
      return h(pwdStrategyComponent, { data: _row });

    case "auto_event_config":
      const auto_event_configComponent = { auto_event_config }[resourceType || ""] || "";
      if (!auto_event_configComponent) return "";
      return h(auto_event_configComponent, { data: _row });

    default:
      return "";
  }
}
async function handleStateRefresh() {
  if (state.loading) return;
  state.loading = true;
  state.data.splice(0, state.data.length, ...(await querysItem({})));
  state.loading = false;
}
</script>

<style lang="scss" scoped></style>
