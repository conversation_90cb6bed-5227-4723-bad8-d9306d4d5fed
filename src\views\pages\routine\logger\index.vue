<template>
  <el-card>
    <el-scrollbar>
      <div class="flex-search" :style="{ minWidth: `${width - 42}px`, padding: '0px' }">
        <div class="left">
          <!-- <el-input v-model="state.search.keyword" placeholder="关键字搜索" clearable @change="() => ((state.page = 1), (state.size = 20), (state.total = 0), handleStateRefresh())"></el-input> -->
          <!-- <el-date-picker :model-value="[state.search.operationTimeStart, state.search.operationTimeEnd]" @update:model-value="(v) => setTimerange(v)" @change="handleStateRefresh" type="datetimerange" :shortcuts="shortcuts" value-format="X" format="YYYY-MM-DD HH:mm" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" :clearable="false" /> -->

          <el-form ref="searchFormRef" :model="state.search" :inline="true" :rules="searchFormRule">
            <el-form-item>
              <el-input v-model="state.search.keyword" placeholder="关键字搜索" clearable @change="() => ((state.page = 1), (state.size = 50), (state.total = 0), handleStateRefresh())"></el-input>
            </el-form-item>
            <el-form-item>
              <el-select v-model="state.search.shortcuts" @change="handleSetOperationTime">
                <el-option v-for="item in shortcuts" :key="item.value" :label="item.text" :value="item.flag" />
              </el-select>
            </el-form-item>

            <el-form-item label="开始时间" prop="operationTimeStart">
              <el-date-picker v-model="state.search.operationTimeStart" type="datetime" value-format="X" format="YYYY-MM-DD HH:mm" placeholder="请选择开始时间" :clearable="false" />
            </el-form-item>

            <el-form-item label="结束时间" prop="operationTimeEnd">
              <el-date-picker v-model="state.search.operationTimeEnd" type="datetime" value-format="X" format="YYYY-MM-DD HH:mm" placeholder="请选择结束时间" :clearable="false" />
            </el-form-item>
          </el-form>

          <!-- <el-date-picker :model-value="[state.search.operationTimeStart, state.search.operationTimeEnd]" @update:model-value="(v) => setTimerange(v).then(() => handleStateRefresh())" value-format="X" type="datetimerange" :shortcuts :disabled-date="(date: Date) => moment().isBefore(date)" :clearable="false" @visible-change="($event) => ($event ? pause() : resume())"></el-date-picker> -->
          <!-- <span v-if="range">{{ state.search.operationTimeStart === now.getTime() ? "现在" : moment(state.search.operationTimeStart, true).from(now) }} - {{ state.search.operationTimeEnd === now.getTime() ? "现在" : moment(state.search.operationTimeEnd, true).from(now) }}</span> -->
        </div>
        <div class="center">
          <!--  -->
        </div>
        <div class="right">
          <el-switch v-model="state.search.queryType" class="ml-2" inline-prompt active-value="all" inactive-value="main" active-text="全部" inactive-text="当前" @change="(v) => (v ? handleStateRefresh() : undefined)" />
          <el-button type="default" :icon="Refresh" @click="handleClickRefresh"></el-button>
        </div>
      </div>
    </el-scrollbar>
    <el-table v-loading="state.loading" border :data="state.data" row-key="id" :height="height - 64 - 60 - (state.total ? 32 : 0)" :style="{ width: `${width - 40}px`, margin: '0 auto' }" @filter-change="($event) => ((state.search = { ...state.search, ...$event }), handleStateRefresh())">
      <TableColumn type="condition" :list="<DataItem[]>[]" filter-multiple show-filter v-model:custom-filtered-value="searchByAction" @filter-change="() => ((state.page = 1), (state.size = 50), (state.total = 0), handleStateRefresh())" prop="action" label="操作类型" :width="100" :filters="$filter0">
        <template #default="{ row, column }">
          <div>{{ (find(actionTypeOptions, (v) => v.value === row[column.property]) || { label: "--" }).label }}</div>
        </template>
      </TableColumn>
      <el-table-column v-for="column in state.column" :show-overflow-tooltip="column.overflow" :key="column.key" :column-key="column.key" :prop="column.key" :label="column.label" :width="column.width" :formatter="column.formatter" :filters="column.filters" :filtered-value="column.filters ? state.search[column.key] : void 0" :filter-method="column.filters ? () => true : void 0" />
      <TableColumn v-if="state.search.queryType === 'all'" type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByTenant" @filter-change="() => ((state.page = 1), (state.size = 50), (state.total = 0), handleStateRefresh())" prop="tenantName" label="客户" :min-width="120" :filters="$filter0"></TableColumn>
      <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByUser" @filter-change="() => ((state.page = 1), (state.size = 50), (state.total = 0), handleStateRefresh())" prop="userShowName" label="用户" :min-width="120" :filters="$filter2"></TableColumn>
      <TableColumn type="condition" filter-multiple show-filter v-model:custom-filtered-value="searchByIp" @filter-change="() => ((state.page = 1), (state.size = 50), (state.total = 0), handleStateRefresh())" prop="originalIp" label="IP" :width="130" :filters="$filter2"></TableColumn>
      <TableColumn type="date" prop="operationTime" label="创建时间" :width="156"></TableColumn>
      <!-- <TableColumn type="default" prop="digest" label="摘要" :min-width="120" :filters="$filter0"></TableColumn>
      <TableColumn type="default" prop="digest" label="摘要" :min-width="120" :filters="$filter0"></TableColumn> -->
    </el-table>
    <div :style="{ margin: '8px 20px 20px' }">
      <el-pagination v-show="state.total" v-model:current-page="state.page" v-model:page-size="state.size" :page-sizes="state.sizes" :total="state.total" layout="->, total, sizes, prev, pager, next, jumper" small @size-change="handleStateRefresh()" @current-change="handleStateRefresh()" />
    </div>
  </el-card>
</template>

<script setup lang="ts" name="personnel/business">
import { ref, reactive, computed, watch, inject, nextTick, h, type VNode, toValue } from "vue";
// import { useI18n } from "vue-i18n";
import { sizes } from "@/utils/common";
// import { formatterDate } from "@/utils/date";

import resource from "./components/resource.vue";
import contact from "./components/contact.vue";
import region from "./components/region.vue";
import vendor from "./components/vendor.vue";
import location from "./components/location.vue";
import service_number from "./components/service_number.vue";
import support_note from "./components/support_note.vue";
import device_group from "./components/device_group.vue";
import resource_type from "./components/resource_type.vue";
import alert_classification from "./components/alert_classification.vue";
import role from "./components/role.vue";
import role_auth from "./components/role_auth.vue";
import tenant from "./components/tenant.vue";
import user_center from "./components/user_center.vue";
import user from "./components/user.vue";
import user_group from "./components/user_group.vue";
import current_org from "./components/current_org.vue";
import monitor_source_mapping from "./components/monitor_source_mapping.vue";
import priority_matrix from "./components/priority_matrix.vue";
import global_config from "./components/global_config.vue";
import system_config from "./components/system_config.vue";
import sla from "./components/sla.vue";
import global_sla from "./components/global_sla.vue";
import global_support_note from "./components/global_support_note.vue";
import global_degrade from "./components/global_degrade.vue";
import tenant_pwd_strategy from "./components/password_strategy.vue";

import degrade from "./components/degrade.vue";
import auto_event_config from "./components/auto_event_config.vue";
import degrade_roletion from "./components/degradeRoletion.vue";
import support_roletion from "./components/supportRoletion.vue";
import secure_container from "./components/secure_container.vue";

// Ui
import { ElMessage, ElText, ElForm, ElFormItem, ElTag, ElIcon, FormRules, FormInstance } from "element-plus";
// eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
import { More, Refresh, Plus, Edit, Delete, CircleCloseFilled, CircleCheckFilled, Select, CloseBold } from "@element-plus/icons-vue";

import TableColumn from "@/components/tableColumn/TableColumn.vue";

// Api
import { getLogger as getData, actionTypeOptions, ActionType, OperationType, operationTypeOption, resourceTypeOption } from "@/api/system";
import type { LoggerItem as DataItem } from "@/api/system";
import moment from "moment";
import { find } from "lodash-es";
import { useNow } from "@vueuse/core";
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
import getUserInfo from "@/utils/getUserInfo";

import { exoprtMatch1, exoprtMatch2, exoprtMatch3 } from "@/components/tableColumn/common";
const userInfo = getUserInfo();
const timeZoneHours = ref(_timeZoneHours);
const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);
const $filter2 = ref(exoprtMatch3);

const searchFormRef = ref<FormInstance>();
const searchFormRule = ref<FormRules<typeof state.search>>({
  operationTimeStart: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (state.search.operationTimeStart > state.search.operationTimeEnd) return callback(new Error("开始时间不能大于结束时间"));

        handleStateRefresh();
        searchFormRef.value && searchFormRef.value.clearValidate();
        callback();
      },
      trigger: ["blur", "change"],
    },
  ],

  operationTimeEnd: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (state.search.operationTimeEnd < state.search.operationTimeStart) return callback(new Error("结束时间不能小于开始时间"));

        handleStateRefresh();
        searchFormRef.value && searchFormRef.value.clearValidate();
        callback();
      },
      trigger: ["blur", "change"],
    },
  ],
});

// const { now, pause, resume } = useNow({ interval: 1000, controls: true });
// const range = ref("-30day");
// const rangeRegExp = "^(?<method>[+-]?)(?<value>[0-9]+)(?<unit>(year|month|week|day|hour|minute|second|millisecond)?)$";
// const timerange = computed((): [number, number] => {
//   const $now = moment(toValue(now));
//   const timestamp = $now.valueOf();
//   const $exec = new RegExp(rangeRegExp, "g").exec(toValue(range));
//   // console.log($exec);
//   if ($exec) {
//     const method = (($exec.groups || {}).method as "+" | "-") || "-";
//     const value = Number(($exec.groups || {}).value || "1");x
//     const unit = (($exec.groups || {}).unit as "year" | "month" | "week" | "day" | "hour" | "minute" | "second" | "millisecond" | undefined) || "day";
//     switch (method) {
//       case "+": {
//         return [timestamp, $now.add(value, unit).valueOf()];
//       }
//       case "-": {
//         return [$now.subtract(value, unit).valueOf(), timestamp];
//       }
//     }
//   }
//   return [state.search.operationTimeStart, state.search.operationTimeEnd];

// });
const setTimerange = async ($event: [number, number]) => {
  state.page = 1;
  state.size = 50;
  state.total = 0;

  state.search.operationTimeStart = ($event instanceof Array && $event.length ? $event : ([undefined, undefined] as any))[0];
  state.search.operationTimeEnd = ($event instanceof Array && $event.length ? $event : ([undefined, undefined] as any))[1];
  // if ($event instanceof Array) {
  //   if ($event.every((v) => !Number.isNaN(v))) {
  //     range.value = "";
  //     state.search.operationTimeStart = $event[0];
  //     state.search.operationTimeEnd = $event[1];
  //   }
  // } else if (typeof $event === "string" && new RegExp(rangeRegExp, "g").test($event)) {
  //   range.value = `${$event}`;
  // } else {
  //   range.value = "-30day";
  // }
  // await nextTick();
  // const $timerange = toValue(timerange);
  // state.search.operationTimeStart = $timerange[0];
  // state.search.operationTimeEnd = $timerange[1];
};

const shortcuts = ref([
  { flag: "6hour", text: "最近6小时", value: () => [new Date().getTime() - 1000 * 60 * 60 * 6, new Date().getTime()] },
  { flag: "12hour", text: "最近12小时", value: () => [new Date().getTime() - 1000 * 60 * 60 * 12, new Date().getTime()] },
  { flag: "24hour", text: "最近24小时", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24, new Date().getTime()] },
  { flag: "2day", text: "最近2天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 2, new Date().getTime()] },
  { flag: "7day", text: "最近7天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 7, new Date().getTime()] },
  { flag: "30day", text: "最近30天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 30, new Date().getTime()] },
  { flag: "60day", text: "最近60天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 60, new Date().getTime()] },
  { flag: "90day", text: "最近90天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 90, new Date().getTime()] },
  { flag: "180day", text: "最近180天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 180, new Date().getTime()] },
  { flag: "365day", text: "最近365天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 365, new Date().getTime()] },
]);

const publicParams = computed<Record<string, unknown>>(() => ({ schemas: ["USER", "TENANT"].join(",") }));
function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == getUserInfo().zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}
async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  await nextTick();
  let controller = new AbortController();
  // const $timerange = toValue(timerange);
  // state.search.operationTimeStart = $timerange[0];
  // state.search.operationTimeEnd = $timerange[1];
  const response = getData({
    ...(typeof state.search.keyword === "string" && state.search.keyword ? { keyword: state.search.keyword } : {}),
    operationTimeStart: (state.search.operationTimeStart + "000") as any,
    operationTimeEnd: (state.search.operationTimeEnd + "000") as any,
    ...(state.search.userIdIn instanceof Array ? { userIdIn: state.search.userIdIn } : {}) /* 用户ID */,
    ...(state.search.actionIn instanceof Array ? { actionIn: state.search.actionIn } : {}) /* 审计操作 枚举类型: CREATE :新增 | UPDATE :更新 | DELETE :删除 | READ :查看 | ASSIGN :分配 | UNASSIGN :解除分配 | REMOVE :移除 | ENABLE :启用 | DISABLE :停用 | BLOCK :冻结 | UNBLOCK :解冻 | OPEN :开启 | CLOSE :关闭 | CONNECT :连接 | DISCONNECT :断开连接 | PERMISSION_CONFIG :权限配置 | NONE :无(正常不应该会有) */,
    ...(typeof state.search.resourceType === "string" && state.search.resourceType ? { resourceType: state.search.resourceType } : {}) /* 资源类型 */,
    ...(typeof state.search.resourceId === "string" && state.search.resourceId ? { resourceId: state.search.resourceId } : {}) /* 资源ID */,
    ...(typeof state.search.classificationIn === "string" && state.search.classificationIn ? { classificationIn: [state.search.classificationIn] } : {}) /* 审计分类列表 */,
    includeAction: state.search.includeAction,
    excludeAction: state.search.excludeAction,
    eqAction: state.search.eqAction,
    neAction: state.search.neAction,
    actionFilterRelation: state.search.actionFilterRelation,
    includeTenant: state.search.includeTenant,
    excludeTenant: state.search.excludeTenant,
    eqTenant: state.search.eqTenant,
    neTenant: state.search.neTenant,
    tenantFilterRelation: state.search.tenantFilterRelation,
    includeIp: state.search.includeIp,
    excludeIp: state.search.excludeIp,
    ipFilterRelation: state.search.ipFilterRelation,
    includeUser: state.search.includeUser,
    excludeUser: state.search.excludeUser,
    userFilterRelation: state.search.userFilterRelation,

    success: true,
    paging: { pageNumber: state.page, pageSize: state.size },
    controller,
    queryType: state.search.queryType,
  });
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const { success, message, data, page: page = 1, size: size = 20, total: total = 0 } = await response;
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);
      return data instanceof Array
        ? data.map((item) => {
            return {
              ...item,
              operationTime: Number(item.operationTime) + timeZoneSwitching(),
            };
          })
        : [];
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(50);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}

/*********************************************************/

// const { t } = useI18n();

// interface Props {
//   width: number;
//   height: number;
// }
// const props = withDefaults(defineProps<Props>(), {});

const width = inject<import("vue").Ref<number>>("width", ref(100));
const height = inject<import("vue").Ref<number>>("height", ref(100));
type Params = Omit<typeof getData extends (req: infer P) => any ? P : never, "pageNumber" | "pageSize" | "sort" | "paging" | "controller">;
interface StateData<T, P> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: P;
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode; filters?: { text: string; value: string }[]; overflow: boolean }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}

const state = reactive<StateData<DataItem, Params>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {
    keyword: "",
    // operationTimeStart: moment().subtract(30, "day").valueOf(),
    // operationTimeEnd: moment().valueOf(),
    operationTimeStart: Date.parse(new Date((shortcuts.value.find((v) => v.flag === "30day") || ({} as unknown as never)).value()[0]) as unknown as never) / 1000 + timeZoneSwitching() / 1000,
    operationTimeEnd: Date.parse(new Date((shortcuts.value.find((v) => v.flag === "30day") || ({} as unknown as never)).value()[1]) as unknown as never) / 1000 + timeZoneSwitching() / 1000,
    includeAction: [],
    excludeAction: [],
    eqAction: [],
    neAction: [],
    actionFilterRelation: "AND",
    includeTenant: [],
    excludeTenant: [],
    eqTenant: [],
    neTenant: [],
    tenantFilterRelation: "AND",
    includeIp: [],
    excludeIp: [],
    ipFilterRelation: "AND",
    includeUser: [],
    excludeUser: [],
    userFilterRelation: "AND",
    queryType: "main",

    shortcuts: "30day",
  },
  column: [
    /* 列 */
    {
      key: "classificationName",
      label: "配置项",
      overflow: false,
      formatter: (row, _col, value) => {
        if (["audit.fields", "audit.modified_fields", "iam.sc.assign_permission_audit"].includes(row.auditSchema || "")) {
          switch (row.auditSchema) {
            /* ==================================================================================== */
            case "audit.fields": {
              const fields: { name: string; ident: string; value: string }[] = [];
              try {
                const $preValue = JSON.parse(row.auditInfo as string);
                const $value = Object.prototype.toString.call($preValue) === "[object Object]" ? $preValue : { fields: [] };
                if ($value.fields instanceof Array) fields.push(...$value.fields.map((v) => ({ name: v.name || "", ident: v.ident || "", value: v.value })));
              } catch (error) {
                /*  */
              }
              return h("div", { style: { display: "flex", minHeight: "96px" } }, [
                /*  */
                h("div", { style: { flex: "1", display: "flex", flexDirection: "column" } }, [
                  /*  */
                  h(ElText, { truncated: false, type: "info", style: { height: "auto", width: "100%", lineHeight: "24px", fontWeight: "bold" } }, () => value as string),
                ]),
                h("div", { style: { flex: "1", display: "flex", flexDirection: "column", justifyContent: "space-between" } }, [
                  /*  */
                  h(ElText, { truncated: false, type: "", style: { height: "auto", width: "100%", lineHeight: "24px", fontWeight: "bold" } }, () => row.resourceName),
                  h(ElText, { truncated: false, type: "danger", style: { height: "auto", width: "100%", lineHeight: "24px", fontWeight: "bold" } }, () => row.originalResourceName),
                  h(ElText, { truncated: false, type: "info", style: { height: "auto", width: "100%", lineHeight: "24px" } }),
                  h(ElText, { truncated: false, type: "info", style: { height: "auto", width: "100%", lineHeight: "24px" } }, () => row.resourceTenantName),
                ]),
              ]);
            }
            /* ==================================================================================== */
            case "audit.modified_fields": {
              const modifiedFields: { name: string; ident: string; value: string; modifiedValue: string }[] = [];
              try {
                const $preValue = JSON.parse(row.auditInfo as string);
                const $value = Object.prototype.toString.call($preValue) === "[object Object]" ? $preValue : { modifiedFields: [] };
                if ($value.modifiedFields instanceof Array) modifiedFields.push(...$value.modifiedFields.map((v) => ({ name: v.name || "", ident: v.ident || "", value: v.value, modifiedValue: v.modifiedValue })));
              } catch (error) {
                /*  */
              }
              return h("div", { style: { display: "flex", minHeight: "96px" } }, [
                /*  */
                h("div", { style: { flex: "1", display: "flex", flexDirection: "column" } }, [
                  /*  */
                  h(ElText, { truncated: false, type: "info", style: { height: "auto", width: "100%", lineHeight: "24px", fontWeight: "bold" } }, () => value as string),
                ]),
                h("div", { style: { flex: "1", display: "flex", flexDirection: "column", justifyContent: "space-between" } }, [
                  /*  */
                  h(ElText, { truncated: false, type: "", style: { height: "auto", width: "100%", lineHeight: "24px", fontWeight: "bold" } }, () => row.resourceName),
                  h(ElText, { truncated: false, type: "danger", style: { height: "auto", width: "100%", lineHeight: "24px", fontWeight: "bold" } }, () => row.originalResourceName),
                  h(ElText, { truncated: false, type: "info", style: { height: "auto", width: "100%", lineHeight: "24px" } }),
                  h(ElText, { truncated: false, type: "info", style: { height: "auto", width: "100%", lineHeight: "24px" } }, () => row.resourceTenantName),
                ]),
              ]);
            }
            /* ==================================================================================== */
            case "iam.sc.assign_permission_audit": {
              const container = { id: "", name: "" };
              const userGroup = { id: "", name: "" };
              const permissionGroup = { id: "", name: "" };
              const permissionItems: { id: string; name: string; addPermissions: Record<"id" | "name", string>[]; removePermissions: Record<"id" | "name", string>[] }[] = [];
              try {
                const $preValue = JSON.parse(row.auditInfo as string);
                const $value = Object.prototype.toString.call($preValue) === "[object Object]" ? $preValue : { containerId: "", containerName: "", userGroupId: "", userGroupName: "", permissionGroupId: "", permissionGroupName: "", permissionItems: [] };
                Object.assign(container, { id: $value.containerId, name: $value.containerName });
                Object.assign(userGroup, { id: $value.userGroupId, name: $value.userGroupName });
                Object.assign(permissionGroup, { id: $value.permissionGroupId, name: $value.permissionGroupName });
                if ($value.permissionItems instanceof Array) permissionItems.push(...$value.permissionItems.map((v) => ({ id: v.id || "", name: v.name || "", addPermissions: (v.addPermissions instanceof Array ? v.addPermissions : []).map((v0) => ({ id: v0.id || "", name: v0.name })), removePermissions: (v.removePermissions instanceof Array ? v.removePermissions : []).map((v0) => ({ id: v0.id || "", name: v0.name })) })));
              } catch (error) {
                /*  */
              }
              return h("div", { style: { display: "flex", minHeight: "96px" } }, [
                /*  */
                h("div", { style: { flex: "1", display: "flex", flexDirection: "column" } }, [
                  /*  */
                  h(ElText, { truncated: false, type: "info", style: { height: "auto", width: "100%", lineHeight: "24px", fontWeight: "bold" } }, () => value as string),
                ]),
                h("div", { style: { flex: "1", display: "flex", flexDirection: "column", justifyContent: "space-between" } }, [
                  /*  */
                  h(ElText, { truncated: false, type: "", style: { height: "auto", width: "100%", lineHeight: "24px", fontWeight: "bold" } }, () => row.resourceName),
                  h(ElText, { truncated: false, type: "danger", style: { height: "auto", width: "100%", lineHeight: "24px", fontWeight: "bold" } }, () => row.originalResourceName),
                  h(ElText, { truncated: false, type: "info", style: { height: "auto", width: "100%", lineHeight: "24px" } }),
                  h(ElText, { truncated: false, type: "info", style: { height: "auto", width: "100%", lineHeight: "24px" }, title: `${container.name} / ${permissionGroup.name}` }, () => `${container.name} / ${permissionGroup.name}`),
                ]),
              ]);
            }
            default:
              break;
          }
          // switch (row.action) {
          //   case ActionType.CREATE /* 新增 */:
          //     return createVNode("success", "success");
          //   case ActionType.UPDATE /* 更新 */:
          //     return createVNode("danger", "success");
          //   case ActionType.DELETE /* 删除 */:
          //     return createVNode("danger", "danger");
          //   case ActionType.READ /* 查看 */:
          //     return createVNode();
          //   case ActionType.ASSIGN /* 分配 */:
          //     return createVNode();
          //   case ActionType.UNASSIGN /* 解除分配 */:
          //     return createVNode();
          //   case ActionType.REMOVE /* 移除 */:
          //     return createVNode();
          //   case ActionType.ENABLE /* 启用 */:
          //     return createVNode();
          //   case ActionType.DISABLE /* 停用 */:
          //     return createVNode();
          //   case ActionType.BLOCK /* 冻结 */:
          //     return createVNode();
          //   case ActionType.UNBLOCK /* 解冻 */:
          //     return createVNode();
          //   case ActionType.OPEN /* 开启 */:
          //     return createVNode();
          //   case ActionType.CLOSE /* 关闭 */:
          //     return createVNode();
          //   case ActionType.CONNECT /* 连接 */:
          //     return createVNode();
          //   case ActionType.DISCONNECT /* 断开连接 */:
          //     return createVNode();
          //   case ActionType.PERMISSION_CONFIG /* 权限配置 */:
          //     return createVNode("danger", "success");
          //   default:
          //     break;
          // }
        }
        return h("div", { style: { display: "flex", minHeight: "96px" } }, [
          /*  */
          h("div", { style: { flex: "1", display: "flex", flexDirection: "column" } }, [
            /*  */
            h(ElText, { truncated: true, type: "info", style: { height: "auto", width: "100%", lineHeight: "24px", fontWeight: "bold" } }, () => value as string),
          ]),
          h("div", { style: { flex: "1", display: "flex", flexDirection: "column", justifyContent: "space-between" } }, [
            /*  */
            h(ElText, { truncated: true, type: "", style: { height: "auto", width: "100%", lineHeight: "24px", fontWeight: "bold" } }, () => row.resourceName),
            h(ElText, { truncated: true, type: "danger", style: { height: "auto", width: "100%", lineHeight: "24px", fontWeight: "bold" } }, () => row.originalResourceName),
            h(ElText, { truncated: true, type: "info", style: { height: "auto", width: "100%", lineHeight: "24px" } }),
            h(ElText, { truncated: true, type: "info", style: { height: "auto", width: "100%", lineHeight: "24px" } }, () => row.resourceTenantName),
          ]),
        ]);
      },
      width: 280,
    },
    {
      key: "auditInfo",
      label: "审计信息",
      overflow: false,
      formatter: (row, _col, value) => {
        if (["audit.fields", "audit.modified_fields", "iam.sc.assign_permission_audit"].includes(row.auditSchema || "")) {
          const generateValue = (v?: string, type?: "success" | "warning" | "info" | "danger") => {
            switch (v) {
              case "true":
              case "false":
                return h(ElTag, { size: "small", type, style: { marginRight: "auto", marginTop: "2px", marginBottom: "2px" } }, () => h(ElIcon, () => (v === "true" ? h(Select) : h(CloseBold))));
              case void 0:
                return null;
              default:
                return h(ElText, { style: { verticalAlign: "top", marginRight: "auto" }, type, truncated: true }, () => h("pre", { style: { margin: "0", fontFamily: "inherit", minHeight: "24px", lineHeight: "24px", whiteSpace: "pre-line" } }, JSON.stringify(v)));
            }
          };
          switch (row.auditSchema) {
            /* ==================================================================================== */
            case "audit.fields": {
              const fields: { name: string; ident: string; value: string }[] = [];
              try {
                const $preValue = JSON.parse(value as string);
                const $value = Object.prototype.toString.call($preValue) === "[object Object]" ? $preValue : { fields: [] };
                if ($value.fields instanceof Array) fields.push(...$value.fields.map((v) => ({ name: v.name || "", ident: v.ident || "", value: v.value })));
              } catch (error) {
                /*  */
              }
              const nodes = fields.map((v) => {
                const culValue = generateValue(
                  v.value,
                  [
                    /* 标记红色 */
                    "cmdb.resourceGroup.cancelResource",
                    "cmdb.resourceGroup.delete",
                    "cmdb.vendor.removeRelationResource",
                    "cmdb.vendor.delete",
                    "ec.serviceCatalog.device.remove",
                    "ec.dictproject.delete",
                    "iam.user_group.delete",
                    "iam:user_group:remove_children",
                    "iam:user_group:remove_user",
                  ].includes(row.auditCode as string)
                    ? "danger"
                    : [
                          /* 标记绿色` */
                          "iam:user_group:add_user",
                          "ec.dictproject.add",
                        ].includes(row.auditCode as string)
                      ? "success"
                      : undefined
                );
                return h("div", { style: { display: "flex" } }, [
                  /*  */
                  h(ElText, { type: "info", truncated: false, style: { width: "auto", height: "24px", lineHeight: "24px", fontWeight: "bold", marginBottom: "auto", textAlign: "left", display: "contents", whiteSpace: "nowrap" }, title: v.name }, () => v.name),
                  h("span", { style: { marginLeft: "6px", marginRight: "6px", marginBottom: "auto", lineHeight: "24px", verticalAlign: "top" } }, ":"),
                  h("div", { style: { width: "calc(100% - 100px)", display: "flex", flexDirection: "column" } }, [culValue]),
                ]);
              });
              return h("div", { style: { height: "fit-content", display: "flex", flexDirection: "column" } }, [
                /*  */
                h(ElText, { truncated: false, type: "", style: { height: "24px", width: "100%", lineHeight: "24px", fontWeight: "bold" } }, () => (find(actionTypeOptions, (v) => v.value === row.action) || { label: "--" }).label),
                h("div", { style: { display: "flex", flexDirection: "column", width: "100%" } }, nodes),
              ]);
            }
            /* ==================================================================================== */
            case "audit.modified_fields": {
              const modifiedFields: { name: string; ident: string; value: string; modifiedValue: string }[] = [];
              try {
                const $preValue = JSON.parse(value as string);
                const $value = Object.prototype.toString.call($preValue) === "[object Object]" ? $preValue : { modifiedFields: [] };
                if ($value.modifiedFields instanceof Array) modifiedFields.push(...$value.modifiedFields.map((v) => ({ name: v.name || "", ident: v.ident || "", value: v.value, modifiedValue: v.modifiedValue })));
              } catch (error) {
                /*  */
              }
              const nodes = modifiedFields.map((v) => {
                return h("div", { style: { display: "flex" } }, [
                  /*  */
                  h(ElText, { type: "info", truncated: false, style: { width: "90px", height: "24px", lineHeight: "24px", fontWeight: "bold", marginBottom: "auto", textAlign: "left", display: "contents", whiteSpace: "nowrap" } }, () => v.name),
                  h("span", { style: { marginLeft: "6px", marginRight: "6px", marginBottom: "auto", lineHeight: "24px", verticalAlign: "top" } }, ":"),
                  h("div", { style: { width: "calc(100% - 80px)", display: "flex", flexDirection: "column" } }, row.action === ActionType.UPDATE ? [generateValue(v.modifiedValue, "success"), generateValue(v.value, "danger")] : [generateValue(v.modifiedValue, "success")]),
                ]);
              });
              return h("div", { style: { height: "fit-content", display: "flex", flexDirection: "column" } }, [
                /*  */
                h(ElText, { truncated: false, type: "", style: { height: "24px", width: "100%", lineHeight: "24px", fontWeight: "bold" } }, () => (find(actionTypeOptions, (v) => v.value === row.action) || { label: "--" }).label),
                h("div", { style: { display: "flex", flexDirection: "column", width: "100%" } }, nodes),

                // h(ElText, { truncated: true, type: "info", style: { height: "24px", width: "100%", lineHeight: "24px" } }, value),
              ]);
            }
            /* ==================================================================================== */
            case "iam.sc.assign_permission_audit": {
              const container = { id: "", name: "" };
              const userGroup = { id: "", name: "" };
              const permissionGroup = { id: "", name: "" };
              const permissionItems: { id: string; name: string; addPermissions: Record<"id" | "name", string>[]; removePermissions: Record<"id" | "name", string>[] }[] = [];
              try {
                const $preValue = JSON.parse(value as string);
                const $value = Object.prototype.toString.call($preValue) === "[object Object]" ? $preValue : { containerId: "", containerName: "", userGroupId: "", userGroupName: "", permissionGroupId: "", permissionGroupName: "", permissionItems: [] };
                Object.assign(container, { id: $value.containerId, name: $value.containerName });
                Object.assign(userGroup, { id: $value.userGroupId, name: $value.userGroupName });
                Object.assign(permissionGroup, { id: $value.permissionGroupId, name: $value.permissionGroupName });
                if ($value.permissionItems instanceof Array) permissionItems.push(...$value.permissionItems.map((v) => ({ id: v.id || "", name: v.name || "", addPermissions: (v.addPermissions instanceof Array ? v.addPermissions : []).map((v0) => ({ id: v0.id || "", name: v0.name })), removePermissions: (v.removePermissions instanceof Array ? v.removePermissions : []).map((v0) => ({ id: v0.id || "", name: v0.name })) })));
              } catch (error) {
                /*  */
              }
              const newFieldsNode = permissionItems
                .map((v) => {
                  const newValue = v.addPermissions.map((v) => h(ElTag, { size: "small", type: "success", style: { marginRight: "2px", marginTop: "2px", marginBottom: "2px" } }, () => v.name));
                  if (!newValue.length) return null;
                  return h("div", { style: { display: "flex", width: "100%", flexDirection: "column" } }, [
                    /*  */
                    h("div", { style: { display: "flex", width: "100%" } }, [
                      /*  */
                      h(ElText, { type: "info", truncated: true, style: { width: "90px", height: "24px", lineHeight: "24px", textAlign: "right", marginRight: "12px" } }, () => "授权"),
                      h(ElText, { type: "info", truncated: true, style: { width: "calc(100% - 80px)", height: "24px", lineHeight: "24px", fontWeight: "bold" } }, () => v.name),
                    ]),
                    h("div", { style: { width: "calc(100% - 80px)", display: "flex", flexDirection: "column" } }, [
                      /*  */
                      h("div", { style: { whiteSpace: "pre-line", paddingLeft: "80px" } }, newValue),
                    ]),
                  ]);
                })
                .filter((v) => v);
              const oldFieldsNode = permissionItems
                .map((v) => {
                  const oldValue = v.removePermissions.map((v) => h(ElTag, { size: "small", type: "danger", style: { marginRight: "2px", marginTop: "2px", marginBottom: "2px" } }, () => v.name));
                  if (!oldValue.length) return null;
                  return h("div", { style: { display: "flex", width: "100%", flexDirection: "column" } }, [
                    /*  */
                    h("div", { style: { display: "flex", width: "100%" } }, [
                      /*  */
                      h(ElText, { type: "info", truncated: true, style: { width: "90px", height: "24px", lineHeight: "24px", textAlign: "right", marginRight: "12px" } }, () => "授权"),
                      h(ElText, { type: "info", truncated: true, style: { width: "calc(100% - 80px)", height: "24px", lineHeight: "24px", fontWeight: "bold" } }, () => v.name),
                    ]),
                    h("div", { style: { width: "calc(100% - 80px)", display: "flex", flexDirection: "column" } }, [
                      /*  */
                      h("div", { style: { whiteSpace: "pre-line", paddingLeft: "80px" } }, oldValue),
                    ]),
                  ]);
                })
                .filter((v) => v);
              return h("div", { style: { height: "fit-content", display: "flex", flexDirection: "column" } }, [
                /*  */
                h(ElText, { truncated: true, type: "", style: { height: "24px", width: "100%", lineHeight: "24px", fontWeight: "bold" } }, () => (find(actionTypeOptions, (v) => v.value === row.action) || { label: "--" }).label),
                h("div", { style: { display: "flex", flexDirection: "column", width: "100%" } }, [
                  /*  */
                  // h("div", { style: { display: "flex" } }, [
                  //   /*  */
                  //   h(ElText, { style: { width: "90px", height: "24px", lineHeight: "24px", fontWeight: "bold", marginBottom: "auto", textAlign: "right" }, type: "info" }, () => "安全容器"),
                  //   h("span", { style: { marginLeft: "6px", marginRight: "6px", marginBottom: "auto", lineHeight: "24px", verticalAlign: "top" } }, ":"),
                  //   h(ElText, { style: { width: "calc(100% - 80px)", verticalAlign: "top", marginRight: "auto" }, truncated: true, title: container.name }, () => container.name),
                  // ]),
                  h("div", { style: { display: "flex" } }, [
                    /*  */
                    h(ElText, { style: { width: "90px", height: "24px", lineHeight: "24px", fontWeight: "bold", marginBottom: "auto", textAlign: "right" }, type: "info" }, () => "用户组"),
                    h("span", { style: { marginLeft: "6px", marginRight: "6px", marginBottom: "auto", lineHeight: "24px", verticalAlign: "top" } }, ":"),
                    h(ElText, { style: { width: "calc(100% - 80px)", verticalAlign: "top", marginRight: "auto" }, truncated: true, title: userGroup.name }, () => userGroup.name),
                  ]),
                  // h("div", { style: { display: "flex" } }, [
                  //   /*  */
                  //   h(ElText, { style: { width: "90px", height: "24px", lineHeight: "24px", fontWeight: "bold", marginBottom: "auto", textAlign: "right" }, type: "info" }, () => "权限组"),
                  //   h("span", { style: { marginLeft: "6px", marginRight: "6px", marginBottom: "auto", lineHeight: "24px", verticalAlign: "top" } }, ":"),
                  //   h(ElText, { style: { width: "calc(100% - 80px)", verticalAlign: "top", marginRight: "auto" }, truncated: true, title: permissionGroup.name }, () => permissionGroup.name),
                  // ]),
                  h("div", { style: { display: "flex" } }, [
                    /*  */
                    h(ElText, { style: { width: "90px", height: "24px", lineHeight: "24px", fontWeight: "bold", marginBottom: "auto", textAlign: "right" }, type: "info" }),
                    h("span", { style: { marginLeft: "6px", marginRight: "6px", marginBottom: "auto", lineHeight: "24px", verticalAlign: "top" } }, ""),
                    h(ElText, { style: { width: "calc(100% - 80px)", verticalAlign: "top", marginRight: "auto" }, truncated: true, title: row.resourceTenantName }, () => row.resourceTenantName),
                  ]),
                ]),
                newFieldsNode.length ? h("div", { style: { display: "flex" } }, [h(ElText, { style: { width: "90px", height: "24px", lineHeight: "24px", fontWeight: "bold", marginBottom: "auto", textAlign: "right", whiteSpace: "nowrap" }, type: "info" }, () => "权限配置"), h("span", { style: { marginLeft: "6px", marginRight: "6px", marginBottom: "auto", lineHeight: "24px", verticalAlign: "top" } }, ""), h(ElText, { style: { width: "calc(100% - 80px)", verticalAlign: "top", marginRight: "auto" }, truncated: true })]) : null,
                h("div", { style: { display: "flex", flexWrap: "wrap", flexDirection: "column", width: "100%" } }, newFieldsNode),
                oldFieldsNode.length ? h("div", { style: { display: "flex" } }, [h(ElText, { style: { width: "90px", height: "24px", lineHeight: "24px", fontWeight: "bold", marginBottom: "auto", textAlign: "right", whiteSpace: "nowrap" }, type: "info" }, () => "撤回权限配置"), h("span", { style: { marginLeft: "6px", marginRight: "6px", marginBottom: "auto", lineHeight: "24px", verticalAlign: "top" } }, ""), h(ElText, { style: { width: "calc(100% - 80px)", verticalAlign: "top", marginRight: "auto" }, truncated: true })]) : null,
                h("div", { style: { display: "flex", flexWrap: "wrap", flexDirection: "column", width: "100%" } }, oldFieldsNode),
              ]);
            }
            default:
              break;
          }
          // switch (row.action) {
          //   case ActionType.CREATE /* 新增 */:
          //     return createVNode("success", "success");
          //   case ActionType.UPDATE /* 更新 */:
          //     return createVNode("danger", "success");
          //   case ActionType.DELETE /* 删除 */:
          //     return createVNode("danger", "danger");
          //   case ActionType.READ /* 查看 */:
          //     return createVNode();
          //   case ActionType.ASSIGN /* 分配 */:
          //     return createVNode();
          //   case ActionType.UNASSIGN /* 解除分配 */:
          //     return createVNode();
          //   case ActionType.REMOVE /* 移除 */:
          //     return createVNode();
          //   case ActionType.ENABLE /* 启用 */:
          //     return createVNode();
          //   case ActionType.DISABLE /* 停用 */:
          //     return createVNode();
          //   case ActionType.BLOCK /* 冻结 */:
          //     return createVNode();
          //   case ActionType.UNBLOCK /* 解冻 */:
          //     return createVNode();
          //   case ActionType.OPEN /* 开启 */:
          //     return createVNode();
          //   case ActionType.CLOSE /* 关闭 */:
          //     return createVNode();
          //   case ActionType.CONNECT /* 连接 */:
          //     return createVNode();
          //   case ActionType.DISCONNECT /* 断开连接 */:
          //     return createVNode();
          //   case ActionType.PERMISSION_CONFIG /* 权限配置 */:
          //     return createVNode("danger", "success");
          //   default:
          //     break;
          // }
        }

        if (!row.success) return "";
        const { resourceType, type: operationType } = find(operationTypeOption, (v) => v.value === row.auditCode) || {};
        switch (operationType) {
          case "删除":
            let changedValue;
            try {
              changedValue = new Function(`return ${row.changedValue || row.originalValue}`)() || {};
            } catch (error) {
              changedValue = {};
            }
            const _title = h("p", { style: "font-weight:600;color:#000" }, "删除");
            const label = row.auditCode == "cmdb.region.delete" ? (changedValue.parentName ? "子区域" : "区域") : "";

            const parent_title = changedValue.parentName ? h("p", { style: "font-weight:400;" }, "父区域") : "";
            const parent_Name = changedValue.parentName ? h("p", { style: "margin-left:90px;font-weight:400;color:#000" }, changedValue.parentName) : "";

            const _content = h(ElForm, { model: row, labelWidth: "90px", labelPosition: "left" }, [h(ElFormItem, { label: label || operationTypeOption.find((v) => v.value === row.auditCode)?.operation || "" }, () => h("div", [h("p", { style: `font-weight:600;color:#FF0000` }, changedValue.name || changedValue.number || changedValue.ruleName || row.name), h("p", h(ElText, { truncated: true, type: "info", style: { height: "24px", width: "100%", lineHeight: "24px" } }, row.resourceTenantName))]))]);
            return h("div", {}, [_title, parent_title, parent_Name, _content]);
          default:
            switch (resourceType) {
              // 安全容器
              case "secure_container":
                const securityComponent = { secure_container }[resourceType || ""] || "";

                if (!securityComponent) return "";
                return h(securityComponent, { data: row });
              // 设备
              case "resource":
                const auditingComponent = { resource }[resourceType || ""] || "";

                if (!auditingComponent) return "";
                return h(auditingComponent, { data: row });
              // 联系人
              case "contact":
                const contactComponent = { contact }[resourceType || ""] || "";
                if (!contactComponent) return "";
                return h(contactComponent, { data: row });
              // 区域
              case "region":
                const regionComponent = { region }[resourceType || ""] || "";
                if (!regionComponent) return "";
                return h(regionComponent, { data: row });
              // 场所
              case "location":
                const locationComponent = { location }[resourceType || ""] || "";
                if (!locationComponent) return "";
                return h(locationComponent, { data: row });
              // 供应商
              case "vendor":
                const vendorComponent = { vendor }[resourceType || ""] || "";
                if (!vendorComponent) return "";
                return h(vendorComponent, { data: row });
              // 响应策略
              case "support_note":
                const supportNoteComponent = { support_note }[resourceType || ""] || "";
                if (!supportNoteComponent) return "";
                return h(supportNoteComponent, { data: row });
              //行动策略关联
              case "support_roletion":
                const supportRoletionComponent = { support_roletion }[resourceType || ""] || "";
                if (!supportRoletionComponent) return "";
                return h(supportRoletionComponent, { data: row });

              // 服务编号
              case "service_number":
                const serviceNumberComponent = { service_number }[resourceType || ""] || "";
                if (!serviceNumberComponent) return "";
                return h(serviceNumberComponent, { data: row });
              // 设备分组
              case "device_group":
                const deviceGroupComponent = { device_group }[resourceType || ""] || "";
                if (!deviceGroupComponent) return "";
                return h(deviceGroupComponent, { data: row });
              // 设备类型
              case "resource_type":
                const deviceTypeComponent = { resource_type }[resourceType || ""] || "";
                if (!deviceTypeComponent) return "";
                return h(deviceTypeComponent, { data: row });
              // 告警类型
              case "alert_classification":
                const alertClassificationsComponent = { alert_classification }[resourceType || ""] || "";
                if (!alertClassificationsComponent) return "";
                return h(alertClassificationsComponent, { data: row });
              // 角色
              case "role":
                const roleComponent = { role }[resourceType || ""] || "";
                if (!roleComponent) return "";
                return h(roleComponent, { data: row });
              case "tenant":
                const tenantComponent = { tenant }[resourceType || ""] || "";
                if (!tenantComponent) return "";
                return h(tenantComponent, { data: row });
              case "user_center":
                const userCenterComponent = { user_center }[resourceType || ""] || "";
                if (!userCenterComponent) return "";
                return h(userCenterComponent, { data: row });
              // 用户
              case "user":
                const userComponent = { user }[resourceType || ""] || "";
                if (!userComponent) return "";
                return h(userComponent, { data: row });
              // 用户组
              case "user_group":
                const userGroupComponent = { user_group }[resourceType || ""] || "";
                if (!userGroupComponent) return "";
                return h(userGroupComponent, { data: row });

              // 映射关系
              case "monitor_source_mapping":
                const monitor_source_mappingComponent = { monitor_source_mapping }[resourceType || ""] || "";
                if (!monitor_source_mappingComponent) return "";
                return h(monitor_source_mappingComponent, { data: row });
              // 事件优先级矩阵
              case "priority_matrix":
                const priority_matrixComponent = { priority_matrix }[resourceType || ""] || "";
                if (!priority_matrixComponent) return "";
                return h(priority_matrixComponent, { data: row });
              // sla配置
              case "sla":
                const slaComponent = { sla }[resourceType || ""] || "";

                if (!slaComponent) return "";
                return h(slaComponent, { data: row });
              // 告警降级配置
              case "degrade":
                const degradeComponent = { degrade }[resourceType || ""] || "";
                if (!degradeComponent) return "";
                return h(degradeComponent, { data: row });

              //告警降级关联
              case "degrade_roletion":
                const degrade_roletionComponent = { degrade_roletion }[resourceType || ""] || "";
                if (!degrade_roletionComponent) return "";
                return h(degrade_roletionComponent, { data: row });

              // 完结代码
              case "global_config":
              case "finish_code":
                const globalConfigComponent = global_config;
                if (!globalConfigComponent) return "";
                return h(globalConfigComponent, { data: row });
              //事件处理配置
              case "system_config":
                const systemConfigComponent = system_config;
                if (!systemConfigComponent) return "";
                return h(systemConfigComponent, { data: row });
              // 全局响应策略
              case "support_note_global":
                // const globalsupportNoteComponents = { global_support_note }[row.resourceType || ""] || "";
                if (!global_support_note) return "";
                return h(global_support_note, { data: row });
              // 全局sla配置
              case "sla_global":
                if (!global_sla) return "";
                return h(global_sla, { data: row });

              // 全局告警降级配置
              case "degrade_global":
                if (!global_degrade) return "";
                return h(global_degrade, { data: row });

              // 角色数据权限配置
              case "role_auth":
                const roleAuthComponent = { role_auth }[resourceType || ""] || "";
                if (!roleAuthComponent) return "";
                return h(roleAuthComponent, { data: row });
              case "tenant_pwd_strategy":
                const pwdStrategyComponent = { tenant_pwd_strategy }[resourceType || ""] || "";
                if (!pwdStrategyComponent) return "";
                return h(pwdStrategyComponent, { data: row });

              case "auto_event_config":
                const auto_event_configComponent = { auto_event_config }[resourceType || ""] || "";
                if (!auto_event_configComponent) return "";
                return h(auto_event_configComponent, { data: row });

              default:
                return "";
            }
        }
      },
      width: 560,
    },
  ],
  data: [],
  page: 1,
  size: 50,
  sizes,
  total: 0,
});
/* 操作类型 */
const searchType0ByAction = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByAction = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByAction = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByAction) === "include") value0 = state.search.includeAction[0] || "";
    if (toValue(searchType0ByAction) === "exclude") value0 = state.search.excludeAction[0] || "";
    if (toValue(searchType0ByAction) === "eq") value0 = state.search.eqAction[0] || "";
    if (toValue(searchType0ByAction) === "ne") value0 = state.search.neAction[0] || "";
    let value1 = "";
    if (toValue(searchType1ByAction) === "include") value1 = state.search.includeAction[state.search.includeAction.length - 1] || "";
    if (toValue(searchType1ByAction) === "exclude") value1 = state.search.excludeAction[state.search.excludeAction.length - 1] || "";
    if (toValue(searchType1ByAction) === "eq") value1 = state.search.eqAction[state.search.eqAction.length - 1] || "";
    if (toValue(searchType1ByAction) === "ne") value1 = state.search.neAction[state.search.neAction.length - 1] || "";
    return {
      type0: toValue(searchType0ByAction),
      type1: toValue(searchType1ByAction),
      relation: state.search.actionFilterRelation,
      value0,
      value1,
      input0: actionTypeOptions.reduce((p, c) => (p.set(c.label || c.value, c.label), p), new URLSearchParams()).toString(),
      input1: actionTypeOptions.reduce((p, c) => (p.set(c.label || c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByAction.value = v.type0 as typeof searchType0ByAction extends import("vue").Ref<infer T> ? T : string;
    searchType1ByAction.value = v.type1 as typeof searchType1ByAction extends import("vue").Ref<infer T> ? T : string;
    state.search.actionFilterRelation = v.relation;
    state.search.includeAction = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeAction = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqAction = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neAction = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
/* IP */
const searchType0ByIp = ref<"include" | "exclude">("include");
const searchType1ByIp = ref<"include" | "exclude">("include");
const searchByIp = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByIp) === "include") value0 = state.search.includeIp[0] || "";
    if (toValue(searchType0ByIp) === "exclude") value0 = state.search.excludeIp[0] || "";
    let value1 = "";
    if (toValue(searchType1ByIp) === "include") value1 = state.search.includeIp[state.search.includeIp.length - 1] || "";
    if (toValue(searchType1ByIp) === "exclude") value1 = state.search.excludeIp[state.search.excludeIp.length - 1] || "";
    return {
      type0: toValue(searchType0ByIp),
      type1: toValue(searchType1ByIp),
      relation: state.search.ipFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByIp.value = v.type0 as typeof searchType0ByIp extends import("vue").Ref<infer T> ? T : string;
    searchType1ByIp.value = v.type1 as typeof searchType1ByIp extends import("vue").Ref<infer T> ? T : string;
    state.search.ipFilterRelation = v.relation;
    state.search.includeIp = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeIp = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
  },
});
/* 租户名称 */
const searchType0ByTenant = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByTenant = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByTenant = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByTenant) === "include") value0 = state.search.includeTenant[0] || "";
    if (toValue(searchType0ByTenant) === "exclude") value0 = state.search.excludeTenant[0] || "";
    if (toValue(searchType0ByTenant) === "eq") value0 = state.search.eqTenant[0] || "";
    if (toValue(searchType0ByTenant) === "ne") value0 = state.search.neTenant[0] || "";
    let value1 = "";
    if (toValue(searchType1ByTenant) === "include") value1 = state.search.includeTenant[state.search.includeTenant.length - 1] || "";
    if (toValue(searchType1ByTenant) === "exclude") value1 = state.search.excludeTenant[state.search.excludeTenant.length - 1] || "";
    if (toValue(searchType1ByTenant) === "eq") value1 = state.search.eqTenant[state.search.eqTenant.length - 1] || "";
    if (toValue(searchType1ByTenant) === "ne") value1 = state.search.neTenant[state.search.neTenant.length - 1] || "";
    return {
      type0: toValue(searchType0ByTenant),
      type1: toValue(searchType1ByTenant),
      relation: state.search.tenantFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByTenant.value = v.type0 as typeof searchType0ByTenant extends import("vue").Ref<infer T> ? T : string;
    searchType1ByTenant.value = v.type1 as typeof searchType1ByTenant extends import("vue").Ref<infer T> ? T : string;
    state.search.tenantFilterRelation = v.relation;
    state.search.includeTenant = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeTenant = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqTenant = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neTenant = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});
/* 用户 */
const searchType0ByUser = ref<"include" | "exclude">("include");
const searchType1ByUser = ref<"include" | "exclude">("include");
const searchByUser = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByUser) === "include") value0 = state.search.includeUser[0] || "";
    if (toValue(searchType0ByUser) === "exclude") value0 = state.search.excludeUser[0] || "";
    let value1 = "";
    if (toValue(searchType1ByUser) === "include") value1 = state.search.includeUser[state.search.includeUser.length - 1] || "";
    if (toValue(searchType1ByUser) === "exclude") value1 = state.search.excludeUser[state.search.excludeUser.length - 1] || "";
    return {
      type0: toValue(searchType0ByUser),
      type1: toValue(searchType1ByUser),
      relation: state.search.userFilterRelation,
      value0,
      value1,
      input0: "",
      input1: "",
    };
  },
  set: (v) => {
    searchType0ByUser.value = v.type0 as typeof searchType0ByUser extends import("vue").Ref<infer T> ? T : string;
    searchType1ByUser.value = v.type1 as typeof searchType1ByUser extends import("vue").Ref<infer T> ? T : string;
    state.search.userFilterRelation = v.relation;
    state.search.includeUser = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeUser = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
  },
});

watch<typeof publicParams, true>(
  publicParams,
  async function () {
    if (state.loading) return;
    handleStateRefresh();
  },
  { immediate: true, flush: "post" }
);
function showComponents(resourceType, _row) {
  switch (resourceType) {
    // 安全容器
    case "secure_container":
      const securityComponent = { secure_container }[resourceType || ""] || "";

      if (!securityComponent) return "";
      return h(securityComponent, { data: _row });
    // 设备
    case "resource":
      const auditingComponent = { resource }[resourceType || ""] || "";

      if (!auditingComponent) return "";
      return h(auditingComponent, { data: _row });
    // 联系人
    case "contact":
      const contactComponent = { contact }[resourceType || ""] || "";
      if (!contactComponent) return "";
      return h(contactComponent, { data: _row });
    // 区域
    case "region":
      const regionComponent = { region }[resourceType || ""] || "";
      if (!regionComponent) return "";
      return h(regionComponent, { data: _row });
    // 场所
    case "location":
      const locationComponent = { location }[resourceType || ""] || "";
      if (!locationComponent) return "";
      return h(locationComponent, { data: _row });
    // 供应商
    case "vendor":
      const vendorComponent = { vendor }[resourceType || ""] || "";
      if (!vendorComponent) return "";
      return h(vendorComponent, { data: _row });
    // 响应策略
    case "support_note":
      const supportNoteComponent = { support_note }[resourceType || ""] || "";
      if (!supportNoteComponent) return "";
      return h(supportNoteComponent, { data: _row });
    // 服务编号
    case "service_number":
      const serviceNumberComponent = { service_number }[resourceType || ""] || "";
      if (!serviceNumberComponent) return "";
      return h(serviceNumberComponent, { data: _row });
    // 设备分组
    case "device_group":
      const deviceGroupComponent = { device_group }[resourceType || ""] || "";
      if (!deviceGroupComponent) return "";
      return h(deviceGroupComponent, { data: _row });
    // 设备类型
    case "resource_type":
      const deviceTypeComponent = { resource_type }[resourceType || ""] || "";
      if (!deviceTypeComponent) return "";
      return h(deviceTypeComponent, { data: _row });
    // 告警类型
    case "alert_classification":
      const alertClassificationsComponent = { alert_classification }[resourceType || ""] || "";
      if (!alertClassificationsComponent) return "";
      return h(alertClassificationsComponent, { data: _row });
    // 角色
    case "role":
      const roleComponent = { role }[resourceType || ""] || "";
      if (!roleComponent) return "";
      return h(roleComponent, { data: _row });
    case "tenant":
      const tenantComponent = { tenant }[resourceType || ""] || "";
      if (!tenantComponent) return "";
      return h(tenantComponent, { data: _row });
    case "user_center":
      const userCenterComponent = { user_center }[resourceType || ""] || "";
      if (!userCenterComponent) return "";
      return h(userCenterComponent, { data: _row });
    // 用户
    case "user":
      const userComponent = { user }[resourceType || ""] || "";
      if (!userComponent) return "";
      return h(userComponent, { data: _row });
    // 用户组
    case "user_group":
      const userGroupComponent = { user_group }[resourceType || ""] || "";
      if (!userGroupComponent) return "";
      return h(userGroupComponent, { data: _row });

    // 映射关系
    case "monitor_source_mapping":
      const monitor_source_mappingComponent = { monitor_source_mapping }[resourceType || ""] || "";
      if (!monitor_source_mappingComponent) return "";
      return h(monitor_source_mappingComponent, { data: _row });
    // 事件优先级矩阵
    case "priority_matrix":
      const priority_matrixComponent = { priority_matrix }[resourceType || ""] || "";
      if (!priority_matrixComponent) return "";
      return h(priority_matrixComponent, { data: _row });
    // sla配置
    case "sla":
      const slaComponent = { sla }[resourceType || ""] || "";

      if (!slaComponent) return "";
      return h(slaComponent, { data: _row });
    // 告警降级配置
    case "degrade":
      const degradeComponent = { degrade }[resourceType || ""] || "";
      if (!degradeComponent) return "";
      return h(degradeComponent, { data: _row });

    // 完结代码
    case "global_config":
    case "finish_code":
      const globalConfigComponent = global_config;
      if (!globalConfigComponent) return "";
      return h(globalConfigComponent, { data: _row });
    //事件处理配置
    case "system_config":
      const systemConfigComponent = system_config;
      if (!systemConfigComponent) return "";
      return h(systemConfigComponent, { data: _row });
    // 全局响应策略
    case "support_note_global":
      // const globalsupportNoteComponents = { global_support_note }[_row.resourceType || ""] || "";
      if (!global_support_note) return "";
      return h(global_support_note, { data: _row });
    // 全局sla配置
    case "sla_global":
      if (!global_sla) return "";
      return h(global_sla, { data: _row });

    // 全局告警降级配置
    case "degrade_global":
      if (!global_degrade) return "";
      return h(global_degrade, { data: _row });

    // 角色数据权限配置
    case "role_auth":
      const roleAuthComponent = { role_auth }[resourceType || ""] || "";
      if (!roleAuthComponent) return "";
      return h(roleAuthComponent, { data: _row });
    case "tenant_pwd_strategy":
      const pwdStrategyComponent = { tenant_pwd_strategy }[resourceType || ""] || "";
      if (!pwdStrategyComponent) return "";
      return h(pwdStrategyComponent, { data: _row });

    case "auto_event_config":
      const auto_event_configComponent = { auto_event_config }[resourceType || ""] || "";
      if (!auto_event_configComponent) return "";
      return h(auto_event_configComponent, { data: _row });

    default:
      return "";
  }
}

function handleClickRefresh() {
  state.search.operationTimeEnd = parseInt((new Date().getTime() / 1000).toString());
  nextTick(() => handleStateRefresh());
}

async function handleStateRefresh() {
  if (state.loading) return;
  state.loading = true;
  state.data.splice(0, state.data.length, ...(await querysItem({})));
  state.loading = false;
}

/**
 * @desc 时间选择器快捷方式
 */
function handleSetOperationTime(val) {
  const item = shortcuts.value.find((v) => v.flag === val);
  if (item) {
    const value = item.value();
    state.search.operationTimeStart = Math.floor(value[0] / 1000);
    state.search.operationTimeEnd = Math.floor(value[1] / 1000);

    nextTick(() => handleStateRefresh());
  }
}
// watch(
//   () => [toValue(timerange)[0] - state.search.operationTimeStart, toValue(timerange)[1] - state.search.operationTimeEnd],
//   async (offsetrange) => {
//     if (offsetrange.some((offset) => offset >= 30000)) {
//       await nextTick();
//       await handleStateRefresh();
//     }
//   }
// );
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 0 !important;
}
</style>
