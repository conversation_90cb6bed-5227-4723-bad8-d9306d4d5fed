<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="getList()" @current-change="getList()">
      <template #left>
        <el-form ref="searchFormRef" :model="searchForm" :inline="true" :rules="searchFormRule">
          <el-form-item>
            <el-select v-model="searchForm.shortcuts" @change="handleSetOperationTime" placeholder="请选择发送时间" clearable class="tw-w-[200px]">
              <el-option v-for="item in shortcuts" :key="item.value" :label="item.text" :value="item.flag" />
            </el-select>
          </el-form-item>
        </el-form>
      </template>
      <template #right>
        <el-switch v-model="searchForm.queryType" class="ml-2" inline-prompt active-value="all" inactive-value="main" active-text="全部" inactive-text="当前" @change="(v) => (v ? getList() : undefined)" />
        <el-button type="default" class="tw-ml-2" :icon="Refresh" @click="getList"></el-button>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table v-loading="loading" stripe :data="tableData" row-key="id" :height="tableHeight" style="width: 100%">
          <TableColumn type="condition" :prop="`name`" :label="`收件手机号`" :width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByPhone" :filters="$filter0" @filter-change="getList()" :formatter="formatterTable">
            <template #default="scope">
              <el-tooltip class="box-item" effect="dark" :content="scope.row.phones.join()" placement="top">
                <el-text line-clamp="1"> {{ scope.row.phones.join(",") || "--" }}</el-text>
              </el-tooltip>
            </template>
          </TableColumn>

          <TableColumn type="condition" :prop="`content`" :label="`短信内容`" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByContent" :filters="$filter1" @filter-change="getList()" :formatter="formatterTable">
            <template #default="{ row }">
              <div>
                <el-button link @click="handleMessageView(row.content)">
                  {{ handleFormatterContent(row.content) }}
                </el-button>
              </div>
            </template>
          </TableColumn>

          <!-- <el-table-column :prop="`content`" :label="`短信内容`" :show-overflow-tooltip="true" :formatter="formatterTable"></el-table-column> -->

          <TableColumn type="date" show-filter v-model:filtered-value="timeByCreate" filter-multiple @filter-change="getList()" prop="createTime" label="发送时间" :width="180">
            <template #default="{ row }">{{ moment(row.sendTime, "x").format("yyyy-MM-DD HH:mm:ss") }}</template>
          </TableColumn>

          <TableColumn type="enum" :prop="`active`" :label="`发送状态`" :width="180" :showOverflowTooltip="true" show-filter v-model:filtered-value="searchForm.success" :filters="['true', 'false'].map((v) => ({ value: v, text: v === 'true' ? '发送成功' : '发送失败' }))" @filter-change="getList()">
            <template #default="scope">
              <span>{{ scope.row.success == true ? "发送成功" : "发送失败" }}</span>
            </template>
          </TableColumn>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, computed, toValue, h } from "vue";

/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMessageBox, FormRules, FormInstance, ElButton } from "element-plus";
import { Refresh } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import { getMessageList, type CurrentListResBody as DataItem } from "@/views/pages/apis/messageShow";
import moment from "moment";
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);

import TableColumn from "@/components/tableColumn/TableColumn.vue";
import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance()!;
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
defineOptions({ name: "messageShow" });

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const userInfo = getUserInfo();
const userInfoS = getUserInfo();

// const $filter0 = ref([
//   { text: "包含", value: "include" },
//   { text: "不包含", value: "exclude" },
//   { text: "等于", value: "eq" },
//   { text: "不等于", value: "ne" },
// ]);

const $filter1 = ref([
  { text: t("glob.Contains"), value: "include" },
  { text: t("glob.Does not contain"), value: "exclude" },
]);
const $filter0 = ref(exoprtMatch1);
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const loading = ref<boolean>(false);

const searchForm = ref<Record<string, any>>({
  sendTimeStart: "" /* 发送起始时间 */,
  sendTimeEnd: "" /* 发送结束时间 */,
  shortcuts: "7day",
  success: "" /* 发送状态 */,

  includePhone: [],
  excludePhone: [],
  eqPhone: [] /* 等于的手机号 */,
  phoneFilterRelation: "AND" /* 手机号过滤关系(AND,OR) */,
  nePhone: [] /* 不等于的手机号 */,
  queryType: "main",

  includeContent: [],
  excludeContent: [],
  contentFilterRelation: "AND",
});

const searchType0ByPhone = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByPhone = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByPhone = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByPhone) === "include") value0 = searchForm.value.includePhone[0] || "";
    if (toValue(searchType0ByPhone) === "exclude") value0 = searchForm.value.excludePhone[0] || "";
    if (toValue(searchType0ByPhone) === "eq") value0 = searchForm.value.eqPhone[0] || "";
    if (toValue(searchType0ByPhone) === "ne") value0 = searchForm.value.nePhone[0] || "";
    let value1 = "";
    if (toValue(searchType1ByPhone) === "include") value1 = searchForm.value.includePhone[1] || "";
    if (toValue(searchType1ByPhone) === "exclude") value1 = searchForm.value.excludePhone[searchForm.value.excludePhone.length - 1] || "";
    if (toValue(searchType1ByPhone) === "eq") value1 = searchForm.value.eqPhone[searchForm.value.eqPhone.length - 1] || "";
    if (toValue(searchType1ByPhone) === "ne") value1 = searchForm.value.nePhone[searchForm.value.nePhone.length - 1] || "";
    return {
      type0: toValue(searchType0ByPhone),
      type1: toValue(searchType1ByPhone),
      relation: searchForm.value.phoneFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByPhone.value = v.type0 as typeof searchType0ByPhone extends import("vue").Ref<infer T> ? T : string;
    searchType1ByPhone.value = v.type1 as typeof searchType1ByPhone extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.phoneFilterRelation = v.relation;
    searchForm.value.includePhone = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludePhone = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqPhone = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.nePhone = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByContent = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByContent = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByContent = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByContent) === "include") value0 = searchForm.value.includeContent[0] || "";
    if (toValue(searchType0ByContent) === "exclude") value0 = searchForm.value.excludeContent[0] || "";
    // if (toValue(searchType0ByContent) === "eq") value0 = searchForm.value.eqContent[0] || "";
    // if (toValue(searchType0ByContent) === "ne") value0 = searchForm.value.neContent[0] || "";
    let value1 = "";
    if (toValue(searchType1ByContent) === "include") value1 = searchForm.value.includeContent[1] || "";
    if (toValue(searchType1ByContent) === "exclude") value1 = searchForm.value.excludeContent[searchForm.value.excludeContent.length - 1] || "";
    // if (toValue(searchType1ByContent) === "eq") value1 = searchForm.value.eqContent[searchForm.value.eqContent.length - 1] || "";
    // if (toValue(searchType1ByContent) === "ne") value1 = searchForm.value.neContent[searchForm.value.neContent.length - 1] || "";
    return {
      type0: toValue(searchType0ByContent),
      type1: toValue(searchType1ByContent),
      relation: searchForm.value.contentFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByContent.value = v.type0 as typeof searchType0ByContent extends import("vue").Ref<infer T> ? T : string;
    searchType1ByContent.value = v.type1 as typeof searchType1ByContent extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.contentFilterRelation = v.relation;
    searchForm.value.includeContent = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeContent = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    // searchForm.value.eqContent = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    // searchForm.value.neContent = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const timeByCreate = computed({
  get: () => (searchForm.value.sendTimeStart && searchForm.value.sendTimeEnd ? { start: searchForm.value.sendTimeStart, end: searchForm.value.sendTimeEnd } : ""),
  set: (v) => {
    searchForm.value.sendTimeStart = (v || {}).start || "";
    searchForm.value.sendTimeEnd = (v || {}).end || "";
  },
});

const tableData = ref<DataItem[]>([]);
const paging = reactive({
  pageNumber: 1,
  pageSize: 50,
  total: 0,
});

const searchFormRef = ref<FormInstance>();

const searchFormRule = ref<FormRules<typeof searchForm.value>>({
  operationTimeStart: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (searchForm.value.operationTimeStart > searchForm.value.operationTimeEnd) return callback(new Error("开始时间不能大于结束时间"));

        getList();
        searchFormRef.value && searchFormRef.value.clearValidate();
        callback();
      },
      trigger: ["blur", "change"],
    },
  ],

  operationTimeEnd: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (searchForm.value.operationTimeEnd < searchForm.value.operationTimeStart) return callback(new Error("结束时间不能小于开始时间"));

        getList();
        searchFormRef.value && searchFormRef.value.clearValidate();
        callback();
      },
      trigger: ["blur", "change"],
    },
  ],
});

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  // getList();
}

function beforeMount() {}
function mounted() {
  handleSetOperationTime(searchForm.value.shortcuts);
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

function handleMessageView(v) {
  ElMessageBox({
    title: "短信内容",
    message: h("div", { innerHTML: JSON.parse(v).ctsms_content }),
  })
    .then(() => {})
    .catch(() => {});
}

function handleFormatterContent(v) {
  try {
    return JSON.parse(v).ctsms_content;
  } catch (error) {
    return v;
  }
}

function formatterTable(_row, _col, v) {
  switch (_col.property) {
    case "content":
      try {
        return h("div", [
          h(
            ElButton,
            {
              link: true,
              onClick: () => handleMessageView(v),
            },
            () => JSON.parse(v).ctsms_content
          ),
        ]);
      } catch (error) {
        return "--";
      }
    default:
      return v || "--";
  }
}

function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == getUserInfo().zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}

//获取列表
function getList() {
  loading.value = true;
  getMessageList({ pageNumber: paging.pageNumber, pageSize: paging.pageSize, ...searchForm.value }).then((res) => {
    if (res.success) {
      loading.value = false;
      tableData.value = res.data.map((item) => {
        return {
          ...item,
          sendTime: Number(item.sendTime) + timeZoneSwitching(),
        };
      });
      paging.total = Number(res.total);
    } else {
      ElMessage.error(JSON.parse(res.data)?.message);
    }
  });
}

function handleSizeChange(v) {
  paging.pageSize = v;
  paging.pageNumber = 1;
}
function handleCurrentPageChange(v) {
  paging.pageNumber = v;
}

const shortcuts = ref([
  { flag: "6hour", text: "最近6小时", value: () => [new Date().getTime() - 1000 * 60 * 60 * 6, new Date().getTime()] },
  { flag: "12hour", text: "最近12小时", value: () => [new Date().getTime() - 1000 * 60 * 60 * 12, new Date().getTime()] },
  { flag: "24hour", text: "最近24小时", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24, new Date().getTime()] },
  { flag: "2day", text: "最近2天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 2, new Date().getTime()] },
  { flag: "7day", text: "最近7天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 7, new Date().getTime()] },
  { flag: "30day", text: "最近30天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 30, new Date().getTime()] },
  { flag: "60day", text: "最近60天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 60, new Date().getTime()] },
  { flag: "90day", text: "最近90天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 90, new Date().getTime()] },
  { flag: "180day", text: "最近180天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 180, new Date().getTime()] },
  { flag: "365day", text: "最近365天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 365, new Date().getTime()] },
]);

/**
 * @desc 时间选择器快捷方式
 */
function handleSetOperationTime(val) {
  const item = shortcuts.value.find((v) => v.flag === val);
  if (item) {
    const value = item.value();
    searchForm.value.sendTimeStart = Math.floor(value[0]);
    searchForm.value.sendTimeEnd = Math.floor(value[1]);
  } else {
    searchForm.value.sendTimeStart = "";
    searchForm.value.sendTimeEnd = "";
  }

  nextTick(() => getList());
}

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
