<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="getList()" @current-change="getList()">
      <template #left>
        <el-form ref="searchFormRef" :model="searchForm" :inline="true" :rules="searchFormRule">
          <el-form-item>
            <el-select v-model="searchForm.shortcuts" @change="handleSetOperationTime" placeholder="请选择发送时间" clearable class="tw-w-[200px]">
              <el-option v-for="item in shortcuts" :key="item.value" :label="item.text" :value="item.flag" />
            </el-select>
          </el-form-item>
        </el-form>
      </template>
      <template #right>
        <el-switch v-model="searchForm.queryType" class="ml-2" inline-prompt active-value="all" inactive-value="main" active-text="全部" inactive-text="当前" @change="(v) => (v ? getList() : undefined)" />
        <el-button type="default" class="tw-ml-2" :icon="Refresh" @click="getList"></el-button>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table v-loading="loading" stripe :data="tableData" row-key="id" :height="tableHeight" style="width: 100%">
          <TableColumn type="condition" :prop="`name`" :label="`收件Email`" width="250" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByTo" :filters="$filter0" @filter-change="handleQuery()" :formatter="formatterTable">
            <template #default="scope">
              <el-tooltip class="box-item" effect="dark" :content="scope.row.to.join()" placement="top">
                <el-text line-clamp="1"> {{ scope.row.to.join(",") || "--" }}</el-text>
              </el-tooltip>
            </template>
          </TableColumn>

          <!-- <el-table-column prop="name" :formatter="formatterTable" label="">
            <template #default="scope">
              <el-tooltip class="box-item" effect="dark" :content="scope.row.to.join()" placement="top">
                <el-text line-clamp="1"> {{ scope.row.to.join(",") || "--" }}</el-text>
              </el-tooltip>
            </template>
          </el-table-column> -->
          <!-- <el-table-column prop="name" :formatter="formatterTable" label="发送Email" :width="250">
            <template #default="scope">
              <el-tooltip class="box-item" effect="dark" :content="scope.row.from" placement="top">
                <el-text line-clamp="1"> {{ scope.row.from || "--" }}</el-text>
              </el-tooltip>
            </template>
          </el-table-column> -->

          <TableColumn type="condition" :prop="`name`" :label="`发送Email`" width="250" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByFrom" :filters="$filter0" @filter-change="handleQuery()" :formatter="formatterTable">
            <template #default="scope">
              <el-tooltip class="box-item" effect="dark" :content="scope.row.from" placement="top">
                <el-text line-clamp="1"> {{ scope.row.from || "--" }}</el-text>
              </el-tooltip>
            </template>
          </TableColumn>

          <TableColumn type="condition" :prop="`subject`" :label="`邮件内容`" :showOverflowTooltip="false" filter-multiple show-filter v-model:custom-filtered-value="searchByContent" :filters="$filter1" @filter-change="getList()" :formatter="formatterTable">
            <template #default="{ row }">
              <div>
                <el-button link @click="handleEmailView(row)">
                  {{ row.subject }}
                </el-button>
              </div>
            </template>
          </TableColumn>

          <TableColumn type="date" show-filter v-model:filtered-value="timeByCreate" filter-multiple @filter-change="handleQuery()" prop="createTime" label="发送时间" width="180">
            <template #default="{ row }">{{ moment(row.sendTime, "x").format("yyyy-MM-DD HH:mm:ss") }}</template>
          </TableColumn>

          <TableColumn type="enum" :prop="`active`" :label="`发送状态`" width="120" :showOverflowTooltip="true" show-filter v-model:filtered-value="searchForm.success" :filters="['true', 'false'].map((v) => ({ value: v, text: v === 'true' ? '发送成功' : '发送失败' }))" @filter-change="handleQuery()">
            <template #default="scope">
              <span>{{ scope.row.success == true ? "发送成功" : "发送失败" }}</span>
            </template>
          </TableColumn>

          <!-- <el-table-column prop="html" :formatter="formatterTable" label="邮件内容" :width="180">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleEmailView(row)">查看</el-button>
            </template>
          </el-table-column> -->

          <!-- <el-table-column prop="sendTime" :formatter="formatterTable" label="发送时间" :width="180">
            <template #default="{ row }">{{ moment(row.sendTime, "x").format("yyyy-MM-DD HH:mm:ss") }}</template>
          </el-table-column>
          <el-table-column prop="success" :formatter="formatterTable" label="发送状态" :width="180">
            <template #default="scope">
              <span>{{ scope.row.success == true ? "发送成功" : "发送失败" }}</span>
            </template>
          </el-table-column> -->
        </el-table>
      </template>
    </pageTemplate>

    <el-dialog v-model="emailViewVisible" title="邮件内容" width="600">
      <div v-if="emailViewVisible" v-html="emailViewContent"></div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="emailViewVisible = false"> 确 定 </el-button>
        </div>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, inject, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, toValue, computed } from "vue";

/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMessageBox } from "element-plus";
import { Refresh } from "@element-plus/icons-vue";
import pageTemplate from "@/components/pageTemplate.vue";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import { getMailList, type CurrentListResBody as DataItem } from "@/views/pages/apis/messageShow";
import moment from "moment";

import TableColumn from "@/components/tableColumn/TableColumn.vue";
import _timeZoneHours from "@/views/pages/common/offsetHours.json";
const timeZoneHours = ref(_timeZoneHours);
import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance()!;
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
defineOptions({ name: "emailShow" });

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const userInfo = getUserInfo();
const userInfoS = getUserInfo();

/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
const loading = ref<boolean>(false);

// const $filter0 = ref([
//   { text: "包含", value: "include" },
//   { text: "不包含", value: "exclude" },
//   { text: "等于", value: "eq" },
//   { text: "不等于", value: "ne" },
// ]);

// const $filter1 = ref([
//   { text: "包含", value: "include" },
//   { text: "不包含", value: "exclude" },
// ]);
const $filter1 = ref([
  { text: t("glob.Contains"), value: "include" },
  { text: t("glob.Does not contain"), value: "exclude" },
]);
const $filter0 = ref(exoprtMatch1);

const searchForm = ref<Record<string, any>>({
  shortcuts: "7day",
  sendTimeStart: "" /* 发送起始时间 */,
  sendTimeEnd: "" /* 发送结束时间 */,
  success: "" /* 发送状态 */,

  includeTo: [],
  excludeTo: [],
  eqTo: [] /* 等于的收件email */,
  toFilterRelation: "AND" /* 收件email过滤关系(AND,OR) */,
  neTo: [] /* 不等于的收件email */,

  includeFrom: [],
  excludeFrom: [],
  eqFrom: [] /* 等于的发送email */,
  fromFilterRelation: "AND" /* 发送email过滤关系(AND,OR) */,
  neFrom: [] /* 不等于的发送email */,

  queryType: "main",

  includeContent: [],
  excludeContent: [],
  contentFilterRelation: "AND",
});

const searchType0ByTo = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByTo = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByTo = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByTo) === "include") value0 = searchForm.value.includeTo[0] || "";
    if (toValue(searchType0ByTo) === "exclude") value0 = searchForm.value.excludeTo[0] || "";
    if (toValue(searchType0ByTo) === "eq") value0 = searchForm.value.eqTo[0] || "";
    if (toValue(searchType0ByTo) === "ne") value0 = searchForm.value.neTo[0] || "";
    let value1 = "";
    if (toValue(searchType1ByTo) === "include") value1 = searchForm.value.includeTo[searchForm.value.includeTo.length - 1] || "";
    if (toValue(searchType1ByTo) === "exclude") value1 = searchForm.value.excludeTo[searchForm.value.excludeTo.length - 1] || "";
    if (toValue(searchType1ByTo) === "eq") value1 = searchForm.value.eqTo[searchForm.value.eqTo.length - 1] || "";
    if (toValue(searchType1ByTo) === "ne") value1 = searchForm.value.neTo[searchForm.value.neTo.length - 1] || "";
    return {
      type0: toValue(searchType0ByTo),
      type1: toValue(searchType1ByTo),
      relation: searchForm.value.toFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByTo.value = v.type0 as typeof searchType0ByTo extends import("vue").Ref<infer T> ? T : string;
    searchType1ByTo.value = v.type1 as typeof searchType1ByTo extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.toFilterRelation = v.relation;
    searchForm.value.includeTo = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeTo = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqTo = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neTo = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByFrom = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByFrom = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByFrom = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByFrom) === "include") value0 = searchForm.value.includeFrom[0] || "";
    if (toValue(searchType0ByFrom) === "exclude") value0 = searchForm.value.excludeFrom[0] || "";
    if (toValue(searchType0ByFrom) === "eq") value0 = searchForm.value.eqFrom[0] || "";
    if (toValue(searchType0ByFrom) === "ne") value0 = searchForm.value.neFrom[0] || "";
    let value1 = "";
    if (toValue(searchType1ByFrom) === "include") value1 = searchForm.value.includeFrom[searchForm.value.includeFrom.length - 1] || "";
    if (toValue(searchType1ByFrom) === "exclude") value1 = searchForm.value.excludeFrom[searchForm.value.excludeFrom.length - 1] || "";
    if (toValue(searchType1ByFrom) === "eq") value1 = searchForm.value.eqFrom[searchForm.value.eqFrom.length - 1] || "";
    if (toValue(searchType1ByFrom) === "ne") value1 = searchForm.value.neFrom[searchForm.value.neFrom.length - 1] || "";
    return {
      type0: toValue(searchType0ByFrom),
      type1: toValue(searchType1ByFrom),
      relation: searchForm.value.fromFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByFrom.value = v.type0 as typeof searchType0ByFrom extends import("vue").Ref<infer T> ? T : string;
    searchType1ByFrom.value = v.type1 as typeof searchType1ByFrom extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.fromFilterRelation = v.relation;
    searchForm.value.includeFrom = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeFrom = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    searchForm.value.eqFrom = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    searchForm.value.neFrom = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByContent = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByContent = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByContent = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByContent) === "include") value0 = searchForm.value.includeContent[0] || "";
    if (toValue(searchType0ByContent) === "exclude") value0 = searchForm.value.excludeContent[0] || "";
    // if (toValue(searchType0ByContent) === "eq") value0 = searchForm.value.eqContent[0] || "";
    // if (toValue(searchType0ByContent) === "ne") value0 = searchForm.value.neContent[0] || "";
    let value1 = "";
    if (toValue(searchType1ByContent) === "include") value1 = searchForm.value.includeContent[1] || "";
    if (toValue(searchType1ByContent) === "exclude") value1 = searchForm.value.excludeContent[searchForm.value.excludeContent.length - 1] || "";
    // if (toValue(searchType1ByContent) === "eq") value1 = searchForm.value.eqContent[searchForm.value.eqContent.length - 1] || "";
    // if (toValue(searchType1ByContent) === "ne") value1 = searchForm.value.neContent[searchForm.value.neContent.length - 1] || "";
    return {
      type0: toValue(searchType0ByContent),
      type1: toValue(searchType1ByContent),
      relation: searchForm.value.contentFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByContent.value = v.type0 as typeof searchType0ByContent extends import("vue").Ref<infer T> ? T : string;
    searchType1ByContent.value = v.type1 as typeof searchType1ByContent extends import("vue").Ref<infer T> ? T : string;
    searchForm.value.contentFilterRelation = v.relation;
    searchForm.value.includeContent = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    searchForm.value.excludeContent = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    // searchForm.value.eqContent = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    // searchForm.value.neContent = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const timeByCreate = computed({
  get: () => (searchForm.value.sendTimeStart && searchForm.value.sendTimeEnd ? { start: searchForm.value.sendTimeStart, end: searchForm.value.sendTimeEnd } : ""),
  set: (v) => {
    searchForm.value.sendTimeStart = (v || {}).start || "";
    searchForm.value.sendTimeEnd = (v || {}).end || "";
  },
});

const shortcuts = ref([
  { flag: "6hour", text: "最近6小时", value: () => [new Date().getTime() - 1000 * 60 * 60 * 6, new Date().getTime()] },
  { flag: "12hour", text: "最近12小时", value: () => [new Date().getTime() - 1000 * 60 * 60 * 12, new Date().getTime()] },
  { flag: "24hour", text: "最近24小时", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24, new Date().getTime()] },
  { flag: "2day", text: "最近2天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 2, new Date().getTime()] },
  { flag: "7day", text: "最近7天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 7, new Date().getTime()] },
  { flag: "30day", text: "最近30天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 30, new Date().getTime()] },
  { flag: "60day", text: "最近60天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 60, new Date().getTime()] },
  { flag: "90day", text: "最近90天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 90, new Date().getTime()] },
  { flag: "180day", text: "最近180天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 180, new Date().getTime()] },
  { flag: "365day", text: "最近365天", value: () => [new Date().getTime() - 1000 * 60 * 60 * 24 * 365, new Date().getTime()] },
]);

const tableData = ref<DataItem[]>([]);
const paging = reactive({
  pageNumber: 1,
  pageSize: 50,
  total: 0,
});

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  // getList();
}

function beforeMount() {}
function mounted() {
  handleSetOperationTime(searchForm.value.shortcuts);
  // nextTick(() => getList());
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

/**
 * @desc 时间选择器快捷方式
 */
function handleSetOperationTime(val) {
  const item = shortcuts.value.find((v) => v.flag === val);
  if (item) {
    const value = item.value();
    searchForm.value.sendTimeStart = Math.floor(value[0]);
    searchForm.value.sendTimeEnd = Math.floor(value[1]);
  } else {
    searchForm.value.sendTimeStart = "";
    searchForm.value.sendTimeEnd = "";
  }

  nextTick(() => getList());
}

function formatterTable(_row, _col, v) {
  switch (_col.property) {
    default:
      return v || "--";
  }
}
function timeZoneSwitching() {
  const timeZone = timeZoneHours.value.find((item) => {
    if (item.zoneId == getUserInfo().zoneId) {
      return item.offsetHours;
    }
  });

  return timeZone ? timeZone.offsetHours * 60 * 60 * 1000 : 0; // 如果未找到，返回 0
}

async function handleQuery() {
  paging.pageNumber = 1;
  await nextTick();
  getList();
}

//获取列表
function getList() {
  loading.value = true;
  getMailList({ pageNumber: paging.pageNumber, pageSize: paging.pageSize, ...searchForm.value }).then((res) => {
    if (res.success) {
      loading.value = false;
      tableData.value = res.data.map((item) => {
        return {
          ...item,
          sendTime: Number(item.sendTime) + timeZoneSwitching(),
        };
      });

      paging.total = Number(res.total);
    } else {
      ElMessage.error(JSON.parse(res.data)?.message);
    }
  });
}

function handleSizeChange(v) {
  paging.pageSize = v;
  paging.pageNumber = 1;
}
function handleCurrentPageChange(v) {
  paging.pageNumber = v;
}

const emailViewVisible = ref<boolean>(false);
const emailViewContent = ref<string>("");

function handleEmailView(row) {
  emailViewContent.value = row.html;
  nextTick(() => (emailViewVisible.value = true));
}

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
