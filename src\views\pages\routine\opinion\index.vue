<template>
  <el-card>
    <div :style="{ margin: '8px 20px 20px' }">
      <el-table v-loading="state.loading" ref="tableRef" :height="height - 64 - 30" :data="state.data" style="width: 100%">
        <el-table-column prop="name" label="用户" />
        <el-table-column prop="phone" label="手机号" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="content" label="反馈意见">
          <template #default="{ row }">
            <div v-html="row.content"></div>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="date" label="附近"  /> -->
        <el-table-column prop="createdTime" label="提出日期">
          <template #default="{ row }">{{ moment(row.updatedTime, "x").format("yyyy-MM-DD HH:mm:ss") }}</template>
        </el-table-column>
        <!-- <el-table-column prop="date" label="Date"  /> -->
        <!-- <TableColumn type="enum" v-model:filtered-value="state.search.status" @filter-change="handleQuery()" :filters="statusOptions.map((v) => ({ ...v, text: v.label }))" show-filter  prop="status" label="解决状态" :min-width="120"> </TableColumn> -->
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button type="primary" @click="handleCreateOpinion(row)" link>查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination v-show="state.total" v-model:current-page="state.page" v-model:page-size="state.size" :page-sizes="state.sizes" :total="state.total" layout="->, total, sizes, prev, pager, next, jumper" small @size-change="handleStateRefresh()" @current-change="handleStateRefresh()" />
    </div>
  </el-card>
  <opinion ref="opinionCreateRef" title="意见反馈" />
</template>

<script setup lang="ts" name="personnel/business">
import { ref, reactive, computed, watch, inject, h, getCurrentInstance, onBeforeUnmount, onMounted } from "vue";
// import { useI18n } from "vue-i18n";
import { sizes } from "@/utils/common";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */

import opinion from "./dialog.vue";
// import { formatterDate } from "@/utils/date";

// Ui
import { ElMessage } from "element-plus";
import TableColumn from "@/components/tableColumn/TableColumn.vue";
// eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
import { More, Refresh, Plus, Edit, Delete } from "@element-plus/icons-vue";

// Api
import { getOpinionList as getData, getOpinionDetail } from "@/views/pages/apis/opinion";
import type { LoggerItem as DataItem } from "@/api/system";
import moment from "moment";

const { proxy } = getCurrentInstance()!;

onMounted(() => {
  if (proxy?.eventBus) {
    proxy.eventBus.on("opinionCreate", () => querysItem({}));
  }
});

onBeforeUnmount(() => {
  if (proxy?.eventBus) {
    proxy.eventBus.off("opinionCreate");
  }
});

const state = reactive<StateData<DataItem>>({
  loading: false,

  data: [],
  page: 1,
  size: 50,
  sizes,
  total: 0,
  search: {
    status: "",
  },
});
const statusOptions: { label: string; value: string }[] = [
  { label: "待规划", value: "WAIT_PLAN" },
  { label: "开发中", value: "DEVELOPING" },
  { label: "待发布", value: "WAIT_PUBLISH" },
  { label: "已解决", value: "SOLVED" },
  { label: "不采用", value: "NOT_ADOPTED" },
];
const publicParams = computed<Record<string, unknown>>(() => ({}));

async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  let controller = new AbortController();
  const response = getData({
    pageNumber: state.page,
    pageSize: state.size,
    sort: ["createdTime,desc"],
    status: state.search.status,
  });
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    const { success, message, data, page: page = 1, size: size = 10, total: total = 0 } = await response;
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);
      state.data = [...data];
      // return data instanceof Array ? data : [];
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(50);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}

async function handleQuery() {
  try {
    state.loading = true;
    // await nextTick();
    await querysItem({});
  } catch (error) {
    if (error instanceof Error) {
      const message = error.message;
      await new Promise((resolve) => ElMessage.error({ message, onClose: () => resolve(null) }));
    }
  } finally {
    state.loading = false;
  }
}
/*********************************************************/

// const { t } = useI18n();

// interface Props {
//   width: number;
//   height: number;
// }
// const props = withDefaults(defineProps<Props>(), {});

const width = inject<import("vue").Ref<number>>("width", ref(100));
const height = inject<import("vue").Ref<number>>("height", ref(100));

interface StateData<T> {
  loading: boolean;

  // column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
  search: {
    status: "";
  };
}

watch<typeof publicParams, true>(
  publicParams,
  async function () {
    if (state.loading) return;
    handleStateRefresh();
  },
  { immediate: true, flush: "post" }
);

async function handleStateRefresh() {
  if (state.loading) return;
  await querysItem({});
  state.loading = true;
  // // console.log(state);
  // state.data.splice(0, state.data.length, ...(await querysItem({})));
  state.loading = false;
}

const opinionCreateRef = ref<InstanceType<typeof opinion>>();
function handleCreateOpinion(item: any) {
  if (!opinionCreateRef.value) return false;
  const params = {};
  // // console.log(item);
  getOpinionDetail({ id: item.id }).then((res) => {
    // // console.log(res);
    if (res.success) {
      opinionCreateRef.value.open({ disabled: true, ...res.data }, async (returnForm: Record<string, unknown>) => {});
    } else {
      //
    }
  });

  proxy?.eventBus && proxy.eventBus.emit("opinionCreate");
}
</script>

<style lang="scss" scoped></style>
