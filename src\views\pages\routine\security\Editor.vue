<template>
  <!-- 对话框表单 -->
  <el-dialog v-model="data.visible" :close-on-click-modal="false" draggable :width="`${width}px`" :before-close="handleCancel">
    <template #header>
      <div class="title">{{ editorType[$params["#TYPE"]] }}{{ props.title }}</div>
    </template>
    <template #default>
      <el-scrollbar ref="contentRef" :height="height">
        <FormModel ref="formRef" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @resize.once="handleSize" @submit="handleFinish">
          <template v-if="[EditorType.Add, EditorType.Mod].includes($params['#TYPE'] || '')">
            <!--  -->
            <FormItem :span="24" :label="`${props.title}名称`" tooltip="" prop="title" :rules="[buildValidatorData({ name: 'required', title: `${props.title}名称` })]">
              <el-input v-model="form.title" type="text" clearable :placeholder="t('glob.Please input field', { field: `${props.title}名称` })"></el-input>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="24" :label="`${props.title}类型`" prop="type" :tooltip="`${props.title}类型，创建后不可更改！`" :rules="[buildValidatorData({ name: 'required', title: `${props.title}类型` })]">
              <el-radio-group v-model="form.type" :disabled="$params['#TYPE'] !== EditorType.Add">
                <el-radio v-for="item in appTypeOption" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="24" :label="`${props.title}终端类型`" prop="terminal" :tooltip="``" :rules="[buildValidatorData({ name: 'required', title: `${props.title}终端类型` })]">
              <el-radio-group v-model="form.terminal" :disabled="$params['#TYPE'] !== EditorType.Add">
                <el-radio v-for="item in appTerminalOption" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </FormItem>
            <!--  -->
            <!--  -->
            <!--  -->
            <FormItem :span="24" :label="`${props.title}备注`" tooltip="" prop="note" :rules="[]">
              <el-input v-model="form.note" type="text" clearable :placeholder="t('glob.Please input field', { field: `${props.title}备注` })"></el-input>
            </FormItem>
            <!--  -->
            <FormGroup :span="24" :label="(appTypeOption.find(({ value }) => value === form.type) || {}).label || '未选择类型'" tooltip="">
              <template #default="{ label }">
                <template v-if="!form.type">
                  <el-col :span="24"><el-empty description="请先选择菜单类型"></el-empty></el-col>
                </template>
                <template v-else-if="form.type === appType.DIR">
                  <FormItem :span="24" :label="`${label}主题`" tooltip="请选择主题布局模式" prop="theme" :rules="[]">
                    <ThemeEditor v-model="form.theme"></ThemeEditor>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}图标`" tooltip="" prop="icon" :rules="[]">
                    <IconSelector v-model="form.icon"></IconSelector>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}路径`" tooltip="" prop="path" :rules="[buildValidatorData({ name: 'required', title: `${label}路径` })]">
                    <el-input v-model="form.path" type="text" clearable :placeholder="t('glob.Please input field', { field: `${label}路径` })"></el-input>
                  </FormItem>
                </template>
                <template v-else-if="form.type === appType.MENU">
                  <FormItem :span="24" :label="`${label}主题`" tooltip="请选择主题布局模式" prop="theme" :rules="[]">
                    <ThemeEditor v-model="form.theme"></ThemeEditor>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}图标`" tooltip="" prop="icon" :rules="[]">
                    <IconSelector v-model="form.icon"></IconSelector>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}路径`" tooltip="" prop="path" :rules="[buildValidatorData({ name: 'required', title: `${label}路径` })]">
                    <el-input v-model="form.path" type="text" clearable :placeholder="t('glob.Please input field', { field: `${label}路径` })"></el-input>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}页面`" tooltip="页面文件位置，基于`views/pages`的绝对路径" prop="component" :rules="[buildValidatorData({ name: 'required', title: `${label}页面` })]">
                    <el-input v-model="form.component" type="text" clearable :placeholder="t('glob.Please input field', { field: `${label}页面` })"></el-input>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}缓存`" :tooltip="`${props.title}缓存部分频繁切换的页面，可有效提高加载速度`" prop="keepalive" :rules="[]">
                    <el-switch v-model="form.keepalive" :active-value="true" :active-text="t('glob.Enable')" :inactive-value="false" :inactive-text="t('glob.Disable')"></el-switch>
                  </FormItem>
                </template>
                <template v-else-if="form.type === appType.MICRO">
                  <FormItem :span="24" :label="`${label}主题`" tooltip="请选择主题布局模式" prop="theme" :rules="[]">
                    <ThemeEditor v-model="form.theme"></ThemeEditor>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}图标`" tooltip="" prop="icon" :rules="[]">
                    <IconSelector v-model="form.icon"></IconSelector>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}路径`" tooltip="" prop="path" :rules="[buildValidatorData({ name: 'required', title: `${label}路径` })]">
                    <el-input v-model="form.path" type="text" clearable :placeholder="t('glob.Please input field', { field: `${label}路径` })"></el-input>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label} URL`" tooltip="" prop="url" :rules="[buildValidatorData({ name: 'required', title: `${label} URL` })]">
                    <el-input v-model="form.url" type="text" clearable :placeholder="t('glob.Please input field', { field: `${label} URL` })"></el-input>
                  </FormItem>
                </template>
                <template v-else-if="form.type === appType.LINK">
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}图标`" tooltip="" prop="icon" :rules="[]">
                    <IconSelector v-model="form.icon"></IconSelector>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label} URL`" tooltip="" prop="url" :rules="[buildValidatorData({ name: 'required', title: `${label} URL` })]">
                    <el-input v-model="form.url" type="text" clearable :placeholder="t('glob.Please input field', { field: `${label} URL` })"></el-input>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}打开方式`" :tooltip="``" prop="keepalive" :rules="[]">
                    <el-switch v-model="form.keepalive" :active-value="true" active-text="Blank" :inactive-value="false" inactive-text="Self"></el-switch>
                  </FormItem>
                </template>
                <template v-else-if="form.type === appType.ROUTE">
                  <FormItem :span="24" :label="`${label}主题`" tooltip="请选择主题布局模式" prop="theme" :rules="[]">
                    <ThemeEditor v-model="form.theme"></ThemeEditor>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}路径`" tooltip="" prop="path" :rules="[buildValidatorData({ name: 'required', title: `${label}路径` })]">
                    <el-input v-model="form.path" type="text" clearable :placeholder="t('glob.Please input field', { field: `${label}路径` })"></el-input>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}页面`" tooltip="页面文件位置，基于`views/pages`的绝对路径" prop="component" :rules="[buildValidatorData({ name: 'required', title: `${label}页面` })]">
                    <el-input v-model="form.component" type="text" clearable :placeholder="t('glob.Please input field', { field: `${label}页面` })"></el-input>
                  </FormItem>
                  <FormItem :span="width > 600 ? 12 : 24" :label="`${label}缓存`" :tooltip="`${props.title}缓存部分频繁切换的页面，可有效提高加载速度`" prop="keepalive" :rules="[]">
                    <el-switch v-model="form.keepalive" :active-value="true" :active-text="t('glob.Enable')" :inactive-value="false" :inactive-text="t('glob.Disable')"></el-switch>
                  </FormItem>
                </template>
                <template v-else-if="form.type === appType.BUTTON">
                  <el-col :span="24">
                    <el-empty description="未开发！！！"></el-empty>
                  </el-col>
                </template>
              </template>
            </FormGroup>
            <!--  -->
          </template>
        </FormModel>
      </el-scrollbar>
    </template>
    <template #footer>
      <div>
        <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
        <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
        <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      </div>
      <div class="zoom-handle" @mousedown.self="handleZoom">
        <svg style="display: block; width: 60%; height: 60%; transform: translate(-25%, -25%); fill: currentColor; pointer-events: none" viewBox="0 0 1024 1024">
          <path d="M319.20128 974.56128L348.16 1003.52l655.36-655.36-28.95872-28.95872-655.36 655.36zM675.84 1003.52l327.68-327.68-28.95872-28.95872-327.68 327.68L675.84 1003.52z" fill="#000000"></path>
        </svg>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="PlatformEditor">
import { reactive, ref, nextTick, h, readonly, provide } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { buildValidatorData } from "@/utils/validate";
import { ElForm, ElMessage, ElMessageBox } from "element-plus";
import { EditorType, editorType } from "@/views/common/interface";
import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";
import IconSelector from "@/components/formItem/iconSelector.vue";
import ThemeEditor from "@/components/formItem/ThemeEditor.vue";
import { type NavItem as ItemData, appType, appTheme, appTypeOption, appTerminal, appTerminalOption } from "@/api/application";
import { TypeHelper } from "@/utils/type";

type Item = Omit<ItemData, "createdTime" | "updatedTime" | "children" | "query" | "params" | "hash" | "pattern" | "names" | "config" | "version">;

interface Props {
  title: string;
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  labelWidth: 130,
});

const formRef = ref<InstanceType<typeof ElForm>>();

const { t } = useI18n();

// const sizeRef = ref<HTMLDivElement>();
const width = ref(0);
const height = ref(0);

interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve: ((value: Item) => void) | undefined;
  reject: ((value: Item) => void) | undefined;
  callback: ((form: Item) => Promise<boolean>) | undefined;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item> & { "#TYPE": EditorType; [key: string]: unknown }>({ "#TYPE": EditorType.Cat });

type DefaultForm<T> = { [P in keyof T]: { value: T[P]; test: (v: any) => v is T[P]; transfer: (fv: any, ov: T[P]) => T[P] } };
const defaultForm = readonly<DefaultForm<Required<Item>>>({
  id: { value: "", ...TypeHelper.string },
  rootId: { value: "", ...TypeHelper.string },
  parentId: { value: "", ...TypeHelper.string },
  name: { value: "", ...TypeHelper.string },
  title: { value: "", ...TypeHelper.string },
  path: { value: "/", ...TypeHelper.string },
  url: { value: "", ...TypeHelper.string },
  order: { value: 0, ...TypeHelper.number },
  icon: { value: "", ...TypeHelper.string },
  theme: { value: appTheme.BASE, test: (v: any): v is appTheme => new RegExp(`^${Object.keys(appTheme).join("|")}$`, "g").test(v), transfer: (_, v) => v },
  type: { value: appType.DIR, test: (v: any): v is appType => new RegExp(`^${Object.keys(appType).join("|")}$`, "g").test(v), transfer: (_, v) => v },
  enabled: { value: true, ...TypeHelper.boolean },
  permission: { value: [], ...TypeHelper.array },
  component: { value: "", ...TypeHelper.string },
  keepalive: { value: false, ...TypeHelper.boolean },
  terminal: { value: appTerminal.WEB, test: (v: any): v is appTerminal => new RegExp(`^${Object.keys(appTerminal).join("|")}$`, "g").test(v), transfer: (_, v) => v },
  note: { value: "", ...TypeHelper.string },
});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  type DefaultForm = typeof defaultForm;
  const _form = (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], util.value as never)) });
    },
    cloneDeep(form) as Required<Item>
  );
  return _form;
}
async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => {
      if (typeof formRef.value?.validate === "function") formRef.value.validate(resolve);
      else resolve(false);
    });
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") {
      const valid = await data.callback($form);
      if (!valid) throw new Error("Error");
    }

    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  formRef.value && formRef.value.clearValidate();
  const _form = await getForm($params.value);
  form.value = { ..._form };
  try {
    formRef.value && formRef.value.clearValidate();
    // await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }
  data.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

function handleZoom($event: MouseEvent) {
  const w = width.value;
  const h = height.value;
  ($event.target as HTMLElement).ownerDocument.onmousemove = (e: MouseEvent) => {
    e.preventDefault();
    if (w + (e.clientX - $event.clientX) * 2 < document.body.clientWidth - 200) width.value = w + (e.clientX - $event.clientX) * 2 > 360 ? w + (e.clientX - $event.clientX) * 2 : 360;
    else width.value = document.body.clientWidth - 200;
    if (h + (e.clientY - $event.clientY) * 1 < document.body.clientHeight - 260 - document.body.clientHeight * 0.15) height.value = h + (e.clientY - $event.clientY) * 1 > 24 ? h + (e.clientY - $event.clientY) * 1 : 24;
    else document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  };
  ($event.target as HTMLElement).ownerDocument.onmouseup = (e: MouseEvent) => {
    (e.target as HTMLElement).ownerDocument.onmousemove = null;
    (e.target as HTMLElement).ownerDocument.onmouseup = null;
  };
}

function handleSize(size: { width: number; height: number }) {
  const maxHeight = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
  const formHeight = size.height || 24;
  height.value = Math.min(formHeight, maxHeight);
}

provide("#PARAMS", $params);
provide("#WIDTH", width);

defineExpose({
  close: handleCancel,
  open(params: Partial<ItemData> & { "#TYPE": EditorType; [key: string]: unknown }, callback?: (form: Item) => Promise<boolean>) {
    switch (params["#TYPE"]) {
      case EditorType.Cat:
      case EditorType.Add:
      case EditorType.Mod: {
        if (data.visible) {
          return new Promise((resolve) => {
            ElMessage.warning("先关闭其他弹窗再重试！");
            resolve(params);
          });
        } else {
          $params.value = { ...params, config: JSON.parse(params.config || "{}") };
          data.visible = true;
          data.loading = true;
          data.submitLoading = true;
          data.callback = callback;
          return new Promise((resolve, reject) => {
            data.resolve = resolve;
            data.reject = reject;
            nextTick(async () => {
              width.value = document.body.clientWidth / 2;
              await nextTick();
              // if (formRef.value) {
              //   const maxHeight = document.body.clientHeight - 260 - document.body.clientHeight * 0.15;
              //   const formHeight = (formRef.value.$el as HTMLFormElement).clientHeight || 24;
              //   height.value = Math.max(formHeight, maxHeight);
              // }
              // if ([EditorType.Cat, EditorType.Mod].includes(params["#TYPE"])) {
              //   try {
              //     if (!params.id) throw new Error("找不到用户信息！");
              //     const { success, message, data } = await getUserById({ id: params.id });
              //     if (success) {
              //       Object.assign($params.value, data);
              //     } else Object.assign(new Error(message), { success, data });
              //   } catch (error) {
              //     await nextTick();
              //     if (error instanceof Error) ElMessage.error(error.message);
              //     return handleCancel();
              //     /*  */
              //   }
              // }
              await handleReset();
              data.loading = false;
              data.submitLoading = false;
            });
          });
        }
      }
      case EditorType.Del: {
        const option = reactive<{ message: string; valid: boolean; [key: string]: unknown }>({
          message: (params["#MESSAGE"] || `确认${editorType[params["#TYPE"]]}`) as string,
          valid: true,
        });
        return new Promise((resolve, reject) => {
          ElMessageBox({
            title: `${editorType[params["#TYPE"]]}${props.title}`,
            message() {
              return h("span", {}, [h("span", {}, option.message), h("span", { style: { margin: "0 3px", color: "var(--el-color-danger)" } }, params.name || "此"), option.valid ? h("span", {}, `${props.title}？`) : h("span", {}, `${props.title}删除失败！`)]);
            },
            type: "info",
            showCancelButton: true,
            showConfirmButton: true,
            cancelButtonText: t("glob.Cancel"),
            confirmButtonText: t("glob.delete"),
            distinguishCancelAndClose: true,
            draggable: true,
            async beforeClose(action, instance, done) {
              if (action === "confirm") {
                instance.confirmButtonLoading = true;
                try {
                  if (typeof callback === "function") option.valid = await callback(await getForm(params));
                  if (!option.valid) throw new Error("Error");
                  resolve(params);
                  done();
                } catch (error) {
                  option.message = "";
                  option.valid = false;
                  instance.showConfirmButton = false;
                  instance.type = "error";
                } finally {
                  instance.confirmButtonLoading = false;
                }
              } else {
                reject(params);
                done();
              }
            },
          })
            .then(async () => {})
            .catch(() => {
              reject(params);
            });
        });
      }
    }
  },
});
</script>

<style scoped lang="scss"></style>
