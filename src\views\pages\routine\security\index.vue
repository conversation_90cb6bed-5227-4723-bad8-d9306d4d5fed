<template>
  <el-scrollbar :height="height">
    <el-card>
      <div class="md:tw-col-span-2">
        <div class="tw-w-full tw-px-4 sm:tw-px-0">
          <h3 class="tw-text-base tw-font-semibold tw-leading-6 dark:tw-text-white">{{ $t("personalInformation.Security Setting") }}</h3>
          <p class="tw-mt-1 tw-text-sm tw-text-gray-600 dark:tw-text-slate-300">{{ $t("personalInformation.verifications") }}</p>
        </div>
      </div>
      <el-row>
        <el-col>
          <div class="">
            <div :class="userInfoClassNames">
              <div class="tw-text-base dark:tw-text-white">{{ $t("personalInformation.Account password") }}</div>
              <div class="tw-flex tw-justify-between">
                <div class="tw-inline-block tw-text-gray-400">
                  {{ $t("personalInformation.change your password")
                  }}<!--当前密码强度: <span>高中低</span>-->
                </div>
                <el-button link :disabled="!allowUserChange" type="primary" @click="() => bindPwdWizard(mfaMethods)">{{ $t("personalInformation.Change pwd") }}</el-button>
              </div>
            </div>
            <div :class="userInfoClassNames">
              <div class="tw-text-base dark:tw-text-white">{{ $t("personalInformation.Dynamic Key") }}</div>
              <div class="tw-flex tw-justify-between">
                <div class="tw-inline-block tw-text-gray-400">{{ mfaMethods.includes(MFAMethod.TOTP) ? $t("personalInformation.has") : $t("personalInformation.not") }}{{ $t("personalInformation.Bind dynamic key") }}</div>
                <div>
                  <template v-if="mfaMethods.includes(MFAMethod.TOTP)">
                    <el-button type="warning" @click="$nextTick(() => Unbind())">{{ $t("personalInformation.unBind dynamic key") }}</el-button>
                  </template>
                  <template v-else>
                    <el-button link type="primary" @click="$nextTick(() => ($refs['bindMFAWizardRef'] as InstanceType<typeof BindMFAWizard>).open())">{{ $t("personalInformation.Bind dynamic key") }}</el-button>
                  </template>
                  <BindMFAWizard ref="bindMFAWizardRef" :mfaMethods="mfaMethods" title="动态密钥" @done="handleStateRefresh()" />
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </el-scrollbar>
</template>

<script setup lang="ts" name="routine/userInfo/safe">
import { inject, ref, onMounted } from "vue";
import type { Ref } from "vue";
import { getMFAMethods, MFAMethod } from "@/api/system";
import BindMFAWizard from "../bindMFAWizard.vue";
import { bindMFAWizard } from "../bindWizard";

import { bindPasswordWizard as bindPwdWizard } from "../bindWizard";
import { getTenantPasswordStrategyMsg, getUserPasswordStrategyMsg } from "@/api/personnel";

// const width = inject<Ref<number>>("width", ref(100));
const height = inject<Ref<number>>("height", ref(100));
const userInfoClassNames = ref<string>("tw-border-b tw-border-solid tw-border-gray-200 tw-p-2 tw-pt-6");

const mfaMethods = ref<(keyof typeof MFAMethod)[]>([]);
async function handleStateRefresh() {
  const { success, message, data } = await getMFAMethods({});
  if (success) {
    if (data instanceof Array) mfaMethods.value = Array.from(new Set([...data]));
  } else throw Object.assign(new Error(message), { success, data });
}

onMounted(async () => {
  await handleStateRefresh();
});

function Unbind() {
  bindMFAWizard(mfaMethods.value).then((res) => {
    handleStateRefresh();
  });
}
const pwdMinLength = ref(0);
const pwdExpire = ref(false);
const allowUserChange = ref(false);
getpwdMinLength();
function getpwdMinLength() {
  getUserPasswordStrategyMsg({}).then((res) => {
    if (res.success) {
      pwdMinLength.value = Number(res.data.minLength || 0);
      pwdExpire.value = res.data.passwordExpire;
      allowUserChange.value = res.data.allowUserChange;
    }
  });
}
</script>

<style lang="scss" scoped></style>
