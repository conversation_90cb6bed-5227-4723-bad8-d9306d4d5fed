<template>
  <el-card :body-style="{ padding: '20px', height: `${height}px`, width: `${width}px` }">
    <pageTemplate v-model:currentPage="paging.pageNumber" v-model:pageSize="paging.pageSize" :total="paging.total" :height="height - 40" @size-change="getList()" @current-change="getList()">
      <!-- <template #left>
        <el-input v-model="ServiceSearch" placeholder="请输入告警降级配置名称" @keyup.enter="getSlaList()">
          <template #append>
            <el-button :icon="Search" @click="getSlaList()" />
          </template>
        </el-input>
      </template> -->
      <template #right>
        <span v-if="userInfo.hasPermission(PERMISSION.group574133521883332608.create)" class="">
          <el-button type="primary" :icon="Plus" @click="deviceDialog('add')">{{ $t("glob.add") }}行动策略</el-button>
        </span>
      </template>
      <template #default="{ height: tableHeight }">
        <el-table v-loading="loading" stripe :data="tableData.slice((paging.pageNumber - 1) * paging.pageSize, paging.pageNumber * paging.pageSize)" row-key="id" :height="tableHeight" :expand-row-keys="expandList" style="width: 100%" @expand-change="expandChange">
          <el-table-column type="expand">
            <template #default="{ row, expanded }">
              <strategy v-if="expanded" :detail="row" :width="width - 40" @confirm="update">
                <ModelExpand
                  v-if="row.id && expanded && !row.defaultNote"
                  :key="row.id"
                  :id="row.id"
                  :tenantIds="row.assignedTenantIds"
                  type="globalStrategy"
                  :create="{
                    tenant: !userInfo.hasPermission(PERMISSION.group574133521883332608.auth574134788886102016),
                  }"
                  :viewer="{
                    tenant: false,
                  }"
                  :remove="{
                    tenant: !userInfo.hasPermission(PERMISSION.group574133521883332608.auth574135239907999744),
                  }"
                ></ModelExpand>
              </strategy>
            </template>
          </el-table-column>
          <el-table-column prop="name" :formatter="formatterTable" label="行动策略名称"></el-table-column>
          <el-table-column prop="active" label="是否激活">
            <template #default="scope">
              <span v-if="userInfo.hasPermission(PERMISSION.group574133521883332608.auth574135095519084544)">
                <el-switch v-model="scope.row.active" active-color="#13ce66" @change="updateActive(scope.row)"></el-switch>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="description" :formatter="formatterTable" label="描述"></el-table-column>
          <el-table-column :label="$t('glob.operate')" align="left" fixed="right" :width="120">
            <template #default="{ row }">
              <span v-if="!row.defaultNote && userInfo.hasPermission(PERMISSION.group574133521883332608.auth574135095519084544)">
                <el-link type="primary" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="deviceDialog('edit', row)">{{ $t("glob.edit") }}</el-link>
              </span>
              <span v-if="!row.defaultNote && userInfo.hasPermission(PERMISSION.group574133521883332608.remove)">
                <el-link type="danger" :underline="false" class="tw-mx-[2.5px] tw-align-middle" @click="delItem(row)">{{ $t("glob.delete") }}</el-link>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </pageTemplate>
  </el-card>
  <Editor ref="editorRef" title="行动策略"></Editor>
  <supportNotesCreate :dialog="dialog" ref="supplier" @dialogClose="dialogClose"></supportNotesCreate>
</template>

<script setup lang="ts" generic="T extends object">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";
import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete, Download } from "@element-plus/icons-vue";

import Editor from "./Editor.vue";

import assignContacts from "@/components/bindContacts/assignContacts";
import supportNotesCreate from "./supportNotesCreate.vue";
import ModelExpand from "@/views/pages/modelExpand/Model.vue";
import strategy from "./strategy.vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { useTemplateRefsList } from "@vueuse/core";
import getUserInfo from "@/utils/getUserInfo";
import { ElMessage, ElMessageBox } from "element-plus";
import pageTemplate from "@/components/pageTemplate.vue";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";
import { getSupport_notesListGlobal, deleteSupport_notesGlobal, editSupport_notesGlobal, Support_notesSetHours, updateSupport_notesActive, type SlaConfigList as DataItem } from "@/views/pages/apis/supportNotes";
import { getAlarmClassificationList } from "@/views/pages/apis/alarmClassification";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
const ctx = getCurrentInstance()!;
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const i18n = useI18n({ useScope: "local" });
const route = useRoute();
const router = useRouter();
defineOptions({ name: "deviceGroupManage" });
const editorRef = ref<InstanceType<typeof Editor>>();
const assignContactsRef = ref<InstanceType<typeof assignContacts>>();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */
const width = inject("width", ref(0));
const height = inject("height", ref(0));

const siteConfig = useSiteConfig();
const userInfo = getUserInfo();

const refs = useTemplateRefsList();

// interface Props {
//   data?: T;
// }
// const props = withDefaults(defineProps<Props>(), { data: () => ({} as T) });

// interface Emits {
//   (name: "change", event: T): void;
// }
// const emits = defineEmits<Emits>();

// const modelValue = defineModel("modelValue");

// const assignContacts = ref<InstanceType<typeof AssignContacts>>();
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);

// 搜索关键字
const searchForm = ref<Record<string, any>>({});
const tableData = ref<DataItem[]>([]);
const expandList = ref<string[]>([]);
const paging = reactive({
  pageNumber: 1,
  pageSize: 10,
  total: 0,
});

const allRegion = ref([]);
const allRegionByPage = ref([]);
const allRegionSelect = ref([]);

const deviceList = ref([]);
const deviceGroupList = ref([]);
const deviceTypeList = ref([]);
const dialog = ref(false);
const options = ref({});

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {
  // getList();
}
function beforeMount() {}
function mounted() {
  loading.value = true;
  getList();
  getAlarmClassificationList({})
    .then(({ success, message, data }) => {
      if (!success) throw new Error(message);
      options.value = data.reduce((p, c) => ({ ...p, [c.id]: c.name }), {});
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    })
    .finally(() => {
      loading.value = false;
    });
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

function formatterTable(_row, _col, v) {
  switch (_col.property) {
    default:
      return v || "--";
  }
}
function expandChange(row, column) {
  row.locations = [];
  row.regions = [];
  row.resources = [];
}
function updateActive(val) {
  // console.log(val, 444);
  updateSupport_notesActive({
    id: val.id,
    value: val.active,
  }).then((res) => {
    if (res.success) {
      ElMessage.success("操作成功");
      getList();
    } else {
      ElMessage.error(JSON.parse(res.data)?.message);
    }
  });
}

//修改表格展开行数据
function update(val) {
  // Support_notesSetHours({
  //   id: val.id,
  //   autoTimeZone: val.activeConfig.useAutoTimeZone ? getUserInfo()?.zoneId : "",
  // });
  let obj = { ...val };
  if (val.activeConfig.timeZone == "自动时区") {
    obj.activeConfig.timeZone = getUserInfo()?.zoneId;
  }
  editSupport_notesGlobal({ ...obj })
    .then((res) => {
      if (res.success) {
        ElMessage.success("操作成功");
        // getList();
      } else {
        ElMessage.error(JSON.parse(res.data)?.message);
      }
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
}
//获取列表
function getList() {
  getSupport_notesListGlobal({}).then((res) => {
    if (res.success) {
      tableData.value = [...res.data];
      paging.total = res.data.length;
    } else {
      ElMessage.error(JSON.parse(res.data)?.message);
    }
  });
}
function deviceDialog(type, row) {
  ctx.refs.supplier.type = type;
  if (type === "add") {
    ctx.refs.supplier.title = "新增行动策略";
  } else {
    ctx.refs.supplier.form = {
      name: row.name,
      description: row.description,
      inactiveNote: row.inactiveNote,
      activeNote: row.activeNote,
      id: row.id,
    };
    ctx.refs.supplier.title = "编辑行动策略";
  }
  dialog.value = true;
}
function handleSizeChange(v) {
  paging.pageSize = v;
  paging.pageNumber = 1;
}
function handleCurrentPageChange(v) {
  paging.pageNumber = v;
}
function dialogClose(bool) {
  dialog.value = bool;
  getList();
}
function delItem(row, index) {
  ElMessageBox.confirm(`确定删除${row.name}?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // // tableData.value.length = tableData.value.length - 1;
      deleteSupport_notesGlobal({
        id: row.id,
      })
        .then((res) => {
          if (res.success) {
            ElMessage.success("删除成功");
            if (tableData.value.slice((paging.pageNumber - 1) * paging.pageSize, paging.pageNumber * paging.pageSize).length == 0) {
              paging.pageNumber = 1;
            }
            getList();
          } else {
            ElMessage.error(JSON.parse(res.data)?.message);
          }
        })
        .catch((e) => {
          // console.log(e);
          if (e instanceof Error) ElMessage.error(e.message);
        });
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
}
/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
