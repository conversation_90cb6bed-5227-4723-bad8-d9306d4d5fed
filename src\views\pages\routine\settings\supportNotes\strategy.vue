<template>
  <el-form :model="form" :style="{ marginTop: '10px', padding: '0 10px' }" label-position="left" label-width="160px" :rules="rules" ref="roleFormRef" label-suffix=":">
    <el-row :gutter="20">
      <!-- <el-col :span="12">
        <el-form-item label="行动策略名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入行动策略名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="行动策略描述">
          <el-input type="textarea" v-model="form.description" placeholder="请输入行动策略描述" />
        </el-form-item>
      </el-col> -->
      <el-col :span="12">
        <el-form-item label="工作时间">
          <!--  -->
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="选择时区">
          <el-select v-model="form.activeConfig.timeZone" filterable placeholder="请选择" class="tw-w-full">
            <el-option v-for="item in timeZone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"> </el-option>
          </el-select>
        </el-form-item>
      </el-col>

      <div style="width: 100%" class="support-table-content" ref="tableContentRef">
        <el-table stripe border :data="form.activeConfig.activeHours" style="width: 100%" @header-click="(column: Record<string, any>, $event: Event) => handleClick({ column, evene: $event })">
          <el-table-column align="left" width="80" prop="week" fixed="left">
            <template #default="scope">
              <div @click="handleSelectTime('all', scope.$index, scope.row)">
                {{ scope.row.weekDay == 1 ? "周一" : scope.row.weekDay == 2 ? "周二" : scope.row.weekDay == 3 ? "周三" : scope.row.weekDay == 4 ? "周四" : scope.row.weekDay == 5 ? "周五" : scope.row.weekDay == 6 ? "周六" : "周日" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column :width="tableWidth" align="left" v-for="(item, key) in 24" :key="`h-${key}`" :label="String(key)" class-name="tw-py-0">
            <template #header="{ column }">
              <div class="tw-my-[12px]">{{ column.label }}</div>
            </template>
            <template #default="scope">
              <div class="tw-mx-[-12px] tw-flex tw-h-[50px] tw-items-center tw-justify-center tw-text-[20px]" @click="handleSelectTime(key, scope.$index, scope.row)" style="height: 100%" :class="scope.row.hours && scope.row.hours.length && scope.row.hours.includes(key) ? 'sun' : 'moon'">
                <el-icon v-if="scope.row.hours && scope.row.hours.length && scope.row.hours.includes(key)" class="tw-text-white"><Sunny></Sunny></el-icon>
                <el-icon v-else class="tw-text-black"><Moon></Moon></el-icon>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-col :span="12" class="tw-pb-[18px]">
        <el-form-item label="工作时间监控策略">
          <el-icon><Sunny></Sunny></el-icon>
        </el-form-item>
        <div class="tw-flex tw-min-h-[250px] tw-flex-col">
          <QuillEditor theme="snow" class="tw-h-[250px] tw-w-full" style="flex: 1" v-model:content="form.activeNote" contentType="html" :modules="modules" toolbar="full"></QuillEditor>
        </div>
      </el-col>
      <el-col :span="12" class="tw-pb-[18px]">
        <el-form-item label="非工作时间监控策略">
          <el-icon><Moon></Moon></el-icon>
        </el-form-item>
        <div class="second tw-flex tw-min-h-[250px] tw-flex-col">
          <QuillEditor theme="snow" class="tw-h-[250px] tw-w-full" style="flex: 1" v-model:content="form.inactiveNote" contentType="html" :modules="modules" toolbar="full"></QuillEditor>
        </div>
        <!-- <Editor theme="snow" ref="inActiveNote" @getValue="getEditorInactiveNote" qlEditorHeight="250"></Editor> -->
      </el-col>
      <el-col :span="24" class="tw-pb-[18px]" style="text-align: center">
        <el-button type="primary" @click="CreateSlaDownConfig" :disabled="form.defaultNote"> 保 存</el-button>

        <!-- <el-button type="primary" @click="CreateSlaDownConfig" v-else :disabled="!userInfo.hasPermission(PERMISSION.group512890628258922496.editor)"> 保 存</el-button> -->
      </el-col>
      <el-col :span="24">
        <slot :viewer="{ contact: false }"></slot>
        <!-- <bindDevice ref="bindDeviceRef" @delete="delRelation" @confirm="bindRelation" :id="form.id" :list="[areaList, locationList, devicesList]" :title="['分配区域', '分配场所', '分配设备']"></bindDevice> -->
      </el-col>
      <!-- <el-form-item>
        <bindDevice :id="'1'" :list="[]" :title="'分配场所'"></bindDevice>
      </el-form-item>
      <el-form-item>
        <bindDevice :id="'2'" :list="[]" :title="'分配设备'"></bindDevice>
      </el-form-item> -->
      <!-- <el-form-item style="width: 100%"> -->

      <!-- </el-form-item> -->
    </el-row>
  </el-form>
</template>

<script lang="ts" setup generic="T extends { [key: string]: unknown; id: string }">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";
import { ElMessage, ElMenuItem, ElForm } from "element-plus";
import { QuillEditor } from "@vueup/vue-quill";
import BlotFormatter from "quill-blot-formatter";
import "@vueup/vue-quill/dist/vue-quill.snow.css";
// 注册
import { Sunny, Moon } from "@element-plus/icons-vue";
import { Support_notesRelationArea, Support_notesRelationLocation, Support_notesRelationDevice, Support_notesGlobalRelationTenant, Support_notesDeGlobalRelationTenant } from "@/views/pages/apis/supportNotes";
// import Editor from "@/components/Editor/indexEditor.vue";
import _timeZone from "@/views/pages/common/strategyZone.json";
import bindDevice from "@/components/bindDevice/bindDevice.vue";
// import { getDeviceList } from "@/views/pages/apis/deviceManage";
// import { getRegionsTenantCurrent } from "@/views/pages/apis/regionManage";
// import { getLocationsTenantCurrent } from "@/views/pages/apis/locationManang";
import getUserInfo from "@/utils/getUserInfo";

import { useResizeObserver } from "@vueuse/core";
// import ModelExpand from "@/views/pages/modelExpand/Model.vue";
const userInfo = getUserInfo();

const modules = {
  name: "blotFormatter",
  module: BlotFormatter,
};
interface Emits {
  ($event: "confirm", data: Record<string, any>): any;
}
const emits = defineEmits<Emits>();

interface Props {
  detail: T;
  width: number;
}
const props = withDefaults(defineProps<Props>(), { width: 0, detail: () => ({}) as T });

const roleFormRef = ref<InstanceType<typeof ElForm>>();
// const bindDeviceRef = ref<InstanceType<typeof bindDevice>>();

const id = ref("");
const rules = reactive({
  name: [{ required: true, message: "请输入行动策略名称", trigger: "blur" }],
});
const timeZone = ref(_timeZone);
const basicClassInput = reactive({ width: "25.8vw" }); /* 输入框选择器基本样式 */
const basicClassInputDown = reactive({ width: "20.8vw" }); /* 输入框选择器基本样式 */
const type = ref("");
//工作时间降级
const areaList = ref<Record<"id" | "name" | "detail", string>[]>([]); //区域表格数组
const locationList = ref<Record<"id" | "name" | "detail", string>[]>([]); //场所表格数据
const devicesList = ref<Record<"id" | "name" | "detail", string>[]>([]); //设备列表
const form = reactive<Record<string, any>>({ ...props.detail });
const ctx = getCurrentInstance()!;

const paging = reactive({
  pageNumber: 1,
  pageSize: 10,
  total: 0,
});

const allRegion = ref([]);
const allRegionByPage = ref([]);
const allRegionSelect = ref([]);

const allBool = ref([false, false, false, false, false, false, false]);
const tableContentRef = ref();
const tableWidth = ref(0);
useResizeObserver(tableContentRef, (entries) => {
  const entry = entries[0];
  const { width, height } = entry.contentRect;
  tableWidth.value = ((width - 80) / 24).toFixed(0);
});

const titleConfig = [
  { Choice: ".ql-insertMetric", title: "跳转配置" },
  { Choice: ".ql-bold", title: "加粗" },
  { Choice: ".ql-italic", title: "斜体" },
  { Choice: ".ql-underline", title: "下划线" },
  { Choice: ".ql-header", title: "段落格式" },
  { Choice: ".ql-strike", title: "删除线" },
  { Choice: ".ql-blockquote", title: "块引用" },
  { Choice: ".ql-code", title: "插入代码" },
  { Choice: ".ql-code-block", title: "插入代码段" },
  { Choice: ".ql-font", title: "字体" },
  { Choice: ".ql-size", title: "字体大小" },
  { Choice: '.ql-list[value="ordered"]', title: "编号列表" },
  { Choice: '.ql-list[value="bullet"]', title: "项目列表" },
  { Choice: ".ql-direction", title: "文本方向" },
  { Choice: '.ql-header[value="1"]', title: "h1" },
  { Choice: '.ql-header[value="2"]', title: "h2" },
  { Choice: ".ql-align", title: "对齐方式" },
  { Choice: ".ql-color", title: "字体颜色" },
  { Choice: ".ql-background", title: "背景颜色" },
  { Choice: ".ql-image", title: "图像" },
  { Choice: ".ql-video", title: "视频" },
  { Choice: ".ql-link", title: "添加链接" },
  { Choice: ".ql-formula", title: "插入公式" },
  { Choice: ".ql-clean", title: "清除字体格式" },
  { Choice: '.ql-script[value="sub"]', title: "下标" },
  { Choice: '.ql-script[value="super"]', title: "上标" },
  { Choice: '.ql-indent[value="-1"]', title: "向左缩进" },
  { Choice: '.ql-indent[value="+1"]', title: "向右缩进" },
  { Choice: ".ql-header .ql-picker-label", title: "标题大小" },
  { Choice: '.ql-header .ql-picker-item[data-value="1"]', title: "标题一" },
  { Choice: '.ql-header .ql-picker-item[data-value="2"]', title: "标题二" },
  { Choice: '.ql-header .ql-picker-item[data-value="3"]', title: "标题三" },
  { Choice: '.ql-header .ql-picker-item[data-value="4"]', title: "标题四" },
  { Choice: '.ql-header .ql-picker-item[data-value="5"]', title: "标题五" },
  { Choice: '.ql-header .ql-picker-item[data-value="6"]', title: "标题六" },
  { Choice: ".ql-header .ql-picker-item:last-child", title: "标准" },
  { Choice: '.ql-size .ql-picker-item[data-value="small"]', title: "小号" },
  { Choice: '.ql-size .ql-picker-item[data-value="large"]', title: "大号" },
  { Choice: '.ql-size .ql-picker-item[data-value="huge"]', title: "超大号" },
  { Choice: ".ql-size .ql-picker-item:nth-child(2)", title: "标准" },
  { Choice: ".ql-align .ql-picker-item:first-child", title: "居左对齐" },
  { Choice: '.ql-align .ql-picker-item[data-value="center"]', title: "居中对齐" },
  { Choice: '.ql-align .ql-picker-item[data-value="right"]', title: "居右对齐" },
  { Choice: '.ql-align .ql-picker-item[data-value="justify"]', title: "两端对齐" },
];

onMounted(() => {
  nextTick(() => {
    timeZone.value.unshift({
      zoneId: "自动时区",
      displayName: "自动时区",
    });

    let map = new Map();
    for (let item of timeZone.value) {
      map.set(item.zoneId, item);
    }
    timeZone.value = [...map.values()];
    // bindDeviceRef.value.contacts = [[], [], []];
    // bindDeviceRef.value.contacts = [[], [], []];
    getDetail({ id: props.detail.id });
    console.log(getUserInfo()?.zoneId);
    if (props.detail.activeConfig.timeZone == getUserInfo()?.zoneId) {
      form.activeConfig.timeZone = "自动时区";
    }

    form.activeConfig.activeHours.forEach((item: any) => {
      // // console.log(item);
      if (item.hours.length > 23) {
        allBool.value[item.weekDay - 1] = true;
      } else {
        allBool.value[item.weekDay - 1] = false;
      }
    });
    document.getElementsByClassName("ql-editor")[0].dataset.placeholder = "";
    for (let item of titleConfig) {
      let tip = document.querySelector(".ql-toolbar " + item.Choice);
      let tip1 = document.querySelector(".second " + ".ql-toolbar " + item.Choice);

      if (!tip) continue;
      tip.setAttribute("title", item.title);
      if (item.Choice=='.ql-image' || item.Choice=='.ql-video' || item.Choice=='.ql-link') {
        tip.style.display = "none"; // 或者使用 visibility: "hidden"
      }
      tip1.setAttribute("title", item.title);
    }
  });
});

function getDetail(row: Partial<{ id: string }>) {
  areaList.value = []; //区域表格数组
  locationList.value = []; //场所表格数据
  devicesList.value = []; //设备列表

  // getDeviceList({
  //   pageNumber: 1,
  //   pageSize: 10000,
  //   supportNoteId: row.id,
  // })
  //   .then(({ success, message, data }) => {
  //     if (!success) throw new Error(message);
  //     type Item = typeof devicesList.value;
  //     devicesList.value = data.reduce((p, v) => p.concat({ id: v.id, name: v.name, detail: v.config.ipAddress }), [] as Item);

  //     bindDeviceRef.value.setContacts(2, data);
  //   })
  //   .catch((e) => {
  //     if (e instanceof Error) ElMessage.error(e.message);
  //   });

  // getRegionsTenantCurrent({ supportNoteId: row.id })
  //   .then(({ success, message, data }) => {
  //     if (!success) throw new Error(message);
  //     type Item = typeof areaList.value;
  //     areaList.value = data.reduce((p, v) => p.concat({ id: v.id, name: v.name, detail: v.description || "" }), [] as Item);
  //     bindDeviceRef.value.setContacts(0, data);
  //   })
  //   .catch((e) => {
  //     if (e instanceof Error) ElMessage.error(e.message);
  //   });

  // getLocationsTenantCurrent({ supportNoteId: row.id })
  //   .then(({ success, message, data }) => {
  //     if (!success) throw new Error(message);
  //     type Item = typeof locationList.value;
  //     locationList.value = data.reduce((p, v) => p.concat({ id: v.id, name: v.name, detail: v.description || "" }), [] as Item);
  //     bindDeviceRef.value.setContacts(1, data);
  //   })
  //   .catch((e) => {
  //     if (e instanceof Error) ElMessage.error(e.message);
  //   });
}

//绑定关联关系
async function bindRelation(item: Record<string, any>) {
  try {
    let res: Promise<import("@/api/service/common").Response<any>> | null = null;
    switch (item.type) {
      case "分配客户":
        res = Support_notesGlobalRelationTenant({ ids: item.ids, supportNoteIds: [item.expandId] });
        break;
    }
    if (res) {
      const { success, message } = await res;

      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    getDetail({ id: item.expandId });
  }
}
//删除关联关系
async function delRelation(item: Record<string, any>) {
  try {
    let res: Promise<import("@/api/service/common").Response<any>> | null = null;
    switch (item.type) {
      case "分配区域":
        res = Support_notesDelRelationArea({ ids: item.ids, supportNoteIds: [item.expandId] });
        break;
      case "分配场所":
        res = Support_notesDelRelationLocation({ ids: item.ids, supportNoteIds: [item.expandId] });
        break;
      default:
        res = Support_notesDelRelationDevice({ resourceIds: [item.ids.toString()], id: [item.expandId] });
        break;
    }
    if (res) {
      const { success, message } = await res;
      if (!success) throw new Error(message);
      ElMessage.success("操作成功");
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    getDetail({ id: item.expandId });
  }
}

//覆盖时间列
function handleClick({ column, evene }: { column: Record<string, any>; evene: Event }) {
  let index = Number(column.label);
  const { activeHours } = form.activeConfig;

  let hours = [];
  activeHours.forEach((v) => {
    hours = hours.concat(v.hours);
  });

  const isActive = hours.includes(index) && hours.filter((v) => v === index).length === 7; // 是否激活
  // console.log(isActive);

  activeHours.forEach((v: Record<string, any>, i: number) => {
    let delIndex = v.hours.indexOf(index);
    if (isActive) {
      v.hours.splice(delIndex, 1);
    } else {
      v.hours.push(index);
    }

    v.hours = [...new Set(v.hours.sort((a, b) => a - b))];
  });
}
//覆盖时间
function handleSelectTime(key: string | number, weekIndex: number, row: Record<string, any>) {
  if (key === "all") {
    allBool.value[weekIndex] = !allBool.value[weekIndex];

    let data = [];
    for (let i = 0; i < 24; i++) {
      data.push(i);
    }

    row.hours = [...new Set(data)];

    if (!allBool.value[weekIndex]) {
      row.hours = [];
    }
  } else {
    const index = row.hours.indexOf(key);
    if (index == -1) {
      row.hours.push(key);
    } else row.hours.splice(index, 1);
  }
}

//新增服务
function CreateSlaDownConfig() {
  // build();

  if (!roleFormRef.value) return;
  roleFormRef.value.validate((valid) => {
    if (valid) {
      form.activeConfig.timeZone != "自动时区" ? (form.activeConfig.useAutoTimeZone = false) : (form.activeConfig.useAutoTimeZone = true);
      let data = {
        name: form.name,
        description: form.description,
        activeConfig: form.activeConfig,
        activeNote: form.activeNote,
        inactiveNote: form.inactiveNote,
        id: form.id,
      };
      // if (effectTimeCfg.value.timeZone === "自动时区") {
      //   effectTimeCfg.value.useCustomerTimeZone = true;
      //   effectTimeCfg.value.useDeviceTimeZone = false;
      // }
      emits("confirm", data);

      setTimeout(() => {
        if (props.detail.activeConfig.timeZone == getUserInfo()?.zoneId) {
          form.activeConfig.timeZone = "自动时区";
        }
      });
    } else {
      ElMessage.error("请输入行动策略名称");
    }
  });
}
</script>

<style lang="scss" scoped>
:deep(.ql-blank) {
  height: 160px;
}
.work-editor {
  padding: 0 20px;
  box-sizing: border-box;
}
.modules-item {
  .modules-title {
    color: rgba(32, 128, 255, 1);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}
.sun {
  background: rgb(26, 190, 107);
  :deep() .elstyle-button--text {
    color: #fff;
  }
}
.moon {
  background: #fff;
  :deep() .elstyle-button--text {
    color: rgb(153, 153, 153);
  }
}
.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}
</style>
