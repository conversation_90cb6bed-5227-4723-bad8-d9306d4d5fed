<template>
  <div>
    <el-dialog :title="title" v-model="dialogFormVisible" :before-close="handleClose" width="30%">
      <el-form :model="form" :rules="rules" ref="ruleForm">
        <el-form-item label="名称:" :label-width="formLabelWidth" prop="name">
          <el-input v-model="form.name" style="width: 220px" autocomplete="off" placeholder="请输入行动策略名称"></el-input>
        </el-form-item>
        <el-form-item label="描述:" :label-width="formLabelWidth" prop="description">
          <el-input type="textarea" style="width: 220px" v-model="form.description" autocomplete="off" :rows="2" placeholder="请输入描述"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel()">取 消</el-button>
          <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { defineComponent } from "vue";
import { ElMessage, ElMenuItem } from "element-plus";

import { addSupport_notesGlobal, editSupport_notesGlobal } from "@/views/pages/apis/supportNotes";
import { getAlarmClassificationList } from "@/views/pages/apis/alarmClassification";

export default defineComponent({
  name: "supplierCreate",
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["dialogClose"],
  data() {
    return {
      form: {
        name: "",
        description: "",
        activeConfig: {
          useAutoTimeZone: true,
          timeZone: "自动时区",
          activeHours: {
            weekDay: 0,
            hours: [],
          },
        },
        activeNote: "",
        inactiveNote: "",
      },
      rules: {
        name: [{ required: true, message: "请输入行动策略名称", trigger: "blur" }],
      },
      title: "",
      checked: false,
      dialogFormVisible: false,
      formLabelWidth: "130px",
      type: "",
      options: [],
      value: "",

      disabled: "",
    };
  },
  watch: {
    dialog(val) {
      // this.$refs["ruleForm"].clearValidate();
      this.dialogFormVisible = val;
    },
    type(val) {
      if (val === "add") {
        for (var key in this.form) {
          this.form[key] = null;
        }
      }
      // console.log(this.form);
    },
  },
  created() {
    // console.log(this.$props, 5555);
    this.getAlarmList();
  },
  methods: {
    getAlarmList() {
      getAlarmClassificationList({}).then((res) => {
        if (res.success) {
          this.options = [...res.data];
        } else {
          ElMessage.error(JSON.parse(res.data)?.message + "操作失败");
        }
      });
    },
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.form.activeConfig = {
            useAutoTimeZone: true,
            timeZone: "自动时区",
            activeHours: [
              {
                weekDay: 1,
                hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
              },
              {
                weekDay: 2,
                hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
              },
              {
                weekDay: 3,
                hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
              },
              {
                weekDay: 4,
                hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
              },
              {
                weekDay: 5,
                hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
              },
              {
                weekDay: 6,
                hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
              },
              {
                weekDay: 7,
                hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
              },
            ],
          };
          if (this.type === "add") {
            addSupport_notesGlobal(this.form)
              .then((res) => {
                if (res.success) {
                  ElMessage.success("新增成功");
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                } else {
                  ElMessage.error(JSON.parse(res.data)?.message);
                }
              })
              .catch((e) => {
                if (e instanceof Error) ElMessage.error(e.message);
                this.$emit("dialogClose", false);
              });
          } else {
            editSupport_notesGlobal(this.form)
              .then((res) => {
                if (res.success) {
                  ElMessage.success("修改成功");
                  this.$emit("dialogClose", false);
                  this.$refs[formName].resetFields();
                } else {
                  ElMessage.error(JSON.parse(res.data)?.message);
                }
              })
              .catch((e) => {
                if (e instanceof Error) ElMessage.error(e.message);
                this.$emit("dialogClose", false);
              });
          }
          this.dialogFormVisible = false;
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
      // this.dialogFormVisible = false;
    },
    cancel() {
      this.$emit("dialogClose", false);
      this.$refs["ruleForm"].resetFields();
    },
    blurChange(data) {
      this.tags.push(data);
    },
    tagClose(index) {
      this.tags.splice(index, 1);
    },
  },
  expose: ["type", "disabled", "title", "form", "value"],
});
</script>
