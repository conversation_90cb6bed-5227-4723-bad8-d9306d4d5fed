<template>
  <div class="SubruleAdd">
    <el-dialog :title="addType == 'area' ? '新增区域' : addType == 'device' ? '新增设备' : '新增场所'" v-model="dialogVisible" width="35%" :before-close="handleClose">
      <el-form>
        <el-form-item style="width: 100%">
          <el-row style="width: 100%">
            <el-col style="text-align: right" class="bold" :span="4">
              <span style="color: red">*</span>
              {{ title + "：" }}
            </el-col>
            <el-col :span="14">
              <el-select v-if="addType != 'area'" v-model="value" :placeholder="'请选择' + title" style="margin-left: 30px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled"> </el-option>
              </el-select>
              <el-cascader v-model="areaList" v-if="addType == 'area'" :options="options" :props="{ checkStrictly: true, value: 'id', label: 'name', disabled: 'disabled' }" clearable></el-cascader>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
export default {
  name: "SlaDownDialog",
  props: {
    addType: {
      type: String,
      default: "",
    },
    options: {
      type: Array,
    },
  },
  data() {
    return {
      title: "",
      dialogVisible: false,
      value: "",
      areaList: [],
    };
  },
  watch: {
    addType(val) {
      val == "area" ? (this.title = "区域名称") : val == "device" ? (this.title = "设备名称") : (this.title = "场所名称");
    },
    options(val) {
      //(val);
    },
  },
  mounted() {
    // // console.log(this.$props.addType);
  },
  methods: {
    cancel() {
      // // console.log(123);
      this.dialogVisible = false;
    },
    confirm() {
      // // console.log(instanceof this.value);
      if (this.addType === "area") {
        if (this.areaList.length > 0) {
          this.$emit("confirmMsg", { value: this.areaList[this.areaList.length - 1], type: this.addType });
        } else {
          this.$message.error("请选择" + this.title);
        }
      } else {
        if (this.value) {
          this.$emit("confirmMsg", { value: this.value, type: this.addType });
        } else {
          this.$message.error("请选择" + this.title);
        }
      }
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch(() => {
          done();
        });
    },
  },
};
</script>
