<!-- eslint-disable no-undef -->
<template>
  <el-card>
    <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane v-for="tab in tabs" :key="`tab-${tab.name}`" :label="tab.label" :name="tab.name">
        <el-scrollbar :height="`${height - 40 - 55}px`">
          <!-- <component :is="tab.component" @confirm="submitSla" :downForm="downForm" :slaForm="slaForm" :width="width"></component> -->
          <slaConfig @confirm="submitSla" :slaForm="slaForm" v-show="activeName === 'Event'" :width="width"></slaConfig>
          <alarmDownConf @confirm="submitSla" :downForm="downForm" v-show="activeName === 'Alarmm'" :width="width"></alarmDownConf>
          <!-- <strategy @confirm="submitSla" v-show="activeName === 'Strategy'"></strategy> -->
          <!-- <strategy  :width="width - 40" @confirm="update"></strategy> -->
        </el-scrollbar>
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script setup lang="ts" name="system">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";

import { getSlaDefault, AddGlobalSlaConfig, getDegradeDefault, AddSlaDownGlobalConfig, EditGlobalSlaConfig, GlobalSlaDownConfigEdit } from "@/views/pages/apis/SlaConfig";

import { ElMessage, ElMessageBox } from "element-plus";
import slaConfig from "./slaConfig.vue";
import alarmDownConf from "./alarmDownConf.vue";
import strategy from "./strategy.vue";
import getUserInfo from "@/utils/getUserInfo";
const userInfo = getUserInfo();

const height = inject("height", ref(0));
const width = inject("width", ref(0));

const ctx = getCurrentInstance()!;
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
const activeName = ref("");
const slaForm = ref({});
const downForm = ref({});

const tabs = reactive([
  { label: "sla配置", name: "Event", component: slaConfig },
  { label: "告警降级配置", name: "Alarmm", component: alarmDownConf },
  // { label: "行动策略", name: "Strategy", component: strategy },
]);

activeName.value = (tabs.find((v) => v)?.name as string) || "";

function mounted() {
  getSlaConfigDefault();
  getDownList();
}
watch(activeName, (newVal, oldVla) => {
  if (newVal == "Event") {
    getSlaConfigDefault();
  } else if (newVal == "Alarmm") {
    getDownList();
  }
});
function submitSla(data: any) {
  if (data.type === "sla") {
    if (JSON.stringify(slaForm.value) == "{}") {
      AddGlobalSlaConfig({ ...data.data, tenantId: -1, defaultRule: true })
        .then((res: any) => {
          if (res.success) {
            ElMessage.success("操作成功");
            getSlaConfigDefault();
          } else {
            ElMessage.error(JSON.parse(res.data)?.message);
          }
        })
        .catch((err) => {
          ElMessage.error(err?.message);
        });
    } else {
      EditGlobalSlaConfig({ ...data.data, tenantId: -1, defaultRule: true })
        .then((res: any) => {
          if (res.success) {
            ElMessage.success("操作成功");
            getSlaConfigDefault();
          } else {
            ElMessage.error(JSON.parse(res.data)?.message);
          }
        })
        .catch((err) => {
          ElMessage.error(err?.message);
        });
    }
  } else if (data.type === "alarm") {
    if (JSON.stringify(downForm.value) == "{}") {
      AddSlaDownGlobalConfig({ ...data.data, tenantId: -1, defaultRule: true })
        .then((res: any) => {
          if (res.success) {
            ElMessage.success("操作成功");
            getDownList();
          } else {
            ElMessage.error(JSON.parse(res.data)?.message);
          }
        })
        .catch((err) => {
          ElMessage.error(err?.message);
        });
    } else {
      GlobalSlaDownConfigEdit({ ...data.data, tenantId: -1, defaultRule: true })
        .then((res: any) => {
          if (res.success) {
            ElMessage.success("操作成功");
            getDownList();
          } else {
            ElMessage.error(JSON.parse(res.data)?.message);
          }
        })
        .catch((err) => {
          ElMessage.error(err?.message);
        });
    }
  }
}

//  //获取默认sla规则
function getSlaConfigDefault() {
  getSlaDefault({ global: true }).then((res: any) => {
    if (res.success && res.data instanceof Object) {
      slaForm.value = res.data;
    } else {
      slaForm.value = {};
    }
  });
}

//获取全局告警降级配置
function getDownList() {
  getDegradeDefault({}).then((res: any) => {
    if (res.success && res.data instanceof Object) {
      downForm.value = res.data;
    } else {
      downForm.value = {};
    }
  });
}

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */

onMounted(mounted, ctx);
</script>
