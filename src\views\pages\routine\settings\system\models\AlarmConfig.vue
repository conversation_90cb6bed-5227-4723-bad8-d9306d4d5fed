<template>
  <el-scrollbar :height="height">
    <div v-for="i in 50" :key="i" class="tw-m-[12px] tw-bg-slate-400">width: {{ width }} height: {{ height }}</div>
  </el-scrollbar>
</template>

<script lang="ts" setup>
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, readonly, nextTick, inject, h, computed } from "vue";

interface Props {
  width: number;
  height: number;
}
const props = withDefaults(defineProps<Partial<Props>>(), {});
const width = computed(() => props.width || inject("width", ref(0)));
const height = computed(() => props.height || inject("height", ref(0)));

interface Data {
  [key: string]: any;
}
interface State<T> {
  loading: boolean;
  data: T;
}
const state = reactive<State<Data>>({
  loading: false,
  data: {},
});
</script>

<style lang="scss" scoped></style>
