<template>
  <el-scrollbar :height="height - 50">
    <FormModel ref="formRef" :loading="state.loading" :model="form" label-position="top" :label-width="`${props.labelWidth}px`" @submit="handleFinish">
      <FormItem :span="width > 600 ? 12 : 24" :label="`${props.title}名称`" tooltip="" prop="alertCount" :rules="[]">
        <el-input v-model="form.eventName" type="text" placeholder="" clearable></el-input>
      </FormItem>
      <FormItem :span="width > 600 ? 12 : 24" :label="`${props.title}描述`" tooltip="" prop="deviceCount" :rules="[]">
        <div class="tw-flex tw-min-h-[250px] tw-flex-col" @keyup.enter.stop>
          <QuillEditor theme="snow" style="flex: 1" v-model:content="form.eventDesc" contentType="html" toolbar="full"></QuillEditor>
        </div>
      </FormItem>
      <FormItem :span="width > 675 ? 24 : 24" :label="`优先级`" tooltip="" prop="tenantCount" :rules="[]">
        <el-select v-model="form.priority" placeholder="" clearable filterable class="tw-w-full">
          <el-option v-for="item in priorityOption" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
      </FormItem>
    </FormModel>
  </el-scrollbar>
  <div class="tw-mt-[18px] tw-flex tw-h-[32px] tw-items-center tw-justify-center">
    <div class="tw-ml-auto">
      <el-button type="default" @click.stop="">{{ $t("glob.Reset") }}</el-button>
      <!-- <el-button type="default" @click.stop="">{{ $t("glob.Cancel") }}</el-button> -->
      <el-button type="primary" @click.stop="">{{ $t("glob.Confirm") }}</el-button>
      <!-- <el-button type="primary" @click.stop="">{{ $t("glob.Save") }}</el-button> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, readonly, nextTick, inject, h, computed } from "vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

interface Props {
  width: number;
  height: number;
  title: string;
}
const props = withDefaults(defineProps<Partial<Props>>(), { title: "" });
const width = computed(() => props.width || inject("width", ref(0)));
const height = computed(() => props.height || inject("height", ref(0)));

interface Data {
  [key: string]: any;
}
interface Form {
  [key: string]: any;
}
interface State<D> {
  loading: boolean;
  data: D;
}
const state = reactive<State<Data>>({
  loading: false,
  data: {},
});
const form = reactive<Form>({});
</script>

<style lang="scss" scoped></style>
