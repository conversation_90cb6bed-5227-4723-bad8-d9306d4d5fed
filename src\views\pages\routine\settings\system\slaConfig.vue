<!--  -->
<template>
  <div class="sla_config">
    <el-form :model="form" :style="{ marginTop: '10px' }" label-position="left">
      <el-card class="el-card-mt">
        <el-row>
          <el-form-item style="width: 100%">
            <el-row style="margin-bottom: 20px; width: 100%">
              <el-col style="text-align: right" class="bold" :span="2">
                <span style="color: red">*</span>
                SLA名称：
              </el-col>
              <el-col :span="10" class="bold">
                <el-input v-model="slaName" placeholder="请输入SLA名称" :style="basicClassInput" style="width: 440px" />
              </el-col>

              <el-col style="text-align: right" class="bold" :span="2"> SLA描述： </el-col>
              <el-col :span="10" style="text-align: left" class="bold">
                <el-input v-model="slaDesc" type="textarea" placeholder="请输入SLA描述" :style="basicClassInput" style="width: 440px" />
              </el-col>
            </el-row>
            <el-row style="width: 100%">
              <el-col class="bold" :span="2" style="text-align: right">覆盖时间：</el-col>
              <el-col class="bold" :span="12" style="text-align: right">选择时区：</el-col>
              <el-col :span="6" style="text-align: left" class="bold">
                <el-select v-model="coverTimeCfg.timeZone" filterable placeholder="请选择" :style="basicClassInputDown" style="width: 440px">
                  <el-option v-for="item in timeZone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"> </el-option>
                </el-select>
              </el-col>

              <el-col style="width: 100%">
                <div style="width: 100%" class="support-table-content" ref="tableContentRef">
                  <el-table stripe border :data="coverTimeCfg.coverWorkTime" class="sla-hour" style="width: 100%; margin-top: 30px" @header-click="(column, $event) => handleClick({ column, $event })">
                    <el-table-column align="left" prop="week" width="80">
                      <template #default="scope">
                        <div @click="handleSelectTime('all', scope.$index, scope.row)">
                          {{ scope.row.weekDay == 1 ? "周一" : scope.row.weekDay == 2 ? "周二" : scope.row.weekDay == 3 ? "周三" : scope.row.weekDay == 4 ? "周四" : scope.row.weekDay == 5 ? "周五" : scope.row.weekDay == 6 ? "周六" : "周日" }}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column align="left" :min-width="tableWidth" v-for="(item, key) in 24" :key="`h-${key}`" :label="String(key)">
                      <template #default="scope">
                        <div @click="handleSelectTime(key, scope.$index, scope.row)" style="width: 100%; height: 100%">
                          <el-button type="text" style="font-size: 30px; color: rgb(26, 190, 107)">
                            <el-icon v-if="scope.row.workTime && scope.row.workTime.length && scope.row.workTime.includes(key)">
                              <Check></Check>
                            </el-icon>
                          </el-button>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-col>
            </el-row>
          </el-form-item>

          <el-form-item class="things">
            <el-row :gutter="24" style="width: 100%">
              <el-col class="bold" :span="5" style="text-align: left"> 事件响应时间等级：</el-col>
              <el-col :span="19" style="text-align: right">
                <el-button type="primary" :icon="Plus" @click="addLevel('response')">新增等级</el-button>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="width: 100%">
              <el-col :span="24">
                <el-table stripe :data="responseTimeList" style="width: 100%; margin-top: 30px">
                  <el-table-column align="left" label="事件优先级">
                    <template #default="{ row }">
                      <span v-if="row.state" class="state" :style="{ color: '#fff', backgroundColor: eventPriorityColor[row.priority].color }">{{ row.priority }}</span>
                      <el-select v-else v-model="row.priority">
                        <el-option v-for="item in responseData" :key="item.value" :label="item.value" :value="item.value"> </el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="容忍时间" width="300">
                    <template #default="scope">
                      <div v-show="scope.row.state">
                        {{ scope.row.resolves[0].day ? scope.row.resolves[0].day + "day" : "" }}
                        {{ scope.row.resolves[0].hour ? scope.row.resolves[0].hour + "h" : "" }}
                        {{ scope.row.resolves[0].minute ? scope.row.resolves[0].minute + "min" : "" }}
                      </div>
                      <div v-show="!scope.row.state">
                        <el-input-number :min="0" v-model="scope.row.resolves[0].day" :max="10000000000"> </el-input-number>
                        day
                        <el-input-number :min="0" v-model="scope.row.resolves[0].hour" :max="23"> </el-input-number>
                        h
                        <el-input-number :min="0" v-model="scope.row.resolves[0].minute" :max="59"> </el-input-number>
                        min
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="SLA状态" prop="urgencyType">
                    <template #default="scope">
                      <span class="state" v-show="scope.row.state" :style="{ color: '#fff', backgroundColor: slaStateColor[scope.row.resolves[0].urgencyType?.toLowerCase()] }">
                        <span v-show="scope.row.resolves[0].urgencyType">
                          {{ scope.row.resolves[0].urgencyType?.slice(0, 1).toUpperCase() + scope.row.resolves[0].urgencyType?.slice(1).toLowerCase() }}
                        </span>
                        <span v-show="!scope.row.resolves[0].urgencyType"> </span>
                      </span>
                      <el-select v-show="!scope.row.state" v-model="scope.row.resolves[0].urgencyType">
                        <el-option v-for="item in statusData" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="名称">
                    <template #default="scope">
                      <div v-show="scope.row.state">
                        {{ scope.row.resolves[0].name }}
                      </div>
                      <div v-show="!scope.row.state">
                        <el-input v-model="scope.row.resolves[0].name"> </el-input>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="描述">
                    <template #default="scope">
                      <div v-show="scope.row.state">
                        {{ scope.row.resolves[0].description }}
                      </div>
                      <div v-show="!scope.row.state">
                        <el-input v-model="scope.row.resolves[0].description"> </el-input>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="定义">
                    <template #default="scope">
                      <div v-show="scope.row.state">
                        {{ scope.row.resolves[0].definition }}
                      </div>
                      <div v-show="!scope.row.state">
                        <el-input v-model="scope.row.resolves[0].definition"> </el-input>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="操作">
                    <template #default="scope">
                      <el-button type="text" v-show="scope.row.state" @click="editLevel('response', scope.$index, scope.row)" style="margin-right: 10px">编辑</el-button>
                      <el-button type="text" v-show="!scope.row.state" @click="confirmLevel('response', scope.$index, scope.row)">保存</el-button>

                      <el-popconfirm :title="delTitle" @confirm="delConfirm('response', scope.$index, scope.row)">
                        <template #reference>
                          <el-button type="text" textColor="danger" @click="delLevel('response', scope.$index, scope.row)">删除</el-button>
                        </template>
                      </el-popconfirm>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>

            <el-row :gutter="24" style="margin-top: 20px; width: 100%">
              <el-col class="bold" :span="5" style="text-align: left"> 事件处理时间等级：</el-col>
              <el-col :span="19" style="text-align: right">
                <el-button type="primary" :icon="Plus" @click="addLevel('resolve')">新增等级</el-button>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="width: 100%">
              <el-col :span="24">
                <el-table stripe :data="handelTimeList" style="width: 100%; margin-top: 30px">
                  <el-table-column align="left" label="事件优先级">
                    <template #default="{ row }">
                      <span v-if="row.state" class="state" :style="{ color: '#fff', backgroundColor: eventPriorityColor[row.priority].color }">{{ row.priority }}</span>
                      <el-select v-else v-model="row.priority">
                        <el-option v-for="item in responseData" :key="item.value" :label="item.value" :value="item.value"> </el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="容忍时间" width="300">
                    <template #default="scope">
                      <div v-show="scope.row.state">
                        {{ scope.row.resolves[0].day ? scope.row.resolves[0].day + "day" : "" }}
                        {{ scope.row.resolves[0].hour ? scope.row.resolves[0].hour + "h" : "" }}
                        {{ scope.row.resolves[0].minute ? scope.row.resolves[0].minute + "min" : "" }}
                      </div>
                      <div v-show="!scope.row.state">
                        <el-input-number :min="0" v-model="scope.row.resolves[0].day" :max="10000000000"> </el-input-number>
                        day
                        <el-input-number :min="0" v-model="scope.row.resolves[0].hour" :max="23"> </el-input-number>
                        h
                        <el-input-number :min="0" v-model="scope.row.resolves[0].minute" :max="59"> </el-input-number>
                        min
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="SLA状态" prop="urgencyType">
                    <template #default="scope">
                      <span class="state" v-show="scope.row.state" :style="{ color: '#fff', backgroundColor: slaStateColor[scope.row.resolves[0].urgencyType?.toLowerCase()] }">
                        <span v-show="scope.row.resolves[0].urgencyType">
                          {{ scope.row.resolves[0].urgencyType?.slice(0, 1).toUpperCase() + scope.row.resolves[0].urgencyType?.slice(1).toLowerCase() }}
                        </span>
                        <span v-show="!scope.row.resolves[0].urgencyType"> </span>
                      </span>
                      <el-select v-show="!scope.row.state" v-model="scope.row.resolves[0].urgencyType">
                        <el-option v-for="item in statusData" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="名称">
                    <template #default="scope">
                      <div v-show="scope.row.state">
                        {{ scope.row.resolves[0].name }}
                      </div>
                      <div v-show="!scope.row.state">
                        <el-input v-model="scope.row.resolves[0].name"> </el-input>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="描述">
                    <template #default="scope">
                      <div v-show="scope.row.state">
                        {{ scope.row.resolves[0].description }}
                      </div>
                      <div v-show="!scope.row.state">
                        <el-input v-model="scope.row.resolves[0].description"> </el-input>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="定义">
                    <template #default="scope">
                      <div v-show="scope.row.state">
                        {{ scope.row.resolves[0].definition }}
                      </div>
                      <div v-show="!scope.row.state">
                        <el-input v-model="scope.row.resolves[0].definition"> </el-input>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="操作">
                    <template #default="scope">
                      <el-button type="text" v-show="scope.row.state" @click="editLevel('resolve', scope.$index, scope.row)" style="margin-right: 10px">编辑</el-button>
                      <el-button type="text" v-show="!scope.row.state" @click="confirmLevel('resolve', scope.$index, scope.row)">保存</el-button>

                      <el-popconfirm :title="delTitle" @confirm="delConfirm(' resolve', scope.$index, scope.row)">
                        <template #reference>
                          <el-button type="text" textColor="danger" @click="delLevel('resolve', scope.$index, scope.row)">删除</el-button>
                        </template>
                      </el-popconfirm>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>

            <!-- <el-row style="margin-top: 20px">
              <el-col class="bold" :span="4" style="text-align: left"> 服务请求响应时间等级：</el-col>
              <el-col :span="20" style="text-align: right">
                <el-button type="primary" :icon="Plus" disabled @click="addLevel('resolve')">新增等级</el-button>
              </el-col>
              <el-col>
                 <el-table stripe :data="handelTimeList" style="width: 100%; margin-top: 30px">
                  <el-table-column align="left" label="事件优先级">
                    <template #default="{ row }">
                      <span v-if="row.state" class="state" :style="{ color: '#fff', backgroundColor: eventPriorityColor[row.priority].color }">{{ row.priority }}</span>
                      <el-select v-else v-model="row.priority">
                        <el-option v-for="item in responseData" :key="item.value" :label="item.value" :value="item.value"> </el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="容忍时间" width="300">
                    <template slot-scope="scope">
                      <div v-show="scope.row.state">
                        {{ scope.row.resolves[0].day ? scope.row.resolves[0].day + "day" : "" }}
                        {{ scope.row.resolves[0].hour ? scope.row.resolves[0].hour + "h" : "" }}
                        {{ scope.row.resolves[0].minute ? scope.row.resolves[0].minute + "min" : "" }}
                      </div>
                      <div v-show="!scope.row.state">
                        <el-input style="width: 50px" v-model="scope.row.resolves[0].day"> </el-input>
                        day
                        <el-input style="width: 50px" v-model="scope.row.resolves[0].hour" maxlength="2" @input="hourChange($event, scope.$index, 'resolve')"> </el-input>
                        h
                        <el-input style="width: 50px" v-model="scope.row.resolves[0].minute" maxlength="2" @input="minuteChange($event, scope.$index, 'resolve')"> </el-input>
                        min
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="SLA状态" prop="urgencyType">
                    <template slot-scope="scope">
                      <span class="state" v-show="scope.row.state" :style="{ color: '#fff', backgroundColor: slaStateColor[scope.row.resolves[0].urgencyType.toLowerCase()] }">
                        {{ scope.row.resolves[0].urgencyType.slice(0, 1).toUpperCase() + scope.row.resolves[0].urgencyType.slice(1).toLowerCase() }}
                      </span>
                      <el-select v-show="!scope.row.state" v-model="scope.row.resolves[0].urgencyType">
                        <el-option v-for="item in statusData" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="名称">
                    <template slot-scope="scope">
                      <div v-show="scope.row.state">
                        {{ scope.row.resolves[0].name }}
                      </div>
                      <div v-show="!scope.row.state">
                        <el-input v-model="scope.row.resolves[0].name"> </el-input>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="描述">
                    <template slot-scope="scope">
                      <div v-show="scope.row.state">
                        {{ scope.row.resolves[0].description }}
                      </div>
                      <div v-show="!scope.row.state">
                        <el-input v-model="scope.row.resolves[0].description"> </el-input>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="定义">
                    <template slot-scope="scope">
                      <div v-show="scope.row.state">
                        {{ scope.row.resolves[0].definition }}
                      </div>
                      <div v-show="!scope.row.state">
                        <el-input v-model="scope.row.resolves[0].definition"> </el-input>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="操作">
                    <template slot-scope="scope">
                      <el-button type="text" v-show="scope.row.state" @click="editLevel('resolve', scope.$index, scope.row)" style="margin-right: 10px">编辑</el-button>
                      <el-button type="text" v-show="!scope.row.state" @click="confirmLevel('resolve', scope.$index, scope.row)">保存</el-button>

                      <el-popconfirm :title="delTitle" @confirm="delConfirm('resolve', scope.$index, scope.row)">
                        <el-button type="text" slot="reference" textColor="danger" @click="delLevel('resolve', scope.$index, scope.row)">删除</el-button>
                      </el-popconfirm>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>-->
            <!--
            <el-row style="margin-top: 20px">
              <el-col class="bold" :span="3" style="text-align: left"> 服务响应时间等级：</el-col>
              <el-col :span="21" style="text-align: right">
                <el-button type="primary" :icon="Plus" disabled @click="addLevel('resolve')">新增等级</el-button>
              </el-col>
              <el-col>
                <el-table stripe :data="handelTimeList" style="width: 100%; margin-top: 30px">
                  <el-table-column align="left" label="事件优先级">
                    <template #default="{ row }">
                      <span v-if="row.state" class="state" :style="{ color: '#fff', backgroundColor: eventPriorityColor[row.priority].color }">{{ row.priority }}</span>
                      <el-select v-else v-model="row.priority">
                        <el-option v-for="item in responseData" :key="item.value" :label="item.value" :value="item.value"> </el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="容忍时间" width="300">
                    <template slot-scope="scope">
                      <div v-show="scope.row.state">
                        {{ scope.row.resolves[0].day ? scope.row.resolves[0].day + "day" : "" }}
                        {{ scope.row.resolves[0].hour ? scope.row.resolves[0].hour + "h" : "" }}
                        {{ scope.row.resolves[0].minute ? scope.row.resolves[0].minute + "min" : "" }}
                      </div>
                      <div v-show="!scope.row.state">
                        <el-input style="width: 50px" v-model="scope.row.resolves[0].day"> </el-input>
                        day
                        <el-input style="width: 50px" v-model="scope.row.resolves[0].hour" maxlength="2" @input="hourChange($event, scope.$index, 'resolve')"> </el-input>
                        h
                        <el-input style="width: 50px" v-model="scope.row.resolves[0].minute" maxlength="2" @input="minuteChange($event, scope.$index, 'resolve')"> </el-input>
                        min
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="SLA状态" prop="urgencyType">
                    <template slot-scope="scope">
                      <span class="state" v-show="scope.row.state" :style="{ color: '#fff', backgroundColor: slaStateColor[scope.row.resolves[0].urgencyType.toLowerCase()] }">
                        {{ scope.row.resolves[0].urgencyType.slice(0, 1).toUpperCase() + scope.row.resolves[0].urgencyType.slice(1).toLowerCase() }}
                      </span>
                      <el-select v-show="!scope.row.state" v-model="scope.row.resolves[0].urgencyType">
                        <el-option v-for="item in statusData" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="名称">
                    <template slot-scope="scope">
                      <div v-show="scope.row.state">
                        {{ scope.row.resolves[0].name }}
                      </div>
                      <div v-show="!scope.row.state">
                        <el-input v-model="scope.row.resolves[0].name"> </el-input>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="描述">
                    <template slot-scope="scope">
                      <div v-show="scope.row.state">
                        {{ scope.row.resolves[0].description }}
                      </div>
                      <div v-show="!scope.row.state">
                        <el-input v-model="scope.row.resolves[0].description"> </el-input>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="定义">
                    <template slot-scope="scope">
                      <div v-show="scope.row.state">
                        {{ scope.row.resolves[0].definition }}
                      </div>
                      <div v-show="!scope.row.state">
                        <el-input v-model="scope.row.resolves[0].definition"> </el-input>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="操作">
                    <template slot-scope="scope">
                      <el-button type="text" v-show="scope.row.state" @click="editLevel('resolve', scope.$index, scope.row)" style="margin-right: 10px">编辑</el-button>
                      <el-button type="text" v-show="!scope.row.state" @click="confirmLevel('resolve', scope.$index, scope.row)">保存</el-button>

                      <el-popconfirm :title="delTitle" @confirm="delConfirm('resolve', scope.$index, scope.row)">
                        <el-button type="text" slot="reference" textColor="danger" @click="delLevel('resolve', scope.$index, scope.row)">删除</el-button>
                      </el-popconfirm>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>-->
            <div class="bold" style="text-align: center; margin-top: 30px; width: 100%">
              <el-button @click="baocunLevel" type="primary" :disabled="!userInfo.hasPermission(PERMISSION.group515414579476430848.group524824224204849152.editor)"> 保存</el-button>
            </div>
          </el-form-item>
        </el-row>
      </el-card>
    </el-form>
  </div>
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { slaState } from "@/views/pages/common/slaState";
import { Check } from "@element-plus/icons-vue";
import { Zone } from "@/utils/zone";
import { Plus, Select } from "@element-plus/icons-vue";
import { responseData, resolveData, priority, statusData } from "./common";
import getUserInfo from "@/utils/getUserInfo";
const userInfo = getUserInfo();
export default {
  components: {
    Check,
  },
  props: {
    slaForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
    width: {
      type: Number,
    },
  },
  data() {
    return {
      Plus,
      Select,
      userInfo,
      customerItemId: "",
      packagePreview: false,
      isShow: false,
      content: "新增SLA",
      eventPriorityColor: priority,
      customerName: "",
      subruleName: "",
      subruleDesc: "",
      slaName: "", //sla名称
      slaType: "", //sla类型
      slaDesc: "", //sla描述
      DefaultTime: "", //选择时区
      PriorityLevel: "", //优先级等级
      SlaDialogVisible: false,
      form: {},

      responseData: responseData,
      resolveData: resolveData,
      statusData: statusData,
      timeZone: Object.keys(Zone).map((v) => ({ zoneId: v, displayName: Zone[v] })),

      slaStateColor: slaState,
      TimeAddress: [],
      expands: [],
      index: 1,
      coverTimeCfg: {
        timeZone: "设备默认",
        useCustomerTimeZone: false,
        useDeviceTimeZone: false,
        coverWorkTime: [
          {
            week: "周一",
            workTime: [],
            weekDay: 1,
          },
          {
            week: "周二",
            workTime: [],
            weekDay: 2,
          },
          {
            week: "周三",
            workTime: [],
            weekDay: 3,
          },
          {
            week: "周四",
            workTime: [],
            weekDay: 4,
          },
          {
            week: "周五",
            workTime: [],
            weekDay: 5,
          },
          {
            week: "周六",
            workTime: [],
            weekDay: 6,
          },
          {
            week: "周日",
            workTime: [],
            weekDay: 7,
          },
        ],
      },
      basicClassInput: { width: "25vw" } /* 输入框选择器基本样式 */,
      basicClassInputDown: { width: "15.8vw" } /* 输入框选择器基本样式 */,
      TimeDialogVisible: false, //事件时间等级弹框
      responseTimeList: [], //事件响应时间等级

      handelTimeList: [], //事件处理时间等级

      eventType: "",
      eventTypeList: [],

      ruleId: "",
      chooseLevelType: "",
      delTitle: "",
      tenantId: userInfo.currentTenantId,
      allBool: [false, false, false, false, false, false, false],
      tableWidth: 0,
    };
  },
  watch: {
    slaForm(val) {
      if (Object.keys(val).length > 0) {
        this.coverTimeCfg = val.coverTimeCfg;
        this.slaName = val.ruleName;
        this.slaDesc = val.ruleDesc;
        this.ruleId = val.ruleId;
        this.coverTimeCfg.coverWorkTime.forEach((item: any) => {
          if (item.workTime.length > 23) {
            this.allBool[item.weekDay - 1] = true;
          } else {
            this.allBool[item.weekDay - 1] = false;
          }
        });
        let responseData = [];
        let resolveData = [];
        // val.eventRespDefaultResolves.forEach((v, i) => {

        // });
        val.eventRespDefaultResolves.forEach((v, i) => {
          responseData.push({
            priority: "Default",
            resolves: [v],
            state: true,
          });
        });
        val.eventRespTimeLevels.forEach((v, i) => {
          if (v.resolves.length > 0) {
            v.resolves.forEach((item) => {
              responseData.push({
                priority: v.priority,
                resolves: [item],
                state: true,
              });
            });
          }
        });
        val.eventResolveDefaultResolves.forEach((v, i) => {
          resolveData.push({
            priority: "Default",
            resolves: [v],
            state: true,
          });
        });
        val.eventResolveTimeLevels.forEach((v, i) => {
          if (v.resolves.length > 0) {
            v.resolves.forEach((item) => {
              resolveData.push({
                priority: v.priority,
                resolves: [item],
                state: true,
              });
            });
          }
        });
        this.responseTimeList = [...responseData];
        this.handelTimeList = [...resolveData];
      }
    },
  },
  created() {
    this.timeZone.unshift(
      
      {
        zoneId: "设备默认",
        displayName: "设备默认",
      }
    );
    let map = new Map();
    for (let item of this.timeZone) {
      map.set(item.zoneId, item);
    }
    this.timeZone = [...map.values()];
  },
  mounted() {
    let that = this;
    // this.$nextTick(() => {
    //(this.$props);
    let width = this.$props ? this.$props.width - 90 : this.$refs.tableContentRef.offsetWidth;

    this.tableWidth = ((width - 80) / 24).toFixed(0);
    // });

    window.addEventListener("resize", function () {
      let width = that.$refs.tableContentRef.offsetWidth;

      that.tableWidth = ((width - 80) / 24).toFixed(0);
    });
  },
  methods: {
    //编辑事件操作

    editLevel(type, index, data) {
      data.state = false;
      this.$forceUpdate();
      // if (type === "response") {
      //   // this.responseTimeList[index].state = false;
      //   this.$set(this.responseTimeList, index, {
      //     state: false,
      //   });
      //   // console.log(this.responseTimeList);
      // } else {
      //   // this.handelTimeList[index].state = false;
      //   this.$set(this.handelTimeList, index, {
      //     state: false,
      //   });
      // }
    },
    //保存事件操作
    confirmLevel(type, index, data) {
      //type 类型
      //data 输入框中的数据
      //index当前操作的下标

      if (data.resolves[0].minute == 0 && data.resolves[0].hour == 0 && data.resolves[0].day == 0) {
        data.state = false;
        return this.$message.warning("容忍时间不能为0");
      }
      if (data.priority) {
        if (type === "response") {
          const hasObj = {};

          this.responseTimeList = this.responseTimeList.reduce((total, next) => {
            const filterKey = next.priority + next.resolves[0].urgencyType;

            if (hasObj[filterKey] != undefined) {
              data.state = false;
              next.state = false;
              total.push(next);
              this.$message.warning("SLA状态不可重复");
            } else data.state = true;
            if (filterKey) {
              hasObj[filterKey] ? "" : (hasObj[filterKey] = true && total.push(next));
            } else {
              total.push(next);
            }

            return total;
          }, []);
        } else {
          const hasObj = {};

          this.handelTimeList = this.handelTimeList.reduce((total, next) => {
            const filterKey = next.priority + next.resolves[0].urgencyType;

            if (hasObj[filterKey] != undefined) {
              data.state = false;
              next.state = false;
              total.push(next);
              this.$message.warning("SLA状态不可重复");
            } else data.state = true;
            if (filterKey) {
              hasObj[filterKey] ? "" : (hasObj[filterKey] = true && total.push(next));
              next.state = true;
            } else {
              total.push(next);
            }

            return total;
          }, []);
        }
      } else {
        return this.$message.warning("请先选择事件优先级");
      }
      //
      //
    },
    //删除事件操作
    delLevel(type, index, data) {
      this.delTitle = "确认删除当前等级为" + data.priority + "的所有内容吗？";
    },
    delConfirm(type, index, data) {
      if (type == "response") {
        if (data.id) {
          // DelRuleTime({ levelId: data.id }).then(({ success, data, message }) => {
          //   if (success) {
          //     this.responseTimeList.splice(index, 1);
          //     this.$message.success("删除成功");
          //   }
          // });
        } else {
          this.responseTimeList.splice(index, 1);
          this.$message.success("删除成功");
        }
      } else {
        if (data.id) {
          // DelRuleTime({ levelId: data.id }).then(({ success, data, message }) => {
          //   if (success) {
          //     this.handelTimeList.splice(index, 1);
          //     this.$message.success("删除成功");
          //   }
          // });
        } else {
          this.handelTimeList.splice(index, 1);
        }
      }
    },
    //新增等级
    addLevel(type) {
      this.eventType = "";
      this.chooseLevelType = type;
      this.TimeDialogVisible = true;

      if (type === "response") {
        this.responseTimeList.push({
          priority: "",
          resolves: [{ day: "", hour: "", minute: "", urgencyType: null, name: "", description: "", definition: "" }],
          state: false,
        });
      } else {
        this.handelTimeList.push({
          priority: "",
          resolves: [{ day: "", hour: "", minute: "", urgencyType: null, name: "", description: "", definition: "" }],
          state: false,
        });
      }
    },
    //事件时间等级保存
    baocunLevel() {
      if (this.slaName == "") {
        return this.$message.warning("请输入SLA名称");
      }
      if (this.coverTimeCfg.timeZone === "客户默认") {
        this.coverTimeCfg.useCustomerTimeZone = true;
        this.coverTimeCfg.useDeviceTimeZone = false;
      } else if (this.coverTimeCfg.timeZone === "设备默认") {
        this.coverTimeCfg.useDeviceTimeZone = true;
        this.coverTimeCfg.useCustomerTimeZone = false;
      } else {
        this.coverTimeCfg.useCustomerTimeZone = false;
        this.coverTimeCfg.useDeviceTimeZone = false;
      }
      let eventRespTimeLevels = [];
      let eventRespDefaultResolves = [];
      let eventResolveTimeLevels = [];
      let eventResolveDefaultResolves = [];
      this.responseTimeList.forEach((v, i) => {
        if (v.priority !== "Default" && v.state) {
          eventRespTimeLevels.push(v);
        } else if (v.priority === "Default" && v.state) {
          eventRespDefaultResolves.push(v.resolves[0]);
        }
      });
      this.handelTimeList.forEach((v, i) => {
        if (v.priority !== "Default" && v.state) {
          eventResolveTimeLevels.push(v);
        } else if (v.priority === "Default" && v.state) {
          eventResolveDefaultResolves.push(v.resolves[0]);
        }
      });
      let data = {
        tenantId: this.tenantId,
        ruleName: this.slaName,
        ruleDesc: this.slaDesc,
        coverTimeCfg: this.coverTimeCfg,
        eventRespTimeLevels,
        eventRespDefaultResolves,
        eventResolveTimeLevels,
        eventResolveDefaultResolves,
        ruleId: this.ruleId ? this.ruleId : undefined,
      };
      this.$emit("confirm", { data, type: "sla" });
    },

    //覆盖时间列
    handleClick({ column, evene }) {
      let index = Number(column.label);

      const { coverWorkTime } = this.coverTimeCfg;

      let workTime = [];
      coverWorkTime.forEach((v) => {
        workTime = workTime.concat(v.workTime);
      });

      const isActive = workTime.includes(index) && workTime.filter((v) => v === index).length === 7; // 是否激活
      // console.log(isActive);

      coverWorkTime.forEach((v: Record<string, any>, i: number) => {
        let delIndex = v.workTime.indexOf(index);
        if (isActive) {
          v.workTime.splice(delIndex, 1);
        } else {
          v.workTime.push(index);
        }

        v.workTime = [...new Set(v.workTime.sort((a, b) => a - b))];
      });
    },
    //覆盖时间
    handleSelectTime(key, weekIndex, row) {
      if (key === "all") {
        this.allBool[weekIndex] = !this.allBool[weekIndex];
        let data = [];
        for (let i = 0; i < 24; i++) {
          data.push(i);
        }
        row.workTime = [...new Set(data)];
        if (!this.allBool[weekIndex]) {
          row.workTime = [];
        }
      } else {
        const index = row.workTime.indexOf(key);
        if (index == -1) {
          row.workTime.push(key);
        } else row.workTime.splice(index, 1);
      }
    },
  },
};
</script>
<style scoped lang="scss">
.sla_config {
  :deep(.things) {
    .el-input-number {
      width: 50px !important;

      .elstyle-input {
        width: 50px;

        input {
          padding: 0;
        }
      }
    }
    .el-input-number {
      .el-input__wrapper {
        padding-left: 0px;
        padding-right: 0px;
      }
    }
    .el-input-number__increase {
      display: none;
    }

    .el-input-number__decrease {
      display: none;
    }

    .el-card {
      margin-top: 20px;
    }

    .el-form-item__content .el-form-item-content {
      display: flex;
      flex-direction: column;
    }

    // .el-table .cell {
    //   padding: 0 !important;
    // }

    .el-table .el-table__cell {
      padding: 0 !important;
      height: 50px;
    }
  }
  :deep(.sla-hour) {
    .cell {
      padding: 0 !important;
      display: flex;
      align-items: center;
      justify-content: center;
      > div {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
// ::v-deep .elstyle-input-number {
//   width: 50px !important;
//   .elstyle-input {
//     width: 50px;
//     input {
//       padding: 0;
//     }
//   }
// }
// ::v-deep .elstyle-input-number__increase {
//   display: none;
// }
// ::v-deep .elstyle-input-number__decrease {
//   display: none;
// }
// ::v-deep .elstyle-card {
//   margin-top: 20px;
// }
.priority-icon {
  width: 10px;
  height: 10px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 10px;
}
.state {
  padding: 2px 10px;
  box-sizing: border-box;
  border-radius: 20px;
}
.modules-item {
  .modules-title {
    color: rgba(32, 128, 255, 1);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}

.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;

  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}

// ::v-deep .elstyle-form-item__content .el-form-item-content {
//   display: flex;
//   flex-direction: column;
// }
// ::v-deep .elstyle-table .cell {
//   padding: 0 !important;
// }
// ::v-deep .elstyle-table .elstyle-table__cell {
//   padding: 0 !important;
//   height: 50px;
// }
</style>
