<template>
  <el-card ref="" :style="{ height: `${height / 2 - 10}px` }">
    <template #header>
      <h3>事件处理配置</h3>
    </template>
    <el-scrollbar :height="`${height / 2 - 10 - 40 - 100}px`">
      <el-form>
        <!-- <el-form-item label="是否自动分配:" :label-width="formLabelWidth">
          <el-radio v-model="handle.autoDistribution" :label="false">手动分配</el-radio>
          <el-radio v-model="handle.autoDistribution" disabled :label="true">自动分配</el-radio>
        </el-form-item> -->
        <el-form-item v-show="!handle.autoDistribution" label="默认处理对象" :label-width="formLabelWidth">
          <el-cascader v-show="isTrue" ref="cascader" style="width: 500px" v-model="value" @change="cascaderChange" filterable :options="userGroupList" :props="props" clearable></el-cascader>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div style="text-align: center">
      <!-- 配置事件处理默认对象 -->
      <el-button v-if="userInfo.hasPermission('612173767866580992')" type="primary" @click="submit('handle')"> 保存 </el-button>
    </div>
  </el-card>

  <!-- <el-card :style="{ marginTop: `20px`, height: `${height / 2 - 10}px` }">
    <template #header>
      <h3>事件自动关闭配置</h3>
    </template>
    <el-scrollbar :height="`${height / 2 - 10 - 40 - 100}px`">
      <el-form> -->
  <!-- <el-form-item label="是否使用默认配置:" :label-width="formLabelWidth">
          <div>
            <el-checkbox v-model="closeConfig.useGlobalDefault">默认配置</el-checkbox>
          </div>
          <div class="allocation" v-show="closeConfig.useGlobalDefault">
            <span> 自动关闭：{{ closeConfig.autoCloseTime }}分钟 </span>
          </div>
        </el-form-item> -->
  <!-- <el-form-item label="是否自动关闭:" :label-width="formLabelWidth">
          <el-switch v-model="closeConfig.autoClose"> </el-switch>
        </el-form-item>
        <el-form-item v-show="closeConfig.autoClose" label="自动关闭时限:" :label-width="formLabelWidth">
          <el-input-number v-model="day" :min="0" :max="1000000000000000" style="margin-right: 10px"></el-input-number>
          day
          <el-input-number v-model="hour" :min="0" :max="23" style="margin: 0 10px"></el-input-number>
          h
          <el-input-number v-model="minute" :min="0" :max="59" style="margin: 0 10px"></el-input-number>
          min
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div style="text-align: center"> -->
  <!-- 事件自动关闭时间配置 -->
  <!-- <el-button v-if="userInfo.hasPermission('612173800296939520')" type="primary" @click="submit('close')"> 保存 </el-button>
    </div>
  </el-card> -->
</template>
<script lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { eventClose, eventHandler, getAllEventDefault } from "@/views/pages/apis/eventConfig.ts";
import { getGroupUsersByPage, getUserGroupByPage } from "@/views/pages/apis/userGroup.ts";
import getUserInfo from "@/utils/getUserInfo";

import { getUserGroupByPermissionIds } from "@/api/personnel";

import { 安全管理中心_用户组管理_可读, 智能事件中心_用户组_分配工单 } from "@/views/pages/permission";
// const userInfo =
export default {
  inject: ["height"],
  data() {
    return {
      formLabelWidth: "130px",
      handle: {
        global: false, //是否全局配置页面
        autoDistribution: false, //事件处理自动分配
        useGlobalDefault: false, //是否使用全局配置
        userId: "", //处理对象人员id
        groupId: "", //处理对象组id
      },
      closeConfig: {
        global: false, //是否全局配置页面
        autoClose: false, //事件自动关闭
        autoCloseTime: "", //事件自动关闭时间
        useGlobalDefault: false, //是否使用全局配置
      },
      userInfo: getUserInfo(),
      day: "",
      hour: "",
      minute: "",
      value: [],
      userName: "",
      userId: "",
      groupId: "",
      isTrue: false,
      props: {
        checkStrictly: true,
        children: "children",
        label: "name",
        value: "id",
      },
      showComponents: false,
      userGroupList: [],
      userOptions: [],
      userList: [],
    };
  },
  watch: {
    userGroupList(val) {
      this.isTrue = true;
    },
  },
  created() {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    this.$nextTick(() => {
      this.getList();
    });
  },
  mounted() {
    // this.getPriorityList();

    this.getEvent();
  },
  methods: {
    async getList() {
      try {
        const { success: groupSuccess, data: groups, message: groupMessage } = await getUserGroupByPermissionIds({ queryPermissionId: 安全管理中心_用户组管理_可读, verifyPermissionIds: [智能事件中心_用户组_分配工单] });
        if (!groupSuccess) throw new Error(groupMessage);

        const users = await Promise.all(groups.map((v) => getGroupUsersByPage({ id: v.id })));
        this.userGroupList = groups.map((v, i) =>
          Object.assign(v, {
            children: users[i].data.map((userItem) => ({
              checkStrictly: false,
              children: [],
              name: userItem.name + `(${userItem.account}@${userItem.tenantAbbreviation})`,
              id: userItem.id,
            })),

            name: `${v.name}${v.tenantAbbreviation ? "[" + v.tenantAbbreviation + "]" : ""}`,
          })
        );
      } catch (error) {
        this.$message.error(error.message);
      }
    },
    cascaderChange(val) {
      // // console.log(val);
      if (val instanceof Array) {
        let i = 0;
        this.handle = Object.assign(
          this.handle,
          ["groupId", "userId"].reduce((p, k) => {
            const obj = Object.assign({ ...p }, { [k]: val[i] || null });
            i++;
            return obj;
          }, {})
        );
      } else {
        this.handle.groupId = null;
        this.handle.userId = null;
      }
    },
    //获取全局配置事件信息
    getEvent() {
      // this.value = [];
      this.getList();
      getAllEventDefault({}).then((res) => {
        if (res.success) {
          let second = res.data[0]?.eventCloseTenantConfig.autoCloseTimeLimit * 60;
          this.minute = parseInt((second / 60) % 60) || "";
          this.hour = parseInt((second / 60 / 60) % 24) || "";
          this.day = parseInt(second / 60 / 60 / 24) || "";
          this.closeConfig.autoCloseTime = res.data[0]?.eventCloseTenantConfig.autoCloseTimeLimit; //全局配置展示时间
          this.closeConfig.autoClose = res.data[0]?.eventCloseTenantConfig.autoClose;
          this.closeConfig.autoDistribution = res.data[0]?.tenantSystemConfig.autoDistribution;
          this.userId = res.data[0]?.tenantSystemConfig.userId;
          this.groupId = res.data[0]?.tenantSystemConfig.groupId;
          this.handle.userId = res.data[0]?.tenantSystemConfig.userId;
          this.handle.groupId = res.data[0]?.tenantSystemConfig.groupId;
          this.value = [this.groupId, this.userId].filter((v) => v);
          this.$forceUpdate();
        }
      });
    },
    async submit(type) {
      let minutes = this.day * 24 * 60 + this.hour * 60 + Number(this.minute);
      try {
        switch (type) {
          case "close":
            const { success, message } = await eventClose({
              global: this.closeConfig.global, //是否全局配置页面
              autoClose: this.closeConfig.autoClose, //事件自动关闭
              useGlobalDefault: this.closeConfig.useGlobalDefault, //是否使用全局配置
              autoCloseTime: this.closeConfig.autoClose ? minutes : 0, //事件自动关闭时间
              tenantId: this.userInfo.currentTenantId,
            });
            if (!success) throw new Error(message);
            this.$message.success("操作成功");
            this.getEvent();
            break;
          case "handle":
            const { success: handelSuccess, message: handelMsg } = await eventHandler({
              global: this.handle.global, //是否全局配置页面
              useGlobalDefault: this.handle.useGlobalDefault, //是否使用全局配置
              autoDistribution: this.handle.autoDistribution, //是否使用自动分配
              userId: this.handle.userId,
              groupId: this.handle.groupId,
              tenantId: this.userInfo.currentTenantId,
            });
            if (!handelSuccess) throw new Error(handelMsg);
            this.$message.success("操作成功");
            this.getEvent();
            break;
          default:
            break;
        }
      } catch (error) {
        this.$message.error(error.message);
      }
    },
  },
};
</script>
<style scoped lang="scss">
.elstyle-card {
  height: 100%;
}

.elstyle-card__body {
  display: flex;
  padding: 15px 0;
  box-sizing: border-box;
  flex-direction: column;
  > div {
    flex: none;
    width: 100%;
    h3 {
      color: #65acff;
      font-weight: 700;
      font-size: 14px;
      border-bottom: 2px solid #f0f0f0;
      padding: 0 15px 15px;
      box-sizing: border-box;
    }
    .elstyle-form {
      padding: 20px;
      box-sizing: border-box;
    }
    .allocation {
      width: 30%;
      background: #ddd;
      padding: 0 20px;
      box-sizing: border-box;
    }
  }
  > div:first-child {
    margin-bottom: 20px;
    border-bottom: 20px solid #f0f0f0;
  }
}
</style>
