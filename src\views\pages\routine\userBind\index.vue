<template>
  <el-scrollbar :height="height">
    <el-card>
      <div class="md:tw-col-span-2">
        <div class="tw-w-full tw-px-4 sm:tw-px-0">
          <h3 class="tw-text-base tw-font-semibold tw-leading-6 dark:tw-text-white">账号绑定</h3>
          <p class="tw-mt-1 tw-text-sm tw-text-gray-600 dark:tw-text-slate-300">平台绑定第三方账号可用作身份验证</p>
        </div>
      </div>
      <el-row>
        <el-col>
          <div class="">
            <div :class="userInfoClassNames">
              <div class="tw-mr-2">
                <Icon color="inherit" size="42" name="fab-faGithub" />
              </div>
              <div class="tw-w-full">
                <div class="tw-text-base dark:tw-text-white">绑定Github</div>
                <div class="tw-flex tw-justify-between">
                  <div class="tw-inline-block tw-text-gray-400">{{ "未" }}绑定Github</div>
                  <el-link v-if="authPlats.map((v) => v).includes(loginChannels.GIT_HUB)" type="danger" :underline="false" @click="unbindingChannels(loginChannels.GIT_HUB)">解除绑定</el-link>
                  <el-link v-else type="primary" :underline="false" @click="bindingChannels(loginChannels.GIT_HUB)">绑定账号</el-link>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </el-scrollbar>
</template>

<script setup lang="ts" name="routine/userInfo/bindAccount">
import { inject, ref, onMounted } from "vue";
import type { Ref } from "vue";
import { loginChannels, delAuthPlats, getBindingGithubInfo, getAuthPlats } from "@/api/system";
import { ElMessage } from "element-plus";
// import { bindPasswordWizard } from "./bindWizard";
const height = inject<Ref<number>>("height", ref(100));
const userInfoClassNames = ref<string>("tw-border-b tw-border-solid tw-border-gray-200 tw-p-2 tw-pt-6 tw-flex tw-items-center");

const authPlats = ref<(keyof typeof loginChannels)[]>([]);
async function handleStateRefresh() {
  const { success, message, data } = await getAuthPlats({});
  if (success) {
    if (data) authPlats.value = data;
  } else throw Object.assign(new Error(message), { success, data });
}

async function bindingChannels(channels: keyof typeof loginChannels) {
  try {
    switch (channels) {
      case loginChannels.GIT_HUB:
        const { success, message, data } = await getBindingGithubInfo();
        if (success) {
          ElMessage.success("Github绑定成功");
        } else throw Object.assign(new Error(message), { success, data });
        break;
      case loginChannels.WECHAT:
        break;
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    handleStateRefresh();
  }
}
async function unbindingChannels(channels: keyof typeof loginChannels) {
  try {
    const { success, message, data } = await delAuthPlats({ platCode: channels });
    if (success) {
      ElMessage.success("Github绑定成功");
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    handleStateRefresh();
  }
}

onMounted(async () => {
  await handleStateRefresh();
});
</script>

<style lang="scss" scoped></style>
