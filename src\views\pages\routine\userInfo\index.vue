<template>
  <div></div>
  <el-scrollbar :height="height">
    <el-card class="info-item-view">
      <div class="md:tw-col-span-2">
        <div class="tw-w-full tw-px-4 sm:tw-px-0">
          <h3 class="tw-text-base tw-font-semibold tw-leading-6 dark:tw-text-white">{{ $t("personalInformation.User") }}</h3>
          <p class="tw-mt-1 tw-text-sm tw-text-gray-600 dark:tw-text-slate-300">{{ $t("personalInformation.Personal") }}</p>
        </div>
      </div>
      <el-row>
        <el-col :span="4" class="tw-border-r tw-border-solid tw-border-gray-200 tw-pr-4">
          <div class="tw-hidden md:tw-col-span-2 md:tw-block">
            <!-- tw-w-8/12 -->
            <div class="tw-m-auto tw-my-8 tw-w-full tw-px-4 sm:tw-px-0">
              <el-avatar :size="140" fit="fill" style="font-size: 30px">{{ userInfo.username.slice(-2) }}</el-avatar>
              <!-- <el-avatar class="tw-flex tw-h-auto tw-w-full"
              :size="140" fit="fill" style="cursor: pointer"><img :src="userInfo.avatar" class="tw-h-auto tw-w-full" /></el-avatar> -->
              <!-- <el-dropdown class="tw-mx-auto tw-block tw-w-full" trigger="click" @command="$event.command()">
                <el-avatar class="tw-flex tw-h-auto tw-w-full" :size="140" fit="fill" style="cursor: pointer"><img :src="userInfo.avatar" class="tw-h-auto tw-w-full" /></el-avatar>
                <template #dropdown>
                  <el-dropdown-menu class="tw-grid tw-w-[200px] tw-grid-cols-4">
                    <el-dropdown-item class="tw-col-span-1 tw-p-0 tw-px-[5px]" v-for="srcItem in srcList" :key="srcItem.url" :command="{ command: () => setUserAvatar(srcItem.url) }">
                      <el-avatar :size="40" fit="fill" :src="srcItem.src" style="cursor: pointer"></el-avatar>
                    </el-dropdown-item>
                    <el-dropdown-item class="tw-col-span-4" divided :command="{ command: () => addAvatar() }">
                      <el-icon><Upload /></el-icon>上传头像
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown> -->
            </div>
          </div>
          <el-divider />
          <!-- <div class="tw-pb-8">
            <el-select v-model="userInfo.gender" class="m-2 tw-w-full" placeholder="请选择性别" size="default" @change="handleGenderAndBirthday">
              <el-option v-for="item in genderOption" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div> -->
          <!-- <div>
            <el-date-picker v-model="userInfo.birthday" type="date" placeholder="请选择出生日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" size="default" @change="handleGenderAndBirthday" class="tw-w-full" />
          </div> -->
        </el-col>
        <el-col :span="20" class="tw-pl-4">
          <div class="tw-p-1">
            <div :class="userInfoClassNames">
              <div class="tw-text-base dark:tw-text-white">{{ $t("personalInformation.Name") }}</div>
              <div class="tw-flex tw-justify-between">
                <div class="tw-inline-block tw-text-gray-400">{{ userInfo.username }}</div>
                <el-button link type="primary" @click="putUsernameWizard({ id: userInfo.username, label: '姓名' }).then(() => userInfo.updateInfo())">{{ $t("personalInformation.Edit Name") }}</el-button>
              </div>
            </div>
            <!-- <div :class="userInfoClassNames">
              <div class="tw-text-base dark:tw-text-white">昵称</div>
              <div class="tw-flex tw-justify-between">
                <div class="tw-inline-block tw-text-gray-400">{{ userInfo.nickname }}</div>
                <el-button link type="primary" @click="putNicknameWizard(userInfo.nickname).then(() => userInfo.updateInfo())">修改昵称</el-button>
              </div>
            </div> -->
            <div :class="userInfoClassNames">
              <div class="tw-text-base dark:tw-text-white">{{ $t("personalInformation.Username login ID") }}</div>
              <div class="tw-flex tw-justify-between">
                <!-- {{ userInfo }} -->
                <div class="tw-inline-block tw-text-gray-400">
                  {{ userInfo.account + "@" + userInfo.tenantAbbreviation }}
                  <span class="tw-text-[var(--el-color-danger)]" v-if="userInfo.accountExpirationDate && Number(accountExpirationDateDay) <= 30">（您的账号有效期还剩{{ accountExpirationDateDay }}天，请尽快找管理员申请延长有效期。）</span>
                </div>
                <el-button link type="primary" @click="bindAccountWizard(mfaMethods).then(() => userInfo.updateInfo())">{{ $t("personalInformation.Edit Username login ID") }}</el-button>
              </div>
            </div>
            <div :class="userInfoClassNames">
              <div class="tw-text-base dark:tw-text-white">{{ $t("personalInformation.Mobile") }}</div>
              <div class="tw-flex tw-justify-between">
                <div class="tw-inline-block tw-text-gray-400">{{ userInfo.phone }}</div>
                <el-button link type="primary" @click="bindPhoneWizard(mfaMethods).then(() => userInfo.updateInfo())">{{ $t("personalInformation.Edit Mobile") }}</el-button>
              </div>
            </div>
            <div :class="userInfoClassNames">
              <div class="tw-text-base dark:tw-text-white">{{ $t("personalInformation.Email") }}</div>
              <div class="tw-flex tw-justify-between">
                <div class="tw-inline-block tw-text-gray-400">{{ userInfo.email }}</div>
                <el-button link type="primary" @click="bindEmailWizard(mfaMethods).then(() => userInfo.updateInfo())">{{ $t("personalInformation.Edit Email") }}</el-button>
              </div>
            </div>
            <div :class="userInfoClassNames">
              <div class="tw-text-base dark:tw-text-white">{{ $t("personalInformation.Time Zone") }}</div>
              <div class="tw-flex tw-justify-between">
                <div class="tw-inline-block tw-text-gray-400">
                  <div v-for="item in timeZone" :key="item.zoneId">
                    <span v-if="userInfo.zoneId == item.zoneId">{{ item.displayName }}</span>
                  </div>
                </div>
                <el-button link type="primary" @click="putUsernameWizard({ id: userInfo.zoneId, label: '时区' }).then(() => userInfo.updateInfo())">{{ $t("personalInformation.Edit Time Zone") }}</el-button>
              </div>
            </div>
            <div :class="userInfoClassNames">
              <div class="tw-text-base dark:tw-text-white">{{ $t("personalInformation.Language") }}</div>
              <div class="tw-flex tw-justify-between">
                <div class="tw-inline-block tw-text-gray-400">
                  <div style="margin-right: 2px" v-for="itemA in localesOption" :key="itemA.value">
                    <div v-if="itemA.value == userInfo.language" :style="{ background: `url(${itemA.icon}) no-repeat left / auto`, paddingLeft: '30px' }">{{ itemA.label }}</div>
                  </div>
                </div>
                <el-button link type="primary" @click="putUsernameWizard({ id: userInfo.language, label: '语言' }).then(() => userInfo.updateInfo())">{{ $t("personalInformation.Edit Language") }}</el-button>
              </div>
            </div>
            <!-- <div :class="userInfoClassNames">
              <div class="tw-text-base dark:tw-text-white">注销</div>
              <div class="tw-flex tw-justify-between">
                <div class="tw-inline-block tw-text-gray-400">注销账号操作不可恢复，请谨慎操作！</div>
                <el-button link type="danger">注销账号</el-button>
              </div>
            </div> -->
          </div>
        </el-col>
      </el-row>
    </el-card>
  </el-scrollbar>
</template>

<script setup lang="ts" name="routine/userInfo/data">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { onMounted, onBeforeUnmount, ref, reactive, nextTick, provide, inject, watch, computed } from "vue";
import type { Ref, VNode } from "vue";
import { useFileDialog } from "@vueuse/core";
import { sizes } from "@/utils/common";
import { formatterDate, formatDateToString } from "@/utils/date";
import FormItem from "@/components/formItem/index.vue";
import { InputType } from "@/components/formItem";

import { useSiteConfig } from "@/stores/siteConfig";
import { useSuperInfo } from "@/stores/infoBySuper";

import Cropper from "@/components/cropper/index.vue";
import { ElMessage } from "element-plus";
import { Upload, RefreshRight, RefreshLeft, Refresh, EditPen } from "@element-plus/icons-vue";

import { bindAccountWizard, bindPasswordWizard, bindPhoneWizard, bindEmailWizard, putUsernameWizard, putNicknameWizard } from "../bindWizard";
// Api
import { getAvatarUrlList, getUserLogs, getAvatar, setAvatar, getMFAMethods, getAuthPlats, delAuthPlats, getBindingGithubInfo, loginChannels, MFAMethod, setUserInfo } from "@/api/system";
import type { GetUserInfo as DataItem, GetUserLogs } from "@/api/system";
import { gender, genderOption } from "@/api/personnel";
import { EditorType } from "@/views/common/interface";
import moment from "moment";
import { ErrorTypes } from "vue-router";

import { useAdminInfo } from "@/stores/infoByAdmin";
import { useUsersInfo } from "@/stores/infoByUsers";
import { superBaseRoute, adminBaseRoute, usersBaseRoute } from "@/router/common";
import { localesOption } from "@/api/locale.ts";
import _timeZone from "@/views/pages/common/contactsZone.json";
import { id } from "element-plus/es/locale";
const timeZone = ref(_timeZone);
/* 头像上传 */
const cropper = ref<InstanceType<typeof Cropper>>();
const { files, open, reset } = useFileDialog({ multiple: false, accept: "image/gif, image/jpg, image/jpeg, image/bmp, image/png, image/webp" });
const file = ref<string | null>(null);
const preview = ref<{ div: { width: string; height: string }; w: number; h: number; url: string; img: { width: string; height: string; transform: string }; html: string }>({
  div: { width: "", height: "" },
  w: 0,
  h: 0,
  url: "",
  img: { width: "", height: "", transform: "" },
  html: "",
});
async function addAvatar() {
  reset();
  await nextTick();
  open();
}

watch(files, (files) => {
  if (files instanceof FileList) {
    const _file = files.item(0);
    if (!_file) return;
    if (file.value) URL.revokeObjectURL(file.value);
    file.value = URL.createObjectURL(_file.slice(0, _file.size, _file.type));
  }
});

function clone() {
  if (file.value) URL.revokeObjectURL(file.value);
  file.value = null;
}

async function addUserAvatar() {
  if (!cropper.value) return;
  const file = await cropper.value.getCropBlob();
  if (!file) return ElMessage.error("浏览器不支持或截取错误！");
  try {
    const { success, message, data } = await setAvatar({ file: new File([file], `avatar_${moment().format("YYYYMMDDHHmmssSSSS")}.jpg`, { type: file.type }) });
    if (success) ElMessage.success("头像设置成功！");
    else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    return;
  }
  clone();
  await handleStateRefresh();
}

async function setUserAvatar(filename: string) {
  await nextTick();
  try {
    const { success, message, data } = await setAvatar({ filename });
    if (success) {
      ElMessage.success("头像修改成功！");
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    await userInfo.value.updateInfo();
  }
}
/*  */
/*  */
/*  */

const width = inject<Ref<number>>("width", ref(100));
const height = inject<Ref<number>>("height", ref(100));

// const userInfo = useSuperInfo();
const siteConfig = useSiteConfig();

const userInfo = computed(() => {
  console.log(siteConfig.current);
  console.log(superBaseRoute.name);
  console.log(adminBaseRoute.name);
  console.log(usersBaseRoute.name);
  switch (siteConfig.current) {
    case superBaseRoute.name:
      return useSuperInfo();
    case adminBaseRoute.name:
      return useAdminInfo();
    case usersBaseRoute.name:
      return useUsersInfo();
    default:
      return useSuperInfo();
  }
});
const userInfoClassNames = ref<string>("tw-border-b tw-border-solid tw-border-gray-200 tw-p-2 tw-pt-6");

const scrollbarRef = ref<InstanceType<typeof import("element-plus").ElScrollbar>>();

const accountExpirationDateDay = ref<number | string>("");

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: Required<Omit<T, "platform" | "roles" | "password">>;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: {}, value: T[keyof T]) => string | VNode }[];
  data: GetUserLogs[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<DataItem>>({
  loading: false,
  select: [],
  current: {
    id: "",
    userId: "",
    name: "",
    // nickname: "",
    account: "",
    phone: "",
    email: "",

    passwordDate: "",

    gender: gender.SECRET,
    birthday: "",
    profilePicture: "",

    busy: false,
    improved: false,
    frozen: false,
    frozenExpire: "",
    frozenNote: "",

    owner: false,
    superAdmin: false,
    roleIds: [],
    multiTenant: false,

    tenants: [],

    createdTime: "",
    updatedTime: "",
    tenantId: "",
  },
  filter: {},
  expand: [],
  search: {
    keyword: "",
  },
  column: [
    /* 列 */
    { key: "id", label: "ID" },
    { key: "name", label: "名称" },
    { key: "createdTime", label: "创建日期", formatter: (_row, _col, v) => formatterDate(v as string) },
    { key: "updatedTime", label: "修改日期", formatter: (_row, _col, v) => formatterDate(v as string) },
  ],
  data: [],
  page: 1,
  size: 20,
  sizes,
  total: 0,
});

provide("#PARAMS", { "#TYPE": EditorType.Mod });
provide("#WIDTH", width);

const srcList = ref<{ url: string; src: string }[]>([]);
const mfaMethods = ref<(keyof typeof MFAMethod)[]>([]);
const authPlats = ref<(keyof typeof loginChannels)[]>([]);

async function handleStateRefresh() {
  if (state.loading) return;
  state.loading = true;
  try {
    const [/*avatar*/ /*logs,*/ mfa /*auth*/] = await Promise.all([/*getAvatarUrlList({}),*/ /*getUserLogs({ paging: { page: 1, size: 20 } }),*/ getMFAMethods({}) /*getAuthPlats({})*/]);
    await Promise.all([
      // (async ({ success, message, data }) => {
      //   if (success) {
      //     for (let index = 0; index < srcList.value.length; index++) URL.revokeObjectURL(srcList.value[index].url);
      //     srcList.value = await Promise.all((data instanceof Array ? data : []).map(async (v) => ({ url: v, src: await getAvatar({ filePath: v }) })));
      //   } else throw Object.assign(new Error(message), { success, data });
      // })(avatar),
      // (async ({ success, message, data }) => {
      //   if (success) {
      //     state.page = 1;
      //     state.size = 20;
      //     state.total = state.total + data.length;
      //     state.data.push(...(data instanceof Array ? data : []).map((v) => ({ ...v, loginTime: formatDateToString(v.loginTime) })));
      //   } else throw Object.assign(new Error(message), { success, data });
      // })(logs),
      (async ({ success, message, data }) => {
        if (success) {
          if (data instanceof Array) mfaMethods.value = Array.from(new Set([...data]));
        } else throw Object.assign(new Error(message), { success, data });
      })(mfa),
      // (async ({ success, message, data }) => {
      //   if (success) {
      //     if (data instanceof Array) authPlats.value = data;
      //   } else throw Object.assign(new Error(message), { success, data });
      // })(auth),
    ]);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    state.loading = false;
  }
}
async function getNextPage() {
  if (state.loading) return;
  if (state.total !== state.page * state.size) return;
  state.loading = true;
  try {
    const { success, message, data } = await getUserLogs({ paging: { page: ++state.page, size: state.size } });
    if (success) {
      // state.page = Number(page);
      // state.size = Number(size);
      // state.total = Number(total);
      state.total = Number(state.total + data.length);
      state.data.push(...(data instanceof Array ? data : []).map((v) => ({ ...v, loginTime: formatDateToString(v.loginTime) })));
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    await handleStateRefresh();
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    state.loading = false;
  }
}
async function bindingChannels(channels: keyof typeof loginChannels) {
  try {
    switch (channels) {
      case loginChannels.GIT_HUB:
        const { success, message, data } = await getBindingGithubInfo();
        if (success) {
          ElMessage.success("Github绑定成功");
        } else throw Object.assign(new Error(message), { success, data });
        break;
      case loginChannels.WECHAT:
        break;
    }
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    handleStateRefresh();
  }
}
async function unbindingChannels(channels: keyof typeof loginChannels) {
  try {
    const { success, message, data } = await delAuthPlats({ platCode: channels });
    if (success) {
      ElMessage.success("Github绑定成功");
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    handleStateRefresh();
  }
}

async function handleGenderAndBirthday(v: String) {
  console.log(userInfo.value.language, "userInfo :>> ", userInfo.value.name);
  try {
    const { success, message } = await setUserInfo({ gender: userInfo.value.gender, birthday: userInfo.value.birthday });
    if (success) {
      ElMessage.success("修改成功");
    } else throw Object.assign(new Error(message), { success });
    await userInfo.value.updateInfo();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  }
}
onBeforeUnmount(() => {
  if (file.value) URL.revokeObjectURL(file.value);
});
onMounted(async () => {
  await handleStateRefresh();

  await handleAccountExpirationDateTip();
});

async function handleAccountExpirationDateTip() {
  if (userInfo.value.accountExpirationDate) accountExpirationDateDay.value = parseInt(((Number(userInfo.value.accountExpirationDate) - new Date().getTime()) / (1000 * 60 * 60 * 24)).toString());
}
</script>

<style lang="scss" scoped>
.user-info-flex-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
<style lang="scss" scoped>
// .user-info-base .user-nickname {
//   font-size: 22px;
//   color: var(--el-text-color-primary);
//   text-align: center;
//   padding: 8px 0;
// }

.user-info-base .user-other {
  color: var(--el-text-color-regular);
  font-size: 14px;
  text-align: center;
  line-height: 20px;
}
.user-info-form {
  width: 100%;
  padding: 30px;
}
.info-item-view {
  overflow: hidden;
}

.user-info-title {
  font-size: 14px;
  font-family:
    PingFang SC-Medium,
    PingFang SC;
  font-weight: 500;
  color: #1d2129;
  line-height: 35px;
}
</style>
