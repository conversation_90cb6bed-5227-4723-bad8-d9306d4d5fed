<template>
  <el-scrollbar :height="height">
    <el-card>
      <div class="md:tw-col-span-2">
        <div class="tw-w-full tw-px-4 sm:tw-px-0">
          <h3 class="tw-text-base tw-font-semibold tw-leading-6 dark:tw-text-white">{{ $t("personalInformation.Login Log") }}</h3>
          <p class="tw-mt-1 tw-text-sm tw-text-gray-600 dark:tw-text-slate-300">{{ $t("personalInformation.Your login history") }}</p>
        </div>
      </div>
      <el-row class="tw-mt-4">
        <el-col class="tw-mb-4 tw-border-b tw-border-solid tw-border-gray-300 tw-pb-3" v-for="logData in state.data" :key="logData.id">
          <p class="tw-mb-2 tw-leading-8">
            <span class="tw-mr-2">{{ logData.loginTime }}</span>
            <el-tag size="small" effect="dark" class="tw-mr-2"> {{ "IP:" + logData.ip }}</el-tag>
            <el-tag size="small" effect="dark" class="tw-mr-2"> {{ $t("personalInformation.Authorized end") + ":" + logData.clientName }}</el-tag>
          </p>
          <div class="tw-flex tw-justify-between tw-leading-8">
            <span class="tw-text-sm tw-text-gray-400"> {{ "UA:" + logData.userAgent }}</span>
            <span class="tw-text-sm tw-text-gray-400">{{ $t("personalInformation.Login address") + ":" + logData.location }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </el-scrollbar>
</template>

<script setup lang="ts" name="routine/userInfo/loginLog">
import { inject, ref, onMounted, reactive } from "vue";
import type { Ref } from "vue";
import { getUserLogs, GetUserLogs } from "@/api/system";
import { ElTag } from "element-plus";
import { sizes } from "@/utils/common";
import { timeFormat } from "@/utils/date";

// import { useI18n } from "vue-i18n";
const height = inject<Ref<number>>("height", ref(100));

// const { t } = useI18n();

interface StateData<T> {
  data: T[];
  loading: Boolean;
  column: {
    key: keyof T;
    label?: string;
    align?: "left" | "center" | "right";
    width?: number;
    showOverflowTooltip?: boolean;
    formatter?: (row: T, col: {}, value: T[keyof T]) => string | import("vue").VNode;
  }[];
  total: number;
  page: number;
  pageSize: number;

  sizes: typeof sizes;
}

const state = reactive<StateData<GetUserLogs>>({
  loading: false,
  data: [],
  column: [],
  total: 0,
  page: 1,
  pageSize: 50,
  sizes,
});

async function handleStateRefresh() {
  state.loading = true;
  try {
    const params = {
      paging: { page: state.page, size: state.pageSize },
    };
    const { success, data } = await getUserLogs(params);
    if (success) {
      if (data instanceof Array)
        state.data = data.map((v) => {
          return { ...v, loginTime: timeFormat(Number(v.loginTime)) };
        });
      state.loading = false;
    }
  } catch (e) {
    state.loading = false;
  }
}

onMounted(async () => {
  await handleStateRefresh();
});
</script>

<style lang="scss" scoped></style>
