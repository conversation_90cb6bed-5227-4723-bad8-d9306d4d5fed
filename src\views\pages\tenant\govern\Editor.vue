<template>
  <!-- 对话框表单 -->
  <component :is="ComponentRender" v-model:visible="data.visible" :handle-cancel="handleCancel">
    <template #header>
      {{ `${$params.id ? t("glob.edit") : t("glob.add")}${props.title}` }}
    </template>
    <template #default="{ width }">
      <!-- width -->
      <FormModel ref="formRef" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @submit="handleFinish" class="editor-tenant">
        <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.customers name')" tooltip="" prop="name" :rules="[buildValidatorData({ name: 'required', title: `${props.title}名称` })]" :required="true">
          <el-input v-model="form.name" :placeholder="$t('customers.Please enter customer name')" @change="form.name = form.name.trim()" />
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.customers abbreviation')" tooltip="" prop="abbreviation" :rules="[buildValidatorData({ name: 'required', title: `${props.title}名称缩写` }), buildValidatorData({ name: 'ident', title: `${props.title}名称缩写` })]">
          <el-input v-model="form.abbreviation" :placeholder="$t('customers.Please enter your customer name abbreviation')" />
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.Is active')" tooltip="" prop="activated" :rules="[]">
          <el-checkbox label="" v-model="form.activated"> </el-checkbox>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.synchronize NetCare')" tooltip="" prop="synetcare" :rules="[]">
          <el-icon @click="netHelp" style="color: #2a8bf5; cursor: pointer; position: absolute; left: -35px"><QuestionFilled /></el-icon>
          <el-checkbox label="" v-model="form.synetcare"> </el-checkbox>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.customers type')" tooltip="" prop="fax" :rules="[]">
          <el-select v-model="form.tenantType" class="tw-w-full" :placeholder="$t('customers.Please select customer type')" filterable clearable>
            <el-option label="NETCARE" value="NETCARE"></el-option>
            <el-option label="NETSTAR" value="NETSTAR"></el-option>
            <el-option label="IT外包" value="IT外包"></el-option>
            <el-option label="企业数通网管" value="EDNM"></el-option>
            <el-option label="DICT" value="DICT"></el-option>
          </el-select>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.Service language')" tooltip="" prop="language" :rules="[]">
          <el-select v-model="form.language" class="tw-w-full" :placeholder="$t('customers.Please select the service language')" filterable clearable>
            <el-option v-for="item in localesOption" :key="`locale-${item.value}`" :label="item.label" :value="item.value">
              <div :style="{ background: `url(${item.icon}) no-repeat left / auto calc(100% - 12px)`, paddingLeft: '30px' }">{{ item.label }}</div>
            </el-option>
          </el-select>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.customer time zone')" tooltip="" prop="zoneId" :rules="[]">
          <Timezone v-model="form.zoneId"></Timezone>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.platform version')" tooltip="" prop="systemEdition" :rules="[]">
          <el-select v-model="form.systemEdition" class="tw-w-full" :placeholder="$t('customers.Please select platform version')" filterable clearable>
            <el-option v-for="item in systemEditionOption" :key="`locale-${item.code}`" :label="item.name" :value="item.code"> </el-option>
          </el-select>
        </FormItem>
        <el-divider />
        <el-scrollbar max-height="400px" style="width: 100%">
          <div class="scrollbar__view">
            <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.address')" tooltip="" prop="address" :rules="[]">
              <el-input v-model="form.address" :placeholder="$t('customers.Please enter the address')" />
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.phone')" tooltip="" prop="tenantPhone" :rules="[[buildValidatorData({ name: 'required', title: `${props.title}手机号` })], buildValidatorData({ name: 'tenantPhone', title: `${props.title}手机号` })]">
              <el-input v-model="form.tenantPhone" :placeholder="$t('customers.Please enter your phone number')" />
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.zip code')" tooltip="" prop="postcode" :rules="[]">
              <el-input type="number" v-model="form.postcode" :placeholder="$t('customers.Please enter a post code')" />
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.email')" tooltip="" prop="email" :rules="[[buildValidatorData({ name: 'required', title: `${props.title}邮箱` })], buildValidatorData({ name: 'email', title: `${props.title}邮箱` })]">
              <el-input v-model="form.email" :placeholder="$t('customers.Please enter email address')" />
            </FormItem>

            <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.fax')" tooltip="" prop="fax" :rules="[]">
              <el-input type="number" v-model="form.fax" :placeholder="$t('customers.Please enter the fax')" />
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.Customer signing place')" tooltip="" prop="language" :rules="[]" style="margin-top: 10px">
              <el-select v-model="form.signAddress" class="tw-w-full" :placeholder="$t('customers.Please select the customer signing place')" filterable clearable>
                <el-option v-for="item in Clientsigningplace" :key="`locale-${item.value}`" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.Customer signing place-2')" tooltip="" prop="signAddress2" :rules="[]" style="margin-top: 10px">
              <el-input v-model="form.signAddress2" :placeholder="$t('customers.Please select the customer signing place-2')" />
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.industry')" tooltip="" prop="industry" :rules="[]" style="margin-top: 10px">
              <el-select v-model="form.industry" class="tw-w-full" :placeholder="$t('customers.Please select an industry')" filterable clearable>
                <el-option v-for="item in industry" :key="`locale-${item.value}`" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.nature')" tooltip="" prop="nature" :rules="[]" style="margin-top: 10px">
              <el-input v-model="form.nature" :placeholder="$t('customers.Please enter the nature')" />
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.customer channels')" tooltip="" prop="channel" :rules="[]" style="margin-top: 10px">
              <el-select v-model="form.channel" class="tw-w-full" :label="$t('customers.customer channels')" filterable clearable>
                <el-option v-for="item in OTCR" :key="`locale-${item.value}`" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </FormItem>
            <FormItem :span="width > 600 ? 12 : 24" :label="$t('customers.described')" tooltip="" prop="note" :rules="[]" style="margin-top: 10px">
              <el-input v-model="form.note" :placeholder="$t('customers.please enter a description')" />
            </FormItem>
            <FormItem v-if="!$params.id" :span="24" :label="$t('customers.Select a security directory')" tooltip="" prop="containerId" :rules="[{ required: true, validator: handleValidContainerId }]" style="margin-top: 10px">
              <treeAuth ref="treeAuthRef" :treeStyle="treeStyle"></treeAuth>
            </FormItem>
          </div>
        </el-scrollbar>
      </FormModel>
    </template>
    <template #footer>
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <el-button type="primary" @click="handleSubmit()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
    </template>
  </component>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { toRaw, readonly, reactive, ref, nextTick, computed, h, renderSlot, getCurrentInstance, createVNode } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep, find, findIndex } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { Back, QuestionFilled } from "@element-plus/icons-vue";
import { buildTypeHelper } from "@/utils/type";
import { useConfig } from "@/stores/config";
import moment from "moment";

import { buildValidatorData, validatorPattern } from "@/utils/validate";

import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import treeAuth from "@/components/treeAuth/index.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import Timezone from "@/components/formItem/timezone/index.vue";

import getUserInfo from "@/utils/getUserInfo";

import { getSystemVersion } from "@/api/system";

import { locales, localesOption, Clientsigningplace, industry, OTCR, platformVersion } from "@/api/locale";
import { Zone as zones } from "@/utils/zone";

import { priority, priorityOption } from "@/views/pages/apis/event";
import { TenantItem, getUserByPlatform, getTenantList, type UserItem, getGroup } from "@/api/personnel";
import { getUserGroups, jionUserGroups } from "@/views/pages/apis/deviceBatchManage";

import { getAvatar } from "@/api/system";
import { el } from "element-plus/es/locale";

const { log } = console;

const dataLoading = ref(false);
const netHelpdialog = ref(false);
const dataList = ref<Partial<{ id: string; name: string; account: string; phone: string; email: string; [key: string]: unknown }>[]>([]);
async function remoteMethod(query: string) {
  dataList.value = [];
  if (!query) return;
  try {
    dataLoading.value = true;
    form.value.owner = { id: "new", name: "", account: "", phone: "", email: "" };
    if ((<{ pattern: RegExp; message: string }>validatorPattern.email).pattern.test(query)) {
      Object.assign(form.value.owner, { email: query });
    } else if ((<{ pattern: RegExp; message: string }>validatorPattern.mobile).pattern.test(query)) {
      Object.assign(form.value.owner, { phone: query });
    } else if ((<{ pattern: RegExp; message: string }>validatorPattern.account).pattern.test(query)) {
      Object.assign(form.value.owner, { account: query });
    }
    const { success, message, data } = await getUserByPlatform({ keyword: query, paging: { pageNumber: 1, pageSize: 30 } });
    if (!success) throw Object.assign(new Error(message), { success, data });
    const res = await Promise.all(
      (data instanceof Array ? data : []).map(async (v) => {
        return { id: v.id, name: v.name, account: v.account, phone: v.phone, email: v.email, avatar: await getAvatar({ filePath: v.profilePicture }), tenantAbbreviation: v.tenantAbbreviation };
      })
    );
    dataList.value.push(...res);
    if (!form.value.owner.account && !form.value.owner.phone && !form.value.owner.email) return;
    if (!dataList.value.length) dataList.value = [{ ...form.value.owner }];
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    dataLoading.value = false;
  }
}

const userInfo = getUserInfo();
const config = useConfig();
const { t } = useI18n({ useScope: "global" });
const formRef = ref<InstanceType<typeof FormModel>>();
const treeAuthRef = ref<InstanceType<typeof FormModel>>();
const ctx = getCurrentInstance();
if (!ctx) throw new Error("Component context initialization failed!");
const { appContext } = ctx;

const userGroupIds = ref([]);
const isJoin = ref(false);
const isActivate = ref(false);
const userGroups = ref([]);
const groupProps = ref({
  checkStrictly: true,
  children: "children",
  label: "name",
  value: "id",
  lazy: true,
  multiple: true,
  leaf: "leaf",
  lazyLoad(node: any, resolve: any) {
    const { level } = node;
    //下一层节点
    let nodes: any = [];
    //如果有子节点 或者 为根节点（即首次进入level为0）
    //也有人写成 node.level == 0 作用是一样的

    if (node.level == 1) {
      // 0 代表第一次请求

      //这里setTimeout的目的是 显示加载动画
      setTimeout(() => {
        //调用后端接口 获得返回数据

        getUserGroups({ tenantId: node.value }).then((res: any) => {
          if (res.success) {
            nodes = Array.from(res.data).map((item: any) => ({
              id: item.id,
              name: item.name,
              leaf: level >= 1,
            }));

            resolve(nodes);
          } else {
            resolve(undefined);
          }
        });
        // if (node.children.length < 1) {
        //   node.children = undefined;
        // }
      }, 1);
    } else {
      //如果没有子节点就不发起请求，直接渲染，也避免了点击叶子节点仍然有加载动画的问题
      node.children = [];
      resolve([]);
    }
  },
});

const groupIdList = ref([]);

const treeStyle = ref({
  width: "300px",
  height: "150px",
});

const containerIdS = ref("");
interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 120,
});

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Item {
  baileeTenantId: string; // 受托租户ID
  name: string;
  abbreviation: string;
  tenantPhone: string;
  systemEdition: string;
  zoneId: string;
  language: string;
  note: string;
  address: string;
  ownerUserId: string;
  containerId: string;
  owner: Partial<{
    id: string;
    name: string /* 姓名 */;
    // nickname: string /* 昵称 */;
    account: string /* 账号 */;
    phone: string /* 手机号 */;
    email: string /* 邮箱 */;
    // language: string /* 语言 */;
    // gender: gender /* 性别 */;
    // password: string /* 密码 */;
  }>;
  joinUserGroupIds: Array[];
  joinUserIds: Array[];
  tenantType: string;
  email: string;
  /** 邮编 */
  postcode: string;
  /** 传真 */
  fax: string;
  /** 租户签约地 */
  signAddress: string;
  /** 租户签约地2 */
  signAddress2: string;
  /** 租户行业 */
  industry: string;
  /** 租户性质 */
  nature: string;
  /** 租户渠道 */
  channel: string;
  /** 租户是否运营商 */
  isOperator: boolean;
  /** 租户是否国外客户 */
  isForeign: boolean;
  isJoin: boolean;
  note: string;
  blocked: boolean;
  activated: boolean;
  sendMsg: boolean;
  sendEmail: boolean;
  synetcare: boolean;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  baileeTenantId: buildTypeHelper<Required<Item>["baileeTenantId"]>(""),
  name: buildTypeHelper<Required<Item>["name"]>(""),
  abbreviation: buildTypeHelper<Required<Item>["abbreviation"]>(""),
  tenantPhone: buildTypeHelper<Required<Item>["tenantPhone"]>(""),
  systemEdition: buildTypeHelper<Required<Item>["systemEdition"]>("standard"),
  zoneId: buildTypeHelper<Required<Item>["zoneId"]>("China Standard Time"),
  language: buildTypeHelper<Required<Item>["language"]>("zh-CN"),
  note: buildTypeHelper<Required<Item>["note"]>(""),
  address: buildTypeHelper<Required<Item>["address"]>(""),
  ownerUserId: buildTypeHelper<Required<Item>["ownerUserId"]>(""),
  owner: buildTypeHelper<Required<Item>["owner"]>({ id: "", name: "", account: "", phone: "", email: "" }),
  joinUserGroupIds: buildTypeHelper<Required<Item>["joinUserGroupIds"]>([]),
  joinUserIds: buildTypeHelper<Required<Item>["joinUserIds"]>([]),
  email: buildTypeHelper<Required<Item>["email"]>(""),
  postcode: buildTypeHelper<Required<Item>["postcode"]>(""),
  fax: buildTypeHelper<Required<Item>["fax"]>(""),
  signAddress: buildTypeHelper<Required<Item>["signAddress"]>(""),
  signAddress2: buildTypeHelper<Required<Item>["signAddress2"]>(""),
  industry: buildTypeHelper<Required<Item>["industry"]>(""),
  nature: buildTypeHelper<Required<Item>["nature"]>(""),
  channel: buildTypeHelper<Required<Item>["channel"]>(""),
  isOperator: buildTypeHelper<Required<Item>["isOperator"]>(false),
  isForeign: buildTypeHelper<Required<Item>["isForeign"]>(false),
  isJoin: buildTypeHelper<Required<Item>["isJoin"]>(false),
  note: buildTypeHelper<Required<Item>["note"]>(""),
  blocked: buildTypeHelper<Required<Item>["isForeign"]>(false),
  activated: buildTypeHelper<Required<Item>["isForeign"]>(true),
  tenantType: buildTypeHelper<Required<Item>["tenantType"]>(""),
  containerId: buildTypeHelper<Required<Item>["containerId"]>(""),
  sendMsg: buildTypeHelper<Required<Item>["sendMsg"]>(false),
  sendEmail: buildTypeHelper<Required<Item>["sendEmail"]>(false),
  synetcare: buildTypeHelper<Required<Item>["synetcare"]>(false),
});

const systemEditionOption = ref<Record<"name" | "code", string>[]>([]);

const tenants = ref({
  loading: false,
  base: <TenantItem | null>null,
  option: <TenantItem[]>[],
});

function handleValidContainerId(rule: any, value: any, callback: any) {
  if (treeAuthRef.value && (treeAuthRef.value as any).treeItem.id) {
    form.value.containerId = (treeAuthRef.value as any).treeItem.id;
    return callback();
  } else return callback(new Error("请选择安全容器"));
}

function cascaderChange(node: any) {
  form.value.joinUserGroupIds = node;
}
function filterOpened(val: any) {
  if (val == "basic" || val == "standard" || val == "itOutsourcing") {
    ElMessageBox.confirm("IT外包、标准版、基础版，需要同步到v6。DICT，不需要同步到v6。", "提醒", {
      confirmButtonText: "关闭",
      // cancelButtonText: "关闭",
      type: "warning",
      dangerouslyUseHTMLString: true, // 允许 HTML 标签
      showCancelButton: false,
      showClose: false,
    })
      .then(() => {})
      .catch(() => {});
  }
}

async function remoteTenantsMethod(query: string) {
  tenants.value.option = tenants.value.base ? [tenants.value.base] : [];
  if (!query) return;
  try {
    tenants.value.loading = true;
    const { success, message, data } = await getTenantList({ keyword: query, paging: { pageNumber: 1, pageSize: 50 } });

    if (!success) throw Object.assign(new Error(message), { success, data });

    tenants.value.option = (data instanceof Array ? data : [])
      .filter((v) => (v.baileeTenantId ? v.baileeTenantId === $params.value.baileeTenantId && v.id !== $params.value.id : true))
      .reduce(
        (p, c) => {
          const index = findIndex(p, (v) => v.id === c.id);
          if (index !== -1) p.splice(index, 1);
          else p.unshift(c);
          return p;
        },
        tenants.value.base ? [tenants.value.base] : []
      );
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    tenants.value.loading = false;
  }
}
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  await (async () => {
    const { success, message, data } = await getSystemVersion({});
    if (!success) throw Object.assign(new Error(message), { success, data });
    systemEditionOption.value = data instanceof Array ? data.reverse() : [];
    form.value.systemEdition = systemEditionOption.value[0].code;
  })();
}
const userGroupOption = ref<Record<"name" | "code", string>[]>([]);
// 获取组织下所有用户组
async function getGroupList() {
  await (async () => {
    const { success, message, data } = await getGroup({});
    if (!success) throw Object.assign(new Error(message), { success, data });
    userGroupOption.value = data;
  })();
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  // if (!$params.value.id) {
  tenants.value.loading = true;
  let pageNumber = 1;
  let pageSize = 300;
  let pageTotal = Infinity;
  const $data: (ReturnType<typeof getTenantList> extends Promise<infer U> ? U : never)["data"] = [];
  while ((pageNumber - 1) * pageSize < pageTotal) {
    const { success, message, data, page, size, total } = await getTenantList({ paging: { pageNumber, pageSize } });
    if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
    pageNumber = Number(page) || 1;
    pageSize = Number(size) || 30;
    pageTotal = Number(total) || 0;
    if (data instanceof Array) $data.push(...(data instanceof Array ? data : []));
    pageNumber++;
  }
  // if (params.baileeTenantId) {
  //   const { success, message, data } = await getTenantList({ id: <string>params.baileeTenantId, paging: { pageNumber: 1, pageSize: 1 } });
  //   if (!success) throw Object.assign(new Error(message), { success, data });
  //   tenants.value.base = (data instanceof Array ? data : [])[0] || null;
  // } else {
  //   tenants.value.base = null;
  // }
  tenants.value.option = $data.filter((v) => (v.baileeTenantId ? v.baileeTenantId === $params.value.baileeTenantId && v.id !== $params.value.id : true));
  tenants.value.loading = false;

  userGroups.value = [...$data];
  // }
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  if (form.ownerUserId === "new") delete form.ownerUserId;
  else delete form.owner;
  return form;
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 窗口方法
 */
interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<Item>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Record<string, unknown>) => void;
  reject?: (value: Error) => void;
  callback?: (form: Record<string, unknown>) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Record<string, unknown>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {}) as Item);

function netHelp() {
  ElMessageBox.confirm("如果客户的监控使用的是NetCare,请勾选此选项<br/>如果客户无需NetCare作为监控源,可不勾选此选项", "提醒", {
    confirmButtonText: "关闭",
    // cancelButtonText: "关闭",
    type: "warning",
    dangerouslyUseHTMLString: true, // 允许 HTML 标签
    showCancelButton: false,
    showClose: false,
  })
    .then(() => {})
    .catch(() => {});
}

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    if (form.id) {
      form.containerId = form.containerId;
    } else {
      form.containerId = treeAuthRef.value?.treeItem?.id;
    }
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(function (formResult, [key, util]) {
    if (!util) return formResult;
    else if (util.test(formResult[key])) return formResult;
    else return Object.assign(formResult, { [key]: util.transfer(formResult[key], cloneDeep(toRaw(util.value)) as never) });
  }, form as Required<Item>);
}

async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => (formRef.value ? formRef.value.validate(resolve) : resolve(false)));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleSubmit(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }
  const netcareActive = form.value.synetcare;
  if (!netcareActive) {
    ElMessageBox.confirm("确认不使用NetCare作为监控源,不同步到NetCare?", "提醒", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        handleSubmitT();
      })
      .catch(() => {
        data.submitLoading = false;
      });
  } else {
    ElMessageBox.confirm("确认使用NetCare作为监控源,信息同步到NetCare?", "提醒", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        handleSubmitT();
      })
      .catch(() => {
        data.submitLoading = false;
      });
  }
}
async function handleSubmitT(done?: () => void) {
  const treeItemId = form.value.containerId || treeAuthRef.value?.treeItem?.id;
  if (!treeItemId) {
    data.submitLoading = false;
    ElMessage.error("请选择安全目录");
    return;
  }
  const $form = await getForm(form.value || {});
  if (isJoin.value) {
    $form.joinUserIds = [userInfo.userId];
  }

  // if (isActivate.value) {
  // }
  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.reject === "function") await data.reject(Object.assign(new Error(), $form));
    // if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 下一步方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();
  containerIdS.value = "";
  const $form = await getForm(form.value || {});
  if (treeAuthRef.value) {
    treeAuthRef.value.getSafeContaine();
    treeAuthRef.value.treeId = -1;
    treeAuthRef.value.treeItem.id = false;
  }
  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  treeAuthRef.value && treeAuthRef.value.getSafeContaine();
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  containerIdS.value = "";
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();

defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, callback?: (form: Record<string, unknown>) => Promise<void>): Promise<unknown> {
    if (data.visible) handleCancel();
    const wait = new Promise<Record<string, unknown>>((resolve, reject) => ((data.resolve = resolve), (data.reject = reject)));
    $params.value = cloneDeep(params);
    data.visible = true;
    data.loading = true;
    data.submitLoading = true;
    data.callback = callback;
    userGroupIds.value = [];
    isJoin.value = false;
    // remoteTenantsMethod();
    await resetFormInit($params.value);
    // await getGroupList();
    await nextTick();
    try {
      await runningInit($params.value);
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      handleCancel();
    }
    handleReset();
    data.loading = false;
    data.submitLoading = false;

    return await wait;
    // try {
    //   return await wait;
    // } catch (error) {
    //   return error;
    // }
  },
  async confirm<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.confirm(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async alert<T extends { $title: string; $type: "" | "success" | "warning" | "info" | "error"; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.alert(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
});
</script>

<style lang="scss" scoped>
.dissableFirstLevel {
  .el-scrollbar:first-child {
    .el-checkbox {
      display: none;
    }
  }
}
.scrollbar__view {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.el-overlay-dialog {
  overflow: initial !important;
}
</style>
