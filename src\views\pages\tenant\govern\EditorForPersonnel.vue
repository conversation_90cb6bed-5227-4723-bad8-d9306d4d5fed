<template>
  <!-- 对话框表单 -->
  <component :is="ComponentRender" v-model:visible="data.visible" :handle-cancel="handleCancel">
    <template #header>
      {{ `${$params.id ? t("glob.edit") : t("glob.add")}${props.title}` }}
    </template>
    <template #default="{ width }">
      <!-- width -->
      <FormModel ref="formRef" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @submit="handleFinish">
        <FormItem :span="width > 600 ? 12 : 24" :label="`姓名`" tooltip="" prop="name" :rules="[buildValidatorData({ name: 'required', title: `${props.title}姓名` })]">
          <el-input v-model="form.name" :placeholder="`请输入${props.title}姓名`" />
        </FormItem>
        <!-- <FormItem :span="width > 600 ? 12 : 24" :label="`昵称`" tooltip="" prop="nickname" :rules="[]">
          <el-input v-model="form.nickname" :disabled="!!$params.id" :placeholder="`请输入${props.title}昵称`" />
        </FormItem> -->
        <FormItem :span="width > 600 ? 12 : 24" :label="`账号	`" tooltip="账号以大小写字母开头，包含6到64位大小写字母、数字、下划线、问号、感叹号、井号" prop="account" :rules="[...(!form.email ? [buildValidatorData({ name: 'required', title: `${props.title}账号` })] : []), buildValidatorData({ name: 'account', title: `${props.title}账号` })]">
          <el-input v-model="form.account" :disabled="!!$params.id" :placeholder="`请输入${props.title}账号`">
            <template #append>@{{ $params.accountSuffix || (userInfo.currentTenant || {}).tenantAbbreviation }}</template>
          </el-input>
        </FormItem>
        <!-- <FormItem :span="width > 600 ? 12 : 24" :label="`密码`" tooltip="" prop="password" :rules="[...(form.account ? [buildValidatorData({ name: 'required', title: `${props.title}密码` })] : []), buildValidatorData({ name: 'password', title: `${props.title}密码` })]">
          <el-input v-model="form.password" type="password" :disabled="!!$params.id" :placeholder="`请输入${props.title}密码`" show-password />
        </FormItem> -->
        <FormItem :span="width > 600 ? 12 : 24" :label="`手机号	`" tooltip="" prop="phone" :rules="[...(false ? [buildValidatorData({ name: 'required', title: `${props.title}手机号` })] : []), buildValidatorData({ name: 'mobile', title: `${props.title}手机号` })]">
          <el-input v-model="form.phone" :disabled="!!$params.id" :placeholder="`请输入${props.title}手机号`" />
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="`邮箱`" tooltip="" prop="email" :rules="[...(!form.account ? [buildValidatorData({ name: 'required', title: `${props.title}邮箱` })] : []), buildValidatorData({ name: 'email', title: `${props.title}邮箱` })]">
          <el-input v-model="form.email" :disabled="!!$params.id" :placeholder="`请输入${props.title}邮箱`" />
        </FormItem>
        <!-- <FormItem :span="width > 600 ? 12 : 24" :label="`性别`" tooltip="" prop="gender" :rules="[buildValidatorData({ name: 'required', title: `${props.title}性别` })]">
          <el-radio-group v-model="form.gender">
            <el-radio v-for="item in genderOption" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem :span="width > 600 ? 12 : 24" :label="`${props.title}语言`" tooltip="" prop="language" :rules="[buildValidatorData({ name: 'required', title: `${props.title}语言` })]">
          <el-select v-model="form.language" class="tw-w-full" :placeholder="`请选择${props.title}语言`" filterable clearable>
            <el-option v-for="item in localesOption" :key="`locale-${item.value}`" :label="item.label" :value="item.value">
              <div :style="{ background: `url(${item.icon}) no-repeat left / auto calc(100% - 12px)`, paddingLeft: '30px' }">{{ item.label }}</div>
            </el-option>
          </el-select>
        </FormItem> -->
      </FormModel>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </component>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readonly, reactive, ref, nextTick, computed, h, renderSlot, getCurrentInstance, createVNode } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { buildTypeHelper } from "@/utils/type";
import { useConfig } from "@/stores/config";
import moment from "moment";

import { buildValidatorData } from "@/utils/validate";

import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import getUserInfo from "@/utils/getUserInfo";

import { locales, localesOption } from "@/api/locale";
import { gender, genderOption } from "@/api/personnel";

const userInfo = getUserInfo();
const config = useConfig();
const { t } = useI18n({ useScope: "global" });
const formRef = ref<InstanceType<typeof FormModel>>();
const ctx = getCurrentInstance();
if (!ctx) throw new Error("Component context initialization failed!");
const { appContext } = ctx;

interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 120,
});

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Item {
  name: string;
  // nickname: string;
  account: string;
  phone: string;
  email: string;
  language: locales;
  gender: gender;
  password: string;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  name: buildTypeHelper<Required<Item>["name"]>(""),
  // nickname: buildTypeHelper<Required<Item>["nickname"]>(""),
  account: buildTypeHelper<Required<Item>["account"]>(""),
  phone: buildTypeHelper<Required<Item>["phone"]>(""),
  email: buildTypeHelper<Required<Item>["email"]>(""),
  language: buildTypeHelper<Required<Item>["language"]>(locales["zh-CN"]),
  gender: buildTypeHelper<Required<Item>["gender"]>(gender.SECRET),
  password: buildTypeHelper<Required<Item>["password"]>(""),
});

/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  // await (async () => {
  //   const { success, message, data } = await getSystemVersion({});
  //   if (!success) throw Object.assign(new Error(message), { success, data });
  //   systemEditionOption.value = data instanceof Array ? data : [];
  // })();
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  return form;
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 窗口方法
 */
interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<Item>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Record<string, unknown>) => void;
  reject?: (value: Error) => void;
  callback?: (form: Record<string, unknown>) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Record<string, unknown>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(function (formResult, [key, util]) {
    if (!util) return formResult;
    else if (util.test(formResult[key])) return formResult;
    else return Object.assign(formResult, { [key]: util.transfer(formResult[key], util.value as never) });
  }, form as Required<Item>);
}

async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => (formRef.value ? formRef.value.validate(resolve) : resolve(false)));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();

defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, callback?: (form: Record<string, unknown>) => Promise<void>): Promise<unknown> {
    if (data.visible) handleCancel();
    const wait = new Promise<Record<string, unknown>>((resolve, reject) => ((data.resolve = resolve), (data.reject = reject)));
    $params.value = cloneDeep(params);
    data.visible = true;
    data.loading = true;
    data.submitLoading = true;
    data.callback = callback;
    await nextTick();
    try {
      await runningInit($params.value);
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      handleCancel();
    }
    handleReset();
    data.loading = false;
    data.submitLoading = false;

    try {
      return await wait;
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async confirm<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.confirm(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async alert<T extends { $title: string; $type: "" | "success" | "warning" | "info" | "error"; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.alert(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
});
</script>

<style scoped lang="scss"></style>
