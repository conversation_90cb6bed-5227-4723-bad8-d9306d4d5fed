<template>
  <!-- 对话框表单 -->
  <component :is="ComponentRender" v-model:visible="data.visible" :handle-cancel="handleCancel">
    <template #header>
      {{ `变更拥有人` }}
    </template>
    <template #default="{ width }">
      <!-- width -->
      <FormModel ref="formRef" :loading="data.loading" :model="form" label-position="left" :label-width="`${props.labelWidth}px`" @submit="handleFinish">
        <FormGroup :span="24" :label="`当前拥有人`">
          <FormItem :span="24" :label="label" tooltip="">
            <el-input v-model="ownerName" disabled></el-input>
          </FormItem>
        </FormGroup>
        <FormGroup v-if="!$params.id" :span="24" :label="`拥有人`">
          <template v-if="form.ownerUserId !== 'new'" #default="{ label }">
            <FormItem :span="24" :label="label" tooltip="" prop="ownerUserId" :rules="[buildValidatorData({ name: 'required', title: label })]">
              <el-select v-model="form.ownerUserId" :placeholder="`输入${label} 关键字 搜索`" clearable filterable default-first-option reserve-keyword remote :loading="dataLoading" :remote-method="remoteMethod" class="tw-w-full">
                <el-option v-for="item in dataList" :key="item.id" :label="`${item.name} (${item.account || item.email})`" :value="(item.id as string)" class="tw-h-fit">
                  <div class="tw-flex tw-items-center">
                    <!-- <el-avatar class="tw-mr-[10px]" :size="50" :src="item.avatar as string || ''"> -->
                    <el-avatar class="tw-flex-shrink-0" :size="50" fit="fill"
                      >{{ item.name.slice(-2) }}
                      <template v-if="item.id === 'new'">
                        <el-text>创建</el-text>
                      </template>
                    </el-avatar>
                    <el-row :gutter="12" class="tw-w-[calc(100%_-_60px)]">
                      <el-col v-if="item.id === 'new'" :span="12">
                        <el-text type="primary">创建{{ label }}</el-text>
                      </el-col>
                      <el-col v-else :span="12">姓名: {{ item.name }}</el-col>
                      <el-col :span="12">账号: {{ item.account }}{{ item.account ? `@${item.tenantAbbreviation}` : "" }}</el-col>
                      <el-col :span="12">手机号: {{ item.phone }}</el-col>
                      <el-col :span="12">邮箱: {{ item.email }}</el-col>
                    </el-row>
                  </div>
                </el-option>
              </el-select>
            </FormItem>
          </template>
        </FormGroup>
      </FormModel>
    </template>
    <template #footer>
      <!-- <el-button type="warning" @click="handleReset()" :disabled="data.submitLoading">{{ t("glob.Reset") }}</el-button> -->
      <el-button type="default" @click="handleCancel()" :disabled="data.submitLoading">{{ t("glob.Cancel") }}</el-button>
      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button> -->
      <el-button type="primary" @click="handleSubmit()" v-blur :loading="data.submitLoading">{{ t("glob.Confirm") }}</el-button>

      <!-- <el-button type="primary" @click="handleFinish()" v-blur :loading="data.submitLoading">{{ t("glob.Save") }}</el-button> -->
    </template>
  </component>
</template>

<script setup lang="ts" name="EditorForm">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { toRaw, readonly, reactive, ref, nextTick, computed, h, renderSlot, getCurrentInstance, createVNode } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep, find, findIndex } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { Back, UserFilled } from "@element-plus/icons-vue";
import { buildTypeHelper } from "@/utils/type";
import { useConfig } from "@/stores/config";
import moment from "moment";

import { buildValidatorData, validatorPattern } from "@/utils/validate";

import EditorDrawer from "@/components/editor/EditorDrawer.vue";
import EditorDialog from "@/components/editor/EditorDialog.vue";

import FormModel from "@/components/formItem/FormModel.vue";
import FormItem from "@/components/formItem/FormItem.vue";
import FormGroup from "@/components/formItem/FormGroup.vue";

import Timezone from "@/components/formItem/timezone/index.vue";

import getUserInfo from "@/utils/getUserInfo";

import { getSystemVersion } from "@/api/system";

import { locales, localesOption } from "@/api/locale";
import { Zone as zones } from "@/utils/zone";

import { priority, priorityOption } from "@/views/pages/apis/event";
import { TenantItem, getUserByPlatform, getTenantList, type UserItem } from "@/api/personnel";
import { getAvatar } from "@/api/system";

const { log } = console;

const dataLoading = ref(false);
const dataList = ref<Partial<{ id: string; name: string; account: string; phone: string; email: string; [key: string]: unknown }>[]>([]);

async function remoteMethod(query: string) {
  dataList.value = [];
  if (!query) return;
  try {
    dataLoading.value = true;
    form.value.owner = { id: "new", name: "", account: "", phone: "", email: "" };
    if ((<{ pattern: RegExp; message: string }>validatorPattern.email).pattern.test(query)) {
      Object.assign(form.value.owner, { email: query });
    } else if ((<{ pattern: RegExp; message: string }>validatorPattern.mobile).pattern.test(query)) {
      Object.assign(form.value.owner, { phone: query });
    } else if ((<{ pattern: RegExp; message: string }>validatorPattern.account).pattern.test(query)) {
      Object.assign(form.value.owner, { account: query });
    }
    const { success, message, data } = await getUserByPlatform({ keyword: query, paging: { pageNumber: 1, pageSize: 30 } });
    if (!success) throw Object.assign(new Error(message), { success, data });
    const res = await Promise.all(
      (data instanceof Array ? data : []).map(async (v) => {
        return { id: v.id, name: v.name, account: v.account, phone: v.phone, email: v.email, avatar: await getAvatar({ filePath: v.profilePicture }), tenantAbbreviation: v.tenantAbbreviation };
      })
    );
    dataList.value.push(...res);
    if (!form.value.owner.account && !form.value.owner.phone && !form.value.owner.email) return;
    if (!dataList.value.length) dataList.value = [{ ...form.value.owner }];
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    dataLoading.value = false;
  }
}

const userInfo = getUserInfo();
const config = useConfig();
const { t } = useI18n({ useScope: "global" });
const formRef = ref<InstanceType<typeof FormModel>>();
const ctx = getCurrentInstance();
if (!ctx) throw new Error("Component context initialization failed!");
const { appContext } = ctx;

interface Props {
  title?: string;
  display?: "dialog" | "drawer";
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  display: "dialog",
  labelWidth: 120,
});

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
interface Item {
  baileeTenantId: string; // 受托租户ID
  name: string;
  abbreviation: string;
  systemEdition: string;
  zoneId: string;
  language: string;
  note: string;
  address: string;
  ownerUserId: string;
  owner: Partial<{
    id: string;
    name: string /* 姓名 */;
    // nickname: string /* 昵称 */;
    account: string /* 账号 */;
    phone: string /* 手机号 */;
    email: string /* 邮箱 */;
    // language: string /* 语言 */;
    // gender: gender /* 性别 */;
    // password: string /* 密码 */;
  }>;
}
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */

/**
 * TODO: 此为表单初始默认数据和校验方法
 *
 * import { buildTypeHelper } from "@/utils/type";
 *
 * 需要引入工具类
 */
const defaultForm = readonly<{ [Key in keyof Item]: { value: Item[Key]; test: (v: unknown) => v is Item[Key]; transfer: (fv: unknown, ov: Item[Key]) => Item[Key]; type: string; ConstructorFunction: import("@/utils/type").ConstructorFunc<Item[Key]> } }>({
  baileeTenantId: buildTypeHelper<Required<Item>["baileeTenantId"]>(""),
  name: buildTypeHelper<Required<Item>["name"]>(""),
  abbreviation: buildTypeHelper<Required<Item>["abbreviation"]>(""),
  systemEdition: buildTypeHelper<Required<Item>["systemEdition"]>(""),
  zoneId: buildTypeHelper<Required<Item>["zoneId"]>(""),
  language: buildTypeHelper<Required<Item>["language"]>(""),
  note: buildTypeHelper<Required<Item>["note"]>(""),
  address: buildTypeHelper<Required<Item>["address"]>(""),
  ownerUserId: buildTypeHelper<Required<Item>["ownerUserId"]>(""),
  owner: buildTypeHelper<Required<Item>["owner"]>({ id: "", name: "", account: "", phone: "", email: "" }),
});

const systemEditionOption = ref<Record<"name" | "code", string>[]>([]);
const tenants = ref({
  loading: false,
  base: <TenantItem | null>null,
  option: <TenantItem[]>[],
});
async function remoteTenantsMethod(query: string) {
  tenants.value.option = tenants.value.base ? [tenants.value.base] : [];
  if (!query) return;
  try {
    tenants.value.loading = true;
    const { success, message, data } = await getTenantList({ keyword: query, paging: { pageNumber: 1, pageSize: 50 } });
    if (!success) throw Object.assign(new Error(message), { success, data });
    tenants.value.option = (data instanceof Array ? data : [])
      .filter((v) => (v.baileeTenantId ? v.baileeTenantId === $params.value.baileeTenantId && v.id !== $params.value.id : true))
      .reduce(
        (p, c) => {
          const index = findIndex(p, (v) => v.id === c.id);
          if (index !== -1) p.splice(index, 1);
          else p.unshift(c);
          return p;
        },
        tenants.value.base ? [tenants.value.base] : []
      );
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    tenants.value.loading = false;
  }
}
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 首次打开初始化时执行
 *
 * @param params Partial<Item>
 *
 * 首次打开弹窗弹窗显示时调用，抛出错误则关闭弹窗
 */
async function runningInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  await (async () => {
    const { success, message, data } = await getSystemVersion({});
    if (!success) throw Object.assign(new Error(message), { success, data });
    systemEditionOption.value = data instanceof Array ? data : [];
  })();
}
/**
 * TODO: 每次重置表单时调用
 *
 * @param params Partial<Item>
 *
 * 每次重置表单时调用，抛出错误则关闭弹窗
 */
async function resetFormInit(params: Record<string, unknown>): Promise<void> {
  if (!params) return;
  if (!$params.value.id) {
    tenants.value.loading = true;
    let pageNumber = 1;
    let pageSize = 300;
    let pageTotal = Infinity;
    const $data: (ReturnType<typeof getTenantList> extends Promise<infer U> ? U : never)["data"] = [];
    while ((pageNumber - 1) * pageSize < pageTotal) {
      const { success, message, data, page, size, total } = await getTenantList({ paging: { pageNumber, pageSize } });
      if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
      pageNumber = Number(page) || 1;
      pageSize = Number(size) || 30;
      pageTotal = Number(total) || 0;
      if (data instanceof Array) $data.push(...(data instanceof Array ? data : []));
      pageNumber++;
    }
    // if (params.baileeTenantId) {
    //   const { success, message, data } = await getTenantList({ id: <string>params.baileeTenantId, paging: { pageNumber: 1, pageSize: 1 } });
    //   if (!success) throw Object.assign(new Error(message), { success, data });
    //   tenants.value.base = (data instanceof Array ? data : [])[0] || null;
    // } else {
    //   tenants.value.base = null;
    // }
    tenants.value.option = $data.filter((v) => (v.baileeTenantId ? v.baileeTenantId === $params.value.baileeTenantId && v.id !== $params.value.id : true));
    tenants.value.loading = false;
  }
}
/**
 * TODO: 此处使用可对生成的数据操作
 *
 * @param form Partial<Record<keyof Item, unknown>>
 *
 * 获取表单数据方法
 * 直接修改 `form` 变量中数据就行每次获取表单都会调用
 */
async function transformForm(form: Partial<Record<keyof Item, unknown>>): Promise<Partial<Record<keyof Item, unknown>>> {
  if (form.ownerUserId === "new") delete form.ownerUserId;
  else delete form.owner;
  return form;
}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */
/**
 * TODO: 窗口方法
 */
interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
}
const state = reactive<StateData<Item>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Record<string, unknown>) => void;
  reject?: (value: Error) => void;
  callback?: (form: Record<string, unknown>) => Promise<void>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const ownerName = ref("");
const $params = ref<Record<string, unknown>>({});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(toRaw(value)) }), {}) as Item);

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  try {
    form = await transformForm(cloneDeep(form));
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    form = cloneDeep(form);
  }
  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(function (formResult, [key, util]) {
    if (!util) return formResult;
    else if (util.test(formResult[key])) return formResult;
    else return Object.assign(formResult, { [key]: util.transfer(formResult[key], cloneDeep(toRaw(util.value)) as never) });
  }, form as Required<Item>);
}

async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => (formRef.value ? formRef.value.validate(resolve) : resolve(false)));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleSubmit(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.reject === "function") await data.reject(Object.assign(new Error(), $form));
    // if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 下一步方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") await data.callback($form);
    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    formRef.value && formRef.value.clearValidate();
    await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
    ownerName.value = "";
  });
}

const ComponentRender = computed(() => {
  switch (props.display) {
    case "dialog":
      return EditorDialog;
    case "drawer":
      return EditorDrawer;
    default:
      return h("div");
  }
});

interface Slots {
  [slot: string]: (props: { params: Record<string, unknown>; form: Record<string, any>; close(): void }) => any;
}
const slots = defineSlots<Slots>();

defineExpose({
  close: handleCancel,
  async open(params: Record<string, unknown>, callback?: (form: Record<string, unknown>) => Promise<void>): Promise<unknown> {
    ownerName.value = params.ownerName;
    // // console.log(params.ownerName, 5555);
    if (data.visible) handleCancel();

    // form.value.ownerUserId = params.ownerId;

    const wait = new Promise<Record<string, unknown>>((resolve, reject) => ((data.resolve = resolve), (data.reject = reject)));
    $params.value = cloneDeep(params);
    data.visible = true;
    data.loading = true;
    data.submitLoading = true;
    data.callback = callback;
    await nextTick();
    try {
      await runningInit($params.value);
    } catch (error) {
      if (error instanceof Error) ElMessage.error(error.message);
      handleCancel();
    }
    handleReset();
    data.loading = false;
    data.submitLoading = false;

    return await wait;
    // try {
    //   return await wait;
    // } catch (error) {
    //   return error;
    // }
  },
  async confirm<T extends { $title: string; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.confirm(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
  async alert<T extends { $title: string; $type: "" | "success" | "warning" | "info" | "error"; $slot: string; [key: string]: unknown }>(params: T, callback?: (form: Record<string, unknown>) => Promise<void>) {
    try {
      const $form = reactive<Record<string, unknown>>({ ...cloneDeep(Object.entries(params).reduce((p, [k, v]) => ({ ...p, ...(/^[a-zA-Z].*$/g.test(k) ? { [k]: v } : {}) }), {})) });
      return new Promise<void>((resolve, reject) => {
        const beforeClose = () => {
          reject(void 0);
          setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          ElMessageBox.close();
        };
        ElMessageBox.alert(createVNode({ setup: () => () => renderSlot(slots, params.$slot, { params, form: $form, close: beforeClose }) }), params.$title, {
          customStyle: { "--el-messagebox-width": "500px" },
          draggable: true,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              try {
                instance.cancelButtonLoading = true;
                instance.confirmButtonLoading = true;
                if (typeof callback === "function") await callback(params);
                done();
              } catch (error) {
                if (error instanceof Error) ElMessage.error(error.message);
              } finally {
                instance.cancelButtonLoading = false;
                instance.confirmButtonLoading = false;
              }
            } else done();
          },
        })
          .then(() => {
            resolve(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          })
          .catch(() => {
            reject(void 0);
            setTimeout(() => (resolve = reject = (_: void | PromiseLike<void>) => {}));
          });
      });
    } catch (error) {
      if (error instanceof Error) throw error;
    }
  },
});
</script>

<style scoped lang="scss"></style>
