<template>
  <el-form v-loading="loading" :model="form" :style="{ marginTop: '10px', padding: '0 10px' }" label-position="left" label-width="160px" ref="roleFormRef" label-suffix=":">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="工作时间"> </el-form-item>
      </el-col>
      <!-- <el-col :span="12">
        <el-form-item label="选择时区">
          <el-select v-model="form.tenantConfig.timeZone" filterable placeholder="请选择" class="tw-w-full">
            <el-option v-for="item in timeZone" :key="item.zoneId" :label="item.displayName" :value="item.zoneId"> </el-option>
          </el-select>
        </el-form-item>
      </el-col> -->

      <div style="width: 100%" class="support-table-content" ref="tableContentRef">
        <el-table stripe border :data="form.tenantConfig.tenantHours" style="width: 100%" @header-click="(column: Record<string, any>, $event: Event) => handleClick({ column, evene: $event })">
          <el-table-column align="left" width="80" prop="week" fixed="left">
            <template #default="scope">
              <div @click="handleSelectTime('all', scope.$index, scope.row)">
                {{ scope.row.weekDay == 1 ? "周一" : scope.row.weekDay == 2 ? "周二" : scope.row.weekDay == 3 ? "周三" : scope.row.weekDay == 4 ? "周四" : scope.row.weekDay == 5 ? "周五" : scope.row.weekDay == 6 ? "周六" : "周日" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column :width="tableWidth" align="left" v-for="(item, key) in 24" :key="`h-${key}`" :label="String(key)" class-name="tw-py-0">
            <template #header="{ column }">
              <div class="tw-my-[12px]">{{ column.label }}</div>
            </template>
            <template #default="scope">
              <div class="tw-mx-[-12px] tw-flex tw-h-[50px] tw-items-center tw-justify-center tw-text-[20px]" @click="handleSelectTime(key, scope.$index, scope.row)" style="height: 100%" :class="scope.row.hours && scope.row.hours.length && scope.row.hours.includes(key) ? 'sun' : 'moon'">
                <el-icon v-if="scope.row.hours && scope.row.hours.length && scope.row.hours.includes(key)" class="tw-text-white"><Sunny></Sunny></el-icon>
                <el-icon v-else class="tw-text-black"><Moon></Moon></el-icon>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-col :span="24" class="tw-pb-[18px] tw-text-right">
        <el-button type="primary" @click="CreateSlaDownConfig" class="tw-mt-[10px]"> 保 存</el-button>
      </el-col>
    </el-row>
  </el-form>
</template>

<script lang="ts" setup generic="T extends { [key: string]: unknown; id: string }">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured } from "vue";
import { ElMessage, ElMenuItem, ElForm } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { QuillEditor } from "@vueup/vue-quill";
import BlotFormatter from "quill-blot-formatter";
import "@vueup/vue-quill/dist/vue-quill.snow.css";
// 注册
import { Sunny, Moon } from "@element-plus/icons-vue";
import { Support_notesRelationArea } from "@/views/pages/apis/supportNotes";
import _timeZone from "@/views/pages/common/strategyZone.json";

import getUserInfo from "@/utils/getUserInfo";

import { useResizeObserver } from "@vueuse/core";

import { 安全管理中心_客户管理_可读 } from "@/views/pages/permission";
import { getTenantNotes } from "@/views/pages/apis/tenant";

const userInfo = getUserInfo();
const route = useRoute();
const router = useRouter();
const modules = {
  name: "blotFormatter",
  module: BlotFormatter,
};
interface Emits {
  ($event: "confirm", data: Record<string, any>): any;
  ($event: "child-event", data: Record<string, any>): any;
  ($event: "refresh", data: unknown): any;
}
const emits = defineEmits<Emits>();
const roleFormRef = ref<InstanceType<typeof ElForm>>();

interface Props {
  detail: T;
  width: number;
}
const props = withDefaults(defineProps<Props>(), {
  width: 0,
  detail: () => ({}) as T,
});

const timeZone = ref(_timeZone);
const loading = ref(false);
const InitData = reactive({});

const form = reactive<Record<string, any>>({ ...props.detail, tenantConfig: { timeZone: "设备默认", tenantHours: [] } });
const ctx = getCurrentInstance()!;

const allBool = ref([false, false, false, false, false, false, false]);
const tableContentRef = ref();
const tableWidth = ref(0);
useResizeObserver(tableContentRef, (entries) => {
  const entry = entries[0];
  const { width, height } = entry.contentRect;
  tableWidth.value = ((width - 80) / 24).toFixed(0);
});

onMounted(() => {
  nextTick(() => {
    timeZone.value.unshift({
      zoneId: "设备默认",
      displayName: "设备默认",
    });

    let map = new Map();
    for (let item of timeZone.value) {
      map.set(item.zoneId, item);
    }
    timeZone.value = [...map.values()];

    getDetail({ id: props.detail.id });

    form.tenantConfig.tenantHours.forEach((item: any) => {
      if (item.hours.length > 23) {
        allBool.value[item.weekDay - 1] = true;
      } else {
        allBool.value[item.weekDay - 1] = false;
      }
    });
  });
});

//覆盖时间列
function handleClick({ column, evene }: { column: Record<string, any>; evene: Event }) {
  let index = Number(column.label);
  const { tenantHours } = form.tenantConfig;

  let hours = [];
  tenantHours.forEach((v) => {
    hours = hours.concat(v.hours);
  });

  const isActive = hours.includes(index) && hours.filter((v) => v === index).length === 7; // 是否激活

  tenantHours.forEach((v: Record<string, any>, i: number) => {
    let delIndex = v.hours.indexOf(index);
    if (isActive) {
      v.hours.splice(delIndex, 1);
    } else {
      v.hours.push(index);
    }

    v.hours = [...new Set(v.hours.sort((a, b) => a - b))];
  });
}
//覆盖时间
function handleSelectTime(key: string | number, weekIndex: number, row: Record<string, any>) {
  if (key === "all") {
    allBool.value[weekIndex] = !allBool.value[weekIndex];

    let data = [];
    for (let i = 0; i < 24; i++) {
      data.push(i);
    }

    row.hours = [...new Set(data)];

    if (!allBool.value[weekIndex]) {
      row.hours = [];
    }
  } else {
    const index = row.hours.indexOf(key);
    if (index == -1) {
      row.hours.push(key);
    } else row.hours.splice(index, 1);
  }
}

async function getDetail(row: Partial<{ id: string }>) {
  loading.value = true;
  const { success, message, data } = await getTenantNotes({ tenantId: row.id });
  form.tenantConfig = { ...data.tenantConfig };
  loading.value = false;
  if (!success) throw Object.assign(new Error(message), { success, data });
}

//更新
function CreateSlaDownConfig() {
  if (!roleFormRef.value) return;
  roleFormRef.value.validate((valid) => {
    if (valid) {
      let data = {
        tenantId: props.detail.id,
        tenantConfig: form.tenantConfig,
      };

      emits("confirm", data);
    } else {
      ElMessage.error("请输入正确信息");
    }
  });
}
</script>

<style lang="scss" scoped>
:deep(.ql-blank) {
  height: 184px;
}
.work-editor {
  padding: 0 20px;
  box-sizing: border-box;
}
.modules-item {
  .modules-title {
    color: rgba(32, 128, 255, 1);
    font-size: 16px;
    text-align: left;
    font-family: SourceHanSansSC-medium;
  }
}
.sun {
  background: rgb(26, 190, 107);
  :deep() .elstyle-button--text {
    color: #fff;
  }
}
.moon {
  background: #fff;
  :deep() .elstyle-button--text {
    color: rgb(153, 153, 153);
  }
}
.modules-tips {
  margin-left: 20px;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  .el-icon-info {
    color: rgba(252, 202, 0, 1);
    margin-right: 5px;
  }
}
</style>
