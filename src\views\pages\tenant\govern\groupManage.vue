<template>
  <!-- 对话框表单 -->
  <el-drawer class="el-card tw-bottom-[16px] tw-right-[16px] tw-top-[120px]" :size="width" :style="{ height: `${height}px` }" v-model="data.visible" :close-on-click-modal="false" :show-close="false" :modal="false" :before-close="handleCancel">
    <template #header>
      <div class="tw-flex tw-h-[30px] tw-flex-nowrap tw-items-center">
        <el-page-header class="tw-mr-auto" :content="`${props.title}`" @back="handleCancel()"></el-page-header>

        <span v-if="userInfo.hasPermission(PERMISSION.tenant.create)">
          <el-button type="primary" :icon="Plus" @click="(dialogVisible = true), (userGroupIds = [])">邀请用户组</el-button>
        </span>
      </div>
    </template>
    <template #default>
      <!-- <div class="flex-search" :style="{ minWidth: `${width - 60}px`, padding: '0px' }">
        <div class="left"></div>
        <div class="center"></div>
        <div class="right">

        </div>
      </div> -->
      <!-- <el-scrollbar v-loading="state.loading"> -->
      <el-table v-loading="state.loading" :data="groupList" :height="height - 104 - 20 - (state.total ? 32 : 0)" :style="{ width: `${width}px`, margin: '0 auto' }">
        <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :min-width="column.width || 120" :="column.showOverflowTooltip" :formatter="column.formatter" />
        <el-table-column :label="t('glob.operate')" align="left" header-align="left" :width="110" fixed="right">
          <template #default="{ row }: { row: DataItem }">
            <span v-if="userInfo.hasPermission(PERMISSION.tenant.editor)" class="el-button is-link">
              <el-button link type="primary" size="small" @click="deleteItem(row as Record<string, any>)" style="font-size: 14px">{{ row.tenantId == $params.id ? "删除" : "移除" }}</el-button>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <div :style="{ margin: '2px 20px 20px' }">
        <!-- <el-pagination v-show="state.total" v-model:current-page="state.page" v-model:page-size="state.size" :page-sizes="state.sizes" :total="state.total" layout="->, total, sizes, prev, pager, next, jumper" @size-change="handleStateRefresh()" @current-change="handleStateRefresh()" /> -->
      </div>
      <!-- </el-scrollbar> -->
    </template>
  </el-drawer>
  <el-dialog v-model="dialogVisible" title="邀请用户组" width="30%" :before-close="handleClose">
    <el-form>
      <el-form-item :span="width > 600 ? 12 : 24" :label="`选择用户组`" tooltip="" prop="zoneId" :rules="[]" class="choose-groups">
        <!-- <el-cascader ref="cascaderRef" style="width: 500px" v-model="userGroupIds" @change="cascaderChange" filterable :options="userGroups" :props="groupProps" clearable popper-class="dissableFirstLevel"></el-cascader> -->
        <!-- :before-filter="beforeFilter" @input="inputEnter" -->
        <el-cascader ref="cascaderRef" style="width: 500px" v-model="userGroupIds" @change="cascaderChange" filterable :options="userGroups" :props="groupProps" clearable popper-class="dissableFirstLevel"></el-cascader>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="(dialogVisible = false), (userGroupIds = [])">取消</el-button>
        <el-button type="primary" @click="submit"> 提交 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<!--  generic="Item extends Record<'id', string> & Record<string, unknown>" -->
<script setup lang="ts" name="EditorForm">
/* eslint-disable no-unused-vars, @typescript-eslint/no-unused-vars */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { getCurrentInstance, onMounted } from "vue";
import { ElTable, ElCascader } from "element-plus";

import { readonly, reactive, ref, nextTick, inject, h } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "lodash-es";
import { ElMessage, ElMessageBox } from "element-plus";
import { TypeHelper } from "@/utils/type";
import { InfoFilled as ElIconInfo, Plus as ElIconPlus } from "@element-plus/icons-vue";

import getUserInfo from "@/utils/getUserInfo";
import { Check } from "@element-plus/icons-vue";
import { type SlaConfigList as DataItem } from "@/views/pages/apis/SlaConfig";
import * as Api from "@/views/pages/apis/SlaConfig";

import { responseData as _responseData, resolveData as _resolveData, priority, statusData as _statusData } from "./common";

import { slaState } from "@/views/pages/common/slaState";
import _timeZone from "@/views/pages/common/zone.json";

import { getUserGroups, jionUserGroups } from "@/views/pages/apis/deviceBatchManage";
import { getTenantList } from "@/api/personnel";

import { useResizeObserver } from "@vueuse/core";
import { userGroupUnassignTenant, delUserGroup } from "@/views/pages/apis/userGroup";
const userInfo = getUserInfo();
const emit = defineEmits(["confrim"]);

const dialogVisible = ref(false);
/**
 * TODO: 本地方法
 */

// //查询客户名称列表
// function getCustomerList() {
//   let params = {
//     ...this.page,
//   };
//   this.tableLoading = true;
//   getCustomerList(params)
//     .then(({ success, data, page, size, total }) => {
//       // console.log(success, data);
//       if (success) {
//         this.customerNameList = data.tenants;
//         this.tableLoading = false;
//         this.page.total = Number(total);
//         this.page.pageNumber = page;
//         this.page.pageSize = size;
//       }
//     })
//     .catch(() => {
//       this.tableLoading = false;
//     });
// }
/**
 * TODO: 窗口方法
 */
type Item = Omit<DataItem, "createdTime" | "updatedTime" | "id" | "status"> & { id: string };

interface Props {
  title: string;
  labelWidth?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: "",
  labelWidth: 116,
});

const { t } = useI18n();

const userGroupIds = ref([]);
const originBusinessList = ref([]);
const joinUserGroupIds = ref([]);

const userGroups = ref([]);
const groupProps = ref({
  checkStrictly: true,
  children: "children",
  label: "name",
  value: "id",
  lazy: true,
  multiple: true,
  leaf: "leaf",
  lazyLoad(node: any, resolve: any) {
    const { level } = node;
    //下一层节点
    let nodes: any = [];
    //如果有子节点 或者 为根节点（即首次进入level为0）
    //也有人写成 node.level == 0 作用是一样的

    if (node.level == 1) {
      // 0 代表第一次请求

      //这里setTimeout的目的是 显示加载动画
      setTimeout(() => {
        //调用后端接口 获得返回数据

        getUserGroups({ tenantId: node.value }).then((res: any) => {
          if (res.success) {
            let arr = [];
            arr = res.data.filter((itemA: any) => {
              return groupList.value.every((itemB: any) => {
                return itemB.id !== itemA.id;
              });
            });
            // // console.log(arr, 77777);
            // currentRole = arr;
            nodes = Array.from(arr).map((item: any) => ({
              id: item.id,
              name: item.name,
              leaf: level >= 1,
            }));

            resolve(nodes);
          } else {
            resolve(undefined);
          }
        });
        // if (node.children.length < 1) {
        //   node.children = undefined;
        // }
      }, 1);
    } else {
      //如果没有子节点就不发起请求，直接渲染，也避免了点击叶子节点仍然有加载动画的问题
      node.children = [];
      resolve([]);
    }
  },
});

const width = inject<import("vue").Ref<number>>("width", ref(100));
const height = inject<import("vue").Ref<number>>("height", ref(100));

//

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: string };
  data: T[];
  total: T | null;
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: TableColumnCtx<DataItem>, value: T[keyof T]) => string | import("vue").VNode; showOverflowTooltip?: boolean }[];
}
const state = reactive<StateData<DataItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {},
  data: [],
  column: [
    { key: "name", label: "用户组名称", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? String(v) : "--") },
    { key: "tenantName", label: "所属客户", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? String(v) + `[${_row.tenantAbbreviation}]` : "--") },
    // { key: "baileeTenantId", label: "是否托管", showOverflowTooltip: true, formatter: (_row, _col, v) => h(ElTag, { type: v ? "success" : "" }, () => (v ? t("glob.Yes") : t("glob.Not"))) },
    // { key: "baileeTenantName", label: "受托客户", showOverflowTooltip: true, formatter: (_row, _col, v) => (_row.baileeTenantId ? `${v || "--"}（${_row.baileeTenantAbbreviation || "--"}）` : "") },
    // { key: "zoneId", label: "成员", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? String(v) : "--") },
  ],
  total: 10,
});
interface EditorData {
  visible: boolean;
  loading: boolean;
  submitLoading: boolean;
  resolve?: (value: Partial<Item>) => void;
  reject?: (value: Partial<Item>) => void;
  callback?: (form: Item & { [key: string]: unknown }) => Promise<boolean>;
}
const data = reactive<EditorData>({
  visible: false,
  loading: false,
  submitLoading: false,
  resolve: undefined,
  reject: undefined,
  callback: undefined,
});
const $params = ref<Partial<Item>>({});
const groupList = ref([]);
type DefaultForm<T> = { [P in keyof T]: { value: T[P]; test: (v: any) => v is T[P]; transfer: (fv: any, ov: T[P]) => T[P] } };
const defaultForm = readonly<DefaultForm<Required<Item>>>({
  id: { value: "", ...TypeHelper.string },
  itemId: { value: "", ...TypeHelper.string },
  level: { value: "", ...TypeHelper.string },
  effectiveBegin: { value: [], ...TypeHelper.array },
  effectiveEnd: { value: [], ...TypeHelper.array },
  response: { value: [], ...TypeHelper.array },
  resolve: { value: [], ...TypeHelper.array },
  ruleId: { value: "", ...TypeHelper.string },
  ruleName: { value: "", ...TypeHelper.string },
  ruleDesc: { value: "", ...TypeHelper.string },
  ruleType: { value: "", ...TypeHelper.string },
  slaRuleId: { value: "", ...TypeHelper.string },
  degradeId: { value: "", ...TypeHelper.string },
  slaRuleName: { value: "", ...TypeHelper.string },
  tenantId: { value: "", ...TypeHelper.string },
});

const form = ref<Item>(Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item);

async function getTenantUserGroups(id) {
  await getUserGroups({ tenantId: id, external: true }).then((res: any) => {
    if (res.success) {
      // // console.log(res.data);
      groupList.value = [...res.data];
      // userGroups.value=[...]
    }
  });
}

async function deleteItem(item) {
  if (item.tenantId != $params.value.id) {
    const { success } = await userGroupUnassignTenant({ groupIds: [item.id], tenantIds: [$params.value.id] });
    // // console.log(success)
    if (success) {
      ElMessage.success("操作成功");
      getTenantUserGroups($params.value.id);
    }
  } else {
    const { success } = await delUserGroup({ id: item.id });
    // // console.log(success)
    if (success) {
      ElMessage.success("操作成功");
      getTenantUserGroups($params.value.id);
    }
  }
}
const cascaderRef = ref<InstanceType<typeof ElCascader>>();
function cascaderChange(node: any) {
  joinUserGroupIds.value = [];

  node.forEach((v: any) => {
    if (v.length > 1) {
      joinUserGroupIds.value.push(v[1]);
    }
  });
}
function beforeFilter() {
  console.log(cascaderRef);

  return false;
}

//模糊搜索
function inputEnter() {
  // setTimeout(() => {
  //   this.businessProList = [];
  //   this.originBusinessList.forEach((item) => {
  //     if (item.label.indexOf(ref.inputValue) !== -1) {
  //       this.businessProList.push(item);
  //     }
  //   });
  // });
}

async function getAllTenantList(id) {
  const { success, message, data, page, size, total } = await getTenantList({ paging: { pageNumber: 1, pageSize: 300000 } });
  if (!success) throw Object.assign(new Error(message), { success, data, page, size, total });
  // userGroups.value = [...data];

  // let arr = [];
  // data.forEach((v, i) => {
  //   if (id != v.id)
  //     if (v.id === userInfo.currentTenantId) {
  //       arr.unshift(v);
  //     } else {
  //       arr.push(v);
  //     }
  // });

  // const arr = [...data];

  const arr = [...data.filter((v) => v.id !== $params.value.id)];
  const belongTenantIndex = arr.map((v) => v.id).indexOf(userInfo.tenantId);
  if (belongTenantIndex !== -1) {
    const belongTenant = arr.splice(belongTenantIndex, 1).find((v) => v);
    arr.unshift(belongTenant);
  }
  userGroups.value = [...arr];

  // console.log(cascaderRef);
  // originBusinessList.value = userGroups.value.slice(0);
}
function handleClose() {
  dialogVisible.value = false;
  userGroupIds.value = [];
}
async function submit() {
  await jionUserGroups(
    {
      joinUserGroupIds: joinUserGroupIds.value,
    },
    $params.value.id
  ).then((res) => {
    if (res.success) {
      // getAllTenantList($params.value.id);
      ElMessage.success("操作成功");
      dialogVisible.value = false;
      getTenantUserGroups($params.value.id);
    }
  });
}

async function getForm(form: Partial<Record<keyof Item, unknown>>): Promise<Required<Item>> {
  form = cloneDeep(form);

  await nextTick();
  type DefaultForm = typeof defaultForm;
  return (Object.entries(defaultForm) as [keyof Item, DefaultForm[keyof Item]][]).reduce<Required<Item>>(
    /* 运行格式化工具 */
    function (formResult, [key, util]) {
      if (!util) return formResult;
      else if (util.test(formResult[key])) return formResult;
      else return Object.assign(formResult, { [key]: cloneDeep(util.transfer(formResult[key], util.value as never)) });
    },
    form as Required<Item>
  );
}
async function checkForm(): Promise<boolean> {
  try {
    return await new Promise((resolve) => resolve(true));
  } catch (error) {
    return false;
  }
}
/* TODO: 提交方法 */
async function handleFinish(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  if (!(await checkForm())) {
    data.submitLoading = false;
    return;
  }

  const $form = await getForm(form.value || {});

  try {
    if (typeof data.callback === "function") {
      const valid = await data.callback($form);
      if (!valid) throw new Error("Error");
    }

    if (typeof data.resolve === "function") data.resolve($form);
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 取消方法 */
async function handleCancel(done?: () => void) {
  data.submitLoading = true;
  await nextTick();

  const $form = await getForm(form.value || {});

  //  coverTimeCfg = ref({
  // timeZone: "客户默认",
  // useCustomerTimeZone: false,
  // useDeviceTimeZone: false,
  // coverWorkTime: [
  try {
    if (typeof data.reject === "function") data.reject(Object.assign(new Error(""), $form));
    handleClean();
    await nextTick();
    if (typeof done === "function") done();
    close();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.dir(error);
    return;
  } finally {
    data.submitLoading = false;
  }
}
/* TODO: 重置表单 */
async function handleReset() {
  data.loading = true;
  state.loading = true;
  form.value = await getForm($params.value);
  await nextTick();
  try {
    // formRef.value && formRef.value.clearValidate();
    // await resetFormInit($params.value);
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    handleCancel();
  }

  data.loading = false;
  state.loading = false;
}
/* TODO: 清空表单 */
function handleClean() {
  form.value = Object.entries(defaultForm).reduce<Partial<Item>>((p, [key, { value }]) => ({ ...p, [key]: cloneDeep(value) }), {}) as Item;
  state.data = [];
  state.select = [];
  state.current = null;
  state.filter = {};
  state.expand = [];
  state.search = {};
}
function close() {
  data.visible = false;
  data.loading = false;
  data.submitLoading = false;
  setTimeout(() => {
    data.resolve = undefined;
    data.reject = undefined;
    data.callback = undefined;
  });
}

defineExpose({
  close: handleCancel,
  async open(params: Partial<Item>, callback?: (form: Item) => Promise<boolean>) {
    if (data.visible) {
      return await new Promise((resolve) => {
        ElMessage.warning("先关闭其他弹窗再重试！");
        resolve(params);
      });
    } else {
      getTenantUserGroups(params.id);
      getAllTenantList(params.id);
      $params.value = cloneDeep(params);
      data.visible = true;
      data.loading = true;
      data.submitLoading = true;
      data.callback = callback;
      try {
        return await new Promise((resolve, reject) => {
          data.resolve = resolve;
          data.reject = reject;

          nextTick(async () => {
            await nextTick();
            handleReset();
            data.loading = false;
            data.submitLoading = false;
          });
        });
      } catch (error) {
        return error;
      }
    }
  },
});
</script>

<style scoped lang="scss"></style>
