<template>
  <el-card>
    <el-scrollbar>
      <div class="flex-search" :style="{ minWidth: `${width - 42}px`, padding: '0px' }">
        <div class="left">
          <el-input v-model="state.search.keyword" :placeholder="t('glob.quick Search Placeholder', { fields: t('glob.Keyword') })" clearable @keydown.enter="handleStateRefresh()" @clear="handleStateRefresh()">
            <template #append>
              <el-button type="primary" :icon="Search" @click="handleStateRefresh()"></el-button>
            </template>
          </el-input>
        </div>
        <div class="center"></div>
        <div class="right">
          <span v-if="userInfo.hasPermission(PERMISSION.tenant.create)">
            <el-button type="primary" :icon="Plus" @click="createItem({}).finally(() => handleStateRefresh())">{{ t("glob.add") }} {{ t("customers.customers") }}</el-button>
          </span>
        </div>
      </div>
    </el-scrollbar>
    <el-table v-loading="state.loading" :data="state.data" :height="height - 104 - 20 - (state.total ? 32 : 0)" :style="{ width: `${width}px`, margin: '0 auto' }" row-key="id" :expand-row-keys="expandList" @expand-change="expandChange">
      <el-table-column type="expand">
        <template #default="{ row, expanded }">
          <governstrategy v-if="expanded" :detail="{ ...row }" :width="width - 40" @confirm="update" @child-event="handleStateRefresh" @refresh="handleStateRefresh"></governstrategy>
        </template>
      </el-table-column>
      <TableColumn type="condition" :prop="`name`" :label="$t('customers.customers name')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByName" :filters="$filter0" @filter-change="handleStateRefresh()"> </TableColumn>
      <TableColumn type="condition" :prop="`abbreviation`" :label="$t('customers.customers abbreviation')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByAbbreviation" :filters="$filter0" @filter-change="handleStateRefresh()"> </TableColumn>
      <!-- <TableColumn type="condition" :prop="`address`" :label="`地址`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByAddress" :filters="$filter0" @filter-change="handleStateRefresh()"> </TableColumn>

      <TableColumn type="enum" :prop="`blocked`" :label="`状态`" :min-width="120" :showOverflowTooltip="true" show-filter v-model:filtered-value="state.search.blocked" :filters="[false, true].map((v) => ({ value: v, text: !v ? '正常' : '锁定', type: !v ? 'success' : 'danger' }))" @filter-change="handleStateRefresh()"></TableColumn> -->

      <!-- <TableColumn type="default" :prop="`tenantType`" :label="`客户类型`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByAbbreviation" :filters="$filter0" @filter-change="handleStateRefresh()"> </TableColumn>
      <TableColumn type="default" :prop="`language`" :label="`语言`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByAbbreviation" :filters="$filter0" @filter-change="handleStateRefresh()"> </TableColumn>
      <TableColumn type="default" :prop="`systemEdition`" :label="`版本信息`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByAbbreviation" :filters="$filter0" @filter-change="handleStateRefresh()"> </TableColumn>
      <TableColumn type="default" :prop="`synetcare`" :label="`是否同步NetCare`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByAbbreviation" :filters="$filter0" @filter-change="handleStateRefresh()"> </TableColumn> -->

      <TableColumn type="enum" :prop="`tenantType`" :label="$t('customers.customers type')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:filtered-value="state.search.tenantTypes" :filters="customerTypeOption.map((v) => ({ value: v.value, text: v.label }))" @filter-change="handleStateRefresh()">
        <template #default="{ row }">
          <div>
            {{ row.tenantType ? (customerTypeOption.find((v) => v.value === row.tenantType) || {}).label : "" }}
          </div>
        </template>
      </TableColumn>
      <TableColumn type="enum" :prop="`language`" :label="$t('customers.language')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:filtered-value="state.search.languages" :filters="localesOption.map((v) => ({ value: v.value, text: v.label }))" @filter-change="handleStateRefresh()">
        <template #default="{ row }">
          <div>
            {{ row.language ? (localesOption.find((v) => v.value === row.language) || {}).label : "" }}
          </div>
        </template>
      </TableColumn>
      <TableColumn type="condition" :prop="`zoneId`" :label="$t('customers.time zone')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByZoneId" :filters="$filter0" @filter-change="handleStateRefresh()">
        <template #default="{ row }">
          <div>{{ row.zoneId ? (timeZoneOption.find((v) => v.zoneId === row.zoneId) || {}).displayName : "" }}</div>
        </template>
      </TableColumn>

      <TableColumn type="enum" :prop="`systemEdition`" :label="$t('customers.version information')" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:filtered-value="state.search.systemEditions" :filters="systemEditionOption" @filter-change="handleStateRefresh()">
        <template #default="{ row }">
          <div>
            {{ row.systemEdition ? (systemEditionOption.find((v) => v.value === row.systemEdition) || {}).text : "" }}
          </div>
        </template>
      </TableColumn>

      <TableColumn type="enum" :prop="`synetcare`" :label="$t('customers.Is synchronize NetCare')" :min-width="120" :showOverflowTooltip="true" show-filter v-model:filtered-value="state.search.synetcare" :filters="['true', 'false'].map((v) => ({ value: v, text: v === 'true' ? '是' : '否', type: v ? 'success' : 'info' }))" @filter-change="handleStateRefresh()">
        <template #default="{ row }">
          <el-tag :type="row.synetcare ? 'success' : 'info'">{{ row.synetcare ? "是" : "否" }}</el-tag>
        </template>
      </TableColumn>

      <TableColumn type="enum" :prop="`activated`" :label="$t('customers.Is active')" :min-width="120" :showOverflowTooltip="true" show-filter v-model:filtered-value="state.search.activated" :filters="['true', 'false'].map((v) => ({ value: v, text: v === 'true' ? '激活' : '未激活', type: v ? 'success' : 'info' }))" @filter-change="handleStateRefresh()">
        <template #default="{ row }">
          <el-tag :type="row.activated ? 'success' : 'info'">{{ row.activated ? "激活" : "未激活" }}</el-tag>
        </template>
      </TableColumn>

      <TableColumn type="condition" :prop="`id`" :label="`ID`" :showOverflowTooltip="true" width="180"></TableColumn>

      <!-- <TableColumn type="enum" :prop="`address`" :label="`是否激活`" :min-width="120" :showOverflowTooltip="true" filter-multiple show-filter v-model:custom-filtered-value="searchByAddress" :filters="$filter0" @filter-change="handleStateRefresh()"> </TableColumn> -->

      <!-- <el-table-column v-for="column in state.column" :key="column.key" :prop="column.key" :label="column.label" :min-width="column.width || 120" :showOverflowTooltip="column.showOverflowTooltip" :formatter="column.formatter" /> -->

      <!-- <el-table-column label="双因素认证" :width="110" prop="mfaState">
        <template #default="{ row }">
          <el-tooltip :content="$t('glob.noPower')" :disabled="userInfo.hasPermission(PERMISSION.tenant.editor)">
            <div>
              <el-select :disabled="!userInfo.hasPermission(PERMISSION.tenant.editor)" v-if="row.mfaState != 'DEFAULT'" v-model="row.mfaState" @change="mfaChange(row, 'normal')">
                <el-option value="ENABLED" label="开">开</el-option>
                <el-option value="DISABLED" label="关">关</el-option>
              </el-select>
              <el-select :disabled="!userInfo.hasPermission(PERMISSION.tenant.editor)" v-else v-model="row.enableMfaDefault" @change="mfaChange(row, 'default')">
                <el-option :value="true" label="开">开</el-option>
                <el-option :value="false" label="关">关</el-option>
              </el-select>
            </div>
          </el-tooltip>
        </template>
      </el-table-column> -->
      <el-table-column :label="t('glob.operate')" align="left" header-align="left" :width="150" fixed="right">
        <template #header="{ column }">
          <span class="tw-mr-[2.5px]">{{ column.label }}</span>
          <el-link class="tw-ml-[2.5px] tw-align-middle" type="primary" :underline="false" :icon="Refresh" :title="t('glob.refresh')" @click.prevent="handleStateRefresh()" style="font-size: 14px"></el-link>
        </template>
        <template #default="{ row }: { row: DataItem }">
          <span v-if="userInfo.hasPermission(PERMISSION.tenant.editor)" class="el-button is-link">
            <el-button link type="primary" size="small" @click="editorItem(row as Record<string, any>).finally(() => handleStateRefresh())" style="font-size: 14px">
              {{ $t("glob.edit") }}
            </el-button>
          </span>
          <span>
            <!-- 客户管理('604221614397063168') -->
            <el-link :type="userInfo.hasPermission('612170309713264640') ? 'primary' : 'info'" :underline="false" class="tw-mx-[2.5px] tw-align-middle" :icon="Security" :disabled="userInfo.hasPermission('612170309713264640') ? state.loading : true" style="font-size: 22px" @click="showSecurityTree({ containerId: row.containerId })"></el-link>
          </span>
          <!-- <el-button link type="danger" size="small" :icon="Delete"></el-button> -->
          <!-- <el-dropdown trigger="click" @command="$event.callback(row)" class="el-button el-button--default is-link" style="vertical-align: middle; margin: 0">
            <span class="el-button is-link">
              <el-button link type="primary" size="small" style="font-size: 14px">
                {{ $t("glob.More") }}
              </el-button>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="userInfo.hasPermission('578402712236851200')" :command="{ callback: () => cutItemActivate(row as Record<string, any>).finally(() => handleStateRefresh()) }" class="tw-text-[var(--el-color-danger)]">
                  <el-link type="primary" style="font-size: 14px" :underline="false">{{ row.activated ? "取消激活" : "激活" }}客户</el-link>
                </el-dropdown-item>
                <el-dropdown-item v-if="userInfo.hasPermission('513160696909791232')" :command="{ callback: () => cutItemByState(row as Record<string, any>).finally(() => handleStateRefresh()) }" class="tw-text-[var(--el-color-danger)]">
                  <el-link type="primary" style="font-size: 14px" :underline="false">{{ row.blocked ? "解锁" : "锁定" }}客户</el-link>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown> -->
        </template>
      </el-table-column>
    </el-table>
    <div :style="{ margin: '2px 20px 20px' }">
      <el-pagination v-show="state.total" v-model:current-page="state.page" v-model:page-size="state.size" :page-sizes="state.sizes" :total="state.total" layout="->, total, sizes, prev, pager, next, jumper" @size-change="handleStateRefresh()" @current-change="handleStateRefresh()" />
    </div>
  </el-card>
  <!-- <EditorForm ref="editorRef" title="客户" /> -->
  <Editor ref="editorRef" title="客户">
    <template #cutItemByState="{ params }">
      <div class="tw-flex tw-flex-row tw-items-center tw-font-semibold tw-text-[var(--el-color-warning)]">
        <!-- {{ isActive }} -->
        <el-icon class="tw-mr-2 tw-text-[20px]"><InfoFilled></InfoFilled></el-icon>
        <p class="title" v-show="!isActive">
          确定
          {{ params.blocked ? "解锁" : "锁定" }}
          <span :style="{ color: 'var(--el-color-danger)' }">{{ params.name }}</span>
          客户吗？
        </p>
        <p class="title" v-show="isActive">
          确定
          {{ params.activated ? "取消激活" : "激活" }}
          <span :style="{ color: 'var(--el-color-danger)' }">{{ params.name }}</span>
          客户吗？
        </p>
      </div>
    </template>
  </Editor>
  <EditorForPersonnel ref="editorForPersonnelRef" title="客户拥有人"></EditorForPersonnel>
  <EditorUserHave ref="editorUserHaveRef" title="变更客户拥有人"></EditorUserHave>
  <EditorForTenant ref="editorForTenantRef" title="客户"><!-- 受托客户 --></EditorForTenant>
  <EditorForPermission ref="editorForPermissionRef" title="客户权限" display="drawer"></EditorForPermission>
  <EditorGroup ref="EditorGroupRef" title="管理用户组" @confrim="getSlaList"></EditorGroup>
</template>

<script setup lang="ts" generic="T extends object">
/* ========================================================= USE IMPORT START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 核心 Start ↓↓ ‖----------------------------------------------------------- */
import { ref, reactive, readonly, computed, watch, inject, provide, nextTick, onBeforeMount, onMounted, onBeforeUpdate, onUpdated, onActivated, onDeactivated, onBeforeUnmount, onUnmounted, getCurrentInstance, onRenderTracked, onRenderTriggered, onErrorCaptured, h, toValue } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
/* --------------------------------------------‖ ↑↑  核心 End  ↑↑ ‖--------‖ ↓↓ 组件 Start ↓↓ ‖-------------------------------------------- */
import { Search, Plus, Edit, Delete, Connection, More, Refresh, Lock, Unlock, InfoFilled, EditPen, Setting } from "@element-plus/icons-vue";
/* --------------------------------------------‖ ↑↑  组件 End  ↑↑ ‖--------‖ ↓↓ 工具 Start ↓↓ ‖-------------------------------------------- */
import { ElMessage, ElMessageBox, ElTag } from "element-plus";

import type { TableColumnCtx } from "element-plus";
/* --------------------------------------------‖ ↑↑  工具 End  ↑↑ ‖--------‖ ↓↓ 其他 Start ↓↓ ‖-------------------------------------------- */
import moment from "moment";
import { useSiteConfig } from "@/stores/siteConfig";
import getUserInfo from "@/utils/getUserInfo";
/* --------------------------------------------‖ ↑↑  其他 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */
import { getTenantList as getData, addTenantData as addData, modTenantData as modData, setTenantData as setData, type TenantItem as DataItem, setUserHave, activeTenant, customerTypeOption } from "@/api/personnel";
import { cutTenantState, setTenantDataByTrusteeship, setTenantByPermission, cutTenantActivate } from "@/api/personnel";
import { updateTenantNotes } from "@/views/pages/apis/tenant";

import { localesOption } from "@/api/locale";

import { getSystemVersion } from "@/api/system";

import { sizes } from "@/utils/common";

import treeAuth from "@/components/treeAuth/index.vue";
import { Zone } from "@/utils/zone";

import { EditorType } from "@/views/common/interface";

import TableColumn from "@/components/tableColumn/TableColumn.vue";

// import EditorForm from "./EditForm.vue";
import Editor from "./Editor.vue";
import EditorForTenant from "./EditorForTenant.vue";
import EditorForPersonnel from "./EditorForPersonnel.vue";
import EditorForPermission from "./EditorForPermission.vue";
import EditorUserHave from "./EditorUserHave";
import EditorGroup from "./groupManage.vue";
import governstrategy from "./governstrategy.vue";
import { routerV6Service } from "@/views/pages/common/routeV6";
import Security from "@/assets/dp.vue";
import showSecurityTree from "@/components/security-container";
import { exoprtMatch1, exoprtMatch2 } from "@/components/tableColumn/common";

import timeZoneOption from "@/components/formItem/timezone/zone.json";
const ctx = getCurrentInstance();
if (!ctx) {
  ElMessageBox.alert("Component context initialization failed!", "Error", { type: "error" });
  throw new Error("Component context initialization failed!");
}
// const i18n = useI18n({ useScope: "local" });
const { t } = useI18n();
const expandList = ref<string[]>([]);

const route = useRoute();
const router = useRouter();
const userInfo = getUserInfo();
defineOptions({ name: "TenantGovern" });

const siteConfig = useSiteConfig();
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 注入 Start ↓↓ ‖-------------------------------------------- */

const $filter0 = ref(exoprtMatch1);
const $filter1 = ref(exoprtMatch2);

const width = inject("width", ref(0));
const height = inject("height", ref(0));

interface StateData<T> {
  loading: boolean;
  select: T[];
  current: T | null;
  filter: { [key: string]: unknown };
  expand: string[];
  search: { [key: string]: any };
  column: { key: keyof T; label?: string; align?: "left" | "center" | "right"; width?: number; formatter?: (row: T, col: TableColumnCtx<DataItem>, value: T[keyof T]) => string | import("vue").VNode; showOverflowTooltip?: boolean }[];
  data: T[];
  page: number;
  size: number;
  sizes: typeof sizes;
  total: number;
}
const state = reactive<StateData<DataItem>>({
  loading: false,
  select: [],
  current: null,
  filter: {},
  expand: [],
  search: {
    /** 等于的客户名称 **/
    eqTenantName: [],
    /** 包含的客户名称 **/
    includeTenantName: [],
    /** 客户名称过滤关系(AND,OR) **/
    tenantNameFilterRelation: "AND",
    /** 不等于的客户名称 **/
    neTenantName: [],
    /** 不包含的客户名称 **/
    excludeTenantName: [],
    /** 等于的客户缩写 **/
    eqTenantAbbreviation: [],
    /** 包含的客户缩写 **/
    includeTenantAbbreviation: [],
    /** 客户缩写过滤关系(AND,OR) **/
    tenantAbbreviationFilterRelation: "AND",
    /** 不等于的客户缩写 **/
    neTenantAbbreviation: [],
    /** 不包含的客户缩写 **/
    excludeTenantAbbreviation: [],
    /** 等于的时区缩写 **/
    eqTenantZoneId: [],
    /** 包含的时区缩写 **/
    includeTenantZoneId: [],
    /** 时区缩写过滤关系(AND,OR) **/
    tenantZoneIdFilterRelation: "AND",
    /** 不等于的时区缩写 **/
    neTenantZoneId: [],
    /** 不包含的时区缩写 **/
    excludeTenantZoneId: [],
    /** 等于的地址 **/
    eqAddress: [],
    /** 包含的地址 **/
    includeAddress: [],
    /** 地址过滤关系(AND,OR) **/
    addressFilterRelation: "AND",
    /** 不等于的地址 **/
    neAddress: [],
    /** 不包含的地址 **/
    excludeAddress: [],
    /** 双因素认证 **/
    // mfaState: "",

    tenantTypes: "",
    languages: "",
    systemEditions: "",
    synetcare: "",

    // blocked: [],
    // activated: [],
  },
  column: [
    // { key: "name", label: "客户名称", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? String(v) : "--") },
    // { key: "abbreviation", label: "客户缩写", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? String(v) : "--") },
    // { key: "baileeTenantId", label: "是否托管", showOverflowTooltip: true, formatter: (_row, _col, v) => h(ElTag, { type: v ? "success" : "" }, () => (v ? t("glob.Yes") : t("glob.Not"))) },
    // { key: "baileeTenantName", label: "受托客户", showOverflowTooltip: true, formatter: (_row, _col, v) => (_row.baileeTenantId ? `${v || "--"}（${_row.baileeTenantAbbreviation || "--"}）` : "") },
    // { key: "zoneId", label: "时区", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? Zone[v as keyof typeof Zone] : "--") },
    // { key: "address", label: "地址", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? String(v) : "--") },
    { key: "blocked", label: "状态", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? h(ElTag, { type: "danger" }, () => "锁定") : h(ElTag, { type: "success" }, () => "正常")) },
    // { key: "mfaState", label: "双因素认证", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? h(ElTag, { type: "danger" }, () => "锁定") : h(ElTag, { type: "success" }, () => "正常")) },
    { key: "activated", label: "是否激活", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? h(ElTag, { type: "success" }, () => "激活") : h(ElTag, { type: "info" }, () => "未激活")) },
    // { key: "id", label: "客户ID", showOverflowTooltip: true, formatter: (_row, _col, v) => (v ? String(v) : "--") },
  ],
  data: [],
  page: 1,
  size: 50,
  sizes,
  total: 0,
});
// const tenantConfig = reactive({
//   timeZone: "设备默认",
//   tenantHours: [
//     {
//       weekDay: 1,
//       hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
//     },
//     {
//       weekDay: 2,
//       hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
//     },
//     {
//       weekDay: 3,
//       hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
//     },
//     {
//       weekDay: 4,
//       hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
//     },
//     {
//       weekDay: 5,
//       hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
//     },
//     {
//       weekDay: 6,
//       hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
//     },
//     {
//       weekDay: 7,
//       hours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
//     },
//   ],
// });
const searchType0ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByName = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByName = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByName) === "include") value0 = state.search.includeTenantName[0] || "";
    if (toValue(searchType0ByName) === "exclude") value0 = state.search.excludeTenantName[0] || "";
    if (toValue(searchType0ByName) === "eq") value0 = state.search.eqTenantName[0] || "";
    if (toValue(searchType0ByName) === "ne") value0 = state.search.neTenantName[0] || "";
    let value1 = "";
    if (toValue(searchType1ByName) === "include") value1 = state.search.includeTenantName[state.search.includeTenantName.length - 1] || "";
    if (toValue(searchType1ByName) === "exclude") value1 = state.search.excludeTenantName[state.search.excludeTenantName.length - 1] || "";
    if (toValue(searchType1ByName) === "eq") value1 = state.search.eqTenantName[state.search.eqTenantName.length - 1] || "";
    if (toValue(searchType1ByName) === "ne") value1 = state.search.neTenantName[state.search.neTenantName.length - 1] || "";
    return {
      type0: toValue(searchType0ByName),
      type1: toValue(searchType1ByName),
      relation: state.search.tenantNameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByName.value = v.type0 as typeof searchType0ByName extends import("vue").Ref<infer T> ? T : string;
    searchType1ByName.value = v.type1 as typeof searchType1ByName extends import("vue").Ref<infer T> ? T : string;
    state.search.tenantNameFilterRelation = v.relation;
    state.search.includeTenantName = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeTenantName = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqTenantName = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neTenantName = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const searchType0ByAbbreviation = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByAbbreviation = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByAbbreviation = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByAbbreviation) === "include") value0 = state.search.includeTenantAbbreviation[0] || "";
    if (toValue(searchType0ByAbbreviation) === "exclude") value0 = state.search.excludeTenantAbbreviation[0] || "";
    if (toValue(searchType0ByAbbreviation) === "eq") value0 = state.search.eqTenantAbbreviation[0] || "";
    if (toValue(searchType0ByAbbreviation) === "ne") value0 = state.search.neTenantAbbreviation[0] || "";
    let value1 = "";
    if (toValue(searchType1ByAbbreviation) === "include") value1 = state.search.includeTenantAbbreviation[state.search.includeTenantAbbreviation.length - 1] || "";
    if (toValue(searchType1ByAbbreviation) === "exclude") value1 = state.search.excludeTenantAbbreviation[state.search.excludeTenantAbbreviation.length - 1] || "";
    if (toValue(searchType1ByAbbreviation) === "eq") value1 = state.search.eqTenantAbbreviation[state.search.eqTenantAbbreviation.length - 1] || "";
    if (toValue(searchType1ByAbbreviation) === "ne") value1 = state.search.neTenantAbbreviation[state.search.neTenantAbbreviation.length - 1] || "";
    return {
      type0: toValue(searchType0ByAbbreviation),
      type1: toValue(searchType1ByAbbreviation),
      relation: state.search.tenantNameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByAbbreviation.value = v.type0 as typeof searchType0ByAbbreviation extends import("vue").Ref<infer T> ? T : string;
    searchType1ByAbbreviation.value = v.type1 as typeof searchType1ByAbbreviation extends import("vue").Ref<infer T> ? T : string;
    state.search.tenantNameFilterRelation = v.relation;
    state.search.includeTenantAbbreviation = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeTenantAbbreviation = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqTenantAbbreviation = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neTenantAbbreviation = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const search0ByZoneId = ref<"include" | "exclude" | "eq" | "ne">("include");
const search1ByZoneId = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByZoneId = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(search0ByZoneId) === "include") value0 = state.search.includeTenantZoneId[0] || "";
    if (toValue(search0ByZoneId) === "exclude") value0 = state.search.excludeTenantZoneId[0] || "";
    if (toValue(search0ByZoneId) === "eq") value0 = state.search.eqTenantZoneId[0] || "";
    if (toValue(search0ByZoneId) === "ne") value0 = state.search.neTenantZoneId[0] || "";
    let value1 = "";
    if (toValue(search1ByZoneId) === "include") value1 = state.search.includeTenantZoneId[state.search.includeTenantZoneId.length - 1] || "";
    if (toValue(search1ByZoneId) === "exclude") value1 = state.search.excludeTenantZoneId[state.search.excludeTenantZoneId.length - 1] || "";
    if (toValue(search1ByZoneId) === "eq") value1 = state.search.eqTenantZoneId[state.search.eqTenantZoneId.length - 1] || "";
    if (toValue(search1ByZoneId) === "ne") value1 = state.search.neTenantZoneId[state.search.neTenantZoneId.length - 1] || "";
    return {
      type0: toValue(search0ByZoneId),
      type1: toValue(search1ByZoneId),
      relation: state.search.tenantZoneIdFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    search0ByZoneId.value = v.type0 as typeof search0ByZoneId extends import("vue").Ref<infer T> ? T : string;
    search0ByZoneId.value = v.type1 as typeof search0ByZoneId extends import("vue").Ref<infer T> ? T : string;
    state.search.tenantZoneIdFilterRelation = v.relation;
    state.search.includeTenantZoneId = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeTenantZoneId = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqTenantZoneId = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neTenantZoneId = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

//

const searchType0ByAddress = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchType1ByAddress = ref<"include" | "exclude" | "eq" | "ne">("include");
const searchByAddress = computed<Record<`${"type" | "value" | "input"}${"0" | "1"}`, string> & { relation: "AND" | "OR" }>({
  get: () => {
    let value0 = "";
    if (toValue(searchType0ByAddress) === "include") value0 = state.search.includeAddress[0] || "";
    if (toValue(searchType0ByAddress) === "exclude") value0 = state.search.excludeAddress[0] || "";
    if (toValue(searchType0ByAddress) === "eq") value0 = state.search.eqAddress[0] || "";
    if (toValue(searchType0ByAddress) === "ne") value0 = state.search.neAddress[0] || "";
    let value1 = "";
    if (toValue(searchType1ByAddress) === "include") value1 = state.search.includeAddress[state.search.includeAddress.length - 1] || "";
    if (toValue(searchType1ByAddress) === "exclude") value1 = state.search.excludeAddress[state.search.excludeAddress.length - 1] || "";
    if (toValue(searchType1ByAddress) === "eq") value1 = state.search.eqAddress[state.search.eqAddress.length - 1] || "";
    if (toValue(searchType1ByAddress) === "ne") value1 = state.search.neAddress[state.search.neAddress.length - 1] || "";
    return {
      type0: toValue(searchType0ByAddress),
      type1: toValue(searchType1ByAddress),
      relation: state.search.tenantNameFilterRelation,
      value0,
      value1,
      input0: "",
      // input0: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
      input1: "",
      // input1: eventStateOption.reduce((p, c) => (p.append(c.value, c.label), p), new URLSearchParams()).toString(),
    };
  },
  set: (v) => {
    searchType0ByAddress.value = v.type0 as typeof searchType0ByAddress extends import("vue").Ref<infer T> ? T : string;
    searchType1ByAddress.value = v.type1 as typeof searchType1ByAddress extends import("vue").Ref<infer T> ? T : string;
    state.search.tenantNameFilterRelation = v.relation;
    state.search.includeAddress = [...(v.type0 === "include" ? [v.value0] : []), ...(v.type1 === "include" ? [v.value1] : [])];
    state.search.excludeAddress = [...(v.type0 === "exclude" ? [v.value0] : []), ...(v.type1 === "exclude" ? [v.value1] : [])];
    state.search.eqAddress = [...(v.type0 === "eq" ? [v.value0] : []), ...(v.type1 === "eq" ? [v.value1] : [])];
    state.search.neAddress = [...(v.type0 === "ne" ? [v.value0] : []), ...(v.type1 === "ne" ? [v.value1] : [])];
  },
});

const publicParams = computed<Record<string, unknown>>(() => ({}));
const editorRef = ref<InstanceType<typeof Editor>>();
const editorForPersonnelRef = ref<InstanceType<typeof EditorForPersonnel>>();
const editorForTenantRef = ref<InstanceType<typeof EditorForTenant>>();
const editorForPermissionRef = ref<InstanceType<typeof EditorForPermission>>();
const editorUserHaveRef = ref<InstanceType<typeof EditorUserHave>>();
const EditorGroupRef = ref<InstanceType<typeof EditorGroup>>();
const isActive = ref(false);
const dialogVisibleshow = ref(false);
const treeStyle = ref({
  pointerEvents: "none",
});
const containerId = ref("");
async function createItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  if (!editorForPersonnelRef.value) return row;
  try {
    const $raw: Partial<(ReturnType<typeof addData> extends Promise<infer U> ? U : never)["data"]> = {};
    await editorRef.value.open(row, async (form: Record<string, unknown>) => {
      if (form.ownerUserId) delete form.owner;
      else if ((<Record<string, unknown>>form.owner || {}).name || (<Record<string, unknown>>form.owner || {}).account || (<Record<string, unknown>>form.owner || {}).phone || (<Record<string, unknown>>form.owner || {}).email) {
        delete form.ownerUserId;
        form.owner = { name: (<Record<string, unknown>>form.owner || {}).name, account: (<Record<string, unknown>>form.owner || {}).account, phone: (<Record<string, unknown>>form.owner || {}).phone, email: (<Record<string, unknown>>form.owner || {}).email };
      } else {
        delete form.owner;
        delete form.ownerUserId;
      }
      const { success, message, data } = await addData({ ...publicParams.value, ...form });
      if (!success) throw Object.assign(new Error(message), { success, data });
      if (form.active) {
        await activeTenant({ id: data.id, abbreviation: data.abbreviation }).then((res) => {
          if (success) {
            ElMessage.success(t("axios.Operation successful"));
          }
        });
      } else {
        ElMessage.success(t("axios.Operation successful"));
      }

      Object.assign($raw, data);
      // const action = await new Promise<"confirm" | "close" | "cancel">((resolve) => {
      //   ElMessageBox.confirm("是否为客户同时创建拥有人？", `${t("glob.add")}客户`, { type: "info", distinguishCancelAndClose: true, confirmButtonText: "是", cancelButtonText: "否" })
      //     .then(resolve)
      //     .catch(resolve);
      // });
      // switch (action) {
      //   case "confirm": {
      //     if (!editorForPersonnelRef.value) throw new Error("致命错误,请刷新后操作");
      //     try {
      //       await editorForPersonnelRef.value.open({ accountSuffix: form.abbreviation }, async (personnelForm: Record<string, unknown>) => {
      //         const { success, message, data } = await addData({ ...publicParams.value, ...form, owner: personnelForm });
      //         if (!success) throw Object.assign(new Error(message), { success, data });
      //         ElMessage.success(t("axios.Operation successful"));
      //       });
      //     } catch (error) {
      //       throw void 0;
      //     }
      //     break;
      //   }
      //   case "cancel": {
      //     const { success, message, data } = await addData({ ...publicParams.value, ...form });
      //     if (!success) throw Object.assign(new Error(message), { success, data });
      //     ElMessage.success(t("axios.Operation successful"));
      //     break;
      //   }
      //   default: {
      //     /* "close" */ throw action;
      //   }
      // }
    });
    /* TODO: 采集机放在这就行 */
    routerV6Service($raw.id);
  } catch (error) {
    /*  */
  }
}
async function mfaChange(row: any, type: string) {
  // console.log(row, type);
  if (type === "default") {
    // // console.log(form, 777777);
    const { success, message, data } = await modData({ id: <string>row.id, mfaState: row.enableMfaDefault ? "ENABLED" : "DISABLED" });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(t("axios.Operation successful"));
    handleStateRefresh();
  } else {
    const { success, message, data } = await modData({ id: <string>row.id, mfaState: row.mfaState });
    if (!success) throw Object.assign(new Error(message), { success, data });
    ElMessage.success(t("axios.Operation successful"));
    handleStateRefresh();
  }
}
async function changeItem(row: Record<string, unknown>) {
  // // console.log(row, 5555555);
  if (!editorUserHaveRef.value) return row;

  try {
    await editorUserHaveRef.value.open(row, async (form: Record<string, unknown>) => {
      if (form.ownerUserId) delete form.owner;
      else if ((<Record<string, unknown>>form.owner || {}).name || (<Record<string, unknown>>form.owner || {}).account || (<Record<string, unknown>>form.owner || {}).phone || (<Record<string, unknown>>form.owner || {}).email) {
        delete form.ownerUserId;
        form.owner = { name: (<Record<string, unknown>>form.owner || {}).name, account: (<Record<string, unknown>>form.owner || {}).account, phone: (<Record<string, unknown>>form.owner || {}).phone, email: (<Record<string, unknown>>form.owner || {}).email };
      } else {
        delete form.owner;
        delete form.ownerUserId;
      }
      // // console.log(form);
      const { success, message, data } = await setUserHave({ id: row.baileeTenantId, userId: form.ownerUserId });
      if (success) {
        handleStateRefresh();
      }
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(t("axios.Operation successful"));
    });
  } catch (error) {
    /*  */
  }
}

async function manageGroup(row: Record<string, unknown>) {
  if (!EditorGroupRef.value) return row;
  try {
    await EditorGroupRef.value.open(row, async (form: Record<string, unknown>) => {
      // // console.log(form, 777777);
      const { success, message, data } = await setData({ ...publicParams.value, id: <string>row.id, ...form });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(t("axios.Operation successful"));
    });
  } catch (error) {
    /*  */
  }
}

async function editorItem(row: Record<string, unknown>) {
  if (!editorRef.value) return row;
  try {
    await editorRef.value.open({ ...row, channel: row.tenantChannel, fax: row.tenantFax, postcode: row.tenantPostcode, email: row.tenantEmail, signAddress2: row.tenantSigningPlace2, signAddress: row.tenantSigningPlace, industry: row.tenantIndustry, nature: row.tenantNature, isOperator: row.tenantOperator, isForeign: row.tenantForeign, description: row.tenantDescription, blocked: row.blocked, activated: row.activated, sendMsg: row.sendMsg, sendEmail: row.sendEmail }, async (form: Record<string, unknown>) => {
      // // console.log(form, 777777);
      const { success, message, data } = await setData({ ...publicParams.value, id: <string>row.id, ...form });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(t("axios.Operation successful"));
    });
  } catch (error) {
    /*  */
  }
}

async function cutItemByState(row: Record<string, unknown>) {
  isActive.value = false;
  if (!editorRef.value) return row;
  try {
    await editorRef.value.confirm({ ...row, $title: `${row.blocked ? "解锁" : "锁定"}客户`, $slot: "cutItemByState" }, async (form: Record<string, unknown>) => {
      const { success, message, data } = await cutTenantState({ id: <string>row.id, abbreviation: <string>row.abbreviation, state: form.blocked ? "unblock" : "block" });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(t("axios.Operation successful"));
    });
  } catch (error) {
    /*  */
  }
}

async function cutItemActivate(row: Record<string, unknown>) {
  isActive.value = true;

  if (!editorRef.value) return row;
  try {
    await editorRef.value.confirm({ ...row, $title: `${row.activated ? "取消激活" : "激活"}客户`, $slot: "cutItemByState" }, async (form: Record<string, unknown>) => {
      const { success, message, data } = await cutTenantActivate({ id: <string>row.id, abbreviation: <string>row.abbreviation, state: form.activated ? "unblock" : "block" });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(t("axios.Operation successful"));
    });
  } catch (error) {
    /*  */
  }
}

async function cutItemByTrusteeship(row: Record<string, unknown>) {
  if (!editorForTenantRef.value) return row;
  try {
    await editorForTenantRef.value.open(row, async (form: Record<string, unknown>) => {
      const { success, message, data } = await setTenantDataByTrusteeship({ id: <string>row.id, baileeTenantId: <string>form.baileeTenantId });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(t("axios.Operation successful"));
    });
  } catch (error) {
    /*  */
  }
}
async function cutItemByPermission(row: Record<string, unknown>) {
  if (!editorForPermissionRef.value) return row;
  try {
    await editorForPermissionRef.value.open({ tenantId: row.id, appId: (siteConfig.baseInfo || {}).app }, async (form: Record<string, unknown>) => {
      const { success, message, data } = await setTenantByPermission({ tenantId: row.id, appId: (siteConfig.baseInfo || {}).app || "", appAssigned: true, permissionIds: <string[]>form.permissionIds });
      if (!success) throw Object.assign(new Error(message), { success, data });
      ElMessage.success(t("axios.Operation successful"));
    });
  } catch (error) {
    /*  */
  }
}

async function querysItem(params: Record<string, unknown>, onCleanup?: (cleanupFn: () => void) => void) {
  let controller = new AbortController();
  if (typeof onCleanup === "function") onCleanup(() => controller.abort());
  try {
    console.log(state.search);
    const params = { keyword: state.search.keyword };
    const { success, message, data, page: page = 1, size: size = 50, total: total = 0 } = await getData({ ...state.search, ...params, paging: { pageNumber: state.page, pageSize: state.size } });
    if (success) {
      state.page = Number(page);
      state.size = Number(size);
      state.total = Number(total);
      return data instanceof Array ? data : [];
    } else throw Object.assign(new Error(message), { success, data });
  } catch (error) {
    if (error instanceof Error) ElMessage.error(error.message);
    state.page = Number(1);
    state.size = Number(20);
    state.total = Number(0);
    return [];
  } finally {
    controller = new AbortController();
  }
}

const systemEditionOption = ref<Record<"value" | "text", string>[]>([]);

async function handleGetSystemVersion() {
  const { data, message, success } = await getSystemVersion({});
  if (!success) throw new Error(message);
  systemEditionOption.value = data.map((v) => ({ value: v.code, text: v.name }));
}

async function handleStateRefresh() {
  if (state.loading) return;
  state.loading = true;
  state.data.splice(0, state.data.length, ...(await querysItem({})));
  state.loading = false;
}
function update(val) {
  let obj = { ...val };
  updateTenantNotes({ ...obj })
    .then((res) => {
      if (res.success) {
        ElMessage.success("操作成功");
        handleStateRefresh();
      } else {
        // ElMessage.error(JSON.parse(res.data)?.message);
      }
    })
    .catch((e) => {
      if (e instanceof Error) ElMessage.error(e.message);
    });
}

function expandChange(row, expandedRows) {
  if (expandedRows.length) {
    //展开
    expandList.value = []; //先干掉之前展开的行
    if (row) {
      expandList.value.push(row.id); //push新的行 (原理有点类似防抖)
    }
  } else {
    expandList.value = []; //折叠 就清空expand-row-keys对应的数组
  }
}
/* ---------------------------------------------------------‖ ↑↑  注入 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE IMPORT END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE ACHIEVE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 类型 Start ↓↓ ‖----------------------------------------------------------- */
// interface State {
//   name: string;
// }
/* --------------------------------------------‖ ↑↑  类型 End  ↑↑ ‖--------‖ ↓↓ 数据 Start ↓↓ ‖-------------------------------------------- */
// const final = readonly<T>({} as T);
// const loading = ref<boolean>(false);
// const state = reactive<State>({
//   name: "",
// });
// const name = computed(() => state.name);
/* --------------------------------------------‖ ↑↑  数据 End  ↑↑ ‖--------‖ ↓↓ 钩子 Start ↓↓ ‖-------------------------------------------- */
function beforeCreate() {}
function created() {}
function beforeMount() {}
async function mounted() {
  await handleGetSystemVersion();
  handleStateRefresh();
}
function beforeUpdate() {}
function updated() {}
function activated() {}
function deactivated() {}
function beforeDestroy() {}
function destroyed() {}
/* --------------------------------------------‖ ↑↑  钩子 End  ↑↑ ‖--------‖ ↓↓ 调用 Start ↓↓ ‖-------------------------------------------- */

/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 方法 Start ↓↓ ‖-------------------------------------------- */

/* ---------------------------------------------------------‖ ↑↑  方法 End  ↑↑ ‖----------------------------------------------------------- */
/* =========================================================  USE ACHIEVE END  ========================================================= */

/* ==================================================================================================================================== */

/* ========================================================= USE EXPOSE START ========================================================= */
/* ---------------------------------------------------------‖ ↓↓ 调用 Start ↓↓ ‖----------------------------------------------------------- */
beforeCreate();
nextTick(created);
onMounted(mounted, ctx);
onUpdated(updated, ctx);
onUnmounted(destroyed, ctx);
onBeforeMount(beforeMount, ctx);
onBeforeUpdate(beforeUpdate, ctx);
onBeforeUnmount(beforeDestroy, ctx);
// onErrorCaptured(/* 钩子，在捕获了后代组件传递的错误时调用 */ // console.log.bind(null, "🚀[onErrorCaptured]"), ctx);
// onRenderTracked(/* 调试钩子，当组件渲染过程中追踪到响应式依赖时调用 */ // console.log.bind(null, "🚀[onRenderTracked]"), ctx);
// onRenderTriggered(/* 调试钩子，当响应式依赖的变更触发了组件渲染时调用 */ // console.log.bind(null, "🚀[onRenderTriggered]"), ctx);
onActivated(activated, ctx);
onDeactivated(deactivated, ctx);
/* --------------------------------------------‖ ↑↑  调用 End  ↑↑ ‖--------‖ ↓↓ 插槽 Start ↓↓ ‖-------------------------------------------- */
interface Slots {
  default(props: T): any;
}
defineSlots<Slots>();
/* --------------------------------------------‖ ↑↑  插槽 End  ↑↑ ‖--------‖ ↓↓ 暴露 Start ↓↓ ‖-------------------------------------------- */
defineExpose({});
/* ---------------------------------------------------------‖ ↑↑  暴露 End  ↑↑ ‖----------------------------------------------------------- */
/* ========================================================= USE EXPOSE START ========================================================= */
</script>

<style scoped lang="scss"></style>
